{"name": "monarch", "version": "1.0.0", "description": "Monarch", "license": "ISC", "author": "QV", "engines": {"node": "12.22.12", "npm": "6.14.16"}, "scripts": {"build": "npx webpack", "watch": "npx webpack -w", "analyze": "npx webpack --profile --json > stats.json && npx webpack-bundle-analyzer stats.json ./public/javascripts", "build:prod": "npx webpack -p --display-error-details", "test": "echo \"Error: no test specified\" && exit 1", "lint": "npx eslint --ext .js,.vue vue/"}, "dependencies": {"@babel/core": "^7.4.3", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/preset-env": "^7.4.3", "@quotable-value/validation": "0.0.0-ae1de92", "@vue/babel-preset-app": "^3.5.5", "axios": "^0.18.0", "babel-loader": "^8.0.5", "babel-plugin-add-module-exports": "^1.0.0", "babel-polyfill": "^6.23.0", "blueimp-load-image": "^2.17.1", "bootstrap": "^3.3.5", "bootstrap-multiselect": "0.9.13-1", "css-loader": "^2.1.1", "daterangepicker": "^2.1.25", "deep-equal": "^1.0.1", "file-loader": "^3.0.1", "file-saver": "^1.3.3", "highcharts": "^11.1.0", "html-webpack-plugin": "^3.2.0", "jquery": "^3.2.1", "json2csv": "^3.7.3", "knockout": "^3.5.1", "lodash": "^4.17.4", "lodash.orderby": "^4.6.0", "luxon": "^3.4.0", "moment": "^2.17.1", "moment-locales-webpack-plugin": "^1.0.7", "moment-timezone": "^0.5.14", "nouislider": "^10.1.0", "numeral": "^2.0.6", "qv-maps": "git+ssh://**************/Quotable-Value/qv-maps.git#1.2.1", "sass": "^1.57.1", "style-loader": "^0.23.1", "underscore": "^1.8.3", "underscore.string": "^3.3.4", "url-loader": "^1.1.2", "vue": "^2.7.0", "vue-clipboard2": "^0.3.1", "vue-currency-filter": "^3.4.1", "vue-file-selector": "^0.6.0", "vue-hot-reload-api": "^2.1.0", "vue-js-toggle-button": "^1.3.3", "vue-multiselect": "^2.1.7", "vue-resource": "^1.3.1", "vue-router": "^3.6.5", "vue-select": "2.2.0", "vue-style-loader": "^4.1.2", "vue2-datepicker": "^3.10.2", "vuejs-paginate": "^2.1.0", "vuex": "^3.6.2", "webpack-merge": "^4.2.1"}, "devDependencies": {"babel-eslint": "^10.0.2", "eslint": "^5.16.0", "eslint-config-airbnb-base": "^13.2.0", "eslint-config-standard": "^12.0.0", "eslint-friendly-formatter": "^4.0.1", "eslint-import-resolver-webpack": "^0.11.1", "eslint-loader": "^2.1.2", "eslint-plugin-html": "^5.0.3", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.1.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^9.0.0", "sass-loader": "^7.1.0", "vue-eslint-parser": "^6.0.4", "vue-loader": "^15.10.0", "vue-template-compiler": "^2.7.0", "webpack": "^4.29.6", "webpack-bundle-analyzer": "^3.3.2", "webpack-cli": "^3.3.0", "webpack-dev-middleware": "^3.6.2", "webpack-hot-middleware": "^2.24.3"}}
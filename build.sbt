import com.typesafe.sbt.packager.docker.{Cmd, ExecCmd}

organization in ThisBuild := "com.qv"

// the Scala version that will be used for cross-compiled libraries
scalaVersion in ThisBuild := "2.12.6"

// Configure Nexus Repositories (for publishing)
publishTo in ThisBuild := {
  val nexus = "https://sonarqube.qvmonarch.co.nz/nexus/repository/"
  if (isSnapshot.value)
    Some("snapshots" at nexus + "maven-snapshots")
  else
    Some("releases" at nexus + "maven-releases")
}

credentials ++= Seq(
  Credentials(
    "quotable-value/monarch-repository",
    "quotable-value-948396734470.d.codeartifact.ap-southeast-2.amazonaws.com",
    "aws",
    sys.env.getOrElse("CODEARTIFACT_TOKEN", "")
  ),
  Credentials(
    "Sonatype Nexus Repository Manager",
    "sonarqube.qvmonarch.co.nz",
    "cibuild",
    "admin123"
  )
)

// Configure Dependency Resolvers
resolvers in ThisBuild ++= Seq(
  "Local Maven Repository" at "file://" + Path.userHome.absolutePath + "/.m2/repository",
  "AWS CodeArtifact" at "https://quotable-value-948396734470.d.codeartifact.ap-southeast-2.amazonaws.com/maven/monarch-repository/",
  "nexus-qv-maven-snapshot" at "https://sonarqube.qvmonarch.co.nz/nexus/repository/maven-snapshots/",
  "nexus-qv-maven-releases" at "https://sonarqube.qvmonarch.co.nz/nexus/repository/maven-releases/",
  "lightbend-commercial-maven-releases" at "https://sonarqube.qvmonarch.co.nz/nexus/repository/lightbend-commercial-maven-releases/"
)

// Disable Services for standalone deployment
lagomKafkaEnabled in ThisBuild := false
lagomCassandraEnabled in ThisBuild := false

// Default to Dev Services for local development
val testUrl = "http://internal-Monarch-test-PrivateALB-178025222.ap-southeast-2.elb.amazonaws.com"
val devUrl = "http://internal-monarch-dev-privatealb-1666931830.ap-southeast-2.elb.amazonaws.com"
val lsa = "http://internal-Monarch-lsa4-PrivateALB-1287331361.ap-southeast-2.elb.amazonaws.com"
val servicesUrl = sys.env.getOrElse("SERVICES_URL", devUrl)
lagomUnmanagedServices in ThisBuild := Map(
  "property" -> servicesUrl,
  "sale" -> servicesUrl,
  "sale-analysis" -> servicesUrl,
  "media" -> servicesUrl,
  "home-valuation" -> servicesUrl,
  "reporting" -> servicesUrl,
  "classification" -> servicesUrl,
  "roll-maintenance" -> servicesUrl
)

//Dependencies
val monarchCommon = "com.qv" % "monarch-common" % "1.0.0-16-gf196657-SNAPSHOT"
val propertyApi = "com.qv" %% "property-api" % "1.8.0-67-g5f542c7-SNAPSHOT"
val saleApi = "com.qv" %% "sale-api" % "1.6.0-30-g88a341e-SNAPSHOT"
val statsApi = "com.qv" % "stats-service" % "0.1.5"
val saleAnalysisApi = "com.qv" %% "sale-analysis-api" % "1.7.0"
val mediaApi = "com.qv" %% "media-api" % "1.5.34"
val taApi = "com.qv" % "ta-service" % "0.1.5"
val classificationApi = "com.qv" %% "classification-api" % "1.7.0"
val homeValuationApi = "com.qv" %% "home-valuation-api" % "1.7.1-27-g05e34a5-SNAPSHOT"
val userApi = "com.qvapi" % "user" % "0.0.1-37-ge0a504c-SNAPSHOT"
val reportingApi = "com.qv" %% "reporting-api" % "1.13.3-38-gd1d18a0-SNAPSHOT"
val auditApi = "com.qv" %% "audit-api" % "1.5.5"
val qivsStreamApi = "com.qv" %% "qivs-stream-api" % "1.5.12"
val rollMaintenanceApi = "com.qv" %% "roll-maintenance-api" % "1.11.0-178-gda091b8-SNAPSHOT" excludeAll(ExclusionRule(organization = "com.qv", name = "monarch-common"))

val postgresqlDriver = "org.postgresql" % "postgresql" % "42.2.4"
val amazonaws = "com.amazonaws" % "aws-java-sdk" % "1.11.386"
val serviceLocator = "com.lightbend.rp" %% "reactive-lib-service-discovery-lagom14-java" % "0.9.0"
val deadbolt = "be.objectify" %% "deadbolt-java" % "2.6.4"
val JavaMail = "com.sun.mail" % "javax.mail" % "1.6.1"
val pdfbox = "org.apache.pdfbox" % "pdfbox" % "2.0.27"
val jsonLogEncoder = "net.logstash.logback" % "logstash-logback-encoder" % "6.6"

val versionRegex = "([0-9]+.[0-9]+.[0-9]+\\.?[0-9]?)-?(.*)?".r

//Projects
lazy val `monarch-web-impl` = (project in file("."))
  .enablePlugins(PlayJava, LagomPlay, GitVersioning, BuildInfoPlugin, Cinnamon)
  .settings(
    buildInfoKeys := Seq[BuildInfoKey](name, version, scalaVersion, sbtVersion),
    buildInfoPackage := "util"
  )
  .settings(git.useGitDescribe := true)
  .settings(
    git.gitTagToVersionNumber := {
      case versionRegex(v, "") => Some(v)
      case versionRegex(v, "SNAPSHOT") => Some(s"$v-SNAPSHOT")
      case versionRegex(v, s) => Some(s"$v-$s-SNAPSHOT")
      case _ => None
    }
  )
  .settings(
    libraryDependencies ++= Seq(
      ehcache,
      guice,
      javaWs,
      lagomJavadslClient,
      lagomJavadslApi,
      Cinnamon.library.cinnamonCHMetrics,
      Cinnamon.library.cinnamonCHMetricsHttpReporter,
      Cinnamon.library.cinnamonJvmMetricsProducer,
      Cinnamon.library.cinnamonLagom,
      serviceLocator,
      reportingApi,
      deadbolt,
      statsApi,
      taApi,
      saleApi,
      saleAnalysisApi,
      propertyApi,
      mediaApi,
      classificationApi,
      homeValuationApi,
      userApi,
      postgresqlDriver,
      amazonaws,
      filters,
      JavaMail,
      rollMaintenanceApi,
      pdfbox,
      jsonLogEncoder,
      monarchCommon
    )
  )
  // Define extra dependencies and resources if building a Docker image for Kubernetes
  .settings(
    sys.props.get("buildTarget") match {
      case Some("kubernetes") =>
        Seq(
          unmanagedResourceDirectories in Compile += baseDirectory.value / "conf" / "kubernetes-resources"
        )
      case _ => Seq.empty
    }
  )
  // Define Docker build configuration
  .settings(
    dockerBaseImage := "openjdk:8-jre-alpine",
    dockerRepository := Some("373100629421.dkr.ecr.ap-southeast-2.amazonaws.com"),
    dockerUsername := Some("monarch"),
    dockerUpdateLatest := true,
    dockerCommands :=
      dockerCommands.value.flatMap {
        case c@Cmd("FROM", _) => Seq(c, ExecCmd("RUN", "/bin/sh", "-c", "apk add --no-cache bash && ln -sf /bin/bash /bin/sh"))
        case v => Seq(v)
      }
  )
  // Suppress eviction warnings - can still execute 'sbt evicted' to see evictions.
  .settings(
    evictionWarningOptions in update := EvictionWarningOptions.default.withWarnTransitiveEvictions(false),
    dependencyOverrides += "com.qv" % "monarch-common" % "1.0.0-16-gf196657-SNAPSHOT"
  )
  .settings(
    unmanagedResourceDirectories in Test += baseDirectory(_ / "target/web/public/test").value,
    lagomWatchDirectories in Test := Seq(baseDirectory(_ / "app").value),
    // Play provides two styles of routers, one expects its actions to be injected, the
    // other, legacy style, accesses its actions statically.
    routesGenerator := InjectedRoutesGenerator,
    fork in run := false
  )

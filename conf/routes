# Home page
GET     /                                                                       controllers.Application.index()
GET     /healthCheck                                                            controllers.Application.healthCheck()
GET     /version                                                                controllers.Application.version(disableLogging: java.util.Optional[java.lang.Boolean])
GET     /versions                                                               controllers.Application.versions()
GET     /fetchTerritorialAuthorities                                            controllers.Application.fetchTerritorialAuthorities()

GET     /fetchUserData                                                          controllers.Application.fetchUserData()
GET     /callback                                                               controllers.AuthController.callback(code: java.util.Optional[String], state: java.util.Optional[String])
GET     /logOut                                                                 controllers.AuthController.logOut()

GET     /displaySalesGroups/:taCodes                                            controllers.ReferenceData.displaySalesGroups(taCodes : String)
GET     /displaySalesAnalysis/:id                                               controllers.SalesAnalysis.displaySalesAnalysis(id: String)

POST    /displaySalesSearchResult                                               controllers.SalesSearch.displaySalesSearchResult()
POST    /getSalesByQupid                                                        controllers.SalesSearch.getSalesByQupid
POST    /updateSalesAnalysis                                                    controllers.SalesAnalysis.updateSalesAnalysis()
DELETE  /deleteSalesAnalysis/:id                                                controllers.SalesAnalysis.deleteSalesAnalysis(id: String)

POST    /displayPropertySearchResult                                            controllers.PropertyController.displayPropertySearchResult()
POST    /searchProperties                                                       controllers.PropertyController.searchProperties()
POST    /displayTypeAheadResult                                                 controllers.PropertyController.displayTypeAheadResult()

POST    /exportProperties                                                       controllers.ExportProperties.exportProperties()
POST    /getExportPropertiesCriteria                                            controllers.ExportProperties.getExportPropertiesCriteria()

GET     /getStatsSummary/:qupid                                                 controllers.PropertyMasterData.getStatsSummary(qupid: Integer)
GET     /getTASummary/:taNumber                                                 controllers.PropertyMasterData.getTASummary(taNumber: Integer)
GET     /getProperty/:id                                                        controllers.PropertyMasterData.getProperty(id: String)
POST    /getPropertyDescription                                                 controllers.PropertyMasterData.getPropertyDescription()
GET     /hasQvProperty/:ownerId                                                 controllers.PropertyMasterData.hasQvProperty(ownerId: String)
GET     /getQvProperty/:ownerId                                                 controllers.PropertyMasterData.getQvProperty(ownerId: String)
GET     /getQvPropertyZoneInfo/:qpid                                            controllers.PropertyMasterData.getQvPropertyZoneInfo(qpid: Integer)
POST    /saveQvProperty                                                         controllers.PropertyMasterData.saveQvProperty()
POST    /saveQvPropertyZoneInfo                                                 controllers.PropertyMasterData.saveQvPropertyZoneInfo()
GET     /getLinzTitles/:qupid                                                   controllers.PropertyMasterData.getLinzTitles(qupid: Integer)
GET     /getMaoriLandLumpSum/:qupid                                             controllers.PropertyMasterData.getMaoriLandLumpSum(qupid: Integer)
GET     /getCurrentRollLandValueIndex/:id                                       controllers.PropertyMasterData.getCurrentRollLandValueIndex(id: java.util.UUID)
GET     /getPropertyInfo/:qupid                                                 controllers.PropertyMasterData.getPropertyInfo(qupid: Integer)
GET     /getPropertyInfoFull/:qupid                                             controllers.PropertyMasterData.getPropertyInfoFull(qupid: Integer)
POST    /updateOwners                                                           controllers.PropertyMasterData.updateOwners()
POST    /updatePropertyInfoField                                                controllers.PropertyMasterData.updatePropertyInfoField()
GET     /getPickListValues                                                      controllers.PropertyMasterData.getPickListValues()
GET     /getApportionmentDetails                                                controllers.PropertyMasterData.getApportionmentDetails(qpid: Integer)
GET     /getQpidDetails                                                         controllers.PropertyMasterData.getDetailsForQpids(qpids: String)
GET     /getDvrDerivedValues                                                    controllers.PropertyMasterData.getDvrDataFromQivs(qpid: Integer)
GET     /getAttachment/:attachmentId                                            controllers.PropertyMasterData.getAttachment(attachmentId: Integer)
POST    /saveHazardNotes                                                        controllers.PropertyMasterData.saveHazardNotes()

## Map static resources from the /public folder to the /assets URL path
GET     /assets/*file                                                           controllers.Assets.versioned(path="/public", file: Asset)
GET     /property/assets/*file                                                  controllers.Assets.versioned(path="/public", file: Asset)
GET     /property/property/assets/*file                                         controllers.Assets.versioned(path="/public", file: Asset)

#Javascript Routes
GET     /javascriptRoutes                                                       controllers.JSRoutesController.javascriptRoutes()


GET     /loadTADashboard/:userTACode                                            controllers.TADashboardController.loadTADashboard(userTACode: String)
GET     /displayGraphs/:userTACode                                              controllers.TADashboardController.displayGraphs(userTACode: String)


#Media Routes
GET     /getMedia/:mediaId                                                      controllers.MediaController.getMedia(mediaId: String)
GET     /getMediaByOwner/:ownerId                                               controllers.MediaController.getMediaByOwner(ownerId: String)
GET     /getPrimaryMediaByOwner/:ownerId/:category                              controllers.MediaController.getPrimaryMediaByOwner(ownerId: String, category: String)
GET     /getMediaByQupID/:qupIds                                                controllers.MediaController.getMediaByQupID(qupIds: String)
POST    /saveMedia                                                              controllers.MediaController.saveMedia()
POST    /updateMedia                                                            controllers.MediaController.updateMedia()
POST    /generateMediaEntryID                                                   controllers.MediaController.generateMediaEntryID()
POST    /linkMedia                                                              controllers.MediaController.linkMedia()
GET     /unLinkMedia/:mediaId                                                   controllers.MediaController.unLinkMedia(mediaId: String)

GET     /reLinkPhoto/:mediaId/:primary/:qupid                                   controllers.MediaController.reLinkPhoto(mediaId: String, primary: Boolean, qupid: Integer)

#QIVS Integration
GET     /property/properties/:id/photos                                         controllers.MediaController.getPropertyWithPhotos(id: String)
GET     /property/properties/relink/validation                                  controllers.MediaController.verifyRelink(qpidsIn: String , qpidsOut: String )

# Disable CSRF protection for URLs hit by QIVS, as it is not supplying CSRF tokens
+ nocsrf
POST    /property/properties/identifier_lodgement                               controllers.PropertyController.getPropertyUUID()
+ nocsrf
POST    /property/properties/:ownerId/photos/upload_initiation                  controllers.MediaController.uploadInitiation(ownerId: String)
+ nocsrf
POST    /property/properties/:ownerId/photos/:mediaId/upload_acknowledgement    controllers.MediaController.uploadAcknowledge(ownerId: String, mediaId: String)


#Subdivision Relinking
GET     /property/property/Relink                                               controllers.Application.index()
GET     /getSubdivisionData                                                     controllers.Subdivision.getSubdivisionData(qupidsIn: String, qupidsOut: String)

#Property QIVS Integration
GET     /property/login                                                         controllers.Application.index()
GET     /property/property                                                      controllers.Application.index()
GET     /property/property/Search                                               controllers.Application.index()


#Home Valuation
POST    /saveHomeValuation                                                      controllers.HomeValuation.saveHomeValuation()
POST    /displayMarketValuationJobSearchResult                                  controllers.HomeValuation.displayMarketValuationJobSearchResult()
GET     /getHomeValuation/:id                                                   controllers.HomeValuation.getHomeValuation(id: String)
GET     /getHomeValuationByProperty/:id                                         controllers.HomeValuation.getHomeValuationByProperty(id: String)
GET     /getHomeValuationPropertyDescription/:id                                controllers.HomeValuation.getHomeValuationPropertyDescription(id: String)
POST    /generateValuationConclusion                                            controllers.HomeValuation.generateValuationConclusion
POST    /displayComparableProperties                                            controllers.ComparableSales.displayComparableProperties
GET     /exportComparableSale/:id                                               controllers.ComparableSales.exportComparableSale(id: String)
POST    /getComparableSaleBySaleId                                              controllers.ComparableSales.getComparableSaleBySaleId
GET     /getValuationJobUpdateInformation/:id                                   controllers.HomeValuation.getValuationJobUpdateInformation(id: java.util.UUID)
GET     /saveValuationJobUpdateInformation/:id                                  controllers.HomeValuation.saveValuationJobUpdateInformation(id: java.util.UUID, userName: String, qupid: Integer)
GET     /fields/:reportTypeCode                                                 controllers.HomeValuation.getFields(reportTypeCode : String)

# Get picklist values from API-PICKLIST Lambda
# TODO: Tech-debt incorrect api route pattern, migrate objection routes to /api/objections
GET     /api-picklist/:keys                                                     controllers.ApiPicklistController.getPicklistValue(keys: String)
POST    /api-picklist/objection/search/:queryString                             controllers.ApiPicklistController.searchObjection(queryString: String)
POST    /api-picklist/objection/export/:queryString                             controllers.ApiPicklistController.exportObjection(queryString: String)
POST    /api-picklist/objection/assign                                          controllers.ApiPicklistController.bulkAssignObjection
POST    /api-picklist/objection/review/add                                      controllers.ApiPicklistController.addObjectionJobReview
GET     /api-picklist/objection/review/:ratingValuationId                       controllers.ApiPicklistController.getObjectionJobReview(ratingValuationId: String)
POST    /api-picklist/objection/review/update                                   controllers.ApiPicklistController.updateObjectionJobReview(ratingValuationId: String)
POST    /api-picklist/objection/:objectionId/rating-valuation                   controllers.ApiPicklistController.addRatingValuation(objectionId: Integer)
POST    /api-picklist/objection-job/:ratingValuationId/complete                 controllers.ApiPicklistController.completeObjectionJobValuation(ratingValuationId: String)
POST    /api-picklist/comparable-sale/search                                    controllers.ApiPicklistController.searchComparableSales
POST    /api-picklist/objection-job/validate                                    controllers.ApiPicklistController.validateObjectionJob(ratingValuationId: String, isValidatingAtReviewStage: Boolean)
GET     /api-picklist/objection-contact/:ratingValuationId                      controllers.ApiPicklistController.getObjectionContact(ratingValuationId: String)
POST    /api-picklist/objection-contact/:ratingValuationId/update               controllers.ApiPicklistController.updateObjectionContact(ratingValuationId: String)
GET     /api-picklist/comparable-sale/:ratingValuationId                        controllers.ApiPicklistController.getComparableSales(ratingValuationId: String)
POST    /api-picklist/comparable-sale/:ratingValuationId/add/:shouldIncludeAllSales                    controllers.ApiPicklistController.addComparableSales(ratingValuationId: String,shouldIncludeAllSales: Boolean)
POST    /api-picklist/comparable-sale/:ratingValuationId/update                 controllers.ApiPicklistController.updateComparableSales(ratingValuationId: String)
POST    /api-picklist/comparable-sale/:ratingValuationId/remove                 controllers.ApiPicklistController.removeComparableSales(ratingValuationId: String)
POST    /api-picklist/comparable-sale/:ratingValuationId/refresh                controllers.ApiPicklistController.refreshComparableSale(ratingValuationId: String)
POST    /api-picklist/objection-job/auto-select-comps                           controllers.ApiPicklistController.autoSelectComparableSales


GET     /get-sales-groups-and-rolls/:keys                                       controllers.ApiPicklistController.getSalesGroupsAndRolls(keys: String)

# Generate Home Valuation Report
GET     /generateValuationReport/:id                                            controllers.ValuationReport.generateValuationReport(id: String)
GET     /getPeerReviewPDF                                                       controllers.ValuationReport.getPeerReviewPDF()

#Classification
GET     /displayClassification/:category/:isActive                              controllers.ReferenceData.displayClassification(category: String, isActive: Boolean)
POST    /displayClassifications                                                 controllers.ReferenceData.displayClassifications()
POST    /searchClassifications                                                  controllers.ReferenceData.searchClassifications()

#Api-user
GET    /displayValuers                                                          controllers.ReferenceData.displayValuers()
GET    /displayRatingValuers                                                    controllers.ReferenceData.displayRatingValuers()
GET    /displayCountersigners                                                   controllers.ReferenceData.displayCountersigners()
GET    /displayUsers                                                            controllers.ReferenceData.displayUsers()
GET    /displayUserByUsername/:username                                         controllers.ReferenceData.displayUserByUsername(username: String)
GET    /displayOffices                                                          controllers.ReferenceData.displayOffices()
GET    /getRoles                                                                controllers.ReferenceData.getRoles()
POST   /saveUser                                                                controllers.ReferenceData.saveUser()

POST    /displayClassificationTypeAheadResult                                   controllers.ClassificationController.displayClassificationTypeAheadResult()
POST    /saveClassification                                                     controllers.ClassificationController.saveClassification()
POST    /fetchClassifications                                                   controllers.ClassificationController.fetchClassifications()
GET     /getClassifications                                                     controllers.ClassificationController.getClassifications(category: String)
GET     /sendEmailForPeerReview/:jobid                                          controllers.EmailController.sendEmailForPeerReview(jobid: String)
GET     /sendEmailOnReviewApproved/:jobid                                       controllers.EmailController.sendEmailOnReviewApproved(jobid: String)
GET     /sendEmailOnReviewRejected/:jobid                                       controllers.EmailController.sendEmailOnReviewRejected(jobid: String)
GET     /getAllCategories                                                       controllers.ClassificationController.getAllCategories()
GET     /getSpecialCategories                                                   controllers.ClassificationController.getSpecialCategories()
# Roll Maintenance routes
GET     /web/rollMaintenance/getActivity/:id                                    controllers.RollMaintenanceController.getRollMaintenanceActivity(id: String)
POST    /web/rollMaintenance/getActivities                                      controllers.RollMaintenanceController.getRollMaintenanceActivitiesByIds()
POST    /web/rollMaintenance/saveActivity                                       controllers.RollMaintenanceController.saveRollMaintenanceActivity()
GET     /web/rollMaintenance/:id/requireMoreInformation                         controllers.RollMaintenanceController.requireMoreInformation(id: String, notes: String, requestPlans: Boolean)
GET     /web/rollMaintenance/:id/informationProvided                            controllers.RollMaintenanceController.informationProvided(id: String)
GET     /web/rollMaintenance/:id/requireInspection                              controllers.RollMaintenanceController.requireInspection(id: String, notes: String)
GET     /web/rollMaintenance/:id/inspected                                      controllers.RollMaintenanceController.inspected(id: String)
GET     /web/rollMaintenance/:id/constructionComplete                           controllers.RollMaintenanceController.constructionComplete(id: String)
GET     /web/rollMaintenance/:id/constructionInProgress                         controllers.RollMaintenanceController.constructionInProgress(id: String)
GET     /web/rollMaintenance/:id/complianceCertificateIssued                    controllers.RollMaintenanceController.complianceCertificateIssued(id: String)
GET     /web/rollMaintenance/:id/noComplianceCertificate                        controllers.RollMaintenanceController.noComplianceCertificate(id: String)
GET     /web/rollMaintenance/:id/plansRequired                                  controllers.RollMaintenanceController.togglePlansRequired(id: String)
GET     /web/rollMaintenance/:id/plansDrawn                                     controllers.RollMaintenanceController.togglePlansDrawn(id: String)
GET     /web/rollMaintenance/:id/planStatus                                     controllers.RollMaintenanceController.togglePlanStatus(id: String, planStatus: String)
POST    /web/rollMaintenance/searchActivities                                   controllers.RollMaintenanceController.searchRollMaintenanceActivities()
POST    /web/rollMaintenance/generateInspectionReport                           controllers.RollMaintenanceController.generateInspectionReport()

# Rating Valuation routes
POST    /web/ratingValuation/generateRatingValuation                            controllers.RatingValuationController.generateRatingValuation()
GET     /web/ratingValuation/getRatingValuation/:id                             controllers.RatingValuationController.getRatingValuation(id: java.util.UUID)
GET     /web/ratingValuation/getInProgressValuationForActivity/:id              controllers.RatingValuationController.getInProgressValuationForActivity(id: String, generate: Boolean)
GET     /web/ratingValuation/:id/deleteRatingValuation                          controllers.RatingValuationController.deleteRatingValuation(id: String)
GET     /web/ratingValuation/:id/:id/deleteInactiveRatingValuation              controllers.RatingValuationController.deleteInactiveRatingValuation(ratingVal: String, id: String)
GET     /web/ratingValuation/getCompleteValuationForActivity/:id                controllers.RatingValuationController.getCompleteValuationForActivity(id: String)
POST    /web/ratingValuation/saveRatingValuation                                controllers.RatingValuationController.saveRatingValuation()
GET     /web/ratingValuation/getDefaultComparablePropertySearchCriteria/:id     controllers.RatingValuationController.getDefaultComparablePropertySearchCriteria(id: java.util.UUID)
GET     /web/ratingValuation/:id/link                                           controllers.RatingValuationController.linkRollMaintenanceActivity(id: java.util.UUID, rollMaintenanceActivityId: String)
GET     /web/ratingValuation/:id/unlink                                         controllers.RatingValuationController.unlinkRollMaintenanceActivity(id: java.util.UUID, rollMaintenanceActivityId: String)
GET     /web/ratingValuation/:id/completeSetup                                  controllers.RatingValuationController.completeSetup(id: java.util.UUID)
POST    /web/ratingValuation/:id/autoValue                                      controllers.RatingValuationController.autoValue(id: java.util.UUID)
POST    /web/ratingValuation/:id/completeValuation                              controllers.RatingValuationController.completeValuation(id: java.util.UUID, ignoreWarnings: Boolean)
GET     /web/ratingValuation/:id/isValid                                        controllers.RatingValuationController.validateRatingValuationComplete(id: java.util.UUID)
GET     /web/ratingValuation/:id/requireMoreInformation                         controllers.RatingValuationController.requireMoreInformation(id: java.util.UUID, notes: String, requestPlans: Boolean)
GET     /web/ratingValuation/:id/informationProvided                            controllers.RatingValuationController.informationProvided(id: java.util.UUID)
GET     /web/ratingValuation/:id/requireInspection                              controllers.RatingValuationController.requireInspection(id: java.util.UUID, notes: String)
GET     /web/ratingValuation/:id/inspected                                      controllers.RatingValuationController.inspected(id: java.util.UUID)
GET     /web/ratingValuation/:id/activities                                     controllers.RatingValuationController.getRollMaintenanceActivities(id: java.util.UUID)
GET     /web/ratingValuation/getConsentJobUpdateInformation/:id                 controllers.RatingValuationController.getConsentJobUpdateInformation(id: java.util.UUID)
GET     /web/ratingValuation/saveConsentJobUpdateInformation/:id                controllers.RatingValuationController.saveConsentJobUpdateInformation(id: java.util.UUID, userName: String, qupid: Integer)
POST    /web/ratingValuation/validate                                           controllers.RatingValuationController.validate()
POST    /web/ratingValuation/validate-on-save                                   controllers.RatingValuationController.validateOnSave()

# Report Upload Controller Routes
POST    /web/reportUpload/createUploadedReportEntry                             controllers.ReportUploadController.createUploadedReportEntry()
POST    /web/reportUpload/completeReportUpload                                  controllers.ReportUploadController.completeReportUpload()

# RTV Controller Routes
GET    /web/rtv/getRuralIndexMainIndex/:taCode                                  controllers.RtvController.getRuralIndexMainIndex(taCode: Integer)
POST   /web/rtv/validateRuralIndexMainIndex/:taCode                             controllers.RtvController.validateRuralIndexMainIndex(taCode: Integer)
POST   /web/rtv/updateRuralIndexMainIndex/:taCode/:userName/:ignoreWarnings     controllers.RtvController.updateRuralIndexMainIndex(taCode: Integer, userName: String, ignoreWarnings: Boolean)
GET    /web/rtv/getRuralIndexSecondaryRefinements/:taCode                       controllers.RtvController.getRuralIndexSecondaryRefinements(taCode: Integer)
POST   /web/rtv/validateRuralIndexSecondaryRefinements/:taCode                  controllers.RtvController.validateRuralIndexSecondaryRefinements(taCode: Integer)
POST   /web/rtv/updateRuralIndexSecondaryRefinements/:taCode/:userName/:ignoreWarnings  controllers.RtvController.updateRuralIndexSecondaryRefinements(taCode: Integer, userName: String, ignoreWarnings: Boolean)
GET    /web/rtv/getRuralPropertyRtvValues/:qpid                                 controllers.RtvController.getRuralPropertyRtvValues(qpid: Integer)
GET    /web/rtv/getRuralPropertyRtvIndices/:qpid                                 controllers.RtvController.getRuralPropertyRtvIndices(qpid: Integer)
GET    /web/rtv/getRuralIndexAssessments/:taCode                                controllers.RtvController.getRuralIndexAssessments(taCode: Integer)
GET    /web/rtv/getRuralIndexBaseCategories                                     controllers.RtvController.getRuralIndexBaseCategories()

# Rural Worksheet Controller Routes
GET    /web/ruralWorksheet/getCurrentWorksheet/:qpid                            controllers.RuralWorksheetController.getCurrentWorksheet(qpid: Integer)
GET    /web/ruralWorksheet/getRevisionWorksheet/:qpid                           controllers.RuralWorksheetController.getRevisionWorksheet(qpid: Integer)
GET    /web/ruralWorksheet/getRtvWorksheet/:qpid                                controllers.RuralWorksheetController.getRtvWorksheet(qpid: Integer)
GET    /web/ruralWorksheet/createRevisionWorksheet/:qpid                        controllers.RuralWorksheetController.createRevisionWorksheet(qpid: Integer)
POST   /web/ruralWorksheet/updateWorksheet/:userName                            controllers.RuralWorksheetController.updateWorksheet(userName: String)
GET    /web/ruralWorksheet/deleteWorksheet/:qpid                                controllers.RuralWorksheetController.deleteWorksheet(qpid: Integer)
POST   /web/ruralWorksheet/updateAssessment/:userName                           controllers.RuralWorksheetController.updateAssessment(userName: String)
GET    /web/ruralWorksheet/updateAssessment/:qpid/:worksheetLandValue/:apportionmentCode     controllers.RuralWorksheetController.recalculateLandMatrix(qpid: Integer, worksheetLandValue: Integer, apportionmentCode: String)
GET    /web/ruralWorksheet/landMatrix                                           controllers.RuralWorksheetController.getWorksheetLandMatrix(qpid: Integer, worksheetLandValue: Integer)
# Commercial Worksheet Routes
GET    /api/commercialWorksheet/getCommercialWorksheet                          controllers.RuralWorksheetController.getCommercialWorksheet(qpid: Integer)
GET    /api/commercialWorksheet/getCommercialRevisionWorksheet                  controllers.RuralWorksheetController.getCommercialRevisionWorksheet(qpid: Integer)
GET    /api/commercialWorksheet/createCommercialRevisionWorksheet               controllers.RuralWorksheetController.createCommercialRevisionWorksheet(qpid: Integer)
GET    /api/commercialWorksheet/deleteCommercialWorksheet                       controllers.RuralWorksheetController.deleteCommercialWorksheet(qpid: Integer)
GET    /api/commercialWorksheet/getRfcClassifications                           controllers.RuralWorksheetController.getRfcClassifications()
POST   /api/commercialWorksheet/updateCommercialWorksheet                       controllers.RuralWorksheetController.updateCommercialWorksheet()
POST   /api/commercialWorksheet/validateCommercialWorksheet                     controllers.RuralWorksheetController.validateCommercialWorksheet()
GET    /api/commercialWorksheet/generateCommercialWorksheetPdf                  controllers.RuralWorksheetController.generateCommercialWorksheetPdf(qpid: Integer, type: String)
GET    /web/ruralWorksheet/generateRuralWorksheetPDFReport                      controllers.RuralWorksheetController.generateRuralWorksheetPdf(qpid: Integer, type: String)

# Reason for Change Controller Routes
GET    /web/reasonForChange/getExistingReasonForChange/:qpid/:userName           controllers.ReasonForChangeController.getExistingReasonForChange(qpid: Integer, userName: String)
POST   /web/reasonForChange/addReasonForChange                                   controllers.ReasonForChangeController.addReasonForChange()

# Sales Processing Controller Routes
GET     /api/salesProcessing/getSale/:saleId                                    controllers.SalesProcessingController.getSale(saleId: Integer)
GET     /api/salesProcessing/getDVR/:id/:current/:saleIdToRefresh          controllers.SalesProcessingController.getDVR(id: Integer, current: Boolean, saleIdToRefresh: Integer)
POST    /api/salesProcessing/saveSale                                           controllers.SalesProcessingController.saveSale()
POST    /api/salesProcessing/updateDVR                                          controllers.SalesProcessingController.updateDVR()
GET     /api/salesProcessing/getSaleClassification                              controllers.SalesProcessingController.getSaleClassification()
GET     /api/salesProcessing/getTALandUseZone/:qpid                             controllers.SalesProcessingController.getTALandUseZone(qpid: String)
GET     /api/salesProcessing/getSalePortalSalePdfUrl/:saleId/:sourceId          controllers.SalesProcessingController.getSalePortalSalePdfUrl(saleId: Integer, sourceId: Integer)
GET     /api/salesProcessing/getTitles                                          controllers.SalesProcessingController.getTitles(saleId: String, qpids: String)
POST    /api/salesProcessing/relinkSaleRfs                                      controllers.SalesProcessingController.relinkSaleRfs()
POST    /api/salesProcessing/deleteSale                                         controllers.SalesProcessingController.deleteSale()
POST    /api/salesProcessing/validateSale                                       controllers.SalesProcessingController.validateSale()
POST    /api/salesProcessing/addSaleInspectionConsent                           controllers.SalesProcessingController.addSaleInspectionConsent()
GET     /api/salesProcessing/getChattelParameter/:qpid                          controllers.SalesProcessingController.getChattelParameter(qpid: Integer)
GET     /api/salesProcessing/getLinzSaleWarnings/:saleId                        controllers.SalesProcessingController.getLinzSaleWarnings(saleId: Integer)
POST    /api/salesProcessing/searchUnlinkedSales                                controllers.SalesProcessingController.searchUnlinkedNotices()
GET     /api/sales/notice/:sourceId/:noticeId                                   controllers.SalesProcessingController.viewSalesNotice(noticeId: Integer, sourceId: Integer)
POST    /api/sales/notice/link                                                  controllers.SalesProcessingController.linkNotice()

# Property Details routes
GET     /web/propertyDetail/getPendingPropertyDetail/:qpid                      controllers.PropertyDetailController.getPendingPropertyDetail(qpid: Integer)
GET     /web/propertyDetail/getCurrentPropertyDetail/:propertyId                controllers.PropertyDetailController.getCurrentPropertyDetail(propertyId: java.util.UUID)
GET     /web/propertyDetail/editCurrentPropertyDetail/:propertyId               controllers.PropertyDetailController.editCurrentPropertyDetail(propertyId: java.util.UUID)
GET     /web/propertyDetail/:id                                                 controllers.PropertyDetailController.getPropertyDetail(id: java.util.UUID)
GET     /web/propertyDetail/:id/validate                                        controllers.PropertyDetailController.validatePendingPropertyDetail(id: java.util.UUID)
GET     /web/propertyDetail/:id/validateComplete                                controllers.PropertyDetailController.validatePropertyDetailComplete(id: java.util.UUID)
POST    /web/propertyDetail/savePendingPropertyDetail                           controllers.PropertyDetailController.savePendingPropertyDetail()
POST    /web/propertyDetail/savePendingPropertyDetailForRVJob                   controllers.PropertyDetailController.savePendingPropertyDetailForRVJob()
POST    /web/propertyDetail/saveCurrentPropertyDetail                           controllers.PropertyDetailController.saveCurrentPropertyDetail(ignoreWarnings: Boolean)
GET     /web/propertyDetail/getPropertyDetailUpdateInformation/:qupid           controllers.PropertyDetailController.getPropertyDetailUpdateInformation(qupid: Integer)
POST    /web/propertyDetail/generateBuildingsFromPropertyDetail                 controllers.PropertyDetailController.generateBuildingsFromPropertyDetail(umrGarages: java.util.Optional[Integer], fsGarages: java.util.Optional[Integer])
POST    /web/propertyDetail/newDwelling/propertyInformation                     controllers.PropertyDetailController.generatePropertyDetailNewDwellingInformation()
GET     /web/propertyDetail/:id/generatePropertyDetailDescription               controllers.PropertyDetailController.generatePropertyDetailDescription(id: java.util.UUID)
POST    /web/propertyDetail/validate                                            controllers.PropertyDetailController.validateOnSave()
# Property Sra routes
GET     /web/propertySraDetail/getSraOutputCode                                 controllers.PropertySraDetailController.getSraOutputCode()
GET     /web/propertySraDetail/getSraReasonSource                               controllers.PropertySraDetailController.getSraReasonSource()
GET     /web/propertySraDetail/getSraAuthorityClasses                           controllers.PropertySraDetailController.getSraAuthorityClasses(qpid: Integer)
GET     /web/propertySraDetail/getCurrentSraValuation                           controllers.PropertySraDetailController.getCurrentSraValuation(qpid: Integer)
POST    /web/propertySraDetail/updateSraValuation                               controllers.PropertySraDetailController.updateSraValuation()
# Linz Search routes
GET     /web/linzSearch/getLinzLandDistrict                                     controllers.LinzSearchController.getLinzLandDistrict()
GET     /web/linzSearch/getLinzParcelType                                       controllers.LinzSearchController.getLinzParcelType()
GET     /web/linzSearch/getLinzPlanType                                         controllers.LinzSearchController.getLinzPlanType()
POST    /web/linzSearch/getLinzAutoSuggestLegalDescription                      controllers.LinzSearchController.getLinzAutoSuggestLegalDescription()
POST    /web/linzSearch/getLinzByCertificate                                    controllers.LinzSearchController.getLinzByCertificate()
POST    /web/linzSearch/getLinzByParcelID                                       controllers.LinzSearchController.getLinzByParcelID()
POST    /web/linzSearch/getLinzByLegalDescription                               controllers.LinzSearchController.getLinzByLegalDescription()
POST    /web/linzSearch/getLinzByLegalDescriptionAutoSuggest                    controllers.LinzSearchController.getLinzByLegalDescriptionAutoSuggest()
POST    /web/linzSearch/getLinzByOwner                                          controllers.LinzSearchController.getLinzByOwner()
POST    /web/linzSearch/getUserGroupId                                          controllers.LinzSearchController.getUserGroupId()
# Sales Search routes
GET     /web/salesSearch/getSalesProcessingStatus                               controllers.SalesSearchController.getSalesProcessingStatus()
GET     /web/salesSearch/getSalesProcessingSource                               controllers.SalesSearchController.getSalesProcessingSource()
POST    /web/salesSearch/getSearchSale                                          controllers.SalesSearchController.getSearchSale()

# QVMaps Routes
GET     /api/maps/propertyDetails/coords/get                                    controllers.QVMapsController.getPropertyDetailsForLatLng(lat: Double, lng: Double)
GET     /api/maps/layerInfo/get                                                 controllers.QVMapsController.getLayerDataForLatLng(lat: Double, lng: Double, column: String)
GET     /api/maps/propertyDetails/bndryPoints/get                               controllers.QVMapsController.getPropertyDetailsForBndryPoints(geomWkt: String)
GET     /api/maps/propertyDetails/qpid/get                                      controllers.QVMapsController.getPropertyDetailsForQpids(qpids: String, parcelIds: Boolean)
POST    /api/maps/siteplan/upload                                               controllers.QVMapsController.uploadSiteplanImage()
GET     /api/maps/saleInfo/get                                                  controllers.QVMapsController.getPropertySaleInfoFromLatLng(lat: Double, lng: Double)
GET     /api/maps/picklists/get                                                 controllers.QVMapsController.getPickListValues()
GET     /api/maps/rolls/get                                                     controllers.QVMapsController.getRollsListForTA(taCode: Integer)
GET     /api/maps/geoserver/:type/wms                                           controllers.QVMapsController.getWMSLayer(type)
+nocsrf
POST    /api/maps/geoserver/:type/wms                                           controllers.QVMapsController.postWMSLayer(type)

# FloorPlan Measure Routes
GET    /web/rollMaintenance/getFloorPlans                                       controllers.FloorPlanToolController.getFloorPlans(qpid: Integer)
GET    /web/rollMaintenance/getFloorPlanImage                                   controllers.FloorPlanToolController.getFloorPlanImage(resourceId: Integer)
POST   /web/rollMaintenance/saveFloorPlans/:username                            controllers.FloorPlanToolController.saveFloorPlans(username: String)

# Rural Sale Analysis Routes
GET     /api/sale-analysis/rural/exists/:saleId                                 controllers.RuralSaleAnalysisController.hasRuralSaleAnalysis(saleId: Integer)
GET     /api/sale-analysis/rural/get/:saleId                                    controllers.RuralSaleAnalysisController.getRuralSaleAnalysis(saleId: Integer)
GET     /api/sale-analysis/rural/create/:saleId                                 controllers.RuralSaleAnalysisController.createRuralSaleAnalysis(saleId: Integer)
POST    /api/sale-analysis/rural/update                                         controllers.RuralSaleAnalysisController.updateRuralSaleAnalysis(validateOnly: Boolean)
GET     /api/sale-analysis/rural/classifications                                controllers.RuralSaleAnalysisController.getRuralClassifications(taCode: Integer)
GET     /api/sale-analysis/rural/refresh/:saleId                                controllers.RuralSaleAnalysisController.refreshRuralSaleAnalysis(saleId: Integer)
GET     /api/sale-analysis/rural/delete/:saleId                                 controllers.RuralSaleAnalysisController.deleteRuralSaleAnalysis(saleId: Integer)
GET     /api/sale-analysis/rural/:saleId                                        controllers.RuralSaleAnalysisController.displayRuralSaleAnalysis(saleId: Integer)
GET     /api/sale-analysis/checkAnalysis/:saleId                                controllers.SalesAnalysis.checkAnalysis(saleId: Integer)
GET     /api/sale-analysis/rural/migrateResidential/:saleId                     controllers.SalesAnalysis.migrateResidentialToRuralAnalysis(saleId: Integer)
GET     /generateRuralSalesPDFReport                                            controllers.RuralSaleAnalysisController.generateRuralSalesPdf(saleId: Integer)

# Objection Routes
# if you change the 'from-qivs' routes you will need to update qivs web.config to point to new path
GET     /api/objection/action-from-qivs                                        controllers.ObjectionController.actionObjection()
GET     /api/objection/update-from-qivs                                        controllers.ObjectionController.actionObjectionFromQivs()
POST    /api/objection/reinstatement                                           controllers.ObjectionController.reinstateObjectionJob()
GET     /api/objection/reinstatement-from-qivs                                 controllers.ObjectionController.reinstateObjectionJobFromQivs()
GET     /api/objection/withdraw-from-qivs                                      controllers.ObjectionController.withdrawObjectionFromQivs()
POST    /api/objection/:objectionId/updateObjectionLink                        controllers.ObjectionController.updateObjectionLink(objectionId: Integer)
POST    /api/objection/job-status                                              controllers.ObjectionController.updateObjectionJobStatus
GET     /api/action-record/:qpid                                               controllers.ObjectionController.getActionRecordsForQpid(qpid: Integer)

# Consent Routes
POST    /api-consent/consent/assign                                            controllers.ConsentController.bulkAssignConsent()

# Report Routes
GET     /reports/objection-job/:objectionId/$file<.*>                          controllers.ObjectionController.generateObjectionJobPDF(objectionId: Integer, file)

# Work Unit Dashboard routes
GET     /reports/work-unit/valuer-summary/:username/:startDate/:endDate                   controllers.StatsController.getWorkUnitValuerSummary(username:String,startDate:String, endDate: String)
GET     /reports/work-unit/valuer-rating/:username/:startDate/:endDate                    controllers.StatsController.getWorkUnitValuerRating(username:String,startDate:String, endDate: String)
GET     /reports/work-unit/notifications/:username                                        controllers.StatsController.getWorkUnitNotifications(username:String)
GET     /reports/work-unit/consultancy-summary/:username/:startDate/:endDate              controllers.StatsController.getConsultancyValuerSummary(username:String,startDate:String, endDate: String)
GET     /reports/work-unit/consultancy-hours/:username/:startDate/:endDate                controllers.StatsController.getConsultancyValuerHrs(username:String,startDate:String, endDate: String)

# Report Generator Routes
GET     /report                                                                 controllers.ReportGeneratorController.getReports(includeHidden: Boolean = false)
GET     /report/:includeHidden                                                  controllers.ReportGeneratorController.getReports(includeHidden: Boolean)
POST    /report-job                                                             controllers.ReportGeneratorController.createReportJob(isExternalReportJob: Boolean)
POST    /report-job-status-update                                               controllers.ReportGeneratorController.updateReportJobStatus(reportJobId: String, status: String)
+nocsrf
POST    /upload-report-job                                                      controllers.ReportGeneratorController.createAndUploadExternalReportJob(secretKey: String)
+nocsrf
POST    /upload-report-job-auth                                                 controllers.ReportGeneratorController.createAndUploadExternalReportJobAuth()
GET     /report-job                                                             controllers.ReportGeneratorController.getReportJobsPagination(pageSize: Integer, pageNumber: Integer, orderBy: String, orderDirection: String)
GET     /report-job-file                                                        controllers.ReportGeneratorController.getReportJobFile(s3Url: String, fileName: String)

# Google Maps API Routes
GET    /web/google-map/location-view                                            controllers.GoogleMapsController.getLocationsPhotoForQpids(qpids: String)

# Default route - render home page to allow Vue Router to take control
GET     /$path<.*>                                                              controllers.Application.fallback(path)

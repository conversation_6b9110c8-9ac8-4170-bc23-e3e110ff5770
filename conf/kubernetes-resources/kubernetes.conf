// Enable DNS Service Discovery in Kubernetes
play.modules.enabled += "com.lightbend.rp.servicediscovery.lagom.javadsl.ServiceLocatorModule"

// Configure the use of Akka DNS to enable SRV resolution in Kubernetes
akka {
  io {
    dns {
      resolver = async-dns
      async-dns {
        provider-object = "com.lightbend.rp.asyncdns.AsyncDnsProvider"
        resolve-srv = true
        resolv-conf = on
      }
    }
  }
}

// Setup HTTP Bind address and port
http {
  address = "0.0.0.0"
  port = "9000"
}

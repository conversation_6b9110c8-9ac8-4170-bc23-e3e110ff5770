# This is the main configuration file for the application.
# ~~~~~

# Secret key
# ~~~~~
# The secret key is used to secure cryptographics functions.
# If you deploy your application to several instances be sure to use the same key!
application.mode=local
application.mode=${?ENV_NAME}
lambda_invoke.target_env=dev
lambda_invoke.target_env=${?ENV_NAME}
play {
  http {
    session.secure = false
    session.secure = ${?PLAY_SECURE_SESSION_COOKIE}
    session.maxAge = 12 hours
    secret.key = "changeme"
    secret.key = ${?APPLICATION_SECRET}
  }
  i18n {
    langs = [ "en" ]
  }
  modules {
    enabled += "be.objectify.deadbolt.java.DeadboltModule"
    enabled += "security.CustomDeadboltHook"
    enabled += "modules.MonarchWebModule"
  }
  editor="http://localhost:63342/service.api/file/?file=%s&line=%s"
}

# Lambda API Configuration
api {
    qv {
      host = "https://dev.qvapi.co.nz"
      key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
      host  = ${?QV_LAMBDA_API_URL}
      key   = ${?QV_LAMBDA_API_KEY}
    }

    load {
      host  = ${api.qv.host}
      key   = ${api.qv.key}
      #host  = "http://localhost:3001"
      #key   = "d41d8cd98f00b204e9800998ecf8427e"
      host  = ${?QV_LAMBDA_API_URL}
      key   = ${?QV_LAMBDA_API_KEY}
    }

    sale-analysis {
      host  = "https://api-sale-analysis.dev.qvapi.co.nz"
      key   = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
      #host  = "http://127.0.0.1:3000/local"
      #key   = "d41d8cd98f00b204e9800998ecf8427e"
      key   = ${?RURAL_SALES_LAMBDA_API_KEY}
      host  = ${?RURAL_SALES_LAMBDA_API_URL}
    }

    sales {
      host = "https://sales.dev.qvapi.co.nz"
      key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
      #host = "http://localhost:3000/local"
      #key = "d41d8cd98f00b204e9800998ecf8427e"
      host = ${?SALES_LAMBDA_API_URL}
      key = ${?SALES_LAMBDA_API_KEY}
    }

    property {
      host = "https://api-property.dev.qvapi.co.nz"
      key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
      #host  = "http://localhost:3000/local"
      #key   = "d41d8cd98f00b204e9800998ecf8427e"
      host = ${?PROPERTY_API_URL}
      key = ${?PROPERTY_API_KEY}
    }

    search {
      host = "https://api-search.dev.qvapi.co.nz"
      key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
      #host = "http://localhost:3000/local"
      #key = "d41d8cd98f00b204e9800998ecf8427e"
      host = ${?SEARCH_API_HOST}
      key = ${?SEARCH_API_KEY}
    }

    objection {
      #host = "http://localhost:3000/local"
      #key = "d41d8cd98f00b204e9800998ecf8427e"
      host = "https://api-objection.dev.qvapi.co.nz"
      key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
      host = ${?API_OBJECTION_API_URL}
      key = ${?API_OBJECTION_API_KEY}
    }

    stats {
      host = "https://api-stats.dev.qvapi.co.nz"
      key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
      # host = "http://localhost:3000/dev"
      # key = "d41d8cd98f00b204e9800998ecf8427e"
      host = ${?API_STATS_API_URL}
      key = ${?API_STATS_API_KEY}
    }

    report-generator {
        host = "https://dev.qvapi.co.nz/report-generator"
        key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
        # host = "http://localhost:3000/local"
        # key = "d41d8cd98f00b204e9800998ecf8427e"
        host = ${?API_REPORT_GENERATOR_API_URL}
        key = ${?API_REPORT_GENERATOR_API_KEY}
    }

    consent {
      host = "https://api-consent.dev.qvapi.co.nz"
      key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
      host = ${?API_CONSENT_API_URL}
      key = ${?API_CONSENT_API_KEY}
    }
}



#Public Report Lambda Settings
qvwebsite-api {
  api-url = "https://dev.qvapi.co.nz/public-reports"
  api-key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
  # api-url = "http://localhost:3000/dev"
  # api-key = "d41d8cd98f00b204e9800998ecf8427e"
  api-url = ${?LAMBDA_API_URL}
  api-key = ${?CUSTOMER_REPORTS_API_KEY}
}

qvwebsite-rural-worksheet-api {
  api-url = "https://api-worksheet.dev.qvapi.co.nz"
  api-key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
  # api-url = "http://localhost:3000/dev"
  # api-key = "d41d8cd98f00b204e9800998ecf8427e"
  api-url = ${?RURAL_WORKSHEET_API_URL}
  api-key = ${?RURAL_WORKSHEET_API_KEY}
}

#ReasonForChange Lambda Settings
qvwebsite-reason-for-change-api {
  api-url = "https://api-worksheet.dev.qvapi.co.nz"
  api-key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
  # api-url = "http://localhost:3000/dev"
  # api-key = "d41d8cd98f00b204e9800998ecf8427e"
  api-url = ${?RURAL_WORKSHEET_API_URL}
  api-key = ${?RURAL_WORKSHEET_API_KEY}
}

qvwebsite-maps-api {
  # api-url = "http://localhost:3000/local"
  # api-key = "d41d8cd98f00b204e9800998ecf8427e"
  api-url = "https://api-maps.dev.qvapi.co.nz"
  api-key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
  api-url = ${?MAPS_API_URL}
  api-key = ${?MAPS_API_KEY}
}

floorplan-measure-api {
  api-url = "https://dev.qvapi.co.nz/floor-plan"
  api-key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
  # api-url = "http://localhost:3000/local"
  # api-key = "d41d8cd98f00b204e9800998ecf8427e"
  api-key = ${?FLOOR_PLAN_MEASURE_API_KEY}
  api-url = ${?FLOOR_PLAN_MEASURE_API_URL}
}

pdf-generator-api {
  api-url = "https://api-pdf-report-generator.dev.qvapi.co.nz"
  api-key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
  # api-url = "http://localhost:3000/dev"
  # api-key = "d41d8cd98f00b204e9800998ecf8427e"
  api-key = ${?PDF_GENERATOR_API_KEY}
  api-url = ${?PDF_GENERATOR_API_URL}
}

qvwebsite-rtv-api {
  api-url = "https://api-rtv.dev.qvapi.co.nz"
  api-key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
  # api-url = "http://localhost:3000/dev"
  # api-key = "d41d8cd98f00b204e9800998ecf8427e"
  api-url = ${?RTV_API_URL}
  api-key = ${?RTV_API_KEY}
}

api-picklist {
  api-url = "https://api-picklist.dev.qvapi.co.nz"
  api-key = "KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma"
  # api-url = "http://localhost:3000/local"
  # api-key = "d41d8cd98f00b204e9800998ecf8427e"
  api-key = ${?API_PICKLIST_API_KEY}
  api-url = ${?API_PICKLIST_API_URL}
}

# Auth0 Settings
authentication {
  auth0 {
    clientSecret="1_XENGYXGiK4qvIWzjKEo-bkwSmkuGLX8Q4pglnoQ5oK_9u2prM2Jju3Oq8kc82I"
    clientId="waWLuxADVQVBbooDZ67EXWpddCj5XNmM"
    domain="login.dev.qvmonarch.co.nz"
    redirectURI="http://localhost:9000/callback"
    scope="openid roles user_id name nickname email permissions external_qivs_url groupType userTACode userTAName taGroupName"
    clientSecret=${?AUTH0_CLIENT_SECRET}
    clientId=${?AUTH0_CLIENT_ID}
    domain=${?AUTH0_DOMAIN}
    redirectURI=${?AUTH0_REDIRECT_URI}
  }
}

# AWS Setting
amazon {
  awshost="s3-ap-southeast-2.amazonaws.com"
  photosOriginal="qv-property-photos-migration-original"
  photosResized="qv-property-photos-migration-resized"
  talogs="qv-webassets/ta-logos"
  photosOriginal=${?S3_QV_ORIGINAL}
  photosResized=${?S3_QV_RESIZED}
  aws_access_key_id="********************"
  aws_secret_access_key="6ZfJaVcT7ZqVoGyVTSg99lyTgnFb/GLIUVUTjMh3"
  aws_access_key_id = ${?AWS_ACCESS_KEY}
  aws_secret_access_key = ${?AWS_SECRET_KEY}
  region = "ap-southeast-2"
  peer_review_form_bucket="peerreviewform-dev"
  peer_review_form="PeerReviewForm.docx"
  peer_review_form_bucket=${?AWS_PEER_REVIEW_BUCKET}
  peer_review_form=${?AWS_PEER_REVIEW_FORM}
}

# Google Settings
google {
  apiKey="AIzaSyAb6CU0-QeHJCnfE2UmwL9bKDbj6g5j_0c"
  apiKey=${?GOOGLE_API_KEY}
}

linzMap {
  apiKey="d01fbtg0ar5tffmp20sfzzcctcm"
}

qivs{
  url="http://devawsinweb01-qivs"
  url = ${?QIVS_WEB_UI}
  rural_categories="ADFHPS"
  updateKey="T3DuydwVjsZTINPR3PUPdsEZ60hSss"
  # TODO: TECHDEBT - each env *should* have its own update key
  # updateKey=${?QIVS_UPDATE_KEY}
}

geoserver {
  url="https://testqvms.quotable.co.nz:8443",
  url=${?MAPS_GEOSERVER_URL}
}

geoserverProxy {
  url="/api/maps",
  url=${?MAPS_GEOSERVER_PROXY_URL}
}

email{
  completedValuation{
    to=""
    from=""
    fromName=""
    userName=""
    password=""
    host=""
    smtpPort=""
    to=${?EMAIL_TO}
    from=${?EMAIL_FROM}
    fromName=${?EMAIL_FROM_NAME}
    smtpPort=${?EMAIL_SMTP_PORT}
    host=${?EMAIL_HOST}
  }
  monarchWebUrl="https://test.qvmonarch.co.nz";
  monarchWebUrl=${?MONARCH_WEB_URL};

}

taDashboard{
  showGraphs=true
}

# Enable Security Filters
play.filters.enabled = []
play.filters.enabled += play.filters.csrf.CSRFFilter
play.filters.enabled += play.filters.headers.SecurityHeadersFilter
play.filters.enabled += play.filters.hosts.AllowedHostsFilter
play.filters.enabled += security.Auth0Filter
play.filters.hosts {
  allowed = [".elb.amazonaws.com:8080", ".qvmonarch.co.nz", "localhost:9000", ".:32080", ".monarch.internal.quotablevalue.co.nz"]
}
play.filters.csrf.header.bypassHeaders {
  X-Requested-With = "*"
  Csrf-Token = "nocheck"
}
play.filters.headers.contentSecurityPolicy = "default-src 'self' *.google.com *.gstatic.com *.googleapis.com *.openstreetmap.org *.linz.govt.nz *.amazonaws.com *.qvmonarch.co.nz *.monarch.internal.quotablevalue.co.nz *.auth0.com https://testqvms.quotable.co.nz:8443 https://qvms.quotable.co.nz:8443 https://fonts.googleapis.com https://fonts.gstatic.com https://secure.gravatar.com cdn.jsdelivr.net data: blob: 'unsafe-inline'"
play.filters.headers.referrerPolicy = "no-referrer-when-downgrade"

# Enable Ajax Filter (disables caching for all Ajax requests).
play.filters.enabled += filters.AjaxFilter

play.http.parser.maxMemoryBuffer=6MB
lagom.persistence.ask-timeout=30s
play.http.parser.maxDiskBuffer=6MB
parsers.anyContent.maxLength=6MB

# Increasing timeout for below servcie is 120s because of comparable sales
# Reporting service timeout is increased 120 second because of export is taking more time
# Property service timeout is increased 20 second because property master detail is taking more time
# Media service timeout is increased 20 second because there are times that media is taking more time and most of the time we are getting CircuitBreakerOpenException
# Stats Service timeout is icreased as we are now using the long running spd to get comparables
lagom.circuit-breaker {
  sale.call-timeout = 120s
  saleAnalysis.call-timeout = 60s
  reporting.call-timeout = 120s
  property.call-timeout = 120s
  media.call-timeout = 60s
  home-valuation.call-timeout = 60s
  roll-maintenance.call-timeout = 120s
  stats.call-timeout = 120s

  // These exceptions should not trigger the circuit breaker
  default.exception-whitelist = [
    "com.lightbend.lagom.javadsl.api.transport.NotFound"
  ]
}

# Cinnamon Monitoring Configuration
lagom.spi.circuit-breaker-metrics-class = "cinnamon.lagom.CircuitBreakerInstrumentation"

cinnamon {

  application = "monarch-web"

  # Configure the metric reporters (JMX and HTTP)
  chmetrics.reporters += "jmx-reporter"
  chmetrics.reporters += "http-reporter"
  chmetrics.http-reporter.host = "0.0.0.0"

  # Disable Class Loading Metrics
  jvm-metrics-producer.class-loading.metrics = off

  # Disable Akka dispatcher metrics
  akka.dispatchers.basic-information.names = []

}

# Include Kubernetes configuration (will be made available in build.sbt if -DbuildTarget=kubernetes is set during build)
include "kubernetes.conf"
# Include local application.conf overrides
include "application.local.conf"
<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <provider class="net.logstash.logback.composite.loggingevent.MessageJsonProvider"/>
            <provider class="net.logstash.logback.composite.loggingevent.MdcJsonProvider"/>
        </encoder>
    </appender>

    <appender name="ASYNCSTDOUT" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="STDOUT" />
        <queueSize>500</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <!-- Default Log Level -->
    <root level="WARN">
        <appender-ref ref="ASYNCSTDOUT"/>
    </root>

    <!-- Log Play Framework messages at INFO level -->
    <logger name="play" level="INFO"/>

    <!-- Log Monarch Application messages at INFO level -->
    <logger name="application" level="INFO"/>
    <logger name="controllers" level="INFO"/>
    <logger name="com.qv" level="INFO"/>
</configuration>

{"apiVersion": "settings.k8s.io/v1alpha1", "kind": "PodPreset", "metadata": {"name": "monarch-web-env-uat"}, "spec": {"selector": {"matchLabels": {"app": "monarch-web"}}, "env": [{"name": "APP_NAME", "value": "monarch-web"}, {"name": "AUTH0_CLIENT_SECRET", "value": "{/uat/Monarch/Auth0/client-secret}"}, {"name": "AUTH0_CLIENT_ID", "value": "{/uat/Monarch/Auth0/client-id}"}, {"name": "AUTH0_DOMAIN", "value": "{/uat/Monarch/Auth0/domain}"}, {"name": "S3_QV_ORIGINAL", "value": "qv-property-photos-migrated-uat-original"}, {"name": "S3_QV_RESIZED", "value": "qv-property-photos-migrated-uat-resized"}, {"name": "QIVS_WEB_UI", "value": "http://uatawsinweb01-qivs"}, {"name": "APPLICATION_SECRET", "value": "c50295ca-4c8c-4405-8056-14aec0987d9a"}, {"name": "AUTH0_REDIRECT_URI", "value": "https://uat.qvmonarch.co.nz/callback"}, {"name": "AUTH0_RETURN_URI", "value": "https://uat.qvmonarch.co.nz/property"}, {"name": "ENV_NAME", "value": "uat"}, {"name": "PLAY_SECURE_SESSION_COOKIE", "value": "true"}, {"name": "EMAIL_TO", "value": "<EMAIL>"}, {"name": "AWS_PEER_REVIEW_BUCKET", "value": "peerreviewform-dev"}, {"name": "AWS_PEER_REVIEW_FORM", "value": "PeerReviewForm.docx"}, {"name": "EMAIL_FROM", "value": "<EMAIL>"}, {"name": "EMAIL_FROM_NAME", "value": "DEV ACC"}, {"name": "EMAIL_SMTP_PORT", "value": "587"}, {"name": "EMAIL_HOST", "value": "email-smtp.us-west-2.amazonaws.com"}, {"name": "MONARCH_WEB_URL", "value": "https://uat.qvmonarch.co.nz"}, {"name": "RP_PLATFORM", "value": "kubernetes"}, {"name": "HOST_IP", "valueFrom": {"fieldRef": {"fieldPath": "status.hostIP"}}}, {"name": "JAVA_OPTS", "value": "-Xmx256m -Dcom.sun.management.jmxremote.port=32013 -Dcom.sun.management.jmxremote.rmi.port=32113 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=$(HOST_IP)"}, {"name": "RURAL_SALES_LAMBDA_API_URL", "value": "https://uat.qvapi.co.nz/sale-analysis"}, {"name": "RURAL_SALES_LAMBDA_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "CUSTOMER_REPORTS_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "LAMBDA_API_URL", "value": "https://uat.qvapi.co.nz/public-reports"}, {"name": "SALES_LAMBDA_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "SALES_LAMBDA_API_URL", "value": "https://uat.qvapi.co.nz/sales"}, {"name": "MAPS_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "MAPS_API_URL", "value": "https://uat.qvapi.co.nz/maps"}, {"name": "PROPERTY_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "PROPERTY_API_URL", "value": "https://uat.qvapi.co.nz/property"}, {"name": "FLOOR_PLAN_MEASURE_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "FLOOR_PLAN_MEASURE_API_URL", "value": "https://uat.qvapi.co.nz/floor-plan"}, {"name": "MAPS_GEOSERVER_URL", "value": "https://testqvms.quotable.co.nz:8443"}, {"name": "MAPS_GEOSERVER_PROXY_URL", "value": "/api/maps"}, {"name": "RURAL_WORKSHEET_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "RURAL_WORKSHEET_API_URL", "value": "https://uat.qvapi.co.nz/worksheet"}, {"name": "SEARCH_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "SEARCH_API_HOST", "value": "https://uat.qvapi.co.nz/search"}, {"name": "PDF_GENERATOR_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "PDF_GENERATOR_API_URL", "value": "https://uat.qvapi.co.nz/pdf-report-generator"}, {"name": "GOOGLE_API_KEY", "value": "AIzaSyCPpfppC19nlwWnxiBGnyz0gb1ruQIfhOM"}, {"name": "RTV_API_URL", "value": "https://uat.qvapi.co.nz/rtv"}, {"name": "RTV_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "API_PICKLIST_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "API_PICKLIST_API_URL", "value": "https://uat.qvapi.co.nz/picklist"}, {"name": "API_OBJECTION_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "API_OBJECTION_API_URL", "value": "https://uat.qvapi.co.nz/objection"}, {"name": "API_STATS_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "API_STATS_API_URL", "value": "https://uat.qvapi.co.nz/stats"}, {"name": "API_REPORT_GENERATOR_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "API_REPORT_GENERATOR_API_URL", "value": "https://uat.qvapi.co.nz/report-generator"}, {"name": "QV_LAMBDA_API_URL", "value": "https://uat.qvapi.co.nz"}, {"name": "QV_LAMBDA_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}, {"name": "API_CONSENT_API_URL", "value": "https://api-consent.uat.qvapi.co.nz"}, {"name": "API_CONSENT_API_KEY", "value": "d0jiEV6KWR67BoQbRsUYl9OXJk4KjVWr54twPIkN"}]}}
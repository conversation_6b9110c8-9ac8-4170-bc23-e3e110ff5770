{"apiVersion": "settings.k8s.io/v1alpha1", "kind": "PodPreset", "metadata": {"name": "monarch-web-env-test"}, "spec": {"selector": {"matchLabels": {"app": "monarch-web"}}, "env": [{"name": "APP_NAME", "value": "monarch-web"}, {"name": "AUTH0_CLIENT_SECRET", "value": "{/test/Monarch/Auth0/client-secret}"}, {"name": "AUTH0_CLIENT_ID", "value": "{/test/Monarch/Auth0/client-id}"}, {"name": "AUTH0_DOMAIN", "value": "{/test/Monarch/Auth0/domain}"}, {"name": "S3_QV_ORIGINAL", "value": "qv-property-photos-migrated-test-original"}, {"name": "S3_QV_RESIZED", "value": "qv-property-photos-migrated-test-resized"}, {"name": "QIVS_WEB_UI", "value": "http://testawsinweb01-qivs"}, {"name": "APPLICATION_SECRET", "value": "c50295ca-4c8c-4405-8056-14aec0987d9a"}, {"name": "AUTH0_REDIRECT_URI", "value": "https://test.qvmonarch.co.nz/callback"}, {"name": "AUTH0_RETURN_URI", "value": "https://test.qvmonarch.co.nz/property"}, {"name": "ENV_NAME", "value": "test"}, {"name": "AWS_PEER_REVIEW_BUCKET", "value": "peerreviewform-dev"}, {"name": "AWS_PEER_REVIEW_FORM", "value": "PeerReviewForm.docx"}, {"name": "PLAY_SECURE_SESSION_COOKIE", "value": "true"}, {"name": "EMAIL_TO", "value": "<EMAIL>"}, {"name": "EMAIL_FROM", "value": "<EMAIL>"}, {"name": "EMAIL_FROM_NAME", "value": "DEV ACC"}, {"name": "EMAIL_SMTP_PORT", "value": "587"}, {"name": "EMAIL_HOST", "value": "email-smtp.us-west-2.amazonaws.com"}, {"name": "MONARCH_WEB_URL", "value": "https://test.qvmonarch.co.nz"}, {"name": "RP_PLATFORM", "value": "kubernetes"}, {"name": "HOST_IP", "valueFrom": {"fieldRef": {"fieldPath": "status.hostIP"}}}, {"name": "JAVA_OPTS", "value": "-Xmx2g -XX:+UseG1GC -Dcom.sun.management.jmxremote.port=32013 -Dcom.sun.management.jmxremote.rmi.port=32113 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=$(HOST_IP)"}, {"name": "RURAL_SALES_LAMBDA_API_URL", "value": "https://api-sale-analysis.test.qvapi.co.nz"}, {"name": "RURAL_SALES_LAMBDA_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "CUSTOMER_REPORTS_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "LAMBDA_API_URL", "value": "https://test.qvapi.co.nz/public-reports"}, {"name": "SALES_LAMBDA_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "SALES_LAMBDA_API_URL", "value": "https://sales.test.qvapi.co.nz"}, {"name": "MAPS_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "MAPS_API_URL", "value": "https://api-maps.test.qvapi.co.nz"}, {"name": "PROPERTY_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "PROPERTY_API_URL", "value": "https://api-property.test.qvapi.co.nz"}, {"name": "FLOOR_PLAN_MEASURE_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "FLOOR_PLAN_MEASURE_API_URL", "value": "https://test.qvapi.co.nz/floor-plan"}, {"name": "MAPS_GEOSERVER_URL", "value": "https://test.maps.internal.quotablevalue.co.nz"}, {"name": "MAPS_GEOSERVER_PROXY_URL", "value": "/api/maps"}, {"name": "RURAL_WORKSHEET_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "RURAL_WORKSHEET_API_URL", "value": "https://api-worksheet.test.qvapi.co.nz"}, {"name": "SEARCH_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "SEARCH_API_HOST", "value": "https://api-search.test.qvapi.co.nz"}, {"name": "PDF_GENERATOR_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "PDF_GENERATOR_API_URL", "value": "https://api-pdf-report-generator.test.qvapi.co.nz"}, {"name": "GOOGLE_API_KEY", "value": "AIzaSyCPpfppC19nlwWnxiBGnyz0gb1ruQIfhOM"}, {"name": "RTV_API_URL", "value": "https://api-rtv.test.qvapi.co.nz"}, {"name": "RTV_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "API_PICKLIST_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "API_PICKLIST_API_URL", "value": "https://api-picklist.test.qvapi.co.nz"}, {"name": "API_OBJECTION_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "API_OBJECTION_API_URL", "value": "https://api-objection.test.qvapi.co.nz"}, {"name": "API_STATS_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "API_STATS_API_URL", "value": "https://api-stats.test.qvapi.co.nz"}, {"name": "API_REPORT_GENERATOR_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "API_REPORT_GENERATOR_API_URL", "value": "https://test.qvapi.co.nz/report-generator"}, {"name": "QV_LAMBDA_API_URL", "value": "https://test.qvapi.co.nz"}, {"name": "QV_LAMBDA_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}, {"name": "API_CONSENT_API_URL", "value": "https://api-consent.test.qvapi.co.nz"}, {"name": "API_CONSENT_API_KEY", "value": "8u6Gn2dD5P4yEPhjZKNXH7TD6xAEBPwN2UaTKhrQ"}]}}
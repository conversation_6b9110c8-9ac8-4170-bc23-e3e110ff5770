{"apiVersion": "settings.k8s.io/v1alpha1", "kind": "PodPreset", "metadata": {"name": "monarch-web-env-prod"}, "spec": {"selector": {"matchLabels": {"app": "monarch-web"}}, "env": [{"name": "APP_NAME", "value": "monarch-web"}, {"name": "AUTH0_CLIENT_SECRET", "value": "{/prod/Monarch/Auth0/client-secret}"}, {"name": "AUTH0_CLIENT_ID", "value": "{/prod/Monarch/Auth0/client-id}"}, {"name": "AUTH0_DOMAIN", "value": "{/prod/Monarch/Auth0/domain}"}, {"name": "S3_QV_ORIGINAL", "value": "qv-prod-property-photos-mbackup-original"}, {"name": "S3_QV_RESIZED", "value": "qv-prod-property-photos-mbackup-resized"}, {"name": "QIVS_WEB_UI", "value": "http://qvartinweb01-qivs"}, {"name": "AWS_PEER_REVIEW_BUCKET", "value": "peerreviewform-prod"}, {"name": "AWS_PEER_REVIEW_FORM", "value": "PeerReviewForm.docx"}, {"name": "APPLICATION_SECRET", "value": "c50295ca-4c8c-4405-8056-14aec0987d9a"}, {"name": "AUTH0_REDIRECT_URI", "value": "https://qvmonarch.co.nz/callback"}, {"name": "AUTH0_RETURN_URI", "value": "https://qvmonarch.co.nz/property"}, {"name": "ENV_NAME", "value": "prod"}, {"name": "PLAY_SECURE_SESSION_COOKIE", "value": "true"}, {"name": "EMAIL_FROM", "value": "<EMAIL>"}, {"name": "EMAIL_FROM_NAME", "value": "QV Home Valuation Support"}, {"name": "EMAIL_SMTP_PORT", "value": "587"}, {"name": "EMAIL_HOST", "value": "email-smtp.us-west-2.amazonaws.com"}, {"name": "MONARCH_WEB_URL", "value": "https://qvmonarch.co.nz"}, {"name": "AWS_ACCESS_KEY", "value": "********************"}, {"name": "AWS_SECRET_KEY", "value": "dQ77MR63w7ZjAeYna4duZne+AA3Qx63SagND5Xv4"}, {"name": "RP_PLATFORM", "value": "kubernetes"}, {"name": "HOST_IP", "valueFrom": {"fieldRef": {"fieldPath": "status.hostIP"}}}, {"name": "JAVA_OPTS", "value": "-Xmx2g -XX:+UseG1GC -Dcom.sun.management.jmxremote.port=32013 -Dcom.sun.management.jmxremote.rmi.port=32113 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=$(HOST_IP)"}, {"name": "RURAL_SALES_LAMBDA_API_URL", "value": "https://api-sale-analysis.prod.qvapi.co.nz"}, {"name": "RURAL_SALES_LAMBDA_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "CUSTOMER_REPORTS_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "LAMBDA_API_URL", "value": "https://prod.qvapi.co.nz/public-reports"}, {"name": "FLOOR_PLAN_MEASURE_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "FLOOR_PLAN_MEASURE_API_URL", "value": "https://prod.qvapi.co.nz/floor-plan"}, {"name": "RURAL_WORKSHEET_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "RURAL_WORKSHEET_API_URL", "value": "https://api-worksheet.prod.qvapi.co.nz"}, {"name": "MAPS_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "MAPS_API_URL", "value": "https://api-maps.prod.qvapi.co.nz"}, {"name": "MAPS_GEOSERVER_URL", "value": "https://prod.maps.internal.quotablevalue.co.nz"}, {"name": "MAPS_GEOSERVER_PROXY_URL", "value": "/api/maps"}, {"name": "SALES_LAMBDA_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "SALES_LAMBDA_API_URL", "value": "https://sales.prod.qvapi.co.nz"}, {"name": "PROPERTY_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "PROPERTY_API_URL", "value": "https://api-property.prod.qvapi.co.nz"}, {"name": "SEARCH_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "SEARCH_API_HOST", "value": "https://api-search.prod.qvapi.co.nz"}, {"name": "PDF_GENERATOR_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "PDF_GENERATOR_API_URL", "value": "https://api-pdf-report-generator.prod.qvapi.co.nz"}, {"name": "GOOGLE_API_KEY", "value": "AIzaSyCPpfppC19nlwWnxiBGnyz0gb1ruQIfhOM"}, {"name": "RTV_API_URL", "value": "https://api-rtv.prod.qvapi.co.nz"}, {"name": "RTV_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "API_PICKLIST_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "API_PICKLIST_API_URL", "value": "https://api-picklist.prod.qvapi.co.nz"}, {"name": "API_OBJECTION_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "API_OBJECTION_API_URL", "value": "https://api-objection.prod.qvapi.co.nz"}, {"name": "API_STATS_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "API_STATS_API_URL", "value": "https://api-stats.prod.qvapi.co.nz"}, {"name": "API_REPORT_GENERATOR_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "API_REPORT_GENERATOR_API_URL", "value": "https://prod.qvapi.co.nz/report-generator"}, {"name": "QV_LAMBDA_API_URL", "value": "https://prod.qvapi.co.nz"}, {"name": "QV_LAMBDA_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}, {"name": "API_CONSENT_API_URL", "value": "https://api-consent.prod.qvapi.co.nz"}, {"name": "API_CONSENT_API_KEY", "value": "SFfxBlUuh37vNaVeccBpq6R8Nwma0xVNaQCMhtcH"}]}}
{"apiVersion": "settings.k8s.io/v1alpha1", "kind": "PodPreset", "metadata": {"name": "monarch-web-env-{ENV_NAME}"}, "spec": {"selector": {"matchLabels": {"app": "monarch-web"}}, "env": [{"name": "APP_NAME", "value": "monarch-web"}, {"name": "AUTH0_CLIENT_SECRET", "value": "{AUTH0_CLIENT_SECRET}"}, {"name": "AUTH0_CLIENT_ID", "value": "{AUTH0_CLIENT_ID}"}, {"name": "AUTH0_DOMAIN", "value": "{AUTH0_DOMAIN}"}, {"name": "S3_QV_ORIGINAL", "value": "{S3_QV_RESIZED}"}, {"name": "S3_QV_RESIZED", "value": "{S3_QV_RESIZED}"}, {"name": "QIVS_WEB_UI", "value": "{QIVS_WEB_UI}"}, {"name": "AWS_PEER_REVIEW_BUCKET", "value": "{AWS_PEER_REVIEW_BUCKET}"}, {"name": "AWS_PEER_REVIEW_FORM", "value": "{AWS_PEER_REVIEW_FORM}"}, {"name": "APPLICATION_SECRET", "value": "c50295ca-4c8c-4405-8056-14aec0987d9a"}, {"name": "AUTH0_REDIRECT_URI", "value": "{AUTH0_REDIRECT_URI}"}, {"name": "AUTH0_RETURN_URI", "value": "{AUTH0_RETURN_URI}"}, {"name": "ENV_NAME", "value": "{ENV_NAME}"}, {"name": "PLAY_SECURE_SESSION_COOKIE", "value": "true"}, {"name": "EMAIL_TO", "value": "{EMAIL_TO}"}, {"name": "EMAIL_FROM", "value": "{EMAIL_FROM}"}, {"name": "EMAIL_FROM_NAME", "value": "{EMAIL_FROM_NAME}"}, {"name": "EMAIL_SMTP_PORT", "value": "{EMAIL_SMTP_PORT}"}, {"name": "EMAIL_HOST", "value": "{EMAIL_HOST}"}, {"name": "MONARCH_WEB_URL", "value": "{MONARCH_WEB_URL}"}, {"name": "RP_PLATFORM", "value": "kubernetes"}, {"name": "HOST_IP", "valueFrom": {"fieldRef": {"fieldPath": "status.hostIP"}}}, {"name": "JAVA_OPTS", "value": "-Xmx2g -XX:+UseG1GC -Dcom.sun.management.jmxremote.port=32013 -Dcom.sun.management.jmxremote.rmi.port=32113 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=$(HOST_IP)"}, {"name": "RURAL_SALES_LAMBDA_API_URL", "value": "{RURAL_SALES_LAMBDA_API_URL}"}, {"name": "RURAL_SALES_LAMBDA_API_KEY", "value": "{API_KEY}"}, {"name": "CUSTOMER_REPORTS_API_KEY", "value": "{API_KEY}"}, {"name": "LAMBDA_API_URL", "value": "{PUBLIC_REPORTS_API_URL}"}, {"name": "FLOOR_PLAN_MEASURE_API_KEY", "value": "{API_KEY}"}, {"name": "FLOOR_PLAN_MEASURE_API_URL", "value": "{FLOOR_PLAN_MEASURE_API_URL}"}, {"name": "RURAL_WORKSHEET_API_KEY", "value": "{API_KEY}"}, {"name": "RURAL_WORKSHEET_API_URL", "value": "{RURAL_WORKSHEET_API_URL}"}, {"name": "MAPS_API_KEY", "value": "{API_KEY}"}, {"name": "MAPS_API_URL", "value": "{MAPS_API_URL}"}, {"name": "MAPS_GEOSERVER_URL", "value": "{MAPS_URL}"}, {"name": "MAPS_GEOSERVER_PROXY_URL", "value": "{MAPS_PROXY_URL}"}, {"name": "SALES_LAMBDA_API_KEY", "value": "{API_KEY}"}, {"name": "SALES_LAMBDA_API_URL", "value": "{SALES_LAMBDA_API_URL}"}, {"name": "PROPERTY_API_KEY", "value": "{API_KEY}"}, {"name": "PROPERTY_API_URL", "value": "{PROPERTY_API_URL}"}, {"name": "SEARCH_API_KEY", "value": "{API_KEY}"}, {"name": "SEARCH_API_HOST", "value": "{SEARCH_API_HOST}"}, {"name": "PDF_GENERATOR_API_KEY", "value": "{API_KEY}"}, {"name": "PDF_GENERATOR_API_URL", "value": "{PDF_GENERATOR_API_URL}"}, {"name": "GOOGLE_API_KEY", "value": "{GOOGLE_API_KEY}"}, {"name": "RTV_API_URL", "value": "{RTV_API_URL}"}, {"name": "RTV_API_KEY", "value": "{API_KEY}"}, {"name": "API_PICKLIST_API_KEY", "value": "{API_KEY}"}, {"name": "API_PICKLIST_API_URL", "value": "{API_PICKLIST_API_URL}"}, {"name": "API_OBJECTION_API_KEY", "value": "{API_KEY}"}, {"name": "API_OBJECTION_API_URL", "value": "{API_OBJECTION_API_URL}"}, {"name": "API_STATS_API_KEY", "value": "{API_KEY}"}, {"name": "API_STATS_API_URL", "value": "{API_STATS_API_URL}"}, {"name": "API_REPORT_GENERATOR_API_KEY", "value": "{API_KEY}"}, {"name": "API_REPORT_GENERATOR_API_URL", "value": "{API_REPORT_GENERATOR_API_URL}"}, {"name": "API_CONSENT_API_URL", "value": "{API_CONSENT_API_URL}"}, {"name": "API_CONSENT_API_KEY", "value": "{API_CONSENT_API_KEY}"}]}}
{"apiVersion": "apps/v1", "kind": "StatefulSet", "metadata": {"name": "monarch-web"}, "spec": {"selector": {"matchLabels": {"app": "monarch-web"}}, "serviceName": "monarch-web", "replicas": 1, "template": {"metadata": {"labels": {"app": "monarch-web", "version": "{version}"}}, "spec": {"containers": [{"name": "monarch-web", "image": "373100629421.dkr.ecr.ap-southeast-2.amazonaws.com/monarch/monarch-web-impl:{version}", "imagePullPolicy": "IfNotPresent", "resources": {"requests": {"memory": "512Mi"}}, "ports": [{"containerPort": 9000, "name": "http-play"}, {"containerPort": 8080, "name": "cinnamon-http"}, {"containerPort": 32013, "name": "jmx"}, {"containerPort": 32113, "name": "jmx-rmi"}], "readinessProbe": {"tcpSocket": {"port": 9000}, "initialDelaySeconds": 5, "periodSeconds": 30}, "livenessProbe": {"tcpSocket": {"port": 9000}, "initialDelaySeconds": 60, "periodSeconds": 60}}]}}}}
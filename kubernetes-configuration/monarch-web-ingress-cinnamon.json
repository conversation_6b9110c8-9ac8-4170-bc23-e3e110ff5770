{"apiVersion": "extensions/v1beta1", "kind": "Ingress", "metadata": {"name": "monarch-web-cinnamon-ingress", "annotations": {"kubernetes.io/ingress.class": "nginx-private", "nginx.ingress.kubernetes.io/ssl-redirect": "false", "nginx.ingress.kubernetes.io/rewrite-target": "/"}}, "spec": {"rules": [{"http": {"paths": [{"path": "/metrics/monarch-web", "backend": {"serviceName": "monarch-web", "servicePort": 8080}}]}}]}}
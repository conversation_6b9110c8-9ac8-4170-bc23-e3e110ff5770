package util;

import akka.util.ByteString;
import play.http.HttpEntity;
import play.mvc.ResponseHeader;
import play.mvc.Result;
import play.shaded.ahc.org.asynchttpclient.AsyncHttpClient;
import play.shaded.ahc.org.asynchttpclient.BoundRequestBuilder;
import play.shaded.ahc.org.asynchttpclient.DefaultAsyncHttpClient;
import play.shaded.ahc.org.asynchttpclient.Response;

import javax.inject.Inject;
import javax.inject.Singleton;

import java.util.HashMap;
import java.util.Optional;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

import static play.mvc.Results.status;

@Singleton
public class LambdaHandler {
    public final AsyncHttpClient client;
    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");

    @Inject
    public LambdaHandler() {
        client = new DefaultAsyncHttpClient();
    }

    public BoundRequestBuilder prepareGet(String url, String apiKey) {
        return client
                .prepareGet(url)
                .addHeader("x-api-key", apiKey)
                .setHeader("Content-Type", "application/json");
    }

    public BoundRequestBuilder preparePost(String url, String apiKey) {
        return client
                .preparePost(url)
                .addHeader("x-api-key", apiKey)
                .setHeader("Content-Type", "application/json");
    }

    public static Result responsePassThrough(Response response, Charset charset) {
        if (response.getStatusCode() != 200) {
            logger.warn("{} response from {} Path: {}?{} Body: {}",
                    response.getStatusCode(),
                    response.getUri().getHost(),
                    response.getUri().getPath(),
                    response.getUri().getQuery(),
                    response.getResponseBody(StandardCharsets.UTF_8)
            );
        }

        if (charset == null){
            return status(response.getStatusCode(), response.getResponseBody()).as("application/json");
        }

        return status(response.getStatusCode(), response.getResponseBody(charset)).as("application/json");
    }

    public static Result responsePassThrough(Response response) {
        return responsePassThrough(response, null);
    }

    public static Result geoServerResponsePassThrough(Response response) {
        HashMap<String, String> headersMap = new HashMap<>();
        response.getHeaders().entries().forEach( header -> {
            if(!header.getKey().equalsIgnoreCase( "Content-Type")
                    && !header.getKey().equalsIgnoreCase( "Transfer-Encoding")) {
                    headersMap.put(header.getKey(),header.getValue());
            }
        });
        return new Result(
                new ResponseHeader(response.getStatusCode(),headersMap),
                new HttpEntity.Strict(ByteString.fromArray(response.getResponseBodyAsBytes()), Optional.of(response.getContentType()))
        );
    }
}

package models;

import com.lightbend.lagom.serialization.Jsonable;
import com.qv.property.api.model.property.PropertyDetail;
import com.qv.rollmaintenance.api.ValidationResult;
import com.qv.rollmaintenance.api.model.valuation.RatingValuation;
import lombok.AllArgsConstructor;
import lombok.Value;
import lombok.experimental.Wither;

@Value
@AllArgsConstructor
@Wither
public class CompleteRatingValuationResult implements Jsonable {
    ValidationResult<RatingValuation> ratingValuationResult;
    ValidationResult<PropertyDetail> propertyDetailResult;

    public Boolean hasErrors() {
        return ratingValuationResult.hasErrors() || propertyDetailResult.hasErrors();
    }

    public Boolean hasWarnings() {
        return ratingValuationResult.hasWarnings() || propertyDetailResult.hasWarnings();
    }
}

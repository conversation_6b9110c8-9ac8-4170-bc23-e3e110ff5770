package services;

import com.fasterxml.jackson.databind.JsonNode;
import com.typesafe.config.Config;
import play.shaded.ahc.org.asynchttpclient.AsyncHttpClient;
import play.shaded.ahc.org.asynchttpclient.DefaultAsyncHttpClient;
import play.shaded.ahc.org.asynchttpclient.Response;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.concurrent.CompletableFuture;

@Singleton
public class ReasonForChangeApiService {
    private final String host;
    private final String key;
    private final AsyncHttpClient http;

    @Inject
    public ReasonForChangeApiService(Config config) {
        this.http = new DefaultAsyncHttpClient();
        this.host = config.getString("qvwebsite-reason-for-change-api.api-url");
        this.key = config.getString("qvwebsite-reason-for-change-api.api-key");
    }

    public CompletableFuture<Response> getExistingReasonForChange(Integer qpid, String userName) {
        return http
                .prepareGet(host + "/getExistingReasonForChange?qpid=" + qpid + "&userName=" + userName)
                .addHeader("x-api-key", key)
                .setHeader("Content-Type", "application/json")
                .execute().toCompletableFuture();
    }

    public CompletableFuture<Response> addReasonForChange(JsonNode body) {
        return http
                .preparePost(host + "/addReasonForChange")
                .addHeader("x-api-key", key)
                .setHeader("Content-Type", "application/json")
                .setBody(body.toString())
                .execute().toCompletableFuture();

    }
}
package controllers;

import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import models.CompleteReportUploadRequest;
import models.CreateReportUploadRequest;
import org.json.JSONObject;
import play.libs.Json;
import play.mvc.Result;
import play.shaded.ahc.org.asynchttpclient.Response;
import services.ReportsApiService;
import util.LoggerWrapper;

import javax.inject.Inject;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

/**
 * This controller acts as a proxy between Monarch web and API Website Lambdas to provide functionality to upload a PDF Report to a QV Website Users Cloud Dashboard.
 */

@SubjectPresent
public class ReportUploadController extends AbstractController {
    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");

    /**
     * Used to get Configuration Values from application.conf.
     */
    private final Config config;

    /**
     * Used to parse JSON responses.
     */
    private final ObjectMapper objectMapper;
    private final ReportsApiService reportsApiService;

    @Inject
    public ReportUploadController(Config config, ObjectMapper objectMapper, ReportsApiService reportsApiService) {
        this.config = config;
        this.objectMapper = objectMapper;
        this.reportsApiService = reportsApiService;
    }

    /**
     * Create a Report Record in API Hub Database for uploaded Report PDF.
     *
     * @return The newly created Report Entry Object
     */
    @SubjectPresent
    public CompletionStage<Result> createUploadedReportEntry() {
        logger.info("Request for createUploadedReportEntry received.");

        try {
            CreateReportUploadRequest request = Json.fromJson(request().body().asJson(), CreateReportUploadRequest.class);
            Integer qpid = request.getQpid();
            String reportType = request.getReportType();

            logger.info("Creating new Report Entry for QPID: {}", qpid);
            return createReport(qpid, reportType)
                    .thenApply(response -> {
                        try {
                            JsonNode json = objectMapper.readTree(response.getResponseBody());
                            return status(response.getStatusCode(), json);
                        } catch (IOException e) {
                            logger.error("ERR-UPR-008", "Exception mapping response from /createReport Lambda: ", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            logger.error("ERR-UPR-001", "createUploadedReportEntry error", e);
            return CompletableFuture.completedFuture(status(400, Json.toJson(e.getMessage())));
        }
    }

    /**
     * Call the Complete Report Endpoint and if successful link the report to the supplied User.
     *
     * @return The Status Code of the Lambda Endpoints Response.
     */
    @SubjectPresent
    public CompletionStage<Result> completeReportUpload() {
        logger.info("Request for completeReportUpload received.");
        try {

            CompleteReportUploadRequest request = Json.fromJson(request().body().asJson(), CompleteReportUploadRequest.class);
            String jobId = request.getJobId();
            Integer userId = request.getUserId();
            Optional<Integer> subscriptionId = request.getSubscriptionId();

            logger.info("Completing Report Job for Job: {}", jobId);
            return completeReport(jobId, false).thenCompose(completeResponse -> {
                if (completeResponse.getStatusCode() != 200) {
                    logger.error("ERR-UPR-002", "Completion of Report Job: {} has failed with status code: {}\n{}", jobId, completeResponse.getStatusCode(), completeResponse.getResponseBody());
                    try {
                        JsonNode json = objectMapper.readTree(completeResponse.getResponseBody());
                        return CompletableFuture.completedFuture(status(completeResponse.getStatusCode(), json));
                    } catch (IOException e) {
                        logger.error("ERR-UPR-003", "Exception mapping response from /completeReport Lambda: {}", e.getMessage());
                        throw new RuntimeException(e);
                    }

                } else {
                    logger.info("ERR-UPR-004", "Linking Job: {} to userId: {}", jobId, userId);
                    return linkReportToUser(jobId, userId, subscriptionId, true).thenApply(linkResponse -> {
                        logger.info(linkResponse.getResponseBody());
                        try {
                            JsonNode json = objectMapper.readTree(linkResponse.getResponseBody());
                            return status(linkResponse.getStatusCode(), json);
                        } catch (IOException e) {
                            logger.error("ERR-UPR-005", "Exception mapping response from /linkReportToUser Lambda: {}", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
                }
            });
        } catch (Exception e) {
            logger.error("ERR-UPR-006", "completeReportUpload error", e);
            return CompletableFuture.completedFuture(status(400, Json.toJson(e.getMessage())));
        }
    }

    /**
     * Call the createReport Lambda endpoint to create a new record for newly uploaded report.
     *
     * @param qpid
     * @param reportType
     * @return
     */
    public CompletableFuture<Response> createReport(Integer qpid, String reportType) {
        try {
            JSONObject payload = new JSONObject();
            payload.put("qpid", qpid);
            payload.put("reportType", reportType);
            CompletableFuture<Response> responseFuture = reportsApiService.createReport(payload);
            return responseFuture;

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Complete the newly uploaded report job by calling the lambda endpoint.
     *
     * @param jobId
     * @param callPublicWeb
     * @return
     */
    public CompletableFuture<Response> completeReport(String jobId, Boolean callPublicWeb) {
        try {
            JSONObject payload = new JSONObject();
            payload.put("jobId", jobId);
            payload.put("callPublicWeb", callPublicWeb);
            CompletableFuture<Response> responseFuture = reportsApiService.completeReport(payload);
            return responseFuture;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * Once the report has been successfully completed via compete lambda, link it to a User and if supplied
     * a subscription by calling the linkReportToUser endpoint.
     *
     * @param jobId
     * @param userId
     * @param subscriptionId
     * @param callPublicWeb
     * @return
     */
    public CompletableFuture<Response> linkReportToUser(String jobId, Integer userId, Optional<Integer> subscriptionId, Boolean callPublicWeb) {
        try {
            JSONObject payload = new JSONObject();
            payload.put("jobId", jobId);
            payload.put("userId", userId);
            if (subscriptionId.isPresent()) {
                payload.put("subscriptionId", subscriptionId.get());
            }
            payload.put("callPublicWeb", callPublicWeb);
            CompletableFuture<Response> responseFuture = reportsApiService.linkReportToUser(payload);
            return responseFuture;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

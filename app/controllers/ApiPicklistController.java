package controllers;

import be.objectify.deadbolt.java.actions.Group;
import be.objectify.deadbolt.java.actions.Restrict;
import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.qv.media.api.MediaEntry;
import com.qv.media.api.MediaService;
import com.qv.property.api.PropertyService;
import com.qv.property.api.model.property.PropertyDetail;
import com.qv.rollmaintenance.api.RollMaintenanceService;
import com.qv.rollmaintenance.api.model.valuation.RatingValuation;
import com.qv.rollmaintenance.api.model.RollMaintenanceActivity;
import com.typesafe.config.Config;
import models.User;
import play.mvc.Controller;
import play.mvc.Result;
import play.shaded.ahc.org.asynchttpclient.Response;
import security.AuthSupport;
import services.ApiPicklistService;
import util.LambdaHandler;
import util.LoggerWrapper;

import javax.inject.Inject;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.UUID;
import java.util.List;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@SubjectPresent
public class ApiPicklistController extends Controller {
    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");

    /**
     * Used to get Configuration Values from application.conf.
     */
    private final Config config;

    /**
     * Used to parse JSON responses.
     */
    private final ObjectMapper objectMapper;
    private final ApiPicklistService apiPicklistService;

    private final RollMaintenanceService rollMaintenanceService;
    private final PropertyService propertyService;
    private final MediaService mediaService;

    private final AuthSupport authSupport;

    @Inject
    public ApiPicklistController(final Config config, final ObjectMapper objectMapper,
                                 final ApiPicklistService apiPicklistService,
                                 final RollMaintenanceService rollMaintenanceService, final PropertyService propertyService,
                                 final MediaService mediaService,
                                 AuthSupport authSupport) {
        this.config = config;
        this.objectMapper = objectMapper;
        this.apiPicklistService = apiPicklistService;
        this.rollMaintenanceService = rollMaintenanceService;
        this.propertyService = propertyService;
        this.mediaService = mediaService;
        this.authSupport = authSupport;
    }

    @SubjectPresent
    public CompletionStage<Result> getPicklistValue(String keys) {
        logger.info("Request for getPicklistValue received. keys: {}", keys);

        try {
            CompletableFuture<Response> completeResponse = apiPicklistService.getPicklistValue(keys);

            return completeResponse
                    .thenApply(response -> {
                        try {
                            JsonNode json = objectMapper.readTree(response.getResponseBody());
                            return status(response.getStatusCode(), json);
                        } catch (IOException e) {
                            logger.error("ERR-APL-001", "Exception mapping response from /getPicklistValue lambda with keys {} {}", keys, e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            logger.error("ERR-APL-002", "Exception while fetching picklist with keys {} {}", keys, e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> getSalesGroupsAndRolls(String keys) {
        logger.info("Request for getSalesGroupsAndRolls received. keys: {}", keys);

        try {
            CompletableFuture<Response> completeResponse = apiPicklistService.getSalesGroupsAndRolls(keys);

            return completeResponse
                    .thenApply(response -> {
                        try {
                            JsonNode json = objectMapper.readTree(response.getResponseBody());
                            return status(response.getStatusCode(), json);
                        } catch (IOException e) {
                            logger.error("ERR-APL-003", "Exception mapping response from /getSalesGroupsAndRolls Lambda: {}", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            logger.error("ERR-APL-004", "Exception while fetching salesGroupsAndRolls from api-picklist with tas {} {}", keys, e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> searchObjection(String queryString) {
        logger.info("Request for searchObjection received.");
        return apiPicklistService.searchObjection(request().body().asJson(), queryString)
                .thenApply((response) -> {
                    return LambdaHandler.responsePassThrough(response, StandardCharsets.UTF_8);
                })
                .exceptionally(e -> {
                    logger.error("ERR-APL-006", "Exception while calling searchObjection", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> exportObjection(String queryString) {
        logger.info("Request for exportObjection received.");
        return apiPicklistService.exportObjection(request().body().asJson(), queryString)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-005", "Exception while calling exportObjection", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> bulkAssignObjection() {
        logger.info("Request for bulkAssignObjection received.");
        return apiPicklistService.bulkAssignObjection(request().body().asJson())
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-010", "Exception while calling batchAssignObjection", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> searchComparableSales() {
        logger.info("Request for searchComparableSales received.");
        return apiPicklistService.searchComparableSales(request().body().asJson(), request().asScala().rawQueryString())
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-007", "Exception while calling searchComparableSales", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> getComparableSales(String ratingValuationId) {
        logger.info("Request for getComparableSales received.");
        return apiPicklistService.getComparableSales(ratingValuationId, request().asScala().rawQueryString())
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-008", "Exception while calling getComparableSales", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> addComparableSales(String ratingValuationId, Boolean shouldIncludeAllSales) {
        logger.info("Request for addComparableSales received.");
        return apiPicklistService
                .addComparableSales(request().body().asJson(), ratingValuationId, shouldIncludeAllSales)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-009", "Exception while calling addComparableSales", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> updateComparableSales(String ratingValuationId) {
        logger.info("Request for updateComparableSales received.");
        return apiPicklistService.updateComparableSales(request().body().asJson(), ratingValuationId)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-011", "Exception while calling updateComparableSales", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> removeComparableSales(String ratingValuationId) {
        logger.info("Request for removeComparableSales received.");
        return apiPicklistService.removeComparableSales(request().body().asJson(), ratingValuationId)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-012", "Exception while calling removeComparableSales", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> refreshComparableSale(String ratingValuationId) {
        logger.info("Request for refreshComparableSale received.");
        return apiPicklistService.refreshComparableSale(request().body().asJson(), ratingValuationId)
                .thenApply(LambdaHandler::responsePassThrough)
                .exceptionally(e -> {
                    logger.error("ERR-APL-013", "Exception while calling refreshComparableSale", e.getCause());
                    return status(500);
                });
    }

    @Restrict({@Group("INTERNAL_USER")})
    @SubjectPresent
    public CompletionStage<Result> addObjectionJobReview() {
        logger.info("Request for addObjectionJobReview received.");
        return apiPicklistService.addObjectionJobReview(request().body().asJson())
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-014", "Exception while calling addObjectionJobReview", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> getObjectionJobReview(String ratingValuationId) {
        logger.info("Request for getObjectionJobReview received.");
        return apiPicklistService.getObjectionJobReview(ratingValuationId)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-015", "Exception while calling getObjectionJobReview", e.getCause());
                    return status(500);
                });
    }

    @Restrict({@Group("INTERNAL_USER")})
    @SubjectPresent
    public CompletionStage<Result> updateObjectionJobReview(String ratingValuationId) {
        logger.info("Request for updateObjectionJobReview received.");
        JsonNode requestBodyJson = request().body().asJson();
        RatingValuation ratingValuation = rollMaintenanceService.getRatingValuation(UUID.fromString(ratingValuationId))
                .invoke().toCompletableFuture().join();
        // Need adopted value fields if review passed and further objector contact not
        // required, so the valuation can complete.
        Optional<BigDecimal> adoptedCapitalValue = ratingValuation.getAdoptedValue().get().getCapitalValue();
        Optional<BigDecimal> adoptedLandValue = ratingValuation.getAdoptedValue().get().getLandValue();
        ((ObjectNode) requestBodyJson).put("adoptedCapitalValue", adoptedCapitalValue.get());
        ((ObjectNode) requestBodyJson).put("adoptedLandValue", adoptedLandValue.get());
        Optional<User> currentUser = authSupport.currentUser(ctx());
        ((ObjectNode) requestBodyJson).put("employeeName", currentUser.get().getName());
        return apiPicklistService.updateObjectionJobReview(requestBodyJson)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-016", "Exception while calling updateObjectionJobReview", e.getCause());
                    return status(500);
                });
    }

    @Restrict({@Group("INTERNAL_USER")})
    @SubjectPresent
    public CompletionStage<Result> completeObjectionJobValuation(String ratingValuationId)
            throws JsonProcessingException {
        logger.info("Request for completeObjectionJobValuation received.");
        Optional<User> currentUser = authSupport.currentUser(ctx());
        RatingValuation ratingValuation = rollMaintenanceService.getRatingValuation(UUID.fromString(ratingValuationId))
                .invoke().toCompletableFuture().join();
        Optional<BigDecimal> adoptedCapitalValue = ratingValuation.getAdoptedValue().get().getCapitalValue();
        Optional<BigDecimal> adoptedLandValue = ratingValuation.getAdoptedValue().get().getLandValue();
        ObjectNode parentNode = objectMapper.createObjectNode();
        parentNode.put("ratingValuationId", ratingValuationId);
        parentNode.put("adoptedCapitalValue", adoptedCapitalValue.get());
        parentNode.put("adoptedLandValue", adoptedLandValue.get());
        parentNode.put("employeeName", currentUser.get().getName());
        String body = objectMapper.writeValueAsString(parentNode);
        return apiPicklistService.completeObjectionJobValuation(body)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-017", "Exception while calling completeObjectionJobValuation", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> getObjectionContact(String ratingValuationId) {
        logger.info("Request for getObjectionContact received. rating valuation id: {}", ratingValuationId);
        return apiPicklistService.getObjectionContact(ratingValuationId)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-018", "Exception while calling getObjectionContact", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> updateObjectionContact(String ratingValuationId) {
        logger.info("Request for getObjectionContact received.");
        return apiPicklistService.updateObjectionContact(request().body().asJson(), ratingValuationId)
                .thenApply(LambdaHandler::responsePassThrough)
                .exceptionally(e -> {
                    logger.error("ERR-APL-019", "Exception while calling updateObjectionContact", e.getCause());
                    return status(500);
                });
    }

    @Restrict({@Group("INTERNAL_USER")})
    @SubjectPresent
    public CompletionStage<Result> addRatingValuation(Integer objectionId) {
        logger.info("Request for addRatingValuation received.");
        return apiPicklistService.addRatingValuation(request().body().asJson(), objectionId)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-020", "Exception while calling addRatingValuation", e.getCause());
                    return status(500);
                });
    }

    @Restrict({@Group("INTERNAL_USER")})
    @SubjectPresent
    public CompletionStage<Result> validateObjectionJob(String ratingValuationId, Boolean isValidatingAtReviewStage) throws JsonProcessingException {
        logger.info("Request for validateObjectionJob received. rating valuation id: {}", ratingValuationId);
        RatingValuation ratingValuation = rollMaintenanceService.getRatingValuation(UUID.fromString(ratingValuationId))
                .invoke().toCompletableFuture().join();
        UUID propertyId = ratingValuation.getRatingUnit().getPropertyId();
        PropertyDetail draftProperty = propertyService.getPendingPropertyDetail(propertyId).invoke()
                .toCompletableFuture().join();
        Optional<MediaEntry> primaryPhoto = mediaService.getPrimaryMediaByOwner(propertyId, "Property").invoke()
                .toCompletableFuture().thenApply(Optional::of).exceptionally(e -> {
                    logger.error("ERR-APL-020", "Error invoking getPrimaryMediaByOwner for propertyId: " + propertyId, e);
                    return Optional.empty();
                }).join();

        Optional<Boolean> canBeValued = Optional.ofNullable(ratingValuation.getRatingUnit().getPropertyId())
                .map(id -> propertyService.canBeValued(id).invoke().toCompletableFuture().thenApply(Optional::of))
                .orElse(CompletableFuture.completedFuture(Optional.empty()))
                .join();

        List<String> ids = ratingValuation.getRollMaintenanceActivityIds();
        List<RollMaintenanceActivity> rollMaintenanceActivities = rollMaintenanceService.getRollMaintenanceActivities()
                .invoke(ids).toCompletableFuture().join();

        ObjectNode parentNode = objectMapper.createObjectNode();
        JsonNode ratingValuationJsonNode = objectMapper.valueToTree(ratingValuation);
        JsonNode draftPropertyJsonNode = objectMapper.valueToTree(draftProperty);
        JsonNode primaryPhotoJsonNode = objectMapper.valueToTree(primaryPhoto);
        JsonNode rollMaintenanceActivitiesJsonNode = objectMapper.valueToTree(rollMaintenanceActivities);
        JsonNode canBeValuedJsonNode = objectMapper.valueToTree(canBeValued);

        parentNode.set("ratingValuation", ratingValuationJsonNode);
        parentNode.set("draftProperty", draftPropertyJsonNode);
        parentNode.set("primaryPhoto", primaryPhotoJsonNode);
        parentNode.set("activities", rollMaintenanceActivitiesJsonNode);
        parentNode.set("canBeValued", canBeValuedJsonNode);
        parentNode.put("isValidatingAtReviewStage", isValidatingAtReviewStage);

        String body = objectMapper.writeValueAsString(parentNode);
        return apiPicklistService.validateObjectionJob(body)
                .thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
                    logger.error("ERR-APL-021", "Exception while calling validateObjectionJob", e.getCause());
                    return status(500);
                });
    }

    @Restrict({@Group("INTERNAL_USER")})
    @SubjectPresent
    public CompletionStage<Result> autoSelectComparableSales() {
        logger.info("Request for autoSelectComparableSales received.");
        return apiPicklistService.autoSelectComparableSales(request().body().asJson())
                .thenApply(LambdaHandler::responsePassThrough)
                .exceptionally(e -> {
                    logger.error("ERR-APL-022", "Exception while calling autoSelectComparableSales", e.getCause());
                    return status(500);
                });
    }
}

package controllers;

import akka.NotUsed;
import be.objectify.deadbolt.java.actions.Group;
import be.objectify.deadbolt.java.actions.Restrict;
import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.lightbend.lagom.javadsl.api.ServiceCall;
import com.qv.property.api.PropertyService;
import com.qv.property.api.model.Property;
import com.qv.property.api.model.property.PropertyDetail;
import com.qv.property.api.model.property.PropertyDetailDvrSnapshot;
import com.qv.property.api.model.property.PropertyDetailInferenceLevel;
import com.qv.property.api.model.search.PropertySearchCriteria;
import com.qv.property.api.model.search.PropertySearchResults;
import com.qv.reporting.api.ReportType;
import com.qv.reporting.api.ReportingService;
import com.qv.rollmaintenance.api.RollMaintenanceService;
import com.qv.rollmaintenance.api.model.BuildingConsent;
import com.qv.rollmaintenance.api.model.RollMaintenanceActivity;
import com.qv.rollmaintenance.api.model.search.RollMaintenanceSearchCriteria;
import com.qv.rollmaintenance.api.model.search.RollMaintenanceSearchResult;
import com.qv.rollmaintenance.api.model.valuation.RatingValuation;
import com.qv.rollmaintenance.api.model.valuation.RatingValuationStatus;
import com.typesafe.config.Config;
import play.libs.Json;
import play.mvc.Result;
import play.mvc.Results;
import play.shaded.ahc.org.asynchttpclient.Response;
import services.*;
import util.LoggerWrapper;

import javax.inject.Inject;
import java.io.IOException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * This controller provides front-end access to roll maintenance functionality.
 */
@SubjectPresent
public class RollMaintenanceController extends AbstractController {
    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");


    /**
     * Provides access to the Roll Maintenance Service.
     */
    private final RollMaintenanceService rollMaintenanceService;

    /**
     * Provides access to the Property Service.
     */
    private final PropertyService propertyService;


    /**
     * Provides access to the Reporting Service.
     */
    private final ReportingService reportingService;

    /**
     * A collection of common helper methods.
     */
    private final RollMaintenanceApiService rollMaintenanceApiService;

    /**
     * Provides access to the application.conf configuration
     */
    private final Config config;

    /**
     * Provides access to the Google Maps Service.
     */
    private final GoogleMapsApiService googleMapsApiService;

    private final ApiPicklistService apiPicklistService;

    private final ObjectionApiService objectionApiService;

    private final PropertyApiService propertyApiService;

    private final ObjectMapper objectMapper;

    @Inject
    public RollMaintenanceController(final RollMaintenanceService rollMaintenanceService, final PropertyService propertyService,
                                     final ReportingService reportingService,
                                     final RollMaintenanceApiService rollMaintenanceApiService,
                                     final ApiPicklistService apiPicklistService,
                                     final GoogleMapsApiService googleMapsApiService,
                                     final ObjectionApiService objectionApiService,
                                     final PropertyApiService propertyApiService,
                                     final Config config,
                                     ObjectMapper objectMapper) {
        this.rollMaintenanceService = rollMaintenanceService;
        this.propertyService = propertyService;
        this.reportingService = reportingService;
        this.rollMaintenanceApiService = rollMaintenanceApiService;
        this.config = config;
        this.apiPicklistService = apiPicklistService;
        this.objectMapper = objectMapper;
        this.googleMapsApiService = googleMapsApiService;
        this.objectionApiService = objectionApiService;
        this.propertyApiService = propertyApiService;
    }


    /**
     * Retrieve a single Roll Maintenance Activity.
     *
     * @param id The Roll Maintenance Activity ID.
     * @return A JSON representation of the Roll Maintenance Activity.
     */
    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getRollMaintenanceActivity(String id) {
        logger.info("Request for getRollMaintenanceActivity received. id: {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke()
                .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    public CompletionStage<Result> getRollMaintenanceActivitiesByIds() {
        logger.info("Request for getRollMaintenanceActivitiesByIds received. activity ids: {}", request().body().asJson());
        //Optional<User> currentUser = authSupport.currentUser(ctx());
        List<String> activityIds = Json.fromJson(request().body().asJson(), List.class);
        // get a list of all related roll maintenance activities
        return rollMaintenanceService.getRollMaintenanceActivities().invoke(activityIds)
                //.thenApply(result -> rollMaintenanceApiService.filterAllowed(currentUser, Optional.of(result)))
                .thenApply(result -> ok(Json.toJson(result)))
                .exceptionally(this::handleException);
    }

    /**
     * Save a Roll Maintenance Activity.
     *
     * @return A JSON representation of the save result, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> saveRollMaintenanceActivity() {
        logger.info("Request for saveRollMaintenanceActivity received. \n: {}", request().body().asJson());
        RollMaintenanceActivity activity = Json.fromJson(request().body().asJson(), RollMaintenanceActivity.class);
        return rollMaintenanceService.saveRollMaintenanceActivity(activity.getId()).invoke(activity)
                .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Set the needs more information flag in the Roll Maintenance Activity to True, Update Notes if any provided
     *
     * @return A JSON representation of the saved result upon updating the needs more information flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> requireMoreInformation(String id, String notes, Boolean requestPlans) {
        logger.info("Request for requireMoreInformation received. id = {}, requestPlans = {}", id, requestPlans);
        String formattedNote = this.today().format(DateTimeFormatter.ofPattern("dd/MM/YYYY"))
                + " Needs More Information"
                + ((notes != null && notes.length() > 0) ? " - " + notes : "");

        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity ->
                rollMaintenanceService.saveRollMaintenanceActivity(id)
                        .invoke(rollMaintenanceApiService.appendNote(rollMaintenanceActivity, Optional.of(formattedNote))
                                .withNeedsMoreInformation(Optional.of(true))
                                .withBuildingConsent(
                                        rollMaintenanceActivity.getBuildingConsent().map(x ->
                                                (requestPlans && !x.getPlansObtained().orElse(false) ? x.withPlansRequired(Optional.of(true)).withPlansObtained(Optional.of(false)) : x))
                                )
                        )
                        .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                        .thenApply(Results::ok)
        ).exceptionally(this::handleException);
    }

    /**
     * Set the needs more information flag in the Roll Maintenance Activity to False
     *
     * @return A JSON representation of the saved result upon updating the needs more information flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> informationProvided(String id) {
        logger.info("Request for informationProvided received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity ->
                rollMaintenanceService.saveRollMaintenanceActivity(id)
                        .invoke(rollMaintenanceActivity.withNeedsMoreInformation(Optional.of(false)))
                        .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                        .thenApply(Results::ok)
        ).exceptionally(this::handleException);
    }

    /**
     * Set the needs more information flag in the Roll Maintenance Activity to True
     *
     * @return A JSON representation of the saved result upon updating the needs more information flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> requireInspection(String id, String notes) {
        logger.info("Request for informationProvided received. Roll Maintenance Activity id = {}", id);

        String formattedNote = this.today().format(DateTimeFormatter.ofPattern("dd/MM/YYYY"))
                + " Needs Inspection"
                + ((notes != null && notes.length() > 0) ? " - " + notes : "");

        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity ->
                rollMaintenanceService.saveRollMaintenanceActivity(id)
                        .invoke(
                                rollMaintenanceApiService.appendNote(rollMaintenanceActivity, Optional.of(formattedNote))
                                        .withNeedsInspection(Optional.of(true))
                        )
                        .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                        .thenApply(Results::ok)
        ).exceptionally(this::handleException);
    }

    /**
     * Set the inspection flag in the Roll Maintenance Activity to False
     *
     * @return A JSON representation of the saved result upon updating the needs more information flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> inspected(String id) {
        logger.info("Request for inspected received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity ->
                rollMaintenanceService.saveRollMaintenanceActivity(id)
                        .invoke(rollMaintenanceActivity.withNeedsInspection(Optional.of(false)))
                        .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                        .thenApply(Results::ok)
        ).exceptionally(this::handleException);
    }

    /**
     * Set the construction completion date to today (if not already set) to flag that Construction is Complete
     *
     * @return A JSON representation of the saved result upon updating the needs more information flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> constructionComplete(String id) {
        logger.info("Request for constructionComplete received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity -> {
            // If construction completion date already set then short circuit out
            if (rollMaintenanceActivity.getBuildingConsent().flatMap(BuildingConsent::getConstructionCompletionDate).isPresent()) {
                return rollMaintenanceApiService.injectCurrentPropertyToRollMaintenanceActivity(rollMaintenanceActivity).thenApply(Results::ok);
            }
            // If no construction completion date then set to today (local NZ time)
            return rollMaintenanceService.saveRollMaintenanceActivity(id)
                    .invoke(rollMaintenanceActivity.withBuildingConsent(
                            rollMaintenanceActivity.getBuildingConsent().map(x -> x.withConstructionCompletionDate(Optional.of(today())))
                    ))
                    .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                    .thenApply(Results::ok);
        }).exceptionally(this::handleException);
    }

    /**
     * Set the construction completion date to empty to flag that Construction In Progress
     *
     * @return A JSON representation of the saved result upon updating the needs more information flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> constructionInProgress(String id) {
        logger.info("Request for constructionInProgress received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity -> {
            // Clear the construction date
            return rollMaintenanceService.saveRollMaintenanceActivity(id)
                    .invoke(rollMaintenanceActivity.withBuildingConsent(
                            rollMaintenanceActivity.getBuildingConsent().map(x -> x.withConstructionCompletionDate(Optional.empty()))
                    ))
                    .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                    .thenApply(Results::ok);
        }).exceptionally(this::handleException);
    }

    /**
     * Flag that a compliance certificate has been issued
     *
     * @return A JSON representation of the saved result upon updating the needs more information flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> complianceCertificateIssued(String id) {
        logger.info("Request for complianceCertificateIssued received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity -> {
            // If no construction completion date then set to today (local NZ time)
            return rollMaintenanceService.saveRollMaintenanceActivity(id)
                    .invoke(rollMaintenanceActivity.withBuildingConsent(
                            rollMaintenanceActivity.getBuildingConsent().map(x -> x.withComplianceCertificateIssued(Optional.of(true))
                            )))
                    .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                    .thenApply(Results::ok);
        }).exceptionally(this::handleException);
    }

    /**
     * Flag that a compliance certificate has NOT been issued
     *
     * @return A JSON representation of the saved result upon updating the needs more information flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> noComplianceCertificate(String id) {
        logger.info("Request for noComplianceCertificate received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity -> {
            // Clear the construction date
            return rollMaintenanceService.saveRollMaintenanceActivity(id)
                    .invoke(rollMaintenanceActivity.withBuildingConsent(
                            rollMaintenanceActivity.getBuildingConsent().map(x -> x.withComplianceCertificateIssued(Optional.of(false)))
                    ))
                    .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                    .thenApply(Results::ok);
        }).exceptionally(this::handleException);
    }

    /**
     * Toggle plans required of a roll maintenance activity and save the roll maintenance activity. Plans drawn and plans required can not be ticked at the same time.
     *
     * @return A JSON representation of the saved result upon updating the plans required flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> togglePlansRequired(String id) {
        logger.info("Request for togglePlansRequired received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity -> {
            // Get the current plans drawn
            Boolean currentPlansDrawn = rollMaintenanceActivity.getBuildingConsent().flatMap(BuildingConsent::getPlansObtained).orElse(Boolean.FALSE);
            // Get the current plans required
            Boolean currentPlansRequired = rollMaintenanceActivity.getBuildingConsent().flatMap(BuildingConsent::getPlansRequired).orElse(Boolean.FALSE);
            // Toggle the plans required
            Boolean savedPlansRequired = !currentPlansRequired;
            // But plans drawn and plans required cannot be ticked at the same time.
            Boolean savedPlansDrawn = savedPlansRequired ? Boolean.FALSE : currentPlansDrawn;

            Optional<BuildingConsent> savedBuildingConsent = rollMaintenanceActivity.getBuildingConsent()
                    .map(consent -> consent
                            .withPlansRequired(Optional.of(savedPlansRequired))
                            .withPlansObtained(Optional.of(savedPlansDrawn)));

            RollMaintenanceActivity savedRollMaintenanceActivity = rollMaintenanceActivity.withBuildingConsent(savedBuildingConsent);
            return rollMaintenanceService.saveRollMaintenanceActivity(id).invoke(savedRollMaintenanceActivity)
                    .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                    .thenApply(Results::ok);
        }).exceptionally(this::handleException);
    }

    /**
     * Toggle plans drawn of a roll maintenance activity and save the roll maintenance activity. Plans drawn and plans required can not be ticked at the same time.
     *
     * @return A JSON representation of the saved result upon updating the plans drawn flag, either the updated Roll Maintenance Activity or a list of errors.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> togglePlansDrawn(String id) {
        logger.info("Request for togglePlansDrawn received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity -> {
            // Get the current plans drawn
            Boolean currentPlansDrawn = rollMaintenanceActivity.getBuildingConsent().flatMap(BuildingConsent::getPlansObtained).orElse(Boolean.FALSE);
            // Get the current plans required
            Boolean currentPlansRequired = rollMaintenanceActivity.getBuildingConsent().flatMap(BuildingConsent::getPlansRequired).orElse(Boolean.FALSE);
            // Toggle the plans required
            Boolean savedPlansDrawn = !currentPlansDrawn;
            // But plans drawn and plans required cannot be ticked at the same time.
            Boolean savedPlansRequired = savedPlansDrawn ? Boolean.FALSE : currentPlansRequired;

            Optional<BuildingConsent> savedBuildingConsent = rollMaintenanceActivity.getBuildingConsent()
                    .map(consent -> consent
                            .withPlansObtained(Optional.of(savedPlansDrawn))
                            .withPlansRequired(Optional.of(savedPlansRequired)));
            RollMaintenanceActivity savedRollMaintenanceActivity = rollMaintenanceActivity.withBuildingConsent(savedBuildingConsent);
            return rollMaintenanceService.saveRollMaintenanceActivity(id).invoke(savedRollMaintenanceActivity)
                    .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                    .thenApply(Results::ok);
        }).exceptionally(this::handleException);
    }

    /**
     * Toggle plans status of a roll maintenance activity and save the roll maintenance activity. Only one plan status can be selected at the same time.
     *
     * @return A JSON representation of the saved result upon updating the plans status, either the updated Roll Maintenance Activity or a list of errors.
     */
    public CompletionStage<Result> togglePlanStatus(String id, String planStatus) {
        logger.info("Request for togglePlanStatus received. Roll Maintenance Activity id = {}", id);
        return rollMaintenanceService.getRollMaintenanceActivity(id).invoke().thenCompose(rollMaintenanceActivity -> {

            Boolean setPlansDrawn = planStatus.equals("plansAvailable") ? Boolean.TRUE : Boolean.FALSE;

            Boolean setPlansRequired = planStatus.equals("plansNeeded") ? Boolean.TRUE : Boolean.FALSE;

            Boolean setPlansNotRequired = planStatus.equals("plansNotRequired") ? Boolean.TRUE : Boolean.FALSE;

            Boolean setPlansRequestedWithTa = planStatus.equals("plansRequestedWithTa") ? Boolean.TRUE : Boolean.FALSE;

            Boolean setPlansUnavailable = planStatus.equals("plansUnavailable") ? Boolean.TRUE : Boolean.FALSE;


            Optional<BuildingConsent> savedBuildingConsent = rollMaintenanceActivity.getBuildingConsent()
                    .map(consent -> consent
                            .withPlansObtained(Optional.of(setPlansDrawn))
                            .withPlansRequired(Optional.of(setPlansRequired))
                            .withPlansUnavailable(Optional.of(setPlansUnavailable))
                            .withPlansNotRequired(Optional.of(setPlansNotRequired))
                            .withPlansRequestedWithTa(Optional.of(setPlansRequestedWithTa)));
            RollMaintenanceActivity savedRollMaintenanceActivity = rollMaintenanceActivity.withBuildingConsent(savedBuildingConsent);
            return rollMaintenanceService.saveRollMaintenanceActivity(id).invoke(savedRollMaintenanceActivity)
                    .thenCompose(rollMaintenanceApiService::injectCurrentPropertyToRollMaintenanceActivity)
                    .thenApply(Results::ok);
        }).exceptionally(this::handleException);
    }

    /**
     * Search for Roll Maintenance Activities meeting the criteria supplied in the request body.
     *
     * @return A JSON representation of the Roll Maintenance Activity search results.
     */
    public CompletionStage<Result> searchRollMaintenanceActivities() {
        logger.info("Request for searchRollMaintenanceActivities received. criteria:\n {}", request().body().asJson());
        RollMaintenanceSearchCriteria criteria = Json.fromJson(request().body().asJson(), RollMaintenanceSearchCriteria.class);
        return rollMaintenanceService.searchRollMaintenanceActivities().invoke(criteria).thenCompose(result -> {
            // Need to retrieve the current property data for each search result
            List<Integer> qpids = result.getRollMaintenanceActivities().stream()
                    .map(activity -> activity.getRatingUnit().getQpid().orElseThrow(IllegalStateException::new))
                    .collect(Collectors.toList());
            //Check apportionment and worksheet for qpids
            Map<Integer, JsonNode> hasValidRaAndRw = new HashMap<>();
            if(!qpids.isEmpty()) {
                propertyApiService.validateConsentApportionment(qpids.stream().map(String::valueOf).collect(Collectors.joining(","))).thenCompose(response -> {
                    try {
                        for (JsonNode res : Json.toJson(objectMapper.readTree(response.getResponseBody()))) {
                            if (res.has("qupid")) {
                                hasValidRaAndRw.put(res.get("qupid").asInt(), res);
                            }
                        }
                        return CompletableFuture.completedFuture(hasValidRaAndRw);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }).join();
            }
            PropertySearchCriteria propertyCriteria = PropertySearchCriteria.emptyCriteria().withQupids(Optional.of(qpids)).withMax(Optional.of(qpids.size()));
            return propertyService.searchPropertiesWithDistance().invoke(propertyCriteria).thenCompose(propertySearchResult -> {
                // Key the returned properties by QPID
                Map<Integer, Property> properties = propertySearchResult.getResultList().stream()
                        .collect(Collectors.toMap(item -> item.getProperty().getQupid().orElseThrow(IllegalStateException::new),
                                PropertySearchResults.PropertySearchResult::getProperty));
                // Need to retrieve the current rating valuation data for each search result
                Map<String, CompletableFuture<List<RatingValuation>>> ratingValuations = result.getRollMaintenanceActivities().stream()
                        .collect(Collectors.toMap(
                                RollMaintenanceActivity::getId, activity ->
                                rollMaintenanceService.searchRatingValuations(Optional.empty(), Optional.of(activity.getId())).invoke().toCompletableFuture()
                                )
                        );
                Map<String, CompletableFuture<List<RatingValuation>>> objectionRVs = result.getRollMaintenanceActivities().stream()
                        .filter(rma -> rma.getActiveObjectionIds().isPresent() && !rma.getActiveObjectionIds().get().isEmpty())
                        .collect(Collectors.toMap(
                                RollMaintenanceActivity::getId, activity ->
                                rollMaintenanceService.searchRatingValuations(Optional.empty(), Optional.of(activity.getActiveObjectionIds().get().get(0))).invoke().toCompletableFuture()
                                )
                        );

                Collection<CompletableFuture<List<RatingValuation>>> futures = new ArrayList<>(ratingValuations.values());
                futures.addAll(objectionRVs.values());

                return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).thenApply(v2 -> {
                    // Build the response
                    JsonNode response = Json.toJson(result);
                    for (JsonNode activity : response.get("rollMaintenanceActivities")) {
                        // Add the current property address and TA to each search result
                        Integer qpid = activity.get("ratingUnit").get("qpid").asInt();
                        Property currentProperty = properties.get(qpid);
                        ObjectNode currentPropertyNode = Json.newObject();
                        currentPropertyNode.put("id", currentProperty.getId().orElseThrow(IllegalStateException::new).toString());
                        currentPropertyNode.set("address", Json.toJson(currentProperty.getAddress()));
                        currentPropertyNode.set("territorialAuthority", Json.toJson(currentProperty.getTerritorialAuthority()));
                        currentProperty.getApportionment().ifPresent(apportionment -> 
                            currentPropertyNode.set("apportionmentCode", Json.toJson(apportionment.getCode()))
                        );
                        currentPropertyNode.set("assessmentStatus", Json.toJson(currentProperty.getStatus().get().getCode()));
                        currentPropertyNode.set("category", Json.toJson(currentProperty.getCategory()));
                        ((ObjectNode) activity).set("property", currentPropertyNode);
                        if(hasValidRaAndRw.containsKey(qpid)) {
                            currentPropertyNode.set("hasSameRollAndAssessment", Json.toJson(hasValidRaAndRw.get(qpid).get("hasSameRollAndAssessment").asBoolean()));
                            currentPropertyNode.set("hasRatingRW", Json.toJson(hasValidRaAndRw.get(qpid).get("hasRatingRW").asBoolean()));
                        } else {
                            currentPropertyNode.set("hasSameRollAndAssessment", Json.toJson(false));
                            currentPropertyNode.set("hasRatingRW", Json.toJson(false));
                        }
                        // Add rating valuation details to each search result
                        // TODO - Currently assumes only one valuation per activity will exist - may need to handle multiple in the future.
                        String activityId = activity.get("id").asText();
                        Optional<RatingValuation> ratingValuation = Optional.ofNullable(ratingValuations.get(activityId))
                                .flatMap(rv -> rv.join().stream().findFirst());
                        ratingValuation.ifPresent(rv -> {
                            ObjectNode ratingValuationNode = Json.newObject();
                            ratingValuationNode.put("id", rv.getId().toString());
                            ratingValuationNode.put("status", rv.getStatus().toString());
                            ratingValuationNode.put("qpid", Json.toJson(rv.getRatingUnit().getQpid()));
                            ((ObjectNode) activity).set("ratingValuation", ratingValuationNode);
                        });

                        if (!activity.get("activeObjectionIds").isNull()) {
                            ArrayNode activeObjectionIds = (ArrayNode) activity.get("activeObjectionIds");
                            if (activeObjectionIds != null && !activeObjectionIds.isEmpty()) {
                                Optional<RatingValuation> objectionRv = Optional.ofNullable(objectionRVs.get(activityId))
                                        .flatMap(rv -> rv.join().stream().findFirst());
                                objectionRv.ifPresent(rv -> {
                                    ((ObjectNode) activity).put("activeObjectionUUID", rv.getId().toString());
                                });
                            };
                        }
                    }
                    return ok(response);
                });
            });
        }).exceptionally(this::handleException);
    }

    /**
     * Generate a Consent Inspection Report for the list of Roll Maintenance Activity IDs supplied in the
     * request body.
     *
     * @return The S3 download URL of the generated Consent Inspection Report PDF.
     */
    @Restrict({@Group("INTERNAL_USER")})
    @SuppressWarnings("unchecked")
    public CompletionStage<Result> generateInspectionReport() {
        logger.info("Request for generateInspectionReport received.");
        JsonNode body = request().body().asJson();
        List<String> activityIds = Json.fromJson(body.get("activityIds"), List.class);
        String reportType = Json.fromJson(body.get("reportType"), String.class);
        // TODO should be configurable or with some research should be able to determine scheme but appears this doesn't worlk so hard coding.
        // String monarchScheme = request().secure() ? "https://" : "http://";
        String monarchScheme = "https://";
        String monarchBaseUrl = monarchScheme + request().host();
        String qivsBaseUrl = config.getString("qivs.url");

        return rollMaintenanceService.getRollMaintenanceActivities().invoke(activityIds).thenCompose(activities -> {

            // Need to retrieve the current property data for each activity
            List<Integer> qpids = activities.stream()
                    .map(activity -> activity.getRatingUnit().getQpid().orElseThrow(IllegalStateException::new))
                    .collect(Collectors.toList());

            // Retrieve Rating Valuation for activities
            Map<String, CompletableFuture<Stream<RatingValuation>>> ratingValuationMap = activities.stream()
                    .collect(Collectors.toMap(RollMaintenanceActivity::getId, activity -> rollMaintenanceService.searchRatingValuations(activity.getRatingUnit().getQpid(), Optional.of(activity.getId()))
                            .invoke().toCompletableFuture().thenApply(valuations -> valuations.stream())));

            return CompletableFuture.allOf(ratingValuationMap.values().toArray(new CompletableFuture[0])).thenCompose(v -> {

                PropertySearchCriteria propertyCriteria = PropertySearchCriteria.emptyCriteria().withQupids(Optional.of(qpids)).withMax(Optional.of(qpids.size()));

                return propertyService.searchPropertiesWithDistance().invoke(propertyCriteria).thenCompose(result -> {
                    // Key the returned properties by QPID
                    Map<Integer, Property> properties = result.getResultList().stream()
                            .collect(Collectors.toMap(item -> item.getProperty().getQupid().orElseThrow(IllegalStateException::new),
                                    PropertySearchResults.PropertySearchResult::getProperty));

                    // Build the report data JSON
                    ObjectNode activitiesObject = new ObjectNode(JsonNodeFactory.instance);
                    ArrayNode activitiesList = new ArrayNode(JsonNodeFactory.instance);
                    activitiesObject.set("rollMaintenanceActivities", activitiesList);

                    ArrayNode taList = new ArrayNode(JsonNodeFactory.instance);
                    activitiesObject.set("territorialAuthorities", taList);

                    //Build the header data JSON for objections inspection report
                    ArrayNode headersList = new ArrayNode(JsonNodeFactory.instance);
                    activitiesObject.set("rollMaintenanceHeaders", headersList);

                    ArrayNode qpidList = new ArrayNode(JsonNodeFactory.instance);
                    // sort by valref
                    rollMaintenanceApiService.sortByValRef(activities);

                    activities.forEach(activity -> {
                        ObjectNode activityJson = (ObjectNode) Json.toJson(activity);
                        ObjectNode ratingUnitJson = (ObjectNode) activityJson.get("ratingUnit");
                        Integer qpid = ratingUnitJson.get("qpid").asInt();
                        Property property = properties.get(qpid);
                        property.getFullAddress().ifPresent(address -> ratingUnitJson.put("fullAddress", address));
                        property.getCurrentValuation().getCapitalValue().ifPresent(cv -> ratingUnitJson.put("capitalValue", cv));
                        property.getCurrentValuation().getLandValue().ifPresent(lv -> ratingUnitJson.put("landValue", lv));
                        property.getCurrentValuation().getValueOfImprovements().ifPresent(vi -> ratingUnitJson.put("valueOfImprovements", vi));
                        ratingUnitJson.put("propertyUrl", monarchBaseUrl + "/property/property/Search?qupid=" + qpid);
                        // Add property details for each property into the JSON node
                        // TODO - Currently assumes only one valuation per activity will exist - may need to handle multiple in the future.
                        String activityId = activityJson.get("id").asText();

                        Optional<ObjectNode> existingActivity = StreamSupport.stream(activitiesList.spliterator(), false)
                                .map(node -> (ObjectNode) node)
                                .filter(node -> node.get("ratingUnit").get("qpid").asInt() == qpid)
                                .findFirst();

                        Optional<RatingValuation> ratingValuation = Optional.ofNullable(ratingValuationMap.get(activityId))
                                .flatMap(rv -> rv.join().findFirst());
                        //Property details are retrieved based on there rating valuation status or if there is no valuation
                        ratingValuation.ifPresent(rv -> {
                            if (rv.getStatus().equals(RatingValuationStatus.IN_PROGRESS) && rv.getPropertyDetailId().isPresent()) {
                                try {
                                    PropertyDetail inProgressDetails = propertyService.getPropertyDetail(rv.getPropertyDetailId().get()).invoke().toCompletableFuture().get();
                                    PropertyDetailDvrSnapshot derivedDetails = propertyService.generatePropertyDetailDvrSnapshotFromId(rv.getPropertyDetailId().get()).invoke().toCompletableFuture().get();
                                    ratingUnitJson.putPOJO("propertyDetail", inProgressDetails);
                                    ratingUnitJson.putPOJO("propertyDetailDvrSnapshot", derivedDetails);
                                } catch (InterruptedException | ExecutionException e) {
                                    Thread.currentThread().interrupt();
                                    throw new RuntimeException(e);
                                }
                            } else if (rv.getStatus().equals(RatingValuationStatus.IN_PROGRESS) && !rv.getPropertyDetailId().isPresent()) {
                                try {
                                    UUID id = property.getId().get();
                                    PropertyDetail pendingPropertyDetails = propertyService.getPendingPropertyDetail(id).invoke().toCompletableFuture().get();
                                    PropertyDetailDvrSnapshot derivedDetails = propertyService.generatePropertyDetailDvrSnapshotFromId(pendingPropertyDetails.getId()).invoke().toCompletableFuture().get();
                                    ratingUnitJson.putPOJO("propertyDetail", pendingPropertyDetails);
                                    ratingUnitJson.putPOJO("propertyDetailDvrSnapshot", derivedDetails);
                                } catch (InterruptedException | ExecutionException e) {
                                    Thread.currentThread().interrupt();
                                    throw new RuntimeException(e);
                                }
                            } else {
                                property.getId().ifPresent(id -> {
                                    getCurrentPropertyDetailIntoJson(id, PropertyDetailInferenceLevel.FULL, ratingUnitJson);
                                });
                            }
                        });
                        if (!ratingValuation.isPresent()) {
                            property.getId().ifPresent(id -> {
                                getCurrentPropertyDetailIntoJson(id, PropertyDetailInferenceLevel.FULL, ratingUnitJson);
                            });
                        }
                        //Populate Header Json
                        if (reportType.equals("OB")) {
                            ObjectNode headerJson = new ObjectNode(JsonNodeFactory.instance);
                            headerJson.put("objectionId", activityJson.get("id").asText().split("-")[1]);
                            headerJson.put("qpid", qpid);
                            headerJson.put("valuationReference", ratingUnitJson.get("valuationReference").asText());
                            headerJson.put("category", Json.toJson(ratingUnitJson.get("propertyDetail")).get("category").get("code").asText());
                            headerJson.put("address", property.getFullAddress().get().split(",")[0]);
                            headerJson.put("taCode", property.getTerritorialAuthority().get().getCode());
                            JsonNode objJson = apiPicklistService.searchObjection(Json.newObject().set("objectionIds", Json.newArray().add(activityJson.get("id").asText().split("-")[1])),
                                    Json.stringify(Json.newObject().put("total", true))).thenCompose(response -> {
                                try {
                                    return CompletableFuture.completedFuture(objectMapper.readTree(response.getResponseBody()).get("objections").get(0));
                                } catch (IOException e) {
                                    throw new RuntimeException(e);
                                }
                            }).join();
                            ObjectNode objNode = (ObjectNode) objJson;
                            objNode.put("documentationUrl", qivsBaseUrl + "/default.asp?Plans/ObjectionDocuments/ObjectionDocuments.aspx?qpid=" + qpid + "&ObjID=" + objJson.get("objectionId"));
                            objNode.put("dateReceived", ZonedDateTime.parse(objJson.get("dateReceived").asText()).withZoneSameInstant(ZoneId.of("Pacific/Auckland")).format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
                            objJson = objNode;
                            headerJson.put("ObjectorName", String.join(" ", getValueOrDefault(objJson.get("firstName")), getValueOrDefault(objJson.get("lastName"))));
                            headersList.add(headerJson);
                            // check if property already added then add only Objection details;
                            if (existingActivity.isPresent()) {
                                ((ArrayNode) existingActivity.get().get("objectionList")).add(objJson);
                            } else {
                                ArrayNode objectionList = new ArrayNode(JsonNodeFactory.instance);
                                activityJson.set("objectionList", objectionList);
                                objectionList.add(objJson);
                            }
                            if (!existingActivity.isPresent() && activity.getHasActiveConsent().orElse(false)) {
                                RollMaintenanceSearchCriteria searchCriteria = RollMaintenanceSearchCriteria.emptyCriteria().withQpid(Optional.ofNullable(qpid));
                                RollMaintenanceSearchResult bcResult = rollMaintenanceService.searchRollMaintenanceActivities().invoke(searchCriteria).toCompletableFuture().join();
                                List<RollMaintenanceActivity> activeConsents = bcResult.getRollMaintenanceActivities()
                                        .stream()
                                        .filter(rm -> rm.getBuildingConsent()
                                                .map(bc -> !bc.getStatus().get().getDescription().equals("ACTIONED"))
                                                .orElse(true))
                                        .collect(Collectors.toList());
                                ArrayNode activeBuildingConsents = new ArrayNode(JsonNodeFactory.instance);
                                activityJson.set("activeConsents", activeBuildingConsents);
                                for (RollMaintenanceActivity rollMaintenanceActivity : activeConsents) {
                                    ObjectNode consentJson = (ObjectNode) Json.toJson(rollMaintenanceActivity);
                                    consentJson.put("plansUrl", qivsBaseUrl + "/default.asp?Plans/FloorPlans/FloorPlans.aspx?Qpid=" + qpid);
                                    consentJson.put("address", property.getFullAddress().get());
                                    consentJson.put("totalFloorArea", Json.toJson(ratingUnitJson.get("propertyDetail")).get("summary").get("totalFloorArea").asText());
                                    activeBuildingConsents.add(consentJson);
                                }
                            }
                        } else {
                            ObjectNode headerJson = new ObjectNode(JsonNodeFactory.instance);
                            headerJson.put("qpid", qpid);
                            headerJson.put("valuationReference", ratingUnitJson.get("valuationReference").asText());
                            headerJson.put("status", activityJson.get("status").get("description").asText());
                            headerJson.put("location", property.getFullAddress().get().split(",")[0]);
                            headerJson.put("bcNumber", activityJson.get("buildingConsent").get("consentNumber").asText());
                            headerJson.put("description", activityJson.get("buildingConsent").get("description").asText());
                            headerJson.put("issued", activityJson.get("buildingConsent").get("consentDate").asText());
                            headerJson.put("cost", activityJson.get("buildingConsent").get("cost").asDouble());
                            headerJson.put("floorArea", Math.round(activityJson.get("buildingConsent").get("totalFloorArea").asDouble()));
                            headerJson.put("plansDrawn", activityJson.get("buildingConsent").get("plansObtained").asBoolean());
                            headersList.add(headerJson);
                        }
                        try {
                            // Get Action records
                            Response actionRecord = objectionApiService.getActionRecordsForQpid(qpid).toCompletableFuture().join();
                            activityJson.putPOJO("action_record", objectMapper.readTree(actionRecord.getResponseBody()));

                            // Get Improvement Summary
                            Response improvementRes = propertyApiService.getImprovementSummary(qpid).toCompletableFuture().join();
                            activityJson.putPOJO("improvementSummary", objectMapper.readTree(improvementRes.getResponseBody()).get("improvementSummary"));
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }

                        // check if property already added then add only consent details;
                        if (existingActivity.isPresent()) {
                            ObjectNode consent = (ObjectNode) activityJson.get("buildingConsent");
                            consent.set("notes", activityJson.get("notes"));
                            consent.set("estimatedInspectionReadyDate", activityJson.get("estimatedInspectionReadyDate"));
                            consent.put("plansUrl", qivsBaseUrl + "/default.asp?Plans/FloorPlans/FloorPlans.aspx?Qpid=" + qpid);
                            ((ArrayNode) existingActivity.get().get("buildingConsentList")).add(consent);
                        } else {
                            ArrayNode buildingConsentList = new ArrayNode(JsonNodeFactory.instance);
                            activityJson.set("buildingConsentList", buildingConsentList);
                            ObjectNode consent = (ObjectNode) activityJson.get("buildingConsent");
                            consent.set("notes", activityJson.get("notes"));
                            consent.set("estimatedInspectionReadyDate", activityJson.get("estimatedInspectionReadyDate"));
                            consent.put("plansUrl", qivsBaseUrl + "/default.asp?Plans/FloorPlans/FloorPlans.aspx?Qpid=" + qpid);
                            buildingConsentList.add(consent);
                            activitiesList.add(activityJson);

                        }

                        String taToAdd = property.getTerritorialAuthority().get().getName();
                        if (!StreamSupport.stream(taList.spliterator(), false).anyMatch(node -> node.asText().equals(taToAdd))) taList.add(taToAdd);
                        if (!StreamSupport.stream(qpidList.spliterator(), false)
                                .anyMatch(node -> node.asInt() == qpid)) {
                            qpidList.add(qpid);
                        }
                    });

                    // Get Location view
                    Response mapRes = googleMapsApiService.getLocationsPhotoForQpids(qpidList.toString()).toCompletableFuture().join();
                    activitiesObject.put("googleMapImage", mapRes.getResponseBody());
                    // Generate the report
                    return reportingService.generateReport(reportType.equals("BC") ? ReportType.INSPECTION_REPORT : ReportType.OBJECTION_INSPECTION_REPORT)
                            .invoke(Json.stringify(activitiesObject)).thenApply(url -> ok(Json.toJson(url.getValue())));
                });
            }).exceptionally(this::handleException);
        });
    }

    /**
     * Generates Property Information for the specified PropertyId based on Inference level and
     * stores in the specified JSON node.
     *
     * @param propertyId     The Property ID retrieved from property service
     * @param inferenceLevel The Inference level of property detail to be generated
     * @param jsonNode       The JSON node that will store the property details from property service
     */
    @Restrict({@Group("INTERNAL_USER")})
    public void getCurrentPropertyDetailIntoJson(UUID propertyId, PropertyDetailInferenceLevel inferenceLevel, ObjectNode jsonNode) {
        try {
            PropertyDetail completedDetails = propertyService.getCurrentPropertyDetail(propertyId, inferenceLevel).invoke().toCompletableFuture().get();
            PropertyDetailDvrSnapshot derivedDetails = propertyService.generatePropertyDetailDvrSnapshot().invoke(completedDetails).toCompletableFuture().get();
            jsonNode.putPOJO("propertyDetail", completedDetails);
            jsonNode.putPOJO("propertyDetailDvrSnapshot", derivedDetails);
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
        }
    }

    private static String getValueOrDefault(JsonNode node) {
        return (node != null && !node.isNull()) ? node.asText() : "";
    }
}
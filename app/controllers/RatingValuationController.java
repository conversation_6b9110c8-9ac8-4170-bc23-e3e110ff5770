package controllers;

import be.objectify.deadbolt.java.actions.Group;
import be.objectify.deadbolt.java.actions.Restrict;
import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lightbend.lagom.javadsl.api.transport.NotFound;
import com.qv.property.api.PropertyService;
import com.qv.property.api.model.Property;
import com.qv.property.api.model.Valuation;
import com.qv.property.api.model.property.PropertyDetail;
import com.qv.property.api.model.search.PropertySearchCriteria;
import com.qv.property.api.model.search.PropertySearchResults;
import com.qv.rollmaintenance.api.RollMaintenanceService;
import com.qv.rollmaintenance.api.ValidationResult;
import com.qv.rollmaintenance.api.model.RollMaintenanceActivity;
import com.qv.rollmaintenance.api.model.valuation.RatingValuation;
import com.qv.rollmaintenance.api.model.valuation.RatingValuationStatus;
import com.qv.rollmaintenance.api.model.valuation.RatingValue;
import models.CompleteRatingValuationResult;
import models.User;
import play.libs.Json;
import play.mvc.Result;
import play.mvc.Results;
import security.AuthSupport;
import services.*;

import javax.inject.Inject;
import play.shaded.ahc.org.asynchttpclient.Response;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import util.LambdaHandler;
import util.LoggerWrapper;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

/**
 * This controller provides front-end access to rating valuation functionality.
 */
@SubjectPresent
public class RatingValuationController extends AbstractController {
    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");

    /**
     * Provides access to the Roll Maintenance Service.
     */
    private final RollMaintenanceService rollMaintenanceService;

    /**
     * Provides access to the Property Service.
     */
    private final PropertyService propertyService;

    /**
     * Roll Maintenance Helper methods
     */
    private final RollMaintenanceApiService rollMaintenanceApiService;

    private final SalesApiService salesApiService;

    /**
     * Provides access to the Auth Support for getting the current user
     */
    private final AuthSupport authSupport;

    private final PropertyApiService propertyApiService;

    private final RuralWorksheetApiService  ruralWorksheetApiService;

    private final ConsentApiService consentApiService;

    private final ObjectMapper objectMapper;

    @Inject
    public RatingValuationController(final RollMaintenanceService rollMaintenanceService, PropertyService propertyService,
                                     RollMaintenanceApiService rollMaintenanceApiService, SalesApiService salesApiService,
                                     AuthSupport authSupport, PropertyApiService propertyApiService,
                                     RuralWorksheetApiService ruralWorksheetApiService,
                                     ObjectMapper objectMapper,
                                     ConsentApiService consentApiService
    ) {
        this.rollMaintenanceService = rollMaintenanceService;
        this.propertyService = propertyService;
        this.rollMaintenanceApiService = rollMaintenanceApiService;
        this.salesApiService = salesApiService;
        this.authSupport = authSupport;
        this.propertyApiService = propertyApiService;
        this.ruralWorksheetApiService = ruralWorksheetApiService;
        this.objectMapper = objectMapper;
        this.consentApiService = consentApiService;
    }

    /**
     * Generate a new Rating Valuation for the Roll Maintenance Activities supplied in the request body.
     *
     * @return A JSON representation of the new rating valuation.
     */
    @Restrict({@Group("INTERNAL_USER")})
    @SuppressWarnings("unchecked")
    public CompletionStage<Result> generateRatingValuation() {
        List<String> activityIds = Json.fromJson(request().body().asJson(), List.class);
        logger.info("Request for generateRatingValuation received. activityIds = {}", activityIds);
        return rollMaintenanceService.generateRatingValuation().invoke(activityIds)
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Retrieve a single Rating Valuation.
     *
     * @param id The Rating Valuation ID.
     * @return A JSON representation of the Rating Valuation.
     */
    public CompletionStage<Result> getRatingValuation(UUID id) {
        logger.info("Request for getRatingValuation received. id = {}", id);
        return rollMaintenanceService.getRatingValuation(id).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    public Boolean areValuesEqual(Optional<BigDecimal> value1, Optional<BigDecimal> value2) {
        return value1.isPresent() && value2.isPresent() && value1.get().compareTo(value2.get()) == 0;
    }

    /**
     * Check the Rating Valuations latest revision values and update and save if out of date.
     *
     * @param ratingValuation The Rating Valuation.
     * @param property The Property information.
     * @return The updated Rating Valuation.
     */
    public Optional<RatingValuation> checkValuationRevisionValues(Optional<Property> property, Optional<RatingValuation> ratingValuation, String activityId){
        if (activityId.startsWith("OB-")) {
            return ratingValuation;
        }
        if (ratingValuation.isPresent()) {
            List<String> rollMaintenanceActivityIds = ratingValuation.get().getRollMaintenanceActivityIds();
            boolean hasObjection = rollMaintenanceActivityIds.stream().anyMatch(id -> id.startsWith("OB-"));
          
            if (hasObjection) {
                return ratingValuation;
            }
          }
        Optional<Valuation> propertyValuation = property.isPresent() ? Optional.of(property.get().getRevisedValuation()) : Optional.empty();
        Optional<RatingValue> ratingValue = ratingValuation.get().getOriginalRevisionValue().isPresent() ? ratingValuation.get().getOriginalRevisionValue() : Optional.empty();

        if(propertyValuation.isPresent() && ratingValue.isPresent()){
            Boolean cvEqual = areValuesEqual(propertyValuation.get().getCapitalValue(), ratingValue.get().getCapitalValue());
            Boolean lvEqual = areValuesEqual(propertyValuation.get().getLandValue(), ratingValue.get().getLandValue());
            if(!cvEqual || !lvEqual) {
                RatingValue updatedRevisionValue = new RatingValue(propertyValuation.get().getCapitalValue(), propertyValuation.get().getLandValue());
                Optional<RatingValuation> updatedValuation = Optional.of(ratingValuation.get().withOriginalRevisionValue(Optional.of(updatedRevisionValue)));
                RatingValuation savedValuation = rollMaintenanceService.saveRatingValuation(updatedValuation.get().getId()).invoke(updatedValuation.get()).toCompletableFuture().join().getValue();
                if (savedValuation != null) {
                    return Optional.of(savedValuation);
                }
                logger.error("ERR-RV-001","Failed to save valuation when running checkValuationRevisionValues for valuation: {} rv rating unit qpid: {}",ratingValuation.get().getId(), ratingValuation.get().getRatingUnit().getQpid());
                return ratingValuation;
            }
        }
        return ratingValuation;
    }

    /**
     * Loop through linked activities for a given rating valuation and try to unlink any of status: cancelled
     * @param rv the RatingValuation.
     * @return Optional.Empty() if rv ends up having no activities (deleted) OR
     * RatingValuation if has linked activities with none being of status cancelled.
     */
    public Optional<RatingValuation> unlinkCancelledActivities(RatingValuation rv) {
        logger.info("unlinkCancelledActivities with rv: {}", rv.getId());
        List<RollMaintenanceActivity> activities = rollMaintenanceService.getRollMaintenanceActivities()
                .invoke(rv.getRollMaintenanceActivityIds()).toCompletableFuture().join();
        List<RollMaintenanceActivity> cancelledActivities = activities.stream().filter(activity -> activity.getStatus().get()
                .getCode().equals("CANCELED")).collect(Collectors.toList());
        //RV with no activities OR RV activities all have status cancelled. Invalid. Delete.
        if (activities.size() == 0 || activities.size() == cancelledActivities.size()) {
            boolean deleted = rollMaintenanceService.deleteInactiveValuation(rv.getId(), activities.get(0).getId())
                    .invoke().toCompletableFuture().join();
            if (!deleted) throw new RuntimeException("Unsuccessful deletion of rv: " + rv.getId());
            return Optional.empty();
        }
        if (cancelledActivities.size() == 0) return Optional.of(rv);
        //unlink all cancelled activities from rv
        List<CompletableFuture<ValidationResult<RatingValuation>>> streamResult = activities.stream()
                .map(activity -> rollMaintenanceService.unlinkRollMaintenanceActivity(rv.getId(), activity.getId()).invoke()
                        .toCompletableFuture()).collect(Collectors.toList());

        return CompletableFuture.allOf(streamResult.toArray(new CompletableFuture[streamResult.size()])).thenApply(v -> {
            List<ValidationResult<RatingValuation>> allResults = streamResult.stream().map(CompletableFuture::join)
                    .collect(Collectors.toList());
            List<RatingValuation> ratingValuationResponses = new ArrayList<>();
            List<ValidationResult.ValidationError> errors = new ArrayList<>();

            allResults.forEach(result -> {
                if (!result.isSuccess() && result.hasErrors()) {
                    errors.addAll(result.getErrors());
                } else if (result.isSuccess() && result.getValue() != null) {
                    ratingValuationResponses.add(result.getValue());
                } else {
                    logger.error("ERR-RV-002","Invalid response unlinking activity from rv: {}", rv.getId());
                    throw new RuntimeException("Invalid response calling unlinkRollMaintenanceActivity");
                }
            });
            if (!errors.isEmpty()) {
                logger.error("ERR-RV-003","Error unlinking activity from rv: {}", rv.getId());
                throw new RuntimeException("Error unlinking activity");
            }
            return ratingValuationResponses.get(ratingValuationResponses.size() - 1);
        }).thenApply(Optional::of).join();
    }

    /**
     * Retrieve the current 'IN PROGRESS' Rating Valuation for a single Roll Maintenance Activity, optionally generating one
     * if it doesn't already exist.
     *
     * @param rollMaintenanceActivityId The Roll Maintenance Activity ID.
     * @param generate                  Indicates whether a Rating Valuation should be generated if it does not already exist.
     * @return A JSON representation of the Roll Maintenance Activity's current 'in progress' rating valuation, or a
     * 404 error if one could not be found and generate is false
     */
    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getInProgressValuationForActivity(String rollMaintenanceActivityId, Boolean generate) {
        // TODO This needs refactoring, logic should be in the roll maintenance service
        logger.info("Request for getInProgressValuationForActivity received. Roll Maintenance Activity id = {}", rollMaintenanceActivityId);
        // Attempt to find and return any existing 'in progress' valuation
        return rollMaintenanceService.searchRatingValuations(Optional.empty(), Optional.of(rollMaintenanceActivityId)).invoke().thenCompose(ratingValuations -> {
                    Optional<RatingValuation> existingValuation = filterRatingValuation(rollMaintenanceActivityId, ratingValuations);
                    return existingValuation.map(ratingValuation -> returnRatingValuation(rollMaintenanceActivityId, ratingValuation)).orElseGet(() -> rollMaintenanceService.getRollMaintenanceActivity(rollMaintenanceActivityId).invoke().thenCompose(rollMaintenanceActivity ->
                            // Attempt to find and return any existing in progress valuation on a property
                            rollMaintenanceService.searchRatingValuations(rollMaintenanceActivity.getRatingUnit().getQpid(), Optional.empty()).invoke().thenCompose(valuations -> {
                                Optional<RatingValuation> existingPropertyValuation = filterRatingValuation(rollMaintenanceActivityId, valuations);
                                if (existingPropertyValuation.isPresent()) {
                                    return returnRatingValuation(rollMaintenanceActivityId, existingPropertyValuation.get());
                                }
                                if (generate) {
                                    CompletableFuture<RatingValuation> ratingValuation = rollMaintenanceService.generateRatingValuation().invoke(Collections.singletonList(rollMaintenanceActivityId)).exceptionally(error -> {
                                                if (error.getCause().getMessage().contains("landUse.landZone")) {
                                                    logger.error("ERR-RV-013","Missing Land Zone Code from Monarch Database for rollMaintenanceActivityId: " + rollMaintenanceActivityId);
                                                    throw new IllegalStateException("Missing Land Zone Code from Monarch Database for rollMaintenanceActivityId: " + rollMaintenanceActivityId);
                                                } else {
                                                    throw new RuntimeException(error);
                                                }
                                            })
                                            .thenApply(result -> {
                                                if (result.isSuccess() && result.getValue() != null) {
                                                    Integer qpid = result.getValue().getRatingUnit().getQpid();
                                                    propertyApiService.getMasterDetails(qpid).thenCompose(response -> {
                                                        try {
                                                            JsonNode propertyInfo = objectMapper.readTree(response.getResponseBody()).get("propertyInfo");
                                                            if (propertyInfo.get("hasCommercialWorksheet").asBoolean()) {
                                                                return ruralWorksheetApiService.deleteCommercialWorksheet(qpid);
                                                            }
                                                            if (propertyInfo.get("hasRuralWorksheet").asBoolean()) {
                                                                return ruralWorksheetApiService.deleteWorksheet(qpid);
                                                            }
                                                        } catch (IOException e) {
                                                            throw new RuntimeException(e);
                                                        }
                                                        return null;
                                                    });
                                                    return result.getValue();
                                                } else {
                                                    throw new RuntimeException("Could not generate Rating Valuation: " + Json.stringify(Json.toJson(result)));
                                                }
                                            })
                                            .toCompletableFuture();
                                    if (rollMaintenanceActivityId.startsWith("BC-")) {
                                        try {
                                            Optional<String> notes = getNotesForActivity(rollMaintenanceActivityId);
                                            if (notes.isPresent() && notes.get().length() > 0) {
                                                Boolean requestPlans = false;
                                                requireMoreInformation(ratingValuation.get().getId(), notes.get(), requestPlans).toCompletableFuture().join();
                                            }
                                        } catch (Exception error) {
                                            logger.error("ERR-RV-011","Error getting notes for activity " + rollMaintenanceActivityId, error);
                                            throw new RuntimeException(error);
                                        }
                                    }
                                    return ratingValuation;
                                } else {
                                    throw new NotFound("No IN_PROGRESS Rating Valuation found for Roll Maintenance Activity " + rollMaintenanceActivityId);
                                }
                            })
                    ).toCompletableFuture());
                })
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(ex -> {
                    if (ex.getCause() instanceof IllegalStateException) {
                        return forbidden(ex.getMessage());
                    }
                    return handleException(ex);
                });
    }

    public Optional<String> getNotesForActivity(String rollMaintenanceActivityId) throws IOException {
        Response notesResponse = salesApiService.getNotesForActivity(rollMaintenanceActivityId).toCompletableFuture().join();
        String jsonPayload = notesResponse.getResponseBody();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonPayload);
        JsonNode notes = rootNode.get("notes");
        if (notes != null && notes.isTextual()) {
            return Optional.of(notes.asText());
        }
        else {
            return Optional.empty();
        }
    }

    private Optional<RatingValuation> filterRatingValuation(String rollMaintenanceActivityId, List<RatingValuation> valuations) {
        Optional<RatingValuation> existingPropertyValuation = valuations.stream().filter(rv ->
            rv.getStatus().equals(RatingValuationStatus.IN_PROGRESS)
        ).findAny();
        if (
                existingPropertyValuation.isPresent()
                && rollMaintenanceActivityId.startsWith("OB-")
                && existingPropertyValuation.get().getRollMaintenanceActivityIds().stream().noneMatch(id -> id.startsWith("OB-"))
        ) {
            Boolean deleted = rollMaintenanceService
                .deleteRatingValuationAndGenerate(existingPropertyValuation.get().getRollMaintenanceActivityIds().get(0))
                .invoke().toCompletableFuture().join();
            if (deleted) {
                existingPropertyValuation = Optional.empty();
            }
        }
        if (existingPropertyValuation.isPresent()) {
            existingPropertyValuation = unlinkCancelledActivities(existingPropertyValuation.get());
        }
        return existingPropertyValuation;
    }

    private CompletableFuture<RatingValuation> returnRatingValuation(String rollMaintenanceActivityId, RatingValuation existingValuation) {
        PropertySearchResults propertyResult = propertyService.searchPropertiesWithDistance()
                .invoke(PropertySearchCriteria.emptyCriteria().withQupid(Optional.of(existingValuation.getRatingUnit().getQpid())))
                .toCompletableFuture().join();
        Optional<Property> property = propertyResult.getTotalResults() > 0 ? Optional.of(propertyResult.getResultList().get(0).getProperty()) : Optional.empty();

        Integer qpid = existingValuation.getRatingUnit().getQpid();
        propertyApiService.getMasterDetails(qpid).thenCompose(response -> {
            try {
                JsonNode propertyInfo = objectMapper.readTree(response.getResponseBody()).get("propertyInfo");
                if (propertyInfo.get("hasCommercialWorksheet").asBoolean()) {
                    return ruralWorksheetApiService.deleteCommercialWorksheet(qpid);
                }
                if (propertyInfo.get("hasRuralWorksheet").asBoolean()) {
                    return ruralWorksheetApiService.deleteWorksheet(qpid);
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return null;
        });

        Optional<RatingValuation> updatedValuation = checkValuationRevisionValues(property, Optional.of(existingValuation), rollMaintenanceActivityId);
        return updatedValuation.map(CompletableFuture::completedFuture).get();
    }

    /**
     * Retrieve the latest COMPLETE rating valuation for the given roll maintenance activity id
     *
     * @param rollMaintenanceActivityId The Roll Maintenance Activity ID.
     * @return A JSON representation of the Roll Maintenance Activity's current 'COMPLETE' rating valuation, or a 404 error if one could not be found
     */
    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getCompleteValuationForActivity(String rollMaintenanceActivityId) {
        logger.info("Request for getCompleteValuationForActivity received. Roll Maintenance Activity id = {}", rollMaintenanceActivityId);
        Optional<User> currentUser = authSupport.currentUser(ctx());

        return rollMaintenanceService.searchRatingValuations(Optional.empty(), Optional.of(rollMaintenanceActivityId)).invoke()
                .thenApply(ratingValuations -> ratingValuations.stream()
                        .filter(ratingValuation -> ratingValuation.getStatus().equals(RatingValuationStatus.COMPLETE))
                        // Use Effective date as comparator,
                        .max(Comparator.comparing(rv -> rv.getEffectiveDate().orElse(null), Comparator.nullsFirst(Comparator.naturalOrder()))))
                .thenCompose(latestCompleteValuation -> rollMaintenanceApiService.ifAllowed(currentUser, latestCompleteValuation))
                .thenApply(x -> x.orElseThrow(() -> new NotFound("No COMPLETE Rating Valuation found for Roll Maintenance Activity " + rollMaintenanceActivityId)))
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Saves the Rating Valuation supplied in the request body.
     *
     * @return A JSON representation of the saved Rating Valuation.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> saveRatingValuation() {
        RatingValuation ratingValuation = Json.fromJson(request().body().asJson(), RatingValuation.class);
        logger.info("Request for saveRatingValuation received. id = {}",  ratingValuation.getId());
        return rollMaintenanceService.saveRatingValuation(ratingValuation.getId()).invoke(ratingValuation)
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Link Roll Maintenance Activity to Rating Valuation
     *
     * @param id                        UUID of Rating Valuation
     * @param rollMaintenanceActivityId ID of Roll Maintenance Activity
     * @return A JSON representation of the of the Rating Valuation that is saved upon linking a Roll Maintenance Activity
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> linkRollMaintenanceActivity(UUID id, String rollMaintenanceActivityId) {
        logger.info("Request for linkRollMaintenanceActivity received. RollMaintenanceActivityId = {}, Rating Valuation = {}", rollMaintenanceActivityId, id);
        return rollMaintenanceService.linkRollMaintenanceActivity(id, rollMaintenanceActivityId).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Unlink Roll Maintenance Activity to Rating Valuation
     *
     * @param id                        UUID of Rating Valuation
     * @param rollMaintenanceActivityId ID of Roll Maintenance Activity
     * @return A JSON representation of the of the Rating Valuation that is saved upon un-linking a Roll Maintenance Activity
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> unlinkRollMaintenanceActivity(UUID id, String rollMaintenanceActivityId) {
        logger.info("Request for unlinkRollMaintenanceActivity received. RollMaintenanceActivityId = {}, Rating Valuation = {}", rollMaintenanceActivityId, id);
        return rollMaintenanceService.unlinkRollMaintenanceActivity(id, rollMaintenanceActivityId).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Retrieve the default comparable property search criteria for the supplied rating valuation.
     *
     * @param id The Rating Valuation ID.
     * @return A JSON representation of the property search criteria, suitable for use with the Property Service API.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> getDefaultComparablePropertySearchCriteria(UUID id) {
        logger.info("Request for getDefaultComparablePropertySearchCriteria received. id = {}", id);
        return rollMaintenanceService.getDefaultComparablePropertySearchCriteria(id).invoke()
                .thenApply(result -> ok(Json.parse(result)))
                .exceptionally(this::handleException);
    }

    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getRollMaintenanceActivities(UUID id) {
        logger.info("Request for getRollMaintenanceActivities received. Rating Valuation id = {}", id);
        Optional<User> currentUser = authSupport.currentUser(ctx());

        // get a list of all related roll maintenance activities
        return rollMaintenanceService.getRatingValuation(id).invoke()
                .thenCompose(
                        ratingValuation ->
                                rollMaintenanceService.getRollMaintenanceActivities().invoke(ratingValuation.getRollMaintenanceActivityIds())
                )
                .thenApply(result -> rollMaintenanceApiService.filterAllowed(currentUser, Optional.of(result)))
                .thenApply(result -> ok(Json.toJson(result)))
                .exceptionally(this::handleException);
    }

    /**
     * Complete setup for a rating valuation
     *
     * @param id The Rating Valuation ID
     * @return A JSON representation of the rating valuation or error
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> completeSetup(UUID id) {
        // TODO This should do both validate and complete setup.
        logger.info("Request for completeSetup received. id = {}", id);
        // ASSUMPTION: the draft is saved before complete setup is called
        return rollMaintenanceService.completeRatingValuationSetup(id).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }
    /**
     * validate rating valuation can be completed
     *
     * @param id The Rating Valuation ID
     * @return A JSON representation of the rating valuation which may contain errors or warnings.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> validateRatingValuationComplete(UUID id) {
        logger.info("Request for validateRatingValuationComplete received. id = {}", id);
        return rollMaintenanceService.validateRatingValuationComplete(id).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Delete a Rating Valuation
     *
     * @param rollMaintenanceActivityID
     * @return
     */
    public CompletionStage<Result> deleteRatingValuation(String rollMaintenanceActivityID){
        logger.info("Request for deleteRatingValuation received. Roll Maintenance Activity id = {}", rollMaintenanceActivityID);
        return rollMaintenanceService.deleteRatingValuationAndGenerate(rollMaintenanceActivityID).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Delete a inactive Rating Valuation and generates a new one
     *
     * @param ratingValId The inactive rating valuation id to be deleted
     * @param rollMaintenanceActivityId The roll maintenance activity ID associated with the inactive rating valiaton
     * @return
     */
    public CompletionStage<Result> deleteInactiveRatingValuation(String ratingValId, String rollMaintenanceActivityId) {
        logger.info("Request for deleteInactiveRatingValuation received. Roll Maintenance Activity id = {}", rollMaintenanceActivityId);
        UUID inactiveRatingValuationId = UUID.fromString(ratingValId);
        boolean deleteInactive = rollMaintenanceService.deleteInactiveValuation(inactiveRatingValuationId, rollMaintenanceActivityId).invoke().toCompletableFuture().join();
        if (deleteInactive) {
            return this.getInProgressValuationForActivity(rollMaintenanceActivityId, true);
        }
        else {
            throw new RuntimeException("Couldn't delete inactive valuation: " + ratingValId + "For Roll maintenance activity: " + rollMaintenanceActivityId);
        }
    }


    /**
     * Perform auto-valuation for a rating valuation
     *
     * @param id The Rating Valuation ID
     * @return A JSON representation of the rating valuation or error
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> autoValue(UUID id) {
        logger.info("Request for autoValue received. id = {}", id);
        return rollMaintenanceService.autoValue(id).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Complete a rating valuation
     *
     * @param id             The Rating Valuation ID
     * @param ignoreWarnings If true, valuation completion will only be aborted on errors, not warnings.
     * @return A JSON representation of the rating valuation along with any associated completion errors/warnings
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> completeValuation(UUID id, boolean ignoreWarnings) {
        logger.info("Request for completeValuation received. id = {}", id);
        Optional<User> currentUser = authSupport.currentUser(ctx());
        return rollMaintenanceService.validateRatingValuationComplete(id).invoke().thenCompose(valuationResult -> {
            UUID propertyDetailId = valuationResult.getValue().getPropertyDetailId().orElseThrow(IllegalStateException::new);
            // TODO Move the QVNZ\ concat to the repository
            String ntUsername = "QVNZ\\".concat(currentUser.orElseThrow(IllegalAccessError::new).getName());
            return propertyService.validatePropertyDetailComplete(propertyDetailId).invoke().thenCompose(propertyDetailResult -> {
                // Merge the errors and warnings from both calls

                CompleteRatingValuationResult result = new CompleteRatingValuationResult(valuationResult, mapValidationResult(propertyDetailResult));

                if (!result.hasErrors() && (!result.hasWarnings() || ignoreWarnings)) {
                    // Complete the PropertyDetail
                    CompletableFuture<com.qv.property.api.Result<PropertyDetail>> pdCompletionFuture = propertyService.completePropertyDetail(propertyDetailId).invoke().toCompletableFuture();

                    return pdCompletionFuture.thenApply(pdCompletionResult -> {
                        if (pdCompletionResult.isSuccess()) {
                            // Complete the Rating Valuation Job
                            return rollMaintenanceService.completeRatingValuation(id, ntUsername, false)
                                    .invoke()
                                    .thenApply(result::withRatingValuationResult)
                                    .toCompletableFuture()
                                    .join();
                        } else {
                            ValidationResult<PropertyDetail> completionResult = new ValidationResult<>(
                                    pdCompletionResult.getValue().orElseThrow(IllegalStateException::new),
                                    false
                            );

                            completionResult.getErrors().addAll(mapErrors(pdCompletionResult.getErrors().orElse(Collections.emptyList())));
                            completionResult.getWarnings().addAll(mapErrors(pdCompletionResult.getWarnings().orElse(Collections.emptyList())));

                            return CompletableFuture.completedFuture(result.withPropertyDetailResult(completionResult));
                        }
                    });
                } else {
                    return CompletableFuture.completedFuture(result);
                }
            });
        })
        .thenApply(Json::toJson)
        .thenApply(Results::ok)
        .exceptionally(this::handleException);
    }

    private ValidationResult<PropertyDetail> mapValidationResult(com.qv.property.api.ValidationResult<PropertyDetail> propertyDetailResult) {
        ValidationResult<PropertyDetail> result = new ValidationResult<>(propertyDetailResult.getValue().orElseThrow(IllegalStateException::new), propertyDetailResult.isSuccess());

        List<ValidationResult.ValidationError> errors = mapPropertyErrors(propertyDetailResult.getErrors().orElse(Collections.emptyList()));
        List<ValidationResult.ValidationError> warnings = mapPropertyErrors(propertyDetailResult.getWarnings().orElse(Collections.emptyList()));

        result.getErrors().addAll(errors);
        result.getWarnings().addAll(warnings);
        return result;
    }

    /**
     * Set the needs more information flag on the primary Roll Maintenance Activity, append Notes if any provided,
     * and flag "Plans Required" on any linked roll maintenance activities that are NOT already "Plans Drawn"
     *
     * @return A consolidated JSON representation of the save results
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> requireMoreInformation(UUID id, String notes, Boolean requestPlans) {
        logger.info("Request for requireMoreInformation received. id = {} , requestPlans = {}", id, requestPlans);
        String formattedNote = this.today().format(DateTimeFormatter.ofPattern("dd/MM/YYYY"))
                + ((notes != null && notes.length() > 0) ? " - " + notes : "");

        // get a list of all related roll maintenance activities
        return rollMaintenanceService.getRatingValuation(id).invoke()
                .thenCompose(
                        ratingValuation -> rollMaintenanceService.getRollMaintenanceActivities().invoke(ratingValuation.getRollMaintenanceActivityIds())
                ).thenCompose(rollMaintenanceActivities -> {
                            // get the first ID
                            String primaryId = rollMaintenanceApiService.firstByCost(rollMaintenanceActivities)
                                    .map(x -> x.getId())
                                    .orElseThrow(() -> new RuntimeException("No activities are linked to this valuation."));

                            List<CompletableFuture<com.qv.rollmaintenance.api.Result<RollMaintenanceActivity>>> saveFutures = rollMaintenanceActivities.stream()
                                    // filter to get the set of activities we need to update
                                    .filter(// The primary one ...
                                            activity -> activity.getId() == primaryId ||
                                                    // or if plans are being requested then also any that are NOT already set to Plans Obtained
                                                    (requestPlans && activity.getBuildingConsent().isPresent() && !activity.getBuildingConsent().get().getPlansObtained().orElse(false))
                                    ).map(activity ->
                                            // Build save futures for each affected activity
                                            rollMaintenanceService.saveRollMaintenanceActivity(activity.getId())
                                                    .invoke((
                                                                    // If this is the primary then add the note and flag as needs more info
                                                                    activity.getId() == primaryId ?
                                                                            rollMaintenanceApiService.appendNote(activity, Optional.of(formattedNote))
                                                                                    .withNeedsMoreInformation(Optional.of(true))
                                                                            : activity
                                                            )// If requesting plans and this is NOT plans drawn then set plans required
                                                                    .withBuildingConsent(activity.getBuildingConsent()
                                                                            .map(x -> requestPlans && !x.getPlansObtained().orElse(false) ? x.withPlansRequired(Optional.of(true)).withPlansObtained(Optional.of(false)) : x)
                                                                    )
                                                    )
                                                    .toCompletableFuture()
                                    ).collect(Collectors.toList());

                            return CompletableFuture.allOf(saveFutures.toArray(new CompletableFuture[0])).thenApply(v -> {
                                return saveFutures.stream().map(CompletableFuture::join).collect(Collectors.toList());
                            });
                        }
                ).thenApply(saveResults -> {
                    return ok(Json.toJson(rollMaintenanceApiService.combineResults(saveResults)));
                })
                .exceptionally(this::handleException);
    }

    /**
     * Set the needs more information flag to FALSE for ALL related roll maintenance activities
     *
     * @return A consolidated JSON representation of the save results
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> informationProvided(UUID id) {
        logger.info("Request for informationProvided received. Rating Valuation id = {}", id);
        // get a list of all related roll maintenance activities
        return rollMaintenanceService.getRatingValuation(id).invoke()
                .thenCompose(
                        ratingValuation -> rollMaintenanceService.getRollMaintenanceActivities().invoke(ratingValuation.getRollMaintenanceActivityIds())
                ).thenCompose(rollMaintenanceActivities -> {
                            List<CompletableFuture<com.qv.rollmaintenance.api.Result<RollMaintenanceActivity>>> saveFutures = rollMaintenanceActivities.stream()
                                    // filter to get the set of activities we need to update
                                    .filter(x -> x.getNeedsMoreInformation().orElse(false))
                                    .map(x ->
                                            // Build save futures for each affected activity
                                            rollMaintenanceService.saveRollMaintenanceActivity(x.getId())
                                                    .invoke(x.withNeedsMoreInformation(Optional.of(false)))
                                                    .toCompletableFuture()
                                    ).collect(Collectors.toList());

                            // save them all
                            return CompletableFuture.allOf(saveFutures.toArray(new CompletableFuture[0])).thenApply(v -> {
                                return saveFutures.stream().map(CompletableFuture::join).collect(Collectors.toList());
                            });
                        }
                ).thenApply(saveResults -> {
                    return ok(Json.toJson(rollMaintenanceApiService.combineResults(saveResults)));
                })
                .exceptionally(this::handleException);
    }

    /**
     * Set the needs inspection flag on the primary related Roll Maintenance Activity to True, append Notes if any provided
     *
     * @return A JSON representation of the saved result
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> requireInspection(UUID id, String notes) {
        logger.info("Request for requireInspection received. Rating Valuation id = {}", id);
        String formattedNote = this.today().format(DateTimeFormatter.ofPattern("dd/MM/YYYY"))
                + " Needs Inspection"
                + ((notes != null && notes.length() > 0) ? " - " + notes : "");

        // get a list of all related roll maintenance activities
        return rollMaintenanceService.getRatingValuation(id).invoke()
                .thenCompose(ratingValuation -> rollMaintenanceService.getRollMaintenanceActivities().invoke(ratingValuation.getRollMaintenanceActivityIds()))
                .thenCompose(rollMaintenanceActivities -> {
                    // Get the roll maintenance activity with the highest cost - "Main" BC
                    RollMaintenanceActivity firstByCost = rollMaintenanceApiService.firstByCost(rollMaintenanceActivities).orElseThrow(IllegalStateException::new);
                    List<CompletableFuture<com.qv.rollmaintenance.api.Result<RollMaintenanceActivity>>> rollMaintenanceActivityFutures = rollMaintenanceActivities.stream().map(rollMaintenanceActivity -> {
                        RollMaintenanceActivity savedRollMaintenanceActivity = rollMaintenanceActivity.withNeedsInspection(Optional.of(Boolean.TRUE));
                        // Only add notes to the roll maintenance activity with the highest cost - "Main" BC
                        if (firstByCost.equals(rollMaintenanceActivity)) {
                            savedRollMaintenanceActivity = rollMaintenanceApiService.appendNote(savedRollMaintenanceActivity, Optional.of(formattedNote));
                        }
                        // Save the roll maintenance activity
                        return rollMaintenanceService.saveRollMaintenanceActivity(savedRollMaintenanceActivity.getId()).invoke(savedRollMaintenanceActivity).toCompletableFuture();
                    }).collect(Collectors.toList());

                    return CompletableFuture.allOf(rollMaintenanceActivityFutures.toArray(new CompletableFuture[0])).thenApply(v -> {
                        List<com.qv.rollmaintenance.api.Result<RollMaintenanceActivity>> results = rollMaintenanceActivityFutures.stream().map(rollMaintenanceActivityFuture -> rollMaintenanceActivityFuture.join()).collect(Collectors.toList());
                        // Return combined results
                        return rollMaintenanceApiService.combineResults(results);
                    });
                })
                .thenApply(combinedResults -> ok(Json.toJson(combinedResults)))
                .exceptionally(this::handleException);
    }


    /**
     * Set the needs inspection flag to FALSE for ALL related roll maintenance activities
     *
     * @return A consolidated JSON representation of the save results
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> inspected(UUID id) {
        logger.info("Request for inspected received. Rating Valuation id = {}", id);
        // get a list of all related roll maintenance activities
        return rollMaintenanceService.getRatingValuation(id).invoke()
                .thenCompose(
                        ratingValuation -> rollMaintenanceService.getRollMaintenanceActivities().invoke(ratingValuation.getRollMaintenanceActivityIds())
                ).thenCompose(rollMaintenanceActivities -> {
                            List<CompletableFuture<com.qv.rollmaintenance.api.Result<RollMaintenanceActivity>>> saveFutures = rollMaintenanceActivities.stream()
                                    // filter to get the set of activities we need to update
                                    .filter(x -> x.getNeedsInspection().orElse(false))
                                    .map(x ->
                                            // Build save futures for each affected activity
                                            rollMaintenanceService.saveRollMaintenanceActivity(x.getId())
                                                    .invoke(x.withNeedsInspection(Optional.of(false)))
                                                    .toCompletableFuture()
                                    ).collect(Collectors.toList());

                            // save them all
                            return CompletableFuture.allOf(saveFutures.toArray(new CompletableFuture[0])).thenApply(v -> {
                                return saveFutures.stream().map(CompletableFuture::join).collect(Collectors.toList());
                            });
                        }
                ).thenApply(saveResults -> {
                    return ok(Json.toJson(rollMaintenanceApiService.combineResults(saveResults)));
                })
                .exceptionally(this::handleException);
    }

    /**
     *
     * @param id
     * @param userName
     * @return The Saved Consent Job Update Information.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> saveConsentJobUpdateInformation(UUID id, String userName, Integer qpid){
        logger.info("Request for saveConsentJobUpdateInformation received. Job id = {}, User = {}", id, userName);
        return rollMaintenanceService.saveConsentJobUpdateInformation(id,userName, qpid).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     *
     * @param id
     * @return The Consent Job Update Information for the provided id.
     */
    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> getConsentJobUpdateInformation(UUID id){
        logger.info("Request for getConsentJobUpdateInformation received. Job id = {}", id);
        return rollMaintenanceService.getConsentJobUpdateInformation(id).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    public CompletionStage<Result> validate() {
        logger.info("Request for /rating-valuation/validate");
        JsonNode json = request().body().asJson();
        return consentApiService.validate(json)
                .thenApply(LambdaHandler::responsePassThrough);
    }

    public CompletionStage<Result> validateOnSave() {
        logger.info("Request for /rating-valuation/validateOnSave");

        JsonNode json = request().body().asJson();
        return consentApiService.validateOnSave(json)
                .thenApply(LambdaHandler::responsePassThrough);
    }

    /**
     * This method maps errors returned by the Property Service to the same type as errors returned by the Rating Valuation Service
     * so that error lists from the two services can be merged.
     *
     * @param errors The Property Service errors
     * @return The errors converted to the Rating Valuation Service error type.
     */
    private List<ValidationResult.ValidationError> mapErrors(List<com.qv.property.api.Result.Error> errors) {
        return errors.stream().map(error -> new ValidationResult.ValidationError(
                        new ValidationResult.Field(error.getField(), error.getField(), ""),
                        error.getMessage(),
                        0
                ))
                .collect(Collectors.toList());    }


    private List<ValidationResult.ValidationError> mapPropertyErrors(List<com.qv.property.api.ValidationResult.ValidationError> errors) {
        return errors.stream().map(error -> new ValidationResult.ValidationError(
                        new ValidationResult.Field(error.getField().getPath(), error.getField().getDisplayName(), ""),
                        error.getMessage(),
                        0
                ))
                .collect(Collectors.toList());
    }

}

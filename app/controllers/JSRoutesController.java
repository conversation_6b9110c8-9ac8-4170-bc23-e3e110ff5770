package controllers;

import play.mvc.Controller;
import play.mvc.Result;
import play.routing.JavaScriptReverseRouter;

/**
 * Created by Vishal.Arora on 7/27/2017.
 */

public class JSRoutesController extends Controller {

    public Result javascriptRoutes() {

        return ok(
                JavaScriptReverseRouter.create("jsRoutes",
                        routes.javascript.ReferenceData.displaySalesGroups(),
                        routes.javascript.Application.fetchTerritorialAuthorities(),
                        routes.javascript.Application.fetchUserData(),
                        routes.javascript.Application.version(),
                        routes.javascript.Application.versions(),
                        routes.javascript.SalesSearch.displaySalesSearchResult(),
                        routes.javascript.SalesAnalysis.displaySalesAnalysis(),
                        routes.javascript.SalesAnalysis.updateSalesAnalysis(),
                        routes.javascript.SalesAnalysis.deleteSalesAnalysis(),
                        routes.javascript.SalesAnalysis.checkAnalysis(),
                        routes.javascript.SalesAnalysis.migrateResidentialToRuralAnalysis(),
                        routes.javascript.TADashboardController.loadTADashboard(),
                        routes.javascript.TADashboardController.displayGraphs(),
                        routes.javascript.PropertyController.displayPropertySearchResult(),
                        routes.javascript.PropertyController.searchProperties(),
                        routes.javascript.PropertyController.displayTypeAheadResult(),
                        routes.javascript.PropertyController.getPropertyUUID(),
                        routes.javascript.ExportProperties.exportProperties(),
                        routes.javascript.ExportProperties.getExportPropertiesCriteria(),
                        routes.javascript.PropertyMasterData.getProperty(),
                        routes.javascript.PropertyMasterData.getStatsSummary(),
                        routes.javascript.PropertyMasterData.getTASummary(),
                        routes.javascript.PropertyMasterData.getPropertyDescription(),
                        routes.javascript.PropertyMasterData.getPropertyInfo(),
                        routes.javascript.PropertyMasterData.getPropertyInfoFull(),
                        routes.javascript.PropertyMasterData.updateOwners(),
                        routes.javascript.PropertyMasterData.updatePropertyInfoField(),
                        routes.javascript.MediaController.saveMedia(),
                        routes.javascript.MediaController.updateMedia(),
                        routes.javascript.MediaController.getMediaByOwner(),
                        routes.javascript.MediaController.getMedia(),
                        routes.javascript.MediaController.generateMediaEntryID(),
                        routes.javascript.MediaController.getPrimaryMediaByOwner(),
                        routes.javascript.MediaController.linkMedia(),
                        routes.javascript.MediaController.unLinkMedia(),
                        routes.javascript.MediaController.reLinkPhoto(),
                        routes.javascript.Subdivision.getSubdivisionData(),
                        routes.javascript.ReferenceData.displayClassification(),
                        routes.javascript.ReferenceData.displayClassifications(),
                        routes.javascript.ReferenceData.searchClassifications(),
                        routes.javascript.ReferenceData.displayValuers(),
                        routes.javascript.ReferenceData.displayRatingValuers(),
                        routes.javascript.ReferenceData.displayCountersigners(),
                        routes.javascript.ReferenceData.displayUsers(),
                        routes.javascript.ReferenceData.displayOffices(),
                        routes.javascript.ReferenceData.getRoles(),
                        routes.javascript.ReferenceData.saveUser(),
                        routes.javascript.ReferenceData.displayUserByUsername(),
                        routes.javascript.HomeValuation.saveHomeValuation(),
                        routes.javascript.HomeValuation.displayMarketValuationJobSearchResult(),
                        routes.javascript.HomeValuation.saveHomeValuation(),
                        routes.javascript.HomeValuation.getHomeValuation(),
                        routes.javascript.HomeValuation.getHomeValuationByProperty(),
                        routes.javascript.HomeValuation.getHomeValuationPropertyDescription(),
                        routes.javascript.HomeValuation.generateValuationConclusion(),
                        routes.javascript.HomeValuation.getValuationJobUpdateInformation(),
                        routes.javascript.HomeValuation.saveValuationJobUpdateInformation(),
                        routes.javascript.HomeValuation.getFields(),
                        routes.javascript.ComparableSales.displayComparableProperties(),
                        routes.javascript.ComparableSales.exportComparableSale(),
                        routes.javascript.ComparableSales.getComparableSaleBySaleId(),
                        routes.javascript.ValuationReport.generateValuationReport(),
                        routes.javascript.ValuationReport.getPeerReviewPDF(),
                        routes.javascript.ClassificationController.displayClassificationTypeAheadResult(),
                        routes.javascript.ClassificationController.saveClassification(),
                        routes.javascript.ClassificationController.fetchClassifications(),
                        routes.javascript.ClassificationController.getClassifications(),
                        routes.javascript.EmailController.sendEmailForPeerReview(),
                        routes.javascript.EmailController.sendEmailOnReviewApproved(),
                        routes.javascript.EmailController.sendEmailOnReviewRejected(),
                        routes.javascript.ClassificationController.getAllCategories(),
                        routes.javascript.ClassificationController.getSpecialCategories(),
                        routes.javascript.SalesSearch.getSalesByQupid(),
                        routes.javascript.PropertyMasterData.hasQvProperty(),
                        routes.javascript.PropertyMasterData.saveQvProperty(),
                        routes.javascript.PropertyMasterData.saveQvPropertyZoneInfo(),
                        routes.javascript.PropertyMasterData.getQvProperty(),
                        routes.javascript.PropertyMasterData.getQvPropertyZoneInfo(),
                        routes.javascript.PropertyMasterData.getLinzTitles(),
                        routes.javascript.PropertyMasterData.getMaoriLandLumpSum(),
                        routes.javascript.PropertyMasterData.getCurrentRollLandValueIndex(),
                        routes.javascript.PropertyMasterData.getPropertyInfo(),
                        routes.javascript.PropertyMasterData.updatePropertyInfoField(),
                        routes.javascript.PropertyMasterData.getPickListValues(),
                        routes.javascript.PropertyMasterData.getApportionmentDetails(),
                        routes.javascript.PropertyMasterData.getDetailsForQpids(),
                        routes.javascript.PropertyMasterData.getDvrDataFromQivs(),
                        routes.javascript.PropertyMasterData.getAttachment(),
                        routes.javascript.PropertyMasterData.saveHazardNotes(),
                        routes.javascript.RollMaintenanceController.searchRollMaintenanceActivities(),
                        routes.javascript.RollMaintenanceController.getRollMaintenanceActivity(),
                        routes.javascript.RollMaintenanceController.getRollMaintenanceActivitiesByIds(),
                        routes.javascript.RollMaintenanceController.saveRollMaintenanceActivity(),
                        routes.javascript.RollMaintenanceController.requireMoreInformation(),
                        routes.javascript.RollMaintenanceController.informationProvided(),
                        routes.javascript.RollMaintenanceController.requireInspection(),
                        routes.javascript.RollMaintenanceController.inspected(),
                        routes.javascript.RollMaintenanceController.constructionComplete(),
                        routes.javascript.RollMaintenanceController.constructionInProgress(),
                        routes.javascript.RollMaintenanceController.complianceCertificateIssued(),
                        routes.javascript.RollMaintenanceController.noComplianceCertificate(),
                        routes.javascript.RollMaintenanceController.togglePlansRequired(),
                        routes.javascript.RollMaintenanceController.togglePlanStatus(),
                        routes.javascript.RollMaintenanceController.togglePlansDrawn(),
                        routes.javascript.RollMaintenanceController.generateInspectionReport(),
                        routes.javascript.RatingValuationController.generateRatingValuation(),
                        routes.javascript.RatingValuationController.getRatingValuation(),
                        routes.javascript.RatingValuationController.getInProgressValuationForActivity(),
                        routes.javascript.RatingValuationController.deleteRatingValuation(),
                        routes.javascript.RatingValuationController.deleteInactiveRatingValuation(),
                        routes.javascript.RatingValuationController.getCompleteValuationForActivity(),
                        routes.javascript.RatingValuationController.saveRatingValuation(),
                        routes.javascript.RatingValuationController.getDefaultComparablePropertySearchCriteria(),
                        routes.javascript.RatingValuationController.linkRollMaintenanceActivity(),
                        routes.javascript.RatingValuationController.unlinkRollMaintenanceActivity(),
                        routes.javascript.RatingValuationController.completeSetup(),
                        routes.javascript.RatingValuationController.autoValue(),
                        routes.javascript.RatingValuationController.validateRatingValuationComplete(),
                        routes.javascript.RatingValuationController.completeValuation(),
                        routes.javascript.RatingValuationController.requireMoreInformation(),
                        routes.javascript.RatingValuationController.informationProvided(),
                        routes.javascript.RatingValuationController.requireInspection(),
                        routes.javascript.RatingValuationController.inspected(),
                        routes.javascript.RatingValuationController.getRollMaintenanceActivities(),
                        routes.javascript.RatingValuationController.getConsentJobUpdateInformation(),
                        routes.javascript.RatingValuationController.saveConsentJobUpdateInformation(),
                        routes.javascript.RatingValuationController.validate(),
                        routes.javascript.RatingValuationController.validateOnSave(),
                        routes.javascript.PropertyDetailController.getPendingPropertyDetail(),
                        routes.javascript.PropertyDetailController.savePendingPropertyDetailForRVJob(),
                        routes.javascript.PropertyDetailController.getCurrentPropertyDetail(),
                        routes.javascript.PropertyDetailController.editCurrentPropertyDetail(),
                        routes.javascript.PropertyDetailController.getPropertyDetail(),
                        routes.javascript.PropertyDetailController.savePendingPropertyDetail(),
                        routes.javascript.PropertyDetailController.saveCurrentPropertyDetail(),
                        routes.javascript.PropertyDetailController.generateBuildingsFromPropertyDetail(),
                        routes.javascript.PropertyDetailController.generatePropertyDetailNewDwellingInformation(),
                        routes.javascript.PropertyDetailController.validatePendingPropertyDetail(),
                        routes.javascript.PropertyDetailController.validatePropertyDetailComplete(),
                        routes.javascript.PropertyDetailController.getPropertyDetailUpdateInformation(),
                        routes.javascript.PropertyDetailController.generatePropertyDetailDescription(),
                        routes.javascript.PropertyDetailController.validateOnSave(),
                        routes.javascript.ReportUploadController.createUploadedReportEntry(),
                        routes.javascript.ReportUploadController.completeReportUpload(),
                        routes.javascript.RtvController.getRuralIndexMainIndex(),
                        routes.javascript.RtvController.validateRuralIndexMainIndex(),
                        routes.javascript.RtvController.updateRuralIndexMainIndex(),
                        routes.javascript.RtvController.getRuralIndexSecondaryRefinements(),
                        routes.javascript.RtvController.validateRuralIndexSecondaryRefinements(),
                        routes.javascript.RtvController.updateRuralIndexSecondaryRefinements(),
                        routes.javascript.RtvController.getRuralPropertyRtvValues(),
                        routes.javascript.RtvController.getRuralPropertyRtvIndices(),
                        routes.javascript.RtvController.getRuralIndexAssessments(),
                        routes.javascript.RtvController.getRuralIndexBaseCategories(),
                        routes.javascript.RuralSaleAnalysisController.hasRuralSaleAnalysis(),
                        routes.javascript.RuralSaleAnalysisController.getRuralSaleAnalysis(),
                        routes.javascript.RuralSaleAnalysisController.createRuralSaleAnalysis(),
                        routes.javascript.RuralSaleAnalysisController.updateRuralSaleAnalysis(),
                        routes.javascript.RuralSaleAnalysisController.getRuralClassifications(),
                        routes.javascript.RuralSaleAnalysisController.refreshRuralSaleAnalysis(),
                        routes.javascript.RuralSaleAnalysisController.displayRuralSaleAnalysis(),
                        routes.javascript.RuralSaleAnalysisController.generateRuralSalesPdf(),
                        routes.javascript.RuralSaleAnalysisController.deleteRuralSaleAnalysis(),
                        routes.javascript.RuralWorksheetController.getCurrentWorksheet(),
                        routes.javascript.RuralWorksheetController.getRevisionWorksheet(),
                        routes.javascript.RuralWorksheetController.getRtvWorksheet(),
                        routes.javascript.RuralWorksheetController.createRevisionWorksheet(),
                        routes.javascript.RuralWorksheetController.updateWorksheet(),
                        routes.javascript.RuralWorksheetController.deleteWorksheet(),
                        routes.javascript.RuralWorksheetController.updateAssessment(),
                        routes.javascript.RuralWorksheetController.recalculateLandMatrix(),
                        routes.javascript.RuralWorksheetController.getWorksheetLandMatrix(),
                        routes.javascript.RuralWorksheetController.getCommercialWorksheet(),
                        routes.javascript.RuralWorksheetController.getCommercialRevisionWorksheet(),
                        routes.javascript.RuralWorksheetController.createCommercialRevisionWorksheet(),
                        routes.javascript.RuralWorksheetController.deleteCommercialWorksheet(),
                        routes.javascript.RuralWorksheetController.getRfcClassifications(),
                        routes.javascript.RuralWorksheetController.updateCommercialWorksheet(),
                        routes.javascript.RuralWorksheetController.validateCommercialWorksheet(),
                        routes.javascript.RuralWorksheetController.generateCommercialWorksheetPdf(),
                        routes.javascript.RuralWorksheetController.generateRuralWorksheetPdf(),
                        routes.javascript.ReasonForChangeController.getExistingReasonForChange(),
                        routes.javascript.ReasonForChangeController.addReasonForChange(),
                        routes.javascript.SalesProcessingController.getSale(),
                        routes.javascript.SalesProcessingController.getDVR(),
                        routes.javascript.SalesProcessingController.updateDVR(),
                        routes.javascript.SalesProcessingController.saveSale(),
                        routes.javascript.SalesProcessingController.getSaleClassification(),
                        routes.javascript.SalesProcessingController.getTALandUseZone(),
                        routes.javascript.SalesProcessingController.getSalePortalSalePdfUrl(),
                        routes.javascript.SalesProcessingController.getTitles(),
                        routes.javascript.SalesProcessingController.relinkSaleRfs(),
                        routes.javascript.SalesProcessingController.deleteSale(),
                        routes.javascript.SalesProcessingController.validateSale(),
                        routes.javascript.SalesProcessingController.addSaleInspectionConsent(),
                        routes.javascript.SalesProcessingController.getChattelParameter(),
                        routes.javascript.SalesProcessingController.getLinzSaleWarnings(),
                        routes.javascript.SalesProcessingController.searchUnlinkedNotices(),
                        routes.javascript.SalesProcessingController.viewSalesNotice(),
                        routes.javascript.SalesProcessingController.linkNotice(),
                        routes.javascript.PropertySraDetailController.getSraOutputCode(),
                        routes.javascript.PropertySraDetailController.getSraReasonSource(),
                        routes.javascript.PropertySraDetailController.getSraAuthorityClasses(),
                        routes.javascript.PropertySraDetailController.getCurrentSraValuation(),
                        routes.javascript.PropertySraDetailController.updateSraValuation(),
                        routes.javascript.QVMapsController.getPropertyDetailsForLatLng(),
                        routes.javascript.QVMapsController.getLayerDataForLatLng(),
                        routes.javascript.QVMapsController.getPropertyDetailsForBndryPoints(),
                        routes.javascript.QVMapsController.getPropertyDetailsForQpids(),
                        routes.javascript.QVMapsController.uploadSiteplanImage(),
                        routes.javascript.QVMapsController.getPropertySaleInfoFromLatLng(),
                        routes.javascript.QVMapsController.getPickListValues(),
                        routes.javascript.QVMapsController.getRollsListForTA(),
                        routes.javascript.QVMapsController.getWMSLayer(),
                        routes.javascript.LinzSearchController.getLinzLandDistrict(),
                        routes.javascript.LinzSearchController.getLinzParcelType(),
                        routes.javascript.LinzSearchController.getLinzPlanType(),
                        routes.javascript.LinzSearchController.getLinzAutoSuggestLegalDescription(),
                        routes.javascript.LinzSearchController.getLinzByCertificate(),
                        routes.javascript.LinzSearchController.getLinzByParcelID(),
                        routes.javascript.LinzSearchController.getLinzByLegalDescription(),
                        routes.javascript.LinzSearchController.getLinzByLegalDescriptionAutoSuggest(),
                        routes.javascript.LinzSearchController.getLinzByOwner(),
                        routes.javascript.LinzSearchController.getUserGroupId(),
                        routes.javascript.FloorPlanToolController.getFloorPlans(),
                        routes.javascript.FloorPlanToolController.getFloorPlanImage(),
                        routes.javascript.FloorPlanToolController.saveFloorPlans(),
                        routes.javascript.SalesSearchController.getSalesProcessingStatus(),
                        routes.javascript.SalesSearchController.getSalesProcessingSource(),
                        routes.javascript.SalesSearchController.getSearchSale(),
                        routes.javascript.ApiPicklistController.getPicklistValue(),
                        routes.javascript.ApiPicklistController.getSalesGroupsAndRolls(),
                        routes.javascript.ApiPicklistController.searchObjection(),
                        routes.javascript.ApiPicklistController.exportObjection(),
                        routes.javascript.ApiPicklistController.bulkAssignObjection(),
                        routes.javascript.ApiPicklistController.searchComparableSales(),
                        routes.javascript.ApiPicklistController.getComparableSales(),
                        routes.javascript.ApiPicklistController.addComparableSales(),
                        routes.javascript.ApiPicklistController.updateComparableSales(),
                        routes.javascript.ApiPicklistController.removeComparableSales(),
                        routes.javascript.ApiPicklistController.addObjectionJobReview(),
                        routes.javascript.ApiPicklistController.getObjectionJobReview(),
                        routes.javascript.ApiPicklistController.updateObjectionJobReview(),
                        routes.javascript.ApiPicklistController.completeObjectionJobValuation(),
                        routes.javascript.ApiPicklistController.refreshComparableSale(),
                        routes.javascript.ApiPicklistController.getObjectionContact(),
                        routes.javascript.ApiPicklistController.updateObjectionContact(),
                        routes.javascript.ApiPicklistController.addRatingValuation(),
                        routes.javascript.ApiPicklistController.validateObjectionJob(),
                        routes.javascript.ApiPicklistController.autoSelectComparableSales(),
                        routes.javascript.ConsentController.bulkAssignConsent(),
                        routes.javascript.ObjectionController.generateObjectionJobPDF(),
                        routes.javascript.ObjectionController.actionObjection(),
                        routes.javascript.ObjectionController.actionObjectionFromQivs(),
                        routes.javascript.ObjectionController.updateObjectionLink(),
                        routes.javascript.ObjectionController.reinstateObjectionJob(),
                        routes.javascript.ObjectionController.updateObjectionJobStatus(),
                        routes.javascript.ObjectionController.reinstateObjectionJobFromQivs(),
                        routes.javascript.ObjectionController.getActionRecordsForQpid(),
                        routes.javascript.StatsController.getWorkUnitValuerSummary(),
                        routes.javascript.StatsController.getWorkUnitValuerRating(),
                        routes.javascript.StatsController.getWorkUnitNotifications(),
                        routes.javascript.StatsController.getConsultancyValuerSummary(),
                        routes.javascript.StatsController.getConsultancyValuerHrs(),
                        routes.javascript.ReportGeneratorController.getReports(),
                        routes.javascript.ReportGeneratorController.createReportJob(),
                        routes.javascript.ReportGeneratorController.createAndUploadExternalReportJob(),
                        routes.javascript.ReportGeneratorController.createAndUploadExternalReportJobAuth(),
                        routes.javascript.ReportGeneratorController.updateReportJobStatus(),
                        routes.javascript.ReportGeneratorController.getReportJobFile(),
                        routes.javascript.ReportGeneratorController.getReportJobsPagination(),
                        routes.javascript.GoogleMapsController.getLocationsPhotoForQpids()
                ))
                .as("text/javascript");

    }
}

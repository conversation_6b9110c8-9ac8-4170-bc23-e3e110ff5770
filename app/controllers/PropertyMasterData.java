package controllers;


import be.objectify.deadbolt.java.actions.Group;
import be.objectify.deadbolt.java.actions.Restrict;
import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.qv.classification.api.Classification;
import com.qv.property.api.PropertyService;
import com.qv.property.api.model.Property;
import com.qv.property.api.model.QvNumberOfBathrooms;
import com.qv.property.api.model.qvproperty.PropertyZoneInformation;
import com.qv.property.api.model.qvproperty.QvPropertyDetails;
import com.qv.property.api.model.search.PropertySearchCriteria;
import com.qv.property.api.model.search.PropertySearchResult;
import com.qv.property.api.model.search.PropertySortField;
import com.qv.reporting.api.ReportingService;
import com.qv.stats.service.StatsService;
import com.qv.ta.service.TaService;
import com.typesafe.config.Config;
import models.QivsPropertyInfo;
import models.UpdatePropertyInfoFieldRequest;
import org.json.JSONObject;
import play.libs.Json;
import play.libs.concurrent.HttpExecutionContext;
import play.mvc.Controller;
import play.mvc.Result;
import play.shaded.ahc.org.asynchttpclient.AsyncHttpClient;
import play.shaded.ahc.org.asynchttpclient.DefaultAsyncHttpClient;
import play.shaded.ahc.org.asynchttpclient.Response;
import services.PropertyApiService;
import util.LambdaHandler;
import util.LoggerWrapper;
import util.PDFUtility;

import javax.inject.Inject;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

@SubjectPresent
public class PropertyMasterData extends Controller {

    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");

    private final TaService taService;
    private final StatsService statsService;
    private final PropertyService propertyService;
    private final ReportingService reportingService;
    private final MediaController mediaController;
    private final ObjectMapper objectMapper;
    private final Config config;
    private final PropertyApiService propertyApiService;

    @Inject
    public PropertyMasterData(final TaService taService,
                              final StatsService statsService,
                              final PropertyService propertyService,
                              final ReportingService reportingService,
                              final HttpExecutionContext ec,
                              final MediaController mediaController,
                              final ObjectMapper objectMapper,
                              final Config config,
                              final PropertyApiService propertyApiService) {
        this.taService = taService;
        this.statsService = statsService;
        this.propertyService = propertyService;
        this.reportingService = reportingService;
        this.mediaController = mediaController;
        this.objectMapper = objectMapper;
        this.config = config;
        this.propertyApiService = propertyApiService;
    }

    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getPropertyDescription() {
        logger.info("Request for getPropertyDescription received. request data {} ", request().body().asJson());
        Property property = Json.fromJson(request().body().asJson(), Property.class);
        return reportingService.generatePropertySummary().invoke(property).thenApply(summary -> {
                    String propertySummary = "";
                    if(summary != null && summary.getSummaryText() != null) {
                        propertySummary = summary.getSummaryText().replaceAll("[\\t\\n\\r]+", " ");
                    }
                    ObjectNode result = Json.newObject();
                    result.put("summaryText", propertySummary);
                    return ok(result);
                }
        ).exceptionally(ex -> {
            logger.error("ERR-WEB-003", "Exception while calling getPropertyDescription ", ex);
            throw new RuntimeException(ex);
        });
    }

    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getStatsSummary(Integer qupid){
        logger.info("Request for getStatsSummary received. request data {}", qupid);
        return CompletableFuture.supplyAsync(() -> statsService.getPropertySummary(qupid)).thenApply(summary -> ok(Json.toJson(summary))
        );
    }

    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getTASummary(Integer taNumber){
        logger.info("Request for getTASummary received. taNumber {}", taNumber);
        return CompletableFuture.supplyAsync(() -> taService.getTASummary(taNumber)).thenApply(summary -> ok(Json.toJson(summary)));
    }

    // TODO Refactor. This is not clean
    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getProperty(String id) {
        logger.info("Request for getProperty received.");
        return  (id.length() > 10)? getPropertyById(id) : getPropertyByQUPID(id);
    }

    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getQvProperty(String ownerId) {
        logger.info("Request for getQvProperty received. ownerId: {}", ownerId);
        return propertyService.getQvProperty(UUID.fromString(ownerId)).invoke().thenApply(result -> {
                    return ok(Json.toJson(result));
                }
        ).exceptionally(ex -> {
            if (ex.getMessage().contains("404")) {
                return ok(Json.toJson(Optional.empty()));
            }
            logger.error("ERR-PMD-012", "Exception while calling getQvProperty ", ex);
            throw new RuntimeException(ex);
        });
    }

    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> hasQvProperty(String ownerId) {
        logger.info("Request for hasQvProperty received. ownerId: {}", ownerId);
        return propertyService.hasQvPropertyDetails(UUID.fromString(ownerId)).invoke().thenApply(result -> {
                return ok(Json.toJson(result));
            }
        ).exceptionally(ex -> {
            logger.error("ERR-PMD-013", "Exception while calling hasQvProperty ", ex);
            throw new RuntimeException(ex);
        });
    }

    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> saveQvProperty() {
        logger.info("Request for saveQvProperty received. json Request: {}", request().body().asJson());
        QvPropertyDetails qvPropertyDetails = Json.fromJson(request().body().asJson(), QvPropertyDetails.class);
        qvPropertyDetails = qvPropertyDetails.withEffectiveDateOfCollection(LocalDate.now(ZoneId.of("Pacific/Auckland")));
        return propertyService.saveQvProperty().invoke(qvPropertyDetails).thenApply(result -> {
                    return ok(Json.toJson(result));
                }
        ).exceptionally(ex -> {
            logger.error("ERR-WEB-010", "Exception while calling saveQvProperty ", ex);
            throw new RuntimeException(ex);
        });
    }

    @Restrict({@Group("INTERNAL_USER")})
    public CompletionStage<Result> saveQvPropertyZoneInfo() {
        logger.info("Request for getLinzTitles received. json Request: {}", request().body().asJson());
        PropertyZoneInformation propertyZoneInformation = Json.fromJson(request().body().asJson(), PropertyZoneInformation.class);
        return propertyService.saveQvPropertyZoneInformation().invoke(propertyZoneInformation).thenApply(result -> {
                    return ok(Json.toJson(result));
                }
        ).exceptionally(ex -> {
            logger.error("ERR-PMD-001", "Exception while calling saveQvPropertyZoneInformation. ", ex);
            throw new RuntimeException(ex);
        });
    }

    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getQvPropertyZoneInfo(Integer qpid) {
        logger.info("Request for getQvPropertyZoneInfo received. qpid: {}", qpid);
        return propertyService.getQvPropertyZoneInformation(qpid).invoke().thenApply(result -> {
                    return ok(Json.toJson(result));
                }
        ).exceptionally(ex -> {
            logger.error("ERR-PMD-002", "Exception while calling getQvPropertyZoneInformation. ", ex);
            throw new RuntimeException(ex);
        });
    }

    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getLinzTitles(Integer qupid) {
        logger.info("Request for getLinzTitles received. qupid = {}", qupid);
        return CompletableFuture.supplyAsync(() -> statsService.getLinzTitleSummary(qupid)).thenApply(result -> {
                    return ok(Json.toJson(result));
                }
        ).exceptionally(ex -> {
            logger.error("ERR-PMD-003", "Exception while calling getLinzTitles ", ex);
            throw new RuntimeException(ex);
        });
    }
    /**
     *  Retrieves the Maori Land Lump Sums for the specified qupid.
     * @param qupid UUID of the property
     * @return The Maori Land Lump Sums for the specified qupid.
     */
    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getMaoriLandLumpSum(Integer qupid) {
        logger.info("Request for getMaoriLandLumpSum received. qupid = {}", qupid);
        return CompletableFuture.supplyAsync(() -> statsService.getMaoriLandLumpSum(qupid)).thenApply(result -> {
                return ok(Json.toJson(result));
                }
        ).exceptionally(ex -> {
            logger.error("ERR-PMD-004", "Exception while getting Maori Land Lump Sum ", ex);
            throw new RuntimeException(ex);
        });
    }

    /**
     *  Retrieves the Land Value Index for the current valuation for a roll.
     * @param id UUID of the property
     * @return The Land Value Index for the current valuation for a roll otherwise 1.
     */
    @Restrict({@Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ")})
    public CompletionStage<Result> getCurrentRollLandValueIndex(UUID id) {
        logger.info("Request for getCurrentRollLandValueIndex received. id = {}", id);
        // Retrieve the property
        return propertyService.getProperty(id).invoke().thenCompose(property -> {
            Integer taCode = property.getTerritorialAuthority().orElseThrow(IllegalStateException::new).getCode();
            // Get the TA Summary
            return CompletableFuture.supplyAsync(() -> statsService.getTASummary(taCode)).thenCompose(taSummary -> {
                String salesGroupCode = property.getSalesGroup().map(Classification::getCode).orElseThrow(IllegalAccessError::new);
                Integer rollNumber = property.getRollNumber().orElseThrow(IllegalStateException::new);
                LocalDate revisionDate = taSummary.getCurrentRevisionDate();
                // Retrieve the roll land value index from stats service
                return CompletableFuture.supplyAsync(() -> statsService.getRollLandValueIndex(taCode, salesGroupCode, rollNumber, revisionDate, LocalDate.now(ZoneId.of("Pacific/Auckland"))))
                        .thenApply(result -> result.orElse(BigDecimal.ONE))
                        .thenApply(result -> ok(Json.toJson(result)));
            });

        }).exceptionally(e -> {
            logger.error("ERR-PMD-008", "Exception while calling Land Value Index", e);
            throw new RuntimeException(e);
        });
    }

    // TODO This needs refactoring
    private CompletionStage<Result> getPropertyByQUPID(String qupid) {
        logger.info("request data {}", qupid);

        ObjectNode objNode = Json.newObject().put("qupid", Integer.parseInt(qupid));

        Jdk8Module module = new Jdk8Module().configureAbsentsAsNulls(true);
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(module);
        PropertySearchCriteria propertySearchCriteria = mapper.convertValue(objNode, PropertySearchCriteria.class);
        return propertyService.searchProperties().invoke(propertySearchCriteria).thenCompose(respond -> {
            List<Property> properties = respond.getProperties();

            logger.info("Number of properties found : {}", properties.size());

            if(properties.size() > 0) {

                Property property = properties.get(0);

                //JsonNode photos = mediaController.getMediaByQupIDJson(property.getQupid().get().toString());

                return mediaController.getMediaByQupIDJson(property.getQupid().get().toString()).thenCompose(photos -> {
                    List<PropertySearchCriteria.RollIdentifier> rollIdentifiers = new ArrayList<>();

                    rollIdentifiers.add(new PropertySearchCriteria.RollIdentifier(
                            property.getTerritorialAuthority().get().getCode(),
                            property.getRollNumber().get())
                    );

                    CompletableFuture<PropertySearchResult> previousProperty = getPreviousProperty(rollIdentifiers, property.getAssessmentNumber());
                    CompletableFuture<PropertySearchResult> nextProperty = getNextProperty(rollIdentifiers, property.getAssessmentNumber());

                    PropertySearchCriteria propertiesSearchCriteria = getPropertySearchCriteria(property.getAssessmentId());
                    CompletableFuture<PropertySearchResult> propertiesList = propertyService.searchProperties().invoke(propertiesSearchCriteria).toCompletableFuture();

                    PropertySearchCriteria propertySearchCriteriaWithQupid = getPropertySearchCriteriaQupid(property.getQupid());
                    CompletableFuture<QvNumberOfBathrooms> numberOfBathrooms = propertyService.getNumberOfBathrooms().invoke(propertySearchCriteriaWithQupid).toCompletableFuture();

                    return CompletableFuture.allOf(previousProperty, nextProperty, propertiesList).thenApply(v -> {

                        ObjectNode result = composeFinalRespond(
                                property,
                                photos,
                                previousProperty.join().getProperties(),
                                nextProperty.join().getProperties(),
                                propertiesList.join().getProperties(),
                                numberOfBathrooms.join().getNumberOfBathrooms()
                        );

                        return ok(result);
                    });
                });
            }

            logger.info("Property not found for given qupid");
            return null;
        });

    }

     public CompletionStage<Result> updatePropertyInfoField(){
        logger.info("Request for updatePropertyInfoField received.");

        UpdatePropertyInfoFieldRequest propertyInfoUpdateRequest = Json.fromJson(request().body().asJson(), UpdatePropertyInfoFieldRequest.class);

        logger.info("Updating data field {} for qpid: {}", propertyInfoUpdateRequest.getFieldName(), propertyInfoUpdateRequest.getQpid());
        JSONObject updatePropertyInfoFieldPayloadJson = new JSONObject(propertyInfoUpdateRequest);

        return propertyApiService
                .updatePropertyInfoField(updatePropertyInfoFieldPayloadJson)
                .thenApply(res -> ok(Json.toJson(res.getResponseBody())));
    }

    public CompletionStage<Result> getPropertyInfo(Integer qpid){
        logger.info("Request for getPropertyInfo received.");
        return propertyApiService.getMasterDetails(qpid).thenApply(res -> {
                    if (res.getStatusCode() > 200) {
                        return ok(Json.toJson(Optional.empty()));
                    }
                    try {
                        JSONObject propertyInfoJson = new JSONObject(res.getResponseBody()).getJSONObject("propertyInfo");
                        String propertyInfo = propertyInfoJson.toString();
                        return ok(Json.toJson(Optional.of(objectMapper.readValue(propertyInfo, QivsPropertyInfo.class))));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
    }

    public CompletionStage<Result> getPropertyInfoFull(Integer qpid) throws IOException {
        logger.info("Request for getPropertyInfoFull received.");

        Response response = propertyApiService.getMasterDetails(qpid).toCompletableFuture().join();
        if (response.getStatusCode() > 200) {
            return CompletableFuture.completedFuture(LambdaHandler.responsePassThrough(response));
        }
        else {
            try {
                Boolean canBeValued = false;
                Property property = propertyService.getPropertyByQupid(qpid).invoke().toCompletableFuture().join();
                if (property.getId().isPresent()) {
                    canBeValued = propertyService.canBeValued(property.getId().get()).invoke().toCompletableFuture().join();
                }
                String responseBody = response.getResponseBody();
                JsonNode updatedResponseBody = objectMapper.readTree(responseBody);
                ((ObjectNode) updatedResponseBody).put("canBeValued", canBeValued);
                return CompletableFuture.completedFuture(ok(updatedResponseBody));
            } catch (Exception error) {
                logger.error("ERR-PMD-006", "Error while fetching canBeValued for qpid: " + qpid, error);
                return CompletableFuture.completedFuture(LambdaHandler.responsePassThrough(response));
            }
        }
    }

    public CompletionStage<Result> updateOwners() {
        logger.info("Request for updateOwners received.");
        JsonNode payload = request().body().asJson();
        return propertyApiService.updateOwners(payload).thenApply(LambdaHandler::responsePassThrough);
    }


    private CompletionStage<Result> getPropertyById(String id) {

        logger.info("request data {}", id);

        UUID uuid = UUID.fromString(id);
        return propertyService.getProperty(uuid).invoke().thenCompose(property -> {

            List<PropertySearchCriteria.RollIdentifier> rollIdentifiers = new ArrayList<>();

            rollIdentifiers.add(new PropertySearchCriteria.RollIdentifier(property.getTerritorialAuthority().get().getCode(),property.getRollNumber().get()));
            CompletableFuture<PropertySearchResult> previousProperty = getPreviousProperty(rollIdentifiers, property.getAssessmentNumber());
            CompletableFuture<PropertySearchResult> nextProperty = getNextProperty(rollIdentifiers, property.getAssessmentNumber());
            PropertySearchCriteria propertySearchCriteria = getPropertySearchCriteria(property.getAssessmentId());
            CompletableFuture<PropertySearchResult> propertiesList = propertyService.searchProperties().invoke(propertySearchCriteria).toCompletableFuture();
            PropertySearchCriteria propertySearchCriteriaWithQupid = getPropertySearchCriteriaQupid(property.getQupid());
            CompletableFuture<QvNumberOfBathrooms> numberOfBathrooms = propertyService.getNumberOfBathrooms().invoke(propertySearchCriteriaWithQupid).toCompletableFuture();


            return mediaController.getMediaByQupIDJson(property.getQupid().get().toString()).thenCompose(photos -> {
                return CompletableFuture.allOf(previousProperty, nextProperty, propertiesList, numberOfBathrooms).thenApply(v -> {
                    ObjectNode result = composeFinalRespond(
                            property,
                            photos,
                            previousProperty.join().getProperties(),
                            nextProperty.join().getProperties(),
                            propertiesList.join().getProperties(),
                            numberOfBathrooms.join().getNumberOfBathrooms()
                    );
                    return ok(result);
                });
            });
        });

    }

    private ObjectNode composeFinalRespond(Property property,
                                           JsonNode photos,
                                           List<Property> previousProperty,
                                           List<Property> nextProperty,
                                           List<Property> propertyList,
                                           Optional<Integer> numberOfBathrooms){

        ObjectNode respond = Json.newObject();

        respond.set("property", Json.toJson(property));
        respond.set("photos", photos);

        String previousId = getPropertyID(previousProperty);
        respond.put("previousProperty", previousId);

        String nextId = getPropertyID(nextProperty);
        respond.put("nextProperty", nextId);

        List<JsonNode> properties = getPropertiesList(propertyList);
        respond.set("propertiesList", Json.toJson(properties));

        respond.set("numberOfBathrooms", Json.toJson(numberOfBathrooms));

        return respond;
    }

    private String getPropertyID(List<Property> properties){
        return  ((properties != null) && (properties.size() > 0)) ? properties.get(0).getId().get().toString() : null;
    }

    private CompletableFuture<PropertySearchResult> getPreviousProperty(List<PropertySearchCriteria.RollIdentifier> rollIdentifiers,Optional<Long> assessmentNumber){

        Optional<Long> assessmentNumberTo = Optional.of(assessmentNumber.get() - 1);
        Optional<String> order = Optional.of("DESC");
        PropertySearchCriteria propertySearchCriteria = getPropertySearchCriteria(rollIdentifiers, null, assessmentNumberTo, order);
        return propertyService.searchProperties().invoke(propertySearchCriteria).toCompletableFuture();
    }

    private CompletableFuture<PropertySearchResult> getNextProperty(
            List<PropertySearchCriteria.RollIdentifier> rollIdentifiers,
            Optional<Long> assessmentNumber){

        Optional<Long> assessmentNumberFrom = Optional.of(assessmentNumber.get() + 1);
        Optional<String> order = Optional.of("ASC");
        PropertySearchCriteria propertySearchCriteria = getPropertySearchCriteria(rollIdentifiers, assessmentNumberFrom, null, order);

        return propertyService.searchProperties().invoke(propertySearchCriteria).toCompletableFuture();
    }

    private List<JsonNode> getPropertiesList(List<Property> properties){

        if((properties != null) && (!properties.isEmpty())){

            List<JsonNode> objectNodes = new ArrayList<>();

            properties.forEach(p -> {
                ObjectNode result = Json.newObject();

                result.put("id", p.getId().get().toString());
                result.put("qpid", p.getQupid().get());
                result.put("rollNumber", p.getRollNumber().orElse(0));
                result.put("assessmentNumber", p.getAssessmentNumber().orElse(0L));
                result.put("suffix", p.getSuffix().orElse(""));

                objectNodes.add(Json.toJson(result));
            });

            return objectNodes;
        }

        return Collections.emptyList();
    }

    private PropertySearchCriteria getPropertySearchCriteria(Optional<Integer> assessmentId){

        List<PropertySortField> sortFields = new ArrayList<>();
        sortFields.add(PropertySortField.VALUATION_REFERENCE);

        return PropertySearchCriteria.emptyCriteria()
                .withAssessmentId(assessmentId)
                .withMax(Optional.of(50000))
                .withSort(Optional.of(sortFields));
    }

    private PropertySearchCriteria getPropertySearchCriteriaQupid(Optional<Integer> qupid){

        List<PropertySortField> sortFields = new ArrayList<>();
        sortFields.add(PropertySortField.VALUATION_REFERENCE);

        return PropertySearchCriteria.emptyCriteria()
                .withQupid(qupid)
                .withMax(Optional.of(50000))
                .withSort(Optional.of(sortFields));
    }

    private PropertySearchCriteria getPropertySearchCriteria(
            List<PropertySearchCriteria.RollIdentifier> rollIdentifiers,
            Optional<Long> assessmentNumberFrom,
            Optional<Long> assessmentNumberTo,
            Optional<String> order){

        List<PropertySortField> sortFields = new ArrayList<>();
        sortFields.add(PropertySortField.ASSESSMENT_NUMBER);
        sortFields.add(PropertySortField.VALUATION_REFERENCE);

        return PropertySearchCriteria.emptyCriteria()
                .withRolls(Optional.of(rollIdentifiers))
                .withAssessmentNumberFrom(assessmentNumberFrom)
                .withAssessmentNumberTo(assessmentNumberTo)
                .withIncludeChildAssessments(Optional.of(false))
                .withMax(Optional.of(1))
                .withSort(Optional.of(sortFields))
                .withOrder(order);
    }

    @SubjectPresent
    public CompletionStage<Result> getPickListValues() {
        logger.info("Request for getPickListValues received.");

        try {
            CompletableFuture<Response> completeResponse = propertyApiService.getPicklistValues();

            return completeResponse
                    .thenApply(response -> {
                        try {
                            JsonNode json = objectMapper.readTree(response.getResponseBody());
                            return status(response.getStatusCode(), json);
                        } catch (IOException e) {
                            logger.error("ERR-PMD-007", "Exception mapping response from /getPickListValues Lambda: {}", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> getApportionmentDetails(Integer qpid) {
        logger.info("Request for getApportionmentDetails received. qpid = {}", qpid);

        try {
            CompletableFuture<Response> completeResponse = propertyApiService.getApportionmentDetails(qpid);

            return completeResponse
                    .thenApply(response -> {
                        try {
                            JsonNode json = objectMapper.readTree(response.getResponseBody());
                            return status(response.getStatusCode(), json);
                        } catch (IOException e) {
                            logger.error("ERR-PMD-009", "Exception mapping response from /getApportionmentDetails Lambda: ", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> getDetailsForQpids(String qpids) {
        logger.info("Request for getDetailsForQpids received. qpids = {}", qpids);

        try {
            CompletableFuture<Response> completeResponse = propertyApiService.getQpidDetails(qpids);

            return completeResponse
                    .thenApply(response -> {
                        try {
                            JsonNode json = objectMapper.readTree(response.getResponseBody());
                            return status(response.getStatusCode(), json);
                        } catch (IOException e) {
                            logger.error("ERR-PMD-019", "Exception mapping response from /getQpidDetails Lambda: {}", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> getDvrDataFromQivs(Integer qpid) {
        logger.info("Request for getDvrDataFromQivs received. qpid = {}", qpid);

        try {
            CompletableFuture<Response> completeResponse = propertyApiService.getDvrDerivedValues(qpid);

            return completeResponse
                    .thenApply(response -> {
                        try {
                            JsonNode json = objectMapper.readTree(response.getResponseBody());
                            return status(response.getStatusCode(), json);
                        } catch (IOException e) {
                            logger.error("ERR-PMD-021", "Exception mapping response from /getDvrDerivedValues Lambda: {}", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> getAttachment(Integer attachmentId) {
        logger.info("Request for getAttachment received. attachmentId = {}", attachmentId);
        try {
            Response getAttachmentResponse = propertyApiService.getAttachmentUrl(attachmentId).join();
            JSONObject response = new JSONObject(getAttachmentResponse.getResponseBody());

            if (getAttachmentResponse.getStatusCode() != 200) {
                return CompletableFuture.completedFuture(status(getAttachmentResponse.getStatusCode(), response.toString()));
            }

            JSONObject attachment = response.getJSONObject("attachment");

            if (attachment.isNull("url")) {
                return CompletableFuture.completedFuture(notFound("No attachment found"));
            }

            String url = attachment.getString("url");
            String fileName = attachment.getString("fileName");
            String contentType = URLConnection.guessContentTypeFromName(fileName);

            // ensure pdf displays in browser tab instead of downloading
            if (contentType.equals("application/pdf")) {
                byte[] bytes = PDFUtility.downloadPdf(url);
                bytes = PDFUtility.renamePDF(bytes, fileName);
                return CompletableFuture.completedFuture(
                        ok(bytes).as(contentType)
                                .withHeader("Content-Disposition", "inline; filename=" + "\"" + fileName + "\""));
            }

            return CompletableFuture.completedFuture(redirect(url));
        } catch (Exception e) {
            logger.error("ERR-PMD-011","Failed to get property attachment for attachmentId: {}", attachmentId);
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    @Restrict({@Group("INTERNAL_USER")})
    public CompletableFuture<Result> saveHazardNotes() {
        logger.info("Request for saveHazardNotes received.");
        return propertyApiService.saveHazardNotes(request().body().asJson()).thenApply(LambdaHandler::responsePassThrough);
    }
}

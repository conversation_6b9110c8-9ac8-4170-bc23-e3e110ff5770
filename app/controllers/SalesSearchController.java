package controllers;

import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import play.mvc.Result;
import services.SearchApiService;
import util.LambdaHandler;
import util.LoggerWrapper;

import javax.inject.Inject;
import java.util.concurrent.CompletionStage;

/**
 * This controller acts as a proxy between Monarch web and API Website Lambdas
 * to pass sales information in and out of the Monarch DB.
 */

@SubjectPresent
public class SalesSearchController extends AbstractController {
    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");

    /**
     * Used to get Configuration Values from application.conf.
     */
    private final Config config;

    /**
     * Used to parse JSON responses.
     */
    private final ObjectMapper objectMapper;
    private final SearchApiService searchApiService;

    @Inject
    public SalesSearchController(Config config, ObjectMapper objectMapper, SearchApiService searchApiService) {
        this.config = config;
        this.objectMapper = objectMapper;
        this.searchApiService = searchApiService;
    }

    @SubjectPresent
    public CompletionStage<Result> getSalesProcessingStatus() {
        logger.info("Request for getSalesProcessingStatus received.");

        try {
            return searchApiService.getSalesProcessingStatus()
                    .thenApply(LambdaHandler::responsePassThrough)
                    .exceptionally(e -> {
                        logger.error("ERR-SRH-001", "Exception while calling getSalesProcessingStatus", e.getCause());
                        return status(500);
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> getSearchSale() {
        logger.info("Request for getSearchSale received. {}", request().body().asJson().toString());

        try {

            return searchApiService.getSearchSale(request().body().asJson())
                    .thenApply(LambdaHandler::responsePassThrough)
                    .exceptionally(e -> {
                        logger.error("ERR-SRH-002", "Exception while calling getSearchSale", e.getCause());
                        return status(500);
                    });

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> getSearchSaleExport() {

        logger.info("Retrieving Sales Search: {}", request().body().asJson().toString());

        try {
            return searchApiService.getSearchSaleExport(request().body().asJson())
                    .thenApply(LambdaHandler::responsePassThrough)
                    .exceptionally(e -> {
                        logger.error("ERR-SRH-003", "Exception while calling getSearchSaleExport", e.getCause());
                        return status(500);
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> getSalesProcessingSource() {
        logger.info("Request for getSalesProcessingSource received.");

        try {
            return searchApiService.getSalesProcessingSource()
                    .thenApply(LambdaHandler::responsePassThrough)
                    .exceptionally(e -> {
                        logger.error("ERR-SRH-004", "Exception while calling getSalesProcessingSource", e.getCause());
                        return status(500);
                    });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
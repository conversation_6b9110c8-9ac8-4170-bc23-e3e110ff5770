package controllers;

import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.qv.property.api.PropertyService;
import com.qv.sale.api.SaleService;
import com.qv.sale.api.model.Sale;
import com.qv.sale.api.model.search.SaleSearchCriteria;
import com.qv.sale.api.model.search.SaleSearchResult;
import org.json.JSONObject;
import play.mvc.Controller;
import play.mvc.Result;
import play.shaded.ahc.org.asynchttpclient.Response;
import services.RuralWorksheetApiService;
import services.SaleAnalysisApiService;
import util.LambdaHandler;
import util.LoggerWrapper;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Stream;

@SubjectPresent
public class RuralSaleAnalysisController extends Controller {
    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");

    private final SaleAnalysisApiService saleAnalysisApiService;
    private final SaleService saleService;
    private final PropertyService propertyService;
    private final RuralWorksheetApiService ruralWorksheetApiService;

    @Inject
    public RuralSaleAnalysisController(SaleAnalysisApiService saleAnalysisApiService, SaleService saleService, PropertyService propertyService, RuralWorksheetApiService ruralWorksheetApiService) {
        this.saleAnalysisApiService = saleAnalysisApiService;
        this.saleService = saleService;
        this.propertyService = propertyService;
        this.ruralWorksheetApiService = ruralWorksheetApiService;
    }

    @SubjectPresent
    public CompletionStage<Result> displayRuralSaleAnalysis(Integer qivsSaleId) {
        logger.info("Request for displayRuralSaleAnalysis received. qivsSaleId: {}", qivsSaleId);
        CompletableFuture<Response> hasAnalysisFuture = saleAnalysisApiService.hasRuralSaleAnalysis(qivsSaleId);

        return hasAnalysisFuture
                .thenApply(Response::getResponseBody)
                .thenApply(JSONObject::stringToValue)
                .thenApply(val -> (boolean) val)
                .thenCompose(hasAnalysis -> hasAnalysis ? saleAnalysisApiService.getRuralSaleAnalysis(qivsSaleId) : saleAnalysisApiService.createRuralSaleAnalysis(qivsSaleId))
                .thenApply(response -> status(response.getStatusCode(), response.getResponseBody()).as("application/json"))
                .exceptionally(e -> {
                    logger.error("ERR-RSA-001", "Exception while executing displayRuralSaleAnalysis", e.getCause());
                    return status(500);
                });
    }

    @SubjectPresent
    public CompletionStage<Result> hasRuralSaleAnalysis(Integer saleId) {
        logger.info("Request for hasRuralSaleAnalysis received.");
        return saleAnalysisApiService.hasRuralSaleAnalysis(saleId).thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
            logger.error("ERR-RSA-002", "Exception while calling hasRuralSaleAnalysis", e.getCause());
            return status(500);
        });
    }

    @SubjectPresent
    public CompletionStage<Result> getRuralSaleAnalysis(Integer qivsSaleId) {
        logger.info("Request for getRuralSaleAnalysis received.");
        return saleAnalysisApiService.getRuralSaleAnalysis(qivsSaleId).thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
            logger.error("ERR-RSA-003", "Exception while calling getRuralSaleAnalysis", e.getCause());
            return status(500);
        });
    }

    @SubjectPresent
    public CompletionStage<Result> createRuralSaleAnalysis(Integer qivsSaleId) {
        logger.info("Request for createRuralSaleAnalysis received.");
        return saleAnalysisApiService.createRuralSaleAnalysis(qivsSaleId).thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
            logger.error("ERR-RSA-004", "Exception while calling createRuralSaleAnalysis", e.getCause());
            return status(500);
        });
    }

    @SubjectPresent
    public CompletionStage<Result> updateRuralSaleAnalysis(Boolean validateOnly) {
        logger.info("Request for updateRuralSaleAnalysis received.");
        return saleAnalysisApiService.updateRuralSaleAnalysis(request().body().asJson(), validateOnly).thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
            logger.error("ERR-RSA-005", "Exception while calling updateRuralSaleAnalysis", e.getCause());
            return status(500);
        });
    }

    @SubjectPresent
    public CompletionStage<Result> getRuralClassifications(Integer taCode) {
        logger.info("Request for getRuralClassifications received. taCode: {}", taCode);
        return saleAnalysisApiService.getRuralClassifications(taCode).thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
            logger.error("ERR-RSA-006", "Exception while calling getRuralClassifications", e.getCause());
            return status(500);
        });
    }

    @SubjectPresent
    public CompletionStage<Result> refreshRuralSaleAnalysis(Integer qivsSaleId) {
        logger.info("Request for refreshRuralSaleAnalysis received. qivsSaleId: {}", qivsSaleId);
        return saleAnalysisApiService.refreshRuralSaleAnalysis(qivsSaleId).thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
            logger.error("ERR-RSA-007", "Exception while calling refreshRuralSaleAnalysis", e.getCause());
            return status(500);
        });
    }

    @SubjectPresent
    public CompletionStage<Result> deleteRuralSaleAnalysis(Integer qivsSaleId) {
        logger.info("Request for deleteRuralSaleAnalysis received. qivsSaleId: {}", qivsSaleId);
        return saleAnalysisApiService.deleteRuralSaleAnalysis(qivsSaleId).thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
            logger.error("ERR-RSA-008", "Exception while calling deleteRuralSaleAnalysis", e.getCause());
            return status(500);
        });
    }

    private CompletableFuture<Sale> getSaleByQivsID(Integer qivsSaleId) {
        SaleSearchCriteria saleSearchCriteria = SaleSearchCriteria.emptyCriteria().withQivsSaleIds(Optional.of(new ArrayList<Integer>() {{
            add(qivsSaleId);
        }}));
        return saleService.searchSales().invoke(saleSearchCriteria).thenApply(SaleSearchResult::getSales).thenApply(List::stream).thenApply(Stream::findFirst).thenApply(Optional::get).toCompletableFuture();
    }

    @SubjectPresent
    public CompletionStage<Result> generateRuralSalesPdf(Integer qivsSaleId) {
        logger.info("Request for generateRuralSalesPdf received. qivsSaleId: {} ", qivsSaleId);
        return saleAnalysisApiService.generateRuralSalesPdf(qivsSaleId).thenApply(LambdaHandler::responsePassThrough).exceptionally(e -> {
            logger.error("ERR-RSA-009", "Exception while calling generateRuralSalesPdf", e.getCause());
            return status(500);
        });
    }
}
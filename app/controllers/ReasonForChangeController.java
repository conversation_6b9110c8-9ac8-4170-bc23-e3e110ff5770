package controllers;

import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.typesafe.config.Config;
import models.AddReasonForChangeRequest;
import play.mvc.Result;
import play.shaded.ahc.org.asynchttpclient.Response;
import play.libs.Json;
import services.ReasonForChangeApiService;
import util.LoggerWrapper;

import javax.inject.Inject;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;



/**
 * This controller acts as a proxy between Monarch web and API Website Lambdas to pass sales information in and out of the Monarch DB.
 */

@SubjectPresent
public class ReasonForChangeController extends AbstractController {
    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");

    /**
     * Used to get Configuration Values from application.conf.
     */
    private final Config config;

    /**
     * Used to parse JSON responses.
     */
    private final ObjectMapper objectMapper;
    private final ReasonForChangeApiService reasonApiService;

    @Inject
    public ReasonForChangeController(Config config, ObjectMapper objectMapper, ReasonForChangeApiService reasonApiService) {
        this.config = config;
        this.objectMapper = objectMapper;
        this.reasonApiService = reasonApiService;
    }


    @SubjectPresent
    public CompletionStage<Result> getExistingReasonForChange(Integer qpid, String userName){
        logger.info("Request for getExistingReasonForChange received. qpid: {} user: {}", qpid, userName);

        try {
            CompletableFuture<Response> completeResponse = reasonApiService.getExistingReasonForChange(qpid, userName);

            return completeResponse
                .thenApply(response -> {
                    try {
                        JsonNode json = objectMapper.readTree(response.getResponseBody());
                        return status(response.getStatusCode(), json);
                    } catch (IOException e) {
                        logger.error("ERR-WEB-013", "Exception mapping response from /getExistingReasonForChange Lambda: ", e.getMessage());
                        throw new RuntimeException(e);
                    }
                });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SubjectPresent
    public CompletionStage<Result> addReasonForChange() {
        logger.info("Request for addReasonForChange received.");

        AddReasonForChangeRequest reasonForChange = Json.fromJson(request().body().asJson(), AddReasonForChangeRequest.class);
        logger.info("Adding Reason for Change for qpid: {}", reasonForChange.qpid);

        try {
            CompletableFuture<Response> completeResponse = reasonApiService.addReasonForChange(request().body().asJson());

            return completeResponse
                .thenApply(response -> {
                    try {
                        JsonNode json = objectMapper.readTree(response.getResponseBody());
                        return status(response.getStatusCode(), json);
                    } catch (IOException e) {
                        logger.error("ERR-WEB-005", "Exception mapping response from /addReasonForChange Lambda: ", e.getMessage());
                        throw new RuntimeException(e);
                    }
                });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
package controllers;

import be.objectify.deadbolt.java.actions.Group;
import be.objectify.deadbolt.java.actions.Restrict;
import be.objectify.deadbolt.java.actions.SubjectPresent;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.qv.property.api.PropertyService;
import com.qv.property.api.model.Property;
import com.qv.property.api.model.property.PropertyDetail;
import com.qv.property.api.model.property.PropertyDetailDvrSnapshot;
import com.qv.property.api.model.property.PropertyDetailInferenceLevel;
import com.qv.property.api.model.property.PropertyDetailUpdateInformation;
import models.User;
import play.libs.Json;
import play.mvc.Result;
import play.mvc.Results;
import security.AuthSupport;
import services.PropertyApiService;
import util.LambdaHandler;
import util.LoggerWrapper;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Collectors;

@SubjectPresent
public class PropertyDetailController extends AbstractController {

    private static final LoggerWrapper logger = LoggerWrapper.getLogger("com.qv.monarchweb");


    private final PropertyService propertyService;
    private final PropertyApiService propertyApi;

    /**
     * Provides access to the current user
     */
    private final AuthSupport authSupport;

    @Inject
    public PropertyDetailController(final PropertyService propertyService, final AuthSupport authSupport, final PropertyApiService propertyApi) {
        this.propertyService = propertyService;
        this.authSupport = authSupport;
        this.propertyApi = propertyApi;
    }

    @Deprecated
    // TODO - Remove this method at some point - pending detail should be generated
    // as part of creating a Rating Valuation job and retrieved via the job.
    public CompletionStage<Result> getPendingPropertyDetail(Integer qpid) {
        logger.info("Retrieving Pending Property Details for QPID {}", qpid);
        return propertyService.getPropertyByQupid(qpid).invoke()
                .thenCompose(property -> propertyService
                        .getPendingPropertyDetail(property.getId().orElseThrow(IllegalStateException::new)).invoke())
                .thenApply(Json::toJson)
                .thenCompose(this::injectQivsImprovementsStatus)
                .thenCompose(this::injectDvrSnapshot)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Retrieves a read-only version of a property's current property detail.
     *
     * @param propertyId The property ID.
     * @return a read-only view of a property's current property detail.
     */
    @Restrict({ @Group("INTERNAL_USER"), @Group("EXTERNAL_USER_RW"), @Group("EXTERNAL_USER_READ") })
    public CompletionStage<Result> getCurrentPropertyDetail(UUID propertyId) {
        logger.info("Request for getCurrentPropertyDetail received. propertyId: {}", propertyId);
        Optional<User> currentUser = authSupport.currentUser(ctx());

        return propertyService.getCurrentPropertyDetail(propertyId, PropertyDetailInferenceLevel.DVR).invoke()
                .thenCompose(pd -> ifAllowed(currentUser, Optional.of(pd)))
                .thenApply(Json::toJson)
                .thenCompose(this::injectQivsImprovementsStatus)
                .thenCompose(this::injectDvrSnapshot)
                .thenCompose(this::injectCanBeEdited)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Retrieves an editable version of a property's current property detail. This
     * version has more pre-populated
     * fields than the read-only version above, with the expectation that users will
     * amend as necessary to ensure
     * the data is correct prior to saving.
     *
     * @param propertyId The property ID.
     * @return an editable version of a property's current property detail.
     */
    @Restrict({ @Group("INTERNAL_USER") })
    public CompletionStage<Result> editCurrentPropertyDetail(UUID propertyId) {
        logger.info("Request for editCurrentPropertyDetail received. propertyId: {}", propertyId);
        return propertyService.getCurrentPropertyDetail(propertyId, PropertyDetailInferenceLevel.FULL).invoke()
                .thenApply(Json::toJson)
                .thenCompose(this::injectQivsImprovementsStatus)
                .thenCompose(this::injectDvrSnapshot)
                .thenCompose(this::injectCanBeEdited)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    public CompletionStage<Result> getPropertyDetail(UUID propertyDetailId) {
        logger.info("Request for getPropertyDetail received. propertyDetailId: {}", propertyDetailId);
        Optional<User> currentUser = authSupport.currentUser(ctx());

        return propertyService.getPropertyDetail(propertyDetailId).invoke()
                .thenCompose(pd -> ifAllowed(currentUser, Optional.of(pd)))
                .thenApply(Json::toJson)
                .thenCompose(this::injectQivsImprovementsStatus)
                .thenCompose(this::injectDvrSnapshot)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    @Restrict({ @Group("INTERNAL_USER") })
    public CompletionStage<Result> savePendingPropertyDetail() {
        logger.info("Saving Pending Property Details: {}", request().body().asJson().findPath("id").asText());
        PropertyDetail propertyDetail = Json.fromJson(request().body().asJson(), PropertyDetail.class);
        return propertyService.savePendingPropertyDetail().invoke(propertyDetail)
                .thenApply(Json::toJson)
                .thenCompose(resultJson -> injectQivsImprovementsStatus(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenCompose(resultJson -> injectDvrSnapshot(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    @Restrict({ @Group("INTERNAL_USER") })
    public CompletionStage<Result> savePendingPropertyDetailForRVJob() {
        logger.info("Saving Pending Property Details: " + request().body().asJson().findPath("id").asText());
        PropertyDetail propertyDetail = Json.fromJson(request().body().asJson(), PropertyDetail.class);
        return propertyService.savePendingPropertyDetailForRatingValuationJob().invoke(propertyDetail)
                .thenApply(Json::toJson)
                .thenCompose(resultJson -> injectQivsImprovementsStatus(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenCompose(resultJson -> injectDvrSnapshot(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    @Restrict({ @Group("INTERNAL_USER") })
    public CompletionStage<Result> saveCurrentPropertyDetail(boolean ignoreWarnings) {
        logger.info("Request for saveCurrentPropertyDetail received. \n{}", request().body().asJson());
        PropertyDetail propertyDetail = Json.fromJson(request().body().asJson(), PropertyDetail.class);
        // TODO Move the QVNZ\ concat to the repository
        String ntUsername = "QVNZ\\"
                .concat(authSupport.currentUser(ctx()).orElseThrow(IllegalAccessError::new).getName());
        return propertyService.saveCurrentPropertyDetail(ntUsername, ignoreWarnings).invoke(propertyDetail)
                .thenApply(Json::toJson)
                .thenCompose(resultJson -> injectQivsImprovementsStatus(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenCompose(resultJson -> injectDvrSnapshot(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenCompose(resultJson -> injectCanBeEdited(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    public CompletionStage<Result> getPropertyDetailUpdateInformation(Integer qupid) {
        logger.info("Request for getPropertyDetailUpdateInformation received. qupid = {}", qupid);
        return propertyService.getPropertyDetailUpdateInformation(qupid).invoke()
                .thenApply(res -> {
                    return new PropertyDetailUpdateInformation(res.getUser().replace("-", "\\"),
                            res.getLastUpdatedDateTime());
                })
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Validate the saved pending property detail.
     *
     * @param id The Pending Property Detail ID
     * @return A JSON representation of the result of the validation which may
     *         contain errors or warnings.
     */
    @Restrict({ @Group("INTERNAL_USER") })
    public CompletionStage<Result> validatePendingPropertyDetail(UUID id) {
        logger.info("Request for validatePendingPropertyDetail received. id: {}", id);
        return propertyService.validatePropertyDetailSetupComplete(id).invoke()
                .thenApply(Json::toJson)
                .thenCompose(resultJson -> injectQivsImprovementsStatus(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenCompose(resultJson -> injectDvrSnapshot(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Validate the pending property detail to be completed.
     *
     * @param id The Pending Property Detail ID
     * @return A JSON representation of the result of the validation which may
     *         contain errors or warnings.
     */
    @Restrict({ @Group("INTERNAL_USER") })
    public CompletionStage<Result> validatePropertyDetailComplete(UUID id) {
        logger.info("Request for validatePropertyDetailComplete received. id: {}", id);
        return propertyService.validatePropertyDetailComplete(id).invoke()
                .thenApply(Json::toJson)
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    public CompletionStage<Result> validateOnSave() {
        logger.info("Request for /property-detail/validateOnSave");

        JsonNode json = request().body().asJson();

        if (json == null) {
            return CompletableFuture.completedFuture(badRequest("Missing body"));
        }

        if (!json.has("propertyDetail")) {
            return CompletableFuture.completedFuture(badRequest("Missing property detail"));
        }

        if (!json.has("property")) {
            return CompletableFuture.completedFuture(badRequest("Missing property"));
        }

        if (!json.has("dvrSnapshot")) {
            return CompletableFuture.completedFuture(badRequest("Missing dvr snapshot"));
        }

        PropertyDetail propertyDetail = Json.fromJson(json.get("propertyDetail"), PropertyDetail.class);
        Property property = Json.fromJson(json.get("property"), Property.class);
        PropertyDetailDvrSnapshot dvrSnapshot = Json.fromJson(json.get("dvrSnapshot"), PropertyDetailDvrSnapshot.class);
        String validationContext = json.has("validationContext") ? json.get("validationContext").asText() : null;
        BigDecimal landArea = property.getLandUseData().getLandArea().orElse(null);

        return propertyApi.validatePropertyDetails(propertyDetail, property, dvrSnapshot, landArea, validationContext)
                .thenApply(LambdaHandler::responsePassThrough);
    }

    /**
     * Generates buildings and spaces for the supplied property detail if a new
     * dwelling i.e. category (RV,RB,RM)
     * then tries to save the property detail with the generated list of buildings.
     * If the property detail is not for new dwellings, and is Residential Improved
     * then this will regenerate the property detail, updating the buildings and
     * spaces and site improvements. Otherwise this will
     * just return the supplied property details without generating list of property
     * buildings. This will then try save the property detail.
     *
     * @param umrGarages The number of umr garages to be generated.
     * @param fsGarages  The number of fs garages to be generated.
     * @return Result of saving the property detail with validation errors if there
     *         is any.
     */
    @Restrict({ @Group("INTERNAL_USER") })
    public CompletionStage<Result> generateBuildingsFromPropertyDetail(Optional<Integer> umrGarages,
            Optional<Integer> fsGarages) {
        logger.info("Request for getPropertyDetailUpdateInformation received.\n{}", request().body().asJson());
        PropertyDetail propertyDetail = Json.fromJson(request().body().asJson(), PropertyDetail.class);

        CompletionStage<PropertyDetail> propertyDetailFuture = propertyService
                .getProperty(propertyDetail.getPropertyId()).invoke().thenCompose(property -> {
                    // Check property is vacant
                    String category = property.getCategory().map(c -> c.getCode().substring(0, 2)).orElse("");
                    if (Arrays.asList("RB", "RM", "RV").contains(category)) {
                        // Generate buildings from property detail
                        return propertyService.generateBuildingsFromPropertyDetail(umrGarages, fsGarages)
                                .invoke(propertyDetail).thenApply(propertyDetail::withBuildings);
                    }
                    // Check property is Residential Improved
                    else if (Arrays.asList("RA", "RD", "RH", "RF", "RC", "RR", "RN").contains(category)) {
                        // Regenerate the property detail
                        return propertyService
                                .regeneratePropertyDetail(umrGarages, fsGarages, propertyDetail.getPropertyId())
                                .invoke(propertyDetail);
                    } else {
                        // Non vacant, don't generate buildings
                        return CompletableFuture.completedFuture(propertyDetail);
                    }
                });
        // Try to save the property detail and return the result of the save
        return propertyDetailFuture
                .thenCompose(
                        savedPropertyDetail -> propertyService.savePendingPropertyDetail().invoke(savedPropertyDetail))
                .thenApply(Json::toJson)
                .thenCompose(resultJson -> injectQivsImprovementsStatus(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenCompose(resultJson -> injectDvrSnapshot(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Generates property detail information for new dwelling, saves the property
     * detail, then return the result
     *
     * @return Result of saving the property detail with validation errors if there
     *         is any.
     */
    public CompletionStage<Result> generatePropertyDetailNewDwellingInformation() {
        logger.info("Request for generatePropertyDetailNewDwellingInformation received.\n{}", request().body().asJson());
        PropertyDetail propertyDetail = Json.fromJson(request().body().asJson(), PropertyDetail.class);

        CompletionStage<PropertyDetail> propertyDetailFuture = propertyService
                .getProperty(propertyDetail.getPropertyId()).invoke().thenCompose(property -> {
                    String category = property.getCategory().map(c -> c.getCode()).orElse("");
                    // Check property is vacant
                    if (Arrays.asList("RB", "RM", "RV").contains(category)) {
                        // Generate new dwelling information
                        return propertyService.generatePropertyDetailNewDwellingInformation().invoke(propertyDetail);
                    } else {
                        // Non vacant, don't generate dwelling information
                        return CompletableFuture.completedFuture(propertyDetail);
                    }
                });
        // Save property detail and return the result of the save
        return propertyDetailFuture
                .thenCompose(
                        savedPropertyDetail -> propertyService.savePendingPropertyDetail().invoke(savedPropertyDetail))
                .thenApply(Json::toJson)
                .thenCompose(resultJson -> injectQivsImprovementsStatus(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenCompose(resultJson -> injectDvrSnapshot(resultJson.get("value"))
                        .thenApply(propertyDetailJson -> resultJson))
                .thenApply(Results::ok)
                .exceptionally(this::handleException);
    }

    /**
     * Adds QIVS Improvements Status retrieved from the Property Service to
     * PropertyDetail JSON for use in the front end.
     *
     * @param propertyDetailJson The PropertyDetail JSON
     * @return The PropertyDetail JSON with the QIVS Improvements Status added.
     */
    private CompletionStage<JsonNode> injectQivsImprovementsStatus(JsonNode propertyDetailJson) {
        if (!propertyDetailJson.isNull()) {
            UUID propertyId = UUID.fromString(propertyDetailJson.get("propertyId").asText());
            return propertyService.getQivsImprovementsStatus(propertyId).invoke().thenApply(qivsImprovementsStatus -> {
                ((ObjectNode) propertyDetailJson).put("qivsImprovementsStatus", qivsImprovementsStatus.toString());
                return propertyDetailJson;
            });
        } else {
            return CompletableFuture.completedFuture(propertyDetailJson);
        }
    }

    /**
     * Adds a Property Detail DVR Snapshot retrieved from the Property Service to
     * PropertyDetail JSON for use in the front end.
     *
     * @param propertyDetailJson The PropertyDetail JSON
     * @return The PropertyDetail JSON with the DVR Snapshot added.
     */
    private CompletionStage<JsonNode> injectDvrSnapshot(JsonNode propertyDetailJson) {
        if (!propertyDetailJson.isNull()) {
            return propertyService.generatePropertyDetailDvrSnapshot()
                    .invoke(Json.fromJson(propertyDetailJson, PropertyDetail.class)).thenApply(dvrSnapshot -> {
                        ((ObjectNode) propertyDetailJson).set("dvrSnapshot", Json.toJson(dvrSnapshot));
                        return propertyDetailJson;
                    });
        } else {
            return CompletableFuture.completedFuture(propertyDetailJson);
        }
    }

    /**
     * Adds a boolean field indicating whether or not Property Detail can be edited
     * in Monarch to PropertyDetail JSON for use in the front end.
     *
     * @param propertyDetailJson The PropertyDetail JSON
     * @return The PropertyDetail JSON with the canBeEdited field added.
     */
    private CompletionStage<JsonNode> injectCanBeEdited(JsonNode propertyDetailJson) {
        if (!propertyDetailJson.isNull()) {
            UUID propertyId = UUID.fromString(propertyDetailJson.get("propertyId").asText());
            return propertyService.canBeValued(propertyId).invoke().thenApply(canBeEdited -> {
                ((ObjectNode) propertyDetailJson).put("canBeEdited", canBeEdited);
                return propertyDetailJson;
            });
        } else {
            return CompletableFuture.completedFuture(propertyDetailJson);
        }
    }

    public CompletionStage<Optional<PropertyDetail>> ifAllowed(Optional<User> currentUser,
            Optional<PropertyDetail> propertyDetail) {

        // If internal user then can see anything
        if (!propertyDetail.isPresent() || isInternalUser(currentUser))
            return CompletableFuture.completedFuture(propertyDetail);

        // get user TAs
        List<Integer> allowedTAs = currentUser.orElseThrow(IllegalStateException::new).getAllowedTA().stream()
                .map(Integer::parseInt).collect(Collectors.toList());

        // check the property is in the allowed TAs
        return propertyService.getProperty(propertyDetail.get().getPropertyId()).invoke()
                .thenApply(x -> (allowedTAs.contains(x.getTerritorialAuthority().map(ta -> ta.getCode()).orElse(0)))
                        ? propertyDetail
                        : Optional.empty());
    }

    /*
     * Check if user is internal.
     */
    private Boolean isInternalUser(Optional<User> user) {
        return Optional.of(user.orElseThrow(() -> new RuntimeException("No user context provided.")).getRoles())
                .orElse(new ArrayList<>()).stream().anyMatch(x -> x.getName().equals("INTERNAL_USER"));
    }

    /*
     * Generates Property Description for a Property Detail
     */
    @Restrict({ @Group("INTERNAL_USER") })
    public CompletionStage<Result> generatePropertyDetailDescription(UUID id) {
        logger.info("Request for generatePropertyDetailDescription received. id: {}",id);
        return propertyService.generatePropertyDetailDescription(id).invoke()
                .thenApply(response -> {
                    logger.info("Generated Pending Property Description {}", id);
                    return ok(Json.toJson(response));
                });
    }
}

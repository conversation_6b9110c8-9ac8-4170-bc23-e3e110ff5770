.admin-content {
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
}

.admin-search-box {
    background-color:white;
    color: black;
    padding: 1px;
    border: 3px solid #ddd;
    display: block;
    margin: 0 auto;
    font-size: 16px;
    font-weight: 500;
    width: 50%;
    height: 100%;
}

.admin-search-box:focus {
    border: 3px solid #ddd;
}

textarea {
    resize: none;
    height: 200px;
    font-size: 13px;
    min-height: 50px;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

div.minimalistBlack {
    border: 1px solid #000000;
    width: 100%;
    text-align: left;
    border-collapse: collapse;
}
.divTable.minimalistBlack .divTableCell, .divTable.minimalistBlack .divTableHead {
    border: 1px solid #000000;
    padding: 5px 4px;
    margin:1em;
}
.divTable.minimalistBlack .divTableBody .divTableCell {
    font-size: 13px;
    vertical-align: top;
}
.divTable.minimalistBlack .divTableHeading {
    background: #CFCFCF;
    background: -moz-linear-gradient(top, #dbdbdb 0%, #d3d3d3 66%, #CFCFCF 100%);
    background: -webkit-linear-gradient(top, #dbdbdb 0%, #d3d3d3 66%, #CFCFCF 100%);
    background: linear-gradient(to bottom, #dbdbdb 0%, #d3d3d3 66%, #CFCFCF 100%);
}
.divTable.minimalistBlack .divTableHeading .divTableHead {
    font-size: 15px;
    font-weight: bold;
    color: #000000;
    text-align: left;
}
.minimalistBlack .tableFootStyle {
    font-size: 14px;
    font-weight: bold;
    color: #000000;
    border-top: 3px solid #000000;
}
.minimalistBlack .tableFootStyle {
    font-size: 14px;
}
/* DivTable.com */
.divTable{ display: table; }
.divTableRow { display: table-row; }
.divTableHeading { display: table-header-group;}
.divTableCell, .divTableHead { display: table-cell;}
.divTableHeading { display: table-header-group;}
.divTableFoot { display: table-footer-group;}
.divTableBody { display: table-row-group;}

.searchWrapper{ margin:1.8rem auto; max-width:144rem; }
.searchInner-wrapper {
    background-color: #f5f5f5;
    padding: 0 0 .1rem;
    margin: 0 2rem;
}

.admin_label {
    display:inline-block;
    font-size: 18px;
}

.admin_typeahead__field {
    display:inline-block;
    margin-left:5rem;
    width:calc(100% - 41.5rem);
}

.admin_typeahead__field input {
    height:3.2rem;
    border:none;
    padding:0;
    color:#434343;
    font-size:1.8rem;
    font-weight:400;
    width: 100%;
}

.admin_typeahead__field input :not(:valid) ~ .close-icon { display:none; }

.admin-simpleSearch-icon {
    position:absolute;
    left:100rem;
    top:.5rem;
    pointer-events:none;
}


.admin-editable-text {
    background-color: white;
    text-align: left;
    vertical-align: top;
}

.admin-editable-textarea {
    background-color: white;
    text-align: left;
    vertical-align: top;
    height: 0px;
}

.divTableCell select {
    background-color: white;
    border: 1px solid;
    padding: 2px;
    border-color: rgb(169, 169, 169);
    height: unset;
}

.divTableCell .roleSelect {
    height: 100px;
}

.QVHV-admin-buttons {
    padding:1.6rem 0;
    overflow:auto;
}

.QVHV-admin-buttons button {
    display: inline-block;
    position: relative;
    font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    color: rgb(0,0,0);
    text-transform: uppercase;
    line-height: 1;
    line-height: 36px;
    letter-spacing: 0;
    text-align: center;
    text-decoration: none;
    background: transparent;
    border: none;
    border-radius: 2px;
    vertical-align: middle;
    padding: 0 16px;
    margin: 0;
    will-change: box-shadow;
    transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 20rem;
    height: 36px;
    overflow: hidden;
    outline: none;
    cursor: pointer;
}

.QVHV-admin-buttons button.primary {
    color: rgb(255,255,255);
    background-color: rgb(84,144,219);
    display: block;
    margin-bottom: 5px;
}

/** Override forced margin in snapLarge.css */
.QVHV-admin-buttons button.primary + button.primary {
    margin-left: 0px !important;
}

.QVHV-admin-buttons button.secondary {
    color: rgb(84,144,219);
    background-color: rgb(237,241,245);
}


.admin-content {
    min-height: 48px;
    bottom: 0;
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
}

div, .admin-content p {
    font-size: 1.4rem;
    font-weight: 400;
    line-height: 1.6;
}

.admin-searchWrapper {
    width: 100%;
    position: relative
}

.admin-searchField {
    float: left;
    width: 100%;
    border: 3px solid #00B4CC;
    padding: 5px;
    height: 20px;
    border-radius: 5px;
    outline: none;
    color: #9DBFAF;
}

.admin-searchField:focus{
    color: #00B4CC;
}

.admin-searchButton {
    position: absolute;
    right: -50px;
    width: 40px;
    height: 36px;
    border: 1px solid #00B4CC;
    background: #00B4CC;
    text-align: center;
    color: #fff;
    border-radius: 5px;
    cursor: pointer;
    font-size: 20px;
}

.admin-wrap{
    width: 30%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}


.report-dashboard-toolbar {
    margin-bottom:2em;
}

.report-dashboard {
    padding:20px;
    font-family:'<PERSON><PERSON><PERSON>';
    font-size: 1.4em;
    min-height: calc(100vh - 300px);
}

.report-dashboard .menu-area {
    min-width:250px;
    padding:20px;
}

.report-dashboard .menu-area .menu {
    overflow-y: auto;
}

.report-dashboard .menu-area .menu::-webkit-scrollbar,
.report-dashboard .report-criteria::-webkit-scrollbar {
    width: 11px;
}

.report-dashboard .report-criteria::-webkit-scrollbar-track,
.report-dashboard .menu-area .menu::-webkit-scrollbar-track {
    background-color: var(--qv-color-lightbuff);
}

.report-dashboard .report-criteria::-webkit-scrollbar-thumb,
.report-dashboard .menu-area .menu::-webkit-scrollbar-thumb {
    background-color: var(--qv-color-lightblue);
}

.report-dashboard .report-criteria::-webkit-scrollbar-track,
.report-dashboard .menu-area .menu::-webkit-scrollbar-track,
.report-dashboard .report-criteria::-webkit-scrollbar-thumb,
.report-dashboard .menu-area .menu::-webkit-scrollbar-thumb {
    padding-left: 5px;
    background-clip: padding-box;
    border-left:5px solid transparent;
}

.work-unit-dashboard .menu-area {
    color:white;
    background:var(--qv-color-darkblue);
}

.work-unit-dashboard .menu-area .report-category .report-category-name {
    color: #FFF;
}

.work-unit-dashboard .menu-area .report-category .report-name.active {
    color:var(--qv-color-darkblue);
    background:#FFF;
}

.work-unit-dashboard .menu-area .report-category .report-name:hover:not(.active) {
    background: rgba(255,255,255,0.2);
}

.report-dashboard .content-wrapper {
    position:relative;
    flex:1;
}

.report-dashboard .notification-area {
    padding:20px;
}

.report-dashboard .notification-area div {
    display:block;
    white-space:pre-wrap;
    line-height:100%;
    padding: 10px 0;
    color: var(--base-color);
    --base-color: rgba(14, 37, 106, 1);
}

.report-dashboard .notification-area div span {
    display:inline-block;
    font-weight:bold;
    max-width: calc(100% - 50px);
}

.report-dashboard .notification-area div::before {
    font-family: "Material Symbols Outlined";
    content: '\e88e';
    display: inline-block;
    font-size:3rem;
    padding-right:15px;
    vertical-align: top;
    font-variation-settings:'FILL' 1,'wght' 700;
}

.report-dashboard .notification-area div.warning {
    --base-color: rgba(255, 153, 15, 1);
}

.report-dashboard .notification-area div.warning::before {
    content: '\e82a';
}

.report-dashboard .notification-area div.error {
    --base-color: rgba(210, 54, 43, 1);
}

.report-dashboard .notification-area div.error::before {
    content: '\e000';
}

.report-dashboard .content-area {
    flex:1;
    padding:20px;
}

.report-dashboard .content-area > .qv-flex-column {
    flex:1;
}

.report-dashboard.work-unit-dashboard {
    height: auto;
    min-height: 400px;
}

.work-unit-dashboard .content-area {
    background:var(--color-lightblue-200);
    overflow: hidden;
}

.report-dashboard .dashboard-card {
    --base-color: rgba(55, 71, 79, 1);
    --fade-color: rgba(255, 255, 255, 1);
    border-radius:10px;
    padding:20px 5px;
    text-align:center;
    flex:1;
    align-items:center;
    box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.14), 0 3px 3px -2px rgba(0, 0, 0, 0.2), 0 1px 8px 0 rgba(0, 0, 0, 0.12);
    background: linear-gradient(180deg, var(--fade-color) 0%, rgba(255,255,255,1) 90%);
}

.report-dashboard .dashboard-card.qv-flex-column {
    gap:0;
}

.report-dashboard .dashboard-card .title, .report-dashboard .dashboard-card .stat {
    color: var(--base-color);
}

.report-dashboard .dashboard-card .stat {
    font-size:1.7em;
    font-weight:bold;
    margin-bottom:10px;
}

.report-dashboard .dashboard-card .stat:last-child {
    margin:auto 0;
}

.report-dashboard .dashboard-card .title {
    font-weight:bold;
}

.report-dashboard .dashboard-card i {
    display:block;
    border-radius:50px;
    color:#FFF;
    background: var(--base-color);
    border: 0.4rem solid #FFF;
    font-size: 4rem;
    padding:3px;
    margin-bottom: 20px;
    box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.14), 0 3px 3px -2px rgba(0, 0, 0, 0.2), 0 1px 8px 0 rgba(0, 0, 0, 0.12);
}

.report-dashboard .grey {
    --base-color: rgba(88, 88, 88, 1);
    --fade-color: rgba(88, 88, 88, 0.15);
}

.report-dashboard .green {
    --base-color: rgba(55, 186, 38, 1);
    --fade-color: rgba(55, 186, 38, 0.15);
}

.report-dashboard .darkgreen {
    --base-color: rgba(35, 119, 24, 1);
    --fade-color: rgba(35, 119, 24, 0.15);
}

.report-dashboard .yellow {
    --base-color: rgba(255, 174, 0, 1);
    --fade-color: rgba(255, 174, 0, 0.15);
}

.report-dashboard .lightblue {
    --base-color: rgba(186, 209, 235, 1);
    --fade-color: rgba(186, 209, 235, 0.15);
}

.report-dashboard .mediumblue {
    --base-color: rgba(82, 144, 219, 1);
    --fade-color: rgba(186, 209, 235, 0.15);
}

.report-dashboard .blue {
    --base-color: rgba(30, 65, 150, 1);
    --fade-color: rgba(30, 65, 150, 0.15);
}

.report-dashboard .darkblue {
    --base-color: rgba(14, 37, 106, 1);
    --fade-color: rgba(14, 37, 106, 0.15);
}

.report-dashboard .orange {
    --base-color: rgba(255, 153, 15, 1);
    --fade-color: rgba(255, 153, 15, 0.15);
}

.report-dashboard .darkorange {
    --base-color: rgba(234, 106, 15, 1);
    --fade-color: rgba(234, 106, 15, 0.15);
}


.report-dashboard .red {
    --base-color: rgba(210, 54, 43, 1);
    --fade-color: rgba(210, 54, 43, 0.15);
}

/* Override highcharts fonts/font-sizing */
.highcharts-legend-item, .highcharts-axis-labels, .highcharts-tooltip, .highcharts-label {
    font-family: "Lucida Grande", "Lucida Sans Unicode", Arial, Helvetica, sans-serif !important;
}

.highcharts-axis-labels {
    font-size:1.2em;
}

.highcharts-axis-labels text {
    font-size:0.9em !important;
}

.highcharts-tooltip {
    font-size:1.0em;
}

.highcharts-tooltip text {
    font-size:0.9em !important;
}

.highcharts-label {
    font-size:1.4em;
}

.highcharts-label span {
    font-family: "Lucida Grande", "Lucida Sans Unicode", Arial, Helvetica, sans-serif !important;
}

.highcharts-label > span {
    font-size:0.8em !important;
}


.report-dashboard .chart-wrapper {
    position:relative;
    flex:1;
}

.report-dashboard .centered-chart-dot-outer {
    position:absolute;
    top:0;
    height:100%;
    width:100%;
    transform:translate(50%,50%);
    pointer-events:none;
}

.report-dashboard .centered-chart-dot-outer .centered-chart-dot-inner {
    position:relative;
    gap:0;
    justify-content:center;
    height:10rem;
    width:10rem;
    transform:translate(-50%,-50%);
    color:#FFF;
    border:3px solid #FFF;
    background: var(--base-color);
    z-index:10;
    border-radius:5rem;
    text-align:center;
    pointer-events:all;
    transition: width 0.2s ease-in-out, height 0.2s ease-in-out, border-radius 0.2s ease-in-out;
}

.report-dashboard .centered-chart-dot-outer .centered-chart-dot-inner:hover {
    cursor:default;
    height:12rem;
    width:12rem;
    border-radius:6rem;
}

.report-dashboard .centered-chart-dot-outer .centered-chart-dot-inner span {
    font-weight:bold;
    font-size:1rem;
    white-space:nowrap;
    transition: font-size 0.2s ease-in-out, line-height 0.2s ease-in-out;
}

.report-dashboard .centered-chart-dot-outer .centered-chart-dot-inner:hover span {
    font-size:1.25rem;
}

.report-dashboard .centered-chart-dot-outer .centered-chart-dot-inner span:first-child {
    font-size:4rem;
    line-height:3rem;
}

.report-dashboard .centered-chart-dot-outer .centered-chart-dot-inner:hover span:first-child {
    font-size:5rem;
    line-height:3.75rem;
}



.vertical-space {
    height: 10px;
    background-color: transparent;
}

.my-reports-button {
    display: inline-block;
    color: white;
    background-color: var(--qv-color-blue);
    border: none;
    border-radius: 5px;
    width: 100%;
    text-align: center;
    font-size: 1.1em;
    padding: 5px;
}

.my-reports-button:hover {
    background-color: #3f91e1;
}

.report-category.qv-flex-column {
    gap:0;
    cursor:default;
}

.report-category .report-category-name,.report-category .report-name  {
    padding:2px 10px;
}

.report-category .report-category-name {
    color: var(--qv-color-darkblue);
    font-weight: bold;
}

.report-category .report-category-name::before {
    content: '\25BA';
    font-size: 1rem;
    display: inline-block;
    transform: rotate(0deg);
    transition: transform 0.15s ease-in-out;
}

.report-category .report-category-name.expanded::before {
    transform:rotate(90deg);
}

.report-category .report-category-name,.report-category .report-name {
    display: inline-block;
    border-radius:5px;
    margin:2px 0;
}

.report-category .report-name:visited {
    color:#FFF;
}

.report-category .report-name.active {
    color:#FFF;
    background:var(--qv-color-darkblue);
}

.report-category .report-name:hover:not(.active), .report-category .report-category-name:hover {
    background: rgba(0,0,0,0.2);
    cursor:pointer;
}

.report-menu-section {
    height:0;
    overflow:hidden;
}

.report-menu-section.expanded {
    height:auto;
}

/* override roll-maintenance.scss */
.router .mx-datepicker.report-datepicker {
    width:auto;
    flex:1;
}
.router .mx-datepicker.report-datepicker input {
    width:100%;
}
/*--*/

.report-criteria {
    flex: 1;
    overflow-y:auto;
    gap: 3rem;
}


.report-criteria .qv-flex-row > input[type="text"], .report-criteria .qv-flex-row > input[type="number"],
.report-criteria .qv-flex-column > input[type="text"], .report-criteria .qv-flex-column > input[type="number"] {
    height: var(--qv-input-height);
    font-size: 1.2rem;
    padding: 0.5rem;
    border: solid 1px #d2d2d2;
    border-radius: 5px;
    line-height: 1.4;
    width:100%;
}

.report-criteria h3 {
    font-size: 1.6rem;
    font-weight: bold;
    color: var(--qv-color-blue);
}

.report-criteria > .qv-flex-row > .qv-flex-column {
    flex:1;
    padding:10px;
    gap: 0.5em;
}

.report-criteria .report-option {
    border-radius:5px;
    padding: 5px 10px;
}

.report-criteria .report-option:hover {
    background: rgba(0,0,0,0.10);
    cursor:pointer;
}

.report-criteria span.icon-label {
    display:flex;
    align-items:center;
}

.report-criteria .fs-file-selector .uploaderBody .dropzone {
    position:relative;
    top:0;
}

.report-criteria .fs-file-selector .dropzone .dz-message {
    margin:auto;
}

.my-reports {
    flex:1;
}

.my-reports h2 {
    margin-right: auto;
}

.my-reports .mdl-button.material-symbols-outlined {
    font-family: 'Material Symbols Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height:1;
    min-width:auto;
}

.my-reports .report-job-table th, .my-reports .report-job-table td {
    padding: 5px 10px;
}

.my-reports .report-job-table tr td {
    background-color: #f0f8fd;
}

.my-reports .report-job-table tr:nth-child(odd) td {
    background-color: white;
}

.my-reports .report-job-table th {
    color: #fff;
    background-color: var(--qv-color-darkblue);
}

.my-reports .report-job-table th input,
.my-reports .report-job-table td input {
    vertical-align:middle;
}

.my-reports .report-job-table td.red-text {
    color: red;
}

.my-reports .report-job-table .download-button {
    display: inline-block;
    padding: 2px 10px;
    background-color: var(--qv-color-darkblue);
    color: white;
    text-decoration: none;
    border-radius: 2px;
}

.my-reports .report-job-table .download-button:hover {
    background-color: var(--qv-color-blue);
    color: white;
}

.my-reports .sortable {
    cursor: pointer;
}

.my-reports .sortable.asc {
    content: '\25BC';
}

.my-reports .sortable.desc {
    content: '\25BC';
}

.my-reports .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1em;
}

.my-reports .pagination .page-number {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 30px;
    border: 1px solid var(--qv-color-blue);
    border-radius: 50%;
    padding: 5px;
    margin: 0 5px;
    background-color: white;
    color: var(--qv-color-blue);
    font-weight: bold;
}

.my-reports .pagination .page-number:hover:not(.active) {
    background-color: rgba(0,0,0,0.15);
    cursor:pointer;
}

.my-reports .pagination .page-number.active {
    background-color: var(--qv-color-blue);
    color: white;
}

.select-report-checkbox {
    width: 18px;
    height: 18px;
}

.report-name {
    text-align: left;
    cursor: pointer;
}

.delete-button {
    display: inline-block;
    padding: 2px 10px;
    background-color: var(--qv-color-error);
    color: white;
    text-decoration: none;
    border-radius: 2px;
    border: none;
}

.delete-button:hover {
    opacity: 0.8;
}

.delete-button[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

.report-dashboard .data-summary {
    padding: 0 5px;
    .title {
        font-size: 12px;
        font-weight: 600;
        text-align: left;
        padding: 0px 5px;
        color: #fff;
        width: 100%;
    }
    .value {
        width: 100%;
        justify-content: space-between;
        padding: 0 5px;
        .item {
            font-weight: 600;
            color: #fff;

        }
    }
}

.report-dashboard .filter {
    position: relative;
    align-items: flex-end;
}

.report-dashboard .graph-container {
    width: 82%;
}

.report-dashboard .filter-container {
    width: 100%;
}

.report-dashboard .data-container {
    width: 18%;
}

.dashboard-card-container {
    --base-color: rgba(55, 71, 79, 1);
    --fade-color: rgba(255, 255, 255, 1);
    border-radius:10px;
    padding:20px 5px;
    text-align:center;
    flex:1;
    align-items:center;
    box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.14), 0 3px 3px -2px rgba(0, 0, 0, 0.2), 0 1px 8px 0 rgba(0, 0, 0, 0.12);
    background: linear-gradient(180deg, var(--fade-color) 0%, rgba(255,255,255,1) 90%);
    .label {
        font-size: 1.2em;
        color: rgb(51, 51, 51);
        font-weight: bold;
        fill: rgb(51, 51, 51);
        padding-left: 10px;
        text-align: left;
    }
    .cards {
        padding: 10px;
    }
}

.report-dashboard .skyBlue {
    --base-color: rgb(48 185 242);
    --fade-color: rgba(135, 206, 235, 0.15);
}

.report-dashboard .deepBlue {
    --base-color: rgb(0 12 93 / 62%);
    --fade-color: rgb(0 60 120 / 27%);
}

.report-dashboard .plum {
    --base-color: rgba(221, 160, 221, 1);
    --fade-color: rgba(221, 160, 221, 0.15);
}

.report-dashboard .darkerGreen {
    --base-color: rgb(3 157 0);
    --fade-color: rgb(23 178 19 / 36%);
}

.report-dashboard .maroon {
    --base-color: rgba(128, 0, 0, 1);
    --fade-color: rgba(128, 0, 0, 0.15);
}

.analysis-card {
    padding:0 5px !important;
}

.report-dashboard .darkerOrange {
    --base-color: rgb(205 136 42);
    --fade-color: rgb(255 153 15 / 27%);
}

.report-dashboard .fadedPink {
    --base-color: rgb(229 137 106);
    --fade-color: rgb(229 137 106 / 47%);
}

.report-dashboard .darkGrey {
    --base-color: rgb(35 35 35);
    --fade-color: rgb(26 24 24 / 21%);
}

.report-dashboard .fw-b {
    font-weight:bold;
}

.target-red {
    background: #d2362b !important;
}

.target-orange {
    background: #ff990f !important;
}

.target-green {
    background: #37ba26 !important;
}
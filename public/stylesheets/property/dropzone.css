@-webkit-keyframes passing-through {
	0% {
			opacity: 0;
  -webkit-transform: translateY(4rem);
	 -moz-transform: translateY(4rem);
	  -ms-transform: translateY(4rem);
	   -o-transform: translateY(4rem);
		  transform: translateY(4rem); }

	30%, 70% {
    	    opacity: 1;
  -webkit-transform: translateY(0px);
     -moz-transform: translateY(0px);
      -ms-transform: translateY(0px);
       -o-transform: translateY(0px);
          transform: translateY(0px); }

	100% {
            opacity: 0;
  -webkit-transform: translateY(-4rem);
     -moz-transform: translateY(-4rem);
      -ms-transform: translateY(-4rem);
       -o-transform: translateY(-4rem);
          transform: translateY(-4rem); }}

@-moz-keyframes passing-through {
	0% {
            opacity: 0;
  -webkit-transform: translateY(4rem);
     -moz-transform: translateY(4rem);
      -ms-transform: translateY(4rem);
       -o-transform: translateY(4rem);
          transform: translateY(4rem); }
	30%, 70% {
            opacity: 1;
  -webkit-transform: translateY(0);
     -moz-transform: translateY(0);
      -ms-transform: translateY(0);
       -o-transform: translateY(0);
          transform: translateY(0); }
	100% {
            opacity: 0;
  -webkit-transform: translateY(-4rem);
     -moz-transform: translateY(-4rem);
      -ms-transform: translateY(-4rem);
       -o-transform: translateY(-4rem);
          transform: translateY(-4rem); }}

@keyframes passing-through {
	0% {
            opacity: 0;
  -webkit-transform: translateY(4rem);
     -moz-transform: translateY(4rem);
      -ms-transform: translateY(4rem);
       -o-transform: translateY(4rem);
          transform: translateY(4rem); }
	30%, 70% {
            opacity: 1;
  -webkit-transform: translateY(0);
     -moz-transform: translateY(0);
      -ms-transform: translateY(0);
       -o-transform: translateY(0);
          transform: translateY(0); }
	100% {
            opacity: 0;
  -webkit-transform: translateY(-4rem);
     -moz-transform: translateY(-4rem);
      -ms-transform: translateY(-4rem);
       -o-transform: translateY(-4rem);
          transform: translateY(-4rem); }}
          
@-webkit-keyframes slide-in {
	0% {
            opacity: 0;
  -webkit-transform: translateY(4rem);
     -moz-transform: translateY(4rem);
      -ms-transform: translateY(4rem);
       -o-transform: translateY(4rem);
          transform: translateY(4rem); }
	30% {
            opacity: 1;
  -webkit-transform: translateY(0);
     -moz-transform: translateY(0);
      -ms-transform: translateY(0);
       -o-transform: translateY(0);
          transform: translateY(0); }}

@-moz-keyframes slide-in {
	0% {
            opacity: 0;
  -webkit-transform: translateY(4rem);
     -moz-transform: translateY(4rem);
      -ms-transform: translateY(4rem);
       -o-transform: translateY(4rem);
          transform: translateY(4rem); }
	30% {
            opacity: 1;
  -webkit-transform: translateY(0);
     -moz-transform: translateY(0);
      -ms-transform: translateY(0);
       -o-transform: translateY(0);
          transform: translateY(0); }}

@keyframes slide-in {
	0% {
            opacity: 0;
  -webkit-transform: translateY(4rem);
     -moz-transform: translateY(4rem);
      -ms-transform: translateY(4rem);
       -o-transform: translateY(4rem);
          transform: translateY(4rem); }
	30% {
            opacity: 1;
  -webkit-transform: translateY(0);
     -moz-transform: translateY(0);
      -ms-transform: translateY(0);
       -o-transform: translateY(0);
          transform: translateY(0); }}

@-webkit-keyframes pulse {
	0% {
  -webkit-transform: scale(1);
     -moz-transform: scale(1);
      -ms-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1); }
	10% {
  -webkit-transform: scale(1.1);
     -moz-transform: scale(1.1);
      -ms-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1); }
  	20% {
  -webkit-transform: scale(1);
     -moz-transform: scale(1);
      -ms-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1); }}

@-moz-keyframes pulse {
	0% {
  -webkit-transform: scale(1);
     -moz-transform: scale(1);
      -ms-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1); }
	10% {
  -webkit-transform: scale(1.1);
     -moz-transform: scale(1.1);
      -ms-transform: scale(1.1);
       -o-transform: scale(1.1);
          transform: scale(1.1); }
	20% {
  -webkit-transform: scale(1);
     -moz-transform: scale(1);
      -ms-transform: scale(1);
       -o-transform: scale(1);
          transform: scale(1); }}

@keyframes pulse {
	0% {
 -webkit-transform: scale(1);
    -moz-transform: scale(1);
     -ms-transform: scale(1);
      -o-transform: scale(1);
         transform: scale(1); }
	10% {
 -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
     -ms-transform: scale(1.1);
      -o-transform: scale(1.1);
    	 transform: scale(1.1); }
	20% {
 -webkit-transform: scale(1);
    -moz-transform: scale(1);
     -ms-transform: scale(1);
      -o-transform: scale(1);
         transform: scale(1); }}
         
         
.dropzone, .dropzone * { box-sizing: border-box; }

.dropzone.dz-clickable { cursor: pointer; }

.dropzone.dz-clickable * { cursor: default; }

.dropzone.dz-clickable .dz-message, 
.dropzone.dz-clickable .dz-message * { cursor: pointer; }

.dropzone.dz-started .dz-message { display: none; }

.dropzone.dz-drag-hover { border-style: solid; background-color:#0288D1; }

.dropzone.dz-drag-hover .dz-message { opacity: 0.1; }

.dropzone .dz-message {
	text-align: center;
	margin: 12rem 0 0; 
}

.dropzone .dz-preview {
    display: inline-block;
    position: relative;
    vertical-align: top;
    padding:2rem;
    border:1px solid #f2f2f2;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
    margin: 1.6rem 1.6rem 4rem;
    min-height: 10rem; 
}

.dropzone .dz-preview:hover { z-index: 1000; }

.dropzone .dz-preview:hover .dz-details { opacity: 1; }

.dropzone .dz-preview.dz-file-preview .dz-image {
      background: #999;
      background: linear-gradient(to bottom, #f7f7f7, #f2f2f2); 
      border-radius: 0.2rem;
}

.dropzone .dz-preview.dz-file-preview .dz-details { opacity: 1; }

.dropzone .dz-preview.dz-image-preview { background: white; }

.dropzone .dz-preview.dz-image-preview .dz-details {
	-webkit-transition: opacity 0.2s linear;
       -moz-transition: opacity 0.2s linear;
        -ms-transition: opacity 0.2s linear;
         -o-transition: opacity 0.2s linear;
            transition: opacity 0.2s linear; 
}
    
.dropzone .dz-preview .dz-remove {
	display: block;
	font-size: 1.4rem;
	text-align: center;
	border: none; 
	cursor: pointer;
}

.dropzone .dz-preview .dz-remove:hover { text-decoration: underline; }

.dropzone .dz-preview:hover .dz-details { opacity: 1; }

.dropzone .dz-preview .dz-details {
	position: absolute;
	bottom: -4.8rem;
	left: 0;
	font-size: 1.1rem;
	color: rgba(0, 0, 0, 0.9);
	line-height: 150%; 
	text-align: center;
	padding: 0.5rem;
	min-width: 100%;
	max-width: 100%;
	opacity: 0;
	z-index: 20;
}

.dropzone .dz-preview .dz-details .dz-size { font-size: 1.2rem; }
      
.dropzone .dz-preview .dz-details .dz-filename { white-space: nowrap; }

.dropzone .dz-preview .dz-details .dz-filename:not(:hover) {
	text-overflow: ellipsis; 
	overflow: hidden;
}
          
.dropzone .dz-preview .dz-details .dz-filename:not(:hover) span { border: 1px solid transparent; }

.dropzone .dz-preview:hover .dz-image img {
	-webkit-transform: scale(1.05, 1.05);
       -moz-transform: scale(1.05, 1.05);
        -ms-transform: scale(1.05, 1.05);
         -o-transform: scale(1.05, 1.05);
            transform: scale(1.05, 1.05);
              opacity:0.8;
}

.dropzone .dz-preview .dz-image {
	display: block;
	position: relative;
	overflow: hidden;
	width: 12rem;
	height: 12rem;
	z-index: 10; 
}
      
.dropzone .dz-preview .dz-image img { display: block; }

.dropzone .dz-preview.dz-success .dz-success-mark {
	-webkit-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
       -moz-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
        -ms-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
         -o-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
            animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1); 
}
    
.dropzone .dz-preview.dz-error .dz-error-mark {
              opacity: 1;
	-webkit-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
       -moz-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
        -ms-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
         -o-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
            animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1); 
}

.dropzone .dz-preview .dz-success-mark, 
.dropzone .dz-preview .dz-error-mark {
	position: absolute;
	top: 50%;
	left: 50%;
	display: block;
	margin-left: -2.7rem;
	margin-top: -2.7rem;
	opacity: 0;
	pointer-events: none;
	z-index: 500;
}
      
.dropzone .dz-preview .dz-success-mark svg, 
.dropzone .dz-preview .dz-error-mark svg {
	display: block;
	width: 5.4rem;
	height: 5.4rem; 
}

.dropzone .dz-preview.dz-processing .dz-progress {
			   opacity: 1;
	-webkit-transition: all 0.2s linear;
       -moz-transition: all 0.2s linear;
        -ms-transition: all 0.2s linear;
         -o-transition: all 0.2s linear;
            transition: all 0.2s linear; 
}
    
.dropzone .dz-preview.dz-complete .dz-progress {
      		   opacity: 0;
	-webkit-transition: opacity 0.4s ease-in;
       -moz-transition: opacity 0.4s ease-in;
        -ms-transition: opacity 0.4s ease-in;
         -o-transition: opacity 0.4s ease-in;
            transition: opacity 0.4s ease-in; 
}

.dropzone .dz-preview:not(.dz-processing) .dz-progress {
	-webkit-animation: pulse 6s ease infinite;
       -moz-animation: pulse 6s ease infinite;
        -ms-animation: pulse 6s ease infinite;
         -o-animation: pulse 6s ease infinite;
            animation: pulse 6s ease infinite; 
}

.dropzone .dz-preview .dz-progress {
	position: absolute;
	top: 50%;
	left: 50%;
	background: rgba(255, 255, 255, 0.9);
	margin-top: -0.8rem;
	margin-left: -4rem;
	border-radius: 0.2rem;
	width: 8rem;
	height: 1.6rem;
	overflow: hidden; 
	opacity: 1;
	z-index: 1000;
	pointer-events: none;
	-webkit-transform: scale(1);
}

.dropzone .dz-preview .dz-progress .dz-upload {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	background: #333;
	background: linear-gradient(to bottom, #4CAF50, #388E3C);
	width: 0;
	-webkit-transition: width 300ms ease-in-out;
	   -moz-transition: width 300ms ease-in-out;
	    -ms-transition: width 300ms ease-in-out;
	     -o-transition: width 300ms ease-in-out;
	        transition: width 300ms ease-in-out; 
}

.dropzone .dz-preview.dz-error .dz-error-message { display: block; }

.dropzone .dz-preview.dz-error:hover .dz-error-message {
	opacity: 1;
	pointer-events: auto; 
}

.dropzone .dz-preview .dz-error-message {
	display: block;
	display: none;
	position: absolute;
	top: -0.1rem;
	left: -0.1rem;
	font-size: 1.1rem;
	color: white; 
	text-align:center;
	background: #be2626;
	background: linear-gradient(to bottom, #be2626, #a92222);
	padding: 0.5rem 1.2rem;
	border-radius: 0.2rem;
	opacity: 0;
	width: 16.2rem;
	z-index: 1000;
	pointer-events: none;
	-webkit-transition: opacity 0.3s ease;
	   -moz-transition: opacity 0.3s ease;
	    -ms-transition: opacity 0.3s ease;
	     -o-transition: opacity 0.3s ease;
	        transition: opacity 0.3s ease;
}

.dropzone .dz-preview .dz-error-message:after {
	position: absolute;
	bottom: -0.5rem;
	left: 7.4rem;
	content: '';
	border-left: 6px solid transparent;
	border-right: 6px solid transparent;
	border-top: 6px solid #be2626; 
	width: 0;
	height: 0;
}

.dz-fallback {
    text-align:center;
    margin-top:2rem;
}

.dz-fallback p {
    display:none;
}

.dz-fallback input[type=file] {
    font-size:1.2rem;
    background-color:#fff;
    padding:1rem;
    border:1px solid #ddd;
    margin-right:1rem;
}

.dz-fallback input[type=submit] {
    font-size:1.4rem;
    color:#fff;
    text-transform:uppercase;
    background-color:rgba(74,144,226,1);
    padding:1rem 2rem;
    border:none;
    border-radius:3px;
}

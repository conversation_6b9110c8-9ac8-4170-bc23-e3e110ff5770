select {
	font-size:1.5rem;
	font-weight:400;
	background:none;
	border:none;
	border-right:1px solid rgba(0,0,0,0.12);
	border-radius:0;
	height:3.2rem;
	-webkit-appearance:none;
	-moz-appearance:none;
	appearance:none;
	font-family:'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif;
}

input:focus,
select:focus,
textarea:focus {
	outline:0;
	box-shadow:none;
}

/*  ============================================================================================ */
/*  MATERIAL ICON STYLES  ====================================================================== */
/*  ============================================================================================ */

.material-icons { cursor:pointer; }

.material-icons {
	font-family: 'Material Icons';
	font-weight: normal;
	font-style: normal;
	font-size: 24px;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-feature-settings: 'liga';
	-webkit-font-smoothing: antialiased;
}

.material-icons.md-dark { color: rgba(0, 0, 0, 0.54); }
.material-icons.md-dark.md-inactive { color: rgba(0, 0, 0, 0.26); }
.material-icons.md-light { color: rgba(255, 255, 255, 1); }
.material-icons.md-light.md-inactive { color: rgba(255, 255, 255, 0.3); cursor:initial; }

.icon > .material-icons.md-dark:hover,
.icon > .material-icons.md-dark.md-inactive:hover,
.icon > .material-icons.md-light:hover { color: rgba(0, 0, 0, 0.87); }

button.material-icons.md-dark:hover,
button.material-icons.md-dark.md-inactive:hover,
button.material-icons.md-light:hover { color: rgba(0, 0, 0, 0.87); }

.material-icons.md-18 { font-size: 18px; }
.material-icons.md-24 { font-size: 24px; }
.material-icons.md-36 { font-size: 36px; }
.material-icons.md-48 { font-size: 48px; }


/*  ============================================================================================ */
/*  MY ICON ANIMATION STYLES  ================================================================== */
/*  ============================================================================================ */

.sorter, h4 i {
	-webkit-transition: all .2s linear;
	transition: all .2s linear;
	-ms-transition: all .2s linear;
}

.sorter.down, h4 i.down {
	-webkit-transform:rotate(180deg);
	transform:rotate(180deg);
	-ms-transform:rotate(180deg);
	-sand-transform:rotate(180deg);
}


/*  ============================================================================================ */
/*  MATERIAL RIPPLE STYLES  ================================================================== */
/*  ============================================================================================ */

.mdl-ripple {
	position: absolute;
	top: 0;
	left: 0;
	background: rgb(0,0,0);
	border-radius: 50%;
	height: 50px;
	width: 50px;
	opacity: 0;
	overflow: hidden;
	pointer-events: none;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.mdl-ripple.is-animating {
	transition: width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
	transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1);
	transition: transform 0.3s cubic-bezier(0, 0, 0.2, 1), width 0.3s cubic-bezier(0, 0, 0.2, 1), height 0.3s cubic-bezier(0, 0, 0.2, 1), opacity 0.6s cubic-bezier(0, 0, 0.2, 1), -webkit-transform 0.3s cubic-bezier(0, 0, 0.2, 1);
}

.mdl-ripple.is-visible { opacity: 0.3; }


/*  ============================================================================================ */
/*  MATERIAL BUTTON STYLES  ==================================================================== */
/*  ============================================================================================ */


.mdl-button {
	display: inline-block;
	position: relative;
	font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
	font-size: 14px;
	font-weight: 600;
	font-style:normal;
	color: rgb(0,0,0);
	text-transform: uppercase;
	line-height: 1;
	line-height: 36px;
	letter-spacing: 0;
	text-align: center;
	text-decoration: none;
	background: transparent;
	border: none;
	border-radius: 2px;
	vertical-align: middle;
	padding: 0 16px;
	margin: 0;
	will-change: box-shadow;
	transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
	min-width:8.5rem;
	height: 36px;
	overflow: hidden;
	outline: none;
	cursor: pointer;
}

.mdl-button::-moz-focus-inner { border: 0; }

.mdl-button:hover { background-color: rgba(158,158,158, 0.20); }

.mdl-button:focus:not(:active) { background-color: rgba(0,0,0, 0.12); }

.mdl-button:active { background-color: rgba(158,158,158, 0.40); }

.mdl-button.mdl-button--colored { color: rgb(74,144,226); }

.mdl-button.mdl-button--colored-red { color: rgb(255,255,255); background-color: rgb(255, 64, 64);}

.mdl-button.mdl-button--colored:focus:not(:active) { background-color: rgba(0,0,0, 0.12); }

.mdl-button.mdl-button--colored-grey { color: rgb(255,255,255); background-color: rgba(117,117,117); }

.mdl-button.mdl-button--colored-darkgrey { color: rgb(255,255,255); background-color: rgb(51,51,51); }

input.mdl-button[type="submit"] { -webkit-appearance: none; }

.mdl-button--raised {
	background: rgba(158,158,158, 0.20);
	/*box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);*/
}

.mdl-button--raised:active {
	background-color: rgba(158,158,158, 0.40);
	box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.mdl-button--raised:focus:not(:active) {
	background-color: rgba(158,158,158, 0.40);
	box-shadow: 0 0 8px rgba(0, 0, 0, 0.18), 0 8px 16px rgba(0, 0, 0, 0.36);
}

.mdl-button--raised.mdl-button--colored {
	color: rgb(255,255,255);
	background: rgb(74,144,226);
}

.mdl-button--raised.mdl-button--colored:hover { color:#fff; background-color: rgb(74,144,226); }

.mdl-button--raised.mdl-button--colored:active { background-color: rgb(74,144,226); }

.mdl-button--raised.mdl-button--colored:focus:not(:active) { background-color: rgb(74,144,226); }

.mdl-button--raised.mdl-button--colored .mdl-ripple { background: rgb(255,255,255); }

.mdl-button--icon {
	font-size: 24px;
	color: inherit;
	line-height: normal;
	padding: 0;
	border-radius: 50%;
	margin-left: 0;
	margin-right: 0;
	min-width: 32px;
	width: 32px;
	overflow: hidden;
	height: 32px;
}

.mdl-button--icon .material-icons {
	position: absolute;
	top: calc(50% - 12px);
	left: calc(50% - 12px);
	line-height: 24px;
	width: 24px;
}

.mdl-button--icon.mdl-button--mini-icon {
	height: 24px;
	min-width: 24px;
	width: 24px;
}

.mdl-button--icon.mdl-button--mini-icon .material-icons {
	top: 0px;
	left: 0px;
}

.mdl-button--icon .mdl-button__ripple-container {
	border-radius: 50%;
	-webkit-mask-image: -webkit-radial-gradient(circle, white, black);
}

.mdl-button__ripple-container {
	display: block;
	position: absolute;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	z-index: 0;
	overflow: hidden;
}

.mdl-button[disabled] .mdl-button__ripple-container .mdl-ripple,
.mdl-button.mdl-button--disabled .mdl-button__ripple-container .mdl-ripple { background-color: transparent; }


.mdl-button--primary.mdl-button--primary { color: rgb(63,81,181); }

.mdl-button--primary.mdl-button--primary .mdl-ripple { background: rgb(255,255,255); }

.mdl-button--primary.mdl-button--primary.mdl-button--raised {
	color: rgb(255,255,255);
	background-color: rgb(63,81,181);
}

.mdl-button--accent.mdl-button--accent { color: rgb(255,64,129); }

.mdl-button--accent.mdl-button--accent .mdl-ripple { background: rgb(255,255,255); }

.mdl-button--accent.mdl-button--accent.mdl-button--raised {
	color: rgb(255,255,255);
	background-color: rgb(255,64,129);
}

.mdl-button[disabled][disabled],
.mdl-button.mdl-button--disabled.mdl-button--disabled {
	color: rgba(0,0,0, 0.26);
	background-color: transparent;
	cursor: default;
}

.mdl-button--raised[disabled][disabled],
.mdl-button--raised.mdl-button--disabled.mdl-button--disabled {
	color: rgba(0,0,0, 0.26);
	background-color: rgba(0,0,0, 0.12);
	box-shadow: none;
}

.mdl-button--colored[disabled][disabled],
.mdl-button--colored.mdl-button--disabled.mdl-button--disabled { color: rgba(0,0,0, 0.26); }

.mdl-button .material-icons { vertical-align: middle; }


/*  ============================================================================================ */
/*  MATERIAL TAB STYLES  ======================================================================= */
/*  ============================================================================================ */

.mdl-tabs {
	display: block;
	width: 100%;
}

.mdl-tabs__tab-bar {
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: row;
	-ms-flex-direction: row;
	flex-direction: row;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-align-content: space-between;
	-ms-flex-line-pack: justify;
	align-content: space-between;
	-webkit-align-items: flex-start;
	-ms-flex-align: start;
	align-items: flex-start;
	padding: 0 0 0 0;
	border-bottom: 1px solid rgb(224,224,224);
	margin: 0;
	height: 50px;

}

.mdl-tabs__tab {
	position: relative;
	display: block;
	font-size: 14px;
	font-weight: 600;
	color: rgba(0,0,0, 0.54);
	text-align: center;
	text-transform: uppercase;
	text-decoration: none;
	line-height: 48px;
	padding: 0 24px 0 24px;
	border: none;
	margin: 0;
	height: 48px;
	float: left;
	overflow: hidden;
}

.mdl-tabs__tab.is-active { color: rgba(0,0,0, 0.87); }

.mdl-tabs__tab.is-active:after {
	display: block;
	position: absolute;
	bottom: 0px;
	left: 0px;
	content: " ";
	background: rgb(63,81,181);
	height: 2px;
	width: 100%;
	-webkit-animation: border-expand 0.2s cubic-bezier(0.4, 0, 0.4, 1) 0.01s alternate forwards;
	animation: border-expand 0.2s cubic-bezier(0.4, 0, 0.4, 1) 0.01s alternate forwards;
	transition: all 1s cubic-bezier(0.4, 0, 1, 1);
}

.mdl-tabs__tab .mdl-tabs__ripple-container {
	display: block;
	position: absolute;
	top: 0px;
	left: 0px;
	height: 100%;
	width: 100%;
	z-index: 1;
	overflow: hidden;
}

.mdl-tabs__tab .mdl-tabs__ripple-container .mdl-ripple { background: rgb(63,81,181); }

.mdl-tabs__panel { display: block; }

.mdl-tabs.is-upgraded .mdl-tabs__panel { display: none; }

.mdl-tabs.is-upgraded .mdl-tabs__panel.is-active { display: block; }

@-webkit-keyframes border-expand {
	0% 	 { opacity: 0; width: 0; }
	100% { opacity: 1; width: 100%; }
}

@keyframes border-expand {
	0%   { opacity: 0; width: 0; }
	100% { opacity: 1; width: 100%; }
}


/*  ============================================================================================ */
/*  MATERIAL TOOLTIP STYLES  =================================================================== */
/*  ============================================================================================ */

.mdl-tooltip {
	display: inline-block;
	position: fixed;
	top: -500px;
	left: -500px;
	font-size: 10px;
	font-weight: 500;
	color: rgb(255,255,255);
	line-height: 14px;
	text-align: center;
	background: rgba(97,97,97, 0.9);
	padding: 8px;
	border-radius: 2px;
	max-width: 170px;
	z-index: 999;
	will-change: transform;
	-webkit-transform: scale(0);
	transform: scale(0);
	-webkit-transform-origin: top center;
	transform-origin: top center;
}

.mdl-tooltip.is-active {
	-webkit-animation: pulse 200ms cubic-bezier(0, 0, 0.2, 1) forwards;
	animation: pulse 200ms cubic-bezier(0, 0, 0.2, 1) forwards;
}

.mdl-tooltip--large {
	font-size: 14px;
	line-height: 14px;
	padding: 16px;
}

@-webkit-keyframes pulse {
	0% {
		opacity: 0;
		-webkit-transform: scale(0);
		transform: scale(0);
	}
	50% {
		-webkit-transform: scale(0.99);
		transform: scale(0.99);
	}
	100% {
		opacity: 1;
		visibility: visible;
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}

@keyframes pulse {
	0% {
		opacity: 0;
		-webkit-transform: scale(0);
		transform: scale(0);
	}
	50% {
		-webkit-transform: scale(0.99);
		transform: scale(0.99);
	}
	100% {
		opacity: 1;
		visibility: visible;
		-webkit-transform: scale(1);
		transform: scale(1);
	}
}


/*  ============================================================================================ */
/*  MATERIAL SHADOW STYLES  ==================================================================== */
/*  ============================================================================================ */

.mdl-shadow--2dp  { box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12); }
.mdl-shadow--3dp  { box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.14), 0 3px 3px -2px rgba(0, 0, 0, 0.2), 0 1px 8px 0 rgba(0, 0, 0, 0.12); }
.mdl-shadow--4dp  { box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.2); }
.mdl-shadow--6dp  { box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12), 0 3px 5px -1px rgba(0, 0, 0, 0.2); }
.mdl-shadow--8dp  { box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2); }
.mdl-shadow--16dp { box-shadow: 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2); }
.mdl-shadow--24dp { box-shadow: 0 9px 46px 8px rgba(0, 0, 0, 0.14), 0 11px 15px -7px rgba(0, 0, 0, 0.12), 0 24px 38px 3px rgba(0, 0, 0, 0.2); }


/*  ============================================================================================ */
/*  BUTTON BAR STYLES  ========================================================================= */
/*  ============================================================================================ */

.buttonBar {
	background-color:#fff;
	padding:1.6em 2.4rem ;
	border-top:1px solid rgba(0,0,0,0.14);
	width:100%;
	height:6.9rem;
}

.buttonBar.bottom {
	position:absolute;
	bottom:0;
}


/*  ============================================================================================ */
/*  MY TAB STYLES  ============================================================================= */
/*  ============================================================================================ */

.tabs {
	padding:0 4.2rem;
	width:100%;
}

.tabs li {
	display:inline-block;
	margin-right:0.4rem;
}

.tabs li a {
	display:block;
	font-size:1.3rem;
	font-weight:300;
	color:rgba(255,255,255,0.7);
	line-height:3.6;
	text-transform:uppercase;
	text-align:center;
	min-width:16rem;
	height:4.8rem;
}

.tabs li a:hover {
	color:rgba(255,255,255,1);
	opacity:1;
	cursor:pointer;
}

.tabs li a.active,
.tabs li a.active:hover {
	color:rgba(255,255,255,1);
	border-bottom:2px solid #f8e71c;
	opacity:1;
}

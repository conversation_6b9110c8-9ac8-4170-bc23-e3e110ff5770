@me dia only screen and (max-width:480px) {

/*  ==================================================================================================== */
/*  START EXTRA SMALL DEVICES, PHONES (MAX-WIDTH:480PX)  =============================================== */
/*  ==================================================================================================== */
		
		
		/*  ============================================================================================ */
		/*  GENERIC STYLES  ============================================================================ */
		/*  ============================================================================================ */
		
		/*body	{ border-top:2rem solid orange; }*/
		
		
		/*  ============================================================================================ */
		/*  ICON STYLES  =============================================================================== */
		/*  ============================================================================================ */
		
		
		/*  ============================================================================================ */
		/*  BOX-CONTENT : BORDER-BOX ELEMENTS  ========================================================= */
		/*  ============================================================================================ */
		
		
		/*  ============================================================================================ */
		/*  PROPERTY SEARCH BAR STYLES  ================================================================ */
		/*  ============================================================================================ */
		
		.simpleSearch { padding:0 1.8rem 0 0; }
		
		.listBox { padding: 1.2rem 1.6rem; }
			
		.listBox li {
			font-size: 1.2rem;
			line-height: 3;
		}
		
		.listBox li span { margin:0 0.8rem; }
		
		.taSelect { width: 5.7rem; }

		.taSelect dt span::before {	content:"TA's" }

		.taSelect + span { left:7rem; }

		.typeahead__field {
			margin-left: 10.5rem;
			width: calc(100% - 7rem);
		}

		/*  ============================================================================================ */
		/*  PROPERTY SEARCH RESULTS ROWS  ============================================================== */
		/*  ============================================================================================ */
		
		.resultsTitle h1 { font-size: 1.4rem; }
		
		.colCell.capval, 
		.colCell.landval, 
		.colCell.tfa, 
		.colCell.tlamas, 
		.colCell.landarea {
    		top: 9.75rem;
		}
		
		.colCell.valref, .colCell.category {
			font-size: 1rem;
			line-height: 1.8;
			min-width: 7rem;
		}

		.primaryPhoto_thumb {
    		width: 4.8rem;
   			height: 4.8rem;
		}
		
		.fullAddress { 
			margin-left:5.75rem;
		    min-height: 5.25rem;
		}
		
		.fullAddress span:last-child { font-size:1.1rem; }

		
	
		
		/*  ============================================================================================ */
		/*  PROPERTY SEARCH RESULTS OPEN PROPERTY CARD  ================================================ */
		/*  ============================================================================================ */
		
		.colCell.capval::after,
		.colCell.landval::after,
		.colCell.tfa::after,
		.colCell.tlamas::after,
		.colCell.landarea::after {
			position:absolute;
			top:3rem;
			left:0;
			font-size:1rem;
			line-height:1;
			color:rgba(0,0,0,0.38);
			vertical-align:top;
		}
		
		.colCell.capval::after 	 { content:"Capital value"; }
		.colCell.landval::after  { content:"Land value"; }
		.colCell.tfa::after 	 { content:"TFA"; }
		.colCell.tlamas::after   { content:"TLA"; }
		.colCell.landarea::after { content:"Land area"; }
		
		
		/*  ============================================================================================ */
		/*  PROPERTY CARD EXTRA DETAILS DRAWER  ======================================================== */
		/*  ============================================================================================ */
		
		.extras { padding:0.8rem 0 0.4rem; }

		.extrasCalculated { 
			padding: 1.2rem 0.5rem 3rem;
			margin: 0 0.5rem 0;
			width: calc(100% - 1rem);
		}

		.extrasCalculated div {
			margin: 0 0.5rem;
			width: calc(33% - 1.1rem);
		}

		.extrasCalculated .cvnr::after, 
		.extrasCalculated .vinr::after, 
		.extrasCalculated .lvnr::after { font-size: 1rem; }

		.masExtras {
			padding: 1rem 0 1.2rem;
			margin: 0 0.5rem 1rem;
		}

		.masExtras li { margin:0 0.15rem; }
				
		.extrasList,
		.extrasList + .extrasList {
			display:block;
			margin: 0 1.2rem;
			width: initial;
		}

		.extrasList + .extrasList li:last-child { border-bottom:initial; }

		@media only screen and (max-width:380px) {

			.masExtras li {
				font-size: 0.9rem;
				line-height: 1.3;
				padding-top: 0.75rem;
				margin:0 0.1rem;
				width: 4.15rem;
				height: 4.15rem;
			}

		}
		
		/*  ============================================================================================ */
		/*  IMAGE UPLOAD WINDOW  ======================================================================= */
		/*  ============================================================================================ */
				
				
/*  ==================================================================================================== */
/*  END EXTRA SMALL DEVICES, PHONES (MAX-WIDTH:480PX)  ================================================= */
/*  ==================================================================================================== */

}
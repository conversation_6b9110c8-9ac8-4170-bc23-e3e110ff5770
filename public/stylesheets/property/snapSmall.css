@media only screen and (max-width:768px) {

    /*  ==================================================================================================== */
    /*  START SMALL DEVICES, TABLETS (MAX-WIDTH:768PX)  ==================================================== */
    /*  ==================================================================================================== */

    body { background-color:#f7f8f9; }

    .bannerControls li.closer,
    .qvToolbar-leftMenu,
    .qvToolbar-qivs,
    .resultsFound,
    .resultsTitle,
    .searchbarWrapper,
    .sortRow-wrapper {
        display:none;
    }

    /* ---------------------------------------------------------------- */

    .home { min-width:auto; }

    .headerWrapper {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background-color: rgba(30,65,150,1);
        background: linear-gradient(to right, rgba(22,69,139,.95) 0%,rgba(18,39,86,.9) 100%), url(../../images/monarchWing.jpg);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 100% 0;
        padding:0;
        border-bottom: 2px solid #548fdb;
        border-top: none;
        min-width:auto;
        width: 100%;
        height: 6.3rem;
        z-index: 1;
    }

    .headerWrapper h2 {
        display:block;
        left:0;
        font-size: .8rem;
        color: transparent;
        background-image: url(../../images/monarchHome-monarchLogoBug-small.png);
        background-repeat: no-repeat;
        background-size: 4rem 3.4rem;
        background-position: 50% 1.5rem;
        width:100%;
        height: 6.3rem;
    }

    .bannerControls { margin:1.8rem 1rem 0 0; }

    .bannerControls li.closer {display:none;  }

    .bannerControls i { font-size:2.2rem; color:rgba(255,255,255,.84); }

    .contentWrapper {
        padding: 0 .5rem .5rem;
        margin-top:6.3rem;
    }


    /* ---------------------------------------------------------------- */

    .qvToolbar-wrapper { margin: 0 auto 2.4rem; }

    .qvToolbar {
        background-color: #0f2c40;
        border-radius: 0;
        margin: 0 -.5rem;
    }

    .qvToolbar-links {
        margin: 0 1rem;
        width: calc(100% - 2rem);
    }

    .qvToolbar-links li, .qvToolbar-link {
        width:33.3333%;
    }


    .masterDetails-Wrapper  .qvToolbar-wrapper { margin:0 auto 4.8rem; }

    .masterDetails-Wrapper .qvToolbar { margin:4.8rem 2rem 0; }

    .masterDetails-Wrapper .qvToolbar-links {
        margin:0 1.8rem;
        box-sizing:border-box;
        width:auto;
    }

    .masterDetails-Wrapper .qvToolbar-links li, .qvToolbar-link {
        width:auto;
    }

    /* ---------------------------------------------------------------- */

    .resultsWrapper {
        margin: 0;
        min-width:auto;
        max-width: auto;
    }

    .valuationJobs-dashboard .resultsInner-wrapper {
        background:none;
        padding:0;
        box-shadow:none;
    }

    .resultsRow.valjobRow {
        display: block;
        position:relative;
        background:#fff;
        padding: 0;
        margin: 0 0 2rem;
        border-bottom: none;
        box-shadow:0 0 0 .1rem #dadde0;
        height: 18rem;
        width: 100%;
        -moz-transition: all .1s linear;
        -webkit-transition: all .1s linear;
        transition: all .1s linear;
    }

    /* ---------------------------------------------------------------- */

    .searchDetails-wrapper {
        display:inline;
        height:1px;
        width:1px;
        pointer-event:none;
    }

    /* ---------------------------------------------------------------- */

    .resultsRow.valjobRow .colCell {
        position:initial;
        display:block;
        font-size:1.2rem;
        width:auto;
        height:auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .resultsRow.valjobRow .colCell.inspectiontime		{ position:absolute; top:9.3rem; left:18.5rem; width:calc(50% - 10.75rem); height:3.8rem;					}
    .resultsRow.valjobRow .colCell.jobdue 		 		{ position:absolute; top:9.3rem; left:calc(50% + 9.5rem); width:calc(50% - 10.75rem); height:3.8rem;		}
    .resultsRow.valjobRow .colCell.assignedvaluer 		{ position:absolute; bottom:.75rem; left:18.5rem; width:calc(50% - 10.75rem); height:3.8rem; 			}
    .resultsRow.valjobRow .colCell.countersignedvaluer 	{ position:absolute; bottom:.75rem; left:calc(50% + 9.5rem); width:calc(50% - 10.75rem); height:3.8rem; }
    .resultsRow.valjobRow .colCell.jobstatus 			{ position:absolute; bottom:1rem; left:1rem; font-size:1rem; color:#fff; background:#0f2c40; padding-left:.5rem; width:16rem; line-height:2.4; }

    .resultsRow.valjobRow .colCell.jobdue::before,
    .resultsRow.valjobRow .colCell.inspectiontime::before,
    .resultsRow.valjobRow .colCell.reporttype::before,
    .resultsRow.valjobRow .colCell.assignedvaluer::before,
    .resultsRow.valjobRow .colCell.countersignedvaluer::before {
        display:block;
        content:"";
        font-size:.9rem;
        color:#455667;
        padding-bottom:.2rem;
        border-bottom:.1rem solid #dadde0;
        margin-bottom:.2rem;
    }

    .resultsRow.valjobRow .colCell.jobdue::before 			   { content:"Job Due"; 		 }
    .resultsRow.valjobRow .colCell.inspectiontime::before 	   { content:"Inspection Time";  }
    .resultsRow.valjobRow .colCell.assignedvaluer::before 	   { content:"Assigned To"; 	 }
    .resultsRow.valjobRow .colCell.countersignedvaluer::before { content:"Countersigned By"; }

    /* ---------------------------------------------------------------- */

    .resultsRow.valjobRow:hover .fullAddress {
        padding: inherit;
        margin-left: inherit;
        box-shadow: none;
    }

    .resultsRow.valjobRow:hover .fullAddress::after { display:none; }

    .resultsRow.valjobRow:hover .fullAddress:hover {
        color: inherit;
        background: transparent;
        box-shadow: none;
    }

    .resultsRow.valjobRow .fullAddress {
        display: block;
        position: absolute;
        top: 0.8rem;
        left: 18.5rem;
        font-size: 1.8rem;
        font-weight: 400;
        line-height:1.4;
        padding: 0;
        margin:0;
    }

    .resultsRow.valjobRow .fullAddress span {font-weight: 400;}

    .resultsRow.valjobRow .fullAddress span:nth-child(2) {
        font-size: 1rem;
    }

    .resultsRow.valjobRow .primaryThumb-Wrapper {
        display: block;
        position: absolute;
        bottom:1rem;
        left:1rem;
        width: 16rem;
        height: 16rem;
        background:#f7f7f7;
    }

    .resultsRow.valjobRow .colCell.valref {
        position: absolute;
        top: 5.8rem;
        left: 18.5rem;
        right:1rem;
        font-size: 1.1rem;
        font-weight: 400;
        font-style: italic;
        color: #0f2c40;
        line-height: 2;
        background: #eeeff0;
        padding-left: .5rem;
        white-space: initial;
    }

    .resultsRow.valjobRow .colCell.valref::before {content:"Val Ref: ";font-size: 1rem;}

    .resultsRow.valjobRow .colCell.reporttype {
        text-align: right;
        position: absolute;
        top: 5.8rem;
        right: 2rem;
        font-size: 1rem;
        color: #0f2c40;
        line-height: 2.3;
        text-transform:uppercase;
        white-space: initial;
    }

    .resultsRow.valjobRow .colCell.reporttype::before { display:none; }

    .primaryThumb-Wrapper img[src="assets/images/property/addPhotos.png"] {
        margin: 2.8rem 0 0 4rem;
        width: 8rem;
        height: 8rem;
    }



    /*  ==================================================================================================== */
    /*  END SMALL DEVICES, TABLETS (MAX-WIDTH:768PX)  ====================================================== */
    /*  ==================================================================================================== */

}
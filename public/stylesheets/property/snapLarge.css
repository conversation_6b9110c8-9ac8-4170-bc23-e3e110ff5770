/*  ==================================================================================================== */
/*  START LARGE DEVICES, WIDES SCREENS (MIN-WIDTH:993PX)  ============================================== */
/*  ==================================================================================================== */

/*  ============================================================================================ */
/*  AUTH0 STYLES  ============================================================================== */
/*  ============================================================================================ */

* 	{ box-sizing: border-box; }

.auth0-lock-close-button { display:none !important; }

.auth0-lock-header {
    height:16rem !important;
}

.auth0-lock-header-bg {
    background:linear-gradient(to bottom, rgba(255,255,255,0) 35%,rgba(237,75,57,1) 90%), linear-gradient(135deg, rgba(237,185,20,1) 10%,rgba(255,255,255,0) 65%),	linear-gradient(45deg, rgba(255,255,255,0) 10%,rgba(237,108,26,1) 65%),	url(../../images/monarchWing-login.jpg) !important;
    background-size:cover !important;
    background-repeat:no-repeat !important;
    background-position:20% 0 !important;
    transform:initial !important;
    height:16rem !important;
}

.auth0-lock-header img {
    height:8rem !important;
    margin-top:2rem !important;
}

.auth0-lock-name {
    display:none !important;
    position:relative !important;
    color:#fff !important;
    margin:-4.5rem 0 0 !important;
    padding:0 1.5rem 1.5rem !important;
}

.auth0-global-message-error { background-color:rgba(237,75,57,1) !important; }
.auth0-lock-submit { background-color:rgba(237,75,57,1) !important; }


/*  ============================================================================================ */
/*  GENERIC STYLES  ============================================================================ */
/*  ============================================================================================ */

html		{ font-size:62.5% } /* Sets the base font size of the page to 1rem */
body		{ font-family:'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif; color:rgba(0,0,0,.87); line-height:1.4; background:#edeff0; }
/*body		{ border-top:2rem solid red; }*/
a 			{ color:#3f91e1; text-decoration:none; transition: opacity .2s ease; }
a:hover 	{ opacity:.8; cursor:pointer; }
h1 			{ font-size:2rem; }
h2 			{ font-size:2.4rem; font-weight:300; }
input		{ font-family:'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif !important; }
input[type=text]::-ms-clear { display: none; }

/*  ============================================================================================ */
/*  BASE STYLES  =============================================================================== */
/*  ============================================================================================ */

/*.bannerControls {
	position:absolute;
	top:1rem;
	right:1.6rem;
	padding:0;
	margin:0 auto;
	max-width:144rem;
	overflow:auto;
}*/

.bannerControls span { float:left; }

.bannerControls {
    margin-top:.5rem;
    float:right;
}

.bannerControls li {
    display:inline-block;
    font-size:1.4rem;
    line-height:3rem;
    padding:0 0 0 1.5rem;
    border-radius:0;
    margin:0 0 0 1rem;
    vertical-align:middle;
}

.bannerControls li.logOut-link {
    border-left:1px solid rgba(255,255,255,.6);
    margin-left:1rem;
}

.bannerControls i {	display:block; }

.bannerControls i:hover { color:rgba(255,255,255,.8); }

.bannerControls a {	color:rgba(255,255,255,1); }

.taDashboard-selector.advSearch-group.noMargin {
    display:block;
    padding-top:.2rem;
    padding-right:.75rem;
    border-right:1px solid rgba(255,255,255,.5);
    margin:0.3rem 0.75rem 0 0;
    float:right;
}

/*.taDashboard-selector.advSearch-group .dropdown-menu {

border:none;
  box-shadow: 19 3px 4px 0 rgba(0, 0, 0, 0.14), 0 3px 3px -2px rgba(0, 0, 0, 0.2), 0 1px 8px 0 rgba(0, 0, 0, 0.12);
}
*/

.taDashboard-selector li:nth-child(2) { display:none; }

.taDashboard-selector.advSearch-group span.fieldTwo,
.taDashboard-selector.advSearch-group .btn-group { width: 20rem; }

.taDashboard-selector.advSearch-group span.multiselect-native-select {
    padding: 2.2rem 1rem .6rem;
    margin-right: 0 !important;
    margin-bottom:-.1rem;
    box-shadow: none;
    height: 5.6rem;
    box-sizing: border-box;
}

.taDashboard-selector.advSearch-group span.multiselect-selected-text { font-size:1.3rem }

.taDashboard-selector.advSearch-group .btn-default { color:#fff; background-color:transparent; }

.taDashboard-selector.advSearch-group .btn { font-size: 1.1rem; box-shadow: none; }

.taDashboard-selector.advSearch-group .btn:hover,
.taDashboard-selector.advSearch-group .btn:focus {
    background-color:inherit;
    box-shadow: none;
    outline:0;
}

.taDashboard-selector.advSearch-group .btn .caret { top:.5rem; }

.taDashboard-selector .caret { border-top-color: #fff; }

.taDashboard-selector li:nth-child(2) { display:none; }

*[title='Ignore me for now'] { display:none; }

.righty	{ float:right; }
.lefty	{ float:left; }
.blanc  { color:rgba(255,255,255,.87); }
.hide	{ display:none; }

.stopScrolling {
    position:fixed;
    margin-top:0;
    width:100%;
}

.pageWrapper 	{
    margin:0 auto;
    width:100%;
    float:left;
    box-sizing:border-box;
}

.headerWrapper	{
    position:relative;
    color:#fff;
    background-color:rgba(30,65,150,1);
    background:linear-gradient(to right,  rgba(22,69,139,.95) 0%,rgba(18,39,86,.9) 100%), url(../../images/monarchWing.jpg);
    background-repeat:no-repeat;
    background-size:cover;
    background-position:100% 0;
    padding:1rem 2.4rem 6rem 4.2rem;
    border-top:2.4rem solid rgba(18,39,86,1);
    min-width:142rem;
    height:11rem;
}

.headerWrapper h2 {
    position:absolute;
    bottom:0;
    left:4rem;
    background-image:url(../../images/monarchLogo.png);
    background-repeat:no-repeat;
    background-size:30rem;
    background-position:0 0;
    width:32rem;
    height:7.1rem;
    text-indent:-500rem;
}
.browserAlertMessage {
    font-size: 1.4rem;
    background-color: #fc3d39;
    border: solid 1px #fc3d39;
    color: #fff;
    padding-left: 1.5rem;
    font-weight: bold;
}
.browserAlertMessage span.material-icons {
    position: absolute;
    right: 5px;
}
.contentWrapper	{
    padding:2.4rem;
    margin:0 auto;
}

.home { min-width:142rem; }

.home.salesAnalysis { min-width:inherit; }

.searchbarWrapper[style="display: none;"] + .contentWrapper	{ min-width:auto; }

.contentWrapper.hide { display:none; }

.resultsWrapper			{ margin:1.6rem auto 50rem; min-width:1366px; max-width:144rem; }

.resultsInner-wrapper	{ background-color:#f5f5f5; padding:0 0 .1rem; margin:0; }

.toolbar li		{ margin-left:0; }

.toolbar li.expandAll { margin-left: 1rem; }

.buildNumber { position:absolute; top:-1.9rem; right:1.6rem; color:rgba(255,255,255,.8); }

.spinner::before {
    position:absolute;
    left:0;
    top:0;
    content:"";
    background:#f9f9f9 url('../../images/spinner.gif') no-repeat 50% 50%;
    width:100%;
    height:100%;
    z-index:10;
}

.typeahead__container.loading .typeahead__query:before,
.typeahead__container.loading .typeahead__query:after {
    transition: all 0s linear, opacity 0.2s ease;
    position: absolute;
    z-index: 3;
    content: '';
    top: calc(50% - 8px);
    right: 2.4rem;
    margin-top: 0;
    width: 21px;
    height: 21px;
    box-sizing: border-box;
    border-radius: 500rem;
    border-style: solid;
    border-width: .1em;}

.btn .caret {
    position:absolute;
    top:1.1rem;
    right:.7rem;
    margin-left:0;
}

.advSearch-multiselect-quickSearchTa + .btn-group .btn .caret { top:1.4rem; }

.caret {
    display:inline-block;
    width:0;
    height:0;
    margin-left:.2rem;
    vertical-align:middle;
    border-top:.5rem solid #757575;
    border-right:.5rem solid transparent;
    border-left:.5rem solid transparent;
}

.mdl-button {
    transition: background-color .3s ease;
}

/*  ============================================================================================ */
/*  ENVIRONMENT STYLES  ======================================================================== */
/*  ============================================================================================ */

body.production .headerWrapper,
body.prod .headerWrapper {
    border-top:inherit;
}

body.production .headerWrapper p.buildNumber,
body.prod .headerWrapper p.buildNumber { display:none; }

.headerWrapper::before {
    position:absolute;
    top:-2.4rem;
    left:0;
    content:"Monarch " var(--env-name);
    font-weight:600;
    line-height:2.4;
    color:rgba(255,255,255,.8);
    text-transform:uppercase;
    padding-left:1rem;
    width:100%;
    height:2.4rem;
    box-sizing:border-box;
}

/*  ENVIRONMENT HEADERS  ==================================================================== */



body.local .headerWrapper::before {
    --env-name:"local";
    content:"Monarch " var(--env-name) " Development";
    color:rgba(255,255,255,1);
    background:linear-gradient(45deg, #dca 12%, transparent 0, transparent 88%, #dca 0),
    linear-gradient(135deg, transparent 37%, #a85 0, #a85 63%, transparent 0),
    linear-gradient(45deg, transparent 37%, #dca 0, #dca 63%, transparent 0) #1d8ce6;
    background-size:2.5rem 2.5rem;
}

body.development .headerWrapper::before,
body.dev .headerWrapper::before{
    --env-name:"dev";
    color:#222;
    background-color:#ffd600;
}

body.development .headerWrapper p,
body.dev .headerWrapper p { color:#222; }

body.test .headerWrapper::before {
    --env-name:"test";
    color:#fff;
    background-color:#43a047;
}

body.test2 .headerWrapper::before {
    --env-name:"test2";
    color:#fff;
    background-color:#c001da;
}

body.uat .headerWrapper::before {
    --env-name:"uat";
    color:#01579b;
    background-color:#72d0f4;
}

body.uat .headerWrapper p { color:#01579b; }

body.preprod .headerWrapper::before {
    content:"Monarch Performance Test";
    color:#fff;
    font-weight:600;
    background-color:#de4a7c;
}

body.production .headerWrapper::before,
body.prod .headerWrapper::before { display:none; }

/*  ============================================================================================ */
/*  BOX-CONTENT :BORDER-BOX ELEMENTS  ========================================================= */
/*  ============================================================================================ */

.extras,
.extrasCalculated,
.extrasCalculated div,
.icon,
input,
.masExtras,
.masExtras li,
.openProp,
.openProp .capval,
.openProp .landval,
.openProp .tfa,
.openProp .tla,
.openProp .landarea,
.resultsRow,
.simpleSearch,
.searchbarWrapper,
select,
.sortRow {
    box-sizing:border-box;
}

/*  ============================================================================================ */
/*  BOOTSTRAP MULTISELECT CSS  ================================================================= */
/*  ============================================================================================ */

span.multiselect-native-select { position:relative; }

span.multiselect-native-select select {
    position:absolute!important;
    top:3rem;
    left:50%;
    padding:0!important;
    border:0!important;
    margin:-.1rem -.1rem -.1rem -.3rem !important;
    width:.1rem!important;
    height:.1rem!important;
    overflow:hidden!important;
    clip:rect(0 0 0 0)!important;
}

.multiselect-container {
    position:absolute;
    list-style-type:none;
    padding:0;
    margin:0;
}

.multiselect-container .input-group {
    margin:1.25rem 1.2rem 1.5rem;
    width:calc(100% - 2.4rem);
}

.multiselect-container>li label {
    background-color:#fff;
    padding:0;
}


.multiselect-container>li.multiselect-group label {
    font-weight:700;
    background-color:#f0f8fc;
    padding:.3rem 2rem .3rem 2rem;
    margin:0;
    height:100%;
}


.multiselect-container>li.multiselect-item.multiselect-all {
    background-color: #fff;
    border-bottom: 3px solid #a4b3c6;
    margin: -.5rem 0 0;
}

.multiselect-container>li.multiselect-item.multiselect-all label {
    font-size: 1.2rem;
    font-weight: 700;
    background-color: #f0f8fc;
    padding: 0 1rem .5rem 1rem;
    border-bottom: 1px solid #e2e2e2;
    line-height: 3;
    background-color: #d1dae6;
    padding: 0.4rem 1rem 0 1rem;
    border-bottom: 1px solid #d1dae6;
}

.multiselect-container>li.multiselect-item.multiselect-all>a:focus label,
.multiselect-container>li.multiselect-item.multiselect-all.active>a label,
.multiselect-container>li.multiselect-item.multiselect-all.active>a:focus label {
    color: #3d91e1;
}

.multiselect-container>li.multiselect-group-clickable label { cursor:pointer; }

.multiselect-container>li>a {padding:0;text-decoration: none;}

.multiselect-container>li>a:focus { outline:0; }

.multiselect-container>li>a:focus label,
.multiselect-container>li.active>a label,
.multiselect-container>li.active>a:focus label {
    color:#fff;
    background-color:#337ab7;
}

/* .multiselect-container>li:first-of-type.active>a[tabindex="0"] label { background:#fff } */

.multiselect-container>li>a>label {
    font-weight:400;
    padding: 0 1rem;
    margin:0;
    height:100%;
    cursor:pointer;
}

.multiselect-container>li>a>label.radio,.multiselect-container>li>a>label.checkbox {
    font-size:1.1rem;
    line-height: 2.5;
    margin:0;
}

.multiselect-container>li>a>label>input[type=checkbox] {
    margin:0 .5rem 0 0;
    vertical-align:text-top;
    width:2rem;
    height:1.4rem;
}

.form-inline .multiselect-container label.checkbox,
.form-inline .multiselect-container label.radio {
    padding:.3rem 2rem .3rem 4rem;
}

.form-inline .multiselect-container li a label.checkbox input[type=checkbox],
.form-inline .multiselect-container li a label.radio input[type=radio] {
    margin:0 0 0 -2rem;
}

.multiselect-container input[type=radio] { display:none; }

.classification-parent .multiselect-container>li:nth-child(-n + 3) label {
    font-size: 1.2rem;
    font-weight:600;
    background: inherit;
    padding: 0.75rem 1rem .75rem;
}

.classification-parent .multiselect-container>li:nth-child(-n + 3)>a:focus label,
.classification-parent .multiselect-container>li:nth-child(-n + 3)>a label,
.classification-parent .multiselect-container>li:nth-child(-n + 3)>a:focus label {
    color: #fff;
}

.classification-parent .multiselect-container>li:nth-child(3) label { border-bottom:.5rem solid #e2e2e2; }

.classification-parent .multiselect-container>li:nth-child(2) label { padding: 0 1rem; }

.classification-parent .multiselect-container>li.multiselect-group label {
    font-size: 1.3rem;
    font-weight: 400;
    color: #001c2a;
    background-color:#fff;
    padding: .75rem 0 0 1rem;
}




/*  ============================================================================================ */
/*  ALERT BOX STYLES  ========================================================================== */
/*  ============================================================================================ */

.alertWrapper {
    display:flex;
    align-items:center;
    justify-content:center;
    position:fixed;
    top:0;
    right:0;
    bottom:0;
    left:0;
    background-color:rgba(255,255,255,.6);
    z-index:120;
}
.alert {
    display:flex;
    flex-direction:column;
    background-color:#fff;
    padding:1.6rem;
    width:45rem;
    height:30rem;
    box-sizing:border-box;
}
.alert h3 {
    font-size:1.8rem;
    color:#dd2c00;
    margin-bottom:.8rem;
}
.alert p {
    font-size:1.3rem;
    color:#3f3f3f;
}
.alertButtons {
    text-align:right;
    margin:auto 0 0
}

.alertForm {
    display:block;
    margin:1.6rem 0 2.4rem;
}

.alertForm label {
    display:inline-block;
    font-size:1.3rem;
    color:#0e3a83;
}

.alertForm input[type="checkbox"] {
    -webkit-appearance: none;
    border-radius: .2rem;
    margin: 1rem 0 0 .5rem;
    box-shadow: 0 0 0 0.2rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align: top;
}

.alertForm input[type="checkbox"]:checked {
    content:"\E876";
    font-family:"Material Icons";
    font-size:1.3rem;
    color:#fff;
    background:rgb(74,144,226);
    box-shadow:0 0 0 .2rem rgb(74,144,226);
    width:1.4rem;
    height:1.4rem;
}

.alertForm input[type="checkbox"]:checked::before {
    content:"\E876";
    font-family:"Material Icons";
    color:#fff;
}

.alertForm input[type="checkbox"] + label {
    display: inline-block;
    color:initial;
    padding-left: 1.7rem;
    width: 66%;
}

.alertForm input[type="checkbox"] + label span {
    display: block;
    color: rgba(0,0,0,.54);
    width: 30rem;
}

.alertForm textarea {
    font-size: 1.2rem;
    padding: .5rem;
    border: none;
    margin:1rem 0 0;
    -webkit-box-shadow: inset 0 0 0 0.1rem #d2d2d2;
    -moz-box-shadow: inset 0 0 0 0.1rem #d2d2d2;
    box-shadow: inset 0 0 0 0.1rem #d2d2d2;
    width: 100% !important;
    min-height: 8.1rem;
}

.alertForm textarea:focus {
    -webkit-box-shadow: inset 0 0 0 0.2rem rgba(51,161,230,.75);
    -moz-box-shadow: inset 0 0 0 .2rem rgba(51,161,230,.75);
    box-shadow: inset 0 0 0 0.2rem rgba(51,161,230,.75);
}

/*  ============================================================================================ */
/*  RELINK ALERT BOX STYLES  =================================================================== */
/*  ============================================================================================ */

.relinkWrapper h3 { color:#039be5; }
.relinkPhoto {
    display:-webkit-flex;
    display:flex;
    -webkit-flex-direction:row;
    flex-direction:row;
    margin:1.8rem 0 3.6rem;
}
.relinkPhoto .primaryPhoto_thumb {
    width:9rem;
    height:9rem;
}
.relinkControls {
    display:inline-block;
    position:relative;
    margin-left:2rem;
    width:calc(100% - 9rem);
}

.relink-box {
    vertical-align:top;
    font-size:1.6rem;
    padding-bottom:1rem;
    border:none;
    border-bottom:.1rem solid rgba(0,0,0,.12);
    margin-left:1rem;
    width:calc(100% - 8rem);
}
.relinkPhoto .close-icon {
    position:absolute;
    top:-.5rem;
    right:3.5rem;
}
.relink-box:not(:valid) ~ .close-icon { display:none; }
.relinkControls .relinkQupid { margin:2rem 3rem 0 0; }

.relink-validation {
    margin-left:3.7rem;
    margin-top:.7rem;
    color:red;
}

/*  ============================================================================================ */
/*  PROPERTY SEARCH BAR STYLES  ================================================================ */
/*  ============================================================================================ */

.searchbarWrapper {
    position:relative;
    background-color:#fff;
    padding:0 2.4rem;
    height:6.2rem;
    z-index:11;
}

form.searchBar {
    position:relative;
    padding-top:1.4rem;
    margin:0 auto;
    max-width:144rem;
}

.searchbarWrapper.fixed {
    position:fixed;
    top:0;
    left:0;
    width:100%;
}

.taSelect,
.simpleSearch {
    display:inline-block;
    vertical-align:text-bottom;
}

.taSelect {
    position:absolute;
    font-size:1.15rem;
    border-right:.1rem solid rgba(0,0,0,.12);
    width:14rem;
    height:3.4rem;
    z-index:1;
}

.taSelect dt {
    line-height:2;
    padding-top:.5rem;
    cursor:pointer;
}

.taSelect dt span {
    display:block;
    position:relative;
    width:100%;
}

.taSelect dt span::before {
    content:"Select Authorities"
}

.taHeader {
    padding:1.2rem 1.6rem .8rem;
    border-bottom:.1rem solid #eee;
    height:initial;
    overflow:auto;
}

.taHeader h3 {
    font-size:1.3rem;
    line-height:2.5;
}

.taList {
    display:none;
    position:relative;
    top:-3rem;
    left:-.5rem;
    background-color:#fff;
    padding:0;
    width:28rem;
    height:36.6rem;
}

.taControls {
    background-color:rgba(2,136,209,.06);
    padding:1.7rem 1.6rem 1.9rem;
    border-top:.1rem solid rgba(2,136,209,.15);
    height:initial;
}

.taControls input { margin-right:1rem; }

.taControls label { font-weight:600; }

.taSelect ul {
    padding:1.6rem 1.6rem 0;
    overflow:auto;
    width:100%;
    height:26rem;
    box-sizing:border-box;
}

.taSelect li { margin-bottom:1.2rem; }

.taSelect li span {
    display:inline-block;
    position:relative;
    top:-.2rem;
    margin-right:1rem;
}

.taSelect input[type="checkbox"] {
    padding:0;
    vertical-align:middle;
    width:inherit;
    height:inherit;
}

.taSelect label { vertical-align:middle; }

.taSelect label span {
    display:inline-block;
    font-size:1rem;
    text-align:center;
    line-height:1.3;
    background-color:#eee;
    padding:.25rem .5rem .35rem;
    border-radius:.3rem;
    margin:0 1rem;
    vertical-align:middle;
    min-width:2rem;
}

.taSelect input[type="checkbox"]:checked + label span {
    background-color:rgb(74,144,226);
    color:#fff;
}

.taSelect > i {
    position:absolute;
    top:.4rem;
    right:.8rem;
    pointer-events:none;
}

.advSearch-icon,
.loading-icon {
    position:absolute;
    left:17.15rem;
    top:0;
    padding: .5rem .75rem 0 .45rem;
    border-right:.1rem solid rgba(0,0,0,.25);
    height:3.4rem;
}

.advSearch-icon i:hover { color:rgba(51,161,230,.75); }

.simpleSearch-icon {
    position:absolute;
    left:22rem;
    top:.5rem;
    pointer-events:none;
}

.simpleSearch {
    position:relative;
    padding:0;
    width:100%;
}

.searchbarWrapper input {
    font-size:1.8rem;
    font-weight:400;
    color:#434343;
    padding:.8rem 0;
    border:none;
}

.searchbarWrapper select {
    width:6rem;
}

.close-icon {
    position:absolute;
    top:0;
    right:0;
}

.search-box:not(:valid) ~ .close-icon { display:none; }

.listBox {
    position:absolute;
    top:4.9rem;
    left:-2.5rem;
    background-color:#f9f9f9;
    padding:1.6rem 2.4rem 1.6rem;
    box-shadow:inset 0 .3rem .1rem -.2rem rgba(0, 0, 0, .2), 0 .4rem .5rem 0 rgba(0, 0, 0, .14), 0 .1rem 1rem 0 rgba(0, 0, 0, .12), 0 .2rem .4rem -.1rem rgba(0, 0, 0, .2);
    width:calc(100% - 2.3rem);
}

.listBox li {
    font-size:1.3rem;
    line-height:3;
}

.listBox li:hover {
    color:#0288d1;
    cursor:pointer;
}

.listBox li:hover i { color:#0288d1 !important; }


.listBox li i {
    margin-top:-.4rem;
    vertical-align:middle;
}

.listBox li span { margin:0 1.6rem; }

.typeahead__container {
    position:relative;
    font:1.2rem 'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif;
}

.typeahead__container button { height:2.7rem; }

.typeahead__field {
    display:inline-block;
    margin-left:9rem;
    width:calc(100% - 26.5rem);
}

.typeahead__field input {
    height:3.2rem;
    border:none;
    padding:0;
    color:#434343;
    font-size:1.8rem;
    font-weight:400;
}

.typeahead__field input :not(:valid) ~ .close-icon { display:none; }

.typeahead__list .typeahead__dropdown {
    z-index:0;
    padding:1.6rem 2.4rem 1.6rem;
    background-color:#f9f9f9;
    box-shadow:inset 0 .3rem .1rem -.2rem rgba(0, 0, 0, .2), 0 .4rem .5rem 0 rgba(0, 0, 0, .14), 0 .1rem 1rem 0 rgba(0, 0, 0, .12), 0 .2rem .4rem -.1rem rgba(0, 0, 0, .2);
}

.typeahead__result ul {
    max-height:50rem;
    overflow:auto;
}

.typeahead__list li, .typeahead__dropdown li {
    font-size:1.3rem;
    line-height:3;
}

.typeahead__list > li {
    position:relative;
    border-top:solid .1rem #f2f2f2;
}

.typeahead__list li:hover {
    color:#0288d1;
    cursor:pointer;
}

.typeahead__dropdown li:hover {
    color:#0288d1;
    cursor:pointer;
}

.typeahead__query {
    position:relative;
    z-index:0;
    width:100%;
    display:inline-block;
    vertical-align:text-bottom;
}

.typeahead__list li:hover i { color:#0288d1 !important; }

.typeahead__list li:hover span { color:#0288d1 !important; }

.typeahead__dropdown li:hover i { color:#0288d1 !important; }

.typeahead__dropdown li:hover span { color:#0288d1 !important; 	}

.typeahead__list li i {
    margin-top:-.4rem;
    vertical-align:middle;
}

.typeahead__dropdown li i {
    margin-top:-.4rem;
    vertical-align:middle;
}

.typeahead__list li span {
    display:inline-block;
    margin:0 1.6rem;
    width:10rem;
}

.typeahead__list li span.listBox-address { width:45rem; }

.typeahead__dropdown li span {
    margin:0 1.6rem;
}

.typeahead__container > .advSearch-group > label { display:none; }

.typeahead__container > .advSearch-group {
    border-right:.1rem solid #c0c0c0;
    margin:0;
    min-height:3.4rem;
    padding-top:.1rem;
}

.typeahead__container > .advSearch-group span.multiselect-native-select {
    box-shadow:none;
    margin-top:.2rem;
}

.typeahead__container > .advSearch-group .btn-group { width:15rem; }

.typeahead__container > .advSearch-group .btn {
    padding:.7rem .6rem;
    box-shadow:none;
    color: rgba(0,0,0,0.87);
    background-color: transparent;
    border-radius:.4rem;
    height:3.2rem;
    margin-top:0;
}

.typeahead__container > .advSearch-group .btn.multiselect-clear-filter { margin: 0; }

.searchDomain-switcher {
    display: inline-block;
    vertical-align:top;
}

.pageWrapper.salesWrapper .searchDomain-switcher {
    float:right;
}

.searchDomain-switcher li {
    line-height: 2.8;
    padding: 0 0.9rem 0 .75rem;
    border-radius: .25rem;
    margin: .1rem 0 0 -.1rem;
    box-shadow:0 0 0 0.1rem rgba(0,0,0,.17);
    width: 15rem;
    cursor:pointer;
    transition: background-color .15s ease;
}

.searchDomain-switcher li.propertyDomain-search {
    color:rgba(255,255,255,.87);
    box-shadow: 0 0 0 0.1rem rgba(255,255,255,.54);
}

.pageWrapper li.propertyDomain-search,
.pageWrapper.salesWrapper li.salesDomain-search {
    display: none;
}

.pageWrapper.salesWrapper li.propertyDomain-search,
.pageWrapper li.salesDomain-search {
    display: block;
}

.searchDomain-switcher li.propertyDomain-search i {
    background: url(../../images/icons8-Real-Estate-48.png) no-repeat 50% 50%;
    opacity: .87;
}

.searchDomain-switcher li.salesDomain-search i {
    background: url(../../images/icons8-Land-Sales-48.png) no-repeat 50% 50%;
    opacity: .54;
}

.searchDomain-switcher li.salesDomain-search i,
.searchDomain-switcher li.propertyDomain-search i {
    display: inline-block;
    background-size: contain;
    width: 2rem;
    height: 2rem;
    margin-top: 0.7rem;
    float: right;
}

.searchDomain-switcher li:hover { box-shadow: 0 0 0 0.2rem rgba(51,161,230,.75) }




/*  ============================================================================================ */
/*  SALES SEARCH BAR STYLES  =================================================================== */
/*  ============================================================================================ */

.pageWrapper.salesWrapper .searchbarWrapper {
    background-color: #002943;
    padding:1.4rem 2.4rem;
}

.searchbarWrapper .typeahead__container {
    position: relative;
    margin: 0 auto;
    max-width: 143.7rem;
}

.pageWrapper.salesWrapper .simpleSearch-icon,
.pageWrapper.salesWrapper .typeahead__field.simpleSearch {
    display: none;
}

.pageWrapper.salesWrapper .advSearch-icon {
    left: 0;
    border-right: none;
    padding: 0.4rem 1.2rem 0.2rem .75rem;
    border-radius: .4rem;
    margin: .1rem 0 0 -.1rem;
    box-shadow: 0 0 0 0.1rem rgba(255,255,255,.54);
    width: 15rem;
    height: 3.3rem;
    cursor:pointer;
}

.pageWrapper.salesWrapper .advSearch-icon:hover { box-shadow: 0 0 0 0.2rem rgba(51,161,230,.75); }

.pageWrapper.salesWrapper .advSearch-icon::after {
    display:inline-block;
    content:"Sales Search";
    color: rgba(255,255,255,0.87);
    line-height:2.2;
    margin-left:1rem;
    vertical-align:top
}

.pageWrapper.salesWrapper .advSea3ch-icon:hover { box-shadow: 0 0 0 0.2rem rgba(51,161,230,.75); }

.pageWrapper.salesWrapper .advSearch-icon .material-icons.md-dark { color: rgba(255,255,255,0.87); }

.pageWrapper.salesWrapper .typeahead__container > .advSearch-group .btn {
    color: rgba(255,255,255,0.87);
    background-color: transparent;
    border-radius:.4rem;
    box-shadow:  0 0 0 0.1rem rgba(255,255,255,.54);
    height:3.2rem;
    margin-top:-.2rem;
}

.pageWrapper.salesWrapper .typeahead__container > .advSearch-group .btn:hover { box-shadow:  0 0 0 0.2rem rgba(51,161,230,.75); }

.pageWrapper.salesWrapper .typeahead__container > .advSearch-group .btn .caret { border-top: .5rem solid rgba(255,255,255,.87); }

.pageWrapper.salesWrapper .typeahead__container > .advSearch-group { border-right: none; }



/*  ============================================================================================ */
/*  TA WELCOMEPAGE  ============================================================================ */
/*  ============================================================================================ */

.homeWrapper {
    background-color:#fff;
    color:#3a3a3a;
    padding:9rem 5rem;
    margin:4rem auto 5rem;
    max-width:144rem;
    overflow:auto;
}

.homeRow {
    margin-bottom:8rem;
    overflow:auto;
}

.homeRow.imageCenter {
    text-align:center;
    margin:0 auto 7rem;
}

.homeRow.imageCenter .homeText {
    display:inline-block;
    margin-bottom:3rem;
    max-width:89rem;
}

.homeRow.imageRight .homeText {
    display:inline-block;
    max-width:53rem;
}

.homeRow.imageRight img {
    display:inline-block;
    margin-left:5rem;
    float:right;
    width:inherit;
}

.homeRow.imageLeft img {
    display:inline-block;
    margin-right:5rem;
    float:left;
    width:inherit;
}

.homeRow img.border {
    padding:2rem;
    border:.1rem solid #eee;
    margin:0 auto;
    width:inherit;
}

.homeLeft {
    text-align:left;
    margin:0 auto 3rem;
    display:inline-block;
}

.homeText h1 {
    font-size:5.5rem;
    font-weight:300;
    letter-spacing:-.2rem;
    background-image:url(../../images/monarchHome-monarchLogoBug.png);
    background-repeat:no-repeat;
    background-position:50% 0;
    background-size:7.5rem;
    padding-top:8rem;
    margin-bottom:2rem;
}

.homeText h3 {
    font-size:2.8rem;
    font-weight:300;
    line-height:1.2;
    margin:3rem 0 1.5rem;
}

.homeText p {
    font-size:1.5rem;
    font-weight:300;
    line-height:2;
}

.homeText p.intro {
    font-size:2rem;
    font-weight:300;
    line-height:1.8;
    margin-bottom:2rem;
}

.moreFeatures {
    margin:5rem auto 0;
    width:80%;
}

.oneFive {
    display:inline-block;
    position:relative;
    font-size:1.45rem;
    padding-top:10rem;
    vertical-align:top;
    width:calc(20% - .3rem);
}

.oneFive.search::before,
.oneFive.trends::before,
.oneFive.sales::before,
.oneFive.mobile::before,
.oneFive.exportable::before {
    display:block;
    position:absolute;
    top:0;
    left:calc(50% - 3.95rem);
    content:"";
    background-image:url(../../images/monarchHome-iconSprite.png);
    background-repeat:no-repeat;
    background-position:0 0;
    background-size:39.5rem 7.9rem;
    width:7.9rem;
    height:7.9rem;
}

.oneFive.trends::before { background-position:-7.9rem 0; }
.oneFive.sales::before { background-position:-15.8rem 0; }
.oneFive.mobile::before { background-position:-23.7rem 0; }
.oneFive.exportable::before { background-position:-31.6rem 0; }

/*  ============================================================================================ */
/*  TA DASHBOARD  ============================================================================== */
/*  ============================================================================================ */

.dashboardWrapper {
    display:none;
    color:#37474f;
    padding:0 2rem .3rem;
    margin:4rem auto 5rem;
    min-width: 136.6rem;
    max-width:144rem;
    overflow:auto;
    box-sizing:border-box;
}

.taSummary-wrapper {
    padding:0 0 2rem;
    border-bottom:.8rem solid #37474f;
    margin-bottom:5rem;
}

.taSummary-wrapper h4 {
    font-size:.9rem;
    font-weight:600;
    color:#546e7a;
    text-transform:uppercase;
    margin-bottom:.8rem;
}

.taBranding-wrapper,
.taTotals-wrapper,
.reportCentre-wrapper {
    display:inline-block;
    margin:0 3rem 0 -.3rem;
    min-height:10rem;
    vertical-align:top;
    box-sizing:border-box;
}

.taBranding-wrapper {
    margin-right:4rem;
    width:calc(20% - 4.3rem);
}

.taTotals-wrapper {
    position:relative;
    padding-bottom:1.4rem;
    width:calc(30% - 3.3rem);
    border-bottom:.1rem solid #eee;
}

.taTotals-value {
    position:relative;
    padding-bottom:1.2rem;
    border-bottom:.1rem solid #eee;
    margin:0 0 .9rem;
}

.taTotals-value h4 span {
    font-weight:600;
    font-style:italic;
    float:right;
}

.taTotals-wrapper h3 {
    font-size:2.3rem;
    font-weight:700;
    line-height:.75;
}

.taTotals-value p {
    display:inline-block;
    position:absolute;
    bottom:.9rem;
    right:0;
    font-size:1.4rem;
    font-weight:300;
    color:#fff;
    line-height:1.3;
    padding:.2rem .5rem;
    border-radius:.2rem;
    width:7rem;
    float:right;
}

.taTotals-value p.posVal { background-color:#43a047; }
.taTotals-value p.negVal { background-color:#bf360c; }

.taTotals-value p span {
    display:block;
    font-size:1.6rem;
    font-weight:700;
    line-height:1.2;
    margin-top:0;
    float:right;
}

.revalDate {
    display:inline-block;
    margin-top:.1rem;
    width:calc(50% - .3rem);
}

.revalDate p {
    position:relative;
    font-size:1.3rem;
    font-weight:600;
    padding-left:2.2rem;
    margin-top:-.4rem;
}

.revalDate p::before {
    display:inline-block;
    position:absolute;
    top:.2rem;
    left:0;
    content:"";
    background-image:url(../../images/icon-revalDate.png);
    background-size:100%;
    width:1.5rem;
    height:1.5rem;
}

.reportCentre-wrapper {
    position:relative;
    background-color:#f3f3f3;
    border:.1rem solid #eee;
    height:23rem;
    width:calc(30% - 2rem);
}

.reportCentre-left,
.reportCentre-right {
    display:inline-block;
    background-color:#fff;
    padding:1rem 1.5rem .5rem;
    width:60%;
    height:22.8rem;
    vertical-align:top;
    box-sizing:border-box;
}

.reportCentre-right {
    background-color:inherit;
    padding:1rem 1rem 1rem 1.5rem;
    margin-left:-.3rem;
    width:40%;
}

.reportCentre-left h3 {
    font-size:1.7rem;
    font-weight:600;
    margin-bottom:1rem;
}

.reportCentre-left li {
    font-size:1.15rem;
    margin-bottom:.8rem;
}

.reportCentre-left li label{
    color:#37474f;
}

.reportCentre-left li label:hover {
    opacity:.8;
}

.reportCentre-left li::before {
    display:inline-block;
    position:relative;
    top:.5rem;
    content:"";
    background-image:url(../../images/icon-reportLink.png);
    background-size:100%;
    margin-right:1rem;
    width:1.75rem;
    height:1.75rem;
}

.reportCentre-right h3 {
    position:relative;
    font-size:1.35rem;
    font-weight:600;
    font-style:italic;
    color:#fff;
    background-color:#204178;
    padding:.3rem 1.5rem;
    margin:0 0 1.2rem -1.5rem;
}

.reportCentre-right h3::after {
    display:inline-block;
    position:absolute;
    right:0;
    top:0;
    content:"";
    border-bottom:3rem solid #f3f3f3;
    border-left:1.5rem solid transparent;
    width:0;
    height:0;
}

.reportCentre-right p {
    font-size:1.15rem;
    font-weight:400;
    line-height:1.4;
    margin-bottom:1rem;
}

.reportCentre-right label {
    display:inline-block;
    font-size:1rem;
    color:#fff;
    line-height:2.3rem;
    background-color:#fc932f;
    padding:.3rem .5rem .3rem .7rem;
    border:none;
    border-radius:.3rem;
    margin:.5rem 0 0;
    vertical-align:middle;
    width:100%;
    overflow:auto;
    cursor:pointer;
}

.reportCentre-right label:hover {
    opacity:.8;
}

.reportCentre-right label i {
    display:inline-block;
    font-size:2.2rem;
    line-height:1;
    margin:.1rem 0 0 0;
    float:right;
}

.taNames-wrapper {
    display:inline-block;
    padding:1.5rem 1.5rem 1.25rem;
    margin-right:0;
    margin-left:-.3rem;
    min-height:10rem;
    background-color:#f3f3f3;
    width:calc(20% - .3rem);
    box-sizing:border-box;
}

.taNames-wrapper h3 {
    position:relative;
    font-size:1.3rem;
    font-weight:600;
    font-style:italic;
    color:#fff;
    background-color:#204178;
    padding:.3rem 1.5rem;
    margin:-.4rem 0 0 -1.5rem;
    width:80%;
}

.taNames-wrapper h3::after {
    display:inline-block;
    position:absolute;
    right:0;
    top:0;
    content:"";
    border-bottom:3rem solid #f3f3f3;
    border-left:1.5rem solid transparent;
    width:0;
    height:0;
}

ul.qvContact {
    margin:1rem 0 1.5rem;
}

ul.qvContact i {
    display:inline-block;
    font-size:1.6rem;
    color:#78909c;
    margin-right:1rem;
    vertical-align:text-top;
}

li.qvName {
    font-size:1.2rem;
    font-weight:700;
}

li.qvRole {
    font-size:1.1rem;
    font-style:italic;
    font-weight:400;
    margin-bottom:0;
}

li.qvPhone,
li.qvMail {
    font-size:1.1rem;
    margin-top:0;
}

li.qvMail {	color:#039be5; }

li.qvMail:hover { opacity:.8; }


@media only screen and (max-width:1366px) {

    .taBranding-wrapper, .taTotals-wrapper, .reportCentre-wrapper {	margin:0 2rem 0 -.3rem; }

    .taBranding-wrapper {
        margin-right:2rem;
        width:calc(19% - 2.3rem);
    }

    .taTotals-wrapper { width:calc(30% - 2.3rem); }

    .taTotals-wrapper h3 { font-size:2rem;	}

    .revalDate p {
        font-size:1.15rem;
        padding-left:1.8rem;
        margin-top:-.2rem;
    }

    .revalDate p::before {
        top:.1rem;
        width:1.35rem;
        height:1.35rem;
    }

    .reportCentre-wrapper {
        width:calc(32% - 2.4rem);
        height:22.1rem;
    }

    .reportCentre-left,
    .reportCentre-right {
        padding:1rem 1rem .5rem;
        height:21.9rem;
    }

    .reportCentre-left li {
        font-size:1.1rem;
        margin-bottom:.7rem;
    }

    .reportCentre-left li::before {
        top:.3rem;
        margin-right:.7rem;
        width:1.35rem;
        height:1.35rem;
    }

    .reportCentre-right h3 {
        font-size:1.2rem;
        padding:.3rem .9rem;
        margin:0 0 1.2rem -.9rem;
    }

    .reportCentre-right p { font-size:1rem; }

    .reportCentre-right label {
        padding:.2rem .3rem .2rem .5rem;
    }

    .reportCentre-right label i {
        font-size:2rem;
        margin:.15rem 0 0 0;
    }

    .taNames-wrapper {
        padding:1.5rem 1rem 1.25rem;
        width:20%;
    }


    .taNames-wrapper h3 {
        font-size:1.2rem;
        padding:.3rem .9rem;
        margin:-.4rem 0 0 -.9rem;
    }

    ul.qvContact { margin:1.2rem 0 1.1rem; }

    ul.qvContact i { margin-right:.7rem; }

}

/*  ============================================================================================ */
/*  TA DASHBOARD CHARTS AND GRAPHS ============================================================= */
/*  ============================================================================================ */

.highCharts-wrapper {
    position:relative;
    text-align:center;
}

.graphRow,
.graphSection,
.graphBox,
.simpleBar ul {
    box-sizing:border-box;
    vertical-align:top;
}

.graphRow {
    position:relative;
    background-color:#fff;
    text-align: center;
    padding:0 0 3rem;
    border:.1rem solid #eee;
    margin-top:3rem;
    min-height:40rem;
    overflow:auto;
    width: 100%;
}
.dragBlockBuildingConsentsAndSubDivisions{
    font-size: 0;
}
.graphRow h2 {
    position:relative;
    font-size:1.8rem;
    font-weight:700;
    text-align:center;
    letter-spacing:-.01rem;
    background:#f2f2f2;
    padding:.8rem 0 .75rem;
    margin:0 0 3rem;
    vertical-align:top;
    width: 100%;
}

.graphRow h2::before,
.graphRow h2::after {
    display:inline-block;
    position:absolute;
    top:.6rem;
    left:1rem;
    content:"";
    background:url(../../images/grip.png);
    background-size:.6rem;
    width:1.8rem;
    height:3rem;
    opacity:.6;
}

.graphRow h2::after {
    left:inherit;
    right:1rem;
}


/*  BUILDING CONSENTS AND SUBDIVISIONS ROW ===================================================== */

.buildingConsents 				{display:inline-block;width: 20%; vertical-align:top;}
.buildingConsents h2::after 	{ display:none;}
.buildingConsents .stackedCol	{ display:inline-block; margin-left:2rem; width:60%; text-align:right;}
.buildingConsents .simpleBar	{ display:inline-block; text-align:left; width:100%; }

.subConsents 				    {display:inline-block; width: 20%;}
.subConsents h2::before 	    { display:none;}
.subConsents .stackedCol	    { display:inline-block; margin-right:2rem; width:60%; }
.subConsents .simpleBar	        { display:inline-block; text-align:left; width:100%; }

.subdivisions 					{display:inline-block; width: 60%; }
.subdivisions h2::before 		{ display:none; }
.subdivisions h2::after 		{ display:none; } 
.subdivisions .chart 			{display:inline-block; width: 100%;}
.subdivisions .simpleBar		{ display:inline-block; text-align:left; width:100%; }


/*  SALES ROW ================================================================================== */

.sales-left						{ display:inline-block; }
.sales-left .stackedCol			{ display:inline-block; margin-left:2rem; width:18rem; }
.sales-left .chart 				{ display:inline-block; margin:0 2rem; width:42rem; }

.sales-right					{ display:inline-block; }
.sales-right .chart 			{display:inline-block;width: 59rem; margin-right: 2rem; }

/*  REVISION OBJECTIONS ROW  =================================================================== */

.revObj-left					{ display:inline-block; }
.revObj-left .chart 			{ display:inline-block; width:36rem; }
.revObj-left .chart + .chart	{ width:auto; }

.revObj-right					{ display:inline-block; }
.revObj-right .chart 			{ display:inline-block; }

/*  MAINTENANCE OBJECTIONS ROW  ================================================================ */

.mainObj						{display:block;margin:0 auto;width: 100%;}
.mainObj .stackedCol			{ display:inline-block; width:18rem; }
.mainObj .simpleBar				{ display:inline-block;text-align:left; margin-left:2rem; width:180px; }

.graphBox {	overflow:auto; }

.graphBox h3 {
    font-size:1.2rem;
    font-weight:600;
    color:#546e7a;
    text-align:center;
    text-transform:uppercase;
    padding-bottom:1rem;
}

.graphBox.simpleBar h3,
.graphBox.simpleTable h3,
.graphBox.simpleLegend h3 {
    border-bottom:none;
}

/*
@media only screen and (max-width:1366px) {

    .graphRow {
        padding:0;
        margin:3rem auto 0;
        width:76rem;
        overflow:auto;
    }

    .graphRow h2 {
        position:relative;
        font-size:1.8rem;
        font-weight:700;
        text-align:right;
        letter-spacing:-.01rem;
        background:#f2f2f2;
        padding:.8rem 0 .75rem;
        margin:0;
    }


    /*  ROW ONE  =================================================================================== */

/*
.buildingConsents 				{ display:block; margin:0 auto 3rem; width:100%; overflow:auto; clear:both; }
 .buildingConsents h2 			{ margin:0; }
 .buildingConsents h2::after 	{ display:block; }
 .buildingConsents .stackedCol	{ margin:3rem 1rem 0 18.9rem; width:18rem; }
 .buildingConsents .simpleBar	{ margin:3rem 18.9rem 0 1rem; width:18rem; }

 .subdivisions 					{ display:block; margin:0 3rem 3rem; width:auto; overflow:auto; clear:both; }
 .subdivisions h2 				{ margin:0; }
 .subdivisions h2::before 		{ display:none; }
 .subdivisions h2::after 		{ display:none; }
 .subdivisions .chart 			{ margin:3rem 1rem 0 3rem; width:calc(100% - 26rem); height:31.1rem; float:left; }
 .subdivisions .simpleBar		{ margin:3rem 3rem 0 1rem; width:18rem; height:32rem; float:right; }
*/


/*  ROW TWO  =================================================================================== */

/*
.sales-left						{ display:block; margin:0 auto 3rem; width:100%; overflow:auto; clear:both; }
 .sales-left .stackedCol			{ margin:3rem 1rem 0 5rem; width:18rem; }
 .sales-left .chart 				{ margin:3rem 5rem 0 1rem; width:calc(100% - 30rem); }

 .sales-right					{ display:block; margin:0 5rem 3rem; width:auto; overflow:auto; clear:both; }
 .sales-right .chart 			{ margin:0; width:100%; height:31.1rem; }
*/

/*  ROW THREE  ================================================================================= */

/*
.revObj-left					{ display:block; margin:0 auto 3rem; width:100%; overflow:auto; clear:both; }
 .revObj-left .chart 			{ margin:3rem 1rem 0 5rem; width:calc(55% - 6rem); }
 .revObj-left .chart + .chart	{ margin:3rem 5rem 0 1rem; width:calc(45% - 6rem); }

 .revObj-right					{ display:block; margin:0 5rem 3rem; width:auto; overflow:auto; clear:both; }
 .revObj-right .chart 			{ margin:0; width:100%; height:31.1rem; }
*/

/*  ROW FOUR  ================================================================================== */

/*
.mainObj 						{ display:block; margin:0 auto 3rem; width:100%; overflow:auto; clear:both; }
.mainObj .stackedCol			{ margin:3rem 1rem 0 18.9rem; width:18rem; }
.mainObj .simpleBar				{ margin:3rem 18.9rem 0 1rem; width:18rem; }




}
*/

/*  SIMPLE BAR CHART  ========================================================================== */

.simpleBar ul {
    background:#fff;
    padding:4.4rem 1rem 4.5rem;
    border:.1rem solid #eee;
}

.simpleBar ul li { position:relative; }

.simpleBar ul li.lightBar {	margin-top:2.3rem; }

.simpleBar ul label {
    display:inline-block;
    font-size:1.3rem;
}

.simpleBar ul li.lightBar label {
    position:absolute;
    top:5.75rem;
}

.simpleBar ul h3 {
    font-size:3.6rem;
    font-weight:600;
    font-style:italic;
    text-align:left;
    line-height:1.6;
    padding-left:1rem;
    padding-bottom:0;
    margin-left:-1rem;
}

.simpleBar li.darkBar span,
.simpleBar li.lightBar span {
    display:block;
    position:relative;
    background-color:#204178;
    padding-left:1rem;
    margin-left:-1rem;
    height:2.4rem;
}

.simpleBar li.lightBar span {
    top:-8rem;
    background-color:#7eb4ec;
}

.simpleBar li.darkBar span::after,
.simpleBar li.lightBar span::after {
    position:absolute;
    right:0;
    bottom:0;
    content:"";
    border-bottom:2.3rem solid #fff;
    border-left:1rem solid transparent;
}

/*  STACKED COLUMN  ================================================================= */


.stackedCol ul {
    position:relative;
    color:#fff;
    text-align:center;
    background:#204178;
    padding:5.8rem 3rem 5.7rem;
    box-sizing:border-box;
}

.stackedCol li {
    position:relative;
    padding-bottom:1.5rem;
    z-index:1;
}

.stackedCol li:first-child { border-bottom:.1rem solid rgba(255,255,255,.5); }

.stackedCol li:last-child {
    display:block;
    position:absolute;
    top:0;
    background-color:#7eb4ec;
    padding:0;
    margin:0 -3rem;
    width:100%;
    z-index:0;
}

.stackedCol li label {
    position:absolute;
    left:10%;
    bottom:1.5rem;
    font-size:1.1rem;
    width:80%;
}

.stackedCol li h3 {
    font-size:3.6rem;
    font-weight:600;
    font-style:italic;
    color:#fff;
    padding-top:.8rem;
    border-bottom:none;
}


/*  HIGHCHARTS DONUT  ========================================================================== */

#consentschart > .highcharts-container,
#maintenanceObjectionsChart > .highcharts-container,
#salesChart > .highcharts-container{
    margin:0 auto;
}


.donutLabels {
    position:absolute;
    left:calc(50% - 7.9rem);
    top:calc(50% - 5.7rem);
    display:block;
    text-align:center;
    width:15.8rem;
    height:11.4rem;
}

.donutLabels .currentValue {
    display:block;
    font-size:4.2rem;
    font-weight:700;
    line-height:1;
}

.donutLabels .currentValue-label {
    display:block;
    font-size:1rem;
    font-weight:300;
    padding:0 2rem .75rem;
    border-bottom:.1rem solid #eee;
    margin-bottom:.25rem;
    white-space:nowrap;
}

.donutLabels .totalValue {
    display:block;
    font-size:2.2rem;
    font-weight:700;
}

.donutLabels .totalValue-label {
    display:block;
    font-size:1rem;
    font-weight:300;
    white-space:nowrap;
}

/*  HIGHCHARTS BAR  ============================================================================ */

#subdivisionchart > .highcharts-container {
    border:.1rem solid #eee;
    width:100%;
    height:30rem;
}


/*  SIMPLE TABLE  ============================================================================== */

.simpleTable table {
    font-size:1.1rem;
    border:.1rem solid #eee;
    background-color:#fff;
    margin:0 0 2rem;
    width:100%;
}

.simpleTable table + table {
    margin:0;
}

.simpleTable tr,
.simpleTable th,
.simpleTable td {
    padding:.4rem 1rem;
    vertical-align:middle;
}

.simpleTable th {
    font-size:1.2rem;
    font-weight:600;
    color:#fff;
    text-align:left;
    background-color:#204178;
}

.simpleTable td + td { font-weight:600; text-align:right; }

.simpleTable .totalRows.darkBorder td { padding:.9rem 1rem; }

.simpleTable .totalRows td {
    font-weight:600;
    color:#fff;
    line-height:1.3;
    background-color:#03a9f4;
    padding:0 1rem 1rem;
    border-top:none;
}

.simpleTable .totalRows td span {
    display:block;
    font-size:1rem;
    font-weight:400;
    margin-left:-.15rem;
}

.simpleTable .totalRows td span::before {
    content:"(";
    padding-right:.1rem;
}

.simpleTable .totalRows td span::after {
    content:")";
    padding-left:.1rem;
}

/*  SIMPLE LEGEND ============================================================================== */

.simpleLegend ul {
    font-size:1.2rem;
    line-height:1.4;
    background:#fff;
    padding:1rem 1rem 0;
    border:.1rem solid #eee;
}

.simpleLegend li { margin-bottom:1rem; }

.simpleLegend label {
    position:relative;
    display:inline-block;
    margin:0 1.2rem 0 2.4rem;
    vertical-align:middle;
    width:9rem;
}

.simpleLegend span {
    display:inline-block;
    font-size:1.1rem;
    font-weight:600;
    color:#fff;
    text-align:center;
    line-height:2;
    background:#7d807f;
    border-radius:.2rem;
    vertical-align:middle;
    width:4.2rem;
}

.simpleLegend label::before {
    position:absolute;
    left:-2.4rem;
    display:inline-block;
    content:"";
    border-radius:50%;
    width:1.6rem;
    height:1.6rem;
}

.simpleLegend label.twoLine::before { top:.9rem; }

.simpleLegend label.resiRates::before  { background-color:#30598a; }
.simpleLegend label.commRates::before  { background-color:#ba2e34; }
.simpleLegend label.lifeRates::before  { background-color:#6e3f78; }
.simpleLegend label.ruralRates::before { background-color:#5f954c; }
.simpleLegend label.otherRates::before { background-color:#e6a047; }



/*  ============================================================================================ */
/*  ADVANCED SEARCH BOX  ======================================================================= */
/*  ============================================================================================ */

.advSearch-icon.hide { display:none; }
.advSearch-icon.hide + .simpleSearch-icon { left:17.5rem; }
.advSearch-icon.hide + .simpleSearch-icon + .typeahead__field {
    margin-left:5.5rem;
    width:calc(100% - 26.5rem);
}

.advSearch-wrapper,
.advSearch-row,
.advSearch-group,
.advSearch-group label,
.advSearch-group span,
.advSearch-group input,
.advSearch-group .spacer {
    box-sizing:border-box;
    vertical-align:top;
}

.advSearch-wrapper {
    position:absolute;
    top:-3rem;
    left:-.2rem;
    background-color:#fff;
    padding:0;
    box-shadow: 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
    width:80rem;
    height:71rem !important;
    z-index:101;
    display:none;
}

.advSearch-wrapper[style*="display:block;"] + .advSearch-icon i { color:rgb(74,144,226); }

.searchType-wrapper {
    text-align:center;
    padding:1rem 0 1.5rem;
    border-bottom:1px solid #37474f;
}

.advSearch-tab label {
    display: inline-block;
    font-size: 1.1rem;
    color: #689ac6;
    color:rgba(255,255,255,.8);
    line-height: 2.8;
    background: transparent;
    padding: 0 .75rem;
    border-radius: .2rem;
    margin: 0 -0.4rem 0 0;
    box-sizing: border-box;
    cursor: pointer;
    transition: background-color .15s ease;
    width: 20rem;
    height: initial;
    box-shadow: inset 0 0 0 2px #4a90e2;
}

.advSearch-tab:first-child label {
    border-top-right-radius:0;
    border-bottom-right-radius:0;
}

.advSearch-tab:last-child label {
    border-top-left-radius:0;
    border-bottom-left-radius:0;
}

.advSearch-tab label:hover {
    color:#4a90e2;
    background: #f0f8fc;
    cursor:pointer;
}

.advSearch-tab input[type=radio] { display:none; }

.advSearch-tab input[type=radio]:checked + label {
    color:#fff;
    background:#4a90e2;
}

.searchScroll-wrapper { height:calc(100% - 27.3rem); }

.advSearch-header {
    background:#fcfcfc;
    padding:1.6rem 2.2rem 1.4rem;
    overflow:auto;
}

.advSearch-header.divider {
    background:#fcfcfc;
    padding:1.6rem 2.2rem 1rem;
    border-bottom:.1rem solid #f2f2f2;
    margin-bottom:.8rem;
}

.advSearch-header h3 {
    font-size:1.6rem;
    color:#222;
}

.advSearch-row {
    padding:.75rem 2.2rem 1rem;
    border-bottom:.1rem dashed #e2e2e2;
}

.advSearch-wrapper .advSearch-row:last-child,
.advSearch-wrapper .advSearch-row:nth-child(9) {
    padding-bottom:2.4rem;
    border-bottom:none;
}

.advSearch-row.divider {
    background:#162b3f;
    padding:.8rem 2.2rem 1.2rem;
    border-bottom:.1rem solid #eee;
    margin: -.1rem -.1rem .8rem -.1rem;
}

.advSearch-row.divider + .advSearch-row.divider {
    padding-bottom: 1.8rem;
    margin-top: -1.8rem;
}

.advSearch-row.divider.advSearchForm:nth-child(3) { margin-bottom: 0; }

.advSearch-row h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff;
    margin: 1rem 0 1.2rem;
}

.advSearch-row-dependent.disabled,
.advSearch-row-street-dependent.disabled {
    pointer-events:none;
    opacity:1;
}

.advSearch-row-dependent.disabled,
.advSearch-row-street-dependent.disabled {opacity:1;}

.advSearch-row-dependent.divider.disabled input,
.advSearch-row-dependent.divider.disabled > .advSearch-group .btn,
.advSearch-group.noMargin.advSearch-row-dependent.disabled ~ .advSearch-group .btn {
    color:#999;
    background-color:#ddd;
}

.advSearch-row-dependent.disabled input,
.advSearch-row-dependent.disabled > .advSearch-group .btn,
.advSearch-group.advSearch-row-dependent.disabled ~ .advSearch-group .btn {
    color:#999;
    background-color:#f2f2f2;
}

.advSearch-group {
    display:inline-block;
    margin-right:1.7rem;
    margin-left:-.3rem;
}

.advSearch-group.noMargin,
.advSearch-group span.noMargin {
    position:relative;
    margin-right:0;
}

.advSearch-group span.multiselect-native-select,
.advSearch-group span.multiselect-selected-text {
    margin-right:inherit !important;
    margin-left:inherit !important;
    width:inherit !important;
}

.advSearch-group span.multiselect-native-select { box-shadow:inset 0 0 0 .1rem #d2d2d2; }

.advSearch-group span.multiselect-selected-text {
    line-height: 1.1;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    width:calc(100% - 1.3rem) !important;
}

.advSearch-row.divider .advSearch-group > label { color: #ddd; }

.advSearch-group span.input-group-btn {
    position: absolute;
    top: 0;
    right: .2rem;
    border-left: inherit;
    margin: 0;
    width: 2.3rem;
}

.advSearch-group span {
    display:inline-block;
    position:relative;
    margin-right:2rem;
    margin-left:-.3rem;
    width:6.5rem;
}

.advSearch-group span.rollCount {
    position:absolute;
    top:.9rem;
    right:-1rem;
    font-size:1.2rem;
    color:#fff;
    line-height:2;
    text-align:center;
    background:#7eb4ec;
    padding:0 .8rem;
    border-radius:1.2rem;
    margin:0;
    width:inherit;
    min-width:2.4rem;
    box-shadow:0 .2rem .2rem 0 rgba(0, 0, 0, .1), .1rem 0 0 0 rgba(0, 0, 0, .1), 0 .1rem 0 0 rgba(0, 0, 0, .06);
    pointer-events:none;
}

.advSearch-group span.salesgroups-btn:hover { color:#fff; }

.advSearch-group.streetType-div .dropdown-menu,
.advSearch-group.age-div .dropdown-menu,
.advSearch-group.roofCondition-div .dropdown-menu {
    left:inherit;
    right:0;
}

.advSearch-group.noMargin,
.advSearch-group span.noMargin {
    margin-right:0;
}

.advSearch-group span.multiselect-native-select,
.advSearch-group span.multiselect-selected-text {
    margin-right:inherit !important;
    margin-left:inherit !important;
    width:inherit !important;
}

.advSearch-group span.multiselect-native-select { box-shadow:inset 0 0 0 .1rem #d2d2d2; }

.advSearch-group span.multiselect-selected-text {
    line-height: 1.2;
    padding-right:2rem;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    width:calc(100% - 1.3rem) !important;
}

.advSearch-group label {
    display:block;
    font-size: 1.1rem;
    line-height: 1.6;
    color:#0e3a83;
    width:100%;
    height:2.2rem;
}

.advSearch-group span {
    display:inline-block;
    position:relative;
    margin-right:2rem;
    margin-left:-.3rem;
    width:6.5rem;
}

.advSearch-group span.salesgroups-btn {
    font-size:1rem;
    color:#fff;
    line-height:2.6;
    text-align:center;
    border-radius:0.2rem;
    height:2.6rem;
    box-shadow:inset 0 0 0 1px #4a90e2;
    transition:background-color .15s ease;
    cursor:pointer;
}

.advSearch-group span.salesgroups-btn:hover { background-color:#4a90e2; }

.advSearch-group span.fieldTwo { width:15rem; }

.advSearch-group span.fieldFive { width:33.6rem; }

.advSearch-group label + span {	margin-left:0; }

.advSearch-group div:first-of-type  { line-height:0; }

.advSearch-group div label + span i.material-icons.md-dark {
    position: absolute;
    top: -2.4rem;
    right:.2rem;
    font-size: 1.6rem;
    line-height: 1;
    background: #5290db;
    color: #fff;
    padding: .2rem .8rem;
    border-radius: 3px;
    cursor:pointer;
}

.tightMargin + .tightMargin { margin-left:3.4rem; }

.advSearch-group.tightMargin span {	margin-right:1rem; }

.conjunctionJnxn::after {
    position:absolute;
    top:.45rem;
    right:-1.45rem;
    content:"to";
    font-size:1rem;
    color:#666;
}

.advSearch-row.divider .conjunctionJnxn::after { color: #acb1b7; }

#grossPriceDualSlider,
#propertyDistanceSlider {
    margin-right:.3rem;
}

#grossPriceDualSlider.noUi-handle:focus,
#propertyDistanceSlider.noUi-handle:focus {
    outline: 0;
    box-shadow: inset 0 0 0 2px #5692d8, inset 0 1px 7px #ebebeb, 0 3px 6px -3px #bbb;
    border-radius: 5px;
}

#grossPriceDualSlider .noUi-connect,
#propertyDistanceSlider .noUi-connect {
    background: #0e3a83;
}
#grossPriceDualSlider + span,
#propertyDistanceSlider + span {
    display: block;
    margin-top: 1.3rem;
    overflow: auto;
}

#grossPriceFrom,
#propertyFrom {
    width:10rem;
    float:left;
}

#grossPriceTo {
    width:10rem;
    float:right;
}

.advSearch-group input[type=text],
.advSearch-group input[type=number],
.advSearch-group select {
    display:block;
    font-size:1.2rem;
    background-color:#fff;
    padding:.5rem;
    border:none;
    width:100%;
    height:2.7rem;
    -webkit-box-shadow:inset 0 0 0 .1rem #d2d2d2;
    -moz-box-shadow:inset 0 0 0 .1rem #d2d2d2;
    box-shadow:inset 0 0 0 .1rem #d2d2d2;
}

.sales-search-criteria .advSearch-group input[type=text] {
    font-size: 1.2rem;
    padding: 0.5rem;
    height: 39px !important;
    border-radius: 5px;
}
.advSearch-group input[type=text] + select {
    position: absolute;
    top: .3rem;
    right: 0;
    font-size: 1.1rem;
    padding: 0 0 0 1rem;
    border: none;
    border-left: 1px solid #d2d2d2;
    width: 4.4rem;
    height: 2rem;
    line-height: 2.1;
}

.advSearch-group.icons8-clock input[type=text] + select {
    -webkit-box-shadow:none;
    -moz-box-shadow:none;
    box-shadow:none;
    margin-right:.1rem
}

.advSearch-group input[type=text].multiselect-search {
    color: #434343;
    font-weight: 400;
    box-sizing:border-box;
}

.advSearch-group input:not([type=checkbox]):focus,
.advSearch-group input:not([type=checkbox]):hover {
    -webkit-box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
    -moz-box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
    box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
}

.advSearch-group i {
    font-size:1.8rem;
    line-height:1.5;
    transition:.2s;
}

.advSearch-group i:hover { color:rgb(74,144,226); }

.advSearch-group .btn-group {
    position:relative;
    display:block;
    vertical-align:middle;
    width:15rem;
}

.advSearch-group .btn-group > .advSearch-group .btn:first-child { margin-left:0; }

.advSearch-group .btn-group > .advSearch-group .btn {
    position:relative;
    float:left;
}
.advSearch-group .btn-default {
    color:#333;
    background-color:#fff;
    border-color:#ccc;
}

.advSearch-group .btn {
    display:inline-block;
    margin-bottom:0;
    font-size:1.3rem;
    text-align:left;
    line-height:1;
    white-space:nowrap;
    vertical-align:middle;
    -ms-touch-action:manipulation;
    touch-action:manipulation;
    cursor:pointer;
    -webkit-user-select:none;
    -moz-user-select:none;
    -ms-user-select:none;
    user-select:none;
    background-image:none;
    padding:0 .5rem;
    border:none;
    border-radius:0;
    width:100%;
    box-shadow:inset 0 0 0 .1rem #d2d2d2;
}

.advSearch-group .btn:hover,
.advSearch-group .btn:focus {
    background-color:#fff;
    box-shadow:0 0 0 .2rem rgba(51,161,230,.75);
    outline: none;
}

.advSearch-wrapper .advSearch-group .btn:hover,
.advSearch-wrapper .advSearch-group .btn:focus {
    border:none;
    outline: none;
}

.advSearch-group .btn:hover .caret,
.advSearch-group .btn:focus .caret {
    border-top-color:rgba(51,161,230,1);
}

.advSearch-group button {
    line-height:inherit;
    -webkit-appearance:button;
    cursor:pointer;
    text-transform:none;
    overflow:visible;
    margin:0;
    font:inherit;
    color:inherit;
}

.advSearch-group .dropdown-menu {
    display: none;
    position: absolute;
    top: 2.6rem;
    left: 0;
    font-size: 1.4rem;
    text-align: left;
    list-style: none;
    background-color: #3d4247;
    padding: 0;
    border: .4rem solid #37474f;
    margin: -0.2rem 0 0 .1rem;
    -webkit-box-shadow: 0 0.6rem 1.2rem rgba(0, 0, 0, .175);
    box-shadow: 0 0.6rem 1.2rem rgba(0, 0, 0, .175);
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    min-width: 26rem;
    z-index: 1000;
    float: left;
    width: inherit;
}

.advSearch-group .open > .dropdown-menu { display:block; }

div.updateInformation {
    float: right;
    padding-top: 30px;
    padding-left: 20px;
    color: #214d90;
}

div.updateInformation span {
    font-weight: 600;
}

.multiselect-container>li.multiselect-item.filter {
    background:inherit;
    border-bottom: .5rem solid #e2e2e2;
}

.btn.multiselect-clear-filter {
    position:relative;
    box-shadow:inherit;
    background:inherit;
    box-shadow:none !important;
}

.btn.multiselect-clear-filter:focus {
    outline:inherit;
    box-shadow:inherit;
}

.btn.multiselect-clear-filter::after {
    position:absolute;
    top:.4rem;
    right:.4rem;
    content:'\E5C9';
    font-family:"Material Icons";
    font-size:1.5rem;
    color:rgba(0,0,0,.24);
    line-height:1;
}

.input-group {
    position:relative;
    display:table;
    border-collapse:separate;
}

.input-group-addon {
    display:none!important;
    padding:.6rem 1.2rem;
    font-size:1.4rem;
    font-weight:normal;
    line-height:1;
    color:#555;
    text-align:center;
    background-color:#eee;
    border:.1rem solid #ccc;
    border-radius:.4rem;
    white-space:nowrap;
}

.input-group-addon:first-child {
    border-right:0;
    border-top-right-radius:0;
    border-bottom-right-radius:0;
}

.input-group .form-control:last-child {
    border-top-left-radius:0;
    border-bottom-left-radius:0;
}

.input-group-btn {
    display:table-cell;
    position:relative;
    font-size:0;
    width:1%;
    white-space:nowrap;
    vertical-align:middle;
}

.input-group-btn:last-child > .btn {
    top:.2rem;
    padding:0;
    height:2.3rem;
    z-index:2;
}

.input-group-btn:last-child > .btn:hover,
.input-group-btn:last-child > .btn:focus {
    -webkit-box-shadow:none;
    -moz-box-shadow:none;
    box-shadow:none;
}

.glyphicon {
    position:relative;
    top:.1rem;
    display:inline-block;
    font-family:'Glyphicons Halflings';
    font-style:normal;
    font-weight:normal;
    line-height:1;
    -webkit-font-smoothing:antialiased;
    -moz-osx-font-smoothing:grayscale;
}

.advSearch-footer .buttonBar {
    background-color:#f0f8fc;
    text-align:right;
    margin-top:0;
}

.advSearch-footer .buttonBar > span + span { margin-left:.7rem; }

.advSearch-wrapper select {
    display:inline-block;
    margin-bottom:0;
    font-size:1.1rem;
    text-align:left;
    white-space:nowrap;
    vertical-align:middle;
    -ms-touch-action:manipulation;
    touch-action:manipulation;
    cursor:pointer;
    -webkit-user-select:none;
    -moz-user-select:none;
    -ms-user-select:none;
    user-select:none;
    background-image:none;
    padding:.6rem;
    border:.1rem solid transparent;
    border-radius:0;
    width:100%;
    height:inherit;
    box-shadow:inset 0 0 0 .1rem #d2d2d2;
}

.advSearch-group div.saleStatus {
    display: inline-block;
    line-height:1.5;
    width: 10rem;
}

.advSearch-group div.excludeHpiRtv {
    width: 20rem;
}
.saleStatus label {
    display: inline-block;
    font-size: 1.25rem;
    padding-left: .75rem;
    width: calc(100% - 3.5rem);
}

.saleStatus label span {
    display:block;
    color:rgba(0,0,0,.84);
    line-height: 2;
}
.excludeHpiRtv label span {
    width: 100%;
}

.saleStatus input[type="checkbox"] {
    -webkit-appearance:none;
    padding:0;
    border-radius:.2rem;
    margin:.55rem 0 0 .5rem;
    box-shadow:0 0 0 .2rem #d2d2d2;
    width:1.4rem;
    height:1.4rem;
    vertical-align: top;
}

.saleStatus input[type="checkbox"]:checked {
    content:"\E876";
    font-family:"Material Icons";
    font-size:1.3rem;
    color:#fff;
    background:rgb(74,144,226);
    box-shadow:0 0 0 .2rem rgb(74,144,226);
    width:1.4rem;
    height:1.4rem;
}

.saleStatus input[type="checkbox"]:checked::before {
    content:"\E876";
    font-family:"Material Icons";
    color:#fff;
}

/*  ============================================================================================ */
/*  PROPERTY PLUS AND INVESTMENTS ============================================================== */
/*  ============================================================================================ */

.investmentdetails .advSearch-row { position: relative; margin-top:1rem;}

.investmentdetails .advSearch-row:first-of-type {
    padding-top: 1rem;
    padding-bottom: 1.6rem;
    border-top: 1px solid #daebf2;
    border-bottom: 4px solid #daebf2;
    background-color: #f5fcff;
    margin-top: -1.6rem;
}

.investmentdetails .advSearch-row .advSearch-Subrow { margin: 0 0 4rem;}

.investmentdetails .advSearch-row:last-of-type .advSearch-Subrow { margin: 0;}

.investmentdetails .advSearch-row .advSearch-Subrow:last-of-type {
    display:none;
    margin: -4rem 0 1rem;
}

.investmentdetails .advSearch-row:last-of-type .advSearch-Subrow:last-of-type { margin:0; }

.risksandhazards .advSearch-group.calculated input,
.investmentdetails .advSearch-group.calculated input {
    background-color: #e3f5fc;
    box-shadow: 0 0 0 1px #afdcef;
    pointer-events: none;
}

label.investmentExtras-trigger {
    display: inline-block;
    position: absolute;
    top: 6.25rem;
    line-height: 0;
    background-color: rgba(237,241,245,1);
    padding: 0 1rem 0 1.5rem;
    border-radius: 1.2rem;
    margin: 1rem 0 0 3rem;
    height:-1rem;
}

label.investmentExtras-trigger i {
    display: inline-block;
    line-height:1;
    vertical-align:middle;
}

/*  ============================================================================================ */
/*  ADVANCED SEARCH BOX DATE PICKER  =========================================================== */
/*  ============================================================================================ */

#saleDate,
#saleInputDate {
    margin-left: 0;
}

.advSearch-group span.dateRangeSpan {
    display: inline-block;
    font-size: 1.2rem;
    line-height: 1.5;
    padding: .5rem .5rem .5rem 2.8rem;
    border: none;
    margin-right:1.4rem;
    margin-left:-.5rem;
    width: 29rem;
    height: 2.6rem;
    -webkit-box-shadow: inset 0 0 0 0.1rem #d2d2d2;
    -moz-box-shadow: inset 0 0 0 .1rem #d2d2d2;
    box-shadow: inset 0 0 0 0.1rem #d2d2d2;
}

.dateRangeSpan + .caret {
    margin:1.15rem 0 0 -3.4rem;
    pointer-events:none;
}

.daterangepicker { padding: 1rem 1rem 2.4rem; display:none; }

.daterangepicker:before, .daterangepicker:after { display:none; }

.daterangepicker.dropdown-menu {
    border-radius: 0;
    box-shadow: 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
}

.daterangepicker .input-mini {
    border: 1px solid #ccc;
    border-radius: 4px;
    color: #555;
    height: 27px;
    line-height: 1;
    display: block;
    vertical-align: middle;
    margin: 0 0 5px 0;
    padding: 0 6px 0 28px;
    width: 100%;
    border-radius: 0;
    font-size: 1.2rem;
}

#saleDate > i,
#saleInputDate > i {
    position: relative;
}

#saleDate > i::before,
#saleInputDate > i::before {
    position: absolute;
    top: -0.8rem;
    left: .4rem;
    content: "\E916";
    font-family: "Material Icons";
    font-size: 1.8rem;
    color: #999;
    line-height: 1;
}

.daterangepicker_input .input-mini.active + i::before {
    color: #357ebd;
}

.ranges {
    font-size: 1.1rem;
    float: none;
    margin: 0 0 6.4rem 0;
    text-align: left;
}

.ranges li {
    font-size: 1.1rem;
    line-height: 2.7;
    background-color: #f5f5f5;
    border: 1px solid #f5f5f5;
    border-radius: 2px;
    color: #4a90e2;
    padding: 0 1rem;
    margin-bottom: .7rem;
    cursor: pointer;
}

.ranges li:hover {
    background-color: #357EBE;
    border: 1px solid #357EBE;
    color: #fff;
}

.ranges li.active {
    background-color: #4a90e2;
    border: 1px solid #4a90e2;
    color: #fff;
}

.range_inputs {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #f0f8fc;
    text-align: left;
    padding: 1.35em 1.1rem;
    border-top: 1px solid rgba(0,0,0,0.14);
    margin: 0;
    height: 6rem;
    box-sizing: border-box;
}

.daterangepicker button.btn.btn-sm {
    display: inline-block;
    position: relative;
    font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    font-style: normal;
    color: rgb(0,0,0);
    text-transform: uppercase;
    line-height: 1;
    line-height: 30px;
    letter-spacing: 0;
    text-align: center;
    text-decoration: none;
    background: rgba(158,158,158, 0.20);
    border: none;
    border-radius: 2px;
    vertical-align: middle;
    padding: 0 1rem;
    margin: 0;
    will-change: box-shadow;
    transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 7.1rem;
    height: 3rem;
    overflow: hidden;
    outline: none;
    cursor: pointer;
}

.daterangepicker button.btn.btn-sm.btn-success {
    color: rgb(255,255,255);
    background: rgb(74,144,226);
}

.daterangepicker .calendar {
    padding-top:1rem;
    margin:0;
}

.daterangepicker .input-mini.active {
    border: 1px solid #08c;
    border-radius: 0;
}

.daterangepicker.ltr .calendar.left {
    padding-left: 2rem;
    margin-right: 1rem;
    margin-left: 2rem;
    height: 26rem;
    border-left: 1px solid #d2d2d2;
    margin: 0 1.6rem 0;
}

.daterangepicker .calendar-table { padding:.4rem 0 0; }

.daterangepicker table {
    width: 100%;
    margin: 0;
    height: 20rem;
}

.daterangepicker th.month {
    font-size: 1.6rem;
    padding-bottom: 0;
    width: auto;
}

.daterangepicker .calendar th,
.daterangepicker .calendar td {
    white-space: nowrap;
    text-align: center;
    min-width: 32px;
    vertical-align: middle;
}

.daterangepicker .table-condensed tr:nth-child(2) th {
    padding-top: 1rem;
}

.daterangepicker .daterangepicker_input i {
    position: absolute;
    left: 4px;
    top: 4px;
    width:1.8rem;
    height:2rem;
    pointer-events:none;
}

.daterangepicker_input .input-mini + i::before {
    content: "\E916";
    font-family: "Material Icons";
    font-size: 1.8rem;
    color: #999;
    line-height: 1;
}

.daterangepicker_input .input-mini.active + i::before {
    color: #357ebd;
}

.daterangepicker .calendar th {
    position: relative;
    border-radius: 0;
}

.daterangepicker .calendar th.prev.available i.glyphicon,
.daterangepicker .calendar th.next.available i.glyphicon {
    position:initial;
    width: 3.2rem;
    height: 1.8rem;
}

.daterangepicker .calendar th.prev.available i::before,
.daterangepicker .calendar th.next.available i::before {
    position:absolute !important;
    left: 0;
    top: 50%;
    content: "\E314";
    font-family: "Material Icons";
    font-size: 2.6rem;
    line-height:0;
    color: #357ebd;
    line-height: 0;
    width: 3.2rem;
}

.daterangepicker .calendar th.next.available i::before { content: "\E315"; }

.daterangepicker td.available:hover,
.daterangepicker th.available:hover {
    background-color: rgb(74,144,226) !important;
}

.daterangepicker .calendar th.prev.available:hover i::before,
.daterangepicker .calendar th.next.available:hover i::before {
    color:#fff;
}


/* -- SINGLE CALENDAR + TIME PICKER -- */

.daterangepicker.ltr.single .calendar-time {
    padding:0 0 1.5rem;
    border-bottom:1px solid #d2d2d2;
    margin-bottom:1rem;
}

.daterangepicker.ltr.single .calendar-time select {
    border: 1px solid #d2d2d2;
    font-size: 1.4rem;
    padding:.5rem 1rem;
    border-radius:.2rem;
}

.daterangepicker.ltr.single .calendar.left {
    padding:0;
    margin:1rem;
    border-left: none;
    height:31.5rem;
}

.daterangepicker.ltr.single button.btn.btn-sm { width:calc(50% - .55rem); }





/*  ============================================================================================ */
/*  ADVANCED SEARCH BOX VALIDATION  ============================================================ */
/*  ============================================================================================ */

.advSearch-row.valError > .valMessage 					 { margin-top:1.5rem; width:100%; }
.advSearch-group.valError > .valMessage 				 { width:15rem; }
.advSearch-group.valError.tightMargin > .valMessage 	 { width:100%;  }
.advSearch-group.valError > span.fieldFive ~ .valMessage { width:100%;  }

.valError .valMessage {
    display:block;
    margin:.75rem 0 .5rem;
}

.valMessage {
    display:none;
    margin-top:1rem;
}

.valMessage label {
    font-size:1.1rem;
    color:#ea2e2d;
    line-height:1.2;
}

.advSearch-group.valError input[type=text],
.advSearch-group.valError span.multiselect-native-select,
.advSearch-group.valError .btn {
    -webkit-box-shadow:inset 0 0 0 .1rem #ea2e2d;
    -moz-box-shadow:inset 0 0 0 .1rem #ea2e2d;
    box-shadow:inset 0 0 0 .1rem #ea2e2d;
}

.advSearch-group.valError .btn .caret { color:#ea2e2d; }

.advSearch-group.valError input[type=text].multiselect-search {box-shadow: inset 0 0 0 0.1rem #d2d2d2;width: 100%;}

.advSearch-group.valError .btn .caret { color:#ea2e2d; }


/*  ============================================================================================ */
/*  SALES GROUPS AND ROLLS SELECTION BOX  ====================================================== */
/*  ============================================================================================ */

.salesGroup-wrapper {
    padding:.8rem 2.4rem 1.4rem 2.4rem;
    margin-top:-.8rem;
    height:calc(100% - 12.3rem);
    overflow:auto;
}

.salesGroup-wrapper h3 {
    font-size:1.5rem;
    font-weight:600;
    padding:0 2.2rem 1.2rem 0;
    border-bottom:.1rem solid #ddd;
    margin-bottom:1.8rem;
    margin-right:2.4rem;
}

.salesGroup-wrapper .advSearch-group.divider {
    display:block;
    padding-bottom:1rem;
    margin:0 0 1rem 0;
    min-height:initial;
}

.salesGroup-wrapper .advSearch-group.noMargin {
    padding-bottom:.75rem;
    margin-left:0;
    width:100%;
}

.salesGroup-ta {
    padding-top:.75rem;
    border-top:.1rem dashed #f2f2f2;
    margin-top:1rem;
}

.salesGroup-ta:not(.hide):first-child {
    border-top:none;
    margin-top:0;
}

.salesGroup-taname {
    line-height:1;
    padding-top:.25rem;
    margin-top:1rem;
    vertical-align:top;
}

.salesGroup-taname span {
    margin:0;
    width:inherit;
}

.salesGroup-taname span input { font-size:2rem; }

.salesGroup-taname span label {
    display:inline-block;
    font-size:1.6rem;
    font-weight:600;
    color:initial;
    line-height:.9;
    margin-left:.5rem;
    width:45rem;
    white-space:nowrap;
}

.salesGroup-taname label span {
    font-size:1rem;
    color:#fff;
    line-height:1.7;
    text-align:center;
    background:rgb(74,144,226);
    padding:0 .4rem;
    border-radius:.2rem;
    margin:-.1rem .6rem 0 0;
    width:3rem;
}

.salesGroup-name,
.rollValues {
    display:inline-block;
    width:calc(100% - 24rem);
}

.salesGroup-name {
    padding-top:.25rem;
    width:23.5rem;
    vertical-align:top;
}

.salesGroup-name span {
    margin-left:0;
    width:inherit;
}

.salesGroup-name span label {
    display:inline-block;
    font-size:1.3rem;
    color:initial;
    line-height:1.4;
    margin-left:.5rem;
    width:19rem;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}

.salesGroup-name span input { font-size:2rem; }

.rollValues span.tag {
    text-align:center;
    margin:0 .3rem .4rem 0;
    width:4.6rem;
}

.rollValues legend { display:none; }


/*  ============================================================================================ */
/*  PROPERTY SEARCH NO RESULTS ================================================================= */
/*  ============================================================================================ */

.noResults-wrapper {
    background-color:#fff;
    color:#3a3a3a;
    padding:3rem 5rem;
    margin:1.6rem auto 5rem;
    max-width:144rem;
    overflow:auto;
}

.noResults-wrapper h2 {
    font-size:2.3rem;
    font-weight:400;
    color:#ef7909;
    padding-bottom:1.6rem;
    border-bottom:.1rem solid #eee;
    margin-bottom:1.6rem;
}

.noResults-wrapper p {
    font-size:1.6rem;
    font-weight:600;
}

.noResults-wrapper ul {
    font-size:1.4rem;
    list-style:initial;
    margin:1.6rem 0 1.2rem 1.8rem;
    line-height:1.8;
}

.noResults-wrapper.hide { display:none; }

/*  ============================================================================================ */
/*  PROPERTY SEARCH RESULTS HEADER ============================================================= */
/*  ============================================================================================ */

.resultsTitle {
    background-color:#fff;
    padding: .8rem 1.2rem .2rem 1.6rem;
    margin: 0;
    overflow:auto;
}

.resultsTitle h1 {
    color:#37474f;
    font-weight:700;
    margin-top:1.6rem;
}

.resultsFound { clear: both; }

.resultsFound p {
    font-size:1.3rem;
    color:#808080;
    background-color:#fff;
    padding:0 1.6rem 3rem;
    margin:0;
    overflow:hidden;
}

.resultsTitle .toolbar { margin-top:1.2rem; }

.expandAll {
    -webkit-transition:all .2s linear;
    transition:all .2s linear;
}

.expandAll.down {
    -webkit-transform:rotate(0deg);
    transform:rotate(-180deg);
}

.expandAll i { font-size:2.2rem }



/*  ============================================================================================ */
/*  PROPERTY SEARCH RESULTS BASE STYLES  ======================================================= */
/*  ============================================================================================ */

.colHeader.saleStatus,
.colCell.saleStatus,
.colHeader.nsp,
.colCell.nsp,
.colHeader.saleDate,
.colHeader.chattels,
.colCell.saleDate,
.colCell.chattels {
    display:none;
}

.colHeader,
.colCell {
    display:table-cell;
    font-size:1rem;
    text-align:right;
    vertical-align:middle;
}

.colCell { font-size:1.2rem; }

.colHeader.address,
.colCell.address {
    text-align:left;
    padding-left:1.6rem;
    width:25%;
}

.colCell.address { padding-left:0; }

.colHeader.valref,
.colCell.valref {
    width:7.5%;
}

.searchDetails-wrapper {
    display: table;
    width: 100%;
    height: 7.1rem;
}

.sortRow .searchDetails-wrapper { height: inherit; }

.colHeader.capval,
.colHeader.landval,
.colHeader.valimp,
.colHeader.landarea,
.colCell.capval,
.colCell.landval,
.colCell.valimp,
.colCell.landarea {
    display:table-cell;
    width:18.5%;
}

.colHeader.tfa,
.colHeader.tla {
    width:13%;
}

.colCell.tfa,
.colCell.tla {
    width:13%;
    height:7.1rem;
}

.colHeader.category,
.colCell.category {
    width:8.5%;
}

.resultsRow:last-child .colCell { height: 8rem; }

.resultsRow.openProp:last-child .colCell { height: inherit; }



/*  ============================================================================================ */
/*  SALES SEARCH RESULTS BASE STYLES  ========================================================== */
/*  ============================================================================================ */

.salesRow .colCell.capval,
.salesRow .colCell.landval,
.salesRow .nsptfa,
.salesRow .colCell.nsptocv,
.salesRow .colCell.salegst,
.salesRow .colCell.saleother,
.salesRow .colCell.valimp,
.salesRow .sales-trafficLights {
    display:none;
}

.salesRow .colHeader.saleStatus,
.salesRow .colCell.saleStatus,
.salesRow .colHeader.qv-col-sale-id,
.salesRow .colCell.qv-col-sale-id,
.salesRow .tfaTla-wrapper,
.salesRow .colHeader.nsp,
.salesRow .colHeader.landarea,
.salesRow .colCell.nsp,
.salesRow .colCell.landarea,
.salesRow .colHeader.saleDate,
.salesRow .colHeader.chattels,
.salesRow .colHeader.tfa,
.salesRow .colCell.saleDate,
.salesRow .colCell.chattels,
.salesRow .colCell.tfa,
.salesRow .colHeader.category,
.salesRow .colCell.category {
    display:table-cell;
}

.salesRow .colHeader.valref ,
.salesRow .colCell.valref {
    width:8.5%;
}

.salesRow .colHeader.nsp,
.salesRow .colHeader.landarea,
.salesRow .colCell.nsp,
.salesRow .tfaTla-wrapper,
.salesRow .colCell.landarea {
    width:15%;
}

.salesRow .colHeader.saleStatus,
.salesRow .colHeader.qv-col-sale-id,
.salesRow .colHeader.saleDate,
.salesRow .colHeader.chattels,
.salesRow .colHeader.tfa,
.salesRow .colCell.saleDate,
.salesRow .colCell.saleStatus,
.salesRow .colCell.chattels,
.salesRow .colCell.tfa,
.salesRow .colCell.qv-col-sale-id{
    width:13.5%;
}

.salesRow .colHeader.category,
.salesRow .colCell.category {
    width:7.5%;
}


.salesRow .tfaTla-wrapper {
    display: table;
    width: 100%;
}

.salesRow .colCell.tfa,
.salesRow .colCell.tla {
    width: 50%;
}

.salesRow .colCell.tfa span,
.salesRow .colCell.tla span {
    display:none;
}


/*  ============================================================================================ */
/*  PROPERTY SEARCH RESULTS SORTING  =========================================================== */
/*  ============================================================================================ */

.colHeader i,
.colHeader > .icon,
.colHeader span.icon,
.sortRow + i,
.colHeader.saleDate,
.colHeader.nsp,
.colHeader.chattels,
.colHeader.saleStatus {
    display:none;
}

.sortRow-wrapper {
    background-color:#fff;
    overflow:initial;
}

.sortRow {
    display:table;
    background-color:#fff;
    padding: 0 2.4rem 1rem .8rem;
    border-bottom: 0.4rem solid #ddd;
    width:100%;
}

.sortRow:hover { background-color:#fff; }

.colHeader.active {	position:relative; }

.colHeader a {
    font-weight:600;
    color:rgba(0,0,0,.54);
    line-height:3;
    text-decoration:underline;
}

.colHeader a:hover,
.colHeader.active a {
    font-weight:600;
    color:rgba(74,144,226,1);
}

.colHeader.active > .icon {
    display:inline-block;
    position:absolute;
    vertical-align:middle;
}

.colHeader.active .icon {
    display:inline-block;
    position:relative;
    top:.45rem;
}

.colHeader i.sorter {
    display:inline-block;
    font-size:1.6rem;
    margin-right:.2rem;
}


/*  ============================================================================================ */
/*  SALES SEARCH RESULTS SORTING  =========================================================== */
/*  ============================================================================================ */

.salesRow .colHeader.capval,
.salesRow .colHeader.landVal,
.salesRow .colHeader.valimp {
    display:none;
}



/*  ============================================================================================ */
/*  PROPERTY SEARCH RESULTS ROWS  ============================================================== */
/*  ============================================================================================ */

.capval div,
.extras,
.fullAddress div,
.fullAddress .md-link,
.fullAddress ul,
.landval div,
.nsptocv,
.occupier,
.photoGallery_propSearch,
.qpid,
.resultsRow .toolbar,
.revalRates,
.reval-nsptorcv,
.salegst,
.saleother,
.sales-trafficLights,
.tapid,
.valimp,
.valimp div {
    display:none;
}

.resultsRow {
    display:table;
    background:#fff;
    padding:0 2.4rem 0 .8rem;
    margin:0;
    border-bottom:.1rem solid #f2f2f2;
    height:7.2rem;
    width:100%;
    -moz-transition:all .1s linear;
    -webkit-transition:all .1s linear;
    transition:all .1s linear;
}

.resultsRow:hover {
    background-color:#f9f9f9;
    color:rgba(50,135,200,1);
    cursor:pointer;
}

.resultsRow:last-child {
    padding-bottom:.8rem;
    border-bottom:initial;
    height:8rem;
}

.primaryThumb-Wrapper {
    display:inline-block;
    position:relative;
    width:5.5rem;
    height:5.5rem;
    vertical-align:middle;
}

.primaryThumb-Wrapper img {
    width:100%;
    height:100%;
}

.fullAddress {
    position:relative;
    display:inline-block;
    font-weight:700;
    color:#214d90;
    padding:1.2rem .7rem 1rem 1.2rem;
    border-radius:.5rem;
    margin-left:.4rem;
    vertical-align:middle;
    width:calc(100% - 7.5rem);
}

.fullAddress span:nth-child(2) {
    display:block;
    font-size:1rem;
}

.resultsRow:hover .fullAddress {
    padding: .85rem 1rem .65rem;
    margin-left: .6rem;
    box-shadow:0 0 0 .15rem rgba(74,144,226,.25);
    box-sizing:border-box;
}

.resultsRow:hover .fullAddress:hover {
    color:#214d90;
    background:rgba(255,255,255,.25);
    box-shadow:0 0 0 .15rem rgba(255,111,0,.5);
}

.resultsRow:hover .fullAddress::after {
    position:absolute;
    top:.9rem;
    right:.8rem;
    content:"";
    background-image:url(../../images/monarchLogo-mini.png);
    background-repeat:no-repeat;
    background-size:100%;
    width:2rem;
    height:2rem;
}

.resultsRow:hover .fullAddress:hover::after { color:#fc932f; }

.resultsRow.salesRow.pendingSale { background-color: rgba(255,255,245,1); }

.resultsRow.salesRow.pendingSale:hover {
    background-color: rgba(255,255,230,1);
    color: #1d5580;
}

.resultsRow.salesRow.pendingSale:hover .fullAddress { background-color: rgba(255,255,255,.8); }

.resultsRow.salesRow.unconfirmedSale {
    color: #fff;
    background-color: rgba(205,50,0,.75);
}

.resultsRow.salesRow.unconfirmedSale:hover {
    background-color: rgba(205,50,0,.9);
}

.resultsRow.salesRow.unconfirmedSale .fullAddress { color: #fff; }

.resultsRow.salesRow.unconfirmedSale:hover .fullAddress {
    background-color: rgba(255,255,255,.2);
    box-shadow: none;
}


/*  ============================================================================================ */
/*  PROPERTY SEARCH RESULTS OPEN PROPERTY CARD  ================================================ */
/*  ============================================================================================ */

.listingControls li.md-sales,
.openProp .category,
.openProp .qpid,
.openProp .valref,
.openProp .legaldesc,
.openProp.noThumbnails .photoGallery_propSearch,
.openProp.revalOn.noThumbnails .photoGallery_propSearch,
.openProp.salesRow.revalOn.noThumbnails .photoGallery_propSearch {
    display:none;
}

.openProp {
    display:block;
    position:relative;
    background-color:#fff;
    padding:1.6rem 1rem 0;
    margin:.8rem -1rem 1.15rem;
    height:inherit;
    width:calc(100% + 2rem);
}

.openProp:last-of-type {
    padding-bottom:0;
    margin-bottom: -0.5rem;
}

.openProp::before {
    position: absolute;
    top: -.9rem;
    left: 0;
    content: "";
    background-color: #edeff0;
    border-bottom: .1px solid #ececec;
    width: 100%;
    height: .9rem;
}

.sortRow-wrapper + .openProp::before {
    top:-.8rem;
    height: .8rem;
}

.openProp::after {
    position: absolute;
    bottom: -1.15rem;
    left: 0;
    content: "";
    background-color:#edeff0;
    background: linear-gradient(to bottom, rgba(0,0,0,.25) 0%, rgba(249,249,249,1) 30%, rgba(249,249,249,1) 100%);
    width: 100%;
    height: 1.15rem;
}

.openProp:last-of-type::after { display:none; }

.openProp:hover { background-color:#fff; }

.openProp.resultsRow:last-child {
    padding-bottom: 0;
    border: none;
    height: inherit;
}

.openProp .toolbar {
    display:block;
    position:absolute;
    top:1.2rem;
    right:1.6rem;
    margin:0;
}

.openProp .address,
.openProp .capval,
.openProp .landarea,
.openProp .landval,
.openProp .tapid,
.openProp .tfa,
.openProp .tla,
.openProp .valimp {
    display:inline-block;
    text-align:left;
    width:inherit;
}

.openProp .address {
    display:block;
    max-width:60%;
}

.openProp .primaryThumb-Wrapper { display:none; }

.openProp .fullAddress {
    font-size:1.8rem;
    margin:-.8rem 0 0 -.2rem;
    min-width:50rem;
}

.openProp:hover .fullAddress {
    padding:1rem .5rem 1rem 1rem;
    margin:-.6rem .2rem 0 0;
}

.openProp .fullAddress span { font-weight:600; }

.openProp .fullAddress span:nth-child(2) {
    display:block;
    font-size:1.3rem;
    margin-bottom:1rem;
}

.openProp .fullAddress .qpid,
.openProp .fullAddress .valref,
.openProp.salesRow .fullAddress .legaldesc,
.openProp.salesRow .fullAddress .sa-category,
.certificate-title-detail .fullAddress .div-land-district,
.certificate-title-detail .fullAddress .title-status {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #204d90;
    line-height: 1.9;
    text-align: center;
    background-color: rgba(2,136,209,.06);
    padding: 0 .5rem;
    border: .1rem solid rgba(2,136,209,.15);
    border-radius: .3rem;
    margin: 0 .8rem 0 0;
    vertical-align: top;
    min-width: 15.3rem;
    max-width: calc(100% - 33rem);
    overflow: hidden;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: initial;
    box-sizing: border-box;
    pointer-events: none;
    float: left;
}

.openProp.salesRow .fullAddress .legaldesc { min-width:18rem; }

.openProp .fullAddress .valref::before {
    content:"Val Ref: ";
    font-weight:400;
}

.openProp .fullAddress .qpid::before {
    content:"QPID: ";
    font-weight:400;
}

.openProp .fullAddress .legaldesc::before {
    content:"Legal Desc: ";
    font-weight:400;
}

.openProp .fullAddress .sa-category::before {
    content:"Category: ";
    font-weight:400;
}

.openProp .fullAddress .occupier {
    display:block;
    font-size:1.2rem;
    color:#214d90;
    margin-top:1rem;
    float:left;
    clear:both;
}

.openProp .fullAddress .occupier::before {
    content:"Occupier:";
    font-size:1.1rem;
    font-weight:400;
    padding-right:.2rem;
    text-transform:uppercase;
}

.openProp .fullAddress .occupier li {
    display:inline-block;
    font-weight:600;
}

.openProp .fullAddress .occupier li:last-child:not(:empty)::before {
    content:", ";
    margin-left:-.3rem;
}

.openProp .searchDetails-wrapper {
    display: block;
    position:relative;
    color: #fff;
    background-color: #283c64;
    margin-top:1rem;
    height: 8.4rem;
}

.openProp.maoriLand .searchDetails-wrapper { background:#7f0707; }

.openProp .capval,
.openProp .landarea,
.openProp .landval,
.openProp .tfa,
.openProp .tla,
.openProp .valimp {
    position:relative;
    font-size:1.7rem;
    line-height: 1.35;
    padding:2.7rem 0 2.5rem 1rem;
    border-right:.1rem solid rgba(255,255,255,.15);
    width:16.6%;
    height:8.4rem;
    box-sizing:border-box;
    float:left;
}

.openProp .capval::after,
.openProp .landarea::after,
.openProp .landval::after,
.openProp .tfa::after,
.openProp .tla::after,
.openProp .valimp::after {
    position:absolute;
    top: 1.1rem;
    bottom: inherit;
    left: 1.1rem;
    font-size: 1.05rem;
    color:#fff;
    font-weight:300;
    padding-bottom:.3rem;
    width:calc(100% - 2rem);
}

.openProp .capval::after 	{ content:"Capital Value"; 	  		}
.openProp .landarea::after  { content:"Land Area (Ha)";		 	}
.openProp .landval::after  	{ content:"Land Value"; 	  		}
.openProp .tfa::after 		{ content:"Total Floor Area"; 		}
.openProp .tla::after  		{ content:"Total Living Area"; 		}
.openProp .valimp::after 	{ content:"Value of Improvements"; 	}

.openProp .capval div,
.openProp .landval div,
.openProp .valimp div {
    display:block;
    font-size:1.4rem;
    font-weight:300;
    color:#fff;
    line-height: 1.5;
}

.openProp .capval div span,
.openProp .landval div span,
.openProp .valimp div span {
    font-size:1rem;
    padding-left:.2rem;
}

.openProp .capval div sup,
.openProp .landval div sup,
.openProp .valimp div sup {
    font-size:.8rem;
    padding-left:.2rem;
}

.openProp .landarea {
    line-height: 1.35;
    overflow-wrap: break-word;
    word-wrap: break-word;
    width:17.01%;
}

.openProp .tfa { width:16.66%; }

.openProp .tla {
    border-right:none;
    width:16.26%;
}

.openProp .tfa span,
.openProp .tla span {
    font-size:1.5rem;
    padding-left:.3rem;
}
.openProp .tfa span sup,
.openProp .tla span sup {
    font-size:1.1rem;
    padding-left:.2rem;
}

.openProp .photoGallery_propSearch {
    display:block;
    background: rgba(237,241,245,.2);
    margin:.8rem 0 0;
    width:100%;
    overflow:auto;
    white-space:nowrap;
}

.thumbWrapper {
    display:inline-block;
    margin-right:.2rem;
    width: 11.2rem;
    height: 7.8rem;
    vertical-align:middle;
}


/*  PROPERTY CARD EXTRA DETAILS  =============================================================== */

.openProp .extras {	display:block ; }

.openProp .extras .md-landMas {
    font-size: .9rem;
    padding: .7rem 0 .4rem 0;
    border:none;
    margin: 0 0 0 -.3rem;
    width: calc(100% + 0.5rem);
}

.openProp .extras .md-landMas li {
    padding-left: 3.4rem;
    padding: 0.4rem 0.5rem 0.4rem 3.6rem;
    margin-bottom: 0.5rem;
    margin-left: .25rem;
    width: calc(16.5% - .25rem);
    background: rgba(237,241,245,.8);
}

.openProp .extras .md-landMas strong {
    font-size: 1.2rem;
    margin-top:.2rem;
}

.openProp .extras .md-landMas li:before {
    top:.8rem;
    left:.8rem;
    font-size:2rem;
    width:2rem;
    height:2rem;
}


/*  REVISION VALUES  =========================================================================== */

.openProp.revalOn .revalRates {
    display:inline-block;
    color:#283c64;
    background: rgba(74,144,226,.15);
    margin:0 0 -.3rem;
    width:49.8%;
    height:8.4rem;
}

.openProp.revalOn.noThumbnails .revalRates { width: 100%; }

.openProp.revalOn .reval-capval,
.openProp.revalOn .reval-landval,
.openProp.revalOn .reval-valimp {
    position:relative;
    font-size:1.7rem;
    padding:2.7rem 0 0 1rem;
    border-right:.1rem solid rgba(255,255,255,.4);
    width:33.33%;
    height:8.4rem;
    box-sizing:border-box;
    float:left;
}

/*.openProp.revalOn .reval-valimp { border-right:none; }*/

.openProp.revalOn.noThumbnails .reval-capval,
.openProp.revalOn.noThumbnails .reval-landval,
.openProp.revalOn.noThumbnails .reval-valimp {
    width:16.6%;
}

.openProp.revalOn .reval-capval div,
.openProp.revalOn .reval-landval div,
.openProp.revalOn .reval-valimp div {
    display:block;
    font-size:1.4rem;
}

.openProp.revalOn .reval-capval div span,
.openProp.revalOn .reval-landval div span,
.openProp.revalOn .reval-valimp div span {
    font-size:1rem;
    padding-left:.2rem;
}

.openProp.revalOn .reval-capval div sup,
.openProp.revalOn .reval-landval div sup,
.openProp.revalOn .reval-valimp div sup {
    font-size:.8rem;
    padding-left:.2rem;
}

.openProp.revalOn .reval-capval > span,
.openProp.revalOn .reval-landval > span,
.openProp.revalOn .reval-valimp > span {
    position: absolute;
    right: .5rem;
    font-size: 1.1rem;
    color: #fff;
    text-align:right;
    line-height: 1.5;
    background: #4caf50;
    padding: 0 .3rem 0 1rem;
    border: .1rem solid #4caf50;
    margin-top: 0.3rem;
    min-width:4.6rem;
}

.openProp.revalOn .reval-capval > span.valueDown,
.openProp.revalOn .reval-landval > span.valueDown,
.openProp.revalOn .reval-valimp > span.valueDown {
    background-color:#fc3d39;
    border: .1rem solid #fc3d39;
}

.openProp.revalOn .reval-capval > span.valueUp::before,
.openProp.revalOn .reval-capval > span.valueDown::before,
.openProp.revalOn .reval-landval > span.valueUp::before,
.openProp.revalOn .reval-landval > span.valueDown::before,
.openProp.revalOn .reval-valimp > span.valueUp::before,
.openProp.revalOn .reval-valimp > span.valueDown::before {
    position: absolute;
    left: 0.2rem;
    top: calc(50% - .2rem);
    content: "";
    border-left: .3rem solid transparent;
    border-right: .3rem solid transparent;
    width: 0;
    height: 0;
}

.openProp.revalOn .reval-capval > span.valueUp::before,
.openProp.revalOn .reval-landval > span.valueUp::before,
.openProp.revalOn .reval-valimp > span.valueUp::before {
    border-bottom: .4rem solid #fff;
}

.openProp.revalOn .reval-capval > span.valueDown::before,
.openProp.revalOn .reval-landval > span.valueDown::before,
.openProp.revalOn .reval-valimp > span.valueDown::before {
    top: calc(50% - 0.2rem);
    border-top: .4rem solid #fff;
}

.openProp.revalOn .reval-capval::after,
.openProp.revalOn .reval-landval::after,
.openProp.revalOn .reval-valimp::after {
    position:absolute;
    top:1.1rem;
    bottom:inherit;
    left:1.1rem;
    font-size:1rem;
    padding-bottom:.3rem;
    width:calc(100% - 2rem);
}

.reval-capval::after 	{ content:"Revision Capital Value"; }
.reval-landval::after  	{ content:"Revision Land Value"; }
.reval-valimp::after 	{ content:"Revision Value of Improvements"; }

.openProp.revalOn .photoGallery_propSearch {
    display:inline-block;
    background-color: rgba(237,241,245,.2);
    width:calc(50.92% - 1.5rem);
    margin:.5rem 0 0 .2rem;
    vertical-align:top;
}


@media only screen and (max-width:1366px) {

    .resultsInner-wrapper { margin: 0; }

    .openProp {
        margin: .9rem 0 1.1rem;
        width: 100%;
    }

}



/*  ============================================================================================ */
/*  SALES SEARCH RESULTS OPEN PROPERTY CARD  =================================================== */
/*  ============================================================================================ */

.resultsRow.openProp.salesRow.pendingSale,
.resultsRow.openProp.salesRow.unconfirmedSale {
    background-color: #fff;
}

.resultsRow.openProp.salesRow.pendingSale:hover,
.resultsRow.openProp.salesRow.unconfirmedSale:hover {
    border-bottom:.1rem solid #f2f2f2;
}

.resultsRow.openProp.salesRow.pendingSale:hover .fullAddress,
.resultsRow.openProp.salesRow.unconfirmedSale:hover .fullAddress {
    box-shadow: inset 0 0 0 0.15rem rgba(74,144,226,.25);
}

.resultsRow.openProp.salesRow.pendingSale:hover .fullAddress:hover,
.resultsRow.openProp.salesRow.unconfirmedSale:hover .fullAddress:hover {
    box-shadow: inset 0 0 0 0.15rem rgba(255,111,0,.5);
}

.resultsRow.openProp.salesRow.unconfirmedSale .fullAddress { color: #214d90; }


.openProp.salesRow .colCell.category,
.openProp.salesRow .colCell.qv-col-sale-id,
.openProp.salesRow .colCell.saleStatus,
.openProp.salesRow .colCell.saleDate {
    display:none;
}

.openProp.salesRow .colCell.capval,
.openProp.salesRow .colCell.landarea,
.openProp.salesRow .colCell.landval,
.openProp.salesRow .colCell.valimp {
    display:inline-block;
}

.openProp.salesRow .fullAddress {
    font-size: 1.8rem;
    margin-bottom: 0;
    width: auto;
}

.openProp .fullAddress .md-link { display:block; }

.openProp.salesRow .fullAddress .occupier { display: none; }

.openProp.salesRow .fullAddress .lastReval-date {
    display:block;
    font-size:1.2rem;
    color:#214d90;
    margin-top:1rem;
    float:left;
    clear:both;
}

.openProp .fullAddress .lastReval-date::before {
    content:"Rating Valuation Date: ";
    font-size:1.1rem;
    font-weight:400;
    padding-right:.2rem;
    text-transform:uppercase;
}

.openProp .fullAddress .lastReval-date li {
    display:inline-block;
    font-weight:600;
}

.openProp.salesRow .fullAddress .saleId {
    display:block;
    font-size:1.2rem;
    color:#214d90;
    margin-top:1rem;
    float:right;
    clear:both;
}

.openProp .fullAddress .saleId::before {
    content:"Sale Id: ";
    font-size:1.1rem;
    font-weight:400;
    padding-right:.2rem;
    text-transform:uppercase;
}

.openProp .fullAddress .saleId li {
    display:inline-block;
    font-weight:600;
}

.openProp.salesRow .sales-trafficLights {
    display: block;
    position: absolute;
    top: 6.7rem;
    left:calc(59% - 1rem);
    padding: .6rem 0 0.6rem .9rem;
    border-radius: 0.5rem;
    margin-left: 0.6rem;
    width: 40%;
}

.openProp.salesRow .sales-trafficLights .qv-col-sale-id,
.openProp.salesRow .sales-trafficLights .saleClassification,
.openProp.salesRow .sales-trafficLights .saleDate,
.openProp.salesRow .sales-trafficLights .saleStatus,
.openProp.salesRow .sales-trafficLights .saleAnalysis {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #204d90;
    line-height: 1.9;
    text-align: center;
    background-color: rgba(2,136,209,.06);
    padding: 0 .25rem;
    border: .1rem solid rgba(2,136,209,.15);
    border-radius: .3rem;
    margin: 0 .8rem 0 -.2rem;
    vertical-align: top;
    width: calc(20% - .6rem);
    box-sizing: border-box;
    pointer-events: none;
    float: left;
}

.openProp .sales-trafficLights .qv-col-sale-id::before,
.openProp .sales-trafficLights .saleClassification::before,
.openProp .sales-trafficLights .saleDate::before,
.openProp .sales-trafficLights .saleStatus::before,
.openProp .sales-trafficLights .saleAnalysis::before {
    font-weight: 400;
}

.openProp .sales-trafficLights .qv-col-sale-id::before, .salesAnalysis-form .openProp .sales-trafficLights .qv-col-sale-id::before 	{ content: "Sale ID: "; 	}
.openProp .sales-trafficLights .saleClassification::before, .salesAnalysis-form .openProp .sales-trafficLights .saleClassification::before 	{ content: "Classification: "; 	}
.openProp .sales-trafficLights .saleDate::before, .salesAnalysis-form .openProp .sales-trafficLights .saleDate::before 						{ content: "Date: "; 			}
.openProp .sales-trafficLights .saleStatus::before, .salesAnalysis-form .openProp .sales-trafficLights .saleStatus::before  				{ content: "Status: "; 			}
.openProp .sales-trafficLights .saleAnalysis::before, .salesAnalysis-form .openProp .sales-trafficLights .saleAnalysis::before 				{ content: "Analysis: "; 		}



.openProp.salesRow .sales-trafficLights .saleClassification.classTwo,
.openProp.salesRow.pendingSale .sales-trafficLights .saleStatus {
    color:#4e342e;
    background-color: rgba(255,200,40,1);
    border: .1rem solid rgba(225,175,0,1);
}

.openProp.salesRow .sales-trafficLights .saleClassification.classThree,
.openProp.salesRow.unconfirmedSale .sales-trafficLights .saleStatus {
    color:#fff;
    background-color: rgba(190,55,15,1);
    border: .1rem solid rgba(165,45,10,1);
}



.openProp.salesRow .sales-trafficLights .vendPurchase,
.openProp.salesRow .sales-trafficLights .saleId {
    display: block;
    font-size: 1.2rem;
    color: #234D93;
    margin-top: 1rem;
    float: left;
    clear: both;
}

.openProp.salesRow .sales-trafficLights .vendPurchase li {
    display: inline-block;
    font-weight: 600;
}

.openProp.salesRow .sales-trafficLights .vendPurchase::before {
    content: "Vendor/Purchaser:";
    font-size: 1.1rem;
    font-weight: 400;
    padding-right: .5rem;
    text-transform: uppercase;
}

.openProp.salesRow .sales-trafficLights .saleId {
    font-weight:600;
    padding-right: .5rem;
    margin-top: .5rem;
}

.openProp.salesRow .sales-trafficLights .saleId::before {
    content: "Sale ID:";
    font-weight:400;
}

.openProp.salesRow .searchDetails-wrapper {width: 60.66%;}

.openProp.salesRow .colCell.capval,
.openProp.salesRow .colCell.landarea,
.openProp.salesRow .colCell.landval {
    width:22%;
}

.openProp.salesRow .colCell.valimp { width:25%; }


.openProp.salesRow .colCell.landarea {
    width:31%;
}

.openProp.salesRow .tfaTla-wrapper {
    display: inline-block;
    position: absolute;
    bottom: 1rem;
    left: calc(69% + 1.1rem);
    width: auto;
}

.openProp.salesRow .colCell.tfa,
.openProp.salesRow .colCell.tla {
    display: inline-block;
    font-size: 1.4rem;
    font-weight: 300;
    border:none;
    width: auto;
    height: auto;
    line-height: 2.1;
    padding: 0 0 0 2.3rem;
    margin-right:1.2rem;
}

.openProp.salesRow .tfa::after,
.openProp.salesRow .tla::after {
    position: absolute;
    top: inherit;
    bottom: 0;
    left: 0;
    content: "TFA";
    font-size: 1.05rem;
    color: #fff;
    font-weight: 300;
    padding-bottom: .2rem;
    width: calc(100% - 2.8rem);
}

.openProp.salesRow .tfa::after { content: "TFA"; }
.openProp.salesRow .tla::after { content: "TLA"; }

.openProp.salesRow .tfa span,
.openProp.salesRow .tla span {
    display:inline-block;
    font-size: 1rem;
    padding-left: .2rem;
}

.openProp.salesRow .tfa span sup,
.openProp.salesRow .tla span sup {
    font-size: 1.1rem;
    padding-left: .2rem;
    font-size: .8rem;
    padding-left: .2rem;
}

.openProp.salesRow .nsptocv {
    display: block;
    position: absolute;
    left: 100%;
    top: 0;
    font-size: 1.7rem;
    line-height: 1.35;
    text-align: left;
    background: #002943;
    padding: 2.7rem 0 2.5rem 1rem;
    width: calc(66% - 1rem);
    height: 8.4rem;
}

.openProp.salesRow .nsp,
.openProp.salesRow .gsp,
.openProp.salesRow .chattels,
.openProp.salesRow .salegst,
.openProp.salesRow .saleother {
    display: block;
    position: absolute;
    font-size: 1.7rem;
    line-height: 1.35;
    text-align:left;
    padding: 2.7rem 0 2.5rem 1rem;
    border-left: .1rem solid rgba(255,255,255,.15);
    width: 11.5%;
    height: 8.4rem;
    box-sizing: border-box;
    z-index:1;
}

.openProp.salesRow .nsp 		{ left: calc(109% + .5rem); width:22.5%; }
.openProp.salesRow .chattels 	{ left: calc(128.5% + .5rem); }
.openProp.salesRow .salegst 	{ left: calc(141% + .5rem);   }
.openProp.salesRow .saleother 	{ left: calc(153.5% + .5rem); }

.openProp.salesRow .nsp::after,
.openProp.salesRow .gsp::after,
.openProp.salesRow .nsptocv::after,
.openProp.salesRow .chattels::after,
.openProp.salesRow .salegst::after,
.openProp.salesRow .saleother::after,
.openProp.salesRow.revalOn .reval-nsptorcv::after {
    position: absolute;
    top: 1.1rem;
    bottom: inherit;
    left: 1.1rem;
    font-size: 1.05rem;
    font-weight: 300;
    color: #fff;
    padding-bottom: .3rem;
    width: calc(100% - 1.1rem);
}

.openProp.salesRow .nsp::after 					  { content: "Net Sale Price"; }
.openProp.salesRow .gsp::after 					  { content: "Gross Sale Price"; }
.openProp.salesRow .nsptocv::after 				  { content: "NSP/CV"; }
.openProp.salesRow .chattels::after 			  { content: "Chattels"; }
.openProp.salesRow .salegst::after 				  { content: "GST"; }
.openProp.salesRow .saleother::after 			  { content: "Other"; }
.openProp.salesRow.revalOn .reval-nsptorcv::after { content: "NSP/RCV"; color:#283c64; }

.openProp.salesRow .nsp div {
    display: block;
    font-size: 1.4rem;
    font-weight: 300;
    color: #fff;
    line-height: 1.5;
}

.openProp.salesRow .nsp div span {
    font-size: 1rem;
    padding-left: .2rem;
}

.openProp.salesRow.revalOn .revalRates {width: calc(66.4% + .1rem);}

.openProp.salesRow.revalOn .reval-nsptorcv {
    display: block;
    position: relative;
    font-size: 1.7rem;
    padding: 2.7rem 0 0 1rem;
    border-left: .1rem solid rgba(255,255,255,.9);
    margin-left: calc(28.2% - 0.1rem);
    width: calc(8.8% - 1rem);
    height: 8.4rem;
    box-sizing: border-box;
    float: left;
}

.openProp.salesRow.revalOn .reval-capval,
.openProp.salesRow.revalOn .reval-landval,
.openProp.salesRow.revalOn .reval-valimp {
    position: relative;
    font-size: 1.7rem;
    padding: 2.7rem 0 0 1rem;
    border-right: .1rem solid rgba(255,255,255,.9);
    width: 20.1%;
    height: 8.4rem;
    box-sizing: border-box;
    float: left;
}

.openProp.salesRow.revalOn .reval-valimp {width: 22.8%;}

/*.openProp.salesRow.revalOn.noThumbnails .reval-nsptorcv {
    border-right: .1rem solid rgba(255,255,255,.3);
    margin-left: calc(26% + .15rem);
    width:8.8%;
}

.openProp.salesRow.revalOn.noThumbnails .reval-capval,
.openProp.salesRow.revalOn.noThumbnails .reval-landval,
.openProp.salesRow.revalOn.noThumbnails .reval-valimp {
    width: calc(21% - .05rem);
}*/

.openProp.salesRow.revalOn .photoGallery_propSearch {
    display: inline-block;
    margin: .5rem 0 0 .3rem;
    width: calc(33.8% - 1rem);
    vertical-align: top;
    background: rgba(237,241,245,.2);
}

@media only screen and (max-width:1366px) {

    .salesRow .reval-valimp::after 	{ content:"Revision Value of Imps."; }

    .openProp.salesRow.revalOn .reval-capval > span,
    .openProp.salesRow.revalOn .reval-landval > span,
    .openProp.salesRow.revalOn .reval-valimp > span {
        right:0.8rem;
        bottom: 1.55rem;
    }

}

@media only screen and (max-width:1366px) {

    .openProp .searchDetails-wrapper,
    .openProp .capval,
    .openProp .landarea,
    .openProp .landval,
    .openProp .tfa,
    .openProp .tla,
    .openProp .valimp,
    .openProp.salesRow .nsptocv,
    .openProp.salesRow .nsp,
    .openProp.salesRow .chattels,
    .openProp.salesRow .salegst,
    .openProp.salesRow .saleother {
        height: 7.8rem;
    }

    .openProp.salesRow .colCell.capval,
    .openProp.salesRow .colCell.landarea,
    .openProp.salesRow .colCell.landval {
        width: 22%;
    }

    .openProp.salesRow .colCell.valimp { width: 25%; }

    .openProp.salesRow .colCell.landarea { width: 31%; }

    .openProp.salesRow.revalOn.noThumbnails .reval-capval,
    .openProp.salesRow.revalOn.noThumbnails .reval-landval,
    .openProp.salesRow.revalOn.noThumbnails .reval-valimp {
        width: calc(20.4% + .1rem);
    }

    .openProp.salesRow.revalOn.noThumbnails .reval-nsptorcv {
        margin-left: calc(27.3% + .15rem);
        width: 9.3%;
    }

    .openProp.salesRow .capval,
    .openProp.salesRow .landarea,
    .openProp.salesRow .landval,
    .openProp.salesRow .tfa,
    .openProp.salesRow .tla,
    .openProp.salesRow .valimp,
    .openProp.salesRow .nsp,
    .openProp.salesRow .chattels,
    .openProp.salesRow .salegst,
    .openProp.salesRow .saleother,
    .openProp.salesRow .nsptocv,
    .openProp.salesRow.revalOn .reval-capval,
    .openProp.salesRow.revalOn .reval-landval,
    .openProp.salesRow.revalOn .reval-valimp {
        font-size: 1.55rem;
        padding:2.7rem 0 2.5rem 0.65rem;
    }

    .openProp .capval::after,
    .openProp .landarea::after,
    .openProp .landval::after,
    .openProp .tfa::after,
    .openProp .tla::after,
    .openProp .valimp::after,
    .openProp.salesRow .nsp::after,
    .openProp.salesRow .nsptocv::after,
    .openProp.salesRow .chattels::after,
    .openProp.salesRow .salegst::after,
    .openProp.salesRow .saleother::after,
    .openProp.salesRow.revalOn .reval-nsptorcv::after,
    .openProp.revalOn .reval-capval::after,
    .openProp.revalOn .reval-landval::after,
    .openProp.revalOn .reval-valimp::after {
        left: 0.7rem;
    }

    .openProp.salesRow .capval div,
    .openProp.salesRow .landval div,
    .openProp.salesRow .valimp div,
    .openProp.salesRow .nsp div,
    .openProp.salesRow.revalOn .reval-capval div,
    .openProp.salesRow.revalOn .reval-landval div,
    .openProp.salesRow.revalOn .reval-valimp div {
        font-size: 1.3rem;
        margin-top: 0.2rem;
    }

    .openProp.salesRow .colCell.tfa,
    .openProp.salesRow .colCell.tla {
        bottom: .5rem;
        left:calc(69% + .7rem);
        font-size: 1.3rem;
        padding: 0 0 0 2rem;
    }

    .openProp.salesRow .colCell.tla { left: calc(69% + 9.4rem); }

    .openProp .sales-trafficLights .saleClassification::before,
    .openProp .sales-trafficLights .saleStatus::before,
    .openProp .sales-trafficLights .saleDate::before {
        content: "";
    }

    .openProp.salesRow.revalOn .reval-capval,
    .openProp.salesRow.revalOn .reval-landval,
    .openProp.salesRow.revalOn .reval-valimp {
        width:20.95%;
    }

    .openProp.salesRow.revalOn .reval-nsptorcv { margin-left: calc(28.3% - 0.2rem); }

    .openProp.salesRow.revalOn.noThumbnails .revalRates {
        padding-right: 32.7%;
        width: calc(100% - .2rem);
    }

    .openProp.salesRow.revalOn .reval-nsptorcv {
        border-right: .1rem solid rgba(255,255,255,.6);
    }

    .openProp.salesRow .nsptocv {
        width: calc(68% - .75rem);
    }

}




/*  ============================================================================================ */
/*  PROPERTY SEARCH RESULTS SKINNY PROPERTY CARD  ============================================== */
/*  ============================================================================================ */

.theSkinny.resultsRow { height:5.6rem; }

.theSkinny.resultsRow .primaryPhoto_thumb[src$=".png"] {
    width:80%;
    height:80%;
    margin:10% 0 0 11%;
}

.theSkinny.resultsRow .searchDetails-wrapper { height: 5.5rem; }

.theSkinny.resultsRow .searchDetails-wrapper .colCell.capval,
.theSkinny.resultsRow .searchDetails-wrapper .colCell.landval,
.theSkinny.resultsRow .searchDetails-wrapper .colCell.valimp,
.theSkinny.resultsRow .searchDetails-wrapper .colCell.landarea,
.theSkinny.resultsRow .searchDetails-wrapper .colCell.tfa,
.theSkinny.resultsRow .searchDetails-wrapper .colCell.tla {
    height:5.5rem;
}

.theSkinny.openProp {
    display:block;
    position:relative;
    background-color:#fff;
    padding:2em 23rem 1.26rem 1rem;
    border-bottom:.1rem solid rgba(40,60,100,.2);
    margin:.8rem 0 1.15rem;;
    width:100%;
    height:inherit;
    box-sizing:border-box;
    box-shadow:none;
}

.theSkinny.openProp:last-of-type {
    margin-bottom:0;
    padding-bottom:1.15rem;

}

.theSkinny.openProp::before {
    position: absolute;
    top: -1rem;
    left: -.3rem;
    content: "";
    background-color: #f9f9f9;
    border-bottom: .1px solid #ececec;
    border-top: .1px solid #ececec;
    width: calc(100% + .6em);
    height: 1rem;
}

.theSkinny.openProp::after {
    position: absolute;
    bottom: -1.15rem;
    left: 0;
    content: "";
    background-color: #f9f9f9;
    background: linear-gradient(to bottom, rgba(0,0,0,.25) 0%, rgba(249,249,249,1) 30%, rgba(249,249,249,1) 100%);
    width: 100%;
    height: 1.15rem;
}

.theSkinny.openProp:last-of-type::after {
    display: none;
    margin-bottom:0
}

.theSkinny.openProp.noThumbnails { padding-right:23em; }

.theSkinny.resultsRow.openProp + .theSkinny.resultsRow.openProp { margin-top:0; }

.theSkinny.openProp .address {
    display:block;
    width:inherit;
    max-width:50%;
}



.theSkinny.openProp .fullAddress {
    font-size:1.5rem;
    padding:.5rem 1rem .2rem .8rem;
    margin:-.5rem 0 0 .3rem;
    width:100%;
    box-sizing:border-box;
}

.theSkinny.openProp:hover .fullAddress { padding: .5rem 0 .2rem 0.8rem; }

.theSkinny.openProp:hover .fullAddress::after { top:.7rem; }

.theSkinny.openProp .fullAddress span {	float:left; }

.theSkinny.openProp .fullAddress span:nth-child(2) {
    display:inline-block;
    font-size:1.5rem;
    font-weight:600;
    text-align:left;
    margin:0 0 1rem .5rem;
}

.theSkinny.openProp .fullAddress .valref,
.theSkinny.openProp .fullAddress .qpid,
.theSkinny.openProp.salesRow .fullAddress .legaldesc {
    background:none;
    padding:0;
    border:none;
    font-size:1.05rem;
    border-radius:0;
    min-width:auto;
    width:auto;
}

.theSkinny.openProp.salesRow .fullAddress .legaldesc { max-width: calc(100% - 24rem); }

.theSkinny.openProp.salesRow .fullAddress .valref {
    padding-right: 1rem;
    border-right: .1rem solid rgba(74,144,226,.25);
    margin-right: 1.2em;
    width:auto;
}

.theSkinny.openProp .fullAddress .qpid { clear:both; }

.theSkinny.openProp .fullAddress .qpid {
    padding-right:1rem;
    border-right:.1rem solid rgba(74,144,226,.25);
    margin-right:1.2em;
    margin-left:0;
}

.theSkinny.openProp .fullAddress .occupier {
    top:4.6rem;
    left:34.5rem;
    font-size:1.1rem;
    line-height:1.9;
    padding-left:1rem;
    border-left:.1rem solid rgba(74,144,226,.25);
    margin:0;
    clear:inherit;
}

.theSkinny.openProp .fullAddress .occupier::before { text-transform:inherit; }


.theSkinny.openProp .searchDetails-wrapper {
    height: inherit;
    overflow:auto;
}

.theSkinny.openProp .capval,
.theSkinny.openProp .landarea,
.theSkinny.openProp .landval,
.theSkinny.openProp .tfa,
.theSkinny.openProp .tla,
.theSkinny.openProp .valimp {
    font-size:1.35rem;
    font-weight:400;
    padding:0.7rem 1rem .6rem;
    border-right:.1rem solid rgba(255,255,255,.15);
    height:inherit;
    float:left;
}

.theSkinny.openProp .landarea,
.theSkinny.openProp .tfa,
.theSkinny.openProp .tla {
    text-align:left;
}

.theSkinny.openProp .tla {
    border-right:none;
}

.theSkinny.openProp .landval div,
.theSkinny.openProp .capval div,
.theSkinny.openProp .valimp div {
    font-size: 1.2rem;
    text-align: left;
    margin: .1rem 0 0 2.5rem;
    min-width: 7.5rem;
}

.theSkinny.openProp .capval::before,
.theSkinny.openProp .landval::before,
.theSkinny.openProp .valimp::before,
.theSkinny.openProp .tfa::before,
.theSkinny.openProp .tla::before,
.theSkinny.openProp .landarea::before {
    content:"CV";
    font-weight:300;
    font-size:1rem;
    padding-right:.75rem;
}

.theSkinny.openProp .landval::before	{content:"LV"; }
.theSkinny.openProp .valimp::before 	{content: "VI";}
.theSkinny.openProp .tfa::before 		{content:"TFA"; }
.theSkinny.openProp .tla::before 		{content:"TLA";}
.theSkinny.openProp .landarea::before 	{content:"LA\0000a0(Ha)";}

.theSkinny.openProp .capval::before,
.theSkinny.openProp .landval::before,
.theSkinny.openProp .valimp::before {
    display:inline-block;
    width:2.5rem;
}

.theSkinny.openProp .capval::after,
.theSkinny.openProp .landval::after,
.theSkinny.openProp .tfa::after,
.theSkinny.openProp .tla::after,
.theSkinny.openProp .landarea::after,
.theSkinny.openProp .valimp::after 	{
    content:none;
}

.theSkinny.openProp .tfa span,
.theSkinny.openProp .tla span,
.theSkinny.openProp .landarea span {
    font-size:1.3rem;
    padding-left:.1rem;
}

.theSkinny.openProp .photoGallery_propSearch {
    position:absolute;
    bottom: 2.7rem;
    right:1rem;
    background: rgba(237,241,245,.15);
    padding:.3rem;
    border:.1rem solid #eee;
    width:20rem;
    height:12.1rem;
    overflow-y:auto;
    white-space:inherit;
}

.theSkinny .thumbWrapper {
    position:relative;
    display:inline-block;
    margin-right:.2rem;
    margin-bottom:.2rem;
    vertical-align:middle;
    width:8.1rem;
    height:5.4rem;
}



/*  SKINNY PROPERTY SALES ROW  ================================================================= */

/*.theSkinny.openProp.salesRow.resultsRow:hover .fullAddress .md-link { padding: .4rem .8rem; }*/

.theSkinny.openProp.salesRow .fullAddress .lastReval-date {
    font-size:1.05rem;
    padding-bottom:.5rem;
    margin-top:0;
}

.theSkinny.openProp.salesRow .fullAddress .lastReval-date::before {
    text-transform:inherit;
}

.theSkinny.openProp.salesRow .searchDetails-wrapper {
    width: 100%;
    height: 10rem;
    overflow: inherit;
}

.theSkinny.openProp.salesRow .capval,
.theSkinny.openProp.salesRow .landarea,
.theSkinny.openProp.salesRow .landval,
.theSkinny.openProp.salesRow .tfa,
.theSkinny.openProp.salesRow .tla,
.theSkinny.openProp.salesRow .valimp {
    padding: 0.7rem 1rem .6rem .7rem;
}

.theSkinny.openProp.salesRow .colCell.capval,
.theSkinny.openProp.salesRow .colCell.landarea,
.theSkinny.openProp.salesRow .colCell.landval,
.theSkinny.openProp.salesRow .valimp {
    top:4.4rem;
    width: 15.25%;
}

.theSkinny.openProp.salesRow .capval::before,
.theSkinny.openProp.salesRow .landval::before,
.theSkinny.openProp.salesRow .valimp::before {
    width: 3rem;
}

.theSkinny.openProp.salesRow .landval div,
.theSkinny.openProp.salesRow .capval div,
.theSkinny.openProp.salesRow .valimp div {
    margin-left:3rem;
}

.theSkinny.openProp.salesRow .landarea,
.theSkinny.openProp.salesRow .tfa,
.theSkinny.openProp.salesRow .tla {
    font-size:1.35rem;
    text-align: left;
}

.theSkinny.openProp.salesRow .colCell.landarea {
    padding-top:1.1rem;
}

.theSkinny.openProp.salesRow .landarea::before,
.theSkinny.openProp.salesRow .tfa::before,
.theSkinny.openProp.salesRow .tla::before {
    display:block;
    margin-bottom:.1rem;
}

.theSkinny.openProp.salesRow .tfaTla-wrapper {
    top: 4.6rem;
    bottom: initial;
    left: 61%;
    width: 30.5%;
}

.theSkinny.openProp.salesRow .tfa,
.theSkinny.openProp.salesRow .tla {
    font-weight: 400;
    line-height: 1.35;
    padding: 0.9rem 0 0.5rem .75rem;
    border-right: 1px solid rgba(255,255,255,.15);
    margin: 0;
    width: 50%;
}

.theSkinny.openProp.salesRow .landval div,
.theSkinny.openProp.salesRow .capval div,
.theSkinny.openProp.salesRow .valimp div {
    text-align: left;
    float: none;
}

.theSkinny.openProp.salesRow .nsp {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 1.35rem;
    line-height: 1.35;
    text-align: left;
    background: #002943;
    padding: 0.5rem 0 2.3rem 3.6rem;
    height:4.6rem;
    width: 100%;
    box-sizing: border-box;
    z-index:1;
}

.theSkinny.openProp.salesRow .chattels,
.theSkinny.openProp.salesRow .salegst,
.theSkinny.openProp.salesRow .saleother,
.theSkinny.openProp.salesRow .nsptocv {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 1.35rem;
    line-height: 1.35;
    text-align: left;
    padding: 2.2rem 0 2.2rem .75rem;
    border-left: .1rem solid rgba(255,255,255,.15);
    height:4.6rem;
    width: 15.25%;
    box-sizing: border-box;
    z-index:1;
}

.theSkinny.openProp.salesRow .nsp::after,
.theSkinny.openProp.salesRow .nsptocv::after,
.theSkinny.openProp.salesRow .chattels::after,
.theSkinny.openProp.salesRow .salegst::after,
.theSkinny.openProp.salesRow .saleother::after,
.theSkinny.openProp.salesRow.revalOn .reval-nsptorcv::after {
    top: .8rem;
}

.theSkinny.openProp.salesRow .nsptocv {
    width: 8.5%;
}

.openProp.salesRow .nsp::after,
.openProp.salesRow .nsptocv::after,
.openProp.salesRow .chattels::after,
.openProp.salesRow .salegst::after,
.openProp.salesRow .saleother::after,
.openProp.salesRow.revalOn .reval-nsptorcv::after {
    left: .75rem;
}

.theSkinny.openProp.salesRow .chattels 	{ left:calc(45.75% - .1rem); }
.theSkinny.openProp.salesRow .salegst 	{ left:calc(61% - .1rem); }
.theSkinny.openProp.salesRow .saleother { left:calc(76.25% - .1rem); }
.theSkinny.openProp.salesRow .nsptocv 	{ left:calc(91.5% - .1rem); }

.theSkinny.openProp.salesRow .nsp::after { content:"NSP"; }

.theSkinny.openProp.salesRow .chattels::after {  }

.theSkinny.openProp.salesRow .nsp div {
    font-size: 1.2rem;
}

.theSkinny.openProp.salesRow.revalOn .revalRates {
    width: 100%;
    height: 5rem;
    overflow: hidden;
}
.theSkinny.openPropsalesRowrevalOn .reval-capval,
.theSkinny.openPropsalesRow.revalOn .reval-landval,
.theSkinny.openPropsalesRow.revalOn .reval-valimp {
    font-size: 1.4rem;
    font-weight: 500;
    padding: 1rem 1rem 1.3rem;
    height: inherit;
    width: 15.25%;
    float: left;
}

.theSkinny.openProp.salesRow.revalOn .reval-nsptorcv {
    font-size: 1.35rem;
    margin-left: 45.75%;
    width: 8.5%;
    height: 5rem;

}

.theSkinny.openProp.salesRow .searchDetails-wrapper {
    width: 100%;
    height: 9.2rem;
    overflow: inherit;
}

.theSkinny.openProp.salesRow.revalOn .reval-capval,
.theSkinny.openProp.salesRow.revalOn .reval-landval,
.theSkinny.openProp.salesRow.revalOn .reval-valimp {
    padding: 0.7rem 1rem .7rem;
    width:15.25%;
}

.theSkinny.openProp.salesRow.revalOn .reval-capval > span,
.theSkinny.openProp.salesRow.revalOn .reval-landval > span,
.theSkinny.openProp.salesRow.revalOn .reval-valimp > span {
    bottom: 0.7rem;
    right: .7rem;
    font-size:1rem;
    line-height: 1.3;
}

.theSkinny.openProp.salesRow .photoGallery_propSearch {
    height: 15.7rem;
}

.theSkinny.openProp.salesRow.revalOn .photoGallery_propSearch {
    height: 20.7rem;
}

.theSkinny.openProp.salesRow .sales-trafficLights {
    top: 4.8rem;
    left: inherit;
    right: 22.1rem;
    padding: .3rem 0;
    width: auto;
    max-width: calc(57% - 23.1rem);
}

.theSkinny.openProp.salesRow .sales-trafficLights .qv-col-sale-id,
.theSkinny.openProp.salesRow .sales-trafficLights .saleClassification,
.theSkinny.openProp.salesRow .sales-trafficLights .saleDate,
.theSkinny.openProp.salesRow .sales-trafficLights .saleStatus,
.theSkinny.openProp.salesRow .sales-trafficLights .saleAnalysis {
    display: inline-block;
    font-size: 1.05rem;
    font-weight: 600;
    color: #204d90;
    line-height: 1.7;
    text-align: center;
    background-color: inherit;
    padding: 0 .25rem;
    border: none;
    border-radius: 0;
    margin: 0;
    vertical-align: top;
    width: inherit;
    box-sizing: border-box;
    pointer-events: none;
    float: left;
    padding: 0 1rem 0 1rem;
    border-right: .1rem solid rgba(74,144,226,.25);
    margin-right: .8em;
    margin-left: -.9rem;
}

.theSkinny.openProp.salesRow .sales-trafficLights .saleClassification.classTwo,
.theSkinny.openProp.salesRow.pendingSale .sales-trafficLights .saleStatus {
    color:#4e342e;
    background-color: rgba(255,200,40,1);
    border-radius:.3rem;
    border-right:none;
}

.theSkinny.openProp.salesRow .sales-trafficLights .saleClassification.classThree,
.theSkinny.openProp.salesRow.unconfirmedSale .sales-trafficLights .saleStatus {
    color:#fff;
    background-color: rgba(190,55,15,1);
    border-radius:.3rem;
    border-right:none;
}

.theSkinny.openProp.salesRow.pendingSale .sales-trafficLights .saleClassification,
.theSkinny.openProp.salesRow.unconfirmedSale .sales-trafficLights .saleClassification {
    border-right:none;
}

.theSkinny.openProp.salesRow .sales-trafficLights .saleAnalysis {
    padding-right: 0;
    border-right: none;
    margin-right: 0;
}

.theSkinny.openProp.salesRow .sales-trafficLights .vendPurchase {
    font-size: 1.05rem;
    line-height: 2;
    padding-left:.15rem;
    margin-top: 0;
}

.theSkinny.openProp.salesRow .sales-trafficLights .vendPurchase::before {
    font-size: 1.05rem;
    text-transform: inherit;
}

.openProp.salesRow .sales-trafficLights .saleClassification.classTwo ~ .vendPurchase,
.openProp.salesRow .sales-trafficLights .saleClassification.classThree ~ .vendPurchase {
    margin-left:-.9rem;
}



/*  SKINNY REVISION VALUES  ==================================================================== */

.theSkinny.openProp.revalOn .revalRates {
    width: 100%;
    height:5.5rem;
}

.theSkinny.openProp.revalOn .reval-capval,
.theSkinny.openProp.revalOn .reval-landval,
.theSkinny.openProp.revalOn .reval-valimp {
    font-size: 1.35rem;
    font-weight: 400;
    padding: .8rem 1rem .7rem;
    height: inherit;
    width: 16.6%;
    float: left;
}

.theSkinny.openProp.revalOn .reval-capval::before,
.theSkinny.openProp.revalOn .reval-landval::before,
.theSkinny.openProp.revalOn .reval-valimp::before {
    display:inline-block;
    content: "RCV";
    font-weight: 400;
    font-size: 1rem;
    width:2.5rem;
}

.theSkinny.openProp.revalOn .reval-landval::before { content: "RLV"; }

.theSkinny.openProp.revalOn .reval-valimp::before { content: "RVI"; }

.theSkinny.openProp.revalOn .reval-capval::after,
.theSkinny.openProp.revalOn .reval-landval::after,
.theSkinny.openProp.revalOn .reval-valimp::after {
    display:none;
}

.theSkinny.openProp.revalOn .reval-capval div,
.theSkinny.openProp.revalOn .reval-landval div,
.theSkinny.openProp.revalOn .reval-valimp div {
    font-size: 1.2rem;
    font-weight:400;
    margin: .25rem 0 0 2.5rem;
}

.theSkinny.openProp.revalOn .reval-capval > span,
.theSkinny.openProp.revalOn .reval-landval > span,
.theSkinny.openProp.revalOn .reval-valimp > span {
    padding-left: 1.2rem;
    padding-right: 0.3rem;
    margin-top: 0;
}

.theSkinny.openProp.revalOn .reval-capval > span.valueUp::before,
.theSkinny.openProp.revalOn .reval-capval > span.valueDown::before,
.theSkinny.openProp.revalOn .reval-landval > span.valueUp::before,
.theSkinny.openProp.revalOn .reval-landval > span.valueDown::before,
.theSkinny.openProp.revalOn .reval-valimp > span.valueUp::before,
.theSkinny.openProp.revalOn .reval-valimp > span.valueDown::before {
    left: 0.3rem;
}

.theSkinny.openProp.revalOn .photoGallery_propSearch {
    width: 20.7rem;
    height:17.5rem;
}


/*  SKINNY PROPERTY CARD EXTRA DETAILS  ======================================================== */

.theSkinny.openProp .extras {
    position:relative;
    background:none;
    padding:0;
    border-top:none;
    margin:0;
    width:inherit;
}

.theSkinny.openProp .extras .md-landMas {
    padding: 3rem 0 4.8rem 0;
    border: none;
    margin: 0;
    width:100%;
}

.theSkinny.openProp .extras .md-landMas li {
    top: 0;
    font-size:0;
    padding:0 0 0 2.8rem;
    margin: 0.6rem .5rem 0 0;
    max-width: calc(8% - .75rem);
    background: rgba(237,241,245,.8);
    border-radius:.2rem;
}

.theSkinny.openProp .extras  .md-landMas strong {
    font-size:1.1rem;
    line-height:2.3;
}

.theSkinny.openProp .extras .md-landMas li.md-masIcon-landUse,
.theSkinny.openProp .extras .md-landMas li.md-masIcon-walls,
.theSkinny.openProp .extras .md-landMas li.md-masIcon-roof,
.theSkinny.openProp .extras .md-landMas li.md-masIcon-contour,
.theSkinny.openProp .extras .md-landMas li.md-masIcon-viewScope {
    max-width:inherit;
    width: calc(20% - 0.57rem);
    float:left;
}

.theSkinny.openProp .extras .md-landMas li.md-masIcon-viewScope {
    margin-right: 0;
    width: 20.23%;
}

.theSkinny.openProp .extras .md-landMas li.md-masIcon-category 		{ position:absolute; top:-.2rem; left:0;     }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-eyb 			{position:absolute;  top: -.2rem;left:7.7%;  }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-bedrooms 		{ position:absolute; top:-.2rem; left:15.4%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-toilets 		{ position:absolute; top:-.2rem; left:23.1%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-units 		{ position:absolute; top:-.2rem; left:30.8%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-fsg 			{ position:absolute; top:-.2rem; left:38.5%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-umrg 			{ position:absolute; top:-.2rem; left:46.2%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-oli 			{ position:absolute; top:-.2rem; left:53.9%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-modernisation { position:absolute; top:-.2rem; left:61.6%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-zone 			{ position:absolute; top:-.2rem; left:69.3%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-lotPosition 	{ position:absolute; top:-.2rem; left:77%;   }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-production 	{ position:absolute; top:-.2rem; left:84.7%; }
.theSkinny.openProp .extras .md-landMas li.md-masIcon-maoriLand 	{ position:absolute; top:-.2rem; left:92.4%; max-width: calc(8% - 0.5rem); }

.theSkinny.openProp .extras .md-landMas li:before {
    top:.5rem;
    left: .5rem;
    background-size:16.6rem 7.2rem;
    width:1.8rem;
    height:1.8rem;
}

.theSkinny.openProp .extras .md-masIcon-units::before		   		{ background-position:-1.8rem 0;	 	 }
.theSkinny.openProp .extras .md-masIcon-zone::before				{ background-position:-3.6rem 0;		 }
.theSkinny.openProp .extras .md-masIcon-walls::before		   		{ background-position:-7.4rem 0;		 }
.theSkinny.openProp .extras .md-masIcon-roof::before				{ background-position:-9.3rem 0;		 }
.theSkinny.openProp .extras .md-masIcon-maoriLand::before	   		{ background-position:-1.8rem -1.8rem;	 }
.theSkinny.openProp .extras .md-masIcon-viewScope::before	   		{ background-position:-7.4rem -1.8rem;	 }
.theSkinny.openProp .extras .md-masIcon-lotPosition::before	 		{ background-position:-9.2rem -1.8rem;	 }
.theSkinny.openProp .extras .md-masIcon-contour::before		 		{ background-position:-11rem -1.8rem;	 }
.theSkinny.openProp .extras .md-masIcon-eyb::before			 		{ background-position:-1.9rem -3.6rem;	 }
.theSkinny.openProp .extras .md-masIcon-modernisation::before   	{ background-position:-3.6rem -3.6rem;	 }
.theSkinny.openProp .extras .md-masIcon-bedrooms::before			{ background-position:-9.4rem -3.6rem;	 }
.theSkinny.openProp .extras .md-masIcon-toilets::before		 		{ background-position:-11.2rem -3.6rem;  }
.theSkinny.openProp .extras .md-masIcon-oli::before			 		{ background-position:-12.9rem -3.6rem;  }
.theSkinny.openProp .extras .md-masIcon-category::before	 		{ background-position:-14.8rem -3.6rem;  }
.theSkinny.openProp .extras .md-masIcon-umrg::before		   		{ background-position:-9.2rem -5.4rem;	 }
.theSkinny.openProp .extras .md-masIcon-fsg::before					{ background-position:-11rem -5.4rem;	 }
.theSkinny.openProp .extras .md-masIcon-production::before	 		{ background-position:-14.8rem -5.4rem;  }


/*  SKINNY AND NARROW PROPERTY CARD EXTRA DETAILS  ============================================= */

/*
@media only screen and (max-width:1366px) {


    .theSkinny.openProp .capval,
    .theSkinny.openProp .landarea,
    .theSkinny.openProp .landval,
    .theSkinny.openProp .tfa,
    .theSkinny.openProp .tla,
    .theSkinny.openProp .valimp {
        font-size: 1.3rem;
        line-height:1.35;
        width:18%;
        min-height: 4.9rem;
    }

    .theSkinny.openProp .capval div,
    .theSkinny.openProp .landval div,
    .theSkinny.openProp .valimp div,
    .theSkinny.openProp .reval-capval div,
    .theSkinny.openProp .reval-landval div,
    .theSkinny.openProp .reval-valimp div {
        text-align: left;
        margin: 0.rem 0 0 2.1rem;
        float: inherit;
    }

    .theSkinny.openProp .capval::before,
    .theSkinny.openProp .landval::before,
    .theSkinny.openProp .valimp::before,
    .theSkinny .reval-capval::before,
    .theSkinny .reval-landval::before,
    .theSkinny .reval-valimp::before {
        display:inline-block;
        min-width:2.1rem;
    }

    .theSkinny.openProp .revalRates { width:54.2%; }

    .theSkinny .reval-capval,
    .theSkinny .reval-landval,
    .theSkinny .reval-valimp {
        padding: 0.7rem 1rem 1.7rem;
    }

    .theSkinny .reval-capval > span,
    .theSkinny .reval-landval > span,
    .theSkinny .reval-valimp > span {
        right: 0.6rem;
        bottom: 1.5rem;
        left: inherit;
        padding-left: 1.9rem;
    }

    .theSkinny.openProp.revalOn .photoGallery_propSearch { height: 20rem; }

    .theSkinny.openProp .extras .md-landMas {
        position:relative;
        padding:2.7rem 1rem .9rem;
        overflow:hidden;
    }

    .theSkinny.openProp .extras .md-landMas li { top:.5rem; }

    .theSkinny.openProp .extras .md-landMas li.md-masIcon-landUse,
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-walls,
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-roof,
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-contour,
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-viewScope {
        margin-top:-.3rem;
    }

    .theSkinny.openProp .extras .md-landMas li.md-masIcon-oli 			{ left:44rem;	 }
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-modernisation { left:50.5rem;	 }
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-zone 			{ left:56rem;	 }
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-lotPosition 	{ left:61.25rem; }
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-production 	{ top:3.2rem; left:1rem;	 }
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-maoriLand 	{ top:3.2rem; left:7.25rem;	 }
    .theSkinny.openProp .extras .md-landMas li.md-masIcon-landUse 		{ margin-left:12.25rem; 	 }

}
 */


/*  ============================================================================================ */
/*  SKINNY SWITCH CONTROLLER  ================================================================== */
/*  ============================================================================================ */

.switch {
    position:relative;
    padding:.6rem 0 .7rem 1.8rem;
    border-left:.1rem solid rgba(0,0,0,.12);
    margin:1.1rem 1rem 0 1.3rem;
}

.switch .cmn-toggle {
    position:absolute;
    margin-left:-9999px;
    visibility:hidden;
}

.switch .cmn-toggle + label {
    display:block;
    position:relative;
    cursor:pointer;
    outline:none;
    user-select:none;
}

input.cmn-toggle-round-flat + label span {
    display:block;
    padding:.2rem;
    width:4rem;
    height:2.2rem;
    background-color:#ccc;
    border-radius:3rem;
    transition:background .4s;
    margin:0 2rem;
}

input.cmn-toggle-round-flat + label span.toolTipper {
    display:inline-block;
    position:absolute;
    top:0;
    background:none;
    padding:0;
    border-radius:0;
    margin:0;
    width:2.2rem;
    height:2.2rem;
}

input.cmn-toggle-round-flat + label span.toolTipper::before,
input.cmn-toggle-round-flat + label span.toolTipper::after {
    display:none;
}

input.cmn-toggle-round-flat + label span.toolTipper.normalView { left:-.4rem; }

input.cmn-toggle-round-flat + label span.toolTipper.compactView {
    left:inherit;
    right:-.4rem
}

input.cmn-toggle-round-flat + label span:before,
input.cmn-toggle-round-flat + label span:after {
    display:block;
    position:absolute;
    content:"";
}

input.cmn-toggle-round-flat + label span:before {
    top:.2rem;
    left:2.2rem;
    bottom:.2rem;
    right:2.2rem;
    background-color:#fff;
    border-radius:1.4rem;
    transition:background .4s;
}

input.cmn-toggle-round-flat + label span:after {
    top:.4rem;
    left:2.4rem;
    bottom:.4rem;
    width:1.4rem;
    background-color:rgba(74,144,226,1);
    border-radius:1.4rem;
    transition:margin .4s, background .4s;
}

input.cmn-toggle-round-flat:checked + label span:after { margin-left:1.8rem; }

i.material-icons.fatman {
    position:absolute;
    top:.8rem;
    left:1.6rem;
    font-size:1.8rem;
    color:rgba(74,144,226,1);
    pointer-events:none;
}

i.material-icons.littleboy {
    position:absolute;
    top:.85rem;
    right:-.25rem;
    font-size:1.8rem;
    color:rgba(0, 0, 0, .34);
    pointer-events:none;
}

input.cmn-toggle-round-flat:checked ~ i.material-icons.fatman {	color:rgba(0, 0, 0, .34);	}

input.cmn-toggle-round-flat:checked ~ i.material-icons.littleboy { color:rgba(74,144,226,1); }



/*  ============================================================================================ */
/*  IMAGE UPLOAD WINDOW  ======================================================================= */
/*  ============================================================================================ */

.photoManager { overflow:hidden; }

.photoUpload {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
}

.uploaderHeader {
    position:relative;
    background-color:rgba(30,65,150,1);
    background:linear-gradient(to right, rgba(30,65,150,.95) 20%,rgba(27,37,106,.95) 100%);
    padding:0 2.4rem;
}

.uploaderHeader span {
    display:block;
    position:absolute;
    top:.6rem;
    right:1.2rem;
}

.uploaderHeader i:hover { color:rgba(237,185,20,1); }

.uploaderHeader .mdl-tabs__tab-bar {
    -webkit-justify-content:inherit;
    justify-content:inherit;
    border-bottom:inherit;
}

.uploaderHeader .mdl-tabs__tab { color:rgba(255,255,255,.54); }

.mdl-tabs__tab.is-active { color:rgba(255,255,255,1); }

.mdl-tabs__tab.is-active:after { background:rgba(237,185,20,1); }

.photoView {
    display:table;
    position:fixed;
    top:4.8rem;
    right:0;
    bottom:0;
    left:0;
    background:#fff;
    padding:3.2rem 2.4rem 16.2rem;
    width:100%;
    height:100%;
}

.photoView.disabled {
    pointer-events:none;
    opacity:1;
}

.uploaderBody .dropzone {
    position:absolute;
    top:4.8rem;
    right:0;
    bottom:0;
    left:0;
    background:#fff;
    padding:2.4rem 3.2rem;
    overflow-y:auto;
}

.uploaderBody h3,
.dz-browser-not-supported span {
    display:inline-block;
    font-size:3.6rem;
    font-weight:400;
    color:rgba(0, 0, 0, .54);
    background-image:url('../../images/property/stack_of_photos.png');
    background-repeat:no-repeat;
    background-position:50% 4rem;
    background-size:12.8rem 10.5rem;
    line-height:1.8;
    padding-top:16rem;
    margin:0 0 2rem;
    text-align:center;
}

.uploaderBody h3 span {
    display:block;
    font-size:1.6rem;
}

.uploaderBody.dragNdrop ~ .uploaderFooter  { display:none; }

.fileUploader-button {
    display:block;
    text-align:center;
}

.fileUploader-button:hover .mdl-button { box-shadow:0 0 .2rem .1rem rgb(74,144,226); }

.fileUploader-button .mdl-button {
    font-weight:600;
    text-transform:initial;
}

.mdl-button.disabled,
.photo-uploader.disabled {
    pointer-events:none;
    opacity:.4;
}

.loadingSpinner {
    position:relative;
    font-size:3.6rem;
    font-weight:400;
    color:rgba(0,0,0,.54);
    background-image:url('../../images/spinner.gif');
    background-repeat:no-repeat;
    background-position:50% 0;
    background-size:2.5rem;
    line-height:1.8;
    padding-top:9rem;
    margin:-4.2rem 0 2rem;
    text-align:center;
    display:none;
    z-index:100;
    pointer-events:none;
}
.loadingSpinnerExportResults {
    position:relative;
    font-size:4rem;
    font-weight:400;
    color:rgba(0,0,0,.54);
    background-image:url('../../images/spinner.gif');
    background-repeat:no-repeat;
    background-position:50% 0;
    background-size:2.5rem;
    line-height:1.8;
    padding-top:2.5rem;
    margin:-4.2rem 0 2rem;
    text-align:center;
    display:none;
    z-index:100;
    pointer-events:none;
}

.multi-uploads-done,
.save-and-close,
.photoRelink,
.photoDelete {
    pointer-events:none;
}

.photoRelink.disabled,
.photoDelete.disabled {
    pointer-events:none;
    opacity:.4;
}

.multi-uploads-done.active,
.save-and-close.active,
.photoDelete.active,
.photoRelink.active {
    pointer-events:auto;
}

/*  ============================================================================================ */
/*  IMAGE DETAILS WINDOW  ====================================================================== */
/*  ============================================================================================ */

.imageDetails {
    display:table-cell;
    position:relative;
    font-size:1.3rem;
    color:rgba(0,0,0,.87);
    vertical-align:top;
    padding:1rem 0 0 2rem;
    width:35%;
}

.imageDetails.disabled {
    pointer-events:none;
    opacity:.6;
}

.detailsTitle .edit {
    display:inline-block;
    position:relative;
    font-size:1.6rem;
    font-weight:600;
    color:rgba(0,0,0,1);
    padding-top:1.8rem;
    margin-left:1.2rem;
    margin-top:-1.8rem;
    width:calc(100% - 5rem);
    min-height:4.8rem;
    vertical-align:top;
    box-sizing:border-box;
}

.detailsTitle .edit input {
    position:absolute;
    padding:0 1rem;
    border:none;
    border-radius:0;
    margin-top:-.5rem;
    -webkit-box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
    -moz-box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
    box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
}

.detailsDate,
.detailsPrimary,
.detailsInternalOnly,
.detailsInsurance,
.relinkQupid {
    margin:2rem 4rem 0 0;
    float:left;
}

.detailsPrimary label,
.detailsInternalOnly label,
.detailsInsurance label,
.relinkQupid label {
    display:inline-block;
    padding-left:1.7rem;
    width:calc(100% - 3.5rem);
}

.detailsInsurance label { margin:.75rem 0 1.5rem; }

.detailsPrimary label span,
.detailsInternalOnly label span,
.detailsInsurance label span,
.relinkQupid label span{
    display:block;
    color:rgba(0,0,0,.54);
    width:30rem;
}

.detailsPrimary input[type="checkbox"],
.detailsInternalOnly input[type="checkbox"],
.detailsInsurance input[type="checkbox"],
.relinkQupid input[type="checkbox"] {
    -webkit-appearance:none;
    border-radius:.2rem;
    margin:1rem 0 0 .5rem;
    box-shadow:0 0 0 .2rem rgba(0,0,0,.54);
    width:1.4rem;
    height:1.4rem;
    vertical-align:top;
}

.detailsPrimary input[type="checkbox"]:checked,
.detailsInternalOnly input[type="checkbox"]:checked,
.detailsInsurance input[type="checkbox"]:checked,
.relinkQupid input[type="checkbox"]:checked {
    content:"\E876";
    font-family:"Material Icons";
    font-size:1.3rem;
    color:#fff;
    background:rgb(74,144,226);
    box-shadow:0 0 0 .2rem rgb(74,144,226);
    width:1.4rem;
    height:1.4rem;
}

.detailsPrimary input[type="checkbox"]:checked::before,
.detailsInternalOnly input[type="checkbox"]:checked::before,
.detailsInsurance input[type="checkbox"]:checked::before,
.relinkQupid input[type="checkbox"]:checked::before {
    content:"\E876";
    font-family:"Material Icons";
    color:#fff;
}

.detailsPrimary.primary { pointer-events:none; }

.detailsDate {
    position:relative;
    width:35rem;
}

.datePicker {
    display:inline-block;
    position:relative;
    top:-.8rem;
    margin:0 0 0 1.2rem;
    width:calc(100% - 4rem);
}

.detailsDate label  {
    position:absolute;
    top:0;
    display:inline-block;
    color:rgba(0,0,0,.54);
    pointer-events:none;
}

.detailsDate input:not([type="checkbox"]) {
    background:transparent;
    padding:1.8rem 0 0;
    border:inherit;
    width:100%;
    cursor:pointer;
}

.photoDisplay {
    position:relative;
    display:table;
    width:100%;
}

.propertyPhoto {
    display:table-cell;
    position:relative;
    font-size:1.3rem;
    color:rgba(0,0,0,.87);
    background-color:#fff;
    vertical-align:middle;
    padding:2rem;
    border:.1rem solid #f2f2f2;
    width:65%;
}

.propertyPhoto img {
    display:block;
    width:100%;
}

.photoControls {
    position:absolute;
    bottom:4.8rem;
    left:0;
    background-color:#f9f9f9;
    padding:1.6rem 2rem 1.6rem 1.2rem;
    border-top:.1rem solid #eee;
    width:100%;
    height:8.2rem;
}

.photoControls span,
.photoControls i {
    display:inline-block;
    vertical-align:middle;
}


.material-icons.photoButton {
    color:rgba(74,144,226,1);
    text-align:center;
    line-height:2;
    background-color:rgba(255,255,255,.5);
    border-radius:50%;
    box-shadow:inset 0 0 .1rem #ddd;
    width:4.8rem;
    height:4.8rem;
    cursor:pointer;
    transition:background-color .5s ease;
}

.material-icons.photoButton:nth-child(-n+2) {
    position:relative;
    background-color:initial;
    box-shadow:initial;
}

.material-icons.photoButton + .material-icons.photoButton { margin-left:.5rem; }

.material-icons.photoButton:hover {
    color:rgba(255,255,255,1);
    background-color:rgba(74,144,226,1);
    box-shadow:initial;
}

.photoDelete:hover { background-color:#d2362b; }

.photoPagination {
    text-align:center;
    width:calc(100% - 26.8rem);
    cursor:pointer;
}

.photoPagination span {
    display:inline-block;
    background-color:rgba(0,0,0,.1);
    border-radius:50%;
    margin:.25rem;
    width:1.4rem;
    height:1.4rem;
    opacity:.4;
}

.setPrimary-label {
    display:block;
    position:absolute;
    bottom:1.8rem;
    left:calc(50% - 8rem);
    color:rgba(0,0,0,.54);
    text-align:center;
    width:20rem;
}

.setPrimary-label[style="display:none;"] ~ .photoPagination { margin-top:initial; }

.photoPagination span.active { opacity:1; }

.photoPagination span.invalid,
.photoPagination span.completed,
.photoPagination span.primary {
    position:relative;
    background-color:#fff;
}

.photoPagination span.invalid::before,
.photoPagination span.completed::before,
.photoPagination span.primary::before {
    position:absolute;
    top:-.1rem;
    left:-.18rem;
    content:"\E8D0";
    font-family:"Material Icons";
    font-size:1.7rem;
    color:rgba(74,144,226,1);
    line-height:1;
}

.photoPagination span.invalid::before { content:"\E000"; color:#d2362b; }

.photoPagination span.completed::before { content:"\E86C"; }

.photoPagination span.primary::before { content:"\E8D0"; }

.detailsWarning {
    color: #a94442;

    background-color: #f2dede;
    border-color: #ebccd1;
    padding: 15px;
    border: 1px solid transparent;
    border-radius: 4px;
    margin: 2rem 2rem 20px 0;
    float: left;
}
.detailsWarning h4 {
    margin-top: 0;
    color: inherit;
}

.detailsWarning hr {
    border-top-color: #e4b9c0;
}

.detailsWarning > p,
.detailsWarning > ul {
    margin-bottom: 0;
}

.detailsWarning > p + p {
    margin-top: 5px;
}

.warning-dismissable,
.warning-dismissible {
    padding-right: 35px;
}

.warning-dismissable .close,
.warning-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit;
}

.close {
    float: right;
    font-size: 21px;
    font-weight: bold;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=20);
    opacity: .2;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    filter: alpha(opacity=50);
    opacity: .5;
}

button.close {
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
}

.fade {
    opacity: 0;
    -webkit-transition: opacity .15s linear;
         -o-transition: opacity .15s linear;
            transition: opacity .15s linear;
  }

.fade.in {
    opacity: 1;
}

/*  ============================================================================================ */
/*  IMAGE DETAILS TAG PICKER  ================================================================== */
/*  ============================================================================================ */

.detailsTags {
    margin:2.4rem 0 1rem;
    float:left;
}

.detailsTags > label {
    color:rgba(0,0,0,.54);
    line-height:1.7;
    margin-left:1.2rem;
    vertical-align:top;
}

.tagPicker { margin:.3rem 0 0 3.8rem; }

.tagRow { margin:0 0 1rem; }

.tagSelect {
    position:relative;
    top:-.1rem;
    display:inline-block;
    border-radius:.3rem;
    margin-right:.5rem;
    vertical-align:middle;
    width:6rem;
    height:2.25rem;
    overflow:hidden;
}

.advSearch-group .tag label,
.tag label,
.tagSelect select {
    display:inline-block;
    font-size:1.1rem;
    color:initial;
    line-height:2;
    background:#e7e7e7;
    padding:0 .75rem;
    border-radius:.3rem;
    margin:0 .5rem 0 0;
    box-sizing:border-box;
    cursor:pointer;
    transition:background-color .2s ease;
    width:inherit;
    height:initial;

}

.tag label:hover { background:rgba(74,144,226,.4) }

.tag input[type=checkbox] { display:none; }

.tag input[type=checkbox]:checked + label {
    color:#fff;
    background:rgba(74,144,226,1);
}

.tagSelect select {
    padding-right:1.8rem;
    border-right:inherit;
    height:inherit;
    width:10rem;
}

.tagSelect::after {
    position:absolute;
    top:.8rem;
    right:1rem;
    content:"";
    width:0;
    height:0;
    border-left:.4rem solid transparent;
    border-right:.4rem solid transparent;
    border-top:.5rem solid #333;
    pointer-events:none;
}

.tagSelect.tagSelected::after { border-top:.5rem solid #f2f2f2; }

.tagSelect select.tagSelected {
    color:#fff;
    background-color:rgba(74,144,226,1)
}

/*  ============================================================================================ */
/*  RELINK PROPERTY PHOTOS  ==================================================================== */
/*  ============================================================================================ */


.relinkWrapper {
    background-color:#f5f5f5;
    padding:0 0 .1rem;
    margin:1.6rem auto 5rem;
    max-width:100rem;
}

.relinkTitle {
    background-color:#fff;
    padding:.8rem 2.4rem 1.6rem 2.4rem;
    margin:0;
    overflow:hidden;
}

.relinkTitle h1 {
    color:#e65100;
    margin:1.6rem 0;
}

.relinkRow {
    position:relative;
    display:table;
    background:#fff;
    padding:1.2rem .8rem;
    margin:0;
    border-bottom:.1rem solid #f2f2f2;
    width:100%;
    -moz-transition:all .1s linear;
    -webkit-transition:all .1s linear;
    transition:all .1s linear;
}

.relinkRow:last-child {
    border-bottom:none;
}

.relinkCell {
    display:table-cell;
    position:relative;
    font-size:1.2rem;
    text-align:center;
    vertical-align:middle;
    width:calc(100% - 56.4rem);
    padding:0 1.6rem;
}

.relinkCell:first-child {
    text-align:left;
    width:28.2rem;
}

.relinkCell:last-child {
    text-align:right;
    width:28.2rem;
}

.relinkDetails {
    display:inline-block;
    color:#005b7f;
    width:calc(100% - 12.8rem);
    vertical-align:top;
    padding:0 0 0 1.2rem;
}

.relinkCell h3 {
    position:relative;
    font-size:1.5rem;
    line-height:1.9;
    font-weight:600;
    color:rgba(0,0,0,.54);
    background-color:#f9f9f9;
    padding:.4rem .8rem .3rem .8rem;
    border:.1rem solid rgba(0,0,0,.12);
    border-radius:.3rem;
    height:3.9rem;
    max-width:25rem;
}

.relinkCell i {
    position: absolute;
    top: 4.4rem;
    right: 2rem;
    pointer-events: none;
}

.relinkRow p {
    font-size:1.3rem;
    font-weight:600;
    color:#005b7f;
    margin:1rem 0 .5rem;
}

select.valRefOption.valRef-select + i {
    position: absolute;
    right: 3.8rem;
    pointer-events:none;
}

.valRef-select select {
    position:relative;
    font-size:1.5rem;
    font-family:"Open Sans", sans-serif;
    font-weight:600;
    padding:.4rem 6.4rem .3rem 1rem;
    border:.1rem solid rgba(0,0,0,.12);
    border-radius:.3rem;
    height:3.9rem;
    min-width:25rem;
    cursor:pointer;
}

select::-ms-expand { display:none; }

.relinkRow:nth-child(2) .valRef-current::before,
.relinkRow:nth-child(2) .valRef-select::before {
    position:absolute;
    top:1.3rem;
    content:"New Val Ref:";
    font-weight:600;
    font-size:1.2rem;
    color:rgba(0,0,0,.54);
    z-index:1;
}

.relinkRow:nth-child(2) .valRef-current::before {
    content:"Current Val Ref:";
}

.relinkButtons {
    text-align:right;
    padding:.8rem 1.8rem 3rem;
}



/*  ============================================================================================ */
/*  MASTER DETAILS  ============================================================================ */
/*  ============================================================================================ */

.listingControls {
    padding:0;
    margin:3.6rem auto 1rem;
    max-width:144rem;
    overflow:auto;
}

.listingControls span {	float:left; }

.listingControls ul {
    margin-top:.5rem;
    float:right;
    overflow:hidden;
}

.salesRow .listingControls li { display:inline-block; }

.listingControls li {
    display:inline-block;
    font-size:1.3rem;
    color:#455a64;
    line-height:3.2rem;
    padding:0 1rem;
    margin:.4rem 0 0 1rem;
    vertical-align:middle;
}

.listingControls li strong { font-size:1.5rem; }

.listingControls li label { font-size:1.1rem; }

li.md-qivs,
li.md-qvms,
li.md-salesAnalysis,
li.md-qv-col-sale-id,
.jobStatusboard li.md-qivs,
.salesRow li.md-sales {
    display:inline-block;
    position: relative;
    font-size:1rem;
    color: #fff;
    line-height: 1.8;
    background-color: #fc932f;
    padding: .3rem .3rem .25rem .6rem;
    border: none;
    border-radius: .3rem;
    margin:0 0 0 .5rem;
    transition: opacity .2s ease;
}

li.md-salesAnalysis{
    background-color: rgba(74,144,226,1);
    position: absolute;
    bottom: -1rem;
    right: 50.3px;
    width: 10.5rem;
}

li.md-qv-col-sale-id{
    background-color: rgba(74,144,226,1);
    position: absolute;
    bottom: -4rem;
    right: 50.3px;
    width: 10.5rem;
}

li.md-salesAnalysis i { float:right; }

.listingControls li.md-qivs label,
.listingControls li.md-qvms label,
.listingControls li.md-sales label {
    font-size:1rem;
}

li.md-qivs:hover,
li.md-qvms:hover,
li.md-qv-col-sale-id:hover,
li.md-salesAnalysis:hover,
li.md-sales:hover {
    opacity:.8;
}

li.md-qvms { margin-right:0; }

li.md-qpid {
    font-size:1.1rem;
    padding:0;
    margin-left:1.2rem;
    border-radius:0;
}

li.md-qivs a,
li.md-qvms a {
    color:#fff;
}

li.md-qvms i,
li.md-qivs i,
li.md-salesAnalysis i,
li.md-sales i {
    display:inline-block;
    font-size:1.4rem;
    vertical-align:text-top;
    width:1.5rem;
}

li.md-valRef {
    font-size:1.1rem;
    padding:0;
    margin-left:1.2rem;
    border-radius:0;
}

li.md-valRef select {
    display: inline-block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #455a64;
    line-height: 1.8;
    border-right:none;
    margin: -.5rem 0 0 0.3rem;
    height: inherit;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid;
    z-index: 1;
    appearance: auto;
    -webkit-appearance: auto;
}


li.md-valRef .material-icons.md-dark {
    display: inline-block;
    position: relative;
    color: #283c64;
    line-height: 1.15;
    /* border-left: .1rem solid rgba(0,0,0,.25); */
    margin: 0 0 0 3.5rem;
    vertical-align: middle;
    pointer-events: none;
}

li.md-extensions span {
    position: inherit;
    display:inline-block;
    font-size:1.1rem;
    color:#fff;
    text-align:center;
    line-height: 2.2;
    background:#204d90;
    padding:0 .5rem;
    border-radius:.4rem;
    margin: -0.4rem 0 0 -2rem;
    float:inherit;
    vertical-align:middle;
    min-width: 2.4rem;
}
li.md-extensions {
    margin: 0 0 0 2rem;
}

li.md-copyValref i{
    margin: 0.8rem 0 0 -2rem;
    font-size: 2rem;
}

.listingControls li.mdl-button--icon {
    font-size:2.4rem;
    color:inherit;
    line-height:normal;
    padding:0;
    border-radius:50%;
    margin:.3rem 0 0 1rem;
    min-width:3.2rem;
    width:3.2rem;
    overflow:hidden;
    height:3.2rem;
}


.listingButton i {
    display:inline-block;
    vertical-align:middle;
}

.listingButton.material-icons {
    color:rgba(74,144,226,1);
    text-align:center;
    line-height:2;
    background-color:transparent;
    border-radius:50%;
    box-shadow:none;
    width:4.8rem;
    height:4.8rem;
    cursor:pointer;
    transition:background-color .5s ease;
    margin: 0;
}

.listingButton:nth-child(-n+2) {
    position:relative;
    background-color:initial;
    box-shadow:initial;
}

.listingButton + .listingButton { margin-left:.5rem; }

.listingButton:hover {
    color:rgba(255,255,255,1);
    background-color:rgba(74,144,226,1);
    box-shadow:initial;
}

.listingButton.searchResults {
    position: relative;
    color: transparent;
    line-height:1;
    transition: background-color .5s ease, box-shadow .5s ease;
    float: left;
    font-style: normal;
}

.listingButton.searchResults::before,
.listingButton.searchResults::after {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    content:"";
    font-family:"Material Icons";
    font-weight:normal;
    color:#5290db;
    pointer-events:none;
}

.listingButton.searchResults::before {
    top: 1.5rem;
    left: 0.5rem;
    content:"\E314";
    font-size: 1.8rem;
}

.listingButton.searchResults::after {
    top: 1.3rem;
    left: 1.7rem;
    content: "\E8EE";
    font-size: 2.2rem;
}

.listingButton.searchResults:hover::before,
.listingButton.searchResults:hover::after {
    color:#fff;
}

.masterDetails {
    margin:1.6rem auto 5rem;
    min-width:1366px;
    max-width:144rem;
}

.masterDetails-Wrapper {
    background-color:#fff;
    padding:0 0 3rem;
}

.masterDetails-Wrapper .expandAll {
    position:absolute;
    top:1.1rem;
    right:1rem;
}

.qvtd-rural-expander .expandAll {
    position: relative;
    top: -0.1rem;
    right: 0;
    padding: 0 5px;
}

.qvtd-rural-expander .section-title {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 0;
    margin-top: 0.5rem;
    background-color: #283c64;
    color: #fff;
    padding: 0.5rem;
    padding-left: 1rem;
}

.md-full::after {
    content:".";
    visibility:hidden;
    display:block;
    height:0;
    clear:both;
}

.md-left {
    float: right;
    padding:3.6rem 1.8rem 3.6rem 3.6rem;
    width:calc(100% - 46rem);
    box-sizing:border-box;
    vertical-align:top;
}

.md-right {
    float: right;
    padding:3.6rem 1.8rem 3.6rem 3.6rem;
    width:83%;
    box-sizing:border-box;
    vertical-align:top;
}

.md-full.md-summaryHeader {
    position:relative;
    background-color: #0e2b41;
    padding-bottom: 1.6rem;
}

.md-full.md-summaryHeader::before {
    position: absolute;
    top: 8rem;
    left: 69.6rem;
    content: "";
    width: 16rem;
    height: 16rem;
    background-image: url(../../images/logo-noPhotos.png);
    background-position: -0.7rem 3.5rem;
    background-size: contain;
    background-repeat: no-repeat;
    border: 1px solid rgba(255,255,255,.5);
    border-radius: 50%;
}

.md-propertyOverview  {
    position:relative;
    color:#fff;
    background-color:#214d90;
    padding:1.6rem 2rem;
    margin-bottom:1.6rem;
    width:55rem;
    height:30rem;
    overflow:auto;
    float:left;
    box-sizing:border-box;
}

.md-categoryDescription {
    color: #fff;
    background-color:#162b3f;
    padding: 1rem 2rem .65rem;
    margin: -2rem -2rem 0;
}

.md-categoryDescription li {
    display: inline-block;
    width: calc(100% - 12.3rem);
}

.md-categoryDescription li:first-of-type {
    font-size:1.3rem;
    font-weight:600;
}

.md-categoryDescription li:first-of-type span {
    font-size:1.1rem;
    font-weight: 400;
}

.md-categoryDescription li:last-of-type {
    font-size:1.1rem;
    color:rgba(255,255,255,.7);
    text-align: right;
    width: 12rem;
}

.md-categoryDescription li:last-of-type strong { color:rgba(255,255,255,1); }

.md-propertyOverview-inner  {
    display:table-cell;
    position:relative;
    color:#fff;
    background-color:#214d90;
    width:55rem;
    height:30rem;
    overflow:auto;
    float:none;
    box-sizing:border-box;
    vertical-align:bottom;
}

.md-summaryAddress { float:none; }

.md-summaryAddress h1 {
    font-size:2.4rem;
    font-weight:400;
    margin-top: 0.5rem;
}

.md-summaryAddress h1 span {
    display:block;
    font-size:1.3rem;
    font-weight:300;
    color:rgba(255,255,255,.9);
    margin-top: 0;
}

.md-addrsummaryAddressess em {
    display:block;
    font-size:1.15rem;
    font-weight:300;
    color:rgba(255,255,255,.7);
    margin-top:.3rem;
}

.md-summaryOwners,
.md-summaryTotals {
    font-size: 1.3rem;
    font-weight:600;
    text-align: left;
    margin-top:2rem;
    width: calc(33% - 1.6rem);
    min-width: fit-content;
    float:right;

}

.md-summaryOwners {
    width: calc(66% - 3.2rem);
    float:left;
}

.md-summaryOwners li,
.md-summaryTotals li {
    line-height:1.4;
    margin-bottom: 0;
    clear:both;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.md-summaryOwners label,
.md-summaryTotals label {
    display: inline-block;
    font-size: 1.1rem;
    font-weight: 300;
    line-height:1.8;
    text-align: left;
    width:7rem;
    float:left;
}

.md-summaryOwners label { width:9rem; }

.md-summaryTotals span {
    display:inline-block;
    font-size:1.3rem;
    font-weight:300;
    line-height:1;
    margin-left:.2rem;
}

.md-summaryTotals sup {
    display:inline-block;
    font-size:.9rem;
    margin:-.1rem 0 0 .2rem;
}

.md-estimates-wrapper {
    background-color:#2c3c61;
    padding:.5rem 1rem .5rem;
}

.md-marketEstimate {
    position:inherit;
    background:transparent;
    padding:0;
    margin:0 0 .5rem;
    width:100%;
    height:auto;
    box-sizing:border-box;
    overflow:hidden;
}

.md-marketEstimate li {
    display: inline-block;
    font-weight: 400;
    text-align:right;
    padding: 0;
    margin: 0;
    vertical-align: top;
    width:40%;
    height: auto;
    float:left;
}

.md-marketEstimate li.me-comparison { float: right; }

.md-marketEstimate li:first-child {
    position:inherit;
    display: block;
    font-size:1.1rem;
    color: rgba(255,255,255,.7);
    text-align: left;
    padding: 0;
    margin:0 0 -0.2rem;
    height: auto;
    width: auto;
    float:none;
}

.md-marketEstimate li:first-child strong { color: rgba(255,255,255,1); }

.md-marketEstimate li h3 {
    font-size:2rem;
    line-height: 1.4;
    text-align: left;
    margin: 0;
}

.md-marketEstimate li.me-comparison label {
    display: inline-block;
    position: relative;
    color:transparent;
    line-height: 0;
    margin: 0 .5rem 0 0;
    width: calc(100% - 8rem);
    vertical-align: top;
}

.md-marketEstimate li.me-comparison label::before {
    display: block;
    position:absolute;
    right: .5rem;
    font-size: .9rem;
    color:#fff;
    line-height: 1.5;
    text-align: right;
    margin: 0 0 .1rem 0;
    width: 9.5rem;
    vertical-align: middle;
}

.md-marketEstimate li.me-compare-CV label::before {
    content:"Compared to Current Capital Value";
}

.md-marketEstimate li.me-compare-LV label::before {
    content:"Compared to Current Land Value";
}


@media only screen and (max-width:1400px) {

    .md-marketEstimate li.me-compare-CV label::before {
        content:"Compared to Current CV";
        width:6rem;
    }
    .md-marketEstimate li.me-compare-LV label::before {
        content:"Compared to Current LV";
        width:6rem;
    }

}


.md-marketEstimate span.valueUp,
.md-marketEstimate span.valueDown {
    font-size: 1.4rem;
    line-height: 2;
    background: #626970;
    padding: 0 .5rem 0 2rem;
    margin: 0;
    width: 7rem;
    height: 2.8rem;
    float: right;
}

.md-marketEstimate span.valueUp   { background: #4caf50; }
.md-marketEstimate span.valueDown { background: #fc3d39; }


.md-marketEstimate .valueUp::before,
.md-marketEstimate .valueDown::before {
    top:calc(50% - .3rem);
    left:.7rem;
    border-left:.4rem solid transparent;
    border-right:.4rem solid transparent;

}

.md-marketEstimate .valueUp::before { border-bottom:.5rem solid #fff; }
.md-marketEstimate .valueDown::before { border-top:.5rem solid #fff; }

.md-summaryValues {
    display:block;
    position:absolute;
    bottom:0;
    left:0;
    background-color:#2c3c61;
    padding:.8rem 0 1.2rem .8rem;
    width:100%;
    clear:both;
}

.md-marketEstimate + .md-values { bottom:6.5rem; }

.md-summaryValues li {
    display:inline-block;
    font-size:1.8rem;
    margin:0 .7rem;
    width:calc(25% - 1.6rem);
    vertical-align:top;

}

.md-summaryValues.md-summaryLastSale li {
    width:calc(33% - 1.6rem);
}

.md-summaryValues li label {
    display: block;
    font-size:1.1rem;
    color: rgba(255,255,255,.7);
    line-height:2.4;
    border-bottom: .1rem solid rgba(255,255,255,.2);
    margin-bottom:.35rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.md-summaryValues li:last-of-type span {
    display: inline-block;
    font-weight:bold;
}

.md-summaryValues li:last-of-type span::before {
    display: inline-block;
    content: "/";
    font-weight:400;
    margin: 0 .2rem 0 .2rem;
}

.md-summaryValues span,
.revisionValues div {
    display:block;
    font-size:1.2rem;
    font-weight:300;
    margin-top:0;
}

.md-summaryValues strong,
.revisionValues div strong {
    font-size:1.35rem;
    font-weight:300;
    margin-right:.2rem;
}

.revisionValues div strong { font-weight:400; }

.md-summaryValues strong::after,
.revisionValues div strong::after {
    display:inline-block;
    content:"/";
    margin:0 .02rem 0 .2rem;
}

.maoriLand .md-propertyOverview { background-color:#910d0d; }

.maoriLand .md-categoryDescription { background-color:#1b1b1b; }

.maoriLand .md-address { padding-top:.8rem; }

.maoriLand .md-summaryValues {	background-color:#2b2b2b; }

.maoriLand .md-totals { margin-top: 0.8rem; }

.maoriLand .md-full.md-summaryHeader { background-color:#1b1b1b; }

.maoriLand .md-rtvSales-overview { background-color:#1b1b1b; }

.maoriLand .md-estimates-wrapper { background-color: #2b2b2b; }

.maoriLand .md-rtvSales-overview .md-summaryValues { background-color:#1b1b1b; padding-bottom:0; }

.maoriLand .md-summaryValues.md-summaryLastSale li:first-of-type {
    display: inline-block;
    font-size: 1.8rem;
    padding-left:initial;
    margin: 0 1rem;
    width: calc(33% - 2.1rem);
    overflow:initial;
}

.maoriLand .md-rtvSales-overview .md-summaryValues li { width: calc(33% - 1.6rem); }

.maoriLand .md-rtvSales-overview .md-summaryValues li.maoriLand-adjustment {
    margin:2rem 1rem 0;
    width: calc(16.5% - 2.1rem);
}

li.maoriLand-adjustment label {
    font-size:1rem;
    border-bottom:none;
    margin:.2rem 0 0;
    overflow: inherit;
}

li.maoriLand-adjustment span {
    position:relative;
    display:inline-block;
    font-size: 1.3rem;
    font-weight:500;
    color: #fff;
    line-height: 2;
    text-align: center;
    background-color:#63686f;
    padding: 0 1rem;
    border-radius: .2rem;
    min-width:5rem;
}

li.maoriLand-adjustment.currentLumpSum span {
    background-color:#2c3c61;
}
li.maoriLand-adjustment.adjustmentPct span { background-color:#2c3c61; }
li.maoriLand-adjustment.totalOwners span { background-color:#910d0d; }

li.maoriLand-adjustment.ownersPct span::after,
li.maoriLand-adjustment.significancePct span::after {
    position:absolute;
    top: 0;
    right: -1.6rem;
    content: "=";
    font-size:1.6rem;
    font-weight:600;
    color: rgba(255,255,255,.5);
    line-height: 1.7;
    z-index:1;
}

li.maoriLand-adjustment.significancePct span::after { content: "+"; }
li.maoriLand-adjustment.totalOwners span::before { display:none; }

.md-revisionBox .revisionLumpSum label{
    font-size:1.3rem;
    font-weight:500;
}

.md-revisionBox .revisionLumpSum span{
    position:relative;
    display:inline-block;
    font-size: 1.3rem;
    font-weight:500;
    color: #fff;
    line-height: 2;
    text-align: left;
    background-color:#2c3c61;
    padding: 0 1rem;
    border-radius: .2rem;
    min-width:5rem;
    margin-left: .8rem;
}

.md-revisionBox .revisionLumpSum {
    position:relative;
    float: right;
    margin-top: -2.4rem;
    left: -3rem;
}


.revisionValues li.maoriLand-adjustment {
    margin-top: 2.2rem;
    vertical-align: top;
    width: 5rem;
}

.revisionValues li.maoriLand-adjustment.ownersPct { right: 9.5rem; }
.revisionValues li.maoriLand-adjustment.adjustmentPct { right: 2rem; }

.revisionValues.maoriLand li.maoriLand-adjustment label {
    font-size:1rem;
    line-height:1.8;
    border: none;
}

.revisionValues li.maoriLand-adjustment.ownersPct span::after,
.revisionValues li.maoriLand-adjustment.significancePct span::after {
    color: #2c3c61;
}

.md-totals sup {
    display:inline-block;
    font-size:.9rem;
    margin:-.1rem 0 0 .2rem;
}

.md-photoGallery {
    width:44.5rem;
    height:30rem;
    float:left;
    margin-bottom:1.6rem;
}

.morePhotos {
    bottom: 8.8rem;
    left: 101rem;
    font-size: .9rem;
    color: #a1a4a9;
    line-height: 1.1;
    height: 2.5rem;
    height: 2.3rem;
    box-sizing: border-box;
    padding: 0.2rem .2rem 0.2rem .3rem;
    transition: all .2s linear;
}

.morePhotos span {
    display:inline-block;
    color:#bbb;
    width:calc(100% - 2.4rem);
    vertical-align:middle;
}

.morePhotos i {
    display:inline-block;
    font-size:2.2rem;
    color:#bbb;
    width:2.4rem;
    vertical-align:middle;
}

.morePhotos:hover {
    background-color:#fff;
    box-shadow: none;
}

.morePhotos:hover i, .morePhotos:hover span {
    color:#2c94e2;
    cursor:pointer;
}


.md-rtvSales-overview {
    position:relative;
    color: #fff;
    background-color: #0e2b41;
    padding: 1.2rem;
    margin-bottom:1.6rem;
    width: calc(100% - 99.5rem);
    height:30rem;
    float: left;
    box-sizing: border-box;
}

.md-propertySale {
    display: inline-block;
    position:relative;
    padding:1rem;
    margin-left: -.4rem;
    width: 25%;
    vertical-align: bottom;
}

.md-summaryLastSale {
    position: absolute;
    color: #fff;
    background-color:transparent;
    padding: 0 0.4rem 0 .8rem;
}

.md-summaryLastSale li { margin-top:.3rem; }

.md-summaryLastSale li:first-of-type {
    display:block;
    font-size: 1.1rem;
    font-style:italic;
    color: rgba(255,255,255,.7);
    padding-left:1rem;
    margin:0 0 .9rem;
    width:100%;
    overflow:auto;
}

.md-summaryLastSale li:first-of-type strong {
    font-size:1.1rem;
    font-weight:600;
    color: rgba(255,255,255,1);
}

.md-summaryLastSale li:first-of-type strong.saleClass {
    display: inline-block;
    font-size: 1rem;
    font-style: normal;
    text-align: center;
    line-height: 2.5;
    color: #455963;
    background-color: #fff;
    padding: 0 .3rem;
    border-radius: 50%;
    margin-left: .5rem;
    width: 2.5rem;
    height: 2.5rem;
    cursor:default;
}

.md-legacyMonarchDataCheckFlags {
    position: relative;
    top: 200px;
    text-align: right;
    color: #fff;
    background-color:transparent;
    padding: 0 0.4rem 0 0.8rem;
}

.md-legacyMonarchDataCheckFlags button {
    background:none;
    border:none;
    text-align: right;
}
.md-legacyMonarchDataCheckFlags #issuesCheckedButton:hover {
    text-decoration:underline;
}

.md-newMonarchDataCheckFlags {
    position: relative;
    top: 190px;
    text-align: right;
    color: #fff;
    background-color:transparent;
    padding: 0 0.4rem 0 0.8rem;
}

.md-newMonarchDataCheckFlags button {
    background:none;
    border:none;
    text-align: right;
}
.md-newMonarchDataCheckFlags #issuesCheckedButton:hover {
    text-decoration:underline;
}


.md-summaryLastSale strong.saleClass + ul.saleClass-description { opacity:0; }
.md-summaryLastSale strong.saleClass:hover + ul.saleClass-description { opacity:1; }

.md-summaryLastSale li ul.saleClass-description {
    position: absolute;
    top: 2rem;
    left: 12rem;
    background: #214d90;
    padding: 1rem 1.5rem;
    border-radius: .2rem;
    border:1px solid rgba(255,255,255,.1);
    width: auto;
    box-sizing: border-box;
    transition: all .2s linear;
    opacity: 0;
    pointer-events: none;
    z-index: 1;
}

.md-summaryLastSale li ul.saleClass-description li {
    display: block;
    font-size: 1rem;
    font-style:normal;
    color: rgba(255,255,255,.9);
    padding: 0;
    margin: 0;
    width: 100%;
    overflow: auto;
}

.md-summaryLastSale li ul.saleClass-description li span { font-weight:400; }

.md-summaryLastSale li ul.saleClass-description li:nth-of-type(2) {
    padding:.5rem 0;
    border-top:1px dotted rgba(255,255,255,.5);
    border-bottom:1px dotted rgba(255,255,255,.5);
    margin:.5rem 0;
}

.md-summaryLastSale li  ul.saleClass-description li em { display:block; font-weight:normal; }

ul.saleClass-description li span:first-of-type::after {
    display: inline-block;
    content: " – ";
    padding: 0 0.5rem;
}

.md-summaryLastSale li span { display: inline-block; font-weight:bold;}

.md-summaryLastSale li span::before {
    display: inline-block;
    content: "/";
    font-weight:400;
    margin:0 .2rem 0 .2rem;
}

.md-summaryLastSale li:first-of-type span.saleStatus {
    font-size: 1.1rem;
    font-style:normal;
    color: #fff;
    line-height: 2;
    text-align: center;
    background: #214d90;
    padding: 0 2rem;
    margin-right:1.5rem;
    border-radius: 1.1rem;
    min-width: 10.6rem;
    width: calc(33% - 2.1rem);
    float:right;
}

.md-summaryLastSale li:first-of-type strong::after,
.md-summaryLastSale li:first-of-type span::before {
    display:none;
}

.md-summary { margin-bottom:4rem; }

.md-summary h3 {
    font-size:1.6rem;
    font-weight:600;
    margin-bottom:.5rem;
}

.md-summary div,
.md-summary p {
    font-size:1.4rem;
    font-weight:400;
    line-height:1.6;
}

.md-summary textarea {
    font-size: 1.35rem;
    font-weight: 400;
    font-family: 'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif;
    line-height: 1.6;
    padding: 1rem;
    border: .1rem solid #fff;
    height: 12rem !important;
}

.md-summary textarea:focus {
    font-size:1.4rem;
    font-weight:400;
    line-height:1.6;
    padding:1rem;
    -webkit-box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
    -moz-box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
    box-shadow:inset 0 0 0 .2rem rgba(51,161,230,.75);
    height:12rem !important;
}

.md-summary button, .md-summary input[type=button] {
    font-size:1.3rem;
    color:#333;
    line-height:2;
    background-color:#ddd;
    border:none;
    border-radius:.2rem;
    margin-top:.5rem;
    width:auto;
    min-width:10rem;
    overflow:visible;
    -webkit-appearance:none;
}

.md-summary button[type="submit"] {
    color:#fff;
    background-color:#4c8ce1;
}

.md-summary button + button { margin-left:1rem !important;	}

ul.md-landMas-tabs {
    background:rgba(237,241,245,1);
    padding:0 2.5%;
}

.md-landMas-tabs li {
    display:inline-block;
    width:calc(25% - .3rem);
}

.md-landMas-tabs hr {
    height:.25rem;
    width:calc(25% - .3rem);
    margin:0;
    background:#0e3a83;
    border:none;
    transition:.3s ease-in-out;
}

.md-landMas-tabs hr.MasTab-1 { margin-left:0%;	}
.md-landMas-tabs hr.MasTab-2 { margin-left:25%; }
.md-landMas-tabs hr.MasTab-3 { margin-left:50%; }
.md-landMas-tabs hr.MasTab-4 { margin-left:75%; }

/* FOR PROPERTY PLUS TAB
.md-landMas-tabs li {
    display:inline-block;
    width:calc(20% - .3rem);
}

.md-landMas-tabs hr {
    height:.25rem;
    width:calc(20% - .3rem);
    margin:0;
    background:#0e3a83;
    border:none;
    transition:.3s ease-in-out;
}

.md-landMas-tabs hr.MasTab-1 { margin-left:0%;	}
.md-landMas-tabs hr.MasTab-2 { margin-left:20%; }
.md-landMas-tabs hr.MasTab-3 { margin-left:40%; }
.md-landMas-tabs hr.MasTab-4 { margin-left:60%; }
.md-landMas-tabs hr.MasTab-5 { margin-left:80%; }

 */

.md-landMas-tabs li span {
    display:block;
    font-size:1.4rem;
    color:#555;
    text-align:center;
    padding:1.6rem 0 1.4rem;
    box-sizing:border-box;
    cursor:pointer;
}

.md-landMas-tabs li span:hover {
    color:#0e3a83;
    opacity:.6;
}

.md-landMas-tabs li span.is-active {
    position:relative;
    color:#0e3a83;
}

.md-landMas-tabs li span.is-active:hover { opacity:initial; }

.md-landMas-Container {
    display:none;
    background: rgba(237,241,245,.2);
}

.md-landMas-Container.active { display:block; }

.md-landMas {
    font-size:1rem;
    color:rgb(55,61,64);
    padding:4rem 3.6rem 1.6rem;
    border: 1px solid rgba(237,241,245,1);
    border-top:none;
    margin-bottom:3.6rem;
}

.md-landMas strong {
    display:block;
    font-size:1.6rem;
    font-weight:400;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}

.md-landMas li {
    display:inline-block;
    position:relative;
    padding-left:5.2rem;
    margin-bottom:2.4rem;
    width:calc(33.3% - .3rem);
    box-sizing:border-box;
}

.md-landMas li:before {
    display: inline-block;
    position:absolute;
    top:.4rem;
    left:0;
    font-family: "QV-Monarch";
    font-size:3.2rem;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    line-height: 1;
    text-decoration: inherit;
    text-rendering: optimizeLegibility;
    text-transform: none;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-smoothing: antialiased;
    color:rgba(74,144,226,1);
    width:3.2rem;
    height:3.2rem;
}

.showallData .md-landMas li:before {
    font-size:2.2rem;
    width:2.2rem;
    height:2.2rem;
}

.md-landMas li.md-masIcon-category::before 			{ content: "\f10c"; }
.md-landMas li.md-masIcon-landUse::before 			{ content: "\f12d"; }
.md-landMas li.md-masIcon-units::before				{ content: "\f150"; }
.md-landMas li.md-masIcon-zone::before				{ content: "\f113"; }
.md-landMas li.md-masIcon-age::before				{ content: "\f106"; }
.md-landMas li.md-masIcon-walls::before				{ content: "\f108"; }
.md-landMas li.md-masIcon-roof::before				{ content: "\f159"; }
.md-landMas li.md-masIcon-tfa::before				{ content: "\f11e"; }
.md-landMas li.md-masIcon-landArea::before			{ content: "\f15c"; }
.md-landMas li.md-masIcon-siteCoverage::before		{ content: "\f154"; }
.md-landMas li.md-masIcon-carParks::before			{ content: "\f163"; }
.md-landMas li.md-masIcon-maoriLand::before			{ content: "\f13a"; }
.md-landMas li.md-masIcon-sub::before				{ content: "\f166"; }
.md-landMas li.md-masIcon-csi::before				{ content: "\f14e"; }
.md-landMas li.md-masIcon-viewScope::before			{ content: "\f142"; }
.md-landMas li.md-masIcon-lotPosition::before		{ content: "\f134"; }
.md-landMas li.md-masIcon-contour::before			{ content: "\f11b"; }
.md-landMas li.md-masIcon-houseType::before			{ content: "\f12a"; }
.md-landMas li.md-masIcon-bathrooms::before			{ content: "\f153"; }
.md-landMas li.md-masIcon-landscaping::before		{ content: "\f11f"; }
.md-landMas li.md-masIcon-eyb::before				{ content: "\f10a"; }
.md-landMas li.md-masIcon-modernisation::before		{ content: "\f139"; }
.md-landMas li.md-masIcon-mla::before				{ content: "\f12b"; }
.md-landMas li.md-masIcon-tla::before				{ content: "\f132"; }
.md-landMas li.md-masIcon-bedrooms::before			{ content: "\f104"; }
.md-landMas li.md-masIcon-toilets::before			{ content: "\f160"; }
.md-landMas li.md-masIcon-oli::before				{ content: "\f15d"; }
.md-landMas li.md-masIcon-deck::before				{ content: "\f15a"; }
.md-landMas li.md-masIcon-foundation::before		{ content: "\f120"; }
.md-landMas li.md-masIcon-laundryWorkshop::before 	{ content: "\f103"; }
.md-landMas li.md-masIcon-carAccess::before			{ content: "\f143"; }
.md-landMas li.md-masIcon-driveway::before			{ content: "\f117"; }
.md-landMas li.md-masIcon-umrg::before				{ content: "\f124"; }
.md-landMas li.md-masIcon-fsg::before				{ content: "\f123"; }
.md-landMas li.md-masIcon-outlier::before			{ content: "\f141"; }
.md-landMas li.md-masIcon-production::before		{ content: "\f112"; }
.md-landMas li.md-masIcon-bedroomStudy::before		{ content: "\f145"; }
.md-landMas li.md-masIcon-totalBaths::before		{ content: "\f162"; }
.md-landMas li.md-masIcon-kitchen::before			{ content: "\f121"; }
.md-landMas li.md-masIcon-redecorated::before		{ content: "\f14f"; }
.md-landMas li.md-masIcon-plumbing::before			{ content: "\f149"; }
.md-landMas li.md-masIcon-heating::before			{ content: "\f11c"; }
.md-landMas li.md-masIcon-insulation::before		{ content: "\f128"; }
.md-landMas li.md-masIcon-wiring::before			{ content: "\f14b"; }
.md-landMas li.md-masIcon-windows::before			{ content: "\f10f"; }
.md-landMas li.md-masIcon-altEnergy::before			{ content: "\f158"; }
.md-landMas li.md-masIcon-ventialtion::before		{ content: "\f100"; }


.md-landMas li.md-masIcon-todo::before 	{
    background:#ddd;
    border-radius:50%;
}


.rentalIncome {
    font-size: 2rem;
    background-color: rgba(240,248,254,1);
    padding: 1.6rem 3.2rem;
    border-top: .1rem solid #dbe8f2;
    margin-top:-3.6rem;
    margin-bottom: 3.6rem;
}

.rentalIncome span {
    display:inline-block;
}

.rentalIncome span:first-of-type {
    padding-right:1.6rem;
    border-right: .1rem solid #dbe8f2;
    margin-right:1.6rem;
}

.rentalIncome span strong {
    font-size: 1.35rem;
    font-weight: 400;
    margin-left: .2rem;
}

.rentalIncome label {
    display: block;
    font-size: 1rem;
    color: #336699;
    margin-bottom: .2rem;
}

.propertyplusButton {
    color: rgba(74,144,226,1);
    text-align: center;
    line-height: 2;
    border-radius: 50%;
    margin-top: -.3rem;
    width: 4.8rem;
    height: 4.8rem;
    cursor: pointer;
    transition: background-color .5s ease;
    float: right;
}

.propertyplusButton:hover {
    background-color: rgba(255,255,255,1);
    box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
}

.showallData .md-landMas {
    font-size:.95rem;
    padding:4rem 1.6rem 1.6rem 2.6rem;
}

.showallData .md-landMas li {
    padding-left:3rem;
    width:calc(25% - .3rem);
}

.update-sra-values-section .md-table h2 {
    display: inline-block;
    font-size: 2rem;
    font-weight: 700;
    position: relative;
    top: -52px;
}

.md-landMas strong {
    font-size:1.3rem;
    margin-bottom:.1rem;
}

.md-full.md-bodyWrapper { padding:3.6rem; }

.update-sra-values-section {
    padding:3.6rem;
    margin-left: 23rem;
    width: 83%;
}

.md-bodyWrapper .md-table { padding: 1.6rem 2.4rem 0; }

.md-bodyWrapper .md-table h2 {
    display: inline-block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 4rem;
}

.md-table {
    display:table;
    position:relative;
    font-size:1.2rem;
    padding:9rem 1.6rem 1.6rem;
    border-top: 1px solid #d7d7d7;
    border-left: 1px solid #d7d7d7;
    margin-bottom:3.6rem;
    width:100%;
}

.qv-block-table {
    display: block !important;
}

.md-property-info-table {
    float: left;
    margin-left: 2.8rem;
    margin-top: 5rem;
    width:30%;
}

.md-propertyInfo-table table, .property-info-skinny table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0.8rem;
    font-size: 14px;
}

.md-propertyInfo-table .title, .property-info-skinny .title {
    text-align: left;
    font-weight: bold;
    text-decoration: underline;
}

.md-propertyInfo-table .switch-title span, .property-info-skinny .switch-title span {
    text-align: left;
    font-weight: bold;
}

.md-propertyInfo-block, .md-propertyInfo-worksheets {
    font-size: 14px;
}

.noText {
    background-color: darkgrey;
    border-radius: 5px;
    padding: 0rem 0.7rem 0rem 0.7rem;
    color: white;
    text-align: center;
}

.yesText {
    background-color: #ff0000;
    border-radius: 5px;
    padding: 0rem 0.7rem 0rem 0.7rem;
    color: white;
    text-align: center;
}

.md-propertyInfo-block ul, .md-propertyInfo-worksheets ul, .property-info-skinny ul {
    display: flex;
    justify-content:space-between;
    padding:0.5rem;
    list-style-type: none;
}

.md-propertyInfo-block li, .md-propertyInfo-worksheets li, .property-info-skinny li {
    display: inline-block;
}

.md-propertyInfo-block li.c1 {
    float:right;
}

.md-propertyInfo-block li.c2 {
    display: inline-block;
    padding-left: calc(50% - 3.3rem);
}

.md-propertyInfo-block a {
    font-weight: bold;
    text-decoration: underline;
    white-space: nowrap;
    padding-right: calc(50% - -5.699999999999999rem);
}

.md-property-info-table h3, .property-info-skinny h3 {
    font-size:1.5rem;
    font-weight:600;
}

.md-propertyInfo-worksheets, .property-info-skinny .worksheets {
    padding-bottom: 2rem;
}

.md-propertyInfo-worksheets ul, .property-info-skinny .worksheets ul {
    background-color: rgba(240,248,254,1);
}

.md-rural-worksheet {
    line-height: 50px;
    padding-left: 1rem;
    font-size: 14px;
}

.md-rural-worksheet .title{
    font-weight: bold;
}

.md-rural-worksheet .yes-no {
    text-align: right;
    padding-right: 2rem;
}

.md-rural-worksheet li {
    display: inline-block;
    width: calc(50% - 0.3rem);
}

.md-rural-worksheet li.create-worksheet {
    position: relative;
    bottom: 1rem;
}

.md-rural-worksheet li a:first-of-type {
    text-decoration: underline;
}

.md-rural-worksheet li:last-of-type {
   white-space: nowrap;
}

.md-table h3, .sales-section-heading {
    position:absolute;
    top:1.6rem;
    font-size:1.5rem;
    font-weight:600;
    padding:0 0 1.2rem;
}

.md-table h3.qivs-sales a {
    position: relative;
    color:rgba(0,0,0,.87);
    text-decoration:none;
}

.md-table h3.qivs-sales a::before {
    position: absolute;
    top:.15rem;
    right:-7.5rem;
    content:"HISTORY";
    color:#fff;
    font-size:1rem;
    font-weight:400;
    line-height: 1;
    background-color:#fc932f;
    padding: 0.5rem 2rem 0.5rem 0.6rem;
    border-radius:.3rem;
}

.md-table h3.qivs-sales a::after {
    position: absolute;
    top: .2rem;
    right: -7.1rem;
    content:"\E0B2";
    font-family:"Material Icons";
    font-size: 1.3rem;
    font-weight: normal;
    color: #fff;
}

.md-table h5 {
    display: block;
    font-size: 1.1rem;
    line-height: 1.6;
    color: #0e3a83;
    width: 100%;
    height: 2.2rem;
}

.md-table p {
    position:absolute;
    top:4.8rem;
    font-size:1.4rem;
    font-weight:400;
    padding:0 0 2rem;
}

.md-table h5 + p {
    position:inherit;
    margin-right:4rem;
}

.md-table p span {
    display: inline-block;
    font-size: 1.2rem;
    color: #fff;
    line-height: 1.8;
    background-color: rgba(74,144,226,1);
    padding: 0 .6rem 0 .5rem;
    border-radius: .25rem;
    margin: 0 0 0 .5rem;
}



.md-table p + dl.toraDescription { opacity:0; }
.md-table p:hover + dl.toraDescription { opacity:1; }

dl.toraDescription {
    position: absolute;
    top: 6rem;
    left: 3rem;
    font-size: 1.25rem;
    color: rgba(255,255,255,.9);
    background: #0e2b41;
    padding: 1.5rem;
    border-radius: .2rem;
    width: 40rem;
    box-sizing: border-box;
    transition: all .2s linear;
    opacity:0;
    pointer-events:none;
    z-index:1;
}

dl.toraDescription dt,
dl.toraDescription dd { display: inline-block; }

dl.toraDescription dt {
    font-weight: 300;
    margin-bottom: 0.5rem;
    width: 10rem;
}

dl.toraDescription dd { width: calc(100% - 10.6rem); }

.peerReview-wrapper .reviewDate {
    display: inline-block;
    font-size: 1rem;
    padding: .25rem .5rem;
    border-radius: .3rem;
    color: #fff;
    background: rgb(55,61,64);
    float: right;
}

.peerReview-wrapper ul {
    font-size: 1.4rem;
    padding: inherit;
    padding-top:0;
    margin: inherit;
}

.peerReview-wrapper ul.reviewReason {
    margin: initial;
    margin-bottom:1rem;
    list-style: inherit;
}

.peerReview-wrapper ul.reviewValuer li { line-height:1.2; }

.peerReview-wrapper ul.reviewValuer li.reviewValuerDescription:before{
    content: "\A";
    white-space: pre;
}

.peerReview-wrapper ul.reviewValuer li strong {
    display: inline-block;
    font-weight: 600;
    margin-right:.5rem;
    vertical-align: bottom;
    overflow: inherit;
}

.peerReview-wrapper ul.reviewValuer { margin-bottom:1rem; }

.peerReview-wrapper ul.reviewValuer li strong:before {
    display: inline-block;
    position:relative;
    top:.5rem;
    content: "\f119";
    font-family: "QV-Monarch";
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    line-height: 1;
    text-decoration: inherit;
    text-rendering: optimizeLegibility;
    text-transform: none;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-smoothing: antialiased;
    font-size: 2.4rem;
    color: #5290db;
    margin-right:.5rem;
    width: 2.4rem;
    height: 2.4rem;
}

.peerReview-wrapper ul.peerReview-form { margin: 3rem 0.7rem 0; }

.peerReview-wrapper ul.reviewOutcome li {
    font-weight:600;
    margin-bottom:.2rem;
}

.peerReview-wrapper ul.reviewOutcome li.pass:before,
.peerReview-wrapper ul.reviewOutcome li.fail:before {
    display: inline-block;
    position: relative;
    top: -0.2rem;
    content: "";
    font-size: .9rem;
    font-weight:400;
    color: #fff;
    text-align: center;
    text-transform: uppercase;
    line-height: 1.8;
    padding: 0;
    border-radius: .3rem;
    margin-right: 0.8rem;
    width: 3.2rem;
}

.peerReview-wrapper ul.reviewOutcome li.pass:before {
    content: "Pass";
    background-color: #4caf50;
}

.peerReview-wrapper ul.reviewOutcome li.fail:before {
    content: "Fail";
    background-color: #dd2c00;
}

.peerReview-wrapper ul.reviewOutcome li span {
    display: block;
    font-size: 1.3rem;
    font-weight: 400;
    line-height: 1.5;
    margin: 0.5rem 0 1.5rem 4.2rem;
}

.md-table .expandAllOwnersOccupiers {
    -webkit-transition:all .2s linear;
    transition:all .2s linear;
    -webkit-transform:rotate(-180deg);
    transform:rotate(-180deg);
    position:absolute;
    top:1.1rem;
    right:1.1rem;
}

.expandAllOwnersOccupiers.down {
    -webkit-transform:rotate(0deg);
    transform:rotate(0deg);
}

.md-table .hideNames {
    position:absolute;
    top:1.1rem;
    right:4.8rem;
}

.hideNames.right  {
    position:absolute;
    top:1.1rem;
    right:1.1rem;
}

.md-table a { text-decoration:underline; }

.md-tableRow {
    display:table-row;
    font-size:1.25rem;
}

.excess.hide,
.salesTable.md-tableRow.hide { display:none; }

.md-tableRow.md-headerRow,
.md-tableRow.md-headerRow.salesTable {
    font-size:1.1rem;
    text-transform:uppercase;
    background-color:rgb(237,241,245);
}

.md-tableRow li {
    display:table-cell;
    padding:1.6rem 0;
    border-bottom:.1rem solid rgb(229,229,229);
}

.md-tableRow.md-headerRow li {
    color:#555;
    background-color:rgb(237,241,245);
    padding:.6rem 0 .5rem;
    border-bottom:none;
}

.md-tableRow:last-child li { border-bottom:none; }

.md-tableRow li:first-child { padding-left:.75rem; }

.md-tableRow li span {
    display:inline-block;
    line-height:1.9;
    padding:0 .5rem;
    border-radius:.25rem;
}

.md-tableRow.md-headerRow li.smallCell	{ width:12% }
.md-tableRow.md-headerRow li.mediumCell { width:17% }
.md-tableRow.md-headerRow li.wideCell	{ width:35% }
.md-tableRow.md-headerRow li.jumboCell	{ width:48% }


.md-tableRow.salesTable {
    display:block;
    font-size:1.25rem;
}

.md-tableRow.salesTable:not(.md-headerRow) + .md-tableRow.salesTable {
    border-top:1rem solid rgb(237,241,245);
    margin-top:1.6rem;
}

.md-tableRow.salesTable li {
    display:inline-block;
    line-height:1.9;
    text-align:center;
    padding:1.6rem 0;
    border-bottom:none;
    margin-left:-.3rem;
}

.md-tableRow.md-headerRow.salesTable li { padding:.6rem 0; }

.md-tableRow.salesTable li { width:11.3%; }

.md-tableRow.salesTable li:nth-child(9) { width: 9%; }

.md-tableRow.md-headerRow.salesTable li:first-of-type,
.md-tableRow.salesTable li:first-of-type {
    text-align: left;
    padding-left: 1rem;
}

.md-tableRow.salesTable li.vendorCell,
.md-tableRow.salesTable li.remarksCell {
    display: block;
    text-align: left;
    padding: 0 0 0 .8rem;
    border-bottom: none;
    margin: -.75rem 0 0;
    width: 100%;
    font-style: italic;
}

.md-tableRow.salesTable li.remarksCell { padding-top:.8rem; }

.md-tableRow.salesTable li.vendorCell:empty,
.md-tableRow.salesTable li.remarksCell:empty { display: none; }

.md-tableRow.salesTable li.vendorCell::before {
    content:"Vendor/Purchaser: ";
    font-weight:600;
}

.md-tableRow.salesTable li.remarksCell::before {
    content:"Remarks: ";
    font-weight:600;
}

.md-tableRow.salesTable li.sales-analysis-link a {
    display: inline-block;
    background: #3f91e1;
    padding: 0rem 1rem .2rem;
    border-radius: 1rem;
    color: #fff;
    font-size: 0.9rem;
    line-height: 1.8;
}

.supressed li:nth-child(2), .supressed li:nth-child(3) { color:#fff; }

.supressed li:nth-child(2)::before {
    display:block;
    position:absolute;
    content:"Information suppressed at the request of the individual under section 28C(1) of the Local Government (Rating) Amendment Act 2004";
    font-size:1.2rem;
    font-style:italic;
    color:#455a64;
    text-align:center;
    background-color:rgba(240,248,254,1);
    padding:.7rem 0;
    border-radius:.4rem;
    margin:-.8rem 0 0 -1rem;
    width:calc(100% - 17.9rem);
}

.md-titles {
    position:relative;
    font-size:1.2rem;
    padding:8rem 1.6rem 3.2rem;
    margin-bottom:3.6rem;
    width:100%;
    overflow:auto;
}

.md-titles h3 {
    position:absolute;
    top:1.6rem;
    font-size:1.5rem;
    font-weight:600;
    padding:0 0 1.2rem;
}

.qvTitles,
.linzTitles {
    width:calc(50% - .8rem);
}

.qvTitles { float:left; }

.linzTitles { float:right; }

.md-titles p {
    position:absolute;
    top:4.8rem;
    font-size:1.4rem;
    font-weight:400;
}

.md-titles a {
    color:rgba(255,255,255,.7);
    text-decoration:underline;
}

.md-titles a:hover {
    color:rgba(255,255,255,1);
}

.linzTitles-wrapper {
    max-height:77.8rem;
    overflow-y:auto;
}

.md-titleSummary {
    font-size:1.15rem;
    color:rgba(255,255,255,.9);
    background:#162b3f;
    padding:.6rem 1.2rem;
    margin-bottom:1rem;
}

.md-titleSummary li {
    display:inline-block;
    width:calc(50% - .3rem);
}

.md-titleSummary li:last-of-type {
    text-align:right;
}

.md-titleCard {
    font-size:1.15rem;
    color:rgba(255,255,255,.9);
    background:#324148;
    padding:1rem .75rem .6rem 1rem;
    border-radius:.5rem;
    margin-bottom:.5rem;
    box-sizing:border-box;
    overflow:auto;
}

.linzTitles .md-titleCard:nth-of-type(even) { background:#455a64; }

.linzTitles .md-titleCard {
    padding:1rem .75rem .6rem 1rem;
}

.md-titleCard dt {
    display: inline-block;
    position:relative;
    font-weight:300;
    padding: 0 0 .1rem 0;
    width: 12rem;
}

.md-titleCard dd {
    display:inline-block;
    font-size:1.15rem;
    color:rgba(255,255,255,1);
    padding-bottom:.5rem;
    width: calc(100% - 13rem);
    vertical-align: top;
}

.linzTitles .md-titleCard dd { padding-bottom:.5rem; }

.md-titleCard span {
    padding:0 .25rem 0 0;
}

.md-titleSummary span.mismatch {
    font-weight: 600;
    color: #1d2123;
    background: #ffd000;
    padding: .2rem .6rem;
    border-radius: .5rem;
    margin-left:.5rem
}

.md-titleCard span.mismatch {
    font-weight: 600;
    color: #1d2123;
    background: #ffd000;
    padding: 0 .5rem;
    border-radius: 5rem;
}

.md-improvements {
    background-color: #fff;
    border-top: 1px solid #d7d7d7;
    border-left: 1px solid #d7d7d7;
    border-radius: .2rem;
    margin-bottom: 4rem;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
    overflow: auto;
}

.md-improvements h3 {
    display: inline-block;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 2.2;
    color: #555;
    padding: 0 0 0 1.2rem;
    margin: 1.2rem 0;
}

.md-improvements span {
    color:#fff;
    font-size:1.4rem;
    font-weight:300;
}

ul.md-improvementTags {
    display:inline-block;
    font-size: 1.35rem;
    color: #555;
}

ul.md-improvementTags li::before {
    content:": ";
    padding-right:.5rem;
}

.md-statusboard {
    background-color:rgb(250,250,250);
    padding-bottom:2rem;
}

.md-statusboard h3 {
    font-size:1.5rem;
    font-weight:600;
    color:#fff;
    background:#283c64;
    padding:1.3rem;
    border-radius:.2rem;
    margin-bottom:1.6rem;
    min-height:4.8rem;
}

ul.md-statusboardLeft,
ul.md-statusboardRight {
    display:inline-block;
    font-size:1.2rem;
    line-height:2.5;
    font-weight:400;
    padding:0 .8rem 0 1.4rem;
    vertical-align:top;
    width:16.9rem;
    box-sizing:border-box;
}

ul.md-statusboardRight { padding:0 1.4rem 0 .8rem }

ul.md-statusboard li { width:100%; }

li.improveYes::after,
li.improveNo::after,
li.improveNeutral::after {
    display:inline-block;
    font-size:1.35rem;
    line-height:1.8;
    color:#fff;
    text-align:center;
    border-radius:.2rem;
    margin-top:.3rem;
    width:4rem;
    float:right;
}

li.improveYes::after {
    content:"YES";
    background:#53d769;
}

li.improveNo::after {
    content:"No";
    background:#fc3d39;
}

li.improveNeutral::after {
    content:"0";
    background:#455a64;
}

.md-revisionBox {
    position:relative;
    color:#283c64;
    background-color:rgba(240,248,254,1);
    padding: .8rem 1rem;
    border: .1rem solid #dbe8f2;
    border-radius: 0;
    margin-bottom:4rem;
    box-sizing:border-box;
}

.md-revisionBox h3 {
    font-size:1.6rem;
    font-weight:600;
    margin:0 0 .5rem .5rem;
}

.md-revisionBox h3 em {
    font-size:1.2rem;
    font-weight:400;
    color:#336699;
    line-height:1.6;
    margin-left:.6rem;
}

.revisionValues { position:relative; }

.revisionValues.maoriLand {
    background:#dbe8f2;
    padding-bottom: .3rem;
    padding-top: 0;
    margin:1.6rem 0 1rem;
}

.revisionValues li {
    display:inline-block;
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0 1rem;
    width: calc(25% - 2.3rem);
    vertical-align: top;
}

.revisionValues li.evaluer {
    position:absolute;
    top:1.9rem;
    left:16rem;
    font-size:1.8rem;
    font-weight:300;
    color:#fff;
    background-color:#455a64;
    padding:.3rem 1rem;
    border-radius:.3rem;
    margin:0;
    width:inherit;
    min-width:20.5rem;
}

.revisionValues li label {
    display:block;
    font-size:1.1rem;
    color:#336699;
    line-height:2.4;
    border-bottom:.1rem solid #dbe8f2;
    margin-bottom: .35rem;
}

.revisionValues.maoriLand li label {
    border-bottom:.1rem solid #b8ccdc;
}

.revisionValues li.evaluer label {
    display:inline-block;
    position:relative;
    top:-.2rem;
    font-size:1.2rem;
    font-weight:300;
    color:#fc932f;;
    line-height:0;
    border:none;
    margin-right:.8rem;
}

.revisionValues .valueUp,
.revisionValues .valueDown {
    position: absolute;
    top: 2.9rem;
    text-align:right;
    min-width:5rem;
}

.valueUp,
.valueDown {
    position:relative;
    display:inline-block;
    font-size:1.15rem;
    color:#fff;
    line-height:2;
    text-align:center;
    padding:0 .5rem 0 1.25rem;
    border-radius:.2rem;
    margin:.5rem 0 0 .9rem;
    vertical-align:top;
}

.valueUp { background:#4caf50;	}

.valueDown { background:#fc3d39; }

.valueUp::before,
.valueDown::before {
    position:absolute;
    left:.4rem;
    top:calc(50% - .3rem);
    content:"";
    border-left:.3rem solid transparent;
    border-right:.3rem solid transparent;
    width:0;
    height:0;
}

.valueUp::before {
    top:calc(50% - .2rem);
    border-bottom:.4rem solid #fff;
}

.valueDown::before {
    top:calc(50% - .2rem);
    border-top:.4rem solid #fff;
}

.evaluer .valueUp,
.evaluer .valueDown {
    margin-top:.3rem;
}

.qvToolbar-wrapper {
    margin:0 auto 4.8rem;
    max-width:144rem;
}

.qvToolbar {
    background-color:#37474f;
    border-radius:.3rem;
    margin:4.8rem 0 0;
}

.md-summaryHeader .qvToolbar {
    display: block;
    background-color: #455963;
    margin: 0 1.6rem;
    width: calc(100% - 3.2rem);
    float: left;
    clear:both;
}

.qvToolbar-leftMenu {
    position: relative;
    top: -0.1rem;
    background-color: #2e3b42;
    box-shadow: 0 0.2rem 0.1rem 0 rgba(0, 0, 0, .8), 0 0 0.3rem -0.2rem rgba(0, 0, 0, .7), 0 0 0.2rem 0.1rem rgba(255, 255, 255, .25)
}

.qvToolbar-leftMenu select {
    font-family: inherit;
    font-size: 1.4rem;
    color:#fff;
    padding: 1rem 5.4rem 1rem 1.8rem;
    border-right: none;
    height: 5.2rem;
}

.qvToolbar-leftMenu i {
    position:absolute;
    top:calc(50% - 1.2rem);
    right:1.4rem;
}

.qvToolbar-links {
    margin:0 1.8rem;
    box-sizing:border-box;
}

/* Interim fix for using nested routes with legacy tabs */
.qvToolbar-link {
    padding: 0 !important;

    > label {
        width: max-content !important;
    }
}

.qvToolbar-mobileButton label {
    float: right;
    color: white;
    margin-right: 0.5rem;
    margin-top: 0.5rem;
}

.qvToolbar-links a,
.qvToolbar-links label {
    display:inline-block;
    font-size:1.5rem;
    color:#fff;
    text-align:center;
    padding:1.7rem 1.7rem 1.1rem;
    width:100%;
    box-sizing:border-box;
}

.qvToolbar-links label.menu { padding-right:1rem; }

.qvToolbar-links label i {
    margin-top: -.5rem;
    margin-left:.2rem;
    vertical-align:bottom;
    width:2.4rem;
}

.qvToolbar-links li, .qvToolbar-link {
    display:inline-block;
    position:relative;
}

.qvToolbar-links > li, .qvToolbar-link {
    border-bottom:.5rem solid transparent;
}

.qvToolbar-links li.active a {
    padding-bottom: 1rem;
    border-bottom: none;
}

.qvToolbar-links li.active, .qvToolbar-link.active {
    border-bottom:.5rem solid #3f91e1;
}

.qvToolbar-combined {
    position:relative;
    top: 2rem;
}

.qvToolbar-qivs {
    padding: .7rem;
    margin: .8rem .7rem;
}

.qvToolbar-icons {
    padding: 0.7rem 1rem 0.4rem;
    border-left: .1rem solid rgba(255,255,255,.4);
    margin: .8rem 0;
}

.qvToolbar-icons li,
.qvToolbar-qivs li {
    display: inline-block;
    vertical-align: middle;
}

.qvToolbar-icons a,
.qvToolbar-qivs a {
    color:inherit;
    line-height:0;
    padding:.4rem .7rem;
}
.qvToolbar-links ul {
    position:absolute;
    top: 4.2rem;
    left: 1rem;
    background:#fff;
    padding:0;
    margin:0;
    min-width:16rem;
    z-index:1;
}
.qvToolbar-links ul li {
    display:block;
    font-size:1.15rem;
    padding:1.25rem 1rem .25rem 2.4rem;
    border-top:none;
    cursor:pointer;
}

.qvToolbar-links ul li:last-of-type:not(.divider) { margin-bottom:1.25rem; }

.qvToolbar-links ul li:hover {
    color:#369;
}

.qvToolbar-links ul li.divider {
    font-weight:600;
    font-size:1.25rem;
    padding:1rem 1rem 1rem 2.4rem;
}

.qvToolbar-links ul li.divider:hover {
    color:#fff;
    background-color:#4f91db;
    border-top-color:#4f91db;
}

.valJobs-menu li.divider { border-bottom:1px solid #d2d2d2; }

input[type="checkbox"].valJobs-trigger { position:absolute; opacity:0; }

input[type="checkbox"].valJobs-trigger ~ .valJobs-menu { display:none; }

input[type="checkbox"].valJobs-trigger:checked ~ .valJobs-menu { display:block; }


/* @media only screen and (max-width:1200px) {

    .md-left { width:calc(100% - 32.5rem); }

    .md-right {
        padding-left:0;
        width:32.2rem;
    }

    .md-propertyOverview {
        padding:1.2rem 1.6rem;
        width:48rem;
    }

    .md-photoGallery { width:calc(100% - 48rem); }

    .md-address h1 { font-size:2.2rem; }

    .md-address h1 span {
        font-size:1.2rem;
        margin-top:.5rem;
    }

    .md-address em { margin-top:.4rem; }

    .md-values li { font-size:2rem; }

    .md-values span { font-size:1.2rem; }

    .md-values strong { font-size:1.25rem; }

    .md-landMas { padding:4rem 1.8rem 1.6rem; }

    .md-landMas strong { font-size:1.25rem; }

    ul.md-landMas-tabs { padding:0; }

    .md-landMas li { padding-left:4.5rem; }

    .md-landMas li:before { top:.3rem; }

    .md-landMas-tabs li span { font-size:1.2rem; }

    .md-tableRow { font-size:1.1rem; }

    .md-tableRow.md-headerRow { font-size:1rem; }

    .md-tableRow.md-headerRow li.smallCell	{ width:13% }
    .md-tableRow.md-headerRow li.mediumCell { width:18% }
    .md-tableRow.md-headerRow li.wideCell	{ width:30% }

    .supressed li:nth-child(2)::before { width:calc(100% - 25rem);	}

    .md-titleCard li::before { top:.1rem; }

    .md-titleCard li span {
        padding:0;
        margin-top:.3rem;
        width:100%;
        float:inherit;
    }

    ul.md-statusboardLeft, ul.md-statusboardRight {
        font-size:1.1rem;
        padding:0 .4rem 0 1.2rem;
        width:14.1rem;
    }

    ul.md-statusboardRight { padding:0 1rem 0 .4rem; }

    li.improveYes::after, li.improveNo::after, li.improveNeutral::after {
        font-size:1rem;
        width:2.6rem;
    }

    .revisionValues li { font-size:2rem; }

    .revisionValues li.evaluer {
        right:2rem;
        left:inherit;
    }

    .valueUp, .valueDown {
        font-size:1rem;
        padding:0 .5rem 0 1rem;
        margin:.5rem 0 0 .75rem;
    }

    .valueUp::before, .valueDown::before {
        top:calc(50% - .2rem);
        border-left:.2rem solid transparent;
        border-right:.2rem solid transparent;
    }

    .valueUp::before {
        top:calc(50% - .2rem);
        border-bottom:.4rem solid #fff;
    }

    .valueDown::before {
        top:calc(50% - .1rem);
        border-top:.4rem solid #fff;
    }

}*/

@media all and (-ms-high-contrast:none), (-ms-high-contrast:active) {
    .md-landMas-tabs hr.MasTab-1 { margin-left:-75%; }
    .md-landMas-tabs hr.MasTab-2 { margin-left:-25%; }
    .md-landMas-tabs hr.MasTab-3 { margin-left: 25%; }
    .md-landMas-tabs hr.MasTab-4 { margin-left: 75%; }

}

.md-photoGallery .slick-slide img { width:100%; }

.md-photoGallery .slick-next {
    right:0;
    z-index:1;
}

.md-photoGallery .slick-prev {
    left:0;
    z-index:1;
}

.md-photoGallery .slick-slide {position: relative; margin:0;}

.md-photoGallery .slick-prev,
.md-photoGallery .slick-next {
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    font-size: 0;
    color: transparent;
    line-height: 0;
    padding: 0;
    border: none;
    width: 2.5rem;
    height: 100%;
    transform: inherit;
    cursor: pointer;
    outline: none;
}

.md-photoGallery .slick-prev::before,
.md-photoGallery .slick-next::before {
    position: absolute;
    left: 0;
    right: 0;
    bottom: -3.5rem;
    font-family: 'Material Icons';
    font-size: 2rem;
    color: #999;
    line-height: 1.3;
    width: 2.5rem;
    height: 2.5rem;
    opacity: 1 !important;
    box-shadow: inset 0 0 0 0.1rem #626970;
    -webkit-font-smoothing: antialiased;
    transition: 0.2s linear all;
}

.md-photoGallery .slick-next::before { content:"\E409"; }
.md-photoGallery .slick-prev::before { content:"\E408"; }

.md-photoGallery .slick-prev:hover::before,
.md-photoGallery .slick-next:hover::before {
    color:#2c94e2;
    opacity:1;
    background-color:#fff;
}

.md-photoGallery .slick-dots {
    display: table;
    bottom:-3.5rem;
    margin:0 4rem;
    width:calc(100% - 8rem);
    height:2.5rem
}

.md-photoGallery .slick-dots li {
    position: relative;
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    margin: 0;
    padding: 0;
    cursor: pointer;
    min-width: 5%;
    width: auto;
    height:100%;
}

.md-photoGallery .slick-dots li button {
    display: block;
    padding:0;
    width: 100%;
    height: .2rem;
}



.md-photoGallery .slick-dots li button::before {
    display: block;
    position: inherit;
    content: '';
    background-color: #626970;
    border-radius: 0;
    width: 100%;
    height: .2rem;
    opacity: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing:grayscale;
    transition: 0.1s linear all;
}

.md-photoGallery .slick-dots li.slick-active button::before {
    opacity: 1;
    background-color: #2c94e2;
}

.md-photoGallery .slick-dots li:hover button::before { background-color:rgba(255,255,255,.8); }


.md-photoGallery .slick-dotted.slick-slider { margin-bottom:0; }

ul.galleryCaption {
    display: block;
    position: absolute;
    bottom: 0.4rem;
    left: 0;
    font-size: 1.35rem;
    font-weight: 300;
    color: rgba(255,255,255,.9);
    text-shadow: 0.1rem 0.1rem rgba(0,0,0,.5);
    background:linear-gradient(to right, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 40%);
    padding: 1.2rem 1.5rem;
    width: 100%;
    box-sizing: border-box;
}


/*  ============================================================================================ */
/*  SALES ANALYSIS POP-UP  ===================================================================== */
/*  ============================================================================================ */

.analysis { position:relative; }

.salesAnalysis-wrapper {
    background: #fff;
}

.salesAnalysis-form {
    right: 0;
    left: 0;
    background-color: #fff;
    padding:3rem 2rem;
    top: unset;
    bottom: unset;
    position: unset;
}

.salesAnalysis-form .salesRow.openProp::before,
.salesAnalysis-form .salesRow.openProp::after {
    display:none;
}

.sa-analysisStatus {
    position: absolute;
    right: 2rem;
    padding-right:3.6rem;
    z-index: 1;
    top: unset;
    margin-top: 1rem;
}

.sa-analysisStatus::before,
.sa-analysisStatus::before {
    position: absolute;
    top:-.35rem;
    right: 0;
    content:"\E000";
    font-family:"Material Icons";
    font-size:2.8rem;
    color:#d2362b;
}


.sa-analysisStatus.verified::before,
.sa-analysisStatus.unVerified::before {
    position: absolute;
    top:-.35rem;
    right: 0;
    content:"\E8E8";
    font-family:"Material Icons";
    font-size:2.8rem;
    color:rgba(74,144,226,1);
}

.sa-analysisStatus.unVerified::before { color:#283c64; }

.sa-analysisStatus.unVerified::after {
    position: absolute;
    top: .9rem;
    right: 0.45rem;
    content: "";
    background-image: url(../../images/monarchLogo-mini.png);
    background-position: 50% 50%;
    background-color: #283c64;
    background-repeat: no-repeat;
    background-size: 66%;
    border-radius: 50%;
    width: 1.82rem;
    height: 1.65rem;
}

.sa-analysisStatus li {
    font-size: 1.1rem;
    font-weight:600;
    color: #d2362b;
    text-align:right;
}

.sa-analysisStatus.verified li,
.sa-analysisStatus.unVerified li {
    color: #214d90;
}



.sa-analysisStatus li.sa-analysedBy::before {
    content:"Analysed By: ";
    font-weight:400;
}

.sa-analysisStatus li.sa-analysedDate::before {
    content:"On: ";
    font-weight:400;
}

.sa-analysisStatus li.sa-sale-to-process {
    font-weight:600;
    color: var(--qv-color-red)
}

.salesAnalysis-form .openProp {
    padding-top:0;
    margin-top:0;
}

.salesAnalysis-form .openProp .fullAddress {
    padding: 1.2rem 0 1rem;
    margin: -.8rem 0 1.5rem 0;
}

.salesAnalysis-form .openProp.salesRow .sales-trafficLights {
    top: 4.8rem;
    left: calc(61.4% - 1rem);
    width: 39.2%;
}

.salesAnalysis-form .resultsRow.openProp.salesRow,
.salesAnalysis-form .resultsRow.openProp.salesRow.pendingSale,
.salesAnalysis-form .resultsRow.openProp.salesRow.unconfirmedSale,
.salesAnalysis-form .resultsRow.openProp.salesRow:hover,
.salesAnalysis-form .resultsRow.openProp.salesRow.pendingSale:hover,
.salesAnalysis-form .resultsRow.openProp.salesRow.unconfirmedSale:hover {
    border-bottom: none;
}

.salesAnalysis-form .resultsRow.openProp.salesRow:hover .fullAddress,
.salesAnalysis-form .resultsRow.openProp.salesRow.pendingSale:hover .fullAddress,
.salesAnalysis-form .resultsRow.openProp.salesRow.unconfirmedSale:hover .fullAddress {
    box-shadow: none;
}

.salesAnalysis-form .resultsRow:hover .fullAddress::after { display: none; }

.salesAnalysis-form .openProp.salesRow .sales-trafficLights .saleClassification,
.salesAnalysis-form .openProp.salesRow .sales-trafficLights .saleDate,
.salesAnalysis-form .openProp.salesRow .sales-trafficLights .saleStatus,
.salesAnalysis-form .openProp.salesRow .sales-trafficLights .saleAnalysis {
    width: calc(33% - .6rem);
}

.salesAnalysis-form .openProp .capval,
.salesAnalysis-form .openProp .landarea,
.salesAnalysis-form .openProp .landval,
.salesAnalysis-form .openProp .tfa,
.salesAnalysis-form .openProp .tla,
.salesAnalysis-form .openProp .valimp,
.salesAnalysis-form .openProp.salesRow .nsp,
.salesAnalysis-form .openProp.salesRow .gsp,
.salesAnalysis-form .openProp.salesRow .chattels,
.salesAnalysis-form .openProp.salesRow .salegst,
.salesAnalysis-form .openProp.salesRow .saleother {
    font-size:1.45rem;
    height:9.5rem;
}

.salesAnalysis-form .openProp.salesRow .searchDetails-wrapper {
    width: 50%;
    height:9.5rem;
}

.salesAnalysis-form .openProp.salesRow .colCell.capval,
.salesAnalysis-form .openProp.salesRow .colCell.landval {
    width:22.5%;
}

.salesAnalysis-form .openProp.salesRow .colCell.valimp { width: 28%; }

.salesAnalysis-form .openProp.salesRow .colCell.landarea { width: 27%; }

.salesAnalysis-form .openProp .capval div,
.salesAnalysis-form .openProp .landval div,
.salesAnalysis-form .openProp .valimp div {
    font-size: 1.3rem;
    line-height: 1.6;
}

.salesAnalysis-form .openProp.salesRow .tfaTla-wrapper {
    bottom:1.2rem;
    left:73%;
    padding-left:1rem;
}

.salesAnalysis-form .openProp.salesRow .colCell.tfa,
.salesAnalysis-form .openProp.salesRow .colCell.tla {
    bottom: initial;
    left: initial;
    font-size: 1.3rem;
    line-height:1.4;
    width: auto;
}

.openProp.salesRow .tfa::after,
.openProp.salesRow .tla::after {
    font-size: 1rem;
    padding-bottom: .2rem;
}

.salesAnalysis-form .openProp.salesRow .nsptocv { display: none; }

.salesAnalysis-form .openProp.salesRow .gsp {
    left: 100%;
    background: #002943;
    border-left: none;
    width: 100%;
    height:9.5rem;
}

.salesAnalysis-form .openProp.salesRow .nsp {
    left: 126%;
    width: 23%;
    height: 9.5rem;
}

.salesAnalysis-form .openProp.salesRow .nsp div {
    font-size: 1.3rem;
    line-height: 1.6;
}

.salesAnalysis-form .openProp.salesRow .chattels,
.salesAnalysis-form .openProp.salesRow .salegst,
.salesAnalysis-form .openProp.salesRow .saleother {
    left: 151%;
    width:18%;
    height:10rem;
}

.salesAnalysis-form .openProp.salesRow .salegst { left: 167%; }

.salesAnalysis-form .openProp.salesRow .saleother {	left: 183%;	}

.salesAnalysis-form .openProp.salesRow .nsp::after,
.salesAnalysis-form .openProp.salesRow .chattels::after,
.salesAnalysis-form .openProp.salesRow .salegst::after,
.salesAnalysis-form .openProp.salesRow .saleother::after {
    left: 1rem;
}

.sa-summary.md-summary,
.sa-salesComment {
    margin:0 0 3rem;
}

.sa-summary.md-summary div[title="Click to edit..."] {
    border: 1px solid #ccc;
    padding: 1rem;
}

.sa-summary.md-summary h3 {
    font-size:1.5rem;
    color:#003f64;
}

.sa-summary textarea,
.sa-salesComment textarea {
    font-size: 1.3rem;
    font-weight: 400;
    font-family: 'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif;
    line-height: 1.6;
    padding: 1rem;
    box-shadow: inset 0 0 0 1px #ccc;
    height: 8.5rem !important;
    width: 100%;
}

.sa-summary textarea:focus,
.sa-salesComment textarea:focus{
    font-size: 1.3rem;
    height: 8.5rem !important;
}

.sa-salesRemarks {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #204d90;
    line-height: 1.9;
    text-align: center;
    background-color: rgba(2,136,209,.06);
    padding: 0 1rem;
    border: .1rem solid rgba(2,136,209,.15);
    border-radius: .3rem;
    margin: -.2rem 0 0 0;
    vertical-align: top;
    min-width: 16rem;
    width: initial;
    box-sizing: border-box;
    float: right;
}

.sa-salesRemarks::before {
    content:"Sale Remarks: ";
    font-weight: 400;
}

.sa-salesRemarks:empty { display:none; }

.sa-salesComment label,
.sa-salesComment div {
    display:block;
    font-size: 1.5rem;
    font-weight: 600;
    color:#003f64;
    margin-bottom: .75rem;
}

.sa-salesComment div { font-weight: 400; }

.sa-salesComment input[type="text"] {
    font-size: 1.2rem;
    padding: .5rem;
    border: none;
    width: 100%;
    box-shadow: 0 0 0 1px #ccc;
}

.salesAnalysis-table {
    display:table;
    padding:1rem 0;
    width: 100%;
}

.salesAnalysis-table h3 {
    font-size:1.5rem;
    font-weight:600;
    color:#003f64;
    margin-bottom:.75rem;
}

.salesAnalysis-row { display: table-row; }

.salesAnalysis-th {
    display: table-cell;
    font-size:1.1rem;
    color:rgba(255,255,255,.9);
    line-height:2;
    background-color:#283c64;
    padding: 0 1rem;
}



.salesAnalysis-td {
    display: table-cell;
    line-height:2;
    background-color:#f5fcff;
    padding:1.4rem 1.5rem 1.25rem 1rem;
    border-bottom:1px solid #daebf2;
    width: 16.66%;
    vertical-align:top;
}

.salesAnalysis-th:first-of-type,
.salesAnalysis-td:first-of-type	{
    padding-left:2rem;
}



.salesAnalysis-td:first-of-type	{
    border-left:1px solid #daebf2;
}

.sa-details .salesAnalysis-td.sa-spacer5 { width:83.3333%; }
.salesAnalysis-td.sa-description-long 	 { width:54%;  	   }
.salesAnalysis-td.sa-description-medium  { width:40%;  	   }
.salesAnalysis-td.sa-description 		 { width:30%;  	   }
.salesAnalysis-td.sa-capRate             { width:15%;  	   }
.salesAnalysis-td.sa-rent 	             { width:15%;  	   }
.salesAnalysis-td.sa-area 				 { width:12%;  	   }
.salesAnalysis-td.sa-rate 				 { width:12%;  	   }
.salesAnalysis-td.sa-rentWeekly 		 { width:12%;  	   }
.salesAnalysis-td.sa-rentAnnual 		 { width:12%;  	   }
.salesAnalysis-td.sa-salePrice 			 { padding-right:1.5rem; border-left:1px solid #daebf2; width:18%; }
.salesAnalysis-td.sa-value 				 { padding-right:1.5rem; width:18%; }
.salesAnalysis-td.sa-runnintgTotal		 { font-size:1.5rem; font-weight:800; font-style:italic; text-align:right; background-color:#d3effb; padding:1.25rem 1.25rem 1.25rem .5rem; border-left:1px solid #afdcef; border-bottom:1px solid #afdcef; width:19%; }
.salesAnalysis-td.sa-addRemove 			 { text-align:left; background-color:#afdcef; padding-left: 0; border-bottom:1px solid #97c9de; width:11%; }

.salesAnalysis-td div:first-of-type { margin-bottom:1.5rem; }
.salesAnalysis-td.sa-runnintgTotal div { padding-left:.5rem; overflow:auto; }
.salesAnalysis-td div label {
    display:inline-block;
    font-size:1.2rem;
    font-weight:600;
    font-style:normal;
    line-height: 2.4;
    text-align:left;
    width:33.3333%;
    float:left;
}

.salesAnalysis-td.sa-runnintgTotal div label { width:75%; }

.salesAnalysis-td div input[type="text"] { width:66.6666%; }

.salesAnalysis-table.calculationPanel { padding:0 0 3rem;}

.salesAnalysis-table.calculationPanel h3 { padding-left:3rem; }
.salesAnalysis-table.calculationPanel .advSearch-row h3 { padding-left:0rem; }

.salesAnalysis-table.calculationPanel .advSearch-row:first-of-type { border-top: 4px solid #2c3c61; }
.salesAnalysis-table.calculationPanel .advSearch-row:last-of-type { border-bottom: 4px solid #2c3c61; }

.salesAnalysis-table.calculationPanel .investmentdetails .advSearch-row:last-of-type { border-bottom: none; }
.salesAnalysis-table.calculationPanel .investmentdetails { border-bottom: 4px solid #2c3c61; }



/** -----------------Update SRA --------------------------------------**/

.sra-addRemove i	{
    position:relative;
    display:inline-block;
    font-size:2.5rem;
    margin:0 .15rem;
    z-index:1;
    float:right;
}

.sra-addRemove i::hover { opacity:.8; }

.sra-addRemove i::after	{
    position: absolute;
    top: 0.9rem;
    left: 0.3rem;
    content: "";
    background-color: #fff;
    border-radius: 50%;
    width: 1.8rem;
    height: 1.8rem;
    z-index: -1;
}

.updateSra-table .sra-addRemove i::after { top: .3rem; }

.updateSra-td {
    display: table-cell;
    line-height:2;
    background-color:#f5fcff;
    padding:1.4rem 1.5rem 1.25rem 1rem;
    border-bottom:1px solid #daebf2;
    width: 16.66%;
    vertical-align:top;
}


.QVHV-formSection .updateSra-table {
    margin: 0 2.4rem;
    width:calc(100% - 4.8rem);
}

.updateSra-table {
    display:table;
    padding:1rem 0;
    width: 100%;
}

.updateSra-row { display: table-row; }

.updateSra-th {
    display: table-cell;
    font-size:1.1rem;
    color:rgba(255,255,255,.9);
    line-height:2;
    background-color:#283c64;
    padding: 0 1rem;
    white-space: nowrap;
}

/* .updateSra-td:first-of-type	{
    border-left:1px solid #daebf2;
} */
.updateSra-th:first-of-type,
.updateSra-td:first-of-type	{
    padding-left:2rem;
}

.updateSra-td span {
    font-size: 14px;
    display: inline-block;
    font-weight: 600;
}

.updateSra-td.sra-description-long 	     { width:54%;  	   }
.updateSra-td.sra-description-medium     { width:40%;  	   }
.updateSra-td.sra-description 		       { width:30%;  	   }
.updateSra-td.tav-description 		       { width:30%;  	   }
.updateSra-td.rfc-description 		       { width:19%;  	   }
.updateSra-td.sra-area 				     { width:12%;  	   }
.updateSra-td.tav-area 				     { width:12%;  	   }
.updateSra-td.rfc-output-code           {width: 12%}
.updateSra-td.rfc-source                 {width: 12%}
.updateSra-td.rfc-reason-for-change      {width: 24.5%}
.updateSra-td.sra-capital-value				 { width:12%;  	   }
.updateSra-td.tav-capital-value				 { width:12%;  	   }
.updateSra-td.sra-land-value 				 { padding-right:1.5rem; width:12%; }
.updateSra-td.tav-land-value 				 { padding-right:1.5rem; width:12%; }
.updateSra-td.sra-improvements-value		 { width: 12% }
.updateSra-td.tav-improvements-value		 { width: 24.5% }
.updateSra-td.sra-addRemove 			 { text-align:left; background-color:#afdcef; padding-left: 0; border-bottom:1px solid #97c9de; width:11%; }

.updateSra-td div:first-of-type { margin-bottom:1.5rem; }
.updateSra-td.sra-improvements-value div { padding-left:.5rem; overflow:auto; }
.updateSra-td div label {
    display:inline-block;
    font-size:1.2rem;
    font-weight:600;
    font-style:normal;
    line-height: 2.4;
    text-align:left;
    width:33.3333%;
    float:left;
}

.updateSra-td.sra-improvements-value div label { width:75%; }

.updateSra-td div input[type="text"] { width:66.6666%; }

.updateSra-table.calculationPanel { padding:0 0 3rem;}

.updateSra-table.calculationPanel h3 { padding-left:3rem; }
.updateSra-table.calculationPanel .advSearch-row h3 { padding-left:0rem; }

.updateSra-table.calculationPanel .advSearch-row:first-of-type { border-top: 4px solid #2c3c61; }
.updateSra-table.calculationPanel .advSearch-row:last-of-type { border-bottom: 4px solid #2c3c61; }

.updateSra-table.calculationPanel .investmentdetails .advSearch-row:last-of-type { border-bottom: none; }
.updateSra-table.calculationPanel .investmentdetails { border-bottom: 4px solid #2c3c61; }


i.sraRow-add		{ color:rgba(74,144,226,1); }
i.sraRow-remove	{ color:#ff6320; }

.updateSra-td input[type="text"] {
    font-size: 1.2rem;
    padding: 0.5rem;
    border: none;
    width: 100%;
    box-shadow: 0 0 0 1px #ccc;
}

.updateSra-td.tav-improvements-value input[type="text"] {
    font-size: 1.2rem;
    padding: 0.5rem;
    border: none;
    width: 81%;
    box-shadow: 0 0 0 1px #ccc;
}

.updateSra-td.rfc-reason-for-change input[type="text"] {
    font-size: 1.2rem;
    padding: 0.5rem;
    border: none;
    width: 215%;
    box-shadow: 0 0 0 1px #ccc;
}
/** ---------- END OF UPDATE SRA SECTION ----------------**/

input[type="checkbox"].calculationPanel-trigger {
    position: relative;
    top: 2rem;
    left: 0;
    -webkit-appearance: none;
    border-radius: .2rem;
    margin: 1rem 0 0 .5rem;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align: top;
}

input[type="checkbox"].calculationPanel-trigger:checked {
    content: "\E876";
    font-family: "Material Icons";
    font-size: 1.4rem;
    color: #fff;
    background: rgb(74,144,226);
    box-shadow: 0 0 0 0.3rem rgb(74,144,226);
    width: 1.4rem;
    height: 1.4rem;
}
input[type="checkbox"].calculationPanel-trigger:checked::before {
    content: "\E876";
    font-family: "Material Icons";
    color: #fff;
}


.sa-revisionAnalysis {
    position:relative;
    margin:2rem 0;
}

.sa-revisionAnalysis .salesAnalysis-row:first-of-type .salesAnalysis-td { border-top:.1rem solid #daebf2; }

.sa-revisionAnalysis .salesAnalysis-row:nth-child(odd) .salesAnalysis-td { background: rgba(237,252,255,.1); }

.sa-revisionAnalysis .salesAnalysis-td {
    border-right:.1rem solid #daebf2;
    padding:.5rem 3rem;
    width:25%;
    vertical-align:middle;
}

.sa-revisionAnalysis .salesAnalysis-td label {
    font-size:1.2rem;
    font-weight:400;
    font-style:italic;
    line-height: 2.3;
    vertical-align:middle;
}

.sa-revisionAnalysis .salesAnalysis-td span {
    display:inline-block;
    font-size: 1.4rem;
    font-weight:600;
    color:#003f64;
    line-height: 2;
    text-align: right;
    border: none;
    float: right;
    vertical-align:middle;
}

.salesAnalysis-td.sa-runnintgTotal span	{ font-weight:800; }

.sa-revisionAnalysis .salesAnalysis-td:first-of-type {
    padding-left: 2.5rem;
    border-left:.1rem solid #daebf2;
}

.salesAnalysis-td input[type="text"] {
    font-size:1.2rem;
    padding: .5rem;
    border:none;
    width: 100%;
    box-shadow: 0 0 0 1px #ccc;
}

.sa-salesComment input[type="text"]:focus,
.salesAnalysis-td input[type="text"]:focus {
    -webkit-box-shadow: inset 0 0 0 0.2rem rgba(51,161,230,.75);
    -moz-box-shadow: inset 0 0 0 .2rem rgba(51,161,230,.75);
    box-shadow: inset 0 0 0 0.2rem rgba(51,161,230,.75);
}

.salesAnalysis-td.calculated input[type="text"] {
    background-color:#e3f5fc;
    box-shadow: 0 0 0 1px #afdcef;
    pointer-events:none;
}

.sa-details { margin-bottom:2.4rem;}

.sa-details .salesAnalysis-td {width: 9.1%;}

.sa-details .salesAnalysis-td.sa-marketValue { width:16.6666%; }

.sa-details .salesAnalysis-td:last-of-type { border-right:.1rem solid #daebf2; }

.sa-details .salesAnalysis-th.spacer,
.sa-details .salesAnalysis-td.spacer {
    padding-left: 0;
    width: 1rem;
}

.sa-mainBuildings .salesAnalysis-td.calculatedRate input[type="text"] {
    color:rgba(15,157,88,1);
    background-color:rgba(15,157,88,0.14);
    box-shadow: 0 0 0 1px rgba(15,157,88,0.54);
    pointer-events:none;
}

.sa-addRemove i	{
    position:relative;
    display:inline-block;
    font-size:2.5rem;
    margin:0 .15rem;
    z-index:1;
    float:right;
}

.sa-addRemove i::hover { opacity:.8; }

.sa-addRemove i::after	{
    position: absolute;
    top: 0.9rem;
    left: 0.3rem;
    content: "";
    background-color: #fff;
    border-radius: 50%;
    width: 1.8rem;
    height: 1.8rem;
    z-index: -1;
}

.salesAnalysis-table .sa-addRemove i::after { top: .3rem; }

i.saRow-add		{ color:rgba(74,144,226,1); }
i.saRow-remove	{ color:#ff6320; }

.advSearch-group i.saRow-remove:hover {
    color:#ff6320;
}

.saControls {
    position:absolute;
    bottom:0;
    left:0;
    background-color:rgba(74,144,226,1);
    background: linear-gradient(40deg, var(--color-blue-100) 20%, var(--color-blue-300) 100%);
    padding:1.6rem 2rem 1.6rem 1.2rem;
    border-top:.1rem solid #eee;
    width:100%;
    height:8.2rem;
}

.saControls span,
.saControls i {
    display:inline-block;
    line-height:2;
    vertical-align:middle;
}

.saButton {
    color:rgba(255,255,255,1);
    text-align:center;
    background-color:rgba(255,255,255,0);
    border-radius:50%;
    width:4.8rem;
    height:4.8rem;
    cursor:pointer;
    transition:background-color .5s ease;
    float:right;
}

.saButton + .saButton { margin-right:1.25rem; }

.saButton:hover {
    color:rgba(74,144,226,1);
    background-color:rgba(255,255,255,1);
    box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
}

.saButton:last-child:hover {
    color:#fff;
    background-color:#d2362b;
}

.saButton.calculate {
    background-image: url(../../images/icons8-Math-48.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: 2.2rem;
}

.sa-Totals {
    display: inline-block;
    color: #fff;
    line-height: 2;
    padding-right:1rem;
    border-right:1px solid rgba(255,255,255,.34);
    margin-left:.8rem;
    width: auto;
    height: 4.8rem;
    box-sizing:border-box;
}

.saControls .sa-useBenchmark {
    display: inline-block;
    color: #fff;
    line-height: 4.6;
    padding-right:3.2rem;
    border-right:1px solid rgba(255,255,255,.34);
    margin-right:2.4rem;
    width: auto;
    height: 4.8rem;
    box-sizing:border-box;
    float: right;
}

.sa-useBenchmark label {
    display: inline-block;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1;
    vertical-align: middle;
}

input[type="checkbox"].benchmark-trigger {
    -webkit-appearance: none;
    background:rgba(255,255,255,.2);
    border-radius: .2rem;
    margin: 0 0 0 1.5rem;
    box-shadow: 0 0 0 0.3rem rgba(255,255,255,1);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align: middle;
}

input[type="checkbox"].benchmark-trigger:checked {
    content: "\E876";
    font-family: "Material Icons";
    font-size: 1.4rem;
    color: #283c64;
    background: #fff;
    width: 1.4rem;
    height: 1.4rem;
}
input[type="checkbox"].benchmark-trigger:checked::before {
    content: "\E876";
    font-family: "Material Icons";
    color: rgba(74,144,226,1);
}

.sa-Totals:last-of-type {
    border-right:none;
}

.sa-Totals label {
    display:block;
    font-size:1rem;
    font-weight:300;
    margin-bottom:.2rem;
}

.sa-Totals span {
    display:block;
    font-size:2rem;
    line-height:1;
}

.sa-rvDate {
    display: inline-block;
    position:absolute;
    top:1rem;
    right:0;
    font-size: 1.1rem;
    font-weight: 600;
    color:#003f64;
    text-align: center;
    line-height: 1.9;
    padding: 0 1.5rem 0.1rem;
    background-color: #f5fcff;
    border: 1px solid #daebf2;
    border-radius: 1.2rem;
    margin-top: -0.25rem;
    margin-left: 1rem;
    min-width: 10rem;
    float: right;
}

.sa-rvDate::before {
    content:"Effective Date: ";
    font-weight: 400;
}

/*@media only screen and (max-width:1200px) {

    .salesAnalysis-form .openProp.salesRow .capval,
    .salesAnalysis-form .openProp.salesRow .landarea,
    .salesAnalysis-form .openProp.salesRow .landval,
    .salesAnalysis-form .openProp.salesRow .tfa,
    .salesAnalysis-form .openProp.salesRow .tla,
    .salesAnalysis-form .openProp.salesRow .valimp,
    .salesAnalysis-form .openProp.salesRow .nsp,
    .salesAnalysis-form .openProp.salesRow .chattels,
    .salesAnalysis-form .openProp.salesRow .salegst,
    .salesAnalysis-form .openProp.salesRow .saleother,
    .salesAnalysis-form .openProp.salesRow .nsptocv {
        padding:2.7rem 0 2.5rem 1rem;
    }

    .salesAnalysis-form .openProp .capval::after,
    .salesAnalysis-form .openProp .landarea::after,
    .salesAnalysis-form .openProp .landval::after,
    .salesAnalysis-form .openProp .valimp::after,
    .salesAnalysis-form .openProp.salesRow .nsp::after,
    .salesAnalysis-form .openProp.salesRow .nsptocv::after,
    .salesAnalysis-form .openProp.salesRow .chattels::after,
    .salesAnalysis-form .openProp.salesRow .salegst::after,
    .salesAnalysis-form .openProp.salesRow .saleother::after {
        left: 1.1rem;
    }

    .salesAnalysis-form .openProp .tfa::after,
    .salesAnalysis-form .openProp .tla::after {
        left: 0;
    }

    .salesAnalysis-form .openProp.salesRow .capval div,
    .salesAnalysis-form .openProp.salesRow .landval div,
    .salesAnalysis-form .openProp.salesRow .valimp div,
    .salesAnalysis-form .openProp.salesRow .nsp div {
        margin-top: 0;
    }

    .salesAnalysis-form .openProp.salesRow .nsp div span {
        padding-left: .2rem;
        letter-spacing: inherit;
    }

    .openProp.salesRow .tfaTla-wrapper {
        display: inline-block;
        position: absolute;
        bottom: .8rem;
        left: calc(69% + .7rem);
    }

    .openProp.salesRow .colCell.tla { left:0; }

    .openProp.salesRow .colCell.tfa,
    .openProp.salesRow .colCell.tla {
        bottom: 0;
        left:0;
    }

    .openProp.salesRow .colCell.tfa,
    .openProp.salesRow .colCell.tla {
        display: inline-block;
        font-weight: 300;
        border: none;
        width: auto;
        height: auto;
        padding: 0 0 0 2.3rem;
        margin-right: 1.2rem;
    }

}

@media only screen and (max-width:1024px) {

    .salesAnalysis-form .openProp .valimp::after { 	content: "Value of Imps."; }

    .salesAnalysis-form .openProp {
        margin: .9rem -1rem 1.1rem;
        width: calc(100% + 2rem);
    }

}*/


/*  ============================================================================================ */
/*  QVHV REPORT ================================================================================ */
/*  ============================================================================================ */

.QVHV-buttons {
    padding:1.6rem 0;
    margin:3.6rem 0 10rem;
    overflow:auto;
}

.QVHV-buttons button {
    display: inline-block;
    position: relative;
    font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    color: rgb(0,0,0);
    text-transform: uppercase;
    line-height: 1;
    line-height: 36px;
    letter-spacing: 0;
    text-align: center;
    text-decoration: none;
    background: transparent;
    border: none;
    border-radius: 2px;
    vertical-align: middle;
    padding: 0 16px;
    margin: 0;
    will-change: box-shadow;
    transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 8.5rem;
    height: 36px;
    overflow: hidden;
    outline: none;
    cursor: pointer;
}

.QVHV-buttons button.primary {
    color: rgb(255,255,255);
    background-color: rgb(84,144,219);
}

.QVHV-buttons button.secondary {
    color: rgb(84,144,219);
    background-color: rgb(237,241,245);
}

.advSearch-row .QVHV-buttons {
    margin:1rem 3.6rem 0;
}

.advSearch-row .QVHV-buttons button {
    font-size: 12px;
    line-height: 30px;
    height: 30px;
}

.advSearch-row .QVHV-buttons button[disabled="disabled"] {
    background-color:rgba(0,0,0,.14);
}

.advSearch-row .QVHV-buttons span {
    display: inline-block;
    vertical-align: middle;
    padding: 0.5rem 0 0.5rem 1.2rem;
    border-left: .1rem solid rgba(0,0,0,.25);
    margin-left: 1.2rem;
}

.advSearch-row .QVHV-buttons.noDivider span { border-left: none; }

.advSearch-row .QVHV-buttons span + span {
    border-left: none;
    margin-left: 0;
}

.QVHV-buttons-left { float:left; }

.QVHV-buttons-left.disabled,
.QVHV-buttons-left .primary.disabled,
.QVHV-buttons-right.disabled,
.QVHV-buttons-right .primary.disabled{
    pointer-events:none;
    opacity:0.5;
}

.QVHV-buttons-right { float:right; }

.QVHV-buttons button { min-width:15rem; }

.md-QVHV-wrapper { padding: 3.6rem; }

.md-left-QVHV {
    display: inline-block;
    padding:0;
    width: 26rem;
    box-sizing: border-box;
    vertical-align: top;
}

.md-right-QVHV {
    display: inline-block;
    padding: 0;
    width: calc(100% - 26rem);
    margin-left:-.3rem;
    box-sizing: border-box;
    vertical-align: top;
    min-height:105rem;
}

.wizard.waiting { position:relative; }

.wizard.waiting::before {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    content: "";
    background: rgba(255,255,255,.9);
    z-index: 2;
}

.md-QVHV-wrapper h1  {
    font-size: 1.6rem;
    font-weight: 800;
    line-height: 0.6;
    margin:0 0 6rem;
}

.md-QVHV-wrapper h1 span {
    display: block;
    font-size:3.6rem;
    font-weight:300;
    margin:2rem 0 0;
}

.masterDetails-Wrapper .md-QVHV-wrapper .toolbar {
    position:relative;
    width:40rem;
    text-align:right;
}

.masterDetails-Wrapper .md-QVHV-wrapper .toolbar li {
    display: inline-block;
    top: initial;
    right: initial;
    margin-left: 1rem;
}

li.selectedComps-count span:empty { display:none; }

li.selectedComps-count span {
    position: absolute;
    top: -.4rem;
    right:-.6rem;
    font-weight:600;
    color:#fff;
    line-height:1.6;
    text-align:center;
    background-color:#46ac53;
    border-radius:50%;
    margin-left: 0;
    width:2rem;
    height:2rem;
    pointer-events:none;
    z-index:1;
}

li.addComp { position:relative; }

li.addComp input[type="text"] {
    font-size: 1.2rem;
    padding: .5rem;
    border: none;
    box-shadow: 0 0 0 1px #ccc;
    height:2.8rem;
}

li.addComp button {
    position:absolute;
    top: 0rem;
    right: -0.3rem;
    background:transparent;
    border:none;
}

li.addComp input + button i {
    color:rgba(74,144,226,1) !important;
}

li.addComp button:hover {
    opacity:.8;
}

li.addComp button:active,
li.addComp button:focus {
    outline:none;
}

li.addComp button i:active,
li.addComp button i:focus {
    color:#283c64 !important;
}



/*
.masterDetails-Wrapper .md-QVHV-wrapper .expandAll {
    position: relative;
    top: initial;
    right: initial;
}

.masterDetails-Wrapper .md-QVHV-wrapper .expandAll.border {
    border-left: .1rem solid rgba(0,0,0,.24);
}
 */



ul.QVHV-stepper {
    border-left: 4px solid #112358;
    margin-left: 1rem;
    margin-bottom:3rem;
}

.QVHV-stepper li {
    margin:0 0 2rem -1.7rem;
    cursor:pointer;
}

.QVHV-stepper a {
    font-size:1.2rem;
    font-weight:600;
    color:#112358;
}

.QVHV-stepper a:hover {
    opacity:initial;
    color:#5290db;
}

.QVHV-stepper li.disabled {
    pointer-events:none;
}

.QVHV-stepper li span {
    display:inline-block;
    line-height:1.2;
}

.QVHV-stepper li span.stepperCount {
    position:relative;
    font-size:1.4rem;
    font-weight:400;
    color:transparent;
    text-align:center;
    background:#112358;
    padding-top:.7rem;
    border-radius:50%;
    margin-right:.8rem;
    width:3rem;
    height:3rem;
    box-sizing:border-box;
    cursor: pointer;
}

.QVHV-stepper li.active span.stepperCount {	background:#5290db; }

.QVHV-stepper li.disabled span.stepperCount {
    background: rgba(237,241,245,1);
    box-shadow: inset 0 0 0 3px #112358;
    pointer-events:none;
}

.QVHV-stepper li span.stepperCount::before,
.QVHV-stepper li.active span.stepperCount::before,
.QVHV-stepper li.disabled span.stepperCount::before {
    position:absolute;
    top:0;
    left:0;
    content:"";
    font-family:"Material Icons";
    font-size:1.3rem;
    line-height:2.3;
    width:3rem;
    height:3rem;
}

.QVHV-stepper li span.stepperCount::before {
    content:"\E83A";
    color:rgba(255,255,255,.7);
}

.QVHV-stepper li.active span.stepperCount::before {
    content:"\E838";
    color:#fff;
}

.QVHV-stepper li.disabled span.stepperCount::before {
    content:"\E897";
    color:#112358;
}

.QVHV-Container.disabled,
.QVHV-buttons.disabled,
.wizard.disabled,
.homeValTab.disabled,
.salesAnalysis-td.disabled {
    pointer-events:none;
    opacity:.7;
}

li.peerReviewFormRequiredTitle{
    color: red;
    font-size: 1.5rem;
    font-weight: bold;
}
li.peerReviewFormButton{
    width: 212px;
    list-style-position:inside;
    border-top:1px solid #e2e2e2;
    border-bottom:1px solid #e2e2e2;
    margin-bottom:1rem;
    font-size: 1.2rem;
    font-weight: bold;
    color:#112358;
}

.jobStatusboard {
    border:1px solid #e2e2e2;
    margin-right:4.8rem;
}

.jobStatusboard li {
    color: #666;
    font-size: 1rem;
    padding: .3rem 1.5rem;
}
.jobStatusboard li span {
    display:block;
    font-size:1.2rem;
    line-height: 1.4;
    color:#000;
}

.jobStatusboard li:first-of-type {
    font-size:1rem;
    padding-top:1rem;
}

.jobStatusboard li:first-of-type span {
    display: block;
    float: initial;
    font-size: 1.7rem;
    font-weight: 600;
    color: initial;
    padding-bottom: 0.3rem;
    border-bottom: 1px solid #e2e2e2;
    margin-bottom: 0rem;
}

.jobStatusboard li:first-of-type span.jobLock {
    display: inline-block;
    color: #dd2c00;
    border:none;
    width: auto;
    float: right;
}

.jobStatusboard li:first-of-type span.jobLock i {
    font-size:2.2rem;
    transition: color .2s ease;
}

.jobStatusboard li:first-of-type span.jobLock:hover i { color: rgb(84,144,219); }

.jobStatusboard li.QVHV-buttons {
    background-color:#fbfcfd;
    padding:1rem 1.5rem;
    border-top:1px solid #e2e2e2;
    margin:.75rem 0 0;
}

.jobStatusboard li.QVHV-buttons button.cancel {
    font-size:1.1rem;
    line-height:1;
    color: #fff;
    background-color: #dd2c00;
    margin:0;
    height:2.7rem;
    width:100%
}

.iconLegend {
    margin-top: 1.8rem;
    font-size: 1.1rem;
    color:#112358;
    margin-right:4.8rem;
    border-bottom: 1px solid #e2e2e2;
}

.iconLegend dt {
    font-size: 1.2rem;
    line-height: 2.4;
    font-weight: 600;
    border-bottom: 1px solid #e2e2e2;
}

.iconLegend dd { margin: 1rem 0; }

.iconLegend i {
    font-style: normal;
    font-weight:600;
}

.iconLegend i::before {
    font-size: 1.4rem;
    color: #fff;
    line-height: 1.7;
    text-align: center;
    background-color: #5290db;
    padding-left: .1rem;
    border: 1px solid #5290db;
    border-radius: 50%;
    margin-right: .6rem;
    vertical-align: middle;
    width: 2.4rem;
    height: 2.4rem;
    box-sizing: border-box;
}

.iconLegend i.required::before      { background-color:#5290db; border-color:#5290db; }
.iconLegend i.notRequired::before 	{ background-color:#bbb; border-color:#bbb; }
.iconLegend i.optional::before 		{ background-color:#4f5a7f; border-color:#4f5a7f; }

.iconLegend i span {
    display: block;
    font-size: 1rem;
    font-weight: normal;
    font-style: italic;
    margin: -.3rem 0 0 3rem;
}

.QVHV-formSection .advSearch-group.required:before 	    { color:#5290db; }
.QVHV-formSection .advSearch-group.notRequired:before 	{ color:#bbb; }
.QVHV-formSection .advSearch-group.optional:before 		{ color:#4f5a7f; }

.md-right-QVHV .md-table { padding:1.6rem 2.4rem 0; }

.md-right-QVHV .md-table h2 {
    display: inline-block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 4rem;
    min-width:50%;
}

.md-right-QVHV .comparableProperties .md-table h2 {
    margin-bottom: 7.2rem;
}

.md-right-QVHV .md-table .selectedComps-open h2 {margin-bottom: 2.9rem;}

.comparableProperties.wizard h2 a {
    display: inline-block;
    position: relative;
    font-weight: normal;
    color: transparent;
    border-radius: 50%;
    margin: -0.3rem 1rem 0 -1rem;
    width: 4rem;
    height: 4rem;
    transition: background-color .5s ease, box-shadow .5s ease;
    cursor: pointer;
    float: left;
    box-shadow: 0 0 0 0.1rem rgba(0,0,0,.14);
}

.comparableProperties.wizard h2 a::before,
.comparableProperties.wizard h2 a::after {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    content:"";
    font-family:"Material Icons";
    font-weight:normal;
    color:#5290db;
    pointer-events:none;
}

.comparableProperties.wizard h2 a::before {
    top: 0.6rem;
    left: -0.1rem;
    content:"\E314";
    font-size: 1.8rem;
}

.comparableProperties.wizard h2 a::after {
    top: 0.3rem;
    left: 1.1rem;
    content: "\E8EE";
    font-size: 2.2rem;
}

.comparableProperties.wizard h2 a:hover {
    background-color:#5290db;
    box-shadow: 0 0 0 0.1rem #5290db;
}

.comparableProperties.wizard h2 a:hover::before,
.comparableProperties.wizard h2 a:hover::after {
    color:#fff;
}

.md-right-QVHV .md-table h2 span {
    display:block;
    font-size: 1.3rem;
    color: #808080;
}

.QVHV-tabs {
    display:block;
    background: rgba(237,241,245,.75);
    padding: 0 1rem;
    box-sizing: border-box;
    clear:both;
}

.QVHV-tabs li {
    display:inline-block;
    width:calc(14.25% - .3rem);
}

.QVHV-tabs li span {
    display:block;
    font-size:1.2rem;
    color:#555;
    text-align:center;
    padding:1.6rem 0 1.4rem;
    box-sizing:border-box;
    cursor:pointer;
}

.QVHV-tabs li span:hover {
    color:#0e3a83;
    opacity:.6;
}

.QVHV-tabs li span.is-active {
    position:relative;
    color:#0e3a83;
}

.QVHV-tabs li span.is-active:hover { opacity:initial; }

.QVHV-tabs hr {
    text-align:left;
    background:#0e3a83;
    border:none;
    margin:0;
    width:calc(14.25% - .3rem);
    height:.3rem;
    transition:.3s ease-in-out;
}

.QVHV-tabs hr.QVHVTab-1 	{ margin-left:0%; 					}
.QVHV-tabs hr.QVHVTab-2 	{ margin-left:calc(14.25% + .3rem); }
.QVHV-tabs hr.QVHVTab-3 	{ margin-left:calc(28.5% + .3rem); 	}
.QVHV-tabs hr.QVHVTab-4 	{ margin-left:calc(42.75% + .3rem); }
.QVHV-tabs hr.QVHVTab-5 	{ margin-left:calc(57% + .3rem); 	}
.QVHV-tabs hr.QVHVTab-6 	{ margin-left:calc(71.25% + .3rem); }
.QVHV-tabs hr.QVHVTab-7 	{ margin-left:calc(85.5% + .3rem); 	}

@media all and (-ms-high-contrast:none), (-ms-high-contrast:active) {
    .QVHV-tabs hr.QVHVTab-1 	{ margin-left:0%; 					}
    .QVHV-tabs hr.QVHVTab-2 	{ margin-left:calc(14.25% + .3rem); }
    .QVHV-tabs hr.QVHVTab-3 	{ margin-left:calc(28.5% + .3rem); 	}
    .QVHV-tabs hr.QVHVTab-4 	{ margin-left:calc(42.75% + .3rem); }
    .QVHV-tabs hr.QVHVTab-5 	{ margin-left:calc(57% + .3rem); 	}
    .QVHV-tabs hr.QVHVTab-6 	{ margin-left:calc(71.25% + .3rem); }
    .QVHV-tabs hr.QVHVTab-7 	{ margin-left:calc(85.5% + .3rem); 	}

}

.QVHV-Container {
    display:none;
    background: rgba(237,241,245,.15);
}

.QVHV-Container .QVHV-tabs { display:none; }

.QVHV-Container.canOpener,
.QVHV-Container.canOpener .QVHV-tabs {
    display:block;
}

input[type="checkbox"].changeAddress-trigger {
    position: absolute;
    top: 1.1rem;
    right: 6.7rem;
    -webkit-appearance: none;
    border-radius: .2rem;
    margin: 1rem 0 0 .5rem;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align: top;
    display: none;
}

input[type="checkbox"].changeAddress-trigger + label::after {
    display: inline-block;
    content: "\f138";
    font-family: "QV-Monarch";
    font-size:2.1rem;
    color: rgba(0, 0, 0, 0.54);
    line-height: 1.7;
    margin-left: 0.4rem;
    vertical-align:middle;
    height: 32px;
    width: 32px;
    border-radius:50%;
    text-align:center;
}



input[type="checkbox"].changeAddress-trigger + label:hover::after { background-color: rgba(158,158,158, 0.20); }

input[type="checkbox"].changeAddress-trigger:checked {
    content:"\E876";
    font-family:"Material Icons";
    font-size:1.4rem;
    color:#fff;
    background:rgb(74,144,226);
    box-shadow:0 0 0 .3rem rgb(74,144,226);
    width:1.4rem;
    height:1.4rem;
}

input[type="checkbox"].changeAddress-trigger:checked + label::before {
    position:absolute;
    bottom:-5.2rem;
    right:-2.1rem;
    display: inline-block;
    content: "\E5CD";
    font-family: "Material Icons";
    font-size:2.4rem;
    color: #5290db;
    line-height: 1.7;
    margin-left: 0.4rem;
    vertical-align:middle;
    height: 32px;
    width: 32px;
    border-radius:50%;
    text-align:center;
    z-index:1;
}

.changeAddress-trigger + label {
    display:inline-block;
    position:absolute;
    top: 0.4rem;
    right: 5rem;
    font-size: 1rem;
    line-height: 2;
    padding: 0 1rem 0 1.7rem;
    border-right: 1px solid #d2d2d2;
    margin:.75rem 0 1.5rem;
}

input[type="checkbox"].changeAddress-trigger ~ .QVHV-Container.changeaddressdetails { display:none; }

input[type="checkbox"].changeAddress-trigger:checked ~ .QVHV-Container.changeaddressdetails {display:block;margin: -2.4rem 0 2.4rem;}

.QVHV-Container.changeaddressdetails {
    position:relative;
    background: #f0f8fe;
    border:1px solid #dbe8f2;
    margin:0 0 4.4rem;
}

.QVHV-Container.changeaddressdetails .QVHV-tabs {
    background: #5290DC;
    padding: 0 1rem;
    border-bottom:1px solid #dbe8f2;
    margin:-.1rem -.1rem 0;
    box-sizing: border-box;
}

.QVHV-Container.changeaddressdetails .QVHV-tabs li span.is-active { color: #fff; }

.QVHV-Container.changeaddressdetails .QVHV-tabs hr { background: #fff; }

.QVHV-Container.changeaddressdetails .advSearch-row { border-bottom:1px dotted #c8d9e6; }

.QVHV-Container.changeaddressdetails .QVHV-formSection {
    padding: .75rem 0 0;
    margin-bottom:0;
}

.QVHV-Container.active { display:block; }

.QVHV-formSection  {
    font-size: 1rem;
    color: rgb(55,61,64);
    padding:1.6rem 0 4.8em;
    border: 1px solid rgba(237,241,245,1);
    border-top: none;
    margin-bottom: 3.6rem;
}

.QVHV-formSection .QVHV-tabs li {
    display: inline-block;
    position: relative;
    padding-left: 5.2rem;
    margin-bottom: 2.4rem;
    width: calc(33.3% - .3rem);
    box-sizing: border-box;
}

.QVHV-formSection strong {
    display: block;
    font-size: 1.3rem;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


.QVHV-formSection .dz-size strong {
    display: inline-block;
    font-size: 1.2rem;
    white-space: inherit;
    overflow: inherit;
    text-overflow: inherit;
    vertical-align:bottom
}

.QVHV-formSection .advSearch-row { padding: 0 0 1.5rem 3.6rem; }

.QVHV-formSection .advSearch-row:last-of-type {
    padding-bottom: 2.4rem;
    border:none;
}

.QVHV-formSection .advSearch-group {
    position:relative;
    padding-left:3.2rem;
    padding-right:2.6rem;
    margin-top:1.25rem;
    margin-right:0;
    width: 16.6%;
}

.QVHV-formSection .advSearch-group:empty::before 				{ display: none; }

.QVHV-formSection .advSearch-group.hundyPct 					{ width:100%; 				}
.QVHV-formSection .advSearch-group.seventyfivePct 				{ width:75%; 				}
.QVHV-formSection .advSearch-group.seventyfivePct-tenRem 		{ width:calc(75% - 10rem); 	}
.QVHV-formSection .advSearch-group.fiftyPct 					{ width:50%; 				}
.QVHV-formSection .advSearch-group.fourtyPct 					{ width:40%; 				}
.QVHV-formSection .advSearch-group.thirtysevenfivePct 			{ width:37.5%; 				}
.QVHV-formSection .advSearch-group.thirtysevenfivePct-fiveRem 	{ width:calc(37.5% - 5rem); }
.QVHV-formSection .advSearch-group.thirtyfivePct 				{ width:35%; 				}
.QVHV-formSection .advSearch-group.thirtythreePct 				{ width:33.3%; 				}
.QVHV-formSection .advSearch-group.thirtytwofivePct 			{ width:32.5%; 				}
.QVHV-formSection .advSearch-group.thirtyPct					{ width:30%; 				}
.QVHV-formSection .advSearch-group.twentyfivePct 				{ width:25%; 				}
.QVHV-formSection .advSearch-group.twentyfivePct-threeRem		{ width:calc(25% - 3.3rem); }
.QVHV-formSection .advSearch-group.twentyPct 				    { width:20%; 				}
.QVHV-formSection .advSearch-group.eighteensevenfivePct 	    { width:18.75%; 			}
.QVHV-formSection .advSearch-group.fifteenPct 				    { width:15%; 				}
.QVHV-formSection .advSearch-group.twelvefivePct 				{ width:12.5%; 				}
.QVHV-formSection .advSearch-group.tenPct 				        { width:10%; 				}
.QVHV-formSection .advSearch-group.fivePct 				        { width:5%; 				}

.QVHV-formSection .advSearch-group.hundyPct textarea,
.QVHV-formSection .advSearch-group.hundyPct span,
.QVHV-formSection .advSearch-group.seventyfivePct span,
.QVHV-formSection .advSearch-group.seventyfivePct-tenRem span,
.QVHV-formSection .advSearch-group.fiftyPct span,
.QVHV-formSection .advSearch-group.fourtyPct span,
.QVHV-formSection .advSearch-group.thirtysevenfivePct span,
.QVHV-formSection .advSearch-group.thirtysevenfivePct-fiveRem span,
.QVHV-formSection .advSearch-group.thirtyfivePct span,
.QVHV-formSection .advSearch-group.thirtythreePct span,
.QVHV-formSection .advSearch-group.thirtytwofivePct span,
.QVHV-formSection .advSearch-group.thirtyPct span,
.QVHV-formSection .advSearch-group.twentyfivePct span,
.QVHV-formSection .advSearch-group.twentyfivePct-threeRem span,
.QVHV-formSection .advSearch-group.twentyPct span,
.QVHV-formSection .advSearch-group.eighteensevenfivePct span,
.QVHV-formSection .advSearch-group.fifteenPct span,
.QVHV-formSection .advSearch-group.twelvefivePct span,
.QVHV-formSection .advSearch-group.tenPct span,
.QVHV-formSection .advSearch-group.fivePct span,
.QVHV-formSection .advSearch-group span.multiselect-selected-text {
    width:100% !important;
}

.QVHV-formSection .advSearch-group.hundyPct span.input-group-btn,
.QVHV-formSection .advSearch-group.seventyfivePct span.input-group-btn,
.QVHV-formSection .advSearch-group.seventyfivePct-tenRem span.input-group-btn,
.QVHV-formSection .advSearch-group.fiftyPct span.input-group-btn,
.QVHV-formSection .advSearch-group.thirtysevenfivePct span.input-group-btn,
.QVHV-formSection .advSearch-group.thirtysevenfivePct-fiveRem span.input-group-btn,
.QVHV-formSection .advSearch-group.thirtythreePct span.input-group-btn,
.QVHV-formSection .advSearch-group.twentyfivePct span.input-group-btn,
.QVHV-formSection .advSearch-group.twentyfivePct-threeRem span.input-group-btn {
    width:2.3rem !important;
}

.QVHV-formSection .advSearch-row .rowSpacer {
    padding-bottom: 2rem;
    border-bottom: 1px dotted #d2d2d2;
    margin-right: 3.6rem;
}

.QVHV-formSection .advSearch-row .rowSpacer + div div.icons8-pencil { margin:2rem 0 0; }

.QVHV-formSection .advSearch-group h4 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.5;
    margin-top: 0.4rem;
}

.QVHV-formSection .advSearch-group textarea {
    font-size: 1.3rem;
    line-height:1.7rem;
    padding: .5rem;
    border:1px solid #d2d2d2;
    margin-right:0;
    width:100% !important;
    min-height:15.1rem;
}
.QVHV-formSection .advSearch-group textarea:focus {
    border:1px solid rgba(51,161,230,.75);
    box-shadow: none;
}

.QVHV-formSection .advSearch-group input[disabled="disabled"] {
    background-color: #e3f5fc;
    box-shadow: 0 0 0 1px #afdcef;
    pointer-events: none;
}

.QVHV-formSection .advSearch-row .advSearch-group:first-of-type { margin-left:0; }

.QVHV-formSection .advSearch-group:before {
    position: absolute;
    top: .4rem;
    left: 0;
    font-size:2.4rem;
    color:#5290db;
    border-radius:3px;
    width: 2.4rem;
    height: 2.4rem;
}

.QVHV-formSection .advSearch-group.sa-addRemove:before { display:none; }

.QVHV-formSection .advSearch-group .btn-group,
.QVHV-formSection .advSearch-group .btn {
    width:100%;
    height:2.7rem;
}
.btn.multiselect-clear-filter,
.btn.multiselect-clear-filter:focus {
    background-color: transparent;
}

.advSearch-group.sa-addRemove {
    padding-left:0;
    margin-bottom:-.5rem;
    width: 10rem;
    vertical-align: bottom;
}

.propDetails-edit.alertWrapper { background-color: rgba(0,0,0,.6); }

.propDetails-edit alert { width:90rem; }

.propDetails-edit h3 {
    font-size: 2rem;
    font-weight: 600;
    color:#112358;
}

.propDetails-edit .QVHV-Container.active {
    border:1px solid #e2e2e2;
    margin:1rem 0 3rem;
}

.propDetails-edit .QVHV-formSection {
    padding-bottom:0;
    margin-bottom:0;
}

.QVHV-formSection .peerReview-wrapper {
    display: block;
    padding: 2.4rem;
    width: calc(100% - 3.6rem);
}

.QVHV-formSection .peerReview-wrapper .advSearch-row  {
    padding:1rem 2.4rem;
    margin:0 -2.4rem 1rem;
}

.QVHV-formSection .peerReview-wrapper ul .advSearch-row  {
    padding:0;
    border:none;
    margin:2.5rem 0 0 0;
}

.QVHV-formSection .peerReview-wrapper ul .advSearch-row .advSearch-group { padding-right:0; }

.peerReview-form {
    display: block;
    margin:3rem 3rem 4rem;
}

.peerReview-form li {
    margin-bottom:1.5rem;
}

.peerReview-form li input[type="radio"] ~ .advSearch-row { display:none; }

.peerReview-form fieldset[role="radiogroup"] {
    position:relative;
    padding: 1.3rem 0 1.6rem 1.3rem;
    width: 100%;
    color: #373d40;
    width: 100%;
}

.peerReview-form fieldset[role="radiogroup"] h3 {
    position:absolute;
    top:0;
    left:0;
    color: #373d40;
    line-height:3.9rem;
    text-align:left;
    background-color: #ecf1f5;
    padding:0 1.6rem;
    margin:0;
    width:100%;
    transition: background-color .5s ease;
}

.peerReview-form li fieldset > input[type="radio"],
.peerReview-form li fieldset > label {
    position:absolute;
    top:1.3rem;
    right:0;
    z-index:5;
}

.peerReview-form li fieldset input[type="radio"] { right: 12.3rem; }
.peerReview-form li fieldset label { right: 9rem; }
.peerReview-form li fieldset input[type="radio"]:first-of-type { right: 5rem; }
.peerReview-form li fieldset label:first-of-type { right: 2rem; }

.peerReview-form li fieldset > label {
    display:inline-block;
    font-size: 1.43rem;
    color: #373d40;
    line-height: 1;
    margin-top: 0;
    margin-left: 1rem;
    width:auto;
}

.peerReview-form input[type="radio"] {
    position:relative;
    display:inline-block;
    -webkit-appearance: none;
    border-radius: 50%;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    z-index:1;
}

.peerReview-form label + input[type="radio"] { margin-left:2rem; }

.peerReview-form input[type="radio"]:checked {
    color: #fff;
    background: #346cae;
    box-shadow: 0 0 0 0.3rem rgb(74,144,226), inset 0 0 0 0.23rem #fff;
    width: 1.4rem;
    height: 1.4rem;
}



.peerReview-form li input.canOpener:checked {
    background: #fff;
    box-shadow: 0 0 0 0.2rem #fff, inset 0 0 0 0.33rem rgb(74,144,226);
}

.peerReview-form li input.canOpener:checked ~ input[type="radio"] {
    box-shadow: 0 0 0 0.2rem #fff, inset 0 0 0 0.33rem rgb(74,144,226);
}

.peerReview-form li input.canOpener:checked ~ label {
    color:#fff;
}

.peerReview-form li input.canOpener:checked ~ h3 {
    color:#fff;
    background-color:rgb(84,144,219)
}

.peerReview-form li input.canOpener:checked ~ .advSearch-row { display:block; }




/* -- VALUATION WORKSHEET STYLES -- */

.QVHV-formSection .salesAnalysis-table {
    margin: 0 2.4rem;
    width:calc(100% - 4.8rem);
}

.md-table .QVHV-formSection h3 {
    position: inherit;
    padding:0;
    margin: 1.6rem 0 0 2.2rem;
}

input[type="checkbox"].proposedZone-trigger ~ .propsedZoning-values,
input[type="checkbox"].changeAddress-trigger ~ .changeaddressdetails,
input[type="checkbox"].hideshowMortgagee-trigger ~ .salesAnalysis-table.mortgageeValues,
input[type="checkbox"].hideshowHNZPeriods-trigger ~ .salesAnalysis-table.hnzPeriods,
input[type="checkbox"].hideshowLikelyRealisablePrice-trigger ~ .salesAnalysis-table.likelyRealisablePrice {
    display:none;
}
input[type="checkbox"].proposedZone-trigger:checked ~ .propsedZoning-values,
input[type="checkbox"].changeAddress-trigger:checked ~ .changeaddressdetails,
input[type="checkbox"].hideshowMortgagee-trigger:checked ~ .salesAnalysis-table.mortgageeValues,
input[type="checkbox"].hideshowHNZPeriods-trigger:checked ~ .salesAnalysis-table.hnzPeriods,
input[type="checkbox"].hideshowLikelyRealisablePrice-trigger:checked ~ .salesAnalysis-table.likelyRealisablePrice {
    display:block;
}

input[type="checkbox"].sundry-client {
    position: relative;
    top: 1.7rem;
    left: 0rem;
    -webkit-appearance: none;
    border-radius: .2rem;
    margin: 1rem 0 0 0rem;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align: top;
}

input[type="checkbox"].includeSales-trigger,
input[type="checkbox"].proposedZone-trigger,
input[type="checkbox"].hideshowMortgagee-trigger,
input[type="checkbox"].hideshowHNZPeriods-trigger,
input[type="checkbox"].hideshowLikelyRealisablePrice-trigger {
    position: relative;
    top: 1.7rem;
    left: 2.2rem;
    -webkit-appearance: none;
    border-radius: .2rem;
    margin: 1rem 0 0 .5rem;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align: top;
}

input[type="checkbox"].includeSales-trigger:checked,
input[type="checkbox"].sundry-client:checked,
input[type="checkbox"].proposedZone-trigger:checked,
input[type="checkbox"].hideshowMortgagee-trigger:checked,
input[type="checkbox"].hideshowHNZPeriods-trigger:checked,
input[type="checkbox"].hideshowLikelyRealisablePrice-trigger:checked {
    content: "\E876";
    font-family: "Material Icons";
    font-size: 1.4rem;
    color: #fff;
    background: rgb(74,144,226);
    box-shadow: 0 0 0 0.3rem rgb(74,144,226);
    width: 1.4rem;
    height: 1.4rem;
}
input[type="checkbox"].sundry-client:checked::before,
input[type="checkbox"].includeSales-trigger:checked::before,
input[type="checkbox"].proposedZone-trigger:checked::before,
input[type="checkbox"].hideshowMortgagee-trigger:checked::before,
input[type="checkbox"].hideshowHNZPeriods-trigger:checked::before,
input[type="checkbox"].hideshowLikelyRealisablePrice-trigger:checked::before {
    content: "\E876";
    font-family: "Material Icons";
    color: #fff;
}

.md-table .QVHV-formSection label h3 {
    color:#373d40;
    line-height:1.2;
    margin-top:0;
    margin-left:5.5rem;
}

.md-table .QVHV-formSection label.no-icon span{
    margin-left:2.5rem;
}

.md-table .QVHV-formSection label span {
    font-size:1.43rem;
    color:#373d40;
    line-height:1.35;
    margin-top:0;
    margin-left:5.5rem;
}

input[type="checkbox"].proposedZone-trigger {
    top: 3.2rem;
    left: 3.6rem;
}

label.proposedZone-trigger {
    display: inline-block;
    margin-top: 4rem;
}

.md-table .QVHV-formSection label.proposedZone-trigger h3 { margin-left:4.5rem; }

.propsedZoning-values { margin-top: 2.4rem; }




/* -- RANGE SLIDER STYLES -- */

input[type=range] {
    display:block;
    background-color:transparent;
    border:none;
    border-radius: 0.15rem;
    width:100%;
    padding:0;
    height:4rem;
    -webkit-appearance: none;
}

input[type=range]:focus,
input[type=range]:hover {
    border:none;
    box-shadow: none !important;
}

input[type='range']::-webkit-slider-thumb {
    background:url(../../images/bubble.png) no-repeat 0 0;
    background-size:100%;
    width: 3rem;
    height: 3.6rem;
    cursor: ew-resize;
    -webkit-appearance: none;
}

input[type=range]::-ms-track {
    color: transparent;  /*remove default tick marks*/
    background: transparent; /*remove bg colour from the track, we'll use ms-fill-lower and ms-fill-upper instead */
    border-color: transparent; /*leave room for the larger thumb to overflow with a transparent border */
    border-width: 1.8rem 0;
    width: 100%;
    height: 0;
}

input[type=range]::-ms-fill-upper,
input[type=range]::-ms-fill-lower,
input[type=range]:hover::-ms-fill-upper,
input[type=range]:hover::-ms-fill-lower,
input[type=range]:focus::-ms-fill-upper,
input[type=range]:focus::-ms-fill-lower {
    border:ransparent;
    box-shadow:none;
    background:transparent;
}

input[type=range]::-ms-thumb {
    background:url(../../images/bubble.png) no-repeat 0 0;
    background-size:100%;
    border:none;
    width: 3rem;
    height: 3.6rem;
}

.sliderLabel-row { border-top:.3rem solid #ddd; }

.sliderLabel-row li {
    position:relative;
    display:inline-block;
    font-size:1.3rem;
    font-weight:600;
    color:rgba(0,0,0,0.54);
    text-align:center;
    margin-top:1rem;
    width:calc(23.3% - 0.15rem);
}

.sliderLabel-row li:first-child { text-align:left; padding-left:1.2rem; width:calc(15% - 0.25rem); }
.sliderLabel-row li:last-child { text-align:right; padding-right:0.3rem; width:calc(15% - 0.45rem); }

.formRow:nth-child(4) .sliderLabel-row li:first-child { padding-left:0; }
.formRow:nth-child(4) .sliderLabel-row li:last-child { padding-right:0; }

.sliderLabel-row li::before {
    position:absolute;
    top:-1.3rem;
    left:calc(50% - 1.5rem);
    display:inline-block;
    content:"";
    background:rgba(0,0,0,0.54);
    border-radius: 0.15rem;
    width:3rem;
    height:0.3rem;
}

.sliderLabel-row li:first-child::before { left:0; }
.sliderLabel-row li:last-child::before 	{ right:-0.2rem; left:inherit; }

/* .advSearch-group.rangeSlider span { padding:5rem 0 .5rem; }*/

.advSearch-group.rangeSlider span { width:100%; }

.advSearch-group.rangeSlider + .advSearch-group.sa-addRemove { top: -2.2rem; }


/* -- RADIO BUTTON STYLES -- */

.QVHV-formSection .advSearch-row .advSearch-group fieldset[role="radiogroup"] { padding: 0.6rem 0 0 0.5rem; }

.QVHV-formSection .advSearch-row .advSearch-group fieldset[role="radiogroup"] span {
    display:inline-block;
    margin-right: 4rem;
    width:auto !important;
}

.QVHV-formSection .advSearch-row .advSearch-group input[type="radio"] {
    display:inline-block;
    -webkit-appearance: none;
    border-radius: 50%;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
}

.QVHV-formSection .advSearch-row .advSearch-group .multiselect-container input[type="radio"] {
    display:none;
}

.QVHV-formSection .advSearch-row .advSearch-group input[type="radio"]:checked {
    color: #fff;
    background: #346cae;
    box-shadow: 0 0 0 0.3rem rgb(74,144,226), inset 0 0 0 0.23rem #fff;
    width: 1.4rem;
    height: 1.4rem;
}


.QVHV-formSection .advSearch-row .advSearch-group input[type="radio"] + label {
    display:inline-block;
    font-size: 1.43rem;
    color: #373d40;
    line-height: 1;
    margin-top: 0;
    margin-left: .5rem;
    width:auto;
}

.QVHV-formSection .advSearch-row .advSearch-group input[type="radio"] + label + input[type="radio"] { margin-left: 2.4rem; }




/* -- VALUATION WORKSHEET STYLES -- */

.compProperty {
    position:relative;
    background: #002b43;
    padding-bottom: 0;
    margin-bottom: 10rem;
    max-width: 106rem;
}

.compProperty .compControls {
    background:#fff;
    text-align: center;
    padding-top:3rem;
}

.compProperty .compControls i { vertical-align:bottom; }

.compProperty .compControls .listingButton {
    display:inline-block !important;
    background-color: rgba(240,248,254,.5);
    box-shadow: inset 0 0 0.1rem #ddd;
}

i.listingButton.material-icons.assessmentNav[style="display: none;"] {
    color: #e2e2e2;
    pointer-events:none;
}
i.listingButton.material-icons.assessmentNav.disabled { color: #cccccc }
i.listingButton.material-icons.assessmentNav.disabled:hover { background-color: initial; cursor:default; }

.compProperty .compControls .listingButton:hover { background-color: rgba(74,144,226,1); }

input[type="checkbox"].selectComp-trigger {
    display: inline-block;
    position: absolute;
    top: -2.6rem;
    left: 0.8rem;
    -webkit-appearance: none;
    background: transparent;
    border-radius: 50%;
    margin: 0 0 0 .5rem;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align: middle;
    z-index:1;
}

input[type="checkbox"].selectComp-trigger + label {
    display: block;
    position: absolute;
    top: -3.9rem;
    font-size: 1.5rem;
    text-align: left;
    color: rgb(55, 61, 64);
    background: #ddd;
    padding: .8rem 1rem .7rem 4rem;
    width: 100%;
    -webkit-appearance: none;
    cursor: pointer;
    transition: background-color .5s ease;
}

input[type="checkbox"].selectComp-trigger:not(:checked) + label {
    padding-right:5rem;
}

input[type="checkbox"].selectComp-trigger:not(:checked) + label::before {
    display: inline-block;
    content: "Don't ";
    margin-right: .5rem;
}



/* input[type="checkbox"].selectComp-trigger + label::before {
    display: inline-block;
    position: absolute;
    top: calc(50% - 1.8rem);
    left: calc(50% - 1.8rem);
    content:"\E148";
    font-family: "Material Icons";
    font-size: 3.6rem;
    color: #eee;
    text-align: center;
    line-height:1;
    width: 3.6rem;
    height: 3.6rem;
} */

input[type="checkbox"].selectComp-trigger:checked + label {
    color: #fff;
    background: #00b341;
}

input[type="checkbox"].selectComp-trigger:checked::before {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    font-family: "Material Icons";
    content: "\E876";
    font-size: 1.4rem;
    color: rgb(74,144,226);
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 0 0.3rem rgb(255,255,255);
    width: 1.4rem;
    height: 1.4rem;
}

/* input[type="checkbox"].selectComp-trigger:checked + label::before {
    content: "\E5CA";
        top: calc(50% - 2.4rem);
    left: calc(50% - 2.4rem);
    font-family: "Material Icons";
    font-size: 4.8rem;
} */


.compBubbles {
    margin: 2rem auto 0;
    width:30rem;
}

.compBubbles li { display:inline-block; }

.compBubbles li span {
    display:block;
    background-color:#ddd;
    border-radius: 50%;
    margin: .3rem;
    width: 1.2rem;
    height: 1.2rem;
    transition: background-color .2s ease;
}

.compBubbles li.active span {
    position: relative;
    background-color: #fff;
    box-shadow: 0px 0px 0px 3px #3f94db, inset 0px 0px 0px 3px #43c8ff;
}

.compBubbles li.selectedComp span {
    background-color:#46ac53;
}

.compProperty ul.qivsLinks {
    position:absolute;
    right:1rem;
    top:0;
}

.compProperty ul.qivsLinks li {margin-left: .5rem;/* text-align: center; */}

.compProperty ul.qivsLinks li.comp-photoUploader {
    position: relative;
    top: 0.9rem;
    display: inline-block;
    border-left: 1px solid rgba(255,255,255,.5);
    padding: .5rem 0 2.5rem 1.2rem;
    margin-left: 1rem;
}

.compProperty .md-propertyOverview {
    display:inline-block;
    padding: 0;
    width: 37.5%;
    float: inherit;
    position: initial;
    color: transparent;
    background-color: transparent;
    height: auto;
    overflow: initial;
}

.compProperty .md-address {
    position:absolute;
    top:0;
    left:0;
    width:100%;
    float: inherit;
}

.compProperty .md-propertyOverview em {
    display:block;
    font-size: 1.1rem;
    line-height: 1.4;
    font-weight: 400;
    color:rgba(255,255,255,.8);
    font-style:normal;
    background-color:#002b43;
    padding:.8rem 1.2rem;
    margin:0 0 .75rem;
}

.compProperty .md-propertyOverview em span { display: block; }

.compProperty .md-propertyOverview em strong {
    display:inline-block;
    font-size: 1.3rem;
    line-height: 1.4;
    font-weight: 600;
    color:rgba(255,255,255,1);
    margin-left:.2rem;
}

.compProperty .md-propertyOverview h3 {
    top:5.7rem;
    left:1.2rem;
    font-size: 2rem;
    font-weight: 600;
}

.compProperty .md-propertyOverview h3 span {
    display:block;
    font-size:1.3rem;
    line-height:1.2;
    font-weight:400;
}

.compProperty .md-totals {
    display: block;
    font-size: 1.2rem;
    font-weight: 400;
    line-height: 1.4;
    text-align: left;
    /* margin: initial; */
    margin: 1.4rem 1.8rem;
    margin-left: 1.2rem;
    width: auto;
    float: inherit;
    clear: both;
}

.compProperty .md-totals li {
    display: block;
    line-height: 1.2;
    padding-left: 0;
    border: none;
    margin-left: 0;
    margin-bottom: .6rem;
}

.compProperty .md-totals li:first-child {
    font-size: 1.3rem;
}

.compProperty .md-totals label {
    display: inline-block;
    font-size: 1.2rem;
    font-weight: 300;
    line-height: 1;
    text-align: left;
    margin-right: .5rem;
    width:7.5rem;
    float: none;
}

.compProperty .md-values {
    display: block;
    position: inherit;
    background-color: #283c64;
    padding: .7rem 0.25rem 1.2rem 1.2rem;
    width: 100%;
    clear: both;
    line-height: 1.4;
    border-bottom:none;
}

.compProperty .md-values li {
    font-size:1.8rem;

    width: calc(33% - 1.1rem);
    display: inline-block;
    font-weight: 400;
    margin: 0 1rem 0 0;
}

.compProperty .md-values li label {
    display: block;
    font-size: .9rem;
    color: rgba(255,255,255,.7);
    line-height: 2.2;
    border-bottom: .1rem solid rgba(255,255,255,.2);
    margin-bottom: .2rem;
}

.compProperty .md-values span {
    display: block;
    font-size: 1.15rem;
    font-weight: 300;
    margin-top: .1rem;
}

.compProperty .md-values strong::after {
    display: inline-block;
    content: "/";
    margin: 0 .02rem 0 .2rem;
}

.compProperty .md-values strong {
    font-size: 1.3rem;
    font-weight: 300;
    margin-right: .2rem;
}

.compProperty .md-values.md-lastSale {
    position: inherit;
    color: #fff;
    background-color: transparent;
}

.compProperty .md-values.md-lastSale span {
    display: inline-block;
    width:auto;
}

.compProperty .md-values.md-lastSale span::before {
    display: inline-block;
    content: "/";
    margin: 0 .2rem;
}

.compProperty .md-photoGallery {
    display:inline-block;
    margin-left:-.3rem;
    width: 37.5%;
    float: none;
    vertical-align:top;
}

.compProperty .slick-dotted.slick-slider { margin-bottom:0; }

.compProperty .md-photoGallery ul.photoUploader-icon {
    position: absolute;
    right: -.5rem;
    bottom: -3.2rem;
}

.compProperty .md-propertySale {
    padding-bottom:2.5rem;
    top: 6rem;
}

.compProperty .md-values.md-lastSale li {
    width: calc(50% - 1.2rem);
    margin-top: 1rem;
}

.compProperty .md-values.md-lastSale li.saleStatus {
    right: 0;
    font-size: 1.1rem;
    color:#fff;
    line-height: 2;
    text-align: center;
    background: #214d90;
    padding: 0 2rem;
    border-radius: 1.1rem;
    min-width: 10.6rem;
    width: auto;
    margin: 3.9rem 0 1rem;
    float: right;
}


.compProperty .md-values.md-lastSale li.saleStatus.pendingSale {
    color:#4e342e;
    background-color: rgba(255,200,40,1);
}

.compProperty .md-values.md-lastSale li.saleStatus.unconfirmedSale {
    color:#fff;
    background-color: rgba(190,55,15,1);
}

.compProperty .comparableStatement {
    text-align:center;
    padding:3rem 10rem 1rem;
    border:1px solid #e2e2e2;
    border-top:none;
    background-color:#fbfcfd;
}

.compProperty .comparableStatement span {
    display:inline-block;
    position:relative;
    margin-bottom: 1.6rem;
    width: auto;
}

.compProperty .comparableStatement span::after {
    display:block;
    position:absolute;
    top:calc(50% - .25rem);
    right:2rem;
    content:"";
    border-top: .5rem solid #fff;
    border-right: .5rem solid transparent;
    border-left: .5rem solid transparent;
}

.comparableStatement {
    text-align: center;
}

.comparableStatement select {
    display: inline-block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #fff;
    text-align: center;
    line-height: 2;
    background-color: #002b43;
    padding: 0 3rem;
    border: none;
    border-radius: 3rem;
    min-width: 16rem;
    cursor:pointer;
}

.compProperty .comparableStatement div {
    position: initial;
    font-size:1.5rem;
    line-height:1.4;
    margin-bottom:2.4rem;
}

.compProperty .comparableStatement textarea {
    font-size: 1.5rem;
    font-weight: 400;
    font-family: 'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif;
    line-height: 1.4;
    padding: 1rem;
    border:none;
    box-shadow: inset 0 0 0 0.2rem rgba(51,161,230,1);
    height: 12rem !important;
    width: 100%;
}

.compProperty .comparableStatement textarea:focus {
    font-size: 1.5rem;
    height: 12rem !important;
}

.compProperty .comparableStatement button,
.compProperty .comparableStatement input[type=button] {
    font-size:1.3rem;
    color:#333;
    line-height:2;
    background-color:#ddd;
    border:none;
    border-radius:.2rem;
    margin-top:1rem;
    width:auto;
    min-width:10rem;
    overflow:visible;
    -webkit-appearance:none;
}

.compProperty .comparableStatement button[type="submit"] {
    color:#fff;
    background-color:#4c8ce1;
}

.compProperty .comparableStatement button + button { margin-left:1rem !important;	}


/*  COMPARABLE PROPERTIES EXTRA DETAILS  ======================================================== */

.compProperty .extras {
    display:block;
    position:relative;
    background:none;
    padding:0;
    border-top:none;
    margin:3.2rem .35rem 0;
    width:inherit;
}

.compProperty .extras .md-landMas {
    padding: 3rem 0 3.8rem 0;
    border: none;
    margin: 0;
    width:100%;
}

.compProperty .extras .md-landMas li {
    top: 0;
    font-size:0;
    padding:0 0 0 2.8rem;
    margin: 0.6rem .35rem 0 0;
    max-width: calc(8% - .65rem);
    background: #5f6b72;
    border-radius:.2rem;
}

.compProperty .extras .md-landMas strong {
    font-size:1.1rem;
    color:#fff;
    line-height:2.5;
}

.compProperty .extras .md-landMas li.md-masIcon-landUse,
.compProperty .extras .md-landMas li.md-masIcon-walls,
.compProperty .extras .md-landMas li.md-masIcon-roof,
.compProperty .extras .md-landMas li.md-masIcon-contour,
.compProperty .extras .md-landMas li.md-masIcon-viewScope {
    max-width:inherit;
    width: calc(20% - 0.43rem);
    float:left;
}

.compProperty .extras .md-landMas li.md-masIcon-viewScope {
    margin-right: 0;
    width: 20.23%;
}

.compProperty .extras .md-landMas li.md-masIcon-category 		{ position:absolute; top:-.1rem; left:0;     }
.compProperty .extras .md-landMas li.md-masIcon-eyb 			{position:absolute;  top: -.1rem;left:7.7%;  }
.compProperty .extras .md-landMas li.md-masIcon-bedrooms 		{ position:absolute; top:-.1rem; left:15.4%; }
.compProperty .extras .md-landMas li.md-masIcon-toilets 		{ position:absolute; top:-.1rem; left:23.1%; }
.compProperty .extras .md-landMas li.md-masIcon-units 			{ position:absolute; top:-.1rem; left:30.8%; }
.compProperty .extras .md-landMas li.md-masIcon-fsg 			{ position:absolute; top:-.1rem; left:38.5%; }
.compProperty .extras .md-landMas li.md-masIcon-umrg 			{ position:absolute; top:-.1rem; left:46.2%; }
.compProperty .extras .md-landMas li.md-masIcon-oli 			{ position:absolute; top:-.1rem; left:53.9%; }
.compProperty .extras .md-landMas li.md-masIcon-modernisation 	{ position:absolute; top:-.1rem; left:61.6%; }
.compProperty .extras .md-landMas li.md-masIcon-zone 			{ position:absolute; top:-.1rem; left:69.3%; }
.compProperty .extras .md-landMas li.md-masIcon-lotPosition 	{ position:absolute; top:-.1rem; left:77%;   }
.compProperty .extras .md-landMas li.md-masIcon-production 		{ position:absolute; top:-.1rem; left:84.7%; }
.compProperty .extras .md-landMas li.md-masIcon-maoriLand 		{ position:absolute; top:-.1rem; left:92.4%; max-width: calc(8% - 0.5rem); }

.compProperty .extras .md-landMas li:before {
    top: .5rem;
    left: .5rem;
    font-size: 1.7rem;
    color:#fff;
    width: 2rem;
    height: 2rem;
}

.compProperty .extras .md-masIcon-units::before		   			{ background-position:-1.8rem 0;	 	 }
.compProperty .extras .md-masIcon-zone::before					{ background-position:-3.6rem 0;		 }
.compProperty .extras .md-masIcon-walls::before		   			{ background-position:-7.4rem 0;		 }
.compProperty .extras .md-masIcon-roof::before					{ background-position:-9.3rem 0;		 }
.compProperty .extras .md-masIcon-maoriLand::before	   			{ background-position:-1.8rem -1.8rem;	 }
.compProperty .extras .md-masIcon-viewScope::before	   			{ background-position:-7.4rem -1.8rem;	 }
.compProperty .extras .md-masIcon-lotPosition::before	 		{ background-position:-9.2rem -1.8rem;	 }
.compProperty .extras .md-masIcon-contour::before		 		{ background-position:-11rem -1.8rem;	 }
.compProperty .extras .md-masIcon-eyb::before			 		{ background-position:-1.9rem -3.6rem;	 }
.compProperty .extras .md-masIcon-modernisation::before   		{ background-position:-3.6rem -3.6rem;	 }
.compProperty .extras .md-masIcon-bedrooms::before				{ background-position:-9.4rem -3.6rem;	 }
.compProperty .extras .md-masIcon-toilets::before		 		{ background-position:-11.2rem -3.6rem;  }
.compProperty .extras .md-masIcon-oli::before			 		{ background-position:-12.9rem -3.6rem;  }
.compProperty .extras .md-masIcon-category::before	 			{ background-position:-14.8rem -3.6rem;  }
.compProperty .extras .md-masIcon-umrg::before		   			{ background-position:-9.2rem -5.4rem;	 }
.compProperty .extras .md-masIcon-fsg::before					{ background-position:-11rem -5.4rem;	 }
.compProperty .extras .md-masIcon-production::before	 		{ background-position:-14.8rem -5.4rem;  }


.compProperty .QVHV-formSection {
    padding: 0 0 1rem 2rem;
    border: none;
    margin-bottom: 0;
    color: #fff;
}

.compProperty .QVHV-formSection h3 { margin: 1.6rem 0 1rem 0; }

.compProperty .advSearch-row { padding:0; border-bottom:0; }

.compProperty .advSearch-row:last-of-type .advSearch-group {padding: 0 2.3rem 0 3.5rem;}

.compProperty label { color: #fff; }

.compProperty span { width: 100%; }

.qvtd-comparable-statement span::after {
    display: block;
    position: absolute;
    top: calc(50% - -0.75rem);
    right: 2rem;
    content: "";
    border-top: 0.5rem solid #fff;
    border-right: 0.5rem solid transparent;
    border-left: 0.5rem solid transparent;
}

.qv-mt-5 {
    margin-top: 0.5rem;
}

.qv-position-relative {
    position: relative;
}

.qvtd-comparable-statement-label {
    font-size: 1.1rem;
    color: var(--qv-color-blue);
}


/*  QVHV DROPZONE PHOTO AND ATTACHMENT UPLOADER  =============================================== */

.advSearch-row .uploaderBody .dropzone {
    position: inherit;
    box-sizing: border-box;
    padding: 3.2rem;
    border: 1px solid #d8ebf2;
    margin-top: 1.6rem;
    width: calc(100% - 3.6rem);
}

.advSearch-row .dropzone .dz-message { margin: 0 0 2rem; }

.advSearch-row .disabled {pointer-events:none; opacity:0.5;}

.advSearch-row .uploadConfirm {
    position: absolute;
    top: 9rem;
    right: 3rem;
}

.advSearch-row .uploadConfirm i {
    display:inline-block;
    cursor:pointer;
}

.advSearch-row .uploadConfirm.disabled i {
    color:rgba(0,0,0,.14);
    pointer-events:none;
}

.md-table .QVHV-formSection .uploaderBody h3,
.md-table .QVHV-formSection .dz-browser-not-supported span {
    background-position:50% 0;
    padding: 12rem 0 1rem;
    margin:0;
}

.md-right-QVHV .md-table .advSearch-row h2 {
    margin-top:2.4rem;
}

.reportPhoto {
    display: inline-block;
    position:relative;
    padding:0 1.6rem 1.6rem;
    margin:0 3.2rem 2.4rem 0;
    width: calc(50% - 3.5rem);
    box-sizing:border-box;
    vertical-align:top;
}

.reportPhoto:nth-child(-n + 2) { margin-top:3rem; }

.qvhvReport-files {
    display: block;
    padding:1.6rem;
    width:calc(100% - 3.6rem);
}

.qvhvReport-files.reviewRequest { padding: 0; }

.qvhvReport-files li {
    position:relative;
    margin-bottom:.2rem;
}


.reportPhoto .includePhoto {
    display: block;
    margin:0 -1.6rem 1.6rem;
}

.reportPhoto .includePhoto input[type="checkbox"],
.QVHV-formSection .qvhvReport-files li input[type="checkbox"] {
    display:inline-block;
    position:absolute;
    top:.3rem;
    left:1rem;
    -webkit-appearance: none;
    border-radius: .2rem;
    margin: 1rem 0 0 .5rem;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align: top;
    z-index:1;
}

.md-table .QVHV-formSection .reportPhoto .includePhoto label,
.md-table .QVHV-formSection .qvhvReport-files li input + label,
.md-table .QVHV-formSection .qvhvReport-files li > label {
    display: block;
    color: #373d40;
    background-color:#ecf1f5;
    padding: 0 0 0 4rem;
    width: 100%;
    transition: background-color .5s ease;
}

.md-table .QVHV-formSection .qvhvReport-files li > label { padding-left:1.6rem; }

.md-table .QVHV-formSection .qvhvReport-files li > label { position: initial; }

.md-table .QVHV-formSection .reportPhoto .includePhoto label h3,
.md-table .QVHV-formSection .qvhvReport-files li label h3 {
    line-height: 2.7;
    margin:0;
}

.md-table .QVHV-formSection .qvhvReport-files .multiselect-container>li {
    background-color: #fff;
    margin-bottom:0
}

.md-table .QVHV-formSection .qvhvReport-files .multiselect-container>li>a:focus label,
.md-table .QVHV-formSection .qvhvReport-files .multiselect-container>li.active>a label,
.md-table .QVHV-formSection .qvhvReport-files .multiselect-container>li.active>a:focus label {
    color: #fff;
    background-color: #337ab7;
}

.reportPhoto i,
.qvhvReport-files li i {
    position:absolute;
    top:.8rem;
    right:4.8rem;
    z-index:1;
}

.reportPhoto i + i,
.qvhvReport-files li i + i {
    position:absolute;
    top:.8rem;
    right:1rem;
    z-index:1;
}

.reportPhoto i.download,
.qvhvReport-files li i.download,
.reportPhoto i + i.download,
.qvhvReport-files li i + i.download {
    pointer-events:auto;
    opacity: 1;
}

.reportPhoto .includePhoto input[type="checkbox"]:checked,
.qvhvReport-files li input[type="checkbox"]:checked {
    content: "\E876";
    font-family: "Material Icons";
    font-size: 1.4rem;
    color: #5290db;
    background: rgb(255,255,255);
    box-shadow: 0 0 0 0.3rem rgb(255,255,255);
    width: 1.4rem;
    height: 1.4rem;
}

.qvhvReport-files.reviewRequest.reviewFailed li input[type="checkbox"]:checked,
.qvhvReport-files.reviewRequest.reviewFailed li input[type="checkbox"]:checked + label { pointer-events:none; }

.reportPhoto .includePhoto input[type="checkbox"]:checked::before,
.qvhvReport-files li input[type="checkbox"]:checked::before {
    content: "\E876";
    font-family: "Material Icons";
    color:#5290db;
}

.reportPhoto .includePhoto input[type="checkbox"]:checked + label,
.qvhvReport-files li input[type="checkbox"]:checked + label {
    background-color: #5290db;
}

.reportPhoto .includePhoto input[type="checkbox"]:checked + label h3,
.qvhvReport-files li input[type="checkbox"]:checked + label h3 {
    color: #fff;
}

.reportPhoto .includePhoto input[type="checkbox"]:checked ~ i,
.qvhvReport-files li input[type="checkbox"]:checked ~ i {
    color: #fff;
}

.qvhvReport-files li input[type="checkbox"] ~ .advSearch-row { display:none; }

.qvhvReport-files li input[type="checkbox"]:checked ~ .advSearch-row { display:block; }

.reportPhoto img { height:auto; }

.reportPhoto .photoCaption {
    position:absolute;
    bottom:1.6rem;
    font-size:1.3rem;
    color:#fff;
    line-height:1.5;
    padding:1rem 1.6rem;
    background:rgba(0,0,0,.5);
    background: linear-gradient(to right, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.0) 100%);
    width:calc(100% - 3.2rem);
}

ul.photoCaption.noDetails { background: #dd2c00; }

ul.photoCaption.noDetails::before {
    content: "Details Required";
    font-size: 1.5rem;
    font-weight: 600;
}

ul.photoCaption.noDetails::after {
    display:block;
    content: "Click the ADD DETAILS button to add a description or tag for this photo";
    font-size: 1.2rem;
}

ul.photoCaption.noDetails li { display:none }

.md-table .QVHV-formSection .reportPhoto .includePhoto fieldset[role="radiogroup"] {
    background-color: #ecf1f5;
    padding: 0 0 0 1.5rem;
    transition: background-color .5s ease;
}

.md-table .QVHV-formSection .reportPhoto .includePhoto fieldset[role="radiogroup"] span {
    display:inline-block;
    margin-right: 2rem;
    width:auto !important;
}

.md-table .QVHV-formSection .reportPhoto .includePhoto input[type="radio"] {
    display:inline-block;
    position: relative;
    top: -0.1rem;
    -webkit-appearance: none;
    border-radius: 50%;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    z-index:2;
}

.md-table .QVHV-formSection .reportPhoto .includePhoto .multiselect-container input[type="radio"] { display:none; }

.md-table .QVHV-formSection .reportPhoto .includePhoto input[type="radio"]:checked {
    background: #fff;
    box-shadow: 0 0 0 0.2rem #fff, inset 0 0 0 0.43rem rgb(74,144,226);
    width: 1.4rem;
    height: 1.4rem;
}

.md-table .QVHV-formSection .reportPhoto .includePhoto input[type="radio"] + label {
    display:inline-block;
    position:relative;
    font-size: 1.5rem;
    font-weight:600;
    color: #373d40;
    line-height: 2.7;
    background:transparent;
    padding-left:.5rem;
    margin-top: 0;
    margin-left: .5rem;
    width:auto;
    z-index:2;
}

.reportPhoto fieldset + i { right: 4.8rem; }


.md-table .QVHV-formSection .reportPhoto .includePhoto fieldset[role="radiogroup"].selected { background-color: #5290db; }

.md-table .QVHV-formSection .reportPhoto .includePhoto fieldset[role="radiogroup"].selected label { color: #fff; }

.md-table .QVHV-formSection .reportPhoto .includePhoto fieldset[role="radiogroup"].selected input[type="radio"] { box-shadow: 0 0 0 0.2rem #fff, inset 0 0 0 0.33rem rgb(74,144,226); }

.md-table .QVHV-formSection .reportPhoto .includePhoto fieldset[role="radiogroup"].selected + i { color: #fff; }

.md-table .QVHV-formSection .reportPhoto img ~ .advSearch-group { padding:.5rem 0 .5rem 3.2rem; }

/*  SKINNY AND NARROW PROPERTY CARD EXTRA DETAILS  ============================================= */

@media only screen and (max-width:1366px) {

    /*     .compProperty .extras .md-landMas {
            position:relative;
            padding:2.7rem 1rem .9rem;
            overflow:hidden;
        }

        .compProperty .extras .md-landMas li { top:.5rem; }

        .compProperty .extras .md-landMas li.md-masIcon-landUse,
        .compProperty .extras .md-landMas li.md-masIcon-walls,
        .compProperty .extras .md-landMas li.md-masIcon-roof,
        .compProperty .extras .md-landMas li.md-masIcon-contour,
        .compProperty .extras .md-landMas li.md-masIcon-viewScope {
            margin-top:-.3rem;
        }

        .compProperty .extras .md-landMas li.md-masIcon-oli 			{ left:44rem;	 			}
        .compProperty .extras .md-landMas li.md-masIcon-modernisation 	{ left:50.5rem;	 			}
        .compProperty .extras .md-landMas li.md-masIcon-zone 			{ left:56rem;	 			}
        .compProperty .extras .md-landMas li.md-masIcon-lotPosition 	{ left:61.25rem; 			}
        .compProperty .extras .md-landMas li.md-masIcon-production 		{ top:3.2rem; left:1rem;	}
        .compProperty .extras .md-landMas li.md-masIcon-maoriLand 		{ top:3.2rem; left:7.25rem;	}
        .compProperty .extras .md-landMas li.md-masIcon-landUse 		{ margin-left:12.25rem; 	} */

}

.selectedComps-open .compProperty {
    margin-top:5rem;
    margin-bottom:7rem;
}

.selectedComps-open .compProperty:last-of-type {
    margin-bottom:7.2rem;
}

.selectedComps-open .compProperty .compControls {
    position: absolute;
    top: -3.9rem;
    text-align: left;
    background: transparent;
    padding-top: 0;
    width: 100%;
}

.selectedComps-open .compProperty .compControls .listingButton { display:none !important; }

.selectedComps-open .compProperty .compControls input[type="checkbox"].selectComp-trigger {
    display:inline-block;
    position:absolute;
    top: 1.3rem;
    left: 0.8rem;
    -webkit-appearance: none;
    background:transparent;
    border-radius: 50%;
    margin: 0 0 0 .5rem;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
    vertical-align:middle;
}

.selectedComps-open .compProperty .compControls input[type="checkbox"].selectComp-trigger::before {
    display: none;
}

.selectedComps-open .compProperty .compControls input[type="checkbox"].selectComp-trigger:checked::before {
    display: inline-block;
    position: absolute;
    top:0;
    left:0;
    font-family:"Material Icons";
    content: "\E876";
    font-size: 1.4rem;
    color: rgb(74,144,226);
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 0 0.3rem rgb(255,255,255);
    width: 1.4rem;
    height: 1.4rem;
}

.selectedComps-open .compProperty .compControls input[type="checkbox"].selectComp-trigger + label {
    display: inline-block;
    position: inherit;
    font-size: 1.5rem;
    color: rgb(55, 61, 64);
    background: #ddd;
    padding: .8rem 1rem .7rem 4rem;
    border-radius: inherit;
    margin: 0;
    width: 100%;
    height: auto;
    vertical-align: middle;
    transition: background-color .5s ease;
}

.selectedComps-open .compProperty .compControls input[type="checkbox"].selectComp-trigger:checked + label::before { display:none; }

.selectedComps-open .compProperty .compControls input[type="checkbox"].selectComp-trigger:not(:checked) + label::before {
    display: inline-block;
    position: inherit;
    content:"Don't ";
    font-family: "Open Sans";
    font-size: 1.5rem;
    color: rgb(55, 61, 64);
    text-align: left;
    line-height: 1.4;
    margin-right:.5rem;
    width: auto;
    height: auto;
}

.selectedComps-open .compProperty .compControls input[type="checkbox"].selectComp-trigger:checked + label {
    color:#fff;
    background:#00b341;
}

.selectedComps-open .compProperty .compControls .compBubbles { display:none; }


.QVHV-formSection.compsList-filters { position:relative; }

.QVHV-formSection.compsList-filters input[type="checkbox"].compsFilters-trigger {
    display:none;
    position:absolute;
    top:-3.6rem;
    right:8rem;
    width:2.8rem;
    height:2.8rem;
    -webkit-appearance:none
}

.QVHV-formSection.compsList-filters input[type="checkbox"].compsFilters-trigger + label {
    display:block;
    position:absolute;
    top: -3.9rem;
    right:8rem;
    font-size:2.8rem;
    line-height:1;
    color:#0e3a83;
}

.QVHV-formSection.compsList-filters input[type="checkbox"].compsFilters-trigger + label::before {
    display:block;
    content: "\f137";
    font-family:"QV-Monarch";
    width:2.8rem;
    height:2.8rem;
    transition: all .2s linear;
}

.QVHV-formSection.compsList-filters input[type="checkbox"]:checked.compsFilters-trigger + label::before {
    content: "\f136";
    transform:rotate(180deg);
}

.QVHV-formSection.compsList-filters .selectComps-confirm {
    position: absolute;
    top: -4.9rem;
    right: 1rem;
}

.QVHV-formSection.compsList-filters input[type="checkbox"].compsFilters-trigger ~ .compsFilters {
    opacity:0;
    height:0;
    pointer-events:none;
}

.QVHV-formSection.compsList-filters input[type="checkbox"]:checked.compsFilters-trigger ~ .compsFilters {
    margin-bottom: 1.6rem;
    border-bottom: 1px dotted #e2e2e2;
    opacity:1;
    height:auto;
    pointer-events:inherit;
}

.compsFilters { transition: all .2s linear }

.compsList.salesResults .resultsInner-wrapper { margin:0; }


ul.reportFormat { margin: 2rem 0 0 .5rem; }

ul.reportFormat li { margin: 0 0 1.5rem; }

ul.reportFormat input[type="radio"] {
    display: inline-block;
    -webkit-appearance: none;
    border-radius: 50%;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
}

ul.reportFormat input[type="radio"]:checked {
    color: #fff;
    background: #346cae;
    box-shadow: 0 0 0 0.3rem rgb(74,144,226), inset 0 0 0 0.23rem #fff;
    width: 1.4rem;
    height: 1.4rem;
}

ul.reportFormat label {
    display: inline-block;
    font-size: 1.43rem;
    color: #373d40;
    line-height: 1;
    margin-top: 0;
    margin-left: 1rem;
    width: auto;
    vertical-align: middle;
}






/*  COMPS LIST SORT ROW AND CLOSED VIEW ======================================================== */

.compsList {
    position: relative;
    background: #fff;
    padding-top: 3.2rem;
    border: 5px solid rgba(237,241,245,.2);
    border-top:none;
    border-bottom:none;
    margin-top: -1.6rem;
    z-index: 1;
}

/*
.compsList .colHeader,
.compsRow.salesRow .colCell,
.compsRow.salesRow .searchDetails-wrapper .colCell {
	box-shadow: inset 0px 0px 0px 1px red;
}
 */

/*  COMPS LIST HEADERS ===================== */

.compsList .sortRow { padding-right:0; }

.compsRow.salesRow .colCell.nsp,
.compsRow.salesRow .colCell.saleStatus,
.compsRow.salesRow .colCell.chattels,
.compsRow.salesRow .colCell.grosssale div { display:none; }

.compsList .colHeader.tfa,
.compsList .colHeader.tla {
    width:5%;
}

.compsList .colHeader.compSelect {
    text-align:left;
    width:7%;
}

.compsList .colHeader.category,
.compsList .colHeader.distance {
    width:8%;
}

.compsList .colHeader.valref,
.compsList .colHeader.qv-col-sale-id,
.compsList .colHeader.saleDate,
.compsList .colHeader.grosssale,
.compsList .colHeader.landarea {
    width:10%;
}

.compsList .colHeader.address {
    padding-left:2.8rem;
    width:25%;
}


/*  COMPS LIST ROWS=== ===================== */

.compsRow-wrapper {
    position:relative;
}

.compsRow-wrapper:last-of-type { margin-bottom: -4.4rem; }

.compsRow-wrapper .resultsRow {
    padding:0;
    border-bottom: .1rem solid #f2f2f2;
}

.compsRow-wrapper:last-of-type .resultsRow { border-bottom: none; }

.compsRow-wrapper .compSelect {
    position: absolute;
    top: 2.2rem;
    left: 1.4rem;
    z-index: 1;
}

.compSelect label { display:none; }

.compSelect input[type="checkbox"] {
    -webkit-appearance: none;
    border-radius: .2rem;
    margin: 1rem 0 0 .5rem;
    box-shadow: 0 0 0 0.3rem rgba(0,0,0,.54);
    width: 1.4rem;
    height: 1.4rem;
}

.compSelect input[type="checkbox"]:checked:before {
    content: "\E876";
    font-family: "Material Icons";
    font-size: 1.4rem;
    color: #fff;
    background: rgb(74,144,226);
    border-radius: .2rem;
    box-shadow: 0 0 0 0.3rem rgb(74,144,226);
    width: 1.4rem;
    height: 1.4rem;
}

.compsRow .colCell.address {
    padding-left:6rem;
    width: 32%;
}

.compsRow.salesRow .colCell.tfa,
.compsRow.salesRow .colCell.tla {
    width:12%;
}

.compsRow.salesRow .colCell.compSelect {
    text-align:center;
    width:7%;
}

.compsRow.salesRow .colCell.category,
.compsRow.salesRow .colCell.distance {
    width:8%;
}

.compsRow.salesRow .colCell.valref {
    width:10%;
}

.compsRow.salesRow .colCell.qv-col-sale-id,
.compsRow.salesRow .colCell.saleDate,
.compsRow.salesRow .colCell.grosssale,
.compsRow.salesRow .colCell.landarea {
    width:20%;
}

.compsRow .colHeader.address {
    width:25%;
}


.compsRow.salesRow .colCell.compSelect {
    position:relative;
    text-align:center;
    padding: 0 1.5rem 0 0;
    z-index:1;
}

.compsList .colHeader.distance,
.compsRow.salesRow .colCell.distance { padding-right:1rem; }


.resultsRow.compsRow.salesRow:hover .fullAddress {
    padding: .5rem;
    margin-left: .5rem;
    box-shadow: none;
    border-radius: .25rem;
    width: calc(100% - 6.4rem);
    box-sizing: border-box;
}

.resultsRow.compsRow.salesRow .fullAddress {
    padding: 1rem;
    margin-left: 0;
}

.resultsRow.compsRow.salesRow:hover .fullAddress::after {
    display:none;
    top: 0.5rem;
    right: 0.5rem;
    width: 1.6rem;
    height: 1.6rem;
}

/*  COMPS LIST SORT ROW AND OPEN VIEW ======================================================== */

.compsRow-wrapper .openProp.resultsRow { padding: 1.6rem 1rem 0; }

.openProp.compsRow.salesRow .fullAddress { margin-left: -1.3rem; }

.openProp.compsRow.salesRow:hover .fullAddress { margin-left: -0.8rem; }

.openProp.compsRow .colCell.address {
    padding: inherit;
    width: inherit;
    margin-top: -2rem;
}

.openProp.compsRow.salesRow {
    padding-right: 0.5rem;
    border:1px solid #eee;
    margin-bottom:1rem;
}

.openProp.compsRow.salesRow::before { display:none; }

.openProp.compsRow.salesRow .colCell.compSelect {
    position:absolute;
    top: 3rem;
    left: 3.9rem;
    width: auto;
    z-index:1;
}

.masterDetails-Wrapper .md-QVHV-wrapper .openProp.compsRow.salesRow .toolbar {
    display: block;
    position: absolute;
    top: 1.2rem;
    right: 1.6rem;
    margin: 0;
}

.openProp.compsRow.salesRow .toolbar li.md-sales,
.openProp.compsRow.salesRow .toolbar li[title="Upload Photos"] {
    display:none;
}

.openProp.compsRow.salesRow .colCell.grosssale,
.openProp.compsRow.salesRow .colCell.distance {
    display:none;
}

.openProp.compsRow.salesRow .colCell.nsp,
.openProp.compsRow.salesRow .colCell.chattels { display:inherit; }

.openProp.compsRow.salesRow:hover .fullAddress {
    margin-top: -0.3rem;
    margin-bottom: .5rem;
}

.openProp.compsRow.salesRow:hover .fullAddress {
    padding-right: 1rem;
    width:auto;
}

.openProp.compsRow.salesRow .fullAddress span { margin-left:4rem; }

.openProp.compsRow.salesRow .md-link { margin-left:4rem; }

.openProp.compsRow.salesRow .fullAddress .qpid,
.openProp.compsRow.salesRow .fullAddress .valref,
.openProp.compsRow.salesRow .fullAddress .legaldesc,
.openProp.compsRow.salesRow .fullAddress .sa-category,
.openProp.compsRow.salesRow .sales-trafficLights .qv-col-sale-id,
.openProp.compsRow.salesRow .sales-trafficLights .saleClassification,
.openProp.compsRow.salesRow .sales-trafficLights .saleDate,
.openProp.compsRow.salesRow .sales-trafficLights .saleStatus,
.openProp.compsRow.salesRow .sales-trafficLights .saleAnalysis {
    font-size:1rem;
    margin-right:.5rem
}

.openProp.compsRow.salesRow .fullAddress .qpid { min-width:10rem; }
.openProp.compsRow.salesRow .fullAddress .valref { min-width:13rem; }

.openProp.compsRow.salesRow .sales-trafficLights .qv-col-sale-id,
.openProp.compsRow.salesRow .sales-trafficLights .saleClassification,
.openProp.compsRow.salesRow .sales-trafficLights .saleDate,
.openProp.compsRow.salesRow .sales-trafficLights .saleStatus {
    width: 20%;
}

.openProp.compsRow.salesRow .sales-trafficLights .saleAnalysis { width: 17%; }

.openProp.compsRow.salesRow .sales-trafficLights {
    top:6.5rem;
    left: calc(50% - 1rem);
    padding: .6rem 0 0.6rem 0;
    margin-left: 0;
    width:45%;
}

.openProp.compsRow.salesRow .sales-trafficLights .saleAnalysis { margin-right:0; }

.openProp.compsRow.salesRow .sales-trafficLights .saleClassification.classTwo ~ .vendPurchase,
.openProp.compsRow.salesRow .sales-trafficLights .saleClassification.classThree ~ .vendPurchase {
    margin-left:0;
}

.openProp.compsRow.salesRow .sales-trafficLights .saleStatus { display:block; }

.openProp.compsRow.salesRow .searchDetails-wrapper { height:7.4rem; }

.openProp.salesRow .colCell.landarea { width:31%; }

.openProp.compsRow.salesRow .capval,
.openProp.compsRow.salesRow .landarea,
.openProp.compsRow.salesRow .landval,
.openProp.compsRow.salesRow .valimp,
.openProp.compsRow.salesRow .nsptocv,
.openProp.compsRow.salesRow .nsp,
.openProp.compsRow.salesRow .gsp,
.openProp.compsRow.salesRow .chattels,
.openProp.compsRow.salesRow .salegst,
.openProp.compsRow.salesRow .saleother {
    font-size: 1.4rem;
    height:7.4rem;
}

.openProp.compsRow.salesRow .tfa,
.openProp.compsRow.salesRow .tla {
    font-size: 1.4rem;
}

.openProp.compsRow.salesRow .capval div,
.openProp.compsRow.salesRow .landval div,
.openProp.compsRow.salesRow .valimp div,
.openProp.compsRow.salesRow .nsp div {
    font-size: 1.3rem;
}

.openProp.compsRow.salesRow .tfaTla-wrapper { bottom: .6rem; }

.openProp.compsRow.salesRow .colCell.tfa,
.openProp.compsRow.salesRow .colCell.tla {
    font-size: 1.3rem;
    margin-right: 1rem;
    width: calc(50% - 1rem);
}

.openProp.compsRow.salesRow .chattels 	{ left: calc(127.1% + .5rem); }
.openProp.compsRow.salesRow .salegst 	{ left: calc(139.6% + .5rem); }
.openProp.compsRow.salesRow .saleother 	{ left: calc(152.1% + .5rem); }

.openProp.compsRow.salesRow .photoGallery_propSearch {
    position: relative;
    background: #002943;
    padding: .8rem 0.5rem;
    border-top: 1px solid #485a7a;
    margin-top: 0;
    max-width: 104.7rem;
    z-index: 1;
    overflow-x: auto;
    box-sizing: border-box;
}


/*  COMPARABLE PROPERTIES EXTRA DETAILS  ======================================================== */

.openProp.compsRow.salesRow .extras {
    display: block;
    position: relative;
    background: #002943;
    padding: 9rem .5rem .5rem;
    border-top: 1px solid #485a7a;
    margin: -8.6rem 0 1rem;
    width: calc(100% - 0.3rem);
    z-index: 0;
}

.openProp.compsRow.salesRow .extras .md-landMas {
    padding: 3rem 0 3.8rem 0;
    border: none;
    margin: 0;
    width:100%;
}

.openProp.compsRow.salesRow .extras .md-landMas li {
    top: 0;
    font-size:0;
    padding:0 0 0 2.8rem;
    margin: 0.6rem .35rem 0 0;
    max-width: calc(8% - .65rem);
    background: #5f6b72;
    border-radius:.2rem;
}

.openProp.compsRow.salesRow .extras .md-landMas strong {
    font-size:1.1rem;
    color:#fff;
    line-height:2.5;
}

.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-landUse,
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-walls,
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-roof,
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-contour,
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-viewScope {
    max-width:inherit;
    width: calc(20% - 0.43rem);
    float:left;
}

.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-viewScope {
    margin-right: 0;
    width: 20.23%;
}

.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-category 		{ position:absolute; top:8.6rem; left:.5rem; }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-eyb 			{ position:absolute; top:8.6rem; left:calc(7.7% + .5rem);  }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-bedrooms 		{ position:absolute; top:8.6rem; left:calc(15.4% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-toilets 		{ position:absolute; top:8.6rem; left:calc(23.1% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-units 		{ position:absolute; top:8.6rem; left:calc(30.8% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-fsg 			{ position:absolute; top:8.6rem; left:calc(38.5% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-umrg 			{ position:absolute; top:8.6rem; left:calc(46.2% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-oli 			{ position:absolute; top:8.6rem; left:calc(53.9% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-modernisation { position:absolute; top:8.6rem; left:calc(61.6% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-zone 			{ position:absolute; top:8.6rem; left:calc(69.3% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-lotPosition 	{ position:absolute; top:8.6rem; left:calc(77% + .5rem);   }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-production 	{ position:absolute; top:8.6rem; left:calc(84.7% + .5rem); }
.openProp.compsRow.salesRow .extras .md-landMas li.md-masIcon-maoriLand 	{ position:absolute; top:8.6rem; left:calc(92.4% + .5rem); max-width: calc(7% - 0.4rem); }

.openProp.compsRow.salesRow .extras .md-landMas li:before {
    top: .5rem;
    left: .5rem;
    font-size: 1.7rem;
    color:#fff;
    width: 2rem;
    height: 2rem;
}

.openProp.compsRow.salesRow .extras .md-masIcon-units::before		   		{ background-position:-1.8rem 0;	 	 }
.openProp.compsRow.salesRow .extras .md-masIcon-zone::before				{ background-position:-3.6rem 0;		 }
.openProp.compsRow.salesRow .extras .md-masIcon-walls::before		   		{ background-position:-7.4rem 0;		 }
.openProp.compsRow.salesRow .extras .md-masIcon-roof::before				{ background-position:-9.3rem 0;		 }
.openProp.compsRow.salesRow .extras .md-masIcon-maoriLand::before	   		{ background-position:-1.8rem -1.8rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-viewScope::before	   		{ background-position:-7.4rem -1.8rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-lotPosition::before	 		{ background-position:-9.2rem -1.8rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-contour::before		 		{ background-position:-11rem -1.8rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-eyb::before			 		{ background-position:-1.9rem -3.6rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-modernisation::before   	{ background-position:-3.6rem -3.6rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-bedrooms::before			{ background-position:-9.4rem -3.6rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-toilets::before		 		{ background-position:-11.2rem -3.6rem;  }
.openProp.compsRow.salesRow .extras .md-masIcon-oli::before			 		{ background-position:-12.9rem -3.6rem;  }
.openProp.compsRow.salesRow .extras .md-masIcon-category::before	 		{ background-position:-14.8rem -3.6rem;  }
.openProp.compsRow.salesRow .extras .md-masIcon-umrg::before		   		{ background-position:-9.2rem -5.4rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-fsg::before					{ background-position:-11rem -5.4rem;	 }
.openProp.compsRow.salesRow .extras .md-masIcon-production::before	 		{ background-position:-14.8rem -5.4rem;  }


.openProp.compsRow.salesRow .otherFeatures {
    padding: .8rem 3rem 3rem;
    width:100%;
}

.openProp.compsRow.salesRow .otherFeatures label { color: #fff; }

.openProp.compsRow.salesRow .otherFeatures span { width: 100%; }

.openProp.compsRow.salesRow .photoGallery_propSearch:empty { display:none; }

.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras {
    padding-top: .4rem;
    margin-top: 0;

}

.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-category,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-eyb,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-bedrooms,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-toilets,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-units,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-fsg,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-umrg,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-oli,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-modernisation,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-zone,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-lotPosition,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-production,
.openProp.compsRow.salesRow .photoGallery_propSearch:empty + .extras .md-landMas li.md-masIcon-maoriLand {
    top:0;
}




/*  ============================================================================================ */
/*  VALUATION JOBS DASHBOARD =================================================================== */
/*  ============================================================================================ */

.valuationJobs-dashboard .resultsInner-wrapper { position:relative; background-color: #fff; }

.qvSorter {
    position: absolute;
    right: 1.5rem;
    line-height: 2.4;
    padding: 1rem .5rem 0 .5rem;
    box-shadow: 0 0 0 0.1rem rgba(232,232,232,1);
    border-radius: .25rem;
    margin: 1.5rem 0.5rem 0 0;
    box-sizing: border-box;
}

.qvSorter:hover { box-shadow: 0 0 0 0.2rem rgba(51,161,230,.75); }

.qvSorter .btn .caret { position:inherit; }

.qvSorter:hover .caret { border-top-color: rgba(51,161,230,1); }

.advSearch-group.qvSorter span.multiselect-native-select {
    box-shadow: none;
    margin-right: 0 !important;
}

.advSearch-group.qvSorter .btn { box-shadow: none; }

.resultsRow.valjobRow { padding-right:1.2rem; }

.resultsRow.valjobRow.theSkinny,
.resultsRow.valjobRow.theSkinny .searchDetails-wrapper {
    height: 7.1rem;
}

.valjobRow.completedJob {
    border-top:.4rem solid #eee;
    background-color:#fcfcfc;
}

.valjobRow.completedJob ~  .valjobRow.completedJob { border-top:none; }

.valjobRow.overdueJob {
    color:#fff;
    background-color:#d7633e;
}

.valjobRow.overdueJob .fullAddress { color:#fff; }

.valjobRow .searchDetails-wrapper { width:100%; }

.valjobRow .searchDetails-wrapper .colHeader.active .icon {
    display: inline-block;
    position: absolute;
    top: .45rem;
    left: -2rem;
}

.valjobRow .colHeader.address,
.valjobRow .colCell.address {
    width:23%;
}

.valjobRow .colHeader.valref,
.valjobRow .colCell.valref {
    text-align:left;
    width:10%;
}

.valjobRow .colHeader.jobdue,
.valjobRow .colCell.jobdue,
.valjobRow .colHeader.inspectiontime,
.valjobRow .colCell.inspectiontime,
.valjobRow .colHeader.reporttype,
.valjobRow .colCell.reporttype,
.valjobRow .colHeader.assignedvaluer,
.valjobRow .colCell.assignedvaluer,
.valjobRow .colHeader.countersignedvaluer,
.valjobRow .colCell.countersignedvaluer,
.valjobRow .colHeader.jobstatus,
.valjobRow .colCell.jobstatus {
    text-align: left;
    width:20%;
}

.valjobRow .colHeader.jobdue,
.valjobRow .colCell.jobdue {
    width:16.25%;
}

.valjobRow .colHeader.inspectiontime,
.valjobRow .colCell.inspectiontime {
    width:16.25%;
}

.valjobRow .colHeader.reporttype,
.valjobRow .colCell.reporttype {
    width:25%;
}

.valjobRow .colHeader.assignedvaluer,
.valjobRow .colCell.assignedvaluer {
    width:16.25%;
}

.valjobRow .colHeader.countersignedvaluer,
.valjobRow .colCell.countersignedvaluer {
    width:16.25%;
}

.valjobRow .colHeader.jobstatus,
.valjobRow .colCell.jobstatus {
    width:15%;
}



/*  ============================================================================================ */
/*  COMP PROPERTIES MOBILE HEADER & STRUCTURE  ================================================= */
/*  ============================================================================================ */

.compProperty-mobile {
    position: absolute;
    top: 8.3rem;
    right: calc(100% - 43.5rem);
    bottom: 0;
    left: 0;
    color: #fff;
    background: #fff;
    padding-bottom: 2rem;
    box-sizing: border-box;
    overflow: auto;
}

.compProperty-mobile::before {
    display: block;
    position: absolute;
    left: 43.3rem;
    top: 8.3rem;
    bottom: 0;
    content: "";
    background-color: #f9f9f9;
    width: calc(100% - 43.3rem);
}

.compProperty-mobile .cpm-headerWrapper {
    position: fixed;
    top: 0;
    width: 100%;
    z-index:1;
}

.compProperty-mobile h1 {
    font-size:.8rem;
    color:transparent;
    background-image:url(../../images/logo-monarch-mobile.png);
    background-repeat: no-repeat;
    background-size:15rem 3.3rem;
    background-position:1.6rem 50%;
    margin:0;
    height:5.5rem;
}

.compProperty-mobile h2 {
    font-size:1.2rem;
    font-weight:600;
    color:#022c43;
    text-align:center;
    text-transform:uppercase;
    line-height:2.4;
    background:#e5e9ec;
    margin:0;
}

.compProperty-mobile .cpm-headerWrapper li:first-of-type {
    position: relative;
    color: #fff;
    background-color: rgba(30,65,150,1);
    background: linear-gradient(to right, rgba(22,69,139,.95) 0%,rgba(18,39,86,.9) 100%), url(../../images/monarchWing.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 100% 0;
    width:100%;
    height:5.5rem;
    padding:0;
    margin:0;
}

.cpm-headerWrapper + div { padding:2.5rem 0 0 1.6rem; }



/*  ============================================================================================ */
/*  COMP PROPERTIES MOBILE LIST VIEW  ========================================================== */
/*  ============================================================================================ */

.compsCount {
    font-size: 1.5rem;
    font-weight: 700;
    color: rgba(0,0,0,.87);
    margin-left:.2rem;
}

.compsCount span {
    display:block;
    font-size: 1.1rem;
    font-weight: 400;
    color: #808080;
}

.cpm-list {
    display:block !important;
    background:#fff;
    padding:1rem 0 3rem;
    width:40rem;
}

.cpm-card {
    position:relative;
    color:#012b43;
    padding:0.5rem 0 0.5rem 0.5rem;
    margin-bottom:1.6rem;
    box-shadow: inset 0px 0px 0px 1px #e5e9ec;
    height:9rem;
}

.cpm-card:last-of-type { margin-bottom:0; }

.cpm-card img {
    display:inline-block;
    position:absolute;
    top:.5rem;
    left:.5rem;
    width:8rem;
    height:8rem;
}

.cpm-card h3.cpm-address  {
    font-size:1.5rem;
    font-weight:600;
    color:#24508e;
    line-height:1.3;
    margin:.2rem 0 0 8.8rem;
}

.cpm-card h3.cpm-address span {
    display:block;
    font-size:1.1rem;
    font-weight:600;
}


.cpm-card .cpm-values {
    margin:.7rem 0 0 8.8rem;
}

.cpm-card .cpm-values li {
    display:inline-block;
    font-size:1.4rem;
    font-weight:600;
    margin-right:.7rem;
    width:calc(33.3% - 1rem);
}

.cpm-card .cpm-values label {
    display:block;
    font-size:.9rem;
    line-height: 1.8;
    border-bottom:1px solid #e5e9ec;
    margin-bottom:.1rem;
}



/*  ============================================================================================ */
/*  COMP PROPERTIES EXPANDED MOBILE VIEW  ====================================================== */
/*  ============================================================================================ */

.cpm-expanded {
    position: fixed;
    top: 8.3rem;
    left: 43.3rem;
    bottom: 0;
    right: 0;
    background-color: #022c43;
    border-right: 1.6rem solid #fff;
    overflow: auto;
    z-index:10;
}

.cpm-expanded .cpm-toolbar {
    font-size:3rem;
    line-height:0;
    background:#fff;
    padding:4.3rem .8rem .5rem 1rem;
    overflow:hidden;
}

.cpm-expanded .cpm-toolbar li {
    display:inline-block;
    margin-left:1.5rem;
    float:right;
}

.cpm-expanded .cpm-toolbar li:first-of-type { display:none; }

.cpm-expanded .cpm-toolbar li:first-of-type a {
    display:inline-block;
    line-height:.8;
    text-align:center;
    background-color:rgba(79,145,223,1);
    border-radius:50%;
    width:2.6rem;
    height:2.6rem;
}

.cpm-expanded .cpm-toolbar li i { font-size:2.8rem; }

.cpm-expanded .cpm-toolbar li:first-of-type i {
    font-size:2rem;
    color:#fff;
}

.cpm-expanded .cpm-address {
    background: #24508e;
    padding: 1rem 2rem 2.4rem;
}

.cpm-expanded h3.cpm-address  {
    font-size:2.2rem;
    line-height:1.4;
    padding-right: 20rem;
    margin:0;
}

.cpm-expanded h3.cpm-address span {
    display:block;
    font-size:1.3rem;
}

.cpm-expanded .cpm-values {
    padding:1.6rem .2rem .5rem .5rem;
}

.cpm-expanded .cpm-totals {
    position: absolute;
    top:7.8rem;
    right: 0;
}

.cpm-expanded .cpm-values li {
    display:inline-block;
    width: calc(33.3% - 3rem);
    margin: 0 1.4rem;
}

.cpm-expanded .cpm-totals li { display:block; }

.cpm-expanded .cpm-totals li span,
.cpm-expanded .cpm-values li span {
    display:inline-block;
    font-size:1rem;
    margin-left:.2rem;
}

.cpm-expanded .cpm-values li span {	font-size:1.4rem; }

.cpm-expanded .cpm-totals {
    text-align:right;
    background-color:#24508e;
    padding:1.2rem .5rem 0;
}

.cpm-expanded .cpm-totals li {
    position:relative;
    font-size:1.4rem;
    line-height: 1.3;
    margin: 0 1.6rem 0 0;
    min-width: 12.5rem;
}

.cpm-expanded .cpm-totals li::before {
    display:inline-block;
    position: absolute;
    top: .3rem;
    left: 0;
    content:"";
    font-size:1rem;
    color:rgba(255,255,255,.6);
}

.cpm-expanded .cpm-totals li:nth-child(1)::before { content:"Total Floor:"; 	}
.cpm-expanded .cpm-totals li:nth-child(2)::before { content:"Total Living:"; 	}
.cpm-expanded .cpm-totals li:nth-child(3)::before { content:"Land Area:"; 		}

.cpm-expanded .cpm-totals label { display:none; }

.cpm-expanded .cpm-values li {
    font-size:2.4rem;
    margin-bottom:1.2rem;
}

.cpm-expanded .cpm-values label {
    display:block;
    font-size:1.1rem;
    color:rgba(255,255,255,.6);
    line-height: 2;
    border-bottom:1px solid rgba(255,255,255,.25);
    margin-bottom:.2rem;
}

.cpm-expanded .cpm-values li strong {
    display:block;
    font-size:1.3rem;
    font-weight:400;
}

.propPics {
    background-color:#fff;
    padding:.1rem 0 3.6rem;
}

.cpm-expanded .cpm-landMas { padding:0 .3rem 0 .45rem; }

.cpm-expanded .cpm-landMas li {
    display: inline-block;
    font-size: 1.15rem;
    font-weight:300;
    line-height:3;
    background-color: #5f6b72;
    padding: 0 0 0 .5rem;
    border-radius: .2rem;
    margin: .2rem 0 0;
    width: calc(25% - .25rem);
    box-sizing: border-box;
}

.cpm-expanded .cpm-landMas li:nth-child(n + 13) {
    width:calc(50% - .25rem);
}

.cpm-expanded .cpm-landMas li::before {
    font-size: 1.6rem;
    line-height:1;
    vertical-align: middle;
    margin-right: 0.5rem;
}

.cpm-expanded .cpm-landMas li label { display:none; }

.cpm-expanded .cpm-additionalFeatures {
    padding:1.6rem 1rem 3.2rem;
}

.cpm-expanded .cpm-additionalFeatures h3 {
    display: block;
    font-size: 1rem;
    color: rgba(255,255,255,.6);
    margin-bottom: .3rem;
}

.cpm-expanded .cpm-additionalFeatures p { font-size: 1.1rem; }



/*  ============================================================================================ */
/*  COMP PROPERTIES MOBILE SALES ANALYSIS  ===================================================== */
/*  ============================================================================================ */

.cpm-extraText {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    text-align: center;
    background: rgba(255,255,255,1);
    padding: 5rem 1rem 0;
    z-index: 10;
}

.cpm-extraText h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #003f64;
    text-align:left;
    margin-bottom: 1.5rem;
}

.cpm-extraText h3 i { float:right; }

.cpm-extraText textarea {
    font-size: 1.3rem;
    font-weight: 400;
    font-family: 'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif;
    color:#000;
    line-height: 1.6;
    padding: .5rem .75rem;
    border:none;
    width:100%;
    height: calc(100vh - 17rem);
    box-shadow: inset 0 0 0 1px #ccc;
}

.cpm-extraText textarea:focus { box-shadow: inset 0 0 0 0.2rem rgba(51,161,230,.75); }



/*  ============================================================================================ */
/*  COMP PROPERTIES MOBILE PHOTO UPLOADER  ===================================================== */
/*  ============================================================================================ */

.cpm-uploadPhoto {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    text-align: center;
    background: rgba(255,255,255,1);
    padding-top:5rem;
    z-index: 10;
}

.buttonRow {
    position:absolute;
    right:0;
    bottom:0;
    left:0;
    background:#f9f9f9;
    border-top:1px solid #eee;
    height:7.3rem;
    z-index:1;
}

.cpm-uploadPhoto button {
    background: transparent;
    border: none;
    outline:0;
}

.buttonRow i.material-icons {
    font-size: 4.8rem;
    line-height: 1.4;
}

.imageHolder {
    display:block;
    position:relative;
    line-height:0;
    margin:0 auto 2rem;
    width:92.5%;
}

.imageHolder i {
    display: block;
    position: absolute;
    top: -0.9rem;
    right: -0.9rem;
    font-size: 1.7rem;
    line-height: 1.5;
    background-color: rgba(79,145,223,1);
    border-radius: 50%;
    width: 2.6rem;
    height: 2.6rem;
    border: none;
}

.imageHolder canvas { width:100% }



/*  ============================================================================================ */
/*  COMP PROPERTIES MOBILE SLICK SLIDER OVERIDES  ============================================== */
/*  ============================================================================================ */

.compProperty-mobile .slick-initialized .slick-slide { height:auto; }

.compProperty-mobile .slick-dots { bottom:1rem; }

.compProperty-mobile .slick-dots li { margin: 0 .1rem; }

.compProperty-mobile .slick-dots li button:before {
    font-size: 1.2rem;
    color:#4f6682;
    line-height: 2;
    opacity: 1;
}

.compProperty-mobile .slick-dots li button:before {
    font-size: 1.2rem;
    color:#4f6682;
    line-height: 2;
    opacity: 1;
}

.compProperty-mobile .slick-dots li.slick-active button:before { color: #3395df; }

.compProperty-mobile .slick-dotted.slick-slider { margin-bottom:.5rem; }

.compProperty-mobile .slick-prev, .compProperty-mobile .slick-next { display:none !important; }



/*  ============================================================================================ */
/*  COMP PROPERTIES MOBILE MATERIAL ICON STYLES  =============================================== */
/*  ============================================================================================ */

.material-icons { cursor:pointer; }

.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

.material-icons.cpm-dark { color: rgba(0, 0, 0, 0.54); }
.material-icons.cpm-dark.cpm-inactive { color: rgba(0, 0, 0, 0.26); }
.material-icons.cpm-light { color: rgba(255, 255, 255, 1); }
.material-icons.cpm-light.cpm-inactive { color: rgba(255, 255, 255, 0.3); cursor:initial; }
.material-icons.cpm-blue { color: rgba(79,145,223,1); }


.icon > .material-icons.cpm-dark:hover,
.icon > .material-icons.cpm-dark.cpm-inactive:hover,
.icon > .material-icons.cpm-light:hover { color: rgba(0, 0, 0, 0.87); }

button.material-icons.cpm-dark:hover,
button.material-icons.cpm-dark.cpm-inactive:hover,
button.material-icons.cpm-light:hover { color: rgba(0, 0, 0, 0.87); }

.material-icons.cpm-18 { font-size: 18px; }
.material-icons.cpm-24 { font-size: 24px; }
.material-icons.cpm-36 { font-size: 36px; }
.material-icons.cpm-48 { font-size: 48px; }



/*  ============================================================================================ */
/*  QV ICONS =================================================================================== */
/*  ============================================================================================ */


@font-face {
    font-family: "QV-Monarch";
    src: url("../../fonts/QV-Monarch.eot");
    src: url("../../fonts/QV-Monarch.eot?#iefix") format("embedded-opentype"),
    url("../../fonts/QV-Monarch.woff2") format("woff2"),
    url("../../fonts/QV-Monarch.woff") format("woff"),
    url("../../fonts/QV-Monarch.ttf") format("truetype"),
    url("../../fonts/QV-Monarch.svg#QV-Monarch") format("svg");
    font-weight: normal;
    font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
    @font-face {
        font-family: "QV-Monarch";
        src: url("../../fonts/QV-Monarch.svg#QV-Monarch") format("svg");
    }
}

[data-icons8]:before { content: attr(data-icons8); }

.icons8, [data-icons8]:before,
.icons8-air-conditioner-filled:before,
.icons8-apple:before,
.icons8-ask-question:before,
.icons8-basement-filled:before,
.icons8-bed-filled:before,
.icons8-bench-sink-filled:before,
.icons8-birthday-cake-filled:before,
.icons8-brake-warning-filled:before,
.icons8-brick-wall-filled:before,
.icons8-bus-filled:before,
.icons8-calendar-filled:before,
.icons8-carpet-filled:before,
.icons8-category-filled:before,
.icons8-checked-user-male:before,
.icons8-clock:before,
.icons8-closed-window-filled:before,
.icons8-compass:before,
.icons8-contacts:before,
.icons8-cow-filled:before,
.icons8-crosshair-filled:before,
.icons8-curtains-filled:before,
.icons8-defensive-wood-wall-filled:before,
.icons8-dishwasher-filled:before,
.icons8-driveway-new:before,
.icons8-earthquake-symbol:before,
.icons8-edit-user-male:before,
.icons8-email:before,
.icons8-field-filled:before,
.icons8-fire-station-filled:before,
.icons8-floods-filled:before,
.icons8-floor-plan-filled:before,
.icons8-forest-filled:before,
.icons8-foundation-new:before,
.icons8-fridge-filled:before,
.icons8-front-gate-closed-filled:before,
.icons8-garage-closed-filled:before,
.icons8-garage-filled:before,
.icons8-geo-fence:before,
.icons8-happy:before,
.icons8-hashtag:before,
.icons8-heating-room-filled:before,
.icons8-high-price-filled:before,
.icons8-house:before,
.icons8-interior-filled:before,
.icons8-kitchen-floor-new:before,
.icons8-land-use-new:before,
.icons8-layers:before,
.icons8-leaky-home:before,
.icons8-legal-description:before,
.icons8-liquifaction:before,
.icons8-living-areas-new:before,
.icons8-log-cabin-filled:before,
.icons8-lot-position-filled:before,
.icons8-low-price-filled:before,
.icons8-mail-filter:before,
.icons8-mail-filter-2:before,
.icons8-mailbox-opened-flag-up-filled:before,
.icons8-maintenance-filled:before,
.icons8-maori-new:before,
.icons8-map-marker-filled:before,
.icons8-medium-priority-filled:before,
.icons8-money-filled:before,
.icons8-museum-filled:before,
.icons8-news-filled:before,
.icons8-oil-barrel:before,
.icons8-outlier-new:before,
.icons8-panorama:before,
.icons8-parking:before,
.icons8-paste-special-filled:before,
.icons8-pc-on-desk-filled:before,
.icons8-pencil:before,
.icons8-phone-daytime-filled:before,
.icons8-phone-night-filled:before,
.icons8-piping-filled:before,
.icons8-playground-filled:before,
.icons8-plug-4-filled:before,
.icons8-qv-logo-new:before,
.icons8-rating-filled:before,
.icons8-real-estate:before,
.icons8-roller-brush-filled:before,
.icons8-room-filled:before,
.icons8-sell-property-filled:before,
.icons8-shake-phone:before,
.icons8-shower-and-tub-filled:before,
.icons8-site-coverage-new:before,
.icons8-slip:before,
.icons8-small-business-filled:before,
.icons8-sofa-filled:before,
.icons8-solar-panel-filled:before,
.icons8-structural-filled:before,
.icons8-sun-lounger-filled:before,
.icons8-sun-rays:before,
.icons8-surface-filled:before,
.icons8-swimming-pool:before,
.icons8-temperature-inside-filled:before,
.icons8-todo-list-filled:before,
.icons8-toilet-bowl-filled:before,
.icons8-toilet-paper-filled:before,
.icons8-total-bathrooms-filled:before,
.icons8-traffic-jam-filled:before,
.icons8-transmission-tower:before,
.icons8-tsunami:before,
.icons8-view-module-filled:before,
.icons8-washing-machine-filled:before {
    display: inline-block;
    font-family: "QV-Monarch";
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    line-height: 1;
    text-decoration: inherit;
    text-rendering: optimizeLegibility;
    text-transform: none;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-smoothing: antialiased;
}

.icons8-air-conditioner-filled:before 		{ content: "\f100"; }
.icons8-apple:before 						{ content: "\f101"; }
.icons8-ask-question:before 				{ content: "\f102"; }
.icons8-basement-filled:before 				{ content: "\f103"; }
.icons8-bed-filled:before 					{ content: "\f104"; }
.icons8-bench-sink-filled:before 			{ content: "\f105"; }
.icons8-birthday-cake-filled:before 		{ content: "\f106"; }
.icons8-brake-warning-filled:before			{ content: "\f107"; }
.icons8-brick-wall-filled:before 			{ content: "\f108"; }
.icons8-bus-filled:before 					{ content: "\f109"; }
.icons8-calendar-filled:before 				{ content: "\f10a"; }
.icons8-carpet-filled:before 				{ content: "\f10b"; }
.icons8-category-filled:before 				{ content: "\f10c"; }
.icons8-checked-user-male:before 			{ content: "\f10d"; }
.icons8-clock:before 						{ content: "\f10e"; }
.icons8-closed-window-filled:before 		{ content: "\f10f"; }
.icons8-compass:before 						{ content: "\f110"; }
.icons8-contacts:before 					{ content: "\f111"; }
.icons8-cow-filled:before 					{ content: "\f112"; }
.icons8-crosshair-filled:before 			{ content: "\f113"; }
.icons8-curtains-filled:before 				{ content: "\f114"; }
.icons8-defensive-wood-wall-filled:before 	{ content: "\f115"; }
.icons8-dishwasher-filled:before 			{ content: "\f116"; }
.icons8-driveway-new:before 				{ content: "\f117"; }
.icons8-earthquake-symbol:before 			{ content: "\f118"; }
.icons8-edit-user-male:before 				{ content: "\f119"; }
.icons8-email:before 						{ content: "\f11a"; }
.icons8-field-filled:before 				{ content: "\f11b"; }
.icons8-fire-station-filled:before 			{ content: "\f11c"; }
.icons8-floods-filled:before 				{ content: "\f11d"; }
.icons8-floor-plan-filled:before 			{ content: "\f11e"; }
.icons8-forest-filled:before 				{ content: "\f11f"; }
.icons8-foundation-new:before 				{ content: "\f120"; }
.icons8-fridge-filled:before 				{ content: "\f121"; }
.icons8-front-gate-closed-filled:before 	{ content: "\f122"; }
.icons8-garage-closed-filled:before 		{ content: "\f123"; }
.icons8-garage-filled:before 				{ content: "\f124"; }
.icons8-geo-fence:before 					{ content: "\f125"; }
.icons8-happy:before 						{ content: "\f126"; }
.icons8-hashtag:before 						{ content: "\f127"; }
.icons8-heating-room-filled:before 			{ content: "\f128"; }
.icons8-high-price-filled:before 			{ content: "\f129"; }
.icons8-house:before 						{ content: "\f12a"; }
.icons8-interior-filled:before 				{ content: "\f12b"; }
.icons8-kitchen-floor-new:before 			{ content: "\f12c"; }
.icons8-land-use-new:before 				{ content: "\f12d"; }
.icons8-layers:before 						{ content: "\f12e"; }
.icons8-leaky-home:before 					{ content: "\f12f"; }
.icons8-legal-description:before 			{ content: "\f130"; }
.icons8-liquifaction:before 				{ content: "\f131"; }
.icons8-living-areas-new:before 			{ content: "\f132"; }
.icons8-log-cabin-filled:before 			{ content: "\f133"; }
.icons8-lot-position-filled:before 			{ content: "\f134"; }
.icons8-low-price-filled:before 			{ content: "\f135"; }
.icons8-mail-filter:before 					{ content: "\f136"; }
.icons8-mail-filter-2:before 				{ content: "\f137"; }
.icons8-mailbox-opened-flag-up-filled:before { content: "\f138"; }
.icons8-maintenance-filled:before 			{ content: "\f139"; }
.icons8-maori-new:before 					{ content: "\f13a"; }
.icons8-map-marker-filled:before 			{ content: "\f13b"; }
.icons8-medium-priority-filled:before 		{ content: "\f13c"; }
.icons8-money-filled:before 				{ content: "\f13d"; }
.icons8-museum-filled:before			 	{ content: "\f13e"; }
.icons8-news-filled:before 					{ content: "\f13f"; }
.icons8-oil-barrel:before 					{ content: "\f140"; }
.icons8-outlier-new:before 					{ content: "\f141"; }
.icons8-panorama:before 					{ content: "\f142"; }
.icons8-parking:before 						{ content: "\f143"; }
.icons8-paste-special-filled:before 		{ content: "\f144"; }
.icons8-pc-on-desk-filled:before 			{ content: "\f145"; }
.icons8-pencil:before 						{ content: "\f146"; }
.icons8-phone-daytime-filled:before 		{ content: "\f147"; }
.icons8-phone-night-filled:before 			{ content: "\f148"; }
.icons8-piping-filled:before 				{ content: "\f149"; }
.icons8-playground-filled:before 			{ content: "\f14a"; }
.icons8-plug-4-filled:before 				{ content: "\f14b"; }
.icons8-qv-logo-new:before 					{ content: "\f14c"; }
.icons8-rating-filled:before 				{ content: "\f14d"; }
.icons8-real-estate:before 					{ content: "\f14e"; }
.icons8-roller-brush-filled:before			{ content: "\f14f"; }
.icons8-room-filled:before 					{ content: "\f150"; }
.icons8-sell-property-filled:before 		{ content: "\f151"; }
.icons8-shake-phone:before 					{ content: "\f152"; }
.icons8-shower-and-tub-filled:before 		{ content: "\f153"; }
.icons8-site-coverage-new:before 			{ content: "\f154"; }
.icons8-slip:before 						{ content: "\f155"; }
.icons8-small-business-filled:before 		{ content: "\f156"; }
.icons8-sofa-filled:before 					{ content: "\f157"; }
.icons8-solar-panel-filled:before 			{ content: "\f158"; }
.icons8-structural-filled:before 			{ content: "\f159"; }
.icons8-sun-lounger-filled:before 			{ content: "\f15a"; }
.icons8-sun-rays:before 					{ content: "\f15b"; }
.icons8-surface-filled:before 				{ content: "\f15c"; }
.icons8-swimming-pool:before 				{ content: "\f15d"; }
.icons8-temperature-inside-filled:before 	{ content: "\f15e"; }
.icons8-todo-list-filled:before 			{ content: "\f15f"; }
.icons8-toilet-bowl-filled:before 			{ content: "\f160"; }
.icons8-toilet-paper-filled:before 			{ content: "\f161"; }
.icons8-total-bathrooms-filled:before 		{ content: "\f162"; }
.icons8-traffic-jam-filled:before 			{ content: "\f163"; }
.icons8-transmission-tower:before 			{ content: "\f164"; }
.icons8-tsunami:before 						{ content: "\f165"; }
.icons8-view-module-filled:before 			{ content: "\f166"; }
.icons8-washing-machine-filled:before		{ content: "\f167"; }

.mobileComps-linker { display:none; }

.md-photoGallery img.md-primary {
    display:inline-block;
    height:30rem;
    cursor: pointer;
}

@media only screen and (max-width:480px) {

    .valjobRow .colCell.address { position:relative; }

    .mobileComps-linker {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
    }

}

.icons8-house-filled:before {
    display: inline-block;
    font-family: "QV-Monarch";
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    line-height: 1;
    text-decoration: inherit;
    text-rendering: optimizeLegibility;
    text-transform: none;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-smoothing: antialiased;
}
.icons8-house-filled:before { content: "\f12a"; }

.grey-border{
    border: solid 1px #d2d2d2;
}

.button-red-text{
    color: red !important;
    padding: 0 5px;
    font-size: 1.3rem
}

.sa-footer-wrapper {
    position: fixed;
    height: 82px;
    width: 100%;
    bottom: 0;
    z-index: 1000;
}

.sa-footer-margin {
    padding-top: 5rem;
    height: 82px;
}

/*  ==================================================================================================== */
/*  END LARGE DEVICES, WIDES SCREENS (MIN-WIDTH:993PX)  ================================================ */
/*  ==================================================================================================== */
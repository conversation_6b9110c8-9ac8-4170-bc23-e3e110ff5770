@media only screen and (max-width:480px) {

/*  ==================================================================================================== */
/*  START EXTRA SMALL DEVICES, PHONES (MAX-WIDTH:480PX)  =============================================== */
/*  ==================================================================================================== */

.qvToolbar-links li { width:50%; }

/* ---------------------------------------------------------------- */

.resultsRow.valjobRow {height: 18.5rem;}

/* ---------------------------------------------------------------- */

.resultsRow.valjobRow .colCell.inspectiontime		{top: 9.5rem;left: 9rem;width: calc(50% - 5.5rem);}
.resultsRow.valjobRow .colCell.jobdue 		 		{top: 9.5rem;left: calc(50% + 4.75rem);width: calc(50% - 5.5rem);}
.resultsRow.valjobRow .colCell.assignedvaluer 		{left: 9rem;width: calc(50% - 5.5rem);}
.resultsRow.valjobRow .colCell.countersignedvaluer 	{left: calc(50% + 4.75rem);width: calc(50% - 5.5rem);}
.resultsRow.valjobRow .colCell.jobstatus 			{ bottom:.75rem; left:.5rem; font-size:.9rem; padding-left:.5rem; width:7.5rem; }

/* ---------------------------------------------------------------- */

.resultsRow.valjobRow .fullAddress {
    top: .75rem;
    left: .75rem;
    font-size: 1.6rem;
}

.resultsRow.valjobRow .primaryThumb-Wrapper { 
	bottom:1.5rem;
	left:.5rem;
	width: 7.5rem;
    height: 7.5rem;
}

.resultsRow.valjobRow .colCell.valref {
    top: 6rem;
    left: .5rem;
    line-height: 2.2;
    margin:0;
    width: calc(100% - 1rem);
}

.resultsRow.valjobRow .colCell.reporttype {
    top: 6.1rem;
    right: 1rem;
    line-height: 2.2;
}

.primaryThumb-Wrapper img[src="assets/images/property/addPhotos.png"] {
    margin: .6rem 0 0 1.25rem;
    width: 5rem;
    height: 5rem;
}

	
/*  ==================================================================================================== */
/*  END EXTRA SMALL DEVICES, PHONES (MAX-WIDTH:480PX)  ================================================= */
/*  ==================================================================================================== */

}
		
		/* ---------------------------------------------------------------- */

		.editDetails-wrapper {
			display:grid;
			grid-template-columns: auto;
			grid-template-rows:auto;
			grid-gap:0;
			padding:4rem 0;
			margin:5.3rem auto 0;
			max-width:136.6rem; 
		}
			
		.editDetails-wrapper .tabs	  { width:100%; }

		.editDetails-wrapper .tabs li { width:13%; }
		
		/* ---------------------------------------------------------------- */
		
		.editDetails-item { 
			display:grid;
			grid-template-columns: repeat(12, 1fr);
			grid-template-rows:auto;
			grid-gap:3rem;
			align-items:start;
			color:var(--blue-medium);
			background-color:var(--grey-ultralight);
			padding:3rem 2rem;
    		border-bottom:.1rem dashed var(--grey-lighter);
		}
		
		.editDetails-item:last-of-type { border-bottom:none; }
		
		.editDetails-item li { 
			justify-content: stretch; 
			position:relative; 
		} 
				
		.editDetails-item i 		{ display:inline-block; font-size:2rem; color:var(--blue-light);	}
		.editDetails-item li label 	{ font-size:1.1rem; margin-bottom:.5rem; }
		.editDetails-item li div 	{ display: inline-block; vertical-align: top; margin-left:.5rem; width: calc(100% - 3.4rem); }
		
		.editDetails-item li.Add_Remove					{ grid-column: span 2; text-align:right; }
		.editDetails-item li.Add_Remove	i				{ font-size:3.2rem; margin:0 .5rem; }
		.editDetails-item li.Add_Remove i:first-of-type { color:var(--down-red); }
		.editDetails-item li.Add_Remove i:last-of-type 	{ color:var(--blue-bright); }
		.editDetails-item li.Add_Remove label 			{ display:none;   }
		
		
		/* -- OVERVIEW ---------------------------------------------------- */
		
		.Bathrooms,
		.Bedrooms,
		.Garaging,
		.Living_Areas_Overview,
		.Offstreet_Parking,
		.Toilets { 
			grid-column: span 2; 
		}
		
		.Year_Built,
		.Exterior_Cladding,
		.Foundation,
		.Joinery,
		.Roof_Construction,
		.Roof_Style,
		.Spouting,
		.Total_Floor_Area { 
			grid-column: span 4; 
		}

		.External_Condition,
		.Immediate_Maintenance_Required,
		.Internal_Condition,
		.Legal_Descriptions,
		.Maintenance_Required,
		.Quality_of_External_Presentation,
		.Quality_of_Internal_Presentation,
		.Standard_of_Accomodation { 
			grid-column: span 6; 
		}
		
		.House_Type,
		.Total_Floor_Area_Description { 
			grid-column: span 8; 
		}
				
		.Code_Compliance,
		.Overview_Notes,
		.Recent_Alterations {
			grid-column: span 12; 
		}
		
		/* -- INTERIOR ---------------------------------------------------- */
		
		.Alternative_Energy,
		.Double_Glazing,
		.Heating_Type,
		.Insulation,
		.Living_Areas_Interior,
		.Plumbing_Age,
		.Redecoration_Age,
		.Ventilation,
		.Wiring_Age { 
			grid-column: span 3; 
		}
		
		.Floors,
		.Internal_Linings,
		.Living_Areas_Description { 
			grid-column: span 6; 
		}
		
		.Living_Areas_Description { 
			grid-column: span 9; 
		}
		
		.Chattels,
		.Interior_Notes,
		.Laundry,
		.Other_Features {
			grid-column: span 12; 
		}
		
		.interior .editDetails-item li.Add_Remove { 
			grid-area: auto / 11 / span 1 / span 2; 
		}
		
		/* -- BEDROOMS ---------------------------------------------------- */

		.Bedroom_Type { 
			grid-column: span 3; 
		}
		
		.Bedroom_Description { 
			grid-column: span 9; 
		}
		
		.Bedroom_Notes { 
			grid-column: span 12; 
		}
		
		.bedrooms .editDetails-item li.Add_Remove { 
			grid-area: auto / 11 / span 1 / span 2; 
		}
		
		/* -- KITCHEN ---------------------------------------------------- */

		.Appliances,
		.Bench_and_Sink,
		.Kitchen_Age,
		.Kitchen_Floor,
		.Kitchen_Layout,
		.Kitchen_Quality { 
			grid-column: span 4; 
		}
		
		.Kitchen_Notes { 
			grid-column: span 12; 
		}
		
		/* -- BATHROOMS -------------------------------------------------- */

		.Bathroom_or_Toilet,
		.Ensuite,
		.Ensuite_Age,
		.Ensuite_Description,
		.Ensuite_Quality,
		.Main_Bathroom,
		.Main_Bathroom_Age,
		.Main_Bathroom_Description,
		.Main_Bathroom_Quality { 
			grid-column: span 3; 
		}
		
		.Bathroom_or_Toilet_Description { 
			grid-column: span 9; 
		}
		
		.Bathroom_Notes { 
			grid-column: span 12; 
		}
		
		.bathrooms .editDetails-item li.Add_Remove { 
			grid-area: auto / 11 / span 1 / span 2; 
		}
		
		/* -- GARAGING --------------------------------------------------- */

		.Garage_Type,
		.Other_Buildings { 
			grid-column: span 3; 
		}
		
		.Garage_Age,
		.Other_Buildings_Age { 
			grid-area: 2 / 4 / span 1 / span 3; 
		}
		
		.Garage_Floor_Area,
		.Other_Buildings_Floor_Area { 
			grid-area: 2 / 7 / span 1 / span 3; 
		}
		
		.Garage_Modernisation,
		.Other_Buildings_Modernisation { 
			grid-area: 2 / 10 / span 1 / span 3; 
		}
		
		.Garage_Exterior_Cladding,
		.Other_Buildings_Exterior_Cladding { 
			grid-area: 3 / 4 / span 1 / span 3; 
		}
				
		.Garage_Roof_Construction,
		.Other_Buildings_Roof_Construction { 
			grid-area: 3 / 7 / span 1 / span 3; 
		}
				
		.Garage_Foundation,
		.Other_Buildings_Foundation { 
			grid-area: 3 / 10 / span 1 / span 3; 
		}
		
		.Garage_Description,
		.Other_Buildings_Description { 
			grid-column: span 9; 
		}
		
		.Garage_Notes,
		.Other_Buildings_Notes { 
			grid-area: auto / 4 / span 1 / span 9; 
		}
		
		.garaging .editDetails-item li.Add_Remove { 
			grid-area: auto / 11 / span 1 / span 2; 
		}
		
		/* -- IMPROVEMENTS ------------------------------------------------ */

		.Major_Site_Improvements { 
			grid-column: span 3; 
		}
		
		.Driveway,
		.Fencing,
		.Landscaping { 
			grid-column: span 4; 
		}
		
		.Major_Site_Improvements_Description { 
			grid-column: span 9; 
		}
		
		.Improvements_Notes,
		.Minor_Site_Improvements { 
			grid-column: span 12; 
		}
		
		.improvements .editDetails-item li.Add_Remove { 
			grid-area: auto / 11 / span 1 / span 2; 
		}

		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:1220px) {
		
			.editDetails-wrapper { padding: 1.2rem 0; }
			
			.editDetails-item { grid-gap:3rem 1rem; }
			
			/* -- INTERIOR ---------------------------------------------------- */
			
			.Bathrooms,
			.Bedrooms,
			.Garaging,
			.Living_Areas_Overview,
			.Offstreet_Parking,
			.Toilets { 
				grid-column: span 4; 
			}
		
			.Living_Areas_Description { 
				grid-column: span 6; 
			}
		
			.Alternative_Energy,
			.Double_Glazing,
			.Floors,
			.Heating_Type,
			.Insulation,
			.Internal_Linings,
			.Plumbing_Age,
			.Redecoration_Age,
			.Ventilation,
			.Wiring_Age { 
				grid-column: span 6; 
			}
			
			.Chattels,
			.House_Type,
			.Interior_Notes,
			.Laundry,
			.Other_Features,
			.Total_Floor_Area_Description {
				grid-column: span 12; 
			}
			
			/* -- GARAGING --------------------------------------------------- */

			.Garage_Age,
			.Garage_Exterior_Cladding,
			.Garage_Floor_Area,
			.Garage_Foundation,
			.Garage_Modernisation,
			.Garage_Roof_Construction,
			.Garage_Type,
			.Other_Buildings,
			.Other_Buildings_Age,
			.Other_Buildings_Exterior_Cladding,
			.Other_Buildings_Floor_Area,
			.Other_Buildings_Foundation,
			.Other_Buildings_Modernisation,
			.Other_Buildings_Roof_Construction { 
				grid-area: initial; 
				grid-column: span 4; 
			}
		
			.Garage_Description,
			.Other_Buildings_Description { 
				grid-area: initial; 
				grid-column: span 8; 
			}
	
			.Garage_Notes,
			.Other_Buildings_Notes { 
				grid-area: initial; 
				grid-column: span 12;
			}
			
		/* ---------------------------------------------------------------- */
			
		@media only screen and (max-width:768px) { 

			.editDetails-wrapper { padding:1.2rem 0rem; }
			
			.editDetails-wrapper .tabs	  { width:92.5rem; padding: 0;}
			.editDetails-wrapper .tabs li { width:12rem; }
												
			.editDetails-item { 
				border:none;
				margin-bottom:1.6rem;
				box-shadow:inset 0 0 0 .1rem var(--grey-lighter);
			}
			
			.editDetails-item li.Add_Remove 	  { grid-column: span 12; text-align: center; background-color: var(--grey-lightest); padding-top:.8rem; border-top: 1px solid var(--grey-lighter); margin: 0 -.9rem -2.9rem; }
			.editDetails-item li.Add_Remove label { display:none; }
			
			/* -- OVERVIEW ---------------------------------------------------- */
		
			.Bathrooms,
			.Bedrooms,
			.Garaging,
			.Living_Areas_Overview,
			.Offstreet_Parking,
			.Toilets { 
				grid-column: span 4; 
			}
			
			.Total_Floor_Area,
			.Year_Built { 
				grid-column: span 6; 
			}

			.Code_Compliance,
      .Recent_Alterations,
			.Exterior_Cladding,
			.External_Condition,
			.Foundation,
			.House_Type,
			.Immediate_Maintenance_Required,
			.Internal_Condition,
			.Joinery,
			.Legal_Descriptions,
			.Maintenance_Required,
			.Overview_Notes,
			.Quality_of_External_Presentation,
			.Quality_of_Internal_Presentation,
			.Roof_Construction,
			.Roof_Style,
			.Spouting,
			.Standard_of_Accomodation,
			.Total_Floor_Area_Description { 
				grid-column: span 12; 
			}
			
			/* -- INTERIOR ---------------------------------------------------- */
		
			.Plumbing_Age,	
			.Redecoration_Age,	
			.Ventilation,	
			.Wiring_Age { 
				grid-column: span 6; 
			}
			
			.Alternative_Energy,	
			.Chattels,	
			.Double_Glazing,	
			.Floors,	
			.Heating_Type,	
			.Insulation,	
			.Interior_Notes,	
			.Internal_Linings,	
			.Laundry,	
			.Living_Areas_Description,	
			.Living_Areas_Interior,	
			.Other_Features {
				grid-column: span 12; 
			}
			
			.interior .editDetails-item li.Add_Remove { 
				grid-area: initial; 
				grid-column: span 12; 
			}
			
			/* -- BEDROOMS ---------------------------------------------------- */

			.Bedroom_Description,
			.Bedroom_Notes,
			.Bedroom_Type { 
				grid-column: span 12; 
			}
			
			.bedrooms .editDetails-item li.Add_Remove { 
				grid-area: initial; 
				grid-column: span 12; 
			}
			
			/* -- KITCHEN ---------------------------------------------------- */

			.Kitchen_Age,
			.Kitchen_Quality { 
				grid-column: span 6; 
			}
			
			.Appliances,	
			.Bench_and_Sink,	
			.Kitchen_Floor,	
			.Kitchen_Layout { 
				grid-column: span 12; 
			}
			
			/* -- BATHROOMS -------------------------------------------------- */

			.Ensuite_Age,	
			.Ensuite_Quality,	
			.Main_Bathroom_Age,	
			.Main_Bathroom_Quality { 
				grid-column: span 6; 
			}
			
			.Bathroom_or_Toilet,	
			.Bathroom_or_Toilet_Description,
			.Ensuite,	
			.Ensuite_Description,	
			.Main_Bathroom,	
			.Main_Bathroom_Description { 
				grid-column: span 12; 
			}	
			
			.bathrooms .editDetails-item li.Add_Remove { 
				grid-area: initial; 
				grid-column: span 12; 
			}
			
			/* -- GARAGING --------------------------------------------------- */

			.Garage_Age,	
			.Garage_Floor_Area,	
			.Garage_Modernisation,	
			.Other_Buildings_Age,	
			.Other_Buildings_Floor_Area,	
			.Other_Buildings_Modernisation { 
				grid-column: span 6; 
			}
			
			.Garage_Description,	
			.Garage_Exterior_Cladding,	
			.Garage_Foundation,	
			.Garage_Roof_Construction,	
			.Garage_Type,	
			.Other_Buildings,	
			.Other_Buildings_Description,	
			.Other_Buildings_Exterior_Cladding,	
			.Other_Buildings_Foundation,	
			.Other_Buildings_Roof_Construction { 
				grid-column: span 12; 
			}
			
			.garaging .editDetails-item li.Add_Remove { 
				grid-area: initial; 
				grid-column: span 12; 
			}
			
			/* -- Interiors -------------------------------------------------- */
		
			.Driveway,	
			.Fencing,	
			.Improvements_Notes,	
			.Landscaping,	
			.Major_Site_Improvements,	
			.Major_Site_Improvements_Description,	
			.Minor_Site_Improvements { 
				grid-column: span 12; 
			}
		
			.improvements .editDetails-item li.Add_Remove { 
				grid-area: initial; 
				grid-column: span 12; 
			}
						
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:600px) { }
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:480px) {
		
			.editDetails-item { padding:3rem 1rem; }
			
			/* -- OVERVIEW ---------------------------------------------------- */
			
			.Bathrooms,
			.Bedrooms,
			.Garaging,
			.Living_Areas_Overview,
			.Offstreet_Parking,
			.Toilets,
			.Total_Floor_Area,
			.Year_Built{ 
				grid-column: span 6; 
			}
			
			.Code_Compliance,
      .Recent_Alterations,
			.Exterior_Cladding,	
			.External_Condition,	
			.Foundation,	
			.House_Type,	
			.Immediate_Maintenance_Required,	
			.Internal_Condition,	
			.Joinery,	
			.Legal_Descriptions,	
			.Maintenance_Required,	
			.Overview_Notes,	
			.Quality_of_External_Presentation,	
			.Quality_of_Internal_Presentation,	
			.Roof_Construction,	.Roof_Style,	
			.Spouting,	
			.Standard_of_Accomodation,	
			.Total_Floor_Area_Description,
			 { 
				grid-column: span 12; 
			}
			
			/* -- INTERIOR ---------------------------------------------------- */
		
			.Plumbing_Age,
			.Redecoration_Age
			.Ventilation,
			.Wiring_Age
			 {
				grid-column: span 6; 
			}
			
			.Alternative_Energy,
			.Chattels,
			.Double_Glazing,
			.Floors,
			.Heating_Type,
			.Insulation,
			.Interior_Notes,
			.Internal_Linings,
			.Laundry,
			.Living_Areas_Description,
			.Living_Areas_Interior,
			.Other_Features {
				grid-column: span 12; 
			}
			
			/* -- BEDROOMS ---------------------------------------------------- */
		
			.Bedroom_Description,
			.Bedroom_Notes,
			.Bedroom_Type { 
				grid-column: span 12; 
			}
			
			/* -- KITCHEN ---------------------------------------------------- */

			.Kitchen_Age,
			.Kitchen_Quality { 
				grid-column: span 6; 
			}
			
			/* -- BATHROOMS -------------------------------------------------- */

			.Ensuite_Age,
			.Ensuite_Quality,
			.Main_Bathroom_Age,
			.Main_Bathroom_Quality { 
				grid-column: span 6; 
			}
			
			.Ensuite,
			.Ensuite_Description,
			.Main_Bathroom,
			.Main_Bathroom_Description { 
				grid-column: span 12; 
			}
			
			/* -- GARAGING --------------------------------------------------- */

			.Garage_Age,
			.Garage_Floor_Area,
			.Garage_Modernisation,
			.Other_Buildings_Age,
			.Other_Buildings_Floor_Area,			
			.Other_Buildings_Modernisation { 
				grid-column: span 6; 
			}

			.Garage_Description,
			.Garage_Type,
			.Other_Buildingse,
			.Other_Buildings_Description { 
				grid-column: span 12; 
			}
			
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:374px) { 	}
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
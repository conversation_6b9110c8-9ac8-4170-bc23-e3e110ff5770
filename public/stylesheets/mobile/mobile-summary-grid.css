
		/* ---------------------------------------------------------------- */

		.summary-wrapper {
			display:grid;
			grid-gap: 0;
			grid-template-columns:minmax(46rem, 58.5rem) 45rem minmax(29rem, 38.1rem);
			grid-template-rows:4.25rem 6.75rem 10rem 9rem 5rem auto;
			color:#fff;
			background-color: var(--blue-dark);
			margin:4rem auto 1rem;
			max-width:136.6rem;
		}
		
		.summary-wrapper label 				{ color: var(--grey-light); }  
		.summary-wrapper .hrLabel label 	{ display:block; border-bottom-color: var(--grey-dark); }
				
		/* ---------------------------------------------------------------- */

		.h2 {
			display: grid;
			grid-area: 2 / 1 / span 1 / span 1;
			align-items:start;
			font-size: 2.4rem;
    		font-weight: 400;
			background-color: var(--blue-light);
			padding: 1rem;
		}
		
		.h2 span { display: block; font-size: 1.3rem; font-weight: 300; }
		
		/* ---------------------------------------------------------------- */

		.propertyCategory {
			display: grid;
			grid-area: 1 / 1 / span 1 / span 1;
			grid-template-columns: 1fr;
			grid-gap: 0;
			align-items: center;
			font-size: 1.1rem;
			font-style: italic;
			padding: .5rem 1rem;
		}
						
		.propertyCategory strong { font-weight:600; font-style:normal; }
				
		/* ---------------------------------------------------------------- */
		
		.propertyValref {
			display: grid;
			grid-area:1 / 1 / span 1 / span 1;
			grid-template-columns: 1fr;
			font-size: 1.1rem;
			font-weight: 600;
			font-style:italic;
			color: #fff;
			text-align: right;
			background: var(--blue-dark);
			padding: .5rem 1rem;
		}
			
		/* ---------------------------------------------------------------- */

		.propertySummary {
			display: grid;
			grid-area: 3 / 1 / span 1 / span 1;
			grid-template-columns:2fr 3fr;
			align-items:center;
			font-size: 1.2rem;
			color: #fff;
			line-height: 1.6;
			background-color: var(--blue-light);
			padding: 0 1rem;
		}
				
		.propertySummary label { font-size:1rem; font-weight:300; color:#fff; min-width:9em; }
    	    	
 		/* ---------------------------------------------------------------- */
   	
		.propertySale-date {
			display:grid;
			grid-area: 3 / 3 / span 1 / span 1;
			grid-template-columns:2fr 1fr;
			grid-gap:0 1.5rem;
			align-items:end;
			font-size: 1.1rem;
			font-weight: 600;
			font-style: italic;
			line-height: 2;
			margin:0 1.25rem;
			height:6rem;
		}
		
		.propertySale-date li { grid-column: span 1; }
    	
    	.propertySale-date li span {
			display:block;
			font-size: 1rem;
			font-weight: 400;
			font-style: normal;
			text-align: center;
			border-radius: 1.1rem;
			margin-bottom: 0.2rem;
		}
		
		.propertySale-date li .saleClass {
			display: inline-block;
			font-size: 1rem;
			font-style: normal;
			font-weight: normal;
			text-align: center;
			line-height: 2.5;
			color: #fff;
			background-color: var(--grey-dark);
			border-radius: 50%;
			margin-left: 1rem;
			width: 2.5rem;
			height: 2.5rem;
		}
		
		.confirmedSale span { background: var(--blue-light); }
		.pendingSale span { color:var(--sale-brown); background-color: var(--sale-yellow); }
		.unconfirmedSale span { background-color: var(--sale-red); }
		
		/* ---------------------------------------------------------------- */
		
		.propertySale-details {
			display:grid;
			grid-area: 3 / 3 / span 2 / span 1;
			grid-template-columns:repeat(3, 1fr);
			grid-gap:1rem 1.5rem;
			align-items:end;
			margin:7rem 1.25rem 0 1.25rem;
			height:calc(100% - 7rem);
		}
		
		.propertySale-details li { grid-column: span 1; }
		
		/* ---------------------------------------------------------------- */
		
		.propertyEstimates {
			display:grid;
			grid-area: 1 / 3 / span 2 / span 1;
			align-content: space-evenly;
		    background-color: var(--blue-medium);
		    padding:.5rem 1rem;
		    margin:1.25rem 1.25rem 0 1.25rem;
		    height:calc(100% - 1.25rem);
		}
		
		.propertyEstimates label { font-style:italic; }
		.propertyEstimates strong { color:#fff; }

		.propertyEstimates span {
			position: relative;
			bottom:.3rem;
			font-size: 1.35rem;
			line-height:1.9;
			text-align:right;
			padding:0 .5rem;
			width:6rem;
			float: right;
		}

		.propertyEstimates span::after {
			position: absolute;
			top:-.1rem;
			left: -11rem;
			content: "Compared to Currrent Capital Value";
			font-size: .9rem;
			line-height:initial;
			width: 10rem;
		}
		
		@media only screen and (max-width:1300px) {
		
			.propertyEstimates span::after { left: -8rem; content: "Compared to Currrent CV"; width: 7rem; }
			
		}
		
		/* ---------------------------------------------------------------- */

		.propertyValues {
			display:grid;
			grid-area: 4 / 1 / span 1 / span 1;
			grid-template-columns:repeat(4, 1fr);
			grid-gap: 1.5rem;
			align-items:end;
			font-size: 1.8rem;
			color:#fff;
			background-color: var(--blue-medium);
			padding:0 1rem 1rem;
		}
		
		.propertyValues li { grid-column: span 1; }
		
		/* ---------------------------------------------------------------- */
		
		.propertyPhotos {
			display:grid;
			grid-area: 1 / 2 / span 4/ span 1;
			padding:0;
		}
		
		.propertyPhotos img { min-height:30rem; }
		
		/* ---------------------------------------------------------------- */

		.slick-dotted.slick-slider { margin: 0; }

		.slick-prev, .slick-next { 
			top: initial;
			bottom: -4.8rem;
			box-shadow: 0 0 0 1px var(--grey-dark);
			width: 2.5rem;
			height: 2.5rem;
			z-index: 1;
			transition: 0.2s linear all;
    	}
    	.slick-prev	{ left:0; }
    	.slick-next { right:0; }
    	
    	.slick-prev::before, .slick-next::before { font-family:"Material Icons"; color:var(--grey-light); opacity:1; }
    	.slick-prev::before	{ content:"\E314"; }
    	.slick-next::before	{ content:"\E315"; }
    	
    	.slick-prev:hover, .slick-next:hover { background-color: #fff; box-shadow: 0 0 0 1px #fff; }
    	.slick-prev:hover::before, .slick-next:hover::before { color:var(--blue-bright); }
    
		.slick-dots { 
			display:table;
			top:initial; 
			bottom: 0; 
			line-height: 0; 
    		margin: 0 5rem;
    		width: calc(100% - 10rem);
		}
		
		.slick-dots li { 
			position: relative;
			display: table-cell;
			text-align: center;
			vertical-align: middle;
			margin: 0;
			padding: 0;
			cursor: pointer;
			min-width: 5%;
			width: auto;
			height: 100%; 
    	}
		
		.slick-dots li button { height:2.5rem; width:100%; }

		.slick-dots li button:before {
			content: "";
			font-size: 1.4rem;
			background-color: var(--grey-dark);
			line-height: 2;
			opacity: 1;
			height: .1rem;
			width: 100%;
			top: initial;
		}

		.slick-dots li.slick-active button:before { background-color: var(--blue-vivid); height: .2rem; }
		
		.slick-slide {
			position: relative;
    		margin: 0;
		}
		
		.galleryCaption {
			display: block;
			position: absolute;
			bottom: 0;
			left: 0;
			font-size: 1.35rem;
			font-weight: 300;
			color: rgba(255,255,255,.9);
			text-shadow: 0.1rem 0.1rem rgba(0,0,0,.5);
			background: linear-gradient(to right, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 40%);
			padding: 1.2rem 1.5rem;
			width: 100%;
			box-sizing: border-box;
		}
		
		/* ---------------------------------------------------------------- */

		.propertyData {
			display:grid;
			grid-area: 6 / 1 / span 1 / span 3;
			grid-template-columns:repeat(12, 1fr);
			grid-gap: .4rem;
			font-size: 1.15rem;
			color:#fff;
			background-color:var(--blue-dark);
			padding:1rem;
			min-width:auto;
		}
		
		.propertyData li { 
			grid-column: span 3;
			position:relative;
			line-height:1.5;
			background-color: var(--grey-dark);
			padding:.5rem 1rem .5rem 4rem;
		}
		
		.propertyData li:nth-child(n+13) { grid-column: span 4; }
		
		.propertyData li::before {
			position:absolute;
			top:calc(50% - .9rem);
			left:1rem;
			font-size: 1.8rem;
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:1220px) {
		
			.summary-wrapper { 
				grid-template-columns:1fr 1fr;
				grid-template-rows:repeat(5, auto) 16rem 9.5rem 16rem auto;
				grid-column-gap:2rem;
				margin-top:0;
				box-shadow: 0 0 0 .1rem var(--grey-lightest);
			}
			
			.propertySummary { 
				grid-area: 4 / 1 / span 1 / span 2; 
				grid-template-columns:17.5rem 1fr;
				grid-gap: 2rem;
				padding:1.5rem 1rem;
			}
			
			.h2						{ grid-area: 1 / 1 / span 1 / span 2; color: var(--blue-dark); background-color: #fff; padding:1.25rem 1rem 1rem; border-bottom:1px solid var(--grey-mercury); }
			.propertyValref 		{ grid-area: 2 / 1 / span 1 / span 2; font-size:1.1rem; color:var(--blue-dark); line-height:1.6; text-align: left; background-color: #fff; padding: .5rem 1rem; border-top: .1rem solid var(--grey-lightest); border-bottom: .1rem solid var(--grey-lightest); }
			.propertyCategory		{ grid-area: 3 / 1 / span 1 / span 2; grid-template-columns:initial; color: var(--blue-dark);  line-height:1.6; background-color: #fff; padding:1rem 1rem 1.5rem; }
			.propertySummary		{ grid-area: 4 / 1 / span 1 / span 2; grid-template-columns:17.5rem 1fr; grid-gap: 2rem; padding:1.5rem 1rem; }
			.propertySale-date 		{ grid-area: 5 / 1 / span 1 / span 1; grid-template-columns:repeat(6, 1fr); padding:2rem 0; margin:0 0 0 1rem; height:auto; }
			.propertySale-details 	{ grid-area: 6 / 1 / span 1 / span 1; align-items: center; padding:2rem 0 2rem 1rem; margin:0; height:auto; }
			.propertyPhotos 		{ grid-area: 5 / 2 / span 4 / span 1; padding:2rem 1rem 0 0 ; }
			.propertyEstimates 		{ grid-area: 7 / 1 / span 1 / span 1; padding:.5rem .8rem; margin:0 0 0 1rem; height:auto;}
			.propertyValues 		{ grid-area: 8 / 1 / span 1 / span 1; align-items:start; background-color: var(--blue-dark); padding:2rem 0 0 1rem; }
			.propertyData 			{ grid-area: 9 / 1 / span 1 / span 2; }
			
			.propertyValref li { font-weight:600; }
			.propertyValref label { font-size:1rem; font-weight:400; color: var(--blue-dark); }
			.propertyCategory li:last-of-type { text-align:left; }
			.propertyCategory label { font-size: 1rem; color: var(--blue-dark); }
			.propertySale-date li { grid-column: span 4; }
			.propertySale-date li:last-of-type { grid-column: span 2; }
			.propertyPhotos img { height:38rem; }
			.slick-prev, .slick-next { bottom:2rem; }
			.slick-next { right: 1rem; }
			.propertyEstimates span::after {	left: -11rem; content: "Compared to Currrent Capital Value"; width: 10rem; }
		
		}

		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:768px) {
		
			.summary-wrapper { 
				grid-template-columns: 1fr;
				grid-template-rows: auto;
				grid-auto-rows: auto;
				grid-gap: initial;
    		}
			
			.h2						{ grid-area: 1 / 1 / span 1 / span 1; }
			.propertyValref 		{ grid-area: 2 / 1 / span 1 / span 1; }
			.propertyCategory		{ grid-area: 3 / 1 / span 1 / span 1; }
			.propertySummary		{ grid-area: 4 / 1 / span 1 / span 1; }
			.propertySale-date 		{ grid-area: 5 / 1 / span 1 / span 1; grid-template-columns:2fr 1fr; padding:2rem 1rem 1rem 0; }
			.propertySale-date li:last-of-type { grid-column: initial }
			.propertySale-details 	{ grid-area: 6 / 1 / span 1 / span 1; padding:2rem 1rem; }
			.propertyEstimates 		{ grid-area: 7 / 1 / span 1 / span 1; padding-bottom:.2rem; margin:0 1rem; }
			.propertyValues 		{ grid-area: 9 / 1 / span 1 / span 1; padding:3rem 1rem 2rem; }
			.propertyPhotos 		{ grid-area: 8 / 1 / span 1 / span 1; padding:2rem 0 3.5rem; }
			.propertyData 			{ grid-area: 10 / 1 / span 1 / span 1; }
			
			.propertySale-date li { grid-column: span 1; }
			.propertyEstimates.roundBox li { padding:.4rem 0; }
			.propertyPhotos img { min-height:auto; height:auto; }
			.slick-prev, .slick-next { bottom:-1.6rem; top:initial; }
			.slick-prev { left: 1rem; }
								
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:600px) {
		
			.propertyValref 		{ grid-area: 3 / 1 / span 1 / span 1; background-color:#fff; text-align: left; padding:.5rem 1rem; border-top:.1rem solid var(--grey-lightest); border-bottom:.1rem solid var(--grey-lightest); grid-gap:0; }
			.propertyCategory		{ grid-area: 4 / 1 / span 1 / span 1; padding:.5rem 1rem 1rem; }
			.propertySummary		{ grid-area: 5 / 1 / span 1 / span 1; grid-template-columns: 1fr; grid-gap:.1rem; }
			.propertySale-date 		{ grid-area: 6 / 1 / span 1 / span 1;  }
			.propertySale-details 	{ grid-area: 7 / 1 / span 1 / span 1;  }
			.propertyEstimates 		{ grid-area: 8 / 1 / span 1 / span 1;  }
			.propertyValues 		{ grid-area: 10 / 1 / span 1 / span 1; }
			.propertyPhotos 		{ grid-area: 9 / 1 / span 1 / span 1;  }
			.propertyData 			{ grid-area: 11 / 1 / span 1 / span 1; }
			
			.propertyData li { display:flex; align-items:center; }
			.propertyData li:nth-child(n+13) { grid-column: span 6; }
			.slick-dots { margin: 0 1rem; width: calc(100% - 2rem); }
			.slick-prev, .slick-next { display:none !important; }
			
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:480px) {
			
			.propertySale-date { grid-template-columns:1fr 1fr; }

			.propertyEstimates span::after { left: -8rem; content: "Compared to Currrent CV"; width: 7rem; }
			.propertyData li { grid-column: span 4; }
			
			.propertySale-date li .saleClass {
				font-size: 0.9rem;
				width: 2.3rem;
				height: 2.3rem;
			}
			
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:374px) { }
		
		
		/* ---------------------------------------------------------------- */
		
		*						{ box-sizing:border-box; }
		html					{ font-size:62.5% }
		body					{ font-family:'Open Sans', sans-serif; font-size:1.8rem; line-height:1.4;   background:var(--grey-verylight); min-width:32rem; }
		.h2 					{ font-size:2.4rem; font-weight:400; }
		.h3 					{ font-size:1.3rem; font-weight:400; }
		.h3 span 				{ font-size:1.2rem; font-weight:300; }

		:root {
			--blue-vivid:		rgba(90,190,250,1); 	/* color:#5abefa; */
			--blue-bright:		rgba(51,149,223,1); 	/* color:#3395df; */
			--blue-light:		rgba(33,77,144,1); 		/* color:#214d90; */
			--blue-medium:		rgba(45,60,96,1); 		/* color:#2c3c61; */
			--blue-dark:		rgba(15,44,64,1); 		/* color:#0f2c40; */ 
			
			--grey-ultralight:	rgba(252,253,254,1); 	/* color:#fcfdfe; */
			--grey-verylight:	rgba(247,248,249,1); 	/* color:#f7f8f9; */
			--grey-lightest:	rgba(238,239,240,1); 	/* color:#eeeff0; */
			--grey-lighter:		rgba(218,221,224,1); 	/* color:#dadde0; */
			--grey-light:		rgba(186,192,198,1); 	/* color:#bac0c5; */
			--grey-medium:		rgba(98,105,112,1); 	/* color:#626970; */
			--grey-dark:		rgba(69,86,103,1); 		/* color:#455667; */
			--grey-darker:		rgba(45,60,75,1); 		/* color:#2d3c4b; */
			--grey-darkest:		rgba(30,42,54,1); 		/* color:#2d3c4b; */
			
			--up-green:			rgba(76,175,80,1); 		/* color:#4caf50; */
			--down-red:			rgba(252,61,57,1); 		/* color:#fc3d39; */
			
			--sale-brown:		rgba(55,35,30,1); 		/* color:#37231e; */
			--sale-red:			rgba(190,55,15,1); 		/* color:#be370f; */
			--sale-yellow:		rgba(255,200,40,1); 	/* color:#ffc828; */
		}		
		
		/* ---------------------------------------------------------------- */
		
		:focus { 
			outline:none; 
			background-color:var(--grey-verylight); 
			border:1px solid var(--blue-vivid); 
		}
		
		input,select,textarea 	{ 
			font-size:1.3rem; 
			line-height:2; 
			background-color:transparent; 
			padding:.5rem 1rem; 
			border:.1rem solid var(--grey-lighter); 
			box-shadow:none; 
			width:100%; 
			-webkit-appearance:none; 
			border-radius:.2rem; 
		}
		
		.textarea { min-height:10rem; }
		
		.textarea_xlarge { min-height:30rem; }
		
		.select { position:relative; }
  	
		.select::after { 
			position:absolute; 
			top:2.5rem; 
			right:.5rem; 
			content:"\e5c5"; 
			font-family:"Material Icons"; 
			font-size:2.4rem; 
			line-height:1.2; 
			color:var(--grey-medium); 
			opacity:1; 
			pointer-events:none; 
		}
	
		.select_multiple::after { 
			top:2.7rem; 
			content:"\e03b"; 
			font-size:2rem; 
		}
	
		.button {
		  	display:inline-block; 
			position:relative; 
			font-size:1.4rem; 
			font-weight:600; 
			font-style:normal; 
			text-transform:uppercase; 
			line-height:1; 
			letter-spacing:0; 
			text-align:center; 
			text-decoration:none; 
			background:transparent; 
			border:none; 
			border-radius:.2rem; 
			vertical-align:middle; 
			padding:0 1.5rem; 
			margin:0; 
			will-change:box-shadow; 
			transition:box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1); 
			min-width:15rem; 
			height:3.6rem; 
			overflow:hidden; 
			outline:none; 
			cursor:pointer; 
		}
		
		.button.primary {
			color:rgb(255,255,255); 
			background-color:rgb(84,144,219); 
		}
		
		button i {
			vertical-align:middle; 
			margin:-.3rem .8rem 0 0; 
		}
				
		/* ---------------------------------------------------------------- */
		
		.form-save { 
			grid-area: initial; 
			justify-self: initial; 
			position: fixed;
			bottom: 12rem;
			right: 2rem;
			z-index:1;
		}
		
		.form-save .button {
			font-size:1.4rem;
			line-height:1.8rem;
			padding:.8rem 2rem .8rem 1.6rem;
			border-radius:2.8rem;
			height:5.6rem;
			width:auto;
			box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
		}
		
		.form-save .button  i {	display:inline-block; }
				
		/* ---------------------------------------------------------------- */
		
		label 			   { display:block; font-size:1rem; font-weight:400; }
  		.hrLabel label	   { padding-bottom:.3rem; border-bottom:.1rem solid transparent; margin-bottom:.3rem; }
  		.inlineLabel label { display:inline-block; border-bottom:none; margin-right:.5rem; }
  	
  		/* ---------------------------------------------------------------- */

  		.roundBox li,
  		.roundBox li span 	{ border-radius:.2rem; }
  	
		/* ---------------------------------------------------------------- */

  		.rate div 			{ font-size:1.35rem; }
		.rate span 			{ font-size:1rem; font-weight:300; }
		.rate span::before 	{ content:"/"; margin:0 .2rem 0 .3rem; }
		
		/* ---------------------------------------------------------------- */
		
		.valueUp  { color:#fff; background:var(--up-green); }
		.valueDown { color:#fff; background:var(--down-red); }
		.valueNone { color:#fff; background:var(--grey-darker); }
		
		.valueUp::before,
		.valueDown::before {
			position:absolute; 
			top:calc(50% - .3rem); 
			left:.7rem; 
			content:""; 
			border-left:.4rem solid transparent; 
			border-right:.4rem solid transparent; 
		}

		.valueUp::before 	{ border-bottom:.5rem solid #fff; }
		.valueDown::before  { border-top:.5rem solid #fff; }	
		
		/* ---------------------------------------------------------------- */
		 
		header { 
			position:fixed; 
			top:0; 
			left:0; 
			background-color:rgba(30,65,150,1); 
			background:linear-gradient(to right, rgba(22,69,139,.95) 0%,rgba(18,39,86,.9) 100%), url(../../images/monarchWing.jpg); 
			background-repeat:no-repeat; 
			background-size:cover; 
			background-position:100% 0; 
			border-bottom:4px solid #548fdb; 
			width:100%; 
			height:6.3rem; 
			z-index:1; 
			transition:top 0.3s; 
		}
 
		header ul {
			position:relative; 
			color:#fff; 
			background-image:url(../../images/monarchMobile-logoBug.png); 
			background-repeat:no-repeat; 
			background-size:3.6rem 3rem; 
			background-position:50% calc(50% - .2rem); 
			height:6.3rem; 
		}
 
		header li { 
			display:inline-block; 
			position:absolute; 
			top:calc(50% - 2.8rem); 
			line-height:2.8; 
			text-align:center; 
			width:4.4rem; 
			height:4.4rem; 
		}
		
		header i				{ vertical-align:middle; }
		
		header li:first-of-type { right:inherit; left:.75rem; }
		header li:last-of-type  { right:1rem; left:inherit;   }
 
		main { 
			position:relative; 
			padding:1rem; 
			margin:6.3rem auto 5rem; 
			width:100%; 
		}
		 
		footer {
			display:grid; 
			grid-template-columns:repeat(5, 1fr); 
			grid-gap:0; 
			align-items:start; 
			position:fixed; 
			bottom:0; 
			font-size:1.2rem; 
			color:var(--grey-medium); 
			text-align:center; 
			background-color:var(--grey-ultralight); 
			padding-top:1rem; 
			border-top:.1rem solid var(--grey-lighter); 
			width:100%; 
			height:7rem; 
			z-index:1; 
		}
		
		footer a { color:var(--grey-medium); text-decoration:none; }
		
		/* ---------------------------------------------------------------- */
		
		.summary-view + footer .summary-link, 
		.editDetails-view + footer .editDetails-link, 
		.addPhotos-view + footer .addPhotos-link, 
		.comparablesList-view + footer .comparablesList-link,
		.comparablesSummary-view + footer .comparablesList-link,
		.jobInfo-view + footer .jobInfo-link { 
			color:var(--blue-bright); 
		}
		
		footer i 	 { vertical-align:middle; margin:0 .5rem; }
		footer label { display:inline-block; }
		
		/* ---------------------------------------------------------------- */
		
		nav {
			position:fixed;
			top:6.3rem;
			left:0;
			right:0;
			background: var(--blue-dark);
			padding:0 1.5rem;
			margin:0;
			overflow: auto;
			-webkit-overflow-scrolling: touch;
			-webkit-transform: translate3d(0,0,0);
			z-index:1;
			transition: top 0.3s; 
			width:100vw;
		}
			
		nav.off-canvas { top: .4rem; }
		nav.fixed 	   { top: 6.3rem; z-index: 1; }
	
		.tabs {
			font-size:1.6rem;
			text-align:center;
			width:100%;
			overflow: auto;
		}
	
		.tabs li {
			display:inline-block;
			padding:0;
			border-left:none;
			margin-left:0;
			width:45%;
			float:left;
		}
		
		.tabs i 			 { display:none; }
		.tabs span 			 { display:block; color:#fff; line-height:4; }
		.tabs span.is-active { color:#fff; box-shadow:inset 0 -.4rem 0 0 var(--blue-vivid); }
		
		.tabBlock-wrapper:not(.default-section) { display:none; }
		.tabBlock-wrapper.disabled {
			pointer-events:none;
			opacity:0.5;
		}
				   
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:1220px) {	
			
			.h3 { font-size:1.8rem; }
			
		}

		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:768px) { 
		
			body { overscroll-behavior-y: contain; }
			
			header.off-canvas { top:-5.9rem; }
			header.fixed { top:0; z-index:1; }
			footer label { display:block; }

			.button { font-size:1.2rem; min-width:8rem; height:3rem; }
			.form-save { bottom: 9rem; }
			
			.roundBox li { margin:.0; }
  			.roundBox li span 	{ margin: -0.4rem 0 0; }

			
			.tabBlock-wrapper { grid-area: initial; box-shadow:none; }
			
			
		
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:600px) { 
			
			main { padding:1rem .5rem; }
			.h3 { padding-left:0.2rem; }
			.iconLabel label { display:none; }
			
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:480px) {
		
			label { font-size:.9rem; }
			
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:374px) { 
		
			.form-save { 
				right: 0;
				left:0;
				text-align:center;
				margin:0 auto;
			}
			
			.form-save .button {	width:30rem; }
			
		}
		
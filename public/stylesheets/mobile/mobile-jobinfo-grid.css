		
		/* ---------------------------------------------------------------- */

		.jobInfo-wrapper {
			display:grid;
			grid-template-columns: 1fr;
			grid-template-rows:auto;
			grid-gap:0;
			padding:4rem 0;
			margin:0 auto;
			max-width:136.6rem;
		}
		
		/* ---------------------------------------------------------------- */
		
		.jobInfo-item { 
			display:grid;
			grid-template-columns: repeat(12, 1fr);
			grid-template-rows:auto;
			grid-gap:3rem;
			align-items:start;
			color:var(--blue-medium);
			background-color:var(--grey-ultralight);
			padding:3rem 2rem;
    		border-bottom:.1rem dashed var(--grey-lighter);
		}
		
		.jobInfo-item:last-of-type { border-bottom:none; }
		
		.jobInfo-item li { 
			justify-content: stretch; 
			position:relative; 
		} 
				
		.jobInfo-item i 		{ display:inline-block; font-size:2rem; color:var(--blue-light);	}
		.jobInfo-item li label 	{ font-size:1.1rem; padding-bottom: .5rem; border-bottom: 1px solid var(--grey-lightest); margin-bottom: .7rem; }
		.jobInfo-item li div 	{ display: inline-block; vertical-align: top; margin-left:.5rem; width: calc(100% - 3.4rem); }
		.jobInfo-item li span 	{ display: inline-block; font-size: 1.3rem; font-weight:600; color: var(--grey-darker); line-height: 1.6; background: rgba(227,246,252,.5); padding: .2rem 1rem;}
		
		.Site_Inspection_Notes,
		.Inspection_Type,
		.Inspection_Date,
		.Inspection_Time,
		.Valuation_Report_Type,
		.Purpose_of_Valuation,
		.Valuation_Date,
		.Valuation_Time,
		.Valuer,
		.Countersigned_By,
		.Client_Name,
		.Lender_Name,
		.Borrower,
		.Instructed_By,
		.Extended_To,
		.Other_Instructions	{ 
			grid-column: span 3; 
		}
		
		.Lender_Name { 
			grid-column: span 4; 
		}
		
		.Site_Inspection_Notes,
		.Other_Instructions	{ 
			grid-column: span 10; 
		}

		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:1220px) {
		
			.jobInfo-wrapper { padding:1.2rem 0; }
			
			.jobInfo-item { grid-gap:3rem 1rem; }
			
			.Inspection_Type,
			.Inspection_Date,
			.Inspection_Time { 
				grid-column: span 4; 
			}
			
			.Valuation_Report_Type,
			.Purpose_of_Valuation,
			.Valuation_Date,
			.Valuation_Time,
			.Valuer,
			.Countersigned_By,
			.Client_Name,
			.Borrower,
			.Instructed_By,
			.Extended_To,
			.Other_Instructions	{ 
				grid-column: span 6; 
			}
		
			.Site_Inspection_Notes,
			.Lender_Name,
			.Other_Instructions	{ 
				grid-column: span 12; 
			}
						
		}
			
			
			
		/* ---------------------------------------------------------------- */
			
		@media only screen and (max-width:768px) { 
		
			.jobInfo-wrapper { padding: 0; }			
			
			.jobInfo-item { 
				border:none;
				margin-bottom:1.6rem;
				box-shadow:inset 0 0 0 .1rem var(--grey-lightest);
			}
			
			.jobInfo-item li span 	{ font-size: 1.4rem; line-height: 1.6; }
			
			.Inspection_Date,
			.Inspection_Time,
			.Valuation_Report_Type,
			.Purpose_of_Valuation,
			.Valuation_Date,
			.Valuation_Time,
			.Valuer,
			.Countersigned_By,
			.Client_Name,
			.Borrower,
			.Instructed_By,
			.Extended_To { 
				grid-column: span 6; 
			}
		
			.Site_Inspection_Notes,
			.Inspection_Type,
			.Lender_Name,
			.Other_Instructions	{ 
				grid-column: span 12; 
			}
						
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:600px) { 	}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:480px) {
		
			.jobInfo-item { padding:2rem 1rem; }
						
			.Inspection_Date,
			.Inspection_Time,
			.Valuation_Date,
			.Valuation_Time	{ 
				grid-column: span 6; 
			}
			
			.Site_Inspection_Notes,
			.Inspection_Type,
			.Valuation_Report_Type,
			.Purpose_of_Valuation,
			.Valuer,
			.Countersigned_By,
			.Client_Name,
			.Lender_Name,
			.Borrower,
			.Instructed_By,
			.Extended_To,
			.Other_Instructions,
			.Site_Inspection_Notes,
			.Other_Instructions	{ 
				grid-column: span 12; 
			}
								
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:374px) { 
		
	
			
		}
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		/* ---------------------------------------------------------------- */

		.addPhotos-wrapper {
			display: grid;
			grid-template-columns: auto;
			grid-template-rows: auto;
			grid-gap: 0;
			padding: 4rem 0;
			margin: 0 auto;
			max-width: 136.6rem;
		}
		
		/* ---------------------------------------------------------------- */
		
		.addPhotos-item { 
			display:grid;
			grid-template-columns: repeat(12, 1fr);
			grid-template-rows:auto;
			grid-gap:3rem;
			align-items:start;
			color:var(--blue-medium);
			background-color:var(--grey-ultralight);
			padding:3rem 2rem;
		}
		
		.addPhotos-item i 		 { display:inline-block; font-size:2rem; color:var(--blue-light);	}
		.addPhotos-item li label { font-size:1.1rem; margin-bottom:.5rem; }
		.addPhotos-item li div 	 { display: inline-block; vertical-align: top; margin-left:.5rem; width: calc(100% - 3.4rem); }

		.Add_Property_Photos { 
			grid-area:1 / 2 / span 1 / span 10; 
		}

		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:1220px) {
		
			.addPhotos-wrapper { padding:1.2rem 0; }
			
			.addPhotos-item { grid-gap:3rem 1rem; }
						
		}
			
		/* ---------------------------------------------------------------- */
			
		@media only screen and (max-width:768px) { 
		
			.addPhotos-wrapper { padding: 0; }			
			
			.addPhotos-item { 
				border:none;
				margin-bottom:1.6rem;
				box-shadow:inset 0 0 0 .1rem var(--grey-lightest);
			}
			
			.addPhotos-item li span 	{ font-size: 1.4rem; line-height: 1.6; }
						
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:600px) { 	
		
			.Add_Property_Photos { 
				grid-area:1 / 1 / span 1 / span 12; 
			}
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:480px) {
		
			.addPhotos-item		   { padding:2rem 1rem; }
			.addPhotos-item i	   { display:none; }
			.addPhotos-item li div { width: calc(100% - .8rem); }

								
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:374px) { 	}
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		.hrLabel label { display:none; }
		
		.comparablesList-back { width:auto; } 
		.comparablesList-back label {
			display: inline-block;
			color: #fff;
			font-size: 1.6rem;
    		margin-left:-.5rem;
		}
		
		/* ---------------------------------------------------------------- */
		
		.comparablesList-wrapper,
		.comparablesSummary-wrapper {
			display: grid;
			grid-template-columns: auto;
			grid-template-rows: auto;
			grid-gap: 0;
			padding:4rem 0;
			margin:5.3rem auto 0;
			max-width:136.6rem; 
		}

		.comparablesList-wrapper .compsList { z-index: 0; }

		
		.comparablesSummary-wrapper { padding:0; 	  }
		.comparablesSummaryPhoto 	{ padding:4rem 0; }
		.comparablesSummaryComment 	{ padding:4rem 0; }
		
		.comparablesList-wrapper .tabs,
		.comparablesSummary-wrapper .tabs { padding:0 25%; }
	
		.comparablesSummary-wrapper .tabs li { width:30%; }
		
		.comparablesList-wrapper  label 		 { color:var(--grey-dark); }
		.comparablesList-wrapper  .hrLabel label { border-bottom-color:var(--grey-lighter); }
		
		/* ---------------------------------------------------------------- */
		
		.h3 span { display:block; font-weight:400; }
		.h3 span::before { display:none; }
		
		/* ---------------------------------------------------------------- */
		
		.listHeading  { grid-template-columns:5.5rem repeat(10, 1fr); }
		
		.listHeading.propertylist-item { 
			grid-template-rows:3rem; 
			align-items:start; 
			border-bottom:.4rem solid var(--grey-lightest); 
		}
		
		/* ---------------------------------------------------------------- */
		
		.comparablesList  { grid-template-columns:5.5rem repeat(10, 1fr); }
		.comparablesList  li:nth-of-type(2) { grid-column:span 2; color:var(--blue-light); }
		.comparablesList  li:nth-child(n+3) { grid-column:span 1; text-align:right; }
		
		/* ---------------------------------------------------------------- */		
		
		.propertylist-item {
			display:grid; 
			grid-template-rows:8rem; 
			grid-gap:1.5rem; 
			align-items:center; 
			font-size:1.2rem; 
			background-color:#fff; 
			padding: 0 2rem 0;
			border-bottom:.1rem solid var(--grey-lightest); 
		}
		
		.propertylist-item:last-of-type { padding-bottom:1rem; border-bottom:none; }
		
		.listHeading { padding-top:3rem; }
		
		.propertylist-item li {	grid-column:span 1; }
		.propertylist-item li:last-of-type::before { display:none; }
				
		.propertylist-item img { display:block; width:6rem; height:6rem; }
		
		/* ---------------------------------------------------------------- */
		
		.editComparables-item { 
			display:grid;
			grid-template-columns: repeat(12, 1fr);
			grid-template-rows:auto;
			grid-gap:3rem;
			align-items:start;
			color:var(--blue-medium);
			background-color:var(--grey-ultralight);
			padding:3rem 2rem;
		}
		
		.editComparables-item i 		{ display:inline-block; font-size:2rem; color:var(--blue-light);	}
		.editComparables-item li label 	{ font-size:1.1rem; margin-bottom:.5rem; }
		.editComparables-item li div 	{ display: inline-block; vertical-align: top; margin-left:.5rem; width: calc(100% - 3.4rem); }
		
		.Comment {
			grid-column: span 12; 
		}
		
		.Add_a_Primary_Photo_for_this_Property { 
			grid-area:1 / 2 / span 1 / span 10; 
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:1220px) { 
				
			.comparablesList-wrapper { grid-template-columns: auto; padding: 1.2rem 0; }
			
			.comparablesSummary-wrapper { padding: 1.2rem 0; }
			
			.comparablesSummaryPhoto { padding: 0; }
		
			.comparablesSummaryComment { padding: 0; }
			
			.comparablesList-wrapper .tabs,
			.comparablesSummary-wrapper .tabs { padding:0 18.75%; }

			.hrLabel label { display:block; }
			.hrLabel .inlineLabel label { display:inline-block; }
			
			.comparablesList { box-shadow: none; }
					
			.propertylist-item {
				grid-template-columns:18rem repeat(6, 1fr); 
				grid-template-rows:auto 5rem auto auto; 
				grid-gap:.75rem 1.5rem; 
				align-items:center; 
				font-size:1.3rem; 
				padding:1rem; 
				border:none;; 
				margin-bottom:1.5rem;
				box-shadow:inset 0 0 0 .1rem var(--grey-lightest);
			}
						
			.listHeading { display:none; }
						
			.propertylist-item li:nth-child(n+3) { text-align:left; }
		
			.propertylist-item li:nth-of-type(1) 	{ grid-area:1 / 1 / span 4 / span 2; align-self:start; }
			.propertylist-item li:nth-of-type(2) 	{ grid-area:1 / 2 / span 1 / span 5; align-self:start;  }
			.propertylist-item li:nth-of-type(3) 	{ grid-area:2 / 2 / span 1 / span 6; align-self; end; font-size:1.1rem; font-weight:400; font-style:italic; line-height:2.4; color:var(--blue-dark); text-align:left; background:var(--grey-lightest); padding:0 .75rem; }
			.propertylist-item li:last-of-type 		{ grid-area:4 / 1 / span 1 / span 1; align-self:end; position:relative; font-size:1rem; line-height:2.4; background:rgba(15,44,64,.9); color:#fff; padding-left:.5rem; }

			.propertylist-item li:nth-of-type(4) 	{ grid-area:3 / 2 / span 1 / span 2; }
			.propertylist-item li:nth-of-type(5) 	{ grid-area:3 / 4 / span 1 / span 2; }
			.propertylist-item li:nth-of-type(6) 	{ grid-area:3 / 6 / span 1 / span 2; }
			.propertylist-item li:nth-of-type(7) 	{ grid-area:4 / 2 / span 1 / span 2; }
			.propertylist-item li:nth-of-type(8) 	{ grid-area:4 / 4 / span 1 / span 2; }
			.propertylist-item li:nth-of-type(9) 	{ grid-area:4 / 6 / span 1 / span 2; }
						
			.propertylist-item li:last-of-type { padding-left:2rem; }
			.propertylist-item li:last-of-type label { display:none; }
			.propertylist-item li:last-of-type::before {
				display:inline-block; 
				position:absolute; 
				top:calc(50% - .5rem); 
				left:.5rem; 
				font-size:1rem; 
				line-height:1
			}
			
			.propertylist-item.hrLabel li:nth-of-type(3) label { font-size:1rem; padding-bottom:0; margin-bottom:0; }
			.propertylist-item img { width:18rem; height:18rem; }
			
			.editComparables-item { grid-gap:3rem 1rem; }

		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:768px) {
			
			.comparablesList-wrapper,
			.comparablesSummary-wrapper { 
				grid-template-columns:1fr; 
				grid-template-rows:auto; 
				grid-gap:1.5rem; 
				background-color:transparent; 
				margin-top: 5.3rem; 
			}
			
			.comparablesList-wrapper +  .comparablesSummary-wrapper { margin-top: -1.2rem; }
			
			.comparablesList-wrapper .tabs,
			.comparablesSummary-wrapper .tabs { padding:0 12.5%; }
			
			.comparablesSummary-wrapper .summary-wrapper { width:auto; }
			
			.editComparables-item { 
				border:none;
				box-shadow:inset 0 0 0 .1rem var(--grey-lightest);
			}
			
			.cpm-uploadPhoto {
				display: flex;
				flex-direction: column-reverse;
				position:relative;
				border-top: 1px solid #e7e7e7;
				margin: 0 auto;
				box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
				width: calc(100% - 2rem);
			}
			
			.cpm-uploadPhoto .buttonRow {
				color: #373d40;
				text-align:center;
				padding:2rem 0
			}

			.cpm-uploadPhoto .buttonRow button {
				display: inline-block;
				position: relative;
				font-size: 1.4rem;
				font-weight: 600;
				font-style: normal;
				color: rgb(84,144,219);
				text-transform: uppercase;
				line-height: 3.6rem;
				letter-spacing: 0;
				text-align: center;
				text-decoration: none;
    			background-color: rgb(237,241,245);
				border: none;
				border-radius: 2px;
				padding: 0 1.6rem;
				margin: .5rem .5rem .5rem 0;
				width: calc(50% - 2.5rem);
				max-width:18rem;
				height: 36px;
				vertical-align: middle;
			}
			
			.cpm-uploadPhoto .buttonRow button:last-of-type {
				color: #fff; 
				background: #5290db;
				margin: .5rem 0 .5rem .5rem;
			}

			.cpm-uploadPhoto .buttonRow button:first-of-type::after { content: "Cancel"; }
			.cpm-uploadPhoto .buttonRow button:last-of-type::after  { content: "Save"; 	 }
			.cpm-uploadPhoto .buttonRow button i 					{ display:none; 	 }
			
			.cpm-uploadPhoto .imageHolder 		 { padding: 1.5rem; 		  }
			.cpm-uploadPhoto .imageHolder i 	 { display:none;			  }
			.cpm-uploadPhoto .imageHolder canvas { display:block; width:100%; }

		}

		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:600px) { 
								
			.propertylist-item { grid-template-columns:10rem repeat(6, 1fr); grid-template-rows:auto auto auto auto; padding:1rem .8rem; }
			
			.propertylist-item li:nth-of-type(1) 	{ grid-area:3 / 1 / span 2 / span 2; }
			.propertylist-item li:nth-of-type(2) 	{ grid-area:1 / 1 / span 1 / span 5; margin-top:-.3rem; }
			.propertylist-item li:nth-of-type(3) 	{ grid-area:2 / 1 / span 1 / span 7; line-height:2; }

			.propertylist-item img { width:10rem; height:10rem; }
			
			.Add_a_Primary_Photo_for_this_Property { 
				grid-area:1 / 1 / span 1 / span 12; 
			}
			
		}
		
		/* ---------------------------------------------------------------- */
		
		@media only screen and (max-width:480px) { 
				
			.comparablesList-wrapper .tabs,
			.comparablesSummary-wrapper .tabs { padding:0; }
			
			.comparablesList-wrapper li { text-overflow:ellipsis; white-space:nowrap; overflow:hidden; }
			
			.comparablesList-wrapper .hrLabel label { padding-bottom:.3rem; margin-bottom:.4rem; }
			
			.propertylist-item {grid-template-columns:8rem repeat(6, 1fr); grid-gap:1rem 1rem; }
			
			.propertylist-item img { display:block; width:8rem; height:8rem; }
			
			.editComparables-item 			{ padding:2rem 1rem; box-shadow:none; }
			.editComparables-item i 		{ display:none; }
			.editComparables-item li div 	{ width: calc(100% - .8rem); }

		}
		
		@media only screen and (max-width:374px) {
			
			.propertylist-item { grid-template-columns:7rem repeat(6, 1fr);  grid-gap:1rem 1rem; font-size:1.2rem; }
			
			.propertylist-item img { width:7rem; height:7rem; }

		}
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
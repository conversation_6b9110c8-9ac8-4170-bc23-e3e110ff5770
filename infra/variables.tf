variable "target_account_role_arn" {
  description = "The role ARN to assume in the target account"
  type        = string
  default     = "arn:aws:iam::************:role/ci-cd-account-service-role"
  validation {
    condition     = length(var.target_account_role_arn) > 0
    error_message = "Target account role ARN must be provided"
  }
}

variable "env" {
  description = "The environment name"
  type        = string
  validation {
    condition     = length(var.env) > 0
    error_message = "You must provide a valid environment name"
  }
}

variable "app_name" {
  description = "The application name"
  type        = string
  validation {
    condition     = length(var.app_name) > 0
    error_message = "You must provide a valid application name"
  }
}

variable "cost_center" {
  description = "The cost center for the application"
  type        = string
  validation {
    condition     = length(var.cost_center) > 0
    error_message = "You must provide a valid cost center"
  }
}
language: scala
jdk:
  - openjdk8
before_cache:
  # Delete all ivydata files since ivy touches them on each build
  - find $HOME/.ivy2/cache -name "ivydata-*.properties" | xargs rm
env:
  - JAVASCRIPT_FOLDER="$HOME/build/Quotable-Value/monarch-web/public/javascripts"
cache:
  directories:
    - $HOME/.ivy2/cache
    - $HOME/.sonar/cache
    - $HOME/.npm
notifications:
  slack: qv-team:SFDFayqM6iWaMKA3VcnpNWG1
addons:
  sonarcloud:
    token: $SONAR_TOKEN
before_install:
  - nvm install 10
  - nvm use 10
  - mkdir -p ~/.ssh
  - echo "$GIT_SSH_KEY" > ~/.ssh/id_rsa
  - chmod 600 ~/.ssh/id_rsa
  - ssh-keygen -F github.com || ssh-keyscan github.com >>~/.ssh/known_hosts
install:
  # Need to ensure tags are fetched for Git-based versioning to work
  # testing build
  - git fetch --unshallow
  - pip install --user awscli
  - eval $(aws ecr get-login --no-include-email --region ap-southeast-2)
before_script:
  - npm ci
  - chmod 777 public -R
  - npm run build:prod
script:
  - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
  - export CODEARTIFACT_TOKEN=$(aws codeartifact get-authorization-token --region ap-southeast-2 --domain quotable-value --domain-owner 948396734470 --query authorizationToken --output text)
  - sbt -DbuildTarget=kubernetes docker:publish
  # Can't figure out how to expose version from sbt, so just recalculate it from git describe
  - sonar-scanner -Dsonar.host.url=https://sonarqube.qvmonarch.co.nz -Dsonar.projectVersion=$(git describe --abbrev=7 --tags)
before_deploy:
  # Create a ZIP of all Kubernetes Resource JSON files, replacing the {version} placeholder with the actual version where required
  - mkdir -p deploy
  - cd kubernetes-configuration
  - >
    for file in *.json; do
      VERSION=$(git describe --abbrev=7 --tags)
      if [[ $VERSION = *"-"* ]]; then
        VERSION=$VERSION"-SNAPSHOT-SNAPSHOT"
      else
        VERSION=$VERSION"-SNAPSHOT"
      fi
      sed -i "s/{version}/${VERSION}/g" $file
    done
  - zip -r $TRAVIS_BUILD_DIR/deploy/monarch-web-$(git describe --abbrev=7 --tags).zip *
deploy:
  # Development branch CI releases
  - provider: s3
    access_key_id: $ARTIFACTS_KEY
    secret_access_key: $ARTIFACTS_SECRET
    bucket: qv-monarch-dev-kubernetes-delivery
    upload-dir: dev
    region: $ARTIFACTS_REGION
    skip_cleanup: true
    local_dir: $TRAVIS_BUILD_DIR/deploy
    detect_encoding: true
    on:
      all_branches: true
      condition: $TRAVIS_BRANCH =~ ^(develop|Release-.*)$

  # Tagged releases
  - provider: s3
    access_key_id: $ARTIFACTS_KEY
    secret_access_key: $ARTIFACTS_SECRET
    bucket: qv-monarch-dev-kubernetes-delivery
    upload-dir: dev
    region: $ARTIFACTS_REGION
    skip_cleanup: true
    local_dir: $TRAVIS_BUILD_DIR/deploy
    detect_encoding: true
    on:
      tags: true

module.exports = {
    // parser: 'vue-eslint-parser',
    parserOptions: {
        parser: 'babel-eslint',
        sourceType: 'module',
        allowImportExportEverywhere: true
    },
    env: {
        jquery: true,
    },
    globals: {
        'jsRoutes': 'readonly',
    },
    extends: [
        'airbnb-base',
        'plugin:vue/recommended'
    ],
    rules: {
        'radix': 'off',
        'linebreak-style': ['error', 'unix'],
        'no-use-before-define': 0,
        'yoda': 0,
        'eqeqeq': 0,
        'max-len': 0,
        'no-console': 0,
        'no-alert': 0,
        'no-plusplus': 0,
        'no-restricted-syntax': 0,
        'import/order': 0,
        'brace-style': [
            'error',
            'stroustrup',
            {
                'allowSingleLine': true
            }
        ],
        'object-curly-newline': 0,
        "indent": ["error", 4, { "SwitchCase": 1 }],
        'import/extensions': 0,
        'import/no-unresolved': 0,
        'no-param-reassign': ['error', {
            'props': false,
        }],
        'vue/require-v-for-key': 0,
        'vue/no-unused-vars': 'error',
        'vue/first-attribute-linebreak': 0,
        'vue/html-closing-bracket-spacing': 0,
        'vue/html-closing-bracket-newline': 0,
        'vue/singleline-html-element-content-newline': 0,
        'vue/max-attributes-per-line': ['error', {
            'singleline': {
                'max': 5
            },
            'multiline': {
                'max': 5
            }
        }],
        'vue/html-indent': ['error', 4, {
            'ignores': []
        }]
    },
};

# monarch-web
 
Monarch web provides the user interface and external facing REST API of the Monarch system.

The web application is built on the Play 2 framework, while Vue.js is used as the primary
enabler of JavaScript UI components.

The Play 2 routes expose REST API endpoints to the Monarch Web application, which in turn may
be a simple facade and secure access point to supporting microservices.

While user authorisation is handled by Auth0, Monarch Web uses security filters that enforce
access control based on this authorisation

## Docker dev environment setup

### Quickstart: `docker compose up`

There are two containers, one for building/bundling the frontend code, and another to run the backend (which serves the bundled frontend code). The containers watch for file changes.
```bash
git clone monarch-web
git clone qv-maps
cd monarch-web
docker compose up
# or to avoid the frontend not building in time you can start the vue container first
docker compose up monarch-vue
docker compose up monarch-sbt
```

[conf/application.conf](conf/application.conf) will pick up env vars set on the [monarch-sbt](docker-compose.yml) container.

## Frontend Overview

The front-end application has two distinct areas: legacy, and routed. Everything that isn't listed
in `routes.js` is accessed via the `/` and is considered the legacy part of the app.

Expected knowledge for frontend work:

* HTML, CSS
* JavaScript, ES2017+
* ESLint
* SCSS / Sass
* Vue 2.x, Vue Router, Vuex
* Node.JS for running webpack and tooling
* Webpack for running the build
* cmder for Windows command line stuff

### Tip

* <https://www.vuemastery.com> is a really good way to get up to speed with Vue, Vue Router and
  Vuex.

### Files

* `main.js` is the root javascript file for the build process. Everything starts from there.
* `DataStore.js` is the root for all the vuex stores.
* `routes.js` is the root for the vue router
* `package.json` has a `scripts` section which lists common and useful tasks such as `watch` that
   you can invoke i.e. `npm run watch`
* `.editorconfig` sets sane defaults, if you let your editor obey it with a plugin or setting.
* `webpack.config.js` has pretty sane defaults for building a vue js project like this.

## Building Vue Frontend

```sh
npm ci # clean install

set JAVASCRIPT_FOLDER=c:\dev\monarch-web\public\javascripts
# defaults to this value, NO TRAILING SLASH

npm run build

# watch mode
npm run watch
```
## Starting Local Dev

### To install dependencies
Assume `arn:aws:iam::************:role/CICD-RW-Cross-Account` to get dependencies from CodeArtifact.
- assume cicd role arn:aws:iam::************:role/CICD-RW-Cross-Account
    -     aws-mfa --assume-role arn:aws:iam::************:role/CICD-RW-Cross-Account --duration 28800
- set the environment variable CODEARTIFACT_AUTH_TOKEN using the following command:
    - for bash:
        -     export CODEARTIFACT_AUTH_TOKEN=`` ` ``aws codeartifact get-authorization-token --domain quotable-value --domain-owner ************ --region ap-southeast-2 --query authorizationToken --output text`` ` ``
    - for powershell:
        - declare the following function:
            -     function Set-CodeArtifact-Token {
                        $token = (aws codeartifact get-authorization-token --domain quotable-value --domain-owner ************ --region ap-southeast-2 --query authorizationToken --output text)
                        [System.Environment]::SetEnvironmentVariable('CODEARTIFACT_AUTH_TOKEN', $token, 'User')
                        [System.Environment]::SetEnvironmentVariable('CODEARTIFACT_TOKEN', $token, 'User')
                    }
        - invoke it
            -     Set-Artifact-Token

- run the following command to update sbt dependencies:
    -     sbt update
### To run Monarch (invoke lambdas)

Config `lambda_invoke.target_env` to the target app environment name (e.g. dev) in [application.conf](conf%2Fapplication.conf)
and assume the role which has access to the AWS account where the app is deployed.(e.g. `arn:aws:iam::************:role/DEV-RW-Cross-Account

-     aws-mfa --assume-role arn:aws:iam::************:role/DEV-RW-Cross-Account --duration 28800
-     sbt runAll

and open <http://localhost:9000>


## Troubleshooting

If you get a message around "digital envelope routines" being unsupported, run the following before `npm run build` or `npx cypress open`:

```sh
$env:NODE_OPTIONS = "--openssl-legacy-provider"
```

## Tooling

### Visual Studio Code

Install these extensions:
* EditorConfig
* Vetur by Pine Wu
* ESLint by Dirk Baeumer

Add the following lines to your VSCode user `settings.json` file.

(<kbd>Ctrl+P, open settings json</kbd>)

```json
{
   ...
    "eslint.alwaysShowStatus": true,
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        { "language": "vue"},
    ],
    "vetur.validation.template": false,
    ...
}
```

### ESLint

Check `.eslintrc.js` for the settings, knowing that there are presets we're inheriting

```sh
# get lint for everything in vue\
npm run lint
# which is a shortcut for
npx eslint --ext .js,.vue vue\

# lint and fix one file (repeat as necessary)
npx eslint --ext .js,.vue --fix vue\components\Home.vue
```

## API Endpoints

The following endpoints are exposed by the Monarch Web API. This list is NOT EXHAUSTIVE and
presented for examples.

* `/property/properties/identifier_lodgement`
* `/property/properties/:ownerId/photos/upload_initiation`
* `/property/properties/:ownerId/photos/:mediaId/upload_acknowledgement`
* `/property/properties/relink/validation?qpidsIn=:qpidsIn&qpidsOut=:qpidsOut`
* `/property/properties/:id/photos`

### API Examples

#### POST: `http://localhost:9000/property/properties/identifier_lodgement`

##### Request

```json
{
   "kind":"identifierLodgement",
   "identifiers":{
      "qivsQupidId":123
   },
   "action":"OBTAIN_IF_NOT_EXIST",
   "_link":{
      "self":{
         "href":"/consents/u980fy89yhjef323kjyhg8765s45s"
      }
   }
}
```

##### Response Header

```text
Location: /properties/70cded41-27c0-4912-993e-c0e596db3634
```

#### POST: `http://localhost:9000/property/properties/:ownerId/photos/upload_initiation`

##### Request

```json
{
   "kind":"photoUploadInitiation",
   "identifiers":{
      "qivsPhotoId":"2941755_2"
   },
   "fileName":"Testing.jpg",
   "imageContentType":"image/jpeg",
   "primaryPhoto":false,
   "includeInInsurance":false,
   "includeInInsuranceReports":false,
   "description":"Test Description",
   "capturedDate":"2015-09-28T21:45:47Z",
   "status":"UPLOAD_COMPLETED",
   "_links":{
      "self":{
         "href":"property/properties/u980fy89yhjef323kjyhg8765s45s/photos"
      }
   }
}
```

##### Response

```json
{
   "url":"https://qv-property-photos-migration-original.s3-ap-southeast-2.amazonaws.com/e8f8cad7-f40d-42e9-9b0a-87a2988def12/Testing.jpg?AWSAccessKeyId=AKIAJZ3RPMH5W2HCLL4A&Expires=1501739618&Signature=x1NBrQ0bEKk2xGUUWPfcY9g7kjQ%3D",
   "photoId":"e8f8cad7-f40d-42e9-9b0a-87a2988def12"
}
```

#### POST: `http://localhost:9000/property/properties/:ownerId/photos/:mediaId/upload_acknowledgement`

##### Response

```json
"true"
```

#### GET: `http://localhost:9000/property/properties/relink/validation?qpidsIn=1234&qpidsOut=3003921,3003924`

##### Response

```json
"true"
```

#### GET: `http://localhost:9000/property/properties/d91a07b7-2fdc-4183-9ef4-2848e14af804/photos`

##### Response

```json
{
   "kind":"propertyPhotos",
   "id":"1d6d73fe-ab60-4744-96b3-3e3a8c29a65c",
   "identifiers":{
      "qivsQupidId":2578930
   },
   "photos":[
      {
         "id":"ac70d9bd-afbf-4af0-b945-664227f47c22",
         "identifiers":{
            "qivsPhotoId":"2578930_1"
         },
         "fileName":"monarchHome-mdHeader.jpg",
         "imageContentType":"image/jpeg",
         "primaryPhoto":true,
         "includeInInsurance":false,
         "includeInInsuranceReports":false,
         "description":"Front",
         "status":"UPLOAD_CONFIRMED",
         "capturedDate":"2017-08-03T10:41:10Z",
         "_links":{
            "self":{
               "href":"/properties/ac70d9bd-afbf-4af0-b945-664227f47c22"
            },
            "originalImage":{
               "href":"https://qv-property-photos-dev1-original.s3-ap-southeast-2.amazonaws.com/ac70d9bd-afbf-4af0-b945-664227f47c22/monarchHome-mdHeader.jpg?AWSAccessKeyId=AKIAJZ3RPMH5W2HCLL4A&Expires=1501717321&Signature=JniSb46T%2BXyMrAzWYa3wFHHMc4M%3D"
            },
            "smallSizedImage":{
               "href":"https://qv-property-photos-dev1-resized.s3-ap-southeast-2.amazonaws.com/200x200/ac70d9bd-afbf-4af0-b945-664227f47c22/monarchHome-mdHeader.jpeg?AWSAccessKeyId=AKIAJZ3RPMH5W2HCLL4A&Expires=1501717321&Signature=Vmj1ya2MCZVuJ1%2By2sdCyqrIA%2Bg%3D"
            },
            "smallQVSizedImage":{
               "href":"https://qv-property-photos-dev1-resized.s3-ap-southeast-2.amazonaws.com/160x100/ac70d9bd-afbf-4af0-b945-664227f47c22/monarchHome-mdHeader.jpeg?AWSAccessKeyId=AKIAJZ3RPMH5W2HCLL4A&Expires=1501717321&Signature=YONhcGXFCmBMBmYO%2FH%2FTzVfYZxo%3D"
            },
            "mediumSizedImage":{
               "href":"https://qv-property-photos-dev1-resized.s3-ap-southeast-2.amazonaws.com/300x200/ac70d9bd-afbf-4af0-b945-664227f47c22/monarchHome-mdHeader.jpeg?AWSAccessKeyId=AKIAJZ3RPMH5W2HCLL4A&Expires=1501717321&Signature=1GOyUKu4ajaFVjvV%2BoD27gfvfLk%3D"
            },
            "largeSizedImage":{
               "href":"https://qv-property-photos-dev1-resized.s3-ap-southeast-2.amazonaws.com/1200x800/ac70d9bd-afbf-4af0-b945-664227f47c22/monarchHome-mdHeader.jpeg?AWSAccessKeyId=AKIAJZ3RPMH5W2HCLL4A&Expires=1501717321&Signature=v8DAfkW3B%2BwEdy%2FTkc99ItdteFA%3D"
            }
         },
         "propertyID":"1d6d73fe-ab60-4744-96b3-3e3a8c29a65c"
      }
   ],
   "_link":{
      "self":{
         "href":"/properties/1d6d73fe-ab60-4744-96b3-3e3a8c29a65c"
      }
   },
   "propertyID":"1d6d73fe-ab60-4744-96b3-3e3a8c29a65c"
}
```



# Frontend Test Development

If you're developing tests please read the notes at the bottom

## Run Tests

To run the Test in the command  line

To run a specific test
npx cypress run --spec cypress/e2e/api-picklist/Api-Picklist.cy.js --browser chrome 
To run the whole suite
npx cypress run --spec cypress/e2e/*/**.cy.js --browser chrome

## Documentation Link for Monarch Coverage
https://qv-projects.atlassian.net/wiki/spaces/QC/pages/2796126233/Cypress+test+coverage+in+Monarch




There are two prefix's for each environment

- `cli:{env}` run tests directly in the command line
- `cypress:{env}` opens tests in cypress studio for visual debugging

Example usage:

```
npm run cli:dev
or
npm run cypress:dev
or
npm run cli:{dev/test}:1 (to run one test specified in package.json)
```

Example environments:

```
dev
test
prod
```

## Notes for Devs

### **IMPORTANT!!!**

cypress > integration > smoke-tests

These tests are designed so they can be run in PRODUCTION, so only write tests that won't change prod data in here.

When running Cypress in prod environment, make sure to only run tests that are contained in this folder.

To run the smoke tests suite in prod, run:

```
cli:prod-smoke
``` 

In cypress > support > index.js
There is a before and beforeEach that run for every test which is necessary for the way monarch Authentication works.

Page model: cypress > model
Idea being each file represents a component / main monarch chunk, try to follow this style

Running tests from test runner, if you want to rerun a test you might need to stop it and start it again from the test runner itself, and not restart it from the cypress browser
or else the login may fail. Unsure why.

You might find that waiting on a request response before making assertions isnt always
reliable for reasons unclear, and it might be necessary to use arbitrary cy.wait() times.

Because of monarch being a SPA, be careful of invisible elements that share the same selector as what you are trying to select for.

- `cli` = "command line interface" run headless
- `cypress` = run in GUI

{PUT FORMATTER SETTINGS JSON HERE}

## Debugging

For debugging, remove '```"numTestsKeptInMemory" : 0```' from `cypress.config.json` in order to capture screenshots and read debug logs. This is included to stop the larger tests such as `VerifiedQV.test.js` from crashing in desktop mode.

## Custom commands

```javascript
doubleLog()
```

Prints both to cypress log and console.

### node version error

```javascript
Webpack Compilation Error
Module build failed (from ../../../AppData/Local/Cypress/Cache/13.7.3/Cypress/resources/app/node_modules/babel-loader/lib/index.js):
Error: Cannot find module 'fs/promises'
```
If you see this error, you are running the test suite in node version 12. Cypress requires node version 14.17.0 and above. You can use nvm to switch to the correct version of node.

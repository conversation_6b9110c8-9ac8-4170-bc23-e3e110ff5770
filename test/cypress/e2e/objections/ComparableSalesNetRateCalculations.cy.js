import testFactory from '../../support/testFactory.js';
import ObjectionComparableSales from '../../model/ObjectionComparableSales.js';
import ObjectionJob from '../../model/ObjectionJob.js';
let ObjectionJobPage = '';
const propertyAttributes = {
    "landValue": 100000,
    "noOfFreeStandingGarages": 1
}
const saleAttributes = {
    "saleDate": "2024-07-02 12:58:00",
    "categoryId": 888,
    "landZoneGroup": "9A",
    "capitalValue": 444444,
    "effectiveYearBuilt": 1999,
    "salePriceNet": 500000,
    "totalFloor": 100,
    "ois": "N",
    "buildingFloorArea": 150
}

describe('CV Comparables NetRate', () => {
    before('Generate test factory objection', function () {
        const objectionInputBody = testFactory.getComparableSales(propertyAttributes, saleAttributes);
        cy.request(objectionInputBody).then((response) => {
            const attributes = response.body.data[0].children[0].attributes;
            ObjectionJobPage = ObjectionJob.createObjectionJobPageURL(attributes.ratingValuationId);
            cy.wait(4000);
        });
    });


    context('Validating the Net rate value for CV comparable fields', () => {

        before('Visits page', () => {
            cy.visitWithLogin(ObjectionJobPage);
            ObjectionJob.elements.objectionJobStepperComparableSales
                .contains('Comparable Sales')
                .click({ force: true });
            cy.wait(2000);
            ObjectionJob.elements.objectionJobStepperComparableSales
                .contains('Comparable Sales')
                .click({ force: true });
            cy.wait(2000);
            ObjectionComparableSales.elements.cvCategories
                .contains('Categories')
                .find('input')
                .type('R*', { force: true })
                .clear()
            ObjectionComparableSales.elements.cvFromNetSalePrice
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvToNetSalePrice
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvFromSaleDates
                .should('be.visible')
                .clear()
                .type('01/07/2024', { force: true })
            ObjectionComparableSales.elements.cvToSaleDate
                .should('be.visible')
                .clear()
                .type('03/07/2024', { force: true })
            ObjectionComparableSales.elements.cvFromLandValue
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvToLandValue
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvLandZone
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvDistance
                .find('input')
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvFromTotalLivingArea
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvToTotalLivingArea
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvFromNetRate
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvToNetRate
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvFromEffectiveYearBuilt
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.cvToEffectiveYearBuilt
                .should('be.visible')
                .clear()
            ObjectionComparableSales.elements.comparablesSearch
                .contains('Search')
                .click({ force: true });
        });

        context('CV Comparable Sales ', () => {
            it('CV Comparables -Net rate Validation', () => {
                ObjectionJob.elements.objectionJobStepperComparableSales
                    .contains('Comparable Sales')
                    .click({ force: true });
                cy.wait(2000);
                ObjectionComparableSales.elements.cvComparablesTableBuildingNetRateColumnValues
                    .should('be.visible')
                    .contains('$2,626');
            });
        });

    })

});
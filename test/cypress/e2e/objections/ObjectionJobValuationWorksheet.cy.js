import ObjectionJob from '../../model/ObjectionJob';
import Model from '../../model/ValuationWorksheet.js';
import testFactory from '../../support/testFactory';

let ObjectionJobPage = '';

describe('Objection Job', function () {
    before('generate test factory objection', function () {
        const objectionInputBody = testFactory.getPropertyWithObjectionInputBody();

        cy.request(objectionInputBody).then((response) => {
            const attributes = response.body.data[0].children[0].attributes;
            ObjectionJobPage = ObjectionJob.valuationWorksheetObjectionJobPageURL(attributes.ratingValuationId);
        });;
    })

    context('Objection Job Valuation Worksheet and Save Job', function () {
        before('Visits page', function () {
            cy.visitWithLogin(ObjectionJobPage);
        });
        it('Filling in the Improvements details, Land, Adopted Values, and Saving the job', function () {

            // Improvement
            //Other Buildings
            Model.WorksheetTableOtherBuildings.row(0).valuePerSquareMetre.clear().type('100');
            Model.WorksheetTableOtherBuildings.row(0).area.clear().type('100');

            //Other Improvements
            Model.WorksheetTableOtherImprovements.row(0).valuePerSquareMetre.clear().type('100');
            Model.WorksheetTableOtherImprovements.row(0).area.clear().type('100');

            //Land
            Model.WorksheetTableLand.row(0).area.clear().type('100');
            Model.WorksheetTableLand.row(0).valuePerSquareMetre.clear().type('100');

            //Principal Buildings
            Model.WorksheetTablePrimaryBuildings.row(0).valuePerSquareMetre.clear().type('179280');
            Model.WorksheetTablePrimaryBuildings.row(0).area.clear().type('100');

            // Adopted Values
            Model.WorksheetTableAdoptedValues.capitalValue.clear().type('285000');
            Model.WorksheetTableAdoptedValues.landValue.clear().type('0');

            //Reload Page to check the saved values
            cy.visitWithLogin(ObjectionJobPage);
            Model.WorksheetTablePrimaryBuildings.row(0).valuePerSquareMetre.should('have.value', '179280');
            Model.WorksheetTablePrimaryBuildings.row(0).area.should('have.value', '100');
            Model.WorksheetTableAdoptedValues.capitalValue.clear().type('285000');
            Model.WorksheetTableAdoptedValues.landValue.clear().type('0');

        });
    });
});

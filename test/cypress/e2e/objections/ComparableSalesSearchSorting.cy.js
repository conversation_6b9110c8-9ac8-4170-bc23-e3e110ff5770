import ObjectionComparableSales from '../../model/ObjectionComparableSales';
import ObjectionJob from '../../model/ObjectionJob';
import testFactory from '../../support/testFactory';

let ObjectionJobPage = '';

describe('Objection Job - comparables', () => {
    before('generate test factory objection', function () {
        const objectionInputBody = testFactory.getPropertyWithObjectionInputBody();
        cy.request(objectionInputBody).then((response) => {
            const attributes = response.body.data[0].children[0].attributes;
            ObjectionJobPage = ObjectionJob.createObjectionJobPageURL(attributes.ratingValuationId);
            cy.wait(4000);
        });
    })

    context('Validating the CV comparable fields and LV comparables fields', () => {

        before('Visits page', () => {
            cy.visitWithLogin(ObjectionJobPage);
            ObjectionJob.elements.objectionJobStepperComparableSales
                .contains('Comparable Sales')
                .click({ force: true });
            cy.wait(2000);

        });
        context('CV Comparable Sales And LV comparable Sales', () => {

            it('CV Comparables - Categories field exists, is visible and editable', () => {
                ObjectionJob.elements.objectionJobStepperComparableSales
                    .contains('Comparable Sales')
                    .click({ force: true });
                cy.wait(2000);
                ObjectionComparableSales.elements.cvCategories
                    .contains('Categories')
                    .find('input')
                    .type('R*', { force: true })
                    .clear()
                    .type('R*', { force: true });
            });

            it('CV Comparables - Net Sale Price field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.cvNetSalePrice
                    .contains('Net Sale Price')
                ObjectionComparableSales.elements.cvFromNetSalePrice
                    .should('be.visible')
                    .type('30000', { force: true })
                    .clear()
                    .type('30000', { force: true });
                ObjectionComparableSales.elements.cvToNetSalePrice
                    .should('be.visible')
                    .type('80000', { force: true })
                    .clear()
                    .type('80000', { force: true });
            });

            it('CV Comparables - Sale Date field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.cvSaleDate
                    .contains('Sale Date')
                ObjectionComparableSales.elements.cvFromSaleDates
                    .should('be.visible')
                    .type('01/04/2022', { force: true })
                    .clear()
                    .type('01/04/2022', { force: true });
                ObjectionComparableSales.elements.cvToSaleDate
                    .should('be.visible')
                    .type('01/10/2022', { force: true })
                    .clear()
                    .type('01/10/2022', { force: true });
            });

            it('CV Comparables - Land value field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.cvLandValue
                    .contains('Land Value')
                ObjectionComparableSales.elements.cvFromLandValue
                    .should('be.visible')
                    .type('720000', { force: true })
                    .clear()
                    .type('720000', { force: true });
                ObjectionComparableSales.elements.cvToLandValue
                    .should('be.visible')
                    .type('1080000', { force: true })
                    .clear()
                    .type('1080000', { force: true });
            });

            it('CV Comparables - Distance field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.cvDistance
                    .contains('Distance')
                ObjectionComparableSales.elements.cvDistance
                    .find('input')
                    .should('be.visible')
                    .type('1000', { force: true })
                    .clear()
                    .type('1000', { force: true });
            });

            it('CV Comparables - Total Living Area field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.cvTotalLivingArea
                    .contains('Total Living Area')
                ObjectionComparableSales.elements.cvFromTotalLivingArea
                    .should('be.visible')
                    .type('96', { force: true })
                    .clear()
                    .type('96', { force: true });
                ObjectionComparableSales.elements.cvToTotalLivingArea
                    .should('be.visible')
                    .type('144', { force: true })
                    .clear()
                    .type('144', { force: true });
            });

            it('CV Comparables - Net Rate field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.cvNetRate
                    .contains('Net Rate')
                ObjectionComparableSales.elements.cvFromNetRate
                    .should('be.visible')
                    .type('96', { force: true })
                    .clear()
                    .type('96', { force: true });
                ObjectionComparableSales.elements.cvToNetRate
                    .should('be.visible')
                    .type('144', { force: true })
                    .clear()
                    .type('144', { force: true });
            });

            it('CV Comparables - Effective Year Built field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.cvEffectiveYearBuilt
                    .contains('Effective Year Built')
                ObjectionComparableSales.elements.cvFromEffectiveYearBuilt
                    .should('be.visible')
                    .type('1997', { force: true })
                    .clear()
                    .type('1997', { force: true });
                ObjectionComparableSales.elements.cvToEffectiveYearBuilt
                    .should('be.visible')
                    .type('2010', { force: true })
                    .clear()
                    .type('2010', { force: true });
            });

            it('CV Comparables - Comparables Search/Reset button exists and is visible', () => {
                ObjectionComparableSales.elements.comparablesReset
                    .should('be.visible')
                    .contains('Reset')
                    .click({ force: true });
                ObjectionComparableSales.elements.comparablesSearch
                    .should('be.visible')
                    .contains('Search')
                    .click({ force: true });
            });

            it('LV Comparables ', () => {
                ObjectionComparableSales.elements.lvComparables
                    .contains('LV Comparables')
                    .should('be.visible')
                    .click({ force: true });
            });

            it('LV Comparables - Categories field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.lvCategories
                    .contains('Categories')
                    .find('input')
                    .type('R*', { force: true })
                    .clear()
                    .type('R*', { force: true });
            });

            it('LV Comparables - Net Sale Price field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.lvNetSalePrice
                    .contains('Net Sale Price')
                ObjectionComparableSales.elements.lvFromNetSalePrice
                    .should('be.visible')
                    .type('30000', { force: true })
                    .clear()
                    .type('30000', { force: true });
                ObjectionComparableSales.elements.lvToNetSalePrice
                    .should('be.visible')
                    .type('80000', { force: true })
                    .clear()
                    .type('80000', { force: true });
            });

            it('LV Comparables - Sale Date field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.lvSaleDate
                    .contains('Sale Date')
                ObjectionComparableSales.elements.lvFromSaleDates
                    .should('be.visible')
                    .type('01/04/2022', { force: true })
                    .clear()
                    .type('01/04/2022', { force: true });
                ObjectionComparableSales.elements.lvToSaleDate
                    .should('be.visible')
                    .type('01/10/2022', { force: true })
                    .clear()
                    .type('01/10/2022', { force: true });
            });

            it('LV Comparables - Land value field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.lvLandValue
                    .contains('Land Value')
                ObjectionComparableSales.elements.lvFromLandValue
                    .should('be.visible')
                    .type('720000', { force: true })
                    .clear()
                    .type('720000', { force: true });
                ObjectionComparableSales.elements.lvToLandValue
                    .should('be.visible')
                    .type('1080000', { force: true })
                    .clear()
                    .type('1080000', { force: true });
            });
            it('LV Comparables - Land Zone field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.lvLandZone
                    .contains('Land Zone')
                    .find('input')
                    .type('1*', { force: true })
                    .clear({ force: true })
                    .type('1*', { force: true });
            });

            it('LV Comparables - Distance field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.lvDistance
                    .contains('Distance')
                ObjectionComparableSales.elements.lvDistance
                    .find('input')
                    .should('be.visible')
                    .type('5000', { force: true })
                    .clear()
                    .type('5000', { force: true });
            });

            it('LV Comparables - Land Area field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.lvLandArea
                    .contains('Land Area')
                ObjectionComparableSales.elements.lvFromLandArea
                    .should('be.visible')
                    .type('96', { force: true })
                    .clear()
                    .type('96', { force: true });
                ObjectionComparableSales.elements.lvToLandArea
                    .should('be.visible')
                    .type('144', { force: true })
                    .clear()
                    .type('144', { force: true });
            });

            it('LV Comparables - Land Sale Net Rate field exists, is visible and editable', () => {
                ObjectionComparableSales.elements.lvLandSaleNetRate
                    .contains('Net Rate')
                ObjectionComparableSales.elements.lvFromLandSaleNetRate
                    .should('be.visible')
                    .type('96', { force: true })
                    .clear()
                    .type('96', { force: true })
                    .clear()
                ObjectionComparableSales.elements.lvToNetLandSaleNetRate
                    .should('be.visible')
                    .type('144', { force: true })
                    .clear()
                    .type('144', { force: true })
                    .clear()
            });

            it('LV Comparables - Comparables Search/Reset button exists and is visible', () => {
                ObjectionComparableSales.elements.comparablesReset
                    .should('be.visible')
                    .contains('Reset')
                    .click({ force: true });
                ObjectionComparableSales.elements.comparablesSearch
                    .should('be.visible')
                    .contains('Search')
                    .click({ force: true });
            });


        });

    })
})
describe('sorting the CV comparables', () => {
    before('Visits page', () => {
        cy.visitWithLogin(ObjectionJobPage);
        ObjectionJob.elements.objectionJobStepperComparableSales
            .contains('Comparable Sales')
            .click({ force: true });
        cy.wait(2000);
        ObjectionJob.elements.objectionJobStepperComparableSales
            .contains('Comparable Sales')
            .click({ force: true });
        cy.wait(2000);
        ObjectionComparableSales.elements.cvCategories
            .contains('Categories')
            .find('input')
            .type('R*', { force: true })
            .clear()
            .type('R*', { force: true });
        ObjectionComparableSales.elements.cvFromNetSalePrice
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvToNetSalePrice
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvFromSaleDates
            .should('be.visible')
            .clear()
            .type('01/04/2022', { force: true })
        ObjectionComparableSales.elements.cvToSaleDate
            .should('be.visible')
            .clear()
            .type('01/06/2022', { force: true })
        ObjectionComparableSales.elements.cvFromLandValue
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvToLandValue
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvDistance
            .find('input')
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvFromTotalLivingArea
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvToTotalLivingArea
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvFromNetRate
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvToNetRate
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvFromEffectiveYearBuilt
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.cvToEffectiveYearBuilt
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.comparablesSearch
            .contains('Search')
            .click({ force: true });
    });

    context('Sorting the CV comparable', () => {
        it('Sorting the column by Address', () => {
            ObjectionComparableSales.elements.cvComparablesTableAddress
                .eq(1)
                .scrollIntoView()
                .contains('Address')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });
        it('Sorting the column by ValuationReference', () => {
            ObjectionComparableSales.elements.cvComparablesTableValuationReference
                .eq(1)
                .scrollIntoView()
                .contains('Val Ref')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });
        it('Sorting the column by SaleDate', () => {
            ObjectionComparableSales.elements.cvComparablesTableSaleDate
                .eq(1)
                .scrollIntoView()
                .contains('Sale Date')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });
        it('Sorting the column by Net Sale Price', () => {
            ObjectionComparableSales.elements.cvComparablesTableNetSalePrice
                .eq(1)
                .scrollIntoView()
                .contains('NSP')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by CurrentLandValue', () => {
            ObjectionComparableSales.elements.cvComparablesTableCurrentLandValue
                .eq(1)
                .scrollIntoView()
                .contains('Current LV')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });

        it('Sorting the column by LandArea', () => {
            ObjectionComparableSales.elements.cvComparablesTableLandArea
                .eq(1)
                .scrollIntoView()
                .contains('Land Area (Ha)')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by TotalFloorArea', () => {
            ObjectionComparableSales.elements.cvComparablesTableTotalFloorArea
                .eq(1)
                .scrollIntoView()
                .contains('TFA')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });

        it('Sorting the column by TotalLivingArea', () => {
            ObjectionComparableSales.elements.cvComparablesTableTotalLivingArea
                .eq(1)
                .scrollIntoView()
                .contains('TLA')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by UnderMainRoofGarages', () => {
            ObjectionComparableSales.elements.cvComparablesTableUnderMainRoofGarages
                .eq(1)
                .scrollIntoView()
                .contains('UMR')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });

        it('Sorting the column by FreestandingGarages', () => {
            ObjectionComparableSales.elements.cvComparablesTableFreestandingGarages
                .eq(1)
                .scrollIntoView()
                .contains('FS')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by Category', () => {
            ObjectionComparableSales.elements.cvComparablesTableCategory
                .eq(1)
                .scrollIntoView()
                .contains('Category')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });

        it('Sorting the column by BuildingNetRate', () => {
            ObjectionComparableSales.elements.cvComparablesTableBuildingNetRate
                .eq(1)
                .scrollIntoView()
                .contains('Net Rate')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by Distance', () => {
            ObjectionComparableSales.elements.cvComparablesTableDistance
                .eq(1)
                .scrollIntoView()
                .contains('Distance')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });
        it('Sorting the column by ComparabilityScore', () => {
            ObjectionComparableSales.elements.cvComparablesTableComparabilityScore
                .eq(1)
                .scrollIntoView()
                .contains('Match')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });
    });
});

describe('sorting the LV comparables', () => {
    before('Visits page', () => {
        cy.visitWithLogin(ObjectionJobPage);
        ObjectionJob.elements.objectionJobStepperComparableSales
            .contains('Comparable Sales')
            .click({ force: true });
        cy.wait(2000);
        ObjectionJob.elements.objectionJobStepperComparableSales
            .contains('Comparable Sales')
            .click({ force: true });
        cy.wait(2000);
        ObjectionComparableSales.elements.lvComparables
            .contains('LV Comparables')
            .should('be.visible')
            .click({ force: true });
        ObjectionComparableSales.elements.lvCategories
            .contains('Categories')
            .find('input')
            .clear()
            .type('RV*', { force: true })
        ObjectionComparableSales.elements.lvFromNetSalePrice
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.lvToNetSalePrice
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.lvFromSaleDates
            .should('be.visible')
            .clear()
            .type('01/04/2022', { force: true })
        ObjectionComparableSales.elements.lvToSaleDate
            .should('be.visible')
            .clear()
            .type('01/05/2022', { force: true })
        ObjectionComparableSales.elements.lvFromLandValue
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.lvToLandValue
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.lvLandZone
            .contains('Land Zone')
            .find('input')
            .clear({ force: true })
        ObjectionComparableSales.elements.lvDistance
            .find('input')
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.lvFromLandArea
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.lvToLandArea
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.lvFromLandSaleNetRate
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.lvToNetLandSaleNetRate
            .should('be.visible')
            .clear()
        ObjectionComparableSales.elements.comparablesSearch
            .contains('Search')
            .click({ force: true });
    });

    context('Sorting the LV comparable', () => {
        it('Sorting the column by Address', () => {
            ObjectionComparableSales.elements.lvComparablesTableAddress
                .eq(1)
                .scrollIntoView()
                .contains('Address')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');
        });
        it('Sorting the column by ValuationReference', () => {
            ObjectionComparableSales.elements.lvComparablesTableValuationReference
                .eq(1)
                .scrollIntoView()
                .contains('Val Ref')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });
        it('Sorting the column by SaleDate', () => {
            ObjectionComparableSales.elements.lvComparablesTableSaleDate
                .eq(1)
                .scrollIntoView()
                .contains('Sale Date')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });
        it('Sorting the column by SalePrice', () => {
            ObjectionComparableSales.elements.lvComparablesTableNetSalePrice
                .eq(1)
                .scrollIntoView()
                .contains('NSP')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by CurrentLandValue', () => {
            ObjectionComparableSales.elements.lvComparablesTableCurrentLandValue
                .eq(1)
                .scrollIntoView()
                .contains('Current LV')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });

        it('Sorting the column by LandArea', () => {
            ObjectionComparableSales.elements.lvComparablesTableLandArea
                .eq(1)
                .scrollIntoView()
                .contains('Land Area (Ha)')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by ZoneCode', () => {
            ObjectionComparableSales.elements.lvComparablesTableZoneCode
                .eq(1)
                .scrollIntoView()
                .contains('Zone')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });

        it('Sorting the column by ContourCode', () => {
            ObjectionComparableSales.elements.lvComparablesTableContourCode
                .eq(1)
                .scrollIntoView()
                .contains('Cntr')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by ViewCodeViewScopeCode', () => {
            ObjectionComparableSales.elements.lvComparablesTableViewCodeViewScopeCode
                .eq(1)
                .scrollIntoView()
                .contains('View/Scp')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });

        it('Sorting the column by LotPositionCode', () => {
            ObjectionComparableSales.elements.lvComparablesTableLotPositionCode
                .eq(1)
                .scrollIntoView()
                .contains('Lot Pos')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by PropertyCategory', () => {
            ObjectionComparableSales.elements.lvComparablesTablePropertyCategory
                .eq(1)
                .scrollIntoView()
                .contains('Category')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });

        it('Sorting the column by LandSaleNetRate', () => {
            ObjectionComparableSales.elements.lvComparablesTableLandSaleNetRate
                .eq(1)
                .scrollIntoView()
                .contains('Land SNR')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });

        it('Sorting the column by Distance', () => {
            ObjectionComparableSales.elements.lvComparablesTableDistance
                .eq(1)
                .scrollIntoView()
                .contains('Distance')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');

        });
        it('Sorting the column by ComparabilityScore', () => {
            ObjectionComparableSales.elements.lvComparablesTableComparabilityScore
                .eq(1)
                .scrollIntoView()
                .contains('Match')
                .click()
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');

        });
    });
});

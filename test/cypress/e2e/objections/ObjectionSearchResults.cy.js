import ObjectionSearchPage from '../../model/ObjectionSearchPage';
import ObjectionSearchResults from '../../model/ObjectionSearchResults';

const ObjectionsPage = 'roll-maintenance/objections';
const propertyQpids = {
    maoriLand: 475804,
    invalidApportionment: 3409302,
    invalidAssessmentStatus: 3249915,
    sameRollAndAssessmentNumber: 2565911,
    nonResidential: 1047494,
    maintenance: 3395341
}
const actionButtonTexts = {
    editValuation: 'EDIT VALUATION',
    qivs: 'QIVSCALL_MADE'
}

describe('Objection Search Results', () => {
    before(() => {
        cy.visitWithLogin(ObjectionsPage);
        ObjectionSearchResults.elements.objectionSearchTable.find('td').should('have.length.at.least', 100);
        ObjectionSearchPage.elements.valuationJobStatusesClear.click();
        ObjectionSearchPage.elements.administrationStatusClear.click();
    });
    context('Objection list action button', () => {
        it('Should display Edit Valuation button for a valid Maori property', () => {
            ObjectionSearchPage.elements.qpidInput.type(propertyQpids.maoriLand);
            ObjectionSearchPage.elements.objectionSearch.click();
            ObjectionSearchResults.elements.objectionActionButton
                .should('exist')
                .and('be.visible')
                .invoke('text')
                .then((text) => text.trim().toUpperCase())
                .should('eq', actionButtonTexts.editValuation);
        });
        it('Should display QIVS button for residential assessments that are NOT apportionment code 0, 2 or 5', () => {
            ObjectionSearchPage.elements.qpidInput.clear().type(propertyQpids.invalidApportionment);
            ObjectionSearchPage.elements.objectionSearch.click();
            ObjectionSearchResults.elements.objectionActionButton
                .should('exist')
                .and('be.visible')
                .invoke('text')
                .then((text) => text.trim().toUpperCase())
                .should('eq', actionButtonTexts.qivs);
        });
        it('Should display QIVS button for residential assessments that that are assessment status I', () => {
            ObjectionSearchPage.elements.qpidInput.clear().type(propertyQpids.invalidAssessmentStatus);
            ObjectionSearchPage.elements.objectionSearch.click();
            ObjectionSearchResults.elements.objectionActionButton
                .should('exist')
                .and('be.visible')
                .invoke('text')
                .then((text) => text.trim().toUpperCase())
                .should('eq', actionButtonTexts.qivs);
        });
        it('Should display QIVS button for residential assessments that are apportionment code 2, and there are assessments with the same Roll Number and Assessment Number that are apportionment code 1', () => {
            ObjectionSearchPage.elements.qpidInput.clear().type(propertyQpids.sameRollAndAssessmentNumber);
            ObjectionSearchPage.elements.objectionSearch.click();
            ObjectionSearchResults.elements.objectionActionButton
                .should('exist')
                .and('be.visible')
                .invoke('text')
                .then((text) => text.trim().toUpperCase())
                .should('eq', actionButtonTexts.qivs);
        });
        it('Should display QIVS button for non-residential assessments', () => {
            ObjectionSearchPage.elements.qpidInput.clear().type(propertyQpids.nonResidential);
            ObjectionSearchPage.elements.objectionSearch.click();
            ObjectionSearchResults.elements.objectionActionButton
                .should('exist')
                .and('be.visible')
                .invoke('text')
                .then((text) => text.trim().toUpperCase())
                .should('eq', actionButtonTexts.qivs);
        });
        it('Should display QIVS button for maintenance assessments', () => {
            ObjectionSearchPage.elements.objectionTypeSelector.click();
            ObjectionSearchPage.elements.objectionTypeSelector.find('li').first().click();
            ObjectionSearchPage.elements.qpidInput.clear().type(propertyQpids.maintenance);
            ObjectionSearchPage.elements.objectionSearch.click();
            ObjectionSearchResults.elements.objectionActionButton
                .should('exist')
                .and('be.visible')
                .invoke('text')
                .then((text) => text.trim().toUpperCase())
                .should('eq', actionButtonTexts.qivs);
        });
    });
});
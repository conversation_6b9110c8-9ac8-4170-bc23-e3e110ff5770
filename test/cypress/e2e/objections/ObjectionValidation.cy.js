import ObjectionJob from '../../model/ObjectionJob';

describe('Objection Job', function () {
    context('Objection validation test', function () {
        before('Visits page', function () {
            const ratingValuationId = '7b64ddd8-0903-4771-b74a-c3052a635534';
            const ObjectionJobPage = ObjectionJob.createObjectionJobDraftPropertyURL(ratingValuationId);
            cy.visitWithLogin(ObjectionJobPage);
        });
        context('Draft Property Details ', function () {
            it('Should pop up error modal if there are validation errors', function () {
                ObjectionJob.elements.effectiveLandAreaInput.should('be.visible').clear().type(2);
                cy.wait(1000);
                ObjectionJob.elements.saveAsDraftButton.should('be.visible').click();
                ObjectionJob.elements.errorsPopUp.should('be.visible');
                ObjectionJob.elements.cancelButton.should('be.visible').click();
            });
            it('Should pop up warning modal if there is no error but there are warnings', function () {
                ObjectionJob.elements.effectiveLandAreaInput.should('be.visible').clear();
                cy.wait(1000);
                ObjectionJob.elements.saveAsDraftButton.should('be.visible').click();
                ObjectionJob.elements.warningsPopUp.should('be.visible');
                ObjectionJob.elements.confirmButton.should('be.visible');
            });
        });
    })
})

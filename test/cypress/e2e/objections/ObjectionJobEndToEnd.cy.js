import ObjectionJob from '../../model/ObjectionJob';
import testFactory from '../../support/testFactory';

let ObjectionJobPage = '';
let ObjectionJobPageDVR = '';

describe('Objection Job', function () {
    const generateRandomNumber = () => Cypress._.random(0, 99)
    const id = generateRandomNumber()
    const buildingLabel = `D${id}`

    before('generate test factory objection', function () {
        const objectionInputBody = testFactory.getPropertyWithObjectionInputBody();

        cy.request(objectionInputBody).then((response) => {
            const attributes = response.body.data[0].children[0].attributes;
            ObjectionJobPage = ObjectionJob.createObjectionJobPageURL(attributes.ratingValuationId);
        });;
    })

    context('Objection end to end Job Testing', function () {
        let testData;
        let userData;
        before('Visits page', function () {
            cy.visitWithLogin(ObjectionJobPage);
            cy.fixture('objection-job-testdata').then(function (inputTestData) {
                testData = inputTestData;
            })
            cy.fixture('objection-job-inputdata').then(function (inputTestData) {
                userData = inputTestData;
            })
        });
        context('Draft Property Details ', function () {
            context('General Property Information ', function () {
                it('Objections section Expandable', function () {
                    ObjectionJob.elements.expandAll.eq(0)
                        .should('be.visible')
                        .click({ force: true })
                        .should('have.attr', 'class', 'expandAll')
                        .click({ force: true })
                        .should('have.attr', 'class', 'expandAll down')
                    ObjectionJob.elements.recentWork
                        .should('be.visible')
                        .contains('Recent Work')
                        .click({ force: true });
                });

                it('Valuation Conclusions section Expandable', function () {
                    ObjectionJob.elements.expandAll.eq(1)
                        .should('be.visible')
                        .click({ force: true })
                        .should('have.attr', 'class', 'expandAll')
                        .click({ force: true })
                        .should('have.attr', 'class', 'expandAll down');
                    ObjectionJob.elements.updatedPropertyDescription
                        .should('be.visible')
                        .contains('Updated Property Description')
                        .click({ force: true });
                });

                it('Delete objection job button exists, is visible, and able to click', function () {
                    const expectedTexts = ['Delete Objection Job',
                        'Warning: This will delete the current objection job including any updated draft property details. Are you sure?',
                        'Note: This will not delete the Objection, just the Objection Job.', 'NO, RETURN TO OBJECTION JOB', 'YES, DELETE OBJECTION JOB'];
                    ObjectionJob.elements.deleteObjectionJobButton
                        .scrollIntoView()
                        .should('be.visible')
                        .contains('Delete Objection Job')
                        .click({ force: true });
                    ObjectionJob.elements.alertModelDeleteObjectionJob
                        .should('exist')
                        .and('be.visible')
                        .and($element => {
                            expectedTexts.forEach(text => {
                                expect($element).to.contain(text.trim());
                            });
                        });
                    ObjectionJob.elements.alertModelDeleteObjectionJob
                        .find('button').eq(0)
                        .should('exist')
                        .contains('NO, RETURN TO OBJECTION JOB')
                        .click({ force: true });
                });
                context("get DVR Data property, needs to be code ['RB', 'RM', 'RV']", function () {
                    before('Visits page', function () {
                        const objectionInputBody = testFactory.getPropertyWithObjectionInputBody('RB');
                        cy.request(objectionInputBody).then((response) => {
                            const attributes = response.body.data[0].children[0].attributes;
                            ObjectionJobPageDVR = ObjectionJob.createObjectionJobPageURL(attributes.ratingValuationId);
                        });;
                    });
                    it('General Property Information - Populate DVR Data field exists, is visible, readonly', function () {
                        cy.visitWithLogin(ObjectionJobPageDVR);
                        ObjectionJob.elements.populateDvrDataButton
                        .should('be.visible')
                        .contains('Populate DVR Data')
                        .click({ force: true });
                    });
                    after('Visits page', function () {
                        cy.visitWithLogin(ObjectionJobPage);
                    });
                })

                it('General Property Information - Category field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.draftPropertyCategoryValueExist,
                        testData.categoryInput,
                        ObjectionJob.elements.draftPropertyCategoryDropDown,
                    ).then((text) => {
                        userData.categoryData = text;
                        ObjectionJob.elements.draftPropertyCategoryValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.categoryData);
                        });
                    });
                });

                it('General Property Information - Nature of Improvements field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.draftPropertyNatureOfImprovementValueExist,
                        testData.natureOfImprovementInput,
                        ObjectionJob.elements.draftPropertyNatureOfImprovementDropdown,
                    ).then((text) => {
                        userData.natureOfImprovementData = text;
                        ObjectionJob.elements.draftPropertyNatureOfImprovementValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.natureOfImprovementData);
                        });
                    });
                });

                it('General Property Information - Land Use field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.draftPropertyLandUseValueExist,
                        testData.landUseInput,
                        ObjectionJob.elements.draftPropertyLandUseDropDown,
                    ).then((text) => {
                        userData.landUseData = text;
                        ObjectionJob.elements.draftPropertyLandUseValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.landUseData);
                        });
                    });
                });

                it('General Property Information - TA Land Zone field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.draftPropertyTALandZoneValueExist,
                        testData.tALandZoneInput,
                        ObjectionJob.elements.draftPropertyTALandZoneDropDown,
                    ).then((text) => {
                        userData.tALandZoneData = text;
                        ObjectionJob.elements.draftPropertyTALandZoneValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.tALandZoneData);
                        });
                    });
                });

                it('General Property Information - Effective Land Area field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.draftPropertyEffectiveLandArea
                        .type('0.0401', { force: true })
                        .clear()
                        .type('0.0401', { force: true });
                });

                it('General Property Information - Land Area field exists, is visible, readonly', function () {
                    ObjectionJob.elements.draftPropertyLandArea
                        .should('have.attr', 'readonly', 'readonly');
                });

                it('General Property Information - Maori Land field exists, is visible, readonly', function () {
                    ObjectionJob.elements.draftPropertyMaoriLand
                        .should('have.attr', 'readonly', 'readonly');
                });

                it('General Property Information - Draft Property Plan Id field exists, is visible, readonly', function () {
                    ObjectionJob.elements.draftPropertyPlanId
                        .should('have.attr', 'readonly', 'readonly');
                });

                it('General Property Information - Draft Property Producation field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.draftPropertyProducation
                        .type('1', { force: true }).clear().type('0', { force: true });
                });

            });
            context('Location Details', function () {
                it('Location Details -lot Position field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.lotPositionValueExist,
                        testData.lotPositionInput,
                        ObjectionJob.elements.lotDropDownValues,
                    ).then((text) => {
                        userData.lotPositionData = text;
                        ObjectionJob.elements.lotPositionValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.lotPositionData);
                        });
                    });
                });

                it('Location Details - Contour field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.contourValueExist,
                        testData.contourInput,
                        ObjectionJob.elements.contourDownValues,
                    ).then((text) => {
                        userData.contourData = text;
                        ObjectionJob.elements.contourValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.contourData);
                        });
                    });
                });

                it('Location Details - View field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.viewCheckValueExist,
                        testData.viewInput,
                        ObjectionJob.elements.viewDropDownValues,
                    ).then((text) => {
                        userData.viewData = text;
                        ObjectionJob.elements.viewCheckValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.viewData);
                        });
                    });
                });

                it('Location Details - View Scope field exists, is visible, selectable, and validates the selected value. ', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.viewScopeValueExist,
                        testData.viewScopeInput,
                        ObjectionJob.elements.viewScopeDropDown,
                    ).then((text) => {
                        userData.viewScopeData = text;
                        ObjectionJob.elements.viewScopeValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.viewScopeData);
                        });
                    });
                });

                it('Location Details -  Class of Surrounding Improvements (CSI) field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.csiValueExist,
                        testData.csiInput,
                        ObjectionJob.elements.csiDropDownValues,
                    ).then((text) => {
                        userData.csiData = text;
                        ObjectionJob.elements.csiValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.csiData);
                        });
                    });
                });

                it('Location Details -  Outlier field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.outlierValueExist,
                        testData.outlierInput,
                        ObjectionJob.elements.outlierDropDownValues,
                    ).then((text) => {
                        userData.outlierData = text;
                        ObjectionJob.elements.outlierValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.outlierData);
                        });
                    });
                });
            });
            context('Property Summary', function () {
                it('Property Summary -  houseType field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.houseTypeValueExist,
                        testData.houseTypeInput,
                        ObjectionJob.elements.houseTypeDropDown,
                    ).then((text) => {
                        userData.houseTypeData = text;
                        ObjectionJob.elements.houseTypeValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.houseTypeData);
                        });
                    });
                });

                it('Property Summary -  unitOfUse field exists, is visible, editable, and validates the entered value. ', function () {
                    ObjectionJob.elements.unitOfUse
                        .type('1', { force: true }).clear().type('1', { force: true });
                });

                it('Property Summary -  age field exists, is visible, selectable, and validates the selected value. ', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.ageValueExist,
                        testData.ageInput,
                        ObjectionJob.elements.ageDropDown,
                    ).then((text) => {
                        userData.ageInputData = text;
                        ObjectionJob.elements.ageValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.ageInputData);
                        });
                    });
                });

                it('Property Summary -  effectiveYearBuilt field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.effectiveYearBuilt
                        .type('1', { force: true }).clear().type('2021', { force: true });
                });

                it('Property Summary -  poorFdn field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.poorfdnValueExist,
                        testData.poorfdnInput,
                        ObjectionJob.elements.poorfdnDropDownValues,
                    ).then((text) => {
                        userData.poorfdnData = text;
                        ObjectionJob.elements.poorfdnValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.poorfdnData);
                        });
                    });
                });

                it('Property Summary -  totalBderms field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.totalBderms
                        .type('3', { force: true }).clear().type('3', { force: true });
                });

                it('Property Summary -  totalBathrms field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.totalBathrms
                        .type('1', { force: true }).clear().type('1', { force: true });
                });

                it('Property Summary -  totalToilets field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.totaltoilets
                        .type('1', { force: true }).clear().type('1', { force: true });
                });

                it('Property Summary -  buildingSiteCover field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.buildingSiteCover
                        .type('150', { force: true }).clear().type('150', { force: true });
                });

                it('Property Summary -  totalFloorArea field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.totalFloorArea
                        .type('200', { force: true }).clear().type('200', { force: true });
                });

                it('Property Summary -  mainLivingArea field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.mainLivingArea
                        .type('50', { force: true }).clear().type('50', { force: true });
                });

                it('Property Summary -  totalLivingArea field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.totalLivingArea
                        .type('120', { force: true }).clear().type('120', { force: true });
                });

                it('Property Summary -  laundryAndWorkshop field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.laundryAndWorkshopValueExist,
                        testData.laundryAndWorkshopInput,
                        ObjectionJob.elements.laundryAndWorkshopDropDown,
                    ).then((text) => {
                        userData.ldyAndWorkshopData = text;
                        ObjectionJob.elements.laundryAndWorkshopValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.ldyAndWorkshopData);
                        });
                    });
                });

                it('Property Summary -  carAccess field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.carAccessValueExist,
                        testData.carAccessInput,
                        ObjectionJob.elements.carAccessDropDown,
                    ).then((text) => {
                        userData.carAccessData = text;
                        ObjectionJob.elements.carAccessValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.carAccessData);
                        });
                    });
                });

                it('Property Summary -  driveWay field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.driveWayValueExist,
                        testData.driveWayInput,
                        ObjectionJob.elements.driveWayDropDown,
                    ).then((text) => {
                        userData.driveWayData = text;
                        ObjectionJob.elements.driveWayValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.driveWayData);
                        });
                    });
                });

                it('Property Summary -  carParks field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.carParks
                        .type('4', { force: true }).clear().type('4', { force: true });
                    ObjectionJob.elements.saveButtonforRegenerateBuilding
                        .should('be.visible')
                        .contains('Save')
                        .click({ force: true });
                    cy.wait(2000);
                    ObjectionJob.elements.alertButtonClose
                        .contains('Close').click({ force: true });
                });
            });
            context('Construction Information', function () {
                it('Construction Information - Add Building and remove building  button exists, is visible, and able to click ', function () {
                    ObjectionJob.elements.addBuildingRow
                        .scrollIntoView()
                        .should('be.visible')
                        .should('have.attr', 'title', 'Add a Building')
                        .click({ force: true });
                    ObjectionJob.elements.removeBuildingRow.eq(1)
                        .should('be.visible')
                        .should('have.attr', 'title', 'Remove a Building')
                        .click({ force: true });
                });

                it('Construction Information - copy Building and remove building button exists, is visible, and able to click ', function () {
                    ObjectionJob.elements.copyBuildingRow.eq(0)
                        .should('be.visible')
                        .should('have.attr', 'title', 'Duplicate this building')
                        .click({ force: true });
                    ObjectionJob.elements.removeBuildingRow.eq(1)
                        .should('be.visible')
                        .should('have.attr', 'title', 'Remove a Building')
                        .click({ force: true });
                });

                it('Construction Information -  Types Of Building field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.typesOfBuildingValueExist,
                        testData.typesOfBuildingInput,
                        ObjectionJob.elements.typesOfBuildingDropDown,
                    ).then((text) => {
                        userData.typesOfBuildingData = text;
                        ObjectionJob.elements.typesOfBuildingValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.typesOfBuildingData);
                        });
                    });
                });

                it('Construction Information -  floorArea field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.floorArea.eq(0)
                        .type('100', { force: true }).clear().type('100', { force: true });
                });

                it('Construction Information -  noOfStoreys field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.noOfStoreys.eq(0)
                        .type('100', { force: true }).clear().type('1', { force: true });
                });

                it('Construction Information -  yearBuilt field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.yearBuilt.eq(0)
                        .type('100', { force: true }).clear().type('2022', { force: true });
                });

                it('Construction Information -  Description field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.description.eq(0)
                        .type('100', { force: true }).clear().type('Types of building(Automation)', { force: true });
                });

                it('Construction Information -  buildingLabel field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.buildingLabel.eq(0)
                        .type('100', { force: true }).clear().type(buildingLabel, { force: true });
                });

                it('Construction Information -  principalBuilding field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.principalBuildingValueExist,
                        testData.principalBldgInput,
                        ObjectionJob.elements.principalBuildingDropDown,
                    ).then((text) => {
                        userData.principalBldgData = text;
                        ObjectionJob.elements.principalBuildingValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.principalBldgData);
                        });
                    });
                });

                it('Construction Information -  wallConstruction field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.wallConstructionValueExist,
                        testData.wallConstructionInput,
                        ObjectionJob.elements.wallConstructionDropDown,
                    ).then((text) => {
                        userData.wallConstructionData = text;
                        ObjectionJob.elements.wallConstructionValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.wallConstructionData);
                        });
                    });
                });

                it('Construction Information -  wallCondition field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.wallConditionValueExist,
                        testData.wallConditionInput,
                        ObjectionJob.elements.wallConditionDropDown,
                    ).then((text) => {
                        userData.wallConditionData = text;
                        ObjectionJob.elements.wallConditionValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.wallConditionData);
                        });
                    });
                });

                it('Construction Information -  roofConstruction field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.roofConstructionValueExist,
                        testData.roofConstructionInput,
                        ObjectionJob.elements.roofConstructionDropDown,
                    ).then((text) => {
                        userData.roofConstructionData = text;
                        ObjectionJob.elements.roofConstructionValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.roofConstructionData);
                        });
                    });
                });

                it('Construction Information -  roofCondition field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.roofConditionValueExist,
                        testData.roofConditionInput,
                        ObjectionJob.elements.roofConditionDropDown,
                    ).then((text) => {
                        userData.roofConditionData = text;
                        ObjectionJob.elements.roofConditionValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.roofConditionData);
                        });
                    });
                });

                it('Construction Information -  floorConstruction field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.floorConstructionValueExist,
                        testData.floorConstructionInput,
                        ObjectionJob.elements.floorConstructionDropDown,
                    ).then((text) => {
                        userData.floorConstructionData = text;
                        ObjectionJob.elements.floorConstructionValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.floorConstructionData);
                        });
                    });
                });

                it('Construction Information -  foundation field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.foundationValueExist,
                        testData.foundationInput,
                        ObjectionJob.elements.foundationDropDown,
                    ).then((text) => {
                        userData.foundationData = text;
                        ObjectionJob.elements.foundationValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.foundationData);
                        });
                    });
                });

                it('Construction Information -  wiringAge field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.wiringAgeValueExist,
                        testData.wiringAgeInput,
                        ObjectionJob.elements.wiringAgeDropDown,
                    ).then((text) => {
                        userData.wiringAgeData = text;
                        ObjectionJob.elements.wiringAgeValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.wiringAgeData);
                        });
                    });
                });

                it('Construction Information -  plumbingAge field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.plumbingAgeValueExist,
                        testData.plumbingAgeInput,
                        ObjectionJob.elements.plumbingAgeDropDown,
                    ).then((text) => {
                        userData.plumbingAgeData = text;
                        ObjectionJob.elements.plumbingAgeValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.plumbingAgeData);
                        });
                    });
                });

                it('Construction Information -  insulation field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.insulationValueExist,
                        testData.insulationInput,
                        ObjectionJob.elements.insulationDropDown,
                    ).then((text) => {
                        userData.insulationData = text;
                        ObjectionJob.elements.insulationValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.insulationData);
                        });
                    });
                });

                it('Construction Information -  glazing field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.glazingValueExist,
                        testData.glazingInput,
                        ObjectionJob.elements.glazingDropDown,
                    ).then((text) => {
                        userData.glazingData = text;
                        ObjectionJob.elements.glazingValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.glazingData);
                        });
                    });
                });

                it('Construction Information -  otherFeatures field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.otherFeaturesCiValueExist,
                        testData.otherFeaturesCiInput,
                        ObjectionJob.elements.otherFeaturesCiDropDown,
                    ).then((text) => {
                        userData.otherFeaturesCiData = text;
                        ObjectionJob.elements.otherFeaturesCiValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.otherFeaturesCiData);
                        });
                    });
                });
            });
            context('Spaces', function () {
                it('Spaces - Add Space and remove Space  button exists, is visible, and able to click ', function () {
                    ObjectionJob.elements.addSpaceRow
                        .scrollIntoView()
                        .should('be.visible')
                        .should('have.attr', 'title', 'Add a Space to a Building')
                        .click({ force: true });
                    cy.reload();
                    ObjectionJob.elements.removeGarageSpaceRow.eq(0)
                        .should('be.visible')
                        .should('have.attr', 'title', 'Remove a Space')
                        .click({ force: true });
                });

                it('Spaces - copy Space and remove Space button exists, is visible, and able to click ', function () {
                    // ObjectionJob.elements.spaces.buildingSelect.click({ force: true });
                    // ObjectionJob.elements.spaces.buildingSelectOption.click({ force: true });
                    // ObjectionJob.elements.spaces.storageSelect.click({ force: true });
                    // ObjectionJob.elements.spaces.storageSelectOption.click({ force: true });
                    ObjectionJob.elements.copyDwellingSpaceRow
                        .scrollIntoView()
                        .should('be.visible')
                        .should('have.attr', 'title', 'Duplicate this space')
                        .click({ force: true });
                    ObjectionJob.elements.removeDwellingSpaceRow.eq(1)
                        .scrollIntoView()
                        .should('be.visible')
                        .should('have.attr', 'title', 'Remove a Space')
                        .click({ force: true });
                });
            });
            context('Site Improvements', function () {
                //siteImprovements
                it('Site Improvements -  siteImprovement field exists, is visible, editable, and validates the entered value.', function () {
                    ObjectionJob.elements.siteOtherImprovement
                        .should('have.attr', 'readonly', 'readonly');
                });

                it('Site Improvements -  siteImprovementQuality field exists, is visible, selectable, and validates the selected value.', function () {
                    ObjectionJob.dropDownBox(
                        ObjectionJob.elements.siteImprovementQualityValueExist,
                        testData.siteImprovementQualityInput,
                        ObjectionJob.elements.siteImprovementQualityDropDown,
                    ).then((text) => {
                        userData.siteImprovementQualityData = text;
                        ObjectionJob.elements.siteImprovementQualityValueExist.invoke('text').then((text) => {
                            const trimmedText = text.trim();
                            expect(trimmedText).to.equal(userData.siteImprovementQualityData);
                        });
                    });
                });

                it('Site Improvements - Add improvements and remove Space  button exists, is visible, and able to click ', function () {
                    ObjectionJob.elements.addImprovementRow
                        .scrollIntoView()
                        .should('be.visible')
                        .contains('Add Improvement')
                        .click({ force: true });
                    ObjectionJob.elements.removeImprovementRow.eq(0)
                        .should('be.visible')
                        .contains('Remove')
                        .click({ force: true });

                });

                it('Setup Complete -  setupButton button exists, is visible, able to click', function () {
                    ObjectionJob.elements.saveAsDraftButton
                        .scrollIntoView()
                        .contains('Save as Draft')
                        .click({ force: true });
                    cy.wait(2000);
                    ObjectionJob.elements.setupCompleteAndValueButton
                        .contains('Setup Complete & Value')
                        .click({ force: true });
                    cy.wait(2000);
                    ObjectionJob.elements.alertButtonClose
                        .contains('Close').click({ force: true });
                });
            });

        });
        context('Comparable Sales', function () {

            it('Comparable Sales -  Comparable Sales field exists, is visible and able to clikc', function () {
                ObjectionJob.elements.objectionJobStepperComparableSales
                    .contains('Comparable Sales').click({ force: true });
                cy.wait(2000);
            });

            it('Comparable Sales-  Comparable Sales - Adding  comments field exists, is visible, editable, and validates the entered value.', function () {
                ObjectionJob.elements.objectionJobStepperComparableSales
                    .contains('Comparable Sales').click({ force: true });
                cy.wait(2000);
                ObjectionJob.elements.comparableSalesSearchOptions.categories.clear({force: true});
                ObjectionJob.elements.comparableSalesSearchOptions.distance.clear({force: true});
                ObjectionJob.elements.comparableSalesSearchOptions.searchButton.click({force: true});
                cy.wait(1000);
                ObjectionJob.elements.comparableSalesResults.firstResult.click({force: true});
                cy.wait(1000);
                ObjectionJob.elements.comparableSalesAllSaleComment
                    .each(($el) => {
                        cy.wrap($el)
                            .clear()
                            .type('Test', { force: true });
                        cy.wait(1000);
                    });
            });

            it('Comparable Sales -  Comparable Sales -field exists, is visible, selectable, and validates the selected value.', function () {
                ObjectionJob.elements.objectionJobStepperComparableSales
                    .contains('Comparable Sales').click({ force: true });
                cy.wait(2000);
                ObjectionJob.elements.comparableSalesAllComparabilityDropDown
                    .each(($el) => {
                        cy.wrap($el)
                            .select('Comparable', { force: true });
                        cy.wait(500);
                    });
            });
        });
        context('Valuation', function () {

            it('Valuation - Valuation field exists, is visible and able to click ', function () {
                ObjectionJob.elements.objectionJobStepperValuation
                    .contains('Valuation').click({ force: true });
                cy.wait(1000);
            });


        });
        context('Job Completion', function () {
            it('Job Completion - Job Completion exists, is visible and able to click', function () {
                ObjectionJob.elements.objectionStepperJobCompletion
                    .contains('Job Completion').click({ force: true });
                cy.wait(500);
            });

            it('Objection Job - Job Completion - Job Valuer field exists, is visible, selectable, and validates the selected value.', function () {
                ObjectionJob.dropDownBox(
                    ObjectionJob.elements.jobValuerValueExist,
                    testData.jobValuerInput,
                    ObjectionJob.elements.jobValuerDropDown,
                ).then((text) => {
                    userData.jobValuerData = text;
                    ObjectionJob.elements.jobValuerValueExist.invoke('text').then((text) => {
                        const trimmedText = text.trim();
                        expect(trimmedText).to.equal(userData.jobValuerData);
                    });
                });
            });
        });
        context('Assigning an Objection Valuation Review Job from Non-Registered Valuers to Registered Valuers', function () {
            it('should successfully assign a Non-Registered Valuer to a Registered Valuer and send the job for review', function () {
                ObjectionJob.elements.objectionStepperJobCompletion
                    .contains('Job Completion').click({ force: true });
                cy.wait(500);
                ObjectionJob.elements.jobValuerDropDownType
                    .type('TestUserInt1', { force: true });
                ObjectionJob.elements.registeredJobValuerDropDownType
                    .type('TestUserInt2', { force: true });
                ObjectionJob.elements.objectionSaveAsDraft
                    .contains('Save as Draft').click({ force: true });
                cy.wait(1000);
                ObjectionJob.elements.objectionRequestReviewButton
                    .contains('Request Review').click({ force: true });
                ObjectionJob.elements.notesForRegisteredValuer
                    .type('Test', { force: true });
                ObjectionJob.elements.riskType.type('High', { force: true });
                ObjectionJob.elements.objectionValuationSendToReview
                    .click({ force: true });
                ObjectionJob.elements.alertButtonClose
                    .contains('Yes, Send to Review').click({ force: true });
            });

            it('JobCompletion -Save as Draft - Complete Valuation field exists, is visible and able to click', function () {
                ObjectionJob.elements.objectionSaveAsDraft
                    .contains('Save as Draft').click({ force: true });
                cy.wait(1000);
                ObjectionJob.elements.objectionCompleteValuationButton
                    .contains('Complete Valuation').click({ force: true });
            });
        });
    })
})

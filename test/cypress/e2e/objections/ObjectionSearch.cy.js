import ObjectionSearchPage from '../../model/ObjectionSearchPage';
import Reports from '../../model/Reports';
const ObjectionsPage = 'roll-maintenance/objections';
let valuerName = 'NULL';
let registeredValuerName = 'NULL';

describe('Objection Search', () => {
    before(() => {
        cy.visitWithLogin(ObjectionsPage);
    });
    context('Objection Type', () => {
        it('Should validate the content of the objection type section', () => {
            ObjectionSearchPage.elements.objectionType
                .should('be.visible')
                .contains('Objection Type');
        });
        it('should validate the objection type options', () => {
            ObjectionSearchPage.elements.objectionTypeDropdown
                .then(($objectionTypeDropdown) => {
                    expect($objectionTypeDropdown.eq(0)).to.contain('Maintenance');
                    expect($objectionTypeDropdown.eq(1)).to.contain('Revision');
                });
        });

        it('Validate that the selected objection type is "Maintenance" and verify that the search results are highlighted in yellow.', () => {
            ObjectionSearchPage.elements.objectionTypeDropdown
                .contains('Maintenance')
                .click({ force: true })
                .then(() => {
                    ObjectionSearchPage.elements.objectionTypeInput.invoke('text').then((text) => {
                        const trimmedText = text.trim();
                        expect(trimmedText).to.equal('Maintenance');
                    });
                });
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.administativeStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            ObjectionSearchPage.elements.objectionSearchTableRow
                .should('be.visible');
        });

        it('should validate the selected objection type as revision', () => {
            ObjectionSearchPage.elements.objectionTypeDropdown
                .contains('Revision')
                .click({ force: true })
                .then(() => {
                    ObjectionSearchPage.elements.objectionTypeInput.invoke('text').then((text) => {
                        const trimmedText = text.trim();
                        expect(trimmedText).to.equal('Revision');
                    });
                });
        });
    });

    context('Valuation Job Statuses', () => {
        it('should validate the content of the Valuation Job Statuses sections', () => {
            ObjectionSearchPage.elements.valuationJobStatuses
                .should('be.visible')
                .contains('Valuation Job Statuses');
        });

        it('should validate the valuation Job Statuses all active', () => {
            ObjectionSearchPage.elements.valuationJobStatusesAllActive
                .should('be.visible')
                .contains('All Active')
                .click({ force: true });

        });
        it('should validate the valuation Job Statuses clear button', () => {
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });

        });

        it('should validate the valuation job statuses options', () => {
            ObjectionSearchPage.elements.valuationJobStatusesDropdown
                .then(($objectionTypeDropdown) => {
                    expect($objectionTypeDropdown.eq(0)).to.contain('Deleted');
                    expect($objectionTypeDropdown.eq(1)).to.contain('Further Contact Required');
                    expect($objectionTypeDropdown.eq(2)).to.contain('Not Ready to Value');
                    expect($objectionTypeDropdown.eq(3)).to.contain('Ready to Value');
                    expect($objectionTypeDropdown.eq(4)).to.contain('Registered Valuer Review');
                    expect($objectionTypeDropdown.eq(5)).to.contain('Rejected');
                    expect($objectionTypeDropdown.eq(6)).to.contain('Review Failed');
                    expect($objectionTypeDropdown.eq(7)).to.contain('TA Sign-off');
                    expect($objectionTypeDropdown.eq(8)).to.contain('Valued/Actioned');
                    expect($objectionTypeDropdown.eq(9)).to.contain('Withdrawn');
                });
        });

        it('should validate the selected valuation job statuses as Ready to Value', () => {
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.dropDownBox(
                ObjectionSearchPage.elements.valuationJobStatusesInput,
                /^Ready to Value$/,
                ObjectionSearchPage.elements.valuationJobStatusesDropdown,
            ).then((text1) => {
                ObjectionSearchPage.elements.valuationJobStatusesInput.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(text1);
                });
            });
        });

        it('should validate the selected valuation job statuses as Valued/Actioned', () => {
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.dropDownBox(
                ObjectionSearchPage.elements.valuationJobStatusesInput,
                /^Valued\/Actioned$/,
                ObjectionSearchPage.elements.valuationJobStatusesDropdown,
            ).then((text1) => {
                ObjectionSearchPage.elements.valuationJobStatusesInput.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(text1);
                });
            });
            ObjectionSearchPage.elements.administativeStatusesClear
                .should('be.visible', { timeout: 10000 })
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait(5000);
            ObjectionSearchPage.elements.objectiontableresultColumnResult
                .should('be.visible')
                .contains('View Valuation');
        });
    });
    context('Categories', () => {
        it('Validating the objection job for non-residential category ', () => {
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible', { timeout: 10000 })
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.category
                .should('be.visible')
                .contains('Categories');
            ObjectionSearchPage.elements.categoryInput
                .should('be.visible')
                .type('C*');
            ObjectionSearchPage.elements.administativeStatusesClear
                .should('be.visible', { timeout: 10000 })
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait(2000);
            ObjectionSearchPage.elements.objectiontableresultColumnResult
                .should('be.visible')
                .contains('QIVS');
        });
    });


    context('Category Groups', () => {
        it('Should validate the content of the Category Groups section', () => {
            ObjectionSearchPage.elements.categoryGroups
                .should('be.visible')
                .contains('Category Groups');
        });

        it('should validate the Category Groups clear button', () => {
            ObjectionSearchPage.elements.categoryGroupsClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });

        });

        it('should validate the Category Groups options', () => {
            ObjectionSearchPage.elements.categoryGroupsDropdown
                .then(($objectionTypeDropdown) => {
                    expect($objectionTypeDropdown.eq(0)).to.contain('R10 — Farm - Animal Production');
                    expect($objectionTypeDropdown.eq(1)).to.contain('R15 — Farm - Crop and Specialist');
                    expect($objectionTypeDropdown.eq(2)).to.contain('R30 — Lifestyle');
                    expect($objectionTypeDropdown.eq(3)).to.contain('R50 — Forestry and Mining');
                    expect($objectionTypeDropdown.eq(4)).to.contain('U10 — Residential Dwellings');
                    expect($objectionTypeDropdown.eq(5)).to.contain('U15 — Residential Flats');
                    expect($objectionTypeDropdown.eq(6)).to.contain('U19 — Residential Vacant');
                    expect($objectionTypeDropdown.eq(7)).to.contain('U30 — Commercial');
                    expect($objectionTypeDropdown.eq(8)).to.contain('U35 — Industrial');
                    expect($objectionTypeDropdown.eq(9)).to.contain('U39 — Vacant - Commercial and Industrial');
                    expect($objectionTypeDropdown.eq(10)).to.contain('X10 — Other');
                    expect($objectionTypeDropdown.eq(11)).to.contain('X19 — Other Vacant');
                    expect($objectionTypeDropdown.eq(12)).to.contain('nul — Null group');
                });
        });


        it('should validate the selected Category Groups as revision', () => {
            ObjectionSearchPage.dropDownBox(
                ObjectionSearchPage.elements.categoryGroupsInput,
                'U15 — Residential Flats',
                ObjectionSearchPage.elements.categoryGroupsDropdown,
            ).then((text1) => {
                ObjectionSearchPage.elements.categoryGroupsInput.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(text1);
                });
            });
        });

    });
    context('Valuer', () => {
        it('Should validate the content of the Valuer section', () => {
            ObjectionSearchPage.elements.valuer
                .should('be.visible')
                .contains('Valuer');
        });

        it('should validate the Valuer clear button', () => {
            ObjectionSearchPage.elements.valuerClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });

        });

        it('should validate the Valuer options', () => {
            ObjectionSearchPage.elements.valuerDropdown
                .then(($objectionTypeDropdown) => {
                    expect($objectionTypeDropdown.eq(0)).to.contain('Unassigned');
                    expect($objectionTypeDropdown).to.have.length.greaterThan(1);
                });
        });

        it('should validate the selected Valuer', () => {
            ObjectionSearchPage.elements.valuerName.then(($el) => {
                valuerName = $el.text().trim()
            }).then(() => {
                ObjectionSearchPage.dropDownBox(
                    ObjectionSearchPage.elements.valuerInput,
                    valuerName,
                    ObjectionSearchPage.elements.valuerDropdown,
                ).then((text1) => {
                    ObjectionSearchPage.elements.valuerInput.invoke('text').then((text) => {
                        const trimmedText = text.trim();
                        expect(trimmedText).to.equal(text1);
                    });
                });
            });
        });

    });
    context('Registered Valuer', () => {
        it('Should validate the content of the Registered Valuer section', () => {
            ObjectionSearchPage.elements.registeredValuer
                .should('be.visible')
                .contains('Registered Valuer');
        });

        it('should validate the Registered Valuer clear button', () => {
            ObjectionSearchPage.elements.registeredValuerClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });

        });

        it('should validate the Registered Valuer options', () => {
            ObjectionSearchPage.elements.registeredValuerDropdown
                .then(($objectionTypeDropdown) => {
                    expect($objectionTypeDropdown.eq(0)).to.contain('Unassigned');
                    expect($objectionTypeDropdown).to.have.length.greaterThan(1);
                });
        });

        it('should validate the selected Registered Valuer', () => {
            ObjectionSearchPage.elements.registeredValuerName.then(($el) => {
                registeredValuerName = $el.text().trim()
            }).then(() => {
                ObjectionSearchPage.dropDownBox(
                    ObjectionSearchPage.elements.registeredValuerInput,
                    registeredValuerName,
                    ObjectionSearchPage.elements.registeredValuerDropdown,
                ).then((text1) => {
                    ObjectionSearchPage.elements.registeredValuerInput.invoke('text').then((text) => {
                        const trimmedText = text.trim();
                        expect(trimmedText).to.equal(text1);
                    });
                });
            });
        });

    });

    context('Administration Status', () => {
        it('should validate the selected Administration statuses as Withdrawn', () => {
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.categoryInput
                .should('be.visible')
                .type('C*')
                .clear();
            ObjectionSearchPage.elements.categoryGroupsClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.valuerClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.registeredValuerClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.administativeStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.dropDownBox(
                ObjectionSearchPage.elements.administativeStatusesInput,
                /^Withdrawn$/,
                ObjectionSearchPage.elements.administativeStatusesDropdown,
            ).then((text1) => {
                ObjectionSearchPage.elements.administativeStatusesInput.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(text1);
                });
            });
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait(1000);
            ObjectionSearchPage.elements.objectionResultQivsButton
                .should('be.visible')
                .contains('QIVS');
        });
        it('should validate the selected Administration statuses as To Complete', () => {
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.categoryInput
                .should('be.visible')
                .type('C*')
                .clear();
            ObjectionSearchPage.elements.categoryGroupsClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.valuerClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.registeredValuerClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.administativeStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.dropDownBox(
                ObjectionSearchPage.elements.administativeStatusesInput,
                /^To Complete$/,
                ObjectionSearchPage.elements.administativeStatusesDropdown,
            ).then((text1) => {
                ObjectionSearchPage.elements.administativeStatusesInput.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(text1);
                });
            });
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait(1000);
            ObjectionSearchPage.elements.objectionResultQivsButton
                .should('be.visible')
                .contains('QIVS');
        });
    });

    context('Sales Groups & ROLLS', () => {
        it('Should validate the content of the Sales Groups & ROLLS section', () => {
            ObjectionSearchPage.elements.salesGroupsAndRollsButton
                .should('be.visible')
                .contains('Sales Groups & ROLLS');
        });
        it('should validate the Sales Groups & ROLLS ', () => {
            ObjectionSearchPage.elements.salesGroupsAndRollsButton
                .should('be.visible')
                .click({ force: true });
            ObjectionSearchPage.elements.alertModal
                .should('be.visible')
                .contains('SALES GROUPS AND ROLLS')
            ObjectionSearchPage.elements.salesGroupsSetRollsButton
                .should('be.visible')
                .contains('SET ROLLS')
            ObjectionSearchPage.elements.salesGroupsClearButton
                .should('be.visible')
                .contains('CLEAR')
            ObjectionSearchPage.elements.salesGroupFormClose
                .should('be.visible')
                .click({ force: true });

        });

        it('should randomly check any 2 Territorial Authorities Criteria for the sales group and rolls', () => {
            ObjectionSearchPage.elements.salesGroupsAndRollsButton
                .should('be.visible')
                .click({ force: true });
            ObjectionSearchPage.elements.salesGroupTerritorialAuthoritiesCheckbox
                .then($item => {
                    return Cypress._.sampleSize($item.toArray(), 2);
                })
                .should('have.length', 2)
                .click({ multiple: true });
            ObjectionSearchPage.elements.salesGroupsClearButton
                .should('be.visible')
                .contains('CLEAR')
                .click({ force: true });
        });


        it('should randomly check any 2 sales Group Category for the sales group and rolls', () => {
            ObjectionSearchPage.elements.salesGroupsAndRollsButton
                .should('be.visible')
                .click({ force: true });
            ObjectionSearchPage.elements.salesGroupSGCheckbox
                .then($item => {
                    return Cypress._.sampleSize($item.toArray(), 2);
                })
                .should('have.length', 2)
                .click({ multiple: true });
            ObjectionSearchPage.elements.salesGroupsClearButton
                .should('be.visible')
                .contains('CLEAR')
                .click({ force: true });
            ObjectionSearchPage.elements.salesGroupFormClose
                .should('be.visible')
                .click({ force: true });
        });

    });

    context('Search', () => {
        it('should successfully view the search button', () => {
            ObjectionSearchPage.elements.territorialAuthorities
                .should('be.visible').click();
            ObjectionSearchPage.elements.territorialAuthoritiesCheckbox
                .eq(0).check();
            cy.wait(2000);
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.dropDownBox(
                ObjectionSearchPage.elements.valuationJobStatusesInput,
                /^Ready to Value$/,
                ObjectionSearchPage.elements.valuationJobStatusesDropdown,
            ).then((text1) => {
                ObjectionSearchPage.elements.valuationJobStatusesInput.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(text1);
                });
            });
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
        });
    });

    context('Export Report', () => {

        function setupSearch(){
            cy.wait(4000);
            ObjectionSearchPage.elements.objectionClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.territorialAuthorities
                .should('be.visible').click();
            ObjectionSearchPage.elements.territorialAuthoritiesCheckbox
                .eq(0).check();
            cy.wait(6000);
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.dropDownBox(
                ObjectionSearchPage.elements.valuationJobStatusesInput,
                /^Ready to Value$/,
                ObjectionSearchPage.elements.valuationJobStatusesDropdown,
            ).then((text1) => {
                ObjectionSearchPage.elements.valuationJobStatusesInput.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(text1);
                });
            });
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait(4000);
        }

        function setupExceedLimitSearch(){
            cy.wait(4000);
            ObjectionSearchPage.elements.objectionClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.territorialAuthorities
                .should('be.visible').click();
            ObjectionSearchPage.elements.territorialAuthoritiesCheckbox
                .eq(0).check();
            cy.wait(6000);
            ObjectionSearchPage.elements.valuationJobStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });
            ObjectionSearchPage.elements.administativeStatusesClear
                .should('be.visible')
                .contains('Clear')
                .click({ force: true });    
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait(4000);
        }

        function exportReport(){
            ObjectionSearchPage.elements.exportButton
            .should('be.visible')
            .click({ force: true });

            cy.wait(2000);

            ObjectionSearchPage.elements.alertModal
                .and('be.visible')
                .and('contain', 'Export Scheduled')
                .and('contain', 'Your export has been acknowledged and can be viewed in View My Reports.');
        }

        it('should successfully view the export report', () => { 
            setupSearch();
            exportReport();
            ObjectionSearchPage.elements.alertModalOk
                .click({ force: true });
        });
        it('Objection Search Page - Export Limit Exceeded Error', () => {
            setupExceedLimitSearch();
            ObjectionSearchPage.elements.exportButton
                .should('be.visible')
                .click({ force: true });
            cy.wait(4000);
            ObjectionSearchPage.elements.alertModal
                .should('be.visible')
                .and('contain', 'Export limit exceeded')
                .and('contain', 'A maximum of ')
                .and('contain', 'results may be exported.  Please refine your search criteria.');
            ObjectionSearchPage.elements.alertModalExportLimitExceededOk
                .click({ force: true }); 
        });
        it('should successfully view  report through VIEW MY REPORTS', () => {
            setupSearch();
            exportReport();
            ObjectionSearchPage.elements.alertModalViewMyReport
                .click({ force: true });
            cy.wait(10000);
            Reports.elements.myReportsRefreshButton
                .click({ force: true });
            cy.wait(500);
            Reports.elements.jobFirstColumns
                .should('be.visible')
                .and('contain', 'Objection Export');
            Reports.elements.jobFirstColumnsFileSize
                .invoke('text')
                .then(parseFloat)
                .should('not.be.NaN')
                .should('be.gt', 0);
        });
      
    });

    context('sorting the columns', () => {
        it('Sorting the columns by Address', () => {
            cy.visitWithLogin(ObjectionsPage);  
            ObjectionSearchPage.elements.objectionResultAddress
                .should('be.visible')
                .contains('Address')
                .click();
            cy.wait(2000);
            ObjectionSearchPage.elements.objectionResultAddress
                .find('a')
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');
        });

        it('Sorting the columns by Val Ref', () => {
            ObjectionSearchPage.elements.objectionResultValRef
                .should('be.visible')
                .contains('Val Ref')
                .click();
            cy.wait(2000);
            ObjectionSearchPage.elements.objectionResultValRef
                .find('a')
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');
        });

        it('Sorting the columns by Date Received', () => {
            ObjectionSearchPage.elements.objectionResultDataReceived
                .should('be.visible')
                .contains('Date Received')
                .click();
            cy.wait(2000);
            ObjectionSearchPage.elements.objectionResultDataReceived
                .find('a')
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');
        });

        it('Sorting the columns by Category', () => {
            ObjectionSearchPage.elements.objectionResultCategory
                .should('be.visible')
                .contains('Category')
                .click();
            cy.wait(2000);
            ObjectionSearchPage.elements.objectionResultCategory
                .find('a')
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');
        });

        it('Sorting the columns by Objector', () => {
            ObjectionSearchPage.elements.objectionResultObjector
                .should('be.visible')
                .contains('Objector')
                .click();
            cy.wait(2000);
            ObjectionSearchPage.elements.objectionResultObjector
                .find('a')
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');
        });

        it('Sorting the columns by Valuer', () => {
            ObjectionSearchPage.elements.objectionResultValuer
                .should('be.visible')
                .contains('Valuer')
                .click();
            cy.wait(2000);
            ObjectionSearchPage.elements.objectionResultValuer
                .find('a')
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');
        });

        it('Sorting the columns by  Admin Status', () => {
            ObjectionSearchPage.elements.objectionResultAdminStatus
                .should('be.visible')
                .contains('Admin Status')
                .click();
            cy.wait(2000);
            ObjectionSearchPage.elements.objectionResultAdminStatus
                .find('a')
                .should('be.visible')
                .should('have.attr', 'title', 'sort ascending');
        });

        it('Sorting the columns by   Val. Job Status', () => {
            ObjectionSearchPage.elements.objectionResultJobStatus
                .should('be.visible')
                .contains('Val. Job Status')
                .click();
            cy.wait(2000);
            ObjectionSearchPage.elements.objectionResultJobStatus
                .find('a')
                .should('be.visible')
                .should('have.attr', 'title', 'sort descending');
        });
    });

});
describe('Objection Search - TA Approval', () => {
    beforeEach('Visits the page and sets up intercept', () => {
        cy.intercept("/searchProperties*", {
            fixture: "objection-search-taapproval-property.json",
        }).as('searchProperties');
        cy.intercept("/api-picklist/objection/search/total%3Dtrue", {
            fixture: "objection-search-api-picklist.json",
        }).as('apiPicklist');
        cy.intercept("/api-picklist/objection/search/*", {
            fixture: "objection-search-offset.json",
        }).as('offsetapiPicklist');
        cy.visitWithLogin(ObjectionsPage);
    });
    context('TA Approval Actions   ', () => {
        it('should approve objection with TA Approval button', () => {
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait('@searchProperties', { timeout: 18000 });
            ObjectionSearchPage.elements.checkboxHeader.eq(0).click();
            ObjectionSearchPage.elements.taApprovalButton.click();
            ObjectionSearchPage.elements.taApprovalDatePicker.type('10/7/2024');
            ObjectionSearchPage.elements.checkboxConfirmApproval.check({ force: true });
            ObjectionSearchPage.elements.approveObjection.click();
            ObjectionSearchPage.elements.objectionSuccessfulMessage.should('be.visible').contains('Objections successfully approved.');
            ObjectionSearchPage.elements.objectionCloseButton.click();
        });

        it('should approve objection with Approve button ', () => {
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait('@searchProperties', { timeout: 18000 });
            ObjectionSearchPage.elements.checkboxHeader.eq(0).click();
            ObjectionSearchPage.elements.objectionTableHeaderTaSignOff.eq(0).contains('Approve').click();
            ObjectionSearchPage.elements.taApprovalDatePicker.type('10/7/2024');
            ObjectionSearchPage.elements.checkboxConfirmApproval.check({ force: true });
            ObjectionSearchPage.elements.approveObjection.click();
            ObjectionSearchPage.elements.objectionSuccessfulMessage.should('be.visible').contains('Objections successfully approved.');
            ObjectionSearchPage.elements.objectionCloseButton.click();
        });

        it('should reject objection with Reject Button', () => {
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait('@searchProperties', { timeout: 18000 });
            ObjectionSearchPage.elements.checkboxHeader.eq(0).click();
            ObjectionSearchPage.elements.objectionTableHeaderTaSignOff.eq(0).contains('Reject').click();
            ObjectionSearchPage.elements.rejectionReason.type('Test');
            ObjectionSearchPage.elements.reinstatementSave.click({ force: true });
        });
    });
});

describe('Objection Search - High Risk Objection job', () => {
    let fixtureData;
    before(() => {
        cy.fixture('high-risk-objection-job.json').then((data) => {
            fixtureData = data;
        });
        cy.intercept('/searchProperties*', (req) => {
            req.reply(fixtureData.searchProperties);
        }).as('searchProperties');

        cy.intercept('/api-picklist/objection/search/total%3Dtrue', (req) => {
            req.reply(fixtureData.apiPicklist);
        }).as('apiPicklist');

        cy.intercept('/api-picklist/objection/search/*', (req) => {
            req.reply(fixtureData.offsetApiPicklist);
        }).as('offsetapiPicklist');

        cy.visitWithLogin(ObjectionsPage);
    });

    context('High Risk Objection job', () => {
        it('should approve objection with TA Approval button', () => {
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait('@searchProperties', { timeout: 18000 });
        });
    });
});
describe('Objection Search - Inactive Objection job', () => {
    let fixtureData;
    before(() => {
        cy.fixture('inactive-objection-job.json').then((data) => {
            fixtureData = data;
        });
        cy.intercept('/searchProperties*', (req) => {
            req.reply(fixtureData.searchProperties);
        }).as('searchProperties');

        cy.intercept('/api-picklist/objection/search/total%3Dtrue', (req) => {
            req.reply(fixtureData.apiPicklist);
        }).as('apiPicklist');

        cy.intercept('/api-picklist/objection/search/*', (req) => {
            req.reply(fixtureData.offsetApiPicklist);
        }).as('offsetapiPicklist');

        cy.visitWithLogin(ObjectionsPage);
    });

    context('Inactive Objection job', () => {
        it('should approve objection with TA Approval button', () => {
            ObjectionSearchPage.elements.objectionSearch
                .contains('Search')
                .click({ force: true });
            cy.wait('@searchProperties', { timeout: 18000 });
            ObjectionSearchPage.elements.objectionSearchTableRow
                .should('be.visible')
                .should('have.attr', 'class', 'resultsRow activity-list__row red-highlight');
        });
    });
});
describe('Objection Search - Show Modals', () => {
    let fixtureData;

    beforeEach('Visits the page and sets up intercept', () => {
        cy.fixture('high-risk-objection-job.json').then((data) => {
            fixtureData = data;
        });
        cy.intercept('/searchProperties*', (req) => {
            req.reply(fixtureData.searchProperties);
        }).as('searchProperties');
    });

    before(() => {
        cy.visitWithLogin(ObjectionsPage);
    });

    context('Testing Modal Pop Ups', () => {
        it('should pop up a modal if no properties are selected when trying to create an objection inspection report', () => {
            ObjectionSearchPage.elements.createObjectionReportButton
                .contains('Create Objection Inspection Report')
                .click({ force: true });

            ObjectionSearchPage.elements.objectionSuccessfulMessage.should('be.visible').contains('No objection selected.');
            ObjectionSearchPage.elements.objectionCloseButton.click();
        });

        it('should pop up a modal if no properties are selected when trying to view', () => {
            ObjectionSearchPage.elements.mapButton
                .contains('Map')
                .click({ force: true });

            ObjectionSearchPage.elements.objectionSuccessfulMessage.should('be.visible').contains('Show Map');
            ObjectionSearchPage.elements.objectionCloseButton.click();
        });

        it('should select over 100 properties and a modal should pop up stating that more than 100 properties have been selected', () => {
            cy.wait('@searchProperties', { timeout: 25000 })
            ObjectionSearchPage.elements.selectAll.click();
            ObjectionSearchPage.elements.nextPage.click();
            cy.wait('@searchProperties', { timeout: 25000 })
            ObjectionSearchPage.elements.selectAll.click();
            
            ObjectionSearchPage.elements.mapButton
                .contains('Map')
                .click();
            ObjectionSearchPage.elements.objectionSuccessfulMessage.should('be.visible').contains('Limit exceeded');
            ObjectionSearchPage.elements.objectionCloseButton.click();
            ObjectionSearchPage.elements.clearAll.click();
        });
    });

    context('Testing Show Map', () => {
        it('should select one property and show the map with said property selected', () => {
            ObjectionSearchPage.elements.checkboxHeader.eq(0).click();
            ObjectionSearchPage.elements.mapButton
                .contains('Map')
                .click().then(() => {
                    ObjectionSearchPage.elements.mapButton.invoke('attr','map-url').then((mapurl) => {
                        cy.visit(mapurl);
                    });
                });
            cy.visitWithLogin(ObjectionsPage);
        });

        it('should select all the properties on the current page and show the map with said properties selected', () => {
            cy.wait('@searchProperties', { timeout: 25000 })

            ObjectionSearchPage.elements.selectAll.click();
            ObjectionSearchPage.elements.mapButton
                .contains('Map')
                .click().then(() => {
                    ObjectionSearchPage.elements.mapButton.invoke('attr','map-url').then((mapurl) => {
                        cy.visit(mapurl);
                    });
                });
        });
    });
    
});
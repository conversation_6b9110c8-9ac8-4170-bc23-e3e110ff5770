import MainNavigation from '../../model/MainNavigation';
import user from '../../support/user.js';
import getConfig from '../../support/config.js';

const baseUrl = getConfig('baseUrl');

describe('Main Navigation', { defaultCommandTimeout: 10000 }, () => {
    context('Presents the expected navigation links', () => {
        it('Main nav is displayed on the page', () => {
            cy.visitWithUser('', user.INTERNAL_RTV_USER);

            MainNavigation.navBar.then(mainNav => {
                expect(mainNav).to.exist.and.be.visible;
            });
        });

        it('Has the RTV link', () => {
            cy.visitWithUser('', user.INTERNAL_RTV_USER);

            MainNavigation.rtvLink.then(rtvLink => {
                expect(rtvLink).to.exist.and.be.visible;
            });
        });

        it('Clicking the RTV link navigates to the RTV Dashboard', () => {
            cy.visitWithUser('', user.INTERNAL_RTV_USER);

            MainNavigation.rtvLink.click().then(() => {
                cy.url().then(url => {
                    expect(url).to.equal(`${baseUrl}rtv-dashboard`);
                });
            });
        });
    });
});

import MainNavigation from '../../model/MainNavigation';
import user from '../../support/user.js';

describe('Main Navigation', { defaultCommandTimeout: 10000 }, () => {
    context('Presents the expected navigation links', () => {
        it('Main nav is displayed on the page', () => {
            cy.visitWithUser('', user.INTERNAL_NON_RTV_USER);

            MainNavigation.navBar.then(mainNav => {
                expect(mainNav).to.exist.and.be.visible;
            });
        });

        it('Does not have the RTV link', () => {
            cy.visitWithUser('', user.INTERNAL_NON_RTV_USER);

            MainNavigation.rtvLink.then(rtvLink => {
                expect(rtvLink).to.not.exist;
            });
        });

    });
});

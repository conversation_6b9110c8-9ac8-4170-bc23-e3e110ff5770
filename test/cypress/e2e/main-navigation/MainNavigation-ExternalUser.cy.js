import MainNavigation from '../../model/MainNavigation';
import user from '../../support/user.js';

describe('Main Navigation', { defaultCommandTimeout: 10000 }, () => {
    context('Presents the expected navigation links', () => {
        it('Main nav is displayed on the page', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.navBar.then(mainNav => {
                expect(mainNav).to.exist.and.be.visible;
            });
        });

        it('Has the Home link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.homeLink.then(link => {
                expect(link).to.exist.and.be.visible;
            });
        });

        it('Does not have the Roll Maintenance link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.rollMaintenanceLink.then(link => {
                expect(link).to.not.exist;
            });
        });

        it('Does not have the Cloud Uploader link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.cloudUploaderLink.then(link => {
                expect(link).to.not.exist;
            });
        });

        it('Has the QIVS Application link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.qivsAppLink.then(link => {
                expect(link).to.exist.and.be.visible;
            });
        });

        it('Has the Reports link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.reportsLink.then(link => {
                expect(link).to.exist.and.be.visible;
            });
        });

        it('Does not have the Picklists link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.picklistsLink.then(link => {
                expect(link).to.not.exist;
            });
        });

        it('Does not have the User Maintenance link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.userMaintenanceLink.then(link => {
                expect(link).to.not.exist;
            });
        });

        it('Has the Logout link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.logoutLink.then(link => {
                expect(link).to.exist.and.be.visible;
            });
        });

        it('Does not have the Sales Processing link', () => {
            cy.visitWithUser('', user.EXTERNAL_USER);

            MainNavigation.salesProcessingLink.then(link => {
                expect(link).to.not.exist;
            });
        });

    });
});

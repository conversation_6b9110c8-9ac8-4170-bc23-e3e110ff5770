import ValuerDashboard from '../../model/ValuerDashboard.js';
import user from '../../support/user.js';

describe('View Dashboard',{
    testIsolation: false,
    defaultCommandTimeout: 30000,
},() => {

    context('As Internal User', () => {
        before(() => {
            cy.login();
            ValuerDashboard.visit();
        });

        it('Displays Filter Section', () => {
            ValuerDashboard.elements.filterSection.should('exist').and('be.visible');
            ValuerDashboard.elements.valuerFilter.should('exist').and('be.visible');
            ValuerDashboard.elements.dateFilter.should('exist').and('be.visible');
            ValuerDashboard.elements.customDateFromFilter.should('exist').and('be.visible');
            ValuerDashboard.elements.customDateToFilter.should('exist').and('be.visible');
            ValuerDashboard.elements.taFilter.should('exist').and('be.visible');
        });

        it('Displays Progress Bar', () => {
            ValuerDashboard.elements.progressBars.should('exist').and('be.visible');
        });

        it('Displays Rating Section', () => {
            ValuerDashboard.elements.ratingSection.should('exist').and('be.visible');
        });

        it('Displays Rating Tiles', () => {
            ValuerDashboard.elements.ratingTiles.should('exist');
            ValuerDashboard.elements.ratingTiles.should('have.length', 7);
        });

        it('Displays Rating Analysis Tiles ', () => {
            ValuerDashboard.elements.ratingAnalysisTiles.should('exist');
            ValuerDashboard.elements.ratingAnalysisTiles.should('have.length', 7);
        });

        it('Displays Consulancy Section', () => {
            ValuerDashboard.elements.consultancySection.should('exist').and('be.visible');
        });

        it('Displays Consulancy Tiles', () => {
            ValuerDashboard.elements.consultancyTiles.should('exist');
            ValuerDashboard.elements.consultancyAnalysisTiles.should('have.length', 7);
        });

        it('Displays Consulancy Analysis Tiles ', () => {
            ValuerDashboard.elements.consultancyTiles.should('exist');
            ValuerDashboard.elements.consultancyAnalysisTiles.should('have.length', 7);
        });

        it('Should Not Display Other Valuers Rating Analysis Tiles', () => {
            ValuerDashboard.elements.valuerFilter.click().find('.multiselect__element:nth-child(2)').click();
            ValuerDashboard.elements.ratingAnalysisTiles.should('not.exist');
        });

        it('Should Not Display Other Valuers Consultancy Analysis Tiles', () => {
            ValuerDashboard.elements.valuerFilter.click().find('.multiselect__element:nth-child(3)').click();
            ValuerDashboard.elements.consultancyAnalysisTiles.should('not.exist');
        });

    });

    context('As Monarch Reporting Manager user', () => {
        before(() => {
            cy.visitWithUser('/dashboard/valuer-metrics', user.INTERNAL_USER_REPORTING_MANAGER);
        });

        it('Displays Filter Section', () => {
            ValuerDashboard.elements.filterSection.should('exist').and('be.visible');
            ValuerDashboard.elements.valuerFilter.should('exist').and('be.visible');
            ValuerDashboard.elements.dateFilter.should('exist').and('be.visible');
            ValuerDashboard.elements.customDateFromFilter.should('exist').and('be.visible');
            ValuerDashboard.elements.customDateToFilter.should('exist').and('be.visible');
            ValuerDashboard.elements.taFilter.should('exist').and('be.visible');
        });

        it('Displays Rating Section', () => {
            ValuerDashboard.elements.ratingSection.should('exist').and('be.visible');
        });

        it('Displays Rating Tiles', () => {
            ValuerDashboard.elements.ratingTiles.should('exist');
            ValuerDashboard.elements.ratingTiles.should('have.length', 7);
        });

        it('Displays Rating Analysis Tiles ', () => {
            ValuerDashboard.elements.ratingAnalysisTiles.should('exist');
            ValuerDashboard.elements.ratingAnalysisTiles.should('have.length', 7);
        });

        it('Displays Consulancy Section', () => {
            ValuerDashboard.elements.consultancySection.should('exist').and('be.visible');
        });

        it('Displays Consulancy Tiles', () => {
            ValuerDashboard.elements.consultancyTiles.should('exist');
            ValuerDashboard.elements.consultancyAnalysisTiles.should('have.length', 7);
        });

        it('Displays Consulancy Analysis Tiles ', () => {
            ValuerDashboard.elements.consultancyTiles.should('exist');
            ValuerDashboard.elements.consultancyAnalysisTiles.should('have.length', 7);
        });

        it('Display All Valuers Rating Analysis Tiles', () => {
            ValuerDashboard.elements.valuerFilter.click().find('.multiselect__element:nth-child(2)').click();
            ValuerDashboard.elements.ratingAnalysisTiles.should('exist');
            ValuerDashboard.elements.ratingAnalysisTiles.should('have.length', 7);
        });

        it('Display All Valuers Consultancy Analysis Tiles', () => {
            ValuerDashboard.elements.valuerFilter.click().find('.multiselect__element:nth-child(3)').click();
            ValuerDashboard.elements.consultancyAnalysisTiles.should('exist');
            ValuerDashboard.elements.consultancyAnalysisTiles.should('have.length', 7);
        });

        it('Display All Valuers Progress Bar Tiles', () => {
            ValuerDashboard.elements.valuerFilter.click().find('.multiselect__element:nth-child(2)').click();
            ValuerDashboard.elements.progressBars.should('exist');
        });
        
        it('Should check if the custom date \'From\' and \'To\' filters are disabled when custom range is not selected', () => {
            ValuerDashboard.elements.valuerFilter.click().find('.multiselect__element:nth-child(2)').click();
            ValuerDashboard.elements.progressBars.should('exist');
            
            ValuerDashboard.elements.dateFilter.click().find('.multiselect__element:nth-child(1)').click();
            ValuerDashboard.elements.customDateFromFilter.find('.mx-input').should('exist').and('be.disabled');
            ValuerDashboard.elements.customDateToFilter.find('.mx-input').should('exist').and('be.disabled');
        });

        it('Should check when custom range is selected then the custom date \'From\' and \'To\' filters are not disabled', () => {
            ValuerDashboard.elements.dateFilter.click().find('.multiselect__element:nth-child(7)').click();
            ValuerDashboard.elements.customDateFromFilter.find('.mx-input').should('not.be.disabled');
            ValuerDashboard.elements.customDateToFilter.find('.mx-input').should('not.be.disabled');
        });
        
        describe('Error-checking for Custom Range date picker:', () => {
            before(() => {
                ValuerDashboard.elements.valuerFilter.click().find('.multiselect__element:nth-child(2)').click();
                ValuerDashboard.elements.progressBars.should('exist');
                ValuerDashboard.elements.dateFilter.click().find('.multiselect__element:nth-child(7)').click();

                cy.wait(1000);
            });

            it(`should by default use start of the month for the \'To\' date and current date for the \'From\' date`, () => {
                ValuerDashboard.checkCustomRangesAreAtDefaultValues();
            });

            it(`should throw an error when either the \'From\' date or the \'To\' date is deleted`, () => {
                ValuerDashboard.elements.customDateFromFilter.find('.mx-input').clear().click();
                ValuerDashboard.elements.customDateToFilter.find('.mx-input').clear().click();

                ValuerDashboard.clickOutsideDatePicker();

                ValuerDashboard.checkNotificationMessage('Invalid date range: \"To\" date needs to be selected');
                ValuerDashboard.checkNotificationMessage('Invalid date range: \"From\" date needs to be selected');
            });

            it(`should throw an error when the \'From\' date is greater than both  \'To\' date and the current date`, () => {
                const date = new Date();
                const today = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
                const startOfMonthNextYear = `1/${date.getMonth() + 1}/${date.getFullYear() + 1}`;

                ValuerDashboard.elements.customDateFromFilter.find('.mx-input').clear().type(startOfMonthNextYear);
                ValuerDashboard.elements.customDateToFilter.find('.mx-input').clear().type(today);

                ValuerDashboard.clickOutsideDatePicker();

                ValuerDashboard.checkNotificationMessage(`Invalid date range: \"From\" date was greater than \"To\" date and Today\'s date (${today})`);
            });

            it(`should throw an error when \"From\" date is just greater than \"To\" date`, () => {
                const date = new Date();
                const startOfMonthLastYear = `1/${date.getMonth() + 1}/${date.getFullYear() - 1}`;
                const secondDayOfMonthLastYear = `2/${date.getMonth() + 1}/${date.getFullYear() - 1}`;

                ValuerDashboard.elements.customDateFromFilter.find('.mx-input').clear().type(secondDayOfMonthLastYear);
                ValuerDashboard.elements.customDateToFilter.find('.mx-input').clear().type(startOfMonthLastYear);

                ValuerDashboard.clickOutsideDatePicker();

                ValuerDashboard.checkNotificationMessage('Invalid date range: \"From\" date was greater than \"To\" date');
            });

            it(`should throw an error when either the \'From\' date and the \'To\' date is greater than the current date`, () => {
                const date = new Date();
                const today = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
                const startOfMonthNextYear = `1/${date.getMonth() + 1}/${date.getFullYear() + 1}`;
                const secondDayOfMonthNextYear = `2/${date.getMonth() + 1}/${date.getFullYear() + 1}`;

                ValuerDashboard.elements.customDateFromFilter.find('.mx-input').clear().type(startOfMonthNextYear);
                ValuerDashboard.elements.customDateToFilter.find('.mx-input').clear().type(secondDayOfMonthNextYear);

                ValuerDashboard.clickOutsideDatePicker();

                ValuerDashboard.checkNotificationMessage(`Invalid date range: \"From\" date was greater than Today\'s date (${today})`);
                ValuerDashboard.checkNotificationMessage(`Invalid date range: \"To\" date was greater than Today\'s date (${today})`);
            });

            it('should reset to default \'To\' date and \'From\' date when switching to different Valuer while errors are accuring', () => {
                ValuerDashboard.elements.customDateFromFilter.find('.mx-input').clear().click();
                ValuerDashboard.elements.customDateToFilter.find('.mx-input').clear().click();

                ValuerDashboard.clickOutsideDatePicker();

                ValuerDashboard.elements.valuerFilter.click().find('.multiselect__element:nth-child(3)').click();
                cy.wait(10000);

                ValuerDashboard.checkNotificationMessage('Invalid date range: custom \"From\" date and \"To\" date have been reset to default dates');
                ValuerDashboard.checkCustomRangesAreAtDefaultValues();
            });
        });
    });
});

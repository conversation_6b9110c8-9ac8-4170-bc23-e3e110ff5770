import Home from '../../model/Home';

describe('Property Search', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it('fills in a valuation reference that has suffixes', () => {
    Home.quickSearchBar.clear().type('31570/6601');

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length.at.least', 1);
    Home.typeAheadResults.children().each($el => {
      expect($el.text()).to.include('31570/6601');
    });
  });

  it('fills in a valuation reference with suffix', () => {
    Home.quickSearchBar.clear().type('31570/6601 AB');
    cy.wait(3000);

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length', 1);
    Home.typeAheadResults.children().each($el => {
      expect($el.text()).to.include('31570/6601 AB');
    });
  });
});

import Home from '../../model/Home';
import QuickSearch from '../../model/QuickSearch';

describe('Property Search', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it('fills in a street name', () => {
    Home.quickSearchBar.clear().type('te kiteroa grove');

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length.at.least', 1);
    Home.typeAheadResults.children().each($el => {
      expect($el.text()).to.include('Te Kiteroa Grove');
    });
  });

  it('fills in a flat number', () => {
    Home.quickSearchBar.clear().type('9 te kiteroa');
    cy.wait(3000);

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length.at.least', 1);
    Home.typeAheadResults.then($el => {
      expect($el.text()).to.include('9 F1');
      expect($el.text()).to.include('9 F2');
    });
  });

  it('fills in a unit number', () => {
    Home.quickSearchBar.clear().type('9 F1 te kiteroa');
    cy.wait(3000);

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length', 1);
    Home.typeAheadResults.then($el => {
      expect($el.text()).to.include('9');
      expect($el.text()).to.include('F1');
      expect($el.text()).to.include('Te');
      expect($el.text()).to.include('Kiteroa');
    });
  });

  it('fills in a specific address', () => {
    Home.quickSearchBar.clear().type('1 Queen Street, Awanui, Far North District');
    cy.wait(3000);

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length', 1);
    Home.typeAheadResults.children().each($el => {
      expect($el.text()).to.include('1 Queen Street');
      expect($el.text()).to.include('Awanui');
      expect($el.text()).to.include('Far North District');
    });
  });

  it('sets TAs and finds street addresses in them', () => {
    QuickSearch.setTas([1,13]);
    Home.quickSearchBar.clear().type('1 queen street');
    cy.wait(3000);

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length.at.least', 1);
    Home.typeAheadResults.children().each($el => {
      expect($el.text()).to.satisfy(text => {
        return text.includes('Far North District') || text.includes('Waikato District');
      });
    });
  });
});

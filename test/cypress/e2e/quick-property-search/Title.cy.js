import Home from '../../model/Home';
import QuickSearch from '../../model/QuickSearch';

describe('Property Search', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it('fills in a title', () => {
    Home.quickSearchBar.clear().type('wn15d/1469');

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length', 1);
    Home.typeAheadResults.children().each($el => {
      expect($el.text()).to.include('WN15D/1469');
    });
  });

  it('sets a TA that and does not find the title', () => {
    QuickSearch.setTas([7]);
    Home.quickSearchBar.clear().type('wn15d/1469');
    cy.wait(3000);

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length', 1);
    Home.typeAheadResults.children().each($el => {
      expect($el.text()).to.include('No results found');
    });
  });
});

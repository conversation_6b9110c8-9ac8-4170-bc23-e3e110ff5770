import Home from '../../model/Home';

describe('Property Search', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it('fills in a QPID', () => {
    Home.quickSearchBar.clear().type('963790');

    Home.typeAheadResults.should('exist');
    Home.typeAheadResults.children().should('have.length.at.least', 1);
    Home.typeAheadResults.children().each($el => {
      expect($el.text()).to.include('963790');
    });
  });
});

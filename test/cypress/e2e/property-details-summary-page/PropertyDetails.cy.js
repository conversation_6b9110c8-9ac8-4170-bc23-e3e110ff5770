import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';
import PropertySearchResults from '../../model/PropertySearchResults';
import ValuationJob from '../../model/ValuationJob';
import Monarch from '../../model/Monarch';

describe('Property Details', { defaultCommandTimeout: 15000 }, () => {
    beforeEach(() => { cy.visitWithLogin("/") });
    context('Quick Search', () => {
        it(`Quick search typeahead: QPID ${env.ADDRESS2_QPID}`, () => {
            Home.quickSearchBar.type(env.ADDRESS2_SEARCH_TERM);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
            Home.typeAheadResults
                .contains('span.listBox-category', env.ADDRESS2_QPID)
                .should('exist');
        });

        it(`Quick search and enter: QPID ${env.ADDRESS2_QPID}`, () => {
            Home.quickSearchBar.type(env.ADDRESS2_SEARCH_TERM + '{enter}');
            Monarch.waitForPropertyInfo();

            PropertyDetails.qpid.then(elem => {
                expect(elem.text()).to.equal(env.ADDRESS2_QPID);
            });
            PropertyDetails.addressLine1.then((elem) => {
                expect(elem).to.contain(env.ADDRESS2_SEARCH_TERM);
            });
        });

        it(`Occupier details suppressed: QPID ${env.ADDRESS8.QPID}`, () => {
            const page = `property/property?qupid=${env.ADDRESS8.QPID}`;
            cy.visitWithLogin(page);
            Monarch.waitForPropertyInfo();

            PropertyDetails.qpid.then(elem => {
                cy.log(elem);
                cy.log(elem.text());
                expect(elem.text()).to.equal(env.ADDRESS8.QPID);
            });
            PropertyDetails.ownerHeaderRow
                .next()
                .should('have.class', 'supressed')
                .next()
                .should('have.class', 'supressed');
        });
    });

    describe('Search for property with apportionments', () => {
        it(`Search: ${env.ADDRESS3.SEARCH_TERM}`, () => {
            Home.quickSearchBar.clear().type(`${env.ADDRESS3.SEARCH_TERM}{enter}`);
            cy.wait(3000);
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(env.ADDRESS3.APPORT2.QPID);
            });
            PropertyDetails.addressLine1.should('contain.text', env.ADDRESS3.APPORT2.ADDRESS);
        });
        it(`Select apportionment: ${env.ADDRESS3.APPORT1.VALREF}`, () => {
            //FIXME: replace wait with intercept. unsure why the first select doesnt wait properly
            cy.wait(6000);
            PropertyDetails.valrefSelectOption(env.ADDRESS3.APPORT1.VALREF)
                .invoke('val')
                .then(val => {
                    PropertyDetails.valrefSelect.select(val, { force: true });
                });
            cy.wait(6000);
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(env.ADDRESS3.APPORT1.QPID);
            });
            PropertyDetails.addressLine1.should('contain.text', env.ADDRESS3.APPORT1.ADDRESS);
        });
        it(`Select apportionment: ${env.ADDRESS3.APPORT3.VALREF}`, () => {
            cy.intercept({
                method: 'POST',
                url: `${env.BASE_URL}getPropertyDescription`
            }).as('getProperty');

            PropertyDetails.valrefSelectOption(env.ADDRESS3.APPORT3.VALREF)
                .invoke('val')
                .then(val => {
                    PropertyDetails.valrefSelect.select(val, { force: true });
                });
            cy.wait('@getProperty').then(interception => {
                PropertyDetails.qpid.invoke('text').then(text => {
                    expect(text).to.equal(env.ADDRESS3.APPORT3.QPID);
                });
                PropertyDetails.addressLine1.should('contain.text', env.ADDRESS3.APPORT3.ADDRESS);
            });
        });
    });
    describe(`Search for property with > 20 photos: ${env.ADDRESS4.ADDRESS}`, () => {
        it('Search', () => {
            Home.quickSearchBar.clear().type(`${env.ADDRESS4.VALREF}{enter}`);
            cy.wait(5000);
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(env.ADDRESS4.QPID);
            });
            PropertyDetails.addressLine1.should('contain.text', env.ADDRESS4.SEARCH_TERM);
        });
        it('View more photos button present', () => {
            PropertyDetails.viewMorePhotosButton.should('exist');
        });
        it('20 Photos - 20 slider buttons', () => {
            PropertyDetails.photoSelectSlider.children().should('have.length', 20);
        });
    });

    describe('Valuation Jobs Button', () => {
        it('New valuation job option available', () => {
            PropertyDetails.valuationJobsButton.should('exist').click();
            PropertyDetails.valuationJobsList
                .contains('li', 'New Valuation Job')
                .should('exist')
                .click();
        });
        it('Setup job screen', () => {
            ValuationJob.setupHeader.should('exist');
            ValuationJob.jobSetupStep.should('have.class', 'active');
        });
    });

    describe('Photo and web buttons', () => {
        it('Upload photos button', () => {
            PropertyDetails.photoUploaderButton.should('exist').and('be.visible');
        });

        it('Web button for google search', () => {
            PropertyDetails.webButton.should('exist').and('be.visible');
        });
    });

    describe('QV left toolbar', () => {
        //TODO: All 4 tabs
        it('Extra Details present', () => {
            PropertyDetails.toolbarLeft
                .contains('a', 'Extra Details')
                .should('exist')
                .click();
            PropertyDetails.monarchDetails.should('exist').and('have.class', 'active');
        });
        //TODO: check labels for other sections also
        PropertyDetails.generalPropertyInfoLabels.forEach(label => {
            it(`Label: ${label} present`, () => {
                PropertyDetails.monarchDetails.contains('span', label).should('exist');
            });
        });
        PropertyDetails.propertySummaryLabels.forEach(label => {
            it(`Label: ${label} present`, () => {
                PropertyDetails.monarchDetails.contains('span', label).should('exist');
            });
        });
    });
});

describe.skip('SRA Values', () => {
    describe('SRA Values', () => {
        it('Quick search and enter', () => {
            Home.quickSearchBar.type(2557853);
            Home.quickSearchBar.type('{enter}');
            cy.wait(1000);
            PropertyDetails.propertyInformationLeftMenu.should('exist');
        });
        it('Go to SRA Valuation Screen', () => {
            PropertyDetails.getSraValuationMenuText.should('contain.text', 'YES');
            cy.wait(3000);
            PropertyDetails.getSraValuationMenuAction.should('exist').click();
            PropertyDetails.sraValuationSection.should('exist');
        });

        it('insert SRA Row with Error', () => {
            cy.wait(5000);
            PropertyDetails.sraRowAddAreaInput.type(0.01);
            PropertyDetails.sraRowAddCVInput.type(0);
            PropertyDetails.sraRowAddLVInput.type(0);
            PropertyDetails.sraRowAddRevisionCVInput.type(0);
            PropertyDetails.sraRowAddRevisionLVInput.type(0);
            PropertyDetails.sraRowAddButton.click();
            PropertyDetails.sraRowAddIVInput.should('contain.value', 0);
            PropertyDetails.sraRowAddRevisionIVInput.should('contain.value', 0);
            PropertyDetails.sraErrorMeessages.should('exist').should('be.visible');
        });

        it('Update and Insert Sra Row without reason selection', () => {
            cy.wait(1000);
            PropertyDetails.sraRowOneAreaToUpdate.type(0.15).then(elem => { elem.val(0.15); });
            PropertyDetails.sraRowAddSraDropDown.select('1042-117-361-  -4');
            PropertyDetails.sraRowAddAreaInput.type(0.01);
            PropertyDetails.sraRowAddCVInput.type(0);
            PropertyDetails.sraRowAddLVInput.type(0);
            PropertyDetails.sraRowAddRevisionCVInput.type(0);
            PropertyDetails.sraRowAddRevisionLVInput.type(0);
            PropertyDetails.sraRowAddButton.click();
            PropertyDetails.updateSraData.click();
            PropertyDetails.sraErrorMeessages.should('exist').should('be.visible');
        });

        it('Update and Insert Sra Row with reason selection', () => {
            PropertyDetails.sraOutPutCode.select('1');
            PropertyDetails.sraReasonSource.select('2');
            PropertyDetails.sraReasonForChange.type('correction goes here');
            PropertyDetails.updateSraData.click();
            cy.wait(1000);
            PropertyDetails.sraValuationSection.should('not.exist');
        });

        it('Go back to SRA Valuation Screen', () => {
            PropertyDetails.getSraValuationMenuText.should('contain.text', 'YES');
            cy.wait(3000);
            PropertyDetails.getSraValuationMenuAction.should('exist').click();
        });

        it('Update and delete SRA Row', () => {
            cy.wait(5000);
            PropertyDetails.sraRowDeleteZero.click();
            PropertyDetails.sraRowUpdateTwo.type(0.16).then(elem => { elem.val(0.16); });
            PropertyDetails.sraOutPutCode.select('1');
            PropertyDetails.sraReasonSource.select('2');
            PropertyDetails.sraReasonForChange.type('correction goes here');
            PropertyDetails.updateSraData.click();
            cy.wait(1000);
            PropertyDetails.sraValuationSection.should('not.exist');
        });

        it('Go back to SRA Valuation Screen', () => {
            PropertyDetails.getSraValuationMenuText.should('contain.text', 'YES');
            cy.wait(3000);
            PropertyDetails.getSraValuationMenuAction.should('exist').click();
        });

        it('Update and delete SRA Row for No Owner Notice with cancel button event', () => {
            cy.wait(5000);
            PropertyDetails.sraRowOneAreaToUpdate.type(0.15).then(elem => { elem.val(0.15); });
            PropertyDetails.sraRowAddSraDropDown.select('1042-117-361-  -4');
            PropertyDetails.sraRowAddAreaInput.type(0.01);
            PropertyDetails.sraRowAddCVInput.type(0);
            PropertyDetails.sraRowAddLVInput.type(0);
            PropertyDetails.sraRowAddRevisionCVInput.type(0);
            PropertyDetails.sraRowAddRevisionLVInput.type(0);
            PropertyDetails.sraRowAddButton.click();
            PropertyDetails.sraOutPutCode.select('7');
            PropertyDetails.sraReasonSource.select('2');
            PropertyDetails.sraReasonForChange.type('correction goes here');
            PropertyDetails.updateSraData.click();
            PropertyDetails.sraAlertModel.should('be.visible');
            PropertyDetails.alertModalCancelButton.click();
            PropertyDetails.sraValuationSection.should('exist');
        });

        it('Update and delete SRA Row for No Owner Notice with ok button event', () => {
            PropertyDetails.updateSraData.click();
            PropertyDetails.sraAlertModel.should('be.visible');
            PropertyDetails.alertModalOkButton.click();
            cy.wait(1000);
            PropertyDetails.sraValuationSection.should('not.exist');
        });

        it('Go back to SRA Valuation Screen to remove testing data', () => {
            PropertyDetails.getSraValuationMenuText.should('contain.text', 'YES');
            cy.wait(3000);
            PropertyDetails.getSraValuationMenuAction.should('exist').click();
        });

        it('Remove testing data SRA Row', () => {
            cy.wait(5000);
            PropertyDetails.sraRowDeleteZero.click();
            PropertyDetails.sraRowUpdateTwo.type(0.16).then(elem => { elem.val(0.16); });
            PropertyDetails.sraOutPutCode.select('1');
            PropertyDetails.sraReasonSource.select('2');
            PropertyDetails.sraReasonForChange.type('correction goes here');
            PropertyDetails.updateSraData.click();
            cy.wait(1000);
            PropertyDetails.sraValuationSection.should('not.exist');
        });
    });
});

// A property that has a reval survey should be able to schedule an extract from the property info panel
import PropertyDetails from '../../model/PropertyDetails';
import PropertyInfoPanel from '../../model/PropertyInfoPanel';
// A property with a reval survey that can also have rural worksheet (survey_entry record with submitted_date_time)
const qpid = 3114314;
const myReportsPath = '/reports/my-reports';

describe('Property Info Reval Survey Link', { defaultCommandTimeout: 25000 }, () => {
    context('From Master Details', () => {
        // get current timestamp rounded down to the nearest minute
        const currentTimeStamp = Math.floor(new Date().getTime() / (1000 * 60)) * (1000 * 60);
        before(() => {
            cy.login();
            PropertyDetails.visit(qpid);
        });

        it('Reval Survey Link should appear in the property info panel', () => {
            cy.get('[data-cy="reval-survey-link"]').should('exist');
            cy.get('[data-cy="reval-survey-yn"]').should('exist').and('have.text', 'YES');
        });

        it('Reval Survey Link should schedule a survey report and open modal', () => {
            cy.intercept('POST', '/report-job').as('createReportJob');
            cy.get('[data-cy="reval-survey-link"]').click();
            cy.wait('@createReportJob').then((interception) => {
                expect(interception.response.statusCode).to.equal(201);
            });
            cy.get('[data-cy="noWorksheetModalCancelButton"]').should('exist').and('be.visible');
        });

        it('On "My Reports" page, there should be a new survey report with status "Ready"', () => {
            cy.visit(myReportsPath);
            // allow generous time for the report to generate
            cy.wait(5000);
            cy.get('[data-cy="my-reports-refresh-button"]').click();
            cy.get('[data-cy="my-reports-table"]').should('exist');
            cy.get('[data-cy="my-reports-table-row"]').first().find('[data-cy="report-name"]').should('have.text', 'Revaluation Survey Results');
            cy.get('[data-cy="my-reports-table-row"]').first().find('[data-cy="report-date"]').invoke('text').then((dateString) => {
                expect(new Date(dateString).getTime()).to.be.gte(currentTimeStamp);
            });
            cy.get('[data-cy="my-reports-table-row"]').first().find('[data-cy="report-status"]').should('contain.text', 'Ready');
        });
    });

    // this page uses property info panel component (master details doesnt use this component)
    context('From Rural Worksheet', () => {
        // get current timestamp rounded down to the nearest minute
        const currentTimeStamp = Math.floor(new Date().getTime() / (1000 * 60)) * (1000 * 60);
        before(() => {
            cy.login();
            cy.visit(`/roll-maintenance/rural-worksheet/${qpid}`);
        });

        it('Reval Survey Link should appear in the property info panel', () => {
            cy.get('[data-cy="reval-survey-link"]').should('exist');
            cy.get('[data-cy="reval-survey-yn"]').should('exist').and('have.text', 'YES');
        });

        it('Reval Survey Link should schedule a survey report and open modal', () => {
            cy.intercept('POST', '/report-job').as('createReportJob');
            cy.get('[data-cy="reval-survey-link"]').click();
            cy.wait('@createReportJob').then((interception) => {
                expect(interception.response.statusCode).to.equal(201);
            });
            cy.get('[data-cy="modal-cancel-button"]').should('exist').and('be.visible');
        });

        it('On "My Reports" page, there should be a new survey report with status "Ready"', () => {
            cy.visit(myReportsPath);
            // allow generous time for the report to generate
            cy.wait(5000);
            cy.get('[data-cy="my-reports-refresh-button"]').click();
            cy.get('[data-cy="my-reports-table"]').should('exist');
            cy.get('[data-cy="my-reports-table-row"]').first().find('[data-cy="report-name"]').should('have.text', 'Revaluation Survey Results');
            cy.get('[data-cy="my-reports-table-row"]').first().find('[data-cy="report-date"]').invoke('text').then((dateString) => {
                expect(new Date(dateString).getTime()).to.be.gte(currentTimeStamp);
            });
            cy.get('[data-cy="my-reports-table-row"]').first().find('[data-cy="report-status"]').should('contain.text', 'Ready');
        });

    });
});

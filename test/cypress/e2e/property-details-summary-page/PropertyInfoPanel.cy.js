import PropertyInfoPanel from '../../model/PropertyInfoPanel';
import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';
import user from '../../support/user.js';
import testFactory from '../../support/testFactory.js';

let property;

describe('Property Details', { defaultCommandTimeout: 15000 }, () => {
    describe('Normal Property', () => {
        before(() => {
            property = {qpid: 9001} // await testFactory.getProperty(); // FIXME: testFactory call needs to be updated
        });

        it('Search address that is a normal property', () => {
            cy.visitWithUser('', user.INTERNAL_ADMIN_USER);

            Home.quickSearchBar.type(property.qpid);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
            Home.typeAheadResults.contains('span.listBox-category', property.qpid).should('exist');
        });

        it('Verify on property', () => {
            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then((text) => {
                expect(parseInt(text)).to.equal(property.qpid);
            });

            checkPropertyInformation();
        });

        after(() => {
            Home.quickSearchBar.clear();
        });
    });

    describe('Rural Worksheet', () => {
        it('Search address that can have a Rural Worksheet', () => {
            cy.visitWithUser('', user.INTERNAL_ADMIN_USER);

            Home.quickSearchBar.type(env.ADDRESS5.SEARCH_TERM);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
            Home.typeAheadResults.contains('span.listBox-category', env.ADDRESS5.QPID).should('exist');
        });

        it('Verify on property', () => {
            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then((text) => {
                expect(text).to.equal(env.ADDRESS5.QPID);
            });
            PropertyInfoPanel.singleRuralWorksheetHeader.should('exist');
            PropertyInfoPanel.singleRuralWorksheetHeader.contains('Rural Worksheet');
            PropertyInfoPanel.singleRuralWorksheetValue.should('exist');
            PropertyInfoPanel.propertyInfoHeader.should('exist');
            checkPropertyInformation();
            PropertyInfoPanel.autoWorksheetHeader.should('exist');
            PropertyInfoPanel.autoWorksheetSwitch.should('exist');
        });

        testHazardNotes();

        after(() => {
            Home.quickSearchBar.clear();
        });
    });

    describe('Commercial Worksheet', () => {
        it('Search address that can have a Commercial Worksheet', () => {
            cy.visitWithUser('', user.INTERNAL_ADMIN_USER);

            Home.quickSearchBar.type(env.ADDRESS6.SEARCH_TERM);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
        });

        it('Verify on property', () => {
            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then((text) => {
                expect(text).to.equal(env.ADDRESS6.QPID);
            });
            PropertyInfoPanel.singleCommercialWorksheetHeader.should('exist');
            PropertyInfoPanel.singleCommercialWorksheetHeader.contains('Commercial Worksheet');
            PropertyInfoPanel.singleCommercialWorksheetValue.should('exist');
            PropertyInfoPanel.propertyInfoHeader.should('exist');
            checkPropertyInformation();
        });

        after(() => {
            Home.quickSearchBar.clear();
        });
    });

    describe('Both Worksheet', () => {
        it('Search address that can have a Both Worksheet', () => {
            cy.visitWithUser('', user.INTERNAL_ADMIN_USER);

            Home.quickSearchBar.type(env.ADDRESS7.SEARCH_TERM);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
        });

        it('Verify on property', () => {
            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then((text) => {
                expect(text).to.equal(env.ADDRESS7.QPID);
            });
            PropertyInfoPanel.bothWorksheetRuralHeader.should('exist');
            PropertyInfoPanel.bothWorksheetRuralValue.should('exist');
            PropertyInfoPanel.bothWorksheetCommercialHeader.should('exist');
            PropertyInfoPanel.bothWorksheetCommercialValue.should('exist');
            PropertyInfoPanel.propertyInfoHeader.should('exist');
            checkPropertyInformation();
        });

        after(() => {
            Home.quickSearchBar.clear();
        });
    });

    function checkPropertyInformation() {
        PropertyInfoPanel.consentsLink.should('exist');
        PropertyInfoPanel.consentsValue.should('exist');

        PropertyInfoPanel.subdivisionsLink.should('exist');
        PropertyInfoPanel.subdivisionsValue.should('exist');

        PropertyInfoPanel.objectionsLink.should('exist');
        PropertyInfoPanel.objectionsValue.should('exist');

        PropertyInfoPanel.valuationDataLink.should('exist');
        PropertyInfoPanel.valuationDataValue.should('exist');

        PropertyInfoPanel.sraValuesLink.should('exist');
        PropertyInfoPanel.sraValuesValue.should('exist');

        PropertyInfoPanel.autoMasHeader.should('exist');
        PropertyInfoPanel.autoMasSwitch.should('exist');

        PropertyInfoPanel.floorPlansLink.should('exist');
        PropertyInfoPanel.floorPlansValue.should('exist');

        PropertyInfoPanel.surveyPlansLink.should('exist');
        PropertyInfoPanel.surveyPlansValue.should('exist');

        PropertyInfoPanel.sitePlansLink.should('exist');
        PropertyInfoPanel.sitePlansValue.should('exist');

        PropertyInfoPanel.attachmentsLink.should('exist');
        PropertyInfoPanel.attachmentsValue.should('exist');

        PropertyInfoPanel.propertyFileHeader.should('exist');
        PropertyInfoPanel.propertyFileSwitch.should('exist');

        PropertyInfoPanel.suspectValuationHeader.should('exist');
        PropertyInfoPanel.suspectValuationSwitch.should('exist');
    }

    // Define the function that encapsulates the tests for hazard notes
    function testHazardNotes() {
        it('Should open the modal and check for visibility', () => {
            PropertyInfoPanel.openModalButton.first().click();
            PropertyInfoPanel.alertModal.should('be.visible');
            PropertyInfoPanel.modalTitle.should('contain', 'Notes for Valuer');
        });

        it('Should allow entry of notes and close modal on confirm', () => {
            // Arrange - Open the modal
            // PropertyInfoPanel.openModalButton.first().click();

            // Act - Enter notes and confirm
            const sampleNote = 'Example hazard note for testing.';
            PropertyInfoPanel.notesInput.type(sampleNote);
            PropertyInfoPanel.confirmButton.click();

            // Assert - Modal should close
            PropertyInfoPanel.alertModal.should('not.exist');
        });

        it('Should reset notes on cancel', () => {
            // Arrange - Open the modal and enter text
            PropertyInfoPanel.openModalButton.first().click();
            const tempNote = 'Temporary note';
            PropertyInfoPanel.notesInput.type(tempNote);

            // Act - Cancel the action
            PropertyInfoPanel.cancelButton.click();

            // Assert - Modal should close
            PropertyInfoPanel.alertModal.should('not.exist');

            // Reopen the modal to check if the notes were reset
            PropertyInfoPanel.openModalButton.first().click();
            PropertyInfoPanel.notesInput.should('not.have.value', 'Temporary note');

        });
    }
});

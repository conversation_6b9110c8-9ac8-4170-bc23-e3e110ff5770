import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';
import PropertyToolbar from '../../model/PropertyToolbar';
import user from '../../support/user.js';
import testFactory from '../../support/testFactory.js';

let property;

describe('Property Details', { defaultCommandTimeout: 15000 }, () => {
    describe('"New" Monarch Property Toolbar', () => {
        before(() => {
            property = {qpid: 9001} // await testFactory.getProperty();// FIXME: testFactory call needs to be updated
        });

        it('Navigate to "New" Monarch Property page', () => {
            cy.wait(5000);
            cy.visitWithUser('', user.INTERNAL_ADMIN_USER);

            Home.quickSearchBar.type(property.qpid);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
            Home.typeAheadResults.contains(':nth-child(1) > a > :nth-child(5) > strong', property.qpid).should('exist');

            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then((text) => {
                expect(parseInt(text)).to.equal(property.qpid);
            });
            Home.quickSearchBar.clear();

            cy.wait(2000);
            PropertyToolbar.oldMonarchExtraDetailsTab.click();
            cy.wait(500);
            PropertyToolbar.oldMonarchExtraDetailsTab.should('have.class', 'active');
            cy.get('button.edit-property-detail').click();
        });

        it('Verify toolbar exists', () => {
            checkPropertyToolbar();
        });
    });

    function checkPropertyToolbar() {
        PropertyToolbar.propertyToolbar.should('exist');
        PropertyToolbar.propertyToolbar.should('exist');
        PropertyToolbar.propertyToolbarSummaryTab.should('exist');
        PropertyToolbar.propertyToolbarExtraDetailsTab.should('exist');
        PropertyToolbar.propertyToolbarValuationJobsTab.should('exist'); // assuming internal user
        PropertyToolbar.propertyToolbarRollMaintenanceTab.should('exist'); // assuming internal user
        PropertyToolbar.propertyToolbarMapTab.should('exist'); // assuming internal user
        PropertyToolbar.propertyToolbarPhotoUploaderLink.should('exist');
        PropertyToolbar.propertyToolbarQivsLink.should('exist');
        PropertyToolbar.propertyToolbarFloorPlansLink.should('exist');
        PropertyToolbar.propertyToolbarMapLink.should('exist'); // assuming internal user
        PropertyToolbar.propertyToolbarGoogleLink.should('exist');
    }
});

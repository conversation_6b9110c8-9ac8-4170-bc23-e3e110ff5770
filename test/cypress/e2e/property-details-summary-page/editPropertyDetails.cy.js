import PropertyDetails from '../../model/PropertyDetails.js';
import CommercialWorksheet from '../../model/CommercialWorksheet.js';
import ExtraPropertyDetails from '../../model/ExtraPropertyDetails.js';
import PropertyToolbar from '../../model/PropertyToolbar.js';

describe(
    'Edit Property Details',
    {
        testIsolation: false,
        defaultCommandTimeout: 30000,
    },
    () => {
        const qpid = 2996267;

        context('As an internal user', () => {
            before(() => {
                cy.login();
            });

            context('Property Details', () => {
                context('Commercial Property', () => {
                    it('Should be able to see Monarch Details page successfully', () => {
                        PropertyDetails.visit(qpid);
                        PropertyToolbar.oldMonarchExtraDetailsTab.click();
                        ExtraPropertyDetails.isLoaded();
                    });
                    it('Should be able to open edit mode', () => {
                        // there is a tab bug where it switch back after you click on different tab
                        // have to revisit the page
                        CommercialWorksheet.visit(qpid);
                        CommercialWorksheet.isLoaded();
                        PropertyToolbar.oldMonarchExtraDetailsTab.click();
                        ExtraPropertyDetails.isLoaded();
                        ExtraPropertyDetails.editPropertyDetails.editPropertyDetailButton.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.editPropertyDetailButton.click();
                        ExtraPropertyDetails.editPropertyDetails.isLoaded();
                    });
                    it('Should be able to see the sections', () => {
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.section.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.propertyInfo.title.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.locationDetails.title.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.propertySummary.title.should('exist');
                    });
                    it('Should be able to see the new fields in General Information section', () => {
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.qvCategoryInput.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.groupingInput.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.actualEarthquakeRatingInput.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.earthquakeRatingRangeInput.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.earthquakeRatingAssessorInput.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.remedyDeadlineInput.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.liquefactionInput.should('exist');
                    });
                    it('Should be able to see the new fields in other sections', () => {
                        ExtraPropertyDetails.editPropertyDetails.dateEntered.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.propertyInfo.effectiveLandArea.should('exist');
                    });
                    it('Should be able to see all the buttons', () => {
                        ExtraPropertyDetails.editPropertyDetails.cancelButton.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.saveButton.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.saveAndCloseButton.should('exist');
                    });
                    it('Should display error when remedy deadline is not 4 digit', () => {
                        const remedyDeadline = '20';
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.remedyDeadlineInput.clear().type(remedyDeadline);

                        ExtraPropertyDetails.editPropertyDetails.generalInfo.section.should('contain', 'Please enter a valid year');
                    });
                    it('Should display error when date is not entered', () => {
                        ExtraPropertyDetails.editPropertyDetails.dateEntered.focus();
                        ExtraPropertyDetails.editPropertyDetails.dateEntered.clear();
                        ExtraPropertyDetails.editPropertyDetails.dateEntered.blur();

                        ExtraPropertyDetails.editPropertyDetails.container.should('contain', 'Entered Date must be in format dd/mm/yyyy.');
                    });
                    it('Should be able to edit the Monarch Details page', () => {
                        // there is a tab bug where it switch back after you click on different tab
                        // have to revisit the page
                        CommercialWorksheet.visit(qpid);
                        CommercialWorksheet.isLoaded();
                        PropertyToolbar.oldMonarchExtraDetailsTab.click();
                        ExtraPropertyDetails.isLoaded();
                        ExtraPropertyDetails.editPropertyDetails.editPropertyDetailButton.should('exist');
                        ExtraPropertyDetails.editPropertyDetails.editPropertyDetailButton.click();
                        ExtraPropertyDetails.editPropertyDetails.isLoaded();

                        const qvCategory = 'CMS - Commercial-Motor Vehicle-Suburban';
                        const grouping = 'Grade B Office Buildings';
                        const actualEarthquakeRating = '80';
                        const earthquakeRatingRange = '67-100%';
                        const earthquakeRatingAssessor = 'Unknown';
                        const remedyDeadline = '2030';
                        const liquefaction = 'TC1';

                        ExtraPropertyDetails.editPropertyDetails.generalInfo.qvCategoryInput.click();
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.qvCategoryInput.focus();
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.qvCategoryInput.contains('.multiselect__option', qvCategory).parent('li').click();

                        ExtraPropertyDetails.editPropertyDetails.generalInfo.groupingInput.click();
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.groupingInput.focus();
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.groupingInput.contains('.multiselect__option', grouping).parent('li').click();

                        ExtraPropertyDetails.editPropertyDetails.generalInfo.actualEarthquakeRatingInput.clear().type(actualEarthquakeRating);

                        ExtraPropertyDetails.editPropertyDetails.generalInfo.earthquakeRatingRangeInput.click();
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.earthquakeRatingRangeInput.contains('.multiselect__option', earthquakeRatingRange).parent('li').click();

                        ExtraPropertyDetails.editPropertyDetails.generalInfo.earthquakeRatingAssessorInput.click();
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.earthquakeRatingAssessorInput.contains('.multiselect__option', earthquakeRatingAssessor).parent('li').click();

                        ExtraPropertyDetails.editPropertyDetails.generalInfo.remedyDeadlineInput.clear().type(remedyDeadline);

                        ExtraPropertyDetails.editPropertyDetails.generalInfo.liquefactionInput.click();
                        ExtraPropertyDetails.editPropertyDetails.generalInfo.liquefactionInput.contains('.multiselect__option', liquefaction).parent('li').click();

                        ExtraPropertyDetails.editPropertyDetails.saveButton.click();
                        ExtraPropertyDetails.editPropertyDetails.successModalAppears();

                        cy.reload().then(() => {
                            ExtraPropertyDetails.editPropertyDetails.isLoaded();

                            const fieldPromises = [
                                ExtraPropertyDetails.editPropertyDetails.generalInfo.qvCategoryInput.find('.multiselect__single').invoke('text'),
                                ExtraPropertyDetails.editPropertyDetails.generalInfo.groupingInput.find('.multiselect__single').invoke('text'),
                                ExtraPropertyDetails.editPropertyDetails.generalInfo.actualEarthquakeRatingInput.invoke('val'),
                                ExtraPropertyDetails.editPropertyDetails.generalInfo.earthquakeRatingAssessorInput.find('.multiselect__single').invoke('text'),
                                ExtraPropertyDetails.editPropertyDetails.generalInfo.remedyDeadlineInput.invoke('val'),
                                ExtraPropertyDetails.editPropertyDetails.generalInfo.liquefactionInput.find('.multiselect__single').invoke('text')
                            ];

                            Promise.all(fieldPromises).then(([qvCategoryValue, groupingValue, actualEarthquakeRatingValue, earthquakeRatingAssessorValue, remedyDeadlineValue, liquefactionValue]) => {
                                expect(qvCategoryValue).to.equal(qvCategory);
                                expect(groupingValue).to.equal(grouping);
                                expect(actualEarthquakeRatingValue).to.equal(actualEarthquakeRating);
                                expect(earthquakeRatingAssessorValue).to.equal(earthquakeRatingAssessor);
                                expect(remedyDeadlineValue).to.equal(remedyDeadline);
                                expect(liquefactionValue).to.equal(liquefaction);
                            })

                        });
                    });
                });
            });
        });
    }
);

import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe('Validate Commercial Worksheet', { testIsolation: false, defaultCommandTimeout: 15000 }, () => {
    const qpid = 2911074;
    const qpidWithZeroCVIncome = 430742;
    context('clicking [Update Worksheet Only] - button', () => {
        before(() => {
            cy.login();
            CommercialWorksheet.visit(qpid);
            CommercialWorksheet.isLoaded();
        });

        it('should skip reason for change validation', () => {
            CommercialWorksheet.propertyDetail.capRate.clear().type(0.0001);
            CommercialWorksheet.rfc.reason.clear();
            CommercialWorksheet.updateWorksheetOnly();
            cy.get('[data-cy="cw-validation-title"]').should('exist').and('be.visible');
            cy.get(`[data-cy=validation-messages-errors-li]`).should('not.contain', '- Please complete the reason for change');
            cy.get('[data-cy="button-cw-validation-return"]').click();
        });

        it('should validate Cap rate', () => {
            CommercialWorksheet.propertyDetail.capRate.clear().type(0.0001);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Invalid Cap Rate.');
        });

        it('should validate Cap rate when valuation method is [CV Income]', () => {
            CommercialWorksheet.propertyDetail.capRate.clear().type(0.0001);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Invalid Cap Rate.');
        });

        it('should validate Land Row', () => {
            CommercialWorksheet.land.rows.each(($row) => {
                cy.wrap($row).find('[data-cy="cw-remove-land-row"]').click();
            });
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- At least one land row must have a value.');
        });

        it('should validate Land Row missing details', () => {
            CommercialWorksheet.land.newRowArea.clear().type(1418.2);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Please enter a [Street Location/Area m²/Rate/m²]');
        });

        it('should validate Area and car Parks details', () => {
            CommercialWorksheet.improvements.rows.each(($row) => {
                cy.wrap($row).find('[data-cy="cw-remove-improvement-row"]').click();
            });
            CommercialWorksheet.improvements.newRowDescription.clear().type('Test: Description');
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(2)').click();
            CommercialWorksheet.improvements.newRowArea.clear().type(1000);
            CommercialWorksheet.improvements.newRowCarparks.clear().type(2);
            cy.get('[data-cy="cw-dialog-title"]').should('exist').and('be.visible');
            cy.get('[data-cy="cw-modal-message"]').contains('Area and car parks cannot be used in the same calculation.').should('exist');
            cy.get('[data-cy="cw-button-dialog-confirm"]').should('exist').and('be.visible').click();
        });

        it('should validate Area and car Parks when Rental is entered', () => {
            CommercialWorksheet.improvements.newRowArea.clear();
            CommercialWorksheet.improvements.newRowCarparks.clear();
            CommercialWorksheet.improvements.newRowRent.clear().type(982);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Area is required when the parks field is empty and a rental amount is entered');
        });

        it('should validate Area and car Parks when Vac% is entered', () => {
            CommercialWorksheet.improvements.newRowArea.clear();
            CommercialWorksheet.improvements.newRowRent.clear();
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(1)').click();
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(2)').click();
            CommercialWorksheet.improvements.newRowVacant.clear().type(89);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Rental and Area or Parks must be entered when Vac% is used.');
        });

        it('should validate missing rental when Valuation metod is [CV INCOME]', () => {
            CommercialWorksheet.totals.cvIncomeRadio.click();
            CommercialWorksheet.improvements.newRowArea.clear().type(100);
            CommercialWorksheet.improvements.newRowRent.clear();
            CommercialWorksheet.improvements.newRowVacant.clear();
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Improvements row has missing rental.');
        });

        it('should validate Improvement type', () => {
            CommercialWorksheet.improvements.newRowArea.clear().type(100);
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(2)').click();
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Improvement data has been entered but no improvement type is selected.');
        });

        it('should validate missing data', () => {
            CommercialWorksheet.improvements.newRowArea.clear().type(100);
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(2)').click();
            CommercialWorksheet.improvements.newRowDescription.clear();
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- CV or VI improvement data has been entered but is incomplete.');
        });

        it('should validate missing Date Rent', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.clear();
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(100);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Date Rent Set is required.');
        });

        it('should validate missing annual rental', () => {
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(1418.2);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Annual Rental is required.');
        });

        it('should validate Future Date Rent', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.type('2034-01-01');
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Date cannot be in the future.');
        });

        it('should validate Date Rent older than 15years', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.type('2000-01-01');
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Date must be within 15 years.');
        });

        it('should validate Future Date Rent', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.type('2034-01-01');
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Date cannot be in the future.');
        });

        it('should validate Date Rent older than 15 years', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.type('2000-01-01');
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(100);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Date must be within 15 years.');
        });

        it('should validate missing Date Rent', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.clear();
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(100);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Date Rent Set is required.');
        });

        it('should validate missing annual rental', () => {
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(1418.2);
            CommercialWorksheet.updateWorksheetOnly();
            CommercialWorksheet.validationModalAppears('errors', '- Annual Rental is required.');
        });

        it('should skip validation for selected valuation method having zero CV', () => {
            CommercialWorksheet.totals.cvIncomeRadio.click(); // select CV income as valuation method
            CommercialWorksheet.updateWorksheetOnly();
            cy.get('[data-cy="cw-validation-title"]').should('exist').and('be.visible');
            cy.get(`[data-cy=validation-messages-errors-li]`).should('not.contain', '- Selected valuation method has zero CV.');
        });
    });

    context('Clicking [Update Assessment]- button', () => {
        before(() => {
            CommercialWorksheet.visit(qpid);
            CommercialWorksheet.isLoaded();
        });

        it('should display an error message if the reason for change is incomplete', () => {
            CommercialWorksheet.rfc.reason.clear();
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.validationModalAppears('errors', '- Please complete the reason for change');
        });

        it('should display a warning message for owner notice if the reason for change output is 7 and Lv or CV is changed', () => {
            CommercialWorksheet.fillReasonForChange();
            CommercialWorksheet.rfc.output.click().find('.multiselect__element:nth-child(5)').click();
            CommercialWorksheet.land.rows.first().get('[data-cy="cw-land-row-area"]').clear().type(3406.98);
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.validationModalAppears('warnings', '- CV or LV has changed but no owner notice will be produced');
        });

        it('should validate Cap rate', () => {
            CommercialWorksheet.propertyDetail.capRate.clear().type(0.00485);
            CommercialWorksheet.fillReasonForChange();
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Invalid Cap Rate.');
        });

        it('should validate Land Row', () => {
            CommercialWorksheet.land.rows.each(($row, index, $rows) => {
                cy.wrap($row).find('[data-cy="cw-remove-land-row"]').click();
            });
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- At least one land row must have a value.');
        });

        it('should validate Land Row missing details', () => {
            CommercialWorksheet.land.newRowArea.clear().type(1418.2);
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Please enter a [Street Location/Area m²/Rate/m²]');
        });

        it('should validate Area and car Parks details', () => {
            CommercialWorksheet.improvements.rows.each(($row) => {
                cy.wrap($row).find('[data-cy="cw-remove-improvement-row"]').click();
            });
            CommercialWorksheet.improvements.newRowDescription.clear().type('Test: Description');
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(2)').click();
            CommercialWorksheet.improvements.newRowArea.clear().type(1000);
            CommercialWorksheet.improvements.newRowCarparks.clear().type(2);
            cy.get('[data-cy="cw-dialog-title"]').should('exist').and('be.visible');
            cy.get('[data-cy="cw-modal-message"]').contains('Area and car parks cannot be used in the same calculation.').should('exist');
            cy.get('[data-cy="cw-button-dialog-confirm"]').should('exist').and('be.visible').click();
        });

        it('should validate Area and car Parks when Rental is entered', () => {
            CommercialWorksheet.improvements.newRowArea.clear();
            CommercialWorksheet.improvements.newRowRent.clear().type(982);
            CommercialWorksheet.improvements.newRowCarparks.clear();
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Area is required when the parks field is empty and a rental amount is entered');
        });

        it('should validate Area and car Parks when Vac% is entered', () => {
            CommercialWorksheet.improvements.newRowArea.clear();
            CommercialWorksheet.improvements.newRowRent.clear();
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(1)').click();
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(2)').click();
            CommercialWorksheet.improvements.newRowVacant.clear().type(89);
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Rental and Area or Parks must be entered when Vac% is used.');
        });

        it('should validate missing rental when Valuation metod is [CV INCOME]', () => {
            CommercialWorksheet.totals.cvIncomeRadio.click();
            CommercialWorksheet.improvements.newRowArea.clear().type(100);
            CommercialWorksheet.improvements.newRowRent.clear();
            CommercialWorksheet.improvements.newRowVacant.clear();
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Improvements row has missing rental.');
        });

        it('should validate Improvement type', () => {
            CommercialWorksheet.improvements.newRowArea.clear().type(100);
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(2)').click();
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Improvement data has been entered but no improvement type is selected.');
        });

        it('should validate missing data', () => {
            CommercialWorksheet.improvements.newRowArea.clear().type(100);
            CommercialWorksheet.improvements.newRowImpType.click().find('.multiselect__element:nth-child(2)').click();
            CommercialWorksheet.improvements.newRowDescription.clear();
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- CV or VI improvement data has been entered but is incomplete.');
        });

        it('should validate missing Date Rent', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.clear();
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(1000);
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Date Rent Set is required.');
        });

        it('should validate missing annual rental', () => {
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(1418.2);
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Annual Rental is required.');
        });

        it('should validate Future Date Rent', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.clear().type('2034-01-01');
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Date cannot be in the future.');
        });

        it('should validate Future Date Rent older than 15years', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.clear().type('2000-01-01');
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Date must be within 15 years.');
        });

        it('should validate Future Date Rent', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.type('2034-01-01');
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Date cannot be in the future.');
        });

        it('should validate Future Date Rent older than 15 years', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.type('2000-01-01');
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(100);
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Date must be within 15 years.');
        });

        it('should validate missing Date Rent', () => {
            CommercialWorksheet.actualRentals.newRowDateRent.clear();
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(1000);
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Date Rent Set is required.');
        });

        it('should validate missing annual rental', () => {
            CommercialWorksheet.actualRentals.newRowFloorArea.clear().type(1418.2);
            CommercialWorksheet.updateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.validationModalAppears('errors', '- Annual Rental is required.');
        });

        context('Validate Valuation method having zero CV', () => {
            before(() => {
                CommercialWorksheet.visit(qpidWithZeroCVIncome);
                CommercialWorksheet.isLoaded();
            });

            it('should display an error for selected CV Income valuation method having zero CV', () => {
                CommercialWorksheet.propertyDetail.capRate.clear().type(0.0001);
                CommercialWorksheet.totals.cvIncomeRadio.click();
                CommercialWorksheet.fillReasonForChange();
                CommercialWorksheet.updateAssessment();
                CommercialWorksheet.acceptNewValues();
                CommercialWorksheet.validationModalAppears('errors', '- Selected valuation method has zero CV.');
            });
        });
    });
});

import CommercialWorksheet from '../../model/CommercialWorksheet.js';

// relying on apportionments
// relying on imp rows being filled out
// relying on no validation errors
// cv income method

// const qpid = 3420095;
const qpid = 2493723;
const capRate = 8.5;
const planNumber = Math.floor(10000 + Math.random() * 90000);
const rental = {
    floorArea: 52.0,
    grossRental: 4576,
    rentalDate: '2021-01-01',
    rentalPerSqmt: 88,
    tenant: 'TEST TENANT',
};
const cvOverride = 20000
const lvOverride = 10000

describe('Update Assessment', { testIsolation: false, defaultCommandTimeout: 15000 }, () => {
    let int;
    let response;
    before(() => {
        cy.login();
        CommercialWorksheet.visit(qpid);
        CommercialWorksheet.isLoaded();

        CommercialWorksheet.updatePropertyDetails(capRate, planNumber);

        CommercialWorksheet.land.rows.first().find('[data-cy="cw-land-row-description"]').clear().type('TEST: Land Description');
        CommercialWorksheet.land.rows.first().find('[data-cy="cw-land-row-area"]').clear().type(971.13);
        CommercialWorksheet.land.rows.first().find('[data-cy="cw-land-row-rate-per-metre"]').clear().type(762);

        CommercialWorksheet.improvements.rows.first();
        CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-area"]').clear().type(159.81);
        CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-description"]').clear().type('TEST: Improvement Description');
        CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-multiple"]').clear().type(0);
        CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-percent-vacant"]').clear().type(0);
        CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-year-built"]').clear().type(1983);

        CommercialWorksheet.totals.cvIncomeRadio.click();
        CommercialWorksheet.totals.comment.clear().type(`TEST: COMMENT`);

        CommercialWorksheet.addNewActualRentalRow(rental);

        CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-cvOverride"]').clear().type(cvOverride);
        CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-lvOverride"]').clear().type(lvOverride);

        CommercialWorksheet.fillReasonForChange();
        CommercialWorksheet.interceptAndUpdateAssessment();
        CommercialWorksheet.acceptNewValues();
        CommercialWorksheet.dismissValidationWarnings();

        cy.wait('@updateWorksheet').then((interception) => {
            int = interception;
            response = int.response.body;
        });
    });

    describe("Changes in all worksheet sections are reflected in successful api save response after clicking 'Update Assessment' button", () => {
        it('Should have a successful response', () => {
            expect(int.response.statusCode).to.equal(200);
        });

        it('Property Detail section saves correctly', () => {
            expect(response.capRate).to.equal(capRate);
            expect(response.planNumber).to.equal(`${planNumber}`);
        });
        it('Land section saves correctly', () => {
            const landResponse = response.landRows[0];
            expect(landResponse.description).to.equal('TEST: Land Description');
            expect(landResponse.area).to.equal(971.13);
            expect(landResponse.ratePerMetre).to.equal(762);
        });
        it('Improvement section saves correctly', () => {
            const impResponse = response.improvementRows[0];
            expect(impResponse.area).to.equal(159.81);
            expect(impResponse.description).to.equal('TEST: Improvement Description');
            expect(impResponse.multiple).to.equal(0);
            expect(impResponse.percentVacant).to.equal(0);
            expect(impResponse.yearBuilt).to.equal(1983);
        });
        it('Worksheet totals section saves correctly', () => {
            expect(response.commercialValuationMethodId).to.equal(2);
            expect(response.comment).to.equal(`TEST: COMMENT`);
        });
        it('Actual Rentals section saves correctly', () => {
            const rentalRes = response.actualRentalRows[response.actualRentalRows.length - 1];
            expect(rentalRes.floorArea).to.equal(rental.floorArea);
            expect(rentalRes.grossRental).to.equal(rental.grossRental);
            expect(rentalRes.rentalDate.toString().split('T')[0]).to.equal(rental.rentalDate);
            expect(rentalRes.rentalPerSqmt).to.equal(rental.rentalPerSqmt);
            expect(rentalRes.tenant).to.equal(rental.tenant);
        });
        it('Adopted values section saves correctly', () => {
            const adoptedRes = response.adoptedValues[0];
            expect(adoptedRes.cvOverride).to.equal(cvOverride);
            expect(adoptedRes.lvOverride).to.equal(lvOverride);
        });
        it('Rfc section saves correctly', () => {
            expect(response.rfc.qpid).to.equal(qpid);
            expect(response.rfc.reason).to.equal('Test Reason for Change');
        });

        it('Success Modal is displayed', () => {
            CommercialWorksheet.successModalAppears();
        });
    });
});

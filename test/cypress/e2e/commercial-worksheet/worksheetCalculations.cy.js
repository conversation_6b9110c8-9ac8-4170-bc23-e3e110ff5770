import CommercialWorksheet from '../../model/CommercialWorksheet.js';

const QPID_WITHOUT_COMMERCIAL_WORKSHEET = 2544614;

describe(
    'Commercical Worksheet Calculations',
    {
        testIsolation: false,
        defaultCommandTimeout: 30000,
    },
    () => {
        const qpid = 2133860// 3420133;

        context('As an internal user', () => {
            before(() => {
                cy.login();
                CommercialWorksheet.visit(qpid);
            });

            it('Commercial worksheet page loads successfully', () => {
                cy.url().should('include', CommercialWorksheet.getPageRoute(qpid));
                CommercialWorksheet.isLoaded();
                CommercialWorksheet.allSectionsExist();
                CommercialWorksheet.allActionButtonsExist();
            });
            context('Land Section', () => {
                it('Entering land area on a new row updates the total land area', () => {
                    const areaToAdd = 500;
                    CommercialWorksheet.getInputValue('land', 'totalArea').then((totalLandArea) => {
                        CommercialWorksheet.land.newRow.find('[data-cy="cw-new-land-row-area"]').type(`${areaToAdd}`);
                        CommercialWorksheet.getInputValue('land', 'totalArea').should('equal', totalLandArea + areaToAdd);
                    });
                });

                it('Entering land rate on a new row updates the total land value', () => {
                    const rateToAdd = 10;
                    const areaToAdd = 500;
                    CommercialWorksheet.getInputValue('land', 'totalValue').then((totalLandValue) => {
                        CommercialWorksheet.land.newRow.find('[data-cy="cw-new-land-row-rate"]').type(`${rateToAdd}`);
                        CommercialWorksheet.getInputValue('land', 'totalValue').should('equal', totalLandValue + areaToAdd * rateToAdd);
                    });
                });

                describe('Can add new land row and then remove it', () => {
                    it('Clicking button', () => {
                        CommercialWorksheet.land.newRow.should('exist');
                        CommercialWorksheet.land.rows.then(($rows) => {
                            const rowCount = $rows.length;
                            CommercialWorksheet.land.newRowButton.click();
                            CommercialWorksheet.land.rows.should('have.length', rowCount + 1);
                            CommercialWorksheet.land.rows.last().find('[data-cy="cw-remove-land-row"]').click();
                            CommercialWorksheet.land.rows.should('have.length', rowCount);
                        });
                    });
                });
            });

            context('Improvements Section', () => {
                describe('Can add new improvement row and then remove it', () => {
                    it('Clicking button', () => {
                        CommercialWorksheet.improvements.newRow.should('exist');
                        CommercialWorksheet.improvements.rows.then(($rows) => {
                            const rowCount = $rows.length;
                            CommercialWorksheet.improvements.newRowButton.click();
                            CommercialWorksheet.improvements.rows.should('have.length', rowCount + 1);
                            CommercialWorksheet.improvements.rows.last().find('[data-cy="cw-remove-improvement-row"]').click();
                            CommercialWorksheet.improvements.rows.should('have.length', rowCount);
                        });
                    });
                });
                it('CV Income calculates correctly', () => {
                    const carparks = 1;
                    const rent = 2;
                    const vacant = 3;
                    const excessLand = 4;
                    const capRate = 5;
                    CommercialWorksheet.improvements.capRate.clear().type(capRate);
                    CommercialWorksheet.improvements.newRowCarparks.type(carparks);
                    CommercialWorksheet.improvements.newRowRent.type(rent);
                    CommercialWorksheet.improvements.newRowVacant.type(vacant);
                    CommercialWorksheet.improvements.newRowExcessLand.type(excessLand);
                    CommercialWorksheet.calculateCvIncome({ rent, vacant, carparks, excessLand, capRate }).then((cvIncome) => {
                        CommercialWorksheet.getInputValue('improvements', 'newRowCvIncome').should('equal', cvIncome);
                    });
                });
                it('VI Summation calculates correctly', () => {
                    const area = 1;
                    const multiple = 2;
                    const life = 3;
                    const yearBuilt = new Date().getFullYear();
                    const obsolete = 4;
                    const lumpSum = 5;
                    const depreciation = 0;
                    CommercialWorksheet.improvements.newRowCarparks.clear();
                    CommercialWorksheet.improvements.newRowArea.type(area);
                    CommercialWorksheet.improvements.newRowMultiple.type(multiple);
                    CommercialWorksheet.improvements.newRowLife.type(life);
                    CommercialWorksheet.improvements.newRowYearBuilt.type(yearBuilt);
                    CommercialWorksheet.improvements.newRowObsolete.type(obsolete);
                    CommercialWorksheet.improvements.newRowLumpSum.type(lumpSum);
                    CommercialWorksheet.calculateViSummation({ area, multiple, life, depreciation, yearBuilt, obsolete, lumpSum }).then((viSummation) => {
                        CommercialWorksheet.getInputValue('improvements', 'newRowViSummation').should('equal', viSummation);
                    });
                });

                it('Total Outgoings calculates correctly', () => {
                    const outgoingsPerMtSq = 5;
                    CommercialWorksheet.improvements.outgoingsPerMtSq.clear().type(outgoingsPerMtSq);
                    CommercialWorksheet.improvements.nla.invoke('val').as('nla');
                    cy.get('@nla').then(nla => {
                        const expectedTotalOutgoings = Math.round(CommercialWorksheet.moneyStringToNumber(nla) *  outgoingsPerMtSq);
                        CommercialWorksheet.getInputValue('improvements', 'totalOutGoings').should('equal', expectedTotalOutgoings);
                    });
                });

                it('Total Gross Rent calculates correctly', () => {
                    const outgoingsPerMtSq = 5;
                    CommercialWorksheet.improvements.outgoingsPerMtSq.clear().type(outgoingsPerMtSq);
                    CommercialWorksheet.improvements.totalNetRent.invoke('val').as('totalNetRent');
                    CommercialWorksheet.improvements.totalOutGoings.invoke('val').as('totalOutGoings');
                    cy.get('@totalNetRent').then(totalNetRent => {
                        cy.get('@totalOutGoings').then(totalOutGoings => {
                            const expectedGrossRent = CommercialWorksheet.moneyStringToNumber(totalNetRent) + CommercialWorksheet.moneyStringToNumber(totalOutGoings);
                            CommercialWorksheet.getInputValue('improvements', 'totalGrossRent').should('equal', expectedGrossRent);
                        });
                    });
                });
            });

            context('Adopted Values Section', () => {
                context('For a property with current improvement value > 0', () => {
                    it('Adopts "TA minumum improvement value" when worksheet improvement value is less than equal to both "TA minumum improvement value" and "current improvement value"', () => {
                        CommercialWorksheet.totals.cvIncomeRadio.click();
                        CommercialWorksheet.totals.cvIncomeRadio.should('be.checked');
                        CommercialWorksheet.land.rows.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-remove-land-row"]').click();
                        });
                        CommercialWorksheet.getInputValue('totals', 'cvIncomeCv').then((cvIncomeCv) => {
                            const areaToAdd = cvIncomeCv + 100_000;
                            const rateToAdd = 1;
                            CommercialWorksheet.land.newRow.find('[data-cy="cw-new-land-row-area"]').type(`${areaToAdd}`);
                            CommercialWorksheet.land.newRow.find('[data-cy="cw-new-land-row-rate"]').type(`${rateToAdd}`);
                            CommercialWorksheet.getInputValue('land', 'totalValue').should('equal', areaToAdd * rateToAdd);
                            CommercialWorksheet.getInputValue('totals', 'cvIncomeVi').should('be.lt', 0);
                            CommercialWorksheet.getInputValue('adoptedValues', 'vi').should('be.gte', 0);
                        });
                    });
                });

                context('For a property with improvement value = 0', () => {
                    it('Adopts "current improvement value" when worksheet improvement value is less than equal to both "TA minumum improvement value" and "current improvement value"', () => {
                        CommercialWorksheet.visit(QPID_WITHOUT_COMMERCIAL_WORKSHEET);
                        CommercialWorksheet.isLoaded();
                        CommercialWorksheet.totals.cvIncomeRadio.click();
                        CommercialWorksheet.totals.cvIncomeRadio.should('be.checked');
                        CommercialWorksheet.land.newRowButton.click();
                        CommercialWorksheet.land.rows.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-remove-land-row"]').click();
                        });
                        CommercialWorksheet.getInputValue('totals', 'cvIncomeCv').then((cvIncomeCv) => {
                            const areaToAdd = cvIncomeCv + 100_000;
                            const rateToAdd = 1;
                            CommercialWorksheet.land.newRow.find('[data-cy="cw-new-land-row-area"]').type(`${areaToAdd}`);
                            CommercialWorksheet.land.newRow.find('[data-cy="cw-new-land-row-rate"]').type(`${rateToAdd}`);
                            CommercialWorksheet.getInputValue('land', 'totalValue').should('equal', areaToAdd * rateToAdd);
                            CommercialWorksheet.getInputValue('totals', 'cvIncomeVi').should('be.lt', 0);
                            CommercialWorksheet.getInputValue('adoptedValues', 'vi').should('equal', 0);
                        });
                    });
                });
            });
        });
    }
);
import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe('Update Revision Worksheet', { testIsolation: false, defaultCommandTimeout: 30000 }, () => {
    before(() => {
        cy.login();
    });
    // const qpid = 2996267;
    /*
    select top 1000 * from commercial_worksheet cw
    join assessment_child ac on ac.qupid = cw.qupid
    left join commercial_worksheet_apportionment cwa on cwa.qupid = ac.qupid
    join commercial_worksheet_row cwr on cwr.qupid = ac.qupid
    join commercial_worksheet_row_adjustment cwra on cwra.commercial_worksheet_row_id = cwr.commercial_worksheet_row_id
    join Rating_Authority ra on ra.Rating_Authority_Id = ac.rating_authority_id
    join actual_rental cwre on cwre.qpid = cw.qupid and cwre.created > '2012'

    where
    ac.qupid between 100000 and 2500000
    and cw.commercial_valuation_method_id = 2
    --and ra.Organisation_Short_Name like '%nelson%'
    and cwra.record_type = 'R'
    and assessment_status in ('a', 's')
    and cwa.qupid is null
    order by ac.qupid desc
    */
    const qpid = 2349658;

    context('Revision worksheet Property Details Section', () => {
        before(() => {
            CommercialWorksheet.visitRevision(qpid);
            CommercialWorksheet.isLoaded();
        });

        it('should update the revision worksheet fields on Property Details Section when [Update] button is clicked', () => {
            const capRate = 2;
            CommercialWorksheet.propertyDetail.capRate.clear().type(capRate);
            CommercialWorksheet.propertyDetail.viSummationInput.click();
            CommercialWorksheet.interceptAndUpdateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                expect(int.response.statusCode).to.equal(200);
                expect(response.capRate).to.equal(capRate);
            });
        });
    });

    context('Revision worksheet Land Section', () => {
        before(() => {
            CommercialWorksheet.visitRevision(qpid);
            CommercialWorksheet.isLoaded();
        });
        it('should update the revision worksheet Land Section when [Update] button is clicked', () => {
            const ratePerMetre = 100;
            CommercialWorksheet.propertyDetail.viSummationInput.click();
            CommercialWorksheet.land.rows.first().find('[data-cy="cw-land-row-rate-per-metre"]').clear().type(ratePerMetre);
            CommercialWorksheet.interceptAndUpdateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                const landResponse = response.landRows[0];
                expect(int.response.statusCode).to.equal(200);
                expect(landResponse.ratePerMetre).to.equal(ratePerMetre);
            });
        });
    });

    context('Revision worksheet Improvement section', () => {
        before(() => {
            CommercialWorksheet.visitRevision(qpid);
            CommercialWorksheet.isLoaded();
        });

        it('should update the revision worksheet Improvement Section when [Update] button is clicked', () => {
            const rent = 100;
            CommercialWorksheet.propertyDetail.viSummationInput.click();
            CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-rent"]').clear().type(rent);
            CommercialWorksheet.interceptAndUpdateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                const impResponse = response.improvementRows[0];
                expect(int.response.statusCode).to.equal(200);
                expect(impResponse.rental).to.equal(rent);
            });
        });
    });

    context('Revision worksheet Total Values section', () => {
        before(() => {
            CommercialWorksheet.visitRevision(qpid);
            CommercialWorksheet.isLoaded();
        });

        it('should update the revision worksheet Total Worksheet Values Section when [Update] button is clicked', () => {
            const comment = `TEST: COMMENT`;
            CommercialWorksheet.propertyDetail.viSummationInput.click();
            CommercialWorksheet.totals.comment.clear().type(comment);
            CommercialWorksheet.interceptAndUpdateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                expect(int.response.statusCode).to.equal(200);
                expect(response.commercialValuationMethodId).to.equal(2);
                expect(response.comment).to.equal(comment);
            });
        });
    });

    context('Revision worksheet Actual rental section', () => {
        before(() => {
            CommercialWorksheet.visitRevision(qpid);
            CommercialWorksheet.isLoaded();
        });
        it('should update the worksheet Actual Rentals Section when [Update] button is clicked', () => {
            const rentalPerSqmt = 5;
            CommercialWorksheet.propertyDetail.viSummationInput.click();
            CommercialWorksheet.actualRentals.rows.first().find('[data-cy="cw-actual-rental-per-sqmt"]').clear().type(rentalPerSqmt);
            CommercialWorksheet.interceptAndUpdateAssessment();
            CommercialWorksheet.acceptNewValues();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                const actualRentalsResponse = response.actualRentalRows[0];
                expect(int.response.statusCode).to.equal(200);
                expect(actualRentalsResponse.rentalPerSqmt).to.equal(rentalPerSqmt);
            });
        });
    });
});

import PropertyDetails from '../../../model/PropertyDetails.js';
import CommercialWorksheet from '../../../model/CommercialWorksheet.js';

describe(
    'View Commercical Worksheet',
    {
        testIsolation: false,
        defaultCommandTimeout: 30000,
    },
    () => {
        const qpid = 922181;
        const ivsQpid = 2996267;
        const inactiveQpidWithRevision = 1372339;

        context('As an external user', () => {
            before(() => {
                cy.login();
                cy.overrideUserData({
                    roles: [{ name: 'EXTERNAL_USER_READ' }],
                });
            });

            context('Property Details', () => {
                it('Clicking commercial worksheet link opens commercial worksheet page', () => {
                    PropertyDetails.visit(qpid);
                    PropertyDetails.commercialWorksheetLink.should('exist');
                    PropertyDetails.commercialWorksheetLink.click();
                    cy.wait(3000);
                    cy.url().should('include', CommercialWorksheet.getPageRoute(qpid));
                });
                it('Commercial Worksheet page loads successfully with all sections', () => {
                    CommercialWorksheet.isLoaded();
                    cy.wait(3000);
                    CommercialWorksheet.pageTitle.should('include.text', 'Update Commercial Worksheet');
                    CommercialWorksheet.propertyInfo.allRowsExist();
                    CommercialWorksheet.allSectionsExceptRfcExist();
                    CommercialWorksheet.externalUserActionButtonsNotExist();
                });
            });

            context('Commercial Worksheet', () => {
                it('Auto Worksheet should be toggled on for C & I property', () => {
                    CommercialWorksheet.visit(ivsQpid);
                    let isToggled = false;
                    CommercialWorksheet.propertyInfo.autoWorksheet.autoWorksheetSwitch.find('label').then(($element) => {
                        if ($element.hasClass('toggled')){
                            isToggled = true;
                        }
                        else {
                            isToggled = false;
                        }
                        cy.wrap($element).click();
                        // we have click the toggle so the value will be the other way around what it is previously
                        isToggled = !isToggled;
                    });
                    cy.wait(2000);
                    cy.reload();
                    CommercialWorksheet.propertyInfo.autoWorksheet.autoWorksheetSwitch.find('label').then(($element) => {
                        if (isToggled){
                            cy.wrap($element).should('have.class', 'toggled');
                        }
                        else {
                            cy.wrap($element).should('not.have.class', 'toggled');
                        }

                    });
                });
                it('Inactive Current Commercial Worksheet should only be read only', () => {
                    CommercialWorksheet.visit(inactiveQpidWithRevision);
                    CommercialWorksheet.isLoaded();
                    CommercialWorksheet.propertyDetail.isCurrentReadOnly();
                    CommercialWorksheet.land.isCurrentReadOnly();
                    CommercialWorksheet.improvements.isCurrentReadOnly();
                    CommercialWorksheet.rfc.isReadOnly();
                });
                it('Inactive Revision Commercial Worksheet should only be read only', () => {
                    CommercialWorksheet.visit(inactiveQpidWithRevision);
                    CommercialWorksheet.isLoaded();
                    CommercialWorksheet.propertyDetail.isRevisionReadOnly();
                    CommercialWorksheet.land.isRevisionReadOnly();
                    CommercialWorksheet.improvements.isRevisionReadOnly();
                });
                it('Active Current Commercial Worksheet should be read write', () => {
                    CommercialWorksheet.visit(qpid);
                    CommercialWorksheet.isLoaded();
                    CommercialWorksheet.propertyDetail.isCurrentReadWrite();
                    CommercialWorksheet.land.isCurrentReadWrite();
                    CommercialWorksheet.improvements.isCurrentReadWrite();
                    CommercialWorksheet.actualRentals.isReadWrite();
                    CommercialWorksheet.rfc.isReadWrite();
                });
                it('Active Revision Commercial Worksheet should be read write', () => {
                    CommercialWorksheet.visit(qpid);
                    CommercialWorksheet.isLoaded();
                    CommercialWorksheet.propertyDetail.isRevisionReadWrite();
                    CommercialWorksheet.land.isRevisionReadWrite();
                    CommercialWorksheet.improvements.isRevisionReadWrite();
                    CommercialWorksheet.actualRentals.isReadWrite();
                });
            });
        });
    }
);

import PropertyDetails from '../../../model/PropertyDetails.js';
import CommercialWorksheet from '../../../model/CommercialWorksheet.js';

describe(
    'Create Revision Worksheet',
    {
        testIsolation: false,
        defaultCommandTimeout: 30000,
    },
    () => {
        let qpid;

        context('As an external user', () => {
            before(() => {
                CommercialWorksheet.generateCommercialPropertyWithWorksheet().then(property => {
                    qpid = property.qpid;
                    expect(qpid).to.be.a('number').and.to.be.greaterThan(0);
                    cy.wait(15000); // wait for qivs stream to create monarch property
                });

                cy.login();
                cy.overrideUserData({
                    roles: [{ name: 'EXTERNAL_USER_READ' }],
                });
            });

            context('Commercial Worksheet', () => {
                it('Should not be able to create revision worksheet', () => {
                    CommercialWorksheet.visit(qpid);
                    CommercialWorksheet.isLoaded();
                    CommercialWorksheet.createRevisionWorksheetButton.should('not.exist');
                });
            });
        });
    }
);

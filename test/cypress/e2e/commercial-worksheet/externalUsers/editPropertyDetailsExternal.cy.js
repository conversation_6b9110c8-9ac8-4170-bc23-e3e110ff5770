import PropertyDetails from '../../../model/PropertyDetails.js';
import CommercialWorksheet from '../../../model/CommercialWorksheet.js';
import ExtraPropertyDetails from '../../../model/ExtraPropertyDetails.js';
import PropertyToolbar from '../../../model/PropertyToolbar.js';

describe(
    'Edit Property Details',
    {
        testIsolation: false,
        defaultCommandTimeout: 30000,
    },
    () => {
        let qpid;

        context('As an external user', () => {
            before(() => {
                CommercialWorksheet.generateCommercialPropertyWithWorksheet().then((property) => {
                    qpid = property.qpid;
                    expect(qpid).to.be.a('number').and.to.be.greaterThan(0);
                    cy.wait(15000); // wait for qivs stream to create monarch property
                });

                cy.login();
                cy.overrideUserData({
                    roles: [{ name: 'EXTERNAL_USER_READ' }],
                });
            });

            it('Should be able to see Monarch Details page successfully', () => {
                PropertyDetails.visit(qpid);
                PropertyToolbar.oldMonarchExtraDetailsTab.click();
                ExtraPropertyDetails.isLoaded();
            });
            it('Should not be able to see edit button', () => {
                // there is a tab bug where it switch back after you click on different tab
                // have to revisit the page
                CommercialWorksheet.visit(qpid);
                CommercialWorksheet.isLoaded();
                PropertyToolbar.oldMonarchExtraDetailsTab.click();
                ExtraPropertyDetails.isLoaded();
                ExtraPropertyDetails.editPropertyDetails.editPropertyDetailButton.should('not.exist');
            });
        });
    }
);

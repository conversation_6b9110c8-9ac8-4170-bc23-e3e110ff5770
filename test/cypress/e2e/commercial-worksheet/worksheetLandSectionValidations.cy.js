import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe(
    'Commercical Worksheet Validations',
    {
        testIsolation: false,
        defaultCommandTimeout: 15000,
    },
    () => {
        context('As an internal user', () => {
            context('Land Section', () => {
                context('New C & I Worksheet', () => {
                    const qpid = 3107912;
                    before(() => {
                        cy.login();
                        CommercialWorksheet.visit(qpid);
                    });
                    it('Should load the page successfully', () => {
                        // please note that this requires DEV2-2542 SQL scripts to be run first
                        cy.url().should('include', CommercialWorksheet.getPageRoute(qpid));
                        CommercialWorksheet.isLoaded();
                        CommercialWorksheet.allSectionsExist();
                        CommercialWorksheet.allActionButtonsExist();
                    });

                    it ('Should have the pre-selected Undefined Street Location Type', () => {
                        CommercialWorksheet.land.rows.should('exist');
                        CommercialWorksheet.land.rows.first()
                        .find('[data-cy="cw-land-row-street-location-type"]').then(($dropdownDiv) => {
                            // display Undefined pre-selected value
                            // however the dropdown list should not have Undefined option to be selected
                            cy.get($dropdownDiv).find('span[class="multiselect__single"]').then(($dropdownValue) => {
                                expect($dropdownValue.text()).to.equal('Undefined');
                            })
                        })
                    })
                    it ('Should not display Undefined option when street location type dropdown is clicked', () => {
                        CommercialWorksheet.land.rows.should('exist');
                        CommercialWorksheet.land.rows.first()
                        .find('[data-cy="cw-land-row-street-location-type"]').then(($dropdownDiv) => {
                            // display Undefined pre-selected value
                            // however the dropdown list should not have Undefined option to be selected
                            cy.wrap($dropdownDiv).focus().invoke('click');
                            cy.get($dropdownDiv).find('ul[id="listbox-null"]').then(($dropdownList) => {
                                cy.wrap($dropdownList).should('not.contain', 'Undefined');
                            })
                        })
                    })
                    it ('Revision should have the pre-selected Undefined Street Location Type', () => {
                        CommercialWorksheet.createRevisionWorksheetButton.should('exist');
                        CommercialWorksheet.createRevisionWorksheetButton.click();
                        CommercialWorksheet.land.rows.should('exist');
                        CommercialWorksheet.land.rows.first()
                        .find('[data-cy="cw-land-row-street-location-type"]').then(($dropdownDiv) => {
                            // display Undefined pre-selected value
                            // however the dropdown list should not have Undefined option to be selected
                            cy.get($dropdownDiv).find('span[class="multiselect__single"]').then(($dropdownValue) => {
                                expect($dropdownValue.text()).to.equal('Undefined');
                            })
                        })
                    })
                })
            });
        });
    }
);

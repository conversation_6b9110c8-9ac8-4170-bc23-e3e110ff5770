import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe('Update Worksheet Only', { testIsolation: false, defaultCommandTimeout: 15000 }, () => {
    // relying on apportionments
    // relying on imp rows being filled out
    // const qpid = 3420095;
    const qpid = 2493723;

    context('As an internal user', () => {
        before(() => {
            cy.login();
            CommercialWorksheet.visit(qpid);
        });

        it('should successfully load the Commercial worksheet page with all sections and action buttons', () => {
            cy.url().should('include', CommercialWorksheet.getPageRoute(qpid));
            CommercialWorksheet.isLoaded();
            CommercialWorksheet.allSectionsExist();
            CommercialWorksheet.allActionButtonsExist();
        });

        it('should update the Worksheet Fields on Property Details Section when [Update Assessment] button is clicked', () => {
            const capRate = 8.5;
            CommercialWorksheet.updatePropertyDetails(capRate);
            CommercialWorksheet.interceptAndUpdateWorksheet();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                expect(int.response.statusCode).to.equal(200);
                expect(response.capRate).to.equal(capRate);
            });
            CommercialWorksheet.successModalAppears();
        });

        it('should update the worksheet Land Section when [Update Assessment] button is clicked', () => {
            CommercialWorksheet.land.rows.first().get('[data-cy="cw-land-row-description"]').clear().type('TEST: Land Description');
            CommercialWorksheet.land.rows.first().get('[data-cy="cw-land-row-area"]').clear().type(971.13);
            CommercialWorksheet.land.rows.first().get('[data-cy="cw-land-row-rate-per-metre"]').clear().type(762);
            CommercialWorksheet.interceptAndUpdateWorksheet();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                const landResponse = response.landRows[0];
                expect(int.response.statusCode).to.equal(200);
                expect(landResponse.description).to.equal('TEST: Land Description');
                expect(landResponse.area).to.equal(971.13);
                expect(landResponse.ratePerMetre).to.equal(762);
            });
            CommercialWorksheet.successModalAppears();
        });

        it('should update the worksheet Improvement Section when [Update Assessment] button is clicked', () => {
            CommercialWorksheet.improvements.rows.first();
            CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-area"]').clear().type(159.81);
            CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-description"]').clear().type('TEST: Improvement Description');
            CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-multiple"]').clear().type(0);
            CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-percent-vacant"]').clear().type(0);
            CommercialWorksheet.improvements.rows.first().find('[data-cy="cw-imp-row-year-built"]').clear().type(1983);

            CommercialWorksheet.interceptAndUpdateWorksheet();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                const impResponse = response.improvementRows[0];
                expect(int.response.statusCode).to.equal(200);
                expect(impResponse.area).to.equal(159.81);
                expect(impResponse.description).to.equal('TEST: Improvement Description');
                expect(impResponse.multiple).to.equal(0);
                expect(impResponse.percentVacant).to.equal(0);
                expect(impResponse.yearBuilt).to.equal(1983);
            });
            CommercialWorksheet.successModalAppears();
        });

        it('should update the worksheet Total Worksheet Values Section when [Update Assessment] button is clicked', () => {
            CommercialWorksheet.totals.cvIncomeRadio.click();
            CommercialWorksheet.totals.comment.clear().type(`TEST: COMMENT`);
            CommercialWorksheet.interceptAndUpdateWorksheet();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                expect(int.response.statusCode).to.equal(200);
                expect(response.commercialValuationMethodId).to.equal(2);
                expect(response.comment).to.equal(`TEST: COMMENT`);
            });
            CommercialWorksheet.successModalAppears();
        });

        it('should update the worksheet Actual Rentals Section when [Update Assessment] button is clicked', () => {
            const rental = {
                floorArea: 52.00,
                grossRental: 4576,
                rentalDate: '2021-01-01',
                rentalPerSqmt: 88,
                tenant: 'TEST TENANT',
            };
            CommercialWorksheet.addNewActualRentalRow(rental);
            CommercialWorksheet.interceptAndUpdateWorksheet();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                const rentalRes = response.actualRentalRows[response.actualRentalRows.length - 1];
                expect(int.response.statusCode).to.equal(200);
                expect(rentalRes.floorArea).to.equal(rental.floorArea);
                expect(rentalRes.grossRental).to.equal(rental.grossRental);
                expect(rentalRes.rentalDate.toString().split('T')[0]).to.equal(rental.rentalDate);
                expect(rentalRes.rentalPerSqmt).to.equal(rental.rentalPerSqmt);
                expect(rentalRes.tenant).to.equal(rental.tenant);
            });
            CommercialWorksheet.successModalAppears();
        });

        it('should update the worksheet Adopted Worksheet Values Section when [Update Assessment] button is clicked', () => {
            CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-cvOverride"]').clear().type(529000);
            CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-lvOverride"]').clear().type(475000);
            CommercialWorksheet.interceptAndUpdateWorksheet();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                const adoptedRes = response.adoptedValues[0];
                expect(int.response.statusCode).to.equal(200);
                expect(adoptedRes.cvOverride).to.equal(529000);
                expect(adoptedRes.lvOverride).to.equal(475000);
            });
            CommercialWorksheet.successModalAppears();
        });

        it('should update the worksheet Adopted Worksheet Values Section when [Update Assessment] button is clicked', () => {
            //CommercialWorksheet.visit(qpid);
            CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-cvOverride"]').clear().type(529000);
            CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-lvOverride"]').clear().type(475000);
            CommercialWorksheet.interceptAndUpdateWorksheet();
            CommercialWorksheet.dismissValidationWarnings();
            cy.wait('@updateWorksheet').then((int) => {
                const response = int.response.body;
                const adoptedRes = response.adoptedValues[0];
                expect(int.response.statusCode).to.equal(200);
                expect(adoptedRes.cvOverride).to.equal(529000);
                expect(adoptedRes.lvOverride).to.equal(475000);
            });
            CommercialWorksheet.successModalAppears();
        });

    });
});

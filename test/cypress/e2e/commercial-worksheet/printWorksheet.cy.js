import { createCommercialPropertyWithWorksheetBody, getGenerateRequest } from '../../support/testFactoryCommercialWorksheet.js';
import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe(
    'Print Worksheet',
    {
        testIsolation: false,
        defaultCommandTimeout: 15000,
    },
    () => {
        let commercialProperty;
        let qpid;

        context('As an internal user', () => {
            before(() => {
                CommercialWorksheet.generateCommercialPropertyWithWorksheet().then(property => {
                    commercialProperty = property;
                    qpid = commercialProperty.qpid;
                    expect(qpid).to.be.a('number').and.to.be.greaterThan(0);
                    cy.wait(15000); // wait for qivs stream to create monarch property
                })
            });

            it('Logs in successfully', () => {
                cy.login();
            });


            it('Commercial worksheet page loads successfully', () => {
                CommercialWorksheet.visit(qpid);
                cy.url().should('include', CommercialWorksheet.getPageRoute(qpid));
                CommercialWorksheet.isLoaded();
                CommercialWorksheet.allSectionsExist();
            });

            it('Commercial Worksheet should have Print button', () => {
                CommercialWorksheet.printWorksheetButtonTop.should('exist');
            });

            it('Clicking Print Worksheet should open a PDF in new tab', () => {
                cy.intercept(`/api/commercialWorksheet/generateCommercialWorksheetPdf?qpid=${qpid}&type=current`).as('printWorksheet');
                CommercialWorksheet.printWorksheetButtonTop.click();
                cy.wait('@printWorksheet').then((int) => {
                    expect(int.response.statusCode).to.equal(200);
                });
            });
        });
    }
);

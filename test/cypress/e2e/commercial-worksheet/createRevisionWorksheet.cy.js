import testFactoryCommercialWorksheet from '../../support/testFactoryCommercialWorksheet.js';
import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe(
    'Create Revision Worksheet',
    {
        testIsolation: false,
        defaultCommandTimeout: 30000,
    },
    () => {
        let commercialProperty = null;
        let qpid = null;

        context('As an internal user', () => {
            before(() => {
                CommercialWorksheet.generateCommercialPropertyWithWorksheet().then(property => {
                    commercialProperty = property;
                    qpid = commercialProperty.qpid;
                    expect(qpid).to.be.a('number').and.to.be.greaterThan(0);
                    cy.wait(15000); // wait for qivs stream to create monarch property
                })
            });

            it('Logs in successfully', () => {
                cy.login();
            });

            context('Commercial Worksheet', () => {
                it('Should be able to create revision worksheet when clicking the button', () => {
                    CommercialWorksheet.visit(qpid);
                    CommercialWorksheet.isLoaded();
                    CommercialWorksheet.createRevisionWorksheetButton.should('exist');
                    CommercialWorksheet.createRevisionWorksheetButton.click();
                    cy.get('[data-cy="cw-button-dialog-cancel"]').should('not.exist');
                    CommercialWorksheet.successModalType2Appears();
                    CommercialWorksheet.pageTitle.should('include.text', 'Update Commercial Worksheet');
                    CommercialWorksheet.pageTitle.should('include.text', 'Revision Values');
                    CommercialWorksheet.propertyInfo.allRowsExist();
                    CommercialWorksheet.allSectionsExceptRfcExist();
                    CommercialWorksheet.cancelChangesButton.should('exist');
                    CommercialWorksheet.updateAssessmentButton.should('exist');
                });
            });
        });
    }
);

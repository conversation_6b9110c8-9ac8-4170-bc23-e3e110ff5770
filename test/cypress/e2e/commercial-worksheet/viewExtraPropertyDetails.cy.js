import testFactoryCommercialWorksheet from '../../support/testFactoryCommercialWorksheet.js';
import PropertyDetails from '../../model/PropertyDetails.js';
import ExtraPropertyDetails from '../../model/ExtraPropertyDetails.js';
import PropertyToolbar from '../../model/PropertyToolbar.js';
import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe(
    'View Property Details',
    {
        testIsolation: false,
        defaultCommandTimeout: 30000,
    },
    () => {
        let commercialProperty = null;
        let commercialQpid = null;
        let nonCommercialProperty = null;
        let nonCommercialQpid = null;

        context('As an internal user', () => {
            before(() => {
                CommercialWorksheet.generateCommercialPropertyWithWorksheet().then(property => {
                    commercialProperty = property;
                    commercialQpid = commercialProperty.qpid;
                    expect(commercialQpid).to.be.a('number').and.to.be.greaterThan(0);
                })
                CommercialWorksheet.generateResidentialProperty().then(property => {
                    nonCommercialProperty = property;
                    nonCommercialQpid = nonCommercialProperty.qpid;
                    expect(nonCommercialQpid).to.be.a('number').and.to.be.greaterThan(0);
                })
                cy.wait(15000); // note: need to force a timeout to allow the property to propagate over to Monarch DB
            });

            it('Logs in successfully', () => {
                cy.login();
            });

            context('Property Details', () => {
                context('Commercial Property', () => {
                    it('navigates to the commercial property', () => {
                        PropertyDetails.visit(commercialQpid);
                    });

                    it('Should be able to see Monarch Details page successfully', () => {
                        cy.wait(10000) // wait for eventbus
                        PropertyToolbar.oldMonarchExtraDetailsTab.click();
                        ExtraPropertyDetails.isLoaded();
                    });
                    it('New fields exist in the Monarch Details tab', () => {
                        cy.get('[data-cy="edit-property-detail-button"]').click();
                        ExtraPropertyDetails.propertyInfo.qvCategory.should('exist');
                        ExtraPropertyDetails.propertyInfo.grouping.should('exist');
                        ExtraPropertyDetails.propertyInfo.proposedZone.should('exist');
                        ExtraPropertyDetails.propertyInfo.actualEarthquakeRating.should('exist');
                        ExtraPropertyDetails.propertyInfo.earthquakeRatingRange.should('exist');
                        ExtraPropertyDetails.propertyInfo.earthquakeRatingAssessor.should('exist');
                        ExtraPropertyDetails.propertyInfo.remedyDeadline.should('exist');
                        ExtraPropertyDetails.propertyInfo.liquefaction.should('exist');
                    });
                    it('Should not be able to see construction information, spaces, and site improvements sections', () => {
                        ExtraPropertyDetails.constructionInfo.title.should('not.exist');
                        ExtraPropertyDetails.spaces.title.should('not.exist');
                        ExtraPropertyDetails.siteImprovements.title.should('not.exist');
                    });
                });

                context('Non Commercial Property', () => {
                    it('navigates to the non-commercial property', () => {
                        PropertyDetails.visit(nonCommercialQpid);
                    });

                    it('Should be able to see Monarch Details page successfully', () => {
                        cy.wait(10000); // wait for eventbus
                        PropertyToolbar.oldMonarchExtraDetailsTab.click();
                        ExtraPropertyDetails.isLoaded();
                    });

                    it('Should be able to see construction information, spaces, and site imporvements sections', () => {
                        ExtraPropertyDetails.constructionInfo.title.should('exist');
                        ExtraPropertyDetails.spaces.title.should('exist');
                        ExtraPropertyDetails.siteImprovements.title.should('exist');
                    });

                    it('New fields do not exist in the Monarch Details tab', () => {
                        cy.get('[data-cy="edit-property-detail-button"]').click();
                        ExtraPropertyDetails.propertyInfo.qvCategory.should('not.exist');
                        ExtraPropertyDetails.propertyInfo.grouping.should('not.exist');
                        ExtraPropertyDetails.propertyInfo.proposedZone.should('not.exist');
                        ExtraPropertyDetails.propertyInfo.actualEarthquakeRating.should('not.exist');
                        ExtraPropertyDetails.propertyInfo.earthquakeRatingRange.should('not.exist');
                        ExtraPropertyDetails.propertyInfo.earthquakeRatingAssessor.should('not.exist');
                        ExtraPropertyDetails.propertyInfo.remedyDeadline.should('not.exist');
                        ExtraPropertyDetails.propertyInfo.liquefaction.should('not.exist');
                    });

                })
            });

            context('Remedy Deadline Field', () => {
                it('should successfully load the risk and hazard section', () => {
                    CommercialWorksheet.visit(commercialQpid);
                    CommercialWorksheet.isLoaded();
                    PropertyToolbar.oldMonarchExtraDetailsTab.click();
                    ExtraPropertyDetails.isLoaded();
                    ExtraPropertyDetails.riskAndHazards.tab.click();
                });

                context('When clearing out Remedy Year field', () => {
                    it('Should display the placeholder YYYY', () => {
                        ExtraPropertyDetails.riskAndHazards.remedyDeadline.clear();
                        ExtraPropertyDetails.riskAndHazards.remedyDeadline.invoke('attr', 'placeholder').then(placeholder => {
                            expect(placeholder).to.equal('YYYY');
                        });
                    });
                    it('Should be able to save blank year', () => {
                        ExtraPropertyDetails.interceptAndSave();
                        cy.wait('@saveQvProperty').then(interception => {
                            expect(interception.response.statusCode).to.eq(200);
                        });
                    });
                });

                context('When typing invalid remedy year', () => {
                    const invalidRemedyYear = '22';
                    it('Should display an error', () => {
                        ExtraPropertyDetails.riskAndHazards.remedyDeadline.clear();
                        ExtraPropertyDetails.riskAndHazards.remedyDeadline.type(invalidRemedyYear);
                        ExtraPropertyDetails.riskAndHazards.remedyDeadline.blur();
                        ExtraPropertyDetails.riskAndHazards.propertyPlusSection.should('contain.text', 'Invalid Number length.');
                    });
                    it('Should clear out the field', () => {
                        ExtraPropertyDetails.riskAndHazards.remedyDeadline.invoke('val').then(value => {
                            expect(value).to.equal('');
                        });
                    });
                });

                context('When updating the Remedy Year field', () => {
                    const remedyYear = '2222';
                    it('Should update the remedy year successfully', () => {
                        ExtraPropertyDetails.riskAndHazards.remedyDeadline.clear().type(remedyYear);
                        ExtraPropertyDetails.interceptAndSave();
                        cy.wait('@saveQvProperty').then(interception => {
                            expect(interception.response.statusCode).to.eq(200);
                        });
                    });
                    it('Should reflect the updated remedy year in commercial worksheet', () => {
                        CommercialWorksheet.visit(commercialQpid);
                        CommercialWorksheet.isLoaded();
                        CommercialWorksheet.propertyDetail.remedyYear.invoke('val').then(year => {
                            expect(year).to.equal(remedyYear);
                        })
                    });
                });
            });
        });
    }
);

import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe('View Revision Worksheet', { testIsolation: false, defaultCommandTimeout: 30000 }, () => {
    // const qpid = 2996267;
    const qpid = 2391345;

    context('As an internal user', () => {
        before(() => {
            cy.login();
            CommercialWorksheet.visit(qpid);
        });

        it('should be able to go to revision worksheet section', () => {
            CommercialWorksheet.revisionWorksheetTab.should('exist');
            CommercialWorksheet.revisionWorksheetTab.click();
        })

        it('should successfully load the Commercial worksheet page with all sections and action buttons', () => {
            cy.url().should('include', CommercialWorksheet.getRevisionPageRoute(qpid));
            CommercialWorksheet.isLoaded();
            CommercialWorksheet.allSectionsExceptRfcExist();
            CommercialWorksheet.cancelChangesButton.should('exist');
            CommercialWorksheet.updateAssessmentButton.should('exist');
        });

        it('should not have add or remove button on the page', () => {
            CommercialWorksheet.improvements.newRowButton.should('not.exist');
            CommercialWorksheet.land.newRowButton.should('not.exist');
            CommercialWorksheet.actualRentals.newRowButton.should('not.exist');
        });

        it('should disable read only fields', () => {
            cy.get('[data-cy="cw-land-row-street-location-type"]').each(($el) => {
                cy.wrap($el).should('have.class', 'multiselect--disabled');
            });
            cy.get('[data-cy="cw-imp-row-imp-type"]').each(($el) => {
                cy.wrap($el).should('have.class', 'multiselect--disabled');
            });
        });
    });
});

import testFactoryCommercialWorksheet from '../../support/testFactoryCommercialWorksheet.js';
import CommercialWorksheet from '../../model/CommercialWorksheet.js';

describe(
    'Delete Worksheet',
    {
        testIsolation: false,
        defaultCommandTimeout: 15000,
    },
    () => {
        let commercialProperty = null;
        let qpid = 2499999;

        context('As an internal user', () => {
            before(() => {
                CommercialWorksheet.generateCommercialPropertyWithWorksheet().then(property => {
                    commercialProperty = property;
                    qpid = commercialProperty.qpid;
                    expect(qpid).to.be.a('number').and.to.be.greaterThan(0);
                    cy.wait(15000); // wait for qivs stream to create monarch property
                })
            });

            it('Logs in successfully', () => {
                cy.login();
            });

            it('Commercial worksheet page loads successfully', () => {
                CommercialWorksheet.visit(qpid);
                cy.url().should('include', CommercialWorksheet.getPageRoute(qpid));
                CommercialWorksheet.isLoaded();
                CommercialWorksheet.allSectionsExist();
            });

            it('Commercial Worksheet should have delete button', () => {
                CommercialWorksheet.deleteButton.should('exist');
            });

            it('Clicking Delete Worksheet should delete the worksheet', () => {
                cy.intercept(`/api/commercialWorksheet/deleteCommercialWorksheet?qpid=${qpid}`).as('deleteWorksheet');
                CommercialWorksheet.deleteWorksheet();
                cy.wait('@deleteWorksheet').then((int) => {
                    const body = int.response.body;
                    expect(body.status).to.equal('SUCCESS');
                });
            });
        });
    }
);

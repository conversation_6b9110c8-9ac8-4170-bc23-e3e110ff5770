import CommercialWorksheet from '../../model/CommercialWorksheet.js';

//
describe(
    'Commercical Worksheet Calculations',
    {
        testIsolation: false,
        defaultCommandTimeout: 15000,
    },
    () => {
        // assuming property has apportionments (apportionmentId 5)
        // assuming at least 1 imp row are filled out for each apportionment/suffix
        const qpid = 2992694;

        context('As an internal user', () => {
            before(() => {
                cy.login();
                CommercialWorksheet.visit(qpid);
            });

            context('With no CV overrides filled out', () => {
                it('Total apportionment CV should equal adopted CV', () => {
                    CommercialWorksheet.adoptedValues.row.each(($row) => {
                        cy.wrap($row).find('[data-cy="cw-adopted-cvOverride"]').clear();
                    });
                    CommercialWorksheet.apportionmentCvEqualsAdoptedCv();
                });
            });

            context('With no LV overrides filled out', () => {
                it('Total apportionment LV should equal adopted LV', () => {
                    CommercialWorksheet.adoptedValues.row.each(($row) => {
                        cy.wrap($row).find('[data-cy="cw-adopted-lvOverride"]').clear();
                    });
                    CommercialWorksheet.apportionmentLvEqualsAdoptedLv();
                });
            });

            // assuming more than one apportionment
            context('When some but not all CV overrides are filled in', () => {
                it('Apportionment CV should equal CV override', () => {
                    const cvOverride = 100;
                    CommercialWorksheet.adoptedValues.row.each(($row) => {
                        cy.wrap($row).find('[data-cy="cw-adopted-cvOverride"]').clear();
                    });
                    CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-cvOverride"]').type(cvOverride);
                    CommercialWorksheet.adoptedValues.row
                        .first()
                        .find('[data-cy="cw-apportionment-cv"]')
                        .invoke('val')
                        .then((value) => {
                            expect(CommercialWorksheet.moneyStringToNumber(value)).to.equal(cvOverride);
                        });
                });
                it('Total apportionment CV should equal adopted CV', () => {
                    CommercialWorksheet.apportionmentCvEqualsAdoptedCv();
                });
            });

            context('When some but not all LV overrides are filled in', () => {
                it('Apportionment LV should equal LV override', () => {
                    const lvOverride = 100;
                    CommercialWorksheet.adoptedValues.row.each(($row) => {
                        cy.wrap($row).find('[data-cy="cw-adopted-lvOverride"]').clear();
                    });
                    CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-lvOverride"]').type(lvOverride);
                    CommercialWorksheet.adoptedValues.row
                        .first()
                        .find('[data-cy="cw-apportionment-lv"]')
                        .invoke('val')
                        .then((value) => {
                            expect(CommercialWorksheet.moneyStringToNumber(value)).to.equal(lvOverride);
                        });
                });
                it('Total apportionment LV should equal adopted LV', () => {
                    CommercialWorksheet.apportionmentLvEqualsAdoptedLv();
                });
            });

            context('When all CV overrides are filled in', () => {
                context('When CV override total is not equal to adopted CV', () => {
                    it('Total apportionment CV should equal CV override total', () => {
                        const cvOverride = 100;
                        CommercialWorksheet.adoptedValues.row.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-adopted-cvOverride"]').clear();
                        });
                        CommercialWorksheet.adoptedValues.row.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-adopted-cvOverride"]').type(cvOverride);
                        });
                        CommercialWorksheet.getInputValue('adoptedValues', 'apportionmentCv').then((apportionmentCv) => {
                            CommercialWorksheet.getInputValue('adoptedValues', 'cvOverrideTotal').then((cvOverrideTotal) => {
                                expect(cvOverrideTotal).to.equal(apportionmentCv);
                            });
                        });
                    });
                    it('CV override total should show a validation warning', () => {
                        CommercialWorksheet.adoptedValues.cvOverrideTotal.parent().find('[data-cy="input-error-message"]').should('exist');
                    });
                });
                context('When CV override total is equal to adopted CV', () => {
                    it('CV override total will not show a validation warning', () => {
                        const cvOverride = 0;
                        CommercialWorksheet.adoptedValues.row.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-adopted-cvOverride"]').clear();
                        });
                        CommercialWorksheet.adoptedValues.row.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-adopted-cvOverride"]').type(cvOverride);
                        });
                        CommercialWorksheet.getInputValue('adoptedValues', 'cv').then((adoptedCv) => {
                            CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-cvOverride"]').clear().type(adoptedCv);
                            CommercialWorksheet.getInputValue('adoptedValues', 'apportionmentCv').then((apportionmentCv) => {
                                CommercialWorksheet.getInputValue('adoptedValues', 'cvOverrideTotal').then((cvOverrideTotal) => {
                                    expect(cvOverrideTotal).to.equal(apportionmentCv);
                                });
                            });
                        });
                        CommercialWorksheet.adoptedValues.cvOverrideTotal.parent().find('[data-cy="input-error-message"]').should('not.exist');
                    });
                });
            });

            context('When all LV overrides are filled in', () => {
                context('When LV override total is not equal to adopted LV', () => {
                    it('Total apportionment LV should equal LV override total', () => {
                        const lvOverride = 100;
                        CommercialWorksheet.adoptedValues.row.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-adopted-lvOverride"]').clear();
                        });
                        CommercialWorksheet.adoptedValues.row.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-adopted-lvOverride"]').type(lvOverride);
                        });
                        CommercialWorksheet.getInputValue('adoptedValues', 'apportionmentLv').then((apportionmentLv) => {
                            CommercialWorksheet.getInputValue('adoptedValues', 'lvOverrideTotal').then((lvOverrideTotal) => {
                                expect(lvOverrideTotal).to.equal(apportionmentLv);
                            });
                        });
                    });
                    it('LV override total should show a validation warning', () => {
                        CommercialWorksheet.adoptedValues.lvOverrideTotal.parent().find('[data-cy="input-error-message"]').should('exist');
                    });
                });
                context('When LV override total is equal to adopted LV', () => {
                    it('LV override total will not show a validation warning', () => {
                        const lvOverride = 0;
                        CommercialWorksheet.adoptedValues.row.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-adopted-lvOverride"]').clear();
                        });
                        CommercialWorksheet.adoptedValues.row.each(($row) => {
                            cy.wrap($row).find('[data-cy="cw-adopted-lvOverride"]').type(lvOverride);
                        });
                        CommercialWorksheet.getInputValue('adoptedValues', 'lv').then((adoptedLv) => {
                            CommercialWorksheet.adoptedValues.row.first().find('[data-cy="cw-adopted-lvOverride"]').clear().type(adoptedLv);
                            CommercialWorksheet.getInputValue('adoptedValues', 'apportionmentLv').then((apportionmentLv) => {
                                CommercialWorksheet.getInputValue('adoptedValues', 'lvOverrideTotal').then((lvOverrideTotal) => {
                                    expect(lvOverrideTotal).to.equal(apportionmentLv);
                                });
                            });
                        });
                        CommercialWorksheet.adoptedValues.lvOverrideTotal.parent().find('[data-cy="input-error-message"]').should('not.exist');
                    });
                });
            });
        });
    }
);

import QVMaps from '../../model/QVMaps';

const visitQpid = 9001;

describe('QVMaps search multiple qpids - DEV-3680', { defaultCommandTimeout: 15000 }, () => {
    beforeEach(() => {
        cy.visitWithLogin(QVMaps.href(visitQpid));
    });
    describe('Multiple qpid search', () => {
        it('searches for multiple properties, handles leading and trailing spaces', () => {
            const validStringEntry = '   9002  ,   9003    ';
            QVMaps.elements.addQpids.type(validStringEntry);
            QVMaps.elements.searchButton
                .click()
                .then(() => {
                    expect(QVMaps.elements.propertyInfoWrapper).to.exist;
                    expect(QVMaps.elements.propertyInfoHeader).to.exist;
                    QVMaps.elements.propertyInfoWrapper.should('have.length.greaterThan', 1);
                });
        });
    });
    describe('Qpid - Search field expand as entered', () => {
        it('The Search QPID input field should scale based on the number entered ', () => {
            const longInputString = '4444444444444444444444444444444444444444444444444444444444444444444';
            QVMaps.elements.addQpids
                .type(longInputString)
                .then(() => {
                    QVMaps.elements.addQpids.should('have.css', 'height', '65px');
                });
        });
    });
    describe('Multiple Qpid with comma separated', () => {
        const tooltipString = 'Add a QPID or multiple comma separated QPIDS to Panel';
        it(`The “Tickbox” Tooltip should say "${tooltipString}"`, () => {
            QVMaps.elements.searchButton.then((elem) => {
                expect(elem).to.have.attr('title', tooltipString);
            });
        });
    });
    describe('Invalid Entry pop up', () => {
        it('Alert model appears if invalid input is entered', () => {
            const invalidStringEntry = '9001,invalid,string';
            QVMaps.elements.addQpids.type(invalidStringEntry);
            QVMaps.elements.searchButton
                .click()
                .then(() => {
                    cy.wait(2500);
                    QVMaps.elements.alertModal.should.exist;
                    QVMaps.elements.alertMessage.should('contain', 'Invalid qpids in array');
                });
        });
    });
});

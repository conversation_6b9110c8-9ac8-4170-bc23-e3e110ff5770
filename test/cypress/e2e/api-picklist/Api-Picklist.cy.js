describe('Api Picklist', { defaultCommandTimeout: 15000 }, () => {
    describe('apiPicklist endpoint validation', () => {
        it('expects apiPicklist to be the intended result', () => {
            cy.intercept('GET', env.BASE_URL + '/api-picklist/*').as('apiPicklist');

            cy.visitWithLogin('');
            cy.wait('@apiPicklist').then((res) => {
                expect(res.response.statusCode === 200);
                expect(res.response.body).to.not.be.empty;
                expect(res.response.body).to.be.an('object');
                expect(res.response.body.status).to.equal('SUCCESS');

                const { result } = res.response.body;
                const resultKeys = Object.keys(result);

                for (const apiPicklistKey of listOfApiPicklistKeys) {
                expect(resultKeys.includes(apiPicklistKey), `Spot check: ${apiPicklistKey} api picklist key exists`).to.be.true;
                }
            });
        });
    });
});

const listOfApiPicklistKeys = [
    'ObjectionType',
    'CategoryGroupType',
];

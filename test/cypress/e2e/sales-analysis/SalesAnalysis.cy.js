/* global cy before describe context */

import Home from '../../model/Home';
import SalesAnalysis from '../../model/SalesAnalysis';

const saleId = '5de06e46-f5ea-44e2-aee9-65a789851cb7'; // 14c2a4bc-4f83-4541-8b14-b399804fc3ad // 'f83e2d7a-baf5-47b4-9c5f-6d061a6d9aa8'; //'083727bb-6a3d-4937-a533-42b963673e6b';
const url = `?saleId=${saleId}`; // '?saleId=14c2a4bc-4f83-4541-8b14-b399804fc3ad';

const revisionSaleId= '602b8295-d526-4ef4-8c62-18adb031b525';

const revisionProperty = 1342086;

describe('Sales Analysis', { defaultCommandTimeout: 15000 }, () => {
    before(() => {
        cy.login();
    });

    context('Sales Analysis link exists on page', () => {
        before(() => {
            Home.visit();
        });

        it('Sales Analysis link exists on page', () => {
            Home.quickSearchBar.type(revisionProperty + '{enter}');
            SalesAnalysis.elements.salesAnalysisPropertyPageLink.then(elem => {
                expect(elem).to.have.attr('href', '#');
            });
        });
    });

    context('Check elements exist', () => {
        before(() => {
            SalesAnalysis.visitSaleAnalysisPage(saleId);
        });
        SalesAnalysis.checkSalesAnalysis();
    });

    context('Sales Analysis Calculations', () => {
        SalesAnalysis.salesAnalysisCalculations();
    });
});


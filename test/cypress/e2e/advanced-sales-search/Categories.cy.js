import Home from '../../model/Home';
import AdvancedSearch from '../../model/AdvancedSearch';
import AdvancedSearchSales from '../../model/AdvancedSearchSales';
import PropertySearchResults from '../../model/PropertySearchResults';

describe('Sales Search', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it(`restricts search by category types`, () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.salesSearch.click();
    AdvancedSearch.taRangeFrom.clear().type(env.TA_CODE.CHCH);
    AdvancedSearch.taRangeTo.clear().type(env.TA_CODE.CHCH);
    AdvancedSearchSales.includeCategory1Sales.clear().type('RD197*');
    AdvancedSearchSales.excludeCategory1Sales.clear().type('RD197B');
    AdvancedSearch.search();
    cy.wait(3000);
    PropertySearchResults.results.should('have.length.at.least', 5);
  });

  it('has results with expected categories', () => {
    PropertySearchResults.results.each(result => {
      cy.wrap(result)
        .find(PropertySearchResults.categorySelector)
        .invoke('text')
        .then(text => {
          expect(text).to.include('RD197');
          expect(text).to.not.include('RD197B');
        });
    });
  });
});

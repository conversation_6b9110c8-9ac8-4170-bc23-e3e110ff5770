import AdvancedSearch from '../../model/AdvancedSearch';
import AdvancedSearchSales from '../../model/AdvancedSearchSales';
import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';
import PropertySearchResults from '../../model/PropertySearchResults';

const category1Prefix = 'I';
const category2Prefix = 'RD';

const streetName = 'Southwark';
const nspFrom = '400000';
const minResults = 10;

describe('Sales Search', () => {
    before(() => {
        Home.visit();
    });
    //should exclude RD sales from southwark avenue, Napier
    it(`Sales search results for street: ${streetName}, NSP >= ${nspFrom}, TA: ${
        env.TA_CODE.CHCH
    }`, () => {
        Home.advancedSearchButton.click();
        AdvancedSearch.salesSearch.click();
        AdvancedSearch.taRangeFrom.clear().type(env.TA_CODE.CHCH);
        AdvancedSearch.taRangeTo.clear().type(env.TA_CODE.CHCH);
        AdvancedSearch.streetName.clear().type(streetName);
        AdvancedSearchSales.nspFrom.clear().type(nspFrom);
        AdvancedSearch.search();
        cy.wait(3000);
        PropertySearchResults.results.should('have.length.at.least', minResults);
    });
    it(`Results NSP above: ${nspFrom}`, () => {
        PropertySearchResults.results.each(result => {
            cy.wrap(result)
                .find(PropertySearchResults.nspSelector)
                .invoke('text')
                .then(text => {
                    expect(currencyToInt(text)).to.be.gte(parseInt(nspFrom));
                });
        });
    });
    it('Results category should not include any residential dwellings', () => {
        PropertySearchResults.results.each(result => {
            cy.wrap(result)
                .find(PropertySearchResults.categorySelectorSales)
                .invoke('text')
                .then(text => {
                    expect(text.trim().substring(0, 2)).to.not.equal(category2Prefix);
                });
        });
    });
    //should now see RD properties from southwark avenue, napier
    it(`Search again, remove TA: ${env.TA_CODE.CHCH}, include category ${category2Prefix}`, () => {
        Home.advancedSearchButton.click();
        AdvancedSearch.salesSearch.click();
        AdvancedSearch.taRangeFrom.clear();
        AdvancedSearch.taRangeTo.clear();
        AdvancedSearchSales.includeCategory1Sales.clear().type(`${category2Prefix}*`);
        AdvancedSearch.search();
        cy.wait(4000);
        PropertySearchResults.results.should('have.length.at.least', 1);
    });

    it('Results category should include only residential dwellings', () => {
        PropertySearchResults.results.each(result => {
            cy.wrap(result)
                .find(PropertySearchResults.categorySelectorSales)
                .invoke('text')
                .then(text => {
                    expect(text.trim().substring(0, 2)).to.equal(category2Prefix);
                });
        });
    });

    it('Compact view', () => {
        PropertySearchResults.compactViewToggle.click();
        PropertySearchResults.results.each(result => {
            cy.wrap(result).should('have.class', 'theSkinny');
        });
    });

    it('Normal view', () => {
        PropertySearchResults.compactViewToggle.click();
        PropertySearchResults.results.each(result => {
            cy.wrap(result).should('not.have.class', 'theSkinny');
        });
    });

    it('Expanded view', () => {
        PropertySearchResults.expandResultsButton.click();
        PropertySearchResults.results.each(result => {
            cy.wrap(result).should('have.class', 'openProp');
        });
    });

    it('Upload photos button', () => {
        PropertySearchResults.results.each(result => {
            cy.wrap(result)
                .find(PropertySearchResults.resultToolbarRightSelector)
                .find(PropertySearchResults.uploadPhotosSelector)
                .should('exist')
                .and('be.visible');
        });
    });

    it('Web search button', () => {
        PropertySearchResults.results.each(result => {
            cy.wrap(result)
                .find(PropertySearchResults.resultToolbarRightSelector)
                .contains('label', 'WEB')
                .should('exist')
                .and('be.visible');
        });
    });

    it('Sales analysis button', () => {
        PropertySearchResults.results.each(result => {
            cy.wrap(result)
                .find(PropertySearchResults.saleAnalysisSelector)
                .should('exist')
                .and('be.visible');
        });
    });

    it('Expanded => Normal view', () => {
        PropertySearchResults.expandResultsButton.click();
        PropertySearchResults.results.each(result => {
            cy.wrap(result).should('not.have.class', 'openProp');
        });
    });
});

const currencyToInt = amount => {
    //remove $ and ,
    return parseInt(
        amount
            .trim()
            .slice(1)
            .replace(/,/g, '')
    );
};

import AdvancedSearch from '../../model/AdvancedSearch';
import AdvancedSearchSales from '../../model/AdvancedSearchSales';
import Home from '../../model/Home';
import PropertySearchResults from '../../model/PropertySearchResults';

const streetName = 'Southwark';
const nspFrom = 400000;

describe('Sales Search', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it(`restricts search by minimum Net Sale Price`, () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.salesSearch.click();
    AdvancedSearch.taRangeFrom.clear().type(env.TA_CODE.CHCH);
    AdvancedSearch.taRangeTo.clear().type(env.TA_CODE.CHCH);
    AdvancedSearch.streetName.clear().type(streetName);
    AdvancedSearchSales.nspFrom.clear().type(nspFrom);
    AdvancedSearch.search();
    cy.wait(3000);
    PropertySearchResults.results.should('have.length.at.least', 10);
  });

  it(`has results with Net Sale Price above ${nspFrom}`, () => {
    PropertySearchResults.results.each(result => {
      cy.wrap(result)
        .find(PropertySearchResults.nspSelector)
        .invoke('text')
        .then(text => {
          const nsp = currencyToInt(text);
          expect(nsp).to.be.gte(nspFrom);
        });
    });
  });

});

function currencyToInt(amount) {
  return parseInt(amount.trim().slice(1).replace(/,/g, '')); // remove $ and ,
}


import { dashboard, reportCriteria, myReports } from '../../model/Reports';

/* Assumes the test user has access to at least one report */

describe('Report Criteria', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        reportCriteria.visit();
        cy.wait(1000);
    });

    context('Displays the expected elements', () => {

        it('Displays the notification panel', () => {
            dashboard.elements.notificationsPanel.then(panel => {
                expect(panel).to.exist.and.be.visible;
            });
        });
        
        it('Displays the report criteria panel', () => {
            reportCriteria.elements.criteriaWrapper.then(wrapper => {
                expect(wrapper).to.exist.and.be.visible;
            });
        });
        
        it('Displays the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.exist.and.be.visible;
                expect(button.text().toLowerCase()).to.contain('schedule report');
            });
        });
    });

    context('Scheduling a Report', () => {
        before(() => {
            reportCriteria.elements.scheduleReportButton.click();
        });

        it('Masks the page', () => {
            reportCriteria.elements.pageMask.should('exist');
        });

        it('Disables the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.be.disabled;
            });
        });
        
        it('Displays a modal', () => {
            reportCriteria.elements.reportModal.should('exist')
        });
    });

    context('Confirming the modal', () => {
        before(() => {
            reportCriteria.visit();
            reportCriteria.elements.scheduleReportButton.click();
            reportCriteria.elements.reportModalConfirm.click();
        });

        it('Hides the modal', () => {
            reportCriteria.elements.reportModal.should('not.exist')
        });
        
        it('Unmasks the page', () => {
            reportCriteria.elements.pageMask.should('not.exist')
        });

        it('Enables the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.not.be.disabled;
            });
        });
    });
    
    context('Clicking \'View My Reports\' in the modal', () => {
        before(() => {
            reportCriteria.visit();
            reportCriteria.elements.scheduleReportButton.click();
            reportCriteria.elements.reportModalCancel.click();
        });

        it('Hides the modal', () => {
            reportCriteria.elements.reportModal.should('not.exist')
        });
        
        it('Unmasks the page', () => {
            reportCriteria.elements.pageMask.should('not.exist')
        });

        it('Changes the route', () => {
            cy.url().should('include', '/reports/my-reports');
        });

        it('Report criteria is no longer displayed in main panel', () => {
            reportCriteria.elements.criteriaWrapper.should('not.exist');
        });

        it('My Reports are displayed in main panel ', () => {
            myReports.elements.heading.should('exist');
        });
    });
    
});

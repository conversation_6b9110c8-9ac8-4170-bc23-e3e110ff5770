import { reportCriteria, RevaluationSurveyResults, myReports, dashboard } from '../../model/Reports';

/* Assumes the test user has access to at least one report */

describe('Generic json report scheduling', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        RevaluationSurveyResults.visit();
        cy.wait(1000);
    });

    it('Display schedule failed modal when no options are selected', () => {
        reportCriteria.elements.scheduleReportButton.click();
        reportCriteria.elements.alertModal.should('exist');
        reportCriteria.elements.reportModalConfirm.should('exist');
        reportCriteria.elements.reportModalCancel.should('not.exist');
    });
});
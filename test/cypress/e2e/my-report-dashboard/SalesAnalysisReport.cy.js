import { reportCriteria, myReports, dashboard, SalesAnalysisReport } from '../../model/Reports';
import "cypress-real-events";

/* Assumes the test user has access to at least one report */

describe('Sales Analysis Report scheduling', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
    });

    context('Scheduling a Standard Report', () => {
        before(() => {
            SalesAnalysisReport.visit();
            cy.wait(3000);
        });

        context('Scheduling a Report', () => {
            before(() => {
                SalesAnalysisReport.scheduleStandardReport();
            });

            it('Masks the page', () => {
                reportCriteria.elements.pageMask.should('exist');
            });

            it('Disables the \'Schedule Report\' button', () => {
                reportCriteria.elements.scheduleReportButton.then(button => {
                    expect(button).to.be.disabled;
                });
            });

            it('Displays a modal', () => {
                reportCriteria.elements.reportModal.should('exist')
            });
        });

        context('Clicking \'View My Reports\' in the modal', () => {
            before(() => {
                reportCriteria.elements.reportModalCancel.click();
            });

            it('Hides the modal', () => {
                reportCriteria.elements.reportModal.should('not.be.visible')
            });

            it('Unmasks the page', () => {
                reportCriteria.elements.pageMask.should('not.exist')
            });

            it('Changes the route', () => {
                cy.url().should('include', dashboard.elements.url);
            });

            it('Report criteria is no longer displayed in main panel', () => {
                reportCriteria.elements.criteriaWrapper.should('not.exist');
            });

            it('My Reports are displayed in main panel ', () => {
                myReports.elements.heading.should('exist');
            });
        });
    });

    context('Scheduling a Commercial Report', () => {
        before(() => {
            SalesAnalysisReport.visit();
            cy.wait(3000);
        });

        context('Scheduling a Report', () => {
            before(() => {
                SalesAnalysisReport.scheduleCommercialReport();
            });

            it('Masks the page', () => {
                reportCriteria.elements.pageMask.should('exist');
            });

            it('Disables the \'Schedule Report\' button', () => {
                reportCriteria.elements.scheduleReportButton.then(button => {
                    expect(button).to.be.disabled;
                });
            });

            it('Displays a modal', () => {
                reportCriteria.elements.reportModal.should('exist')
            });
        });

        context('Clicking \'View My Reports\' in the modal', () => {
            before(() => {
                reportCriteria.elements.reportModalCancel.click();
            });

            it('Hides the modal', () => {
                reportCriteria.elements.reportModal.should('not.be.visible')
            });

            it('Unmasks the page', () => {
                reportCriteria.elements.pageMask.should('not.exist')
            });

            it('Changes the route', () => {
                cy.url().should('include', dashboard.elements.url);
            });

            it('Report criteria is no longer displayed in main panel', () => {
                reportCriteria.elements.criteriaWrapper.should('not.exist');
            });

            it('My Reports are displayed in main panel ', () => {
                myReports.elements.heading.should('exist');
            });
        });
    });

    context('Scheduling a Rural Report', () => {
        before(() => {
            SalesAnalysisReport.visit();
            cy.wait(3000);
        });

        context('Scheduling a Report', () => {
            before(() => {
                SalesAnalysisReport.scheduleRuralReport();
            });

            it('Masks the page', () => {
                reportCriteria.elements.pageMask.should('exist');
            });

            it('Disables the \'Schedule Report\' button', () => {
                reportCriteria.elements.scheduleReportButton.then(button => {
                    expect(button).to.be.disabled;
                });
            });

            it('Displays a modal', () => {
                reportCriteria.elements.reportModal.should('exist')
            });
        });

        context('Clicking \'View My Reports\' in the modal', () => {
            before(() => {
                reportCriteria.elements.reportModalCancel.click();
            });

            it('Hides the modal', () => {
                reportCriteria.elements.reportModal.should('not.be.visible')
            });

            it('Unmasks the page', () => {
                reportCriteria.elements.pageMask.should('not.exist')
            });

            it('Changes the route', () => {
                cy.url().should('include', dashboard.elements.url);
            });

            it('Report criteria is no longer displayed in main panel', () => {
                reportCriteria.elements.criteriaWrapper.should('not.exist');
            });

            it('My Reports are displayed in main panel ', () => {
                myReports.elements.heading.should('exist');
            });
        });
    });

    context('Scheduling a Residential Report', () => {
        before(() => {
            SalesAnalysisReport.visit();
            cy.wait(3000);
        });

        context('Scheduling a Report', () => {
            before(() => {
                SalesAnalysisReport.scheduleResidentialReport();
            });

            it('Masks the page', () => {
                reportCriteria.elements.pageMask.should('exist');
            });

            it('Disables the \'Schedule Report\' button', () => {
                reportCriteria.elements.scheduleReportButton.then(button => {
                    expect(button).to.be.disabled;
                });
            });

            it('Displays a modal', () => {
                reportCriteria.elements.reportModal.should('exist')
            });
        });

        context('Clicking \'View My Reports\' in the modal', () => {
            before(() => {
                reportCriteria.elements.reportModalCancel.click();
            });

            it('Hides the modal', () => {
                reportCriteria.elements.reportModal.should('not.be.visible')
            });

            it('Unmasks the page', () => {
                reportCriteria.elements.pageMask.should('not.exist')
            });

            it('Changes the route', () => {
                cy.url().should('include', dashboard.elements.url);
            });

            it('Report criteria is no longer displayed in main panel', () => {
                reportCriteria.elements.criteriaWrapper.should('not.exist');
            });

            it('My Reports are displayed in main panel ', () => {
                myReports.elements.heading.should('exist');
            });
        });
    });


    context('Validations', () => {
        before(() => {
            SalesAnalysisReport.visit();
            cy.wait(3000);
        });

        context('Category Group - alphanumeric textfield, accepts * wildcard', () => {
            before(() => {
                SalesAnalysisReport.elements.taSelector.type('1{enter}');
                cy.get('body').click(0, 0);
                cy.wait(1000);
                SalesAnalysisReport.elements.saleDateRangeFrom.type('01/01/2021');
                SalesAnalysisReport.elements.standardReport.click();
            });

            it('Accepts alphanumeric characters & accepts "*" wildcard', () => {
                SalesAnalysisReport.elements.categoryGroupInput.should('exist')
                    .and('be.visible')
                    .and('be.enabled')
                    .and('have.attr', 'type', 'text');

                SalesAnalysisReport.elements.categoryGroupInput.type('Test123');
                SalesAnalysisReport.elements.categoryGroupInput.should('have.value', 'Test123');

                SalesAnalysisReport.elements.categoryGroupInput.clear();

                SalesAnalysisReport.elements.categoryGroupInput.type('R*');
                SalesAnalysisReport.elements.categoryGroupInput.should('have.value', 'R*');
            });

            it('Successfully schedules report', () => {
                reportCriteria.elements.scheduleReportButton.click();
                reportCriteria.elements.reportModal.should('exist');
                reportCriteria.elements.reportModalConfirm.click();
            });

            after('Clears the textfield', () => {
                SalesAnalysisReport.elements.categoryGroupInput.clear();
                SalesAnalysisReport.elements.clearReportButton.click();
            });
        });

        context('If a TA or Roll/s entered but Sale Date Range is left blank/range incomplete system must show a validation error', () => {
            before(() => {
                SalesAnalysisReport.inputFields();
                SalesAnalysisReport.elements.saleDateRangeFrom.clear();
                SalesAnalysisReport.elements.saleDateRangeTo.clear();
                reportCriteria.elements.scheduleReportButton.click();
            });

            it('Validation message appears on Sale Date Range To', () => {
                SalesAnalysisReport.elements.saleDateRangeTo.realClick().then(() => {
                    SalesAnalysisReport.elements.saleDateRangeTo.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Sale Date To is required');
                });
            });

            it('Validation message appears on Sale Date Range From', () => {
                SalesAnalysisReport.elements.saleDateRangeFrom.realClick().then(() => {
                    SalesAnalysisReport.elements.saleDateRangeFrom.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Sale Date From is required');
                });
            });

            after(() => {
                SalesAnalysisReport.elements.clearReportButton.click();
            });
        });

        context("Following fields: 'Net Price Range’, ‘Capital Value Range’ and ‘Production Units’", () => {
            beforeEach(() => {
                SalesAnalysisReport.inputFields();
            });

            const lowNum = '100';
            const highNum = '200';
            it('Value only entered into field name From → error message: ‘[field name] From can only be used when ‘[field name] To’ is also used ', () => {
                SalesAnalysisReport.elements.netPriceRangeFrom.type(lowNum);
                SalesAnalysisReport.elements.capitalValueRangeFrom.type(lowNum);
                SalesAnalysisReport.elements.productionUnitsRangeFrom.type(lowNum);

                reportCriteria.elements.scheduleReportButton.click();

                SalesAnalysisReport.elements.netPriceRangeTo.realClick().then(() => {
                    SalesAnalysisReport.elements.netPriceRangeTo.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Net Price From can only be used when Net Price To is also used');
                });
                SalesAnalysisReport.elements.capitalValueRangeTo.realClick().then(() => {
                    SalesAnalysisReport.elements.capitalValueRangeTo.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Capital Value From can only be used when Capital Value To is also used');
                });
                SalesAnalysisReport.elements.productionUnitsRangeTo.realClick().then(() => {
                    SalesAnalysisReport.elements.productionUnitsRangeTo.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Production Unit From can only be used when Production Unit To is also used');
                });

                SalesAnalysisReport.elements.clearReportButton.click({ force: true });
            });

            it('Value only entered into field name To → error message: ‘[field name] To can only be used when ‘[field name] From’ is also used ', () => {
                SalesAnalysisReport.elements.netPriceRangeTo.type(lowNum);
                SalesAnalysisReport.elements.capitalValueRangeTo.type(lowNum);
                SalesAnalysisReport.elements.productionUnitsRangeTo.type(lowNum);

                reportCriteria.elements.scheduleReportButton.click();

                SalesAnalysisReport.elements.netPriceRangeFrom.realClick().then(() => {
                    SalesAnalysisReport.elements.netPriceRangeFrom.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Net Price To can only be used when Net Price From is also used');
                });
                SalesAnalysisReport.elements.capitalValueRangeFrom.realClick().then(() => {
                    SalesAnalysisReport.elements.capitalValueRangeFrom.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Capital Value To can only be used when Capital Value From is also used');
                });
                SalesAnalysisReport.elements.productionUnitsRangeFrom.realClick().then(() => {
                    SalesAnalysisReport.elements.productionUnitsRangeFrom.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Production Unit To can only be used when Production Unit From is also used');
                });

                SalesAnalysisReport.elements.clearReportButton.click({ force: true });
            });

            it('Error when value in From field is greater than the value in To field', () => {
                SalesAnalysisReport.elements.netPriceRangeFrom.type(highNum);
                SalesAnalysisReport.elements.capitalValueRangeFrom.type(highNum);
                SalesAnalysisReport.elements.productionUnitsRangeFrom.type(highNum);

                SalesAnalysisReport.elements.netPriceRangeTo.type(lowNum);
                SalesAnalysisReport.elements.capitalValueRangeTo.type(lowNum);
                SalesAnalysisReport.elements.productionUnitsRangeTo.type(lowNum);

                reportCriteria.elements.scheduleReportButton.click();


                SalesAnalysisReport.elements.netPriceRangeFrom.realClick().then(() => {
                    SalesAnalysisReport.elements.netPriceRangeFrom.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Net Price From must be less than Net Price To');
                });
                SalesAnalysisReport.elements.capitalValueRangeFrom.realClick().then(() => {
                    SalesAnalysisReport.elements.capitalValueRangeFrom.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Capital Value From must be less than Capital Value To');
                });
                SalesAnalysisReport.elements.productionUnitsRangeFrom.realClick().then(() => {
                    SalesAnalysisReport.elements.productionUnitsRangeFrom.parent().find('.tooltip')
                        .should('exist')
                        .and('be.visible')
                        .and('contain', 'Production Unit From must be less than Production Unit To');
                });

            });

            after(() => {
                SalesAnalysisReport.elements.clearReportButton.click({ force: true });
            });

        });

        context('On clicking [CLEAR], the users selection are cleared and filters re-set to defaults', () => {
            before(() => {
                SalesAnalysisReport.inputFields();
                SalesAnalysisReport.elements.categoryGroupInput.type('Test123');
                SalesAnalysisReport.elements.netPriceRangeFrom.type('100');
                SalesAnalysisReport.elements.capitalValueRangeFrom.type('100');
                SalesAnalysisReport.elements.productionUnitsRangeFrom.type('100');
                SalesAnalysisReport.elements.netPriceRangeTo.type('100');
                SalesAnalysisReport.elements.capitalValueRangeTo.type('100');
                SalesAnalysisReport.elements.productionUnitsRangeTo.type('100');

                SalesAnalysisReport.elements.clearReportButton.click();
            });

            it('TA Selector is cleared', () => {
                SalesAnalysisReport.elements.taSelector.should('have.value', '');
            });

            it('Sale Date From is cleared, Sale Date To defaults to today\'s date', () => {
                SalesAnalysisReport.elements.saleDateRangeFrom.should('have.value', '');
                const date = getTodayDateString();
                SalesAnalysisReport.elements.saleDateRangeTo
                    .find('.mx-input')
                    .should('have.value', date);
            });

            it('Category Group is cleared', () => {
                SalesAnalysisReport.elements.categoryGroupInput.should('have.value', '');
            });

            it('Net Price Range is cleared', () => {
                SalesAnalysisReport.elements.netPriceRangeFrom.should('have.value', '');
                SalesAnalysisReport.elements.netPriceRangeTo.should('have.value', '');
            });

            it('Capital Value Range is cleared', () => {
                SalesAnalysisReport.elements.capitalValueRangeFrom.should('have.value', '');
                SalesAnalysisReport.elements.capitalValueRangeTo.should('have.value', '');
            });

            it('Production Units is cleared', () => {
                SalesAnalysisReport.elements.productionUnitsRangeFrom.should('have.value', '');
                SalesAnalysisReport.elements.productionUnitsRangeTo.should('have.value', '');
            });

            it('Analysed Sales Only is set to On', () => {
                SalesAnalysisReport.elements.analysedSalesOnlyRadio.should('be.checked');
            });

            it('Market Freehold Sales Only is set to Off', () => {
                SalesAnalysisReport.elements.marketFreeholdSalesOnlyRadio.should('not.be.checked');
            });

            it('Report Type is set to Full', () => {
                SalesAnalysisReport.elements.fullReport.should('be.checked');
                SalesAnalysisReport.elements.standardReport.should('not.be.checked');
            });

            after(() => {
                SalesAnalysisReport.elements.clearReportButton.click();
            });
        });
    });

    context('Name', () => {
        before(() => {
            SalesAnalysisReport.visit();
            cy.wait(3000);
        });

        it('Report should be titled “Analysed sales”', () => {
            dashboard.elements.menuPanel.find('.active')
                .should('contain', 'Analysed Sales');
        })

        it('Notifications panel should say “ Generate a report of Analysed Sales for a TA or Roll', () => {
            dashboard.elements.notificationsPanel
                .should('contain', 'Generate a report of Analysed Sales for a TA or Roll');
        });
    });
});


function getTodayDateString() {
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = today.getMonth() + 1;
    const dd = today.getDate();

    const formattedToday = dd + '/' + mm + '/' + yyyy;

    return formattedToday;
}

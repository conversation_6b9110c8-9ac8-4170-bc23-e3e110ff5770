
import { dashboard, myReports } from '../../model/Reports';

describe('My Reports (Job List)', { defaultCommandTimeout: 10000 }, () => {
    context('Displays the expected elements', () => {
        before(() => {
            cy.login();
            dashboard.visit();
        });

        it('Displays the heading', () => {
            myReports.elements.heading.then(heading => {
                expect(heading).to.exist.and.be.visible;
            });
        });

        it('Displays the job list', () => {
            myReports.elements.jobTable.then(table => {
                expect(table).to.exist.and.be.visible;
            });
        });

        it('Job list has the expected columns', () => {
            myReports.elements.jobColumns.then(columns => {
                expect(columns.length).to.equal(8); // 8th column is a blank column for downloads

                expect(columns[0].querySelector('input[type="checkbox"]')).to.exist; // columns[0] is the clickbox column
                expect(columns[1]).to.contain('Name');
                expect(columns[2]).to.contain('Type');
                expect(columns[3]).to.contain('Size');
                expect(columns[4]).to.contain('Run Time');
                expect(columns[5]).to.contain('Scheduled Date');
                expect(columns[6]).to.contain('Status');
            });
        });
    });
});

import { dashboard, myReports, reportCriteria, reportMenu } from '../../model/Reports';

const TIME_TO_WAIT_FOR_REPORT = 12000;
const FILE_NAME = 'TA75-RevisionRuralWorksheet.xlsx';

describe('Rural Worksheet Revision Upload Report', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        dashboard.visit();
    });

    it('Should be able to navigate to the report', () => {
        reportMenu.expand();
        reportMenu.elements.reports.contains('Revision Rural Worksheet').click();
        cy.get('[data-cy="rw-upload-title"]').should('contain', 'Revision Rural Worksheet');
    });

    it('Should be able to upload a file and schedule the report', () => {
        cy.get('[data-cy="rw-file-selector"]').find('input[type="file"]').selectFile(`cypress/fixtures/${FILE_NAME}`, { force: true });
        cy.get('[data-cy="rw-file-selector"]').find('h3').should('contain', FILE_NAME);
        cy.get('[data-cy="upload-worksheet-button"]').click();
        cy.get('[data-cy="upload-worksheet-modal"]').find('h1').should('contain', 'Report Scheduled');
        cy.wait(TIME_TO_WAIT_FOR_REPORT);
        reportCriteria.elements.reportModalCancel.click();
    });

    it('Should be able to view the completed report in My Reports', () => {
        myReports.elements.heading.should('exist').and('contain', 'My Reports');
        myReports.elements.jobTable.should('be.visible');
        myReports.elements.jobTableRow.first().then((row) => {
            cy.wrap(row).get('[data-cy="report-name"]').should('contain', 'Revision Rural Worksheet Upload');
            cy.wrap(row).get('[data-cy="report-status"]').should('contain', 'Completed');
        });
    });

    it('Should be able to delete the report', () => {
        myReports.elements.jobTableRow.first().then((row) => {
            cy.wrap(row).find('[data-cy="report-name"]').click();
            cy.wrap(row).find('[data-cy="report-checkbox"]').should('be.checked');

            myReports.elements.deleteButton.click();
            cy.intercept('POST', '/report-job-status-update*').as('deleteReport');
            cy.get('[data-cy="report-modal-confirm"]').click();
            cy.wait('@deleteReport').then((interception) => {
                expect(interception.response.statusCode).to.eq(200);
                expect(interception.response.body.status).to.eq('UPDATED');
            });
        });
    });
});

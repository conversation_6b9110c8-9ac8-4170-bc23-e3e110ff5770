import { WorkUnitReport } from '../../model/WorkUnitReport.js';

const valuerRoleList = [
    'Area Valuer',
    'Managing Senior Valuer',
    'Rating Valuer',
    'Registered Valuer',
    'RTV Valuer',
    'Senior Valuer',
    'Valuer'
];

const nonValuerRoleList = [
        'Customer Care',
        'Receptionist Typist', 
        'Reporting - Manager',
        'Reporting - Reval'
    ];

const customerCareOnlyRoleList = [
    'Customer Care'
];

const valuerFullName = '<PERSON>ley'; // This Valuer User needs to be active to work in this test
const allValuersDefault = 'All Valuers';

describe('Work Unit Report', { defaultCommandTimeout: 10000 }, () => {

    context('User Permissions Tests:', () => {

        context('Non-Valuer User with Non-Valuer Permissions', () => {   
            before(() => {
                cy.login();
                cy.overrideInternalUserData({   
                    roles: nonValuerRoleList
                });
                cy.wait(1000);
                WorkUnitReport.visit();
                cy.wait(1000);
            });
            
            it('should display the current active Valuer in the Valuer field by default', () => {
                WorkUnitReport.verifyValuerSelectedShouldContain(allValuersDefault);
            });
            
            it('should be able to select other valuers from the Valuer field by default', () =>{
                WorkUnitReport.verifyValuerOptionsContainAll();
            });
            
            it('should not disable the TA dropdown field', () => {
                WorkUnitReport.checkTADropdownFieldIsEnabled();  
            });
        });
        
        context('Valuer User with Valuer Permissions', () => {   
            before(() => {
                cy.login();
                cy.overrideUserData({
                    name: valuerFullName,
                    userFullName: valuerFullName,
                });
                cy.overrideInternalUserData({   
                    roles: valuerRoleList
                });
                cy.wait(1000);
                WorkUnitReport.visit();
                cy.wait(1000);
            });
            
            it('should display the current active Valuer in the Valuer field by default', () => {
                WorkUnitReport.verifyValuerSelectedShouldContain(valuerFullName);
            });
            
            it('should not be able to select other valuers from the Valuer field by default', () =>{
                cy.wait(1000);
                WorkUnitReport.verifyValuerOptionsOnlyContainSelected();
            });
            
            it('should disable the TA dropdown field', () => {                
                WorkUnitReport.checkTADropdownFieldIsDisabled();
            });
        });
        
        context('Valuer User with non Valuer Permissions', () => {   
            before(() => {
                cy.login();
                cy.overrideUserData({
                    name: valuerFullName,
                    userFullName: valuerFullName,
                });
                cy.overrideInternalUserData({   
                    roles: nonValuerRoleList
                });
                cy.wait(1000);
                WorkUnitReport.visit();
            });
            
            it('should display \'All Valuers\' in the Valuer field by default', () =>{
                WorkUnitReport.verifyValuerSelectedShouldContain(allValuersDefault);
            });
            
            it('should be able to select other valuers from the Valuer field by default', () =>{
                cy.wait(1000);
                WorkUnitReport.verifyValuerOptionsContainAll();
            });

            it('should not disable the TA dropdown field', () =>{
                WorkUnitReport.checkTADropdownFieldIsEnabled();                
            });
        });
        
        context('Non-Valuer User with Valuer Permissions', () => {   
            before(() => {
                cy.login();
                cy.overrideInternalUserData({   
                    roles: valuerRoleList
                });
                cy.wait(1000);
                WorkUnitReport.visit();
            });
            
            it('should display \'All Valuers\' in the Valuer field by default', () =>{
                WorkUnitReport.verifyValuerSelectedShouldContain(allValuersDefault);
            });
            
            it('should be able to select other valuers from the Valuer field by default', () =>{
                cy.wait(1000);
                WorkUnitReport.verifyValuerOptionsContainAll();
            });

            it('should not disable the TA dropdown field', () =>{                
                WorkUnitReport.checkTADropdownFieldIsEnabled();  
            });
        });
        
        context('Non-Valuer User with only Customer Care Permission', () => {   
            before(() => {
                cy.login();
                cy.overrideUserData({
                    name: valuerFullName,
                });
                cy.overrideInternalUserData({   
                    roles: customerCareOnlyRoleList
                });
                cy.wait(1000);
                WorkUnitReport.visit();
            });
            
            it('should have a disabled submit button with only a Customer Care Permission', () =>{
                WorkUnitReport.checkScheduleReportButtonIsDisabled();
            });
        });
    });
});
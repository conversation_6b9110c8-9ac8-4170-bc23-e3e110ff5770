
import { toolbar } from '../../model/SecondaryNavigationToolbar';
import { dashboard, myReports } from '../../model/Reports';

describe('Report Dashboard', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        cy.visit('');
    });

    context('Navigation', () => {

        it('Exists in the toolbar', () => {
            toolbar.reportsDashboardTab.then(tab => {
                expect(tab).to.exist.and.be.visible;
            });
        });

        it('Routes to reports dashboard', () => {
            toolbar.reportsDashboardTab.click();
            
            cy.url().should('include', '/reports/my-reports');

            toolbar.reportsDashboardTab.then(tab => {
                expect(tab).to.have.class('active');
            });

            dashboard.elements.mainPanel.then(panel => {
                expect(panel).to.exist.and.be.visible;
            });
        });

        it('Displays the \'My Reports\' job list by default', () => {
            myReports.elements.heading.then(heading => {
                expect(heading).to.exist.and.be.visible;
            });

            myReports.elements.jobTable.then(table => {
                expect(table).to.exist.and.be.visible;
            });
        });
    });
});

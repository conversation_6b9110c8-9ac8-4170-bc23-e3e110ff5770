import { reportCriteria, myReports, dashboard, OVGCostRecoveryStatisticsReport } from '../../model/Reports';

/* Assumes the test user has access to at least one report */

describe('OVG cost recovery statistics report scheduling', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        OVGCostRecoveryStatisticsReport.visit();
        cy.wait(1000);
    });

    context('Press Schedule Report button without filling in fields', () => {
        before(() => {
            reportCriteria.elements.scheduleReportButton.click();
        });

        it('Displays a modal, contains expected error messages, able to close modal', () => {
            reportCriteria.elements.reportModal.should('exist');
            reportCriteria.elements.alertModalText
                .should('contain', 'You must enter one or more TAs to generate this report')
                .and('contain', 'You must enter a start date to generate this report')
                .and('contain', 'You must enter an end date to generate this report')
            });

        after(() => {
            OVGCostRecoveryStatisticsReport.elements.reportModalConfirm.click();
        });
    });

    context('Clearing the Report Criteria', () => {
        before(() => {
            OVGCostRecoveryStatisticsReport.populateReportCriteria();
        });

        it('Clears the criteria', () => {
            reportCriteria.clearCriteria();
            OVGCostRecoveryStatisticsReport.elements.taSelector
                .should('have.value', '');
            OVGCostRecoveryStatisticsReport.elements.startDate
                .should('have.value', '');
            OVGCostRecoveryStatisticsReport.elements.endDate
                .should('have.value', '');
        });
    });

    context('Scheduling a Report', () => {
        before(() => {
            OVGCostRecoveryStatisticsReport.scheduleReport();
        });

        it('Masks the page', () => {
            reportCriteria.elements.pageMask.should('exist');
        });

        it('Disables the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.be.disabled;
            });
        });

        it('Displays a modal', () => {
            reportCriteria.elements.reportModal.should('exist');
            OVGCostRecoveryStatisticsReport.elements.reportModalConfirm.click();
        });
    });

    context('Scheduling a Report with more than 1 TA', () => {
        before(() => {
            reportCriteria.clearCriteria();
            OVGCostRecoveryStatisticsReport.scheduleReport(['1','2','3']);
        });

        it('Masks the page', () => {
            reportCriteria.elements.pageMask.should('exist');
        });

        it('Disables the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.be.disabled;
            });
        });

        it('Displays a modal', () => {
            reportCriteria.elements.reportModal.should('exist');
        });
    });

    context('Clicking \'View My Reports\' in the modal', () => {
        before(() => {
            OVGCostRecoveryStatisticsReport.elements.reportModalCancel.click();
        });

        it('Hides the modal', () => {
            reportCriteria.elements.reportModal.should('not.be.visible')
        });

        it('Unmasks the page', () => {
            reportCriteria.elements.pageMask.should('not.exist')
        });

        it('Changes the route', () => {
            cy.url().should('include', dashboard.elements.url);
        });

        it('Report criteria is no longer displayed in main panel', () => {
            reportCriteria.elements.criteriaWrapper.should('not.exist');
        });

        it('My Reports are displayed in main panel ', () => {
            myReports.elements.heading.should('exist');
        });
    });

});

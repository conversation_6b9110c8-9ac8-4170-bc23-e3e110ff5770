import { reportCriteria, exceptionListingReport, myReports, dashboard } from '../../model/Reports';

/* Assumes the test user has access to at least one report */

describe('Exception listing report scheduling', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        exceptionListingReport.visit();
        cy.wait(1000);
    });

    context('Scheduling a Report', () => {
        before(() => {
            scheduleReport();
        });

        it('Masks the page', () => {
            reportCriteria.elements.pageMask.should('exist');
        });

        it('Disables the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.be.disabled;
            });
        });

        it('Displays a modal', () => {
            reportCriteria.elements.reportModal.should('exist')
        });
    });

    context('Clicking \'View My Reports\' in the modal', () => {
        before(() => {
            reportCriteria.elements.reportModalCancel.click();
        });

        it('Hides the modal', () => {
            reportCriteria.elements.reportModal.should('not.exist')
        });

        it('Unmasks the page', () => {
            reportCriteria.elements.pageMask.should('not.exist')
        });

        it('Changes the route', () => {
            cy.url().should('include', dashboard.elements.url);
        });

        it('Report criteria is no longer displayed in main panel', () => {
            reportCriteria.elements.criteriaWrapper.should('not.exist');
        });

        it('My Reports are displayed in main panel ', () => {
            myReports.elements.heading.should('exist');
        });
    });

});

function scheduleReport() {
    exceptionListingReport.elements.singleRollInput.type(11);
    reportCriteria.elements.scheduleReportButton.click();
}
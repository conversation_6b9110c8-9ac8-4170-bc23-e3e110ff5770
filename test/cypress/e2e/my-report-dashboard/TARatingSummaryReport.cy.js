import { reportCriteria, TARatingSummaryReport, myReports, dashboard } from '../../model/Reports';

/* Assumes the test user has access to at least one report */

describe('TA Rating Summary report scheduling', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        TARatingSummaryReport.visit();  
        cy.wait(1000);      
    });

    context('Scheduling a Report', () => {
        before(() => {
            TARatingSummaryReport.elements.taSelector.click().type('1', {force: true}).type('{enter}', {force: true});
            cy.get('body').click(0, 0);            
        });    
        it('Has a 201 response', () => {
            cy.intercept('POST', '/report-job*').as('createReportJob');
            reportCriteria.elements.scheduleReportButton.click();
            cy.wait('@createReportJob').then((interception) => {
                expect(interception.response.statusCode).to.equal(201);
            });
        });    
        it('Masks the page', () => {
            reportCriteria.elements.pageMask.should('exist');
        });

        it('Disables the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.be.disabled;
            });
        });

        it('Displays a modal', () => {
            reportCriteria.elements.reportModal.should('exist')
        });
    });

    context('Clicking \'View My Reports\' in the modal', () => {
        before(() => {
            reportCriteria.elements.reportModalCancel.click();
        });

        it('Unmasks the page', () => {
            reportCriteria.elements.pageMask.should('not.exist')
        });

        it('Changes the route', () => {
            cy.url().should('include', dashboard.elements.url);
        });

        it('Report criteria is no longer displayed in main panel', () => {
            reportCriteria.elements.criteriaWrapper.should('not.exist');
        });

        it('My Reports are displayed in main panel ', () => {
            myReports.elements.heading.should('exist');
        });
    });

});
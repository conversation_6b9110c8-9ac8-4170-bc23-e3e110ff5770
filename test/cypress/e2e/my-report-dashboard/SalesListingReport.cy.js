import { reportCriteria, myReports, dashboard, SalesListingReport } from '../../model/Reports';

/* Assumes the test user has access to at least one report */

describe('Sales Listing Report scheduling', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
    });

    context('Scheduling a Sales Listing Roll Report', () => {
        before(() => {
            SalesListingReport.visit();
            cy.wait(1000);
        });
        context('Scheduling a Report', () => {
            before(() => {
                SalesListingReport.scheduleRollReport();
            });

            it('Masks the page', () => {
                reportCriteria.elements.pageMask.should('exist');
            });

            it('Disables the \'Schedule Report\' button', () => {
                reportCriteria.elements.scheduleReportButton.then(button => {
                    expect(button).to.be.disabled;
                });
            });

            it('Displays a modal', () => {
                reportCriteria.elements.reportModal.should('exist')
            });
        });

        context('Clicking \'View My Reports\' in the modal', () => {
            before(() => {
                SalesListingReport.elements.reportModalCancel.click();
            });

            it('Hides the modal', () => {
                reportCriteria.elements.reportModal.should('not.be.visible')
            });

            it('Unmasks the page', () => {
                reportCriteria.elements.pageMask.should('not.exist')
            });

            it('Changes the route', () => {
                cy.url().should('include', dashboard.elements.url);
            });

            it('Report criteria is no longer displayed in main panel', () => {
                reportCriteria.elements.criteriaWrapper.should('not.exist');
            });

            it('My Reports are displayed in main panel ', () => {
                myReports.elements.heading.should('exist');
            });
        });
    });

    context('Scheduling a Sales Listing Sales Group Report', () => {
        before(() => {
            SalesListingReport.visit();
            cy.wait(1000);
        });
        context('Scheduling a Report', () => {
            before(() => {
                SalesListingReport.scheduleSalesGroupReport();
            });

            it('Masks the page', () => {
                reportCriteria.elements.pageMask.should('exist');
            });

            it('Disables the \'Schedule Report\' button', () => {
                reportCriteria.elements.scheduleReportButton.then(button => {
                    expect(button).to.be.disabled;
                });
            });

            it('Displays a modal', () => {
                reportCriteria.elements.reportModal.should('exist')
            });
        });

        context('Clicking \'View My Reports\' in the modal', () => {
            before(() => {
                SalesListingReport.elements.reportModalCancel.click();
            });

            it('Hides the modal', () => {
                reportCriteria.elements.reportModal.should('not.be.visible');
            });

            it('Unmasks the page', () => {
                reportCriteria.elements.pageMask.should('not.exist')
            });

            it('Changes the route', () => {
                cy.url().should('include', dashboard.elements.url);
            });

            it('Report criteria is no longer displayed in main panel', () => {
                reportCriteria.elements.criteriaWrapper.should('not.exist');
            });

            it('My Reports are displayed in main panel ', () => {
                myReports.elements.heading.should('exist');
            });
        });
    });

    context('Scheduling a Sales Listing TA Report', () => {
        before(() => {
            SalesListingReport.visit();
            cy.wait(1000);
        });
        context('Scheduling a Report', () => {
            before(() => {
                SalesListingReport.scheduleTAReport();
            });

            it('Masks the page', () => {
                reportCriteria.elements.pageMask.should('exist');
            });

            it('Disables the \'Schedule Report\' button', () => {
                reportCriteria.elements.scheduleReportButton.then(button => {
                    expect(button).to.be.disabled;
                });
            });

            it('Displays a modal', () => {
                reportCriteria.elements.reportModal.should('exist')
            });
        });

        context('Clicking \'View My Reports\' in the modal', () => {
            before(() => {
                SalesListingReport.elements.reportModalCancel.click();
            });

            it('Hides the modal', () => {
                reportCriteria.elements.reportModal.should('not.be.visible');
            });

            it('Unmasks the page', () => {
                reportCriteria.elements.pageMask.should('not.exist')
            });

            it('Changes the route', () => {
                cy.url().should('include', dashboard.elements.url);
            });

            it('Report criteria is no longer displayed in main panel', () => {
                reportCriteria.elements.criteriaWrapper.should('not.exist');
            });

            it('My Reports are displayed in main panel ', () => {
                myReports.elements.heading.should('exist');
            });
        });
    });
});

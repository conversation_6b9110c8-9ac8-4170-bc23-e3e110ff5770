import { reportCriteria, objectionListingReport, myReports } from '../../model/Reports';

/* Assumes the test user has access to at least one report */

describe('Objection listing report scheduling', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        objectionListingReport.visit();
        cy.wait(1000);
    });

    context('Scheduling a Report', () => {
        before(() => {
            scheduleReport();
        });

        it('Masks the page', () => {
            reportCriteria.elements.pageMask.should('exist');
        });

        it('Disables the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.be.disabled;
            });
        });

        it('Displays a modal', () => {
            reportCriteria.elements.reportModal.should('exist')
        });
    });

    context('Clicking \'View My Reports\' in the modal', () => {
        before(() => {
            reportCriteria.elements.reportModalCancel.click();
        });

        it('Hides the modal', () => {
            reportCriteria.elements.reportModal.should('not.exist')
        });

        it('Unmasks the page', () => {
            reportCriteria.elements.pageMask.should('not.exist')
        });

        it('Changes the route', () => {
            cy.url().should('include', '/reports/my-reports');
        });

        it('Report criteria is no longer displayed in main panel', () => {
            reportCriteria.elements.criteriaWrapper.should('not.exist');
        });

        it('My Reports are displayed in main panel ', () => {
            myReports.elements.heading.should('exist');
        });
    });

});

function scheduleReport() {
    objectionListingReport.elements.revisionDatePicker.click();
    objectionListingReport.elements.revisionDateButton.click();
    reportCriteria.elements.scheduleReportButton.click();
}
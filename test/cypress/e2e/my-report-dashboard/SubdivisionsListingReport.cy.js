import { reportCriteria, subdivisionListingReport, myReports } from '../../model/Reports';
import "cypress-real-events";

/* Assumes the test user has access to at least one report */

describe('Subdivision listing report scheduling', { defaultCommandTimeout: 10000 }, () => {
    before(() => {
        cy.login();
        subdivisionListingReport.visit();
        cy.wait(1000);
    });

    context('Clear the Report Criteria', () => {
        before(() => {
            initializeReportData();
            subdivisionListingReport.elements.statusMultiselect
                .click()
                .type('{enter}');

            subdivisionListingReport.elements.taMultiselect
                .click()
                .type('{downarrow}{enter}');

        });

        it('Clears the criteria', () => {
            subdivisionListingReport.elements.clearReportButton.click({ force: true }).then(() => {
                subdivisionListingReport.elements.startDatePicker.find('input')
                    .should('have.value', '');
                subdivisionListingReport.elements.endDatePicker.find('input')
                    .should('have.value', '');
                subdivisionListingReport.elements.statusMultiselect.find('.multiselect__single')
                    .should('have.text', 'All');
                subdivisionListingReport.elements.taMultiselect.find('.multiselect__tag')
                    .should('include.text', 'All Territorial Authorities')
                    .and('have.length', 1);
            });
        });

    });

    context('Scheduling a Report', () => {
        before(() => {
            scheduleReport();
        });

        it('Masks the page', () => {
            reportCriteria.elements.pageMask.should('exist');
        });

        it('Disables the \'Schedule Report\' button', () => {
            reportCriteria.elements.scheduleReportButton.then(button => {
                expect(button).to.be.disabled;
            });
        });

        it('Displays a modal', () => {
            reportCriteria.elements.reportModal.should('exist')
        });
    });

    context('Clicking \'View My Reports\' in the modal', () => {
        before(() => {
            subdivisionListingReport.elements.modalCancelButton.click();
        });

        it('Hides the modal', () => {
            reportCriteria.elements.reportModal.should('not.be.visible');
        });

        it('Unmasks the page', () => {
            reportCriteria.elements.pageMask.should('not.exist')
        });

        it('Changes the route', () => {
            cy.url().should('include', '/reports/my-reports');
        });

        it('Report criteria is no longer displayed in main panel', () => {
            reportCriteria.elements.criteriaWrapper.should('not.exist');
        });

        it('My Reports are displayed in main panel ', () => {
            myReports.elements.heading.should('exist');
        });
    });

});

function scheduleReport() {
    initializeReportData();
    reportCriteria.elements.scheduleReportButton.click();
}

function initializeReportData() {
    subdivisionListingReport.elements.startDatePicker.realClick().then(() => {
        cy.wait(50);
        subdivisionListingReport.elements.startDateButton.click();
    });
    subdivisionListingReport.elements.endDatePicker.realClick().then(() => {
        cy.wait(50);
        subdivisionListingReport.elements.endDateButton.click();
    });
}

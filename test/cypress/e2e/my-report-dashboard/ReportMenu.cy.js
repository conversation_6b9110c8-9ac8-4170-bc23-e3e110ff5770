
import { dashboard, myReports, reportCriteria, reportMenu } from '../../model/Reports';
import user from '../../support/user.js';

/* Assumes the test user has access to at least one report */

describe('Report Menu', { defaultCommandTimeout: 10000 }, () => {
    context('INTERNAL USERS', () => {
        before(() => {
            cy.login();
            dashboard.visit();
        });

        context('Displays the expected elements', () => {

            it('Displays the \'View My Reports\' button', () => {
                reportMenu.elements.viewMyReportsButton.then(button => {
                    expect(button).to.exist.and.be.visible;
                    expect(button.text().toLowerCase()).to.contain('view my reports');
                });
            });

            it('Displays the category tree', () => {
                reportMenu.elements.menuWrapper.then(wrapper => {
                    expect(wrapper).to.exist;
                });

                reportMenu.elements.reportCategories.then(categories => {
                    expect(categories.length).to.be.greaterThan(0);
                });

                reportMenu.elements.reports.then(reports => {
                    expect(reports.length).to.be.greaterThan(0);
                });
            });
        });

        context('Expands and collapses', () => {
            it('Expands the tree', () => {
                let originalMenuHeight = 0;
                let newMenuHeight = 0;

                reportMenu.elements.menuWrapper.then(wrapper => {
                    originalMenuHeight = wrapper.height();
                });

                reportMenu.expand();

                reportMenu.elements.menuWrapper.then(wrapper => {
                    newMenuHeight = wrapper.height();
                    expect(newMenuHeight).to.be.greaterThan(originalMenuHeight);
                });
            });

            it('Collapses the tree', () => {
                let originalMenuHeight = 0;
                let newMenuHeight = 0;

                reportMenu.elements.menuWrapper.then(wrapper => {
                    originalMenuHeight = wrapper.height();
                });

                reportMenu.collapse();

                reportMenu.elements.menuWrapper.then(wrapper => {
                    newMenuHeight = wrapper.height();
                    expect(newMenuHeight).to.be.lessThan(originalMenuHeight);
                });
            });
        });

        context('Is scrollable', () => {
            it('Has overflow styling', () => {
                reportMenu.elements.menuWrapper.then(wrapper => {
                    expect(wrapper).to.have.css('overflow-y', 'auto');
                });
            });
        });

        context('Selecting a report displays the report criteria', () => {
            before(() => {
                reportMenu.expand();

                reportMenu.elements.reports.then(reports => {
                    reports[0].click();
                });
            });

            it('Selected report is active in the menu', () => {
                reportMenu.elements.reports.then(reports => {
                    expect(reports[0]).to.have.class('active');
                });
            });

            it('Route has changed', () => {
                cy.url().should('not.include', '/reports/my-reports');
                cy.url().should('include', '/reports/');
            });

            it('My Reports is no longer displayed in main panel', () => {
                myReports.elements.heading.should('not.exist')
            });

            it('Report criteria component is displayed in main panel ', () => {
                reportCriteria.elements.criteriaWrapper.then(wrapper => {
                    expect(wrapper).to.exist.and.be.visible;
                });
            });
        });

        context('Clicking \'View My Reports\' displays the job list', () => {
            before(() => {
                reportMenu.elements.viewMyReportsButton.click();
            });

            it('Selected report is not longer active in the menu', () => {
                reportMenu.elements.reports.then(reports => {
                    expect(reports[0]).to.not.have.class('active');
                });
            });

            it('Route has changed', () => {
                cy.url().should('include', '/reports/my-reports');
            });

            it('Report criteria is no longer displayed in main panel', () => {
                reportCriteria.elements.criteriaWrapper.should('not.exist');
            });

            it('My Reports are displayed in main panel ', () => {
                myReports.elements.heading.then(heading => {
                    expect(heading).to.exist.and.be.visible;
                });
            });
        });
    });
    context('EXTERNAL USERS', () => {
        before(() => {
            cy.visitWithUser('', user.EXTERNAL_USER);
            dashboard.visit();
        });

        context('Does not displays Report Menu', () => {

            it('Does not displays the \'View My Reports\' button', () => {
                reportMenu.elements.viewMyReportsButton.should('not.exist');
            });

            it('Does not displays the category tree', () => {
                reportMenu.elements.menuWrapper.should('not.exist');

                reportMenu.elements.reportCategories.should('not.exist');

                reportMenu.elements.reports.should('not.exist');
            });
        });
    });

    context('Cost Centre Revenue Summary', () => {
        before(() => {
            reportMenu.expand();

            reportMenu.elements.reports.then(reports => {
                reports[62].click();
            });
        });

        it('Selected report is active in the menu', () => {
            reportMenu.elements.reports.then(reports => {
                expect(reports[62]).to.have.class('active');
            });
        });

        it('Route has changed', () => {
            cy.url().should('not.include', '/reports/my-reports');
            cy.url().should('include', '/reports/');
        });

        it('My Reports is no longer displayed in main panel', () => {
            myReports.elements.heading.should('not.exist')
        });

        it('Report criteria component is displayed in main panel ', () => {
            reportCriteria.elements.criteriaWrapper.then(wrapper => {
                expect(wrapper).to.exist.and.be.visible;
            });
        });
    });
});

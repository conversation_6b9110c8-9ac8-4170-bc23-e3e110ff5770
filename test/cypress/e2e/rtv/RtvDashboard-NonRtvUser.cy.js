import RtvDashboard from '../../model/RtvDashboard';
import user from '../../support/user.js';

describe('RTV Dashboard', { defaultCommandTimeout: 10000 }, () => {
    context('Load the RTV Dashboard for non-RTV user', () => {
        it('Does not display the RTV dashboard', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_NON_RTV_USER);

            RtvDashboard.dashboard.then(dashboard => {
                expect(dashboard).to.not.exist;
            });
        });
    });
});

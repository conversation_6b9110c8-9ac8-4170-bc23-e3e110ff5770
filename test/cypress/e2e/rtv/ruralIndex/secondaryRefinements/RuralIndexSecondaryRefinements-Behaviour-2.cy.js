import RtvRuralIndexDashboard from '../../../../model/RtvRuralIndexDashboard';
import RtvRuralIndexSecondaryRefinements from '../../../../model/RtvRuralIndexSecondaryRefinements';
import numeral from 'numeral';
import user from '../../../../support/user.js';

describe('Secondary Refinements Behaviour', { defaultCommandTimeout: 3000 }, () => {

    context('Add refinement row basic field behaviours', () => {
        before(() => {
            cy.log('Loading Secondary Refinements window...');

            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();
        });

        beforeEach(() => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton)
                .scrollIntoView()
                .click();
        });

        it('New refinement no-category-placeholder does not exist when Refinement Category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementNoCategoryPlaceholder).should('not.exist');
        });

        it('New refinement no-category-placeholder does not exist when Refinement Category is \'Single Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementNoCategoryPlaceholder).should('not.exist');
        });

        it('New refinement Sales Group exists when Refinement Category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementSalesGroup)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New refinement Sales Group does not exist when Refinement Category is \'Single Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementSalesGroup).should('not.exist');
        });

        it('New refinement Sales Group is populated with options', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementSalesGroup).find('option')
                .then($els => {
                    expect($els.length).to.be.greaterThan(1);
                });
        });

        it('New refinement Sales Group is not disabled', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementSalesGroup)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New refinement Sales Group is not selected by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementSalesGroup)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New refinement Roll Number does not exist when Refinement Category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).should('not.exist');
        });

        it('New refinement Roll Number exists when Refinement Category is \'Single Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New refinement Roll Number is populated with options', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).find('option')
                .then($els => {
                    expect($els.length).to.be.greaterThan(1);
                });
        });

        it('New refinement Roll Number is not disabled', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New refinement Roll Number is not selected by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Assessment Min remains disabled when the refinement category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('New Assessment Min remains disabled when the refinement category is \'Single Roll\' but Roll Number has not been selected', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('New Assessment Min is enabled when the Roll Number has been selected', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New Assessment Min only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('New Assessment Min field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Assessment Min field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Assessment Min field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('New Assessment Min field formats values as whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .type('5678.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5679).format('0'));
                });
        });

        it('New Assessment Min field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('New Assessment Max remains disabled when the refinement category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('New Assessment Max remains disabled when the refinement category is \'Single Roll\' but Roll Number has not been selected', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('New Assessment Max is enabled when the Roll Number has been selected', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New Assessment Max only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('New Assessment Max field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Assessment Max field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Assessment Max field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('New Assessment Max field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('New Assessment Max field formats values as whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .type('5678.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5679).format('0'));
                });
        });

        it('New CV Min only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('New CV Min field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New CV Min field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New CV Min field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('New CV Min field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('New CV Min field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .type('5678.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5679).format('$0,0'));
                });
        });

        it('New CV Max only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('New CV Max field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New CV Max field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New CV Max field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('New CV Max field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('New CV Max field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });

        it('New LV Min only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('New LV Min field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New LV Min field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New LV Min field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('New LV Min field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('New LV Min field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });

        it('New LV Max only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('New LV Max field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New LV Max field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New LV Max field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('New LV Max field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('New LV Max field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });

        it('New QV Category field only accepts alphanumeric characters', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQvCategory)
                .type('1]%A2B3C4D[;`@#')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1A2B3C4D');
                });
        });

        it('New QV Category field capitalizes alphabetical characters', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQvCategory)
                .type('abcd')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('ABCD');
                });
        });

        it('New QV Category field limits input to 10 characters', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQvCategory)
                .type('1234567890_here_are_some_more_characters')
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234567890');
                    expect($value.length).to.equal(10);
                });
        });

        it('New Grouping multiselect allows you to select a value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click()
                .then($selectedOption => {
                    cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                        .find('.multiselect__tag > span')
                        .then($els => {
                            expect($els.length).to.equal(1);
                            expect($els[0].innerText.trim()).to.equal($selectedOption.text().trim());
                        });
                });
        });

        it('New Grouping multiselect allows you to select multiple values', () => {
            const selectedValues = [];

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(1)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(2)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });


            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(3)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                    for(let i = 0; i < $els.length; i++) {
                        expect($els[i].innerText.trim()).to.equal(selectedValues[i]);
                    }
                });
        });

        it('New Grouping multiselect allows you to deselect a value', () => {
            const selectedValues = [];

            // select multiple values
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(1)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(2)')
                .click()

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(3)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });

            //confirm they are selected
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                });

            // remove a selected value
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__tag:nth-child(2) > i')
                .click()

            //confirm one has been deselected
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(2);
                    for(let i = 0; i < $els.length; i++) {
                        expect($els[i].innerText.trim()).to.equal(selectedValues[i]);
                    }
                });
        });

        it('New Grouping multiselect allows you to deselect all values', () => {
            // select multiple values
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(1)')
                .click()

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(2)')
                .click()

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click('left')
                .find('.multiselect__element:nth-child(3)')
                .click()

            //confirm they are selected
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                });

            // remove all selected values
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('.multiselect__tag > i')
                .then($els => {
                    for (let i = $els.length -1; i >= 0; i--) {
                        cy.wrap($els[i]).click();
                    }
                });

            //confirm all have been deselected
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('.multiselect__tag')
                .should('not.exist')
        });

        it('New Quality Rating multiselect allows you to select a value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click()
                .then($selectedOption => {
                    cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                        .find('.multiselect__tag > span')
                        .then($els => {
                            expect($els.length).to.equal(1);
                            expect($els[0].innerText.trim()).to.equal($selectedOption.text().trim());
                        });
                });
        });

        it('New Quality Rating multiselect allows you to select multiple values', () => {
            const selectedValues = [];

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(1)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });


            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(3)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                    for(let i = 0; i < $els.length; i++) {
                        expect($els[i].innerText.trim()).to.equal(selectedValues[i]);
                    }
                });
        });

        it('New Quality Rating multiselect allows you to deselect a value', () => {
            const selectedValues = [];

            // select multiple values
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(1)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click()

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(3)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim())
                });

            //confirm they are selected
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                });

            // remove a selected value
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__tag:nth-child(2) > i')
                .click()

            //confirm one has been deselected
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(2);
                    for(let i = 0; i < $els.length; i++) {
                        expect($els[i].innerText.trim()).to.equal(selectedValues[i]);
                    }
                });
        });

        it('New Quality Rating multiselect allows you to deselect all values', () => {

            // select multiple values
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(1)')
                .click()

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click()

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(3)')
                .click()

            //confirm they are selected
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                });

            // remove all selected values
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('.multiselect__tag > i')
                .then($els => {
                    for (let i = $els.length -1; i >= 0; i--) {
                        cy.wrap($els[i]).click();
                    }
                });

            //confirm all have been deselected
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('.multiselect__tag')
                .should('not.exist')
        });


        it('New LV Percent Index only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .type('1]%a2@#b3c4d.[;5`6')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234.56');
                });
        });

        it('New LV Percent Index field resets zero value to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1.00');
                });
        });

        it('New LV Percent Index field resets negative value to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1.00');
                });
        });

        it('New LV Percent Index field rounds down to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .type('1234.5649')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234.56);
                });
        });

        it('New LV Percent Index field rounds up to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .type('1234.565')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234.57);
                });
        });

        it('New LV Percent Index field formats values to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .type('50.88888')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(50.89).format('0.00'));
                });
        });

        it('New LV Lump Sum only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('New LV Lump Sum field does not clear zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .type('0')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('0');
                });
        });

        it('New LV Lump Sum field accepts negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .type('-1234')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('-1234');
                });
        });

        it('New LV Lump Sum field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('New LV Lump Sum field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('New LV Lump Sum field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });

        it('New VI Percent Index only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .type('1]%a2@#b3c4d.[;5`6')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234.56');
                });
        });

        it('New VI Percent Index field resets zero value to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1.00');
                });
        });

        it('New VI Percent Index field resets negative value to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1.00');
                });
        });

        it('New VI Percent Index field rounds down to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .type('1234.5649')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234.56);
                });
        });

        it('New VI Percent Index field rounds up to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .type('1234.565')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234.57);
                });
        });

        it('New VI Percent Index field formats values to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .type('50.88888')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(50.89).format('0.00'));
                });
        });

        it('New VI Lump Sum only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('New VI Lump Sum field does not clear zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .type('0')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('0');
                });
        });

        it('New VI Lump Sum field accepts negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .type('-1234')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('-1234');
                });
        });

        it('New VI Lump Sum field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('New VI Lump Sum field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('New VI Lump Sum field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });
    });

});

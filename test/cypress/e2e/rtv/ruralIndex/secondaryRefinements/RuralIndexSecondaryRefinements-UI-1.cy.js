import RtvRuralIndexDashboard from '../../../../model/RtvRuralIndexDashboard';
import RtvRuralIndexSecondaryRefinements from '../../../../model/RtvRuralIndexSecondaryRefinements';
import user from '../../../../support/user.js';

describe('Secondary Refinements UI', { defaultCommandTimeout: 3000 }, () => {

    context('Default RTV Dashboard state', () => {
        it('Secondary Refinements container is not present on first page load', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).should('not.exist');
        });

        it('Secondary refinements container appears after the tab is selected', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

            cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then($el => {
                expect($el).to.exist.and.be.visible;
            });
        });
    });

    context('Header cells are sticky', () => {

        before(() => {
            cy.log('Loading Secondary Refinements window...');

            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

            // make sure there are at least 30 refinements
            cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then(window => {
                const refinementRows = window.find('tr[data-cy=secondaryRefinementsRow]');

                if (refinementRows.length < 30) {
                    for (let i = 0; i < 30 - refinementRows.length; i++) {
                        cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton)
                            .scrollIntoView()
                            .click();
                    }
                }
            });
        });

        it('Header cell is visible when scrolled to bottom of secondary refinements', () => {

            // scroll down to bottom of secondary refinements
            cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow)
                .scrollIntoView()
                .scrollTo('left')
                .scrollTo('bottom');

            cy.get(RtvRuralIndexSecondaryRefinements.header)
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

    });

    context('Secondary Refinements default state', () => {

        before(() => {
            cy.log('Loading Secondary Refinements window...');

            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();
        });

        it('Table head exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.header)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('There are three header rows', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.header).find('tr')
                .then($els => {
                    expect($els.length).to.equal(3);
                });
        });

        it('\'Sales Group & Multiple Roll\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell1)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Sales Group & Multiple Roll\' header cell has text \'Sales Group & Multiple Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell1).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('sales group & multiple roll');
            });
        });

        it('\'Single Roll & Assessment Range\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell2)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Single Roll & Assessment Range\' header cell has text \'Single Roll & Assessment Range\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell2).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('single roll & assessment range');
            });
        });

        it('\'Value Ranges\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell3)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Value Ranges\' header cell has text \'Value Ranges\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell3).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('value ranges');
            });
        });

        it('\'Category & Groupings\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell4)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Category & Groupings\' header cell has text \'Category & Groupings\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell4).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.contain('category & groupings');
            });
        });

        it('\'Land and Improvement Value Index Set\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell5)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Land and Improvement Value Index Set\' header cell has text \'Land and Improvement Value Index Set\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell5).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('land and improvement value index set');
            });
        });

        it('\'Land Index\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell7)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Land Index\' header cell has text \'Land Index\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell7).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('land index');
            });
        });

        it('\'Improvement Index\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell8)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Improvement Index\' header cell has text \'Improvement Index\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell8).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('improvement index');
            });
        });

        it('\'SG Code / Roll\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell9)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'SG Code / Roll\' header cell has text \'SG Code / Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell9).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('sg code / roll');
            });
        });

        it('\'Assessment Low\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell10)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Assessment Low\' header cell has text \'Low\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell10).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('low');
            });
        });

        it('\'Assessment High\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell11)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Assessment High\' header cell has text \'High\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell11).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('high');
            });
        });

        it('\'CV Low\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell12)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'CV Low\' header cell has text \'CV Low\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell12).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('cv low');
            });
        });

        it('\'CV High\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell13)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'CV High\' header cell has text \'CV High\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell13).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('cv high');
            });
        });

        it('\'LV Low\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell14)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'LV Low\' header cell has text \'LV Low\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell14).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('lv low');
            });
        });

        it('\'LV High\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell15)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'LV High\' header cell has text \'LV High\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell15).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('lv high');
            });
        });

        it('\'QV Category\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell16)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'QV Category\' header cell has text \'QV Category\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell16).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('qv category');
            });
        });

        it('\'Grouping\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell17)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Grouping\' header cell has text \'Grouping\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell17).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('grouping');
            });
        });

        it('\'Quality Rating\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell18)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'Quality Rating\' header cell has text \'Quality Rating\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell18).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('quality rating');
            });
        });

        it('\'LV Percent Index\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell19)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'LV Percent Index\' header cell has text \'%\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell19).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('%');
            });
        });

        it('\'LV Lump Sum Index\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell20)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'LV Lump Sum Index\' header cell has text \'Lump Sum\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell20).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('lump sum');
            });
        });

        it('\'CV Percent Index\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell21)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'CV Percent Index\' header cell has text \'%\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell21).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('%');
            });
        });

        it('\'CV Lump Sum Index\' header cell exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell22)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('\'CV Lump Sum Index\' header cell has text \'Lump Sum\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.headerCell22).then($el => {
                expect($el.text().replace(/\u00a0/g, ' ').trim().toLowerCase()).to.equal('lump sum');
            });
        });

        it('New refinement row exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRow)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New refinement row has 16 cells (one for each field)', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRow).find('td')
                .then($els => {
                    expect($els.length).to.equal(16);
                });
        });

        it('New refinement category select list exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New refinement category select list has three options', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).find('option')
                .then($els => {
                    expect($els.length).to.equal(3);
                });
        });

        it('New refinement category options are ', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).find('option')
                .then($els => {
                    expect($els.length).to.equal(3);
                });
        });

        it('New refinement category options are \'Entire TA\', \'Sales Group\', and \'Single Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).find('option')
                .then($els => {
                    expect($els[0].text.trim().toLowerCase()).to.equal('entire ta');
                    expect($els[1].text.trim().toLowerCase()).to.equal('sales group');
                    expect($els[2].text.trim().toLowerCase()).to.equal('single roll');
                });
        });

        it('New refinement category has \'Entire TA\' selected by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory)
                .invoke('val')
                .then($value => {
                    expect($value.trim().toLowerCase()).to.equal('entire ta');
                });
        });

        it('New refinement no-category-placeholder exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementNoCategoryPlaceholder)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New refinement no-category-placeholder is disabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementNoCategoryPlaceholder)
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('New refinement Sales Group does not exist when Refinement Category is \'Entire TA\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementSalesGroup).should('not.exist');
        });

        it('New refinement Roll Number does not exist when Refinement Category is \'Entire TA\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).should('not.exist');
        });

        it('New Assessment Min input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New Assessment Min input is disabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('New Assessment Min input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Assessment Max input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New Assessment Max input is disabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('New Assessment Max input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New CV Min input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New CV Min input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New CV Min input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New CV Max input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New CV Max input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New CV Max input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New LV Min input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New LV Min input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New LV Min input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New LV Max input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New LV Max input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New LV Max input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New QV Category input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQvCategory)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New QV Category input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQvCategory)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New QV Category input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQvCategory)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Grouping multiselect exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New Grouping input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('.multiselect__input')
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New Grouping input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Grouping multiselect is populated with items', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('li.multiselect__element')
                .then($els => {
                    expect($els.length).to.be.greaterThan(0);
                });
        });

        it('New Quality Rating multiselect exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New Quality Rating input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('.multiselect__input')
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New Quality Rating input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('New Quality Rating multiselect is populated with items', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('li.multiselect__element')
                .then($els => {
                    expect($els.length).to.be.greaterThan(0);
                });
        });

        it('New LV Percent Index input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New LV Percent Index input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New LV Percent Index input defaults to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1);
                });
        });

        it('New LV Lump Sum Index input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New LV Lump Sum Index input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New LV Lump Sum Index input defaults to 0', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(0);
                });
        });

        it('New VI Percent Index input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New VI Percent Index input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New VI Percent Index input defaults to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1);
                });
        });

        it('New VI Lump Sum Index input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('New VI Lump Sum Index input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('New VI Lump Sum Index input defaults to 0', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(0);
                });
        });

        it('New Refinement Add button exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });
    });

    context('Secondary refinements row default state', () => {

        before(() => {
            cy.log('Loading Secondary Refinements window...');

            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

            // add a blank refinement
            cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then(window => {
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton)
                    .scrollIntoView()
                    .click();
            });
        });

        it('Refinement row exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement row has 16 cells (one for each field)', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows)
                .last()
                .find('td')
                .then($els => {
                    expect($els.length).to.equal(16);
                });
        });

        it('Refinement category select list exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement category select list has three options', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .find('option')
                .then($els => {
                    expect($els.length).to.equal(3);
                });
        });

        it('Refinement category options are ', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .find('option')
                .then($els => {
                    expect($els.length).to.equal(3);
                });
        });

        it('Refinement category options are \'Entire TA\', \'Sales Group\', and \'Single Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .find('option')
                .then($els => {
                    expect($els[0].text.trim().toLowerCase()).to.equal('entire ta');
                    expect($els[1].text.trim().toLowerCase()).to.equal('sales group');
                    expect($els[2].text.trim().toLowerCase()).to.equal('single roll');
                });
        });

        it('Refinement category has \'Entire TA\' selected by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value.trim().toLowerCase()).to.equal('entire ta');
                });
        });

        it('Refinement no-category-placeholder exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementNoCategoryPlaceholder)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement no-category-placeholder is disabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementNoCategoryPlaceholder)
                .last()
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('Refinement Sales Group does not exist when Refinement Category is \'Entire TA\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementNewSalesGroup).should('not.exist');
        });

        it('Refinement Roll Number does not exist when Refinement Category is \'Entire TA\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementNewRollNumber).should('not.exist');
        });

        it('Refinement Assessment Min input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement Assessment Min input is disabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('Refinement Assessment Min input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Assessment Max input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement Assessment Max input is disabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('Refinement Assessment Max input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement CV Min input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement CV Min input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement CV Min input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement CV Max input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement CV Max input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement CV Max input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement LV Min input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement LV Min input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement LV Min input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement LV Max input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement LV Max input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement LV Max input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement QV Category input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQvCategory)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement QV Category input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQvCategory)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement QV Category input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQvCategory)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Grouping multiselect exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement Grouping input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('.multiselect__input')
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement Grouping input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Grouping multiselect is populated with items', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('li.multiselect__element')
                .then($els => {
                    expect($els.length).to.be.greaterThan(0);
                });
        });

        it('Refinement Quality Rating multiselect exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement Quality Rating input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('.multiselect__input')
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement Quality Rating input is blank by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Quality Rating multiselect is populated with items', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('li.multiselect__element')
                .then($els => {
                    expect($els.length).to.be.greaterThan(0);
                });
        });

        it('Refinement LV Percent Index input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement LV Percent Index input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement LV Percent Index input defaults to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1);
                });
        });

        it('Refinement LV Lump Sum Index input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement LV Lump Sum Index input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement LV Lump Sum Index input defaults to 0', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(0);
                });
        });

        it('Refinement VI Percent Index input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement VI Percent Index input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement VI Percent Index input defaults to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1);
                });
        });

        it('Refinement VI Lump Sum Index input exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement VI Lump Sum Index input is enabled by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement VI Lump Sum Index input defaults to 0', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(0);
                });
        });

        it('Refinement Remove button exists', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });
    });
});

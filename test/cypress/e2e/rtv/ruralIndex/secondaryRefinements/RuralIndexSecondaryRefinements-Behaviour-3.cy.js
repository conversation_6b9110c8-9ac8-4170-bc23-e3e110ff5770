import RtvRuralIndexDashboard from '../../../../model/RtvRuralIndexDashboard';
import RtvRuralIndexSecondaryRefinements from '../../../../model/RtvRuralIndexSecondaryRefinements';
import numeral from 'numeral';
import user from '../../../../support/user.js';

describe('Secondary Refinements Behaviour', { defaultCommandTimeout: 2000 }, () => {

    context('Adding a refinement row', () => {
        let refinementRows;

        before(() => {
            cy.log('Loading Secondary Refinements window...');

            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();
        })

        beforeEach(() => {
            cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then(window => {
                const rows = window.find('tr[data-cy=secondaryRefinementsRow]');
                refinementRows = rows.length;
            });
        })

        it('Add button adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New Assessment Min field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New Assessment Max field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New CV Min field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New CV Max field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New LV Min field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New LV Max field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New QV Category field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQvCategory)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New LV Percent Index field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New LV Lump Sum Index field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New VI Percent Index field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing enter while on the New VI Lump Sum Index field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .focus()
                .type('{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Pressing tab while on the New VI Lump Sum Index field adds a refinement row', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .focus()
                .trigger('keydown', {
                    keyCode: 9,
                    which: 9
                });

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows + 1);
            });
        });

        it('Refinement Category value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('Sales Group');
                });
        });

        it('Sales Group value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Sales Group');
            let salesGroup;
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementSalesGroup).select(1)
                .invoke('val')
                .then($value => {
                    salesGroup = $value
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementSalesGroup)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(salesGroup);
                });
        });

        it('Roll Number value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');
            let rollNumber;
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1)
                .invoke('val')
                .then($value => {
                    rollNumber = $value
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(rollNumber);
                });
        });

        it('Assessment Min value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMin)
                .focus()
                .type('123{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('123');
                });
        });

        it('Assessment Max value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCategory).select('Single Roll');
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementRollNumber).select(1);
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAssessmentMax)
                .focus()
                .type('456{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('456');
                });
        });

        it('CV Min value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMin)
                .focus()
                .type('789{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral('789').format('$0,0'));
                });
        });

        it('CV Max value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementCvMax)
                .focus()
                .type('321{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral('321').format('$0,0'));
                });
        });

        it('LV Min value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMin)
                .focus()
                .type('654{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral('654').format('$0,0'));
                });
        });

        it('LV Max value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvMax)
                .focus()
                .type('987{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral('987').format('$0,0'));
                });
        });

        it('QV Category value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQvCategory)
                .focus()
                .type('abc{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQvCategory)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('ABC');
                });
        });

        it('Grouping value is retained on new row when refinement is added', () => {
            let groupingValue;

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementGrouping)
                .find('.multiselect__tag > span')
                .then($els => {
                    groupingValue = $els[0].innerText.trim();
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(1);
                    expect($els[0].innerText.trim()).to.equal(groupingValue);
                });
        });

        it('Quality Rating value is retained on new row when refinement is added', () => {
            let qualityRatingValue;

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementQualityRating)
                .find('.multiselect__tag > span')
                .then($els => {
                    qualityRatingValue = $els[0].innerText.trim();
                });

            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(1);
                    expect($els[0].innerText.trim()).to.equal(qualityRatingValue);
                });
        });

        it('LV Percent Index value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvPercentIndex)
                .focus()
                .type('987.12{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral('987.12').format('0.00'));
                });
        });

        it('LV Lump Sum Index value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementLvLumpSumIndex)
                .focus()
                .type('123456{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral('123456').format('$0,0'));
                });
        });

        it('VI Percent Index value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViPercentIndex)
                .focus()
                .type('123.98{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral('123.98').format('0.00'));
                });
        });

        it('VI Lump Sum Index value is retained on new row when refinement is added', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementViLumpSumIndex)
                .focus()
                .type('987654{enter}');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral('987654').format('$0,0'));
                });
        });
    });

    context('Removing a refinement row', () => {
        let refinementRows;

        beforeEach(() => {
            cy.log('Loading Secondary Refinements window...');

            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

            //add some test refinements
            for(let i = 0; i < 3; i++) {
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton)
                    .scrollIntoView()
                    .click();
            }

            cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then(window => {
                const rows = window.find('tr[data-cy=secondaryRefinementsRow]');
                refinementRows = rows.length;
            });
        });

        it('Remove button removes a blank refinement with no warning message', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows - 1);
            });
        });

        it('Remove button displays a warning modal when Assessment Min is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory).last().select('Single Roll');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber).last().select(1);
            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Remove button displays a warning modal when Assessment Max is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory).last().select('Single Roll');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber).last().select(1);
            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Remove button displays a warning modal when CV Min is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Remove button displays a warning modal when CV Max is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Remove button displays a warning modal when LV Min is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Remove button displays a warning modal when LV Max is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Remove button displays a warning modal when QV Category is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQvCategory).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Remove button displays a warning modal when Grouping is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Remove button displays a warning modal when Quality Rating is not blank', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click()
                .find('.multiselect__element:nth-child(2)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modal).then($el => {
                expect($el).to.exist.and.be.visible;
            });
            cy.get(RtvRuralIndexDashboard.modalResponseCode)
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('DELETE_WARNING');
                });
        });

        it('Modal Cancel button closes the modal', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modalCancelButton).click();

            cy.get(RtvRuralIndexDashboard.modal).should('not.exist');
        });

        it('Modal Cancel button does not delete the refinement', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modalCancelButton).click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows);
            });
        });

        it('Modal Confirm button closes the modal', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modalConfirmButton).click();

            cy.get(RtvRuralIndexDashboard.modal).should('not.exist');
        });

        it('Modal Confirm button deletes the refinement', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).last().type('123');
            cy.get(RtvRuralIndexSecondaryRefinements.refinementRemoveButton).last().click();

            cy.get(RtvRuralIndexDashboard.modalConfirmButton).click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows).then($rows => {
                expect($rows.length).to.equal(refinementRows - 1);
            });
        });
    });

});

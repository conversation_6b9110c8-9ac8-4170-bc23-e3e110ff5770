import RtvRuralIndexDashboard from '../../../../model/RtvRuralIndexDashboard';
import RtvRuralIndexSecondaryRefinements from '../../../../model/RtvRuralIndexSecondaryRefinements';
import numeral from 'numeral';
import user from '../../../../support/user.js';
import getConfig from '../../../../support/config.js';

const baseUrl = getConfig('baseUrl');

describe('Secondary Refinements Behaviour', { defaultCommandTimeout: 3000 }, () => {

    context('Refinement row basic field behaviours', () => {
        before(() => {
            cy.log('Loading Secondary Refinements window...');

            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

            // make sure there are at least 1 'existing' refinement
            for (let i = 0; i < 3; i++) {
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton)
                    .scrollIntoView()
                    .click();
            }
        });

        beforeEach(() => {
            cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton)
                .scrollIntoView()
                .click();
        });

        it('Refinement no-category-placeholder does not exist when Refinement Category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows)
                .last()
                .find(RtvRuralIndexSecondaryRefinements.refinementNoCategoryPlaceholder).should('not.exist');
        });

        it('Refinement no-category-placeholder does not exist when Refinement Category is \'Single Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows)
                .last()
                .find(RtvRuralIndexSecondaryRefinements.refinementNoCategoryPlaceholder).should('not.exist');
        });

        it('Refinement Sales Group exists when Refinement Category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementSalesGroup)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement Sales Group does not exist when Refinement Category is \'Single Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows)
                .last()
                .find(RtvRuralIndexSecondaryRefinements.refinementNewSalesGroup).should('not.exist');
        });

        it('Refinement Sales Group is populated with options', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementSalesGroup)
                .last()
                .find('option')
                .then($els => {
                    expect($els.length).to.be.greaterThan(1);
                });
        });

        it('Refinement Sales Group is not disabled', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementSalesGroup)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement Sales Group is not selected by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementSalesGroup)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Roll Number does not exist when Refinement Category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRows)
                .last()
                .find(RtvRuralIndexSecondaryRefinements.refinementNewRollNumber).should('not.exist');
        });

        it('Refinement Roll Number exists when Refinement Category is \'Single Roll\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Refinement Roll Number is populated with options', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .find('option')
                .then($els => {
                    expect($els.length).to.be.greaterThan(1);
                });
        });

        it('Refinement Roll Number is not disabled', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement Roll Number is not selected by default', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Assessment Min remains disabled when the refinement category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('Refinement Assessment Min remains disabled when the refinement category is \'Single Roll\' but Roll Number has not been selected', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('Refinement Assessment Min is enabled when the Roll Number has been selected', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement Assessment Min only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last().select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('Refinement Assessment Min field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Assessment Min field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Assessment Min field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('Refinement Assessment Min field formats values as whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .type('5678.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5679).format('0'));
                });
        });

        it('Refinement Assessment Min field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMin)
                .last()
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('Refinement Assessment Max remains disabled when the refinement category is \'Sales Group\'', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('Refinement Assessment Max remains disabled when the refinement category is \'Single Roll\' but Roll Number has not been selected', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Sales Group');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .then($el => {
                    expect($el).to.be.disabled;
                });
        });

        it('Refinement Assessment Max is enabled when the Roll Number has been selected', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .then($el => {
                    expect($el).to.not.be.disabled;
                });
        });

        it('Refinement Assessment Max only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('Refinement Assessment Max field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Assessment Max field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement Assessment Max field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('Refinement Assessment Max field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('Refinement Assessment Max field formats values as whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCategory)
                .last()
                .select('Single Roll');

            cy.get(RtvRuralIndexSecondaryRefinements.refinementRollNumber)
                .last()
                .select(1);

            cy.get(RtvRuralIndexSecondaryRefinements.refinementAssessmentMax)
                .last()
                .type('5678.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5679).format('0'));
                });
        });

        it('Refinement CV Min only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('Refinement CV Min field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement CV Min field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement CV Min field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('Refinement CV Min field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('Refinement CV Min field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin)
                .last()
                .type('5678.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5679).format('$0,0'));
                });
        });

        it('Refinement CV Max only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('Refinement CV Max field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement CV Max field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement CV Max field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('Refinement CV Max field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('Refinement CV Max field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax)
                .last()
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });

        it('Refinement LV Min only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('Refinement LV Min field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement LV Min field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement LV Min field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('Refinement LV Min field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('Refinement LV Min field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMin)
                .last()
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });

        it('Refinement LV Max only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('Refinement LV Max field clears zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement LV Max field clears negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.be.empty;
                });
        });

        it('Refinement LV Max field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('Refinement LV Max field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('Refinement LV Max field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvMax)
                .last()
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });

        it('Refinement QV Category field only accepts alphanumeric characters', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQvCategory)
                .last()
                .type('1]%A2B3C4D[;`@#')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1A2B3C4D');
                });
        });

        it('Refinement QV Category field capitalizes alphabetical characters', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQvCategory)
                .last()
                .type('abcd')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('ABCD');
                });
        });

        it('Refinement QV Category field limits input to 10 characters', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQvCategory)
                .last()
                .type('1234567890_here_are_some_more_characters')
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234567890');
                    expect($value.length).to.equal(10);
                });
        });

        it('Refinement Grouping multiselect allows you to select a value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(2)')
                .click()
                .then($selectedOption => {
                    cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                        .last()
                        .find('.multiselect__tag > span')
                        .then($els => {
                            expect($els.length).to.equal(1);
                            expect($els[0].innerText.trim()).to.equal($selectedOption.text().trim());
                        });
                });
        });

        it('Refinement Grouping multiselect allows you to select multiple values', () => {
            const selectedValues = [];

            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(1)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });

            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(2)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });


            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(3)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });

            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                    for (let i = 0; i < $els.length; i++) {
                        expect($els[i].innerText.trim()).to.equal(selectedValues[i]);
                    }
                });
        });

        it('Refinement Grouping multiselect allows you to deselect a value', () => {
            const selectedValues = [];

            // select multiple values
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(1)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });

            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(2)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(3)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });

            //confirm they are selected
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                });

            // remove a selected value
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .find('.multiselect__tag:nth-child(2) > i')
                .click();

            //confirm one has been deselected
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(2);
                    for (let i = 0; i < $els.length; i++) {
                        expect($els[i].innerText.trim()).to.equal(selectedValues[i]);
                    }
                });
        });

        it('Refinement Grouping multiselect allows you to deselect all values', () => {
            // select multiple values
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(1)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(2)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(3)')
                .click();

            //confirm they are selected
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                });

            // remove all selected values
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('.multiselect__tag > i')
                .then($els => {
                    for (let i = $els.length - 1; i >= 0; i--) {
                        cy.wrap($els[i]).click();
                    }
                });

            //confirm all have been deselected
            cy.get(RtvRuralIndexSecondaryRefinements.refinementGrouping)
                .last()
                .find('.multiselect__tag')
                .should('not.exist');
        });

        it('Refinement Quality Rating multiselect allows you to select a value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(2)')
                .click()
                .then($selectedOption => {
                    cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                        .last()
                        .find('.multiselect__tag > span')
                        .then($els => {
                            expect($els.length).to.equal(1);
                            expect($els[0].innerText.trim()).to.equal($selectedOption.text().trim());
                        });
                });
        });

        it('Refinement Quality Rating multiselect allows you to select multiple values', () => {
            const selectedValues = [];

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(1)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(2)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });


            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(3)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                    for (let i = 0; i < $els.length; i++) {
                        expect($els[i].innerText.trim()).to.equal(selectedValues[i]);
                    }
                });
        });

        it('Refinement Quality Rating multiselect allows you to deselect a value', () => {
            const selectedValues = [];

            // select multiple values
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(1)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(2)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(3)')
                .click()
                .then($selectedOption => {
                    selectedValues.push($selectedOption.text().trim());
                });

            //confirm they are selected
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                });

            // remove a selected value
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .find('.multiselect__tag:nth-child(2) > i')
                .click();

            //confirm one has been deselected
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(2);
                    for (let i = 0; i < $els.length; i++) {
                        expect($els[i].innerText.trim()).to.equal(selectedValues[i]);
                    }
                });
        });

        it('Refinement Quality Rating multiselect allows you to deselect all values', () => {
            // select multiple values
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(1)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(2)')
                .click();

            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .scrollIntoView()
                .click('right')
                .find('.multiselect__element:nth-child(3)')
                .click();

            //confirm they are selected
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('.multiselect__tag > span')
                .then($els => {
                    expect($els.length).to.equal(3);
                });

            // remove all selected values
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('.multiselect__tag > i')
                .then($els => {
                    for (let i = $els.length - 1; i >= 0; i--) {
                        cy.wrap($els[i]).click();
                    }
                });

            //confirm all have been deselected
            cy.get(RtvRuralIndexSecondaryRefinements.refinementQualityRating)
                .last()
                .find('.multiselect__tag')
                .should('not.exist');
        });

        it('Refinement LV Percent Index only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .type('1]%a2@#b3c4d.[;5`6')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234.56');
                });
        });

        it('Refinement LV Percent Index field resets zero value to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1.00');
                });
        });

        it('Refinement LV Percent Index field resets negative value to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1.00');
                });
        });

        it('Refinement LV Percent Index field rounds down to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .type('1234.5649')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234.56);
                });
        });

        it('Refinement LV Percent Index field rounds up to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .type('1234.565')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234.57);
                });
        });

        it('Refinement LV Percent Index field formats values to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex)
                .last()
                .type('50.88888')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(50.89).format('0.00'));
                });
        });

        it('Refinement LV Lump Sum only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('Refinement LV Lump Sum field does not clear zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .type('0')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('0');
                });
        });

        it('Refinement LV Lump Sum field accepts negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .type('-1234')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('-1234');
                });
        });

        it('Refinement LV Lump Sum field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('Refinement LV Lump Sum field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('Refinement LV Lump Sum field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex)
                .last()
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });

        it('Refinement VI Percent Index only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .type('1]%a2@#b3c4d.[;5`6')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234.56');
                });
        });

        it('Refinement VI Percent Index field resets zero value to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .type('0')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1.00');
                });
        });

        it('Refinement VI Percent Index field resets negative value to 1.00', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .type('-1234')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1.00');
                });
        });

        it('Refinement VI Percent Index field rounds down to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .type('1234.5649')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234.56);
                });
        });

        it('Refinement VI Percent Index field rounds up to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .type('1234.565')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234.57);
                });
        });

        it('Refinement VI Percent Index field formats values to two decimal places', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex)
                .last()
                .type('50.88888')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(50.89).format('0.00'));
                });
        });

        it('Refinement VI Lump Sum only accepts numeric values', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .type('1]%a2@#b3c4d[;`')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('1234');
                });
        });

        it('Refinement VI Lump Sum field does not clear zero value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .type('0')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('0');
                });
        });

        it('Refinement VI Lump Sum field accepts negative value', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .type('-1234')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal('-1234');
                });
        });

        it('Refinement VI Lump Sum field rounds down to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .type('1234.49')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1234);
                });
        });

        it('Refinement VI Lump Sum field rounds up to nearest whole number', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .type('1234.5')
                .blur()
                .focus()
                .invoke('val')
                .then($value => {
                    expect(parseFloat($value)).to.equal(1235);
                });
        });

        it('Refinement VI Lump Sum field formats values as currency', () => {
            cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex)
                .last()
                .type('5060708.88')
                .blur()
                .invoke('val')
                .then($value => {
                    expect($value).to.equal(numeral(5060709).format('$0,0'));
                });
        });
    });

});

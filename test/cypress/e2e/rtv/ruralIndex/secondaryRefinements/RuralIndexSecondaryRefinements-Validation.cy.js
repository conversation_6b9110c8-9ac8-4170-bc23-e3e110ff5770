import RtvRuralIndexDashboard from '../../../../model/RtvRuralIndexDashboard';
import RtvRuralIndexToolbar from '../../../../model/RtvRuralIndexToolbar';
import RtvRuralIndexSecondaryRefinements from '../../../../model/RtvRuralIndexSecondaryRefinements';
import RtvRuralIndexModal from '../../../../model/RtvRuralIndexModal';
import helpers from '../../../../plugins/helpers';
import user from '../../../../support/user.js';

describe('Secondary Refinements Validation', { defaultCommandTimeout: 5000 }, () => {

    describe('When \'Save Changes\' button is clicked', () => {

        context('Throws an error modal if any fields are invalid', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('1000')
                        .blur();
                });
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('100')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'error\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('warning')).to.be.true;
                })
            });

            it('Modal has response code \'SAVE_SEC_REFINEMENTS_ERROR\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('SAVE_SEC_REFINEMENTS_ERROR');
                    });
            });

            it('Modal heading has text \'Save Secondary Refinements\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Save Secondary Refinements');
                });
            });

            it('Modal message list includes warning \'There were errors with one or more provided refinements\'', () => {
                cy.get(RtvRuralIndexModal.messageList + ' li').then($els => {
                    let hasWarning = false;
                    for (let i = 0; i < $els.length; i++) {
                        if ($els[i].innerText.trim().includes('There were errors with one or more provided refinements')) {
                            hasWarning = true;
                        }
                    }
                    expect(hasWarning).to.be.true;
                });
            });

            it('Modal cancel button does not exist', () => {
                cy.get(RtvRuralIndexModal.cancelButton).should('not.exist');
            });

            it('Modal confirm button has text \'OK\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('OK');
                });
            });

            it('Fields that cause error have class \'error\'', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('error')).to.be.true;
                });
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax).then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('error')).to.be.true;
                });
            });

            it('Fields that cause error have red outline', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(215, 69, 59) solid 3px');
                    });
                });
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax).then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(215, 69, 59) solid 3px');
                    });
                });
            });
        });

        context('Throws a warning if any percent indexes are below 0.5', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('0.49')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'warning\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('caution')).to.be.true;
                })
            });

            it('Modal has response code \'SAVE_SEC_REFINEMENTS_WARNING\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('SAVE_SEC_REFINEMENTS_WARNING');
                    });
            });

            it('Modal heading has text \'Save Main Index\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Save Secondary Refinements');
                });
            });

            it('Modal message list includes warning \'Some percent indexes are outside the range of 0.5 to 5\'', () => {
                cy.get(RtvRuralIndexModal.messageList + ' li').then($els => {
                    let hasWarning = false;
                    for (let i = 0; i < $els.length; i++) {
                        if ($els[i].innerText.trim().includes('Some percent indexes are outside the range of 0.5 to 5')) {
                            hasWarning = true;
                        }
                    }
                    expect(hasWarning).to.be.true;
                });
            });

            it('Modal cancel button has text \'No, Return to Secondary Refinements\'', () => {
                cy.get(RtvRuralIndexModal.cancelButton).then($el => {
                    expect($el.text().trim()).to.equal('No, Return to Secondary Refinements');
                });
            });

            it('Modal confirm button has text \'Yes, Save Refinements\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('Yes, Save Refinements');
                });
            });

            it('Fields that cause warning have class \'warning\'', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex).then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('warning')).to.be.true;
                });
            });

            it('Fields that cause warning have yellow outline', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex).then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(255, 179, 76) solid 3px');
                    });
                });
            });
        });

        context('Throws a warning if any percent indexes are above 5', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('5.01')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'warning\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('caution')).to.be.true;
                })
            });

            it('Modal has response code \'SAVE_SEC_REFINEMENTS_WARNING\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('SAVE_SEC_REFINEMENTS_WARNING');
                    });
            });

            it('Modal heading has text \'Save Secondary Refinements\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Save Secondary Refinements');
                });
            });

            it('Modal message list includes warning \'Some percent indexes are outside the range of 0.5 to 5\'', () => {
                cy.get(RtvRuralIndexModal.messageList + ' li').then($els => {
                    let hasWarning = false;
                    for (let i = 0; i < $els.length; i++) {
                        if ($els[i].innerText.trim().includes('Some percent indexes are outside the range of 0.5 to 5')) {
                            hasWarning = true;
                        }
                    }
                    expect(hasWarning).to.be.true;
                });
            });

            it('Modal cancel button has text \'No, Return to Secondary Refinements\'', () => {
                cy.get(RtvRuralIndexModal.cancelButton).then($el => {
                    expect($el.text().trim()).to.equal('No, Return to Secondary Refinements');
                });
            });

            it('Modal confirm button has text \'Yes, Save Refinements\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('Yes, Save Refinements');
                });
            });

            it('Fields that cause warning have class \'warning\'', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex).then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('warning')).to.be.true;
                });
            });

            it('Fields that cause warning have yellow outline', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex).then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(255, 179, 76) solid 3px');
                    });
                });
            });

        });

        context('Errors happen before warnings', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('1000')
                        .blur();
                });
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('100')
                        .blur();
                });

                cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('5.01')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'error\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('warning')).to.be.true;
                })
            });

            it('Modal has response code \'SAVE_SEC_REFINEMENTS_ERROR\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('SAVE_SEC_REFINEMENTS_ERROR');
                    });
            });

            it('Modal heading has text \'Save Secondary Refinements\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Save Secondary Refinements');
                });
            });

            it('Modal message list includes warning \'There were errors with one or more provided refinements\'', () => {
                cy.get(RtvRuralIndexModal.messageList + ' li').then($els => {
                    let hasWarning = false;
                    for (let i = 0; i < $els.length; i++) {
                        if ($els[i].innerText.trim().includes('There were errors with one or more provided refinements')) {
                            hasWarning = true;
                        }
                    }
                    expect(hasWarning).to.be.true;
                });
            });

            it('Modal cancel button does not exist', () => {
                cy.get(RtvRuralIndexModal.cancelButton).should('not.exist');
            });

            it('Modal confirm button has text \'OK\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('OK');
                });
            });

            it('Fields that cause error have class \'error\'', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('error')).to.be.true;
                });
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax).then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('error')).to.be.true;
                });
            });

            it('Fields that cause error have red outline', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMin).then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(215, 69, 59) solid 3px');
                    });
                });
                cy.get(RtvRuralIndexSecondaryRefinements.refinementCvMax).then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(215, 69, 59) solid 3px');
                    });
                });
            });

            it('Fields that cause warning have class \'warning\'', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex).then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('warning')).to.be.true;
                });
            });

            it('Fields that cause warning have yellow outline', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementViPercentIndex).then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(255, 179, 76) solid 3px');
                    });
                });
            });
        });

        context('Cancelling warning modal closes with no action', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('5.01')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.cancelButton).click();
                });
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

        });

        context('Confirming warning modal saves the refinements', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvPercentIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('5.01')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.confirmButton).click();
                });
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'success\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('success')).to.be.true;
                })
            });

            it('Modal has response code \'SAVE_SEC_REFINEMENTS_SUCCESS\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('SAVE_SEC_REFINEMENTS_SUCCESS');
                    });
            });

            it('Modal heading has text \'Saved Secondary Refinements\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Saved Secondary Refinements');
                });
            });

            it('Modal confirm button has text \'OK\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('OK');
                });
            });

        });
    });

    describe('When \'Discard Changes\' button is clicked', () => {

        context('Does not throw a warning modal if there are no changes', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                cy.get(RtvRuralIndexToolbar.discardButton).click();
            });

            it('Does not display the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

        });

        context('Throws a warning modal if there are unsaved changes', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexToolbar.discardButton).click();
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'warning\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('caution')).to.be.true;
                })
            });

            it('Modal has response code \'DISCARD_SEC_REFINEMENTS_WARNING\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('DISCARD_SEC_REFINEMENTS_WARNING');
                    });
            });

            it('Modal heading has text \'Discard Changes\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Discard Changes');
                });
            });

            it('Modal message has warning \'All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?\'', () => {
                cy.get(RtvRuralIndexModal.message).then($el => {
                    expect($el.text().trim()).to.contain('All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?')
                });
            });

            it('Modal cancel button has text \'No, Return to Secondary Refinements\'', () => {
                cy.get(RtvRuralIndexModal.cancelButton).then($el => {
                    expect($el.text().trim()).to.equal('No, Return to Secondary Refinements');
                });
            });

            it('Modal confirm button has text \'Yes, Discard Changes\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('Yes, Discard Changes');
                });
            });

        });

        context('Does not throw a warning modal if changes have been saved', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)                                     // make a change
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();                    // save the change

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'SAVE_SEC_REFINEMENTS_WARNING') {
                                cy.get(RtvRuralIndexModal.confirmButton).click()
                            }
                        });
                })
                .then(() => {
                    cy.get(RtvRuralIndexModal.modal)
                })
                .then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'SAVE_SEC_REFINEMENTS_SUCCESS') {
                                cy.get(RtvRuralIndexModal.confirmButton)
                                    .click()                                                    // dimiss the save success modal
                                    .then(() => {
                                        cy.get(RtvRuralIndexDashboard.loadingSpinner)
                                            .should('not.exist')
                                            .then(() => {
                                                cy.get(RtvRuralIndexToolbar.discardButton).click();
                                            });                                                 // click discard changes button
                                    });
                            }
                        });
                });


            });

            it('Does not display the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

        });

        context('Cancelling warning modal does not discard the changes', () => {
            let newValue;

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexToolbar.discardButton).click();

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'DISCARD_SEC_REFINEMENTS_WARNING') {
                                cy.get(RtvRuralIndexModal.cancelButton).click()
                            }
                        });
                })
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('Persists the changes', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            expect(parseFloat(val)).to.equal(newValue);
                        });
                });
            });
        });

        context('Confirming warning modal discards the changes', () => {
            let oldValue;

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then(window => {
                    const buttons = window.find(RtvRuralIndexSecondaryRefinements.refinementRemoveButton);
                    oldValue = buttons.length;
                });

                // add 5 refinements
                for(let i = 0; i < 5; i++) {
                    cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();
                }

                cy.get(RtvRuralIndexToolbar.discardButton).click();

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'DISCARD_SEC_REFINEMENTS_WARNING') {
                                cy.get(RtvRuralIndexModal.confirmButton).click()
                            }
                        });
                })
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('Discards the the changes', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then(window => {
                    const buttons = window.find(RtvRuralIndexSecondaryRefinements.refinementRemoveButton);
                    expect(buttons.length).to.equal(oldValue);
                });
            });
        });

    });

    describe('When selected TA is changed', () => {

        context('Does not throw a warning modal if there are no changes', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                cy.wait(1000);

                cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist').then(() => {
                    cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                });
            });

            it('Does not display the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('TA changed successfully', () => {
                cy.get(RtvRuralIndexDashboard.taSelector).find(':selected').then($selectedItem => {
                    cy.get(RtvRuralIndexDashboard.taSelector + ' option').each(($option, index) => {
                        if ($option.text() === $selectedItem.text()) {
                            expect(index).to.equal(2);
                        }
                    })
                });
            });

        });

        context('Throws a warning modal if there are unsaved changes', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                // clear all existing refinements
                cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then(window => {
                    const buttons = window.find(RtvRuralIndexSecondaryRefinements.refinementRemoveButton);
                    if (buttons && buttons.length) {
                        for(let i = buttons.length - 1; i >= 0; i--) {
                            cy.wrap(buttons[i]).click();
                            cy.wait(100);

                            RtvRuralIndexDashboard.ruralIndexDashboard.then(dashboard => {
                                const modal = dashboard.find(RtvRuralIndexModal.modal);
                                if (modal && modal.length > 0) {
                                    cy.get(RtvRuralIndexModal.confirmButton).click();
                                }
                            })
                        }
                    }
                });

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist').then(() => {
                    cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                });
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'warning\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('caution')).to.be.true;
                })
            });

            it('Modal has response code \'DISCARD_SEC_REFINEMENTS_WARNING\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('DISCARD_SEC_REFINEMENTS_WARNING');
                    });
            });

            it('Modal heading has text \'Discard Changes\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Discard Changes');
                });
            });

            it('Modal message has warning \'All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?\'', () => {
                cy.get(RtvRuralIndexModal.message).then($el => {
                    expect($el.text().trim()).to.contain('All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?')
                });
            });

            it('Modal cancel button has text \'No, Return to Secondary Refinements\'', () => {
                cy.get(RtvRuralIndexModal.cancelButton).then($el => {
                    expect($el.text().trim()).to.equal('No, Return to Secondary Refinements');
                });
            });

            it('Modal confirm button has text \'Yes, Discard Changes\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('Yes, Discard Changes');
                });
            });

        });

        context('Does not throw a warning modal if changes have been saved', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementViLumpSumIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)                                     // make a change
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();                    // save the change

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'SAVE_SEC_REFINEMENTS_WARNING') {
                                cy.get(RtvRuralIndexModal.confirmButton).click()
                            }
                        });
                })
                .then(() => {
                    cy.get(RtvRuralIndexModal.modal)
                })
                .then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'SAVE_SEC_REFINEMENTS_SUCCESS') {
                                cy.get(RtvRuralIndexModal.confirmButton)
                                    .click()                                                    // dimiss the save success modal
                                    .then(() => {
                                        cy.get(RtvRuralIndexDashboard.loadingSpinner)
                                            .should('not.exist')
                                            .then(() => {
                                                cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                                            });                                                 // click discard changes button
                                    });
                            }
                        });
                });

            });

            it('Does not display the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('TA changed successfully', () => {
                cy.get(RtvRuralIndexDashboard.taSelector).find(':selected').then($selectedItem => {
                    cy.get(RtvRuralIndexDashboard.taSelector + ' option').each(($option, index) => {
                        if ($option.text() === $selectedItem.text()) {
                            expect(index).to.equal(2);
                        }
                    })
                });
            });

        });

        context('Cancelling warning modal does not discard the changes', () => {
            let newValue;

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();

                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist').then(() => {
                    cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                });

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'DISCARD_SEC_REFINEMENTS_WARNING') {
                                cy.get(RtvRuralIndexModal.cancelButton).click()
                            }
                        });
                })
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('Persists the changes', () => {
                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            expect(parseFloat(val)).to.equal(newValue);
                        });
                });
            });

            it('TA did not change', () => {
                cy.get(RtvRuralIndexDashboard.taSelector).find(':selected').then($selectedItem => {
                    cy.get(RtvRuralIndexDashboard.taSelector + ' option').each(($option, index) => {
                        if ($option.text() === $selectedItem.text()) {
                            expect(index).to.equal(1);
                        }
                    })
                });
            });
        });

        context('Confirming warning modal switches the TA', () => {

            before(() => {
                cy.log('Loading Secondary Refinements window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);
                cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

                clearRefinements();
                // add a refinement
                cy.get(RtvRuralIndexSecondaryRefinements.newRefinementAddButton).click();

                cy.get(RtvRuralIndexSecondaryRefinements.refinementLvLumpSumIndex).then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist').then(() => {
                    cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                });

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'DISCARD_SEC_REFINEMENTS_WARNING') {
                                cy.get(RtvRuralIndexModal.confirmButton).click()
                            }
                        });
                })
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('TA changed successfully', () => {
                cy.get(RtvRuralIndexDashboard.taSelector).find(':selected').then($selectedItem => {
                    cy.get(RtvRuralIndexDashboard.taSelector + ' option').each(($option, index) => {
                        if ($option.text() === $selectedItem.text()) {
                            expect(index).to.equal(2);
                        }
                    })
                });
            });
        });

    });

});

function clearRefinements() {
    // clear all existing refinements
    cy.get(RtvRuralIndexSecondaryRefinements.secondaryRefinementsWindow).then(window => {
        const buttons = window.find(RtvRuralIndexSecondaryRefinements.refinementRemoveButton);
        if (buttons && buttons.length) {
            for(let i = buttons.length - 1; i >= 0; i--) {
                cy.wrap(buttons[i]).click();
                cy.wait(100);

                RtvRuralIndexDashboard.ruralIndexDashboard.then(dashboard => {
                    const modal = dashboard.find(RtvRuralIndexModal.modal);
                    if (modal && modal.length > 0) {
                        cy.get(RtvRuralIndexModal.confirmButton).click();
                    }
                })
            }
        }
    });
}

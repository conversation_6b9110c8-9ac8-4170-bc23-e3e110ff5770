
import RtvRuralIndexDashboard from '../../../model/RtvRuralIndexDashboard';
import RtvRuralIndexToolbar from '../../../model/RtvRuralIndexToolbar';
import user from '../../../support/user.js';

describe('RTV Toolbar', { defaultCommandTimeout: 2000 }, () => {
    context('Toolbar elements all exist', () => {

        it('Toolbar is not present on first page load', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexToolbar.toolbar).should('not.exist');
        });

        it('Toolbar appears after a TA is selected', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.toolbar).should('exist').and('be.visible');
        });

        it('Toolbar message', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.toolbarMessage).should('exist');
        });

        it('Toolbar message is empty on first load', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.toolbarMessage).then($el => {
                expect($el.text().trim()).to.be.empty;
            });
        });

        it('Save button', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.saveButton).should('exist').and('be.visible');
        });

        it('Save button has text \'Save Changes\'', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.saveButton).then($el => {
                expect($el.text().trim()).to.equal('Save Changes');
            });
        });

        it('Discard button', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.discardButton).should('exist').and('be.visible');
        });

        it('Discard has text \'Discard\'', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.discardButton).then($el => {
                expect($el.text().trim()).to.equal('Discard');
            });
        });

    });

    context('Toolbar is fixed to bottom of page', () => {

        it('Has css position: fixed', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.toolbar).invoke('css','position').then($css => {
                expect($css).to.equal('fixed');
            });
        });

        it('Has css bottom: 0', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexToolbar.toolbar).invoke('css','bottom').then($css => {
                expect($css).to.equal('0px');
            });
        });

    });
});

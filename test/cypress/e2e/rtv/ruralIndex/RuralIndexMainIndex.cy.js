import RtvRuralIndexDashboard from '../../../model/RtvRuralIndexDashboard';
import RtvRuralIndexToolbar from '../../../model/RtvRuralIndexToolbar';
import RtvRuralIndexMainIndex from '../../../model/RtvRuralIndexMainIndex';
import RtvRuralIndexModal from '../../../model/RtvRuralIndexModal';
import numeral from 'numeral';
import user from '../../../support/user.js';

describe('Main Index UI', { defaultCommandTimeout: 2000 }, () => {
    const categories = [
        { code:'P', description:'Pastoral' },
        { code:'D', description:'Dairy' },
        { code:'S', description:'Specialist Livestock' },
        { code:'H', description:'Horticulture' },
        { code:'F', description:'Forestry' },
        { code:'A', description:'Arable' },
    ];

    context('Default dashboard state', () => {
        it('Main index container is not present on first page load', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexMainIndex.mainIndexWindow).should('not.exist');
        });

        it('Main index container appears after a TA is selected', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.mainIndexWindow).then($el => {
                expect($el).to.exist.and.be.visible;
            });
        });
    });

    context('Header cells are present', () => {

        before(() => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.mainBodyCells);
        })

        // check each expected cell exists and is present
        it('Left header', () => {
            cy.get(RtvRuralIndexMainIndex.leftHeader).then($el => {
                expect($el).to.exist.and.be.visible;
            });
        });

        it('Location header cell', () => {
            cy.get(RtvRuralIndexMainIndex.locationHeaderCell).then($el => {
                expect($el).to.exist.and.be.visible;
            });
        });

        it('Location header cell has text \'LOCATION\'', () => {
            cy.get(RtvRuralIndexMainIndex.locationHeaderCell).then($el => {
                expect($el.text().trim()).to.equal('LOCATION');
            });
        });

        it('Sales Group header cell', () => {
            cy.get(RtvRuralIndexMainIndex.salesGroupHeaderCell).then($el => {
                expect($el).to.exist.and.be.visible;
            });
        });

        it('Sales Group header cell has text \'SALES GROUP\'', () => {
            cy.get(RtvRuralIndexMainIndex.salesGroupHeaderCell)
                .then($el => {
                    expect($el.text().trim()).to.equal('SALES GROUP');
                });
        });

        it('Sales Group Code header cell', () => {
            cy.get(RtvRuralIndexMainIndex.salesGroupCodeHeaderCell).then($el => {
                expect($el).to.exist.and.be.visible;
            });
        });

        it('Sales Group Code header cell has text \'SG CODE\'', () => {
            cy.get(RtvRuralIndexMainIndex.salesGroupCodeHeaderCell)
                .then($el => {
                    expect($el.text().trim()).to.equal('SG CODE');
                });
        });

        it('Roll header cell', () => {
            cy.get(RtvRuralIndexMainIndex.rollHeaderCell).then($el => {
                expect($el).to.exist.and.be.visible;
            });
        });

        it('Roll header cell has text \'ROLL\'', () => {
            cy.get(RtvRuralIndexMainIndex.rollHeaderCell)
                .then($el => {
                    expect($el.text().trim()).to.equal('ROLL');
                });
        });

        it('Right header', () => {
            cy.get(RtvRuralIndexMainIndex.rightHeader)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Title header cell', () => {
            cy.get(RtvRuralIndexMainIndex.titleHeaderCell)
                .scrollIntoView()
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Title header cell has text \'LAND AND IMPROVEMENT VALUE INDEX SET\'', () => {
            cy.get(RtvRuralIndexMainIndex.titleHeaderCell)
                .then($el => {
                    expect($el.text().trim()).to.equal('LAND AND IMPROVEMENT VALUE INDEX SET');
                });
        });

        const scrollIncrement = 260;

        for(let i = 0; i < categories.length; i++) {

            it('Category ' + categories[i].description + ' header cell', () => {
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow).scrollTo(scrollIncrement * i, 0);

                cy.get(RtvRuralIndexMainIndex.categoryHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el).to.exist.and.be.visible;
                    });
            });

            it('Category ' + categories[i].description + ' header cell has text \'' + categories[i].description + '\'', () => {
                cy.get(RtvRuralIndexMainIndex.categoryHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el.text().trim()).to.equal(categories[i].description);
                    });
            });

            it('Category ' + categories[i].description + ' Land Index header cell', () => {
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow).scrollTo(scrollIncrement * i, 0);

                cy.get(RtvRuralIndexMainIndex.categoryLandIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el).to.exist.and.be.visible;
                    });
            });

            it('Category ' + categories[i].description + ' Land Index header cell has text \'LAND INDEX\'', () => {
                cy.get(RtvRuralIndexMainIndex.categoryLandIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el.text().trim()).to.equal('LAND INDEX');
                    });
            });

            it('Category ' + categories[i].description + ' Improvement Index header cell', () => {
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow).scrollTo(scrollIncrement * i, 0);

                cy.get(RtvRuralIndexMainIndex.categoryImprovementIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el).to.exist.and.be.visible;
                    });
            });

            it('Category ' + categories[i].description + ' Improvement Index header cell has text \'VI INDEX\'', () => {
                cy.get(RtvRuralIndexMainIndex.categoryImprovementIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el.text().trim()).to.equal('VI INDEX');
                    });
            });

            it('Category ' + categories[i].description + ' Land Percent Index header cell', () => {
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow).scrollTo(scrollIncrement * i, 0);

                cy.get(RtvRuralIndexMainIndex.categoryLandPercentIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el).to.exist.and.be.visible;
                    });
            });

            it('Category ' + categories[i].description + ' Land Percent Index header cell has text \'%\'', () => {
                cy.get(RtvRuralIndexMainIndex.categoryLandPercentIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el.text().trim()).to.equal('%');
                    });
            });

            it('Category ' + categories[i].description + ' Land Lump Sum Index header cell', () => {
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow).scrollTo(scrollIncrement * i, 0);

                cy.get(RtvRuralIndexMainIndex.categoryLandLumpSumIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el).to.exist.and.be.visible;
                    });
            });

            it('Category ' + categories[i].description + ' Land Lump Sum Index header cell has text \'LUMP SUM\'', () => {
                cy.get(RtvRuralIndexMainIndex.categoryLandLumpSumIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el.text().trim()).to.equal('LUMP SUM');
                    });
            });

            it('Category ' + categories[i].description + ' Improvement Percent Index header cell', () => {
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow).scrollTo(scrollIncrement * i, 0);

                cy.get(RtvRuralIndexMainIndex.categoryImprovementPercentIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el).to.exist.and.be.visible;
                    });
            });

            it('Category ' + categories[i].description + ' Improvement Percent Index header cell has text \'%\'', () => {
                cy.get(RtvRuralIndexMainIndex.categoryImprovementPercentIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el.text().trim()).to.equal('%');
                    });
            });

            it('Category ' + categories[i].description + ' Improvement Lump Sum Index header cell', () => {
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow).scrollTo(scrollIncrement * i, 0);

                cy.get(RtvRuralIndexMainIndex.categoryImprovementLumpSumIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el).to.exist.and.be.visible;
                    });
            });

            it('Category ' + categories[i].description + ' Improvement Lump Sum Index header cell has text \'LUMP SUM\'', () => {
                cy.get(RtvRuralIndexMainIndex.categoryImprovementLumpSumIndexHeaderCell.format(categories[i].code))
                    .then($el => {
                        expect($el.text().trim()).to.equal('LUMP SUM');
                    });
            });
        }
    });

    context('Header cells are sticky', () => {

        it('Title header cell is visible when scrolled to bottom of main index', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            // force cypress to wait for the body cells to load (so the main index has height)
            cy.get(RtvRuralIndexMainIndex.mainBodyCells).then(() => {
                cy.wait(500);
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow)
                    .scrollIntoView()
                    .scrollTo('bottom');
            });

            cy.get(RtvRuralIndexMainIndex.titleHeaderCell)
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });

        it('Left columns are visible when scrolled to right of main index', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            // force cypress to wait for the body cells to load (so the main index has height)
            cy.get(RtvRuralIndexMainIndex.mainBodyCells).then(() => {
                cy.wait(500);
                // scroll across to make element visible
                cy.get(RtvRuralIndexMainIndex.mainIndexWindow)
                    .scrollIntoView()
                    .scrollTo('right');
            });

            cy.get(RtvRuralIndexMainIndex.locationHeaderCell)
                .then($el => {
                    expect($el).to.exist.and.be.visible;
                });
        });
    });

    context('Roll list', () => {
        it('Is populated with a list of rolls', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.leftBody).find('tr').then($els => {
                expect($els.length).to.be.greaterThan(0);
            });
        });

        it('Each row has three columns', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.leftBody).find('tr').then($rows => {
                for(let i = 0; i < $rows.length; i++) {
                    cy.wrap($rows[i]).find('td').then($cells => {
                        expect($cells.length).to.equal(3);
                    });
                }
            });
        });

        it('Is read only (no input fields)', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.leftBody).find('input').should('not.exist');
        });

        it('Is sorted by \'Sales Group Code\' then \'Roll Number\'', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            // extract the array from the DOM
            const extractedArray = [];
            const sortedArray = [];

            cy.get(RtvRuralIndexMainIndex.leftBody).find('tr').then($rows => {
                for(let i = 0; i < $rows.length; i++) {
                    cy.wrap($rows[i]).find('td').then($cells => {
                        const roll = {
                            index: i,
                            salesGroupCode: parseInt($cells[1].textContent.trim()),
                            rollNumber: parseInt($cells[2].textContent.trim())
                        }
                        extractedArray.push(roll);
                        sortedArray.push(roll);
                    });
                }
            }).then(() => {
                // sort the array
                sortedArray.sort(((a, b) => (a.salesGroupCode > b.salesGroupCode) ? 1 : (a.rollNumber > b.rollNumber) ? 1 : -1));

                // compare the sorted array to the extracted array
                expect(extractedArray).to.deep.equal(sortedArray);
            });
        });
    });

    context('Main index', () => {
        it('Is populated with rows', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.mainBody).find('tr').then($els => {
                expect($els.length).to.be.greaterThan(0);
            });
        });

        it('Has a row for each roll', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.leftBody).find('tr').then($els => {
                return $els.length
            }).then(rollCount => {
                cy.get(RtvRuralIndexMainIndex.mainBody).find('tr').then($els => {
                    expect($els.length).to.be.equal(rollCount);
                });
            });
        });

        it('Has ' + (4 * categories.length) + ' columns in each row', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.mainBody).find('tr').then($rows => {
                for(let i = 0; i < $rows.length; i++) {
                    cy.wrap($rows[i]).find('td').then($cells => {
                        expect($cells.length).to.equal(4 * categories.length);
                    });
                }
            });
        });

        it('Has an input field in each cell', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.mainBody).find('td').then($cells => {
                for(let i = 0; i < $cells.length; i++) {
                    cy.wrap($cells[i]).find('input').then($inputs => {
                        expect($inputs.length).to.equal(1);
                    });
                }
            });
        });

        it('Has no rows that are completely disabled', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.mainBody).find('tr').then($rows => {
                for(let i = 0; i < $rows.length; i++) {
                    cy.wrap($rows[i]).find('input:disabled').then($inputs => {
                        // must be less disabled inputs than there are cells
                        expect($inputs.length).to.be.lessThan(4 * categories.length);
                    });
                }
            });
        });

        it('Disabled cells have empty value (appear blank)', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.mainBody).find('input:disabled').then($inputs => {
                for(let i = 0; i < $inputs.length; i++) {
                    expect($inputs[i].value).to.be.empty;
                }
            });
        });

        it('Disabled cells are shaded grey', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // select Christchurch City - which has a higher number of Rolls
            cy.get(RtvRuralIndexDashboard.taSelector).select('60 - Christchurch City');

            cy.get(RtvRuralIndexMainIndex.mainBody).find('input:disabled').then($inputs => {
                for(let i = 0; i < $inputs.length; i++) {
                    cy.wrap($inputs[i]).invoke('css','background-color').then($css => {
                        expect($css).to.equal('rgba(0, 0, 0, 0.15)');
                    });
                }
            });
        });
    });
});

describe('Main Index Functionality', { defaultCommandTimeout: 2000 }, () => {

    context('Index fields default behaviour', () => {

        it('Land Percent field defaults to 1 when cleared', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal('1.00');
                    });
            });
        });

        it('Land Percent field ignores negative values (resets to default)', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type('-23')
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal('1.00');
                    });
            });
        });

        it('Land Percent field rounds to 2 decimal places', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const inputValue = 1.119;

            cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type('1.119')
                    .blur()
                    .then(() => {
                        // field displays rounded to 2 decimal places
                        expect($inputs[0].value).to.equal(inputValue.toFixed(2));
                    })
                    .focus()
                    .then(() => {
                        // re-focus field to ensure assigned value is not simply rounded when displayed
                        expect($inputs[0].value).to.equal(inputValue.toFixed(2));
                    });
            });
        });

        it('Land Lump Sum field defaults to $0 when cleared', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal('$0');
                    });
            });
        });

        it('Land Lump Sum field accepts negative values', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const inputValue = -68773;

            cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type(inputValue)
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal(numeral(inputValue).format('$0,0'));
                    });
            });
        });

        it('Land Lump Sum field displays value in currency format', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const inputValue = 2571644.04971;

            cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type(inputValue)
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal(numeral(inputValue).format('$0,0'));
                    });
            });
        });

        it('Land Lump Sum field rounds to the nearest whole number', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const inputValue = 982346.71395;

            cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type(inputValue)
                    .blur()
                    .then(() => {
                        // field displays rounded to 2 decimal places
                        expect($inputs[0].value).to.equal(numeral(inputValue).format('$0,0'));
                    })
                    .focus()
                    .then(() => {
                        // re-focus field to ensure assigned value is not simply rounded when displayed
                        expect($inputs[0].value).to.equal(inputValue.toFixed(0));
                    });
            });
        });

        it('Improvement Percent field defaults to 0 when cleared', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal('1.00');
                    });
            });
        });

        it('Improvement Percent field ignores negative values (resets to default)', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type('-23')
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal('1.00');
                    });
            });
        });

        it('Improvement Percent field rounds to 2 decimal places', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const inputValue = 1.558;

            cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type(inputValue)
                    .blur()
                    .then(() => {
                        // field displays rounded to 2 decimal places
                        expect($inputs[0].value).to.equal(inputValue.toFixed(2));
                    })
                    .focus()
                    .then(() => {
                        // re-focus field to ensure assigned value is not simply rounded when displayed
                        expect($inputs[0].value).to.equal(inputValue.toFixed(2));
                    });
            });
        });

        it('Improvement Lump Sum field defaults to 1 when cleared', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal('$0');
                    });
            });
        });

        it('Improvement Lump Sum field accepts negative values', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const inputValue = -24622;

            cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type(inputValue)
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal(numeral(inputValue).format('$0,0'));
                    });
            });
        });

        it('Improvement Lump Sum field displays value in currency format', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const inputValue = 18654322.05621;

            cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type(inputValue)
                    .blur()
                    .then(() => {
                        expect($inputs[0].value).to.equal(numeral(inputValue).format('$0,0'));
                    });
            });
        });

        it('Improvement Lump Sum field rounds to the nearest whole number', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const inputValue = 250133.652223;

            cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .clear()
                    .type(inputValue)
                    .blur()
                    .then(() => {
                        // field displays rounded to 2 decimal places
                        expect($inputs[0].value).to.equal(numeral(inputValue).format('$0,0'));
                    })
                    .focus()
                    .then(() => {
                        // re-focus field to ensure assigned value is not simply rounded when displayed
                        expect($inputs[0].value).to.equal(inputValue.toFixed(0));
                    });
            });
        });


    });

    context('Toolbar message', () => {

        it('Displays a message when an index cell is focused', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0])
                    .focus();
            }).then(() => {
                cy.get(RtvRuralIndexToolbar.toolbarMessage).then($el => {
                    expect($el.text().trim()).to.not.be.empty;
                });
            });
        });

        it('Message does not change when a cell in the same category and roll is focused', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            let messageText = '';

            cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0]).focus()
            })
            .then(() => {
                cy.get(RtvRuralIndexToolbar.toolbarMessage).then($el => {
                    messageText = $el.text().trim();
                });
            })
            .then(() => {
                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0]).focus()
                })
                .then(() => {
                    cy.get(RtvRuralIndexToolbar.toolbarMessage).then($el => {
                        expect($el.text().trim()).to.equal(messageText);
                    });
                });
            });
        });

        it('Message changes when a cell in another category or roll is focused', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            let messageText = '';

            cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                cy.wrap($inputs[0]).focus()
            })
            .then(() => {
                cy.get(RtvRuralIndexToolbar.toolbarMessage).then($el => {
                    messageText = $el.text().trim();
                });
            })
            .then(() => {
                cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[1]).focus()
                })
                .then(() => {
                    cy.get(RtvRuralIndexToolbar.toolbarMessage).then($el => {
                        expect($el.text().trim()).to.not.equal(messageText);
                    });
                });
            });
        });
    });

    context('Loading behaviour', () => {

        it('Saved indices are loaded from the database', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            const lvPercentValue = 0.95;
            const lvLumpSumValue = 37164.00;
            const viPercentValue = 1.23;
            const viLumpSumValue = 65952.00;

            // alter values and save them
            cy.get(RtvRuralIndexMainIndex.mainIndexWindow).then(() => {
                cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type(lvPercentValue)
                        .blur()
                });
                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type(lvLumpSumValue)
                        .blur()
                });
                cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type(viPercentValue)
                        .blur()
                });
                cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type(viLumpSumValue)
                        .blur()
                })
            })
            .then(() => {
                // save the index
                cy.get(RtvRuralIndexToolbar.saveButton).click()
                .then(() => {
                    cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist');
                })
            })
            .then(() => {
                // reload index and ensure the saved values are loaded.
                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                    expect($inputs[0].value).to.equal(lvPercentValue.toFixed(2));
                });
                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    expect($inputs[0].value).to.equal(numeral(lvLumpSumValue).format('$0,0'));
                });
                cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                    expect($inputs[0].value).to.equal(viPercentValue.toFixed(2));
                });
                cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                    expect($inputs[0].value).to.equal(numeral(viLumpSumValue).format('$0,0'));
                })
            });

            //reset to default values
            cy.get(RtvRuralIndexMainIndex.mainIndexWindow).then(() => {
                cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .blur()
                });
                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .blur()
                });
                cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .blur()
                });
                cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .blur()
                })
            })
            .then(() => {
                // save the index
                cy.get(RtvRuralIndexToolbar.saveButton).click()
                .then(() => {
                    cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist');
                })
            })
        });

    });
});

describe('Main Index Validation', { defaultCommandTimeout: 5000 }, () => {

    context.skip('Errors happen before warnings', () => {
        // skip as not sure how to force invalid value via cypress to throw an error
    });

    describe('When \'Save Changes\' button is clicked', () => {

        context.skip('Throws an error modal if any fields are invalid', () => {
            // skip as not sure how to force invalid value via cypress to throw an error
        });

        context('Throws a warning if any percent indexes are below 0.5', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('0.49')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'warning\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('caution')).to.be.true;
                })
            });

            it('Modal has response code \'SAVE_MAIN_INDEX_WARNING\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('SAVE_MAIN_INDEX_WARNING');
                    });
            });

            it('Modal heading has text \'Save Main Index\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Save Main Index');
                });
            });

            it('Modal message list includes warning \'Some percent indexes are outside the range of 0.5 to 5\'', () => {
                cy.get(RtvRuralIndexModal.messageList + ' li').then($els => {
                    let hasWarning = false;
                    for (let i = 0; i < $els.length; i++) {
                        if ($els[i].innerText.trim().includes('Some percent indexes are outside the range of 0.5 to 5')) {
                            hasWarning = true;
                        }
                    }
                    expect(hasWarning).to.be.true;
                });
            });

            it('Modal cancel button has text \'No, Return to Main Index\'', () => {
                cy.get(RtvRuralIndexModal.cancelButton).then($el => {
                    expect($el.text().trim()).to.equal('No, Return to Main Index');
                });
            });

            it('Modal confirm button has text \'Yes, Save Main Index\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('Yes, Save Main Index');
                });
            });

            it('Fields that cause warning have class \'warning\'', () => {
                cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('warning')).to.be.true;
                });
            });

            it('Fields that cause warning have yellow outline', () => {
                cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(255, 179, 76) solid 3px');
                    });
                });
            });

        });

        context('Throws a warning if any percent indexes are above 5', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('5.01')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'warning\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('caution')).to.be.true;
                })
            });

            it('Modal has response code \'SAVE_MAIN_INDEX_WARNING\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('SAVE_MAIN_INDEX_WARNING');
                    });
            });

            it('Modal heading has text \'Save Main Index\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Save Main Index');
                });
            });

            it('Modal message list includes warning \'Some percent indexes are outside the range of 0.5 to 5\'', () => {
                cy.get(RtvRuralIndexModal.messageList + ' li').then($els => {
                    let hasWarning = false;
                    for (let i = 0; i < $els.length; i++) {
                        if ($els[i].innerText.trim().includes('Some percent indexes are outside the range of 0.5 to 5')) {
                            hasWarning = true;
                        }
                    }
                    expect(hasWarning).to.be.true;
                });
            });

            it('Modal cancel button has text \'No, Return to Main Index\'', () => {
                cy.get(RtvRuralIndexModal.cancelButton).then($el => {
                    expect($el.text().trim()).to.equal('No, Return to Main Index');
                });
            });

            it('Modal confirm button has text \'Yes, Save Main Index\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('Yes, Save Main Index');
                });
            });

            it('Fields that cause warning have class \'warning\'', () => {
                cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                    expect(Array.from($inputs[0].classList).includes('warning')).to.be.true;
                });
            });

            it('Fields that cause warning have yellow outline', () => {
                cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0]).invoke('css','outline').then($css => {
                        expect($css).to.equal('rgb(255, 179, 76) solid 3px');
                    });
                });
            });

        });

        context('Cancelling warning modal closes with no action', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('5.01')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.cancelButton).click();
                });
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

        });

        context('Confirming warning modal saves the index', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.viPercentInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .clear()
                        .type('5.01')
                        .blur();
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.confirmButton).click();
                });
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'success\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('success')).to.be.true;
                })
            });

            it('Modal has response code \'SAVE_MAIN_INDEX_SUCCESS\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('SAVE_MAIN_INDEX_SUCCESS');
                    });
            });

            it('Modal heading has text \'Saved Main Index\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Saved Main Index');
                });
            });

            it('Modal confirm button has text \'OK\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('OK');
                });
            });

        });
    });

    describe('When \'Discard Changes\' button is clicked', () => {

        context('Does not throw a warning modal if there are no changes', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexToolbar.discardButton).click();
            });

            it('Does not display the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

        });

        context('Throws a warning modal if there are unsaved changes', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexToolbar.discardButton).click();
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'warning\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('caution')).to.be.true;
                })
            });

            it('Modal has response code \'DISCARD_MAIN_INDEX_WARNING\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('DISCARD_MAIN_INDEX_WARNING');
                    });
            });

            it('Modal heading has text \'Discard Changes\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Discard Changes');
                });
            });

            it('Modal message has warning \'All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?\'', () => {
                cy.get(RtvRuralIndexModal.message).then($el => {
                    expect($el.text().trim()).to.contain('All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?')
                });
            });

            it('Modal cancel button has text \'No, Return to Main Index\'', () => {
                cy.get(RtvRuralIndexModal.cancelButton).then($el => {
                    expect($el.text().trim()).to.equal('No, Return to Main Index');
                });
            });

            it('Modal confirm button has text \'Yes, Discard Changes\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('Yes, Discard Changes');
                });
            });

        });

        context('Does not throw a warning modal if changes have been saved', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)                                     // make a change
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();                    // save the change

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'SAVE_MAIN_INDEX_WARNING') {
                                cy.get(RtvRuralIndexModal.confirmButton).click()
                            }
                        });
                })
                .then(() => {
                    cy.get(RtvRuralIndexModal.modal)
                })
                .then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'SAVE_MAIN_INDEX_SUCCESS') {
                                cy.get(RtvRuralIndexModal.confirmButton)
                                    .click()                                                    // dimiss the save success modal
                                    .then(() => {
                                        cy.get(RtvRuralIndexDashboard.loadingSpinner)
                                            .should('not.exist')
                                            .then(() => {
                                                cy.get(RtvRuralIndexToolbar.discardButton).click();
                                            });                                                 // click discard changes button
                                    });
                            }
                        });
                });


            });

            it('Does not display the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

        });

        context('Cancelling warning modal does not discard the changes', () => {
            let newValue;

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexToolbar.discardButton).click();

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'DISCARD_MAIN_INDEX_WARNING') {
                                cy.get(RtvRuralIndexModal.cancelButton).click()
                            }
                        });
                })
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('Persists the changes', () => {
                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            expect(parseFloat(val)).to.equal(newValue);
                        });
                });
            });
        });

        context('Confirming warning modal discards the changes', () => {
            let oldValue;

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            oldValue = parseFloat(val);
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexToolbar.discardButton).click();

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'DISCARD_MAIN_INDEX_WARNING') {
                                cy.get(RtvRuralIndexModal.confirmButton).click()
                            }
                        });
                })
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('Discards the the changes', () => {
                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            expect(parseFloat(val)).to.equal(oldValue);
                        });
                });
            });
        });

    });

    describe('When selected TA is changed', () => {

        context('Does not throw a warning modal if there are no changes', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.wait(1000);

                cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist').then(() => {
                    cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                });
            });

            it('Does not display the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('TA changed successfully', () => {
                cy.get(RtvRuralIndexDashboard.taSelector).find(':selected').then($selectedItem => {
                    cy.get(RtvRuralIndexDashboard.taSelector + ' option').each(($option, index) => {
                        if ($option.text() === $selectedItem.text()) {
                            expect(index).to.equal(2);
                        }
                    })
                });
            });

        });

        context('Throws a warning modal if there are unsaved changes', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist').then(() => {
                    cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                });
            });

            it('Displays the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('exist');
            });

            it('Modal has \'warning\' class', () => {
                cy.get(RtvRuralIndexModal.modal).then($els => {
                    expect(Array.from($els[0].classList).includes('caution')).to.be.true;
                })
            });

            it('Modal has response code \'DISCARD_MAIN_INDEX_WARNING\'', () => {
                cy.get(RtvRuralIndexModal.responseCode)
                    .invoke('val')
                    .then(val => {
                        expect(val).to.equal('DISCARD_MAIN_INDEX_WARNING');
                    });
            });

            it('Modal heading has text \'Discard Changes\'', () => {
                cy.get(RtvRuralIndexModal.heading).then($el => {
                    expect($el.text().trim()).to.equal('Discard Changes');
                });
            });

            it('Modal message has warning \'All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?\'', () => {
                cy.get(RtvRuralIndexModal.message).then($el => {
                    expect($el.text().trim()).to.contain('All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?')
                });
            });

            it('Modal cancel button has text \'No, Return to Main Index\'', () => {
                cy.get(RtvRuralIndexModal.cancelButton).then($el => {
                    expect($el.text().trim()).to.equal('No, Return to Main Index');
                });
            });

            it('Modal confirm button has text \'Yes, Discard Changes\'', () => {
                cy.get(RtvRuralIndexModal.confirmButton).then($el => {
                    expect($el.text().trim()).to.equal('Yes, Discard Changes');
                });
            });

        });

        context('Does not throw a warning modal if changes have been saved', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.viLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)                                     // make a change
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexToolbar.saveButton).click();                    // save the change

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'SAVE_MAIN_INDEX_WARNING') {
                                cy.get(RtvRuralIndexModal.confirmButton).click()
                            }
                        });
                })
                .then(() => {
                    cy.get(RtvRuralIndexModal.modal)
                })
                .then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'SAVE_MAIN_INDEX_SUCCESS') {
                                cy.get(RtvRuralIndexModal.confirmButton)
                                    .click()                                                    // dimiss the save success modal
                                    .then(() => {
                                        cy.get(RtvRuralIndexDashboard.loadingSpinner)
                                            .should('not.exist')
                                            .then(() => {
                                                cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                                            });                                                 // click discard changes button
                                    });
                            }
                        });
                });


            });

            it('Does not display the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('TA changed successfully', () => {
                cy.get(RtvRuralIndexDashboard.taSelector).find(':selected').then($selectedItem => {
                    cy.get(RtvRuralIndexDashboard.taSelector + ' option').each(($option, index) => {
                        if ($option.text() === $selectedItem.text()) {
                            expect(index).to.equal(2);
                        }
                    })
                });
            });

        });

        context('Cancelling warning modal does not discard the changes', () => {
            let newValue;

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist').then(() => {
                    cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                });

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'DISCARD_MAIN_INDEX_WARNING') {
                                cy.get(RtvRuralIndexModal.cancelButton).click()
                            }
                        });
                })
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('Persists the changes', () => {
                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            expect(parseFloat(val)).to.equal(newValue);
                        });
                });
            });

            it('TA did not change', () => {
                cy.get(RtvRuralIndexDashboard.taSelector).find(':selected').then($selectedItem => {
                    cy.get(RtvRuralIndexDashboard.taSelector + ' option').each(($option, index) => {
                        if ($option.text() === $selectedItem.text()) {
                            expect(index).to.equal(1);
                        }
                    })
                });
            });
        });

        context('Confirming warning modal switches the TA', () => {

            before(() => {
                cy.log('Loading Main Index window...');

                cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

                cy.get(RtvRuralIndexDashboard.taSelector).select(1);

                cy.get(RtvRuralIndexMainIndex.lvLumpSumInputs + ':not([disabled])').then($inputs => {
                    cy.wrap($inputs[0])
                        .click()
                        .invoke('val')
                        .then(val => {
                            const newValue = parseFloat(val) + 1000;
                            cy.wrap($inputs[0])
                                .clear()
                                .type(newValue)
                                .blur();
                        });
                });

                cy.get(RtvRuralIndexDashboard.loadingSpinner).should('not.exist').then(() => {
                    cy.get(RtvRuralIndexDashboard.taSelector).select(2);
                });

                cy.get(RtvRuralIndexModal.modal).then(() => {
                    cy.get(RtvRuralIndexModal.responseCode)
                        .invoke('val')
                        .then(val => {
                            if (val === 'DISCARD_MAIN_INDEX_WARNING') {
                                cy.get(RtvRuralIndexModal.confirmButton).click()
                            }
                        });
                })
            });

            it('Closes the modal', () => {
                cy.get(RtvRuralIndexModal.modal).should('not.exist');
            });

            it('TA changed successfully', () => {
                cy.get(RtvRuralIndexDashboard.taSelector).find(':selected').then($selectedItem => {
                    cy.get(RtvRuralIndexDashboard.taSelector + ' option').each(($option, index) => {
                        if ($option.text() === $selectedItem.text()) {
                            expect(index).to.equal(2);
                        }
                    })
                });
            });
        });

    });

});

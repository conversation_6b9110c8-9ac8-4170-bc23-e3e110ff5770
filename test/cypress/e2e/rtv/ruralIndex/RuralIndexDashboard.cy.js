import RtvRuralIndexDashboard from '../../../model/RtvRuralIndexDashboard';
import user from '../../../support/user.js';

describe('Rural Index Toolbar', { defaultCommandTimeout: 10000 }, () => {
    context('Load the Rural Index section (default)', () => {

        it('Displays the Rural Index dashboard', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvRuralIndexDashboard.ruralIndexDashboard.then(dashboard => {
                expect(dashboard).to.exist.and.be.visible;
            });
        });

        it('Displays the Rural Index secondary navbar', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvRuralIndexDashboard.navBar.then(navBar => {
                expect(navBar).to.exist.and.be.visible;
            });
        });

        it('Displays the TA selector', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).then(selector => {
                expect(selector).to.exist.and.be.visible;
            });
        });

        it('TA selector is populated with items', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvRuralIndexDashboard.taSelectorItems.then(items => {
                expect(items.length).to.be.greaterThan(1);
            });
        });

        it('Displays a warning message when no TA is selected', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(0);

            RtvRuralIndexDashboard.selectTaMessage.then(message => {
                expect(message).to.exist.and.be.visible;
            });
        });

        it('Warning message has text \'Please select a TA.\'', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(0);

            RtvRuralIndexDashboard.selectTaMessage.then($el => {
                expect($el.text().trim()).to.equal('Please select a TA.');
            });
        });

        it('Does not display a message when a TA is selected', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            RtvRuralIndexDashboard.selectTaMessage.then(message => {
                expect(message).to.not.exist;
            });
        });

        it('Displays an error message when a TA has no valid rolls with rural assessments', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            // Select North Shore City - has no valid rural assessments at time of this test
            cy.get(RtvRuralIndexDashboard.taSelector).select('5 - North Shore City');

            cy.get(RtvRuralIndexDashboard.errorMessage).then($el => {
                expect($el).to.exist.and.be.visible;
            });
        });

        it('Error message has text \'No valid rural assessments in this TA.\'', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select('5 - North Shore City');

            cy.get(RtvRuralIndexDashboard.errorMessage).then($el => {
                expect($el.text().trim()).to.equal('No valid rural assessments in this TA.');
            });
        });

        it('Displays "Select a TA..." when no TA is selected', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvRuralIndexDashboard.taSelectorItems.then(items => {
                expect(items[0].textContent).to.equal('Select a TA...');
            });
        });

        it('Displays Main Index link', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexDashboard.mainIndexLink).then(link => {
                expect(link).to.exist.and.be.visible;
            });
        });

        it('Main Index link has the text "Main Index"', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexDashboard.mainIndexLink).then(link => {
                expect(link.text().trim()).equal('Main Index');
            });
        });

        it('Main Index link defaults to selected', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexDashboard.mainIndexLink).then(link => {
                expect(link).to.have.class('active');
            });
        });

        it('Has a Secondary refinements link', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).then(link => {
                expect(link).to.exist.and.be.visible;
            });
        });

        it('Secondary refinements link has the text "Secondary Refinements"', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);

            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).then(link => {
                expect(link.text().trim()).equal('Secondary Refinements');
            });
        });
    });

    context('Confirm Rural Index secondary toolbar functionality', () => {

        it('When Main Index is selected, the Main Index screen is displayed', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.mainIndexLink).click();

            RtvRuralIndexDashboard.mainIndexScreen.then(screen => {
                expect(screen).to.exist.and.be.visible;
            })
        });

        it('Clicking the Secondary Refinements link makes it the active tab', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click().then(link => {
                expect(link).to.have.class('active');
            });
        });

        it('Clicking the Secondary Refinements link makes Main Index inactive', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

            cy.get(RtvRuralIndexDashboard.mainIndexLink).then(link => {
                expect(link).to.not.have.class('active');
            });
        });

        it('When Secondary refinements is selected, the Secondary refinements screen is displayed', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click();

            cy.get(RtvRuralIndexDashboard.secondaryRefinementsScreen).then(screen => {
                expect(screen).to.exist.and.be.visible;
            });
        });

        it('Clicking the Main Index link makes it the active tab', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).click().then(link => {
                expect(link).to.have.class('active');
            });

            cy.get(RtvRuralIndexDashboard.mainIndexLink).click().then(link => {
                expect(link).to.have.class('active');
            });
        });

        it('Clicking the Main Index link makes Secondary Refinements inactive', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            cy.get(RtvRuralIndexDashboard.taSelector).select(1);
            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink)
                .click()
                .then(link => {
                    expect(link).to.have.class('active');
                });

            cy.get(RtvRuralIndexDashboard.mainIndexLink).click();

            cy.get(RtvRuralIndexDashboard.secondaryRefinementsLink).then(link => {
                expect(link).to.not.have.class('active');
            });
        });
    });

});

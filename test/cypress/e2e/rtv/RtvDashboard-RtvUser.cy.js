import MainNavigation from '../../model/MainNavigation';
import RtvDashboard from '../../model/RtvDashboard';
import user from '../../support/user.js';

describe('RTV Dashboard', { defaultCommandTimeout: 10000 }, () => {
    context('Load the RTV Dashboard', () => {
        it('Clicking the RTV link navigates to the RTV Dashboard', () => {
            cy.visitWithUser('', user.INTERNAL_RTV_USER);

            MainNavigation.rtvLink.click();

            cy.url().should('equal', env.BASE_URL + 'rtv-dashboard');
        });

        it('Displays the RTV dashboard', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.dashboard.then(dashboard => {
                expect(dashboard).to.exist.and.be.visible;
            });
        });

        it('Displays the primary toolbar', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.primaryToolbar.then(toolbar => {
                expect(toolbar).to.exist.and.be.visible;
            });
        });

        it('Primary toolbar displays the RTV Dashboard heading', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.dashboardHeading.then(heading => {
                expect(heading).to.exist.and.be.visible;
            });
        });

        it('RTV Dashboard heading has the text "RTV Dashboard"', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.dashboardHeading.then(heading => {
                expect(heading.text().trim()).equal('RTV Dashboard');
            });
        });

        it('Primary toolbar displays the the Rural Index link', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.ruralIndexLink.then(link => {
                expect(link).to.exist.and.be.visible;
            });
        });

        it('Rural Index link has the text "Rural Index"', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.ruralIndexLink.then(link => {
                expect(link.text().trim()).equal('Rural Index');
            });
        });

        it('Rural Index link defaults to selected', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.ruralIndexLink.then(link => {
                expect(link).to.have.class('active');
            });
        });

        it('Primary toolbar displays a QIVS link', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.qivsLink.then(link => {
                expect(link).to.exist.and.be.visible;
            });
        });

        it('QIVS link has the text "QIVS"', () => {
            cy.visitWithUser('rtv-dashboard', user.INTERNAL_RTV_USER);

            RtvDashboard.qivsLink.then(link => {
                expect(link.text().trim()).contains('QIVS');
            });
        });
    });
});

import RuralWorksheet, {
    loadCurrentWorksheet,
    verifyCreateWorksheetElements,
    dismissModal,
    verifyCannotHaveWorksheetModal,
    verifyCancelChangesModal,
} from '../../model/RuralWorksheet';
import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';
import PropertyInfoPanel from '../../model/PropertyInfoPanel';

describe('Create Rural Worksheet', { defaultCommandTimeout: 15000 }, () => {

    const validQpidWithNoWorksheet = '3363806'
    const validQpidIsMaoriLand = false;
    const isExternalUser =false;
    const invalidQpidWithNoWorksheet = '2250107'

    before(() => {
        cy.visitWithUser('', env.INTERNAL_ADMIN_USER);
    })

    describe('Loads and cancels with valid property with no worksheet', () => {

        before(() => {
            cy.visitWithLogin('/')
        });

        it('Search address that can have a Rural Worksheet', () => {
            Home.quickSearchBar.type(validQpidWithNoWorksheet);
            Home.typeAheadResults.should('exist');
            cy.wait(1000);
        });

        it('Verify on property', () => {

            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(validQpidWithNoWorksheet);
            });

            Home.quickSearchBar.clear();
        });

        it('Create Rural worksheet link is present', () => {
            PropertyDetails.propertySummaryTab.click();

            PropertyInfoPanel.propertyInfoHeader.should('exist');
            PropertyInfoPanel.singleRuralWorksheetHeader.should('exist');
            PropertyInfoPanel.singleRuralWorksheetValue.should('exist');
            PropertyInfoPanel.createRuralWorksheetLink.should('exist');
        });

        it('Successfully loads the Create Rural Worksheet page', () => {
            const isMaoriLand = true;
            const isExternalUser =false;
            loadCurrentWorksheet(validQpidWithNoWorksheet);

            cy.wait(500);

            verifyCreateWorksheetElements(isMaoriLand, isExternalUser);
        });

        it('Area and Production values are not empty', () => {
            RuralWorksheet.ruralWorksheetWorksheetArea.invoke('val').then(val => {
                expect(Number.isNaN(+val), 'Area should be a number').to.eq(false);
            });
            RuralWorksheet.ruralWorksheetProduction.invoke('val').then(val => {
                expect(Number.isNaN(+val), 'Production should be a number').to.eq(false);
            });
        });

        it('Drop down lists default to blank', () => {
            RuralWorksheet.ruralWorksheetImprovementAddType.invoke('val').then(val => {
                expect(val, 'Improvement type dropdown should be blank').to.eq('');
            });
            RuralWorksheet.ruralWorksheetLandUseTypeAddType.invoke('val').then(val => {
                expect(val, 'Land use type dropdown should be blank').to.eq('');
            });
            RuralWorksheet.ruralWorksheetLandUseTypeAddContour.invoke('val').then(val => {
                expect(val, 'Land use contour dropdown should be blank').to.eq('');
            });
            RuralWorksheet.ruralWorksheetLandUseTypeAddIrrigation.invoke('val').then(val => {
                expect(val, 'Land use irrigation dropdown should be blank').to.eq('');
            });
            RuralWorksheet.ruralWorksheetSiteAddView.invoke('val').then(val => {
                expect(val, 'Land view dropdown should be blank').to.eq('');
            });
        });

        it('Editable numeric fields default to 0', () => {
            RuralWorksheet.ruralWorksheetImprovementAddSize.invoke('val').then(val => {
                expect(val, 'Improvement size should be zero').to.eq('0');
            });
            RuralWorksheet.ruralWorksheetImprovementAddRate.invoke('val').then(val => {
                expect(val, 'Improvement rate should be zero').to.eq('0');
            });
            RuralWorksheet.ruralWorksheetLandUseTypeAddSize.invoke('val').then(val => {
                expect(val, 'Land use size should be zero').to.eq('0');
            });
            RuralWorksheet.ruralWorksheetLandUseTypeAddRate.invoke('val').then(val => {
                expect(val, 'Land use rate should be zero').to.eq('0');
            });
            RuralWorksheet.ruralWorksheetSiteAddArea.invoke('val').then(val => {
                expect(val, 'Site area should be zero').to.eq('0');
            });
            RuralWorksheet.ruralWorksheetSiteAddValue.invoke('val').then(val => {
                expect(val, 'Site lump sum should be blank').to.eq('');
            });
            if (!validQpidIsMaoriLand) {
                RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.invoke('val').then(val => {
                    expect(val, 'CV rounded should be zero').to.eq('$0');
                });
                RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.invoke('val').then(val => {
                    expect(val, 'LV rounded should be zero').to.eq('$0');
                });
            }
            if (validQpidIsMaoriLand) {
                RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedCV.invoke('val').then(val => {
                    expect(val, 'Adjusted CV rounded should be zero').to.eq('$0');
                });
                RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedLV.invoke('val').then(val => {
                    expect(val, 'Adjusted LV rounded should be zero').to.eq('$0');
                });
            }
        });

        it('Worksheet comment and Reason for Change fields are blank', () => {
            RuralWorksheet.ruralWorksheetComment.invoke('val').then(val => {
                expect(val, 'Worksheet comment should be blank').to.eq('');
            });
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.invoke('val').then(val => {
                expect(val, 'Reason for change output should be None').to.eq('-1');
            });
            RuralWorksheet.ruralWorksheetReasonForChangeSource.invoke('val').then(val => {
                expect(val, 'Reason for change source should be None').to.eq('-1');
            });
            RuralWorksheet.ruralWorksheetReasonForChangeReason.invoke('val').then(val => {
                expect(val, 'Reason for change should be blank').to.eq('');
            });
        });

        it('Cancel button discards the changes', () => {
            RuralWorksheet.ruralWorksheetCancelButton.click();

            cy.wait(500);

            verifyCancelChangesModal();
        });

        it('User is redirected to property detail summary tab', () => {
            dismissModal('CancelChangesWarning');
            cy.wait(1000);
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(validQpidWithNoWorksheet);
            });
            PropertyDetails.propertySummaryTab.should('have.class', 'active');
            PropertyInfoPanel.propertyInfoHeader.should('exist');
        });

    });

    // skip this test as we do not want to invalidate one of our only test cases
    describe.skip('Loads and saves with valid property with no worksheet', () => {
        const isMaoriLand = true;
        const isExternalUser = false;
        const currentDate = new Date().toUTCString();

        before(() => {
            cy.visit('');
        })

        it('Search address that cannot have a Rural Worksheet', () => {

            Home.quickSearchBar.type(validQpidWithNoWorksheet);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
            Home.typeAheadResults.contains('span.listBox-category', validQpidWithNoWorksheet).should('exist');
        });

        it('Verify on property', () => {
            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(validQpidWithNoWorksheet);
            });

            Home.quickSearchBar.clear();
        });

        it('Create Rural worksheet link is present', () => {
            PropertyDetails.propertySummaryTab.click();

            PropertyInfoPanel.propertyInfoHeader.should('exist');
            PropertyInfoPanel.singleRuralWorksheetHeader.should('exist');
            PropertyInfoPanel.singleRuralWorksheetValue.should('not.exist');
            PropertyInfoPanel.createRuralWorksheetLink.should('exist');
        });

        it('Successfully loads the Create Rural Worksheet page', () => {
            const isMaoriLand = true;
            const isExternalUser =false;
            loadCurrentWorksheet(validQpidWithNoWorksheet);

            cy.wait(500);

            verifyCreateWorksheetElements(isMaoriLand, isExternalUser);
        });

        it('Successfully adds a land use type and updates the worksheet only', () => {
            loadCurrentWorksheet(validQpidWithNoWorksheet);

            // add a valid land use row
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.click();
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.clear();
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.type(currentDate);
            RuralWorksheet.ruralWorksheetLandUseTypeAddContour.select('Easy Hill');
            RuralWorksheet.ruralWorksheetLandUseTypeAddType.select('Pasture');
            RuralWorksheet.ruralWorksheetLandUseTypeAddSize.click();
            RuralWorksheet.ruralWorksheetLandUseTypeAddSize.type(20);
            RuralWorksheet.ruralWorksheetLandUseTypeAddRate.click();
            RuralWorksheet.ruralWorksheetLandUseTypeAddRate.type(2500);
            RuralWorksheet.ruralWorksheetLandUseTypeAddButton.click();

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            verifySuccessModal();
        });

        it('Reloading the worksheet reflects the saved changes', () => {

            loadCurrentWorksheet(validQpidWithNoWorksheet);

            RuralWorksheet.ruralWorksheetImprovementEditDescriptions.then($items => {
                const lastItem = $items.length -1;
                expect($items[lastItem]).prop('value').to.equal(currentDate);
            });

            cy.wait(500);

            verifyCreateWorksheetElements(isMaoriLand, isExternalUser);
        });
    });

    describe('Invalid property with no worksheet', () => {

        before(() => {
            cy.visit('');
        })

        it('Search address that cannot have a Rural Worksheet', () => {
            Home.quickSearchBar.type(invalidQpidWithNoWorksheet);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
            Home.typeAheadResults.contains('span.listBox-category', invalidQpidWithNoWorksheet).should('exist');
        });

        it('Verify on property', () => {
            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(invalidQpidWithNoWorksheet);
            });

            Home.quickSearchBar.clear();
        });

        it('Rural worksheet links are not present', () => {
            PropertyDetails.propertySummaryTab.click();

            PropertyInfoPanel.propertyInfoHeader.should('exist');
            PropertyInfoPanel.singleRuralWorksheetHeader.should('not.exist');
            PropertyInfoPanel.singleRuralWorksheetValue.should('not.exist');
            PropertyInfoPanel.createRuralWorksheetLink.should('not.exist');
        });

        it('Prevents the user from creating a rural worksheet', () => {
            loadCurrentWorksheet(invalidQpidWithNoWorksheet);

            verifyCannotHaveWorksheetModal();
        });

        it('Redirects the user to the property detail summary tab', () => {
            dismissModal('CannotCreateWorksheet');
            cy.wait(500);
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(invalidQpidWithNoWorksheet);
            });
            PropertyDetails.propertySummaryTab.should('have.class', 'active');
            PropertyInfoPanel.propertyInfoHeader.should('exist');
        });

    });

});

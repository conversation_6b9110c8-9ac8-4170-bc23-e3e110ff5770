import RuralWorksheet, {
    loadCurrentWorksheet,
    loadRevisionWorksheet,
    loadRtvWorksheet,
    verifyCopyLandMatrixWarningModal,
    verifyRecalculateLandMatrixWarningModal,
} from '../../model/RuralWorksheet';
import LandMatrix, { verifyLandMatrixElements } from '../../model/RuralWorksheetLandMatrix';

describe('Rural Worksheet Land Matrix', { defaultCommandTimeout: 15000 }, () => {
    const qpidWithLandMatrix = 1123601;
    const revisionQpidWithLandMatrix = 3329585;
    const qpidWithNonZeroApportionmentCodeAndLandMatrix = 3419694;
    const rtvRevisionQpidWithLandMatrix = env.ADDRESS9.QPID;

    // exists on all versions of the worksheet (current, revision, rtv)
    describe('Land Matrix exists on all versions of the worksheet (current, revision, rtv)', () => {
        before(() => {
            cy.login();
        });

        it('Exists on current worksheet', () => {
            loadCurrentWorksheet(qpidWithLandMatrix);
            // Check we are on current worksheet
            RuralWorksheet.ruralWorksheetTab1.should('have.class', 'is-active');

            verifyLandMatrixElements(true);
        });

        it('Exists on revision worksheet', () => {
            loadRevisionWorksheet(revisionQpidWithLandMatrix);
            // Check we are on revision worksheet
            RuralWorksheet.ruralWorksheetTab1.should('not.have.class', 'is-active');
            RuralWorksheet.ruralWorksheetTab2.should('have.class', 'is-active');

            verifyLandMatrixElements(false);
        });

        it('Exists on rtv worksheet', () => {
            loadRtvWorksheet(rtvRevisionQpidWithLandMatrix);
            // Check we are on revision worksheet
            RuralWorksheet.ruralWorksheetTab1.should('not.have.class', 'is-active');
            RuralWorksheet.ruralWorksheetTab3.should('have.class', 'is-active');

            verifyLandMatrixElements(false);
        });
    });

    describe('Estimated values are blank when apportionmentCode is not 0', () => {
        //skip as we do not have reliable test case
        it('Estimated rates and values are blank', () => {
            loadCurrentWorksheet(qpidWithNonZeroApportionmentCodeAndLandMatrix);
            verifyLandMatrixElements(true);

            // check values are blank here
            LandMatrix.ruralWorksheetLandMatrixTotalLV.invoke('val').then(val => {
                expect(val).to.equal('$0');
            });
        });
    });


    describe('Copy Land Matrix', () => {
        it('Displays a warning modal', () => {
            loadCurrentWorksheet(qpidWithLandMatrix);
            verifyLandMatrixElements(true);

            LandMatrix.ruralWorksheetLandMatrixCopyLandMatrixButton.click();

            verifyCopyLandMatrixWarningModal();
        });
    });

    describe('Recalculate Land Matrix', () => {
        it('Displays a warning modal', () => {
            loadCurrentWorksheet(qpidWithLandMatrix);
            verifyLandMatrixElements(true);

            LandMatrix.ruralWorksheetLandMatrixRecalculateLandMatrixButton.click();

            verifyRecalculateLandMatrixWarningModal();
        });
    });

});

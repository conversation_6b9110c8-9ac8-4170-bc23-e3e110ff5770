import RuralWorksheet, {
    loadCurrentWorksheet,
    verifyCurrentWorksheetElements,
    verifyAreaWarningModal,
    verifyProductionTreesUpdateErrorModal,
    verifyValidationErrorModal,
    verifySuccessModal,
    dismissAreaMismatchWarningModal,
} from '../../../model/RuralWorksheet';
describe('Rural Worksheet Current #3', { defaultCommandTimeout: 15000 }, () => {
    describe('Update Worksheet Only (Current)', () => {
        before(() => {
            cy.login();
        });

        it('Warns the user when the calculated area does not match assessment or worksheet', () => {
            const isMaoriLand = true;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS8.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // change the area
            RuralWorksheet.ruralWorksheetWorksheetArea.click();
            RuralWorksheet.ruralWorksheetWorksheetArea.clear();
            RuralWorksheet.ruralWorksheetWorksheetArea.type(5);

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();
            // check for presence of the area warning modal
            verifyAreaWarningModal();
        });

        it('Stops the user from updating the worksheet only when production has been altered', () => {
            const isMaoriLand = true;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS8.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // change the production
            RuralWorksheet.ruralWorksheetProduction.click();
            RuralWorksheet.ruralWorksheetProduction.clear();
            RuralWorksheet.ruralWorksheetProduction.type(123);

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();
            // check for presence of the production/trees error modal
            verifyProductionTreesUpdateErrorModal();
        });

        it('Stops the user from updating the worksheet only when trees has been altered', () => {
            const isMaoriLand = true;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS8.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // change the production
            RuralWorksheet.ruralWorksheetWorksheetValuesTV.click();
            RuralWorksheet.ruralWorksheetWorksheetValuesTV.clear();
            RuralWorksheet.ruralWorksheetWorksheetValuesTV.type(123);

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();
            // check for presence of the production/trees error modal
            verifyProductionTreesUpdateErrorModal();
        });

        it('Stops the user from updating the worksheet when an invalid VI is entered', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            //enter values to produce negative vi
            RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.click();
            RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.clear();
            RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.type(500000);
            RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.click();
            RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.clear();
            RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.type(700000);

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            // check for presence of the validation error modal
            verifyValidationErrorModal();
            RuralWorksheet.ruralWorksheetModalListItems.should(items => {
                expect(items[0]).to.contain.text('VI cannot be negative');
            });
        });

        it('Stops the user from updating the worksheet when an invalid improvement type exists', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // add an improvement
            RuralWorksheet.ruralWorksheetImprovementAddDescription.click();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.clear();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.type(123);
            RuralWorksheet.ruralWorksheetImprovementAddButton.click();

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            // check for presence of the validation error modal
            verifyValidationErrorModal();
            RuralWorksheet.ruralWorksheetModalListItems.should(items => {
                expect(items[0]).to.contain.text('There are problems with one or more improvements');
            });
        });

        it('Stops the user from updating the worksheet when an invalid land use type exists', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // add an improvement
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.click();
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.clear();
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.type(123);
            RuralWorksheet.ruralWorksheetLandUseTypeAddButton.click();

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            // check for presence of the production/trees error modal
            verifyValidationErrorModal();
            RuralWorksheet.ruralWorksheetModalListItems.should(items => {
                expect(items[0]).to.contain.text('There are problems with one or more land use types');
            });
        });

        it('Stops the user from updating the worksheet when an invalid site exists', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);
            // add an improvement
            RuralWorksheet.ruralWorksheetSiteAddArea.click();
            RuralWorksheet.ruralWorksheetSiteAddArea.clear();
            RuralWorksheet.ruralWorksheetSiteAddArea.type(123);
            RuralWorksheet.ruralWorksheetSiteAddButton.click();

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            // check for presence of the production/trees error modal
            verifyValidationErrorModal();
            RuralWorksheet.ruralWorksheetModalListItems.should(items => {
                expect(items[0]).to.contain.text('There are problems with one or more sites');
            });
        });

        it('Successfully adds an improvement and updates the worksheet only', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // add an improvement
            RuralWorksheet.ruralWorksheetImprovementAddDescription.click();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.clear();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.type(currentDate);
            RuralWorksheet.ruralWorksheetImprovementAddType.select('Dwelling');
            RuralWorksheet.ruralWorksheetImprovementAddSize.click();
            RuralWorksheet.ruralWorksheetImprovementAddSize.type(123);
            RuralWorksheet.ruralWorksheetImprovementAddRate.click();
            RuralWorksheet.ruralWorksheetImprovementAddRate.type(123);
            RuralWorksheet.ruralWorksheetImprovementAddButton.click();

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            // check for presence of the success modal
            verifySuccessModal();

            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            RuralWorksheet.ruralWorksheetImprovementEditDescriptions.then($items => {
                const lastItem = $items.length - 1;
                expect($items[lastItem]).prop('value').to.equal(currentDate);
            });
        });
    });
});

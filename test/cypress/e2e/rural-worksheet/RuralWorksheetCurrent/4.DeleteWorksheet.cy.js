import RuralWorksheet, {
    loadCurrentWorksheet,
    verifyCurrentWorksheetElements,
    verifyDeleteModal,
} from '../../../model/RuralWorksheet';
describe('Rural Worksheet Current #4', { defaultCommandTimeout: 15000 }, () => {
    describe('Delete Worksheet', () => {
        before(() => {
            cy.login();
        });

        it('Asks the user to confirm deletion of the worksheet', () => {
            const isMaoriLand = false;
            const isExternalUser = false;

            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // click the delete button
            RuralWorksheet.ruralWorksheetDeleteButton.click();

            cy.wait(500);
            verifyDeleteModal();

            // cancel deletion of worksheet (so as not to break tests)
            RuralWorksheet.ruralWorksheetModalCancelButton.click();
        })
    });
});

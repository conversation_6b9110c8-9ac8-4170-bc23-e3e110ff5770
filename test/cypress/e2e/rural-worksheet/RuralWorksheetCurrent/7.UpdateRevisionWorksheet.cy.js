import RuralWorksheet, {
    loadCurrentWorksheet,
    verifyCurrentWorksheetElements,
    verifyRevisionWorksheetMustBeUpdatedModal,
    dismissAreaMismatchWarningModal,
    dismissRevisionWorksheetMustBeUpdatedModal,
    verifyRevisionWorksheetElements
} from '../../../model/RuralWorksheet';
describe('Rural Worksheet Current #2', { defaultCommandTimeout: 15000 }, () => {
    // skip test until we can reliably provide a test case
    describe.skip('Forces the user to update the revision worksheet when necessary', () => {
        const isMaoriLand = false;
        const isExternalUser = false;
        const currentDate = new Date().toUTCString();
        const testRate = '5000';
        const testQpid = 1430131; // qpid that has a revision worksheet, and revision values exist i.e. revision_capital_value, revision_land_value

        before(() => {
            cy.login();
        });

        it('Tells the user that the Revision Worksheet needs to be updated', () => {
            loadCurrentWorksheet(testQpid);
            verifyCurrentWorksheetElements(isMao<PERSON><PERSON>and, isExternalUser);

            RuralWorksheet.ruralWorksheetImprovementAddDescription.click();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.clear();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.type(currentDate);
            RuralWorksheet.ruralWorksheetImprovementAddType.select('Dwelling');
            RuralWorksheet.ruralWorksheetImprovementAddSize.click();
            RuralWorksheet.ruralWorksheetImprovementAddSize.clear();
            RuralWorksheet.ruralWorksheetImprovementAddSize.type(123);
            RuralWorksheet.ruralWorksheetImprovementAddRate.click();
            RuralWorksheet.ruralWorksheetImprovementAddRate.clear();
            RuralWorksheet.ruralWorksheetImprovementAddRate.type(testRate);
            RuralWorksheet.ruralWorksheetImprovementAddButton.click();

            RuralWorksheet.ruralWorksheetUpdateWorksheetButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            verifyRevisionWorksheetMustBeUpdatedModal();

            dismissRevisionWorksheetMustBeUpdatedModal();
        });

        it('Navigates the user to the Revision Worksheet ', () => {
            verifyRevisionWorksheetElements(isMaoriLand, isExternalUser);
        });

        it('Revision worksheet reflects the changes from Current', () => {
            RuralWorksheet.ruralWorksheetImprovementEditDescriptions.its('length').should('be.gt', 0);
            RuralWorksheet.ruralWorksheetImprovementEditDescriptions.then($items => {
                expect($items[$items.length -1]).prop('value').to.equal(currentDate);
            });
        });

        it('Newly added rate has been adjusted', () => {
            RuralWorksheet.ruralWorksheetImprovementEditRates.its('length').should('be.gt', 0);
            RuralWorksheet.ruralWorksheetImprovementEditRates.then($items => {
                expect($items[$items.length -1]).prop('value').to.not.equal(testRate);
            });
        });
    });

});

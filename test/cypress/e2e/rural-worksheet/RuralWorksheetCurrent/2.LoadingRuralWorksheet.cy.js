import RuralWorksheet, {
    loadCurrentWorksheet,
    verifyCurrentWorksheetElements,
} from '../../../model/RuralWorksheet';

describe('Rural Worksheet Current #2', { defaultCommandTimeout: 15000 }, () => {
    describe('Loading Rural Worksheet (Current)', () => {
        before(() => {
            cy.login();
        });

        it('Loads a non-maori land Rural Worksheet as an internal user', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            // navigate to the rural worksheet (should click the rural worksheet link on the property panel, once it exists)
            loadCurrentWorksheet(env.ADDRESS9.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);
            // Check we are on current worksheet
            RuralWorksheet.ruralWorksheetTab1.should('have.class', 'is-active');
        });

        it('Loads a maori land Rural Worksheet as an internal user', () => {
            const isMaoriLand = true;
            const isExternalUser = false;
            // navigate to the rural worksheet (should click the rural worksheet link on the property panel, once it exists)
            loadCurrentWorksheet(env.ADDRESS8.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // Check we are on current worksheet
            RuralWorksheet.ruralWorksheetTab1.should('have.class', 'is-active');
        });

        it('Loads a maori land Rural Worksheet as an external user', () => {
            cy.overrideUserData({
                roles: [{ name: 'EXTERNAL_USER_READ' }],
            });

            const isMaoriLand = false;
            const isExternalUser = true;

            // navigate to the rural worksheet (should click the rural worksheet link on the property panel, once it exists)
            // cy.visit(`roll-maintenance/rural-worksheet/${env.ADDRESS5.QPID}`);
            loadCurrentWorksheet(env.ADDRESS9.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // Check we are on current worksheet
            RuralWorksheet.ruralWorksheetTab1.should('have.class', 'is-active');
            RuralWorksheet.ruralWorksheetTab2.should('not.exist');
        });
    });
});

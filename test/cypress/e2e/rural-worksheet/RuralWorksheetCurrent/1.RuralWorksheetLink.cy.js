import Home from '../../../model/Home';
import PropertyDetails from '../../../model/PropertyDetails';
import PropertyInfoPanel from '../../../model/PropertyInfoPanel';

describe('Rural Worksheet Current #1', { defaultCommandTimeout: 15000 }, () => {
    describe('Rural Worksheet link', () => {
        before(() => {
            cy.visitWithLogin('/')
        });

        it('Search address that can have a Rural Worksheet', () => {
            Home.quickSearchBar.type(env.ADDRESS7.SEARCH_TERM);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
            Home.typeAheadResults.contains('span.listBox-category', env.ADDRESS7.QPID).should('exist');
        });

        it('Verify on property', () => {
            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(env.ADDRESS7.QPID);
            });
            // commented out until the PropertyInfoPanel actually exists
            PropertyInfoPanel.bothWorksheetRuralHeader.should('exist');
            PropertyInfoPanel.bothWorksheetRuralHeader.contains('Rural Worksheet');
            PropertyInfoPanel.bothWorksheetRuralValue.should('exist');
            PropertyInfoPanel.propertyInfoHeader.should('exist');
            Home.quickSearchBar.clear();
        });
    });
});

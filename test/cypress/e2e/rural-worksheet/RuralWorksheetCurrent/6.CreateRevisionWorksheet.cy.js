import RuralWorksheet, {
    loadCurrentWorksheet,
    verifyCurrentWorksheetElements,
    verifyAreaWarningModal,
    verifyRevisionWorksheetElements,
} from '../../../model/RuralWorksheet';

describe('Rural Worksheet Current #6', { defaultCommandTimeout: 15000 }, () => {

    describe.skip('Create Revision Worksheet', () => {
        before(() => {
            cy.login();
        });

        // skip until we have reliable test data/generation methods
        it('Creates a revision worksheet', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet('2349889');
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            RuralWorksheet.ruralWorksheetTab1.should('have.class', 'is-active');
            RuralWorksheet.ruralWorksheetCreateRevisionButton.should('exist');

            RuralWorksheet.ruralWorksheetCreateRevisionButton.click();

            verifyRevisionWorksheetElements(isMaoriLand, isExternalUser);

            // check for presence of the area warning modal
            verifyAreaWarningModal();
        });
    });

});

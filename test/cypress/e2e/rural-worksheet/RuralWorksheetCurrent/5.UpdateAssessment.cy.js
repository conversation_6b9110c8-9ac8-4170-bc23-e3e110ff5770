

import RuralWorksheet, {
    loadCurrentWorksheet,
    verifyCurrentWorksheetElements,
    verifyAreaWarningModal,
    verifyAcceptNewValuesConfirmation,
    verifyBalanceChangedWarningModal,
    verifyValidationErrorModal,
    verifySuccessModal,
    dismissAcceptNewValuesConfirmationModal,
    dismissBalanceChangedWarningModal,
    dismissAreaMismatchWarningModal,
} from '../../../model/RuralWorksheet';

describe('Rural Worksheet Current #5', { defaultCommandTimeout: 15000 }, () => {
    describe('Update Assessment (Current)', () => {
        before(() => {
            cy.login();
        });

        it('Stops the user from updating the assessment when Reason For Change is invalid', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // clear the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();

            // check for presence of the production/trees error modal
            verifyValidationErrorModal();
            RuralWorksheet.ruralWorksheetModalListItems.should(items => {
                expect(items[0]).to.contain.text('Please complete the Reason for Change');
            });
        });

        it('Warns the user when the calculated area does not match assessment or worksheet', () => {
            const isMaoriLand = true;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS8.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // change the area
            RuralWorksheet.ruralWorksheetWorksheetArea.click();
            RuralWorksheet.ruralWorksheetWorksheetArea.clear();
            RuralWorksheet.ruralWorksheetWorksheetArea.type(5);

            // enter the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('1 Entered Reason');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('From within Quotable Value');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();
            RuralWorksheet.ruralWorksheetReasonForChangeReason.type(currentDate);

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();
            // check for presence of the area warning modal
            verifyAreaWarningModal();
        });

        it('Asks the user to confirm new assessment values', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // enter the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('1 Entered Reason');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('From within Quotable Value');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();
            RuralWorksheet.ruralWorksheetReasonForChangeReason.type(currentDate);

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            cy.wait(500);
            verifyAcceptNewValuesConfirmation();
        });

        it('Warns the user the land value does not match the assessment', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // add a land use row
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.click();
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.clear();
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.type(currentDate);
            RuralWorksheet.ruralWorksheetLandUseTypeAddContour.select('Easy Hill');
            RuralWorksheet.ruralWorksheetLandUseTypeAddType.select('Pasture');
            RuralWorksheet.ruralWorksheetLandUseTypeAddSize.click();
            RuralWorksheet.ruralWorksheetLandUseTypeAddSize.type(20);
            RuralWorksheet.ruralWorksheetLandUseTypeAddRate.click();
            RuralWorksheet.ruralWorksheetLandUseTypeAddRate.type(2500);
            RuralWorksheet.ruralWorksheetLandUseTypeAddButton.click();
            RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.click();
            RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.clear();

            // enter the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('1 Entered Reason');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('From within Quotable Value');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();
            RuralWorksheet.ruralWorksheetReasonForChangeReason.type(currentDate);

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();

            cy.wait(500);
            dismissAcceptNewValuesConfirmationModal();

            cy.wait(500)
            verifyBalanceChangedWarningModal();
        });

        it('Stops the user from updating the assessment when an invalid improvement type exists', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // add an improvement
            RuralWorksheet.ruralWorksheetImprovementAddDescription.click();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.clear();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.type(123);
            RuralWorksheet.ruralWorksheetImprovementAddButton.click();

            // enter the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('1 Entered Reason');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('From within Quotable Value');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();
            RuralWorksheet.ruralWorksheetReasonForChangeReason.type(currentDate);

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();
            cy.wait(500);
            dismissAcceptNewValuesConfirmationModal();
            cy.wait(500);
            dismissBalanceChangedWarningModal();

            // check for presence of the production/trees error modal
            verifyValidationErrorModal();
            RuralWorksheet.ruralWorksheetModalListItems.should(items => {
                expect(items[0]).to.contain.text('There are problems with one or more improvements');
            });
        });

        it('Stops the user from updating the assessment when an invalid land use type exists', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // add an improvement
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.click();
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.clear();
            RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.type(123);
            RuralWorksheet.ruralWorksheetLandUseTypeAddButton.click();

            // enter the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('1 Entered Reason');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('From within Quotable Value');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();
            RuralWorksheet.ruralWorksheetReasonForChangeReason.type(currentDate);

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();
            cy.wait(500);
            dismissAcceptNewValuesConfirmationModal();
            cy.wait(500);
            dismissBalanceChangedWarningModal();

            // check for presence of the production/trees error modal
            verifyValidationErrorModal();
            RuralWorksheet.ruralWorksheetModalListItems.should(items => {
                expect(items[0]).to.contain.text('There are problems with one or more land use types');
            });
        });

        it('Stops the user from updating the assessment when an invalid site exists', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);
            // add an improvement
            RuralWorksheet.ruralWorksheetSiteAddArea.click();
            RuralWorksheet.ruralWorksheetSiteAddArea.clear();
            RuralWorksheet.ruralWorksheetSiteAddArea.type(123);
            RuralWorksheet.ruralWorksheetSiteAddButton.click();

            // enter the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('1 Entered Reason');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('From within Quotable Value');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();
            RuralWorksheet.ruralWorksheetReasonForChangeReason.type(currentDate);

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();
            cy.wait(500);
            dismissAcceptNewValuesConfirmationModal();
            cy.wait(500);
            dismissBalanceChangedWarningModal();

            // check for presence of the production/trees error modal
            verifyValidationErrorModal();
            RuralWorksheet.ruralWorksheetModalListItems.should(items => {
                expect(items[0]).to.contain.text('There are problems with one or more sites');
            });
        });

        it('Successfully updates the Assessment', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            const newProductionVal = Math.floor(Math.random() * 9999);
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // add an improvement
            RuralWorksheet.ruralWorksheetImprovementAddDescription.click();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.clear();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.type(currentDate);
            RuralWorksheet.ruralWorksheetImprovementAddType.select('Dwelling');
            RuralWorksheet.ruralWorksheetImprovementAddSize.click();
            RuralWorksheet.ruralWorksheetImprovementAddSize.type(123);
            RuralWorksheet.ruralWorksheetImprovementAddRate.click();
            RuralWorksheet.ruralWorksheetImprovementAddRate.type(123);
            RuralWorksheet.ruralWorksheetImprovementAddButton.click();

            // update worksheet area
            RuralWorksheet.ruralWorksheetProduction.clear();
            RuralWorksheet.ruralWorksheetProduction.type(newProductionVal);

            // enter the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('1 Entered Reason');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('From within Quotable Value');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();
            RuralWorksheet.ruralWorksheetReasonForChangeReason.type(currentDate);

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();
            cy.wait(500);
            dismissAcceptNewValuesConfirmationModal();
            cy.wait(500);
            dismissBalanceChangedWarningModal();

            // check for presence of the success modal
            verifySuccessModal();

            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS5.QPID);
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);

            // check for updated worksheet area
            RuralWorksheet.ruralWorksheetProduction.should('have.value', newProductionVal);

            // check for updated improvement
            RuralWorksheet.ruralWorksheetImprovementEditDescriptions.then($items => {
                    const lastItem = $items.length -1;
                    expect($items[lastItem]).prop('value').to.equal(currentDate);
                }
            );
        });

        // TODO: find/generate a qpid with SRA values that are value based and split between classes
        it.skip('Updates the Assessment, and tells the user SRA values need changing', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const currentDate = new Date().toUTCString();
            // reload the worksheet
            loadCurrentWorksheet(env.ADDRESS10.QPID);  // should be a property with SRA values that are value based and split between classes
            verifyCurrentWorksheetElements(isMaoriLand, isExternalUser);  // should be a property with SRA values that are value based and split between classes

            // add an improvement row (so the value is changing, meaning the SRAs should be updated)
            RuralWorksheet.ruralWorksheetImprovementAddDescription.click();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.clear();
            RuralWorksheet.ruralWorksheetImprovementAddDescription.type(currentDate);
            RuralWorksheet.ruralWorksheetImprovementAddType.select('Dwelling');
            RuralWorksheet.ruralWorksheetImprovementAddSize.click();
            RuralWorksheet.ruralWorksheetImprovementAddSize.type(5);
            RuralWorksheet.ruralWorksheetImprovementAddRate.click();
            RuralWorksheet.ruralWorksheetImprovementAddRate.type(10000);
            RuralWorksheet.ruralWorksheetImprovementAddButton.click();

            // enter the reason for change
            RuralWorksheet.ruralWorksheetReasonForChangeOutput.select('1 Entered Reason');
            RuralWorksheet.ruralWorksheetReasonForChangeSource.select('From within Quotable Value');
            RuralWorksheet.ruralWorksheetReasonForChangeReason.clear();
            RuralWorksheet.ruralWorksheetReasonForChangeReason.type(currentDate);

            RuralWorksheet.ruralWorksheetUpdateAssessmentButton.click();

            cy.wait(500);
            dismissAreaMismatchWarningModal();
            cy.wait(500);
            dismissAcceptNewValuesConfirmationModal();
            cy.wait(500);
            dismissBalanceChangedWarningModal();

            // check for presence of the success modal
            verifySuccessModal();

            RuralWorksheet.ruralWorksheetModalHeading.contains('SRA values need updating');
            RuralWorksheet.ruralWorksheetModalMessage.contains('but the SRA values need to be adjusted');
        });
    });

});

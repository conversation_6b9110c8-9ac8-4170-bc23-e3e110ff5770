import RuralWorksheet, {
    verifyRevisionWorksheetElements,
    loadRevisionWorksheet,
    verifyAcceptNewValuesConfirmation,
    verifySuccessModal,
    dismissAcceptNewValuesConfirmationModal,
} from '../../model/RuralWorksheet';

import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';

describe('Rural Worksheet Revision', { defaultCommandTimeout: 15000 }, () => {
    const revisionQpid = 3329584;
    const revisionQpidMaoriLand = 423454;

    before(() => {
        cy.visitWithLogin('/');
    });

    describe('Rural Worksheet link', () => {
        it('Search address that can have a Rural Worksheet', () => {
            Home.quickSearchBar.type(env.ADDRESS5.SEARCH_TERM);
            Home.typeAheadResults.should('exist');
            Home.typeAheadResults.children().should('have.length.at.least', 1);
            Home.typeAheadResults.contains('span.listBox-category', env.ADDRESS5.QPID).should('exist');
        });

        it('Verify on property', () => {
            Home.quickSearchBar.type('{enter}');
            PropertyDetails.qpid.invoke('text').then(text => {
                expect(text).to.equal(env.ADDRESS5.QPID);
            });

            Home.quickSearchBar.clear();
        });
    });

    describe('Loading Rural Worksheet (Revision)', () => {
        it('Loads a non-maori land Rural Revision Worksheet', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            // navigate to the rural worksheet (should click the rural worksheet link on the property panel, once it exists)
            loadRevisionWorksheet(revisionQpid);
            verifyRevisionWorksheetElements(isMaoriLand, isExternalUser);
            // Check we are on revision worksheet
            RuralWorksheet.ruralWorksheetTab1.should('not.have.class', 'is-active');
            RuralWorksheet.ruralWorksheetTab2.should('have.class', 'is-active');
        });

        it('Loads a maori land Rural Revision Worksheet', () => {
            const isMaoriLand = true;
            const isExternalUser = false;
            // navigate to the rural worksheet (should click the rural worksheet link on the property panel, once it exists)
            loadRevisionWorksheet(revisionQpidMaoriLand);
            verifyRevisionWorksheetElements(isMaoriLand, isExternalUser);

            // Check we are on revision worksheet
            RuralWorksheet.ruralWorksheetTab1.should('not.have.class', 'is-active');
            RuralWorksheet.ruralWorksheetTab2.should('have.class', 'is-active');
        });

    });

    describe('Update (Revision)', () => {
        it('Asks the user to confirm new assessment values', () => {
            const isMaoriLand = false;
            const isExternalUser = false;

            // reload the worksheet
            loadRevisionWorksheet(revisionQpid);
            verifyRevisionWorksheetElements(isMaoriLand, isExternalUser);

            RuralWorksheet.ruralWorksheetUpdateRevisionButton.click();

            cy.wait(500);
            verifyAcceptNewValuesConfirmation();
        });

        it('Successfully updates the Assessment', () => {
            const isMaoriLand = false;
            const isExternalUser = false;
            const newRate = Math.floor(Math.random() * 9999);
            // reload the worksheet
            loadRevisionWorksheet(revisionQpid);
            verifyRevisionWorksheetElements(isMaoriLand, isExternalUser);

            // update the rate of the last improvement (should check an improvement exists)
            RuralWorksheet.ruralWorksheetImprovementEditRates.last().click();
            RuralWorksheet.ruralWorksheetImprovementEditRates.last().type(newRate);

            RuralWorksheet.ruralWorksheetUpdateRevisionButton.click();

            cy.wait(500);
            dismissAcceptNewValuesConfirmationModal();

            // check for presence of the success modal
            verifySuccessModal();

            // reload the worksheet
            loadRevisionWorksheet(revisionQpid);
            verifyRevisionWorksheetElements(isMaoriLand, isExternalUser);

            // check for updated improvement
            RuralWorksheet.ruralWorksheetImprovementEditRates.last().click();
            RuralWorksheet.ruralWorksheetImprovementEditRates.last().should('have.value', newRate);

        });

    });
});


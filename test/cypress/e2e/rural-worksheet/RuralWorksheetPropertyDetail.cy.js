import {
    loadCurrentWorksheet,
} from '../../model/RuralWorksheet';
import PropertyDetail, { verifyPropertyDetailElements } from '../../model/RuralWorksheetPropertyDetail';

describe('Rural Worksheet Property Details', { defaultCommandTimeout: 15000 }, () => {

    const qpidWithValidApportionmentCode = '1123601'
    const qpidWithInvalidApportionmentCode = '2463893'
    
    before(() => {
        cy.visitWithUser('', env.INTERNAL_ADMIN_USER);
    });

    describe('Rural Worksheet Property Detail Section', () => {
        before(() => {
            cy.login();
        });

        // exists on all versions of the worksheet (current, revision, rtv)
        it('Exists on worksheet', () => {
            loadCurrentWorksheet(qpidWithValidApportionmentCode);
            verifyPropertyDetailElements();
        });

        it('Fields are disabled for invalid apportionment code', () => {

            loadCurrentWorksheet(qpidWithInvalidApportionmentCode);
            verifyPropertyDetailElements();

            // check editable state of property detail fields
            PropertyDetail.ruralWorksheetQVCategory.then($el => {
                const inputElement = $el.find('input.multiselect__input');
                expect(inputElement[0]).to.have.property('disabled');
            });
            PropertyDetail.ruralWorksheetGrouping.then($el => {
                const inputElement = $el.find('input.multiselect__input');
                expect(inputElement[0]).to.have.property('disabled');
            });
            PropertyDetail.ruralWorksheetQualityRating.then($el => {
                const inputElement = $el.find('input.multiselect__input');
                expect(inputElement[0]).to.have.property('disabled');
            });
            PropertyDetail.ruralWorksheetZone.then($el => {
                const inputElement = $el.find('input.multiselect__input');
                expect(inputElement[0]).to.have.property('disabled');
            });
            PropertyDetail.ruralWorksheetNutrientScore.then($el => {
                const inputElement = $el.find('input.multiselect__input');
                expect(inputElement[0]).to.have.property('disabled');
            });
            PropertyDetail.ruralWorksheetWaterQualityRating.then($el => {
                const inputElement = $el.find('input.multiselect__input');
                expect(inputElement[0]).to.have.property('disabled');
            });
        });
    });

});

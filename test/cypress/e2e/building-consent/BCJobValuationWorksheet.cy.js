import Model from '../../model/ValuationWorksheet.js';
import testFactory from '../../support/testFactory.js';

let bcJobPage;
describe('Building Consent Job', { defaultCommandTimeout: 3000 }, function () {
    before('load test factory property', function () {
        const propertyWithBuildingConsentInputBody = testFactory.getPropertyWithBuildingConsentInputBody('Pending');
        cy.request(propertyWithBuildingConsentInputBody)
            .then((response) => {
                console.log(response.body);
                const property = response.body.data[0];
                const ratingValuationId = property.children[0].attributes.ratingValuationId;
            /*
                TODO: TEST FACTORY is returning an invalid ratingValuation
                bcJobPage = `/roll-maintenance/rating-valuation/${ratingValuationId}/worksheet`;
            */
                bcJobPage = `/roll-maintenance/rating-valuation/608c4290-a9cd-4470-9f1e-b5c711f39efa/worksheet`;
            });
    });

    context('BC Job Valuation Worksheet and Saving the job', function () {
        before('Visits page', function () {
            cy.visitWithLogin(bcJobPage);
        });


        it('Filling in the Improvements details, Land, Adopted Values, and Saving the job', function () {

            // Improvement
            //Principal Buildings
            Model.WorksheetTablePrimaryBuildings.row(0).valuePerSquareMetre.clear().type('179280');
            Model.WorksheetTablePrimaryBuildings.row(0).area.clear().type('100');

            //Other Buildings
            Model.WorksheetTableOtherBuildings.row(0).valuePerSquareMetre.clear().type('100');
            Model.WorksheetTableOtherBuildings.row(0).area.clear().type('100');

            //Other Improvements
            Model.WorksheetTableOtherImprovements.row(0).valuePerSquareMetre.clear().type('100');
            Model.WorksheetTableOtherImprovements.row(0).area.clear().type('100');

            //Land
            Model.WorksheetTableLand.row(0).area.clear().type('100');
            Model.WorksheetTableLand.row(0).valuePerSquareMetre.clear().type('100');

            // Adopted Values
            Model.WorksheetTableAdoptedValues.capitalValue.clear().type('285000');
            Model.WorksheetTableAdoptedValues.landValue.clear().type('0');

            //Save Job
            Model.Action.saveButton.click();
            cy.wait(2000);

            //Alert close
            Model.Action.alertButtonClose.contains('Close').click({ force: true });
        });
    });

});
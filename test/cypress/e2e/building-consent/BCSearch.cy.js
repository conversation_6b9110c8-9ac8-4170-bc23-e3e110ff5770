import '@testing-library/cypress/add-commands'
import BCSearch from '../../model/BCSearch';
import Reports from '../../model/Reports';
const BCJobPage = 'roll-maintenance/consents';

describe('Building Consent Search Page', { defaultCommandTimeout: 3000 }, () => {
    beforeEach('Visits page', () => {
        cy.intercept("/web/rollMaintenance/searchActivities", {
            fixture: "bc-search-activity.json",
        }).as('searchActivity');
    });
    before('Visits page', () => {
        cy.visitWithLogin(BCJobPage);
    });
    context('Check for all the text boxes, dropdowns and buttons to exist.', () => {

        it('Territorial Authorities section should exist and be visible', () => {
            BCSearch.elements.territorialAuthorities
                .should('exist')
                .and('be.visible')
                .and('contain', 'Territorial Authorities');
        });

        it('Activity Statuses section should exist and be visible', () => {
            BCSearch.elements.activityStatuses
                .should('exist')
                .and('be.visible')
                .and('contain', 'Activity Statuses');
        });

        it('Plan Status section should exist and be visible', () => {
            BCSearch.elements.planStatus
                .should('exist')
                .and('be.visible')
                .and('contain', 'Plan Status');
        });

        it('Construction Comp section should exist and be visible', () => {
            BCSearch.elements.constructionComp
                .should('exist')
                .and('be.visible')
                .and('contain', 'Construction Comp');
        });

        it('Categories section should exist and be visible', () => {
            BCSearch.elements.categories
                .should('exist')
                .and('be.visible')
                .and('contain', 'Categories');
        });

        it('BC Numbers section should exist and be visible', () => {
            BCSearch.elements.bcNumber
                .should('exist')
                .and('be.visible')
                .and('contain', 'BC Number');
        });

        it('QPID section should exist and be visible', () => {
            BCSearch.elements.qpid
                .should('exist')
                .and('be.visible')
                .and('contain', 'QPID');
        });

        it('Roll Number section should exist and be visible', () => {
            BCSearch.elements.rollNumber
                .should('exist')
                .and('be.visible')
                .and('contain', 'Roll Number');
        });

        it('Assessment Number section should exist and be visible', () => {
            BCSearch.elements.assessment
                .should('exist')
                .and('be.visible')
                .and('contain', 'Assessment Number');
        });

        it('Suffix section should exist and be visible', () => {
            BCSearch.elements.suffix
                .should('exist')
                .and('be.visible')
                .and('contain', 'Suffix');
        });

        it('BC Issue Date section should exist and be visible', () => {
            BCSearch.elements.bcIssueDate
                .should('exist')
                .and('be.visible')
                .and('contain', 'BC Issue Date');
        });

        it('BC Entered Date section should exist and be visible', () => {
            BCSearch.elements.bcEnteredDate
                .should('exist')
                .and('be.visible')
                .and('contain', 'BC Entered Date');
        });

        it('Sale Group Codes section should exist and be visible', () => {
            BCSearch.elements.saleGroupCodes
                .should('exist')
                .and('be.visible')
                .and('contain', 'Sales Groups & ROLLS');
        });

        it('Valuers section should exist and be visible', () => {
            BCSearch.elements.valuers
                .should('exist')
                .and('be.visible')
                .and('contain', 'Valuers');
        });

        it('BC Cost section should exist and be visible', () => {
            BCSearch.elements.bcCost
                .should('exist')
                .and('be.visible')
                .and('contain', 'BC Cost');
        });

        it('BC Due Date section should exist and be visible', () => {
            BCSearch.elements.bcDueDate
                .should('exist')
                .and('be.visible')
                .and('contain', 'BC Due Date');
        });

        it('Actioned Date section should exist and be visible', () => {
            BCSearch.elements.actionedDate
                .should('exist')
                .and('be.visible')
                .and('contain', 'Actioned Date');
        });

        it('Notes for Valuer section should exist and be visible', () => {
            BCSearch.elements.notesForValuer
                .should('exist')
                .and('be.visible')
                .and('contain', 'Notes for Valuer');
        });

        it('Needs Inspection section should exist and be visible', () => {
            BCSearch.elements.needsInspection
                .should('exist')
                .and('be.visible')
                .and('contain', 'Needs Inspection');
        });

        it('Clear All should exist and be visible', () => {
            BCSearch.elements.clearAll
                .should('exist', { timeout: 10000 });
        });

        it('Map button should exist and be visible', () => {
            BCSearch.elements.mapButton
                .should('exist', { defaultCommandTimeout: 3000 })
                .and('be.visible')
                .and('contain', 'Map');
        });

        it('View Summary Report should exist and be visible', () => {
            BCSearch.elements.createConsentInspectionReport
                .should('exist', { defaultCommandTimeout: 3000 })
                .and('be.visible')
                .and('contain', 'Create Consent Inspection Report');
        });

        it('Export Button should exist and be visible', () => {
            BCSearch.elements.exportBtn
                .should('exist',)
                .and('be.visible');

        });

        it('Clear Button should exist and be visible', () => {
            BCSearch.elements.clearButton
                .should('exist',)
                .and('be.visible');

        });

        it('Search Button should exist and be visible', () => {
            BCSearch.elements.searchButton
                .should('exist',)
                .and('be.visible');

        });

    })

    context('Validating the dropdowns and their values exist.', () => {
        context('Territorial Authorities', () => {

            it('Territorial Authorities -Validate the error message when  field for Territorial Authorities is not selected', () => {
                BCSearch.elements.territorialAuthorities
                    .should('be.visible')
                    .click();
                BCSearch.elements.territorialAuthoritiesCheckbox
                    .eq(0).uncheck()
                    .should('not.be.checked');
                BCSearch.elements.territorialAuthoritiesErrorMessage
                    .should('be.visible')
                    .and('contain', 'Territorial Authority is required');
                BCSearch.elements.territorialAuthorities
                    .should('be.visible')
                    .click();
            });

            it('Territorial Authorities - Validating the dropdown by selecting \'Select All\' and deselecting \'Select All\' .', () => {
                BCSearch.elements.territorialAuthorities
                    .should('be.visible')
                    .click();
                BCSearch.elements.territorialAuthoritiesCheckbox
                    .eq(0).check()
                    .should('be.checked');
                BCSearch.elements.territorialAuthoritiesCheckbox
                    .should('exist')
                    .and('have.length', '78');
                BCSearch.elements.territorialAuthoritiesMultiselectDropDown
                    .should('be.visible')
                    .and('contain', "All TA\'s Selected (77)");
                BCSearch.elements.territorialAuthoritiesCheckbox
                    .eq(0).uncheck()
                    .should('not.be.checked');
                BCSearch.elements.territorialAuthoritiesMultiselectDropDown
                    .should('be.visible')
                    .and('contain', "Select Authorities");
                BCSearch.elements.territorialAuthorities
                    .should('be.visible')
                    .click();

            });
        })
        context('Activity status', () => {

            it('Activity Statuses - Validate \'Clear All\' activity status', () => {
                BCSearch.elements.activityStatusesClear
                    .click()
                    .should('be.visible');
            });
            it(' Activity Statuses - Validate the dropdown field for activity status. ', () => {
                BCSearch.elements.activityStatusesAllActive
                    .click()
                    .should('be.visible');
                BCSearch.elements.activityStatusesMultiselect
                    .then(($el) => {
                        expect($el).to.have.length(4)
                        expect($el[0]).to.contain('Confirmation Required')
                        expect($el[1]).to.contain('Pending')
                        expect($el[2]).to.contain('Ready To Value')
                        expect($el[3]).to.contain('Setup Required')
                        BCSearch.elements.activityStatusesClear.click()
                            .should('be.visible');
                    })

            });
            it('Activity Statuses -Selecting an options', () => {
                BCSearch.elements.activityStatusesClear
                    .click()
                    .should('be.visible');
                BCSearch.elements.activityStatusDropdown
                    .click();
                BCSearch.elements.activityStatusDropdownMultiselect
                    .eq(1).click();
                BCSearch.elements.activityStatusesMultiselect.then(($el) => {
                    expect($el).to.have.length(1);
                    expect($el[0]).contain('Complete');
                })

            })
            it.skip('Activity Statuses has Setup Required as default', () => {
                cy.visitWithLogin(RollMaintenance.href)
                    .contains('Activity Statuses')
                    .contains("Setup Required")
                    .then(element => {
                        expect(element).to.exist.and.to.be.visible;
                    });
            });
        });
        context('Plan Status:', () => {
            it('Plan Status- - Validate the dropdown field for Plan status.', () => {
                BCSearch.elements.planStatusClear.click();
                BCSearch.elements.planStatusMultiSelectCombo.click({ force: true });
                BCSearch.elements.planStatusMultiSelectPlanStatus
                    .eq(0).click({ force: true })
                    .should('be.visible')
                    .and('contain', 'Plans Drawn');
                BCSearch.elements.planStatusMultiSelectPlanStatus
                    .eq(1).click({ force: true })
                    .should('be.visible')
                    .and('contain', 'Plans Required');
                BCSearch.elements.planStatusMultiSelectPlanStatus
                    .eq(2).click({ force: true })
                    .should('be.visible')
                    .and('contain', 'Plans Unavailable');
                BCSearch.elements.planStatusMultiSelectPlanStatus
                    .eq(3).click({ force: true })
                    .should('be.visible')
                    .and('contain', 'Plans Not Required');
                BCSearch.elements.planStatusMultiSelectPlanStatus
                    .eq(4).click({ force: true })
                    .should('be.visible')
                    .and('contain', 'Plans Requested with TA');
                BCSearch.elements.planStatusMultiSelectPlanStatus
                    .eq(5).click({ force: true })
                    .should('be.visible')
                    .and('contain', 'Plans Unknown');
                BCSearch.elements.planStatusClear.click(0, 0);
            });
        })
        context('Construction Complete', () => {
            it('Construction Complete - Validate the dropdown field for Construction Complete. ', () => {
                BCSearch.elements.constructionCompDropdown
                    .click().should('be.visible');
                BCSearch.elements.constructionCompSelectOptions
                    .then($els => Cypress.$.map($els, (el) => el.innerText.trim()))
                    .should(values => {
                        expect(values).to.deep.eq(["Yes", "No"]);
                    })
                BCSearch.elements.constructionCompSelectOptions
                    .eq(0).click({ force: true })
                    .should('be.visible')
                    .and('contain', 'Yes');
                BCSearch.elements.constructionCompSelectOptions
                    .eq(1).click({ force: true })
                    .should('be.visible')
                    .and('contain', 'No');
            });
        })
        context('Valuers Dropdown ', () => {
            it('should successfully select valuers', () => {
                BCSearch.elements.valuersDropdownBox
                    .click({ force: true });
                BCSearch.elements.selectValuersOptions1
                    .click({ force: true });
                BCSearch.elements.selectValuersOptions2
                    .click({ force: true });
                BCSearch.elements.selectValuersOptions3
                    .click({ force: true })
                BCSearch.elements.bcCost
                    .should('be.visible')
                    .click({ force: true });
            });
        });
        context('Notes for Valuer', () => {
            it('Notes for valuers- Validate the dropdown field for Notes for valuers.', () => {
                BCSearch.elements.notesForValuer
                    .click({ force: true }).should('be.visible')
                BCSearch.elements.notesForValuerDropDownSelectOptions
                    .eq(0).click({ force: true });
                BCSearch.elements.notesForValuerDropDown
                    .should('be.visible')
                    .and('contain', 'Yes');
                BCSearch.elements.notesForValuerDropDownSelectOptions
                    .eq(1).click({ force: true })
                BCSearch.elements.notesForValuerDropDown
                    .should('be.visible')
                    .and('contain', 'No');
            });

        })
        context('Needs Inspection', () => {
            it('Needs Inspection- Validate the dropdown field for Needs Inspection', () => {
                BCSearch.elements.needsInspection
                    .click({ force: true }).should('be.visible');
                BCSearch.elements.needsInspectionDropDownSelectOptions
                    .eq(0).click({ force: true });
                BCSearch.elements.needsInspectionDropDown
                    .should('be.visible')
                    .and('contain', 'Yes');
                BCSearch.elements.needsInspectionDropDownSelectOptions
                    .eq(1).click({ force: true });
                BCSearch.elements.needsInspectionDropDown
                    .should('be.visible')
                    .and('contain', 'No');
            });
        })

    });

    context('Validating Date Range', () => {
        it('BC Issue Date - Validate the date range field for BC Issue Date', () => {
            BCSearch.elements.bcIssueDateFrom
                .should('be.visible')
                .type('01/01/2024');
            BCSearch.elements.bcIssueDateTo
                .should('be.visible')
                .type('01/01/2023');
            BCSearch.elements.bcEnteredDate
                .click({ force: true }).should('be.visible');
            BCSearch.elements.bcIssueDateErrorMessage
                .should('be.visible')
                .and('contain', 'The To date must be equal to or greater than the From date.');
            BCSearch.elements.clearButton.click();
        });

        it('BC Entered Date - Validate the date range field for BC Entered Date', () => {
            BCSearch.elements.bcEnteredDate
                .click({ force: true }).should('be.visible');
            BCSearch.elements.bcEnteredDateFrom
                .should('be.visible')
                .type('01/01/2024');
            BCSearch.elements.bcEnteredDateTo
                .should('be.visible')
                .type('01/01/2023');
            BCSearch.elements.bcDueDate
                .click({ force: true }).should('be.visible');
            BCSearch.elements.bcEnteredDateErrorMessage
                .should('be.visible')
                .and('contain', 'The To date must be equal to or greater than the From date.');
            BCSearch.elements.clearButton.click();
        });

        it('BC Due Date - Validate the date range field for BC Due Date', () => {
            BCSearch.elements.bcDueDate
                .click({ force: true }).should('be.visible');
            BCSearch.elements.bcDueDateFrom
                .should('be.visible')
                .type('01/01/2024');
            BCSearch.elements.bcDueDateTo
                .should('be.visible')
                .type('01/01/2023');
            BCSearch.elements.notesForValuer.click({ force: true }).should('be.visible')
            BCSearch.elements.bcDueDateErrorMessage
                .should('be.visible')
                .and('contain', 'The To date must be equal to or greater than the From date.');
            BCSearch.elements.clearButton.click();
        });

        it('Actioned Date - Validate the date range field for Actioned Date', () => {
            BCSearch.elements.actionedDate
                .click({ force: true }).should('be.visible');
            BCSearch.elements.actionedDateFrom
                .should('be.visible')
                .type('01/01/2024',{ force: true });
            BCSearch.elements.actionedDateTo
                .should('be.visible')
                .type('01/01/2023',{ force: true });
            BCSearch.elements.notesForValuer.click({ force: true }).should('be.visible');
            cy.wait(2000);
            BCSearch.elements.actionedDateErrorMessage
                .should('be.visible')
                .and('contain', 'The To date must be equal to or greater than the From date.');
            BCSearch.elements.clearButton.click();
        });
    });

    context('BC search criteria', () => {
        it('should randomly check selection Criteria', () => {
            BCSearch.elements.territorialAuthorities
                .should('be.visible').click();
            BCSearch.elements.territorialAuthoritiesCheckbox
                .eq(0).uncheck();
            BCSearch.elements.territorialAuthoritiesCheckbox
                .then($item => {
                    return Cypress._.sampleSize($item.toArray(), 2);
                })
                .should('have.length', 2)
                .click({ multiple: true });
            BCSearch.elements.searchButton.click({ force: true });
            cy.wait(8000);
        });
    })

    context('Sorting the Result Column', () => {
        it('Sorting the columns by Val Ref', () => {
            BCSearch.elements.valRefTableHeader
                .click();
            cy.wait('@searchActivity');
            BCSearch.elements.valRefHeaderIcon
                .should('be.visible');
        });

        it('Sorting the columns by BC Number', () => {
            BCSearch.elements.bcNumberTableHeader
                .click();
            cy.wait('@searchActivity');
            BCSearch.elements.bcNumberHeaderIcon
                .should('be.visible');
        });

        it('Sorting the columns by BC Cost', () => {
            BCSearch.elements.bcCostTableHeader
                .click();
            cy.wait('@searchActivity');
            BCSearch.elements.bcCostHeaderIcon
                .should('be.visible');
        });

        it('Sorting the columns by Description', () => {
            BCSearch.elements.descriptionHeader
                .click();
            cy.wait('@searchActivity');
            BCSearch.elements.descriptionHeaderIcon
                .should('be.visible');
        });

        it('Sorting the columns by Consent Due date', () => {
            BCSearch.elements.consentHeader
                .click({ force: true });
            cy.wait('@searchActivity');
            BCSearch.elements.consentHeaderIcon
                .should('be.visible');
        });
    });

    context('Sales Groups & ROLLS', () => {
        it('Should validate the content of the Sales Groups & ROLLS section', () => {
            BCSearch.elements.salesGroupsAndRollsButton
                .should('be.visible')
                .contains('Sales Groups & ROLLS');
        });
        it('should validate the Sales Groups & ROLLS ', () => {
            BCSearch.elements.salesGroupsAndRollsButton
                .should('be.visible')
                .click({ force: true });
            BCSearch.elements.alertModal
                .should('be.visible')
                .contains('SALES GROUPS AND ROLLS')
            BCSearch.elements.salesGroupsSetRollsButton
                .should('be.visible')
                .contains('SET ROLLS')
            BCSearch.elements.salesGroupsClearButton
                .should('be.visible')
                .contains('CLEAR')
            BCSearch.elements.salesGroupFormClose
                .should('be.visible')
                .click({ force: true });

        });

        it('should randomly check any 2 Territorial Authorities Criteria for the sales group and rolls', () => {
            BCSearch.elements.salesGroupsAndRollsButton
                .should('be.visible')
                .click({ force: true });
            BCSearch.elements.salesGroupTerritorialAuthoritiesCheckbox
                .then($item => {
                    return Cypress._.sampleSize($item.toArray(), 2);
                })
                .should('have.length', 2)
                .click({ multiple: true });
            BCSearch.elements.salesGroupsClearButton
                .should('be.visible')
                .contains('CLEAR')
                .click({ force: true });
        });


        it('should randomly check any 2 sales Group Category for the sales group and rolls', () => {
            BCSearch.elements.salesGroupsAndRollsButton
                .should('be.visible')
                .click({ force: true });
            BCSearch.elements.salesGroupSGCheckbox
                .then($item => {
                    return Cypress._.sampleSize($item.toArray(), 2);
                })
                .should('have.length', 2)
                .click({ multiple: true });
            BCSearch.elements.salesGroupsClearButton
                .should('be.visible')
                .contains('CLEAR')
                .click({ force: true });
            BCSearch.elements.salesGroupFormClose
                .should('be.visible')
                .click({ force: true });
        });

    });
    context('Assign Valuers', () => {
        it('should successfully assign bulk valuers', () => {
            cy.wait(4000);
            BCSearch.elements.checkboxHeader.children().eq(0).check({ force: true });
            BCSearch.elements.checkboxHeader.children().eq(1).check({ force: true });
            BCSearch.elements.assignValuers
                .should('be.visible')
                .click({ force: true });
            BCSearch.elements.alertModal
                .eq(0)
                .and('be.visible')
                .and('contain', 'Assign Valuers')
            BCSearch.elements.alertModal
                .eq(1)
                .and('be.visible')
                .and('contain', '2 of 2 consents are able to be assigned.')
            BCSearch.elements.assignValuersDropDown.click({ force: true });
            BCSearch.elements.multiselectValuers.click({ force: true });
            BCSearch.elements.bulkAssignValuersCheckBox
                .check({ force: true })
                .should('be.visible')
                .and('be.checked');
            BCSearch.elements.confirmAssignValuersButton
                .should('be.visible')
                .click({ force: true });
            BCSearch.elements.closeAssignValuersButton
                .should('be.visible')
                .click({ force: true });
        });
    });

    context('Plans Drawn', () => {
        it('should successfully view the plans drawn for monarch', () => {
            BCSearch.elements.floorPlanDrawn
            .children().eq(0)
            .should('be.visible')
            .should('have.attr', 'href','/roll-maintenance/floorPlan/3213379')

        });
    });

    context('Export Report', () => {
        it('should successfully view the export report', () => {
            cy.wait(4000);
            BCSearch.elements.exportBtn.children().eq(0)
                .click({ force: true });
            cy.wait(8000);
            BCSearch.elements.alertModal
                .and('be.visible')
                .and('contain', 'Export Scheduled')
                .and('contain', 'Your export has been acknowledged and can be viewed in View My Reports.');
            BCSearch.elements.alertModalOk
                .click({ force: true });

        });

        it('should successfully view the Building Consent Report', () => {
            cy.wait(4000);
            BCSearch.elements.exportBtn.children().eq(0)
                .click({ force: true });
            cy.wait(8000);
            BCSearch.elements.alertModal
                .and('be.visible')
                .and('contain', 'Export Scheduled')
                .and('contain', 'Your export has been acknowledged and can be viewed in View My Reports.');
            BCSearch.elements.alertModalViewMyReport
                .click({ force: true });
            cy.wait(16000);
            Reports.elements.myReportsRefreshButton
                .click({ force: true });
            cy.wait(1000);
            Reports.elements.jobFirstColumns
                .should('be.visible')
                .and('contain', 'Building Consent Export');
            Reports.elements.jobFirstColumnsFileSize
                .invoke('text')
                .then(parseFloat)
                .should('be.gt', 100);
        });
    });
});

describe('Building Consent Search Page - Export Limit Exceeded Error', () => {
    beforeEach('Visits page', () => {
        cy.intercept("/web/rollMaintenance/searchActivities", {
            fixture: "bc-search-export-report.json",
        }).as('export');
    });
    before('Visits page', () => {
        cy.visitWithLogin(BCJobPage);
    });
    context('Export Report - Export Limit Exceeded Error', () => {
        it('should display error message when export limit is exceeded', () => {
            BCSearch.elements.activityStatusesClear
                .click()
                .should('be.visible');
            BCSearch.elements.searchButton.dblclick({ force: true })
            cy.wait('@export')
            BCSearch.elements.exportBtn.children().eq(0)
                .click({ force: true });
            BCSearch.elements.alertModal
                .and('be.visible')
                .and('contain', 'Export limit exceeded')
                .and('contain', 'A maximum of ')
                .and('contain', 'results may be exported.  Please refine your search criteria.');
            BCSearch.elements.alertModalExportLimitExceededOk
                .click({ force: true });
        });
    });
});

describe('Inactive Property', () => {
    before('Visits the page and sets up intercept', () => {
        cy.intercept("/web/rollMaintenance/searchActivities", {
                    fixture: "bc-search-inactive-property.json",
                }).as('inactiveProperty');
        cy.visitWithLogin(BCJobPage);
    });
    context('Check Inactive Property Visibility and Attributes', () => {
        it('should verify the inactive property is displayed with correct class', () => {
            cy.wait('@inactiveProperty', { timeout: 8000 });
            BCSearch.elements.activityStatusesClear
            .click()
            .should('be.visible');
            BCSearch.elements.searchButton.dblclick({ force: true })
            BCSearch.elements.inactiveProperty
            .should('be.visible')
            .should('have.attr', 'class','resultsRow activity-list__row red-highlight');

        });
    });
});

describe('Consent Search - Show Modals', () => {
    before('Visits page', () => {
        cy.visitWithLogin(BCJobPage);
    });
    context('Testing Modal Pop Ups', () => {
        it('should pop up a modal if no properties are selected when trying to create an consent inspection report', () => {
            BCSearch.elements.createConsentReportButton.contains('Create Consent Inspection Report').click();

            BCSearch.elements.alertModal.should('be.visible').contains('No consent selected.');
            BCSearch.elements.modalCloseButton.click();
        });

        it('should pop up a modal if no properties are selected when trying to view the map for selected properties', () => {
            BCSearch.elements.mapButton
                .contains('Map')
                .click();

            BCSearch.elements.alertModal.should('be.visible').contains('Show Map');
            BCSearch.elements.modalCloseButton.click();
        });

        it('should select over 100 properties and a modal should pop up stating that more than 100 properties have been selected', () => {
            cy.wait(2500);
            BCSearch.elements.selectAll.click();
            BCSearch.elements.nextPage.click();
            cy.wait(2500);
            BCSearch.elements.selectAll.click();

            BCSearch.elements.mapButton
                .contains('Map')
                .click();
            BCSearch.elements.alertModal.should('be.visible').contains('Limit exceeded');
            BCSearch.elements.modalCloseButton.click();
            BCSearch.elements.clearAll.click();
        });
    });

    context('Testing Show Map', () => {
        it('should select one property and show the map with said property selected', () => {
            BCSearch.elements.checkboxHeader.eq(0).click();
            BCSearch.elements.mapButton
                .contains('Map')
                .click().then(() => {
                    BCSearch.elements.mapButton.invoke('attr','map-url').then((mapurl) => {
                        cy.visit(mapurl);
                    });
                });
            cy.visitWithLogin(BCJobPage);
        });

        it('should select all the properties on the current page and show the map with said properties selected', () => {
            cy.wait(1500);
            BCSearch.elements.selectAll.click();
            BCSearch.elements.mapButton
                .contains('Map')
                .click().then(() => {
                    BCSearch.elements.mapButton.invoke('attr','map-url').then((mapurl) => {
                        cy.visit(mapurl);
                    });
                });
        });
    });
});

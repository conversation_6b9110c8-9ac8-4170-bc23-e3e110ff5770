import BCDetails from '../../model/BCDetails';
import testFactory from '../../support/testFactory.js';

// test factory api generates a unique building consent for the null statuses
const BUILDING_CONSENT_IDS = {
    'Setup Required': null,
    'Confirmation Required': 2043000,
    'Ready to Value': null,
    'Pending': 2279404,
    'Completed': null,
    'Cancelled': 1972639,
};

const address = (function () {
    return {
        visitQpid: 163921,
        street: '178 Victoria Avenue,  Remuera, Auckland City'
    }
})();

const getRandomString = () => Math.random().toString(36).slice(2, 10);
const INVALID_STRING = 'INVALID_STRING';
let property;
let qpid;

describe('BC Details', { defaultCommandTimeout: 25000 }, function () {
    before('get test factory building consent', function () {
        const propertyWithBuildingConsentInputBody = testFactory.getPropertyWithBuildingConsentInputBody();
        cy.request(propertyWithBuildingConsentInputBody)
            .then((response) => {
                property = response.body.data[0];
                qpid = property.id;
                BUILDING_CONSENT_IDS['Setup Required'] = property.children[0].attributes.buildingConsentId;
                const readyToValueInputBody = testFactory.addBuildingConsentToPropertyInputBody(qpid, 'Ready to Value');
                const completedInputBody = testFactory.addBuildingConsentToPropertyInputBody(qpid, 'Completed');
                cy.request(readyToValueInputBody)
                    .then((response) => {
                        BUILDING_CONSENT_IDS['Ready to Value'] = response.body.data[0].children[0].attributes.buildingConsentId;
                        cy.request(completedInputBody)
                            .then((response) => {
                                BUILDING_CONSENT_IDS['Completed'] = response.body.data[0].children[0].attributes.buildingConsentId;
                                console.log(BUILDING_CONSENT_IDS);
                            });
                    });

            });
    });

    context('Visits BCDetails from Property Details page', function () {
        before('Visits page', function () {
            cy.visitQpid(qpid);
        });

        it('Visits most recent BCDetails', function () {
            cy.wait(10000);
            BCDetails.elements.masterDetailsPropertyToolbarRollMaintenanceTabLink
                .click({ force: true });
            BCDetails.elements.activityListConsentNumber.then(elem => {
                cy.visit(elem[0].href, {
                    onBeforeLoad(win) {
                        cy.stub(win, 'open');
                    }
                });
                cy.url().should('equal', elem[0].href);
            });
        });
    });

    context('Visit Setup Required BCDetails Page and Analyse BCDetails', function () {
        before('Visits page', function () {
            cy.visitWithLogin(BCDetails.getBCDetailsPageHref(BUILDING_CONSENT_IDS['Setup Required']));
        });

        it('Visits page', function () {
            cy.url().should('equal', env.config.baseUrl + BCDetails.getBCDetailsPageHref(BUILDING_CONSENT_IDS['Setup Required']));
        });

        context('Check elements exist', function () {
            it('Title section should exist and be visible and have correct text', function () {
                BCDetails.elements.title
                    .should('exist')
                    .and('be.visible')
                    .invoke('text')
                    .then((text) => {
                        const cleanedText = text.replace(/(\r\n|\n|\r)/gm, " ").trim();
                        expect(cleanedText).to.equal('Building Consent');
                    });
            });
            it('Property Summary Header section should exist and be visible', function () {
                BCDetails.elements.propertySummaryHeader
                    .should('exist')
                    .and('be.visible');
            });

            it('Consent Number should exist and be visible', function () {
                BCDetails.elements.consentNumberInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Territorial Authority should exist and be visible and be read only', function () {
                BCDetails.elements.territorialAuthorityInput
                    .should('exist')
                    .and('be.visible')
                    .and('have.attr', 'readonly', 'readonly')
                    .invoke('val')
                    .should('exist');
            });

            it('Cost should exist and be visible', function () {
                BCDetails.elements.costInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Floor Area should exist and be visible', function () {
                BCDetails.elements.floorAreaInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Estimated Inspection Ready Date should exist and be visible', function () {
                BCDetails.elements.estimatedInspectionReadyDateInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Location (Street Number) should exist and be visible', function () {
                BCDetails.elements.streetNumberInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Location (Street Name) should exist and be visible', function () {
                BCDetails.elements.streetNameInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Issue Date should exist and be visible', function () {
                BCDetails.elements.consentDateInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Date Entered should exist and be visible and be read only', function () {
                BCDetails.elements.enteredDateInput
                    .should('exist')
                    .and('be.visible')
                    .and('have.attr', 'readonly', 'readonly')
                    .invoke('val')
                    .should('exist');
            });

            it('Valuer should exist and be visible and be read only', function () {
                BCDetails.elements.valuerInput
                    .should('exist')
                    .and('be.visible')
                    .and('have.attr', 'readonly', 'readonly')
                    .invoke('val')
                    .should('exist');
            });

            it('Owner or Consent Applicant should exist and be visible', function () {
                BCDetails.elements.applicantInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Construction Completion Date should exist and be visible', function () {
                BCDetails.elements.constructionCompletionDate
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Date Consent Actioned should exist and be visible and be read only', function () {
                BCDetails.elements.consentActionedInput
                    .should('exist')
                    .and('be.visible')
                    .and('have.attr', 'readonly', 'readonly')
                    .invoke('val')
                    .should('exist');
            });

            it('Consent Description should exist and be visible', function () {
                BCDetails.elements.consentDescriptionInput
                    .should('exist')
                    .and('be.visible')
                        .invoke('val')
                        .should('exist');
                    });

            it('Inspection Status should exist and be visible', function () {
                BCDetails.elements.inspectionStateDropdown
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Nature Of Works should exist and be visible', function () {
                BCDetails.elements.natureOfWorksDropdown
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });

            it('Notes should exist and visible', function () {
                BCDetails.elements.notesInput
                    .should('exist')
                    .and('be.visible')
                    .invoke('val')
                    .should('exist');
            });
        });

        context('Confirm data types are present', function () {
            let numericValue;
            let estimatedInspectionReadyDate;
            it('Location (Street Name) should be a string', function () {
                BCDetails.elements.streetNameInput
                    .should('have.attr', 'type', 'text')
                    .invoke('val')
                    .should('be.a', 'string');
            });

            it('Cost should only be a number, does not accept non-numeric values', function () {
                BCDetails.elements.costInput
                    .should('have.attr', 'type', 'number')
                    .invoke('val')
                    .should('match', /^\d+$/)
                    .then((value) => {
                        numericValue = Number(value);
                        expect(numericValue).to.be.a('number');
                    });
                BCDetails.elements.costInput
                    .type(INVALID_STRING)
                    .invoke('val')
                    .then((value) => {
                        expect(Number(value)).to.equal(numericValue);
                    });
            });

            it('Estimated Inspection Ready Date should be a date with the correct date formatting: "dd/mm/yyyy"', function () {
                BCDetails.elements.estimatedInspectionReadyDateInput
                    .should('have.attr', 'type', 'text')
                    .invoke('val')
                    .should('be.a', 'string')
                    .then((value) => {
                        const regex = /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/;
                        estimatedInspectionReadyDate = value;
                        expect(estimatedInspectionReadyDate).to.match(regex);
                    });
                BCDetails.elements.estimatedInspectionReadyDateInput
                    .clear()
                    .type(INVALID_STRING + '{enter}');
                BCDetails.elements.validationHeaders.errors
                    .find('li')
                    .should('have.text', '\n                - Estimated Inspection Ready Date must be dd/mm/yyyy.\n            ')
                BCDetails.elements.validationMessages.estimatedInspectionReadyDate
                    .should('exist')
                    .and('have.text', '\n    must be dd/mm/yyyy\n');
            });

            after('Reset Estimated Inspection Ready Date', function () {
                BCDetails.elements.estimatedInspectionReadyDateInput
                    .clear()
                    .type(estimatedInspectionReadyDate + '{enter}');
            });
        });

        context('Plan Status', function () {
            it("Enters 'Plans Drawn'", function () {
                BCDetails.enterPlanStatus('Plans Drawn');
            });

            it("Enters 'Plans Unknown'", function () {
                BCDetails.enterPlanStatus('');
            });

            it("Enters 'Plans Required'", function () {
                BCDetails.enterPlanStatus('Plans Required');
            });

            it("Enters 'Plans Unavailable'", function () {
                BCDetails.enterPlanStatus('Plans Unavailable');
            });

            it("Enters 'Plans Not Required'", function () {
                BCDetails.enterPlanStatus('Plans Not Required');
            });

            it("Enters 'Plans Requested with TA'", function () {
                BCDetails.enterPlanStatus('Plans Requested with TA');
            });

            it('Status Panel Header should exist and be visible', function () {
                BCDetails.elements.statusPanelHeader
                    .should('exist')
                    .and('be.visible');
            });
        });

        context('Notes for Valuer', function () {
            it('Tick on', function () {
                BCDetails.elements.statusCheckboxes.needsMoreInformation.click({ force: true });
                BCDetails.elements.statusList.items.needsMoreInformation.should('exist');
                BCDetails.elements.statusList.labels.needsMoreInformation
                    .should('exist')
                    .and('have.text', 'Notes for Valuer');
            });
            it('Tick off', function () {
                BCDetails.elements.statusCheckboxes.needsMoreInformation.click({ force: true });
                BCDetails.elements.statusList.labels.needsMoreInformation
                    .should('not.exist');
            });
        });

        context('Needs Inspection', function () {
            it('Tick on', function () {
                BCDetails.elements.statusCheckboxes.needsInspection.click({ force: true });
                BCDetails.elements.statusList.items.needsInspection.should('exist');
                BCDetails.elements.statusList.labels.needsInspection
                    .should('exist')
                    .and('have.text', 'Needs Inspection');
            });
            it('Tick off', function () {
                BCDetails.elements.statusCheckboxes.needsInspection.click({ force: true });
                BCDetails.elements.statusList.labels.needsInspection.should('not.exist');
            });
        });

        context('Needs for Valuer and Inspection', function () {
            it('Tick on and items are in right order', function () {
                BCDetails.elements.statusCheckboxes.needsMoreInformation.click({ force: true });
                BCDetails.elements.statusCheckboxes.needsInspection.click({ force: true });
                BCDetails.elements.statusList.ul.its('length').should('be.gte', 1);
                BCDetails.elements.statusList.labels.needsInspection.should('exist');
                BCDetails.elements.statusList.labels.needsMoreInformation.should('exist');
                BCDetails.elements.statusList.warningLabels.eq(0).should('exist').and('have.text', 'Needs Inspection');
                BCDetails.elements.statusList.warningLabels.eq(1).should('exist').and('have.text', 'Notes for Valuer');
            });
            it('Tick off', function () {
                BCDetails.elements.statusCheckboxes.needsMoreInformation.click({ force: true });
                BCDetails.elements.statusCheckboxes.needsInspection.click({ force: true });
                BCDetails.elements.statusList.labels.needsInspection.should('not.exist');
                BCDetails.elements.statusList.labels.needsMoreInformation.should('not.exist');
                expect(BCDetails.elements.statusList.ul.warningLabels).to.equal(undefined);
            });
        });

        context('Construction Complete', function () {
            it('Tick on', function () {
                BCDetails.elements.statusCheckboxes.constructionComplete.click({ force: true });
                BCDetails.elements.statusCheckboxes.constructionComplete.click({ force: true });
                BCDetails.checkConstructionCompletionDate();
            });
            it('Tick off', function () {
                BCDetails.elements.statusCheckboxes.constructionComplete.click({ force: true });
                BCDetails.elements.statusList.labels.constructionInProgress.should('exist').and(
                    'have.text',
                    'Construction In Progress'
                );
                BCDetails.elements.constructionCompletionDate
                    .invoke('val')
                    .should('equal', '');
            });
        });

        context('Code of Compliance Issued', function () {
            it('Tick on', function () {
                BCDetails.elements.statusCheckboxes.complianceCertificateIssued
                    .click({ force: true });
                BCDetails.checkConstructionCompletionDate();
            });
            it('Tick off', function () {
                BCDetails.elements.statusCheckboxes.complianceCertificateIssued
                    .click({ force: true });
                BCDetails.checkConstructionCompletionDate();
            });
        });

        context('Buttons', function () {
            it('Setup Complete Button should exist and be visible', function () {
                BCDetails.elements.buttons.setupCompleteButton
                    .should('exist')
                    .and('be.visible');
            });
            it('Cancel Button should exist and be visible', function () {
                BCDetails.elements.buttons.cancelButton
                    .should('exist')
                    .and('be.visible');
            });
            it('Save Button should exist and be visible', function () {
                BCDetails.elements.buttons.saveButton
                    .should('exist')
                    .and('be.visible');
            });
            it('Save and Close Button should exist and be visible', function () {
                BCDetails.elements.buttons.saveAndCloseButton
                    .should('exist')
                    .and('be.visible');
            });
        });

        context('Saving', function () {
            let oldNotesValue;
            let newNotesValue;
            beforeEach('Updates notes value', function () {
                BCDetails.elements.notesInput
                    .invoke('val')
                    .then((value) => {
                        oldNotesValue = value;
                    });
                newNotesValue = 'TEST NOTES VALUE: ' + getRandomString();
                BCDetails.elements.notesInput
                    .clear()
                    .type(newNotesValue);
            });

            it('Cancel button should not save the form', function () {
                BCDetails.elements.buttons.cancelButton.click();
                cy.visitWithLogin(BCDetails.getBCDetailsPageHref(BUILDING_CONSENT_IDS['Setup Required']));
                BCDetails.elements.notesInput
                    .invoke('val')
                    .should('equal', oldNotesValue);
            });

            it('Save button should save the form', function () {
                cy.intercept("/web/rollMaintenance/saveActivity").as('saveActivity');

                BCDetails.elements.buttons.saveButton.click();

                cy.on('window:alert', (str) => {
                    expect(str).to.equal('Saved!');
                });

                BCDetails.elements.validationHeaders.errors
                    .should('not.exist');

                cy.wait('@saveActivity')
                    .then((interception) => {
                        expect(interception.response.statusCode).to.equal(200);
                        const responseBody = interception.response.body;
                        expect(responseBody).to.have.property('success', true);
                        expect(responseBody.value).to.have.property('notes', newNotesValue);
                    });

            });
        });
    });

    for (const status in (BUILDING_CONSENT_IDS)) {
        context(`Visit "${status}" BCDetails page`, function () {
            before('Visits page', function () {
                // const bcDetailsPage = BCDetails.getBCDetailsPageHref(BUILDING_CONSENT_IDS[status]);
                cy.visitWithLogin(BCDetails.getBCDetailsPageHref(BUILDING_CONSENT_IDS[status]));
            });

            it(`Visits page and has correct "${status}" status`, function () {
                cy.url().should('equal', env.config.baseUrl + BCDetails.getBCDetailsPageHref(BUILDING_CONSENT_IDS[status]));
                BCDetails.elements.statusPanelHeader.should('exist')
                    .and('have.text', status);
            });

            if (status === 'Setup Required') {
                return;
            }

            it(`Setup Complete Button should not exist`, function () {
                BCDetails.elements.buttons.setupCompleteButton.should('not.exist');
            });
        });
    }

});

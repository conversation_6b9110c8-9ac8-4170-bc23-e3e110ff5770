import Model from '../../model/ValuationWorksheet.js';

const ratingValuationId = '2f57fd6e-a92f-4508-932e-c8f259da36bd';

describe('Maori Land Building Consent Job', { defaultCommandTimeout: 3000 }, function () {
    context('Valuation Worksheet Validation', function () {
        before('Visits page', function () {
            const bcJobPage = `/roll-maintenance/rating-valuation/${ratingValuationId}/worksheet`;
            cy.visitWithLogin(bcJobPage);
        });

        it('When entering 0 for Multiple Owners and Site Significance, a warning pop-up should display upon saving.', function () {
            Model.WorksheetTableMaoriLand.multipleOwnerAdjusted.clear().type('0');
            Model.WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.clear().type('0');
            Model.WorksheetTableMaoriLand.siteSignificanceAdjusted.clear().type('0');
            Model.WorksheetTableMaoriLand.siteSignificanceRevisionAdjustment.clear().type('0');
            Model.Action.saveButton.click();
            Model.Action.warningsPopUp.should('exist').and('be.visible');
        });

        it('When Adopted Capital Value is less than Adopted Land Value, an error pop-up should display upon saving.', function () {
            Model.Action.closeWarningsPopUpBtn.click();
            Model.WorksheetTableMaoriAdoptedValues.RatingValues.capitalValue.clear().type('10');
            Model.WorksheetTableMaoriAdoptedValues.RatingValues.landValue.clear().type('100');
            Model.Action.saveButton.click();
            Model.Action.errorsPopUp.should('exist').and('be.visible');
        });
    });
});

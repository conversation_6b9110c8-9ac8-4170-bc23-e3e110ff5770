import '@testing-library/cypress/add-commands'
import BCSearch from '../../model/BCSearch';
import BCDraftPropertyDetails from '../../model/BCDraftPropertyDetails';
import BCJobReport from '../../model/BCJobReport';
import testFactory from '../../support/testFactory.js';

let bcJobPage;
const bcJobSearchPage = '/roll-maintenance/consents';
describe('Building Consent Job', { defaultCommandTimeout: 3000 }, function () {
    const generateRandomNumber = () => Cypress._.random(0, 99);
    const id = generateRandomNumber();
    const buildingLabel = `D${id}`;

    before('load test factory property', function () {
        const propertyWithBuildingConsentInputBody = testFactory.getPropertyWithBuildingConsentInputBody('Ready to Value');
        cy.request(propertyWithBuildingConsentInputBody)
            .then((response) => {
                const property = response.body.data[0];
                const ratingValuationId = property.children[0].attributes.ratingValuationId;
                bcJobPage = `/roll-maintenance/rating-valuation/${ratingValuationId}/property-details`;
            });
    });

    context('BC Job End-to-End Testing- Filling in the draft property details, comparables, valuation, and completing the job', function () {
        let testData;
        let userData;
        before('Visits page', function () {
            cy.visitWithLogin(bcJobPage);
            cy.fixture('bc-job-testdata').then(function (inputTestData) {
                testData = inputTestData;
            })
            cy.fixture('bc-job-inputdata').then(function (inputTestData) {
                userData = inputTestData;
            })
        });


        it('Filling in the draft property details, comparables, valuation, and completing the job', function () {
            //Getting the BC number for the report
            BCDraftPropertyDetails.elements.currentRollActivityMaintenanceTable
                .then((text) => {
                    userData.bcNumberData = text.text().trim();
                });

            // Draft Property Details
            //General Property Information
            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.draftPropertyCategoryValueExist,
                testData.categoryInput,
                BCDraftPropertyDetails.elements.draftPropertyCategoryDropDown,
            ).then((text) => {
                userData.categoryData = text;
                BCDraftPropertyDetails.elements.draftPropertyCategoryValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.categoryData);
                });

            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.draftPropertyNatureOfImprovementValueExist,
                testData.natureOfImprovementInput,
                BCDraftPropertyDetails.elements.draftPropertyNatureOfImprovementDropdown,
            ).then((text) => {
                userData.natureOfImprovementData = text;
                BCDraftPropertyDetails.elements.draftPropertyNatureOfImprovementValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.natureOfImprovementData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.draftPropertyLandUseValueExist,
                testData.landUseInput,
                BCDraftPropertyDetails.elements.draftPropertyLandUseDropDown,
            ).then((text) => {
                userData.landUseData = text;
                BCDraftPropertyDetails.elements.draftPropertyLandUseValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.landUseData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.draftPropertyTALandZoneValueExist,
                testData.tALandZoneInput,
                BCDraftPropertyDetails.elements.draftPropertyTALandZoneDropDown,
            ).then((text) => {
                userData.tALandZoneData = text;
                BCDraftPropertyDetails.elements.draftPropertyTALandZoneValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.tALandZoneData);
                });
            });

            BCDraftPropertyDetails.elements.draftPropertyEffectiveLandArea
                .type('1', { force: true }).clear()
            BCDraftPropertyDetails.elements.draftPropertyEffectiveLandArea
                .type('0.0401', { force: true });
            BCDraftPropertyDetails.elements.draftPropertyLandArea
                .should('have.attr', 'readonly', 'readonly');
            BCDraftPropertyDetails.elements.draftPropertyMaoriLand
                .should('have.attr', 'readonly', 'readonly');
            BCDraftPropertyDetails.elements.draftPropertyPlanId
                .should('have.attr', 'readonly', 'readonly');
            BCDraftPropertyDetails.elements.draftPropertyProducation
                .type('1', { force: true }).clear().type('0', { force: true });


            //LocationDetails
            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.lotPositionValueExist,
                testData.lotPositionInput,
                BCDraftPropertyDetails.elements.lotDropDownValues,
            ).then((text) => {
                userData.lotPositionData = text;
                BCDraftPropertyDetails.elements.lotPositionValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.lotPositionData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.contourValueExist,
                testData.contourInput,
                BCDraftPropertyDetails.elements.contourDownValues,
            ).then((text) => {
                userData.contourData = text;
                BCDraftPropertyDetails.elements.contourValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.contourData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.viewCheckValueExist,
                testData.viewInput,
                BCDraftPropertyDetails.elements.viewDropDownValues,
            ).then((text) => {
                userData.viewData = text;
                BCDraftPropertyDetails.elements.viewCheckValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.viewData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.viewScopeValueExist,
                testData.viewScopeInput,
                BCDraftPropertyDetails.elements.viewScopeDropDown,
            ).then((text) => {
                userData.viewScopeData = text;
                BCDraftPropertyDetails.elements.viewScopeValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.viewScopeData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.csiValueExist,
                testData.csiInput,
                BCDraftPropertyDetails.elements.csiDropDownValues,
            ).then((text) => {
                userData.csiData = text;
                BCDraftPropertyDetails.elements.csiValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.csiData);
                });
            });


            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.outlierValueExist,
                testData.outlierInput,
                BCDraftPropertyDetails.elements.outlierDropDownValues,
            ).then((text) => {
                userData.outlierData = text;
                BCDraftPropertyDetails.elements.outlierValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.outlierData);
                });
            });

            // Property Summary
            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.houseTypeValueExist,
                testData.houseTypeInput,
                BCDraftPropertyDetails.elements.houseTypeDropDown,
            ).then((text) => {
                userData.houseTypeData = text;
                BCDraftPropertyDetails.elements.houseTypeValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.houseTypeData);
                });
            });

            BCDraftPropertyDetails.elements.unitOfUse
                .type('1', { force: true }).clear().type('1', { force: true });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.ageValueExist,
                testData.ageInput,
                BCDraftPropertyDetails.elements.ageDropDown,
            ).then((text) => {
                userData.ageInputData = text;
                BCDraftPropertyDetails.elements.ageValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.ageInputData);
                });
            });

            BCDraftPropertyDetails.elements.effectiveYearBuilt
                .type('1', { force: true }).clear().type('2021', { force: true });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.poorfdnValueExist,
                testData.poorfdnInput,
                BCDraftPropertyDetails.elements.poorfdnDropDownValues,
            ).then((text) => {
                userData.poorfdnData = text;
                BCDraftPropertyDetails.elements.poorfdnValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.poorfdnData);
                });
            });

            BCDraftPropertyDetails.elements.totalBderms
                .type('3', { force: true }).clear().type('3', { force: true });
            BCDraftPropertyDetails.elements.totalBathrms
                .type('1', { force: true }).clear().type('1', { force: true });
            BCDraftPropertyDetails.elements.totaltoilets
                .type('1', { force: true }).clear().type('1', { force: true });
            BCDraftPropertyDetails.elements.buildingSiteCover
                .type('150', { force: true }).clear().type('150', { force: true });
            BCDraftPropertyDetails.elements.totalFloorArea
                .type('200', { force: true }).clear().type('200', { force: true });
            BCDraftPropertyDetails.elements.mainLivingArea
                .type('50', { force: true }).clear().type('50', { force: true });
            BCDraftPropertyDetails.elements.totalLivingArea
                .type('120', { force: true }).clear().type('120', { force: true });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.laundryAndWorkshopValueExist,
                testData.laundryAndWorkshopInput,
                BCDraftPropertyDetails.elements.laundryAndWorkshopDropDown,
            ).then((text) => {
                userData.ldyAndWorkshopData = text;
                BCDraftPropertyDetails.elements.laundryAndWorkshopValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.ldyAndWorkshopData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.carAccessValueExist,
                testData.carAccessInput,
                BCDraftPropertyDetails.elements.carAccessDropDown,
            ).then((text) => {
                userData.carAccessData = text;
                BCDraftPropertyDetails.elements.carAccessValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.carAccessData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.driveWayValueExist,
                testData.driveWayInput,
                BCDraftPropertyDetails.elements.driveWayDropDown,
            ).then((text) => {
                userData.driveWayData = text;
                BCDraftPropertyDetails.elements.driveWayValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.driveWayData);
                });
            });

            BCDraftPropertyDetails.elements.carParks
                .type('4', { force: true }).clear().type('4', { force: true });

            //Construction Information

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.typesOfBuildingValueExist,
                testData.typesOfBuildingInput,
                BCDraftPropertyDetails.elements.typesOfBuildingDropDown,
            ).then((text) => {
                userData.typesOfBuildingData = text;
                BCDraftPropertyDetails.elements.typesOfBuildingValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.typesOfBuildingData);
                });
            });

            BCDraftPropertyDetails.elements.floorArea.eq(0)
                .type('100', { force: true }).clear().type('100', { force: true });
            BCDraftPropertyDetails.elements.noOfStoreys.eq(0)
                .type('100', { force: true }).clear().type('1', { force: true });
            BCDraftPropertyDetails.elements.yearBuilt.eq(0)
                .type('100', { force: true }).clear().type('2022', { force: true });
            BCDraftPropertyDetails.elements.description.eq(0)
                .type('100', { force: true }).clear().type('Types of building(Automation)', { force: true });

            BCDraftPropertyDetails.elements.buildingLabel.eq(0)
                .type('100', { force: true }).clear().type(buildingLabel, { force: true });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.principalBuildingValueExist,
                testData.principalBldgInput,
                BCDraftPropertyDetails.elements.principalBuildingDropDown,
            ).then((text) => {
                userData.principalBldgData = text;
                BCDraftPropertyDetails.elements.principalBuildingValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.principalBldgData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.wallConstructionValueExist,
                testData.wallConstructionInput,
                BCDraftPropertyDetails.elements.wallConstructionDropDown,
            ).then((text) => {
                userData.wallConstructionData = text;
                BCDraftPropertyDetails.elements.wallConstructionValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.wallConstructionData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.wallConditionValueExist,
                testData.wallConditionInput,
                BCDraftPropertyDetails.elements.wallConditionDropDown,
            ).then((text) => {
                userData.wallConditionData = text;
                BCDraftPropertyDetails.elements.wallConditionValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.wallConditionData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.roofConstructionValueExist,
                testData.roofConstructionInput,
                BCDraftPropertyDetails.elements.roofConstructionDropDown,
            ).then((text) => {
                userData.roofConstructionData = text;
                BCDraftPropertyDetails.elements.roofConstructionValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.roofConstructionData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.roofConditionValueExist,
                testData.roofConditionInput,
                BCDraftPropertyDetails.elements.roofConditionDropDown,
            ).then((text) => {
                userData.roofConditionData = text;
                BCDraftPropertyDetails.elements.roofConditionValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.roofConditionData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.floorConstructionValueExist,
                testData.floorConstructionInput,
                BCDraftPropertyDetails.elements.floorConstructionDropDown,
            ).then((text) => {
                userData.floorConstructionData = text;
                BCDraftPropertyDetails.elements.floorConstructionValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.floorConstructionData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.foundationValueExist,
                testData.foundationInput,
                BCDraftPropertyDetails.elements.foundationDropDown,
            ).then((text) => {
                userData.foundationData = text;
                BCDraftPropertyDetails.elements.foundationValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.foundationData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.wiringAgeValueExist,
                testData.wiringAgeInput,
                BCDraftPropertyDetails.elements.wiringAgeDropDown,
            ).then((text) => {
                userData.wiringAgeData = text;
                BCDraftPropertyDetails.elements.wiringAgeValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.wiringAgeData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.plumbingAgeValueExist,
                testData.plumbingAgeInput,
                BCDraftPropertyDetails.elements.plumbingAgeDropDown,
            ).then((text) => {
                userData.plumbingAgeData = text;
                BCDraftPropertyDetails.elements.plumbingAgeValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.plumbingAgeData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.insulationValueExist,
                testData.insulationInput,
                BCDraftPropertyDetails.elements.insulationDropDown,
            ).then((text) => {
                userData.insulationData = text;
                BCDraftPropertyDetails.elements.insulationValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.insulationData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.glazingValueExist,
                testData.glazingInput,
                BCDraftPropertyDetails.elements.glazingDropDown,
            ).then((text) => {
                userData.glazingData = text;
                BCDraftPropertyDetails.elements.glazingValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.glazingData);
                });
            });

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.otherFeaturesCiValueExist,
                testData.otherFeaturesCiInput,
                BCDraftPropertyDetails.elements.otherFeaturesCiDropDown,
            ).then((text) => {
                userData.otherFeaturesCiData = text;
                BCDraftPropertyDetails.elements.otherFeaturesCiValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.otherFeaturesCiData);
                });
            });

            //siteImprovements

            BCDraftPropertyDetails.elements.siteOtherImprovement
                .should('have.attr', 'readonly', 'readonly');

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.siteImprovementQualityValueExist,
                testData.siteImprovementQualityInput,
                BCDraftPropertyDetails.elements.siteImprovementQualityDropDown,
            ).then((text) => {
                userData.siteImprovementQualityData = text;
                BCDraftPropertyDetails.elements.siteImprovementQualityValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.siteImprovementQualityData);
                });
            });

            //Setup Complete

            BCDraftPropertyDetails.elements.setupButton
                .eq(0).click({ force: true });
            cy.wait(2000);

            BCDraftPropertyDetails.elements.alertButtonClose
                .contains('Close').click({ force: true });

            cy.wait(2000);
            BCDraftPropertyDetails.elements.recalculateButton
                .contains('Recalculate').click({ force: true });
            BCDraftPropertyDetails.elements.alertButtonClose
                .contains('Close').click({ force: true });

            BCDraftPropertyDetails.elements.bcJobStepperValuation
                .contains('Valuation').click({ force: true });
            cy.wait(2000);

            BCDraftPropertyDetails.elements.valuationSaveAsDraft
                .click({ force: true });

            BCDraftPropertyDetails.elements.alertButtonClose
                .contains('Close').click({ force: true });
            cy.wait(2000);
            BCDraftPropertyDetails.elements.bcJobStepperJobCompletion
                .contains('Job Completion').click({ force: true });
            cy.wait(2000);

            BCDraftPropertyDetails.dropDownBox(
                BCDraftPropertyDetails.elements.jobValuerValueExist,
                testData.jobValuerInput,
                BCDraftPropertyDetails.elements.jobValuerDropDown,
            ).then((text) => {
                userData.jobValuerData = text;
                BCDraftPropertyDetails.elements.jobValuerValueExist.invoke('text').then((text) => {
                    const trimmedText = text.trim();
                    expect(trimmedText).to.equal(userData.jobValuerData);
                });
            });

            cy.writeFile('cypress/fixtures/bc-job-userdata.json', userData);
            cy.wait(3000);

            BCDraftPropertyDetails.elements.completeValuation
                .contains('Complete Valuation').click({ force: true });
            cy.wait(8000);
            BCDraftPropertyDetails.elements.alertButtonClose
                .contains('Close').click({ force: true });
            cy.wait(8000);
        });
    });

    context('BC Job Report', function () {
        before('Visits page', function () {
            cy.intercept('POST', '/web/rollMaintenance/searchActivities')
                .as('consents');
            cy.visitWithLogin(bcJobSearchPage);
        });
        it('Validating the BC job report', function () {
            BCSearch.elements.activityStatusesClear
                .click()
                .should('be.visible');
            cy.wait('@consents', { timeout: 30000 });
            cy.readFile('cypress/fixtures/bc-job-userdata.json').then((userData) => {

                BCSearch.elements.bcNumber
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'BC Number');


                BCSearch.elements.bcNumber.find('input')
                    .type(userData.bcNumberData, { force: true });

                BCSearch.elements.searchButton
                    .contains('Search')
                    .should('not.be.disabled')
                    .click({ force: true });
                cy.wait('@consents', { timeout: 30000 });
                BCSearch.elements.viewValuation
                    .contains('View Valuation').click({ force: true });

                BCJobReport.elements.category
                    .should('exist')
                    .and('contain', userData.categoryData);

                BCJobReport.elements.natureOfImprovement
                    .should('exist')
                    .and('contain', userData.natureOfImprovementData);

                BCJobReport.elements.landUse
                    .should('exist')
                    .and('contain', userData.landUseData.replace(/[0-9—]/g, '').trim());

                BCJobReport.elements.tALandZone
                    .should('exist')
                    .and('contain', userData.tALandZoneData);

                BCJobReport.elements.lotPosition
                    .should('exist')
                    .and('contain', userData.lotPositionData);

                BCJobReport.elements.contour
                    .should('exist')
                    .and('contain', userData.contourData);

                BCJobReport.elements.view
                    .should('exist')
                    .and('contain', userData.viewData);

                BCJobReport.elements.viewScope
                    .should('exist')
                    .and('contain', userData.viewScopeData);

                BCJobReport.elements.cSI
                    .should('exist')
                    .and('contain', userData.csiData);
            });
        });
    });
});



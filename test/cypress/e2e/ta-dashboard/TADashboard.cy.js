/* global cy it describe */
/* eslint-disable */

import TADashboard from '../../model/TADashboard';

const taCode = 1;
const propertyValueProps = [
    'totalCapitalValue',
    'totalCapitalValuePercentage',
    'totalLandValue',
    'totalLandValuePercentage',
    'totalRatingUnits',
    'totalChangeInRatingUnits',
    'ratingValuationDate',
    'nextRatingValuationDate'
];
const consentProps = ['inProgressConsents', 'monthlyConsentSummaries', 'readyToCheckConsents'];
const subdivisionProps = ['monthlySubdivisionSummaries', 'outstandingSubdivisions'];
const objectionSummaryProps = ['maintenanceObjectionSummary', 'revisionObjectionSummary'];
const objectionProps = [
    'inProgressObjections',
    'monthlyObjectionSummaries',
    'objectionType',
    'yearToDateCommercialObjections',
    'yearToDateLifestyleObjections',
    'yearToDateOtherObjections',
    'yearToDateReceivedObjections',
    'yearToDateResidentialObjections',
    'yearToDateRuralObjections'
];

const salesProps = ['monthlySalesSummaries'];

describe('TA Dashboard', () => {
    // TODO: spread all these assertions across many 'it blocks'
    it('Selects a TA, loadTADashboard and displayGraphs return 200s', () => {
        cy.intercept({
            method: 'GET',
            url: `${env.BASE_URL}loadTADashboard/*`
        }).as('loadTADashboard');

        cy.intercept({
            method: 'GET',
            url: `${env.BASE_URL}displayGraphs/*`
        }).as('displayGraphs');

        TADashboard.DashboardList.find(`input[value="${taCode}"]`).click({ force: true });

        cy.wait('@loadTADashboard').then(interception => {
            expect(interception.response.statusCode).to.equal(200);
            for (const prop of propertyValueProps) {
                expect(interception.response.body.userPropertyValue)
                    .to.have.property(prop)
                    .and.not.equal(null);
            }
            expect(interception.response.body)
                .to.have.property('isRevisionDateInCurrentYear')
                .and.not.equal(null);
            expect(interception.response.body.isRevisionDateInCurrentYear)
                .to.be.a('boolean');

            expect(interception.response.body)
                .to.have.property('isTodayBeforeObjectionVisibilityExpiryDate')
                .and.not.equal(null);
            expect(interception.response.body.isTodayBeforeObjectionVisibilityExpiryDate)
                .to.be.a('boolean');
        });

        cy.wait('@displayGraphs').then(interception => {
            expect(interception.response.statusCode).to.equal(200);
            expect(interception.response.body.taConsentSummary)
                .to.have.property('taCode')
                .and.equal(taCode);
            for (const prop of consentProps) {
                expect(interception.response.body.taConsentSummary)
                    .to.have.property(prop)
                    .and.not.equal(null);
            }
            for (const prop of subdivisionProps) {
                expect(interception.response.body.taSubdivisionSummary)
                    .to.have.property(prop)
                    .and.not.equal(null);
            }
            for (const prop of objectionSummaryProps) {
                expect(interception.response.body.taObjectionSummary)
                    .to.have.property(prop)
                    .and.not.equal(null);
            }
            for (const prop of objectionProps) {
                expect(interception.response.body.taObjectionSummary.maintenanceObjectionSummary)
                    .to.have.property(prop)
                    .and.not.equal(null);
                expect(interception.response.body.taObjectionSummary.revisionObjectionSummary)
                    .to.have.property(prop)
                    .and.not.equal(null);
            }
        });
    });
});

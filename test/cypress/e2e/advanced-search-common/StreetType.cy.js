import Home from '../../model/Home';
import AdvancedSearch from '../../model/AdvancedSearch';
import PropertySearchResults from '../../model/PropertySearchResults';

describe('Sales Search common criteria', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it(`restricts search by street type`, () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.propertySearch.click();
    AdvancedSearch.streetName.clear().type('queen');
    AdvancedSearch.streetType('Avenue');
    AdvancedSearch.search();
    cy.wait(3000);
    PropertySearchResults.results.should('have.length.at.least', 10);
  });

  it('has results with expected address type', () => {
    PropertySearchResults.results.each(result => {
      cy.wrap(result)
        .find(PropertySearchResults.addressOneSelector)
        .invoke('text')
        .then(text => {
          // note: can have entries like... 'Queen Mary Avenue'
          expect(text).to.include('Queen');
          expect(text).to.include('Avenue');
        });
    });
  });
});

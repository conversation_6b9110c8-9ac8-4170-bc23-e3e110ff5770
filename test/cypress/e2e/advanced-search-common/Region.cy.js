import Home from '../../model/Home';
import AdvancedSearch from '../../model/AdvancedSearch';
import PropertySearchResults from '../../model/PropertySearchResults';

describe('Advanced Search region', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it(`sets the region for Wellington Region`, () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.propertySearch.click();
    AdvancedSearch.regionType('Wellington Region');
    AdvancedSearch.streetName.clear().type('Nevis');
    AdvancedSearch.search();
    cy.wait(3000);
    PropertySearchResults.results.should('have.length.at.least', 10);
  });


  it('has results with expected region', () => {
    PropertySearchResults.results.each(result => {
      cy.wrap(result)
        .find(PropertySearchResults.addressTwoSelector)
        .invoke('text')
        .then(text => {
          expect(text).to.include('Lower Hutt');
          expect(text).to.include('Hutt City');
        });
    });
  });
});

import Home from '../../model/Home';
import AdvancedSearch from '../../model/AdvancedSearch';
import PropertySearchResults from '../../model/PropertySearchResults';

describe('Property Search common criteria', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it(`restricts search by street numbers`, () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.propertySearch.click();
    AdvancedSearch.streetName.clear().type('queen');
    AdvancedSearch.streetNumFrom.clear().type(10);
    AdvancedSearch.streetNumTo.clear().type(10);
    AdvancedSearch.search();
    cy.wait(3000);
    PropertySearchResults.results.should('have.length.at.least', 10);
  });

  it('has results with expected address number', () => {
    PropertySearchResults.results.each(result => {
      cy.wrap(result)
        .find(PropertySearchResults.addressOneSelector)
        .invoke('text')
        .then(text => {
          // note: can have entries like... '10 B Queen Street'
          expect(text).to.include('10');
          expect(text).to.include('Queen');
        });
    });
  });
});

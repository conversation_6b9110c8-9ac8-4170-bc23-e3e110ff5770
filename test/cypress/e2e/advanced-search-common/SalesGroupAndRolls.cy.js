import Home from '../../model/Home';
import AdvancedSearch from '../../model/AdvancedSearch';
import SalesGroupsAndRolls from '../../model/SalesGroupsAndRolls';
import PropertySearchResults from '../../model/PropertySearchResults';

describe('Advanced Search sales groups and rolls', { defaultCommandTimeout: 15000 }, () => {
  let countOne = null;
  let countTwo = null;

  before(() => {
    cy.visitWithLogin('');
  });

  it(`sets the groups and rolls for Christchurch`, () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.propertySearch.click();
    AdvancedSearch.taRangeFrom.clear().type(env.TA_CODE.CHCH);
    AdvancedSearch.taRangeTo.clear().type(env.TA_CODE.CHCH);
  });

  it('opens the sales groups and rolls criteria', () => {
    cy.contains('span', 'Sales Groups & Rolls').click();
    cy.get('label[for="ta60"]').should('contain', 'Christchurch City');
    cy.get('.advSearch-group').each((entry) => {
      const label = entry.find('.salesGroup-name').find('label').text();
      if (label.includes('Hoon Hay')) {
        cy.wrap(entry).find('.salesGroup-name').find('label').click();
      }

      if(label.includes('Lyttleton Bays')) {
        cy.wrap(entry).find('fieldset').find('label').first().click();
      }
    });

    SalesGroupsAndRolls.elements.setRolls.click();
  });

  it('checks that the badge has been set', () => {
    SalesGroupsAndRolls.elements.rollCount.should('contain', '6');
  });

  it('runs the search', () => {
    AdvancedSearch.search();
    cy.wait(3000);
    PropertySearchResults.results.should('have.length.at.least', 5);

    PropertySearchResults.resultsCount.then(($count) => {
      countOne = parseInt($count);
      expect(countOne).to.be.gte(5);
    });
  });

  // note: the salegroup does not show as part of the result set

  it('clears the rolls', () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.propertySearch.click();
    cy.contains('span', 'Sales Groups & Rolls').click();
    SalesGroupsAndRolls.elements.clearRolls.click();
    SalesGroupsAndRolls.elements.setRolls.click();
  });

  it('hides the rollCount badge', () => {
    // note: the rollcount remains at previous value of 6, but the badge hides
    SalesGroupsAndRolls.elements.rollCount.should('not.be.visible');
  });

  it('re-runs the search', () => {
    AdvancedSearch.search();
    cy.wait(3000);
    PropertySearchResults.results.should('have.length.at.least', 5);

    PropertySearchResults.resultsCount.then(($count) => {
      countTwo = parseInt($count);
      expect(countTwo).to.be.gt(countOne);
    });
  });
});

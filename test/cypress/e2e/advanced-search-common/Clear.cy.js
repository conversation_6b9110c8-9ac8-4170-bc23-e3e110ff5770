import Home from '../../model/Home';
import AdvancedSearch from '../../model/AdvancedSearch';

describe('Advanced search common criteria', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it('fills in several parts of the search form', () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.propertySearch.click();
    AdvancedSearch.taRangeFrom.clear().type(env.TA_CODE.CHCH);
    AdvancedSearch.taRangeTo.clear().type(env.TA_CODE.CHCH);
    AdvancedSearch.streetNumFrom.clear().type('1');
    AdvancedSearch.streetNumTo.clear().type('100');
    AdvancedSearch.streetName.clear().type('queen');
    AdvancedSearch.streetType('Avenue');
  });

  it('clears the form', () => {
    AdvancedSearch.clear();
    AdvancedSearch.taRangeFrom.should('have.value', '');
    AdvancedSearch.taRangeTo.should('have.value', '');
    AdvancedSearch.streetNumFrom.should('have.value', '');
    AdvancedSearch.streetNumTo.should('have.value', '');
    AdvancedSearch.streetName.should('have.value', '');
    AdvancedSearch.streetTypeMultiselect.within(() => {
      cy.get('span.multiselect-selected-text').should('have.text', 'Select...');
    });
  });
});

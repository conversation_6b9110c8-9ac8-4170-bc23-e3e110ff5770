import SaleProcessing from '../../model/SaleProcessing.js';
import testFactory from '../../support/testFactory.js';

let sale;

describe.skip('Delete Sale Details', () => {
    context('Delete Sale Details', () => {
        before(() => {
            SaleProcessing.visit();
            SaleProcessing.setTas(['11']);
            SaleProcessing.search();
            SaleProcessing.saleSearchResult('val-ref').first().click({ force: true });
        });
        it('Clicking Delete Sale should delete the sale', () => {
            cy.intercept('/api/salesProcessing/deleteSale').as('deleteSale');

            SaleProcessing.deleteSale();

            cy.wait('@deleteSale').then((int) => {
                const response = int.response.body;
                expect(response.status).be.equal('SUCCESS');
            })
        });
    });
});

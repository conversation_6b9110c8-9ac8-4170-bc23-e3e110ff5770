import SaleProcessing from '../../model/SaleProcessing.js';
import Sale from '../../model/Sale.js';
import testFactory from '../../support/testFactory.js';

const timestamp = new Date().toISOString();

let sale;

describe('Update Sale Details-Sales inspection', () => {
    context('As an Internal Admin or Customer care user', () => {
        before(async () => {
            cy.login();
            SaleProcessing.visit();
            cy.intercept({
                method: 'POST',
                url: `${env.BASE_URL}api/salesSearch/getSearchSale`
            }).as('getSearchSale');
            cy.intercept('/web/salesSearch/getSearchSale').as('getSearchSale');

            sale = await testFactory.addSaleToProperty(1002578);

            cy.doubleLog(sale)

            SaleProcessing.elements.taSelectDiv.scrollIntoView().click({ force: true });
            SaleProcessing.elements.selectAllTasCheckbox.scrollIntoView();
            SaleProcessing.forceUnselectAllTAs();

            SaleProcessing.search();

            SaleProcessing.elements.saleIdColumn.click({ force: true });
            cy.wait(2000);
            SaleProcessing.elements.saleIdColumn.click({ force: true });
            cy.wait(5000);

            // cy.wait('@getSearchSale');
        });

        it('Clicking Update Sale should save the sale', () => {
            cy.intercept('/api/salesProcessing/saveSale').as('saveSale');

            SaleProcessing.saleSearchResult('val-ref').first().click();
            SaleProcessing.saleSearchExpandedView('sale-details').click({ force: true });
            SaleProcessing.elements.saleInput('vendor-purchaser').clear().type(`TEST:${timestamp}`);
            SaleProcessing.elements.saleInput('reason-for-change').clear().type(`TEST:${timestamp}`);
            SaleProcessing.saveSale();

            Sale.saveSale();

            cy.wait('@saveSale').then((int) => {
                const body = int.request.body;
                expect(body.sale.vendorPurchaser).to.equal(`TEST:${timestamp}`);
            });
        });
    });

});

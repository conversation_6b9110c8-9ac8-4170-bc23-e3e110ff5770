import SaleProcessing from '../../model/SaleProcessing.js';
import Sale from '../../model/Sale.js';
import PropertyDetails from '../../model/PropertyDetails.js';
import user from '../../support/user.js';
import testFactory from '../../support/testFactory.js';

const taCodes = env['SALE_PROCESSING_SEARCH'].taCodes;
const ownershipTaCodes = env['SALE_PROCESSING_SEARCH'].ownershipTaCodes;
const saleDateFrom = env['SALE_PROCESSING_SEARCH'].saleDateFrom;;
const saleDateTo = env['SALE_PROCESSING_SEARCH'].saleDateTo;;
const timestamp = new Date().toISOString();

let sale;

describe('Sales Processing Search Access',{
    testIsolation: false,
    defaultCommandTimeout: 30000,
},() => {
    context('As an Internal Admin or Customer care user', () => {
        before(() => {
            cy.login();
            SaleProcessing.visit();
        });

        it('Displays Sales Processing Header tab', () => {
            SaleProcessing.elements.header.should('exist').and('be.visible');
        });

        it('Displays Sales To Process tab', () => {
            SaleProcessing.elements.SalesToProcessTab.should('exist').and('be.visible');
            // SaleProcessing.elements.SalesToProcessTab.should('have.class', 'router-link-active');
            // SaleProcessing.elements.SalesInspectionTab.should('not.have.class', 'router-link-active');
        });

        it('Displays Sales Inspection tab', () => {
            SaleProcessing.elements.SalesInspectionTab.should('exist').and('be.visible');
            // SaleProcessing.elements.SalesToProcessTab.should('have.class', 'router-link-active');
            // SaleProcessing.elements.SalesInspectionTab.should('not.have.class', 'router-link-active');
        });

    });

    context('As an External user', () => {
        before(() => {
            cy.login();
            cy.overrideUserData({
                roles: [{ name: 'EXTERNAL_USER_READ' }],
            });
            cy.visit('/roll-maintenance?tab=salesSearch');
        });

        it('Sales Processing Search is not available', () => {
            cy.get('[data-cy="sales-search-tab]').should('not.exist');
        });
    });

    // TODO: redo INTERNAL_NON_ADMIN_USER user details
    context.skip('As an Internal other user', () => {
        before(() => {
            cy.visitWithUser('/roll-maintenance?tab=salesSearch', user.INTERNAL_NON_ADMIN_USER);
        });

        it('Displays Sales Processing Header tab', () => {
            SaleProcessing.elements.header.should('exist').and('be.visible');
        });

        it('Displays Sales Inspection tab', () => {
            SaleProcessing.elements.SalesInspectionTab.should('exist').and('be.visible');
            SaleProcessing.elements.SalesInspectionTab.should('have.class', 'qv-sp-tab-active');
        });

        it('Should not display Sales To Process tab', () => {
            SaleProcessing.elements.SalesToProcessTab.should('not.exist');
        });
    });
});

describe('Sales To Process Search',{
    testIsolation: false,
    defaultCommandTimeout: 30000,
},() => {

    context('Default Sales to Process selection', () => {
        before(() => {
            cy.login();
            SaleProcessing.visit();
            SaleProcessing.setTas(taCodes)
        });

        it('Displays Sales Search Title ', () => {
            SaleProcessing.elements.subheader.should('exist').and('be.visible');
        });

        it('Displays Ta Selection in Sales search form', () => {
            SaleProcessing.elements.taSelect.should('exist').and('be.visible');
        });
        it('Displays Sale Date From in Sales search form', () => {
            SaleProcessing.elements.saleDateFrom.should('exist').and('be.visible');
        });
        it('Displays Sale Date To in Sales search form', () => {
            SaleProcessing.elements.saleDateTo.should('exist').and('be.visible');
        });
        it('Displays Sale Input Date From in Sales search form', () => {
            SaleProcessing.elements.saleInputDateFrom.should('exist').and('be.visible');
        });
        it('Displays Sale Input Date To in Sales search form', () => {
            SaleProcessing.elements.saleInputDateTo.should('exist').and('be.visible');
        });
        it('Displays Categories input in Sales search form', () => {
            SaleProcessing.elements.categories.should('exist').and('be.visible');
        });
        it('Displays Source selection in Sales search form', () => {
            SaleProcessing.elements.source.should('exist').and('be.visible');
        });
        it('Displays Processing Status selection in Sales search form', () => {
            SaleProcessing.elements.processingStatusMultiselect.should('exist').and('be.visible');
        });
        it('Displays GSP From in Sales search form', () => {
            SaleProcessing.elements.gspFrom.should('exist').and('be.visible');
        });
        it('Displays GSP To in Sales search form', () => {
            SaleProcessing.elements.gspTo.should('exist').and('be.visible');
        });
        it('Displays Exclude form HPI and RTV in Sales search form', () => {
            SaleProcessing.elements.excludeFromHpiRtv.should('exist').and('be.visible');
        });
        it('Displays Clear Button in Sales search form', () => {
            SaleProcessing.elements.clearButton.should('exist').and('be.visible');
        });
        it('Displays Search Button inSales search form', () => {
            SaleProcessing.elements.searchButton.should('exist').and('be.visible');
        });

        it(`Expect default TA selection: ${taCodes}`, () => {
            SaleProcessing.elements.taSelectDiv.find('input[type="checkbox"]:checked').should(($checkboxes) => {
                const values = $checkboxes.map((_, el) => Cypress.$(el).val()).get();
                expect(values).to.deep.equal(taCodes);
              });
        });

        it.skip('Expect default sale classification selection', () => {
            SaleProcessing.elements.saleClassificationsSelectDiv.find('input[type="checkbox"]:checked').should('have.length',11);
        });

        it.skip('Displays default Procesing status selection : To Process', () => {
            SaleProcessing.elements.processingStatusMultiselect.find('.multiselect__tag').invoke('text').then(text => text.trim()).should('eq', 'To Process');
        });

        it('Expect default Sale Status : Confirmed and Unconfirmed', () => {
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Confirmed") input[type="checkbox"]').should('be.checked');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Pending") input[type="checkbox"]').should('not.be.checked');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Unconfirmed") input[type="checkbox"]').should('be.checked');
        });

        it('Expect other default selection blank', () => {
            SaleProcessing.elements.saleDateFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.saleDateTo.invoke('text').should('be.empty');
            SaleProcessing.elements.saleInputDateFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.saleInputDateTo.invoke('text').should('be.empty');
            SaleProcessing.elements.categories.invoke('text').should('be.empty');
            SaleProcessing.elements.source.find('.multiselect__tags-wrap').should('not.be.visible');
            SaleProcessing.elements.gspFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.gspTo.invoke('text').should('be.empty');
            SaleProcessing.elements.excludeFromHpiRtv.should('not.be.checked');
        });

        it('Click on Search Button with No Result Criteria', () => {
            SaleProcessing.elements.categories.type('xxxx');
            SaleProcessing.search();
            SaleProcessing.elements.searchResults.should('not.exist');
            SaleProcessing.elements.saleSearchTopPagination.should('not.exist');
            SaleProcessing.elements.saleSearchNoResult.should('exist').and('be.visible');
        });

        it('Click on Search Button with default Criteria', () => {
            SaleProcessing.clear();
            // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(1)').click();
            SaleProcessing.search();
            SaleProcessing.elements.searchResults.should('exist').and('be.visible');
            SaleProcessing.elements.saleSearchTopPagination.should('exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });

        it('Click on Search Button with extra Criteria', () => {
            SaleProcessing.elements.saleDateFrom.type(saleDateFrom);
            SaleProcessing.elements.saleDateTo.type(saleDateTo);
            SaleProcessing.elements.saleInputDateFrom.type(saleDateFrom);
            SaleProcessing.elements.saleInputDateTo.type(saleDateTo);
            SaleProcessing.elements.categories.type('R*');
            // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(1)').click();
            SaleProcessing.search();
            SaleProcessing.elements.searchResults.should('exist').and('be.visible');
            SaleProcessing.elements.saleSearchTopPagination.should('exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });

        it('Click on Clear Button reset to default selection', () => {
            SaleProcessing.clear();
            SaleProcessing.elements.taSelectDiv.find('input[type="checkbox"]:checked').should(($checkboxes) => {
                const values = $checkboxes.map((_, el) => Cypress.$(el).val()).get();
                expect(values).to.deep.equal(taCodes);
              });
            SaleProcessing.elements.saleDateFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.saleDateTo.invoke('text').should('be.empty');
            SaleProcessing.elements.saleInputDateFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.saleInputDateTo.invoke('text').should('be.empty');
            SaleProcessing.elements.categories.invoke('text').should('be.empty');
            // SaleProcessing.elements.saleClassificationsSelectDiv.find('input[type="checkbox"]:checked').should('have.length',11);
            // SaleProcessing.elements.processingStatusMultiselect.find('.multiselect__tag').invoke('text').then(text => text.trim()).should('eq', 'To Process');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Confirmed") input[type="checkbox"]').should('be.checked');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Pending") input[type="checkbox"]').should('not.be.checked');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Unconfirmed") input[type="checkbox"]').should('be.checked');
            SaleProcessing.elements.source.find('.multiselect__tags-wrap').should('not.be.visible');
            SaleProcessing.elements.gspFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.gspTo.invoke('text').should('be.empty');
            SaleProcessing.elements.excludeFromHpiRtv.should('not.be.checked');
            SaleProcessing.elements.searchResults.should('not.exist');
            SaleProcessing.elements.saleSearchTopPagination.should('not.exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('not.exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });
    });
});

// FIXME: reimplement when INTERNAL_NON_ADMIN_USER is fixed
describe.skip('Sales Inspection Search',{
    testIsolation: false,
    defaultCommandTimeout: 30000,
},() => {

    context('Default Sales Inspection selection', () => {
        before(() => {
            cy.visitWithUser('', user.INTERNAL_NON_ADMIN_USER);
            SaleProcessing.visit();
            SaleProcessing.setTas(taCodes);
        });

        it('Displays Sales Search Title ', () => {
            SaleProcessing.elements.subheader.should('exist').and('be.visible');
        });

        it('Displays Sales search form', () => {
            SaleProcessing.elements.taSelect.should('exist').and('be.visible');
            SaleProcessing.elements.saleDateFrom.should('exist').and('be.visible');
            SaleProcessing.elements.saleDateTo.should('exist').and('be.visible');
            SaleProcessing.elements.saleInputDateFrom.should('exist').and('be.visible');
            SaleProcessing.elements.saleInputDateTo.should('exist').and('be.visible');
            SaleProcessing.elements.categories.should('exist').and('be.visible');
            SaleProcessing.elements.source.should('exist').and('be.visible');
            SaleProcessing.elements.processingStatusMultiselect.should('exist').and('be.visible');
            SaleProcessing.elements.gspFrom.should('exist').and('be.visible');
            SaleProcessing.elements.gspTo.should('exist').and('be.visible');
            SaleProcessing.elements.excludeFromHpiRtv.should('exist').and('be.visible');
            SaleProcessing.elements.clearButton.should('exist').and('be.visible');
            SaleProcessing.elements.searchButton.should('exist').and('be.visible');
        });

        it(`Expect default TA selection: ${taCodes}`, () => {
            SaleProcessing.elements.taSelectDiv.find('input[type="checkbox"]:checked').should(($checkboxes) => {
                const values = $checkboxes.map((_, el) => Cypress.$(el).val()).get();
                expect(values).to.deep.equal(taCodes);
              });
        });

        it('Expect default sale classification selection', () => {
            SaleProcessing.elements.saleClassificationsSelectDiv.find('input[type="checkbox"]:checked').should('have.length',4);
        });

        it.skip('Displays default Procesing status selection : Processed and Auto-Processed', () => {
            SaleProcessing.elements.processingStatusMultiselect.find('.multiselect__tag').then(elements => {
                const textArray = elements.map((_, el) => Cypress.$(el).text().trim()).get();
                expect(textArray).to.deep.equal(['Processed', 'Auto-Processed']);
              });
        });

        it('Expect default Sale Status : Confirmed', () => {
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Confirmed") input[type="checkbox"]').should('be.checked');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Pending") input[type="checkbox"]').should('not.be.checked');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Unconfirmed") input[type="checkbox"]').should('be.checked');
        });

        it('Expect other default selection blank', () => {
            SaleProcessing.elements.saleDateFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.saleDateTo.invoke('text').should('be.empty');
            SaleProcessing.elements.saleInputDateFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.saleInputDateTo.invoke('text').should('be.empty');
            SaleProcessing.elements.categories.invoke('text').should('be.empty');
            SaleProcessing.elements.source.find('.multiselect__tags-wrap').should('not.be.visible');
            SaleProcessing.elements.gspFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.gspTo.invoke('text').should('be.empty');
            SaleProcessing.elements.excludeFromHpiRtv.should('not.be.checked');
        });

        it('Click on Search Button with No Result Criteria', () => {
            SaleProcessing.elements.categories.type('xxxx');
            SaleProcessing.search();
            SaleProcessing.elements.searchResults.should('not.exist');
            SaleProcessing.elements.saleSearchTopPagination.should('not.exist');
            SaleProcessing.elements.saleSearchNoResult.should('exist').and('be.visible');
        });

        it.skip('Click on Search Button with default Criteria', () => {
            SaleProcessing.clear();
            SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(2)').click();
            SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(3)').click();
            SaleProcessing.search();
            SaleProcessing.elements.searchResults.should('exist').and('be.visible');
            SaleProcessing.elements.saleSearchTopPagination.should('exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });

        it.skip('Click on Search Button with extra Criteria', () => {
            SaleProcessing.elements.saleDateFrom.type(saleDateFrom);
            SaleProcessing.elements.saleDateTo.type(saleDateTo);
            SaleProcessing.elements.saleInputDateFrom.type(saleDateFrom);
            SaleProcessing.elements.saleInputDateTo.type(saleDateTo);
            SaleProcessing.elements.categories.type('R*');
            SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(2)').click();
            SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(3)').click();
            SaleProcessing.search();
            SaleProcessing.elements.searchResults.should('exist').and('be.visible');
            SaleProcessing.elements.saleSearchTopPagination.should('exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });

        it('Click on Clear Button reset to default selection', () => {
            SaleProcessing.clear();
            SaleProcessing.elements.taSelectDiv.find('input[type="checkbox"]:checked').should(($checkboxes) => {
                const values = $checkboxes.map((_, el) => Cypress.$(el).val()).get();
                expect(values).to.deep.equal(taCodes);
              });
            SaleProcessing.elements.saleDateFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.saleDateTo.invoke('text').should('be.empty');
            SaleProcessing.elements.saleInputDateFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.saleInputDateTo.invoke('text').should('be.empty');
            SaleProcessing.elements.categories.invoke('text').should('be.empty');
            SaleProcessing.elements.source.find('.multiselect__tags-wrap').should('not.be.visible');
            SaleProcessing.elements.saleClassificationsSelectDiv.find('input[type="checkbox"]:checked').should('have.length',4);
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Confirmed") input[type="checkbox"]').should('be.checked');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Pending") input[type="checkbox"]').should('not.be.checked');
            SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Unconfirmed") input[type="checkbox"]').should('be.checked');
            SaleProcessing.elements.gspFrom.invoke('text').should('be.empty');
            SaleProcessing.elements.gspTo.invoke('text').should('be.empty');
            SaleProcessing.elements.excludeFromHpiRtv.should('not.be.checked');
            SaleProcessing.elements.searchResults.should('not.exist');
            SaleProcessing.elements.saleSearchTopPagination.should('not.exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('not.exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });

    });
});

describe('Sales Search One line result ', {
    testIsolation: false,
    defaultCommandTimeout: 30000,
}, () => {
    context('As an internal user', () => {
        before(() => {
            cy.login();
            SaleProcessing.visit();
            SaleProcessing.clear();
            // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(1)').click();
            // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(2)').click();
            SaleProcessing.search();
        });

        it('Displays search result when Search button is clicked with default criteria', () => {
            SaleProcessing.elements.searchResults.should('exist').and('be.visible');
            SaleProcessing.elements.saleSearchTopPagination.should('exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });

        it('Displays Address in result', () => {
            SaleProcessing.saleSearchResult('address').should('exist').and('be.visible');
        });

        it('Displays ValRef in result', () => {
            SaleProcessing.saleSearchResult('val-ref').should('exist').and('be.visible');
        });

        it('Displays Sale Id in result', () => {
            SaleProcessing.saleSearchResult('sale-id').should('exist').and('be.visible');
        });

        it('Displays Sale Date in result', () => {
            SaleProcessing.saleSearchResult('sale-date').should('exist').and('be.visible');
        });

        it('Displays Net Sale Price in result', () => {
            SaleProcessing.saleSearchResult('sale-price-net').should('exist').and('be.visible');
        });

        it('Displays Chattels in result', () => {
            SaleProcessing.saleSearchResult('chattels').should('exist').and('be.visible');
        });

        it('Displays Sale Classification in result', () => {
            SaleProcessing.saleSearchResult('sale-classification').should('exist').and('be.visible');
        });

        it('Displays Sale Status in result', () => {
            SaleProcessing.saleSearchResult('sale-status').should('exist').and('be.visible');
        });

        it('Displays Processing Status in result', () => {
            SaleProcessing.saleSearchResult('processing-status').should('exist').and('be.visible');
        });

        it('Displays Land Area in result', () => {
            SaleProcessing.saleSearchResult('land-area').should('exist').and('be.visible');
        });

        it('Displays Category in result', () => {
            SaleProcessing.saleSearchResult('category').should('exist').and('be.visible');
        });

        it('Shows the expanded view when a sale search result except Address is clicked', () => {
            SaleProcessing.saleSearchResult('val-ref').first().click();
            SaleProcessing.elements.searchResultsExpanded.should('exist');
            SaleProcessing.elements.searchResultsExpanded.should('have.length.greaterThan', 0);
        });

        it('Navigates to Property Summary page when a sale search result is clicked', () => {
            SaleProcessing.clickSaleSearchResultPropertyLink();
            PropertyDetails.propertySummaryHeader.should('exist').and('be.visible');
        });

    });
});

describe('Sales to Process: Sales Search expanded View ', {
    testIsolation: false,
    defaultCommandTimeout: 30000,
}, () => {
    context('As an internal user', () => {

        before(() => {
            // TODO: enter search results that will get the testfactory property
            cy.login();
            SaleProcessing.visit();
            SaleProcessing.setTas(taCodes)
            SaleProcessing.clear();
            // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(1)').click();
            // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(2)').click();
            SaleProcessing.search();
        });

        it('Displays search result when Search button is clicked with default criteria', () => {
            SaleProcessing.elements.searchResults.should('exist').and('be.visible');
            SaleProcessing.elements.saleSearchTopPagination.should('exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });

        context('Displays Address in result', () => {
            before(() => {
                SaleProcessing.saleSearchResult('val-ref').first().click();
            });

            it('Shows the expanded view when a sale search result except Address is clicked', () => {
                SaleProcessing.elements.searchResultsExpanded.should('exist');
                SaleProcessing.elements.searchResultsExpanded.should('have.length.greaterThan', 0);
            });

            it('Displays Address in result', () => {
                SaleProcessing.saleSearchExpandedView('address').should('exist').and('be.visible');
            });

            it('Displays Qpid in result', () => {
                SaleProcessing.saleSearchExpandedView('qpid').should('exist').and('be.visible');
            });

            it('Displays Sale Id in result', () => {
                SaleProcessing.saleSearchExpandedView('sale-id').should('exist').and('be.visible');
            });

            it('Displays Analysis in result', () => {
                SaleProcessing.saleSearchExpandedView('analysis').should('exist').and('be.visible');
            });

            it('Displays Source in result', () => {
                SaleProcessing.saleSearchExpandedView('source').should('exist').and('be.visible');
            });

            it('Displays Val Ref in result', () => {
                SaleProcessing.saleSearchExpandedView('val-ref').should('exist').and('be.visible');
            });

            it('Displays Portal ID in result', () => {
                SaleProcessing.saleSearchExpandedView('portal-id').should('exist').and('be.visible');
            });

            it('Displays Sale Status in result', () => {
                SaleProcessing.saleSearchExpandedView('status').should('exist').and('be.visible');
            });

            it('Displays Sale classification in result', () => {
                SaleProcessing.saleSearchExpandedView('sale-classification').should('exist').and('be.visible');
            });

            it('Displays PDF link', () => {
                SaleProcessing.saleSearchExpandedView('pdf-link').should('exist').and('be.visible');
            });

            it('Displays SALE link', () => {
                SaleProcessing.saleSearchExpandedView('sale-link').should('exist').and('be.visible');
            });

            it('Displays QIVS link', () => {
                SaleProcessing.saleSearchExpandedView('qivs-link').should('exist').and('be.visible');
            });

            it('Displays WEB link', () => {
                SaleProcessing.saleSearchExpandedView('web-link').should('exist').and('be.visible');
            });

            it('Displays Close icon',() => {
                SaleProcessing.saleSearchExpandedView('close').should('exist').and('be.visible');
            });

            it('Should displays Property banner', () => {
                SaleProcessing.saleSearchExpandedView('property-banner').should('exist').and('be.visible');
            });

            it('Should displays Capital Value in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('capital-value').should('exist').and('be.visible');
            });

            it('Should displays Land Value in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('land-value').should('exist').and('be.visible');
            });

            it('Should displays Value Of Improvements in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('value-of-imp').should('exist').and('be.visible');
            });

            it('Should displays Land Area in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('land-area-TFA-TLA').should('exist').and('be.visible');
            });

            it('Should displays NSP/CV in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('nsp-by-cv').should('exist').and('be.visible');
            });

            it('Should displays NSP/RTV in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('nsp-by-rtv').should('exist').and('be.visible');
            });

            it('Should displays QIVS Title in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('qivs-title').should('exist').and('be.visible');
            });

            it('Should displays LINZ Title in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('linz-title').should('exist').and('be.visible');
            });

            it('Should displays QIVS Owner in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('qivs-owner').should('exist').and('be.visible');
            });

            it('Should displays Sale Vendor in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('sale-vendor').should('exist').and('be.visible');
            });

            it('Should displays Sale Purchaser in Property banner', () => {
                SaleProcessing.saleSearchExpandedView('sale-purchaser').should('exist').and('be.visible');
            });

            it.skip('Should not display Extra Property Details',() => {
                SaleProcessing.saleSearchExpandedView('property-landMas').should('not.exist');
            });

            it('Should display Sale Details', () => {
                SaleProcessing.saleSearchExpandedView('sale-details').should('exist').and('be.visible');
            });

        });
    });
});


describe('Ownership Update for QV Maintaining ownership in expanded view',{
    testIsolation: false,
    defaultCommandTimeout: 30000,
},() => {
    before(() => {
        cy.login();
        SaleProcessing.visit();
        SaleProcessing.setTas(ownershipTaCodes);
    });

    it.skip('Shows the ownership update section', () => {
        // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(1)').click();
        SaleProcessing.search();
        cy.wait(1000);
        SaleProcessing.saleSearchResult('val-ref').first().click();
        SaleProcessing.elements.searchResultsExpanded.should('exist');
        SaleProcessing.elements.searchResultsExpanded.should('have.length.greaterThan', 0);
        SaleProcessing.elements.ownershipUpdateSection.should('exist');
    });
});


describe('Sales Inspection: Sales Search expanded View ', {
    testIsolation: false,
    defaultCommandTimeout: 30000,
}, () => {
    context('As an internal user, non admin', () => {
        before(() => {
            cy.visitWithUser('/roll-maintenance/sales/inspection', user.INTERNAL_NON_ADMIN_USER);
            SaleProcessing.setTas(taCodes);
        });

        it('Displays search result when Search button is clicked with default criteria', () => {
            SaleProcessing.clear();
            // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(2)').click();
            // SaleProcessing.elements.processingStatusMultiselect.click({ force: true }).find('.multiselect__element:nth-child(3)').click();
            SaleProcessing.search();

            SaleProcessing.elements.searchResults.should('exist').and('be.visible');
            SaleProcessing.elements.saleSearchTopPagination.should('exist');
            SaleProcessing.elements.saleSearchBottomPagination.should('exist');
            SaleProcessing.elements.saleSearchNoResult.should('not.exist');
        });

        it('Shows the expanded view when a sale search result except Address is clicked', () => {
            SaleProcessing.saleSearchResult('val-ref').first().click();

            SaleProcessing.elements.searchResultsExpanded.should('exist');
            SaleProcessing.elements.searchResultsExpanded.should('have.length.greaterThan', 0);
        });

        it('Displays Address in result', () => {
            SaleProcessing.saleSearchExpandedView('address').should('exist').and('be.visible');
        });

        it('Displays Qpid in result', () => {
            SaleProcessing.saleSearchExpandedView('qpid').should('exist').and('be.visible');
        });

        it('Displays Sale Id in result', () => {
            SaleProcessing.saleSearchExpandedView('sale-id').should('exist').and('be.visible');
        });

        it('Displays Analysis in result', () => {
            SaleProcessing.saleSearchExpandedView('analysis').should('exist').and('be.visible');
        });

        it('Displays Source in result', () => {
            SaleProcessing.saleSearchExpandedView('source').should('exist').and('be.visible');
        });

        it('Displays Val Ref in result', () => {
            SaleProcessing.saleSearchExpandedView('val-ref').should('exist').and('be.visible');
        });

        it('Displays Portal ID in result', () => {
            SaleProcessing.saleSearchExpandedView('portal-id').should('exist').and('be.visible');
        });

        it('Displays Sale Status in result', () => {
            SaleProcessing.saleSearchExpandedView('status').should('exist').and('be.visible');
        });

        it('Displays Sale classification in result', () => {
            SaleProcessing.saleSearchExpandedView('sale-classification').should('exist').and('be.visible');
        });

        it('Displays PDF link', () => {
            SaleProcessing.saleSearchExpandedView('pdf-link').should('exist').and('be.visible');
        });

        it('Displays SALE link', () => {
            SaleProcessing.saleSearchExpandedView('sale-link').should('exist').and('be.visible');
        });

        it('Displays QIVS link', () => {
            SaleProcessing.saleSearchExpandedView('qivs-link').should('exist').and('be.visible');
        });

        it('Displays WEB link', () => {
            SaleProcessing.saleSearchExpandedView('web-link').should('exist').and('be.visible');
        });

        it('Displays Close icon',() => {
            SaleProcessing.saleSearchExpandedView('close').should('exist').and('be.visible');
        });

        it('Should displays Property banner', () => {
            SaleProcessing.saleSearchExpandedView('property-banner').should('exist').and('be.visible');
        });

        it('Should displays Capital Value in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('capital-value').should('exist').and('be.visible');
        });

        it('Should displays Land Value in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('land-value').should('exist').and('be.visible');
        });

        it('Should displays Value Of Improvements in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('value-of-imp').should('exist').and('be.visible');
        });

        it('Should displays Land Area in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('land-area-TFA-TLA').should('exist').and('be.visible');
        });

        it('Should displays NSP/CV in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('nsp-by-cv').should('exist').and('be.visible');
        });

        it('Should displays NSP/RTV in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('nsp-by-rtv').should('exist').and('be.visible');
        });

        it('Should displays QIVS Title in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('qivs-title').should('exist').and('be.visible');
        });

        it('Should displays LINZ Title in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('linz-title').should('exist').and('be.visible');
        });

        it('Should displays QIVS Owner in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('qivs-owner').should('exist').and('be.visible');
        });

        it('Should displays Sale Vendor in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('sale-vendor').should('exist').and('be.visible');
        });

        it('Should displays Sale Purchaser in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('sale-purchaser').should('exist').and('be.visible');
        });

        it('Should displays Property banner', () => {
            SaleProcessing.saleSearchExpandedView('property-banner').should('exist').and('be.visible');
        });

        it('Should displays Capital Value in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('capital-value').should('exist').and('be.visible');
        });

        it('Should displays Land Value in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('land-value').should('exist').and('be.visible');
        });

        it('Should displays Value Of Improvements in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('value-of-imp').should('exist').and('be.visible');
        });

        it('Should displays Land Area in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('land-area-TFA-TLA').should('exist').and('be.visible');
        });

        it('Should displays NSP/CV in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('nsp-by-cv').should('exist').and('be.visible');
        });

        it('Should displays NSP/RTV in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('nsp-by-rtv').should('exist').and('be.visible');
        });

        it('Should displays QIVS Title in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('qivs-title').should('exist').and('be.visible');
        });

        it('Should displays LINZ Title in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('linz-title').should('exist').and('be.visible');
        });

        it('Should displays QIVS Owner in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('qivs-owner').should('exist').and('be.visible');
        });

        it('Should displays Sale Vendor in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('sale-vendor').should('exist').and('be.visible');
        });

        it('Should displays Sale Purchaser in Property banner', () => {
            SaleProcessing.saleSearchExpandedView('sale-purchaser').should('exist').and('be.visible');
        });

        it('Should display Category in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-category').should('exist').and('be.visible');
        });

        it('Should display EYB in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-eyb').should('exist').and('be.visible');
        });

        it('Should display Land Use in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-land-use').should('exist').and('be.visible');
        });

        it('Should display Units in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-units').should('exist').and('be.visible');
        });

        it('Should display Bedrooms in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-bedrooms').should('exist').and('be.visible');
        });

        it('Should display Toilets in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-toilets').should('exist').and('be.visible');
        });

        it('Should display Wall contsruction and conditions in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-wall').should('exist').and('be.visible');
        });

        it('Should display Roof construction and conditions in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-roof').should('exist').and('be.visible');
        });

        it('Should display UMRG in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-umrg').should('exist').and('be.visible');
        });

        it('Should display FSG in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-fsg').should('exist').and('be.visible');
        });

        it('Should display Other Large Improvements in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-oli').should('exist').and('be.visible');
        });

        it('Should display Modernisation in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-modernisation').should('exist').and('be.visible');
        });

        it('Should display Landzone in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-land-zone').should('exist').and('be.visible');
        });

        it('Should display Lot position in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-lot-position').should('exist').and('be.visible');
        });

        it('Should display Contour in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-contour').should('exist').and('be.visible');
        });

        it('Should display View and Scope in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-view-scope').should('exist').and('be.visible');
        });

        it('Should display Maori Land in Extra Property Details',() => {
            SaleProcessing.saleSearchExpandedView('prop-maori-land').should('exist').and('be.visible');
        });

        it('Should display Sale Details', () => {
            SaleProcessing.saleSearchExpandedView('sale-details').should('exist').and('be.visible');
        });

        it.skip('Clicking SI consent should add SI consent', () => {
            cy.intercept('/web/salesProcessing/addSaleInspectionConsent').as('addConsent');
            SaleProcessing.addSaleInspectionConsent();

            cy.wait('@addConsent').then((int) => {
                const body = int.request.body;
                expect(body.description).to.equal('Sales Inspection Request');
            })
        });
    });
});


// TODO: move this to ownership update test when test factory available
// it('Shows errors in validation modal when trying to update without required fields', () => {
//     SaleProcessing.elements.surnameOrgInput.clear();
//     SaleProcessing.elements.updateSaleBtn.click();
//     SaleProcessing.elements.globalDialog.should('exist');
//     SaleProcessing.elements.saleValidationModalMessages.should('exist');
//     SaleProcessing.elements.saleValidationModalErrors.should('exist');
//     SaleProcessing.elements.saleValidationModalErrors.should('have.length.greaterThan', 0);
// });

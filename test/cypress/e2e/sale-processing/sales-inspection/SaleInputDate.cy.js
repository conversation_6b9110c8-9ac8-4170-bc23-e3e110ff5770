import SaleProcessing from "../../../model/SaleProcessing";

describe('Sales Inspection Date',{
  testIsolation: false,
  defaultCommandTimeout: 30000,
},() => {
  before(() => {
    cy.login();
    SaleProcessing.visitSalesInspection();
  });

  it('sets input sale date to FY 2023', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.saleInputDateFrom.type('01/07/2023');
    SaleProcessing.elements.saleInputDateTo.type('30/06/2024');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.least', 1);
  });

  it('sets input sale date from start of FY 2023 onwards', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.saleInputDateFrom.type('01/07/2023');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.least', 1);
  });

  it('sets input sale date in reverse', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.saleInputDateFrom.type('31/05/2023');
    SaleProcessing.elements.saleInputDateTo.type('01/05/2023');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('not.exist');
    SaleProcessing.elements.saleSearchTopPagination.should('not.exist');
    SaleProcessing.elements.saleSearchNoResult.should('exist').and('be.visible');
  });
});

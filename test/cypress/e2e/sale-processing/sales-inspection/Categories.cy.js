import SaleProcessing from "../../../model/SaleProcessing";

describe('Sales Inspection Search',{
  testIsolation: false,
  defaultCommandTimeout: 30000,
},() => {
  before(() => {
    cy.login();
    SaleProcessing.visitSalesInspection();
  });

  it('sets categories to invalid', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.categories.type('thisisinvalid');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('not.exist');
    SaleProcessing.elements.saleSearchTopPagination.should('not.exist');
    SaleProcessing.elements.saleSearchNoResult.should('exist').and('be.visible');
  });

  it('sets categories to residential', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.categories.type('R*');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.least', 5);
    SaleProcessing.elements.saleSearchTopPagination.should('exist');
    SaleProcessing.elements.saleSearchBottomPagination.should('exist');
    SaleProcessing.elements.saleSearchNoResult.should('not.exist');
  });
});

import SaleProcessing from "../../../model/SaleProcessing";

describe('Sales Inspection Sales Classification',{
  testIsolation: false,
  defaultCommandTimeout: 30000,
},() => {
  before(() => {
    cy.login();
    SaleProcessing.visitSalesInspection();
  });

  it('sets classifications to Market Sales', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.saleClassificationsSelectDiv.find('button').click({ force: true });
    cy.wait(700);
    SaleProcessing.elements.saleClassificationsSelectDiv.find('label.checkbox').then(elements => {
      elements.each((index, $el) => {
        // note: using an include rather than === because the innerText includes some spacing
        if ($el.innerText.includes('Market Sales')) {
          $el.click();
        };
      });
    });

    SaleProcessing.search();

    SaleProcessing.elements.saleClassificationsSelectDiv.find('.multiselect-selected-text').should('include.text', '5 selected');
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.least', 1);
    SaleProcessing.elements.saleSearchTopPagination.should('exist');
    SaleProcessing.elements.saleSearchBottomPagination.should('exist');
    SaleProcessing.elements.saleSearchNoResult.should('not.exist');
  });
});

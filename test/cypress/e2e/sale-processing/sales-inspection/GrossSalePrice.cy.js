import SaleProcessing from "../../../model/SaleProcessing";

describe('Sales Inspection Gross Sale Price',{
  testIsolation: false,
  defaultCommandTimeout: 30000,
},() => {
  before(() => {
    cy.login();
    SaleProcessing.visitSalesInspection();
  });

  it('sets gross sale price to a range', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.gspFrom.type('513000');
    SaleProcessing.elements.gspTo.type('514000');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.least', 1);
  });

  it('sets a minimum GSP of 80 million', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.gspFrom.type('80000000');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.most', 20);
  });

  it('sets a maximum of $500', () => {
    SaleProcessing.clear();
    // note: if this is left blank, there are 30ish results where NSP, GSP and others are null value
    SaleProcessing.elements.gspFrom.type('1');
    SaleProcessing.elements.gspTo.type('500');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.most', 40);
  });
});

import SaleProcessing from "../../../model/SaleProcessing";

describe('Sales Inspection Sale Status',{
  testIsolation: false,
  defaultCommandTimeout: 30000,
},() => {
  before(() => {
    cy.login();
    SaleProcessing.visitSalesInspection();
  });

  it('sets sale status to unconfirmed', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Confirmed") input[type="checkbox"]').click();
    SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Unconfirmed") input[type="checkbox"]').click();
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.least', 1);
  });

  it('sets sale status to pending', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Confirmed") input[type="checkbox"]').click();
    SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Pending") input[type="checkbox"]').click();
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('not.exist');
    SaleProcessing.elements.saleSearchTopPagination.should('not.exist');
    SaleProcessing.elements.saleSearchNoResult.should('exist').and('be.visible');
  });
});

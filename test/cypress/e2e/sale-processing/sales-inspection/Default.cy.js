import SaleProcessing from "../../../model/SaleProcessing";

describe('Sales Inspection default form',{
  testIsolation: false,
  defaultCommandTimeout: 30000,
},() => {
  before(() => {
    cy.login();
    SaleProcessing.visitSalesInspection();
  });

  it('starts on Sales Inspection tab', () => {
    SaleProcessing.elements.SalesInspectionTab.should('exist').and('be.visible');
    SaleProcessing.elements.SalesInspectionTab.should('have.class', 'router-link-active');
  });

  it('starts with empty results', () => {
    SaleProcessing.elements.searchResults.should('have.length', 0);
  });

  it('Displays Sales Inspection search form', () => {
    SaleProcessing.elements.taSelect.should('exist').and('be.visible');
    SaleProcessing.elements.saleDateFrom.should('exist').and('be.visible');
    SaleProcessing.elements.saleDateTo.should('exist').and('be.visible');
    SaleProcessing.elements.saleInputDateFrom.should('exist').and('be.visible');
    SaleProcessing.elements.saleInputDateTo.should('exist').and('be.visible');
    SaleProcessing.elements.categories.should('exist').and('be.visible');
    SaleProcessing.elements.source.should('exist').and('be.visible');
    SaleProcessing.elements.saleClassificationsSelectDiv.should('exist').and('be.visible');
    SaleProcessing.elements.processingStatusMultiselect.should('exist').and('be.visible');
    SaleProcessing.elements.gspFrom.should('exist').and('be.visible');
    SaleProcessing.elements.gspTo.should('exist').and('be.visible');
    SaleProcessing.elements.excludeFromHpiRtv.should('exist').and('be.visible');
    SaleProcessing.elements.clearButton.should('exist').and('be.visible');
    SaleProcessing.elements.searchButton.should('exist').and('be.visible');
  });

  it('has all TA codes', () => {
    SaleProcessing.elements.taSelectDiv.find('.multiselect-selected-text').should('include.text', "All TA's Selected");
  });

  it('Expect default sale classification selection', () => {
    SaleProcessing.elements.saleClassificationsSelectDiv.find('input[type="checkbox"]:checked').should('have.length',4);
  });

  it('Displays default Procesing status selection : Processed and Auto-Processed', () => {
    SaleProcessing.elements.processingStatusMultiselect.find('.multiselect__tag').then(elements => {
      const textArray = elements.map((_, el) => Cypress.$(el).text().trim()).get();
      expect(textArray).to.deep.equal(['Processed', 'Auto-Processed']);
    });
  });

  it('Expect default Sale Status : Confirmed', () => {
    SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Confirmed") input[type="checkbox"]').should('be.checked');
    SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Pending") input[type="checkbox"]').should('not.be.checked');
    SaleProcessing.elements.saleStatus.find('.saleStatus:contains("Unconfirmed") input[type="checkbox"]').should('not.be.checked');
  });

  it('has remaining empty fields', () => {
    SaleProcessing.elements.saleDateFrom.invoke('text').should('be.empty');
    SaleProcessing.elements.saleDateTo.invoke('text').should('be.empty');
    SaleProcessing.elements.saleInputDateFrom.invoke('text').should('be.empty');
    SaleProcessing.elements.saleInputDateTo.invoke('text').should('be.empty');
    SaleProcessing.elements.categories.invoke('text').should('be.empty');
    SaleProcessing.elements.source.find('.multiselect__tags-wrap').should('not.be.visible');
    SaleProcessing.elements.gspFrom.invoke('text').should('be.empty');
    SaleProcessing.elements.gspTo.invoke('text').should('be.empty');
    SaleProcessing.elements.excludeFromHpiRtv.should('not.be.checked');
  });

  it('performs a default search', () => {
    cy.intercept('POST', '/web/salesSearch/getSearchSale').as('getSearchSale');
    cy.intercept('POST', '/searchProperties').as('searchProperties');
    SaleProcessing.search();
    cy.wait(['@getSearchSale', '@searchProperties']);
    SaleProcessing.elements.searchResults.should('have.length.at.least', 5);
  });

  it('Displays search results', () => {
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');    
    SaleProcessing.elements.searchResults.should('have.length.at.least', 1);
    SaleProcessing.elements.saleSearchTopPagination.should('exist');
    SaleProcessing.elements.saleSearchBottomPagination.should('exist');
    SaleProcessing.elements.saleSearchNoResult.should('not.exist');
  });
});

import SaleProcessing from "../../../model/SaleProcessing";

describe('Sales Inspection Search',{
  testIsolation: false,
  defaultCommandTimeout: 30000,
},() => {
  before(() => {
    cy.login();
    SaleProcessing.visitSalesInspection();
  });

  it('sets sources to SalesDirect and TA', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.source.click({ force: true }).find('.multiselect__element:nth-child(2)').click();
    SaleProcessing.elements.source.click({ force: true }).find('.multiselect__element:nth-child(4)').click();
    SaleProcessing.elements.source.find('input').blur();
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');
    SaleProcessing.elements.searchResults.should('have.length.at.least', 1);
  });
});

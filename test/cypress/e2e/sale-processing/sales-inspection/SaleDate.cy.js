import SaleProcessing from "../../../model/SaleProcessing";

describe('Sales Inspection Date',{
  testIsolation: false,
  defaultCommandTimeout: 30000,
},() => {
  before(() => {
    cy.login();
    SaleProcessing.visitSalesInspection();
  });

  it('sets date to May 2023', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.saleDateFrom.type('01/05/2023');
    SaleProcessing.elements.saleDateTo.type('31/05/2023');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('exist').and('be.visible');    
    SaleProcessing.elements.searchResults.should('have.length.at.least', 1);
  });

  it('sets dates in reverse', () => {
    SaleProcessing.clear();
    SaleProcessing.elements.saleDateFrom.type('31/05/2023');
    SaleProcessing.elements.saleDateTo.type('01/05/2023');
    SaleProcessing.search();
    SaleProcessing.elements.searchResults.should('not.exist');
    SaleProcessing.elements.saleSearchTopPagination.should('not.exist');
    SaleProcessing.elements.saleSearchNoResult.should('exist').and('be.visible');
  });
});

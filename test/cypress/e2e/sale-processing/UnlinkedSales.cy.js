import SaleProcessing from '../../model/SaleProcessing.js';
import UnlinkedSaleSearch from '../../model/UnlinkedSaleSearch.js';

describe('Unlinked Sales Search', {
    testIsolation: false,
    defaultCommandTimeout: 30000,
}, () => {
    context('As internal user with admin role', () => {
        before(() => {
            cy.login();
            SaleProcessing.visit();
        });

        it('displays unlinked sales tab', () => {
            SaleProcessing.elements.UnlinkedTab.should('exist');
        });
    });

    // FIXME: broken overrideUserData: can't override internal user's roles
    context.skip('As internal user with customer care role', () => {
        before(() => {
            cy.login();
            cy.overrideUserData({
                roles: [{ name: 'Customer Care' }],
            });
            SaleProcessing.visit();
        });

        it('displays unlinked sales tab', () => {
            SaleProcessing.elements.UnlinkedTab.should('exist');
        });
    });

    context.skip('As internal user without admin or customer care role', () => {
        before(() => {
            cy.login();
            cy.overrideUserData({
                roles: [{ name: 'Registered Valuer' }],
            });
            SaleProcessing.visit();
        });

        it('hides unlinked sales tab', () => {
            SaleProcessing.elements.UnlinkedTab.should('not.exist');
        });
    });

    context('As an external user', () => {
        before(() => {
            cy.login();
            cy.overrideUserData({
                roles: [{ name: 'EXTERNAL_USER_READ' }],
            });
            SaleProcessing.visit();
        });

        it('hides unlinked sales tab', () => {
            SaleProcessing.elements.UnlinkedTab.should('not.exist');
        });
    });

    context('Making a valid search', () => {
        before(() => {
            cy.login();
            SaleProcessing.visit();
            SaleProcessing.elements.UnlinkedTab.click();
            UnlinkedSaleSearch.search();
        });

        it('displays result count', () => {
            UnlinkedSaleSearch.elements.LabelResultCount.should('be.visible');
        });
    });

    context('Clearing search results', () => {
        before(() => {
            cy.intercept('/api/salesProcessing/searchUnlinkedSales').as('searchUnlinkedSales');

            cy.login();
            SaleProcessing.visit();
            SaleProcessing.elements.UnlinkedTab.click();
            UnlinkedSaleSearch.search();
            // wait for the first response to finish
            cy.wait('@searchUnlinkedSales');
            cy.wait(1000);
            UnlinkedSaleSearch.clear();
        });

        it('clears results', () => {
            UnlinkedSaleSearch.elements.ResultRows.should('not.exist');
        });

        it('shows placeholder text', () => {
            UnlinkedSaleSearch.elements.LabelResultPlaceholder.should('be.visible');
        });
    });
});

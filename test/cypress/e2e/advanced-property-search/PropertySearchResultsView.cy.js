/* global cy it describe context */

import AdvancedSearch from '../../model/AdvancedSearch';
import AdvancedSearchProperty from '../../model/AdvancedSearchProperty';
import AdvancedSearchSales from '../../model/AdvancedSearchSales';
import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';
import PropertySearchResults from '../../model/PropertySearchResults';

const streetNumTo = 2;
const streetName = 'Southwark';
const category1 = 'RF*';
const category1Prefix = 'RF';
const salesGroup = '01 - Far North';

const streetSearch = 'Southwark Avenue';
const minResults = 20;

describe('Property Search', { defaultCommandTimeout: 15000 }, () => {
    it('Quick search typeahead', () => {
        cy.visitWithLogin(Home.href);
        Home.quickSearchBar.type(env.ADDRESS1_SEARCH_TERM);
        Home.typeAheadResults.should('exist');
        Home.typeAheadResults.children().should('have.length.at.least', 1);
        Home.typeAheadResults.contains('span.listBox-category', env.ADDRESS1_QPID).should('exist');
    });

    //one result => property
    it('Quick search and enter', () => {
        Home.quickSearchBar.type('{enter}');
        PropertyDetails.qpid.invoke('text').then(text => {
            expect(text).to.equal(env.ADDRESS1_QPID);
        });
        PropertyDetails.addressLine1.should('contain.text', env.ADDRESS1_SEARCH_TERM);
    });

    describe('Search results: different views: compact, expanded', () => {
        it(`Search ${streetSearch}`, () => {
            cy.intercept({
                method: 'POST',
                url: `${env.BASE_URL}displayPropertySearchResult`
            }).as('displayPropertySearchResult');

            Home.quickSearchBar.clear().type(`${streetSearch}{enter}`);
            cy.wait('@displayPropertySearchResult').then(interception => {
                cy.wait(3000);
                PropertySearchResults.results.should('have.length.at.least', minResults);
                PropertySearchResults.compactViewToggle.click();
                PropertySearchResults.results.each(result => {
                    cy.wrap(result).should('have.class', 'theSkinny');
                });
                PropertySearchResults.compactViewToggle.click();
                PropertySearchResults.results.each(result => {
                    cy.wrap(result).should('not.have.class', 'theSkinny');
                });
                PropertySearchResults.expandResultsButton.click();
                PropertySearchResults.results.each(result => {
                    cy.wrap(result).should('have.class', 'openProp');
                    cy.wrap(result)
                        .find(PropertySearchResults.resultToolbarRightSelector)
                        .find(PropertySearchResults.uploadPhotosSelector)
                        .should('exist')
                        .and('be.visible');
                    cy.wrap(result)
                        .find(PropertySearchResults.resultToolbarRightSelector)
                        .contains('label', 'WEB')
                        .should('exist')
                        .and('be.visible');
                });
                PropertySearchResults.expandResultsButton.click();
                PropertySearchResults.results.each(result => {
                    cy.wrap(result).should('not.have.class', 'openProp');
                });
            });
        });
    });

    context(
        `streetNumTo: ${streetNumTo}, streetName: ${streetName}, category1: ${category1}`,
        () => {
            it('Advanced Search', () => {
                cy.intercept({
                    method: 'POST',
                    url: `${env.BASE_URL}displayPropertySearchResult`
                }).as('displayPropertySearchResult');

                Home.advancedSearchButton.click({ force: true });
                AdvancedSearch.propertySearch.click();
                AdvancedSearch.streetNumTo.clear().type(streetNumTo);
                AdvancedSearch.streetName.clear().type(streetName);
                AdvancedSearchProperty.includeCategory1.clear().type(category1);
                AdvancedSearch.search();

                cy.wait('@displayPropertySearchResult').then(interception => {
                    expect(interception.response.statusCode).to.equal(200);
                    PropertySearchResults.header.should('exist');
                    PropertySearchResults.results.should('have.length.at.least', 1);
                    //TODO: Figure out why this doesnt work (trying to test each result category starts with RF)
                    //fix the category selector
                    PropertySearchResults.results.each(result => {
                        cy.wrap(result)
                            .contains(PropertySearchResults.categorySelector, 'RF')
                            .invoke('text')
                            .then(text => {
                                expect(text).to.include(category1Prefix);
                            });
                    });
                });
            });
        }
    );

    it('Search inputs remembered', () => {
        //TODO:
    });

    it('Clears search inputs', () => {
        //TODO:
    });

    describe('Sales groups and rolls', () => {
        it(`Search for ${salesGroup}`, () => {
            cy.intercept({
                method: 'POST',
                url: `${env.BASE_URL}displayPropertySearchResult`
            }).as('displayPropertySearchResult');

            Home.advancedSearchButton.click({ force: true });
            cy.wait(2000);
            AdvancedSearch.clear();
            cy.contains('span', 'Sales Groups & Rolls').click();
            cy.contains('label', salesGroup)
                .find('input[type="checkbox"]')
                .check({ force: true });
            cy.contains('span', 'Set rolls').click({ force: true });
            AdvancedSearch.search();

            cy.wait('@displayPropertySearchResult').then(interception => {
                expect(interception.response.statusCode).to.equal(200);
                PropertySearchResults.header.should('exist');
                PropertySearchResults.results.should('have.length.at.least', 1);
            });
        });
    });
});

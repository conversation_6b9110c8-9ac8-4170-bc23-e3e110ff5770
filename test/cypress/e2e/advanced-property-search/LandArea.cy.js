import Home from '../../model/Home';
import AdvancedSearch from '../../model/AdvancedSearch';
import AdvancedSearchProperty from '../../model/AdvancedSearchProperty';
import PropertySearchResults from '../../model/PropertySearchResults';

describe('Property Search', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    cy.visitWithLogin('');
  });

  it(`restricts search by land area`, () => {
    Home.advancedSearchButton.click();
    AdvancedSearch.propertySearch.click();
    AdvancedSearch.taRangeFrom.clear().type(env.TA_CODE.CHCH);
    AdvancedSearch.taRangeTo.clear().type(env.TA_CODE.CHCH);
    AdvancedSearchProperty.landAreaFrom.clear().type(10);
    AdvancedSearchProperty.landAreaTo.clear().type(20);
    AdvancedSearch.search();
    cy.wait(3000);
    PropertySearchResults.results.should('have.length.at.least', 5);
  });

  it('has results with land area of 10-20 hectares', () => {
    PropertySearchResults.results.each(result => {
      cy.wrap(result)
        .find(PropertySearchResults.landAreaSelector)
        .invoke('text')
        .then(text => {
          const landArea = parseInt(text);
          expect(landArea).to.be.gte(10);
          expect(landArea).to.be.lte(20);
        });
    });
  });
});

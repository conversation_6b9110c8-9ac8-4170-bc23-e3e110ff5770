
import { MobileValuationJob, OverViewTab, ValuationJobTab, InteriorTab, BedroomsTab, KitchenTab, BathroomsTab, GaragingTab, ImprovementsTab, JobInfo, Comparables } from '../../model/MobileValuationJob';

const valuationJobPage = '?valJobId=85ae3ab8-416a-4035-b014-748b62df541e&propertyId=da0f20f2-a0d5-46c2-b42a-479d0b127822';
describe('Mobile Valuation job', { defaultCommandTimeout: 120000 }, () => {
    Cypress.config('viewportWidth', env.MOBILE_SCREEN_WIDTH);
    Cypress.config('viewportHeight', env.MOBILE_SCREEN_HEIGHT);

    before(() => {
        cy.visitWithLogin(valuationJobPage);
    });

    context('Validate the Summary Page for the Valuation Job', () => {

        it('Property Address section should exist and be visible', () => {
            const expectedTexts = ['18 Mansor Court', 'Central Otago District'];
            MobileValuationJob.elements.propertyAddress
                .should('exist')
                .and('be.visible')
                .and($element => {
                    expectedTexts.forEach(text => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });

        it('Property Val Ref section should exist and be visible', () => {
            const expectedTexts = ['Val Ref:'];
            MobileValuationJob.elements.propertyValRef
                .should('exist')
                .and('be.visible')
                .and($element => {
                    expectedTexts.forEach(text => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });

        it('Property Category section should exist and be visible', () => {
            const expectedTexts = ['RD200B:', 'Effective:',];
            MobileValuationJob.elements.propertyCategory
                .should('exist')
                .and('be.visible')
                .and($element => {
                    expectedTexts.forEach(text => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });

        it('The Property Summary section should exist and be visible', () => {
            const expectedTexts = ['Units:', 'Total Floor Area:',
                'Total Living Area:', 'Land Area:', 'Occupier:',
                'Occupier:', 'Improvements:'];
            MobileValuationJob.elements.propertySummary
                .should('exist')
                .and('be.visible')
                .and(($element) => {
                    expectedTexts.forEach((text) => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });


        it('Property sale date section should exist and be visible', () => {
            const expectedTexts = ['Last Sale:', 'Confirmed'];
            MobileValuationJob.elements.propertySaleDate
                .should('exist')
                .and('be.visible')
                .and(($element) => {
                    expectedTexts.forEach((text) => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });


        it('The Property Sale Details section should exist and be visible', () => {
            const expectedTexts = ['Net Sale Price', 'Chattels', 'Analysed Land', 'NSP/CV', 'Sale BNR', 'Gross Rate'];
            MobileValuationJob.elements.propertySaleDetails
                .should('exist')
                .and('be.visible')
                .and(($element) => {
                    expectedTexts.forEach((text) => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });

        it('The Property estimates section should exist and be visible', () => {
            const expectedTexts = ['RealTime Value', 'Market Estimate:'];
            MobileValuationJob.elements.propertyEstimates
                .should('exist')
                .and('be.visible')
                .and(($element) => {
                    expectedTexts.forEach((text) => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });

        it('The Property Values section should exist and be visible', () => {
            const expectedTexts = ['Capital Value', 'Land Value', 'Improvements', 'Building Net Rate'];
            MobileValuationJob.elements.propertyValues
                .should('exist')
                .and('be.visible')
                .and(($element) => {
                    expectedTexts.forEach((text) => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });

        it('The Property Icon section should exist and be visible', () => {
            const expectedTexts = ['Category', 'RD200B', 'Effective Year Built', 'Bedrooms', 'Toilets'
                , 'Units', 'Free Standing Garaging', 'Under Main Roof Garaging', 'Other Large Improvements',
                'Modernisation', 'Zone', 'Lot Position', 'Maori Land', 'Land Use',
                'Wall Construction and Condition', 'Roof Construction and Condition', 'Contour', 'View and Scope', 'Production'];
            MobileValuationJob.elements.propertyIconLabel
                .should('exist')
                .and('be.visible')
                .and(($element) => {
                    expectedTexts.forEach((text) => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });
    });
    context('Validate the Edit details page for the valuation job', () => {

        context('Validate the overview Tab page', () => {
            before(() => {
                MobileValuationJob.elements.valuationJobEditDetails
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Edit Details')
                    .wait(1000)
                    .click({ force: true });
            });

            it('Bedroom field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.bedrooms
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bedrooms')
                OverViewTab.elements.bedroomsInputBox.scrollIntoView();
                OverViewTab.elements.bedroomsInputBox
                    .type('3', { force: true }).clear({ force: true })
                OverViewTab.elements.bedroomsInputBox
                    .type('3', { force: true })
                OverViewTab.elements.bedroomsInputBox
                    .should('have.value', '3');
            });

            it('Bathroom field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.bathrooms
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bathrooms');
                OverViewTab.elements.bathroomsInputBox
                    .type('3', { force: true }).clear({ force: true })
                OverViewTab.elements.bathroomsInputBox
                    .type('3', { force: true })
                OverViewTab.elements.bedroomsInputBox
                    .should('have.value', '3');
            });

            it('Toilets field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.toilets
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Toilets');
                OverViewTab.elements.toiletsInputBox
                    .type('3', { force: true }).clear({ force: true })
                OverViewTab.elements.toiletsInputBox
                    .type('3', { force: true })
                OverViewTab.elements.bedroomsInputBox
                    .should('have.value', '3');
            });
            it('LivingAreas field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.livingAreas
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Living Areas');
                OverViewTab.elements.livingAreasInputBox
                    .type('3', { force: true }).clear({ force: true })
                OverViewTab.elements.livingAreasInputBox
                    .type('3', { force: true })
                OverViewTab.elements.livingAreasInputBox
                    .should('have.value', '3');
            });

            it('Garaging field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.garaging
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Garaging');
                OverViewTab.elements.garagingInputBox
                    .type('3', { force: true }).clear({ force: true })
                OverViewTab.elements.garagingInputBox
                    .type('3', { force: true })
                OverViewTab.elements.garagingInputBox
                    .should('have.value', '3');

            });
            it('OffStreetParking field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.offStreetParking
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Offstreet Parking');
                OverViewTab.elements.offStreetParkingInputBox
                    .type('3', { force: true }).clear({ force: true })
                OverViewTab.elements.offStreetParkingInputBox
                    .type('3', { force: true })
                OverViewTab.elements.offStreetParkingInputBox
                    .should('have.value', '3');
            });

            it('HouseType field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.houseType
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'House Type');
                OverViewTab.elements.houseTypeSelectOption
                    .select('Apartment', { force: true });
                OverViewTab.elements.houseTypeSelectOption.contains('option', 'Apartment')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('YearBuilt field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.yearBuilt
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Year Built');
                OverViewTab.elements.yearBuiltInputBox
                    .type('2003', { force: true }).clear({ force: true })
                OverViewTab.elements.yearBuiltInputBox
                    .type('2003', { force: true })
                OverViewTab.elements.yearBuiltInputBox
                    .should('have.value', '2003');
            });

            it('Total Floor Area field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.totalFloorArea
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Total Floor Area');
                OverViewTab.elements.totalFloorAreaInputBox
                    .clear({ force: true })
                OverViewTab.elements.totalFloorAreaInputBox
                    .type('2003', { force: true })
                OverViewTab.elements.totalFloorAreaInputBox
                    .should('have.value', '2003');
            });

            it('Total Floor Area Description field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.totalFloorAreaDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Total Floor Area Description');
                OverViewTab.elements.totalFloorAreaDescriptionInputBox
                    .clear({ force: true })
                OverViewTab.elements.totalFloorAreaDescriptionInputBox
                    .type('244 m2', { force: true })
                OverViewTab.elements.totalFloorAreaDescriptionInputBox
                    .should('have.value', '244 m2');
            });

            it('Exterior Cladding field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.exteriorCladding
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Exterior Cladding');
                OverViewTab.elements.exteriorCladdingSelectOption
                    .select('Corrugated steel', { force: true })
                OverViewTab.elements.exteriorCladdingSelectOption.contains('option', 'Corrugated steel')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Foundation field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.foundation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Foundation');
                OverViewTab.elements.foundationSelectOption
                    .select('Concrete perimeter wall and piles', { force: true })
                OverViewTab.elements.foundationSelectOption.contains('option', 'Concrete perimeter wall and piles')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Joinery field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.joinery
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Joinery');
                OverViewTab.elements.joinerySelectOption
                    .select('Aluminium (double glazed)', { force: true })
                OverViewTab.elements.joinerySelectOption.contains('option', 'Aluminium (double glazed)')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Roof Style field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.roofStyle
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Roof Style');
                OverViewTab.elements.roofStyleSelectOption
                    .select('Gable', { force: true })
                OverViewTab.elements.roofStyleSelectOption.contains('option', 'Gable')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Roof Construction field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.roofConstruction
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Roof Construction');
                OverViewTab.elements.roofConstructionSelectOption
                    .select('Aluminium', { force: true })
                OverViewTab.elements.roofConstructionSelectOption.contains('option', 'Aluminium')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('External Condition field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.externalCondition
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'External Condition');
                OverViewTab.elements.externalConditionSelectOption
                    .select('Fair', { force: true })
                OverViewTab.elements.externalConditionSelectOption.contains('option', 'Fair')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Quality of External Presentation field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.qualityOfExternalPresentation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Quality of External Presentation');
                OverViewTab.elements.qualityOfExternalPresentationSelectOption
                    .select('Standard', { force: true })
                OverViewTab.elements.qualityOfExternalPresentationSelectOption.contains('option', 'Standard')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Internal Condition field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.internalCondition
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Internal Condition');
                OverViewTab.elements.internalConditionSelectOption
                    .select('Fair', { force: true })
                OverViewTab.elements.internalConditionSelectOption.contains('option', 'Fair')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Quality of Internal Presentation field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.qualityOfInternalPresentation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Quality of Internal Presentation');
                OverViewTab.elements.qualityOfInternalPresentationSelectOption
                    .select('Superior', { force: true })
                OverViewTab.elements.qualityOfInternalPresentationSelectOption.contains('option', 'Superior')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Standard of Accomodation field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.standardOfAccomodation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Standard of Accomodation');
                OverViewTab.elements.standardOfAccomodationSelectOption
                    .select('Fair', { force: true })
                OverViewTab.elements.standardOfAccomodationSelectOption.contains('option', 'Fair')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Layout Description field exists, is visible, selectable, and validates the selected value.', () => {
                OverViewTab.elements.layoutDescriptions
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Layout Description');
                OverViewTab.elements.layoutDescriptionsSelectOption
                    .select('Home lacks separation of bedrooms and living areas', { force: true })
                OverViewTab.elements.layoutDescriptionsSelectOption.contains('option', 'Home lacks separation of bedrooms and living areas')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Maintenance Required field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.maintenanceRequired
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Maintenance Required');
                OverViewTab.elements.maintenanceRequiredInputBox
                    .type('test', { force: true }).clear({ force: true })
                OverViewTab.elements.maintenanceRequiredInputBox
                    .type('test', { force: true })
                OverViewTab.elements.maintenanceRequiredInputBox
                    .should('have.value', 'test');
            });

            it('Immediate Maintenance Required field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.immediateMaintenanceRequired
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Immediate Maintenance Required');
                OverViewTab.elements.immediateMaintenanceRequiredInputBox
                    .type('test', { force: true }).clear({ force: true })
                OverViewTab.elements.immediateMaintenanceRequiredInputBox
                    .type('test', { force: true })
                OverViewTab.elements.immediateMaintenanceRequiredInputBox
                    .should('have.value', 'test');
            });

            it('Recent Alterations field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.recentAlterations
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Recent Alterations');
                OverViewTab.elements.recentAlterationsInputBox
                    .type('test', { force: true }).clear({ force: true })
                OverViewTab.elements.recentAlterationsInputBox
                    .type('test', { force: true })
                OverViewTab.elements.recentAlterationsInputBox
                    .should('have.value', 'test');
            });

            it('Code Compliance field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.codeCompliance
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Code Compliance');
                OverViewTab.elements.codeComplianceInputBox
                    .type('We ', { force: true }).clear({ force: true });
                OverViewTab.elements.codeComplianceInputBox
                    .type('We assume all buildings and subsequent alterations are compliant', { force: true })
                OverViewTab.elements.codeComplianceInputBox
                    .should('have.value', 'We assume all buildings and subsequent alterations are compliant');
            });

            it('Overview Notes field exists, is visible, editable, and validates the entered value.', () => {
                OverViewTab.elements.overviewNotes
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Overview Notes');
                OverViewTab.elements.overviewNotesTextBox
                    .type('test', { force: true }).clear({ force: true })
                OverViewTab.elements.overviewNotesTextBox
                    .type('test', { force: true })
                OverViewTab.elements.overviewNotesTextBox
                    .should('have.value', 'test');
            });


        });

        context('Validate the Interior Tab page', () => {
            before(() => {
                ValuationJobTab.elements.interiorTab
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Interior')
                    .wait(1000)
                    .click();
            });
            it('living Area field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.livingArea
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Living Area')
                InteriorTab.elements.livingAreaSelectOption
                    .select('Family Room', { force: true })
                InteriorTab.elements.livingAreaSelectOption
                    .contains('option', 'Family Room')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });
            it('living Area Description field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.livingAreaDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Living Area Description')
                InteriorTab.elements.livingAreaDescriptionSelectOption
                    .select('Aluminium joinery', { force: true })
                InteriorTab.elements.livingAreaDescriptionSelectOption
                    .contains('option', 'Aluminium joinery')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Laundry field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.laundry
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Laundry')
                InteriorTab.elements.laundrySelectOption
                    .select('Concrete floor', { force: true })
                InteriorTab.elements.laundrySelectOption
                    .contains('option', 'Concrete floor')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('internalLinings field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.internalLinings
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Internal Linings')
                InteriorTab.elements.internalLiningsSelectOption
                    .select('Timber veneer', { force: true })
                InteriorTab.elements.internalLiningsSelectOption
                    .contains('option', 'Timber veneer')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });

            });
            it('floors field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.floors
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Floors')
                InteriorTab.elements.floorsSelectOption
                    .select('Native timber', { force: true })
                InteriorTab.elements.floorsSelectOption
                    .contains('option', 'Native timber')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Chattels field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.chattels
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Chattels')
                InteriorTab.elements.chattelsSelectOption
                    .select('Floor coverings', { force: true })
                InteriorTab.elements.chattelsSelectOption
                    .contains('option', 'Floor coverings')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Heating Type field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.heatingType
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Heating Type')
                InteriorTab.elements.heatingTypeSelectOption
                    .select('Underfloor heating', { force: true })
                InteriorTab.elements.heatingTypeSelectOption
                    .contains('option', 'Underfloor heating')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Insulation field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.insulation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Insulation')
                InteriorTab.elements.insulationSelectOption
                    .select('Underfloor', { force: true })
                InteriorTab.elements.insulationSelectOption
                    .contains('option', 'Underfloor')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Plumbing Age field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.plumbingAge
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Plumbing Age')
                InteriorTab.elements.plumbingAgeSelectOption
                    .select('2010-19', { force: true })
                InteriorTab.elements.plumbingAgeSelectOption
                    .contains('option', '2010-19')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Wiring Age field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.wiringAge
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Wiring Age')
                InteriorTab.elements.wiringAgeSelectOption
                    .select('2010-19', { force: true })
                InteriorTab.elements.plumbingAgeSelectOption
                    .contains('option', '2010-19')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Double Glazing field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.doubleGlazing
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Double Glazing')
                InteriorTab.elements.doubleGlazingSelectOption
                    .select('Full double glazing', { force: true })
                InteriorTab.elements.doubleGlazingSelectOption
                    .contains('option', 'Full double glazing')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Alternative Energy field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.alternativeEnergy
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Alternative Energy')
                InteriorTab.elements.alternativeEnergySelectOption
                    .select('Electric vehicle charging', { force: true })
                InteriorTab.elements.alternativeEnergySelectOption
                    .contains('option', 'Electric vehicle charging')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Ventilation field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.ventilation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Ventilation')
                InteriorTab.elements.ventilationSelectOption
                    .select('Yes', { force: true })
                InteriorTab.elements.ventilationSelectOption
                    .contains('option', 'Yes')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('redecoration Age field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.redecorationAge
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Redecoration Age')
                InteriorTab.elements.redecorationAgeSelectOption
                    .select('2010-19', { force: true })
                InteriorTab.elements.redecorationAgeSelectOption
                    .contains('option', '2010-19')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });
            it('Other Features field exists, is visible, selectable, and validates the selected value.', () => {

                InteriorTab.elements.otherFeatures
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Other Features')
                InteriorTab.elements.otherFeaturesSelectOption
                    .select('Architecturally designed', { force: true })
                InteriorTab.elements.otherFeaturesSelectOption
                    .contains('option', 'Architecturally designed')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });
            it('Interior Notes field exists, is visible, selectable, and validates the selected value.', () => {
                InteriorTab.elements.interiorNotes
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Interior Notes')
                InteriorTab.elements.interiorNotestextBox
                    .type('test interior notes', { force: true }).clear({ force: true })
                InteriorTab.elements.interiorNotestextBox
                    .type('test interior notes', { force: true })
                InteriorTab.elements.interiorNotestextBox
                    .should('have.value', 'test interior notes');
            });
        });


        context('Validate the Bedroom Tab page', () => {
            before(() => {
                ValuationJobTab.elements.bedroomsTab
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bedrooms')
                    .wait(1000)
                    .click();
            });
            it('Bedroom field exists, is visible, selectable, and validates the selected value.e', () => {
                BedroomsTab.elements.bedroom
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bedroom')
                BedroomsTab.elements.bedroomSelectOption
                    .select('Master Bedroom', { force: true })
                BedroomsTab.elements.bedroomSelectOption
                    .contains('option', 'Master Bedroom')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Bedroom Description field exists, is visible, selectable, and validates the selected value.', () => {
                BedroomsTab.elements.bedroomDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bedroom Description')
                BedroomsTab.elements.bedroomDescriptionSelectOption
                    .select('Carpet', { force: true })
                BedroomsTab.elements.bedroomDescriptionSelectOption
                    .contains('option', 'Carpet')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Home Office or Study field exists, is visible, selectable, and validates the selected value.', () => {
                BedroomsTab.elements.homeOfficeOrStudy
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Home Office or Study')
                BedroomsTab.elements.homeOfficeOrStudySelectOption
                    .select('Office', { force: true })
                BedroomsTab.elements.homeOfficeOrStudySelectOption
                    .contains('option', 'Office')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });

            });

            it('Home Office or Study Description field exists, is visible, selectable, and validates the selected value.', () => {
                BedroomsTab.elements.homeOfficeOrStudyDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Home Office or Study')
                BedroomsTab.elements.homeOfficeOrStudyDescriptionSelectOption
                    .select('Timber floors', { force: true })
                BedroomsTab.elements.homeOfficeOrStudyDescriptionSelectOption
                    .contains('option', 'Timber floors')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });

            });
            it('Add/Remove Bedroom field exists, is visible, selectable, and validates the selected value.', () => {
                BedroomsTab.elements.addBedrooms
                    .should('exist')
                    .and('be.visible')
                    .click();

                BedroomsTab.elements.removeBedrooms
                    .should('exist')
                    .and('be.visible')
                    .click();

            });
            it(' Add/Remove HomeOffice field exists, is visible and clickable', () => {
                BedroomsTab.elements.addHomeOffice
                    .should('exist')
                    .and('be.visible')
                    .click();

                BedroomsTab.elements.removeHomeOffice
                    .should('exist')
                    .and('be.visible')
                    .click();
            });
        });


        context('Validate the Kitchen Tab page', () => {
            before(() => {
                ValuationJobTab.elements.kitchenTab
                    .scrollIntoView()
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Kitchen')
                    .wait(1000)
                    .click();
            });
            it('kitchenLayout field exists, is visible, selectable, and validates the selected value.', () => {
                KitchenTab.elements.kitchenLayout
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Kitchen Layout')
                KitchenTab.elements.kitchenLayoutSelectOption
                    .select('Basic', { force: true })
                KitchenTab.elements.kitchenLayoutSelectOption
                    .contains('option', 'Basic')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Age field exists, is visible, selectable, and validates the selected value.', () => {
                KitchenTab.elements.age
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Age')
                KitchenTab.elements.ageSelectOption
                    .select('2000-09', { force: true })
                KitchenTab.elements.ageSelectOption
                    .contains('option', '2000-09')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Quality field exists, is visible, selectable, and validates the selected value.', () => {
                KitchenTab.elements.quality
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Quality')
                KitchenTab.elements.qualitySelectOption
                    .select('Good', { force: true })
                KitchenTab.elements.qualitySelectOption
                    .contains('option', 'Good')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Appliances field exists, is visible, selectable, and validates the selected value.', () => {
                KitchenTab.elements.appliances
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Appliances')
                KitchenTab.elements.appliancesSelectOption
                    .select('Dishdrawers', { force: true })
                KitchenTab.elements.appliancesSelectOption
                    .contains('option', 'Dishdrawers')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Bench And Sink field exists, is visible, selectable, and validates the selected value.', () => {
                KitchenTab.elements.benchAndSink
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bench and Sink')
                KitchenTab.elements.benchAndSinkSelectOption
                    .select('Double Sink', { force: true })
                KitchenTab.elements.benchAndSinkSelectOption
                    .contains('option', 'Double Sink')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Floor field exists, is visible, selectable, and validates the selected value.', () => {
                KitchenTab.elements.floor
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Floor')
                KitchenTab.elements.floorSelectOption
                    .select('No floor coverings', { force: true })
                KitchenTab.elements.floorSelectOption
                    .contains('option', 'No floor coverings')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });
            it('kitchenNotes field exists, is visible, selectable, and validates the selected value.', () => {
                KitchenTab.elements.kitchenNotes
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Kitchen Notes')
                KitchenTab.elements.kitchenNotesTestArea
                    .clear({ force: true })
                KitchenTab.elements.kitchenNotesTestArea
                    .type('test kitchen notes', { force: true })
                KitchenTab.elements.kitchenNotesTestArea
                    .should('have.value', 'test kitchen notes');

            });
        });

        context('Validate the Bathrooms Tab page', () => {
            before(() => {
                ValuationJobTab.elements.bathroomsTab
                    .scrollIntoView()
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bathrooms')
                    .wait(1000)
                    .click();
            });
            it('Main Bathroom field exists, is visible, selectable, and validates the selected value.', () => {
                BathroomsTab.elements.mainBathroom
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Main Bathroom')
                BathroomsTab.elements.mainBathroomSelectOption
                    .select('Family bathroom', { force: true })
                BathroomsTab.elements.mainBathroomSelectOption
                    .contains('option', 'Family bathroom')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Main Bathroom Description field exists, is visible, selectable, and validates the selected value.', () => {
                BathroomsTab.elements.mainBathroomDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Main Bathroom Description')
                BathroomsTab.elements.mainBathroomDescriptionSelectOption
                    .select('Feature vanity', { force: true })
                BathroomsTab.elements.mainBathroomDescriptionSelectOption
                    .contains('option', 'Feature vanity')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Main Bathroom Age field exists, is visible, selectable, and validates the selected value.', () => {
                BathroomsTab.elements.mainBathroomAge
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Age')
                BathroomsTab.elements.mainBathroomAgeSelectOption
                    .select('2010-19', { force: true })
                BathroomsTab.elements.mainBathroomAgeSelectOption
                    .contains('option', '2010-19')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Main Bathroom Quality field exists, is visible, selectable, and validates the selected value.', () => {
                BathroomsTab.elements.mainBathroomQuality
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Quality')
                BathroomsTab.elements.mainBathroomQualitySelectOption
                    .select('Fair', { force: true })
                BathroomsTab.elements.mainBathroomQualitySelectOption
                    .contains('option', 'Fair')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Ensuite field exists, is visible, selectable, and validates the selected value.', () => {
                BathroomsTab.elements.ensuit
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Ensuit')
                BathroomsTab.elements.ensuitSelectOption
                    .select('Guest Bathroom', { force: true })
                BathroomsTab.elements.ensuitSelectOption
                    .contains('option', 'Guest Bathroom')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Ensuite Description field exists, is visible, selectable, and validates the selected value.', () => {
                BathroomsTab.elements.ensuitDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Ensuite Description')
                BathroomsTab.elements.ensuitDescriptionSelectOption
                    .select('Heated towel rail', { force: true })
                BathroomsTab.elements.ensuitDescriptionSelectOption
                    .contains('option', 'Heated towel rail')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Ensuite Age field exists, is visible, selectable, and validates the selected value.', () => {
                BathroomsTab.elements.ensuitAge
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Age')
                BathroomsTab.elements.ensuitAgeSelectOption
                    .select('2000-09', { force: true })
                BathroomsTab.elements.ensuitAgeSelectOption
                    .contains('option', '2000-09')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });
            it('Ensuite Quality field exists, is visible, selectable, and validates the selected value.', () => {

                BathroomsTab.elements.ensuitQuality
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Quality')
                BathroomsTab.elements.ensuitQualitySelectOption
                    .select('Good', { force: true })
                BathroomsTab.elements.ensuitQualitySelectOption
                    .contains('option', 'Good')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Bathroom or Toilet field exists, is visible, selectable, and validates the selected value.', () => {

                BathroomsTab.elements.bathroomOrToilet
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bathroom or Toilet')
                BathroomsTab.elements.bathroomOrToiletSelectOption
                    .select('Family bathroom', { force: true })
                BathroomsTab.elements.bathroomOrToiletSelectOption
                    .contains('option', 'Family bathroom')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Bathroom or Toilet Description field exists, is visible, selectable, and validates the selected value.', () => {
                BathroomsTab.elements.bathroomOrToiletDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bathroom or Toile Description')
                BathroomsTab.elements.bathroomOrToiletDescriptionSelectOption
                    .select('Tiled flooring', { force: true })
                BathroomsTab.elements.bathroomOrToiletDescriptionSelectOption
                    .contains('option', 'Tiled flooring')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });
            it('Bathroom or Toilet Age field exists, is visible and clickable', () => {
                BathroomsTab.elements.addBathroom
                    .should('exist')
                    .and('be.visible')
                    .click();

                BathroomsTab.elements.removeBathroom
                    .should('exist')
                    .and('be.visible')
                    .click();
            });
            it('Bathroom or Toilet Quality field exists, is visible, editable, and validates the entered value.', () => {
                BathroomsTab.elements.bathroomNotes
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Bathroom Notes')
                BathroomsTab.elements.bathroomNotesTextArea
                    .type('test bathroom notes', { force: true }).clear({ force: true })
                BathroomsTab.elements.bathroomNotesTextArea
                    .type('test bathroom notes', { force: true })
                BathroomsTab.elements.bathroomNotesTextArea
                    .should('have.value', 'test bathroom notes');
            });

        });


        context('Validate the Garaging Tab page', () => {
            before(() => {
                ValuationJobTab.elements.garagingTab
                    .scrollIntoView()
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Garaging')
                    .wait(1000)
                    .click();
            });
            it('Garage Type field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.garageType
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Garage Type')
                GaragingTab.elements.garageTypeSelectOption
                    .select('Double freestanding garage', { force: true })
                GaragingTab.elements.garageTypeSelectOption
                    .contains('option', 'Double freestanding garage')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Garage Description field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.garageDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Garage Description')
                GaragingTab.elements.garageDescriptionSelectOption
                    .select('Door - Sectional', { force: true })
                GaragingTab.elements.garageDescriptionSelectOption
                    .contains('option', 'Door - Sectional')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Garage Age field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.garageAge
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Age')
                GaragingTab.elements.garageAgeSelectOption
                    .select('2010-19', { force: true })
                GaragingTab.elements.garageAgeSelectOption
                    .contains('option', '2010-19')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Garage Modernisation field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.garageModernisation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Modernisation')
                GaragingTab.elements.garageModernisationSelectOption
                    .select('2010-19', { force: true })
                GaragingTab.elements.garageModernisationSelectOption
                    .contains('option', '2010-19')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Garage Floor Area field exists, is visible, editable, and validates the entered value.', () => {
                GaragingTab.elements.garageFloorArea
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Floor Area')
                GaragingTab.elements.garageFloorAreaInputBox
                    .type('15', { force: true }).clear({ force: true })
                GaragingTab.elements.garageFloorAreaInputBox
                    .type('15', { force: true })
                GaragingTab.elements.garageFloorAreaInputBox
                    .should('have.value', '15');
            });

            it('Garage Exterior Cladding field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.garageExteriorCladding
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Exterior Cladding')
                GaragingTab.elements.garageExteriorCladdingSelectOption
                    .select('Coloursteel', { force: true })
                GaragingTab.elements.garageExteriorCladdingSelectOption
                    .contains('option', 'Coloursteel')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Garage Roof Construction field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.garageRoofConstruction
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Roof Construction')
                GaragingTab.elements.garageRoofConstructionSelectOption
                    .select('Corrugated iron', { force: true })
                GaragingTab.elements.garageRoofConstructionSelectOption
                    .contains('option', 'Corrugated iron')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Garage Foundation field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.garageFoundation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Foundation')
                GaragingTab.elements.garageFoundationSelectOption
                    .select('Concrete ribraft', { force: true })
                GaragingTab.elements.garageFoundationSelectOption
                    .contains('option', 'Concrete ribraft')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Garage Notes field exists, is visible, editable, and validates the entered value.', () => {
                GaragingTab.elements.garageNotes
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Garage Notes')
                GaragingTab.elements.garageNotesTextArea
                    .type('2000-09', { force: true }).clear({ force: true })
                GaragingTab.elements.garageNotesTextArea
                    .type('2000-09', { force: true })
                GaragingTab.elements.garageNotesTextArea
                    .should('have.value', '2000-09');
            });

            it('Add/Remove Garage field exists, is visible, and clickable', () => {
                GaragingTab.elements.addGarrage
                    .should('exist')
                    .and('be.visible')
                    .click();

                GaragingTab.elements.removeGarrage
                    .should('exist')
                    .and('be.visible')
                    .click();
            });
            it('Other Buildings field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.otherBuildings
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Other Buildings')
                GaragingTab.elements.otherBuildingsSelectOption
                    .select('Implement shed', { force: true });
                GaragingTab.elements.otherBuildingsSelectOption
                    .contains('option', 'Implement shed')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Other Buildings Description field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.otherBuildingsDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Other Buildings Description')
                GaragingTab.elements.otherBuildingsDescriptionSelectOption
                    .select('Bathroom', { force: true })
                GaragingTab.elements.otherBuildingsDescriptionSelectOption
                    .contains('option', 'Bathroom')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Other Buildings Age field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.otherBuildingsAge
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Age')
                GaragingTab.elements.otherBuildingsAgeSelectOption
                    .select('2010-19', { force: true })
                GaragingTab.elements.otherBuildingsAgeSelectOption
                    .contains('option', '2010-19')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Other Buildings Modernisation field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.otherBuildingsModernisation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Modernisation')
                GaragingTab.elements.otherBuildingsModernisationSelectOption
                    .select('2010-19', { force: true })
                GaragingTab.elements.otherBuildingsModernisationSelectOption
                    .contains('option', '2010-19')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Other Buildings Floor Area field exists, is visible, editable, and validates the entered value.', () => {
                GaragingTab.elements.otherBuildingsFloorArea
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Floor Area')
                GaragingTab.elements.otherBuildingsFloorAreaInputBox
                    .type('10', { force: true }).clear({ force: true })
                GaragingTab.elements.otherBuildingsFloorAreaInputBox
                    .type('10', { force: true })
                GaragingTab.elements.otherBuildingsFloorAreaInputBox
                    .should('have.value', '10');
            });

            it('Other Buildings Exterior Cladding field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.otherBuildingsExteriorCladding
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Exterior Cladding')
                GaragingTab.elements.otherBuildingsExteriorCladdingSelectOption
                    .select('Corrugated steel', { force: true })
                GaragingTab.elements.otherBuildingsExteriorCladdingSelectOption
                    .contains('option', 'Corrugated steel')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Other Buildings Roof Construction field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.otherBuildingsRoofConstruction
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Roof Construction')
                GaragingTab.elements.otherBuildingsRoofConstructionSelectOption
                    .select('Corrugated steel', { force: true })
                GaragingTab.elements.otherBuildingsRoofConstructionSelectOption
                    .contains('option', 'Corrugated steel')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Other Buildings Foundation field exists, is visible, selectable, and validates the selected value.', () => {
                GaragingTab.elements.otherBuildingsFoundation
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Foundation')
                GaragingTab.elements.otherBuildingsFoundationSelectOption
                    .select('Concrete piles', { force: true })
                GaragingTab.elements.otherBuildingsFoundationSelectOption
                    .contains('option', 'Concrete piles')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Other Buildings Notes field exists, is visible, editable, and validates the entered value.', () => {
                GaragingTab.elements.otherBuildingsNotes
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Other Buildings Notes')
                GaragingTab.elements.otherBuildingsNotesTextArea
                    .type('10', { force: true }).clear({ force: true })
                GaragingTab.elements.otherBuildingsNotesTextArea
                    .type('10', { force: true })
                GaragingTab.elements.otherBuildingsNotesTextArea
                    .should('have.value', '10');
            });
        });


        context('Validate the Improvements Tab page', () => {
            before(() => {
                ValuationJobTab.elements.improvementsTab
                    .scrollIntoView()
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Improvements')
                    .wait(1000)
                    .click();
            });
            it('Major Site Improvements field exists, is visible, selectable, and validates the selected value.', () => {
                ImprovementsTab.elements.majorSiteImprovements
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Major Site Improvements')
                ImprovementsTab.elements.majorSiteImprovementsSelectOption
                    .select('Retaining Wall', { force: true })
                ImprovementsTab.elements.majorSiteImprovementsSelectOption
                    .contains('option', 'Retaining Wall')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Major Site Improvements Description field exists, is visible, editable, and validates the entered value.', () => {
                ImprovementsTab.elements.majorSiteImprovementsDescription
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Major Site Improvements Description')
                ImprovementsTab.elements.majorSiteImprovementsDescriptionSelectOption
                    .type('test', { force: true }).clear({ force: true })
                ImprovementsTab.elements.majorSiteImprovementsDescriptionSelectOption
                    .type('test', { force: true })
                ImprovementsTab.elements.majorSiteImprovementsDescriptionSelectOption
                    .should('have.value', 'test');
            });

            it('Add/Remove Major Site Improvements field exists, is visible and clickable', () => {
                ImprovementsTab.elements.addMajorSiteImprovement
                    .should('exist')
                    .and('be.visible')
                    .click();

                ImprovementsTab.elements.removeMajorSiteImprovement
                    .should('exist')
                    .and('be.visible')
                    .click();
            });

            it('Minor Site Improvements field exists, is visible, selectable, and validates the selected value.', () => {
                ImprovementsTab.elements.minorSiteImprovements
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Minor Site Improvements')
                ImprovementsTab.elements.minorSiteImprovementsSelectOption
                    .select('Formed footpaths', { force: true })
                ImprovementsTab.elements.minorSiteImprovementsSelectOption
                    .contains('option', 'Formed footpaths')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Driveway field exists, is visible, selectable, and validates the selected value.', () => {
                ImprovementsTab.elements.driveway
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Driveway')
                ImprovementsTab.elements.drivewaySelectOption
                    .select('Formed', { force: true })
                ImprovementsTab.elements.drivewaySelectOption
                    .contains('option', 'Formed')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('Landscaping field exists, is visible, selectable, and validates the selected value.', () => {
                ImprovementsTab.elements.landscaping
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Landscaping')
                ImprovementsTab.elements.landscapingSelectOption
                    .select('Paving', { force: true })
                ImprovementsTab.elements.landscapingSelectOption
                    .contains('option', 'Paving')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });
            });

            it('fencing field exists, is visible, selectable, and validates the selected value.', () => {
                ImprovementsTab.elements.fencing
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Fencing')
                ImprovementsTab.elements.fencingSelectOption
                    .select('Hedge/Natural vegetation', { force: true })
                ImprovementsTab.elements.fencingSelectOption
                    .contains('option', 'Hedge/Natural vegetation')
                    .then(elems => {
                        expect(elems[0].selected).to.equal(true);
                    });

            });

            it('Improvements Notes field exists, is visible, editable, and validates the entered value.', () => {
                ImprovementsTab.elements.improvementsNotes
                    .should('exist')
                    .and('be.visible')
                    .and('contain', 'Improvements Notes')
                ImprovementsTab.elements.improvementsNotesTextArea
                    .type('test', { force: true }).clear({ force: true })
                ImprovementsTab.elements.improvementsNotesTextArea
                    .type('test', { force: true })
                ImprovementsTab.elements.improvementsNotesTextArea
                    .should('have.value', 'test');
            });

        });


    });
    context('Validate the comparable page', () => {
        it('Should validate the comparable page', () => {
            MobileValuationJob.elements.valuationJobComparables
                .should('exist')
                .and('be.visible')
                .and('contain', 'Comparables')
                .wait(1000)
                .click();
            Comparables.elements.compsList
                .should('exist')
                .and('be.visible')
                .and('contain', 'Comparables')


            Comparables.elements.compsMap
                .should('exist')
                .and('be.visible')
                .and('contain', 'Map')

            Comparables.elements.propertyAddress.eq(0)
                .should('exist')
                .and('be.visible')
                .click();
            Comparables.elements.comparablesListBack
                .click();
        });
    });
    context('Validate the Job Info page', () => {
        before(() => {
            MobileValuationJob.elements.valuationJobInformation
                .should('exist')
                .and('be.visible')
                .and('contain', 'Job Info')
                .wait(1000)
                .click();
        });


        it('Site Inspection Notes field exists and visible', () => {
            JobInfo.elements.siteInspectionNotes
                .should('exist')
                .and('be.visible')
                .and('contain', 'Site Inspection Notes')
        });

        it('inspection type field exists and visible', () => {
            JobInfo.elements.inspectionType
                .should('exist')
                .and('be.visible')
                .and('contain', 'Inspection Type')
                .and('contain', 'Internal')
        });

        it('Inspection Date field exists and  visible', () => {
            JobInfo.elements.inspectionDate
                .should('exist')
                .and('be.visible')
                .and('contain', 'Inspection Date')
                .and('contain', '10/10/2023')
        });
        it('Inspection Time field exists and visible', () => {
            JobInfo.elements.inspectionTime
                .should('exist')
                .and('be.visible')
                .and('contain', 'Inspection Time')
                .and('contain', '12:00')
        });
        it('valuation Report Type field exists and visible', () => {
            JobInfo.elements.valuationReportType
                .should('exist')
                .and('be.visible')
                .and('contain', 'Valuation Report Type')
                .and('contain', 'Market Valuation')
        });
        it('purpose of valuation field exists and visible', () => {
            JobInfo.elements.purposeOfValuation
                .should('exist')
                .and('be.visible')
                .and('contain', 'Purpose of Valuation')
                .and('contain', 'Market Value')
        });
        it('Valuation Date field exists and visible', () => {
            JobInfo.elements.valuationDate
                .should('exist')
                .and('be.visible')
                .and('contain', 'Valuation Date')
                .and('contain', '17/10/2023')
        });

        it('Valuation Time field exists and visible', () => {
            JobInfo.elements.valuationTime
                .should('exist')
                .and('be.visible')
                .and('contain', 'Valuation Time')
                .and('contain', '12:00')
        });

        it('Valuer field exists and visible', () => {
            JobInfo.elements.valuer
                .should('exist')
                .and('be.visible')
                .and('contain', 'Valuer')
        });

        it('Countersigned By field exists and visible', () => {
            JobInfo.elements.countersignedBy
                .should('exist')
                .and('be.visible')
                .and('contain', 'Countersigned By')
        });
        it('Client Name field exists and visible', () => {
            JobInfo.elements.clientName
                .should('exist')
                .and('be.visible')
                .and('contain', 'Client Name')
        });
        it('Borrower field exists and visible', () => {
            JobInfo.elements.borrower
                .should('exist')
                .and('be.visible')
                .and('contain', 'Borrower')
        });
        it('Lender Name field exists and visible', () => {
            JobInfo.elements.lenderName
                .should('exist')
                .and('be.visible')
                .and('contain', 'Lender Name')
        });
        it('Instructed By field exists and visible', () => {
            JobInfo.elements.instructedBy
                .should('exist')
                .and('be.visible')
                .and('contain', 'Instructed By')
        });
        it('Extended To field exists and visible', () => {
            JobInfo.elements.extendedTo
                .should('exist')
                .and('be.visible')
                .and('contain', 'Extended To')
        });

        it('Other Instructions field exists and visible', () => {
            JobInfo.elements.otherInstructions
                .should('exist')
                .and('be.visible')
                .and('contain', 'Other Instructions')
        });
    });
});

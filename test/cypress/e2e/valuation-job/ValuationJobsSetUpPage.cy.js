
import PropertyDetails from '../../model/PropertyDetails';


describe('Valuation Jobs Home Page', { defaultCommandTimeout: 15000 }, () => {
    before(() => {
        cy.visitQpid(1621837);
        cy.wait(2000);
    });
    context('Valuation Jobs Home Page Part 2', () => {
        it('should display the Valuation Jobs Home Page', () => {
            cy.wait(750);
            PropertyDetails.valuationJobsButton.should('exist').should('be.visible').click();
            cy.wait(150);

            PropertyDetails.valuationJobsList
                .contains('li', 'New Valuation Job')
                .should('exist')
                .should('be.visible')
                .click();
            cy.wait(500);
        });

        it('should display the job status and setup details', () => {
            const expectedTexts = ['Setup', 'Job Status','Report Due','Valuer','Countersigner','Cancel Job'];
            PropertyDetails.jobStatusBoard
                .should('exist')
                .and('be.visible')
                .and($element => {
                    expectedTexts.forEach(text => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });

        it('should display the Legend details', () => {
            const expectedTexts = ['Legend', 'Required for Selected Report','Included in Report if Entered','Values Not in Report'];
            PropertyDetails.iconLegend
                .should('exist')
                .and('be.visible')
                .and($element => {
                    expectedTexts.forEach(text => {
                        expect($element).to.contain(text.trim());
                    });
                });
        });


    });
});
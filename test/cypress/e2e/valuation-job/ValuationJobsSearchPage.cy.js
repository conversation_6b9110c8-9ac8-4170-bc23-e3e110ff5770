/* global cy describe before expect context it */

import Home from '../../model/Home';
import ValuationJob from '../../model/ValuationJob';
import Reports from '../../model/Reports';
import ValuationSearch from '../../model/ValuationSearch';

const valuerFirstName = 'Alex';
const valuerLastName = 'Wills';

let dateString1, dateString2;

describe('Valuation Jobs Home Page', { defaultCommandTimeout: 15000 }, () => {
    before(() => {
        Home.visit();
        cy.wait(2000);
        switchToAllTASelectedNoValuerSelected();    
    });

    context('Check for all the text boxes, dropdowns and buttons to exist.', () => {

        it('Territorial Authorities section should exist and be visible', () => {
            ValuationSearch.elements.territorialAuthorities
                .should('exist')
                .and('be.visible')
                .and('contain', 'Territorial Authorities');
        });

        it('Report Type section should exist and be visible', () => {
            ValuationSearch.elements.reportTypes
                .should('exist')
                .and('be.visible')
                .and('contain', 'Report Type');
        });

        it('Job Status section should exist and be visible', () => {
            ValuationSearch.elements.jobStatus
                .should('exist')
                .and('be.visible')
                .and('contain', 'Job Status');
        });

        it('Valuers section should exist and be visible', () => {
            ValuationSearch.elements.valuers
                .should('exist')
                .and('be.visible')
                .and('contain', 'Valuers');
        });

        it('Inspection Date section should exist and be visible', () => {
            ValuationSearch.elements.inspectionDate
                .should('exist')
                .and('be.visible')
                .and('contain', 'Inspection Date');
        });

        it('Export Button should exist and be visible', () => {
            ValuationSearch.elements.exportButton
                .should('exist',)
                .and('be.visible');
        });

        it('Clear Button should exist and be visible', () => {
            ValuationSearch.elements.clearButton
                .should('exist',)
                .and('be.visible');
        });

        it('Search Button should exist and be visible', () => {
            ValuationSearch.elements.searchButton
                .should('exist',)
                .and('be.visible');
        });

    })

    context('Validating the dropdowns and their values exist.', () => {
        context('Territorial Authorities', () => {

            it('Territorial Authorities -Validate the error message when  field for Territorial Authorities is not selected', () => {
                ValuationSearch.elements.territorialAuthorities
                    .should('be.visible')
                    .click();
                ValuationSearch.elements.territorialAuthoritiesCheckbox
                    .eq(0).uncheck({ force: true })
                    .should('not.be.checked');
                ValuationSearch.elements.territorialAuthoritiesErrorMessage
                    .should('be.visible')
                    .and('contain', 'Territorial Authority is required');
                ValuationSearch.elements.territorialAuthorities
                    .should('be.visible')
                    .click();
            });

            it('Territorial Authorities - Validating the dropdown by selecting \'Select All\' and deselecting \'Select All\' .', () => {
                ValuationSearch.elements.territorialAuthorities
                    .should('be.visible')
                    .click();
                ValuationSearch.elements.territorialAuthoritiesCheckbox
                    .eq(0).check({ force: true })
                    .should('be.checked');
                ValuationSearch.elements.territorialAuthoritiesCheckbox
                    .should('exist')
                    .and('have.length', '78');
                ValuationSearch.elements.territorialAuthoritiesMultiselectDropDown
                    .should('be.visible')
                    .and('contain', "All TA\'s Selected (77)");
                ValuationSearch.elements.territorialAuthoritiesCheckbox
                    .eq(0).uncheck({ force: true })
                    .should('not.be.checked');
                ValuationSearch.elements.territorialAuthoritiesMultiselectDropDown
                    .should('be.visible')
                    .and('contain', "Select Authorities");
                ValuationSearch.elements.territorialAuthorities
                    .should('be.visible')
                    .click();
            });
        })

        context('Report Types', () => {

            it('Report Types - Validate \'Clear All\' activity status', () => {
                ValuationSearch.elements.reportTypesClear
                    .click()
                    .should('be.visible');
            });
            it('Report Types - Validate the dropdown field for Report Types. ', () => {
                ValuationSearch.elements.reportTypesAllActive
                    .click()
                    .should('be.visible');
                ValuationSearch.elements.reportTypesMultiselect
                    .then(($el) => {
                        expect($el).to.have.length(14)
                        expect($el[0]).to.contain('Desktop Estimate')
                        expect($el[1]).to.contain('Heartland Bank')
                        expect($el[2]).to.contain('HNZ - Adhoc')
                        expect($el[3]).to.contain('HNZ Tenant To Buy')
                        expect($el[4]).to.contain('Insurance Valuation')
                        expect($el[5]).to.contain('Kerbside')
                        expect($el[6]).to.contain('Kerbside - AVO')
                        expect($el[7]).to.contain('Land Damage Valuation - EQC Act')
                        expect($el[8]).to.contain('Market Valuation')
                        expect($el[9]).to.contain('Proposed Works - New Build')
                        expect($el[10]).to.contain('Vacant Land')
                        expect($el[11]).to.contain('Verified QV')
                        expect($el[12]).to.contain('Verified QV - Bank')
                        expect($el[13]).to.contain('Land Damage Valuation - NHI Act')
                        ValuationSearch.elements.reportTypesClear.click({ force: true })
                            .should('be.visible');
                    })
            });
            it('Report Types -Selecting an options', () => {
                ValuationSearch.elements.reportTypesClear
                    .click({ force: true })
                    .should('be.visible');
                ValuationSearch.elements.reportTypesDropdown
                    .click()
                    .should('be.visible');
                cy.wait(1000); 
                cy.contains('span', 'Heartland Bank').click({force:true});   
                ValuationSearch.elements.reportTypesMultiselect
                    .then(($el) => {
                        expect($el).to.have.length(1);
                        expect($el[0]).contain('Heartland Bank');
                    })
            })
        });

        context('Job Status', () => {

            it('Job Status - Validate \'Clear All\' Job Status', () => {
                cy.wait(5000);
                ValuationSearch.elements.jobStatusClear
                    .click()
                    .should('be.visible');
            });
            it('Job Status - Validate the dropdown field for Job Status. ', () => {
                ValuationSearch.elements.jobStatusAllActive
                    .click()
                    .should('be.visible');
                ValuationSearch.elements.jobStatusMultiselect
                    .then(($el) => {
                        expect($el).to.have.length(7)
                        expect($el[0]).to.contain('Cancelled')
                        expect($el[1]).to.contain('Complete')
                        expect($el[2]).to.contain('Inspection')
                        expect($el[3]).to.contain('Peer Review')
                        expect($el[4]).to.contain('Report')
                        expect($el[5]).to.contain('Review')
                        expect($el[6]).to.contain('Setup')
                        ValuationSearch.elements.jobStatusClear.click()
                            .should('be.visible');
                    })
            });
            it('Job Status -Selecting an options', () => {
                ValuationSearch.elements.jobStatusClear
                    .click({ force: true })
                    .should('be.visible');
                ValuationSearch.elements.jobStatusDropdown
                    .click()
                    .should('be.visible');
                cy.contains('span', 'Complete').click({force:true});  
                ValuationSearch.elements.jobStatusMultiselect.then(($el) => {
                    expect($el).to.have.length(1);
                    expect($el[0]).contain('Complete');
                })

            })
        });

        context('Valuers', () => {
            it('Valuers - Validate "Clear All" for Valuers', () => {
                ValuationSearch.elements.clearValuers
                    .click()
                    .should('be.visible');
            });
        
            it('Valuers - Selecting options from Valuers', () => {
                ValuationSearch.elements.valuersDropdownBox
                    .click()
                    .should('be.visible');
                ValuationSearch.elements.selectValuersOptions1
                    .click();
                ValuationSearch.elements.valuersSelected
                    .then(($el) => {
                        expect($el).to.have.length(1);
                        expect($el[0]).to.contain('Unassigned'); // Replace with actual expected name
                    });
                ValuationSearch.elements.clearValuers
                    .click()
                    .should('be.visible');
            });
        });
    });

    context('Validating Date Range', () => {
        it('Inspection Date - Validate the date range field for Valuation Inspection Date', () => {
            ValuationSearch.elements.inspectionDateFrom
                .should('be.visible')
                .type('01/01/2024', { force: true });
            ValuationSearch.elements.inspectionDateTo
                .should('be.visible')
                .type('01/01/2023', { force: true });
            ValuationSearch.elements.clearValuers
                .click();
            ValuationSearch.elements.inspectionDateErrorMessage
                .should('be.visible')
                .and('contain', 'The To date must be equal to or greater than the From date.');
            ValuationSearch.elements.clearButton.click();
        });
    });

    context('Check page loads', () => {
        it('Populates table with Valuation Job results', () => { 
            cy.get('.resultsInner-wrapper > :nth-child(4) > :nth-child(1)').should('exist');
        });
    });

    context('Switch to TA Dashboard and Valuation Jobs', () => {
        it('Views TA', () => {
            cy.get(
                '#viewDashboardDiv > .multiselect-native-select > .btn-group > .multiselect > .caret'
            ).click();
            cy.get(':nth-child(3) > a > .radio')
                .click({ force: true })
                .then(() => {
                    cy.get('.taSummary-wrapper')
                        .should('exist')
                        .and('be.visible');
                });
        });

        // NOTE: 19/10/2021: will fail in test currently, fix in Pull Request 307 / DEV-1629
        it('Views Valuation Jobs', () => {
            cy.get('.qvToolbar-links > li > label')
                .click()
                .then(() => {
                    cy.get('.resultsTitle').should('have.text', 'Valuation Jobs');
                });
        });
    });

    context('Check columns', () => {
        ValuationJob.checkColumns();
    });

    // context.only('Sorting Columns', () => {
    //     // NOTE: 08/05/2024: will fail in test currently, as the sort is [date, status]
    //     it('Sorts columns by Inspection Time', () => {
    //         cy.get('.sortRow > .searchDetails-wrapper > .inspectiontime').click();
    //         cy.wait(2000);

    //         cy.get(':nth-child(4) > :nth-child(1) > .searchDetails-wrapper > .inspectiontime')
    //             .invoke('text')
    //             .then(text1 => {
    //                 if (text1 !== null && text1 !== '') {
    //                     dateString1 = text1;

    //                     cy.get(':nth-child(2) > .searchDetails-wrapper > .inspectiontime')
    //                         .invoke('text')
    //                         .then(text2 => {
    //                             if (text2 !== null && text2 !== '') {
    //                                 dateString2 = text2;

    //                                 const date1 = convertStringToDate(dateString1);
    //                                 const date2 = convertStringToDate(dateString2);

    //                                 expect(date1).to.be.lessThan(date2);
    //                             }
    //                         });
    //                 }
    //             });
    //     });

    //     it('Sorts columns by Job Due', () => {
    //         cy.get('.jobdue > a').click();
    //         cy.wait(2000);
    //         cy.get(':nth-child(4) > :nth-child(1) > .searchDetails-wrapper > .jobdue')
    //             .invoke('text')
    //             .then(text1 => {
    //                 if (text1 !== null && text1 !== '') {
    //                     dateString1 = text1;

    //                     cy.get(':nth-child(2) > .searchDetails-wrapper > .jobdue')
    //                         .invoke('text')
    //                         .then(text2 => {
    //                             if (text2 !== null && text2 !== '') {
    //                                 dateString2 = text2;

    //                                 const date1 = convertStringToDate(dateString1);
    //                                 const date2 = convertStringToDate(dateString2);

    //                                 expect(date1).to.be.lessThan(date2);
    //                             }
    //                         });
    //                 }
    //             });
    //     });
        context('Sorts columns by Job Status', () => {
            it('Ascending', () => {
                cy.get('.jobstatus > a').click();
                cy.get('.jobstatus > a > .icon > .sorter').should('have.class', 'down');
            });

            it('Descending', () => {
                cy.get('.jobstatus > a').click();
                cy.get('.jobstatus > a > .icon > .sorter').should('have.class', 'up');
            });
        });
    });

    context('Filter based on valuer', () => {

        context('Searches for valuer(s)', () => {

            it('None valuers selected by default is all selected', () => {
                cy.get('.resultsFound > p')
                    .wait(2000)
                    .invoke('text')
                    .then(text => {
                        const number = parseInt(text.split(' ')[0]);
                        console.log("number",number)
                        cy.wrap(number).should('be.gt', 0);
                    });
            });

            it('Filters to multiple valuers', () => {
                ValuationSearch.elements.valuersDropdownBox
                    .click()
                    .should('be.visible');
                ValuationSearch.elements.selectValuersOptions1
                    .click({force:true})
                    .should('be.visible');
                ValuationSearch.elements.selectValuersOptions2
                    .click({force:true});
                ValuationSearch.elements.selectValuersOptions3
                    .click({force:true});
                ValuationSearch.elements.jobStatus
                    .click();    
                ValuationSearch.elements.valuersSelected
                    .should('contain', '3 options selected');
                ValuationSearch.elements.clearValuers
                    .click()
                    .should('be.visible');
            });
        });

        context('Searches for a valuer', () => {
            it('Searches for valuer', () => {
                ValuationSearch.elements.valuersDropdownBox
                    .find('input')
                    .click({ force: true })
                    .should('be.visible')
                    .type('Alan', { force: true })
                    .then(() => {
                        // Check that "Alan Chadderton" is within a specific `<li>` with a role of "option"
                        cy.contains('li[role="option"]', 'Alan Chadderton').should('exist').and('be.visible');

                        // Check that "Alan Dryburgh" is within a specific `<li>` with a role of "option"
                        cy.contains('li[role="option"]', 'Alan Dryburgh').should('exist').and('be.visible');

                    });

            });
        });
    });

    context('View Property', () => {
        it('Click property in valuation jobs list to view valuation job', () => {
            cy.get(':nth-child(1) > .address > .fullAddress').click({
                force: true
            });
            cy.wait(1000);

            ValuationJob.setupHeader.should('exist');
        });
    });



describe('Home Valuation Export Results ', () => {
    before(() => {
        Home.visit();
        cy.wait(2000);
    });

    context('Export Results - Report ', () => {
        it('should successfully view the Home Valuation Report', () => {
            ValuationSearch.elements.clearValuers
                .click({ force: true });
            ValuationSearch.elements.selectValuersOptions1
                .click({ force: true }); 
            ValuationSearch.elements.selectValuersOptions2
                .click({ force: true }); 
            ValuationSearch.elements.selectValuersOptions3
                .click({ force: true }); 
            ValuationSearch.elements.jobStatus
                .click({ force: true });    
            ValuationSearch.elements.valuersSelected
                .should('contain', '3 options selected'); 
            ValuationSearch.elements.searchButton
                .click({ force: true });       
            cy.wait(4000);    
            ValuationSearch.elements.exportButton
                .click({ force: true });
            cy.wait(8000);               
            ValuationJob.alertModal
                .and('be.visible')
                .and('contain', 'Export Scheduled')
                .and('contain', 'Your export has been acknowledged and can be viewed in View My Reports.');
            ValuationJob.alertModalViewMyReport
                .click({ force: true });
            cy.wait(10000);
            Reports.elements.myReportsRefreshButton
                .click({ force: true });
            cy.wait(500);
            Reports.elements.jobFirstColumns
                .should('be.visible')
                .and('contain', 'Home Valuation Export');

            Reports.elements.jobFirstColumnsFileSize
                .invoke('text')
                .then(text => {
                    const fileSize = parseFloat(text);
                    expect(fileSize).to.be.gt(0);
                });    
        });
    });
});

function convertStringToDate(str) {
    // Split the date and time parts
    const dateTime = str.split(" @ ");

    // Reformat the date part
    const dateParts = dateTime[0].split("/");
    const reformattedDate = dateParts[1] + "/" + dateParts[0] + "/" + dateParts[2];

    // Combine the reformatted date and time parts
    const combinedDateTime = reformattedDate + " " + dateTime[1];

    // Create a new Date object
    const dateObject = new Date(combinedDateTime);

    return dateObject;
}

function switchToAllTASelectedNoValuerSelected(){
    ValuationSearch.elements.territorialAuthorities
    .should('be.visible')
    .click();
ValuationSearch.elements.territorialAuthoritiesCheckbox
    .eq(0).check({ force: true })
    .should('be.checked');
ValuationSearch.elements.territorialAuthorities
    .should('be.visible')
    .click();
ValuationSearch.elements.clearValuers
    .should('be.visible')
    .click();      
ValuationSearch.elements.searchButton
    .should('be.visible')
    .click({force:true});     
}
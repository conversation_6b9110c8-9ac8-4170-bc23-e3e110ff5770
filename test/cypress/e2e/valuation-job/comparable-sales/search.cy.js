import ComparableSales from '../../../model/ComparableSales';

describe('Home Valuation Comparable Sales', { defaultCommandTimeout: 15000 }, () => {
    before(() => {
        ComparableSales.goToComparableProperties(3002038); // assuming there is a valuation job with setup complete on this qpid
        cy.wait(5000);
        ComparableSales.clearDateRange();
    });
    context('Sales search default parameters', () => {
        it('category is R*', () => {
            ComparableSales.elements.comparablePropertiesCategories.should('include.value', 'R*');
        });
        it('date range is Now - 3 months ago', () => {
            ComparableSales.elements.comparablePropertiesDateRange.click();
            cy.wait(300);
            // can have hidden date range pickers on the page
            cy.get('.daterangepicker').each(($el) => {
                if (Cypress.dom.isVisible($el)) {
                    cy.wrap($el).find('[data-range-key="Last 3 Months"]').should('have.class', 'active');
                }
            });
        });
        it('distance is 0.5km', () => {
            ComparableSales.elements.comparablePropertiesDistanceInput.should('have.value', 0.5);
        });
        it('gross sale price range is not defined', () => {
            ComparableSales.elements.comparablePropertiesGrossPriceFromInput.should('be.empty');
            ComparableSales.elements.comparablePropertiesGrossPriceToInput.should('be.empty');
        });
        it('Zone is not defined', () => {
            ComparableSales.elements.comparablePropertiesZones.should('be.empty');
        });
    });
    context('Searching for sales', () => {
        it('setting non-restrictive values for each parameter should return some sales', () => {
            ComparableSales.elements.comparablePropertiesCategories.clear().type('R*');
            ComparableSales.elements.comparablePropertiesDateRange.click();
            cy.wait(300);
            // can have hidden date range pickers on the page
            cy.get('.daterangepicker').each(($el) => {
                if (Cypress.dom.isVisible($el)) {
                    cy.wrap($el).find('[data-range-key="Last 12 Months"]').click();
                }
            });
            ComparableSales.elements.comparablePropertiesDistanceInput.clear().type('1.5');
            ComparableSales.elements.comparablePropertiesGrossPriceFromInput.clear().type('100000');
            ComparableSales.elements.comparablePropertiesGrossPriceToInput.clear().type('2000000');
            ComparableSales.elements.comparablePropertiesZones.clear().type('9*').blur();
            cy.wait(5000);
            ComparableSales.elements.comparableRows.should('have.length.greaterThan', 0);
        });
    });
    context('Sale search result expand and collapse', () => {
        it('expects items to be initially hidden', () => {
            cy.get(ComparableSales.elements.comparablePropertyWalls).should('not.be.visible');
            ComparableSales.elements.comparableRows.each(($el) => {
                cy.wrap($el).find(ComparableSales.elements.rowClose).should('not.be.visible');
            });
        });
        it('expands some rows', () => {
            ComparableSales.elements.comparableRows.each(($el, idx, $list) => {
                if (idx % 2 === 0) {
                    cy.wrap($el).click();
                    cy.wrap($el).find(ComparableSales.elements.rowClose).should('be.visible');
                    cy.wrap($el).find(ComparableSales.elements.comparablePropertyWalls).should('be.visible');
                }
            });
        });
        it('collapses some rows', () => {
            ComparableSales.elements.comparableRows.each(($el, idx, $list) => {
                if (idx % 2 === 0) {
                    cy.wrap($el).find(ComparableSales.elements.rowClose).click();
                    cy.wrap($el).find(ComparableSales.elements.rowClose).should('not.be.visible');
                    cy.wrap($el).find(ComparableSales.elements.comparablePropertyWalls).should('not.be.visible');
                }
            });
        });
    });
    context('Clicking on table headers to sort results', () => {
        context('sort by gross price', () => {
            it('sorts ascending', () => {
                cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
                cy.get(ComparableSales.elements.sortTableHeader('GROSS_PRICE')).click();
                cy.wait('@displayComparableProperties');
                ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

                let pricePrior = 0;
                ComparableSales.elements.comparableRows.each(($el) => {
                    const priceCurrent = parseFloat(
                        $el
                            .find(ComparableSales.elements.comparablePropertyGrossPrice)
                            .text()
                            .replace(/[^0-9.-]+/g, '')
                    );
                    expect(priceCurrent).to.be.at.least(pricePrior);
                    pricePrior = priceCurrent;
                });
            });

            it('sorts descending', () => {
                cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
                cy.get(ComparableSales.elements.sortTableHeader('GROSS_PRICE')).click();
                cy.wait('@displayComparableProperties');
                ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

                let pricePrior = Number.MAX_VALUE;
                ComparableSales.elements.comparableRows.each(($el) => {
                    const priceCurrent = parseFloat(
                        $el
                            .find(ComparableSales.elements.comparablePropertyGrossPrice)
                            .text()
                            .replace(/[^0-9.-]+/g, '')
                    );
                    expect(priceCurrent).to.be.at.most(pricePrior);
                    pricePrior = priceCurrent;
                });
            });
        });

        context('sort by sale date', () => {
            it('sorts ascending', () => {
                cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
                cy.get(ComparableSales.elements.sortTableHeader('SALE_DATE')).click();
                cy.wait('@displayComparableProperties');
                ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

                let datePrior = 0;
                ComparableSales.elements.comparableRows.each(($el) => {
                    const dateCurrent = Date.parse($el.find(ComparableSales.elements.comparablePropertySaleDate).text().split('/').reverse().join('-'));
                    expect(dateCurrent).to.be.at.least(datePrior);
                    datePrior = dateCurrent;
                });
            });

            it('sorts descending', () => {
                cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
                cy.get(ComparableSales.elements.sortTableHeader('SALE_DATE')).click();
                cy.wait('@displayComparableProperties');
                ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

                let datePrior = Number.MAX_SAFE_INTEGER;
                ComparableSales.elements.comparableRows.each(($el) => {
                    const dateCurrent = Date.parse($el.find(ComparableSales.elements.comparablePropertySaleDate).text().split('/').reverse().join('-'));
                    expect(dateCurrent).to.be.at.most(datePrior);
                    datePrior = dateCurrent;
                });
            });
        });

        context('sort by land area', () => {
            it('sorts ascending', () => {
                cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
                cy.get(ComparableSales.elements.sortTableHeader('LAND_AREA')).click();
                cy.wait('@displayComparableProperties');
                ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

                let landAreaPrior = 0;
                ComparableSales.elements.comparableRows.each(($el) => {
                    const landAreaCurrent = parseFloat(
                        $el
                            .find(ComparableSales.elements.comparablePropertyLandArea)
                            .text()
                            .replace(/[^0-9.-]+/g, '')
                    );
                    expect(landAreaCurrent).to.be.at.least(landAreaPrior);
                    landAreaPrior = landAreaCurrent;
                });
            });

            it('sorts descending', () => {
                cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
                cy.get(ComparableSales.elements.sortTableHeader('LAND_AREA')).click();
                cy.wait('@displayComparableProperties');
                ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

                let landAreaPrior = Number.MAX_VALUE;
                ComparableSales.elements.comparableRows.each(($el) => {
                    const landAreaCurrent = parseFloat(
                        $el
                            .find(ComparableSales.elements.comparablePropertyLandArea)
                            .text()
                            .replace(/[^0-9.-]+/g, '')
                    );
                    expect(landAreaCurrent).to.be.at.most(landAreaPrior);
                    landAreaPrior = landAreaCurrent;
                });
            });
        });
    });
});

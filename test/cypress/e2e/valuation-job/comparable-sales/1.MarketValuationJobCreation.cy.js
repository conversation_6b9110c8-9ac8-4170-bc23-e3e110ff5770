import dateFormat from 'dateformat';
import PropertyDetails from '../../../model/PropertyDetails';
import ValuationJob from '../../../model/ValuationJob';

const clientName = 'client name';
const inspectorName = 'inspector name';

const valuerFirstName = 'Alex';
const valuerLastName = 'Wills';

describe('Market Valuation Job Creation', { defaultCommandTimeout: 15000 }, () => {
    before(() => {
        cy.login(); // assuming receptionist typist role so the valuation job isnt read only
        cy.visit(`/property/${1621837}/summary`);
        // cy.visitQpid(3118024); // * Lifestyle:
        // cy.visitQpid(2460476);
        cy.wait(8000);
    });

    // NOTE: alert pop up check
    beforeEach(() => {
        ValuationJob.elements.valuationJobErrorAlert.then($alert => {
            if ($alert.is(':visible')) {
                cy.wait(100);
                ValuationJob.elements.valuationJobErrorCancelButton
                    .click()
                    .then(() => {
                        cy.doubleLog('###\n ERROR BUTTON CLICKED \n ###');
                        cy.wait(2000);
                        ValuationJob.elements.valuationJobErrorAlert.should('not.be.visible');
                    });
            }
        });
    });

    context('Valuation Jobs Button', () => {
        it('New valuation job option available', () => {
            cy.wait(750);
            PropertyDetails.valuationJobsButton.should('exist').click();
            cy.wait(150);

            PropertyDetails.valuationJobsList
                .contains('li', 'New Valuation Job')
                .should('exist')
                .click();
            cy.wait(500);
        });
        it('Setup job screen should be displayed', () => {
            cy.wait(5000);
            ValuationJob.setupHeader.should('exist');
            ValuationJob.jobSetupStep.should('have.class', 'active');
        });
    });

    // * Market Valuation
    context('Set-up Valuation Job', () => {

        // * Set report type and create report
        context('Creates new Market Valuation report', () => {
            it('Selects Market Valuation Report Type', () => {
                ValuationJob.selectMarketValuationReport();
            });

            it('Selects date', () => {
                ValuationJob.selectTodaysDateInspectionDate();
                const todaysDate = dateFormat(new Date(), 'dd/mm/yyyy').toString() + ' 12:00 AM';
                cy.doubleLog(todaysDate);
                ValuationJob.inspectionDateAndTimeText.invoke('val').then(text => {
                    expect(text).to.equal(todaysDate);
                });
            });

            it("Enters client's name", () => {
                ValuationJob.clientNameTextField.type(clientName);
                ValuationJob.clientNameTextField.invoke('val').then(text => {
                    expect(text).to.equal(clientName);
                });
            });

            it("Enters inspector's name", () => {
                ValuationJob.instructedByTextField.type(inspectorName);
                ValuationJob.instructedByTextField.invoke('val').then(text => {
                    expect(text).to.equal(inspectorName);
                });
            });

            it('Selects valuer', () => {
                ValuationJob.selectValuer(valuerFirstName, valuerLastName);
                ValuationJob.elements.selectValuerChosen.should('have.text', `${valuerFirstName} ${valuerLastName}`);
            });

            it('Selects purpose of valuation', () => {
                ValuationJob.selectPurposeOfValuation();
                ValuationJob.elements.purposeOfValuationChosen.should('have.text', 'Market Value');
            });

            it('Selects peer review', () => {
                ValuationJob.selectPeerReview();
                ValuationJob.elements.peerReviewChosen.should('have.text', 'Peer Review Not Required');
            });

            it('Completes setup, changes to Property Details step', () => {
                ValuationJob.completeSetup.click();
            });
        });
    });

    context('Property Details', () => {
        context('Checks page', () => {
            it('Should be on Property Details page', () => {
                ValuationJob.stepperItem('propertyDetails').should('have.class', 'active');
            });
        });

        context('Edits data', () => {
            it('Changes house type', () => {
                ValuationJob.elements.changeHouseTypeInput.click();
                ValuationJob.changeHouseType();
                ValuationJob.houseTypeMultiselect.should('have.text', 'Cottage ');
            });
        });

        context('Leaves Step', () => {
            it('Goes to next step', () => {
                // ! finishes Property details section, goes to next page
                ValuationJob.nextStep('propertyDetails').click();
            });
        });
    });

    context('Comparable Properties', () => {
        context('Check correct step', () => {
            it('Should be on Comparable Properties page', () => {
                ValuationJob.stepperItem('comparableProperties').should('have.class', 'active');
            });
        });
    });
});

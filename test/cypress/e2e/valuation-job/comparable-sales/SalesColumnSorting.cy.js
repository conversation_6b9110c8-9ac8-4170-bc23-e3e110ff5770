import ComparableSales from "../../../model/ComparableSales";

describe('Comparable sales column sorting', { defaultCommandTimeout: 15000 }, () => {
  before(() => {
    ComparableSales.goToComparableProperties(1621837);
    ComparableSales.clearCategories();
    ComparableSales.clearDateRange();
  });

  context('sort by gross price', () => {
    it('sorts ascending', () => {
      cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
      cy.get(ComparableSales.elements.sortTableHeader('GROSS_PRICE')).click();
      cy.wait('@displayComparableProperties');
      ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

      let pricePrior = 0;
      ComparableSales.elements.comparableRows.each($el => {
        const priceCurrent = parseFloat($el.find(ComparableSales.elements.comparablePropertyGrossPrice).text().replace(/[^0-9.-]+/g, ''));
        expect(priceCurrent).to.be.at.least(pricePrior);
        pricePrior = priceCurrent;
      });
    });

    it('sorts descending', () => {
      cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
      cy.get(ComparableSales.elements.sortTableHeader('GROSS_PRICE')).click();
      cy.wait('@displayComparableProperties');
      ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

      let pricePrior = Number.MAX_VALUE;
      ComparableSales.elements.comparableRows.each($el => {
        const priceCurrent = parseFloat($el.find(ComparableSales.elements.comparablePropertyGrossPrice).text().replace(/[^0-9.-]+/g, ''));
        expect(priceCurrent).to.be.at.most(pricePrior);
        pricePrior = priceCurrent;
      });
    });
  });

  context('sort by sale date', () => {
    it('sorts ascending', () => {
      cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
      cy.get(ComparableSales.elements.sortTableHeader('SALE_DATE')).click();
      cy.wait('@displayComparableProperties');
      ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

      let datePrior = 0;
      ComparableSales.elements.comparableRows.each($el => {
        const dateCurrent = Date.parse($el.find(ComparableSales.elements.comparablePropertySaleDate).text().split('/').reverse().join('-'));
        expect(dateCurrent).to.be.at.least(datePrior);
        datePrior = dateCurrent;
      });
    });

    it('sorts descending', () => {
      cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
      cy.get(ComparableSales.elements.sortTableHeader('SALE_DATE')).click();
      cy.wait('@displayComparableProperties');
      ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

      let datePrior = Number.MAX_SAFE_INTEGER;
      ComparableSales.elements.comparableRows.each($el => {
        const dateCurrent = Date.parse($el.find(ComparableSales.elements.comparablePropertySaleDate).text().split('/').reverse().join('-'));
        expect(dateCurrent).to.be.at.most(datePrior);
        datePrior = dateCurrent;
      });
    });
  });

  context('sort by land area', () => {
    it('sorts ascending', () => {
      cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
      cy.get(ComparableSales.elements.sortTableHeader('LAND_AREA')).click();
      cy.wait('@displayComparableProperties');
      ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

      let landAreaPrior = 0;
      ComparableSales.elements.comparableRows.each($el => {
        const landAreaCurrent = parseFloat($el.find(ComparableSales.elements.comparablePropertyLandArea).text().replace(/[^0-9.-]+/g, ''));
        expect(landAreaCurrent).to.be.at.least(landAreaPrior);
        landAreaPrior = landAreaCurrent;
      });
    });

    it('sorts descending', () => {
      cy.intercept('POST', '/displayComparableProperties').as('displayComparableProperties');
      cy.get(ComparableSales.elements.sortTableHeader('LAND_AREA')).click();
      cy.wait('@displayComparableProperties');
      ComparableSales.elements.comparableRows.should('have.length.greaterThan', 1);

      let landAreaPrior = Number.MAX_VALUE;
      ComparableSales.elements.comparableRows.each($el => {
        const landAreaCurrent = parseFloat($el.find(ComparableSales.elements.comparablePropertyLandArea).text().replace(/[^0-9.-]+/g, ''));
        expect(landAreaCurrent).to.be.at.most(landAreaPrior);
        landAreaPrior = landAreaCurrent;
      });
    });
  });
});

/* global cy describe before beforeEach context it */

import dateFormat from 'dateformat';

import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';
import ValuationJob from '../../model/ValuationJob';

import 'cypress-file-upload';

const clientName = 'client name';
const inspectorName = 'inspector name';

const valuerFirstName = 'Alex';
const valuerLastName = 'Wills';

const valref = '33423 / 43400';
const saleId = 6447568;

const locationComment = 'Location Comment';

// TODO: load this in for testing adding non-R/L style properties
const DFBSaleId = 4385999;

let jobId;
let counter = 0;

// REFACTOR: move all the alter data functions to ValuationJob.js too

describe('Market Valuation Job Creation', { defaultCommandTimeout: 15000 }, () => {
    before(() => {
        cy.visitQpid(1621837); // * Original
        // cy.visitQpid(3118024); // * Lifestyle:
        // cy.visitQpid(2460476);
        cy.wait(8000);

        // Home.quickSearchBar.clear().type(`${valref}{enter}`);
        // cy.wait(5000);
    });

    // NOTE: alert pop up check

    beforeEach(() => {
        cy.get('.valuationJobError > .alert').then($alert => {
            if ($alert.is(':visible')) {
                cy.wait(100);
                cy.get('.valuationJobError > .alert > .alertButtons > #errorCancel')
                    .click()
                    .then(() => {
                        cy.doubleLog('###\n ERROR BUTTON CLICKED \n ###');
                        cy.wait(2000);

                        cy.get('.valuationJobError > .alert').should('not.be.visible');
                    });
            }
        });
    });

    context('Valuation Jobs Button', () => {
        it('New valuation job option available', () => {
            cy.wait(750);
            PropertyDetails.valuationJobsButton.should('exist').click();
            cy.wait(150);

            PropertyDetails.valuationJobsList
                .contains('li', 'New Valuation Job')
                .should('exist')
                .click();
            cy.wait(500);
        });
        it('Setup job screen should be displayed', () => {
            cy.wait(5000);
            ValuationJob.setupHeader.should('exist');
            ValuationJob.jobSetupStep.should('have.class', 'active');
        });
    });
    context('Job Setup Page - Expand All', () => {
        it('Should validate the expand all functionality on the Job Setup page', () => {
            PropertyDetails.jobSetUpExpandAll
                .should('exist').click();
            PropertyDetails.jobSetUpExpandAll
                .should('have.class', 'expandAll down');
            PropertyDetails.jobSetUpExpandAll
                .should('exist').click();
            PropertyDetails.jobSetUpExpandAll
                .should('have.class', 'expandAll');
        });
    });

    // * Market Valuation
    context('Set-up Valuation Job', () => {
        // DEBUG:
        context('Check details container', () => {
            // * Check every element exists on set up page

            context('Checks Report Details Exists', () => {
                ValuationJob.checkReportDetailsExists();
            });

            // * Check every element exists on extra details page

            context('Checks Extra Details Exists', () => {
                ValuationJob.checkExtraDetailsExists();
            });

        });

        // * Set report type and create report

        context('Creates new Market Valuation report', () => {
            it('Selects Market Valuation Report Type', () => {
                ValuationJob.selectMarketValuationReport();
            });

            it('Selects date', () => {
                ValuationJob.selectTodaysDateInspectionDate();
                const todaysDate = dateFormat(new Date(), 'dd/mm/yyyy').toString() + ' 12:00 AM';
                cy.doubleLog(todaysDate);
                ValuationJob.inspectionDateAndTimeText.invoke('val').then(text => {
                    expect(text).to.equal(todaysDate);
                });
            });

            it("Enters client's name", () => {
                ValuationJob.clientNameTextField.type(clientName);
                ValuationJob.clientNameTextField.invoke('val').then(text => {
                    expect(text).to.equal(clientName);
                });
            });

            it("Enters inspector's name", () => {
                ValuationJob.instructedByTextField.type(inspectorName);
                ValuationJob.instructedByTextField.invoke('val').then(text => {
                    expect(text).to.equal(inspectorName);
                });
            });

            it('Selects valuer', () => {
                ValuationJob.selectValuer(valuerFirstName, valuerLastName);
                cy.get(
                    '.required > .multiselect-native-select > .btn-group > .multiselect > .multiselect-selected-text'
                ).should('have.text', `${valuerFirstName} ${valuerLastName}`);
            });

            it('Selects purpose of valuation', () => {
                ValuationJob.selectPurposeOfValuation();
                cy.get(
                    '#jobSetupReportDetailsPurposeOfValuation > .btn-group > .multiselect > .multiselect-selected-text'
                ).should('have.text', 'Market Value');
            });

            it('Selects peer review', () => {
                ValuationJob.selectPeerReview();
                cy.get(
                    '#jobSetupReportDetailsPeerReview > .btn-group > .multiselect > .multiselect-selected-text'
                ).should('have.text', 'Peer Review Not Required');
            });
        });

        context('Goes to next step', () => {
            it('Completes setup, changes to Property Details step', () => {
                const saveHomeValuationURL = env.config.baseUrl + 'saveHomeValuation';
                cy.intercept('POST', saveHomeValuationURL).as('saveHomeValuation');

                ValuationJob.completeSetup.click();

                cy.wait('@saveHomeValuation').then(res => {
                    jobId = res.response.body.id;
                });
            });
        });
    });

    context('Property Details', () => {
        context('Checks page', () => {
            it('Should be on Property Details page', () => {
                ValuationJob.stepperItem('propertyDetails').should('have.class', 'active');
            });
        });
        context('Property Details Page - Expand All', () => {
            it('Should validate the expand all functionality on the Property Details page', () => {
                PropertyDetails.propertyDetailsExpandAll
                    .should('exist').click();
                PropertyDetails.propertyDetailsExpandAll
                    .should('have.class', 'expandAll down');
                PropertyDetails.propertyDetailsExpandAll
                    .should('exist').click();
                PropertyDetails.propertyDetailsExpandAll
                    .should('have.class', 'expandAll');
            });
        });


        // DEBUG:
        context('Checks for elements existing', () => {
            context('Checks Overview tab', () => {
                ValuationJob.checkPropertyDetailsOverview();
            });

            context('Checks Interior tab', () => {
                ValuationJob.checkPropertyDetailsInterior();
            });

            context('Checks Bedroom tab', () => {
                ValuationJob.checkPropertyDetailsBedrooms();
            });

            context('Checks Kitchen tab', () => {
                ValuationJob.checkPropertyDetailsKitchen();
            });

            context('Checks Bathrooms tab', () => {
                ValuationJob.checkPropertyDetailsBathrooms();
            });

            context('Checks Garages tab', () => {
                ValuationJob.checkPropertyDetailsGarages();
            });

            context('Checks Improvements tab', () => {
                ValuationJob.checkPropertyDetailsImprovements();
            });
        });

        context('Edits data', () => {
            it('Changes house type', () => {
                cy.get('#tabsElement > .QVHVTab-1 > span').click();
                ValuationJob.changeHouseType();
                ValuationJob.houseTypeMultiselect.should('have.text', 'Cottage ');
            });
        });

        context('Leaves Step', () => {
            it('Goes to next step', () => {
                // ! finishes Property details section, goes to next page
                ValuationJob.nextStep('propertyDetails').click();
            });
        });
    });

    context('Comparable Properties', () => {
        context('Check correct step', () => {
            it('Should be on Comparable Properties page', () => {
                ValuationJob.stepperItem('comparableProperties').should('have.class', 'active');
            });
        });

        // FIXME:
        context('Do comparable sales container', () => {
            it('Expects comparable properties', () => {
                ValuationJob.comparablePropertiesList
                    // .children()
                    .should('have.length.above', 1);
                // cy.get('.selectComps-confirm > .material-icons').click()
                // cy.get(':nth-child(2) > .compSelect').click()
            });

            it('Default distance equals 0.5km', () => {
                cy.get('#propertyFrom').should('have.value', 0.5);
            });

            it('Expands property in results row', () => {
                // NOTE: won't display property comparable properties on default unless this is done
                cy.wait(2000);
                ValuationJob.comparablePropertyNext.click({ force: true });
                cy.get('h2 > a').click({ force: true });
                cy.wait(2000);

                cy.get(':nth-child(2) > .resultsRow')
                    .should('exist')
                    .and('be.visible')
                    .click()
                    .then(() => {
                        cy.get(':nth-child(2) > .resultsRow').should('have.class', 'openProp');
                    });
            });

            it('Enters comparable property manually', () => {
                ValuationJob.comparablePropertyNext.click({ force: true });
                // ValuationJob.comparablePropertyNext.click({ force: true });
                cy.wait(1000);
                // ValuationJob.firstPropertyCheckbox.click({ force: true });
                // cy.wait(2000);
                ValuationJob.enterSaleID(saleId);
                // ValuationJob.enterSaleID(saleId2);
                cy.get(
                    ':nth-child(3) > .md-propertyOverview > .md-propertyOverview-inner > .md-totals > :nth-child(1)'
                ).should('have.text', '1/6600790000');
            });

            it('Expands Comparable Properties list in correct order', () => {
                ValuationJob.expandComparableSalesButton.click({ force: true });
                cy.get(':nth-child(3) > .compControls > .compBubbles').should('not.be.visible');
            });

            it('Labels properties with NSP more than 10% below market estimate as Inferior', () => {
                cy.get(':nth-child(3) > .comparableStatement > span > select').should(
                    'have.value',
                    'I'
                );
            });

            it("Edits comparable sale's paragraph", () => {
                cy.get(':nth-child(3) > .comparableStatement > .edit_area').type(
                    '{end} Test sentence.'
                );
            });

            context('Exports comparable sales', () => {
                let comparableSalesURL;
                before(() => {
                    comparableSalesURL = env.config.baseUrl + `exportComparableSale/${jobId}?_=**`;
                });
                it('Exports comparable sales', () => {
                    cy.doubleLog(comparableSalesURL);
                    cy.intercept('GET', comparableSalesURL).as('exportComparableSale');
                    ValuationJob.exportComparableSalesButton.click();
                    cy.wait('@exportComparableSale').then(res => {
                        cy.doubleLog(res);
                        expect(res.response.body).to.not.be.empty;
                    });
                });
            });

            // TODO: do something better with these elements
            it('Click Sale link on comparable sale to view QIVS Sale screen', () => {
                ValuationJob.qivsSaleButton.should('exist');
            });

            it('Click Map link on comparable sale to view QVMS map for sale', () => {
                ValuationJob.mapButton.should('exist');
            });

            it('Click Analysis link on comparable sale to view Monarch sales analysis', () => {
                ValuationJob.salesAnalysisButton.should('exist');
            });

            // TODO: change name of cy.get -> get rid of uuid in classname
            it('Click Upload Photos link on comparable sale and add photo', () => {
                cy.get(
                    '.photo-uploader383a3e16-4d44-47d5-a50b-d26c605ba4f3 > .material-icons'
                ).should('exist');
            });
        });

        context('Next step', () => {
            it('Goes to next step', () => {
                ValuationJob.nextStep('comparableProperties').click();
            });
        });
    });

    context('Valuation', () => {
        context('Checks page', () => {
            it('Should be on Valuation page', () => {
                ValuationJob.stepperItem('valuation').should('have.class', 'active');
            });
        });

        context('Valuation Page - Expand All', () => {
            it('Should validate the expand all functionality on the Valuation page', () => {
                PropertyDetails.valuationExpandAll
                    .should('exist').click();
                PropertyDetails.valuationExpandAll
                    .should('have.class', 'expandAll down');
                PropertyDetails.valuationExpandAll
                    .should('exist').click();
                PropertyDetails.valuationExpandAll
                    .should('have.class', 'expandAll');
            });
        });

        context('Checks for elements existing', () => {
            context('Checks Current Valuation', () => {
                ValuationJob.checkCurrentValuation();
            });

            context('Checks ProposedValuation', () => {
                ValuationJob.checkProposedValuation();
            });

            context('Checks Income Method', () => {
                ValuationJob.checkIncomeMethod();
            });
        });

        context('Alter data', () => {
            context('Clicks to first tab container', () => {
                it('Clicks to first tab', () => {
                    cy.get('#tabsElementValuationWorksheet > .QVHVTab-1 > span').click();

                    cy.get('#tabsElementValuationWorksheet > li.QVHVTab-1 > .is-active').should(
                        'be.visible'
                    );
                });
            });

            context('Enter valuation workings and report data', () => {
                it('Alter Land value', () => {
                    cy.get('.valuation > .md-table').click();

                    cy.get(
                        '.currentValuation > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-value > span > input'
                    )
                        .clear()
                        .type('1234', { force: true });
                    cy.get('.valuation > .md-table').click();

                    cy.get(
                        '.currentValuation > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-value > span > input'
                    ).should('have.value', '$1,234');
                });

                it('Alter Chattels value', () => {
                    cy.get('#tabsElementValuationWorksheet > .QVHVTab-2 > span').click();

                    cy.get(
                        '.proposedValuation > .QVHV-formSection > :nth-child(6) > :nth-child(2) > .sa-value > span > input'
                    )
                        .clear()
                        .type('1234', { force: true });
                    cy.get('.valuation > .md-table').click();

                    cy.get(
                        '.proposedValuation > .QVHV-formSection > :nth-child(6) > :nth-child(2) > .sa-value > span > input'
                    ).should('have.value', '$1,234');

                    cy.get('#tabsElementValuationWorksheet > .QVHVTab-1 > span').click();
                    cy.get('.valuation > .QVHV-buttons > .QVHV-buttons-left > .primary').click();
                });

                it('Alter Adopted Values for Report', () => {
                    cy.get(
                        '.QVHV-formSection > :nth-child(12) > :nth-child(2) > :nth-child(1) > span > input'
                    )
                        .type(1234)
                        .then(() => {
                            cy.get('.contentWrapper')
                                .click({ force: true })
                                .then(() => {
                                    cy.get(
                                        '.QVHV-formSection > :nth-child(12) > :nth-child(2) > :nth-child(1) > span > input'
                                    ).should('have.value', '$1,234');
                                });
                        });

                    cy.get('.valuation > .QVHV-buttons > .QVHV-buttons-left > .primary').click();
                });
            });
        });

        context('Leaves Step', () => {
            it('Goes to next step', () => {
                ValuationJob.nextStep('valuation').click();
            });
        });
    });

    context('Report Details', () => {
        context('Checks page', () => {
            it('Should be on Report Details page', () => {
                ValuationJob.stepperItem('reportDetails').should('have.class', 'active');
            });
        });

        context('Report Details Page - Expand All', () => {
            it('Should validate the expand all functionality on the Report Details page', () => {
                PropertyDetails.reportDetailsExpandAll
                    .should('exist').click();
                PropertyDetails.reportDetailsExpandAll
                    .should('have.class', 'expandAll down');
                PropertyDetails.reportDetailsExpandAll
                    .should('exist').click();
                PropertyDetails.reportDetailsExpandAll
                    .should('have.class', 'expandAll');
            });
        });

        context('Checks for elements existing', () => {
            context('Checks Valuation Conclusion', () => {
                ValuationJob.checkValuationConclusion();
            });

            context('Checks Risks', () => {
                ValuationJob.checkRisks();
            });
        });

        context('Alter data', () => {
            context('Switch tab to Valuation Conclusion', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementReportDetails > .QVHVTab-1 > span').click();

                    cy.get('#tabsElementReportDetails > li.QVHVTab-1 > .is-active').should(
                        'be.visible'
                    );
                });
            });

            context('Input', () => {
                it('Populates Market Comments', () => {
                    ValuationJob.marketCommentsMultiselect.click();
                    cy.get(
                        '.valuationconclusion > .QVHV-formSection > :nth-child(1) > .fiftyPct > .btn-group > .multiselect-container > :nth-child(3) > a > .radio'
                    ).click();
                    cy.get(
                        '.QVHV-formSection > :nth-child(1) > .hundyPct > :nth-child(2) > .advSearch-text'
                    )
                        .invoke('val')
                        .then($val => {
                            expect($val).to.contain('National overview');
                        });
                });

                it('Generates Property Description', () => {
                    cy.get(
                        ':nth-child(2) > .advSearch-group > :nth-child(1) > span > .material-icons'
                    )
                        .click()
                        .then(() => {
                            cy.wait(1000);
                            cy.get(
                                '.valuationconclusion > .QVHV-formSection > :nth-child(2) > .advSearch-group > :nth-child(2) > .advSearch-text'
                            )
                                .invoke('val')
                                .then($val => {
                                    expect($val).to.equal(
                                        'The property being valued comprises of a cottage constructed circa 2000 with no appreciable view and garaging for two vehicles.'
                                    );
                                });
                        });
                });

                it('Generates Valuation Approach', () => {
                    cy.get(':nth-child(3) > :nth-child(4) > :nth-child(1) > span > .material-icons')
                        .click()
                        .then(() => {
                            cy.wait(1000);
                            cy.get(
                                '.valuationconclusion > .QVHV-formSection > :nth-child(3) > :nth-child(4) > :nth-child(2) > .advSearch-text'
                            )
                                .invoke('val')
                                .then($val => {
                                    expect($val).to.not.equal(
                                        ''
                                    );
                                });
                        });
                });
            });
        });

        context('Leaves Step', () => {
            it('Goes to next step', () => {
                ValuationJob.save();
                ValuationJob.nextStep('reportDetails').click();
            });
        });
    });

    context('Location Details', () => {
        context('Checks page', () => {
            it('Should be on Location Details page', () => {
                ValuationJob.stepperItem('locationDetails').should('have.class', 'active');
            });
        });

        context('Location Details Page - Expand All', () => {
            it('Should validate the expand all functionality on the Location Details page', () => {
                PropertyDetails.locationDetailsExpandAll
                    .should('exist').click();
                PropertyDetails.locationDetailsExpandAll
                    .should('have.class', 'expandAll down');
                PropertyDetails.locationDetailsExpandAll
                    .should('exist').click();
                PropertyDetails.locationDetailsExpandAll
                    .should('have.class', 'expandAll');
            });
        });

        context('Checks for elements existing', () => {
            context('Checks Sale History', () => {
                ValuationJob.checkSaleHistory();
            });

            context('Checks Zoning', () => {
                ValuationJob.checkZoning();
            });

            context('Checks Legal Description', () => {
                ValuationJob.checkLegalDescription();
            });

            context('Checks Site and Location', () => {
                ValuationJob.checkSiteAndLocation();
            });

            context('Checks Rating Information', () => {
                ValuationJob.checkRatingInformation();
            });
        });

        context('Alter data', () => {
            context('Switch back to first tab', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementLocationDetails > .QVHVTab-1 > span').click();

                    cy.get('#tabsElementLocationDetails > li.QVHVTab-1 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Input', () => {
                it('Writes location details comment', () => {
                    cy.get(
                        '[data-cy="saleComments"] > :nth-child(2) > .advSearch-text'
                    ).type(locationComment);
                    cy.get(
                        '[data-cy="saleComments"] > :nth-child(2) > .advSearch-text'
                    ).should('have.value', locationComment);
                });
            });
        });

        context('Leaves Step', () => {
            it('Goes to next step', () => {
                ValuationJob.nextStep('locationDetails').click();
            });
        });
    });

    context('Photos and Attachments', () => {
        it('New valuation job option available', () => {
            ValuationJob.photoAndAttachmentStepper.should('exist').click();
            ValuationJob.uploadButton.eq(1).selectFile('cypress/fixtures/JPEG - kbsize large, photo sample.jpeg', { force: true });
            ValuationJob.uploadConfirm.click();
            ValuationJob.photosIncluded.check();
            cy.wait(1000);
            let jobId = null;
            ValuationJob.addDetailsButton.click();
            ValuationJob.jobId.then(href => {
                cy.visit(href);
                ValuationJob.primaryPhotos.check({ force: true });
                ValuationJob.internalUseOnly.check();
                ValuationJob.includeInInsurance.check();
                ValuationJob.tagPicker.then(spans => {
                    const selectedSpans = Cypress._.sampleSize(spans.toArray(), 2);
                    cy.wrap(selectedSpans[0]).click();
                    cy.wrap(selectedSpans[1]).click();
                    ValuationJob.saveAndClose.click({ force: true });
                });
                jobId = href.split('=')[1];
                cy.visit('/property/' + 1621837 + '/home-valuation/' + jobId);
                cy.wait(1000);
                ValuationJob.photoAndAttachmentStepper.should('exist').click();
                ValuationJob.attachmentTab.click();
                ValuationJob.uploadButton.eq(2).selectFile('cypress/fixtures/JPEG - kbsize large, photo sample.jpeg', { force: true });
                cy.wait(1000);
                ValuationJob.aerialAndLocationTab.click();
                ValuationJob.uploadButton.eq(2).selectFile('cypress/fixtures/Landscape Orientation, photo sample.jpeg', { force: true });
                cy.wait(1000);
                ValuationJob.notesAndFinalReportsTab.click();
                ValuationJob.uploadButton.eq(2).selectFile('cypress/fixtures/JPEG - kbsize large, photo sample.jpeg', { force: true });
            });
        });
    });

    context('QA and Review', () => {
        context('Checks page', () => {
            it('Should be on QA and Review page', () => {
                ValuationJob.qaAndReviewStepper.should('exist').click();
            });
        });

        context('QA and Review Page - Expand All', () => {
            it('Should validate the expand all functionality on the QA and Review page', () => {
                PropertyDetails.qaAndReviewExpandAll
                    .should('exist').click();
                PropertyDetails.qaAndReviewExpandAll
                    .should('have.class', 'expandAll down');
                PropertyDetails.qaAndReviewExpandAll
                    .should('exist').click();
                PropertyDetails.qaAndReviewExpandAll
                    .should('have.class', 'expandAll');
            });
        });

        context('Checks for elements existing', () => {
            context('Checks Report & Compliance', () => {
                ValuationJob.checkReportAndCompliance();
            });

            context('Checks Countersigner Review', () => {
                ValuationJob.checkCountersignerReview();
            });

            context('Uses QV Cloud Uploader', () => {
                it('Uploads photo by drag and drop', () => {
                    ValuationJob.uploadFileQVCloudUploader();
                });
            });

            it('Valuation jobs are read only except for valuer, countersigning valuer and admin staff', () => { });
        });

        context('Creates report context', () => {
            it('Creates report', () => {
                const reportLink = `${env.config.baseUrl}/generateValuationReport/${jobId}`;

                cy.request(reportLink).then(response => {
                    cy.doubleLog(response);
                    cy.doubleLog(response.body);
                    cy.doubleLog(JSON.stringify(response.body));

                    expect(response.status).to.equal(200);

                });

                ValuationJob.createReportButton.click();

                cy.get('.qaAndReview > .warning > .alert').should('not.have.attr', 'visible');
            });

            it('Completes valuation', () => {
                cy.get('.QVHV-buttons-left > :nth-child(2)').click();
                cy.wait(1000);
                cy.get(
                    '#confirmationCompleteJob > .alert > .alertButtons > #confirmationClose'
                ).click();
                cy.wait(2000);
                cy.get('.valuationJobError > .alert').then($alert => {
                    if ($alert.is(':visible')) {
                        cy.wait(100);
                        cy.get('.valuationJobError > .alert > .alertButtons > #errorCancel')
                            .click()
                            .then(() => {
                                cy.doubleLog('###\n ERROR BUTTON CLICKED \n ###');
                                cy.wait(2000);

                                cy.get('.valuationJobError > .alert').should('not.be.visible');
                            });
                    }
                });
            });
        });
    });
});

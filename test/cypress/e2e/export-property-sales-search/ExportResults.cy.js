//TODO:

//could use https://www.npmjs.com/package/node-xlsx to parse downloaded excel files
//check column headers
//check #rows-1 matches the number of results found to be exported

// TODO:
//property search export
//sales search export

import AdvancedSearch from '../../model/AdvancedSearch';
import AdvancedSearchSales from '../../model/AdvancedSearchSales';
import Home from '../../model/Home';
import Reports, { dashboard } from '../../model/Reports';
import PropertyDetails from '../../model/PropertyDetails';
import PropertySearchResults from '../../model/PropertySearchResults';

const category1Prefix = 'I';
const category2Prefix = 'RD';

const streetName = 'Southwark';
const nspFrom = '400000';
const minResults = 10;

describe('Export Results',
    {
        testIsolation: false,
        defaultCommandTimeout: 15000,
    },
    () => {

    before(() => {
        cy.login();
        cy.visit('/');
    });

     context('Export Property Search Results', () => {
        it('Exports Property Search Results', () => {
            cy.intercept('POST', '/report-job').as('scheduleReportJob');
            Home.advancedSearchButton.click();
            AdvancedSearch.streetName.clear().type(streetName);
            AdvancedSearch.search();
            cy.wait(3000);
            cy.get('.toolbar > .exportResults > .material-icons').click();
            cy.wait('@scheduleReportJob').then(intercept => {
                expect(intercept.response.statusCode === 201);
            });
            cy.get('[data-cy="alert-modal"]').should('be.visible').find('#continue').click();
        });
    });

    context('Export Sale Search Results', () => {
        it(`Sales search results for street: ${streetName}, NSP >= ${nspFrom}, TA: ${
            env.TA_CODE.CHCH
        }`, () => {
            cy.intercept('POST', '/report-job').as('scheduleReportJob');
            Home.advancedSearchButton.click();
            AdvancedSearch.salesSearch.click();
            AdvancedSearch.taRangeFrom.clear().type(env.TA_CODE.CHCH);
            AdvancedSearch.taRangeTo.clear().type(env.TA_CODE.CHCH);
            AdvancedSearch.streetName.clear().type(streetName);
            AdvancedSearchSales.nspFrom.clear().type(nspFrom);
            AdvancedSearch.search();
            cy.wait(3000);
            PropertySearchResults.results.should('have.length.at.least', minResults);

            cy.get('.toolbar > .exportResults > .material-icons').click();
            cy.wait('@scheduleReportJob').then(intercept => {
                expect(intercept.response.statusCode === 201);
            });
            cy.get('[data-cy="alert-modal"]').should('be.visible').find('#continue').click();
        });
    });
    context('Failed export', () => {
        it('Tries to export 200,000+ Property Search Results records', () => {
            Home.quickSearchBar.clear().type('{enter}');
            cy.get('.toolbar > .exportResults > .material-icons').click();
            cy.get('[data-cy="alert-modal"]').should('be.visible').find('h1').should('contain.text', 'Export limit exceeded');
        });
    });

    describe('Exports are available for download in "My Reports"', () => {
        before(() => {
            dashboard.visit();
        });
        const time2MinsAgo = new Date(Date.now() - 2 * 60 * 1000);
        // of all the reports in the table, get the first record that matches the report name
        const saleExportName = 'Sale Export';
        const propertyExportName = 'Property Export';
        const homeValuationExportName = 'Home Valuation Export';
        const exportReportNames = [saleExportName, propertyExportName, homeValuationExportName];

        for (const [index, reportName] of exportReportNames.entries()) {
            it(`Report "${reportName}" is available for download`, () => {
                cy.get('[data-cy="my-reports-table-row"]').eq(index).then(row => {
                    cy.wrap(row).find('[data-cy="report-name"]').should('have.text', reportName);
                    cy.wrap(row).find('[data-cy="report-date"]').invoke('text').then(reportDateText => {
                        const reportDate = new Date(reportDateText);
                        expect(reportDate).to.be.gte(time2MinsAgo);
                    });
                    cy.wrap(row).find('[data-cy="report-status"]').should('contain.text', 'Ready');
                });
            });
        }
    });
});

import PropertyDetails from '../../model/PropertyDetails';
import SalePage from '../../model/Sale';

describe('View Sale', {
    testIsolation: false,
    defaultCommandTimeout: 30000,
}, () => {
    const qpid = env['ADDRESS9'].QPID

    context('As an internal user', () => {
        before(() => {
            cy.login();
            PropertyDetails.visit(qpid);
        });

        it('Clicking agreement date opens Update Sale screen in new tab', () => {
            const SaleDetails = PropertyDetails.saleDetails;

            SaleDetails.elements.container.should('exist');
            SaleDetails.clickSaleLink(0);
            SalePage.isLoaded();
            SalePage.elements.pageTitle.should('have.text', 'Update Sale');
        });

        it('Cancel & Save buttons should be visisble', () => {
            SalePage.elements.saveButton.should('exist');
            SalePage.elements.cancelButton.should('exist');
        });
    });

    context('As an external user', () => {
        before(() => {
            cy.login();
            cy.overrideUserData({
                roles: [{ name: 'EXTERNAL_USER_READ' }],
            });
            PropertyDetails.visit(qpid);
        });

        it('Clicking agreement date opens View Sale screen in new tab', () => {
            const SaleDetails = PropertyDetails.saleDetails;

            SaleDetails.elements.container.should('exist');
            SaleDetails.clickSaleLink(0);
            SalePage.isLoaded();
            SalePage.elements.pageTitle.should('have.text', 'View Sale');
        });

        it('Sale should be read only', () => {
            SalePage.elements.formInputs.should('be.disabled');
        });

        it('Cancel & Save buttons should not be visisble', () => {
            SalePage.elements.saveButton.should('not.exist');
            SalePage.elements.cancelButton.should('not.exist');
        });
    });
});

describe('Add Sale', {
    testIsolation: false,
    defaultCommandTimeout: 30000,
}, () => {
    const timestamp = new Date().toISOString();
    const qpid = env['ADDRESS9'].QPID


    context('As an internal user', () => {
        before(() => {
            cy.login();
            PropertyDetails.visit(qpid);
        });

        it('Clicking Add Sale button opens Add Sale screen', () => {
            PropertyDetails.saleDetails.clickAddSale();

            SalePage.isLoaded();
            SalePage.elements.pageTitle.should('have.text', 'Add Sale');
            SalePage.fillDefaultFields(timestamp);
        });

        it('Clicking save should save the sale', () => {
            cy.intercept('/api/salesProcessing/saveSale').as('saveSale');

            SalePage.saveSale();

            cy.wait('@saveSale').then((int) => {
                const body = int.request.body;
                expect(body.sale.vendorPurchaser).to.equal(`TEST:${timestamp}`);
            });
        });
    });

    context('As an external user', () => {
        before(() => {
            cy.login();
            cy.overrideUserData({
                roles: [{ name: 'EXTERNAL_USER_READ' }],
            });
            PropertyDetails.visit(qpid);
        });

        it('Add Sale button should not be visible', () => {
            PropertyDetails.saleDetails.elements.addSaleButton.should('be.disabled');
        });
    });
});

describe('Update Sale', {
    testIsolation: false,
    defaultCommandTimeout: 30000,
}, () => {
    context('As an internal user', () => {
        const timestamp = new Date().toISOString();
        const qpid = env['ADDRESS9'].QPID

        before(() => {
            cy.login();
            PropertyDetails.visit(qpid);
            const SaleDetails = PropertyDetails.saleDetails;
            SaleDetails.elements.container.should('exist');
            SaleDetails.clickSaleLink(0);
            SalePage.isLoaded();
        });

        it('Clicking Update Sale should save the sale', () => {
            cy.intercept('/api/salesProcessing/saveSale').as('saveSale');

            SalePage.elements.saleInput('vendor').clear().type(`TEST:${timestamp}`);
            SalePage.saveSale();

            cy.wait('@saveSale').then((int) => {
                const body = int.request.body;
                expect(body.sale.vendorPurchaser).to.equal(`TEST:${timestamp}`);
            });
        });
    });
});

describe('Delete Sale', {
    testIsolation: false,
    defaultCommandTimeout: 30000,
}, () => {
    const qpid = env['ADDRESS9'].QPID

    context('As an internal user', () => {
        before(() => {
            cy.login();
            PropertyDetails.visit(qpid);
            PropertyDetails.saleDetails.clickSaleLink(0);
            SalePage.isLoaded();
        });

        it('Clicking Delete Sale should delete the sale', () => {
            cy.intercept('/api/salesProcessing/deleteSale').as('deleteSale');

            SalePage.deleteSale();

            cy.wait('@deleteSale').then((int) => {
                const body = int.request.body;
                expect(body.qpid).to.equal(qpid);
            })
        });
    });
});

describe('Classification', { defaultCommandTimeout: 15000 }, () => {
    describe('displayClassifications endpoint validation', () => {
        it('expects displayClassifications to be the intended result', () => {
            cy.intercept('POST', env.BASE_URL + '/displayClassifications').as('displayClassifications');

            cy.visitWithLogin('');
            cy.wait('@displayClassifications').then((res) => {
                expect(res.response.statusCode === 200);
                expect(res.response.body).to.not.be.empty;
                expect(res.response.body).to.be.an('array');
                expect(res.response.body.length, 'Response body contains at least one result').to.be.greaterThan(0);
                const responseCategories = res.response.body.map(obj => obj.category);
                const uniqueCategories = new Set(responseCategories);

                for (const category of listOfSampleCategories) {
                    expect(uniqueCategories.has(category), `Spot check: ${category} category exists`).to.be.true;
                }
            });
        });
    });
});

const listOfSampleCategories = [
    'Age',
    'RegionType',
    'Tags',
];

import OwnershipUpdate from '../../../vue/components/rollMaintenance/propertySale/common/OwnershipUpdate.vue';
import { ref } from 'vue';

describe('OwnershipUpdate.cy.js', () => {
    it('todo', () => {

        // TODO: revisit when optional chaining is available in Cypress component tests


        // const ownership = {
        //     owners: [
        //         { qpid: 929500, ownerId: 66537520, entityId: 1, entity: '1 Private: individual', entityCode: '1', type: 'Occupier', firstName: '<PERSON>', secondName: '<PERSON>', thirdName: 'smith', surname: '<PERSON>', daytimePhoneSTD: '03', daytimePhone: '123123123', mobilePhoneSTD: '021', mobilePhone: '123213123', email: '<EMAIL>', nameSecretYN: true, delete: false, numberOfActiveProperties: 1, assessment: { occownType: 'A', order: 1 }, address: { CO: 'CO saved!', streetBox: '323 Stokes Valley Road', suburb: 'Stokes Valley', town: 'Lower Hutt', postcode: '5019', country: null } },
        //         { qpid: 929500, ownerId: 66537519, entityId: 1, entity: '1 Private: individual', entityCode: '1', type: 'Occupier', firstName: 'Lisa', secondName: 'Megan ', thirdName: 'bob', surname: 'Sinclair', daytimePhone: null, mobilePhone: null, email: null, nameSecretYN: true, delete: false, numberOfActiveProperties: 1, assessment: { occownType: 'A', order: 2 }, address: { CO: 'CO saved!', streetBox: '323 Stokes Valley Road', suburb: 'Stokes Valley', town: 'Lower Hutt', postcode: '5019', country: null } },
        //         { qpid: 929500, ownerId: 66537518, entityId: 1, entity: '1 Private: individual', entityCode: '1', type: 'Occupier', firstName: 'Gladys', secondName: 'Edith ', thirdName: 'mary', surname: 'Derham', daytimePhone: null, mobilePhone: null, email: null, nameSecretYN: true, delete: false, numberOfActiveProperties: 1, assessment: { occownType: 'A', order: 3 }, address: { CO: 'CO saved!', streetBox: '323 Stokes Valley Road', suburb: 'Stokes Valley', town: 'Lower Hutt', postcode: '5019', country: null } },
        //     ],
        //     toras: { tenureId: 1, entityId: 1, rateabilityId: 1, apportionmentId: 1, specialValueId: 1, tenure: '1 Not Leased (Owner is Occupier)', entity: '1 Private: individual', rateability: '1 Rateable', apportionment: '0 NOT APPLICABLE' },
        //     isMaoriLand: false,
        // };
        // const isReadOnly = false;
        // const isAnyExistingQivsOwnerNonOccupier = false;
        // const isAnyExistingQivsOwnerHavingMultipleProperties = false;
        // const validationResult = { status: 'SUCCESS', errors: [], hasErrors: false, warnings: [], hasWarnings: false, validations: {}, formattedErrors: [], formattedWarnings: [] };
        // function getErrorsForLabel(label) {
        //     return validationResult.validations[label].errors;
        // }
        // cy.mount(OwnershipUpdate, {
        //     propsData: {
        //         isReadOnly,
        //         ownership,
        //         isAnyExistingQivsOwnerNonOccupier,
        //         isAnyExistingQivsOwnerHavingMultipleProperties,
        //         validationResult,
        //         getErrorsForLabel,
        //     },
        // });
    });
});

/*
    ownership: {
        type: Object,
        required: true,
    },
    isReadOnly: {
        type: Boolean,
        required: false,
        default: false,
    },
    isAnyExistingQivsOwnerNonOccupier: {
        type: Boolean,
        required: false,
        default: false,
    },
    isAnyExistingQivsOwnerHavingMultipleProperties: {
        type: Boolean,
        required: false,
        default: false,
    },
    getErrorsForLabel: {
        type: Function,
        required: true,
    },
    validationResult: {
        type: Object,
        required: false,
        default: () => ({}),
    },

*/

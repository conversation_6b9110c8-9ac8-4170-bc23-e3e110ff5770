import { DetailSection, DetailSectionHeader, DetailSectionTitle, DetailSectionExpander, DetailSectionBody } from '@/components/ui/detailSection';
import { DetailSectionModel } from '../../../model/components/ui/DetailSection.js';

const stubContent = 'Test content, Inside DetailSectionBody';

function createDetailSection(props = {}, on = {}) {
    const selector = 'test-component';
    const cypressSelector = `[data-cy="${selector}"]`;
    return {
        name: 'TestComponent',
        selector,
        cypressSelector,
        render(h) {
            return h(DetailSection, { props, on, attrs: { 'data-cy': selector } }, [
                h(DetailSectionHeader, [
                    h(DetailSectionTitle, 'Testing Detail Section Component'),
                    h(DetailSectionExpander),
                ]),
                h(DetailSectionBody, [
                    h('p', {
                        class: ['qv-text-md'],
                    }, stubContent),
                ]),
            ]);
        },
    };
}

describe('DetailSection', () => {
    context('props', () => {
        it('defaults to expanded', () => {
            const component = createDetailSection();
            cy.mount(component);

            const model = DetailSectionModel(component.cypressSelector);
            model.content.should('be.visible');
        });

        it('should default to collapsed when defaultExpanded is false', () => {
            const component = createDetailSection({ defaultExpanded: false });
            cy.mount(component);

            const model = DetailSectionModel(component.cypressSelector);
            model.content.should('not.be.visible');
        });

        it('should default to expanded when defaultExpanded is true', () => {
            const component = createDetailSection({ defaultExpanded: true });
            cy.mount(component);

            const model = DetailSectionModel(component.cypressSelector);
            model.content.should('be.visible');
        });
    });

    context('functionality', () => {
        it('should collapse/expand when expanded is clicked', () => {
            const component = createDetailSection();
            cy.mount(component);

            const section = DetailSectionModel(component.cypressSelector);
            section.content.should('be.visible');
            section.expander.click();
            section.content.should('not.be.visible');
            section.expander.click();
            section.content.should('be.visible');
        });

    });

    context('events', () => {
        it('emits onCollapse event when collapsed', () => {
            const onCollapse = cy.spy().as('onCollapse');
            const component = createDetailSection({}, {
                onCollapse,
            });

            cy.mount(component);

            const section = DetailSectionModel(component.cypressSelector);
            section.expander.click();
            section.content.should('not.be.visible');
            cy.get('@onCollapse').should('have.been.calledOnce');
        });

        it('emits onExpand event when expanded', () => {
            const onExpand = cy.spy().as('onExpand');
            const component = createDetailSection({ defaultExpanded: false }, {
                onExpand,
            });
            cy.mount(component);
            const section = DetailSectionModel(component.cypressSelector);
            section.expander.click();
            section.content.should('be.visible');
            cy.get('@onExpand').should('have.been.calledOnce');
        });
    });
});

import { NumeralInput as Model } from '@/components/ui/input';
import { ref } from 'vue';

describe('NumeralInput', () => {
    it('shows raw value when focused', () => {
        cy.mount(Model, {
            propsData: {
                value: 10000,
            },
        });

        cy.get('input').focus();
        cy.get('input').should('have.value', 10000);
    });

    it('shows formatted value when not focused', () => {
        cy.mount(Model, {
            propsData: {
                value: 10000,
                preset: 'MONEY',
            },
        });

        cy.get('input').should('have.value', '$10,000');
    });

    it('typing emits input event with new value', () => {
        const onInput = cy.spy().as('onInput');
        const value = ref(0);
        const newValue = 10000;

        cy.mount(Model, {
            propsData: {
                value,
            },
            listeners: {
                input: onInput
            }
        })

        cy.get('input').focus();
        cy.get('input').clear().type(newValue).blur();
        cy.get('@onInput').should('have.been.calledWith', newValue);
    });

    context('when readonly', () => {
        it('has disabled input', () => {
            cy.mount(Model, {
                propsData: {
                    readonly: true,
                    value: 10000,
                },
            });

            cy.get('input').should('be.disabled');
        });

        it('is removed from tabindex', () => {
            cy.mount(Model, {
                propsData: {
                    readonly: true,
                    value: 10000,
                },
            });

            cy.get('input').should('have.attr', 'tabindex').should('equal', '-1');
        })
    });
});

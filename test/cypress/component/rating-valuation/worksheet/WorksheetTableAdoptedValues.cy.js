import { WorksheetTableAdoptedValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

function createValuesObject(number = 500) {
    return {
        value: {
            capitalValue: number,
            landValue: number,
            valueOfImprovements: number * 2,
        },
    };
}

describe('WorksheetTableAdoptedValues', () => {

    it('should correctly display initial adopted worksheet capital value', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedValues.capitalValue.should('have.value', `$1,000`);
    });

    it('should correctly display initial adopted worksheet land value', () => {
        const value = 2000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedValues.landValue.should('have.value', `$2,000`);
    });

    it('should correctly display initial adopted worksheet value of improvements', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedValues.valueOfImprovements.should('have.value', `$2,000`);
    });

    it('should update capital value when modified', () => {
        const value = 5000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedValues.capitalValue.clear().type('6000');
        Model.WorksheetTableAdoptedValues.capitalValue.blur();
        Model.WorksheetTableAdoptedValues.capitalValue.should('have.value', '$6,000');
    });

    it('should update land value when modified', () => {
        const value = 7000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedValues.landValue.clear().type('4000');
        Model.WorksheetTableAdoptedValues.landValue.blur();
        Model.WorksheetTableAdoptedValues.landValue.should('have.value', '$4,000');
    });

    it('should update value of improvements when capital value and land value  is modified', () => {
        cy.mount(Component, {
            propsData: createValuesObject(0),
        });
        Model.WorksheetTableAdoptedValues.capitalValue.clear().type('7000');
        Model.WorksheetTableAdoptedValues.landValue.clear().type('5000');
        Model.WorksheetTableAdoptedValues.landValue.blur();
        Model.WorksheetTableAdoptedValues.valueOfImprovements.should('have.value', '$2,000');
    });

    it('should show formatted dollar capital value', () => {
        cy.mount(Component, {
            propsData: createValuesObject(123),
        });
        Model.WorksheetTableAdoptedValues.capitalValue.should('have.value', '$123');
    });

    it('should show formatted dollar land value', () => {
        cy.mount(Component, {
            propsData: createValuesObject(123),
        });
        Model.WorksheetTableAdoptedValues.landValue.should('have.value', '$123');
    });

    it('should show formatted dollar value of improvements ', () => {
        cy.mount(Component, {
            propsData: createValuesObject(123),
        });
        Model.WorksheetTableAdoptedValues.valueOfImprovements.should('have.value', '$246');
    });
});

import { WorksheetTableMaoriLand as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

function createProps(number = 3.5, owners = 2) {
    const maoriLandData = {
        currentMaoriLandAdjustment: {
            multipleOwnerAdjustmentPercentage: number,
            siteSignificanceAdjustmentPercentage: number,
        },
        numberOfOwners: owners,
        revisedMaoriLandAdjustment: {
            multipleOwnerAdjustmentPercentage: number,
            siteSignificanceAdjustmentPercentage: number,
        },
    }

    return {
        value: maoriLandData,
        currentAdjustment: maoriLandData.currentMaoriLandAdjustment,
        revisionAdjustment: maoriLandData.revisedMaoriLandAdjustment,
    };
}

describe('ValuationWorksheetMaoriLand', () => {
    it('should correctly render initial values for multiple owners adjustment', () => {
        const value = 0.3;
        const propsData = createProps(value);
        cy.mount(Component, {
            propsData,
        });
        Model.WorksheetTableMaoriLand.multipleOwnerAdjusted.should('have.value', `${value}%`);
        Model.WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.should('have.value', `${value}%`);
    });

    it('should correctly render initial values for site significance adjustment', () => {
        const value = 0.3;
        cy.mount(Component, {
            propsData: createProps(value),
        });
        Model.WorksheetTableMaoriLand.siteSignificanceAdjusted.should('have.value', `${value}%`);
        Model.WorksheetTableMaoriLand.siteSignificanceRevisionAdjustment.should('have.value', `${value}%`);
    });

    it('should correctly render initial no. of owners', () => {
        const owners = 5;
        cy.mount(Component, {
            propsData: createProps(0.05, owners),
        });
        Model.WorksheetTableMaoriLand.numberOfOwners.should('have.value', owners);
    });

    it('should update multiple owners adjustment percentage when modified', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.multipleOwnerAdjusted.clear().type('7.00');
        Model.WorksheetTableMaoriLand.multipleOwnerAdjusted.blur();
        Model.WorksheetTableMaoriLand.multipleOwnerAdjusted.should('have.value', '7.0%');
    });

    it('should update site significance adjustment percentage when modified', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.siteSignificanceAdjusted.clear().type('7.00');
        Model.WorksheetTableMaoriLand.siteSignificanceAdjusted.blur();
        Model.WorksheetTableMaoriLand.siteSignificanceAdjusted.should('have.value', '7.0%');
    });

    it('should update no. of owners when modified', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.numberOfOwners.clear().type('7');
        Model.WorksheetTableMaoriLand.numberOfOwners.blur();
        Model.WorksheetTableMaoriLand.numberOfOwners.should('have.value', '7');
    });

    it('should update multiple owners revision adjustment percentage when modified', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.clear().type('7.00');
        Model.WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.blur();
        Model.WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.should('have.value', '7.0%');
    });

    it('should update site significance revision adjustment percentage when modified', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.siteSignificanceRevisionAdjustment.clear().type('7.00');
        Model.WorksheetTableMaoriLand.siteSignificanceRevisionAdjustment.blur();
        Model.WorksheetTableMaoriLand.siteSignificanceRevisionAdjustment.should('have.value', '7.0%');
    });

    it('should show 0.00% as default multiple owners adjustment percentage when no value provided', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.multipleOwnerAdjusted.clear().type('{backspace}');
        Model.WorksheetTableMaoriLand.multipleOwnerAdjusted.blur();
        Model.WorksheetTableMaoriLand.multipleOwnerAdjusted.should('have.value', '0.0%');
    });

    it('should show 0.00% as default site significance adjustment percentage when no value provided', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.siteSignificanceAdjusted.clear().type('{backspace}');
        Model.WorksheetTableMaoriLand.siteSignificanceAdjusted.blur();
        Model.WorksheetTableMaoriLand.siteSignificanceAdjusted.should('have.value', '0.0%');
    });

    it('should show 0 as default no. of owners when no value provided', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.numberOfOwners.clear().type('{backspace}');
        Model.WorksheetTableMaoriLand.numberOfOwners.blur();
        Model.WorksheetTableMaoriLand.numberOfOwners.should('have.value', '0');
    });

    it('should show 0.00% as default multiple owners revision adjustment percentage when no value provided', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.clear().type('{backspace}');
        Model.WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.blur();
        Model.WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.should('have.value', '0.0%');
    });

    it('should show 0.00% as default site significance revision adjustment percentage when no value provided', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.siteSignificanceRevisionAdjustment.clear().type('{backspace}');
        Model.WorksheetTableMaoriLand.siteSignificanceRevisionAdjustment.blur();
        Model.WorksheetTableMaoriLand.siteSignificanceRevisionAdjustment.should('have.value', '0.0%');
    });

    it('should only allow positive whole numbers for no. owners', () => {
        cy.mount(Component, {
            propsData: createProps(),
        });
        Model.WorksheetTableMaoriLand.numberOfOwners.clear().type('-7');
        Model.WorksheetTableMaoriLand.numberOfOwners.blur();
        Model.WorksheetTableMaoriLand.numberOfOwners.should('have.value', '0');
    });

});

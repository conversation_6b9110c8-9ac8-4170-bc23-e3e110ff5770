import { WorksheetTableAdoptedRevisionValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

function createValuesObject(number = 500) {
    return {
        value: {
            capitalValue: number,
            landValue: number,
            valueOfImprovements: number * 2,
        },
    };
}

describe('WorksheetTableAdoptedRevisionValues', () => {

    it('should correctly display initial adopted worksheet revision capital value', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedRevisionValues.capitalValue.should('have.value', `$1,000`);
    });

    it('should correctly display initial adopted worksheet revision land value', () => {
        const value = 2000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedRevisionValues.landValue.should('have.value', `$2,000`);
    });

    it('should correctly display initial adopted worksheet revision value of improvements', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedRevisionValues.valueOfImprovements.should('have.value', `$2,000`);
    });

    it('should update revision capital value when modified', () => {
        const value = 5000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedRevisionValues.capitalValue.clear().type('6000');
        Model.WorksheetTableAdoptedRevisionValues.capitalValue.blur();
        Model.WorksheetTableAdoptedRevisionValues.capitalValue.should('have.value', '$6,000');
    });

    it('should update revision land value when modified', () => {
        const value = 7000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableAdoptedRevisionValues.landValue.clear().type('4000');
        Model.WorksheetTableAdoptedRevisionValues.landValue.blur();
        Model.WorksheetTableAdoptedRevisionValues.landValue.should('have.value', '$4,000');
    });

    it('should update revision value of improvements when capital value and land value  is modified', () => {
        cy.mount(Component, {
            propsData: createValuesObject(0),
        });
        Model.WorksheetTableAdoptedRevisionValues.capitalValue.clear().type('7000');
        Model.WorksheetTableAdoptedRevisionValues.landValue.clear().type('5000');
        Model.WorksheetTableAdoptedRevisionValues.landValue.blur();
        Model.WorksheetTableAdoptedRevisionValues.valueOfImprovements.should('have.value', '$2,000');
    });

    it('should show formatted dollar revision capital value', () => {
        cy.mount(Component, {
            propsData: createValuesObject(123),
        });
        Model.WorksheetTableAdoptedRevisionValues.capitalValue.should('have.value', '$123');
    });

    it('should show formatted dollar revision land value', () => {
        cy.mount(Component, {
            propsData: createValuesObject(123),
        });
        Model.WorksheetTableAdoptedRevisionValues.landValue.should('have.value', '$123');
    });

    it('should show formatted dollar revision value of improvements ', () => {
        cy.mount(Component, {
            propsData: createValuesObject(123),
        });
        Model.WorksheetTableAdoptedRevisionValues.valueOfImprovements.should('have.value', '$246');
    });
});

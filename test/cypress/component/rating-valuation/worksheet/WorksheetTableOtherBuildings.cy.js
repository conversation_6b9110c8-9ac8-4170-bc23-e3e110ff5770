import { WorksheetTableOtherBuildings as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

const { WorksheetTableOtherBuildings } = Model;
function mockRow() {
    return {
        buildingType: 'Test',
        description: 'Test',
        areaInSquareMetres: 0,
        valuePerSquareMetre: 0,
        value: 0,
    }
}

describe('WorksheetTablePrimaryBuildings', () => {
    it('recalculates the value when the value per square metre is changed', () => {
        const mock = mockRow();
        mock.areaInSquareMetres = 100;
        const propsData = {
            value: [mock],
        }

        cy.mount(Component, {
            propsData
        });

        const row = WorksheetTableOtherBuildings.row(0);
        row.valuePerSquareMetre.clear().type('100');
        row.valuePerSquareMetre.blur();
        row.value.should('have.value', '$10,000');
    });

    it('recalculates the value per square metre when the area is changed', () => {
        const mock = mockRow();
        mock.valuePerSquareMetre = 100;
        mock.value = 10000;
        const propsData = {
            value: [mock],
        }

        cy.mount(Component, {
            propsData
        });

        const row = WorksheetTableOtherBuildings.row(0);
        row.area.clear().type('100');
        row.area.blur();
        row.valuePerSquareMetre.should('have.value', '$100.00');
    });

    it('recalculates the value per square metre when the value is changed', () => {
        const mock = mockRow();
        mock.areaInSquareMetres = 100;
        const propsData = {
            value: [mock],
        }

        cy.mount(Component, {
            propsData
        });

        const row = WorksheetTableOtherBuildings.row(0);
        row.value.clear().type('10000');
        row.value.blur();
        row.valuePerSquareMetre.should('have.value', '$100.00');
    });

    it('adds a new row when the add button is clicked', () => {
        cy.mount(Component, {
            propsData: {
                value: [mockRow()],
            }
        });

        WorksheetTableOtherBuildings.rows.should('have.length', 1);

        const row = WorksheetTableOtherBuildings.row(0);
        row.addRemoveButton.add.click();

        WorksheetTableOtherBuildings.rows.should('have.length', 2);
    });

    it('removes a row when the remove button is clicked', () => {
        cy.mount(Component, {
            propsData: {
                value: [mockRow(), mockRow()],
            }
        });

        WorksheetTableOtherBuildings.rows.should('have.length', 2);

        const row = WorksheetTableOtherBuildings.row(1);
        row.addRemoveButton.remove.click();

        WorksheetTableOtherBuildings.rows.should('have.length', 1);
    });

    it('hides the remove button when there is only one row', () => {
        cy.mount(Component, {
            propsData: {
                value: [mockRow()],
            }
        });

        const row = WorksheetTableOtherBuildings.row(0);
        row.addRemoveButton.remove.should('not.exist');
    });

    it('emits changed event when any value is changed', () => {
        const onChangeSpy = cy.spy().as('onChangeSpy')
        const listeners = {
            changed: onChangeSpy,
        }

        const mock = mockRow();
        const propsData = {
            value: [mock],
        }

        cy.mount(Component, {
            propsData,
            listeners,
        });

        const row = WorksheetTableOtherBuildings.row(0);
        row.valuePerSquareMetre.clear().type('100');
        row.valuePerSquareMetre.blur();

        cy.get('@onChangeSpy').should('have.been.called', 1);

        row.area.clear().type('100');
        row.area.blur();
        cy.get('@onChangeSpy').should('have.been.called', 3);

        row.value.clear().type('10000');
        row.value.blur();

        cy.get('@onChangeSpy').should('have.been.called', 6);
    });

    it('emits changed event when a row is added', () => {
        const onChangeSpy = cy.spy().as('onChangeSpy')
        const listeners = {
            changed: onChangeSpy,
        }

        cy.mount(Component, {
            propsData: {
                value: [mockRow()],
            },
            listeners,
        });

        WorksheetTableOtherBuildings.row(0).addRemoveButton.add.click();

        cy.get('@onChangeSpy').should('have.been.called', 1);
    });

    it('emits changed event when a row is removed', () => {
        const onChangeSpy = cy.spy().as('onChangeSpy')
        const listeners = {
            changed: onChangeSpy,
        }

        cy.mount(Component, {
            propsData: {
                value: [mockRow(), mockRow()],
            },
            listeners,
        });

        WorksheetTableOtherBuildings.row(1).addRemoveButton.remove.click();

        cy.get('@onChangeSpy').should('have.been.called', 1);
    });

    it('should have a blank row when there are no rows', () => {
        cy.mount(Component, {
            propsData: {
                value: [],
            }
        });

        WorksheetTableOtherBuildings.rows.should('have.length', 1);
    });

    it('should disable inputs when readonly', () => {
        cy.mount(Component, {
            propsData: {
                readonly: true,
                value: [mockRow()],
            }
        });

        const row = WorksheetTableOtherBuildings.row(0);
        row.valuePerSquareMetre.should('be.disabled');
        row.area.should('be.disabled');
        row.value.should('be.disabled');
    });

    it('should hide the add/remove button when readonly', () => {
        cy.mount(Component, {
            propsData: {
                readonly: true,
                value: [mockRow()],
            }
        });

        const row = WorksheetTableOtherBuildings.row(0);
        row.addRemoveButton.component.should('not.exist');
    });
});

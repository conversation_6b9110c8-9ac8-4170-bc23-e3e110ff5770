import { WorksheetTableValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

function createValuesObject(number = 1000) {
    return {
        value: {
            capitalValue: number,
            landValue: number,
            valueOfImprovements: number*2,
        },
    };
}

describe('ValuationWorksheetValues', () => {
    it('should correctly display initial worksheet capital value', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableValues.capitalValue.should('have.value', `$1,000`);
    });

    it('should correctly display initial worksheet land value', () => {
        const value = 2000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableValues.landValue.should('have.value', `$2,000`);
    });

    it('should correctly display initial worksheet value of improvements', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableValues.valueOfImprovements.should('have.value', `$2,000`);
    });

    it('should have the capital value field disabled', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableValues.capitalValue.should('be.disabled');
    });

    it('should have the land value field disabled', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableValues.landValue.should('be.disabled');
    });

    it('should have the value of improvements field disabled', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableValues.valueOfImprovements.should('be.disabled');;
    });

});

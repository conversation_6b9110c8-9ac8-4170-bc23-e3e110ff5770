import { WorksheetFormMaori as Component } from '@/components/rollMaintenance/ratingValuation/worksheet';
import Model from '../../../model/ValuationWorksheet.js';

describe('WorksheetFormMaori', () => {
    context('Worksheet Values (Unadjusted)', () => {
        it('should recalculate land value when land rows change', () => {
            const ratingValuation = createMockRatingValuation();
            const components = [
                { 'componentType': 'LAND', 'buildingType': null, 'description': 'Land', 'areaInSquareMetres': 100, 'valuePerSquareMetre': 100, 'value': 100000 },
            ];
            ratingValuation.ratingValuationComponents = components;

            const propsData = {
                property: createMockProperty(),
                value: ratingValuation,
            }

            cy.mount(Component, {
                propsData,
            });

            const {
                WorksheetTableValues,
                WorksheetTableLand
            } = Model

            WorksheetTableValues.landValue.should('have.value', '$100,000');

            const row = WorksheetTableLand.row(0);
            row.value.clear().type('150000');
            row.value.blur();

            WorksheetTableValues.landValue.should('have.value', '$150,000');
        });

        it ('should recalculate capital value when building rows change', () => {
            const ratingValuation = createMockRatingValuation();
            const components = [
                { 'componentType': 'PRIMARY_BUILDING', 'buildingType': 'Dwelling', 'description': 'Living', 'areaInSquareMetres': 60, 'valuePerSquareMetre': 2051, 'value': 123060 },
            ];
            ratingValuation.ratingValuationComponents = components;

            const propsData = {
                property: createMockProperty(),
                value: ratingValuation,
            }

            cy.mount(Component, {
                propsData,
            });

            const {
                WorksheetTableValues,
                WorksheetTablePrimaryBuildings
            } = Model

            WorksheetTableValues.capitalValue.should('have.value', '$123,060');

            const row = WorksheetTablePrimaryBuildings.row(0);
            row.value.clear().type('164080');
            row.value.blur();

            WorksheetTableValues.capitalValue.should('have.value', '$164,080');
        });
    })

    context('Unadjusted Value calculations', () => {
        it('should calculate rating values when adjustment values change', () => {
            const ratingValuation = createMockRatingValuation();

            ratingValuation.adoptedValue = {
                capitalValue: 200000,
                landValue: 100000,
                valueOfImprovements: 100000,
            };
            ratingValuation.unadjustedValue = {
                capitalValue: 200000,
                landValue: 100000,
                valueOfImprovements: 100000,
            }

            ratingValuation.lumpSum = 0;

            const propsData = {
                property: createMockProperty(),
                value: ratingValuation,
            }

            cy.mount(Component, {
                propsData,
            });

            const {
                WorksheetTableMaoriLand,
                WorksheetTableMaoriAdoptedValues: { RatingValues}
            } = Model;

            RatingValues.capitalValue.should('have.value', '$200,000');
            RatingValues.landValue.should('have.value', '$100,000');
            RatingValues.valueOfImprovements.should('have.value', '$100,000');


            WorksheetTableMaoriLand.multipleOwnerAdjusted.clear().type('5');
            WorksheetTableMaoriLand.multipleOwnerAdjusted.blur();

            RatingValues.capitalValue.should('have.value', '$190,000');
            RatingValues.landValue.should('have.value', '$95,000');
            RatingValues.valueOfImprovements.should('have.value', '$95,000');
        })

        it('should calculate rating values when unadjusted values change', () => {
            const ratingValuation = createMockRatingValuation();

            ratingValuation.adoptedValue = {
                capitalValue: 200000,
                landValue: 100000,
                valueOfImprovements: 100000,
            };
            ratingValuation.unadjustedValue = {
                capitalValue: 200000,
                landValue: 100000,
                valueOfImprovements: 100000,
            }

            ratingValuation.lumpSum = 0;

            const propsData = {
                property: createMockProperty(),
                value: ratingValuation,
            }

            cy.mount(Component, {
                propsData,
            });

            const {
                WorksheetTableMaoriAdoptedValues: { RatingValues, UnadjustedValues}
            } = Model;

            RatingValues.capitalValue.should('have.value', '$200,000');
            RatingValues.landValue.should('have.value', '$100,000');
            RatingValues.valueOfImprovements.should('have.value', '$100,000');

            UnadjustedValues.capitalValue.clear().type('190000');
            UnadjustedValues.capitalValue.blur();
            UnadjustedValues.landValue.clear().type('80000');
            UnadjustedValues.landValue.blur();

            RatingValues.capitalValue.should('have.value', '$190,000');
            RatingValues.landValue.should('have.value', '$80,000');
            RatingValues.valueOfImprovements.should('have.value', '$110,000');
        })
    })

    context('New Revision Value calculations', () => {
        it('should calculate revision values when maori land revision values change', () => {
            const ratingValuation = createMockRatingValuation();

            ratingValuation.adoptedRevisionValue = {
                capitalValue: 200000,
                landValue: 100000,
                valueOfImprovements: 100000,
            };
            ratingValuation.unadjustedRevisionValue = {
                capitalValue: 200000,
                landValue: 100000,
                valueOfImprovements: 100000,
            }

            ratingValuation.lumpSumRevision = 0;

            const propsData = {
                property: createMockProperty(),
                value: ratingValuation,
            }

            cy.mount(Component, {
                propsData,
            });

            const {
                WorksheetTableMaoriLand,
                WorksheetTableMaoriAdoptedRevisionValues: { RevisionValues}
            } = Model;

            RevisionValues.capitalValue.should('have.value', '$200,000');
            RevisionValues.landValue.should('have.value', '$100,000');
            RevisionValues.valueOfImprovements.should('have.value', '$100,000');


            WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.clear().type('5');
            WorksheetTableMaoriLand.multipleOwnerRevisionAdjusted.blur();

            RevisionValues.capitalValue.should('have.value', '$190,000');
            RevisionValues.landValue.should('have.value', '$95,000');
            RevisionValues.valueOfImprovements.should('have.value', '$95,000');
        })

        it('should calculate revision values when unadjusted revision values change', () => {
            const ratingValuation = createMockRatingValuation();

            ratingValuation.adoptedRevisionValue = {
                capitalValue: 200000,
                landValue: 100000,
                valueOfImprovements: 100000,
            };
            ratingValuation.unadjustedRevisionValue = {
                capitalValue: 200000,
                landValue: 100000,
                valueOfImprovements: 100000,
            }

            ratingValuation.lumpSumRevision = 0;

            const propsData = {
                property: createMockProperty(),
                value: ratingValuation,
            }

            cy.mount(Component, {
                propsData,
            });

            const {
                WorksheetTableMaoriAdoptedRevisionValues: { RevisionValues, UnadjustedValues}
            } = Model;

            RevisionValues.capitalValue.should('have.value', '$200,000');
            RevisionValues.landValue.should('have.value', '$100,000');
            RevisionValues.valueOfImprovements.should('have.value', '$100,000');

            UnadjustedValues.capitalValue.clear().type('190000');
            UnadjustedValues.capitalValue.blur();
            UnadjustedValues.landValue.clear().type('80000');
            UnadjustedValues.landValue.blur();

            RevisionValues.capitalValue.should('have.value', '$190,000');
            RevisionValues.landValue.should('have.value', '$80,000');
            RevisionValues.valueOfImprovements.should('have.value', '$110,000');
        })
    })
});

function createMockProperty() {
    return {
        landUseData: {
            isMaoriLand: true,
        },
        maoriLandData: {
            currentMaoriLandAdjustment: { 'multipleOwnerAdjustmentPercentage': 10, 'siteSignificanceAdjustmentPercentage': null, unadjustedValuation: { 'landValue': 141000, 'capitalValue': 420000, 'treesValue': null } },
            numberOfOwners: null,
            revisedMaoriLandAdjustment: { 'multipleOwnerAdjustmentPercentage': 10, 'siteSignificanceAdjustmentPercentage': null, unadjustedValuation: { 'landValue': 141000, 'capitalValue': 420000, 'treesValue': null } }
        },
    }
}

function createMockRatingValuation() {
    return {
        id: '14b23f81-680c-46ba-b898-92f45478e618',
        workingNetRate: 2051,
        ratingValuationComponents: [{ 'componentType': 'PRIMARY_BUILDING', 'buildingType': 'Dwelling', 'description': 'Living', 'areaInSquareMetres': 60, 'valuePerSquareMetre': 2051, 'value': 123060 }, { 'componentType': 'OTHER_BUILDING', 'buildingType': 'Garage', 'description': 'Garage', 'areaInSquareMetres': 18, 'valuePerSquareMetre': 676.83, 'value': 12183 }, { 'componentType': 'OTHER_IMPROVEMENT', 'buildingType': null, 'description': 'Site Development', 'areaInSquareMetres': null, 'valuePerSquareMetre': null, 'value': 15027 }, { 'componentType': 'LAND', 'buildingType': null, 'description': 'Land', 'areaInSquareMetres': 1012, 'valuePerSquareMetre': 127.4704, 'value': 129000 }],
        originalValue: { 'capitalValue': 398000, 'landValue': 129000, 'valueOfImprovements': 269000 },
        calculatedValue: { 'capitalValue': 279270, 'landValue': 129000, 'valueOfImprovements': 150270 },
        adoptedValue: { 'capitalValue': 200000, 'landValue': 100000, 'valueOfImprovements': 100000 },
        unadjustedValue: { 'capitalValue': 200000, 'landValue': 100000, 'valueOfImprovements': 100000 },
        originalRevisionValue: { 'capitalValue': null, 'landValue': null, 'valueOfImprovements': null },
        adoptedRevisionValue: { 'capitalValue': 200000, 'landValue': 100000, 'valueOfImprovements': 100000 },
        effectiveDate: null,
        isMaoriLand: true,
        maoriLandData: { 'numberOfOwners': 1, 'currentMaoriLandAdjustment': { 'multipleOwnerAdjustmentPercentage': 0, 'siteSignificanceAdjustmentPercentage': 0, 'unadjustedValuation': { 'landValue': 141000, 'capitalValue': 420000, 'treesValue': null } }, 'revisedMaoriLandAdjustment': { 'multipleOwnerAdjustmentPercentage': null, 'siteSignificanceAdjustmentPercentage': null, 'unadjustedValuation': { 'landValue': null, 'capitalValue': null, 'treesValue': null } } },
        unadjustedRevisionValue: null,
        lumpSum: 7000,
        lumpSumRevision: 7000,
        roundingValues: [{ 'from': 0, 'to': 500, 'rounding': 50 }, { 'from': 501, 'to': 1000, 'rounding': 100 }, { 'from': 1001, 'to': 10000, 'rounding': 500 }, { 'from': 10001, 'to': 1000000000, 'rounding': 1000 }],
        valueFromComponents: { 'capitalValue': 279270, 'landValue': 129000, 'valueOfImprovements': 150270 },
        currentTotalAdjustment: 3.5,
        hasRevisionValue: true,
        maoriLandValues: { 'currentMaoriLandAdjustment': { 'multipleOwnerAdjustmentPercentage': null, 'siteSignificanceAdjustmentPercentage': null }, 'numberOfOwners': null, 'revisedMaoriLandAdjustment': { 'multipleOwnerAdjustmentPercentage': null, 'siteSignificanceAdjustmentPercentage': null } },
    };
}

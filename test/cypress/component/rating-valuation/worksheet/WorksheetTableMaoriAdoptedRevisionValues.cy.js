import { WorksheetTableMaoriAdoptedRevisionValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

function createValuesObject(number = 500) {
    return {
        capitalValue: number,
        landValue: number,
        valueOfImprovements: number * 2,
    };
}

const UnadjustedValues = Model.WorksheetTableMaoriAdoptedRevisionValues.UnadjustedValues;
const RevisionValues = Model.WorksheetTableMaoriAdoptedRevisionValues.RevisionValues;

describe('WorksheetTableMaoriAdoptedRevisionValues', () => {

    it('should correctly display unadjusted values', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
        };
        cy.mount(Component, {
            propsData,
        });

        UnadjustedValues.capitalValue.should('have.value', `$1,000`);
        UnadjustedValues.landValue.should('have.value', `$1,000`);
        UnadjustedValues.valueOfImprovements.should('have.value', `$2,000`);
    });

    it('should correctly display revision values', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
        };

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.capitalValue.should('have.value', `$2,000`);
        RevisionValues.landValue.should('have.value', `$2,000`);
        RevisionValues.valueOfImprovements.should('have.value', `$4,000`);
    })

    it('should correctly display lump sum', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
            lumpSum: 4000,
        };

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.lumpSum.should('have.text', `$4,000`);
    })

    it('should correctly display adjustment', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
            adjustment: 4.3,
        };

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.adjustment.should('have.text', `4.3%`);
    })

    it('should emit changed and updated events when unadjusted values change', () => {
        const onChangeSpy = cy.spy().as('onChangeSpy')
        const onUpdateSpy = cy.spy().as('onUpdateSpy');
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
        }
        const listeners = {
            changed: onChangeSpy,
            'update:unadjusted': onUpdateSpy,
        }

        cy.mount(Component, {
            propsData,
            listeners,
        });

        UnadjustedValues.capitalValue.clear().type('2000');
        UnadjustedValues.capitalValue.blur();

        cy.get('@onChangeSpy').should('have.been.called', 1);
        cy.get('@onUpdateSpy').should('have.been.called', 1);

        UnadjustedValues.landValue.clear().type('3000');
        UnadjustedValues.landValue.blur();

        cy.get('@onChangeSpy').should('have.been.called', 2);
        cy.get('@onUpdateSpy').should('have.been.called', 2);
    })

});

import { WorksheetTableMaoriAdoptedValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet';
import Model from '../../../model/ValuationWorksheet.js';

function createValuesObject(number = 500) {
    return {
        capitalValue: number,
        landValue: number,
        valueOfImprovements: number * 2,
    };
}

const UnadjustedValues = Model.WorksheetTableMaoriAdoptedValues.UnadjustedValues;
const RatingValues = Model.WorksheetTableMaoriAdoptedValues.RatingValues;

describe('WorksheetTableMaoriAdoptedValues', () => {

    it('should correctly display unadjusted values', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            rating: createValuesObject(2000),
            lumpSum: 4000,
            adjustment: 4.3,
        };
        cy.mount(Component, {
            propsData,
        });

        UnadjustedValues.capitalValue.should('have.value', `$1,000`);
        UnadjustedValues.landValue.should('have.value', `$1,000`);
        UnadjustedValues.valueOfImprovements.should('have.value', `$2,000`);
    });

    it('should correctly display rating values', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            rating: createValuesObject(2000),
            lumpSum: 4000,
            adjustment: 4.3,
        };

        cy.mount(Component, {
            propsData,
        });

        RatingValues.capitalValue.should('have.value', `$2,000`);
        RatingValues.landValue.should('have.value', `$2,000`);
        RatingValues.valueOfImprovements.should('have.value', `$4,000`);
    })

    it('should correctly display lump sum', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            rating: createValuesObject(2000),
            lumpSum: 4000,
            adjustment: 4.3,
        };

        cy.mount(Component, {
            propsData,
        });

        RatingValues.lumpSum.should('contain.text', `$4,000`);
    })

    it('should correctly display adjustment', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            rating: createValuesObject(2000),
            lumpSum: 4000,
            adjustment: 4.3,
        };

        cy.mount(Component, {
            propsData,
        });

        RatingValues.adjustment.should('have.text', `4.3%`);
    })

    it('should emit changed and updated events when unadjusted values change', () => {
        const onChangeSpy = cy.spy().as('onChangeSpy')
        const onUpdateSpy = cy.spy().as('onUpdateSpy');
        const propsData = {
            unadjusted: createValuesObject(1000),
            rating: createValuesObject(2000),
            lumpSum: 4000,
            adjustment: 4.3,
        }
        const listeners = {
            changed: onChangeSpy,
            'update:unadjusted': onUpdateSpy,
        }

        cy.mount(Component, {
            propsData,
            listeners,
        });

        UnadjustedValues.capitalValue.clear().type('2000');
        UnadjustedValues.capitalValue.blur();

        cy.get('@onChangeSpy').should('have.been.called', 1);
        cy.get('@onUpdateSpy').should('have.been.called', 1);

        UnadjustedValues.landValue.clear().type('3000');
        UnadjustedValues.landValue.blur();

        cy.get('@onChangeSpy').should('have.been.called', 2);
        cy.get('@onUpdateSpy').should('have.been.called', 2);
    })

    it ('should emit changed and updated events when rating values change', () => {
        const onChangeSpy = cy.spy().as('onChangeSpy')
        const onUpdateSpy = cy.spy().as('onUpdateSpy');
        const propsData = {
            unadjusted: createValuesObject(1000),
            rating: createValuesObject(2000),
            lumpSum: 4000,
            adjustment: 4.3,
        }
        const listeners = {
            changed: onChangeSpy,
            'update:rating': onUpdateSpy,
        }

        cy.mount(Component, {
            propsData,
            listeners,
        });

        RatingValues.capitalValue.clear().type('3000');
        RatingValues.capitalValue.blur();

        cy.get('@onChangeSpy').should('have.been.called', 1);
        cy.get('@onUpdateSpy').should('have.been.called', 1);

        RatingValues.landValue.clear().type('4000');
        RatingValues.landValue.blur();

        cy.get('@onChangeSpy').should('have.been.called', 2);
        cy.get('@onUpdateSpy').should('have.been.called', 2);
    })
});

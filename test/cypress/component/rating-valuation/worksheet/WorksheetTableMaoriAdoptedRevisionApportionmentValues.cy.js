import { WorksheetTableMaoriNewRevisionApportionmentValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

const UnadjustedRevisionValues = Model.WorksheetTableMaoriAdoptedRevisionApportionmentValues.UnadjustedRevisionValues;
const RevisionValues = Model.WorksheetTableMaoriAdoptedRevisionApportionmentValues.RevisionValues;

describe('WorksheetTableMaoriAdoptedRevisionApportionmentValues', () => {

    it('should correctly display unadjusted revision apportionment values', () => {
        const propsData = createProps();

        cy.mount(Component, {
            propsData,
        });

        UnadjustedRevisionValues.capitalValue(0).should('have.value', `$11,111`);
        UnadjustedRevisionValues.capitalValue(1).should('have.value', `$333,333`);
        UnadjustedRevisionValues.landValue(0).should('have.value', `$2,222`);
        UnadjustedRevisionValues.landValue(1).should('have.value', `$44,444`);
        UnadjustedRevisionValues.valueOfImprovements(0).should('have.value', `$8,889`);
        UnadjustedRevisionValues.valueOfImprovements(1).should('have.value', `$288,889`);
    });

    it('should correctly display revision apportionment values', () => {
        const propsData = createProps();

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.capitalValue(0).should('have.value', `$100`);
        RevisionValues.capitalValue(1).should('have.value', `$100`);
        RevisionValues.landValue(0).should('have.value', `$100`);
        RevisionValues.landValue(1).should('have.value', `$100`);
        RevisionValues.valueOfImprovements(0).should('have.value', `$0`);
        RevisionValues.valueOfImprovements(1).should('have.value', `$0`);
    })

    it('should correctly display lump sum', () => {
        const propsData = createProps();

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.lumpSum(0).invoke('text').then((text) => {
            expect(text.trim().replace(/\s+/g, ' ')).to.contain('$23,354');
        });

        RevisionValues.lumpSum(1).invoke('text').then((text) => {
            expect(text.trim().replace(/\s+/g, ' ')).to.contain('$467,129');
        });
    })

    it('should correctly display adjustment', () => {
        const propsData = createProps();

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.adjustment(0).invoke('text').then((text) => {
            expect(text.trim().replace(/\s+/g, ' ')).to.contain('7.0%');
        });

        RevisionValues.adjustment(1).invoke('text').then((text) => {
            expect(text.trim().replace(/\s+/g, ' ')).to.contain('7.0%');
        });
    })
});

function createProps() {
    return {
        "parentValue": {
            "unadjusted": {
                "capitalValue": 5555,
                "landValue": 666,
                "valueOfImprovements": 4889
            },
            "revision": {
                "capitalValue": 100,
                "landValue": 100,
                "valueOfImprovements": 0
            },
            "lumpSum": 7000,
            "adjustment": 7
        },
        "apportionmentValues": [
            {
                "qpid": 3241221,
                "suffix": "A",
                "adoptedValue": {
                    "capitalValue": 1473000,
                    "landValue": 520000,
                    "valueOfImprovements": 953000
                },
                "originalRevisionValue": {
                    "capitalValue": 1470000,
                    "landValue": 524000,
                    "valueOfImprovements": 946000
                },
                "adoptedRevisionValue": {
                    "capitalValue": 100,
                    "landValue": 100,
                    "valueOfImprovements": 0
                },
                "adoptedUnadjustedValue": {
                    "capitalValue": 1600000,
                    "landValue": 575000,
                    "valueOfImprovements": 1025000
                },
                "unadjustedRevisionValue": {
                    "capitalValue": 11111,
                    "landValue": 2222,
                    "valueOfImprovements": 8889
                },
                "originalUnadjustedRevisionValue": {
                    "capitalValue": 1600000,
                    "landValue": 575000,
                    "valueOfImprovements": 1025000
                }
            },
            {
                "qpid": 3241222,
                "suffix": "B",
                "adoptedValue": {
                    "capitalValue": 322000,
                    "landValue": 94000,
                    "valueOfImprovements": 228000
                },
                "originalRevisionValue": {
                    "capitalValue": 280000,
                    "landValue": 96000,
                    "valueOfImprovements": 184000
                },
                "adoptedRevisionValue": {
                    "capitalValue": 100,
                    "landValue": 100,
                    "valueOfImprovements": 0
                },
                "adoptedUnadjustedValue": {
                    "capitalValue": 350000,
                    "landValue": 105000,
                    "valueOfImprovements": 245000
                },
                "unadjustedRevisionValue": {
                    "capitalValue": 333333,
                    "landValue": 44444,
                    "valueOfImprovements": 288889
                },
                "originalUnadjustedRevisionValue": {
                    "capitalValue": 350000,
                    "landValue": 105000,
                    "valueOfImprovements": 245000
                }
            }
        ],
        "valuationReference": "30922/49401"
    };
}
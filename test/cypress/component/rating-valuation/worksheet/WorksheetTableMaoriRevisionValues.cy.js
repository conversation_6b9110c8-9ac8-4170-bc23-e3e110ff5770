import { WorksheetTableMaoriRevisionValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

function createValuesObject(number = 500) {
    return {
        capitalValue: number,
        landValue: number,
        valueOfImprovements: number * 2,
    };
}

const UnadjustedValues = Model.WorksheetTableMaoriRevisionValues.UnadjustedValues;
const RevisionValues = Model.WorksheetTableMaoriRevisionValues.RevisionValues;

describe('WorksheetTableMaoriRevisionValues', () => {

    it('should correctly display unadjusted values', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
        };
        cy.mount(Component, {
            propsData,
        });

        UnadjustedValues.capitalValue.should('have.value', `$1,000`);
        UnadjustedValues.landValue.should('have.value', `$1,000`);
        UnadjustedValues.valueOfImprovements.should('have.value', `$2,000`);
    });

    it('should correctly display revision values', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
        };

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.capitalValue.should('have.value', `$2,000`);
        RevisionValues.landValue.should('have.value', `$2,000`);
        RevisionValues.valueOfImprovements.should('have.value', `$4,000`);
    })

    it('should correctly display lump sum', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
            lumpSum: 4000,
        };

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.lumpSum.should('have.text', `$4,000`);
    })

    it('should correctly display adjustment', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
            adjustment: 4.3,
        };

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.adjustment.should('have.text', `4.3%`);
    });

    it('should display readonly unadjusted values', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
        };

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.capitalValue.should('have.value', `$2,000`).should('be.disabled');
        RevisionValues.landValue.should('have.value', `$2,000`).should('be.disabled');
        RevisionValues.valueOfImprovements.should('have.value', `$4,000`).should('be.disabled');
    });

    it('should correctly readonly  revision values', () => {
        const propsData = {
            unadjusted: createValuesObject(1000),
            revision: createValuesObject(2000),
        };

        cy.mount(Component, {
            propsData,
        });

        RevisionValues.capitalValue.should('have.value', `$2,000`).should('be.disabled');
        RevisionValues.landValue.should('have.value', `$2,000`).should('be.disabled');
        RevisionValues.valueOfImprovements.should('have.value', `$4,000`).should('be.disabled');
    });

});

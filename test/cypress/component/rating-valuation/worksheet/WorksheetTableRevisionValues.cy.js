import { WorksheetTableRevisionValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import Model from '../../../model/ValuationWorksheet.js';

function createValuesObject(number = 1000) {
    return {
        value: {
            capitalValue: number,
            landValue: number,
            valueOfImprovements: number*2,
        },
    };
}

describe('WorksheetTableRevisionValues', () => {
    it('should correctly display initial worksheet revision capital value', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableRevisionValues.capitalValue.should('have.value', `$1,000`);
    });

    it('should correctly display initial worksheet revision land value', () => {
        const value = 2000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableRevisionValues.landValue.should('have.value', `$2,000`);
    });

    it('should correctly display initial worksheet revision value of improvements', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableRevisionValues.valueOfImprovements.should('have.value', `$2,000`);
    });

    it('should have the revision capital value field disabled', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableRevisionValues.capitalValue.should('be.disabled');
    });

    it('should have the revision land value field disabled', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableRevisionValues.landValue.should('be.disabled');
    });

    it('should have the revision value of improvements field disabled', () => {
        const value = 1000;
        cy.mount(Component, {
            propsData: createValuesObject(value),
        });
        Model.WorksheetTableRevisionValues.valueOfImprovements.should('be.disabled');;
    });

});

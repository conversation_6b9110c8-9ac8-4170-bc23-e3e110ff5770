import { WorksheetTableAdoptedApportionmentValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';
import { RatingValuation } from '@quotable-value/validation';
import { VALIDATION_CONTEXT } from '@/components/ui/validation/index';
import { computed } from 'vue';

describe('WorksheetTableAdoptedApportionmentValues', () => {

    it('should not have errors when values add correctly', () => {
        const mockPropertyDetail = createMockPropertyDetail();
        const mockRatingValuation = createMockRatingValuation();
        const mockActivities = createMockActivities();
        const propsData = {
            value: [{
                suffix: "A",
                qpid: "123",
                adoptedValue: {
                    capitalValue: 500000,
                    landValue: 300000,
                    valueOfImprovements: 200000,
                },
            }, {
                suffix: "B",
                qpid: "124",
                adoptedValue: {
                    capitalValue: 620000,
                    landValue: 450000,
                    valueOfImprovements: 170000,
                },
            }],
            valuationReference: "1111/22",
            parentValue: {
                capitalValue: 1120000,
                landValue: 750000,
                valueOfImprovements: 370000,
            }
        };

        mockRatingValuation.apportionmentValues = propsData.value;
        mockRatingValuation.adoptedValue = propsData.parentValue;

        const validationSet = RatingValuation.validateRatingValuationJob(
            mockRatingValuation, mockPropertyDetail, mockActivities, true, false
        );

        cy.mount(Component, {
            propsData: propsData,
            provide: {
                [VALIDATION_CONTEXT]: computed(() => validationSet),
            }
        });

        cy.get(':nth-child(2) > .qv-validation-wrapper > .qv-alert-warning')
            .should('not.exist');

        cy.get(':nth-child(3) > .qv-validation-wrapper > .qv-alert-warning')
            .should('not.exist');
    });

    it('should show error to add CV when its not enough', () => {
        const mockPropertyDetail = createMockPropertyDetail();
        const mockRatingValuation = createMockRatingValuation();
        const mockActivities = createMockActivities();
        const propsData = {
            value: [{
                suffix: "A",
                qpid: "123",
                adoptedValue: {
                    capitalValue: 400000,
                    landValue: 300000,
                    valueOfImprovements: 100000,
                },
            }, {
                suffix: "B",
                qpid: "124",
                adoptedValue: {
                    capitalValue: 620000,
                    landValue: 450000,
                    valueOfImprovements: 170000,
                },
            }],
            valuationReference: "1111/22",
            parentValue: {
                capitalValue: 1120000,
                landValue: 750000,
                valueOfImprovements: 370000,
            }
        };

        mockRatingValuation.apportionmentValues = propsData.value;
        mockRatingValuation.adoptedValue = propsData.parentValue;

        const validationSet = RatingValuation.validateRatingValuationJob(
            mockRatingValuation, mockPropertyDetail, mockActivities, true, false
        );

        cy.mount(Component, {
            propsData: propsData,
            provide: {
                [VALIDATION_CONTEXT]: computed(() => validationSet),
            }
        });

        cy.get(':nth-child(2) > .qv-validation-wrapper > .qv-alert-warning')
            .should('contain.text', 'Add $100,000');

        cy.get(':nth-child(3) > .qv-validation-wrapper > .qv-alert-warning')
            .should('not.exist');
    });

    it('should show error to subtract CV when its too much', () => {
        const mockPropertyDetail = createMockPropertyDetail();
        const mockRatingValuation = createMockRatingValuation();
        const mockActivities = createMockActivities();
        const propsData = {
            value: [{
                suffix: "A",
                qpid: "123",
                adoptedValue: {
                    capitalValue: 550000,
                    landValue: 300000,
                    valueOfImprovements: 250000,
                },
            }, {
                suffix: "B",
                qpid: "124",
                adoptedValue: {
                    capitalValue: 620000,
                    landValue: 450000,
                    valueOfImprovements: 170000,
                },
            }],
            valuationReference: "1111/22",
            parentValue: {
                capitalValue: 1120000,
                landValue: 750000,
                valueOfImprovements: 370000,
            }
        };

        mockRatingValuation.apportionmentValues = propsData.value;
        mockRatingValuation.adoptedValue = propsData.parentValue;

        const validationSet = RatingValuation.validateRatingValuationJob(
            mockRatingValuation, mockPropertyDetail, mockActivities, true, false
        );

        cy.mount(Component, {
            propsData: propsData,
            provide: {
                [VALIDATION_CONTEXT]: computed(() => validationSet),
            }
        });

        cy.get(':nth-child(2) > .qv-validation-wrapper > .qv-alert-warning')
            .should('contain.text', 'Subtract $50,000');

        cy.get(':nth-child(3) > .qv-validation-wrapper > .qv-alert-warning')
            .should('not.exist');
    });

    it('should show error to add LV when its not enough', () => {
        const mockPropertyDetail = createMockPropertyDetail();
        const mockRatingValuation = createMockRatingValuation();
        const mockActivities = createMockActivities();
        const propsData = {
            value: [{
                suffix: "A",
                qpid: "123",
                adoptedValue: {
                    capitalValue: 500000,
                    landValue: 300000,
                    valueOfImprovements: 200000,
                },
            }, {
                suffix: "B",
                qpid: "124",
                adoptedValue: {
                    capitalValue: 620000,
                    landValue: 350000,
                    valueOfImprovements: 270000,
                },
            }],
            valuationReference: "1111/22",
            parentValue: {
                capitalValue: 1120000,
                landValue: 750000,
                valueOfImprovements: 370000,
            }
        };

        mockRatingValuation.apportionmentValues = propsData.value;
        mockRatingValuation.adoptedValue = propsData.parentValue;

        const validationSet = RatingValuation.validateRatingValuationJob(
            mockRatingValuation, mockPropertyDetail, mockActivities, true, false
        );

        cy.mount(Component, {
            propsData: propsData,
            provide: {
                [VALIDATION_CONTEXT]: computed(() => validationSet),
            }
        });

        cy.get(':nth-child(2) > .qv-validation-wrapper > .qv-alert-warning')
            .should('not.exist');

        cy.get(':nth-child(3) > .qv-validation-wrapper > .qv-alert-warning')
            .should('contain.text', 'Add $100,000');
    });

    it('should show error to subtract LV when its too much', () => {
        const mockPropertyDetail = createMockPropertyDetail();
        const mockRatingValuation = createMockRatingValuation();
        const mockActivities = createMockActivities();
        const propsData = {
            value: [{
                suffix: "A",
                qpid: "123",
                adoptedValue: {
                    capitalValue: 500000,
                    landValue: 350000,
                    valueOfImprovements: 150000,
                },
            }, {
                suffix: "B",
                qpid: "124",
                adoptedValue: {
                    capitalValue: 620000,
                    landValue: 450000,
                    valueOfImprovements: 170000,
                },
            }],
            valuationReference: "1111/22",
            parentValue: {
                capitalValue: 1120000,
                landValue: 750000,
                valueOfImprovements: 370000,
            }
        };

        mockRatingValuation.apportionmentValues = propsData.value;
        mockRatingValuation.adoptedValue = propsData.parentValue;

        const validationSet = RatingValuation.validateRatingValuationJob(
            mockRatingValuation, mockPropertyDetail, mockActivities, true, false
        );

        cy.mount(Component, {
            propsData: propsData,
            provide: {
                [VALIDATION_CONTEXT]: computed(() => validationSet),
            }
        });

        cy.get(':nth-child(2) > .qv-validation-wrapper > .qv-alert-warning')
            .should('not.exist');

        cy.get(':nth-child(3) > .qv-validation-wrapper > .qv-alert-warning')
            .should('contain.text', 'Subtract $50,000');
    });

});

function createMockActivities() {
    return [
        {
            "id": "BC-2057243",
            "activityType": {
                "category": "RollMaintenanceActivityType",
                "code": "BC",
                "description": "Building Consent",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null
            },
            "ratingUnit": {
                "qpid": 184621,
                "rollNumber": 1990,
                "assessmentNumber": 7301,
                "suffix": "B",
                "valuationReference": "1990/7301 B",
                "assessmentStatus": "A"
            },
            "territorialAuthority": {
                "code": 7,
                "name": "Auckland City"
            },
            "valuer": {
                "externalId": null,
                "name": "Default Consent Valuer",
                "ntUsername": "QVNZ\\ConsentValuer"
            },
            "initiatedDate": "2020-12-02",
            "actionedDate": null,
            "rollUpdatedDate": null,
            "status": {
                "category": "RollMaintenanceActivityStatus",
                "code": "READY",
                "description": "Ready to Value",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null
            },
            "createdDateTime": "2020-12-03T07:46:09.85Z",
            "lastUpdatedDateTime": "2023-11-08T23:41:27.697Z",
            "estimatedInspectionReadyDate": "2023-11-09",
            "inspectionState": null,
            "noAddedValue": false,
            "buildingConsent": {
                "qpid": 184621,
                "consentNumber": "BCO10315586",
                "consentDate": "2020-12-02",
                "applicant": "Yuqing Karen Zheng, Ivan Miskovic",
                "streetNumber": "2/10",
                "streetName": "2/10 Walpole Street Ellerslie Auckland    1051",
                "description": "RBW - Conversion of internal access garage to bedroom. Wall between current garage and kitchen to be",
                "cost": 3.5E+4,
                "totalFloorArea": null,
                "complianceCertificateIssued": true,
                "plansRequired": false,
                "plansObtained": true,
                "plansNotRequired": false,
                "plansUnavailable": false,
                "plansRequestedWithTa": false,
                "natureOfWorks": null,
                "constructionCompletionDate": "2023-11-09",
                "status": {
                    "category": "ConsentStatus",
                    "code": "OVERDUE",
                    "description": "OVERDUE",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null
                },
                "streetAddress": "2/10 2/10 Walpole Street Ellerslie Auckland    1051",
                "constructionComplete": true
            },
            "notes": null,
            "setupComplete": true,
            "needsMoreInformation": false,
            "hasMonarchValuation": false,
            "needsInspection": false,
            "hasActiveConsent": true,
            "hasActiveObjection": false,
            "activeConsentIds": [
                "BC-2057243"
            ],
            "activeObjectionIds": [],
            "hasActiveSubDivision": false,
            "description": "RBW - Conversion of internal access garage to bedroom. Wall between current garage and kitchen to be",
            "property": {
                "id": "9c5a8271-d419-45f1-8222-f681c4fc855a",
                "address": {
                    "streetNumber": 10,
                    "streetNumberSuffix": null,
                    "streetName": "Walpole",
                    "streetType": {
                        "category": "StreetType",
                        "code": "St",
                        "description": "Street",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "suburb": null,
                    "town": "Ellerslie",
                    "streetAddress": "10 Walpole Street"
                },
                "territorialAuthority": {
                    "code": 7,
                    "name": "Auckland City"
                },
                "apportionmentCode": "0",
                "assessmentStatus": "A",
                "category": {
                    "category": "Category",
                    "code": "RF196B",
                    "description": "Residential-OYO-1960's-average",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": {
                        "category": "CategoryGroup",
                        "code": "U15",
                        "description": "Residential Flats",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "externalCode": null
                },
                "hasSameRollAndAssessment": false,
                "hasRatingRW": false
            },
            "ratingValuation": {
                "id": "c6173d91-6128-4d37-8173-1eb0115ecefa",
                "status": "IN_PROGRESS",
                "qpid": 184621
            }
        }
    ];
}

function createMockRatingValuation() {
    return {
        "id": "c6173d91-6128-4d37-8173-1eb0115ecefa",
        "status": "IN_PROGRESS",
        "ratingUnit": {
            "propertyId": "9c5a8271-d419-45f1-8222-f681c4fc855a",
            "qpid": 184621
        },
        "rollMaintenanceActivityIds": [
            "BC-2057243"
        ],
        "propertyDetailId": "c70a7d46-ea01-4462-ab6a-886435bd8000",
        "valuer": {
            "externalId": "QVNZ-WrightG",
            "name": "Grace Wright",
            "ntUsername": "QVNZ\\WrightG"
        },
        "comparableProperties": [],
        "comparablePropertySnapshots": [],
        "workingNetRate": 3505,
        "ratingValuationComponents": [
            {
                "componentType": "PRIMARY_BUILDING",
                "buildingType": "Flat - Single",
                "description": "Living",
                "areaInSquareMetres": 83,
                "valuePerSquareMetre": 3505,
                "value": 290915
            },
            {
                "componentType": "PRIMARY_BUILDING",
                "buildingType": "Flat - Single",
                "description": "Garage",
                "areaInSquareMetres": 20,
                "valuePerSquareMetre": 2278.25,
                "value": 45565
            },
            {
                "componentType": "OTHER_IMPROVEMENT",
                "buildingType": null,
                "description": "Site Development",
                "areaInSquareMetres": null,
                "valuePerSquareMetre": null,
                "value": 32129
            },
            {
                "componentType": "OTHER_IMPROVEMENT",
                "buildingType": null,
                "description": "Deck - Uncovered",
                "areaInSquareMetres": null,
                "valuePerSquareMetre": null,
                "value": 5258
            },
            {
                "componentType": "LAND",
                "buildingType": null,
                "description": "Land",
                "areaInSquareMetres": null,
                "valuePerSquareMetre": null,
                "value": 7.5E+5
            }
        ],
        "originalValue": {
            "capitalValue": 1.2E+6,
            "landValue": 7.5E+5,
            "valueOfImprovements": 4.5E+5
        },
        "calculatedValue": {
            "capitalValue": 1123867,
            "landValue": 7.5E+5,
            "valueOfImprovements": 373867
        },
        "adoptedValue": {
            "capitalValue": 1.12E+6,
            "landValue": 7.5E+5,
            "valueOfImprovements": 3.7E+5
        },
        "originalRevisionValue": {
            "capitalValue": null,
            "landValue": null,
            "valueOfImprovements": null
        },
        "adoptedRevisionValue": null,
        "effectiveDate": null,
        "summaryOfChanges": "There have been no changes to the core property data as part of this valuation.",
        "entityVersion": 181,
        "isMaoriLand": null,
        "maoriLandData": null,
        "unadjustedValue": null,
        "unadjustedRevisionValue": null,
        "lumpSum": null,
        "lumpSumRevision": null,
        "roundingValues": null,
        "apportionmentValues": null,
        "valueFromComponents": {
            "capitalValue": 1123867,
            "landValue": 7.5E+5,
            "valueOfImprovements": 373867
        },
        "hasRevisionValue": false,
        "currentTotalAdjustment": 0
    };
}

function createMockPropertyDetail() {
    return {
        "id": "c70a7d46-ea01-4462-ab6a-886435bd8000",
        "propertyId": "9c5a8271-d419-45f1-8222-f681c4fc855a",
        "qpid": 184621,
        "status": "PENDING",
        "category": {
            "category": "Category_DVR",
            "code": "RF196B",
            "description": "Residential-OYO-1960's-average",
            "shortDescription": null,
            "isActive": true,
            "parentClassification": {
                "category": "CategoryGroup",
                "code": "U15",
                "description": "Residential Flats",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "externalCode": null
        },
        "grouping": null,
        "qvCategory": {
            "category": "QV_Category",
            "code": "RF196B ",
            "description": "RF196B  Residential-OYO-1960's-average",
            "shortDescription": null,
            "isActive": true,
            "parentClassification": null,
            "externalCode": null
        },
        "planNumber": null,
        "propertyName": null,
        "description": "A 103 square metre three bedroom 1960's brick flat in average condition which is located on a level site with no appreciable view.\nThe property includes one toilet, under main roof garaging for one vehicle and decking.",
        "natureOfImprovements": [
            {
                "quantity": 1,
                "improvement": {
                    "category": "NatureOfImprovements_DVR",
                    "code": "FLAT",
                    "description": "Flat",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                }
            },
            {
                "quantity": 1,
                "improvement": {
                    "category": "NatureOfImprovements_DVR",
                    "code": "OI",
                    "description": "Other Improvements",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                }
            }
        ],
        "isOutlier": null,
        "site": {
            "landArea": 0,
            "effectiveLandArea": null,
            "lotPosition": null,
            "contour": {
                "category": "Contour_DVR",
                "code": "LV",
                "description": "Level",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "view": {
                "category": "View_DVR",
                "code": "N",
                "description": "No appreciable view",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "viewScope": {
                "category": "ViewScope_DVR",
                "code": "N",
                "description": "None",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "hasDriveway": null,
            "hasCarAccess": null,
            "carparks": 0,
            "classOfSurroundingImprovements": null,
            "siteDevelopment": {
                "type": "OtherImprovement",
                "definition": null,
                "age": null,
                "quantity": null,
                "unitOfMeasure": null,
                "quality": null,
                "description": null,
                "inferred": null,
                "collectionDate": null
            }
        },
        "landUse": {
            "landZone": {
                "category": "TA_7_LandZone_DVR",
                "code": "9C",
                "description": "9C",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "landUse": {
                "category": "LandUse_DVR",
                "code": "91",
                "description": "Single Unit excluding Bach",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "isMaoriLand": false,
            "production": null
        },
        "summary": {
            "houseType": null,
            "units": 1,
            "totalBedrooms": 3,
            "totalBathrooms": null,
            "totalToilets": 1,
            "buildingSiteCover": 103,
            "totalFloorArea": 103,
            "totalLivingArea": 83,
            "mainLivingArea": null,
            "age": {
                "category": "Age_DVR",
                "code": "1960",
                "description": "1960-69",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "effectiveYearBuilt": null,
            "hasPoorFoundations": null,
            "hasLaundryOrWorkshop": false,
            "actualRentPerWeek": null,
            "actualRentKnownDate": null
        },
        "ruralDetail": {
            "qualityRating": null,
            "farmedWith": [],
            "irrigationSourceConsents": [],
            "irrigationTypeConsents": [],
            "waterQualityRating": null,
            "waterStorageType": null,
            "waterStorageSize": 0,
            "irrigationLinkedWith": [],
            "nutrientManagementConsents": [],
            "fences": [],
            "fruit": []
        },
        "commercialDetail": null,
        "buildings": [
            {
                "buildingLabel": "FS1",
                "isPrimaryBuilding": true,
                "buildingType": {
                    "category": "BuildingType",
                    "code": "FS",
                    "description": "Flat - Single",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": "FLAT"
                },
                "description": null,
                "numberOfStoreys": 1,
                "otherFeatures": null,
                "yearBuilt": null,
                "floorArea": 103,
                "floorConstruction": {
                    "type": "ComplexFeature",
                    "definition": [
                        {
                            "category": "FloorConstruction_DVR",
                            "code": "503",
                            "description": "Timber",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        }
                    ],
                    "age": null,
                    "quality": null,
                    "inferred": null,
                    "collectionDate": null
                },
                "foundation": {
                    "type": "ComplexFeature",
                    "definition": [
                        {
                            "category": "Foundation_DVR",
                            "code": "203",
                            "description": "Concrete Pile",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        }
                    ],
                    "age": null,
                    "quality": null,
                    "inferred": null,
                    "collectionDate": null
                },
                "wallConstruction": {
                    "type": "ComplexFeature",
                    "definition": [
                        {
                            "category": "WallConstruction_DVR",
                            "code": "301",
                            "description": "Brick",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": "B"
                        }
                    ],
                    "age": null,
                    "quality": {
                        "category": "FeatureQuality",
                        "code": "A",
                        "description": "Average",
                        "shortDescription": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": "A"
                    },
                    "inferred": null,
                    "collectionDate": null
                },
                "roofConstruction": {
                    "type": "ComplexFeature",
                    "definition": [
                        {
                            "category": "RoofConstruction_DVR",
                            "code": "410",
                            "description": "Tile Profile",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": "T"
                        }
                    ],
                    "age": null,
                    "quality": {
                        "category": "FeatureQuality",
                        "code": "A",
                        "description": "Average",
                        "shortDescription": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": "A"
                    },
                    "inferred": null,
                    "collectionDate": null
                },
                "glazing": null,
                "insulation": null,
                "plumbing": null,
                "wiring": null,
                "spaces": [
                    {
                        "type": "LivingSpace",
                        "spaceType": {
                            "category": "SpaceType",
                            "code": "LI",
                            "description": "Living",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        },
                        "spaceLabel": null,
                        "numberOfSimilarSpaces": 1,
                        "floorArea": 83,
                        "quality": null,
                        "modernisationAge": {
                            "category": "ModernisationAge",
                            "code": "2010",
                            "description": "2010-14",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": "2010"
                        },
                        "heating": null,
                        "totalBedrooms": 3,
                        "singleBedrooms": null,
                        "doubleBedrooms": null,
                        "homeOfficeOrStudy": null,
                        "kitchen": {
                            "type": "Room",
                            "age": null,
                            "quality": null,
                            "description": null,
                            "hasRoom": null,
                            "inferred": null,
                            "collectionDate": null
                        },
                        "totalBathrooms": null,
                        "totalToilets": 1,
                        "mainBathroom": {
                            "type": "Room",
                            "age": null,
                            "quality": null,
                            "description": null,
                            "hasRoom": null,
                            "inferred": null,
                            "collectionDate": null
                        },
                        "ensuite": {
                            "type": "Room",
                            "age": null,
                            "quality": null,
                            "description": null,
                            "hasRoom": null,
                            "inferred": null,
                            "collectionDate": null
                        }
                    },
                    {
                        "type": "GarageSpace",
                        "spaceType": {
                            "category": "SpaceType",
                            "code": "GA",
                            "description": "Garage",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        },
                        "spaceLabel": null,
                        "numberOfSimilarSpaces": null,
                        "floorArea": 20,
                        "quality": {
                            "category": "FeatureQuality",
                            "code": "A",
                            "description": "Average",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": "A"
                        },
                        "numberOfCarparks": 1,
                        "garageFeatures": null
                    }
                ]
            }
        ],
        "otherImprovements": [
            {
                "type": "OtherImprovement",
                "definition": {
                    "category": "OtherImprovement",
                    "code": "DU",
                    "description": "Deck - Uncovered",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": "DECK"
                },
                "age": null,
                "quantity": null,
                "unitOfMeasure": null,
                "quality": null,
                "description": "Timber deck",
                "inferred": null,
                "collectionDate": null
            }
        ],
        "asAtDateTime": null,
        "audit": {
            "lastUpdatedDateTime": "2025-02-19T01:30:44.361Z",
            "lastUpdatedUser": null,
            "outputCode": {
                "category": "OutputCode_DVR",
                "code": "6",
                "description": "Change to Improvements",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "source": {
                "category": "Source_DVR",
                "code": "B/P",
                "description": "Building permit from the TA",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "reasonForChange": "Building Consent BCO10315586 Alterations"
        },
        "isStale": false,
        "propertyNotes": {
            "text": null,
            "enteredDate": null,
            "updateReason": null
        },
        "ratingApportionments": [
            {
                "qpid": 3349446,
                "assessmentId": 39796,
                "rollNumber": 411,
                "assessmentNumber": 13900,
                "suffix": "A",
                "category": {
                    "category": "Category_DVR",
                    "code": "OP",
                    "description": "Other-Passive Reserve",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": {
                        "category": "CategoryGroup",
                        "code": "X10",
                        "description": "Other",
                        "shortDescription": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "externalCode": null
                },
                "natureOfImprovements": [],
                "landZone": {
                    "category": "TA_1_LandZone_DVR",
                    "code": "9D",
                    "description": "9D",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "landUse": {
                    "category": "LandUse_DVR",
                    "code": "55",
                    "description": "Passive outdoor",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "units": 1,
                "age": null,
                "totalFloorArea": 0,
                "buildingSiteCover": 0,
                "roofCondition": null,
                "wallCondition": null,
                "roofConstruction": null,
                "wallConstruction": null,
                "landArea": 0.18,
                "carparks": 0
            },
            {
                "qpid": 3349447,
                "assessmentId": 39796,
                "rollNumber": 411,
                "assessmentNumber": 13900,
                "suffix": "B",
                "category": {
                    "category": "Category_DVR",
                    "code": "RD199C",
                    "description": "Residential-Dwelling-1990's-poor",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": {
                        "category": "CategoryGroup",
                        "code": "U10",
                        "description": "Residential Dwellings",
                        "shortDescription": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "externalCode": null
                },
                "natureOfImprovements": [
                    {
                        "quantity": 1,
                        "improvement": {
                            "category": "NatureOfImprovements_DVR",
                            "code": "DWG",
                            "description": "Dwelling",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        }
                    },
                    {
                        "quantity": 1,
                        "improvement": {
                            "category": "NatureOfImprovements_DVR",
                            "code": "OI",
                            "description": "Other Improvements",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        }
                    }
                ],
                "landZone": {
                    "category": "TA_1_LandZone_DVR",
                    "code": "9D",
                    "description": "9D",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "landUse": {
                    "category": "LandUse_DVR",
                    "code": "91",
                    "description": "Single Unit excluding Bach",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "units": 1,
                "age": null,
                "totalFloorArea": 100,
                "buildingSiteCover": 60,
                "roofCondition": {
                    "category": "RoofCondition_DVR",
                    "code": "A",
                    "description": "Average",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "wallCondition": {
                    "category": "WallCondition_DVR",
                    "code": "A",
                    "description": "Average",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "roofConstruction": {
                    "category": "RoofConstruction_DVR",
                    "code": "I",
                    "description": "Steel/G-Iron",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "wallConstruction": null,
                "landArea": 0.068,
                "carparks": 0
            }
        ],
        "entityVersion": 29,
        "qivsImprovementsStatus": "USEFUL_IMPROVEMENTS",
        "dvrSnapshot": {
            "numberOfUnderMainRoofGarages": 1,
            "numberOfFreestandingGarages": 0,
            "hasDeck": true,
            "hasLargeOtherImprovements": false,
            "wallConstruction": {
                "category": "BuildingConstruction",
                "code": "B",
                "description": "Brick",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "wallCondition": {
                "category": "BuildingCondition",
                "code": "A",
                "description": "Average",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "roofConstruction": {
                "category": "BuildingConstruction",
                "code": "T",
                "description": "Tile Profile",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "roofCondition": {
                "category": "BuildingCondition",
                "code": "A",
                "description": "Average",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "isModernised": true,
            "natureOfImprovements": "FLAT OI",
            "landscapingQuality": null,
            "changedDvrFields": [
                "isModernised"
            ]
        }
    };
}

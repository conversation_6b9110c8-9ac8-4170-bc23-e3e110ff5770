import { WorksheetTableSraValues as Component } from '@/components/rollMaintenance/ratingValuation/worksheet/index.js';

describe('WorksheetTableSraValues', () => {

    it('should correctly handle SRA worksheet', () => {
        cy.mount(Component, {
            propsData: {
                value: [
                    {
                        "ratingAuthority": {
                            "name": "NATURAL GAS CORPORATION ",
                        },
                        "area": 680.6155,
                        "capitalValue": 9080000,
                        "landValue": 7960000,
                        "valueOfImprovements": 1120000,
                    },
                    {
                        "ratingAuthority": {
                            "name": "SUPERNATURAL GAS CORPORATION ",
                        },
                        "area": 688.1912,
                        "capitalValue": 11820000,
                        "landValue": 10340000,
                        "valueOfImprovements": 1480000,
                    }
                ],
            },
        });

        cy.get('[data-cy="sra-area-0"]').should('have.value', 680.6155);
        cy.get('[data-cy="sra-area-1"]').should('have.value', 688.1912);

        cy.get('[data-cy="sra-capital-value-0"]').should('have.value', '$9,080,000');
        cy.get('[data-cy="sra-capital-value-1"]').should('have.value', '$11,820,000');

        cy.get('[data-cy="sra-land-value-0"]').should('have.value', '$7,960,000');
        cy.get('[data-cy="sra-land-value-1"]').should('have.value', '$10,340,000');

        cy.get('[data-cy="sra-capital-value-1"]').clear();
        cy.get('[data-cy="sra-capital-value-1"]').type("12000000");
        cy.get('[data-cy="sra-capital-value-1"]').blur();

        cy.get('[data-cy="sra-value-of-improvements-1"]').should('have.value', '$1,660,000');
    });

    it('should correctly handle SRA with revision worksheet', () => {
        cy.mount(Component, {
            propsData: {
                value: [
                    {
                        "ratingAuthority": {
                            "name": "NATURAL GAS CORPORATION ",
                        },
                        "area": 680.6155,
                        "capitalValue": 9080000,
                        "landValue": 7960000,
                        "valueOfImprovements": 1120000,
                        "revision": {
                            "capitalValue": 9090000,
                            "landValue": 7970000,
                            "valueOfImprovements": 1120000,
                        },
                    },
                    {
                        "ratingAuthority": {
                            "name": "SUPERNATURAL GAS CORPORATION ",
                        },
                        "area": 688.1912,
                        "capitalValue": 11820000,
                        "landValue": 10340000,
                        "valueOfImprovements": 1480000,
                        "revision": {
                            "capitalValue": 11830000,
                            "landValue": 10350000,
                            "valueOfImprovements": 1480000,
                        },
                    }
                ],
                hasSraRevision: true,
            },
        });

        cy.get('[data-cy="sra-area-0"]').should('have.value', 680.6155);
        cy.get('[data-cy="sra-area-1"]').should('have.value', 688.1912);

        cy.get('[data-cy="sra-capital-value-0"]').should('have.value', '$9,080,000');
        cy.get('[data-cy="sra-capital-value-1"]').should('have.value', '$11,820,000');

        cy.get('[data-cy="sra-land-value-0"]').should('have.value', '$7,960,000');
        cy.get('[data-cy="sra-land-value-1"]').should('have.value', '$10,340,000');

        cy.get('[data-cy="sra-capital-value-1"]').clear();
        cy.get('[data-cy="sra-capital-value-1"]').type("12000000");
        cy.get('[data-cy="sra-capital-value-1"]').blur();

        cy.get('[data-cy="sra-value-of-improvements-1"]').should('have.value', '$1,660,000');
    });

});

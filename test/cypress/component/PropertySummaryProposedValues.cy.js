import { default as Component } from '../../../vue/components/property/PropertySummaryProposedValues.vue';
import { default as Model } from '../model/PropertySummaryProposedValues.js';

function createValuesObject(number) {
    return {
        capitalValue: number,
        landValue: number,
        valueOfImprovements: number,
    };
}

describe('PropertySummaryProposedValues', () => {
    it('shows proposed values when provided', () => {
        const propsData = {
            proposedValues: createValuesObject(100),
        };

        cy.mount(Component, {
            propsData,
        });

        console.log(Model.elements)
        Model.elements.proposedCV
            .should('exist')
            .find('span')
            .should('have.text', '100');
        Model.elements.proposedLV
            .should('exist')
            .find('span')
            .should('have.text', '100');
        Model.elements.proposedVI
            .should('exist')
            .find('span')
            .should('have.text', '100');

        Model.elements.unadjustedValuesSection.should('not.exist');
    });

    it('shows unadjusted values when provided', () => {
        const propsData = {
            proposedValues: createValuesObject(100),
            unadjustedValues: createValuesObject(100),
        };

        cy.mount(Component, {
            propsData,
        });

        Model.elements.proposedUCV
            .should('exist')
            .find('span')
            .should('have.text', '100');
        Model.elements.proposedULV
            .should('exist')
            .find('span')
            .should('have.text', '100');
        Model.elements.proposedUVI
            .should('exist')
            .find('span')
            .should('have.text', '100');

        Model.elements.unadjustedValuesSection.should('exist');
    });

    it ('hides the unadjusted values when they are not provided', () => {
        const propsData = {
            proposedValues: createValuesObject(100),
        }

        cy.mount(Component, {
            propsData,
        });

        Model.elements.unadjustedValuesSection.should('not.exist');
    })

    it('highlights the proposed values when they are less than the current values', () => {
        const propsData = {
            proposedValues: createValuesObject(100),
            currentValues: createValuesObject(200),
        }

        cy.mount(Component, {
            propsData,
        });

        Model.elements.proposedCV
            .should('exist')
            .should('have.class', 'qv-bg-red')
            .find('span')
            .should('have.text', '100')

        Model.elements.proposedLV
            .should('exist')
            .should('have.class', 'qv-bg-red')
            .find('span')
            .should('have.text', '100')
    });

    it('highlights the unadjusted values when they are less than the current values', () => {
        const propsData = {
            proposedValues: createValuesObject(200),
            unadjustedValues: createValuesObject(100),
            currentUnadjustedValues: createValuesObject(300),
        }

        cy.mount(Component, {
            propsData,
        });

        Model.elements.proposedUCV
            .should('exist')
            .should('have.class', 'qv-bg-red')
            .find('span')
            .should('have.text', '100')

        Model.elements.proposedULV
            .should('exist')
            .should('have.class', 'qv-bg-red')
            .find('span')
            .should('have.text', '100')
    });

    it('shows the working net rate when provided', () => {
        const propsData = {
            proposedValues: createValuesObject(100),
            workingNetRate: 1.2,
        }

        cy.mount(Component, {
            propsData,
        });

        Model.elements.proposedNetRate
            .should('exist')
            .find('span')
            .first()
            .should('have.text', '1.2');
    });
});

import PercentChange from '../../../vue/components/rollMaintenance/commercialWorksheet/PercentChange.vue';

describe('PercentChange.cy.js', () => {
    it('shows value on green badge when value is positive', () => {
        const propsData = {
            showPercentChange: true,
            value: 10,
        };
        cy.mount(PercentChange, {
            propsData
        });
        cy.get('[data-cy=pc-value]').should('exist');
        cy.get('[data-cy=pc-value]').should('have.class', 'positive');
    });

    it('shows value on red badge when value is negative', () => {
        const propsData = {
            showPercentChange: true,
            value: -10,
        };

        cy.mount(PercentChange, {
            propsData
        });
        cy.get('[data-cy=pc-value]').should('exist');
        cy.get('[data-cy=pc-value]').should('have.class', 'negative');
    });

    it('shows value on grey badge when value is 0', () => {
        const propsData = {
            showPercentChange: true,
            value: 0,
        };

        cy.mount(PercentChange, {
            propsData
        });
        cy.get('[data-cy=pc-value]').should('exist');
        cy.get('[data-cy=pc-value]').should('not.have.class', 'positive');
        cy.get('[data-cy=pc-value]').should('not.have.class', 'negative');
    });
});

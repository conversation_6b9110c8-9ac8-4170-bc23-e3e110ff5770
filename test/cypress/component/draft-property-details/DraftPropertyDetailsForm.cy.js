import DraftPropertyForm from '@/components/propertyDetails/residential/DraftPropertyForm.vue';
import Model from '../..//model/DraftPropertyDetails.js';
import { ref } from 'vue';
import { createMockStore } from '../../support/store.js';

describe('DraftPropertyForm', () => {
    context('General Property Information', () => {
        beforeEach(() => {
            const store = createMockStore();
            let propsData = {
                propertyDetail: createMockPropertyDetail(),
                property: createMockProperty(),
                currentPropertyDetail: createMockCurrentPropertyDetail(),
                validations: createMockValidations(),
                highlight: false,
                disabled: false,
            };
            store.commit('classificationsLoaded', true);
            const onUpdate = cy.spy().as('onUpdate');
            cy.mount(DraftPropertyForm, {
                propsData,
                emitSpy: true,
                store,
                listeners: {
                    'update:property-detail': onUpdate,
                },
            });
        });

        it('should update the general property details when updated', () => {
            Model.GeneralPropertyInformation.propertyName.type('--Property Name--');
            cy.get('@onUpdate').should('have.been.called', 1);
            Model.GeneralPropertyInformation.propertyName.find('.qv-form-input').should('have.value', '--Property Name--');
        });

        it('should update the location details when updated', () => {
            Model.LocationDetails.classOfSurroundingImprovements.find('.multiselect').click();
            Model.LocationDetails.classOfSurroundingImprovements.find('.multiselect__element').first().click();
            cy.get('@onUpdate').should('have.been.called', 1);
        });

        it('should update the property summary when updated', () => {
            Model.PropertySummary.totalBedRooms.type('3');
            cy.get('@onUpdate').should('have.been.called', 1);
            Model.PropertySummary.totalBedRooms.find('.qv-form-input').should('have.value', '3');
        });

        it('should update the construction information when updated', () => {
            Model.BuildingsInformation.description.type('--Property Name--');
            cy.get('@onUpdate').should('have.been.called', 1);
            Model.BuildingsInformation.description.find('.qv-form-input').should('have.value', '--Property Name--');
        });

        it('should update the spaces when updated', () => {
            Model.BuildingSpaces.buildingSpaceLabel.find('.multiselect').click();
            Model.BuildingSpaces.buildingSpaceLabel.find('.multiselect__element').first().click();
            cy.get('@onUpdate').should('have.been.called', 18);
        });

        it('should update the site improvements when updated', () => {
            Model.SiteImprovements.description.type('--test--');
            cy.get('@onUpdate').should('have.been.called', 1);
            Model.SiteImprovements.description.find('.qv-form-input').should('have.value', '--test--');
        });
    });
});

function createMockProperty() {
    return {
        category: { code: 'RB', description: 'Residential - Dwelling' },
        territorialAuthority: { code: '123', description: 'Test TA' },
    };
}

function createMockCurrentPropertyDetail() {
    return { currentPropertyDetail: { buildings: [] } };
}

function createMockPropertyDetail() {
    return {
        category: { category: 'Category_DVR', code: '1', description: 'Test Category Category_DVR' },
        natureOfImprovements: [],
        propertyName: null,
        landUse: { landUse: { category: 'LandUse_DVR', code: '1', description: 'Test Category LandUse_DVR' }, landZone: {} },
        planNumber: '1A',
        site: {
            landArea: 0.6655,
            effectiveLandArea: null,
            lotPosition: { category: 'LotPosition_DVR', code: '1', description: 'Test Category LotPosition_DVR' },
            contour: { category: 'Contour_DVR', code: '1', description: 'Test Category Contour_DVR' },
            view: { category: 'View_DVR', code: '1', description: 'Test Category View_DVR' },
            viewScope: { category: 'ViewScope_DVR', code: '1', description: 'Test Category ViewScope_DVR' },
            classOfSurroundingImprovements: { category: 'ClassOfSurroundingImprovements_DVR', code: '1', description: 'Test Category ClassOfSurroundingImprovements_DVR' },
            hasDriveway: true,
            hasCarAccess: true,
            hasLaundryWorkshop: true,
            carParks: 2,
        },
        summary: {
            houseType: { category: 'HouseType_DVR', code: '1', description: 'Test Category HouseType_DVR' },
            unitsOfUse: { category: 'UnitsOfUse_DVR', code: '1', description: 'Test Category UnitsOfUse_DVR' },
            age: { category: 'Age_DVR', code: '1', description: 'Test Category Age_DVR' },
            effectiveYearBuilt: { category: 'EffectiveYearBuilt_DVR', code: '1', description: 'Test Category EffectiveYearBuilt_DVR' },
            poorFoundations: null,
            totalBedRooms: 2,
            totalBathRooms: 2,
            totalToilets: 2,
        },
        isOutlier: true,
        dvrSnapshot: { wallConstruction: 'Brick', wallCondition: 'Good', roofConstruction: 'Tile', roofCondition: 'Good', isModernised: 'Yes', landscapingQuality: 'Good', hasDeck: 'Yes', hasLargeOtherImprovements: 'Yes', numberofUnderMainroofgarages: 2, numberOfFreeStandingGarages: 1 },
        buildings: [
            {
                buildingType: { category: 'BuildingType', code: 'DW', description: 'Dwelling', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'DWG' },
                floorArea: 110,
                numberOfStoreys: 1,
                yearBuilt: 2018,
                description: null,
                buildingLabel: 'D2',
                isPrimaryBuilding: true,
                wallConstruction: { category: 'WallConstruction', code: 'BR', description: 'Brick', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'BRK' },
                wallCondition: { category: 'WallCondition', code: 'G', description: 'Good', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'G' },
                roofConstruction: { category: 'RoofConstruction', code: 'T', description: 'Tile', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'T' },
                roofCondition: { category: 'RoofCondition', code: 'G', description: 'Good', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'G' },
                floorConstruction: { category: 'FloorConstruction', code: 'C', description: 'Concrete', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'C' },
                foundation: { category: 'Foundation', code: 'C', description: 'Concrete', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'C' },
                wiring: { type: 'StandardFeature', definition: null, age: { category: 'FeatureAge', code: '2010', description: '2010-14', shortDescription: null, isActive: true, parentClassification: null, externalCode: null }, quality: null, inferred: null, collectionDate: null },
                plumbing: { type: 'StandardFeature', definition: null, age: { category: 'FeatureAge', code: '2015', description: '2015-19', shortDescription: null, isActive: true, parentClassification: null, externalCode: null }, quality: null, inferred: null, collectionDate: null },
                insulation: {
                    type: 'ComplexFeature',
                    definition: [{ category: 'Insulation', code: 'NO', description: 'None', shortDescription: null, isActive: true, parentClassification: null, externalCode: null }],
                    age: null,
                    quality: null,
                    inferred: null,
                    collectionDate: null,
                },
                glazing: {
                    type: 'StandardFeature',
                    definition: { category: 'DoubleGlazing', code: 'FLTG', description: 'Full triple glazing', shortDescription: null, isActive: true, parentClassification: null, externalCode: null },
                    age: null,
                    quality: null,
                    inferred: null,
                    collectionDate: null,
                },
                otherFeatures: {
                    type: 'ComplexFeature',
                    definition: [{ category: 'BuildingFeature', code: 'AF', description: 'Accessible features', shortDescription: null, isActive: true, parentClassification: null, externalCode: null }],
                    age: null,
                    quality: null,
                    inferred: null,
                    collectionDate: null,
                },
                spaces: [
                    {
                        type: 'GarageSpace',
                        spaceType: { category: 'SpaceType', code: 'GA', description: 'Garage', shortDescription: null, isActive: true, parentClassification: null, externalCode: null },
                        spaceLabel: null,
                        numberOfSimilarSpaces: 1,
                        floorArea: 48,
                        quality: { category: 'FeatureQuality', code: 'A', description: 'Average', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'A' },
                        numberOfCarparks: 1,
                        garageFeatures: {
                            type: 'ComplexFeature',
                            definition: [{ category: 'GarageFeature', code: 'IA', description: 'Internal Access', shortDescription: null, isActive: true, parentClassification: null, externalCode: null }],
                            age: null,
                            quality: null,
                            inferred: null,
                            collectionDate: null,
                        },
                    },
                ],
            },
        ],
        siteDevelopment: { type: 'OtherImprovement', definition: null, age: null, quantity: null, unitOfMeasure: null, quality: { category: 'FeatureQuality', code: 'P', description: 'Poor', shortDescription: null, isActive: true, parentClassification: null, externalCode: 'P' }, description: null, inferred: null, collectionDate: null },
        otherImprovements: [],
    };
}

function createMockValidations() {
    return {
        propertyDetail: {
            errorCount: 3,
            warningCount: 1,
            errors: [{ displayName: 'Property Name', path: 'propertyName', message: 'is required' }],
            warnings: [{ displayName: 'Production', path: 'landUse.production', message: 'is required' }],
        },
        valuationWorksheet: {
            errorCount: 1,
            warningCount: 1,
            errors: [{ displayName: 'Adopted Capital Value', path: 'adoptedCapitalValue', message: 'cannot be less than 0' }],
            warnings: [{ displayName: 'Current Multiple Owner Adjustment', path: 'maoriLand.currentMultipleOwnerAdjustment', message: 'cannot be less than 0%' }],
        },
    };
}

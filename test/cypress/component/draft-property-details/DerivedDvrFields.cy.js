import { DerivedDvrFields as Component } from '@/components/propertyDetails/residential/index.js';
import Model from '../../model/DraftPropertyDetails';
import { ref } from 'vue';
import { createMockStore } from '../../support/store.js';

describe('DerivedDvrFields', () => {

    it('should render the DerivedDvrFields', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
        });

        Model.DerivedDvrFields.wallConstructionAndCondition.should('exist');
        Model.DerivedDvrFields.roofConstructionAndCondition.should('exist');
        Model.DerivedDvrFields.modernisation.should('exist');
        Model.DerivedDvrFields.landscaping.should('exist');
        Model.DerivedDvrFields.deck.should('exist');
        Model.DerivedDvrFields.largeOIs.should('exist');
        Model.DerivedDvrFields.umrGaraging.should('exist');
        Model.DerivedDvrFields.fsGaraging.should('exist');
        Model.DerivedDvrFields.saveButton.should('not.exist');
    });

});

function getMockPropertyDetails() {
    const dvrSnapshot = ref({
        wallConstruction: 'Brick',
        wallCondition: 'Good',
        roofConstruction: 'Tile',
        roofCondition: 'Good',
        isModernised: 'Yes',
        landscapingQuality: 'Good',
        hasDeck: 'Yes',
        hasLargeOtherImprovements: 'Yes',
        numberofUnderMainroofgarages: 2,
        numberOfFreeStandingGarages: 1,
    });

    return {
        dvrSnapshot,
        hasDerivedDvrFields: true,
        highlight: false,
    };
}

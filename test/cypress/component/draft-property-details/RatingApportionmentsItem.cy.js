import { default as Component } from '@/components/propertyDetails/residential/RatingApportionmentsItem.vue';
import Model from '../../model/DraftPropertyDetails.js';
import { ref, watch } from 'vue';
import { createDraftPDClassificationsStore } from './mocks.js';

let store;

describe('RatingApportionmentsItem', () => {
    beforeEach(() => {
        store = createDraftPDClassificationsStore();
        store.commit('classificationsLoaded', true);
    });

    context('Collapsable', () => {
        it('clicking the toggle should toggle the collapsable', () => {
            const apportionment = ref(createMockRatingApportionment());
            const propsData = {
                value: apportionment,
            };

            const listeners = {
                'input': (value) => {
                    apportionment.value = value;
                },
            }

            cy.mount(Component, {
                propsData,
                emitsSpy: true,
                store,
                listeners,
            });
        });
    });

    context('Emits', () => {
        let apportionment;
        let propsData;
        let listeners;

        beforeEach(() => {
            apportionment = ref(createMockRatingApportionment());
            propsData = {
                value: apportionment,
            };
            listeners = {
                'input': cy.spy().as('onInputSpy'),
            }
        })

        it ('should emit input when category is changed', () => {
            cy.mount(Component, {
                propsData,
                store,
                listeners,
            });

            cy.get('[data-cy="category"]').click(1);
            cy.get('[data-cy="category"]').type("\n");
            cy.get('@onInputSpy').should('be.called');
        })
    });
});

function createMockRatingApportionment() {
    return {
            'qpid': 14959,
            'suffix': 'A',
            'category': {
                'category': 'Category_DVR',
                'code': 'COPC',
                'description': 'Commercial-Office-Provincial-poor',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': {
                    'category': 'CategoryGroup',
                    'code': 'U30',
                    'description': 'Commercial',
                    'shortDescription': null,
                    'isActive': true,
                    'parentClassification': null,
                    'externalCode': null,
                },
                'externalCode': null,
            },
            'natureOfImprovements': [
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'DWG',
                        'description': 'Dwelling',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OBS',
                        'description': 'Other Buildings',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OFFICE',
                        'description': 'Office',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OI',
                        'description': 'Other Improvements',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
            ],
            'landZone': {
                'category': 'TA_1_LandZone_DVR',
                'code': '8A',
                'description': '8A',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'landUse': {
                'category': 'LandUse_DVR',
                'code': '80',
                'description': 'Multi-use within Commercial',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'units': 3,
            'age': {
                'category': 'Age_DVR',
                'code': 'MIXED',
                'description': 'Mixed/Remod',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'totalFloorArea': 190,
            'buildingSiteCover': 190,
            'roofCondition': {
                'category': 'FeatureQuality',
                'code': 'A',
                'description': 'Average',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'A',
            },
            'wallCondition': {
                'category': 'FeatureQuality',
                'code': 'A',
                'description': 'Average',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'A',
            },
            'roofConstruction': {
                'category': 'RoofConstruction_DVR',
                'code': '405',
                'description': 'Steel/G-Iron',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'I',
            },
            'wallConstruction': {
                'category': 'WallConstruction_DVR',
                'code': '312',
                'description': 'Mix.Material',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'X',
            },
            'landArea': 0.0868,
            'carparks': 1,
        }
}

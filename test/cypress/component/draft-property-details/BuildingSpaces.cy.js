import { BuildingSpaces as Component } from '@/components/propertyDetails/residential/index.js';
import Model from '../../model/DraftPropertyDetails';
import { ref } from 'vue';
import { createMockStore } from '../../support/store.js';

describe('BuildingSpaces', () => {
    it('should render the BuildingSpaces', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
        });

        Model.BuildingSpaces.spaceType.should('exist');
        Model.BuildingSpaces.floorArea.should('exist');
        Model.BuildingSpaces.quality.should('exist');
        Model.BuildingSpaces.numberOfCarparks.should('exist');
        Model.BuildingSpaces.garageFeatures.should('exist');
        Model.BuildingSpaces.removeSpaceRow.should('exist');
        Model.BuildingSpaces.duplicateSpaceRow.should('exist');
    });

    it('emits changed event when remove space button is clicked', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:buildings': onUpdate,
            },
        });

        Model.BuildingSpaces.removeSpaceRow.click();
        cy.get('@onUpdate').should('have.been.called', 1);
    });

    it('emits changed event when duplicate space button is clicked', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:buildings': onUpdate,
            },
        });
        Model.BuildingSpaces.duplicateSpaceRow.first().click();
        cy.get('@onUpdate').should('have.been.called', 1);
    });

    it('emits changed event when add spaces button is clicked', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:buildings': onUpdate,
            },
        });

        Model.BuildingSpaces.buildingSpaceLabel.find('.multiselect').click();
        Model.BuildingSpaces.buildingSpaceLabel.find('.multiselect__element').first().click();
        Model.BuildingSpaces.buildingSpaceType.find('.multiselect').click();
        Model.BuildingSpaces.buildingSpaceType.find('.multiselect__element').first().click();

        Model.BuildingSpaces.addSpaceBtn.click();
        cy.get('@onUpdate').should('have.been.called', 18);
    });
});

function getMockPropertyDetails() {
    const buildings = ref([
        {
            spaces: [
                {
                    type: 'GarageSpace',
                    spaceType: {
                        category: 'SpaceType',
                        code: 'GA',
                        description: 'Garage',
                        shortDescription: null,
                        isActive: true,
                        parentClassification: null,
                        externalCode: null,
                    },
                    spaceLabel: null,
                    numberOfSimilarSpaces: 1,
                    floorArea: 48,
                    quality: {
                        category: 'FeatureQuality',
                        code: 'A',
                        description: 'Average',
                        shortDescription: null,
                        isActive: true,
                        parentClassification: null,
                        externalCode: 'A',
                    },
                    numberOfCarparks: 1,
                    garageFeatures: {
                        type: 'ComplexFeature',
                        definition: [
                            {
                                category: 'GarageFeature',
                                code: 'IA',
                                description: 'Internal Access',
                                shortDescription: null,
                                isActive: true,
                                parentClassification: null,
                                externalCode: null,
                            },
                        ],
                        age: null,
                        quality: null,
                        inferred: null,
                        collectionDate: null,
                    },
                },
            ],
        },
    ]);

    return {
        buildings,
    };
}

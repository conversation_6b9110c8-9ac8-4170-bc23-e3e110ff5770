import { PropertySummary as Component } from '@/components/propertyDetails/residential/index.js';
import Model from '../../model/DraftPropertyDetails';
import { ref } from 'vue';
import { createMockStore } from '../../support/store.js';

describe('PropertySummary', () => {

    it('should render the PropertySummary', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
        });

        Model.PropertySummary.houseType.should('exist');
        Model.PropertySummary.unitsOfUse.should('exist');
        Model.PropertySummary.age.should('exist');
        Model.PropertySummary.effectiveYearBuilt.should('exist');
        Model.PropertySummary.poorFoundations.should('exist');
        Model.PropertySummary.totalBedRooms.should('exist');
        Model.PropertySummary.totalBathRooms.should('exist');
        Model.PropertySummary.totalToilets.should('exist');
        Model.PropertySummary.buildingSiteCover.should('exist');
        Model.PropertySummary.totalFloorArea.should('exist');
        Model.PropertySummary.mainLivingArea.should('exist');
        Model.PropertySummary.totalLivingArea.should('exist');
        Model.PropertySummary.laundaryWorkshop.should('exist');
        Model.PropertySummary.carAccess.should('exist');
        Model.PropertySummary.driveway.should('exist');
        Model.PropertySummary.carparks.should('exist');

    });

    it('emits changed event when location details are updated', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate')

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:property-detail': onUpdate,
            }
        });
        Model.PropertySummary.age.find('.multiselect').click();
        Model.PropertySummary.age.find('.multiselect__element').first().click();

        cy.get('@onUpdate').should('have.been.called', 1);
    });

});

function getMockPropertyDetails() {
    const propertyDetail = ref({
        site: {
            hasDriveway: true,
            hasCarAccess: true,
            hasLaundryWorkshop: true,
            carParks: 2,
        },
        summary: {
            houseType: {
                category: 'HouseType_DVR',
                code: '1',
                description: 'Test Category HouseType_DVR',
            },
            unitsOfUse: {
                category: 'UnitsOfUse_DVR',
                code: '1',
                description: 'Test Category UnitsOfUse_DVR',
            },
            age: {
                category: 'Age_DVR',
                code: '1',
                description: 'Test Category Age_DVR',
            },
            effectiveYearBuilt: {
                category: 'EffectiveYearBuilt_DVR',
                code: '1',
                description: 'Test Category EffectiveYearBuilt_DVR',
            },
            poorFoundations: null,
            totalBedRooms: 2,
            totalBathRooms: 2,
            totalToilets: 2,
        },
    });

    return {
        propertyDetail,
    };
}

import { createMockStore } from '../../support/store.js';

export function createDraftPDClassificationsStore() {
    const createConstructionClassifications = (category) => [
        {
            category,
            'code': '405',
            'description': 'Steel/G-Iron',
        },
        {
            category,
            'code': '407',
            'description': 'Mixed',
        },
    ];
    const classifications = {
        Category_DVR: [
            {
                category: 'Category_DVR',
                code: 'RD202B',
                description: 'Residential-Dwelling-2020/2029-average',
            },
            {
                'category': 'Category_DVR',
                'code': 'COPC',
                'description': 'Commercial-Office-Provincial-poor',
            },
        ],
        NatureOfImprovements_DVR: [
            {
                'category': 'NatureOfImprovements_DVR',
                'code': 'OI',
                'description': 'Other Improvements',
            },
            {
                'category': 'NatureOfImprovements_DVR',
                'code': 'OFFICE',
                'description': 'Office',
            },
        ],
        TA_1_LandZone_DVR: [
            {
                category: 'TA_1_LandZone_DVR',
                code: '8A',
                description: '8A',
            },
            {
                category: 'TA_1_LandZone_DVR',
                code: '9A',
                description: '9A',
            },
        ],
        LandUse_DVR: [
            {
                'category': 'LandUse_DVR',
                'code': '80',
                'description': 'Multi-use within Commercial',
            },
            {
                'category': 'LandUse_DVR',
                'code': '81',
                'description': 'Single Unit excluding Bach',
            },
        ],
        Age_DVR: [
            {
                'category': 'Age_DVR',
                'code': 'MIXED',
                'description': 'Mixed/Remod',
            },
            {
                'category': 'Age_DVR',
                'code': 'Old',
                'description': 'Very Old',
            },
        ],
        FeatureQuality: [
            {
                'category': 'FeatureQuality',
                'code': 'A',
                'description': 'Average',
                'externalCode': 'A',
            },
            {
                'category': 'FeatureQuality',
                'code': 'B',
                'description': 'Good',
                'externalCode': 'B',
            },
        ],
        RoofConstruction_DVR: createConstructionClassifications('RoofConstruction_DVR'),
        WallConstruction_DVR: createConstructionClassifications('WallConstruction_DVR'),
    };

    return createMockStore(classifications);
}

import { BuildingsInformation as Component } from '@/components/propertyDetails/residential/index.js';
import Model from '../../model/DraftPropertyDetails';
import { ref } from 'vue';
import { createMockStore } from '../../support/store.js';

describe('BuildingsInformation', () => {
    it('should render the BuildingsInformation', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);

        const onUpdate = (value) => {
            if (value.id === 'landUse' || value.id === 'landZone') {
                propsData.propertyDetail.value.landUse.landUse = value.value;
                return;
            }
            propsData.propertyDetail.value[value.id] = value.value;
        };

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:property-detail': onUpdate,
            },
        });

        Model.BuildingsInformation.typeOfBuilding.should('exist');
        Model.BuildingsInformation.floorArea.should('exist');
        Model.BuildingsInformation.noOfStoreys.should('exist');
        Model.BuildingsInformation.yearBuilt.should('exist');
        Model.BuildingsInformation.description.should('exist');
        Model.BuildingsInformation.buildingLabel.should('exist');
        Model.BuildingsInformation.principalBuilding.should('exist');
        Model.BuildingsInformation.wallConstruction.should('exist');
        Model.BuildingsInformation.wallCondition.should('exist');
        Model.BuildingsInformation.roofConstruction.should('exist');
        Model.BuildingsInformation.roofCondition.should('exist');
        Model.BuildingsInformation.floorConstruction.should('exist');
        Model.BuildingsInformation.foundation.should('exist');
        Model.BuildingsInformation.wirirngAge.should('exist');
        Model.BuildingsInformation.plumbingAge.should('exist');
        Model.BuildingsInformation.insulation.should('exist');
        Model.BuildingsInformation.glazing.should('exist');
        Model.BuildingsInformation.otherFeatures.should('exist');
    });

    it('emits changed event when building informations are updated', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:buildings': onUpdate,
            },
        });

        Model.BuildingsInformation.description.type('--Property Name--');
        cy.get('@onUpdate').should('have.been.called', 1);
    });

    it('emits changed event when remove building button is clicked', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:buildings': onUpdate,
            },
        });

        Model.BuildingsInformation.removeBtn.click();
        cy.get('@onUpdate').should('have.been.called', 1);
    });

    it('emits changed event when copy building button is clicked', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:buildings': onUpdate,
            },
        });

        Model.BuildingsInformation.copyBtn.click();
        cy.get('@onUpdate').should('have.been.called', 1);
    });

    it('emits changed event when add building button is clicked', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:buildings': onUpdate,
            },
        });

        Model.BuildingsInformation.addBuildingBtn.click();
        cy.get('@onUpdate').should('have.been.called', 18);
    });
});

function getMockPropertyDetails() {
    const buildings = ref([
        {
            buildingType: {
                category: 'BuildingType',
                code: 'DW',
                description: 'Dwelling',
                shortDescription: null,
                isActive: true,
                parentClassification: null,
                externalCode: 'DWG',
            },
            floorArea: 110,
            numberOfStoreys: 1,
            yearBuilt: 2018,
            description: null,
            buildingLabel: 'D2',
            isPrimaryBuilding: true,
            wallConstruction: {
                category: 'WallConstruction',
                code: 'BR',
                description: 'Brick',
                shortDescription: null,
                isActive: true,
                parentClassification: null,
                externalCode: 'BRK',
            },
            wallCondition: {
                category: 'WallCondition',
                code: 'G',
                description: 'Good',
                shortDescription: null,
                isActive: true,
                parentClassification: null,
                externalCode: 'G',
            },
            roofConstruction: {
                category: 'RoofConstruction',
                code: 'T',
                description: 'Tile',
                shortDescription: null,
                isActive: true,
                parentClassification: null,
                externalCode: 'T',
            },
            roofCondition: {
                category: 'RoofCondition',
                code: 'G',
                description: 'Good',
                shortDescription: null,
                isActive: true,
                parentClassification: null,
                externalCode: 'G',
            },
            floorConstruction: {
                category: 'FloorConstruction',
                code: 'C',
                description: 'Concrete',
                shortDescription: null,
                isActive: true,
                parentClassification: null,
                externalCode: 'C',
            },
            foundation: {
                category: 'Foundation',
                code: 'C',
                description: 'Concrete',
                shortDescription: null,
                isActive: true,
                parentClassification: null,
                externalCode: 'C',
            },
            wiring: {
                type: 'StandardFeature',
                definition: null,
                age: {
                    category: 'FeatureAge',
                    code: '2010',
                    description: '2010-14',
                    shortDescription: null,
                    isActive: true,
                    parentClassification: null,
                    externalCode: null,
                },
                quality: null,
                inferred: null,
                collectionDate: null,
            },
            plumbing: {
                type: 'StandardFeature',
                definition: null,
                age: {
                    category: 'FeatureAge',
                    code: '2015',
                    description: '2015-19',
                    shortDescription: null,
                    isActive: true,
                    parentClassification: null,
                    externalCode: null,
                },
                quality: null,
                inferred: null,
                collectionDate: null,
            },
            insulation: {
                type: 'ComplexFeature',
                definition: [
                    {
                        category: 'Insulation',
                        code: 'NO',
                        description: 'None',
                        shortDescription: null,
                        isActive: true,
                        parentClassification: null,
                        externalCode: null,
                    },
                ],
                age: null,
                quality: null,
                inferred: null,
                collectionDate: null,
            },
            glazing: {
                type: 'StandardFeature',
                definition: {
                    category: 'DoubleGlazing',
                    code: 'FLTG',
                    description: 'Full triple glazing',
                    shortDescription: null,
                    isActive: true,
                    parentClassification: null,
                    externalCode: null,
                },
                age: null,
                quality: null,
                inferred: null,
                collectionDate: null,
            },
            otherFeatures: {
                type: 'ComplexFeature',
                definition: [
                    {
                        category: 'BuildingFeature',
                        code: 'AF',
                        description: 'Accessible features',
                        shortDescription: null,
                        isActive: true,
                        parentClassification: null,
                        externalCode: null,
                    },
                ],
                age: null,
                quality: null,
                inferred: null,
                collectionDate: null,
            },
        },
    ]);

    return {
        buildings,
    };
}

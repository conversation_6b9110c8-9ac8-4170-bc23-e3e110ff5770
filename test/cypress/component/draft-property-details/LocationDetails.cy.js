import { LocationDetails as Component } from '@/components/propertyDetails/residential/index.js';
import Model from '../../model/DraftPropertyDetails';
import { ref } from 'vue';
import { createMockStore } from '../../support/store.js';

describe('LocationDetails', () => {

    it('should render the LocationDetails', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);

        const onUpdate = (value) => {
            propsData.propertyDetail.value[value.id] = value.value;
        }

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:property-detail': onUpdate,
            }
        });

        Model.LocationDetails.lotPosition.should('exist');
        Model.LocationDetails.contour.should('exist');
        Model.LocationDetails.view.should('exist');
        Model.LocationDetails.viewScope.should('exist');
        Model.LocationDetails.classOfSurroundingImprovements.should('exist');
        Model.LocationDetails.outlier.should('exist');
    });

    it('emits changed event when location details are updated', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate')

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:property-detail': onUpdate,
            }
        });
        Model.LocationDetails.classOfSurroundingImprovements.find('.multiselect').click();
        Model.LocationDetails.classOfSurroundingImprovements.find('.multiselect__element').first().click();

        cy.get('@onUpdate').should('have.been.called', 1);
    });

});

function getMockPropertyDetails() {
    const propertyDetail = ref({
        site: {
            lotPosition: {
                category: 'LotPosition_DVR',
                code: '1',
                description: 'Test Category LotPosition_DVR',
            },
            contour: {
                category: 'Contour_DVR',
                code: '1',
                description: 'Test Category Contour_DVR',
            },
            view: {
                category: 'View_DVR',
                code: '1',
                description: 'Test Category View_DVR',
            },
            viewScope: {
                category: 'ViewScope_DVR',
                code: '1',
                description: 'Test Category ViewScope_DVR',
            },
            classOfSurroundingImprovements: {
                category: 'ClassOfSurroundingImprovements_DVR',
                code: '1',
                description: 'Test Category ClassOfSurroundingImprovements_DVR',
            }
        },
        isOutlier: true,
    });

    return {
        propertyDetail,
    };
}

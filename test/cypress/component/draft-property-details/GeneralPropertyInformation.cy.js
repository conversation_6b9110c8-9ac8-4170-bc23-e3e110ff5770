import { GeneralPropertyInformation as Component } from '@/components/propertyDetails/residential/index.js';
import Model from '../../model/DraftPropertyDetails';
import { ref } from 'vue';
import { createMockStore } from '../../support/store.js';

describe('GeneralPropertyInformation', () => {

    it('should render the GeneralPropertyInformation', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);

        const onUpdate = (value) => {
            if(value.id === 'landUse' || value.id === 'landZone') {
                propsData.propertyDetail.value.landUse.landUse = value.value;
                return;
            }
            propsData.propertyDetail.value[value.id] = value.value;
        }

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:property-detail': onUpdate,
            }
        });

        Model.GeneralPropertyInformation.category.should('exist');
        Model.GeneralPropertyInformation.natureOfImprovements.should('exist');
        Model.GeneralPropertyInformation.propertyName.should('exist');
        Model.GeneralPropertyInformation.landUse.should('exist');
        Model.GeneralPropertyInformation.taLandZone.should('exist');
        Model.GeneralPropertyInformation.effectiveLandArea.should('exist');
        Model.GeneralPropertyInformation.landArea.should('exist');
        Model.GeneralPropertyInformation.maoriLand.should('exist');
        Model.GeneralPropertyInformation.planId.should('exist');
        Model.GeneralPropertyInformation.production.should('exist');
    });

    it('emits changed event when property details are updated', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate')

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:property-detail': onUpdate,
            }
        });

        Model.GeneralPropertyInformation.propertyName.type('--Property Name--');

        cy.get('@onUpdate').should('have.been.called', 1);
    });

});

function getMockPropertyDetails() {
    const propertyDetail = ref({
        category: {
            category: 'Category_DVR',
            code: '1',
            description: 'Test Category Category_DVR',
        },
        natureOfImprovements: [],
        propertyName: 'Test',
        landUse: {
            landUse :{
                category: 'LandUse_DVR',
                code: '1',
                description: 'Test Category LandUse_DVR',
            },
            landZone :{},
        },
        site: {
            landArea: 0.6655,
            effectiveLandArea: null,
        },
        planNumber: '1A',
    });

    return {
        propertyDetail,
        taCode: 'TA01',
    };
}

import { default as Component } from '@/components/propertyDetails/residential/RatingApportionments.vue';
import { ref } from 'vue';
import { createDraftPDClassificationsStore } from './mocks.js';
import { PropertyDetail } from '@quotable-value/validation';

let store;

describe('RatingApportionments', () => {
    beforeEach(() => {
        store = createDraftPDClassificationsStore();
        store.commit('classificationsLoaded', true);
    });

    context('emits', () => {
        it('changing a value should emit update:apportionments', () => {
            const apportionments = ref(createMockRatingApportionments());

            const propsData = {
                apportionments,
            };

            const updateSpy = cy.spy((value) => {
                apportionments.value = value;
            }).as('onUpdateSpy');
            const listeners = {
                'update:apportionments': updateSpy,
            };

            cy.mount(Component, {
                propsData,
                store,
                listeners,
            });

            cy.get('[data-cy="units"]').first().type('{selectall}2');
            cy.get('[data-cy="units"]').first().blur();
            cy.get('@onUpdateSpy').should('be.called');
        });
    });
    context('validations', () => {
        let apportionments, property, propertyDetail, dvrSnapshot;
        beforeEach(() => {
            apportionments = createMockRatingApportionments();
            property = createMockProperty();
            propertyDetail = createMockPropertyDetail();
            dvrSnapshot = createMockDvrSnapshot();
        });

        it('highlights age if any building detail fields are selected but age is blank', () => {
            apportionments[0].age = null; // remove age to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(1) > .qv-validation-wrapper > [data-cy="age"] > .multiselect__tags')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it('highlights wall construction if any building detail fields are selected but wall construction is blank', () => {
            apportionments[0].wallConstruction = null; // remove wall construction to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(2) > .qv-validation-wrapper > [data-cy="wall-construction"] > .multiselect__tags')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it('highlights wall condition if any building detail fields are selected but wall condition is blank', () => {
            apportionments[0].wallCondition = null; // remove wall condition to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(3) > .qv-validation-wrapper > [data-cy="wall-condition"] > .multiselect__tags')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it('highlights roof construction if any building detail fields are selected but roof construction is blank', () => {
            apportionments[0].roofConstruction = null; // remove roof construction to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(4) > .qv-validation-wrapper > [data-cy="roof-construction"] > .multiselect__tags')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it('highlights roof condition if any building detail fields are selected but roof condition is blank', () => {
            apportionments[0].roofCondition = null; // remove roof condition to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(5) > .qv-validation-wrapper > [data-cy="roof-condition"] > .multiselect__tags')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it('highlights site cover if any building detail fields are selected but site cover is blank', () => {
            apportionments[0].buildingSiteCover = null; // remove site cover to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(6) > .qv-validation-wrapper > [data-cy="site-cover"]')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it('highlights total floor area if any building detail fields are selected but total floor area is blank', () => {
            apportionments[0].totalFloorArea = null; // remove total floor area to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(7) > .qv-validation-wrapper > [data-cy="total-floor-area"]')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it("highlights total floor area if it's greater than zero but site cover is blank or zero", () => {
            apportionments[0].buildingSiteCover = 0; // remove sote cover while having total floor area to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(7) > .qv-validation-wrapper > [data-cy="total-floor-area"]')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it("highlights total floor area if it's less than site area" , () => {
            apportionments[0].totalFloorArea = 5; // set total floor area to smaller than site area to trigger warning
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(7) > .qv-validation-wrapper > [data-cy="total-floor-area"]')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it("highlights category if it's blank" , () => {
            apportionments[0].category = null; // remove category to trigger error
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(1) > .qv-flex-grow > .qv-validation-wrapper > [data-cy="category"] > .multiselect__tags')
                .should('have.css','border-color','rgb(252, 61, 57)');
        });

        it("highlights all land areas if total is greater than parent land area (set to 0.2 for this test)" , () => {
            apportionments[0].landArea = 0.11; // set both land areas to 0.11 so that total is 0.22 to trigger warnings
            apportionments[1].landArea = 0.11;
            propertyDetail.site.landArea = 0.2;
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(1) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(9) > .qv-validation-wrapper > [data-cy="land-area"]')
                .should('have.css','border-color','rgb(255, 153, 15)');

            cy.get(':nth-child(2) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(9) > .qv-validation-wrapper > [data-cy="land-area"]')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });

        it("highlights second land areas if this one is greater than parent land area (set to 0.2 for this test)" , () => {
            apportionments[1].landArea = 0.3; // set second land area to 0.3 so that it gets highlighted
            propertyDetail.site.landArea = 0.2;
            propertyDetail.ratingApportionments = apportionments;

            const validationSet = PropertyDetail.validatePropertyDetailRatingValuation(
                propertyDetail, property, dvrSnapshot, 0.2, 'NOT_COMPLETE'
            );

            const propsData = {
                apportionments,
                validationSet
            };

            cy.mount(Component, {
                propsData,
                store
            })
            cy.get(':nth-child(2) > :nth-child(1) > [data-cy="rating-apportionments-item"] > .qv-flex-column > :nth-child(2) > :nth-child(9) > .qv-validation-wrapper > [data-cy="land-area"]')
                .should('have.css','border-color','rgb(255, 153, 15)');
        });
    });
});

function createMockRatingApportionments() {
    return [
        {
            'qpid': 14959,
            'suffix': 'A',
            'category': {
                'category': 'Category_DVR',
                'code': 'COPC',
                'description': 'Commercial-Office-Provincial-poor',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': {
                    'category': 'CategoryGroup',
                    'code': 'U30',
                    'description': 'Commercial',
                    'shortDescription': null,
                    'isActive': true,
                    'parentClassification': null,
                    'externalCode': null,
                },
                'externalCode': null,
            },
            'natureOfImprovements': [
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'DWG',
                        'description': 'Dwelling',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OBS',
                        'description': 'Other Buildings',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OFFICE',
                        'description': 'Office',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OI',
                        'description': 'Other Improvements',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
            ],
            'landZone': {
                'category': 'TA_1_LandZone_DVR',
                'code': '8A',
                'description': '8A',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'landUse': {
                'category': 'LandUse_DVR',
                'code': '80',
                'description': 'Multi-use within Commercial',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'units': 3,
            'age': {
                'category': 'Age_DVR',
                'code': 'MIXED',
                'description': 'Mixed/Remod',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'totalFloorArea': 190,
            'buildingSiteCover': 190,
            'roofCondition': {
                'category': 'FeatureQuality',
                'code': 'A',
                'description': 'Average',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'A',
            },
            'wallCondition': {
                'category': 'FeatureQuality',
                'code': 'A',
                'description': 'Average',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'A',
            },
            'roofConstruction': {
                'category': 'RoofConstruction_DVR',
                'code': '405',
                'description': 'Steel/G-Iron',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'I',
            },
            'wallConstruction': {
                'category': 'WallConstruction_DVR',
                'code': '405',
                'description': 'Steel/G-Iron',
            },
            'landArea': 0.0868,
            'carparks': 1,
        },
        {
            'qpid': 2812490,
            'suffix': 'B',
            'category': {
                'category': 'Category_DVR',
                'code': 'COPC',
                'description': 'Commercial-Office-Provincial-poor',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': {
                    'category': 'CategoryGroup',
                    'code': 'U30',
                    'description': 'Commercial',
                    'shortDescription': null,
                    'isActive': true,
                    'parentClassification': null,
                    'externalCode': null,
                },
                'externalCode': null,
            },
            'natureOfImprovements': [
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'DWG',
                        'description': 'Dwelling',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OB',
                        'description': 'Other Building',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OFFICE',
                        'description': 'Office',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
                {
                    'quantity': 1,
                    'improvement': {
                        'category': 'NatureOfImprovements_DVR',
                        'code': 'OI',
                        'description': 'Other Improvements',
                        'shortDescription': null,
                        'isActive': true,
                        'parentClassification': null,
                        'externalCode': null,
                    },
                },
            ],
            'landZone': {
                'category': 'TA_1_LandZone_DVR',
                'code': '8A',
                'description': '8A',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'landUse': {
                'category': 'LandUse_DVR',
                'code': '80',
                'description': 'Multi-use within Commercial',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'units': 2,
            'age': {
                'category': 'Age_DVR',
                'code': 'MIXED',
                'description': 'Mixed/Remod',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': null,
            },
            'totalFloorArea': 189,
            'buildingSiteCover': 189,
            'roofCondition': {
                'category': 'FeatureQuality',
                'code': 'A',
                'description': 'Average',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'A',
            },
            'wallCondition': {
                'category': 'FeatureQuality',
                'code': 'A',
                'description': 'Average',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'A',
            },
            'roofConstruction': {
                'category': 'RoofConstruction_DVR',
                'code': '405',
                'description': 'Steel/G-Iron',
                'shortDescription': null,
                'isActive': true,
                'parentClassification': null,
                'externalCode': 'I',
            },
            'wallConstruction': {
                'category': 'WallConstruction_DVR',
                'code': '405',
                'description': 'Steel/G-Iron',
            },
            'landArea': 0.0868,
            'carparks': 1,
        },
    ];
}

function createMockProperty() {
    return {
        "property": {
            "id": "7e1a59eb-6b9e-4d42-bbf6-7b8924d7e7d6",
            "qupid": 3349445,
            "tapid": null,
            "status": {
                "category": "AssessmentStatus",
                "code": "A",
                "description": "Active",
                "shortDescription": null,
                "sortOrder": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "territorialAuthority": {
                "code": 1,
                "name": "Far North District"
            },
            "planNumber": "9B",
            "propertyName": null,
            "assessmentId": 39796,
            "rollNumber": 411,
            "assessmentNumber": 13900,
            "suffix": null,
            "salesGroup": {
                "category": "SalesGroup",
                "code": "0107",
                "description": "Paihia;Waitangi;Haruru;Russell",
                "shortDescription": null,
                "sortOrder": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "category": {
                "category": "Category",
                "code": "RD199C",
                "description": "Residential-Dwelling-1990's-poor",
                "shortDescription": null,
                "sortOrder": null,
                "isActive": true,
                "parentClassification": {
                    "category": "CategoryGroup",
                    "code": "U10",
                    "description": "Residential Dwellings",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "externalCode": null
            },
            "address": {
                "streetNumber": 40,
                "streetNumberSuffix": null,
                "streetName": "Wellington",
                "streetType": {
                    "category": "StreetType",
                    "code": "St",
                    "description": "Street",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "suburb": null,
                "town": "Russell",
                "streetAddress": "40 Wellington Street"
            },
            "apportionment": {
                "category": "Apportionment",
                "code": "5",
                "description": "APPORTIONMENT FOLLOWS",
                "shortDescription": null,
                "sortOrder": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "ownership": {
                "category": "Entity",
                "code": "1",
                "description": "Private: individual",
                "shortDescription": null,
                "sortOrder": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "tenure": {
                "category": "Tenure",
                "code": "1",
                "description": "Not Leased (Owner is Occupier)",
                "shortDescription": null,
                "sortOrder": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "rateability": {
                "category": "Rateability",
                "code": "1",
                "description": "Rateable",
                "shortDescription": null,
                "sortOrder": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "region": {
                "category": "Region",
                "code": "01",
                "description": "Northland Region",
                "shortDescription": null,
                "sortOrder": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "legalDescription": "LOT 2 DP 152980 - SUBJ TO QEII OPEN SPACE COVENANT",
            "certificateOfTitle": "NA91B/525",
            "natureOfImprovements": "DWG OI",
            "currentValuation": {
                "landValue": 5.6E+5,
                "capitalValue": 8.3E+5,
                "treesValue": 0
            },
            "revisedValuation": {
                "landValue": null,
                "capitalValue": null,
                "treesValue": 0
            },
            "landUseData": {
                "landZone": "9D",
                "landUse": {
                    "category": "LandUse",
                    "code": "91",
                    "description": "Single Unit excluding Bach",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "units": 1,
                "percentageSubdivisible": null,
                "carparks": null,
                "isMaoriLand": false,
                "buildingAge": {
                    "category": "BuildingAge",
                    "code": "1990",
                    "description": "1990-99",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "roofCondition": {
                    "category": "BuildingCondition",
                    "code": "A",
                    "description": "Average",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "wallCondition": {
                    "category": "BuildingCondition",
                    "code": "A",
                    "description": "Average",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "roofConstruction": {
                    "category": "BuildingConstruction",
                    "code": "I",
                    "description": "Steel/G-Iron",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "wallConstruction": {
                    "category": "BuildingConstruction",
                    "code": "W",
                    "description": "Weatherboard",
                    "shortDescription": null,
                    "sortOrder": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                },
                "buildingSiteCover": 60,
                "totalFloorArea": 100,
                "landArea": 0.248,
                "production": 0
            },
            "massAppraisalData": {
                "classifications": {
                    "classOfSurroundingImprovements": {
                        "category": "ClassOfSurroundingImprovements",
                        "code": "3",
                        "description": "Average Quality",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "lotPosition": {
                        "category": "LotPosition",
                        "code": "I",
                        "description": "Inside",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "contour": {
                        "category": "Contour",
                        "code": "EF",
                        "description": "Easy/Moderate Fall",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "landscapingQuality": {
                        "category": "LandscapingQuality",
                        "code": "A",
                        "description": "Average",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "view": {
                        "category": "View",
                        "code": "O",
                        "description": "Focal Point Of view - Other",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "viewScope": {
                        "category": "ViewScope",
                        "code": "M",
                        "description": "Moderate",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "houseType": {
                        "category": "HouseType",
                        "code": "CN",
                        "description": "Contemporary",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    }
                },
                "isModernised": false,
                "effectiveYearBuilt": 1998,
                "mainLivingArea": 50,
                "totalLivingArea": 87,
                "hasPoorFoundations": false,
                "hasDeck": true,
                "hasLaundry": false,
                "hasLargeOtherImprovements": false,
                "hasDriveway": true,
                "underMainRoofGarages": 0,
                "freestandingGarages": 0,
                "isOutlier": false,
                "bedrooms": null,
                "toilets": null,
                "hasCarAccess": true,
                "extensions": 2
            },
            "maoriLandData": {
                "numberOfOwners": null,
                "currentMaoriLandAdjustment": {
                    "multipleOwnerAdjustmentPercentage": null,
                    "siteSignificanceAdjustmentPercentage": null,
                    "unadjustedValuation": {
                        "landValue": null,
                        "capitalValue": null,
                        "treesValue": null
                    }
                },
                "revisedMaoriLandAdjustment": {
                    "multipleOwnerAdjustmentPercentage": null,
                    "siteSignificanceAdjustmentPercentage": null,
                    "unadjustedValuation": {
                        "landValue": null,
                        "capitalValue": null,
                        "treesValue": null
                    }
                }
            },
            "owners": [],
            "occupiers": [
                {
                    "firstName": "Helen",
                    "secondName": "Ruth",
                    "thirdName": null,
                    "lastName": "Ough Dealy",
                    "isNameSecret": false,
                    "initials": null,
                    "ownerOccupierType": "A",
                    "ownershipType": {
                        "category": "Entity",
                        "code": "1",
                        "description": "Private: individual",
                        "shortDescription": null,
                        "sortOrder": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": null
                    },
                    "daytimePhoneAreaCode": null,
                    "daytimePhone": null,
                    "mobilePhoneAreaCode": null,
                    "mobilePhone": null,
                    "emailAddress": null,
                    "mailingAddress": {
                        "careOf": null,
                        "organisation": null,
                        "unit": null,
                        "building": null,
                        "streetAddress": "40 Wellington Street",
                        "suburb": null,
                        "town": "Russell",
                        "postcode": "0202",
                        "country": null
                    },
                    "order": 1
                }
            ],
            "buildingNetRate": 2793,
            "revisedBuildingNetRate": null,
            "unadjustedBuildingNetRate": 2793,
            "coordinates": {
                "latitude": -35.2574,
                "longitude": 174.1179,
                "estimatedLocationAsQupid": null,
                "valid": true
            },
            "entityVersion": 11,
            "unadjustedValueOfImprovements": 2.7E+5,
            "valuationReference": "411/13900"
        },
        "photos": [
            [
                {
                    "id": "08eff2d7-074f-46be-9064-ba10680205ba",
                    "ownerId": "7e1a59eb-6b9e-4d42-bbf6-7b8924d7e7d6",
                    "category": "Property",
                    "isPrimary": true,
                    "internalOnlyYN": false,
                    "qivsPhotoId": "3349445_1",
                    "description": "",
                    "tags": [],
                    "mediaItem": {
                        "id": "c60a6703-1d86-4840-8323-ca72d82187ef",
                        "fileName": "411 13900.jpg",
                        "contentType": "image/jpeg",
                        "status": "UPLOAD_COMPLETED",
                        "captureDate": 1654128000000,
                        "uploadedDate": 1654041600000,
                        "tags": [
                            "10"
                        ],
                        "improvementDateRange": "",
                        "originalImageUrl": "https://qv-property-photos-migration-resized.s3.ap-southeast-2.amazonaws.com/c60a6703-1d86-4840-8323-ca72d82187ef/411%2013900.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250211T014424Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Expires=3600&X-Amz-Credential=AKIAJZ3RPMH5W2HCLL4A%2F20250211%2Fap-southeast-2%2Fs3%2Faws4_request&X-Amz-Signature=0f17355a5c1bd4ef8018827ea1a330322bef5c06164f389af39d0fc5d91fa203",
                        "smallImageUrl": "https://qv-property-photos-migration-resized.s3.ap-southeast-2.amazonaws.com/200x200/c60a6703-1d86-4840-8323-ca72d82187ef/411%2013900.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250211T014424Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Expires=3600&X-Amz-Credential=AKIAJZ3RPMH5W2HCLL4A%2F20250211%2Fap-southeast-2%2Fs3%2Faws4_request&X-Amz-Signature=e5c174cb582b05025dc2e288ab28f752447d1438c458eacfb9b435f44cfea5b2",
                        "smallQVImageUrl": "https://qv-property-photos-migration-resized.s3.ap-southeast-2.amazonaws.com/160x100/c60a6703-1d86-4840-8323-ca72d82187ef/411%2013900.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250211T014424Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Expires=3600&X-Amz-Credential=AKIAJZ3RPMH5W2HCLL4A%2F20250211%2Fap-southeast-2%2Fs3%2Faws4_request&X-Amz-Signature=eb82e3f953d097d395bbab7f75ae0fdc0f17fe1302831970ce245c3c682ee6c6",
                        "mediumImageUrl": "https://qv-property-photos-migration-resized.s3.ap-southeast-2.amazonaws.com/300x200/c60a6703-1d86-4840-8323-ca72d82187ef/411%2013900.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250211T014424Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Expires=3600&X-Amz-Credential=AKIAJZ3RPMH5W2HCLL4A%2F20250211%2Fap-southeast-2%2Fs3%2Faws4_request&X-Amz-Signature=7cde1839e38469cb2cbbb9cc75fe1cdb49b166ac89e5cd61910b5492dac07a5b",
                        "largeImageUrl": "https://qv-property-photos-migration-resized.s3.ap-southeast-2.amazonaws.com/1200x800/c60a6703-1d86-4840-8323-ca72d82187ef/411%2013900.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250211T014424Z&X-Amz-SignedHeaders=content-type%3Bhost&X-Amz-Expires=3600&X-Amz-Credential=AKIAJZ3RPMH5W2HCLL4A%2F20250211%2Fap-southeast-2%2Fs3%2Faws4_request&X-Amz-Signature=a811fd3ea9affe949f98d8ae6453af6dd9dfedc8ea67c010bbe7aff94ef66626"
                    }
                }
            ]
        ],
        "previousProperty": "438e7570-9293-4a71-9869-3ea9e024467d",
        "nextProperty": "53c8c5dc-a3aa-475b-9dea-c9ab3ca00d1f",
        "propertiesList": [
            {
                "id": "7e1a59eb-6b9e-4d42-bbf6-7b8924d7e7d6",
                "qpid": 3349445,
                "rollNumber": 411,
                "assessmentNumber": 13900,
                "suffix": ""
            },
            {
                "id": "1b94178c-acee-45ac-bfd0-962816a88c3f",
                "qpid": 3349446,
                "rollNumber": 411,
                "assessmentNumber": 13900,
                "suffix": "A"
            },
            {
                "id": "61dfccba-e6db-4703-9768-4169aeb0a313",
                "qpid": 3349447,
                "rollNumber": 411,
                "assessmentNumber": 13900,
                "suffix": "B"
            }
        ],
        "numberOfBathrooms": null
    };
}

function createMockPropertyDetail() {
    return {
        "id": "a58579e5-5412-484d-bab3-65c12274a4cb",
        "propertyId": "7e1a59eb-6b9e-4d42-bbf6-7b8924d7e7d6",
        "qpid": 3349445,
        "status": "PENDING",
        "category": {
            "category": "Category_DVR",
            "code": "RD199C",
            "description": "Residential-Dwelling-1990's-poor",
            "shortDescription": null,
            "isActive": true,
            "parentClassification": {
                "category": "CategoryGroup",
                "code": "U10",
                "description": "Residential Dwellings",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "externalCode": null
        },
        "grouping": null,
        "qvCategory": {
            "category": "QV_Category",
            "code": "RD199C ",
            "description": "RD199C  Residential-Dwelling-1990's-poor",
            "shortDescription": null,
            "isActive": true,
            "parentClassification": null,
            "externalCode": null
        },
        "planNumber": "9B",
        "propertyName": null,
        "description": null,
        "natureOfImprovements": [
            {
                "quantity": 1,
                "improvement": {
                    "category": "NatureOfImprovements_DVR",
                    "code": "DWG",
                    "description": "Dwelling",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                }
            },
            {
                "quantity": 1,
                "improvement": {
                    "category": "NatureOfImprovements_DVR",
                    "code": "OI",
                    "description": "Other Improvements",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": null
                }
            }
        ],
        "isOutlier": false,
        "site": {
            "landArea": 0.248,
            "effectiveLandArea": null,
            "lotPosition": {
                "category": "LotPosition_DVR",
                "code": "I",
                "description": "Inside",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "contour": {
                "category": "Contour_DVR",
                "code": "EF",
                "description": "Easy/Moderate Fall",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "view": {
                "category": "View_DVR",
                "code": "O",
                "description": "Focal Point Of view - Other",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "viewScope": {
                "category": "ViewScope_DVR",
                "code": "M",
                "description": "Moderate",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "hasDriveway": true,
            "hasCarAccess": true,
            "carparks": null,
            "classOfSurroundingImprovements": {
                "category": "ClassOfSurroundingImprovements_DVR",
                "code": "3",
                "description": "Average Quality",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "siteDevelopment": {
                "type": "OtherImprovement",
                "definition": null,
                "age": null,
                "quantity": null,
                "unitOfMeasure": null,
                "quality": {
                    "category": "FeatureQuality",
                    "code": "A",
                    "description": "Average",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": "A"
                },
                "description": null,
                "inferred": null,
                "collectionDate": null
            }
        },
        "landUse": {
            "landZone": {
                "category": "TA_1_LandZone_DVR",
                "code": "9D",
                "description": "9D",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "landUse": {
                "category": "LandUse_DVR",
                "code": "91",
                "description": "Single Unit excluding Bach",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "isMaoriLand": false,
            "production": 0
        },
        "summary": {
            "houseType": {
                "category": "HouseType_DVR",
                "code": "CN",
                "description": "Contemporary",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "units": 1,
            "totalBedrooms": null,
            "totalBathrooms": null,
            "totalToilets": null,
            "buildingSiteCover": 60,
            "totalFloorArea": 100,
            "totalLivingArea": 87,
            "mainLivingArea": 50,
            "age": {
                "category": "Age_DVR",
                "code": "1990",
                "description": "1990-99",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "effectiveYearBuilt": 1998,
            "hasPoorFoundations": false,
            "hasLaundryOrWorkshop": false,
            "actualRentPerWeek": null,
            "actualRentKnownDate": null
        },
        "ruralDetail": {
            "qualityRating": null,
            "farmedWith": [],
            "irrigationSourceConsents": [],
            "irrigationTypeConsents": [],
            "waterQualityRating": null,
            "waterStorageType": null,
            "waterStorageSize": 0,
            "irrigationLinkedWith": [],
            "nutrientManagementConsents": [],
            "fences": [],
            "fruit": []
        },
        "commercialDetail": {
            "propertyGroupingTypeCommercial": null
        },
        "buildings": [
            {
                "buildingLabel": "D1",
                "isPrimaryBuilding": true,
                "buildingType": {
                    "category": "BuildingType",
                    "code": "DW",
                    "description": "Dwelling",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": "DWG"
                },
                "description": null,
                "numberOfStoreys": 2,
                "otherFeatures": null,
                "yearBuilt": 1998,
                "floorArea": 100,
                "floorConstruction": {
                    "type": "ComplexFeature",
                    "definition": [
                        {
                            "category": "FloorConstruction_DVR",
                            "code": "500",
                            "description": "Concrete Slab",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        },
                        {
                            "category": "FloorConstruction_DVR",
                            "code": "502",
                            "description": "Particle Board",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        }
                    ],
                    "age": null,
                    "quality": null,
                    "inferred": null,
                    "collectionDate": null
                },
                "foundation": {
                    "type": "ComplexFeature",
                    "definition": [
                        {
                            "category": "Foundation_DVR",
                            "code": "200",
                            "description": "Concrete Slab",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        }
                    ],
                    "age": null,
                    "quality": null,
                    "inferred": null,
                    "collectionDate": null
                },
                "wallConstruction": {
                    "type": "ComplexFeature",
                    "definition": [
                        {
                            "category": "WallConstruction_DVR",
                            "code": "311",
                            "description": "Weatherboard",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": "W"
                        }
                    ],
                    "age": null,
                    "quality": {
                        "category": "FeatureQuality",
                        "code": "A",
                        "description": "Average",
                        "shortDescription": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": "A"
                    },
                    "inferred": null,
                    "collectionDate": null
                },
                "roofConstruction": {
                    "type": "ComplexFeature",
                    "definition": [
                        {
                            "category": "RoofConstruction_DVR",
                            "code": "405",
                            "description": "Steel/G-Iron",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": "I"
                        }
                    ],
                    "age": null,
                    "quality": {
                        "category": "FeatureQuality",
                        "code": "A",
                        "description": "Average",
                        "shortDescription": null,
                        "isActive": true,
                        "parentClassification": null,
                        "externalCode": "A"
                    },
                    "inferred": null,
                    "collectionDate": null
                },
                "glazing": null,
                "insulation": null,
                "plumbing": null,
                "wiring": null,
                "spaces": [
                    {
                        "type": "LivingSpace",
                        "spaceType": {
                            "category": "SpaceType",
                            "code": "LI",
                            "description": "Living",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        },
                        "spaceLabel": null,
                        "numberOfSimilarSpaces": 1,
                        "floorArea": 87,
                        "quality": null,
                        "modernisationAge": {
                            "category": "ModernisationAge",
                            "code": "ORIG",
                            "description": "Original",
                            "shortDescription": null,
                            "isActive": true,
                            "parentClassification": null,
                            "externalCode": null
                        },
                        "heating": null,
                        "totalBedrooms": null,
                        "singleBedrooms": null,
                        "doubleBedrooms": null,
                        "homeOfficeOrStudy": null,
                        "kitchen": {
                            "type": "Room",
                            "age": null,
                            "quality": null,
                            "description": null,
                            "hasRoom": null,
                            "inferred": null,
                            "collectionDate": null
                        },
                        "totalBathrooms": null,
                        "totalToilets": null,
                        "mainBathroom": {
                            "type": "Room",
                            "age": null,
                            "quality": null,
                            "description": null,
                            "hasRoom": null,
                            "inferred": null,
                            "collectionDate": null
                        },
                        "ensuite": {
                            "type": "Room",
                            "age": null,
                            "quality": null,
                            "description": null,
                            "hasRoom": null,
                            "inferred": null,
                            "collectionDate": null
                        }
                    }
                ]
            }
        ],
        "otherImprovements": [
            {
                "type": "OtherImprovement",
                "definition": {
                    "category": "OtherImprovement",
                    "code": "DU",
                    "description": "Deck - Uncovered",
                    "shortDescription": null,
                    "isActive": true,
                    "parentClassification": null,
                    "externalCode": "DECK"
                },
                "age": null,
                "quantity": null,
                "unitOfMeasure": null,
                "quality": null,
                "description": null,
                "inferred": null,
                "collectionDate": null
            }
        ],
        "asAtDateTime": null,
        "audit": {
            "lastUpdatedDateTime": "2025-02-10T00:22:51.504Z",
            "lastUpdatedUser": null,
            "outputCode": null,
            "source": null,
            "reasonForChange": null
        },
        "isStale": false,
        "propertyNotes": {
            "text": null,
            "enteredDate": null,
            "updateReason": null
        },
        "ratingApportionments": null,
        "entityVersion": 6,
        "qivsImprovementsStatus": "IMPROVEMENTS",
        "dvrSnapshot": {
            "numberOfUnderMainRoofGarages": 0,
            "numberOfFreestandingGarages": 0,
            "hasDeck": true,
            "hasLargeOtherImprovements": false,
            "wallConstruction": {
                "category": "BuildingConstruction",
                "code": "W",
                "description": "Weatherboard",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "wallCondition": {
                "category": "BuildingCondition",
                "code": "A",
                "description": "Average",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "roofConstruction": {
                "category": "BuildingConstruction",
                "code": "I",
                "description": "Steel/G-Iron",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "roofCondition": {
                "category": "BuildingCondition",
                "code": "A",
                "description": "Average",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "isModernised": false,
            "natureOfImprovements": "DWG OI",
            "landscapingQuality": {
                "category": "LandscapingQuality_DVR",
                "code": "A",
                "description": "Average",
                "shortDescription": null,
                "isActive": true,
                "parentClassification": null,
                "externalCode": null
            },
            "changedDvrFields": []
        }
    };
}

function createMockDvrSnapshot() {
    return {};
}

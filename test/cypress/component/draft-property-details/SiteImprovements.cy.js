import { SiteImprovements as Component } from '@/components/propertyDetails/residential/index.js';
import Model from '../../model/DraftPropertyDetails';
import { ref } from 'vue';
import { createMockStore } from '../../support/store.js';

describe('SiteImprovements', () => {
    it('should render the SiteImprovements', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
        });

        Model.SiteImprovements.otherImprovements.should('exist');
        Model.SiteImprovements.quality.should('exist');
        Model.SiteImprovements.description.should('exist');
        Model.SiteImprovements.addImprovementBtn.should('exist');
    });

    it('emits changed event when site developements are updated', () => {
        const store = createMockStore();
        let propsData = getMockPropertyDetails();
        store.commit('classificationsLoaded', true);
        const onUpdate = cy.spy().as('onUpdate');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            listeners: {
                'update:site-development': onUpdate,
            },
        });

        Model.SiteImprovements.description.type('--test--');
        cy.get('@onUpdate').should('have.been.called', 1);
    });

});

function getMockPropertyDetails() {
    const siteDevelopment = ref({
        type: 'OtherImprovement',
        definition: null,
        age: null,
        quantity: null,
        unitOfMeasure: null,
        quality: {
            category: 'FeatureQuality',
            code: 'P',
            description: 'Poor',
            shortDescription: null,
            isActive: true,
            parentClassification: null,
            externalCode: 'P'
        },
        description: null,
        inferred: null,
        collectionDate: null
    });
    const otherImprovements = ref([]);
    return {
        siteDevelopment,
        otherImprovements,
    };
}

import InputNumber from '../../../vue/components/common/form/InputNumber.vue';
import { ref } from 'vue';

describe('InputNumber.cy.js', () => {
    it('shows raw value when focused', () => {
        cy.mount(InputNumber, {
            propsData: {
                value: 10000,
            },
        });

        cy.get('input').focus();
        cy.get('input').should('have.value', 10000);
    });

    it('shows formatted value when not focused', () => {
        cy.mount(InputNumber, {
            propsData: {
                value: 10000,
            },
        });

        cy.get('input').should('have.value', '$10,000');
    });

    it('typing emits input event with new value', () => {
        const onInput = cy.spy().as('onInput');
        const value = ref(0);
        const newValue = 10000;

        cy.mount(InputNumber, {
            propsData: {
                value,
            },
            listeners: {
                input: onInput
            }
        })

        cy.get('input').focus();
        cy.get('input').type(newValue).trigger('change');
        cy.get('@onInput').should('have.been.calledWith', newValue);
    });

    context('when readonly', () => {
        it('has disabled input', () => {
            cy.mount(InputNumber, {
                propsData: {
                    readonly: true,
                    value: 10000,
                },
            });

            cy.get('input').should('be.disabled');
        });

        it('is removed from tabindex', () => {
            cy.mount(InputNumber, {
                propsData: {
                    readonly: true,
                    value: 10000,
                },
            });

            cy.get('input').should('have.attr', 'tabindex').should('equal', '-1');
        })
    });

    context('when errored', () => {
        it('displays error message', () => {
            const errorMessage = 'This is an error message';
            cy.mount(InputNumber, {
                propsData: {
                    value: 10000,
                    errors: [
                        errorMessage,
                    ],
                },
            });

            cy.get('[data-cy="input-error-message"]').contains(errorMessage);
        });
    })
});

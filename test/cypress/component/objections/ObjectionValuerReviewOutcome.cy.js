import { ObjectionValuerReviewOutcome as Component } from '@/components/rollMaintenance/objections';

describe('ObjectionValuerReviewOutcome', () => {
    it('should correctly display objection valuer review outcome', () => {

        const propsData = {
            review: {
                reviewerFullName: "reviewerFullName",
                relevantSalesUsed: "relevantSalesUsed",
                valuesAreAppropriate: "valuesAreAppropriate",
                compliesWithOvg: "compliesWithOvg",
                primaryPhotoUpdated: "primaryPhotoUpdated",
                reviewPassed: true,
                reviewFailureReason: "reviewFailureReason",
            }
        };

        cy.mount(Component, {
            propsData,
        });

        cy.get(':nth-child(2) > :nth-child(2) > .qv-flex-row > .qv-font-semibold')
            .should('contain.text', 'PASS');

        cy.get(':nth-child(3) > .qv-flex-row > .qv-font-semibold')
            .should('contain.text', 'PASS');

        cy.get(':nth-child(4) > .qv-flex-row > .qv-font-semibold')
            .should('contain.text', 'PASS');

        cy.get(':nth-child(5) > .qv-flex-row > .qv-font-semibold')
            .should('contain.text', 'PASS');

    });
});

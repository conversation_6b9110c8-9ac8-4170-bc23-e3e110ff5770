import { ObjectionValuerReview as Component } from '@/components/rollMaintenance/objections';
import { computed } from 'vue';
import { createObjectionsClassificationsStore } from './mocks.js';
import { ValidationSet } from '@quotable-value/validation';

let store;

describe('ObjectionValuerReview', () => {

    before(() => {
        store = createObjectionsClassificationsStore();
        store.commit('classificationsLoaded', true);
    });

    it('should correctly display objection valuer review', () => {

        const validationSet = new ValidationSet();
        const propsData = {
            currentReview : "currentReview",
            ratingValuationId: "ratingValuationId",
            requestingReview: "requestingReview",
            latestReview: "latestReview",
            validationSet
        };
        const sendToReview = cy.spy().as('sendToReview');

        cy.mount(Component, {
            propsData,
            emitSpy: true,
            store,
            provide: {
                valuersLoaded: computed(() => true),
                valuers: [{ id: 'Assigned', name: 'Assigned', ntUsername: 'Assigned' }],
                registeredValuers: [{ id: 'Assigned', name: 'Assigned', ntUsername: 'Assigned' }],
                isValuerChanged: computed(() => false),
                isRegisteredValuerChanged: computed(() => false),
                isAssignedValuerRegistered: computed(() => true),
                isCurrentUserAdmin: computed(() => true),
                isJobReadOnly: computed(() => false),
                isActionRecord: computed(() => false),
                isCurrentUserRegisteredValuerAssigned: computed(() => true),
                linkedObjection: computed(() => ({
                    valJobStatus: 'Ready to Value',
                    valuer: { id: 'Assigned', name: 'Assigned', ntUsername: 'Assigned' },
                    registeredValuer: { id: 'Assigned', name: 'Assigned', ntUsername: 'Assigned' }
                })),
            },
            listeners: {
                'sendToReview': sendToReview,
            }
        });

        cy.get('.mdl-button').click();
        cy.get('[style="list-style: inside;"] > :nth-child(1)')
            .should('contain.text', 'Notes for Registered Valuer have not been entered');
        cy.get('[style="list-style: inside;"] > :nth-child(2)')
            .should('contain.text', 'A Risk option has not been selected');
        cy.get('[data-cy="noWorksheetModalCancelButton"]').click();

        cy.get('textarea').type("Notes for registered valuer");
        cy.get('.mdl-button').click();
        cy.get('[style="list-style: inside;"] > :nth-child(1)')
            .should('contain.text', 'A Risk option has not been selected');
        cy.get('[data-cy="noWorksheetModalCancelButton"]').click();

        cy.get('.multiselect__tags').click();
        cy.get('.multiselect__tags').type("1\n");

        cy.get('.mdl-button').click();
        cy.get('@sendToReview').should('have.been.called', 1);
    });
});

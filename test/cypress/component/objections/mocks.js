import { createMockStore } from '../../support/store.js';

export function createObjectionsClassificationsStore() {
    const classifications = {
        ObjectionJobInspectionType: [
            {
                category: "ObjectionJobInspectionType",
                id: 1,
                code: 1,
                description: "Internal",
            },
            {
                category: "ObjectionJobInspectionType",
                id: 2,
                code: 2,
                description: "External",
            },
            {
                category: "ObjectionJobInspectionType",
                id: 3,
                code: 3,
                description: "Roadside",
            },
        ],
        ObjectionJobObjectorContactType: [
            {
                category: "ObjectionJobObjectorContactType",
                id: 1,
                code: 1,
                description: "On site",
            },
            {
                category: "ObjectionJobObjectorContactType",
                id: 2,
                code: 2,
                description: "By phone",
            },
            {
                category: "ObjectionJobObjectorContactType",
                id: 3,
                code: 3,
                description: "By email",
            },
            {
                category: "ObjectionJobObjectorContactType",
                id: 4,
                code: 4,
                description: "Other",
            },
        ],
        ObjectionJobFurtherContactReasonType: [
            {
                category: "ObjectionJobFurtherContactReasonType",
                id: 1,
                code: 1,
                description: "15% Decision Variance CV",
            },
            {
                category: "ObjectionJobFurtherContactReasonType",
                id: 2,
                code: 2,
                description: "15% Decision Variance LV",
            },
            {
                category: "ObjectionJobFurtherContactReasonType",
                id: 3,
                code: 3,
                description: "Objector Requested",
            },
            {
                category: "ObjectionJobFurtherContactReasonType",
                id: 4,
                code: 4,
                description: "Other",
            },
        ],
    };

    return createMockStore(classifications);
}

import { ObjectionContact as Component } from '@/components/rollMaintenance/objections';
import { computed } from 'vue';
import { createObjectionsClassificationsStore } from './mocks.js';

let store;

describe ('ObjectionContact', () => {

    before(() => {
        store = createObjectionsClassificationsStore();
        store.commit('classificationsLoaded', true);

        const GET_CONTACT_MOCK_ENDPOINT = '/getObjectionContact';
        const POST_CONTACT_MOCK_ENDPOINT = '/postObjectionContact';
        window.jsRoutes.controllers = {
            ApiPicklistController : {
                getObjectionContact : () => ({url: GET_CONTACT_MOCK_ENDPOINT}),
                updateObjectionContact : () => ({url: POST_CONTACT_MOCK_ENDPOINT}),
            },
        };

        cy.intercept(GET_CONTACT_MOCK_ENDPOINT, (req) => {
            req.reply(200,
                {
                    "status": "SUCCESS",
                    "objectorContact": {
                        "valuer": "Assigned",
                        "registeredValuer": "Assigned",
                        "valuerNTUserName": "Assigned",
                        "registeredValuerNTUserName": "Assigned",
                        "workDone": false,
                        "inspectionTypeId": 1,
                        "inspectionDate": "2023-11-21T11:00:00.000Z",
                        "qivsComment": "Other",
                        "contacted": false,
                        "furtherContactNotes": "",
                        "furtherContactTypes": [
                            {
                                "furtherContactReasonTypeId": 1
                            },
                            {
                                "furtherContactReasonTypeId": 2
                            }
                        ],
                        "objectionJobObjectorContact": [
                            {
                                "id": 3171,
                                "ratingValuationId": "2D03F7CA-7A5B-4950-9802-485BB27EE3F6",
                                "contactTypeId": 1,
                                "contactNotes": null,
                                "contactDate": "2023-11-21T11:00:00.000Z"
                            }
                        ]
                    }
                }
            )
        }).as('getObjectionContact');

        cy.intercept(POST_CONTACT_MOCK_ENDPOINT, (req) => {
            req.reply(200,
                {
                    "status": "SUCCESS",
                }
            )
        }).as('updateObjectionContact');

    });

    it('should correctly display objection contact', () => {

        const propsData = {
            objection: computed (() => ({
                objectionId: "OBJ-123",
                valJobStatus: 'Ready to Value',
                valuer: { id: 'Assigned', name: 'Assigned', ntUsername: 'Assigned' },
                registeredValuer: { id: 'Assigned', name: 'Assigned', ntUsername: 'Assigned' }
            })),
            loaded: true,
            ratingValuationId: '123-45-67-89',
            latestReview: {},
            furtherContactWarnings: [],
        };

        cy.mount(Component, {
            propsData,
            store,
            provide: {
                valuersLoaded: computed(() => true),
                valuers: computed(() => ([{ id: 'Unassigned', name: 'Unassigned', ntUsername: 'Unassigned' }, { id: 'Assigned', name: 'Assigned', ntUsername: 'Assigned' }])),
                registeredValuers: computed(() => ([{ id: 'Unassigned', name: 'Unassigned', ntUsername: 'Unassigned' }, { id: 'Assigned', name: 'Assigned', ntUsername: 'Assigned' }])),
                isCurrentUserAdmin: computed(() => true),
                isJobReadOnly: computed(() => false),
                isActionRecord: computed(() => false),
                isCurrentUserRegisteredValuerAssigned: computed(() => true),
                isCurrentUserValuerAssigned: computed(() => true),
                isAssignedValuerRegistered: computed(() => true),
                isValuerChanged: computed(() => false),
                isRegisteredValuerChanged: computed(() => false),
            },
        });

        cy.wait('@getObjectionContact');

        // Inspection Type
        cy.get(':nth-child(1) > .col-5 > label > .qv-validation-wrapper > .multiselect > .multiselect__tags').click();
        cy.get(':nth-child(1) > .col-5 > label > .qv-validation-wrapper > .multiselect > .multiselect__tags').type("1\n");

        // Objector Contact
        cy.get(':nth-child(2) > .col-5 > label > .qv-validation-wrapper > .multiselect > .multiselect__tags').click();
        cy.get(':nth-child(2) > .col-5 > label > .qv-validation-wrapper > .multiselect > .multiselect__tags').type("2\n");

        // Further Objector Contact Required
        cy.get(':nth-child(3) > .col-5 > label > .qv-validation-wrapper > .multiselect > .multiselect__tags').click();
        cy.get(':nth-child(3) > .col-5 > label > .qv-validation-wrapper > .multiselect > [name="multiselect"] > .multiselect__content-wrapper > #listbox-null > #null-0 > .multiselect__option').click();

        // Further contact notes
        cy.get('#furtherContactNotes').should('have.value', '');
        cy.get('#furtherContactNotes').type("Further Contact Notes");
        cy.get('#furtherContactNotes').blur();
        cy.get('#furtherContactNotes').should('have.value', 'Further Contact Notes');


        cy.get('[data-cy="objection-save-draft-button"]').click();
        cy.wait('@updateObjectionContact')
            .then((interception) => {
                expect(interception.response.statusCode).to.equal(200);
            });
    });

});

import AdvancedSearch from '../../model/AdvancedSearch';
import AdvancedSearchSales from '../../model/AdvancedSearchSales';
import Home from '../../model/Home';
import PropertyDetails from '../../model/PropertyDetails';
import PropertySearchResults from '../../model/PropertySearchResults';

describe('Warning for new rural sale analysis on property without worksheet', () => {
    before(() => {
        Home.visit();
    });

    context('New sale analysis from sales search result', () => {
        it('Found result for a rural property without a worksheet', () => {
            Home.advancedSearchButton.click();
            AdvancedSearch.salesSearch.click();
            AdvancedSearch.streetNumFrom.clear().type(env.ruralNoWorksheet.situationNumber);
            AdvancedSearch.streetNumTo.clear().type(env.ruralNoWorksheet.situationNumber);
            AdvancedSearch.streetName.clear().type(env.ruralNoWorksheet.streetName);
            AdvancedSearchSales.nspFrom.clear().type(env.ruralNoWorksheet.nsp);
            AdvancedSearchSales.nspTo.clear().type(env.ruralNoWorksheet.nsp);
            AdvancedSearchSales.includeCategory1Sales.clear().type(env.ruralNoWorksheet.category);
            AdvancedSearch.search();
            cy.wait(3000) //need this or results undefined
            PropertySearchResults.results.should('not.be.undefined').and('have.length', 1);
        });

        it('Search results expanded', () => {
            PropertySearchResults.expandResultsButton.click();
            PropertySearchResults.results.each(result => {
                cy.wrap(result).should('have.class', PropertySearchResults.expandedClass);
            });
        });

        it('Sales analysis button present', () => {
            PropertySearchResults.results.each(result => {
                cy.wrap(result)
                    .find(PropertySearchResults.saleAnalysisSelector)
                    .should('exist')
                    .and('be.visible');
            });
        });

        context('Warning displayed after clicking analysis link', () => {
            it('Warning modal present', () => {
                PropertySearchResults.results.first(result => {
                    cy.wrap(result)
                        .find(PropertySearchResults.saleAnalysisSelector)
                        .click({ force: true });
                    cy.get('[data-cy="noWorksheetModalHeading"]')
                        .should('exist')
                        .and('be.visible');
                    cy.get('[data-cy="noWorksheetModalMessage"]')
                        .should('exist')
                        .and('be.visible');
                    cy.get('[data-cy="noWorksheetModalContinueButton"]')
                        .should('exist')
                        .and('be.visible');
                    cy.get('[data-cy="noWorksheetModalCancelButton"]')
                        .should('exist')
                        .and('be.visible');
                });
            });

            it('Warning disappeared after clicking cancel button', () => {
                PropertySearchResults.results
                    .first()
                    .find(PropertySearchResults.saleAnalysisSelector)
                    .click({ force: true });
                cy.get('[data-cy="noWorksheetModalCancelButton"]').click();
                cy.get('[data-cy="noWorksheetModalHeading"]').should('not.exist');
            });
        });

        context('Warning displayed after clicking analysis link', () => {
            it('Warning heading present', () => {
                PropertySearchResults.results
                    .first()
                    .find(PropertySearchResults.saleAnalysisSelector)
                    .click({ force: true });
                cy.get('[data-cy="noWorksheetModalHeading"]')
                    .should('exist')
                    .and('be.visible');
            });

            context('Navigated to master details after clicking continue button', () => {
                it('Qpid present', () => {
                    cy.get('[data-cy="noWorksheetModalContinueButton"]').click();
                    PropertyDetails.qpid
                        .should('exist')
                        .and('be.visible')
                        .and('contain.text', env.ruralNoWorksheet.qpid);
                });

                it('Warning no longer present', () => {
                    cy.get('[data-cy="noWorksheetModalHeading"]').should('not.exist');
                });

                it('Address present', () => {
                    PropertyDetails.addressLine1.should(
                        'contain.text',
                        `${env.ruralNoWorksheet.situationNumber} ${env.ruralNoWorksheet.streetName}`
                    );
                });
            });
        });
    });
});

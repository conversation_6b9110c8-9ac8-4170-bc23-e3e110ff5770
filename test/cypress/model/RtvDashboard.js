export default {
    get dashboard() {
        return cy.get('body').then(body => {
            const dashboards = body.find('div[data-cy=rtvDashboard]');
            if (dashboards && dashboards.length > 0) {
                return dashboards[0];
            }
            return null;
        });
    },
    get primaryToolbar() {
        return this.dashboard.then(dashboard => {
            const toolbars = dashboard.find('div[data-cy=rtvDashboardPrimaryToolbar]');
            if (toolbars && toolbars.length > 0) {
                return toolbars[0];
            }
            return null;
        });
    },
    get dashboardHeading() {
        return this.primaryToolbar.then(toolbar => {
            const headings = toolbar.find('span[data-cy=rtvDashboardHeading]');
            if (headings && headings.length > 0) {
                return headings[0];
            }
            return null;
        });
    },
    get ruralIndexLink() {
        return this.primaryToolbar.then(toolbar => {
            const links = toolbar.find('li[data-cy=rtvDashboardRuralIndexLink]');
            if (links && links.length > 0) {
                return links[0];
            }
            return null;
        });
    },
    get qivsLink() {
        return this.primaryToolbar.then(toolbar => {
            const links = toolbar.find('li[data-cy=rtvDashboardQivsLink]');
            if (links && links.length > 0) {
                return links[0];
            }
            return null;
        });
    },
};

/* global cy it context */
import { get } from 'lodash';
import Home from './Home';
import PropertyDetails from './PropertyDetails';


const stepperItems = [
    'jobSetup',
    'propertyDetails',
    'comparableProperties',
    'valuation',
    'reportDetails',
    'locationDetails',
    'photoAndAttachments',
    'qaAndReview'
];

export default {
    elements: {
        get valuationJobErrorAlert() {
            return cy.get('.valuationJobError > .alert');
        },
        get valuationJobErrorCancelButton() {
            return cy.get('.valuationJobError > .alert > .alertButtons > #errorCancel');
        },
        get selectValuerChosen() {
            return cy.get('.required > .multiselect-native-select > .btn-group > .multiselect > .multiselect-selected-text');
        },
        get purposeOfValuationChosen() {
            return cy.get('#jobSetupReportDetailsPurposeOfValuation > .btn-group > .multiselect > .multiselect-selected-text');
        },
        get peerReviewChosen() {
            return cy.get('#jobSetupReportDetailsPeerReview > .btn-group > .multiselect > .multiselect-selected-text');
        },
        get changeHouseTypeInput() {
            return cy.get('#tabsElement > .QVHVTab-1 > span');
        },
    },

    // * steppers
    stepperItems,

    get setupHeader() {
        return cy.contains('h2', 'Set-up Valuation Job');
    },

    get stepper() {
        return cy.get('ul.QVHV-stepper');
    },

    get jobSetupStep() {
        return this.stepper.find('li[data-step="jobSetup"]');
    },
    get uploadButton() {
        return cy.get('input[type=file]');
    },
    get uploadConfirm() {
        return cy.get('[data-cy="upload-confirm"]');
    },
    get addDetailsButton() {
        return cy.get('[data-cy="add-details"]');
    },
    get jobId() {
        return cy.get('[data-cy="add-details"]').invoke('attr', 'data-href');
    },
    get primaryPhotos() {
        return cy.get('[data-cy="primary-photos"]').find('input');
    },
    get internalUseOnly() {
        return cy.get('[data-cy="internal-use-only"]').find('input');
    },
    get includeInInsurance() {
        return cy.get('[data-cy="include-in-insurance"]').find('input');
    },
    get tagPicker() {
        return cy.get('[data-cy="tag-picker"]').find('span');
    },
    get photosIncluded() {
        return cy.get('[data-cy="photos-included"]').find('input');
    },
    get attachmentTab() {
        return cy.get('[data-cy="attachments-tab"]');
    },
    get saveAndClose() {
        return cy.get('#save-and-close');
    },
    get aerialAndLocationTab() {
        return cy.get('[data-cy="aerial-and-location-tab"]');
    },
    get notesAndFinalReportsTab() {
        return cy.get('[data-cy="notes-and-final-reports"]');
    },
    get uploadForNotesAndFinalReports() {
        return cy.get('#photo-dropzone-HomeValuationReport > .dz-default > .fileUploader-button > .mdl-button');
    },
    get photoAndAttachmentStepper() {
        return cy.get('li[data-step="photoAndAttachments"]');
    },
    get qaAndReviewStepper() {
        return cy.get('li[data-step="qaAndReview"]');
    },
    stepperItem(item) {
        return this.stepper.find(`li[data-step="${item}"]`);
    },
    stepperItemClass(item) {
        return cy.get(`.${item}Stepper`);
    },
    nextStep(page) {
        return cy.get(`.${page} > .QVHV-buttons > .QVHV-buttons-right > .primary`);
    },
    // * setup page getters
    get valuationReportTypeMultiselect() {
        return cy.get('#jobSetupReportDetailsReportType > .btn-group > .multiselect');
    },
    get marketValuationReportType() {
        return cy.get(
            '#jobSetupReportDetailsReportType > .btn-group > .multiselect-container > :nth-child(10) > a > .radio'
        );
    },
    get verifiedQVReportType() {
        return cy.get(
            '#jobSetupReportDetailsReportType > .btn-group > .multiselect-container > :nth-child(13) > a > .radio'
        );
    },
    get verifiedQVBankReportType() {
        return cy.get(
            '#jobSetupReportDetailsReportType > .btn-group > .multiselect-container > :nth-child(14) > a > .radio'
        );
    },
    get inspectionDateAndTimeText() {
        return cy.get('[rangeid="inspectionDateTime"] > .advSearch-text'); //cy.get(".valError > .advSearch-text");
    },
    get clientNameTextField() {
        return cy.get('#jobSetupReportDetailsClientName');
    },
    get instructedByTextField() {
        return cy.get('#jobSetupReportDetailsInstructedBy');
    },
    get valuerMultiselect() {
        return cy.get('.valError > .multiselect-native-select > .btn-group > .multiselect');
    },
    get purposeOfValuationMultiselect() {
        return cy.get('#jobSetupReportDetailsPurposeOfValuation > .btn-group > .multiselect');
    },
    get peerReviewMultiselect() {
        return cy.get('#jobSetupReportDetailsPeerReview > .btn-group > .multiselect');
    },
    get completeSetup() {
        return cy.get('.jobSetup > .QVHV-buttons > .QVHV-buttons-right > .secondary');
    },
    get confirmationClose() {
        return cy.get('#confirmationFramework > .alert > .alertButtons > #confirmationClose');
    },
    get confirmationCancel() {
        return cy.get('#confirmationFramework > .alert > .alertButtons > #confirmationCancel');
    },
    get exportsResults() {
        return cy.get('[data-cy="valuation-export-results-button"]');
    },
    get alertModalViewMyReport() {
        return cy.getBySel('alert-modal').children().eq(3).find('button').eq(0);
    },
    get alertModal() {
        return cy.getBySel('alert-modal').children();
    },
    get advanceSearchGroup() {
        return cy.get('.resultsTitle > .advSearch-group');
    },
    get checkBox() {
        return cy.get('.multiselect-native-select > .btn-group > .multiselect-container > .multiselect-item.multiselect-all > .multiselect-all > .checkbox');
    },
    get multiSelectSelectedText() {
        return cy.get('.resultsTitle > .advSearch-group > .multiselect-native-select > .btn-group > .multiselect > .multiselect-selected-text');
    },

    // * setup page functions

    selectMarketValuationReport() {
        this.valuationReportTypeMultiselect.click();
        this.marketValuationReportType.click();
        this.valuationReportTypeMultiselect.should('have.text', 'Market Valuation ');
    },

    selectVerifiedQVReport() {
        this.valuationReportTypeMultiselect.click();
        this.verifiedQVReportType.click();
        this.valuationReportTypeMultiselect.should('have.text', 'Verified QV ');
    },

    // TODO: needed for bank report type
    selectVerifiedQVBankReport() {
        this.valuationReportTypeMultiselect.click();
        this.verifiedQVBankReportType.click();
    },

    selectTodaysDateInspectionDate() {
        this.inspectionDateAndTimeText.click();
        cy.get('.show-calendar > .ranges > .range_inputs > .applyBtn').click();
    },

    selectValuer(firstName, lastName) {
        this.valuerMultiselect.click();
        const elemGet = `#label-Valuers-${firstName}\\ ${lastName} > a > .checkbox`;
        cy.get(elemGet).click();
    },

    selectPurposeOfValuation() {
        this.purposeOfValuationMultiselect.click();
        cy.get('#jobSetupReportDetailsPurposeOfValuation')
            .contains('label', 'Market Value')
            .click();
    },

    selectPeerReview() {
        this.peerReviewMultiselect.click();
        cy.get('#jobSetupReportDetailsPeerReview')
            .contains('label', 'Peer Review Not Required')
            .click();
    },

    // * Property details page getters

    get houseTypeMultiselect() {
        return cy.get('#overviewHouseType > .btn-group > .multiselect');
    },

    // * Property details page functions

    changeHouseType() {
        this.houseTypeMultiselect.click();
        cy.get(
            '#overviewHouseType > .btn-group > .multiselect-container > :nth-child(7) > a > .radio'
        ).click({ force: true });
    },

    // * Comparable properties page getters

    get comparablePropertiesList() {
        return cy.get('.resultsInner-wrapper');
    },

    get comparablePropertyNext() {
        return cy.get('.selectComps-confirm > .material-icons');
    },

    get enterSaleIDField() {
        return cy.get('#comparablePropertiesSaleIdText');
    },

    get enterSaleIDButton() {
        return cy.get('#comparablePropertiesSaleIdButton');
    },

    get firstPropertyCheckbox() {
        return cy.get(':nth-child(3) > .compControls > :nth-child(1) > label');
    },

    get expandComparableSalesButton() {
        return cy.get('[title="Show selected comps"]');
    },

    get exportComparableSalesButton() {
        return cy.get('[title="Export comps"] > .material-icons');
    },

    get qivsSaleButton() {
        return cy.get(':nth-child(3) > .qivsLinks > .md-qivs');
    },
    get mapButton() {
        return cy.get(':nth-child(3) > .qivsLinks > :nth-child(3)');
    },
    get webButton() {
        return cy.get(':nth-child(3) > .qivsLinks > :nth-child(4)');
    },
    get salesAnalysisButton() {
        return cy.get(':nth-child(3) > .qivsLinks > .md-salesAnalysis');
    },

    // * Comparable properties page functions

    enterSaleID(input) {
        this.enterSaleIDField.type(input);
        this.enterSaleIDButton.click({ force: true });
    },

    // * Report Details page getters

    get marketCommentsMultiselect() {
        return cy.get(
            '.valuationconclusion > .QVHV-formSection > :nth-child(1) > .fiftyPct > .btn-group > .multiselect'
        );
    },

    // * Report Details page functions

    // * Photos & Attachments page getters

    get fileUploadButton() {
        return cy.get('#photo-dropzone-HomeValuationPhoto > .dz-default > h3');
    },

    get fileUploadDropzone() {
        return cy.get('#photo-dropzone-HomeValuationPhoto');
    },

    get attachmentsTab() {
        return cy.get('#attachmentsTab > span');
    },

    // * Photos & Attachments page functions

    confirmFile(valref, jobId, counter) {
        cy.doubleLog(jobId);
        cy.get('.uploadConfirm > .material-icons').click();
        cy.get('.includePhoto > label > h3').click();
        // cy.get(".QVHV-buttons > .primary").click();
        // NOTE: simulates the new window opening
        cy.visit(`${env.config.baseUrl}?jobId=${jobId}`, {
            onBeforeLoad(win) {
                cy.stub(win, 'open');
            }
        });

        cy.get('.detailsPrimary > label > span').click();
        // cy.get("#Primary\\ Photo").click();
        cy.get(':nth-child(1) > :nth-child(2) > label').click({ force: true });
        cy.wait(5000);
        cy.get('#save-and-close').click();
        this.returnToStep(valref, 'photoAndAttachments');
        counter++;
        cy.get('.selectedComps-count').should('contain', counter.toString());
    },
    // * QA and Review page getters

    get createReportButton() {
        return cy.get('.qaAndReview > :nth-child(2) > .QVHV-buttons-right > .primary');
    },

    // * QA and Review page functions

    // * MISC
    returnToStep(valref, stepperItem) {
        cy.visit(env.config.baseUrl);

        Home.quickSearchBar.clear().type(`${valref}{enter}`);
        cy.wait(5500);

        cy.wait(750);
        PropertyDetails.valuationJobsButton
            .should('exist')
            .and('be.visible')
            .click();
        cy.wait(150);
        cy.get('.valJobs-menu > :nth-child(2)').click();
        cy.wait(500);

        this.stepperItemClass(stepperItem).click();
    },

    save() {
        cy.get('.reportDetails > .QVHV-buttons > .QVHV-buttons-left > .primary').click();
    },
    /*
   _____ _               _      ______ _                           _         ______      _     _
  / ____| |             | |    |  ____| |                         | |       |  ____|    (_)   | |
 | |    | |__   ___  ___| | __ | |__  | | ___ _ __ ___   ___ _ __ | |_ ___  | |__  __  ___ ___| |_
 | |    | '_ \ / _ \/ __| |/ / |  __| | |/ _ \ '_ ` _ \ / _ \ '_ \| __/ __| |  __| \ \/ / / __| __|
 | |____| | | |  __/ (__|   <  | |____| |  __/ | | | | |  __/ | | | |_\__ \ | |____ >  <| \__ \ |_
  \_____|_| |_|\___|\___|_|\_\ |______|_|\___|_| |_| |_|\___|_| |_|\__|___/ |______/_/\_\_|___/\__|
    */

    //   TODO: break these up into seperate folders, this file is getting too long.

    /*
 __      __   _             _   _                    _       _
 \ \    / /  | |           | | (_)                  | |     | |
  \ \  / /_ _| |_   _  __ _| |_ _  ___  _ __        | | ___ | |__  ___
   \ \/ / _` | | | | |/ _` | __| |/ _ \| '_ \   _   | |/ _ \| '_ \/ __|
    \  / (_| | | |_| | (_| | |_| | (_) | | | | | |__| | (_) | |_) \__ \
     \/ \__,_|_|\__,_|\__,_|\__|_|\___/|_| |_|  \____/ \___/|_.__/|___/                                                                                                       |___/
    */

    checkColumns() {
        context('Check column names are correct', () => {
            it('Address column should exist and be visible', () => {
                cy.get('.sortRow > .address')
                    .should('exist')
                    .and('be.visible')
                    .and('contain.text', 'Address');
            });

            it('Val Ref column should exist and be visible', () => {
                cy.get('.sortRow > .valref')
                    .should('exist')
                    .and('be.visible')
                    .and('contain.text', 'Val Ref');
            });

            it('Inspection Time column should exist and be visible', () => {
                cy.get('.inspectiontime > a')
                    .should('exist')
                    .and('be.visible')
                    .and('contain.text', 'Inspection Date');
            });

            it('Job Due column should exist and be visible', () => {
                cy.get('.jobdue > a')
                    .should('exist')
                    .and('be.visible')
                    .and('contain.text', 'Job Due');
            });

            it('Report Type column should exist and be visible', () => {
                cy.get('.sortRow > .searchDetails-wrapper > .reporttype')
                    .should('exist')
                    .and('be.visible')
                    .and('contain.text', 'Report Type');
            });

            it('Assigned Valuer column should exist and be visible', () => {
                cy.get('.sortRow > .searchDetails-wrapper > .assignedvaluer')
                    .should('exist')
                    .and('be.visible')
                    .and('contain.text', 'Assigned To');
            });

            it('Countersigned Valuer column should exist and be visible', () => {
                cy.get('.sortRow > .searchDetails-wrapper > .countersignedvaluer')
                    .should('exist')
                    .and('be.visible')
                    .and('contain.text', 'Countersigned By');
            });

            it('Job Status column should exist and be visible', () => {
                cy.get('.sortRow > .searchDetails-wrapper > .jobstatus')
                    .should('exist')
                    .and('be.visible')
                    .and('contain.text', 'Job Status');
            });
        });
    },

    /*

       _       _        _____      _
      | |     | |      / ____|    | |
      | | ___ | |__   | (___   ___| |_ _   _ _ __
  _   | |/ _ \| '_ \   \___ \ / _ \ __| | | | '_ \
 | |__| | (_) | |_) |  ____) |  __/ |_| |_| | |_) |
  \____/ \___/|_.__/  |_____/ \___|\__|\__,_| .__/
                                            | |
                                            |_|
    */
    checkReportDetailsExists() {
        context('Checks every field exists for the report', () => {
            context('Report Details', () => {
                it('Stepper should exist and be visible', () => {
                    this.stepper.should('exist').and('be.visible');
                });

                it('Valuation Report Type should exist and be visible', () => {
                    this.valuationReportTypeMultiselect.should('exist').and('be.visible');
                });

                it('Inspection Type should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsInspectionType > .btn-group > .multiselect')

                        .should('exist')
                        .and('be.visible');
                });

                it('Type of Property should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsPropertyType > .btn-group > .multiselect')

                        .should('exist')
                        .and('be.visible');
                });

                it('Valuation Due Date and Time should exist and be visible', () => {
                    cy.get('.notRequired > .advSearch-text')

                        .should('exist')
                        .and('be.visible');
                });

                it('Inspection Date and Time should exist and be visible', () => {
                    this.inspectionDateAndTimeText.should('exist').and('be.visible');
                });

                it('Client Name should exist and be visible', () => {
                    this.clientNameTextField.should('exist').and('be.visible');
                });

                it('Client Reference should exist and be visible', () => {
                    cy.get('#jobsetupReportDetailsClientReference')

                        .should('exist')
                        .and('be.visible');
                });

                it('Website User ID should exist and be visible', () => {
                    cy.get('#jobsetupReportDetailsWebsiteUserId')

                        .should('exist')
                        .and('be.visible');
                });

                it('Postal Address should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsPostalAddress')

                        .should('exist')
                        .and('be.visible');
                });

                it('Email Address should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsEmailAddress')

                        .should('exist')
                        .and('be.visible');
                });

                it('Mobile Number should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsMobileNumber')

                        .should('exist')
                        .and('be.visible');
                });

                it('Daytime Phone should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsDaytimePhone')

                        .should('exist')
                        .and('be.visible');
                });

                it('Evening Phone should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsEveningPhone')

                        .should('exist')
                        .and('be.visible');
                });

                it('Lender Name should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsLenderName')

                        .should('exist')
                        .and('be.visible');
                });

                it('Lender Address should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsLenderAddress')

                        .should('exist')
                        .and('be.visible');
                });

                it('Instructed By should exist and be visible', () => {
                    this.instructedByTextField.should('exist').and('be.visible');
                });

                it('Borrower should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsBorrower')

                        .should('exist')
                        .and('be.visible');
                });

                it('Extended To should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsExtendedTo')

                        .should('exist')
                        .and('be.visible');
                });

                it('Other Instructions should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsOtherInstructions')

                        .should('exist')
                        .and('be.visible');
                });

                it('Site Inspection Notes should exist and be visible', () => {
                    cy.get('#jobSetupReportDetailsSiteInspectionNotes')

                        .should('exist')
                        .and('be.visible');
                });

                it('Valuer should exist and be visible', () => {
                    this.valuerMultiselect.should('exist').and('be.visible');
                });

                it('Countersigned By should exist and be visible', () => {
                    cy.get('.optional > .multiselect-native-select > .btn-group > .multiselect')

                        .should('exist')
                        .and('be.visible');
                });

                it('Purpose of Valuation should exist and be visible', () => {
                    this.purposeOfValuationMultiselect.should('exist').and('be.visible');
                });

                it('Purpose of Valuation should exist and be visible', () => {
                    cy.get(
                        ':nth-child(13) > .thirtythreePct > .multiselect-native-select > .btn-group > .multiselect'
                    )

                        .should('exist')
                        .and('be.visible');
                });

                it('Peer Review should exist and be visible', () => {
                    this.peerReviewMultiselect.should('exist').and('be.visible');
                });
            });
        });
    },

    checkExtraDetailsExists() {
        context('Checks every field exists for the report', () => {
            context('Switch tab to Extra Details', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementJobSetup > .QVHVTab-2 > span').click();
                    cy.get('li.QVHVTab-2 > .is-active').should('have.class', 'is-active');

                    cy.get('.extraPropertyDetails > .QVHV-formSection').should('be.visible');
                });
            });

            context('Change Subject Address', () => {
                it('Shows Change Property section', () => {
                    cy.get('label.changeAddress-trigger').click();
                    cy.get('.changeaddressdetails > .QVHV-formSection').should('be.visible');
                });

                it('Category should exist and be visible', () => {
                    cy.get('#jobSetupCagetoryOverride')
                        .should('exist')
                        .and('be.visible');
                });

                it('Street Address value matches address should exist and be visible', () => {
                    cy.get('#jobSetupStreetAddressOverride')
                        .should('exist')
                        .and('be.visible');
                    cy.get('#jobSetupStreetAddressOverride')
                        .invoke('val')
                        .should('have.length.above', 0)
                        // FIXME: compare this address to base address
                        .then($val => {
                            cy.get('.md-summaryAddress > h1')
                                .invoke('text')
                                .then($text => {
                                    const text = $text
                                        .split(/[ ,]+/)
                                        .slice(0, 3)
                                        .join(' ');
                                    const val = $val
                                        .split(/[ ,]+/)
                                        .slice(0, 3)
                                        .join(' ');
                                    expect(text).to.equal(val);
                                });
                        });
                });

                it('Legal Address should exist and be visible', () => {
                    cy.get('#jobSetupLegalDescriptionOverride')
                        .should('exist')
                        .and('be.visible');
                });

                it('Close Change Property section', () => {
                    cy.get('label.changeAddress-trigger')
                        .should('exist')
                        .click()
                        .then(() => {
                            cy.get('.changeaddressdetails > .QVHV-formSection').should(
                                'not.be.visible'
                            );
                        });
                });
            });

            // TODO: find smarter method of looping through every element on the page here
            // * similar to getting all property details from Property Details page on public website

            context('Check Extra Details exist and are visible', () => {
                it('Single Bedrooms should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsNumberOfSingleBedrooms')
                        .should('exist')
                        .and('be.visible');
                });

                it('Double Bedrooms should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsNumberOfDoubleBedrooms')
                        .should('exist')
                        .and('be.visible');
                });

                it('Double Bedrooms should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsNumberOfDoubleBedrooms')
                        .should('exist')
                        .and('be.visible');
                });

                it('Home Office or Studies should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsNumberOfHomeOfficesOrStudies')
                        .should('exist')
                        .and('be.visible');
                });

                it('Living Areas should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsNumberOfLivingAreass')
                        .should('exist')
                        .and('be.visible');
                });

                it('Garaging should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsGaraging')
                        .should('exist')
                        .and('be.visible');
                });

                it('Offstreet Parking should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsOffStreetParking')
                        .should('exist')
                        .and('be.visible');
                });

                it('Number of Bathrooms should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsNumberOfBathrooms')
                        .should('exist')
                        .and('be.visible');
                });

                it('Number of Toilets should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsNumberOfToilets')
                        .should('exist')
                        .and('be.visible');
                });

                it('Main Bathroom Age should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsMainBathroomAge > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Main Bathroom Quality should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsMainBathroomQuality > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Ensuite Age should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsEnsuiteAge > .btn-group > .multiselect').should(
                        'exist'
                    );
                });

                it('Ensuite Quality should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsEnsuiteQuality > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Kitchen Age should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsKitchenAge > .btn-group > .multiselect').should(
                        'exist'
                    );
                });

                it('Kitchen Quality should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsKitchenQuality > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Redecoration Age should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsRedecorationAge > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Internal Condition should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsInteriorPresentation > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Heating Type should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsHeatingType > .btn-group > .multiselect').should(
                        'exist'
                    );
                });

                it('Insulation should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsInsulation > .btn-group > .multiselect').should(
                        'exist'
                    );
                });

                it('Plumbing Age should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsPlumbingAge > .btn-group > .multiselect').should(
                        'exist'
                    );
                });

                it('Wiring Age should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsWiringAge > .btn-group > .multiselect').should(
                        'exist'
                    );
                });

                it('Double Glazing should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsDoubleGlazing > .btn-group > .multiselect').should(
                        'exist'
                    );
                });

                it('Alternative Energy should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsAlternativeEnergy > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Alternative Energy should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsVentilation > .btn-group > .multiselect').should(
                        'exist'
                    );
                });

                it('Rental Income ($ Per Week) should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsRentalIncomePerWeek')
                        .should('exist')
                        .and('be.visible');
                });

                it('Rental Income Known (MM/YYYY) should exist and be visible', () => {
                    cy.get('#jobSetupExtraDetailsRentalIncomeKnownDate')
                        .should('exist')
                        .and('be.visible');
                });

                it('Save Button should exist and be visible', () => {
                    cy.get('.jobSetup > .QVHV-buttons > .QVHV-buttons-left > .primary')
                        .should('exist')
                        .and('be.visible')
                        .click();
                });
            });

            context('Switch tab to Report Details', () => {
                it('Clicks back to Report tab', () => {
                    cy.get('#tabsElementJobSetup > .QVHVTab-1 > span').click();
                    cy.get('li.QVHVTab-1 > .is-active').should('have.class', 'is-active');
                });
            });
        });
    },
    /*
  _____                           _           _____       _        _ _
 |  __ \                         | |         |  __ \     | |      (_) |
 | |__) | __ ___  _ __   ___ _ __| |_ _   _  | |  | | ___| |_ __ _ _| |___
 |  ___/ '__/ _ \| '_ \ / _ \ '__| __| | | | | |  | |/ _ \ __/ _` | | / __|
 | |   | | | (_) | |_) |  __/ |  | |_| |_| | | |__| |  __/ || (_| | | \__ \
 |_|   |_|  \___/| .__/ \___|_|   \__|\__, | |_____/ \___|\__\__,_|_|_|___/
                 | |                   __/ |
                 |_|                  |___/
*/
    checkPropertyDetailsOverview() {
        context('Checks every field exists in Overview for the Property Details', () => {
            it('Bedrooms should exist and be visible', () => {
                cy.get('#overviewNumberOfBedrooms')
                    .should('exist')
                    .and('be.visible');
            });

            it('Bathrooms should exist and be visible', () => {
                cy.get('#overviewNumberOfBathrooms')
                    .should('exist')
                    .and('be.visible');
            });

            it('Toilets should exist and be visible', () => {
                cy.get('#overviewNumberOfToilets')
                    .should('exist')
                    .and('be.visible');
            });

            it('Living Areas should exist and be visible', () => {
                cy.get('#overviewNumberOfLivingAreas')
                    .should('exist')
                    .and('be.visible');
            });

            it('Garaging should exist and be visible', () => {
                cy.get('#overviewNumberOfGaraging')
                    .should('exist')
                    .and('be.visible');
            });

            it('Offstreet Parking should exist and be visible', () => {
                cy.get('#overviewNumberOfOffstreetParking')
                    .should('exist')
                    .and('be.visible');
            });

            it('OffstreetParking should exist and be visible', () => {
                cy.get('#overviewNumberOfOffstreetParking')
                    .should('exist')
                    .and('be.visible');
            });

            it('House Type should exist and be visible', () => {
                this.houseTypeMultiselect.should('exist').and('be.visible');
            });

            it('Estimated Year of Construction should exist and be visible', () => {
                cy.get('#overviewEstimatedYearOfConstruction')
                    .should('exist')
                    .and('be.visible');
            });

            it('Total Floor Area should exist and be visible', () => {
                cy.get('#overviewTotalFloorArea')
                    .should('exist')
                    .and('be.visible');
            });

            it('Total Floor Description should exist and be visible', () => {
                cy.get('#totalFloorDescription')
                    .should('exist')
                    .and('be.visible');
            });

            it('Exterior Cladding should exist and be visible', () => {
                cy.get('#overviewExteriorCladding > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('Foundation should exist and be visible', () => {
                cy.get('#overviewFoundation > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('Joinery should exist and be visible', () => {
                cy.get('#overviewJoinery > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('Roof Style should exist and be visible', () => {
                cy.get('#overviewRoofStyle > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('Roof Construction should exist and be visible', () => {
                cy.get('#overviewRoofConstruction > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('Spouting should exist and be visible', () => {
                cy.get('#overviewSpouting > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('External Condition should exist and be visible', () => {
                cy.get('#overviewExternalCondition > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('External Presentation should exist and be visible', () => {
                cy.get('#overviewQualityOfExternalPresentation > .btn-group > .multiselect').should(
                    'exist'
                );
            });

            it('Internal Condition should exist and be visible', () => {
                cy.get('#overviewInternalCondition > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('Internal Presentation should exist and be visible', () => {
                cy.get('#overviewQualityOfInternalPresentation > .btn-group > .multiselect').should(
                    'exist'
                );
            });

            it('Standard Of Accommodation should exist and be visible', () => {
                cy.get('#overviewStandardOfAccommodation > .btn-group > .multiselect').should(
                    'exist'
                );
            });

            it('Layout Description should exist and be visible', () => {
                cy.get('#overviewLayoutDesccription > .btn-group > .multiselect')
                    .should('exist')
                    .and('be.visible');
            });

            it('Maintenance Required should exist and be visible', () => {
                cy.get(
                    '.housedetails > .QVHV-formSection > :nth-child(8) > .advSearch-group > :nth-child(2) > .advSearch-text'
                )
                    .should('exist')
                    .and('be.visible');
            });

            it('Immediate Maintenance Required should exist and be visible', () => {
                cy.get('#overviewImmediateMaintenanceRequired')
                    .should('exist')
                    .and('be.visible');
            });

            it('Recent Alterations should exist and be visible', () => {
                cy.get('#overviewRecentAlterations')
                    .should('exist')
                    .and('be.visible');
            });

            it('Code Compliance should exist and be visible', () => {
                cy.get('#overviewCodeCompliance')
                    .should('exist')
                    .and('be.visible');

                cy.get('#overviewCodeCompliance')
                    .invoke('val')
                    .should(
                        'equal',
                        'We assume all buildings and subsequent alterations are compliant'
                    );
            });

            it('Notes should exist and be visible', () => {
                cy.get('#overviewNotes')
                    .should('exist')
                    .and('be.visible');
            });
        });
    },

    checkPropertyDetailsInterior() {
        context('Checks every field exists in Interior for the Property Details', () => {
            context('Switch tab to Interior', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElement > .QVHVTab-2 > span').click();
                    cy.get('#tabsElement > li.QVHVTab-2 > .is-active').should(
                        'have.class',
                        'is-active'
                    );

                    cy.get('.mainbuilding > .QVHV-formSection').should('be.visible');
                });
            });

            context('Check Interior elements should exist and be visible', () => {
                it('Living Area should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(1) > .twentyfivePct > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Living Area Description should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(1) > .seventyfivePct-tenRem > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it("Living Area delete row doesn't exist for first row", () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(1) > .sa-addRemove > .saRow-remove'
                    ).should('not.exist');
                });

                it('Laundry should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(2) > .advSearch-group > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Internal Linings should exist and be visible', () => {
                    cy.get(
                        '.QVHV-formSection > :nth-child(3) > .icons8-brick-wall-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Floors should exist and be visible', () => {
                    cy.get('.icons8-carpet-filled > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Chattels should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(4) > .advSearch-group > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Heating Type should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(5) > .icons8-fire-station-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Insulation should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(5) > .icons8-heating-room-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Plumbing Age should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(5) > .icons8-piping-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Wiring Age should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(5) > .icons8-plug-4-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Double Glazing should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(6) > .icons8-closed-window-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Alternative Energy should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(6) > .icons8-solar-panel-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Ventilation should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(6) > .icons8-air-conditioner-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Redecoration Age should exist and be visible', () => {
                    cy.get(
                        ':nth-child(6) > .icons8-roller-brush-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Other Features should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(7) > .advSearch-group > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Other Features should exist and be visible', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(8) > .advSearch-group > span > .advSearch-text'
                    )
                        .should('exist')
                        .and('be.visible');
                });
            });

            context('Add and remove Living Area rows', () => {
                it('Living Area add row', () => {
                    cy.wait(1500);
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(1) > .sa-addRemove > .saRow-add'
                    )
                        .should('exist')
                        .and('be.visible')
                        .click()
                        .then(() => {
                            cy.get(
                                '.mainbuilding > .QVHV-formSection > :nth-child(2) > .twentyfivePct > :nth-child(1)'
                            )
                                .should('exist')
                                .and('be.visible');
                        });
                });

                it('Living Area delete row', () => {
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(2) > .sa-addRemove > .saRow-add'
                    )
                        .should('exist')
                        .and('be.visible');
                    cy.get(
                        '.mainbuilding > .QVHV-formSection > :nth-child(2) > .sa-addRemove > .saRow-remove'
                    )
                        .should('exist')
                        .and('be.visible')
                        .click()
                        .then(() => {
                            cy.wait(500);
                            cy.get(
                                '.mainbuilding > .QVHV-formSection > :nth-child(2) > .advSearch-group > :nth-child(1)'
                            ).should('have.text', 'Laundry');
                        });
                });
            });
        });
    },

    checkPropertyDetailsBedrooms() {
        context('Checks every field exists in Bedroom for the Property Details', () => {
            context('Switch tab to Bedroom', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElement > .QVHVTab-3 > span').click();
                    cy.get('#tabsElement > li.QVHVTab-3 > .is-active').should(
                        'have.class',
                        'is-active'
                    );

                    cy.get('.bedrooms > .QVHV-formSection').should('be.visible');
                });
            });

            context('Check Bedroom elements should exist and be visible', () => {
                it('Bedroom List item should exist and be visible', () => {
                    cy.get('.bedrooms > .QVHV-formSection > :nth-child(1)')
                        .should('exist')
                        .and('be.visible');
                });

                it('Bedroom List remove does not exist for first row, but will for any subsequent rows', () => {
                    cy.get(':nth-child(1) > .sa-addRemove > .saRow-remove').should('not.exist');
                    cy.get(':nth-child(2) > .sa-addRemove > .saRow-remove')
                        .should('exist')
                        .and('be.visible');
                });

                it('HomeOffice row should exist and be visible', () => {
                    cy.get('#pd_homeoffice0')
                        .should('exist')
                        .and('be.visible');
                });

                it('HomeOffice adds new row', () => {
                    cy.get('#pd_homeoffice0 > .sa-addRemove > .saRow-add')
                        .click()
                        .then(() => {
                            cy.wait(250);
                            cy.get('#pd_homeoffice1')
                                .should('exist')
                                .and('be.visible');
                        });
                });

                it('HomeOffice remove does not exist for first row, but will for any subsequent rows', () => {
                    cy.get('#pd_homeoffice0 > .sa-addRemove > .saRow-remove').should('not.exist');
                    cy.get('#pd_homeoffice1 > .sa-addRemove > .saRow-remove')
                        .should('exist')
                        .and('be.visible');
                });

                it('HomeOffice remove row button removes row', () => {
                    cy.get('#pd_homeoffice1 > .sa-addRemove > .saRow-remove')
                        .click()
                        .then(() => {
                            cy.wait(250);
                            cy.get('#pd_homeoffice1').should('not.exist');
                        });
                });

                it('Notes should exist and be visible', () => {
                    cy.get('#pd_bedrooms_notes')
                        .should('exist')
                        .and('be.visible');
                });
            });
        });
    },

    checkPropertyDetailsKitchen() {
        context('Checks every field exists in Kitchen for the Property Details', () => {
            context('Switch tab to Kitchen', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElement > .QVHVTab-4 > span').click();
                    cy.get('#tabsElement > li.QVHVTab-4 > .is-active').should(
                        'have.class',
                        'is-active'
                    );

                    cy.get('.kitchen > .QVHV-formSection').should('be.visible');
                });
            });

            context('Check Kitchen elements should exist and be visible', () => {
                it('Kitchen Layout should exist and be visible', () => {
                    cy.get('.kitchen > .QVHV-formSection')
                        .should('exist')
                        .and('be.visible');
                });

                it('Kitchen Age should exist and be visible', () => {
                    cy.get(':nth-child(1) > .icons8-calendar-filled > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Kitchen Quality should exist and be visible', () => {
                    cy.get(':nth-child(1) > .icons8-rating-filled > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Appliances should exist and be visible', () => {
                    cy.get('.icons8-dishwasher-filled > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Bench and Sink should exist and be visible', () => {
                    cy.get('.icons8-bench-sink-filled > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Kitchen Floor should exist and be visible', () => {
                    cy.get('.icons8-kitchen-floor-new > .btn-group > .multiselect')
                        .should('exist')
                        .and('be.visible');
                });

                it('Notes should exist and be visible', () => {
                    cy.get(
                        '.kitchen > .QVHV-formSection > :nth-child(3) > .advSearch-group > span > .advSearch-text'
                    )
                        .should('exist')
                        .and('be.visible');
                });
            });
        });
    },

    checkPropertyDetailsBathrooms() {
        context('Checks every field exists in Bathrooms for the Property Details', () => {
            context('Switch tab to Bathrooms', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElement > .QVHVTab-5 > span').click();
                    cy.get('#tabsElement > li.QVHVTab-5 > .is-active').should(
                        'have.class',
                        'is-active'
                    );

                    cy.get('.bathrooms > .QVHV-formSection').should('be.visible');
                });
            });

            context('Check Bathrooms elements should exist and be visible', () => {
                it('Main Bathroom should exist and be visible', () => {
                    cy.get(
                        '.bathrooms > .QVHV-formSection > :nth-child(1) > :nth-child(1) > .twentyfivePct > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Main Bathroom Description should exist and be visible', () => {
                    cy.get(
                        '.bathrooms > .QVHV-formSection > :nth-child(1) > :nth-child(1) > .seventyfivePct-tenRem > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Main Bathroom Age should exist and be visible', () => {
                    cy.get(
                        '.bathrooms > .QVHV-formSection > :nth-child(1) > :nth-child(2) > .icons8-calendar-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Main Bathroom Quality should exist and be visible', () => {
                    cy.get(
                        ':nth-child(1) > :nth-child(2) > .icons8-rating-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Ensuite should exist and be visible', () => {
                    cy.get(
                        '.bathrooms > .QVHV-formSection > :nth-child(2) > :nth-child(1) > .twentyfivePct > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Ensuite Description should exist and be visible', () => {
                    cy.get(
                        '.bathrooms > .QVHV-formSection > :nth-child(2) > :nth-child(1) > .seventyfivePct-tenRem > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Ensuite Age should exist and be visible', () => {
                    cy.get(
                        '.bathrooms > .QVHV-formSection > :nth-child(2) > :nth-child(2) > .icons8-calendar-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Ensuite Quality should exist and be visible', () => {
                    cy.get(
                        ':nth-child(2) > :nth-child(2) > .icons8-rating-filled > .btn-group > .multiselect'
                    )
                        .should('exist')
                        .and('be.visible');
                });

                it('Bathroom or Toilet should exist and be visible', () => {
                    cy.get('.bathrooms > .QVHV-formSection > :nth-child(3)')
                        .should('exist')
                        .and('be.visible');
                });

                it('Notes should exist and be visible', () => {
                    cy.get(
                        '.bathrooms > .QVHV-formSection > :nth-child(4) > .advSearch-group > span > .advSearch-text'
                    )
                        .should('exist')
                        .and('be.visible');
                });
            });
            context('Add and remove Bathroom or Toilet', () => {
                it('Bathroom or Toilet adds row', () => {
                    cy.get(
                        '.bathrooms > .QVHV-formSection > :nth-child(3) > .sa-addRemove > .saRow-add'
                    )
                        .should('exist')
                        .and('be.visible')
                        .click()
                        .then(() => {
                            cy.wait(250);
                            cy.get('.bathrooms > .QVHV-formSection > :nth-child(4)').should(
                                'exist'
                            );
                        });
                });

                it('Bathroom or Toilet removes row', () => {
                    cy.get(
                        '.QVHV-formSection > :nth-child(3) > .sa-addRemove > .saRow-remove'
                    ).should('not.exist');

                    cy.wait(250);
                    cy.get('.QVHV-formSection > :nth-child(4) > .sa-addRemove > .saRow-remove')
                        .should('exist')
                        .and('be.visible')
                        .click()
                        .then(() => {
                            cy.wait(250);
                            cy.get('.bathrooms > .QVHV-formSection > :nth-child(4)').should(
                                'exist'
                            );
                        });
                });
            });
        });
    },

    checkPropertyDetailsGarages() {
        context('Checks every field exists in Garages for the Property Details', () => {
            context('Switch tab to Garages', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElement > .QVHVTab-6 > span').click();
                    cy.get('#tabsElement > li.QVHVTab-6 > .is-active').should(
                        'have.class',
                        'is-active'
                    );

                    cy.get('.QVHV-Container.garaging > .QVHV-formSection').should('be.visible');
                });
            });

            context('Analyse page', () => {
                context('Check Garages elements exist', () => {
                    context('Garage', () => {
                        it('Garaging item should exist and be visible', () => {
                            cy.get('.QVHV-Container.garaging > .QVHV-formSection > :nth-child(1)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Type should exist and be visible', () => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(1) > :nth-child(1) > .twentyfivePct > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Description should exist and be visible', () => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(1) > :nth-child(1) > .seventyfivePct-tenRem > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Age should exist and be visible', () => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(1) > :nth-child(2) > .icons8-calendar-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Floor Area should exist and be visible', () => {
                            cy.get(
                                ':nth-child(1) > :nth-child(2) > .icons8-floor-plan-filled > span > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Modernisation should exist and be visible', () => {
                            cy.get(
                                ':nth-child(1) > :nth-child(2) > .icons8-maintenance-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Modernisation should exist and be visible', () => {
                            cy.get(
                                ':nth-child(1) > :nth-child(2) > .icons8-maintenance-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Exterior Cladding should exist and be visible', () => {
                            cy.get(
                                ':nth-child(1) > :nth-child(3) > .icons8-brick-wall-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Roof Construction should exist and be visible', () => {
                            cy.get(
                                ':nth-child(1) > :nth-child(3) > .icons8-structural-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Foundation should exist and be visible', () => {
                            cy.get(
                                ':nth-child(1) > :nth-child(3) > .icons8-structural-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Roof Construction should exist and be visible', () => {
                            cy.get(
                                ':nth-child(1) > :nth-child(3) > .icons8-structural-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Garage Notes should exist and be visible', () => {
                            cy.get(
                                ':nth-child(1) > :nth-child(4) > .seventyfivePct-tenRem > span > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });
                    });

                    context('Other Buildings', () => {
                        it('Other Buildings item should exist and be visible', () => {
                            cy.get('.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Type should exist and be visible', () => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2) > :nth-child(1) > .twentyfivePct > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Description should exist and be visible', () => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2) > :nth-child(1) > .seventyfivePct-tenRem > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Age should exist and be visible', () => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2) > :nth-child(2) > .icons8-calendar-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Floor Area should exist and be visible', () => {
                            cy.get(
                                ':nth-child(2) > :nth-child(2) > .icons8-floor-plan-filled > span > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Modernisation should exist and be visible', () => {
                            cy.get(
                                ':nth-child(2) > :nth-child(2) > .icons8-maintenance-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Modernisation should exist and be visible', () => {
                            cy.get(
                                ':nth-child(2) > :nth-child(2) > .icons8-maintenance-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Exterior Cladding should exist and be visible', () => {
                            cy.get(
                                ':nth-child(2) > :nth-child(3) > .icons8-brick-wall-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Roof Construction should exist and be visible', () => {
                            cy.get(
                                ':nth-child(2) > :nth-child(3) > .icons8-structural-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Foundation should exist and be visible', () => {
                            cy.get(
                                ':nth-child(2) > :nth-child(3) > .icons8-structural-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Roof Construction should exist and be visible', () => {
                            cy.get(
                                ':nth-child(2) > :nth-child(3) > .icons8-structural-filled > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Other Buildings Notes should exist and be visible', () => {
                            cy.get(
                                ':nth-child(2) > :nth-child(4) > .seventyfivePct-tenRem > span > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });
                    });
                });
            });

            context('Add and remove items in Garages', () => {
                it('Add new Garaging Item', () => {
                    cy.get(':nth-child(1) > :nth-child(4) > .sa-addRemove > .saRow-add')
                        .click()
                        .then(() => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2) > :nth-child(1) > .twentyfivePct > :nth-child(1)'
                            ).should('have.text', 'Garage Type');
                        });
                });

                it('Remove Garaging Item', () => {
                    cy.get(':nth-child(1) > :nth-child(4) > .sa-addRemove > .saRow-remove').should(
                        'not.exist'
                    );
                    cy.get(
                        '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2) > :nth-child(4) > .sa-addRemove > .saRow-remove'
                    )
                        .should('exist')
                        .and('be.visible')
                        .click()
                        .then(() => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2) > :nth-child(1) > .twentyfivePct > :nth-child(1)'
                            ).should('have.text', 'Other Buildings');
                        });
                });

                it('Add new Other Building item', () => {
                    cy.get(
                        '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2) > :nth-child(4) > .sa-addRemove > .saRow-add'
                    )
                        .should('exist')
                        .and('be.visible')
                        .click()
                        .then(() => {
                            cy.get('.QVHV-Container.garaging > .QVHV-formSection > :nth-child(3)')
                                .should('exist')
                                .and('be.visible');
                        });
                });

                it('Remove Other Building item', () => {
                    cy.get(
                        '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(2) > :nth-child(4) > .sa-addRemove > .saRow-remove'
                    ).should('not.exist');
                    cy.get(':nth-child(3) > :nth-child(4) > .sa-addRemove > .saRow-remove')
                        .should('exist')
                        .and('be.visible')
                        .click()
                        .then(() => {
                            cy.get(
                                '.QVHV-Container.garaging > .QVHV-formSection > :nth-child(3)'
                            ).should('not.exist');
                        });
                });
            });
        });
    },

    checkPropertyDetailsImprovements() {
        context('Checks every field exists in Improvements for the Property Details', () => {
            context('Switch tab to Improvements', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElement > .QVHVTab-7 > span').click();
                    cy.get('#tabsElement > li.QVHVTab-7 > .is-active').should(
                        'have.class',
                        'is-active'
                    );

                    cy.get('.siteimprovements > .QVHV-formSection').should('be.visible');
                });
            });

            context('Check Improvements elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Major Site Improvements should exist and be visible', () => {
                        cy.get(
                            '.siteimprovements > .QVHV-formSection > :nth-child(1) > .advSearch-Subrow > .twentyfivePct > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get(
                            '.siteimprovements > .QVHV-formSection > :nth-child(1) > .advSearch-Subrow > .seventyfivePct-tenRem > span > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Minor Site Improvements should exist and be visible', () => {
                        cy.get(
                            '.siteimprovements > .QVHV-formSection > :nth-child(2) > .advSearch-group > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Driveway should exist and be visible', () => {
                        cy.get('#improvementsDriveWay > .btn-group > .multiselect')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Landscaping should exist and be visible', () => {
                        cy.get('#improvementsLandscaping > .btn-group > .multiselect')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Fencing should exist and be visible', () => {
                        cy.get('#improvementsFencing > .btn-group > .multiselect')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Notes should exist and be visible', () => {
                        cy.get('#improvementsFencing > .btn-group > .multiselect')
                            .should('exist')
                            .and('be.visible');
                    });
                });

                context('Add and Remove rows', () => {
                    it('Adds improvement', () => {
                        cy.get(
                            '.siteimprovements > .QVHV-formSection > :nth-child(1) > .advSearch-Subrow > .sa-addRemove > .saRow-add'
                        )
                            .click()
                            .then(() => {
                                cy.get(
                                    '.siteimprovements > .QVHV-formSection > :nth-child(2) > .advSearch-Subrow > .twentyfivePct > :nth-child(1)'
                                )
                                    .should('exist')
                                    .and('be.visible');
                            });
                    });

                    it('Removes improvement', () => {
                        cy.get(
                            '.siteimprovements > .QVHV-formSection > :nth-child(1) > .advSearch-Subrow > .sa-addRemove > .saRow-remove'
                        ).should('not.exist');
                        cy.get('.advSearch-Subrow > .sa-addRemove > .saRow-remove')
                            .click()
                            .then(() => {
                                cy.get(
                                    '.siteimprovements > .QVHV-formSection > :nth-child(2) > .advSearch-Subrow > .twentyfivePct > :nth-child(1)'
                                ).should('not.exist');
                            });
                    });
                });
            });
        });
    },
    /*
 __      __   _             _   _              __          __        _        _               _
 \ \    / /  | |           | | (_)             \ \        / /       | |      | |             | |
  \ \  / /_ _| |_   _  __ _| |_ _  ___  _ __    \ \  /\  / /__  _ __| | _____| |__   ___  ___| |_ ___
   \ \/ / _` | | | | |/ _` | __| |/ _ \| '_ \    \ \/  \/ / _ \| '__| |/ / __| '_ \ / _ \/ _ \ __/ __|
    \  / (_| | | |_| | (_| | |_| | (_) | | | |    \  /\  / (_) | |  |   <\__ \ | | |  __/  __/ |_\__ \
     \/ \__,_|_|\__,_|\__,_|\__|_|\___/|_| |_|     \/  \/ \___/|_|  |_|\_\___/_| |_|\___|\___|\__|___/

    */
    checkCurrentValuation() {
        context(
            'Checks every field exists in Current Valuation for the Valuation Worksheets',
            () => {
                context('Switch tab to Current Valuation', () => {
                    it('Clicks to new tab', () => {
                        cy.get('#tabsElementValuationWorksheet > .QVHVTab-1 > span').click();

                        cy.get('#tabsElementValuationWorksheet > li.QVHVTab-1 > .is-active')
                            .should('be.visible')
                            .and('have.class', 'is-active');
                    });
                });

                context('Check Current Valuation elements should exist and be visible', () => {
                    context('Check elements exist', () => {
                        it('Improvements should exist and be visible', () => {
                            cy.get(
                                '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(2) > .sa-description > span > input'
                            )
                                .should('exist')
                                .and('be.visible');
                        });
                        context('Improvement values', () => {
                            it('Dwelling value', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(2) > .sa-description > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', 'Dwelling');
                            });

                            it('Garage value', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-description > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', 'Garage');
                            });

                            it('Other Improvement value', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-description > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', 'Other Improvements');
                            });
                        });

                        it('Land should exist and be visible', () => {
                            cy.get('.currentValuation > .QVHV-formSection > :nth-child(4)')
                                .should('exist')
                                .and('be.visible');
                        });

                        context('Land values', () => {
                            it('Description equals "Land"', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-description > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', 'Land');
                            });
                        });

                        it('Chattels should exist and be visible', () => {
                            cy.get('.currentValuation > .QVHV-formSection > :nth-child(6)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Worksheet Values should exist and be visible', () => {
                            cy.get('.currentValuation > .QVHV-formSection > :nth-child(8)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Adopted Values for Report should exist and be visible', () => {
                            cy.get('.currentValuation > .QVHV-formSection > :nth-child(12)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Mortgagee Sales Values should exist and be visible after check click', () => {
                            cy.get('#Mortgagee\\ Value')
                                .should('exist')
                                .and('be.visible')
                                .click()
                                .then(() => {
                                    cy.get('.mortgageeValues')
                                        .should('exist')
                                        .and('be.visible');
                                });
                        });

                        it('Housing New Zealand Marketing Sales Periods Values should exist and be visible after check click', () => {
                            cy.get('#HNZPeriods')
                                .should('exist')
                                .and('be.visible')
                                .click()
                                .then(() => {
                                    cy.get('.hnzPeriods')
                                        .should('exist')
                                        .and('be.visible');
                                });
                        });

                        it('Likely Realisable Price Assuming Constrained Circumstances Values should exist and be visible after check click', () => {
                            cy.get('label.hideshowLikelyRealisablePrice-trigger > h3')
                                .should('exist')
                                .and('be.visible');

                            cy.get('#likelyRealisablePrice')
                                .should('exist')
                                .and('be.visible');

                            cy.get('#likelyRealisablePrice')
                                .click()
                                .then(() => {
                                    cy.get('.QVHV-formSection > :nth-child(21)')
                                        .should('exist')
                                        .and('be.visible');
                                    cy.get('.QVHV-formSection > :nth-child(22)')
                                        .should('exist')
                                        .and('be.visible');
                                });
                        });
                    });

                    context('Add and remove rows', () => {
                        context('Improvements', () => {
                            it('Adds Dwelling', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(2) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-description > span > input'
                                        )
                                            .invoke('val')
                                            .should('equal', '');
                                    });
                            });

                            it('Removes Dwelling', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(2) > .sa-addRemove > .saRow-remove'
                                ).should('not.exist');
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-description > span > input'
                                        )
                                            .invoke('val')
                                            .should('equal', 'Garage');
                                    });
                            });

                            it('Adds Garage', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-description > span > input'
                                        )
                                            .invoke('val')
                                            .should('equal', '');
                                    });
                            });

                            it('Removes Garage', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-addRemove > .saRow-remove'
                                ).should('exist');
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.wait(250);
                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-description > span > input'
                                        )
                                            .invoke('val')
                                            .should('equal', 'Other Improvements');
                                    });
                            });

                            it('Adds Other Improvements', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.wait(250);
                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(5) > .sa-description > span > input'
                                        )
                                            .should('exist')
                                            .and('be.visible')
                                            .invoke('val')
                                            .should('equal', '');
                                    });
                            });

                            it('Removes Other Improvements', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-addRemove > .saRow-remove'
                                ).should('exist');
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(5) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > .sa-oli > :nth-child(5) > .sa-description > span > input'
                                        ).should('not.exist');
                                    });
                            });
                        });

                        context('Land', () => {
                            it('Adds Land row', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(':nth-child(4) > :nth-child(3)')
                                            .should('exist')
                                            .and('be.visible')
                                            .invoke('val')
                                            .should('equal', '');

                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > :nth-child(4) > :nth-child(3) > .sa-runnintgTotal > span'
                                        )
                                            .should('exist')
                                            .and('be.visible')
                                            .invoke('val')
                                            .should('not.equal', '0');
                                    });
                            });

                            it('Removes Land row', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > :nth-child(4) > :nth-child(3) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            ':nth-child(4) > :nth-child(3) > .sa-description > span > input'
                                        ).should('not.exist');

                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-runnintgTotal > span'
                                        )
                                            .should('exist')
                                            .and('be.visible')
                                            .invoke('val')
                                            .should('not.equal', '0');
                                    });
                            });
                        });

                        // DEBUG:
                        context('Chattels', () => {
                            it('Adds Chattels row', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > :nth-child(6) > :nth-child(2) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(':nth-child(6) > :nth-child(3)')
                                            .should('exist')
                                            .and('be.visible');

                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > :nth-child(6) > :nth-child(3) > .sa-runnintgTotal > span'
                                        )
                                            .should('exist')
                                            .and('be.visible');
                                    });
                            });

                            it('Removes Chattels row', () => {
                                cy.get(
                                    '.currentValuation > .QVHV-formSection > :nth-child(6) > :nth-child(3) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.currentValuation > .QVHV-formSection > :nth-child(6) > :nth-child(2) > .sa-runnintgTotal > span'
                                        )
                                            .should('exist')
                                            .and('be.visible');
                                    });
                            });
                        });
                    });
                });
            }
        );
    },

    checkProposedValuation() {
        context(
            'Checks every field exists in Proposed Valuation for the Valuation Worksheets',
            () => {
                context('Switch tab to Proposed Valuation', () => {
                    it('Clicks to new tab', () => {
                        cy.get('#tabsElementValuationWorksheet > .QVHVTab-2 > span').click();

                        cy.get('#tabsElementValuationWorksheet > li.QVHVTab-2 > .is-active')
                            .should('be.visible')
                            .and('have.class', 'is-active');
                    });
                });

                context('Check Proposed Valuation elements should exist and be visible', () => {
                    context('Check elements exist', () => {
                        it('Improvements should exist and be visible', () => {
                            cy.get(
                                '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(2) > .sa-description > span > input'
                            )
                                .should('exist')
                                .and('be.visible');
                        });
                        context('Improvement values', () => {
                            it('Dwelling value', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(2) > .sa-description > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', 'Dwelling');
                            });

                            it('Garage value', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-description > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', 'Garage');
                            });

                            it('Other Improvement value', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-description > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', 'Other Improvements');
                            });
                        });

                        it('Land should exist and be visible', () => {
                            cy.get('.proposedValuation > .QVHV-formSection > :nth-child(4)')
                                .should('exist')
                                .and('be.visible');
                        });

                        context('Land values', () => {
                            it('Description equals "Land"', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-description > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', 'Land');
                            });
                        });

                        it('Chattels should exist and be visible', () => {
                            cy.get('.proposedValuation > .QVHV-formSection > :nth-child(6)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Worksheet Values should exist and be visible', () => {
                            cy.get('.proposedValuation > .QVHV-formSection > :nth-child(8)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Adopted Values for Report should exist and be visible', () => {
                            cy.get('.proposedValuation > .QVHV-formSection > :nth-child(10)')
                                .should('exist')
                                .and('be.visible');
                        });
                    });

                    context('Add and remove rows', () => {
                        context('Improvements', () => {
                            it('Adds Dwelling', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(2) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-description > span > input'
                                        )
                                            .invoke('val')
                                            .should('equal', '');
                                    });
                            });

                            it('Removes Dwelling', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(2) > .sa-addRemove > .saRow-remove'
                                ).should('not.exist');
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-description > span > input'
                                        )
                                            .invoke('val')
                                            .should('equal', 'Garage');
                                    });
                            });

                            it('Adds Garage', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-description > span > input'
                                        )
                                            .invoke('val')
                                            .should('equal', '');
                                    });
                            });

                            it('Removes Garage', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(3) > .sa-addRemove > .saRow-remove'
                                ).should('exist');
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.wait(250);
                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-description > span > input'
                                        )
                                            .invoke('val')
                                            .should('equal', 'Other Improvements');
                                    });
                            });

                            it('Adds Other Improvements', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.wait(250);
                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(5) > .sa-description > span > input'
                                        )
                                            .should('exist')
                                            .and('be.visible')
                                            .invoke('val')
                                            .should('equal', '');
                                    });
                            });

                            it('Removes Other Improvements', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(4) > .sa-addRemove > .saRow-remove'
                                ).should('exist');
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(5) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > .sa-oli > :nth-child(5) > .sa-description > span > input'
                                        ).should('not.exist');
                                    });
                            });
                        });

                        context('Land', () => {
                            it('Adds Land row', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(':nth-child(4) > :nth-child(3)')
                                            .should('exist')
                                            .and('be.visible')
                                            .invoke('val')
                                            .should('equal', '');

                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > :nth-child(4) > :nth-child(3) > .sa-runnintgTotal > span'
                                        )
                                            .should('exist')
                                            .and('be.visible')
                                            .invoke('val')
                                            .should('not.equal', '0');
                                    });
                            });

                            it('Removes Land row', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > :nth-child(4) > :nth-child(3) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            ':nth-child(4) > :nth-child(3) > .sa-description > span > input'
                                        ).should('not.exist');
                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-runnintgTotal > span'
                                        )
                                            .should('exist')
                                            .and('be.visible')
                                            .invoke('val')
                                            .should('not.equal', '0');
                                    });
                            });
                        });

                        // DEBUG:
                        context('Chattels', () => {
                            it('Adds Chattels row', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > :nth-child(6) > :nth-child(2) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(':nth-child(6) > :nth-child(3)')
                                            .should('exist')
                                            .and('be.visible');

                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > :nth-child(6) > :nth-child(3) > .sa-runnintgTotal > span'
                                        )
                                            .should('exist')
                                            .and('be.visible');
                                    });
                            });

                            it('Removes Chattels row', () => {
                                cy.get(
                                    '.proposedValuation > .QVHV-formSection > :nth-child(6) > :nth-child(3) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.proposedValuation > .QVHV-formSection > :nth-child(6) > :nth-child(2) > .sa-runnintgTotal > span'
                                        )
                                            .should('exist')
                                            .and('be.visible');
                                    });
                            });
                        });
                    });
                });
            }
        );
    },

    checkIncomeMethod() {
        context(
            'Checks every field exists in Current Valuation for the Valuation Worksheets',
            () => {
                context('Switch tab to Current Valuation', () => {
                    it('Clicks to new tab', () => {
                        cy.get('#tabsElementValuationWorksheet > .QVHVTab-3 > span').click();

                        cy.get('#tabsElementValuationWorksheet > li.QVHVTab-3 > .is-active')
                            .should('be.visible')
                            .and('have.class', 'is-active');
                    });
                });

                context('Check Current Valuation elements should exist and be visible', () => {
                    context('Check elements exist', () => {
                        it('Gross Rate Calculation should exist and be visible', () => {
                            cy.get('.incomeMethod > .QVHV-formSection > :nth-child(2)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Cap Rates should exist and be visible', () => {
                            cy.get('.incomeMethod > .QVHV-formSection > :nth-child(4)')
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Market Values - Income Method Approach should exist and be visible', () => {
                            cy.get('.incomeMethod > .QVHV-formSection > .sa-details')
                                .should('exist')
                                .and('be.visible');
                        });
                    });

                    context('Add and remove rows', () => {
                        context('Gross Rate Calculation', () => {
                            it('Adds Flat', () => {
                                cy.get(
                                    '.incomeMethod > .QVHV-formSection > :nth-child(2) > :nth-child(3) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            '.incomeMethod > .QVHV-formSection > :nth-child(2) > :nth-child(4) > .sa-runnintgTotal > span'
                                        )
                                            .invoke('val')
                                            .should('equal', '');
                                        cy.get(
                                            ':nth-child(2) > :nth-child(4) > .sa-description-medium'
                                        ).should('exist');
                                    });
                            });

                            it('Removes Flat', () => {
                                cy.get(
                                    '.incomeMethod > .QVHV-formSection > :nth-child(2) > :nth-child(2) > .sa-addRemove > .saRow-remove'
                                ).should('not.exist');
                                cy.get(
                                    '.incomeMethod > .QVHV-formSection > :nth-child(2) > :nth-child(4) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            ':nth-child(2) > :nth-child(4) > .sa-description-medium'
                                        ).should('not.exist');
                                    });
                            });
                        });

                        context('Cap Rates', () => {
                            it('Adds Cap Rates row', () => {
                                cy.get(
                                    '.incomeMethod > .QVHV-formSection > :nth-child(4) > :nth-child(2) > .sa-addRemove > .saRow-add'
                                )
                                    .click()
                                    .then(() => {
                                        cy.get(':nth-child(4) > :nth-child(5)')
                                            .should('exist')
                                            .and('be.visible');
                                    });
                            });

                            it('Removes Cap Rates row', () => {
                                cy.get(
                                    ':nth-child(4) > :nth-child(4) > .sa-addRemove > .saRow-remove'
                                )
                                    .should('exist')
                                    .and('be.visible')
                                    .click()
                                    .then(() => {
                                        cy.get(
                                            ':nth-child(4) > :nth-child(5) > .sa-description > span > input'
                                        ).should('not.exist');
                                    });
                            });
                        });
                    });
                });
            }
        );
    },
    /*
  _____                       _     _____       _        _ _
 |  __ \                     | |   |  __ \     | |      (_) |
 | |__) |___ _ __   ___  _ __| |_  | |  | | ___| |_ __ _ _| |___
 |  _  // _ \ '_ \ / _ \| '__| __| | |  | |/ _ \ __/ _` | | / __|
 | | \ \  __/ |_) | (_) | |  | |_  | |__| |  __/ || (_| | | \__ \
 |_|  \_\___| .__/ \___/|_|   \__| |_____/ \___|\__\__,_|_|_|___/
            | |
            |_|

    */
    checkValuationConclusion() {
        context('Checks every field exists in Valuation Conclusion for the Report Details', () => {
            context('Switch tab to Valuation Conclusion', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementReportDetails > .QVHVTab-1 > span').click();

                    cy.get('#tabsElementReportDetails > li.QVHVTab-1 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Check Valuation Conclusion elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Market Comments should exist and be visible', () => {
                        cy.get(
                            '.valuationconclusion > .QVHV-formSection > :nth-child(1) > .fiftyPct > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Market Comments Description should exist and be visible', () => {
                        cy.get(
                            '.QVHV-formSection > :nth-child(1) > .hundyPct > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Property Description should exist and be visible', () => {
                        cy.get(
                            '.QVHV-formSection > :nth-child(1) > .hundyPct > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Valuation Approach should exist and be visible', () => {
                        cy.get('#valuationApproaches_1 > .btn-group > .multiselect')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Valuation Approach Description should exist and be visible', () => {
                        cy.get(
                            '.valuationconclusion > .QVHV-formSection > :nth-child(3) > :nth-child(3) > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Valuation Conclusion should exist and be visible', () => {
                        cy.get(':nth-child(3) > :nth-child(4) > :nth-child(2) > .advSearch-text')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Demand for Subject Property should exist and be visible', () => {
                        cy.get(':nth-child(4) > .fiftyPct > .btn-group > .multiselect')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Demand for Subject Property Description should exist and be visible', () => {
                        cy.get('#demandForSubjectPropertyDesccription')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Basis of Value should exist and be visible', () => {
                        cy.get(
                            '.valuationconclusion > .QVHV-formSection > :nth-child(5) > .fiftyPct > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Basis of Value Description should exist and be visible', () => {
                        cy.get('#basisOfValueDescription')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Special Conditions should exist and be visible', () => {
                        cy.get(
                            '.valuationconclusion > .QVHV-formSection > :nth-child(6) > .fiftyPct > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Special Conditions Description should exist and be visible', () => {
                        cy.get('#specialConditionsDescription')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Additional Comments should exist and be visible', () => {
                        cy.get(
                            '.valuationconclusion > .QVHV-formSection > :nth-child(7) > .fiftyPct > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Additional Comments Description should exist and be visible', () => {
                        cy.get('#additionalCommentsDescription')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Market Rental should exist and be visible', () => {
                        cy.get('#marketRental')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Minimum Weekly Rental should exist and be visible', () => {
                        cy.get('#minimumWeeklyRental')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Maximum Weekly Rental should exist and be visible', () => {
                        cy.get('#maximumWeeklyRental')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Cashflow Sustainability should exist and be visible', () => {
                        cy.get('#cashflowSustainability')
                            .should('exist')
                            .and('be.visible');
                    });
                });

                context('Add and remove rows', () => {
                    context('Valuation Approach', () => {
                        it('Adds Valuation Approach', () => {
                            cy.get(
                                '.valuationconclusion > .QVHV-formSection > :nth-child(3) > .sa-addRemove > .saRow-add'
                            )
                                .click()
                                .then(() => {
                                    cy.get('#valuationApproaches_2').should('exist');
                                });
                        });

                        it('Removes Valuation Approach', () => {
                            cy.get(
                                '.QVHV-formSection > :nth-child(3) > .sa-addRemove > .saRow-remove'
                            ).should('not.exist');
                            cy.get(
                                '.QVHV-formSection > :nth-child(4) > .sa-addRemove > .saRow-remove'
                            )
                                .should('exist')
                                .and('be.visible')
                                .click()
                                .then(() => {
                                    cy.get('#valuationApproaches_2').should('not.exist');
                                });
                        });
                    });
                });
            });
        });
    },

    checkRisks() {
        context('Checks every field exists in Risks for the Report Details', () => {
            context('Switch tab to Risks', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementReportDetails > .QVHVTab-2 > span').click();

                    cy.get('#tabsElementReportDetails > li.QVHVTab-2 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Check Risks elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Expects risks page to be populated', () => {
                        for (let i = 1; i <= 9; i++) {
                            cy.get(`.risks > .QVHV-formSection > :nth-child(${i})`)
                                .should('exist')
                                .and('be.visible');
                        }
                    });
                });
            });
        });
    },

    /*
  _                     _   _               _____       _        _ _
 | |                   | | (_)             |  __ \     | |      (_) |
 | |     ___   ___ __ _| |_ _  ___  _ __   | |  | | ___| |_ __ _ _| |___
 | |    / _ \ / __/ _` | __| |/ _ \| '_ \  | |  | |/ _ \ __/ _` | | / __|
 | |___| (_) | (_| (_| | |_| | (_) | | | | | |__| |  __/ || (_| | | \__ \
 |______\___/ \___\__,_|\__|_|\___/|_| |_| |_____/ \___|\__\__,_|_|_|___/
    */
    checkSaleHistory() {
        context('Checks every field exists in Sale History for the Location Details', () => {
            context('Switch tab to Sale History', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementLocationDetails > .QVHVTab-1 > span').click();

                    cy.get('#tabsElementLocationDetails > li.QVHVTab-1 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Check Sale History elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Sale Date should exist and be visible', () => {
                        cy.get('.rowSpacer > .icons8-calendar-filled > span')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Sale Price should exist and be visible', () => {
                        cy.get('.rowSpacer > .icons8-sell-property-filled > span')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Include in Report should exist and be visible', () => {
                        cy.get('.rowSpacer > :nth-child(3) > label')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Sales Comment should exist and be visible', () => {
                        cy.get(
                            '.salehistory > .QVHV-formSection > .advSearch-row > :nth-child(2) > .advSearch-group > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });
                });
            });
        });
    },

    checkZoning() {
        context('Checks every field exists in Zoning for the Location Details', () => {
            context('Switch tab to Zoning', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementLocationDetails > .QVHVTab-2 > span').click();

                    cy.get('#tabsElementLocationDetails > li.QVHVTab-2 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Check Zoning elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Current Use should exist and be visible', () => {
                        cy.get(
                            '.zoning > .QVHV-formSection > :nth-child(1) > .advSearch-group > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Operative Zoning should exist and be visible', () => {
                        cy.get(
                            '.zoning > .QVHV-formSection > :nth-child(2) > .fiftyPct > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Zone Description should exist and be visible', () => {
                        cy.get('.zoning > .QVHV-formSection > :nth-child(3) > .advSearch-group > :nth-child(2) > .advSearch-text')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Site Density should exist and be visible', () => {
                        cy.get(
                            '.zoning > .QVHV-formSection > :nth-child(4) > .advSearch-group > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Zone Comments should exist and be visible', () => {
                        cy.get(
                            '.zoning > .QVHV-formSection > :nth-child(5) > .advSearch-group > :nth-child(2) > .advSearch-text'
                        )

                            .should('exist')
                            .and('be.visible');
                    });

                    it('"Does current use comply" should exist and be visible', () => {
                        cy.get(
                            '.zoning > .QVHV-formSection > :nth-child(6) > .advSearch-group > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Subdivision Potential should exist and be visible', () => {
                        cy.get(
                            '.zoning > .QVHV-formSection > :nth-child(7) > .advSearch-group > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    context('Proposed Zoning Details', () => {
                        it('Views on expand', () => {
                            cy.get('#Proposed\\ Zoning\\ Values')
                                .click()
                                .then(() => {
                                    cy.get('.propsedZoning-values').should('be.visible');
                                });
                        });

                        it('Proposed Zoning should exist and be visible', () => {
                            cy.get(
                                '.propsedZoning-values > :nth-child(1) > .fiftyPct > .btn-group > .multiselect'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Proposed Zoning Description should exist and be visible', () => {
                            cy.get(
                                '.propsedZoning-values > :nth-child(2) > .hundyPct > :nth-child(2) > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Proposed Site Density should exist and be visible', () => {
                            cy.get(
                                '.propsedZoning-values > :nth-child(3) > .advSearch-group > :nth-child(2) > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Proposed Zone Comments should exist and be visible', () => {
                            cy.get(
                                '.propsedZoning-values > :nth-child(4) > .advSearch-group > :nth-child(2) > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('"Does current use comply" should exist and be visible', () => {
                            cy.get(
                                '.propsedZoning-values > :nth-child(5) > .advSearch-group > span > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });

                        it('Subdivision Potential should exist and be visible', () => {
                            cy.get(
                                '.propsedZoning-values > :nth-child(6) > .advSearch-group > span > .advSearch-text'
                            )
                                .should('exist')
                                .and('be.visible');
                        });
                    });
                });
            });
        });
    },

    checkLegalDescription() {
        context('Checks every field exists in Legal Description for the Location Details', () => {
            context('Switch tab to Legal Description', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementLocationDetails > .QVHVTab-3 > span').click();

                    cy.get('#tabsElementLocationDetails > li.QVHVTab-3 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Check Legal Description elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Record of Title should exist and be visible', () => {
                        cy.get('#legalDetailsComputerRegistered')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Legal Description should exist and be visible', () => {
                        cy.get('#legalDetailsLegalDescription')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Land Area should exist and be visible', () => {
                        cy.get('#legalDetailsLandArea')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Land Area should exist and be visible', () => {
                        cy.get('#legalDetailsLandArea')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Tenure should exist and be visible', () => {
                        cy.get('#legalDetailsTenure > .btn-group > .multiselect')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Proprietors should exist and be visible', () => {
                        cy.get('#legalDetailsOwner')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Comment on Registered Interests should exist and be visible', () => {
                        cy.get('#legalDetailsRegisteredInterestsComment')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Comment on Tenure or Saleability should exist and be visible', () => {
                        cy.get('#legalDetailsTenureOrSaleability')
                            .should('exist')
                            .and('be.visible');
                    });
                });
            });
        });
    },

    checkSiteAndLocation() {
        context('Checks every field exists in Site and Location for the Location Details', () => {
            context('Switch tab to Site and Location', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementLocationDetails > .QVHVTab-4 > span').click();

                    cy.get('#tabsElementLocationDetails > li.QVHVTab-4 > span').should(
                        'be.visible'
                    );
                });
            });

            context('Check Site and Location elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Contour should exist and be visible', () => {
                        cy.get('#countourDropDown')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Shape should exist and be visible', () => {
                        cy.get('#shapeDropDown')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Access should exist and be visible', () => {
                        cy.get('#accessDropDown')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Outlook should exist and be visible', () => {
                        cy.get('#outlookDropDown')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Aspect should exist and be visible', () => {
                        cy.get(
                            '.siteandlocation > .QVHV-formSection > :nth-child(2) > .twentyfivePct'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Position of improvements on site should exist and be visible', () => {
                        cy.get(':nth-child(2) > .seventyfivePct')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Services should exist and be visible', () => {
                        cy.get('#servicesDropDown')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Site Comments should exist and be visible', () => {
                        cy.get('#siteCommentsArea')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Features and Hazards should exist and be visible', () => {
                        cy.get('#featuresAndHazardsArea')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Environmental Consideration should exist and be visible', () => {
                        cy.get('#environmentalConsiderationsArea')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Suburb/Town should exist and be visible', () => {
                        cy.get('#suburbAndTownDropdown')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Proximity of Suburb to CBD should exist and be visible', () => {
                        cy.get('#proximityOfSuburbToCBDText')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Surrounding Development should exist and be visible', () => {
                        cy.get('#surroundingDevelopmentArea')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Facilities to CBD should exist and be visible', () => {
                        cy.get('#facilitiesArea')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Public Transport should exist and be visible', () => {
                        cy.get('#publicTransportArea')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Locality Features should exist and be visible', () => {
                        cy.get('#localityFeaturesArea')
                            .should('exist')
                            .and('be.visible');
                    });
                });
            });
        });
    },

    checkRatingInformation() {
        context('Checks every field exists in Rating Information for the Location Details', () => {
            context('Switch tab to Rating Information', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementLocationDetails > .QVHVTab-5 > span').click();

                    cy.get('#tabsElementLocationDetails > li.QVHVTab-5 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Check Rating Information elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Capital Value should exist and be visible', () => {
                        cy.get('#ratingInfoCapitalValue')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Land Value should exist and be visible', () => {
                        cy.get('#ratingInfoLandValue')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Value of Improvements should exist and be visible', () => {
                        cy.get(
                            '.ratinginformation > .QVHV-formSection > :nth-child(1) > :nth-child(3) > span'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Effective Date should exist and be visible', () => {
                        cy.get('#ratingInfoEffectiveDate')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Current Value should exist and be visible', () => {
                        cy.get('#ratingInfoCurrentValue')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('ValuationReference should exist and be visible', () => {
                        cy.get('#ratingInfoValuationReference')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Comment on Rating Valuation should exist and be visible', () => {
                        cy.get('#commentsArea')
                            .should('exist')
                            .and('be.visible');
                    });
                });
            });
        });
    },

    /*
   ____                              _   _____            _
  / __ \     /\                     | | |  __ \          (_)
 | |  | |   /  \      __ _ _ __   __| | | |__) |_____   ___  _____      __
 | |  | |  / /\ \    / _` | '_ \ / _` | |  _  // _ \ \ / / |/ _ \ \ /\ / /
 | |__| | / ____ \  | (_| | | | | (_| | | | \ \  __/\ V /| |  __/\ V  V /
  \___\_\/_/    \_\  \__,_|_| |_|\__,_| |_|  \_\___| \_/ |_|\___| \_/\_/


    */

    checkReportAndCompliance() {
        context('Checks every field exists in Report & Compliance for the QA and Review', () => {
            context('Switch tab to Report & Compliance', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementQAAndReview > .QVHVTab-1 > span').click();

                    cy.get('#tabsElementQAAndReview > li.QVHVTab-1 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Check Report & Compliance elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Date of Valuation should exist and be visible', () => {
                        cy.get(
                            '.reportAndCompliance > .QVHV-formSection > :nth-child(1) > .advSearch-group > span > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Valuation Standards should exist and be visible', () => {
                        cy.get(
                            '.reportAndCompliance > .QVHV-formSection > :nth-child(2) > .advSearch-group > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Logos should exist and be visible', () => {
                        cy.get(
                            '.reportAndCompliance > .QVHV-formSection > :nth-child(3) > .advSearch-group > .btn-group > .multiselect'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Compliance Statement should exist and be visible', () => {
                        cy.get(
                            '.reportAndCompliance > .QVHV-formSection > :nth-child(4) > .advSearch-group > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Documents Sighted', () => {
                        cy.get(
                            '.reportAndCompliance > .QVHV-formSection > :nth-child(5) > .advSearch-group > :nth-child(2) > .advSearch-text'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Report File Type should exist and be visible', () => {
                        cy.get(
                            '.reportAndCompliance > .QVHV-formSection > :nth-child(6) > .required'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Report Branding should exist and be visible', () => {
                        cy.get('.QVHV-formSection > :nth-child(6) > .righty')
                            .should('exist')
                            .and('be.visible');
                    });

                    this.checkQVCloudUploader();

                    it('Complete Button should exist and be visible', () => {
                        cy.get('.QVHV-buttons-left > :nth-child(2)')
                            .should('exist')
                            .and('be.visible');
                    });
                });
            });
        });
    },

    checkCountersignerReview() {
        context('Checks every field exists in Countersigner Review for the QA and Review', () => {
            context('Switch tab to Countersigner Review', () => {
                it('Clicks to new tab', () => {
                    cy.get('#tabsElementQAAndReview > .QVHVTab-2 > span').click();

                    cy.get('#tabsElementQAAndReview > li.QVHVTab-2 > .is-active')
                        .should('be.visible')
                        .and('have.class', 'is-active');
                });
            });

            context('Check Countersigner Review elements should exist and be visible', () => {
                context('Check elements exist', () => {
                    it('Request Countersigner Review should exist and be visible', () => {
                        cy.get('.peerReview > .QVHV-formSection > :nth-child(1)')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Request Countersigner Review expands on click', () => {
                        cy.get('#peerReview-1')
                            .should('exist')
                            .and('be.visible')
                            .click()
                            .then(() => {
                                cy.get('.qvhvReport-files > :nth-child(1) > .advSearch-row')
                                    .should('exist')
                                    .and('be.visible');
                                cy.get(
                                    ':nth-child(1) > .advSearch-row > .fiftyPct > .btn-group > .multiselect'
                                )
                                    .should('exist')
                                    .and('be.visible');
                                cy.get(
                                    ':nth-child(1) > .advSearch-row > :nth-child(2) > .advSearch-group > span > .advSearch-text'
                                )
                                    .should('exist')
                                    .and('be.visible');
                                cy.get(
                                    '.advSearch-row > .QVHV-buttons > .QVHV-buttons-right > .primary'
                                )
                                    .should('exist')
                                    .and('be.visible');
                            });
                    });

                    this.checkQVCloudUploader();
                });
            });
        });
    },
    checkQVCloudUploader() {
        context('QV Cloud Uploader', () => {
            it('QV Cloud Uploader should exist and be visible', () => {
                cy.get(
                    '.qaAndReview > .md-table > :nth-child(6) > .QVHV-formSection > .advSearch-row'
                )
                    .should('exist')
                    .and('be.visible');
            });

            it('Report QPID should exist and be visible', () => {
                cy.get('.QVHV-formSection > .advSearch-row > .icons8-geo-fence')
                    .should('exist')
                    .and('be.visible');
            });

            it('Report Type should exist and be visible', () => {
                cy.get(':nth-child(6) > .QVHV-formSection > .advSearch-row > .icons8-news-filled')
                    .should('exist')
                    .and('be.visible');
            });

            it('Website User ID should exist and be visible', () => {
                cy.get(
                    '.qaAndReview > .md-table > :nth-child(6) > .QVHV-formSection > .advSearch-row > .icons8-contacts'
                )
                    .should('exist')
                    .and('be.visible');
            });

            it('Subscription ID should exist and be visible', () => {
                cy.get(
                    ':nth-child(6) > .QVHV-formSection > .advSearch-row > .icons8-category-filled'
                )
                    .should('exist')
                    .and('be.visible');
            });

            it('Dropzone should exist and be visible', () => {
                cy.get('.fs-btn-select > .uploaderBody > .dropzone')
                    .should('exist')
                    .and('be.visible');
            });

            it('Upload should exist and be visible', () => {
                cy.get('.fs-btn-select > .uploaderBody > .dropzone')
                    .should('exist')
                    .and('be.visible');
            });
        });
    },

    uploadFileQVCloudUploader() {
        const id = 33;
        cy.get('.fs-btn-select > .uploaderBody > .dropzone')
            .attachFile('sample-image.jpg', {
                subjectType: 'drag-n-drop'
            })
            .then(() => {
                cy.get('.icons8-contacts > .advSearch-text').type(id.toString());
                cy.get('[style="padding-top: 1.5em;"] > .primary').click();
            });
    }
};

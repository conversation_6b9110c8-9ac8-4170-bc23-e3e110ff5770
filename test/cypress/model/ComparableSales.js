import PropertyDetails from "./PropertyDetails";
import ValuationJob from "./ValuationJob";

export default {
  elements: {
    sortTableHeader(headerName) {
      return `[data-sort="${headerName}"] > a`;
    },
    get comparableRows() {
      return cy.get('.compsRow-wrapper');
    },
    get comparablePropertiesDistanceInput() {
      // note: does not come through under 'data-cy="comparable-properties-distance"' as either '> input' or '> #propertyFrom'
      return cy.get('#propertyFrom');
    },
    get comparablePropertiesGrossPriceFromInput() {
      // note: does not come through under 'data-cy="comparable-properties-gross-price"' as either '> input' or '> #grossPriceFrom'
      return cy.get('#grossPriceFrom');
    },
    get comparablePropertiesGrossPriceToInput() {
      // note: does not come through under 'data-cy="comparable-properties-gross-price"' as either '> input' or '> #grossPriceTo'
      return cy.get('#grossPriceTo');
    },
    get comparablePropertiesDateRange() {
      return cy.get('[data-cy="comparable-properties-date-range"]');
    },
    get comparablePropertiesDateRangeInput() {
      return cy.get('[data-cy="comparable-properties-date-range"] > input');
    },
    get comparablePropertiesCategories() {
      return cy.get('[data-cy="comparable-properties-categories"]');
    },
    get comparablePropertiesZones() {
      return cy.get('[data-cy="comparable-properties-zones"]');
    },
    get comparablePropertyCategory() {
      return cy.get('[data-cy="comparable-property-category"]');
    },
    get comparablePropertyZone() {
      return cy.get('[data-cy="comparable-property-zone"]');
    },
    comparablePropertyLandArea: '[data-cy="comparable-property-land-area"]',
    comparablePropertySaleDate: '[data-cy="comparable-property-sale-date"]',
    comparablePropertyGrossPrice: '[data-cy="comparable-property-gross-price"]',
    comparablePropertyWalls: '[data-cy="comparable-property-walls"]',
    rowClose: 'li[title="Close"]',
  },

  goToComparableProperties(qpid) {
    cy.visitQpid(qpid);
    cy.wait(15000);

    PropertyDetails.valuationJobsButton.click();
    cy.wait(3000);

    PropertyDetails.valuationJobsList.contains('li', 'New Valuation Job').should('exist');
    PropertyDetails.valuationJobsList.children().should('have.length.greaterThan', 1);
    PropertyDetails.valuationJobsList.children().each(($el, idx, $list) => {
      if (idx === 1) {
        cy.wrap($el).click();
      }
    });

    ValuationJob.stepperItem('comparableProperties').should('exist').click();
    ValuationJob.stepperItem('comparableProperties').should('have.class', 'active');
    this.elements.comparablePropertiesCategories.should('have.length.greaterThan', 0);
    this.elements.comparablePropertiesDateRangeInput.should('have.length.greaterThan', 0);
  },

  clearCategories() {
    this.elements.comparablePropertiesCategories.clear().blur();
    cy.wait(5000);
  },

  clearDateRange() {
    this.elements.comparablePropertiesDateRangeInput.click();
    cy.get('.daterangepicker').each($el => {
      if (Cypress.dom.isVisible($el)) {
        cy.wrap($el).find('.cancelBtn').click();
      }
    });
    cy.wait(5000);
  },
}

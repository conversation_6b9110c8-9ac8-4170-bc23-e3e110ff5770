
export default {
    elements: {
        get cvCategories() {
            return cy.getBySel('cv-categories');
        },
        get cvNetSalePrice() {
            return cy.getBySel('cv-net-sale-price');
        },
        get cvFromNetSalePrice() {
            return cy.getBySel('cv-from-net-sale-price');
        },
        get cvToNetSalePrice() {
            return cy.getBySel('cv-to-net-sale-price');
        },
        get cvSaleDate() {
            return cy.getBySel('cv-sale-date');
        },
        get cvFromSaleDates() {
            return cy.getBySel('cv-from-sale-date');
        },
        get cvToSaleDate() {
            return cy.getBySel('cv-to-sale-date');
        },
        get cvLandValue() {
            return cy.getBySel('cv-land-value');
        },
        get cvFromLandValue() {
            return cy.getBySel('cv-from-land-value');
        },
        get cvToLandValue() {
            return cy.getBySel('cv-to-land-value');
        },
        get cvLandZone() {
            return cy.getBySel('cv-land-zone');
        },
        get cvDistance() {
            return cy.getBySel('cv-distance');
        },
        get cvTotalLivingArea() {
            return cy.getBySel('cv-total-living-area');
        },
        get cvFromTotalLivingArea() {
            return cy.getBySel('cv-from-total-living-area');
        },
        get cvToTotalLivingArea() {
            return cy.getBySel('cv-to-total-living-area');
        },
        get cvNetRate() {
            return cy.getBySel('cv-net-rate');
        },
        get cvFromNetRate() {
            return cy.getBySel('cv-from-net-rate');
        },
        get cvToNetRate() {
            return cy.getBySel('cv-to-net-rate');
        },
        get cvEffectiveYearBuilt() {
            return cy.getBySel('cv-effective-year-built');
        },
        get cvFromEffectiveYearBuilt() {
            return cy.getBySel('cv-from-effective-year-built');
        },
        get cvToEffectiveYearBuilt() {
            return cy.getBySel('cv-to-effective-year-built');
        },
        get lvComparables() {
            return cy.getBySel('lv-comparables');
        },
        get lvCategories() {
            return cy.getBySel('lv-categories');
        },
        get lvNetSalePrice() {
            return cy.getBySel('lv-net-sale-price-range');
        },
        get lvFromNetSalePrice() {
            return cy.getBySel('lv-from-net-sale-price-range');
        },
        get lvToNetSalePrice() {
            return cy.getBySel('lv-to-net-sale-price-range');
        },
        get lvSaleDate() {
            return cy.getBySel('lv-sale-date-range');
        },
        get lvFromSaleDates() {
            return cy.getBySel('lv-from-sale-date-range');
        },
        get lvToSaleDate() {
            return cy.getBySel('lv-to-sale-date-range');
        },
        get lvLandValue() {
            return cy.getBySel('lv-land-value-range');
        },
        get lvFromLandValue() {
            return cy.getBySel('lv-from-land-value-range');
        },
        get lvToLandValue() {
            return cy.getBySel('lv-to-land-value-range');
        },
        get lvLandZone() {
            return cy.getBySel('lv-land-zone');
        },
        get lvDistance() {
            return cy.getBySel('lv-distance');
        },
        get lvLandArea() {
            return cy.getBySel('lv-land-area');
        },
        get lvFromLandArea() {
            return cy.getBySel('lv-from-land-area');
        },
        get lvToLandArea() {
            return cy.getBySel('lv-to-land-area');
        },
        get lvLandSaleNetRate() {
            return cy.getBySel('lv-land-sale-net-rate');
        },
        get lvFromLandSaleNetRate() {
            return cy.getBySel('lv-from-land-sale-net-rate');
        },
        get lvToNetLandSaleNetRate() {
            return cy.getBySel('lv-to-land-sale-net-rate');
        },
        get comparablesReset() {
            return cy.getBySel('comparables-reset');
        },
        get comparablesSearch() {
            return cy.getBySel('comparables-search');
        },
        get lvComparablesTableAddress() {
            return cy.getBySel('lv-comparables-table-address');
        },
        get lvComparablesTableValuationReference() {
            return cy.getBySel('lv-comparables-table-valuation-reference');
        },
        get lvComparablesTableSaleDate() {
            return cy.getBySel('lv-comparables-table-sale-date');
        },
        get lvComparablesTableNetSalePrice() {
            return cy.getBySel('lv-comparables-table-net-sale-price');
        },
        get lvComparablesTableCurrentLandValue() {
            return cy.getBySel('lv-comparables-table-current-land-value');
        },
        get lvComparablesTableLandArea() {
            return cy.getBySel('lv-comparables-table-land-area');
        },
        get lvComparablesTableZoneCode() {
            return cy.getBySel('lv-comparables-table-zone-code');
        },
        get lvComparablesTableContourCode() {
            return cy.getBySel('lv-comparables-table-contour-code');
        },
        get lvComparablesTableViewCodeViewScopeCode() {
            return cy.getBySel('lv-comparables-table-view-code-view-scope-code');
        },
        get lvComparablesTableLotPositionCode() {
            return cy.getBySel('lv-comparables-table-lot-position-code');
        },
        get lvComparablesTablePropertyCategory() {
            return cy.getBySel('lv-comparables-table-property-category');
        },
        get lvComparablesTableLandSaleNetRate() {
            return cy.getBySel('lv-comparables-table-land-sale-net-rate');
        },
        get lvComparablesTableDistance() {
            return cy.getBySel('lv-comparables-table-distance');
        },
        get lvComparablesTableComparabilityScore() {
            return cy.getBySel('lv-comparables-table-comparability-score');
        },
        get cvComparablesTableAddress() {
            return cy.getBySel('cv-comparables-table-address');
        },
        get cvComparablesTableValuationReference() {
            return cy.getBySel('cv-comparables-table-valuation-reference');
        },
        get cvComparablesTableSaleDate() {
            return cy.getBySel('cv-comparables-table-sale-date');
        },
        get cvComparablesTableNetSalePrice() {
            return cy.getBySel('cv-comparables-table-net-sale-price');
        },
        get cvComparablesTableCurrentLandValue() {
            return cy.getBySel('cv-comparables-table-current-land-value');
        },
        get cvComparablesTableLandArea() {
            return cy.getBySel('cv-comparables-table-land-area');
        },
        get cvComparablesTableTotalFloorArea() {
            return cy.getBySel('cv-comparables-table-total-floor-area');
        },
        get cvComparablesTableTotalLivingArea() {
            return cy.getBySel('cv-comparables-table-total-living-area');
        },
        get cvComparablesTableUnderMainRoofGarages() {
            return cy.getBySel('cv-comparables-table-under-main-roof-garages');
        },
        get cvComparablesTableFreestandingGarages() {
            return cy.getBySel('cv-comparables-table-freestanding-garages');
        },
        get cvComparablesTableCategory() {
            return cy.getBySel('cv-comparables-table-category');
        },
        get cvComparablesTableBuildingNetRate() {
            return cy.getBySel('cv-comparables-table-building-net-rate');
        },
        get cvComparablesTableBuildingNetRateColumnValues() {
            return cy.get(':nth-child(2) > :nth-child(13)');
        },
        get cvComparablesTableDistance() {
            return cy.getBySel('cv-comparables-table-distance');
        },
        get cvComparablesTableComparabilityScore() {
            return cy.getBySel('cv-comparables-table-comparability-score');
        },
    }
};
/*global cy it describe context */

export default {
    elements: {
        get pageTitle() {
            return cy.get('[data-cy="sale-page-title"]');
        },
        get deleteButton() {
            return cy.get('[data-cy="button-sale-delete"]');
        },
        get cancelButton() {
            return cy.get('[data-cy="cancel-sale-button"]');
        },
        get saveButton() {
            return cy.get('[data-cy="save-sale-button"]');
        },
        get updateAndProcessButton() {
            return cy.get('[data-cy="update-process-sale-button"]');
        },
        get titles() {
            return cy.get('[data-cy="property-titles"]');
        },
        get saleDetails() {
            return cy.get('[data-cy="sale-details"]');
        },
        get crossReferencedProperties() {
            return cy.get('[data-cy="sale-cross-referenced-properties"]');
        },
        get dvrDetails() {
            return cy.get('[data-cy="dvr-property-details"]');
        },
        get reasonForChange() {
            return cy.get('[data-cy="reason-for-change"]');
        },
        get form() {
            return cy.get('[data-cy="sales-processing"]');
        },
        get formInputs() {
            return this.form.find('input, select');
        },
        saleInput(name) {
            return cy.get(`[data-cy="input-sale-${name}"]`);
        },
        dvrInput(name) {
            return cy.get(`[data-cy="input-dvr-${name}"]`);
        },
        get confirmValidationButton() {
            return cy.get(`[data-cy="button-sale-validation-confirm"]`);
        },
        get confirmDeleteButton() {
            return cy.get(`[data-cy="button-modal-confirm"]`).contains('DELETE');
        },
        multiselectDropdownNameString(name, saleType) {
            return `[data-cy="input-${saleType}-${name}"]`;
        },
    },

    deleteSale() {
        this.elements.deleteButton.click();
        this.elements.confirmDeleteButton.click();
    },

    saveSale() {
        this.elements.saveButton.click();
        this.elements.confirmValidationButton.then(($button) => {
            $button.trigger('click');
        });
    },

    fillDefaultFields(timestamp) {
        this.multiselectDropdownSelect('status', 'sale', 0);

        this.elements.saleInput('gross').type('1000000');
        this.elements.saleInput('vendor').type(`TEST:${timestamp}`);
        this.elements.saleInput('legal').clear();
        this.elements.saleInput('legal').type('Automated Test');

        const date = new Date().toISOString().split('T')[0];
        this.elements.saleInput('agreement-date').type(date);
        this.elements.saleInput('settlement-date').type(date);
        this.elements.saleInput('pdf').type('222');
    },

    isLoaded() {
        this.elements.form.should('exist');
    },

    visit(qpid, saleId) {
        cy.visit(`/roll-maintenance/${qpid}/sale/${saleId}`);
    },

    multiselectDropdownSelect(name, saleType, selectIndex) {
        cy.get(this.elements.multiselectDropdownNameString(name, saleType))
            .click()
            .then(() => {
                cy.get(`${this.elements.multiselectDropdownNameString(name, saleType)} > .multiselect__content-wrapper > #listbox-null > #null-${selectIndex} > .multiselect__option`).click({ force: true });
            });
    },
};

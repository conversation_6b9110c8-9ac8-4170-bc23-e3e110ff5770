
export const dashboard = {
    elements: {
        get menuPanel() {
            return cy.get('div[data-cy=reports-menu-panel]');
        },
        get notificationsPanel() {
            return cy.get('div[data-cy=reports-notifications-panel]');
        },
        get mainPanel() {
            return cy.get('div[data-cy=reports-main-panel]');
        },
        get url() {
            return '/reports/my-reports';
        }
    },

    visit() {
        cy.visit('/reports/my-reports');
    }
}
export default {
    elements: {
        get myReportsRefreshButton() {
            return cy.getBySel('my-reports-refresh-button');
        },
        get jobFirstColumns() {
            return cy.get('[data-cy="my-reports-table"] tbody tr:nth-child(1)');
        },
        get jobFirstColumnsFileSize() {
            return cy.get('[data-cy="my-reports-table"] tbody tr:nth-child(1)').find('td:nth-child(4)')
        },

    }
}
export const myReports = {
    elements: {
        get heading() {
            return cy.get('h2[data-cy=my-reports-heading]');
        },
        get jobTable() {
            return cy.get('table[data-cy=my-reports-table]');
        },
        get jobTableRow() {
            return cy.get('[data-cy="my-reports-table-row"]');
        },
        get jobColumns() {
            return cy.get('table[data-cy=my-reports-table] th');
        },
        get deleteButton() {
            return cy.get('[data-cy="delete-report-button"]');
        },
    },
}

export const reportMenu = {
    elements: {
        get menuWrapper() {
            return cy.get('div[data-cy=reports-menu]');
        },
        get viewMyReportsButton() {
            return cy.get('button[data-cy=my-reports-button]');
        },
        get reportCategories() {
            return cy.get('div[data-cy=report-category]');
        },
        get reports() {
            return cy.get('div[data-cy=report-item]');
        },


    },

    expand() {
        this.elements.reportCategories.then(categories => {
            for (let i = 0; i < categories.length; i++) {
                if (!categories[i].classList.contains('expanded')) {
                    categories[i].click();
                }
            }
        });
    },

    collapse() {
        this.elements.reportCategories.then(categories => {
            for (let i = 0; i < categories.length; i++) {
                if (categories[i].classList.contains('expanded')) {
                    categories[i].click();
                }
            }
        });
    }
}

export const reportCriteria = {
    elements: {
        get criteriaWrapper() {
            return cy.get('div[data-cy=report-criteria]');
        },
        get pageMask() {
            return cy.get('div[data-cy=report-page-mask]');
        },
        get clearCriteriaButton() {
            return cy.get('button[data-cy=clear-button]');
        },
        get scheduleReportButton() {
            return cy.get('button[data-cy=schedule-report-button]');
        },
        get reportModal() {
            return cy.get('[data-cy="global-modal"]');
        },
        get alertModal() {
            return cy.get('div[data-cy="alert-modal"]');
        },
        get alertModalText() {
            return cy.get('div[data-cy="alert-modal"] > p');
        },
        get reportModalConfirm() {
            return cy.get('[data-cy="button-dialog-confirm"]');
        },
        get reportModalCancel() {
            return cy.get('[data-cy="button-dialog-cancel"]');
        },
    },

    visit() {
        cy.visit('/reports/QV_ANALYTICS_HOUSE_PRICE_INDEX_MONTHLY_DATA');
    },

    clearCriteria() {
        this.elements.clearCriteriaButton.click();
    }
}

export const subdivisionListingReport = {
    elements: {
        get statusMultiselect() {
            return cy.get('[data-cy="status-multiselect"]');
        },
        get startDatePicker() {
            return cy.get('[data-cy="start-date"]');
        },
        get endDatePicker() {
            return cy.get('[data-cy="end-date"]');
        },
        get startDateButton() {
            return cy.get('.cell').eq(0);
        },
        get endDateButton() {
            return cy.get('.today').eq(0);
        },
        get taMultiselect() {
            return cy.get('[data-cy="ta-multiselect"]');
        },
        get clearReportButton() {
            return cy.get('[data-cy="clear-report-button"]');
        },
        get modalCancelButton() {
            return cy.get('[data-cy="report-modal-cancel"]');
        },
    },

    visit() {
        cy.visit('/reports/SUBDIVISIONS_LISTING');
    }
}
export const objectionListingReport = {
    elements: {
        get revisionDatePicker() {
            return cy.get('[data-cy=revision-date]');
        },
        get revisionDateButton() {
            return cy.get('.cell').eq(1);
        },
    },

    visit() {
        cy.visit('/reports/OBJECTIONS_LISTING');
    }
}

export const exceptionListingReport = {
    elements: {
        get singleRollInput() {
            return cy.get('[data-cy=single-roll-input]');
        }
    },

    visit() {
        cy.visit('/reports/EXCEPTION_LIST');
    }
}

export const OvgStatisticsReport = {
    elements: {
        get startDatePicker() {
            return cy.get('[data-cy=start-date]');
        },
        get endDatePicker() {
            return cy.get('[data-cy=end-date]');
        },
        get startDateButton() {
            return cy.get('.cell').eq(1);
        },
        get endDateButton() {
            return cy.get('.cell').eq(10);
        },
        get singleValueInput() {
            return cy.get('[data-cy=single-value]');
        },
        get categoryInput() {
            return cy.get('[data-cy=category-input]');
        }
    },

    visit() {
        cy.visit('/reports/OVG_STATISTICS');
    }
}

export const CommercialWorksheetReport = {
    elements: {
        get singleRollInput() {
            return cy.get('[data-cy=single-roll-input]');
        },
        get categoryInput() {
            return cy.get('[data-cy=category-input]');
        }
    },

    visit() {
        cy.visit('/reports/COMMERCIAL_WORKSHEET_REPORT');
    }
}

export const CutdownOvgRollExtracts = {
    visit() {
        cy.visit('/reports/CUTDOWN_OVG_ROLL_EXTRACTS');
    }
}

export const CutdownOvgSalesExtracts = {
    elements: {
        get startDatePicker() {
            return cy.get('[data-cy=date-from]');
        },
        get endDatePicker() {
            return cy.get('[data-cy=date-to]');
        },
        get startDateButton() {
            return cy.get('.cell').eq(1);
        },
        get endDateButton() {
            return cy.get('.cell').eq(10);
        }
    },

    visit() {
        cy.visit('/reports/CUTDOWN_OVG_SALES_EXTRACTS');
    }
}

export const ReconciliationStatementReport = {
    elements: {
        get singleRollInput() {
            return cy.get('[data-cy=single-roll-input]');
        },
        get closingDatePicker() {
            return cy.get('[data-cy=closing-date]');
        },
        get closingDateButton() {
            return cy.get('.cell').eq(1);
        },
    },

    visit() {
        cy.visit('/reports/RECONCILIATION_STATEMENT');
    }
}

export const RuralWorksheetReport = {
    elements: {
        get singleRollInput() {
            return cy.get('[data-cy=single-roll-input]');
        },
        get taSelector() {
            return cy.get('[data-cy=ta-selector]');
        },
        get inputCategory() {
            return cy.get('[data-cy=category-input]');
        }
    },

    visit() {
        cy.visit('/reports/RURAL_WORKSHEET_REPORT');
    }
}

export const EstimateOfUpcomingConsentReport = {
    elements: {
        get taSelector() {
            return cy.get('[data-cy="territorial-authority-selector"]');
        },
        get dateToEstimateUntilDatePicker() {
            return cy.get('[data-cy="date-to-estimate-until-datepicker"]');
        }
    },

    scheduleReport() {
        this.elements.taSelector.type('1{enter}');
        reportCriteria.elements.scheduleReportButton.click();
    },

    visit() {
        cy.visit('/reports/ESTIMATE_UPCOMING_CONSENTS');
    }
}

export const SummaryOfConsentsByTAOrValuerReport = {
    elements: {
        get taSelector() {
            return cy.get('[data-cy="territorial-authority-selector"]');
        },
        get valuerSelector() {
            return cy.get('[data-cy="valuer-selector"]');
        },
        get yearInput() {
            return cy.get('[data-cy="year-of-report-input"]');
        }
    },

    scheduleTAReport() {
        this.elements.taSelector.type('1{enter}');
        this.elements.yearInput.type('2024{enter}');
        reportCriteria.elements.scheduleReportButton.click();
    },

    scheduleValuerReport() {
        this.elements.valuerSelector.type('1{enter}');
        this.elements.yearInput.type('2024{enter}');
        reportCriteria.elements.scheduleReportButton.click();
    },

    visit() {
        cy.visit('/reports/SUMMARY_CONSENTS_TA_OR_VALUER');
    }
}

export const OVGCostRecoveryStatisticsReport = {
    elements: {
        get taSelector() {
            return cy.get('[data-cy="territorial-authority-selector"]');
        },
        get startDate() {
            return cy.get('[data-cy="start-date-datepicker"]');
        },
        get endDate() {
            return cy.get('[data-cy="end-date-datepicker"]');
        },
        get reportModalCancel() {
            return cy.get('[data-cy="report-modal-cancel"]');
        },
        get reportModalConfirm() {
            return cy.get('[data-cy="report-modal-confirm"]');
        }
    },

    populateReportCriteria(inputTAs = ['1']) {
        for(const ta of inputTAs) {
            this.elements.taSelector.type(ta + '{enter}');
        }
        cy.get('body').click(0, 0);
        this.elements.startDate.type('01/01/2021');
        this.elements.endDate.type('01/01/2024');
    },

    scheduleReport(inputTAs = '1') {
        this.populateReportCriteria(inputTAs);
        reportCriteria.elements.scheduleReportButton.click();
    },

    visit() {
        cy.visit('/reports/OVG_COST_RECOVERY_STATISTICS');
    }
}

export const SalesListingReport = {
    elements: {
        get taSelector() {
            return cy.get('[data-cy="ta-selector"]');
        },
        get rollNumberInput() {
            return cy.get('[data-cy="roll-number-input"]');
        },
        get salesGroupInput() {
            return cy.get('[data-cy="sales-group-input"]');
        },
        get categoryInput() {
            return cy.get('[data-cy="category-input"]');
        },
        get saleStartDate() {
            return cy.get('[data-cy="sale-start-date-datepicker" ]');
        },
        get saleEndDate() {
            return cy.get('[data-cy="sale-end-date-datepicker"]');
        },
        get reportModalCancel() {
            return cy.get('[data-cy="button-dialog-cancel"]')
        }
    },

    scheduleRollReport() {
        this.elements.rollNumberInput.type('123');
        this.elements.categoryInput.type('RD');
        this.elements.saleStartDate.type('01/01/2021');
        this.elements.saleEndDate.type('01/01/2024');
        reportCriteria.elements.scheduleReportButton.click();
    },

    scheduleSalesGroupReport() {
        this.elements.taSelector.type('1{enter}');
        cy.get('body').click(0, 0);
        this.elements.salesGroupInput.type('11');
        this.elements.categoryInput.type('RD');
        this.elements.saleStartDate.type('01/01/2021');
        this.elements.saleEndDate.type('01/01/2024');
        reportCriteria.elements.scheduleReportButton.click();
    },

    scheduleTAReport() {
        this.elements.taSelector.type('1{enter}');
        cy.get('body').click(0, 0);
        this.elements.categoryInput.type('RD');
        this.elements.saleStartDate.type('01/01/2021');
        this.elements.saleEndDate.type('01/01/2024');
        reportCriteria.elements.scheduleReportButton.click();
    },

    visit() {
        cy.visit('/reports/SALES_LISTING');
    }
}

export const RevaluationSurveyResults = {
    visit() {
        cy.visit('/reports/REVALUATION_SURVEY_RESULTS');
    }
}

export const SalesAnalysisReport = {
    elements: {
        get singleRollInput() {
            return cy.get('[data-cy="single-roll-input"]');
        },
        get taSelector() {
            return cy.get('[data-cy="ta-selector"]');
        },
        get saleDateRangeFrom() {
            return cy.get('[data-cy="sale-date-from-datepicker"]');
        },
        get saleDateRangeTo() {
            return cy.get('[data-cy="sale-date-to-datepicker"]');
        },
        get categoryInput() {
            return cy.get('[data-cy="category-input"]');
        },
        get categoryGroupInput() {
            return cy.get('[data-cy="category-group-input"]');
        },

        get netPriceRangeFrom() {
            return cy.get('[data-cy="net-price-range-from-input"]');
        },
        get netPriceRangeTo() {
            return cy.get('[data-cy="net-price-range-to-input"]');
        },

        get capitalValueRangeFrom() {
            return cy.get('[data-cy="capital-value-range-from-input"]');
        },
        get capitalValueRangeTo() {
            return cy.get('[data-cy="capital-value-range-to-input"]');
        },

        get productionUnitsRangeFrom() {
            return cy.get('[data-cy="production-unit-from-input"]');
        },
        get productionUnitsRangeTo() {
            return cy.get('[data-cy="production-unit-to-input"]');
        },

        get analysedSalesOnlyRadio() {
            return cy.get('[data-cy="analysed-sale-only-radio"]');
        },

        get marketFreeholdSalesOnlyRadio() {
            return cy.get('[data-cy="market-freehold-sales-only-radio"]');
        },

        get fullReport() {
            return cy.get('[data-cy="full-report-radio"]');
        },

        get standardReport() {
            return cy.get('[data-cy="standard-report-radio"]');
        },

        get clearReportButton() {
            return cy.get('[data-cy="clear-report-button"]');
        },
    },

    inputFields() {
        this.elements.taSelector.type('1{enter}');
        cy.get('body').click(0, 0);
        cy.wait(1000);
        this.elements.saleDateRangeFrom.type('01/01/2021');
        this.elements.categoryInput.type('R*');
        this.elements.standardReport.click();
    },

    // standard, commercial, rural, residential
    scheduleStandardReport() {
        this.inputFields();
        reportCriteria.elements.scheduleReportButton.click();
    },

    scheduleReport(category) {
        this.elements.singleRollInput.type('123');
        this.elements.saleDateRangeFrom.type('01/01/2021');
        this.elements.categoryInput.type(category);
        reportCriteria.elements.scheduleReportButton.click();
    },

    scheduleCommercialReport() {
        this.scheduleReport('C*');
    },

    scheduleRuralReport() {
        this.scheduleReport('AIA');
    },

    scheduleResidentialReport() {
        this.scheduleReport('R*');
    },

    visit() {
        cy.visit('/reports/SALES_ANALYSIS');
    }
}

export const SalesExtractReport = {
    elements: {
        get taSelector() {
            return cy.get('[data-cy="territorial-authority-selector"]');
        },
        get saleDateRangeFrom() {
            return cy.get('[data-cy="sale-date-from-datepicker"]');
        },
        get categoryInput() {
            return cy.get('[data-cy="category-input"]');
        }
    },

    scheduleReport() {
        this.elements.taSelector.type('1{enter}');
        cy.get('body').click(0, 0);
        cy.wait(1000);
        this.elements.saleDateRangeFrom.type('01/01/2021');
        this.elements.categoryInput.type('R*');
        reportCriteria.elements.scheduleReportButton.click();
    },

    visit() {
        cy.visit('/reports/SALES_EXTRACT');
    }
}

export const TARatingSummaryReport = {
    elements: {
        get taSelector() {
            return cy.get('[data-cy="territorial-authority-selector"]');
        },
    },

    scheduleReport() {
        this.elements.taSelector.click().type('1', {force: true}).type('{enter}', {force: true});
        cy.get('body').click(0, 0);
        reportCriteria.elements.scheduleReportButton.click();
    },

    visit() {
        cy.visit('/reports/TA_RATING_SUMMARY');
    }
}
export default {
    get includeCategory1() {
        return cy.get('input.propertyIncludedCategories').first();
    },

    get excludeCategory1() {
        return cy.get('input.propertyExcludedCategories').first();
    },

    get bedroomRangeFrom() {
        return cy.get('#from-propertyBedroomsCount');  
    },

    get bedroomRangeTo() {
        return cy.get('#to-propertyBedroomsCount');
    },

    get landAreaFrom() {
        return cy.get('#from-propertyLandArea');
    },

    get landAreaTo() {
        return cy.get('#to-propertyLandArea');
    },

    get buildingAgeMultiselect() {
        return cy.get('[data-cy="property-building-age"]');
    },

    buildingAge(type) {
        this.buildingAgeMultiselect.within(() => {
            cy.get('.caret').click();
            cy.contains(type).click();
            cy.get('.caret').click({ force: true });
        });
    },
};

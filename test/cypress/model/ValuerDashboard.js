export default {
    elements: {
        get filterSection() {
            return cy.get('[data-cy="vd-filer-section"]');
        },
        get valuerFilter() {
            return cy.get('[data-cy="vd-filter-valuer"]');
        },
        get dateFilter() {
            return cy.get('[data-cy="vd-filter-datePeriod"]');
        },
        get customDateFromFilter() {
            return cy.get('[data-cy="vd-filter-custom-from-date"]');
        },
        get customDateToFilter() {
            return cy.get('[data-cy="vd-filter-custom-to-date"]');
        },
        get taFilter() {
            return cy.get('[data-cy="vd-filter-ta"]');
        },
        get ratingSection() {
            return cy.get('[data-cy="vd-rating"]');
        },
        get consultancySection() {
            return cy.get('[data-cy="vd-consultancy"]');
        },
        get activityPieChart() {
            return cy.get('[data-cy="vd-activity-pie"]');
        },
        get hoursPieChart() {
            return cy.get('[data-cy="vd-hours-pie"]');
        },
        get teamTracker() {
            return cy.get('[data-cy="vd-team-tracker"]');
        },
        get bcSubDivisonGraph() {
            return cy.get('[data-cy="vd-bc-subdivision-graph"]');
        },
        get objectionsGraph() {
            return cy.get('[data-cy="vd-objections-graph"]');
        },
        get ratingTiles() {
            return cy.get('[data-cy=vd-rating] .cards [data-cy=vd-data-card]');
        },
        get ratingAnalysisTiles() {
            return cy.get('[data-cy=vd-consultancy] .cards [data-cy=vd-analysis-card]');
        },
        get consultancyTiles() {
            return cy.get('[data-cy=vd-consultancy] .cards [data-cy=vd-data-card]');
        },
        get consultancyAnalysisTiles() {
            return cy.get('[data-cy=vd-consultancy] .cards [data-cy=vd-analysis-card]');
        },
        get progressBars() {
            return cy.get('[data-cy=vd-progress-bar]');
        }
    },

    visit() {
        cy.visit('/dashboard/valuer-metrics');
    },

    clickOutsideDatePicker() {
        cy.get('body').click(0, 0);
        cy.wait(10000);
    },

    checkNotificationMessage(message) {
        cy.get('[data-cy="my-dashboard-notification-list"]').find('span').invoke('text')
            .then((text) => {
                expect(text).to.include(message);
            });
    },

    checkCustomRangesAreAtDefaultValues() {
        const date = new Date();
        const today = `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
        const startOfMonth = `1/${date.getMonth() + 1}/${date.getFullYear()}`;

        this.elements.customDateFromFilter.find('.mx-input').invoke('val').should('equal', startOfMonth);
        this.elements.customDateToFilter.find('.mx-input').invoke('val').should('equal', today);
    },
};

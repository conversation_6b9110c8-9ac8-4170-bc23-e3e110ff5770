export default {
    getMultiSelectAdvSearchFilterComponent(inputKey) {
        const selector = inputKey === 'buildingAge' ? `:nth-child(6) > :nth-child(4) > .${inputKey}` : `.${inputKey}`;
        return cy.get(`${selector} > .fieldTwo > .btn-group > .multiselect`);
    },
    getMultiSelectAdvSearchFilterContainerComponent(inputKey) {
        return cy.get(`.${inputKey} > .fieldTwo > .btn-group > .multiselect-container`);
    },
    get searchTypeLabel() {
        return cy.get('.searchType-wrapper > :nth-child(2) > label');
    },
    get advSearchIcon() {
        return cy.get('.advSearch-icon');
    },
};

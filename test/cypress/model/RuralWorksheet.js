const RuralWorksheet = {
	get ruralWorksheetHeader() {
		return cy.get('h1[data-cy=ruralWorksheetHeader]');
	},
	get ruralWorksheetTab1() {
		return cy.get('span[data-cy=ruralWorksheetTab1]');
	},
	get ruralWorksheetTab2() {
		return cy.get('span[data-cy=ruralWorksheetTab2]');
	},
	get ruralWorksheetTab3() {
		return cy.get('span[data-cy=ruralWorksheetTab3]');
	},
	get ruralWorksheetCreateRevisionButton() {
		return cy.get('span[data-cy=ruralWorksheetCreateRevisionButton]');
	},
	get ruralWorksheetTab3() {
		return cy.get('span[data-cy=ruralWorksheetTab3]');
	},
	get ruralWorksheetWorksheetArea() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetArea]');
	},
	get ruralWorksheetProduction() {
		return cy.get('input[data-cy=ruralWorksheetProduction]');
	},
	get ruralWorksheetImprovementsHeader() {
		return cy.get('h3[data-cy=ruralWorksheetImprovementsHeader]');
	},
	get ruralWorksheetImprovementEditDescriptions() {
		return cy.get('input[data-cy=ruralWorksheetImprovementEditDescription]');
	},
	get ruralWorksheetImprovementEditTypes() {
		return cy.get('input[data-cy=ruralWorksheetImprovementEditType]');
	},
	get ruralWorksheetImprovementEditSizes() {
		return cy.get('input[data-cy=ruralWorksheetImprovementEditSize]');
	},
	get ruralWorksheetImprovementEditRates() {
		return cy.get('input[data-cy=ruralWorksheetImprovementEditRate]');
	},
	get ruralWorksheetImprovementEditValues() {
		return cy.get('input[data-cy=ruralWorksheetImprovementEditValue]');
	},
	get ruralWorksheetImprovementRemoveButtons() {
		return cy.get('i[data-cy=ruralWorksheetImprovementRemoveButton]');
	},
	get ruralWorksheetImprovementAddDescription() {
		return cy.get('input[data-cy=ruralWorksheetImprovementAddDescription]');
	},
	get ruralWorksheetImprovementAddType() {
		return cy.get('select[data-cy=ruralWorksheetImprovementAddType]');
	},
	get ruralWorksheetImprovementAddSize() {
		return cy.get('input[data-cy=ruralWorksheetImprovementAddSize]');
	},
	get ruralWorksheetImprovementAddRate() {
		return cy.get('input[data-cy=ruralWorksheetImprovementAddRate]');
	},
	get ruralWorksheetImprovementAddButton() {
		return cy.get('i[data-cy=ruralWorksheetImprovementAddButton]');
	},
	get ruralWorksheetImprovementTotalVI() {
		return cy.get('input[data-cy=ruralWorksheetImprovementTotalVI]');
	},
	get ruralWorksheetLandHeader() {
		return cy.get('h3[data-cy=ruralWorksheetLandHeader]');
	},
	get ruralWorksheetHasIrrigationIndicator() {
		return cy.get('span[data-cy=ruralWorksheetHasIrrigationIndicator]');
	},
	get ruralWorksheetLandUseTypeEditDescriptions() {
		return cy.get('input[data-cy=ruralWorksheetLandUseTypeEditDescription]');
	},
	get ruralWorksheetLandUseTypeEditTypes() {
		return cy.get('select[data-cy=ruralWorksheetLandUseTypeEditType]');
	},
	get ruralWorksheetLandUseTypeEditContours() {
		return cy.get('select[data-cy=ruralWorksheetLandUseTypeEditContour]');
	},
	get ruralWorksheetLandUseTypeEditContours() {
		return cy.get('select[data-cy=ruralWorksheetLandUseTypeEditIrrigation]');
	},
	get ruralWorksheetLandUseTypeEditSizes() {
		return cy.get('input[data-cy=ruralWorksheetLandUseTypeEditSize]');
	},
	get ruralWorksheetLandUseTypeEditRates() {
		return cy.get('input[data-cy=ruralWorksheetLandUseTypeEditRate]');
	},
	get ruralWorksheetLandUseTypeEditValues() {
		return cy.get('input[data-cy=ruralWorksheetLandUseTypeEditValue]');
	},
	get ruralWorksheetLandUseTypeRemoveButtons() {
		return cy.get('i[data-cy=ruralWorksheetLandUseTypeRemoveButton]');
	},
	get ruralWorksheetLandUseTypeAddDescription() {
		return cy.get('input[data-cy=ruralWorksheetLandUseTypeAddDescription]');
	},
	get ruralWorksheetLandUseTypeAddType() {
		return cy.get('select[data-cy=ruralWorksheetLandUseTypeAddType]');
	},
	get ruralWorksheetLandUseTypeAddContour() {
		return cy.get('select[data-cy=ruralWorksheetLandUseTypeAddContour]');
	},
	get ruralWorksheetLandUseTypeAddIrrigation() {
		return cy.get('select[data-cy=ruralWorksheetLandUseTypeAddIrrigation]');
	},
	get ruralWorksheetLandUseTypeAddSize() {
		return cy.get('input[data-cy=ruralWorksheetLandUseTypeAddSize]');
	},
	get ruralWorksheetLandUseTypeAddRate() {
		return cy.get('input[data-cy=ruralWorksheetLandUseTypeAddRate]');
	},
	get ruralWorksheetLandUseTypeAddButton() {
		return cy.get('i[data-cy=ruralWorksheetLandUseTypeAddButton]');
	},
	get ruralWorksheetSiteEditViews() {
		return cy.get('select[data-cy=ruralWorksheetSiteEditView]');
	},
	get ruralWorksheetSiteEditAreas() {
		return cy.get('input[data-cy=ruralWorksheetSiteEditArea]');
	},
	get ruralWorksheetSiteEditValues() {
		return cy.get('input[data-cy=ruralWorksheetSiteEditValue]');
	},
	get ruralWorksheetSiteRemoveButtons() {
		return cy.get('i[data-cy=ruralWorksheetSiteRemoveButton]');
	},
	get ruralWorksheetSiteAddView() {
		return cy.get('select[data-cy=ruralWorksheetSiteAddView]');
	},
	get ruralWorksheetSiteAddArea() {
		return cy.get('input[data-cy=ruralWorksheetSiteAddArea]');
	},
	get ruralWorksheetSiteAddValue() {
		return cy.get('input[data-cy=ruralWorksheetSiteAddValue]');
	},
	get ruralWorksheetSiteAddButton() {
		return cy.get('i[data-cy=ruralWorksheetSiteAddButton]');
	},
	get ruralWorksheetLandTotalArea() {
		return cy.get('input[data-cy=ruralWorksheetLandTotalArea]');
	},
	get ruralWorksheetLandTotalLV() {
		return cy.get('input[data-cy=ruralWorksheetLandTotalLV]');
	},
	get ruralWorksheetMaoriLandHeader() {
		return cy.get('h3[data-cy=ruralWorksheetMaoriLandHeader]');
	},
	get ruralWorksheetMaoriLandMultipleOwnerAdjustment() {
		return cy.get('input[data-cy=ruralWorksheetMaoriLandMultipleOwnerAdjustment]');
	},
	get ruralWorksheetMaoriLandSiteSignificanceAdjustment() {
		return cy.get('input[data-cy=ruralWorksheetMaoriLandSiteSignificanceAdjustment]');
	},
	get ruralWorksheetMaoriLandMaoriOwnerCount() {
		return cy.get('input[data-cy=ruralWorksheetMaoriLandMaoriOwnerCount]');
	},
	get ruralWorksheetWorksheetTotalsHeader() {
		return cy.get('h3[data-cy=ruralWorksheetWorksheetTotalsHeader]');
	},
	get ruralWorksheetWorksheetValuesTotalCV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesTotalCV]');
	},
	get ruralWorksheetWorksheetValuesTotalLV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesTotalLV]');
	},
	get ruralWorksheetWorksheetValuesTotalVI() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesTotalVI]');
	},
	get ruralWorksheetWorksheetValuesTV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesTV]');
	},
	get ruralWorksheetWorksheetValuesRoundedCV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesRoundedCV]');
	},
	get ruralWorksheetWorksheetValuesRoundedLV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesRoundedLV]');
	},
	get ruralWorksheetWorksheetValuesRoundedVI() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesRoundedVI]');
	},
	get ruralWorksheetWorksheetValuesRoundedTV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesRoundedTV]');
	},
	get ruralWorksheetWorksheetValuesAdjustment() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesAdjustment]');
	},
	get ruralWorksheetWorksheetValuesAdjustedCV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesAdjustedCV]');
	},
	get ruralWorksheetWorksheetValuesAdjustedLV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesAdjustedLV]');
	},
	get ruralWorksheetWorksheetValuesAdjustedVI() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesAdjustedVI]');
	},
	get ruralWorksheetWorksheetValuesAdjustedRoundedCV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesAdjustedRoundedCV]');
	},
	get ruralWorksheetWorksheetValuesAdjustedRoundedLV() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesAdjustedRoundedLV]');
	},
	get ruralWorksheetWorksheetValuesAdjustedRoundedVI() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetValuesAdjustedRoundedVI]');
	},
	get ruralWorksheetWorksheetRatiosCvHa() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetRatiosCvHa]');
	},
	get ruralWorksheetWorksheetRatiosLvHa() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetRatiosLvHa]');
	},
	get ruralWorksheetWorksheetTotalsLandArea() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetTotalsLandArea]');
	},
	get ruralWorksheetWorksheetTotalsCvProduction() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetTotalsCvProduction]');
	},
	get ruralWorksheetWorksheetTotalsLvProduction() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetTotalsLvProduction]');
	},
	get ruralWorksheetWorksheetTotalsProduction() {
		return cy.get('input[data-cy=ruralWorksheetWorksheetTotalsProduction]');
	},
	get ruralWorksheetRevisionValuesHeader() {
		return cy.get('input[data-cy=ruralWorksheetRevisionValuesHeader]');
	},
	get ruralWorksheetRevisionTotalAssessedValueUnadjustedCV() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueUnadjustedCV]');
	},
	get ruralWorksheetRevisionTotalAssessedValueUnadjustedLV() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueUnadjustedLV]');
	},
	get ruralWorksheetRevisionTotalAssessedValueUnadjustedVI() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueUnadjustedVI]');
	},
	get ruralWorksheetRevisionTotalAssessedValueUnadjustedTV() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueUnadjustedTV]');
	},
	get ruralWorksheetRevisionTotalAssessedValueAdjustedCV() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueAdjustedCV]');
	},
	get ruralWorksheetRevisionTotalAssessedValueAdjustedLV() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueAdjustedLV]');
	},
	get ruralWorksheetRevisionTotalAssessedValueAdjustedVI() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueAdjustedVI]');
	},
	get ruralWorksheetRevisionTotalAssessedValueHeader() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueHeader]');
	},
	get ruralWorksheetRevisionTotalAssessedValueCV() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueCV]');
	},
	get ruralWorksheetRevisionTotalAssessedValueLV() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueLV]');
	},
	get ruralWorksheetRevisionTotalAssessedValueVI() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueVI]');
	},
	get ruralWorksheetRevisionTotalAssessedValueTV() {
		return cy.get('input[data-cy=ruralWorksheetRevisionTotalAssessedValueTV]');
	},
	get ruralWorksheetComment() {
		return cy.get('textarea[data-cy=ruralWorksheetComment]');
	},
	get ruralWorksheetCurrentValuesHeader() {
		return cy.get('h3[data-cy=ruralWorksheetCurrentValuesHeader]');
	},
	get ruralWorksheetReasonForChangeHeader() {
		return cy.get('h3[data-cy=ruralWorksheetReasonForChangeHeader]');
	},
	get ruralWorksheetReasonForChangeOutput() {
		return cy.get('select[data-cy=ruralWorksheetReasonForChangeOutput]');
	},
	get ruralWorksheetReasonForChangeSource() {
		return cy.get('select[data-cy=ruralWorksheetReasonForChangeSource]');
	},
	get ruralWorksheetReasonForChangeReason() {
		return cy.get('input[data-cy=ruralWorksheetReasonForChangeReason]');
	},
	get ruralWorksheetDeleteButton() {
		return cy.get('input[data-cy=ruralWorksheetDeleteButton]');
	},
	get ruralWorksheetUpdateAssessmentButton() {
		return cy.get('input[data-cy=ruralWorksheetUpdateAssessmentButton]');
	},
	get ruralWorksheetCancelButton() {
		return cy.get('input[data-cy=ruralWorksheetCancelButton]');
	},
	get ruralWorksheetUpdateWorksheetButton() {
		return cy.get('input[data-cy=ruralWorksheetUpdateWorksheetButton]');
	},
	get ruralWorksheetUpdateRevisionButton() {
		return cy.get('input[data-cy=ruralWorksheetUpdateRevisionButton]');
	},
	get ruralWorksheetModalHeading() {
		return cy.get('h1[data-cy=ruralWorksheetModalHeading]');
	},
	get ruralWorksheetModalMessage() {
		return cy.get('p[data-cy=ruralWorksheetModalMessage]');
	},
	get ruralWorksheetModalListBox() {
		return cy.get('div[data-cy=ruralWorksheetModalListBox]');
	},
	get ruralWorksheetModalListItems() {
		return cy.get('li[data-cy=ruralWorksheetModalListItem]');
	},
	get ruralWorksheetModalResponseCode() {
		return cy.get('button[data-cy=ruralWorksheetModalResponseCode]');
	},
	get ruralWorksheetModalCancelButton() {
		return cy.get('button[data-cy=ruralWorksheetModalCancelButton]');
	},
	get ruralWorksheetModalContinueButton() {
		return cy.get('button[data-cy=ruralWorksheetModalContinueButton]');
	},
}

function verifyRevisionWorksheetElements(isMaoriLand, isExternalUser) {
	// check fields present
	RuralWorksheet.ruralWorksheetHeader.should('exist');
	RuralWorksheet.ruralWorksheetTab1.should('exist');
	RuralWorksheet.ruralWorksheetTab2.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetArea.should('exist');
	RuralWorksheet.ruralWorksheetProduction.should('exist');
	RuralWorksheet.ruralWorksheetImprovementsHeader.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddDescription.should('not.exist');
	RuralWorksheet.ruralWorksheetImprovementAddType.should('not.exist');
	RuralWorksheet.ruralWorksheetImprovementAddSize.should('not.exist');
	RuralWorksheet.ruralWorksheetImprovementAddRate.should('not.exist');
	RuralWorksheet.ruralWorksheetImprovementAddButton.should('not.exist');
	RuralWorksheet.ruralWorksheetImprovementTotalVI.should('exist');
	RuralWorksheet.ruralWorksheetLandHeader.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.should('not.exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddType.should('not.exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddContour.should('not.exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddIrrigation.should('not.exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddSize.should('not.exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddRate.should('not.exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddButton.should('not.exist');
	RuralWorksheet.ruralWorksheetSiteAddView.should('not.exist');
	RuralWorksheet.ruralWorksheetSiteAddArea.should('not.exist');
	RuralWorksheet.ruralWorksheetSiteAddValue.should('not.exist');
	RuralWorksheet.ruralWorksheetSiteAddButton.should('not.exist');
	RuralWorksheet.ruralWorksheetLandTotalArea.should('exist');
	RuralWorksheet.ruralWorksheetLandTotalLV.should('exist');
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandHeader.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedCV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedLV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.should('exist');
	}
	else{
		RuralWorksheet.ruralWorksheetMaoriLandHeader.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('not.exist');
	}
	RuralWorksheet.ruralWorksheetWorksheetTotalsHeader.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalCV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalLV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalVI.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.should('exist');
	RuralWorksheet.ruralWorksheetComment.should('exist');
	RuralWorksheet.ruralWorksheetCurrentValuesHeader.should('exist');
	RuralWorksheet.ruralWorksheetReasonForChangeHeader.should('not.exist');
	RuralWorksheet.ruralWorksheetReasonForChangeOutput.should('not.exist');
	RuralWorksheet.ruralWorksheetReasonForChangeSource.should('not.exist');
	RuralWorksheet.ruralWorksheetReasonForChangeReason.should('not.exist');
	RuralWorksheet.ruralWorksheetCancelButton.should('exist');
	RuralWorksheet.ruralWorksheetDeleteButton.should('not.exist');

	if (isExternalUser) {
		RuralWorksheet.ruralWorksheetUpdateAssessmentButton.should('not.exist');
		RuralWorksheet.ruralWorksheetUpdateWorksheetButton.should('not.exist');
		RuralWorksheet.ruralWorksheetUpdateRevisionButton.should('not.exist');
	}
	else {
		RuralWorksheet.ruralWorksheetUpdateAssessmentButton.should('not.exist');
		RuralWorksheet.ruralWorksheetUpdateWorksheetButton.should('not.exist');
		RuralWorksheet.ruralWorksheetUpdateRevisionButton.should('exist');
	}

	// check field type/contents
	RuralWorksheet.ruralWorksheetHeader.contains('Update Rural Worksheet');
	RuralWorksheet.ruralWorksheetWorksheetArea.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetProduction.invoke('attr', 'type').should('equal','number');
	if (RuralWorksheet.ruralWorksheetImprovementEditDescriptions && RuralWorksheet.ruralWorksheetImprovementEditDescriptions.length > 0) {
		for (let i = 0; i < RuralWorksheet.ruralWorksheetImprovementEditDescriptions.length; i++) {
			RuralWorksheet.ruralWorksheetImprovementEditDescriptions[i].invoke('attr', 'type').should('equal','text');
			RuralWorksheet.ruralWorksheetImprovementEditRates[i].invoke('attr', 'type').should('equal','text');
			RuralWorksheet.ruralWorksheetImprovementEditSizes[i].invoke('attr', 'type').should('equal','text');
		}
	}
	if (RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions && RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions.length > 0) {
		for (let i = 0; i < RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions.length; i++) {
			RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions[i].invoke('attr', 'type').should('equal','text');
			RuralWorksheet.ruralWorksheetLandUseTypeEditRates[i].invoke('attr', 'type').should('equal','text');
			RuralWorksheet.ruralWorksheetLandUseTypeEditSizes[i].invoke('attr', 'type').should('equal','text');
		}
	}
	if (RuralWorksheet.ruralWorksheetSiteEditAreas && RuralWorksheet.ruralWorksheetSiteEditAreas.length > 0) {
		for (let i = 0; i < RuralWorksheet.ruralWorksheetSiteEditAreas.length; i++) {
			RuralWorksheet.ruralWorksheetSiteEditAreas[i].invoke('attr', 'type').should('equal','text');
			RuralWorksheet.ruralWorksheetSiteEditValues[i].invoke('attr', 'type').should('equal','text');
		}
	}
	RuralWorksheet.ruralWorksheetImprovementTotalVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetLandTotalArea.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetLandTotalLV.invoke('attr', 'type').should('equal','text');
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedCV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedLV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.invoke('attr', 'type').should('equal','text');
	}
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalCV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalLV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetCancelButton.invoke('attr', 'type').should('equal','button');

	if (!isExternalUser) {
		RuralWorksheet.ruralWorksheetUpdateRevisionButton.invoke('attr', 'type').should('equal', 'button');
	}

	// check editable state
	RuralWorksheet.ruralWorksheetImprovementTotalVI.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetLandTotalArea.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetLandTotalLV.should('have.attr', 'disabled', 'disabled');

	if (RuralWorksheet.ruralWorksheetImprovementEditDescriptions && RuralWorksheet.ruralWorksheetImprovementEditDescriptions.length > 0) {
		for (let i = 0; i < RuralWorksheet.ruralWorksheetImprovementEditDescriptions.length; i++) {
			RuralWorksheet.ruralWorksheetImprovementEditDescriptions[i].should('have.attr', 'disabled', 'disabled');
			//RuralWorksheet.ruralWorksheetImprovementEditRates[i].should('have.attr', 'disabled', 'disabled');
			RuralWorksheet.ruralWorksheetImprovementEditSizes[i].should('have.attr', 'disabled', 'disabled');
		}
	}
	if (RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions && RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions.length > 0) {
		for (let i = 0; i < RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions.length; i++) {
			RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions[i].should('have.attr', 'disabled', 'disabled');
			//RuralWorksheet.ruralWorksheetLandUseTypeEditRates[i].should('have.attr', 'disabled', 'disabled');
			RuralWorksheet.ruralWorksheetLandUseTypeEditSizes[i].should('have.attr', 'disabled', 'disabled');
		}
	}
	if (RuralWorksheet.ruralWorksheetSiteEditAreas && RuralWorksheet.ruralWorksheetSiteEditAreas.length > 0) {
		for (let i = 0; i < RuralWorksheet.ruralWorksheetSiteEditAreas.length; i++) {
			RuralWorksheet.ruralWorksheetSiteEditAreas[i].should('have.attr', 'disabled', 'disabled');
			//RuralWorksheet.ruralWorksheetSiteEditValues[i].should('have.attr', 'disabled', 'disabled');
		}
	}
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.should('have.attr', 'disabled', 'disabled');
	}
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.should('have.attr', 'disabled', 'disabled');
}

function verifyCurrentWorksheetElements(isMaoriLand, isExternalUser) {

	// check fields present
	RuralWorksheet.ruralWorksheetHeader.should('exist');
	RuralWorksheet.ruralWorksheetTab1.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetArea.should('exist');
	RuralWorksheet.ruralWorksheetProduction.should('exist');
	RuralWorksheet.ruralWorksheetImprovementsHeader.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddDescription.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddType.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddSize.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddRate.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddButton.should('exist');
	RuralWorksheet.ruralWorksheetImprovementTotalVI.should('exist');
	RuralWorksheet.ruralWorksheetLandHeader.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddType.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddContour.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddIrrigation.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddSize.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddRate.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddButton.should('exist');
	RuralWorksheet.ruralWorksheetSiteAddView.should('exist');
	RuralWorksheet.ruralWorksheetSiteAddArea.should('exist');
	RuralWorksheet.ruralWorksheetSiteAddValue.should('exist');
	RuralWorksheet.ruralWorksheetSiteAddButton.should('exist');
	RuralWorksheet.ruralWorksheetLandTotalArea.should('exist');
	RuralWorksheet.ruralWorksheetLandTotalLV.should('exist');
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandHeader.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedCV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedLV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.should('exist');
	}
	else{
		RuralWorksheet.ruralWorksheetMaoriLandHeader.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('not.exist');
	}
	RuralWorksheet.ruralWorksheetWorksheetTotalsHeader.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalCV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalLV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalVI.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.should('exist');
	RuralWorksheet.ruralWorksheetComment.should('exist');
	RuralWorksheet.ruralWorksheetCurrentValuesHeader.should('exist');

    if (!isExternalUser) {
        RuralWorksheet.ruralWorksheetReasonForChangeHeader.should('exist');
        RuralWorksheet.ruralWorksheetReasonForChangeOutput.should('exist');
        RuralWorksheet.ruralWorksheetReasonForChangeSource.should('exist');
        RuralWorksheet.ruralWorksheetReasonForChangeReason.should('exist');
    }

    RuralWorksheet.ruralWorksheetCancelButton.should('exist');

	if (isExternalUser) {
		RuralWorksheet.ruralWorksheetDeleteButton.should('not.exist');
		RuralWorksheet.ruralWorksheetUpdateAssessmentButton.should('not.exist');
		RuralWorksheet.ruralWorksheetUpdateWorksheetButton.should('not.exist');
	}
	else {
		RuralWorksheet.ruralWorksheetDeleteButton.should('exist');
		RuralWorksheet.ruralWorksheetUpdateAssessmentButton.should('exist');
		RuralWorksheet.ruralWorksheetUpdateWorksheetButton.should('exist');
	}

	// check field type/contents
	RuralWorksheet.ruralWorksheetHeader.contains('Update Rural Worksheet');
	RuralWorksheet.ruralWorksheetWorksheetArea.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetProduction.invoke('attr', 'type').should('equal','number');
	if (RuralWorksheet.ruralWorksheetImprovementEditDescriptions.length > 0) {
		for (let i = 0; i < RuralWorksheet.ruralWorksheetImprovementEditDescriptions.length; i++) {
			RuralWorksheet.ruralWorksheetImprovementEditDescriptions[i].invoke('attr', 'type').should('equal','text');
			RuralWorksheet.ruralWorksheetImprovementEditRates[i].invoke('attr', 'type').should('equal','text');
			RuralWorksheet.ruralWorksheetImprovementEditSizes[i].invoke('attr', 'type').should('equal','text');
		}
	}
	RuralWorksheet.ruralWorksheetImprovementAddDescription.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetImprovementAddSize.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetImprovementAddRate.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetImprovementTotalVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetLandUseTypeAddSize.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetLandUseTypeAddRate.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetSiteAddArea.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetSiteAddValue.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetLandTotalArea.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetLandTotalLV.invoke('attr', 'type').should('equal','text');
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedCV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedLV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.invoke('attr', 'type').should('equal','text');
	}
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalCV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalLV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetCancelButton.invoke('attr', 'type').should('equal','button');

	if (!isExternalUser) {
        RuralWorksheet.ruralWorksheetReasonForChangeReason.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetUpdateAssessmentButton.invoke('attr', 'type').should('equal', 'button');
		RuralWorksheet.ruralWorksheetUpdateWorksheetButton.invoke('attr', 'type').should('equal', 'button');
	}

	// check editable state
	RuralWorksheet.ruralWorksheetImprovementTotalVI.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetLandTotalArea.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetLandTotalLV.should('have.attr', 'disabled', 'disabled');
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.should('have.attr', 'disabled', 'disabled');
	}
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.should('have.attr', 'disabled', 'disabled');
}


function verifyCreateWorksheetElements(isMaoriLand, isExternalUser) {

	// check fields present
	RuralWorksheet.ruralWorksheetHeader.should('exist');
	RuralWorksheet.ruralWorksheetTab1.should('exist');
	RuralWorksheet.ruralWorksheetTab2.should('not.exist');
	RuralWorksheet.ruralWorksheetWorksheetArea.should('exist');
	RuralWorksheet.ruralWorksheetProduction.should('exist');
	RuralWorksheet.ruralWorksheetImprovementsHeader.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddDescription.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddType.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddSize.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddRate.should('exist');
	RuralWorksheet.ruralWorksheetImprovementAddButton.should('exist');
	RuralWorksheet.ruralWorksheetImprovementTotalVI.should('exist');
	RuralWorksheet.ruralWorksheetLandHeader.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddType.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddContour.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddIrrigation.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddSize.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddRate.should('exist');
	RuralWorksheet.ruralWorksheetLandUseTypeAddButton.should('exist');
	RuralWorksheet.ruralWorksheetSiteAddView.should('exist');
	RuralWorksheet.ruralWorksheetSiteAddArea.should('exist');
	RuralWorksheet.ruralWorksheetSiteAddValue.should('exist');
	RuralWorksheet.ruralWorksheetSiteAddButton.should('exist');
	RuralWorksheet.ruralWorksheetLandTotalArea.should('exist');
	RuralWorksheet.ruralWorksheetLandTotalLV.should('exist');
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandHeader.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedCV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedLV.should('exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.should('exist');
	}
	else{
		RuralWorksheet.ruralWorksheetMaoriLandHeader.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('not.exist');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('not.exist');
	}
	RuralWorksheet.ruralWorksheetWorksheetTotalsHeader.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalCV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalLV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalVI.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesTV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.should('exist');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.should('exist');
	RuralWorksheet.ruralWorksheetComment.should('exist');
	RuralWorksheet.ruralWorksheetCurrentValuesHeader.should('exist');
	RuralWorksheet.ruralWorksheetReasonForChangeHeader.should('exist');
	RuralWorksheet.ruralWorksheetReasonForChangeOutput.should('exist');
	RuralWorksheet.ruralWorksheetReasonForChangeSource.should('exist');
	RuralWorksheet.ruralWorksheetReasonForChangeReason.should('exist');
	RuralWorksheet.ruralWorksheetCancelButton.should('exist');
	RuralWorksheet.ruralWorksheetDeleteButton.should('not.exist');

	if (isExternalUser) {
		RuralWorksheet.ruralWorksheetUpdateAssessmentButton.should('not.exist');
		RuralWorksheet.ruralWorksheetUpdateWorksheetButton.should('not.exist');
	}
	else {
		RuralWorksheet.ruralWorksheetUpdateAssessmentButton.should('exist');
		RuralWorksheet.ruralWorksheetUpdateWorksheetButton.should('exist');
	}

	// check field type/contents
	RuralWorksheet.ruralWorksheetHeader.contains('Create Rural Worksheet');
	RuralWorksheet.ruralWorksheetWorksheetArea.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetProduction.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetImprovementEditDescriptions.should('have.length', 0);
	RuralWorksheet.ruralWorksheetImprovementAddDescription.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetImprovementAddSize.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetImprovementAddRate.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetImprovementTotalVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetLandUseTypeEditDescriptions.should('have.length', 0);
	RuralWorksheet.ruralWorksheetLandUseTypeAddDescription.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetLandUseTypeAddSize.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetLandUseTypeAddRate.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetSiteEditAreas.should('have.length', 0);
	RuralWorksheet.ruralWorksheetSiteAddArea.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetSiteAddValue.invoke('attr', 'type').should('equal','number');
	RuralWorksheet.ruralWorksheetLandTotalArea.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetLandTotalLV.invoke('attr', 'type').should('equal','text');
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedCV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedLV.invoke('attr', 'type').should('equal','text');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.invoke('attr', 'type').should('equal','text');
	}
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalCV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalLV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTotalVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesTV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetReasonForChangeReason.invoke('attr', 'type').should('equal','text');
	RuralWorksheet.ruralWorksheetCancelButton.invoke('attr', 'type').should('equal','button');

	if (!isExternalUser) {
		RuralWorksheet.ruralWorksheetUpdateAssessmentButton.invoke('attr', 'type').should('equal', 'button');
		RuralWorksheet.ruralWorksheetUpdateWorksheetButton.invoke('attr', 'type').should('equal', 'button');
	}

	// check editable state
	RuralWorksheet.ruralWorksheetImprovementTotalVI.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetLandTotalArea.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetLandTotalLV.should('have.attr', 'disabled', 'disabled');
	if (isMaoriLand) {
		RuralWorksheet.ruralWorksheetMaoriLandMultipleOwnerAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetMaoriLandSiteSignificanceAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetMaoriLandMaoriOwnerCount.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesRoundedCV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesRoundedLV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustment.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedCV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedLV.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedVI.should('have.attr', 'disabled', 'disabled');
		RuralWorksheet.ruralWorksheetWorksheetValuesAdjustedRoundedVI.should('have.attr', 'disabled', 'disabled');
	}
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedVI.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetValuesRoundedTV.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetRatiosCvHa.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetRatiosLvHa.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLandArea.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsCvProduction.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsLvProduction.should('have.attr', 'disabled', 'disabled');
	RuralWorksheet.ruralWorksheetWorksheetTotalsProduction.should('have.attr', 'disabled', 'disabled');
}

function loadCurrentWorksheet(qpid) {
    cy.visit(`roll-maintenance/rural-worksheet/${qpid}`);
    dismissValuesChangedSinceLastSaveModal();
}

function loadRevisionWorksheet(qpid) {
    cy.visit(`roll-maintenance/rural-revision-worksheet/${qpid}`);
    dismissValuesChangedSinceLastSaveModal();
}

function loadRtvWorksheet(qpid) {
    cy.visit(`roll-maintenance/rural-rtv-worksheet/${qpid}`);
    dismissValuesChangedSinceLastSaveModal();
}

function verifyAreaWarningModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.should('not.exist');
    RuralWorksheet.ruralWorksheetModalListBox.should('exist');
    RuralWorksheet.ruralWorksheetModalListBox.should('have.class', 'message-warning');
    RuralWorksheet.ruralWorksheetModalListItems.should(items => {
        expect(items[0]).to.contain.text('Total Land Area does not match');
    });
    RuralWorksheet.ruralWorksheetModalCancelButton.should('exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.contains('No, Return to worksheet');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('Yes, Update');
}

function verifyDeleteModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.should('exist');
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalMessage.contains('for this assessment will be deleted. Are you sure?');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.contains('No, Return to worksheet');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('Yes, Delete Worksheet');
}

function verifyAcceptNewValuesConfirmation() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.contains('New Assessment values will be');
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.contains('No, Return to worksheet');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('Accept new values');
}

function verifyBalanceChangedWarningModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.contains('Worksheet land value is different from land value on assessment.');
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.contains('No, Return to worksheet');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('Yes, Update');
}

function verifyProductionTreesUpdateErrorModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalHeading.contains('Oops');
    RuralWorksheet.ruralWorksheetModalMessage.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.contains('can only be modified from this worksheet when the \'Update Assessment\' button is clicked.');
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('not.exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('Return to worksheet');
}

function verifyValidationErrorModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalHeading.contains('Oops');
    RuralWorksheet.ruralWorksheetModalMessage.should('not.exist');
    RuralWorksheet.ruralWorksheetModalListBox.should('exist');
    RuralWorksheet.ruralWorksheetModalListBox.should('have.class', 'message-error');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('not.exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('Return to worksheet');
}

function verifyRevisionWorksheetMustBeUpdatedModal() {
    RuralWorksheet.ruralWorksheetModalHeading.then($el => {
        expect($el).to.exist;
    });
    RuralWorksheet.ruralWorksheetModalMessage.then($el => {
        RuralWorksheet.ruralWorksheetModalMessage.invoke('text').then($text => {
            expect($text.trim()).to.contain('Revision worksheet must be updated')
        });
    });
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('not.exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.then($el => {
        expect($el).to.exist;
    });
}

function verifySuccessModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalHeading.contains('Success');
    RuralWorksheet.ruralWorksheetModalMessage.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.contains('was successfully updated');
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('not.exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
}

function verifyCannotHaveWorksheetModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.should('not.exist');
    RuralWorksheet.ruralWorksheetModalListBox.should('exist');
    RuralWorksheet.ruralWorksheetModalListItems.should(items => {
        expect(items[0]).to.contain.text('Unable to create rural worksheet for this property. The property has an invalid category or apportionment code.');
    });
    RuralWorksheet.ruralWorksheetModalCancelButton.should('not.exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('OK');
}

function verifyCancelChangesModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.contains('You will lose all your current changes');
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.contains('No, return to worksheet');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('Yes, discard my changes');
}

function verifyCopyLandMatrixWarningModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.contains('Land rows will be deleted and be replaced by Land Matrix rows.');
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.contains('No, Return to worksheet');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('OK');
}

function verifyRecalculateLandMatrixWarningModal() {
    RuralWorksheet.ruralWorksheetModalHeading.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.should('exist');
    RuralWorksheet.ruralWorksheetModalMessage.contains('You will lose any changes you have made in the Land Matrix section.');
    RuralWorksheet.ruralWorksheetModalListBox.should('not.exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.should('exist');
    RuralWorksheet.ruralWorksheetModalCancelButton.contains('No, Return to worksheet');
    RuralWorksheet.ruralWorksheetModalContinueButton.should('exist');
    RuralWorksheet.ruralWorksheetModalContinueButton.contains('OK');
}


function dismissValuesChangedSinceLastSaveModal() {
    dismissModal('ValuesChangedWarning', true);
}

function dismissAcceptNewValuesConfirmationModal() {
    dismissModal('NewAssessmentValuesConfirmation', false);
}

function dismissBalanceChangedWarningModal() {
    dismissModal('BalanceChangedWarning', false);
}

function dismissAreaMismatchWarningModal() {
    // skip the area warning if it pops up
    dismissModal('AreaMismatchWarning', false);
}

function dismissRevisionWorksheetMustBeUpdatedModal() {
    // skip the area warning if it pops up
    dismissModal('RevisionWorksheetUpdateRequired', false);
}

function dismissModal(modalCode, cancel) {
    cy.get('body').then($body => {
            if ($body.find('.alert > #modalResponseCode').length) {
                return '.alert > #modalResponseCode';
            }
            return null;
        })
        .then($selector => {
            if ($selector) {
                cy.get($selector).then($alert => {
                    if ($alert.val() === modalCode) {
                        if (cancel) {
                            RuralWorksheet.ruralWorksheetModalCancelButton.click();
                        } else {
                            RuralWorksheet.ruralWorksheetModalContinueButton.click();
                        }
                    }
                });
            }
        });
}

export {
    RuralWorksheet as default,
    verifyRevisionWorksheetElements,
    verifyCurrentWorksheetElements,
    verifyCreateWorksheetElements,
    loadCurrentWorksheet,
    loadRevisionWorksheet,
    loadRtvWorksheet,
    verifyAreaWarningModal,
    verifyDeleteModal,
    verifyAcceptNewValuesConfirmation,
    verifyBalanceChangedWarningModal,
    verifyProductionTreesUpdateErrorModal,
    verifyValidationErrorModal,
    verifyRevisionWorksheetMustBeUpdatedModal,
    verifySuccessModal,
    verifyCannotHaveWorksheetModal,
    verifyCancelChangesModal,
    verifyCopyLandMatrixWarningModal,
    verifyRecalculateLandMatrixWarningModal,
    dismissValuesChangedSinceLastSaveModal,
    dismissAcceptNewValuesConfirmationModal,
    dismissBalanceChangedWarningModal,
    dismissAreaMismatchWarningModal,
    dismissRevisionWorksheetMustBeUpdatedModal,
    dismissModal,
};

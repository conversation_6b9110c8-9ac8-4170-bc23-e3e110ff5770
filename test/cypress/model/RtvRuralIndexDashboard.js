export default {
    get ruralIndexDashboard() {
        return cy.get('div[data-cy=rtvRuralIndex]');
    },
    get loadingSpinner() {
        'div[data-cy=rtvRuralIndexLoadingSpinner]';
    },
    get pageMask() {
        'div[data-cy=rtvRuralIndexPageMask]';
    },
    get navBar() {
        return this.ruralIndexDashboard.then(dashboard => {
            const navBars = dashboard.find('div[data-cy=rtvRuralIndexNavbar]');
            if (navBars && navBars.length > 0) {
                return navBars[0];
            }
            return null;
        });
    },
    get taSelector() {
        return 'select[data-cy=rtvRuralIndexTaSelector]';
    },
    get taSelectorItems() {
        return cy.get(this.taSelector).then(selector => {
            return selector.find('option');
        });
    },
    get mainIndexLink() {
        return 'li[data-cy=rtvRuralIndexMainIndexLink]';
    },
    get secondaryRefinementsLink() {
        return 'li[data-cy=rtvRuralIndexSecondaryRefinementsLink]';
    },
    get selectTaMessage() {
        return this.ruralIndexDashboard.then(dashboard => {
            const messages = dashboard.find('div[data-cy=rtvRuralIndexSelectTaMessage]');
            if (messages && messages.length > 0) {
                return messages[0];
            }
            return null;
        });
    },
    get errorMessage() { 
        return 'div[data-cy=rtvRuralIndexErrorMessage]';
    },
    get mainIndexScreen() {
        return this.ruralIndexDashboard.then(dashboard => {
            const indexScreens = dashboard.find('div[data-cy=rtvRuralIndexMainIndex]');
            if (indexScreens && indexScreens.length > 0) {
                return indexScreens[0];
            }
            return null;
        });
    },
    get secondaryRefinementsScreen() {
        return 'div[data-cy=rtvRuralIndexSecondaryRefinements]';
    },
    get modal() {
        return 'div[data-cy=rtvRuralIndexModal]'
    },
    get modalHeading() {
        return 'h1[data-cy=rtvRuralIndexModalHeading]'
    },
    get modalMessage() {
        return 'p[data-cy=rtvRuralIndexModalMessage]'
    },
    get modalMessageList() {
        return 'ul[data-cy=rtvRuralIndexModalMessageList]'
    },
    get modalCancelButton() {
        return 'button[data-cy=rtvRuralIndexModalCancelButton]'
    },
    get modalConfirmButton() {
        return 'button[data-cy=rtvRuralIndexModalConfirmButton]'
    },
    get modalResponseCode() {
        return 'input[data-cy=rtvRuralIndexModalResponseCode]'
    },
};

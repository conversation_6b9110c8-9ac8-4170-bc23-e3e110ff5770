export default {
    elements: {
        validationHeaders: {
            get errors() {
                return cy.get('[data-cy="validation-header-errors"]');
            },
            get warnings() {
                return cy.get('[data-cy="validation-headers-warnings"]');
            }
        },
        get title() {
            return cy.get('[data-cy="building-consent-title"]');
        },
        get exceptionMessage() {
            return cy.get('[data-cy="exception-message"]');
        },
        get masterDetailsPropertyToolbarRollMaintenanceTabLink() {
            return cy.get('[data-cy="master-details-property-toolbar-roll-maintenance-tab-link"]');
        },
        get activityListConsentNumber() {
            return cy.get('[data-cy="activity-list-consent-number"]');
        },
        get propertySummaryHeader() {
            return cy.get('[data-cy="property-summary-header"]');
        },
        get consentNumberInput() {
            return cy.get('[data-cy="consent-number-input"]');
        },
        get territorialAuthorityInput() {
            return cy.get('[data-cy="territorial-authority-input"]');
        },
        get costInput() {
            return cy.get('[data-cy="cost-input"]');
        },
        get floorAreaInput() {
            return cy.get('[data-cy="floor-area-input"]');
        },
        get estimatedInspectionReadyDateInput() {
            return cy.get('[data-cy="estimated-inspection-ready-date-input"]');
        },
        get streetNumberInput() {
            return cy.get('[data-cy="street-number-input"]');
        },
        get streetNameInput() {
            return cy.get('[data-cy="street-name-input"]');
        },
        get consentDateInput() {
            return cy.get('[data-cy="consent-date-input"]');
        },
        get enteredDateInput() {
            return cy.get('[data-cy="entered-date-input"]');
        },
        get valuerInput() {
            return cy.get('[data-cy="valuer-input"]');
        },
        get applicantInput() {
            return cy.get('[data-cy="applicant-input"]');
        },
        get constructionCompletionDate() {
            return cy.get('[data-cy="construction-completion-date"]');
        },
        get consentActionedInput() {
            return cy.get('[data-cy="consent-actioned-input"]');
        },
        get consentDescriptionInput() {
            return cy.get('[data-cy="consent-description-input"]');
        },
        get inspectionStateDropdown() {
            return cy.get('[data-cy="inspection-state-dropdown"]');
        },
        get natureOfWorksDropdown() {
            return cy.get('[data-cy="nature-of-works-dropdown"]');
        },
        get notesInput() {
            return cy.get('[data-cy="notes-input"]');
        },
        validationMessages: {
            get estimatedInspectionReadyDate() {
                return cy.get('[data-cy="estimated-inspection-ready-date-validation-message"]');
            },
        },
        get statusPanelHeader() {
            return cy.get('[data-cy="status-panel-header"]');
        },
        get planStatusDropdown() {
            return cy.get('[data-cy="plan-status-dropdown"] > .multiselect__tags');
        },
        statusList: {
            get ul() {
                return cy.get('[data-cy="status-list-ul"]')
            },
            get warningLabels() {
                return this.ul.find('.danger');
            },
            items: {
                get needsInspection() {
                    return cy.get('[data-cy="status-list-item-needs-inspection"]');
                },
                get needsMoreInformation() {
                    return cy.get('[data-cy="status-list-item-needs-more-information"]');
                },
                get plansUnknown() {
                    return cy.get('[data-cy="status-list-item-plans-unknown"]');
                },
                get plansNeeded() {
                    return cy.get('[data-cy="status-list-item-plans-needed"]');
                },
                get plansAvailable() {
                    return cy.get('[data-cy="status-list-item-plans-available"]');
                },
                get plansRequestedWithTA() {
                    return cy.get('[data-cy="status-list-item-plans-requested-with-ta"]');
                },
                get setupComplete() {
                    return cy.get('[data-cy="status-list-item-setup-complete"]');
                },
                get constructionInProgress() {
                    return cy.get('[data-cy="status-list-item-construction-in-progress"]');
                },
                get constructionComplete() {
                    return cy.get('[data-cy="status-list-item-construction-complete"]');
                }
            },
            labels: {
                get needsInspection() {
                    return cy.get('[data-cy="status-list-label-needs-inspection"]');
                },
                get needsMoreInformation() {
                    return cy.get('[data-cy="status-list-label-needs-more-information"]');
                },
                get plansUnknown() {
                    return cy.get('[data-cy="status-list-label-plans-unknown"]');
                },
                get plansNeeded() {
                    return cy.get('[data-cy="status-list-label-plans-needed"]');
                },
                get plansAvailable() {
                    return cy.get('[data-cy="status-list-label-plans-available"]');
                },
                get plansRequestedWithTA() {
                    return cy.get('[data-cy="status-list-label-plans-requested-with-ta"]');
                },
                get setupComplete() {
                    return cy.get('[data-cy="status-list-label-setup-complete"]');
                },
                get constructionInProgress() {
                    return cy.get('[data-cy="status-list-label-construction-in-progress"]');
                },
                get constructionComplete() {
                    return cy.get('[data-cy="status-list-label-construction-complete"]');
                }
            }
        },
        statusCheckboxes: {
            get needsMoreInformation() {
                return cy.get('[data-cy="needs-more-information-checkbox"]')
            },
            get needsInspection() {
                return cy.get('[data-cy="needs-inspection-checkbox"]')
            },
            get constructionComplete() {
                return cy.get('[data-cy="construction-complete-checkbox"]')
            },
            get complianceCertificateIssued() {
                return cy.get('[data-cy="compliance-certificate-issued-checkbox"]')
            },
        },
        buttons: {
            get setupCompleteButton() {
                return cy.get('[data-cy="setup-complete-button"]')
            },
            get cancelButton() {
                return cy.get('[data-cy="cancel-button"]')
            },
            get saveButton() {
                return cy.get('[data-cy="save-button"]')
            },
            get saveAndCloseButton() {
                return cy.get('[data-cy="save-and-close-button"]')
            },
        }
    },

    getBCDetailsPageHref(id) {
        return `roll-maintenance/BC-${id}`;
    },

    enterPlanStatus(input) {
        this.elements.planStatusDropdown.type(`${input}{enter}`);
        const parsedInput = input.replace(/\*/g, '');
        this.elements.statusList.ul.find('li').eq(0)
            .invoke('text')
            .should('equal', parsedInput != '' ? parsedInput : 'Plans Unknown');
    },

    checkConstructionCompletionDate() {
        this.elements.statusCheckboxes.constructionComplete
            .invoke('val')
            .should('equal', 'on');
        this.elements.statusList.labels.constructionComplete.should('exist').and(
            'have.text',
            'Construction Complete'
        );
        this.elements.constructionCompletionDate
            .invoke('val')
            .should('equal', new Date().toLocaleDateString('en-gb'));
    },
};

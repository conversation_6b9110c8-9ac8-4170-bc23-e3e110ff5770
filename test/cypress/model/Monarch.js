/*
    A place to store functions repeatedly used while testing Monarch-web
 */

import Home from "./Home";

export default {

    waitForPropertyInfo() {
        cy.intercept(`${env.BASE_URL}/getPropertyInfo/*`).as('propertyInfo');
        cy.wait('@propertyInfo');
    },

    clearErrorBoxes() {
        let badQPIDErrorSelector = '#errorCancel';
        let unExpectedErrorSelector = '.alertWrapper.modal.warning';
        cy.get('body', { timeout: 10000 }) // we need the page to have rendered
            .wait(1000) // we need the page to have completed it's important calls
            .then($body => {
                // check for a Missing QPID error
                if ($body.find(badQPIDErrorSelector).length) {
                    //find and click the button
                    $body.find(badQPIDErrorSelector).click();
                }
                // check for an Unexpected Error
                if ($body.find(unExpectedErrorSelector).length > 0) {
                    //find and click the button
                    $body.find(unExpectedErrorSelector).find('.mdl-button--primary').click();
                }
            });
    },


    sanityCheck() {
        describe('Check login', () => {
            it('Successfully reaches the home page', () => {
                cy.visitWithLogin(Home.href)
                    .url()
                    .then((item) => {
                        expect(item).to.equal(env.BASE_URL);
                    });
            });
        });
    }
};

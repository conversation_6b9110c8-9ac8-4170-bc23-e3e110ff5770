import '@testing-library/cypress/add-commands'
export default {
    elements: {
        get objectionActionButton() {
            return cy.get('[data-cy="objection-list-action"]')
                     .first()
                     .find('button')
                     .first();
        },
        get objectionSearchTable() {
            return cy.get('[data-cy="objection-search-table"]');
        },
    }
};

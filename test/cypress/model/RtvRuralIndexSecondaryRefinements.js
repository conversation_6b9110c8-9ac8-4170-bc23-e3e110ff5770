export default {
    get secondaryRefinementsWindow() { 
        return 'div[data-cy=rtvRuralIndexSecondaryRefinements]' 
    },
    get header() {
        return 'thead[data-cy=secondaryRefinementsHeader]'
    },
    get headerCell1() {
        return 'th[data-cy=secondaryRefinementsHeaderCell1]'
    },
    get headerCell2() {
        return 'th[data-cy=secondaryRefinementsHeaderCell2]'
    },
    get headerCell3() {
        return 'th[data-cy=secondaryRefinementsHeaderCell3]'
    },
    get headerCell4() {
        return 'th[data-cy=secondaryRefinementsHeaderCell4]'
    },
    get headerCell5() {
        return 'th[data-cy=secondaryRefinementsHeaderCell5]'
    },
    get headerCell6() {
        return 'th[data-cy=secondaryRefinementsHeaderCell6]'
    },
    get headerCell7() {
        return 'th[data-cy=secondaryRefinementsHeaderCell7]'
    },
    get headerCell8() {
        return 'th[data-cy=secondaryRefinementsHeaderCell8]'
    },
    get headerCell9() {
        return 'th[data-cy=secondaryRefinementsHeaderCell9]'
    },
    get headerCell10() {
        return 'th[data-cy=secondaryRefinementsHeaderCell10]'
    },
    get headerCell11() {
        return 'th[data-cy=secondaryRefinementsHeaderCell11]'
    },
    get headerCell12() {
        return 'th[data-cy=secondaryRefinementsHeaderCell12]'
    },
    get headerCell13() {
        return 'th[data-cy=secondaryRefinementsHeaderCell13]'
    },
    get headerCell14() {
        return 'th[data-cy=secondaryRefinementsHeaderCell14]'
    },
    get headerCell15() {
        return 'th[data-cy=secondaryRefinementsHeaderCell15]'
    },
    get headerCell16() {
        return 'th[data-cy=secondaryRefinementsHeaderCell16]'
    },
    get headerCell17() {
        return 'th[data-cy=secondaryRefinementsHeaderCell17]'
    },
    get headerCell18() {
        return 'th[data-cy=secondaryRefinementsHeaderCell18]'
    },
    get headerCell19() {
        return 'th[data-cy=secondaryRefinementsHeaderCell19]'
    },
    get headerCell20() {
        return 'th[data-cy=secondaryRefinementsHeaderCell20]'
    },
    get headerCell21() {
        return 'th[data-cy=secondaryRefinementsHeaderCell21]'
    },
    get headerCell22() {
        return 'th[data-cy=secondaryRefinementsHeaderCell22]'
    },
    get newRefinementRow() {
        return 'tr[data-cy=secondaryRefinementsNewRow]'
    },
    get newRefinementCategory() {
        return 'select[data-cy=secondaryRefinementsNewRefinementCategory]'
    },
    get newRefinementNoCategoryPlaceholder() {
        return 'input[data-cy=secondaryRefinementsNewNoCategoryPlaceholder]'
    },
    get newRefinementSalesGroup() {
        return 'select[data-cy=secondaryRefinementsNewSalesGroup]'
    },
    get newRefinementRollNumber() {
        return 'select[data-cy=secondaryRefinementsNewRollNumber]'
    },
    get newRefinementAssessmentMin() {
        return 'input[data-cy=secondaryRefinementsNewAssessmentMin]'
    },
    get newRefinementAssessmentMax() {
        return 'input[data-cy=secondaryRefinementsNewAssessmentMax]'
    },
    get newRefinementCvMin() {
        return 'input[data-cy=secondaryRefinementsNewCvMin]'
    },
    get newRefinementCvMax() {
        return 'input[data-cy=secondaryRefinementsNewCvMax]'
    },
    get newRefinementLvMin() {
        return 'input[data-cy=secondaryRefinementsNewLvMin]'
    },
    get newRefinementLvMax() {
        return 'input[data-cy=secondaryRefinementsNewLvMax]'
    },
    get newRefinementQvCategory() {
        return 'input[data-cy=secondaryRefinementsNewQvCategory]'
    },
    get newRefinementGrouping() {
        return 'div.multiselect[data-cy=secondaryRefinementsNewGrouping]'
    },
    get newRefinementQualityRating() {
        return 'div.multiselect[data-cy=secondaryRefinementsNewQualityRating]'
    },
    get newRefinementLvPercentIndex() {
        return 'input[data-cy=secondaryRefinementsNewLvPercentIndex]'
    },
    get newRefinementLvLumpSumIndex() {
        return 'input[data-cy=secondaryRefinementsNewLvLumpSumIndex]'
    },
    get newRefinementViPercentIndex() {
        return 'input[data-cy=secondaryRefinementsNewViPercentIndex]'
    },
    get newRefinementViLumpSumIndex() {
        return 'input[data-cy=secondaryRefinementsNewViLumpSumIndex]'
    },
    get newRefinementAddButton() {
        return 'td[data-cy=secondaryRefinementsAddRefinementButton]'
    },
    get refinementRows() {
        return 'tr[data-cy=secondaryRefinementsRow]'
    },
    get refinementCategory() {
        return 'select[data-cy=secondaryRefinementsRefinementCategory]'
    },
    get refinementNoCategoryPlaceholder() {
        return 'input[data-cy=secondaryRefinementsNoCategoryPlaceholder]'
    },
    get refinementSalesGroup() {
        return 'select[data-cy=secondaryRefinementsSalesGroup]'
    },
    get refinementRollNumber() {
        return 'select[data-cy=secondaryRefinementsRollNumber]'
    },
    get refinementAssessmentMin() {
        return 'input[data-cy=secondaryRefinementsAssessmentMin]'
    },
    get refinementAssessmentMax() {
        return 'input[data-cy=secondaryRefinementsAssessmentMax]'
    },
    get refinementCvMin() {
        return 'input[data-cy=secondaryRefinementsCvMin]'
    },
    get refinementCvMax() {
        return 'input[data-cy=secondaryRefinementsCvMax]'
    },
    get refinementLvMin() {
        return 'input[data-cy=secondaryRefinementsLvMin]'
    },
    get refinementLvMax() {
        return 'input[data-cy=secondaryRefinementsLvMax]'
    },
    get refinementQvCategory() {
        return 'input[data-cy=secondaryRefinementsQvCategory]'
    },
    get refinementGrouping() {
        return 'div.multiselect[data-cy=secondaryRefinementsGrouping]'
    },
    get refinementQualityRating() {
        return 'div.multiselect[data-cy=secondaryRefinementsQualityRating]'
    },
    get refinementLvPercentIndex() {
        return 'input[data-cy=secondaryRefinementsLvPercentIndex]'
    },
    get refinementLvLumpSumIndex() {
        return 'input[data-cy=secondaryRefinementsLvLumpSumIndex]'
    },
    get refinementViPercentIndex() {
        return 'input[data-cy=secondaryRefinementsViPercentIndex]'
    },
    get refinementViLumpSumIndex() {
        return 'input[data-cy=secondaryRefinementsViLumpSumIndex]'
    },
    get refinementRemoveButton() {
        return 'td[data-cy=secondaryRefinementsRemoveRefinementButton]'
    },

};

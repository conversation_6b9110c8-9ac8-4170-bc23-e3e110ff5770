import { get } from "lodash";

export default {
    elements: {
        get draftPropertyCategoryValueExist() {
            return cy.get('[data-cy="objection-job-category"] [role="combobox"]').children().eq(1);
        },
        get draftPropertyCategoryDropDown() {
            return cy.get('[data-cy="objection-job-category"] ul[role="listbox"]').children();
        },
        get draftPropertyNatureOfImprovementValueExist() {
            return cy.get('[data-cy="objection-job-nature-of-improvement"] [role="combobox"]').children().eq(1);
        },
        get draftPropertyNatureOfImprovementDropdown() {
            return cy.get('[data-cy="objection-job-nature-of-improvement"] ul[role="listbox"]').children();
        },
        get expandAll() {
            return cy.getBySel('expand-all');
        },
        get recentWork() {
            return cy.getBySel('recent-work');
        },
        get updatedPropertyDescription() {
            return cy.getBySel("updated-property-description");
        },
        get populateDvrDataButton() {
            return cy.getBySel('objection-job-populate-dvr-data');
        },
        get draftPropertyPropertyName() {
            return cy.getBySel('objection-job-property-name');
        },
        get draftPropertyLandUseValueExist() {
            return cy.get('[data-cy="objection-job-landuse"] [role="combobox"]').children().eq(1);
        },
        get draftPropertyLandUseDropDown() {
            return cy.get('[data-cy="objection-job-landuse"] ul[role="listbox"]').children();
        },
        get draftPropertyTALandZoneValueExist() {
            return cy.get('[data-cy="objection-job-ta-land-zone"] [role="combobox"]').children().eq(1);
        },
        get draftPropertyTALandZoneDropDown() {
            return cy.get('[data-cy="objection-job-ta-land-zone"] ul[role="listbox"]').children();
        },
        get draftPropertyEffectiveLandArea() {
            return cy.getBySel('objection-job-effective-land-area').find('input');
        },
        get draftPropertyLandArea() {
            return cy.getBySel('objection-job-land-area').find('input');
        },
        get draftPropertyMaoriLand() {
            return cy.getBySel('objection-job-maori-land').find('input');
        },
        get draftPropertyPlanId() {
            return cy.getBySel('objection-job-plan-id').find('input');
        },
        get draftPropertyProducation() {
            return cy.getBySel('objection-job-production').find('input');
        },
        get lotPositionValueExist() {
            return cy.get('[data-cy="objection-job-lotposition"] [role="combobox"]').children().eq(1);
        },
        get lotDropDownValues() {
            return cy.get('[data-cy="objection-job-lotposition"] ul[role="listbox"]').children();
        },
        get contourValueExist() {
            return cy.get('[data-cy="objection-job-contour"] [role="combobox"]').children().eq(1);
        },
        get contourDownValues() {
            return cy.get('[data-cy="objection-job-contour"] ul[role="listbox"]').children();
        },
        get viewCheckValueExist() {
            return cy.get('[data-cy="objection-job-view"] [role="combobox"]').children().eq(1);
        },
        get viewDropDownValues() {
            return cy.get('[data-cy="objection-job-view"] ul[role="listbox"]').children();
        },
        get viewScopeValueExist() {
            return cy.get('[data-cy="objection-job-view-scope"] [role="combobox"]').children().eq(1);
        },
        get viewScopeDropDown() {
            return cy.get('[data-cy="objection-job-view-scope"] ul[role="listbox"]').children();
        },
        get csiValueExist() {
            return cy.get('[data-cy="objection-job-class-of-surrounding-improvements"] [role="combobox"]').children().eq(1);
        },
        get csiDropDownValues() {
            return cy.get('[data-cy="objection-job-class-of-surrounding-improvements"] ul[role="listbox"]').children();
        },
        get outlierValueExist() {
            return cy.get('[data-cy="objection-job-outlier"] [role="combobox"]').children().eq(1);
        },
        get outlierDropDownValues() {
            return cy.get('[data-cy="objection-job-outlier"] ul[role="listbox"]').children();
        },
        get houseTypeValueExist() {
            return cy.get('[data-cy="objection-job-house-type"] [role="combobox"]').children().eq(1);
        },
        get houseTypeDropDown() {
            return cy.get('[data-cy="objection-job-house-type"] ul[role="listbox"]').children();
        },
        get unitOfUse() {
            return cy.getBySel('objection-job-units-of-use').find('input');
        },
        get ageValueExist() {
            return cy.get('[data-cy="objection-job-age"] [role="combobox"]').children().eq(1);
        },
        get ageDropDown() {
            return cy.get('[data-cy="objection-job-age"] ul[role="listbox"]').children();
        },
        get effectiveYearBuilt() {
            return cy.get('[data-cy="objection-job-effective-year-built"]').children().find('input');
        },
        get poorfdnValueExist() {
            return cy.get('[data-cy="objection-job-poor-foundations"] [role="combobox"]').children().eq(1);
        },
        get poorfdnDropDownValues() {
            return cy.get('[data-cy="objection-job-poor-foundations"] ul[role="listbox"]').children();
        },
        get totalBderms() {
            return cy.getBySel('objection-job-total-bedrooms').find('input');
        },
        get totalBathrms() {
            return cy.getBySel('objection-job-total-bathrooms').find('input');
        },
        get totaltoilets() {
            return cy.getBySel('objection-job-total-toilets').find('input');
        },
        get buildingSiteCover() {
            return cy.getBySel('objection-job-building-site-cover').find('input');
        },
        get totalFloorArea() {
            return cy.getBySel('objection-job-total-floor-area').find('input');
        },
        get mainLivingArea() {
            return cy.getBySel('objection-job-main-living-area').find('input');
        },
        get totalLivingArea() {
            return cy.getBySel('objection-job-total-living-area').find('input');
        },
        get laundryAndWorkshopValueExist() {
            return cy.get('[data-cy="objection-job-laundry-workshop"] [role="combobox"]').children().eq(1);
        },
        get laundryAndWorkshopDropDown() {
            return cy.get('[data-cy="objection-job-laundry-workshop"] ul[role="listbox"]').children();
        },
        get carAccessValueExist() {
            return cy.get('[data-cy="objection-job-car-access"] [role="combobox"]').children().eq(1);
        },
        get carAccessDropDown() {
            return cy.get('[data-cy="objection-job-car-access"] ul[role="listbox"]').children();
        },
        get driveWayValueExist() {
            return cy.get('[data-cy="objection-job-driveway"] [role="combobox"]').children().eq(1);
        },
        get driveWayDropDown() {
            return cy.get('[data-cy="objection-job-driveway"] ul[role="listbox"]').children();
        },
        get carParks() {
            return cy.getBySel('objection-job-carparks').find('input');
        },
        get saveButtonforRegenerateBuilding() {
            return cy.get('[data-cy="save-button"]');
        },
        get typesOfBuildingValueExist() {
            return cy.get('[data-cy="construction-information-type-of-building"] [role="combobox"]').children().eq(1);
        },
        get typesOfBuildingDropDown() {
            return cy.get('[data-cy="construction-information-type-of-building"] ul[role="listbox"]').children();
        },
        get floorArea() {
            return cy.getBySel('construction-information-floor-area').find('input');
        },
        get noOfStoreys() {
            return cy.getBySel('construction-information-number-of-storeys').find('input');
        },
        get yearBuilt() {
            return cy.getBySel('construction-information-year-built').find('input');
        },
        get description() {
            return cy.getBySel('construction-information-description').find('input');
        },
        get buildingLabel() {
            return cy.getBySel('construction-information-building-label').find('input');
        },
        get principalBuildingValueExist() {
            return cy.get('[data-cy="construction-information-principal-bldg"] [role="combobox"]').children().eq(1);
        },
        get principalBuildingDropDown() {
            return cy.get('[data-cy="construction-information-principal-bldg"] ul[role="listbox"]').children();
        },
        get wallConstructionValueExist() {
            return cy.get('[data-cy="construction-information-wall-construction"] [role="combobox"]').children().eq(1);
        },
        get wallConstructionDropDown() {
            return cy.get('[data-cy="construction-information-wall-construction"] ul[role="listbox"]').children();
        },
        get wallConditionValueExist() {
            return cy.get('[data-cy="construction-information-wall-condition"] [role="combobox"]').children().eq(1);
        },
        get wallConditionDropDown() {
            return cy.get('[data-cy="construction-information-wall-condition"] ul[role="listbox"]').children();
        },
        get roofConstruction() {
            return cy.getBySel('construction-information-roof-construction').find('input');
        },
        get roofConstructionValueExist() {
            return cy.get('[data-cy="construction-information-roof-construction"] [role="combobox"]').children().eq(1);
        },
        get roofConstructionDropDown() {
            return cy.get('[data-cy="construction-information-roof-construction"] ul[role="listbox"]').children();
        },
        get roofConditionValueExist() {
            return cy.get('[data-cy="construction-information-roof-condition"] [role="combobox"]').children().eq(1);
        },
        get roofConditionDropDown() {
            return cy.get('[data-cy="construction-information-roof-condition"] ul[role="listbox"]').children();
        },
        get floorConstructionValueExist() {
            return cy.get('[data-cy="construction-information-floor-construction"] [role="combobox"]').children().eq(1);
        },
        get floorConstructionDropDown() {
            return cy.get('[data-cy="construction-information-floor-construction"] ul[role="listbox"]').children();
        },
        get foundationValueExist() {
            return cy.get('[data-cy="construction-information-foundation"] [role="combobox"]').children().eq(1);
        },
        get foundationDropDown() {
            return cy.get('[data-cy="construction-information-foundation"] ul[role="listbox"]').children();
        },
        get wiringAgeValueExist() {
            return cy.get('[data-cy="construction-information-wiring-age"] [role="combobox"]').children().eq(1);
        },
        get wiringAgeDropDown() {
            return cy.get('[data-cy="construction-information-wiring-age"] ul[role="listbox"]').children();
        },
        get plumbingAgeValueExist() {
            return cy.get('[data-cy="construction-information-plumbing-age"] [role="combobox"]').children().eq(1);
        },
        get plumbingAgeDropDown() {
            return cy.get('[data-cy="construction-information-plumbing-age"] ul[role="listbox"]').children();
        },
        get insulationValueExist() {
            return cy.get('[data-cy="construction-information-insulation"] [role="combobox"]').children().eq(1);
        },
        get insulationDropDown() {
            return cy.get('[data-cy="construction-information-insulation"] ul[role="listbox"]').children();
        },
        get glazingValueExist() {
            return cy.get('[data-cy="construction-information-glazing"] [role="combobox"]').children().eq(1);
        },
        get glazingDropDown() {
            return cy.get('[data-cy="construction-information-glazing"] ul[role="listbox"]').children();
        },
        get otherFeaturesCiValueExist() {
            return cy.get('[data-cy="construction-information-other-features"] [role="combobox"]').children().eq(1);
        },
        get otherFeaturesCiDropDown() {
            return cy.get('[data-cy="construction-information-other-features"] ul[role="listbox"]').children();
        },
        get addBuildingRow() {
            return cy.getBySel('add-building-row').find('button');
        },
        get removeBuildingRow() {
            return cy.getBySel('remove-building-row').find('button');
        },
        get copyBuildingRow() {
            return cy.getBySel('copy-building-row').find('button');
        },
        get addSpaceRow() {
            return cy.getBySel('add-space').find('button');
        },
        get removeDwellingSpaceRow() {
            return cy.getBySel('remove-dwelling-space-row').find('button');
        },
        get copyDwellingSpaceRow() {
            return cy.getBySel('duplicate-dwelling-space-row').find('button');
        },
        get removeGarageSpaceRow() {
            return cy.getBySel('remove-garage-space-row').find('button');
        },
        get copyGarageSpaceRow() {
            return cy.getBySel('duplicate-garage-space-row').find('button');
        },
        get removeAnyotherSpaceRow() {
            return cy.getBySel('remove-anyother-space-row').find('button');
        },
        get copyAnyotherSpaceRow() {
            return cy.getBySel('duplicate-anyother-space-row').find('button');
        },
        get addImprovementRow() {
            return cy.getBySel('add-improvement-row').find('button');
        },
        get removeImprovementRow() {
            return cy.getBySel('remove-improvement-row').find('button');
        },
        get deleteObjectionJobButton() {
            return cy.getBySel('delete-objection-job');
        },
        get alertModelDeleteObjectionJob() {
            return cy.getBySel('alert-model-delete-objection-job');
        },
        get siteOtherImprovement() {
            return cy.getBySel('site-other-improvement').find('input');
        },
        get siteImprovementQualityValueExist() {
            return cy.get('[data-cy="site-quality"] [role="combobox"]').children().eq(1);
        },
        get siteImprovementQualityDropDown() {
            return cy.get('[data-cy="site-quality"] ul[role="listbox"]').children();
        },
        get saveAsDraftButton() {
            return cy.getBySel('save-as-draft');
        },
        get setupCompleteAndValueButton() {
            return cy.getBySel('setup-complete-and-value');
        },
        get alertButtonClose() {
            return cy.getBySel('alert-modal');
        },
        get objectionJobStepperComparableSales() {
            return cy.getBySel('step-comparable-sales');
        },
        get objectionJobStepperValuation() {
            return cy.getBySel('step-valuation');
        },
        get jobValuer() {
            return cy.getBySel('objection-job-valuer');
        },
        get jobValuerValueExist() {
            return cy.get('[data-cy="objection-job-valuer"] [role="combobox"]').children().eq(1);
        },
        get jobValuerDropDown() {
            return cy.get('[data-cy="objection-job-valuer"] ul[role="listbox"]').children();
        },
        get jobValuerDropDownType() {
            return cy.get('[data-cy="objection-job-valuer"] [role="combobox"]').children().eq(1).find('input')
        },
        get registeredJobValuerDropDownType() {
            return cy.get('[data-cy="objection-job-registered-valuer"] [role="combobox"]').children().eq(1).find('input')
        },
        get notesForRegisteredValuer(){
            return cy.getBySel('notes-for-registered-valuer').find('textarea');
        },
        get riskType(){
            return cy.get('[data-cy="risk-type"] [role="combobox"]').children().eq(1).find('input');
        },
        get registeredValuer() {
            return cy.getBySel('objection-job-registered-valuer');
        },
        get objectionValuationSendToReview(){
            return cy.getBySel('objection-valuation-send-to-review').find('button');
        },
        get objectionSaveAsDraft() {
            return cy.getBySel('objection-save-draft-button');
        },
        get objectionRequestReviewButton() {
            return cy.getBySel('objection-request-review-button');
        },
        get objectionCompleteValuationButton() {
            return cy.getBySel('objection-complete-valuation-button');
        },
        get registeredValuerValueExist() {
            return cy.get('[data-cy="objection-job-registered-valuer"] [role="combobox"]').children().eq(1);
        },
        get registeredValuerDropDown() {
            return cy.get('[data-cy="objection-job-registered-valuer"] ul[role="listbox"]').children();
        },
        get comparableSalesAllSaleComment() {
            return cy.get('.qv-comparable-sales-list [data-cy="comparable-sales"] [data-cy="sale-comment-textarea"]');
        },
        comparableSalesSearchOptions: {
            get categories() {
                return cy.get('[data-cy="cv-categories"] > label > input')
            },
            get distance() {
                return cy.get('[data-cy="cv-distance"] > label > input')
            },
            get searchButton() {
                return cy.get('[data-cy="comparables-search"]');
            }
        },
        comparableSalesResults : {
            get firstResult() {
                return cy.get(':nth-child(4) > .col-container > .qv-comparable-sales-list > .table > :nth-child(2) > :nth-child(1) > .action-record-hide');
            }
        },
        get comparableSalesAllComparabilityDropDown() {
            return cy.get('.qv-comparable-sales-list [data-cy="comparable-sales"] [data-cy="comparability"]').find('select');
        },
        get objectionStepperJobCompletion() {
            return cy.getBySel('step-job-completion');
        },
        get effectiveLandAreaInput() {
            return cy.get('[data-cy="valuation-property-details-effective-land-area-input"]');
        },
        get errorsPopUp() {
            return cy.get('[data-cy="errors-pop-up"]');
        },
        get cancelButton() {
            return cy.get('[data-cy="button-dialog-cancel"]');
        },
        get warningsPopUp() {
            return cy.get('[data-cy="warnings-pop-up"]');
        },
        get confirmButton() {
            return cy.get('[data-cy="button-dialog-confirm"]');
        }
    },
    dropDownBox(valueExist, value, dropdown) {
        return valueExist.then(($el) => {
            const text = $el.text().trim();
            let output;
            if (!text || text === " " || text === "Select Valuer" || text === "Unassigned" || text === "Select Registered Valuer") {
                dropdown.contains(value).click({ force: true });
            }
            return output;
        }).then((updatedElement) => {
            const output = updatedElement.text().trim();
            return output;
        });
    },
    createObjectionJobPageURL(uuid) {
        return `/roll-maintenance/rating-valuation/${uuid}/objection/compare`;
    },
    createObjectionJobDraftPropertyURL(uuid) {
        return `/roll-maintenance/rating-valuation/${uuid}/objection/draft`;
    },
    valuationWorksheetObjectionJobPageURL(uuid) {
        return `/roll-maintenance/rating-valuation/${uuid}/objection/valuation`;
    },
}

export default {
    get nspFrom() {
        return cy.get('#from-netSalePrice');
    },

    get nspTo() {
        return cy.get('#to-netSalePrice');
    },

    get includeCategory1Sales() {
        return cy.get('#includedCategory1');
    },

    get excludeCategory1Sales() {
        return cy.get('#excludedCategory1');
    },

    get bedroomSalesRangeFrom() {
        return cy.get('#from-salesBedroomCount');
    },

    get bedroomSalesRangeTo() {
        return cy.get('#to-salesBedroomCount');
    },

    get landAreaFrom() {
        return cy.get('#from-salesLandArea');
    },

    get landAreaTo() {
        return cy.get('#to-salesLandArea');
    },

    get buildingAgeMultiselect() {
        return cy.get('[data-cy="sales-building-age"]');
    },

    buildingAge(type) {
        this.buildingAgeMultiselect.within(() => {
            cy.get('.caret').click();
            cy.contains(type).click();
            cy.get('.caret').click({ force: true });
        });
    },
};

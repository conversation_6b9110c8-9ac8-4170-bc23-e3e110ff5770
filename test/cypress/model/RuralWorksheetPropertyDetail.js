const PropertyDetail = {
	get ruralWorksheetQVCategory() {
		return cy.get('div[data-cy=ruralWorksheetQVCategory]');
	},
	get ruralWorksheetGrouping() {
		return cy.get('div[data-cy=ruralWorksheetGrouping]');
	},
	get ruralWorksheetQualityRating() {
		return cy.get('div[data-cy=ruralWorksheetQualityRating]');
	},
	get ruralWorksheetZone() {
		return cy.get('div[data-cy=ruralWorksheetLandZone]');
	},
	get ruralWorksheetNutrientScore() {
		return cy.get('div[data-cy=ruralWorksheetNutrientScore]');
	},
	get ruralWorksheetWaterQualityRating() {
		return cy.get('div[data-cy=ruralWorksheetWaterQualityRating]');
	},
}

function verifyPropertyDetailElements() {
	PropertyDetail.ruralWorksheetQVCategory.then($el => {
		expect($el).to.exist;
	});
	PropertyDetail.ruralWorksheetGrouping.then($el => {
		expect($el).to.exist;
	});
	PropertyDetail.ruralWorksheetQualityRating.then($el => {
		expect($el).to.exist;
	});
	PropertyDetail.ruralWorksheetZone.then($el => {
		expect($el).to.exist;
	});
	PropertyDetail.ruralWorksheetNutrientScore.then($el => {
		expect($el).to.exist;
	});
	PropertyDetail.ruralWorksheetWaterQualityRating.then($el => {
		expect($el).to.exist;
	});
}

export {
    PropertyDetail as default,
    verifyPropertyDetailElements,
};

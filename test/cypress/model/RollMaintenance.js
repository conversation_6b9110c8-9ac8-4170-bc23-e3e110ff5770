export default {
    href: 'roll-maintenance',
    searchUrl: '/web/rollMaintenance/searchActivities',
    resultsSelector: 'tr.resultsRow.activity-list__row',

    get header() {
        return cy.contains('.roll-maintenance', 'Roll Maintenance');
    },
    get consentNumber() {
        return cy.get('.activity-list--consentNumber');
    },
    get searchControlButtons() {
        return cy.get('.righty.search-control-buttons');
    },
    get buildingConsentHeader() {
        return cy.get('.qvToolbar-links').find('li.building-consents');
    },
    get buildingConsentTitle() {
        return cy.get('.resultsTitle').contains('h1', 'Building Consents');
    },
    get buildingConsentSearchButton() {
        this.searchControlButtons.contains('button', 'Search');
    },
    get buildingConsentClearButton() {
        this.searchControlButtons.contains('button', 'Clear');
    },
    get buildingConsentValRefSortColumn() {
        return cy.get('.activity-list--valRef > a');
    },
    get buildingConsentValRefSortIcon() {
        return cy.get('.activity-list--valRef > a > .icon > .sorter');
    },
    get linzSearchHeader() {
        return cy.get('.md-full').find('li.linz-search');
    },
    get linzSearchTitle() {
        return cy.get('.taLinzSearch-title').contains('span', 'LINZ Search');
    },
    get linzCertificateTitleHeader() {
        return cy.get('li.certificate-of-title');
    },
    get linzLegalDescriptionHeader() {
        return cy.get('li.legal-description');
    },
    get linzOwnerHeader() {
        return cy.get('li.owner');
    },
    get linzParcelIdHeader() {
        return cy.get('li.parcel-id');
    },
    get linzNotitleSearchCheckBox() {
        return cy.get('.title-status .linz-title-status-notitle');
    },
    get linzActiveTitleStatusInput() {
        return cy.get('input[data-cy=activeTitleStatusInput]');
    },
    get linzInactiveTitleStatusInput() {
        return cy.get('input[data-cy=inactiveTitleStatusInput]');
    },
    get linzNoTitleStatusInput() {
        return cy.get('input[data-cy=noTitleStatusInput]');
    },
    get linzSearchButton() {
        return cy.get('.linz-search-criteria .advlinzSearch');
    },
    get linzClearButton() {
        return cy.get('.linz-search-criteria .advSearchClear');
    },
    get linzErrorMessages() {
        return cy.get('.linz-search-criteria .linz-search-error-messages');
    },
    get linzLegalSearchSelected() {
        return cy.get('.linz-search-criteria .legalDescriptionSearch');
    },
    get legalDescriptionAutoSearch() {
        return cy.get('.linz-search-criteria .legalDescriptionAutoSearch');
    },
    get linzAutoLegalDescriptionInput() {
        return cy.get('.linz-search-criteria .autoLegalDescriptionInput');
    },
    get linzLandDistrictInput() {
        return cy.get('.linz-search-criteria .landDistrictInput');
    },
    get linzPartInput() {
        return cy.get('.linz-search-criteria .partInput');
    },
    get linzParcelTypeInput() {
        return cy.get('.linz-search-criteria .parcelTypeInput');
    },
    get linzParcelValueInput() {
        return cy.get('.linz-search-criteria .parcelValueInput');
    },
    get linzPlanTypeInput() {
        return cy.get('.linz-search-criteria .planTypeInput');
    },
    get linzPlanNumberInput() {
        return cy.get('.linz-search-criteria .planNumberInput');
    },
    get linzSecondParcelNumberInput() {
        return cy.get('.linz-search-criteria .secondParcelNumberInput');
    },
    get linzBlockInput() {
        return cy.get('.linz-search-criteria .blockInput');
    },
    get linzMaoriNameInput() {
        return cy.get('.linz-search-criteria .maoriNameInput');
    },
    get linzOtherAppInput() {
        return cy.get('.linz-search-criteria .otherAppInput');
    },
    get linzAppellationValueInput() {
        return cy.get('.linz-search-criteria .appellationValueInput');
    },
    get linzSuffixInput() {
        return cy.get('.linz-search-criteria .suffixInput');
    },
    get linzLastNameSearch() {
        return cy.get('.linz-search-criteria .lastNameSearch');
    },
    get linzCorporateSearch() {
        return cy.get('.linz-search-criteria .corporateSearch');
    },
    get linzLastNameInput() {
        return cy.get('.linz-search-criteria .lastNameInput');
    },
    get linzFirstNameInput() {
        return cy.get('.linz-search-criteria .firstNameInput');
    },
    get linzCorporateNameInput() {
        return cy.get('.linz-search-criteria .corporateNameInput');
    },
    get linzParcelIdInput() {
        return cy.get('.linz-search-criteria .parcelIdInput');
    },
    get linzTitleReferenceInput() {
        return cy.get('.linz-search-criteria .titleReferenceInput');
    },
    get linzTopPagination() {
        return cy.get('.paginator--top > .paginate-component');
    },
    get linzBottomPagination() {
        return cy.get('.search-result > .paginate-component');
    },
    get linzCertificateTitleReference() {
        return cy.get('.certificate-title-detail .certificate-title .title-reference');
    },
    get linzNoResult() {
        return cy.get('.no-results');
    },
    get linzNoTitleMsg() {
        return cy.get('.certificate-title .no-title-msg');
    }
};


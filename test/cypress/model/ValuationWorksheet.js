function WorksheetTableRow(table, index) {
    const getTable = () => cy.get(`[data-cy="${table}"]`);

    return {
        get row() {
            return getTable().find(`[data-cy="worksheet-table-row"]`).eq(index);
        },
        get buildingType() {
            return this.row.find('[data-cy="building-type"]');
        },
        get description() {
            return this.row.find('[data-cy="description"]');
        },
        get area() {
            return this.row.find('[data-cy="area"]');
        },
        get valuePerSquareMetre() {
            return this.row.find('[data-cy="value-per-square-metre"]');
        },
        get value() {
            return this.row.find('[data-cy="value"]');
        },
        get addRemoveButton() {
            const root = this.row;
            return {
                get component() {
                    return root.find('[data-cy="add-remove-button"]');
                },
                get add() {
                    return this.component.find('[data-cy="add"]');
                },
                get remove() {
                    return this.component.find('[data-cy="remove"]');
                }
            }
        }
    }
}

export function WorksheetTableMaoriAdoptedValues(root) {
    const unadjustedValues = () => root().find('[data-cy="unadjusted-values"]');
    const ratingValues = () => root().find('[data-cy="rating-values"]');

    return {
        UnadjustedValues: {
            get capitalValue() {
                return unadjustedValues().find('[data-cy="unadjusted-capital-value"]');
            },
            get landValue() {
                return unadjustedValues().find('[data-cy="unadjusted-land-value"]');
            },
            get valueOfImprovements() {
                return unadjustedValues().find('[data-cy="unadjusted-value-of-improvements"]');
            }
        },
        RatingValues: {
            get lumpSum() {
                return ratingValues().find('[data-cy="lump-sum"]');
            },
            get adjustment() {
                return ratingValues().find('[data-cy="adjustment"]');
            },
            get capitalValue() {
                return ratingValues().find('[data-cy="rating-capital-value"]');
            },
            get landValue() {
                return ratingValues().find('[data-cy="rating-land-value"]');
            },
            get valueOfImprovements() {
                return ratingValues().find('[data-cy="rating-value-of-improvements"]');
            }
        }
    }
}

export function WorksheetTableMaoriRevisionValues(root) {
    const unadjustedValues = () => root().find('[data-cy="unadjusted-values"]');
    const revisionValues = () => root().find('[data-cy="revision-values"]');

    return {
        UnadjustedValues: {
            get capitalValue() {
                return unadjustedValues().find('[data-cy="unadjusted-capital-value"]');
            },
            get landValue() {
                return unadjustedValues().find('[data-cy="unadjusted-land-value"]');
            },
            get valueOfImprovements() {
                return unadjustedValues().find('[data-cy="unadjusted-value-of-improvements"]');
            }
        },
        RevisionValues: {
            get lumpSum() {
                return revisionValues().find('[data-cy="lump-sum"]');
            },
            get adjustment() {
                return revisionValues().find('[data-cy="adjustment"]');
            },
            get capitalValue() {
                return revisionValues().find('[data-cy="revision-capital-value"]');
            },
            get landValue() {
                return revisionValues().find('[data-cy="revision-land-value"]');
            },
            get valueOfImprovements() {
                return revisionValues().find('[data-cy="revision-value-of-improvements"]');
            }
        }
    }
}

export function WorksheetTableMaoriAdoptedRevisionValues(root) {
    const unadjustedValues = () => root().find('[data-cy="adopted-unadjusted-values"]');
    const revisionValues = () => root().find('[data-cy="adopted-revision-values"]');

    return {
        UnadjustedValues: {
            get capitalValue() {
                return unadjustedValues().find('[data-cy="unadjusted-capital-value"]');
            },
            get landValue() {
                return unadjustedValues().find('[data-cy="unadjusted-land-value"]');
            },
            get valueOfImprovements() {
                return unadjustedValues().find('[data-cy="unadjusted-value-of-improvements"]');
            }
        },
        RevisionValues: {
            get lumpSum() {
                return revisionValues().find('[data-cy="lump-sum"]');
            },
            get adjustment() {
                return revisionValues().find('[data-cy="adjustment"]');
            },
            get capitalValue() {
                return revisionValues().find('[data-cy="revision-capital-value"]');
            },
            get landValue() {
                return revisionValues().find('[data-cy="revision-land-value"]');
            },
            get valueOfImprovements() {
                return revisionValues().find('[data-cy="revision-value-of-improvements"]');
            }
        }
    }
}

export function WorksheetTableMaoriAdoptedRevisionApportionmentValues(root) {
    const unadjustedRevisionValues = () => root().find('[data-cy="unadjusted-revision-values"]');
    const revisionValues = () => root().find('[data-cy="revision-values"]');

    return {
        UnadjustedRevisionValues: {
            capitalValue: (index = 0) => 
                unadjustedRevisionValues().find('[data-cy="unadjusted-revision-cv"]').eq(index),

            landValue: (index = 0) => 
                unadjustedRevisionValues().find('[data-cy="unadjusted-revision-lv"]').eq(index),

            valueOfImprovements: (index = 0) => 
                unadjustedRevisionValues().find('[data-cy="unadjusted-revision-vi"]').eq(index),
        },
        RevisionValues: {
            lumpSum: (index = 0) => 
                revisionValues().find('[data-cy="lump-sum"]').eq(index),

            adjustment: (index = 0) => 
                revisionValues().find('[data-cy="adjustment"]').eq(index),

            capitalValue: (index = 0) => 
                revisionValues().find('[data-cy="revision-cv"]').eq(index),

            landValue: (index = 0) => 
                revisionValues().find('[data-cy="revision-lv"]').eq(index),

            valueOfImprovements: (index = 0) => 
                revisionValues().find('[data-cy="revision-vi"]').eq(index),
        }
    }
}

export default {
    WorksheetTableMaoriLand: {
        get multipleOwnerAdjusted() {
            return cy.get('[data-cy="multiple-owners-adjusted"]');
        },
        get multipleOwnerRevisionAdjusted() {
            return cy.get('[data-cy="multiple-owners-revision-adjusted"]');
        },
        get numberOfOwners() {
            return cy.get('[data-cy="number-of-owners"]');
        },
        get siteSignificanceAdjusted() {
            return cy.get('[data-cy="site-significance-adjusted"]');
        },
        get siteSignificanceRevisionAdjustment() {
            return cy.get('[data-cy="site-significance-revision-adjusted"]');
        }
    },
    WorksheetTablePrimaryBuildings: {
        get table() {
            return cy.get('[data-cy="worksheet-table-primary-buildings"]');
        },
        get body() {
            return this.table.find('[data-cy="table-body"]');
        },
        get rows() {
            return this.body.find('[data-cy="worksheet-table-row"]');
        },
        row(index) {
            return WorksheetTableRow('worksheet-table-primary-buildings', index);
        }
    },
    WorksheetTableOtherBuildings: {
        get table() {
            return cy.get('[data-cy="worksheet-table-other-buildings"]');
        },
        get body() {
            return this.table.find('[data-cy="table-body"]');
        },
        get rows() {
            return this.body.find('[data-cy="worksheet-table-row"]');
        },
        row(index) {
            return WorksheetTableRow('worksheet-table-other-buildings', index);
        }
    },
    WorksheetTableOtherImprovements: {
        get table() {
            return cy.get('[data-cy="worksheet-table-other-improvements"]');
        },
        get body() {
            return this.table.find('[data-cy="table-body"]');
        },
        get rows() {
            return this.body.find('[data-cy="worksheet-table-row"]');
        },
        row(index) {
            return WorksheetTableRow('worksheet-table-other-improvements', index);
        }
    },
    WorksheetTableLand: {
        get table() {
            return cy.get('[data-cy="worksheet-table-land"]');
        },
        get body() {
            return this.table.find('[data-cy="table-body"]');
        },
        get rows() {
            return this.body.find('[data-cy="worksheet-table-row"]');
        },
        row(index) {
            return WorksheetTableRow('worksheet-table-land', index);
        }
    },
    WorksheetTableValues: {
        get capitalValue() {
            return cy.get('[data-cy="worksheet-capital-value"]');
        },
        get landValue() {
            return cy.get('[data-cy="worksheet-land-value"]');
        },
        get valueOfImprovements() {
            return cy.get('[data-cy="worksheet-value-of-improvements"]');
        }
    },
    WorksheetTableAdoptedValues: {
        get capitalValue() {
            return cy.get('[data-cy="adopted-capital-value"]');
        },
        get landValue() {
            return cy.get('[data-cy="adopted-land-value"]');
        },
        get valueOfImprovements() {
            return cy.get('[data-cy="adopted-value-of-improvements"]');
        }
    },
    WorksheetTableRevisionValues: {
        get capitalValue() {
            return cy.get('[data-cy="revision-capital-value"]');
        },
        get landValue() {
            return cy.get('[data-cy="revision-land-value"]');
        },
        get valueOfImprovements() {
            return cy.get('[data-cy="revision-value-of-improvements"]');
        }
    },
    WorksheetTableAdoptedRevisionValues: {
        get capitalValue() {
            return cy.get('[data-cy="adopted-revision-capital-value"]');
        },
        get landValue() {
            return cy.get('[data-cy="adopted-revision-land-value"]');
        },
        get valueOfImprovements() {
            return cy.get('[data-cy="adopted-revision-value-of-improvements"]');
        }
    },
    WorksheetTableMaoriAdoptedValues: WorksheetTableMaoriAdoptedValues(() => cy.get('[data-cy="worksheet-table-maori-adopted-values"]')),
    WorksheetTableMaoriRevisionValues: WorksheetTableMaoriRevisionValues(() => cy.get('[data-cy="worksheet-table-maori-revision-values"]')),
    WorksheetTableMaoriAdoptedRevisionValues: WorksheetTableMaoriAdoptedRevisionValues(() => cy.get('[data-cy="worksheet-table-maori-adopted-revision-values"]')),
    WorksheetTableMaoriAdoptedRevisionApportionmentValues: WorksheetTableMaoriAdoptedRevisionApportionmentValues(() => cy.get('[data-cy="worksheet-table-maori-adopted-revision-apportionment-values"]')),
    Action: {
        get saveButton() {
            return cy.get('[data-cy="valuation-save-as-draft-btn"]');
        },
        get alertButtonClose() {
            return cy.getBySel('alert-modal');
        },
        get warningsPopUp() {
            return cy.get('[data-cy="warnings-pop-up"]');
        },
        get errorsPopUp() {
            return cy.get('[data-cy="errors-pop-up"]');
        },
        get closeWarningsPopUpBtn() {
            return cy.get('[data-cy="button-dialog-cancel"]');
        }
    },
};


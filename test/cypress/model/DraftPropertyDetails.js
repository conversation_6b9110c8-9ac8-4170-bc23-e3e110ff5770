export default {
    GeneralPropertyInformation: {
        get category() {
            return cy.get('[data-cy="valuation-property-details-category"]');
        },
        get natureOfImprovements() {
            return cy.get('[data-cy="valuation-property-details-nature-of-improvements"]');
        },
        get propertyName() {
            return cy.get('[data-cy="valuation-property-details-property-name"]');
        },
        get landUse() {
            return cy.get('[data-cy="valuation-property-details-land-use"]');
        },
        get taLandZone() {
            return cy.get('[data-cy="valuation-property-details-ta-land-zone"]');
        },
        get effectiveLandArea() {
            return cy.get('[data-cy="valuation-property-details-effective-land-area"]');
        },
        get landArea() {
            return cy.get('[data-cy="valuation-property-details-land-area"]');
        },
        get maoriLand() {
            return cy.get('[data-cy="valuation-property-details-maori-land"]');
        },
        get planId() {
            return cy.get('[data-cy="valuation-property-details-plan-id"]');
        },
        get production() {
            return cy.get('[data-cy="valuation-property-details-production"]');
        },
    },
    LocationDetails: {
        get lotPosition() {
            return cy.get('[data-cy="valuation-property-details-lot-position"]');
        },
        get contour() {
            return cy.get('[data-cy="valuation-property-details-contour"]');
        },
        get view() {
            return cy.get('[data-cy="valuation-property-details-view"]');
        },
        get viewScope() {
            return cy.get('[data-cy="valuation-property-details-view-scope"]');
        },
        get classOfSurroundingImprovements() {
            return cy.get('[data-cy="valuation-property-details-class-of-surrounding-improvements"]');
        },
        get outlier () {
            return cy.get('[data-cy="valuation-property-details-outlier"]');
        }
    },
    PropertySummary: {
        get houseType() {
            return cy.get('[data-cy="valuation-property-details-house-type"]');
        },
        get unitsOfUse() {
            return cy.get('[data-cy="valuation-property-details-units-of-use"]');
        },
        get age() {
            return cy.get('[data-cy="valuation-property-details-age"]');
        },
        get effectiveYearBuilt() {
            return cy.get('[data-cy="valuation-property-details-effective-year-built"]');
        },
        get poorFoundations() {
            return cy.get('[data-cy="valuation-property-details-poor-foundations"]');
        },
        get totalBedRooms() {
            return cy.get('[data-cy="valuation-property-details-total-bedrms"]');
        },
        get totalBathRooms() {
            return cy.get('[data-cy="valuation-property-details-total-bathrms"]');
        },
        get totalToilets() {
            return cy.get('[data-cy="valuation-property-details-total-toilets"]');
        },
        get buildingSiteCover() {
            return cy.get('[data-cy="valuation-property-details-building-site-cover"]');
        },
        get totalFloorArea() {
            return cy.get('[data-cy="valuation-property-details-total-floor-area"]');
        },
        get mainLivingArea() {
            return cy.get('[data-cy="valuation-property-details-main-living-area"]');
        },
        get totalLivingArea() {
            return cy.get('[data-cy="valuation-property-details-total-living-area"]');
        },
        get laundaryWorkshop() {
            return cy.get('[data-cy="valuation-property-details-laundry-workshop"]');
        },
        get carAccess() {
            return cy.get('[data-cy="valuation-property-details-car-access"]');
        },
        get driveway() {
            return cy.get('[data-cy="valuation-property-details-driveway"]');
        },
        get carparks() {
            return cy.get('[data-cy="valuation-property-details-carparks"]');
        },
    },
    DerivedDvrFields: {
        get wallConstructionAndCondition() {
            return cy.get('[data-cy="valuation-property-details-dvr-wall-construction"]');
        },
        get roofConstructionAndCondition() {
            return cy.get('[data-cy="valuation-property-details-dvr-roof-construction"]');
        },
        get modernisation() {
            return cy.get('[data-cy="valuation-property-details-dvr-modernisation"]');
        },
        get landscaping() {
            return cy.get('[data-cy="valuation-property-details-dvr-landscaping-quality"]');
        },
        get deck() {
            return cy.get('[data-cy="valuation-property-details-dvr-deck"]');
        },
        get largeOIs() {
            return cy.get('[data-cy="valuation-property-details-dvr-large-other-improvements"]');
        },
        get umrGaraging() {
            return cy.get('[data-cy="valuation-property-details-dvr-under-main-roof-garages"]');
        },
        get fsGaraging() {
            return cy.get('[data-cy="valuation-property-details-dvr-freestanding-garages"]');
        },
        get saveButton() {
            return cy.get('[data-cy="valuation-property-details-dvr-save-button"]');
        },
    },
    BuildingsInformation: {
        get typeOfBuilding() {
            return cy.get('[data-cy="construction-information-type-of-building"]');
        },
        get floorArea() {
            return cy.get('[data-cy="construction-information-floor-area"]');
        },
        get noOfStoreys() {
            return cy.get('[data-cy="construction-information-number-of-storeys"]');
        },
        get yearBuilt() {
            return cy.get('[data-cy="construction-information-year-built"]');
        },
        get description() {
            return cy.get('[data-cy="construction-information-description"]');
        },
        get buildingLabel() {
            return cy.get('[data-cy="construction-information-building-label"]');
        },
        get principalBuilding() {
            return cy.get('[data-cy="construction-information-principal-bldg"]');
        },
        get wallConstruction() {
            return cy.get('[data-cy="construction-information-wall-construction"]');
        },
        get wallCondition() {
            return cy.get('[data-cy="construction-information-wall-condition"]');
        },
        get roofConstruction() {
            return cy.get('[data-cy="construction-information-roof-construction"]');
        },
        get roofCondition() {
            return cy.get('[data-cy="construction-information-roof-condition"]');
        },
        get floorConstruction() {
            return cy.get('[data-cy="construction-information-floor-construction"]');
        },
        get foundation() {
            return cy.get('[data-cy="construction-information-foundation"]');
        },
        get wirirngAge() {
            return cy.get('[data-cy="construction-information-wiring-age"]');
        },
        get plumbingAge() {
            return cy.get('[data-cy="construction-information-plumbing-age"]');
        },
        get insulation() {
            return cy.get('[data-cy="construction-information-insulation"]');
        },
        get glazing() {
            return cy.get('[data-cy="construction-information-glazing"]');
        },
        get otherFeatures() {
            return cy.get('[data-cy="construction-information-other-features"]');
        },
        get removeBtn() {
            return cy.get('[data-cy="remove-building-row"]');
        },
        get copyBtn() {
            return cy.get('[data-cy="copy-building-row"]');
        },
        get addBuildingBtn() {
            return cy.get('[data-cy="add-building-btn"]');
        },
    },
    BuildingSpaces: {
        get withInBuilding() {
            return cy.get('[data-cy="with-building"]');
        },
        get spaceType() {
            return cy.get('[data-cy="space-type"]');
        },
        get multiplicity() {
            return cy.get('[data-cy="multiplicity"]');
        },
        get floorArea() {
            return cy.get('[data-cy="floor-area"]');
        },
        get bedRooms() {
            return cy.get('[data-cy="bedrooms"]');
        },
        get doubleBedRooms() {
            return cy.get('[data-cy="double-bedrms"]');
        },
        get singleBedRoom() {
            return cy.get('[data-cy="single-bedrms"]');
        },
        get office() {
            return cy.get('[data-cy="office-study"]');
        },
        get bathrooms() {
            return cy.get('[data-cy="bathrooms"]');
        },
        get toilets() {
            return cy.get('[data-cy="toilets"]');
        },
        get spaceAddress() {
            return cy.get('[data-cy="space-address"]');
        },
        get modernisation() {
            return cy.get('[data-cy="modernisation"]');
        },
        get interiorCondition() {
            return cy.get('[data-cy="interior-condition"]');
        },
        get kitchenAge() {
            return cy.get('[data-cy="kitchen-age"]');
        },
        get kitchenQuality() {
            return cy.get('[data-cy="kitchen-quality"]');
        },
        get mainBathroomAge() {
            return cy.get('[data-cy="main-bathroom-age"]');
        },
        get mainBathroomQuality() {
            return cy.get('[data-cy="main-bathroom-quality"]');
        },
        get ensuiteAge() {
            return cy.get('[data-cy="ensuite-age"]');
        },
        get ensuiteQuality() {
            return cy.get('[data-cy="ensuite-quality"]');
        },
        get heatingType() {
            return cy.get('[data-cy="heating-type"]');
        },
        get quality() {
            return cy.get('[data-cy="quality"]');
        },
        get numberOfCarparks() {
            return cy.get('[data-cy="spaces-car-parks"]');
        },
        get garageFeatures() {
            return cy.get('[data-cy="garage-features"]');
        },
        get removeSpaceRow() {
            return cy.get('[data-cy="remove-garage-space-row"]');
        },
        get duplicateSpaceRow() {
            return cy.get('[data-cy="duplicate-garage-space-row"]');
        },
        get buildingSpaceLabel() {
            return cy.get('[data-cy="building-space-label"]');
        },
        get buildingSpaceType() {
            return cy.get('[data-cy="building-space-type"]');
        },
        get addSpaceBtn() {
            return cy.get('[data-cy="add-space-btn"]');
        }
    },
    SiteImprovements: {
        get otherImprovements() {
            return cy.get('[data-cy="site-other-improvement"]');
        },
        get quality() {
            return cy.get('[data-cy="site-quality"]');
        },
        get description() {
            return cy.get('[data-cy="site-development-description"]');
        },
        get addImprovementBtn() {
            return cy.get('[data-cy="add-improvement-row"]');
        },
    }
};


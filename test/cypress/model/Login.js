export default {
    get usernameInput() {
        return cy.get('[name="username"]');
    },
    get passwordInput() {
        return cy.get('[name="password"]');
    },
    get loginButton() {
        return cy.get('[name="submit"]');
    },
    login(username, password) {
        this.usernameInput.type(username);
        this.loginButton.click();
        this.usernameInput
            .first()
            .clear()
            .type(username);
        this.passwordInput.type(password, { log: false });
        this.loginButton.click();
    }
};

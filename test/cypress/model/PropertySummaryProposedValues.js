export default {
    elements: {
        get proposedCV() {
            return cy.get('[data-cy="proposed-cv"]');
        },
        get proposedLV() {
            return cy.get('[data-cy="proposed-lv"]');
        },
        get proposedVI() {
            return cy.get('[data-cy="proposed-vi"]');
        },
        get proposedNetRate() {
            return cy.get('[data-cy="proposed-net-rate"]');
        },
        get proposedUCV() {
            return cy.get('[data-cy="proposed-ucv"]');
        },
        get proposedULV() {
            return cy.get('[data-cy="proposed-ulv"]');
        },
        get proposedUVI() {
            return cy.get('[data-cy="proposed-uvi"]');
        },
        get unadjustedValuesSection() {
            return cy.get('[data-cy="unadjusted-values-section"]');
        }
    },
};

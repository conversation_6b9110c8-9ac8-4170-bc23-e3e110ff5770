export default {
    elements: {
        get header() {
            return cy.get('[data-cy="sales-search-tab"]');
        },
        get subheader() {
            return cy.get('[data-cy="search-criteria-tab-bar-subheader"]');
        },
        get SalesToProcessTab() {
            return cy.get('[data-cy="sales-to-process"]');
        },
        get SalesInspectionTab() {
            return cy.get('[data-cy="sales-inspection"]');
        },
        get UnlinkedTab() {
            return cy.get('[data-cy="sales-unlinked"');
        },
        get taSelect() {
            return cy.get('[data-cy="ta-select"]');
        },
        get taSelectDiv() {
            return cy.get('#territorialAuthoritiesDiv');
        },
        get selectAllTasCheckboxCheckbox() {
            return cy.get('input[type="checkbox"][value="multiselect-all"]');
        },
        get saleDateFrom() {
            return cy.get('[data-cy="sp-from-sale-date"]');
        },
        get saleDateTo() {
            return cy.get('[data-cy="sp-to-sale-date"]');
        },
        get saleInputDateFrom() {
            return cy.get('[data-cy="sp-from-input-date"]');
        },
        get saleInputDateTo() {
            return cy.get('[data-cy="sp-to-input-date"]');
        },
        get categories() {
            return cy.get('[data-cy="sp-categories"]');
        },
        get source() {
            return cy.get('[data-cy="sp-source-mulitselect"]');
        },
        get saleClassificationsSelectDiv() {
            return cy.get('[data-cy="sp-classification"]');
        },
        get processingStatusMultiselect() {
            return cy.get('[data-cy="sp-processing-status-multiselect"]');
        },
        get saleStatus() {
            return cy.get('[data-cy="sp-sale-status"]');
        },
        get gspFrom() {
            return cy.get('[data-cy="sp-from-gsp"]');
        },
        get gspTo() {
            return cy.get('[data-cy="sp-to-gsp"]');
        },
        get excludeFromHpiRtv() {
            return cy.get('[data-cy="sp-exclude-hpi-rtv"]');
        },
        get clearButton() {
            return cy.get('[data-cy="sp-clear-button"]');
        },
        get searchButton() {
            return cy.get('[data-cy="sp-search-button"]');
        },
        get searchResults() {
            return cy.get('[data-cy="sp-sales-search-result-line"]');
        },
        get searchResultsExpanded() {
            return cy.get('[data-cy="sp-sales-search-result-expanded"]');
        },
        get ownershipUpdateSection() {
            return cy.get('[data-cy="sp-ownership-update"]');
        },
        get surnameOrgInput() {
            return cy.get('[data-cy="sp-surname-org"]').first();
        },
        get updateSaleBtn() {
            return cy.get('[data-cy="sp-update-sale-btn"]');
        },
        get deleteSaleBtn() {
            return cy.get('[data-cy="sp-delete-sale-btn"]');
        },
        get globalDialog() {
            return cy.get('[data-cy="global-modal"]');
        },
        get saleValidationModalMessages() {
            return cy.get('[data-cy="sale-validation-modal-validation-messages"]');
        },
        get saleValidationModalErrors() {
            return cy.get('[data-cy="validation-messages-errors-ul"]');
        },
        get saleSearchTopPagination() {
            return cy.get('[data-cy="sp-sales-search-top-pagination"]');
        },
        get saleSearchBottomPagination() {
            return cy.get('[data-cy="sp-sales-search-bottom-pagination"]');
        },
        get saleSearchNoResult() {
            return cy.get('[data-cy="salesSearchNoResult"]');
        },
        get expandedViewCloseButton() {
            return cy.get('[data-cy="sp-expanded-close"');
        },
        get confirmValidationButton() {
            return cy.get(`[data-cy="button-sale-validation-confirm"]`);
        },
        get confirmDeleteButton() {
            return cy.get(`[data-cy="button-modal-confirm"]`).contains('DELETE');
        },
        get confirmSaleInspectionButton() {
            return cy.get(`[data-cy="button-modal-confirm"]`).contains('ADD');
        },
        get saleInspectionBtn() {
            return cy.get('[data-cy="sp-sale-inspection-btn"]');
        },
        saleInput(name) {
            return cy.get(`[data-cy="sp-expanded-sale-${name}"]`);
        },
        resultColumn(name) {
            return cy.get(`[data-cy="${name}-column-header"]`);
        },
        get saleIdColumn() {
            return cy.get('[data-cy="sale-id-column-header"] > .qvtd-sort-header');
        },
        get saleOutputCode() {
            return cy.get('[data-cy="sp-expanded-sale-output-code"] > .qv-h-full > .qv-w-full > :nth-child(1) > .multiselect > .multiselect__tags');
        },
        get saleSource() {
            return cy.get('[data-cy="sp-expanded-sale-source"] > .qv-h-full > .qv-w-full > :nth-child(1) > .multiselect > .multiselect__tags');
        },
        get taSelectBox() {
            return cy.get('.fieldTwo > .btn-group > .multiselect');
        },
        get selectAllTasCheckbox() {
            return cy.get('.multiselect-all > .checkbox');
        },
    },

    getTaCheckbox(taCode) {
        return cy.get(`input[type="checkbox"][value="${taCode}"]`);
    },

    search() {
        this.elements.searchButton.click();
    },

    clear() {
        this.elements.clearButton.click();
    },

    setTas(taCodes) {
        this.elements.taSelectDiv.click();
        this.unselectAllTas();
        for (const taCode of taCodes) {
            this.getTaCheckbox(taCode).check();
        }
    },

    saleSearchResult(name) {
        return this.elements.searchResults.first().find(`[data-cy="sp-${name}"]`);
    },

    saleSearchExpandedView(name) {
        return this.elements.searchResultsExpanded.find(`[data-cy="sp-expanded-${name}"]`);
    },

    toggleSaleSearchResultView() {
        return this.elements.expandedViewCloseButton.click();
    },

    unselectAllTas() {
        this.elements.selectAllTasCheckboxCheckbox.uncheck();
    },

    visit() {
        cy.visit('/roll-maintenance?tab=salesSearch');
    },

    visitSalesInspection() {
        cy.visit('/roll-maintenance/sales/inspection');
    },

    deleteSale() {
        this.elements.deleteSaleBtn.click();
        this.elements.confirmDeleteButton.click();
    },

    saveSale() {
        this.elements.updateSaleBtn.click();
        this.elements.confirmValidationButton.invoke('prop', 'dataset').then((dataset) => {
            const url = dataset.url;
            cy.visit(url);
        });
    },

    addSaleInspectionConsent() {
        this.elements.saleInspectionBtn.click();
        this.elements.confirmSaleInspectionButton.then(($button) => {
            $button.trigger('click');
        });
    },

    clickSaleSearchResultPropertyLink(index = 0) {
        this.saleSearchResult('address').each((element, i, list) => {
            if (i === index) {
                cy.doubleLog(element);
                cy.wrap(element)
                    .find('a')
                    .then((linkElement) => {
                        expect(linkElement).to.have.attr('target', '_blank');
                        expect(linkElement).to.have.attr('href');
                        cy.visit(linkElement.attr('href'));
                    });
            }
        });
    },

    forceUnselectAllTAs() {
        this.elements.taSelectBox.click({force: true});
        this.elements.selectAllTasCheckbox.click({force: true});
        this.elements.selectAllTasCheckbox.click({force: true});
        this.unselectAllTas();
    }
};

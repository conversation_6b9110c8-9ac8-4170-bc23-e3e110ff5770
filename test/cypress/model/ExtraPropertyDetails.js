
export default {
    get monarchDetailsTab() {
        return cy.get('[data-cy="cy-ed-monarch-details"]');
    },
    get title() {
        return cy.get('[data-cy="ed-card-title"]');
    },
    get saveButton() {
        return cy.get('[data-cy="ed-save-button"]');
    },
    isLoaded(){
        this.title.should('exist');
    },
    interceptAndSave(){
        cy.intercept(`/saveQvProperty`).as('saveQvProperty');
        this.saveButton.click();
    },
    riskAndHazards: {
        get tab() {
            return cy.get('[data-cy="cy-ed-risks-and-hazards"]'); 
        },
        get propertyPlusSection() {
            return cy.get('[data-cy="ed-property-plus-section"]');
        },
        get remedyDeadline() {
            return cy.get('[data-cy="ed-remedy-year"]');
        },
    },
    propertyInfo: {
        get qvCategory() {
            return cy.get('[data-cy="general-info-qv-category-label"]');
        },
        get grouping() {
            return cy.get('[data-cy="general-info-grouping-label"]');
        },
        get proposedZone() {
            return cy.get('[data-cy="general-info-proposed-zone-label"]');
        },
        get actualEarthquakeRating() {
            return cy.get('[data-cy="general-info-actual-earthquake-rating-label"]');
        },
        get earthquakeRatingRange() {
            return cy.get('[data-cy="general-info-earthquake-rating-range-label"]');
        },       
        get earthquakeRatingAssessor() {
            return cy.get('[data-cy="general-info-earthquake-rating-assessor-label"]');
        },
        get remedyDeadline() {
            return cy.get('[data-cy="general-info-remedy-deadline-label"]');
        },
        get liquefaction() {
            return cy.get('[data-cy="general-info-qv-liquefaction-label"]');
        }
    },
    constructionInfo: {
        get title() {
            return cy.get('[data-cy="cy-bas-construction-information-title"]');
        }
    },
    spaces: {
        get title() {
            return cy.get('[data-cy="cy-bas-spaces-title"]');
        }
    },
    siteImprovements: {
        get title() {
            return cy.get('[data-cy="cy-site-improvements-title"]');
        } 
    },  
    editPropertyDetails: {
        get title() {
            return cy.get('[data-cy="pd-edit-property-heading"]');
        },
        isLoaded() {
            this.title.should('exist');
        },
        get editPropertyDetailButton() {
            return cy.get('[data-cy="edit-property-detail-button"]');
        },
        get dateEnteredLabel() {
            return cy.get('[data-cy="general-info-date-entered-label"]');  
        },
        get dateEntered() {
            return cy.get('[data-cy="general-info-date-entered"]');
        },
        get container() {
            return cy.get('[data-cy="pd-edit-property-container"]');
        },
        successModalAppears() {
            cy.get('[data-cy="pd-success-modal"]').should('exist').and('be.visible');
            cy.get('[data-cy="modal-close-button"]').should('exist').and('be.visible').click();
        },
        generalInfo: {
            get section() {
                return cy.get('[data-cy="cw-pd-general-information-section"]'); 
            },
            get qvCategory() {
                return cy.get('[data-cy="general-info-qv-category-label"]');
            },
            get grouping() {
                return cy.get('[data-cy="general-info-grouping-label"]');
            },
            get proposedZone() {
                return cy.get('[data-cy="general-info-proposed-zone-label"]');
            },
            get actualEarthquakeRating() {
                return cy.get('[data-cy="general-info-actual-earthquake-rating-label"]');
            },
            get earthquakeRatingRange() {
                return cy.get('[data-cy="general-info-earthquake-rating-range-label"]');
            },       
            get earthquakeRatingAssessor() {
                return cy.get('[data-cy="general-info-earthquake-rating-assessor-label"]');
            },
            get remedyDeadline() {
                return cy.get('[data-cy="general-info-remedy-deadline-label"]');
            },
            get liquefaction() {
                return cy.get('[data-cy="general-info-qv-liquefaction-label"]');
            },
            get qvCategoryInput() {
                return cy.get('[data-cy="cw-pd-qv-category"]');
            },
            get groupingInput() {
                return cy.get('[data-cy="cw-pd-grouping"]');
            },
            get proposedZoneInput() {
                return cy.get('[data-cy="cw-pd-proposed-zone"]');
            },
            get actualEarthquakeRatingInput() {
                return cy.get('[data-cy="cw-pd-actual-eq-rating"]');
            },
            get earthquakeRatingRangeInput() {
                return cy.get('[data-cy="cw-pd-eq-rating-range"]');
            },
            get earthquakeRatingAssessorInput() {
                return cy.get('[data-cy="cw-pd-eq-rating-assessor"]');
            },
            get remedyDeadlineInput() {
                return cy.get('[data-cy="cw-pd-remedy-year"]');
            },
            get liquefactionInput() {
                return cy.get('[data-cy="cw-pd-liquefaction"]');
            },
        },
        propertyInfo: {
            get title() {
                return cy.get('[data-cy="general-info-property-info-title"]'); 
            },
            get effectiveLandArea() {
                return cy.get('[data-cy="general-info-effective-land-area-input"]');
            }
        },
        locationDetails: {
            get title() {
                return cy.get('[data-cy="general-info-location-details-title"]');
            }
        },
        propertySummary: {
            get title() {
                return cy.get('[data-cy="general-info-property-summary-title"]');
            }
        },
        get cancelButton() {
            return cy.get('[data-cy="pd-cancel-button"]');
        },
        get saveButton() {
            return cy.get('[data-cy="pd-save-button"]');
        },
        get saveAndCloseButton() {
            return cy.get('[data-cy="pd-save-and-close-button"]');
        }
    },
}

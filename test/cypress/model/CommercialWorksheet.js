/*global cy it describe context */

import { createCommercialPropertyWithWorksheetBody, createResidentialPropertyBody, getGenerateRequest } from "../support/testFactoryCommercialWorksheet";

export default {
    get pageTitle() {
        return cy.get('[data-cy="cw-page-title"]');
    },
    propertyInfo: {
        autoWorksheet: {
            get autoWorksheetLabel() {
                return cy.get('[data-cy="autoWorksheetHeader"]');
            },
            get autoWorksheetSwitch() {
                return cy.get('[data-cy="autoWorksheetSwitch"]');
            },
            exists() {
                this.autoWorksheetLabel.should('exist');
                this.autoWorksheetSwitch.should('exist');
            },
        },
        allRowsExist() {
            this.autoWorksheet.exists();
        },
    },
    propertyDetail: {
        get header() {
            return cy.get('[data-cy="cw-property-detail-header"]');
        },
        get qvCategoryLabel() {
            return cy.get('[data-cy="cw-pd-qv-category-label"]');
        },
        get qvCategory() {
            return cy.get('[data-cy="cw-pd-qv-category"]');
        },
        get grouping(){
            return cy.get('[data-cy="cw-pd-grouping"]');
        },
        get proposedZone(){
            return cy.get('[data-cy="cw-pd-proposed-zone"]');
        },
        get planNumber(){
            return cy.get('[data-cy="cw-pd-plan-number"]');
        },
        get zone(){
            return cy.get('[data-cy="cw-pd-zone"]');
        },
        get eqRating(){
            return cy.get('[data-cy="cw-pd-actual-eq-rating"]');
        },
        get ratingRange(){
            return cy.get('[data-cy="cw-pd-eq-rating-range"]');
        },
        get ratingAssessor(){
            return cy.get('[data-cy="cw-pd-eq-rating-assessor"]');
        },
        get remedyYear(){
            return cy.get('[data-cy="cw-pd-remedy-year"]');
        },
        get liquefaction(){
            return cy.get('[data-cy="cw-pd-liquefaction"]');
        },
        get propertyName(){
            return cy.get('[data-cy="cw-pd-property-name"]');
        },
        get valuationDate(){
            return cy.get('[data-cy="cw-valuation-date"]');
        },
        get quality() {
            return cy.get('[data-cy="cw-pd-quality"]');
        },
        get location() {
            return cy.get('[data-cy="cw-pd-location"]');
        },
        get remedyYear() {
            return cy.get('[data-cy="cw-pd-remedy-year"]');
        },
        get capRate() {
            return cy.get('[data-cy="cw-pd-cap-rate"]');
        },
        get taModal() {
            return cy.get('[data-cy="cw-ta-modal"]');
        },
        get cvIncomeInput(){
            return cy.get('[data-cy="cw-pd-cv"]')
        },
        get viSummationInput(){
            return cy.get('[data-cy="cw-pd-vi"]')
        },
        exists() {
            this.header.should('exist');
            this.qvCategoryLabel.should('exist');
        },
        isCurrentReadOnly(){
            this.qvCategory.should('have.class', 'multiselect--disabled');
            this.grouping.should('have.class', 'multiselect--disabled');
            this.proposedZone.should('have.class', 'multiselect--disabled');
            this.quality.should('have.class', 'multiselect--disabled');
            this.location.should('have.class', 'multiselect--disabled');
            this.eqRating.should('have.attr', 'disabled', 'disabled');
            this.ratingRange.should('have.class', 'multiselect--disabled');
            this.remedyYear.should('have.attr', 'disabled', 'disabled');
            this.liquefaction.should('have.class', 'multiselect--disabled');
            this.capRate.should('have.attr', 'disabled', 'disabled');
        },
        isCurrentReadWrite(){
            this.qvCategory.should('not.have.class', 'multiselect--disabled');
            this.grouping.should('not.have.class', 'multiselect--disabled');
            this.proposedZone.should('not.have.class', 'multiselect--disabled');
            this.quality.should('not.have.class', 'multiselect--disabled');
            this.location.should('not.have.class', 'multiselect--disabled');
            this.eqRating.should('not.have.attr', 'disabled');
            this.ratingRange.should('not.have.class', 'multiselect--disabled');
            this.remedyYear.should('not.have.attr', 'disabled');
            this.liquefaction.should('not.have.class', 'multiselect--disabled');
            this.capRate.should('not.have.attr', 'disabled');
        },
        isRevisionReadOnly(){
            this.capRate.should('have.attr', 'disabled', 'disabled');
        },
        isRevisionReadWrite(){
            this.capRate.should('not.have.attr', 'disabled', 'disabled');
        }
    },
    land: {
        get header() {
            return cy.get('[data-cy="cw-land-header"]');
        },
        get tableHeader() {
            return cy.get('[data-cy="cw-land-table-head"]');
        },
        get rows() {
            return cy.get('[data-cy="cw-land-row"]');
        },
        get newRow() {
            return cy.get('[data-cy="cw-new-land-row"]');
        },
        get newRowButton() {
            return cy.get('[data-cy="cw-add-new-land-row-button"]');
        },
        get totalArea() {
            return cy.get('[data-cy="cw-total-land-area"]');
        },
        get newRowDescription() {
            return cy.get('[data-cy="cw-new-land-row-description"]');
        },
        get newRowStreetLocation() {
            return cy.get('[data-cy="cw-new-land-row-street-location-type"]');
        },
        get newRowArea() {
            return cy.get('[data-cy="cw-new-land-row-area"]');
        },
        get newRowRate() {
            return cy.get('[data-cy="cw-new-land-row-rate"]');
        },
        get totalValue() {
            return cy.get('[data-cy="cw-total-land-value"]');
        },
        exists() {
            this.header.should('exist');
            this.tableHeader.should('exist');
        },
        isCurrentReadOnly(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-land-row-description"]').then(description => {
                    cy.wrap(description).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-land-row-street-location-type"]').then(locationType => {
                    cy.wrap(locationType).should('have.class', 'multiselect--disabled');
                })
                cy.wrap(row).find('[data-cy="cw-land-row-area"]').then(area => {
                    cy.wrap(area).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-land-row-rate-per-metre"]').then(ratePerMetre => {
                    cy.wrap(ratePerMetre).should('have.attr', 'readonly', 'readonly');
                })
            });
            this.newRow.should('not.exist');
        },
        isCurrentReadWrite(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-land-row-description"]').then(description => {
                    cy.wrap(description).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-land-row-street-location-type"]').then(locationType => {
                    cy.wrap(locationType).should('not.have.class', 'multiselect--disabled');
                })
                cy.wrap(row).find('[data-cy="cw-land-row-area"]').then(area => {
                    cy.wrap(area).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-land-row-rate-per-metre"]').then(ratePerMetre => {
                    cy.wrap(ratePerMetre).should('not.have.attr', 'readonly', 'readonly');
                })
            });
            this.newRow.should('exist');
        },
        isRevisionReadOnly(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-land-row-rate-per-metre"]').then(ratePerMetre => {
                    cy.wrap(ratePerMetre).should('have.attr', 'readonly', 'readonly');
                })
            });
        },
        isRevisionReadWrite(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-land-row-rate-per-metre"]').then(ratePerMetre => {
                    cy.wrap(ratePerMetre).should('not.have.attr', 'readonly', 'readonly');
                })
            });
        },
    },
    improvements: {
        get header() {
            return cy.get('[data-cy="cw-improvements-header"]');
        },
        get tableHeader() {
            return cy.get('[data-cy="cw-improvements-table-head"]');
        },
        get rows() {
            return cy.get('[data-cy="cw-improvement-row"]');
        },
        get newRow() {
            return cy.get('[data-cy="cw-new-improvement-row"]');
        },
        get newRowButton() {
            return cy.get('[data-cy="cw-add-new-improvement-row-button"]');
        },
        get newRowDescription() {
            return cy.get('[data-cy="cw-new-improvement-row-description"]');
        },
        get newRowImpType() {
            return cy.get('[data-cy="cw-new-improvement-row-imp-type"]');
        },
        get newRowArea() {
            return cy.get('[data-cy="cw-new-improvement-row-area"]');
        },
        get newRowCarparks() {
            return cy.get('[data-cy="cw-new-improvement-row-carparks"]');
        },
        get newRowRent() {
            return cy.get('[data-cy="cw-new-improvement-row-rental"]');
        },
        get newRowVacant() {
            return cy.get('[data-cy="cw-new-improvement-row-vacant"]');
        },
        get newRowExcessLand() {
            return cy.get('[data-cy="cw-new-improvement-row-excess-land"]');
        },
        get capRate() {
            return cy.get('[data-cy="cw-improvement-cap-rate"]');
        },
        get nla() {
            return cy.get('[data-cy="cw-nla"]');
        },
        get nlaByTotalArea() {
            return cy.get('[data-cy="cw-nla-by-total-area"]');
        },
        get outgoingsPerMtSq() {
            return cy.get('[data-cy="cw-outgoings-per-mtsq"]');
        },
        get newRowCvIncome() {
            return cy.get('[data-cy="cw-new-improvement-row-cv-income"]');
        },
        get newRowMultiple() {
            return cy.get('[data-cy="cw-new-improvement-row-multiple"]');
        },
        get newRowLife() {
            return cy.get('[data-cy="cw-new-improvement-row-life"]');
        },
        get newRowYearBuilt() {
            return cy.get('[data-cy="cw-new-improvement-row-year-built"]');
        },
        get newRowObsolete() {
            return cy.get('[data-cy="cw-new-improvement-row-obsolete"]');
        },
        get newRowLumpSum() {
            return cy.get('[data-cy="cw-new-improvement-row-lump-sum"]');
        },
        get newRowViSummation() {
            return cy.get('[data-cy="cw-new-improvement-row-vi-summation"]');
        },
        get totalGrossRent() {
            return cy.get('[data-cy="cw-improvement-total-gross-rent"]');
        },
        get totalOutGoings() {
            return cy.get('[data-cy="cw-improvement-total-outgoings"]');
        },
        get totalNetRent() {
            return cy.get('[data-cy="cw-improvement-total-net-rent"]')
        },
        exists() {
            this.header.should('exist');
            this.tableHeader.should('exist');
        },
        isCurrentReadOnly(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-imp-row-description"]').then(description => {
                    cy.wrap(description).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-imp-type"]').then(type => {
                    cy.wrap(type).should('have.class', 'multiselect--disabled');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-area"]').then(area => {
                    cy.wrap(area).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-parks"]').then(parks => {
                    cy.wrap(parks).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-multiple"]').then(multiple => {
                    cy.wrap(multiple).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-rent"]').then(rent => {
                    cy.wrap(rent).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-life"]').then(life => {
                    cy.wrap(life).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-year-built"]').then(year => {
                    cy.wrap(year).should('have.attr', 'readonly', 'readonly');
                })
            });
            this.newRow.should('not.exist');
        },
        isCurrentReadWrite(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-imp-row-description"]').then(description => {
                    cy.wrap(description).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-imp-type"]').then(type => {
                    cy.wrap(type).should('not.have.class', 'multiselect--disabled');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-area"]').then(area => {
                    cy.wrap(area).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-parks"]').then(parks => {
                    cy.wrap(parks).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-multiple"]').then(multiple => {
                    cy.wrap(multiple).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-rent"]').then(rent => {
                    cy.wrap(rent).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-life"]').then(life => {
                    cy.wrap(life).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-year-built"]').then(year => {
                    cy.wrap(year).should('not.have.attr', 'readonly', 'readonly');
                })
            });
            this.newRow.should('exist');
        },
        isRevisionReadOnly(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-imp-row-rent"]').then(rent => {
                    cy.wrap(rent).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-life"]').then(life => {
                    cy.wrap(life).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-percent-vacant"]').then(year => {
                    cy.wrap(year).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-percent-excess-land"]').then(year => {
                    cy.wrap(year).should('have.attr', 'readonly', 'readonly');
                })
            });
            this.capRate.should('have.attr', 'readonly', 'readonly');
        },
        isRevisionReadWrite(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-imp-row-rent"]').then(rent => {
                    cy.wrap(rent).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-life"]').then(life => {
                    cy.wrap(life).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-percent-vacant"]').then(year => {
                    cy.wrap(year).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-imp-row-percent-excess-land"]').then(year => {
                    cy.wrap(year).should('not.have.attr', 'readonly', 'readonly');
                })
            });
            this.capRate.should('not.have.attr', 'readonly', 'readonly');
        },
    },
    totals: {
        get header() {
            return cy.get('[data-cy="cw-total-worksheet-values-header"]');
        },
        get tableHeader() {
            return cy.get('[data-cy="cw-totals-table-head"]');
        },
        get cvIncomeRadio() {
            return cy.get('[data-cy="cw-totals-cv-income-radio"]');
        },
        get viIncomeRadio() {
            return cy.get('[data-cy="cw-totals-vi-income-radio"]');
        },
        get cvIncomeCv() {
            return cy.get('[data-cy="cw-totals-total-income-cv"]');
        },
        get cvIncomeVi() {
            return cy.get('[data-cy="cw-totals-cv-income-vi"]');
        },
        get comment() {
            return cy.get('[data-cy="cw-totals-comment"]');
        },
        exists() {
            this.header.should('exist');
            this.tableHeader.should('exist');
        },
        isReadOnly(){
            this.cvIncomeRadio.should('have.attr', 'disabled', 'disabled');
            this.viIncomeRadio.should('have.attr', 'disabled', 'disabled');
            this.comment.should('have.attr', 'readonly', 'readonly');
        },
        isReadWrite(){
            this.cvIncomeRadio.should('not.have.attr', 'disabled', 'disabled');
            this.viIncomeRadio.should('not.have.attr', 'disabled', 'disabled');
            this.comment.should('not.have.attr', 'readonly', 'readonly');
        },
    },
    actualRentals: {
        get header() {
            return cy.get('[data-cy="cw-actual-rentals-header"]');
        },
        get tableHeader() {
            return cy.get('[data-cy="cw-actual-rental-table-head"]');
        },
        get rows() {
            return cy.get('[data-cy="cw-actual-rental-row"]');
        },
        get newRow() {
            return cy.get('[data-cy="cw-new-actual-rental-row"]');
        },
        get newRowButton() {
            return cy.get('[data-cy="cw-add-new-actual-rental-button"]');
        },
        get newRowDateRent() {
            return cy.get('[data-cy="cw-new-actual-rental-date-rent"]');
        },
        get newRowAnnualRent() {
            return cy.get('[data-cy="cw-new-actual-rental-annual-rent"]');
        },
        get newRowFloorArea() {
            return cy.get('[data-cy="cw-new-actual-rental-area"]');
        },
        get newRowrentalPerSqmt() {
            return cy.get('[data-cy="cw-new-actual-rental-per-sqmt"]');
        },
        get newRowRentCode() {
            return cy.get('[data-cy="cw-new-actual-rental-rent-code"]');
        },
        get newRowComment() {
            return cy.get('[data-cy="cw-new-actual-rental-tenant"]');
        },
        exists() {
            this.header.should('exist');
            this.tableHeader.should('exist');
        },
        isReadOnly(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-actual-rental-date-rent"]').then(rentalDate => {
                    cy.wrap(rentalDate).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-actual-rental-annual-rent"]').then(annualRent => {
                    cy.wrap(annualRent).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-actual-rental-per-sqmt"]').then(rentalPerSqmt => {
                    cy.wrap(rentalPerSqmt).should('have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-actual-rental-rent-code"]').then(parks => {
                    cy.wrap(parks).should('have.class', 'multiselect--disabled');
                })
            });
        },
        isReadWrite(){
            this.rows.each(row => {
                cy.wrap(row).find('[data-cy="cw-actual-rental-date-rent"]').then(rentalDate => {
                    cy.wrap(rentalDate).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-actual-rental-annual-rent"]').then(annualRent => {
                    cy.wrap(annualRent).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-actual-rental-per-sqmt"]').then(rentalPerSqmt => {
                    cy.wrap(rentalPerSqmt).should('not.have.attr', 'readonly', 'readonly');
                })
                cy.wrap(row).find('[data-cy="cw-actual-rental-rent-code"]').then(parks => {
                    cy.wrap(parks).should('not.have.class', 'multiselect--disabled');
                })
            });
        },
    },
    adoptedValues: {
        get header() {
            return cy.get('[data-cy="cw-adopted-worksheet-values-header"]');
        },
        get tableHeader() {
            return cy.get('[data-cy="cw-adopted-table-head"]');
        },
        get row() {
            return cy.get('[data-cy="cw-adopted-row"]');
        },
        get cv() {
            return cy.get('[data-cy="cw-adopted-cv"]');
        },
        get lv() {
            return cy.get('[data-cy="cw-adopted-lv"]');
        },
        get vi() {
            return cy.get('[data-cy="cw-adopted-vi"]');
        },
        get cvOverrideTotal() {
            return cy.get('[data-cy="cw-total-cv-override"]');
        },
        get lvOverrideTotal() {
            return cy.get('[data-cy="cw-total-lv-override"]');
        },
        get apportionmentCv() {
            return cy.get('[data-cy="cw-total-apportionment-cv"]');
        },
        get apportionmentLv() {
            return cy.get('[data-cy="cw-total-apportionment-lv"]');
        },
        exists() {
            this.header.should('exist');
            this.tableHeader.should('exist');
        },
    },
    currentAssessment: {
        get header() {
            return cy.get('[data-cy="cw-current-assessment-header"]');
        },
        get tableHeader() {
            return cy.get('[data-cy="cw-current-assessment-table-head"]');
        },
        get row() {
            return cy.get('[data-cy="cw-current-assessment-row"]');
        },
        exists() {
            this.header.should('exist');
            this.tableHeader.should('exist');
            this.row.should('exist');
        },
    },
    rfc: {
        get header() {
            return cy.get('[data-cy="cw-rfc-header"]');
        },
        get updateAssessmentLabel() {
            return cy.get('[data-cy="cw-rfc-update-assessment-label"]');
        },
        get inputSection() {
            return cy.get('[data-cy="cw-rfc-input"]');
        },
        get output() {
            return cy.get('[data-cy="reason-for-change-output"]');
        },
        get source() {
            return cy.get('[data-cy="reason-for-change-source"]');
        },
        get reason() {
            return cy.get('[data-cy="reason-for-change-reason"]');
        },
        exists() {
            this.header.should('exist');
            this.updateAssessmentLabel.should('exist');
            this.inputSection.should('exist');
        },
        isReadOnly(){
            this.output.should('have.class', 'multiselect--disabled');
            this.source.should('have.class', 'multiselect--disabled');
            this.reason.should('have.attr', 'readonly', 'readonly');
        },
        isReadWrite(){
            this.output.should('not.have.class', 'multiselect--disabled');
            this.source.should('not.have.class', 'multiselect--disabled');
            this.reason.should('not.have.attr', 'readonly', 'readonly');
        }
    },

    get deleteButton() {
        return cy.get('[data-cy="cw-delete-button"]');
    },
    get cancelChangesButton() {
        return cy.get('[data-cy="cw-cancel-button"]');
    },
    get updateAssessmentButton() {
        return cy.get('[data-cy="cw-update-assessment-button"]');
    },
    get updateWorksheetOnlyButton() {
        return cy.get('[data-cy="cw-update-worksheet-button"]');
    },
    get confirmDeleteButton() {
        return cy.get(`[data-cy="cw-button-dialog-confirm"]`).contains('Yes, Delete Worksheet');
    },
    get saveWorksheetButton() {
        return cy.get('[data-cy="cw-save-button"]');
    },
    get modalOkButton(){
        return cy.get('[data-cy="button-dialog-confirm"]');
    },
    get modalConfirmButton() {
        return cy.get(`[data-cy="cw-modal-confirm-button"]`);
    },
    get createRevisionWorksheetButton() {
        return cy.get('[data-cy="cw-create-revision-button"]');
    },
    get revisionWorksheetTab(){
        return cy.get('[data-cy="commercialWorksheetTab2"]');
    },
    get printWorksheetButton() {
        return cy.get('[data-cy="cw-print-button"]').first();
    },
    get printWorksheetButtonTop() {
        return cy.get('[data-cy="cw-print-button-top"]');
    },
    get printWorksheetButtonBottom() {
        return cy.get('[data-cy="cw-print-button-bottom"]');
    },

    closeGlobalDialog() {
        // let a = document.querySelector('[data-cy="global-modal"]')
        // a.close()
        cy.get('[data-cy="global-modal"]').then($el => {
            if ($el.length > 0) {
                $el[0].close();
            }
        });
    },

    dismissValidationWarnings() {
        cy.get('[data-cy="global-modal"]').then($el => {
            if ($el.find('[data-cy="button-cw-validation-confirm"]').length > 0) {
                cy.get('[data-cy="button-cw-validation-confirm"]').click();
            }
        });
    },

    generateCommercialPropertyWithWorksheet() {
        return cy.request(getGenerateRequest(createCommercialPropertyWithWorksheetBody())).then((res) => {
            const commercialProperty = res.body.data[0];
            commercialProperty.qpid = parseInt(commercialProperty.id);
            return commercialProperty;
        });
    },

    generateResidentialProperty() {
        return cy.request(getGenerateRequest(createResidentialPropertyBody())).then((res) => {
            const property = res.body.data[0];
            property.qpid = parseInt(property.id);
            return property;
        });
    },

    calculateCvIncome({ rent, vacant, carparks, excessLand, capRate }) {
        // '(Total Rent * (1 - % Vacant / 100)) / (Cap Rate / 100) + (% Excess Land / 100) * Total Land Value';
        return this.getInputValue('land', 'totalValue').then((totalLandValue) => {
            const cvIncome = (rent * carparks * 52 * (1 - vacant / 100)) / (capRate / 100) + (excessLand / 100) * totalLandValue;
            return Math.round(cvIncome);
        });
    },

    calculateViSummation({ area, multiple, life, depreciation, obsolete, lumpSum }) {
        // 'Area * Modal * Multiple * (1 - Depreciation) * (1 - % Obsolete  / 100) + Lump Sum';
        return this.getInputValue('propertyDetail', 'taModal').then((modal) => {
            const viSummation = area * modal * multiple * (1 - depreciation) * (1 - obsolete / 100) + lumpSum;
            return Math.round(viSummation);
        });
    },

    allActionButtonsExist() {
        this.deleteButton.should('exist');
        this.cancelChangesButton.should('exist');
        this.updateAssessmentButton.should('exist');
        this.updateWorksheetOnlyButton.should('exist');
        // this.createRevisionWorksheetButton.should('exist');
    },

    externalUserActionButtonsNotExist() {
        this.deleteButton.should('not.exist');
        this.updateAssessmentButton.should('not.exist');
        this.updateWorksheetOnlyButton.should('not.exist');
        this.createRevisionWorksheetButton.should('not.exist');
    },

    allSectionsExist() {
        this.propertyDetail.exists();
        this.land.exists();
        this.improvements.exists();
        this.totals.exists();
        this.actualRentals.exists();
        this.adoptedValues.exists();
        this.currentAssessment.exists();
        this.rfc.exists();
    },

    allSectionsExceptRfcExist() {
        this.propertyDetail.exists();
        this.land.exists();
        this.improvements.exists();
        this.totals.exists();
        this.actualRentals.exists();
        this.adoptedValues.exists();
        this.currentAssessment.exists();
    },

    everythingExists() {
        this.isLoaded();
        this.allSectionsExist();
        this.allActionButtonsExist();
    },

    getInputValue(section, element) {
        return this[section][element].invoke('val').then((val) => {
            return this.moneyStringToNumber(val);
        });
    },

    moneyStringToNumber(moneyString) {
        const regex = /[$,]/g;
        return Number(moneyString.replace(regex, ''));
    },

    apportionmentCvEqualsAdoptedCv() {
        let total = 0;
        this.adoptedValues.row
            .each(($row) => {
                cy.wrap($row)
                    .find('[data-cy="cw-apportionment-cv"]')
                    .invoke('val')
                    .then((value) => {
                        total += this.moneyStringToNumber(value);
                    });
            })
            .then(() => {
                this.getInputValue('adoptedValues', 'apportionmentCv').should('eq', total);
                this.getInputValue('adoptedValues', 'cv').should('eq', total);
            });
    },

    apportionmentLvEqualsAdoptedLv() {
        let total = 0;
        this.adoptedValues.row
            .each(($row) => {
                cy.wrap($row)
                    .find('[data-cy="cw-apportionment-lv"]')
                    .invoke('val')
                    .then((value) => {
                        total += this.moneyStringToNumber(value);
                    });
            })
            .then(() => {
                this.getInputValue('adoptedValues', 'apportionmentLv').should('eq', total);
                this.getInputValue('adoptedValues', 'lv').should('eq', total);
            });
    },

    saveWorksheet() {
        this.saveWorksheetButton.click();
        this.modalConfirmButton.then(($button) => {
            $button.trigger('click');
        });
    },

    deleteWorksheet() {
        this.deleteButton.click();
        this.confirmDeleteButton.click();
    },

    updateAssessment() {
        this.updateAssessmentButton.click();
    },

    updateWorksheetOnly() {
        this.updateWorksheetOnlyButton.click();
    },

    isLoaded() {
        this.pageTitle.should('exist');
    },

    visit(qpid) {
        cy.visit(this.getPageRoute(qpid));
    },

    visitRevision(qpid){
        cy.visit(this.getRevisionPageRoute(qpid));
    },

    getPageRoute(qpid) {
        return `/roll-maintenance/commercial-worksheet/${qpid}`;
    },

    getRevisionPageRoute(qpid){
        return `/roll-maintenance/commercial-revision-worksheet/${qpid}`;
    },

    interceptAndUpdateWorksheet() {
        cy.intercept(`/api/commercialWorksheet/updateCommercialWorksheet`).as('updateWorksheet');
        this.updateWorksheetOnly();
        cy.wait(500);
    },

    successModalAppears(click = true) {
        cy.get('[data-cy="dialog-title"]').should('exist').and('be.visible');
        cy.get('[data-cy="button-dialog-confirm"]').should('exist').and('be.visible');
        if (click) {
            cy.get('[data-cy="button-dialog-confirm"]').click();
        }
    },

    successModalType2Appears() {
        cy.get('[data-cy="cw-dialog-title"]').should('exist').and('be.visible');
        cy.get('[data-cy="cw-button-dialog-confirm"]').should('exist').and('be.visible').click();
    },

    interceptAndUpdateAssessment() {
        cy.intercept(`/api/commercialWorksheet/updateCommercialWorksheet`).as('updateWorksheet');
        this.updateAssessment();
    },

    validationModalAppears(type, message) {
        cy.get('[data-cy="cw-validation-title"]').should('exist').and('be.visible');
        cy.get('[data-cy="button-cw-validation-return"]').should('exist').and('be.visible');
        cy.get(`[data-cy=validation-messages-${type}-li]`).contains(message).should('exist');
        cy.get('[data-cy="button-cw-validation-return"]').click();
    },

    acceptNewValues() {
        cy.get('[data-cy="cw-dialog-title"]').should('exist').and('be.visible');
        cy.get('[data-cy="cw-button-dialog-cancel"]').should('exist').and('be.visible');
        cy.get('[data-cy="cw-button-dialog-confirm"]').should('exist').and('be.visible').click();
        cy.wait(500);
    },

    updatePropertyDetails(capRate, planNumber = 'TEST') {
        this.propertyDetail.quality.first().click();
        this.propertyDetail.location.first().click();
        this.propertyDetail.planNumber.clear().type(planNumber);
        this.propertyDetail.capRate.clear().type(capRate);
    },

    addNewActualRentalRow(rental) {
        this.actualRentals.newRowDateRent.type(rental.rentalDate);
        this.actualRentals.newRowAnnualRent.type(rental.grossRental);
        this.actualRentals.newRowFloorArea.type(rental.floorArea);
        this.actualRentals.newRowRentCode.click().find('.multiselect__element:nth-child(2)').click();
        this.actualRentals.newRowComment.type(rental.tenant);
        this.actualRentals.newRowButton.click();
    },

    fillReasonForChange() {
        this.rfc.output.click().find('.multiselect__element:nth-child(1)').click();
        this.rfc.output.click().find('.multiselect__element:nth-child(2)').click();
        this.rfc.source.click().find('.multiselect__element:nth-child(1)').click();
        this.rfc.source.click().find('.multiselect__element:nth-child(2)').click();
        this.rfc.reason.clear().type('Test Reason for Change');
    }

};

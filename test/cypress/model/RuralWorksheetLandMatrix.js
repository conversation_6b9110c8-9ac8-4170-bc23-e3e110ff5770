const LandMatrix = {
	get ruralWorksheetLandMatrixSection() {
		return cy.get('div[data-cy=ruralWorksheetLandMatrixSection]');
	},
	get ruralWorksheetLandMatrixExpander() {
		return cy.get('div[data-cy=ruralWorksheetLandMatrixSection] [data-cy=expander]');
	},
	get ruralWorksheetLandMatrixRecalculateLandMatrixButton() {
		return cy.get('li[data-cy=ruralWorksheetLandMatrixRecalculateLandMatrixButton]');
	},
	get ruralWorksheetLandMatrixCopyLandMatrixButton() {
		return cy.get('li[data-cy=ruralWorksheetLandMatrixCopyLandMatrixButton]');
	},
	get ruralWorksheetMatchesLandMatrixIndicator() {
		return cy.get('span[data-cy=ruralWorksheetMatchesLandMatrixIndicator]');
	},
	get ruralWorksheetLandMatrixContourColumn() {
		return cy.get('div[data-cy=ruralWorksheetLandMatrixContourColumn]');
	},
	get ruralWorksheetLandMatrixLandCoverColumn() {
		return cy.get('div[data-cy=ruralWorksheetLandMatrixLandCoverColumn]');
	},
	get ruralWorksheetLandMatrixQvLandCoverColumn() {
		return cy.get('div[data-cy=ruralWorksheetLandMatrixQvLandCoverColumn]');
	},
	get ruralWorksheetLandMatrixAreaColumn() {
		return cy.get('div[data-cy=ruralWorksheetLandMatrixAreaColumn]');
	},
	get ruralWorksheetLandMatrixEstimatedRateColumn() {
		return cy.get('div[data-cy=ruralWorksheetLandMatrixEstimatedRateColumn]');
	},
	get ruralWorksheetLandMatrixEstimatedValueColumn() {
		return cy.get('div[data-cy=ruralWorksheetLandMatrixEstimatedValueColumn]');
	},
	get ruralWorksheetLandMatrixContourNames() {
		return cy.get('input[data-cy=ruralWorksheetLandMatrixContourName]');
	},
	get ruralWorksheetLandMatrixCoverNames() {
		return cy.get('input[data-cy=ruralWorksheetLandMatrixCoverName]');
	},
	get ruralWorksheetLandMatrixQvCoverNames() {
		return cy.get('input[data-cy=ruralWorksheetLandMatrixQvCoverName]');
	},
	get ruralWorksheetLandMatrixAreas() {
		return cy.get('input[data-cy=ruralWorksheetLandMatrixArea]');
	},
	get ruralWorksheetLandMatrixEstimatedRates() {
		return cy.get('input[data-cy=ruralWorksheetLandMatrixEstimatedRate]');
	},
	get ruralWorksheetLandMatrixEstimatedValues() {
		return cy.get('input[data-cy=ruralWorksheetLandMatrixEstimatedValue]');
	},
	get ruralWorksheetLandMatrixTotalLV() {
		return cy.get('input[data-cy=ruralWorksheetLandMatrixTotalLV]');
	},
	get ruralWorksheetLandTotalLV() {
		return cy.get('input[data-cy=ruralWorksheetLandTotalLV]');
	},
}

function verifyLandMatrixElements(isCurrent) {
	// check elements present
	LandMatrix.ruralWorksheetLandMatrixSection.should('exist');
	LandMatrix.ruralWorksheetLandMatrixExpander.should('exist');
	if (!isCurrent) {
        LandMatrix.ruralWorksheetLandMatrixExpander.should('not.have.class', 'down');
        LandMatrix.ruralWorksheetLandMatrixExpander.click();
	}

    LandMatrix.ruralWorksheetLandMatrixExpander.should('have.class', 'down');
    if (!isCurrent) {
        LandMatrix.ruralWorksheetLandMatrixRecalculateLandMatrixButton.should('not.exist');
        LandMatrix.ruralWorksheetLandMatrixCopyLandMatrixButton.should('not.exist');
    }
	LandMatrix.ruralWorksheetMatchesLandMatrixIndicator.should('exist');
	LandMatrix.ruralWorksheetLandMatrixContourColumn.should('exist');
	LandMatrix.ruralWorksheetLandMatrixLandCoverColumn.should('exist');
	LandMatrix.ruralWorksheetLandMatrixQvLandCoverColumn.should('exist');
	LandMatrix.ruralWorksheetLandMatrixAreaColumn.should('exist');
	LandMatrix.ruralWorksheetLandMatrixEstimatedRateColumn.should('exist');
	LandMatrix.ruralWorksheetLandMatrixEstimatedValueColumn.should('exist');

	if (LandMatrix.ruralWorksheetLandMatrixCoverNames && LandMatrix.ruralWorksheetLandMatrixCoverNames.length > 0) {
		for (let i = 0; i < LandMatrix.ruralWorksheetLandMatrixCoverNames.length; i++) {
			LandMatrix.ruralWorksheetLandMatrixContourNames[i].invoke('attr', 'type').should('equal','text');
			LandMatrix.ruralWorksheetLandMatrixCoverNames[i].invoke('attr', 'type').should('equal','text');
			LandMatrix.ruralWorksheetLandMatrixQvCoverNames[i].invoke('attr', 'type').should('equal','text');
			LandMatrix.ruralWorksheetLandMatrixAreas[i].invoke('attr', 'type').should('equal','text');
			LandMatrix.ruralWorksheetLandMatrixEstimatedRates[i].invoke('attr', 'type').should('equal','text');
			LandMatrix.ruralWorksheetLandMatrixEstimatedValues[i].invoke('attr', 'type').should('equal','text');

			LandMatrix.ruralWorksheetLandMatrixContourNames[i].should('have.attr', 'disabled', 'disabled');
			LandMatrix.ruralWorksheetLandMatrixCoverNames[i].should('have.attr', 'disabled', 'disabled');
			LandMatrix.ruralWorksheetLandMatrixQvCoverNames[i].should('have.attr', 'disabled', 'disabled');
			LandMatrix.ruralWorksheetLandMatrixAreas[i].should('have.attr', 'disabled', 'disabled');
			LandMatrix.ruralWorksheetLandMatrixEstimatedRates[i].should('not.have.attr', 'disabled', 'disabled');
			LandMatrix.ruralWorksheetLandMatrixEstimatedValues[i].should('not.have.attr', 'disabled', 'disabled');
		}
	}
	LandMatrix.ruralWorksheetLandMatrixTotalLV.should('exist');
	LandMatrix.ruralWorksheetLandMatrixTotalLV.invoke('attr', 'type').should('equal','text');
	LandMatrix.ruralWorksheetLandMatrixTotalLV.should('have.attr', 'disabled', 'disabled');

	LandMatrix.ruralWorksheetLandTotalLV.should('exist');
	LandMatrix.ruralWorksheetLandMatrixTotalLV.invoke('attr', 'type').should('equal','text');
	LandMatrix.ruralWorksheetLandMatrixTotalLV.should('have.attr', 'disabled', 'disabled');

}

export {
    LandMatrix as default,
    verifyLandMatrixElements,
};

export default {
    get navBar() {
        return cy.get('ul[data-cy=mainNavigation]');
    },
    get homeLink() {
        return this.navBar.then(mainNav => {
            const homeLinks = mainNav.find('li[data-cy=mainNavigationHomeLink]');
            if (homeLinks && homeLinks.length > 0) {
                return homeLinks[0];
            }
            return null;
        });
    },
    get rollMaintenanceLink() {
        return this.navBar.then(mainNav => {
            const rollMaintenanceLinks = mainNav.find('li[data-cy=mainNavigationRollMaintenanceLink]');
            if (rollMaintenanceLinks && rollMaintenanceLinks.length > 0) {
                return rollMaintenanceLinks[0];
            }
            return null;
        });
    },
    get cloudUploaderLink() {
        return this.navBar.then(mainNav => {
            const cloudUploaderLinks = mainNav.find('li[data-cy=mainNavigationCloudUploaderLink]');
            if (cloudUploaderLinks && cloudUploaderLinks.length > 0) {
                return cloudUploaderLinks[0];
            }
            return null;
        });
    },
    get qivsAppLink() {
        return this.navBar.then(mainNav => {
            const qivsAppLinks = mainNav.find('li[data-cy=mainNavigationQivsAppLink]');
            if (qivsAppLinks && qivsAppLinks.length > 0) {
                return qivsAppLinks[0];
            }
            return null;
        });
    },
    get reportsLink() {
        return this.navBar.then(mainNav => {
            const reportLinks = mainNav.find('li[data-cy=mainNavigationReportsLink]');
            if (reportLinks && reportLinks.length > 0) {
                return reportLinks[0];
            }
            return null;
        });
    },
    get picklistsLink() {
        return this.navBar.then(mainNav => {
            const picklistsLinks = mainNav.find('li[data-cy=mainNavigationPicklistsLink]');
            if (picklistsLinks && picklistsLinks.length > 0) {
                return picklistsLinks[0];
            }
            return null;
        });
    },
    get userMaintenanceLink() {
        return this.navBar.then(mainNav => {
            const userMaintenanceLinks = mainNav.find('li[data-cy=mainNavigationUserMaintenanceLink]');
            if (userMaintenanceLinks && userMaintenanceLinks.length > 0) {
                return userMaintenanceLinks[0];
            }
            return null;
        });
    },
    get rtvLink() {
        return this.navBar.then(mainNav => {
            const rtvLinks = mainNav.find('li[data-cy=mainNavigationRtvLink]');
            if (rtvLinks && rtvLinks.length > 0) {
                return rtvLinks[0];
            }
            return null;
        });
    },
    get logoutLink() {
        return this.navBar.then(mainNav => {
            const logoutLinks = mainNav.find('li[data-cy=mainNavigationLogoutLink]');
            if (logoutLinks && logoutLinks.length > 0) {
                return logoutLinks[0];
            }
            return null;
        });
    },
    get salesProcessingLink() {
        return this.navBar.then(mainNav => {
            const salesProcessingLink = mainNav.find('li[data-cy=mainNavigationSalesProcessingLink]');
            if (salesProcessingLink && salesProcessingLink.length > 0) {
                return salesProcessingLink[0];
            }
            return null;
        });
    },
};

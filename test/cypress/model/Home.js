export default {
    href: '/',

    get toolbar() {
        return cy.get('ul.toolbar');
    },

    get homeButton() {
        return this.toolbar.find('li[title="Home"]');
    },

    get viewDashboardSelect() {
        return cy.get(
            '#viewDashboardDiv > .multiselect-native-select > .btn-group > .multiselect > .multiselect-selected-text'
        );
    },

    get rollMaintenanceButton() {
        return this.toolbar.find('li.roll-maintenance a');
        // return cy.get('.roll-maintenance > a > label')
    },

    get cloudUploaderButton() {
        return this.toolbar.find('a[href="/qv-cloud-uploader"]');
    },

    get picklistsButton() {
        return this.toolbar.contains('label', 'Picklists');
    },

    get userMaintenanceButton() {
        return this.toolbar.contains('label', 'User Maintenance');
    },

    get logoutButton() {
        return this.toolbar.find('li[title="Logout"]');
    },

    get logoutLink() {
        return this.toolbar.find('li[title="Logout"] a');
    },

    get quickSearchBar() {
        return cy.get('#typeAhead[name="searchQuery"]');
    },

    get typeAheadResults() {
        return cy.get('ul.typeahead__list');
    },

    get advancedSearchButton() {
        return cy.get('[title="Advanced Search"]');
    },

    visit() {
        cy.visitWithLogin('');
    },

    search(searchTerm) {
        this.quickSearchBar.type(`${searchTerm}{enter}`);
    },

    selectValuer(firstName, lastName) {
        cy.get(`#valuer-user${firstName}\\ ${lastName}`).click({ force: true });
    }
};

export default {
    generalPropertyInfoLabels: [
        'Category',
        'Nature of Improvements',
        'Property Name',
        'Land Use',
        'TA Land Zone',
        'Effective Land Area, ha',
        'Land Area, ha',
        'Māori Land',
        'Plan ID',
        'Production'
    ],

    propertySummaryLabels: [
        'House Type',
        'Units of Use',
        'Effective Year Built',
        'Poor Fdn.',
        'Total Bedrms',
        'Total Bathrms',
        'Total Toilets',
        'Building Site Cover, m2',
        'Total Floor Area, m2',
        'Main Living Area, m2',
        'Total Living Area, m2',
        'Ldy/Wkshp',
        'Car Access',
        'Driveway',
        'Carparks'
    ],

    visit(qpid) {
        cy.visit(`/property/${qpid}/summary`)
    },

    get saleDetails() {
        return SaleDetails;
    },

    get qivsSales() {
        return cy.get('[data-cy="qivs-sale"]')
    },

    get qpid() {
        return cy.contains('li.md-qpid', 'QPID:').find('strong');
    },

    get addressLine1() {
        return cy.get('div.md-summaryAddress').find('h1');
    },

    get addressLine2() {
        return this.AddressLine1.find('span');
    },

    get ownerBlock() {
        return cy.get('#dragBlockOwnerOccupier');
    },

    get ownerHeaderRow() {
        return this.ownerBlock.find('ul.md-headerRow');
    },

    get valref() {
        return cy.contains('label', 'VAL REF:');
    },

    get valrefSelect() {
        return this.valref.next();
    },

    get viewMorePhotosButton() {
        return cy.contains('span', 'View More Photos');
    },

    get photoSelectSlider() {
        return cy.get('ul.slick-dots');
    },

    get valuationJobsButton() {
        return cy.contains('label.valuation', 'Valuation Jobs');
    },

    get valuationJobsList() {
        return cy.get('ul.valJobs-menu');
    },

    get photoUploaderButton() {
        return cy.get('[title="Upload Photos"]');
    },

    get webButton() {
        return cy.contains('label', 'WEB');
    },

    get toolbarLeft() {
        return cy.get('ul.qvToolbar-links.lefty');
    },

    get monarchDetails() {
        return cy.get('div.monarchdetails');
    },

    get propertySummaryTab() {
        return cy.get('[data-cy=propertySummaryTab]');
    },

    get propertySummaryHeader() {
        return cy.get('[data-cy=property-summary-header]');
    },

    valrefSelectOption(valref) {
        return this.valrefSelect.contains(valref);
    },

    get propertyInformationLeftMenu() {
        return cy.get('div.md-propertyInfo-table');
    },
    get getSraValuationMenuAction() {
        return cy.get('td.sraValuationMenu');
    },
    get getSraValuationMenuText() {
        return cy.get('td.sraValuationMenuText');
    },
    get sraRowOneAreaToUpdate() {
        return cy.get('[data-cy=sraArea-sra-1]');
    },

    get sraRowAddSraDropDown() {
        return cy.get('[data-cy=sraAddSelect]');
    },
    get sraRowAddAreaInput() {
        return cy.get('[data-cy=sraArea-add]');
    },
    get sraRowAddCVInput() {
        return cy.get('[data-cy=sraCapitalValue-add]');
    },
    get sraRowAddLVInput() {
        return cy.get('[data-cy=sraLandValue-add]');
    },
    get sraRowAddIVInput() {
        return cy.get('[data-cy=sraImprovementsValue-add]');
    },
    get sraRowAddRevisionCVInput() {
        return cy.get('[data-cy=sraCapitalValue-add-revised]');
    },
    get sraRowAddRevisionLVInput() {
        return cy.get('[data-cy=sraLandValue-add-revised]');
    },
    get sraRowAddRevisionIVInput() {
        return cy.get('[data-cy=sraImprovementsValue-add-revised]');
    },
    get sraRowAddButton() {
        return cy.get('.sraAddAction');
    },
    get sraErrorMeessages() {
        return cy.get('.sra-error-messages');
    },
    get sraOutPutCode() {
        return cy.get('[data-cy=sraOutPutCode]');
    },
    get sraReasonSource() {
        return cy.get('[data-cy=sraReasonSource]');
    },
    get sraReasonForChange() {
        return cy.get('[data-cy=sraReasonForChange]');
    },
    get updateSraData() {
        return cy.get('[data-cy=updateSraData]');
    },
    get cancelSraData() {
        return cy.get('[data-cy=cancelSraData]');
    },
    get sraValuationSection() {
        return cy.get('.update-sra-values-section');
    },
    get sraRowUpdateTwo() {
        return cy.get('[data-cy=sraArea-sra-2]');
    },
    get sraRowDeleteZero() {
        return cy.get('[data-cy=removeSraRow-sra-0]');
    },
    get sraAlertModel() {
        return cy.get('.alertWrapper');
    },
    get alertModalCancelButton() {
        return cy.get('[data-cy=alertModalCancelButton]');
    },
    get alertModalOkButton() {
        return cy.get('[data-cy=alertModalOkButton]');
    },
    get jobStatusBoard() {
        return cy.get('[data-cy="job-status-board"]');
    },
    get iconLegend() {
        return cy.get('[data-cy="icon-legend"]');
    },
    get jobSetUpExpandAll() {
        return cy.get('[data-cy="job-setup-expand-all"]');
    },
    get propertyDetailsExpandAll() {
        return cy.get('[data-cy="property-details-expand-all"]');
    },
    get valuationExpandAll() {
        return cy.get('[data-cy="valuation-expand-all"]');
    },
    get reportDetailsExpandAll() {
        return cy.get('[data-cy="report-details-expand-all"]');
    },
    get locationDetailsExpandAll() {
        return cy.get('[data-cy="location-details-expand-all"]');
    },
    get qaAndReviewExpandAll() {
        return cy.get('[data-cy="qaandreview-expand-all"]');
    },
    get commercialWorksheetLink() {
        return cy.get('[data-cy=singleCommercialWorksheetHeader]');
    },
    get createCommercialWorksheetLink() {
        return cy.get('[data-cy=createCommercialWorksheetLink]');
    }
};


const SaleDetails = {
    elements: {
        get container() {
            return cy.get('[data-cy="sale-details"]');
        },
        get saleRows() {
            return this.container.find('[data-cy="qivs-sale"]');
        },
        get addSaleButton() {
            return this.container.find('[data-cy="button-add-sale"]');
        },
    },
    clickAddSale() {
        this.elements.addSaleButton.invoke('prop', 'dataset').then((dataset) => {
            const url = dataset.url;
            cy.visit(url);
        });
    },
    clickSaleLink(index = 0) {
        this.elements.saleRows
            .each((element, i, list) => {
                if (i === index) {
                    cy.wrap(element)
                        .find('a')
                        .then((linkElement) => {
                            expect(linkElement).to.have.attr('target', '_blank');
                            expect(linkElement).to.have.attr('href');
                            cy.visit(linkElement.attr('href'));
                        });
                }
            });
    },
    hasVendor(vendor) {
        this.elements.saleRows.find('.vendorCell').contains(vendor);
    }
}

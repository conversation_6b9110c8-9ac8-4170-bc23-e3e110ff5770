
export default {

    get singleRuralWorksheetHeader() {
        return cy.get('[data-cy=singleRuralWorksheetHeader]');
    },

    get singleRuralWorksheetValue() {
        return cy.get('[data-cy=singleRuralWorksheetYn]');
    },

    get singleCommercialWorksheetHeader() {
        return cy.get('[data-cy=singleCommercialWorksheetHeader]');
    },

    get singleCommercialWorksheetValue() {
        return cy.get('[data-cy=singleCommercialWorksheetYn]');
    },

    get bothWorksheetRuralHeader() {
        return cy.get('[data-cy=ruralWorksheetHeader]');
    },

    get bothWorksheetRuralValue() {
        return cy.get('[data-cy=ruralWorksheetYn]');
    },

    get bothWorksheetCommercialHeader() {
        return cy.get('[data-cy=commercialWorksheetHeader]');
    },

    get bothWorksheetCommercialValue() {
        return cy.get('[data-cy=commercialWorksheetYn]');
    },

    get createRuralWorksheetLink() {
        return cy.get('[data-cy=createRuralWorksheetLink]');
    },

    get createCommercialWorksheetLink() {
        return cy.get('[data-cy=createCommercialWorksheetLink]');
    },

    get propertyInfoHeader(){
        return cy.get('[data-cy=propertyInfoHeader]')
    },

    get consentsLink() {
        return cy.get('[data-cy=consentsLink]');
    },

    get consentsValue() {
        return cy.get('[data-cy=consentsYn]');
    },

    get floorPlansLink() {
        return cy.get('[data-cy=floorPlansLink]');
    },

    get floorPlansValue() {
        return cy.get('[data-cy=floorPlansYn]');
    },

    get subdivisionsLink() {
        return cy.get('[data-cy=subdivisionsLink]');
    },

    get subdivisionsValue() {
        return cy.get('[data-cy=subdivisionsYn]');
    },

    get surveyPlansLink() {
        return cy.get('[data-cy=surveyPlansLink]');
    },

    get surveyPlansValue() {
        return cy.get('[data-cy=surveyPlansYn]');
    },

    get objectionsLink() {
        return cy.get('[data-cy=objectionsLink]');
    },

    get objectionsValue() {
        return cy.get('[data-cy=objectionsYn]');
    },

    get sitePlansLink() {
        return cy.get('[data-cy=sitePlansLink]');
    },

    get sitePlansValue() {
        return cy.get('[data-cy=sitePlansYn]');
    },

    get valuationDataLink() {
        return cy.get('[data-cy=valuationDataLink]');
    },

    get valuationDataValue() {
        return cy.get('[data-cy=valuationDataYn]');
    },

    get attachmentsLink() {
        return cy.get('[data-cy=attachmentsLink]');
    },

    get attachmentsValue() {
        return cy.get('[data-cy=attachmentsYn]');
    },

    get sraValuesLink() {
        return cy.get('[data-cy=sraValuesLink]');
    },

    get sraValuesValue() {
        return cy.get('[data-cy=sraValuesYn]');
    },

    get propertyFileHeader() {
        return cy.get('[data-cy=propertyFileHeader]');
    },

    get propertyFileSwitch() {
        return cy.get('[data-cy=propertyFileSwitch] > .vue-js-switch > .v-switch-core');
    },

    get autoMasHeader() {
        return cy.get('[data-cy=autoMasHeader]');
    },

    get autoMasSwitch() {
        return cy.get('[data-cy=autoMasSwitch] > .vue-js-switch > .v-switch-core')
    },

    get suspectValuationHeader() {
        return cy.get('[data-cy=suspectValuationHeader]');
    },

    get suspectValuationSwitch() {
        return cy.get('[data-cy=suspectValuationSwitch] > .vue-js-switch > .v-switch-core');
    },

    get autoWorksheetHeader() {
        return cy.get('[data-cy=autoWorksheetHeader]');
    },

    get autoWorksheetSwitch() {
        return cy.get('[data-cy=autoWorksheetSwitch] > .vue-js-switch > .v-switch-core')
    },

    get openModalButton() {
        return cy.get('[data-cy=openModalButton]');
    },

    get readOnlyNotesArea() {
        return cy.get('[data-cy=readOnlyNotesArea]');
    },

    get alertModal() {
        return cy.get('[data-cy=alertModal]');
    },

    get modalTitle() {
        return cy.get('[data-cy=modalTitle]');
    },

    get notesInput() {
        return cy.get('[data-cy=notesInput]');
    },

    get cancelButton() {
        return cy.get('[data-cy=cancelButton]');
    },

    get confirmButton() {
        return cy.get('[data-cy=confirmButton]');
    }

}

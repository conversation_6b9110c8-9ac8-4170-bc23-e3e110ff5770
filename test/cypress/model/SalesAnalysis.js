/*global cy it describe context */

export default {
    elements: {
        get salesAnalysisPropertyPageLink() {
            return cy.get(':nth-child(6) > [data-cy="sales-analysis-link"] > a');
        },

        header: {
            get salesAnalysisHeader() {
                return cy.get('[data-cy="sales-analysis-header"]');
            },

            get verifyAndCloseButton() {
                return cy.get('#save-and-close');
            },
        },

        resultsRow: {
            get fullAddress() {
                return cy.get('.fullAddress');
            },

            addressLine(index) {
                return cy.get(`.fullAddress > :nth-child(${index})`)
            },

            get lastRevalDate() {
                return cy.get('.lastReval-date > li');
            },

            get analysedBy() {
                return cy.get('.sa-analysedBy');
            },

            get analysedDate() {
                return cy.get('.sa-analysedDate');
            },

            get trafficLights() {
                return cy.get('.sales-trafficLights');
            },

            get vendorPurchaser() {
                return cy.get('.vendPurchase > li');
            },

            get saleId() {
                return cy.get('.saleId');
            },

            get capitalValue() {
                return cy.get('#saleCapitalValueDiv');
            },

            get capitalValuePerM2() {
                return cy.get('.cvnr');
            },

            get landValue() {
                return cy.get('#saleLandValueDiv');
            },

            get landValuePerM2() {
                return cy.get('.lvnr');
            },

            get valueOfImprovements() {
                return cy.get('#saleValueOfImprovementsDiv');
            },

            get valueOfImprovementsPerM2() {
                return cy.get('.vinr');
            },

            get landArea() {
                return cy.get('#saleLandAreaDiv');
            },

            get tfa() {
                return cy.get('#saleTFASpan');
            },

            get tla() {
                return cy.get('#saleTLASpan');
            },

            get grossSalePrice() {
                return cy.get('#saleGrossPriceDiv');
            },

            get netSalePrice() {
                return cy.get('#saleNetPriceDiv');
            },

            get chattels() {
                return cy.get('#saleChattelSDiv');
            },

            get gst() {
                return cy.get('#saleGstDiv');
            },

            get other() {
                return cy.get('#saleOtherDiv');
            },
        },

        saleAnalysisDetails: {
            get saleAnalysisDetailsWrapper() {
                return cy.get('.sa-details');
            },

            get saleNetPrice() {
                return cy.get('#saleNetPrice');
            },

            get analysedLV() {
                return cy.get('#analysisAnalysedLV');
            },

            get analysedVI() {
                return cy.get('#analysisAnalysedVI');
            },

            get nspTFA() {
                return cy.get('#saleNspTfa');
            },

            get nspTLA() {
                return cy.get('#saleNspTla');
            },

            get analysedLNR() {
                return cy.get('#analysisAnalysedLNR');
            },

            get nspCV() {
                return cy.get('#saleNspCV');
            },

            get alvLV() {
                return cy.get('#analysisAlvLv');
            },

            get aviVI() {
                return cy.get('#analysisAviVi');
            },

            get qvHPI() {
                return cy.get('#analysisHousePriceIndex');
            },

            get qvLVI() {
                return cy.get('#analysisLandValueIndex');
            },
        },

        summaryAndComment: {
            get propertySummary() {
                return cy.get('.sa-summary > textarea');
            },

            get salesAnalysisComment() {
                return cy.get('.sa-salesComment > textarea');
            },
        },

        get salesAnalysisComment() {
            return cy.get('.sa-salesComment > textarea');
        },
    },

    visitSaleAnalysisPage(id) {
        cy.visit(`?saleId=${id}`);
    },

    // Assert Elements Exist

    checkSalesAnalysis() {
        context('Checks every field exists in Sales Analysis', () => {
            context('Header', () => {
                it('Sales Analysis header should exist and be visible', () => {
                    this.elements.header.salesAnalysisHeader
                        .should('exist')
                        .and('be.visible');
                });

                it('Verify and Close should exist and be visible', () => {
                    this.elements.header.verifyAndCloseButton
                        .should('exist')
                        .and('be.visible');
                });
            });

            context('Results Row', () => {
                it('Full Address should exist and be visible', () => {
                    this.elements.resultsRow.fullAddress
                        .should('exist')
                        .and('be.visible');
                });

                it('Address Line 1 should exist and be visible', () => {
                    this.elements.resultsRow.addressLine(1)
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Address Line 2 should exist and be visible', () => {
                    this.elements.resultsRow.addressLine(2)
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Last Reval Date should exist and be visible, have value', () => {
                    this.elements.resultsRow.lastRevalDate
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Analysed By text should exist and be visible', () => {
                    this.elements.resultsRow.analysedBy
                        .should('exist', 'Analysed by text should exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Analysed Date text should exist and be visible', () => {
                    this.elements.resultsRow.analysedDate
                        .should('exist', 'Analysed date text should exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Traffic Lights should exist and be visible', () => {
                    this.elements.resultsRow.trafficLights
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Vendor/Purchaser should exist and be visible', () => {
                    this.elements.resultsRow.vendorPurchaser
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Sale ID should exist and be visible', () => {
                    this.elements.resultsRow.saleId
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Capital Value should exist and be visible', () => {
                    this.elements.resultsRow.capitalValue
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Capital Value /m ^2 should exist and be visible', () => {
                    this.elements.resultsRow.capitalValuePerM2
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Land Value should exist and be visible', () => {
                    this.elements.resultsRow.landValue
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Land Value /m ^2 should exist and be visible', () => {
                    this.elements.resultsRow.landValuePerM2
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Value Of Improvements should exist and be visible', () => {
                    this.elements.resultsRow.valueOfImprovements
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Value Of Improvements /m ^2 should exist and be visible', () => {
                    this.elements.resultsRow.valueOfImprovementsPerM2
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Land Area should exist and be visible', () => {
                    this.elements.resultsRow.landArea
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('TFA should exist and be visible', () => {
                    this.elements.resultsRow.tfa
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('TLA should exist and be visible', () => {
                    this.elements.resultsRow.tla
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Gross Sale Price should exist and be visible', () => {
                    this.elements.resultsRow.grossSalePrice
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Net Sale Price should exist and be visible', () => {
                    this.elements.resultsRow.netSalePrice
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Chattels should exist and be visible', () => {
                    this.elements.resultsRow.chattels
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('GST should exist and be visible', () => {
                    this.elements.resultsRow.gst
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Other should exist and be visible', () => {
                    this.elements.resultsRow.other
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });
            });

            context('Sale Analysis Details', () => {
                it('Sale Analysis Details section should exist and be visible', () => {
                    this.elements.saleAnalysisDetails.saleAnalysisDetailsWrapper
                        .should('exist')
                        .and('be.visible');
                });

                it('Sale Net Price should exist and be visible and value should be a currency', () => {
                    this.elements.saleAnalysisDetails.saleNetPrice
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isCurrency(val)).to.equal(true, `Value '${val}' should be a currency`);
                        });
                });

                it('Analysed LV should exist and be visible and value should be a currency', () => {
                    this.elements.saleAnalysisDetails.analysedLV
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isCurrency(val)).to.equal(true, `Value '${val}' should be a currency`);
                        });
                });

                it('Analysed VI should exist and be visible and value should be a currency', () => {
                    this.elements.saleAnalysisDetails.analysedVI
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isCurrency(val)).to.equal(true, `Value '${val}' should be a currency`);
                        });
                });

                it('NSP/TFA should exist and be visible and value should be a currency', () => {
                    this.elements.saleAnalysisDetails.nspTFA
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isCurrency(val)).to.equal(true, `Value '${val}' should be a currency`);
                        });
                });

                it('NSP/TLA should exist and be visible and value should be a currency', () => {
                    this.elements.saleAnalysisDetails.nspTLA
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isCurrency(val)).to.equal(true, `Value '${val}' should be a currency`);
                        });
                });

                it('AnalysedLNR should exist and be visible and value should be a currency', () => {
                    this.elements.saleAnalysisDetails.analysedLNR
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isCurrency(val)).to.equal(true, `Value '${val}' should be a currency`);
                        });
                });

                it('NSP/CV should exist and be visible and value matches decimal places', () => {
                    this.elements.saleAnalysisDetails.nspCV
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isValidDecimal(val)).to.equal(true, `Value '${val}' should match decimal places`);
                        });
                });

                it('ALV/LV should exist and be visible and value matches decimal places', () => {
                    this.elements.saleAnalysisDetails.alvLV
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isValidDecimal(val)).to.equal(true, `Value '${val}' should match decimal places`);
                        });
                });

                it('AVI/VI should exist and be visible and value matches decimal places', () => {
                    this.elements.saleAnalysisDetails.aviVI
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isValidDecimal(val)).to.equal(true, `Value '${val}' should match decimal places`);
                        });
                });

                it('QV HPI should exist and be visible and value matches decimal places', () => {
                    this.elements.saleAnalysisDetails.qvHPI
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isValidDecimal(val)).to.equal(true, `Value '${val}' should match decimal places`);
                        });
                });

                it('QV LVI should exist and be visible and value matches decimal places', () => {
                    this.elements.saleAnalysisDetails.qvLVI
                        .should('exist')
                        .and('be.visible')
                        .find('input')
                        .invoke('val')
                        .then((val) => {
                            expect(isValidDecimal(val)).to.equal(true, `Value '${val}' should match decimal places`);
                        });
                });
            });

            context('Summary and Comment', () => {
                it('Property Summary should exist and be visible', () => {
                    this.elements.summaryAndComment.propertySummary
                        .should('exist')
                        .and('be.visible')
                        .type('{rightarrow}1234')
                        .invoke('val')
                        .should('be.ok');
                });

                it('Sales Analysis Comment should exist and be visible', () => {
                    this.elements.summaryAndComment.salesAnalysisComment
                        .should('exist')
                        .and('be.visible')
                        .clear()
                        .type('{rightarrow}1234')
                        .invoke('val')
                        .should('be.ok');
                });
            });

            context('Residual Land Analysis', () => {
                context('Expands section', () => {
                    it('Residual Land Analysis Div should exist and be visible, subsections not existing', () => {
                        cy.get('#saResidualLandCalculationsDiv')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#rsaMainBuildingsTable').should('not.exist');

                        cy.get('#rsaOtherBuildingsTable').should('not.exist');

                        cy.get('#rsaOtherImprovementsTable').should('not.exist');

                        cy.get('#rsaLandTable').should('not.exist');
                    });

                    it('Clicks check', () => {
                        cy.get('#Residual\\ Land\\ Analysis').click();

                        cy.get('#rsaMainBuildingsTable')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#rsaOtherBuildingsTable')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#rsaOtherImprovementsTable')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#rsaLandTable')
                            .should('exist')
                            .and('be.visible');
                    });
                });

                context('Main Buildings Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#rsaMainBuildingsTable > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Main Buildings');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get(
                            '#rsaMainBuildingsTable > :nth-child(3) > .sa-description > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get('#rsaMainBuildingsTable > :nth-child(3) > .sa-area > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get('#rsaMainBuildingsTable > :nth-child(3) > .sa-rate > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get('#rsaMainBuildingsTable > :nth-child(3) > .sa-value > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get('#rsaMainBuildingsTable > :nth-child(3) > .sa-runnintgTotal > span')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get(
                                    '#rsaMainBuildingsTable > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });
                });

                context('Other Buildings Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#rsaOtherBuildingsTable > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Other Buildings');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get(
                            '#rsaOtherBuildingsTable > :nth-child(3) > .sa-description > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get('#rsaOtherBuildingsTable > :nth-child(3) > .sa-area > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get('#rsaOtherBuildingsTable > :nth-child(3) > .sa-rate > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get('#rsaOtherBuildingsTable > :nth-child(3) > .sa-value > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get('#rsaOtherBuildingsTable > :nth-child(3) > .sa-runnintgTotal > span')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get(
                                    '#rsaOtherBuildingsTable > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });

                    it('Adds row to Other Buildings', () => {
                        cy.get(
                            '#rsaOtherBuildingsTable > :nth-child(3) > .sa-addRemove > .saRow-add'
                        )
                            .click()
                            .then(() => {
                                cy.get('#rsaOtherBuildingsTable > :nth-child(4)')
                                    .should('exist')
                                    .and('be.visible');
                                cy.get(':nth-child(4) > .sa-runnintgTotal > span')
                                    .invoke('text')
                                    .should('be.ok');
                            });
                    });

                    it('Removes row from Other Buildings', () => {
                        cy.get(
                            '#rsaOtherBuildingsTable > :nth-child(3) > .sa-addRemove > .saRow-remove'
                        ).should('not.exist');
                        cy.get(
                            '#rsaOtherBuildingsTable > :nth-child(4) > .sa-addRemove > .saRow-remove'
                        )
                            .click()
                            .then(() => {
                                cy.get('#rsaOtherBuildingsTable > :nth-child(4)').should(
                                    'not.exist'
                                );
                            });
                    });
                });

                context('Other Improvements Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#rsaOtherImprovementsTable > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Other Improvements');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get(
                            '#rsaOtherImprovementsTable > :nth-child(3) > .sa-description > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get(
                            '#rsaOtherImprovementsTable > :nth-child(3) > .sa-area > span > input'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get(
                            '#rsaOtherImprovementsTable > :nth-child(3) > .sa-rate > span > input'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get(
                            '#rsaOtherImprovementsTable > :nth-child(3) > .sa-value > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get(
                            '#rsaOtherImprovementsTable > :nth-child(3) > .sa-runnintgTotal > span'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get(
                                    '#rsaOtherImprovementsTable > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });

                    it('Adds row to Other Improvements', () => {
                        cy.get(
                            '#rsaOtherImprovementsTable > :nth-child(3) > .sa-addRemove > .saRow-add'
                        )
                            .click()
                            .then(() => {
                                cy.get('#rsaOtherImprovementsTable > :nth-child(4)')
                                    .should('exist')
                                    .and('be.visible');

                                cy.get(':nth-child(4) > .sa-runnintgTotal > span')
                                    .invoke('text')
                                    .should('be.ok');
                            });
                    });

                    it('Removes row from Other Improvements', () => {
                        cy.get(
                            '#rsaOtherImprovementsTable > :nth-child(3) > .sa-addRemove > .saRow-remove'
                        ).should('not.exist');
                        cy.get(
                            '#rsaOtherImprovementsTable > :nth-child(4) > .sa-addRemove > .saRow-remove'
                        )
                            .click()
                            .then(() => {
                                cy.get('#rsaOtherImprovementsTable > :nth-child(4)').should(
                                    'not.exist'
                                );
                            });
                    });
                });

                context('Land Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#rsaLandTable > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Land');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get('#rsaLandTable > :nth-child(3) > .sa-description > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get('#rsaLandTable > :nth-child(3) > .sa-area > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get('#rsaLandTable > :nth-child(3) > .sa-rate > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get('#rsaLandTable > :nth-child(3) > .sa-value > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get('#rsaLandTable > :nth-child(3) > .sa-runnintgTotal > span')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get('#rsaLandTable > :nth-child(3) > .sa-value > span > input')
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });
                });

                context('Hides section', () => {
                    it('Clicks check', () => {
                        cy.get('#Residual\\ Land\\ Analysis').click();

                        cy.get('#rsaMainBuildingsTable').should('not.exist');

                        cy.get('#rsaOtherBuildingsTable').should('not.exist');

                        cy.get('#rsaOtherImprovementsTable').should('not.exist');

                        cy.get('#rsaLandTable').should('not.exist');
                    });
                });
            });

            context('Net Rate Calculations', () => {
                context('Initialize Section', () => {
                    it('Net Rate Calculations Div should exist and be visible without having to expand', () => {
                        cy.get('#saLandTableDiv')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#saMainBuildingsTableDiv')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#saOtherBuildingsTableDiv')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#saOtherImprovementsTableDiv')
                            .should('exist')
                            .and('be.visible');
                    });
                });

                context('Land Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#saLandTableDiv > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Land');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get('#saLandTableDiv > :nth-child(3) > .sa-description > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get('#saLandTableDiv > :nth-child(3) > .sa-area > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get('#saLandTableDiv > :nth-child(3) > .sa-rate > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get('#saLandTableDiv > :nth-child(3) > .sa-value > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get('#saLandTableDiv > :nth-child(3) > .sa-runnintgTotal > span')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get('#saLandTableDiv > :nth-child(3) > .sa-value > span > input')
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });
                });

                context('Other Improvements Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#saOtherImprovementsTableDiv > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Other Improvements');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get(
                            '#saOtherImprovementsTableDiv > :nth-child(3) > .sa-description > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get(
                            '#saOtherImprovementsTableDiv > :nth-child(3) > .sa-area > span > input'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get(
                            '#saOtherImprovementsTableDiv > :nth-child(3) > .sa-rate > span > input'
                        )
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get(
                            '#saOtherImprovementsTableDiv > :nth-child(3) > .sa-value > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get(
                            '#saOtherImprovementsTableDiv > :nth-child(3) > .sa-runnintgTotal > span'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get(
                                    '#saOtherImprovementsTableDiv > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });

                    it('Adds row to Other Improvements', () => {
                        cy.get(
                            '#saOtherImprovementsTableDiv > :nth-child(3) > .sa-addRemove > .saRow-add'
                        )
                            .click()
                            .then(() => {
                                cy.get('#saOtherImprovementsTableDiv > :nth-child(4)')
                                    .should('exist')
                                    .and('be.visible');

                                cy.get(':nth-child(4) > .sa-runnintgTotal > span')
                                    .invoke('text')
                                    .should('be.ok');
                            });
                    });

                    it('Removes row from Other Improvements', () => {
                        cy.get(
                            '#saOtherImprovementsTableDiv > :nth-child(3) > .sa-addRemove > .saRow-remove'
                        ).should('not.exist');
                        cy.get(
                            '#saOtherImprovementsTableDiv > :nth-child(4) > .sa-addRemove > .saRow-remove'
                        )
                            .click()
                            .then(() => {
                                cy.get('#saOtherImprovementsTableDiv > :nth-child(4)').should(
                                    'not.exist'
                                );
                            });
                    });
                });

                context('Other Buildings Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#saOtherBuildingsTableDiv > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Other Buildings');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get(
                            '#saOtherBuildingsTableDiv > :nth-child(3) > .sa-description > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get(
                            '#saOtherBuildingsTableDiv > :nth-child(3) > .sa-area > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get(
                            '#saOtherBuildingsTableDiv > :nth-child(3) > .sa-rate > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get(
                            '#saOtherBuildingsTableDiv > :nth-child(3) > .sa-value > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get(
                            '#saOtherBuildingsTableDiv > :nth-child(3) > .sa-runnintgTotal > span'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get(
                                    '#saOtherBuildingsTableDiv > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });

                    it('Adds row to Other Buildings', () => {
                        cy.get(
                            '#saOtherBuildingsTableDiv > :nth-child(3) > .sa-addRemove > .saRow-add'
                        )
                            .click()
                            .then(() => {
                                cy.get('#saOtherBuildingsTableDiv > :nth-child(4)')
                                    .should('exist')
                                    .and('be.visible');
                                cy.get(':nth-child(4) > .sa-runnintgTotal > span')
                                    .invoke('text')
                                    .should('be.ok');
                            });
                    });

                    it('Removes row from Other Buildings', () => {
                        cy.get(
                            '#saOtherBuildingsTableDiv > :nth-child(3) > .sa-addRemove > .saRow-remove'
                        ).should('not.exist');
                        cy.get(
                            '#saOtherBuildingsTableDiv > :nth-child(4) > .sa-addRemove > .saRow-remove'
                        )
                            .click()
                            .then(() => {
                                cy.get('#saOtherBuildingsTableDiv > :nth-child(4)').should(
                                    'not.exist'
                                );
                            });
                    });
                });

                context('Main Buildings Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#saMainBuildingsTableDiv > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Main Buildings');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get(
                            '#saMainBuildingsTableDiv > :nth-child(3) > .sa-description > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get('#saMainBuildingsTableDiv > :nth-child(3) > .sa-area > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get('#saMainBuildingsTableDiv > :nth-child(3) > .sa-rate > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get(
                            '#saMainBuildingsTableDiv > :nth-child(3) > .sa-value > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get(
                            '#saMainBuildingsTableDiv > :nth-child(3) > .sa-runnintgTotal > span'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get(
                                    '#saMainBuildingsTableDiv > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });
                });

                context('Hides section', () => {
                    it('Clicks check', () => {
                        cy.get('#Net\\ Rate\\ Calculations').click();

                        cy.get('#saMainBuildingsTableDiv').should('not.exist');

                        cy.get('#saOtherBuildingsTableDiv').should('not.exist');

                        cy.get('#saOtherImprovementsTableDiv').should('not.exist');

                        cy.get('#saLandTableDiv').should('not.exist');
                    });
                });
            });

            context('Gross Rate Calculations', () => {
                context('Expand section', () => {
                    it('Gross Rate Calculations Div should exist and be visible upon click', () => {
                        cy.get('#Gross\\ Rate\\ Calculations').click();

                        cy.get('#SaleValueTableDiv')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#DeductionsTableDiv')
                            .should('exist')
                            .and('be.visible');

                        cy.get('#MainUnitTableDiv')
                            .should('exist')
                            .and('be.visible');
                    });
                });

                context('Sale Value Table', () => {
                    it('Header should not exist', () => {
                        cy.get('#SaleValueTableDiv > h3').should('not.exist');
                    });

                    it('Gross Sale Price should exist and be visible', () => {
                        cy.get('#SaleValueTableDiv > :nth-child(2) > .sa-area > span')
                            .should('exist')
                            .and('be.visible')
                            .children()
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Chattels should exist and be visible', () => {
                        cy.get('#SaleValueTableDiv > :nth-child(2) > .sa-rate > span')
                            .should('exist')
                            .and('be.visible')
                            .children()
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Net Sale Price should exist and be visible', () => {
                        cy.get('#SaleValueTableDiv > :nth-child(2) > .sa-value > span')
                            .should('exist')
                            .and('be.visible')
                            .children()
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Net Sale Price value', () => {
                        cy.get('#SaleValueTableDiv > :nth-child(2) > .sa-runnintgTotal > span')
                            .should('exist')
                            .and('be.visible')

                            .invoke('text')
                            .should('be.ok')
                            .then($text => {
                                cy.get('#SaleValueTableDiv > :nth-child(2) > .sa-value > span')
                                    .should('exist')
                                    .children()
                                    .invoke('val')
                                    .should('equal', $text);
                            });
                    });
                });

                context('Deductions Table', () => {
                    it('Header should exist and be visible', () => {
                        cy.get('#DeductionsTableDiv > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Deductions');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get(
                            '#DeductionsTableDiv > :nth-child(3) > .sa-description > span > input'
                        )
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get('#DeductionsTableDiv > :nth-child(3) > .sa-area > span > input')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Rate /m ^2 should exist and be visible', () => {
                        cy.get('#DeductionsTableDiv > :nth-child(3) > .sa-rate > span > input')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Analysed Value should exist and be visible', () => {
                        cy.get('#DeductionsTableDiv > :nth-child(3) > .sa-value > span > input')
                            .should('exist')
                            .and('be.visible');
                    });

                    // NOTE: 18/10/21: only place value is input in tests so far
                    it('Analysed Value input value', () => {
                        cy.get('#DeductionsTableDiv > :nth-child(3) > .sa-value > span > input')
                            .should('exist')
                            .and('be.visible')
                            .clear()
                            .type('1234')
                            .then(() => {
                                cy.get('.salesAnalysis-form').click();

                                cy.get(
                                    '#DeductionsTableDiv > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .should('be.ok')
                                    .and('equal', '$1,234');
                            });
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        // cy.get(
                        //     '#DeductionsTableDiv > :nth-child(3) > .sa-value > span > input'
                        // ).should('not.exist');

                        cy.get(':nth-child(4) > .sa-runnintgTotal > span')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get(
                                    '#DeductionsTableDiv > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .then($val2 => {
                                        expect(`-${$val2}`).to.equal($val); //.should('equal', `${$val}`);
                                    });
                            });
                    });

                    it('Adds row to Deductions', () => {
                        cy.get('#DeductionsTableDiv > :nth-child(4) > .sa-addRemove > .saRow-add')
                            .click()
                            .then(() => {
                                cy.get('#DeductionsTableDiv > :nth-child(5)')
                                    .should('exist')
                                    .and('be.visible');

                                cy.get(':nth-child(5) > .sa-runnintgTotal > span')
                                    .invoke('text')
                                    .should('be.ok');
                            });
                    });

                    it('Removes row from Deductions', () => {
                        cy.get(
                            '#DeductionsTableDiv > :nth-child(3) > .sa-addRemove > .saRow-remove'
                        ).should('not.exist');
                        cy.get(
                            '#DeductionsTableDiv > :nth-child(5) > .sa-addRemove > .saRow-remove'
                        )
                            .click()
                            .then(() => {
                                cy.get('#DeductionsTableDiv > :nth-child(5)').should('not.exist');
                            });
                    });
                });

                context('Main Unit Adjusted Gross Rate Table', () => {
                    it('Header should not exist', () => {
                        cy.get('#MainUnitTableDiv');
                        cy.get('#MainUnitTableDiv > h3')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Main Unit Adjusted Gross Rate');
                    });

                    it('Description should exist and be visible', () => {
                        cy.get('#MainUnitTableDiv > :nth-child(3) > .sa-description > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Area should exist and be visible', () => {
                        cy.get('#MainUnitTableDiv > :nth-child(3) > .sa-area > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Rate should exist and be visible', () => {
                        cy.get('#MainUnitTableDiv > :nth-child(3) > .sa-rate > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Value should exist and be visible', () => {
                        cy.get('#MainUnitTableDiv > :nth-child(3) > .sa-value > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Running Total should exist and be visible, and be the same as Analysed Value value', () => {
                        cy.get('#MainUnitTableDiv > :nth-child(3) > .sa-runnintgTotal > span')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .then($val => {
                                cy.get(
                                    '#MainUnitTableDiv > :nth-child(3) > .sa-value > span > input'
                                )
                                    .invoke('val')
                                    .should('equal', $val);
                            });
                    });
                });

                context('Hides section', () => {
                    it('Clicks check', () => {
                        cy.get('#Gross\\ Rate\\ Calculations').click();

                        cy.get('#SaleValueTableDiv').should('not.exist');

                        cy.get('#DeductionsTableDiv').should('not.exist');

                        cy.get('#MainUnitTableDiv').should('not.exist');
                    });
                });
            });

            context('Investment Method Calculation', () => {
                context('Expand section', () => {
                    it('Investment Method Calculation Div should exist and be visible upon click', () => {
                        cy.get('#Investment\\ Method\\ Calculation').click();

                        cy.get('#saInvestmentMethodCalculationDiv > .salesAnalysis-table')
                            .should('exist')
                            .and('be.visible');
                    });
                });

                context('Analyse Investment Method Calculation Table', () => {
                    it('Description should exist and be visible', () => {
                        cy.get('.advSearch-row > .sa-description > span')
                            .should('exist')
                            .and('be.visible')
                            .children()
                            .invoke('val')
                            .should('be.ok')
                            .and('equal', 'Gross Yields');
                    });

                    it('Analysed Rent (Weekly) should exist and be visible', () => {
                        cy.get('.sa-rentWeekly > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Analysed Rent (Annual) should exist and be visible', () => {
                        cy.get('.sa-rentAnnual > span > input')
                            .should('exist')
                            .and('be.visible')
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Net Sale should exist and be visible', () => {
                        cy.get('.sa-salePrice > :nth-child(1) > span')
                            .should('exist')
                            .and('be.visible')
                            .children()
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Gross Sale should exist and be visible', () => {
                        cy.get('.sa-salePrice > :nth-child(2) > span')
                            .should('exist')
                            .and('be.visible')
                            .children()
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Net Sale Price Gross Yield should exist and be visible', () => {
                        cy.get('.sa-runnintgTotal > :nth-child(1) > span')
                            .should('exist')
                            .and('be.visible')

                            .invoke('text')
                            .should('be.ok');
                    });

                    it('Gross Sale Price Gross Yield should exist and be visible', () => {
                        cy.get('.sa-runnintgTotal > :nth-child(2) > span')
                            .should('exist')
                            .and('be.visible')

                            .invoke('text')
                            .should('be.ok');
                    });
                });

                context('Hides section', () => {
                    it('Clicks check', () => {
                        cy.get('#Investment\\ Method\\ Calculation').click();

                        cy.get('#saInvestmentMethodCalculationDiv > .salesAnalysis-table').should(
                            'not.exist'
                        );
                    });
                });
            });

            context('Extra Property Investment Details', () => {
                context('Expands Section', () => {
                    it('Extra Property Investment Div should exist and be visible upon click', () => {
                        cy.get('#Investment\\ Details').click();
                        cy.get('.QVHV-formSection')
                            .should('exist')
                            .and('be.visible');
                    });
                });

                context('Analyse Extra Property Investment Table', () => {
                    it('Title should exist and be visible', () => {
                        cy.get('.investmentUnit')
                            .should('exist')
                            .and('be.visible')
                            .invoke('text')
                            .should('be.ok')
                            .and('equal', 'Total Property Rent');
                    });

                    it('Estimated ($ Per Week) should exist and be visible', () => {
                        cy.get('.fifteenPct.calculated > span')
                            .should('exist')
                            .and('be.visible')
                            .children()
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Rental Estimate Basis should exist and be visible', () => {
                        cy.get('.twentyfivePct > span')
                            .should('exist')
                            .and('be.visible')
                            .children()
                            .invoke('val')
                            .should('be.ok');
                    });

                    it('Actual ($ Per Week) should exist and be visible', () => {
                        cy.get('#weeklyRentalAmountPropPlus')
                            .should('exist')
                            .and('be.visible');
                    });

                    it('Known (MM/YYYY) should exist and be visible', () => {
                        cy.get('#knownDateAmountPropPlus')
                            .should('exist')
                            .and('be.visible');
                    });
                });

                context('Hides section', () => {
                    it('Clicks check', () => {
                        cy.get('#Investment\\ Details').click();
                        cy.get('.QVHV-formSection').should('not.exist');
                    });
                });
            });

            context.skip('Revision Value Analysis', () => {
                it('Revision Value Analysis table should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis')
                        .should('exist')
                        .and('be.visible');
                });

                it('Revision CV should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis > :nth-child(2) > :nth-child(1) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Revision LV should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis > :nth-child(3) > :nth-child(1) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Revision VI should exist and be visible', () => {
                    cy.get(':nth-child(4) > :nth-child(1) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Revision Gross Rate should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis > :nth-child(2) > :nth-child(2) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Revision Land Rate should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis > :nth-child(3) > :nth-child(2) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Auto Revision Net Rate should exist and be visible', () => {
                    cy.get(':nth-child(4) > :nth-child(2) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('RCV/NSP should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis > :nth-child(2) > :nth-child(3) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('RLV/ALV should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis > :nth-child(3) > :nth-child(3) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('RVI/AVI should exist and be visible', () => {
                    cy.get(':nth-child(4) > :nth-child(3) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('RCV/CV should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis > :nth-child(2) > :nth-child(4) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('RLV/LV should exist and be visible', () => {
                    cy.get('.sa-revisionAnalysis > :nth-child(3) > :nth-child(4) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('RVI/VI should exist and be visible', () => {
                    cy.get(':nth-child(4) > :nth-child(4) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });
            });

            context('Controls', () => {
                it('Controls should exist and be visible', () => {
                    cy.get('.saControls')
                        .should('exist')
                        .and('be.visible');
                });

                it('Net Sale Price should exist and be visible', () => {
                    cy.get('#saTotalsNetSalePriceDiv > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Current Difference should exist and be visible', () => {
                    cy.get('#saTotalsCurrentDifferenceDiv > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Main Buildings Rate /m^2 should exist and be visible', () => {
                    cy.get('#saTotalsMainBuildingsRateDiv > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Net Sale Price Gross Yield should exist and be visible', () => {
                    cy.get('.saControls > :nth-child(4) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Gross Rate /m^2 should exist and be visible', () => {
                    cy.get('.saControls > :nth-child(5) > span')
                        .should('exist')
                        .and('be.visible')
                        .invoke('text')
                        .should('be.ok');
                });

                it('Use Benchmark should exist and be visible', () => {
                    cy.get('.sa-useBenchmark')
                        .should('exist')
                        .and('be.visible');
                });

                it('Tick Use Benchmark', () => {
                    cy.get('#Benchmark\\ Property')
                        .should('exist')
                        .and('be.visible')
                        .click();
                });

                it('Delete Button should exist and be visible', () => {
                    cy.get('.deleteSalesAnalysis')
                        .should('exist')
                        .and('be.visible');
                });

                it('Restore Button should exist and be visible', () => {
                    cy.get('.deleteSalesAnalysis')
                        .should('exist')
                        .and('be.visible');
                });
            });
        });

        context('Checks for wrong values', () => {
            it('Expects Property Summary to not have default text', () => {
                this.elements.summaryAndComment.propertySummary.should('not.have.text', '[object Object]');
            });

            it('Expects Sales Analysis Comment to not have default text', () => {
                this.elements.salesAnalysisComment.should('not.have.text', '[object Object]');
            });
        });
    },

    // TODO: write out calculations here
    salesAnalysisCalculations() {
        it('Analysed LV = Sale LV x QV Land Value Index', () => {
            cy.get('#analysisAnalysedLV > span')
                .children()
                .invoke('text')
                .then(text => {
                    cy.doubleLog(text);
                });
        });
        it('Revision values', () => {});
    }
};

function isCurrency(str) {
    const regex = /^\$\d{1,3}(,\d{3})*(\.\d{2})?$/;
    return regex.test(str);
}

function isValidDecimal(str) {
    const regex = /^(0|1)?\.\d{2}$|^(0|1)$/;
    const num = parseFloat(str);
    return regex.test(str) && num >= 0 && num <= 1;
}

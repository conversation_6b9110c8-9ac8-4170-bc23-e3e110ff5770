import '@testing-library/cypress/add-commands'
export default {
    elements: {
        get territorialAuthorities() {
            return cy.getBySel("territorial-authorities");
        },
        get territorialAuthoritiesCheckbox() {
            return cy.getBySel("territorial-authorities").findAllByRole('checkbox');
        },
        get territorialAuthoritiesMultiselectDropDown() {
            return cy.getBySel("territorial-authorities").findAllByRole('button');
        },
        get territorialAuthoritiesFilterList() {
            return cy.getBySel("territorial-authorities").findAllByRole('textbox');
        },
        get territorialAuthoritiesErrorMessage() {
            return cy.getBySel("territorial-authorities-error-message");
        },
        get activityStatuses() {
            return cy.getBySel("activity-statuses");
        },
        get activityStatusesAllActive() {
            return cy.getBySel("activity-statuses-all-active");
        },
        get activityStatusesClear() {
            return cy.getBySel("activity-statuses-clear");
        },
        get activityStatusesMultiselect() {
            return cy.get('[data-cy="activity-statuses-dropdown"] .multiselect__tags-wrap > span');
        },
        get activityStatusDropdown() {
            return cy.getBySel('activity-statuses-dropdown');
        },
        get activityStatusDropdownMultiselect() {
            return cy.getBySel('activity-statuses-dropdown').findAllByRole('option');
        },
        get planStatus() {
            return cy.getBySel("plan-status");
        },
        get planStatusClear() {
            return cy.getBySel("plan-status-clear");
        },
        get planStatusMultiSelectCombo() {
            return cy.getBySel("plan-status-multi-select-combo");
        },
        get planStatusMultiSelectPlanStatus() {
            return cy.getBySel("plan-status-multi-select-combo").findAllByRole('option');
        },
        get constructionCompDropdown() {
            return cy.getBySel("construction-complete-dropdown");
        },
        get constructionCompSelectOptions() {
            return cy.getBySel("construction-complete-dropdown").findAllByRole('option');
        },
        get constructionComp() {
            return cy.getBySel("construction-complete");
        },
        get categories() {
            return cy.getBySel("categories");
        },
        get bcNumber() {
            return cy.getBySel("bc-number");
        },
        get qpid() {
            return cy.getBySel("bc-criteria-qpid");
        },
        get rollNumber() {
            return cy.getBySel("roll-number");
        },
        get assessment() {
            return cy.getBySel("assessment");
        },
        get suffix() {
            return cy.getBySel("suffix");
        },
        get bcIssueDate() {
            return cy.getBySel("bc-issue-date");
        },
        get bcIssueDateFrom() {
            return cy.getBySel("bc-issue-date").children().eq(1);
        },
        get bcIssueDateTo() {
            return cy.getBySel("bc-issue-date").children().eq(2);
        },
        get bcIssueDateErrorMessage() {
            return cy.getBySel("bc-issue-date-error");
        },
        get bcEnteredDate() {
            return cy.getBySel('bc-entered-date');
        },
        get bcEnteredDateFrom() {
            return cy.getBySel('bc-entered-date').children().eq(1);
        },
        get bcEnteredDateTo() {
            return cy.getBySel('bc-entered-date').children().eq(2);
        },
        get bcEnteredDateErrorMessage() {
            return cy.getBySel('bc-entered-date-error');
        },
        get saleGroupCodes() {
            return cy.getBySel('sale-group-codes').findAllByRole('button');
        },
        get valuers() {
            return cy.getBySel('bc-valuers').children().eq(1);
        },
        get valuersDropdownBox() {
            return cy.getBySel('bc-valuers').children().eq(1).children().eq(1).children().eq(1).find('input');
        },
        get selectValuersOptions1() {
            return cy.getBySel('multiselect-valuers-selected').children().eq(2).children().eq(0).children().eq(5).find('span').children().eq(0);
        },
        get selectValuersOptions2() {
            return cy.getBySel('multiselect-valuers-selected').children().eq(2).children().eq(0).children().eq(25).find('span').children().eq(0);
        },
        get selectValuersOptions3() {
            return cy.getBySel('multiselect-valuers-selected').children().eq(2).children().eq(0).children().eq(35).find('span').children().eq(0);
        },
        get valuersSelected() {
            return cy.getBySel('bc-valuers').children().eq(1).children().eq(0);
        },
        get clearValuers() {
            return cy.get('[data-cy="bc-valuers"] [data-cy="clear-valuers"]');
        },
        get bcCost() {
            return cy.getBySel('bc-cost');
        },
        get bcDueDate() {
            return cy.getBySel('bc-due-date');
        },
        get bcDueDateFrom() {
            return cy.getBySel('bc-due-date').children().eq(1);
        },
        get bcDueDateTo() {
            return cy.getBySel('bc-due-date').children().eq(2);
        },
        get bcDueDateErrorMessage() {
            return cy.getBySel('bc-due-date-error-message');
        },
        get notesForValuer() {
            return cy.getBySel('notes-for-valuer');
        },
        get notesForValuerDropDown() {
            return cy.getBySel('notes-for-valuer-dropDown');
        },
        get notesForValuerDropDownSelectOptions() {
            return cy.getBySel('notes-for-valuer-dropDown').children().eq(2).children().children();
        },
        get needsInspection() {
            return cy.getBySel('needs-inspection');
        },
        get needsInspectionDropDown() {
            return cy.getBySel('needs-inspection-dropdown');
        },
        get needsInspectionDropDownSelectOptions() {
            return cy.getBySel('needs-inspection-dropdown').children().eq(2).children().children();
        },
        get actionedDate() {
            return cy.getBySel('actioned-date');
        },
        get actionedDateFrom() {
            return cy.getBySel('actioned-date').children().eq(1);
        },
        get actionedDateTo() {
            return cy.getBySel('actioned-date').children().eq(2);
        },
        get actionedDateErrorMessage() {
            return cy.getBySel('actioned-date-error-message');
        },
        get createConsentInspectionReport() {
            return cy.getBySel('create-consent-inspection-report');
        },
        get exportBtn() {
            return cy.getBySel('export-button');
        },
        get clearButton() {
            return cy.getBySel('clear-btn');
        },
        get searchButton() {
            return cy.getBySel('search-btn');
        },
        get checkboxHeader() {
            return cy.get('table[data-cy="bc-search-table"] td:nth-child(1)');
        },
        get valRefTableHeader() {
            return cy.getBySel('val-ref-header');
        },
        get bcSearchTable() {
            return cy.getBySel('bc-search-table');
        },
        get bcSearchTableValueRefData() {
            return cy.getBySel('bc-search-table').children().eq(1).children().eq(2);
        },
        get bcSearchTabletd() {
            return cy.getBySel('bc-search-table').children();
        },
        get bcNumberTableHeader() {
            return cy.getBySel('bc-number-header');
        },
        get bcCostTableHeader() {
            return cy.getBySel('bc-cost-header');
        },
        get descriptionHeader() {
            return cy.getBySel('description-header');
        },
        get planDrawnHeader() {
            return cy.getBySel('plan-drawn-header');
        },
        get floorPlanDrawn() {
            return cy.get('table[data-cy="bc-search-table"] td:nth-child(8)');
        },
        get inactiveProperty() {
            return cy.get('table[data-cy="bc-search-table"] tr:nth-child(2)')
        },
        get statusHeader() {
            return cy.getBySel('status-header');
        },
        get consentHeader() {
            return cy.getBySel('consent-header');
        },
        get consentHeaderIcon() {
            return cy.getBySel('consent-header').children().eq(0).children().eq(0);
        },
        get bcCostHeaderIcon() {
            return cy.getBySel('bc-cost-header').children().eq(0).children().eq(0);
        },
        get bcNumberHeaderIcon() {
            return cy.getBySel('bc-number-header').children().eq(0).children().eq(0);
        },
        get valRefHeaderIcon() {
            return cy.getBySel('val-ref-header').children().eq(0).children().eq(0);
        },
        get descriptionHeaderIcon() {
            return cy.getBySel('description-header').children().eq(0).children().eq(0);
        },
        get alertModal() {
            return cy.getBySel('alert-modal').children();
        },
        get multiselectValuers() {
            return cy.get('[data-cy="alert-modal"] [data-cy="multiselect-valuers"]').children().eq(2).children().eq(0).children().eq(8).find('span').children().eq(0);
        },
        get assignValuersDropDown() {
            return cy.get('[data-cy="alert-modal"] [data-cy="multiselect-valuers"]').children().eq(1).find('input')
        },
        get bulkAssignValuersCheckBox() {
            return cy.get('[data-cy="checkbox-confirm-assign"]');
        },
        get confirmAssignValuersButton() {
            return cy.get('[data-cy="confirm-assign-valuers"]');
        },
        get closeAssignValuersButton() {
            return cy.get('[data-cy="close-assign-valuers"]');
        },
        get alertModalOk() {
            return cy.getBySel('alert-modal').children().eq(3).findAllByRole('button').eq(1);
        },
        get alertModalViewMyReport() {
            return cy.getBySel('alert-modal').children().eq(3).findAllByRole('button').eq(0);
        },
        get alertModalExportLimitExceededOk() {
            return cy.getBySel('alert-modal').children().eq(3).findAllByRole('button').eq(0);
        },
        get viewValuation() {
            return cy.get('table[data-cy="bc-search-table"] td:nth-child(12)').find('button');
        },
        get assignValuers() {
            return cy.get('[data-cy="bulk-assign"]');
        },
        get salesGroupsAndRollsButton() {
            return cy.get('[data-cy="sales-groups-and-rolls-button"]');
        },
        get salesGroupsSetRollsButton() {
            return cy.get('[data-cy="sales-group-set-rolls-button"]');
        },
        get salesGroupsClearButton() {
            return cy.get('[data-cy="sales-group-clear-button"]');
        },
        get salesGroupFormClose() {
            return cy.get('[data-cy="sales-group-form-close"]');
        },
        get salesGroupTerritorialAuthoritiesCheckbox() {
            return cy.get('[data-cy="sales-group-form-wrapper"] input[class="taCheckbox"]')
        },
        get salesGroupSGCheckbox() {
            return cy.get('[data-cy="sales-group-form-wrapper"] input[class="sgCheckbox"]')
        },
        get createConsentReportButton() {
            return cy.getBySel('consent-report');
        },
        get modalCloseButton() {
            return cy.get('#close');
        },
        get mapButton() {
            return cy.getBySel('show-map');
        },
        get selectAll() {
            return cy.get('[data-cy="select-all"]');
        },
        get nextPage() {
            return cy.get('[data-cy="paginate-top"] > .paginate-component > :nth-child(11) > a');
        },
        get clearAll() {
            return cy.get('[data-cy="checkbox-header"] > input');
        }
    }
}

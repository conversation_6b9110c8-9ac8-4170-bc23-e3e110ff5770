import { get } from "lodash";

export default {
    elements: {
        get draftPropertyCategoryValueExist() {
            return cy.get('[data-cy="valuation-property-details-category"] [role="combobox"]').children().eq(1);
        },
        get draftPropertyCategoryDropDown() {
            return cy.get('[data-cy="valuation-property-details-category"] ul[role="listbox"]').children();
        },
        get draftPropertyNatureOfImprovementValueExist() {
            return cy.get('[data-cy="valuation-property-details-nature-of-improvements"] [role="combobox"]').children().eq(1);
        },
        get draftPropertyNatureOfImprovementDropdown() {
            return cy.get('[data-cy="valuation-property-details-nature-of-improvements"] ul[role="listbox"]').children();
        },
        get draftPropertyPropertyName() {
            return cy.getBySel('valuation-property-details-property-name');
        },
        get draftPropertyLandUseValueExist() {
            return cy.get('[data-cy="valuation-property-details-land-use"] [role="combobox"]').children().eq(1);
        },
        get draftPropertyLandUseDropDown() {
            return cy.get('[data-cy="valuation-property-details-land-use"] ul[role="listbox"]').children();
        },
        get draftPropertyTALandZoneValueExist() {
            return cy.get('[data-cy="valuation-property-details-ta-land-zone"] [role="combobox"]').children().eq(1);
        },
        get draftPropertyTALandZoneDropDown() {
            return cy.get('[data-cy="valuation-property-details-ta-land-zone"] ul[role="listbox"]').children();
        },
        get draftPropertyEffectiveLandArea() {
            return cy.getBySel('valuation-property-details-effective-land-area').find('input');
        },
        get draftPropertyLandArea() {
            return cy.getBySel('valuation-property-details-land-area').find('input');
        },
        get draftPropertyMaoriLand() {
            return cy.getBySel('valuation-property-details-maori-land').find('input');
        },
        get draftPropertyPlanId() {
            return cy.getBySel('valuation-property-details-plan-id').find('input');
        },
        get draftPropertyProducation() {
            return cy.getBySel('valuation-property-details-production').find('input');
        },
        get lotPositionValueExist() {
            return cy.get('[data-cy="valuation-property-details-lot-position"] [role="combobox"]').children().eq(1);
        },
        get lotDropDownValues() {
            return cy.get('[data-cy="valuation-property-details-lot-position"] ul[role="listbox"]').children();
        },
        get contourValueExist() {
            return cy.get('[data-cy="valuation-property-details-contour"] [role="combobox"]').children().eq(1);
        },
        get contourDownValues() {
            return cy.get('[data-cy="valuation-property-details-contour"] ul[role="listbox"]').children();
        },
        get viewCheckValueExist() {
            return cy.get('[data-cy="valuation-property-details-view"] [role="combobox"]').children().eq(1);
        },
        get viewDropDownValues() {
            return cy.get('[data-cy="valuation-property-details-view"] ul[role="listbox"]').children();
        },
        get viewScopeValueExist() {
            return cy.get('[data-cy="valuation-property-details-view-scope"] [role="combobox"]').children().eq(1);
        },
        get viewScopeDropDown() {
            return cy.get('[data-cy="valuation-property-details-view-scope"] ul[role="listbox"]').children();
        },
        get csiValueExist() {
            return cy.get('[data-cy="valuation-property-details-class-of-surrounding-improvements"] [role="combobox"]').children().eq(1);
        },
        get csiDropDownValues() {
            return cy.get('[data-cy="valuation-property-details-class-of-surrounding-improvements"] ul[role="listbox"]').children();
        },
        get outlierValueExist() {
            return cy.get('[data-cy="valuation-property-details-outlier"] [role="combobox"]').children().eq(1);
        },
        get outlierDropDownValues() {
            return cy.get('[data-cy="valuation-property-details-outlier"] ul[role="listbox"]').children();
        },
        get houseTypeValueExist() {
            return cy.get('[data-cy="valuation-property-details-house-type"] [role="combobox"]').children().eq(1);
        },
        get houseTypeDropDown() {
            return cy.get('[data-cy="valuation-property-details-house-type"] ul[role="listbox"]').children();
        },
        get unitOfUse() {
            return cy.getBySel('valuation-property-details-units-of-use').find('input');
        },
        get ageValueExist() {
            return cy.get('[data-cy="valuation-property-details-age"] [role="combobox"]').children().eq(1);
        },
        get ageDropDown() {
            return cy.get('[data-cy="valuation-property-details-age"] ul[role="listbox"]').children();
        },
        get effectiveYearBuilt() {
            return cy.get('[data-cy="valuation-property-details-effective-year-built"]').children().find('input');
        },
        get poorfdnValueExist() {
            return cy.get('[data-cy="valuation-property-details-poor-foundations"] [role="combobox"]').children().eq(1);
        },
        get poorfdnDropDownValues() {
            return cy.get('[data-cy="valuation-property-details-poor-foundations"] ul[role="listbox"]').children();
        },
        get totalBderms() {
            return cy.getBySel('valuation-property-details-total-bedrms').find('input');
        },
        get totalBathrms() {
            return cy.getBySel('valuation-property-details-total-bathrms').find('input');
        },
        get totaltoilets() {
            return cy.getBySel('valuation-property-details-total-toilets').find('input');
        },
        get buildingSiteCover() {
            return cy.getBySel('valuation-property-details-building-site-cover').find('input');
        },
        get totalFloorArea() {
            return cy.getBySel('valuation-property-details-total-floor-area').find('input');
        },
        get mainLivingArea() {
            return cy.getBySel('valuation-property-details-main-living-area').find('input');
        },
        get totalLivingArea() {
            return cy.getBySel('valuation-property-details-total-living-area').find('input');
        },
        get laundryAndWorkshopValueExist() {
            return cy.get('[data-cy="valuation-property-details-laundry-workshop"] [role="combobox"]').children().eq(1);
        },
        get laundryAndWorkshopDropDown() {
            return cy.get('[data-cy="valuation-property-details-laundry-workshop"] ul[role="listbox"]').children();
        },
        get carAccessValueExist() {
            return cy.get('[data-cy="valuation-property-details-car-access"] [role="combobox"]').children().eq(1);
        },
        get carAccessDropDown() {
            return cy.get('[data-cy="valuation-property-details-car-access"] ul[role="listbox"]').children();
        },
        get driveWayValueExist() {
            return cy.get('[data-cy="valuation-property-details-driveway"] [role="combobox"]').children().eq(1);
        },
        get driveWayDropDown() {
            return cy.get('[data-cy="valuation-property-details-driveway"] ul[role="listbox"]').children();
        },
        get carParks() {
            return cy.getBySel('valuation-property-details-carparks').find('input');
        },
        get typesOfBuildingValueExist() {
            return cy.get('[data-cy="construction-information-type-of-building"] [role="combobox"]').children().eq(1);
        },
        get typesOfBuildingDropDown() {
            return cy.get('[data-cy="construction-information-type-of-building"] ul[role="listbox"]').children();
        },
        get floorArea() {
            return cy.getBySel('construction-information-floor-area').find('input');
        },
        get noOfStoreys() {
            return cy.getBySel('construction-information-number-of-storeys').find('input');
        },
        get yearBuilt() {
            return cy.getBySel('construction-information-year-built').find('input');
        },
        get description() {
            return cy.getBySel('construction-information-description').find('input');
        },
        get buildingLabel() {
            return cy.getBySel('construction-information-building-label').find('input');
        },
        get principalBuildingValueExist() {
            return cy.get('[data-cy="construction-information-principal-bldg"] [role="combobox"]').children().eq(1);
        },
        get principalBuildingDropDown() {
            return cy.get('[data-cy="construction-information-principal-bldg"] ul[role="listbox"]').children();
        },
        get wallConstructionValueExist() {
            return cy.get('[data-cy="construction-information-wall-construction"] [role="combobox"]').children().eq(1);
        },
        get wallConstructionDropDown() {
            return cy.get('[data-cy="construction-information-wall-construction"] ul[role="listbox"]').children();
        },
        get wallConditionValueExist() {
            return cy.get('[data-cy="construction-information-wall-condition"] [role="combobox"]').children().eq(1);
        },
        get wallConditionDropDown() {
            return cy.get('[data-cy="construction-information-wall-condition"] ul[role="listbox"]').children();
        },
        get roofConstruction() {
            return cy.getBySel('construction-information-roof-construction').find('input');
        },
        get roofConstructionValueExist() {
            return cy.get('[data-cy="construction-information-roof-construction"] [role="combobox"]').children().eq(1);
        },
        get roofConstructionDropDown() {
            return cy.get('[data-cy="construction-information-roof-construction"] ul[role="listbox"]').children();
        },
        get roofConditionValueExist() {
            return cy.get('[data-cy="construction-information-roof-condition"] [role="combobox"]').children().eq(1);
        },
        get roofConditionDropDown() {
            return cy.get('[data-cy="construction-information-roof-condition"] ul[role="listbox"]').children();
        },
        get floorConstructionValueExist() {
            return cy.get('[data-cy="construction-information-floor-construction"] [role="combobox"]').children().eq(1);
        },
        get floorConstructionDropDown() {
            return cy.get('[data-cy="construction-information-floor-construction"] ul[role="listbox"]').children();
        },
        get foundationValueExist() {
            return cy.get('[data-cy="construction-information-foundation"] [role="combobox"]').children().eq(1);
        },
        get foundationDropDown() {
            return cy.get('[data-cy="construction-information-foundation"] ul[role="listbox"]').children();
        },
        get wiringAgeValueExist() {
            return cy.get('[data-cy="construction-information-wiring-age"] [role="combobox"]').children().eq(1);
        },
        get wiringAgeDropDown() {
            return cy.get('[data-cy="construction-information-wiring-age"] ul[role="listbox"]').children();
        },
        get alertButton() {
            return cy.getBySel('alert-modal').find('button');
        },
        get alertButtonClose() {
            return cy.getBySel('alert-modal');
        },
        get plumbingAgeValueExist() {
            return cy.get('[data-cy="construction-information-plumbing-age"] [role="combobox"]').children().eq(1);
        },
        get plumbingAgeDropDown() {
            return cy.get('[data-cy="construction-information-plumbing-age"] ul[role="listbox"]').children();
        },
        get insulationValueExist() {
            return cy.get('[data-cy="construction-information-insulation"] [role="combobox"]').children().eq(1);
        },
        get insulationDropDown() {
            return cy.get('[data-cy="construction-information-insulation"] ul[role="listbox"]').children();
        },
        get glazingValueExist() {
            return cy.get('[data-cy="construction-information-glazing"] [role="combobox"]').children().eq(1);
        },
        get glazingDropDown() {
            return cy.get('[data-cy="construction-information-glazing"] ul[role="listbox"]').children();
        },
        get otherFeaturesCiValueExist() {
            return cy.get('[data-cy="construction-information-other-features"] [role="combobox"]').children().eq(1);
        },
        get otherFeaturesCiDropDown() {
            return cy.get('[data-cy="construction-information-other-features"] ul[role="listbox"]').children();
        },
        get spaceWithinBuildingValueExist() {
            return cy.get('[data-cy="space-within-building"] [role="combobox"]').children().eq(1);
        },
        get spaceWithinBuildingDropDown() {
            return cy.get('[data-cy="space-within-building"] ul[role="listbox"]').children();
        },
        get spaceTypeValueExist() {
            return cy.get('[data-cy="space-space-type"] [role="combobox"]').children().eq(1);
        },
        get spaceTypeDropDown() {
            return cy.get('[data-cy="space-space-type"] ul[role="listbox"]').children();
        },
        get spaceFloorArea() {
            return cy.getBySel('space-floor-area').find('input');
        },
        get spaceQualityValueExist() {
            return cy.get('[data-cy="space-quality"] [role="combobox"]').children().eq(1);
        },
        get spaceQualityInput() {
            return cy.get('[data-cy="space-quality"] [role="combobox"]').find('input');
        },
        get spaceQualityDropDown() {
            return cy.get('[data-cy="space-quality"] ul[role="listbox"]').children();
        },
        get spaceOtherFeatureValueExist() {
            return cy.get('[data-cy="space-other-feature"] [role="combobox"]').children().eq(1);
        },
        get spaceOtherFeatureDropDown() {
            return cy.get('[data-cy="space-other-feature"] ul[role="listbox"]').children();
        },
        get siteDevelopmentDescriptionValueExist() {
            return cy.getBySel('site-development-description');
        },
        get siteOtherImprovement() {
            return cy.getBySel('site-other-improvement').find('input');
        },
        get siteImprovementQualityValueExist() {
            return cy.get('[data-cy="site-quality"] [role="combobox"]').children().eq(1);
        },
        get siteImprovementQualityDropDown() {
            return cy.get('[data-cy="site-quality"] ul[role="listbox"]').children();
        },
        get setupButton() {
            return cy.getBySel('valuation-property-details-setup-button').find('button');
        },
        get areaQuantity() {
            return cy.getBySel('area-quantity');
        },
        get unitOfMeasurement() {
            return cy.getBySel('unit-of-measure');
        },
        get otherImprovementQuality() {
            return cy.getBySel('other-improvement-quality');
        },
        get otherImprovementDescription() {
            return cy.getBySel('other-improvement-description');
        },
        get bcJobStepperDraftPropertyDetails() {
            return cy.getBySel('step-draft-property-details');
        },
        get bcJobStepperComparableProperties() {
            return cy.getBySel('step-comparable-properties');
        },
        get setupCompleteClose() {
            return cy.get('.mdl-button--primary');
        },
        get recalculateButton() {
            return cy.getBySel('valuation-comparables-recalculate');
        },
        get completeValuation() {
            return cy.getBySel('bc-job-complete-valuation');
        },
        get jobValuer() {
            return cy.getBySel('bc-job-valuers');
        },
        get valuationSaveAsDraft() {
            return cy.getBySel('valuation-save-as-draft').find('button');
        },
        get jobValuerValueExist() {
            return cy.get('[data-cy="bc-job-valuers"] [role="combobox"]').children().eq(1);
        },
        get jobValuerDropDown() {
            return cy.get('[data-cy="bc-job-valuers"] ul[role="listbox"]').children();
        },
        get bcJobStepperValuation() {
            return cy.getBySel('step-valuation');
        },
        get bcJobStepperJobCompletion() {
            return cy.getBySel('step-job-completion');
        },
        get currentRollActivityMaintenanceTable() {
            return cy.get('[data-cy="current-roll-maintenance-activity-table"] td:nth-child(2)').children().eq(0);
        },
    },
    dropDownBox(valueExist, value, dropdown) {
        return valueExist.then(($el) => {
            const text = $el.text().trim();
            let output;
            if (!text || text === " ") {
                dropdown.contains(value).click({ force: true });
            }
            return output;
        }).then((updatedElement) => {
            const output = updatedElement.text().trim();
            return output;
        });
    },
}
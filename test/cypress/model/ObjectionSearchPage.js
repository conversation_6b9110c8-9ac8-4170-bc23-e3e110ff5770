import '@testing-library/cypress/add-commands'
export default {
    elements: {
        get objectionType() {
            return cy.get('[data-cy="objection-type"]');
        },
        get objectionTypeInput() {
            return cy.get('[data-cy="objection-type"] [role="combobox"]').children().eq(1);
        },
        get objectionTypeDropdown() {
            return cy.get('[data-cy="objection-type"] ul[role="listbox"]').children();
        },
        get valuationJobStatuses() {
            return cy.get('[data-cy="valuation-job-statuses"]');
        },
        get valuationJobStatusesInput() {
            return cy.get('[data-cy="valuation-job-statuses"] [role="combobox"]').children().eq(1).children().eq(0);
        },
        get valuationJobStatusesDropdown() {
            return cy.get('[data-cy="valuation-job-statuses"] ul[role="listbox"]').children();
        },
        get valuationJobStatusesAllActive() {
            return cy.get('[data-cy="valuation-job-statuses-all-active"]');
        },
        get valuationJobStatusesClear() {
            return cy.get('[data-cy="valuation-job-statuses-clear"]');
        },
        get qpidInput() {
            return cy.get('[data-cy="qpid-input"]');
        },
        get administrationStatusClear() {
            return cy.get('[data-cy="administration-status-clear"]');
        },
        get objectionTypeSelector() {
            return cy.get('[data-cy="objection-type-dropdown"]');
        },
        get administativeStatuses() {
            return cy.get('[data-cy="administration-status"]');
        },
        get administativeStatusesInput() {
            return cy.get('[data-cy="administration-status"] [role="combobox"]').children().eq(1).children().eq(0);
        },
        get administativeStatusesDropdown() {
            return cy.get('[data-cy="administration-status"] ul[role="listbox"]').children();
        },
        get administativeStatusesClear() {
            return cy.get('[data-cy="administration-status"]');
        },
        get category() {
            return cy.get('[data-cy="category"]');
        },
        get categoryInput() {
            return cy.get('[data-cy="category"]').find('input');
        },
        get categoryGroups() {
            return cy.get('[data-cy="category-groups"]');
        },
        get categoryGroupsInput() {
            return cy.get('[data-cy="category-groups"] [role="combobox"]').children().eq(1).children().eq(0);
        },
        get categoryGroupsDropdown() {
            return cy.get('[data-cy="category-groups"] ul[role="listbox"]').children();
        },
        get categoryGroupsClear() {
            return cy.get('[data-cy="category-groups-clear"]');
        },
        get registeredValuer() {
            return cy.get('[data-cy="registered-valuer"]');
        },
        get registeredValuerInput() {
            return cy.get('[data-cy="registered-valuer"] [role="combobox"]').children().eq(1).children().eq(0);
        },
        get registeredValuerDropdown() {
            return cy.get('[data-cy="registered-valuer"] ul[role="listbox"]').children();
        },
        get registeredValuerName() {
            return cy.get('[data-cy="registered-valuer"] ul[role="listbox"]').children().eq(1).find('span').eq(1);
        },
        get registeredValuerClear() {
            return cy.get('[data-cy="registered-valuer-clear"]');
        },
        get valuer() {
            return cy.get('[data-cy="valuer"]');
        },
        get valuerInput() {
            return cy.get('[data-cy="valuer"] [role="combobox"]').children().eq(1).children().eq(0);
        },
        get valuerDropdown() {
            return cy.get('[data-cy="valuer"] ul[role="listbox"]').children();
        },
        get valuerClear() {
            return cy.get('[data-cy="valuer"]');
        },
        get valuerName() {
            return cy.get('[data-cy="valuer"] ul[role="listbox"]').children().eq(1).find('span').eq(1);
        },
        get salesGroupsAndRollsButton() {
            return cy.get('[data-cy="sales-groups-and-rolls-button"]');
        },
        get salesGroupsSetRollsButton() {
            return cy.get('[data-cy="sales-group-set-rolls-button"]');
        },
        get salesGroupsClearButton() {
            return cy.get('[data-cy="sales-group-clear-button"]');
        },
        get alertModal() {
            return cy.get('[data-cy="alert-modal"]');
        },
        get salesGroupFormClose() {
            return cy.get('[data-cy="sales-group-form-close"]');
        },
        get salesGroupTerritorialAuthoritiesCheckbox() {
            return cy.get('[data-cy="sales-group-form-wrapper"] input[class="taCheckbox"]');
        },
        get salesGroupSGCheckbox() {
            return cy.get('[data-cy="sales-group-form-wrapper"] input[class="sgCheckbox"]');
        },
        get exportButton() {
            return cy.get('[data-cy="export"]');
        },
        get alertModal() {
            return cy.getBySel('alert-modal').children();
        },
        get alertModalOk() {
            return cy.getBySel('modal-confirm-button');
        },
        get alertModalViewMyReport() {
            return cy.getBySel('modal-cancel-button');
        },
        get alertModalExportLimitExceededOk() {
            return cy.getBySel('modal-confirm-button');
        },
        
        get exportAlertWrapper() {
            return cy.get('[data-cy="alert-modal"]');
        },
        get exportAlertOkButton() {
            return cy.get('[data-cy="alert-ok"]');
        },
        get exportAlertCancelButton() {
            return cy.get('[data-cy="alert-cancel"]');
        },
        get territorialAuthorities() {
            return cy.getBySel("territorialAuthoritiesDiv");
        },
        get territorialAuthoritiesCheckbox() {
            return cy.getBySel("territorialAuthoritiesDiv").findAllByRole('checkbox');
        },
        get territorialAuthoritiesMultiselectDropDown() {
            return cy.getBySel("territorialAuthoritiesDiv").findAllByRole('button');
        },
        get territorialAuthoritiesFilterList() {
            return cy.getBySel("territorialAuthoritiesDiv").findAllByRole('textbox');
        },
        get objectionSearch() {
            return cy.getBySel("objection-search").findByText('Search');
        },
        get objectionSearchTableRow() {
            return cy.get('table[data-cy="objection-search-table"] tr:nth-child(2)');
        },
        get objectionClear() {
            return cy.getBySel("objection-clear").findByText('Clear');
        },
        get objectiontableresultColumnResult() {
            return cy.get('.activity-list--action > .ta-sign-off > .action-button');
        },
        get objectionResultQivsButton() {
            return cy.get('table[data-cy="objection-search-table"] .activity-list--action > .ta-sign-off > .action-button');
        },
        get objectionResultAddress() {
            return cy.getBySel("objection-result-address");
        },
        get objectionResultAddressIcon() {
            return cy.getBySel('objection-result-address').children().eq(0).children().eq(0).children().eq(0);
        },
        get objectionResultValRef() {
            return cy.getBySel("objection-result-valref");
        },
        get objectionResultDataReceived() {
            return cy.getBySel("objection-result-data-received");
        },
        get objectionResultCategory() {
            return cy.getBySel("objection-result-category");
        },
        get objectionResultObjector() {
            return cy.getBySel("objection-result-objector");
        },
        get objectionResultValuer() {
            return cy.getBySel("objection-result-valuer");
        },
        get objectionResultAdminStatus() {
            return cy.getBySel("objection-result-admin-status");
        },
        get objectionResultJobStatus() {
            return cy.getBySel("objection-result-valuation-job-status");
        },
        get objectionResultDocs() {
            return cy.getBySel("objection-result-docs");
        },
        get checkboxHeader() {
            return cy.get('table[data-cy="objection-search-table"] td:nth-child(1)').children();
        },
        get objectionTableHeaderTaSignOff() {
            return cy.get('table[data-cy="objection-search-table"] td:nth-child(12)').children();
        },
        get taApprovalDatePicker() {
            return cy.get('.mx-input');
        },
        get checkboxConfirmApproval() {
            return cy.get('#confirmApprove');
        },
        get approveObjection() {
            return cy.get('#approve-objections');
        },
        get objectionSuccessfulMessage() {
            return cy.get('[data-cy="alert-modal"]');
        },
        get objectionCloseButton() {
            return cy.get('#close');
        },
        get rejectionReason() {
            return cy.get('.qv-input');
        },
        get reinstatementSave() {
            return cy.get('#reinstatement-save');
        },
        get taApprovalButton() {
            return cy.get('[data-cy="ta-approval"]');
        },
        get createObjectionReportButton() {
            return cy.getBySel('objection-report');
        },
        get mapButton() {
            return cy.getBySel('show-map');
        },
        get selectAll() {
            return cy.getBySel('obj-select-all');
        },
        get nextPage() {
            return cy.get('[data-cy="paginate-top"] > .paginate-component > :nth-child(11) > a');
        },
        get clearAll() {
            return cy.getBySel('clear-all');
        }
    },
    dropDownBox(valueExist, value, dropdown) {
        return valueExist.then(($el) => {
            const text = $el.text().trim();
            let output;
            if (!text || text === " ") {
                dropdown.contains(value).click({ force: true });
            }
            return output;
        }).then((updatedElement) => {
            const output = updatedElement.text().trim();
            return output;
        });
    },
};

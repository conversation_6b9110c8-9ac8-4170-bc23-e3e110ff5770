export default {
    href: ((qpid) => `property/qv-map/0/0/${qpid}`),

    elements: {
        get addQpids() {
            return cy.get('[data-cy="search-qpid-textarea"]');
        },
        get searchButton() {
            return cy.get('[data-cy="search-qpid-button"]');
        },
        get propertyInfoHeader() {
            return cy.get(':nth-child(2) > .qvMaps-propertyInfo-wrapper > .qvMaps-propertyInfo-header');
        },
        get propertyInfoWrapper() {
            return cy.get('[data-cy="property-info-wrapper"]');
        },
        get alertModal() {
            return cy.get('[data-cy="alert-modal"]')
        },
        get alertMessage() {
            return cy.get('[data-cy="alert-modal"] > p');
        },
    },

};

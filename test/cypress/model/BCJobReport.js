export default {
    elements: {
        get category() {
            return cy.get('[data-cy="readonly-category"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.trim();
                });
        },
        get natureOfImprovement() {
            return cy.get('[data-cy="readonly-nature-of-improvement"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.replace(/,/g, '  ').trim();
                });
        },
        get landUse() {
            return cy.get('[data-cy="readonly-land-use"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.trim();
                });
        },
        get tALandZone() {
            return cy.get('[data-cy="readonly-ta-land-zone"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.trim();
                });
        },
        get lotPosition() {
            return cy.get('[data-cy="readonly-lot-position"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.trim();
                });
        },
        get contour() {
            return cy.get('[data-cy="readonly-contour"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.trim();
                });
        },
        get view() {
            return cy.get('[data-cy="readonly-view"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.trim();
                });
        },
        get viewScope() {
            return cy.get('[data-cy="readonly-view-scope"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.trim();
                });
        },
        get cSI() {
            return cy.get('[data-cy="readonly-csi"]')
                .children().eq(0).children().eq(1).invoke('text')
                .then((text) => {
                    return text.trim();
                });
        },
    },
}
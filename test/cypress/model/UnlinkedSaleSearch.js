export default {
    elements: {
        get Root() {
            return cy.get('[data-cy="unlinked-search"]');
        },
        get ButtonSearch() {
            return this.Root.find('[data-cy="button-search"]');
        },
        get ButtonClear() {
            return this.Root.find('[data-cy="button-clear"]');
        },
        get LabelResultCount() {
            return this.Root.find('[data-cy="label-result-count"]').scrollIntoView();
        },
        get LabelResultPlaceholder() {
            return this.Root.find('[data-cy="label-result-placeholder"]');
        },
        get ResultRows() {
            return this.Root.find('[data-cy="result-row"]');
        },
    },
    search() {
        this.elements.ButtonSearch.click({force: true});
    },
    clear() {
        this.elements.ButtonClear.click({force: true});
    },
};

export const WorkUnitReport = {
    elements: {
        get valuerButton() {
            return cy.get('div[data-cy="valuer-button"]');
        },
        get taButton() {
            return cy.get('div[data-cy="ta-button"]');
        },
        get valuerRadio() {
            return cy.get('input[data-cy="valuer-radio"]');
        },
        get taRadio() {
            return cy.get('input[data-cy="ta-radio"]');
        },
        get scheduleReportButton() {
            return cy.get('button[data-cy="schedule-report-button"]');
        },
        get valuerSelectedTag() {
            return cy.get('[data-cy="valuer-multiselect"]').find('.multiselect__tag > span');
        },
        get valuerOptionDropButton(){
            return cy.get('[data-cy="valuer-multiselect"]').find('.multiselect__select');
        },
        get valuerOptions(){
            return cy.get('[data-cy="valuer-multiselect"]').find('.multiselect__content').children('li');
        }
    },

    visit() {
        cy.visit(`/reports/SSRS_WORK_UNIT`);
    },

    verifyValuerSelectedShouldContain(valuer) {
        this.elements.valuerSelectedTag.should('exist').should('contain', valuer);
    },

    verifyValuerOptionsOnlyContainSelected(){
        this.elements.valuerOptionDropButton.click();
        cy.wait(2000);
        this.elements.valuerOptions.its('length').then((length) => {
            expect(length).to.equal(3); // 2 placeholder values and the default valuer
          });
    },
    
    verifyValuerOptionsContainAll(){
        this.elements.valuerOptionDropButton.click();
        cy.wait(2000);
        this.elements.valuerOptions.its('length').then((length) => {
            expect(length).to.be.greaterThan(3); // 2 placeholder values and the default valuer
          });    
    },

    checkTADropdownFieldIsEnabled(){
        this.elements.taButton.should('not.have.class', 'disabled').and('have.css', 'pointer-events', 'auto');
        this.elements.taRadio.should('not.be.disabled');
    },
    
    checkTADropdownFieldIsDisabled(){
        this.elements.taButton.should('have.class', 'disabled').and('have.css', 'pointer-events', 'none');
        this.elements.taRadio.should('be.disabled');
    },

    checkScheduleReportButtonIsDisabled(){
        this.elements.scheduleReportButton.should('be.disabled');
    }
}
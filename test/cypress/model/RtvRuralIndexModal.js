export default {
    get modal() {
        return 'div[data-cy=rtvRuralIndexModal]';
    },
    get heading() {
        return 'h1[data-cy=rtvRuralIndexModalHeading]';
    },
    get message() { 
        return 'p[data-cy=rtvRuralIndexModalMessage]';
    },
    get messageList() { 
        return 'ul[data-cy=rtvRuralIndexModalMessageList]';
    },
    get cancelButton() { 
        return 'button[data-cy=rtvRuralIndexModalCancelButton]';
    },
    get confirmButton() { 
        return 'button[data-cy=rtvRuralIndexModalConfirmButton]';
    },
    get responseCode() { 
        return 'input[data-cy=rtvRuralIndexModalResponseCode]';
    },
};

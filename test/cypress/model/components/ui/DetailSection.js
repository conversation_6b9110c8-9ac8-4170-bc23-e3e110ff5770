export function DetailSectionModel(identifier) {
    const wrapper = () => cy.get(identifier);

    return {
        get header() {
            return wrapper().find(`[data-cy="detail-section-header"]`)
        },
        get title() {
            return wrapper().find(`[data-cy="detail-section-title"]`)
        },
        get expander() {
            return wrapper().find(`[data-cy="detail-section-expander"]`)
        },
        get body() {
            return wrapper().find(`[data-cy="detail-section-body"]`)
        },
        get content() {
            return wrapper().find(`[data-cy="detail-section-content"]`)
        }
    }
}

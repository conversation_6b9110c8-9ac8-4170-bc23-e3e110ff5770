
export default {

    get propertyToolbar() {
        return cy.get('[data-cy=propertyToolbar]'); 
    },
    get propertyToolbarSummaryTab() {
        return cy.get('[data-cy=propertyToolbarSummaryTab]'); 
    },
    get propertyToolbarExtraDetailsTab() {
        return cy.get('[data-cy=propertyToolbarExtraDetailsTab]'); 
    },
    get propertyToolbarValuationJobsTab() {
        return cy.get('[data-cy=propertyToolbarValuationJobsTab]'); 
    },
    get propertyToolbarRollMaintenanceTab() {
        return cy.get('[data-cy=propertyToolbarRollMaintenanceTab]'); 
    },
    get propertyToolbarMapTab() {
        return cy.get('[data-cy=propertyToolbarMapTab]'); 
    },
    get propertyToolbarPhotoUploaderLink() {
        return cy.get('[data-cy=propertyToolbarPhotoUploaderLink]'); 
    },
    get propertyToolbarQivsLink() {
        return cy.get('[data-cy=propertyToolbarQivsLink]'); 
    },
    get propertyToolbarFloorPlansLink() {
        return cy.get('[data-cy=propertyToolbarFloorPlansLink]'); 
    },
    get propertyToolbarMapLink() {
        return cy.get('[data-cy=propertyToolbarMapLink]'); 
    },
    get propertyToolbarGoogleLink() {
        return cy.get('[data-cy=propertyToolbarGoogleLink]'); 
    },
    get oldMonarchExtraDetailsTab() {
        return cy.get('li.extra-details');
    }


}

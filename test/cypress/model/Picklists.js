export default {
    href: '#',

    get searchbar() {
        return cy.get('#typeAheadClassifications');
    },

    get pickListTableHeading() {
        return cy.get('.divTableHeading');
    },

    get pickListHeader() {
        return cy.contains('.divTableHead', 'Pick List');
    },

    get itemsHeader() {
        return cy.contains('.divTableHead', 'Items');
    },

    get tableBody() {
        return cy.get('.divTable.minimalistBlack').find('.divTableBody');
    }
};

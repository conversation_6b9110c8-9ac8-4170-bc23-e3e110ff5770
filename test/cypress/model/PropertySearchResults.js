export default {
    categorySelector: 'div.category',
    categorySelectorSales: '#categoryDiv',
    uploadPhotosSelector: 'li[title="Upload Photos"]',
    resultToolbarRightSelector: 'ul.toolbar.listingControls.righty',
    nspSelector: '#salesNetRateDiv',
    saleAnalysisSelector: 'div.saleAnalysis',
    expandedClass: 'openProp',
    bedroomSelector: '[data-cy="bedrooms"]',
    addressOneSelector: '#address1Span',
    addressTwoSelector: '#address2Span',
    categorySelector: '[data-cy="category"]',
    landAreaSelector: '[data-cy="land-area"]',
    yearBuiltSelector: '[data-cy="effective-year-built"]',

    get header() {
        return cy.contains('h1', 'Property Search Results');
    },

    get resultsWrapper() {
        return cy.get('.resultsInner-wrapper');
    },

    get results() {
        return this.resultsWrapper.find('div[id^="results_"].resultsRow');
    },

    get resultsCount() {
        return cy.get('.resultsFound').last().invoke('text').then(text => {
            return text.match(/\d+/)[0];
        });
    },

    get compactViewToggle() {
        return cy.get('label[for="cmn-toggle-4"]');
    },

    get expandResultsButton() {
        return cy.get('li[title="Expand Results"]');
    }
};

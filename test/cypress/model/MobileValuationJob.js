import { find, get } from "lodash";

export const MobileValuationJob = {
    elements: {
        get propertyAddress() {
            return cy.get('[data-cy="property-address"]')
        },
        get propertyValRef() {
            return cy.get('[data-cy="property-val-ref"]')
        },
        get propertyCategory() {
            return cy.get('[data-cy="property-category"]');
        },
        get propertySummary() {
            return cy.get('[data-cy="property-summary"]');
        },
        get propertySaleDate() {
            return cy.get('[data-cy="property-sale-date"]');
        },
        get propertySaleDetails() {
            return cy.get('[data-cy="property-sale-details"]');
        },
        get propertyEstimates() {
            return cy.get('[data-cy="property-estimates"]');
        },
        get propertyValues() {
            return cy.get('[data-cy="property-values"]');
        },
        get propertyIconLabel() {
            return cy.get('[data-cy="property-data-icon-label"]');
        },
        get valuationJobSummary() {
            return cy.get('[data-cy="valuation-job-summary"]');
        },
        get valuationJobEditDetails() {
            return cy.get('[data-cy="valuation-job-edit-details"]');
        },
        get valuationJobAddPhotos() {
            return cy.get('[data-cy="valuation-job-add-photos"]');
        },
        get valuationJobComparables() {
            return cy.get('[data-cy="valuation-job-comparables"]');
        },
        get valuationJobInformation() {
            return cy.get('[data-cy="valuation-job-information"]');
        },
    }
}

export const ValuationJobTab = {
    elements: {
        get overViewTab() {
            return cy.get('[data-container="overview"]');
        },
        get interiorTab() {
            return cy.get('[data-container="interior"]');
        },
        get bedroomsTab() {
            return cy.get('[data-container="bedrooms"]');
        },
        get kitchenTab() {
            return cy.get('[data-container="kitchen"]');
        },
        get bathroomsTab() {
            return cy.get('[data-container="bathrooms"]');
        },
        get garagingTab() {
            return cy.get('[data-container="garaging"]');
        },
        get improvementsTab() {
            return cy.get('[data-container="improvements"]');
        },
    }

}

export const OverViewTab = {
    elements: {
        get bedrooms() {
            return cy.get('[data-cy="bedrooms"]');
        },
        get bedroomsInputBox() {
            return cy.get('[data-cy="bedrooms"]').find('input');
        },
        get bathrooms() {
            return cy.get('[data-cy="bathrooms"]');
        },
        get bathroomsInputBox() {
            return cy.get('[data-cy="bathrooms"]').find('input');
        },
        get toilets() {
            return cy.get('[data-cy="toilets"]');
        },
        get toiletsInputBox() {
            return cy.get('[data-cy="toilets"]').find('input');
        },
        get livingAreas() {
            return cy.get('[data-cy="living-areas"]');
        },
        get livingAreasInputBox() {
            return cy.get('[data-cy="living-areas"]').find('input');
        },
        get garaging() {
            return cy.get('[data-cy="garaging"]');
        },
        get garagingInputBox() {
            return cy.get('[data-cy="garaging"]').find('input');
        },
        get offStreetParking() {
            return cy.get('[data-cy="offstreet-parking"]');
        },
        get offStreetParkingInputBox() {
            return cy.get('[data-cy="offstreet-parking"]').find('input');
        },
        get houseType() {
            return cy.get('[data-cy="house-type"]');
        },
        get houseTypeSelectOption() {
            return cy.get('[data-cy="house-type"]').find('select');
        },
        get houseTypeInputBox() {
            return cy.get('[data-cy="house-type"]').find('input');
        },
        get yearBuilt() {
            return cy.get('[data-cy="year-built"]');
        },
        get yearBuiltInputBox() {
            return cy.get('[data-cy="year-built"]').find('input');
        },
        get totalFloorArea() {
            return cy.get('[data-cy="total-floor-area"]');
        },
        get totalFloorAreaInputBox() {
            return cy.get('[data-cy="total-floor-area"]').find('input');
        },
        get totalFloorAreaDescription() {
            return cy.get('[data-cy="total-floor-area-description"]');
        },
        get totalFloorAreaDescriptionInputBox() {
            return cy.get('[data-cy="total-floor-area-description"]').find('textarea');
        },
        get exteriorCladding() {
            return cy.get('[data-cy="exterior-cladding"]');
        },
        get exteriorCladdingSelectOption() {
            return cy.get('[data-cy="exterior-cladding"]').find('select');
        },
        get foundation() {
            return cy.get('[data-cy="foundation"]');
        },
        get foundationSelectOption() {
            return cy.get('[data-cy="foundation"]').find('select');
        },
        get joinery() {
            return cy.get('[data-cy="joinery"]');
        },
        get joinerySelectOption() {
            return cy.get('[data-cy="joinery"]').find('select');
        },
        get roofStyle() {
            return cy.get('[data-cy="roof-style"]');
        },
        get roofStyleSelectOption() {
            return cy.get('[data-cy="roof-style"]').find('select');
        },
        get roofConstruction() {
            return cy.get('[data-cy="roof-construction"]');
        },
        get roofConstructionSelectOption() {
            return cy.get('[data-cy="roof-construction"]').find('select');
        },
        get spouting() {
            return cy.get('[data-cy="spouting"]');
        },
        get spoutingSelectOption() {
            return cy.get('[data-cy="spouting"]').find('select');
        },
        get externalCondition() {
            return cy.get('[data-cy="external-condition"]');
        },
        get externalConditionSelectOption() {
            return cy.get('[data-cy="external-condition"]').find('select');
        },
        get qualityOfExternalPresentation() {
            return cy.get('[data-cy="quality-of-external-presentation"]');
        },
        get qualityOfExternalPresentationSelectOption() {
            return cy.get('[data-cy="quality-of-external-presentation"]').find('select');
        },
        get internalCondition() {
            return cy.get('[data-cy="internal-condition"]');
        },
        get internalConditionSelectOption() {
            return cy.get('[data-cy="internal-condition"]').find('select');
        },
        get qualityOfInternalPresentation() {
            return cy.get('[data-cy="quality-of-internal-presentation"]');
        },
        get qualityOfInternalPresentationSelectOption() {
            return cy.get('[data-cy="quality-of-internal-presentation"]').find('select');
        },
        get standardOfAccomodation() {
            return cy.get('[data-cy="standard-of-accomodation"]');
        },
        get standardOfAccomodationSelectOption() {
            return cy.get('[data-cy="standard-of-accomodation"]').find('select');
        },
        get layoutDescriptions() {
            return cy.get('[data-cy="layout-descriptions"]');
        },
        get layoutDescriptionsSelectOption() {
            return cy.get('[data-cy="layout-descriptions"]').find('select');
        },
        get maintenanceRequired() {
            return cy.get('[data-cy="maintenance-required"]');
        },
        get maintenanceRequiredInputBox() {
            return cy.get('[data-cy="maintenance-required"]').find('input');
        },
        get immediateMaintenanceRequired() {
            return cy.get('[data-cy="immediate-maintenance-required"]');
        },
        get immediateMaintenanceRequiredInputBox() {
            return cy.get('[data-cy="immediate-maintenance-required"]').find('input');
        },
        get recentAlterations() {
            return cy.get('[data-cy="recent-alterations"]');
        },
        get recentAlterationsInputBox() {
            return cy.get('[data-cy="recent-alterations"]').find('input');
        },
        get codeCompliance() {
            return cy.get('[data-cy="code-compliance"]');
        },
        get codeComplianceInputBox() {
            return cy.get('[data-cy="code-compliance"]').find('input');
        },
        get overviewNotes() {
            return cy.get('[data-cy="overview-notes"]');
        },
        get overviewNotesTextBox() {
            return cy.get('[data-cy="overview-notes"]').find('textarea');
        },
    },
    dropDownBox(valueExist, value, dropdown) {
        return valueExist.then(($el) => {
            const text = $el.text().trim();
            let output;
            if (!text || text === " ") {
                dropdown.contains(value).click({ force: true });
            }
            return output;
        }).then((updatedElement) => {
            const output = updatedElement.text().trim();
            return output;
        });
    }
}

export const InteriorTab = {
    elements: {
        get livingArea() {
            return cy.get('[data-cy="living-area"]');
        },
        get livingAreaSelectOption() {
            return cy.get('[data-cy="living-area"]').find('select');
        },
        get livingAreaDescription() {
            return cy.get('[data-cy="living-area-description"]');
        },
        get livingAreaDescriptionSelectOption() {
            return cy.get('[data-cy="living-area-description"]').find('select');
        },
        get laundry() {
            return cy.get('[data-cy="laundry"]');
        },
        get laundrySelectOption() {
            return cy.get('[data-cy="laundry"]').find('select');
        },
        get internalLinings() {
            return cy.get('[data-cy="internal-linings"]');
        },
        get internalLiningsSelectOption() {
            return cy.get('[data-cy="internal-linings"]').find('select');
        },
        get floors() {
            return cy.get('[data-cy="floors"]');
        },
        get floorsSelectOption() {
            return cy.get('[data-cy="floors"]').find('select');
        },
        get chattels() {
            return cy.get('[data-cy="chattels"]');
        },
        get chattelsSelectOption() {
            return cy.get('[data-cy="chattels"]').find('select');
        },
        get heatingType() {
            return cy.get('[data-cy="heating-type"]');
        },
        get heatingTypeSelectOption() {
            return cy.get('[data-cy="heating-type"]').find('select');
        },
        get insulation() {
            return cy.get('[data-cy="insulation"]');
        },
        get insulationSelectOption() {
            return cy.get('[data-cy="insulation"]').find('select');
        },
        get plumbingAge() {
            return cy.get('[data-cy="plumbing-age"]');
        },
        get plumbingAgeSelectOption() {
            return cy.get('[data-cy="plumbing-age"]').find('select');
        },
        get wiringAge() {
            return cy.get('[data-cy="wiring-age"]');
        },
        get wiringAgeSelectOption() {
            return cy.get('[data-cy="wiring-age"]').find('select');
        },
        get doubleGlazing() {
            return cy.get('[data-cy="double-glazing"]');
        },
        get doubleGlazingSelectOption() {
            return cy.get('[data-cy="double-glazing"]').find('select');
        },
        get alternativeEnergy() {
            return cy.get('[data-cy="alternative-energy"]');
        },
        get alternativeEnergySelectOption() {
            return cy.get('[data-cy="alternative-energy"]').find('select');
        },
        get ventilation() {
            return cy.get('[data-cy="ventilation"]');
        },
        get ventilationSelectOption() {
            return cy.get('[data-cy="ventilation"]').find('select');
        },
        get redecorationAge() {
            return cy.get('[data-cy="redecoration-age"]');
        },
        get redecorationAgeSelectOption() {
            return cy.get('[data-cy="redecoration-age"]').find('select');
        },
        get otherFeatures() {
            return cy.get('[data-cy="other-features"]');
        },
        get otherFeaturesSelectOption() {
            return cy.get('[data-cy="other-features"]').find('select');
        },
        get interiorNotes() {
            return cy.get('[data-cy="interior-notes"]');
        },
        get interiorNotestextBox() {
            return cy.get('[data-cy="interior-notes"]').find('textarea');
        },
    }
}

export const BedroomsTab = {
    elements: {
        get bedroom() {
            return cy.get('[data-cy="bedroom-1"]');
        },
        get bedroomSelectOption() {
            return cy.get('[data-cy="bedroom-1"]').find('select');
        },
        get bedroomDescription() {
            return cy.get('[data-cy="bedroom-description"]');
        },
        get bedroomDescriptionSelectOption() {
            return cy.get('[data-cy="bedroom-description"]').find('select');
        },
        get homeOfficeOrStudy() {
            return cy.get('[data-cy="home-office-or-study"]');
        },
        get homeOfficeOrStudySelectOption() {
            return cy.get('[data-cy="home-office-or-study"]').find('select');
        },
        get homeOfficeOrStudyDescription() {
            return cy.get('[data-cy="home-office-or-study-description"]');
        },
        get homeOfficeOrStudyDescriptionSelectOption() {
            return cy.get('[data-cy="home-office-or-study-description"]').find('select');
        },
        get bedroomNotes() {
            return cy.get('[data-cy="bedroom_notes"]');
        },
        get bedroomNotesSelectOption() {
            return cy.get('[data-cy="bedroom_notes"]').find('select');
        },
        get addBedrooms() {
            return cy.get('[data-cy="add-bedrooms"]');
        },
        get removeBedrooms() {
            return cy.get('[data-cy="remove-bedrooms"]')
        },
        get addHomeOffice() {
            return cy.get('[data-cy="add-home-office"]');
        },
        get removeHomeOffice() {
            return cy.get('[data-cy="remove-home-office"]');
        },
    }
}

export const KitchenTab = {
    elements: {
        get kitchenLayout() {
            return cy.get('[data-cy="kitchen-layout"]');
        },
        get kitchenLayoutSelectOption() {
            return cy.get('[data-cy="kitchen-layout"]').find('select');
        },
        get age() {
            return cy.get('[data-cy="age"]');
        },
        get ageSelectOption() {
            return cy.get('[data-cy="age"]').find('select');
        },
        get quality() {
            return cy.get('[data-cy="quality"]');
        },
        get qualitySelectOption() {
            return cy.get('[data-cy="quality"]').find('select');
        },
        get appliances() {
            return cy.get('[data-cy="appliances"]');
        },
        get appliancesSelectOption() {
            return cy.get('[data-cy="appliances"]').find('select');
        },
        get benchAndSink() {
            return cy.get('[data-cy="bench-and-sink"]');
        },
        get benchAndSinkSelectOption() {
            return cy.get('[data-cy="bench-and-sink"]').find('select');
        },
        get floor() {
            return cy.get('[data-cy="floor"]');
        },
        get floorSelectOption() {
            return cy.get('[data-cy="floor"]').find('select');
        },
        get kitchenNotes() {
            return cy.get('[data-cy="kitchen-notes"]');
        },
        get kitchenNotesTestArea() {
            return cy.get('[data-cy="kitchen-notes"]').find('textarea');
        },
    }
}

export const BathroomsTab = {
    elements: {
        get mainBathroom() {
            return cy.get('[data-cy="main-bathroom"]');
        },
        get mainBathroomSelectOption() {
            return cy.get('[data-cy="main-bathroom"]').find('select');
        },
        get mainBathroomDescription() {
            return cy.get('[data-cy="main-bathroom-description"]');
        },
        get mainBathroomDescriptionSelectOption() {
            return cy.get('[data-cy="main-bathroom-description"]').find('select');
        },
        get mainBathroomAge() {
            return cy.get('[data-cy="main-bathroom-age"]');
        },
        get mainBathroomAgeSelectOption() {
            return cy.get('[data-cy="main-bathroom-age"]').find('select');
        },
        get mainBathroomQuality() {
            return cy.get('[data-cy="main-bathroom-quality"]');
        },
        get mainBathroomQualitySelectOption() {
            return cy.get('[data-cy="main-bathroom-quality"]').find('select');
        },
        get ensuit() {
            return cy.get('[data-cy="ensuit"]');
        },
        get ensuitSelectOption() {
            return cy.get('[data-cy="ensuit"]').find('select');
        },
        get ensuitDescription() {
            return cy.get('[data-cy="ensuit-description"]');
        },
        get ensuitDescriptionSelectOption() {
            return cy.get('[data-cy="ensuit-description"]').find('select');
        },
        get ensuitAge() {
            return cy.get('[data-cy="ensuit-age"]');
        },
        get ensuitAgeSelectOption() {
            return cy.get('[data-cy="ensuit-age"]').find('select');
        },
        get ensuitQuality() {
            return cy.get('[data-cy="ensuit-quality"]');
        },
        get ensuitQualitySelectOption() {
            return cy.get('[data-cy="ensuit-quality"]').find('select');
        },
        get bathroomOrToilet() {
            return cy.get('[data-cy="bathroom-or-toilet"]');
        },
        get bathroomOrToiletSelectOption() {
            return cy.get('[data-cy="bathroom-or-toilet"]').find('select');
        },
        get bathroomOrToiletDescription() {
            return cy.get('[data-cy="bathroom-or-toilet-description"]');
        },
        get bathroomOrToiletDescriptionSelectOption() {
            return cy.get('[data-cy="bathroom-or-toilet-description"]').find('select');
        },
        get bathroomNotes() {
            return cy.get('[data-cy="bathroom-notes"]');
        },
        get bathroomNotesTextArea() {
            return cy.get('[data-cy="bathroom-notes"]').find('textarea');
        },
        get removeBathroom() {
            return cy.get('[data-cy="remove-bathroom"]');
        },
        get addBathroom() {
            return cy.get('[data-cy="add-bathroom"]');
        },
    }
}

export const GaragingTab = {
    elements: {
        get garageType() {
            return cy.get('[data-cy="garage-type"]');
        },
        get garageTypeSelectOption() {
            return cy.get('[data-cy="garage-type"]').find('select');
        },
        get garageDescription() {
            return cy.get('[data-cy="garage-description"]');
        },
        get garageDescriptionSelectOption() {
            return cy.get('[data-cy="garage-description"]').find('select');
        },
        get garageAge() {
            return cy.get('[data-cy="garage-age"]');
        },
        get garageAgeSelectOption() {
            return cy.get('[data-cy="garage-age"]').find('select');
        },
        get garageModernisation() {
            return cy.get('[data-cy="garage-modernisation"]');
        },
        get garageModernisationSelectOption() {
            return cy.get('[data-cy="garage-modernisation"]').find('select');
        },
        get garageFloorArea() {
            return cy.get('[data-cy="garage-floor-area"]');
        },
        get garageFloorAreaInputBox() {
            return cy.get('[data-cy="garage-floor-area"]').find('input');
        },
        get garageExteriorCladding() {
            return cy.get('[data-cy="garage-exterior-cladding"]');
        },
        get garageExteriorCladdingSelectOption() {
            return cy.get('[data-cy="garage-exterior-cladding"]').find('select');
        },
        get garageRoofConstruction() {
            return cy.get('[data-cy="garage-roof-construction"]');
        },
        get garageRoofConstructionSelectOption() {
            return cy.get('[data-cy="garage-roof-construction"]').find('select');
        },
        get garageFoundation() {
            return cy.get('[data-cy="garage-foundation"]');
        },
        get garageFoundationSelectOption() {
            return cy.get('[data-cy="garage-foundation"]').find('select');
        },
        get garageNotes() {
            return cy.get('[data-cy="garage-notes"]');
        },
        get garageNotesTextArea() {
            return cy.get('[data-cy="garage-notes"]').find('textarea');
        },
        get removeGarrage() {
            return cy.get('[data-cy="remove-field"]');
        },
        get addGarrage() {
            return cy.get('[data-cy="add-field"]')
        },
        get otherBuildings() {
            return cy.get('[data-cy="other-buildings"]');
        },
        get otherBuildingsSelectOption() {
            return cy.get('[data-cy="other-buildings"]').find('select');
        },
        get otherBuildingsDescription() {
            return cy.get('[data-cy="other-buildings-description"]');
        },
        get otherBuildingsDescriptionSelectOption() {
            return cy.get('[data-cy="other-buildings-description"]').find('select');
        },
        get otherBuildingsAge() {
            return cy.get('[data-cy="other-buildings-age"]');
        },
        get otherBuildingsAgeSelectOption() {
            return cy.get('[data-cy="other-buildings-age"]').find('select');
        },
        get otherBuildingsModernisation() {
            return cy.get('[data-cy="other-buildings-modernisation"]');
        },
        get otherBuildingsModernisationSelectOption() {
            return cy.get('[data-cy="other-buildings-modernisation"]').find('select');
        },
        get otherBuildingsFloorArea() {
            return cy.get('[data-cy="other-buildings-floor-area"]');
        },
        get otherBuildingsFloorAreaInputBox() {
            return cy.get('[data-cy="other-buildings-floor-area"]').find('input');
        },
        get otherBuildingsExteriorCladding() {
            return cy.get('[data-cy="other-buildings-exterior-cladding"]');
        },
        get otherBuildingsExteriorCladdingSelectOption() {
            return cy.get('[data-cy="other-buildings-exterior-cladding"]').find('select');
        },
        get otherBuildingsRoofConstruction() {
            return cy.get('[data-cy="other-buildings-roof-construction"]');
        },
        get otherBuildingsRoofConstructionSelectOption() {
            return cy.get('[data-cy="other-buildings-roof-construction"]').find('select');
        },
        get otherBuildingsFoundation() {
            return cy.get('[data-cy="other-buildings-foundation"]');
        },
        get otherBuildingsFoundationSelectOption() {
            return cy.get('[data-cy="other-buildings-foundation"]').find('select');
        },
        get otherBuildingsNotes() {
            return cy.get('[data-cy="other-buildings-notes"]');
        },
        get otherBuildingsNotesTextArea() {
            return cy.get('[data-cy="other-buildings-notes"]').find('textarea');
        },
        get removeOtherBuildings() {
            return cy.get('[data-cy="remove-other-buildings"]');
        },
        get addOtherBuildings() {
            return cy.get('[data-cy="add-other-buildings"]');
        },

    }
}

export const ImprovementsTab = {
    elements: {
        get majorSiteImprovements() {
            return cy.get('[data-cy="major-site-improvements"]');
        },
        get majorSiteImprovementsSelectOption() {
            return cy.get('[data-cy="major-site-improvements"]').find('select');
        },
        get majorSiteImprovementsDescription() {
            return cy.get('[data-cy="major-site-improvements-description"]');
        },
        get majorSiteImprovementsDescriptionSelectOption() {
            return cy.get('[data-cy="major-site-improvements-description"]').find('textarea');
        },
        get removeMajorSiteImprovement() {
            return cy.get('[data-cy="remove-major-site-improvement"]');
        },
        get addMajorSiteImprovement() {
            return cy.get('[data-cy="add-major-site-improvement"]');
        },
        get minorSiteImprovements() {
            return cy.get('[data-cy="minor-site-improvements"]');
        },
        get minorSiteImprovementsSelectOption() {
            return cy.get('[data-cy="minor-site-improvements"]').find('select');
        },
        get driveway() {
            return cy.get('[data-cy="driveway"]');
        },
        get drivewaySelectOption() {
            return cy.get('[data-cy="driveway"]').find('select');
        },
        get landscaping() {
            return cy.get('[data-cy="landscaping"]');
        },
        get landscapingSelectOption() {
            return cy.get('[data-cy="landscaping"]').find('select');
        },
        get fencing() {
            return cy.get('[data-cy="fencing"]');
        },
        get fencingSelectOption() {
            return cy.get('[data-cy="fencing"]').find('select');
        },
        get improvementsNotes() {
            return cy.get('[data-cy="improvements-notes"]');
        },
        get improvementsNotesTextArea() {
            return cy.get('[data-cy="improvements-notes"]').find('textarea');
        },
    }
}
export const Comparables = {
    elements: {
        get compsList() {
            return cy.get('[data-container="compsList"]');
        },
        get compsMap() {
            return cy.get('[data-container="compsMap"]');
        },
        get propertyAddress() {
            return cy.get('[data-cy="property-address"]');
        },
        get comparablesListBack() {
            return cy.get('.comparablesList-back');
        },
    }
}

export const JobInfo = {
    elements: {
        get siteInspectionNotes() {
            return cy.get('[data-cy="site-inspection-notes"]');
        },
        get inspectionType() {
            return cy.get('[data-cy="inspection-type"]');
        },
        get siteInspectionNotes() {
            return cy.get('[data-cy="site-inspection-notes"]');
        },
        get inspectionDate() {
            return cy.get('[data-cy="inspection-date"]');
        },
        get inspectionTime() {
            return cy.get('[data-cy="inspection-time"]');
        },
        get valuationReportType() {
            return cy.get('[data-cy="valuation-report-type"]');
        },
        get purposeOfValuation() {
            return cy.get('[data-cy="purpose-of-valuation"]');
        },
        get valuationDate() {
            return cy.get('[data-cy="valuation-date"]');
        },
        get valuationTime() {
            return cy.get('[data-cy="valuation-time"]');
        },
        get valuer() {
            return cy.get('[data-cy="valuer"]');
        },
        get countersignedBy() {
            return cy.get('[data-cy="countersigned-by"]');
        },
        get clientName() {
            return cy.get('[data-cy="client-name"]');
        },
        get borrower() {
            return cy.get('[data-cy="borrower"]');
        },
        get lenderName() {
            return cy.get('[data-cy="lender-name"]');
        },
        get instructedBy() {
            return cy.get('[data-cy="instructed-by"]');
        },
        get extendedTo() {
            return cy.get('[data-cy="extended-to"]');
        },
        get otherInstructions() {
            return cy.get('[data-cy="other-instructions"]');
        },
    }
}
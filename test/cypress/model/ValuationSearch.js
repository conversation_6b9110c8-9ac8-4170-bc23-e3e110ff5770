import '@testing-library/cypress/add-commands';

export default {
    elements: {
        get territorialAuthorities() {
            return cy.getBySel("territorial-authorities");
        },
        get territorialAuthoritiesCheckbox() {
            return cy.getBySel("territorial-authorities").findAllByRole('checkbox');
        },
        get territorialAuthoritiesMultiselectDropDown() {
            return cy.getBySel("territorial-authorities").findAllByRole('button');
        },
        get territorialAuthoritiesFilterList() {
            return cy.getBySel("territorial-authorities").findAllByRole('textbox');
        },
        get territorialAuthoritiesErrorMessage() {
            return cy.getBySel("territorial-authorities-error-message");
        },

        get reportTypes() {
            return cy.getBySel("report-types");
        },
        get reportTypesAllActive() {
            return cy.getBySel("report-types-all-active");
        },
        get reportTypesClear() {
            return cy.getBySel("report-types-clear");
        },
        get reportTypesMultiselect() {
            return cy.get('[data-cy="report-types-dropdown"] .multiselect__tags-wrap > span');
        },
        get reportTypesDropdown() {
            return cy.getBySel('report-types-dropdown');
        },
        get reportTypesDropdownMultiselect() {
            return cy.getBySel('report-types-dropdown').findAllByRole('option');
        },


        get jobStatus() {
            return cy.getBySel("job-status");
        },
        get jobStatusAllActive() {
            return cy.getBySel("job-status-all-active");
        },
        get jobStatusClear() {
            return cy.getBySel("job-status-clear");
        },
        get jobStatusMultiselect() {
            return cy.get('[data-cy="job-status-dropdown"] .multiselect__tags-wrap > span');
        },
        get jobStatusDropdown() {
            return cy.getBySel('job-status-dropdown');
        },
        get jobStatusDropdownMultiselect() {
            return cy.getBySel('job-status-dropdown').findAllByRole('option');
        },


        get valuers() {
            return cy.getBySel('valuation-valuers').children().eq(1);
        },
        get valuersDropdownBox() {
            return cy.getBySel('valuation-valuers-dropdown');
        },
        get selectValuersOptions1() {
            return cy.getBySel('valuation-valuers-dropdown').children().eq(2).children().eq(0).children().eq(0).find('span').children().eq(0);
        },
        get selectValuersOptions2() {
            return cy.getBySel('valuation-valuers-dropdown').children().eq(2).children().eq(0).children().eq(1).find('span').children().eq(0);
        },
        get selectValuersOptions3() {
            return cy.getBySel('valuation-valuers-dropdown').children().eq(2).children().eq(0).children().eq(2).find('span').children().eq(0);
        },
        get valuersSelected() {
            return cy.getBySel('valuation-valuers-dropdown').children().eq(1).children().eq(0);
        },
        get clearValuers() {
            return cy.get('[data-cy="valuation-valuers"] [data-cy="valuation-valuers-clear"]');
        },


        get inspectionDate() {
            return cy.getBySel("inspection-date");
        },
        get inspectionDateFrom() {
            return cy.getBySel("inspection-date").children().eq(0).find('input');
        },
        get inspectionDateTo() {
            return cy.getBySel("inspection-date").children().eq(2).find('input');
        },
        get inspectionDateErrorMessage() {
            return cy.getBySel("valuation-inspection-date-error-message");
        },

        get exportButton() {
            return cy.getBySel("export-button");
        },
        get clearButton() {
            return cy.getBySel("clear-btn");
        },
        get searchButton() {
            return cy.getBySel("search-btn");
        },
    },
    
};

export default {
    get mainIndexWindow() { 
        return 'div[data-cy=rtvRuralIndexMainIndex]' 
    },
    get leftHeader() {
        return 'thead[data-cy=mainIndexHeader1]'
    },
    get rightHeader() {
        return 'thead[data-cy=mainIndexHeader2]'
    },
    get locationHeaderCell() {
        return 'th[data-cy=mainIndexLocationHeaderCell]'
    },
    get salesGroupHeaderCell() {
        return 'th[data-cy=mainIndexSalesGroupHeaderCell]'
    },
    get salesGroupCodeHeaderCell() {
        return 'th[data-cy=mainIndexSalesGroupCodeHeaderCell]'
    },
    get rollHeaderCell() {
        return 'th[data-cy=mainIndexRollHeaderCell]'
    },
    get titleHeaderCell() {
        return 'th[data-cy=mainIndexTitleHeaderCell]'
    },
    get categoryHeaderCell() {
        return 'th[data-cy=mainIndexCategory{0}HeaderCell]'
    },
    get categoryLandIndexHeaderCell() {
        return 'th[data-cy=mainIndexCategory{0}LandIndexHeaderCell]'
    },
    get categoryImprovementIndexHeaderCell() {
        return 'th[data-cy=mainIndexCategory{0}ImprovementIndexHeaderCell]'
    },
    get categoryLandPercentIndexHeaderCell() {
        return 'th[data-cy=mainIndexCategory{0}LandPercentIndexHeaderCell]'
    },
    get categoryLandLumpSumIndexHeaderCell() {
        return 'th[data-cy=mainIndexCategory{0}LandLumpSumIndexHeaderCell]'
    },
    get categoryImprovementPercentIndexHeaderCell() {
        return 'th[data-cy=mainIndexCategory{0}ImprovementPercentIndexHeaderCell]'
    },
    get categoryImprovementLumpSumIndexHeaderCell() {
        return 'th[data-cy=mainIndexCategory{0}ImprovementLumpSumIndexHeaderCell]'
    },
    get leftBody() {
        return 'tbody[data-cy=mainIndexBody1]'
    },
    get mainBody() {
        return 'tbody[data-cy=mainIndexBody2]'
    },
    get mainBodyCells() {
        return 'td[data-cy=mainIndexCells]'
    },
    get lvPercentInputs() {
        return 'input[data-cy=mainIndexLvPercentInput]'
    },
    get lvLumpSumInputs() {
        return 'input[data-cy=mainIndexLvLumpSumInput]'
    },
    get viPercentInputs() {
        return 'input[data-cy=mainIndexViPercentInput]'
    },
    get viLumpSumInputs() {
        return 'input[data-cy=mainIndexViLumpSumInput]'
    },
};

export default {
    get propertySearch() {
        return cy.get('label[for="propertySearch-tab"]');
    },

    get salesSearch() {
        return cy.get('label[for="salesSearch-tab"]');
    },

    get taRangeFrom() {
        return cy.get('#fromtarange');
    },

    get taRangeTo() {
        return cy.get('#totarange');
    },

    get streetNumFrom() {
        return cy.get('#from-streetNumber');
    },

    get streetNumTo() {
        return cy.get('#to-streetNumber');
    },

    get streetName() {
        return cy.get('#streetName');
    },

    get streetTypeMultiselect() {
        return cy.get('[data-cy="street-type"]');
    },

    get regionTypeMultiSelect() {
        return cy.get('[data-cy="region-type"]');
    },

    get searchButton() {
        return cy.get('#advSearchBtn');
    },

    get clearButton() {
        return this.searchButton.next();
    },

    search() {
        this.searchButton.click({ force: true });
    },

    clear() {
        this.clearButton.click({ force: true });
    },

    streetType(type) {
        this.streetTypeMultiselect.within(() => {
            cy.get('.caret').click();
            cy.contains(type).click();
        });
    },

    regionType(type) {
        this.regionTypeMultiSelect.within(() => {
            cy.get('.caret').click();
            cy.contains(type).click();
            cy.get('.caret').click();
        });
    },
};

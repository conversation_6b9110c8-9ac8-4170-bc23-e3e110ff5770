Cypress.Commands.add('overrideUserData', (overrides = {}) => {
    cy.intercept('/fetchUserData', (req) => {
        req.continue((res) => {
            console.log(res.body);
            res.body = {...res.body, ...overrides}
        });
    }).as('overrideUserData');
});

Cypress.Commands.add('overrideDisplayUser', (overrides = {}) => {
    cy.intercept('/displayUserByUsername', (req) => {
        req.continue((res) => {
            console.log(res.body)
            res.body = {...res.body, ...overrides}
        });
    }).as('overrideDisplayUser');
});

Cypress.Commands.add('overrideInternalUserData', (overrides = {}) => {
    cy.intercept('GET', '/displayUserByUsername/*', (req) => {
        req.continue((res) => {
            console.log(res.body);
            res.body = {...res.body, ...overrides};
        });
    }).as('overrideInternalUserData');
 });

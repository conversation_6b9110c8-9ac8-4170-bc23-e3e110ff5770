import './commands'
import '../../../vue/global.scss'
import '../../../public/stylesheets/application.css'
import '../../../public/stylesheets/desktop.css'
import '../../../vue/components/rollMaintenance/rollMaintenance.scss'

import { mount } from 'cypress/vue2'

window.jsRoutes = {
    controllers: {}
};

Cypress.Commands.add('mount', (...args) => {
    return mount(...args).then(({wrapper}) => {
        Cypress.log({
            name: 'Mounted component',
            displayName: 'mounted',
            message: `${args[0].__name} component mounted`,
            consoleProps() {
                return args[1];
            }
        });

        return cy.wrap(wrapper).as('vue');
    })
})

// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

import Monarch from '../model/Monarch.js';
import './intercepts.js';
import './jsRoutes.js';

Cypress.Commands.add('doubleLog', input => {
    cy.log(input);
    console.log(input);
});

Cypress.Commands.add('visitQpid', inputQpid => {
    const url = `${env.config.baseUrl}property/${inputQpid}/summary`; //  `property/property?qupid=${inputQpid}`;
    cy.doubleLog(url);
    cy.visitWithLogin(url);
});

Cypress.Commands.add("getBySel", (selector, ...args) => {
    return cy.get(`[data-cy=${selector}]`, ...args);
})

Cypress.Commands.add("getBySelLike", (selector, ...args) => {
    return cy.get(`[data-cy*=${selector}]`, ...args);
})

Cypress.Commands.add('waitForPropertyInfo', () => {
    cy.intercept(`${env.config.baseUrl}getPropertyInfo/*`).as('propertyInfo');
    cy.wait('@propertyInfo');
});

Cypress.Commands.add('clearErrorBoxes', { prevSubject: 'optional' }, (prevSubject) => {
    Monarch.clearErrorBoxes();
});


Cypress.Commands.add('visitWithLogin', (qvURL, user = env.config.internalUser) => {
    loginUserRobustly(user);
    cy.visit(qvURL);
});

Cypress.Commands.add('visitWithUser', (qvURL, user) => {
    loginUserRobustly(user);
    cy.visit(qvURL);
});

Cypress.Commands.add('loginAutomatically', (user = env.config.internalUser) => {
    loginUserRobustly(user);
});

Cypress.Commands.add('login', (user = env.config.internalUser) => {
    loginUserRobustly(user);
});

Cypress.Commands.add('logout', () => {
    // cy.clearLocalStorage();
});

function loginUserRobustly(user) {
    cy.session(user, () => {
        const baseDomain = extractBaseUrl(env.config.baseUrl);
        const loginDomainExtracted = extractLoginDomain(env.config.loginDomain);
        if (baseDomain === loginDomainExtracted) {
            fillAuth0Form({ env, user });
        } else {
            cy.origin(env.config.loginDomain, { args: { env, user } }, fillAuth0Form);
        }
    }, {
        cacheAcrossSpecs: true,
    });
}
function extractBaseUrl(baseUrl) {
    const domain = baseUrl.replace(/https?:\/\//, '').split('/')[0];
    return domain;
}

function extractLoginDomain(loginString) {
    const parts = loginString.split('.').slice(1);
    return parts.join('.');
}

function fillAuth0Form({ env, user }) {
    cy.visit(env.config.baseUrl);
    const auth0Form = cy.get('#auth0-lock-container-1', { timeout: 2000 });
    auth0Form.get('input[name=password]').clear().type(user.password, { log: false });
    const usernameField = auth0Form.get('input[name=username]').clear();
    usernameField.type(user.username);
    if (user.isDomainUser) {
        usernameField.type(`@qv.co.nz`);
        auth0Form.get('button[name=submit]').click();
    }
    auth0Form.get('button[name=submit]').click();
    cy.wait(1000);
}

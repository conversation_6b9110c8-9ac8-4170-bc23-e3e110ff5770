function getHeaders() {
    return {
        'Content-Type': 'application/json',
        'x-api-key': env.config.testFactory.key,
    };
}

export function getGenerateRequest(body) {
    return {
        method: 'POST',
        url: env.config.testFactory.url + '/generate',
        headers: getHeaders(),
        body,
        timeout: 30000,
    };
}

async function requestGenerate(inputBody) {
    const myHeaders = getHeaders();
    return cy.request({
        method: 'POST',
        url: env.config.testFactory.url + '/generate',
        headers: myHeaders,
        body: inputBody,
        timeout: 30000,
    }).then(res => {
        const handledResponse = handleResponse(res);
        cy.log('handledResponse', handledResponse);
        return handledResponse;
    });
}

function handleResponse(response) {
    if (!response) {
        throw new Error('ERR-MWT-0e9f1b: No response');
    }

    if (!response.body) {
        throw new Error('ERR-MWT-febc4b: No response body: ' + response);
    }

    if (response.body.status !== 'SUCCESS') {
        throw new Error('ERR-MWT-8f1c0b: Status is not success: ' + response.body.status);
    }

    if (response.body.data.length === 0) {
        throw new Error('ERR-MWT-51880e: No data found');
    }

    return response.body.data;
}

export function createResidentialPropertyBody() {
    return [{ type: "property" }];
}

export function createCommercialPropertyBody() {
    return [{
        type: "property",
        attributes: {
            categoryType: "commercial",
        }
    }];
}

export function createCommercialPropertyWithWorksheetBody() {
    return [{
        type: "property",
        attributes: {
            categoryType: "commercial"
        },
        children: [{
            type: "commercialWorksheet"
        }]
    }];
}

import Vuex from 'vuex';
import Vue from 'vue';
import classifications from '../../../vue/store/classifications';

export function createMockStore(classifications = {}) {
    Vue.use(Vuex);

    const store = new Vuex.Store({
        state: {
            classificationsLoaded: true,
        },
        mutations: {
            definition: function(state, value) {
                state.application = value;
            },
            allUsers: function(state, value) {
                state.users = value;
            },
            classificationsLoaded: function(state, value) {
                state.classificationsLoaded = value;
            }
        },
        modules: {
            classifications: {
                namespaced: false,
                state: {
                    classifications: {},
                    loaded: true,
                },
                getters: {
                    getCategoryClassifications: (state) => (category) => {
                        if (classifications[category]) {
                            return classifications[category];
                        }

                        return [
                            {
                                category,
                                code: '1',
                                description: 'Test ' + category,
                            },
                            {
                                category,
                                code: '2',
                                description: 'Test ' + category,
                            }
                        ];
                    },
                    classificationsLoaded: (state) => {
                        return true;
                    },
                },
            },
        }
    });
    return store;
}

// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands.js';
import 'cypress-mochawesome-reporter/register';
import 'cypress-fail-fast';
import 'cypress-dark';

global.env = Cypress.env();

Cypress.on('uncaught:exception', (err, runnable) => {
    // returning false here prevents <PERSON><PERSON> from failing the test
    return false;
});

// avoid setting a before() at the global level
// as it  will interfere with combining spec files
// before(() => {
// });

// avoid setting a beforeEach() at the global level
// as it  will interfere with combining spec files
// beforeEach(() => {
// });


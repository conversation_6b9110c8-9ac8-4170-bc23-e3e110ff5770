function getInputRequest(body) {
    const headers = {
        'Content-Type': 'application/json',
        'x-api-key': env.config.testFactory.key,
    };
    const inputRequestBody = {
        method: 'POST',
        url: env.config.testFactory.url + '/generate',
        headers,
        body,
        failOnStatusCode: true,
        timeout: 45000,
    };
    return inputRequestBody;
}

export function getProperty() {
    const inputBody = [
        {
            type: 'property',
        },
    ];
    return getInputRequest(inputBody);
}

export function getPropertyWithSale() {
    const inputBody = [
        {
            type: 'property',
            children: [
                {
                    type: 'sale',
                },
            ],
        },
    ];
    return getInputRequest(inputBody);
}

export function addSaleToProperty(qpid) {
    const inputBody = [
        {
            type: 'property',
            id: qpid,
            children: [
                {
                    type: 'sale',
                },
            ],
        },
    ];
    return getInputRequest(inputBody);
}

export function getPropertyWithBuildingConsent(status = '') {
    const inputBody = [
        {
            type: 'property',
            children: [
                {
                    type: 'buildingConsent',
                    attributes: {
                        status,
                    },
                },
            ],
        },
    ];
    return getInputRequest(inputBody);
}

export function addBuildingConsentToProperty(qpid, status = '') {
    const inputBody = [
        {
            type: 'property',
            id: qpid,
            children: [
                {
                    type: 'buildingConsent',
                    attributes: {
                        status,
                    },
                },
            ],
        },
    ];
    return getInputRequest(inputBody);
}

export function getPropertyWithBuildingConsentInputBody(status = '') {
    const inputBody = [
        {
            type: 'property',
            children: [
                {
                    type: 'buildingConsent',
                    attributes: {
                        status,
                    },
                },
            ],
        },
    ];
    return getInputRequest(inputBody);
}

export function addBuildingConsentToPropertyInputBody(qpid, status = '') {
    const inputBody = [
        {
            type: 'property',
            id: qpid,
            children: [
                {
                    type: 'buildingConsent',
                    attributes: {
                        status,
                    },
                },
            ],
        },
    ];
    return getInputRequest(inputBody);
}

export function getPropertyWithObjectionInputBody(categoryCode) {
    const inputBody = [
        {
            type: 'property',
            attributes: {},
            children: [
                {
                    type: 'objection',
                },
            ],
        },
    ];
    if (categoryCode) {
        inputBody[0].attributes.categoryCode = categoryCode;
    }
    return getInputRequest(inputBody);
}

export function addObjectionToPropertyInputBody(qpid) {
    const inputBody = [
        {
            type: 'property',
            id: qpid,
            children: [
                {
                    type: 'objection',
                },
            ],
        },
    ];
    return getInputRequest(inputBody);
}

export function getComparableSales(propertyAttributes = {}, saleAttributes = {}) {
    const inputBody = [
        {
            type: 'property',
            attributes: propertyAttributes,
            children: [
                {
                    type: 'buildingConsent',
                },
                {
                    type: 'sale',
                    attributes: saleAttributes,
                },
            ],
        },
    ];
    return getInputRequest(inputBody);
}



export default {
    getProperty,
    getPropertyWithSale,
    addSaleToProperty,
    getPropertyWithBuildingConsent,
    addBuildingConsentToProperty,
    getPropertyWithBuildingConsentInputBody,
    addBuildingConsentToPropertyInputBody,
    getPropertyWithObjectionInputBody,
    addObjectionToPropertyInputBody,
    getComparableSales
};

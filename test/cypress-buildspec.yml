version: 0.2

batch:
  fast-fail: false
  build-matrix:
    static:
      ignore-failure: false
      env:
        type: LINUX_CONTAINER
        privileged_mode: true
        compute-type: BUILD_GENERAL1_MEDIUM
    dynamic:
      env:
        shell: bash
        compute-type:
          - BUILD_GENERAL1_MEDIUM
        variables: # - "UI - Edge Desktop|edge|cypress/e2e/bc-job/*|a=1"
          CY_GROUP_SPEC:
            - "UI - Building Consent|chrome|cypress/e2e/*/**.cy.js|a=1"
#             - "UI - Property Summary|chrome|cypress/e2e/advanced-property-search/*|a=1"
        exported-variables:
          - REPORT_VIEW_URL

phases:
  install:
    commands:
      - PROJECT_NAME=monarch-web
      - SLACK_WEBHOOK=$(aws ssm get-parameter --name "/dev/deploy/slack-webhook" --query 'Parameter.Value' --output text)
      - CYPRESS_CONFIG=$(aws ssm get-parameter --name "/$ENV/$PROJECT_NAME/cypress-config" --query 'Parameter.Value' --output text)
      - BUILD_BRANCH=$(git branch -a --contains HEAD | sed -n 2p | awk '{ printf $1 }' | sed 's/remotes\/origin\///')
      - BUILD_VERSION=$(git describe --tags)
      - COMMIT_EMAIL=$(git log -1 --pretty=format:'%ae')
      - COMMIT_MESSAGE="$(git log -1 --pretty=%B)"
      - COMMIT_SHA=$(git rev-parse HEAD)
      - SLACK_USER=$(echo $COMMIT_EMAIL | cut -d'@' -f 1)
      - TEST_TYPE="cypress"
      - REPORT_DIR=/report_output
      - CY_BROWSER=$(echo $CY_GROUP_SPEC | cut -d'|' -f2)
      - CY_SPEC=$(echo $CY_GROUP_SPEC | cut -d'|' -f3)
      - wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb >/dev/null 2>&1
      - apt update -y > /dev/null 2>&1
      - apt install -y libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libnss3 libxss1 libasound2 libxtst6 xauth xvfb ./google-chrome-stable_current_amd64.deb > /dev/null 2>&1
      - find . -name 'package-lock.json' -delete
      - cat package.json | grep -v 'qv-maps' > package.json.tmp
      - mv package.json.tmp package.json
      - n 12.22.12
      - npm install >/dev/null 2>&1
      - cd test
      - n 16.20.2
      - npm install >/dev/null 2>&1
      - mkdir -p $REPORT_DIR
      - echo "export default $CYPRESS_CONFIG;" > config.js
  build:
    commands:
      - npx cypress run --browser $CY_BROWSER --spec "$CY_SPEC" | tee $REPORT_DIR/cypress.txt
  post_build:
    commands:
      - aws s3 cp s3://qv-deployment/build-scripts/create_test_report.sh create_test_report.sh && chmod +x create_test_report.sh
      - ./create_test_report.sh "$PROJECT_NAME" "$ENV" "$TEST_TYPE" "$BUILD_BRANCH" "$BUILD_VERSION" "$COMMIT_SHA"
      - REPORT_FOLDER="$(jq -r '.body.data.folder' response.json)"
      - REPORT_ID="$(jq -r '.body.data.reportId' response.json)"
      - REPORT_VIEW_URL="https://launchpad.internal.quotablevalue.co.nz/test-reports?reportId=$REPORT_ID"
      - REPORT_BUCKET="s3://$REPORT_FOLDER"
      - if [ -n "$REPORT_FOLDER" ]; then echo "REPORT_FOLDER is set and not empty"; else echo "REPORT_FOLDER is unset or empty"; exit 1; fi
      - pwd
      - ls cypress
      - cp -r cypress/reports $REPORT_DIR
      - cp -r cypress/screenshots $REPORT_DIR || true
      - ls $REPORT_DIR
      - aws s3 cp $REPORT_DIR $REPORT_BUCKET --recursive
      - |
        echo '
          APP=:testing:
          ICON=:rocket:
          BUCKET=:bucket:
          CHANNEL='#builds'
          curl -X POST --data-urlencode "payload={\"channel\":\"$CHANNEL\",\"username\":\"$PROJECT_NAME - $BUILD_BRANCH\",\"text\":\"cypress test report \\n :testing: launchpad <$REPORT_VIEW_URL|$REPORT_ID> \\n :bucket: s3 $REPORT_BUCKET \",\"icon_emoji\":\"$ICON\"}" $SLACK_WEBHOOK
        ' > ~/slack.sh
      - chmod +x ~/slack.sh
      - ~/slack.sh || true
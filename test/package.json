{"name": "test", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "engines": {"node": "16"}, "engineStrict": true, "scripts": {"start": "npx cypress open --e2e --browser chrome || exit 0", "start:local": "set ENV=local&& npx cypress open --e2e --browser chrome || exit 0", "start:dev": "set ENV=dev&& npx cypress open --e2e --browser chrome || exit 0", "start:lsa4": "set ENV=lsa4&& npx cypress open --e2e --browser chrome || exit 0", "cypress:local": "set ENV=local&& npx cypress open || exit 0", "cypress:dev": "set ENV=dev&& npx cypress open || exit 0", "cypress:test": "set ENV=test&& npx cypress open || exit 0", "cli:dev": "npx cypress run --browser chrome || exit 0", "cli:test": "set ENV=test&& npx cypress run --browser chrome || exit 0", "cli:dev-valuation": "set ENV=dev&& npx cypress run --spec \"cypress/integration/valuation-job/*\" --browser chrome || exit 0", "cli:test-valuation": "set ENV=test&& npx cypress run --spec \"cypress/integration/valuation-job/*\" --browser chrome || exit 0", "cli:dev-smoke": "set ENV=dev&& npx cypress run --spec \"cypress/integration/smoke-tests/*\" --browser chrome || exit 0", "cli:test-smoke": "set ENV=test&& npx cypress run --spec \"cypress/integration/smoke-tests/*\" --browser chrome || exit 0", "cli:prod-smoke": "set ENV=prod&& npx cypress run --spec \"cypress/integration/smoke-tests/*\" --browser chrome || exit 0", "cli:dev:1": "set ENV=dev&& npx cypress run --spec \"cypress/integration/smoke-tests/RollMaintenance.test.js\" || exit 0", "cli:test:1": "set ENV=test&& npx cypress run --spec \"cypress/integration/smoke-tests/RollMaintenance.test.js\" || exit 0", "component:draft-property-details": "npx cypress run --spec \"cypress/component/draft-property-details/*\" --component", "component:rating-valuation": "npx cypress run --spec \"cypress/component/rating-valuation/*\" --component"}, "author": "", "license": "ISC", "devDependencies": {"@testing-library/cypress": "^10.0.1", "cypress": "^13.7.1", "cypress-dark": "^1.8.3", "cypress-fail-fast": "^7.0.3", "cypress-file-upload": "^5.0.8", "cypress-mochawesome-reporter": "^3.5.1", "cypress-real-events": "^1.13.0", "dateformat": "^5.0.3"}}
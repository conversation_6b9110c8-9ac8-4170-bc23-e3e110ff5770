export default {
    baseUrl: 'https://dev.qvmonarch.co.nz/',
    loginDomain: 'login.dev.qvmonarch.co.nz',
    testFactory: {
        url: 'https://dev.qvapi.co.nz/testfactory',
        key: 'KQWE4uhGD13ZdREtn30vy4B0LuXOgyVF76QueBma',
    },
    internalUser: {
        isDomainUser: true,
        username: 'TestUserInt1',
        password: '',
    },
    internalUserStandard: {
        isDomainUser: true,
        username: 'TestUserInt2',
        password: '',
    },
    internalUserCustomerCare: {
        isDomainUser: true,
        username: 'TestUserInt3',
        password: '',
    },
    externalUser: {
        isDomainUser: false,
        username: 'TestUserExt1',
        password: '',
    },
};
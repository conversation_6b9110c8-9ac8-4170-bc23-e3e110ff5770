# Integration

---

## bc-job

### BCJob.test.js

Creates new BC Job.

---

## sales-analysis

### SalesAnalysis.test.js

Views Sales Analysis page.

---

## smoke-tests

All read-only tests that do not alter data. Designed to be run in Prod

### ExportResults.test.js

Clicks the "Export Results" Button at four different places in website, read-only.

### Home.test.js

Analyses Home page.

### PropertyDetails.test.js

Analyses Property Details page.

### RollMaintenance.test.js

Analyses Roll Maintenance page.

### SalesSearch.test.js

Analyses Sales Search page.

### Search.test.js

Analyses Search page.

### TADashboard.test.js

Analyses TA Dashboard page.

---

## valuation-job

### MarketValuation.test.js

Creates MarketValuation Report.

### ValuationJobsHomePage.test.js

Analyses home page of Valuation screen.

### VerifiedQV.test.js

Creates Verified QV Report.
const path = require('path');

const args = process.argv.slice(2);

const returnExitNum = n => {
    console.log(n);
    process.exit(n);
};

if (args[0] === undefined || args[1] === undefined) {
    returnExitNum(1);
}

const uri0 = path.resolve(__dirname, `./config.json`);
const env1 = require(uri0);

let seconds = 0;
const fiveMinutes = 5 * 60000;

const getVer = async inputEnv => {
    try {
        const response = await fetch(`${inputEnv.baseUrl}version`);
        return await response.text();
    }
    catch (error) {
        console.warn(error);
        return returnExitNum(1)
    }
};

const compareVersions = async env1 => {
    const ver1 = await getVer(env1);
    const ver2 = args[1];
    const versionsMatching = ver1 === ver2;

    return {
        versionsMatching
    };
};

const waitForVersionUpdateEveryNSeconds = async n => {
    const intervalObj = setInterval(() => {
        compareVersions(env1).then(result => {
            seconds += n * 1000;

            if (result.versionsMatching) {
                returnExitNum(0);
            }

            if (seconds > fiveMinutes) {
                clearInterval(intervalObj);
                returnExitNum(1);
            }
        });
    }, n * 1000);
};

compareVersions(env1).then(result => {
    if (result.versionsMatching) {
        returnExitNum(0);
    }

    if (seconds > fiveMinutes) {
        clearInterval(intervalObj);
        returnExitNum(1);
    }
});

waitForVersionUpdateEveryNSeconds(10);

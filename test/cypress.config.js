import { defineConfig } from 'cypress';
import plugins from './cypress/plugins/index.js';
import config from './config.js';
import webpackConfig from '../webpack.config.js';

export default defineConfig({
    env: {
        config,
        ADDRESS1_SEARCH_TERM: '15 Southwark',
        ADDRESS1_QPID: '674538',
        ADDRESS2_SEARCH_TERM: '2 Prestwich Rise',
        ADDRESS2_QPID: '3002038',
        ADDRESS3: {
            SEARCH_TERM: '17 Heatherlea Heights',
            APPORT1: {
                ADDRESS: '15 -17 Heatherlea Heights',
                QPID: '806371',
                VALREF: '14061/11800',
            },
            APPORT2: {
                ADDRESS: '17 Heatherlea Heights',
                QPID: '806372',
                VALREF: '14061/11800 A',
            },
            APPORT3: {
                ADDRESS: '15 Heatherlea Heights',
                QPID: '806373',
                VALREF: '14061/11800 B',
            },
        },
        ADDRESS4: {
            ADDRESS: '120 Main Road',
            SEARCH_TERM: '120 Main Road',
            QPID: '857601',
            VALREF: '14941/93828',
        },
        ADDRESS5: {
            ADDRESS: '213 Earnscleugh Road,   Central Otago District',
            SEARCH_TERM: '213 Earnscleugh Road,   Central Otago District',
            QPID: '1446874',
        },
        ADDRESS6: {
            ADDRESS: '3972 C State Highway 1   Northern Aupouri, Far North District',
            SEARCH_TERM:
                '3972 C State Highway 1   Northern Aupouri, Far North District',
            QPID: '3250491',
        },
        ADDRESS7: {
            ADDRESS: '15 Reservoir Road, Kaikohe, Far North District',
            SEARCH_TERM: '15 Reservoir Road, Kaikohe, Far North District',
            QPID: '49025',
        },
        ADDRESS8: {
            QPID: 3415743,
        },
        ADDRESS9: {
            QPID: 3413127,
        },
        // TODO: find a property with SRA values that are value based and split between classes
        ADDRESS10: {
            QPID: 3413127
        },
        TA_CODE: {
            CHCH: 60,
        },
        FAIL_FAST_STRATEGY: 'spec',
        FAIL_FAST_PLUGIN: false,
        ruralNoWorksheet: {
            situationNumber: 1051,
            streetName: 'Te Wharau',
            category: 'PFE',
            nsp: 165000,
            qpid: 1040563,
        },
        SALE_PROCESSING_SEARCH: {
            saleDateFrom: '01/01/2022',
            saleDateTo: '10/02/2023',
            ownershipTaCodes: ['64'],
            taCodes: ['45','46']
        },
        MOBILE_SCREEN_WIDTH: 430,
        MOBILE_SCREEN_HEIGHT: 932,
    },

    viewportWidth: 1200,
    viewportHeight: 800,
    downloadsFolder: 'cypress/downloads',
    fixturesFolder: 'cypress/fixtures',
    scrollBehavior: 'center',

    video: true,
    // videoUploadOnPasses: false,

    failOnStatusCode: false,
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    numTestsKeptInMemory: 2,
    reporter: 'cypress-mochawesome-reporter',

    reporterOptions: {
        embeddedScreenshots: true,
        reportPageTitle: 'monarch-web-test-results',
        saveJson: true,
    },

    e2e: {
        testIsolation: false,
        // We've imported your old cypress plugins here.
        // You may want to clean this up later by importing these.
        reporter: 'cypress-mochawesome-reporter',
        setupNodeEvents(on, cfg) {
            cfg.baseUrl = config.baseUrl;
            return plugins(on, cfg);
        },
    },
    component: {
        defaultCommandTimeout: 1000,
        devServer: {
            framework: 'vue',
            bundler: 'webpack',
            webpackConfig,
        },
    },
});

version: "3.9"
services:
  monarch-vue:
    image: node:14
    volumes:
      - .:/app
      - ../qv-maps:/qv-maps
    working_dir: /app
    environment:
      JAVASCRIPT_FOLDER: /app/public/javascripts
    command: sh -c "npm install --include=dev;npm run watch;"
  monarch-sbt:
    image: hseeberger/scala-sbt:graalvm-ce-21.0.0-java8_1.4.7_2.11.12
    volumes:
      - .:/app
      - sbtuser:/home/<USER>/
    working_dir: /app
    environment:
      TZ: Pacific/Auckland
    ports:
      - 9000:9000
      - 9008:9008
      - 62204:62204
    stdin_open: true
    tty: true
    command: sbt runAll
volumes:
  sbtuser:
    external: false

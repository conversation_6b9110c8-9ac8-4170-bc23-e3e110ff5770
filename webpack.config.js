const path = require('path');
const webpack = require('webpack');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const MomentLocalesPlugin = require('moment-locales-webpack-plugin');
const babelConfig = require('./babel.config');

/* eslint-disable-next-line no-new */
new webpack.EnvironmentPlugin([
    'JAVASCRIPT_FOLDER',
]);

const defaultJavascriptFolder = path.resolve(__dirname, 'public/javascripts');

const javascriptFolder = process.env.JAVASCRIPT_FOLDER || defaultJavascriptFolder;

// console.info('JAVASCRIPT_FOLDER=', javascriptFolder);

module.exports = {
    mode: 'development',
    entry: './vue/main.js',
    output: {
        path: javascriptFolder,
        publicPath: '/assets/javascripts/',
        filename: 'bundle.js',
        chunkFilename: '[name].bundle.js',
    },
    devtool: 'source-map',
    plugins: [
        new webpack.ProvidePlugin({
            $: 'jquery',
            jQuery: 'jquery',
            'window.jQuery': 'jquery',
        }),
        new MomentLocalesPlugin(),
        new VueLoaderPlugin(),
    ],
    module: {
        rules: [
            {
                test: /\.js$/,
                use: {
                    loader: 'babel-loader',
                    options: babelConfig,
                },
            },
            {
                test: /\.vue$/,
                loader: 'vue-loader',
            },
            {
                test: /\.png$/,
                loader: 'url-loader?mimetype=image/png',
            },
            {
                test: /\.gif$/,
                loader: 'url-loader?mimetype=image/gif',
            },
            {
                test: /\.svg$/,
                loader: 'url-loader',
                options: {
                    limit: 10000,
                },
            },
            {
                test: /\.css$/,
                use: [
                    'vue-style-loader',
                    'css-loader',
                ],
            },
            {
                test: /\.scss$/,
                use: [
                    'vue-style-loader',
                    'css-loader',
                    'sass-loader',
                ],
            },

            {
                test: /\.yml$/,
                use: [],
            },
            { test: /\.jsx?$/, exclude: /(node_modules|bower_components)/, loader: 'babel-loader' },
            {
                test: /\.eot(\?v=\d+\.\d+\.\d+)?$/, loader: 'file-loader', resolve: {
                    alias: {
                        '../fonts': './public/fonts',
                    }
                },
            },
            { test: /\.(woff|woff2)$/, loader: 'url-loader' },
            { test: /\.ttf(\?v=\d+\.\d+\.\d+)?$/, loader: 'url-loader' },
            { test: /\.svg(\?v=\d+\.\d+\.\d+)?$/, loader: 'url-loader', resolve: {
                    alias: {
                        '*fonts': './public/fonts',
                    }
                }
            },
            { test: /\.jpg(\?v=\d+\.\d+\.\d+)?$/, loader: 'url-loader' },
        ],
    },
    resolve: {
        extensions: ['.js', '.jsx', '.css'],
        alias: {
            '@': path.resolve(__dirname, 'vue'),
            'Common': path.resolve(__dirname, 'vue/components/common'),
            vue$: path.resolve(__dirname, "node_modules/vue/dist/vue.runtime.esm.js")
        },
    },
    watchOptions: {
        poll: true,
        ignored: /node_modules/
    },
};

$input-border-radius: 5px;
$input-border-width: 1px;
$input-border: solid $input-border-width #d2d2d2;
$input-border-readonly: $input-border;
$input-height: var(--qv-input-height);
$input-font-size: 1.2rem;
$input-background: white;
$input-background-readonly: #eeeeee;
$input-padding: 0.5rem;
$input-primary-color: var(--qv-color-mediumblue);

$checkbox-margin: 1rem;
$checkbox-size: calc($input-height - 2 * $checkbox-margin);

.qv-form-base {
  -moz-appearance: initial;
  appearance: auto;

  font-size: $input-font-size;
  border: $input-border;
  border-radius: $input-border-radius;
  height: $input-height;
  min-height: $input-height;
  background-color: $input-background;
  padding: $input-padding;
  margin: 0;

  &:focus, &[focus] {
    border: $input-border-width solid $input-primary-color;
  }
}

.qv-form-select {
  @extend .qv-form-base;
  width: 100%;

  &[readonly] {
    @extend .qv-form-base;
    background-color: $input-background-readonly;
    border: $input-border-readonly;
    pointer-events: none;
    appearance: none;
    opacity: 1;
  }
}

.qv-form-select-ta-legacy {
  &.advSearch-group input[type=text] {
    font-size: 1.2rem !important;
    padding: 0.5rem !important;
    border: solid 1px #d2d2d2 !important;
  }
}

.qv-form-input {
  @extend .qv-form-base;
  width: 100%;

  &[type="checkbox"] {
    @extend .qv-form-base;
    height: $checkbox-size;
    width: $checkbox-size;
    margin: $checkbox-margin;
  }

  &[readonly] {
    @extend .qv-form-base;
    background-color: $input-background-readonly;
    border: $input-border-readonly;
    pointer-events: none;
    opacity: 1;
  }
}

.qv-form-typeahead {
  margin-top: 2px;
  z-index: 99999;

  &-icon {
    position: absolute;
    left: auto;
    right: 10px;
    top: 30px;
  }

  > li {
    @extend .qv-form-base;
    border: $input-border;
    border-radius: 0;
    border-top: 0;
    border-bottom: 0;
    padding-top: 10px;

    &:focus {
      outline: none;
    }

    &:hover {
      background-color: $input-background-readonly;
    }

    &[selected] {
      background-color: $input-background-readonly;
    }
  }

  > li:last-child {
    border-bottom: $input-border;
    border-bottom-right-radius: $input-border-radius;
    border-bottom-left-radius: $input-border-radius;
  }

  > li:first-child {
    border-top: $input-border;
    border-top-left-radius: $input-border-radius;
    border-top-right-radius: $input-border-radius;
  }
}

.qv-form-input-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.qv-form-input-spinner {
  margin-top: 7px;
  position: absolute;
  right: 7px;
  pointer-events: none;
}

.qv-form-input-error {
  border: var(--qv-color-error) solid 1px !important;
}

.qv-form-input-error-message {
  color: var(--qv-color-error) !important;
  font-size: 1rem;
}

.qv-form-input-warning {
  background-color: var(--color-orange-100) !important;
}

.qv-multiselect-error {
  border: var(--qv-color-error) 1px solid !important;
  border-radius: 6px;
}

.qv-multiselect {
  max-height: 40px;

  span.multiselect__single {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .multiselect__tags {
    max-height: var(--qv-input-height);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &:focus-within {
      border: 2px solid $input-primary-color;
    }

    input.multiselect__input {
      padding: 0;
      margin: 0;
    }
  }
}

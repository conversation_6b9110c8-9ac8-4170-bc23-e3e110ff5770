import 'jquery';
import 'bootstrap';
import 'bootstrap-multiselect';
import 'babel-polyfill';
import './bootstrap-ify.scss';

import Vue from 'vue';
import Vuex from 'vuex';
import VueRouter from 'vue-router';
import VueCurrencyFilter from 'vue-currency-filter';
import FileSelector from 'vue-file-selector';

import { store } from './DataStore';
import routes from './routes';
import App from './App.vue';
import './doneTyping.jquery';
import './plugins/filters/filters';
import { AutoGrowDirective } from '@/plugins/AutoGrowDirective';

Vue.use(Vuex);
Vue.use(VueRouter);
Vue.use(VueCurrencyFilter, {
    symbol: '$',
    thousandsSeparator: ',',
    fractionCount: 0,
    fractionSeparator: '.',
    symbolPosition: 'front',
    symbolSpacing: false,
});
Vue.use(FileSelector);
Vue.directive('auto-grow', AutoGrowDirective);

const router = new VueRouter({
    mode: 'history',
    routes,
    scrollBehavior(to, from, savedPosition) {
        return new Promise((resolve) => {
            let position = { x: 0, y: 0 };
            if (savedPosition) {
                position = savedPosition;
            }

            setTimeout(() => {
                resolve(position);
            }, 10);
        });
    },
});

// Do a little bit of app history tracking
router.beforeEach((to, from, next) => {
    router.previousRoute = {...from};

    try {
        if (to.meta.requiredRole) {
            if (store.getters['userData/userHasMonarchRole'](to.meta.requiredRole)) {
                return next();
            }
            return next({ name: 'home' });
        }

        return next();
    } catch (error) {
        console.error(error)
        return next({ name: 'home' });
    }
})

router.afterEach((to, from) => {
  router.previousRoute = {...from};
});

router.backOrClose = () => {
    if(router.previousRoute && router.previousRoute.name) {
        router.go(-1);
    }
    else
        window.close();
};

const app = new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App),
});

export default app;

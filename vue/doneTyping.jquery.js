if (typeof jQuery !== 'undefined') {
    (function ($) {
        $.fn.donetyping = function (callback, delay) {
            delay || (delay = 1000);
            var timeoutReference;
            var doneTyping = function (elt) {
                if (!timeoutReference) return;
                timeoutReference = null;
                callback(elt);
            };
            this.each(function () {
                var self = $(this);
                self.on('keyup', function () {
                    if (timeoutReference) clearTimeout(timeoutReference);
                    timeoutReference = setTimeout(function () {
                        doneTyping(self);
                    }, delay);
                }).on('blur', function () {
                    doneTyping(self);
                }).on('oninput', function () {
                    doneTyping(self);
                });
            });
            return this;
        };
    })(jQuery);
}
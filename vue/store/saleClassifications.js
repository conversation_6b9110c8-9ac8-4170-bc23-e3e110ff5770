import axios from "../utils/AxiosHeaders";

const saleClassifications = {
    namespaced: true,
    state: {
        classifications: null,
        taLandUseZone: null,
        loading: null,
        exception: null
    },
    mutations: {
        setClassifications(state, values) {
            state.classifications = values;
        },
        setTALandUseZone(state, value) {
            state.taLandUseZone = value;
        },
    },
    actions: {
        async fetchClassifications({ commit }) {
            try {
                const response = await fetch(jsRoutes.controllers.SalesProcessingController.getSaleClassification().url);
                const classifications = await response.json();
                commit("setClassifications", classifications);
            } catch (error) {
                console.error("[Monarch error]: FetchClassifications failed", error);
            }
        },
        async fetchTALandUseZone({ commit }, qpid) {
            try{
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.SalesProcessingController
                        .getTALandUseZone(qpid).url,
                });

                const taLandUseZone = response ? response.data : null;

                if(!taLandUseZone)
                    throw Error('No Land Use Zone found or you do not have permission to see it.');

                commit("setTALandUseZone", taLandUseZone);
            }
            catch (error) {
                console.error("[Monarch error]: FetchTALandUseZone failed", error);
            }
        }
    },
    getters: {
        getClassifications: state => category => {
            if (!state.classifications) {
                return [];
            }

            return state.classifications[category];
        },
        classificationsLoaded: state => {
            return !!state.classifications;
        }
    }
};

export default saleClassifications;
import axios from '../utils/AxiosHeaders';

const valuersList = {
    namespaced: true,

    state: {
        valuers: null,
        loading: false,
        saving: false,
        exception: null,
        errors: null,
    },

    mutations: {
        setValuers(state, value) {
            state.valuers = value;
        },
        setLoading(state, value) {
            state.loading = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setErrors(state, value) {
            console.error(value);
            state.errors = value;
        },
        setException(state, value) {
            console.error(value);
            state.exception = value;
        },

    },

    actions: {
        /* NOTE: This is a list of rating valuers which is distinct from home valuation valuers elsewhere in Monarch. */
        async getValuers({commit}) {
            let response;

            try {
                commit('setLoading', true);

                response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.ReferenceData.displayRatingValuers().url,
                });

                const valuers = response.data;
                commit('setValuers', valuers);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting list of valuers.');

                commit('setValuers', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },

        async getMonarchHomeValuers({commit}) {
            let response;

            try {
                commit('setLoading', true);

                response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.ReferenceData.displayValuers().url,
                });

                const valuers = response.data;
                commit('setValuers', valuers);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting list of valuers.');

                commit('setValuers', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        }
    }
};

export default valuersList;

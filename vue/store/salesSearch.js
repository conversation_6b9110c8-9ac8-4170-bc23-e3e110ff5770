import axios from '../utils/AxiosHeaders';

const salesSearch = {
    namespaced: true,

    state: {
        error: null,
        searchCriteria: {
            fetchRecordCount: 0,
            searchCriteria: '',
            taCode: [],
            fromSaleDate: null,
            toSaleDate: null,
            fromSaleInputDate: null,
            toSaleInputDate: null,
            categories: null,
            saleType: [],
            saleTenure: [],
            priceValueRelationship: [],
            saleProcessingStatusId: [],
            saleStatusId: [],
            saleSourceId: [],
            excludeFromHpiRtv: false,
            fromGSP: null,
            toGSP: null,
        },
        queryParams: {
            offset: 0,
            limit: 100,
            sortBy: 'SALE_DATE',
            sortDesc: true,
        },
        salesSearchLoading: false,
        salesLastResults: {
            searchCriteria: {},
            results: null,
        },
    },

    getters: {
        totalPageCount: (state) => {            
            const { limit } = state.queryParams.limit;
            const { totalResultCount } = state.salesLastResults.results.total;
            return (!state.salesLastResults || !state.salesLastResults.results.total) ? 0 : Math.ceil(totalResultCount / limit);
        },
        currentPage: state => (state.queryParams.offset / state.queryParams.limit) + 1,
    },

    mutations: {
        setOffset(state, value) {
            state.queryParams.offset = value;
        },
        setLimit(state, value) {
            state.queryParams.limit = value;
        },
        setLastResults(state, value) {
            state.salesLastResults = value;
        },
        setLoading(state, value) {
            state.salesSearchLoading = value;
        },
        setFetchRecordCount(state,value) {
            state.salesLastResults.results.total = value;
        },
        setError(state, value) {
            state.error = value;
            state.salesSearchLoading = false;
        },
        setsearchCriteriaItem(state, { id, value }) {
            const searchCriteria = { ...state.searchCriteria };
            searchCriteria[id] = value;
            state.searchCriteria = searchCriteria;
        },
        setQueryParamItem(state, { id, value }) {
            const queryParams = { ...state.queryParams };
            queryParams[id] = value;
            state.queryParams = queryParams;
        }
    },
    actions: {
        async searchResult({ commit, state }) {
            try {
                commit('setError', null);
                const searchCriteria = Object.assign({}, state.searchCriteria);
                const queryParams = Object.assign({}, state.queryParams);
                commit('setLoading', true);

                const salesSearchController = jsRoutes.controllers.SalesSearchController;
                let requestUrl = salesSearchController.getSearchSale().url;

                await axios({
                    method: 'post',
                    url:requestUrl,
                    data: { searchCriteria,queryParams },
                }).then((response) => {
                    const { data } = response;
                    if(!state.searchCriteria.fetchRecordCount && state.salesLastResults.results !== null) {
                        data.total = state.salesLastResults.results.totalResultCount;
                    }
                    commit('setLastResults', {
                        searchCriteria,
                        results: data,
                    });
                }).catch((error) => {
                    let errorMessage = 'An unexpected error has occurred, please try again.';
                    if(error.response && (error.response.status === 504 || error.response.status === '504')) {
                        errorMessage = 'Your request has timed out, please provide additional search criteria to refine your search.';
                    }
                    commit('setError', errorMessage);
                    commit('setLastResults', {
                        searchCriteria,
                        results: null,
                    });
                }).finally(() => {
                    commit('setLoading', false);
                });
            } catch(error) {
                commit('setError', error);
            }
        },
        changePage({ dispatch, commit, state }, page) {
            commit('setOffset', (page - 1) * state.queryParams.limit);
            commit('setFetchRecordCount', 0);
            dispatch('searchResult');
        },
        clearLastResults({ commit }) {
            commit('setLastResults', {
                searchCriteria: {},
                results: null,
            });
        }

    },
};

export default salesSearch;

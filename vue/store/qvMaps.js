import axios from '../utils/AxiosHeaders';
import { pluralizeNoun } from '../utils/FormatUtils';

export default {
    namespaced: true,
    state: {
        searchedProperties: null,
        selectedProperties: null,
        layerDataForHover: null,
        twoLetterZones: null,
        twoLetterCategories: null,
        saleClassifications: null,
        propertiesSaleInfo: null,
        taList: null,
        primaryZoneCategories: null,
        rollsList: null,
        alertMessage: null,
        exception: null,
    },
    mutations: {
        setSearchedProperties(state, value) {
            state.searchedProperties = value;
        },
        setSelectedProperties(state, value) {
            state.selectedProperties = value;
        },
        setLayerDataForHover(state, value) {
            state.layerDataForHover = value;
        },
        setTwoLetterZones(state, value) {
            state.twoLetterZones = value;
        },
        setTwoLetterCategories(state, value) {
            state.twoLetterCategories = value;
        },
        setSaleClassifications(state, value) {
            state.saleClassifications = value;
        },
        setPropertiesSaleInfo(state, value) {
            state.propertiesSaleInfo = value;
        },
        setAlertMessage(state, value) {
            state.alertMessage = value;
        },
        setException(state, value) {
            state.exception = value;
        },
        setTAList(state, value) {
            state.taList = value;
        },
        setPrimaryZoneCategories(state, value) {
            state.primaryZoneCategories = value;
        },
        setRollsList(state, value) {
            state.rollsList = value;
        },
    },
    actions: {
        async getPropertyDetailsForQpids({ commit }, { qpids, isParcelIds }) {
            try {
                const response = await axios.get(jsRoutes.controllers.QVMapsController.getPropertyDetailsForQpids(qpids, isParcelIds).url);

                if (!response || (response.data && response.data.message)) {
                    commit('setAlertMessage', {
                        heading: 'Unable to search/show property on map',
                        message: response.data.message || 'Unknown error',
                    });
                    return;
                }

                const invalidGeometryQpids = new Set();

                for (const result of response.data) {
                    if (!result.geometryWKT) {
                        invalidGeometryQpids.add(result.QPID);
                    }
                }

                if (invalidGeometryQpids.size > 0) {
                    const invalidGeometryQpidsArray = Array.from(invalidGeometryQpids);
                    commit('setAlertMessage', {
                        heading: `Unable to search/show ${pluralizeNoun('property', invalidGeometryQpidsArray)} on map`,
                        message: `Geometry data is not available on the ${pluralizeNoun('Qpid', invalidGeometryQpidsArray)}: ${invalidGeometryQpidsArray.join(', ')}`                    });
                }
                else {
                    commit('setSearchedProperties', response.data);
                }
            }
            catch (exception) {
                commit('setAlertMessage', {
                    heading: 'Unable to search/show property on map',
                    message: exception.response?.data?.message || exception.message || 'Unknown error',
                });
                throw exception;
            }
        },
        async getPropertyDetailsForLatLng({ commit }, coords) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.QVMapsController.getPropertyDetailsForLatLng(coords[1], coords[0]).url,
                });
                commit('setSelectedProperties', response ? response.data : null);
                if (response.data && response.data.message) {
                    commit('setAlertMessage', {
                        heading: 'Unable to display Property Sale Information',
                        message: response.data.message,
                    });
                }
            } catch (exception) {
                commit('setAlertMessage', {
                    heading: 'Unable to select/highlight property(s) on map',
                    message: exception.response && exception.response.data && exception.response.data.message
                        ? exception.response.data.message
                        : exception,
                });
                throw exception;
            }
        },
        async getLayerDataForLatLng({ commit }, data) {
            const { coords, column } = data;
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.QVMapsController.getLayerDataForLatLng(coords[1], coords[0], column).url,
                });
                commit('setLayerDataForHover', response && Array.isArray(response.data) ? response.data : null);
            } catch (exception) {
                //  dont show error message or throw excpeption if qpid(s) not found
                if (exception.response.status !== 400) {
                    commit('setAlertMessage', {
                        heading: 'Unable to get layer data for hover',
                        message: exception.response && exception.response.data && exception.response.data.message
                            ? exception.response.data.message
                            : exception,
                    });
                    throw exception;
                }
            }
        },
        async getPropertyDetailsForBndryPoints({ commit }, geomWkt) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.QVMapsController.getPropertyDetailsForBndryPoints(geomWkt)
                        .url,
                });
                commit('setSelectedProperties', response ? response.data : null);
            } catch (exception) {
                commit('setAlertMessage', {
                    heading: 'Unable to select/highlight property(s) on map',
                    message: exception.response && exception.response.data && exception.response.data.message
                        ? exception.response.data.message
                        : exception,
                });
                throw exception;
            }
        },
        async getPropertySaleInfoFromLatLng({ commit }, coords) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.QVMapsController.getPropertySaleInfoFromLatLng(coords[1], coords[0])
                        .url,
                });
                commit('setPropertiesSaleInfo', response && Array.isArray(response.data) ? response.data : []);
                if (response.data && response.data.message) {
                    commit('setAlertMessage', {
                        heading: 'Unable to display Property Sale Information',
                        message: response.data.message,
                    });
                }
            } catch (exception) {
                commit('setAlertMessage', {
                    heading: 'Unable to display Property Sale Information',
                    message: exception.response && exception.response.data && exception.response.data.message
                        ? exception.response.data.message
                        : exception,
                });
                throw exception;
            }
        },
        async uploadSiteplanImage({ commit }, imageResource) {
            try {
                const response = await axios({
                    method: 'post',
                    url: jsRoutes.controllers.QVMapsController.uploadSiteplanImage().url,
                    data: imageResource,
                    maxContentLength: 10000000,
                    maxBodyLength: 100000000
                });

                const result = response ? response.data : null;
                commit('setAlertMessage', {
                    heading: result.status === 'success'
                        ? 'Site Plan image uploaded completed!'
                        : 'Could not upload Site Plan image',
                    message: result.message,
                });
            } catch (exception) {
                commit('setAlertMessage', {
                    heading: 'Site Plan image upload failed!',
                    message: exception.response && exception.response.data && exception.response.data.message
                        ? exception.response.data.message
                        : exception,
                });
                throw exception;
            }
        },
        async getPickListValues({ commit }) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.QVMapsController.getPickListValues().url,
                });
                const picklistValues = response ? response.data : null;
                if (picklistValues) {
                    commit('setSaleClassifications', picklistValues.salesClassification);
                    commit('setTwoLetterCategories', picklistValues.twoLetterCategories);
                    commit('setTwoLetterZones', picklistValues.twoLetterZoneCodes);
                    commit('setTAList', picklistValues.taList);
                    commit('setPrimaryZoneCategories', picklistValues.primaryZoneCategories);
                }
            } catch (exception) {
                commit('setAlertMessage', {
                    heading: 'Unable to retrieve picklist dropdown list',
                    message: exception.response && exception.response.data && exception.response.data.message
                        ? exception.response.data.message
                        : exception,
                });
                throw exception;
            }
        },
        async getRollsListForTA({ commit }, ratingAuthorityId) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.QVMapsController.getRollsListForTA(ratingAuthorityId).url,
                });
                const rolls = response ? response.data : null;
                commit('setRollsList', rolls);
            } catch (exception) {
                commit('setAlertMessage', {
                    heading: 'Unable to retrieve the Rolls List for a TA',
                    message: exception.response && exception.response.data && exception.response.data.message
                        ? exception.response.data.message
                        : exception,
                });
                throw exception;
            }
        },
    },
};



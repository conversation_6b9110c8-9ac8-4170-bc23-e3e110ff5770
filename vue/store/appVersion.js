import axios from '../utils/AxiosHeaders';

const appVersion = {
    namespaced: true,

    state: {
        version: '',
    },

    mutations: {
        updateVersion(state, value) {
            state.version = value;
        },
    },
    actions: {
        async getMonarchWebServerVersion({ commit }) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.Application.version().url
                });
                commit('updateVersion', response.data);
            } catch (error) {
                console.error(error);
            }
        },
    },
};

export default appVersion;

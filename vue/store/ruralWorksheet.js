import axios from '../utils/AxiosHeaders';

const worksheet = {
    namespaced: true,

    state: {
        worksheet: null,
        roundingTables: null,
        lists: null,
        loading: false,
        saving: false,
        exception: null,
        errors: null,
        warnings: null,
        pdfFileStream: {}
    },
    mutations: {
        setWorksheet(state, value) {
            state.worksheet = value;
        },
        setLandMatrix(state, value) {
            state.worksheet.landMatrix = value;
        },
        setRoundingTables(state, value) {
            state.roundingTables = value;
        },
        setLists(state, value) {
            state.lists = value;
        },
        setLoading(state, value) {
            state.loading = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setErrors(state, value) {
            state.errors = value;
        },
        setWarnings(state, value) {
            state.warnings = value;
        },
        setException(state, value) {
            if (value !== null)
                console.error(value);
            state.exception = value;
        },
        setPdfFileStream(state, value) {
            state.pdfFileStream = value;
        }
    },
    actions: {
        async getCurrentWorksheet({commit}, qpid) {
            try {
                commit('setLoading', true);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralWorksheetController.getCurrentWorksheet(qpid).url,
                });

                const result = response.data;

                commit('setWorksheet', result.worksheet);
                commit('setRoundingTables', result.roundingTables);
                commit('setLists', result.lists);
                commit('setWarnings', result.warnings);
                commit('setErrors', result.errors);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting worksheet.');

                commit('setWorksheet', null);
                commit('setPreviousWorksheetId', null);
                commit('setNextWorksheetId', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },
        async getRevisionWorksheet({commit}, qpid) {
            try {
                commit('setLoading', true);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralWorksheetController.getRevisionWorksheet(qpid).url,
                });

                const result = response.data;

                commit('setWorksheet', result.worksheet);
                commit('setRoundingTables', result.roundingTables);
                commit('setLists', result.lists);
                commit('setWarnings', result.warnings);
                commit('setErrors', result.errors);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting worksheet.');

                commit('setWorksheet', null);
                commit('setPreviousWorksheetId', null);
                commit('setNextWorksheetId', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },
        async getRtvWorksheet({commit}, qpid) {
            try {
                commit('setLoading', true);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralWorksheetController.getRtvWorksheet(qpid).url,
                });

                const result = response.data;

                commit('setWorksheet', result.worksheet);
                commit('setRoundingTables', result.roundingTables);
                commit('setLists', result.lists);
                commit('setWarnings', result.warnings);
                commit('setErrors', result.errors);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting RTV worksheet.');

                commit('setWorksheet', null);
                commit('setPreviousWorksheetId', null);
                commit('setNextWorksheetId', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },
        async createRevisionWorksheet({commit}, qpid) {
            try {
                commit('setLoading', true);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralWorksheetController.createRevisionWorksheet(qpid).url,
                });

                const result = response.data;

                commit('setWorksheet', result.worksheet);
                commit('setRoundingTables', result.roundingTables);
                commit('setLists', result.lists);
                commit('setWarnings', result.warnings);
                commit('setErrors', result.errors);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting worksheet.');

                commit('setWorksheet', null);
                commit('setPreviousWorksheetId', null);
                commit('setNextWorksheetId', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },
        async updateWorksheet({ commit }, { worksheet, userName, applyWorksheet } ) {
            try {
                commit('setSaving', true);

                const response = await axios({
                    method: 'post',
                    data: worksheet,
                    url: jsRoutes.controllers.RuralWorksheetController
                        .updateWorksheet(userName).url,
                });

                const hasWarningsOrErrors = (response.data.warnings && response.data.warnings.length)
                    || (response.data.errors && response.data.errors.length);

                if (applyWorksheet || hasWarningsOrErrors) {
                    commit('setWorksheet', response.data.worksheet);
                }
                commit('setWarnings', response.data.warnings);
                commit('setErrors', response.data.errors);

                commit('setSaving', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        },
        async updateAssessment({ commit }, { worksheet, userName } ) {
            try {
                commit('setSaving', true);

                const response = await axios({
                    method: 'post',
                    data: worksheet,
                    url: jsRoutes.controllers.RuralWorksheetController
                        .updateAssessment(userName).url,
                });

                commit('setWorksheet', response.data.worksheet);
                commit('setWarnings', response.data.warnings);
                commit('setErrors', response.data.errors);

                commit('setSaving', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        },
        async deleteWorksheet({ commit }, { qpid } ) {
            try {
                commit('setSaving', true);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralWorksheetController
                        .deleteWorksheet(qpid).url,
                });

                commit('setWorksheet', null);
                commit('setPreviousWorksheetId', null);
                commit('setNextWorksheetId', null);
                commit('setLists', null);
                commit('setWarnings', null);
                commit('setErrors', null);

                commit('setSaving', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        },
        async recalculateLandMatrix({ commit }, { qpid, worksheetLandValue, apportionmentCode } ) {
            try {
                commit('setSaving', true);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralWorksheetController
                        .recalculateLandMatrix(qpid, worksheetLandValue, apportionmentCode).url,
                });

                commit('setLandMatrix', response.data);

                commit('setSaving', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        },
        async generateRuralWorksheetPdfReport({commit}, {qpid, type}) {
            try {
                commit('setLoading', true);
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralWorksheetController.generateRuralWorksheetPdf(
                        qpid, type).url,
                });
                commit('setLoading', false);
                commit('setPdfFileStream', response.data);
            } catch (error) {
                commit('setLoading', false);
                commit('setError', error);
                throw error;
            }
        }
    },
};

export default worksheet;

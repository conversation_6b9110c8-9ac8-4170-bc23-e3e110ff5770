import axios from '../utils/AxiosHeaders';
import { RatingValuation, createValidationSet } from '@quotable-value/validation';
import * as RatingValuationController from '@/services/RatingValuationController';
import { debounce } from 'lodash';

const {
    validateRatingValuationJobClient,
} = RatingValuation

// TODO - This store is a mashup implementation of two independently
//        developed stores for the Worksheet and Comparable Properties pages
// TODO - It will need to be revisited when we implement a coherent component
//        hierarchy around Rating Valuations.

const ratingValuation = {
    namespaced: true,

    state: {
        loading: true,
        loadingComparables: false,
        saving: false,
        ratingValuation: null,
        comparableSearchCriteria: {
            offset: 0,
            max: 25,
            sort: [],
        },
        selectedComparables: [],
        potentialComparables: [],
        currentActivitiesSearchCriteria: {
            activityType: 'BC',
            sortField: 'INITIATED_DATE',
            direction: 'ASC',
        },
        propertyActivities: [],
        propertyActivitiesQPID: null,
        valuationActivities: [],
        exception: null,
        errors: null,
        validationSet: createValidationSet(),
        clientValidationSet: null,
        backendValidationSet: null,
    },
    getters: {
        totalPageCount: (state) => {
            if (!state.potentialComparables) {
                return 0;
            }
            const { max } = state.comparableSearchCriteria;
            const { totalResults } = state.potentialComparables;

            return Math.ceil(totalResults / max);
        },
        currentPage: (state) => {
            if (!state.comparableSearchCriteria) {
                return 1;
            }
            const { offset, max } = state.comparableSearchCriteria;
            return (offset / max) + 1;
        },
    },
    mutations: {
        setLoading(state, value) {
            state.loading = value;
            if (value) {
                state.exception = null;
                state.errors = null;
            }
        },
        setLoadingComparables(state, value) {
            state.loadingComparables = value;
            if (value) {
                state.exception = null;
                state.errors = null;
            }
        },
        setSaving(state, value) {
            state.saving = value;
            if (value) {
                state.exception = null;
                state.errors = null;
            }
        },
        setRatingValuation(state, value) {
            if (!value) {
                state.ratingValuation = null;
                return;
            }

            if (value.ratingValuationComponents == null) {
                value.ratingValuationComponents = [];
            }

            value.ratingValuationComponents = value.ratingValuationComponents.map((component, index) => {
                return {
                    ...component,
                    index,
                };
            });

            if (state.ratingValuation?.id !== value.id) {
                state.ratingValuation = value;
                return;
            }

            Object.assign(state.ratingValuation, value);
        },
        setConsentJobUpdateInformation(state, value) {
            state.consentJobUpdateInformation = value;
        },
        setComparableSearchCriteria(state, value) {
            state.comparableSearchCriteria = value;
        },
        setSelectedComparables(state, value) {
            state.selectedComparables = value;
        },
        setPotentialComparables(state, value) {
            state.potentialComparables = value;
        },
        setSortField(state, value) {
            if (value.columnName === 'ADDRESS') {
                state.comparableSearchCriteria.sort = ['TA_CODE', 'STREET_NAME', 'STREET_TYPE', 'STREET_NUMBER', 'STREET_NUMBER_SUFFIX', 'VALUATION_REFERENCE'];
            }
            else {
                state.comparableSearchCriteria.sort = [value.columnName];
            }
            state.comparableSearchCriteria.order = value.direction;
        },
        setPropertyActivities(state, value) {
            state.propertyActivities = value;
        },
        setPropertyActivitiesQPID(state, value) {
            state.propertyActivitiesQPID = value;
        },
        setValuationActivities(state, value) {
            state.valuationActivities = value;
        },
        setOffset(state, value) {
            state.comparableSearchCriteria.offset = value;
        },
        setPageSize(state, value) {
            state.comparableSearchCriteria.max = value;
        },

        setException(state, value) {
            state.exception = value;
        },
        setErrors(state, value) {
            state.errors = value;
        },
        setValidationSet(state, value) {
            state.validationSet = value;
        }
    },
    actions: {
        async getCompleteValuationForActivity({ commit }, rollMaintenanceActivityId) {
            commit('setRatingValuation', null);
            commit('setException', null);
            commit('setLoading', true);
            try {
                const urlGenerator = jsRoutes.controllers.RatingValuationController
                    .getCompleteValuationForActivity;

                // Retrieve/Generate the Rating Valuation
                const response = await axios({
                    method: 'get',
                    url: urlGenerator(rollMaintenanceActivityId, true).url,
                });
                // Commit to the Store
                const valuation = response && response.data;
                if (!valuation) {
                    throw Error('No valuation found or you do not have permission to see it.');
                }

                commit('setRatingValuation', valuation);
            }
            catch (exception) {
                commit('setException', exception);
            }
            finally {
                commit('setLoading', false);
            }
        },
        async deleteRatingValuation({ commit, state }, { rollMaintenanceActivityId, objectionId }) {
            commit('setException', null);
            const { url: updateObjectionUrl } = jsRoutes.controllers.ObjectionController.updateObjectionJobStatus();
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RatingValuationController.deleteRatingValuation(rollMaintenanceActivityId).url,
                });
                if (response?.status != 200) {
                    alert(`Deleting Related Activity Failed: ${rollMaintenanceActivityId}`);
                }
                if (objectionId) {
                    const updateObjectionsRes = await fetch(updateObjectionUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                        },
                        body: JSON.stringify({ objectionIds: Array.isArray(objectionId) ? objectionId : [objectionId], deleteJob: true }),
                    });
                    if (updateObjectionsRes.status != 200) {
                        alert(`Resetting job status for objection failed, id(s): ${objectionId}`);
                    }
                }
                return response;
            }
            catch (error) {
                console.error(error);
                commit('setException', error);
            }
        },
        async deleteInactiveRatingValuation({ commit, state }, deleteObject) {
            commit('setException', null);
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RatingValuationController.deleteInactiveRatingValuation(deleteObject.inactiveValuation, deleteObject.rollMaintenanceActivityId).url,
                });
                commit('setRatingValuation', response.data);
                if (response.data && response.status !== 200) {
                    throw new Error('Deleting inactive Related Consent Job Failed!');
                }
            }
            catch (exception) {
                commit('setException', exception);
            }
        },
        async getInProgressValuationForActivity({ commit, dispatch }, { rollMaintenanceActivityId, shouldGenerate }) {
            const generate = shouldGenerate !== false;
            commit('setRatingValuation', null);
            commit('setException', null);
            commit('setLoading', true);
            // Retrieve/Generate the Rating Valuation
            try {
                const { url } = jsRoutes.controllers.RatingValuationController.getInProgressValuationForActivity(rollMaintenanceActivityId, generate);
                const res = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                });
                // TECH DEBT: error messaging doesnt bubble back from services to monarch-web when deployed
                if (!res.ok) {
                    alert('Oops, something went wrong! Please ensure the property\'s land zone code appears in monarch classifications.');
                }
                else {
                    try {
                        const data = await res.json();
                        if (data) {
                            commit('setRatingValuation', data);
                        }
                        else {
                            throw new Error('No valuation found or you do not have permission to see it.');
                        }
                        // Determine the Comparable Search Criteria
                        await dispatch('getDefaultComparableSearchCriteria');
                        // Retrieve Comparables (why here?)
                        await dispatch('loadComparables');
                    }
                    catch (error) {
                        commit('setException', error);
                    }
                }
            }
            catch (error) {
                commit('setException', error);
            }
            finally {
                commit('setLoading', false);
            }
        },
        async linkRollMaintenanceActivity({ commit, state }, rollMaintenanceActivityId) {
            try {
                // console.log(`linkRollMaintenanceActivity ${rollMaintenanceActivityId}`);
                commit('setSaving', true);

                const ratingValuationId = state.ratingValuation.id;
                const controller = jsRoutes.controllers.RatingValuationController;
                const urlGenerator = controller.linkRollMaintenanceActivity;
                const response = await axios({
                    method: 'get',
                    url: urlGenerator(ratingValuationId, rollMaintenanceActivityId).url,
                });
                commit('setValidationSet', createValidationSet(response.data.validations));
                commit('setErrors', response.data.errors);

                if (response.data.success) {
                    commit('setRatingValuation', response.data.value);
                }
            }
            catch (exception) {
                commit('setException', exception);
            }
            finally {
                commit('setSaving', false);
            }
        },
        async unlinkRollMaintenanceActivity({ commit, state }, rollMaintenanceActivityId) {
            try {
                // console.log(`unlinkRollMaintenanceActivity ${  rollMaintenanceActivityId}`);

                commit('setSaving', true);

                const ratingValuationId = state.ratingValuation.id;
                const controller = jsRoutes.controllers.RatingValuationController;
                const urlGenerator = controller.unlinkRollMaintenanceActivity;

                const response = await axios({
                    method: 'get',
                    url: urlGenerator(ratingValuationId, rollMaintenanceActivityId).url,
                });
                commit('setValidationSet', createValidationSet(response.data.validations));
                commit('setErrors', response.data.errors);

                if (response.data.success) {
                    commit('setRatingValuation', response.data.value);
                }
            }
            catch (exception) {
                commit('setException', exception);
            }
            finally {
                commit('setSaving', false);
            }
        },

        // TODO: DEV-6358 @braden fix, you can only have one argument after { commit, state }
        async loadRelatedRollMaintenanceActivities({ commit, state }, activeObjection = true, force = false) {
            commit('setException', null);
            const { qpid } = state.ratingValuation.ratingUnit;
            if (!qpid) {
                commit('setPropertyActivities', []);
                // TODO Why would this happen?
                // console.log('No QPID');
                return;
            }

            // TODO: DEV-6358 @braden fix this
            if (false && !force && qpid === state.propertyActivitiesQPID && (state.propertyActivities || state.propertyActivities.length > 0)) {
                return;
            }

            /* Because objections not included in searchRollMaintenanceActivities (uses lambda search endpoint instead)
               need to find objection ids to pass to getRollMaintenanceActivities */
            const queryParameters = 'total=false';
            const { url: searchObjectionUrl } = jsRoutes.controllers.ApiPicklistController.searchObjection(queryParameters);
            const searchObjectionCriteria = { qpid, activeObjection };
            const { url: getActivitiesUrl } = jsRoutes.controllers.RollMaintenanceController.getRollMaintenanceActivitiesByIds();
            const { url: updateObjectionUrl } = jsRoutes.controllers.ObjectionController.updateObjectionJobStatus();

            const searchCriteria = { ...state.currentActivitiesSearchCriteria, qpid };
            const controller = jsRoutes.controllers.RollMaintenanceController;
            const { url } = controller.searchRollMaintenanceActivities();

            function cleanStr(str) {
                if (str) {
                    return str.trim().toLowerCase();
                }
                return str;
            }

            try {
                const searchObjectionRes = await fetch(searchObjectionUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: JSON.stringify(searchObjectionCriteria),
                });
                const { status, message, objections } = await searchObjectionRes.json();
                if (status !== 'SUCCESS') {
                    console.error(`status: ${status} ${message}`);
                }

                const getActivitiesRes = await fetch(getActivitiesUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: JSON.stringify(objections ? objections.map(o => o.activityId) : []),
                });
                const objectionActivities = await getActivitiesRes.json();
                const mergedObjections = objectionActivities.map((activity) => {
                    const objectionMatch = objections.find(o => o.activityId == activity.id) || {};
                    return { ...activity, ...objectionMatch };
                });

                let response = await axios({
                    method: 'post',
                    url,
                    data: searchCriteria,
                });

                if (response.data && response.data.totalResultCount > response.data.rollMaintenanceActivities.length) {
                    // TODO This is a quick fix because currently if page size is not provided, then the search defaults to 25 page size.
                    // Therefore, the search will return maximum 25 results unless page size is provided.
                    searchCriteria.pageSize = response.data.totalResultCount;
                    response = await axios({
                        method: 'post',
                        url,
                        data: searchCriteria,
                    });
                }

                if ((response.data && response.data.rollMaintenanceActivities) || mergedObjections.length) {
                    let { rollMaintenanceActivities = [] } = response.data;
                    rollMaintenanceActivities = rollMaintenanceActivities.concat(mergedObjections);
                    commit('setPropertyActivities', rollMaintenanceActivities);
                    commit('setPropertyActivitiesQPID', qpid)
                }
                else {
                    commit('setPropertyActivities', []);
                    commit('setPropertyActivitiesQPID', qpid)
                }
            }
            catch (exception) {
                commit('setException', exception);
            }
        },
        async loadRollMaintenanceActivities({ commit, state }) {
            commit('setException', null);

            const id = state.ratingValuation.id;

            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RatingValuationController.getRollMaintenanceActivities(id).url,
                });
                if (response.data) {
                    const rollMaintenanceActivities = response.data;
                    commit('setValuationActivities', rollMaintenanceActivities);
                }
                else {
                    commit('setValuationActivities', []);
                }
            }
            catch (exception) {
                commit('setException', exception);
            }
        },
        async requireMoreInformation({ commit, state }, { note, requestPlans }) {
            commit('setException', null);

            const id = state.ratingValuation.id;

            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RatingValuationController.requireMoreInformation(id, note ? note : '', requestPlans).url,
                });

                if (response.data && !response.data.success) {
                    throw new Error('Update of at least one related consent failed.');
                }

            }
            catch (exception) {
                commit('setException', exception);
            }
        },
        async informationProvided({ commit, state }) {
            commit('setException', null);

            const id = state.ratingValuation.id;

            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RatingValuationController.informationProvided(id).url,
                });

                if (response.data && !response.data.success) {
                    throw new Error('Update of at least one related consent failed.');
                }

            }
            catch (exception) {
                commit('setException', exception);
            }
        },
        async requireInspection({ commit, state }, { note }) {
            commit('setException', null);

            const id = state.ratingValuation.id;

            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RatingValuationController.requireInspection(id, note ? note : '').url,
                });

                if (response.data && !response.data.success) {
                    throw new Error('Update of at least one related consent failed.');
                }

            }
            catch (exception) {
                commit('setException', exception);
            }
        },
        async inspected({ commit, state }) {
            commit('setException', null);

            const id = state.ratingValuation.id;

            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RatingValuationController.inspected(id).url,
                });

                if (response.data && !response.data.success) {
                    throw new Error('Update of at least one related consent failed.');
                }

            }
            catch (exception) {
                commit('setException', exception);
            }
        },
        async selectComparableProperty({ commit, state, dispatch }, params) {
            try {
                // console.log('selectComparableProperty');

                commit('setSaving', true);

                if (params.selected) {
                    // Add the Comparable Property
                    const selectedResult = state.potentialComparables.resultList
                        .find(result => result.property.id === params.propertyId);

                    state.ratingValuation.comparableProperties.push({
                        propertyId: selectedResult.property.id,
                        qpid: selectedResult.property.qupid,
                    });

                    // Update the Comparable Search Criteria
                    state.comparableSearchCriteria.excludedQupids
                        .push(selectedResult.property.qupid);
                }
                else {
                    // Remove the Comparable Property
                    const deselectedQpid = state.ratingValuation.comparableProperties
                        .find(ratingUnit => ratingUnit.propertyId === params.propertyId).qpid;

                    state.ratingValuation.comparableProperties = state
                        .ratingValuation.comparableProperties
                        .filter(ratingUnit => ratingUnit.propertyId !== params.propertyId);

                    // Update the Comparable Search Criteria
                    state.comparableSearchCriteria.excludedQupids = state
                        .comparableSearchCriteria.excludedQupids
                        .filter(qpid => qpid !== deselectedQpid);
                }

                // Save the Rating Valuation
                const response = await axios({
                    method: 'post',
                    url: jsRoutes.controllers.RatingValuationController.saveRatingValuation().url,
                    data: state.ratingValuation,
                });

                // TODO Is this correct?
                if (!response.data.success) {
                    throw new Error(`Failed to save rating valuation. ${response.data.errors}`);
                }

                // Commit to the Store
                commit('setRatingValuation', response.data.value);
                await dispatch('loadComparables');
            }
            catch (exception) {
                commit('setException', exception);
            }
            finally {
                commit('setSaving', false);
            }
        },
        async addPropertyByQpid({ commit, state, dispatch }, propertyData) {
            try {
                // Ensure input validation
                if (!propertyData) {
                    throw new Error('Invalid property data provided.');
                }

                // Add the property data to state
                state.ratingValuation.comparableProperties.push({
                    propertyId: propertyData.property.id,
                    qpid: propertyData.property.qupid,
                });

                // Update the Comparable Search Criteria
                state.comparableSearchCriteria.excludedQupids.push(propertyData.property.qupid);

                // Prepare the data for the request
                const requestData = JSON.stringify(state.ratingValuation);

                // Save the Rating Valuation using fetch
                const response = await fetch(jsRoutes.controllers.RatingValuationController.saveRatingValuation().url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: requestData,
                });

                // Check if the response is successful (status code 200-299)
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Failed to save rating valuation: ${response.statusText} - ${errorText}`);
                }

                const responseData = await response.json();

                if (!responseData.success) {
                    throw new Error(`Failed to save rating valuation. ${responseData.errors}`);
                }

                // Commit to the Store
                commit('setRatingValuation', responseData.value);
                commit('setComparableSearchCriteria', state.comparableSearchCriteria);

                // Reload
                await dispatch('loadComparables');
            }
            catch (exception) {
                commit('setException', exception);
            }
            finally {
                commit('setSaving', false);
            }
        },

        async getDefaultComparableSearchCriteria({ commit, state }) {
            try {
                // console.log('getDefaultComparableSearchCriteria');

                commit('setException', null);

                const { ratingValuation: rv } = state;
                const controller = jsRoutes.controllers.RatingValuationController;
                const response = await axios({
                    method: 'get',
                    url: controller.getDefaultComparablePropertySearchCriteria(rv.id).url,
                });
                const comparableSearchCriteria = response.data;
                comparableSearchCriteria.max = 25;

                commit('setComparableSearchCriteria', comparableSearchCriteria);
            }
            catch (exception) {
                commit('setException', exception);
            }
        },
        async loadComparables({ commit, state }) {
            try {
                // console.log('loadComparables');
                commit('setLoadingComparables', true);
                commit('setException', null);

                // Retrieve the Selected Comparables
                const selectedQpids = state
                    .ratingValuation.comparableProperties
                    .map(ratingUnit => ratingUnit.qpid);

                // If there are no selected comparable properties then we
                // should not do a search and instead return an empty list.
                let selectedComparables = [];

                if (selectedQpids.length > 0) {
                    // Only add distance criteria if subject property coordinates are available
                    let selectedComparablesCriteria = {
                        qupids: selectedQpids,
                        comparabilityScoreParameters: state.comparableSearchCriteria.comparabilityScoreParameters,
                        sort: state.comparableSearchCriteria.sort,
                        order: state.comparableSearchCriteria.order,
                    };
                    if (state.comparableSearchCriteria.distanceCriteria) {
                        selectedComparablesCriteria.distanceCriteria = {
                            coordinates: state.comparableSearchCriteria.distanceCriteria.coordinates,
                        };
                    }

                    const selectedRequest = axios({
                        method: 'post',
                        url: jsRoutes.controllers.PropertyController.searchProperties().url,
                        data: selectedComparablesCriteria,
                    });
                    const selectedResponse = await selectedRequest;
                    selectedComparables = selectedResponse.data;
                }
                // Retrieve the Potential Comparables
                const searchCriteria = { ...state.comparableSearchCriteria };

                const hasSalesGroups = searchCriteria.saleGroupDetails && searchCriteria.saleGroupDetails.length > 0;
                const hasRollNumbers = searchCriteria.rollNumbers && searchCriteria.rollNumbers.length > 0;
                const hasTaCode = searchCriteria.taCodes && searchCriteria.taCodes.length > 0;

                if (hasSalesGroups) {
                    searchCriteria.salesGroups = searchCriteria.saleGroupDetails.map(saleGroup => {
                        return {
                            salesGroup: saleGroup.taCodeSalesGroupNumber,
                            taCode: saleGroup.taCode,
                        };
                    });
                }

                if (hasRollNumbers && hasTaCode) {
                    const taCode = searchCriteria.taCodes[0];
                    searchCriteria.rolls = searchCriteria.rollNumbers.map(rollNumber => {
                        return {
                            rollNumber: rollNumber,
                            taCode: taCode,
                        };
                    });
                }

                // note: if you mix and match sales group and roll numbers, it can return no results. this changes sales groups into rolls
                if (hasSalesGroups && hasRollNumbers && hasTaCode) {
                    const taCode = searchCriteria.taCodes[0];
                    const saleGroupDetailRolls = searchCriteria.saleGroupDetails.map(saleGroupDetail => {
                        return saleGroupDetail.rollNumbers.map(entry => {
                            return {
                                rollNumber: entry.rollNumber,
                                taCode,
                            };
                        });
                    });
                    const additionalRolls = saleGroupDetailRolls.flat();
                    searchCriteria.rolls = [...searchCriteria.rolls, ...additionalRolls];
                    searchCriteria.salesGroups = null;
                }

                searchCriteria.includedApportionmentCodes = [ 0, 2, 5 ];

                const { rollNumbers, saleGroupDetails, ...comparableSearchCriteria } = searchCriteria;
                const potentialRequest = axios({
                    method: 'post',
                    url: jsRoutes.controllers.PropertyController.searchProperties().url,
                    data: comparableSearchCriteria,
                });

                const potentialResponse = await potentialRequest;

                // Commit to the Store
                commit('setSelectedComparables', selectedComparables);
                commit('setPotentialComparables', potentialResponse.data);
                commit('setLoadingComparables', false);
            }
            catch (exception) {
                commit('setException', exception);
                commit('setLoadingComparables', false);
            }
        },
        async changeSort({ commit, dispatch }, value) {
            commit('setSortField', value);
            await dispatch('loadComparables');
        },
        changePage({ dispatch, commit, state }, page) {
            commit('setOffset', (page - 1) * state.comparableSearchCriteria.max);
            dispatch('loadComparables');
        },
        async getValuation({ commit }, ratingValuationId) {
            try {
                const controller = jsRoutes.controllers.RatingValuationController;
                commit('setLoading', true);
                const response = await axios({
                    method: 'get',
                    url: controller.getRatingValuation(ratingValuationId).url,
                });

                const rv = response.data;
                commit('setRatingValuation', rv);
            }
            catch (exception) {
                commit('setException', exception);
            }
            finally {
                commit('setLoading', false);
            }
        },
        setValuation({ commit }, valuation) {
            commit('setRatingValuation', valuation);
        },
        async saveValuation({ commit, state }, ratingValuationToSave = null) {
            try {

                commit('setSaving', true);
                const rv = ratingValuationToSave || state.ratingValuation;
                const response = await axios({
                    method: 'post',
                    url: jsRoutes.controllers.RatingValuationController.saveRatingValuation().url,
                    data: rv,
                });
                commit('setValidationSet', createValidationSet(response.data));

                if (response.data.success === true) {
                    const newRatingValuation = response.data.value;
                    commit('setRatingValuation', newRatingValuation);
                }
            }
            catch (exception) {
                commit('setException', exception);
                throw exception;
            }
            finally {
                commit('setSaving', false);
            }
        },
        async completeSetup({ commit, state, dispatch }) {
            try {
                commit('setSaving', true);
                const controller = jsRoutes.controllers.RatingValuationController;
                const response = await axios({
                    method: 'get',
                    url: controller.completeSetup(state.ratingValuation.id).url,
                });
                commit('setValidationSet', createValidationSet(response.data));

                if (response.data.success === true) {
                    const newRatingValuation = response.data.value;
                    commit('setRatingValuation', newRatingValuation);
                    dispatch('loadComparables');
                    dispatch('loadRollMaintenanceActivities', true, true);
                }
            }
            catch (exception) {
                commit('setException', exception);
                throw (exception);
            }
            finally {
                commit('setSaving', false);
            }
        },
        /* Auto value the current valuation */
        async autoValue({ commit, state, dispatch }, ignoreWarnings = false) {
            try {
                // try and save it first ...
                commit('setSaving', true);

                const { ratingValuation: rv } = state;
                const response = await axios({
                    method: 'post',
                    url: jsRoutes.controllers.RatingValuationController.saveRatingValuation().url,
                    data: rv,
                });

                const newRatingValuation = response.data.value;
                commit('setErrors', response.data.errors);
                commit('setValidationSet', createValidationSet(response.data));

                if (response.data.success === true) {
                    commit('setRatingValuation', newRatingValuation);

                    // Saved OK so auto value
                    const response = await axios({
                        method: 'post',
                        url: jsRoutes.controllers.RatingValuationController.autoValue(newRatingValuation.id).url,
                    });

                    const autoValuedValuation = response.data.value;
                    commit('setErrors', response.data.errors);
                    commit('setValidationSet', createValidationSet(response.data));

                    if (response.data.success === true) {
                        commit('setRatingValuation', autoValuedValuation);

                        // and refresh all the comps
                        await dispatch('getDefaultComparableSearchCriteria');
                        await dispatch('loadComparables');
                    }
                }
            }
            catch (exception) {
                commit('setException', exception);
                throw exception;
            }
            finally {
                commit('setSaving', false);
            }
        },
        async completeValuation({ commit, state }, ignoreWarnings = false) {
            try {
                commit('setSaving', true);
                const controller = jsRoutes.controllers.RatingValuationController;
                const response = await axios({
                    method: 'POST',
                    url: controller.completeValuation(state.ratingValuation.id, ignoreWarnings).url,
                });

                commit('setValidationSet', createValidationSet(response.data.ratingValuationResult));
                this.commit('propertyDraft/setValidationSet', createValidationSet(response.data.propertyDetailResult));

                commit('setErrors', state.validationSet.errors);

                if (response.data.success === true) {
                    const newRatingValuation = response.data.value;
                    commit('setRatingValuation', newRatingValuation);
                }
            }
            catch (exception) {
                commit('setException', exception);
                throw (exception);
            }
            finally {
                commit('setSaving', false);
            }
        },
        async validateValuation({ commit, state }, data)  {
            if (!data.propertyDetail || !data.activities) {
                throw Error('validate valuation requires propertyDetail and job activities')
            }

            const result = await RatingValuationController.validateOnSave(state.ratingValuation, data.propertyDetail, data.activities)
            commit('setValidationSet', createValidationSet(result.validationSet));
        },
        async validateValuationAtCompletion({ commit, state }, data)  {
            if (!data.propertyDetail || !data.activities) {
                throw Error('validate valuation requires propertyDetail and job activities')
            }

            const result = await RatingValuationController.validate(state.ratingValuation, data.propertyDetail, data.activities)
            commit('setValidationSet', createValidationSet(result.validationSet));
        }

    },
};

export default ratingValuation;

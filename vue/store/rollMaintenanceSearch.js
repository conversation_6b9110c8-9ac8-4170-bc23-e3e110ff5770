import numeral from 'numeral';
import axios from '../utils/AxiosHeaders';
import formatUtils from '../utils/FormatUtils';

function toNumber(text) {
    const n = numeral(text).value();
    if (Number.isNaN(n)) return null;
    return n;
}
const rollMaintenanceSearch = {
    namespaced: true,

    state: {
        firstLoad: true,
        searchCriteria: {
            qpid: null,
            offset: 0,
            pageSize: 100,
            sortField: 'VALUATION_REFERENCE',
            activityType: 'Objection',
            sortDescending: false,
            activityStatuses: [
                {
                    code: 'NEW',
                    // TODO: Shouldnt need to hard code descriptions
                    // ... dropdown needs fixing to use CODES ONLY
                    description: 'Setup Required',
                },
            ],
            planStatusValues: [],
            ratingUnitCategories: null,
            initiatedDateFrom: null,
            initiatedDateTo: null,
            createdDateFrom: null,
            createdDateTo: null,
            ntUserNames: null,
            buildingConsentCriteria: {
                consentNumber: null,
                costFrom: null,
                costTo: null,
                natureOfWorks: null,
            },
            objectionCriteria: {
                objectionTypes: null,
                revisionYear: null,
                objectionCategoryGroups: null,
                objectionStatuses: null,
                objectionJobStatuses: null,
                objectorName: null,
                valuers: null,
                registeredValuers: null,
                saleGroups: [],
                rolls: []
            },
            valuationSearchCriteria: {
                isUnassigned: null,
                userIds: null,
                statusCodes: null,
                isUnassigned: null,
                offset: 0,
                max: 0,
                sort: null,
                order: null,
                firstLoad: true,
                valuationReportTypes: null,
                valuationJobStatuses: null,
                valuers: null,
                inspectionDateFrom: null,
                inspectionDateTo: null,
            }
        },
        loading: true,
        lastResults: {
            searchCriteria: {},
            results: null,
        },
        currentRequestCriteria: {},
        error: null,
        exportSearchResults: {},
    },

    getters: {
        totalPageCount: (state) => {
            if (!state.lastResults || !state.lastResults.results) return 0;
            const { pageSize } = state.searchCriteria;
            const { totalResultCount } = state.lastResults.results;

            return Math.ceil(totalResultCount / pageSize);
        },
        currentPage: state => (state.searchCriteria.offset / state.searchCriteria.pageSize) + 1,
        linzTotalPageCount: (state) => {
            if (!state.linzLastResults || !state.linzLastResults.results) return 0;
            const { pageSize } = state.linzSearchCriteria;
            const { totalResultCount } = state.linzLastResults.results;

            return Math.ceil(totalResultCount / pageSize);
        },
        linzCurrentPage: state => (state.linzSearchCriteria.offset / state.linzSearchCriteria.pageSize) + 1,
    },
    mutations: {
        setOffset(state, value) {
            state.searchCriteria.offset = value;
        },
        setPageSize(state, value) {
            state.searchCriteria.pageSize = value;
        },
        setLastResults(state, value) {
            state.lastResults = value;
        },
        setCurrentRequestCriteria(state, value) {
            state.currentRequestCriteria = value;
        },
        setFirstLoad(state, value) {
            state.firstLoad = value;
        },
        setLoading(state, value) {
            state.loading = value;
        },
        setSortField(state, value) {
            state.searchCriteria.sortField = value;
        },
        setSortDescending(state, value) {
            state.searchCriteria.sortDescending = value;
        },
        setSearchCriteriaItem(state, { id, value }) {
            const searchCriteria = { ...state.searchCriteria };
            searchCriteria[id] = value;
            state.searchCriteria = searchCriteria;
        },
        setBuildingConsentCriteriaItem(state, { id, value }) {
            const buildingConsentCriteria = { ...state.searchCriteria.buildingConsentCriteria };
            buildingConsentCriteria[id] = value;
            const searchCriteria = { ...state.searchCriteria };
            searchCriteria.buildingConsentCriteria = buildingConsentCriteria;
            state.searchCriteria = searchCriteria;
        },
        setObjectionCriteriaItem(state, { id, value }) {
            const objectionCriteria = { ...state.searchCriteria.objectionCriteria };
            objectionCriteria[id] = value;
            const searchCriteria = { ...state.searchCriteria };
            searchCriteria.objectionCriteria = objectionCriteria;
            state.searchCriteria = searchCriteria;
        },
        setValuationCriteriaItem(state, { id, value }) {
            const valuationSearchCriteria = { ...state.searchCriteria.valuationSearchCriteria };
            valuationSearchCriteria[id] = value;
            const searchCriteria = { ...state.searchCriteria };
            searchCriteria.valuationSearchCriteria = valuationSearchCriteria;
            state.searchCriteria = searchCriteria;
        },
        setError(state, value) {
            state.error = value;
            state.loading = false;
            // if any errors then clear localstorage (as that may have caused it)
            if(localStorage && value)
                localStorage.removeItem('lastRollMaintenanceSearchCriteria');
        },
        setSearchCriteria(state, value) {
            state.searchCriteria = { ...value };
        },
        setExportSearchResults(state, value) {
            console.log('setExportSearchResults: ', value);
            state.exportSearchResults = value;
        },
    },
    actions: {
        async searchBySuppliedCriteria({ commit }, suppliedCriteria) {
            try {
                commit('setError', null);
                commit('setSearchCriteria', suppliedCriteria);
                commit('setCurrentRequestCriteria', suppliedCriteria);
                commit('setLoading', true);

                const rollMaintenanceController = jsRoutes.controllers.RollMaintenanceController;

                const { url } = rollMaintenanceController.searchRollMaintenanceActivities();
                const response = await axios({
                    method: 'post',
                    url,
                    data: suppliedCriteria,
                });

                commit('setLastResults', {
                    searchCriteria: suppliedCriteria,
                    results: response.data,
                });
            } catch (error) {
                let errorMessage = 'An unexpected error has occurred, please try again.';
                if (error.response && (error.response.status === 504 || error.response.status === '504')) {
                    errorMessage = 'Your request has timed out, please provide additional search criteria to refine your search.';
                }
                commit('setError', errorMessage);
                commit('setLastResults', {
                    searchCriteria: suppliedCriteria,
                    results: {
                        totalResultCount: 0,
                        rollMaintenanceActivities: [],
                    },
                });
                throw new Error(errorMessage);
            } finally {
                commit('setLoading', false);
            }
        },

        async searchByQpid({ commit, state, rootState }, qpid) {
            try {
                commit('setError', null);

                const searchCriteria = {
                    offset: 0,
                    pageSize: 100,
                    sortField: 'VALUATION_REFERENCE',
                    activityType: 'BC',
                    sortDescending: false,
                    activityStatuses: [],
                    ratingUnitCategories: null,
                    initiatedDateFrom: null,
                    initiatedDateTo: null,
                    createdDateFrom: null,
                    createdDateTo: null,
                    qpid: qpid,
                    ntUserNames: null,
                    buildingConsentCriteria: {
                        consentNumber: null,
                        costFrom: null,
                        costTo: null,
                        natureOfWorks: null,
                    },
                };

                commit('setCurrentRequestCriteria', searchCriteria);

                commit('setLoading', true);

                const rollMaintenanceController = jsRoutes.controllers.RollMaintenanceController;

                const { url } = rollMaintenanceController.searchRollMaintenanceActivities();

                await axios({
                    method: 'post',
                    url,
                    data: searchCriteria,
                }).then((response) => {
                    const { data } = response;
                    commit('setLastResults', {
                        searchCriteria,
                        results: data,
                    });
                }).catch((error) => {
                    let errorMessage = 'An unexpected error has occurred, please try again.';
                    if (error.response && (error.response.status === 504 || error.response.status === '504')) {
                        errorMessage = 'Your request has timed out, please provide additional search criteria to refine your search.';
                    }
                    commit('setError', errorMessage);
                    commit('setLastResults', {
                        searchCriteria,
                        results: {
                            totalResultCount: 0,
                            rollMaintenanceActivities: [],
                        },
                    });
                }).finally(() => {
                    commit('setLoading', false);
                });
            } catch(error) {
                commit('setError', error);
            }
        },
        async searchActivities({ commit, state, rootState }, loadCriteria) {
            try {
                commit('setError', null);
                /* TODO Probably should disconnect root TA code list from this RMA searching */
                const { taCodes } = rootState.taCodes;
                state.searchCriteria.taCodes = taCodes;

                // if asked to load last search then do that
                if(loadCriteria) {
                    // load last search criteria (be careful about changes to criteria objects)
                    if(localStorage) {
                        const criteria = JSON.parse(localStorage.getItem('lastRollMaintenanceSearchCriteria'));
                        if (criteria) {
                            if(state?.searchCriteria?.objectionCriteria){
                                criteria.objectionCriteria = state.searchCriteria.objectionCriteria
                            } else {
                                criteria.objectionCriteria = {
                                    objectionTypes: null,
                                    revisionYear: null,
                                    objectionCategoryGroups: null,
                                    objectionStatuses: null,
                                    objectionJobStatuses: null,
                                    objectorName: null,
                                    valuers: null,
                                    registeredValuers: null,
                                    saleGroups: [],
                                    rolls: []
                                };
                            }
                            state.searchCriteria = criteria;
                        }
                    } else {
                        // TODO Clear any QPID search if there is one
                        state.searchCriteria.qpid = null;
                    }
                } else {
                    // Otherwise save this search to local storage
                    if(localStorage) {
                        // Don't remember identifiers when searching
                        // Put into variable search criteria all properties from state.search criteria except qpid, rollnumber, assessmentnumber, assessmentsuffix and BC criteria
                        let { qpid, rollNumber, assessmentNumber, assessmentSuffix, buildingConsentCriteria, ...searchCriteria } = state.searchCriteria;
                        // Put into variable consent criteria all but consent number from building consent criteria
                        let { consentNumber, ...consentCriteria } = buildingConsentCriteria;

                        searchCriteria.buildingConsentCriteria = consentCriteria;

                        // localStorage.setItem('lastRollMaintenanceSearchCriteria', JSON.stringify(state.searchCriteria));
                        localStorage.setItem('lastRollMaintenanceSearchCriteria', JSON.stringify(searchCriteria));
                    }
                }

                const searchCriteria = Object.assign({}, state.searchCriteria);

                const buildingConsentCriteria = Object.assign(
                    {},
                    state.searchCriteria.buildingConsentCriteria,
                );
                searchCriteria.buildingConsentCriteria = buildingConsentCriteria;

                // convert activity status array from object to code
                searchCriteria.activityStatuses = searchCriteria.activityStatuses.map(
                    status => status && status.code,
                );
                // convert categories to array
                if (searchCriteria.ratingUnitCategories) {
                    searchCriteria.ratingUnitCategories = searchCriteria.ratingUnitCategories
                        .replace(/\s/g, '').split(',').filter(el => (el !== '' && el !== null));
                }
                // convert sale group codes to array
                if (searchCriteria.saleGroupCodes && !Array.isArray(searchCriteria.saleGroupCodes)) {
                    searchCriteria.saleGroupCodes = searchCriteria.saleGroupCodes
                        .replace(/\s/g, '').split(',').map(text => (Number.isNaN(text) ? null : toNumber(text))).filter(el => (el !== '' && el !== null));
                }
                // convert roll numbers to array
                if (searchCriteria.rollNumbers && !Array.isArray(searchCriteria.rollNumbers)) {
                    searchCriteria.rollNumbers = searchCriteria.rollNumbers
                        .replace(/\s/g, '').split(',').map(text => (Number.isNaN(text) ? null : toNumber(text))).filter(el => (el !== '' && el !== null));
                }
                // convert nature of works to code
                if (searchCriteria.buildingConsentCriteria.natureOfWorks) {
                    const { code } = searchCriteria.buildingConsentCriteria.natureOfWorks;
                    searchCriteria.buildingConsentCriteria.natureOfWorks = code;
                }

                commit('setCurrentRequestCriteria', searchCriteria);

                if (!searchCriteria.taCodes || searchCriteria.taCodes.length === 0) {
                    // short circuit searching as no TA means no data
                    commit('setLastResults', {
                        searchCriteria,
                        results: {
                            totalResultCount: 0,
                            rollMaintenanceActivities: [],
                        },
                    });
                    commit('setLoading', false);
                    return;
                }

                commit('setLoading', true);

                // console.log('making request', searchCriteria);
                const rollMaintenanceController = jsRoutes.controllers.RollMaintenanceController;

                const { url } = rollMaintenanceController.searchRollMaintenanceActivities();

                await axios({
                    method: 'post',
                    url,
                    data: searchCriteria,
                }).then((response) => {
                    const { data } = response;
                    commit('setLastResults', {
                        searchCriteria,
                        results: data,
                    });
                }).catch((error) => {
                    let errorMessage = 'An unexpected error has occurred, please try again.';
                    if (error.response && (error.response.status === 504 || error.response.status === '504')) {
                        errorMessage = 'Your request has timed out, please provide additional search criteria to refine your search.';
                    }
                    commit('setError', errorMessage);
                    commit('setLastResults', {
                        searchCriteria,
                        results: {
                            totalResultCount: 0,
                            rollMaintenanceActivities: [],
                        },
                    });
                }).finally(() => {
                    commit('setLoading', false);
                });
            } catch(error) {
                commit('setError', error);
            }
        },
        changeSort({ dispatch, commit }, sortDetails) {
            commit('setSortField', sortDetails.columnName);
            commit('setSortDescending', (sortDetails.direction === 'DESC'));
            dispatch('searchActivities');
        },
        changePage({ dispatch, commit, state }, page) {
            commit('setOffset', (page - 1) * state.searchCriteria.pageSize);
            dispatch('searchActivities');
        },
        async exportBuildingConsentsFromRollCriteria({commit}, suppliedCriteria){
            try {
                const rollMaintenanceController = jsRoutes.controllers.RollMaintenanceController;
                const { url } = rollMaintenanceController.exportBuildingConsents();
                const response = await axios({
                    method: 'post',
                    url,
                    data: JSON.parse(JSON.stringify(suppliedCriteria)),
                });
                commit('setExportSearchResults', response.data);
            }
            catch (error) {
                let errorMessage = 'An unexpected error has occurred, please try again.';
                if (error.response && (error.response.status === 504 || error.response.status === '504')) {
                    errorMessage = 'Your request has timed out, please provide additional search criteria to refine your search.';
                }
                commit('setError', errorMessage);
                commit('setExportSearchResults', {});
                throw new Error(errorMessage);
            }
        }
    },
};

export default rollMaintenanceSearch;

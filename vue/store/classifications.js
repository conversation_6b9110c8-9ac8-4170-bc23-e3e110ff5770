import axios from '../utils/AxiosHeaders';

const classifications = {
    namespaced: false,
    state: {
        classifications: {},
        loaded: false,
    },
    mutations: {
        /* Cache all classifications in one hit (intended for upfront use) */
        setClassifications: function (state, values) {
            const classifications = state.classifications;
            state.classifications = { ...classifications, ...values };
        },
        /* Cache individual classifications on an as needed basis */
        addClassification: function (state, item) {
            // TODO Believe this method is being called so that "inactive" classifications can still be displayed. Woudl recommend that approach is changed.
            state.classifications[item.key] = item.data;
        }
    },
    getters: {
        getCategoryClassifications: (state) => (category) => {
            if (!state.classifications[category]) {
                return [];
            }
            return state.classifications[category];
        },
        classificationsLoaded: (state) => {
            return Object.keys(state.classifications).length > 0;
        },
    },
    actions: {
        async fetchTAZoneClassification({ commit, getters }, taCode) {
            const taClassification = `TA_${taCode}_LandZone_DVR`;

            const classificationList = getters.getCategoryClassifications(taClassification)
            if (classificationList && classificationList.length) {
                return;
            }

            let url = jsRoutes.controllers.ReferenceData.displayClassification(taClassification, true).url;

            const response = await new axios({
                method: 'get',
                url,
            });

            commit('addClassification', {key: taClassification, data: response.data});
        },
        async fetchClassifications({ commit }) {
            try {
                const requests = [];

                const allCategories = listOfDisplayClassifications.concat(listOfClassificationsByCategory).concat(listOfClassificationsByCategoryFilteredArray);

                requests.push(
                    new axios({
                        method: 'POST',
                        url: jsRoutes.controllers.ReferenceData.displayClassifications().url,
                        data: {
                            categories: allCategories,
                            isActive: true,
                        },
                    }),
                );

                requests.push(
                    new Promise(async (resolve, reject) => {
                        let response;
                        const inputKeys = listOfApiPicklistKeys.join(',');
                        try {
                            response = await new axios({
                                method: 'GET',
                                url: jsRoutes.controllers.ApiPicklistController.getPicklistValue(inputKeys).url,
                            });
                        }
                        catch (error) {
                            console.error(error);
                            reject(error);
                        }
                        const results = {};
                        Object.keys(response.data.result).forEach((key) => {
                            results[key] = {
                                key,
                                data: response.data.result[key],
                            };
                        });
                        resolve({
                            key: 'ApiPicklist',
                            results,
                        });
                    }),
                );

                const responses = await Promise.all(requests);

                for (const response of responses) {
                    if (response.key === 'ApiPicklist') {
                        for (const key of Object.keys(response.results)) {
                            responses.push(response.results[key]);
                        }
                    }
                }

                const classificationsToStore = {};

                responses.forEach((item) => {
                    if (item.key === 'ApiPicklist') {
                        return;
                    }

                    if (!item.data) {
                        console.error('[Monarch error]: Invalid response ', item);
                        return;
                    }

                    const values = item.data;
                    if (values.length === 0) {
                        console.error(`[Monarch warn]: No classifications returned from ${item.request.responseURL}`);
                        return;
                    }

                    if (item.key) {
                        classificationsToStore[item.key] = values;
                    }
                    else {
                        // * otherwise use the classification category
                        for (const value of values) {
                            const { category } = value;
                            // * if the category is in listOfClassificationsByCategoryFilteredArray then use the storeKey as category
                            const storeKey = listOfClassificationsByCategoryFilteredArray.includes(category)
                                ? listOfClassificationsByCategoryFiltered.find(classificationItem => classificationItem.category === category).storeKey
                                : category;

                            classificationsToStore[storeKey] = classificationsToStore[storeKey] || [];
                            classificationsToStore[storeKey].push(value);
                        }
                    }
                });

                commit('setClassifications', classificationsToStore);
                /* HACK Flag classifications have loaded.  Using already existing technique as a trigger for MasterDetails to finish loading. Nasty. */
                commit('classificationsLoaded', true, { root: true });
            }
            catch (error) {
                console.error('[Monarch error]: FetchClassifications failed', error);
            }
        },
    },
};

export default classifications;

const listOfApiPicklistKeys = [
    'ObjectionType',
    'CategoryGroupType',
    'ObjectionStatusType',
    'ObjectionJobStatusType',
    'ObjectionJobFurtherContactReasonType',
    'ObjectionJobInspectionType',
    'ObjectionJobObjectorContactType',
    'ObjectionJobReviewRiskType',
    'WorkUnitValuer',
    'PropertyGroupingTypeCommercial',
    'QualityType',
    'LocationType',
    'StreetLocationType',
    'WorksheetImprovementType',
    'ActualRentalType',
    'CostCentre',
    'TerritorialAuthority'
];

const listOfDisplayClassifications = [
    'BuildingAge',
    'BuildingConstruction',
    'BuildingCondition',
    'RegionType',
    'StreetType',
    'ValuationPurpose',
    'ValuationReportType',
    'InspectionType',
    'MainBathroomQuality',
    'EnsuiteQuality',
    'KitchenQuality',
    'InternalCondition',
    'InternalCondition_HV',
    'HeatingType',
    'Insulation',
    'DoubleGlazing',
    'AlternativeEnergy',
    'Ventilation',
    'HouseType_HV',
    'ExteriorCladding',
    'Foundation',
    'Joinery',
    'RoofStyle',
    'Spouting',
    'RoofConstruction',
    'ExternalCondition',
    'StandardOfAccommodation',
    'LayoutDescription',
    'Laundry',
    'InternalLinings',
    'Floors',
    'FloorAreaDescription',
    'Chattels',
    'OtherFeatures',
    'KitchenLayout',
    'CodeCompliance',
    'Appliances',
    'BenchandSink',
    'KitchenFloor',
    'MainBathroom',
    'MainBathroomDescription',
    'Ensuite',
    'EnsuiteDescription',
    'MinorSiteImprovements',
    'Driveway',
    'Landscaping',
    'Fencing',
    'CurrentUse',
    'Tenure',
    'Contour',
    'Shape',
    'Access',
    'Views',
    'Services',
    'LogoforReport',
    'ComplianceStatement',
    'SpecialAssumptions',
    'LivingArea',
    'LivingAreaDescription',
    'Bedroom',
    'BedroomDescriptions',
    'HomeofficeorStudy',
    'HomeOfficeDescriptions',
    'BathroomorToilet',
    'GarageType',
    'GarageTypeDescription',
    'Modernisation',
    'OtherBuildings',
    'OtherBuildingsDescription',
    'MajorSiteImprovements',
    'OtherRisks',
    'EnvironmentalConsiderations',
    'ReasonForPeerReview',
    'HeatingType',
    'Insulation',
    'DoubleGlazing',
    'AlternativeEnergy',
    'StudHeight',
    'AdditionalFeatures',
    'UnitType',
    'Source',
    'LocalityIssue',
    'EarthquakeRatingAssessor',
    'WeatherTightnessIssue',
    'OtherConstructionIssue',
    'PlanningRestriction',
    'ContaminationIssue',
    'SitesOfSpecialSignificance',
    'QualityofExternalPresentation',
    'QualityofInternalPresentation',
    'HomeValuationJobStatus',
    /*
    //Classifications removed ...
    'TAZoning',
    'TAPZoning',
    'SuburbOrTown',
    'RoofCondition',
    'EnvironmentalConditions',
    'MarketCommentDS',
    'ValuationApproachDS',
    'DemandForSubjectPropertyDS',
    'BasisOfValuationDS',
    'SpecialConditionsDS',
    'AdditionalCommentsDS',
    */
    'HouseType_DVR',
    'Age_DVR',
    'Category_DVR',
    'LandUse_DVR',
    'NatureOfImprovements_DVR',
    'Aspect',
    'PropertyType',
    'AttachmentTypes',
    'ClassOfSurroundingImprovements_DVR',
    'LotPosition_DVR',
    'Contour_DVR',
    'LandscapingQuality_DVR',
    'View_DVR',
    'ViewScope_DVR',
    'RollMaintenanceActivityStatus',
    'InspectionState',
    'NatureOfWorks',
    'OtherImprovement',
    'FeatureAge',
    'ModernisationAge',
    'FeatureQuality',
    'UnitOfMeasure',
    'BuildingType',
    'BuildingFeature',
    'FloorConstruction_DVR',
    'Foundation_DVR',
    'WallConstruction_DVR',
    'RoofConstruction_DVR',
    'DoubleGlazing',
    'Insulation',
    'SpaceType',
    'SpaceFeature',
    'HeatingType',
    'GarageFeature',
    'Source_DVR',
    'OutputCode_DVR',
    'ImprovementDateRange',
    'Tags',
    'PeerReviewSetup',
    'ZoneOverlay',
    'RuralOtherBuildingTypes',
    'RuralOtherDwellingTypes',
    'RuralOtherImprovement',
    'ValuationInsuranceCompany',
    'ValuationManagementCompany',
    'ValuationEngineerName',
    'ValuationPropertyHasRetainingWalls',
    'RevenueRoleTarget'
];

const listOfClassificationsByCategory = [
    'Age',
    'MainBathroomAge',
    'EnsuiteAge',
    'KitchenAge',
    'KitchenAge_HV',
    'RedecorationAge',
    'PlumbingAge',
    'WiringAge',
    'Risks',
    'OtherBuildingsAge',
    'GarageAge',
    'LiquefactionRating',
    'FloodZone',
    'TsunamiRisk',
    'LandslipRisk',
    'EarthquakeRating',
    'Quality',
    'LandUseType',
    'SeniorValuerInvolvement',
    'ValuationStandards',
    'FileType',
];

const listOfClassificationsByCategoryFiltered = [
    {
        category: 'MarketCommentsList',
        filterId: 'marketComment',
        storeKey: 'MarketCommentDS'
    },
    {
        category: 'ValuationApproaches',
        filterId: 'valuationApproach',
        storeKey: 'ValuationApproachDS'
    },
    {
        category: 'SpecialConditions',
        filterId: 'specialConditions',
        storeKey: 'SpecialConditionsDS'
    },
    {
        category: 'AdditionalComments',
        filterId: 'additionalComments',
        storeKey: 'AdditionalCommentsDS'
    },
    {
        category: 'BasisofValuation',
        filterId: 'basisOfValue',
        storeKey: 'BasisOfValuationDS'
    },
    {
        category: 'DemandforSubjectProperty',
        filterId: 'demandForSubjectProperty',
        storeKey: 'DemandForSubjectPropertyDS'
    }
];

const listOfClassificationsByCategoryFilteredArray = listOfClassificationsByCategoryFiltered.map(item => item.category);

import axios from '../utils/AxiosHeaders';

const worksheet = {
    namespaced: true,

    state: {
        reasonForChange: null,
        loading: false,
        saving: false,
        exception: null,
        errors: null,
    },

    mutations: {
        setReasonForChange(state, value) {
            state.reasonForChange = value;
        },
        // TODO Could we use a mixin for standard stuff?
        setLoading(state, value) {
            state.loading = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setErrors(state, value) {
            console.error(value);
            state.errors = value;
        },
        setException(state, value) {
            console.error(value);
            state.exception = value;
        },
    },
    actions: {
        async getExistingReasonForChange({commit}, { qpid, userName }) {
            let response;
            try {
                commit('setLoading', true);

                response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.ReasonForChangeController.getExistingReasonForChange(qpid, userName).url,
                });

                const result = response.data;

                commit('setReasonForChange', result);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting reason for change.');

                commit('setReasonForChange', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },
        async addReasonForChange({ commit }, reasonForChange) {
            try {
                commit('setSaving', true);

                const response = await axios({
                    method: 'post',
                    data: reasonForChange,
                    url: jsRoutes.controllers.ReasonForChangeController
                        .addReasonForChange().url,
                });

                commit('setReasonForChange', response.data.reasonForChange);
                commit('setErrors', response.data.errors);

                // if (response.data.success === true) {
                //     const newPropertyDetail = response.data.value;
                //     commit('setPropertyDetailObject', newPropertyDetail);
                //     commit('setFormIsStale', false);
                // }

                commit('setSaving', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        },
    },
};

export default worksheet;

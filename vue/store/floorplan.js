export default {
    namespaced: true,
    state: {
        floorPlans: null,
        resources: [],
        loading: true,
        saving: false,
        exception: null,
        listExpanded: false,
        selectedTool: null,
        disabledTool: null,
        warnings: null,
        errors: null,
    },
    mutations: {
        setFloorPlans(state, value) {
            state.floorPlans = value;
        },
        setResources(state, value) {
            state.resources = value || [];
        },
        setLoading(state, value) {
            state.loading = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setException(state, value) {
            state.exception = value;
        },
        setListExpanded(state, value) {
            state.listExpanded = value;
        },
        setSelectedTool(state, value) {
            state.selectedTool = value;
        },
        setDisabledTool(state, value) {
            state.disabledTool = value;
        },
        setWarnings(state, value) {
            state.warnings = value || [];
        },
        setErrors(state, value) {
            state.errors = value || [];
        },
    },
    actions: {
        async getFloorPlans({ commit }, qpid) {
            try {
                commit('setLoading', true);

                const response = await fetch(
                    jsRoutes
                        .controllers
                        .FloorPlanToolController
                        .getFloorPlans(qpid)
                        .url,
                );
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                // TECHDEBT: until images are in s3 load each image in separate request to overcome lambda 6mb request size limit
                const imgResources = [];
                await Promise.all(data.floorPlans.map(async (plan) => {
                    const [imageRes, thumbnailRes] = await Promise.all([
                        fetch(jsRoutes.controllers.FloorPlanToolController.getFloorPlanImage(plan.imgResourceId).url),
                        fetch(jsRoutes.controllers.FloorPlanToolController.getFloorPlanImage(plan.thumbnailImgResourceId).url),
                    ]);
                    const [image, thumbnail] = await Promise.all([
                        imageRes.json(),
                        thumbnailRes.json(),
                    ]);
                    imgResources.push(image);
                    imgResources.push(thumbnail);
                }));


                commit('setFloorPlans', data.floorPlans || []);
                commit('setResources', imgResources || []);
            } catch (error) {
                console.error(`[Monarch error]: Error loading floor plans for QPID:${qpid}`);
                commit('setException', error);
                commit('setFloorPlans', []);
                commit('setResources', []);
                throw error;
            } finally {
                commit('setLoading', false);
            }
        },
        async saveFloorPlans({ commit }, { floorPlans, username }) {
            try {
                commit('setSaving', true);

                const response = await fetch(
                    jsRoutes
                        .controllers
                        .FloorPlanToolController
                        .saveFloorPlans(username)
                        .url, {
                        method: 'post',
                        headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                        body: JSON.stringify(floorPlans),
                    },
                );
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setFloorPlans', data.floorPlans || []);
                commit('setErrors', data.errors);
                commit('setWarnings', data.warnings);
            } catch (error) {
                console.error('Error saving floor plans:', error);
                commit('setFloorPlans', []);
                throw error;
            } finally {
                commit('setSaving', false);
            }
        },
    },
};

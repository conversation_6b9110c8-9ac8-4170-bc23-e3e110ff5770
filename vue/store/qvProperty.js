const qvProperty = {
    namespaced: true,

    state: {
        qvProperty: null,
        loading: false,
        saving: false,
        exception: null,
        errors: null,

    },

    mutations: {
        setQvProperty(state, value) {
            state.qvProperty = value;
        },
        // TODO Could we use a mixin for standard stuff?
        setLoading(state, value) {
            state.loading = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setErrors(state, value) {
            console.error(value);
            state.errors = value;
        },
        setException(state, value) {
            console.error(value);
            state.exception = value;
        },
    },
    actions: {
        async getQvProperty({commit}, propertyId) {
            console.log('getQvProperty ' + propertyId);

            try {
                commit('setLoading', true);

                const response = await fetch(jsRoutes.controllers.PropertyMasterData.getQvProperty(propertyId).url);
                const result = await response.json();

                commit('setQvProperty', result);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting qvProperty.');

                commit('setQvProperty', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },
        async saveQvProperty({commit, getters}){
            try {
                commit('setSaving', true);

                const response = await fetch(jsRoutes.controllers.PropertyMasterData.saveQvProperty().url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: JSON.stringify(getters.preparedQvProperty),
                });
                if (!response.ok) throw new Error('Failed to save QV Property');

                const result = await response.json();
                commit('setQvProperty', result.value);
                commit('setSaving', false);
            }
            catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        }
    },
    getters: {
        preparedQvProperty(state) {
            return state.qvProperty;
        }
    }
};

export default qvProperty;

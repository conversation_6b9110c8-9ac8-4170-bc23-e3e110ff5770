import axios from '../utils/AxiosHeaders';
import moment from 'moment';

const rollMaintenanceActivity = {
    namespaced: true,

    state: {
        loading: false,
        rollMaintenanceActivity: {
            property: null,
            buildingConsent: null
        },
        saving: false,
        saveResult: null,
        exception: null,
    },
    getters: {
        areRollMaintenanceActivityDatesValid(state) {
            const dateFields = [
                'estimatedInspectionReadyDate',
                'initiatedDate',
            ];
            return dateFields.every((key) => moment(state.rollMaintenanceActivity[key], [moment.HTML5_FMT.DATE, 'DD/MM/YYYY'], true).isValid());
        },
    },
    mutations: {
        setActivity(state, value) {
            state.rollMaintenanceActivity = value;
            state.saveResult = null;
        },
        // TODO Could we use a mixin for standard stuff?
        setLoading(state, value) {
            state.loading = value;
            /* Clear errors and exception if started loading */
            if(value) {
                state.errors = null;
                state.exception = null;
            }
        },
        setSaving(state, value) {
            state.saving = value;
            /* Clear errors and exception when saving */
            if(value) {
                state.errors = null;
                state.exception = null;
            }
        },
        setSaveResult(state, value) {
            if(value.success == false)
                console.error(value);

            state.saveResult = value;
        },
        setException(state, value) {
            console.error(value);
            state.exception = value;
        },
    },
    actions: {
        async getActivity({commit}, rollMaintenanceActivityId) {
            console.log('getActivity ' + rollMaintenanceActivityId);

            let response;
            try {
                commit('setLoading', true);
                response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RollMaintenanceController.getRollMaintenanceActivity(rollMaintenanceActivityId).url,
                });

                const rollMaintenanceActivity = response.data;

                commit('setActivity', rollMaintenanceActivity);
                commit('setLoading', false);

            } catch (ex) {
                commit('setActivity', null);
                commit('setException', ex);
                commit('setLoading', false);
            }
        },
        async saveActivity({ commit, state, getters }) {
            console.log('saveActivity ' + state.rollMaintenanceActivity.id);

            let response;
            try {
                commit('setSaving', true);

                const rma = state.rollMaintenanceActivity;

                response = await axios({
                    method: 'post',
                    data: rma,
                    url: jsRoutes.controllers.RollMaintenanceController.saveRollMaintenanceActivity().url,
                });

                // TODO standardise error codes and global error handling for actions that fail
                if(response.data.success === false){
                    console.error(response.data);
                    commit('setSaveResult', {success: false, errors: response.data.errors});
                    commit('setSaving', false);
                    return;
                }

                var newActivity = response.data.value;
                commit('setActivity', newActivity);
                commit('setSaveResult', {success: true, errors: []});
                commit('setSaving', false);

            } catch (error) {
                commit('setException', error);
                commit('setSaving', false);
            }
        },
        async doAction({commit}, actionUrl) {
            let response;
            try {
                commit('setSaving', true);
                response = await axios({
                    method: 'get',
                    url: actionUrl,
                });

                if(response.data.success === false){
                    console.error(response.data);
                    commit('setSaveResult', {success: false, errors: response.data.errors});
                    commit('setSaving', false);
                    return;
                }

                const newActivity = response.data.value;
                commit('setActivity', newActivity);
                commit('setSaveResult', {success: response.data.success, errors: response.data.errors});

            } catch (err) {
                console.error(err);
            } finally {
                commit('setSaving', false);
            }
        },
        async requireMoreInformation({commit, dispatch}, {id, note, requestPlans}) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.requireMoreInformation(id, note ? note :'', requestPlans).url);
        },
        async informationProvided({commit, dispatch}, rollMaintenanceActivityId) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.informationProvided(rollMaintenanceActivityId).url);
        },
        async requireInspection({commit, dispatch}, {id, note}) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.requireInspection(id, note ? note :'').url);
        },
        async inspected({commit, dispatch}, rollMaintenanceActivityId) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.inspected(rollMaintenanceActivityId).url);
        },
        async constructionComplete({commit, dispatch}, rollMaintenanceActivityId) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.constructionComplete(rollMaintenanceActivityId).url);
        },
        async constructionInProgress({commit, dispatch}, rollMaintenanceActivityId) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.constructionInProgress(rollMaintenanceActivityId).url);
        },
        async complianceCertificateIssued({commit, dispatch}, rollMaintenanceActivityId) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.complianceCertificateIssued(rollMaintenanceActivityId).url);
        },
        async noComplianceCertificate({commit, dispatch}, rollMaintenanceActivityId) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.noComplianceCertificate(rollMaintenanceActivityId).url);
        },
        async togglePlansRequired({commit, dispatch}, rollMaintenanceActivityId) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.togglePlansRequired(rollMaintenanceActivityId).url);
        },
        async togglePlansDrawn({commit, dispatch}, rollMaintenanceActivityId) {
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.togglePlansDrawn(rollMaintenanceActivityId).url);
        },
        async togglePlanStatus({commit, dispatch}, {rollMaintenanceActivityId, event}){
            await dispatch('doAction', jsRoutes.controllers.RollMaintenanceController.togglePlanStatus(rollMaintenanceActivityId, event).url);
        },
        addValidationError({commit, state}, error) {
            let saveResult = state.saveResult;
            if (!saveResult) {
                saveResult = {
                    success: false,
                    errors: [],
                }
            }
            saveResult.success = false;
            if (!saveResult.errors) saveResult.errors = [];
            saveResult.errors = saveResult.errors.filter((err) => err.field !== error.field);
            if (error.message) {
                saveResult.errors.push(error);
            }
            if (saveResult.errors.length === 0)  {
                saveResult.success = true;
            }
            commit('setSaveResult', saveResult);
        },
    },
};

export default rollMaintenanceActivity;

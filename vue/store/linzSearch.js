import numeral from 'numeral';
import axios from '../utils/AxiosHeaders';
import formatUtils from '../utils/FormatUtils';

function toNumber(text) {
    const n = numeral(text).value();
    if (Number.isNaN(n)) return null;
    return n;
}
const linzSearch = {
    namespaced: true,

    state: {
        error: null,
        linzSearchCriteria: {
            offset: 0,
            pageSize: 5,
            fetchRecordCount: 0,
            selectedLinzSearchTab: ''
        },
        linzSearchLoading: false,
        linzLastResults: {
            searchCriteria: {},
            results: null,
        },
        linzFilterByTitles:null,
        maoriLandLink: 'https://customer.service.maorilandcourt.govt.nz/prweb/PRAuth/MlcAnonymousUser',
    },

    getters: {
        totalPageCount: (state) => {
            const { pageSize } = state.linzSearchCriteria;
            const { totalResultCount } = state.linzLastResults.results;
            return (!state.linzLastResults || !state.linzLastResults.results) ? 0 : Math.ceil(totalResultCount / pageSize);
        },
        currentPage: state => (state.linzSearchCriteria.offset / state.linzSearchCriteria.pageSize) + 1,
    },

    mutations: {
        setOffset(state, value) {
            state.linzSearchCriteria.offset = value;
        },
        setPageSize(state, value) {
            state.linzSearchCriteria.pageSize = value;
        },
        setLastResults(state, value) {
            state.linzLastResults = value;
        },
        setLoading(state, value) {
            state.linzSearchLoading = value;
        },
        setFetchRecordCount(state,value) {
            state.linzSearchCriteria.fetchRecordCount = value;
        },
        setError(state, value) {
            state.error = value;
            state.linzSearchLoading = false;
            // if any errors then clear localstorage (as that may have caused it)
            if(localStorage && value) {
                localStorage.removeItem('lastlinzSearchCriteria');
            }
        },
        setLinzSearchCriteriaItem(state, { id, value }) {
            const linzSearchCriteria = { ...state.linzSearchCriteria };

            if (id === 'landDistrictId') {
                linzSearchCriteria[id] = value.map(o => o.key);
            } else {
                linzSearchCriteria[id] = value;
            }

            state.linzSearchCriteria = linzSearchCriteria;
        },
        setLinzFilterByTitles(state, value) {
            state.linzFilterByTitles = value;
        }
    },
    actions: {
        async searchResult({ commit, state }, loadCriteria) {
            try {
                commit('setError', null);
                // if asked to load last search then do that
                if(loadCriteria) {
                    if(localStorage) {
                        const criteria = JSON.parse(localStorage.getItem('lastlinzSearchCriteria'));
                        if(criteria) {
                            state.linzSearchCriteria = criteria;
                        }
                    }
                } else {
                    // Otherwise save this search to local storage
                    if(localStorage) {
                        localStorage.setItem('lastlinzSearchCriteria', JSON.stringify(state.linzSearchCriteria));
                    }
                }
                const searchCriteria = Object.assign({}, state.linzSearchCriteria);
                commit('setLoading', true);

                const linzSearchController = jsRoutes.controllers.LinzSearchController;
                let requestUrl = '';
                switch (searchCriteria.selectedLinzSearchTab) {
                    case 'certificateTab' :
                        requestUrl = linzSearchController.getLinzByCertificate().url;
                        break;
                    case 'ownerTab' :
                        requestUrl = linzSearchController.getLinzByOwner().url;
                        break;
                    case 'legalDescriptionTab' :
                        if(searchCriteria.legalDescriptionId && searchCriteria.legalDescriptionId !== "") {
                            requestUrl = linzSearchController.getLinzByLegalDescriptionAutoSuggest().url;
                        } else {
                            requestUrl = linzSearchController.getLinzByLegalDescription().url;
                        }
                        break;
                    case 'parcelIdTab' :
                        requestUrl = linzSearchController.getLinzByParcelID().url;
                        break;
                }
                await axios({
                    method: 'post',
                    url:requestUrl,
                    data: searchCriteria,
                }).then((response) => {
                    const { data } = response;
                    if(!state.linzSearchCriteria.fetchRecordCount) data.totalResultCount = state.linzLastResults.results.totalResultCount;
                    commit('setLastResults', {
                        searchCriteria,
                        results: data,
                    });
                }).catch((error) => {
                    let errorMessage = 'An unexpected error has occurred, please try again.';
                    if (error.response && (error.response.status === 504 || error.response.status === '504')) {
                        errorMessage = 'Your request has timed out, please provide additional search criteria to refine your search.';
                    }
                    commit('setError', errorMessage);
                    commit('setLastResults', {
                        searchCriteria,
                        results: null,
                    });
                    if(searchCriteria.legalDescriptionId && searchCriteria.legalDescriptionId !== "") {
                        commit('legalDescriptionId', null);
                        commit('legalDescription', null);
                    }
                }).finally(() => {
                    commit('setLoading', false);
                });
            } catch(error) {
                commit('setError', error);
            }
        },
        changePage({ dispatch, commit, state }, page) {
            commit('setOffset', (page - 1) * state.linzSearchCriteria.pageSize);
            commit('setFetchRecordCount', 0);
            dispatch('searchResult');
        },
        clearLastResults({ commit }) {
            commit('setLastResults', {
                searchCriteria: {},
                results: null,
            });
        }

    },
};

export default linzSearch;

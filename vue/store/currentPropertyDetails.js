import { isUndefined } from 'util';
import axios from '../utils/AxiosHeaders';

export default {
    namespaced: true,

    state: {
        propertyDetail: null,
        loading: null,
        saving: null,
        exception: null,
        validationSet: null,
        formIsStale: true,
    },
    mutations: {
        setPropertyDetailObject(state, value) {
            state.formIsStale = true;
            state.propertyDetail = value;
        },
        setSinglePropertyDetail(state, { id, value }) {
            const propertyDetail = { ...state.propertyDetail };
            if (!isUndefined(state.propertyDetail.landUse[id])) {
                propertyDetail.landUse[id] = value;
            } else if (!isUndefined(state.propertyDetail.audit[id])) {
                propertyDetail.audit[id] = value;
            } else if (!isUndefined(state.propertyDetail.summary[id])) {
                propertyDetail.summary[id] = value;
            } else if (!isUndefined(state.propertyDetail.site[id])) {
                propertyDetail.site[id] = value;
            } else {
                propertyDetail[id] = value;
            }
            state.propertyDetail = propertyDetail;
            state.formIsStale = true;
        },
        setLoading(state, value) {
            state.loading = value;
            if (value) {
                state.exception = null;
                state.validationSet = null;
            }
        },
        setSaving(state, value) {
            state.saving = value;
            if (value) {
                state.exception = null;
                state.validationSet = null;
            }
        },
        setException(state, value) {
            state.exception = value;
            if (value) {
                console.error(value); // eslint-disable-line no-console
            }
        },
        setValidationSet(state, value) {
            if (!Object.keys(value).includes('success') && !value.errors?.length && !value.warnings?.length) {
                value.success = true;
            }
            state.validationSet = value;
        },
        setFormIsStale(state, value) {
            state.formIsStale = value;
        },
    },
    actions: {
        async getPropertyDetailByPropertyId({ commit }, propertyId) {
            try {
                commit('setLoading', true);
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyDetailController
                        .getCurrentPropertyDetail(propertyId).url,
                });

                const propertyDetail = response ? response.data : null;
                if(!propertyDetail)
                    throw Error('No property detail found or you do not have permission to see it.');

                if (!propertyDetail.audit) {
                    propertyDetail.audit = {
                        description: null,
                        summaryOfChanges: null,
                        reasonForChange: null,
                        outputCode: null,
                        source: null,
                    };
                }
                commit('setPropertyDetailObject', propertyDetail);
                commit('setFormIsStale', false);
                commit('setLoading', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setLoading', false);
                throw exception;
            }
        },

        async getPropertyDetail({ commit }, propertyDetailId) {
            try {
                commit('setLoading', true);
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyDetailController
                        .getPropertyDetail(propertyDetailId).url,
                });

                const propertyDetail = response ? response.data : null;
                if (!propertyDetail.audit) {
                    propertyDetail.audit = {
                        description: null,
                        summaryOfChanges: null,
                        reasonForChange: null,
                        outputCode: null,
                        source: null,
                    };
                }
                commit('setPropertyDetailObject', propertyDetail);
                commit('setFormIsStale', false);
                commit('setLoading', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setLoading', false);
                throw exception;
            }
        },

        async savePropertyDraft({ commit, getters }) {
            try {
                commit('setSaving', true);
                const response = await axios({
                    method: 'post',
                    data: getters.preparedPropertyDetail,
                    url: jsRoutes.controllers.PropertyDetailController
                        .savePendingPropertyDetail().url,
                });

                commit('setValidationSet', response.data);

                if (response.data.success === true) {
                    const newPropertyDetail = response.data.value;
                    commit('setPropertyDetailObject', newPropertyDetail);
                    commit('setFormIsStale', false);
                }

                commit('setSaving', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        },

        async validatePropertyDraft({ commit, state }) {
            try {
                commit('setException', null);
                commit('setValidationSet', null);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyDetailController
                        .validatePendingPropertyDetail(state.propertyDetail.id).url,
                });
                commit('setValidationSet', response.data);
            } catch (exception) {
                commit('setException', exception);
                throw exception;
            }
        },
    },
    getters: {
        preparedPropertyDetail(state) {
            const propertyDetail = { ...state.propertyDetail };
            const { buildings = {} } = propertyDetail;
            Object.values(buildings).forEach((building) => {
                if (building.otherFeatures) building.otherFeatures.type = 'ComplexFeature';
                if (building.floorConstruction) building.floorConstruction.type = 'ComplexFeature';
                if (building.foundation) building.foundation.type = 'ComplexFeature';
                if (building.wallConstruction) building.wallConstruction.type = 'ComplexFeature';
                if (building.roofConstruction) building.roofConstruction.type = 'ComplexFeature';
                if (building.insulation) building.insulation.type = 'ComplexFeature';
                if (building.glazing) building.glazing.type = 'StandardFeature';
                if (building.plumbing) building.plumbing.type = 'StandardFeature';
                if (building.wiring) building.wiring.type = 'StandardFeature';

                const { spaces } = building;
                if (spaces && spaces.length > 0) {
                    Object.values(spaces).forEach((space) => {
                        if (space.spaceType) {
                            switch (space.spaceType.code) {
                            case 'LI':
                                space.type = 'LivingSpace';
                                break;
                            case 'GA':
                                space.type = 'GarageSpace';
                                break;
                            default:
                                space.type = 'OtherSpace';
                            }
                        }

                        if (space.heating) space.heating.type = 'ComplexFeature';
                        if (space.kitchen) space.kitchen.type = 'Room';
                        if (space.mainBathroom) space.mainBathroom.type = 'Room';
                        if (space.ensuite) space.ensuite.type = 'Room';
                        if (space.garageFeatures) space.garageFeatures.type = 'ComplexFeature';
                        if (space.spaceFeatures) space.spaceFeatures.type = 'ComplexFeature';
                    });
                }
            });

            const { otherImprovements = {} } = propertyDetail;
            Object.values(otherImprovements).forEach((improvement) => {
                improvement.type = 'OtherImprovement';
            });

            const { natureOfImprovements = {} } = propertyDetail;
            Object.values(natureOfImprovements).forEach((noi) => {
                noi.type = 'ImprovementQuantity';
                noi.quantity = noi.quantity || 1;
            });

            return propertyDetail;
        },
    },
};

import { isUndefined } from 'util';
import axios from '../utils/AxiosHeaders';
import { createValidationSet } from '@quotable-value/validation';
import * as PropertyDetailController from '@/services/PropertyDetailController'

export default {
    namespaced: true,

    state: {
        propertyDetail: null,
        loading: null,
        saving: null,
        exception: null,
        validationSet: createValidationSet(),
        formIsStale: true,
        picklistValues: null,
        apportionmentDetails: null,
        farmedWithQpids: null,
        nutrientConsentQpids: null,
        irrigationLinkedQpids: null,
        irrigationWaterStorageQpids: null,
        alertError: null,
        qivsDvrData: null,
    },
    mutations: {
        setPropertyDetailObject(state, value) {
            state.formIsStale = true;
            state.propertyDetail = value;
        },
        setSinglePropertyDetail(state, { id, value, notesUpdate }) {
            const propertyDetail = { ...state.propertyDetail };
            if (notesUpdate) {
                propertyDetail.propertyNotes[id] = value;
            }
            if (!isUndefined(state.propertyDetail.landUse[id])) {
                propertyDetail.landUse[id] = value;
            } else if (!isUndefined(state.propertyDetail.audit[id])) {
                propertyDetail.audit[id] = value;
            } else if (!isUndefined(state.propertyDetail.summary[id])) {
                propertyDetail.summary[id] = value;
            } else if (!isUndefined(state.propertyDetail.site[id])) {
                propertyDetail.site[id] = value;
            } else if (state.propertyDetail.ruralDetail && !isUndefined(state.propertyDetail.ruralDetail[id])) {
                propertyDetail.ruralDetail[id] = value;
            } else {
                propertyDetail[id] = value;
            }

            state.propertyDetail = propertyDetail;
            state.formIsStale = true;
        },
        setLoading(state, value) {
            state.loading = value;
            if (value) {
                state.exception = null;
            }
        },
        setSaving(state, value) {
            state.saving = value;
            if (value) {
                state.exception = null;
            }
        },
        setException(state, value) {
            state.exception = value;
            if (value) {
                console.error(value); // eslint-disable-line no-console
            }
        },
        setValidationSet(state, value) {
            if (!Object.keys(value).includes('success') && !value.errors?.length && !value.warnings?.length) {
                value.success = true;
            }
            state.validationSet = value;
        },
        setFormIsStale(state, value) {
            state.formIsStale = value;
        },
        setPicklistValues(state, value) {
            state.picklistValues = value;
        },
        setApportionmentDetails(state, value) {
            state.apportionmentDetails = value;
        },
        setFarmedWithQpids(state, value) {
            state.farmedWithQpids = value;
        },
        setNutrientConsentQpids(state, value) {
            state.nutrientConsentQpids = value;
        },
        setAlertError(state, value) {
            state.alertError = value;
        },
        setIrrigationLinkedQpids(state, value) {
            state.irrigationLinkedQpids = value;
        },
        setIrrigationWaterStorageQpids(state, value) {
            state.irrigationWaterStorageQpids = value;
        },
        setQivsDvrData(state, value) {
            state.qivsDvrData = value;
        },
    },
    actions: {
        async getPropertyDetailByQpid({ commit }, qpid) {
            try {
                commit('setLoading', true);
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyDetailController
                        .getPendingPropertyDetail(qpid).url,
                });

                const propertyDetail = response ? response.data : null;
                if (!propertyDetail.audit) {
                    propertyDetail.audit = {
                        description: null,
                        summaryOfChanges: null,
                        reasonForChange: null,
                        outputCode: null,
                        source: null,
                    };
                }
                commit('setPropertyDetailObject', propertyDetail);
                commit('setFormIsStale', false);
                commit('setLoading', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setLoading', false);
                throw exception;
            }
        },
        async getReadOnlyCurrentPropertyDetail({ commit }, propertyId) {
            try {
                commit('setLoading', true);
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyDetailController
                    .getCurrentPropertyDetail(propertyId).url,
                });

                const propertyDetail = response ? response.data : null;
                if (!propertyDetail.audit) {
                    propertyDetail.audit = {
                        description: null,
                        summaryOfChanges: null,
                        reasonForChange: null,
                        outputCode: null,
                        source: null,
                    };
                }
                commit('setPropertyDetailObject', propertyDetail);
                commit('setFormIsStale', false);
                commit('setLoading', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setLoading', false);
                throw exception;
            }
        },
        async editCurrentPropertyDetail({ commit }, propertyId) {
            try {
                commit('setLoading', true);
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyDetailController
                        .editCurrentPropertyDetail(propertyId).url,
                });

                const propertyDetail = response ? response.data : null;
                if (!propertyDetail.audit) {
                    propertyDetail.audit = {
                        description: null,
                        summaryOfChanges: null,
                        reasonForChange: null,
                        outputCode: null,
                        source: null,
                    };
                }
                commit('setPropertyDetailObject', propertyDetail);
                commit('setFormIsStale', false);
                commit('setLoading', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setLoading', false);
                throw exception;
            }
        },
        async saveCurrentPropertyDetail({ commit, getters }, ignoreWarnings) {
            try {
                commit('setSaving', true);
                const response = await axios({
                    method: 'post',
                    data: getters.preparedPropertyDetail,
                    url: jsRoutes.controllers.PropertyDetailController
                        .saveCurrentPropertyDetail(ignoreWarnings).url,
                });

                const validationSet = createValidationSet(response.data);
                validationSet.success = response.data.success;
                commit('setValidationSet', validationSet);

                if (response.data.success === true) {
                    const newPropertyDetail = response.data.value;
                    commit('setPropertyDetailObject', newPropertyDetail);
                    commit('setFormIsStale', false);
                }

                commit('setSaving', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        },
        async getPropertyDetail({ commit }, propertyDetailId) {
            try {
                commit('setLoading', true);
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyDetailController
                        .getPropertyDetail(propertyDetailId).url,
                });

                const propertyDetail = response ? response.data : null;

                if(!propertyDetail)
                    throw Error('No property detail found or you do not have permission to see it.');

                if (!propertyDetail.audit) {
                    propertyDetail.audit = {
                        description: null,
                        summaryOfChanges: null,
                        reasonForChange: null,
                        outputCode: null,
                        source: null,
                    };
                }
                commit('setPropertyDetailObject', propertyDetail);
                commit('setFormIsStale', false);
                commit('setLoading', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setLoading', false);
                throw exception;
            }
        },

        async savePropertyDraft({ commit, getters }) {
            try {
                commit('setSaving', true);
                const response = await axios({
                    method: 'post',
                    data: getters.preparedPropertyDetail,
                    url: jsRoutes.controllers.PropertyDetailController
                        .savePendingPropertyDetailForRVJob().url,
                });

                commit('setValidationSet', createValidationSet(response.data));

                if (response.data.success === true) {
                    const newPropertyDetail = response.data.value;
                    commit('setPropertyDetailObject', newPropertyDetail);
                    commit('setFormIsStale', false);
                }

                commit('setSaving', false);
            } catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        },
        async validatePropertyDraft({ commit, state }, data) {
            try {
                commit('setException', null);

                const result = await PropertyDetailController.validateOnSave(state.propertyDetail, data.property, state.propertyDetail.dvrSnapshot);

                commit('setValidationSet', createValidationSet(result));
            } catch (exception) {
                commit('setException', exception);
                throw exception;
            }
        },
        async generatePropertyDetailNewDwellingInformation({ commit, getters }) {
            try {
                commit('setSaving', true);
                const response = await axios({
                    method: 'post',
                    data: getters.preparedPropertyDetail,
                    url: jsRoutes.controllers.PropertyDetailController
                        .generatePropertyDetailNewDwellingInformation().url,
                });
                commit('setValidationSet', createValidationSet(response.data));

                if (response.data.success === true) {
                    const newPropertyDetail = response.data.value;
                    commit('setPropertyDetailObject', newPropertyDetail);
                    commit('setFormIsStale', false);
                }
            } catch (exception) {
                commit('setException', exception);
                throw exception;
            } finally {
                commit('setSaving', false);
            }
        },

        async generateBuildingsFromPropertyDetail({ commit, getters }, { umrGarages, fsGarages }) {
            try {
                commit('setSaving', true);
                const response = await axios({
                    method: 'post',
                    data: getters.preparedPropertyDetail,
                    url: jsRoutes.controllers.PropertyDetailController
                        .generateBuildingsFromPropertyDetail(umrGarages, fsGarages).url,
                });
                commit('setValidationSet', createValidationSet(response.data));

                if (response.data.success === true) {
                    const newPropertyDetail = response.data.value;
                    commit('setPropertyDetailObject', newPropertyDetail);
                    commit('setFormIsStale', false);
                }
            } catch (exception) {
                commit('setException', exception);
                throw exception;
            } finally {
                commit('setSaving', false);
            }
        },
        async getPickListValues({ commit }) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyMasterData
                        .getPickListValues().url,
                });

                const picklistValues = response ? response.data : null;
                commit('setPicklistValues', picklistValues);
            } catch (exception) {
                commit('setException', exception);
                throw exception;
            }
        },
        async getApportionmentDetails({ commit }, qpid) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyMasterData.getApportionmentDetails(qpid).url,
                });

                const apportionmentDetails = response ? response.data : null;
                commit('setApportionmentDetails', apportionmentDetails);
            } catch (exception) {
                commit('setException', exception);
                throw exception;
            }
        },
        async getDetailsForQpids({ commit }, params) {
            try {
                let qpids = params.qpids;
                if (params.qpids.indexOf(',') > 0) {
                    qpids = params.qpids.split(',').filter(Number).toString();
                }
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyMasterData.getDetailsForQpids(qpids).url,
                });

                const qpidDetails = response ? response.data : null;
                if (params.id === 'farmedWith') {
                    commit('setFarmedWithQpids', qpidDetails);
                } else if (params.id === 'nutrientConsent') {
                    commit('setNutrientConsentQpids', qpidDetails);
                } else if (params.id === 'irrigationLinkedQpids') {
                    commit('setIrrigationLinkedQpids', qpidDetails);
                } else if (params.id === 'irrigationWaterStorageQpids') {
                    commit('setIrrigationWaterStorageQpids', qpidDetails);
                }
                let noOfQpidsPassed = 1;
                if (qpids.indexOf(',') > 0) {
                    noOfQpidsPassed = qpids.split(',').length;
                }
                if (noOfQpidsPassed !== qpidDetails.length) {
                    if (noOfQpidsPassed === 1 || qpidDetails.length === 0) {
                        commit('setAlertError', {
                            heading: 'Validation Error',
                            message: `QPID(s) ${qpids} is/are not valid or not found in the database.`,
                        });
                    } else {
                        commit('setAlertError', {
                            heading: 'Validation Error',
                            message: 'Some of the QPIDs are not valid or not found in the database. They are being excluded.',
                        });
                    }
                }
            } catch (exception) {
                commit('setException', exception);
                throw exception;
            }
        },
        async getDvrDataFromQivs({ commit }, qpid) {
            try {
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyMasterData.getDvrDataFromQivs(qpid).url,
                });
                const dvrData = response ? response.data : null;
                commit('setQivsDvrData', dvrData);
            } catch (exception) {
                commit('setException', exception);
                throw exception;
            }
        },
    },
    getters: {
        preparedPropertyDetail(state) {
            const propertyDetail = { ...state.propertyDetail };
            const { buildings = [] } = propertyDetail;
            // TODO Should this be in the controller?
            Object.values(buildings).forEach((building) => {
                if (building.otherFeatures) building.otherFeatures.type = 'ComplexFeature';
                if (building.floorConstruction) building.floorConstruction.type = 'ComplexFeature';
                if (building.foundation) building.foundation.type = 'ComplexFeature';
                if (building.wallConstruction) building.wallConstruction.type = 'ComplexFeature';
                if (building.roofConstruction) building.roofConstruction.type = 'ComplexFeature';
                if (building.insulation) building.insulation.type = 'ComplexFeature';
                if (building.glazing) building.glazing.type = 'StandardFeature';
                if (building.plumbing) building.plumbing.type = 'StandardFeature';
                if (building.wiring) building.wiring.type = 'StandardFeature';

                const { spaces = [] } = building;
                if (spaces && spaces.length > 0) {
                    Object.values(spaces).forEach((space) => {
                        if (space.spaceType) {
                            switch (space.spaceType.code) {
                            case 'LI':
                                space.type = 'LivingSpace';
                                break;
                            case 'GA':
                                space.type = 'GarageSpace';
                                break;
                            default:
                                space.type = 'OtherSpace';
                            }
                        }

                        if (space.heating) space.heating.type = 'ComplexFeature';
                        if (space.kitchen) space.kitchen.type = 'Room';
                        if (space.mainBathroom) space.mainBathroom.type = 'Room';
                        if (space.ensuite) space.ensuite.type = 'Room';
                        if (space.garageFeatures) space.garageFeatures.type = 'ComplexFeature';
                        if (space.spaceFeatures) space.spaceFeatures.type = 'ComplexFeature';
                    });
                }
            });

            const { otherImprovements = [] } = propertyDetail;
            Object.values(otherImprovements).forEach((improvement) => {
                improvement.type = 'OtherImprovement';
            });

            const { natureOfImprovements = [] } = propertyDetail;
            Object.values(natureOfImprovements).forEach((noi) => {
                noi.type = 'ImprovementQuantity';
                noi.quantity = noi.quantity || 1;
            });

            return propertyDetail;
        },
    },
};

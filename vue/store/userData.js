import axios from '../utils/AxiosHeaders';

const userData = {
    namespaced: true,

    state: {
        loaded: false,
        internalLoaded: false,
        userName: null,
        userId: null,
        userFullName: null,
        isExternalUser: null,
        isInternalUser: null,
        isAdminUser: false,
        isRtvUser: false,
        isQVMapUser: false,
        isReadOnlyUser: null,
        isRegisteredValuer: false,
        isValuer: false,
        isNonValuer: false,
        isReportingManager: false,
        isReportingRevalUser: false,
        isCustomerCare: false,
        numOfRoles: 0,
        userTACode: null,
        allowedTACodes: [],
        allowedRCCodes: [],
        userRoles: [],
        monarchRoles: [],
        qivsUrl: null,
        userData: null, // to deprecate.
        geoserverUrl: null,
        googleMapApiKey: null,
        linzMapApiKey: null,
        userGroupType: null,
        isTAUser: null,
        taGroupName: null,
        externalObjectionAccess: null,
    },

    getters: {
        userHasRole: state => theRole => state.userRoles.find(role => role.name === theRole),
        userHasMonarchRole: (state) => {
            return (theRoles) => {
                return theRoles.some(r => state.monarchRoles.includes(r))
            }
        },
        // TODO - This host should not be hardcoded here or elsewhere, should be in
        // application.conf overridden by kubernetes podpresets where necessary
        qvmsMapUrl: () => qpid => `http://qvartgis01:8080/QVMS/?qpid=${qpid}`,
        qivsFloorPlanUrl: state => qpid => `${state.qivsUrl}/default.asp?Plans/FloorPlans/FloorPlans.aspx?Qpid=${qpid}`,
        qivsMasterDetailsUrl: state => qpid => `${state.qivsUrl}/default.asp?Property/masterdetails.aspx?Qpid=${qpid}`,
        qivsSalesUrl: state => qpid => `${state.qivsUrl}/default.asp?Property/Sales/Sales.asp?${qpid}`,
        qivsImprovementSummaryUrl: state => qpid => `${state.qivsUrl}/default.asp?Property/PermanentDataWS/PDWorksheet.aspx?Qpid=${qpid}`,
        qivsWorksheetUrl: state => qpid => `${state.qivsUrl}/default.asp?Property/Revisions/RuralWorksheet.aspx?Qpid=${qpid}`,
        qivsRelinkUrl: state => qpid => `${state.qivsUrl}/default.asp?property/Relink.asp?QPID=${qpid}`,
    },

    mutations: {
        setIsExternalUser(state, value) {
            state.isExternalUser = value;
        },
        setIsInternalUser(state, value) {
            state.isInternalUser = value;
        },
        setIsReadOnlyUser(state, value) {
            state.isReadOnlyUser = value;
        },
        setIsAdminUser(state, value) {
            state.isAdminUser = value;
        },
        setIsRtvUser(state, value) {
            state.isRtvUser = value;
        },
        setIsQVMapUser(state, value) {
            state.isQVMapUser = value;
        },
        setIsRegisteredValuer(state, value) {
            state.isRegisteredValuer = value;
        },
        setIsValuer(state, value) {
            state.isValuer = value;
        },
        setIsNonValuer(state, value) {
            state.isNonValuer = value;
        },
        setIsReportingManager(state, value) {
            state.isReportingManager = value;
        },
        setIsReportingReval(state, value) {
            state.isReportingRevalUser = value;
        },
        setIsCustomerCare(state, value) {
            state.isCustomerCare = value;
        },
        setNumOfRoles(state, value) {
            state.numOfRoles = value;
        },
        setUserDetails(state, value) {
            state.userData = value;
            state.userName = value.name;
            state.userId = value.userId;
            state.userFullName = value.userFullName;
            state.userTACode = value.userTACode;
            state.qivsUrl = value.qivsURL;
            state.userRoles = value.roles || [];
            state.allowedTACodes = value.allowedTA || [];
            state.allowedRCCodes = value.allowedRC || [];
            state.geoserverUrl = value.geoserverUrl;
            state.googleMapApiKey = value.googleMapApiKey;
            state.linzMapApiKey = value.linzMapApiKey;
            state.userGroupType = value.userGroupType;
            state.isTAUser = value.userGroupType === 'TA';
            state.taGroupName = value.taGroupName || null;
            state.externalObjectionAccess = value.taGroupName === 'Auckland Council';
        },
        setLoaded(state, value) {
            state.loaded = value;
        },
        setMonarchRoles(state, value) {
            state.monarchRoles = value;
        },
        setInternalLoaded(state, value) {
            state.internalLoaded = value;
        }
    },

    actions: {
        async fetchUserData({
            commit, dispatch, getters, state,
        }) {
            const { userHasRole } = getters;
            try {
                const response = await axios({
                    method: 'get',
                    url: (jsRoutes.controllers.Application.fetchUserData()).url,
                });
                const { data } = response;

                commit('setUserDetails', data);
                const isQvMapUser = state.userData.userGroupType !== 'NA' || (state.userData.userGroupType === 'NA' && state.userData.taGroupName === 'OVG' || state.userData.userGroupType === 'NA' && state.userData.taGroupName === 'Auckland Council');
                if (state.userRoles) {
                    if (userHasRole('INTERNAL_USER')) {
                        commit('setIsInternalUser', true);
                        commit('setIsExternalUser', false);
                        commit('setIsReadOnlyUser', false);
                        commit('setIsQVMapUser', true);
                        commit('setLoaded', true);
                        dispatch('fetchInternalUserData');
                        return;
                    }

                    if (userHasRole('EXTERNAL_USER_RW')) {
                        commit('setIsInternalUser', false);
                        commit('setIsExternalUser', true);
                        commit('setIsReadOnlyUser', false);
                        commit('setLoaded', true);
                        commit('setIsQVMapUser', isQvMapUser);
                        return;
                    }

                    if (userHasRole('EXTERNAL_USER_READ')) {
                        commit('setIsInternalUser', false);
                        commit('setIsExternalUser', true);
                        commit('setIsReadOnlyUser', true);
                        commit('setLoaded', true);
                        commit('setIsQVMapUser', isQvMapUser);
                        return;
                    }
                } else {
                    /* Dont see how this should ever happen. */
                    commit('setIsInternalUser', false);
                    commit('setIsExternalUser', true);
                    commit('setIsReadOnlyUser', false);
                    commit('setIsQVMapUser', false);
                    commit('setLoaded', true);
                    return;
                }
            } catch (error) {
                console.error(error); // eslint-disable-line no-console
            }
            throw new Error("Found a user that we couldn't figure out what role they had.");
        },
        async fetchInternalUserData({ commit, state }) {
            let response;

            if (!state.userName) {
                throw new Error('Cannot fetch internal user data without username');
            }

            const theUserData = {
                ...state.userData,
                isInternalUser: state.isInternalUser,
            };

            const { url } = jsRoutes.controllers.ReferenceData.displayUserByUsername(`QVNZ\\${state.userName}`);

            try {
                response = await axios({
                    method: 'get',
                    url,
                });
                const { data } = response;

                const isAdmin = data.roles && data.roles.includes('Receptionist Typist');
                commit('setIsAdminUser', isAdmin);

                const isRtv = data.roles && data.roles.includes('RTV Valuer');
                commit('setIsRtvUser', isRtv);

                const isRegisteredValuer = data.roles && data.roles.includes('Registered Valuer');
                commit('setIsRegisteredValuer', isRegisteredValuer);

                const isValuer = data.roles && data.roles.includes('Valuer');
                commit('setIsValuer', isValuer);

                const isReportingManager = data.roles && data.roles.includes('Reporting - Manager');
                commit('setIsReportingManager', isReportingManager);

                const isReportingReval = data.roles && data.roles.includes('Reporting - Reval');
                commit('setIsReportingReval', isReportingReval);

                const isCustomerCare = data.roles && data.roles.includes('Customer Care');
                commit('setIsCustomerCare', isCustomerCare);

                const isNonValuer = isAdmin || isReportingManager || isReportingReval || isCustomerCare;
                commit('setIsNonValuer', isNonValuer);

                commit('setMonarchRoles', data.roles);
                // DEPRECATED

                commit('setNumOfRoles', data.roles.length);

                theUserData.monarchUser = data;
                theUserData.isAdmin = isAdmin;
                commit('definition', theUserData, { root: true });
                commit('setInternalLoaded', true);
            } catch (error) {
                console.error(error); // eslint-disable-line no-console
                commit('definition', theUserData, { root: true });
                commit('setInternalLoaded', true);
            }
        },
    },
};

export default userData;

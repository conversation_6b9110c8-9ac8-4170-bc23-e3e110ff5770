import axios from '../utils/AxiosHeaders';
import Vue from 'vue';
import moment from 'moment';

const propertyPhotos = {
    namespaced: true,

    state: {
        noPhotoUrl: '/assets/images/property/addPhotos.png',
        photoUrlMap: {},
    },

    mutations: {
        addPhoto(state, value) {
            Vue.set(state.photoUrlMap, value.propertyId, {
                smallImageUrl: value.smallImageUrl,
                expiry: value.expiry,
            });
        },
    },
    actions: {
        async getPropertyPhoto({ commit, state }, propertyId) {
            if (!propertyId) {
                return;
            }
            try {
                // Only need to retrieve the photo if it's not already in the store or has expired
                if (!state.photoUrlMap[propertyId] || state.photoUrlMap[propertyId].expiry.isBefore()) {
                    // Add a placeholder image to the store before retrieving
                    const photoData = {
                        propertyId,
                        smallImageUrl: state.noPhotoUrl,
                        expiry: moment().add(59, 'minutes'),
                    };
                    commit('addPhoto', photoData);

                    // Retrieve the primary photo from the server and update the store
                    const response = await axios({
                        method: 'get',
                        url: jsRoutes.controllers.MediaController.getPrimaryMediaByOwner(propertyId, 'Property').url,
                    });
                    photoData.smallImageUrl = response.data.mediaItem.smallImageUrl;
                    commit('addPhoto', photoData);
                }
            }
            catch (error) {
                // Ignore 404 errors, just means there is no photo for the property
                if (error.response.status != 404) {
                    console.error('Could not retrieve primary photo for Property ' + propertyId + '. ' + error);
                }
            }
        },
    },
};

export default propertyPhotos;

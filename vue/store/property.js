import axios from '../utils/AxiosHeaders';
import * as referenceDataController from '@/services/ReferenceDataController';

const property = {
    namespaced: true,

    state: {
        property: null,
        propertyPhotos: null,
        properties: null,
        nextPropertyId: null,
        previousPropertyId: null,
        loading: false,
        saving: false,
        exception: null,
        errors: null,
        taSummary: null,
        proposedZoningOptions: [],
    },

    mutations: {
        setTaSummary(state, data) {
            state.taSummary = data;
        },
        setProposedZoningOptions(state, data) {
            state.proposedZoningOptions = data;
        },
        setProperty(state, value) {
            state.property = value;
        },
        setPropertyPhotos(state, value) {
            state.propertyPhotos = value;
        },
        setProperties(state, value) {
            state.properties = value;
        },
        setPreviousPropertyId(state, value) {
            state.previousPropertyId = value;
        },
        setNextPropertyId(state, value) {
            state.nextPropertyId = value;
        },
        // TODO Could we use a mixin for standard stuff?
        setLoading(state, value) {
            state.loading = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setErrors(state, value) {
            console.error(value);
            state.errors = value;
        },
        setException(state, value) {
            console.error(value);
            state.exception = value;
        },
    },
    getters: {
        isMaoriLand: (state) => {
            return state.property?.landUseData?.isMaoriLand;
        }
    },
    actions: {
        async getProperty({commit}, propertyId) {
            console.log('getProperty ' + propertyId);

            let response;
            try {
                commit('setLoading', true);

                response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyMasterData.getProperty(propertyId).url,
                });

                const propertyInfo = response.data;
                const proposedZoningOptions = await referenceDataController.getZoningOptions({
                    parentCode: propertyInfo.property.territorialAuthority.code,
                    groupCode: 'TAPZ',
                    sort: 'SHORT_DESCRIPTION',
                    order: 'ASC',
                });
                commit('setProposedZoningOptions', proposedZoningOptions);
                commit('setProperty', propertyInfo.property);
                commit('setPropertyPhotos', propertyInfo.propertyPhotos);
                commit('setProperties', propertyInfo.propertiesList);
                commit('setPreviousPropertyId', propertyInfo.previousProperty);
                commit('setNextPropertyId', propertyInfo.nextProperty);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting property.');

                commit('setProperty', null);
                commit('setPropertyPhotos', null);
                commit('setProperties', null);
                commit('setPreviousPropertyId', null);
                commit('setNextPropertyId', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },
    },
};

export default property;

import axios from '../utils/AxiosHeaders';
import { cloneDeep, isEqual } from 'lodash';
import { updateRuralSaleAnalysis } from '@/services/RuralSaleAnalysisController';

const roundNearest100 = (num) => Math.round(num / 100) * 100;
const roundNearest50 = (num) => Math.round(num / 50) * 50;
const adjustLandRow = (row, adjustment) => {
    if (row.value) {
        row.value = roundNearest100(row.value * adjustment);
        row.rate = roundNearest50(row.value / row.size);
    }
};
const adjustSiteRow = (row, adjustment) => {
    if (row.value) {
        row.value = roundNearest100(row.value * adjustment);
    }
};
const calculateTotalLV = (analysis) => {
    const landValue = analysis.land.reduce((acc, o) => {
        return acc + o.value;
    }, 0);
    const siteValue = analysis.sites.reduce((acc, o) => {
        return acc + o.value;
    }, 0);

    return siteValue + landValue;
}

const populateMultiSaleDefaultRows = (state, analysis) => {
    for (const [valRef, worksheet] of Object.entries(analysis.land)) {
        populateDefaultRows(state, worksheet);
    }
}

const populateDefaultRows = (state, analysis) => {
    if (analysis.sites.length === 0) {
        analysis.sites.push({
            saleAnalysisRowId: null,
            qpid: state.property.qpid,
            valRef: state.property.valRef,
            viewId: null,
            size: null,
            value: null,
        })
    }

    if (analysis.land.length === 0) {
        analysis.land.push({
            saleAnalysisRowId: null,
            qpid: state.property.qpid,
            valRef: state.property.valRef,
            contourId: null,
            irrigationId: null,
            ruralUseId: null,
            size: null,
            description: null,
            rate: null,
            value: null,
        })
    }

    analysis.totalLV = calculateTotalLV(analysis);
}

export default {
    namespaced: true,
    state: {
        sale: {},
        property: {},
        analysis: {},
        originalAnalysis: {},
        landMatrix: [],
        loading: true,
        error: null,
        saving: false,
        saveErrors: null,
        picklistValues: null,
        ruralClassifications: {},
        fetchingRuralClassifications: false,
        pdfFileStream: {},
    },
    getters: {
        getTotalWorksheetVI(state) {
            return state.analysis.improvements.reduce((acc, o) => {
                return acc + o.value;
            }, 0);
        },
        getTotalWithoutWorksheetVI(state) {
            return state.analysis.improvementsWithoutWorksheets.reduce((acc, o) => {
                return acc + o.value;
            }, 0);
        },
        analysisHasChanged(state, getters) {
            const fieldsToCheck = [
                'qvCategory',
                'grouping',
                'nutrientQuality',
                'ratingQuality',
                'waterQuality',
                'analysisComment',
                'analysedVI',
                'analysedLV',
                'totalLandArea',
                'totalLV',
                'totalVI',
            ]

            for (const field of fieldsToCheck) {
                const changed = !isEqual(state.originalAnalysis[field], state.analysis[field]);
                if (changed) {
                    return true;
                }
            }

            const improvementsChanged = getters.improvementRowsChanged;
            const landChanged = getters.landRowsChanged;
            const siteChanged = getters.siteRowsChanged;
            const deletedRows = state.analysis.deletedRows.length > 0;

            return improvementsChanged || landChanged || siteChanged || deletedRows;
        },
        improvementRowsChanged(state) {
            const originalImprovements = state.originalAnalysis.improvements;
            const currentImprovements = state.analysis.improvements.filter(imp => imp.size || imp.rate || imp.value || imp.improvementId);

            if (currentImprovements.find(imp => imp.saleAnalysisRowId == null)) {
                return true;
            }

            return originalImprovements.length !== currentImprovements.length || originalImprovements.some((original) => {
                const current = currentImprovements.find((imp) => imp.saleAnalysisRowId === original.saleAnalysisRowId);
                return !current || !isEqual(original, current);
            });
        },
        landRowsChanged(state) {
            const originalLand = state.originalAnalysis.land;

            if (typeof(originalLand) === 'object') {
                return !isEqual(originalLand, state.analysis.land);
            }

            const currentLand = state.analysis.land.filter(land => land.size || land.description || land.contourId || land.ruralUseId || land.irrigationId || land.rate || land.value);
            if (currentLand.find(imp => imp.saleAnalysisRowId == null)) {
                return true;
            }

            return originalLand.length !== currentLand.length || originalLand.some((original) => {
                const current = currentLand.find((imp) => imp.saleAnalysisRowId === original.saleAnalysisRowId);
                return !current || !isEqual(original, current);
            });
        },
        siteRowsChanged(state) {
            const originalSites = state.originalAnalysis.sites;
            const currentSites = state.analysis.sites;

            return originalSites.length !== currentSites.length || currentSites.some((current) => {
                const original = originalSites.find((imp) => imp.saleAnalysisRowId === current.saleAnalysisRowId);
                return !original || !isEqual(current, original);
            });
        }
    },
    mutations: {
        adjustLandRows(state, adjustment) {
            if (state.analysis.isMulti) {
                Object.values(state.analysis.land).forEach((worksheet) => {
                    worksheet.land.forEach(row => adjustLandRow(row, adjustment));
                    worksheet.sites.forEach(row => adjustSiteRow(row, adjustment));
                });

                state.analysis.landWithoutWorksheet.forEach((row) => {
                    row.value = row.value * adjustment;
                    row.rate = (row.value * adjustment) / row.area;
                });
            } else {
                state.analysis.land.forEach(row => adjustLandRow(row, adjustment));
                state.analysis.sites.forEach(row => adjustSiteRow(row, adjustment));
            }
        },
        setSale(state, value) {
            state.sale = value;
        },
        setProperty(state, value) {
            state.property = value;
        },
        setAnalysis(state, value) {
            if (value.isMulti) {
                populateMultiSaleDefaultRows(state, value);
            }
            else {
                populateDefaultRows(state, value);
            }

            state.originalAnalysis = cloneDeep(value);
            state.analysis = value;
        },
        setAnalysedLV(state, value) {
            state.analysis.analysedLV = value;
        },
        setTotalVI(state, value) {
            state.analysis.totalVI = value;
        },
        setLandMatrix(state, value) {
            state.landMatrix = value;
        },
        setLoading(state, value) {
            state.loading = value;
        },
        setError(state, value) {
            state.error = value;
        },
        setSaveErrors(state, value) {
            state.saveErrors = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setPicklistValues(state, value) {
            state.picklistValues = value;
        },
        setRuralClassifications(state, value) {
            state.ruralClassifications = value;
        },
        setPdfFileStream(state, value) {
            state.pdfFileStream = value;
        },
    },
    actions: {
        calculateAnalysedLV({state, commit, getters}, forceRecalculate = false) {
            const {analysis, sale} = state;
            const oldTotalVI = analysis.totalVI;
            let totalVI, analysedLV;

            totalVI = getters.getTotalWorksheetVI;
            if (analysis.isMulti) {
                totalVI += getters.getTotalWithoutWorksheetVI;
            }

            if (oldTotalVI !== totalVI || forceRecalculate) {
                analysedLV = sale.price.net - (totalVI || 0);
                commit('setAnalysedLV', analysedLV);
                commit('setTotalVI', totalVI);
            }
        },
        adjustRows({state, commit}) {
            const {analysis, property} = state;
            const adjustment = analysis.analysedLV / property.ratingValuation.landValue;
            commit('adjustLandRows', adjustment);
        },
        deleteRow({state, dispatch}, row) {
            if (!row) {
                return;
            }

            row.delete = true;
            state.analysis.deletedRows.push(row);
            dispatch('calculateAnalysedLV');
        },
        async deleteAnalysis({commit}, saleId) {
            const response = await axios({
                method: 'get',
                url: jsRoutes.controllers.RuralSaleAnalysisController.deleteRuralSaleAnalysis(
                    saleId).url,
            });
            return response ? response.data : false;
        },
        async loadSaleAnalysis({commit, dispatch}, saleId) {
            try {
                commit('setError', null);
                commit('setLoading', true);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralSaleAnalysisController.displayRuralSaleAnalysis(
                        saleId).url,
                });

                const {primaryProperty, sale, classifications, ...analysis} = response
                    ? response.data
                    : null;

                if (!analysis) {
                    throw Error('Failed to retrieve sale analysis');
                }

                if (!analysis.isMulti) {
                    const landMatrixResponse = await axios({
                        method: 'get',
                        url: jsRoutes.controllers.RuralWorksheetController.getWorksheetLandMatrix(
                            analysis.qpid, parseInt(analysis.analysedLV)).url,
                    }).catch((err) => {
                        console.error('Failed to retrieve land matrix', err);
                        return null;
                    });

                    commit('setLandMatrix', landMatrixResponse ? landMatrixResponse.data : []);
                }
                commit('setSale', sale);
                commit('setProperty', primaryProperty);
                commit('setRuralClassifications', classifications);
                commit('setAnalysis', analysis);
                dispatch('calculateAnalysedLV', true);
                commit('setLoading', false);
                commit('setError', null);
            } catch (error) {
                console.error(error);
                commit('setError', 'Failed to retrieve sale analysis');
                commit('setLoading', false);

                throw error;
            }
        },
        async updateSaleAnalysis({commit, state}, validateOnly = false) {
            try {
                commit('setSaveErrors', null);
                commit('setSaving', true);

                const response = await updateRuralSaleAnalysis({...state.analysis, sale: state.sale}, validateOnly);

                if (!response.ok) {
                    throw new Error('Unexpected response from server');
                }

                const analysis = await response.json();
                commit('setAnalysis', analysis);
                commit('setSaveErrors', analysis.errors);
                commit('setSaving', false);
            } catch (error) {
                console.error('Failed to save sale analysis', error);
                commit('setSaveErrors', [error.message]);
                commit('setSaving', false);
            }
        },
        async refreshSaleAnalysis({commit, state, dispatch}, saleId) {
            try {
                commit('setError', null);
                commit('setLoading', true);

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralSaleAnalysisController.refreshRuralSaleAnalysis(
                        saleId).url,
                });

                const analysis = response ? response.data : null;

                let landRows = state.analysis.land;
                let siteRows = state.analysis.sites;
                if (analysis.isMulti) {
                    landRows = Object.keys(state.analysis.land).reduce((acc, valRef) => {
                        return acc.concat(state.analysis.land[valRef].land,
                            state.analysis.land[valRef].sites);
                    }, []);

                    siteRows = [];
                }

                state.analysis.deletedRows = state.analysis.deletedRows.concat(
                    state.analysis.improvements, landRows, siteRows);
                state.analysis.improvements = analysis.improvements;
                state.analysis.land = analysis.land;
                state.analysis.sites = analysis.sites;
                state.analysis.qvCategory = analysis.qvCategory;
                state.analysis.grouping = analysis.grouping;
                state.analysis.nutrientQuality = analysis.nutrientQuality;
                state.analysis.ratingQuality = analysis.ratingQuality;
                state.analysis.waterQuality = analysis.waterQuality;
                state.analysis.worksheetComment = analysis.worksheetComment;

                dispatch('calculateAnalysedLV', true);
                dispatch('adjustRows');
                commit('setLoading', false);
                commit('setError', null);
            } catch (error) {
                commit('setError', 'Failed to refresh sale analysis');
                commit('setLoading', false);

                throw error;
            }
        },
        async loadPickListValues({commit, state}) {
            try {
                if (state.picklistValues != null) {
                    return;
                }

                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.PropertyMasterData.getPickListValues().url,
                });

                const picklistValues = response ? response.data : null;
                picklistValues.qvCategoryTypes = picklistValues.qvCategoryTypes.map(
                    o => ({id: o.qvCategoryId, code: o.code, description: o.description}));
                commit('setPicklistValues', picklistValues);
                picklistValues.qualityRatingTypes = picklistValues.qualityRatingTypes.map(o => ({
                    id: parseInt(o.code),
                    code: o.code,
                    description: o.description,
                }));
                picklistValues.propertyGroupingTypes = picklistValues.propertyGroupingTypes.map(
                    o => ({
                        id: o.groupingId,
                        code: o.code,
                        description: o.description,
                    }));
            } catch (error) {
                console.error(error);
                commit('setError', 'Failed to load picklist values');

                throw error;
            }
        },
        async generateRuralSalesPdfReport({commit}, saleId) {
            try {
                commit('setLoading', null);
                const response = await axios({
                    method: 'get',
                    url: jsRoutes.controllers.RuralSaleAnalysisController.generateRuralSalesPdf(
                        saleId).url,
                });
                commit('setLoading', false);
                commit('setPdfFileStream', response.data);
            } catch (error) {
                commit('setLoading', false);
                commit('setError', error);
                throw error;
            }
        },
    },
};

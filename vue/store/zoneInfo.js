const zoneInfo = {
    namespaced: true,

    state: {
        zoneInfo: null,
        loading: false,
        saving: false,
        exception: null,
        errors: null,

    },

    mutations: {
        setZoneInfo(state, value) {
            state.zoneInfo = value;
        },
        // TODO Could we use a mixin for standard stuff?
        setLoading(state, value) {
            state.loading = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setErrors(state, value) {
            console.error(value);
            state.errors = value;
        },
        setException(state, value) {
            console.error(value);
            state.exception = value;
        },
    },
    actions: {
        async getZoneInfo({commit}, qpid) {
            console.log('getZoneInfo ' + qpid);

            try {
                commit('setLoading', true);

                const response = await fetch(jsRoutes.controllers.PropertyMasterData.getQvPropertyZoneInfo(qpid).url);
                const result = await response.json();

                commit('setZoneInfo', result);
                commit('setLoading', false);

            } catch (ex) {
                /* Error so clear out anything we have as it will no longer be correct */
                console.error('[Monarch error]: Exception getting zoneInfo.');

                commit('setZoneInfo', null);

                commit('setException', ex);
                commit('setLoading', false);
            }
        },
        async saveZoneInfo({commit, getters}){
            try {
                commit('setSaving', true);

                const response = await fetch(jsRoutes.controllers.PropertyMasterData.saveQvPropertyZoneInfo().url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: JSON.stringify(getters.preparedZoneInfo),
                });
                if (!response.ok) throw new Error('Failed to save Zone Info');
                
                const result = await response.json();
                commit('setZoneInfo', result);
                commit('setSaving', false);
            }
            catch (exception) {
                commit('setException', exception);
                commit('setSaving', false);
                throw exception;
            }
        }
    },
    getters: {
        preparedZoneInfo(state) {
            return state.zoneInfo;
        }
    }
};

export default zoneInfo;

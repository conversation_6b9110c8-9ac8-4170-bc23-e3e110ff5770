const ruralRtv = {
    namespaced: true,

    state: {
        currentTab: 'MainIndex',
        territorialAuthority: 0,
        mainIndex: [],
        secondaryRefinements: [],
        assessments: [],
        baseCategories: [],
        rolls: [],
        saleGroups: [],
        groupings: [],
        qualityRatings: [],
        rtvValues: null,
        rtvIndices: null,
        loading: false,
        saving: false,
        exception: null,
        errors: null,
        warnings: null,
    },

    mutations: {
        setCurrentTab(state, value) {
            state.currentTab = value;
        },
        setTerritorialAuthority(state, value) {
            state.territorialAuthority = value;
        },
        setMainIndex(state, value) {
            state.mainIndex = value;
        },
        setSecondaryRefinements(state, value) {
            state.secondaryRefinements = value;
        },
        setAssessments(state, value) {
            state.assessments = value;
        },
        setBaseCategories(state, value) {
            state.baseCategories = value;
        },
        setRollsList(state, value) {
            state.rolls = value;
        },
        setSaleGroupsList(state, value) {
            state.saleGroups = value;
        },
        setGroupingList(state, value) {
            state.groupings = value;
        },
        setQualityRatingList(state, value) {
            state.qualityRatings = value;
        },
        setRtvValues(state, value) {
            state.rtvValues = value;
        },
        setRtvIndices(state, value) {
            state.rtvIndices = value;
        },
        setLoading(state, value) {
            state.loading = value;
        },
        setSaving(state, value) {
            state.saving = value;
        },
        setErrors(state, value) {
            state.errors = value;
        },
        setWarnings(state, value) {
            state.warnings = value;
        },
        setException(state, value) {
            console.error(value);
            state.exception = value;
        },
    },

    actions: {
        async clearStore({ commit }) {
            commit('setLoading', true);

            commit('setMainIndex', []);
            commit('setRollsList', []);
            commit('setSaleGroupsList', []);
            commit('setSecondaryRefinements', []);
            commit('setAssessments', []);

            commit('setLoading', false);
        },
        async getRuralIndex({ commit }, taCode) {
            try {
                commit('setLoading', true);

                const mainIndexResponse = await fetch(jsRoutes.controllers.RtvController.getRuralIndexMainIndex(taCode).url);
                const mainIndexData = await mainIndexResponse.json();
                if (!mainIndexResponse.ok) {
                    throw new Error(mainIndexData);
                }
                commit('setMainIndex', mainIndexData || []);

                const rollList = [];
                mainIndexData.forEach((item) => {
                    const i = rollList.findIndex(x => x.id === item.roll_id);
                    if (i < 0) {
                        rollList.push({
                            id: item.roll_id,
                            code: item.roll_number,
                        });
                    }
                });
                commit('setRollsList', rollList || []);

                const saleGroupList = [];
                mainIndexData.forEach((item) => {
                    const i = saleGroupList.findIndex(x => x.id === item.sale_group.id);
                    if (i < 0) {
                        saleGroupList.push(item.sale_group);
                    }
                });
                commit('setSaleGroupsList', saleGroupList || []);

                const secondaryRefinementsResponse = await fetch(jsRoutes.controllers.RtvController.getRuralIndexSecondaryRefinements(taCode).url);
                const secondaryRefinementsData = await secondaryRefinementsResponse.json();
                if (!secondaryRefinementsResponse.ok) {
                    throw new Error(secondaryRefinementsData);
                }
                commit('setSecondaryRefinements', secondaryRefinementsData || []);
            } catch (error) {
                console.error(`[Monarch error]: Error loading Rural RTV Index for TA:${taCode}`);
                commit('setException', error);
                commit('setMainIndex', []);
                commit('setSecondaryRefinements', []);
                throw error;
            } finally {
                commit('setLoading', false);
            }
        },
        async getMainIndex({ commit }, taCode) {
            try {
                commit('setLoading', true);

                const mainIndexResponse = await fetch(jsRoutes.controllers.RtvController.getRuralIndexMainIndex(taCode).url);
                const mainIndexData = await mainIndexResponse.json();
                if (!mainIndexResponse.ok) {
                    throw new Error(mainIndexData);
                }
                commit('setMainIndex', mainIndexData || []);

                const rollList = [];
                mainIndexData.forEach((item) => {
                    const i = rollList.findIndex(x => x.id === item.roll_id);
                    if (i < 0) {
                        rollList.push({
                            id: item.roll_id,
                            code: item.roll_number,
                        });
                    }
                });
                commit('setRollsList', rollList || []);

                const saleGroupList = [];
                mainIndexData.forEach((item) => {
                    const i = saleGroupList.findIndex(x => x.id === item.sale_group.id);
                    if (i < 0) {
                        saleGroupList.push(item.sale_group);
                    }
                });
                commit('setSaleGroupsList', saleGroupList || []);
            } catch (error) {
                console.error(`[Monarch error]: Error loading Rural RTV Main Index for TA:${taCode}`);
                commit('setException', error);
                commit('setMainIndex', []);
                commit('setSecondaryRefinements', []);
                throw error;
            } finally {
                commit('setLoading', false);
            }
        },
        async validateMainIndex({ commit }, { taCode, mainIndex }) {
            try {
                commit('setSaving', true);

                const response = await fetch(jsRoutes.controllers.RtvController.validateRuralIndexMainIndex(taCode).url, {
                    method: 'post',
                    headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                    body: JSON.stringify(mainIndex),
                });
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setErrors', data.errors);
                commit('setWarnings', data.warnings);
            } catch (error) {
                console.error('Error validating main index:', error);
                throw error;
            } finally {
                commit('setSaving', false);
            }
        },
        async saveMainIndex({ commit }, {
            taCode, userName, ignoreWarnings, mainIndex,
        }) {
            try {
                commit('setSaving', true);

                const response = await fetch(jsRoutes.controllers.RtvController.updateRuralIndexMainIndex(taCode, userName, ignoreWarnings).url, {
                    method: 'post',
                    headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                    body: JSON.stringify(mainIndex),
                });
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setMainIndex', data.mainIndex || []);
                commit('setErrors', data.errors);
                commit('setWarnings', data.warnings);
            } catch (error) {
                console.error('Error saving main index:', error);
                commit('setMainIndex', []);
                throw error;
            } finally {
                commit('setSaving', false);
            }
        },
        async getSecondaryRefinements({ commit }, taCode) {
            try {
                commit('setLoading', true);

                const response = await fetch(jsRoutes.controllers.RtvController.getRuralIndexSecondaryRefinements(taCode).url);
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setSecondaryRefinements', data || []);
            } catch (error) {
                console.error(`[Monarch error]: Error loading Rural RTV Secondary Refinements for TA: ${taCode}`);
                commit('setException', error);
                commit('setSecondaryRefinements', []);
                throw error;
            } finally {
                commit('setLoading', false);
            }
        },
        async validateSecondaryRefinements({ commit }, { taCode, secondaryRefinements }) {
            try {
                commit('setSaving', true);

                const response = await fetch(jsRoutes.controllers.RtvController.validateRuralIndexSecondaryRefinements(taCode).url, {
                    method: 'post',
                    headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                    body: JSON.stringify(secondaryRefinements),
                });
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setErrors', data.errors);
                commit('setWarnings', data.warnings);
            } catch (error) {
                console.error('Error validating secondary refinements:', error);
                throw error;
            } finally {
                commit('setSaving', false);
            }
        },
        async saveSecondaryRefinements({ commit }, {
            taCode, userName, ignoreWarnings, secondaryRefinements,
        }) {
            try {
                commit('setSaving', true);

                const response = await fetch(jsRoutes.controllers.RtvController.updateRuralIndexSecondaryRefinements(taCode, userName, ignoreWarnings).url, {
                    method: 'post',
                    headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
                    body: JSON.stringify(secondaryRefinements),
                });
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setSecondaryRefinements', data.secondaryRefinements || []);
                commit('setErrors', data.errors);
                commit('setWarnings', data.warnings);
            } catch (error) {
                console.error('Error saving secondary refinements:', error);
                commit('setSecondaryRefinements', []);
                throw error;
            } finally {
                commit('setSaving', false);
            }
        },
        async getRuralPropertyRtvValues({ commit }, qpid) {
            try {
                const response = await fetch(jsRoutes.controllers.RtvController.getRuralPropertyRtvValues(qpid).url);
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setRtvValues', data || null);
            } catch (error) {
                console.error(`[Monarch error]: Error loading rural rtv values for QPID:${qpid}`);
                commit('setException', error);
                commit('setRtvValues', null);
                throw error;
            }
        },
        async getRuralPropertyRtvIndices({ commit }, qpid) {
            try {
                const response = await fetch(jsRoutes.controllers.RtvController.getRuralPropertyRtvIndices(qpid).url);
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setRtvIndices', data || null);
            } catch (error) {
                console.error(`[Monarch error]: Error loading rural rtv indices for QPID:${qpid}`);
                commit('setException', error);
                commit('setRtvIndices', null);
                throw error;
            }
        },
        async getRuralAssessments({ commit }, taCode) {
            try {
                const response = await fetch(jsRoutes.controllers.RtvController.getRuralIndexAssessments(taCode).url);
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data);
                }
                commit('setAssessments', data || []);
            } catch (error) {
                console.error(`[Monarch error]: Error loading rural assessments for TA:${taCode}`);
                commit('setException', error);
                commit('setAssessments', []);
                throw error;
            }
        },
        async getLookupLists({ commit }, force) {
            if (!this.groupings
                || this.groupings.length === 0
                || !this.qualityRatings
                || this.qualityRatings.length === 0
                || force
            ) {
                try {
                    const response = await fetch(jsRoutes.controllers.PropertyMasterData.getPickListValues().url);
                    const data = await response.json();
                    if (!response.ok) {
                        throw new Error(data);
                    }
                    commit('setGroupingList', data.propertyGroupingTypes);
                    commit('setQualityRatingList', data.qualityRatingTypes);
                } catch (error) {
                    console.error('[Monarch error]: Error loading lookup lists', error);
                    commit('setException', error);
                    throw error;
                }
            }
            if (!this.baseCategories || this.baseCategories.length === 0 || force) {
                try {
                    const response = await fetch(jsRoutes.controllers.RtvController.getRuralIndexBaseCategories().url);
                    const data = await response.json();
                    if (!response.ok) {
                        throw new Error(data);
                    }
                    commit('setBaseCategories', data);
                } catch (error) {
                    console.error('Error loading base categories:', error);
                    commit('setException', error);
                    throw error;
                }
            }
        },
    },
};

export default ruralRtv;

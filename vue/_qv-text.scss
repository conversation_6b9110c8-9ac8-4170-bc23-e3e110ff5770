@use "helpers" as *;

.qv-font {
  &-lighter {
    @include selectorMixin {
      font-weight: lighter;
    }
  }
  &-normal {
    @include selectorMixin {
      font-weight: normal;
    }
  }

  &-semibold {
    @include selectorMixin {
      font-weight: 600;
    }
  }

  &-bold {
    @include selectorMixin {
      font-weight: bold;
    }
  }
  &-bolder {
    @include selectorMixin {
      font-weight: bolder;
    }
  }
}

.qv-text  {
  &-sm {
    font-size: 1.1rem;
  }

  &-base {
    font-size: 13px;
  }

  &-md {
    font-size: 16px;
  }

  &-lg {
    font-size: 2rem;
  }

  &-wrap {
    white-space: normal;
  }

  &-nowrap {
    white-space: nowrap;
  }

  &-capitalize {
    text-transform: capitalize;
  }

  &-left {
    text-align: left;
  }

  &-right {
    text-align: right;
  }

  &-center {
    text-align: center;
  }
}
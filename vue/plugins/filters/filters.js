import Vue from 'vue';
import moment from 'moment';
import numeral from 'numeral';

// Format Date - 'x' means it accepts unix time since epoch
Vue.filter('date', (date, format = 'DD/MM/YYYY', validForms = ['DD/MM/YYYY', moment.HTML5_FMT.DATE, moment.ISO_8601,'x']) => {
    let formattedDate = date;
    if (date && date !== '' && moment(date, validForms, true).isValid()) {
        formattedDate = moment(date, validForms, true).format(format);
    }
    return formattedDate;
});

// TODO recommend to remove this and standardise on mumeral formatter ...
// easier to find global usage using string search and can use numeral.js library formats
// Currently this is only used by rating valuaiton calculation worksheet
Vue.filter('number', (value, decimal) => {
    let number;
    if (typeof value === 'number') {
        number = value;
    } else {
        number = Number(value ? value.replace(/[^0-9.-]+/g, '') : 0);
    }
    return Number(number ? number.toFixed(decimal) : 0);
});

// Format Distance (in metres)
Vue.filter('distance', (distance) => {
    let formattedDistance = '';
    if (distance && distance !== '') {
        formattedDistance = `${Math.round(distance)}m`;
    }
    return formattedDistance;
});

// currency filter is imported from vue-currency-filter, see main.js

// Format a boolean as a Y/N flag
Vue.filter('yn', value => (value ? 'Y' : 'N'));

Vue.filter('yesno', (value, valueIfEmpty) => {
    if (value == null) {
        return valueIfEmpty;
    }
    return value === true ? 'Yes' : 'No';
});

// EM Dash
Vue.filter('emptyToDash', value => ((value != null && value !== '') ? value : '—'));

Vue.filter('emptyTo', (value, valueIfEmpty) => ((value && value !== '') ? value : valueIfEmpty));

Vue.filter('numeral', (value, format, valueIfEmpty) => {
    if (value == null) return valueIfEmpty;

    return numeral(value).format(format);
});

Vue.filter('percentage', (value, format, valueIfEmpty) => {
    if (value == null) return valueIfEmpty;
    return numeral(value).format(format) + '%';
});

Vue.filter('hectares', (value, valueIfEmpty) => {
    if (value == null) return valueIfEmpty;

    return numeral(value).format('0.0000');
});

Vue.filter('description', (value) => {
    if (value == null || value.description == null || value.description === '') {
        return '—';
        // Can't use -> '&mdash;' because sometimes it displays raw string
    }
    return value.description;
});

Vue.filter('code', (value) => {
    if (value == null || value.code == null || value.code === '') {
        return '—';
        // Can't use -> '&mdash;' because sometimes it displays raw string
    }
    return value.code;
});

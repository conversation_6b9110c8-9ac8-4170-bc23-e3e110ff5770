/**
 * Resizes an element e.g textarea based on its scroll height.
 *
 * Usage:
 * <textarea v-auto-grow></textarea>
 */
export const AutoGrowDirective = {
    inserted: function (el) {
        const listen = (element, event, handler) => {
            element.addEventListener(event, handler, false);
        }

        const resize = () => {
            el.style.height = "auto"
            el.style.setProperty("height", el.scrollHeight -4 + 'px')
        }

        listen(el, 'change', resize);
        listen(el, 'input', resize);
        resize()
    },
}

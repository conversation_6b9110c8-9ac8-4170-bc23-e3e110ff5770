import { EventBus } from '../EventBus.js';
import debounce from '../utils/debounce';

const plugin = (store) => {
    const taCodeUpdateDebouncer = debounce((updateCodes) => {
        store.commit('setSelectedTaCodes', updateCodes);
    }, 500);

    EventBus.$on('taCodes', function(taCodes) {
        taCodeUpdateDebouncer(taCodes);
    });

    EventBus.$on('taCodesSelectAll', function (allSelected) {
        store.commit('setAllSelected', allSelected);
    })
};

export default plugin;

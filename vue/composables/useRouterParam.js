import { Ref, ComputedRef, computed, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

/**
 * @param {string} name
 * @param {Ref} defaultValue
 *
 * @returns {ComputedRef}
 */
export function useRouterParam(name, defaultValue = null, options = {}) {
    const {
        route = useRoute(),
        router = useRouter(),
        format = value => value,
    } = options;

    let param = route.params[name];

    const value = computed({
        get() {
            return format(param !== undefined ? param : defaultValue.value);
        },
        set(newValue) {
            console.log(`set ${name}`, newValue);
            if (param === newValue) {
                return;
            }

            param = (newValue === defaultValue.value || newValue === null) ? undefined : newValue;
            nextTick(async () => {
                await router.replace({
                    params: {
                        ...route.params,
                        [name]: param,
                    },
                });
            });
        },
    });

    watch(() => route.params[name],
        (newValue) => param = newValue,
        { immediate: true, flush: 'sync' });

    return value;
}

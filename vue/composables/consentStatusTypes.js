import { ref, onMounted } from 'vue';

const consentStatusList = ref([])

export function useConsentStatus() {
    onMounted(async () => {
        if (consentStatusList.value.length === 0) {
            consentStatusList.value = await getConsentStatusTypes()
        }}
    );

    return consentStatusList;
}

export async function getConsentStatusTypes() {
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getPicklistValue('ConsentStatus');
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        const result = await res.json();
        return result.result.ConsentStatus;
    }
    catch (error) {
        const message = 'Error calling controller to get Consent Status Types';
        console.error(message, error);
    }
}
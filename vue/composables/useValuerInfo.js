import { computed, ref } from 'vue';
import { store } from '@/DataStore';
const valuers = ref(null);
const registeredValuers = ref(null);
const valuersLoaded = ref(false);
const usersLoaded = ref(false);
const users = ref(null);

async function fetchRegisteredValuers() {
    const { url } = jsRoutes.controllers.ReferenceData.displayCountersigners();
    try {
        const res = await fetch(url);
        registeredValuers.value = await res.json();
        registeredValuers.value = registeredValuers.value.map(item => {
            return { id: item.id, name: item.name, ntUsername: item.ntUsername };
        });
        registeredValuers.value = [{ id: 'Unassigned', name: 'Unassigned', ntUsername: 'Unassigned' }].concat(registeredValuers.value);
    }
    catch (error) {
        console.error('Error fetching registered valuers', error);
    }
}

async function fetchValuers() {
    const { url } = jsRoutes.controllers.ReferenceData.displayValuers();
    try {
        const res = await fetch(url);
        valuers.value = await res.json();
        valuers.value = valuers.value.map(item => {
            return { id: item.id, name: item.name, ntUsername: item.ntUsername, target: item.target, prevTarget: item.prevTarget
            };
        });
        valuers.value = [{ id: 'Unassigned', name: 'Unassigned', ntUsername: 'Unassigned' }].concat(valuers.value);
    }
    catch (error) {
        console.error('Error fetching valuers', error);
        return;
    }
}

async function fetchUsers() {
    const { url } = jsRoutes.controllers.ReferenceData.displayUsers();
    try {
        const res = await fetch(url);
        const data = await res.json();
        store.commit('allUsers', data);
        return data;

    } catch (error) {
        console.error('Error fetching users', error);
    }
}

async function refreshValuerInfo() {
    valuersLoaded.value = false;
    valuers.value = null;
    registeredValuers.value = null;
    await fetchValuers();
    await fetchRegisteredValuers();
    valuersLoaded.value = true;
}

export default function useValuerInfo() {
    if (!valuersLoaded.value) {
        refreshValuerInfo();
    }

    function findValuerByFullName(fullName) {
        return valuers.value?.find(valuer => valuer.name.trim().toLowerCase() === fullName.trim().toLowerCase());
    }

    function findValuerByNtUsername(ntUsername) {
        return valuers.value?.find(valuer => valuer.ntUsername?.trim().toLowerCase() === ntUsername.trim().toLowerCase());
    }

    function findRegisteredValuerByNtUsername(ntUsername) {
        return registeredValuers.value?.find(valuer => valuer.ntUsername?.trim().toLowerCase() === ntUsername.trim().toLowerCase());
    }

    function findValuerById(id) {
        return valuers.value?.find(valuer => valuer.id === id);
    }

    async function loadUsers() {
        if (!usersLoaded.value) {
            users.value = await fetchUsers();
            users.value = users.value.map(item => {
                return { id: item.id, name: item.name, ntUsername: item.ntUsername };
            });
            users.value = [{ id: 'Unassigned', name: 'Unassigned', ntUsername: 'Unassigned' }].concat(users.value);
            usersLoaded.value = true;
        }
        return users;
    }

    return {
        valuers,
        registeredValuers,
        valuersLoaded,
        usersLoaded,
        users,
        loadUsers,
        refreshValuerInfo,
        findValuerByFullName,
        findValuerByNtUsername,
        findRegisteredValuerByNtUsername,
        findValuerById,
    };
}

import { ref, onMounted } from 'vue';

const objectionStatusList = ref([])

export function useObjectionStatus() {
    onMounted(async () => {
        if (objectionStatusList.value.length === 0) {
            objectionStatusList.value = await getObjectionStatusTypes()
        }}
    );

    return objectionStatusList;
}

export async function getObjectionStatusTypes() {
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getPicklistValue('ObjectionStatusType');
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        const result = await res.json();
        const cleanedData = result.result.ObjectionStatusType.map(item => ({
            ...item,
            code: item.code.trim()
        }));
        return cleanedData;
    }
    catch (error) {
        const message = 'Error calling controller to get Objection Status Types';
        console.error(message, error);
    }
}
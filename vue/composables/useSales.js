import { computed, ref, watch } from 'vue';
import SaleValidationModal from '@/components/rollMaintenance/propertySale/common/SaleValidationModal.vue';
import * as controller from '@/services/ApiSalesController';
import * as propertyController from '@/services/PropertyMasterDataController';
import { cloneDeep } from 'lodash';
import { store } from '@/DataStore';
import _ from 'underscore';
import Vue from 'vue';
import { getProperty, getPropertyInfoFull } from '@/services/PropertyMasterDataController';
import useModal from '@/composables/useModal';
import validateSaleClient from '@/utils/sales/validateSale';
import Format from '@/utils/FormatUtils';
const { formatDecimal, formatPrice } = Format.methods;
import { DateTime } from 'luxon';

const STATUS_PROCESSED = 2;
const STATUS_TO_PROCESS = 1;
const EXCLUDE_PROPERTIES = ['RB', 'RM', 'RP', 'RV', 'LV'];
const RESIDENTIAL = 'R';
const LIFESTYLE = 'L';

const defaultSale =
    {
        saleId: null,
        qpid: null,
        monarchSaleProcessingStatusId: STATUS_PROCESSED,
        saleStatusId: 2,
        assuranceLevelId: null,
        agreementDate: null,
        settlementDate: null,
        excludeFromRTV: false,
        vendorPurchaser: '',
        remarks: '',
        salesDirectId: null,
        capitalValue: null,
        landValue: null,
        revisionCapitalValue: null,
        revisionLandValue: null,
        propertyInfo: {
            legalDescription: '',
            category: {
                code: '',
            },
            landArea: 0,
            planNumber: ``,
            production: 0,
        },
        price: {
            gross: null,
            net: null,
            chattels: null,
            other: null,
            gst: null,
        },
        classifications: {
            saleTypeId: 3,
            saleTenureId: 1,
            priceValueRelationshipId: 1,
            buyerSellerRelationshipId: null,
            buyerTypeId: null,
            saleOwnershipTypeId: null,
            farmBuyerTypeId: null,
            farmSellerTypeId: null,
        },
        userName: null,
        domainName: null,
        crossReferencedProperties: [],
        pdfNumberSource: null,
    }

function populateRFC(obj) {
    obj.rfc = {
        reason: 'Sales ex...',
        output: 7,
        source: 3,
    };
}
const isFirstLoad = ref(null);
const loading = ref(false);
const getSalesDirectSaleExistsLoading = ref(false);
const saving = ref(false);
const exception = ref(null);
const dvr = ref(null);
const sale = ref(null);
const titles = ref([]);
const crossReferencedProperties = ref([]);
const nonDeletedCrossReferencedProperties = computed(() => crossReferencedProperties.value?.filter(p => !p.deleted) ?? []);
const qpidsForTitleFetch = computed(() => Array.from(new Set([parseInt(qpid.value), ...nonDeletedCrossReferencedProperties.value.map(p => parseInt(p.qpid))])));
const primaryProperty = ref(null);
const linzWarnings = ref([]);
const taLandUse = ref([]);
const user = computed(() => store.state.userData);
const isNewSale = ref(false);
const isSaleToBeProcessed = ref(false);
const isSingleSale = computed(() => sale.value?.classifications?.saleTypeId === 3);
const isMultiSale = computed(() => sale.value?.classifications?.saleTypeId === 1);
const isPartSale = computed(() => sale.value?.classifications.saleTypeId === 2);
const isPastRevisionDate = computed(() => dvr.value?.saleCurrentRevisionDate < dvr.value?.raCurrentRevisionDate);
const saleType = computed(() => sale.value?.classifications?.saleTypeId);
const saleTypeChanged = ref(false);
const saleStatus = computed(() => sale.value?.saleStatusId);
const qpid = computed(() => sale.value?.qpid);
const saleId = computed(() => sale.value?.saleId);
const isInValidpropertyForAutoChattelCalc = computed(() => {
    const category = dvr.value?.category?.code;
    return (!(category.startsWith(RESIDENTIAL) || category.startsWith(LIFESTYLE)) || EXCLUDE_PROPERTIES.includes(category));
})
const propertyInfoFull = ref(null);
const agreementDate = computed(() => sale.value?.agreementDate);
const allPropertyRtv = computed(() => {
    const date = DateTime.fromJSDate(new Date(agreementDate.value ?? undefined));
    const primaryPropertyRtv = getPrimaryPropertyRtv(date);
    const crossRefRtv = nonDeletedCrossReferencedProperties.value.map(property => getRtvRecord(property.rtv, date, property.category));
    return [primaryPropertyRtv, ...crossRefRtv];
});
const rtvMissing = computed(() => {
    const atLeast1RtvValue = allPropertyRtv.value.some(rtv => !isNaN(parseInt(rtv)));
    const allHaveRtv = allPropertyRtv.value.every(rtv => !isNaN(parseInt(rtv)));
    return atLeast1RtvValue && !allHaveRtv;
});
const rtv = computed(() => allPropertyRtv.value.reduce((acc, rtvValue) => acc + (rtvValue ?? 0), 0));

watch(() => qpidsForTitleFetch.value, async (newValue) => {
    if (isFirstLoad.value) {
        return;
    }
    if (Array.isArray(newValue) && newValue.length > 0 && newValue.every(q => Number.isInteger(q))) {
        const res = await controller.getTitles('', newValue.join(','));
        const uniqueQpids = Array.from(new Set(res.map(t => t.qpid)));
        const _titles = uniqueQpids.map(_qpid => res.find(t => parseInt(t.qpid) === parseInt(_qpid)));
        const qpidMatch = _qpid => _titles.find(t => parseInt(t.qpid) === parseInt(_qpid));
        const index = _.findIndex(_titles, qpidMatch(qpid.value));
        if (index !== -1) {
            const firstTitle = _titles.splice(index, 1)[0];
            _titles.unshift(firstTitle);
        }
        titles.value = _titles;
    }
});

function getPrimaryPropertyRtv(saleDate) {
    const category = (primaryProperty.value?.property?.category?.code ?? '').trim().toUpperCase();
    const isResiOrLifestyle = ['R', 'L'].includes(category[0]);
    if (!propertyInfoFull.value || !saleDate.isValid || !isResiOrLifestyle) {
        return null;
    }
    return getRtvRecord(propertyInfoFull.value?.rtv, saleDate, category);
}

function getRtvRecord(propertyRtv, date, category) {
    const rtvRecord = propertyRtv?.find((r) => {
        const rtvAsAtDate = DateTime.fromJSDate(new Date(r.as_at_date));
        const hasSameMonth = rtvAsAtDate.hasSame(date, 'month');
        const hasSameYear = rtvAsAtDate.hasSame(date, 'year');
        return hasSameMonth && hasSameYear;
    });
    if (!rtvRecord) {
        return null;
    }
    const vacant = ['RB', 'RM', 'RP', 'RV', 'LB', 'LV'];
    return vacant.includes(category.substring(0, 2)) ? parseInt(rtvRecord.revaluer_land_value ?? 0) : parseInt(rtvRecord.revaluer_capital_value ?? 0);
}

const revisionValues = computed(() => {
    const properties = crossReferencedProperties.value?.filter(p => !p.deleted);
    const anyCrossRefHasRevisionValues = properties.some(p => !isNaN(parseFloat(p.revisionCapitalValue)));
    const allCrossRefHaveRevisionValues = properties.every(p => !isNaN(parseFloat(p.revisionCapitalValue)));
    const primary = primaryProperty.value?.property;
    const primaryHasRevisionValues = !isNaN(parseFloat(primary?.revisedValuation?.capitalValue));
    const atLeastOneRevisionValue = anyCrossRefHasRevisionValues || primaryHasRevisionValues;
    const allHaveRevisionValues = allCrossRefHaveRevisionValues && primaryHasRevisionValues;
    const primaryRevisionCapitalValue = primary?.revisedValuation?.capitalValue ?? 0;
    const primaryRevisionLandValue = primary?.revisedValuation?.landValue ?? 0;
    const crossRefRevisionCapitalValue = properties.reduce((acc, p) => acc + (p.revisionCapitalValue ?? 0), 0);
    const crossRefRevisionLandValue = properties.reduce((acc, p) => acc + (p.revisionLandValue ?? 0), 0);
    const totalRevisionCapitalValue = primaryRevisionCapitalValue + crossRefRevisionCapitalValue;
    const totalRevisionLandValue = primaryRevisionLandValue + crossRefRevisionLandValue;
    const totalRevisionImprovementsValue = totalRevisionCapitalValue - totalRevisionLandValue;
    const totalRevisionCapitalValueFormatted = formatPrice(totalRevisionCapitalValue);
    const totalRevisionLandValueFormatted = formatPrice(totalRevisionLandValue);
    const totalRevisionImprovementsValueFormatted = formatPrice(totalRevisionImprovementsValue);
    return {
        atLeastOneRevisionValue,
        allHaveRevisionValues,
        shouldDisplayWarning: atLeastOneRevisionValue && !allHaveRevisionValues,
        warningMessage: 'Not all linked properties have Proposed Revision values',
        totalRevisionCapitalValue,
        totalRevisionCapitalValueFormatted,
        totalRevisionLandValue,
        totalRevisionLandValueFormatted,
        totalRevisionImprovementsValue,
        totalRevisionImprovementsValueFormatted,
    };
});

const validationResult = ref({
    status: null,
    errors: null,
    warnings: null,
    hasErrors: null,
    hasWarnings: null,
    validations: null,
});

const formattedValidationResult = computed(() => ({
    errors: validationResult.value?.errors?.filter(e => e)?.map(error => ({ message: error })) ?? [],
    warnings: validationResult.value?.warnings?.filter(w => w)?.map(warning => ({ message: warning })) ?? [],
}));

const modal = useModal();

export default function useSales() {
    async function loadSale(_qpid, _saleId, shouldValidate) {
        isFirstLoad.value = true;
        isNewSale.value = _saleId == null;
        let initialLandAreaValue;

        try {
            if (isNewSale.value) {
                await createDefaultSale(_qpid);
                await getDvr(_qpid, true);
                await populateChattelParameter(_qpid);
                titles.value = await controller.getTitles('', qpid.value); // TODO: multisale add cross referenced qpids
            } else {
                await refreshSale(_saleId);
                await getDvr(_saleId)
                initialLandAreaValue = dvr.value.site.landArea;
                titles.value = await getTitles(_saleId, '');
                if(shouldValidate) {
                    await validateSale();
                }
            }
            const crossReferencedPropertiesToSet = structuredClone(sale.value.crossReferencedProperties ?? []);
            await Promise.all(crossReferencedPropertiesToSet.map(async property => enrichCrossReferencedProperty(property)));
            crossReferencedProperties.value = crossReferencedPropertiesToSet;
            const [getTALandUseZoneReq, getPropertyReq, getPropertyInfoFullReq] = await Promise.all([
                getTALandUseZone(qpid.value),
                getProperty(qpid.value),
                getPropertyInfoFull(qpid.value),
            ]);

            taLandUse.value = getTALandUseZoneReq.map(ta => ({id: ta.code, ...ta}));
            primaryProperty.value = getPropertyReq;
            propertyInfoFull.value = getPropertyInfoFullReq;
            if (isNewSale.value) {
                sale.value.propertyInfo.legalDescription = primaryProperty.value?.property?.legalDescription ?? '';
            }
            else {
                dvr.value.site.landArea = initialLandAreaValue;
            }
            watch(saleType, onSaleTypeChanged);
            watch(saleStatus, onSaleStatusChanged);
        } catch (error) {
            console.error(error);
            exception.value = error;
        }
        finally {
            isFirstLoad.value = false;
        }
    }

    async function refreshSale(saleId) {
        try {
            loading.value = true;
            const saleResponse = await controller.getSale(saleId);

            if (!saleResponse) {
                exception.value = 'No sale found';
                return;
            }

            if (!saleResponse.rfc) {
                populateRFC(saleResponse);
            }

            if (saleResponse.monarchSaleProcessingStatusId === STATUS_TO_PROCESS) {
                getLinzSaleWarnings(saleId);
            }

            populatePdfNumberSource(saleResponse);

            sale.value = saleResponse;
            return sale;
        }
        catch (e) {
            console.error(e);
            exception.value = e;
        }
        finally {
            loading.value = false;
        }
    }

    function populatePdfNumberSource(saleResponse) {
        if (saleResponse.saleSource === 'LINZ') {
            saleResponse.pdfNumberSource = 1;
        }
        else if (saleResponse.saleSource === 'SalesDirect') {
            saleResponse.pdfNumberSource = 2;
        }
    }

    async function createDefaultSale(_qpid) {
        loading.value = true;
        const newSale = cloneDeep(defaultSale);
        newSale.qpid = _qpid;
        populateRFC(newSale);

        sale.value = newSale;
        loading.value = false;
        return sale;
    }

    async function saveSale() {
        sale.value.crossReferencedProperties = crossReferencedProperties.value;
        const payload = {
            sale: sale.value,
            dvr: dvr.value,
            isFullValidationCheck: true,
        };
        if (isSaleToBeProcessed.value) {
            if (propertyInfoFull.value?.propertyInfo?.qvMaintainsOwnershipData) {
                const hasUpdatedOwnershipInQivs = await modal.showWarning('Save Sale', 'Sale is \'To Process\'. Have you updated ownership in QIVS?');
                if (!hasUpdatedOwnershipInQivs) {
                    return;
                }
            }
        }
        const clientValidationResult = validateSaleClient(payload);
        if (clientValidationResult.hasErrors) {
            validationResult.value = clientValidationResult;
            await showValidationErrors(clientValidationResult);
            return;
        }
        if (isMultiSale.value && crossReferencedProperties.value.length === 0) {
            await modal.showError('Save Sale', 'Multi sale must have at least one cross referenced property.');
            return;
        }

        if (!isNewSale.value) {
            if (saleTypeChanged.value && (isMultiSale.value || isPartSale.value)) {
                const hasUpdatedDetails = await modal.showWarning('Save Sale', 'Multi or Part Sale: Have you updated CV, LV, Area and Legal Description?');

                if (!hasUpdatedDetails) {
                    return;
                }
            }
        }
        await validateSale();
        if (validationResult.value?.status !== 'SUCCESS') {
            await modal.showError('Error', 'Sale validation failed');
            return;
        }
        if (validationResult.value?.hasErrors || validationResult.value?.hasWarnings) {
            const shouldSave = await showValidationErrors({ ...validationResult.value, formattedErrors: formattedValidationResult.value?.errors ?? [], formattedWarnings: formattedValidationResult.value?.warnings ?? [] });
            if (!shouldSave) {
                return;
            }
        }

        if(isSaleToBeProcessed.value) {
            sale.value.monarchSaleProcessingStatusId = STATUS_PROCESSED;
        }

        try {
            sale.value.userName = user.value.userName;
            dvr.value.userName = user.value.userName;

            const payloadToSave = {
                sale: sale.value,
                dvr: dvr.value,
            };

            saving.value = true;
            const result = await controller.save(payloadToSave);
            sale.value = result.sale ?? sale.value;
            dvr.value = result.dvr ?? dvr.value;
            crossReferencedProperties.value = sale.value.crossReferencedProperties;
            if (result.status === 'SUCCESS') {
                window.close();
            }
            else {
                const message = 'Something went wrong saving the sale';
                console.error(`${message}: ${result.message}`);
                await modal.showError('Error', message);
            }
        } catch (e) {
            console.error(e);
            exception.value = e;
            return modal.showError('Error', 'Something went wrong saving the sale');
        } finally {
            saving.value = false;
            isSaleToBeProcessed.value = false;
        }
    }

    async function getDvr(id, current = false, saleIdToRefresh = 0) {
        try {
            loading.value = true;
            const dvrResponse = await controller.getDvr(id, current, saleIdToRefresh);

            if (!dvrResponse) {
                exception.value = 'No dvr found';
                return;
            }

            if (!dvrResponse.audit) {
                populateRFC(dvrResponse);
            }

            dvr.value = dvrResponse;
            return dvr;
        }
        catch (e) {
            console.error(e);
            exception.value = e;
        }
        finally {
            loading.value = false;
        }
    }


    async function getTALandUseZone(qpid) {
        try {
            return await controller.getTALandUseZone(qpid);
        } catch (e) {
            console.error(e);
            exception.value = e;
        }
    }

    async function getTitles(saleId, qpids) {
        if (Array.isArray(qpids)) {
            qpids = qpids.join(',');
        }
        try {
            return await controller.getTitles(saleId, qpids);
        } catch(e) {
            console.error(e);
            exception.value = e;
        }
    }

    async function relinkSaleRfs() {
        try {
            saving.value = true;
            const payload = {
                saleId: saleId.value,
                relinkSaleId: sale.value.relinkSaleId,
            };

            return controller.relinkSaleRfs(payload);
        } catch (e) {
            console.error(e);
            exception.value = e;
            alert('Oops, Something went wrong!');
        } finally {
            saving.value = false;
        }
    }

    async function deleteSale() {
        try {
            const payload = {
                saleId: saleId.value,
                qpid: qpid.value,
                rfc: sale.value.rfc,
            };

            saving.value = true;
            await controller.deleteSale(payload);
            window.close();
        } catch (e) {
            console.error(e);
            exception.value = e;
            alert('Oops, something went wrong!');
        } finally {
            saving.value = false;
        }
    }

    async function validateSale() {
        try {
            const payload = {
                sale: sale.value,
                dvr: dvr.value,
                isFullValidationCheck: true,
            };

            saving.value = true;
            const validation = await controller.validateSale(payload);
            if (validation?.status === 'SUCCESS') {
                validationResult.value = validation;
            }
            else {
                console.error('Non-successful sale validation response');
                validationResult.value = {
                    status: null,
                    hasErrors: null,
                    hasWarnings: null,
                    validations: null,
                    errors: null,
                    warnings: null,
                };
            }
        }
        catch (e) {
            console.error(e);
            exception.value = e;
            validationResult.value = {
                status: null,
                hasErrors: null,
                hasWarnings: null,
                validations: null,
                errors: null,
                warnings: null,
            };
            alert('Oops, something went wrong!');
        }
        finally {
            saving.value = false;
        }
    }

    async function getSalePortalSalePdfUrl(salePortalSaleId, sourceId) {
        const start = Date.now();
        const duration = 666;
        try {
            getSalesDirectSaleExistsLoading.value = true;
            return await controller.getSalePortalSalePdfUrl(salePortalSaleId, sourceId);
        }
        catch (e) {
            console.error(e);
            exception.value = e;
            alert('Oops, something went wrong!');
        }
        finally {
            setTimeout(() => { getSalesDirectSaleExistsLoading.value = false; }, Math.max(0, duration - (Date.now() - start)));
        }
    }

    async function openSalePdf(noticeId, sourceId = 1 /*LINZ*/) {
        try {
            await controller.openSalesNoticePreview(noticeId, sourceId);
        }
        catch (error) {
            console.error(error);
            await modal.showError('Failed to download PDF', 'Something went wrong retrieving the sales notice PDF');
        }
    }

    async function onSaleTypeChanged(newValue, oldValue) {
        saleTypeChanged.value = true;
        if (newValue !== 1) {
            if(crossReferencedProperties.value.length > 0) {
                const removeCrossRef = await modal.showWarning('Warning', 'Removing cross referenced properties from the Sale.');
                if (!removeCrossRef) {
                    sale.value.classifications.saleTypeId = oldValue;
                    return;
                }                                
            }            
            clearCrossReferencedProperties();
        }
    }

    async function addCrossReferencedProperty(property) {
        if (isSingleSale.value) {
            return;
        }

        try {
            crossReferencedProperties.value.push(property);
            const index = crossReferencedProperties.value.indexOf(property);
            await enrichCrossReferencedProperty(property);
            Vue.set(crossReferencedProperties.value, index, property);
            recalculateValues();
        }
        catch (e) {
            removeCrossReferencedProperty(property);
            console.error(e);
            exception.value = e;
        }
    }

    async function enrichCrossReferencedProperty(property) {
        const [propertyData, fullPropertyInfo] = await Promise.all([
            propertyController.getProperty(parseInt(property.qpid)),
            getPropertyInfoFull(parseInt(property.qpid)),
        ]);
        const { landUseData, currentValuation, revisedValuation, massAppraisalData } = propertyData.property;

        property.capitalValue = currentValuation?.capitalValue ?? 0;
        property.landValue = currentValuation?.landValue ?? 0;
        property.revisionCapitalValue = revisedValuation?.capitalValue;
        property.revisionLandValue = revisedValuation?.landValue;
        property.improvementsValue = property.capitalValue - property.landValue;
        property.area = landUseData?.landArea ?? 0;
        property.production = landUseData?.production ?? 0;
        property.bedrooms = massAppraisalData?.bedrooms ?? 0;
        property.bathrooms = propertyData?.numberOfBathrooms ?? 0;
        property.toilets = massAppraisalData?.toilets ?? 0;
        property.buildingSiteCover = landUseData?.buildingSiteCover ?? 0;
        property.totalFloorArea = landUseData?.totalFloorArea ?? 0;
        property.mainLivingArea = massAppraisalData?.mainLivingArea ?? 0;
        property.totalLivingArea = massAppraisalData?.totalLivingArea ?? 0;
        property.freestandingGarages = massAppraisalData?.freestandingGarages ?? 0;
        property.underMainRoofGarages = massAppraisalData?.underMainRoofGarages ?? 0;
        property.rtv = fullPropertyInfo?.rtv ?? [];
        property.category = fullPropertyInfo?.categoryCode?.trim()?.toUpperCase() ?? '';
    }

    function removeCrossReferencedProperty(property) {
        const index = _.findIndex(crossReferencedProperties.value, p => parseInt(p.qpid) === parseInt(property.qpid));
        if (property.assessmentSaleId != null) {
            property.deleted = true;
            Vue.set(crossReferencedProperties.value, index, property);
            recalculateValues();
            return;
        }
        crossReferencedProperties.value = _.filter(crossReferencedProperties.value, (p) => parseInt(p.qpid) !== parseInt(property.qpid));
        recalculateValues();
    }

    function clearCrossReferencedProperties() {
        crossReferencedProperties.value.forEach(property => {
            if (property.assessmentSaleId != null) {
                property.deleted = true;
            }
        });
    
        crossReferencedProperties.value = crossReferencedProperties.value.filter(property => property.assessmentSaleId != null);
    
        recalculateValues();
    }

    async function recalculateValues(manuallyTriggered = false) {
        if (manuallyTriggered && isPastRevisionDate.value) {
            return modal.showError('Error', 'You cannot update values when the sale is not current');
        }
        if (!primaryProperty.value) {
            return;
        }

        const calculatedValues = crossReferencedProperties.value.reduce((acc, property) => {
            if (property.deleted) {
                return acc;
            }
            acc.capitalValue += property.capitalValue;
            acc.landValue += property.landValue;
            acc.area += property.area;
            acc.revisionCapitalValue += property.revisionCapitalValue;
            acc.revisionLandValue += property.revisionLandValue;
            acc.production += property.production;
            acc.bedrooms += property.bedrooms;
            acc.bathrooms += property.bathrooms;
            acc.toilets += property.toilets;
            acc.buildingSiteCover += property.buildingSiteCover;
            acc.totalFloorArea += property.totalFloorArea;
            acc.mainLivingArea += property.mainLivingArea;
            acc.totalLivingArea += property.totalLivingArea;
            acc.freestandingGarages += property.freestandingGarages;
            acc.underMainRoofGarages += property.underMainRoofGarages;
            return acc;
        }, {
            capitalValue: 0,
            landValue: 0,
            improvementsValue: 0,
            area: 0,
            revisionCapitalValue: 0,
            revisionLandValue: 0,
            production: 0,
            bedrooms: 0,
            bathrooms: 0,
            toilets: 0,
            buildingSiteCover: 0,
            totalFloorArea: 0,
            mainLivingArea: 0,
            totalLivingArea: 0,
            freestandingGarages: 0,
            underMainRoofGarages: 0,
        });

        const { landUseData, currentValuation, massAppraisalData } = primaryProperty.value.property;
        dvr.value.currentValuation.capitalValue = calculatedValues.capitalValue + currentValuation.capitalValue;
        dvr.value.currentValuation.landValue = calculatedValues.landValue + currentValuation.landValue;
        dvr.value.currentValuation.improvementsValue = dvr.value.currentValuation.capitalValue - dvr.value.currentValuation.landValue;
        dvr.value.site.landArea = formatDecimal(calculatedValues.area + landUseData.landArea, 4);
        dvr.value.currentValuation.capitalValue = Math.round(dvr.value.currentValuation.capitalValue * 10000) / 10000;
        dvr.value.site.carparks = massAppraisalData.underMainRoofGarages + massAppraisalData.freestandingGarages +
                                 (calculatedValues.underMainRoofGarages ?? 0) + (calculatedValues.freestandingGarages ?? 0);
        dvr.value.summary.totalBedrooms = massAppraisalData.bedrooms + calculatedValues.bedrooms ?? 0;
        dvr.value.summary.totalBathrooms = primaryProperty.value.numberOfBathrooms + calculatedValues.bathrooms ?? 0;
        dvr.value.summary.totalToilets = massAppraisalData.toilets + calculatedValues.toilets ?? 0;
        dvr.value.summary.buildingSiteCover = landUseData.buildingSiteCover + calculatedValues.buildingSiteCover;
        dvr.value.summary.freestandingGarages = massAppraisalData.freestandingGarages+ calculatedValues.freestandingGarages;
        dvr.value.summary.mainLivingArea = massAppraisalData.totalLivingArea + calculatedValues.mainLivingArea;
        dvr.value.landUseData.production = landUseData.production + calculatedValues.production;
        dvr.value.summary.totalFloorArea = landUseData.totalFloorArea + calculatedValues.totalFloorArea;
        dvr.value.summary.totalLivingArea = massAppraisalData.totalLivingArea + calculatedValues.totalLivingArea;
        dvr.value.summary.underMainRoofGarages = massAppraisalData.underMainRoofGarages + calculatedValues.underMainRoofGarages;
    }

    async function refreshPropertyData() {
        if (isPastRevisionDate.value) {
            return modal.showError('Error', 'You cannot update values when the sale is not current');
        }

        const res = await getDvr(qpid.value, true, saleId.value);
        await Vue.nextTick();
        recalculateValues(true);
        return res;
    }

    function showValidationErrors(payload) {
        return modal.show(SaleValidationModal, payload);
    }

    function getErrorsForLabel(label) {
        return validationResult.value?.validations?.[label]?.errors ?? [];
    }

    async function populateChattelParameter(_qpid) {
        if(isInValidpropertyForAutoChattelCalc.value) {
            return;
        }
        try{
            const res = await controller.getChattelParameter(_qpid);
            if (!res) {
                exception.value = `No Chattels Parameters found for qpid : ${_qpid}`;
                return;
            }
            sale.value.price.chattels = res.chattelAmount;
            return res;
        }
        catch (e) {
            console.error(e);
            exception.value = e;
        }
    }

    async function getLinzSaleWarnings(saleId) {
        try{
            const res = await controller.getLinzSaleWarnings(saleId);
            if (!res) {
                exception.value = `No LINZ Warnings found for sale ID : ${saleId}`;
                return;
            }
            linzWarnings.value = res.warningMessages ?? [];
        }
        catch (e) {
            console.error(e);
            exception.value = e;
        }
    }

    function onSaleStatusChanged(newValue) {
        sale.value.pdfNumberSource = null;
        if(newValue == 1) {
            sale.value.pdfNumberSource = 1;
        }
    }

    return {
        loading,
        getSalesDirectSaleExistsLoading,
        saving,
        exception,
        dvr,
        sale,
        validationResult,
        formattedValidationResult,
        getErrorsForLabel,
        crossReferencedProperties,
        titles,
        taLandUse,
        isNewSale,
        isSingleSale,
        isMultiSale,
        qpid,
        saleId,
        primaryProperty,
        propertyInfoFull,
        isSaleToBeProcessed,
        linzWarnings,
        refreshSale,
        loadSale,
        saveSale,
        deleteSale,
        validateSale,
        relinkSaleRfs,
        addCrossReferencedProperty,
        removeCrossReferencedProperty,
        refreshPropertyData,
        getSalePortalSalePdfUrl,
        recalculateValues,
        getDvr,
        revisionValues,
        openSalePdf,
        rtv,
        rtvMissing,
    };
}

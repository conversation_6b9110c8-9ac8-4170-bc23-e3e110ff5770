import { computed } from 'vue';

export const DRAFT_PROPERTY_COMPONENTS = Object.freeze({
    GENERAL_PROPERTY_INFORMATION: 'GENERAL_PROPERTY_INFORMATION',
    LOCATION_DETAILS: 'LOCATION_DETAILS',
    PROPERTY_SUMMARY: 'PROPERTY_SUMMARY',
    DERIVED_FIELDS: 'DERIVED_FIELDS',
    CONSTRUCTION_INFORMATION: 'CONSTRUCTION_INFORMATION',
    SPACES: 'SPACES',
    SITE_IMPROVEMENTS: 'SITE_IMPROVEMENTS',
});

export default function useDraftProperty(propertyDetail, property) {

    const buildings = computed(() => (propertyDetail.value.buildings?.length ? propertyDetail.value.buildings : [{}]));
    const siteDevelopment = computed(() =>
            propertyDetail.value?.site?.siteDevelopment ?? {
                quality: null,
                description: null,
            }
    );
    const dvrSnapshot = computed(() => propertyDetail.value.dvrSnapshot ?? {});
    const otherImprovements = computed(() => (propertyDetail.value.otherImprovements?.length ? propertyDetail.value.otherImprovements : [{}]));
    const isResidentialVacant = computed(() => property.value.category.code && ['RB', 'RM', 'RV'].includes(String(property.value.category.code).substring(0, 2).toUpperCase()));
    const hasQivsImprovements = computed(() => propertyDetail.value.qivsImprovementsStatus !== 'NO_IMPROVEMENTS');
    const hasUsefulQivsImprovements = computed(() => propertyDetail.value.qivsImprovementsStatus === 'USEFUL_IMPROVEMENTS');

    return {
        get qpid() {
            return propertyDetail.value.qpid;
        },
        get taCode() {
            return property.value.territorialAuthority.code;
        },
        get hasDerivedDvrFields() {
            return propertyDetail.value.entityVersion > 0;
        },
        buildings,
        dvrSnapshot,
        siteDevelopment,
        otherImprovements,
        isResidentialVacant,
        hasQivsImprovements,
        hasUsefulQivsImprovements,
    };
}

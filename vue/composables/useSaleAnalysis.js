import { getSale } from '@/services/ApiSalesController';
import { useRouter } from 'vue-router/composables';
import { checkAnalysis, migrateResidentialToRuralAnalysis, deleteSalesAnalysis } from '@/services/SalesAnalysisController';
import { isLifestyleSale, isResidentialSale, isRuralSale, LIFESTYLE_CATEGORIES, RURAL_CATEGORIES } from '@/utils/sales/analysisCategories';
import useModal from '@/composables/useModal';
import { store } from '@/DataStore';
import { canCreateRuralWorksheet } from '@/utils/worksheet/worksheetCategories';

export const RESIDENTIAL_ANALYSIS = 'RESIDENTIAL_ANALYSIS';
export const RURAL_ANALYSIS = 'RURAL_ANALYSIS';

export const ANALYSIS_RURAL_CATEGORIES = [...LIFESTYLE_CATEGORIES, ...RURAL_CATEGORIES];

const CREATE_RURAL_WORKSHEET = 'CREATE_RURAL_WORKSHEET';
const OPEN_EXISTING_ANALYSIS = 'OPEN_EXISTING_ANALYSIS';
const CANCEL = 'CANCEL';

export function useSaleAnalysis() {
    const router = useRouter();
    const modal = useModal();

    async function tryOpenAnalysisById(saleId, popup = true) {
        if (store.state.userData.isExternalUser) {
            return false;
        }

        const sale = await getSale(saleId);
        return tryOpenAnalysis(sale, popup);
    }

    async function getAnalysisCategory(propertyCategoryCode) {

        if (isResidentialSale(propertyCategoryCode)) {
            return RESIDENTIAL_ANALYSIS;
        }
    
        else if (isRuralSale(propertyCategoryCode) || isLifestyleSale(propertyCategoryCode)) {
            return RURAL_ANALYSIS;
        }
    }

    async function tryOpenAnalysis(sale, popup = true) {
        const checks = await checkAnalysis(sale.saleId)
            .then(res => res.json());

        if (isResidentialSale(checks.categoryCode)) {
            return openResidentialAnalysis(sale, popup);
        }

        if (!isLifestyleSale(checks.categoryCode) && !isRuralSale(checks.categoryCode)) {
            return modal.showError('Cannot Be Analysed', 'This sale cannot be analysed');
        }

        if (checks.hasRuralWorksheet) {
            if (checks.hasRuralAnalysis) {
                return openRuralAnalysis(sale, popup);
            }

            if (!checks.hasResidentialAnalysis) {
                return openRuralAnalysis(sale, popup);
            }

            const deleteResidential = await modal.showInfo(
                'Existing Residential Analysis',
                ['There is a Residential Sales Analysis existing for this sale on the property. If you want to keep the existing Residential style Sales Analysis, press CONTINUE.', 'If you want to delete the existing Residential Sales Analysis and create a Rural Sales Analysis, press DELETE'],
                {
                    confirmText: 'DELETE',
                    cancelText: 'CONTINUE',
                });

            if (deleteResidential) {
                const result = await migrateResidentialToRuralAnalysis(sale.saleId);
                if (!result.ok) {
                    return modal.showError('Error', 'An error occurred while migrating the Residential Analysis to a Rural Analysis');
                }

                return openRuralAnalysis(sale, popup);
            }

            if (!deleteResidential) {
                return openResidentialAnalysis(sale, popup);
            }
        }

        if (checks.hasResidentialAnalysis) {
            const createRuralWorksheetResult = await promptCreateRuralWorksheet(sale, true);
            if (createRuralWorksheetResult.action === CREATE_RURAL_WORKSHEET) {
                return createRuralWorksheet(createRuralWorksheetResult.targetQpid);
            }

            if (createRuralWorksheetResult.action === CANCEL) {
                return false;
            }

            return openResidentialAnalysis(sale, popup);
        }

        const createWorksheetResult = await promptCreateRuralWorksheet(sale, false);
        if (createWorksheetResult.action === CREATE_RURAL_WORKSHEET) {
            return createRuralWorksheet(createWorksheetResult.targetQpid);
        }
    }

    async function promptCreateRuralWorksheet(sale, hasExistingResidentialAnalysis) {
        let message = [];
        let apportionmentCodeChanged = false;
        let hasApportionments = sale.propertyInfo.apportionments && sale.propertyInfo.apportionments.length > 0;
        let hasRuralApportionment = false;
        let result = {
            action: CANCEL,
            targetQpid: null,
        }

        if (hasExistingResidentialAnalysis) {
            message.push('There is a Residential Sales Analysis existing for this sale on the property. If you want to keep the existing Residential style Sales Analysis, press CONTINUE.');
        }

        if (hasApportionments) {
            const ruralApportionment = sale.propertyInfo.apportionments.find(apportionment => {
                return canCreateRuralWorksheet(apportionment.categoryCode);
            });

            hasRuralApportionment = ruralApportionment;
            apportionmentCodeChanged = sale.propertyInfo.apportionmentCode !== sale.apportionmentCode;
            message.push('Since this property does NOT currently have a Rural Worksheet on any of its apportionment\'s, if you would like to create a Rural style Sales Analysis please first create a Rural Worksheet then you can create a Rural Sales Analysis.');
            result.targetQpid = ruralApportionment.qpid;
        } else {
            message.push('Since this property does NOT currently have a Rural Worksheet, if you would like to create a Rural style Sales Analysis please first create a Rural Worksheet then you can create a Rural Sales Analysis.');
            result.targetQpid = sale.qpid;
        }

        const shouldCreateWorksheet = await modal.showInfo(
            hasExistingResidentialAnalysis ? 'Existing Residential Analysis' : 'No Rural Worksheet',
            message,
            {
                confirmText: 'CREATE RURAL WORKSHEET',
                cancelText: hasExistingResidentialAnalysis ? 'CONTINUE' : 'CLOSE'
            });

        if (hasApportionments && !hasRuralApportionment) {
            await modal.showError('No Rural Category Apportionment\'s', 'This property has no apportionment\'s with a rural category code.')
            return CANCEL;
        }

        if (apportionmentCodeChanged && shouldCreateWorksheet) {
            const continueOnNewApportionment = await modal.showWarning(
                'Apportionment Code Changed',
                'The Sale was on a Property with a different apportionment code. The Sale Analysis will be created based on the new apportionment code.',
                {
                    confirmText: 'CONTINUE',
                    cancelText: 'CANCEL'
                });

            if (!continueOnNewApportionment) {
                result.action = CANCEL;
                return result;
            }
        }

        if (!shouldCreateWorksheet) {
            result.action = hasExistingResidentialAnalysis ? OPEN_EXISTING_ANALYSIS : CANCEL;
            return result;
        }

        result.action = CREATE_RURAL_WORKSHEET;
        return result;
    }

    async function createRuralWorksheet(qpid) {
        await router.push({
            name: 'rural-worksheet',
            params: {
                id: qpid
            }
        })
    }

    async function openRuralAnalysis(sale, popup = true) {
        return openRoute('sale-analysis-rural', { saleId: sale.saleId }, popup);
    }

    async function openResidentialAnalysis(sale, popup = true) {
        return openRoute('sale-analysis-residential', { saleId: sale.saleId }, popup);
    }

    async function openRoute(routeName, params, popup = true) {
        if (popup) {
            const url =  router.resolve({
                name: routeName,
                params: params,
            }).href;

            const popupWindow = window.open(url, 'SaleAnalysis', 'scrollbars=yes,resizable=yes,height=800,width=1366');
            popupWindow.focus();
            return true;
        }

        await router.push({
            name: routeName,
            params: params,
        });
        return true;
    }

    return {
        tryOpenAnalysis,
        tryOpenAnalysisById,
        getAnalysisCategory
    };
}

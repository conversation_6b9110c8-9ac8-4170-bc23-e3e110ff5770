import { useRoute, useRouter } from 'vue-router/composables';
import { computed, onMounted, watch } from 'vue';
import { filterNonNullValues } from '@/utils/FormatUtils';

/**
 * Syncs a Ref object with query parameters
 *
 * @param query - A Ref of an object - must be an object and include all possible keys
 * @param parser - Optional - a function for parsing query params e.g casting strings back to numbers
 * @returns {*}
 */
export function useRouterQuery(query, parser = defaultParser) {
    const route = useRoute();
    const router = useRouter();
    const formattedQuery = computed(() => filterNonNullValues({ ...route.query, ...parser(route.query) }))

    watch(() => route.query, (newQuery) => {
        Object.assign(query.value, formattedQuery.value);
    }, {
        immediate: true,
    });

    watch(() => query, async (newQuery) => {
        await router.replace({ query: filterNonNullValues(newQuery.value) });
    }, {
        deep: true,
    });

    onMounted(() => {
        Object.assign(query.value, formattedQuery.value);
    });

    return query;
}

function defaultParser(query) {
    return query;
}

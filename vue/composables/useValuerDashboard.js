import { ref, computed, watch } from 'vue';
import _ from 'lodash';
import { store } from '@/DataStore';
import { getPresetDatePeriod, isPreviousFinancialYear, getTarget, getPeriodLength } from '../components/dashboard/workUnit/utils.js';

const BUILDING_CONSENTS_ACTIVITY = 1;
const SUBDIVISIONS_ACTIVITY = 2;
const ROLL_MAINTENANCE_OBJ_ACTIVITY = 3;
const REVALUATION_OBJ_ACTIVITY = 4;
const REVALUATION_ACTIVITY = 5;
const SALES_CLASSIFICATION_ACTIVITY = 6;
const TLA_ENQUIRY_ACTIVITY = 8;
const TRAINING_ACTIVITY = 9;
const MARKET_VALUATION_ACTIVITY = 1;
const DESKTOP_ESTIMATE_ACTIVITY = 2;
const COUNTER_SIGN_ACTIVITY = 3;
const EQC_ACTIVITY = 4;
const ASSET_ACTIVITY = 5;
const URGENT_ROLL_MAINT_ACTIVITY = 6;
const MISCELLANEOUS_ACTIVITY = 7;
const DAY_HOURS = 8;

/*
    const chartColors = {
       1: 'rgba(82, 144, 219, 1)', // building consent
       2: '#0E256A', // subdivision
       3: '#EA6A0F', // maintenance objections
       4: '#D2362B', // revision objections
       5: '#37BA26', // revaluation
       6: '#FF990F', // sales classification
       8: '#BAD1EB', // tla enquiry
       9: '#585858', // training
    };
*/

const datePeriods = ref([
    { code: 'CurrentMonth', description: 'Current Month' },
    { code: 'PreviousMonth', description: 'Previous Month' },
    { code: 'CurrentQuarter', description: 'Current Quarter' },
    { code: 'LastFinancialQuarter', description: 'Last Financial Quarter'},
    { code: 'CurrentFinancialYear', description: 'Current Financial Year' },
    { code: 'LastFinancialYear', description: 'Last Financial Year' },
    { code: 'CustomRange', description: 'Custom Range' },
]);
const selectedDateFilter = ref(null);
const dashboardUser = ref(null);
const selectedDateRange = ref(null);
const customRangeStatus = ref(null);

const errorMessage = ref(null);
const selectedTAs = ref([]);

const currentData = ref([]);
const consultancySummaryData = ref([]);
const consultancyHrs = ref([]);
const valuerRevenueTarget = ref(0);
const showProgressBar = ref(false);
const expandRating = ref(getExpandFlag('isExpandRating'));
const expandConsultancy = ref(getExpandFlag('isExpandConsultancy'));
const loading = ref(false);

const userData = computed(() => store.state.userData);
const classifications = computed(() => store.state.classifications.classifications);
const workUnitValuers = computed(() => classifications.value?.WorkUnitValuer);
const workUnitValuer = computed(() => workUnitValuers.value?.find(valuer=> valuer.ntUserName.toLowerCase() === dashboardUser.value.ntUsername.toLowerCase()));
const weeklySetHours = computed(() => workUnitValuer.value?.weeklySetHours);
const revenueRoleTargetList = computed(() => classifications.value?.RevenueRoleTarget);
const chargeableHours = computed(() => chargeableHrsBarData.value.reduce((a, item) => a + item.data, 0));
const totalHours = computed(() => trainingHours.value + chargeableHours.value);
const chargeableHourRate = computed(() => Math.round(calculateRate(chargeableHours.value, totalHours.value) * 100));
const percentTrainingHrs = computed(() => Math.round(calculateRate(trainingHours.value, totalHours.value) * 100));

const actualRevenue = computed(() => Math.round(revenueBarData.value.reduce((a, item) => a + item.data, 0)));
const currentDataFiltered = computed(() =>
    currentData.value?.filter(x =>
        selectedTAs.value.map(y => y.id).includes(x.rating_authority_id)
        || selectedTAs.value.length === 0
    )
);

const showAnalysisData = computed(() => {
    if (dashboardUser?.value && !dashboardUser.value.ntUsername?.startsWith('QVNZ')) {
        return false;
    }

    if (dashboardUser?.value !== undefined && dashboardUser?.value !== null) {
        return userData.value.isReportingManager || (userData.value.userName.toLowerCase() === dashboardUser.value.ntUsername.split('\\')[1].toLowerCase());
    }
    return false;
});

const ratingData = computed(() => [
    {
        title: 'Building Consents',
        color: 'mediumblue',
        icon: 'assignment_turned_in',
        stat: Math.round(buildingConsentCost.value),
        hours: buildingConsentHrs.value,
        count: buildingConsents.value,
        ratePerDay: calculateRate(buildingConsents.value, buildingConsentDays.value),
        calculationBasedOnHrs: false,
    },
    {
        title: 'Subdivisions',
        color: 'darkblue',
        icon: 'foundation',
        stat: Math.round(subdivisionCost.value),
        hours: subdivisionHrs.value,
        count: subdivisions.value,
        ratePerDay: calculateRate(subdivisions.value, subdivisionDays.value),
        calculationBasedOnHrs: false,
    },
    {
        title: 'Revision Objections',
        color: 'red',
        icon: 'location_away',
        stat: Math.round(revaluationObjectionCost.value),
        hours: revaluationObjectionHrs.value,
        count: revaluationObjections.value,
        ratePerDay: calculateRate(revaluationObjections.value, revaluationObjectionDays.value),
        calculationBasedOnHrs: false,
    },
    {
        title: 'Maint Objections',
        color: 'darkorange',
        icon: 'release_alert',
        stat: Math.round(rollMaintenanceObjectionCost.value),
        hours: rollMaintenanceObjectionHrs.value,
        count: rollMaintenanceObjections.value,
        ratePerDay: calculateRate(rollMaintenanceObjections.value, rollMaintenanceObjectionDays.value),
        calculationBasedOnHrs: false,
    },
    {
        title: 'Sales Classification',
        color: 'orange',
        icon: 'in_home_mode',
        stat: Math.round(salesClassificationCost.value),
        hours: null,
        count: salesClassificationHours.value,
        ratePerDay: null,
        calculationBasedOnHrs: true,
    },
    {
        title: 'Revaluation',
        color: 'green',
        icon: 'currency_exchange',
        stat: Math.round(revaluationCost.value),
        hours: null,
        count: revaluationHours.value,
        ratePerDay: null,
        calculationBasedOnHrs: true,
    },
    {
        title: 'TLA Enquiry',
        color: 'grey',
        icon: 'account_balance',
        stat: Math.round(tlaEnquiryCost.value),
        hours: null,
        count: tlaEnquiryHours.value,
        ratePerDay: null,
        calculationBasedOnHrs: true,
    },
]);

const consultancyData = computed(() => [
    {
      title: 'Market Valuation',
      color: 'skyBlue',
      icon: 'request_quote',
      hours: marketValuationHrs.value,
      stat:  marketValuationCost.value,
      count: marketValuation.value,
      ratePerDay: marketValuationRatePerDay.value,
      ratePerActivity: marketValuationRatePerActivity.value,
    },
    {
      title: 'Desktop Estimate',
      color: 'deepBlue',
      icon: 'desktop_mac',
      hours: desktopEstimateHrs.value,
      stat:  desktopEstimateCost.value,
      count: desktopEstimate.value,
      ratePerDay: desktopEstimateRatePerDay.value,
      ratePerActivity: desktopEstimateRatePerActivity.value,
    },
    {
      title: 'Counter Sign',
      color: 'maroon',
      icon: 'verified',
      hours: counterSignHrs.value,
      stat:  counterSignCost.value,
      count: counterSign.value,
      ratePerDay: counterSignRatePerDay.value,
      ratePerActivity: counterSignRatePerActivity.value,
    },
    {
      title: 'EQC',
      color: 'fadedPink',
      icon: 'earthquake',
      hours: eqcHrs.value,
      stat:  eqcCost.value,
      count: eqc.value,
      ratePerDay: eqcRatePerDay.value,
      ratePerActivity: eqcRatePerActivity.value,
    },
    {
      title: 'Asset',
      color: 'darkerOrange',
      icon: 'web_asset',
      hours: assetHrs.value,
      stat:  assetCost.value,
      count: asset.value,
      ratePerDay: assetRatePerDay.value,
      ratePerActivity: assetRatePerActivity.value,
    },
    {
      title: 'Urgent Roll Maint',
      color: 'darkerGreen',
      icon: 'wifi_home',
      hours: urgentRollMainHrs.value,
      stat:  urgentRollMainCost.value,
      count: urgentRollMain.value,
      ratePerDay: urgentRollMainRatePerDay.value,
      ratePerActivity: urgentRollMainRatePerActivity.value,
    },
    {
      title: 'Miscellaneous',
      color: 'darkGrey',
      icon: 'other_admission',
      hours: miscellaneousHrs.value,
      stat:  miscellaneousCost.value,
      count: miscellaneous.value,
      ratePerDay: miscellaneousRatePerDay.value,
      ratePerActivity: miscellaneousRatePerActivity.value,
    }
]);

const revenueBarData = computed(() => [
    {
      title: 'Building Consents',
      color: '#5290db',
      data:  buildingConsentCost.value,
    },
    {
      title: 'Subdivisions',
      color: '#0E256A',
      data:  subdivisionCost.value,
    },
    {
      title: 'Revision Objections',
      color: '#D2362B',
      data:  revaluationObjectionCost.value,
    },
    {
      title: 'Maint Objections',
      color: '#EA6A0F',
      data:  rollMaintenanceObjectionCost.value,
    },
    {
      title: 'Sales Classification',
      color: '#FF990F',
      data:  salesClassificationCost.value,
    },
    {
      title: 'Revaluation',
      color: '#37BA26',
      data:  revaluationCost.value,
    },
    {
      title: 'TLA Enquiry',
      color: '#585858',
      data:  tlaEnquiryCost.value,
    },
    {
      title: 'Market Valuation',
      color: '#30b9f2',
      data:  marketValuationCost.value
    },
    {
      title: 'Desktop Estimate',
      color: '#000c5d9e',
      data:  desktopEstimateCost.value,
    },
    {
      title: 'Counter Sign',
      color: '#800000',
      data:  counterSignCost.value,
    },
    {
      title: 'EQC',
      color: '#e5896a',
      data:  eqcCost.value,
    },
    {
      title: 'Asset',
      color: '#cd882a',
      data:  assetCost.value,
    },
    {
      title: 'Urgent Roll Maint',
      color: '#039d00',
      data:  urgentRollMainCost.value,
    },
    {
      title: 'Miscellaneous',
      color: '#232323',
      data:  miscellaneousCost.value,
    }
]);

const chargeableHrsBarData = computed(() => [
    {
      title: 'Building Consents',
      color: '#5290db',
      data:  buildingConsentHrs.value,
    },
    {
      title: 'Subdivisions',
      color: '#0E256A',
      data:  subdivisionHrs.value,
    },
    {
      title: 'Revision Objections',
      color: '#D2362B',
      data:  revaluationObjectionHrs.value,
    },
    {
      title: 'Maint Objections',
      color: '#EA6A0F',
      data:  rollMaintenanceObjectionHrs.value,
    },
    {
      title: 'Sales Classification',
      color: '#FF990F',
      data:  salesClassificationHours.value,
    },
    {
      title: 'Revaluation',
      color: '#37BA26',
      data:  revaluationHours.value,
    },
    {
      title: 'TLA Enquiry',
      color: '#585858',
      data:  tlaEnquiryHours.value,
    },
    {
      title: 'Market Valuation',
      color: '#30b9f2',
      data:  marketValuationHrs.value
    },
    {
      title: 'Desktop Estimate',
      color: '#000c5d9e',
      data:  desktopEstimateHrs.value,
    },
    {
      title: 'Counter Sign',
      color: '#800000',
      data:  counterSignHrs.value,
    },
    {
      title: 'EQC',
      color: '#e5896a',
      data:  eqcHrs.value,
    },
    {
      title: 'Asset',
      color: '#cd882a',
      data:  assetHrs.value,
    },
    {
      title: 'Urgent Roll Maint',
      color: '#039d00',
      data:  urgentRollMainHrs.value,
    },
    {
      title: 'Miscellaneous',
      color: '#232323',
      data:  miscellaneousHrs.value,
    }
]);

watch(expandRating, async (val) => {
  if (val) {
    await loadDashboardDataByType(selectedDateRange.value, 'summary');
  }
});

watch(expandConsultancy, async (val) => {
  if (val) {
    await loadDashboardDataByType(selectedDateRange.value, 'consultancy');
  }
});

const buildingConsents = getSummaryData(BUILDING_CONSENTS_ACTIVITY, 'activity_count');
const buildingConsentDays = getSummaryData(BUILDING_CONSENTS_ACTIVITY, 'timesheet_hours', true);
const buildingConsentHrs = getSummaryData(BUILDING_CONSENTS_ACTIVITY, 'timesheet_hours');
const buildingConsentCost = getSummaryData(BUILDING_CONSENTS_ACTIVITY, 'total_cost');
const subdivisions = getSummaryData(SUBDIVISIONS_ACTIVITY, 'activity_count');
const subdivisionDays = getSummaryData(SUBDIVISIONS_ACTIVITY, 'timesheet_hours', true);
const subdivisionHrs = getSummaryData(SUBDIVISIONS_ACTIVITY, 'timesheet_hours');
const subdivisionCost = getSummaryData(SUBDIVISIONS_ACTIVITY, 'total_cost');
const rollMaintenanceObjections = getSummaryData(ROLL_MAINTENANCE_OBJ_ACTIVITY, 'activity_count');
const rollMaintenanceObjectionDays = getSummaryData(ROLL_MAINTENANCE_OBJ_ACTIVITY, 'timesheet_hours', true);
const rollMaintenanceObjectionHrs = getSummaryData(ROLL_MAINTENANCE_OBJ_ACTIVITY, 'timesheet_hours');
const rollMaintenanceObjectionCost = getSummaryData(ROLL_MAINTENANCE_OBJ_ACTIVITY, 'total_cost');
const revaluationObjections = getSummaryData(REVALUATION_OBJ_ACTIVITY, 'activity_count');
const revaluationObjectionDays = getSummaryData(REVALUATION_OBJ_ACTIVITY, 'timesheet_hours', true);
const revaluationObjectionHrs = getSummaryData(REVALUATION_OBJ_ACTIVITY, 'timesheet_hours');
const revaluationObjectionCost = getSummaryData(REVALUATION_OBJ_ACTIVITY, 'total_cost');
const revaluationHours = getSummaryData(REVALUATION_ACTIVITY, 'timesheet_hours');
const revaluationCost = getSummaryData(REVALUATION_ACTIVITY, 'total_cost');
const salesClassificationHours = getSummaryData(SALES_CLASSIFICATION_ACTIVITY, 'timesheet_hours');
const salesClassificationCost = getSummaryData(SALES_CLASSIFICATION_ACTIVITY, 'total_cost');
const tlaEnquiryHours = getSummaryData(TLA_ENQUIRY_ACTIVITY, 'timesheet_hours');
const tlaEnquiryCost = getSummaryData(TLA_ENQUIRY_ACTIVITY, 'total_cost');
const trainingHours = getSummaryData(TRAINING_ACTIVITY, 'timesheet_hours');

const marketValuation = getConsultancySummaryData(MARKET_VALUATION_ACTIVITY, 'activity_count');
const marketValuationHrs = getConsultancyHrs(MARKET_VALUATION_ACTIVITY);
const marketValuationCost = getConsultancySummaryData(MARKET_VALUATION_ACTIVITY, 'total_valuer_split');
const marketValuationRatePerDay = getConsultancyHrs(MARKET_VALUATION_ACTIVITY, true);
const marketValuationRatePerActivity = getConsultancySummaryData(MARKET_VALUATION_ACTIVITY, 'total_valuer_split', true);
const desktopEstimate = getConsultancySummaryData(DESKTOP_ESTIMATE_ACTIVITY, 'activity_count');
const desktopEstimateHrs = getConsultancyHrs(DESKTOP_ESTIMATE_ACTIVITY);
const desktopEstimateCost = getConsultancySummaryData(DESKTOP_ESTIMATE_ACTIVITY, 'total_valuer_split');
const desktopEstimateRatePerDay = getConsultancyHrs(DESKTOP_ESTIMATE_ACTIVITY, true);
const desktopEstimateRatePerActivity = getConsultancySummaryData(DESKTOP_ESTIMATE_ACTIVITY, 'total_valuer_split', true);
const counterSign = getConsultancySummaryData(COUNTER_SIGN_ACTIVITY, 'counter_signed');
const counterSignHrs = getConsultancyHrs(COUNTER_SIGN_ACTIVITY);
const counterSignCost = getConsultancySummaryData(COUNTER_SIGN_ACTIVITY, 'total_valuer_split');
const counterSignRatePerDay = getConsultancyHrs(COUNTER_SIGN_ACTIVITY, true);
const counterSignRatePerActivity = getConsultancySummaryData(COUNTER_SIGN_ACTIVITY, 'total_valuer_split', true);
const eqc = getConsultancySummaryData(EQC_ACTIVITY, 'activity_count');
const eqcHrs = getConsultancyHrs(EQC_ACTIVITY);
const eqcCost = getConsultancySummaryData(EQC_ACTIVITY, 'total_valuer_split');
const eqcRatePerDay = getConsultancyHrs(EQC_ACTIVITY, true);
const eqcRatePerActivity = getConsultancySummaryData(EQC_ACTIVITY, 'total_valuer_split', true);
const asset = getConsultancySummaryData(ASSET_ACTIVITY, 'activity_count');
const assetHrs = getConsultancyHrs(ASSET_ACTIVITY);
const assetCost = getConsultancySummaryData(ASSET_ACTIVITY, 'total_valuer_split');
const assetRatePerDay = getConsultancyHrs(ASSET_ACTIVITY, true);
const assetRatePerActivity = getConsultancySummaryData(ASSET_ACTIVITY, 'total_valuer_split', true);
const urgentRollMain = getConsultancySummaryData(URGENT_ROLL_MAINT_ACTIVITY, 'activity_count');
const urgentRollMainHrs = getConsultancyHrs(URGENT_ROLL_MAINT_ACTIVITY);
const urgentRollMainCost = getConsultancySummaryData(URGENT_ROLL_MAINT_ACTIVITY, 'total_valuer_split');
const urgentRollMainRatePerDay = getConsultancyHrs(URGENT_ROLL_MAINT_ACTIVITY, true);
const urgentRollMainRatePerActivity = getConsultancySummaryData(URGENT_ROLL_MAINT_ACTIVITY, 'total_valuer_split', true);
const miscellaneous = getConsultancySummaryData(MISCELLANEOUS_ACTIVITY, 'activity_count');
const miscellaneousHrs = getConsultancyHrs(MISCELLANEOUS_ACTIVITY);
const miscellaneousCost = getConsultancySummaryData(MISCELLANEOUS_ACTIVITY, 'total_valuer_split');
const miscellaneousRatePerDay = getConsultancyHrs(MISCELLANEOUS_ACTIVITY, true);
const miscellaneousRatePerActivity = getConsultancySummaryData(MISCELLANEOUS_ACTIVITY, 'total_valuer_split', true);

export default function useValuerDashboard() {
    return {
        loading,
        datePeriods,
        userData,
        ratingData,
        consultancyData,
        revenueBarData,
        chargeableHrsBarData,
        totalHours,
        showAnalysisData,
        trainingHours,
        currentData,
        valuerRevenueTarget,
        actualRevenue,
        percentTrainingHrs,
        chargeableHourRate,
        chargeableHours,
        expandRating,
        expandConsultancy,
        showProgressBar,
        selectedTAs,
        loadDashboardData,
        setCustomRangeStatus,
        getRevenueTarget,
        toggleExpand,
    };
}

async function loadDashboardData(valuer, dateFilter, dateRange, status, isPreset = true, type = 'all') {
  const datePeriod = isPreset? getPresetDatePeriod(dateFilter.code) : dateRange;
  await setValuerAndDateOptions(valuer, dateFilter, datePeriod);
  await setCustomRangeStatus(status);
  await loadDashboardDataByType(type);
}

async function setValuerAndDateOptions(valuer, dateFilter, dateRange) {
  dashboardUser.value = valuer;
  selectedDateFilter.value = dateFilter;
  selectedDateRange.value = dateRange;
}

async function setCustomRangeStatus(status) {
  customRangeStatus.value = status;
}

async function loadDashboardDataByType(type = 'all') {
    loading.value = true;
    if (type === 'all'|| (expandRating.value && type === 'summary')) {
        currentData.value = await loadData();
    }
    if (type === 'all'|| (expandConsultancy.value && type === 'consultancy')) {
        consultancySummaryData.value = await loadData('consultancy');
        consultancyHrs.value = await loadData('consultancyHrs');
    }
    getRevenueTarget();
    loading.value = false;
}


async function loadData(dataType = 'summary') {
    if (dashboardUser.value?.id && dashboardUser.value?.id !== 'Unassigned') {
        let response = null;
        if (dataType === 'summary') {
            response = await getWorkUnitSummary(dashboardUser.value.ntUsername, selectedDateRange.value.from, selectedDateRange.value.to);
        }
        if (dataType === 'consultancy') {
            response = await getConsultancyValuerSummary(dashboardUser.value.ntUsername, selectedDateRange.value.from, selectedDateRange.value.to);
        }
        if (dataType === 'consultancyHrs') {
          response = await getConsultancyValuerHrs(dashboardUser.value.ntUsername, selectedDateRange.value.from, selectedDateRange.value.to);
        }
        if (response.data && response.data.constructor === Array) {
            return response.data;
        }
    }
    return [];
}


async function getWorkUnitSummary(username, startDate, endDate) {
    try {
        const { url } = jsRoutes.controllers.StatsController.getWorkUnitValuerSummary(username, startDate, endDate);
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        return await res.json();
    } catch (error) {
        const message = 'Error calling stats api';
        console.error(message, error);
        errorMessage.value = message;
    }
}

async function getConsultancyValuerSummary(username, startDate, endDate) {
    try {
        const { url } = jsRoutes.controllers.StatsController.getConsultancyValuerSummary(username, startDate, endDate);
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        return await res.json();
    } catch (error) {
        const message = 'Error calling stats api';
        console.error(message, error);
    }
}

async function getConsultancyValuerHrs(username, startDate, endDate) {
  try {
      const { url } = jsRoutes.controllers.StatsController.getConsultancyValuerHrs(username, startDate, endDate);
      const res = await fetch(`${url}`, {
          method: 'GET',
          headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest',
          },
      });
      return await res.json();
  } catch (error) {
      const message = 'Error calling stats api';
      console.error(message, error);
  }
}

function getSummaryData(activityId, propertyName, shouldReturnDays = false) {
    return computed(() => {
        const data = currentDataFiltered.value?.filter((item) => item.activity_id === activityId);
        let total = data?.reduce((a, item) => a + item[propertyName], 0);
        if (propertyName === 'timesheet_hours') {
            const totalsMap = new Map();
            data?.forEach((item) => {
                const key = item.rating_authority_id + '-' + item.month;
                if (!totalsMap.has(key)) {
                    totalsMap.set(key, item[propertyName]);
                }
            });
            total = Array.from(totalsMap.values()).reduce((acc, curr) => acc + curr, 0);
            const activityShouldHaveDailyRate = activityId <= REVALUATION_OBJ_ACTIVITY;
            if (activityShouldHaveDailyRate && shouldReturnDays) {
                return total / DAY_HOURS;
            }
        }
        if (propertyName === 'total_cost' && activityId <= REVALUATION_OBJ_ACTIVITY) {
            total = data?.filter((item) => item.activity_count > 0).reduce((a, item) => a + item[propertyName], 0);
        }
        return total;
    });
}

function getRevenueTarget() {
    const datePeriod = selectedDateRange.value;
    const isPrevFinancialYear = isPreviousFinancialYear(selectedDateFilter.value?.code, datePeriod?.from);
    const target = getTarget(isPrevFinancialYear, revenueRoleTargetList.value, dashboardUser.value);
    const periodLength = getPeriodLength(selectedDateFilter.value?.code);
    const FTE = weeklySetHours.value ? weeklySetHours.value / 40 : 1;
    if(weeklySetHours.value == null && dashboardUser.value != null) {
        console.log("Error: weekly set hours for work unit valuer has returned null or undefined");
    }
    valuerRevenueTarget.value = target * periodLength * FTE;
    showProgressBar.value = valuerRevenueTarget.value > 0;
}

function calculateRate(total, days) {
    if (!days) {
        return 0;
    }
    return Math.round((total / days) * 100) / 100;
}

function getConsultancySummaryData(valuation_type, propertyName, isRatePerActivity = false) {
    return computed(() => {
        const data = valuation_type === 3
            ? consultancySummaryData.value?.filter(item => item.counter_signed > 0)
            : consultancySummaryData.value?.filter(item => parseInt(item.valuation_type) === valuation_type && item.counter_signed === 0);
        const activity_count = data?.length;
        if (propertyName === 'activity_count') {
            return activity_count;
        }
        let total = data?.reduce((a, item) => a + item[propertyName], 0) || 0;
        if (isRatePerActivity) {
            const ratePerActivity = activity_count > 0 ? total / activity_count : 0;
            return !_.isNaN(ratePerActivity) ? ratePerActivity : 0;
        }
        return total;
    });
}

function getConsultancyHrs(valuation_type, isRatePerDay = false) {
  return computed(() => {
    const data = valuation_type === 3
        ?   consultancyHrs.value?.filter(item => item.counter_signed > 0)
        :   consultancyHrs.value?.filter(item => parseInt(item.valuation_type) === valuation_type && item.counter_signed === 0);
    const total = data?.reduce((a, item) => a + item.charged_hours, 0) || 0;
    if (isRatePerDay) {
      const activity_count = getConsultancySummaryData(valuation_type, 'activity_count');
      const ratePerDay = total > 0 ? activity_count.value / (total / 8) : 0;
      return !_.isNaN(ratePerDay) ? ratePerDay : 0;
    }
    return total;
  });
}

function getExpandFlag(type) {
    const flag = localStorage.getItem(type);
    return flag ? flag === 'true' : true;
}

function toggleExpand(type) {
    if (type === 'rating') {
        expandRating.value = !expandRating.value;
        localStorage.setItem('isExpandRating', expandRating.value);
    } else {
        expandConsultancy.value = !expandConsultancy.value;
        localStorage.setItem('isExpandConsultancy', expandConsultancy.value);
    }
}

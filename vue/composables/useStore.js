import { store } from '@/DataStore';
import { computed, toRefs, unref } from 'vue';

export function useStore(namespace) {
    const state = toRefs(store.state[namespace]);
    const commit = (mutation, payload) => store.commit(`${namespace}/${mutation}`, payload);
    const dispatch = (action, payload) => store.dispatch(`${namespace}/${action}`, payload);

    const computedState = {};
    for (const key in state) {
        computedState[key] = computed({
            get: () => unref(state[key]),
            set: (value) => store.state[namespace][key] = value,
        });
    }

    const computedGetters = {};
    for (const key in store.getters) {
        if (key.startsWith(`${namespace}/`)) {
            computedGetters[key.replace(`${namespace}/`, '')] = computed(() => store.getters[key]);
        }
    }

    return {
        state: computedState,
        getters: computedGetters,
        commit,
        dispatch,
    };
}

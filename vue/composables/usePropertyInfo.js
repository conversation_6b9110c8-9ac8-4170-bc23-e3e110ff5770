import { ref } from 'vue';

let lastQpidRequested;
let lastRequestPromise;
const propertyInfo = ref();

export default function usePropertyInfo() {
    return {
        async getPropertyInfo(qpid) {
            if (!qpid) {
                throw new Error('qpid missing, getPropertyInfo()')
            }
            
            if (lastQpidRequested == qpid) {
                return lastRequestPromise;
            }

            lastQpidRequested = qpid;
            propertyInfo.value = null;
            lastRequestPromise = fetchPropertyInfo(qpid);
            propertyInfo.value = await lastRequestPromise;
            return lastRequestPromise;
        },
        async refreshPropertyInfo(qpid){
            if (!qpid) {
                throw new Error('qpid missing, getPropertyInfo()')
            }
            lastQpidRequested = qpid;
            propertyInfo.value = null;
            lastRequestPromise = fetchPropertyInfo(qpid);
            propertyInfo.value = await lastRequestPromise;
            return lastRequestPromise;
        },
            propertyInfo,
    }
}

async function fetchPropertyInfo(qpid) {
    const getPropertyInfoUrl = jsRoutes.controllers.PropertyMasterData.getPropertyInfo(qpid).url;
    const response = await fetch(getPropertyInfoUrl);
    const propertyInfo = await response.json();
    propertyInfo.hasNoCurrentWorksheet = propertyInfo.hasCommercialWorksheet && propertyInfo.hasRuralWorksheet;
    return propertyInfo;
}
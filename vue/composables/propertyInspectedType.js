export async function getPropertyInspectedTypes() {
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getPicklistValue('PropertyInspectedType');
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        const result = await res.json();
        const cleanedData = result.result.PropertyInspectedType
            .map(item => ({
                ...item,
                code: item.code.trim()
            }))
            .sort((a, b) => a.description.localeCompare(b.description));
        return cleanedData;
    }
    catch (error) {
        const message = 'Error calling controller to get Property Inspected Types';
        console.error(message, error);
    }
}
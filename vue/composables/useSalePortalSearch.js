import { computed, ref } from 'vue';
import { useRouterQuery } from '@/composables/useRouterQuery';
import { store } from '@/DataStore';
import { searchUnlinkedNotices } from '@/services/ApiSalesController';
import { filterNonNullValues } from '@/utils/FormatUtils';
import Vue from 'vue';

export function useSalePortalSearch(opts = {}) {

    const defaultOptions = {
        limit: 100,
    };
    const options = Object.assign(defaultOptions, opts);
    const defaultFilter = {
    saleDateFrom: null,
        saleDateTo: null,
        jobNumber: null,
        page: 1,
    }

    const filter = ref({
        ...defaultFilter
    });

    useRouterQuery(filter, (query) => {
        return {
            jobNumber: parseInt(query.jobNumber) || null,
            page: Math.max(parseInt(query.page), 1) || 1,
        };
    });

    async function clearFilter() {
        filter.value = {...defaultFilter};
    }

    async function search() {
        const payload = filterNonNullValues({
            ratingAuthorityIds: [],
            limit: options.limit,
            offset: (filter.value.page - 1) * options.limit,
            jobId: filter.value.jobNumber,
            ...filter.value,
        });

        return await searchUnlinkedNotices(payload);
    }

    async function searchTa(ratingAuthorities, allTas = false) {
        const payload = filterNonNullValues({
            ratingAuthorityIds: ratingAuthorities,
            allTas,
            limit: options.limit,
            offset: (filter.value.page - 1) * options.limit,
            jobId: filter.value.jobNumber,
            ...filter.value,
        });

        return await searchUnlinkedNotices(payload);
    }

    return {
        filter,
        clearFilter,
        search,
        searchTa
    };
}

import { ref, watch, computed } from 'vue';
import * as controller from '@/services/SalesSearchController';
import { searchProperties } from '@/services/PropertyController';
import useClassifications from '../utils/classifications.js';
import useSaleStatus from '../utils/saleStatus';
import { store } from '../DataStore';

const { classificationOptions } = useClassifications();
const { statusOptions } = useSaleStatus();

const isInternalUser = computed(() => store.state.userData.isInternalUser);
const isAdminUser = computed(() => store.state.userData.isAdminUser);
const isCustomerCare = computed(() => store.state.userData.isCustomerCare);
const classifications = computed(() => store.state.saleClassifications.classifications);
const isTAUser = computed(() => !isInternalUser && store.state.userData.isTAUser);
const externalObjectionAccess = computed(()=> !isInternalUser && store.state.userData.externalObjectionAccess);
const taCodes = computed(() => isTAUser.value ? [store.state.userData.userTACode].map(item => parseInt(item, 10)) : store.state.taCodes.taCodes.map(item => parseInt(item, 10)));
const salesProcessingStatusOptions = computed(() => classifications?.value?.SalesProcessingStatus ?? []);
const salesSourceOptions = computed(() => classifications?.value?.SalesProcessingSource ?? []);
const SALES_TO_PROCESS_CRITERIA= 'SALES_TO_PROCESS';
const SALES_INSPECTION_CRITERIA = 'SALES_INSPECTION';
const defaultQueryParams = {
    limit: 100,
    offset: 0,
    sortDesc: true,
    sortBy: 'SALE_DATE',
};

const defaultSearchCriteria = {
    fromSaleDate: null,
    toSaleDate : null,
    fromSaleInputDate : null,
    toSaleInputDate : null,
    categories : null,
    saleSourceId : null,
    saleStatusId : null,
    fromGSP : null,
    toGSP : null,
    excludeFromHpiRtv : false
};

const saleClassificationOptions = ref(classificationOptions?.map((e) => ({ ...e })) || []);
const saleStatusOptions = ref(statusOptions?.map((e) => ({ ...e, checked: e.value === 1 })) || []);
const queryParams = ref(defaultQueryParams);
const results = ref([]);
const validationSet = ref([]);
const searching = ref(false);
const page = ref((queryParams.value.offset / queryParams.value.limit) + 1);
const shouldshowExpandedView = ref(false);
const searchCriteria = ref({});
const selectedClassifications = ref([]);
const selectedSaleStatus = ref([]);
const selectedSource = ref([]);
const selectedSaleProcessingStatus = ref([]);

watch(selectedClassifications, (value) => handleClassificationChange(value));
watch(selectedSaleStatus, (value) => handleSaleStatusChange(value));
watch(selectedSource, (value) => handleSource(value));
watch(selectedSaleProcessingStatus, (value) => handleSaleProcessingStatus(value));

function resetSearchCriteria(searchType) {
    searchCriteria.value = getSearchCriteria(searchType);
    queryParams.value = defaultQueryParams;
    results.value = [];
     if (searchType === SALES_TO_PROCESS_CRITERIA) {
        saleStatusOptions.value = statusOptions?.map((option) => ({ ...option, checked: option.value === 1 || option.value === 2 })) || [];
    } else if (searchType === SALES_INSPECTION_CRITERIA) {
        saleStatusOptions.value = statusOptions?.map((option) => ({ ...option, checked: option.value === 1 })) || [];
    }
    saleClassificationOptions.value = (searchType === SALES_TO_PROCESS_CRITERIA) ?
            classificationOptions?.map((e) => ({ ...e, checked: !['marketSales', 'salesToCheck', 'saleType', 'saleTenure', 'priceValueRelationship'].includes(e.value) })) :
            classificationOptions?.map((e) => ({ ...e, checked:  ['0-S','0-M','1-1', '2-2'].includes(e.value) }));
    selectedSaleProcessingStatus.value = salesProcessingStatusOptions?.value.filter(opt => searchCriteria.value.saleProcessingStatusId.includes(opt.id));
    searchCriteria.value.taCode = taCodes?.value;
    selectedSource.value = [];
    handleClassificationChange(saleClassificationOptions.value.filter((e) => e.checked));
    handleSaleStatusChange(saleStatusOptions.value.filter((e)=> e.checked));
}

function getSearchCriteria(searchType) {
    let criteria;
    if(searchType === SALES_TO_PROCESS_CRITERIA) {
        criteria =  {
            ...structuredClone(defaultSearchCriteria),
            saleType: ['M', 'S', 'P'],
            saleTenure: [1,2,3,4],
            priceValueRelationship: [1,2,3],
            saleProcessingStatusId: [1],
        }
    }
    else {
        criteria = {
            ...structuredClone(defaultSearchCriteria),
            saleType: ['M', 'S'],
            saleTenure: [1],
            priceValueRelationship: [2],
            saleProcessingStatusId: [2,3],
            saleInspectionConsentCount: 0,
        }
    }
    return structuredClone(criteria);
}

function handleClassificationChange(classifications) {
    searchCriteria.value.saleType = classifications.filter(c => c.groupId === 0).map(c => c.value.split('-')[1]);
    searchCriteria.value.saleTenure = classifications.filter(c => c.groupId === 1).map(c => c.value.split('-')[1]);
    searchCriteria.value.priceValueRelationship = classifications.filter(c => c.groupId === 2).map(c => c.value.split('-')[1]);
}

function handleSaleStatusChange(saleStatus) {
    searchCriteria.value.saleStatusId = saleStatus.map(c => c.value);
}

function handleSource(sourceId) {
    searchCriteria.value.saleSourceId = sourceId.map(c => c.id);
}

function handleSaleProcessingStatus(saleProcessingStatus) {
    searchCriteria.value.saleProcessingStatusId = saleProcessingStatus.map(c => c.id);
}

export function useSaleSearch() {

    async function search(shouldResetPage = false) {
        if (shouldResetPage) {
            queryParams.value.offset = 0;
            page.value = 1;
        }
        try {
            searching.value = true;
            searchCriteria.value.taCode = taCodes?.value;
            const response = await controller.search({searchCriteria: searchCriteria.value, queryParams: queryParams.value});
            const enrichedResponse = response.total > 0 ? await addPropertyUUID(response) : response;
            results.value = enrichedResponse;
            validationSet.value = enrichedResponse.validationSet ?? null;
        } catch (e) {
            console.error(e);
        } finally {
            searching.value = false;
        }
    }

    async function addPropertyUUID(result) {
        try {
            const { resultList } = await searchProperties({qupids: result.sales.map(sale => sale.qpid),max : 100});
            const properties = resultList.map(result => result.property);
            properties.forEach((property) => {
                result.sales
                    .filter(sale => sale.qpid == property.qupid)
                    .forEach((sale) => {
                        sale.propertyId = property.id;
                    });
            });
            return result;
        }
        catch (error) {
            console.error('ERR-SSD-003: Error calling addPropertyUUID', error);
        }
    }

    return {
        results,
        searching,
        searchCriteria,
        queryParams,
        salesProcessingStatusOptions,
        salesSourceOptions,
        validationSet,
        page,
        shouldshowExpandedView,
        saleClassificationOptions,
        saleStatusOptions,
        selectedSaleStatus,
        selectedClassifications,
        isAdminUser,
        isCustomerCare,
        selectedSource,
        selectedSaleProcessingStatus,
        SALES_TO_PROCESS_CRITERIA,
        SALES_INSPECTION_CRITERIA,
        search,
        resetSearchCriteria,
        addPropertyUUID,
    }
}

import { ref, onMounted } from 'vue';

const categoryTypeList = ref([])

export function useCategoryType() {
    onMounted(async () => {
        if (categoryTypeList.value.length === 0) {
            categoryTypeList.value = await getCategoryTypes()
        }}
    );

    return categoryTypeList;
}

export async function getCategoryTypes() {
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getPicklistValue('Category');
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        const result = await res.json();
        return result.result.Category;
    }
    catch (error) {
        const message = 'Error calling controller to get category types';
        console.error(message, error);
    }
}
import { ref, watch } from 'vue';
import { useTaList } from './taList';

const salesGroups = ref([]);
const taList = useTaList();

watch(taList, async () => {
    if (salesGroups.value.length === 0 && taList.value.length > 0) {
        salesGroups.value = await getSalesGroups();
    }
});

export function useSalesGroups() {
    return salesGroups;
}

export async function getSalesGroups() {
    try {
        const taCodes = getTACodesString(taList.value);
        const res = await fetch(jsRoutes.controllers.ReferenceData.displaySalesGroups(taCodes).url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        const result = await res.json();
        return result;
    }
    catch (error) {
        const message = 'Error calling controller to get Sales Groups';
        console.error(message, error);
    }

    return [];
}

function getTACodesString(inputTAList) {
    const paddedCodes = inputTAList.map(group => group.code.padStart(2, '0'));
    return paddedCodes.length > 0 ? paddedCodes.join(',') : '';
}

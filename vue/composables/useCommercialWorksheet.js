import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router/composables';
import _ from 'lodash';
import { store } from '@/DataStore';
import * as worksheetController from '@/services/WorksheetsController';
import * as rfcController from '@/services/ReasonForChangeController';
import * as propertyMasterDataController from '@/services/PropertyMasterDataController';
import * as referenceDataController from '@/services/ReferenceDataController';
import * as propertyDetailController from '@/services/PropertyDetailController';
import { roundToNearest } from '@/utils/CommonUtils';
import useModal from '@/composables/useModal';
import CommercialWorksheetValidationModal from '@/components/rollMaintenance/commercialWorksheet/CommercialWorksheetValidationModal.vue';
import ConfirmationModal from '@/components/rollMaintenance/commercialWorksheet/CommercialWorksheetConfirmationModal.vue';
import { validateWorksheet as validateWorksheetClient, validateReasonForChange } from '@/utils/ValidateCommercialWorksheet';
import { openBase64DataInNewTab } from '@/utils/QivsUtils';

let router;
let route;
const modal = useModal();

const WEEKS_PER_YEAR = 52;
const CV_ROUNDING_AMOUNT = 1000;
const LV_ROUNDING_AMOUNT = 1000;
const VI_SUMMATION = 'VI Summation';
const CV_INCOME = 'CV Income';
const SQUARE_METERS_IN_HECTARE = 10000;
const CAP_RATE_DEFAULT = -1;

const loaded = ref(false);
const saving = ref(false);
const property = ref({});
const qvProperty = ref({});
const qvPropertyZone = ref({});
const monarchCurrentPropertyDetails = ref({});
const propertyDetails = ref({
    actualEarthquakeRating: null,
    earthquakeRatingRange: null,
    earthquakeRatingAssessor: null,
    liquefaction: null,
    remedyYear: null,
    isRemediationRequired: false,
});
const worksheet = ref({});
const rfc = ref({});
const rfcClassifications = ref({});
const picklistValues = ref({});
const zoningOptions = ref([]);
const newLandRow = ref({ isValidLandArea: true });
const newImprovementRow = ref({
    suffix: suffixOptions?.value?.[0]?.id ?? null,
    description: null,
    worksheetImprovementId: null,
    area: null,
    carparks: null,
    rental: null,
    percentExcessLand: null,
    percentVacant: null,
    commercialWorksheetRowId: _.uniqueId('new_improvement_row_'),
});
const isRevision = ref(false);
const createRevision = ref(false);
const newActualRentalRow = ref({
    actualRentalId: null,
    rentalDate: null,
    grossRental: null,
    floorArea: null,
    rentalPerSqmt: null,
    actualRentalTypeId: null,
    tenant: null,
});
const earthquakeRatingRangeRef = ref(null);
const earthquakeRatingAssessorRef = ref(null);
const liquefactionRef = ref(null);
const proposedZoneRef = ref(null);
const qvCategoryRef = ref(null);
const commercialGroupingRef = ref(null);
const rentalDateErrors = ref([]);
const newRentalDateErrors = ref([]);

const isApportionmentCode5 = computed(() => worksheet.value.apportionmentId === 5);
const suffixOptions = computed(() => (isApportionmentCode5.value ? worksheet.value.childApportionments?.map(item => ({ id: item.suffix, description: item.suffix })) : null) ?? []);
const classifications = computed(() => store.state.classifications.classifications || {});
const userData = computed(() => store.state.userData || {});
const allLandRows = computed(() => isRevision.value || createRevision.value ? worksheet.value.landRows : [...worksheet.value.landRows, newLandRow.value]);
const allImprovementRows = computed(() => isRevision.value || createRevision.value ? worksheet.value.improvementRows : [...worksheet.value.improvementRows, newImprovementRow.value]);

// worksheet totals
const totalLandArea = computed(() => allLandRows.value.reduce((total, landRow) => total + (landRow.area ?? 0), 0));
const totalLandValue = computed(() => allLandRows.value.reduce((total, landRow) => total + getValueForRow(landRow), 0));
const totalCvIncome = computed(() => allImprovementRows.value.reduce((total, improvementRow) => total + calculateCvIncome(improvementRow), 0));
const totalViSummation = computed(() => allImprovementRows.value.reduce((total, improvementRow) => total + calculateViSummation(improvementRow), 0));
const cvIncomeImprovementValue = computed(() => totalCvIncome.value - totalLandValue.value);
const viSummationCapitalValue = computed(() => totalLandValue.value + totalViSummation.value);
const cvByTotalArea = computed(() => (worksheet.value.buildingFloorArea ? adoptedCapitalValue.value / worksheet.value.buildingFloorArea : 0));
const lvByLandArea = computed(() => (worksheet.value.landArea ? adoptedLandValue.value / (worksheet.value.landArea * SQUARE_METERS_IN_HECTARE) : 0));
const viByTotalArea = computed(() => (worksheet.value.buildingFloorArea ? adoptedImprovementValue.value / worksheet.value.buildingFloorArea : 0));
const qpid = computed(() => worksheet.value?.qpid);
const adoptedCapitalValuePreCorrection = computed(() => roundForTa(valuationMethod.value === VI_SUMMATION ? viSummationCapitalValue.value : totalCvIncome.value));
const adoptedCapitalValue = computed(() => (adoptedLandValue.value + adoptedImprovementValue.value));
const adoptedLandValue = computed(() => roundForTa(totalLandValue.value));
const adoptedImprovementValue = computed(() => {
    const vi = adoptedCapitalValuePreCorrection.value - adoptedLandValue.value;
    const minVi = worksheet.value.revisionMinVi ?? 0;
    if (vi <= minVi && vi <= worksheet.value.valueOfImprovements) {
        return Math.min(minVi, worksheet.value.valueOfImprovements);
    }
    return vi;
});
const hasAdoptedMinVi = computed(() => {
    const vi = adoptedCapitalValuePreCorrection.value - adoptedLandValue.value;
    return vi <= (worksheet.value.revisionMinVi ?? 0) && vi <= worksheet.value.valueOfImprovements;
});
const valuationMethod = computed(() => {
    if (worksheet.value.commercialValuationMethodId == 2 || worksheet.value.commercialValuationMethodId == 1) {
        return CV_INCOME;
    }
    if (worksheet.value.commercialValuationMethodId == 4 || worksheet.value.commercialValuationMethodId == 3) {
        return VI_SUMMATION;
    }
    return null;
});
const apportionmentsWithoutCvOverrides = computed(() => worksheet.value.adoptedValues?.filter(apportionment => _.isNil(apportionment.cvOverride)) ?? []);
const apportionmentsWithoutLvOverrides = computed(() => worksheet.value.adoptedValues?.filter(apportionment => _.isNil(apportionment.lvOverride)) ?? []);
const improvementRowsWithoutCvOverrides = computed(() => apportionmentsWithoutCvOverrides.value.flatMap(apportionment => getImprovementRowsForApportionmentSuffix(apportionment.suffix)));
const improvementRowsWithoutLvOverrides = computed(() => apportionmentsWithoutLvOverrides.value.flatMap(apportionment => getImprovementRowsForApportionmentSuffix(apportionment.suffix)));

const totalExcessLandForApportionmentsWithoutCvOverride = computed(() => apportionmentsWithoutCvOverrides.value.reduce((total, apportionment) => total + getApportionmentExcessLand(apportionment), 0));
const totalExcessLandForApportionmentsWithoutLvOverride = computed(() => apportionmentsWithoutLvOverrides.value.reduce((total, apportionment) => total + getApportionmentExcessLand(apportionment), 0));
const totalCvIncomeForApportionmentsWithoutCvOverride = computed(() => improvementRowsWithoutCvOverrides.value.reduce((total, improvementRow) => total + calculateCvIncome(improvementRow), 0));

const allCvOverridesProvided = computed(() => isApportionmentCode5.value && !worksheet.value.adoptedValues.some(apportionment => _.isNil(apportionment.cvOverride)));
const allLvOverridesProvided = computed(() => isApportionmentCode5.value && !worksheet.value.adoptedValues.some(apportionment => _.isNil(apportionment.lvOverride)));
const cvOverrideTotal = computed(() => worksheet.value.adoptedValues.reduce((total, apportionment) => total + (apportionment.cvOverride ?? 0), 0) || null);
const lvOverrideTotal = computed(() => worksheet.value.adoptedValues.reduce((total, apportionment) => total + (apportionment.lvOverride ?? 0), 0) || null);
const totalRentalForApportionmentsWithoutLvOverride = computed(() => improvementRowsWithoutLvOverrides.value.reduce((total, improvementRow) => total + (calculateTotalRent(improvementRow) ?? 0), 0));
const totalApportionmentCv = computed(() => worksheet.value.adoptedValues?.reduce((total, apportionment) => total + (calculateApportionmentCv(apportionment) ?? 0), 0));
const totalApportionmentLv = computed(() => worksheet.value.adoptedValues?.reduce((total, apportionment) => total + (calculateApportionmentLv(apportionment) ?? 0), 0));
const totalApportionmentCvCorrection = computed(() => worksheet.value.adoptedValues?.reduce((total, apportionment) => total + getCvCorrectionForApportionment(apportionment), 0));
const totalApportionmentLvCorrection = computed(() => worksheet.value.adoptedValues?.reduce((total, apportionment) => total + getLvCorrectionForApportionment(apportionment), 0));
const highestCvApportionmentSuffix = computed(() => {
    const apportionments = worksheet.value.adoptedValues ?? [];
    let maxCv = 0;
    let suffix = null;
    for (const apportionment of apportionments) {
        const cv = calculateApportionmentCv(apportionment);
        if (cv > maxCv && _.isNil(apportionment.cvOverride)) {
            maxCv = cv;
            suffix = apportionment.suffix;
        }
    }
    return suffix;
});
const highestLvApportionmentSuffix = computed(() => {
    const apportionments = worksheet.value.adoptedValues ?? [];
    let maxLv = 0;
    let suffix = null;
    for (const apportionment of apportionments) {
        const lv = calculateApportionmentLv(apportionment);
        if (lv > maxLv && _.isNil(apportionment.lvOverride)) {
            maxLv = lv;
            suffix = apportionment.suffix;
        }
    }
    return suffix;
});
const correctionToAddToCv = computed(() => (valuationMethod.value === CV_INCOME ? adoptedCapitalValue.value - totalApportionmentCv.value : 0));
const correctionToAddToLv = computed(() => (valuationMethod.value === CV_INCOME ? adoptedLandValue.value - totalApportionmentLv.value : 0));
const totalApportionmentCvPercent = computed(() => worksheet.value.adoptedValues?.reduce((total, apportionment) => total + (calculateApportionmentCvPercent(apportionment) ?? 0), 0));
const totalApportionmentLvPercent = computed(() => worksheet.value.adoptedValues?.reduce((total, apportionment) => total + (calculateApportionmentLvPercent(apportionment) ?? 0), 0));
const isReadOnly = computed(() => worksheet.value && worksheet.value.assessmentStatus === 'I');

const validationResult = ref({
    status: null,
    errors: null,
    warnings: null,
    hasErrors: null,
    hasWarnings: null,
    validations: null,
});


export default function useCommercialWorksheet() {
    router = useRouter();
    route = useRoute();
    return {
        loaded,
        classifications,
        picklistValues,
        property,
        qvProperty,
        qvPropertyZone,
        propertyDetails,
        worksheet,
        rfc,
        rfcClassifications,
        isRevision,
        createRevision,
        initialPageLoad,
        getWorksheet,
        deleteCommercialWorksheet,
        refreshWorksheet,
        zoningOptions,
        totalLandArea,
        totalLandValue,
        newLandRow,
        newImprovementRow,
        newActualRentalRow,
        suffixOptions,
        isApportionmentCode5,
        calculateCvIncome,
        calculateViSummation,
        calculateTotalRent,
        totalCvIncome,
        totalViSummation,
        cvIncomeImprovementValue,
        viSummationCapitalValue,
        adoptedCapitalValue,
        adoptedLandValue,
        adoptedImprovementValue,
        cvByTotalArea,
        lvByLandArea,
        viByTotalArea,
        allLandRows,
        allImprovementRows,
        calculateApportionmentCvPercent,
        calculateApportionmentLvPercent,
        calculateApportionmentCv,
        calculateApportionmentLv,
        totalApportionmentCv,
        totalApportionmentLv,
        totalApportionmentCvCorrection,
        totalApportionmentLvCorrection,
        highestCvApportionmentSuffix,
        highestLvApportionmentSuffix,
        correctionToAddToCv,
        correctionToAddToLv,
        cvOverrideTotal,
        lvOverrideTotal,
        totalApportionmentCvPercent,
        totalApportionmentLvPercent,
        getApportionmentCv,
        getApportionmentLv,
        calculateCvIncomeFormula,
        cvIncomeFormula,
        calculateViSummationFormula,
        viSummationFormula,
        updateAssessment,
        updateCommercialWorksheet,
        earthquakeRatingRangeRef,
        earthquakeRatingAssessorRef,
        liquefactionRef,
        proposedZoneRef,
        qvCategoryRef,
        commercialGroupingRef,
        isReadOnly,
        getErrorsForLabel,
        valuationMethod,
        newRentalDateErrors,
        rentalDateErrors,
        printCommercialWorksheet,
        allCvOverridesProvided,
        allLvOverridesProvided,
    };
}

function getApportionmentCv(apportionment) {
    if (!_.isNil(apportionment.cvOverride)) {
        return apportionment.cvOverride;
    }
    const cv = calculateApportionmentCv(apportionment);
    const correction = getCvCorrectionForApportionment(apportionment);
    return cv + correction;
}

function getCvCorrectionForApportionment(apportionment) {
    if (apportionment.suffix === highestCvApportionmentSuffix.value) {
        return correctionToAddToCv.value;
    }
    return 0;
}

function getApportionmentLv(apportionment) {
    if (!_.isNil(apportionment.lvOverride)) {
        return apportionment.lvOverride;
    }
    const lv = calculateApportionmentLv(apportionment);
    const correction = getLvCorrectionForApportionment(apportionment);
    return lv + correction;
}

function getLvCorrectionForApportionment(apportionment) {
    if (apportionment.suffix === highestLvApportionmentSuffix.value) {
        return correctionToAddToLv.value;
    }
    return 0;
}

function calculateApportionmentCv(apportionment) {
    if (valuationMethod.value === CV_INCOME) {
        const apportionmentExcessLand = getApportionmentExcessLand(apportionment);
        const apportionmentCvIncome = getImprovementRowsForApportionmentSuffix(apportionment.suffix).reduce((total, improvementRow) => total + calculateCvIncome(improvementRow), 0);
        if (_.isNil(apportionment.cvOverride)) {
            const calculatedCv = (adoptedCapitalValue.value - cvOverrideTotal.value - totalExcessLandForApportionmentsWithoutCvOverride.value) * ((apportionmentCvIncome - apportionmentExcessLand) / (totalCvIncomeForApportionmentsWithoutCvOverride.value - totalExcessLandForApportionmentsWithoutCvOverride.value)) + apportionmentExcessLand;
            return roundToNearest(calculatedCv || 0, CV_ROUNDING_AMOUNT);
        }
        return apportionment.cvOverride;
    }
    if (valuationMethod.value === VI_SUMMATION) {
        return apportionment.cvOverride;
    }
    return null;
}

function calculateApportionmentLv(apportionment) {
    if (valuationMethod.value === CV_INCOME) {
        const apportionmentTotalRental = getApportionmentTotalRental(apportionment);
        const apportionmentExcessLand = getApportionmentExcessLand(apportionment);
        if (_.isNil(apportionment.lvOverride)) {
            const calculatedLv = (adoptedLandValue.value - lvOverrideTotal.value - totalExcessLandForApportionmentsWithoutLvOverride.value) * (apportionmentTotalRental / totalRentalForApportionmentsWithoutLvOverride.value) + apportionmentExcessLand;
            return roundToNearest(calculatedLv || 0, LV_ROUNDING_AMOUNT);
        }
        return apportionment.lvOverride;
    }
    if (valuationMethod.value === VI_SUMMATION) {
        return apportionment.lvOverride;
    }
    return null;
}

function getApportionmentTotalRental(apportionment) {
    return getImprovementRowsForApportionmentSuffix(apportionment.suffix).reduce((total, improvementRow) => total + (calculateTotalRent(improvementRow) ?? 0), 0);
}

function getApportionmentExcessLand(apportionment) {
    return getImprovementRowsForApportionmentSuffix(apportionment.suffix).reduce((total, improvementRow) => total + calculateExcessLand(improvementRow), 0);
}

function getImprovementRowsForApportionmentSuffix(suffix) {
    return worksheet.value.improvementRows?.filter(improvementRow => improvementRow.suffix === suffix) ?? [];
}

function calculateApportionmentCvPercent(apportionment) {
    if (valuationMethod.value === VI_SUMMATION && _.isNil(apportionment.cvOverride)) {
        return null;
    }
    const cvPercent = ((apportionment.cvOverride ?? (calculateApportionmentCv(apportionment) + getCvCorrectionForApportionment(apportionment))) / adoptedCapitalValue.value) * 100;
    return (isNaN(cvPercent) || !isFinite(cvPercent)) ? null : cvPercent;
}

function calculateApportionmentLvPercent(apportionment) {
    if (valuationMethod.value === VI_SUMMATION && _.isNil(apportionment.lvOverride)) {
        return null;
    }
    const lvPercent = ((apportionment.lvOverride ?? (calculateApportionmentLv(apportionment) + getLvCorrectionForApportionment(apportionment))) / adoptedLandValue.value) * 100;
    return (isNaN(lvPercent) || !isFinite(lvPercent)) ? null : lvPercent;
}

function roundForTa(value) {
    if (!value || !worksheet.value.roundingRules) {
        return value;
    }
    const roundingRule = worksheet.value.roundingRules.find(rule => rule.valueFrom <= value && rule.valueTo >= value);
    const roundingAmount = roundingRule ? roundingRule.roundingAmount : 1;
    return roundToNearest(value, roundingAmount);
}

function calculateCvIncome(improvementRow) {
    const totalRent = calculateTotalRent(improvementRow);
    const percentVacant = improvementRow.percentVacant ?? 0;
    const percentExcessLand = improvementRow.percentExcessLand ?? 0;
    const cvIncome = (totalRent * (1 - percentVacant / 100)) / ((worksheet.value.capRate ?? CAP_RATE_DEFAULT) / 100) + (percentExcessLand / 100) * totalLandValue.value;
    return isNaN(cvIncome) ? 0 : Math.round(cvIncome);
}
function calculateCvIncomeFormula(improvementRow) {
    const totalRent = calculateTotalRent(improvementRow);
    return `(${totalRent ?? 0} * (1 - ${improvementRow.percentVacant ?? 0} / 100)) / (${worksheet.value.capRate ?? CAP_RATE_DEFAULT} / 100) + (${improvementRow.percentExcessLand ?? 0} / 100) * ${totalLandValue.value ?? 0}`;
}
function cvIncomeFormula() {
    return '(Total Rent * (1 - % Vacant / 100)) / (Cap Rate / 100) + (% Excess Land / 100) * Total Land Value';
}

function calculateViSummation(improvementRow) {
    const depreciation = getDepreciation(improvementRow);
    const area = improvementRow.area ?? 0;
    const multiple = improvementRow.multiple ?? 0;
    const percentObsolete = improvementRow.percentObsolete ?? 0;
    const lumpSum = improvementRow.lumpSum ?? 0;
    const viSummation = area * worksheet.value.modal * (multiple ?? 1) * (1 - depreciation) * (1 - percentObsolete / 100) + lumpSum;
    return isNaN(viSummation) ? 0 : Math.round(viSummation);
}
function calculateViSummationFormula(improvementRow) {
    const depreciation = getDepreciation(improvementRow);
    return `${improvementRow.area ?? 0} * ${worksheet.value.modal ?? 0} * ${improvementRow.multiple ?? 1} * (1 - ${depreciation ?? 0}) * (1 - ${improvementRow.percentObsolete ?? 0} / 100) + ${improvementRow.lumpSum ?? 0}`;
}
function viSummationFormula() {
    return 'Area * Modal * Multiple * (1 - Depreciation) * (1 - % Obsolete  / 100) + Lump Sum';
}

function getDepreciation(improvementRow) {
    const currentRevisionDateYear = new Date(worksheet.value.currentRevisionDate).getFullYear();
    const age = _.isNil(improvementRow.yearBuilt) ? 0 : currentRevisionDateYear - improvementRow.yearBuilt;
    let depreciation = (100 / improvementRow.life) * (age / 100);
    if (depreciation > 0.9) {
        depreciation = 0.9;
    }
    if (improvementRow.yearBuilt > currentRevisionDateYear) {
        depreciation = 0;
    }
    return depreciation;
}

function calculateExcessLand(improvementRow) {
    const percentExcessLand = improvementRow.percentExcessLand ?? 0;
    return (percentExcessLand / 100) * totalLandValue.value;
}

function calculateTotalRent(improvementRow) {
    if (_.isNil(improvementRow)) {
        return 0;
    }
    if (_.isNil(improvementRow.carparks)) {
        if (_.isNil(improvementRow.area) || _.isNil(improvementRow.rental)) {
            return 0;
        }
        return Math.round(improvementRow.area * improvementRow.rental);
    }
    if (_.isNil(improvementRow.carparks) || _.isNil(improvementRow.rental)) {
        return 0;
    }
    return Math.round(improvementRow.carparks * improvementRow.rental * WEEKS_PER_YEAR);
}

function calculateTotalGrossRent() {
    const nla = allImprovementRows.value.reduce((total, improvementRow) => total + _.defaultTo(improvementRow.area, 0), 0);
    const totalOutgoings = !isNaN(worksheet.value.outgoings) && worksheet.value.outgoings > 0 ? Math.round(nla * worksheet.value.outgoings) : null;
    const totalNetRent = allImprovementRows.value.reduce((total, improvementRow) => total + calculateTotalRent(improvementRow), 0);
    return !isNaN(worksheet.value.outgoings) && worksheet.value.outgoings > 0 ? totalNetRent + totalOutgoings : null;
}

function getValueForRow(landRow) {
    if (_.isNil(landRow.area) || _.isNil(landRow.ratePerMetre)) {
        return null;
    }
    return landRow.area * landRow.ratePerMetre;
}

function populatePropertyDetails() {
    const liquefaction = qvProperty.value?.hazards?.find(hazard => hazard.classification?.category === 'LiquefactionRating');
    const earthquakeRating = qvProperty.value?.hazards?.find(hazard => hazard.classification?.category === 'EarthquakeRating');
    const actualEarthquakeRating = earthquakeRating?.rating;

    propertyDetails.value = {
        actualEarthquakeRating,
        earthquakeRatingRange: earthquakeRating?.classification?.code,
        earthquakeRatingAssessor: earthquakeRating?.source?.code,
        liquefaction: liquefaction?.classification?.code,
        proposedZoneCode: qvPropertyZone.value?.proposedZone?.code,
        qvCategoryCode: monarchCurrentPropertyDetails.value?.qvCategory?.code,
        commercialGroupingCode: monarchCurrentPropertyDetails.value?.commercialDetail?.propertyGroupingTypeCommercial?.code,
        remedyYear: qvProperty.value?.remedyYear,
    };
}
async function initialPageLoad(qpid, isRevisionWorksheet, createRevisionWorksheet) {
    loaded.value = false;
    isRevision.value = isRevisionWorksheet;
    createRevision.value = createRevisionWorksheet;
    const [monarchProperty, picklists, commercialWorksheet, classifications, existingRfc, qvPropertyZoneInfo] = await Promise.all([
        propertyMasterDataController.getProperty(qpid),
        propertyMasterDataController.getPicklistValues(),
        getWorksheet(qpid),
        getRfcClassifications(),
        getExistingReasonForChange(qpid),
        propertyMasterDataController.getQvPropertyZoneInfo(qpid),
    ]);

    property.value = monarchProperty;
    picklistValues.value = picklists;
    worksheet.value = {
        ...commercialWorksheet,
        qpid,
        isUsingValuationMethodCv: true,
        isUsingValuationMethodVi: true,
    };
    rfcClassifications.value = classifications;
    rfc.value = {
        ...existingRfc,
        output: existingRfc.outputId,
        source: existingRfc.sourceId,
    };
    const [qvPropertyMonarch, proposedZones, currentPropertyDetails] = await Promise.all([
        propertyMasterDataController.getQvProperty(monarchProperty.property.id),
        referenceDataController.getZoningOptions({
            parentCode: monarchProperty.property.territorialAuthority.code,
            groupCode: 'TAPZ',
            sort: 'SHORT_DESCRIPTION',
            order: 'ASC',
        }),
        propertyDetailController.getCurrentPropertyDetail(monarchProperty.property.id),
    ]);
    zoningOptions.value = proposedZones;
    initPropertyData(qvPropertyMonarch, qvPropertyZoneInfo, currentPropertyDetails);
    loaded.value = true;
    validationResult.value = {
        status: null,
        errors: null,
        warnings: null,
        hasErrors: null,
        hasWarnings: null,
        validations: null,
    };
    if (route.name === 'commercial-revision-worksheet' && !worksheet.value.hasRevision) {
        const title = 'Revision Worksheet does not exist for this property';
        const message = 'Click OK to go to the  current Commercial Worksheet.';
        const payload = {
            title,
            message,
            confirmText: 'OK',
            showCancelBtn: false
        }
        await modal.show(ConfirmationModal, payload);
        router.push({ name: 'commercial-worksheet', params: { qpid: qpid } });
    }
}

function resetBlankRows() {
    newLandRow.value = { isValidLandArea: true };
    newImprovementRow.value = { suffix: suffixOptions?.value?.[0]?.id ?? null };
    newActualRentalRow.value = {
        actualRentalId: null,
        rentalDate: null,
        grossRental: null,
        floorArea: null,
        rentalPerSqmt: null,
        actualRentalTypeId: null,
        tenant: null,
    };
    newRentalDateErrors.value = [];
}

function initPropertyData(qvPropertyMonarch, qvPropertyZoneInfo, currentPropertyDetails) {
    qvPropertyZone.value = qvPropertyZoneInfo;
    qvProperty.value = qvPropertyMonarch ?? getBlankQvProperty(property.value.property.id, qpid);
    monarchCurrentPropertyDetails.value = currentPropertyDetails;
    populatePropertyDetails();
}

function getWorksheet(qpid) {
    if (createRevision.value){
        return worksheetController.createCommercialRevisionWorksheet(qpid);
    }
    if (isRevision.value){
        return worksheetController.getCommercialRevisionWorksheet(qpid);
    }
    return worksheetController.getCommercialWorksheet(qpid);
}

function getRfcClassifications() {
    return worksheetController.getRfcClassifications();
}

function getExistingReasonForChange(qpid) {
    return rfcController.getExistingReasonForChange(qpid, `QVNZ-${userData?.value?.userName}`);
}

async function deleteCommercialWorksheet() {
    try {
        loaded.value = false;
        await worksheetController.deleteCommercialWorksheet(qpid.value);
        router.push({ name: 'property', params: { qpid: qpid.value } });
    } catch (e) {
        console.error(e);
        modal.showError('Error', 'Something went wrong while deleting worksheet. Please try again');
    }
    finally {
        loaded.value = true;
    }
}

async function refreshWorksheet() {
    try {
        loaded.value = false;
        const [worksheetResponse, rfcResponse] = await Promise.all([getWorksheet(qpid.value), getExistingReasonForChange(qpid.value)]);
        worksheet.value = {
            ...worksheetResponse,
            qpid: qpid.value,
            isUsingValuationMethodCv: true,
            isUsingValuationMethodVi: true,
        };
        rfc.value = {
            ...rfcResponse,
            output: rfcResponse.outputId,
            source: rfcResponse.sourceId,
        };
    }
    catch (e) {
        console.error(e);
        modal.showError('Error', 'Something went wrong while refreshing worksheet.');
    }
    finally {
        loaded.value = true;
    }
}

function getQvPropertyToSave() {
    const hazards = [...qvProperty.value.hazards.filter(hazard => hazard.classification?.category !== 'LiquefactionRating' && hazard.classification?.category !== 'EarthquakeRating')];
    const earthquake = { rating: propertyDetails.value.actualEarthquakeRating ?? null, classification: null, source: null };
    if (propertyDetails.value.earthquakeRatingRange) {
        earthquake.classification = earthquakeRatingRangeRef?.value?.selection;
    }
    if (propertyDetails.value.earthquakeRatingAssessor) {
        earthquake.source = earthquakeRatingAssessorRef?.value?.selection;
    }
    if (parseInt(earthquake.rating) >= 0 || earthquake.classification) {
        hazards.push(earthquake);
    }
    if (propertyDetails.value.liquefaction) {
        hazards.push({ classification: liquefactionRef?.value?.selection });
    }

    const qvPropertyToSave = {
        ...qvProperty.value,
        hazards,
        remedyYear: propertyDetails.value.remedyYear,
    };
    return qvPropertyToSave;
}

function getQvPropertyZoneInfoToSave() {
    return {
        ...qvPropertyZone.value,
        proposedZone: proposedZoneRef?.value?.selection ?? qvPropertyZone.value.proposedZone,
    };
}

function getCurrentPropertyDetailsToSave() {
    return {
        ...monarchCurrentPropertyDetails.value,
        qvCategory: qvCategoryRef?.value?.selection ?? monarchCurrentPropertyDetails.value.qvCategory,
        commercialDetail: {
            ...monarchCurrentPropertyDetails.value.commercialDetail,
            propertyGroupingTypeCommercial: commercialGroupingRef?.value?.selection ?? null,
        },
    };
}

async function updateCommercialWorksheet(isUpdateAssessment = false) {
    let hasError;
    saving.value = true;
    try {
        const payload = getWorksheetPayload(isUpdateAssessment);

        if (isUpdateAssessment) {
            const { rfc, adoptedLandValue, adoptedCapitalValue, parentLv, parentCv, isCurrent } = payload;
            if(isCurrent) {
                const rfcValidation = await handleRfcValidation(rfc, adoptedLandValue, adoptedCapitalValue, parentLv, parentCv);
                if (!rfcValidation) {
                    return;
                }
            }
            const isConfirmed = await getNewValuesConfirmation();
            if (!isConfirmed) {
                return;
            }
        }
        const clientValidationResult = validateWorksheetClient(payload);
        validationResult.value = clientValidationResult;

        if (clientValidationResult.hasErrors || clientValidationResult.hasWarnings) {
            resetBlankRows();
            worksheet.value = {
                ...clientValidationResult.worksheet,
                isUsingValuationMethodCv: worksheet.value.isUsingValuationMethodCv,
                isUsingValuationMethodVi: worksheet.value.isUsingValuationMethodVi,
            };
            const isValidated = await showValidationErrors(clientValidationResult);
            if (!isValidated) {
                return;
            }
        }
        const validationRes = await validateWorksheet(payload);
        if (!validationRes) {
            return;
        }
        const res = await worksheetController.updateCommercialWorksheet(payload);
        hasError = await handleUpdateResult(res, isUpdateAssessment);
    } catch (error) {
        console.error(error);
        hasError = true;
    } finally {
        saving.value = false;
        if (hasError) {
            await modal.showError('Error', 'Something went wrong while Updating Worksheet. Please try again');
        }
    }
}

function getWorksheetPayload(isUpdateAssessment) {
    const payload = {
        ...worksheet.value,
        outgoings: Math.round(worksheet.value.outgoings),
        adoptedCapitalValue: adoptedCapitalValue.value,
        adoptedLandValue: adoptedLandValue.value,
        adoptedImprovementValue: adoptedImprovementValue.value,
        isUpdateAssessment,
        rfc: rfc.value,
        isRevision: isRevision.value,
        createRevision: createRevision.value,
        valuationMethod: valuationMethod.value,
        streetLocationOptions: classifications.value.StreetLocationType?.filter((item) => item.ratingAuthorityId === worksheet.value.ratingAuthorityId),
        hasAdoptedMinVi: hasAdoptedMinVi.value && adoptedImprovementValue.value === worksheet.value.revisionMinVi,
        cvOverrideTotal: cvOverrideTotal.value,
        lvOverrideTotal: lvOverrideTotal.value,
        remedyYear: propertyDetails.value.remedyYear,
        isMaoriLand: property.value.property.landUseData.isMaoriLand,
        categoryCode: property.value.property.category.code,
        isCurrent: !isRevision.value && !createRevision.value,
        hasSameOutgoings: worksheet.value.currentOutgoings === worksheet.value.outgoings,
        nla: allImprovementRows.value.reduce((total, improvementRow) => total + _.defaultTo(improvementRow.area, 0), 0),
        totalLandArea: totalLandArea.value,
        totalCvIncome: totalCvIncome.value,
        viSummationCapitalValue: viSummationCapitalValue.value,
        allCvOverridesProvided: allCvOverridesProvided.value,
        allLvOverridesProvided: allLvOverridesProvided.value,
        totalLandValue: totalLandValue.value,
    };
    payload.adoptedValues = worksheet.value.adoptedValues.map((apportionment) => {
        return {
            ...apportionment,
            qupid: qpid.value,
            cv: getApportionmentCv(apportionment),
            lv: getApportionmentLv(apportionment),
        };
    });
    payload.landRows = allLandRows.value;
    payload.improvementRows = allImprovementRows.value.map((improvements) => {
        return {
            ...improvements,
            viSummation: calculateViSummation(improvements),
            cvIncome: calculateCvIncome(improvements),
        };
    });
    payload.actualRentalRows = [...payload.actualRentalRows, newActualRentalRow.value];
    payload.totalGrossRent = calculateTotalGrossRent();
    return payload;
}

async function updateAssessment() {
    try {
        saving.value = true;
        const [qvPropertyResponse, qvPropertyZoneInfoResponse, currentPropertyDetailResponse] = await Promise.all([
            propertyMasterDataController.saveQvProperty(getQvPropertyToSave()),
            propertyMasterDataController.saveQvPropertyZoneInfo(getQvPropertyZoneInfoToSave()),
            propertyDetailController.saveCurrentPropertyDetail(getCurrentPropertyDetailsToSave()),
            updateCommercialWorksheet(true)
        ]);
        initPropertyData(qvPropertyResponse?.value || qvProperty.value, qvPropertyZoneInfoResponse?.value || qvPropertyZone.value, currentPropertyDetailResponse?.value || monarchCurrentPropertyDetails.value);
    } catch (error) {
        console.error(error);
        await modal.showError('Error', 'Something went wrong while Updating Assessment. Please try again');
    } finally {
        saving.value = false;
    }
}

function getBlankQvProperty(propertyId, qpid) {
    return {
        id: propertyId,
        qupid: qpid,
        effectiveDateOfCollection: null,
        effectiveLandArea: null,
        aggregateUnitDetails: {
            unitNumber: null,
            unitType: null,
            numberOfSingleBedrooms: null,
            numberOfDoubleBedrooms: null,
            numberOfHomeOfficesOrStudies: null,
            numberOfBathrooms: null,
            mainBathroomAge: null,
            mainBathroomQuality: null,
            ensuiteAge: null,
            ensuiteQuality: null,
            kitchenAge: null,
            kitchenQuality: null,
            redecorationAge: null,
            internalCondition: null,
            heatingType: [],
            insulation: [],
            plumbingAge: null,
            wiringAge: null,
            doubleGlazing: null,
            alternativeEnergy: [],
            effectiveFloorArea: null,
            studHeight: null,
            additionalFeatures: null,
            rentalAmount: {
                source: null,
                knownDate: null,
                weeklyRentalAmount: null,
            },
        },
        unitDetails: [],
        hazards: [],
        hazardNotes: null,
        heritageFeatures: [],
        averageDailySunshineHours: null,
    };
}

async function handleRfcValidation(rfc, adoptedLandValue, adoptedCapitalValue, parentLv, parentCv) {
    const rfcValidationResult = validateReasonForChange(rfc, adoptedLandValue, adoptedCapitalValue, parentLv, parentCv);
    if (rfcValidationResult.hasErrors || rfcValidationResult.hasWarnings) {
        return await showValidationErrors({ ...rfcValidationResult, customWarningBtnLabel: 'Ok' });
    }
    return true;
}

async function handleUpdateResult(res, isUpdateAssessment) {
    if (!res) {
        return true;
    }
    resetBlankRows();
    worksheet.value = {
        ...res,
        qpid: qpid.value,
        isUsingValuationMethodCv: worksheet.value.isUsingValuationMethodCv,
        isUsingValuationMethodVi: worksheet.value.isUsingValuationMethodVi,
    };
    if (createRevision.value){
        isRevision.value = true;
        createRevision.value = false;
    }
    await modal.showSuccess('Success', 'The Worksheet was Updated Successfully');
    if (isUpdateAssessment && !(createRevision.value || isRevision.value)) {
        handleUpdateAssessmentNavigation(res);
    }
    return false;
}

async function handleUpdateAssessmentNavigation(res) {
    if (res.isSRAValuesChanging) {
        const confirmedSRA = await showSRAValuesSuccess();
        if (confirmedSRA) {
            router.push({ name: 'property-sra-values', params: { qpid: res.qpid } });
            return;
        }
    }
    router.push({ name: 'property', params: { qpid: res.qpid } });
}

async function showValidationErrors(payload) {
    return await modal.show(CommercialWorksheetValidationModal, payload);
}

async function showSRAValuesSuccess() {
    return await modal.show(ConfirmationModal, {
        title: 'Success- SRA values need updating',
        message: `The assessment was successfully updated,but the SRA values need adjusted.

                ${worksheet.value.hasRevision ? 'Revision worksheet must be updated.' : ''}`,
        confirmText: 'Go To SRA VALUES',
        showCancelBtn: false,
    });
}

async function getNewValuesConfirmation() {
    return await modal.show(ConfirmationModal, {
        title: 'Do you want to proceed?',
        message: `New Assessment values will be

                    - CV: $${adoptedCapitalValue.value}
                    - LV: $${adoptedLandValue.value}
                    - VI: $${adoptedImprovementValue.value}`,
        confirmText: 'Accept New Values',
        cancelText: 'No, Return To Worksheet',
    });
}

function getErrorsForLabel(label, type = 'errors') {
    return validationResult.value?.validations?.[label]?.[type] ?? [];
}

async function validateWorksheet(payload) {
    const response = await worksheetController.validateCommercialWorksheet(payload);
    validationResult.value = response;

    if (validationResult.value.hasErrors) {
        const formattedErrors = (validationResult.value?.errors || []).filter((e) => e).map((error) => ({ message: error }));
        const formattedWarnings = (validationResult.value?.warnings || []).filter((w) => w).map((warning) => ({ message: warning }));
        resetBlankRows();
        worksheet.value = {
            ...validationResult.value.worksheet,
            isUsingValuationMethodCv: worksheet.value.isUsingValuationMethodCv,
            isUsingValuationMethodVi: worksheet.value.isUsingValuationMethodVi,
        };
        return await showValidationErrors({ ...validationResult.value, formattedErrors, formattedWarnings });
    }
    return true;
}

async function printCommercialWorksheet() {
    try {
        modal.showSuccess('Commercial Worksheet Report', 'Generating the PDF...');
        const type = (!isRevision.value && !createRevision.value) ? 'current' : 'revision';
        const res = await worksheetController.generateCommercialWorksheetPdf(qpid.value, type);
        if (res) {
            openBase64DataInNewTab(res, 'application/pdf');
            await modal.showSuccess('Success', 'The PDF was successfully generated.');
        }
    } catch (error) {
        console.error(error);
        await modal.showError('Error', 'Something went wrong while Printing Worksheet. Please try again');
    }
}
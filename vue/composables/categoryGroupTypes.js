import { ref, onMounted } from 'vue';

const categoryGroupTypeList = ref([])

export function useCategoryGroupType() {
    onMounted(async () => {
        if (categoryGroupTypeList.value.length === 0) {
            categoryGroupTypeList.value = await getCategoryGroupTypes()
        }}
    );

    return categoryGroupTypeList;
}

export async function getCategoryGroupTypes() {
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getPicklistValue('CategoryGroupType');
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        const result = await res.json();
        return result.result.CategoryGroupType;
    }
    catch (error) {
        const message = 'Error calling controller to get category group types';
        console.error(message, error);
    }
}
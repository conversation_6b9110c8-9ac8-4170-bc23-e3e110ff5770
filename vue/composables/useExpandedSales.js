import { computed, ref, watch } from 'vue';
import SaleValidationModal from '@/components/rollMaintenance/propertySale/common/SaleValidationModal.vue';
import * as controller from '@/services/ApiSalesController';
import { store } from '@/DataStore';
import _ from 'underscore';
import { getProperty, getPropertyInfoFull, updateOwners } from '@/services/PropertyMasterDataController';
import useModal from '@/composables/useModal';
import validateSaleClient from '@/utils/sales/validateSale';
import { validateOwnershipOnFirstLoad } from '../utils/sales/validateSale';
import { useSaleSearch } from '@/composables/useSaleSearch.js';
import { useRouter } from 'vue-router/composables';
import { DateTime } from 'luxon';

const { results } = useSaleSearch();

export default function useSales() {

    function populateRFC(obj) {
        obj.rfc = {
            reason: 'Sales ex...',
            output: 7,
            source: 3,
        };
    }

    const router = useRouter();

    const loading = ref(false);
    const saving = ref(false);
    const exception = ref(null);
    const linzWarnings = ref({});
    const dvr = ref(null);
    const sale = ref(null);
    const titles = ref([]);
    const primaryProperty = ref(null);
    const propertyInfoFull = ref(null);
    const ownership = ref({});
    const failedInitialOwnershipValidation = ref(null);
    const toggleResultView = ref(false);
    const user = computed(() => store.state.userData);
    const saleType = computed(() => sale.value?.classifications?.saleTypeId);
    const saleTypeChanged = ref(false);
    const qpid = computed(() => sale.value?.qpid);
    const saleId = computed(() => sale.value?.saleId);
    const isSaleToProcess = computed(() => sale.value?.monarchSaleProcessingStatusId === 1);

    const href = ref(null);

    const rtv = computed(() => {
        if (!sale.value || !primaryProperty.value) {
            return '';
        }
        const vacant = ['RB', 'RM', 'RP', 'RV', 'LB', 'LV'];
        const date = new Date(sale.value?.agreementDate ?? undefined);
        const agreementDate = DateTime.fromJSDate(date);
        const category = (primaryProperty.value?.property?.category?.code ?? '').trim().toUpperCase();
        const isResiOrLifestyle = ['R', 'L'].includes(category[0]);
        if (!propertyInfoFull.value || !agreementDate.isValid || !isResiOrLifestyle) {
            return '';
        }
        const rtvRecord = propertyInfoFull.value.rtv?.find((r) => {
            const rtvAsAtDate = DateTime.fromJSDate(new Date(r.as_at_date));
            const hasSameMonth = rtvAsAtDate.hasSame(agreementDate, 'month');
            const hasSameYear = rtvAsAtDate.hasSame(agreementDate, 'year');
            return hasSameMonth && hasSameYear;
        });
        if (!rtvRecord) {
            return '';
        }
        return vacant.includes(category.substring(0, 2)) ? rtvRecord.revaluer_land_value : rtvRecord.revaluer_capital_value;
    });
    const nspByRtv = computed(() => {
        if (!(parseFloat(rtv.value) > 0)) {
            return '';
        }
        const _nspByRtv = parseFloat(sale.value?.price?.net) / parseFloat(rtv.value);
        if (isNaN(_nspByRtv)) {
            return '';
        }
        return _nspByRtv.toFixed(2);
    });

    const qivsOwners = computed(()=> {
        const owners = [];
        titles?.value.forEach(title => {
            title.owners.forEach(owner => {
                owner.decodedLevel = decodeOccownLevel(owner.occownLevel);
                owners.push(owner);
            });
        });
        return owners.sort((a, b) => a.decodedLevel - b.decodedLevel);
    });

    const addSaleInspectionConsentResult = ref(null);

    const qivsOccupiers = computed(()=> {
        const occupiers = [];
        titles?.value.forEach(title => {
            title.occupiers.forEach(occupier => {
                occupier.decodedLevel = decodeOccownLevel(occupier.occownLevel);
                occupiers.push(occupier);
            });
        });
        return occupiers.sort((a, b) => a.decodedLevel - b.decodedLevel);
    });

    const validationResult = ref({
        status: null,
        errors: null,
        warnings: null,
        hasErrors: null,
        hasWarnings: null,
        validations: null,
    });

    const formattedValidationResult = computed(() => {
        return {
            errors: validationResult.value?.errors?.filter(e => e)?.map(error => ({ message: error })) ?? [],
            warnings: validationResult.value?.warnings?.filter(w => w)?.map(warning => ({ message: warning })) ?? [],
        };
    });

    const modal = useModal();

    async function loadSale(_qpid, _saleId) {
        try {
            await Promise.all([
                getSale(_saleId),
                getDvr(_saleId),
            ]);
            const [
                titlesReq,
                primaryPropertyReq,
                propertyInfoFullReq,
            ] = await Promise.all([
                getTitles(_saleId, ''),
                getProperty(qpid.value),
                getPropertyInfoFull(qpid.value),
            ]);
            titles.value = titlesReq;
            primaryProperty.value = primaryPropertyReq;
            propertyInfoFull.value = propertyInfoFullReq;
            populateAndValidateOwnership();
            watch(saleType, onSaleTypeChanged);
            if(isSaleToProcess.value) {
                getLinzSaleWarnings();
            }
        } catch (error) {
            console.error(error);
            exception.value = error;
        }
    }

    function populateAndValidateOwnership() {
        ownership.value.owners = sale.value.owners ?? [];
        ownership.value.toras = structuredClone(propertyInfoFull.value?.toras ?? {});
        ownership.value.isMaoriLand = propertyInfoFull.value?.maoriland === 'Y';

        const ownershipValidation = validateOwnershipOnFirstLoad(ownership.value);
        if (ownershipValidation.hasErrors) {
            failedInitialOwnershipValidation.value = true;
        }
        validationResult.value = ownershipValidation;
    }

    async function getSale(saleId) {
        try {
            loading.value = true;
            const saleResponse = await controller.getSale(saleId);

            if (!saleResponse) {
                exception.value = 'No sale found';
                return;
            }

            if (!saleResponse.rfc) {
                populateRFC(saleResponse);
            }

            sale.value = saleResponse;
            href.value = getHref(saleId, saleResponse.qpid);
            return sale;
        } catch (e) {
            console.error(e);
            exception.value = e;
        } finally {
            loading.value = false;
        }
    }

    async function saveSale(options = {}) {
        const { isSavingOwnershipData = false } = options;
        sale.value.monarchSaleProcessingStatusId = 2;
        const payload = {
            sale: sale.value,
            dvr: dvr.value,
            isExpandedSaleView: true,
        };
        if (isSavingOwnershipData) {
            payload.ownership = ownership.value;
        }
        const clientValidationResult = validateSaleClient(payload);
        if (clientValidationResult.hasErrors) {
            if(clientValidationResult.hasExtraValidation) {
                clientValidationResult.customBtnLabel = 'Update Sale';
                clientValidationResult.hasErrors = false;
                clientValidationResult.showUpdateSaleMsg = true;
            }
            validationResult.value = clientValidationResult;
            validationResult.value.href = href.value;
            const shouldContinue = await showValidationErrors(clientValidationResult);
            if( shouldContinue && clientValidationResult.hasExtraValidation ){
                viewSale(sale.value.saleId, sale.value.qpid);
                toggleResultView.value = true;
            }
            return;
        }

        await validateSale();
        if (validationResult.value?.status !== 'SUCCESS') {
            await modal.showError('Error', 'Sale validation failed');
            return;
        }
        if (validationResult.value?.hasErrors || validationResult.value?.hasWarnings) {
            validationResult.value.href = href.value;
            const shouldSave = await showValidationErrors(
                { ...validationResult.value,
                    formattedErrors: formattedValidationResult.value?.errors ?? [],
                    formattedWarnings: formattedValidationResult.value?.warnings ?? [],
                    customBtnLabel: validationResult.value.hasExtraValidation ? 'Update Sale' : 'Confirm',
                    hasErrors: false,
                    showUpdateSaleMsg: true
                });
            if( shouldSave && validationResult.value?.hasExtraValidation) {
                viewSale(sale.value.saleId, sale.value.qpid);
                toggleResultView.value = true;
                return;
            }
            if (!shouldSave) {
                return;
            }
        }

        if (isSavingOwnershipData) {
            try {
                saving.value = true;
                const addressToSave = ownership.value.owners[0].address; // assuming order maintained
                const res = await updateOwners({
                    qpid: qpid.value,
                    owners: ownership.value.owners.map((owner) => {
                        delete owner.ownerId; // TODO: not necessary when owners soured from linz
                        owner.address = addressToSave;
                        return owner;
                    }),
                    userName: `QVNZ-${user.value.userName}`,
                    rfc: sale.value.rfc,
                });

                if (res.validations?.hasErrors) {
                    validationResult.value = res.validations;
                    validationResult.value.href = href.value;
                    await showValidationErrors({ ...validationResult.value, formattedErrors: formattedValidationResult.value?.errors ?? [], formattedWarnings: formattedValidationResult.value?.warnings ?? [] });
                    return;
                }
            }
            catch (error) {
                console.error('Something went wrong updating the owners');
                modal.showError('Error', 'Something went wrong updating the owners');
                return;
            }
            finally {
                saving.value = false;
                toggleResultView.value = false;
            }
        }

        try {
            sale.value.userName = user.value.userName;
            dvr.value.userName = user.value.userName;

            const payloadToSave = {
                sale: sale.value,
                dvr: dvr.value,
            };

            saving.value = true;
            const res = await controller.save(payloadToSave);
            sale.value = res.sale;
            dvr.value = res.dvr;
            results.value.total--;
            results.value.sales = results.value.sales.filter(rs => rs.saleId != sale.value.saleId);
            if (res.status !== 'SUCCESS') {
                const message = 'Something went wrong saving the sale';
                console.error(`${message}: ${res.message}`);
                await modal.showError('Error', message);
            }
        }
        catch (error) {
            console.error(error);
            exception.value = error;
            modal.showError('Error', 'Something went wrong saving the sale');
            return;
        }
        finally {
            saving.value = false;
        }
    }

    async function getDvr(id, current = false) {
        try {
            loading.value = true;
            const dvrResponse = await controller.getDvr(id, current);

            if (!dvrResponse) {
                exception.value = 'No dvr found';
                return;
            }

            if (!dvrResponse.audit) {
                populateRFC(dvrResponse);
            }

            dvr.value = dvrResponse;
            return dvr;
        } catch (e) {
            console.error(e);
            exception.value = e;
        } finally {
            loading.value = false;
        }
    }

    async function getTitles(saleId, qpids) {
        if (Array.isArray(qpids)) {
            qpids = qpids.join(',');
        }
        try {
            return await controller.getTitles(saleId, qpids);
        } catch (e) {
            console.error(e);
            exception.value = e;
        }
    }

    async function relinkSaleRfs() {
        try {
            saving.value = true;
            const payload = {
                saleId: saleId.value,
                relinkSaleId: sale.value.relinkSaleId,
            };

            return controller.relinkSaleRfs(payload);
        } catch (e) {
            console.error(e);
            exception.value = e;
            alert('Oops, Something went wrong!');
        } finally {
            saving.value = false;
        }
    }

    async function deleteSale() {
        try {
            const payload = {
                saleId: saleId.value,
                qpid: qpid.value,
                username: user.value.userName,
                rfc: sale.value.rfc,
            };

            saving.value = true;
            await controller.deleteSale(payload);
            results.value.total = results.value.total - 1;
            results.value.sales = results.value.sales.filter(rs => rs.saleId != sale.value.saleId);
        } catch (e) {
            console.error(e);
            exception.value = e;
            alert('Oops, something went wrong!');
        } finally {
            saving.value = false;
        }
    }

    async function addSaleInspectionConsent(payload) {
        addSaleInspectionConsentResult.value = {};
        try {
            saving.value = true;
            const res = await controller.addSaleInspectionConsent(payload);
            if (res.status !== 'ADDED') {
                throw new Error(res.message);
            }
            addSaleInspectionConsentResult.value = { status: 'SUCCESS', message: 'Sale inspection consent added successfully' };
        }
        catch (error) {
            console.error(error);
            const message = 'Oops, something went wrong!';
            addSaleInspectionConsentResult.value = { status: 'FAILED', message };
        }
        finally {
            saving.value = false;
        }
    }

    async function validateSale() {
        try {
            const payload = {
                sale: sale.value,
                dvr: dvr.value,
                isExpandedSaleView: true,
            };

            saving.value = true;
            const validation = await controller.validateSale(payload);
            if (validation?.status === 'SUCCESS') {
                validationResult.value = validation;
            } else {
                console.error('Non-successful sale validation response');
                validationResult.value = {
                    status: null,
                    hasErrors: null,
                    hasWarnings: null,
                    validations: null,
                    errors: null,
                    warnings: null,
                };
            }
        } catch (e) {
            console.error(e);
            exception.value = e;
            validationResult.value = {
                status: null,
                hasErrors: null,
                hasWarnings: null,
                validations: null,
                errors: null,
                warnings: null,
            };
            alert('Oops, something went wrong!');
        } finally {
            saving.value = false;
        }
    }

    async function onSaleTypeChanged(newValue, oldValue) {
        saleTypeChanged.value = true;
    }

    async function getLinzSaleWarnings() {
        try {
            const res = await controller.getLinzSaleWarnings(saleId.value);
            if (!res) {
                exception.value = `No LINZ Warnings found for sale ID : ${saleId.value}`;
                return;
            }
            linzWarnings.value = res.warnings ?? {};
        }
        catch (e) {
            console.error(e);
            exception.value = e;
        }
    }

    async function openSalePdf(noticeId, sourceId = 1 /*LINZ*/) {
        try {
            await controller.openSalesNoticePreview(noticeId, sourceId);
        }
        catch (error) {
            console.error(error);
            await modal.showError('Failed to download PDF', 'Something went wrong retrieving the sales notice PDF');
        }
    }

    function showValidationErrors(payload) {
        return modal.show(SaleValidationModal, payload);
    }

    function getErrorsForLabel(label) {
        return validationResult.value?.validations?.[label]?.errors ?? [];
    }

    function asciiDistanceFromA(char) {
        return char.charCodeAt(0) - 'A'.charCodeAt(0);
    }

    function decodeOccownLevel(occownLevel) {
        let order = asciiDistanceFromA(occownLevel[1]) + 1;
        if (isNaN(occownLevel[0])) {
            order += 26 * asciiDistanceFromA(occownLevel[0]);
        }

        return order;
    }

    function viewSale(saleId, qpid, validate = true) {
        const url = router.resolve({
            name: 'property-sale',
            params: {
                id: saleId,
                qpid,
            },
            query: {
                validate
            }
        });
        window.open(url.href, '_blank');
    }

    function getHref(saleId, qpid) {
        return router.resolve({
            name: 'property-sale',
            params: {
                id: saleId,
                qpid,
            },
            query: {
                validate: true,
            },
        }).href;
    }

    return {
        loading,
        saving,
        exception,
        dvr,
        sale,
        titles,
        primaryProperty,
        propertyInfoFull,
        ownership,
        failedInitialOwnershipValidation,
        qivsOwners,
        qivsOccupiers,
        addSaleInspectionConsentResult,
        validationResult,
        toggleResultView,
        isSaleToProcess,
        linzWarnings,
        getErrorsForLabel,
        loadSale,
        saveSale,
        deleteSale,
        relinkSaleRfs,
        addSaleInspectionConsent,
        nspByRtv,
        openSalePdf,
        viewSale,
    };
}

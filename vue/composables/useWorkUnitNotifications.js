import { ref, watch } from 'vue';
import { getPresetDatePeriod, getTodayDate, isPreviousFinancialYear } from '../components/dashboard/workUnit/utils.js';

const notifications = ref([]);
const dashboardUser = ref(null);
const selectedDateFilter = ref({ code: 'CurrentMonth', description: 'Current Month' });
const customRangeStatus = ref({ to: { type: '', message: '' }, from: { type: '', message: '' }, general: { type: '', message: '' }});
const selectedDateRange = ref({ from: null, to: null});
const errorMessage = ref('');

watch(dashboardUser, async () => {
    await loadNotifications();
});

watch(selectedDateFilter, async () => {
    await loadNotifications();
});

watch(customRangeStatus.value, async () => {
    await loadNotifications();
});

export default function useWorkUnitNotifications() {
    return {
        notifications,
        dashboardUser,
        selectedDateFilter,
        selectedDateRange,
        customRangeStatus,
        getWorkUnitNotifications,
        getValuationJobNotification,
    };
}

async function loadNotifications() {
    notifications.value = [{ type: 'info', message: 'Please select a valuer.' }];

    if (dashboardUser.value?.id && dashboardUser.value?.id !== 'Unassigned') {
        const [workUnitNotification, valuationJobNotification] = await Promise.all([getWorkUnitNotifications(dashboardUser.value.ntUsername), getValuationJobNotification(dashboardUser.value.id)]);
        notifications.value = [...workUnitNotification];
        if (valuationJobNotification) {
            notifications.value = [...notifications.value, ...valuationJobNotification];
        }
        if (selectedDateFilter.value?.code !== 'LastFinancialYear' && selectedDateFilter.value?.code !== 'LastFinancialQuarter' && !dashboardUser.value?.target) {
            notifications.value.push({
                type: 'warning',
                message: 'No Revenue Target assigned to the valuer. Please request servicedesk or your manager to update the relevant Revenue Target.',
            });
        }
        if (selectedDateFilter.value?.code === 'LastFinancialYear' || selectedDateFilter.value?.code === 'LastFinancialQuarter') {
            const datePeriod = getPresetDatePeriod(selectedDateFilter.value.code);
            const isPrevYear = isPreviousFinancialYear(selectedDateFilter.value.code, datePeriod.from);
            if (isPrevYear) {
                notifications.value.unshift({
                    type: 'info',
                    message: 'In the Ratings Segment, Sales, Revaluation and TLA Enquiry Revenue calculation is calculated based on current charge out rates since historical charge out rates are not maintained.',
                });
            }
            if (isPrevYear && !dashboardUser.value?.prevTarget) {
                notifications.value.push({
                    type: 'warning',
                    message: 'No Target set for the time period selected in the Time Period Filter.',
                });
            }
        }
        updateCustomRangeNotifications();
    }
}

async function updateCustomRangeNotifications() {
    const notificationTypes = ['general', 'from', 'to'];
    notificationTypes.forEach((key) => {
        const status = customRangeStatus.value?.[key];
        if (status?.type) {
            notifications.value.push({
                type: status.type,
                message: status.message,
            });
        }
    });
}

async function getWorkUnitNotifications(username) {
    try {
        const { url } = jsRoutes.controllers.StatsController.getWorkUnitNotifications(username);
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        return await res.json();
    } catch (error) {
        const message = 'Error calling stats api';
        console.error(message, error);
        errorMessage.value = message;
    }
}

async function getValuationJobNotification(valuerId) {
    try {
        const { url } = jsRoutes.controllers.HomeValuation.displayMarketValuationJobSearchResult();
        const res = await fetch(`${url}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({ userIds: [valuerId], isUnassigned: null, max: 1000 }),
        });
        const response = await res.json();
        const unfinishedJobCount = response.filter((job) => job.jobStatus !== 'Complete' && job.jobStatus !== 'Cancelled').length || 0;
        return unfinishedJobCount > 0 ? [{ type: 'warning', message: `You have ${unfinishedJobCount} unfinished Monarch Valuation Job${unfinishedJobCount > 1 ? 's' : ''}.` }] : [];
    } catch (error) {
        const message = 'Error calling home valuation job search result';
        console.error(message, error);
    }
}

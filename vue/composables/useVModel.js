import { computed, nextTick, ref, toRef, watch } from 'vue';
import { isEqual, debounce } from 'lodash';

/**
 * Two-way binding for props & v-model
 * @param props - Props object
 * @param emit - Emit function with the required events
 * @param key - Prop key to watch
 * @param options - Options
 * @returns {Ref<*>} - The model value
 *
 * @example v-model usage
 * ```
 * <script setup>
 *  const props = defineProps(['value']);
 *  const emit = defineEmits(['input', 'changed']);
 *  const modelValue = useVModel(props, emit, 'value');
 * </script>
 *
 * <component v-model="value">
 * ```
 *
 * @example prop binding usage
 * ```
 * <script setup>
 *     const props = defineProps(['someProp']);
 *     const emit = defineEmits(['update:someProp', 'changed']);
 *     const modelValue = useVModel(props, emit, 'someProp');
 * </script>
 *
 * <component :some-prop="value" @update:someProp="value = $event" />
 */
export function useVModel(props, emit, key = 'value', options = {}) {
    if (!props.hasOwnProperty(key)) {
        throw `The prop ${key} is not defined`;
    }

    if (!emit) {
        throw `The emit function is not defined`;
    }

    const prop = computed(() => props[key]);
    const modelValue = ref(structuredClone(props[key]));

    const debouncedOnChanged = debounce(onChanged, options.debounceTime || 0);
    function onChanged(value, oldValue) {
        if (prop.value === value) {
            return;
        }

        emit(key === 'value' ? `input` : `update:${key}`, structuredClone(value));

        nextTick(() => {
            emit('changed', key);
        })
    }

    function propChanged() {
        options.debug && console.log('propChanged', key, prop.value, modelValue.value);
        if (!isEqual(props.value, modelValue.value)) {
            modelValue.value = structuredClone(props[key]);
        }
    }

    watch(() => prop.value, propChanged, { deep: true });
    watch(() => modelValue.value, (newValue, oldValue) => debouncedOnChanged(newValue, oldValue), options);
    return modelValue;
}

import { ref } from 'vue';
import useValuerInfo from '@/composables/useValuerInfo';
import { completeObjectionJobValuation } from '@/services/ApiPicklistController.js';

const { findValuerByFullName } = useValuerInfo();

export function useJobCompletion(ratingValuationId, linkedObjection, riskType, currentReview) {

    const reviews = ref(null);
    const notesForReview = ref('');
    const validationAction = ref('');
    const requestingReview = ref(false);

    function requestReview() {
        requestingReview.value = true;
    }

    function saveDraft() {
        requestingReview.value = false;
    }

    async function fetchReviews() {
        const { url } = jsRoutes.controllers.ApiPicklistController.getObjectionJobReview(ratingValuationId.value);
        try {
            const res = await fetch(url);
            reviews.value = (await res.json()).reviews;
        }
        catch (error) {
            console.error('Failed to retrieve objection job reviews', error);
        }
    }

    async function sendToReview() {
        const { url } = jsRoutes.controllers.ApiPicklistController.addObjectionJobReview();
        try {
            const res = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify({
                    ratingValuationId: ratingValuationId.value,
                    objectionId: linkedObjection.value.objectionId,
                    valuer: findValuerByFullName(linkedObjection.value.valuer).ntUsername,
                    registeredValuer: findValuerByFullName(linkedObjection.value.registeredValuer).ntUsername,
                    notesForReviewer : notesForReview.value,
                    riskType: riskType.value
                }),
            });
            const body = await res.json();

            if (body.status !== 'ADDED') {
                console.error('Failed to request review', body);
                return;
            }

            await fetchReviews();
        }
        catch (error) {
            console.error('Failed to register review', error);
        }
    }

    async function completeReview(outcome) {
        const { url } = jsRoutes.controllers.ApiPicklistController.updateObjectionJobReview(ratingValuationId.value);
        const data = {
            ...currentReview.value,
            ...outcome,
        };
        try {
            const res = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify(data),
            });
            const body = await res.json();

            if (body.status !== 'UPDATED') {
                console.error('Failed to update review', body);
                return;
            }

            await fetchReviews();
        }
        catch (error) {
            console.error('Failed to update review', error);
        }
    }

    async function completeValuation() {
        try {
            const res = await completeObjectionJobValuation(ratingValuationId.value);
            if (res.status != 'SUCCESS') {
                console.error(`status: ${res.status}, ${res.message}`);
            }
        }
        catch (error) {
            console.error(error);
        }
    }


    return {
        reviews,
        requestingReview,
        notesForReview,
        validationAction,
        requestReview,
        saveDraft,
        sendToReview,
        fetchReviews,
        completeReview,
        completeValuation,
    };
}

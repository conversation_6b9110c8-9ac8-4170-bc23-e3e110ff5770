import { ref, watch } from 'vue';
import { debounce } from 'lodash';
import _ from 'underscore';

const controller = jsRoutes.controllers.PropertyController;

export function useAddressSearch(useOptions = {}) {
    const defaultOptions = {
        debounceTime: 500,
        max: 10,
        filter: () => true,
    };
    const options = {
        ...defaultOptions,
        ...useOptions,
    };

    const results = ref([]);
    const errored = ref(false);
    const searching = ref(false);
    const query = ref('');
    const debouncedSearch = debounce(search, options.debounceTime);

    watch(query, () => {
        searching.value = true;
        errored.value = false;
        debouncedSearch();
    });

    async function search() {
        try {
            const response = await fetch(controller.displayTypeAheadResult().url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: JSON.stringify({
                    basicSearchString: query.value,
                    taCodes: [],
                    sort: ['TA_CODE', 'STREET_NAME', 'STREET_TYPE', 'STREET_NUMBER', 'STREET_NUMBER_SUFFIX', 'VALUATION_REFERENCE'],
                    order: 'asc',
                    max: options.max,
                    ...options.filter,
                }),
            });

            const body = await response.json();
            results.value = typeof options.filter === 'function' ? _.filter(body, options.filter) : body;
            errored.value = false;
        }
        catch (e) {
            console.error(e);
            errored.value = true;
        }
        finally {
            searching.value = false;
        }
    }

    return {
        results,
        query,
        searching,
        errored,
    };
}

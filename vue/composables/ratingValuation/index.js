import { computed } from 'vue';

export * from './useValuationWorksheet.js';
export * from './useValuationWorksheetMaori.js';

export function createValuesObject() {
    return {
        capitalValue: null,
        landValue: null,
        valueOfImprovements: null,
    }
}

export function defineRoundingRules(roundingRules) {
    return function (value) {
        if (value === undefined || value === null) {
            return value;
        }

        if (!roundingRules) {
            console.error('No rounding rules defined');
            return value;
        }

        const rule  = roundingRules.find(rule => rule.from <= value && value <= rule.to);

        if (!rule) {
            return value;
        }

        const rounding = rule.rounding;
        return Math.floor(value / rounding) * rounding;
    }
}

export function calculateValuePerSquareMetre(row) {
    const area = row.areaInSquareMetres;
    const value = row.value;

    if (value === null) {
        row.valuePerSquareMetre = null;
        return;
    }

    if (isNaN(value/area) || area === 0) {
        row.valuePerSquareMetre = null;
        return;
    }

    row.valuePerSquareMetre = parseFloat((value / area).toFixed(4));
}

export function calculateValue(row) {
    const area = row.areaInSquareMetres;
    const rate = row.valuePerSquareMetre;

    if (!area && !rate) {
        return;
    }

    if (rate === null) {
        row.value = null;
        return;
    }

    if (isNaN(rate * area)) {
        row.value = null;
        return;
    }

    row.value = parseFloat((rate * area).toFixed(0));
}

export function calculateValueOfImprovement(row) {
    const capitalValue = row.capitalValue;
    const landValue = row.landValue;

    if (capitalValue == null || landValue == null) {
        row.valueOfImprovements = null;
        return
    }

    row.valueOfImprovements = capitalValue - landValue;
}

export function recalculateApportionmentRevisionValues(newValue, currentApportionmentValues) {
    const currentRevisedCV = currentApportionmentValues?.revisedValuation?.capitalValue;
    const currentRevisedLV = currentApportionmentValues?.revisedValuation?.landValue;

    newValue.adoptedRevisionValue.capitalValue =
        Math.round(
            (newValue.adoptedValue.capitalValue / currentApportionmentValues.currentValuation.capitalValue)
            * currentRevisedCV
            / 1000,
        ) * 1000;

    newValue.adoptedRevisionValue.landValue =
        Math.round(
            (newValue.adoptedValue.landValue / currentApportionmentValues.currentValuation.landValue)
            * currentRevisedLV
            / 1000,
        ) * 1000;

    if (newValue.adoptedRevisionValue.capitalValue != null && newValue.adoptedRevisionValue.landValue != null) {
        newValue.adoptedRevisionValue.valueOfImprovements = newValue.adoptedRevisionValue.capitalValue - newValue.adoptedRevisionValue.landValue;
    }

    return newValue;
}

export function getApportionmentValues(propertyDetail, ratingValuation) {
    return computed({
        get: () => {
            if (propertyDetail === undefined || propertyDetail === null) {
                return null;
            }
            let newApportionmentValues = null;
            if (propertyDetail.dvrSnapshot?.ratingApportionments?.length > 0) {
                // create new apportionments array
                newApportionmentValues = propertyDetail.dvrSnapshot?.ratingApportionments.map(r => ({
                    qpid: r.qpid,
                    suffix: r.suffix,
                    adoptedValue: { capitalValue: null, landValue: null, valueOfImprovements: null },
                    originalRevisionValue: { capitalValue: null, landValue: null, valueOfImprovements: null },
                    adoptedRevisionValue: { capitalValue: null, landValue: null, valueOfImprovements: null },
                    adoptedUnadjustedValue: { capitalValue: null, landValue: null, valueOfImprovements: null },
                    unadjustedRevisionValue: { capitalValue: null, landValue: null, valueOfImprovements: null },
                }));
            }

            if (ratingValuation.apportionmentValues?.length > 0) {
                // preserve existing values from ratingValuation.apportionmentValues
                newApportionmentValues?.forEach((newValue) => {
                    const existingApportionmentValues = ratingValuation.apportionmentValues.find(
                        oldValue =>
                            oldValue.qpid === newValue.qpid
                            && oldValue.suffix === newValue.suffix,
                    );

                    newValue.adoptedValue = {
                        capitalValue: existingApportionmentValues?.adoptedValue?.capitalValue,
                        landValue: existingApportionmentValues?.adoptedValue?.landValue,
                        valueOfImprovements: existingApportionmentValues?.adoptedValue?.valueOfImprovements,
                    };

                    newValue.adoptedRevisionValue = {
                        capitalValue: existingApportionmentValues?.adoptedRevisionValue?.capitalValue,
                        landValue: existingApportionmentValues?.adoptedRevisionValue?.landValue,
                        valueOfImprovements: existingApportionmentValues?.adoptedRevisionValue?.valueOfImprovements,
                    };

                    newValue.adoptedUnadjustedValue = {
                        capitalValue: existingApportionmentValues?.adoptedUnadjustedValue?.capitalValue,
                        landValue: existingApportionmentValues?.adoptedUnadjustedValue?.landValue,
                        valueOfImprovements: existingApportionmentValues?.adoptedUnadjustedValue?.valueOfImprovements,
                    };

                    newValue.unadjustedRevisionValue = {
                        capitalValue: existingApportionmentValues?.unadjustedRevisionValue?.capitalValue,
                        landValue: existingApportionmentValues?.unadjustedRevisionValue?.landValue,
                        valueOfImprovements: existingApportionmentValues?.unadjustedRevisionValue?.valueOfImprovements,
                    };
                });
            }

            // get current values for originalRevisionValue, adoptedRevisionValue
            // and adoptedUnadjustedValue, where applicable
            if (propertyDetail.dvrSnapshot?.ratingApportionments?.length > 0) {
                newApportionmentValues?.forEach((newValue) => {
                    const currentApportionmentValues = propertyDetail.dvrSnapshot.ratingApportionments.find(
                        oldValue =>
                            oldValue.qpid === newValue.qpid
                            && oldValue.suffix === newValue.suffix,
                    );

                    const currentRevisedCV = currentApportionmentValues?.revisedValuation?.capitalValue;
                    const currentRevisedLV = currentApportionmentValues?.revisedValuation?.landValue;
                    const currentRevisedVI = (
                        currentRevisedCV !== null && currentRevisedLV !== null
                            ? currentRevisedCV - currentRevisedLV
                            : null
                    );

                    newValue.originalRevisionValue = {
                        capitalValue: currentRevisedCV,
                        landValue: currentRevisedLV,
                        valueOfImprovements: currentRevisedVI,
                    };

                    const currentUnadjustedRevisedCV = currentApportionmentValues?.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation?.capitalValue;
                    const currentUnadjustedRevisedLV = currentApportionmentValues?.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation?.landValue;
                    const currentUnadjustedRevisedVI = (
                        currentUnadjustedRevisedCV !== null && currentUnadjustedRevisedLV !== null
                        ? currentUnadjustedRevisedCV - currentUnadjustedRevisedLV
                        : null
                    );

                    newValue.originalUnadjustedRevisionValue = {
                        capitalValue: currentUnadjustedRevisedCV,
                        landValue: currentUnadjustedRevisedLV,
                        valueOfImprovements: currentUnadjustedRevisedVI,
                    };

                    if (ratingValuation.isMaoriLand) {
                        if (newValue.adoptedUnadjustedValue.capitalValue === null ||
                            typeof (newValue.adoptedUnadjustedValue.capitalValue) === 'undefined' ||
                            newValue.adoptedUnadjustedValue.landValue === null ||
                            typeof (newValue.adoptedUnadjustedValue.landValue) === 'undefined'
                        ) {
                            const currentAdoptedUnadjustedCV = currentApportionmentValues?.unadjustedValuation?.capitalValue;
                            const currentAdoptedUnadjustedLV = currentApportionmentValues?.unadjustedValuation?.landValue;
                            const currentAdoptedUnadjustedVI = (
                                currentAdoptedUnadjustedCV !== null && currentAdoptedUnadjustedLV != null
                                    ? currentAdoptedUnadjustedCV - currentAdoptedUnadjustedLV
                                    : null
                            );

                            newValue.adoptedUnadjustedValue = {
                                capitalValue: currentAdoptedUnadjustedCV,
                                landValue: currentAdoptedUnadjustedLV,
                                valueOfImprovements: currentAdoptedUnadjustedVI,
                            };
                        }

                        const currentParentUnadjustedValue = ratingValuation?.unadjustedValue;
                        if (newValue.unadjustedRevisionValue.capitalValue === null || newValue.unadjustedRevisionValue.capitalValue === undefined) {
                            newValue.unadjustedRevisionValue.capitalValue =
                                Math.round(
                                    (newValue.adoptedUnadjustedValue.capitalValue / currentParentUnadjustedValue.capitalValue)
                                    * ratingValuation.unadjustedRevisionValue.capitalValue
                                    / 1000,
                                ) * 1000;
                        }
                        if (newValue.unadjustedRevisionValue.landValue === null || newValue.unadjustedRevisionValue.landValue === undefined) {
                            newValue.unadjustedRevisionValue.landValue =
                                Math.round(
                                    (newValue.adoptedUnadjustedValue.landValue / currentParentUnadjustedValue.landValue)
                                    * ratingValuation.unadjustedRevisionValue.landValue
                                    / 1000,
                                ) * 1000;
                        }
                        if (newValue.unadjustedRevisionValue.capitalValue != null && newValue.unadjustedRevisionValue.landValue != null) {
                            newValue.unadjustedRevisionValue.valueOfImprovements = newValue.unadjustedRevisionValue.capitalValue - newValue.unadjustedRevisionValue.landValue;
                        }
                    }

                    if (!ratingValuation.isMaoriLand) {
                        if (newValue.adoptedRevisionValue.capitalValue === null ||
                            typeof (newValue.adoptedRevisionValue.capitalValue) === 'undefined' ||
                            newValue.adoptedRevisionValue.landValue === null ||
                            typeof (newValue.adoptedRevisionValue.landValue) === 'undefined'
                        ) {
                            newValue = recalculateApportionmentRevisionValues(newValue, currentApportionmentValues);
                        }
                    }
                });
            }

            ratingValuation.apportionmentValues = newApportionmentValues;
            return ratingValuation.apportionmentValues;
        },
        set: (value) => {
            ratingValuation.apportionmentValues = value;
        },
    });
}

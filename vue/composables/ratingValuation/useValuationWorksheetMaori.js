import { calculateValueOfImprovement, createValuesObject, defineRoundingRules, getApportionmentValues, useValuationWorksheet } from '@/composables/ratingValuation';
import { computed, set, unref, watch } from 'vue';

function validateMaoriRatingValuation(ratingValuation) {
    if (!ratingValuation.unadjustedValue) {
        set(ratingValuation, 'unadjustedValue', createValuesObject());
    }

    if (!ratingValuation.unadjustedRevisionValue) {
        set(ratingValuation, 'unadjustedRevisionValue', createValuesObject());
    }

    if (!ratingValuation.adoptedRevisionValue) {
        set(ratingValuation, 'adoptedRevisionValue', createValuesObject());
    }

    if (!ratingValuation.maoriLandData) {
        set(ratingValuation, 'maoriLandData',
            {
                currentMaoriLandAdjustment: {
                    multipleOwnerAdjustmentPercentage: null,
                    siteSignificanceAdjustmentPercentage: null,
                },
                numberOfOwners: null,
                revisedMaoriLandAdjustment: {
                    multipleOwnerAdjustmentPercentage: null,
                    siteSignificanceAdjustmentPercentage: null,
                },
            });
    }
}

export function validateAdjustedValue(value, originalValue) {
    if (!originalValue) {
        return 0;
    }

    if (originalValue > 0 && value < 100) {
        return 100;
    }

    return value;
}

export function recalculateRatingValues(unadjusted, lumpSum, adjustment, mangatuRoundingRules) {
    if (!unadjusted) {
        return;
    }

    const adjustmentValue = adjustment/100;

    const unadjustedCV = unadjusted.capitalValue;
    const unadjustedLV = unadjusted.landValue;

    const cv = unadjustedCV - lumpSum;
    const lv = unadjustedLV - lumpSum;
    const vi = cv - lv;

    let adjustedLV = lv - (lv * adjustmentValue);
    let adjustedVI = vi - (vi * adjustmentValue);

    adjustedLV = mangatuRoundingRules(adjustedLV);
    adjustedVI = mangatuRoundingRules(adjustedVI);
    let adjustedCV = adjustedLV + adjustedVI;

    adjustedCV = validateAdjustedValue(adjustedCV, unadjustedCV);
    adjustedLV = validateAdjustedValue(adjustedLV, unadjustedLV);
    adjustedVI = adjustedCV - adjustedLV;

    return {
        capitalValue: adjustedCV,
        landValue: adjustedLV,
        valueOfImprovements: adjustedVI,
    };
}

export function useValuationWorksheetMaori(data, propertyData, propertyDetailData) {
    const propertyDetail = unref(propertyDetailData);
    const valuationWorksheet = useValuationWorksheet(data, propertyData);
    const {
        ratingValuation,
        property,
        roundByTARules,
    } = valuationWorksheet;
    const roundByMangatuRules = defineRoundingRules(ratingValuation.roundingValues);
    validateMaoriRatingValuation(ratingValuation);
    const maoriLandValues = computed({
        get: () => ratingValuation.maoriLandData,
        set: (value) => {
            ratingValuation.maoriLandData = value;
        },
    });
    const currentAdjustmentValue = computed({
        get: () => ratingValuation.maoriLandData.currentMaoriLandAdjustment,
        set: (value) => {
            ratingValuation.maoriLandData.currentMaoriLandAdjustment = value;
            recalculateCurrentRatingValues();
        },
    });
    const revisionAdjustmentValue = computed({
        get: () => ratingValuation.maoriLandData.revisedMaoriLandAdjustment,
        set: (value) => {
            ratingValuation.maoriLandData.revisedMaoriLandAdjustment = value;
            recalculateRevisionRatingValues();
        },
    });

    const unadjustedValue = computed({
        get: () => ratingValuation.unadjustedValue,
        set: (value) => {
            ratingValuation.unadjustedValue = value;
            recalculateCurrentRatingValues();
        },
    });

    const unadjustedRevisionValue = computed({
        get: () => ratingValuation.unadjustedRevisionValue,
        set: (value) => {
            calculateValueOfImprovement(value);
            ratingValuation.unadjustedRevisionValue = value;
            recalculateRevisionRatingValues();
        },
    });

    const ratingValue = computed({
        get: () => ratingValuation.adoptedValue,
        set: (value) => {
            ratingValuation.adoptedValue = value;
        },
    });

    const revisionRatingValue = computed({
        get: () => ratingValuation.adoptedRevisionValue,
        set: (value) => {
            for (const key in value) {
                value[key] = roundByMangatuRules(value[key]);
            }
            ratingValuation.adoptedRevisionValue = value;
        },
    });

    const worksheetValues = computed({
        get: valuationWorksheet.calculateWorksheetValues,
        set: (value) => {
            ratingValuation.calculatedValue = value;
            unadjustedValue.value = recalculateUnadjustedValues(value);
        },
    });

    const totalCurrentAdjustment = computed(calculateCurrentTotalAdjustment);
    const totalRevisionAdjustment = computed(calculateRevisionTotalAdjustment);
    const totalOriginalRevisionAdjustment = computed(calculateOriginalRevisionTotalAdjustment);
    const originalLumpSumRevision = computed(() => ratingValuation.lumpSumRevision);

    const originalMaoriRevisionValue = computed(() => {
        const value = property.maoriLandData.revisedMaoriLandAdjustment.unadjustedValuation;
        calculateValueOfImprovement(value);
        return value;
    });

    const originalUnadjustedRevisionValue = computed(() => {
        const value = property.maoriLandData.currentMaoriLandAdjustment.unadjustedValuation;
        calculateValueOfImprovement(value);
        return value;
    });

    const hasSraValues = computed( () => {
        return ratingValuation?.sra?.sras?.length > 0;
    });

    const hasSraRevision = computed(() => {
        return ratingValuation?.sra?.revisionCapitalValue > 0 && ratingValuation?.sra?.revisionLandValue > 0;
    })

    function recalculateCurrentRatingValues() {
        ratingValue.value = recalculateRatingValues(
            unadjustedValue.value,
            ratingValuation.lumpSum,
            totalCurrentAdjustment.value,
            roundByMangatuRules,
        );
    }

    function recalculateRevisionRatingValues() {
        revisionRatingValue.value = recalculateRatingValues(
            unadjustedRevisionValue.value,
            ratingValuation.lumpSumRevision,
            totalRevisionAdjustment.value,
            roundByMangatuRules,
        );
    }

    function calculateCurrentTotalAdjustment() {
        const currentMaoriLandAdjustment = currentAdjustmentValue.value;
        let totalAdjustment = 0;
        if (currentMaoriLandAdjustment) {
            totalAdjustment += parseFloat(currentMaoriLandAdjustment.multipleOwnerAdjustmentPercentage) || 0;
            totalAdjustment += parseFloat(currentMaoriLandAdjustment.siteSignificanceAdjustmentPercentage) || 0;
        }
        return totalAdjustment;
    }

    function calculateRevisionTotalAdjustment() {
        const revisedMaoriLandAdjustment = revisionAdjustmentValue.value;
        let totalAdjustment = 0;
        if (revisedMaoriLandAdjustment) {
            totalAdjustment += parseFloat(revisedMaoriLandAdjustment.multipleOwnerAdjustmentPercentage) || 0;
            totalAdjustment += parseFloat(revisedMaoriLandAdjustment.siteSignificanceAdjustmentPercentage) || 0;
        }
        return totalAdjustment;
    }

    function calculateOriginalRevisionTotalAdjustment() {
        const revisedMaoriLandAdjustment = property.maoriLandData.revisedMaoriLandAdjustment;
        let totalAdjustment = 0;
        if (revisedMaoriLandAdjustment) {
            totalAdjustment += parseFloat(revisedMaoriLandAdjustment.multipleOwnerAdjustmentPercentage) || 0;
            totalAdjustment += parseFloat(revisedMaoriLandAdjustment.siteSignificanceAdjustmentPercentage) || 0;
        }
        return totalAdjustment;
    }

    function recalculateUnadjustedValues(value) {
        value.capitalValue = roundByTARules(value.capitalValue);
        value.landValue = roundByTARules(value.landValue);
        calculateValueOfImprovement(value);

        return value;
    }

    watch(
        () => maoriLandValues.value,
        (value) => {
            ratingValuation.maoriLandData = value;
        },
        { deep: true },
    );

    const apportionmentValues = getApportionmentValues(propertyDetail, ratingValuation);

    return {
        ...valuationWorksheet,
        maoriLandValues,
        unadjustedValue,
        unadjustedRevisionValue,
        ratingValue,
        revisionRatingValue,
        totalCurrentAdjustment,
        totalRevisionAdjustment,
        currentAdjustmentValue,
        revisionAdjustmentValue,
        originalMaoriRevisionValue,
        originalUnadjustedRevisionValue,
        totalOriginalRevisionAdjustment,
        originalLumpSumRevision,
        apportionmentValues,
        worksheetValues,
        hasSraValues,
        hasSraRevision,
    };
}

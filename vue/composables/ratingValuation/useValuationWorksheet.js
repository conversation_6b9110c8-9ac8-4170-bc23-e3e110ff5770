import { computed, ref, unref, watch, set } from 'vue';
import { defineRoundingRules, getApportionmentValues } from '@/composables/ratingValuation';

export const VALUATION_COMPONENTS = Object.freeze({
    PRIMARY_BUILDING: 'PRIMARY_BUILDING',
    OTHER_BUILDING: 'OTHER_BUILDING',
    OTHER_IMPROVEMENT: 'OTHER_IMPROVEMENT',
    LAND: 'LAND',
    WORKSHEET_VALUES: 'WORKSHEET_VALUES',
    ADOPTED_VALUES: 'ADOPTED_VALUES',
    REVISION_VALUES: 'REVISION_VALUES',
    ADOPTED_REVISION_VALUES: 'ADOPTED_REVISION_VALUES',
    MAORI_LAND: 'MAORI_LAND',
});

export function useValuationWorksheet(ratingValuationData, propertyData, propertyDetailData) {
    const ratingValuation = unref(ratingValuationData);
    const property = unref(propertyData);
    const propertyDetail = unref(propertyDetailData);
    const roundByTARules = defineRoundingRules(ratingValuation.taRoundingValues);

    function getComponentByType(type) {
        if (!ratingValuation) {
            return [];
        }

        return ratingValuation.ratingValuationComponents.filter(
            component => component.componentType === type,
        );
    }

    const primaryBuildings = ref(getComponentByType(VALUATION_COMPONENTS.PRIMARY_BUILDING));
    const otherBuildings = ref(getComponentByType(VALUATION_COMPONENTS.OTHER_BUILDING));
    const otherImprovements = ref(getComponentByType(VALUATION_COMPONENTS.OTHER_IMPROVEMENT));
    const land = ref(getComponentByType(VALUATION_COMPONENTS.LAND));
    const adoptedRevisionValue = ref(calculateAdoptedRevisionValues(ratingValuation, property));
    const worksheetComponents = computed(() => {
        return  [
            ...primaryBuildings.value,
            ...otherBuildings.value,
            ...otherImprovements.value,
            ...land.value,
        ].filter(component => component.buildingType
            || component.description
            || component.areaInSquareMetres > 0
            || component.valuePerSquareMetre > 0
            || component.value > 0
        );
    });

    const worksheetValues = computed({
        get: calculateWorksheetValues,
        set: (value) => {
            ratingValuation.calculatedValue = value;
        },
    });

    const apportionmentValues = getApportionmentValues(propertyDetail, ratingValuation);

    const hasSraValues = computed( () => {
        return ratingValuation?.sra?.sras?.length > 0;
    });

    const hasSraRevision = computed(() => {
        return ratingValuation?.sra?.revisionCapitalValue > 0 && ratingValuation?.sra?.revisionLandValue > 0;
    })

    function calculateWorksheetValues() {
        if (!ratingValuation) {
            return {
                capitalValue: null,
                landValue: null,
                valueOfImprovements: null,
            };
        }

        const capitalValue = worksheetComponents.value
            .map(row => row.value)
            .reduce((total, current) => total + current, 0);
        const landValue = land.value
            .map(row => row.value)
            .reduce((total, current) => total + current, 0);
        const valueOfImprovements = capitalValue - landValue;

        return {
            capitalValue,
            landValue,
            valueOfImprovements,
        };
    }

    function calculateRevisionValues(values) {
        if (!values) {
            return {
                capitalValue: null,
                landValue: null,
                valueOfImprovements: null,
            };
        }

        const { capitalValue, landValue } = values;

        if (capitalValue == null || landValue == null) {
            return {
                capitalValue,
                landValue,
                valueOfImprovements: null,
            };
        }

        const valueOfImprovements = capitalValue - landValue;
        return {
            capitalValue,
            landValue,
            valueOfImprovements,
        };
    }

    function calculateAdoptedRevisionValues(ratingValuation, property) {
        // run calculateRevisionValues and return if values are already saved in the job
        const adoptedRevVal = calculateRevisionValues(ratingValuation.adoptedRevisionValue);
        if (adoptedRevVal.capitalValue && adoptedRevVal.landValue) {
            ratingValuation.adoptedRevisionValue = adoptedRevVal;
            return ratingValuation.adoptedRevisionValue;
        }

        // else
        return recalculateRevisionValues(ratingValuation, property);
    }

    function calculateRoundedAdoptedValues() {
        const worksheetValues = calculateWorksheetValues();
        const capitalValue = roundByTARules(worksheetValues.capitalValue);
        const landValue = roundByTARules(worksheetValues.landValue);

        ratingValuation.adoptedValue = {
            capitalValue,
            landValue,
            valueOfImprovements: capitalValue - landValue,
        };
    }

    function onComponentsChanged() {
        set(ratingValuation, 'ratingValuationComponents', worksheetComponents.value);
        calculateRoundedAdoptedValues();
    }

    function onAdoptedRevisionValueChanged(value) {
        ratingValuation.adoptedRevisionValue = value;
    }

    watch([primaryBuildings, otherBuildings, otherImprovements, land], onComponentsChanged, { deep: true });
    watch(() => adoptedRevisionValue.value, onAdoptedRevisionValueChanged, { deep: true });

    return {
        ratingValuation,
        property,
        primaryBuildings,
        otherBuildings,
        otherImprovements,
        land,
        worksheetComponents,
        worksheetValues,
        adoptedRevisionValue,
        get hasRevisionValue() {
            return ratingValuation?.hasRevisionValue;
        },
        get originalRevisionValue() {
            return calculateRevisionValues(ratingValuation?.originalRevisionValue);
        },
        calculateRoundedAdoptedValues,
        calculateWorksheetValues,
        onComponentsChanged,
        apportionmentValues,
        roundByTARules,
        hasSraValues,
        hasSraRevision,
    };

}

export function recalculateRevisionValues(ratingValuation, property) {
    const roundByTARules = defineRoundingRules(ratingValuation.taRoundingValues);

    if (ratingValuation?.adoptedValue &&
        ratingValuation?.originalRevisionValue &&
        property.currentValuation?.capitalValue &&
        property.currentValuation?.landValue
    ) {
        const capitalValue = roundByTARules(
            (ratingValuation.adoptedValue.capitalValue / property.currentValuation.capitalValue)
            * ratingValuation.originalRevisionValue.capitalValue
        );
        const landValue = roundByTARules(
            (ratingValuation.adoptedValue.landValue / property.currentValuation.landValue)
            * ratingValuation.originalRevisionValue.landValue
        );
        const valueOfImprovements = capitalValue - landValue;

        ratingValuation.adoptedRevisionValue = {
            capitalValue,
            landValue,
            valueOfImprovements,
        };
        return ratingValuation.adoptedRevisionValue;
    } else {
        return {
            capitalValue: null,
            landValue: null,
            valueOfImprovements: null,
        }
    }
}

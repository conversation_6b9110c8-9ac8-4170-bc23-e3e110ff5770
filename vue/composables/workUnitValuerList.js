
import { ref, onMounted } from 'vue';
import { getPicklistValue } from '../services/ApiPicklistController';

const workUnitValuerList = ref([])

export function useWorkUnitValuerList() {
    onMounted(async () => {
        if (workUnitValuerList.value.length === 0) {
            workUnitValuerList.value = await getWorkUnitValuers();
        }}
    );

    return workUnitValuerList;
}

export async function getWorkUnitValuers() {
    try {
        const result = await getPicklistValue('WorkUnitValuer');
        return result.result.WorkUnitValuer;
    }
    catch (error) {
        const message = 'Error calling controller to get Work Unit Valuers';
        errorMessage.value = message;
    }
}


export const currentFinancialYear = () =>{
    const today = new Date();
    const start = new Date(today.getFullYear() - (today.getMonth() > 5 ? 0 : 1) - 4, 6, 1);
    const end = new Date(start.getFullYear() + 1, 6, 1);

    return {
        start,
        end
    };
}

export const currencyFormatter = new Intl.NumberFormat('en-NZ', {
    style: 'currency',
    currency: 'NZD',
});

export function formatCurrency(amount, blankText = '-') {
    return amount === null || amount === undefined ? blankText : currencyFormatter.format(amount);
}

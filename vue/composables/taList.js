
import { ref, onMounted } from 'vue';

const taList = ref([]);

export function useTaList() {
    onMounted(async () => {
        if (taList.value.length === 0) {
            taList.value = await getTerritorialAuthorities();
        }
    });

    return taList;
}

export async function getTerritorialAuthorities() {
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getPicklistValue('TerritorialAuthority');
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        const result = await res.json();
        return result.result.TerritorialAuthority;
    }
    catch (error) {
        const message = 'Error calling controller to get Territorial Authorities';
        console.error(message, error);
        errorMessage.value = message;
    }
}

export function taCodeToId(code, inputTaList) {
    const taObject = inputTaList.find(ta => Number(ta.code) === Number(code));
    return taObject?.id || null;
}

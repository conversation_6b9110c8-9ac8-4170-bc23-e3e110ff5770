import { ref } from 'vue';
import Vue from 'vue';
import AsyncConfirmationModal from 'Common/modal/dialog/AsyncConfirmationModal.vue';

function deferredPromise() {
    let resolve;
    let reject;
    const promise = new Promise((res, rej) => {
        resolve = res;
        reject = rej;
    });
    return {
        promise,
        resolve,
        reject,
    };
}

const dialog = ref(null);
const modal = ref(null);
const payload = ref({});
const promise = ref(null);

export default function useModal() {
    const show = async (component, data = {}) => {
        if (!component || !dialog.value) {
            console.warn("Tries to open modal without a component or dialog");
            return;
        }

        payload.value = data;
        modal.value = Vue.extend(component);
        dialog.value.showModal();
        promise.value = deferredPromise();
        return promise.value.promise;
    };

    const showSuccess = async (title, message) => {
        return show(AsyncConfirmationModal, {
            title,
            message,
            confirmText: 'OK',
            onlyConfirm: true,
        });
    }

    const showInfo = async (title, message, opts = {}) => {
        return show(AsyncConfirmationModal, {
            title,
            message,
            ...opts
        });
    }

    const showWarning = async (title, message, opts = {}) => {
        return show(AsyncConfirmationModal, {
            title,
            message,
            isWarning: true,
            ...opts,
        });
    }

    const showError = async (title, message, opts = {}) => {
        return show(AsyncConfirmationModal, {
            title,
            message,
            isError: true,
            ...opts,
        });
    }

    function close(confirmed) {
        promise.value.resolve(confirmed);
        dialog.value.close();
        modal.value = null;
    }

    function clickToClose(e) {
        const dialogDimensions = dialog.value.getBoundingClientRect();
        if (
            e.clientX < dialogDimensions.left
            || e.clientX > dialogDimensions.right
            || e.clientY < dialogDimensions.top
            || e.clientY > dialogDimensions.bottom
        ) {
            close(false);
        }
    }

    return {
        dialog,
        payload,
        modal,
        show,
        showSuccess,
        showInfo,
        showWarning,
        showError,
        close,
        clickToClose,
    };
}

@for $i from 1 through 6 {
  .flex-grow-#{$i} {
    flex-grow: $i;
  }
  .gap-#{$i} {
    gap: #{$i}rem;
  }
}

.flex-table {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
  width: 100%;
}

@mixin selectorMixin {
  @content;

  &\:hover:hover {
    @content;
  }

  &\:active:active {
    @content;
  }

  &\:focus:focus {
    @content;
  }

  &\:first-of-type:first-of-type {
    @content;
  }

  &\:disabled:disabled {
    @content;
  }
}

.flex-end {
  justify-content: end;
}

/**
 * Generates directional sizing class suffixes, {direction}-{size index}
 * Also includes x & y suffixes.
 * l-0, l-1, l-2... etc
 */
@mixin spacingMixin($property) {
  $spacing: 1rem;

  @include sizeLoop($spacing, #{$property});
  @include sizeLoop($spacing, #{$property}-left, "l");
  @include sizeLoop($spacing, #{$property}-right, "r");
  @include sizeLoop($spacing, #{$property}-top, "t");
  @include sizeLoop($spacing, #{$property}-bottom, "b");

  @include sizeLoop($spacing, #{$property}-top, "y");
  @include sizeLoop($spacing, #{$property}-bottom, "y");

  @include sizeLoop($spacing, #{$property}-left, "x");
  @include sizeLoop($spacing, #{$property}-right, "x");
}

/**
 * Used to loop through different rations of a default size
 */
@mixin sizeLoop($defaultSize, $property, $prefix:"") {
  $ratios: [0, .5, 1, 2, 3];

  @for $i from 1 through length($ratios) {
    $size: nth($ratios, $i);
    $value: $defaultSize * $size;
    $suffix: $i - 1;

    @if $prefix {
      &#{$prefix}-#{$suffix} {
        #{$property}: $value;
        @content
      }
    } @else {
      &-#{$suffix} {
        #{$property}: $value;
        @content
      }
    }
  }
}


@mixin widthMixin($width, $denominator) {
  &-#{$width}\/#{$denominator} {
    min-width: calc(100% / $denominator * $width);
    max-width: calc(100% / $denominator * $width);
    margin: 0;
  }
}

@mixin columnMixin($columns) {
  @for $i from 1 through $columns {
    @include widthMixin($i, $columns);
  }
}

@mixin color($name, $color) {
  @include colorMixin('color', 'color', $color, $name);
  @include colorMixin('background-color', 'bg', $color, $name);

  @include colorMixin('outline-color', 'outline', $color, $name);
  @include colorMixin('border-color', 'border', $color, $name);
  @include colorMixin('border-bottom-color', 'border-bottom', $color, $name);
  @include colorMixin('border-top-color', 'border-top', $color, $name);
}

@mixin colorMixin($key, $prefix, $color, $name) {
  &-#{$prefix}-#{$name} {
    @include selectorMixin {
      #{$key}: $color;
    }

    &\:10\% {
      @include selectorMixin {
        #{$key}: rgba($color, 0.1);
      }
    }


    &\:25\% {
      @include selectorMixin {
        #{$key}: rgba($color, 0.2);
      }
    }

    &\:50\% {
      @include selectorMixin {
        #{$key}: rgba($color, 0.5);
      }
    }

    &\:75\% {
      @include selectorMixin {
        #{$key}: rgba($color, 0.75);
      }
    }
  }
}
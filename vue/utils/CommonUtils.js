import numeral from 'numeral';
import { store } from '../DataStore.js';
import moment from 'moment';

export default {
    methods: {
        handleDragStart: function (e) {
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text', e.target.innerHTML);
            this.dragSrcEl_ = e.target;
            e.target.style.opacity = '0.5';
            $(e.target).addClass('moving');
        },
        handleDragOver: function (e) {
            if (e.preventDefault) {
                e.preventDefault(); // Allows us to drop.
            }
            e.dataTransfer.dropEffect = 'move';
            return false;
        },
        handleDragEnter: function (e) {
            $(e.target).addClass('over');
        },
        handleDragLeave: function (e) {
            $(e.target).removeClass('over');
        },
        handleDragEnd: function (e) {
            e.target.style.opacity = '1';
            [].forEach.call(this.boxes_, function (box) {
                $(box).removeClass('over');
                $(box).removeClass('moving');
            });
        },
        initQivsLink,
        between: function(x, min, max) {
            return x >= min && x <= max;
        },
        getEarthquakeRating: function(rating) {
            var ratingCode = null;
            var ratingCodeClass = null;
            if(rating) {
                if (this.between(rating, 0, 20)) {
                    ratingCode = '000';
                } else if(this.between(rating, 21, 33)) {
                    ratingCode = '021';
                } else if(this.between(rating, 34, 66)) {
                    ratingCode = '034';
                } else if(this.between(rating, 67, 100)) {
                    ratingCode = '067';
                } else if(this.between(rating, 101, 999)) {
                    ratingCode = '100';
                }

                if(ratingCode) {
                    var classificationList = this.getClassifications('EarthquakeRating');
                    var exists = classificationList.filter(function (e) {
                        return e.code == ratingCode;
                    });
                    if (exists.length > 0) {
                        ratingCodeClass =  exists[0];
                    }
                }
            }
            return ratingCodeClass;
        },
        getClassifications: function(category) {
            var self = this;
            var values = self.$store.getters.getCategoryClassifications(category);
            if(!values)
                console.error('[Monarch error]: No classifications loaded for category ' + category);
            return values;
        },
        getClassificationObject: function(category, code) {
            var values = this.getClassifications(category);

            /* Shouldnt happen but if it does then log and continue rather than breaking on filter. */
            if(!values) {
                console.error('[Monarch error]: No classifications loaded when trying to find code ' + code + ' for category ' + category);
                return null;
            }

            var found = values.filter(function(e) { return e.code == code; });
            return found.length > 0 ? found[0] : null;
        },
        initTabs: function() {
            $('body').off("click touchstart").on('click touchstart', '.tabs li',function() {
                // store the CSS name of the container in a variable
                var _ContainerToShow = '.'+$(this).attr('data-container');
                // hide all existing data containers except current target
                $('.tabBlock-wrapper')
                    .not(_ContainerToShow+':visible')
                    .slideUp(); // slide up every container except the one we want to see
                if(!$(_ContainerToShow).is(':visible')) { // if the container is NOT visible, slide it down
                    $(_ContainerToShow).slideDown();
                }

                $(this) .closest('ul') // find the parent UL
                    .find('li > span').removeClass();// clear out the active class on all LI > SPAN elements

                $(this) .find('span')
                    .addClass('is-active'); // add the active class to the clicked LI's SPAN

                if($(this).attr('data-filter')) {
                    // store the CSS name of the filter value in a variable
                    var _FilterToShow = '.'+$(this).attr('data-filter');
                    $(_ContainerToShow)
                        .find('.tabBlock-wrapper li')
                        .not(_FilterToShow)
                        .slideUp();

                } else {
                    $(_ContainerToShow)
                        .find('.tabBlock-wrapper li')
                        .slideDown(500);
                }
            });
        },
        checkImage: function(src) {
            return $("<img>").attr('src', src);
        },
        errorHandler: function(response) {
            console.error('[Monarch error]: Unexpected error response attempting AJAX request', response);
            // Authorisation failures attempt to redirect to Auth0 login page, which is not allowed by CORS, resulting in a 0 status.
            // Display login page in this scenario.
            if(response && (response.status == 0)) {
                window.location = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?home=true';
            }
        },
        sanitize: function(str) {
            var entityMap = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#39;',
                '/': '&#x2F;',
                '`': '&#x60;',
                '=': '&#x3D;'
            };
            return str.replace(/[&<>"'\/]/g, function (s) {
                return entityMap[s];
            });
        },
        isHtml: function(str) {
            return /<[a-z][\s\S]*>/i.test(str);
        },
        errorClasses(field) {
            const matchingErrors = this.findObject(this.errors, 'field', field);
            const x = { 'error': matchingErrors && matchingErrors.length > 0 };
            return x;
        },
        /* TODO should be encapsulated with the concept of a validationSet */
        validationClass(validationSet, field) {
            if(validationSet && validationSet.errors) {
                const matchingErrors = this.findObject(validationSet.errors, 'field', field);
                return { 'error': matchingErrors && matchingErrors.length > 0 };
            }
            return {error: false};
        },
        findObject: function findObject(object, propertyName, propertyValue) {
            let result = [];
            if (!object) throw new Error('object not passed');
            if (object.hasOwnProperty(propertyName) && object[propertyName] === propertyValue) {
                result.push(object);
            }

            for(var i = 0; i < Object.keys(object).length; i += 1) {
                if (typeof object[Object.keys(object)[i]] === "object") {
                    result = [
                        ...result,
                        ...findObject(object[Object.keys(object)[i]], propertyName, propertyValue)
                    ];
                }
            }
            return result;
        }
    }
};

export function initQivsLink(qivsUrl, code, urlParam, urlParam2, urlParam3, urlParam4, urlParam5, urlParam6, urlParam7) {
    if (qivsUrl && qivsUrl != '') {
        var ua = window.navigator.userAgent;
        var old_ie = ua.indexOf('MSIE ');
        var new_ie = ua.indexOf('Trident/');
        var url = qivsUrl;
        var isIE = ((old_ie > -1) || (new_ie > -1));

        if (code === 'QIVS') {
            url = url + "/default.asp?Property/masterdetails.aspx?Qpid=" + urlParam;
        }
        else if (code === 'dataSales') {
            url = url + "/default.asp?Property/Sales/Sales.asp?" + urlParam;
        }
        else if (code === 'floorPlans') {
            url = url + "/default.asp?Plans/FloorPlans/FloorPlans.aspx?Qpid=" + urlParam;
        }
        else if (code == 'maintainFloorPlans') {
            url = url + "/default.asp?Plans/FloorPlans/MaintainFloorPlans.aspx?qpid=" + urlParam;
        }
        else if (code === 'surveyPlans') {
            url = url + "/default.asp?Plans/SurveyPlans/SurveyPlans.aspx?Qpid=" + urlParam;
        }
        else if (code === 'sitePlans') {
            url = url + "/default.asp?Plans/SitePlans/SitePlans.aspx?Qpid=" + urlParam;
        }
        else if (code ==='valuationData') {
            url = url + "/default.asp?property/ValuationDataWS/ViewWorksheet.aspx?Qpid=" + urlParam;
        }
        else if (code === 'attachments') {
            url = url + "/default.asp?Property/Revisions/Attachments.aspx?Qpid=" + urlParam;
        }
        else if (code === 'subdivisions') {
            url = url + "/default.asp?property/Subdivision/Subdivisionmain.asp?sSelectedQupid=" + urlParam + "&AssessmentNumber=" + urlParam2;
        }
        else if (code === 'objections') {
            url = `${url}/default.asp?Property/Objections.asp?Qpid=${urlParam}&bInactive=False`; //TODO: check if the Inactive field needs to come from somewhrere
        }
        else if (code === 'objectionAttachments') {
            url = `${url}/default.asp?Plans/ObjectionDocuments/ObjectionDocuments.aspx?qpid=${urlParam}&ObjID=${urlParam2}`;
        }
        else if (code === 'sraValues') {
            url = url + "/default.asp?property/UpdateSRAs.asp?Qupid=" + urlParam + "&txtRollNumber=" + urlParam2 + "&txtAssessmentNumber=" + urlParam3 + "&txtIsUpdateable=True&"; //TODO: check last boolean if need to come from somewhere.
        }
        else if (code == 'ruralWs') {
            url = url + "/default.asp?property/Revisions/RuralWorksheet.aspx?qpid=" + urlParam;
        }
        else if (code == 'commercialWs') {
            url = url + "/default.asp?property/CommercialWS/CommercialWorksheet.aspx?qpid="+ urlParam;
        }
        else if (code == 'ruralRevisionWs') {
            url = url + "/default.asp?property/Revisions/RuralWorksheet.aspx?qpid=" + urlParam + "&revision=y";
        }
        else if (code == 'commercialRevisionWs') {
            url = url + "/default.asp?property/CommercialWS/CommercialWorksheet.aspx?qpid=" + urlParam + "&revision=y";
        }
        else if (code == 'updateRevisionValues') {
            const revCapVal = Number(urlParam6.replace(/[^0-9\.]+/g,"")).toFixed(4);
            const revLandVal = Number(urlParam7.replace(/[^0-9\.]+/g,"")).toFixed(4);
            const apportionmentCode = urlParam4.charAt(urlParam4.length-1);

            if (urlParam5){
                let natureOfImprovements = urlParam5.replace(/ /g,",%20");
                url = url + "/default.asp?property/UpdateRevisionValues.asp?QPID=" + urlParam + "&txtAction=Show&txtMainCapVal=" + revCapVal + "&txtMainLandVal=" + revLandVal + "&txtRollNumber=" + urlParam2 +  "&txtAssessmentNumber=" + urlParam3 + "&txtApportionmentCode=" + apportionmentCode + "&txtImprExists=True&txtNatureOfImprovements=" + natureOfImprovements +  "&txtIsUpdateable=True";
            }
            else {
                url = url + "/default.asp?property/UpdateRevisionValues.asp?QPID=" + urlParam + "&txtAction=Show&txtMainCapVal=" + revCapVal + "&txtMainLandVal=" + revLandVal + "&txtRollNumber=" + urlParam2 +  "&txtAssessmentNumber=" + urlParam3 + "&txtApportionmentCode=" + apportionmentCode + "&txtIsUpdateable=True";
            }
        }
        if (isIE) {
            var w = window.open(url, "QIVZ");
            w.close();
        }
        window.open(url, "QIVZ");
    }
}

export function roundToNearest(value, roundingAmount) {
    return Math.round(value / roundingAmount) * roundingAmount;
}

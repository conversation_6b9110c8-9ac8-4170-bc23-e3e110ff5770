import { mapState } from 'vuex';
import numeral from 'numeral';
import { store } from '../DataStore.js';
import formatUtils from './FormatUtils';
import commonUtils from './CommonUtils';
import moment from 'moment';

export default {
    mixins: [formatUtils, commonUtils],
    computed: {
        ...mapState('userData', [
            'isAdminUser',
        ]),
        ...mapState({
            monarchUserId: state => state.application.monarchUser.id
        }),
    },
    methods: {
        generateMasterDetailsData: function (response, qivsURL) {
            var self = this;
            var property = response.property;
            property.valuationReference = self.generateValRef(property);
            property.territorialAuthorityId = property.territorialAuthority ? property.territorialAuthority.code : '';
            property.extensions = property.massAppraisalData ? property.massAppraisalData.extensions : 0;
            property.address1 = self.generateAddress1(property.address);
            property.address2 = self.generateAddress2(property.address, property.territorialAuthority);
            property.TLA = property.massAppraisalData ? (property.massAppraisalData.totalLivingArea ? property.massAppraisalData.totalLivingArea : '0') : '0';
            property.TFA = property.landUseData ? (property.landUseData.totalFloorArea ? property.landUseData.totalFloorArea : '0') : '0';
            property.MLA = property.massAppraisalData ? (property.massAppraisalData.mainLivingArea ? property.massAppraisalData.mainLivingArea : '0') : '0';
            property.landArea = property.landUseData ? (property.landUseData.landArea ? property.landUseData.landArea : 0) : 0;
            property.capitalValue = property.currentValuation ? numeral(property.currentValuation.capitalValue).format('$0,0') : '$0';
            property.landValue = property.currentValuation ? numeral(property.currentValuation.landValue).format('$0,0') : '$0';
            property.rawCapitalValue = property.currentValuation ? property.currentValuation.capitalValue : 0;
            property.rawLandValue = property.currentValuation ? property.currentValuation.landValue : 0;
            property.buildingNetRate = property.buildingNetRate ? numeral(property.buildingNetRate).format('$0,0') : '$0';
            property.revisedBuildingNetRate = property.revisedBuildingNetRate ? numeral(property.revisedBuildingNetRate).format('$0,0') : '$0';

            //Calculated values
            var calcValues = self.generateCalculatedValues(property.currentValuation, property.landUseData);
            property.valueOfImprovements = calcValues.valueOfImprovements;
            property.cvNetRate = calcValues.cvNetRate;
            property.lvNetRate = calcValues.lvNetRate;
            property.viNetRate = calcValues.viNetRate;

            //Media
            var media = self.getPropertyPhotos(response.photos, property.qupid);
            property.propertyPhotos = media.list;
            property.primaryPhoto = media.primary;

            //VIEW SCOPE
            property.viewScope = self.generateViewScope(property.massAppraisalData);

            //TORA
            property.tora = self.generateTORA(property.tenure, property.ownership, property.rateability, property.apportionment);

            //Owner Occupiers
            var ownerOccupier = self.generateOwnerOccupiers(property.owners, property.occupiers);
            property.owners = ownerOccupier.owners;
            property.occupiers = ownerOccupier.occupiers;
            property.ownersAndOccupiers = ownerOccupier.ownersAndOccupiers;

            //MAORILAND
            property.maoriLand = property.landUseData ? (property.landUseData.isMaoriLand ? 'Yes' : 'No') : 'No';
            if(property.landUseData.isMaoriLand && property.maoriLandproperty) {
                property.cml = self.calculateMaorilandValues(property.maoriLandproperty.currentMaoriLandAdjustment, landArea, totalFloorArea);
                property.rml = self.calculateMaorilandValues(property.maoriLandproperty.revisedMaoriLandAdjustment, landArea, totalFloorArea, property.cml);
                property.mlnoOfOwners = property.maoriLandproperty.numberOfOwners;
            } else {
                property.cml = "";
                property.rml = "";
                property.mlnoOfOwners = "";
            }

            //Others
            property.buildingAge = self.getRenderableValue(property.landUseData ? (property.landUseData.buildingAge ? property.landUseData.buildingAge.description : '') : '');
            property.siteCoverage = self.getRenderableValue(property.landUseData ? property.landUseData.buildingSiteCover : '');
            property.carParks = self.getRenderableValue(property.landUseData ? property.landUseData.carparks : '');
            property.csi = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.classifications ? (property.massAppraisalData.classifications.classOfSurroundingImprovements ? property.massAppraisalData.classifications.classOfSurroundingImprovements.description : '') : '') : '');
            property.houseType = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.classifications ? (property.massAppraisalData.classifications.houseType ? property.massAppraisalData.classifications.houseType.description : '') : '') : '');
            property.houseTypeObj = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.classifications ? (property.massAppraisalData.classifications.houseType ? property.massAppraisalData.classifications.houseType : null) : null) : null);
            property.landscaping = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.classifications ? (property.massAppraisalData.classifications.landscapingQuality ? property.massAppraisalData.classifications.landscapingQuality.description : '') : '') : '');
            property.deck = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.hasDeck ? 'Yes' : 'No') : '');
            property.foundation = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.hasPoorFoundations ? 'Yes' : 'No') : '');
            property.laundryWorkshop = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.hasLaundry ? 'Yes' : 'No') : '');
            property.carAccess = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.hasCarAccess ? 'Yes' : 'No') : '');
            property.driveway = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.hasDriveway ? 'Yes' : 'No') : '');
            property.outlier = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.isOutlier ? 'Yes' : 'No') : '');
            property.effectiveYearBuilt = self.getRenderableValue(property.massAppraisalData ? property.massAppraisalData.effectiveYearBuilt : '');
            property.landUse = self.getRenderableValue(property.landUseData ? (property.landUseData.landUse ? property.landUseData.landUse.description : '') : '');
            property.units = self.getRenderableValue(property.landUseData ? property.landUseData.units : '');
            property.bedrooms = self.getRenderableValue(property.massAppraisalData ? property.massAppraisalData.bedrooms : '');
            property.toilets = self.getRenderableValue(property.massAppraisalData ? property.massAppraisalData.toilets : '');
            property.wallConstructionAndCondition = property.landUseData ? (property.landUseData.wallConstruction ? property.landUseData.wallConstruction.description : '') : '';
            property.wallConstruction = property.landUseData ? (property.landUseData.wallConstruction ? property.landUseData.wallConstruction : null) : null;
            property.wallConstructionAndCondition += property.landUseData ? (property.landUseData.wallCondition ? ' ' + property.landUseData.wallCondition.description : '') : '';
            property.wallConstructionAndCondition = self.getRenderableValue(property.wallConstructionAndCondition);
            property.roofConstruction = property.landUseData ? (property.landUseData.roofConstruction ? property.landUseData.roofConstruction : null) : null;
            property.roofConstructionAndCondition = property.landUseData ? (property.landUseData.roofConstruction ? property.landUseData.roofConstruction.description : '') : '';
            property.roofConstructionAndCondition += property.landUseData ? (property.landUseData.roofCondition ? ' ' + property.landUseData.roofCondition.description : '') : '';
            property.roofConstructionAndCondition = self.getRenderableValue(property.roofConstructionAndCondition);
            property.underMainRoofGarages = self.getRenderableValue(property.massAppraisalData ? property.massAppraisalData.underMainRoofGarages : '');
            property.freeStandingGarages = self.getRenderableValue(property.massAppraisalData ? property.massAppraisalData.freestandingGarages : '');
            property.otherLargeImprovements = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.hasLargeOtherImprovements ? 'Yes' : 'No') : 'No');
            property.modernisation = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.isModernised ? 'Yes' : 'No') : 'No');
            property.zone = self.getRenderableValue(property.landUseData ? property.landUseData.landZone : '');
            property.lotPosition = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.classifications ? (property.massAppraisalData.classifications.lotPosition ? property.massAppraisalData.classifications.lotPosition.description : '') : '') : '');
            property.contour = self.getRenderableValue(property.massAppraisalData ? (property.massAppraisalData.classifications ? (property.massAppraisalData.classifications.contour ? property.massAppraisalData.classifications.contour.description : '') : '') : '');
            property.production = self.getRenderableValue(property.landUseData ? property.landUseData.production : '');

            //QIVS URL
            property.mapUrl = 'http://qvartgis01:8080/QVMS/?qpid=';
            if (qivsURL && qivsURL != '') {
                property.qivsURL = qivsURL;
            }
            return property;
        },
        generateValRef: function(property) {
            var valuationReference = (property.rollNumber ? property.rollNumber + '/' : '') + (property.assessmentNumber ? property.assessmentNumber : '') + ' ' + (property.suffix ? property.suffix : '');
            return valuationReference;
        },
        generateAddress1: function(address) {
            var streetNumberSuffix = address ? (address.streetNumberSuffix ? ' ' + address.streetNumberSuffix : '') : '';
            var address1 = address ? ((address.streetNumber ? address.streetNumber : '') + streetNumberSuffix + ' ' + (address.streetName ? address.streetName : '')
            + (address.streetType && address.streetType.description ? ' ' + address.streetType.description + ',' : '')) : '';
            if (address1.indexOf('undefined') !== -1) {
                address1 = '';
            }
            return address1;
        },
        generateAddress2: function(address, territorialAuthority) {
            var address2 = address ? (address.suburb ? (address.suburb + ', ') : '') : '';
            address2 += address ? (address.town ? address.town + ', ' : '') : '';
            address2 += territorialAuthority ? territorialAuthority.name : '';
            if (address2.indexOf('undefined') !== -1) {
                address2 = '';
            }
            return address2;
        },
        generateCalculatedValues: function(currentValuation, landUseData) {
            var landValue = currentValuation ? currentValuation.landValue : 0;
            var capitalValue = currentValuation ? currentValuation.capitalValue : 0;
            var totalFloorArea = landUseData ? landUseData.totalFloorArea : 0;
            var landArea = landUseData ? landUseData.landArea : 0;

            var valueOfImprovements = 0;
            if (capitalValue) {
                valueOfImprovements = Math.round(capitalValue - landValue);
            }
            valueOfImprovements = numeral(valueOfImprovements).format('$0,0');

            var cvNetRate = 0;
            if (totalFloorArea > 0 && capitalValue && capitalValue > 0) {
                cvNetRate = Math.round(capitalValue / totalFloorArea);
            }
            cvNetRate = numeral(cvNetRate).format('$0,0');
            var lvNetRate = 0;
            if (landArea > 0 && landValue && landValue > 0) {
                lvNetRate = Math.round(landValue / (landArea * 10000));
            }
            lvNetRate = numeral(lvNetRate).format('$0,0');

            var viNetRate = 0;
            if (totalFloorArea > 0 && capitalValue && capitalValue > 0 && landValue && landValue > 0) {
                viNetRate = Math.round((capitalValue - landValue) / totalFloorArea);
            }
            viNetRate = numeral(viNetRate).format('$0,0');
            return {valueOfImprovements: valueOfImprovements, cvNetRate: cvNetRate, lvNetRate: lvNetRate, viNetRate: viNetRate};
        },
        generateOwnerOccupiers: function(owners, occupiers) {
            var propertyOwnersAndOccupiers = [];
            var propertyOwners = [];
            var propertyOccupiers = [];
            $.each(owners, function (i, obj) {
                var propertyOwner = {};
                propertyOwner.fullName = (obj.firstName ? obj.firstName + ' ' : '') + (obj.secondName ? obj.secondName + ' ' : '') + (obj.thirdName ? obj.thirdName + ' ' : '' )
                    + (obj.lastName ? obj.lastName + ' ' : '');
                propertyOwner.type = 'Owner';
                propertyOwner.order = obj.order;
                propertyOwner.isNameSecret = obj.isNameSecret;
                if (obj.mailingAddress) {
                    var mailingAddress = obj.mailingAddress;
                    propertyOwner.address = (mailingAddress.careOf ? mailingAddress.careOf + ', ' : '') +
                        (mailingAddress.organisation ? mailingAddress.organisation + ', ' : '') +
                        (mailingAddress.unit ? mailingAddress.unit + ', ' : '') +
                        (mailingAddress.building ? mailingAddress.building + ', ' : '') +
                        (mailingAddress.streetAddress ? mailingAddress.streetAddress + ', ' : '') +
                        (mailingAddress.suburb ? mailingAddress.suburb + ', ' : '') +
                        (mailingAddress.town ? mailingAddress.town : '') +
                        (mailingAddress.postcode ? ' ' + mailingAddress.postcode : '') +
                        (mailingAddress.country ? ', ' + mailingAddress.country : '');
                }
                propertyOwners.push(propertyOwner);
            });

            $.each(occupiers, function (i, obj) {
                var propertyOccupier = {};
                propertyOccupier.fullName = (obj.firstName ? obj.firstName + ' ' : '') + (obj.secondName ? obj.secondName + ' ' : '') + (obj.thirdName ? obj.thirdName + ' ' : '' )
                    + (obj.lastName ? obj.lastName + ' ' : '');
                propertyOccupier.type = 'Occupier';
                propertyOccupier.order = obj.order;
                propertyOccupier.isNameSecret = obj.isNameSecret;
                if (obj.mailingAddress) {
                    var mailingAddress = obj.mailingAddress
                    propertyOccupier.address = (mailingAddress.careOf ? mailingAddress.careOf + ', ' : '') +
                        (mailingAddress.organisation ? mailingAddress.organisation + ', ' : '') +
                        (mailingAddress.unit ? mailingAddress.unit + ', ' : '') +
                        (mailingAddress.building ? mailingAddress.building + ', ' : '') +
                        (mailingAddress.streetAddress ? mailingAddress.streetAddress + ', ' : '') +
                        (mailingAddress.suburb ? mailingAddress.suburb + ', ' : '') +
                        (mailingAddress.town ? mailingAddress.town : '') +
                        (mailingAddress.postcode ? ' ' + mailingAddress.postcode : '') +
                        (mailingAddress.country ? ', ' + mailingAddress.country : '');
                }
                propertyOccupiers.push(propertyOccupier);
            });
            return {owners: propertyOwners, occupiers: propertyOccupiers, ownersAndOccupiers: propertyOwners.concat(propertyOccupiers)};
        },
        generateTORA: function(tenure, ownership, rateability, apportionment) {
            var tora = '';
            if (tenure && tenure.code) {
                tora += tenure.code;
                tenure = tenure.description;
            }
            if (ownership && ownership.code) {
                tora += ownership.code;
                ownership = ownership.description;
            }
            if (rateability && rateability.code) {
                tora += rateability.code;
                rateability = rateability.description;
            }
            if (apportionment && apportionment.code) {
                tora += apportionment.code;
                apportionment = apportionment.description;
            }
            return tora;
        },
        generateViewScope: function(massAppraisalData) {
            var self = this;
            var viewScope = "";
            var viewDescription = self.getRenderableValue(massAppraisalData ? (massAppraisalData.classifications ? (massAppraisalData.classifications.view ? (massAppraisalData.classifications.view.description + ' ') : '') : '') : '');
            var viewCode = self.getRenderableValue(massAppraisalData ? (massAppraisalData.classifications ? (massAppraisalData.classifications.view ? (massAppraisalData.classifications.view.code + ' ') : '') : '') : '');
            if (viewCode.trim() == 'N') {
                viewScope = self.getRenderableValue(massAppraisalData ? (massAppraisalData.classifications ? (massAppraisalData.classifications.viewScope ? massAppraisalData.classifications.viewScope.description : '') : '') : '')
            } else {
                viewScope = ((viewDescription && viewDescription != '-') ? viewDescription.substring(viewDescription.trim().lastIndexOf(" ") + 1) : '')
                + self.getRenderableValue(massAppraisalData ? (massAppraisalData.classifications ? (massAppraisalData.classifications.viewScope ? massAppraisalData.classifications.viewScope.description : '') : '') : '');
            }
            return viewScope;
        },
        calculateMaorilandValues: function (adjustment, landArea, totalFloorArea, currentAdjustment) {
            var self = this;
            var maoriLand = {}
            var mlCvNetRate = 0;
            var mlLvNetRate = 0;
            var mlViNetRate = 0;
            var unadjustedValuation = adjustment.unadjustedValuation;
            var mlCapitalValue = unadjustedValuation.capitalValue ? unadjustedValuation.capitalValue : 0;
            var mlLandValue = unadjustedValuation.landValue ? unadjustedValuation.landValue : 0;
            var mlVi = mlCapitalValue - mlLandValue;

            if (totalFloorArea > 0 && mlCapitalValue > 0) {
                mlCvNetRate = Math.round(mlCapitalValue / totalFloorArea);
            }
            maoriLand.rawCapitalValue = mlCapitalValue;
            maoriLand.capitalValue = numeral(mlCapitalValue).format('$0,0');
            maoriLand.cvNetRate = numeral(mlCvNetRate).format('$0,0');

            if (landArea > 0 && mlLandValue > 0) {
                mlLvNetRate = Math.round(mlLandValue / (landArea * 10000));
            }
            maoriLand.rawLandValue = mlLandValue;
            maoriLand.landValue = numeral(mlLandValue).format('$0,0');
            maoriLand.lvNetRate = numeral(mlLvNetRate).format('$0,0');

            if (totalFloorArea > 0 && mlVi > 0) {
                mlViNetRate = Math.round(mlVi / totalFloorArea);
            }
            maoriLand.rawVi = mlVi;
            maoriLand.vi = numeral(Math.round(mlCapitalValue - mlLandValue)).format('$0,0');
            maoriLand.viNetRate = numeral(mlViNetRate).format('$0,0');

            var mlSignificance = adjustment.siteSignificanceAdjustmentPercentage ? adjustment.siteSignificanceAdjustmentPercentage : 0;
            var mlOwners = adjustment.multipleOwnerAdjustmentPercentage ? adjustment.multipleOwnerAdjustmentPercentage : 0;
            maoriLand.adjustment = numeral(self.round((+mlSignificance + +mlOwners), 1)).format('0,0.0');
            maoriLand.significance = numeral(self.round(mlSignificance, 1)).format('0,0.0');
            maoriLand.owners = numeral(self.round(mlOwners, 1)).format('0,0.0');

            if(currentAdjustment) {
                var capitalValueDiff = 0;
                if (currentAdjustment.rawCapitalValue > 0 && mlCapitalValue > 0) {
                    capitalValueDiff = ((mlCapitalValue * 100) / currentAdjustment.rawCapitalValue) - 100;
                }
                maoriLand.capitalValueDiff = (Math.round(capitalValueDiff * 10)) / 10;
                var landValueDiff = 0
                if (currentAdjustment.rawLandValue > 0 && mlLandValue > 0) {
                    landValueDiff = ((mlLandValue * 100) / currentAdjustment.rawLandValue) - 100;
                }
                maoriLand.landValueDiff = (Math.round(landValueDiff * 10)) / 10;
                var valueOfImprovementsDiff = 0;
                if (currentAdjustment.rawVi > 0 && mlVi > 0) {
                    valueOfImprovementsDiff = ((mlVi * 100) / currentAdjustment.rawVi) - 100;
                }
                maoriLand.valueOfImprovementsDiff = (Math.round(valueOfImprovementsDiff * 10)) / 10;
            }
            return maoriLand;
        },
        getPropertyPhotos: function (photos, qupid) {
            var propertyPhotos = [];
            var primaryPhoto = 'assets/images/property/addPhotos.png';
            $.each(photos[0], function (i, obj) {
                if (obj.isPrimary) {
                    primaryPhoto = obj.mediaItem.smallImageUrl;
                }
                propertyPhotos.push({
                    'id': obj.id,
                    'propertyId': obj.ownerId,
                    'qupid': qupid,
                    'link': obj.mediaItem.mediumImageUrl
                });
            });
            var result = {primary: primaryPhoto, list: propertyPhotos};
            return result;
        },
        populateTASummary: function (propertyId, callback) {
            var self = this;
            var m = jsRoutes.controllers.PropertyMasterData.getTASummary(propertyId);
            $.ajax({
                type: "GET",
                url: m.url,
                cache: false,
                success: function (response) {
                    var nextRatingValuationDate = response.nextRevisionDate ? 'As at ' + self.formatDate(response.nextRevisionDate, 'DD MMMM YYYY') : '';
                    var currentRevisionDate = response.currentRevisionDate ? self.formatDate(response.currentRevisionDate) : '';
                    var rawCurrentRevisionDate = response.currentRevisionDate ? response.currentRevisionDate : '';
                    if(callback) callback();
                    var res = { 'nextRatingValuationDate': nextRatingValuationDate, 'currentRevisionDate': currentRevisionDate, 'rawCurrentRevisionDate': rawCurrentRevisionDate };
                    return res;
                },
                error: function (response) {
                    console.log('error fetch property search results: ' + response);
                    if(callback) callback();
                    return null;
                }
            });
        },
        isHomeValuationReadOnly: function (status, valuer, countersigner) {
            var isReadOnly = true;
            if(status){
                var statusCode = status.code;
                if(statusCode == 'S') {
                    isReadOnly = false;
                } else if($.inArray(statusCode, ['I','R','P','W']) !== -1) {
                    //Only allow Admin, Valuer and Countersigner at this point
                    var isAdmin = this.isAdminUser;
                    var isValuer = this.monarchUserId && valuer.id && valuer.id == this.monarchUserId;
                    var isCountersigner = this.monarchUserId && countersigner.id && countersigner.id == this.monarchUserId;
                    if(isAdmin || isValuer || isCountersigner) {
                        isReadOnly = false;
                        //Only allow Admin and Countersigner if status is Peer Review
                        if(statusCode == 'P' && isValuer && !isAdmin) {
                            isReadOnly = true;
                        }
                    }
                }
            } else {
                isReadOnly = false;
            }
            return isReadOnly;
        }

    }
}

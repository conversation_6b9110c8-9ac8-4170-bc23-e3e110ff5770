import {ref} from 'vue'

const globalSelectedClassifications = ref([]);

const options =  [
    { value: "all", label: "Select All", checked: false, group: false, groupId: -1 },
    { value: "marketSales", label: "Market Sales", checked:  true, group: false, groupId: -1 },
    { value: "salesToCheck", label: "Sales to Check", checked:  false, group: false, groupId: -1 },
    { value: "saleType", label: "Sale Type", checked:  false, group: true, groupId: -1 },
    { value: "0-M", label: "M - Multi", checked: false, group: false, groupId: 0 },
    { value: "0-P", label: "P - Part", checked: false, group: false, groupId: 0  },
    { value: "0-S", label: "S - Whole", checked: true, group: false, groupId: 0 },
    { value: "saleTenure", label: "Sale Tenure", checked:  false, group: true, groupId: -1 },
    { value: "1-1", label: "1 - Freehold", checked: true, group: false, groupId: 1 },
    { value: "1-2", label: "2 - Leasehold", checked: false, group: false, groupId: 1 },
    { value: "1-3", label: "3 - Part Interest", checked: false, group: false, groupId: 1 },
    { value: "1-4", label: "4 - Other", checked: false, group: false, groupId: 1 },
    { value: "priceValueRelationship", label: "Price Value Relationship", checked:  false, group: true,  groupId: -1 },
    { value: "2-1", label: "1 - Arm's-length", checked: true, group: false, groupId: 2 },
    { value: "2-2", label: "2 - Review required", checked: true, group: false, groupId: 2 },
    { value: "2-3", label: "3 - Non arm's-length", checked: false, group: false, groupId: 2  },
  ]

export default function useClassifications() {
    return {
        globalSelectedClassifications,
        classificationOptions: options.map(e => Object.freeze(structuredClone(e)))
    }
}
import numeral from 'numeral';
import moment from 'moment';
import _ from 'lodash';

const defaultDateFormat = 'DD/MM/YYYY';

export default {
    methods: {
        round(value, precision) {
            const multiplier = Math.pow(10, precision || 0);
            return Math.round(value * multiplier) / multiplier;
        },
        formatPrice,
        formatDecimal(number, decimal) {
            var formattedDecimal;
            if (typeof number == 'number') {
                formattedDecimal = parseFloat(number).toFixed(parseInt(decimal));
            }
            return formattedDecimal;
        },
        formatDate,
        getRenderableValue(val) {
            return (val && val != '') ? val : '-';
        },
        toNumber(text) {
            const n = numeral(text).value();
            if (Number.isNaN(n)) {
                return null;
            }
            return n;
        },
        convertToNZDate(inputDate) {
            return moment(inputDate).tz('Pacific/Auckland').format('YYYY-MM-DDTHH:mm:ss+00:00');
        },
    },
};

export function formatPrice(price, format = '$0,0') {
    if (price || price == 0) {
        return numeral(price).format(format);
    }
    return '';
}

export function formatDate(date, format = null, isNZST = false) {
    if (!date || date === '') {
        return '';
    }

    const actualFormat = format || defaultDateFormat;
    if (isNZST) {
        return moment(date).utc(false).format(actualFormat);
    }
    return moment(date).format(actualFormat);
}

export function formatArea(area) {
    const areaAsFloat = parseFloat(area);
    if (!isNaN(areaAsFloat)) {
        return areaAsFloat.toFixed(4);
    }
    return null;
}

export function formatDistance(distance, unit = 'm') {
    const distanceAsFloat = parseFloat(distance);
    if (!isNaN(distanceAsFloat)) {
        return `${Math.round(distanceAsFloat)}${unit}`;
    }
    return null;
}

function capitalise(words) {
    if (words?.trim()) {
        return words.trim().split(' ').filter(word => word.trim()).map(word => word[0].toUpperCase() + word.substring(1).toLowerCase())
            .join(' ');
    }
    return '';
}

export function formatAddressLine1(addressComponents = {}) {
    const { situationNumber, additionalNumber, street } = addressComponents;
    return `${situationNumber && `${situationNumber}`.trim() ? `${situationNumber} ` : ''}${additionalNumber && additionalNumber.trim() ? `${additionalNumber} ` : ''}${capitalise(street)}`;
}

export function formatAddressLine2(addressComponents = {}) {
    const { suburb, town, ratingAuthority } = addressComponents;
    const suburbFormatted = capitalise(suburb);
    const townFormatted = capitalise(town);
    return `${suburbFormatted ? `${suburbFormatted}, ` : ''}${townFormatted ? `${townFormatted}, ` : ''}${capitalise(ratingAuthority)}`;
}

export function formattedAddress(addressComponents = {}) {
    return `${formatAddressLine1(addressComponents)}, ${formatAddressLine2(addressComponents)}`;
}

/**
 * Returns the pluralized noun if the array length is greater than 1.
 * @param {String} noun
 * @param {Array[*]} array
 * @returns {String} Pluralized noun if the array length is greater than 1.
 */
export function pluralizeNoun(noun, array) {
    if (Array.isArray(array) && array.length > 1) {
        // Simple pluralization rule (may not cover all cases)
        if (noun.endsWith('y')) {
            return `${noun.slice(0, -1)}ies`;
        }
        return `${noun}s`;
    }
    return noun;
}

export function filterNonNullValues(obj) {
    return Object.keys(obj).reduce((acc, key) => {
        if (obj[key] || obj[key] === false || obj[key] === 0) {
            acc[key] = obj[key];
        }
        return acc;
    }, {});
}

export function appendPercentage(value) {
    return _.isNil(value) ? '' : `${value}%`;
}

export function formatPercentage(value, format = '0.0%', scaleBy100 = false) {
    numeral.options.scalePercentBy100 = scaleBy100;
    return numeral(Math.abs(value)).format(format);
}

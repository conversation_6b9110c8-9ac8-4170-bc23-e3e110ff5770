import { saveAs } from 'file-saver';

export function getPropertySearchInfo(searchParams, isInternalUser) {
    console.log('getPropertyInfo for' + searchParams);
    try {
        if(!searchParams) {
            return;
        }
       if(!searchParams.propertyCount) {
            searchParams.propertyCount = 0;
       }
        const controllerUrl = jsRoutes.controllers.ExportProperties.exportProperties();
        const ajaxRequest = [];
        let combinedPropertiesInfo = "";
        const batchCounts = chunkNumber(searchParams.propertyCount, searchParams.max);
        for (let i = 0; i < batchCounts.length; i++) {
            const offset = batchCounts[i];
            ajaxRequest.push($.ajaxq('propertyExpoQueue',{
                type: "POST",
                url: controllerUrl.url,
                cache: false,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                data: JSON.stringify({...searchParams, offset}),
                timeout: 120000,
                success: function (response) {
                    if(typeof response !== 'undefined' && response.length > 0){
                        combinedPropertiesInfo = combinedPropertiesInfo.concat(response);
                    }
                },
                error: function (textStatus, errorThrown) {
                    console.error(
                        `${textStatus === 'timeout' ? 'Timeout error' : 'Error' } has occurred while sending multiple ajax request`, errorThrown);
                }
            }));
        }

        $.when.apply($,ajaxRequest).then(function() {
            console.log("ajaxRequest length", ajaxRequest.length);
            console.log("All requests are completed!");

            if(!combinedPropertiesInfo) {
                return;
            }
            downloadCsv(combinedPropertiesInfo, isInternalUser);
        });
    } catch (ex) {
        /* Error so clear out anything we have as it will no longer be correct */
        console.error('[Monarch error]: Exception getting property.');
    }
}

function downloadCsv(propertiesInfo, isInternalUser) {
    if(!propertiesInfo){
        console.log("Something went wrong, and propertiesInfo is undefined!");
        return;
    }

    const fieldNames = ['Region', 'TA', 'SG No.', 'Sales Group', 'Qpid', 'Roll', 'Assmt', 'Suf', 'St No', 'Add No',
                      'Street', 'Suburb ', 'Town', 'CAT', 'Revision Date', 'CV', 'LV', 'VI', 'BNR', 'CV/sqm',
                      'LV/sqm', 'LV/ha', 'VI/sqm', 'RCV', 'RLV', 'RVI', 'RBNR', 'RCV/sqm', 'RLV/sqm', 'RLV/ha', 'RVI/sqm', 'RCV/CV',
                      'RLV/LV', 'RVI/VI', 'RTV', 'Land RTV', 'Est Rent', 'Rent Basis', 'Act Rent', 'Rent Known',
                      'Last Sale Date', 'Net Sale Price', 'Chatts', 'Sale Type', 'Sale Tenure', 'Price Val Rel',
                      'Sale CV', 'Sale LV', 'Sale Status','Nature of Imps', 'Land Area',
                      'Eff Land ', 'Maori Land', 'Production', 'Zone', 'Use', 'Land Use Description', 'Units', 'Beds', 'WCs', 'Car',
                      'Age', 'Wall Const', 'Wall Cond', 'Roof Const', 'Roof Cond', 'Site', 'TFA', 'CSI', 'Lot', 'Cont',
                      'Lndscp', 'View', 'Scp', 'HT', 'Mod', 'EYB', 'Main', 'TLA', 'Fdn', 'Deck',
                      'Ldy', 'Ois', 'Acc', 'Drv', 'UMR', 'FS', 'Out', 'Certificates of Title', 'Legal Description', 'Tenure',
                      'Ownership', 'Rateability', 'Apportionment', 'Extens', 'UCV', 'ULV', 'UVI', 'Significance', 'Site %', 'Own %',
                      '# Own', 'URCV', 'URLV', 'URVI', 'R Site %', 'R Own %', 'Occupier1 Name', 'Occupier2 Name', 'Occupier1 Street', 'Occupier1 Town',
                      'Occupier1 Postcode', 'Owner1 Name', 'Owner1 Street', 'Owner1 Town', 'Owner1 Postcode', 'Plan', 'Loc Att', 'EQ Rate %', 'EQ Range', 'EQ Assessor',
                      'Liq/TC', 'Sun', 'WTI', 'Construct', 'Contam', 'Restrict', 'Flood', 'Tsunami', 'Slip', 'Risks',
                      'Sing Bed', 'Dble Bed', 'Office', 'Bath', 'Bath Age', 'Bath Qual', 'Ens Age', 'Ens Qual', 'Kit Age', 'Kit Qual',
                      'Redec', 'Int Cond', 'Heat', 'Insul', 'Plumb', 'Wiring', 'Dble Glaz', 'Alt Energy', 'Stud', 'Features'];

    if(!isInternalUser) {
        const removeValFromIndex = [[18,1], [22, 16], [49, 1], [100, 5], [114, 35]];
        for (let i = removeValFromIndex.length -1; i >= 0; i--) {
            fieldNames.splice(removeValFromIndex[i][0], removeValFromIndex[i][1]);
        }
    }

    const propertiesData = fieldNames + '\n' + propertiesInfo;

    try{
        const fileName = "Properties"+ getFormattedDateExport() + ".csv";
        const blobData = new Blob([propertiesData], {type: "text/plain;charset=utf-8"});

        if (typeof window.navigator.msSaveBlob !== 'undefined') {
            window.navigator.msSaveBlob(blobData, fileName);
        }

        saveAs(blobData, fileName);

    }
    catch(err){
        console.log("Download failed!");
        console.log("Exception: ", err);
    }
}

function getFormattedDateExport() {
    const today = new Date(Date.now());
    return today.getFullYear()+''+(today.getMonth() + 1)+''+today.getDate();
}

function chunkNumber(number, maxSize) {
    let gOffSetCounter  = 0;
    let offSet;
    const offSetArray = [];
    if(maxSize > number){
        offSetArray.push(0)
        return offSetArray;
    }
    const chunkNos = Math.ceil(number / maxSize) ; //The ceil() method rounds a number UPWARDS
    console.log("chunkNos : ",chunkNos);
    for (let i = 0; i < chunkNos; i++) {
        gOffSetCounter = gOffSetCounter +1;
        offSet = (gOffSetCounter - 1) * maxSize;
        offSetArray.push(offSet)
    }
    return offSetArray;
};

import { DateTime } from 'luxon';
import { pushValidation } from './common.js';
import _ from 'lodash';

const STATUS_SUCCESS = 'SUCCESS';

export function validateWorksheet(payload) {
    if(!payload) {
        throw new Error('Oops, something went wrong!');
    }

    const {
        capRate,
        modal,
        remedyYear,
        landRows,
        improvementRows,
        actualRentalRows,
        adoptedValues,
        isMaoriLand,
        streetLocationOptions,
        cvOverrideTotal,
        lvOverrideTotal,
        adoptedCapitalValue,
        adoptedLandValue,
        hasAdoptedMinVi,
        valuationMethod,
        configs,
        isCurrent,
        categoryCode,
        hasSameOutgoings,
        totalLandArea,
        landArea,
        nla,
        isUpdateAssessment,
        totalCvIncome,
        viSummationCapitalValue,
        hasNegativeCapRate,
        allCvOverridesProvided,
        allLvOverridesProvided,
        totalLandValue,
    } = payload;

    const validations = {};
    validatePropertyDetails(validations, remedyYear);
    validateWorksheetPropertyData(validations, capRate, modal, valuationMethod, improvementRows, hasNegativeCapRate);
    validateMaoriLand(validations, isMaoriLand);
    validateWorksheetTotal(isUpdateAssessment, validations, valuationMethod, totalCvIncome, viSummationCapitalValue);
    const validatedLandRows = validateLands(validations, landRows, streetLocationOptions, totalLandArea, totalLandValue, landArea, nla, isCurrent);
    const validatedImpRows = validateImprovements(validations, improvementRows, valuationMethod, hasSameOutgoings, isCurrent);
    const validatedActualRentals = validateActualRental(validations, actualRentalRows, configs, isCurrent);
    const validatedAdopted = validateWorksheetAdopted(validations, adoptedValues, valuationMethod, cvOverrideTotal, lvOverrideTotal, adoptedCapitalValue, adoptedLandValue, hasAdoptedMinVi, categoryCode, isCurrent, allCvOverridesProvided, allLvOverridesProvided);

    const validWorksheet = isValidWorksheet(validations);
    const result = {
        status: STATUS_SUCCESS,
        errors: validWorksheet.errors,
        hasErrors: validWorksheet.hasErrors,
        warnings: validWorksheet.warnings,
        hasWarnings: validWorksheet.hasWarnings,
        validations,
        worksheet: {...payload, landRows: validatedLandRows, improvementRows: validatedImpRows, actualRentalRows: validatedActualRentals, adoptedValues: validatedAdopted },
    };
    formatValidations(result);
    return result;
}

export function validateReasonForChange(reasonForChange, adoptedLandValue, adoptedCapitalValue, parentLv, parentCv) {
    const validation = {};
    if(!(reasonForChange.output > 0 && reasonForChange.source > 0 && reasonForChange.reason)) {
        pushValidation(validation, 'Reason for change', 'errors', 'Please complete the reason for change');
    }
    if(reasonForChange.output === 7 && (adoptedLandValue != parentLv || adoptedCapitalValue != parentCv)) {
        pushValidation(validation, 'Reason for change', 'warnings', 'CV or LV has changed but no owner notice will be produced');
    }
    if(validation) {
        const validWorksheet = isValidWorksheet(validation);
        const result = {
            status: STATUS_SUCCESS,
            errors: validWorksheet.errors,
            hasErrors: validWorksheet.hasErrors,
            warnings: validWorksheet.warnings,
            hasWarnings: validWorksheet.hasWarnings,
            validation,
        };
        formatValidations(result);
        return result;
    }
    return false;
}

function validatePropertyDetails(validations, remedyYear) {
    if(remedyYear && remedyYear.toString().length !== 4) {
        pushValidation(validations, 'Remedy Deadline', 'errors', 'Please enter a valid year.');
    }
}

function validateWorksheetPropertyData(validations, capRate, modal, valuationMethod, improvementRows, hasNegativeCapRate) {
    const decimalRegex = /^\d+(\.\d{1,2})?$/;
    if (capRate && !decimalRegex.test(capRate)) {
        pushValidation(validations, 'Cap Rate', 'errors', 'Invalid Cap Rate.');
    }
    if(!modal) {
        pushValidation(validations, 'Modal', 'errors', 'A modal rate cannot be retrieved.');
    }

    if((valuationMethod == 'CV Income' && !capRate)) {
        pushValidation(validations, 'Cap Rate', 'errors', 'Cap Rate is missing.');
    }

    if(improvementRows.some(row => !_.isNil(row.rental)) && (valuationMethod == 'VI Summation' && !(capRate || hasNegativeCapRate))) {
        pushValidation(validations, 'Cap Rate', 'errors', 'Cap Rate is missing.');
    }
}

function validateMaoriLand(validations, isMaoriLand) {
    if(isMaoriLand) {
        pushValidation(validations, 'Maori Land', 'errors', 'Maori land assessments cannot have a commercial worksheet. Please delete the worksheet.');
    }
}

function validateLands(validations, landRows, streetLocationOptions, totalLandArea, totalLandValue, assessmentLandArea, nla, isCurrent) {
    const lands = landRows.filter(row => row.description || row.streetLocationTypeId || row.area || row.ratePerMetre);
    const landRowCount = lands.length;

    if (landRowCount === 0) {
        pushValidation(validations, 'LandRow', 'errors', 'At least one land row must have a value.');
    }

    if (isCurrent && assessmentLandArea > 0 && totalLandArea != Math.round(assessmentLandArea * 10000)) {
        pushValidation(validations, 'LandArea', 'warnings', 'Worksheet land area does not match assessment land area.');
    }

    if (isCurrent && (assessmentLandArea === null || assessmentLandArea <= 0) && totalLandArea > nla * 2) {
        pushValidation(validations, 'LandArea', 'warnings', 'Worksheet land area is more than double floor area.');
    }

    const conditionsAndMessages = [
        { condition: row => { const location = streetLocationOptions.find(item => item.id == row.streetLocationTypeId); return isCurrent && location?.description === 'Undefined'; }, message: 'Street Location must not be Undefined.', type: 'errors'},
        { condition: row => totalLandValue === 0 && (!row.streetLocationTypeId || !row.area || !row.ratePerMetre) , message: 'Please enter a [Street Location/Area m²/Rate/m²]', type: 'errors'},
        { condition: row => totalLandValue > 0 && landRowCount >= 1 && (!row.streetLocationTypeId || !row.area) , message: 'Please enter a [Street Location/Area m²]', type: 'errors'},
        { condition: row => totalLandValue > 0 && landRowCount > 1 && !row.ratePerMetre, message: 'Please enter Rate/m²', type: 'warnings'}
    ];

    return validateAndPush(conditionsAndMessages, lands, validations , 'LandRow');
}


function validateImprovements(validations, improvementRows, valuationMethod , hasSameOutgoings, isCurrent) {
    const improvements = improvementRows.filter(row => row.description || row.area || row.rental || row.carparks || row.percentVacant || row.percentExcessLand || row.multiple || row.life || row.yearBuilt || row.percentObsolete || row.lumpSum);

    if (!isCurrent && hasSameOutgoings) {
        pushValidation(validations, 'Outgoings', 'warnings', 'Outgoings $/m² on the revision worksheet is the same as on the current worksheet.');
    }
    const conditionsAndMessages = [
        { condition: row => row.area && row.carparks, message: 'Area and car parks cannot be used in the same calculation.', type: 'errors' },
        { condition: row => (!_.isNil(row.rental) && row.rental >= 0) && !row.area && !row.carparks, message: 'Area is required when the parks field is empty and a rental amount is entered', type: 'errors' },
        { condition: row => row.percentVacant >= 100, message: 'Out of range.', type: 'errors' },
        { condition: row => row.percentExcessLand > 100, message: 'Invalid XSLand.', type: 'errors' },
        { condition: row => row.life && row.life % 1 !== 0, message: 'Invalid Life.', type: 'errors' },
        { condition: row => row.yearBuilt && row.yearBuilt % 1 !== 0, message: 'Invalid Year.', type: 'errors' },
        { condition: row => row.percentVacant && (!row.area && _.isNil(row.rental)) && (!row.carparks && _.isNil(row.rental)), message: 'Rental and Area or Parks must be entered when Vac% is used.', type: 'errors' },
        { condition: row => (row.area && _.isNil(row.rental)) && (valuationMethod == 'CV Income') , message: 'Improvements row has missing rental.', type: 'errors' },
        { condition: row => !row.worksheetImprovementId && (row.description ||row.area || _.isNil(row.rental) || row.carparks || row.percentVacant || row.percentExcessLand || row.multiple || row.life || row.yearBuilt || row.percentObsolete || row.lumpSum),
            message: 'Improvement data has been entered but no improvement type is selected.', type: 'errors' },
        { condition: row => ((row.description || row.worksheetImprovementId)
            && !(row.area || !_.isNil(row.rental) || row.carparks || row.percentVacant || row.percentExcessLand || row.multiple || row.life || row.yearBuilt || row.percentObsolete || row.lumpSum)),
            message: 'Invalid or missing data in the improvements section.', type: 'errors' },
        { condition: row => ((_.isNil(row.rental) && !row.viSummation && (row.area || row.carparks)) ||((row.area && row.worksheetImprovementId )
            && !(row.description || !_.isNil(row.rental) || row.carparks || row.percentVacant || row.percentExcessLand || row.multiple || row.life || row.yearBuilt || row.percentObsolete || row.lumpSum)))
            && isCurrent,
            message: 'CV or VI improvement data has been entered but is incomplete.', type: 'errors' },
        { condition: row => ((row.carparks && row.worksheetImprovementId )
            && !(row.description || !_.isNil(row.rental) || row.area || row.percentVacant || row.percentExcessLand || row.multiple || row.life || row.yearBuilt || row.percentObsolete || row.lumpSum))
            && isCurrent,
            message: 'CV improvement data has been entered but is incomplete.', type: 'errors' },
        { condition: row => ((row.area && row.multiple)
            && !(row.description || row.worksheetImprovementId || row.yearBuilt || row.percentObsolete || row.lumpSum))
            && isCurrent,
            message: 'VI improvement data has been entered but is incomplete.', type: 'errors' },
        { condition: row => (_.isNil(row.rental) && !row.worksheetImprovementId && !(row.carparks || row.area ))
            && (valuationMethod == 'CV Income') , message: 'Improvement type, rental and carparks or area is missing.', type: 'errors' },
        { condition: row => (!( row.area && row.worksheetImprovementId && row.multiple && row.life && row.yearBuilt) && (valuationMethod == 'VI Summation')), message: 'CV Income method will adopt land value only if VI is negative.', type: 'warnings' },
        { condition: row => (!(row.worksheetImprovementId || row.lumpSum ) && (valuationMethod == 'VI Summation')), message: 'Improvement type and lumpsum is missing.', type: 'error' },
        { condition: row => (!row.cvIncome || row.cvIncome <= 0) && (valuationMethod == 'CV Income'), message: 'Some improvement rows have $0 CV Income value.', type: 'warnings' },
        { condition: row => (!row.viSummation || row.viSummation <= 0) && (valuationMethod == 'VI Summation'), message: 'Some improvement rows have $0 VI Summation value.', type: 'warnings' },
        { condition: row => (row.area || row.carparks) && _.isNil(row.rental) && (!row.viSummation || row.viSummation <= 0) && !isCurrent, message: 'RCV or RVI Improvement data has been entered but is incomplete.', type: 'errors' },
        { condition: row => !(row.area || row.carparks) && !(row.worksheetImprovementId || _.isNil(row.rental)) && (valuationMethod == 'CV Income') && !isCurrent, message: 'Improvement row does not have the complete field value', type: 'errors' },
        { condition: row => !(row.worksheetImprovementId || row.area || row.multiple || row.life || row.yearBuilt) && !(row.worksheetImprovementId || row.lumpSum) && (valuationMethod == 'VI Summation') && !isCurrent, message: 'Improvement row does not have the complete field value', type: 'errors' },
    ];

    return validateAndPush(conditionsAndMessages, improvements, validations , 'ImprovementRow');
}

function validateActualRental(validations, rentalRows, configs, isCurrent) {
    const actualRentalRows = rentalRows.filter(row => row.rentalDate|| row.grossRental || row.floorArea || row.rentalPerSqmt );
    const key = isCurrent ? 'annual_value_current_worksheet_actual_rental_years_within' : 'annual_value_revision_worksheet_actual_rental_years_within';
    const rentalYearsWithin = configs.find(item => item.keyName == key) || {};
    const pastYearsWithinDate = DateTime.now().minus({ years: parseInt(rentalYearsWithin.keyValue) });
    const conditionsAndMessages = [
        { condition: row => (row.floorArea || row.tenant || row.rentalPerSqmt) && !row.rentalDate, message: 'Date Rent Set is required.', type: 'errors' },
        { condition: row => (row.floorArea || row.tenant || row.rentalPerSqmt) && !row.grossRental , message: 'Annual Rental is required.', type: 'errors' },
        { condition: row => row?.rentalDate && !(DateTime.fromISO(row.rentalDate, 'yyyy-MM-dd').isValid), message: 'Date must be in the format dd/mm/yyyy.', type: 'errors' },
        { condition: row => row?.rentalDate && DateTime.fromFormat(row.rentalDate, 'yyyy-MM-dd') > DateTime.now(), message: 'Date cannot be in the future.', type: 'errors' },
        { condition: row => row?.rentalDate && DateTime.fromFormat(row.rentalDate, 'yyyy-MM-dd') <= pastYearsWithinDate && rentalYearsWithin , message: `Date must be within ${rentalYearsWithin.keyValue} years.`, type: 'errors' },
    ];

    return validateAndPush(conditionsAndMessages, actualRentalRows, validations , 'ActualRental');
}

function validateWorksheetAdopted(validations, adoptedValues, valuationMethod, cvOverrideTotal, lvOverrideTotal, adoptedCapitalValue, adoptedLandValue, hasAdoptedMinVi, categoryCode, isCurrent, allCvOverridesProvided, allLvOverridesProvided) {

    if (adoptedCapitalValue <= 0) {
        pushValidation(validations, 'AdoptedValue', 'errors', `${isCurrent ? 'CV' : 'RCV'} must be greater than 0.`);
    }

    if (adoptedLandValue <= 0) {
        pushValidation(validations, 'AdoptedValue', 'errors', `${isCurrent ? 'LV' : 'RLV'} must be greater than 0.`);
    }

    if (hasAdoptedMinVi && isCurrent) {
        pushValidation(validations, 'AdoptedValue', 'warnings', 'Minimum VI adopted as CV less than LV. Do you want to proceed.');
    }

    // if the category is an improved category (starts with C, I, O or U but excludes categories starting with CV, IV or OV)
    if (!isCurrent && (/^(C|I|O|U)(?!(V|IV|OV))/).test(categoryCode) && hasAdoptedMinVi) {
        pushValidation(validations, 'AdoptedValue', 'warnings', 'Do you want to proceed? Minimum VI has been adopted.');
    }

    if (allCvOverridesProvided && cvOverrideTotal !== adoptedCapitalValue) {
        pushValidation(validations, 'AdoptedValue', 'errors', `${isCurrent ? 'CV' : 'RCV'} Overrides do not add up to the worksheet total ${isCurrent ? 'CV' : 'RCV'}.`);
    }

    if (allLvOverridesProvided && lvOverrideTotal !== adoptedLandValue) {
        pushValidation(validations, 'AdoptedValue', 'errors', `${isCurrent ? 'LV' : 'RLV'} Overrides do not add up to the worksheet total ${isCurrent ? 'LV' : 'RLV'}.`);
    }

    const conditionsAndMessages = [
        { condition: row => !row.cv || row.cv <= 0, message: `All apportionments must have a ${isCurrent ? 'CV' : 'RCV'} greater than 0.`, type: 'errors' },
        { condition: row => !row.lv || row.lv <= 0, message: `All apportionments must have a ${isCurrent ? 'LV' : 'RLV'} greater than 0.`, type: 'errors' },
        { condition: row => (row.cv - row.lv) < 0, message: 'Value of Improvements cannot be a negative number.', type: 'errors' },
        { condition: row => (!row.lvOverride || !row.cvOverride) && valuationMethod == 'VI Summation', message: `All apportionments must have ${isCurrent ? 'CV and LV' : 'RCV and RLV'} overrides for the VI Summation method.`, type: 'errors' },
    ];

    return validateAndPush(conditionsAndMessages, adoptedValues, validations , 'AdoptedValue');
}

function validateWorksheetTotal(isUpdateAssessment, validations, valuationMethod, totalCvIncome, viSummationCapitalValue) {
    if (isUpdateAssessment && (valuationMethod == 'CV Income' && totalCvIncome === 0) || (valuationMethod == 'VI Summation' && viSummationCapitalValue === 0)) {
        pushValidation(validations, 'WorksheetTotal', 'errors', 'Selected valuation method has zero CV.');
    }
}

function validateAndPush(conditionsAndMessages, rows, validations , errorLabel) {
    rows.forEach(row => row.invalid = false);

    conditionsAndMessages.forEach(({ condition, message, type }) => {
        if (rows.some(condition)) {
            rows.forEach(row => {
                if (condition(row) && type === 'errors') {
                    row.invalid = true;
                }
            });
            pushValidation(validations, errorLabel , type, message);
        }
    });

    return rows;
}

function isValidWorksheet(validations) {
    let errors = [];
    let warnings = [];
    for (const [key, value] of Object.entries(validations)) {
        errors = [...errors, ...value.errors];
        warnings = [...warnings, ...value.warnings];
    }
    return {
        errors,
        hasErrors: errors.length > 0,
        warnings,
        hasWarnings: warnings.length > 0,
    };
}

function formatValidations(validations) {
    validations.formattedErrors = validations?.errors?.filter((e) => e)?.map((error) => ({ message: error })) ?? [];
    validations.formattedWarnings = validations?.warnings?.filter((w) => w)?.map((warning) => ({ message: warning })) ?? [];
}
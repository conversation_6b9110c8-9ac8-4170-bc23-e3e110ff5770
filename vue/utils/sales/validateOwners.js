import { validateEmail, pushValidation } from '../common.js';

export function validateOwnershipSalesProcessing(validations, ownership) {
    const individualEntityTypes = [1, 6];

    const ownerToUse = ownership?.owners?.[0];
    const addressToUse = ownerToUse?.address;
    const email = ownerToUse?.email;
    const phone = ownerToUse?.daytimePhone;
    const phonePrefix = ownerToUse?.daytimePhoneSTD;
    const mobile = ownerToUse?.mobilePhone;
    const mobilePrefix = ownerToUse?.mobilePhoneSTD;
    const co = addressToUse?.CO;
    const street = addressToUse?.streetBox;
    const suburb = addressToUse?.suburb;
    const town = addressToUse?.town;
    const postcode = addressToUse?.postcode;

    for (const [index, owner] of ownership?.owners?.entries() ?? []) {
        const isIndividual = individualEntityTypes.includes(parseInt(owner.entityId));
        if (isIndividual && !owner.surname) {
            pushValidation(validations, `Surname ${index}`, 'errors', 'Surname is required.');
        }
        if (!isIndividual && !owner.surname) {
            pushValidation(validations, `Organisation ${index}`, 'errors', 'Organisation name is required.');
        }
        if (isIndividual && !owner.firstName) {
            pushValidation(validations, `First Name ${index}`, 'errors', 'First name is required for an individual.');
        }
        if (owner.firstName?.length > 50) {
            pushValidation(validations, `First Name ${index}`, 'errors', 'First name must not be more than 50 characters.');
        }
        if (owner.surname?.length > 50) {
            pushValidation(validations, `Surname ${index}`, 'errors', 'Surname must not be more than 50 characters.');
        }
        if (owner.secondName?.length > 50) {
            pushValidation(validations, `Second Name ${index}`, 'errors', 'Second name must not be more than 50 characters.');
        }
        if (owner.thirdName?.length > 50) {
            pushValidation(validations, `Third Name ${index}`, 'errors', 'Third name must not be more than 50 characters.');
        }
    }
    const isValidEmail = validateEmail(email);
    if (email && !isValidEmail) {
        pushValidation(validations, 'Email', 'errors', 'Email is not valid.');
    }
    if (email?.length > 50) {
        pushValidation(validations, 'Email', 'errors', 'Email must not be more than 50 characters.');
    }
    if ((phone && !phonePrefix) || (!phone && phonePrefix)) {
        pushValidation(validations, 'Phone', 'errors', 'Please enter a complete phone number.');
        pushValidation(validations, 'Phone Prefix', 'errors', '');
    }
    if (phone?.length > 10) {
        pushValidation(validations, 'Phone', 'errors', 'Phone must not be more than 10 characters.');
    }
    if ((mobile && !mobilePrefix) || (!mobile && mobilePrefix)) {
        pushValidation(validations, 'Mobile', 'errors', 'Please enter a complete phone number.');
        pushValidation(validations, 'Mobile Prefix', 'errors', '');
    }
    if (mobile?.length > 10) {
        pushValidation(validations, 'Mobile', 'errors', 'Mobile must not be more than 10 characters.');
    }
    if (!co && !street && !suburb && !town) {
        pushValidation(validations, 'Address', 'errors', 'At least one address field is required.');
        pushValidation(validations, 'C/O', 'errors', '');
        pushValidation(validations, 'Street/Box', 'errors', '');
        pushValidation(validations, 'Suburb', 'errors', '');
        pushValidation(validations, 'Town', 'errors', '');
    }
    if (co?.length > 50) {
        pushValidation(validations, 'C/O', 'errors', 'C/O must not be more than 50 characters.');
    }
    if (street?.length > 50) {
        pushValidation(validations, 'Street/Box', 'errors', 'Street/Box must not be more than 50 characters.');
    }
    if (suburb?.length > 50) {
        pushValidation(validations, 'Suburb', 'errors', 'Suburb must not be more than 50 characters.');
    }
    if (town?.length > 50) {
        pushValidation(validations, 'Town', 'errors', 'Town must not be more than 50 characters.');
    }
    if (postcode?.length > 20) {
        pushValidation(validations, 'Postcode', 'errors', 'Postcode must not be more than 20 characters.');
    }
}

export function validateToraAndNewOwners(validations, ownership) {
    const propertyEntityId = parseInt(ownership?.toras?.entityId);
    const propertyTenureId = parseInt(ownership?.toras?.tenureId);
    const propertyApportionmentId = parseInt(ownership?.toras?.apportionmentId);
    const areAllOccupiers = ownership?.owners.every(owner => owner.type === 'Occupier');
    const isApportionmentCode1or5 = [0, 5].includes(propertyApportionmentId); // id and code not the same for all apportionment codes
    const isTenure2or3 = [2, 3].includes(propertyTenureId);
    const highestLevelOwnerEntityId = ownership?.owners[0]?.entityId; // asumming array is sorted by ownership level (owner.assessment.order)
    const maoriOwnership = [6, 7];

    if (isApportionmentCode1or5 && areAllOccupiers && propertyTenureId !== 1) {
        pushValidation(validations, 'Tenure', 'errors', 'Tenure must be 1 when apportionment code is 1 or 5 and all owners are occupiers.');
    }
    if (isApportionmentCode1or5 && !areAllOccupiers && !isTenure2or3) {
        pushValidation(validations, 'Tenure', 'errors', 'Tenure must be 2 or 3 when apportionment code is 1 or 5 and not all owners are occupiers.');
    }
    if (propertyApportionmentId === 3 && propertyTenureId !== 0) {
        pushValidation(validations, 'Tenure', 'errors', 'Tenure must be 0 when apportionment code is 2.');
    }
    // if (highestLevelOwnerEntityId === 1 && propertyEntityId !== 1) {
    //     pushValidation(validations, 'Ownership', 'errors', 'Ownership must be 1 when highest level owner is an individual.');
    // }
    // if (highestLevelOwnerEntityId !== 1 && propertyEntityId === 1) {
    //     pushValidation(validations, 'Ownership', 'errors', 'Ownership must not be 1 when highest level owner is not an individual.');
    // }
    if (highestLevelOwnerEntityId !== propertyEntityId) {
        pushValidation(validations, 'Ownership', 'errors', 'Ownership must match highest level owner entity.');
        pushValidation(validations, 'Owner Entity 0', 'errors', 'Highest level owner entity must match property ownership.');
    }
    if (ownership.isMaoriLand && !maoriOwnership.includes(propertyEntityId)) {
        pushValidation(validations, 'Ownership', 'errors', 'Ownership must be 6 or 7 when the property is Māori land.');
    }
    if (ownership.isMaoriLand && !maoriOwnership.includes(highestLevelOwnerEntityId)) {
        pushValidation(validations, 'Owner Entity 0', 'errors', 'Highest level owner entity must be 6 or 7 when the property is Māori land.');
    }
}

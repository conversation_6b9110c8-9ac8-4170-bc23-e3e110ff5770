import { DateTime } from 'luxon';
import { validateEmail, arrHasDuplicates, pushValidation } from '../common.js';
import { validateOwnershipSalesProcessing, validateToraAndNewOwners } from './validateOwners.js';

export default function validateSale(payload) {
    const { sale, dvr, ownership = null, isExpandedSaleView = false } = payload;

    if (sale?.constructor !== Object || dvr?.constructor !== Object) {
        throw new Error('Oops, something went wrong!');
    }
    const validations = {};
    const crossReferencedPropertiesToDelete = sale.crossReferencedProperties?.filter((property) => property.deleted);
    const hasCrossReferencedProperties = sale.crossReferencedProperties?.length > crossReferencedPropertiesToDelete?.length;
    const isConfirmedSale = sale.saleStatusId == 1;

    validateAgreementDate(validations, sale.agreementDate);
    validateSettlementDate(validations, sale.settlementDate, sale.agreementDate, isConfirmedSale);
    validateSaleClassification(validations, sale, dvr, hasCrossReferencedProperties);
    validateSalePrice(validations, sale.price.gross, sale.price.net);
    if (isConfirmedSale) {
        validateVendorPurchaser(validations, sale.vendorPurchaser);
    }
    validateReasonForChange(validations, sale);
    validateSaleStatus(validations, sale.saleStatusId, sale.assuranceLevelId, sale.pdfNumberSource);
    validateMultiSale(validations, sale, hasCrossReferencedProperties);
    validateProperty(validations, dvr, sale);
    validatePropertyValues(validations, sale, dvr);
    validateSalePdfNumber(validations, sale.salesDirectId, sale.pdfNumberSource, isExpandedSaleView, isConfirmedSale);

    if (ownership) {
        validateToraAndNewOwners(validations, ownership);
        validateOwnershipSalesProcessing(validations, ownership);
    }

    const validSale = isValidSale(validations);
    const result = {
        status: STATUS_SUCCESS,
        errors: validSale.errors,
        hasErrors: validSale.hasErrors,
        warnings: validSale.warnings,
        hasWarnings: validSale.hasWarnings,
        validations,
        hasExtraValidation: Object.values(VALIDATION_KEYS).some((key) => validations.hasOwnProperty(key)),
    };
    formatValidations(result);
    return result;
}

export function validateOwnershipOnFirstLoad(ownership) {
    const validations = {};

    validateToraAndNewOwners(validations, ownership);

    const validSale = isValidSale(validations);
    const result = {
        status: STATUS_SUCCESS,
        errors: validSale.errors,
        hasErrors: validSale.hasErrors,
        warnings: validSale.warnings,
        hasWarnings: validSale.hasWarnings,
        validations,
    };
    formatValidations(result);
    return result;
}

export function validateAgreementDate(validations, agreementDate) {
    if (agreementDate?.endsWith('Z')) {
        agreementDate = agreementDate.split('T')[0];
    }
    const agreementDateTime = DateTime.fromFormat(agreementDate ?? '', 'yyyy-MM-dd');
    if (!agreementDate || !agreementDateTime.isValid) {
        pushValidation(validations, 'Agreement Date', 'errors', 'Agreement Date is required.');
        return;
    }
    if (agreementDateTime > DateTime.now()) {
        pushValidation(validations, 'Agreement Date', 'errors', 'Agreement Date cannot be in the future.');
    }
}

export function validateSettlementDate(validations, settlementDate, agreementDate, isConfirmedSale) {
    if (agreementDate?.endsWith('Z')) {
        agreementDate = agreementDate.split('T')[0];
    }
    if (settlementDate?.endsWith('Z')) {
        settlementDate = settlementDate.split('T')[0];
    }
    const settlementDateTime = DateTime.fromFormat(settlementDate ?? '', 'yyyy-MM-dd');
    const agreementDateTime = DateTime.fromFormat(agreementDate ?? '', 'yyyy-MM-dd');
    if (settlementDate && !settlementDateTime.isValid) {
        pushValidation(validations, 'Settlement Date', 'errors', 'Settlement Date is not valid.');
        return;
    }
    if (settlementDateTime > DateTime.now() && isConfirmedSale) {
        pushValidation(validations, 'Settlement Date', 'errors', 'Settlement Date cannot be in the future.');
    }
    if (settlementDateTime.isValid && agreementDateTime.isValid && settlementDateTime < agreementDateTime) {
        pushValidation(validations, 'Settlement Date', 'errors', 'Settlement Date must be after Agreement Date.');
    }
}

// TODO: clarify if capitalValue is coming from dvr or sale
export function validateSaleClassification(validations, sale, dvr, hasCrossReferencedProperties) {
    const {
        classifications,
        remarks,
        propertyInfo,
        // capitalValue,
        price,
    } = sale;
    const net = price?.net;
    const legalDescription = propertyInfo?.legalDescription;
    const capitalValue = dvr.currentValuation?.capitalValue;
    const { saleTypeId, saleTenureId, priceValueRelationshipId } = classifications ?? {};
    const is11Sale = saleIs11(saleTenureId, priceValueRelationshipId);
    const nspCvRatio = parseFloat(net) / parseFloat(capitalValue);

    if (!saleTypeId) {
        pushValidation(validations, 'Sale Type', 'errors', 'Sale Type is required.');
    }
    if (!saleTenureId) {
        pushValidation(validations, 'Tenure Type', 'errors', 'Tenure Type is required.');
    }
    if (!priceValueRelationshipId) {
        pushValidation(validations, 'Price Value Relationship', 'errors', 'Price Value Relationship is required.');
    }
    if (saleTenureId && priceValueRelationshipId && saleTenureId !== 1 && priceValueRelationshipId !== 3) {
        pushValidation(validations, 'Price Value Relationship', 'errors', 'When Sale Tenure type is not 1, Price Value Relationship must be 3.');
    }
    if (saleTypeId === MULTI && !hasCrossReferencedProperties) {
        pushValidation(validations, 'Sale Type', 'errors', 'Sale Type can only be set to Multi if you have cross referenced properties.');
    }
    if (is11Sale === false && !remarks) {
        pushValidation(validations, 'QV Remarks', 'errors', 'Remarks is required when the sale classification is not open market (11).');
    }
    if (!legalDescription) {
        pushValidation(validations, 'Legal', 'errors', 'Legal Description is required.');
    }
    // if (saleTypeId === PART || saleTypeId === MULTI) {
    //     pushValidation(validations, 'Sale Type', 'warnings', 'Multi or Part sale: Have you updated CV, LV, Areas and Description?');
    // }
    // if (is11Sale === true) {
    //     if (isNaN(nspCvRatio) || nspCvRatio === Infinity) {
    //         pushValidation(validations, 'Capital Value', 'warnings', 'Sale classification is 11, but the NSP/CV ratio is not a valid number.');
    //     } else if (nspCvRatio > nspCvRatioUpperLimit) {
    //         pushValidation(validations, 'Capital Value', 'warnings', `Sale classification is 11, but the NSP/CV ratio ${nspCvRatio?.toFixed(2)} is greater than ${nspCvRatioUpperLimit}.`);
    //     } else if (nspCvRatio < nspCvRatioLowerLimit) {
    //         pushValidation(validations, 'Capital Value', 'warnings', `Sale classification is 11, but the NSP/CV ratio ${nspCvRatio?.toFixed(2)} is less than ${nspCvRatioLowerLimit}.`);
    //     }
    // }
}

export function validateSalePrice(validations, grossSalePrice, netSalePrice) {
    if (!(parseInt(grossSalePrice) > 0)) {
        pushValidation(validations, 'Gross', 'errors', 'Gross Sale Price must be greater than 0.');
    }
    if (!(parseInt(netSalePrice) > 0)) {
        pushValidation(validations, 'Net', 'errors', 'Net Sale Price must be greater than 0.');
    }
}

export function validateVendorPurchaser(validations, vendorPurchaser) {
    if (!vendorPurchaser) {
        pushValidation(validations, 'Vendor/Purchaser', 'errors', 'Vendor/Purchaser is required.');
    }
}

export function validateSaleStatus(validations, saleStatus, assuranceLevel, pdfNumberSource) {
    if (!saleStatus) {
        pushValidation(validations, 'Status', 'errors', 'Sale Status is required.');
    }

    if (saleStatus === UNCONFIRMED && !assuranceLevel) {
        pushValidation(validations, 'Level of Assurance', 'errors', 'Level of Assurance is required when Sale Status is Unconfirmed.');
    }

    if (pdfNumberSource === 3 && saleStatus !== UNCONFIRMED) {
        pushValidation(validations, 'Status', 'errors', 'Sale status must be unconfirmed.');
    }
}

// TODO: need to clarify if we are checking capital value of the dvr or the sale
export function validatePropertyValues(validations, sale, dvr) {
    const {
        classifications: { saleTypeId, saleTenureId, priceValueRelationshipId },
    } = sale;
    const capitalValue = dvr.currentValuation?.capitalValue;
    const landValue = dvr.currentValuation?.landValue;
    const saleIsS11 = saleTypeId === WHOLE && saleTenureId === 1 && priceValueRelationshipId === 1;
    if (saleIsS11 && !(parseInt(capitalValue) > 0)) {
        pushValidation(validations, 'Capital Value', 'errors', 'All "S11" sales must have a CV greater than 0. Please update the property values or sale classification to S12 or S13.');
    }
    const improvementValue = parseInt(capitalValue) - parseInt(landValue);
    if (improvementValue < 0) {
        pushValidation(validations, 'Improvements', 'errors', 'Value of improvements cannot be negative.');
    }
    // else if (improvementValue > 0 && !dvr?.natureOfImprovements?.length) {
    //     pushValidation(validations, 'Nature of Improvements', 'warnings', 'Nature of Improvements not provided but the value of improvements is greater than 0.');
    // }
    // else if (improvementValue === 0 && dvr?.natureOfImprovements?.length) {
    //     pushValidation(validations, 'Improvements', 'warnings', 'Value of improvements is 0 but nature of improvements is provided.');
    // }
}

export function validateSalePdfNumber(validations, pdfNumber, pdfNumberSource, isExpandedSaleView, isConfirmedSale = true) {
    if ((!pdfNumberSource && !(pdfNumber ?? false)) || isExpandedSaleView) {
        return;
    }
    const onlyNumbers = /^[0-9]*$/;
    if (pdfNumber && !onlyNumbers.test(pdfNumber)) {
        pushValidation(validations, 'Sale PDF#', 'errors', 'PDF Number must be a whole number.');
    }
    if (!isConfirmedSale) {
        return;
    }
    if (!pdfNumberSource) {
        pushValidation(validations, 'Sale PDF# Source', 'errors', 'Sale PDF# Source is required when Sale PDF# is provided.');
    }
    if (!pdfNumber) {
        pushValidation(validations, 'Sale PDF#', 'errors', 'Sale PDF# is required when Sale PDF# Source is provided.');
    }
}

export function validateMultiSale(validations, sale, hasCrossReferencedProperties) {
    const { qpid, classifications, crossReferencedProperties = [] } = sale;
    const { saleTypeId } = classifications ?? {};
    if (saleTypeId !== MULTI && hasCrossReferencedProperties) {
        pushValidation(validations, 'Sale Type', 'errors', 'Sale Type must be Multi if there are cross referenced properties.');
    }
    if (saleTypeId === MULTI && !hasCrossReferencedProperties) {
        pushValidation(validations, 'Property Search', 'errors', 'Cross referenced properties required for Sale Type Multi.');
    }
    const qpidArr = [...crossReferencedProperties.filter((p) => !p.deleted).map((p) => parseInt(p.qpid)), qpid];
    if (arrHasDuplicates(qpidArr)) {
        pushValidation(validations, 'Property Search', 'errors', 'Sale property and cross referenced properties must all be different.');
    }
}

export function validateProperty(validations, dvr, sale) {
    const { category, landUseData, summary, site } = dvr;
    const { buildingSiteCover, totalFloorArea, mainLivingArea, age, wallCondition, wallConstruction, roofCondition, roofConstruction, effectiveYearBuilt, houseType, units } = summary ?? {};
    const { production } = landUseData ?? {};
    const { view, viewScope, hasDriveway, hasCarAccess } = site ?? {};
    const { classifications } = sale;
    const { saleTenureId, priceValueRelationshipId } = classifications ?? {};
    const productionValue = parseFloat(production);
    const isCategoryProvided = category?.code;
    const categoryCodeTrimmed = category?.code?.trim();
    const isDairyOrPastoral = ['D', 'P'].includes(categoryCodeTrimmed?.[0]);
    const isChar3ABC = ['A', 'B', 'C'].includes(categoryCodeTrimmed?.[2]);
    const isCategoryLV = categoryCodeTrimmed?.startsWith('LV');
    const isCategoryLI = categoryCodeTrimmed?.startsWith('LI');
    const isSale11 = saleIs11(saleTenureId, priceValueRelationshipId);
    const buildingConditions = [
        { label: 'Age', value: age },
        { label: 'Wall Condition', value: wallCondition },
        { label: 'Wall Construction', value: wallConstruction },
        { label: 'Roof Condition', value: roofCondition },
        { label: 'Roof Construction', value: roofConstruction },
    ];
    const buildingConditionsNotProvided = buildingConditions.filter((x) => (x.value ?? null) === null);
    const isHouseTypeQbQn = [QUALITY_BUNGALOW, BUNGALOW_POST_WAR].includes(houseType);

    if (!isCategoryProvided) {
        pushValidation(validations, 'Category', 'errors', 'Category is required.');
    }

    if (isCategoryProvided && !isDairyOrPastoral && productionValue) {
        pushValidation(validations, 'Production', 'errors', 'Production must be empty when the category is not Dairy or Pastoral.');
    }
    if (isCategoryProvided && isDairyOrPastoral && isChar3ABC && (isNaN(productionValue) || productionValue <= 0)) {
        pushValidation(validations, 'Production', 'errors', 'Production is required when the category is Dairy or Pastoral.');
    }
    if (isCategoryLV && (buildingSiteCover ?? 0) !== 0) {
        pushValidation(validations, 'Building Site Cover', 'errors', 'Building Site Cover must be 0 when the category is LV.');
    }
    if (isCategoryLV && (totalFloorArea ?? 0) !== 0) {
        pushValidation(validations, 'Total Floor Area', 'errors', 'Total Floor Area must be 0 when the category is LV.');
    }
    if (isCategoryLI && isSale11 && (buildingSiteCover ?? 0) === 0) {
        pushValidation(validations, 'Building Site Cover', 'errors', 'Building Site Cover must not be 0 when the category is LI and the sale is 11.');
    }
    if (isCategoryLI && isSale11 && (totalFloorArea ?? 0) === 0) {
        pushValidation(validations, 'Total Floor Area', 'errors', 'Total Floor Area must not be 0 when the category is LI and the sale is 11.');
    }
    if (totalFloorArea < buildingSiteCover) {
        pushValidation(validations, 'Total Floor Area', 'errors', 'Total Floor Area must not be less than Building Site Cover.');
    }
    if (totalFloorArea !== 0 && buildingSiteCover === 0) {
        pushValidation(validations, 'Total Floor Area', 'errors', 'Total Floor Area is not 0 but Building Site Cover is 0.');
    }
    if (totalFloorArea === 0 && buildingSiteCover !== 0) {
        pushValidation(validations, 'Total Floor Area', 'errors', 'Total Floor Area is 0 but Building Site Cover is not 0.');
    }
    if (buildingConditionsNotProvided?.length > 0 && buildingConditionsNotProvided?.length !== buildingConditions?.length) {
        for (const buildingCondition of buildingConditionsNotProvided) {
            // if (['Wall Condition', 'Roof Condition'].includes(buildingCondition.label)) {
            //     continue;
            // }
            pushValidation(validations, buildingCondition.label, 'errors', `${buildingCondition.label} is required.`);
        }
    }
    if (view === NO_APPRECIABLE_VIEW && viewScope !== NONE) {
        pushValidation(validations, 'View Scope', 'errors', 'View Scope must be N when View is N.');
    }
    if (viewScope === NONE && view !== NO_APPRECIABLE_VIEW) {
        pushValidation(validations, 'View', 'errors', 'View must be N when View Scope is N.');
    }
    if (Number.isInteger(parseInt(effectiveYearBuilt)) && (effectiveYearBuilt < effectiveYearBuiltLowerLimit || effectiveYearBuilt > effectiveYearBuiltUpperLimit)) {
        pushValidation(validations, 'Effective Year Built', 'errors', `Effective Year Built must be in the range ${effectiveYearBuiltLowerLimit} to ${effectiveYearBuiltUpperLimit}.`);
    }
    if (isHouseTypeQbQn && (effectiveYearBuilt < 1940)) {
        pushValidation(validations, 'Effective Year Built', 'errors', 'Effective Year Built must be greater than or equal to 1940 when house type code is QB or BN.');
    }
    // We commented this check out for the ticket number DEV-4220
    // if (!isNaN(parseFloat(mainLivingArea)) && mainLivingArea > totalFloorArea) {
    //     pushValidation(validations, 'Main Living Area', 'errors', 'Main Living Area must not be greater than Total Floor Area.');
    // }
    if (hasDriveway && !hasCarAccess) {
        pushValidation(validations, 'Car Access', 'errors', 'Car Access must be Yes when Driveway is Yes.');
    }
}

export function validateReasonForChange(validations, sale) {
    const { rfc } = sale;
    const { reason, output, source } = rfc ?? {};
    if (!reason) {
        pushValidation(validations, 'Reason for Change', 'errors', 'Reason for Change is required.');
    }
    if (isNaN(parseInt(output))) {
        pushValidation(validations, 'Output', 'errors', 'Reason for Change Output is required.');
    }
    if (isNaN(parseInt(source))) {
        pushValidation(validations, 'Source', 'errors', 'Reason for Change Source is required.');
    }
}

export function validateOwnership(validations, ownership, propertyData) {
    const entityId = parseInt(propertyData?.entityId);
    const anyOwnerWithFirstAndLastName = ownership?.owners?.find((owner) => owner.firstName && owner.lastName);
    if (entityId === 1 && anyOwnerWithFirstAndLastName === undefined) {
        // pushValidation(validations, 'Surname', 'errors', 'Surname is required for an individual.');
        pushValidation(validations, 'First Name', 'errors', 'First name is required for an individual.');
    }
    for (const [index, owner] of ownership?.owners?.entries() ?? []) {
        if (!owner.lastName) {
            pushValidation(validations, `Surname ${index + 1}`, 'errors', 'An entity name is required.');
        }
    }
    const isValidEmail = validateEmail(ownership?.email);
    if (ownership?.email && !isValidEmail) {
        pushValidation(validations, 'Email', 'errors', 'Email is not valid.');
    }
    if (ownership?.phone && !ownership?.phonePrefix) {
        pushValidation(validations, 'Phone', 'errors', 'Phone prefix is required when phone is provided.');
        pushValidation(validations, 'Mobile', 'errors', 'Mobile prefix is required when mobile is provided.');
    }
    if (!ownership.co && !ownership.street && !ownership.suburb && !ownership.town) {
        pushValidation(validations, 'Address', 'errors', 'At least one address field is required.');
    }
}

function saleIs11(saleTenureId, priceValueRelationshipId) {
    if (!saleTenureId || !priceValueRelationshipId) {
        return null;
    }
    return saleTenureId === FREEHOLD && priceValueRelationshipId === ARMS_LENGTH;
}

function isValidSale(validations) {
    let errors = [];
    let warnings = [];
    for (const [key, value] of Object.entries(validations)) {
        errors = [...errors, ...value.errors];
        warnings = [...warnings, ...value.warnings];
    }
    return {
        errors,
        hasErrors: errors.length > 0,
        warnings,
        hasWarnings: warnings.length > 0,
    };
}

function formatValidations(validations) {
    validations.formattedErrors = validations?.errors?.filter((e) => e)?.map((error) => ({ message: error })) ?? [];
    validations.formattedWarnings = validations?.warnings?.filter((w) => w)?.map((warning) => ({ message: warning })) ?? [];
}

const nspCvRatioUpperLimit = 3;
const nspCvRatioLowerLimit = 0.5;
const effectiveYearBuiltUpperLimit = 2999;
const effectiveYearBuiltLowerLimit = 1800;
const mainLivingAreaUpperLimit = 999;
const mainLivingAreaLowerLimit = 10;
const STATUS_SUCCESS = 'SUCCESS';
// sale_status
const UNCONFIRMED = 2;
// sale_type
const MULTI = 1;
const PART = 2;
const WHOLE = 3;
// sale_tenure_type
const FREEHOLD = 1;
// price_value_relationship_type
const ARMS_LENGTH = 1;
// house_type
const BUNGALOW_POST_WAR = 2;
const QUALITY_BUNGALOW = 6;
// view_scope
const NONE = 2;
// view
const NO_APPRECIABLE_VIEW = 1;

//Extra Validation Keys
const VALIDATION_KEYS = {
    MAIN_PROPERTY: 'Main Property',
    IMPROVEMENTS: 'Improvements',
    NATURE_OF_IMPROVEMENTS: 'Nature of Improvements',
    LEGAL: 'Legal',
    CAPITAL_VALUE: 'Capital Value',
    STATUS: 'Status',
    DATA_QUALITY: 'dataQuality',
    LEVEL_OF_ASSURANCE: 'Level of Assurance',
    SALE_PDF: 'Sale PDF#',
    SALE_TYPE: 'Sale Type',
    PROPERTY_SEARCH: 'Property Search',
    PRODUCTION: 'Production',
    BUILDING_SITE_COVER: 'Building Site Cover',
    TOTAL_FLOOR_AREA: 'Total Floor Area',
    UNITS_OF_USE: 'Units of Use',
    VIEW_SCOPE: 'View Scope',
    VIEW: 'View',
    EFFECTIVE_YEAR_BUILT: 'Effective Year Built',
    MAIN_LIVING_AREA: 'Main Living Area',
    CAR_ACCESS: 'Car Access',
};

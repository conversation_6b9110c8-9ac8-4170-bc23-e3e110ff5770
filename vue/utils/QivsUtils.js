// Global utility functions for opening up QIVS/QVMS URLs
import router from 'vue-router';

export function openQivsInNewTab(url) {
    var ua = window.navigator.userAgent;
    var old_ie = ua.indexOf('MSIE ');
    var new_ie = ua.indexOf('Trident/');
    var isIE = old_ie > -1 || new_ie > -1;
    if (isIE) {
        var w = window.open(url, 'QIVZ');
        w.close();
    }
    window.open(url, 'QIVZ');
}

export function openMap(qpid, latitude = 0, longitude = 0) {
    if (!qpid) {
        return;
    }

    const path = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/property/qv-map/${latitude}/${longitude}/${qpid}`;
    const target = 'QVMap';
    const windowFeatures = 'fullscreen=yes,scrollbars=yes,resizable=yes';
    const qvMapWindow = window.open(path, target, windowFeatures);

    if (!qvMapWindow) {
        return;
    }

    // Check if qvMapWindow is smaller than the main window, make fullscreen
    const mainWidth = window.screen.availWidth || window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    const mainHeight = window.screen.availHeight || window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
    const qvMapWidth = qvMapWindow.innerWidth || qvMapWindow.document.documentElement.clientWidth || qvMapWindow.document.body.clientWidth;
    const qvMapHeight = qvMapWindow.innerHeight || qvMapWindow.document.documentElement.clientHeight || qvMapWindow.document.body.clientHeight;

    if (qvMapWidth < mainWidth || qvMapHeight < mainHeight) {
        qvMapWindow.moveTo(0, 0);
        qvMapWindow.resizeTo(mainWidth, mainHeight);
    }

    qvMapWindow.focus();
}

export function openMapInNewTab(url) {
    window.open(url, 'Map');
}

export function openUrlInNewTab(url) {
    window.open(url, '_blank');
}

export function b64ToBlob(base64Data, contentType) {
    const sliceSize = 512;
    const theContentType = contentType || '';

    // convert base64 to binary
    const byteCharacters = window.atob(base64Data);

    const byteArrays = [];
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
        const slice = byteCharacters.slice(offset, offset + sliceSize);
        const byteNumbers = new Array(slice.length);
        // eslint-disable-next-line no-plusplus
        for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
    }
    const blob = new Blob(byteArrays, { type: theContentType });
    return blob;
}

export function openBlobDataInNewTab(data) {
    const blobUrl = URL.createObjectURL(data);
    if (blobUrl) {
        window.open(blobUrl);
    }
}

export function openBase64DataInNewTab(data, contentType) {
    const blob = b64ToBlob(data, contentType);
    openBlobDataInNewTab(blob);
}

export function objectToSortedArray(object, sortKey) {
    const array = [];
    for (const key in object) {
        array.push({
            id: key,
            ...object[key]
        });
    }
    return array.sort((a, b) => a[sortKey].localeCompare(b[sortKey]));
}

export function formatFileSize(filesize) {
    const sizeUnits = [
        'bytes',
        'KB',
        'MB',
        'GB',
    ]
    if (!filesize || filesize.constructor !== Number) {
        return '-';
    }
    for (let i = 0; i < sizeUnits.length; i++) {
        if (filesize < 1024) {
            return `${filesize.toFixed(2)} ${sizeUnits[i]}`;
        }
        filesize /= 1024;
    }
    return filesize.toFixed(2) + ' TB';
}

export function validateEmail(email) {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
}

export function pushValidation(validations, key, validationType, message) {
    if (!validations[key]) {
        validations[key] = { errors: [], warnings: [] };
    }
    validations[key][validationType].push(message);
}

export function arrHasDuplicates(arr) {
    const items = new Map();
    for (const item of arr) {
        if (items.has(item)) {
            return true;
        }
        items.set(item);
    }
    return false;
}

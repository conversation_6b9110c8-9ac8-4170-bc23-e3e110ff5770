import {ref} from 'vue'

const globalSelectedSaleStatus = ref([]);

const options =  [
    { value: 1, label: "Confirmed", checked: true },
    { value: 3, label: "Pending", checked:  true },
    { value: 2, label: "Unconfirmed", checked:  false },
  ]

export default function useSaleStatus() {
    return {
        globalSelectedSaleStatus,
        statusOptions: options.map(e => Object.freeze(structuredClone(e)))
    }
}
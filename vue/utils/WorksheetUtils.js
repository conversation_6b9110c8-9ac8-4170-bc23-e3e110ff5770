export default {
    methods: {
        landUseMatchesLandMatrix(landUse, landMatrix) {
            landUse = landUse.filter(item =>
                    !item.delete
                    && (
                        item.size > 0
                        || item.contourId !== ''
                        || item.ruralUseId !== ''
                    )
            );

            if (!landMatrix || landMatrix.length === 0) {
                return false;
            }
            if (landUse.length !== landMatrix.length) {
                return false;
            }

            let allMatch = true;
            landMatrix.map((landMatrixItem) => {
                const landUseMatch = landUse.find(item =>
                    item.ruralUseId === landMatrixItem.qvCoverId
                    && item.contourId === landMatrixItem.qvContourId
                    && item.size === landMatrixItem.itemSize
                );

                if (landUseMatch) {
                    landUse.splice(landUse.indexOf(landUseMatch), 1);
                }
                else {
                    allMatch = false;
                }
            });

            return allMatch;
        },
    }
}

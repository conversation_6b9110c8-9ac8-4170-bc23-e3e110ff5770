<template>
    <div>
        <div class="browserAlertMessage" v-if="displayBrowserAlert">
            <label>
                <span>You are using a browser that is not supported by Monarch. Please change to Chrome.</span>
                <span class="material-icons"  v-on:click="hideBrowserAlert">clear</span>
            </label>
        </div>
        <div v-if="userLoaded">
            <div class="pageWrapper" v-if="showPageWrapper">
                <div class="headerWrapper" v-if="(showHome || showRouter) && showHeader">
                    <h2>Monarch</h2>
                    <p class="buildNumber">Version: {{ version }}</p>
                    <ul
                        data-cy="mainNavigation"
                        class="toolbar bannerControls righty"
                    >
                        <home-link data-cy="mainNavigationHomeLink"></home-link>
                        <li
                            v-if="isInternalUser && !isMobileView"
                            data-cy="mainNavigationRollMaintenanceLink"
                            class="roll-maintenance"
                        >
                            <router-link :to="{name: 'consents-search'}"><label>Consents</label></router-link>
                        </li>
                        <li
                            v-if="isTAUser || externalObjectionAccess && !isMobileView"
                            data-cy="mainNavigationRollMaintenanceLink"
                            class="roll-maintenance"
                        >
                            <router-link :to="{name: 'objections-search' }"><label>Roll Maintenance</label></router-link>
                        </li>
                        <li
                            v-if="isInternalUser && !isMobileView"
                            data-cy="mainNavigationObjectionsLink"
                            class="roll-maintenance"
                        >
                            <router-link :to="{ name: 'objections-search' }"><label>Objections</label></router-link>
                        </li>
                        <li
                            v-if="isInternalUser && !isMobileView"
                            data-cy="mainNavigationSalesProcessingLink"
                            class="roll-maintenance"
                        >
                            <router-link :to="{ name: 'sales-dashboard' }"><label>Sales Processing</label></router-link>
                        </li>
                        <li
                            v-if="!isExternalUser && isAdminUser && !isMobileView"
                            data-cy="mainNavigationCloudUploaderLink"
                            class="admin"
                        >
                            <router-link :to="{name: 'qv-cloud-uploader'}"><label>Cloud Uploader</label></router-link>
                        </li>
                        <li
                            v-if="isExternalUser"
                            data-cy="mainNavigationQivsAppLink"
                            class="qivs-app-link"
                        >
                            <a href="#"><label>QIVS Application</label></a>
                        </li>
                        <li
                            v-if="isExternalUser"
                            data-cy="mainNavigationReportsLink"
                        >
                            <router-link :to="{ name: 'report-dashboard-my-reports'}"><label>Reports</label></router-link>
                        </li>
                        <li
                            v-if="!isExternalUser && isAdminUser && !isMobileView"
                            data-cy="mainNavigationPicklistsLink"
                            class="admin"
                        >
                            <a href="#"><label v-on:click="openAdminPage">Picklists</label></a>
                        </li>
                        <li
                            v-if="!isExternalUser && isAdminUser && !isMobileView"
                            data-cy="mainNavigationUserMaintenanceLink"
                            class="admin"
                        >
                            <a href="#"><label v-on:click="openUserMaintenance">User Maintenance</label></a>
                        </li>
                        <li
                            v-if="!isExternalUser && isRtvUser && !isMobileView"
                            data-cy="mainNavigationRtvLink"
                            class="admin"
                        >
                            <router-link :to="{name: 'rtv-dashboard'}"><label>RTV</label></router-link>
                        </li>
                        <li
                            data-cy="mainNavigationLogoutLink"
                            class="closer"
                            title="Logout"
                        >
                            <a href="/logOut">Logout</a>
                        </li>
                    </ul>
                </div>
                <!-- <search-bar v-if="!isMobileView && !showRouter" /> -->
                <home v-if="showHome" :isMobileView="isMobileView" :displayContentEvent="displayContentEvent"></home>
                <photo-upload v-if="showPhoto" class="photoUpload"></photo-upload>
                <router-view v-if="showRouter" :class="{'router': useRouterClass}"></router-view>
            </div>
            <mobile-valuation v-if="!isExternalUser && isMobileView && showMobileComps" :propertyId="propertyId" :jobId="jobId"></mobile-valuation>
        </div>
      <GlobalModal></GlobalModal>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import { computed } from 'vue';
    import Home from './components/Home.vue';
    import HomeLink from './components/dashboard/HomeLink.vue';
    import GlobalModal from '@/components/common/modal/dialog/GlobalModal.vue';
    import TerritorialAuthoritySingleSelect from './components/filters/TerritorialAuthoritySingleSelect.vue';
    import { EventBus } from './EventBus.js';
    const PhotoUpload = () => import(/* webpackChunkName: "PhotoUpload" */ './components/media/PhotoUpload.vue');
    const MobileComps = () => import(/* webpackChunkName: "MobileComps" */ './components/mobile/MobileComps.vue');
    const MobileValuation = () => import(/* webpackChunkName: "MobileFramework" */ './components/mobile/Framework.vue');
    const SearchBar = () => import(/* webpackChunkName: "SearchBar" */ './components/search/SearchBar.vue');

    export default {
        components: {
            Home,
            HomeLink,
            TerritorialAuthoritySingleSelect,
            PhotoUpload,
            MobileComps,
            MobileValuation,
            SearchBar,
            GlobalModal,
        },
        provide() {
            return {
                userData: computed(() => ({...this.userData, isRegisteredValuer: this.isRegisteredValuer, isValuer: this.isValuer, isCustomerCare: this.isCustomerCare}))
            }
        },
        data() {
            return {
                showHome: false,
                showPhoto: false,
                showMobileComps: false,
                showPageWrapper: true,
                showHeader: true,
                useRouterClass: true,
                windowWidth: 993,
                propertyId: null,
                jobId: null,
                showTemplate: true,
                refreshTemplate : false,
                displayContentEvent: null,
                displayBrowserAlert: false
            }
        },
        computed: {
            currentRouteName: function () {
                return this.$router.currentRoute.name;
            },
            showRouter: function () {
                return (!this.showHome && !this.showPhoto && !this.showMobileComps && this.showPageWrapper) ||
                    (this.showPageWrapper && !this.showHeader);
            },
            isMobileView: function () {
                var isMobileView = false;
                if (this.windowWidth <= 768) {
                    isMobileView = true;
                }
                return isMobileView;
            },
            ...mapState('appVersion', [
                'version',
            ]),
            ...mapState('userData', {
                userLoaded: 'loaded',
            }),
            ...mapState('userData', [
                'isExternalUser',
                'isInternalUser',
                'isRtvUser',
                'isReadOnlyUser',
                'isRegisteredValuer',
                'isValuer',
                'isCustomerCare',
                'userData',
                'userFullName',
                'isAdminUser',
                'isTAUser',
                'externalObjectionAccess',
            ]),
        },
        mounted: function () {
            this.displayBrowserAlert = localStorage.getItem('displayBrowserAlert') !== null ? localStorage.getItem('displayBrowserAlert') : !(/chrome|crios|crmo/i.test(navigator.userAgent) && !(/edg/i.test(navigator.userAgent)));

            if (/OPR/i.test(navigator.userAgent)) {
                this.displayBrowserAlert = true;
            }

            this.loadPage(this.$router.currentRoute);

            EventBus.$on('display-content', (event) => {
                console.log("eventbus display-content", event);
                if (this.currentRouteName !== 'home') {
                    this.$router.push({name: 'home'}, () => {
                        console.log('re-emitting', event);
                        EventBus.$emit('display-content', event);
                    });
                }
            });
        },
        watch: {
            '$route' (to, from) {
                this.loadPage(to);
            }
        },
        methods: {
            loadPage(route) {
                console.log('loading route ' + route.name);
                // Allow legacy routes to be defined in the router and then map them to an event here

                // Legacy Master Details
                if(route.name == 'property') {
                    var event={}
                    event.searchType = 'master-details';
                    event.propertyId = route.params.qpid;
                    this.displayContentEvent = event;
                    this.emitDisplayEvent(event);
                    return;
                }

                // Legacy Extra Details
                if(route.name == 'property-detail') {
                    var event={}
                    event.searchType = 'master-details';
                    event.propertyId = route.params.qpid;
                    event.showExtraDetails = true;
                    this.displayContentEvent = event;
                    this.emitDisplayEvent(event);
                    return;
                }

                // Legacy Home Valuation
                if(route.name == 'property-home-valuation') {
                    var event={}
                    event.searchType = 'master-details';
                    event.propertyId = route.params.qpid;
                    event.valuationJobId = route.params.jobId;
                    this.displayContentEvent = event;
                    this.emitDisplayEvent(event);
                    return;
                }

                // Legacy Map
                if(route.name == 'property-map') {
                    var event={}
                    event.searchType = 'master-details';
                    event.propertyId = route.params.qpid;
                    event.showMap = true;
                    this.displayContentEvent = event;
                    this.emitDisplayEvent(event);
                    return;
                }

                /* Parse the URL to determine the "root page". */
                /* TODO This should all be replaced by vue router. */
                const routeName = route.name;

                const photo = this.getURLParam('propertyId');
                const job = this.getURLParam('jobId');
                const valJob = this.getURLParam('valJobId');
                const home = this.getURLParam('home');

                /* HACK if from QIVS then show old "home" */
                const fromQivs = (window.location.pathname.toUpperCase() == '/property/property'.toUpperCase())
                    || (window.location.pathname.toUpperCase() == '/property/property/'.toUpperCase())
                    || (window.location.pathname.toUpperCase() == '/property/property/Relink'.toUpperCase())
                    || (window.location.pathname.toUpperCase() == '/property/property/Relink/'.toUpperCase()) ;

                const hasQupid = this.getURLParam('qupid');

                const photoFromRef = this.getURLParam('propertyId', document.referrer.substring(document.referrer.indexOf("?") + 1));
                const photoIdFromRef = this.getURLParam('photoId', document.referrer.substring(document.referrer.indexOf("?") + 1));
                const joboIdFromRef = this.getURLParam('jobId', document.referrer.substring(document.referrer.indexOf("?") + 1));
                const valJobFromRef = this.getURLParam('valJobId', document.referrer.substring(document.referrer.indexOf("?") + 1));
                this.windowWidth = window.innerWidth;

                /* If going to "OLD HOME" then force that first ... */
                if (home == "true") {
                    console.log('loading page: home');
                    this.showHome = true;
                    this.showPhoto = false;

                    if (!this.isExternalUser) {
                        this.refreshDisplay();
                    }
                    return;
                }

                /* .. then check all other awful rules */
                if ((photo || job) && !valJob) {
                    console.log('loading page: photo');
                    this.showHome = false;
                    this.showPhoto = true;
                    return;
                }
                if ((photoFromRef || joboIdFromRef) && !valJob) {
                    console.log('loading page: photo from ref');
                    this.showHome = false;
                    this.showPhoto = true;
                    if (photoIdFromRef) {
                        window.location = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?propertyId=' + photoFromRef + '&photoId=' + photoIdFromRef;
                    } else if (joboIdFromRef) {
                        window.location = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?jobId=' + joboIdFromRef;
                    } else {
                        window.location = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?propertyId=' + photoFromRef;
                    }
                    return;
                }
                if (valJob) {
                    if (this.isMobileView) {
                        console.log('loading page: mobile valjob');
                        this.propertyId = photo;
                        this.jobId = valJob;
                        this.showPageWrapper = false;
                        this.showMobileComps = true;
                        this.refreshDisplay();
                    }
                    return;
                }
                if (valJobFromRef) {
                    console.log('loading page: mobile val job from ref');
                    this.showHome = false;
                    this.showPhoto = false;
                    this.showMobileComps = true;
                    window.location = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?valJobId=' + valJobFromRef;
                    return;
                }

                /* If couldnt do a specific match then fall through to "old" home */
                if (routeName === 'home' || home || hasQupid || fromQivs) {
                    console.log('loading page: home');
                    this.showHome = true;
                    this.showPhoto = false;

                    if (!this.isExternalUser) {
                        this.refreshDisplay();
                    }
                    return;
                }

                /* ... and if couldnt match "old" home then drop through to new router */
                console.log('loading page: router');
                this.showHome = false;
                this.showPhoto = false;
                this.showPageWrapper = true;
                this.showHeader = route.meta.showHeader != null ? route.meta.showHeader : true;

                // FIXME: This is bug in Vue 2 where inheritAttrs doesn't block class attributes from being inherited. Vue 3 fixes this
                this.useRouterClass = Object.hasOwn(route.meta, 'useRouterClass') ? route.meta.useRouterClass : true;
                if (!this.isExternalUser) {
                    this.refreshDisplay();
                }

            },
            refreshDisplay: function() {
                this.showTemplate = !this.showTemplate;
                if (!this.showTemplate) {
                    this.refreshTemplate = true;
                }
            },
            getURLParam: function (sParam, sUrl) {
                var sPageURL = window.location.search.substring(1);
                if (sUrl) {
                    sPageURL = sUrl;
                }
                var sURLVariables = sPageURL.split('&');
                for (var i = 0; i < sURLVariables.length; i++) {
                    var sParameterName = sURLVariables[i].split('=');
                    if (sParameterName[0] == sParam) {
                        return sParameterName[1];
                    }
                }
            },
            openAdminPage: function () {
                const event = {};
                event.searchType = 'admin-page';
                this.emitDisplayEvent(event);
            },
            openUserMaintenance: function () {
                const event = {};
                event.searchType = 'user-maintenance';
                this.emitDisplayEvent(event);
            },
            emitDisplayEvent: function(event) {
                if(this.$route.name === 'home') {
                    EventBus.$emit('display-content', event);
                }
                else {
                    // change the route to home, wait for the home component to mount, then emit the event
                    this.$router.push({name: 'home'}, () => {
                        this.$nextTick(() => {
                            EventBus.$emit('display-content', event);
                        });
                    });
                }
            },
            hideBrowserAlert() {
                this.displayBrowserAlert = false;
                localStorage.setItem('displayBrowserAlert', false);
            }
        },
        updated: function() {
            if (this.refreshTemplate) {
                this.showTemplate = true;
                this.refreshTemplate = false;
            }
        },
        destroyed: function () {
            EventBus.$off('display-content');
        },
        beforeDestroy: function () {
            EventBus.$off('display-content');
        },
    }
</script>

<style lang="scss">
    @import "./global.scss";
</style>
<style lang="scss" src="./components/rollMaintenance/rollMaintenance.scss"></style>

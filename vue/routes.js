import { reportRoutes } from './components/reports/routes';
import { dashboardRoutes } from './components/dashboard/routes';
import { ratingValuationRoute, objectionRoute } from './components/rollMaintenance/routes';
import { CUSTOMER_CARE, RECEPTIONIST_TYPIST } from '@/utils/Roles';
import { store } from '@/DataStore';


const routes = [
    {
        path: '/',
        name: 'home',
    },
    {
        path: '/property/:qpid/summary',
        name: 'property',
    },
    {
        path: '/property/:qpid/detail',
        name: 'property-detail',
    },
    {
        path: '/property/:qpid/home-valuation/:jobId',
        name: 'property-home-valuation',
    },
    {
        path: '/property/:qpid/map',
        name: 'property-map',
    },
    {
        path: '/property/:qpid/sra-values',
        name: 'property-sra-values',
        component: () => import(/* webpackChunkName: "SraValues" */ './components/propertyDetails/sraValues/UpdateSraValuesSection.vue'),
    },
    {
        path: '/property/:id/detail/edit',
        name: 'property-detail-edit',
        component: () => import(/* webpackChunkName: "PropertyDetailsEdit" */ './components/propertyDetails/PropertyDetails.vue'),
    },
    {
        path: '/rural-property/:id/detail/edit',
        name: 'rural-property-detail-edit',
        component: () => import(/* webpackChunkName: "PropertyDetailsRuralEdit" */ './components/propertyDetails/PropertyDetailsRural.vue'),
    },
    {
        path: '/qv-cloud-uploader',
        name: 'qv-cloud-uploader',
        component: () => import('./components/qvCloudUploader/UploadReport.vue'),
    },
    {
        path: '/roll-maintenance',
        name: 'roll-maintenance',
        component: () => import(/* webpackChunkName: "SearchRollMaintenance" */ './components/rollMaintenance/RollMaintenanceDashboard.vue'),
        children: [
            {
                path: 'consents',
                name: 'consents-search',
                component: () => import(/* webpackChunkName: "SearchRollMaintenance" */ './components/rollMaintenance/search/SearchRollMaintenance.vue'),
            },
            {
                path: 'linz',
                name: 'linz-search',
                component: () => import(/* webpackChunkName: "SearchRollMaintenance" */ './components/rollMaintenance/linzSearch/LinzSearchDashboard.vue'),
            },
            {
                path: 'objections',
                name: 'objections-search',
                component: () => import(/* webpackChunkName: "SearchRollMaintenance" */ './components/rollMaintenance/objections/ObjectionsSearchDashboard.vue'),
                props: route => ({ qpid: route.query.qpid })
            },
            {
                path: 'sales',
                name: 'sales-dashboard',
                redirect: to => {
                    const processUser = store.getters['userData/userHasMonarchRole']([RECEPTIONIST_TYPIST, CUSTOMER_CARE]);
                    if (processUser) {
                        return { name: 'sales-to-process' }
                    }
                    return { name: 'sales-inspection' } ;
                },
                component: () => import(/* webpackChunkName: "SearchRollMaintenance" */ './components/rollMaintenance/salesSearch/SalesSearchDashboard.vue'),
                children: [
                    {
                        path: 'process',
                        name: 'sales-to-process',
                        component: () => import(/* webpackChunkName: "SearchRollMaintenance" */ './components/rollMaintenance/salesSearch/SalesProcessingSearch.vue'),
                        meta: {
                          requiredRole: [RECEPTIONIST_TYPIST, CUSTOMER_CARE]
                        },
                        props: { searchType: 'SALES_TO_PROCESS' }
                    },
                    {
                        path: 'inspection',
                        name: 'sales-inspection',
                        component: () => import(/* webpackChunkName: "SearchRollMaintenance" */ './components/rollMaintenance/salesSearch/SalesProcessingSearch.vue'),
                        props: { searchType: 'SALES_INSPECTION' }
                    },
                    {
                        path: 'unlinked',
                        name: 'sales-unlinked',
                        component: ()  => import(/* webpackChunkName: "SearchRollMaintenance" */ './components/rollMaintenance/salesSearch/SalesUnlinkedSearch.vue'),
                    }
                ]
            }
        ]
    },
    {
        path: '/roll-maintenance/:id',
        name: 'roll-maintenance-activity',
        component: () => import(/* webpackChunkName: "BuildingConsentDetails" */ './components/rollMaintenance/buildingConsent/BuildingConsentDetails.vue'),
    },
    ratingValuationRoute,
    objectionRoute,
    {
        path: '/roll-maintenance/:rollMaintenanceActivityId/latest-valuation',
        name: 'roll-maintenance-activity-latest-valuation',
        component: () => import(/* webpackChunkName: "ViewBuildingConsentValuation" */ './components/rollMaintenance/objections/ActionRecordRedirect.vue'),
    },
    {
        path: '/reports',
        name: 'report-dashboard',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ './components/reports/Dashboard.vue'),
        children: reportRoutes,
    },
    {
        path: '/dashboard',
        name: 'work-dashboard',
        component: () => import(/* webpackChunkName: "WorkUnitDashboard" */ './components/dashboard/workUnit/WorkUnitDashboard.vue'),
        children: dashboardRoutes,
    },
    {
      path: '/sale-analysis/:saleId',
        name: 'sale-analysis',
        component: () => import(/* webpackChunkName: "SaleAnalysis" */ './components/analysis/SaleAnalysis.vue'),
        meta: {
          showHeader: false,
        },
        children: [
            {
                path: 'residential',
                name: 'sale-analysis-residential',
                component: () => import(/* webpackChunkName: "SaleAnalysis" */ './components/analysis/ResidentialSaleAnalysis.vue'),
                meta: {
                    showHeader: false,
                },
            },
            {
                path: 'rural',
                name: 'sale-analysis-rural',
                component: () => import(/* webpackChunkName: "SaleAnalysis" */ './components/analysis/RuralSaleAnalysis.vue'),
                meta: {
                    showHeader: false,
                },
            }
        ],
    },
    ...['/roll-maintenance/floorPlan/:qpid', '/roll-maintenance/floorPlan/:qpid/:floorPlanId'].map(
        path => ({
            path,
            name: 'floor-plan-measure',
            component: () => import(/* webpackChunkName: "FloorPlanMeasure" */ './components/rollMaintenance/floorPlan/FloorPlanTool.vue'),
        }),
    ),
    {
        path: '/roll-maintenance/rural-worksheet/:id',
        name: 'rural-worksheet',
        component: () => import(/* webpackChunkName: "RuralWorksheet" */ './components/rollMaintenance/ruralWorksheet/RuralWorksheetDetail.vue'),
    },
    {
        path: '/roll-maintenance/rural-revision-worksheet/:id',
        name: 'rural-revision-worksheet',
        component: () => import(/* webpackChunkName: "RuralWorksheet" */ './components/rollMaintenance/ruralWorksheet/RuralWorksheetDetail.vue'),
    },
    {
        path: '/roll-maintenance/rural-rtv-worksheet/:id',
        name: 'rural-rtv-worksheet',
        component: () => import(/* webpackChunkName: "RuralWorksheet" */ './components/rollMaintenance/ruralWorksheet/RuralWorksheetDetail.vue'),
    },
    {
        path: '/roll-maintenance/commercial-worksheet/:qpid',
        name: 'commercial-worksheet',
        component: () => import(/* webpackChunkName: "CommercialWorksheet" */ './components/rollMaintenance/commercialWorksheet/CommercialWorksheet.vue'),
    },
    {
        path: '/roll-maintenance/commercial-revision-worksheet/:qpid',
        name: 'commercial-revision-worksheet',
        component: () => import(/* webpackChunkName: "CommercialWorksheet" */ './components/rollMaintenance/commercialWorksheet/CommercialWorksheet.vue'),
    },
    {
        path: '/roll-maintenance/:qpid/sale/:id',
        name: 'property-sale',
        component: () => import(/* webpackChunkName: "SaleProcess" */'./components/rollMaintenance/propertySale/SaleProcess.vue'),
    },
    {
        path: '/roll-maintenance/:qpid/sale',
        name: 'property-sale-create',
        component: () => import(/* webpackChunkName: "SaleProcess" */'./components/rollMaintenance/propertySale/SaleProcess.vue'),
    },
    {
        path: '/property/qv-map/:lat/:lng/:qpid',
        name: 'qv-map',
        component: () => import(/* webpackChunkName: "QVMap" */ './components/masterDetails/QVMap.vue'),
        meta: {
            showHeader: false,
            useRouterClass: false,
        },
    },
    {
        path: '/rtv-dashboard',
        name: 'rtv-dashboard',
        component: () => import(/* webpackChunkName: "rtvDashboard" */'./components/rtv/RtvDashboard.vue'),
    },
];

export default routes;

<template>
    <div :class="{ 'highlight': highlight }">
        <h3 class="section-title">
            Derived fields
        </h3>
        <div class="property-draft-section col-container">
            <div :class="{ 'col-row': true }">
                <div
                    class="col col-2"
                    title="Based on Wall Construction and Condition of Principal buildings"
                >
                    <label>
                        <span
                            class="label"
                        >
                            Wall Construction / Condition
                        </span>
                        <span
                            class="derived-field"
                            :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('wallConstruction') }"
                        >
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.wallConstruction : null | description }}
                        </span>
                        /
                        <span
                            class="derived-field"
                            :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('wallCondition') }"
                        >
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.wallCondition : null | description }}
                        </span>
                        <validation-message
                            :validation-set="validationSet"
                            field="dvrSnapshot.wallCondition"
                        />
                    </label>
                </div>
                <div
                    class="col col-2"
                    title="Based on Roof Construction and Condition of Principal buildings"
                >
                    <label>
                        <span
                            class="label"
                        >
                            Roof Construction / Condition
                        </span>
                        <span
                            class="derived-field"
                            :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('roofConstruction') }"
                        >
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.roofConstruction : null | description }}
                        </span>
                        /
                        <span
                            class="derived-field"
                            :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('roofCondition') }"
                        >
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.roofCondition : null | description }}
                        </span>
                        <validation-message
                            :validation-set="validationSet"
                            field="dvrSnapshot.roofCondition"
                        />
                    </label>
                </div>
                <div
                    class="col col-1"
                    title="Based on Modernisation Age in Living Space of Principal Building"
                >
                    <label>
                        <span
                            class="label"
                        >
                            Modernisation
                        </span>
                        <span class="derived-field" :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('isModernised') }">
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.isModernised : null | yesno('—') }}
                        </span>
                    </label>
                </div>
                <div
                    class="col col-1"
                    title="Based on Site Development Quality"
                >
                    <label>
                        <span
                            class="label"
                        >
                            Landscaping
                        </span>
                        <span class="derived-field" :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('landscapingQuality') }">
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.landscapingQuality : null | description }}
                        </span>
                    </label>
                </div>
                <div
                    class="col col-1"
                    title="Yes if Site Improvements includes Deck"
                >
                    <label>
                        <span
                            class="label"
                        >
                            Deck
                        </span>
                        <span class="derived-field" :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('hasDeck') }">
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.hasDeck : null | yesno('—') }}
                        </span>
                    </label>
                </div>
                <div
                    class="col col-1"
                    title="Has Large Other Improvements: Yes if Site Improvements includes Swimming Pool or Tennis Court or there is a Granny Flat, Guest House, Pool House or Sleep Out building"
                >
                    <label>
                        <span
                            class="label"
                        >
                            Large OIs
                        </span>
                        <span class="derived-field" :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('hasLargeOtherImprovements') }">
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.hasLargeOtherImprovements : null | yesno('—') }}
                        </span>
                    </label>
                </div>
                <div
                    class="col col-1"
                    title="Under Main Roof Garaging: Total number of carparks from Garage spaces in Principal buildings that are Apartment, Dwelling, Flats - Multi, Flat – Single, Incomplete Dwelling or Town House"
                >
                    <label>
                        <span
                            class="label"
                        >
                            UMR Garaging
                        </span>
                        <span class="derived-field" :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('numberOfUnderMainRoofGarages') }">
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.numberOfUnderMainRoofGarages : null | numeral('0', '—') }}
                        </span>
                    </label>
                </div>
                <div
                    class="col col-1"
                    title="Freestanding Garaging: Total number of carparks where Site Improvement is Carport or Garage space is in a Garage building"
                >
                    <label>
                        <span
                            class="label"
                        >
                            FS Garaging
                        </span>
                        <span class="derived-field" :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('numberOfFreestandingGarages') }">
                            {{ hasDerivedDvrFields ? propertyDetail.dvrSnapshot.numberOfFreestandingGarages : null | numeral('0', '—') }}
                        </span>
                    </label>
                </div>
                <div class="col col-2">
                    <p
                        v-if="highlight"
                        class="highlight-warning righty"
                    >
                        This data is calculated on save.  Save to see recalculated values.
                        <button
                            data-cy="save-button"
                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                            v-if="showSave"
                            :disabled="!canSave"
                            @click="$emit('click-save')"
                        >
                            Save
                        </button>
                    </p>
                </div>
            </div>
            <div class="col-row" v-if="highlight">
                <div class="col col-12">
                    <label>NOTE: Derived fields are highlighted where they differ from the DVR data.</label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    components: {
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../common/form/ValidationMessage.vue'),
    },
    props: {
        propertyDetail: {
            type: Object,
            required: true,
        },
        showSave: {
            type: Boolean,
            required: true,
        },
        canSave: {
            type: Boolean,
            required: true,
        },
        validationSet: {
            type: Object,
            default: null,
        },
        highlight: {
            type: Boolean,
            required: true,
        },
    },
    computed: {
        hasDerivedDvrFields() {
            // When the version number is 0, then it means it has no pd and therefore can't derive dvr field
            return this.propertyDetail && this.propertyDetail.entityVersion > 0;
        },
    },
    methods: {
        dvrFieldHasChanged(dvrFieldName) {
            return this.propertyDetail
                && this.propertyDetail.dvrSnapshot
                && this.propertyDetail.dvrSnapshot.changedDvrFields instanceof Array
                && this.propertyDetail.dvrSnapshot.changedDvrFields.includes(dvrFieldName);
        },
    },
};
</script>

<!--
TODO Needs to be refactored - this is a temporary
collection of styles for a light touch "kind of like Monarch" view
-->
<style lang="scss" scoped="true" src="../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">

    /* TODO Standardise - this is a "small button" for inline operations */
    button {
        height: 28px;
        line-height: 28px;
        font-size: 0.9em;
    }

    .derived-field {
        cursor: auto;
    }
    .highlight {
        background-color: #eee;
        .col-container {
            background-color: #eee;
        }
    }
    .highlight-field {
        background-color: #ffffcf;
        border-color:#ffeeba;
    }
    .highlight-warning {
        text-align: center;
        font-size: 0.8em;

    }
    label {
        cursor: text;
    }
    .label {
        display: block;
    }
</style>

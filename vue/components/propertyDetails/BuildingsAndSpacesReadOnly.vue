<template>
    <div>
        <h3 class="section-title" data-cy="cy-bas-construction-information-title">
            Construction Information
        </h3>
        <div
            v-for="(building, index) in buildings"
            :key="index"
            class="property-draft-section col-container"
        >
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Type of Building</span>
                        <span>{{ building.buildingType && building.buildingType.description | emptyToDash }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Floor Area</span>
                        <span>{{ building.floorArea | emptyToDash }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">No. of Storeys</span>
                        <span>{{ building.numberOfStoreys | emptyToDash }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Year Built</span>
                        <span>{{ building.yearBuilt | emptyToDash }}</span>
                    </label>
                </div>
                <div class="col col-7">
                    <label>
                        <span class="label">Description</span>
                        <span>{{ building | description }}</span>
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-1">
                    <label>
                        <span class="label">Building Label</span>
                        <span>{{ building.buildingLabel | emptyToDash }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Principl Bldg</span>
                        <span>{{ building.isPrimaryBuilding | yesno('No') }}</span>
                    </label>
                </div>
                <div class="col col-4">
                    <label>
                        <span class="label">Wall Construction</span>
                        <classification-lookup
                            multiple
                            category="WallConstruction_DVR"
                            :value="building.wallConstruction ? building.wallConstruction.definition : null"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Wall Condition</span>
                        <classification-lookup
                            category="FeatureQuality"
                            :value="building.wallConstruction ? building.wallConstruction.quality : null"
                        />
                    </label>
                </div>
                <div class="col col-4">
                    <label>
                        <span class="label">Roof Construction</span>
                        <classification-lookup
                            multiple
                            category="RoofConstruction_DVR"
                            :value="building.roofConstruction ? building.roofConstruction.definition : null"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Roof Condition</span>
                        <classification-lookup
                            category="FeatureQuality"
                            :value="building.roofConstruction ? building.roofConstruction.quality : null"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-4">
                    <label>
                        <span class="label">Floor Construction</span>
                        <classification-lookup
                            multiple
                            category="FloorConstruction_DVR"
                            :value="building.floorConstruction ? building.floorConstruction.definition : null"
                        />
                    </label>
                </div>
                <div class="col col-4">
                    <label>
                        <span class="label">Foundation</span>
                        <classification-lookup
                            multiple
                            category="Foundation_DVR"
                            :value="building.foundation ? building.foundation.definition : null"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label
                        v-if="isInternalUser"
                    >
                        <span class="label">Wiring Age</span>
                        <classification-lookup
                            category="FeatureAge"
                            :value="building.wiring ? building.wiring.age : null"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label
                        v-if="isInternalUser"
                    >
                        <span class="label">Plumbing Age</span>
                        <classification-lookup
                            category="FeatureAge"
                            :value="building.plumbing ? building.plumbing.age : null"
                        />
                    </label>
                </div>
            </div>
            <div
                v-if="isInternalUser"
                class="col-row"
            >
                <div class="col col-2" />
                <div class="col col-3">
                    <label>
                        <span class="label">Insulation</span>
                        <classification-lookup
                            multiple
                            category="Insulation"
                            :value="building.insulation ? building.insulation.definition : null"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Glazing</span>
                        <classification-lookup
                            category="DoubleGlazing"
                            :value="building.glazing ? building.glazing.definition : null"
                        />
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span class="label">Other Features</span>
                        <classification-lookup
                            multiple
                            category="BuildingFeature"
                            :value="building.otherFeatures ? building.otherFeatures.definition : null"
                        />
                    </label>
                </div>
            </div>
        </div>
        <h3
            v-if="isInternalUser"
            class="section-title"
            data-cy="cy-bas-spaces-title"
        >
            Spaces
        </h3>
        <div
            v-if="isInternalUser"
            v-for="(building, index) in buildings"
            :key="`buildings${index}`"
        >
            <div
                v-for="(space, spaceIndex) in building.spaces"
                :key="`building${index}-space${spaceIndex}`"
            >
                <!-- Living Space -->
                <div
                    v-if="space.spaceType && space.spaceType.code === 'LI'"
                    class="property-draft-section col-container"
                >
                    <div class="col-row">
                        <div class="col col-2">
                            <label>
                                <span class="label">Within Building</span>
                                <span>
                                    {{ `${building.buildingType && building.buildingType.description ? building.buildingType.description : ''} ${building.buildingLabel || ''}` }}
                                </span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Space Type</span>
                                <classification-lookup
                                    category="SpaceType"
                                    :value="space.spaceType"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Multiplicity</span>
                                <span>{{ space.numberOfSimilarSpaces | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Floor Area</span>
                                <span>{{ space.floorArea | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Bedrooms</span>
                                <span>{{ space.totalBedrooms | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Double Bedrms</span>
                                <span>{{ space.doubleBedrooms | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Single Bedrms</span>
                                <span>{{ space.singleBedrooms | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Office/Study</span>
                                <span>{{ space.homeOfficeOrStudy | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Bathrooms</span>
                                <span>{{ space.totalBathrooms | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Toilets</span>
                                <span>{{ space.totalToilets | emptyToDash }}</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-row">
                        <div class="col col-2">
                            <label>
                                <span class="label">Space Address / Id</span>
                                <span>{{ space.spaceLabel | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Modernisation</span>
                                <classification-lookup
                                    category="ModernisationAge"
                                    :value="space.modernisationAge"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Interior Cond.</span>
                                <classification-lookup
                                    category="FeatureQuality"
                                    :value="space.quality"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Kitchen Age</span>
                                <classification-lookup
                                    category="FeatureAge"
                                    :value="space.kitchen ? space.kitchen.age : null"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Kitchen Quality</span>
                                <classification-lookup
                                    category="FeatureQuality"
                                    :value="space.kitchen ? space.kitchen.quality : null"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Main Bath Age</span>
                                <classification-lookup
                                    category="FeatureAge"
                                    :value="space.mainBathroom ? space.mainBathroom.age : null"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Main Bath Qty</span>
                                <classification-lookup
                                    category="FeatureQuality"
                                    :value="space.mainBathroom ? space.mainBathroom.quality : null"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Ensuite Age</span>
                                <classification-lookup
                                    category="FeatureAge"
                                    :value="space.ensuite ? space.ensuite.age : null"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Ensuite Quality</span>
                                <classification-lookup
                                    category="FeatureQuality"
                                    :value="space.ensuite ? space.ensuite.quality : null"
                                />
                            </label>
                        </div>
                        <div class="col col-2">
                            <label>
                                <span class="label">Heating Type</span>
                                <classification-lookup
                                    multiple
                                    category="HeatingType"
                                    :value="space.heating ? space.heating.definition : null"
                                />
                            </label>
                        </div>
                    </div>
                </div>
                <div
                    v-else-if="space.spaceType && space.spaceType.code === 'GA'"
                    class="property-draft-section col-container"
                >
                    <div class="col-row">
                        <div class="col col-2">
                            <label>
                                <span class="label">Within Building</span>
                                <span>
                                    {{ `${building.buildingType && building.buildingType.description ? building.buildingType.description : ''} ${building.buildingLabel || ''}` }}
                                </span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Space Type</span>
                                <classification-lookup
                                    category="SpaceType"
                                    :value="space.spaceType"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Carparks</span>
                                <span>{{ space.numberOfCarparks | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Floor Area</span>
                                <span>{{ space.floorArea | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Quality</span>
                                <classification-lookup
                                    category="FeatureQuality"
                                    :value="space.quality"
                                />
                            </label>
                        </div>
                        <div class="col col-6">
                            <label>
                                <span class="label">Garage Features</span>
                                <classification-lookup
                                    multiple
                                    category="GarageFeature"
                                    :value="space.garageFeatures ? space.garageFeatures.definition : null"
                                />
                            </label>
                        </div>
                    </div>
                </div>
                <div
                    v-else
                    class="property-draft-section col-container"
                >
                    <div class="col-row">
                        <div class="col col-2">
                            <label>
                                <span class="label">Within Building</span>
                                <span>
                                    {{ `${building.buildingType && building.buildingType.description ? building.buildingType.description : ''} ${building.buildingLabel || ''}` }}
                                </span>
                            </label>
                        </div>
                        <div class="col col-2">
                            <label>
                                <span class="label">Space Type</span>
                                <classification-lookup
                                    category="SpaceType"
                                    :value="space.spaceType"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Floor Area</span>
                                <span>{{ space.floorArea | emptyToDash }}</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Quality</span>
                                <classification-lookup
                                    category="FeatureQuality"
                                    :value="space.quality"
                                />
                            </label>
                        </div>
                        <div class="col col-6">
                            <label>
                                <span class="label">Other Features</span>
                                <classification-lookup
                                    multiple
                                    category="SpaceFeature"
                                    :value="space.spaceFeatures ? space.spaceFeatures.definition : null"
                                />
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
    components: {
        'classification-lookup': () => import(/* webpackChunkName: "ClassificationLookup" */ '../common/ClassificationLookup.vue'),
    },
    props: {
        buildings: {
            type: Array,
            default: () => [],
        },
    },
    computed: {
        ...mapState('userData', [
            'isInternalUser',
        ]),
    },
};
</script>

<style lang="scss" src='../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}
.label {
    display: block;
}
</style>

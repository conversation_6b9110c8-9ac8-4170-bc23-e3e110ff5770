<template>
    <div>
        <expander v-if="expandable" class="righty" v-model="expanded" displayMode="light" />
        <h2 class="section-title">
            {{title}}
            <div v-if="hasButtons" class="section-buttons righty">
                <ul>
                    <slot name="buttons"></slot>
                </ul>
            </div>
        </h2>
        <slot v-if="expanded">Put a component in me.</slot>
    </div>
</template>

<script>
export default {
    components: {
        'expander': () => import(/* webpackChunkName: "Expander" */ '../common/Expander.vue'),
    },
    props: {
        title: {
            type: String,
        },
        expandable: {
            type: Boolean,
            default: true,
            required: false,
        },
        initialState: {
            type: String,
            default: "expanded",
            required: false,
        },
    },
    data(){
        return{
            expanded: this.initialState === "expanded"
        }
    },
    computed: {
        hasButtons() {
            return !!this.$slots['buttons']
        },
    }
}

</script>

<style lang="scss" scoped>

    /* TODO Quick n dirty */
    h2.section-title {
        font-size: 1.6rem;
        font-weight: 600;
        margin-bottom: 0;
        margin-top: 0.5rem;
        background-color:  #283c64;
        color: #fff;
        padding: 0.5rem;
        padding-left: 1rem;
    }

    h2 .section-buttons {
        margin-right: 10px;
        margin-top: -2px;
    }

</style>

<template>
    <div class="container-fluid">
        <div class="property-draft-section-row">
            <div class="col col-2">
                <label>
                    <span class="label">Grouping</span>
                </label>
                <vue-multiselect
                    v-model="propertyDetail.grouping"
                    :options="propertyGroupingTypes || []"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :value="propertyDetail.grouping"
                    @input="update"
                />
            </div>
            <div class="col col-4">
                <label>
                    <span class="label">QV Category</span>
                </label>
                <vue-multiselect
                    v-model="propertyDetail.qvCategory"
                    :options="qvCategoryTypes || []"
                    :searchable="true"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :value="propertyDetail.qvCategory"
                    @input="update"
                >
                    <template
                        slot="singleLabel"
                        slot-scope="props">
                        <span class="option__desc">
                            <span class="option__title">{{ getQVCategoryDesc(props.option) }}</span>
                        </span>
                    </template>
                </vue-multiselect>
            </div>
            <div class="col col-2">
                <label>
                    <span class="label">Quality Rating for Farm</span>
                </label>
                <vue-multiselect
                    v-model="propertyDetail.ruralDetail.qualityRating"
                    :options="qualityRatingTypes || []"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :value="propertyDetail.ruralDetail.qualityRating"
                    @input="update"
                />
            </div>
            <div class="col col-4">
                <label>
                    <span
                        class="label"
                        title="The properties which are farmed as one"
                    >
                        Farmed With
                    </span>
                    <vue-multiselect
                        v-model="propertyDetail.ruralDetail.farmedWith"
                        :options="farmedWithDropdown || []"
                        :searchable="true"
                        :multiple="true"
                        :taggable="true"
                        track-by="qpid"
                        label="qpid"
                        placeholder=""
                        :limit="3"
                        select-label="⏎ select"
                        deselect-label="⏎ remove"
                        :value="propertyDetail.ruralDetail.farmedWidth"
                        title="The properties which are farmed as one"
                        @tag="
                            searchQuery =>
                                getDetailsForQpids(searchQuery)
                        "
                        @remove="id => removeQpidFromFarmedWith(id)"
                    >
                        <template
                            slot="tag"
                            slot-scope="{ option, remove }"
                        >
                            <span :class="[option.status === 'A' ? 'multiselect__tag' : 'multiselect__tags--red']">
                                <span>{{ option.qpid }}</span>
                                <i
                                    class="multiselect__tag-icon"
                                    @click.prevent="remove(option)"
                                />
                            </span>
                        </template>
                    </vue-multiselect>
                </label>
            </div>
        </div>

        <h2 class="section-title">
            Property Information
        </h2>

        <div class="property-draft-section">
            <div class="col-row">
                <div class="col col-4">
                    <label>
                        <span class="label">Category</span>
                        <classification-dropdown
                            id="category"
                            category="Category_DVR"
                            :value="propertyDetail.category"
                            label="code"
                            :single-label-function="(opt) => `${opt.code} — ${opt.description}`"
                            :disabled="true"
                            :class="errorClasses('category')"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="category"
                        />
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span
                            class="label"
                            title="To add multiple of an improvement type double click on a selection and enter the number."
                        >
                            Nature of Improvements
                        </span>
                        <nature-of-improvements
                            id="natureOfImprovements"
                            :value="propertyDetail.natureOfImprovements"
                            :class="errorClasses('natureOfImprovements')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="natureOfImprovements"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">Property Name</span>
                        <input
                            id="propertyName"
                            v-model="propertyDetail.propertyName"
                            type="text"
                            :class="errorClasses('propertyName')"
                            readonly
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="propertyName"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-4">
                    <label>
                        <span class="label">Land Use</span>
                        <classification-dropdown
                            id="landUse"
                            category="LandUse_DVR"
                            :value="propertyDetail.landUse.landUse"
                            :single-label-function="(opt) => `${opt.code} — ${opt.description}`"
                            :class="errorClasses('landUse.landUse')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="landUse.landUse"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">TA Land Zone</span>
                        <input
                            type="text"
                            readonly
                            :value="propertyDetail.landUse.landZone && propertyDetail.landUse.landZone.description"
                            :class="errorClasses('planNumber')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="landUse.landZone"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Effective Land Area, ha</span>
                        <input
                            id="effectiveLandArea"
                            v-model.number="propertyDetail.site.effectiveLandArea"
                            type="number"
                            min="0"
                            step="0.0001"
                            :class="errorClasses('site.effectiveLandArea')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="site.effectiveLandArea"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Land Area, ha</span>
                        <input
                            id="landArea"
                            v-model="propertyDetail.site.landArea"
                            type="number"
                            min="0"
                            step="0.0001"
                            readonly
                        >
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Māori Land</span>
                        <input
                            type="text"
                            readonly
                            :value="propertyDetail.landUse.isMaoriLand ? 'Yes' : 'No'"
                            :class="errorClasses('isMaoriLand')"
                        >
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Plan ID</span>
                        <input
                            type="text"
                            readonly
                            :value="propertyDetail.planNumber"
                            :class="errorClasses('planNumber')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="propertyName"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Production</span>
                        <input
                            id="production"
                            v-model.number="propertyDetail.landUse.production"
                            type="number"
                            min="0"
                            step="1"
                            :class="errorClasses('landUse.production')"
                            readonly
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="landUse.production"
                        />
                    </label>
                </div>
            </div>
        </div>

        <h2 class="section-title">
            Location Details
        </h2>

        <div class="property-draft-section">
            <div class="col-row">
                <div class="col col-1">
                    <label>
                        <span class="label">Lot position</span>
                        <classification-dropdown
                            id="lotPosition"
                            category="LotPosition_DVR"
                            :value="propertyDetail.site.lotPosition"
                            :class="errorClasses('site.lotPosition')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.lotPosition"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Contour</span>
                        <classification-dropdown
                            id="contour"
                            category="Contour_DVR"
                            :value="propertyDetail.site.contour"
                            :class="errorClasses('site.contour')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.contour"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">View</span>
                        <classification-dropdown
                            id="view"
                            category="View_DVR"
                            :value="propertyDetail.site.view"
                            :class="errorClasses('site.view')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.view"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">View Scope</span>
                        <classification-dropdown
                            id="viewScope"
                            category="ViewScope_DVR"
                            :value="propertyDetail.site.viewScope"
                            :class="errorClasses('site.viewScope')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.viewScope"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">Class of Surrounding Improvements (CSI)</span>
                        <classification-dropdown
                            id="classOfSurroundingImprovements"
                            category="ClassOfSurroundingImprovements_DVR"
                            :value="propertyDetail.site.classOfSurroundingImprovements"
                            :class="errorClasses('site.classOfSurroundingImprovements')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.classOfSurroundingImprovements"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Outlier</span>
                        <span class="field">
                            <yes-no-indeterminate-dropdown
                                id="isOutlier"
                                :value="propertyDetail.isOutlier"
                                :class="errorClasses('isOutlier')"
                                :disabled="true"
                                @input="update"
                            />
                        </span>
                        <validation-message
                            :validation-set="validationSet"
                            field="isOutlier"
                        />
                    </label>
                </div>
            </div>
        </div>

        <h2 class="section-title">
            Property Summary
        </h2>
        <div class="property-draft-section">
            <div class="col-row">
                <div class="col col-3">
                    <label>
                        <span class="label">House Type</span>
                        <classification-dropdown
                            id="houseType"
                            category="HouseType_DVR"
                            :value="propertyDetail.summary.houseType"
                            :class="errorClasses('summary.houseType')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.houseType"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Units of Use</span>
                        <input
                            id="units"
                            v-model.number="propertyDetail.summary.units"
                            type="number"
                            min="0"
                            step="1"
                            :class="errorClasses('summary.units')"
                            readonly
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.units"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span
                            class="label"
                            title="This is the building age from the land use data"
                        >Age</span>
                        <classification-dropdown
                            id="age"
                            category="Age_DVR"
                            :value="propertyDetail.summary.age"
                            :label-function="(opt) => opt.description"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.age"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Effective Year Built</span>
                        <input
                            id="effectiveYearBuilt"
                            v-model.number="propertyDetail.summary.effectiveYearBuilt"
                            type="number"
                            min="0"
                            step="1"
                            readonly
                            :class="errorClasses('summary.effectiveYearBuilt')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.effectiveYearBuilt"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Has Poor Foundations"
                        >
                            Poor Fdn.
                        </span>
                        <yes-no-indeterminate-dropdown
                            id="hasPoorFoundations"
                            :value="propertyDetail.summary.hasPoorFoundations"
                            :class="errorClasses('summary.hasPoorFoundations')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.hasPoorFoundations"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Bedrooms"
                        >
                            Total Bedrms
                        </span>
                        <input
                            id="totalBedrooms"
                            v-model.number="propertyDetail.summary.totalBedrooms"
                            type="number"
                            min="0"
                            step="1"
                            readonly
                            :class="errorClasses('summary.totalBedrooms')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalBedrooms"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Bathrooms"
                        >
                            Total Bathrms
                        </span>
                        <input
                            id="totalBathrooms"
                            v-model.number="propertyDetail.summary.totalBathrooms"
                            type="number"
                            min="0"
                            step="1"
                            :class="errorClasses('summary.totalBathrooms')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalBathrooms"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Toilets"
                        >
                            Total Toilets
                        </span>
                        <input
                            id="totalToilets"
                            v-model.number="propertyDetail.summary.totalToilets"
                            type="number"
                            min="0"
                            step="1"
                            readonly
                            :class="errorClasses('summary.totalToilets')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalToilets"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Building Site Cover, m<sup>2</sup></span>
                        <input
                            id="buildingSiteCover"
                            v-model.number="propertyDetail.summary.buildingSiteCover"
                            type="number"
                            min="0"
                            step="1"
                            readonly
                            :class="errorClasses('summary.buildingSiteCover')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.buildingSiteCover"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Total Floor Area, m<sup>2</sup></span>
                        <input
                            id="totalFloorArea"
                            v-model.number="propertyDetail.summary.totalFloorArea"
                            type="number"
                            min="0"
                            step="1"
                            readonly
                            :class="errorClasses('summary.totalFloorArea')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalFloorArea"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Main Living Area, m<sup>2</sup></span>
                        <input
                            id="mainLivingArea"
                            v-model.number="propertyDetail.summary.mainLivingArea"
                            type="number"
                            min="0"
                            step="1"
                            readonly
                            :class="errorClasses('summary.mainLivingArea')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.mainLivingArea"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Total Living Area, m<sup>2</sup></span>
                        <input
                            id="totalLivingArea"
                            v-model.number="propertyDetail.summary.totalLivingArea"
                            type="number"
                            min="0"
                            step="1"
                            readonly
                            :class="errorClasses('summary.totalLivingArea')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalLivingArea"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Has a Laundry or Workshop outside of living area or garage"
                        >
                            Ldy/Wkshp
                        </span>
                        <yes-no-indeterminate-dropdown
                            id="hasLaundryOrWorkshop"
                            :value="propertyDetail.summary.hasLaundryOrWorkshop"
                            :class="errorClasses('summary.hasLaundryOrWorkshop')"
                            :disabled="true"
                            @input="update"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.hasLaundryOrWorkshop"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Car Access</span>
                        <span class="field">
                            <yes-no-indeterminate-dropdown
                                id="hasCarAccess"
                                :value="propertyDetail.site.hasCarAccess"
                                :class="errorClasses('site.hasCarAccess')"
                                :disabled="true"
                                @input="update"
                            />
                        </span>
                        <validation-message
                            :validation-set="validationSet"
                            field="site.hasCarAccess"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Driveway</span>
                        <span class="field">
                            <yes-no-indeterminate-dropdown
                                id="hasDriveway"
                                :value="propertyDetail.site.hasDriveway"
                                :class="errorClasses('site.hasDriveway')"
                                :disabled="true"
                                @input="update"
                            />
                        </span>
                        <validation-message
                            :validation-set="validationSet"
                            field="site.hasDriveway"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Carparks</span>
                        <input
                            id="carparks"
                            v-model.number="propertyDetail.site.carparks"
                            type="number"
                            min="0"
                            step="1"
                            readonly
                            :class="errorClasses('site.carparks')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="site.carparks"
                        />
                    </label>
                </div>
            </div>
            <derived-dvr-fields :dvr-data="qivsDvrData" />
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import commonUtils from '../../../utils/CommonUtils';

export default {
    components: {
        'vue-multiselect': () => import(/* webpackChunkName: "vue-multiselect" */ 'vue-multiselect'),
        'classification-dropdown': () => import(/* webpackChunkName: "ClassificationDropdown" */ '../../common/form/ClassificationDropdown.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../common/form/ValidationMessage.vue'),
        'yes-no-indeterminate-dropdown': () => import(/* webpackChunkName: "YesNoIndeterminateDropdown" */ '../../common/form/YesNoIndeterminateDropdown.vue'),
        'nature-of-improvements': () => import(/* webpackChunkName: "NatureOfImprovements" */ '../NatureOfImprovements.vue'),
        'derived-dvr-fields': () => import(/* webpackChunkName: "DerivedDvrFields" */ './DerivedDvrFields.vue'),
    },

    mixins: [commonUtils],

    props: {
        propertyDetail: {
            type: Object,
            required: true,
        },
        propertyGroupingTypes: {
            type: Array,
            default: () => [],
            required: true,
        },
        qvCategoryTypes: {
            type: Array,
            default: () => [],
            required: true,
        },
        qualityRatingTypes: {
            type: Array,
            default: () => [],
            required: true,
        },
        landscapingQualityTypes: {
            type: Array,
            default: () => [],
            required: true,
        },
        validationSet: {
            type: Object,
            default: null,
        },
        qpidDetails: {
            type: Array,
            default: () => [],
        },
        qivsDvrData: {
            type: Object,
            default: null,
        },
    },

    data() {
        return {
            farmedWithDropdown: [],
        };
    },

    computed: {
        ...mapState('property', ['property']),
        errors() {
            return (this.validationSet && this.validationSet.errors) || [];
        },
    },

    watch: {
        qpidDetails(newVal) {
            this.addFarmedWithQpid(newVal);
        },
    },

    created() {
        this.farmedWithDropdown =
            this.propertyDetail.ruralDetail && this.propertyDetail.ruralDetail.farmedWith
                ? this.propertyDetail.ruralDetail.farmedWith
                : [];
    },

    methods: {
        getQVCategoryDesc(qvCategory) {
            if (qvCategory) {
                if (qvCategory.description.includes('—')) {
                    return qvCategory.description;
                }
                const description = qvCategory.description.split(qvCategory.code).filter(Boolean);
                return `${qvCategory.code}— ${description}`;
            }
            return '';
        },

        update(data) {
            this.$store.commit('propertyDraft/setSinglePropertyDetail', data);
        },

        getDetailsForQpids(qpids) {
            this.$emit('getDetailsForQpids', {
                id: 'farmedWith',
                qpids,
            });
        },

        addFarmedWithQpid(qpids) {
            if (qpids) {
                const ruralDetail = { ...this.propertyDetail.ruralDetail };

                let farmedWithQpids = [];
                if (ruralDetail.farmedWith) {
                    farmedWithQpids = [...ruralDetail.farmedWith];
                }

                if (qpids && qpids.length > 0) {
                    // Assign back the qpids to the update object.
                    qpids.forEach((qpid) => {
                        // Skip, if it is already added to prevent duplicates.
                        if (!farmedWithQpids.find(linkedItem => linkedItem.qpid === qpid.qpid)) {
                            farmedWithQpids.push(qpid);
                        }

                        // Add to the dropdown as well.
                        if (!this.farmedWithDropdown.find(linkedItem => linkedItem.qpid === qpid.qpid)) {
                            this.farmedWithDropdown.push(qpid);
                        }
                    });
                    this.update({ id: 'farmedWith', value: farmedWithQpids });
                }
            }
        },

        removeQpidFromFarmedWith(removedProperty) {
            if (removedProperty) {
                this.$emit('showModal', {
                    heading: 'Warning',
                    message: `QPID: ${removedProperty.qpid} is no longer farmed with this property`,
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped src="../../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">
button {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    border-bottom: 0.1rem solid #214d90;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.property-draft-section {
    border-bottom: .1rem dashed #e2e2e2;
    padding-top: 0;
    padding-bottom: 0.8rem;
    display: table;
    width: 100%;
}

.button-row {
    display: table;
    width: 100%;
}

.input-optional {
    background-color: #e3f5fc !important;
}

.row-controls {
    div button {
        margin-bottom: 0.5em;
    }
    div:last-child button {
        margin-bottom: unset;
    }
}
</style>

<template>
    <div class="container-fluid">
        <div
            v-for="(nutrient, nutrientIndex) in nutrientManagementConsents"
            :key="nutrient.id"
            class="property-draft-section-row"
        >
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Nutrient Score</span>
                    </label>
                    <Multiselect
                        v-model="nutrient.nutrientScore"
                        :options="qualityRatingTypes || []"
                        track-by="code"
                        label="description"
                        placeholder=""
                        select-label="⏎ select"
                        deselect-label="⏎ remove"
                        @input="value =>updateNutrientDetails(nutrientIndex, 'nutrientScore', value)"
                    />
                </div>
                
                <div class="col col-2">
                    <label>
                        <span class="label">Consent Number</span>
                        <input
                            v-model="nutrient.consentNumber"
                            type="text"
                            maxlength="20"
                            @input="
                                $event =>
                                    updateNutrientDetails(
                                        nutrientIndex,
                                        'consentNumber',
                                        $event.srcElement.value
                                    )
                            "
                        >
                    </label>
                </div>

                <div class="col col-2">
                    <label>
                        <span class="label">Expiry Date of Consent</span>
                        <date-picker
                            :class="{
                                    'past-date': nutrient.consentExpiry && new Date(nutrient.consentExpiry) < new Date()
                                }"
                            type="date"
                            format="DD/MM/YYYY"
                            :value="nutrient.consentExpiry ? new Date(nutrient.consentExpiry) : null"
                            @input="
                                value =>
                                    updateNutrientDetails(nutrientIndex, 'consentExpiry', value)
                            "
                        />
                    </label>
                </div>

                <div class="col col-2">
                    <label>
                        <span class="label">Baseline</span>
                        <input
                            v-model="nutrient.baseline"
                            type="number"
                            min="0"
                            step="1"
                            @input="
                                $event =>
                                    updateNutrientDetails(
                                        nutrientIndex,
                                        'baseline',
                                        parseInt($event.srcElement.value)
                                    )
                            "
                        >
                    </label>
                </div>

                <div class="col col-2">
                    <label>
                        <span class="label">Good Farm Management No.</span>
                        <input
                            v-model="nutrient.goodFarmManagementNumber"
                            type="text"
                            maxlength="20"
                            @input="
                                $event =>
                                    updateNutrientDetails(
                                        nutrientIndex,
                                        'goodFarmManagementNumber',
                                        $event.srcElement.value
                                    )
                            "
                        >
                    </label>
                </div>

                <div class="col col-2">
                    <label>
                        <span class="label">Current Application Rates</span>
                        <input
                            v-model.lazy="nutrient.applicationRatesFormatted"
                            type="text"
                            @input="
                                $event.srcElement.value = $event.srcElement.value
                                    .replace(/[^0-9.]/g, '')
                                    .replace(/(\..*?)\..*/g, '$1')
                            "
                            @change="
                                $event =>
                                    updateNutrientDetails(
                                        nutrientIndex,
                                        'applicationRates',
                                        parseInt($event.srcElement.value)
                                    )
                            "
                        >
                    </label>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-2" />

                <div class="col col-2">
                    <label>
                        <span class="label">Reduction Target</span>
                        <input
                            v-model="nutrient.reductionTarget"
                            type="number"
                            min="0"
                            step="1"
                            @input="
                                $event =>
                                    updateNutrientDetails(
                                        nutrientIndex,
                                        'reductionTarget',
                                        parseInt($event.srcElement.value)
                                    )
                            "
                        >
                    </label>
                </div>

                <div class="col col-2">
                    <label>
                        <span class="label">Date of Target Reduction</span>
                        <date-picker
                            :class="{
                                    'past-date': nutrient.reductionTargetDate && new Date(nutrient.reductionTargetDate) < new Date()
                                }"
                            type="date"
                            format="DD/MM/YYYY"
                            :value="nutrient.reductionTargetDate ? new Date(nutrient.reductionTargetDate) : null"
                            @input="
                                value =>
                                    updateNutrientDetails(
                                        nutrientIndex,
                                        'reductionTargetDate',
                                        value
                                    )
                            "
                        />
                    </label>
                </div>

                <div class="col col-2">
                    <label>
                        <span class="label">Zones</span>
                        <input
                            v-model="nutrient.zones"
                            type="text"
                            maxlength="20"
                            @input="
                                $event =>
                                    updateNutrientDetails(
                                        nutrientIndex,
                                        'zones',
                                        $event.srcElement.value
                                    )
                            "
                        >
                    </label>
                </div>

                <div class="col col-4">
                    <label>
                        <span class="label">Linked Properties for Nutrient Consent</span>
                        <Multiselect
                            v-model="nutrient.linkedProperties"
                            :options="linkedPropertiesDropDown || []"
                            :searchable="true"
                            :multiple="true"
                            :taggable="true"
                            track-by="qpid"
                            label="qpid"
                            placeholder="Enter or choose qpids from the list"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            @input="
                                value =>
                                    updateNutrientDetails(nutrientIndex, 'linkedProperties', value)
                            "
                            @tag="
                                searchQuery =>
                                    getDetailsForQpids(nutrientIndex, searchQuery)
                            "
                        />
                    </label>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-2" />

                <div class="col col-10">
                    <label>
                        <span class="label">Description</span>
                        <input
                            v-model="nutrient.description"
                            type="text"
                            maxlength="500"
                            @input="
                                $event =>
                                    updateNutrientDetails(
                                        nutrientIndex,
                                        'description',
                                        $event.srcElement.value
                                    )
                            "
                        >
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Multiselect from "vue-multiselect";
import DatePicker from "vue2-datepicker";

export default {
    components: {
        Multiselect,
        DatePicker,
    },

    props: {
        nutrientManagementConsents: {
            type: Array,
            default: () => [],
        },
        qualityRatingTypes: {
            type: Array,
            default: () => [],
        },
        nutrientConsentQpids: {
            type: Array,
            default: () => [],
        },
    },

    data() {
        return {
            linkedPropertiesDropDown: [],
            currIndex: -1,
        };
    },
    
    watch: {
        nutrientConsentQpids(newVal) {
            this.addSelectedQpid(this.currIndex, newVal);
        },
        nutrientManagementConsents(newVal){
            if(newVal) {
                this.setAdditionalProperties();
            }
        }
    },

    created() {
        this.setAdditionalProperties();
    },

    methods: {
        getDetailsForQpids(index, qpids) {
            this.currIndex = index;
            this.$emit('getDetailsForQpids', {
                id: 'nutrientConsent',
                qpids,
            });
        },

        addSelectedQpid(index, qpids) {
            if (qpids && index > -1) {
                const nutrientConsentDetails = [...this.nutrientManagementConsents];
                const nutrientConsent = { ...nutrientConsentDetails[index] };

                let linkedProperties = [];
                if (nutrientConsent.linkedProperties) {
                    linkedProperties = [...nutrientConsent.linkedProperties];
                }

                if (qpids && qpids.length > 0) {
                    // Assign back the qpids to the update object.
                    qpids.forEach((qpid) => {
                        // Skip, if it is already added to prevent duplicates.
                        if (!linkedProperties.find(linkedItem => linkedItem.qpid === qpid.qpid)) {
                            linkedProperties.push(qpid);
                        }

                        // Add to the dropdown as well.
                        if (!this.linkedPropertiesDropDown.find(linkedItem => linkedItem.qpid === qpid.qpid)) {
                            this.linkedPropertiesDropDown.push(qpid);
                        }
                    });
                }
                this.updateNutrientDetails(index, 'linkedProperties', linkedProperties);
            }
        },

        updateNutrientDetails(index, key, value) {
            const nutrientConsentDetails = [...this.nutrientManagementConsents];
            const updatedData = { ...nutrientConsentDetails[index] };
            updatedData[key] = value;

            // Update the formatted amount if the field is application rates.
            if (key === 'applicationRates') {
                updatedData.applicationRatesFormatted = this.formatAmount(value);
            }

            if (key === 'linkedProperties') {
                this.$emit('change:linkedProperties');
            }

            // Replace the old object with the updated one.
            nutrientConsentDetails.splice(index, 1, updatedData);
            this.$emit('update', {
                id: 'nutrientManagementConsents',
                value: nutrientConsentDetails,
            });
        },

        formatAmount(amount) {
            const formatter = new Intl.NumberFormat('en-NZ', {
                style: 'currency',
                currency: 'NZD'
            });
            return amount && amount > 0 ? formatter.format(amount) : null;
        },

        setAdditionalProperties() {
            if (this.nutrientManagementConsents && this.nutrientManagementConsents.length > 0) {
                this.nutrientManagementConsents.forEach((item, index) => {
                    // Formmatted application rates
                    this.nutrientManagementConsents[
                        index
                    ].applicationRatesFormatted = this.formatAmount(item.applicationRates);

                    if (this.nutrientManagementConsents[index].linkedProperties) {
                        this.linkedPropertiesDropDown = this.nutrientManagementConsents[index].linkedProperties;
                    }
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped src="../../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">
button {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    border-bottom: 0.1rem solid #214d90;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.button-row {
    display: table;
    width: 100%;
}

.row-controls {
    div button {
        margin-bottom: 0.5em;
    }
    div:last-child button {
        margin-bottom: unset;
    }
}
</style>

<template>
    <div class="property-draft-section-row">
        <div class="col-row">
            <div class="col col-2">
                <label>
                    <span class="label">Wall Construction</span>
                    <input
                        type="text"
                        :value="dvrData && dvrData.wallConstruction && dvrData.wallConstruction.description"
                        readonly
                    >
                </label>
            </div>
            <div class="col col-1">
                <label>
                    <span class="label">Wall Condition</span>
                    <input
                        type="text"
                        :value="dvrData && dvrData.wallCondition && dvrData.wallCondition.description"
                        readonly
                    >
                </label>
            </div>
            <div class="col col-2">
                <label>
                    <span class="label">Roof Construction</span>
                    <input
                        type="text"
                        :value="dvrData && dvrData.roofConstruction && dvrData.roofConstruction.description"
                        readonly
                    >
                </label>
            </div>
            <div class="col col-1">
                <label>
                    <span class="label">Roof Condition</span>
                    <input
                        type="text"
                        :value="dvrData && dvrData.roofCondition && dvrData.roofCondition.description"
                        readonly
                    >
                </label>
            </div>
            <div class="col col-1">
                <label>
                    <span class="label">Modernisation</span>
                </label>
                <yes-no-indeterminate-dropdown
                    id="isModernised"
                    :value="dvrData && dvrData.isModernised"
                    :disabled="true"
                />
            </div>
            <div class="col col-1">
                <label>
                    <span class="label">Landscaping</span>
                    <input
                        type="text"
                        :value="dvrData && dvrData.landscapingQuality && dvrData.landscapingQuality.description"
                        readonly
                    >
                </label>
            </div>
            <div class="col col-1">
                <label>
                    <span class="label">Deck</span>
                </label>
                <yes-no-indeterminate-dropdown
                    id="hasDeck"
                    :value="dvrData && dvrData.hasDeck"
                    :disabled="true"
                />
            </div>
            <div class="col col-1">
                <label>
                    <span class="label">Large OIs</span>
                </label>
                <yes-no-indeterminate-dropdown
                    id="hasLargeOtherImprovements"
                    :value="dvrData && dvrData.hasLargeOis"
                    :disabled="true"
                />
            </div>
            <div class="col col-1">
                <label>
                    <span class="label">UMR Garaging</span>
                    <input
                        id="numberOfUnderMainRoofGarages"
                        :value="dvrData && dvrData.noOfMainroofGarages"
                        type="number"
                        readonly
                    >
                </label>
            </div>
            <div class="col col-1">
                <label>
                    <span class="label">FS Garaging</span>
                    <input
                        id="numberOfFreestandingGarages"
                        :value="dvrData && dvrData.noOfFreestandingGarages"
                        type="number"
                        readonly
                    >
                </label>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    components: {
        'yes-no-indeterminate-dropdown': () => import(/* webpackChunkName: "YesNoIndeterminateDropdown" */ '../../common/form/YesNoIndeterminateDropdown.vue'),
    },
    props: {
        dvrData: {
            type: Object,
            default: null,
        },
    },
};
</script>

<style lang="scss" scoped src="../../rollMaintenance/rollMaintenance.scss"></style>
<style land="scss" scoped>
.property-draft-section-row {
    border-bottom: none;
}
</style>

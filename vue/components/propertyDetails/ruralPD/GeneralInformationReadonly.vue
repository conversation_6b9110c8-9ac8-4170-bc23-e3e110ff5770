<template>
    <div>
        <h3 class="section-title">
            Property Information
        </h3>
        <div class="property-draft-section col-container">
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Grouping</span>
                        {{ propertyDetail.grouping && propertyDetail.grouping.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-4">
                    <label>
                        <span class="label">QV Category</span>
                        {{ propertyDetail.qvCategory && propertyDetail.qvCategory.code + ' — ' + propertyDetail.qvCategory.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Quality Rating for Farm</span>
                        {{ propertyDetail.ruralDetail.qualityRating && propertyDetail.ruralDetail.qualityRating.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Farmed With</span>
                        {{ farmedWithQpidsList | emptyToDash }}
                    </label>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-4">
                    <label>
                        <span class="label">Category</span>
                        <classification-lookup
                            category="Category_DVR"
                            :value="propertyDetail.category"
                            :label-function="(opt) => opt ? `${opt.code} — ${opt.description}` : '—'"
                        />
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span class="label">Nature of Improvements</span>
                        <!-- Nature of improvement structure is different -->
                        <classification-lookup
                            category="NatureOfImprovements_DVR"
                            :value="propertyDetail.natureOfImprovements.map(noi => noi.improvement)"
                            :label-function="(opt) => {
                                const noi = propertyDetail.natureOfImprovements.find(
                                    noi => noi.improvement.code === opt.code
                                );
                                const quantityText = noi != null && noi.quantity > 1
                                    ? ' (' + noi.quantity + ')'
                                    : '';
                                return noi != null
                                    ? `${noi.improvement.description || ''}${quantityText}`
                                    : '';
                            }"
                            :multiple="true"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">Property Name</span>
                        {{ propertyDetail.propertyName | emptyToDash }}
                    </label>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-4">
                    <label>
                        <span class="label">Land Use</span>
                        <classification-lookup
                            category="LandUse_DVR"
                            :value="propertyDetail.landUse.landUse"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">TA Land Zone</span>
                        <span>{{ propertyDetail.landUse.landZone | code }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Effective Land Area, ha</span>
                        {{ propertyDetail.site.effectiveLandArea | hectares('—') }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Land Area, ha</span>
                        {{ propertyDetail.site.landArea | hectares('—') }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Māori Land</span>
                        <span>{{ propertyDetail.landUse.isMaoriLand | yesno('—') }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Plan ID</span>
                        {{ propertyDetail.planNumber | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Production</span>
                        {{ propertyDetail.landUse.production | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>

        <h3 class="section-title">
            Location Details
        </h3>
        <div class="property-draft-section col-container">
            <div class="col-row">
                <div class="col col-1">
                    <label>
                        <span class="label">Lot position</span>
                        <classification-lookup
                            category="LotPosition_DVR"
                            :value="propertyDetail.site.lotPosition"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Contour</span>
                        <classification-lookup
                            category="Contour_DVR"
                            :value="propertyDetail.site.contour"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">View</span>
                        <classification-lookup
                            category="View_DVR"
                            :value="propertyDetail.site.view"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">View Scope</span>
                        <classification-lookup
                            category="ViewScope_DVR"
                            :value="propertyDetail.site.viewScope"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">Class of Surrounding Improvements (CSI)</span>
                        <classification-lookup
                            category="ClassOfSurroundingImprovements_DVR"
                            :value="propertyDetail.site.classOfSurroundingImprovements"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Outlier</span>
                        <span>
                            {{ propertyDetail.isOutlier | yesno('—') }}
                        </span>
                    </label>
                </div>
            </div>
        </div>

        <h3 class="section-title">
            Property Summary
        </h3>
        <div class="property-draft-section col-container">
            <div class="col-row">
                <div class="col col-3">
                    <label>
                        <span class="label">House Type</span>
                        <classification-lookup
                            category="HouseType_DVR"
                            :value="propertyDetail.summary.houseType"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Units of Use</span>
                        {{ propertyDetail.summary.units | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Age</span>
                        <classification-lookup
                            category="Age_DVR"
                            :value="propertyDetail.summary.age"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Effective Year Built</span>
                        {{ propertyDetail.summary.effectiveYearBuilt | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Poor Fdn.</span>
                        {{ propertyDetail.summary.hasPoorFoundations | yesno('—') }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Total Bedrms</span>
                        {{ propertyDetail.summary.totalBedrooms | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Total Bathrms</span>
                        {{ propertyDetail.summary.totalBathrooms | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Total Toilets</span>
                        {{ propertyDetail.summary.totalToilets | emptyToDash }}
                    </label>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Building Site Cover, m<sup>2</sup></span>
                        {{ propertyDetail.summary.buildingSiteCover | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Total Floor Area, m<sup>2</sup></span>
                        {{ propertyDetail.summary.totalFloorArea | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Main Living Area, m<sup>2</sup></span>
                        {{ propertyDetail.summary.mainLivingArea | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Total Living Area, m<sup>2</sup></span>
                        {{ propertyDetail.summary.totalLivingArea | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Ldy/Wkshp</span>
                        {{ propertyDetail.summary.hasLaundryOrWorkshop | yesno('—') }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Car Access</span>
                        <span>
                            {{ propertyDetail.site.hasCarAccess | yesno('—') }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Driveway</span>
                        <span>
                            {{ propertyDetail.site.hasDriveway | yesno('—') }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Carparks</span>
                        {{ propertyDetail.site.carparks | emptyToDash }}
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span
                            class="label"
                        >
                            Wall Construction / Condition
                        </span>
                        {{ qivsDvrData && qivsDvrData.wallConstruction && qivsDvrData.wallConstruction.description | emptyToDash }} /
                        {{ qivsDvrData && qivsDvrData.wallCondition && qivsDvrData.wallCondition.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span
                            class="label"
                        >
                            Roof Construction / Condition
                        </span>
                        {{ qivsDvrData && qivsDvrData.roofConstruction && qivsDvrData.roofConstruction.description | emptyToDash }} /
                        {{ qivsDvrData && qivsDvrData.roofCondition && qivsDvrData.roofCondition.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span
                            class="label"
                        >
                            Modernisation
                        </span>
                        {{ qivsDvrData && qivsDvrData.isModernised | yesno('—') }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span
                            class="label"
                        >
                            Landscaping
                        </span>
                        {{ qivsDvrData && qivsDvrData.landscapingQuality && qivsDvrData.landscapingQuality.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                        >
                            Deck
                        </span>
                        {{ qivsDvrData && qivsDvrData.hasDeck | yesno('—') }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                        >
                            Large OIs
                        </span>
                        {{ qivsDvrData && qivsDvrData.hasLargeOis | yesno('—') }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                        >
                            UMR Garaging
                        </span>
                        {{ qivsDvrData && qivsDvrData.noOfMainroofGarages | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                        >
                            FS Garaging
                        </span>
                        {{ qivsDvrData && qivsDvrData.noOfFreestandingGarages | emptyToDash }}
                        </span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    components: {
        'classification-lookup': () => import(/* webpackChunkName: "ClassificationLookup" */ '../../common/ClassificationLookup.vue')
    },
    props: {
        propertyDetail: {
            type: Object,
            required: true,
        },
        qivsDvrData: {
            type: Object,
            default: null,
        },
    },
    data() {
        return {
            taCode: null,
        };
    },
    computed: {
        ...mapState('property', ['property']),
        taLandZoneClassification() {
            return `TA_${this.taCode}_LandZone_DVR`;
        },
        farmedWithQpidsList() {
            return Array.prototype.map.call(this.propertyDetail.ruralDetail.farmedWith, function(item) { return item.qpid; }).join(",");
        },
    },
    watch: {
        property(newVal) {
            if (newVal && newVal.territorialAuthority) {
                this.loadTAZoneClassification(newVal.territorialAuthority.code);
            }
        },
    },
    methods: {
        async loadTAZoneClassification(taCode) {
            await this.$store.dispatch('fetchTAZoneClassification', taCode);
            this.taCode = taCode;
        },
    },
};
</script>

<style lang="scss" src='../../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}

.label {
    display: block;
}

span.text {
    max-width: 100px;
    word-break: break-all;
    word-wrap:break-word;
    white-space: normal
}
</style>

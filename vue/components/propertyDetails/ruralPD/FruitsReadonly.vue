<template>
    <div>
        <h3 class="section-title">
            Fruits
        </h3>
        <div class="property-draft-section col-container">
            <div
                v-for="(fruit, index) in fruits"
                :key="`fruits${index}`"
                class="col-row"
            >
                <div class="col col-2">
                    <label>
                        <span class="label">Type</span>
                        {{ fruit.fruitType && fruit.fruitType.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Variety</span>
                        {{ fruit.variety && fruit.variety.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Year Planted</span>
                        {{ fruit.age && fruit.age.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Area (ha)</span>
                        {{ fruit.area | emptyToDash }}
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span class="label">Description</span>
                        {{ fruit.description | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        fruits: {
            type: Array,
            default: () => [],
        },
    }
}
</script>

<style lang="scss" src='../../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}
.label {
    display: block;
}
</style>

<template>
    <div class="container-fluid">
        <div
            v-for="(building, buildingIndex) in buildings"
            :key="`buildings-${buildingIndex}`"
        >
            <div
                v-if="!isOtherDwellingType(building.buildingType)"
                class="property-draft-section-row"
            >
                <div class="col-row">
                    <div data-cy="type-of-building" class="col col-2">
                        <label>
                            <span class="label">Type of Building</span>
                            <classification-dropdown
                                :value="building.buildingType"
                                category="RuralOtherBuildingTypes"
                                :single-label-function="opt => opt.description"
                                :class="errorClasses(`buildings[${buildingIndex}].buildingType`)"
                                hide-codes
                                @input="({ id, value }) => updateBuildingType(buildingIndex, value)"
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].buildingType`"
                            />
                        </label>
                    </div>
                    <div class="col col-1">
                        <label>
                            <span class="label">Floor Area</span>
                            <input
                                :id="`buildings-${buildingIndex}-floorArea`"
                                type="number"
                                :value="building.floorArea"
                                min="0"
                                step="1"
                                :class="errorClasses(`buildings[${buildingIndex}].floorArea`)"
                                @blur="
                                    $event =>
                                        updateBuilding(
                                            `${buildingIndex}.floorArea`,
                                            parseFloat($event.srcElement.value)
                                        )
                                "
                            >
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].floorArea`"
                            />
                        </label>
                    </div>
                    <div class="col col-1">
                        <label>
                            <span class="label">Year Built</span>
                            <input
                                :id="`buildings-${buildingIndex}-yearBuilt`"
                                type="number"
                                :value="building.yearBuilt"
                                min="0"
                                step="1"
                                :class="errorClasses(`buildings[${buildingIndex}].yearBuilt`)"
                                @blur="
                                    $event =>
                                        updateBuilding(
                                            `${buildingIndex}.yearBuilt`,
                                            parseFloat($event.srcElement.value)
                                        )
                                "
                            >
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].yearBuilt`"
                            />
                        </label>
                    </div>
                    <div class="col col-7">
                        <label>
                            <span class="label">Description</span>
                            <input
                                :id="`buildings-${buildingIndex}-description`"
                                type="text"
                                :value="building.description"
                                :class="errorClasses(`buildings[${buildingIndex}].description`)"
                                @blur="
                                    $event =>
                                        updateBuilding(
                                            `${buildingIndex}.description`,
                                            $event.srcElement.value
                                        )
                                "
                            >
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].description`"
                            />
                        </label>
                    </div>
                    <div class="col col-1 row-controls">
                        <div>
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                title="Remove a Building"
                                @click="removeBuildingRow(buildingIndex)"
                            >
                                Remove
                            </button>
                        </div>
                        <div>
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                title="Duplicate this building"
                                @click="duplicateBuilding(buildingIndex)"
                            >
                                Copy
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-row">
                    <div class="col col-1">
                        <label>
                            <span class="label">Building Label</span>
                            <input
                                :id="`buildings-${buildingIndex}-buildingLabel`"
                                readonly
                                type="text"
                                :value="building.buildingLabel"
                                :class="errorClasses(`buildings[${buildingIndex}].buildingLabel`)"
                                @blur="
                                    $event =>
                                        updateBuilding(
                                            `${buildingIndex}.buildingLabel`,
                                            $event.srcElement.value
                                        )
                                "
                            >
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].buildingLabel`"
                            />
                        </label>
                    </div>
                    <div class="col col-1">
                        <label>
                            <span class="label">Principal Bldg</span>
                            <yes-no-indeterminate-dropdown
                                :id="`buildings-${buildingIndex}-isPrimaryBuilding`"
                                :value="building.isPrimaryBuilding"
                                :class="errorClasses(`buildings[${buildingIndex}].isPrimaryBuilding`)"
                                @input="
                                    ({ id, value }) =>
                                        updateBuilding(`${buildingIndex}.isPrimaryBuilding`, value)
                                "
                            />
                        </label>
                    </div>
                    <div class="col col-4">
                        <label>
                            <span class="label">Wall Construction</span>
                            <classification-dropdown
                                :value="
                                    building.wallConstruction
                                        ? building.wallConstruction.definition
                                        : null
                                "
                                category="WallConstruction_DVR"
                                multiple
                                :limit="3"
                                :class="
                                    errorClasses(
                                        `buildings[${buildingIndex}].wallConstruction.definition`
                                    )
                                "
                                hide-codes
                                @input="
                                    ({ id, value }) =>
                                        updateBuilding(
                                            `${buildingIndex}.wallConstruction.definition`,
                                            value
                                        )
                                "
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].wallConstruction.definition`"
                            />
                        </label>
                    </div>
                    <div class="col col-1">
                        <label>
                            <span class="label">Wall Condition</span>
                            <classification-dropdown
                                :value="
                                    building.wallConstruction ? building.wallConstruction.quality : null
                                "
                                category="FeatureQuality"
                                :class="
                                    errorClasses(`buildings[${buildingIndex}].wallConstruction.quality`)
                                "
                                hide-codes
                                @input="
                                    ({ id, value }) =>
                                        updateBuilding(
                                            `${buildingIndex}.wallConstruction.quality`,
                                            value
                                        )
                                "
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].wallConstruction.quality`"
                            />
                        </label>
                    </div>
                    <div class="col col-4">
                        <label>
                            <span class="label">Roof Construction</span>
                            <classification-dropdown
                                :value="
                                    building.roofConstruction
                                        ? building.roofConstruction.definition
                                        : null
                                "
                                category="RoofConstruction_DVR"
                                multiple
                                :limit="3"
                                :class="
                                    errorClasses(
                                        `buildings[${buildingIndex}].roofConstruction.definition`
                                    )
                                "
                                hide-codes
                                @input="
                                    ({ id, value }) =>
                                        updateBuilding(
                                            `${buildingIndex}.roofConstruction.definition`,
                                            value
                                        )
                                "
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].roofConstruction.definition`"
                            />
                        </label>
                    </div>
                    <div class="col col-1">
                        <label>
                            <span class="label">Roof Condition</span>
                            <classification-dropdown
                                :value="
                                    building.roofConstruction ? building.roofConstruction.quality : null
                                "
                                category="FeatureQuality"
                                :class="
                                    errorClasses(`buildings[${buildingIndex}].roofConstruction.quality`)
                                "
                                hide-codes
                                @input="
                                    ({ id, value }) =>
                                        updateBuilding(
                                            `${buildingIndex}.roofConstruction.quality`,
                                            value
                                        )
                                "
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].roofConstruction.quality`"
                            />
                        </label>
                    </div>
                </div>

                <div class="col-row">
                    <div class="col col-4">
                        <label>
                            <span class="label">Floor Construction</span>
                            <classification-dropdown
                                :value="
                                    building.floorConstruction
                                        ? building.floorConstruction.definition
                                        : null
                                "
                                category="FloorConstruction_DVR"
                                multiple
                                :limit="3"
                                :class="
                                    errorClasses(
                                        `buildings[${buildingIndex}].floorConstruction.definition`
                                    )
                                "
                                hide-codes
                                @input="
                                    ({ id, value }) =>
                                        updateBuilding(
                                            `${buildingIndex}.floorConstruction.definition`,
                                            value
                                        )
                                "
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].floorConstruction.definition`"
                            />
                        </label>
                    </div>
                    <div class="col col-4">
                        <label>
                            <span class="label">Foundation</span>
                            <classification-dropdown
                                :value="building.foundation ? building.foundation.definition : null"
                                category="Foundation_DVR"
                                multiple
                                :limit="3"
                                :class="
                                    errorClasses(`buildings[${buildingIndex}].foundation.definition`)
                                "
                                hide-codes
                                @input="
                                    ({ id, value }) =>
                                        updateBuilding(`${buildingIndex}.foundation.definition`, value)
                                "
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].foundation.definition`"
                            />
                        </label>
                    </div>
                    <div class="col col-4">
                        <label>
                            <span class="label">Other Features</span>
                            <classification-dropdown
                                :value="
                                    building.otherFeatures ? building.otherFeatures.definition : null
                                "
                                category="BuildingFeature"
                                multiple
                                :limit="3"
                                :class="
                                    errorClasses(`buildings[${buildingIndex}].otherFeatures.definition`)
                                "
                                hide-codes
                                @input="
                                    ({ id, value }) =>
                                        updateBuilding(
                                            `${buildingIndex}.otherFeatures.definition`,
                                            value
                                        )
                                "
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`buildings[${buildingIndex}].otherFeatures.definition`"
                            />
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="button-row">
            <div class="col-row">
                <div class="col col-12">
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                        title="Add a Building"
                        @click="addBuildingRow()"
                    >
                        Add Building
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import set from 'lodash/set';
import { mapGetters } from 'vuex';
import commonUtils from '../../../utils/CommonUtils';

export default {
    components: {
        'classification-dropdown': () => import(/* webpackChunkName: "ClassificationDropdown" */ '../../common/form/ClassificationDropdown.vue'),
        'yes-no-indeterminate-dropdown': () => import(/* webpackChunkName: "YesNoIndeterminateDropdown" */ '../../common/form/YesNoIndeterminateDropdown.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../common/form/ValidationMessage.vue'),
    },

    mixins: [commonUtils],

    props: {
        buildings: {
            type: Array,
            required: true,
        },
        validationSet: {
            type: Object,
            default: null,
        },
        otherDwellingTypes: {
            type: Array,
            default: () => [],
        },
    },

    computed: {
        ...mapGetters(['getCategoryClassifications']),

        buildingLabels() {
            return this.buildings.map((building, buildingIndex) => {
                const buildingType = building.buildingType && building.buildingType.description;
                const label = building.buildingLabel;
                const buildingLabel = [buildingType, label].filter(x => x != null).join(" ");
                return { buildingLabel, buildingIndex };
            });
        },

        errors() {
            return (this.validationSet && this.validationSet.errors) || [];
        }
    },

    methods: {
        updateBuilding(path, value) {
            const buildings = [...this.buildings];
            set(buildings, path, value);
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        updateBuildingType(buildingIndex, value) {
            this.updateBuilding(`${buildingIndex}.buildingType`, value);
            // Update the label when changing the building type
            this.updateBuilding(
                `${buildingIndex}.buildingLabel`,
                this.generateBuildingLabel(value)
            );
        },

        addBuildingRow(newBuilding = {}) {
            const buildings = [...this.buildings];
            // insert empty object after the index
            buildings.splice(buildings.length, 0, newBuilding);
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        generateBuildingLabel(buildingType) {
            // Generates a building label given a building type.
            let buildingLabel;
            if (buildingType && typeof buildingType.code === "string") {
                const buildingCode = buildingType.code.toUpperCase();

                // Find within the building labels used combination of building code, and number
                const buildingNumbers = this.buildings
                    .map(building => building.buildingLabel)
                    .filter(
                        label =>
                            typeof label === "string" && label.match(new RegExp(`^${buildingCode}`))
                    )
                    .map(label => parseInt(label.match(/\d+/), 10));

                // Then iteratively try building labels
                let nextNumber = 1;
                while (buildingNumbers.includes(nextNumber)) {
                    nextNumber += 1;
                }
                buildingLabel = `${buildingCode}${nextNumber}`;
            }
            return buildingLabel;
        },

        duplicateBuilding(buildingIndex) {
            const building = this.buildings[buildingIndex];
            // Assign to variable building object except spaces.
            const { spaces, ...toCopy } = building;
            const newBuilding = JSON.parse(JSON.stringify(toCopy));
            newBuilding.buildingLabel = this.generateBuildingLabel(newBuilding.buildingType);
            this.addBuildingRow(newBuilding);
        },

        removeBuildingRow(buildingIndex) {
            const buildings = [...this.buildings];
            /* eslint-disable-next-line no-alert, no-restricted-globals */
            if (!confirm('This will remove this Building. Are you sure?')) {
                return;
            }
            buildings.splice(buildingIndex, 1);
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        isOtherDwellingType(buildingType) {
            if (buildingType && buildingType.code) {
                const index = this.otherDwellingTypes.findIndex(item => item.code === buildingType.code);
                return index >= 0;
            }
            return false;
        },
    }
};
</script>

<!--
    TODO Needs to be refactored - this is a temporary collection
    of styles for a light touch "kind of like Monarch" view
-->
<style lang="scss" scoped="true" src="../../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">
button {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    border-bottom: 0.1rem solid #214d90;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.button-row {
    display: table;
    width: 100%;
}

.row-controls {
    div button {
        margin-bottom: 0.5em;
    }
    div:last-child button {
        margin-bottom: unset;
    }
}
</style>

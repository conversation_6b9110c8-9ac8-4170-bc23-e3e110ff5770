<template>
    <div class="container-fluid">
        <div
            v-for="(fruit, fruitIndex) in fruits"
            :key="fruit.id"
            class="property-draft-section-row"
        >
            <div class="col col-2">
                <label>
                    <span class="label">Type</span>
                </label>
                <Multiselect
                    v-model="fruit.fruitType"
                    :options="fruitTypes || []"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    @input="(value) => updateFruitDetails(fruitIndex, 'fruitType', value)"
                />
                <validation-message
                    :validation-set="validationSet"
                    :field="`fruits[${fruitIndex}].fruitType`"
                />
            </div>

            <div class="col col-2">
                <label>
                    <span class="label">Variety</span>
                </label>
                <Multiselect
                    v-model="fruit.variety"
                    :options="filterFruitVariety(fruitIndex) || []"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    @input="(value) => updateFruitDetails(fruitIndex, 'variety', value)"
                />
                <validation-message
                    :validation-set="validationSet"
                    :field="`fruits[${fruitIndex}].variety`"
                />
            </div>

            <div class="col col-2">
                <label>
                    <span class="label">Year Planted</span>
                </label>
                <Multiselect
                    v-model="fruit.age"
                    :options="fruitAgeTypes || []"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    @input="value => updateFruitDetails(fruitIndex, 'age', value)"
                />
                <validation-message
                    :validation-set="validationSet"
                    :field="`fruits[${fruitIndex}].age`"
                />
            </div>

            <div class="col col-2">
                <label>
                    <span class="label">Area (ha)</span>
                    <input
                        v-model.lazy="fruit.area"
                        type="number"
                        min="0.0000"
                        value="0.0000"
                        step="0.0001"
                        @change="
                            $event =>
                                updateFruitDetails(
                                    fruitIndex,
                                    'area',
                                    parseFloat($event.srcElement.value)
                                )
                        "
                        @blur="formatArea($event, fruit.area, 4)"
                    >
                    <validation-message
                        :validation-set="validationSet"
                        :field="`fruits[${fruitIndex}].area`"
                    />
                </label>
            </div>

            <div class="col col-3">
                <label>
                    <span class="label">Description</span>
                    <input
                        v-model="fruit.description"
                        type="text"
                        maxlength="500"
                        @input="
                            $event =>
                                updateFruitDetails(
                                    fruitIndex,
                                    'description',
                                    $event.srcElement.value
                                )
                        "
                    >
                    <validation-message
                        :validation-set="validationSet"
                        :field="`fruits[${fruitIndex}].description`"
                    />
                </label>
            </div>

            <div class="col col-1 row-controls">
                <div>
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                        title="Remove this Fruit"
                        @click="removeFruit(fruitIndex)"
                    >
                        Remove
                    </button>
                </div>
                <div>
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                        title="Duplicate this Fruit"
                        @click="duplicateFruit(fruitIndex)"
                    >
                        Copy
                    </button>
                </div>
            </div>
        </div>

        <div class="col-row">
            <div class="col col-12">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                    @click="addFruit"
                >
                    Add Improvement
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import Multiselect from 'vue-multiselect';
import commonUtils from '../../../utils/CommonUtils';

export default {
    components: {
        Multiselect,
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../common/form/ValidationMessage.vue'),
    },

    mixins: [commonUtils],

    props: {
        fruits: {
            type: Array,
            default: () => []
        },
        fruitTypes: {
            type: Array,
            default: () => []
        },
        fruitAgeTypes: {
            type: Array,
            default: () => []
        },
        fruitVarietyTypes: {
            type: Array,
            default: () => []
        },
        validationSet: {
            type: Object,
            default: null,
        },
    },

    computed: {
        errors() { return (this.validationSet && this.validationSet.errors) || []; },
    },

    methods: {
        updateFruitDetails(index, key, value) {
            // Clear the fruit variety when the fruit type is changed.
            if (key === 'fruitType') {
                this.fruits[index].variety = null;
            }

            const fruitDetails = [...this.fruits];
            const updatedFruitData = { ...fruitDetails[index] };
            updatedFruitData[key] = value;

            // Replace the old object with the updated one.
            fruitDetails.splice(index, 1, updatedFruitData);
            this.$emit('update', { id: 'fruit', value: fruitDetails });
        },

        addFruit() {
            const fruitDetails = [...this.fruits];

            // next id
            const lastRecord = fruitDetails.slice(-1)[0];
            const nextId = lastRecord.fruitId < 0 ? lastRecord.fruitId - 1 : -1;

            // insert empty
            fruitDetails.splice(fruitDetails.length, 0, { fruitId: nextId });
            this.$emit('update', { id: 'fruit', value: fruitDetails });
        },

        removeFruit(index) {
            const fruitDetails = [...this.fruits];
            /* eslint-disable-next-line no-alert, no-restricted-globals */
            if (!confirm('This will remove this Fruit record. Are you sure?')) {
                return;
            }
            fruitDetails.splice(index, 1);
            this.$emit('update', { id: 'fruit', value: fruitDetails });
        },

        duplicateFruit(index) {
            const fruitDetails = [...this.fruits];

            // Fruit to be duplicated
            const fruit = fruitDetails[index];
            const newFruit = JSON.parse(JSON.stringify(fruit));

            // Insert the new fruit.
            fruitDetails.splice(index, 0, newFruit);
            this.$emit('update', { id: 'fruit', value: fruitDetails });
        },

        formatArea(e, value, roundPlaces) {
            const formattedValue = parseFloat(value || 0).toFixed(roundPlaces);
            e.srcElement.value = formattedValue;
        },

        filterFruitVariety(index) {
            if (!this.fruitVarietyTypes || this.fruitVarietyTypes.length <= 0) {
                return [];
            }

            return this.fruits[index] && this.fruits[index].fruitType
                ? this.fruitVarietyTypes.filter(item => item.fruitTypeId === parseInt(this.fruits[index].fruitType.code))
                : [];
        },
    }
};
</script>

<style lang="scss" scoped src="../../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">
button {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    border-bottom: 0.1rem solid #214d90;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.button-row {
    display: table;
    width: 100%;
}

.row-controls {
    div button {
        margin-bottom: 0.5em;
    }
    div:last-child button {
        margin-bottom: unset;
    }
}
</style>

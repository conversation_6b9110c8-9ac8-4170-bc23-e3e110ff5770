<template>
    <div class="container-fluid">
        <div
            v-for="(fence, fenceIndex) in fences"
            :key="fence.id"
            class="property-draft-section-row"
        >
            <div class="col col-2">
                <label>
                    <span class="label">Type</span>
                </label>
                <Multiselect
                    v-model="fence.fenceType"
                    :options="fencingTypes || []"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :class="errorClasses(`fences[${fenceIndex}].fenceType`)"
                    @input="(value) =>updateFenceDetails(fenceIndex, 'fenceType', value)"
                />
                <validation-message
                    :validation-set="validationSet"
                    :field="`fences[${fenceIndex}].fenceType`"
                />
            </div>

            <div class="col col-2">
                <label>
                    <span class="label">Year Built</span>
                </label>
                <Multiselect
                    v-model="fence.age"
                    :options="fencingAgeTypes"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :class="errorClasses(`fences[${fenceIndex}].age`)"
                    @input="(value) => updateFenceDetails(fenceIndex, 'age', value)"
                />
                <validation-message
                    :validation-set="validationSet"
                    :field="`fences[${fenceIndex}].age`"
                />
            </div>

            <div class="col col-1">
                <label>
                    <span class="label">Length</span>
                    <input
                        id="length"
                        v-model="fence.length"
                        type="number"
                        min="0"
                        step="1"
                        :class="errorClasses(`fences[${fenceIndex}].length`)"
                        @input="
                            $event =>
                                updateFenceDetails(
                                    fenceIndex,
                                    'length',
                                    parseFloat($event.srcElement.value)
                                )
                        "
                    >
                    <validation-message
                        :validation-set="validationSet"
                        :field="`fences[${fenceIndex}].length`"
                    />
                </label>
            </div>

            <div class="col col-1">
                <label>
                    <span class="label">Quality</span>
                </label>
                <Multiselect
                    v-model="fence.quality"
                    :options="qualityTypes"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :class="errorClasses(`fences[${fenceIndex}].quality`)"
                    @input="(value) => updateFenceDetails(fenceIndex, 'quality', value)"
                />
                <validation-message
                    :validation-set="validationSet"
                    :field="`fences[${fenceIndex}].quality`"
                />
            </div>

            <div class="col col-5">
                <label>
                    <span class="label">Description</span>
                    <input
                        v-model="fence.description"
                        type="text"
                        maxlength="500"
                        :class="errorClasses(`fences[${fenceIndex}].description`)"
                        @input="
                            $event =>
                                updateFenceDetails(
                                    fenceIndex,
                                    'description',
                                    $event.srcElement.value
                                )
                        "
                    >
                    <validation-message
                        :validation-set="validationSet"
                        :field="`fences[${fenceIndex}].description`"
                    />
                </label>
            </div>

            <div class="col col-1 row-controls">
                <div>
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                        title="Remove this Fence"
                        @click="removeFence(fenceIndex)"
                    >
                        Remove
                    </button>
                </div>
                <div>
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                        title="Duplicate this Fence"
                        @click="duplicateFence(fenceIndex)"
                    >
                        Copy
                    </button>
                </div>
            </div>
        </div>

        <div class="col-row">
            <div class="col col-12">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                    @click="addFence"
                >
                    Add Improvement
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import Multiselect from "vue-multiselect";
import commonUtils from '../../../utils/CommonUtils';

export default {
    components: {
        Multiselect,
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../common/form/ValidationMessage.vue'),
    },

    mixins: [commonUtils],

    props: {
        fences: {
            type: Array,
            default: () => []
        },
        fencingTypes: {
            type: Array,
            default: () => []
        },
        fencingAgeTypes: {
            type: Array,
            default: () => []
        },
        qualityTypes: {
            type: Array,
            default: () => []
        },
        validationSet: {
            type: Object,
            default: null,
        },
    },

    computed: {
        errors() { return (this.validationSet && this.validationSet.errors) || []; },
    },

    methods: {
        updateFenceDetails(index, key, value) {
            const fenceDetails = [...this.fences];
            const updatedFenceData = { ...fenceDetails[index] };
            updatedFenceData[key] = value;

            // Replace the old object with the updated one.
            fenceDetails.splice(index, 1, updatedFenceData);
            this.$emit('update', { id: 'fences', value: fenceDetails });
        },

        addFence() {
            const fenceDetails = [...this.fences];

            // next id
            const lastRecord = fenceDetails.slice(-1)[0];
            const nextId = lastRecord.fenceId < 0 ? lastRecord.fenceId - 1 : -1;

            // insert empty
            fenceDetails.splice(fenceDetails.length, 0, { fenceId: nextId });
            this.$emit('update', { id: 'fences', value: fenceDetails });
        },

        removeFence(index) {
            const fenceDetails = [...this.fences];
            /* eslint-disable-next-line no-alert, no-restricted-globals */
            if (!confirm('This will remove this Fence record. Are you sure?')) {
                return;
            }
            fenceDetails.splice(index, 1);
            this.$emit('update', { id: 'fences', value: fenceDetails });
        },

        duplicateFence(index) {
            const fenceDetails = [...this.fences];

            // Fence to be duplicated
            const fence = fenceDetails[index];
            const newFence = JSON.parse(JSON.stringify(fence));

            // Insert the new fence.
            fenceDetails.splice(index, 0, newFence);
            this.$emit('update', { id: 'fences', value: fenceDetails });
        },
    }
};
</script>

<style lang="scss" scoped src="../../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">
button {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    border-bottom: 0.1rem solid #214d90;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.button-row {
    display: table;
    width: 100%;
}

.row-controls {
    div button {
        margin-bottom: 0.5em;
    }
    div:last-child button {
        margin-bottom: unset;
    }
}
</style>

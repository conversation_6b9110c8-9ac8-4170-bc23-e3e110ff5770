<template>
    <div>
        <h3 class="section-title">
            Nutrient Management
        </h3>
        <div
            v-for="(nutrient, index) in nutrientManagementConsents"
            :key="`nutrientManagementConsents${index}`"
            class="property-draft-section col-container"
        >
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Nutrient Score</span>
                        {{ nutrient.nutrientScore && nutrient.nutrientScore.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Consent Number</span>
                        {{ nutrient.consentNumber | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Consent Date Expiry</span>
                        {{ formatDate(nutrient.consentExpiry) | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Baseline</span>
                        {{ nutrient.baseline | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Good Farm Management No.</span>
                        {{ nutrient.goodFarmManagementNumber | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Current Application Rates</span>
                        {{ formatAmount(nutrient.applicationRates) | emptyToDash }}
                    </label>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-2">
                    <label>
                        <span class="label">Reduction Target</span>
                        {{ nutrient.reductionTarget | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Target Reduction Date</span>
                        {{ formatDate(nutrient.reductionTargetDate) | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Zones</span>
                        {{ nutrient.zones | emptyToDash }}
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span class="label">Linked Properties for Nutrient Consent</span>
                        {{ linkedProperties(nutrient.linkedProperties) | emptyToDash }}
                    </label>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-10">
                    <label>
                        <span class="label">Description</span>
                        {{ nutrient.description | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    props: {
        nutrientManagementConsents: {
            type: Array,
            default: () => [],
        },
    },
    methods: {
        formatAmount(amount) {
            const formatter = new Intl.NumberFormat('en-NZ', {
                style: 'currency',
                currency: 'NZD',
            });
            return amount && amount > 0 ? formatter.format(amount) : null;
        },
        formatDate(dateStr) {
            return dateStr ? moment(new Date(dateStr)).format('DD/MM/YYYY') : null;
        },
        linkedProperties(value) {
            if (!value) {
                return '-';
            }
            return value.map((item) => {
                return item.qpid;
            }).join(', ');
        },
    },
};
</script>

<style lang="scss" src='../../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}
.label {
    display: block;
}
</style>

<template>
    <div class="container-fluid">
        <div class="property-draft-section">
            <div
                v-for="(building, buildingIndex) in buildings"
                :key="`buildings-${buildingIndex}`"
            >
                <div
                    v-if="isOtherDwellingType(building.buildingType)"
                    class="property-draft-section-row"
                >
                    <div class="col-row">
                        <div class="col col-2">
                            <label>
                                <span class="label">Type of Building</span>
                                <classification-dropdown
                                    :value="building.buildingType"
                                    category="RuralOtherDwellingTypes"
                                    :single-label-function="opt => opt.description"
                                    :class="errorClasses(`buildings[${buildingIndex}].buildingType`)"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingType(buildingIndex, value)"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].buildingType`"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Floor Area</span>
                                <input
                                    :id="`buildings-${buildingIndex}-floorArea`"
                                    type="number"
                                    :value="building.floorArea"
                                    min="0"
                                    step="1"
                                    :class="errorClasses(`buildings[${buildingIndex}].floorArea`)"
                                    @blur="
                                        $event =>
                                            updateBuilding(
                                                `${buildingIndex}.floorArea`,
                                                parseFloat($event.srcElement.value)
                                            )
                                    "
                                >
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].floorArea`"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Year Built</span>
                                <input
                                    :id="`buildings-${buildingIndex}-yearBuilt`"
                                    type="number"
                                    :value="building.yearBuilt"
                                    min="0"
                                    step="1"
                                    :class="errorClasses(`buildings[${buildingIndex}].yearBuilt`)"
                                    @blur="
                                        $event =>
                                            updateBuilding(
                                                `${buildingIndex}.yearBuilt`,
                                                parseFloat($event.srcElement.value)
                                            )
                                    "
                                >
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].yearBuilt`"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">No. of Storeys</span>
                                <input
                                    :id="`buildings-${buildingIndex}-numberOfStoreys`"
                                    type="number"
                                    :value="building.numberOfStoreys"
                                    min="0"
                                    step="1"
                                    :class="errorClasses(`buildings[${buildingIndex}].numberOfStoreys`)"
                                    @blur="
                                        $event =>
                                            updateBuilding(
                                                `${buildingIndex}.numberOfStoreys`,
                                                parseFloat($event.srcElement.value)
                                            )
                                    "
                                >
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].numberOfStoreys`"
                                />
                            </label>
                        </div>
                        <div class="col col-6">
                            <label>
                                <span class="label">Description</span>
                                <input
                                    :id="`buildings-${buildingIndex}-description`"
                                    type="text"
                                    :value="building.description"
                                    :class="errorClasses(`buildings[${buildingIndex}].description`)"
                                    @blur="
                                        $event =>
                                            updateBuilding(
                                                `${buildingIndex}.description`,
                                                $event.srcElement.value
                                            )
                                    "
                                >
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].description`"
                                />
                            </label>
                        </div>
                        <div class="col col-1 row-controls">
                            <div>
                                <button
                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                    title="Remove a Building"
                                    @click="removeBuildingRow(buildingIndex)"
                                >
                                    Remove
                                </button>
                            </div>
                            <div>
                                <button
                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                    title="Duplicate this building"
                                    @click="duplicateBuilding(buildingIndex)"
                                >
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-row">
                        <div class="col col-1">
                            <label>
                                <span class="label">Building Label</span>
                                <input
                                    :id="`buildings-${buildingIndex}-buildingLabel`"
                                    readonly
                                    type="text"
                                    :value="building.buildingLabel"
                                    :class="errorClasses(`buildings[${buildingIndex}].buildingLabel`)"
                                    @blur="
                                        $event =>
                                            updateBuilding(
                                                `${buildingIndex}.buildingLabel`,
                                                $event.srcElement.value
                                            )
                                    "
                                >
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].buildingLabel`"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Principal Bldg</span>
                                <yes-no-indeterminate-dropdown
                                    :id="`buildings-${buildingIndex}-isPrimaryBuilding`"
                                    :value="building.isPrimaryBuilding"
                                    :class="errorClasses(`buildings[${buildingIndex}].isPrimaryBuilding`)"
                                    @input="
                                        ({ id, value }) =>
                                            updateBuilding(`${buildingIndex}.isPrimaryBuilding`, value)
                                    "
                                />
                            </label>
                        </div>
                        <div class="col col-4">
                            <label>
                                <span class="label">Wall Construction</span>
                                <classification-dropdown
                                    :value="
                                        building.wallConstruction
                                            ? building.wallConstruction.definition
                                            : null
                                    "
                                    category="WallConstruction_DVR"
                                    multiple
                                    :limit="3"
                                    :class="
                                        errorClasses(
                                            `buildings[${buildingIndex}].wallConstruction.definition`
                                        )
                                    "
                                    hide-codes
                                    @input="
                                        ({ id, value }) =>
                                            updateBuilding(
                                                `${buildingIndex}.wallConstruction.definition`,
                                                value
                                            )
                                    "
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].wallConstruction.definition`"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Wall Condition</span>
                                <classification-dropdown
                                    :value="
                                        building.wallConstruction ? building.wallConstruction.quality : null
                                    "
                                    category="FeatureQuality"
                                    :class="
                                        errorClasses(`buildings[${buildingIndex}].wallConstruction.quality`)
                                    "
                                    hide-codes
                                    @input="
                                        ({ id, value }) =>
                                            updateBuilding(
                                                `${buildingIndex}.wallConstruction.quality`,
                                                value
                                            )
                                    "
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].wallConstruction.quality`"
                                />
                            </label>
                        </div>
                        <div class="col col-4">
                            <label>
                                <span class="label">Roof Construction</span>
                                <classification-dropdown
                                    :value="
                                        building.roofConstruction
                                            ? building.roofConstruction.definition
                                            : null
                                    "
                                    category="RoofConstruction_DVR"
                                    multiple
                                    :limit="3"
                                    :class="
                                        errorClasses(
                                            `buildings[${buildingIndex}].roofConstruction.definition`
                                        )
                                    "
                                    hide-codes
                                    @input="
                                        ({ id, value }) =>
                                            updateBuilding(
                                                `${buildingIndex}.roofConstruction.definition`,
                                                value
                                            )
                                    "
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].roofConstruction.definition`"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Roof Condition</span>
                                <classification-dropdown
                                    :value="
                                        building.roofConstruction ? building.roofConstruction.quality : null
                                    "
                                    category="FeatureQuality"
                                    :class="
                                        errorClasses(`buildings[${buildingIndex}].roofConstruction.quality`)
                                    "
                                    hide-codes
                                    @input="
                                        ({ id, value }) =>
                                            updateBuilding(
                                                `${buildingIndex}.roofConstruction.quality`,
                                                value
                                            )
                                    "
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].roofConstruction.quality`"
                                />
                            </label>
                        </div>
                    </div>

                    <div class="col-row">
                        <div class="col col-2" />
                        <div class="col col-4">
                            <label>
                                <span class="label">Floor Construction</span>
                                <classification-dropdown
                                    :value="
                                        building.floorConstruction
                                            ? building.floorConstruction.definition
                                            : null
                                    "
                                    category="FloorConstruction_DVR"
                                    multiple
                                    :limit="3"
                                    :class="
                                        errorClasses(
                                            `buildings[${buildingIndex}].floorConstruction.definition`
                                        )
                                    "
                                    hide-codes
                                    @input="
                                        ({ id, value }) =>
                                            updateBuilding(
                                                `${buildingIndex}.floorConstruction.definition`,
                                                value
                                            )
                                    "
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].floorConstruction.definition`"
                                />
                            </label>
                        </div>
                        <div class="col col-4">
                            <label>
                                <span class="label">Foundation</span>
                                <classification-dropdown
                                    :value="building.foundation ? building.foundation.definition : null"
                                    category="Foundation_DVR"
                                    multiple
                                    :limit="3"
                                    :class="
                                        errorClasses(`buildings[${buildingIndex}].foundation.definition`)
                                    "
                                    hide-codes
                                    @input="
                                        ({ id, value }) =>
                                            updateBuilding(`${buildingIndex}.foundation.definition`, value)
                                    "
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].foundation.definition`"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Wiring Age</span>
                                <classification-dropdown
                                    :value="building.wiring ? building.wiring.age : null"
                                    category="FeatureAge"
                                    :class="errorClasses(`buildings[${buildingIndex}].wiring.age`)"
                                    hide-codes
                                    @input="({id, value}) => updateBuilding(`${buildingIndex}.wiring.age`, value)"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].wiring.age`"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Plumbing Age</span>
                                <classification-dropdown
                                    :value="building.plumbing ? building.plumbing.age : null"
                                    category="FeatureAge"
                                    :class="errorClasses(`buildings[${buildingIndex}].plumbing.age`)"
                                    hide-codes
                                    @input="({id, value}) => updateBuilding(`${buildingIndex}.plumbing.age`, value)"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].plumbing.age`"
                                />
                            </label>
                        </div>
                    </div>

                    <div class="col-row">
                        <div class="col col-2" />
                        <div class="col col-3">
                            <label>
                                <span class="label">Insulation</span>
                                <classification-dropdown
                                    :value="building.insulation ? building.insulation.definition : null"
                                    category="Insulation"
                                    multiple
                                    :limit="3"
                                    :class="errorClasses(`buildings[${buildingIndex}].insulation.definition`)"
                                    hide-codes
                                    @input="({id, value}) => updateBuilding(`${buildingIndex}.insulation.definition`, value)"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].insulation.definition`"
                                />
                            </label>
                        </div>
                        <div class="col col-2">
                            <label>
                                <span class="label">Glazing</span>
                                <classification-dropdown
                                    :value="building.glazing ? building.glazing.definition : null"
                                    category="DoubleGlazing"
                                    :class="errorClasses(`buildings[${buildingIndex}].glazing.definition`)"
                                    hide-codes
                                    @input="({id, value}) => updateBuilding(`${buildingIndex}.glazing.definition`, value)"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].glazing.definition`"
                                />
                            </label>
                        </div>
                        <div class="col col-5">
                            <label>
                                <span class="label">Other Features</span>
                                <classification-dropdown
                                    :value="building.otherFeatures ? building.otherFeatures.definition : null"
                                    category="BuildingFeature"
                                    multiple
                                    :limit="3"
                                    :class="errorClasses(`buildings[${buildingIndex}].otherFeatures.definition`)"
                                    hide-codes
                                    @input="({id, value}) => updateBuilding(`${buildingIndex}.otherFeatures.definition`, value)"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    :field="`buildings[${buildingIndex}].otherFeatures.definition`"
                                />
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-2">
                    <classification-dropdown
                        :value="addBuildingType"
                        category="RuralOtherDwellingTypes"
                        :single-label-function="opt => opt.description"
                        placeholder="Type of Building"
                        hide-codes
                        @input="({id, value}) => addBuildingType = value"
                    />
                </div>
                <div class="col col-2">
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                        @click="addBuildingRow()"
                    >
                        Add Building
                    </button>
                </div>
            </div>
        </div>

        <h2 class="section-title">
            Internal Details
        </h2>

        <div class="property-draft-section">
            <div
                v-for="(building, buildingIndex) in buildings"
                :key="buildingIndex"
            >
                <div
                    v-if="isOtherDwellingType(building.buildingType)"
                >
                    <div
                        v-for="(space, spaceIndex) in building.spaces"
                        :key="`buildings-${buildingIndex}-spaces-${spaceIndex}`"
                    >
                        <!-- Living Space -->
                        <div
                            v-if="space.spaceType && space.spaceType.code === 'LI'"
                            class="property-draft-section-row"
                        >
                            <div class="col-row">
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Within Building</span>
                                        <vue-multiselect
                                            :value="buildingLabels.find(buildingLabel => buildingLabel.buildingIndex === buildingIndex)"
                                            :options="buildingLabels"
                                            label="buildingLabel"
                                            select-label="⏎ select"
                                            track-by="buildingIndex"
                                            deselect-label=""
                                            @select="(value) => updateSpaceBuildingMapping(buildingIndex, value.buildingIndex, spaceIndex)"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Space Type</span>
                                        <classification-dropdown
                                            :value="space.spaceType"
                                            category="SpaceType"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceType`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.spaceType`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceType`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Number of Identical Living Spaces - used for a large multi-unit property that consists of similar units, such as a retirement home."
                                        >
                                            Multiplicity
                                        </span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-numberOfSpaces`"
                                            type="number"
                                            :value="space.numberOfSimilarSpaces"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].numberOfSimilarSpaces`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.numberOfSimilarSpaces`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].numberOfSimilarSpaces`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Floor Area</span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-floorArea`"
                                            type="number"
                                            :value="space.floorArea"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].floorArea`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.floorArea`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].floorArea`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Total Bedrooms in this Living Space."
                                        >
                                            Bedrooms
                                        </span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-totalBedrooms`"
                                            type="number"
                                            :value="space.totalBedrooms"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].totalBedrooms`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.totalBedrooms`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].totalBedrooms`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Double Bedrooms in this Living Space."
                                        >
                                            Double Bedrms
                                        </span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-doubleBedrooms`"
                                            type="number"
                                            :value="space.doubleBedrooms"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].doubleBedrooms`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.doubleBedrooms`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].doubleBedrooms`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Single Bedrooms in this Living Space."
                                        >
                                            Single Bedrms
                                        </span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-singleBedrooms`"
                                            type="number"
                                            :value="space.singleBedrooms"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].singleBedrooms`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.singleBedrooms`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].singleBedrooms`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Offices and Studies in this Living Spaces."
                                        >
                                            Office/Study
                                        </span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-homeOfficeOrStudy`"
                                            type="number"
                                            :value="space.homeOfficeOrStudy"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].homeOfficeOrStudy`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.homeOfficeOrStudy`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].homeOfficeOrStudy`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Bathrooms in this Living Space."
                                        >
                                            Bathrooms
                                        </span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-totalBathrooms`"
                                            type="number"
                                            :value="space.totalBathrooms"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].totalBathrooms`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.totalBathrooms`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].totalBathrooms`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Toilets in this Living Space."
                                        >
                                            Toilets
                                        </span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-totalToilets`"
                                            type="number"
                                            :value="space.totalToilets"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].totalToilets`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.totalToilets`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].totalToilets`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1 row-controls">
                                    <div>
                                        <button
                                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                            title="Remove a Space"
                                            @click="removeSpaceRow(buildingIndex, spaceIndex)"
                                        >
                                            Remove
                                        </button>
                                    </div>
                                    <div>
                                        <button
                                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                            title="Duplicate this space"
                                            @click="duplicateSpace(buildingIndex, spaceIndex)"
                                        >
                                            Copy
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-row">
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Space Address / Id</span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-spaceLabel`"
                                            type="text"
                                            :value="space.spaceLabel"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceLabel`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.spaceLabel`, $event.srcElement.value)"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceLabel`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Modernisation Age</span>
                                        <classification-dropdown
                                            :value="space.modernisationAge"
                                            category="ModernisationAge"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].modernisationAge`)"
                                            :single-label-function="(opt) => opt.description"
                                            :label-function="(opt) => opt.description"
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.modernisationAge`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].modernisationAge`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Interior Condition"
                                        >
                                            Interior Cond.
                                        </span>
                                        <classification-dropdown
                                            :value="space.quality"
                                            category="FeatureQuality"
                                            :filter-options-function="(options) => options.filter(option => option.code !== 'M')"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].quality`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.quality`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].quality`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Kitchen Age</span>
                                        <classification-dropdown
                                            :value="space.kitchen ? space.kitchen.age : null"
                                            category="FeatureAge"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].kitchen.age`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.kitchen.age`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].kitchen.age`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Kitchen Quality</span>
                                        <classification-dropdown
                                            :value="space.kitchen ? space.kitchen.quality : null"
                                            category="FeatureQuality"
                                            :filter-options-function="(options) => options.filter(option => option.code !== 'M')"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].kitchen.quality`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.kitchen.quality`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].kitchen.quality`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Main Bathroom Age"
                                        >
                                            Main Bath Age
                                        </span>
                                        <classification-dropdown
                                            :value="space.mainBathroom ? space.mainBathroom.age : null"
                                            category="FeatureAge"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].mainBathroom.age`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.mainBathroom.age`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].mainBathroom.age`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Main Bathroom Quality"
                                        >
                                            Main Bath Qty
                                        </span>
                                        <classification-dropdown
                                            :value="space.mainBathroom ? space.mainBathroom.quality : null"
                                            category="FeatureQuality"
                                            :filter-options-function="(options) => options.filter(option => option.code !== 'M')"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].mainBathroom.quality`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.mainBathroom.quality`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].mainBathroom.quality`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Ensuite Age</span>
                                        <classification-dropdown
                                            id="age"
                                            :value="space.ensuite ? space.ensuite.age : null"
                                            category="FeatureAge"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].ensuite.age`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.ensuite.age`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].ensuite.age`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Ensuite Quality</span>
                                        <classification-dropdown
                                            id="quality"
                                            :value="space.ensuite ? space.ensuite.quality : null"
                                            category="FeatureQuality"
                                            :filter-options-function="(options) => options.filter(option => option.code !== 'M')"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].ensuite.quality`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.ensuite.quality`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].ensuite.quality`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Heating Type</span>
                                        <classification-dropdown
                                            id="definition"
                                            :value="space.heating ? space.heating.definition : null"
                                            category="HeatingType"
                                            multiple
                                            :limit="3"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].heating.definition`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.heating.definition`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].heating.definition`"
                                        />
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!-- Garage Space -->
                        <div
                            v-else-if="space.spaceType && space.spaceType.code === 'GA'"
                            class="property-draft-section-row"
                        >
                            <div class="col-row">
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Within Building</span>
                                        <vue-multiselect
                                            :value="buildingLabels.find(buildingLabel => buildingLabel.buildingIndex === buildingIndex)"
                                            :options="buildingLabels"
                                            label="buildingLabel"
                                            select-label="⏎ select"
                                            track-by="buildingIndex"
                                            deselect-label=""
                                            @select="(value) => updateSpaceBuildingMapping(buildingIndex, value.buildingIndex, spaceIndex)"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Space Type</span>
                                        <classification-dropdown
                                            id="spaceType"
                                            :value="space.spaceType"
                                            category="SpaceType"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceType`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.spaceType`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceType`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span
                                            class="label"
                                            title="Carparks in this Garage."
                                        >
                                            Carparks
                                        </span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-numberOfCarparks`"
                                            type="number"
                                            :value="space.numberOfCarparks"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].numberOfCarparks`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.numberOfCarparks`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].numberOfCarparks`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Floor Area</span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-floorArea`"
                                            type="number"
                                            :value="space.floorArea"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].floorArea`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.floorArea`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].floorArea`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Quality</span>
                                        <classification-dropdown
                                            id="quality"
                                            :value="space.quality"
                                            category="FeatureQuality"
                                            :filter-options-function="(options) => options.filter(option => option.code !== 'M')"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].quality`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.quality`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].quality`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-5">
                                    <label>
                                        <span class="label">Garage Features</span>
                                        <classification-dropdown
                                            id="garageFeatures"
                                            :value="space.garageFeatures ? space.garageFeatures.definition : null"
                                            category="GarageFeature"
                                            multiple
                                            :limit="4"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].garageFeatures.definition`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.garageFeatures.definition`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].garageFeatures.definition`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1 row-controls">
                                    <div>
                                        <button
                                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                            title="Remove a Space"
                                            @click="removeSpaceRow(buildingIndex, spaceIndex)"
                                        >
                                            Remove
                                        </button>
                                    </div>
                                    <div>
                                        <button
                                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                            title="Duplicate this space"
                                            @click="duplicateSpace(buildingIndex, spaceIndex)"
                                        >
                                            Copy
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Any Other Space -->
                        <div
                            v-else
                            class="property-draft-section-row"
                        >
                            <div class="col-row">
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Within Building</span>
                                        <vue-multiselect
                                            :value="buildingLabels.find(buildingLabel => buildingLabel.buildingIndex === buildingIndex)"
                                            :options="buildingLabels"
                                            label="buildingLabel"
                                            select-label="⏎ select"
                                            track-by="buildingIndex"
                                            deselect-label=""
                                            @select="(value) => updateSpaceBuildingMapping(buildingIndex, value.buildingIndex, spaceIndex)"
                                        />
                                    </label>
                                </div>
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Space Type</span>
                                        <classification-dropdown
                                            id="spaceType"
                                            :value="space.spaceType"
                                            category="SpaceType"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceType`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.spaceType`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceType`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Floor Area</span>
                                        <input
                                            :id="`buildings-${buildingIndex}-spaces-${spaceIndex}-floorArea`"
                                            type="number"
                                            :value="space.floorArea"
                                            min="0"
                                            step="1"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].floorArea`)"
                                            @blur="($event) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.floorArea`, parseFloat($event.srcElement.value))"
                                        >
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].floorArea`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Quality</span>
                                        <classification-dropdown
                                            id="quality"
                                            :value="space.quality"
                                            category="FeatureQuality"
                                            :filter-options-function="(options) => options.filter(option => option.code !== 'M')"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].quality`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.quality`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].quality`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-5">
                                    <label>
                                        <span class="label">Other Features</span>
                                        <classification-dropdown
                                            id="spaceFeatures"
                                            :value="space.spaceFeatures ? space.spaceFeatures.definition : null"
                                            category="SpaceFeature"
                                            multiple
                                            :limit="4"
                                            :class="errorClasses(`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceFeatures.definition`)"
                                            hide-codes
                                            @input="({id, value}) => updateBuilding(`${buildingIndex}.spaces.${spaceIndex}.spaceFeatures.definition`, value)"
                                        />
                                        <validation-message
                                            :validation-set="validationSet"
                                            :field="`buildings[${buildingIndex}].spaces[${spaceIndex}].spaceFeatures.definition`"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1 row-controls">
                                    <div>
                                        <button
                                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                            title="Remove a Space"
                                            @click="removeSpaceRow(buildingIndex, spaceIndex)"
                                        >
                                            Remove
                                        </button>
                                    </div>
                                    <div>
                                        <button
                                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                            title="Duplicate this space"
                                            @click="duplicateSpace(buildingIndex, spaceIndex)"
                                        >
                                            Copy
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add a space -->
            <div class="button-row">
                <div class="col-row">
                    <div class="col col-2">
                        <vue-multiselect
                            ref="addSpaceBuilding"
                            :value="selectedSpaceBuilding"
                            :options="buildingLabels"
                            placeholder="Type of Building"
                            label="buildingLabel"
                            select-label="⏎ select"
                            track-by="buildingIndex"
                            deselect-label=""
                            @select="addSpaceBuildingChanged"
                        />
                    </div>
                    <div class="col col-2">
                        <classification-dropdown
                            id="spaceType"
                            :value="spaceToAdd.type"
                            category="SpaceType"
                            placeholder="Type of Space"
                            hide-codes
                            @input="({id, value}) => spaceToAdd.type = value"
                        />
                    </div>
                    <div class="col col-2">
                        <button
                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                            title="Add a Space to a Building"
                            @click="addSpaceRow(spaceToAdd.buildingIndex, spaceToAdd.type)"
                        >
                            Add Space
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import set from "lodash/set";
import { mapGetters } from 'vuex';
import commonUtils from "../../../utils/CommonUtils";

export default {
    components: {
        'classification-dropdown': () => import(/* webpackChunkName: "ClassificationDropdown" */ '../../common/form/ClassificationDropdown.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../common/form/ValidationMessage.vue'),
        'vue-multiselect': () => import('vue-multiselect'),
        'yes-no-indeterminate-dropdown': () => import(/* webpackChunkName: "YesNoIndeterminateDropdown" */ '../../common/form/YesNoIndeterminateDropdown.vue'),
    },

    mixins: [commonUtils],

    props: {
        buildings: {
            type: Array,
            required: true,
        },
        validationSet: {
            type: Object,
            default: null,
        },
        otherDwellingTypes: {
            type: Array,
            default: () => [],
        },
    },

    data() {
        return {
            addBuildingType: null,
            spaceToAdd: {
                type: null,
                buildingIndex: null,
            },
            selectedSpaceBuilding: null,
        };
    },

    computed: {
        ...mapGetters(['getCategoryClassifications']),

        buildingLabels() {
            const buildings = this.buildings.filter(building => building.buildingType && this.isOtherDwellingType(building.buildingType));
            return buildings.map((building) => {
                const buildingType = building.buildingType && building.buildingType.description;
                const label = building.buildingLabel;
                const buildingLabel = [buildingType, label].filter(x => x != null).join(' ');
                const buildingIndex = this.buildings.findIndex(item => item.buildingType === building.buildingType);
                return { buildingLabel, buildingIndex };
            });
        },

        errors() {
            return (this.validationSet && this.validationSet.errors) || [];
        },
    },

    methods: {
        addBuildingRow(newBuilding = {}) {
            if (!this.addBuildingType) {
                const buildingTypes = [...this.otherDwellingTypes];
                this.addBuildingType = { ...buildingTypes[0] };
            }
            if (this.addBuildingType) {
                newBuilding.buildingType = this.addBuildingType;
                newBuilding.buildingLabel = this.generateBuildingLabel(this.addBuildingType);
            }
            // Buildings is shared between Dwellings and Other Buildings section. If a dwelling record is inserted
            // remove the previous empty record otherwise it will throw validation error.
            const buildings = this.buildings.filter((item) => {
                if (Object.keys(item).length !== 0) {
                    return true;
                }
                return false;
            });
            // insert empty object after the index
            buildings.splice(buildings.length, 0, newBuilding);
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        addSpaceRow(buildingIndex, spaceType) {
            if (buildingIndex == null || !spaceType) {
                return;
            }
            const buildings = [...this.buildings];
            const spaces = buildings[buildingIndex].spaces ? buildings[buildingIndex].spaces : [];
            const spaceIndex = spaces.length;
            // Default number of similar spaces
            const numberOfSimilarSpaces = 1;

            const newSpace = { spaceType, numberOfSimilarSpaces };

            // insert empty space object
            spaces.splice(spaceIndex, 0, newSpace);
            buildings[buildingIndex].spaces = spaces;
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        addSpaceBuildingChanged(value) {
            this.selectedSpaceBuilding = value;
            this.spaceToAdd.buildingIndex = value.buildingIndex;
            const self = this;
            this.$nextTick(() => {
                setTimeout(() => {
                    self.$refs.addSpaceBuilding.deactivate();
                }, 10);
            });
        },

        updateBuilding(path, value) {
            const buildings = [...this.buildings];
            set(buildings, path, value);
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        updateBuildingType(buildingIndex, value) {
            this.updateBuilding(`${buildingIndex}.buildingType`, value);
            // Update the label when changing the building type
            this.updateBuilding(
                `${buildingIndex}.buildingLabel`,
                this.generateBuildingLabel(value)
            );
        },

        updateSpaceBuildingMapping(buildingIndexFrom, buildingIndexTo, spaceIndex) {
            const buildings = [...this.buildings];
            const space = buildings[buildingIndexFrom].spaces.splice(spaceIndex, 1);
            const targetSpaces = buildings[buildingIndexTo].spaces
                ? buildings[buildingIndexTo].spaces
                : [];
            buildings[buildingIndexTo].spaces = targetSpaces.concat(space);
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        generateBuildingLabel(buildingType) {
            // Generates a building label given a building type.
            let buildingLabel;
            if (buildingType && typeof buildingType.code === "string") {
                const buildingCode = buildingType.code.toUpperCase();

                // Find within the building labels used combination of building code, and number
                const buildingNumbers = this.buildings
                    .map(building => building.buildingLabel)
                    .filter(
                        label =>
                            typeof label === "string" && label.match(new RegExp(`^${buildingCode}`))
                    )
                    .map(label => parseInt(label.match(/\d+/), 10));

                // Then iteratively try building labels
                let nextNumber = 1;
                while (buildingNumbers.includes(nextNumber)) {
                    nextNumber += 1;
                }
                buildingLabel = `${buildingCode}${nextNumber}`;
            }
            return buildingLabel;
        },

        duplicateBuilding(buildingIndex) {
            const building = this.buildings[buildingIndex];
            // Assign to variable building object except spaces.
            const { spaces, ...toCopy } = building;
            const newBuilding = JSON.parse(JSON.stringify(toCopy));
            newBuilding.buildingLabel = this.generateBuildingLabel(newBuilding.buildingType);
            this.addBuildingRow(newBuilding);
        },

        duplicateSpace(buildingIndex, spaceIndex) {
            const buildings = [...this.buildings];
            const { spaces } = buildings[buildingIndex];
            // Space to be duplicated
            const space = spaces[spaceIndex];
            const newSpace = JSON.parse(JSON.stringify(space));
            // Insert the new space
            spaces.splice(spaceIndex, 0, newSpace);
            buildings[buildingIndex].spaces = spaces;
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        removeBuildingRow(buildingIndex) {
            const buildings = [...this.buildings];
            /* eslint-disable-next-line no-alert, no-restricted-globals */
            if (!confirm('This will remove this Building and any associated Spaces. Are you sure?')) {
                return;
            }
            this.selectedSpaceBuilding = null;
            buildings.splice(buildingIndex, 1);
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        removeSpaceRow(buildingIndex, spaceIndex) {
            const buildings = [...this.buildings];
            const spaces = buildings[buildingIndex].spaces ? buildings[buildingIndex].spaces : [];
            /* eslint-disable-next-line no-alert, no-restricted-globals */
            if (!confirm('This will remove this Space. Are you sure?')) {
                return;
            }

            spaces.splice(spaceIndex, 1);
            buildings[buildingIndex].spaces = spaces;
            this.$emit('update', { id: 'buildings', value: buildings });
        },

        isOtherDwellingType(buildingType) {
            if (buildingType && buildingType.code) {
                const index = this.otherDwellingTypes.findIndex(item => item.code === buildingType.code);
                return index >= 0;
            }
            return false;
        },
    },
};
</script>

<style lang="scss" scoped="true" src="../../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">
button {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    border-bottom: 0.1rem solid #214d90;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.property-draft-section {
    border-bottom: .1rem dashed #e2e2e2;
    padding-top: 0;
    padding-bottom: 0.8rem;
    display: table;
    width: 100%;
}

.button-row {
    display: table;
    width: 100%;
}

.row-controls {
    div button {
        margin-bottom: 0.5em;
    }
    div:last-child button {
        margin-bottom: unset;
    }
}
</style>

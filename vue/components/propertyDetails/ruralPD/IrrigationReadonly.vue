<template>
    <div>
        <h3 class="section-title">
            Irrigation Source
        </h3>
        <div
            v-for="(irrigationSource, index) in irrigationSourceConsents"
            :key="`irrigationSourceConsents${index}`"
            class="property-draft-section col-container"
        >
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Source of Water</span>
                        {{ irrigationSource.sourceType && irrigationSource.sourceType.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Consent Number</span>
                        {{ irrigationSource.consentNumber | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Expiry</span>
                        {{ formatDate(irrigationSource.consentExpiry) | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Area</span>
                        {{ irrigationSource.area | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Quantum (litres)</span>
                        {{ irrigationSource.quantum | emptyToDash }}
                    </label>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-5">
                    <label>
                        <span class="label">Description</span>
                        {{ irrigationSource.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span class="label">Linked Properties for Irrigation Consent</span>
                        {{ linkedProperties(irrigationSource.linkedProperties) | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>

        <h3 class="section-title">
            Irrigation Type
        </h3>
        <div
            v-for="(irrigationType, index) in irrigationTypeConsents"
            :key="`irrigationTypeConsents${index}`"
            class="property-draft-section col-container"
        >
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Irrigation Type</span>
                        {{ irrigationType.irrigationType && irrigationType.irrigationType.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Consent Number</span>
                        {{ irrigationType.consentNumber | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Expiry</span>
                        {{ formatDate(irrigationType.consentExpiry) | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Area</span>
                        {{ irrigationType.area | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Quantum (litres)</span>
                        {{ irrigationType.quantum | emptyToDash }}
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-10">
                    <label>
                        <span class="label">Description</span>
                        {{ irrigationType.description | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>

        <div class="property-draft-section col-container">
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Quality of Water</span>
                        {{ ruralDetail.waterQualityRating && ruralDetail.waterQualityRating.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Storage of Water</span>
                        {{ ruralDetail.waterStorageType && ruralDetail.waterStorageType.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Size (litres)</span>
                        {{ ruralDetail.waterStorageSize | emptyToDash }}
                    </label>
                </div>
                <div class="col col-6">
                    <label>
                        <span class="label">Linked Properties for Water Storage</span>
                        {{ linkedProperties(ruralDetail.irrigationLinkedWith) | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment';

export default {
    props: {
        irrigationSourceConsents: {
            type: Array,
            default: () => [],
        },
        irrigationTypeConsents: {
            type: Array,
            default: () => [],
        },
        ruralDetail: {
            type: Object,
            default: () => {},
        },
    },
    methods: {
        formatDate(dateStr) {
            return dateStr ? moment(new Date(dateStr)).format('DD/MM/YYYY') : null;
        },
        linkedProperties(value) {
            if (!value) {
                return '-';
            }
            return value.map((item) => {
                return item.qpid;
            }).join(', ');
        },
    },
};
</script>

<style lang="scss" src='../../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}

.label {
    display: block;
}
</style>

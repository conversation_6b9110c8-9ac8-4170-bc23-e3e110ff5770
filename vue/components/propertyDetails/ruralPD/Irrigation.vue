<template>
    <div class="container-fluid">
        <div class="property-draft-section">
            <div
                v-for="(irrigationSource, irrigationSourceIndex) in irrigationSourceConsents"
                :key="irrigationSource.consentId"
                class="property-draft-section-row"
            >
                <div class="col-row">
                    <div class="col col-2">
                        <label>
                            <span class="label">Source of Water</span>
                        </label>
                        <Multiselect
                            v-model="irrigationSource.sourceType"
                            :options="irrigationSourceOptions || []"
                            track-by="code"
                            label="description"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            placeholder=""
                            :data-cy="`irrigationSourceSourceType`+irrigationSourceIndex"
                            @select="
                                value =>
                                    updateIrrigationSourceDetails(irrigationSourceIndex, 'sourceType', value)
                            "
                        />
                    </div>

                    <div class="col col-2">
                        <label>
                            <span class="label">Consent Number</span>
                            <input
                                v-model.lazy="irrigationSource.consentNumber"
                                type="text"
                                maxlength="20"
                                :data-cy="`irrigationSourceConsentNumber`+irrigationSourceIndex"
                                @change="
                                    $event =>
                                        updateIrrigationSourceDetails(irrigationSourceIndex, 'consentNumber', $event.srcElement.value)"
                            >
                        </label>
                    </div>

                    <div class="col col-2">
                        <label>
                            <span class="label">Expiry</span>
                            <date-picker
                                :class="{
                                    'past-date': irrigationSource.consentExpiry && new Date(irrigationSource.consentExpiry) < new Date()
                                }"
                                type="date"
                                :data-cy="`irrigationSourceConsentExpiry`+irrigationSourceIndex"
                                format="DD/MM/YYYY"
                                :value="irrigationSource.consentExpiry ? new Date(irrigationSource.consentExpiry) : null"
                                @input="
                                    value =>
                                        updateIrrigationSourceDetails(irrigationSourceIndex, 'consentExpiry', value)
                                "
                            />
                        </label>
                    </div>

                    <div class="col col-2">
                        <label>
                            <span class="label">Area</span>
                            <input
                                v-model.lazy="irrigationSource.area"
                                type="number"
                                :data-cy="`irrigationSourceArea`+irrigationSourceIndex"
                                @change="
                                    $event =>
                                        updateIrrigationSourceDetails(irrigationSourceIndex, 'area', $event.srcElement.value)
                                "
                            >
                        </label>
                    </div>

                    <div class="col col-2">
                        <label>
                            <span class="label">Quantum (litres)</span>
                            <input
                                v-model.lazy="irrigationSource.quantum"
                                type="number"
                                :data-cy="`irrigationSourceQuantum`+irrigationSourceIndex"
                                @change="
                                    $event =>
                                        updateIrrigationSourceDetails(irrigationSourceIndex, 'quantum', $event.srcElement.value)
                                "
                            >
                        </label>
                    </div>

                    <div class="col col-2 row-controls">
                        <div>
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                title="Remove Irrigation Source"
                                :data-cy="`removeIrrigationButton`+irrigationSourceIndex"
                                @click="removeIrrigationSourceRow(irrigationSourceIndex)"
                            >
                                Remove
                            </button>
                        </div>
                        <div>
                            <button
                                v-if="irrigationSourceIndex === irrigationSourceConsents.length - 1"
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                title="Add Irrigation Source"
                                data-cy="addIrrigationSourceButton"
                                @click="addIrrigationSourceRow()"
                            >
                                Add
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-row">
                    <div class="col col-2" />
                    <div class="col col-6">
                        <label>
                            <span class="label">Description</span>
                            <input
                                v-model.lazy="irrigationSource.description"
                                type="text"
                                maxlength="500"
                                :data-cy="`irrigationSourceDescription`+irrigationSourceIndex"
                                @change="
                                    $event =>
                                        updateIrrigationSourceDetails(irrigationSourceIndex, 'description', $event.srcElement.value )"
                            >
                        </label>
                    </div>

                    <div class="col col-4">
                        <label>
                            <span class="label">Linked Properties for Irrigation Consent</span>
                            <Multiselect
                                v-model="irrigationSource.linkedProperties"
                                :options="irrigationLinkedPropertyOptions || []"
                                :searchable="true"
                                :multiple="true"
                                :taggable="true"
                                track-by="qpid"
                                label="qpid"
                                :data-cy="`irrigationSourceLinkedProperties`+irrigationSourceIndex"
                                placeholder="Enter or choose qpids from the list"
                                select-label="⏎ select"
                                deselect-label="⏎ remove"
                                @input="value => updateIrrigationSourceDetails(irrigationSourceIndex, 'linkedProperties', value, false)"
                                @tag="searchQuery => getDetailsForQpids(irrigationSourceIndex, 'irrigationLinkedQpids', searchQuery)"
                                @remove="id => removeQpidFromSourceConsents(irrigationSourceIndex, id)"
                                @open="setLinkedPropertiesDropDown(irrigationSource.linkedProperties)"
                            />
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="property-draft-section">
            <div
                v-for="(irrigationTypeData, irrigationTypeIndex) in irrigationTypeConsents"
                :key="irrigationTypeData.consentId"
                class="property-draft-section-row"
            >
                <div class="col-row">
                    <div class="col col-2">
                        <label>
                            <span class="label">Type of Irrigation</span>
                        </label>
                        <Multiselect
                            v-model="irrigationTypeData.irrigationType"
                            :options="irrigationTypeOptions || []"
                            track-by="code"
                            label="description"
                            placeholder=""
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            :data-cy="`irrigationTypeDataIrrigationType`+irrigationTypeIndex"
                            @input="
                                value =>
                                    updateIrrigationTypeDetails(irrigationTypeIndex, 'irrigationType', value)
                            "
                        />
                    </div>

                    <div class="col col-2">
                        <label>
                            <span class="label">Consent Number</span>
                            <input
                                v-model="irrigationTypeData.consentNumber"
                                type="text"
                                maxlength="20"
                                :data-cy="`irrigationTypeDataConsentNumber`+irrigationTypeIndex"
                                @input="
                                    $event =>
                                        updateIrrigationTypeDetails(irrigationTypeIndex, 'consentNumber', $event.srcElement.value)"
                            >
                        </label>
                    </div>

                    <div class="col col-2">
                        <label>
                            <span class="label">Expiry</span>
                            <date-picker
                                :class="{
                                    'past-date': irrigationTypeData.consentExpiry && new Date(irrigationTypeData.consentExpiry) < new Date()
                                }"
                                type="date"
                                format="DD/MM/YYYY"
                                :data-cy="`irrigationTypeDataConsentExpiry`+irrigationTypeIndex"
                                :value="irrigationTypeData.consentExpiry ? new Date(irrigationTypeData.consentExpiry) : null"
                                @input="
                                    value =>
                                        updateIrrigationTypeDetails(irrigationTypeIndex, 'consentExpiry', value)
                                "
                            />
                        </label>
                    </div>

                    <div class="col col-2">
                        <label>
                            <span class="label">Area</span>
                            <input
                                v-model="irrigationTypeData.area"
                                type="number"
                                :data-cy="`irrigationTypeDataArea`+irrigationTypeIndex"
                                @input="
                                    $event =>
                                        updateIrrigationTypeDetails(irrigationTypeIndex, 'area', $event.srcElement.value)
                                "
                            >
                        </label>
                    </div>

                    <div class="col col-2">
                        <label>
                            <span class="label">Quantum (litres)</span>
                            <input
                                v-model="irrigationTypeData.quantum"
                                type="number"
                                :data-cy="`irrigationTypeDataQuantum`+irrigationTypeIndex"
                                @input="
                                    $event =>
                                        updateIrrigationTypeDetails(irrigationTypeIndex, 'quantum', $event.srcElement.value)
                                "
                            >
                        </label>
                    </div>

                    <div class="col col-2 row-controls">
                        <div>
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                title="Remove Irrigation Type"
                                :data-cy="`irrigationTypeRemoveButton`+irrigationTypeIndex"
                                @click="removeIrrigationTypeRow(irrigationTypeIndex)"
                            >
                                Remove
                            </button>
                        </div>
                        <div>
                            <button
                                v-if="irrigationTypeIndex === irrigationTypeConsents.length - 1"
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                title="Add Irrigation Type"
                                data-cy="addIrrigationTypeButton"
                                @click="addIrrigationTypeRow()"
                            >
                                Add
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-row">
                    <div class="col col-2" />

                    <div class="col col-10">
                        <label>
                            <span class="label">Description</span>
                            <input
                                v-model="irrigationTypeData.description"
                                type="text"
                                maxlength="500"
                                :data-cy="`irrigationTypeDataDescription`+irrigationTypeIndex"
                                @input="
                                    $event =>
                                        updateIrrigationTypeDetails(irrigationTypeIndex, 'description', $event.srcElement.value )"
                            >
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="property-draft-section-row">
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Quality of Water</span>
                    </label>
                    <Multiselect
                        v-model="ruralDetail.waterQualityRating"
                        :options="waterQualityOptions || []"
                        track-by="code"
                        label="description"
                        placeholder=""
                        select-label="⏎ select"
                        deselect-label="⏎ remove"
                        data-cy="waterQualityRating"
                        @input="
                            value =>
                                updateRuralDetails('waterQualityRating', value)
                        "
                    />
                </div>

                <div class="col col-2">
                    <label>
                        <span class="label">Storage of Water</span>
                    </label>
                    <Multiselect
                        v-model="ruralDetail.waterStorageType"
                        :options="waterStorageOptions || []"
                        track-by="code"
                        label="description"
                        placeholder=""
                        select-label="⏎ select"
                        deselect-label="⏎ remove"
                        data-cy="waterStorageType"
                        @input="
                            value =>
                                updateRuralDetails('waterStorageType', value)
                        "
                    />
                </div>

                <div class="col col-2">
                    <label>
                        <span class="label">Size (litres)</span>
                        <input
                            v-model.lazy="ruralDetail.waterStorageSize"
                            type="number"
                            data-cy="waterStorageSize"
                            @change="
                                $event =>
                                    updateRuralDetails('waterStorageSize', $event.srcElement.value)
                            "
                        >
                    </label>
                </div>

                <div class="col col-6">
                    <label>
                        <span class="label">Linked Properties for Water Storage</span>
                        <Multiselect
                            v-model="ruralDetail.irrigationLinkedWith"
                            :options="irrigationLinkedPropertyOptions || []"
                            :searchable="true"
                            :multiple="true"
                            :taggable="true"
                            track-by="qpid"
                            label="qpid"
                            placeholder="Enter or choose qpids from the list"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            data-cy="irrigationLinkedWith"
                            @tag="
                                searchQuery =>
                                    getDetailsForQpids(0, 'irrigationWaterStorageQpids', searchQuery)
                            "
                            @remove="clearLinkedPropertyQpid"
                            @open="setLinkedPropertiesDropDown(ruralDetail.irrigationLinkedWith)"
                        />
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import 'vue2-datepicker/index.css';

export default {
    components: {
        Multiselect: () => import('vue-multiselect'),
        DatePicker: () => import('vue2-datepicker'),
    },
    props: {
        irrigationSourceConsents: {
            type: Array,
            default: () => [],
        },
        irrigationTypeConsents: {
            type: Array,
            default: () => [],
        },
        ruralDetail: {
            type: Object,
            default: () => {},
        },
        irrigationTypeOptions: {
            type: Array,
            default: () => [],
        },
        irrigationSourceOptions: {
            type: Array,
            default: () => [],
        },
        waterStorageOptions: {
            type: Array,
            default: () => [],
        },
        waterQualityOptions: {
            type: Array,
            default: () => [],
        },
        irrigationLinkedQpids: {
            type: Array,
            default: () => [],
        },
        irrigationWaterStorageQpids: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            irrigationLinkedPropertyOptions: [],
            currIndex: -1,
        };
    },
    computed: {
        waterQualityRating() {
            if (
                this.ruralDetail &&
                this.ruralDetail.waterQualityRating
            ) {
                return this.ruralDetail.waterQualityRating;
            }
            return {};
        },
        waterStorageType() {
            if (
                this.ruralDetail &&
                this.ruralDetail.waterStorageType
            ) {
                return this.ruralDetail.waterStorageType;
            }
            return {};
        },
        waterStorageSize() {
            if (
                this.ruralDetail &&
                this.ruralDetail.waterStorageSize &&
                this.ruralDetail.waterStorageSize !== null
            ) {
                return this.ruralDetail.waterStorageSize;
            }
            return null;
        },
        irrigationLinkedWith() {
            if (
                this.ruralDetail &&
                this.ruralDetail.irrigationLinkedWith &&
                this.ruralDetail.irrigationLinkedWith.length > 0
            ) {
                return this.ruralDetail.irrigationLinkedWith;
            }
            return [];
        },
    },
    watch: {
        irrigationLinkedQpids(newVal) {
            this.updateIrrigationSourceQpid(this.currIndex, newVal);
        },
        irrigationWaterStorageQpids(newVal) {
            this.updateIrrigationLinkedWithQpid(newVal);
        },
    },
    methods: {
        addedLinkedProperties() {
            this.$emit('change:linkedProperties')
        },
        setLinkedPropertiesDropDown(value) {
            this.irrigationLinkedPropertyOptions = value || [];
        },
        updateIrrigationSourceDetails(index, key, value, showAlert = true) {
            const irrigationSourceDetails = [...this.irrigationSourceConsents];
            const updatedData = { ...irrigationSourceDetails[index] };
            updatedData[key] = value;

            if (updatedData.consentId > 0 && updatedData.linkedProperties && showAlert) {
                this.addedLinkedProperties();
            }

            // Replace the old object with the updated one.
            irrigationSourceDetails.splice(index, 1, updatedData);
            this.$emit('update', {
                id: 'irrigationSourceConsents',
                value: irrigationSourceDetails,
            });
        },
        addIrrigationSourceRow(newSource = {}) {
            const irrigationSourceConsents = [...this.irrigationSourceConsents];
            // next id
            const lastRecord = irrigationSourceConsents.slice(-1)[0];
            newSource.consentId = lastRecord.consentId < 0 ? lastRecord.consentId - 1 : -1;
            newSource.linkedProperties = [];
            // insert empty object after the index
            irrigationSourceConsents.splice(irrigationSourceConsents.length, 0, newSource);
            this.$emit('update', { id: 'irrigationSourceConsents', value: irrigationSourceConsents });
        },
        getDetailsForQpids(index, id, qpids) {
            this.currIndex = index;
            this.$emit('getDetailsForQpids', {
                id,
                qpids,
            });
        },
        updateIrrigationSourceQpid(index, qpids) {
            if (qpids) {
                const irrigationSourceDetails = [...this.irrigationSourceConsents];
                const irrigationSourceData = { ...irrigationSourceDetails[index] };

                let linkedProperties = [];
                if (irrigationSourceData.linkedProperties) {
                    linkedProperties = [...irrigationSourceData.linkedProperties];
                }

                if (qpids && qpids.length > 0) {
                    qpids.forEach((qpid) => {
                        if (!linkedProperties.find(linkedItem => linkedItem.qpid === qpid.qpid)) {
                            linkedProperties.push(qpid);
                        }

                        if (!this.irrigationLinkedPropertyOptions.find(linkedItem => linkedItem.qpid === qpid.qpid)) {
                            this.irrigationLinkedPropertyOptions.push(qpid);
                        }
                    });
                    this.updateIrrigationSourceDetails(index, 'linkedProperties', linkedProperties);
                }
            }
        },
        updateIrrigationTypeDetails(index, key, value) {
            const irrigationTypeConsents = [...this.irrigationTypeConsents];
            const updatedData = { ...irrigationTypeConsents[index] };
            updatedData[key] = value;

            // Replace the old object with the updated one.
            irrigationTypeConsents.splice(index, 1, updatedData);
            this.$emit('update', {
                id: 'irrigationTypeConsents',
                value: irrigationTypeConsents,
            });
        },
        addIrrigationTypeRow(newType = {}) {
            const irrigationTypeConsents = [...this.irrigationTypeConsents];
            // next id
            const lastRecord = irrigationTypeConsents.slice(-1)[0];
            newType.consentId = lastRecord.consentId < 0 ? lastRecord.consentId - 1 : -1;
            // insert empty object after the index
            irrigationTypeConsents.splice(irrigationTypeConsents.length, 0, newType);
            this.$emit('update', { id: 'irrigationTypeConsents', value: irrigationTypeConsents });
        },
        removeIrrigationSourceRow(irrigationSourceIndex) {
            const irrigationSourceConsents = [...this.irrigationSourceConsents];
            /* eslint-disable-next-line no-alert, no-restricted-globals */
            if (!confirm('This will remove this Irrigation Source Consent. Are you sure?')) {
                return;
            }
            irrigationSourceConsents.splice(irrigationSourceIndex, 1);
            this.$emit('update', { id: 'irrigationSourceConsents', value: irrigationSourceConsents });
        },
        removeIrrigationTypeRow(irrigationTypeIndex) {
            const irrigationTypeConsents = [...this.irrigationTypeConsents];
            /* eslint-disable-next-line no-alert, no-restricted-globals */
            if (!confirm('This will remove this Irrigation Type Consent. Are you sure?')) {
                return;
            }
            irrigationTypeConsents.splice(irrigationTypeIndex, 1);
            this.$emit('update', { id: 'irrigationTypeConsents', value: irrigationTypeConsents });
        },
        updateIrrigationLinkedWithQpid(qpids) {
            if (qpids) {
                const linkedProperties = this.irrigationLinkedWith || [];
                if (qpids && qpids.length > 0) {
                    qpids.forEach((qpid) => {
                        if (!linkedProperties.find(linkedItem => linkedItem.qpid === qpid.qpid)) {
                            linkedProperties.push(qpid);
                        }
                        if (!this.irrigationLinkedPropertyOptions.find(linkedItem => linkedItem.qpid === qpid.qpid)) {
                            this.irrigationLinkedPropertyOptions.push(qpid);
                        }
                    });
                    this.updateRuralDetails('irrigationLinkedWith', linkedProperties);
                }
                this.addedLinkedProperties();
            }
        },
        updateRuralDetails(key, value, showAlert = true) {
            this.$emit('update', { id: key, value });
        },
        clearLinkedPropertyQpid(removeQpid) {
            this.$emit('showModal', {
                heading: 'Warning',
                message: `QPID - ${removeQpid.qpid} is removed from this Water Storage source.`,
            });
            const updatedIrrigationLinkedWith = this.irrigationLinkedWith.filter(element => element.qpid !== removeQpid.qpid);
            this.updateRuralDetails('irrigationLinkedWith', updatedIrrigationLinkedWith, false);
        },
        removeQpidFromSourceConsents(index, removedProperty) {
            this.$emit('showModal', {
                heading: 'Warning',
                message: `The consent details will be removed from QPID - ${removedProperty.qpid}`,
            });
            const irrigationSourceDetails = [...this.irrigationSourceConsents];
            const updatedData = { ...irrigationSourceDetails[index] };
            const updatedPropertyList = updatedData.linkedProperties.filter(property => property.qpid !== removedProperty.qpid);
            this.updateIrrigationSourceDetails(index, 'linkedProperties', updatedPropertyList, false);
        },
    },
};
</script>

<style lang="scss" scoped src="../../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">
button {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    border-bottom: 0.1rem solid #214d90;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.property-draft-section {
    border-bottom: .1rem dashed #e2e2e2;
    padding-top: 0;
    padding-bottom: 0.8rem;
    display: table;
    width: 100%;
}

.button-row {
    display: table;
    width: 100%;
}

.row-controls {
    div button {
        margin-bottom: 0.5em;
    }
    div:last-child button {
        margin-bottom: unset;
    }
}
</style>

<template>
    <div>
        <h3 class="section-title">
            Fencing
        </h3>
        <div class="property-draft-section col-container">
            <div
                v-for="(fence, index) in fences"
                :key="`fences${index}`"
                class="col-row"
            >
                <div class="col col-2">
                    <label>
                        <span class="label">Type</span>
                        {{ fence.fenceType && fence.fenceType.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Year Built</span>
                        {{ fence.age && fence.age.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Length</span>
                        {{ fence.length | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Quality</span>
                        {{ fence.quality && fence.quality.description | emptyToDash }}
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span class="label">Description</span>
                        {{ fence.description | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        fences: {
            type: Array,
            default: () => [],
        },
    }
}
</script>

<style lang="scss" src='../../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}
.label {
    display: block;
}
</style>

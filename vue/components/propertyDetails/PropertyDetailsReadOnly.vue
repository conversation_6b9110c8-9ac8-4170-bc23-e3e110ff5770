<template>
    <div>
        <general-info-section-read-only
            v-if="propertyDetail && !isRuralProperty"
            :property-detail="propertyDetail"
            :is-commercial="isCommercialProperty"
            :qv-property-details="qvPropertyDetails"
            :zone-info="zoneInfo"
            :is-rural-property="isRuralProperty"
            :can-edit="false"
        />
        <general-info-section-read-only-rural-pd
            v-if="propertyDetail && isRuralProperty"
            :property-detail="propertyDetail"
            :qivs-dvr-data="qivsDvrData"
        />
        <derived-dvr-fields-section
            v-if="propertyDetail && showDerivedDvrFields && !isRuralProperty"
            :property-detail="propertyDetail"
            :show-save="false"
            :can-save="false"
            :highlight="false"
        />
        <irrigation-section-read-only
            v-if="propertyDetail && propertyDetail.ruralDetail && isRuralProperty"
            :irrigation-source-consents="irrigationSourceConsents"
            :irrigation-type-consents="irrigationTypeConsents"
            :rural-detail="propertyDetail.ruralDetail"
        />
        <nutrient-management-read-only
            v-if="propertyDetail && propertyDetail.ruralDetail && isRuralProperty"
            :nutrient-management-consents="nutrientManagementConsents"
        />
        <rating-apportionments-read-only
            v-if="propertyDetail && propertyDetail.ratingApportionments && propertyDetail.ratingApportionments.length > 0"
            :apportionments="propertyDetail.ratingApportionments"
        />
        <buildings-and-spaces-read-only
            v-if="propertyDetail && showBuildingsAndSpaces && !isCommercialProperty"
            :buildings="buildings"
        />
        <site-improvements-section-read-only
            v-if="propertyDetail && !isCommercialProperty"
            :site-development="siteDevelopment"
            :other-improvements="otherImprovements"
        />
        <fences-section-read-only
            v-if="propertyDetail && propertyDetail.ruralDetail && isRuralProperty"
            :fences="fences"
        />
        <fruits-section-read-only
            v-if="propertyDetail && propertyDetail.ruralDetail && isRuralProperty"
            :fruits="fruits"
        />
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    components: {
        'general-info-section-read-only': () => import(/* webpackChunkName: "GeneralInfoReadOnly" */ './GeneralInfoReadOnly.vue'),
        'general-info-section-read-only-rural-pd': () => import(/* webpackChunkName: "GeneralInfoReadOnlyRuralPD" */ './ruralPD/GeneralInformationReadonly.vue'),
        'derived-dvr-fields-section': () => import(/* webpackChunkName: "DerivedDvrFields" */ './DerivedDvrFields.vue'),
        'buildings-and-spaces-read-only': () => import(/* webpackChunkName: "BuildingsAndSpacesReadOnly" */ './BuildingsAndSpacesReadOnly.vue'),
        'nutrient-management-read-only': () => import(/* webpackChunkName: "NutrientManagementReadonly" */ './ruralPD/NutrientManagementReadonly.vue'),
        'irrigation-section-read-only': () => import(/* webpackChunkName: "IrrigationReadonly" */ './ruralPD/IrrigationReadonly.vue'),
        'rating-apportionments-read-only': () =>import('./residential/RatingApportionmentsReadOnly.vue'),
        'site-improvements-section-read-only': () => import(/* webpackChunkName: "SiteImprovementsReadOnly" */ './SiteImprovementsReadOnly.vue'),
        'fences-section-read-only': () => import(/* webpackChunkName: "FencingReadonly" */ './ruralPD/FencingReadonly.vue'),
        'fruits-section-read-only': () => import(/* webpackChunkName: "FruitsReadonly" */ './ruralPD/FruitsReadonly.vue'),
    },
    props: {
        propertyDetail: {
            type: Object,
            required: true,
        },
        qvPropertyDetails: {
            type: Object,
            required: false
        },
        zoneInfo: {
            type: Object,
            required: false
        },
        showDerivedDvrFields: {
            type: Boolean,
            default: true,
        },
        showBuildingsAndSpaces: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            taCode: null,
        };
    },
    computed: {
        ...mapState('propertyDraft', {
            qivsDvrData: 'qivsDvrData',
        }),
        buildings() {
            if (!this.propertyDetail.buildings || this.propertyDetail.buildings.length === 0) {
                return [];
            }
            return this.propertyDetail.buildings;
        },
        otherImprovements() {
            if (
                !this.propertyDetail.otherImprovements
                || this.propertyDetail.otherImprovements.length === 0
            ) {
                return [];
            }
            return this.propertyDetail.otherImprovements;
        },
        siteDevelopment() {
            if (
                this.propertyDetail
                && this.propertyDetail.site
                && this.propertyDetail.site.siteDevelopment
            ) {
                return this.propertyDetail.site.siteDevelopment;
            }
            return {
                quality: null,
                description: null,
            };
        },
        nutrientManagementConsents() {
            if (
                this.propertyDetail
                && this.propertyDetail.ruralDetail
                && this.propertyDetail.ruralDetail.nutrientManagementConsents
                && this.propertyDetail.ruralDetail.nutrientManagementConsents.length > 0
            ) {
                return this.propertyDetail.ruralDetail.nutrientManagementConsents;
            }
            return [{ consentId: -1 }];
        },
        irrigationSourceConsents() {
            if (
                this.propertyDetail
                && this.propertyDetail.ruralDetail
                && this.propertyDetail.ruralDetail.irrigationSourceConsents
                && this.propertyDetail.ruralDetail.irrigationSourceConsents.length > 0
            ) {
                return this.propertyDetail.ruralDetail.irrigationSourceConsents;
            }
            return [{ consentId: -1 }];
        },
        irrigationTypeConsents() {
            if (
                this.propertyDetail
                && this.propertyDetail.ruralDetail
                && this.propertyDetail.ruralDetail.irrigationTypeConsents
                && this.propertyDetail.ruralDetail.irrigationTypeConsents.length > 0
            ) {
                return this.propertyDetail.ruralDetail.irrigationTypeConsents;
            }
            return [{ consentId: -1 }];
        },
        fences() {
            if (
                this.propertyDetail
                && this.propertyDetail.ruralDetail
                && this.propertyDetail.ruralDetail.fences
                && this.propertyDetail.ruralDetail.fences.length > 0
            ) {
                return this.propertyDetail.ruralDetail.fences;
            }
            return [];
        },
        fruits() {
            if (
                this.propertyDetail
                && this.propertyDetail.ruralDetail
                && this.propertyDetail.ruralDetail.fruit
                && this.propertyDetail.ruralDetail.fruit.length > 0
            ) {
                return this.propertyDetail.ruralDetail.fruit;
            }
            return [];
        },
        isRuralProperty() {
            if (!this.propertyDetail
                || !this.propertyDetail.category
                || !this.propertyDetail.category.code) {
                return false;
            }
            const propertyBaseCategory = this.propertyDetail.category.code.charAt(0);
            const valuableRuralCategories = 'ADFHPS';
            if (valuableRuralCategories.includes(propertyBaseCategory)) {
                return true;
            }
            return false;
        },
        isCommercialProperty() {
            if (!this.propertyDetail
                || !this.propertyDetail.category
                || !this.propertyDetail.category.code) {
                return false;
            }
            const propertyBaseCategory = this.propertyDetail.category.code.charAt(0);
            const valuableCommercialCategories = 'CIOU';
            if (valuableCommercialCategories.includes(propertyBaseCategory)) {
                return true;
            }
            return false;
        },
    },
    watch: {},
    async mounted() {
        if (this.isRuralProperty && this.propertyDetail && this.propertyDetail.qpid) {
            await this.getDvrDataFromQivs(this.propertyDetail.qpid);
        }
    },
    methods: {
        async getDvrDataFromQivs(qpid) {
            if (qpid) {
                try {
                    await this.$store.dispatch('propertyDraft/getDvrDataFromQivs', qpid);
                } catch (error) {
                    this.handleException(error);
                }
            }
        },
    },
};
</script>

<style lang="scss" src='../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}

.label {
    display: block;
}

</style>

<template>
    <multiselect
        v-if="opts && opts.length > 0"
        :value="selectedValue"
        :options="opts"
        label="description"
        :custom-label="getLabelFunction"
        track-by="code"
        :multiple="true"
        :taggable="true"
        :allow-empty="true"
        select-label="⏎ select"
        deselect-label="⏎ remove"
        :loading="!classificationsLoaded"
        :limit="limit"
        placeholder=""
        :disabled="disabled"
        @input="input"
        ref="multiSelect"
        :key="refresh"
        :class="{ 'qv-multiselect-error': errors && errors.length > 0 }"
    >
        <template
            slot="singleLabel"
            slot-scope="props"
        >
            <span>{{ props.option.description }}</span>
        </template>
        <template
            slot="tag"
            slot-scope="props"
        >
            <span
                class="multiselect__tag"
                @blur="endEdit(props.option)"
            >
                <span
                    v-text="props.option.description"
                    @dblclick.prevent.stop="startEdit(props.option)"
                    @mousedown.prevent.stop=""
                    @click.prevent.stop=""
                />
                <span v-if="props.option.quantity > 1 && !props.option.edit ">
                    &nbsp;( <span v-text="props.option.quantity" /> )
                </span>
                <span v-if="props.option.edit">
                    <input
                        ref="quantityInput"
                        type="number"
                        class="quantity"
                        @keypress.capture="keyed"
                        v-model.number="props.option.quantity"
                        @keypress.enter.capture="endEdit(props.option)"
                        @mousedown.capture=""
                        @click.capture=""
                        @blur="endEdit(props.option)"
                    >
                </span>
                <i
                    aria-hidden="true"
                    tabindex="1"
                    class="multiselect__tag-icon"
                    @keypress.enter.prevent="props.remove(props.option)"
                    @mousedown.prevent="props.remove(props.option)"
                />
            </span>
        </template>
    </multiselect>
</template>

<script>
import Multiselect from 'vue-multiselect';
import dropDownMixin from '../common/form/DropDownMixin'

export default {
    components: {
        Multiselect,
    },
    mixins: [dropDownMixin],
    data() {
        return {
            refresh: true
        };
    },
    props: {
        id: {
            type: String,
            default: '',
        },
        category: {
            type: String,
            default: 'NatureOfImprovements_DVR',
        },
        value: {
            type: [Object, Array],
            default: () => null,
        },
        sortFunction: {
            type: Function,
            default(a, b) {
                if (a.sortOrder !== b.sortOrder) {
                    return a.sortOrder - b.sortOrder;
                }
                // sortOrders are equal
                if (a.code < b.code) {
                    return -1;
                }
                return 1;
            },
        },
        hideCodes: {
            type: Boolean,
            default: false,
        },
        limit: {
            type: Number,
            default: undefined,
        },
        filterOptionsFunction: {
            type: Function,
            default: null,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        errors: {
            type: Array,
            default: () => [],
        },
    },
    watch: {
        'value': {
            immediate: true,
            handler(newVal, oldVal) {
                const options = this.opts;
                if(!options) return;

                // Clear quantity on all options
                Object.values(options).forEach((classification) => {
                    classification.quantity = null;
                });

                if(!newVal) return;

                // Set quantity on classifications for provided values
                Object.values(newVal).forEach((noi) => {
                    const x = options.find(val => val.code === noi.improvement.code);
                    if(x)
                        x.quantity = noi.quantity;
                });
          	}
        },
        'opts': {
            immediate: true,
            handler(newVal, oldVal) {
                if(newVal && newVal.length > 0)
                    this.$nextTick(() => {
                        this.bindMultiSelect(this.$refs.multiSelect);
                    });
          	}
        },
    },
    computed: {
        classificationsLoaded() {
            return this.$store.getters.classificationsLoaded;
        },
        opts() {
            if (!this.classificationsLoaded) return [];
            if (!this.category) return [];
            const classifications = this.$store.getters.getCategoryClassifications(this.category);
            if (!classifications) throw new Error(`Couldn't find classification for ${this.category}`);
            if (this.filterOptionsFunction) {
                return (this.filterOptionsFunction([...classifications].sort(this.sortFunction)));
            }
            return [...classifications].sort(this.sortFunction);
        },
        getLabelFunction() {
            function descriptionLabel(opt) {
                return `${opt.description}`;
            }

            function codeDescriptionLabel(opt) {
                if (opt.code === opt.description) return opt.code;
                return `${opt.code} — ${opt.description}`;
            }

            /* If hiding codes then use the description only */
            if (this.hideCodes) return descriptionLabel;

            /* In all other cases use label function provided or code + description by default */
            return codeDescriptionLabel;
        },
        selectedValue() {
            return this.opts.filter(
                c => this.value && this.value.find(val => val.improvement.code === c.code),
            );
        },
    },
    methods: {
        keyed(event) {
            // TODO should be able to remove but wanted to intercept keypress from multi-select.
        },
        startEdit(option) {
            option.edit = true;
            this.refresh = !this.refresh;
            var self = this;
            this.$nextTick(() => {
                self.$refs.quantityInput.focus();
            });
        },
        endEdit(option) {
            option.edit = false;
            this.refresh = !this.refresh;
            this.input(this.$refs.multiSelect.getValue());
        },
        input(values) {
            // Massage values array from multiselect into a list of ImprovementQuantity
            const result = [];

            Object.values(values).forEach((classification) => {
                const improvementQuantity = {
                    improvement: classification,
                    quantity: classification.quantity,
                }
                result.push(improvementQuantity);
            });

            this.$emit('input', {
                value: result,
                id: this.id,
            });
        }
    }
};
</script>
<style scoped>
    input.quantity {
        width: auto;
        color: black;
        height: 2rem;
    }
</style>

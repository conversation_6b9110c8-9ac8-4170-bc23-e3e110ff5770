<template>
    <div class="section-wrapper">
        <h2
            v-if="showSectionHeader"
            class="section-title"
        >
            Site Improvements
        </h2>
        <div class="property-draft-section">
            <!-- Site Development -->
            <div class="property-draft-section-row">
                <div class="col-row">
                    <div data-cy="site-other-improvement" class="col col-2">
                        <label>
                            <span class="label">Other Improvement</span>
                            <input
                                type="text"
                                readonly
                                value="Site Development"
                            >
                        </label>
                    </div>
                    <div data-cy="site-quality" class="col col-2">
                        <label>
                            <span class="label">Quality</span>
                            <classification-dropdown
                                id="siteDevelopmentQuality"
                                category="FeatureQuality"
                                :value="siteDevelopment.quality"
                                :filter-options-function="
                                    (options) => options.filter(option => option.code !== 'M')
                                "
                                hide-codes
                                @input="({id, value}) => updateSiteDevelopment('quality', value)"
                            />
                        </label>
                    </div>
                    <div data-cy="site-development-description" class="col col-8">
                        <label>
                            <span class="label">Description</span>
                            <input
                                id="siteDevelopmentDescription"
                                type="text"
                                :value="siteDevelopment.description"
                                @input="updateSiteDevelopment(
                                    'description',
                                    $event.srcElement.value
                                )"
                            >
                        </label>
                    </div>
                </div>
            </div>
            <div
                v-for="(improvement, improvementIndex) in otherImprovements"
                :key="`otherImprovements-${improvementIndex}`"
                class="property-draft-section-row"
            >
                <div class="col-row">
                    <div data-cy="site-improvement-other-improvement" class="col col-2">
                        <label>
                            <span class="label">Other Improvement</span>
                            <classification-dropdown
                                id="definition"
                                :value="improvement.definition"
                                :category="otherImprovementCategory"
                                :class="errorClasses(
                                    `otherImprovements[${improvementIndex}].definition`
                                )"
                                hide-codes
                                @input="({id, value}) => updateImprovementDefinition(improvementIndex, value)"
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`otherImprovements[${improvementIndex}].definition`"
                            />
                        </label>
                    </div>
                    <div data-cy="site-imporvement-year-built" class="col col-1">
                        <label>
                            <span class="label">Year Built</span>
                            <classification-dropdown
                                id="age"
                                category="FeatureAge"
                                :value="improvement.age"
                                :class="errorClasses(`otherImprovements[${improvementIndex}].age`)"
                                hide-codes
                                @input="({id, value}) => updateOtherImprovements(improvementIndex, id, value)"
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`otherImprovements[${improvementIndex}].age`"
                            />
                        </label>
                    </div>
                    <div data-cy="site-improvement-area-quantity" class="col col-1">
                        <label>
                            <span class="label">Area/Quantity</span>
                            <input
                                :id="`improvements-${improvementIndex}-quantity`"
                                type="number"
                                min="0"
                                step="1"
                                :value="improvement.quantity"
                                :class="errorClasses(
                                    `otherImprovements[${improvementIndex}].quantity`
                                )"
                                @input="($event) => updateOtherImprovements(improvementIndex, 'quantity', parseFloat($event.srcElement.value))"
                            >
                            <validation-message
                                :validation-set="validationSet"
                                :field="`otherImprovements[${improvementIndex}].quantity`"
                            />
                        </label>
                    </div>
                    <div data-cy="site-improvement-unit-of-measure" class="col col-2">
                        <label>
                            <span class="label">Unit of Measure</span>
                            <classification-dropdown
                                id="unitOfMeasure"
                                category="UnitOfMeasure"
                                :value="improvement.unitOfMeasure"
                                :allow-empty="!isUnitOfMeasureLocked(improvement)"
                                :disabled="(isUnitOfMeasureLocked(improvement) && improvement.unitOfMeasure && lockedImprovementUnitOfMeasure[improvement.definition.code] === improvement.unitOfMeasure.code)"
                                :class="errorClasses(
                                    `otherImprovements[${improvementIndex}].unitOfMeasure`
                                )"
                                hide-codes
                                :filter-options-function="(options) => otherImprovementsFilter(options, improvement)"
                                @input="({id, value}) => updateOtherImprovements(improvementIndex, id, value)"
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`otherImprovements[${improvementIndex}].unitOfMeasure`"
                            />
                        </label>
                    </div>
                    <div data-cy="site-improvement-quality" class="col col-1">
                        <label>
                            <span class="label">Quality</span>
                            <classification-dropdown
                                id="quality"
                                category="FeatureQuality"
                                :value="improvement.quality"
                                :filter-options-function="(options) => options.filter(
                                    option => option.code !== 'M'
                                )"
                                :class="errorClasses(
                                    `otherImprovements[${improvementIndex}].quality`
                                )"
                                hide-codes
                                @input="({id, value}) => updateOtherImprovements(improvementIndex, id, value)"
                            />
                            <validation-message
                                :validation-set="validationSet"
                                :field="`otherImprovements[${improvementIndex}].quality`"
                            />
                        </label>
                    </div>
                    <div data-cy="site-improvement-description" class="col col-4">
                        <label>
                            <span class="label">Description</span>
                            <input
                                :id="`improvements-${improvementIndex}-description`"
                                type="text"
                                :value="improvement.description"
                                :class="errorClasses(
                                    `otherImprovements[${improvementIndex}].description`
                                )"
                                @input="($event) => updateOtherImprovements(improvementIndex, 'description', $event.srcElement.value)"
                            >
                            <validation-message
                                :validation-set="validationSet"
                                :field="`otherImprovements[${improvementIndex}].description`"
                            />
                        </label>
                    </div>
                    <div data-cy="remove-improvement-row" class="col col-1">
                        <div class="righty">
                            <button
                                class="mdl-button mdl-js-button
                                    mdl-button--raised mdl-js-ripple-effect"
                                @click="removeImprovementRow(improvementIndex)"
                            >
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="button-row">
            <div class="col-row">
                <div data-cy="add-improvement-row" class="col col-12">
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised
                            mdl-js-ripple-effect mdl-button--colored"
                        @click="addImprovementRow"
                    >
                        Add Improvement
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import commonUtils from '../../utils/CommonUtils';

export default {
    components: {
        'classification-dropdown': () => import('../common/form/ClassificationDropdown.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../common/form/ValidationMessage.vue'),
    },
    mixins: [commonUtils],
    props: {
        siteDevelopment: {
            type: Object,
            required: true,
        },
        otherImprovements: {
            type: Array,
            required: true,
        },
        validationSet: {
            type: Object,
            default: null,
        },
        showSectionHeader: {
            type: Boolean,
            default: true,
        },
        otherImprovementCategory: {
            type: String,
            default: 'OtherImprovement',
        }
    },
    data() {
        return {
            // Mapping of Other Improvement code to Unit of Measure Code
            lockedImprovementUnitOfMeasure: {
                'CA': 'CS',
                'CD': 'CS',
                'CP': 'CS',
                'DC': 'SM',
                'DU': 'SM',
                'FN': 'LM',
                'SW': 'SM',
                'WA': 'LI',
            },
        };
    },
    computed: {
        ...mapGetters(['getCategoryClassifications']),
        errors() { return (this.validationSet && this.validationSet.errors) || []; },
    },
    methods: {
        updateSiteDevelopment(key, value) {
            const siteDevelopment = { ...this.siteDevelopment };
            siteDevelopment[key] = value;
            this.$emit('update', { id: 'siteDevelopment', value: siteDevelopment });
        },
        updateImprovementDefinition(index, value) {
            // Update the definition
            const otherImprovements = [...this.otherImprovements];
            const updatedImprovement = { ...otherImprovements[index] };
            updatedImprovement.definition = value;
            // Select the appropriate unit of measure
            const unitOfMeasureClassifications = this.getCategoryClassifications('UnitOfMeasure');
            if (value && value.code) {
                // Get the unit of measure classification code, otherwise default to UN or Unit/s
                const unitOfMeasureCode = this.lockedImprovementUnitOfMeasure[value.code] || 'UN';
                if (Array.isArray(unitOfMeasureClassifications)) {
                    const defaultUnitOfMeasure = unitOfMeasureClassifications.find(element => element.code === unitOfMeasureCode);
                    updatedImprovement.unitOfMeasure = defaultUnitOfMeasure;
                }
            } else {
                // If other improvement definition is unselected, then set the selected unit of measure to null.
                updatedImprovement.unitOfMeasure = null;
            }
            otherImprovements.splice(index, 1, updatedImprovement);
            this.$emit('update', { id: 'otherImprovements', value: otherImprovements });
        },
        updateOtherImprovements(index, key, value) {
            const otherImprovements = [...this.otherImprovements];
            const updatedImprovement = { ...otherImprovements[index] };
            updatedImprovement[key] = value;
            // Replace the old other improvement with the updated one.
            otherImprovements.splice(index, 1, updatedImprovement);
            this.$emit('update', { id: 'otherImprovements', value: otherImprovements });
        },
        addImprovementRow() {
            const otherImprovements = [...this.otherImprovements];
            // insert empty
            otherImprovements.splice(otherImprovements.length, 0, {});
            this.$emit('update', { id: 'otherImprovements', value: otherImprovements });
        },
        removeImprovementRow(improvementIndex) {
            const otherImprovements = [...this.otherImprovements];
            /* eslint-disable-next-line no-alert, no-restricted-globals */
            if (!confirm('This will remove this Improvement. Are you sure?')) {
                return;
            }
            otherImprovements.splice(improvementIndex, 1);
            this.$emit('update', { id: 'otherImprovements', value: otherImprovements });
        },
        isUnitOfMeasureLocked(otherImprovement) {
            // Determine whether Unit of Measure for an other improvement is locked
            return otherImprovement
                && otherImprovement.definition
                && !!this.lockedImprovementUnitOfMeasure[otherImprovement.definition.code];
        },
        otherImprovementsFilter(options, otherImprovement) {
            // If unit of measure is locked for an other improvement, then filter the options to the locked unit of measure
            // Otherwise, don't filter the options.
            return (
                this.isUnitOfMeasureLocked(otherImprovement)
                && options.filter(option => option.code === this.lockedImprovementUnitOfMeasure[otherImprovement.definition.code])
            ) || options;
        },
    },
};
</script>

<!--
TODO Needs to be refactored - this is a temporary
collection of styles for a light touch "kind of like Monarch" view
-->
<style lang="scss" scoped="true" src="../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped="true">

    /* TODO Standardise - this is a "small button" for inline operations */
    button {
        height: 28px;
        line-height: 28px;
        font-size: 0.9em;
    }

    /* TODO Quick n dirty */
    h2.section-title {
        background-color:  #283c64;
        color: #fff;
        padding: 0.5rem;
        padding-left: 1rem;
    }

    .property-draft-section-row {
        display: table;
        width: 100%;
        border-bottom: .1rem solid #214d90;
        padding-bottom: 5px;
        margin-bottom: 3px;
    }

    .button-row {
        display: table;
        width: 100%;
    }

</style>

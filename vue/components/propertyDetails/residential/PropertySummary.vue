<script setup>
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import YesNoIndeterminateDropdown from '@/components/common/form/YesNoIndeterminateDropdown.vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { NumeralInput } from '@/components/ui/input';
import { PropertyDetail } from '@quotable-value/validation';

const { FIELDS } = PropertyDetail;

const props = defineProps({
    propertyDetail: {
        type: Object,
        required: true,
    },
});
const emit = defineEmits(['update:property-detail', 'changed']);

function onUpdatePropertyDetail(value) {
    emit('update:property-detail', value);
}

function onInputChange(id, value) {
    onUpdatePropertyDetail({ id, value });
};

</script>

<template>
    <div class="property-draft-section">
        <div class="col-row">
            <div class="col col-3" data-cy="valuation-property-details-house-type">
                <label>
                    <span class="label">House Type</span>
                    <ValidationWrapper :path="FIELDS.HOUSE_TYPE">
                        <ClassificationDropdown
                            id="houseType"
                            :value="propertyDetail.summary.houseType"
                            category="HouseType_DVR"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-units-of-use">
                <label>
                    <span class="label">Units of Use</span>
                    <ValidationWrapper :path="FIELDS.UNITS_OF_USE">
                        <NumeralInput
                            v-model="propertyDetail.summary.units"
                            min="0"
                            step="1"
                            @input="onInputChange('effectiveLandArea', $event)"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-age">
                <label>
                    <span class="label" title="This is the building age from the land use data">Age</span>
                    <ValidationWrapper :path="FIELDS.AGE">
                        <ClassificationDropdown
                            id="age"
                            :label-function="(opt) => opt.description"
                            :value="propertyDetail.summary.age"
                            category="Age_DVR"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-effective-year-built">
                <label>
                    <span class="label">Effective Year Built</span>
                    <ValidationWrapper :path="FIELDS.EFFECTIVE_YEAR_BUILT">
                        <NumeralInput
                            v-model="propertyDetail.summary.effectiveYearBuilt"
                            min="0"
                            step="1"
                            @input="onInputChange('effectiveYearBuilt', $event)"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-poor-foundations">
                <label>
                        <span class="label" title="Has Poor Foundations">
                            Poor Fdn.
                        </span>
                    <yes-no-indeterminate-dropdown
                        id="hasPoorFoundations"
                        :value="propertyDetail.summary.hasPoorFoundations"
                        @input="onUpdatePropertyDetail"
                    />
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-total-bedrms">
                <label>
                        <span class="label" title="Total Bedrooms">
                            Total Bedrms
                        </span>
                    <NumeralInput
                        v-model="propertyDetail.summary.totalBedrooms"
                        min="0"
                        step="1"
                        @input="onInputChange('totalBedrooms', $event)"
                    />
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-total-bathrms">
                <label>
                        <span class="label" title="Total Bathrooms">
                            Total Bathrms
                        </span>
                    <NumeralInput
                        v-model="propertyDetail.summary.totalBathrooms"
                        min="0"
                        step="1"
                        @input="onInputChange('totalBathrooms', $event)"
                    />
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-total-toilets">
                <label>
                        <span class="label" title="Total Toilets">
                            Total Toilets
                        </span>
                    <NumeralInput
                        v-model="propertyDetail.summary.totalToilets"
                        min="0"
                        step="1"
                        @input="onInputChange('totalToilets', $event)"
                    />
                </label>
            </div>
        </div>
        <div class="col-row">
            <div class="col col-2" data-cy="valuation-property-details-building-site-cover">
                <label>
                    <span class="label">Building Site Cover, m<sup>2</sup></span>
                    <ValidationWrapper :path="FIELDS.BUILDING_SITE_COVER">
                        <NumeralInput
                            v-model="propertyDetail.summary.buildingSiteCover"
                            min="0"
                            step="1"
                            @input="onInputChange('buildingSiteCover', $event)"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-total-floor-area">
                <label>
                    <span class="label">Total Floor Area, m<sup>2</sup></span>
                    <ValidationWrapper :path="FIELDS.TOTAL_FLOOR_AREA">
                        <NumeralInput
                            v-model="propertyDetail.summary.totalFloorArea"
                            min="0"
                            step="1"
                            @input="onInputChange('totalFloorArea', $event)"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-main-living-area">
                <label>
                    <span class="label">Main Living Area, m<sup>2</sup></span>
                    <ValidationWrapper :path="FIELDS.MAIN_LIVING_AREA">
                        <NumeralInput
                            v-model="propertyDetail.summary.mainLivingArea"
                            min="0"
                            step="1"
                            @input="onInputChange('mainLivingArea', $event)"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-total-living-area">
                <label>
                    <span class="label">Total Living Area, m<sup>2</sup></span>
                    <ValidationWrapper :path="FIELDS.TOTAL_LIVING_AREA">
                        <NumeralInput
                            v-model="propertyDetail.summary.totalLivingArea"
                            min="0"
                            step="1"
                            @input="onInputChange('totalLivingArea', $event)"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-laundry-workshop">
                <label>
                        <span class="label" title="Has a Laundry or Workshop outside of living area or garage">
                            Ldy/Wkshp
                        </span>
                    <ValidationWrapper :path="FIELDS.LAUNDRY_OR_WORKSHOP">
                        <yes-no-indeterminate-dropdown
                            id="hasLaundryOrWorkshop"
                            :value="propertyDetail.summary.hasLaundryOrWorkshop"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-car-access">
                <label>
                    <span class="label">Car Access</span>
                    <span class="field">
                            <ValidationWrapper :path="FIELDS.CAR_ACCESS">
                                <yes-no-indeterminate-dropdown
                                    id="hasCarAccess"
                                    :value="propertyDetail.site.hasCarAccess"
                                    @input="onUpdatePropertyDetail"
                                />
                            </ValidationWrapper>
                        </span>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-driveway">
                <label>
                    <span class="label">Driveway</span>
                    <span class="field">
                        <yes-no-indeterminate-dropdown
                            id="hasDriveway"
                            :value="propertyDetail.site.hasDriveway"
                            @input="onUpdatePropertyDetail"
                        />
                    </span>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-carparks">
                <label>
                    <span class="label">Carparks</span>
                    <ValidationWrapper :path="FIELDS.CAR_PARKS">
                        <NumeralInput
                            v-model="propertyDetail.site.carparks"
                            min="0"
                            step="1"
                            @input="onInputChange('carparks', $event)"
                        />
                    </ValidationWrapper>
                </label>
            </div>
        </div>
    </div>
</template>

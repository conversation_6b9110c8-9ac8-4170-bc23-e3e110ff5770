<script setup>
import RatingApportionmentsItem from '@/components/propertyDetails/residential/RatingApportionmentsItem.vue';
import { ValidationContext } from '@/components/ui/validation';

const props = defineProps({
    apportionments: {
        required: true,
        type: Array,
    },
    validationSet: {
        type: Object,
        required: false,
    }
});
const emit = defineEmits(['update:apportionments']);

function updateApportionments(index, value) {
    const updatedApportionments = [...props.apportionments];
    updatedApportionments[index] = value;
    emit('update:apportionments', updatedApportionments);
}
</script>

<template>
    <ValidationContext :validation-set="validationSet">
        <div class="qv-w-full" data-cy="rating-apportionments">
            <div v-for="(apportionment, index) in apportionments" :key="apportionment.qpid">
                <RatingApportionmentsItem v-model="apportionments[index]" @input="updateApportionments(index, $event)" :index="index"/>
            </div>
        </div>
    </ValidationContext>
</template>

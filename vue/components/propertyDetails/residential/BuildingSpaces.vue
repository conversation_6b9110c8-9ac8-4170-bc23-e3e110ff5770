<script setup>
import { ref, computed, nextTick, set } from 'vue';
import VueMultiselect from 'vue-multiselect';
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { NumeralInput, TextInput } from '@/components/ui/input';
import { PropertyDetail } from '@quotable-value/validation';

const { FIELDS } = PropertyDetail;

const emit = defineEmits(['update:buildings']);

const props = defineProps({
    buildings: {
        type: Array,
        required: true,
    }
});

const spaceToAdd = ref({ type: null, buildingIndex: null });

const buildingLabels = computed(() =>
    props.buildings.map((building, buildingIndex) => {
        const buildingType = building.buildingType?.description;
        const label = building.buildingLabel;
        return { buildingLabel: [buildingType, label].filter(Boolean).join(' '), buildingIndex };
    })
);

function addSpaceBuildingChanged(value) {
    spaceToAdd.value.buildingIndex = value.buildingIndex;
}

function updateBuilding(buildingIndex, spaceIndex, path, value) {
    const updatedBuildings = [...props.buildings];
    if(!updatedBuildings[buildingIndex].spaces[spaceIndex]) {
        updatedBuildings[buildingIndex].spaces[spaceIndex] = {};
    }
    set(updatedBuildings[buildingIndex].spaces[spaceIndex], path, value);
    emit('update:buildings', updatedBuildings);
}

function updateBuildingForProperty(buildingIndex, spaceIndex, path, property, value) {
    const updatedBuildings = [...props.buildings];
    if(!updatedBuildings[buildingIndex].spaces[spaceIndex][path]) {
        updatedBuildings[buildingIndex].spaces[spaceIndex][path] = {};
    }
    set(updatedBuildings[buildingIndex].spaces[spaceIndex][path], property, value);
    emit('update:buildings', updatedBuildings);
}


function updateSpaceBuildingMapping(buildingIndexFrom, buildingIndexTo, spaceIndex) {
    const updatedBuildings = [...props.buildings];
    const [space] = updatedBuildings[buildingIndexFrom].spaces.splice(spaceIndex, 1);
    updatedBuildings[buildingIndexTo].spaces = [...(updatedBuildings[buildingIndexTo].spaces || []), space];
   emit('update:buildings', updatedBuildings);
}

function addBuildingRow(newBuilding = {}) {
    const updatedBuildings = [...props.buildings, newBuilding];
   emit('update:buildings', updatedBuildings);
}

function duplicateSpace(buildingIndex, spaceIndex) {
    const updatedBuildings = [...props.buildings];
    const space = {...updatedBuildings[buildingIndex].spaces[spaceIndex]};
    updatedBuildings[buildingIndex].spaces.splice(spaceIndex, 0, space);
   emit('update:buildings', updatedBuildings);
}

function addSpaceRow(buildingIndex, spaceType) {
    if (buildingIndex == null || !spaceType) return;

    const updatedBuildings = [...props.buildings];
    const spaces = updatedBuildings[buildingIndex].spaces || [];
    spaces.push({ spaceType, numberOfSimilarSpaces: 1 });
    updatedBuildings[buildingIndex].spaces = spaces;
   emit('update:buildings', updatedBuildings);
}

function removeSpaceRow(buildingIndex, spaceIndex) {
    if (!confirm('This will remove this Space. Are you sure?')) return;

    const updatedBuildings = [...props.buildings];
    updatedBuildings[buildingIndex].spaces.splice(spaceIndex, 1);
   emit('update:buildings', updatedBuildings);
}
</script>

<template>
    <div class="property-draft-section">
        <div v-for="(building, buildingIndex) in buildings" :key="buildingIndex">
            <div v-for="(space, spaceIndex) in building.spaces"
                :key="`buildings-${buildingIndex}-spaces-${spaceIndex}`">
                <div v-if="space.spaceType && space.spaceType.code === 'LI'" class="property-draft-section-row">
                    <div class="col-row">
                        <div class="col col-2" data-cy="with-building">
                            <label>
                                <span class="label">Within Building</span>
                                <vue-multiselect
                                    :value="buildingLabels.find((buildingLabel) => buildingLabel.buildingIndex === buildingIndex)"
                                    :options="buildingLabels" label="buildingLabel" select-label="⏎ select"
                                    track-by="buildingIndex" deselect-label=""
                                    @select="(value) => updateSpaceBuildingMapping(buildingIndex, value.buildingIndex, spaceIndex)" />
                            </label>
                        </div>
                        <div class="col col-1" data-cy="space-type">
                            <label>
                                <span class="label">Space Type</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_TYPE" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown :value="space.spaceType" category="SpaceType"
                                        hide-codes
                                        @input="({ id, value }) => updateBuilding(buildingIndex, spaceIndex, 'spaceType', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="multiplicity" class="col col-1">
                            <label>
                                <span class="label"
                                    title="Number of Identical Living Spaces - used for a large multi-unit property that consists of similar units, such as a retirement home.">
                                    Multiplicity </span>
                                <NumeralInput
                                    min="0"
                                    step="1"
                                    v-model="space.numberOfSimilarSpaces"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'floorArea', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="floor-area" class="col col-1">
                            <label>
                                <span class="label">Floor Area</span>
                                <NumeralInput
                                    preset="AREA"
                                    min="0"
                                    step="1"
                                    v-model="space.floorArea"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'floorArea', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="bedrooms" class="col col-1">
                            <label>
                                <span class="label" title="Total Bedrooms in this Living Space."> Bedrooms </span>
                                <NumeralInput
                                    min="0"
                                    step="1"
                                    v-model="space.totalBedrooms"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'totalBedrooms', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="double-bedrms" class="col col-1">
                            <label>
                                <span class="label" title="Double Bedrooms in this Living Space."> Double Bedrms
                                </span>
                                <NumeralInput
                                    min="0"
                                    step="1"
                                    v-model="space.doubleBedrooms"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'doubleBedrooms', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="single-bedrms" class="col col-1">
                            <label>
                                <span class="label" title="Single Bedrooms in this Living Space."> Single Bedrms
                                </span>
                                <NumeralInput
                                    min="0"
                                    step="1"
                                    v-model="space.singleBedrooms"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'singleBedrooms', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="office-study" class="col col-1">
                            <label>
                                <span class="label" title="Offices and Studies in this Living Spaces."> Office/Study
                                </span>
                                <NumeralInput
                                    min="0"
                                    step="1"
                                    v-model="space.homeOfficeOrStudy"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'homeOfficeOrStudy', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="bathrooms" class="col col-1">
                            <label>
                                <span class="label" title="Bathrooms in this Living Space."> Bathrooms </span>
                                <NumeralInput
                                    min="0"
                                    step="1"
                                    v-model="space.totalBathrooms"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'totalBathrooms', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="toilets" class="col col-1">
                            <label>
                                <span class="label" title="Toilets in this Living Space."> Toilets </span>
                                <NumeralInput
                                    min="0"
                                    step="1"
                                    v-model="space.totalToilets"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'totalToilets', $event)"
                                />
                            </label>
                        </div>
                        <div class="col col-1 row-controls">
                            <div data-cy="remove-dwelling-space-row">
                                <button
                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button--small"
                                    title="Remove a Space"
                                    @click="removeSpaceRow(buildingIndex, spaceIndex)">Remove</button>
                            </div>
                            <div data-cy="duplicate-dwelling-space-row">
                                <button
                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button--small"
                                    title="Duplicate this space"
                                    @click="duplicateSpace(buildingIndex, spaceIndex)">Copy</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-row">
                        <div data-cy="space-address" class="col col-2">
                            <label>
                                <span class="label">Space Address / Id</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_LABEL" :index="`${buildingIndex}-${spaceIndex}`">
                                    <TextInput
                                        v-model="space.spaceLabel"
                                        @input="updateBuilding(buildingIndex, spaceIndex, 'spaceLabel', $event)"
                                    />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="modernisation" class="col col-1">
                            <label>
                                <span class="label">Modernisation Age</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_MODERNISATION_AGE" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown :value="space.modernisationAge" category="ModernisationAge"
                                    :single-label-function="(opt) => opt.description"
                                    :label-function="(opt) => opt.description"
                                    @input="({ id, value }) => updateBuilding(buildingIndex, spaceIndex, 'modernisationAge', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="interior-condition" class="col col-1">
                            <label>
                                <span class="label" title="Interior Condition"> Interior Cond. </span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_QUALITY" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown :value="space.quality" category="FeatureQuality"
                                    :filter-options-function="(options) => options.filter((option) => option.code !== 'M')"
                                    hide-codes
                                    @input="({ id, value }) => updateBuilding(buildingIndex, spaceIndex, 'quality', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="kitchen-age" class="col col-1">
                            <label>
                                <span class="label">Kitchen Age</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_KITCHEN_AGE" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown :value="space.kitchen ? space.kitchen.age : null"
                                    category="FeatureAge"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'kitchen' ,'age', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="kitchen-quality" class="col col-1">
                            <label>
                                <span class="label">Kitchen Quality</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_KITCHEN_QUALITY" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown :value="space.kitchen ? space.kitchen.quality : null"
                                    category="FeatureQuality"
                                    :filter-options-function="(options) => options.filter((option) => option.code !== 'M')"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'kitchen' ,'quality', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="main-bathroom-age" class="col col-1">
                            <label>
                                <span class="label" title="Main Bathroom Age"> Main Bath Age </span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_MAIN_BATHROOM_AGE" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown :value="space.mainBathroom ? space.mainBathroom.age : null"
                                    category="FeatureAge"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'mainBathroom', 'age', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="main-bathroom-quality" class="col col-1">
                            <label>
                                <span class="label" title="Main Bathroom Quality"> Main Bath Qty </span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_MAIN_BATHROOM_QUALITY" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown
                                    :value="space.mainBathroom ? space.mainBathroom.quality : null"
                                    category="FeatureQuality"
                                    :filter-options-function="(options) => options.filter((option) => option.code !== 'M')"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'mainBathroom', 'quality', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="ensuite-age" class="col col-1">
                            <label>
                                <span class="label">Ensuite Age</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_ENSUITE_AGE" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="age" :value="space.ensuite ? space.ensuite.age : null"
                                    category="FeatureAge"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'ensuite', 'age', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="ensuite-quality" class="col col-1">
                            <label>
                                <span class="label">Ensuite Quality</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_ENSUITE_QUALITY" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="quality"
                                    :value="space.ensuite ? space.ensuite.quality : null" category="FeatureQuality"
                                    :filter-options-function="(options) => options.filter((option) => option.code !== 'M')"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'ensuite' ,'quality', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="heating-type" class="col col-2">
                            <label>
                                <span class="label">Heating Type</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_HEATING" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="definition"
                                    :value="space.heating ? space.heating.definition : null" category="HeatingType"
                                    multiple :limit="3"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'heating' ,'definition', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                    </div>
                </div>
                <!-- Garage Space -->
                <div v-else-if="space.spaceType && space.spaceType.code === 'GA'"
                    class="property-draft-section-row">
                    <div class="col-row">
                        <div data-cy="within-building5" class="col col-2">
                            <label>
                                <span class="label">Within Building</span>
                                <vue-multiselect
                                    :value="buildingLabels.find((buildingLabel) => buildingLabel.buildingIndex === buildingIndex)"
                                    :options="buildingLabels" label="buildingLabel" select-label="⏎ select"
                                    track-by="buildingIndex" deselect-label=""
                                    @select="(value) => updateSpaceBuildingMapping(buildingIndex, value.buildingIndex, spaceIndex)" />
                            </label>
                        </div>
                        <div data-cy="space-type" class="col col-1">
                            <label>
                                <span class="label">Space Type</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_TYPE" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="spaceType" :value="space.spaceType"
                                    category="SpaceType"
                                    hide-codes
                                    @input="({ id, value }) => updateBuilding(buildingIndex, spaceIndex, 'spaceType', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="spaces-car-parks" class="col col-1">
                            <label>
                                <span class="label" title="Carparks in this Garage."> Carparks </span>
                                <ValidationWrapper :path="FIELDS.CAR_PARKS" :index="`${buildingIndex}-${spaceIndex}`">
                                    <NumeralInput
                                        min="0"
                                        step="1"
                                        v-model="space.numberOfCarparks"
                                        @input="updateBuilding(buildingIndex, spaceIndex, 'numberOfCarparks', $event)"
                                    />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="floor-area" class="col col-1">
                            <label>
                                <span class="label">Floor Area</span>
                                <NumeralInput
                                    preset="AREA"
                                    min="0"
                                    step="1"
                                    v-model="space.floorArea"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'floorArea', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="quality" class="col col-1">
                            <label>
                                <span class="label">Quality</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_QUALITY" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="quality" :value="space.quality"
                                    category="FeatureQuality"
                                    :filter-options-function="(options) => options.filter((option) => option.code !== 'M')"
                                    hide-codes
                                    @input="({ id, value }) => updateBuilding(buildingIndex, spaceIndex, 'quality', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="garage-features" class="col col-5">
                            <label>
                                <span class="label">Garage Features</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_GARAGE_FEATURES" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="garageFeatures"
                                    :value="space.garageFeatures ? space.garageFeatures.definition : null"
                                    category="GarageFeature" multiple :limit="4"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'garageFeatures' , 'definition', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-1 row-controls">
                            <div data-cy="remove-garage-space-row">
                                <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                    title="Remove a Space"
                                    @click="removeSpaceRow(buildingIndex, spaceIndex)">Remove</button>
                            </div>
                            <div data-cy="duplicate-garage-space-row">
                                <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                    title="Duplicate this space"
                                    @click="duplicateSpace(buildingIndex, spaceIndex)">Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Any Other Space -->
                <div v-else class="property-draft-section-row">
                    <div class="col-row">
                        <div data-cy="space-within-building" class="col col-2">
                            <label>
                                <span class="label">Within Building</span>
                                <vue-multiselect
                                    :value="buildingLabels.find((buildingLabel) => buildingLabel.buildingIndex === buildingIndex)"
                                    :options="buildingLabels" label="buildingLabel" select-label="⏎ select"
                                    track-by="buildingIndex" deselect-label=""
                                    @select="(value) => updateSpaceBuildingMapping(buildingIndex, value.buildingIndex, spaceIndex)" />
                            </label>
                        </div>
                        <div data-cy="space-space-type" class="col col-2">
                            <label>
                                <span class="label">Space Type</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_TYPE" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="spaceType" :value="space.spaceType"
                                    category="SpaceType"
                                    hide-codes
                                    @input="({ id, value }) => updateBuilding(buildingIndex, spaceIndex, 'spaceType', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="space-floor-area" class="col col-1">
                            <label>
                                <span class="label">Floor Area</span>
                                <NumeralInput
                                    preset="AREA"
                                    min="0"
                                    step="1"
                                    v-model="space.floorArea"
                                    @input="updateBuilding(buildingIndex, spaceIndex, 'floorArea', $event)"
                                />
                            </label>
                        </div>
                        <div data-cy="space-quality" class="col col-1">
                            <label>
                                <span class="label">Quality</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_QUALITY" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="quality" :value="space.quality"
                                    category="FeatureQuality"
                                    :filter-options-function="(options) => options.filter((option) => option.code !== 'M')"
                                    hide-codes
                                    @input="({ id, value }) => updateBuilding(buildingIndex, spaceIndex, 'quality', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="space-other-feature" class="col col-5">
                            <label>
                                <span class="label">Other Features</span>
                                <ValidationWrapper :path="FIELDS.BUILDING_SPACE_OTHER_FEATURES" :index="`${buildingIndex}-${spaceIndex}`">
                                    <ClassificationDropdown id="spaceFeatures"
                                    :value="space.spaceFeatures ? space.spaceFeatures.definition : null"
                                    category="SpaceFeature" multiple :limit="4"
                                    hide-codes
                                    @input="({ id, value }) => updateBuildingForProperty(buildingIndex, spaceIndex, 'spaceFeatures', 'definition', value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-1 row-controls">
                            <div data-cy="remove-anyother-space-row">
                                <button
                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button--small"
                                    title="Remove a Space"
                                    @click="removeSpaceRow(buildingIndex, spaceIndex)">Remove</button>
                            </div>
                            <div data-cy="duplicate-anyother-space-row">
                                <button
                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button--small"
                                    title="Duplicate this space"
                                    @click="duplicateSpace(buildingIndex, spaceIndex)">Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Add a space -->
        <div class="button-row">
            <div class="col-row">
                <div class="col col-2" data-cy="building-space-label">
                    <vue-multiselect :value="buildingLabels[spaceToAdd.buildingIndex]"
                        :options="buildingLabels" placeholder="Building" label="buildingLabel"
                        select-label="⏎ select" track-by="buildingIndex" deselect-label=""
                        @select="addSpaceBuildingChanged" />
                </div>
                <div class="col col-2" data-cy="building-space-type">
                    <ClassificationDropdown id="spaceType" :value="spaceToAdd.type" category="SpaceType"
                        placeholder="Type of Space" hide-codes
                        @input="({ id, value }) => (spaceToAdd.type = value)" />
                </div>
                <div data-cy="add-space" class="col col-2">
                    <button
                        data-cy="add-space-btn"
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small"
                        title="Add a Space to a Building"
                        @click="addSpaceRow(spaceToAdd.buildingIndex, spaceToAdd.type)">Add Space</button>
                </div>
            </div>
        </div>
    </div>
</template>

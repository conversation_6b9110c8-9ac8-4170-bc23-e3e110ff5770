<script setup>
import { computed } from 'vue';
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import NatureOfImprovements from '@/components/propertyDetails/NatureOfImprovements.vue';
import { ValidationWrapper } from '@/components/ui/validation';
import { NumeralInput, TextInput } from '@/components/ui/input';
import { PropertyDetail } from '@quotable-value/validation';

const { FIELDS, DISPLAY_NAMES } = PropertyDetail;

const props = defineProps({
    propertyDetail: {
        type: Object,
        required: true,
    },
    taCode: {
        type: String | Number,
        required: false,
    },
});

const emit = defineEmits(['update:property-detail']);

function onUpdatePropertyDetail(value) {
    emit('update:property-detail', value);
}

const isMaoriLand = computed(() => props.propertyDetail.landUse.isMaoriLand ? 'Yes' : 'No');

</script>

<template>
    <div class="property-draft-section">
        <div class="col-row">
            <div class="col col-4" data-cy="valuation-property-details-category">
                <label>
                    <span class="label">{{ DISPLAY_NAMES.CATEGORY }}</span>
                    <ValidationWrapper :path="FIELDS.CATEGORY">
                        <ClassificationDropdown
                            id="category"
                            :single-label-function="(opt) => `${opt.code} — ${opt.description}`"
                            :value="propertyDetail.category"
                            category="Category_DVR"
                            label="code"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-5" data-cy="valuation-property-details-nature-of-improvements">
                <label>
                        <span class="label"
                              title="To add multiple of an improvement type double click on a selection and enter the number.">
                            {{ DISPLAY_NAMES.NATURE_OF_IMPROVEMENTS }}
                        </span>
                    <ValidationWrapper :path="FIELDS.NATURE_OF_IMPROVEMENTS">
                        <NatureOfImprovements
                            id="natureOfImprovements"
                            :value="propertyDetail.natureOfImprovements"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-3" data-cy="valuation-property-details-property-name">
                <label>
                    <span class="label">{{ DISPLAY_NAMES.PROPERTY_NAME }}</span>
                    <ValidationWrapper :path="FIELDS.PROPERTY_NAME">
                        <TextInput
                            v-model="propertyDetail.propertyName"
                            @input="onUpdatePropertyDetail({id:'propertyName', value: $event})"
                        />
                    </ValidationWrapper>
                </label>
            </div>
        </div>
        <div class="col-row">
            <div class="col col-4" data-cy="valuation-property-details-land-use">
                <label>
                    <span class="label">{{ DISPLAY_NAMES.LAND_USE }}</span>
                    <ValidationWrapper :path="FIELDS.LAND_USE">
                        <ClassificationDropdown
                            id="landUse"
                            :single-label-function="(opt) => `${opt.code} — ${opt.description}`"
                            :value="propertyDetail.landUse.landUse"
                            category="LandUse_DVR"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-ta-land-zone">
                <label>
                    <span class="label">{{ DISPLAY_NAMES.TA_LAND_ZONE }}</span>
                    <ValidationWrapper :path="FIELDS.TA_LAND_ZONE">
                        <ClassificationDropdown
                            id="landZone"
                            :category="`TA_${taCode}_LandZone_DVR`"
                            :value="propertyDetail.landUse.landZone"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-effective-land-area">
                <label>
                    <span class="label">{{ DISPLAY_NAMES.EFFECTIVE_LAND_AREA }}, ha</span>
                    <ValidationWrapper :path="FIELDS.EFFECTIVE_LAND_AREA">
                        <NumeralInput
                            v-model="propertyDetail.site.effectiveLandArea"
                            data-cy="valuation-property-details-effective-land-area-input"
                            min="0"
                            preset="AREA_HECTARES"
                            step="0.0001"
                            @input="onUpdatePropertyDetail({ id: 'effectiveLandArea', value: $event})"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-land-area">
                <label>
                    <span class="label">Land Area, ha</span>
                    <NumeralInput
                        v-model="propertyDetail.site.landArea"
                        min="0"
                        preset="AREA_HECTARES"
                        readonly
                        step="0.0001"
                    />
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-maori-land">
                <label>
                    <span class="label">{{ DISPLAY_NAMES.MAORI_LAND }}</span>
                    <ValidationWrapper :path="FIELDS.MAORI_LAND">
                        <TextInput
                            v-model="isMaoriLand"
                            readonly
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-plan-id">
                <label>
                    <span class="label">{{ DISPLAY_NAMES.PLAN_NUMBER }}</span>
                    <ValidationWrapper :path="FIELDS.PLAN_NUMBER">
                        <TextInput
                            v-model="propertyDetail.planNumber"
                            readonly
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-production">
                <label>
                    <span class="label">{{ DISPLAY_NAMES.PRODUCTION }}</span>
                    <ValidationWrapper :path="FIELDS.PRODUCTION">
                        <NumeralInput
                            v-model="propertyDetail.landUse.production"
                            min="0"
                            step="1"
                            @input="onUpdatePropertyDetail('production', $event)"
                        />
                    </ValidationWrapper>
                </label>
            </div>
        </div>
    </div>
</template>

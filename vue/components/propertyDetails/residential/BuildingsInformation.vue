<script setup>
import { ref, computed, set } from 'vue';
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import YesNoIndeterminateDropdown from '@/components/common/form/YesNoIndeterminateDropdown.vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { NumeralInput, TextInput } from '@/components/ui/input';
import { getOptions, generateBuildingLabel } from './index';
import { PropertyDetail } from '@quotable-value/validation';

const { FIELDS } = PropertyDetail;

const emit = defineEmits(['update:buildings']);

const props = defineProps({
    buildings: {
        type: Array,
        required: true,
    },
});

function updateBuilding(index, path, value) {
    const updatedBuildings = [...props.buildings];
    set(updatedBuildings[index], path, value);
    emit('update:buildings', updatedBuildings);
}

function updateBuildingForProperty(index, path, property, value) {
    const updatedBuildings = [...props.buildings];
    if (!updatedBuildings[index][path]) {
        updatedBuildings[index][path] = {};
    }
    set(updatedBuildings[index][path], property, value);
    emit('update:buildings', updatedBuildings);
}

function updateBuildingType(index, value) {
    const updatedBuildings = [...props.buildings];
    updateBuilding(index, 'buildingType', value);
    updateBuilding(index, 'buildingLabel', generateBuildingLabel(value, props.buildings));
}

function addBuildingRow(newBuilding = {}) {
    emit('update:buildings', [...props.buildings, newBuilding]);
}

function duplicateBuilding(index) {
    const { spaces, ...buildingToCopy } = props.buildings[index];
    const newBuilding = {
        ...structuredClone(buildingToCopy),
        buildingLabel: generateBuildingLabel(
            buildingToCopy.buildingType,
            props.buildings,
        ),
    };
    addBuildingRow(newBuilding);
}

function removeBuildingRow(index) {
    if (!confirm('This will remove this Building and any associated Spaces. Are you sure?')) {
        return;
    }
    const updatedBuildings = props.buildings.filter((_, i) => i !== index);
    emit('update:buildings', updatedBuildings);
}

</script>

<template>
    <div class="property-draft-section">
        <div v-for="(building, index) in buildings" :key="`buildings-${index}`" class="property-draft-section-row">
            <div class="col-row">
                <div class="col col-2" data-cy="construction-information-type-of-building">
                    <label>
                        <span class="label">Type of Building</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_TYPE" :index="index">
                            <ClassificationDropdown
                                :single-label-function="(opt) => opt.description"
                                :value="building.buildingType"
                                category="BuildingType"
                                hide-codes
                                @input="({id, value}) => updateBuildingType(index, value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-1" data-cy="construction-information-floor-area">
                    <label>
                        <span class="label">Floor Area</span>
                        <ValidationWrapper :path="FIELDS.TOTAL_FLOOR_AREA" :index="index">
                            <NumeralInput
                                v-model="building.floorArea"
                                min="0"
                                preset="AREA"
                                step="1"
                                @input="updateBuilding(index,'floorArea', $event)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-1" data-cy="construction-information-number-of-storeys">
                    <label>
                        <span class="label" title="Number of Storeys">
                            No. of Storeys
                        </span>
                        <NumeralInput
                            v-model="building.numberOfStoreys"
                            min="0"
                            preset="AREA"
                            step="1"
                            @input="updateBuilding(index,'numberOfStoreys', $event)"
                        />
                    </label>
                </div>
                <div class="col col-1" data-cy="construction-information-year-built">
                    <label>
                        <span class="label">Year Built</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_YEAR_BUILT" :index="index">
                            <NumeralInput
                                v-model="building.yearBuilt"
                                min="0"
                                step="1"
                                @input="updateBuilding(index,'yearBuilt', $event)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-6" data-cy="construction-information-description">
                    <label>
                        <span class="label">Description</span>
                        <ValidationWrapper :path="FIELDS.DESCRIPTION" :index="index">
                            <TextInput
                                v-model="building.description"
                                @input="updateBuilding(index,'description', $event)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-1 row-controls">
                    <div data-cy="remove-building-row">
                        <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button--small"
                                title="Remove a Building" @click="removeBuildingRow(index)">
                            Remove
                        </button>
                    </div>
                    <div data-cy="copy-building-row">
                        <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button--small"
                                title="Duplicate this building" @click="duplicateBuilding(index)">
                            Copy
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-1" data-cy="construction-information-building-label">
                    <label>
                        <span class="label">Building Label</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_LABEL" :index="index">
                            <TextInput
                                v-model="building.buildingLabel"
                                @input="updateBuilding(index,'buildingLabel', $event)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-1" data-cy="construction-information-principal-bldg">
                    <label>
                        <span class="label">Principal Bldg</span>
                        <ValidationWrapper :path="FIELDS.PRINCIPLE_BUILDING" :index="index">
                            <YesNoIndeterminateDropdown
                                :id="`buildings-${index}-isPrimaryBuilding`"
                                v-model="building.isPrimaryBuilding"
                                @input="({id, value}) => updateBuilding(index,'isPrimaryBuilding', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-4" data-cy="construction-information-wall-construction">
                    <label>
                        <span class="label">Wall Construction</span>
                        <ValidationWrapper :path="FIELDS.WALL_CONSTRUCTION" :index="index">
                            <ClassificationDropdown
                                :limit="3"
                                :value="building.wallConstruction ? building.wallConstruction.definition : null"
                                category="WallConstruction_DVR"
                                hide-codes
                                multiple
                                @input="({id, value}) => updateBuildingForProperty(index,'wallConstruction' ,'definition', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-1" data-cy="construction-information-wall-condition">
                    <label>
                        <span class="label">Wall Condition</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_WALL_CONSTRUCTION_QUALITY" :index="index">
                            <ClassificationDropdown
                                :value="building.wallConstruction ? building.wallConstruction.quality : null"
                                category="FeatureQuality"
                                hide-codes
                                @input="({id, value}) => updateBuildingForProperty(index,'wallConstruction', 'quality', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-4" data-cy="construction-information-roof-construction">
                    <label>
                        <span class="label">Roof Construction</span>
                        <ValidationWrapper :path="FIELDS.ROOF_CONSTRUCTION" :index="index">
                            <ClassificationDropdown
                                :limit="3"
                                :value="building.roofConstruction ? building.roofConstruction.definition : null"
                                category="RoofConstruction_DVR"
                                hide-codes
                                multiple
                                @input="({id, value}) => updateBuildingForProperty(index,'roofConstruction' , 'definition', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-1" data-cy="construction-information-roof-condition">
                    <label>
                        <span class="label">Roof Condition</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_ROOF_CONSTRUCTION_QUALITY" :index="index">
                            <ClassificationDropdown
                                :value="building.roofConstruction ? building.roofConstruction.quality : null"
                                category="FeatureQuality"
                                hide-codes
                                @input="({id, value}) => updateBuildingForProperty(index,'roofConstruction', 'quality', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-4" data-cy="construction-information-floor-construction">
                    <label>
                        <span class="label">Floor Construction</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_FLOOR_CONSTRUCTION" :index="index">
                            <ClassificationDropdown
                                :limit="3"
                                :value="building.floorConstruction ? building.floorConstruction.definition : null"
                                category="FloorConstruction_DVR"
                                hide-codes
                                multiple
                                @input="({id, value}) => updateBuildingForProperty(index,'floorConstruction' ,'definition', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-4" data-cy="construction-information-foundation">
                    <label>
                        <span class="label">Foundation</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_FOUNDATION" :index="index">
                            <ClassificationDropdown
                                :limit="3"
                                :value="building.foundation ? building.foundation.definition : null"
                                category="Foundation_DVR"
                                hide-codes
                                multiple
                                @input="({id, value}) => updateBuildingForProperty(index,'foundation', 'definition', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-1" data-cy="construction-information-wiring-age">
                    <label>
                        <span class="label">Wiring Age</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_WIRING_AGE" :index="index">
                            <ClassificationDropdown
                                :value="building.wiring ? building.wiring.age : null"
                                category="FeatureAge"
                                hide-codes
                                @input="({id, value}) => updateBuildingForProperty(index,'wiring' ,'age', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-1" data-cy="construction-information-plumbing-age">
                    <label>
                        <span class="label">Plumbing Age</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_PLUMBING" :index="index">
                            <ClassificationDropdown
                                :value="building.plumbing ? building.plumbing.age : null"
                                category="FeatureAge"
                                hide-codes
                                @input="({id, value}) => updateBuildingForProperty(index,'plumbing', 'age', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-3" data-cy="construction-information-insulation">
                    <label>
                        <span class="label">Insulation</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_INSULATION" :index="index">
                            <ClassificationDropdown
                                :limit="3"
                                :value="building.insulation ? building.insulation.definition : null"
                                category="Insulation"
                                hide-codes
                                multiple
                                @input="({id, value}) => updateBuildingForProperty(index,'insulation' ,'definition', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-2" data-cy="construction-information-glazing">
                    <label>
                        <span class="label">Glazing</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_GLAZING" :index="index">
                            <ClassificationDropdown
                                :value="building.glazing ? building.glazing.definition : null"
                                category="DoubleGlazing"
                                hide-codes
                                @input="({id, value}) => updateBuildingForProperty(index,'glazing' ,'definition', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
                <div class="col col-5" data-cy="construction-information-other-features">
                    <label>
                        <span class="label">Other Features</span>
                        <ValidationWrapper :path="FIELDS.BUILDING_OTHER_FEATURES" :index="index">
                            <ClassificationDropdown
                                :limit="6"
                                :value="building.otherFeatures ? building.otherFeatures.definition : null"
                                category="BuildingFeature"
                                hide-codes
                                multiple
                                @input="({id, value}) => updateBuildingForProperty(index,'otherFeatures' ,'definition', value)"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
            </div>
        </div>
        <div class="button-row">
            <div class="col-row">
                <div class="col col-12" data-cy="add-building-row">
                    <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small" data-cy="add-building-btn"
                            title="Add a Building" @click="addBuildingRow">
                        Add Building
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import YesNoIndeterminateDropdown from '@/components/common/form/YesNoIndeterminateDropdown.vue';
import { ValidationWrapper } from '@/components/ui/validation';
import { PropertyDetail } from '@quotable-value/validation';

const { FIELDS } = PropertyDetail;

const props = defineProps({
    propertyDetail: {
        type: Object,
        required: true,
    },
});
const emit = defineEmits(['update:property-detail']);

function onUpdatePropertyDetail(value) {
    emit('update:property-detail', value);
}

</script>

<template>
    <div class="property-draft-section">
        <div class="col-row">
            <div class="col col-1" data-cy="valuation-property-details-lot-position">
                <label>
                    <span class="label">Lot position</span>
                    <ValidationWrapper :path="FIELDS.LOT_POSITION">
                        <ClassificationDropdown
                            id="lotPosition"
                            :value="propertyDetail.site.lotPosition"
                            category="LotPosition_DVR"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-contour">
                <label>
                    <span class="label">Contour</span>
                    <ValidationWrapper :path="FIELDS.CONTOUR">
                        <ClassificationDropdown
                            id="contour"
                            :value="propertyDetail.site.contour"
                            category="Contour_DVR"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-3" data-cy="valuation-property-details-view">
                <label>
                    <span class="label">View</span>
                    <ValidationWrapper :path="FIELDS.VIEW">
                        <ClassificationDropdown
                            id="view"
                            :value="propertyDetail.site.view"
                            category="View_DVR"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-2" data-cy="valuation-property-details-view-scope">
                <label>
                    <span class="label">View Scope</span>
                    <ValidationWrapper :path="FIELDS.VIEW_SCOPE">
                        <ClassificationDropdown
                            id="view"
                            :value="propertyDetail.site.view"
                            category="View_DVR"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-3" data-cy="valuation-property-details-class-of-surrounding-improvements">
                <label>
                    <span class="label">Class of Surrounding Improvements (CSI)</span>
                    <ValidationWrapper :path="FIELDS.SITE_CSI">
                        <ClassificationDropdown
                            id="classOfSurroundingImprovements"
                            :value="propertyDetail.site.classOfSurroundingImprovements"
                            category="ClassOfSurroundingImprovements_DVR"
                            @input="onUpdatePropertyDetail"
                        />
                    </ValidationWrapper>
                </label>
            </div>
            <div class="col col-1" data-cy="valuation-property-details-outlier">
                <label>
                    <span class="label">Outlier</span>
                    <span class="field">
                            <ValidationWrapper :path="FIELDS.IS_OUTLIER">
                                <YesNoIndeterminateDropdown
                                    id="isOutlier"
                                    :value="propertyDetail.isOutlier"
                                    @input="onUpdatePropertyDetail"
                                />
                            </ValidationWrapper>
                        </span>
                </label>
            </div>
        </div>
    </div>
</template>

<script setup>
import ClassificationLookup from '@/components/common/ClassificationLookup.vue';

const props = defineProps({
    apportionments: {
        required: true,
        type: Array,
    },
});

function natureOfImprovementsFormatter(apportionment, opt) {
    const noi = apportionment.natureOfImprovements.find(
        noi => noi.improvement.code === opt.code,
    );
    const quantityText = noi != null && noi.quantity > 1
        ? ' (' + noi.quantity + ')'
        : '';
    return noi != null
        ? `${noi.improvement.description || ''}${quantityText}`
        : '';
}
</script>

<template>
    <div class="qv-w-full" data-cy="rating-apportionments">
        <h3 class="section-title" data-cy="cy-rating-apportionments-title">
            Rating Apportionments
        </h3>
        <div v-for="(apportionment, index) in apportionments" :key="apportionment.qpid">
            <div class="qv-flex-row qv-w-full qv-pd-section-divider qv-mb-3 qv-pb-1 qv-pt-1" data-cy="rating-apportionments-item">
                <div class="col qv-pr-2" style="min-width: 15px;">
                    <label>
                        <span class="label">Suffix</span>
                        <p class="qv-text-center qv-font-bold qv-text-lg qv-color-darkblue" data-cy="suffix">
                            {{ apportionment.suffix }}
                        </p>
                    </label>
                </div>
                <div class="qv-flex-column qv-w-full">
                    <div class="col-row" style="min-height: 48px;">
                        <label class="col col-4">
                            <span class="label">Category</span>
                            <classification-lookup
                                category="Category_DVR"
                                :value="apportionment.category"
                                :label-function="(opt) => opt ? `${opt.code} — ${opt.description}` : '—'"
                            />
                        </label>
                        <label class="col col-4">
                            <span class="label">Nature of Improvements</span>
                            <classification-lookup
                                category="NatureOfImprovements_DVR"
                                :value="apportionment.natureOfImprovements.map(noi => noi.improvement)"
                                :label-function="(opt) => { return natureOfImprovementsFormatter(apportionment,opt); }"
                                :multiple="true"
                            />
                        </label>
                        <label class="col col-1">
                            <span class="label">TA Land Zone</span>
                            <span>{{ apportionment.landZone | code }}</span>
                        </label>
                        <label class="col col-2">
                            <span class="label">Land Use</span>
                            <classification-lookup
                                category="LandUse_DVR"
                                :value="apportionment.landUse"
                            />
                        </label>
                        <label class="col col-1">
                            <span class="label">Units</span>
                            <span>{{ apportionment.units | emptyToDash }}</span>
                        </label>
                    </div>
                    <div class="col-row" style="min-height: 48px;">
                        <label class="col col-2">
                            <span class="label">Age</span>
                            <classification-lookup
                                category="Age_DVR"
                                :value="apportionment.age"
                            />
                        </label>
                        <label class="col col-2">
                            <span class="label">Wall Construction</span>
                            <span>{{ apportionment.wallConstruction | description }}</span>
<!-- we should be using classification-lookup, however something is not quite right with underlying data -->
<!--                            <classification-lookup-->
<!--                                category="WallConstruction_DVR"-->
<!--                                :value="apportionment.wallConstruction"-->
<!--                            />-->
                        </label>
                        <label class="col col-1">
                            <span class="label">Wall Condition</span>
                            <classification-lookup
                                category="FeatureQuality"
                                :value="apportionment.wallCondition"
                            />
                        </label>
                        <label class="col col-2">
                            <span class="label">Roof Construction</span>
                            <span>{{ apportionment.roofConstruction | description }}</span>
<!-- we should be using classification-lookup, however something is not quite right with underlying data -->
<!--                            <classification-lookup-->
<!--                                multiple-->
<!--                                category="RoofConstruction_DVR"-->
<!--                                :value="apportionment.roofConstruction"-->
<!--                            />-->
                        </label>
                        <label class="col col-1">
                            <span class="label">Roof Condition</span>
                            <classification-lookup
                                category="FeatureQuality"
                                :value="apportionment.roofCondition"
                            />
                        </label>
                        <label class="col col-1">
                            <span class="label">Site Cover</span>
                            <span>{{ apportionment.buildingSiteCover | emptyToDash }}</span>
                        </label>
                        <label class="col col-1">
                            <span class="label">Total Floor Area</span>
                            <span>{{ apportionment.totalFloorArea | emptyToDash }}</span>
                        </label>
                        <label class="col col-1">
                            <span class="label">Carparks</span>
                            <span>{{ apportionment.carparks | emptyToDash }}</span>
                        </label>
                        <label class="col col-1">
                            <span class="label">Land Area, ha</span>
                            <span>{{ apportionment.landArea | emptyToDash }}</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.qv-pd-section-divider {
    border-bottom: .1rem dashed #899ac5;
}
</style>

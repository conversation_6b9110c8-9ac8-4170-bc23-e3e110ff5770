<script setup>
import { ref, toRefs, computed } from 'vue';
import useDraftProperty from '@/composables/useDraftProperty';
import { ValidationContext } from '@/components/ui/validation';
import AlertModal from '@/components/common/modal/AlertModal.vue';

import {
    GeneralPropertyInformation,
    LocationDetails,
    PropertySummary,
    DerivedDvrFields,
    BuildingsInformation,
    BuildingSpaces,
    SiteImprovements,
} from '@/components/propertyDetails/residential/index';

import { toNumber } from './index';

import RatingApportionments from '@/components/propertyDetails/residential/RatingApportionments.vue';
import DetailSection from '@/components/ui/detailSection/DetailSection.vue';
import DetailSectionTitle from '@/components/ui/detailSection/DetailSectionTitle.vue';
import DetailSectionHeader from '@/components/ui/detailSection/DetailSectionHeader.vue';
import DetailSectionBody from '@/components/ui/detailSection/DetailSectionBody.vue';
import { DetailSectionExpander } from '@/components/ui/detailSection';

const emit = defineEmits([
    'update:property-detail',
    'update:site-development',
    'update:other-improvements',
    'update:dvr-snapshot',
    'generate:buildings',
    'generate:property-detail',
    'open:qivs-improvement-summary'
]);

const props = defineProps({
    propertyDetail: {
        type: Object,
        required: true,
    },
    property: {
        type: Object,
        required: true,
    },
    currentPropertyDetail: {
        type: Object,
        required: false,
    },
    validationSet: {
        type: Object,
        required: false,
    },
    highlight: {
        required: false,
    },
    disabled: {
        type: Boolean,
        required: false,
    },
});

const {
    propertyDetail,
    property,
    currentPropertyDetail,
} = toRefs(props);

const showGenerateBuildingsModal = ref(false);
const generateBuildingsOptions = ref({ umrGarages: null, fsGarages: null });
const showRegenerateButton = computed(() => currentPropertyDetail?.buildings?.length === 0);

const {
    qpid,
    taCode,
    buildings,
    dvrSnapshot,
    siteDevelopment,
    otherImprovements,
    isResidentialVacant,
    hasDerivedDvrFields,
    hasQivsImprovements,
    hasUsefulQivsImprovements,
} = useDraftProperty(propertyDetail, property);


function cancelGenerateBuildings() {
    showGenerateBuildingsModal.value = false;
}

function startGenerateBuildings() {
    showGenerateBuildingsModal.value = true;
}

function generateBuildings() {
    showGenerateBuildingsModal.value = false;
    emit('generate:buildings', generateBuildingsOptions.value);
}

function onUpdatePropertyDetails($event) {
    emit('update:property-detail', $event);
}

function onUpdateBuldings($event) {
    emit('update:property-detail', { id: 'buildings', value: $event });
}

function onUpdateSiteImprovements($event) {
    emit('update:property-detail', { id: 'siteDevelopment', value: $event });
}

function onUpdateOtherImprovements($event) {
    emit('update:property-detail', { id: 'otherImprovements', value: $event });
}

function onUpdateDvrSnapshot() {
    emit('update:dvr-snapshot');
}

function onGenerateNewDwellingPropertyDetails() {
    emit('generate:property-detail');
}

function openQivsImprovementSummary() {
    emit('open:qivs-improvement-summary');
}

function setRatingApportionments(apportionments) {
    emit('update:property-detail', { id: 'ratingApportionments', value: apportionments });
}

</script>

<template>
    <ValidationContext :validation-set="validationSet">
        <div class="qv-flex-column qv-gap-2">
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">General Property Information</h2>
                <button v-if="isResidentialVacant"
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small flex-left"
                    title="Populates some fields with default values for new dwellings."
                    :disabled="disabled"
                    @click="onGenerateNewDwellingPropertyDetails">
                    Populate DVR Data
                </button>
                <GeneralPropertyInformation
                    :property-detail="propertyDetail"
                    :ta-code="taCode"
                    @update:property-detail="onUpdatePropertyDetails" />
            </div>
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Location Details</h2>
                <LocationDetails
                    :property-detail="propertyDetail"
                    @update:property-detail="onUpdatePropertyDetails"
                />
            </div>
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Property Summary</h2>
                <PropertySummary
                    :property-detail="propertyDetail"
                    @update:property-detail="onUpdatePropertyDetails"
                />
            </div>
            <div v-if="propertyDetail" class="qv-flex-column qv-gap-1" :class="{ 'highlight': highlight }">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Derived fields</h2>
                <DerivedDvrFields
                    :dvr-snapshot="dvrSnapshot"
                    :highlight="highlight"
                    :has-derived-dvr-fields="hasDerivedDvrFields"
                    @update:dvr-snapshot="onUpdateDvrSnapshot"
                />
            </div>

            <DetailSection v-if="Array.isArray(propertyDetail.ratingApportionments) && propertyDetail.ratingApportionments.length > 0"
                           data-cy="draft-property-rating-apportionments"
                           :default-expanded="false"
            >
                <DetailSectionHeader>
                    <DetailSectionTitle>
                        Rating Apportionments
                    </DetailSectionTitle>
                    <DetailSectionExpander/>
                </DetailSectionHeader>
                <DetailSectionBody>
                    <RatingApportionments
                        :apportionments="propertyDetail.ratingApportionments"
                        @update:apportionments="setRatingApportionments"
                        :validation-set="validationSet"
                    />
                </DetailSectionBody>
            </DetailSection>
            <div v-if="isResidentialVacant" class="col-row">
                <div class="col col-12">
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small"
                        title="This will generate buildings based on the draft property data provided"
                        :disabled="disabled" @click="startGenerateBuildings">
                        Generate Buildings
                    </button>
                </div>
            </div>
            <div v-if="!isResidentialVacant && showRegenerateButton" class="col-row">
                <div class="col col-12">
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small"
                        title="This will generate buildings based on the draft property data provided"
                        :disabled="disabled" @click="startGenerateBuildings">
                        Regenerate Buildings
                    </button>
                </div>
            </div>
            <DetailSection data-cy="draft-property-construction-information">
                <DetailSectionHeader>
                    <DetailSectionTitle>Construction Information</DetailSectionTitle>
                    <div v-if="hasQivsImprovements" class="qv-flex-row qv-gap-0 righty">
                        <i v-if="hasUsefulQivsImprovements" class="material-icons"
                           title="The QIVS Improvement Summary may contain useful construction information for this property.">
                            add_alert
                        </i>
                        <ul>
                            <li class="md-qivs" @click="openQivsImprovementSummary">
                                <label>IMPROVEMENT SUMMARY</label>
                                <i class="material-icons">call_made</i>
                            </li>
                        </ul>
                    </div>
                </DetailSectionHeader>
                <DetailSectionBody>
                    <BuildingsInformation
                        :buildings="buildings"
                        @update:buildings="onUpdateBuldings"
                    />
                </DetailSectionBody>
            </DetailSection>
            <DetailSection data-cy="draft-property-spaces">
                <DetailSectionHeader>
                    <DetailSectionTitle>Spaces</DetailSectionTitle>
                </DetailSectionHeader>
                <DetailSectionBody>
                    <BuildingSpaces
                        :buildings="buildings"
                        @update:buildings="onUpdateBuldings"
                    />
                </DetailSectionBody>
            </DetailSection>
            <DetailSection data-cy="draft-property-site-improvements">
                <DetailSectionHeader>
                    <DetailSectionTitle>Site Improvements</DetailSectionTitle>
                </DetailSectionHeader>
                <DetailSectionBody>
                    <SiteImprovements
                        :site-development="siteDevelopment"
                        :other-improvements="otherImprovements"
                        @update:site-development="onUpdateSiteImprovements"
                        @update:other-improvements="onUpdateOtherImprovements"
                    />
                </DetailSectionBody>
            </DetailSection>
            <AlertModal v-if="showGenerateBuildingsModal" warning>
                <h1>Generate Construction Information and Spaces</h1>
                <p>This action will generate construction information and spaces based on the Draft Property data.
                    Existing
                    construction information and spaces will be overwritten.</p>
                <br>
                <p>Provide number of UMR and FS garages if known</p>
                <br>
                <div>
                    <label class="generate-modal-input--small">
                        <span>UMR Garages</span>
                        <input :value="generateBuildingsOptions.umrGarages" type="number" min="0" step="1"
                            @change="($event) => generateBuildingsOptions.umrGarages = toNumber($event.target.value)">
                    </label>
                    <label class="generate-modal-input--small">
                        <span>FS Garages</span>
                        <input :value="generateBuildingsOptions.fsGarages" type="number" min="0" step="1"
                            @change="($event) => generateBuildingsOptions.fsGarages = toNumber($event.target.value)">
                    </label>
                </div>
                <template #buttons>
                    <div class="alertButtons">
                        <button id="errorCancel" class="mdl-button mdl-button--mini lefty"
                            @click="cancelGenerateBuildings()">
                            Cancel
                        </button>
                        <button id="continue" class="mdl-button mdl-button--mini" @click="generateBuildings">
                            Confirm
                        </button>
                    </div>
                </template>
            </AlertModal>
        </div>
    </ValidationContext>
</template>

<script setup>
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import { NUMERAL_PRESET, NumeralInput } from '@/components/ui/input';
import NatureOfImprovements from '@/components/propertyDetails/NatureOfImprovements.vue';
import { computed } from 'vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { PropertyDetail } from '@quotable-value/validation';

const { FIELDS } = PropertyDetail;

const props = defineProps({
    value: {
        type: Object,
        required: true,
    },
    index: {
        type: Number,
        required: false,
    }
});


const emit = defineEmits(['update:apportionment']);
const apportionment = computed(() => props.value);

function setField(field) {
    emit('input', {...apportionment.value, ...field});
}
const setCategory = (event) => setField({category: event.value});
const setNatureOfImprovements = (event) => setField({natureOfImprovements: event.value});
const setLandUse = (event) => setField({landUse: event.value});
const setLandZone = (event) => setField({landZone: event.value});
const setUnits = (event) => setField({units: event});
const setAge = (event) => setField({age: event.value});
const setWallConstruction = (event) => setField({wallConstruction: event.value});
const setWallCondition = (event) => setField({wallCondition: event.value});
const setRoofConstruction = (event) => setField({roofConstruction: event.value});
const setRoofCondition = (event) => setField({roofCondition: event.value});
const setBuildingSiteCover = (event) => setField({buildingSiteCover: event});
const setTotalFloorArea = (event) => setField({totalFloorArea: event});
const setCarparks = (event) => setField({carparks: event});
const setLandArea = (event) => setField({landArea: event});

function formatDropdown(option) {
    return `${option.code} - ${option.description}`;
}
</script>

<template>
    <ValidationProvider path="ratingApportionments">
        <div class="qv-flex-row qv-w-full qv-pd-section-divider qv-mb-3 qv-pb-1 qv-pt-1" data-cy="rating-apportionments-item">
            <div class="qv-pr-2" style="min-width: 15px;">
                <label>
                    <span class="label">Suffix</span>
                    <p class="qv-text-center qv-font-bold qv-text-lg qv-color-darkblue" data-cy="suffix">
                        {{ apportionment.suffix }}
                    </p>
                </label>
            </div>
            <div class="qv-flex-column qv-w-full">
                <div class="qv-flex-row" style="min-height: 48px;">
                    <label class="qv-flex-grow">
                        <span class="label">Category</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_CATEGORY" :index="index">
                            <ClassificationDropdown
                                data-cy="category"
                                :single-label-function="formatDropdown"
                                :value="apportionment.category"
                                @input="setCategory"
                                category="Category_DVR"
                                label="code"
                            />
                        </ValidationWrapper>
                    </label>
                    <label class="qv-w-32/100">
                        <span class="label">Nature of Improvements</span>
                        <NatureOfImprovements
                            data-cy="nature-of-improvements"
                            :value="apportionment.natureOfImprovements"
                            @input="setNatureOfImprovements"
                        />
                    </label>
                    <label class="qv-w-8/100">
                        <span class="label">TA Land Zone</span>
                        <ClassificationDropdown
                            data-cy="ta-land-zone"
                            :category="apportionment.landZone.category"
                            :value="apportionment.landZone"
                            @input="setLandZone"
                            label="code"
                        />
                    </label>
                    <label class="qv-w-20/100">
                        <span class="label">Land Use</span>
                        <ClassificationDropdown
                            data-cy="land-use"
                            :single-label-function="formatDropdown"
                            :value="apportionment.landUse"
                            @input="setLandUse"
                            category="LandUse_DVR"
                            label="code"
                        />
                    </label>
                    <label class="qv-w-3/100">
                        <span class="label">Units</span>
                        <NumeralInput
                            data-cy="units"
                            :value="apportionment.units"
                            @input="setUnits"
                            min="0"
                        />
                    </label>
                </div>
                <div class="qv-flex-row" style="min-height: 48px;">
                    <label class="col-2">
                        <span class="label">Age</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_AGE" :index="index">
                            <ClassificationDropdown
                                data-cy="age"
                                :value="apportionment.age"
                                @input="setAge"
                                category="Age_DVR"
                                label="code"
                            />
                        </ValidationWrapper>
                    </label>
                    <label class="col-2">
                        <span class="label">Wall Construction</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_WALL_CONSTRUCTION" :index="index">
                            <ClassificationDropdown
                                data-cy="wall-construction"
                                :value="apportionment.wallConstruction"
                                @input="setWallConstruction"
                                hide-codes
                                category="BuildingConstruction"
                            />
                        </ValidationWrapper>
                    </label>
                    <label class="col-1">
                        <span class="label">Wall Condition</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_WALL_CONDITION" :index="index">
                            <ClassificationDropdown
                                data-cy="wall-condition"
                                :value="apportionment.wallCondition"
                                @input="setWallCondition"
                                hide-codes
                                category="BuildingCondition"
                            />
                        </ValidationWrapper>
                    </label>
                    <label class="col-2">
                        <span class="label">Roof Construction</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_ROOF_CONSTRUCTION" :index="index">
                            <ClassificationDropdown
                                data-cy="roof-construction"
                                :value="apportionment.roofConstruction"
                                @input="setRoofConstruction"
                                hide-codes
                                category="BuildingConstruction"
                            />
                        </ValidationWrapper>
                    </label>
                    <label class="col-1">
                        <span class="label">Roof Condition</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_ROOF_CONDITION" :index="index">
                            <ClassificationDropdown
                                data-cy="roof-condition"
                                :value="apportionment.roofCondition"
                                @input="setRoofCondition"
                                hide-codes
                                category="BuildingCondition"
                            />
                        </ValidationWrapper>
                    </label>
                    <label class="col-1">
                        <span class="label">Site Cover</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_SITE_COVER" :index="index">
                            <NumeralInput
                                data-cy="site-cover"
                                :value="apportionment.buildingSiteCover"
                                @input="setBuildingSiteCover"
                                :preset="NUMERAL_PRESET.AREA"
                            />
                        </ValidationWrapper>
                    </label>
                    <label class="col-1">
                        <span class="label">Total Floor Area</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_TOTAL_FLOOR_AREA" :index="index">
                            <NumeralInput
                                data-cy="total-floor-area"
                                :value="apportionment.totalFloorArea"
                                @input="setTotalFloorArea"
                            />
                        </ValidationWrapper>
                    </label>
                    <label class="col-1">
                        <span class="label">Carparks</span>
                        <NumeralInput
                            data-cy="carparks"
                            :value="apportionment.carparks"
                            @input="setCarparks"
                        />
                    </label>
                    <label class="col-1">
                        <span class="label">Land Area, ha</span>
                        <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_LAND_AREA" :index="index">
                            <NumeralInput
                                data-cy="land-area"
                                :preset="NUMERAL_PRESET.AREA_HECTARES"
                                :value="apportionment.landArea"
                                @input="setLandArea"
                                min="0"
                            />
                        </ValidationWrapper>
                    </label>
                </div>
            </div>
        </div>
    </ValidationProvider>
</template>

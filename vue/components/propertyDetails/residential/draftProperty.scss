.qv-box-header {
    display: flex;
    flex-direction: row;
    gap: 1rem;

    .title {
        flex-grow: 1;
    }

    .qv-button-row {
        align-self: center;
    }
}

.qv-button-row {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
}

.proposedValues ul {
    display: inline-grid;
}

.proposedValues.error {
    background-color: #fc3d39;
    padding-left: 0.3rem;
}

.draft-property {
    margin-bottom: 2rem;
}

.property-draft-section {
    border-bottom: .1rem dashed #e2e2e2;
    padding-top: 0;
    padding-bottom: 0.8rem;
    /* TODO need to standardise col container but allow for collapsing margins. */
    display: table;
    width: 100%;
}

.validation-header-message--warnings {
    font-size: 0.9em;
    overflow: auto;
}

.derived-title {
    font-weight: 600;
    padding-left: 5px;
    padding-top: 5px;
}

.derived-field {
    cursor: auto;
}

.form-stale {
    background-color: #eee;
}

.form-stale-warning {
    text-align: center;
    font-size: 0.8em;

    &--save-button {
        height: 28px;
        line-height: 28px;
        font-size: 0.9em;
    }
}

.generate-modal-input--small {
    width: 30%;
    display: inline-block;
    margin-right: 1em;
}

.button--small {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.flex-left {
    margin-left: auto;
}

.derived-field {
    cursor: auto;
}

.highlight {
    background-color: #eee;

    .col-container {
        background-color: #eee;
    }
}

.highlight-field {
    background-color: #ffffcf;
    border-color: #ffeeba;
}

.highlight-warning {
    text-align: center;
    font-size: 0.8em;

}

.construction-information {
    padding: 0.5rem;
    padding-left: 1rem;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    border-bottom: 0.1rem solid #214d90;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.row-controls {
    div button {
        margin-bottom: 0.5em;
    }

    div:last-child button {
        margin-bottom: unset;
    }
}

.button--small {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

.button-row {
    display: table;
    width: 100%;
}

.derived-field-label {
    cursor: text;
}

.qv-pd-section-divider {
    border-bottom: 0.1rem dashed #e2e2e2;
}
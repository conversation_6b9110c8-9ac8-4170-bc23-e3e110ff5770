export { default as GeneralPropertyInformation } from './GeneralPropertyInformation.vue';
export { default as LocationDetails } from './LocationDetails.vue';
export { default as PropertySummary } from './PropertySummary.vue';
export { default as DerivedDvrFields } from './DerivedDvrFields.vue';
export { default as BuildingsInformation } from './BuildingsInformation.vue';
export { default as BuildingSpaces } from './BuildingSpaces.vue';
export { default as SiteImprovements } from './SiteImprovements.vue';

export const buildingCodes = {
    AP: 'A',
    DW: 'D',
    GA: 'G',
};

export const OTHER_IMPROVEMENT_CATEGORY = 'OtherImprovement';

export const LOCKED_IMPROVEMENT_UNIT_OF_MEASURE = {
    CA: 'CS',
    CD: 'CS',
    CP: 'CS',
    DC: 'SM',
    DU: 'SM',
    FN: 'LM',
    SW: 'SM',
    WA: 'LI',
};

export function toNumber(text) {
    const n = Number.parseInt(text, 10);
    return Number.isNaN(n) ? null : n;
}

export function errorClasses(validationSet, field) {
    return {
        error: validationSet && validationSet.value[field] && validationSet.value[field].length > 0,
    };
}

export function formatDate(date) {
    const splitDate = date.split('-');
    return `${splitDate[2]}/${splitDate[1]}/${splitDate[0]}`;
}

export function generateBuildingLabel(buildingType, buildings) {
    if (!buildingType?.code) {
        return null;
    }
    const buildingCode = buildingCodes[buildingType.code.toUpperCase()] || buildingType.code.toUpperCase();

    const buildingNumbers = Array.from(buildings)
        .map(({ buildingLabel }) => buildingLabel)
        .filter((label) => label?.startsWith(buildingCode))
        .map((label) => parseInt(label.match(/\d+/)?.[0], 10))
        .filter(Number.isInteger);

    const nextNumber = buildingNumbers.reduce((max, num) => Math.max(max, num), 0) + 1;
    return `${buildingCode}${nextNumber}`;
}

export function getUnitOfMeasure(value, unitOfMeasureClassifications) {
    if (!value || !value.code) return null;
    const unitOfMeasureCode = LOCKED_IMPROVEMENT_UNIT_OF_MEASURE[value.code] || 'UN';
    return unitOfMeasureClassifications.find(element => element.code === unitOfMeasureCode) || null;
}

export function isUnitOfMeasureLocked(improvement) {
    return improvement?.definition?.code && LOCKED_IMPROVEMENT_UNIT_OF_MEASURE[improvement.definition.code];
}

export function otherImprovementsFilter(options, improvement) {
    if (isUnitOfMeasureLocked(improvement)) {
        return options.filter(option => option.code === LOCKED_IMPROVEMENT_UNIT_OF_MEASURE[improvement.definition.code]);
    }
    return options;
}

export function getOptions(classifications, category) {
    return classifications[category] || [];
}
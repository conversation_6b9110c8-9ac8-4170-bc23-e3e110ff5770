<script setup>
import { computed } from 'vue';
import ValidationMessage from '@/components/common/form/ValidationMessage.vue';

const emit = defineEmits(['update:dvr-fields']);
const props = defineProps({
    dvrSnapshot: {
        type: Object,
        required: true,
    },
    hasDerivedDvrFields: {
        type: Boolean,
        required: true,
    },
    highlight: {
        type: Boolean,
        required: true,
    },
});

function dvrFieldHasChanged(dvrFieldName) {
    const dvrSnapshot = props.dvrSnapshot;
    return (
        dvrSnapshot?.changedDvrFields instanceof Array &&
        dvrSnapshot.changedDvrFields.includes(dvrFieldName)
    );
}

function saveDerivedFields() {
    emit('update:dvr-fields');
}

</script>

<template>
    <div class="property-draft-section col-container">
        <div class="col-row">
            <div class="col col-2" title="Based on Wall Construction and Condition of Principal buildings"
                data-cy="valuation-property-details-dvr-wall-construction">
                <label class="derived-field-label">
                    <span class="label">
                        Wall Construction / Condition
                    </span>
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('wallConstruction') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.wallConstruction : null | description }}
                    </span>
                    /
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('wallCondition') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.wallCondition : null | description }}
                    </span>
                </label>
            </div>
            <div class="col col-2" title="Based on Roof Construction and Condition of Principal buildings"
                data-cy="valuation-property-details-dvr-roof-construction">
                <label class="derived-field-label">
                    <span class="label">
                        Roof Construction / Condition
                    </span>
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('roofConstruction') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.roofConstruction : null | description }}
                    </span>
                    /
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('roofCondition') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.roofCondition : null | description }}
                    </span>
                </label>
            </div>
            <div class="col col-1" title="Based on Modernisation Age in Living Space of Principal Building"
                data-cy="valuation-property-details-dvr-modernisation">
                <label class="derived-field-label">
                    <span class="label">
                        Modernisation
                    </span>
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('isModernised') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.isModernised : null | yesno('—') }}
                    </span>
                </label>
            </div>
            <div class="col col-1" title="Based on Site Development Quality"
                data-cy="valuation-property-details-dvr-landscaping-quality">
                <label class="derived-field-label">
                    <span class="label">
                        Landscaping
                    </span>
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('landscapingQuality') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.landscapingQuality : null | description
                        }}
                    </span>
                </label>
            </div>
            <div class="col col-1" title="Yes if Site Improvements includes Deck" data-cy="valuation-property-details-dvr-deck">
                <label class="derived-field-label">
                    <span class="label">
                        Deck
                    </span>
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('hasDeck') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.hasDeck : null | yesno('—') }}
                    </span>
                </label>
            </div>
            <div class="col col-1"
                title="Has Large Other Improvements: Yes if Site Improvements includes Swimming Pool or Tennis Court or there is a Granny Flat, Guest House, Pool House or Sleep Out building"
                data-cy="valuation-property-details-dvr-large-other-improvements">
                <label class="derived-field-label">
                    <span class="label">
                        Large OIs
                    </span>
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('hasLargeOtherImprovements') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.hasLargeOtherImprovements : null |
                        yesno('—') }}
                    </span>
                </label>
            </div>
            <div class="col col-1"
                title="Under Main Roof Garaging: Total number of carparks from Garage spaces in Principal buildings that are Apartment, Dwelling, Flats - Multi, Flat – Single, Incomplete Dwelling or Town House"
                data-cy="valuation-property-details-dvr-under-main-roof-garages">
                <label class="derived-field-label">
                    <span class="label">
                        UMR Garaging
                    </span>
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('numberOfUnderMainRoofGarages') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.numberOfUnderMainRoofGarages : null |
                        numeral('0', '—') }}
                    </span>
                </label>
            </div>
            <div class="col col-1"
                title="Freestanding Garaging: Total number of carparks where Site Improvement is Carport or Garage space is in a Garage building"
                data-cy="valuation-property-details-dvr-freestanding-garages">
                <label class="derived-field-label">
                    <span class="label">
                        FS Garaging
                    </span>
                    <span class="derived-field"
                        :class="{ 'highlight-field': highlight && hasDerivedDvrFields && dvrFieldHasChanged('numberOfFreestandingGarages') }">
                        {{ hasDerivedDvrFields ? dvrSnapshot.numberOfFreestandingGarages : null |
                        numeral('0', '—') }}
                    </span>
                </label>
            </div>
            <div class="col col-2">
                <p v-if="highlight" class="highlight-warning righty">
                    This data is calculated on save. Save to see recalculated values.
                    <button data-cy="valuation-property-details-dvr-save-button"
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button--small"
                        @click="saveDerivedFields">
                        Save
                    </button>
                </p>
            </div>
        </div>
        <div class="col-row" v-if="highlight">
            <div class="col col-12">
                <label class="derived-field-label">NOTE: Derived fields are highlighted where they differ from the DVR
                    data.</label>
            </div>
        </div>
    </div>
</template>
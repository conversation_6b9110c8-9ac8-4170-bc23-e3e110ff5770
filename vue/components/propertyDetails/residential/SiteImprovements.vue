<script setup>
import { ref, toRefs, computed, inject } from 'vue';
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { NumeralInput, TextInput } from '@/components/ui/input';
import { isUnitOfMeasureLocked, otherImprovementsFilter, getUnitOfMeasure, OTHER_IMPROVEMENT_CATEGORY, LOCKED_IMPROVEMENT_UNIT_OF_MEASURE } from './index';
import { PropertyDetail } from '@quotable-value/validation';

const { FIELDS } = PropertyDetail;

const emit = defineEmits(['update:site-development', 'update:other-improvements']);

const props = defineProps({
    siteDevelopment: {
        type: Object,
        required: true,
    },
    otherImprovements: {
        type: Array,
        required: true,
    },
});

const classifications = inject('classifications');

function updateSiteDevelopment(key, value) {
    const updatedSiteDevelopment = { ...props.siteDevelopment, [key]: value };
    emit('update:site-development', updatedSiteDevelopment);
}

function updateImprovementDefinition(index, value) {
    const updatedImprovements = [...props.otherImprovements];
    const updatedImprovement = { ...updatedImprovements[index], definition: value };
    updatedImprovement.unitOfMeasure = getUnitOfMeasure(classifications?.value.UnitOfMeasure, value);
    updatedImprovements.splice(index, 1, updatedImprovement);
    emit('update:other-improvements', updatedImprovements);
}

function updateOtherImprovements(index, key, value) {
    const updatedImprovements = [...props.otherImprovements];
    updatedImprovements[index] = { ...updatedImprovements[index], [key]: value };
    emit('update:other-improvements', updatedImprovements);
}

function addImprovementRow() {
    const updatedImprovements = [...props.otherImprovements, {}];
    emit('update:other-improvements', updatedImprovements);
}

function removeImprovementRow(index) {
    if (confirm('This will remove this Improvement. Are you sure?')) {
        const updatedImprovements = [...props.otherImprovements];
        updatedImprovements.splice(index, 1);
        emit('update:other-improvements', updatedImprovements);
    }
}

</script>

<template>
    <div>
        <div class="property-draft-section">
            <!-- Site Development -->
            <div class="property-draft-section-row">
                <div class="col-row">
                    <div data-cy="site-other-improvement" class="col col-2">
                        <label>
                            <span class="label">Other Improvement</span>
                            <input type="text" readonly value="Site Development">
                        </label>
                    </div>
                    <div data-cy="site-quality" class="col col-2">
                        <label>
                            <span class="label">Quality</span>
                            <ClassificationDropdown
                                id="siteDevelopmentQuality"
                                hide-codes
                                category="FeatureQuality"
                                :value="siteDevelopment.quality"
                                :filter-options-function="(options) => options.filter(option => option.code !== 'M')"
                                @input="({ id, value }) => updateSiteDevelopment('quality', value)" />
                        </label>
                    </div>
                    <div data-cy="site-development-description" class="col col-8">
                        <label>
                            <span class="label">Description</span>
                            <TextInput
                                v-model="siteDevelopment.description"
                                @input="updateSiteDevelopment('description', $event)"
                            />
                        </label>
                    </div>
                </div>
            </div>
            <div v-for="(improvement, improvementIndex) in otherImprovements"
                :key="`otherImprovements-${improvementIndex}`" class="property-draft-section-row">
                <div class="col-row">
                    <div data-cy="site-improvement-other-improvement" class="col col-2">
                        <label>
                            <span class="label">Other Improvement</span>
                            <ClassificationDropdown
                                id="definition"
                                :value="improvement.definition"
                                :category="OTHER_IMPROVEMENT_CATEGORY"
                                hide-codes
                                @input="({ id, value }) => updateImprovementDefinition(improvementIndex, value)"
                            />
                        </label>
                    </div>
                    <div data-cy="site-imporvement-year-built" class="col col-1">
                        <label>
                            <span class="label">Year Built</span>
                            <ClassificationDropdown
                                id="age"
                                category="FeatureAge"
                                :value="improvement.age"
                                hide-codes
                                @input="({ id, value }) => updateOtherImprovements(improvementIndex, id, value)"
                            />
                        </label>
                    </div>
                    <div data-cy="site-improvement-area-quantity" class="col col-1">
                        <label>
                            <span class="label">Area/Quantity</span>
                            <NumeralInput
                                min="0"
                                step="1"
                                v-model="improvement.quantity"
                                @input="updateOtherImprovements(improvementIndex, 'quantity', $event)"
                            />
                        </label>
                    </div>
                    <div data-cy="site-improvement-unit-of-measure" class="col col-2">
                        <label>
                            <span class="label">Unit of Measure</span>
                            <ClassificationDropdown
                                id="unitOfMeasure"
                                category="UnitOfMeasure"
                                :value="improvement.unitOfMeasure"
                                :allow-empty="!isUnitOfMeasureLocked(improvement)"
                                :disabled="(isUnitOfMeasureLocked(improvement) && improvement.unitOfMeasure && LOCKED_IMPROVEMENT_UNIT_OF_MEASURE[improvement.definition.code] === improvement.unitOfMeasure.code)"
                                hide-codes
                                :filter-options-function="(options) => otherImprovementsFilter(options, improvement)"
                                @input="({ id, value }) => updateOtherImprovements(improvementIndex, id, value)"
                            />
                        </label>
                    </div>
                    <div data-cy="site-improvement-quality" class="col col-1">
                        <label>
                            <span class="label">Quality</span>
                            <ValidationWrapper :path="FIELDS.QUALITY" :index="improvementIndex">
                                <ClassificationDropdown
                                    id="quality"
                                    category="FeatureQuality"
                                    :value="improvement.quality"
                                    :filter-options-function="(options) => options.filter(option => option.code !== 'M')"
                                    hide-codes
                                    @input="({ id, value }) => updateOtherImprovements(improvementIndex, id, value)"
                                />
                            </ValidationWrapper>
                        </label>
                    </div>
                    <div data-cy="site-improvement-description" class="col col-4">
                        <label>
                            <span class="label">Description</span>
                            <TextInput
                                v-model="improvement.description"
                                @input=" updateOtherImprovements(improvementIndex, 'description', $event)"
                            />
                        </label>
                    </div>
                    <div data-cy="remove-improvement-row" class="col col-1">
                        <div class="righty">
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button--small"
                                @click="removeImprovementRow(improvementIndex)">
                                Remove
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="button-row">
            <div class="col-row">
                <div data-cy="add-improvement-row" class="col col-12">
                    <button
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small"
                        @click="addImprovementRow">
                        Add Improvement
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

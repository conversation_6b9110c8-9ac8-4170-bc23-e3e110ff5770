<template>
    <expandable-section title="General Information" data-cy="cw-pd-general-information-section">
        <div class="property-draft-section">
            <div class="col-row">
                <div class="col col-4">
                    <label>
                        <span class="label" data-cy="general-info-qv-category-label">QV Category</span>
                        <form-select
                            v-model="qvCategory"
                            :options="qvCategoryOptions"
                            :close-on-select="true"
                            :errors="[]"
                            ref="qvCategoryRef"
                            data-cy="cw-pd-qv-category"
                            @input="handleQvCategoryInput"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label" data-cy="general-info-grouping-label">Grouping</span>
                        <form-select
                            v-model="commercialGroupingCode"
                            :options="groupingOptions"
                            :errors="[]"
                            ref="commercialGroupingRef"
                            data-cy="cw-pd-grouping"
                            @input="handleCommercialGroupingInput"
                        />
                    </label>
                </div>
                <div class="col col-4">
                    <label>
                        <span class="label" data-cy="general-info-proposed-zone-label">Proposed Zone</span>
                        <form-select
                            v-model="proposedZoneCode"
                            :options="zoningOptions"
                            :errors="[]"
                            ref="proposedZoneRef"
                            data-cy="cw-pd-proposed-zone"
                            @input="handleProposedZoneInput"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label" data-cy="general-info-actual-earthquake-rating-label">Actual Earthquake Rating</span>
                        <input-number
                            v-model="actualEarthquakeRating"
                            :errors="[]"
                            :custom-format="appendPercentage"
                            @input="handleActualEarthquakeRatingInput"
                            data-cy="cw-pd-actual-eq-rating"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label" data-cy="general-info-earthquake-rating-range-label">Earthquake Rating Range</span>
                        <form-select
                            v-model="earthquakeRatingCode"
                            :options="earthquakeRatingRangeOptions"
                            :errors="[]"
                            :tabindex="-1"
                            ref="earthquakeRatingRangeRef"
                            :allow-empty="false"
                            data-cy="cw-pd-eq-rating-range"
                            @input="handleEarthquakeRatingRangeInput"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label" data-cy="general-info-earthquake-rating-assessor-label">Earthquake Rating Assessor</span>
                        <form-select
                            v-model="earthquakeRatingAssessorCode"
                            :readonly="!earthquakeRatingCode"
                            :options="earthquakeRatingAssessorOptions"
                            :errors="[]"
                            ref="earthquakeRatingAssessorRef"
                            :allow-empty="false"
                            data-cy="cw-pd-eq-rating-assessor"
                            @input="handleEarthquakeRatingAssessorInput"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label title="The year the remedial earthquake work must be done by">
                        <span class="label" style="white-space: nowrap;" data-cy="general-info-remedy-deadline-label">Remedy Deadline</span>
                        <input-number
                            v-model="remedyYear"
                            format="0"
                            placeholder="YYYY"
                            maxlength="4"
                            data-cy="cw-pd-remedy-year"
                            :errors="remedyYearErrors"
                            @input="handleRemedyYearInput"
                        />
                    </label>
                </div>
                <div class="col col-3" style="padding-left: 1.5rem">
                    <label>
                        <span class="label" data-cy="general-info-qv-liquefaction-label">Liquefaction (TC Rating)</span>
                        <form-select
                            v-model="liquefactionCode"
                            :options="liquefactionOptions"
                            :errors="[]"
                            ref="liquefactionRef"
                            :allow-empty="false"
                            data-cy="cw-pd-liquefaction"
                            @input="handleLiquefactionInput"
                        />
                    </label>
                </div>
            </div>
        </div>
    </expandable-section>
</template>

<script>
import { mapState } from 'vuex';
import * as referenceDataController from '@/services/ReferenceDataController';
import commonUtils from '../../utils/CommonUtils';
import { appendPercentage } from '../../utils/FormatUtils';
import Multiselect from 'vue-multiselect';
import DateTimePicker from '../filters/DateTimePicker.vue';
import _ from 'lodash';


export default {
    components: {
        'classification-lookup': () => import(/* webpackChunkName: "ClassificationLookup" */ '../common/ClassificationLookup.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../common/form/ValidationMessage.vue'),
        'input-number': () => import(/* webpackChunkName: "InputNumber2" */ '../common/form/InputNumber2.vue'),
        'form-select': () => import(/* webpackChunkName: "FormSelect" */ '../common/form/FormSelect.vue'),
        'expandable-section': () => import(/* webpackChunkName: "ExpanderContainer" */ './ExpandableSectionWrapper.vue'),
        Multiselect,
        DateTimePicker
    },
    mixins: [commonUtils],
    props: {
        validationSet: {
            type: Object,
            default: null
        },
        internalValidationSet: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            qvPropertyDetails: {
                id: null,
                qupid: null,
                effectiveDateOfCollection: null,
                effectiveLandArea: null,
                aggregateUnitDetails: {
                    unitNumber: null,
                    unitType: null,
                    numberOfSingleBedrooms: null,
                    numberOfDoubleBedrooms: null,
                    numberOfHomeOfficesOrStudies: null,
                    numberOfBathrooms: null,
                    mainBathroomAge: null,
                    mainBathroomQuality: null,
                    ensuiteAge: null,
                    ensuiteQuality: null,
                    kitchenAge: null,
                    kitchenQuality: null,
                    redecorationAge: null,
                    internalCondition: null,
                    heatingType: [],
                    insulation: [],
                    plumbingAge: null,
                    wiringAge: null,
                    doubleGlazing: null,
                    alternativeEnergy: [],
                    effectiveFloorArea: null,
                    studHeight: null,
                    additionalFeatures: null,
                    rentalAmount: {
                        source: null,
                        knownDate: null,
                        weeklyRentalAmount: null,
                    },
                },
                unitDetails: [],
                hazards: [],
                hazardNotes: null,
                heritageFeatures: [],
                averageDailySunshineHours: null,
                remedyYear: null,

            },
            propertyDetailObject: {
                propertyId: undefined,
                qpid: undefined,
                qvCategory: {
                    category: undefined,
                    code: undefined,
                    description: undefined,
                    shortDescription: undefined,
                    isActive: undefined,
                    parentClassification: undefined,
                    externalCode: undefined
                },
                commercialDetail: {
                    propertyGroupingTypeCommercial: {
                        category: undefined,
                        code: undefined,
                        description: undefined,
                        shortDescription: undefined,
                        isActive: undefined,
                        parentClassification: undefined,
                        externalCode: undefined
                    }
                }
            },
            zoneInfo: {
                maxSites: undefined,
                numberOfSites: undefined,
                proposedZone: undefined,
                qpid: undefined,
                zoneOverlay: undefined
            },
            qvCategoryCode: undefined,
            liquefactionCode: undefined,
            commercialGroupingCode: undefined,
            proposedZoneCode: undefined,
            actualEarthquakeRating: undefined,
            earthquakeRatingCode: undefined,
            earthquakeRatingAssessorCode: undefined,
        };
    },
    computed: {
        ...mapState('property', {
            property: 'property',
            proposedZones: 'proposedZoningOptions',
        }),
        ...mapState('propertyDraft', {
            propertyDetail: 'propertyDetail',
            draftLoading: 'loading',
            draftSaving: 'saving',
            draftException: 'exception',
            picklistValues: 'picklistValues',
            apportionmentDetails: 'apportionmentDetails',
            alertError: 'alertError',
            qivsDvrData: 'qivsDvrData'
        }),
        ...mapState('qvProperty', {
            qvProperty: 'qvProperty',
            qvPropertyLoading: 'loading',
            qvPropertySaving: 'saving',
            qvPropertyException: 'exception',
        }),
        ...mapState('zoneInfo', {
            zone: 'zoneInfo',
            zoneLoading: 'loading',
            zoneSaving: 'saving',
            zoneException: 'exception',
        }),
        propertyId() {
            return this.propertyLoaded ? this.propertyDetailObject.propertyId : null;
        },
        propertyLoaded() {
            return !this.draftLoading && this.propertyDetail;
        },
        qvPropertyLoaded() {
            return !this.qvPropertyLoading && this.qvProperty;
        },
        zoneInfoLoaded() {
            return !this.zoneLoading && this.zone;
        },
        qpid() {
            return this.propertyLoaded ? this.propertyDetailObject.qpid : null;
        },
        groupingOptions() {
            return this.getClassifications('PropertyGroupingTypeCommercial')?.map(item => ({ ...item, id: item.code })) ?? [];
        },
        zoningOptions() {
            return this.proposedZones?.map(item => ({ ...item, id: item.code, description: item.shortDescription })) ?? [];
        },
        qvCategoryOptions() {
            return this.picklistValues?.qvCategoryTypes?.map((item) => ({ ...item, id: item.code })) ?? [];
        },
        earthquakeRatingRangeOptions() {
            return this.getClassifications('EarthquakeRating')?.map(item => ({ ...item, id: item.code })) ?? [];
        },
        earthquakeRatingAssessorOptions() {
            return this.getClassifications('EarthquakeRatingAssessor')?.map(item => ({ ...item, id: item.code })) ?? [];
        },
        liquefactionOptions() {
            return this.getClassifications('LiquefactionRating')?.map(item => ({ ...item, id: item.code })) ?? [];
        },
        actualEarthquakeRatingErrors() {
            const errors = [];
            const value = this.actualEarthquakeRating;
            const rangeCode = this.earthquakeRatingCode;
            const rangeObject = this.earthquakeRatingRangeOptions.find(item => item.code === rangeCode);
            const [min, max] = rangeObject?.description?.replace('%', '').split('-') ?? [0, 0];
            if (!_.isNil(value) && (value < parseFloat(min) || value > parseFloat(max))) {
                errors.push('Actual Earthquake Rating must be within Earthquake Rating Range');
            }
            return errors;
        },
        qvCategory: {
            get() {
                return this.picklistValues && this.qvCategoryCode;
            },
            set(value) {
                this.qvCategoryCode = value;
            }
        },
        remedyYearErrors() {
            const errors = [];
            if (!_.isNil(this.remedyYear) && this.remedyYear.toString().length != 4){
                errors.push('Please enter a valid year');
            }
            return errors;
        },
        remedyYear: {
            get() {
                return this.qvPropertyDetails.remedyYear;
            },
            set(value) {
                this.qvPropertyDetails.remedyYear = value;
            }
        }
    },
    methods: {
        getLiquefactionHazard() {
            return this.qvPropertyDetails?.hazards.find(hazard => hazard.classification?.category === 'LiquefactionRating');
        },
        getEarthquakeRating() {
            return this.qvPropertyDetails.hazards.find(hazard => hazard.classification?.category === 'EarthquakeRating');
        },
        handleQvCategoryInput() {
            if (this.$refs.qvCategoryRef?.selection){
                this.propertyDetailObject.qvCategory = this.$refs.qvCategoryRef?.selection;
                this.updatePropertyDetails('qvCategory', this.$refs.qvCategoryRef?.selection);
            }

        },
        handleCommercialGroupingInput() {
            if (this.$refs.commercialGroupingRef?.selection){
                this.propertyDetailObject.commercialDetail.propertyGroupingTypeCommercial = this.$refs.commercialGroupingRef.selection;
                this.updatePropertyDetails('commercialDetail', this.propertyDetailObject.commercialDetail);
            }
        },
        handleProposedZoneInput() {
            if (this.$refs.proposedZoneRef?.selection){
                this.zoneInfo.proposedZone = this.$refs.proposedZoneRef.selection;
                this.updateZoneInfo();
            }
        },
        handleActualEarthquakeRatingInput(value) {
            let earthquakeRating = this.getEarthquakeRating();

            if (!earthquakeRating){
                this.setupEarthquakeRating();
                earthquakeRating = this.getEarthquakeRating();
            }

            const match = this.earthquakeRatingRangeOptions.find((item) => {
                const [min, max] = item.description.replace('%', '').split('-');
                return value >= parseFloat(min) && value <= (parseFloat(max) || Infinity);
            });

            this.earthquakeRatingCode = match?.code ?? null;
            earthquakeRating.classification = _.assign(earthquakeRating.classification, match);
            earthquakeRating.rating = value;

            this.updateQvProperty();
        },
        handleEarthquakeRatingRangeInput() {
            let earthquakeRating = this.getEarthquakeRating();

            if (!earthquakeRating){
                this.setupEarthquakeRating();
            }

            const value = this.$refs.earthquakeRatingRangeRef?.selection ?? earthquakeRating?.classification;

            earthquakeRating.classification = _.assign(earthquakeRating.classification, value);

            this.updateQvProperty();
        },
        handleEarthquakeRatingAssessorInput() {
            let earthquakeRating = this.getEarthquakeRating();

            if (!earthquakeRating){
                this.setupEarthquakeRating();
            }

            const value = this.$refs.earthquakeRatingAssessorRef?.selection ?? earthquakeRating?.source;

            earthquakeRating.source =  _.assign(earthquakeRating.source, value);

            this.updateQvProperty();
        },
        handleLiquefactionInput() {
            const liquefactionHazard = this.getLiquefactionHazard();
            const value = this.$refs.liquefactionRef?.selection ?? liquefactionHazard?.classification;

            if (liquefactionHazard){
                liquefactionHazard.classification = value;
            }
            else if (value){
                this.qvPropertyDetails?.hazards.push({
                    classification: value
                });
            }

            this.updateQvProperty();
        },
        handleRemedyYearInput() {
            this.updateQvProperty();
            this.$emit('validate-errors', { fieldName: 'remedyYear', errors: this.remedyYearErrors});
        },
        setupEarthquakeRating() {
            this.qvPropertyDetails.hazards.push({
                classification: {
                    category: 'EarthquakeRating'
                },
                source: {
                    category: 'EarthquakeRatingAssessor'
                },
                rating: undefined
            })

        },
        async getPickListValues() {
            try {
                await this.$store.dispatch('propertyDraft/getPickListValues');
            } catch (error) {
                this.handleException(error);
            }
        },
        updateQvProperty() {
            this.$store.commit('qvProperty/setQvProperty', this.qvPropertyDetails);
        },
        updatePropertyDetails(id, value) {
            this.$store.commit('propertyDraft/setSinglePropertyDetail', { id, value });
        },
        updateZoneInfo() {
            console.log('updateZoneInfo', this.zoneInfo);
            this.$store.commit('zoneInfo/setZoneInfo', this.zoneInfo);
        },
        async loadData(propertyId){
            await this.loadPropertyDetail(propertyId);
            await this.loadQvProperty(propertyId);
            await this.getPickListValues();
            await this.loadQvZoneInfo(this.qpid);
        },
        async loadQvProperty(propertyId) {
            if (!this.qvPropertyLoaded){
                await this.$store.dispatch('qvProperty/getQvProperty', propertyId);
            }
            this.qvPropertyDetails = _.merge(this.qvPropertyDetails, this.qvProperty);

            const earthquakeRating = this.getEarthquakeRating();
            this.actualEarthquakeRating = earthquakeRating?.rating;
            this.earthquakeRatingCode = earthquakeRating?.classification?.code;
            this.earthquakeRatingAssessorCode = earthquakeRating?.source?.code;

            const liquefactionHazard = this.getLiquefactionHazard();
            this.liquefactionCode = liquefactionHazard?.classification?.code;
        },
        async loadPropertyDetail(propertyId) {
            if (!this.propertyLoaded){
                this.$store.dispatch('propertyDraft/editCurrentPropertyDetail', propertyId);
            }
            this.propertyDetailObject = _.merge(this.property, this.propertyDetail);

            this.qvCategoryCode = this.propertyDetail?.qvCategory?.code;
            this.commercialGroupingCode = this.propertyDetail.commercialDetail?.propertyGroupingTypeCommercial?.code;
        },
        async loadQvZoneInfo(qpid) {
            if (!this.zoneInfoLoaded){
                await this.$store.dispatch('zoneInfo/getZoneInfo', qpid);
            }
            this.zoneInfo = _.merge(this.zoneInfo, this.zone);

            this.proposedZoneCode = this.zoneInfo?.proposedZone?.code;
        },
        appendPercentage,
    },
    async created() {
        await this.loadData(this.$route.params.id);
    },
    watch: {
        $route: (newVal, oldVal) => {
            if (newVal === oldVal) return;
            this.loadData(newVal.params.id);
        },
        property(newVal) {
            if (`${newVal.qupid}` === `${this.qpid}`) {
                this.qvPropertyDetails = _.merge(this.qvPropertyDetails, this.qvProperty);
                this.qvPropertyDetails.id = newVal.id;
                this.qvPropertyDetails.qupid = newVal.qupid;
                this.$store.commit('qvProperty/setQvProperty', this.qvPropertyDetails);
                this.loadQvZoneInfo(this.qpid);
            }
        },
    },
};
</script>

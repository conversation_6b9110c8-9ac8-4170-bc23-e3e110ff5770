<template>
    <div class="contentWrapper resultsWrapper draftProperty">
        <!-- PROPERTY BANNER -->
        <!-- eslint-disable max-len -->
        <property-summary
            :property-id="propertyId"
            :can-navigate="false"
        />
        <div v-if="draftLoading">
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
        <div
            v-if="draftException"
            class="bAlert bAlert-danger exception-message"
        >
            Unexpected Error: {{ draftException }}
        </div>
        <div v-if="propertyLoaded && classificationsLoaded" class="container-fluid">
            <div class="row col-container">
                <div class="col col-2 mdl-shadow--3dp">
                    <property-info :qpid="qpid" />
                </div>
                <div class="col col-10" data-cy="pd-edit-property-container">
                    <div
                        class="col-container mdl-shadow--3dp padding-edit-property-container"
                    >
                        <h1 class="title" data-cy="pd-edit-property-heading">
                            Edit Property
                        </h1>
                        <validation-header-message
                            ref="validationHeader"
                            :validation-set="validationSet"
                            :show-errors="true"
                            :show-warnings="true"
                            :show-warnings-message="true"
                            action="update the property"
                        />
                        <!-- GENERAL INFO -->
                        <general-info-section-read-only
                            v-if="propertyDetail"
                            :property-detail="propertyDetail"
                            :qv-property-details="qvPropertyDetails"
                            :zone-info="zoneInfo"
                            :validation-set="validationSet"
                            :internal-validation-set="internalValidationSet"
                            :can-edit="true"
                            :is-commercial="isCommercialProperty"
                            @validate-errors="validateErrors"
                            @update="update"
                        />
                        <!-- DERIVED DVR FIELDS -->
                        <derived-dvr-fields-section
                            v-if="propertyDetail && !isCommercialProperty"
                            :property-detail="propertyDetail"
                            :validation-set="validationSet"
                            :show-save="true"
                            :can-save="true"
                            :highlight="formIsStale"
                            @click-save="save"
                        />
                        <!-- BUILDINGS AND SPACES -->
                        <buildings-and-spaces-section
                            v-if="!isCommercialProperty"
                            :buildings="buildings"
                            :validation-set="validationSet"
                            :qpid="qpid"
                            :has-qivs-improvements="hasQivsImprovements"
                            :has-useful-qivs-improvements="hasUsefulQivsImprovements"
                            @update="update"
                        />
                        <!-- SITE IMPROVEMENTS -->
                        <site-improvements-section
                            v-if="!isCommercialProperty"
                            :site-development="siteDevelopment"
                            :other-improvements="otherImprovements"
                            :validation-set="validationSet"
                            @update="update"
                        />

                        <div class="col-row">
                            <div class="col col-12">
                                <div class="righty">
                                    <button
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                        :disabled="draftSaving"
                                        @click="cancel"
                                        data-cy="pd-cancel-button"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                        :disabled="draftSaving"
                                        @click="save"
                                        data-cy="pd-save-button"
                                    >
                                        Save
                                    </button>
                                    <button
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                        :disabled="draftSaving"
                                        @click="saveAndClose"
                                        data-cy="pd-save-and-close-button"
                                    >
                                        Save and Close
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <alert-modal
            v-if="warningsModalIsOpen"
            warning
        >
            <h3>
                Do you want to proceed?
            </h3>
            <p>The following validation checks are failing:</p>
            <validation-header-message
                ref="warnings"
                :validation-set="validationSet"
                :show-errors="true"
                :show-warnings="true"
                message=""
            />
            <template #buttons>
                <div class="alertButtons">
                    <button
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="closeWarningModal()"
                    >
                        No, Return to editing
                    </button>
                    <button
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="saveInternal(true)"
                    >
                        Yes, Save changes
                    </button>
                </div>
            </template>
        </alert-modal>

        <alert-modal
            v-if="alertModalIsOpen"
            warning
            @close="closeAlertModal"
        >
            <h3>{{ alertMessage.heading }}</h3>
            <p>{{ alertMessage.message }}</p>
        </alert-modal>

        <alert-modal
            v-if="successModalIsOpen"
            success
            @close="closeSuccessModal"
            data-cy="pd-success-modal"
        >
            <h3>{{ successMessage.heading }}</h3>
            <p>{{ successMessage.message }}</p>
        </alert-modal>
    </div>
</template>

<script>
// eslint-disable max-len

import { mapState, mapGetters } from 'vuex';
import * as propertyMasterDataController from '@/services/PropertyMasterDataController';
// set from 'lodash/set';
import commonUtils from '../../utils/CommonUtils';

export default {
    components: {
        'property-info': () => import(/* webpackChunkName: "PropertyInfo" */ '../property/PropertyInfo.vue'),
        'property-summary': () => import(/* webpackChunkName: "PropertySummary" */ '../property/PropertySummary.vue'),
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../common/modal/AlertModal.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "ValidationHeaderMessage" */ '../common/form/ValidationHeaderMessage.vue'),
        'general-info-section-read-only': () => import(/* webpackChunkName: "GeneralInfoReadOnly" */ './GeneralInfoReadOnly.vue'),
        'derived-dvr-fields-section': () => import(/* webpackChunkName: "DerivedDvrFields" */ './DerivedDvrFields.vue'),
        'buildings-and-spaces-section': () => import(/* webpackChunkName: "BuildingsAndSpaces" */ './BuildingsAndSpaces.vue'),
        'site-improvements-section': () => import(/* webpackChunkName: "SiteImprovements" */ './SiteImprovements.vue'),
    },
    data() {
        return {
            taCode: '',
            alertModalIsOpen: false,
            alertMessage: {
                heading: '',
                message: '',
            },
            successModalIsOpen: false,
            successMessage: {
                heading: '',
                message: '',
                navigateTo: null,
            },
            warningsModalIsOpen: false,
            closeOnSave: false,
            qvPropertyDetails: {
                id: null,
                qupid: null,
                effectiveDateOfCollection: null,
                effectiveLandArea: null,
                aggregateUnitDetails: {
                    unitNumber: null,
                    unitType: null,
                    numberOfSingleBedrooms: null,
                    numberOfDoubleBedrooms: null,
                    numberOfHomeOfficesOrStudies: null,
                    numberOfBathrooms: null,
                    mainBathroomAge: null,
                    mainBathroomQuality: null,
                    ensuiteAge: null,
                    ensuiteQuality: null,
                    kitchenAge: null,
                    kitchenQuality: null,
                    redecorationAge: null,
                    internalCondition: null,
                    heatingType: [],
                    insulation: [],
                    plumbingAge: null,
                    wiringAge: null,
                    doubleGlazing: null,
                    alternativeEnergy: [],
                    effectiveFloorArea: null,
                    studHeight: null,
                    additionalFeatures: null,
                    rentalAmount: {
                        source: null,
                        knownDate: null,
                        weeklyRentalAmount: null,
                    },
                },
                unitDetails: [],
                hazards: [],
                hazardNotes: null,
                heritageFeatures: [],
                averageDailySunshineHours: null,
                remedyYear: null,

            },
            zoneInfo: {
                maxSites: undefined,
                numberOfSites: undefined,
                proposedZone: undefined,
                qpid: undefined,
                zoneOverlay: undefined
            },
            internalValidationSet: { success: true, errors: [] }
        };
    },
    computed: {
        /* TODO Somewhat of a hack - map the property store to support the TA Zone dropdown. */
        ...mapState('property', {
            property: 'property',
        }),
        ...mapState('propertyDraft', {
            propertyDetail: 'propertyDetail',
            draftLoading: 'loading',
            draftSaving: 'saving',
            draftException: 'exception',
            validationSet: 'validationSet',
            formIsStale: 'formIsStale',
        }),
        ...mapState('qvProperty', {
            qvProperty: 'qvProperty',
            qvPropertyLoading: 'loading',
            qvPropertySaving: 'saving',
            qvPropertyException: 'exception',
        }),
        ...mapState('zoneInfo', {
            zone: 'zoneInfo',
            zoneLoading: 'loading',
            zoneSaving: 'saving',
            zoneException: 'exception',
        }),
        ...mapGetters([
            'classificationsLoaded',
        ]),
        propertyId() {
            if (this.propertyLoaded)
                return this.propertyDetail.propertyId;

            return null;
        },
        propertyLoaded() {
            return !this.draftLoading && this.propertyDetail;
        },
        qvPropertyLoaded() {
            return !this.qvPropertyLoading && this.qvProperty;
        },
        zoneInfoLoaded() {
            return !this.zoneLoading && this.zone;
        },
        taLandZoneClassification() {
            return `TA_${this.taCode}_LandZone_DVR`;
        },
        qpid() {
            return this.propertyLoaded && this.propertyDetail.qpid;
        },
        buildings() {
            if (
                this.propertyDetail
                && this.propertyDetail.buildings
                && this.propertyDetail.buildings.length > 0
            )
                return this.propertyDetail.buildings;
            else
                return [{}];
        },
        otherImprovements() {
            if (
                this.propertyDetail
                && this.propertyDetail.otherImprovements
                && this.propertyDetail.otherImprovements.length > 0
            )
                return this.propertyDetail.otherImprovements;
            else
                return [{}];
        },
        siteDevelopment() {
            if (
                this.propertyDetail
                && this.propertyDetail.site
                && this.propertyDetail.site.siteDevelopment
            )
                return this.propertyDetail.site.siteDevelopment;
            else
                return { quality: null, description: null };
        },
        hasQivsImprovements() {
            return this.propertyDetail.qivsImprovementsStatus !== 'NO_IMPROVEMENTS';
        },
        hasUsefulQivsImprovements() {
            return this.propertyDetail.qivsImprovementsStatus === 'USEFUL_IMPROVEMENTS';
        },
        isCommercialProperty() {
            if (!this.propertyDetail
                || !this.propertyDetail.category
                || !this.propertyDetail.category.code) {
                return false;
            }
            const propertyBaseCategory = this.propertyDetail.category.code.charAt(0);
            const valuableCommercialCategories = 'CIOU';
            if (valuableCommercialCategories.includes(propertyBaseCategory)) {
                return true;
            }
            return false;
        }
    },
    watch: {
        $route: (newVal, oldVal) => {
            if (newVal === oldVal) return;
            this.loadData(newVal.params.id);
        },
        property(newVal) {
            if (`${newVal.qupid}` === `${this.qpid}`) {
                this.qvPropertyDetails = _.merge(this.qvPropertyDetails, this.qvProperty);
                this.qvPropertyDetails.id = newVal.id;
                this.qvPropertyDetails.qupid = newVal.qupid;
                this.$store.commit('qvProperty/setQvProperty', this.qvPropertyDetails);
                this.loadTAZoneClassification(newVal.territorialAuthority.code);
                this.loadQvZoneInfo(this.qpid);
            }
        },
    },
    async created() {
        await this.loadData(this.$route.params.id);
    },
    methods: {
        async loadData(propertyId){
            await Promise.all([this.loadPropertyDetail(propertyId), this.loadQvProperty(propertyId)]);
        },
        async loadQvZoneInfo(qpid) {
            if (!this.zoneInfoLoaded){
                await this.$store.dispatch('zoneInfo/getZoneInfo', qpid);
            }
            this.zoneInfo = _.merge(this.zoneInfo, this.zone);
        },
        async loadQvProperty(propertyId) {
            if (!this.qvPropertyLoaded){
                await this.$store.dispatch('qvProperty/getQvProperty', propertyId);
            }
            this.qvPropertyDetails = _.merge(this.qvPropertyDetails, this.qvProperty);
        },
        async loadPropertyDetail(propertyId) {
            if (this.draftLoading) return;

            try {
                this.$store.dispatch('propertyDraft/editCurrentPropertyDetail', propertyId);
            } catch (err) {
                this.handleException(err);
            }
        },
        async loadTAZoneClassification(taCode) {
            await this.$store.dispatch('fetchTAZoneClassification', taCode);
            this.taCode = taCode;
        },
        update(data) {
            this.$store.commit('propertyDraft/setSinglePropertyDetail', data);
        },
        validateErrors(data) {
            if (this.internalValidationSet){
                let isSuccess = true;
                this.internalValidationSet.errors = this.internalValidationSet.errors.filter((item) => item.field !== data.fieldName);
                for(const error of data.errors){
                    this.internalValidationSet.errors.push({
                        field: data.fieldName,
                        message: error
                    });
                    isSuccess = false;
                }

                this.internalValidationSet.success = isSuccess;
            }
        },
        /* TODO Global exception handling (shouldnt need to do on all components etc) */
        handleException(err) {
            this.showAlertModal(
                'Unexpected Error',
                `An unexpected error occurred attempting to communicate with the server: ${err}`,
            );
        },
        scrollToTop() {
            this.$nextTick(() => {
                window.scrollTo({ top: this.$refs.validationHeader.$el.offsetTop, left: 0, behavior: 'smooth' });
            });
        },
        showAlertModal(heading, message) {
            this.alertMessage = {
                heading,
                message,
            };
            this.alertModalIsOpen = true;
        },
        closeAlertModal() {
            this.alertModalIsOpen = false;
        },
        showSuccess(message = {}) {
            this.successMessage.heading = message.heading || 'Saved.';
            this.successMessage.message = message.message || 'Your changes have been saved.';
            this.successMessage.navigateTo = message.navigateTo || null;
            this.successModalIsOpen = true;
        },
        closeSuccessModal() {
            this.successModalIsOpen = false;
            if (!this.successMessage.navigateTo) return;
            this.$router.push(this.successMessage.navigateTo);
        },
        closeWarningModal() {
            this.warningsModalIsOpen = false;
        },
        showWarningModal() {
            this.warningsModalIsOpen = true;
        },
        async save() {
            this.closeOnSave = false;
            this.saveInternal(false);
        },
        async saveAndClose() {
            this.closeOnSave = true;
            this.saveInternal(false);
        },
        async saveInternal(ignoreWarnings) {
            try {
                this.closeWarningModal();

                if (!this.internalValidationSet.success){
                    this.scrollToTop();
                    return;
                }

                await this.$store.dispatch('propertyDraft/saveCurrentPropertyDetail', ignoreWarnings);
                await this.saveQvProperty();
                await this.saveZoneInfo();
                if (this.validationSet.success) {
                    this.showSuccess({
                        heading: 'Property Update Completed',
                        message: 'The property has been updated.',
                        navigateTo:
                            (this.closeOnSave) ? { name: 'property-detail', params: { qpid: this.qpid }} : null,
                    });
                } else {
                    // if have any errors then scroll to the top
                    if(this.validationSet.errors && this.validationSet.errors.length > 0) {
                        this.scrollToTop();
                        return;
                    }
                    // otherwise assume must have warnings so show dialog
                    this.showWarningModal();
                }
            } catch (err) {
                this.handleException(err);
            }
        },
        async saveQvProperty(ignoreWarnings) {
            await this.$store.dispatch('qvProperty/saveQvProperty', ignoreWarnings)
        },
        async saveZoneInfo(ignoreWarnings) {
            await this.$store.dispatch('zoneInfo/saveZoneInfo', ignoreWarnings)
        },
        async cancel() {
            this.$router.push({name:'property-detail', params: {qpid: this.qpid}});
        },
    },
};
</script>
<!--
TODO Needs to be refactored - this is a temporary collection
of styles for a light touch "kind of like Monarch" view
-->
<style lang="scss" scoped src="../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped>

    .property-draft-section {
        border-bottom: .1rem dashed #e2e2e2;
        padding-top: 0;
        padding-bottom: 0.8rem;
        /* TODO need to standardise col container but allow for collapsing margins. */
        display: table;
        width: 100%;
    }

    .padding-edit-property-container {
        padding-left: 1rem;
    }

</style>

<template>
    <div class="contentWrapper resultsWrapper draftProperty">
        <!-- PROPERTY BANNER -->
        <!-- eslint-disable max-len -->
        <property-summary
            :property-id="propertyId"
            :can-navigate="false"
        />
        <div v-if="draftLoading">
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
        <div
            v-if="draftException"
            class="bAlert bAlert-danger exception-message"
        >
            Unexpected Error: {{ draftException }}
        </div>
        <div
            v-if="propertyLoaded && classificationsLoaded"
            class="col-container mdl-shadow--3dp"
            style="display: table; overflow: auto;"
        >
            <validation-header-message
                ref="validationHeader"
                :validation-set="validationSet"
                :show-errors="true"
                :show-warnings="true"
                :show-warnings-message="true"
                action="update the property"
            />
            <div class="container-fluid">
                <div class="row">
                    <div
                        class="col col-2"
                        style="padding:20px 15px;"
                    >
                        <property-info :qpid="qpid" />
                    </div>
                    <div class="col col-10 mdl-shadow--2dp" style="padding:20px 15px;">
                        <div class="section-wrapper" style="padding:15px">
                            <div class="container-fluid space-rows">
                                <div
                                    v-if="showApportionmentWarning"
                                    class="row"
                                >
                                    <p style="color: #ff0000">
                                        All apportionments are not a rural category. Please check data covers entire property.
                                    </p>
                                </div>
                                <div class="row">
                                    <h1 class="title">
                                        Edit Property
                                    </h1>
                                </div>
                                <div class="row">
                                    <div class="col col-2">
                                        <label>
                                            <span class="label">Date Entered</span>
                                            <input
                                                v-model="propertyDetail.propertyNotes.enteredDate"
                                                type="text"
                                            >
                                            <validation-message
                                                :validation-set="validationSet"
                                                field="propertyDetail.propertyNotes.enteredDate"
                                            />
                                        </label>
                                    </div>
                                    <div class="col col-3">
                                        <label>
                                            <span class="label">Reason for Update</span>
                                            <multi-select
                                                v-model="propertyDetail.propertyNotes.updateReason"
                                                :options="Object.values(reasonForUpdateOptions)"
                                                :close-on-select="true"
                                                select-label="⏎ select"
                                                deselect-label="⏎ remove"
                                            />
                                        </label>
                                    </div>
                                    <div class="col col-7">
                                        <label>
                                            <span class="label">Notes</span>
                                            <input
                                                v-model="propertyDetail.propertyNotes.text"
                                                type="text"
                                                maxlength="500"
                                            >
                                        </label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <expandable-section title="General Information">
                                            <general-information-readonly
                                                v-if="!isInternalUser"
                                                :property-detail="propertyDetail"
                                                :qivs-dvr-data="qivsDvrData"
                                            />
                                            <general-information
                                                v-else
                                                :property-detail="propertyDetail"
                                                :property-grouping-types="
                                                    (picklistValues &&
                                                        picklistValues.propertyGroupingTypes) ||
                                                        []
                                                "
                                                :qv-category-types="
                                                    (picklistValues &&
                                                        picklistValues.qvCategoryTypes) ||
                                                        []
                                                "
                                                :quality-rating-types="
                                                    (picklistValues &&
                                                        picklistValues.qualityRatingTypes) ||
                                                        []
                                                "
                                                :landscaping-quality-types="
                                                    (picklistValues &&
                                                        picklistValues.landscapingQualityTypes) ||
                                                        []
                                                "
                                                :qpid-details="farmedWithQpids"
                                                :qivs-dvr-data="qivsDvrData"
                                                @update="update"
                                                @showModal="showModal"
                                                @getDetailsForQpids="getDetailsForQpids"
                                            />
                                        </expandable-section>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <expandable-section title="Irrigation">
                                            <irrigation
                                                :irrigation-source-consents="irrigationSourceConsents"
                                                :irrigation-type-consents="irrigationTypeConsents"
                                                :rural-detail="propertyDetail.ruralDetail"
                                                :irrigation-type-options="
                                                    (picklistValues &&
                                                        picklistValues.irrigationType) ||
                                                        []
                                                "
                                                :irrigation-source-options="
                                                    (picklistValues &&
                                                        picklistValues.irrigationSourceTypes) ||
                                                        []
                                                "
                                                :water-storage-options="
                                                    (picklistValues &&
                                                        picklistValues.waterStorageTypes) ||
                                                        []
                                                "
                                                :water-quality-options="
                                                    (picklistValues &&
                                                        picklistValues.qualityRatingTypes) ||
                                                        []
                                                "
                                                :irrigation-linked-qpids="irrigationLinkedQpids"
                                                :irrigation-water-storage-qpids="irrigationWaterStorageQpids"
                                                @update="update"
                                                @showModal="showModal"
                                                @getDetailsForQpids="getDetailsForQpids"
                                                @change:linkedProperties="changedLinkedProperties"
                                            />
                                        </expandable-section>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <expandable-section title="Nutrient Management">
                                            <nutrient-management
                                                :nutrient-management-consents="nutrientManagementConsents"
                                                :nutrient-consent-qpids="nutrientConsentQpids"
                                                :quality-rating-types="
                                                    (picklistValues &&
                                                        picklistValues.qualityRatingTypes) ||
                                                        []
                                                "
                                                @update="update"
                                                @getDetailsForQpids="getDetailsForQpids"
                                                @change:linkedProperties="changedLinkedProperties"
                                            />
                                        </expandable-section>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <expandable-section
                                            title="Construction Information - Dwellings"
                                        >
                                            <template #buttons>
                                                <div v-if="hasQivsImprovements">
                                                    <i
                                                        v-if="hasUsefulQivsImprovements"
                                                        class="material-icons"
                                                        title="The QIVS Improvement Summary may contain useful construction information for this property."
                                                    >add_alert</i>
                                                    <li
                                                        class="md-qivs"
                                                        @click="openQivsImprovementSummary"
                                                    >
                                                        <label>IMPROVEMENT SUMMARY</label>
                                                        <i class="material-icons">call_made</i>
                                                    </li>
                                                </div>
                                            </template>

                                            <construction-other-dwellings
                                                :buildings="buildings"
                                                :other-dwelling-types="otherDwellingTypes"
                                                @update="update"
                                            />
                                        </expandable-section>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <expandable-section
                                            title="Construction Information - Other Buildings"
                                        >
                                            <construction-other-buildings
                                                :buildings="buildings"
                                                :other-dwelling-types="otherDwellingTypes"
                                                @update="update"
                                            />
                                        </expandable-section>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <expandable-section title="Other Improvements">
                                            <other-improvements
                                                :site-development="siteDevelopment"
                                                :other-improvements="otherImprovements"
                                                :show-section-header="false"
                                                other-improvement-category="RuralOtherImprovement"
                                                @update="update"
                                            />
                                        </expandable-section>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <expandable-section title="Fencing">
                                            <fencing
                                                :fences="fences"
                                                :fencing-types="
                                                    (picklistValues &&
                                                        picklistValues.fencingTypes) ||
                                                        []
                                                "
                                                :fencing-age-types="
                                                    (picklistValues &&
                                                        picklistValues.fencingAgeTypes) ||
                                                        []
                                                "
                                                :quality-types="
                                                    (picklistValues &&
                                                        picklistValues.featureQualityTypes) ||
                                                        []
                                                "
                                                @update="update"
                                            />
                                        </expandable-section>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <expandable-section title="Fruit">
                                            <fruits
                                                :fruits="fruits"
                                                :fruit-types="
                                                    picklistValues && picklistValues.fruitTypes
                                                "
                                                :fruit-age-types="
                                                    picklistValues && picklistValues.fruitAgeTypes
                                                "
                                                :fruit-variety-types="
                                                    picklistValues &&
                                                        picklistValues.fruitVarietyTypes
                                                "
                                                @update="update"
                                            />
                                        </expandable-section>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <div class="righty">
                                            <button
                                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                                :disabled="draftSaving"
                                                @click="cancel"
                                            >
                                                Cancel
                                            </button>
                                            <button
                                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                                :disabled="draftSaving"
                                                @click="save"
                                            >
                                                Save
                                            </button>
                                            <button
                                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                                :disabled="draftSaving"
                                                @click="saveAndClose"
                                            >
                                                Save and Close
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <alert-modal
            v-if="warningsModalIsOpen"
            warning
        >
            <h3>
                Do you want to proceed?
            </h3>
            <p>The following validation checks are failing:</p>
            <validation-header-message
                ref="warnings"
                :validation-set="validationSet"
                :show-errors="true"
                :show-warnings="true"
                message=""
            />
            <template #buttons>
                <div class="alertButtons">
                    <button
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="closeWarningModal()"
                    >
                        No, Return to editing
                    </button>
                    <button
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="saveChanges(true)"
                    >
                        Yes, Save changes
                    </button>
                </div>
            </template>
        </alert-modal>

        <alert-modal
            v-if="alertModalIsOpen"
            warning
            @close="closeAlertModal"
        >
            <h1>{{ alertMessage.heading }}</h1>
            <p>{{ alertMessage.message }}</p>

            <template #buttons>
                <div v-if="alertModalShowSaveButton" class="alertButtons">
                    <button
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="closeAlertModal"
                    >
                        No, Return to editing
                    </button>
                    <button
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="closeAlertModal(true)"
                    >
                        Yes, Save changes
                    </button>
                </div>
            </template>
        </alert-modal>

        <alert-modal
            v-if="successModalIsOpen"
            success
            @close="closeSuccessModal"
        >
            <h1>{{ successMessage.heading }}</h1>
            <p>{{ successMessage.message }}</p>
        </alert-modal>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import moment from 'moment';
import 'vue2-datepicker/index.css';
import { openQivsInNewTab } from '../../utils/QivsUtils';

export default {
    components: {
        'property-summary': () => import(/* webpackChunkName: "PropertySummary" */ '../property/PropertySummary.vue'),
        'property-info': () => import(/* webpackChunkName: "PropertyInfo" */ '../property/PropertyInfo.vue'),
        'expandable-section': () => import(/* webpackChunkName: "ExpanderContainer" */ './ExpandableSectionWrapper.vue'),
        'general-information': () => import(/* webpackChunkName: "GeneralInformation" */ './ruralPD/GeneralInformation.vue'),
        'general-information-readonly': () => import(/* webpackChunkName: "GeneralInformationReadonly" */ './ruralPD/GeneralInformationReadonly.vue'),
        fencing: () => import(/* webpackChunkName: "Fencing" */ './ruralPD/Fencing.vue'),
        fruits: () => import(/* webpackChunkName: "Fruits" */ './ruralPD/Fruits.vue'),
        irrigation: () => import(/* webpackChunkName: "NutrientManagement" */ './ruralPD/Irrigation.vue'),
        'nutrient-management': () => import(/* webpackChunkName: "NutrientManagement" */ './ruralPD/NutrientManagement.vue'),
        'other-improvements': () => import(/* webpackChunkName: "SiteImprovements" */ './SiteImprovements.vue'),
        'construction-other-buildings': () => import(/* webpackChunkName: "ConstructionOtherBuildings" */ './ruralPD/ConstructionOtherBuildings.vue'),
        'construction-other-dwellings': () => import(/* webpackChunkName: "ConstructionOtherDwellings" */ './ruralPD/ConstructionOtherDwellings.vue'),
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../common/modal/AlertModal.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "ValidationHeaderMessage" */ '../common/form/ValidationHeaderMessage.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../common/form/ValidationMessage.vue'),
        'multi-select': () => import('vue-multiselect'),
    },
    data() {
        return {
            taCode: '',
            alertModalIsOpen: false,
            alertModalShowSaveButton: false,
            alertMessage: {
                heading: '',
                message: '',
            },
            successModalIsOpen: false,
            successMessage: {
                heading: '',
                message: '',
                navigateTo: null,
            },
            warningsModalIsOpen: false,
            closeOnSave: false,
            reasonForUpdateOptions: {
                buildingConsent: 'Building Consent',
                salesInspection: 'Sales Inspection',
                other: 'Other',
            },
            otherDwellingTypes: [],
            linkedConsentPropertiesChanged: false,
        };
    },
    computed: {
        ...mapState('propertyDraft', {
            propertyDetail: 'propertyDetail',
            draftLoading: 'loading',
            draftSaving: 'saving',
            draftException: 'exception',
            validationSet: 'validationSet',
            formIsStale: 'formIsStale',
            picklistValues: 'picklistValues',
            apportionmentDetails: 'apportionmentDetails',
            farmedWithQpids: 'farmedWithQpids',
            nutrientConsentQpids: 'nutrientConsentQpids',
            irrigationLinkedQpids: 'irrigationLinkedQpids',
            irrigationWaterStorageQpids: 'irrigationWaterStorageQpids',
            alertError: 'alertError',
            qivsDvrData: 'qivsDvrData',
        }),
        ...mapState('userData', ['isInternalUser']),
        ...mapGetters(['classificationsLoaded', 'getCategoryClassifications']),
        propertyId() {
            return this.propertyLoaded ? this.propertyDetail.propertyId : null;
        },
        propertyLoaded() {
            return !this.draftLoading && this.propertyDetail;
        },
        qpid() {
            return this.propertyLoaded && this.propertyDetail ? this.propertyDetail.qpid : null;
        },
        fences() {
            if (
                this.propertyDetail &&
                this.propertyDetail.ruralDetail &&
                this.propertyDetail.ruralDetail.fences &&
                this.propertyDetail.ruralDetail.fences.length > 0
            ) {
                return this.propertyDetail.ruralDetail.fences;
            }
            return [{ fenceId: -1 }];
        },
        fruits() {
            if (
                this.propertyDetail &&
                this.propertyDetail.ruralDetail &&
                this.propertyDetail.ruralDetail.fruit &&
                this.propertyDetail.ruralDetail.fruit.length > 0
            ) {
                return this.propertyDetail.ruralDetail.fruit;
            }
            return [{ fruitId: -1 }];
        },
        nutrientManagementConsents() {
            if (
                this.propertyDetail &&
                this.propertyDetail.ruralDetail &&
                this.propertyDetail.ruralDetail.nutrientManagementConsents &&
                this.propertyDetail.ruralDetail.nutrientManagementConsents.length > 0
            ) {
                return this.propertyDetail.ruralDetail.nutrientManagementConsents;
            }
            return [{ consentId: -1, linkedProperties: [] }];
        },
        irrigationSourceConsents() {
            if (
                this.propertyDetail &&
                this.propertyDetail.ruralDetail &&
                this.propertyDetail.ruralDetail.irrigationSourceConsents &&
                this.propertyDetail.ruralDetail.irrigationSourceConsents.length > 0
            ) {
                return this.propertyDetail.ruralDetail.irrigationSourceConsents;
            }
            return [{ consentId: -1, linkedProperties: [] }];
        },
        irrigationTypeConsents() {
            if (
                this.propertyDetail &&
                this.propertyDetail.ruralDetail &&
                this.propertyDetail.ruralDetail.irrigationTypeConsents &&
                this.propertyDetail.ruralDetail.irrigationTypeConsents.length > 0
            ) {
                return this.propertyDetail.ruralDetail.irrigationTypeConsents;
            }
            return [{ consentId: -1 }];
        },
        otherImprovements() {
            if (
                this.propertyDetail
                && this.propertyDetail.otherImprovements
                && this.propertyDetail.otherImprovements.length > 0
            ) {
                return this.propertyDetail.otherImprovements;
            }
            return [{}];
        },
        siteDevelopment() {
            if (
                this.propertyDetail
                && this.propertyDetail.site
                && this.propertyDetail.site.siteDevelopment
            ) {
                return this.propertyDetail.site.siteDevelopment;
            }
            return { quality: null, description: null };
        },
        buildings() {
            if (
                this.propertyDetail
                && this.propertyDetail.buildings
                && this.propertyDetail.buildings.length > 0
            ) {
                return this.propertyDetail.buildings;
            }
            return [{}];
        },
        hasQivsImprovements() {
            return this.propertyDetail.qivsImprovementsStatus !== 'NO_IMPROVEMENTS';
        },
        hasUsefulQivsImprovements() {
            return this.propertyDetail.qivsImprovementsStatus === 'USEFUL_IMPROVEMENTS';
        },
        showApportionmentWarning() {
            const ruralCategories = 'ADFHPS';
            if (this.apportionmentDetails && this.apportionmentDetails.length > 0) {
                const findIndex = this.apportionmentDetails.findIndex(item => !ruralCategories.includes(item.categoryCode.charAt(0)));
                return findIndex > 0;
            }
            return false;
        },
    },
    watch: {
        alertError(newValue) {
            if (newValue) {
                this.showAlertModal(newValue.heading, newValue.message);
            }
        },
    },
    async mounted() {
        await this.loadPropertyDetail(this.$route.params.id);
        await this.getDvrDataFromQivs(this.qpid);
        await this.getPickListValues();
        await this.getApportionmentDetails(this.qpid);
        this.otherDwellingTypes = await this.getCategoryClassifications('RuralOtherDwellingTypes') || [];
    },
    methods: {
        getTodaysDate() {
            return moment(new Date()).format('DD/MM/YYYY');
        },

        setEnteredDate() {
            if (!this.propertyDetail.propertyNotes.enteredDate) {
                this.propertyDetail.propertyNotes.enteredDate = this.getTodaysDate();
            }
        },

        async loadPropertyDetail(propertyId) {
            if (this.draftLoading) return;

            try {
                await this.$store.dispatch('propertyDraft/editCurrentPropertyDetail', propertyId);
                this.setEnteredDate();
            } catch (err) {
                this.handleException(err);
            }
        },

        async getPickListValues() {
            try {
                await this.$store.dispatch('propertyDraft/getPickListValues');
            } catch (error) {
                this.handleException(error);
            }
        },

        async getApportionmentDetails(qpid) {
            try {
                await this.$store.dispatch('propertyDraft/getApportionmentDetails', qpid);
            } catch (error) {
                this.handleException(error);
            }
        },

        async getDetailsForQpids(params) {
            if (params) {
                try {
                    await this.$store.dispatch('propertyDraft/getDetailsForQpids', params);
                } catch (error) {
                    this.handleException(error);
                }
            }
        },

        async getDvrDataFromQivs(qpid) {
            if (qpid) {
                try {
                    await this.$store.dispatch('propertyDraft/getDvrDataFromQivs', qpid);
                } catch (error) {
                    this.handleException(error);
                }
            }
        },

        changedLinkedProperties() {
            this.linkedConsentPropertiesChanged = true;
        },

        update(data) {
            this.$store.commit('propertyDraft/setSinglePropertyDetail', data);
        },

        async cancel() {
            this.$router.push({ name: 'property-detail', params: { qpid: this.qpid } });
        },

        async saveAndClose() {
            this.closeOnSave = true;
            if (this.linkedConsentPropertiesChanged) {
                this.showAlertModal('Warning',
                    'The Consent and Water Storage details are being updated in all Linked Properties',
                    true);
                return;
            }
            await this.saveChanges();
        },

        async save() {
            if (this.linkedConsentPropertiesChanged) {
                this.showAlertModal('Warning',
                    'The Consent and Water Storage details are being updated in all Linked Properties',
                    true);
                return;
            }
            await this.saveChanges();
        },

        async saveChanges(ignoreWarnings = false) {
            try {
                this.closeWarningModal();
                await this.$store.dispatch('propertyDraft/saveCurrentPropertyDetail', ignoreWarnings);
                if (this.validationSet.success) {
                    this.navigateOnSuccess = false;
                    this.linkedConsentPropertiesChanged = false;
                    this.showSuccess();
                } else {
                    // if have any errors then scroll to the top
                    if (this.validationSet.errors && this.validationSet.errors.length > 0) {
                        this.scrollToTop();
                        return;
                    }
                    // otherwise assume must have warnings so show dialog
                    this.showWarningModal();
                }
            } catch (err) {
                this.handleException(err);
            }
        },

        handleException(err) {
            this.showAlertModal(
                'Unexpected Error',
                `An unexpected error occurred attempting to communicate with the server: ${err}`,
            );
        },

        scrollToTop() {
            this.$nextTick(() => {
                window.scrollTo({ top: this.$refs.validationHeader.$el.offsetTop, left: 0, behavior: 'smooth' });
            });
        },

        showSuccess(message = {}) {
            this.successMessage.heading = message.heading || 'Property Update Completed';
            this.successMessage.message = message.message || 'The property has been updated.';
            this.successMessage.navigateTo = this.closeOnSave ? { name: 'property-detail', params: { qpid: this.qpid } } : null;
            this.successModalIsOpen = true;
        },

        showWarningModal() {
            this.warningsModalIsOpen = true;
        },

        closeSuccessModal() {
            this.successModalIsOpen = false;
            if (this.successMessage.navigateTo) {
                this.$router.push(this.successMessage.navigateTo);
            }
        },

        closeWarningModal() {
            this.warningsModalIsOpen = false;
        },

        showModal(message) {
            if (message) {
                this.showAlertModal(message.heading, message.message);
            }
        },

        showAlertModal(heading, message, showSaveButton = false) {
            this.alertMessage = {
                heading,
                message,
            };
            this.alertModalShowSaveButton = showSaveButton;
            this.alertModalIsOpen = true;
        },

        closeAlertModal(saveDetails = false) {
            this.alertModalIsOpen = false;

            if (saveDetails === true) {
                this.saveChanges();
            }
        },

        openQivsImprovementSummary() {
            openQivsInNewTab(this.$store.getters['userData/qivsImprovementSummaryUrl'](this.qpid));
        },
    },
};
</script>

<style lang="scss" scoped src="../rollMaintenance/rollMaintenance.scss"></style>

<style lang="scss" scoped>
.container,
.container-fluid {
    .row {
        &.table-head {
            div {
                &[class*="col-"] {
                    padding: 5px;
                    color: #0e3a83;
                    font-size: 1.1rem;
                    border-bottom: 1px solid #2c3c61;
                }
            }
        }

        &.grey {
            .table-row {
                &:nth-child(even) {
                    background: #fafafa;
                }
                &:nth-child(odd) {
                    background: #f1f1f1;
                }

                &:last-child {
                    border-bottom: 1px solid #dfdfdf;
                }
            }
        }

        &.table-row {
            &:nth-child(even) {
                background: #f5fcff;
            }
            &:nth-child(odd) {
                background: #e8f8ff;
            }

            &:last-child {
                border-bottom: 1px solid #b6e9ff;
            }

            &.summary {
                div {
                    &[class*="col-"] {
                    }
                }

                &.bold {
                    div {
                        &[class*="col-"] {
                            font-weight: bold;
                        }
                    }
                }
            }

            div {
                &[class*="col-"] {
                    padding: 5px;
                }
            }

            .material-icons {
                line-height: 39px;
            }
        }

        > div {
            &[class*="col-"] {
                &.space-rows {
                    > .row {
                        margin-top: 5px;
                        margin-bottom: 10px;
                    }
                }
            }
        }
    }

    &.space-rows {
        > .row {
            margin-top: 10px;
            margin-bottom: 10px;
        }
    }
}
</style>

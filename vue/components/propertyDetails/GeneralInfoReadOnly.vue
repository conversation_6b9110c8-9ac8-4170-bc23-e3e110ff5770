<template>
    <div>
         <div
            v-if="canEdit"
            class="property-draft-section col-container"
         >
            <div class="col-row">
                <div class="col col-1">
                <label v-if="canEdit">
                    <span class="label" data-cy="general-info-date-entered-label">Date Entered</span>
                     <input
                            type="text"
                            v-model="date"
                            @change="handleDateEnteredInput"
                            :class="validationClass(allValidationSet, 'propertyNotes.enteredDate')"
                            data-cy="general-info-date-entered"
                    >
                   <validation-message
                        :validation-set="allValidationSet"
                        field="propertyNotes.enteredDate"
                    />
                </label>
                <label v-if="!canEdit && propertyDetail.propertyNotes !== null && propertyDetail.propertyNotes.enteredDate">
                     <span class="label" data-cy="general-info-date-entered-label">Date Entered</span>
                     <span>{{propertyDetail.propertyNotes.enteredDate}}</span>
                </label>
                </div>
                <div class="col col-2">
                <label v-if="canEdit">
                    <span class="label">Reason for Update</span>
                    <multiselect
                                v-model="selectedUpdateReason"
                                :options="Object.values(reasonForUpdateOptions)"
                                :close-on-select="true"
                                select-label="⏎ select"
                                deselect-label="⏎ remove"
                                :searchable="false"
                                @input="setSelected('updateReason',$event)"/>
                </label>
                <label v-if="!canEdit && propertyDetail.propertyNotes !== null && propertyDetail.propertyNotes.updateReason">
                        <span class="label">Reason for Update</span>
                        <span>{{propertyDetail.propertyNotes.updateReason}}</span>
                </label>
                </div>
                <div class="col col-5">
                <label v-if="canEdit">
                    <span class="label">Notes</span>
                     <input
                        type="text"
                        v-model="propertyDetail.propertyNotes.text"
                        :maxlength="max"
                        @change="updateText('text', propertyDetail.propertyNotes.text)"
                     >
                </label>
                <label v-if="!canEdit && propertyDetail.propertyNotes !== null && propertyDetail.propertyNotes.text">
                     <span class="label">Notes</span>
                      <span class="text">{{propertyDetail.propertyNotes.text}}</span>
                </label>
                </div>
            </div>
         </div>
        <general-info-section-commercial
            v-if="isCommercial && canEdit"
            :zone-info="zoneInfo"
            :validation-set="validationSet"
            :internal-validation-set="internalValidationSet"
            @validate-errors="validateErrors"
        />
        <h3 class="section-title" data-cy="general-info-property-info-title">
            Property Information
        </h3>
        <div class="property-draft-section col-container">
            <div v-if="isCommercial && !canEdit" class="col-row">
                <div class="col col-4">
                    <label data-cy="general-info-qv-category-title">
                        <span class="label">QV Category</span>
                        <classification-lookup v-if="propertyDetail.qvCategory"
                            category="Category_DVR"
                            :value="propertyDetail.qvCategory"
                            :label-function="(opt) => opt ? `${opt.code} — ${opt.description}` : '—'"
                        />
                        <classification-lookup v-else
                            category="Category_DVR"
                            :value="propertyDetail.category"
                            :label-function="(opt) => opt ? `${opt.code} — ${opt.description}` : '—'"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label data-cy="general-info-grouping-label">
                        <span class="label">Grouping</span>
                        {{ grouping | emptyToDash }}
                    </label>
                </div>
                <div class="col col-6">
                    <label data-cy="general-info-proposed-zone-label">
                        <span class="label">Proposed Zone</span>
                        {{ proposedZoneCode | emptyToDash }}
                    </label>
                </div>
            </div>
            <div v-if="isCommercial && !canEdit" class="col-row">
                <div class="col col-2">
                    <label data-cy="general-info-actual-earthquake-rating-label">
                        <span class="label">Actual Earthquake Rating</span>
                        {{ actualEarthquakeRating | emptyToDash }}

                    </label>
                </div>
                <div class="col col-2">
                    <label data-cy="general-info-earthquake-rating-range-label">
                        <span class="label">Earthquake Rating Range</span>
                        {{ earthquakeRatingRange | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label data-cy="general-info-earthquake-rating-assessor-label">
                        <span class="label">Earthquake Rating Assessor</span>
                        {{ earthquakeRatingAssessor | emptyToDash }}
                    </label>
                </div>
                <div class="col col-2">
                    <label data-cy="general-info-remedy-deadline-label">
                        <span class="label">Remedy Deadline</span>
                        {{ remedyYear | emptyToDash }}
                    </label>
                </div>
                <div class="col col-4">
                    <label data-cy="general-info-qv-liquefaction-label">
                        <span class="label">Liquefaction (TC Rating)</span>
                        {{ liquefaction | emptyToDash }}
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div data-cy="readonly-category" class="col col-4">
                    <label>
                        <span class="label">Category</span>
                        <classification-lookup
                            category="Category_DVR"
                            :value="propertyDetail.category"
                            :label-function="(opt) => opt ? `${opt.code} — ${opt.description}` : '—'"
                        />
                    </label>
                </div>
                <div data-cy="readonly-nature-of-improvement" class="col col-5">
                    <label>
                        <span class="label">Nature of Improvements</span>
                        <!-- Nature of improvement structure is different -->
                        <classification-lookup
                            category="NatureOfImprovements_DVR"
                            :value="propertyDetail.natureOfImprovements.map(noi => noi.improvement)"
                            :label-function="(opt) => {
                                const noi = propertyDetail.natureOfImprovements.find(
                                    noi => noi.improvement.code === opt.code
                                );
                                const quantityText = noi != null && noi.quantity > 1
                                    ? ' (' + noi.quantity + ')'
                                    : '';
                                return noi != null
                                    ? `${noi.improvement.description || ''}${quantityText}`
                                    : '';
                            }"
                            :multiple="true"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">Property Name</span>
                        {{ propertyDetail.propertyName | emptyToDash }}
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div data-cy="readonly-land-use" class="col col-4">
                    <label>
                        <span class="label">Land Use</span>
                        <classification-lookup
                            category="LandUse_DVR"
                            :value="propertyDetail.landUse.landUse"
                        />
                    </label>
                </div>
                <div data-cy="readonly-ta-land-zone" class="col col-1">
                    <label>
                        <span class="label">TA Land Zone</span>
                        <span>{{ propertyDetail.landUse.landZone | code }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label v-if="canEdit">
                        <span class="label">Effective Land Area, ha</span>
                        <input
                            id="effectiveLandArea"
                            type="number"
                            min="0"
                            step="0.0001"
                            :class="validationClass(allValidationSet, 'site.effectiveLandArea')"
                            @change="handleEffectiveLandAreaInput($event)"
                            data-cy="general-info-effective-land-area-input"
                            :value="propertyDetail.site.effectiveLandArea"
                        >
                        <validation-message
                            :validation-set="allValidationSet"
                            field="site.effectiveLandArea"
                        />
                    </label>
                    <label v-else>
                        <span class="label">Effective Land Area, ha</span>
                        {{ effectiveLandArea | hectares('—') }}
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Land Area, ha</span>
                        {{ landArea | hectares('—') }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Māori Land</span>
                        <span>{{ propertyDetail.landUse.isMaoriLand | yesno('—') }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Plan ID</span>
                        {{ propertyDetail.planNumber | emptyToDash }}
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Production</span>
                        {{ propertyDetail.landUse.production | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>
        <h3 class="section-title" data-cy="general-info-location-details-title">
            Location Details
        </h3>
        <div class="property-draft-section col-container">
            <div class="col-row">
                <div data-cy="readonly-lot-position" class="col col-1">
                    <label>
                        <span class="label">Lot position</span>
                        <classification-lookup
                            category="LotPosition_DVR"
                            :value="propertyDetail.site.lotPosition"
                        />
                    </label>
                </div>
                <div data-cy="readonly-contour" class="col col-2">
                    <label>
                        <span class="label">Contour</span>
                        <classification-lookup
                            category="Contour_DVR"
                            :value="propertyDetail.site.contour"
                        />
                    </label>
                </div>
                <div data-cy="readonly-view" class="col col-3">
                    <label>
                        <span class="label">View</span>
                        <classification-lookup
                            category="View_DVR"
                            :value="propertyDetail.site.view"
                        />
                    </label>
                </div>
                <div data-cy="readonly-view-scope" class="col col-2">
                    <label>
                        <span class="label">View Scope</span>
                        <classification-lookup
                            category="ViewScope_DVR"
                            :value="propertyDetail.site.viewScope"
                        />
                    </label>
                </div>
                <div data-cy="readonly-csi" class="col col-3">
                    <label>
                        <span class="label">Class of Surrounding Improvements (CSI)</span>
                        <classification-lookup
                            category="ClassOfSurroundingImprovements_DVR"
                            :value="propertyDetail.site.classOfSurroundingImprovements"
                        />
                    </label>
                </div>
                <div data-cy="readonly-outlier" class="col col-1">
                    <label>
                        <span class="label">Outlier</span>
                        <span>
                            {{ propertyDetail.isOutlier | yesno('—') }}
                        </span>
                    </label>
                </div>
            </div>
        </div>
        <h3 class="section-title" data-cy="general-info-property-summary-title">
            Property Summary
        </h3>
        <div class="property-draft-section col-container">
            <div class="col-row">
                <div data-cy="readonly-house-type" class="col col-3">
                    <label>
                        <span class="label">House Type</span>
                        <classification-lookup
                            category="HouseType_DVR"
                            :value="propertyDetail.summary.houseType"
                        />
                    </label>
                </div>
                <div data-cy="readonly-unit-of-use" class="col col-1">
                    <label>
                        <span class="label">Units of Use</span>
                        {{ propertyDetail.summary.units | emptyToDash }}
                    </label>
                </div>
                <div data-cy="readonly-age" class="col col-2">
                    <label>
                        <span class="label">Age</span>
                        <classification-lookup
                            category="Age_DVR"
                            :value="propertyDetail.summary.age"
                        />
                    </label>
                </div>
                <div data-cy="readonly-effective-year-built" class="col col-2">
                    <label>
                        <span class="label">Effective Year Built</span>
                        {{ propertyDetail.summary.effectiveYearBuilt | emptyToDash }}
                    </label>
                </div>
                <div data-cy="readonly-poor-fnd" class="col col-1">
                    <label>
                        <span class="label">Poor Fdn.</span>
                        {{ propertyDetail.summary.hasPoorFoundations | yesno('—') }}
                    </label>
                </div>
                <div data-cy="readonly-total-bedrms" class="col col-1">
                    <label>
                        <span class="label">Total Bedrms</span>
                        {{ propertyDetail.summary.totalBedrooms | emptyToDash }}
                    </label>
                </div>
                <div data-cy="readonly-total-bathrms" class="col col-1">
                    <label v-if="canEdit">
                        <span class="label">Total Bathrms</span>
                        <input
                            id="totalBathrooms"
                            type="number"
                            min="0"
                            step="1"
                            :value="propertyDetail.summary.totalBathrooms"
                            :class="validationClass(validationSet, 'summary.totalBathrooms')"
                            @input="updateNumber('totalBathrooms', $event)"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalBathrooms"
                        />
                    </label>
                    <label v-else>
                        <span class="label">Total Bathrms</span>
                        {{ propertyDetail.summary.totalBathrooms | emptyToDash }}
                    </label>
                </div>
                <div data-cy="readonly-total-toilets" class="col col-1">
                    <label>
                        <span class="label">Total Toilets</span>
                        {{ propertyDetail.summary.totalToilets | emptyToDash }}
                    </label>
                </div>
            </div>
            <div data-cy="readonly-building-site-cover" class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Building Site Cover, m<sup>2</sup></span>
                        {{ propertyDetail.summary.buildingSiteCover | emptyToDash }}
                    </label>
                </div>
                <div data-cy="readonly-total-floor-area" class="col col-2">
                    <label>
                        <span class="label">Total Floor Area, m<sup>2</sup></span>
                        {{ propertyDetail.summary.totalFloorArea | emptyToDash }}
                    </label>
                </div>
                <div data-cy="readonly-main-living-area" class="col col-2">
                    <label>
                        <span class="label">Main Living Area, m<sup>2</sup></span>
                        {{ propertyDetail.summary.mainLivingArea | emptyToDash }}
                    </label>
                </div>
                <div data-cy="readonly-total-living-area" class="col col-2">
                    <label>
                        <span class="label">Total Living Area, m<sup>2</sup></span>
                        {{ propertyDetail.summary.totalLivingArea | emptyToDash }}
                    </label>
                </div>
                <div data-cy="readonly-ldy-workshop" class="col col-1">
                    <label>
                        <span class="label">Ldy/Wkshp</span>
                        {{ propertyDetail.summary.hasLaundryOrWorkshop | yesno('—') }}
                    </label>
                </div>
                <div data-cy="readonly-car-access" class="col col-1">
                    <label>
                        <span class="label">Car Access</span>
                        <span>
                            {{ propertyDetail.site.hasCarAccess | yesno('—') }}
                        </span>
                    </label>
                </div>
                <div data-cy="readonly-driveway" class="col col-1">
                    <label>
                        <span class="label">Driveway</span>
                        <span>
                            {{ propertyDetail.site.hasDriveway | yesno('—') }}
                        </span>
                    </label>
                </div>
                <div data-cy="readonly-carparks" class="col col-1">
                    <label>
                        <span class="label">Carparks</span>
                        {{ propertyDetail.site.carparks | emptyToDash }}
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import commonUtils from '../../utils/CommonUtils';
import Multiselect from 'vue-multiselect';
import DateTimePicker from '../filters/DateTimePicker.vue';
import moment from 'moment';
import { DateTime } from 'luxon';


export default {
    components: {
        'classification-lookup': () => import(/* webpackChunkName: "ClassificationLookup" */ '../common/ClassificationLookup.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../common/form/ValidationMessage.vue'),
        'general-info-section-commercial': () => import(/* webpackChunkName: "GeneralInfoCommercial" */ './GeneralInfoCommercial.vue'),
        'input-text': () => import(/* webpackChunkName: "InputText" */ '../common/form/InputText.vue'),
        'input-number': () => import(/* webpackChunkName: "InputNumber" */ '../common/form/InputNumber2.vue'),
        Multiselect,
        DateTimePicker
    },
    mixins: [commonUtils],
    props: {
        propertyDetail: {
            type: Object,
            required: true,
        },
        qvPropertyDetails: {
            type: Object,
            required: false,
        },
        zoneInfo: {
            type: Object,
            required: false
        },
        canEdit: {
            type: Boolean,
            required: true,
        },
        validationSet: {
            type: Object,
            default: null,
        },
        internalValidationSet: {
            type: Object,
            default: null
        },
        isCommercial: {
            type: Boolean,
            required: false,
            default: false
        }
    },
    data() {
        return {
            date: this.propertyDetail.propertyNotes !== null && this.propertyDetail.propertyNotes.enteredDate ? this.propertyDetail.propertyNotes.enteredDate : this.setTodaysDate(),
            taCode: null,
            selectedUpdateReason: this.propertyDetail.propertyNotes !== null && this.propertyDetail.propertyNotes.updateReason ? this.propertyDetail.propertyNotes.updateReason : null,
             reasonForUpdateOptions: {
                buildingConsent: 'Building Consent',
                salesInspection: 'Sales Inspection',
                other :'Other',
            },
            max: 500,
            effectiveLandArea: this.propertyDetail.site.effectiveLandArea ? this.propertyDetail.site.effectiveLandArea.toString() : null,
            landArea: this.propertyDetail.site.landArea,
            maxLandAreaDp: 4
        };
    },
    computed: {
        ...mapState('property', ['property']),
        taLandZoneClassification() {
            return `TA_${this.taCode}_LandZone_DVR`;
        },
        effectiveLandAreaValue() {
            return parseFloat(this.effectiveLandArea) || this.$options.filters.hectares('—');
        },
        liquefaction() {
            return this.qvPropertyDetails?.hazards?.find(hazard => hazard.classification?.category === 'LiquefactionRating')?.classification.description;
        },
        earthquakeRating() {
            return this.qvPropertyDetails?.hazards?.find(hazard => hazard.classification?.category === 'EarthquakeRating');
        },
        actualEarthquakeRating() {
            return this.earthquakeRating?.rating;
        },
        earthquakeRatingRange() {
            return this.earthquakeRating?.classification?.description;
        },
        earthquakeRatingAssessor() {
            return this.earthquakeRating?.source?.description;
        },
        proposedZoneCode() {
            return this.zoneInfo?.proposedZone?.code;
        },
        remedyYear() {
            return this.qvPropertyDetails?.remedyYear;
        },
        grouping() {
            return this.propertyDetail.commercialDetail?.propertyGroupingTypeCommercial?.description;
        },
        enteredDateErrors() {
            const errors = [];

            const parsedDate = DateTime.fromFormat(this.date, 'dd/MM/yyyy');
            if (!parsedDate.isValid){
                errors.push('Entered Date must be in format dd/mm/yyyy.');
            }

            return errors;
        },
        effectiveLandAreaErrors() {
            const errors = [];

            if (this.landArea != null && this.effectiveLandArea != null && parseFloat(this.effectiveLandArea) > this.landArea){
                errors.push('Cannot be greater than Land Area');
            }

            if (this.effectiveLandArea != null){
                const [integerPart, decimalPart] = _.split(this.effectiveLandArea, '.');
                if (decimalPart && decimalPart.length > this.maxLandAreaDp){
                    errors.push('Must have a maximum of four decimal places');
                }
            }

            return errors;
        },
        allValidationSet() {
            return {
                success: (this.validationSet != null ? this.validationSet.success : true) && (this.internalValidationSet != null ? this.internalValidationSet.success : true),
                errors: [
                    ...(this.validationSet?.errors ?? []),
                    ...(this.internalValidationSet?.errors ?? [])
                ]
            }
        }
    },
    watch: {
        property(newVal) {
            if (newVal && newVal.territorialAuthority) {
                this.loadTAZoneClassification(newVal.territorialAuthority.code);
            }
        },
    },
    methods: {

        setTodaysDate(){
                    let now = new Date();
                    let formattedDate = moment(now).format("DD/MM/YYYY");
                    this.updateText('enteredDate', formattedDate);
                    return formattedDate;

        },
        setSelected(id, event){
            switch(event){
                case this.reasonForUpdateOptions.salesInspection:
                    this.selectedUpdateReason = this.reasonForUpdateOptions.salesInspection;
                    this.updateText(id, event);
                    break;
                case this.reasonForUpdateOptions.buildingConsent:
                    this.selectedUpdateReason = this.reasonForUpdateOptions.buildingConsent;
                    this.updateText(id, event);
                    break;
                case this.reasonForUpdateOptions.other:
                    this.selectedUpdateReason = this.reasonForUpdateOptions.other
                    this.updateText(id, event);
                    break;
            }
        },

        async loadTAZoneClassification(taCode) {
            await this.$store.dispatch('fetchTAZoneClassification', taCode);
            this.taCode = taCode;
        },
        updateNumber(id, event) {
            let value = null;
            if(event.srcElement.value && event.srcElement.value != '')
                value = parseFloat(event.srcElement.value);

            this.$emit('update', { id, value });
        },
        updateText(id, event) {
            let value = event;
            let notesUpdate = true;
            this.$emit('update', { id, value, notesUpdate });
        },
        handleDateEnteredInput() {
            this.updateText('enteredDate',this.date);

            const fieldName = 'propertyNotes.enteredDate';
            this.validateErrors({ fieldName, errors: this.enteredDateErrors });
        },
        handleEffectiveLandAreaInput(event) {
            this.updateNumber('effectiveLandArea', event);
            const fieldName = 'site.effectiveLandArea';
            this.validateErrors({ fieldName, errors: this.effectiveLandAreaErrors });
        },
        validateErrors(data) {
            this.$emit('validate-errors', data);
        }
    },
};
</script>

<style lang="scss" src='../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}

.label {
    display: block;
}

span.text {
    max-width: 100px;
    word-break: break-all;
    word-wrap:break-word;
    white-space: normal
}

</style>

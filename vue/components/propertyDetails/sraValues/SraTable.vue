<template>
  <editable-table :show-total-column="!isReadOnly" :show-total="false">
    <template #title>
      <div class="title">
        Special Rating Authority
      </div>
    </template>
    <template #columns>
      <div class="col-4">Special Rating Authority</div>
      <div class="col-1">Area</div>
      <div class="col-2">Capital Value</div>
      <div class="col-2">Land Value</div>
      <div class="col-2">Value of Improvements</div>
    </template>
    <div v-for="(srav, index) in sras" :key="index">
      <sra-row :srav="srav"
               :index="index"
               @row:add="addRow"
               @row:delete="deleteRow"/>
    </div>
  </editable-table>
</template>

<script>
import EditableTable from 'Common/tables/EditableTable.vue';
import EditableRow from 'Common/tables/EditableRow.vue';
import TableInput from 'Common/tables/TableInput.vue';
import SraRow from './SraRow.vue';
import {mapActions, mapState} from 'vuex';

export default {
  name: 'sra-table',
  components: {
    TableInput,
    EditableTable,
    EditableRow,
    SraRow,
  },
  data() {
    return {
      store: null,
    };
  },
  computed: {
    ...mapState('sra', ['sras', 'isReadOnly']),
    ...mapState('userData', [
      'isExternalUser',
    ]),
  },
  methods: {
    ...mapActions('sra', ['addSraRow', 'deleteSraRow']),
    addRow(index) {
      this.addSraRow(index);
    },
    deleteRow(index) {
      this.deleteSraRow(index);
    },
  },
  mounted() {
  },
};
</script>
.updateSra-td {
    input[type="number"] {
        font-size: 1.2rem;
        padding: 0.5rem;
        width: 100%;
        box-shadow: 0 0 0 1px #ccc;
        border: 1px #d2d2d2;
        height: 2.7rem;
    }
    .sra-description-select, .sra-output-code, .sra-reason-source {
        font-size: 1.2rem;
        padding: 0.5rem;
        border: solid 1px #d2d2d2;
        height: 2.8rem;
        background-color:#fff;
        margin: 0;
        width: 100%;
        appearance: auto;
    }
    textarea {
        font-size: 1.2rem;
        padding: 0.5rem;
        width: 100%;
        box-shadow: 0 0 0 1px #ccc;
        border: 1px #d2d2d2;
        height: auto;
    }
}

.sra-description-revised {
    padding-left: 1rem !important;
}

.updateSra-td.rfc-description {
    width: 10%;
}
.updateSra-td.rfc-output-code {
    width: 17%;
}

.errorSraRow {
    input[type="number"], .sra-description-select {
        border: solid 1px #f8d7da;
        box-shadow: none;
        color: #ff0000;
    }
}
.QVHV-buttons button.danger {
    color: rgb(255,255,255);
    background-color: #ff0000;
    margin-right: 1.5rem;
}
/* Quick n dirty general message */
.message {
    border: 1px solid transparent;
    border-radius: .25rem;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    margin-top: 0.5rem;
    font-size: 1.3rem;
}
.message-error {
    color: #721c24;
    border-color: #f5c6cb;
    background-color: #f8d7da;
}
.message-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}
<template>
  <div class="resultsWrapper router">
    <property-summary
        :property-id="qpid"
        :can-navigate="false"
    />
    <div class="masterDetails-Wrapper bootstrap-ify mdl-shadow--3dp">
      <div class="container-fluid" style="padding-top:1rem;">
        <div class="row">
          <div class="col-lg-2">
            <property-info-panel :qpid="qpid" />
          </div>
          <transition name="fade">
            <div key="1" v-if="loading" class="loading col-lg-10">
              <div class="spinner"></div>
            </div>
            <div key="2" v-else-if="error" class="error col-lg-10">
              <h1 class="error-message">
                {{ error }}
              </h1>
            </div>
            <div key="3" v-else class="col-lg-10">
              <div class="container">
                <div class="row">
                  <div class="col-lg-12">
                    <h1
                        class="title"
                        style="padding-top:10px;margin-bottom: 20px;"
                    >
                      Update SRA Values
                    </h1>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-12">
                    <sra-table/>
                  </div>
                </div>
                <div class="row">
                  <div class="col-lg-12">
                    <editable-table :show-total-column="!isReadOnly" :show-total="false">
                      <template #title>
                        <div class="title">
                          Total Assessment Values
                        </div>
                      </template>
                      <template #columns>
                        <div class="col-4"></div>
                        <div class="col-1">Area</div>
                        <div class="col-2">Capital Value</div>
                        <div class="col-2">Land Value</div>
                        <div class="col-2">Value of Improvements</div>
                      </template>
                      <editable-row :show-buttons="false" :show-total="false" :show-total-column="!isReadOnly">
                        <div class="col-4"><p class="h3 text-blue-600" style="font-weight: 600">Rating Values</p></div>
                        <div class="col-1">
                          <table-input type="number" min="0" :value="landArea" :disabled="true"/>
                        </div>
                        <div class="col-2">
                          <table-input
                              type="money"
                              min="0"
                              step="0.0001"
                              :value="capitalValue"
                              :disabled="true"
                          />
                        </div>
                        <div class="col-2">
                          <table-input
                              type="money"
                              min="0"
                              step="0.0001"
                              :value="landValue"
                              :disabled="true"
                          />
                        </div>
                        <div class="col-2">
                          <table-input
                              type="money"
                              min="0"
                              step="0.0001"
                              :value="improvementsValue"
                              :disabled="true"
                          />
                        </div>
                      </editable-row>
                      <editable-row v-if="hasRevisionValues" :show-buttons="false" :show-total="false" :show-total-column="!isReadOnly">
                        <div class="col-4"><p class="h3 text-blue-600" style="font-weight: 600">Revision Values</p>
                        </div>
                        <div class="col-1">
                        </div>
                        <div class="col-2">
                          <table-input
                              type="money"
                              min="0"
                              step="0.0001"
                              :value="revisionCapitalValue"
                              :disabled="true"
                          />
                        </div>
                        <div class="col-2">
                          <table-input
                              type="money"
                              min="0"
                              step="0.0001"
                              :value="revisionLandValue"
                              :disabled="true"
                          />
                        </div>
                        <div class="col-2">
                          <table-input
                              type="money"
                              min="0"
                              step="0.0001"
                              :value="revisionImprovementsValue"
                              :disabled="true"
                          />
                        </div>
                      </editable-row>
                    </editable-table>
                  </div>
                </div>
                <div class="row" v-if="!isReadOnly">
                  <div class="col-lg-12">
                    <editable-table :show-total-column="false">
                      <template #title>
                        <div class="title">
                          Reason For Change
                        </div>
                      </template>
                      <template #columns>
                        <div class="col-5">Output Code</div>
                        <div class="col-4">Source</div>
                        <div class="col-7">Reason For Change</div>
                      </template>
                      <editable-row :show-total-column="false">
                        <div class="col-5" style="height: 100%;">
                          <tooltip :text="reason.errors.outputId" :display-mode="'error'">
                            <table-select :options="classifications.output"
                                          :value.sync="reason.outputId"
                                          :description-key="'description'"
                                          :invalid="reason.errors.outputId"
                                          @update="() => reason.errors.outputId = null"/>
                          </tooltip>
                        </div>
                        <div class="col-4" style="height: 100%;">
                          <tooltip :text="reason.errors.sourceId" :display-mode="'error'">
                            <table-select :options="classifications.source"
                                          :value.sync="reason.sourceId"
                                          :description-key="'description'"
                                          :invalid="reason.errors.sourceId"
                                          @update="() => reason.errors.sourceId = null"/>
                          </tooltip>
                        </div>
                        <div class="col-7" style="height: 100%;">
                          <tooltip :text="reason.errors.reason" :display-mode="'error'">
                            <textarea type="text"
                                      placeholder="Enter reason..."
                                      v-model="reason.reason"
                                      :class="{ 'invalid': reason.errors.reason}"
                                      @change="() => reason.errors.reason = null"/>
                          </tooltip>
                        </div>
                      </editable-row>
                    </editable-table>
                  </div>
                </div>
                <div class="row" v-if="!isReadOnly">
                  <div class="col-lg-12 flex-row flex-end gap-1">
                    <input
                        type="button"
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                        value="Update Assessment"
                        @click="onUpdateAssessment"
                    />
                    <input
                        type="button"
                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                        value="Cancel Changes"
                        @click="cancelChanges"
                    />
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>
    <save-modal :errors="saveErrors"
                :warnings="saveWarnings"
                :saving="saving"
                :open="saveModalOpen"
                :type="saveModalType"
                @modal:toggle="toggleSaveModal"
                @modal:callback="() => saveCallback ? saveCallback() : true">
    </save-modal>
  </div>
</template>

<script>
import PropertyInfoPanel from '@/components/property/PropertyInfo.vue';
import PropertySummary from '@/components/property/PropertySummary.vue';
import SraTable from './SraTable.vue';
import {mapState, mapActions, mapGetters} from 'vuex';
import EditableTable from 'Common/tables/EditableTable.vue';
import EditableRow from 'Common/tables/EditableRow.vue';
import TableInput from 'Common/tables/TableInput.vue';
import TableSelect from 'Common/tables/TableSelect.vue';
import Tooltip from "Common/Tooltip.vue";
import SaveModal from 'Common/modal/SaveModal.vue';

export default {
  components: {
    SaveModal,
    EditableTable,
    EditableRow,
    TableInput,
    TableSelect,
    PropertyInfoPanel,
    PropertySummary,
    SraTable,
    Tooltip,
  },
  data() {
    return {
      qpid: null,
      saving: false,
      saveModalOpen: false,
      saveModalType: 'warning',
      saveErrors: [],
      saveWarnings: [],
      saveCallback: null,
    };
  },
  computed: {
    ...mapState('userData', [
      'isInternalUser',
      'isExternalUser',
      'userName',
    ]),
    ...mapState('sra', [
      'loading',
      'error',
      'capitalValue',
      'landValue',
      'landArea',
      'revisionCapitalValue',
      'revisionLandValue',
      'classifications',
      'reason',
      'isReadOnly',
      'valuesChanged'
    ]),
    ...mapGetters('sra', [
      'hasRevisionValues',
      'improvementsValue',
      'revisionImprovementsValue',
    ]),
  },
  methods: {
    ...mapActions('sra', ['loadSraProperty', 'saveSraProperty']),
    onUpdateAssessment() {
      this.reason.userName = `QVNZ-${this.userName}`;
      this.handleOwnersNotice().then(() => {
          return this.handleSave();
      });
    },
    async handleOwnersNotice() {
      return new Promise((resolve) => {
        if (this.reason.outputId === 7 && this.valuesChanged) {
          this.saveModalType = 'warning'
          this.saveWarnings = ['Values have changed, but selected Output Code will not produce an Owner\'s Notice. Is this Correct? Select Ok to confirm']
          this.saveModalOpen = true;
          this.saveCallback = resolve;
        } else {
            resolve();
        }
      });
    },
    async handleSave() {
      this.saving = true;
      this.saveModalType = 'saving';
      this.saveModalOpen = true;
      this.saveErrors = null;
      this.saveWarnings = null;
      await this.saveSraProperty(this.handleErrors);
    },
    handleErrors(errors) {
      if (errors) {
        this.saveModalType = 'error';
        this.saveErrors = errors;
      } else {
        this.saveModalType = 'success';
      }
      this.saving = false;
      this.saveModalOpen = true
    },
    toggleSaveModal() {
      this.saveModalOpen = false;
    },
    cancelChanges() {
      this.loadSraProperty(this.qpid)
    }
  },
  async beforeMount() {
    this.qpid = this.$route.params.qpid;
    await this.loadSraProperty(this.qpid);
  },
};
</script>

<style lang="scss" scoped>

textarea {
  font-size: 1.3rem;
  font-weight: 400;
  font-family: 'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif;
  line-height: 1.6;
  padding: 1rem;
  box-shadow: inset 0 0 0 1px #ccc;
  height: 8.5rem !important;
  width: 100%;
  border: .1rem solid #fff;
  border-radius: 5px;

  &.invalid {
    border-color: var(--color-red-400);
    border-width: 2px;
  }
}

.loading {
  height: 65vh;
}

.spinner::before {
  background-color: #fff;
}

.error-message {
  position: absolute;
  width: 100%;
  left: 0;
  top: 30vh;
  font-size: 1.25rem;
  color: var(--color-red-500);
  text-align: center;
}
</style>
<style lang="scss" src="../../rollMaintenance/ruralWorksheet/ruralWorksheet.scss"></style>

<template>
    <div>
        <editable-row
            :index="index"
            :show-total-column="!isReadOnly"
            :show-total="false"
            class="no-border-bottom"
            v-on="$listeners"
        >
            <div class="editable-row multi-row">
                <div class="editable-row-columns">
                    <div class="col-4">
                        <div>
                            <tooltip
                                :text="srav.errors.classId"
                                :border="true"
                            >
                                <table-select
                                    :options="sraClasses"
                                    :unique-selections="true"
                                    :value.sync="srav.classId"
                                    :description-key="'description'"
                                    :disabled="isReadOnly"
                                    @update:value="onSelectSraClass"
                                />
                            </tooltip>
                        </div>
                    </div>
                    <div class="col-1">
                        <tooltip
                            :text="srav.errors.area"
                            :border="true"
                        >
                            <table-input
                                type="area"
                                min="0"
                                :value.sync="srav.area"
                                :disabled="isReadOnly"
                                :select-on-focus="true"
                                @update:value="onChangeArea"
                            />
                        </tooltip>
                    </div>
                    <div class="col-2">
                        <tooltip
                            :text="srav.errors.capitalValue"
                            :border="true"
                        >
                            <table-input
                                type="money"
                                min="0"
                                step="0.0001"
                                :null-empty-values="true"
                                :value.sync="srav.capitalValue"
                                :disabled="isReadOnly"
                                :select-on-focus="true"
                                @update:value="onChangeCapitalValue"
                            />
                        </tooltip>
                    </div>
                    <div class="col-2">
                        <tooltip
                            :text="srav.errors.landValue"
                            :border="true"
                        >
                            <table-input
                                type="money"
                                min="0"
                                step="0.0001"
                                :null-empty-values="true"
                                :select-on-focus="true"
                                :value.sync="srav.landValue"
                                :disabled="isReadOnly"
                                @update:value="onChangeLandValue"
                            />
                        </tooltip>
                    </div>
                    <div class="col-2">
                        <table-input
                            type="money"
                            min="0"
                            step="0.0001"
                            :value="improvementsValue"
                            :disabled="true"
                        />
                    </div>
                </div>
                <div
                    v-if="hasRevisionValues"
                    class="editable-row-columns"
                >
                    <div class="col-4">
                        <div
                            class="text-blue-600"
                            style="font-weight: 600"
                        >
                            Revision Values
                        </div>
                    </div>
                    <div class="col-1" />
                    <div class="col-2">
                      <tooltip
                        :text="srav.errors.revision ? srav.errors.revision.capitalValue : null"
                        :border="true"
                      >
                        <table-input
                            type="money"
                            min="0"
                            step="0.0001"
                            :null-empty-values="true"
                            :value.sync="srav.revision.capitalValue"
                            :select-on-focus="true"
                            :disabled="isReadOnly"
                        />
                      </tooltip>
                    </div>
                    <div class="col-2">
                      <tooltip
                        :text="srav.errors.revision ? srav.errors.landValue : null"
                        :border="true"
                      >
                        <table-input
                            type="money"
                            min="0"
                            step="0.0001"
                            :null-empty-values="true"
                            :value.sync="srav.revision.landValue"
                            :select-on-focus="true"
                            :disabled="isReadOnly"
                        />
                      </tooltip>
                    </div>
                    <div class="col-2">
                        <table-input
                            type="money"
                            min="0"
                            step="0.0001"
                            :value="revisionImprovementsValue"
                            :disabled="true"
                        />
                    </div>
                </div>
            </div>
        </editable-row>
    </div>
</template>

<script>
import EditableRow from 'Common/tables/EditableRow.vue';
import TableInput from 'Common/tables/TableInput.vue';
import TableSelect from 'Common/tables/TableSelect.vue';
import Tooltip from 'Common/Tooltip.vue';
import { mapGetters, mapState, mapMutations } from 'vuex';

export default {
    name: 'SraRow',
    components: {
        EditableRow,
        TableSelect,
        TableInput,
        Tooltip,
    },
    computed: {
        ...mapState('sra', ['sraClasses', 'isReadOnly']),
        ...mapGetters('sra', ['hasRevisionValues']),
        improvementsValue() {
            if (!this.srav.capitalValue || !this.srav.landValue) {
              return null;
            }
            return this.srav.capitalValue - this.srav.landValue;
        },
        revisionImprovementsValue() {
            if (!this.srav.revision.capitalValue || !this.srav.landValue) {
              return null;
            }
            return this.srav.revision.capitalValue - this.srav.revision.landValue;
        },
    },
    mounted() {
    },
    methods: {
        ...mapMutations('sra', ['setValuesChanged']),
        getClass(id) {
            return this.sraClasses.find(cls => cls.id === id);
        },
        onSelectSraClass(id) {
            const cls = this.getClass(id);
            this.srav.ratingSystemId = cls.ratingSystemId;
            this.srav.ratingAuthority = cls.sra;
        },
        onValuesChanged() {
            this.setValuesChanged(true);
        },
        onChangeLandValue() {
            this.srav.errors.landValue = null;
            this.onValuesChanged();
        },
        onChangeCapitalValue() {
            this.srav.errors.capitalValue = null;
            this.onValuesChanged();
        },
        onChangeArea() {
            this.srav.errors.area = null;
        },
    },
    props: {
        srav: {
            type: Object,
            required: true,
        },
        index: {
            required: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.no-border-bottom {
  border-bottom: none;
}

.editable-row {
  min-height: unset !important;
  flex-direction: column;
}

.editable-row-columns {
  border: none !important;
  padding: 0 !important;
}

.multi-row {
  flex-direction: column !important;
  gap: 1rem !important;
}
</style>

<template>
    <div>
        <h3 class="section-title" data-cy="cy-site-improvements-title">
            Site Improvements
        </h3>
        <div class="property-draft-section col-container">
            <div class="col-row">
                <!-- Site Development -->
                <div class="col col-2">
                    <label>
                        <span class="label">Other Improvement</span>
                        <span>Site Development</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Quality</span>
                        <classification-lookup
                            category="FeatureQuality"
                            :value="siteDevelopment.quality"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Description</span>
                        <span>{{ siteDevelopment | description }}</span>
                    </label>
                </div>
            </div>
            <!-- For each other improvement -->
            <div
                v-for="(otherImprovement, index) in otherImprovements"
                :key="`otherImprovements${index}`"
                class="col-row"
            >
                <div class="col col-2">
                    <label>
                        <span class="label">Other Improvement</span>
                        <span>{{ otherImprovement.definition && otherImprovement.definition.description | emptyToDash }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Year Built</span>
                        <classification-lookup
                            category="FeatureAge"
                            :value="otherImprovement.age"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Area/Quantity</span>
                        <span>{{ otherImprovement.quantity | emptyToDash }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Unit of Measure</span>
                        <classification-lookup
                            category="UnitOfMeasure"
                            :value="otherImprovement.unitOfMeasure"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Quality</span>
                        <classification-lookup
                            category="FeatureQuality"
                            :value="otherImprovement.quality"
                        />
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span class="label">Other Improvement</span>
                        <span>{{ otherImprovement | description }}</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    components: {
        'classification-lookup': () => import(/* webpackChunkName: "ClassificationLookup" */ '../common/ClassificationLookup.vue'),
    },
    props: {
        siteDevelopment: {
            type: Object,
            default: () => {},
        },
        otherImprovements: {
            type: Array,
            default: () => [],
        },
    },
    computed: {
    },
};
</script>

<style lang="scss" src='../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}
.label {
    display: block;
}
</style>

<template>
    <div>
        <div class="uploaderHeader mdl-tabs mdl-js-tabs mdl-js-ripple-effect">
            <div class="mdl-tabs__tab-bar">
                <a href="#starks-panel" class="mdl-tabs__tab is-active">{{ headerTitle }}</a>
            </div>
            <span class="righty" href="#">
                <i title="Continue" id="multi-uploads-done" class="multi-uploads-done material-icons md-light md-36" @click="showPhotoViwer">&#xE154;</i>
                <i id="save-and-close" style="display: none;" class="save-and-close mdl-button blanc">Save &amp; Close</i>
            </span>
        </div>
        <div class="uploaderBody dragNdrop">
            <div action="no/presigned/url/assigned" class="dropzone" id="photo-dropzone"></div>
        </div>
        <photo-viewer class="photo-viewer" :property="property" :fromGallery="fromGallery" :fromHomeValuation="fromHomeValuation" style="display: none"></photo-viewer>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import PhotoViewer from '../media/PhotoViewer.vue';
    import commonUtils from '../../utils/CommonUtils';
    import * as EXIF from '../../exif.js';
    import * as Dropzone from '../../dropzone';

    export default {
        mixins: [commonUtils],
        components: {   
            PhotoViewer
        },
        data: function() {
            return {
                property: null,
                photosDropped: [],
                photosUploaded: [],
                fromGallery: false,
                fromHomeValuation: false,
                headerTitle: "UPLOAD PHOTOS"
            }
        },
        methods: {
            getURLParam: function(sParam){
                var sPageURL = window.location.search.substring(1);
                var sURLVariables = sPageURL.split('&');
                for (var i = 0; i < sURLVariables.length; i++){
                    var sParameterName = sURLVariables[i].split('=');
                    if (sParameterName[0] == sParam){
                        return sParameterName[1];
                    }
                }
            },
            getCapturedDate: function(exif) {
                //var dateString = new Date(Date.now()).toUTCString();
                var dateString = new Date();
                try{
                    console.log("dateString: " + dateString);
                    console.log("exif: " + exif + " exif.DateTimeOriginal: " + exif.DateTimeOriginal + " exif.DateTime: " + exif.DateTime);
                    if (exif && (exif.DateTimeOriginal || exif.DateTime)){
                        console.log("inside exif = OK");
                        var dateSplit = (exif.DateTimeOriginal || exif.DateTime).split(" ");
                        dateSplit[0] = dateSplit[0].split(":").join("-");
                        var date = new Date(dateSplit[0] + "T" + dateSplit[1]);
                        if(date != "Invalid Date"){
                            var today = new Date();
                            var timeinmilisec = today.getTime() - date.getTime();
                            var days = Math.ceil(timeinmilisec / (1000 * 60 * 60 * 24));
                            console.log( "days: " + days);
                            if (days < 365 && date <= today) {
                                //dateString = date.toUTCString(); //testing only
                                dateString = date;
                            }
                            console.log("dateString(valid): " + dateString);
                        }
                    }
                } catch(e){
                    //dateString = new Date(Date.now()).toUTCString();
                    dateString = new Date();
                    console.log("dateString(ex): " + dateString);
                }
                return dateString.setHours(13,0,0,0);
            },
            getPhotoIndexField: function(propertyName, value) {
                var self = this;
                for (var i = 0; i < self.photosDropped.length; i++)
                    if (self.photosDropped[i][propertyName] === value && self.photosDropped[i].uploaded === false) {
                        return i;
                    }
                return -1;
            },
            onDropzoneDone: function(success, file) {
                var self = this;
                var i = self.getPhotoIndexField("photoId", file.photoId);

                var mediaEntry = self.photosDropped[i].mediaEntry;

                mediaEntry.mediaItem.status = success ? "DELETED" : "UPLOAD_FAILED";
                self.saveMedia(mediaEntry, function() {
                    if(self.photosUploaded.length == self.photosDropped.length) {
                        $('#multi-uploads-done').removeClass('md-inactive');
                        $('.multi-uploads-done').addClass('active');
                        self.photosUploaded = self.photosUploaded.filter(function (obj) { return obj.mediaItem.status == "DELETED"; });
                        EventBus.$emit('uploaded-photos', self.photosUploaded);
                    }
                });
            },
            showPhotoViwer: function() {
                var self = this;
                $('.dragNdrop').hide();
                $('.save-and-close').show();
                $('.save-and-close').addClass('active');
                $('.multi-uploads-done').hide();
                $('.uploaderBody').hide();
                self.headerTitle = "MANAGE PHOTOS";
                $('.photo-viewer').show();

            },
            saveMedia: function(data, callback) {
                var self = this;
                var url = jsRoutes.controllers.MediaController.saveMedia().url;
                $.ajax({
                    type: "POST",
                    cache: false,
                    url:  url,
                    processData: false,
                    contentType: 'application/json',
                    data: JSON.stringify(data)
                }).done(function (response) {
                    self.photosUploaded.push(response);
                    if(callback) callback();
                });
            }
        },
        mounted: function() {
            var self = this;
            self.property = self.getURLParam('propertyId');
            self.fromGallery = self.getURLParam('photoId') != null;
            self.job = self.getURLParam('jobId');
            self.fromHomeValuation = self.getURLParam('jobId') != null;
            if(self.fromGallery || self.fromHomeValuation) {
                self.showPhotoViwer();
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.MediaController.getMediaByOwner(self.property ? self.property : self.job).url,
                    cache: false,
                    success: function (photoData) {
                        var photos = photoData;
                        if(self.fromHomeValuation) {
                            photos = photoData.filter(function( obj ) {
                                return obj.category == 'HomeValuationPhoto' && obj.tags.indexOf('selected') > -1;
                            });
                        }
                        EventBus.$emit('uploaded-photos', photos);
                    },
                    error: function (response) {
                        console.log("Error in showing photos");
                        self.errorHandler(response);
                    }
                });
            } else {
                Dropzone.autoDiscover = false;
                var dropzone = new Dropzone("#photo-dropzone",{
                    autoDiscover: false,
                    acceptedFiles: ".png,.jpg,.gif,.bmp,.jpeg",
                    autoProcessQueue: false,
                    method: 'PUT',
                    contentType: false,
                    uploadMultiple: false});

                dropzone.on("addedfile", function (file) {
                    $('#multi-uploads-done').addClass('md-inactive');
                    EXIF.getData(file, function() {
                        var dateString = self.getCapturedDate(EXIF.getAllTags(this));
                        var data = {
                            "ownerId":self.property,
                            "category":"Property",
                            "isPrimary":false,
                            "description":"",
                            "tags":[],
                            "mediaItem":{
                                "fileName": file.name,
                                "contentType": file.type,
                                "uploadedDate": new Date(Date.now()).toUTCString(),
                                "captureDate": dateString,
                                "tags":[],
                                "improvementDateRange":""
                            }
                        };
                        $.ajax({
                            type: "POST",
                            cache: false,
                            url:  jsRoutes.controllers.MediaController.generateMediaEntryID().url,
                            processData: false,
                            contentType: 'application/json',
                            data: JSON.stringify(data)
                        }).done(function (response) {
                            dropzone.options.url = response.mediaItem.imageUrl;
                            dropzone.options.headers = {'Content-Type': file.type};
                            self.photosDropped.push({
                                //photoId: response.id, //wil replace once ID is done in backend
                                photoId: response.mediaItem.id, //temporary ID -- will replace it later
                                fileName: response.mediaItem.fileName,
                                mediaEntry: response,
                                uploaded: false
                            });
                            //file.photoId = response.id;
                            file.photoId = response.mediaItem.id; //same with the photosDropped photoId
                            dropzone.processFile(file);
                        });
                    });
                });

                dropzone.on("success", function (file) {
                    self.onDropzoneDone(true, file);
                });

                dropzone.on("error", function (file) {
                    self.onDropzoneDone(false, file);
                });
            }
        }
    }
</script>
<style scoped>
    @import "/assets/stylesheets/desktop.css";
</style>

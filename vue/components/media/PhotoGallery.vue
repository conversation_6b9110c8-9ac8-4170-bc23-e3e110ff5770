<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="photoGallery_propSearch">
        <span class="thumbWrapper" v-for="photo in photos">
            <img class="photoGallery_thumb" v-bind:data-id=photo.id v-bind:data-property=photo.ownerId v-bind:src=photo.mediaItem.largeImageUrl>
        </span>
    </div>
</template>
<script>
    import commonUtils from '../../utils/CommonUtils';

    export default {
        props: ["property", "qupid"],
        mixins: [commonUtils],
        data: function() {
            return {
                photos: []
            }
        },
        mounted: function() {
            //Need to add onClick event
            var self = this;
            var media = jsRoutes.controllers.MediaController.getMediaByOwner(self.property);
            $.ajax({
                type: "GET",
                url: media.url,
                cache: false,
                success: function (response) {
                    console.log(response);
                    self.photos = response;
                    $("#results_" + self.qupid).removeClass("noThumbnails");
                },
                error: function (response) {
                    self.errorHandler(response);
                }
            });
        }
    }
</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="md-photoGallery carousel">
        <img v-for="it in photos" class="md-primary" v-bind:data-id="it.id" v-bind:data-property="it.ownerId" v-bind:src="it.mediaItem.largeImageUrl">
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import Slick from '../../slick.js';
    import commonUtils from '../../utils/CommonUtils';

    export default {
        props: ["property", "qpid"],
        mixins: [commonUtils],
        data: function() {
            return {
                photos: []
            }
        },
        methods: {
            getPhotos: function() {
                var self = this;
                var media = jsRoutes.controllers.MediaController.getMediaByOwner(self.property);
                $.ajax({
                    type: "GET",
                    url: media.url,
                    cache: false,
                    success: function (response) {
                        self.photos = response;
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            },
            registerCarousel: function() {
                $('.carousel').slick('unslick');
                $(".carousel").not('.slick-initialized').slick({
                    dots: true,
                    infinite: false,
                    speed: 300,
                    slidesToShow: 2,
                    slidesToScroll: 2,
                    swipe: false
                });
            }
        },
        mounted: function() {
            var self = this;
            self.getPhotos();
            EventBus.$on('refresh-carousel-' + self.property, function(event) {
                self.getPhotos();
            });
        },
        updated: function() {
            var self = this;
            self.registerCarousel();
        }
    }
</script>

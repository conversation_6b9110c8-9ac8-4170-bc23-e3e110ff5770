<template>
    <div class="photoView disabled">
        <div class="propertyPhoto">
            <div class="propertyPhoto-wrapper">
                <span><img class="image" :src="selectedPhoto.mediaItem.mediumImageUrl" @error="imgError"></span>
            </div>
        </div>

        <div class="imageDetails detailsForm">
                <div class="detailsTitle">
                    <i class="material-icons md-dark">&#xE3C9;</i>
                    <div class="edit" id="div_1"></div>
            </div>

            <div class="detailsDate">
                <i class="material-icons md-dark">&#xE8DF;</i>
                <div class="datePicker">
                    <label>Date Taken</label>
                    <input class="datepicker" type="text" placeholder="Click to add a date">
                </div>
            </div>
            <div data-cy="primary-photos" class="detailsPrimary">
                <input type="checkbox" id="Primary Photo">
                <label for="Primary Photo"><span>Primary photo</span>Use this photo as the main image for this property</label>
            </div>
            <div data-cy="internal-use-only" class="detailsInternalOnly">
                <input type="checkbox" id="Internal Use Only">
                <label for="Internal Use Only"><span>Internal Use Only</span>Restrict to QV internal systems</label>
            </div>
            <div data-cy="include-in-insurance" class="detailsInsurance">
                <input type="checkbox" id="Insurance Photo">
                <label for="Insurance Photo"><span>Include in Insurance</span></label>
            </div>

            <div class="detailsTags">
                <i class="material-icons md-dark">&#xE54E;</i>
                <label>Select Tags for this Photo</label>
                <div data-cy="tag-picker" class="tagPicker">
                    <div class="tagRow">
                        <span class="tagSelect">
                            <select class="improvementDate" v-model="selectedPhoto.mediaItem.improvementDateRange" @change="validateAndSavePhoto(selectedPhoto)">
                                <!-- Default option on first upload as improvementDateRange will be null -->
                                <option :value="null" disabled hidden>Date</option>
                                <!-- Option for unsetting improvement date range. Value is set to empty string -->
                                <option value>Date</option>

                                <option v-for="improvementDateRange in getCategoryClassifications('ImprovementDateRange')"
                                        :value="improvementDateRange.code"
                                        :key="improvementDateRange.code">
                                    {{improvementDateRange.description}}
                                </option>
                            </select>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="dwelling" code="10">
                            <label for="dwelling">{{ tagsDescriptions['10'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="flat" code="20">
                            <label for="flat">{{ tagsDescriptions['20'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="garage" code="80">
                            <label for="garage">{{ tagsDescriptions['80'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="sleepout" code="90">
                            <label for="sleepout">{{ tagsDescriptions['90'] }}</label>
                        </span>
                    </div>

                    <div class="tagRow">
                        <span class="tag">
                            <input type="checkbox" id="from street" code="130">
                            <label for="from street">{{ tagsDescriptions['130'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="front" code="110">
                            <label for="front">{{ tagsDescriptions['110'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="rear" code="120">
                            <label for="rear">{{ tagsDescriptions['120'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="exterior" code="150">
                            <label for="exterior">{{ tagsDescriptions['150'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="interior" code="160">
                            <label for="interior">{{ tagsDescriptions['160'] }}</label>
                        </span>
                    </div>

                    <div class="tagRow">
                        <span class="tag">
                            <input type="checkbox" id="kitchen" code="70">
                            <label for="kitchen">{{ tagsDescriptions['70'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="bathroom" code="30">
                            <label for="bathroom">{{ tagsDescriptions['30'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="ensuite" code="40">
                            <label for="ensuite">{{ tagsDescriptions['40'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="laundry" code="170">
                            <label for="laundry">{{ tagsDescriptions['170'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="bedroom" code="50">
                            <label for="bedroom">{{ tagsDescriptions['50'] }}</label>
                        </span>
                    </div>

                    <div class="tagRow">
                        <span class="tag">
                            <input type="checkbox" id="living" code="60">
                            <label for="living">{{ tagsDescriptions['60'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="view" code="100">
                            <label for="view">{{ tagsDescriptions['100'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="yard" code="140">
                            <label for="yard">{{ tagsDescriptions['140'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="deck" code="180">
                            <label for="deck">{{ tagsDescriptions['180'] }}</label>
                        </span>
                        <span class="tag">
                            <input type="checkbox" id="shed" code="190">
                            <label for="shed">{{ tagsDescriptions['190'] }}</label>
                        </span>
                    </div>
                </div>
            </div>
            <div class="detailsWarning warning-dismissible fade" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close" @click="hideWarning"><span aria-hidden="true">&times;</span></button>
                <strong>Invalid Photo:</strong> A valid Photo requires a title or tag.
            </div>
        </div>
        <div class="photoControls">
            <i class="photoButton material-icons photoDelete" title="Delete Photo">&#xE92B;</i>
            <i class="photoButton material-icons photoRelink" title="Relink Photo">&#xE157;</i>
            <a href="#" target="_blank" class="photoDownloadLink"><i class="photoButton material-icons" title="Original Photo">&#xE2C0;</i></a>
            <span class="setPrimary-label" style="display:none;">Primary Photo Not Set</span>
            <span class="photoPagination"></span>
            <i title="Previous" class="photoButton material-icons photoBack">&#xE5C4;</i>
            <i title="Next" class="photoButton material-icons photoForward">&#xE5C8;</i>
        </div>
        <confirmation
            id="confirmationPhotoViewer"
            :okLabel="confirm.okLabel"
            :cancelLabel="confirm.cancelLabel"
            :header="confirm.header"
            :message="confirm.message"
            :okHandler="confirm.okHandler">
        </confirmation>
        <Warning :header="warning.header" :message="warning.message" close="OK">
        </Warning>
        <Relink :thumbnail="link.thumbnail" :okHandler="link.okHandler" close="OK">
        </Relink>
    </div>
</template>
<script>
    import { mapState, mapGetters } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore.js';
    import editable from '../../jeditable.js';
    import Confirmation from '../common/Confirmation.vue';
    import Warning from '../common/Warning.vue';
    import Relink from '../common/Relink.vue';
    import commonUtils from '../../utils/CommonUtils';
    import moment from 'moment';
    const Picker = import('../../libs/picker');
    const pickadate = import('../../libs/picker.date.js');
    const pickatime = import('../../libs/picker.time.js');

    export default {
        mixins: [commonUtils],
    components: {
          Confirmation,
          Warning,
          Relink
        },
        props: ["fromGallery", "property", "fromHomeValuation"],
        data: function() {
            return {
                selectedPhoto: {
                    mediaItem: {
                        largeImageUrl: "",
                        smallImageUrl: ""
                    }
                },
                warning: {
                    header: "",
                    message: ""
                },
                confirm: {
                    header: "",
                    message: "",
                    okLabel: "",
                    cancelLabel: "",
                    okHandler: ""
                },
                link: {
                    thumbnail: "",
                    okHandler: ""
                },
                hasPrimary: true
            }
        },
        computed: {
            ...mapState('userData', [
                'isReadOnlyUser',
            ]),

            ...mapGetters(['getCategoryClassifications']),

            tagsDescriptions() {
                const tagsClassifications = this.getCategoryClassifications('Tags');
                // Convert the array of Tags Classifications into a key value pair
                return tagsClassifications.reduce((accumulator, currentValue) => {
                    // Key value pair of classification code-description
                    return { ...accumulator, [currentValue.code]: currentValue.description };
                }, {});
            },
            isReadOnly: function () {
                return this.isReadOnlyUser;
            }
        },
        methods: {
            getURLParam: function(sParam){
                var sPageURL = window.location.search.substring(1);
                var sURLVariables = sPageURL.split('&');
                for (var i = 0; i < sURLVariables.length; i++){
                    var sParameterName = sURLVariables[i].split('=');
                    if (sParameterName[0] == sParam){
                        return sParameterName[1];
                    }
                }
            },
            initBubbles: function() {
                var self = this;
                var bubbles = "";
                if(!self.hasPrimary) {
                    $('.setPrimary-label').show();
                }
                $.each(self.photos, function(i, element) {
                    var primary = element.isPrimary ? 'primary' : '';
                    var completed = (element.mediaItem.tags.length > 0 || (element.description != null && element.description.trim().length != 0)) ? 'completed' : 'invalid';
                        bubbles += "<span id=\"bubble_" + element.id + "\" data-id=\"" + element.id + "\" class=\"bubbleSpan " + completed + " " + primary + "\"></span>";
                });
                $('.photoPagination').html(bubbles);
                self.initBubbleClick();
            },
            initBubbleClick: function() {
                var self = this;
                $(document).ready(function () {
                    $('.bubbleSpan').off("click").click(function (evt) {
                        self.waitBeforeClick($(this).data('id'));
                    });
                    if (self.fromGallery == true) {
                        console.log("initBubbleClick (fromGallery)" + self.selectedPhoto.id);
                        $("#bubble_" + self.selectedPhoto.id).click();
                    } else {
                        console.log("initBubbleClick - first bubbleSpan)" + self.selectedPhoto.id);
                        $(".bubbleSpan").first().click();
                    }
                });

            },
            filterById: function(id) {
                var self = this;
                var data = self.photos;
                return data.filter(function(data){ return data.id == id })[0];
            },
            updatePhotos: function(photo) {
                var self = this;
                for (var i=0; i<self.photos.length; i++) {
                    if (self.photos[i].id == photo.id) {
                        self.photos[i] = photo;
                        break;
                    }
                }
            },
            initDescription: function(bubble) {
                var self = this;
                $('.edit').unbind('click.editable')
                    .unbind($('.edit').data('event.editable'))
                    .removeData('disabled.editable')
                    .removeData('event.editable');

                $('.edit').editable(function(value, settings) {
                        let sanitizedText = self.sanitiseText(value);
                        if(self.selectedPhoto) {
                            var tempPhoto = self.selectedPhoto;
                            tempPhoto.description = sanitizedText;

                            if (self.validateAndSavePhoto(tempPhoto)) {
                                bubble.addClass('completed');
                                bubble.removeClass('invalid');
                            } else {
                                bubble.addClass('invalid');
                                bubble.removeClass('completed');
                            }
                        }
                        return sanitizedText;

                }, {
                    indicator: 'Saving...',
                    onblur: 'submit',
                    maxlength: 255
                });
                if (self.selectedPhoto.description != null && self.selectedPhoto.description.trim().length != 0) {
                    $('.edit').text(self.selectedPhoto.description);
                } else {
                    $('.edit').text("Click to add a title");
                }
            },
            sanitiseText: function(value) {
                const map = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "/": '&#x2F;',
                    "`": '&grave;'
                };
                const reg = /[&<>"'/]/ig;
                return value.replace(reg, (match)=>(map[match]));
            },

            initDate: function() {
                var self = this;
                var maxPickdateAttempts = 10;
                var count = 0;
                var interval = setInterval(function () {
                    if(typeof $().pickadate === 'undefined' && count < maxPickdateAttempts){
                        console.log("pickadate not loaded yet");
                        count++;
                        return;
                    }
                    clearInterval(interval);
                    var $input = $('.datepicker').pickadate({
                        selectYears: true,
                        selectMonths: true,
                        today: '',
                        clear: '',
                        close: '',
                        max: 'today',
                        format: 'dd/mm/yyyy'
                    });
                    var picker = $input.pickadate('picker');
                    picker.off('set');
                    picker.on('set', function(context) {
                        if(context.select != null && self.selectedPhoto) {
                            var tempPhoto = self.selectedPhoto;

                            //context.select is selected date -1; so below will add 1 day
                            var tmpDate = new Date(context.select);
                            //tmpDate.setDate(tmpDate.getDate() + 1);

                            tmpDate.setHours(13,0,0,0); //set  hours to 13, min Secs and milliSecs as well (to make it ZDT 12am)

                            //tempPhoto.mediaItem.captureDate = context.select;
                            tempPhoto.mediaItem.captureDate = tmpDate.getTime();
                            console.log("tempPhoto.mediaItem.captureDate: " + tempPhoto.mediaItem.captureDate);
                            self.validateAndSavePhoto(tempPhoto);
                        }
                    });
                    picker.set('select', self.selectedPhoto.mediaItem.captureDate ? self.selectedPhoto.mediaItem.captureDate : '');
                }, 500);
            },
            initPrimaryPhoto: function(bubble) {
                var self = this;
                $(".detailsPrimary input").unbind('change');
                $('.detailsPrimary').removeAttr("disabled");
                $(".detailsPrimary input").on('change', function () {
                    var tempPhoto = self.selectedPhoto;
                    tempPhoto.isPrimary = true;
                    self.validateAndSavePhoto(tempPhoto, function () {
                        $.each(self.photos, function(i, photo) {
                            if(photo.id != self.selectedPhoto.id) {
                               self.photos[i].isPrimary = false;
                            }
                        });
                        var allBubbles = $('.photoPagination').children('span');
                        allBubbles.removeClass('primary');
                        bubble.addClass('primary');
                        $('.setPrimary-label').hide();
                        $('.photoDelete').removeClass('active');
                        $('.photoRelink').removeClass('active');
                        $('.photoDelete').addClass('disabled');
                        $('.photoRelink').addClass('disabled');
                        $('.detailsPrimary').addClass('primary');
                        $('.detailsPrimary').attr("disabled", true);
                    });
                });
                $(".detailsPrimary input").prop("checked", self.selectedPhoto.isPrimary);
                if(self.selectedPhoto.isPrimary) {
                    $('.detailsPrimary').addClass('primary');
                    $('.detailsPrimary').attr("disabled", true);
                } else {
                    $('.detailsPrimary').removeClass('primary');
                    $('.detailsPrimary').removeAttr("disabled");
                }
                if(self.selectedPhoto.isPrimary) {
                    var moreThanOnePhoto = false;
                    $.ajax({
                        type: "GET",
                        url: jsRoutes.controllers.MediaController.getMediaByOwner(self.selectedPhoto.ownerId).url,
                        cache: false,
                        success: function (photoData) {
                            if(photoData && photoData.length > 1) {
                                moreThanOnePhoto = true;
                            }
                            if(photoData && photoData.length == 1 && moreThanOnePhoto == false) {
                                $('.photoDelete').addClass('active');
                                $('.photoDelete').removeClass('disabled');
                                if(self.fromGallery == true) {
                                    if(self.isReadOnly) {
                                        $('.photoRelink').removeClass('active');
                                        $('.photoDelete').removeClass('active');
                                        $('.photoRelink').addClass('disabled');
                                        $('.photoDelete').addClass('disabled');
                                    } else {
                                        $('.photoRelink').addClass('active');
                                        $('.photoRelink').removeClass('disabled');
                                    }
                                }
                            } else {
                                $('.photoDelete').addClass('disabled');
                                $('.photoDelete').removeClass('active');
                                if(self.fromGallery == true) {
                                    $('.photoRelink').addClass('disabled');
                                    $('.photoRelink').removeClass('active');
                                }
                            }
                        },
                        error: function (response) {
                            self.errorHandler(response);
                        }
                    });
                } else {
                    $('.photoDelete').addClass('active');
                    $('.photoDelete').removeClass('disabled');
                    if(self.fromGallery) {
                        $('.photoRelink').addClass('active');
                        $('.photoRelink').removeClass('disabled');
                    }
                }
            },
            initInternalOnly: function() {
                let self = this;
                let isChecked = false;
                if (self.selectedPhoto.internalOnlyYN)  {
                    isChecked = true;
                };

                $(".detailsInternalOnly input").unbind('change');
                $(".detailsInternalOnly input").on('change', function () {
                    let tempPhoto = self.selectedPhoto;
                    if ($(this).prop('checked')) {
                        tempPhoto.internalOnlyYN = true;
                        isChecked = true;
                    } else {
                        tempPhoto.internalOnlyYN = false;
                        isChecked = false;
                    }
                    self.validateAndSavePhoto(tempPhoto);
                });

                $(".detailsInternalOnly input").prop("checked", isChecked);
            },
            initInsurance: function() {
                var self = this;
                var isChecked = false;

                if (self.selectedPhoto.tags.indexOf("isIncludedInInsurance") >= 0)  {
                    isChecked = true;
                };

                $(".detailsInsurance input").unbind('change');
                $(".detailsInsurance input").on('change', function () {
                    var tempPhoto = self.selectedPhoto;
                    //tempPhoto.mediaItem.isIncludedInInsurance = $(this).prop('checked');

                    //for media entry tags
                    //TODO: REMOVE or ADD isIncludedInInsurance from the tags
                    if ($(this).prop('checked')) {
                        tempPhoto.tags.push("isIncludedInInsurance");
//                        tempPhoto.tags.push("isIncludedInInsuranceReports");
                        isChecked = true;
                    } else {
                        tempPhoto.tags.splice(tempPhoto.tags.indexOf('isIncludedInInsurance'), 1 );
                        tempPhoto.tags.splice(tempPhoto.tags.indexOf('isIncludedInInsuranceReports'), 1 );
                        isChecked = false;
                    }
                    self.validateAndSavePhoto(tempPhoto);
                });
                //$(".detailsInsurance input").prop("checked", self.selectedPhoto.mediaItem.isIncludedInInsurance);
                $(".detailsInsurance input").prop("checked", isChecked);
            },
            initTags: function(bubble) {
                var self = this;
                $(".tag input").unbind('change');
                $('.tag input').prop("checked", false)
                $(".tag input").on('change', function () {
                    var tags = $('.tag input:checkbox:checked').map(function() {return $(this).attr("code");}).get().join(',').split(',');
                    tags = tags.filter(function(entry) { return entry.trim() != ''; });
                    var tempPhoto = self.selectedPhoto;
                    tempPhoto.mediaItem.tags = tags;

                    if (self.validateAndSavePhoto(tempPhoto)) {
                        bubble.removeClass('invalid');
                        bubble.addClass('completed');
                    } else {
                        bubble.addClass('invalid');
                        bubble.removeClass('completed');
                    }
                });
                $.each(self.selectedPhoto.mediaItem.tags, function(index, tag) {
                    if(tag) {
                        $('.tag input[code=' + tag.trim() + ']').prop("checked", true)
                    }
                });
            },
            initPhotoDownload: function() {
                var self = this;
                $('.photoDownloadLink').attr("href", self.selectedPhoto.mediaItem.originalImageUrl);
            },
            waitBeforeClick: function(photoId) {
                var self = this;
                if ($('.edit input').length == 0) {
                    var allBubbles = $('.photoPagination').children('span');
                    allBubbles.removeClass('active');
                    var selectedBubble = $("#bubble_" + photoId);
                    selectedBubble.addClass('active');
                    self.selectedPhoto = self.filterById(photoId);
                    self.initDescription(selectedBubble);
                    self.initDate();
                    self.initPrimaryPhoto(selectedBubble);
                    self.initInternalOnly();
                    self.initInsurance();
                    self.initTags(selectedBubble);
                    self.initPhotoDownload();
                    if(self.fromGallery) {
                       // initRelinkButton(selectedBubble, response);
                    }
                    if(self.isReadOnly || self.fromHomeValuation) {
                        $('.photoRelink').removeClass('active');
                        $('.photoDelete').removeClass('active');
                        $('.photoRelink').addClass('disabled');
                        $('.photoDelete').addClass('disabled');
                        if(self.isReadOnly) {
                            $('.imageDetails').addClass('disabled');
                            $('.detailsPrimary input').attr("disabled", true);
                            $('.detailsInsurance input').attr("disabled", true);
                            $('.improvementDate').attr("disabled", true);
                        }
                    }
                } else {
                    setTimeout(self.waitBeforeClick, 100, photoId);
                }
            },
            initDeleteButton: function() {
                var self = this;
                $('.photoDelete').off("click").click(function (evt) {
                    self.confirm.okLabel="Delete";
                    self.confirm.cancelLabel="Cancel";
                    self.confirm.header="Delete Photo";
                    self.confirm.message="Choosing delete will permanently remove this photo. You can't undo this action. Do you want to continue and remove this photo anyway?";
                    self.confirm.okHandler=self.confirmDelete;
                    $('#confirmationPhotoViewer').show();
                });
            },
            initRelinkButton: function() {
                var self = this;
                $('.photoRelink').off("click").click(function (evt) {

                    self.link.thumbnail=self.selectedPhoto.mediaItem.smallImageUrl;
                    self.link.okHandler=self.confirmRelink;
                    $('.relink').show();

                    $('.relink-box').val('');
                    $('.relink-validation').text('');

                    $('.relink-close-icon').off("click").click(function (evt) {
                        $('.relink-box').val('');
                    });
                    $('.relink-box').off("keydown").keydown(function (evt) {
                        $('.relink-validation').text('');
                    });
                    $('#primaryPhotoRelink').prop('checked', false);
                    self.enableRelinkButtons();

                });
            },
            initPreviousAndNext: function() {
                var self = this;
                $('.photoBack').off("click").click(function (evt) {
                    setTimeout(function () {
                        $('.save-and-close').addClass('active');
                        if (self.getIndex() > 0) {
                            $("#bubble_" + self.photos[self.getIndex() - 1].id).click();
                        }
                    }, 500);
                });
                $('.photoForward').off("click").click(function (evt) {
                    setTimeout(function () {
                        $('.save-and-close').addClass('active');
                        if (self.getIndex() < self.photos.length - 1) {
                            $("#bubble_" + self.photos[self.getIndex() + 1].id).click();
                        }
                    }, 500);
                });
            },
            saveAndClosePhotoViewer: function() {
                var self = this;
                $('.save-and-close').off("click").click(function (evt) {
                    setTimeout(self.waitBeforeClose, 1000);
                });
            },
            waitBeforeClose: function() {
                var self = this;
                self.checkIfNoPrimary();
            },
            checkIfNoPrimary: function() {
                var self = this;
                if ($('.primary').length == 0) {
                    self.iffWithPrimaryPhoto(function () {
                        self.warning.header = "No Primary Photo";
                        self.warning.message = "You need to select a Primary Photo.";
                        $('.warning').show();
                        return false;
                    });
                } else {
                    self.checkIfPrimaryIsInvalid();
                }
            },
            checkIfPrimaryIsInvalid: function() {
                var self = this;
                if ($('.bubbleSpan.primary.invalid').length == 1) {
                    self.warning.header = "Invalid Primary Photo";
                    self.warning.message = "In order for the Primary Photo to be valid a description or tag is required.";
                    $('.warning').show();
                    return false;
                } else {
                    self.checkInvalidPhotos();
                }
            },
            checkInvalidPhotos: function() {
                var self = this;
                if ($('.invalid').length > 0) {
                    self.confirm.okLabel="Yes, Discard Photos and Close";
                    self.confirm.cancelLabel="Go Back";
                    self.confirm.header="Invalid Photos";
                    self.confirm.message="There are photos without description or tags. These photos will not be saved if you close this window. Do you want to proceed?";
                    self.confirm.okHandler=self.discardPhotos;
                    $('#confirmationPhotoViewer').show();
                } else {
                    window.close();
                }
            },
            discardPhotos: function() {
                window.close();
            },
            iffWithPrimaryPhoto: function(callback) {
                var self = this;
                $.ajax({
                    type: "GET",
                    cache: false,
                    url:  jsRoutes.controllers.MediaController.getPrimaryMediaByOwner(self.selectedPhoto.ownerId, (!self.selectedPhoto.category || self.selectedPhoto.category == "") ? "Property" : self.selectedPhoto.category).url,
                    error: function(response) {
                        //Back-end currently returning 500 error if no primary photo is defined. Need to refactor this once that is fixed.
                        self.errorHandler(response);
                        self.hasPrimary = false;
                        $('.setPrimary-label').show();
                        if(callback) callback();
                    },
                    success: function(response) {
                        console.log("++++ check if with primary photo++++");
                        console.log(JSON.stringify(response));
                        self.hasPrimary = true;
                        $('.setPrimary-label').hide();
                        if(callback && !response) callback();
                        if(callback) self.checkIfPrimaryIsInvalid();
                    }
                });
            },
            imgError: function() {
                var self = this;
                $('.save-and-close').removeClass('active');
                $('.image').parent('span').addClass('spinner');
                setTimeout(function () {
                    $("#bubble_" + self.selectedPhoto.id).click();
                    $('.save-and-close').addClass('active');
                    $('.image').parent('span').removeClass('spinner');
                }, 4000);
            },
            savePhoto: function(data, callback) {
                if(!data.category || data.category == "") data.category = "Property";
                var self = this;
                var url = jsRoutes.controllers.MediaController.updateMedia().url;
                $.ajax({
                    type: "POST",
                    cache: false,
                    url:  url,
                    processData: false,
                    contentType: 'application/json',
                    data: JSON.stringify(data)
                }).done(function (response) {
                    console.log("++++ save response++++");
                    console.log(JSON.stringify(response));
                    self.selectedPhoto = response;
                    self.updatePhotos(self.selectedPhoto);
                    if(callback) callback();
                });
            },
            validateAndSavePhoto: function(data, callback) {
                let photoError = false;
                // Validate at least 1 tag or a description
                if (data.mediaItem.tags.length == 0 && (data.description == null || data.description.trim().length == 0)) {
                    console.error('Could not save photo, missing either description or tag');
                    this.showWarning();
                    photoError = true;
                }
                data.mediaItem.status = 'UPLOAD_COMPLETED'
                if(!photoError){
                   this.hideWarning();
                }
                this.savePhoto(data, callback);
                return true;
            },
            showWarning: function() {
                $('.detailsWarning').addClass('in');
            },
            hideWarning: function() {
                $('.detailsWarning').removeClass('in');
            },
            relinkPhoto: function(callback) {
                var self = this;
                var qupid = $('.relink-box');

                if(qupid.val().length === 0 ) {
                    $('.relink-validation').text('Qpid is required.');
                    self.enableRelinkButtons();
                } else if ($.isNumeric(qupid.val()) == false) {
                    $('.relink-validation').text('Qpid should be numeric.');
                    self.enableRelinkButtons();
                }  else if (qupid.val().length >= 10 ) {
                    $('.relink-validation').text('Qpid should be less than 10 digit.');
                    self.enableRelinkButtons();
                } else {
                    $.ajax({
                        type: "GET",
                        url: jsRoutes.controllers.MediaController.reLinkPhoto(self.selectedPhoto.id, $('#primaryPhotoRelink').prop('checked'), qupid.val().trim()).url,
                        cache: false,
                        success: function (response) {
                            if(response.status == 'success') {
                                console.log(" +++ relinkPhoto ++++ ");
                                console.log(JSON.stringify(response));
                                //self.selectedPhoto = response;
                                self.updatePhotos(self.selectedPhoto);

                                //refresh the property search result of the two properties
                                var sourceData = {};
                                sourceData.propID = self.selectedPhoto.ownerId;
                                sourceData.qupID =  self.selectedPhoto.qivsPhotoId.substring(0, self.selectedPhoto.qivsPhotoId.indexOf("_"));
                                console.log("++++++++++++++++++++++++++++++relink target-source data (photoviewer source): " + JSON.stringify(sourceData));
                                opener.refreshFromRelink(sourceData);

                                var targetData = {};
                                targetData.propID = response.property;
                                targetData.qupID = $('.relink-box').val();
                                console.log("++++++++++++++++++++++++++++++relink target-source data (photoviewer target): " + JSON.stringify(targetData));
                                opener.refreshFromRelink(targetData);

                                $('.relink').hide();
                            } else {
                                console.log("not success: " + JSON.stringify(response));
                                $('.relink-validation').text(response.message);
                                self.enableRelinkButtons();
                            }
                            if (callback) callback();
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            console.log("with error: " + JSON.stringify(response));
                            $('.relink-validation').text(response.message);
                            self.enableRelinkButtons();
                        }
                    });
                }
            },
            confirmDelete: function() {
                var self = this;
                var tempPhoto = self.selectedPhoto;
                tempPhoto.mediaItem.status = 'DELETED';
                self.savePhoto(tempPhoto, function () {
                    if(self.photos.length == 1) {
                        window.close();
                    } else {
                        $("#bubble_" + self.selectedPhoto.id).remove();
                        $('#confirmationPhotoViewer').modal( "hide" );
                        self.photos = self.photos.filter(function( obj ) {
                            console.log("confirmDelete filter (self.selectedPhoto.id) : " + obj.id);
                            return obj.id !== self.selectedPhoto.id;
                        });
                        self.selectedPhoto = self.photos[0];

                        console.log("confirmDelete before click (self.selectedPhoto) : " + self.selectedPhoto.id);
                        if (self.photos.length == 1) {
                            setTimeout(function() {
                                $("#bubble_" + self.selectedPhoto.id).addClass('primary');
                                $('.photoDelete').addClass('active');
                                $('.photoDelete').removeClass('disabled');
                                $("#bubble_" + self.selectedPhoto.id).click();
                            }, 1500)
                        }
                        $("#bubble_" + self.selectedPhoto.id).click();
                    }
                });
            },
            confirmRelink: function () {
                var self = this;

                self.relinkPhoto(function() {
                    console.log("relink-validation value: " + $('.relink-validation').text());
                    if ($('.relink-validation').text().length <= 0) {
                        if (self.photos.length == 1) {
                            $('.save-and-close').click();
                        } else {
                            $("#bubble_" + self.selectedPhoto.id).remove();
                            $('.relink').modal("hide");
                            self.photos = self.photos.filter(function (obj) {
                                return obj.id !== self.selectedPhoto.id;
                            });
                            self.selectedPhoto = self.photos[0];
                            if (self.photos.length == 1) {
                                setTimeout(function () {
                                    $("#bubble_" + self.selectedPhoto.id).addClass('primary');
                                    $('.photoDelete').addClass('active');
                                    $('.photoDelete').removeClass('disabled');
                                    $("#bubble_" + self.selectedPhoto.id).click();
                                }, 1500);
                            }
                            $("#bubble_" + self.selectedPhoto.id).click();
                        }
                    }
                });

            },
            getIndex: function() {
                var self = this;
                var index = -1;
                $.each(self.photos, function(i, photo) {
                    if(photo.id == self.selectedPhoto.id) {
                        index = i;
                    }
                });
                return index;
            },
            enableRelinkButtons: function() {
                $("#relinkCancel").addClass('active');
                $("#relinkCancel").removeClass('disabled');
                $("#relinkRelink").addClass('active');
                $("#relinkRelink").removeClass('disabled');
            }
        },
        mounted: function() {
            var self = this;

            EventBus.$on('uploaded-photos', function(photos) {
                var photo = self.getURLParam('photoId');
                self.photos = photos;
                self.selectedPhoto = photo ? self.filterById(photo) : photos[0];
                self.iffWithPrimaryPhoto();
                self.initBubbles();
                self.initDeleteButton();
                self.initRelinkButton();
                self.initPreviousAndNext();
                self.saveAndClosePhotoViewer();
                $('.photoView').removeClass('disabled');
            });

        }
    }
</script>
<style scoped>
    @import "/assets/stylesheets/desktop.css";
</style>

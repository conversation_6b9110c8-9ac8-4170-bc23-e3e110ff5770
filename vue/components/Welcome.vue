<template>
    <div class="homeWrapper" v-if="showTemplate">
        <div class="homeRow imageCenter">
            <div class="homeText">
                <h1>Welcome to QV Monarch</h1>

                <p class="intro">Monarch is QV's new core application that, over time, will
                    replace our long lived and much loved QIVS. Monarch is more than a fresh coat of paint and a new engine. Monarch aims to provide a much
                    improved user interface, making it easier and faster to interact with and extract core property data than ever before. Our goal for Monarch
                    is to help you make better property related decisions by providing the right data when and where you need it.</p>

                <p>Monarch is being delivered in a modular fashion, through monthly    releases over a 2-3 year period.  Each release will focus on delivering new
                    features or replacing a key element of the current application. QIVS isn’t being turned off anytime soon. Monarch will work seamlessly alongside
                    QIVS. Overtime you will be seeing and using more and more of the ‘new’ and less of the ‘old’. Questions, ideas and feedback are all welcome –
                    talk to your    local QV Service Manager or email <a
                            href="mailto:<EMAIL>"><EMAIL></a>.</p>
            </div>
        </div>

        <div class="homeRow imageRight">
            <div class="homeText">
                <h3>Find the properties you're<br/>looking for fast</h3>

                <p>The Monarch search has been designed to help you quickly find and retrieve property information. Just start typing an address, Qpid, street name, roll number, category or CT into the search bar and your results will be delivered in an easy to scan "type ahead" results box.</p>
            </div>
            <img src="assets/images/monarchHome-TypeAhead.png" width="650"/>
        </div>

        <div class="homeRow imageLeft">
            <img src="assets/images/monarchHome-ExpandedSearch.jpg" width="800"/>
            <div class="homeText">
                <h3>Get key data within your search results</h3>

                <p>We're making it easier and faster to get information about the properties you're interested in by displaying core data directly within your search results. Every Monarch search result can be expanded to show a helpful range of key property values, photos and more.</p>
            </div>
        </div>

        <div class="homeRow imageCenter">
            <div class="homeText">
                <h3>All new property screen</h3>

                <p>Monarch provides a fresh and friendly home to view and interact with our core property data. Easily accessible from both Monarch search results and the property photos link in QIVS, our new Property Screen features a clean modern design, easy to read property information and a gallery of large photos.</p>
            </div>
            <img src="assets/images/monarchHome-mdHeader.jpg" width="1320"/>
        </div>

        <div class="homeRow imageCenter">
            <div class="homeText">
                <h3>Change your view</h3>

                <p>Every part of Monarch is being designed to help you work better. Our new Property Screen gives you the ability to display different groups of information and to set the view that works best for you. You can even customize your pages by moving sections around to suit what you need to see and how you like work.</p>
            </div>
            <img src="assets/images/monarchHome-LandUseMass.png" width="1100"/>
        </div>

        <!--<div class="homeRow imageCenter">
            <div class="homeText">
                <h3>More tools and features coming soon...</h3>

                <p>In the pipeline for upcoming Monarch releases are the beginnings of a homepage dashboard featuring data visualisations of key roll maintenance metrics, extended search capabilities with advanced property criteria and the ability to export property data directly from search results to a variety of file formats.</p>
            </div>
            <%--<asset:image src="monarchHome-taDashboard.png" width="1100"/>--%>
            <div class="moreFeatures">
                <div class="oneFive search">Advanced Search</div>

                <div class="oneFive trends">Live Trends</div>

                <div class="oneFive sales">Sales Information</div>

                <div class="oneFive mobile">Mobile Friendly</div>

                <div class="oneFive exportable">Exportable Results</div>
            </div>
        </div>-->
    </div>

</template>

<script>
    import { EventBus } from '../EventBus.js'
    import { store } from '../DataStore.js'

    export default {
        data: function() {

            return {
                showTemplate: false
            }

        },
        methods: {

        },
        mounted: function() {
            const self = this;
            EventBus.$on('display-content', function(event) {
                var searchType = event.searchType
                if (searchType && searchType == 'splash-page') {
                    self.showTemplate = true
                }
                else {
                    self.showTemplate=false
                }
            });

            EventBus.$on('display-splash-page', function() {
                self.showTemplate = true
            });
        }
    }
</script>
<template>
    <div>
        <div v-if="showSearch">
            <div class="advSearch-row" v-if="categoriesOnPage">
                <div class="QVHV-admin-buttons">
                    <div class="QVHV-buttons-left">
                        <button class="primary" v-if="showExit" @click="backToPrevCategory()">Back</button>
                    </div>
                </div>
                <div class="divTable minimalistBlack" v-if="!noResultsFound">
                    <div class="divTableHeading">
                        <div class="divTableRow">
                            <div class="divTableHead">Pick List</div>
                            <div class="divTableHead">Items</div>
                        </div>
                    </div>
                    <div class="divTableBody">
                        <div class="divTableRow" v-for="category in categoriesOnPage">
                            <div :title="category.key" class="divTableCell" :contenteditable="false">
                              <a v-on:click="openCategoryDetails(category.key)">{{ category.key }}</a>
                            </div>
                            <div :title="category.value" class="divTableCell" :contenteditable="false">
                              {{specialCategories.indexOf(category.key) === -1 ? (category.value.substring(0, 300)) + (category.value.length > 300 ? "..." : "") : ""}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="noResults-wrapper mdl-shadow--3dp" v-if="noResultsFound">
                <h2>Sorry, no results were found</h2>
                <p>Search suggestions:</p>
                <ul>
                    <li>Check your spelling.</li>
                    <li>Confirm the keyword that you were searching.</li>
                    <li>Try searching by different criteria.</li>
                </ul>
            </div>
        </div>
        <div class="resultsWrapper" v-if="showEdit">
            <div class="resultsInner-wrapper mdl-shadow--3dp">
                <div class="QVHV-admin-buttons">
                    <div class="QVHV-buttons-left">
                        <button class="primary" v-if="showExit" @click="backToPrevCategory()">Back</button>
                    </div>
                </div>
                <div class="loadingSpinnerExportResults"></div>
                <!-- Regular Classifications Details block begin -->
                <div class="divTable minimalistBlack" v-if="isRegular">
                    <div class="divTableHeading">
                        <div class="divTableRow">
                            <div class="divTableHead">Category</div>
                            <div class="divTableHead">Code</div>
                            <div class="divTableHead">Description</div>
                            <div class="divTableHead">Short Description</div>
                            <div class="divTableHead">Sort Order</div>
                            <div class="divTableHead">Is Active</div>
                            <div class="divTableHead"></div>
                        </div>
                    </div>
                    <div class="divTableBody">
                        <div class="divTableRow" v-for="classification, key in classifications">
                            <div :class="[classification.category == '' ? 'admin-editable-text' : '', 'divTableCell']" :contenteditable="classification.category == '' ? true : false">{{classification.category}}</div>
                            <div class="admin-editable-text divTableCell">
                              <textarea :class="[classification.category == '' ? 'admin-editable-text' : '']" v-model="classification.code" :readonly="classification.addnew ? false : true"></textarea>
                            </div>
                            <div class="admin-editable-text divTableCell">
                              <textarea class="admin-editable-text" v-model="classification.description"></textarea>
                            </div>
                            <div class="admin-editable-text divTableCell">
                              <textarea class="admin-editable-text" v-model="classification.shortDescription"></textarea>
                            </div>
                            <div class="admin-editable-text divTableCell">
                              <input type="number" class="admin-editable-text" v-model="classification.sortOrder"></input>
                            </div>
                            <div class="admin-editable-text divTableCell">
                              <input type="checkbox" v-model="classification.isActive"/>
                            </div>
                            <div class="divTableCell QVHV-admin-buttons">
                              <button class="primary" @click="saveOrUpdate(this, key, classification)">{{classification.addnew ? 'Save' : 'Update'}}</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Regular Classifications Details block end -->

                <!-- Zoning Details begin -->
                <div class="divTable minimalistBlack" v-if="isTAZ">
                    <div class="divTableHeading">
                        <div class="divTableRow">
                            <div class="divTableHead">Category</div>
                            <div class="divTableHead">Description</div>
                            <div class="divTableHead">Short Description</div>
                            <div class="divTableHead">Zone Description</div>
                            <div class="divTableHead">Site Density</div>
                            <div class="divTableHead"></div>
                        </div>
                    </div>
                    <div class="divTableBody">
                        <div class="divTableRow" v-if="zoneDetails.length">
                            <div class="divTableCell">{{parentCategoriesQueue[2]}}</div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="parentClassification.description"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="parentClassification.shortDescription"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="zoneDetails[0].description"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="zoneDetails[1].description"></textarea>
                            </div>
                            <div class="divTableCell QVHV-admin-buttons">
                                <button class="primary" @click="saveOrUpdate(this, 0, zoneDetails[0])">Update</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Zoning Details block end -->

                <!-- Locality Details block begin -->
                <div class="divTable minimalistBlack" v-if="isTAL">
                    <div class="divTableHeading">
                        <div class="divTableRow">
                            <div class="divTableHead">Category</div>
                            <div class="divTableHead">Description</div>
                            <div class="divTableHead">Short Description</div>
                            <div class="divTableHead">Proximity</div>
                            <div class="divTableHead">Surrounding Development</div>
                            <div class="divTableHead">Facilities</div>
                            <div class="divTableHead">Public Transport</div>
                            <div class="divTableHead">Locality Features</div>
                            <div class="divTableHead"></div>
                        </div>
                    </div>
                    <div class="divTableBody">
                        <div class="divTableRow" v-if="localityDetails.length">
                            <div class="divTableCell">{{parentCategoriesQueue[2]}}</div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="parentClassification.description"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="parentClassification.shortDescription"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="localityDetails[0].description"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="localityDetails[1].description"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="localityDetails[2].description"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="localityDetails[3].description"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-text" v-model="localityDetails[4].description"></textarea>
                            </div>
                            <div class="divTableCell QVHV-admin-buttons">
                                <button class="primary" @click="saveOrUpdate(this, 0, localityDetails[0])">Update</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Locality Details block end -->

                <div class="QVHV-admin-buttons" v-if="isRegular">
                    <div class="QVHV-buttons-left">
                        <button class="primary" @click="createNewClassification()">Add Item</button>
                    </div>
                </div>
            </div>
            <warning :header="warningHeader" :message="warningMessage" close="Ok"></warning>
            <success :header="successHeader" :message="successMessage" close="Ok"></success>
        </div>

    </div>

</template>


<script>
    import {store} from '../../DataStore.js';
    import {EventBus} from '../../EventBus.js';
    import TextAreaInput from '../filters/TextAreaInput.vue';
    import Warning from '../common/Warning.vue';
    import Success from '../common/Success.vue';
    import commonUtils from '../../utils/CommonUtils';

    export default {
        mixins: [commonUtils],
        components: {
            Warning,
            Success,
            TextAreaInput
        },
        data: function () {
            return {
                showSearch: true,
                showEdit: false,
                categoriesOnPage: [],
                allCategories: [],
                filteredCategories: [],
                recordsPerPage: 25,
                startIndex: 0,
                specialCategories: [],
                categoryParam: null,
                classifications: [],
                warningHeader: '',
                warningMessage: '',
                successHeader: '',
                successMessage: '',
                specialCategoriesLevel: 0,
                parentCategory: '',
                parentCategories: [],
                showExit: '',
                parentCategoriesQueue: [],
                zoneDetails: [],
                localityDetails: [],
                parentClassification: {},
                isRegular: false,
                isTAZ: false,
                isTAL: false,
                noResultsFound: false
            }
        },
        methods: {
            createNewClassification: function () {
                const self = this;
                var classification = {
                    "category": self.classifications[0] ? self.classifications[0]['category'] : "",
                    "addnew": true,
                    "isActive": false
                };
                self.classifications.unshift(classification);
            },
            filterData: function (classifications) {
                var self = this;
                if (classifications != 'undefined' && classifications.length > 0) {
                    self.noResultsFound = false;
                    self.filteredCategories = [];
                    for (var i = 0; i < classifications.length; i++) {
                        var classification = classifications[i];
                        if ((self.filteredCategories[classification.category] == 'undefined' || self.filteredCategories[classification.category] == null) &&
                            (self.allCategories[classification.category] != 'undefined' && self.allCategories[classification.category] != null)) {
                            self.filteredCategories[classification.category] = self.allCategories[classification.category];
                        }
                    }

                    self.pagination();
                } else {
                    self.noResultsFound = true;
                }
            },
            backToPrevCategory: function () {
                this.showSearch = false;
                this.showEdit = false;
                console.log("this.parentCategory" + this.parentCategory);

                const self = this;
                if (self.parentCategoriesQueue && self.parentCategoriesQueue.length <= 1) {
                    self.parentCategoriesQueue = [];
                    self.fetchAllCategories();
                } else {
                    var c = self.parentCategoriesQueue[self.parentCategoriesQueue.length - 2];
                    self.parentCategoriesQueue.pop();
                    self.prevCategoryDetails(c);
                }
                console.log("Special Category level === " + this.specialCategoriesLevel);

                this.showSearch = true;
                var param = {};
                param.displayAdminSearchbar = true;
                EventBus.$emit('display-admin-search-bar', param);
            },
            fetchAllCategories: function () {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.ClassificationController.getAllCategories().url,
                    cache: false,
                    success: function (response) {
                        self.categoriesOnPage = [];
                        self.allCategories = response;
                        self.filteredCategories = response;
                        self.specialCategoriesLevel = 0;
                        if (response.length > 0) {
                            self.noResultsFound = false;
                        }
                        self.pagination();
                    },
                    error: function (response) {
                        console.log("Error when getting all categories, details as below: ");
                        self.errorHandler(response);
                    },
                });

                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.ClassificationController.getSpecialCategories().url,
                    cache: false,
                    success: function (response) {
                        if (response.length > 0) {
                            self.noResultsFound = false;
                        }
                        self.specialCategories = response;
                    },
                    error: function (response) {
                        console.log("Error when fetching special categories, details as below: ");
                        self.errorHandler(response);
                    },
                });
            },
            pagination: function () {
                var self = this;
                setTimeout(function () {
                        self.categoriesOnPage = [];
                        if (self.filteredCategories.length <= self.recordsPerPage) {
                            //self.categoriesOnPage = self.filteredCategories;
                            for (var key in self.filteredCategories) {
                                self.categoriesOnPage.push({key: key, value: self.filteredCategories[key]});
                            }

                        } else {

                            var counter = 0;
                            self.startIndex = self.categoriesOnPage.length;
                            for (var key in self.filteredCategories) {
                                if (counter >= self.startIndex && counter <= (self.startIndex + self.recordsPerPage)) {
                                    self.categoriesOnPage.push({key: key, value: self.filteredCategories[key]});
                                }
                                counter++;
                            }
                        }

                    $(window).data('ajaxready', true).scroll(function () {
                        var scroll = $(window).scrollTop(),
                            winHeight = $(window).height();
                        if (Math.ceil(scroll + winHeight) >= ($(document).height() - 130)) {
                            var counter = 0;
                            self.startIndex = self.categoriesOnPage.length;
                            for (var key in self.filteredCategories) {
                                if (counter >= self.startIndex && counter <= (self.startIndex + self.recordsPerPage)) {
                                    self.categoriesOnPage.push({key: key, value: self.filteredCategories[key]});
                                }
                                counter++;
                            }
                        }
                    });
                }, 500);
            },
            openSpecialCategory: function (specialCategory) {
                const self = this;
                self.parentCategories[self.specialCategoriesLevel] = specialCategory;
                var criteria = {"parentCategory": specialCategory, "sort": "CATEGORY", "order": "ASC"};
                var jsRoute = jsRoutes.controllers.ReferenceData.searchClassifications();
                $.ajax({
                    type: "POST",
                    url: jsRoute.url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(criteria),
                    dataType: "json",
                    async: false,
                    success: function (response) {
                        var subCategories = [];
                        if (response && response.length > 0) {
                            self.showSearch = false;
                            self.allCategories = [];
                            self.filteredCategories = [];
                            self.categoriesOnPage = [];
                            for (var i = 0; i < response.length; i++) {
                                self.allCategories[response[i].category] = "";
                                self.filteredCategories[response[i].category] = "";
                            }
                            self.showSearch = true;
                        }
                        self.pagination();
                        self.specialCategoriesLevel = self.specialCategoriesLevel + 1;
                        var param = {};
                        param.displayAdminSearchbar = false;
                        EventBus.$emit('display-admin-search-bar', param);
                    },
                    error: function (response) {
                        console.log("Error when opening special category, details as below: ");
                        self.errorHandler(response);
                    }
                });

                self.showTemplate = !self.showTemplate;
                if (!self.showTemplate) {
                    self.refreshTemplate = true;
                }
            },
            categoryDetails: function (category) {
                const self = this;
                if (self.parentCategoriesQueue.length === 0) {
                    self.fetchAllCategories();
                } else if ((self.parentCategoriesQueue.length === 1 || self.parentCategoriesQueue.length === 2) && self.specialCategories.indexOf(self.parentCategoriesQueue[0]) !== -1) {
                    self.openSpecialCategory(category);
                } else if (self.parentCategoriesQueue.length === 3 && self.specialCategories.indexOf(self.parentCategoriesQueue[0]) !== -1) {
                    var itemCriteria = {
                        "parentCategory": category,
                        "sort": "CODE"
                    };
                    var parentCriteria = {
                        "category": category
                    };
                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ReferenceData.searchClassifications().url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(itemCriteria),
                        success: function (response) {
                            self.classifications = [];
                            if (self.parentCategoriesQueue[0] === "TAandZoning" || self.parentCategoriesQueue[0] === "TAandProposedZone") {
                                self.zoneDetails = response;
                            } else if (self.parentCategoriesQueue[0] === "TAandLocality") {
                                self.localityDetails = response;
                            }

                            self.showSearch = false;
                            self.showEdit = true;
                            var param = {};
                            param.displayAdminSearchbar = false;
                            EventBus.$emit('display-admin-search-bar', param);
                        },
                        error: function (response) {
                            console.log("Error when getting category details, details as below: ");
                            self.errorHandler(response);
                        }
                    });
                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ReferenceData.searchClassifications().url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(parentCriteria),
                        success: function (response) {
                            self.parentClassification = response[0];
                        },
                        error: function (response) {
                            console.log("Error when getting parent classification details, details as below: ");
                            self.errorHandler(response);
                        }
                    });

                } else {
                    $.ajax({
                        type: "GET",
                        url: jsRoutes.controllers.ClassificationController.getClassifications(self.categoryParam).url,
                        cache: false,
                        success: function (response) {
                            self.classifications = response;
                            self.showSearch = false;
                            self.showEdit = true;
                            var param = {};
                            param.displayAdminSearchbar = false;
                            EventBus.$emit('display-admin-search-bar', param);

                            //This is to make sure when the user clicks on exit it should go to the back screen
                            self.specialCategoriesLevel = self.specialCategoriesLevel + 1;
                        },
                        error: function (response) {
                            console.log("Error when getting classification details: ");
                            self.errorHandler(response);
                        },
                    });
                }
            },
            prevCategoryDetails: function (category) {
                const self = this;
                self.categoryParam = category;
                self.categoryDetails(self.categoryParam);
            },
            openCategoryDetails: function (category) {
                const self = this;
                self.categoryParam = category;
                // Setting the parent categories queue, used for going back to previous category
                if (self.parentCategoriesQueue.indexOf(category) === -1) {
                    self.parentCategoriesQueue.push(category);
                }
                // Setting flags for showing different classifications
                if (self.parentCategoriesQueue[0] === "TAandZoning" || self.parentCategoriesQueue[0] === "TAandProposedZone") {
                    self.isTAZ = true;
                    self.isTAL = false;
                    self.isRegular = false;
                } else if (self.parentCategoriesQueue[0] === "TAandLocality") {
                    self.isTAL = true;
                    self.TAZ = false;
                    self.isRegular = false;
                } else {
                    self.isRegular = true;
                    self.isTAZ = false;
                    self.isTAL = false;
                }
                self.categoryDetails(category);
            },
            saveOrUpdate: function (elem, key, classification) {
                var self = this;

                if (self.isRegular) {
                    var clist = [];
                    for (var i = 0; i < self.classifications.length; i++) {
                        if (self.classifications[i]["code"] == '') {
                            alert("Please provide Category Code for each of records.");
                            return;
                        }
                        console.log("Testing " + self.classifications[i]["sortOrder"]);
                        if (self.classifications[i]["sortOrder"] && !/^\d*$/.test(self.classifications[i]["sortOrder"])) {
                            alert("Sort order must be a positive whole number");
                            return;
                        }
                        if (self.classifications[i]["code"] && !self.classifications[i]['addnew']) {
                            clist.push(self.classifications[i]["code"].toUpperCase());
                        }
                    }

                    setTimeout(function () {
                        if (classification['addnew'] && classification && clist.indexOf(classification.code.toUpperCase()) !== -1) {
                            setTimeout(function () {
                                self.warningHeader = "Category code: ";
                                self.warningMessage = classification["code"] + " already exists in " + classification["category"] + "!";
                                $('.warning').show();
                            }, 500);
                        } else {
                            self.showEdit = false;
                            $.ajax({
                                type: "POST",
                                url: jsRoutes.controllers.ClassificationController.saveClassification().url,
                                cache: false,
                                contentType: "application/json; charset=utf-8",
                                data: JSON.stringify(classification),
                                dataType: "json",
                                success: function (response) {
                                    self.showEdit = true;
                                    var param = {};
                                    param.displayAdminSearchbar = false;
                                    EventBus.$emit('display-admin-search-bar', param);
                                    self.resultMessage = response;
                                    self.successHeader = (classification.addnew) ? "Create new classification" : "Update classification";
                                    self.successMessage = self.resultMessage;
                                    setTimeout(function () {
                                        $('.success').show();
                                    }, 500);
                                },
                                error: function (response) {
                                    self.errorHandler(response);
                                    self.showEdit = true;
                                    var param = {};
                                    param.displayAdminSearchbar = false;
                                    EventBus.$emit('display-admin-search-bar', param);

                                    setTimeout(function () {
                                        self.warningHeader = "Error while saving classification";
                                        self.warningMessage = response.responseText;
                                        $('.warning').show();
                                    }, 500);
                                },
                                complete: function () {
                                    classification.addnew = false;
                                    self.specialCategoriesLevel = self.specialCategoriesLevel - 1;
                                    self.openCategoryDetails(classification.category);
                                }
                            });
                        }
                    }, 300);

                } else if (self.isTAZ || self.isTAL) {
                    setTimeout(function () {
                        self.showEdit = false;
                        if (self.isTAZ) {
                            self.classifications = self.classifications.concat(self.zoneDetails).concat(self.parentClassification);
                        } else if (self.isTAL) {
                            self.classifications = self.classifications.concat(self.localityDetails).concat(self.parentClassification);
                        }
                        for (var i in self.classifications) {
                            $.ajax({
                                type: "POST",
                                url: jsRoutes.controllers.ClassificationController.saveClassification().url,
                                cache: false,
                                contentType: "application/json; charset=utf-8",
                                data: JSON.stringify(self.classifications[i]),
                                dataType: "json",
                                success: function (response) {
                                    self.showEdit = true;
                                    self.successHeader = "Update classification";
                                    self.successMessage = response;
                                    $('.success').show();
                                },
                                error: function (response) {
                                    self.errorHandler(response);
                                    self.showEdit = true;
                                    self.warningHeader = "Error while saving classification";
                                    self.warningMessage = response.responseText;
                                    $('.warning').show();
                                }
                            });
                        }
                    }, 300);
                }
            }
        },
        mounted: function () {
            var self = this;
            EventBus.$on('show-edit-picklists', function (event) {
                var param = {};
                if (event.showPage !== 'undefined' && event.showPage === 'AllPicklists') {
                    self.fetchAllCategories(event);
                    self.showSearch = true;
                    param.displayAdminSearchbar = true;
                    self.showEdit = false;
                } else {
                    self.showSearch = false;
                    self.showEdit = true;
                    param.displayAdminSearchbar = false;
                }
                EventBus.$emit('display-admin-search-bar', param);
            });

            EventBus.$on('new-admin-page-search-results', function (event) {
                var param = {};

                self.showSearch = true;
                param.displayAdminSearchbar = true;
                EventBus.$emit('display-admin-search-bar', param);
                self.showEdit = false;
                if (event.searchParams.basicSearchString.trim() === "") {
                    self.fetchAllCategories();
                } else {
                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ClassificationController.fetchClassifications().url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(event.searchParams),
                        dataType: "json",
                        success: function (response) {
                            //Call function
                            self.specialCategoriesLevel = 0;
                            self.filterData(response);
                        },
                        error: function (response) {
                            self.errorHandler(response);
                        },
                    });
                }
            });
        },
        destroyed: function () {
            console.log("Administration destroyed event");
            EventBus.$off('pick-list-details', this.listener);
            EventBus.$off('exit-category-details', this.listener);
        },
        beforeDestroy: function () {
            console.log("Administration before destroyed event");
            EventBus.$off('pick-list-details', this.listener);
            EventBus.$off('exit-category-details', this.listener);
        },
        watch: {
            specialCategoriesLevel: function (newVal, oldVal) {
                if (newVal === 0) {
                    this.showExit = false;
                    this.parentCategories = [];
                } else if (newVal > 0) {
                    this.showExit = true;
                }
            }
        }
    }
</script>
<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="admin-content" v-if="showAdmin">
        <div class="contentWrapper">
            <div class="searchWrapper" v-if="showSearchBox">
                <search-classifications></search-classifications>
            </div>
            <show-edit-picklist></show-edit-picklist>
        </div>
    </div>
</template>
<script>
    import ShowEditPicklist from './ShowEditPicklists.vue';
    import SearchClassifications from './SearchClassifications.vue';
    import {store} from '../../DataStore.js';
    import {EventBus} from '../../EventBus.js';

    export default {
        props: ['readOnly'],
        components: {
            ShowEditPicklist,
            SearchClassifications
        },
        data: function () {
            return {
                showAdmin: false,
                showPicklist: true,
                showSearchBox: true,
                showPicklistDetails: false,
                currentSection: "All-Picklists"
            }
        },
        methods: {
            setCurrentSection: function (sectionName) {
                const self = this;
                self.currentSection = sectionName;
            }
        },
        mounted: function () {
            var self = this;
            EventBus.$on('display-content', function (event) {
                if (event.searchType === 'admin-page') {
                    console.log("Showing administration screen");
                    self.currentSection = "All-Picklists";
                    self.showAdmin = true;
                    setTimeout(function () {
                        event.showPage='AllPicklists';
                        EventBus.$emit('show-edit-picklists', event);
                    }, 300);

                } else {
                    self.showAdmin = false;
                }
            });
        },
        destroyed: function () {
            console.log("Administration destroyed event");
            EventBus.$off('display-content', this.listener);
            EventBus.$off('pick-list-details', this.listener);
            EventBus.$off('exit-category-details', this.listener);
        },
        beforeDestroy: function () {
            console.log("Administration before destroyed event");
            EventBus.$off('display-content', this.listener);
            EventBus.$off('pick-list-details', this.listener);
            EventBus.$off('exit-category-details', this.listener);
        }
    }
</script>
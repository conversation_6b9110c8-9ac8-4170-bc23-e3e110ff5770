<template>
    <div v-if="showTemplate">
        <input id="typeAheadClassifications" class="js-admin-typeahead admin-search-box admin-search-box" name="searchQuery" type="search" value="" placeholder="Search Classifications" autocapitalize="off" autocomplete="off" autocorrect="off">
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import typeahead from '../../jquery.typeahead.js';
    import commonUtils from '../../utils/CommonUtils';

    export default {
        mixins: [commonUtils],
        data: function() {
            return {
                categories: [],
                categoryType: '',
                showTemplate: true
            }
        },
        methods: {
          addTypeAhead: function() {
              $('.js-admin-typeahead').keypress(function(e){
                  if(e.which === 13){
                      console.log(" inside enter key press event ");
                      var eventObj = {};
                      eventObj.searchParams = {basicSearchString: $('.admin-search-box').val(), categoryType: ''};
                      EventBus.$emit('new-admin-page-search-results', eventObj);
                      $('.typeahead__result').slideToggle();
                  }
              });
              $('.js-admin-typeahead').mousedown(function() {
                  $(".js-admin-typeahead").eq(0).val($('.js-admin-typeahead').val()).trigger("input");
              });
              $.typeahead({
                  input: '.js-admin-typeahead',
                  dynamic: true,
                  delay: 1000,
                  maxItem: 0,
                  hint: true,
                  highlight: true,
                  emptyTemplate: "No results found for {{query}}",
                  filter: false,
                  template: function (query, item) {
//                    console.log("query === "+query);
//                    console.log("item === "+JSON.stringify(item));
                      return '<i class="material-icons md-dark md-18">&#xE0C8;</i>' +
                          '<span class="listBox-category">'+item.category+'</span>'
                  },

                  source: {
                      "classification": {
                          display: ['category', 'code', 'description', 'shortDescription'],
                          ajax: function (query) {
                              return {
                                  type: "POST",
                                  contentType: "application/json; charset=utf-8",
                                  url: jsRoutes.controllers.ClassificationController.displayClassificationTypeAheadResult().url,
                                  dataType: "json",
                                  cache: false,
                                  data: JSON.stringify({basicSearchString: query, categoryType: self.categoryType}),
                                  statusCode: {
                                      401: function () {
                                          self.errorHandler({status: 401});
                                      }
                                  }
                              }
                          }
                      }
                  },
                  callback: {
                      onNavigateAfter: function (node, lis, a, item, query, event) {
                          $('.js-admin-typeahead').val(item.category);
                      },
                      onLayoutBuiltBefore: function (node, query, result, resultHtmlList) {
                          if($('.typeahead__result').css('display') == 'none') {
                              $('.typeahead__result').slideToggle();
                          }
                          return resultHtmlList;
                      },

                      onClick: function(node, a, item, ev) {
                          var event = {};
                          event.searchType = 'admin-page-search-results';
                          event.category = item.category;
                          event.categoryType = self.categoryType;
                          event.searchParams = {basicSearchString: event.category, categoryType: event.categoryType};
                          event.sectionName = "Picklists-Details";
                          event.showPage = "SearchResults";
                          //EventBus.$emit('show-edit-picklists', event);
                          EventBus.$emit('new-admin-page-search-results', event);
                      }
                  }
              });
          }
        },
        mounted: function() {
          var self = this;
          self.addTypeAhead();
          EventBus.$on('display-admin-search-bar', function(param){
            self.showTemplate = param.displayAdminSearchbar;
          });
        },
        updated: function() {
          if (this.showTemplate) {
            this.addTypeAhead();
          }
        }
    }
</script>

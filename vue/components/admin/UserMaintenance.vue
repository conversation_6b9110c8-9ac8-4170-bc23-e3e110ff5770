<template>
    <div v-if="showTemplate" class="admin-content">
        <div v-if="showSearchBox">
            <input id="typeAheadUsers" class="js-admin-typeahead admin-search-box-users admin-search-box" name="searchQuery" type="search" value="" placeholder="Search Users" autocapitalize="off" autocomplete="off" autocorrect="off">
        </div>
        <div class="resultsWrapper">
            <div class="QVHV-admin-buttons">
                <div class="QVHV-buttons-left">
                    <button class="primary" v-if="!showSearchBox" @click="backToUserMaintenance()">Back</button>
                </div>
            </div>
            <div class="noResults-wrapper mdl-shadow--3dp" v-if="noResultsFound">
                <h2>Sorry, no results were found</h2>
                <p>Search suggestions:</p>
                <ul>
                    <li>Check your spelling.</li>
                    <li>Confirm the keyword that you were searching.</li>
                    <li>Try searching by different criteria.</li>
                </ul>
            </div>
            <div class="resultsInner-wrapper mdl-shadow--3dp" v-if="!noResultsFound && !uploadImage">
                <div class="resultsTitle">
                    <h1 class="lefty" >User Maintenance</h1>
                </div>
                <div class="loadingSpinnerExportResults"></div>
                <div class="divTable minimalistBlack">
                    <div class="divTableHeading">
                        <div class="divTableRow">
                            <div class="divTableHead">Name</div>
                            <div class="divTableHead">Username</div>
                            <div class="divTableHead">Office</div>
                            <div class="divTableHead">Qualifications</div>
                            <div class="divTableHead">Email</div>
                            <div class="divTableHead">Targets</div>
                            <div class="divTableHead">Roles</div>
                            <div class="divTableHead">Signature</div>
                            <div class="divTableHead"></div>
                        </div>
                    </div>
                    <div class="divTableBody">
                        <div class="divTableRow">
                            <div class="divTableCell">
                                <textarea class="admin-editable-textarea" rows="2" v-model="newUser.name"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-textarea" rows="2" v-model="newUser.ntUsername"></textarea>
                            </div>
                            <div class="divTableCell">
                                <select v-model="newUser.office">
                                    <option v-for="office in offices" v-bind:value="office">{{office.name}}</option>
                                </select>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-textarea" rows="2" v-model="newUser.qualifications"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-textarea" rows="2" v-model="newUser.email"></textarea>
                            </div>
                            <div class="divTableCell">
                                <select v-model="newUser.target">
                                    <option v-for="roleTarget in revenueRoleTargetList" v-bind:value="roleTarget.code">{{roleTarget.description}}</option>
                                </select>
                            </div>
                            <div class="divTableCell">
                                <select class="roleSelect" multiple size="5" v-model="newUser.roles">
                                    <option v-for="role in roles" v-bind:value="role">{{role}}</option>
                                </select>
                            </div>
                            <div class="divTableCell"/>
                            <div class="divTableCell QVHV-admin-buttons">
                                <button class="primary" @click="saveUser(newUser, true)">Create User</button>
                            </div>
                        </div>
                        <div class="divTableRow" v-for="user in filteredUsers">
                            <div class="divTableCell">
                                <textarea class="admin-editable-textarea" rows="2" v-model="user.name"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-textarea" rows="2" v-model="user.ntUsername"></textarea>
                            </div>
                            <div class="divTableCell">
                                <select v-model="user.office">
                                    <option v-for="office in offices" v-bind:value="office">{{office.name}}</option>
                                </select>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-textarea" rows="2" v-model="user.qualifications"></textarea>
                            </div>
                            <div class="divTableCell">
                                <textarea class="admin-editable-textarea" rows="2" v-model="user.email"></textarea>
                            </div>
                            <div class="divTableCell">
                                <select v-model="user.target"> {{ user.target }}
                                    <option v-for="roleTarget in revenueRoleTargetList" v-bind:value="roleTarget.code">{{roleTarget.description}}</option>
                                </select>
                            </div>
                            <div class="divTableCell">
                                <select class="roleSelect" multiple size="5" v-model="user.roles">
                                    <option v-for="role in roles" v-bind:value="role">{{role}}</option>
                                </select>
                            </div>
                            <div class="divTableCell">
                                <span class="primaryThumb-Wrapper">
                                    <img v-on:click="openImage(user)" class="primaryPhoto_thumb" v-bind:src="user.smallSignatureUrl">
                                </span>
                            </div>
                            <div class="divTableCell QVHV-admin-buttons">
                                <button class="primary" @click="saveUser(user, false)">Update User</button>
                                <button class="primary" @click="uploadSignature(user)" v-if="!user.originalSignatureUrl">Upload Signature</button>
                                <button class="primary" @click="deleteSignatures(user, true)" v-else >Delete Signature</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <upload :max-files="1" :continueHandler="uploadDone" v-if="uploadImage" :ownerId="currentUser.id" category="ValuerSignaturePhoto"></upload>
            <warning :header="warningHeader" :message="warningMessage" close="Ok"></warning>
            <success :header="successHeader" :message="successMessage" close="Ok"></success>
        </div>

    </div>

</template>


<script>
    import { mapGetters } from 'vuex';
    import {store} from '../../DataStore.js';
    import {EventBus} from '../../EventBus.js';
    import Warning from '../common/Warning.vue';
    import Success from '../common/Success.vue';
    import Upload from '../valuation/photosAttachments/Upload.vue'
    import typeahead from '../../jquery.typeahead.js';
    import commonUtils from '../../utils/CommonUtils';

    export default {
        components: {
            Upload,
            Warning,
            Success
        },
        mixins: [commonUtils],
        computed: {
            ...mapGetters(['getCategoryClassifications']),
            revenueRoleTargetList() {
                return this.getCategoryClassifications('RevenueRoleTarget');
            },
        },
        data: function () {
            return {
                showTemplate: false,
                warningHeader: '',
                warningMessage: '',
                successHeader: '',
                successMessage: '',
                currentUser: null,
                uploadImage: false,
                allUsers: [],
                filteredUsers: [],
                newUser: {
                  roles: []
                },
                offices: [],
                roles: [],
                showSearchBox: true,
                noResultsFound: false,
                fieldLabels: {
                  "name": "Name",
                  "ntUsername": "Username",
                  "office": "Office",
                  "qualifications": "Qualifications",
                  "email": "Email",
                  "role": "Role"
                }

            }
        },
        methods: {
            deleteSignatures: function(user, showSuccessMessage){
                var self = this;
                self.currentUser = user;
                try {
                    var ajaxRequest = [];
                    console.log("Images To Delete: " + JSON.stringify(user.allImages));
                    $.each(user.allImages, function(key, obj) {
                        obj.mediaItem.status = "DELETED";
                        ajaxRequest.push(
                            $.ajaxq('deleteValuerSignaturesQueue',{
                            type: "POST",
                            url: jsRoutes.controllers.MediaController.updateMedia().url,
                            cache: false,
                            contentType: 'application/json',
                            data: JSON.stringify(obj),
                            success: function (response) {
                                console.log("Previous Image deleted successfully");
                            },
                            error: function (response) {
                                console.log(response);
                            }
                        }));
                    });

                    $.when.apply($,ajaxRequest).then(function() {
                        console.log("All signatures are deleted!");
                        self.loadImages(self.currentUser);

                        if(showSuccessMessage) {
                            setTimeout(function () {
                                self.successMessage = "Signature deleted successfully.";
                                $('.success').show();
                            }, 600);
                        }
                    });

                }catch(e){
                    console.log("error occured while deleting user signature");
                }
            },
            // FIXME?
            saveUser: function (user, isCreate) {
                var self = this;
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.ReferenceData.saveUser().url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(user),
                    dataType: "json",
                    success: function (response) {
                        if (isCreate) {
                            response.smallSignatureUrl = 'assets/images/property/addPhotos.png';
                            response.originalSignatureUrl = null;
                            response.allImages = [];
                            self.allUsers.unshift(response);
                            self.newUser = {
                                roles: [],
                                target: null,
                            };

                            self.successMessage = 'User saved successfully.';
                            setTimeout(function () {
                                $('.success').show();
                            }, 500);
                        }
                    },
                    error: function (response) {
                        console.log(response);
                        console.log(response.responseJSON);
                        const errors = response.responseJSON?.errors;
                        if (errors) {
                            self.warningMessage = [];
                            errors.forEach(function(error) {
                                self.warningMessage.push(self.fieldLabels[error.field] + ' ' + error.message);
                            });
                            setTimeout(function () {
                                $('.warning').show();
                            }, 500);
                        }else {
                            self.errorHandler(response);
                            self.warningMessage = 'Error while saving user';
                            setTimeout(function () {
                                $('.warning').show();
                            }, 500);
                        }
                    }
                });
            },
            openImage: function(user) {
                if(user.originalSignatureUrl) {
                    console.log(user.originalSignatureUrl);
                    window.open(user.originalSignatureUrl);
                }
            },
            uploadDone: function() {
                var self = this;
                self.uploadImage = false;
                console.log("Inside Upload Done method:"+self.currentUser.id+"="+JSON.stringify(self.currentUser.allImages));
                self.deleteSignatures(self.currentUser, false);
            },
            getUsers: function(param) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.ReferenceData.displayUsers().url,
                    cache: false,
                    success: function (response) {
                        self.allUsers = response;
                        self.filteredUsers = response;
                        self.filteredUsers.forEach(function (user) {
                            self.loadImages(user);
                        });
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            },
            getOffices: function(param) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.ReferenceData.displayOffices().url,
                    cache: true,
                    success: function (response) {
                        self.offices = response;
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            },
            getRoles: function(param) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.ReferenceData.getRoles().url,
                    cache: true,
                    success: function (response) {
                        self.roles = response;
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            },
            uploadSignature: function(user) {
                this.currentUser = user;
                this.uploadImage = true;
                this.showSearchBox = false;
            },
            loadImages: function(user) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.MediaController.getMediaByOwner(user.id).url,
                    cache: false,
                    success: function (response) {
                        if (response[0]) {
                            self.$set(user, 'smallSignatureUrl', response[0].mediaItem.smallImageUrl);
                            self.$set(user, 'originalSignatureUrl', response[0].mediaItem.originalImageUrl);
                            self.$set(user, 'allImages', response)
                        } else {
                            self.$set(user, 'smallSignatureUrl', 'assets/images/property/addPhotos.png');
                            self.$set(user, 'originalSignatureUrl', null);
                            self.$set(user, 'allImages', response)
                        }
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            },
            displayUsersTypeAheadResult: function(userName) {
                var self = this;
                var matchingUsers = [];
                console.log("Searching");

                if(userName === undefined || userName === 'undefined' || userName === '') {
                    self.noResultsFound = false;
                    self.filteredUsers = self.allUsers;
                } else {
                    self.allUsers.forEach(function(user) {
                        if (user.name.toLowerCase().includes(userName.toLowerCase())) {
                            matchingUsers.push(user);
                            self.noResultsFound = false;
                        }
                    });
                    if (matchingUsers.length === 0) {
                        self.noResultsFound = true;
                    }
                    self.filteredUsers = matchingUsers;
                }
            },
            addTypeAhead: function() {
                var self = this;
                $('.js-admin-typeahead').keypress(function(e){
                    if(e.which === 13){
                        var eventObj = {};
                        eventObj.searchParams = {basicSearchString: $('.admin-search-box').val(), categoryType: ''};
                        EventBus.$emit('user-maintenance-search-results', eventObj);
                    }
                });
                $('.js-admin-typeahead').mousedown(function() {
                    $(".js-admin-typeahead").eq(0).val($('.js-admin-typeahead').val()).trigger("input");
                });
                $.typeahead({
                    input: '.js-admin-typeahead',
                    dynamic: true,
                    delay: 1000,
                    minLength: 0,
                    source: {
                        "user": {
                            data: []
                        }
                    },
                    callback: {
                        onSearch: function(node, query) {
                            self.displayUsersTypeAheadResult(query);
                        }
                    }
                });
            },
            loadPage: function() {
                var self = this;
                self.showTemplate = true;
                self.uploadImage = false;
                self.getUsers();
                self.getOffices();
                self.getRoles();
                setTimeout(function(){
                    self.addTypeAhead();
                },500);
            },
            backToUserMaintenance: function () {
                const self = this;
                self.loadImages(self.currentUser);
                self.showSearchBox = true;
                self.uploadImage = false;
                setTimeout(function(){
                    self.addTypeAhead();
                },500);
            }
        },
        mounted: function () {
            var self = this;
            EventBus.$on('display-content', function(event) {
                if (event.searchType === 'user-maintenance') {
                    self.loadPage();
                } else {
                    self.showTemplate = false;
                }
            });
            EventBus.$on('user-maintenance-search-results', function(event) {
                self.displayUsersTypeAheadResult(event.searchParams.basicSearchString);
            });
        }
    }
</script>

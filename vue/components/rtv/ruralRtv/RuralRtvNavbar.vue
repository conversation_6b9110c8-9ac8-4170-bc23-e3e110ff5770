<template>
    <div class="qvToolbar-wrapper light">
        <div 
            class="md-full qvToolbar" 
            data-cy="rtvRuralIndexNavbar"
        >
            <div class="taDashboard-selector qvToolbar-leftMenu lefty">
                <select 
                    class="dashboard-internal-single-select"
                    :value="territorialAuthority"
                    @change="$emit('ta-change', parseInt($event.target.value))"
                    data-cy="rtvRuralIndexTaSelector"
                >
                    <option :value="0">Select a TA...</option>
                    <option 
                        v-for="(ta, index) in territorialAuthorities" 
                        :key="index" 
                        :value="ta.id"
                    >
                        {{ ta.id }} - {{ ta.value }}
                    </option>
                </select>
            </div>
            <ul v-if="territorialAuthority && mainIndex.length" class="qvToolbar-links lefty">
                <li 
                    :class="{'active': tab === 'MainIndex'}" 
                    @click="$emit('tab-change', 'MainIndex')"
                    data-cy="rtvRuralIndexMainIndexLink" 
                >
                    <label>Main Index</label>
                </li>
                <li 
                    :class="{'active': tab === 'SecondaryRefinements'}"
                    @click="$emit('tab-change', 'SecondaryRefinements')"
                    data-cy="rtvRuralIndexSecondaryRefinementsLink" 
                >
                    <label>Secondary Refinements</label>
                </li>
            </ul>
        </div>
        <div 
            v-if="!territorialAuthority" 
            class="bAlert bAlert-warning"
            data-cy="rtvRuralIndexSelectTaMessage" 
        >
            Please select a TA.
        </div>
        <div 
            v-if="errorMessage" 
            class="bAlert bAlert-danger"
            data-cy="rtvRuralIndexErrorMessage" 
        >
            {{ errorMessage }}
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    data: function() {
        return {
           territorialAuthorities: []
        }
    },
    props: {
        errorMessage: {
            type: String,
            required: false
        },
    },
    computed: {
        ...mapState('ruralRtv', {
           tab: 'currentTab',
           mainIndex: 'mainIndex',
           territorialAuthority: 'territorialAuthority',
        }),
    },
    methods: {
        loadTerritorialAuthorities () {
            fetch(jsRoutes.controllers.Application.fetchTerritorialAuthorities().url)
                .then(response => response.json())
                .then(data => {
                    for (const prop in data) {
                        if (data.hasOwnProperty(prop)) {
                            this.territorialAuthorities.push({ id: parseInt(prop), value: data[prop] })
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading territorial authorities:', error);
                });
        },
    },
    mounted() {
        this.loadTerritorialAuthorities();
    },
};
</script>


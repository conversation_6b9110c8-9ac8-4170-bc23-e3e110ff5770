<template>
    <div class="rtv-index-wrapper">
        <table>
            <tbody>
                <tr>
                    <td>
                        <table class="index-table">
                            <thead data-cy="secondaryRefinementsHeader">
                                <tr>
                                    <th
                                        class="left"
                                        style="height:62px;"
                                        colspan="2"
                                        rowspan="2"
                                        data-cy="secondaryRefinementsHeaderCell1"
                                    >
                                        SALES&nbsp;GROUP &amp; <br>MULTIPLE&nbsp;ROLL
                                    </th>
                                    <th
                                        class="left"
                                        style="height:62px;background-color: var(--color-darkblue-700)"
                                        colspan="2"
                                        rowspan="2"
                                        data-cy="secondaryRefinementsHeaderCell2"
                                    >
                                        SINGLE&nbsp;ROLL &amp; <br>ASSESSMENT&nbsp;RANGE
                                    </th>
                                    <th
                                        class="left"
                                        style="height:62px;background-color: var(--color-blue-800)"
                                        colspan="4"
                                        rowspan="2"
                                        data-cy="secondaryRefinementsHeaderCell3"
                                    >
                                        VALUE&nbsp;RANGES
                                    </th>
                                    <th
                                        class="left"
                                        style="height:62px;background-color: var(--color-blue-300)"
                                        colspan="3"
                                        rowspan="2"
                                        data-cy="secondaryRefinementsHeaderCell4"
                                    >
                                        CATEGORY&nbsp;&amp;&nbsp;GROUPINGS <br> <span style="padding:0;font-weight:normal;font-style:italic;text-transform:none;">Only assessments that match ALL selected options</span>
                                    </th>
                                    <th
                                        class="left"
                                        colspan="4"
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell5"
                                    >
                                        LAND AND IMPROVEMENT VALUE INDEX SET
                                    </th>
                                    <th
                                        rowspan="3"
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell6"
                                    >
                                        &nbsp;
                                    </th>
                                </tr>
                                <tr>
                                    <th
                                        colspan="2"
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell7"
                                    >
                                        LAND INDEX
                                    </th>
                                    <th
                                        colspan="2"
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell8"
                                    >
                                        IMPROVEMENT INDEX
                                    </th>
                                </tr>
                                <tr>
                                    <th
                                        colspan="2"
                                        data-cy="secondaryRefinementsHeaderCell9"
                                    >
                                        SG CODE / ROLL
                                    </th>
                                    <th
                                        style="background-color: var(--color-darkblue-700)"
                                        data-cy="secondaryRefinementsHeaderCell10"
                                    >
                                        LOW
                                    </th>
                                    <th
                                        style="background-color: var(--color-darkblue-700)"
                                        data-cy="secondaryRefinementsHeaderCell11"
                                    >
                                        HIGH
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell12"
                                    >
                                        CV LOW
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell13"
                                    >
                                        CV HIGH
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell14"
                                    >
                                        LV LOW
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell15"
                                    >
                                        LV HIGH
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-300)"
                                        data-cy="secondaryRefinementsHeaderCell16"
                                    >
                                        QV CATEGORY
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-300)"
                                        data-cy="secondaryRefinementsHeaderCell17"
                                    >
                                        GROUPING
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-300)"
                                        data-cy="secondaryRefinementsHeaderCell18"
                                    >
                                        QUALITY RATING
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell19"
                                    >
                                        %
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell20"
                                    >
                                        LUMP SUM
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell21"
                                    >
                                        %
                                    </th>
                                    <th
                                        style="background-color: var(--color-blue-800)"
                                        data-cy="secondaryRefinementsHeaderCell22"
                                    >
                                        LUMP SUM
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr
                                    v-for="(refinement, index) in refinementList"
                                    :key="refinement.refinement_id"
                                    :class="outOfDateClass(refinement.last_update)"
                                    data-cy="secondaryRefinementsRow"
                                >
                                    <td class="left">
                                        <select
                                            :value="refinement.refinement_category"
                                            :tabindex="index * fieldCount + 5"
                                            data-cy="secondaryRefinementsRefinementCategory"
                                            @change="($event) => updateCell('refinement_category', refinement.refinement_id, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(refinement)"
                                        >
                                            <option>Entire TA</option>
                                            <option>Sales Group</option>
                                            <option>Single Roll</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input
                                            v-if="refinement.refinement_category === 'Entire TA'"
                                            type="text"
                                            placeholder="-"
                                            data-cy="secondaryRefinementsNoCategoryPlaceholder"
                                            disabled
                                        >
                                        <tooltip
                                            v-if="refinement.refinement_category === 'Sales Group'"
                                            :text="tooltipText('sale_group_code', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('sale_group_code', refinement.validation_set)"
                                        >
                                            <select
                                                :value="refinement.sale_group.id"
                                                :tabindex="index * fieldCount + 6"
                                                :class="tooltipDisplayMode('sale_group_code', refinement.validation_set)"
                                                data-cy="secondaryRefinementsSalesGroup"
                                                @change="($event) => updateCell('sale_group', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                            >
                                                <option :value="null" />
                                                <option
                                                    v-for="item in saleGroups"
                                                    :key="item.id"
                                                    :value="item.id"
                                                >
                                                    {{ item.description }}
                                                </option>
                                            </select>
                                        </tooltip>
                                        <tooltip
                                            v-if="refinement.refinement_category === 'Single Roll'"
                                            :text="tooltipText('roll_number', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('roll_number', refinement.validation_set)"
                                        >
                                            <select
                                                :value="refinement.roll.id"
                                                :tabindex="index * fieldCount + 6"
                                                :class="tooltipDisplayMode('roll_number', refinement.validation_set)"
                                                data-cy="secondaryRefinementsRollNumber"
                                                @change="($event) => updateCell('roll', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                            >
                                                <option :value="null" />
                                                <option
                                                    v-for="item in rolls"
                                                    :key="item.id"
                                                    :value="item.id"
                                                >
                                                    {{ item.code }}
                                                </option>
                                            </select>
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('assessment_min', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('assessment_min', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="assessment_min"
                                                :value="refinement.assessment_min"
                                                format="0"
                                                min="0"
                                                placeholder="-"
                                                :tabindex="index * fieldCount + 7"
                                                :class="tooltipDisplayMode('assessment_min', refinement.validation_set)"
                                                :disabled="!refinement.roll || !refinement.roll.id"
                                                data-cy="secondaryRefinementsAssessmentMin"
                                                @blur="($event) => updateCell('assessment_min', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('assessment_min', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('assessment_min', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('assessment_max', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('assessment_max', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="assessment_max"
                                                :value="refinement.assessment_max"
                                                format="0"
                                                min="0"
                                                placeholder="-"
                                                :tabindex="index * fieldCount + 8"
                                                :class="tooltipDisplayMode('assessment_max', refinement.validation_set)"
                                                :disabled="!refinement.roll || !refinement.roll.id"
                                                data-cy="secondaryRefinementsAssessmentMax"
                                                @blur="($event) => updateCell('assessment_max', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('assessment_max', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('assessment_max', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('cv_min', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('cv_min', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="cv_min"
                                                :value="refinement.cv_min"
                                                format="$0,0"
                                                step="1000"
                                                min="0"
                                                placeholder="$ -"
                                                :tabindex="index * fieldCount + 9"
                                                :class="tooltipDisplayMode('cv_min', refinement.validation_set)"
                                                data-cy="secondaryRefinementsCvMin"
                                                @blur="($event) => updateCell('cv_min', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('cv_min', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('cv_min', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('cv_max', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('cv_max', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="cv_max"
                                                :value="refinement.cv_max"
                                                format="$0,0"
                                                step="1000"
                                                min="0"
                                                placeholder="$ -"
                                                :tabindex="index * fieldCount + 10"
                                                :class="tooltipDisplayMode('cv_max', refinement.validation_set)"
                                                data-cy="secondaryRefinementsCvMax"
                                                @blur="($event) => updateCell('cv_max', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('cv_max', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('cv_max', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('lv_min', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('lv_min', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="lv_min"
                                                :value="refinement.lv_min"
                                                format="$0,0"
                                                step="1000"
                                                min="0"
                                                placeholder="$ -"
                                                :tabindex="index * fieldCount + 11"
                                                :class="tooltipDisplayMode('lv_min', refinement.validation_set)"
                                                data-cy="secondaryRefinementsLvMin"
                                                @blur="($event) => updateCell('lv_min', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('lv_min', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('lv_min', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('lv_max', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('lv_max', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="lv_max"
                                                :value="refinement.lv_max"
                                                format="$0,0"
                                                step="1000"
                                                min="0"
                                                placeholder="$ -"
                                                :tabindex="index * fieldCount + 12"
                                                :class="tooltipDisplayMode('lv_max', refinement.validation_set)"
                                                data-cy="secondaryRefinementsLvMax"
                                                @blur="($event) => updateCell('lv_max', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('lv_max', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('lv_max', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('qv_category', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('qv_category', refinement.validation_set)"
                                        >
                                            <input
                                                ref="qv_category"
                                                type="text"
                                                :value="refinement.qv_category"
                                                :tabindex="index * fieldCount + 13"
                                                maxlength="10"
                                                :class="tooltipDisplayMode('qv_category', refinement.validation_set)"
                                                data-cy="secondaryRefinementsQvCategory"
                                                @keypress.enter.exact.prevent="focusCell('qv_category', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('qv_category', 'up', index)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @change="($event) => updateCell('qv_category', refinement.refinement_id, $event.srcElement.value)"
                                            >
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('groupings', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('groupings', refinement.validation_set)"
                                        >
                                            <multiselect
                                                v-model="refinement.groupings"
                                                :options="groupings"
                                                track-by="code"
                                                label="description"
                                                placeholder=""
                                                select-label=""
                                                deselect-label=""
                                                :multiple="true"
                                                :tabindex="index * fieldCount + 14"
                                                :class="tooltipDisplayMode('groupings', refinement.validation_set)"
                                                data-cy="secondaryRefinementsGrouping"
                                                @focus="updateToolbarMessage(refinement)"
                                                @input="updateToolbarMessage(refinement)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('quality_ratings', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('quality_ratings', refinement.validation_set)"
                                        >
                                            <multiselect
                                                v-model="refinement.quality_ratings"
                                                :options="qualityRatings"
                                                track-by="code"
                                                label="description"
                                                placeholder=""
                                                select-label=""
                                                deselect-label=""
                                                :multiple="true"
                                                :tabindex="index * fieldCount + 15"
                                                :class="tooltipDisplayMode('quality_ratings', refinement.validation_set)"
                                                data-cy="secondaryRefinementsQualityRating"
                                                @focus="updateToolbarMessage(refinement)"
                                                @input="updateToolbarMessage(refinement)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('lv_percent_index', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('lv_percent_index', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="lv_percent_index"
                                                :value="refinement.lv_percent_index"
                                                format="0.00"
                                                min="0"
                                                step="0.1"
                                                :tabindex="index * fieldCount + 16"
                                                :class="tooltipDisplayMode('lv_percent_index', refinement.validation_set)"
                                                data-cy="secondaryRefinementsLvPercentIndex"
                                                @blur="($event) => updateCell('lv_percent_index', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('lv_lump_sum_index', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('lv_lump_sum_index', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="lv_lump_sum_index"
                                                :value="refinement.lv_lump_sum_index"
                                                format="$0,0"
                                                step="1000"
                                                :tabindex="index * fieldCount + 17"
                                                :class="tooltipDisplayMode('lv_lump_sum_index', refinement.validation_set)"
                                                data-cy="secondaryRefinementsLvLumpSumIndex"
                                                @blur="($event) => updateCell('lv_lump_sum_index', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('lv_lump_sum_index', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('lv_lump_sum_index', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('vi_percent_index', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('vi_percent_index', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="vi_percent_index"
                                                :value="refinement.vi_percent_index"
                                                format="0.00"
                                                min="0"
                                                step="0.1"
                                                :tabindex="index * fieldCount + 18"
                                                :class="tooltipDisplayMode('vi_percent_index', refinement.validation_set)"
                                                data-cy="secondaryRefinementsViPercentIndex"
                                                @blur="($event) => updateCell('vi_percent_index', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('vi_percent_index', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('vi_percent_index', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td>
                                        <tooltip
                                            :text="tooltipText('vi_lump_sum_index', refinement.validation_set)"
                                            :display-mode="tooltipDisplayMode('vi_lump_sum_index', refinement.validation_set)"
                                        >
                                            <number-input
                                                ref="vi_lump_sum_index"
                                                :value="refinement.vi_lump_sum_index"
                                                format="$0,0"
                                                step="1000"
                                                :tabindex="index * fieldCount + 19"
                                                :class="tooltipDisplayMode('vi_lump_sum_index', refinement.validation_set)"
                                                data-cy="secondaryRefinementsViLumpSumIndex"
                                                @blur="($event) => updateCell('vi_lump_sum_index', refinement.refinement_id, $event.srcElement.value)"
                                                @focus="updateToolbarMessage(refinement)"
                                                @keypress.enter.exact.prevent="focusCell('vi_lump_sum_index', 'down', index)"
                                                @keypress.enter.shift.exact.prevent="focusCell('vi_lump_sum_index', 'up', index)"
                                            />
                                        </tooltip>
                                    </td>
                                    <td
                                        data-cy="secondaryRefinementsRemoveRefinementButton"
                                        @click="removeRefinementWarning(refinement.refinement_id)"
                                    >
                                        <span class="button-cell">
                                            <i class="saRow-remove material-icons">
                                                
                                            </i>
                                        </span>
                                    </td>
                                </tr>
                                <tr data-cy="secondaryRefinementsNewRow">
                                    <td class="left">
                                        <select
                                            ref="new_refinement_category"
                                            v-model="newRefinement.refinement_category"
                                            :tabindex="secondaryRefinements.length * fieldCount + 5"
                                            data-cy="secondaryRefinementsNewRefinementCategory"
                                            @keypress.tab.exact.prevent=""
                                            @change="($event) => updateCell('refinement_category', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                        >
                                            <option>Entire TA</option>
                                            <option>Sales Group</option>
                                            <option>Single Roll</option>
                                        </select>
                                    </td>
                                    <td>
                                        <input
                                            v-if="newRefinement.refinement_category === 'Entire TA'"
                                            type="text"
                                            placeholder="-"
                                            data-cy="secondaryRefinementsNewNoCategoryPlaceholder"
                                            disabled
                                        >
                                        <select
                                            v-if="newRefinement.refinement_category === 'Sales Group'"
                                            :value="newRefinement.sale_group.id"
                                            :tabindex="secondaryRefinements.length * fieldCount + 6"
                                            data-cy="secondaryRefinementsNewSalesGroup"
                                            @change="($event) => updateCell('sale_group', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                        >
                                            <option :value="null" />
                                            <option
                                                v-for="item in saleGroups"
                                                :key="item.id"
                                                :value="item.id"
                                            >
                                                {{ item.description }}
                                            </option>
                                        </select>
                                        <select
                                            v-if="newRefinement.refinement_category === 'Single Roll'"
                                            :value="newRefinement.roll.id"
                                            :tabindex="secondaryRefinements.length * fieldCount + 6"
                                            data-cy="secondaryRefinementsNewRollNumber"
                                            @change="($event) => updateCell('roll', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                        >
                                            <option :value="null" />
                                            <option
                                                v-for="item in rolls"
                                                :key="item.id"
                                                :value="item.id"
                                            >
                                                {{ item.code }}
                                            </option>
                                        </select>
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_assessment_min"
                                            :value="newRefinement.assessment_min"
                                            format="0"
                                            min="0"
                                            placeholder="-"
                                            :tabindex="secondaryRefinements.length * fieldCount + 7"
                                            :disabled="!newRefinement.roll || !newRefinement.roll.id"
                                            data-cy="secondaryRefinementsNewAssessmentMin"
                                            @blur="($event) => updateCell('assessment_min', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('assessment_min', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_assessment_max"
                                            :value="newRefinement.assessment_max"
                                            format="0"
                                            min="0"
                                            placeholder="-"
                                            :tabindex="secondaryRefinements.length * fieldCount + 8"
                                            :disabled="!newRefinement.roll || !newRefinement.roll.id"
                                            data-cy="secondaryRefinementsNewAssessmentMax"
                                            @blur="($event) => updateCell('assessment_max', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('assessment_max', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_cv_min"
                                            :value="newRefinement.cv_min"
                                            format="$0,0"
                                            step="1000"
                                            min="0"
                                            placeholder="$ -"
                                            :tabindex="secondaryRefinements.length * fieldCount + 9"
                                            data-cy="secondaryRefinementsNewCvMin"
                                            @blur="($event) => updateCell('cv_min', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('cv_min', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_cv_max"
                                            :value="newRefinement.cv_max"
                                            format="$0,0"
                                            step="1000"
                                            min="0"
                                            placeholder="$ -"
                                            :tabindex="secondaryRefinements.length * fieldCount + 10"
                                            data-cy="secondaryRefinementsNewCvMax"
                                            @blur="($event) => updateCell('cv_max', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('cv_max', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_lv_min"
                                            :value="newRefinement.lv_min"
                                            format="$0,0"
                                            step="1000"
                                            min="0"
                                            placeholder="$ -"
                                            :tabindex="secondaryRefinements.length * fieldCount + 11"
                                            data-cy="secondaryRefinementsNewLvMin"
                                            @blur="($event) => updateCell('lv_min', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('lv_min', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_lv_max"
                                            :value="newRefinement.lv_max"
                                            format="$0,0"
                                            step="1000"
                                            min="0"
                                            placeholder="$ -"
                                            :tabindex="secondaryRefinements.length * fieldCount + 12"
                                            data-cy="secondaryRefinementsNewLvMax"
                                            @blur="($event) => updateCell('lv_max', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('lv_max', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <input
                                            ref="new_qv_category"
                                            v-model="newRefinement.qv_category"
                                            type="text"
                                            :tabindex="secondaryRefinements.length * fieldCount + 13"
                                            maxlength="10"
                                            data-cy="secondaryRefinementsNewQvCategory"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @blur="($event) => updateCell('qv_category', null, $event.srcElement.value)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('qv_category', 'up')"
                                        >
                                    </td>
                                    <td>
                                        <multiselect
                                            v-model="newRefinement.groupings"
                                            :options="groupings"
                                            track-by="code"
                                            label="description"
                                            placeholder=""
                                            select-label=""
                                            deselect-label=""
                                            :multiple="true"
                                            :tabindex="secondaryRefinements.length * fieldCount + 14"
                                            data-cy="secondaryRefinementsNewGrouping"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @input="updateToolbarMessage(newRefinement)"
                                        />
                                    </td>
                                    <td>
                                        <multiselect
                                            v-model="newRefinement.quality_ratings"
                                            :options="qualityRatings"
                                            track-by="code"
                                            label="description"
                                            placeholder=""
                                            select-label=""
                                            deselect-label=""
                                            :multiple="true"
                                            :tabindex="secondaryRefinements.length * fieldCount + 15"
                                            data-cy="secondaryRefinementsNewQualityRating"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @input="updateToolbarMessage(newRefinement)"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_lv_percent_index"
                                            :value="newRefinement.lv_percent_index"
                                            format="0.00"
                                            min="0"
                                            step="0.1"
                                            :tabindex="secondaryRefinements.length * fieldCount + 16"
                                            data-cy="secondaryRefinementsNewLvPercentIndex"
                                            @blur="($event) => updateCell('lv_percent_index', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('lv_percent_index', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_lv_lump_sum_index"
                                            :value="newRefinement.lv_lump_sum_index"
                                            format="$0,0"
                                            step="1000"
                                            :tabindex="secondaryRefinements.length * fieldCount + 17"
                                            data-cy="secondaryRefinementsNewLvLumpSumIndex"
                                            @blur="($event) => updateCell('lv_lump_sum_index', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('lv_lump_sum_index', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_vi_percent_index"
                                            :value="newRefinement.vi_percent_index"
                                            format="0.00"
                                            min="0"
                                            step="0.1"
                                            :tabindex="secondaryRefinements.length * fieldCount + 18"
                                            data-cy="secondaryRefinementsNewViPercentIndex"
                                            @blur="($event) => updateCell('vi_percent_index', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keypress.enter.shift.exact.prevent="focusCell('vi_percent_index', 'up')"
                                        />
                                    </td>
                                    <td>
                                        <number-input
                                            ref="new_vi_lump_sum_index"
                                            :value="newRefinement.vi_lump_sum_index"
                                            format="$0,0"
                                            step="1000"
                                            :tabindex="secondaryRefinements.length * fieldCount + 19"
                                            data-cy="secondaryRefinementsNewViLumpSumIndex"
                                            @blur="($event) => updateCell('vi_lump_sum_index', null, $event.srcElement.value)"
                                            @focus="updateToolbarMessage(newRefinement)"
                                            @keypress.enter.exact.prevent="($event) => addRefinement($event.srcElement)"
                                            @keydown.tab.exact.prevent="($event) => addRefinement($event.srcElement, true)"
                                            @keypress.enter.shift.exact.prevent="focusCell('vi_lump_sum_index', 'up')"
                                        />
                                    </td>
                                    <td
                                        data-cy="secondaryRefinementsAddRefinementButton"
                                        @click="addRefinement()"
                                    >
                                        <span class="button-cell">
                                            <i class="saRow-add material-icons">
                                                
                                            </i>
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
import Multiselect from 'vue-multiselect';
import { mapState } from 'vuex';
import moment from 'moment';

export default {
    components: {
        Multiselect,
        Tooltip: () => import('../../common/Tooltip.vue'),
        NumberInput: () => import('../../common/form/NumberInput.vue'),
    },
    data() {
        return {
            fieldCount: 15,
            focusedRefinement: {},
            numericFields: [
                'roll_number',
                'assessment_min',
                'assessment_max',
                'cv_min',
                'cv_max',
                'lv_min',
                'lv_max',
                'lv_percent_index',
                'lv_lump_sum_index',
                'vi_percent_index',
                'vi_lump_sum_index',
            ],
            newRefinement: {
                refinement_id: -1,
                refinement_category: 'Entire TA',
                sale_group: {
                    id: null,
                    code: null,
                    description: null,
                },
                roll: {
                    id: null,
                    code: null,
                },
                assessment_min: null,
                assessment_max: null,
                cv_min: null,
                cv_max: null,
                lv_min: null,
                lv_max: null,
                qv_category: null,
                groupings: null,
                quality_ratings: null,
                lv_percent_index: 1,
                lv_lump_sum_index: 0,
                vi_percent_index: 1,
                vi_lump_sum_index: 0,
                sort: 0,
                state: 'new',
            },
        };
    },
    computed: {
        ...mapState('ruralRtv', {
            mainIndex: 'mainIndex',
            secondaryRefinements: 'secondaryRefinements',
            assessments: 'assessments',
            rolls: 'rolls',
            saleGroups: 'saleGroups',
            groupings: 'groupings',
            qualityRatings: 'qualityRatings',
        }),
        refinementList() {
            return this.secondaryRefinements
                .filter(obj => obj.state !== 'deleted')
                .sort((a, b) => ((a.sort > b.sort) ? 1 : -1));
        },
        toolbarMessage() {
            const affectedAssessments = this.assessments.filter(
                assessment => (
                    this.focusedRefinement.refinement_category === 'Single Roll' ? (this.focusedRefinement.roll && assessment.roll_number === this.focusedRefinement.roll.code) : true)
                    && (this.focusedRefinement.refinement_category === 'Sales Group' ? (this.focusedRefinement.sale_group && assessment.sale_group_code === this.focusedRefinement.sale_group.code) : true)
                    && assessment.assessment_number >= (this.focusedRefinement.assessment_min
                        ? this.focusedRefinement.assessment_min
                        : 0
                    )
                    && assessment.assessment_number <= (
                        this.focusedRefinement.assessment_max
                            ? this.focusedRefinement.assessment_max
                            : 99999999999
                    )
                    && assessment.cv >= (
                        this.focusedRefinement.cv_min
                            ? this.focusedRefinement.cv_min
                            : 0
                    )
                    && assessment.cv <= (
                        this.focusedRefinement.cv_max
                            ? this.focusedRefinement.cv_max
                            : 9999999999999
                    )
                    && assessment.lv >= (
                        this.focusedRefinement.lv_min
                            ? this.focusedRefinement.lv_min
                            : 0
                    )
                    && assessment.lv <= (
                        this.focusedRefinement.lv_max
                            ? this.focusedRefinement.lv_max
                            : 9999999999999
                    )
                    && (this.focusedRefinement.qv_category
                        ? assessment.qv_category
                            .startsWith(this.focusedRefinement.qv_category.toUpperCase())
                        : true
                    )
                    && (this.focusedRefinement.groupings
                        && this.focusedRefinement.groupings.length > 0
                        ? this.focusedRefinement.groupings
                            .map(i => i.groupingId).includes(assessment.grouping_id)
                        : true
                    )
                    && (this.focusedRefinement.quality_ratings
                        && this.focusedRefinement.quality_ratings.length > 0
                        ? this.focusedRefinement.quality_ratings
                            .map(i => parseInt(i.code, 10)).includes(assessment.quality_rating_id)
                        : true
                    ),
            );

            return { message: `Total assessments affected: ${affectedAssessments.length}` };
        },
    },
    watch: {
        toolbarMessage(newVal) {
            this.$emit('cell-focus', newVal);
        },
    },
    methods: {
        addRefinement(srcElement, focusStart) {
            if (srcElement) {
                srcElement.blur();
            }
            this.newRefinement.refinement_id = (this.secondaryRefinements
                .filter(o => o.refinement_id < 0).length
                ? Math.min(...this.secondaryRefinements.map(o => o.refinement_id))
                : 0) - 1;
            this.newRefinement.sort = (this.secondaryRefinements.length
                ? Math.max(...this.secondaryRefinements.map(o => o.sort))
                : 0) + 1;
            this.secondaryRefinements.push(JSON.parse(JSON.stringify(this.newRefinement)));
            this.resetNewRefinement();
            if (focusStart) {
                this.$refs.new_refinement_category.focus();
                return;
            }
            this.$nextTick(() => {
                if (srcElement && !srcElement.disabled) {
                    srcElement.focus();
                }
            });
        },
        removeRefinementWarning(refinementId) {
            const refinementIndex = this.secondaryRefinements
                .findIndex(item => item.refinement_id === refinementId);

            if (refinementIndex < 0) {
                return;
            }

            if (this.refinementIsBlank(this.secondaryRefinements[refinementIndex])) {
                this.removeRefinement(refinementId);
                return;
            }

            let warningMessage = 'This will delete the indexing row for ';
            switch (this.secondaryRefinements[refinementIndex].refinement_category) {
            case 'Sales Group':
                warningMessage += this.secondaryRefinements[refinementIndex].sale_group.code
                    ? `Sales Group ${this.saleGroups.find(sg => sg.code === this.secondaryRefinements[refinementIndex].sale_group.code).description}.`
                    : 'the Sales Group.';
                break;
            case 'Single Roll':
                warningMessage += this.secondaryRefinements[refinementIndex].roll.code
                    ? `Roll ${this.secondaryRefinements[refinementIndex].roll.code}.`
                    : 'the Roll.';
                break;
            default:
                warningMessage += 'the TA.';
                break;
            }
            warningMessage += '\n\nAre you sure?';

            this.$emit('set-modal', {
                mode: 'warning',
                isOpen: true,
                heading: 'Delete Secondary Refinement',
                message: warningMessage,
                messages: [],
                cancelText: 'No, return to Secondary Refinements',
                cancelAction: () => { },
                confirmText: 'Yes, Delete row',
                confirmAction: () => { this.removeRefinement(refinementId); },
                code: 'DELETE_WARNING',
            });
        },
        removeRefinement(refinementId) {
            const refinementIndex = this.secondaryRefinements
                .findIndex(item => item.refinement_id === refinementId);

            if (refinementIndex < 0) {
                return;
            }
            if (this.secondaryRefinements[refinementIndex].refinement_id > 0) {
                this.secondaryRefinements[refinementIndex].state = 'deleted';
                return;
            }
            this.secondaryRefinements.splice(refinementIndex, 1);
        },
        resetNewRefinement() {
            this.newRefinement = {
                refinement_id: -1,
                refinement_category: 'Entire TA',
                sale_group: {
                    id: null,
                    code: null,
                    description: null,
                },
                roll: {
                    id: null,
                    code: null,
                },
                assessment_min: null,
                assessment_max: null,
                cv_min: null,
                cv_max: null,
                lv_min: null,
                lv_max: null,
                qv_category: null,
                groupings: null,
                quality_ratings: null,
                lv_percent_index: 1,
                lv_lump_sum_index: 0,
                vi_percent_index: 1,
                vi_lump_sum_index: 0,
                sort: 0,
                state: 'new',
            };
        },
        focusCell(field, direction, index) {
            const disableableFields = ['assessment_min', 'assessment_max'];

            if (!disableableFields.includes(field)) {
                // default behaviour: focus next field in the desired direction
                if (direction === 'down' && index < this.secondaryRefinements.length) {
                    if (index === this.secondaryRefinements.length - 1) {
                        this.$refs[`new_${field}`].focus();
                        return;
                    }
                    this.$refs[field][index + 1].focus();
                    return;
                }
                if (direction === 'up' && (index === undefined || index > 0)) {
                    if (index === undefined) {
                        this.$refs[field][this.secondaryRefinements.length - 1].focus();
                        return;
                    }
                    this.$refs[field][index - 1].focus();
                    return;
                }
                return;
            }
            // next field may be disabled: find next non-disabled field to focus
            if (direction === 'down' && index < this.secondaryRefinements.length) {
                const targetIndex = this.secondaryRefinements
                    .slice(index + 1)
                    .findIndex(i => i.roll_number !== null);

                if (targetIndex >= 0 && targetIndex + index + 1 < this.secondaryRefinements.length) {
                    this.$refs[field][targetIndex + index + 1].focus();
                    return;
                }
                if (this.newRefinement.roll_number !== null) {
                    this.$refs[`new_${field}`].focus();
                    return;
                }
            }
            if (direction === 'up' && (index === undefined || index > 0)) {
                const targetArray = (index === undefined)
                    ? JSON.parse(JSON.stringify(this.secondaryRefinements))
                    : this.secondaryRefinements.slice(0, index);
                const targetIndex = targetArray.reverse().findIndex(i => i.roll_number !== null);
                if (targetIndex !== null && targetArray.length - targetIndex - 1 >= 0) {
                    this.$refs[field][targetArray.length - targetIndex - 1].focus();
                }
            }
        },
        updateCell(field, refinementId, value) {
            let updateIndexValue = value === '' ? null : value;
            if (this.numericFields.includes(field)) {
                updateIndexValue = null;
                if (field.includes('percent') || field.includes('lump_sum')) {
                    updateIndexValue = field.includes('lump_sum') ? 0 : 1;
                }
                if (value && value !== '' && !Number.isNaN(value)) {
                    updateIndexValue = field.includes('lump_sum') ? 0 : 1;
                    updateIndexValue = Math.round(parseFloat(value) * 100) / 100;
                    if (!field.includes('percent')) {
                        updateIndexValue = Math.round(updateIndexValue);
                    }

                    if (!field.includes('lump_sum') && updateIndexValue <= 0) {
                        updateIndexValue = field.includes('percent') ? 1 : null;
                    }
                }
            }
            if (field.includes('qv_category') && updateIndexValue && updateIndexValue.length > 0) {
                updateIndexValue = updateIndexValue
                    .toUpperCase()
                    .split('')
                    .filter(c => (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9'))
                    .join('');
            }
            if (field === 'sale_group') {
                updateIndexValue = this.saleGroups.find(i => i.id === parseInt(value, 10));
                if (!updateIndexValue) {
                    updateIndexValue = { id: null, code: null, description: null };
                }
            }
            if (field === 'roll') {
                updateIndexValue = this.rolls.find(i => i.id === parseInt(value, 10));
                if (!updateIndexValue) {
                    updateIndexValue = { id: null, code: null };
                }
            }

            let refinement = this.newRefinement;

            if (refinementId !== null) {
                const refinementIndex = this.secondaryRefinements
                    .findIndex(i => i.refinement_id === refinementId);
                refinement = this.secondaryRefinements[refinementIndex];
            }

            refinement[field] = updateIndexValue;

            if (field === 'refinement_category') {
                refinement.sale_group = refinement.refinement_category === 'Sales Group' ? refinement.sale_group : { id: null, code: null, description: null };
                refinement.roll = refinement.refinement_category === 'Single Roll' ? refinement.roll : { id: null, code: null };
                refinement.assessment_min = null;
                refinement.assessment_max = null;
            }
        },
        updateToolbarMessage(refinement) {
            this.focusedRefinement = refinement;
        },
        refinementIsBlank(refinement) {
            if (refinement.assessment_min) {
                return false;
            }
            if (refinement.assessment_max) {
                return false;
            }
            if (refinement.cv_min) {
                return false;
            }
            if (refinement.cv_max) {
                return false;
            }
            if (refinement.lv_min) {
                return false;
            }
            if (refinement.lv_max) {
                return false;
            }
            if (refinement.qv_category && refinement.qv_category.length) {
                return false;
            }
            if (refinement.groupings && refinement.groupings.length) {
                return false;
            }
            if (refinement.quality_ratings && refinement.quality_ratings.length) {
                return false;
            }
            return true;
        },
        saveLastRefinement() {
            if (!this.refinementIsBlank(this.newRefinement)) {
                this.addRefinement();
            }
        },
        getValidationErrors(field, validationSet) {
            if (!validationSet || !validationSet.length) {
                return [];
            }
            return validationSet.filter(i => i.field === field);
        },
        tooltipText(field, validationSet) {
            const errors = this.getValidationErrors(field, validationSet);
            if (!errors || !errors.length) {
                return null;
            }
            if (errors.filter(i => i.severity === 'error').length > 0) {
                return errors.filter(i => i.severity === 'error').map(i => i.message).join(', ');
            }
            return errors.filter(i => i.severity === 'warning').map(i => i.message).join(', ');
        },
        tooltipDisplayMode(field, validationSet) {
            const errors = this.getValidationErrors(field, validationSet);
            if (!errors || !errors.length) {
                return null;
            }
            if (errors.filter(i => i.severity === 'error').length > 0) {
                return 'error';
            }
            return 'warning';
        },
        outOfDateClass(lastUpdate) {
            if (lastUpdate === null) {
                return null;
            }
            const daysSinceUpdate = moment().diff(moment(lastUpdate), 'days');
            return { 'out-of-date': daysSinceUpdate >= 30 };
        },
    },
};

</script>

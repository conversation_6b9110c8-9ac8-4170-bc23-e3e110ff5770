<template>
    <tbody data-cy="mainIndexBody2">
        <tr
            v-for="(roll, rollIndex) in mainIndex"
            :key="'cells'+roll.roll_id"
        >
            <template v-for="category in categories">
                <td
                    :key="roll.roll_id + category.code + '1'"
                    :class="outOfDateClass(roll[category.code].last_update)"
                    data-cy="mainIndexCells"
                >
                    <tooltip
                        :text="tooltipText('lv_percent_index', roll[category.code].validation_set)"
                        :display-mode="tooltipDisplayMode('lv_percent_index', roll[category.code].validation_set)"
                    >
                        <number-input
                            :ref="'lv_percent_index_' + category.code + '_' + rollIndex"
                            :value="roll[category.code].lv_percent_index"
                            data-cy="mainIndexLvPercentInput"
                            format="0.00"
                            step="0.1"
                            min="0"
                            :disabled="roll[category.code].assessment_count === 0"
                            :class="tooltipDisplayMode('lv_percent_index', roll[category.code].validation_set)"
                            @focus="cellFocus(roll.roll_id, roll.roll_number, category.code, roll[category.code].assessment_count, roll.total_assessments)"
                            @blur="($event) => updateCell('lv_percent_index', roll.roll_id, category.code, $event.srcElement.value)"
                            @keyup.enter.exact.prevent="onEnter('lv_percent_index', category.code, rollIndex, 'down')"
                            @keyup.enter.shift.exact.prevent="onEnter('lv_percent_index', category.code, rollIndex, 'up')"
                        />
                    </tooltip>
                </td>
                <td
                    :key="roll.roll_id + category.code + '2'"
                    :class="outOfDateClass(roll[category.code].last_update)"
                    data-cy="mainIndexCells"
                >
                    <tooltip
                        :text="tooltipText('lv_lump_sum_index', roll[category.code].validation_set)" 
                        :display-mode="tooltipDisplayMode('lv_lump_sum_index', roll[category.code].validation_set)" 
                    >
                        <number-input
                            :ref="'lv_lump_sum_index_' + category.code + '_' + rollIndex"
                            :value="roll[category.code].lv_lump_sum_index"
                            data-cy="mainIndexLvLumpSumInput"
                            format="$0,0"
                            step="1000"
                            :disabled="roll[category.code].assessment_count === 0"
                            :class="tooltipDisplayMode('lv_lump_sum_index', roll[category.code].validation_set)"
                            @focus="cellFocus(roll.roll_id, roll.roll_number, category.code, roll[category.code].assessment_count, roll.total_assessments)"
                            @blur="($event) => updateCell('lv_lump_sum_index', roll.roll_id, category.code, $event.srcElement.value)"
                            @keyup.enter.exact.prevent="onEnter('lv_lump_sum_index', category.code, rollIndex, 'down')"
                            @keyup.enter.shift.exact.prevent="onEnter('lv_lump_sum_index', category.code, rollIndex, 'up')"
                        />
                    </tooltip>
                </td>
                <td
                    :key="roll.roll_id + category.code + '3'"
                    :class="outOfDateClass(roll[category.code].last_update)"
                    data-cy="mainIndexCells"
                >
                    <tooltip
                        :text="tooltipText('vi_percent_index', roll[category.code].validation_set)" 
                        :display-mode="tooltipDisplayMode('vi_percent_index', roll[category.code].validation_set)" 
                    >
                        <number-input
                            :ref="'vi_percent_index_' + category.code + '_' + rollIndex"
                            :value="roll[category.code].vi_percent_index"
                            data-cy="mainIndexViPercentInput"
                            format="0.00"
                            step="0.1"
                            min="0"
                            :disabled="roll[category.code].assessment_count === 0"
                            :class="tooltipDisplayMode('vi_percent_index', roll[category.code].validation_set)"
                            @focus="cellFocus(roll.roll_id, roll.roll_number, category.code, roll[category.code].assessment_count, roll.total_assessments)"
                            @blur="($event) => updateCell('vi_percent_index', roll.roll_id, category.code, $event.srcElement.value)"
                            @keyup.enter.exact.prevent="onEnter('vi_percent_index', category.code, rollIndex, 'down')"
                            @keyup.enter.shift.exact.prevent="onEnter('vi_percent_index', category.code, rollIndex, 'up')"
                        />
                    </tooltip>
                </td>
                <td
                    :key="roll.roll_id + category.code + '4'"
                    :class="outOfDateClass(roll[category.code].last_update)"
                    data-cy="mainIndexCells"
                >
                    <tooltip
                        :text="tooltipText('vi_lump_sum_index', roll[category.code].validation_set)"
                        :display-mode="tooltipDisplayMode('vi_lump_sum_index', roll[category.code].validation_set)"
                    >
                        <number-input
                            :ref="'vi_lump_sum_index_' + category.code + '_' + rollIndex"
                            :value="roll[category.code].vi_lump_sum_index"
                            data-cy="mainIndexViLumpSumInput"
                            format="$0,0"
                            step="1000"
                            :disabled="roll[category.code].assessment_count === 0"
                            :class="tooltipDisplayMode('vi_lump_sum_index', roll[category.code].validation_set)"
                            @focus="cellFocus(roll.roll_id, roll.roll_number, category.code, roll[category.code].assessment_count, roll.total_assessments)"
                            @blur="($event) => updateCell('vi_lump_sum_index', roll.roll_id, category.code, $event.srcElement.value)"
                            @keyup.enter.exact.prevent="onEnter('vi_lump_sum_index', category.code, rollIndex, 'down')"
                            @keyup.enter.shift.exact.prevent="onEnter('vi_lump_sum_index', category.code, rollIndex, 'up')"
                        />
                    </tooltip>
                </td>
            </template>
        </tr>
    </tbody>
</template>

<script>
import { mapState } from 'vuex';
import moment from 'moment';

export default {
    components: {
        NumberInput: () => import('../../../common/form/NumberInput.vue'),
        Tooltip: () => import('../../../common/Tooltip.vue'),
    },
    data() {
        return {
            focusedCategory: '',
            focusedRoll: 0,
        };
    },
    computed: {
        ...mapState('ruralRtv', {
            mainIndex: 'mainIndex',
            categories: 'baseCategories',
        }),
    },
    methods: {
        cellFocus(rollId, rollNumber, category, assessmentCount, assessmentTotal) {
            if (this.focusedCategory !== category || this.focusedRoll !== rollId) {
                this.focusedCategory = category;
                this.focusedRoll = rollId;

                const baseCategory = this.categories.find(c => c.code === category);
                const output = { message: `${baseCategory.description} assessments in Roll ${rollNumber}: ${assessmentCount} out of ${assessmentTotal}` };

                this.$parent.$emit('cell-focus', output);
            }
        },
        updateCell(field, roll, category, value) {
            let updateIndexValue = field.includes('lump_sum') ? 0 : 1;
            if (value && value !== '' && !Number.isNaN(value)) {
                updateIndexValue = Math.round(parseFloat(value) * 100) / 100;
                if (field.includes('lump_sum')) {
                    updateIndexValue = Math.round(updateIndexValue);
                }
                if (field.includes('percent') && updateIndexValue <= 0) {
                    updateIndexValue = 1;
                }
            }

            const index = this.mainIndex.find(i => i.roll_id === roll);
            if (index) {
                index[category][field] = updateIndexValue;
            }
        },
        onEnter(field, category, index, direction) {
            let refId = `${field}_${category}_`;
            if (direction === 'up' && index > 0) {
                const targetArray = this.mainIndex.slice(0, index);
                const targetIndex = targetArray.reverse().findIndex(i => i[category].assessment_count > 0);
                if (targetArray.length - targetIndex - 1 >= 0) {
                    refId += (targetArray.length - targetIndex - 1);
                    this.$refs[refId][0].focus();
                }
            }
            if (direction === 'down' && index < this.mainIndex.length) {
                const targetIndex = this.mainIndex.slice(index + 1).findIndex(i => i[category].assessment_count > 0);
                if (targetIndex + index + 1 < this.mainIndex.length) {
                    refId += (targetIndex + index + 1);
                    this.$refs[refId][0].focus();
                }
            }
        },
        getValidationErrors(field, validationSet) {
            if (!validationSet || !validationSet.length) {
                return [];
            }
            return validationSet.filter(i => i.field === field);
        },
        tooltipText(field, validationSet) {
            const errors = this.getValidationErrors(field, validationSet);
            if (!errors || !errors.length) {
                return null;
            }
            if (errors.filter(i => i.severity === 'error').length > 0) {
                return errors.filter(i => i.severity === 'error').map(i => i.message).join();
            }
            return errors.filter(i => i.severity === 'warning').map(i => i.message).join();
        },
        tooltipDisplayMode(field, validationSet) {
            const errors = this.getValidationErrors(field, validationSet);
            if (!errors || !errors.length) {
                return null;
            }
            if (errors.filter(i => i.severity === 'error').length > 0) {
                return 'error';
            }
            return 'warning';
        },
        outOfDateClass(lastUpdate) {
            if (lastUpdate === null) {
                return null;
            }
            const daysSinceUpdate = moment().diff(moment(lastUpdate), 'days');
            return { 'out-of-date': daysSinceUpdate >= 30 };
        },
    },
};

</script>

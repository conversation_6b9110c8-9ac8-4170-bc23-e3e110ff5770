<template>
    <div
        class="index-action-bar"
        data-cy="rtvRuralIndexToolbar"
    >
        <div class="index-action-wrapper">
            <div class="QVHV-buttons">
                <div
                    class="QVHV-buttons-left"
                    data-cy="rtvRuralIndexToolbarMessage"
                >
                    {{ toolbarMessage }}
                </div>
                <div class="QVHV-buttons-right">
                    <button
                        class="secondary"
                        :disabled="loading || saving"
                        data-cy="rtvRuralIndexDiscardButton"
                        @click="$emit('discard')"
                    >
                        Discard Changes
                    </button>
                    <button
                        class="primary"
                        :disabled="loading || saving"
                        data-cy="rtvRuralIndexSaveButton"
                        @click="$emit('save')"
                    >
                        Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    props: {
        messageData: {
            type: Object,
            default: () => {},
            required: false,
        },
    },
    data() {
        return {
            toolbarMessage: '',
        };
    },
    computed: {
        ...mapState('ruralRtv', {
            loading: 'loading',
            saving: 'saving',
        }),
    },
    watch: {
        messageData() {
            this.updateToolbarMessage();
        },
    },
    methods: {
        updateToolbarMessage() {
            this.toolbarMessage = this.messageData.message;
        },
    },
};

</script>

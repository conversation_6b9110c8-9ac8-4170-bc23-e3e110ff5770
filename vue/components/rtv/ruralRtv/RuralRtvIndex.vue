<template>
    <div data-cy="rtvRuralIndex">
        <rural-rtv-navbar 
            ref="navbar"
            :error-message="error"
            @tab-change="selectTab" 
            @ta-change="selectTerritorialAuthority"
        />
        <div 
            v-if="loading || saving" 
            class="page-mask" 
            data-cy="rtvRuralIndexPageMask"
        >
            <div class="loadingSpinnerWrapper">
                <div 
                    class="loadingSpinnerBox"
                    data-cy="rtvRuralIndexLoadingSpinner"
                >
                    <div class="loadingSpinner loadingSpinnerSearchResults" />
                </div>
            </div>
        </div>
        <template v-if="territorialAuthority && mainIndex.length">
            <div>
                <h4>Last Update: {{ lastUpdate }}</h4>
            </div>
            <rural-rtv-main-index 
                v-if="tab === 'MainIndex'"
                ref="mainIndex"
                data-cy="rtvRuralIndexMainIndex" 
                @cell-focus="updateToolbarMessage"
            />
            <rural-rtv-secondary-refinements 
                v-if="tab === 'SecondaryRefinements'" 
                ref="secondaryRefinements"
                data-cy="rtvRuralIndexSecondaryRefinements" 
                @cell-focus="updateToolbarMessage"
                @set-modal="setModal"
            />
        </template>
        <rural-rtv-toolbar 
            v-if="territorialAuthority && mainIndex.length"
            :message-data="toolbarMessageData"
            @discard="discardChanges"
            @save="saveIndex"
        />
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
            data-cy="rtvRuralIndexModal"
        >
            <h1 data-cy="rtvRuralIndexModalHeading">{{ modal.heading }}</h1>
            <p 
                v-if="modal.message !== ''" 
                style="white-space:pre-wrap;"
                data-cy="rtvRuralIndexModalMessage"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul data-cy="rtvRuralIndexModalMessageList">
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                        data-cy="rtvRuralIndexModalCancelButton"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                        data-cy="rtvRuralIndexModalConfirmButton"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
                data-cy="rtvRuralIndexModalResponseCode"
            />
        </alert-modal>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import moment from 'moment';

export default {
    components: {
        RuralRtvNavbar: () => import('./RuralRtvNavbar.vue'),
        RuralRtvMainIndex: () => import('./RuralRtvMainIndex.vue'),
        RuralRtvSecondaryRefinements: () => import('./RuralRtvSecondaryRefinements.vue'),
        RuralRtvToolbar: () => import('./RuralRtvToolbar.vue'),
        AlertModal: () => import('../../common/modal/AlertModal.vue'),
    },
    data() {
        return {
            toolbarMessageData: {},
            originalMainIndex: [],
            originalSecondaryRefinements: [],
            modal: {
                mode: 'warning',
                isOpen: false,
                heading: 'heading',
                message: '',
                messages: [],
                cancelText: 'No',
                cancelAction: () => { },
                confirmText: 'Yes',
                confirmAction: () => { },
                code: '',
            },
        };
    },
    computed: {
        ...mapState('ruralRtv', {
            tab: 'currentTab',
            mainIndex: 'mainIndex',
            secondaryRefinements: 'secondaryRefinements',
            territorialAuthority: 'territorialAuthority',
            categories: 'baseCategories',
            warnings: 'warnings',
            errors: 'errors',
            exception: 'exception',
            loading: 'loading',
            saving: 'saving',
        }),
        ...mapState('userData', {
            userName: 'userName',
        }),
        error() {
            return this.territorialAuthority && this.mainIndex.length === 0
                ? 'No valid rural assessments in this TA.'
                : '';
        },
        mainIndexChanged() {
            return JSON.stringify(this.mainIndex) !== JSON.stringify(this.originalMainIndex);
        },
        secondaryRefinementsChanged() {
            return JSON.stringify(this.secondaryRefinements)
                !== JSON.stringify(this.originalSecondaryRefinements);
        },
        lastUpdate() {
            let date;

            if (this.tab === 'MainIndex') {
                if (!this.mainIndex
                    || !this.mainIndex.length
                    || !this.categories
                    || !this.categories.length) {
                    return null;
                }

                const indexDates = [];
                this.mainIndex.map((o) => {
                    this.categories.map((i) => {
                        const lastUpdate = o[i.code].last_update;
                        if (lastUpdate) {
                            indexDates.push(moment(lastUpdate));
                        }
                    });
                });
                if (indexDates.length) {
                    date = moment.max(indexDates);
                }
            }
            if (this.tab === 'SecondaryRefinements') {
                if (this.secondaryRefinements.filter(i => i.last_update).length > 0) {
                    date = moment.max(...this.secondaryRefinements.map(i => moment(i.last_update)));
                }
            }
            if (date > 0) {
                return moment(date).tz('Pacific/Auckland').format('DD MMMM YYYY, h:mm A'); 
            }
            return 'Never';
        },
    },
    mounted() {
        this.loadLookupLists();
    },
    methods: {
        selectTab(newTab) {
            if (newTab !== this.tab) {
                this.$store.commit('ruralRtv/setCurrentTab', newTab);
            }
        },
        selectTerritorialAuthority(newTA, discardChanges) {
            try {
                if (newTA !== this.territorialAuthority) {
                    if (this.tab === 'MainIndex' && this.mainIndexChanged && !discardChanges) {
                        this.setModal({
                            mode: 'warning',
                            isOpen: true,
                            heading: 'Discard Changes',
                            message: 'All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?',
                            messages: [],
                            cancelText: 'No, Return to Main Index',
                            cancelAction: () => { this.$refs.navbar.$forceUpdate(); },
                            confirmText: 'Yes, Discard Changes',
                            confirmAction: () => { this.selectTerritorialAuthority(newTA, true); },
                            code: 'DISCARD_MAIN_INDEX_WARNING',
                        });
                        return;
                    }

                    if (this.tab === 'SecondaryRefinements' && this.secondaryRefinementsChanged && !discardChanges) {
                        this.setModal({
                            mode: 'warning',
                            isOpen: true,
                            heading: 'Discard Changes',
                            message: 'All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?',
                            messages: [],
                            cancelText: 'No, Return to Secondary Refinements',
                            cancelAction: () => { this.$refs.navbar.$forceUpdate(); },
                            confirmText: 'Yes, Discard Changes',
                            confirmAction: () => { this.selectTerritorialAuthority(newTA, true); },
                            code: 'DISCARD_SEC_REFINEMENTS_WARNING',
                        });

                        return;
                    }

                    this.$store.commit('ruralRtv/setTerritorialAuthority', newTA);
                    this.loadRuralIndex();
                }
            } catch (error) {
                this.handleError(error);
            }
        },
        updateToolbarMessage(message) {
            this.toolbarMessageData = message;
        },
        loadLookupLists() {
            this.$store.dispatch('ruralRtv/getLookupLists');
        },
        async loadRuralIndex() {
            try {
                await this.$store.dispatch('ruralRtv/clearStore', this.territorialAuthority);
                if (this.territorialAuthority && this.territorialAuthority !== '0') {
                    await this.$store.dispatch('ruralRtv/getRuralIndex', this.territorialAuthority);
                    if (this.mainIndex.length) {
                        this.$store.dispatch('ruralRtv/getRuralAssessments', this.territorialAuthority);
                    }
                }
                this.originalMainIndex = JSON.parse(JSON.stringify(this.mainIndex));
                this.originalSecondaryRefinements = JSON.parse(JSON.stringify(this.secondaryRefinements));
            } catch (error) {
                this.handleError(error);
            }
        },
        async discardChanges(confirmed) {
            try {
                if (this.tab === 'MainIndex') {
                    if (!confirmed && this.mainIndexChanged) {
                        this.setModal({
                            mode: 'warning',
                            isOpen: true,
                            heading: 'Discard Changes',
                            message: 'All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?',
                            messages: [],
                            cancelText: 'No, Return to Main Index',
                            cancelAction: () => { },
                            confirmText: 'Yes, Discard Changes',
                            confirmAction: () => { this.discardChanges(true); },
                            code: 'DISCARD_MAIN_INDEX_WARNING',
                        });

                        return;
                    }

                    await this.$store.dispatch('ruralRtv/getMainIndex', this.territorialAuthority);
                }
                if (this.tab === 'SecondaryRefinements') {
                    if (!confirmed && this.secondaryRefinementsChanged) {
                        this.setModal({
                            mode: 'warning',
                            isOpen: true,
                            heading: 'Discard Changes',
                            message: 'All your changes will be lost and will revert back to the last saved index if one exists. Are you sure?',
                            messages: [],
                            cancelText: 'No, Return to Secondary Refinements',
                            cancelAction: () => { },
                            confirmText: 'Yes, Discard Changes',
                            confirmAction: () => { this.discardChanges(true); },
                            code: 'DISCARD_SEC_REFINEMENTS_WARNING'
                        });

                        return;
                    }

                    await this.$store.dispatch('ruralRtv/getSecondaryRefinements', this.territorialAuthority);
                }
            } catch (error) {
                this.handleError(error);
            }
        },
        async saveIndex(ignoreWarnings = false) {
            try {
                if (this.tab === 'MainIndex') {
                    await this.$store.dispatch('ruralRtv/saveMainIndex', {
                        taCode: this.territorialAuthority,
                        userName: this.userName,
                        ignoreWarnings,
                        mainIndex: this.mainIndex,
                    });

                    this.$refs.mainIndex.$forceUpdate();

                    if (this.errors && this.errors.length) {
                        this.setModal({
                            mode: 'error',
                            isOpen: true,
                            heading: 'Save Main Index',
                            message: 'There was a problem saving the main index',
                            messages: this.errors.map(i => i.message),
                            cancelText: null,
                            cancelAction: () => { },
                            confirmText: 'OK',
                            confirmAction: () => { },
                            code: 'SAVE_MAIN_INDEX_ERROR',
                        });

                        return;
                    }

                    if (this.warnings && this.warnings.length && !ignoreWarnings) {
                        this.setModal({
                            mode: 'warning',
                            isOpen: true,
                            heading: 'Save Main Index',
                            message: 'Are you sure?',
                            messages: this.warnings.map(i => i.message),
                            cancelText: 'No, Return to Main Index',
                            cancelAction: () => { },
                            confirmText: 'Yes, Save Main Index',
                            confirmAction: () => { this.saveIndex(true); },
                            code: 'SAVE_MAIN_INDEX_WARNING',
                        });

                        return;
                    }

                    if (this.errors || this.errors.length === 0) {
                        this.setModal({
                            mode: 'success',
                            isOpen: true,
                            heading: 'Saved Main Index',
                            message: 'Success!',
                            messages: [],
                            cancelText: null,
                            cancelAction: () => { },
                            confirmText: 'OK',
                            confirmAction: () => { },
                            code: 'SAVE_MAIN_INDEX_SUCCESS',
                        });
                        this.originalMainIndex = JSON.parse(JSON.stringify(this.mainIndex));

                        return;
                    }

                    return;
                }
                if (this.tab === 'SecondaryRefinements') {
                    this.$refs.secondaryRefinements.saveLastRefinement();

                    await this.$store.dispatch('ruralRtv/saveSecondaryRefinements', {
                        taCode: this.territorialAuthority,
                        userName: this.userName,
                        ignoreWarnings,
                        secondaryRefinements: this.secondaryRefinements,
                    });

                    this.$refs.secondaryRefinements.$forceUpdate();

                    if (this.errors && this.errors.length) {
                        this.setModal({
                            mode: 'error',
                            isOpen: true,
                            heading: 'Save Secondary Refinements',
                            message: 'There was a problem saving the secondary refinements',
                            messages: this.errors.map(i => i.message),
                            cancelText: null,
                            cancelAction: () => { },
                            confirmText: 'OK',
                            confirmAction: () => { },
                            code: 'SAVE_SEC_REFINEMENTS_ERROR',
                        });

                        return;
                    }

                    if (this.warnings && this.warnings.length && !ignoreWarnings) {
                        this.setModal({
                            mode: 'warning',
                            isOpen: true,
                            heading: 'Save Secondary Refinements',
                            message: 'Are you sure?',
                            messages: this.warnings.map(i => i.message),
                            cancelText: 'No, Return to Secondary Refinements',
                            cancelAction: () => { },
                            confirmText: 'Yes, Save Refinements',
                            confirmAction: () => { this.saveIndex(true); },
                            code: 'SAVE_SEC_REFINEMENTS_WARNING',
                        });

                        return;
                    }

                    if (this.errors || this.errors.length === 0) {
                        this.setModal({
                            mode: 'success',
                            isOpen: true,
                            heading: 'Saved Secondary Refinements',
                            message: 'Success!',
                            messages: [],
                            cancelText: null,
                            cancelAction: () => { },
                            confirmText: 'OK',
                            confirmAction: () => { },
                            code: 'SAVE_SEC_REFINEMENTS_SUCCESS',
                        });
                        this.originalSecondaryRefinements = JSON.parse(JSON.stringify(this.secondaryRefinements));
                    }
                }
            } catch (error) {
                this.handleError(error);
            }
        },
        handleError(error) {
            this.setModal({
                mode: 'error',
                isOpen: true,
                heading: 'An error occurred',
                message: `There was a problem while performing the requested action.\n\n${error}`,
                messages: [],
                cancelText: null,
                cancelAction: () => { },
                confirmText: 'OK',
                confirmAction: () => { },
                code: 'API_ERROR',
            });
        },
        setModal(modal) {
            this.modal = modal;
        },
        modalCancel() {
            this.modal.isOpen = false;
            this.modal.cancelAction();
        },
        modalConfirm() {
            this.modal.isOpen = false;
            this.modal.confirmAction();
        },
    },
};

</script>

<style scoped>

    h4 {
        text-align:right;
        font-size:14px;
        font-style:italic;
        color: var(--color-blue-900);
        font-weight:bold;
    }

</style>

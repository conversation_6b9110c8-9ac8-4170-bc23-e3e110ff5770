<template>
    <div class="rtv-index-wrapper">
        <table>
            <tbody>
                <tr>
                    <td>
                        <table class="index-table">
                            <thead data-cy="mainIndexHeader1">
                                <tr>
                                    <th
                                        class="left"
                                        style="height:93px;"
                                        colspan="3"
                                        data-cy="mainIndexLocationHeaderCell"
                                    >
                                        LOCATION
                                    </th>
                                </tr>
                                <tr>
                                    <th
                                        class="left"
                                        data-cy="mainIndexSalesGroupHeaderCell"
                                    >
                                        SALES GROUP
                                    </th>
                                    <th data-cy="mainIndexSalesGroupCodeHeaderCell">
                                        SG CODE
                                    </th>
                                    <th data-cy="mainIndexRollHeaderCell">
                                        ROLL
                                    </th>
                                </tr>
                            </thead>
                            <tbody data-cy="mainIndexBody1">
                                <tr
                                    v-for="roll in mainIndex"
                                    :key="roll.roll_id"
                                >
                                    <td class="left">
                                        <span>{{ roll.sale_group.description }}</span>
                                    </td>
                                    <td>
                                        <span>{{ roll.sale_group.code }}</span>
                                    </td>
                                    <td>
                                        <span>{{ roll.roll_number }}</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                    <td>
                        <table class="index-table">
                            <thead data-cy="mainIndexHeader2">
                                <tr>
                                    <th
                                        class="left"
                                        colspan="24"
                                        data-cy="mainIndexTitleHeaderCell"
                                    >
                                        LAND AND IMPROVEMENT VALUE INDEX SET
                                    </th>
                                </tr>
                                <tr>
                                    <th
                                        v-for="category in categories"
                                        :key="`categoryHeaderCell${category.id}`"
                                        colspan="4"
                                        :data-cy="`mainIndexCategory${category.code}HeaderCell`"
                                    >
                                        {{ category.description }}
                                    </th>
                                </tr>
                                <tr>
                                    <template v-for="category in categories">
                                        <th
                                            :key="`landIndexHeaderCell${category.id}`"
                                            colspan="2"
                                            :data-cy="`mainIndexCategory${category.code}LandIndexHeaderCell`"
                                        >
                                            LAND INDEX
                                        </th>
                                        <th
                                            :key="'improvementIndexHeaderCell' + category.id"
                                            colspan="2"
                                            :data-cy="`mainIndexCategory${category.code}ImprovementIndexHeaderCell`"
                                        >
                                            VI INDEX
                                        </th>
                                    </template>
                                </tr>
                                <tr>
                                    <template v-for="category in categories">
                                        <th
                                            :key="'landPercentIndexHeaderCell' + category.id"
                                            :data-cy="`mainIndexCategory${category.code}LandPercentIndexHeaderCell`"
                                        >
                                            %
                                        </th>
                                        <th
                                            :key="'landLumpSumIndexHeaderCell' + category.id"
                                            :data-cy="`mainIndexCategory${category.code}LandLumpSumIndexHeaderCell`"
                                        >
                                            LUMP SUM
                                        </th>
                                        <th
                                            :key="'improvementPercentIndexHeaderCell' + category.id"
                                            :data-cy="`mainIndexCategory${category.code}ImprovementPercentIndexHeaderCell`"
                                        >
                                            %
                                        </th>
                                        <th
                                            :key="'improvementLumpSumIndexHeaderCell' + category.id"
                                            :data-cy="`mainIndexCategory${category.code}ImprovementLumpSumIndexHeaderCell`"
                                        >
                                            LUMP SUM
                                        </th>
                                    </template>
                                </tr>
                            </thead>
                            <main-index-cells />
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    components: {
        MainIndexCells: () => import('./mainIndex/mainIndexCells.vue'),
    },
    computed: {
        ...mapState('ruralRtv', {
            mainIndex: 'mainIndex',
            categories: 'baseCategories',
        }),
    },
};

</script>

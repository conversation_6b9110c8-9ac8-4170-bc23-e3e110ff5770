.router {

    .page-mask {
        position:fixed;
        top:0;
        left:0;
        height:100%;
        width:100%;
        background:rgba(220,220,220,0.5);
        z-index:500;

        .loadingSpinnerWrapper {
            position: relative;
            top:50%;
            left:50%;
        }

        .loadingSpinnerBox {
            position: relative;
            height:100px;
            width:100px;
            transform: translate(-50%, -50%);
            background:#FFFFFF;
            border:1px solid rgba(237,241,245,.75);
            border-radius:3px;

            .loadingSpinner {
                background-position: 50% 50%;
                background-size: 4rem;
                margin: 0.4rem 0 0 0;
                display:block;
            }
        }
    }

    > .qvToolbar-wrapper > .qvToolbar-leftMenu  {
        span:first-child {
            padding: 1.6rem 2rem 0.6rem;
            margin-right: 0 !important;
            margin-bottom: -0.1rem;
            box-shadow: none;
            height: 5.6rem;
            box-sizing: border-box;
            font-size: 1.6rem;
            color: #fff;
            background-color: transparent;
            min-width: 200px;
            width:auto;
            font-weight: bold;
        }
    }

    .qvToolbar-wrapper.light {
        .qvToolbar {
            background-color: #ffffff;

            .qvToolbar-leftMenu {
                background-color: #ffffff;
            }

            .qvToolbar-links a, .qvToolbar-links label {
                color:rgba(0, 0, 0, 0.87);
            }

            .taDashboard-selector select {
                color:rgba(0, 0, 0, 0.87);
                font-size: 1.3rem;
                height: 5.6rem;
            }
        }
    }

    .multiselect {
        min-height:0;
    }

    .advSearch-group .dropdown-menu {
        top:2.6rem;
    }

    .index-action-bar {
        position: fixed;
        bottom:0;
        left: 0;
        background:white;
        width:100%;
        z-index:100;
        border-top:1px solid #DDD;
        box-shadow: 0px -5px 10px #CCC;
        font-size:1.2rem;
        
        .index-action-wrapper {
            max-width: 144rem;
            margin:0 auto;
            padding:15px;

            .QVHV-buttons {
                margin:0;
                padding:0;

                .QVHV-buttons-left {
                    line-height:3.2;
                }
            }
        }
    }

    .rtv-index-wrapper {
        width: 100%;
        margin-bottom: 75px;
        overflow:auto;
        min-height:600px;
        max-height:calc(100vh - 110px);
        border-radius: 8px;
        padding: 0 3px 3px 3px;

        > table {
            border-collapse: collapse; 
            border-spacing: 0;

            > tbody > tr > td:first-child {
                position:sticky;
                left:0;
                z-index:60;

                .index-table { 
                    th {
                        background-color: var(--color-darkblue-500);
                    }
                    
                    tr {
                        &.out-of-date td {
                            background-color: var(--color-orange-100);
                        }

                        td {
                            background-color: var(--color-lightblue-100);
                            
                            &.out-of-date {
                                background-color: var(--color-orange-100);
                            }
                        }
                    }

                    tr:nth-child(even) {
                        &.out-of-date td {
                            background-color: var(--color-orange-200);
                        }

                        td {
                            background-color: var(--color-lightblue-200);
                            
                            &.out-of-date {
                                background-color: var(--color-orange-200);
                            }
                        }
                    }
                }
            }

            > tbody > tr > td {

                .index-table td { 
                    background-color:#FFFFFF;

                    &.out-of-date {
                        background-color: var(--color-orange-100);
                    }
                }
                
                .index-table tr:nth-child(even) td {
                    background-color: #F5F5F5;
                    
                    &.out-of-date {
                        background-color: var(--color-orange-200);
                    }
                }
            }

            > tbody > tr > td:nth-child(3) {
                .index-table th {
                    background-color: var(--color-blue-800);
                }
            }

            > tbody > tr > td:nth-child(4) {
                .index-table th {
                    background-color: var(--color-blue-300);
                }
            }
        }

        .index-table {
            font-size:1.4rem;
            min-width:100%;
            position:relative;
            border-collapse: collapse; 
            border-spacing: 0;

            span {
                display:block;
                width:100%;
                padding:3px 10px;

                .material-icons {
                    vertical-align:sub;
                    font-size: 21px;
                }
            }

            tr {
                height:1px;

                &.out-of-date td {
                    background-color: var(--color-orange-100);
                }
            }

            td, th {
                height:inherit;
                white-space:nowrap;
                border-left:1px solid #EAEAEA;
                text-align:center;

                input[type=text], input[type=number] {
                    width:100%;
                    min-width:110px;
                    padding:3px 10px;
                    text-align:center;
                }

                select {
                    width:auto;
                    min-width:100%;
                    padding:3px 30px 3px 10px;
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    appearance: none;

                    /* Positions background arrow image */
                    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAh0lEQVQ4T93TMQrCUAzG8V9x8QziiYSuXdzFC7h4AcELOPQAdXYovZCHEATlgQV5GFTe1ozJlz/kS1IpjKqw3wQBVyy++JI0y1GTe7DCBbMAckeNIQKk/BanALBB+16LtnDELoMcsM/BESDlz2heDR3WePwKSLo5eoxz3z6NNcFD+vu3ij14Aqz/DxGbKB7CAAAAAElFTkSuQmCC');
                    background-repeat: no-repeat;
                    background-position: right 3px top 50%;
                }
                
                input[type=text], input[type=number], select {
                    display:block;
                    border:none;
                    height:100%;
                    min-height:46px;
                    line-height: 40px;
                    font-size:1.4rem;
                    font-family:'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif;
                    background-color:transparent;

                    &:focus {
                        outline: 3px solid var(--color-blue-300);
                    }

                    &.error {
                        outline: 3px solid var(--color-red-400);
                        
                        &:focus {
                            outline: 3px solid var(--color-red-400);
                        }
                    }

                    &.warning {
                        outline: 3px solid var(--color-orange-200);
                        
                        &:focus {
                            outline: 3px solid var(--color-orange-200);
                        }
                    }
                }

                input[type=text]:disabled, input[type=number]:disabled, select:disabled {
                    background-color:rgba(0, 0, 0, 0.15) !important;
                }

                .multiselect {
                    height: 100%;
                    
                    &.error {
                        outline: 3px solid var(--color-red-400);
                        
                        &:focus {
                            outline: 3px solid var(--color-red-400);
                        }
                    }

                    &.warning {
                        outline: 3px solid var(--color-orange-200);
                        
                        &:focus {
                            outline: 3px solid var(--color-orange-200);
                        }
                    }
                }

                .multiselect__tags {
                    background-color:transparent;
                    border:none;
                    padding:5px 30px 2px 5px;

                    span.multiselect__tag {
                        width:auto;
                        z-index: 5;
                    }
                }
                
                .multiselect__placeholder {
                    display:none;
                }

                input.multiselect__input{
                    top: -2px;
                    margin: 3px 0;
                    padding: 0;
                    border-radius:0;
                    z-index:1
                }

                &.left {
                    text-align:left;

                    input[type=text], select {
                        text-align:left;
                    }
                }

                &.out-of-date {
                    background-color: var(--color-orange-100);
                }
            }

            thead {
                position: sticky; 
                top: 0;
                z-index:20;
                
                td, th {
                    font-weight:bold;
                    background-color: var(--color-darkblue-700);
                    color: #ffffff;
                    border:none;
                    box-shadow: inset -1px 1px #fff, 0 1px #fff;
                    padding:3px 10px;
                    text-transform:uppercase;
                    line-height: 25px;
                }
            }

            tbody {

                th {
                    background-color: var(--color-lightblue-300);
                    font-weight:normal;
                }

                td, th {
                    border-right:1px solid #999;
                    line-height:40px;
                    vertical-align:middle;
                }

                td:last-child, th:last-child {
                    border-right:none;

                    span.button-cell {
                        display:flex;
                        height:100%;
                        align-items:center;

                        &:hover {
                            cursor:pointer;
                        }
                    }
                }

                tr:last-child td {
                    border-bottom:none;
                }
                
                tr:hover td > div > input, tr:hover td select, tr:hover td > div > .multiselect, tr:hover td > span {
                    background-color: rgba(48, 96, 214, 0.15);
                }
            }
        }
    }
    
    /* Temporary colors for errors taken from bootstrap. */
    .bAlert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .bAlert-warning {
        color: #9c6500;
        background-color: #ffeb9c;
        border-color: #ffe471;
    }

    .bAlert {
        position: relative;
        padding: .75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: .25rem;
    }

    .message {
        border: 1px solid transparent;
        border-radius: .25rem;
        padding: .75rem 1.25rem;
        margin-bottom: 1rem;
        margin-top: 0.5rem;
        font-size: 1.3rem;
    }
    .message-error {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }
    .message-warning {
        color: #9c6500;
        background-color: #ffeb9c;
        border-color: #ffe471;
    }
}
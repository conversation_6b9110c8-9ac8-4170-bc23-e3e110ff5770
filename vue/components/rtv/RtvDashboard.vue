<template>
    <div 
        v-if="isRtvUser" 
        class="contentWrapper"
        data-cy="rtvDashboard"
    >
        <div 
            class="qvToolbar-wrapper"
            data-cy="rtvDashboardPrimaryToolbar"
        >
            <div class="advSearch-group qvToolbar-leftMenu lefty">
                <span data-cy="rtvDashboardHeading">
                    RTV Dashboard
                </span>
            </div>
            <div class="md-full qvToolbar">
                <ul class="qvToolbar-links lefty">
                    <li 
                        class="active"
                        data-cy="rtvDashboardRuralIndexLink"
                    >
                        <label>Rural Index</label>
                    </li>
                </ul>
                <ul class="qvToolbar-qivs righty">
                    <li 
                        class="md-qivs" 
                        @click="openQIVSLink"
                        data-cy="rtvDashboardQivsLink"
                    >
                        <label>QIVS</label> <i class="material-icons">call_made</i>
                    </li>
                </ul>
            </div>
        </div>
        <rural-index />
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    components: {
        RuralIndex: () => import('./ruralRtv/RuralRtvIndex.vue'),
    },
    computed: {
        ...mapState('userData', [
            'isRtvUser',
            'qivsUrl'
        ]),
    },
    methods: {
        openQIVSLink() {
            window.open(this.qivsUrl, 'QIVS');
        },
    },
};
</script>

<style lang="scss" src="./rtv.scss"></style>
<template>
    <div>
    <div class="advSearch-row">
        <range title="Gross Sale Price" group="grossSalePrice" decimal="false"></range>
        <range title="Net Sale Price" group="netSalePrice" decimal="false"></range>
        <range title="Net Sale Price /m²" group="netSalePricePerSquareMetre" decimal="false"></range>
    </div>
    <div class="advSearch-row">
        <div class="advSearch-group">
            <date-range-filter label="Sale Date" rangeId="saleDate" filterClass="reportrange advSearch-group advSearch-row-dependent disabled" showCustomRange=true></date-range-filter>
            <date-range-filter label="Sale Input Date" rangeId="saleInputDate" filterClass="reportrange advSearch-group advSearch-row-dependent disabled" showCustomRange=true></date-range-filter>
        </div>
    </div>
    <div class="advSearch-row" v-if="showClassificationAndStatus">
        <classification :disabled="disableClassificationAndStatus"></classification>
        <sale-status :disabled="disableClassificationAndStatus" v-if="isInternalUser"></sale-status>
    </div>
    <div class="advSearch-row">
        <div class="advSearch-group tightMargin advSearch-row-dependent disabled" id="group-includeCats">
            <label><strong>Include</strong> These Categories</label>
            <span><input id="includedCategory1" class="salesIncludedCategories" type="text"></span>
            <span><input id="includedCategory2" class="salesIncludedCategories" type="text"></span>
            <span><input id="includedCategory3" class="salesIncludedCategories" type="text"></span>
            <span><input id="includedCategory4" class="salesIncludedCategories" type="text"></span>
        </div>
        <div class="advSearch-group tightMargin noMargin advSearch-row-dependent disabled" id="group-excludeCats">
            <label><strong>Exclude</strong> These Categories</label>
            <span><input id="excludedCategory1" class="salesExcludedCategories" type="text"></span>
            <span><input id="excludedCategory2" class="salesExcludedCategories" type="text"></span>
            <span><input id="excludedCategory3" class="salesExcludedCategories" type="text"></span>
            <span><input id="excludedCategory4" class="salesExcludedCategories" type="text"></span>
        </div>
    </div>
    <div class="advSearch-row advSearchForm">
        <range title="Capital Value" group="salesCapitalValue" decimal="true"></range>
        <range title="Land Value" group="salesLandValue" decimal="true"></range>
    </div>
    <div class="advSearch-row advSearchForm">
        <range title="Land Area" group="salesLandArea" decimal="false"></range>
        <range title="Total Floor Area" group="salesFloorArea" decimal="true"></range>
        <range title="Bedrooms" group="salesBedroomCount" decimal="true"></range>
        <multi-select-filter category="BuildingAge" :style-attributes="buildingAgeStyle" filter-id="buildingAge" label="Age" group="salesBuildingAges" property-name="buildingAge" data-cy="sales-building-age"></multi-select-filter>
    </div>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import Range from '../filters/Range.vue'
    import SingleSelect from '../filters/SingleSelect.vue'
    import MultiSelectFilter from '../filters/MultiSelectAdvSearchFilter.vue'
    import Classification from '../filters/Classification-Deprecated.vue'
    import SaleStatus from '../filters/SaleStatus-Deprecated.vue'
    import DateRangeFilter from '../filters/DateRangeFilter.vue'
    import {EventBus} from '../../EventBus.js'

    export default {
        components: {
            Range,
            SingleSelect,
            Classification,
            SaleStatus,
            DateRangeFilter,
            MultiSelectFilter
        },
        data: function() {
            return {
                showClassificationAndStatus: true,
                refreshClassificationAndStatus : false,
                disableClassificationAndStatus: true,
                buildingAgeStyle: []
            }
        },
        computed: {
            ...mapState('userData', [
                'isInternalUser'
            ]),
        },
        mounted: function() {
            const self = this;
            EventBus.$on("clear-sales-search-filters", function(message) {
                self.refreshView();
                self.disableClassificationAndStatus = !message.hasTaCodes;
            });

            self.buildingAgeStyle.push({key:'max-height', value:'300px'});
            self.buildingAgeStyle.push({key:'margin-top', value:'-200px'});
            self.buildingAgeStyle.push({key:'margin-left', value: '-100px'});
        },
        methods: {
            refreshView: function() {
                const self = this
                self.showClassificationAndStatus = !self.showClassificationAndStatus;
                if (!self.showClassificationAndStatus) {
                    self.refreshClassificationAndStatus = true;
                }
            }
        },
        updated: function () {
            var self = this;
            if (self.refreshClassificationAndStatus) {
                self.showClassificationAndStatus = true;
                self.refreshClassificationAndStatus = false;
            }
        },
        destroyed: function () {
            EventBus.$off('clear-sales-search-filters', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('clear-sales-search-filters', this.listener);
        }
    }
</script>

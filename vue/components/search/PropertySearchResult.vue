<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div v-if="showTemplate">
    <div class="resultsWrapper" v-if="totalPropertiesCount > 0">
        <div class="resultsInner-wrapper mdl-shadow--3dp">
            <div class="resultsTitle">
                <h1 class="lefty" >Property Search Results</h1>
                <div class="switch righty">
                    <input id="cmn-toggle-4" class="condensedView cmn-toggle cmn-toggle-round-flat" type="checkbox">
                    <label for="cmn-toggle-4">
                        <span>
                            <span class="toolTipper normalView" title="Normal View"></span>
                            <span class="toolTipper compactView" title="Compact View"></span>
                        </span>
                    </label>
                    <i class="material-icons fatman">&#xE8F2;</i>
                    <i class="material-icons littleboy">&#xE8EE;</i>
                </div>
                <ul class="toolbar righty">
                    <li 
                        title="Export Results (limit 200,000)" 
                        class="exportResults mdl-button mdl-js-button mdl-button--icon" 
                        :class="{ 'disabled': exportResultsDisabled }"
                        @click="exportResults()"
                    >
                        <i class="material-icons md-dark">&#xE06F;</i>
                    </li>

                    <li title="Expand Results" class="expandAll mdl-button mdl-js-button mdl-button--icon"><i class="material-icons md-dark">&#xE8D7;</i></li>
                </ul>
            </div>
            <div class="resultsFound">
                <p>{{totalPropertiesCount}} results found</p>
            </div>

            <div v-if="exportResultsDisabled" class="loadingSpinnerExportResults"></div>

            <div id="desktop" class="sortRow-wrapper">
                <div class="sortRow">
                    <div data-value="value 1" data-sort="addressDefaultSort" class="colHeader addressDefaultSort address active">
                        <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                            Address<i class="material-icons md-dark">&#xE5C5;</i></a>
                    </div>
                    <div data-value="value 2" data-sort="VALUATION_REFERENCE" class="colHeader valuationReference valref">
                        <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                            Val Ref<i class="material-icons md-dark">&#xE5C5;</i></a>
                    </div>

                    <div class="searchDetails-wrapper">
                        <div data-value="value 3" data-sort="CAPITAL_VALUE" class="colHeader capitalValue capval">
                            <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                                Capital Value<i class="material-icons md-dark">&#xE5C5;</i></a>
                        </div>
                        <div data-value="value 4" data-sort="LAND_VALUE" class="colHeader landValue landval">
                            <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                                Land Value<i class="material-icons md-dark">&#xE5C5;</i></a>
                        </div>


                        <div data-value="value 4" data-sort="VALUE_OF_IMPROVEMENTS" class="colHeader valimp">
                            <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                                Value Imps.<i class="material-icons md-dark">&#xE5C5;</i></a>
                        </div>

                        <div data-value="value 7" data-sort="LAND_AREA" class="colHeader landArea">
                            <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                                Land Area (Ha)<i class="material-icons md-dark">&#xE5C5;</i></a>
                        </div>
                        <div data-value="value 5" data-sort="TOTAL_FLOOR_AREA" class="colHeader ludTotalFloorArea tfa">
                            <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                                TFA<i class="material-icons md-dark">&#xE5C5;</i></a>
                        </div>
                        <div data-value="value 6" data-sort="TOTAL_LIVING_AREA" class="colHeader masTotalFloorArea tla">
                            <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                                TLA<i class="material-icons md-dark">&#xE5C5;</i></a>
                        </div>

                    </div>
                    <div data-value="value 8" data-sort="CATEGORY" class="colHeader category">
                        <a href="#"><span class="icon">
                            <i class="sorter material-icons md-18 down">&#xE5DB;</i>
                        </span>
                            Category<i class="material-icons md-dark">&#xE5C5;</i></a>
                    </div>
                </div>
            </div>

            <div class="resultsRow" v-bind:id="'results_'+property.qupid" v-for="property in properties" v-bind:class="{noThumbnails: property.propertyPhotos ? false : true, maoriLand: property.isMaoriLand==='Yes', revalOn: (((isInternalUser == false && isReadOnly == false) || isInternalUser) && property.hasRevision)}">
                <div class="colCell address">
                    <span class="primaryThumb-Wrapper" v-bind:id="'primaryPhoto_'+property.qupid">
                        <img class="primaryPhoto_thumb" v-bind:src=property.primaryPhoto>
                    </span>
                    <div v-bind:id="'fullAddressDiv_'+property.qupid" class="fullAddress" v-bind:data-qupid="property.qupid" @click="loadMasterDetails(property)">
                        <span id="address1Span" v-html="property.address1"></span>
                        <span id="address2Span" v-html="property.address2"></span>
                        <div class="colCell qpid" v-html="property.qupid"></div>
                        <div class="colCell valref" v-html="property.valRef"></div>
                            <ul class="occupier">
                                <li v-html="property.occupier1"></li>
                                <li v-html="property.occupier2"></li>
                            </ul>
                    </div>
                </div>
                <div class="colCell valref" v-html="property.valRef"></div>
                <div class="searchDetails-wrapper">
                    <div class="colCell capval">{{property.capitalValue}}
                        <div class="cvnr">{{property.cvNetRate}}<span>/m<sup>2</sup></span></div>
                    </div>
                    <div class="colCell landval">{{property.landValue}}
                        <div class="lvnr">{{property.lvNetRate}}<span v-if="property.landArea < 1">/m<sup>2</sup></span>
                        <span v-else>/ha</span></div>
                    </div>
                    <div class="colCell valimp">{{property.valueOfImprovements}}
                        <div class="vinr">{{property.viNetRate}}<span>/m<sup>2</sup></span></div>
                    </div>
                    <div class="colCell landarea" v-html="property.landArea" data-cy="land-area"></div>
                    <div class="colCell tfa" v-html="property.TFA"></div>
                    <div class="colCell tla" v-html="property.TLA"></div>
                </div>
                <div class="colCell category" v-html="property.category"></div>
                <div class="revalRates" v-if="((isInternalUser == false && isReadOnly == false) || isInternalUser) && property.hasRevision">
                    <div class="reval-capval">{{property.revisionCapVal}}<span v-bind:class="[property.capitalValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.capitalValueDiff}}%</span><div class="cvnr">{{property.revalCapValNetRate}}<span>/ m<sup>2</sup></span></div></div> 	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
                    <div class="reval-landval">{{property.revisionLandVal}}<span v-bind:class="[property.landValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.landValueDiff}}%</span><div class="lvnr">{{property.revalLandValNetRate}}<span v-if="property.landArea < 1">/ m<sup>2</sup></span><span v-else>/ha</span></div></div>	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
                    <div class="reval-valimp">{{property.revalValueOfImprovements}}<span v-bind:class="[property.valueOfImprovementsDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.valueOfImprovementsDiff}}%</span><div class="vinr">{{property.revalValueOfImprovementsNetRate}}<span>/ m<sup>2</sup></span></div></div>	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
                </div>
                <div class="photoGallery_propSearch">
                    <span class="thumbWrapper" v-for="photo in property.propertyPhotos">
                        <img class="photoGallery_thumb" v-bind:data-id=photo.id v-bind:data-qupid=photo.qupid v-bind:data-property=photo.propertyId v-bind:src=photo.link>
                    </span>
                </div>
                <div class="extras extras_${property.id}" id="extras_${property.id}">
                    <ul class="md-landMas searchList">
                        <li class="md-masIcon-category"><strong v-html="property.category" data-cy="category"></strong>Category</li>
                        <li class="md-masIcon-eyb"><strong v-html="property.effectiveYearBuilt" data-cy="effective-year-built"></strong>Effective Year Built</li>
                        <li class="md-masIcon-landUse"><strong v-html="property.landUse"></strong>Land Use</li>
                        <li class="md-masIcon-units"><strong v-html="property.units"></strong>Units</li>
                        <li class="md-masIcon-bedrooms"><strong v-html="property.bedrooms" data-cy="bedrooms"></strong>Bedrooms</li>
                        <li class="md-masIcon-toilets"><strong v-html="property.toilets"></strong>Toilets</li>
                        <li class="md-masIcon-walls"><strong v-html="property.wallConstructionAndCondition"></strong>Wall Construction and Condition</li>
                        <li class="md-masIcon-roof"><strong v-html="property.roofConstructionAndCondition"></strong>Roof Construction and Condition</li>
                        <li class="md-masIcon-umrg"><strong v-html="property.underMainRoofGarages"></strong>Under Main Roof Garaging</li>
                        <li class="md-masIcon-fsg"><strong v-html="property.freeStandingGarages"></strong>Free Standing Garaging</li>
                        <li class="md-masIcon-oli"><strong v-html="property.otherLargeImprovements"></strong>Other Large Improvements</li>
                        <li class="md-masIcon-modernisation"><strong v-html="property.modernisation"></strong>Modernisation</li>
                        <li class="md-masIcon-zone"><strong v-html="property.zone"></strong>Zone</li>
                        <li class="md-masIcon-lotPosition"><strong v-html="property.lotPosition"></strong>Lot Position</li>
                        <li class="md-masIcon-contour"><strong v-html="property.contour"></strong>Contour</li>
                        <li class="md-masIcon-viewScope"><strong v-html="property.viewScope"></strong>View and Scope</li>
                        <li class="md-masIcon-production"><strong v-html="property.production"></strong>Production</li>
                        <li class="md-masIcon-maoriLand"><strong v-html="property.isMaoriLand"></strong>Maori Land</li>
                    </ul>
                </div>
                <ul class="toolbar listingControls righty">
                    <li class="md-qivs" @click="externalQIVSLinkHandler(property.qupid)"><label>QIVS</label><i class="material-icons">call_made</i></li>		<!-- THIS ITEM LINKS TO THIS PROPERTY RECORD IN QIVS -->
                    <li class="md-qvms" v-if="isQVMapUser" @click="openQVMap(property.qupid, property.coordinates)">
                        <label>MAP</label>
                        <i class="material-icons">call_made</i>
                    </li>		<!-- THIS ITEM LINKS TO THIS PROPERTY RECORD IN THE MAPPING APPLICATION -->
                    <li class="md-qvms"
                        @click="openUrlInNewTab(`https://google.co.nz/search?near=New+Zealand&q=${property.address1}+${property.address2}`)">
                        <label>WEB</label> <i class="material-icons icon--flipped">search</i>
                    </li>
                    <li class="mdl-button mdl-js-button mdl-button--icon photo-uploader" v-bind:data-property="property.id" v-bind:data-qupid="property.qupid" v-bind:class="{disabled: isReadOnly == true}" title="Upload Photos">
                        <i class="material-icons md-dark">&#xE251;</i>
                    </li>
                    <li class="closer mdl-button mdl-js-button mdl-button--icon" title="Close"><i class="material-icons md-dark">&#xE5CD;</i></li>
                </ul>
            </div>
        </div>
        <div class="loadingSpinner loadingSpinnerSearchResults"></div>
        <warning :header="warningHeader" :message="warningMessage" close="Go Back"></warning>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
            />
        </alert-modal>
    </div>
    <div class="noResults-wrapper mdl-shadow--3dp" v-if="noResultsFound">
        <h2>Sorry, no results were found</h2>
        <p>Search suggestions:</p>
        <ul>
            <li>Check your spelling.</li>
            <li>Confirm the address or information that you were searching.</li>
            <li>Try searching by different criteria <em>(e.g. QPID, Valuation Reference Number, Certificate of Title, etc.).</em></li>
        </ul>
    </div>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import numeral from 'numeral';
    import Warning from '../common/Warning.vue';
    import * as $ from "jquery";
    import commonUtils from '../../utils/CommonUtils';
    import { submitMonarchExport } from '../reports/utils.js'
    import moment from 'moment';
    import { openUrlInNewTab, openMap } from '../../utils/QivsUtils';

    export default {
        components: {
            'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../common/modal/AlertModal.vue'),
            Warning
        },
        mixins: [commonUtils],
        data: function() {
            return {
                properties: [],
                totalPropertiesCount: 0,
                searchParams:{},
                sortField:'',
                sortOrder:'',
                warningHeader: '',
                warningMessage: '',
                showTemplate:false,
                noResultsFound: false,
                imageFetchingOffset: 0,
                exportResultsDisabled: false,
                modal: {
                    mode: 'warning',
                    isOpen: false,
                    heading: 'No Rural Worksheet',
                    message: '',
                    cancelText: 'OK',
                    cancelAction: () => { this.modal.isOpen = false; },
                    confirmText: 'Create Rural Worksheet',
                    confirmAction: () => {
                        this.modal.isOpen = false;
                        this.goToRuralWorksheet(this.selectedSaleQpid);
                    },
                },
            }

        },
        computed: {
            ...mapState('userData', [
                'isInternalUser',
                'isReadOnlyUser',
                'qivsUrl',
                'isQVMapUser',
            ]),
            isReadOnly: function() {
                return this.isReadOnlyUser;
            }
        },
        methods: {
            async exportResults() {
                this.exportResultsDisabled = true;

                let searchCriteria = null;

                try {
                    // additional step to apply user based security to search criteria e.g. restrict searchable TAs
                    const { url } = jsRoutes.controllers.ExportProperties.getExportPropertiesCriteria();
                    const res = await fetch(`${url}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify(this.searchParams),
                    });
                    searchCriteria = await res.json();
                }
                catch (error) {
                    const message = 'Error calling report generator API to get reports';
                    console.error(message, error);
                }

                const submitResult = await submitMonarchExport(
                    'MONARCH_PROPERTY_EXPORT',
                    searchCriteria,
                    this.totalPropertiesCount
                );

                console.log('submitResult',submitResult);
                if (submitResult) {
                    if (submitResult.cancelText === 'View My Reports') {
                        submitResult.cancelAction = () => { this.$router.push({ name: 'report-dashboard-my-reports' }); }
                    }
                    this.setModal(submitResult);
                }

                this.exportResultsDisabled = false;
            },
            setModal(modal) {
                this.modal = modal;
            },
            modalCancel() {
                this.modal.isOpen = false;
                this.modal.cancelAction();
            },
            modalConfirm() {
                this.modal.isOpen = false;
                this.modal.confirmAction();
            },
            loadMasterDetails: function(property) {
                var event={}
                event.searchType = 'master-details';
                event.property = property
                EventBus.$emit('display-content', event)
            },
            openQVMap(qpid,coordinates) {
                openMap(qpid,coordinates.latitude, coordinates.longitude)
            },
            externalQIVSLinkHandler: function(qupid) {
                var url = this.qivsUrl;
                if (url && url != '') {
                    var ua = window.navigator.userAgent;
                    var old_ie = ua.indexOf('MSIE ');
                    var new_ie = ua.indexOf('Trident/');

                    var isIE = ((old_ie > -1) || (new_ie > -1));

                    if(qupid) {
                        url = url + "/default.asp?Property/masterdetails.aspx?Qpid=" + qupid;
                    }
                    if(isIE) {
                        var w = window.open(url, "QIVZ");
                        w.close();
                    }
                    window.open(url, "QIVZ");
                }
            },
            getRenderableValue: function(val) {
                return (val && val != '') ? val : '-'
            },
            getFormattedDate: function(date) {
                if (date && date != '') {
                    return moment(String(date)).format('DD/MM/YYYY')
                }
                return ''
            },
            getSortFields: function(sortFieldName) {
                var sortFields = []
                if (sortFieldName == 'addressDefaultSort') {
                    sortFields=['TA_CODE', 'STREET_NAME', 'STREET_TYPE', 'STREET_NUMBER', 'STREET_NUMBER_SUFFIX', 'VALUATION_REFERENCE']
                }
                else {
                    sortFields.push(sortFieldName)
                }
                return sortFields
            },
            fetchImages: function() {
                const self = this;
                var properties = self.properties;
                for(var i=self.imageFetchingOffset; i<properties.length;i++) {
                    var property = self.properties[i];
                    self.refreshResultRow(property.id, property.qupid);
                }
                self.imageFetchingOffset = properties.length;
            },
            getPropertyPhotos: function(photos, qupid){
                var result = {primary: 'assets/images/property/addPhotos.png', list: []};
                if(photos) {
                    try {
                        $.each(photos[0], function(i, obj) {
                            if(obj.isPrimary) {
                                result.primary = obj.mediaItem.smallImageUrl;
                            }
                            result.list.push({'id':obj.id, 'propertyId':obj.ownerId, 'qupid':qupid, 'link':obj.mediaItem.mediumImageUrl});
                        });
                    } catch(e){
                        console.log("Error in getting property photos: " + e);
                    }
                }
                return result;
            },
            generatePropertySearchDataForRender: function (searchResults, resultsCount) {
                var self = this;
                self.totalPropertiesCount = resultsCount;
                if(self.totalPropertiesCount <= 0){
                    self.noResultsFound = true;
                }
                else {
                    self.noResultsFound = false
                }

                var propertyResultsToRender = [];
                var self = this;
                for (var i = 0; i <  searchResults.length; i++) {
                    var propertyObjToRender = {}
                    var currProperty = searchResults[i]
                    propertyObjToRender.id = currProperty.id
                    propertyObjToRender.qupid = currProperty.qupid
                    propertyObjToRender.category = self.getRenderableValue(currProperty.category ? currProperty.category.code : '')
                    propertyObjToRender.valRef = currProperty.rollNumber ? currProperty.rollNumber : ''
                    propertyObjToRender.valRef += currProperty.assessmentNumber ? '/'+currProperty.assessmentNumber : ''
                    propertyObjToRender.valRef += currProperty.suffix ? ' ' + currProperty.suffix : ''
                    if (propertyObjToRender.valRef.indexOf('undefined') !== -1) {
                        propertyObjToRender.valRef = ''
                    }
                    var streetNumberSuffix = currProperty.address ? (currProperty.address.streetNumberSuffix ? ' '+currProperty.address.streetNumberSuffix : '') : ''
                    propertyObjToRender.address1 = currProperty.address ? ((currProperty.address.streetNumber ? currProperty.address.streetNumber : '')+streetNumberSuffix+ ' ' + (currProperty.address.streetName ? currProperty.address.streetName : '')  + (currProperty.address.streetType ? ' '+currProperty.address.streetType.description + ',' : '')) : ''
                    if (propertyObjToRender.address1.indexOf('undefined') !== -1) {
                        propertyObjToRender.address1 = ''
                    }
                    propertyObjToRender.address2 = currProperty.address ? (currProperty.address.suburb ? (currProperty.address.suburb+', ') : '') : ''
                    propertyObjToRender.address2 += currProperty.address ? (currProperty.address.town ? currProperty.address.town + ', ' : '') : ''
                    propertyObjToRender.address2 += currProperty.territorialAuthority ? currProperty.territorialAuthority.name : ''
                    if (propertyObjToRender.address2.indexOf('undefined') !== -1) {
                        propertyObjToRender.address2 = ''
                    }

                    var propertyPhotos = self.getPropertyPhotos(currProperty.photos, currProperty.qupid)
                    propertyObjToRender.primaryPhoto = propertyPhotos.primary
                    propertyObjToRender.propertyPhotos = propertyPhotos.list

                    var isNameSecret = false
                    var occupier1 = ''
                    var occupier2 = ''
                    if (currProperty.occupiers) {
                        $.each(currProperty.occupiers, function(i, obj) {
                            if (obj.isNameSecret) {
                                isNameSecret = true
                            }
                            else {
                                    if (i == 0) {
                                    occupier1 = (obj.firstName ? obj.firstName + ' ' : '') + (obj.lastName)
                                }
                                if (i == 1) {
                                    occupier2 = (obj.firstName ? obj.firstName + ' ' : '') + (obj.lastName)
                                }

                            }
                        });

                        if (isNameSecret) {
                            propertyObjToRender.occupier1 = 'Not Available'
                            propertyObjToRender.occupier2 = ''
                        }
                        else {
                            propertyObjToRender.occupier1 = occupier1
                            propertyObjToRender.occupier2 = occupier2
                        }

                    }
                    else {
                        propertyObjToRender.occupier1 = 'Not Available'
                        propertyObjToRender.occupier2 = ''
                    }

                    propertyObjToRender.isMaoriLand = currProperty.landUseData ? (currProperty.landUseData.isMaoriLand ? 'Yes' : 'No') : 'No'
                    propertyObjToRender.landValue = numeral(currProperty.currentValuation ? currProperty.currentValuation.landValue : 0).format('$0,0')
                    propertyObjToRender.capitalValue = numeral(currProperty.currentValuation ? currProperty.currentValuation.capitalValue : 0).format('$0,0')
                    propertyObjToRender.landArea = currProperty.landUseData && currProperty.landUseData.landArea ? currProperty.landUseData.landArea : 0
                    if (typeof propertyObjToRender.landArea == 'number') {
                        propertyObjToRender.landArea = propertyObjToRender.landArea.toFixed(4)
                    }
                    propertyObjToRender.TLA = currProperty.massAppraisalData ? (currProperty.massAppraisalData.totalLivingArea ? currProperty.massAppraisalData.totalLivingArea : '0') : '0'
                    propertyObjToRender.TFA = currProperty.landUseData ? (currProperty.landUseData.totalFloorArea ? currProperty.landUseData.totalFloorArea : '0') : '0'
                    propertyObjToRender.effectiveYearBuilt = self.getRenderableValue(currProperty.massAppraisalData ? currProperty.massAppraisalData.effectiveYearBuilt : '')
                    propertyObjToRender.landUse = self.getRenderableValue(currProperty.landUseData ? (currProperty.landUseData.landUse ? currProperty.landUseData.landUse.description : '') : '')
                    propertyObjToRender.units = self.getRenderableValue(currProperty.landUseData ? currProperty.landUseData.units : '')
                    propertyObjToRender.bedrooms = self.getRenderableValue(currProperty.massAppraisalData ? currProperty.massAppraisalData.bedrooms : '')
                    propertyObjToRender.toilets = self.getRenderableValue(currProperty.massAppraisalData ? currProperty.massAppraisalData.toilets : '')
                    propertyObjToRender.wallConstructionAndCondition = currProperty.landUseData ? (currProperty.landUseData.wallConstruction ? currProperty.landUseData.wallConstruction.description : '') : ''
                    propertyObjToRender.wallConstructionAndCondition += currProperty.landUseData ? (currProperty.landUseData.wallCondition ? ' '+currProperty.landUseData.wallCondition.description : '') : ''
                    propertyObjToRender.wallConstructionAndCondition = self.getRenderableValue(propertyObjToRender.wallConstructionAndCondition)
                    propertyObjToRender.roofConstructionAndCondition = currProperty.landUseData ? (currProperty.landUseData.roofConstruction ? currProperty.landUseData.roofConstruction.description : '') : ''
                    propertyObjToRender.roofConstructionAndCondition += currProperty.landUseData ? (currProperty.landUseData.roofCondition ? ' '+currProperty.landUseData.roofCondition.description : '') : ''
                    propertyObjToRender.roofConstructionAndCondition = self.getRenderableValue(propertyObjToRender.roofConstructionAndCondition)
                    propertyObjToRender.underMainRoofGarages = self.getRenderableValue(currProperty.massAppraisalData ? currProperty.massAppraisalData.underMainRoofGarages : '')
                    propertyObjToRender.freeStandingGarages = self.getRenderableValue(currProperty.massAppraisalData ? currProperty.massAppraisalData.freestandingGarages : '')
                    propertyObjToRender.otherLargeImprovements = self.getRenderableValue(currProperty.massAppraisalData ? (currProperty.massAppraisalData.hasLargeOtherImprovements ? 'Yes' : 'No') : 'No')
                    propertyObjToRender.modernisation = self.getRenderableValue(currProperty.massAppraisalData ? (currProperty.massAppraisalData.isModernised ? 'Yes' : 'No') : 'No')
                    propertyObjToRender.zone = self.getRenderableValue(currProperty.landUseData ? currProperty.landUseData.landZone : '')
                    propertyObjToRender.lotPosition = self.getRenderableValue(currProperty.massAppraisalData ? (currProperty.massAppraisalData.classifications ? (currProperty.massAppraisalData.classifications.lotPosition ? currProperty.massAppraisalData.classifications.lotPosition.description : '') : '') : '')
                    propertyObjToRender.contour = self.getRenderableValue(currProperty.massAppraisalData ? (currProperty.massAppraisalData.classifications ? (currProperty.massAppraisalData.classifications.contour ? currProperty.massAppraisalData.classifications.contour.description : '') : '') : '')
                    var viewDescription = self.getRenderableValue(currProperty.massAppraisalData ? (currProperty.massAppraisalData.classifications ? (currProperty.massAppraisalData.classifications.view ? (currProperty.massAppraisalData.classifications.view.description+' ') : '') : '') : '')
                    var viewCode = self.getRenderableValue(currProperty.massAppraisalData ? (currProperty.massAppraisalData.classifications ? (currProperty.massAppraisalData.classifications.view ? (currProperty.massAppraisalData.classifications.view.code+' ') : '') : '') : '')
                    if(viewCode.trim() == 'N'){
                        propertyObjToRender.viewScope = self.getRenderableValue(currProperty.massAppraisalData ? (currProperty.massAppraisalData.classifications ? (currProperty.massAppraisalData.classifications.viewScope ? currProperty.massAppraisalData.classifications.viewScope.description : '') : '') : '')
                    }else{
                        propertyObjToRender.viewScope = ((viewDescription && viewDescription != '-') ? viewDescription.substring(viewDescription.trim().lastIndexOf(" ")+1) : '') + self.getRenderableValue(currProperty.massAppraisalData ? (currProperty.massAppraisalData.classifications ? (currProperty.massAppraisalData.classifications.viewScope ? currProperty.massAppraisalData.classifications.viewScope.description : '') : '') : '')
                    }

                    propertyObjToRender.production = self.getRenderableValue(currProperty.landUseData ? currProperty.landUseData.production : '-')

                    //Derived Values
                    var valueOfImprovements = 0
                    var landValue = currProperty.currentValuation ? currProperty.currentValuation.landValue : 0
                    var capitalValue = currProperty.currentValuation ? currProperty.currentValuation.capitalValue : 0
                    var totalFloorArea = currProperty.landUseData ? currProperty.landUseData.totalFloorArea : 0
                    var landArea = currProperty.landUseData ? currProperty.landUseData.landArea : 0

                    if (capitalValue) {
                        valueOfImprovements = Math.round(capitalValue - landValue)
                    }
                    propertyObjToRender.valueOfImprovements = numeral(valueOfImprovements).format('$0,0')

                    var cvNetRate = 0
                    if (totalFloorArea > 0 && capitalValue && capitalValue > 0) {
                        cvNetRate = Math.round(capitalValue/totalFloorArea)
                    }
                    propertyObjToRender.cvNetRate = numeral(cvNetRate).format('$0,0')

                    var lvNetRate = 0
                    var lvNetRateHa;
                    if (landArea > 0 && landValue && landValue > 0) {

                        if (landArea < 1){
                            lvNetRate = Math.round(landValue/(landArea*10000))
                        }
                        else {
                            lvNetRate = Math.round(landValue/landArea)
                        }
                        lvNetRateHa = numeral(lvNetRate).format('\$0,0');
                        propertyObjToRender.lvNetRate = lvNetRateHa;
                    }
                    else {
                    propertyObjToRender.lvNetRate = numeral(lvNetRate).format('$0,0')
                    }

                    var viNetRate = 0
                    if (totalFloorArea > 0 && capitalValue && capitalValue > 0 && landValue && landValue > 0) {
                        viNetRate = Math.round((capitalValue - landValue)/totalFloorArea)
                    }
                    propertyObjToRender.viNetRate = numeral(viNetRate).format('$0,0')

                    propertyObjToRender.revisionCapVal = numeral(currProperty.revisedValuation ? currProperty.revisedValuation.capitalValue : 0).format('$0,0')
                    propertyObjToRender.revisionLandVal = numeral(currProperty.revisedValuation ? currProperty.revisedValuation.landValue : 0).format('$0,0')
                    propertyObjToRender.hasRevision = currProperty.revisedValuation ? (currProperty.revisedValuation.capitalValue || currProperty.revisedValuation.landValue) : false

                    var revalCapVal = currProperty.revisedValuation ? currProperty.revisedValuation.capitalValue : 0
                    var revalLandVal = currProperty.revisedValuation ? currProperty.revisedValuation.landValue : 0
                    var revalValueOfImprovements = 0
                    if (revalCapVal > 0 && revalLandVal > 0) {
                        revalValueOfImprovements = Math.round(revalCapVal - revalLandVal)
                    }
                    propertyObjToRender.revalValueOfImprovements = numeral(revalValueOfImprovements).format('$0,0')

                    var revalCapValNetRate = 0
                    if (revalCapVal > 0 && totalFloorArea > 0) {
                        revalCapValNetRate = Math.round(revalCapVal/totalFloorArea)
                    }
                    propertyObjToRender.revalCapValNetRate = numeral(revalCapValNetRate).format('$0,0')

                    var revalLandValNetRate = 0
                    var revalLandValNetRateHa;
                    if (revalLandVal > 0 && landArea > 0) {

                        if (landArea < 1){
                            revalLandValNetRate = Math.round(revalLandVal/(landArea*10000))
                        }
                        else {
                            revalLandValNetRate = Math.round(revalLandVal/landArea)
                        }
                        revalLandValNetRateHa = numeral(revalLandValNetRate).format('\$0,0');
                        propertyObjToRender.revalLandValNetRate = revalLandValNetRateHa;
                        }
                    else {
                        propertyObjToRender.revalLandValNetRate  = numeral(revalLandValNetRateHa).format('$0,0')
                    }

                    var revalValueOfImprovementsNetRate = 0
                    if (totalFloorArea > 0) {
                        revalValueOfImprovementsNetRate = Math.round((revalCapVal - revalLandVal)/totalFloorArea)
                    }
                    propertyObjToRender.revalValueOfImprovementsNetRate = numeral(revalValueOfImprovementsNetRate).format('$0,0')

                    var capitalValueDiff = 0
                    if (capitalValue && capitalValue > 0 && revalCapVal > 0) {
                        capitalValueDiff = ((revalCapVal * 100)/capitalValue) - 100
                    }
                    propertyObjToRender.capitalValueDiff = (Math.round(capitalValueDiff*10))/10

                    var landValueDiff = 0
                    if (landValue && landValue > 0 && revalLandVal > 0) {
                        landValueDiff = ((revalLandVal * 100)/landValue) - 100
                    }
                    propertyObjToRender.landValueDiff = (Math.round(landValueDiff*10))/10

                    var valueOfImprovementsDiff = 0
                    if (valueOfImprovements > 0) {
                        valueOfImprovementsDiff = (((revalCapVal - revalLandVal)*100)/valueOfImprovements)-100
                    }
                    propertyObjToRender.valueOfImprovementsDiff = (Math.round(valueOfImprovementsDiff*10))/10

                    propertyObjToRender.coordinates = currProperty.coordinates;

                    propertyResultsToRender.push(propertyObjToRender)

                }
                //this.searchResults = propertyResultsToRender;
                return propertyResultsToRender
            },
            refreshResultRow: function(propertyId, qupid) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.MediaController.getMediaByOwner(propertyId).url,
                    cache: false,
                    success: function (photoData) {
                        var propertyPhotos = [];
                        var primaryPhoto = 'assets/images/property/addPhotos.png';
                        $.each(photoData, function(i, obj) {
                            if(obj.isPrimary) {
                                primaryPhoto = obj.mediaItem.smallImageUrl;
                            }
                            propertyPhotos.push({'id':obj.id, 'propertyId':obj.ownerId, 'qupid':qupid, 'link':obj.mediaItem.mediumImageUrl});
                        });
                        if(self.properties) {
                            self.properties.filter(function(data){ return data.id == propertyId })[0].propertyPhotos = propertyPhotos;
                            self.properties.filter(function(data){ return data.id == propertyId })[0].primaryPhoto = primaryPhoto;
                        }
                        self.registerPhotoClickHandler();
                    },
                    error: function (response) {
                        console.log("Error from getMediaByOwner: " + response);
                        self.errorHandler(response);
                    }
                });
            },
            registerPhotoClickHandler: function(){
                var self = this
                $('.photoGallery_thumb').off("click").click(function(evt){
                    var propertyId = $(this).data('property');
                    var photoId = $(this).data('id');
                    var qupid = $(this).data('qupid');
                    var path = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?propertyId='+propertyId+'&photoId='+photoId;
                    var searchWindow = window.open(path,'PropertyPhotos','scrollbars=no,resizable=yes,height=800,width=1024');
                    searchWindow.focus();
                    var timer = setInterval(function() {
                        if(searchWindow.closed == true) {
                            self.refreshResultRow(propertyId, qupid);
                            clearInterval(timer);
                        }
                    }, 1000);

                });
            },
            registerPhotoUploaderHandler: function(){
                var self = this;
                $('.photo-uploader').off("click").click(function(evt){
                    var propertyId = $(this).data('property');
                    var qupid = $(this).data('qupid');
                    var path = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?propertyId='+propertyId;
                    var searchWindow = window.open(path,'PropertyPhotos','scrollbars=no,resizable=yes,height=800,width=1024');
                    searchWindow.focus();
                    var timer = setInterval(function() {
                        if(searchWindow.closed == true) {
                            self.refreshResultRow(propertyId, qupid);
                            clearInterval(timer);
                        }
                    }, 1000);
                });
            },
            openUrlInNewTab(url) {
                openUrlInNewTab(url);
            }
        },
        watch: {
            properties: function(newValue, oldValue) {
                if(this.totalPropertiesCount > 0 && newValue && newValue.length > 0) {
                    this.fetchImages();
                }
            }
        },
        mounted: function() {
            const self = this;

            //This function will be available to the child windows
            window.refreshFromRelink = function(eventData) {
                setTimeout(function() {
                    self.refreshResultRow(eventData.propID, eventData.qupID);
                }, 1500);
            };

            EventBus.$on('display-content', function(event) {
                var searchType = event.searchType
                if (searchType && searchType == 'property-search') {
                    var searchParams = event.searchParams;
                    self.showTemplate=true;
                    self.noResultsFound = false;
                    self.imageFetchingOffset = 0;
                    var m = jsRoutes.controllers.PropertyController.displayPropertySearchResult();
                    self.searchParams = searchParams
                    if (self.sortField && self.sortField != '') {
                        self.searchParams.sort = self.getSortFields(self.sortField)
                    }
                    if (self.sortOrder && self.sortOrder != '') {
                        self.searchParams.order = self.sortOrder
                    }
                    $.ajax({
                        type: "POST",
                        url: m.url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(searchParams),
                        dataType: "json",
                        success: function (response) {
                            var totalResults = response.totalResults;
                            if(totalResults == 1) {
                                var event = {};
                                event.searchType = 'master-details';
                                event.propertyId = response.properties[0].qupid;
                                EventBus.$emit('display-content', event);
                            } else {
                                self.properties = self.generatePropertySearchDataForRender(response.properties, totalResults)
                            }
                        },
                        error: function (response) {
                            console.log('error fetch property search results: '+response);
                            self.errorHandler(response);
                        }
                    });
                }
                else {
                    self.totalPropertiesCount = 0
                    self.properties = null
                    self.showTemplate=false
                    $(window).off('scroll');
                    $(window).unbind('scroll');
                    $('#desktop').find($('a')).off("click")
                    $('.resultsRow').off("click")
                    $('.expandAll').off("click")
                    $('.closer').off("click")
                    $('.extrasWrapper').off("click")
                    $('.resultsRow').removeClass('openProp mdl-shadow--2dp');
                    $('.searchbarWrapper').removeClass('fixed');
                }
            });
        },
        updated: function() {
            const self = this;
            if (self.showTemplate == true) {
                self.registerPhotoUploaderHandler();
                self.registerPhotoClickHandler();
                /*if ($('.resultsRow').length == 0) {
                    $('.noResults-wrapper').removeClass('hide');
                }
                else {
                    $('.noResults-wrapper').addClass('hide');
                }*/

                var isExpandAll = $(".expandAll").hasClass('down')
                if (isExpandAll) {
                    $('.resultsRow').addClass('openProp mdl-shadow--2dp');
                }

                var isCondensedViewOn = $('.condensedView').prop('checked');
                if (isCondensedViewOn == false) {
                    $('.resultsRow').removeClass('theSkinny');
                }
                else {
                    $('.resultsRow').addClass('theSkinny');
                }

                $('.resultsRow').off("click").click(function(evt) {
                    if ($(evt.target).closest('.closer').length == 0) {
                        $(this).addClass('openProp mdl-shadow--2dp');
                    }
                });

                $('.tagSelect select').on('change',function() {
                    $(this).addClass('tagSelected');
                    $('.tagSelect').addClass('tagSelected');
                });

                $(".condensedView").off("change").on('change', function () {
                    var isCondensedViewOn = $('.condensedView').prop('checked');
                    if (isCondensedViewOn == false) {
                        $('.resultsRow').removeClass('theSkinny');
                    }
                    else {
                        $('.resultsRow').addClass('theSkinny');
                    }
                });

                $('.expandAll').off("click").click(function(evt) {
                    if ($('.resultsRow').hasClass('openProp mdl-shadow--2dp')) {
                        $('.resultsRow').removeClass('openProp mdl-shadow--2dp');
                        $('.expandAll').removeClass('down');
                    } else {
                        $('.resultsRow').addClass('openProp mdl-shadow--2dp');
                        //$('.extras').slideToggle();
                        $('.expandAll').addClass('down');
                    }
                });

                $('.closer').off("click").click(function(evt){
                    $(evt.target).closest('.resultsRow').removeClass('openProp mdl-shadow--2dp');
                    $(evt.target).closest('.taList').css('display', 'none');
                    $('body').removeClass('stopScrolling');
                });

                $('.extrasWrapper').off("click").click(function(evt){
                    $(this).find('.extras').slideToggle();
                    $('i').toggleClass('down');
                });

                $('#desktop').find($('a')).off("click").click(function () {
                    var liParent = $(this).parent();
                    var allOptions = $('#desktop').find('.sortRow').find('.colHeader');
                    var allOptionsMobile = $('#mobile').find('.sortRow').find('.colHeader');

                    if (liParent.hasClass('active')) {
                        if ($(this).find('i').hasClass('up')){
                            $(this).find('i').addClass('down');
                            $(this).find('i').removeClass('up');
                        }
                        else {
                            $(this).find('i').addClass('up');
                            $(this).find('i').removeClass('down');
                        }
                    } else {
                        allOptions.removeClass('active');
                        allOptions.find('i').removeClass('up');
                        allOptions.find('i').addClass('down');
                        liParent.addClass('active');
                    }

                    var sort = liParent.data('sort');

                    var order = $(this).find('i').hasClass('up');
                    if (order == true) {
                        order = "DESC";
                    } else {
                        order = "ASC";
                    }
                    self.searchParams.order=order
                    self.searchParams.sort = self.getSortFields(sort)
                    self.searchParams.offset = 0
                    var m = jsRoutes.controllers.PropertyController.displayPropertySearchResult();
                    $.ajax({
                        type: "POST",
                        url: m.url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(self.searchParams),
                        dataType: "json",
                        success: function (response) {
                            self.sortField = sort
                            self.sortOrder = order
                            var totalResults = response.totalResults
                            self.properties = self.generatePropertySearchDataForRender(response.properties, totalResults)
                            self.imageFetchingOffset = 0
                        },
                        error: function (response) {
                            self.errorHandler(response);
                        }
                    });
                });


                $(window).data('ajaxready', true).scroll(function(e) {
                    var sticky = $('.searchbarWrapper'),
                        scroll = $(window).scrollTop(),
                        winHeight = $(window).height();
                    if (scroll >= 153) sticky.addClass('fixed');
                    else sticky.removeClass('fixed');
                    if ($(window).data('ajaxready') == false) return;
                    if(Math.ceil(scroll + winHeight) >= ($(document).height()-130)) {
                        $(window).data('ajaxready', false);
                        if($('.resultsRow:visible').length >= 25) {
                            $('.loadingSpinnerSearchResults').show();
                            if($('.resultsRow').length > 1) {
                                self.searchParams.offset = self.properties.length;
                                var m = jsRoutes.controllers.PropertyController.displayPropertySearchResult();
                                $.ajax({
                                    type: "POST",
                                    url: m.url,
                                    cache: false,
                                    contentType: "application/json; charset=utf-8",
                                    data: JSON.stringify(self.searchParams),
                                    dataType: "json",
                                    success: function (response) {
                                        $('.loadingSpinnerSearchResults').hide();
                                        var totalResults = response.totalResults
                                        var results = self.generatePropertySearchDataForRender(response.properties, totalResults)
                                        var newResultSet = self.properties.concat(results)
                                        self.properties = newResultSet
                                        $(window).data('ajaxready', true);
                                    },
                                    error: function (response) {
                                        self.errorHandler(response);
                                    }
                                });
                            }
                        }
                    }
                });
            }
        }
    }
</script>
<style lang="scss" scoped>
    .icon--flipped {
        transform: scale(-1, 1);
    }
</style>

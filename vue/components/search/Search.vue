<template xmlns:v-on="http://www.w3.org/1999/xhtml">
    <div class="advSearch-wrapper" style="height: 704px;">
        <div class="advSearch-row divider advSearchForm">
            <div class="searchType-wrapper">
                <span class="advSearch-tab">
				    <input type="radio" name="searchTab" value="1" id="propertySearch-tab" checked="checked" @click="advView('property')"/>
				    <label for="propertySearch-tab">Property Search</label>
			    </span>
                <span class="advSearch-tab">
                    <input type="radio" name="searchTab" value="2" id="salesSearch-tab" @click="advView('sales')"/>
                    <label for="salesSearch-tab">Sales Search</label>
                </span>
            </div>
        </div>

        <div class="advSearch-row divider advSearchForm">
                <territorial-authority taId="advSearchTa"></territorial-authority>
                <ta-range></ta-range>
                <region-type></region-type>
                <div id="salesGroupsAndRollsDiv" class="advSearch-group noMargin advSearchForm advSearch-row-dependent disabled">
                    <label></label>
                    <span class="fieldTwo noMargin salesgroups-btn mdl-button mdl-js-button mdl-button--raised mdl-button--colored salesGroupButton"
                          data-upgraded=",MaterialButton" @click="openSalesGroup()">Sales Groups &amp; Rolls</span>
                    <span class="rollCount" data-cy="sales-groups-and-rolls-roll-count"></span>
                </div>
        </div>

        <div class="advSearch-row divider advSearchForm">
                <range title="Street Number" group="streetNumber" decimal="true"></range>
                <div class="advSearch-group  advSearch-row-dependent disabled">
                    <label>Street Name</label>
                    <span class="fieldFive">
                        <input v-on:keyup.13="doSearch()" id="streetName" type="text" class="advSearch-text advSearchStreetName">
                    </span>
                </div>

                <single-select id="streetTypeDiv" selectid="streetType" title="Street Type" event="streetType" :options="streetTypes" data-cy="street-type"></single-select>
            </div>

        <div class="searchScroll-wrapper advSearchForm">
            <div class="advSearch-inputs" v-show="showPropertyForm">
                <adv-property-search></adv-property-search>
            </div>
            <div class="saleSearch-inputs" v-show="showSalesForm">
                <adv-sales-search :isInternalUser="isInternalUser"></adv-sales-search>
            </div>
        </div>
        <div class="advSearch-footer advSearch-row-dependent advSearchForm disabled">
            <div class="buttonBar">
                <span id="advSearchBtn" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored advSearchSubmit"
                      @click="doSearch()">Search</span>
                <span class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect advSearchClear" @click="clear()">Clear</span>
            </div>
        </div>
        <div class="advSearch-header divider salesGroupForm" style="display: none;">
            <h3 class="lefty">Sales Groups and Rolls</h3>
            <span class="righty"><i class="material-icons md-dark salesGroupForm-close" @click="closeSalesGroup()">&#xE5CD;</i></span>
        </div>
        <sales-group-and-rolls  style="display: none;"></sales-group-and-rolls>
        <div class="advSearch-footer salesGroupForm" id="advSearchFooterSalesGroupDiv" style="display: none;">
            <div class="buttonBar">
                <span class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored salesGroupAndRolls-setRolls" @click="setRolls()" data-cy="sales-groups-and-rolls-set-rolls">Set rolls</span>
                <span class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect salesGroupAndRolls-clear" @click="clearRolls()" data-cy="sales-groups-and-rolls-clear">Clear</span>
            </div>
        </div>
    </div>
</template>

<script>
    import { mapState, mapGetters } from 'vuex';
    import TerritorialAuthority from '../filters/TerritorialAuthority.vue'
    import TaRange from '../filters/TARange.vue'
    import RegionType from '../filters/RegionType.vue'
    import SalesGroupAndRolls from '../filters/SalesGroupAndRolls.vue'
    import Range from '../filters/Range.vue'
    import SingleSelect from '../filters/SingleSelect.vue'
    import AdvPropertySearch from './AdvancedPropertySearch.vue'
    import AdvSalesSearch from './AdvancedSalesSearch.vue'
    import {EventBus} from '../../EventBus.js'
    import { store } from '../../DataStore.js';

    export default {
        components: {
            TerritorialAuthority,
            TaRange,
            RegionType,
            SalesGroupAndRolls,
            Range,
            SingleSelect,
            AdvSalesSearch,
            AdvPropertySearch
        },
        data: function() {
            return {
                showSalesForm: false,
                showPropertyForm: true,
                taCodes: [],
                taRange: [],
                regionCodes: [],
                locationCriteria: {},
                salesStreetType: '',
                propertyStreetType: '',
                salesIncludedCategories: null,
                salesExcludedCategories: null,
                salesLandValueFrom: null,
                salesLandValueTo: null,
                salesCapitalValueFrom: null,
                salesCapitalValueTo: null,
                grossSalePriceFrom: null,
                grossSalePriceTo: null,
                netSalePriceFrom: null,
                netSalePriceTo: null,
                netSalePricePerSquareMetreFrom: null,
                netSalePricePerSquareMetreTo: null,
                salesLandAreaFrom: null,
                salesLandAreaTo: null,
                salesFloorAreaFrom: null,
                salesFloorAreaTo: null,
                salesBedroomCountFrom: null,
                salesBedroomCountTo: null,
                salesBuildingAges: null,
                saleStatusCodes : ['1','3'],
                saleTypeCodes: ['S'],
                saleTenureCodes: ['1'],
                priceValueRelationshipCodes: ['1','2'],
                saleDateFrom: null,
                saleDateTo: null,
                saleInputDateFrom: null,
                saleInputDateTo: null,
                propertyIncludedCategories: null,
                propertyExcludedCategories: null,
                propertyLandValueFrom: null,
                propertyLandValueTo: null,
                propertyCapitalValueFrom: null,
                propertyCapitalValueTo: null,
                propertyCapitalValueGrossRateFrom: null,
                propertyCapitalValueGrossRateTo: null,
                propertyLandValueGrossRateFrom: null,
                propertyLandValueGrossRateTo: null,
                propertyVIGrossRateFrom: null,
                propertyVIGrossRateTo: null,
                propertyLandAreaFrom: null,
                propertyLandAreaTo: null,
                propertyTotalFloorAreaFrom: null,
                propertyTotalFloorAreaTo: null,
                propertyBedroomsFrom: null,
                propertyBedroomsTo: null,
                propertyBuildingAges: null,
                propertyWallMaterials: null,
                propertyWallConditions: null,
                propertyRoofMaterials: null,
                propertyRoofConditions: null,
                propertyLandUses: null,
                propertyProductionFrom: null,
                propertyProductionTo: null,
                offset: 0,
                max: 25,
                salesSortOrder: 'desc',
                propertySortOrder: 'asc',
                salesSort: ['SALE_DATE'],
                propertySort: ['TA_CODE', 'STREET_NAME', 'STREET_TYPE', 'STREET_NUMBER', 'STREET_NUMBER_SUFFIX', 'VALUATION_REFERENCE'],
            }
        },
        methods: {
            advView: function(name) {
                var self = this;
                if (name === 'property') {
                    self.showPropertyForm = true;
                    self.showSalesForm = false;
                }
                else {
                    self.showSalesForm = true;
                    self.showPropertyForm = false;
                }

            },
            collectStreetName: function() {
                if($('.advSearchStreetName').val()) {
                    this.locationCriteria["streetName"] = $('.advSearchStreetName').val();
                } else {
                    delete this.locationCriteria["streetName"];
                }
            },
            doSearch: function() {
                var self = this;
                if (self.showPropertyForm) {
                    self.doPropertySearch();
                }
                else if (self.showSalesForm) {
                    self.doSalesSearch();
                }
            },
            doPropertySearch: function(){
                console.log('Perform property search');
                $('.advSearch-wrapper').hide();
                this.collectStreetName();
                var propertyIncludedCategoriesList = [];
                var propertyExcludedCategoriesList = [];
                $(".propertyIncludedCategories").each(function() {
                    if($(this).val() && $(this).val().trim().length > 0) {
                        propertyIncludedCategoriesList.push($(this).val());
                    }
                });
                $(".propertyExcludedCategories").each(function() {
                    if($(this).val()&& $(this).val().trim().length > 0) {
                        propertyExcludedCategoriesList.push($(this).val());
                    }
                });

                if(propertyIncludedCategoriesList.length > 0) {
                    this.propertyIncludedCategories = propertyIncludedCategoriesList;
                } else {
                    this.propertyIncludedCategories = null;
                }

                if(propertyExcludedCategoriesList.length > 0) {
                    this.propertyExcludedCategories = propertyExcludedCategoriesList;
                } else {
                    this.propertyExcludedCategories = null;
                }
                var searchFilters = {
                    taCodes: this.locationCriteria["taCodes"],
                    regionCodes: this.regionCodes,
                    rolls: this.locationCriteria["rolls"],
                    streetNumberFrom: this.locationCriteria["streetNumberFrom"],
                    streetNumberTo: this.locationCriteria["streetNumberTo"],
                    streetName: this.locationCriteria["streetName"],
                    streetType: this.propertyStreetType,
                    includedCategories: this.propertyIncludedCategories,
                    excludedCategories: this.propertyExcludedCategories,
                    capitalValueFrom: this.propertyCapitalValueFrom,
                    capitalValueTo: this.propertyCapitalValueTo,
                    landValueFrom: this.propertyLandValueFrom,
                    landValueTo: this.propertyLandValueTo,
                    cvGrossRateFrom: this.propertyCapitalValueGrossRateFrom,
                    cvGrossRateTo: this.propertyCapitalValueGrossRateTo,
                    lvGrossRateFrom: this.propertyLandValueGrossRateFrom,
                    lvGrossRateTo: this.propertyLandValueGrossRateTo,
                    viGrossRateFrom: this.propertyVIGrossRateFrom,
                    viGrossRateTo: this.propertyVIGrossRateTo,
                    landAreaFrom: this.propertyLandAreaFrom,
                    landAreaTo: this.propertyLandAreaTo,
                    totalFloorAreaFrom: this.propertyTotalFloorAreaFrom,
                    totalFloorAreaTo: this.propertyTotalFloorAreaTo,
                    productionFrom: this.propertyProductionFrom,
                    productionTo: this.propertyProductionTo,
                    bedroomCountFrom: this.propertyBedroomsFrom,
                    bedroomCountTo: this.propertyBedroomsTo,
                    buildingAgeCodes: this.propertyBuildingAges,
                    wallConstructionCodes: this.propertyWallMaterials,
                    wallConditionCodes: this.propertyWallConditions,
                    roofConstructionCodes: this.propertyRoofMaterials,
                    roofConditionCodes: this.propertyRoofConditions,
                    landUseCodes: this.propertyLandUses,
                    offset: this.offset,
                    max: this.max,
                    order: this.propertySortOrder,
                    sort: this.propertySort
                };

                console.log('property search criteria: '+searchFilters);

                var eventObj = {};
                eventObj.searchParams = searchFilters;
                eventObj.searchType = 'property-search';
                //eventObj.onHomePage = true
                EventBus.$emit('display-content', eventObj);
            },
            doSalesSearch: function() {
                $('.advSearch-wrapper').hide();
                this.collectStreetName();
                var salesIncludedCategoriesList = [];
                var salesExcludedCategoriesList = [];
                $(".salesIncludedCategories").each(function() {
                    if($(this).val() && $(this).val().trim().length > 0) {
                        salesIncludedCategoriesList.push($(this).val());
                    }
                });
                $(".salesExcludedCategories").each(function() {
                    if($(this).val()&& $(this).val().trim().length > 0) {
                        salesExcludedCategoriesList.push($(this).val());
                    }
                });

                if(salesIncludedCategoriesList.length > 0) {
                    this.salesIncludedCategories = salesIncludedCategoriesList;
                } else {
                    this.salesIncludedCategories = null;
                }

                if(salesExcludedCategoriesList.length > 0) {
                    this.salesExcludedCategories = salesExcludedCategoriesList;
                } else {
                    this.salesExcludedCategories = null;
                }

                var searchFilters = {
                    locationCriteria: this.locationCriteria,
                    includedCategories: this.salesIncludedCategories,
                    excludedCategories: this.salesExcludedCategories,
                    landValueFrom: this.salesLandValueFrom,
                    landValueTo: this.salesLandValueTo,
                    capitalValueFrom: this.salesCapitalValueFrom,
                    capitalValueTo: this.salesCapitalValueTo,
                    saleDateFrom: this.saleDateFrom,
                    saleDateTo: this.saleDateTo,
                    saleInputDateFrom: this.saleInputDateFrom,
                    saleInputDateTo: this.saleInputDateTo,
                    priceCriteria : {
                        grossSalePriceFrom: this.grossSalePriceFrom,
                        grossSalePriceTo: this.grossSalePriceTo,
                        netSalePriceFrom: this.netSalePriceFrom,
                        netSalePriceTo: this.netSalePriceTo,
                        netSalePricePerSquareMetreFrom: this.netSalePricePerSquareMetreFrom,
                        netSalePricePerSquareMetreTo: this.netSalePricePerSquareMetreTo
                    },
                    landUseCriteria: {
                        landAreaFrom: this.salesLandAreaFrom,
                        landAreaTo: this.salesLandAreaTo,
                        floorAreaFrom: this.salesFloorAreaFrom,
                        floorAreaTo: this.salesFloorAreaTo,
                        bedroomCountFrom: this.salesBedroomCountFrom,
                        bedroomCountTo: this.salesBedroomCountTo,
                        buildingAges: this.salesBuildingAges
                    },
                    classificationCriteria : {
                        saleStatusCodes : this.isInternalUser ? this.saleStatusCodes : ['1'],
                        saleTypeCodes: this.saleTypeCodes,
                        saleTenureCodes: this.saleTenureCodes,
                        priceValueRelationshipCodes: this.priceValueRelationshipCodes
                    },
                    offset: this.offset,
                    max: this.max,
                    order: this.salesSortOrder,
                    sort: this.salesSort
                };

                if(!this.isInternalUser && !this.externalObjectionAccess) {
                    console.log("EXTERNAL USER");
                    if(!searchFilters.locationCriteria.taCodes) {
                        searchFilters.locationCriteria.taCodes = this.allowedTACodes;
                    }
                    if(!searchFilters.locationCriteria.regionCodes) {
                        searchFilters.locationCriteria.regionCodes = this.allowedRCCodes;
                    }
                }

                var eventObj = {};
                eventObj.searchParams = searchFilters;
                eventObj.searchType = 'sales-search';
                //eventObj.onHomePage = true
                EventBus.$emit('display-content', eventObj);
            },
            clear: function() {
                $('.advSearch-singleselect option:selected').each(function() {
                    $(this).prop('selected', false);
                });
                $('.advSearch-singleselect').append('<option selected class="default-singleselect" value="">Select...</option>');
                $('.advSearch-singleselect').multiselect('refresh');

                $('.advSearch-text').val("");
                $('.salesIncludedCategories').val("");
                $('.salesExcludedCategories').val("");
                $('.propertyIncludedCategories').val("");
                $('.propertyExcludedCategories').val("");
                $('.advSearch-group').removeClass("valError");
                $('.saleStatusChk').attr('checked', false);
                $('.dateRangeSpan').text('');

                this.locationCriteria = {};
                if(this.taCodes.length > 0) {
                    this.locationCriteria["taCodes"] = this.taCodes;
                    $('.advSearch-row-dependent').removeClass('disabled');
                } else  {
                    $('.advSearch-row-dependent').addClass('disabled');
                    $('.advSearchSalesGroupAndRolls').addClass('disabled');
                this.taCodes = [];
                }
                this.taRange = [];
                this.regionCodes = [];

                this.salesIncludedCategories = null;
                this.salesExcludedCategories = null;
                this.salesLandValueFrom = null;
                this.salesLandValueTo = null;
                this.salesCapitalValueFrom = null;
                this.salesCapitalValueTo = null;
                this.grossSalePriceFrom = null;
                this.grossSalePriceTo = null;
                this.netSalePriceFrom = null;
                this.netSalePriceTo = null;
                this.netSalePricePerSquareMetreFrom = null;
                this.netSalePricePerSquareMetreTo = null;
                this.salesLandAreaFrom = null;
                this.salesLandAreaTo = null;
                this.salesFloorAreaFrom = null;
                this.salesFloorAreaTo = null;
                this.salesBedroomCountFrom = null;
                this.salesBedroomCountTo = null;
                this.salesBuildingAges = null;
                this.saleTypeCodes = ["S"];
                this.saleTenureCodes = ["1"];
                this.priceValueRelationshipCodes = ["1","2"];
                this.saleStatusCodes = ["1", "3"];
                this.saleDateFrom = null;
                this.saleDateTo = null;
                this.saleInputDateFrom = null;
                this.saleInputDateTo = null;
                this.salesStreetType = null;

                this.propertyIncludedCategories = null;
                this.propertyExcludedCategories = null;
                this.propertyCapitalValueFrom = null;
                this.propertyCapitalValueTo = null;
                this.propertyLandValueFrom = null;
                this.propertyLandValueTo = null;
                this.propertyCapitalValueGrossRateFrom = null;
                this.propertyCapitalValueGrossRateTo = null;
                this.propertyLandValueGrossRateFrom = null;
                this.propertyLandValueGrossRateTo = null;
                this.propertyVIGrossRateFrom = null;
                this.propertyVIGrossRateTo = null;
                this.propertyLandAreaFrom = null;
                this.propertyLandAreaTo = null;
                this.propertyTotalFloorAreaFrom = null;
                this.propertyTotalFloorAreaTo = null;
                this.propertyBedroomsFrom = null;
                this.propertyBedroomsTo = null;
                this.propertyBuildingAges = null;
                this.propertyWallMaterials = null;
                this.propertyWallConditions = null;
                this.propertyRoofMaterials = null;
                this.propertyRoofConditions = null;
                this.propertyLandUses = null;
                this.propertyProductionFrom = null;
                this.propertyProductionTo = null;
                this.propertyStreetType = null;

                EventBus.$emit("multiselect-clear-values-wallMaterial", null);
                EventBus.$emit("multiselect-clear-values-wallCondition", null);
                EventBus.$emit("multiselect-clear-values-roofMaterial", null);
                EventBus.$emit("multiselect-clear-values-roofCondition", null);
                EventBus.$emit("multiselect-clear-values-regionType", null);
                EventBus.$emit("multiselect-clear-values-streetType", null);
                EventBus.$emit("multiselect-clear-values-landUse", null);
                EventBus.$emit("multiselect-clear-values-buildingAge", null);
                EventBus.$emit("clear-sales-search-filters", {hasTaCodes: this.taCodes.length > 0});

                this.clearRolls();
            },
            setRolls: function() {
                var selectedRolls = [];
                var self = this;
                $('.taCheckbox, .sgCheckbox, .rnCheckBox').each(function () {
                    if ($(this).prop('checked')) {
                        if($.inArray(parseInt($(this).data('ta')), self.locationCriteria["taCodes"]) >= 0) {
                            $(this).addClass('salesGroupAndRolls-saved');
                            if($(this).hasClass('rnCheckBox')) {
                                selectedRolls.push({taCode: parseInt($(this).data('ta')), rollNumber: parseInt($(this).data('roll'))});
                            }
                        } else {
                            $(this).prop('checked', false);
                            $(this).removeClass('salesGroupAndRolls-saved');
                        }
                    }
                    else {
                        $(this).removeClass('salesGroupAndRolls-saved');
                    }
                });
                if(selectedRolls.length > 0) {
                    self.locationCriteria["rolls"] = selectedRolls;
                } else {
                    delete self.locationCriteria["rolls"];
                }
                $('.salesGroupForm').hide();
                $('.advSearchForm').show();
                $('.searchScroll-wrapper').show();
                self.setRollCountBubble();
            },
            clearRolls: function() {
                $('.taCheckbox, .sgCheckbox, .rnCheckBox').each(function () {
                    $(this).prop('checked', false);
                    $(this).removeClass('salesGroupAndRolls-saved');
                });
                $('.rollCount').css('display', 'none');
            },
            setRollCountBubble: function() {
                var rollCount = $('.rnCheckBox:checked').length;
                if (rollCount > 0) {
                    $('.rollCount').css('display', 'block');
                    $('.rollCount').empty();
                    $('.rollCount').append(rollCount);
                }
                else {
                    $('.rollCount').css('display', 'none');
                }
            },
            openSalesGroup: function() {
                $('.salesGroup-ta').addClass('hide');
                var selected = this.locationCriteria["taCodes"];
                for(var taCode in selected) {
                    if (taCode) {
                        $('.salesGroup-ta' + (selected[taCode] < 10 ? '0' + selected[taCode]: selected[taCode])).removeClass('hide');
                    }
                }
                $('.salesGroupForm').show();
                $('.advSearchForm').hide();
            },
            closeSalesGroup: function() {
                $('.salesGroupForm').hide();
                $('.advSearchForm').show();
                $('.taCheckbox, .sgCheckbox, .rnCheckBox').each(function () {
                    if ($(this).hasClass('salesGroupAndRolls-saved')) {
                        $(this).prop('checked', true);
                    }
                    else {
                        $(this).prop('checked', false);
                    }
                });
                this.setRollCountBubble();
            }
        },
        computed: {
            ...mapState('userData', [
                'isInternalUser',
                'allowedTACodes',
                'allowedRCCodes',
                'externalObjectionAccess',
            ]),
            ...mapGetters([
                'getCategoryClassifications',
            ]),
            streetTypes() {
                const categoryClassifications = this.getCategoryClassifications('StreetType');
                const output = [];
                for (const streetType of categoryClassifications) {
                    output.push({
                        label: streetType.description,
                        value: streetType.code.trim(),
                    });
                }
                return output;
            },
        },
        mounted: function() {
            const self = this;
            EventBus.$on('taCodes', function(taCodes) {
                self.taCodes = taCodes;
                if(taCodes.length != 0 && self.taRange.length == 0)  {
                    self.locationCriteria["taCodes"] = taCodes;
                    $('.advSearch-row-dependent').removeClass('disabled');
                } else {
                    delete self.locationCriteria["taCodes"];
                    if(self.regionCodes.length  == 0 && self.taRange.length == 0) {
                        $('.advSearch-row-dependent').addClass('disabled');
                    }
                }
                self.setRolls();
            });
            EventBus.$on('taRange', function(taRange) {
                self.taRange = taRange;
                if(taRange.length != 0) {
                    self.locationCriteria["taCodes"] = taRange;
                    $('.advSearch-row-dependent').removeClass('disabled');
                    self.setRolls();
                } else {
                    if(self.taCodes.length != 0) {
                        self.locationCriteria["taCodes"] = self.taCodes;
                        $('.advSearch-row-dependent').removeClass('disabled');
                        self.setRolls();
                    } else {
                        delete self.locationCriteria["taCodes"];
                        if(self.regionCodes.length == 0) {
                            $('.advSearch-row-dependent').addClass('disabled');
                        }
                    }
                }
                self.setRolls();
            });
            EventBus.$on('regionCodes', function(regionCodes) {
                self.regionCodes = regionCodes;
                if(regionCodes.length != 0)  {
                    self.locationCriteria["regionCodes"] = regionCodes;
                    $('.advSearch-row-dependent').removeClass('disabled');
                } else {
                    delete self.locationCriteria["regionCodes"];
                    if(self.taCodes.length == 0 && self.taRange.length == 0) {
                        $('.advSearch-row-dependent').addClass('disabled');
                    }
                }
            });
            EventBus.$on("streetNumber", function(message) {
                if(message) {
                    self.locationCriteria["streetNumberFrom"] = message.from;
                    self.locationCriteria["streetNumberTo"] = message.to;
                } else {
                    delete self.locationCriteria["streetNumberFrom"];
                    delete self.locationCriteria["streetNumberTo"];
                }
            });
            EventBus.$on("streetType", function(message) {
                if(message) {
                    self.propertyStreetType = message.text;
                    self.salesStreetType = message.val;
                    self.locationCriteria["streetType"] = message.val
                } else {
                    delete self.locationCriteria["streetType"];
                }
            });
            EventBus.$on("salesLandValue", function(message) {
                if(message) {
                    self.salesLandValueFrom = message.from;
                    self.salesLandValueTo = message.to;
                } else {
                    self.salesLandValueFrom = null;
                    self.salesLandValueTo = null;
                }
            });
            EventBus.$on("propertyLandValue", function(message) {
                if(message) {
                    self.propertyLandValueFrom = message.from;
                    self.propertyLandValueTo = message.to;
                } else {
                    self.propertyLandValueFrom = null;
                    self.propertyLandValueTo = null;
                }
            });
            EventBus.$on("salesCapitalValue", function(message) {
                if(message) {
                    self.salesCapitalValueFrom = message.from;
                    self.salesCapitalValueTo = message.to;

                } else {
                    self.salesCapitalValueFrom = null;
                    self.salesCapitalValueTo = null;
                }
            });
            EventBus.$on("propertyCapitalValue", function(message) {
                if(message) {
                    self.propertyCapitalValueFrom = message.from;
                    self.propertyCapitalValueTo = message.to;

                } else {
                    self.propertyCapitalValueFrom = null;
                    self.propertyCapitalValueTo = null;
                }
            });
            EventBus.$on("capitalValueGrossRate", function(message) {
                if(message) {
                    self.propertyCapitalValueGrossRateFrom = message.from;
                    self.propertyCapitalValueGrossRateTo = message.to;

                } else {
                    self.propertyCapitalValueGrossRateFrom = null;
                    self.propertyCapitalValueGrossRateTo = null;
                }
            });
            EventBus.$on("landValueGrossRate", function(message) {
                if(message) {
                    self.propertyLandValueGrossRateFrom = message.from;
                    self.propertyLandValueGrossRateTo = message.to;

                } else {
                    self.propertyLandValueGrossRateFrom = null;
                    self.propertyLandValueGrossRateTo = null;
                }
            });
            EventBus.$on("viGrossRate", function(message) {
                if(message) {
                    self.propertyVIGrossRateFrom = message.from;
                    self.propertyVIGrossRateTo = message.to;

                } else {
                    self.propertyVIGrossRateFrom = null;
                    self.propertyVIGrossRateTo = null;
                }
            });
            EventBus.$on("propertyLandArea", function(message) {
                if(message) {
                    self.propertyLandAreaFrom = message.from;
                    self.propertyLandAreaTo = message.to;

                } else {
                    self.propertyLandAreaFrom = null;
                    self.propertyLandAreaTo = null;
                }
            });
            EventBus.$on("propertyFloorArea", function(message) {
                if(message) {
                    self.propertyTotalFloorAreaFrom = message.from;
                    self.propertyTotalFloorAreaTo = message.to;

                } else {
                    self.propertyTotalFloorAreaFrom = null;
                    self.propertyTotalFloorAreaTo = null;
                }
            });
            EventBus.$on("propertyBedroomsCount", function(message) {
                if(message) {
                    self.propertyBedroomsFrom = message.from;
                    self.propertyBedroomsTo = message.to;

                } else {
                    self.propertyBedroomsFrom = null;
                    self.propertyBedroomsTo = null;
                }
            });
            EventBus.$on("propertyWallMaterialTypes", function(message) {
                if(message) {
                    self.propertyWallMaterials = message;

                } else {
                    self.propertyWallMaterials = null;
                }
            });
            EventBus.$on("propertyWallConditionTypes", function(message) {
                if(message) {
                    self.propertyWallConditions = message;

                } else {
                    self.propertyWallConditions = null;
                }
            });
            EventBus.$on("propertyRoofMaterialTypes", function(message) {
                if(message) {
                    self.propertyRoofMaterials = message;

                } else {
                    self.propertyRoofMaterials = null;
                }
            });
            EventBus.$on("propertyRoofConditionTypes", function(message) {
                if(message) {
                    self.propertyRoofConditions = message;

                } else {
                    self.propertyRoofConditions = null;
                }
            });
            EventBus.$on("propertyLandUseTypes", function(message) {
                if(message) {
                    self.propertyLandUses = message;

                } else {
                    self.propertyLandUses = null;
                }
            });
            EventBus.$on("production", function(message) {
                if(message) {
                    self.propertyProductionFrom = message.from;
                    self.propertyProductionTo = message.to;

                } else {
                    self.propertyProductionFrom = null;
                    self.propertyProductionTo = null;
                }
            });
            EventBus.$on("grossSalePrice", function(message) {
                if(message) {
                    self.grossSalePriceFrom = message.from;
                    self.grossSalePriceTo = message.to;

                } else {
                    self.grossSalePriceFrom = null;
                    self.grossSalePriceTo = null;
                }
            });
            EventBus.$on("netSalePrice", function(message) {
                if(message) {
                    self.netSalePriceFrom = message.from;
                    self.netSalePriceTo = message.to;
                } else {
                    self.netSalePriceFrom = null;
                    self.netSalePriceTo = null;
                }
            });
            EventBus.$on("netSalePricePerSquareMetre", function(message) {
                if(message) {
                    self.netSalePricePerSquareMetreFrom = message.from;
                    self.netSalePricePerSquareMetreTo = message.to;

                } else {
                    self.netSalePricePerSquareMetreFrom = null;
                    self.netSalePricePerSquareMetreTo = null;
                }
            });
            EventBus.$on("salesLandArea", function(message) {
                if(message) {
                    self.salesLandAreaFrom = message.from;
                    self.salesLandAreaTo = message.to;

                } else {
                    self.salesLandAreaFrom = null;
                    self.salesLandAreaTo = null;
                }
            });
            EventBus.$on("salesFloorArea", function(message) {
                if(message) {
                    self.salesFloorAreaFrom = message.from;
                    self.salesFloorAreaTo = message.to;

                } else {
                    self.salesFloorAreaFrom = null;
                    self.salesFloorAreaTo = null;
                }
            });
            EventBus.$on("salesBedroomCount", function(message) {
                if(message) {
                    self.salesBedroomCountFrom = message.from;
                    self.salesBedroomCountTo = message.to;

                } else {
                    self.salesBedroomCountFrom = null;
                    self.salesBedroomCountTo = null;
                }
            });
            EventBus.$on("salesBuildingAges", function(message) {
                if(message) {
                    self.salesBuildingAges = message;

                } else {
                    self.salesBuildingAges = null;
                }
            });
            EventBus.$on("propertyBuildingAges", function(message) {
                if(message) {
                    self.propertyBuildingAges = message;

                } else {
                    self.propertyBuildingAges = null;
                }
            });
            EventBus.$on("classificationCriteria", function(message) {
                if(message) {
                    self.saleTypeCodes = message.saleTypeCodes;
                    self.saleTenureCodes = message.saleTenureCodes;
                    self.priceValueRelationshipCodes = message.priceValueRelationshipCodes;
                } else {
                    self.saleTypeCodes = null;
                    self.saleTenureCodes = null;
                    self.priceValueRelationshipCodes = null;
                }
            });
            EventBus.$on("saleStatusCodes", function(message) {
                if(message) {
                    self.saleStatusCodes = message;
                } else {
                    self.saleStatusCodes = null;
                }
            });
            EventBus.$on("saleDate", function(message) {
                if(message) {
                    self.saleDateFrom = message.from;
                    self.saleDateTo = message.to;

                } else {
                    self.saleDateFrom = null;
                    self.saleDateTo = null;
                }
            });
            EventBus.$on("saleInputDate", function(message) {
                if(message) {
                    self.saleInputDateFrom = message.from;
                    self.saleInputDateTo = message.to;

                } else {
                    self.saleInputDateFrom = null;
                    self.saleInputDateTo = null;
                }
            });
            EventBus.$on("saleInputDate", function(message) {
                if(message) {
                    self.saleInputDateFrom = message.from;
                    self.saleInputDateTo = message.to;

                } else {
                    self.saleInputDateFrom = null;
                    self.saleInputDateTo = null;
                }
            });
        }
    }
</script>

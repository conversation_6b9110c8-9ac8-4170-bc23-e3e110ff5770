<template xmlns:v-on="http://www.w3.org/1999/xhtml">
	<div>
    <div class="advSearch-row advSearch-row-dependent advSearchForm disabled">
        <div class="advSearch-group advSearch-row-dependent tightMargin">
            <label><strong>Include</strong> These Categories</label>
            <span><input class="propertyIncludedCategories" type="text"></span>
            <span><input class="propertyIncludedCategories" type="text"></span>
            <span><input class="propertyIncludedCategories" type="text"></span>
            <span><input class="propertyIncludedCategories" type="text"></span>
        </div>
        <div class="advSearch-group advSearch-row-dependent tightMargin noMargin">
            <label><strong>Exclude</strong> These Categories</label>
            <span><input class="propertyExcludedCategories" type="text"></span>
            <span><input class="propertyExcludedCategories" type="text"></span>
            <span><input class="propertyExcludedCategories" type="text"></span>
            <span><input class="propertyExcludedCategories" type="text"></span>
        </div>
    </div>
    <div class="advSearch-row advSearch-row-dependent advSearchForm disabled">
        <range title="Capital Value" group="propertyCapitalValue" decimal="true"></range>
        <range title="Land Value" group="propertyLandValue" decimal="true"></range>
    </div>
    <div class="advSearch-row advSearch-row-dependent advSearchForm disabled">
        <range title="Capital Value Gross Rate" prop-type="area" group="capitalValueGrossRate" decimal="true"></range>
        <range title="Land Value Gross Rate" prop-type="area" group="landValueGrossRate" decimal="true"></range>
        <range title="VI Gross Rate" prop-type="area" group="viGrossRate" decimal="true"></range>
    </div>
    <div class="advSearch-row advSearch-row-dependent advSearchForm disabled">
        <range title="Land Area" group="propertyLandArea" decimal="false"></range>
        <range title="Total Floor Area" group="propertyFloorArea" decimal="true"></range>
        <range title="Bedrooms" group="propertyBedroomsCount" decimal="true"></range>
        <multi-select-filter category="BuildingAge" :style-attributes="buildingAgeStyle" filter-id="buildingAge" label="Age" group="propertyBuildingAges" property-name="buildingAge" data-cy="property-building-age"></multi-select-filter>
    </div>
    <div class="advSearch-row advSearch-row-dependent advSearchForm disabled">
        <multi-select-filter category="BuildingConstruction" :style-attributes="wallMaterialStyle" filter-id="wallMaterial" label="Wall Material" group="propertyWallMaterialTypes" property-name="buildconstructiontype"></multi-select-filter>
        <multi-select-filter category="BuildingCondition" :style-attributes="wallConditionStyle" filter-id="wallCondition" label="Wall Condition" group="propertyWallConditionTypes" property-name="buildconditiontype"></multi-select-filter>
        <multi-select-filter category="BuildingConstruction" :style-attributes="roofMaterialStyle" filter-id="roofMaterial" label="Roof Material" group="propertyRoofMaterialTypes" property-name="buildconstructiontype"></multi-select-filter>
		<multi-select-filter category="BuildingCondition" :style-attributes="roofConditionStyle" filter-id="roofCondition" label="Roof Condition" group="propertyRoofConditionTypes" property-name="buildconditiontype"></multi-select-filter>
    </div>
    <div class="advSearch-row advSearch-row-dependent advSearchForm disabled">
		<multi-select-filter category="LandUseType" show-code="true" :style-attributes="landUseStyle" filter-id="landUse" label="Land Use" group="propertyLandUseTypes" property-name="landusetype" include-code-in-label="true"></multi-select-filter>
		<range title="Production" group="production" decimal="true"></range>
    </div>
	</div>
</template>

<script>
    import Range from '../filters/Range.vue'
    import SingleSelect from '../filters/SingleSelect.vue'
	  import MultiSelectFilter from '../filters/MultiSelectAdvSearchFilter.vue'
    import {EventBus} from '../../EventBus.js'

    export default {
        components: {
            Range,
            SingleSelect,
			MultiSelectFilter
        },
        data: function() {
            return {
                landUseStyle:[],
                roofConditionStyle:[],
                roofMaterialStyle:[],
                wallConditionStyle:[],
                wallMaterialStyle:[],
                buildingAgeStyle:[]
            }
        },
        mounted: function() {
            var self = this;
            self.landUseStyle.push({key:'max-height', value:'300px'});
            self.landUseStyle.push({key:'margin-top', value:'-330px'});

            self.roofConditionStyle.push({key:'max-height', value:'300px'});
            self.roofConditionStyle.push({key:'margin-top', value:'-240px'});
            self.roofConditionStyle.push({key:'margin-left', value: '-100px'});

            self.roofMaterialStyle.push({key:'max-height', value:'300px'});
            self.roofMaterialStyle.push({key:'margin-top', value:'-330px'});

            self.wallConditionStyle.push({key:'max-height', value:'300px'});
            self.wallConditionStyle.push({key:'margin-top', value:'-240px'});

            self.wallMaterialStyle.push({key:'max-height', value:'300px'});
            self.wallMaterialStyle.push({key:'margin-top', value:'-330px'});

            self.buildingAgeStyle.push({key:'max-height', value:'300px'});
            self.buildingAgeStyle.push({key:'margin-top', value:'-200px'});
            self.buildingAgeStyle.push({key:'margin-left', value: '-100px'});

            EventBus.$on('check-data-from-child', function() {
                console.log("wall material style is: " + self.wallMaterialStyle);
            });

        }
    }
</script>

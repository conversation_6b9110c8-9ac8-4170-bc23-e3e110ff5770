<template>
<div>
<div class="resultsWrapper salesResults" v-if="salesResultCount > 0" id="salesResultsMainDiv">
    <div class="resultsInner-wrapper mdl-shadow--3dp" id="salesResultsInnerDiv" v-if="showTemplate">
        <div class="resultsTitle" id="resultsTitleDiv">
        <h1 class="lefty" >Sales Search Results</h1>
        <div class="switch righty">
            <input id="cmn-toggle-4" class="condensedView cmn-toggle cmn-toggle-round-flat" type="checkbox">
            <label for="cmn-toggle-4">
                <span>
                    <span id="normalViewSpan" class="toolTipper normalView" title="Normal View"></span>
                    <span id="compactViewSpan" class="toolTipper compactView" title="Compact View"></span>
                </span>
            </label>
            <i class="material-icons fatman">&#xE8F2;</i>
            <i class="material-icons littleboy">&#xE8EE;</i>
        </div>
        <ul class="toolbar righty">
            <li
                title="Export Results (limit 50,000)"
                class="exportResults mdl-button mdl-js-button mdl-button--icon"
                :class="{ 'disabled': exportResultsDisabled }"
                @click="exportResults()"
            ><i class="material-icons md-dark">&#xE06F;</i></li>
            <li title="Expand Results" class="expandAll mdl-button mdl-js-button mdl-button--icon"><i class="material-icons md-dark">&#xE8D7;</i></li>
        </ul>
    </div>

    <div class="resultsFound" id="resultsFoundDiv">
        <p>{{salesResultCount}} results found</p>
    </div>
    <div v-if="exportResultsDisabled" class="loadingSpinnerExportResults"></div>
    <div id="desktop" class="sortRow-wrapper">
        <div class="sortRow salesRow">
        <!-- ^^ THE ELEMENT ".sortrow" CONTROLS THE VIEW OF THE SEARCH RESULTS SORTING ROW -->
        <!-- ^^ THE ELEMENT ".sortrow" SHOULD INCLUDE ".salesRow" IF A SALES SEARCH HAS BEEN RUN -->

            <div id="addressDiv" data-value="value 1" data-sort="addressDefaultSort" class="colHeader address">
                <a href="#" id="addressHref">
                    <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Address<i class="material-icons md-dark">&#xE5C5;</i>
                </a>
            </div>
            <div id="valRefDiv" data-value="value 2" data-sort="valuationReference" class="colHeader valref">
                <a href="#" id="valRefHref">
                    <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Val Ref<i class="material-icons md-dark">&#xE5C5;</i>
                </a>
            </div>
            <div id="saleDateDiv" class="searchDetails-wrapper">
                <div id="qivsSaleIdDiv" data-value="" data-sort="QIVS_SALE_ID" class="colHeader qv-col-sale-id">
                   <a href="#" id="qivsSaleIdHref">
                        <span class="icon"><i class="sorter material-icons md-18">&#xE5DB;</i></span>Sale ID<i class="material-icons md-dark">&#xE5C5;</i>
                   </a>
                </div>
                <div data-value="" data-sort="SALE_DATE" class="colHeader saleDate active">
                   <a href="#" id="saleDateHref">
                        <span class="icon"><i class="sorter material-icons md-18 up">&#xE5DB;</i></span>Sale Date<i class="material-icons md-dark">&#xE5C5;</i>
                    </a>
                </div>
                <div id="netSalePriceDiv" data-value="" data-sort="NET_PRICE" class="colHeader nsp">
                    <a href="#" id="netSalePriceHref">
                        <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Net Sale Price<i class="material-icons md-dark">&#xE5C5;</i>
                    </a>
                </div>
                <div id="chattelsDiv" data-value="" data-sort="CHATTELS_PRICE" class="colHeader chattels">
                    <a href="#" id="chattelsHref">
                        <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Chattels<i class="material-icons md-dark">&#xE5C5;</i>
                    </a>
                </div>
                <div id="saleStatusDiv" data-value="" data-sort="SALE_STATUS" class="colHeader saleStatus">
                    <a href="#" id="saleStatusHref">
                        <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Sale Status<i class="material-icons md-dark">&#xE5C5;</i>
                    </a>
                </div>
                <div data-value="value 3" data-sort="capitalValue" class="colHeader capval">
                    <a href="#">
                        <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Capital Value<i class="material-icons md-dark">&#xE5C5;</i>
                    </a>
                </div>
                <div data-value="value 4" data-sort="landValue" class="colHeader landVal">
                    <a href="#">
                        <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Land Value<i class="material-icons md-dark">&#xE5C5;</i>
                    </a>
                </div>
                <div data-value="value 5" data-sort="valimp" class="colHeader valimp">
                    <a href="#">
                        <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Value Imps.<i class="material-icons md-dark">&#xE5C5;</i>
                    </a>
                </div>
                <div id="landAreaHaDiv" data-value="value 6" data-sort="LAND_AREA" class="colHeader landarea">
                    <a id="landAreaHaHref" href="#">
                        <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Land Area (Ha)<i class="material-icons md-dark">&#xE5C5;</i>
                    </a>
                </div>
                <div class="tfaTla-wrapper">
                    <div id="tfaDiv" data-value="value 7" data-sort="TOTAL_FLOOR_AREA" class="colHeader tfa">
                        <a  id="tfaHref" href="#">
                            <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>TFA<i class="material-icons md-dark">&#xE5C5;</i>
                        </a>
                    </div>
                    <div id="tlaDiv" data-value="value 8" data-sort="TOTAL_LIVING_AREA" class="colHeader tla">
                        <a id="tlaHref" href="#">
                            <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>TLA<i class="material-icons md-dark">&#xE5C5;</i>
                        </a>
                    </div>
                </div>    
            </div>
            <div id="categoryDiv" data-value="value 9" data-sort="CATEGORY" class="colHeader category">
                <a id="categoryHref" href="#">
                    <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Category<i class="material-icons md-dark">&#xE5C5;</i>
                </a>
            </div>
        </div>
    </div>

    <div class="resultsRow salesRow" v-for="sale in searchResults" v-bind:id="'results_'+sale.qupid" v-bind:class="{noThumbnails: sale.propertyPhotos ? false : true,maoriLand: sale.isMaoriLand==='Yes', revalOn: (((isInternalUser == false && isReadOnly == false) || isInternalUser) && sale.hasRevision), unconfirmedSale: sale.status==='Unconfirmed', pendingSale: sale.status==='Pending'}">
    <!-- ^^ THE ELEMENT ".resultsRow" CONTROLS THE VIEW OF THE OPEN PROPERTY CARD -->
    <!-- ^^ THE ELEMENT ".resultsRow" CAN INCLUDE ".salesRow" , ".maoriLand" , ".revalOn" and/or ".noThumbnails" BASED ON THE PROPERTY'S STATE -->
    <!-- ^^ THE ELEMENT ".resultsRow" CAN INCLUDE ".theSkinny" IF THE USER HAS SELECTED COMPACT VIEW -->

        <div class="colCell address">
            <span class="primaryThumb-Wrapper" v-bind:id="'primaryPhoto_'+sale.qupid"><img class="primaryPhoto_thumb" v-bind:src=sale.primaryPhoto></span>
            <div id="'fullAddressDiv_'+sale.qupid" class="fullAddress" @click="loadMasterDetails(sale.qupid)"><!--@click="showMasterDetails(sale.qupid)"-->
                <span id="address1Span" v-html="sale.address1"></span>
                <span id="address2Span" v-html="sale.address2"></span>
                <div class="md-link">
                    <div class="colCell qpid" v-html="sale.qupid"></div>
                    <div class="colCell valref" v-html="sale.valRef"></div>
                    <div class="colCell legaldesc" v-html="sale.legalDescription"></div>
                    <ul class="occupier">
                        <li></li>
                        <li></li>
                    </ul>
                    <ul class="lastReval-date">
                        <li v-html="sale.currentRevisionDate"></li>
                    </ul>
                </div>
            </div>
            <div class="sales-trafficLights" v-bind:data-id=sale.id v-bind:saleAnalysisType=sale.saleAnalysis>
                <div class="colCell qv-col-sale-id">{{ sale.qivsSaleId }}</div>
                <div class="colCell saleClassification" v-bind:class="{classOne: sale.priceValueRelationship=='1', classTwo: sale.priceValueRelationship=='2', classThree: sale.priceValueRelationship=='3'}" v-html="sale.classification"></div>
                <div id="saleStatusDiv" class="colCell saleStatus" v-html="sale.status" v-bind:class="[sale.status=='Pending' ? 'statusPending' : (sale.status=='Unconfirmed' ? 'statusUnconfirmed' : '')]"></div>	<!-- THIS ELEMENT CAN INCLUDE ".statusPending" or ".statusUnconfirmed" BASED ON THE SALE STATE -->
                <div id="saleDateDiv" class="colCell saleDate" v-html="sale.saleDate" v-bind:class="[sale.status=='Pending' ? 'statusPending' : (sale.status=='Unconfirmed' ? 'statusUnconfirmed' : '')]"></div> 	<!-- THIS ELEMENT CAN INCLUDE ".statusPending" or ".statusUnconfirmed" BASED ON THE SALE STATE -->
                <div class="colCell saleAnalysis" v-html="sale.saleAnalysis" v-if="isInternalUser"></div>		<!-- THE VALUE WILL CHANGE TO 'No' IF A SALES ANALYSIS FOR THIS PROPERTY IS NOT AVAILABLE IN MONARCH -->
                <ul class="vendPurchase">
                    <li v-html="sale.parties"></li>
                </ul>
            </div>
        </div>
        <div class="colCell valref" v-html="sale.valRef"></div>
        <div class="searchDetails-wrapper">
            <div class="colCell qv-col-sale-id" >{{ sale.qivsSaleId }}</div>
            <div class="colCell saleDate" v-html="sale.saleDate"></div>
            <div id="salesNetRateDiv" class="colCell nsp">{{sale.netPrice}}<div class="nsptfa">{{sale.saleNetRate}}<span>/ m<sup>2</sup></span></div></div>
            <div class="colCell nsptocv" v-html="sale.nspCV"></div>
            <div class="colCell capval">{{sale.capitalValue}}<div class="cvnr">{{sale.cvNetRate}}<span>/ m<sup>2</sup></span></div></div>
            <div class="colCell landval">{{ sale.landValue }}<div class="lvnr" v-html="sale.lvRatioHtml"></div></div>
            <div class="colCell valimp">{{sale.valueOfImprovements}}<div class="vinr">{{sale.viNetRate}}<span>/ m<sup>2</sup></span></div></div>
            <div id ="chattelsDiv" class="colCell chattels" v-html="sale.chattels"></div>
            <div id="saleStatusDiv" class="colCell saleStatus" v-html="sale.status"></div>
            <div id="landAreaHaDiv" class="colCell landarea" v-html="sale.landArea" data-cy="land-area"></div>
            <div id="tfaAndTlaDiv" class="tfaTla-wrapper">
            	<span id="tfaSpan" class="colCell tfa">{{sale.TFA}}<span>m<sup>2</sup></span></span>
            	<span id="tlaSpan" class="colCell tla">{{sale.TLA}}<span>m<sup>2</sup></span></span>
            </div>
            <div class="colCell salegst" v-html="sale.gst"></div>
            <div class="colCell saleother" v-html="sale.other"></div>
        </div>
        <div id="categoryDiv" class="colCell category" v-html="sale.category"></div>
        <div class="revalRates" v-if="((isInternalUser == false && isReadOnly == false) || isInternalUser) && sale.hasRevision">
            <div class="reval-capval">{{sale.revisionCapVal}}<span v-bind:class="[sale.capitalValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{sale.capitalValueDiff}}%</span><div class="cvnr">{{sale.revalCapValNetRate}}<span>/ m<sup>2</sup></span></div></div> 	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
            <div class="reval-landval">{{sale.revisionLandVal}}<span v-bind:class="[sale.landValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{sale.landValueDiff}}%</span><div class="lvnr" v-html="sale.revalLvRatioHtml" /></div>	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
            <div class="reval-valimp">{{sale.revalValueOfImprovements}}<span v-bind:class="[sale.valueOfImprovementsDiff >= 0 ? 'valueUp' : 'valueDown']">{{sale.valueOfImprovementsDiff}}%</span><div class="vinr">{{sale.revalValueOfImprovementsNetRate}}<span>/ m<sup>2</sup></span></div></div>	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
            <div class="reval-nsptorcv">{{sale.revalNspToRCV}}</div>
        </div>
        <div class="photoGallery_propSearch" v-bind:id="'photos_'+sale.qupid">
            <span class="thumbWrapper" v-for="photo in sale.propertyPhotos"><img class="photoGallery_thumb" v-bind:data-id=photo.id v-bind:data-qupid=photo.qupid v-bind:data-property=photo.propertyId v-bind:src=photo.link></span>
        </div>
        <div class="extras">
            <ul class="md-landMas searchList">
                <li class="md-masIcon-category"><strong v-html="sale.category" data-cy="category"></strong>Category</li>
                <li class="md-masIcon-eyb"><strong v-html="sale.effectiveYearBuilt" data-cy="effective-year-built"></strong>Effective Year Built</li>
                <li class="md-masIcon-landUse"><strong v-html="sale.landUse"></strong>Land Use</li>
                <li class="md-masIcon-units"><strong v-html="sale.units"></strong>Units</li>
                <li class="md-masIcon-bedrooms"><strong v-html="sale.bedrooms" data-cy="bedrooms"></strong>Bedrooms</li>
                <li class="md-masIcon-toilets"><strong v-html="sale.toilets"></strong>Toilets</li>
                <li class="md-masIcon-walls"><strong v-html="sale.wallConstructionAndCondition"></strong>Wall Construction and Condition</li>
                <li class="md-masIcon-roof"><strong v-html="sale.roofConstructionAndCondition"></strong>Roof Construction and Condition</li>
                <li class="md-masIcon-umrg"><strong v-html="sale.underMainRoofGarages"></strong>Under Main Roof Garaging</li>
                <li class="md-masIcon-fsg"><strong v-html="sale.freeStandingGarages"></strong>Free Standing Garaging</li>
                <li class="md-masIcon-oli"><strong v-html="sale.otherLargeImprovements"></strong>Other Large Improvements</li>
                <li class="md-masIcon-modernisation"><strong v-html="sale.modernisation"></strong>Modernisation</li>
                <li class="md-masIcon-zone"><strong v-html="sale.zone"></strong>Zone</li>
                <li class="md-masIcon-lotPosition"><strong v-html="sale.lotPosition"></strong>Lot Position</li>
                <li class="md-masIcon-contour"><strong v-html="sale.contour"></strong>Contour</li>
                <li class="md-masIcon-viewScope"><strong v-html="sale.viewScope"></strong>View and Scope</li>
                <li class="md-masIcon-production"><strong v-html="sale.production"></strong>Production</li>
                <li class="md-masIcon-maoriLand"><strong v-html="sale.isMaoriLand"></strong>Maori Land</li>
            </ul>
        </div>
        <ul class="toolbar listingControls righty ">
            <li class="md-sales" @click="salesLinkHandler(sale.qupid, sale.qivsSaleId)"><label>SALE</label><i class="material-icons">call_made</i></li>	<!-- THIS ITEM LINKS TO THIS SALE IN QIVS -->
            <li class="md-qivs" @click="externalQIVSLinkHandler(sale.qupid)"><label>QIVS</label><i class="material-icons">call_made</i></li>		<!-- THIS ITEM LINKS TO THIS PROPERTY RECORD IN QIVS -->
            <li class="md-qvms" v-if="isQVMapUser" @click="openQVMap(sale.qupid, sale.latitude, sale.longitude)"><label>MAP</label><i class="material-icons">call_made</i></li>		<!-- THIS ITEM LINKS TO THIS PROPERTY RECORD IN THE MAPPING APPLICATION -->
            <li class="md-qvms"
                @click="openUrlInNewTab(`https://google.co.nz/search?near=New+Zealand&q=${sale.address1}+${sale.address2}`)">
                <label>WEB</label> <i class="material-icons icon--flipped">search</i>
            </li>
            <li class="mdl-button mdl-js-button mdl-button--icon" v-bind:class="{disabled: isReadOnly == true}" title="Upload Photos"><a class="photo-uploader" href="javascript:void(0)" v-bind:data-property="sale.propertyId" v-bind:data-qupid="sale.qupid"><i class="material-icons md-dark">&#xE251;</i></a></li>
            <li class="closer mdl-button mdl-js-button mdl-button--icon" title="Close"><i class="material-icons md-dark">&#xE5CD;</i></li>
        </ul>
    </div>

    </div>
    <div class="loadingSpinner loadingSpinnerSearchResults"></div>
    <warning :header="warningHeader" :message="warningMessage" close="Go Back"></warning>
    <alert-modal
        v-if="modal.isOpen"
        :success="modal.mode==='success'"
        :caution="modal.mode==='caution'"
        :warning="modal.mode==='warning'"
    >
        <h1>{{ modal.heading }}</h1>
        <p v-if="modal.message !== ''" style="white-space:pre-wrap;">{{ modal.message }}</p>
        <template #buttons>
            <div class="alertButtons">
                <button
                    v-if="modal.cancelText"
                    id="errorCancel"
                    class="mdl-button mdl-button--mini lefty"
                    @click="modalCancel"
                >
                    {{ modal.cancelText }}
                </button>
                <button
                    v-if="modal.confirmText"
                    id="continue"
                    class="mdl-button mdl-button--mini"
                    @click="modalConfirm"
                >
                    {{ modal.confirmText }}
                </button>
            </div>
        </template>
    </alert-modal>
</div>
<div class="noResults-wrapper mdl-shadow--3dp" v-if="noResultsFound">
    <h2>Sorry, no results were found</h2>
    <p>Search suggestions:</p>
    <ul>
        <li>Check your spelling.</li>
        <li>Confirm the address or information that you were searching.</li>
        <li>Try searching by different criteria.</li>
    </ul>
</div>
</div>
</template>

<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import Warning from '../common/Warning.vue';
    import * as $ from "jquery";
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import { submitMonarchExport } from '../reports/utils.js'
    import moment from 'moment';
    import { openMap, openUrlInNewTab } from '../../utils/QivsUtils';
    import AlertModal from '../common/modal/AlertModal.vue';
    import { useSaleAnalysis } from '@/composables/useSaleAnalysis';

    export default {
        components: {
            Warning,
            AlertModal
        },
        mixins: [formatUtils, commonUtils],
        setup() {
            const { tryOpenAnalysisById } = useSaleAnalysis();

            return {
                tryOpenAnalysisById
            };
        },
        data: function() {

            return {
                searchResults: [],
                salesResultCount: 0,
                searchParams:{},
                sortField:'',
                sortOrder:'',
                warningHeader: '',
                warningMessage: '',
                showTemplate:false,
                noResultsFound: false,
                selectedSaleQpid: null,
                exportResultsDisabled: false,
                modal: {
                    mode: 'warning',
                    isOpen: false,
                    heading: 'No Rural Worksheet',
                    message: '',
                    cancelText: 'OK',
                    cancelAction: () => { this.modal.isOpen = false; },
                    confirmText: 'Create Rural Worksheet',
                    confirmAction: () => {
                        this.modal.isOpen = false;
                        this.goToRuralWorksheet(this.selectedSaleQpid);
                    },
                },
            }

        },
        computed: {
            ...mapState('userData', [
                'isInternalUser',
                'isReadOnlyUser',
                'qivsUrl',
                'isQVMapUser',
            ]),
            isReadOnly: function() {
                return self.isReadOnlyUser;
            }
        },
        methods: {
            loadMasterDetails: function(propertyId) {
                var event = {}
                event.searchType = 'master-details';
                event.propertyId = propertyId
                EventBus.$emit('display-content', event)
            },
            goToRuralWorksheet(qpid) {
                this.$router.push({ name: 'rural-worksheet',  params: { id: qpid }});
            },
            async exportResults() {
                this.exportResultsDisabled = true;

                const submitResult = await submitMonarchExport(
                    'MONARCH_SALE_EXPORT',
                    this.searchParams,
                    this.salesResultCount
                );

                if (submitResult) {
                    if (submitResult.cancelText === 'View My Reports') {
                        submitResult.cancelAction = () => { this.$router.push({ name: 'report-dashboard-my-reports' }); }
                    }
                    this.setModal(submitResult);
                }

                this.exportResultsDisabled = false;
            },
            setModal(modal) {
                this.modal = modal;
            },
            modalCancel() {
                this.modal.isOpen = false;
                this.modal.cancelAction();
            },
            modalConfirm() {
                this.modal.isOpen = false;
                this.modal.confirmAction();
            },
            generateSalesSearchDataForRender: function (searchResults, resultsCount) {
              this.salesResultCount = resultsCount;
              if(this.salesResultCount <= 0){
                  this.noResultsFound = true;
              }
              else {
                  this.noResultsFound = false
              }

              var salesResultsToRender = [];
              var self = this;
              for (var i = 0; i <  searchResults.length; i++) {
                var saleObjToRender = {}
                var currSale = searchResults[i]
                saleObjToRender.id = currSale.id
                saleObjToRender.saleDate = self.getFormattedDate(currSale.saleDate)
                saleObjToRender.qivsSaleId = currSale.qivsSaleId
                saleObjToRender.parties = currSale.parties
                saleObjToRender.grossPrice = currSale.gross ? self.formatPrice(currSale.gross, '$0,0') : '$0';
                saleObjToRender.netPrice = currSale.net ? self.formatPrice(currSale.net, '$0,0') : '$0';
                saleObjToRender.chattels = currSale.chattels ? self.formatPrice(currSale.chattels, '$0,0') : '$0';
                saleObjToRender.other = currSale.other ? self.formatPrice(currSale.other, '$0,0') : '$0';
                saleObjToRender.gst = currSale.gst ? self.formatPrice(currSale.gst, '$0,0') : '$0';
                saleObjToRender.currentRevisionDate = self.getFormattedDate(currSale.revisionDate)
                saleObjToRender.classification = currSale.saleType ? currSale.saleType.code : ''
                saleObjToRender.classification += currSale.saleTenure ? currSale.saleTenure.code : ''
                saleObjToRender.classification += currSale.priceValueRelationship ? currSale.priceValueRelationship.code : ''
                saleObjToRender.priceValueRelationship = currSale.priceValueRelationship ? currSale .priceValueRelationship.code : ''
                saleObjToRender.status = currSale.status ? currSale.status.description : ''
                saleObjToRender.qupid = currSale.primaryProperty ? currSale.primaryProperty.qupid : ''
                saleObjToRender.propertyId = currSale.propertyId ? currSale.propertyId : ''
                var propertyPhotos = self.getPropertyPhotos(currSale.mediaData, saleObjToRender.qupid)
                saleObjToRender.primaryPhoto = propertyPhotos.primary
                saleObjToRender.propertyPhotos = propertyPhotos.list
                saleObjToRender.category = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.category ? currSale.primaryProperty.category.code : '') : '')
                saleObjToRender.saleAnalysis = self.getSaleAnalysisFromPropertyData(currSale.hasSaleAnalysis, saleObjToRender.category.charAt(0))
                saleObjToRender.hasSaleAnalysis = currSale.hasSaleAnalysis
                saleObjToRender.canBeAnalysed = currSale.canBeAnalysed
                saleObjToRender.valRef = currSale.primaryProperty ? currSale.primaryProperty.rollNumber : ''
                saleObjToRender.valRef += currSale.primaryProperty ? '/'+currSale.primaryProperty.assessmentNumber : ''
                saleObjToRender.valRef += currSale.primaryProperty ? (currSale.primaryProperty.suffix ? ' ' + currSale.primaryProperty.suffix : '') : ''
                if (saleObjToRender.valRef.indexOf('undefined') !== -1) {
                    saleObjToRender.valRef = ''
                }
                saleObjToRender.legalDescription = currSale.primaryProperty ? currSale.primaryProperty.legalDescription : ''
                var streetNumberSuffix = currSale.primaryProperty ? (currSale.primaryProperty.address ? (currSale.primaryProperty.address.streetNumberSuffix ? ' '+currSale.primaryProperty.address.streetNumberSuffix : '') : '') : ''
                saleObjToRender.address1 = currSale.primaryProperty ? (currSale.primaryProperty.address ? ((currSale.primaryProperty.address.streetNumber ? currSale.primaryProperty.address.streetNumber : '')+streetNumberSuffix+ ' ' + (currSale.primaryProperty.address.streetName ? currSale.primaryProperty.address.streetName : '')  + (currSale.primaryProperty.address.streetType ? ' '+currSale.primaryProperty.address.streetType.description + ',' : '')) : '') : ''
                if (saleObjToRender.address1.indexOf('undefined') !== -1) {
                    saleObjToRender.address1 = ''
                }
                saleObjToRender.address2 = currSale.primaryProperty ? (currSale.primaryProperty.address ? (currSale.primaryProperty.address.suburb ? (currSale.primaryProperty.address.suburb+', ') : '') : '') : ''
                saleObjToRender.address2 += currSale.primaryProperty ? (currSale.primaryProperty.address.town ? currSale.primaryProperty.address.town + ', ' : '' ) : ''
                saleObjToRender.address2 += currSale.primaryProperty ? (currSale.primaryProperty.territorialAuthority ? currSale.primaryProperty.territorialAuthority.name : '') : ''
                if (saleObjToRender.address2.indexOf('undefined') !== -1) {
                    saleObjToRender.address2 = ''
                }
                saleObjToRender.isMaoriLand = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.isMaoriLand ? 'Yes' : 'No') : 'No') : 'No'
                saleObjToRender.landValue = currSale.landValue ? self.formatPrice(currSale.landValue, '$0,0') : '$0';
                saleObjToRender.capitalValue = currSale.capitalValue ? self.formatPrice(currSale.capitalValue, '$0,0') : '$0';
                saleObjToRender.landArea = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.landArea : '') : ''
                if (typeof saleObjToRender.landArea == 'number') {
                    saleObjToRender.landArea = saleObjToRender.landArea.toFixed(4)
                }
                saleObjToRender.TLA = currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.totalLivingArea ? currSale.primaryProperty.massAppraisalData.totalLivingArea : '0') : '0') : '0'
                saleObjToRender.TFA = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.totalFloorArea ? currSale.primaryProperty.landUseData.totalFloorArea : '0') : '0') : '0'
                saleObjToRender.effectiveYearBuilt = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.effectiveYearBuilt : '') : '')
                saleObjToRender.landUse = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.landUse ? currSale.primaryProperty.landUseData.landUse.description : '') : '') : '')
                saleObjToRender.units = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.units : '') : '')
                saleObjToRender.bedrooms = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.bedrooms : '') : '')
                saleObjToRender.toilets = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.toilets : '') : '')
                saleObjToRender.wallConstructionAndCondition = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.wallConstruction ? currSale.primaryProperty.landUseData.wallConstruction.description : '') : '') : ''
                saleObjToRender.wallConstructionAndCondition += currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.wallCondition ? ' '+currSale.primaryProperty.landUseData.wallCondition.description : '') : '') : ''
                saleObjToRender.wallConstructionAndCondition = self.getRenderableValue(saleObjToRender.wallConstructionAndCondition)
                saleObjToRender.roofConstructionAndCondition = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.roofConstruction ? currSale.primaryProperty.landUseData.roofConstruction.description : '') : '') : ''
                saleObjToRender.roofConstructionAndCondition += currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.roofCondition ? ' '+currSale.primaryProperty.landUseData.roofCondition.description : '') : '') : ''
                saleObjToRender.roofConstructionAndCondition = self.getRenderableValue(saleObjToRender.roofConstructionAndCondition)
                saleObjToRender.underMainRoofGarages = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.underMainRoofGarages : '') : '')
                saleObjToRender.freeStandingGarages = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.freestandingGarages : '') : '')
                saleObjToRender.otherLargeImprovements = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.hasLargeOtherImprovements ? 'Yes' : 'No') : 'No') : 'No')
                saleObjToRender.modernisation = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.isModernised ? 'Yes' : 'No') : 'No') : 'No')
                saleObjToRender.zone = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.landZone : '') : '')
                saleObjToRender.lotPosition = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.lotPosition ? currSale.primaryProperty.massAppraisalData.classifications.lotPosition.description : '') : '') : '') : '')
                saleObjToRender.contour = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.contour ? currSale.primaryProperty.massAppraisalData.classifications.contour.description : '') : '') : '') : '')
                var viewDescription = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.view ? (currSale.primaryProperty.massAppraisalData.classifications.view.description+' ') : '') : '') : '') : '')
                var viewCode = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.view ? (currSale.primaryProperty.massAppraisalData.classifications.view.code+' ') : '') : '') : '') : '')
                if(viewCode.trim() == 'N'){
                    saleObjToRender.viewScope = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.viewScope ? currSale.primaryProperty.massAppraisalData.classifications.viewScope.description : '') : '') : '') : '')
                }else{
                    saleObjToRender.viewScope = ((viewDescription && viewDescription != '-') ? viewDescription.substring(viewDescription.trim().lastIndexOf(" ")+1) : '') + self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.viewScope ? currSale.primaryProperty.massAppraisalData.classifications.viewScope.description : '') : '') : '') : '')
                }

                saleObjToRender.production = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.production : '-') : '-')

                //Derived Values
                var valueOfImprovements = 0
                var landValue = currSale.landValue
                var capitalValue = currSale.capitalValue
                var totalFloorArea = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.totalFloorArea : 0) : 0
                var landArea = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.landArea : 0) : 0
                var netPrice = currSale.net

                if (landValue && landValue > 0 && capitalValue && capitalValue > 0) {
                    valueOfImprovements = Math.round(capitalValue - landValue)
                }
                saleObjToRender.valueOfImprovements = self.formatPrice(valueOfImprovements, '$0,0');

                var cvNetRate = 0
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0) {
                    cvNetRate = Math.round(capitalValue/totalFloorArea)
                }
                saleObjToRender.cvNetRate = self.formatPrice(cvNetRate, '$0,0');

                var lvNetRate = 0
                if (landArea > 0 && landValue && landValue > 0) {
                    lvNetRate = Math.round(landValue/(landArea*10000))
                }
                saleObjToRender.lvNetRate = self.formatPrice(lvNetRate, '$0,0');

                var viNetRate = 0
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0 && landValue && landValue > 0) {
                    viNetRate = Math.round((capitalValue - landValue)/totalFloorArea)
                }
                saleObjToRender.viNetRate = self.formatPrice(viNetRate, '$0,0');

                var nspCV = 0
                if (capitalValue && capitalValue > 0) {
                    nspCV = (Math.round((netPrice/capitalValue)*100))/100
                }
                saleObjToRender.nspCV = self.formatDecimal(nspCV, 2);

                var saleNetRate = 0
                if (netPrice > 0 && totalFloorArea > 0) {
                    saleNetRate = Math.round(netPrice/totalFloorArea)
                }
                saleObjToRender.saleNetRate = self.formatPrice(saleNetRate, '$0,0');

                saleObjToRender.revisionCapVal = currSale.revisionCapitalValue ? self.formatPrice(currSale.revisionCapitalValue, '$0,0') : '$0';
                saleObjToRender.revisionLandVal = currSale.revisionLandValue ? self.formatPrice(currSale.revisionLandValue, '$0,0') : '$0';
                saleObjToRender.hasRevision = currSale.revisionCapitalValue || currSale.revisionLandValue

                var revalCapVal = currSale.revisionCapitalValue ? currSale.revisionCapitalValue : 0
                var revalLandVal = currSale.revisionLandValue ? currSale.revisionLandValue : 0
                var revalValueOfImprovements = 0
                if (revalCapVal > 0 && revalLandVal > 0) {
                    revalValueOfImprovements = Math.round(revalCapVal - revalLandVal)
                }
                saleObjToRender.revalValueOfImprovements = self.formatPrice(revalValueOfImprovements, '$0,0');

                var revalCapValNetRate = 0
                if (revalCapVal > 0 && totalFloorArea > 0) {
                    revalCapValNetRate = Math.round(revalCapVal/totalFloorArea)
                }
                saleObjToRender.revalCapValNetRate = self.formatPrice(revalCapValNetRate, '$0,0');

                  var revalLandValNetRate = 0
                if (revalLandVal > 0 && landArea > 0) {
                    revalLandValNetRate = Math.round(revalLandVal/(landArea*10000))
                }
                saleObjToRender.revalLandValNetRate = self.formatPrice(revalLandValNetRate, '$0,0');

                var revalValueOfImprovementsNetRate = 0
                if (totalFloorArea > 0) {
                    revalValueOfImprovementsNetRate = Math.round((revalCapVal - revalLandVal)/totalFloorArea)
                }
                saleObjToRender.revalValueOfImprovementsNetRate = self.formatPrice(revalValueOfImprovementsNetRate, '$0,0');

                var revalNspToRCV = 0
                if (revalCapVal > 0) {
                    revalNspToRCV = (Math.round((netPrice/revalCapVal)*100))/100
                }
                saleObjToRender.revalNspToRCV = self.formatDecimal(revalNspToRCV, 2);

                var capitalValueDiff = 0
                if (capitalValue && capitalValue > 0 && revalCapVal > 0) {
                    capitalValueDiff = ((revalCapVal * 100)/capitalValue) - 100
                }
                saleObjToRender.capitalValueDiff = (Math.round(capitalValueDiff*10))/10

                var landValueDiff = 0
                if (landValue && landValue > 0 && revalLandVal > 0) {
                    landValueDiff = ((revalLandVal * 100)/landValue) - 100
                }
                saleObjToRender.landValueDiff = (Math.round(landValueDiff*10))/10

                var valueOfImprovementsDiff = 0
                if (valueOfImprovements > 0) {
                    valueOfImprovementsDiff = (((revalCapVal - revalLandVal)*100)/valueOfImprovements)-100
                }
                saleObjToRender.valueOfImprovementsDiff = (Math.round(valueOfImprovementsDiff*10))/10

                saleObjToRender.latitude = currSale.latitude;
                saleObjToRender.longitude = currSale.longitude;
                saleObjToRender.lvRatioHtml = self.lvRatioHtml(landArea, currSale.landValue);
                saleObjToRender.revalLvRatioHtml = self.lvRatioHtml(landArea, currSale.revisionLandValue);

                salesResultsToRender.push(saleObjToRender)
              }
              //this.searchResults = salesResultsToRender;
              return salesResultsToRender
            },

            async getPropertyInfo(qpid) {
                const m = jsRoutes.controllers.PropertyMasterData.getPropertyInfo(qpid);
                try {
                    const response = await fetch(m.url, {
                        method: m.method,
                        cache: 'no-cache',
                    });

                    if (response.status >= 300) {
                        console.error(`Error fetching property info for qpid ${qpid}: `, response);
                        throw new Exception(`Error fetching property info for qpid ${qpid}`, response);
                    }

                    const responseBody = await response.json();
                    return responseBody;
                }
                catch(err) {
                    console.error(`Error fetching property info for qpid ${qpid}: `, err);
                    throw new Exception(`Error fetching property info for qpid ${qpid}`, err);
                }
            },

            getPropertyPhotos: function(photos, qupid) {
                var propertyPhotos = [];
                var primaryPhoto = 'assets/images/property/addPhotos.png';
                $.each(photos[0], function(i, obj) {
                    if(obj.isPrimary) {
                        primaryPhoto = obj.mediaItem.smallImageUrl;
                    }
                    propertyPhotos.push({'id':obj.id, 'propertyId':obj.ownerId, 'qupid':qupid, 'link':obj.mediaItem.mediumImageUrl});
                });
                var result = {primary: primaryPhoto, list: propertyPhotos};
                return result;
            },

            getSaleAnalysisFromPropertyData: function(hasSaleAnalysis, categoryType) {
                var saleAnalysis = ''
                const self = this
                if (self.isInternalUser && (['R','L','A','D','F','H','P','S'].includes(categoryType))) {
                    if (hasSaleAnalysis) {
                        saleAnalysis = 'Yes'
                    }
                    else {
                        saleAnalysis = 'New'
                    }
                }
                else {
                    saleAnalysis = 'N/A'
                }
                return saleAnalysis
            },
            getFormattedDate: function(date) {
                if (date && date != '') {
                    return moment(String(date)).format('DD/MM/YYYY')
                }
                return ''
            },
            getRenderableValue: function(val) {
                return (val && val != '') ? val : '-'
            },
            openQVMap(qpid,latitude,longitude) {
                openMap(qpid,latitude,longitude);
            },
            async salesLinkHandler(qupid, saleId) {
                const url = await this.$router.resolve({
                    name: 'property-sale',
                    params: {
                        id: saleId,
                        qpid: qupid,
                    },
                });
                window.open(url.href, '_blank');
            },
            externalQIVSLinkHandler: function(qupid) {
                var url = this.qivsUrl;
                if (url && url != '') {
                    var ua = window.navigator.userAgent;
                    var old_ie = ua.indexOf('MSIE ');
                    var new_ie = ua.indexOf('Trident/');

                    var isIE = ((old_ie > -1) || (new_ie > -1));

                    if(qupid) {
                        url = url + "/default.asp?Property/masterdetails.aspx?Qpid=" + qupid;
                    }
                    if(isIE) {
                        var w = window.open(url, "QIVZ");
                        w.close();
                    }
                    window.open(url, "QIVZ");
                }
            },
            showMasterDetails: function(qupid) {
                window.location = window.location.protocol+'//'+window.location.hostname+'/property/property/Search?qupid=' + qupid;
            },
            getSortFields: function(sortFieldName) {
                var sortFields = []
                if (sortFieldName == 'addressDefaultSort') {
                    sortFields=['TA_CODE', 'STREET_NAME', 'STREET_TYPE', 'STREET_NUMBER', 'STREET_NUMBER_SUFFIX']
                }
                else if (sortFieldName == 'valuationReference') {
                    sortFields = ['ROLL_NUMBER', 'ASSESSMENT_NUMBER', 'ASSESSMENT_NUMBER_SUFFIX']
                }
                else {
                    sortFields.push(sortFieldName)
                }
                return sortFields
            },
            expandAll: function() {
                $('.resultsRow').each(function() {
                    if($(this).hasClass('openProp mdl-shadow--2dp') == false) {
                        $(this).toggleClass("openProp mdl-shadow--2dp");
                    }
                })
            },
            findSalesByQupid: function(qupid){
                var self = this
                var salesObjs = []
                $.each(self.searchResults, function(i, obj) {
                    if (qupid == ''+obj.qupid) {
                        salesObjs.push(obj)
                    }
                });
                return salesObjs
            },
            findSalesByUuid: function(uuid){
                var self = this
                var salesObj;
                $.each(self.searchResults, function(i, obj) {
                    if (uuid == ''+obj.id) {
                        salesObj = obj
                    }
                });
                return salesObj
            },
            refreshResultRow: function(propertyId, qupid) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.MediaController.getMediaByOwner(propertyId).url,
                    cache: false,
                    success: function (photoData) {
                        var salesObjs = self.findSalesByQupid(qupid)
                        var propertyPhotos = [];
                        var primaryPhoto = 'assets/images/property/addPhotos.png';
                        $.each(photoData, function(i, obj) {
                            if(obj.isPrimary) {
                                primaryPhoto = obj.mediaItem.smallImageUrl;
                            }
                            propertyPhotos.push({'id':obj.id, 'propertyId':obj.ownerId, 'qupid':qupid, 'link':obj.mediaItem.mediumImageUrl});
                        });
                        if (salesObjs && !$.isEmptyObject(salesObjs)) {
                            $.each(salesObjs, function(i, obj) {
                                obj.primaryPhoto = primaryPhoto;
                                obj.propertyPhotos = propertyPhotos;
                            })
                        }
                        self.registerPhotoClickHandler();
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            },
            registerPhotoClickHandler: function(){
                var self = this
                $('.photoGallery_thumb').off("click").click(function(evt){
                    var propertyId = $(this).data('property');
                    var qupid = $(this).data('qupid');
                    var photoId =  $(this).data('id');
                    var path = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?propertyId='+propertyId+'&photoId='+photoId;
                    var searchWindow = window.open(path,'PropertyPhotos','scrollbars=no,resizable=yes,height=800,width=1024');
                    searchWindow.focus();
                    var timer = setInterval(function() {
                        if(searchWindow.closed == true) {
                            self.refreshResultRow(propertyId, qupid);
                            clearInterval(timer);
                        }
                    }, 1000);
                });
            },
            registerPhotoUploaderHandler: function(){
                var self = this
                $('.photo-uploader').off("click").click(function(evt){
                    var propertyId = $(this).data('property');
                    var qupid = $(this).attr('data-qupid');
                    var path = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?propertyId='+propertyId;
                    var searchWindow = window.open(path,'PropertyPhotos','scrollbars=no,resizable=yes,height=800,width=1024');
                    searchWindow.focus();
                    var timer = setInterval(function() {
                        if(searchWindow.closed == true) {
                            self.refreshResultRow(propertyId, qupid);
                            clearInterval(timer);
                        }
                    }, 1000);
                });
            },
            async registerSaleAnalysisHandler() {
                var self = this
                $('.sales-trafficLights').off("click").click(async function(evt) {
                    var saleId = $(this).attr('data-id');
                    var saleObj = self.findSalesByUuid(saleId)
                    self.selectedSaleQpid = saleObj.qupid;
                    var saleAnalysisType = $(this).attr('saleAnalysisType');

                    return self.tryOpenAnalysisById(saleObj.qivsSaleId);

                    if (saleAnalysisType == 'Yes' || saleAnalysisType == 'New') {
                        let path;
                        if (['A', 'D', 'F', 'H', 'P', 'S'].includes(saleObj.category.charAt(0))) {
                            if (saleObj.hasSaleAnalysis || saleObj.canBeAnalysed) {
                                path = `${window.location.protocol}//${window.location.hostname}${window.location.port ? ':' + window.location.port : ''}/sale-analysis/${saleObj.qivsSaleId}/rural`;
                            }
                            else {
                                self.modal.header = 'No Rural Worksheet';
                                self.modal.message = 'A rural worksheet was not found.\nPlease create a Rural Worksheet to run the Rural Sales Analysis';
                                self.modal.isOpen = !self.modal.isOpen;
                                return;
                            }
                        } else {
                            path = window.location.protocol + '//' + window.location.hostname +
                                ':' + window.location.port + '?saleId=' + saleId
                        }

                        var searchWindow = window.open(path, 'SaleAnalysis',
                            'scrollbars=yes,resizable=yes,height=800,width=1366');
                        searchWindow.focus();
                    }
                });
            },

            async fetchProperty(qpid) {
                var m = jsRoutes.controllers.PropertyMasterData.getProperty(qpid);
                try {
                    const response = await fetch(m.url, {
                        method: m.method,
                        cache: 'no-cache',
                    });
                    if (response.status >= 300) {
                        console.error(`Error fetching property master data for qpid ${qpid}: `, response);
                        throw new Exception(`Error fetching property master data for qpid ${qpid}`, response);
                    }
                    const responseBody = await response.json();
                    return responseBody;
                }
                catch(err) {
                    console.error(`Error fetching property master data for qpid ${qpid}: `, err);
                    throw new Exception(`Error fetching property master data for qpid ${qpid}`, err);
                }
            },

            openUrlInNewTab(url) {
                openUrlInNewTab(url);
            },
            lvRatioHtml(landArea, landValue) {
            const isAreaGte1Ha = parseFloat(landArea) >= 1
            const price = this.formatPrice(!(parseFloat(landArea) > 0 && parseFloat(landValue) > 0) ? 0 : parseFloat(landValue) / (isAreaGte1Ha ? parseFloat(landArea) : parseFloat(landArea) * 10000));
            const unit = isAreaGte1Ha ? 'ha' : 'm<sup>2</sup>';
            return `${price}<span>/ ${unit}</span>`;
        },
          },
        mounted: function() {
            const self = this;
            EventBus.$on('display-content', function(event) {
                var searchType = event.searchType
                if (searchType && searchType == 'sales-search') {
                    self.noResultsFound = false;
                    self.showTemplate = true;
                    $('.loadingSpinnerSearchResults').show();
                    var searchParams = event.searchParams
                    console.log(JSON.stringify(searchParams));

                    var m = jsRoutes.controllers.SalesSearch.displaySalesSearchResult();
                    self.searchParams = searchParams
                    if (self.sortField && self.sortField != '') {
                        self.searchParams.sort = self.getSortFields(self.sortField)
                    }
                    if (self.sortOrder && self.sortOrder != '') {
                        self.searchParams.order = self.sortOrder
                    }
                    $.ajax({
                        type: "POST",
                        url: m.url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(searchParams),
                        dataType: "json",
                        success: function (response) {
                            var totalResults = response && response.length > 0 ? response[0].totalResult : 0
                            self.searchResults = self.generateSalesSearchDataForRender(response, totalResults)
                            $('.loadingSpinnerSearchResults').hide();
                        },
                        error: function (response) {
                            self.errorHandler(response);
                        }
                    });
                }
                else {
                    self.salesResultCount = 0;
                    self.searchResults = null;
                    self.showTemplate=false;
                    self.noResultsFound = false;
                    $(window).off('scroll');
                    $(window).unbind('scroll');
                    $('#desktop').find($('a')).off("click")
                    $('.resultsRow').off("click")
                    $('.expandAll').off("click")
                    $('.closer').off("click")
                    $('.extrasWrapper').off("click")
                    $('.resultsRow').removeClass('openProp mdl-shadow--2dp');
                    $('.searchbarWrapper').removeClass('fixed');
                }
            });
        },
        updated: function() {
            const self = this;
            if (self.showTemplate == true) {
                self.registerPhotoClickHandler()
                self.registerPhotoUploaderHandler()
                self.registerSaleAnalysisHandler()
                /*if ($('.resultsRow').length == 0) {
                  $('.noResults-wrapper').removeClass('hide');
                }
                else {
                  $('.noResults-wrapper').addClass('hide');
                }*/

                var isExpandAll = $(".expandAll").hasClass('down')
                if (isExpandAll) {
                    $('.resultsRow').addClass('openProp mdl-shadow--2dp');
                }

                var isCondensedViewOn = $('.condensedView').prop('checked');
                if (isCondensedViewOn == false) {
                    $('.resultsRow').removeClass('theSkinny');
                }
                else {
                    $('.resultsRow').addClass('theSkinny');
                }

                $('.resultsRow').off("click").click(function(evt) {
                    if ($(evt.target).closest('.closer').length == 0) {
                        $(this).addClass('openProp mdl-shadow--2dp');
                    }
                });

                $('.tagSelect select').on('change',function() {
                    $(this).addClass('tagSelected');
                    $('.tagSelect').addClass('tagSelected');
                });

                $(".condensedView").off("change").on('change', function () {
                    var isCondensedViewOn = $('.condensedView').prop('checked');
                    if (isCondensedViewOn == false) {
                        $('.resultsRow').removeClass('theSkinny');
                    }
                    else {
                        $('.resultsRow').addClass('theSkinny');
                    }
                });

                $('.expandAll').off("click").click(function(evt) {
                    if ($('.resultsRow').hasClass('openProp mdl-shadow--2dp')) {
                        $('.resultsRow').removeClass('openProp mdl-shadow--2dp');
                        $('.expandAll').removeClass('down');
                    } else {
                        $('.resultsRow').addClass('openProp mdl-shadow--2dp');
                        //$('.extras').slideToggle();
                        $('.expandAll').addClass('down');
                    }
                });

                $('.closer').off("click").click(function(evt){
                   $(evt.target).closest('.resultsRow').removeClass('openProp mdl-shadow--2dp');
                   $(evt.target).closest('.taList').css('display', 'none');
                   $('body').removeClass('stopScrolling');
                });

                $('.extrasWrapper').off("click").click(function(evt){
                    $(this).find('.extras').slideToggle();
                    $('i').toggleClass('down');
                });

                $('#desktop').find($('a')).off("click").click(function () {
                    var liParent = $(this).parent();
                    var allOptions = $('#desktop').find('.sortRow').find('.colHeader');
                    var allOptionsMobile = $('#mobile').find('.sortRow').find('.colHeader');

                    if (liParent.hasClass('active')) {
                        if ($(this).find('i').hasClass('up')){
                            $(this).find('i').addClass('down');
                            $(this).find('i').removeClass('up');
                        }
                        else {
                            $(this).find('i').addClass('up');
                            $(this).find('i').removeClass('down');
                        }
                    } else {
                        allOptions.removeClass('active');
                        allOptions.find('i').removeClass('up');
                        allOptions.find('i').addClass('down');
                        liParent.addClass('active');
                    }

                    var sort = liParent.data('sort');

                    var order = $(this).find('i').hasClass('up');
                    if (order == true) {
                        order = "DESC";
                    } else {
                        order = "ASC";
                    }
                    self.searchParams.order=order
                    self.searchParams.sort = self.getSortFields(sort)
                    self.searchParams.offset = 0
                    var m = jsRoutes.controllers.SalesSearch.displaySalesSearchResult();
                    $.ajax({
                        type: "POST",
                        url: m.url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(self.searchParams),
                        dataType: "json",
                        success: function (response) {
                            self.sortField = sort
                            self.sortOrder = order
                            var totalResults = response && response.length > 0 ? response[0].totalResult : 0
                            self.searchResults = self.generateSalesSearchDataForRender(response, totalResults)
                        },
                        error: function (response) {
                            self.errorHandler(response);
                        }
                    });
                });


                $(window).data('ajaxready', true).scroll(function(e) {
                    var sticky = $('.searchbarWrapper'),
                        scroll = $(window).scrollTop(),
                        winHeight = $(window).height();
                    if (scroll >= 153) sticky.addClass('fixed');
                    else sticky.removeClass('fixed');
                    if ($(window).data('ajaxready') == false) return;
                    if(Math.ceil(scroll + winHeight) >= ($(document).height()-100)) {
                        $(window).data('ajaxready', false);
                        if($('.resultsRow:visible').length >= 25) {
                            $('.loadingSpinnerSearchResults').show();
                            if($('.resultsRow').length > 1) {
                                self.searchParams.offset = self.searchResults.length;
                                var m = jsRoutes.controllers.SalesSearch.displaySalesSearchResult();
                                $.ajax({
                                    type: "POST",
                                    url: m.url,
                                    cache: false,
                                    contentType: "application/json; charset=utf-8",
                                    data: JSON.stringify(self.searchParams),
                                    dataType: "json",
                                    success: function (response) {
                                        $('.loadingSpinnerSearchResults').hide();
                                        var totalResults = response && response.length > 0 ? response[0].totalResult : 0;
                                        if(self.searchResults.length < totalResults) {
                                            var results = self.generateSalesSearchDataForRender(response, totalResults)
                                            var newResultSet = self.searchResults.concat(results)
                                            self.searchResults = newResultSet
                                            $(window).data('ajaxready', true);
                                        }
                                    },
                                    error: function (response) {
                                        self.errorHandler(response);
                                    }
                                });
                            }
                        }
                    }
                });
            }
        }
    }
</script>
<style lang="scss" scoped>
    .icon--flipped {
        transform: scale(-1, 1);
    }
</style>

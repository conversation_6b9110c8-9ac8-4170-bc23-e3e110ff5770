<template>
    <div class="searchbarWrapper mdl-shadow--2dp">
        <form class="searchBar">
            <div class="typeahead__container">
                <territorial-authority showLabel="false" taId="quickSearchTa"></territorial-authority>
                <search></search>
                <span class="advSearch-icon" @click="onAdvancedSearchClick" title="Advanced Search">
                    <i class="material-icons md-dark">&#xE429;</i>
                </span>
                <span class="simpleSearch-icon"><i class="material-icons md-dark">&#xE8B6;</i></span>
                <type-ahead></type-ahead>
            </div>
        </form>
    </div>
</template>

<script>
import TypeAhead from '../filters/TypeAhead.vue';
import TerritorialAuthority from '../filters/TerritorialAuthority.vue'
import Search from './Search.vue';

export default {
    components: {
        Search,
        TerritorialAuthority,
        TypeAhead,
    },
    methods: {
        onAdvancedSearchClick: function() {
            $('.typeahead__result').hide();
            $('.advSearch-wrapper').toggle();
        },
    },
}
</script>

<style>

</style>

<template>
    <div class="advSearch-group advSearch-row-dependent" v-bind:class="{disabled: disabled}">
        <label>Sales Status</label>
        <div class="saleStatus">
            <input @click="onChange()" class="saleStatusChk" data-value="1" type="checkbox" id="confirmedSale" checked="checked">
            <label for="confirmedSale"><span>Confirmed</span></label>
        </div>
        <div class="saleStatus">
            <input @click="onChange()" class="saleStatusChk" data-value="3" type="checkbox" id="pendingSale" checked="checked">
            <label for="pendingSale"><span>Pending</span></label>
        </div>
        <div class="saleStatus">
            <input @click="onChange()" class="saleStatusChk" data-value="2" type="checkbox" id="unconfirmedSale">
            <label for="unconfirmedSale"><span>Unconfirmed</span></label>
        </div>
        <div class="saleStatus excludeHpiRtv" v-if="enableExcludeHpiRtv">
            <input @click="onChange()" class="saleStatusChk" data-value="rtvExclude" type="checkbox" id="excludeFromHpiRtvSale" data-cy="excludeFromHpiRtvCheckBox">
            <label for="excludeFromHpiRtvSale"  data-cy="excludeFromHpiRtvLabel"><span>Exclude from HPI-RTV</span></label>
        </div>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';

    export default {
        props: {
            disabled: {
                type: Boolean,
                default: false,
                required: false
            },
            enableExcludeHpiRtv: {
                type: Boolean,
                default: false,
                required: false
            },
        },
        methods: {
            onChange: function() {
                var saleStatus = [];
                $.each($('.saleStatusChk:checked'), function( index, value ) {
                    saleStatus.push($(value).data("value").toString());
                });
                EventBus.$emit('saleStatusCodes', saleStatus.length == 0 ? null : saleStatus);
            }
        }
    }
</script>
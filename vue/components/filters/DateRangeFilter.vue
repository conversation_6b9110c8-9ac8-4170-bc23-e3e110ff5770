<template>
    <div :class="filterClass">
        <label>{{ label }}</label>
        <input type="text" :readonly="readonly" :name="rangeId" :value="defaultVal" class="advSearch-text"/>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import daterangepicker from 'daterangepicker';
    import moment from 'moment';

    export default {
        props: ['rangeId', 'label', 'filterClass', 'defaultVal', 'readonly', 'showCustomRange'],
        methods: {
            cb: function(start, end) {
                EventBus.$emit(this.rangeId, { from: start.format('YYYY-MM-DD'), to: end.format('YYYY-MM-DD')});
            }
        },
        mounted: function() {
            var self = this;
            var showCustom = self.showCustomRange && self.showCustomRange=='true'
            $('input[name="' + self.rangeId + '"]').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    format: 'DD/MM/YYYY',
                    cancelLabel: 'Clear'
                },
                ranges: {
                    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                    'Last 3 Months':[moment().subtract(3, 'month').startOf('month'), moment()],
                    'Last 6 Months':[moment().subtract(6, 'month').startOf('month'), moment()],
                    'Last 9 Months':[moment().subtract(9, 'month').startOf('month'), moment()],
                    'Last 12 Months':[moment().subtract(12, 'month').startOf('month'), moment()],
                    'This Year': [moment((new Date("1/1/" + (new Date()).getFullYear())).valueOf()), moment()]
                },
                showCustomRangeLabel: showCustom
            }, self.cb);

            $('input[name="' + self.rangeId + '"]').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
                EventBus.$emit(self.rangeId, { from: picker.startDate.format('YYYY-MM-DD'), to: picker.endDate.format('YYYY-MM-DD')});
            });

            $('input[name="' + self.rangeId + '"]').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
                EventBus.$emit(self.rangeId, { from:null, to: null});
            });

            $('input[name="' + self.rangeId + '"]').bind("mouseup", function(e){
                var $input = $(this), oldValue = $input.val();
                if (oldValue == "") return;
                setTimeout(function(){
                    var newValue = $input.val();
                    if (newValue == ""){
                        $input.trigger("cleared");
                        EventBus.$emit(self.rangeId, { from:null, to: null});
                    }
                }, 1);
            });
        }
    }
</script>
<style>
    @import url("//cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.css");
    #saleDate,
    #saleInputDate {
        margin-left: 0;
    }

    .advSearch-group span.dateRangeSpan {
        display: inline-block;
        font-size: 1.2rem;
        line-height: 1.5;
        padding: .5rem .5rem .5rem 2.8rem;
        border: none;
        margin-right:1.4rem;
        margin-left:-.5rem;
        width: 29rem;
        height: 2.6rem;
        -webkit-box-shadow: inset 0 0 0 0.1rem #d2d2d2;
        -moz-box-shadow: inset 0 0 0 .1rem #d2d2d2;
        box-shadow: inset 0 0 0 0.1rem #d2d2d2;
    }

    .dateRangeSpan + .caret {
        margin:1.15rem 0 0 -3.4rem;
        pointer-events:none;
    }

    .daterangepicker { padding: 1rem 1rem 2.4rem; }

    .daterangepicker:before, .daterangepicker:after { display:none; }

    .daterangepicker.dropdown-menu {
        border-radius: 0;
        box-shadow: 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
    }

    .daterangepicker .input-mini {
        border: 1px solid #ccc;
        border-radius: 4px;
        color: #555;
        height: 27px;
        line-height: 1;
        display: block;
        vertical-align: middle;
        margin: 0 0 5px 0;
        padding: 0 6px 0 28px;
        width: 100%;
        border-radius: 0;
        font-size: 1.2rem;
    }

    #saleDate > i,
    #saleInputDate > i {
        position: relative;
    }

    #saleDate > i::before,
    #saleInputDate > i::before {
        position: absolute;
        top: -0.8rem;
        left: .4rem;
        content: "\E916";
        font-family: "Material Icons";
        font-size: 1.8rem;
        color: #999;
        line-height: 1;
    }

    .daterangepicker_input .input-mini.active + i::before {
        color: #357ebd;
    }

    .ranges {
        font-size: 1.1rem;
        float: none;
        margin: 0 0 6.4rem 0;
        text-align: left;
    }

    .ranges li {
        font-size: 1.1rem;
        line-height: 2.7;
        background-color: #f5f5f5;
        border: 1px solid #f5f5f5;
        border-radius: 2px;
        color: #4a90e2;
        padding: 0 1rem;
        margin-bottom: .7rem;
        cursor: pointer;
    }

    .ranges li:hover {
        background-color: #357EBE;
        border: 1px solid #357EBE;
        color: #fff;
    }

    .ranges li.active {
        background-color: #4a90e2;
        border: 1px solid #4a90e2;
        color: #fff;
    }

    .range_inputs {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        background-color: #f0f8fc;
        text-align: left;
        padding: 1.35em 1.1rem;
        border-top: 1px solid rgba(0,0,0,0.14);
        margin: 0;
        height: 6rem;
        box-sizing: border-box;
    }

    .daterangepicker button.btn.btn-sm {
        display: inline-block;
        position: relative;
        font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
        font-size: 1.2rem;
        font-weight: 600;
        font-style: normal;
        color: rgb(0,0,0);
        text-transform: uppercase;
        line-height: 1;
        line-height: 30px;
        letter-spacing: 0;
        text-align: center;
        text-decoration: none;
        background: rgba(158,158,158, 0.20);
        border: none;
        border-radius: 2px;
        vertical-align: middle;
        padding: 0 1rem;
        margin: 0;
        will-change: box-shadow;
        transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        min-width: 7.1rem;
        height: 3rem;
        overflow: hidden;
        outline: none;
        cursor: pointer;
    }

    .daterangepicker button.btn.btn-sm.btn-success {
        color: rgb(255,255,255);
        background: rgb(74,144,226);
    }

    .daterangepicker .calendar {
        padding-top:1rem;
        margin:0;
    }

    .daterangepicker .input-mini.active {
        border: 1px solid #08c;
        border-radius: 0;
    }

    .daterangepicker.ltr .calendar.left {
        padding-left: 2rem;
        margin-right: 1rem;
        margin-left: 2rem;
        height: 26rem;
        border-left: 1px solid #d2d2d2;
        margin: 0 1.6rem 0;
    }

    .daterangepicker .calendar-table { padding:.4rem 0 0; }

    .daterangepicker table {
        width: 100%;
        margin: 0;
        height: 20rem;
    }

    .daterangepicker th.month {
        font-size: 1.6rem;
        padding-bottom: 0;
        width: auto;
    }

    .daterangepicker .calendar th,
    .daterangepicker .calendar td {
        white-space: nowrap;
        text-align: center;
        min-width: 32px;
        vertical-align: middle;
    }

    .daterangepicker .table-condensed tr:nth-child(2) th {
        padding-top: 1rem;
    }

    .daterangepicker .daterangepicker_input i {
        position: absolute;
        left: 4px;
        top: 4px;
        width:1.8rem;
        height:2rem;
        pointer-events:none;
    }

    .daterangepicker_input .input-mini + i::before {
        content: "\E916";
        font-family: "Material Icons";
        font-size: 1.8rem;
        color: #999;
        line-height: 1;
    }

    .daterangepicker_input .input-mini.active + i::before {
        color: #357ebd;
    }

    .daterangepicker .calendar th {
        position: relative;
        border-radius: 0;
    }

    .daterangepicker .calendar th.prev.available i.glyphicon,
    .daterangepicker .calendar th.next.available i.glyphicon {
        position:initial;
        width: 3.2rem;
        height: 1.8rem;
    }

    .daterangepicker .calendar th.prev.available i::before,
    .daterangepicker .calendar th.next.available i::before {
        position:absolute !important;
        left: 0;
        top: 50%;
        content: "\E314";
        font-family: "Material Icons";
        font-size: 2.6rem;
        line-height:0;
        color: #357ebd;
        line-height: 0;
        width: 3.2rem;
    }

    .daterangepicker .calendar th.next.available i::before { content: "\E315"; }

    .daterangepicker td.available:hover,
    .daterangepicker th.available:hover {
        background-color: rgb(74,144,226) !important;
    }

    .daterangepicker .calendar th.prev.available:hover i::before,
    .daterangepicker .calendar th.next.available:hover i::before {
        color:#fff;
    }


</style>

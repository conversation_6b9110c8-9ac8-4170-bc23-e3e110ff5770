<template>
    <div class="advSearch-group">
        <multi-select
            select-all="Select all"
            :options="options"
            title="Select..."
            showLabel="false"
            @select="onChange"
            limit-to-show-label="1"
            limit-to-show-val="6"
            field-name="valuer-user"
            span-class="multiselect-native-select"
            :value="defaultValues"
            :button-title="buttonTitle"
        />
    </div>
</template>
<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import MultiSelect from '../common/MultiSelect.vue'

    export default {
        components: {
            MultiSelect
        },
        data: function() {
            return {
                options: [],
                defaultValues: [],
                buttonTitle: ""
            }
        },
        computed: {
            ...mapState('userData', [
                'userId',
                'userName',
            ]),
        },
        methods: {
            onChange: function(obj){
                const self = this;
                var selected = [];
                var selectedValuers = [];
                var selectedValuerNames = [];
                $(Object.keys(obj.values)).each(function(index, key){
                    selected.push((obj.values[key].value));
                    selectedValuers.push(obj.values[key].id);
                    selectedValuerNames.push({value:obj.values[key].value,label:obj.values[key].label, id: obj.values[key].id});
                });
                selectedValuers = selectedValuers.filter(function(id){return id && id != null && id != ''})

                localStorage.setItem(self.userId+'current-home-selection', JSON.stringify({'valuers':selected}))
                localStorage.setItem(self.userId+'selected-valuers', JSON.stringify(selectedValuers))
                localStorage.setItem(self.userId+'selected-valuers-names', JSON.stringify(selectedValuerNames))

                var eventObj = {}
                eventObj.valuers = selectedValuers

                if (selectedValuers && selectedValuers.length > 0) {
                    var unassignedIndex = selectedValuers.indexOf('Unassigned')
                    if (unassignedIndex > -1) {
                        selectedValuers.splice(unassignedIndex, 1)
                        eventObj.isUnassigned = true
                    }
                    eventObj.valuers = selectedValuers
                }
                self.buttonTitle = self.buttonText(obj.values);
                EventBus.$emit('display-valuation-jobs', eventObj);
            },
            buttonText: function(values) {
                const self = this;
                var numberOfOptions = Object.keys(values ? values : []).length;
                if (numberOfOptions === 0) {
                    return 'Select...';
                } else if(numberOfOptions == 1){
                    var label = "";
                    $.each(Object.keys(values), function(index, key) {
                        label = values[key].label;
                    });
                    try{
                        if($.type(label) == "undefined" || label == "") {
                            for(var i in self.options){
                                if(self.options[i].id == values[0]) {
                                    label = self.options[i].label;
                                    break;
                                }
                            }
                        }
                    }catch(e){
                        //Suppress errors, page must still work even if there is an error in above logic
                    }
                    return label;
                }
                else{
                    if (numberOfOptions >= 1) {
                        if(numberOfOptions === self.options.length){
                            return 'All Selected ('+numberOfOptions+')';
                        }
                        else {
                            return numberOfOptions + ' selected';
                        }
                    }
                }
            },
            setupUsersComponent : function(data){
                var self = this
                var options = [];
                var allValuerIds = [];

                $.each(data, function( index , value ) {
                    //console.log('valuer name: ' + value.name)
                    options.push({label: value.name, value: value.ntUsername, title: value.id, checked:'', id: value.id})
                    allValuerIds.push(value.id)
                });
                options.push({label: 'Unassigned', value: 'Unassigned', title: 'Unassigned', id:'Unassigned'})
                self.options = options;

                var homeSelection = JSON.parse(localStorage.getItem(self.userId+'current-home-selection'));
                var selectedValuers = JSON.parse(localStorage.getItem(self.userId + 'selected-valuers'))
                var selectedValuersNames = JSON.parse(localStorage.getItem(self.userId + 'selected-valuers-names'))

                var globalEventObj = null;

                if (homeSelection && homeSelection.valuers /*&& selectedValuersNames && selectedValuersNames.length > 0*/) {
                    var eventObj = {}
                    if (selectedValuers && selectedValuers.length == options.length) {
                        eventObj.allSelected = true
                    }
                    if (eventObj.allSelected) {
                        //All values must be checked
                        eventObj.valuers = allValuerIds
                        eventObj.isUnassigned = true
                    }
                    else {
                        if (selectedValuers && selectedValuers.length > 0) {
                            var unassignedIndex = selectedValuers.indexOf('Unassigned')
                            if (unassignedIndex > -1) {
                                selectedValuers.splice(unassignedIndex, 1)
                                eventObj.isUnassigned = true
                            }
                        }
                        eventObj.valuers = selectedValuers
                    }
                    globalEventObj = JSON.parse(JSON.stringify(eventObj))
                    EventBus.$emit('display-valuation-jobs', eventObj);
                }
                else /* if (!homeSelection) */ {

                    var eventObj = {}
                    eventObj.valuers = []
                    var isLoggedInUserAValuer = false
                    $.each(options, function( key, value ) {
                        var auth0name = 'QVNZ\\' + this.userName;
                        var currentauth0Name = value.value;
                        if (auth0name == currentauth0Name) {
                            eventObj.valuers = [value.title]
                            isLoggedInUserAValuer = true
                        }
                    });
                    if (!isLoggedInUserAValuer) {
                        var allValuers = []
                        eventObj.valuers = allValuerIds
                        eventObj.isUnassigned = true
                        eventObj.allSelected = true
                    }

                    globalEventObj = JSON.parse(JSON.stringify(eventObj))
                    EventBus.$emit('display-valuation-jobs', eventObj);
                }

                // could be defensive re globalEventObj
                self.defaultValues = globalEventObj.valuers
                if(globalEventObj.isUnassigned){
                    self.defaultValues.push('Unassigned');
                }
                EventBus.$emit("multiselect-select-values-valuer-user", self.defaultValues);
                this.buttonTitle = this.buttonText(self.defaultValues);
            }
        },
        mounted: function() {
            var self = this;
            this.$nextTick(function () {
                var allUsers = self.$store.state.users;
                if (!allUsers) {
                    var m = jsRoutes.controllers.ReferenceData.displayUsers();
                    $.ajax({
                        type: "GET",
                        url: m.url,
                        cache: false,
                        success: function (response) {
                            self.$store.commit('allUsers', response);
                            self.setupUsersComponent(response)
                        },
                        error: function (response) {
                            console.log(response);
                        }
                    });
                }
                else {
                    self.setupUsersComponent(allUsers)
                }
            });
        }
    }
</script>

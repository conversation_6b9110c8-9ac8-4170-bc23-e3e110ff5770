<template>
    <div class="advSearch-group advSearch-row-dependent disabled" :id="rangeClass('group', group)">
        <label v-html="getTitle(title, propType)"></label>
        <span class="conjunctionJnxn">
            <input type="text" :id="rangeClass('from',group)" :class="rangeClass('input', group + ' advSearch-text range from')" @paste="pasted" @keypress="keyPress">
        </span>
        <span>
            <input type="text" :id="rangeClass('to',group)" :class="rangeClass('input', group + ' advSearch-text range to')" @paste="pasted" @keypress="keyPress">
        </span>
        <div :class="rangeClass('valMessage error',group)"></div>
    </div>
</template>
<script>
    import {EventBus} from '../../EventBus.js';

    export default {
        props: ['title', 'group', 'decimal', 'propType'],
        methods: {
            getTitle: function (title, propType) {
                if (propType && propType === 'area') {
                    return title + ' /m<sup>2</sup>';
                }
                return title
            },
            rangeClass: function(obj, group) {
                var value = obj + '-' + group;
                return value;
            },
            keyPress: function(event) {
                if(this.decimal == "true") {
                    if(!(event.charCode >= 48 && event.charCode <= 57)) {
                        return event.preventDefault();
                    }
                } else {
                    if(!((event.charCode == 46) || (event.charCode >= 48 && event.charCode <= 57))) {
                        return event.preventDefault();
                    }
                }

            },
            pasted: function(clipboardEvent){
                var clipboardData, pastedData;
                // Stop data actually being pasted into the text field
                clipboardEvent.stopPropagation();
                clipboardEvent.preventDefault();

                // Get pasted data via clipboard API
                clipboardData = clipboardEvent.clipboardData || window.clipboardData;
                pastedData = clipboardData.getData('Text');

                if($.isNumeric(pastedData)) {
                    var result = document.execCommand('paste', false, pastedData);
                    if(!result) {
                        document.execCommand('insertText', false, pastedData);
                    }

                }else {
                    return false;
                }
            }
        },
        mounted: function() {
            var group = this.group;
            var decimal = this.decimal;
            $('.input-' + group).donetyping(function(evt){
                var from = parseInt($('#from-' + group).val());
                var to = parseInt($('#to-' + group).val());
                if(decimal ) {
                    from = parseFloat($('#from-' + group).val());
                    to = parseFloat($('#to-' + group).val());
                }
                if((from && to) || (from == 0 || to == 0)) {
                    if (from > to) {
                        var message = "<label>The To number must be equal to or greater than the From number.</label>";
                        $('.error-' + group).html(message);
                        $('#group-' + group).addClass("valError");
                        $('.advSearchSubmit').addClass('disabled');
                    } else {
                        $('#group-' + group).removeClass("valError");
                        if($('.error-' + group + ':visible').length == 0) {
                            $('.advSearchSubmit').removeClass('disabled');
                        }
                    }
                } else {
                    $('#group-' + group).removeClass("valError");
                    if($('.error-' + group + ':visible').length == 0) {
                        $('.advSearchSubmit').removeClass('disabled');
                    }
                }
                EventBus.$emit(group, { from: from, to: to});
            });
        }
    }
</script>
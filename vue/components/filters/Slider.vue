<template>
    <div class="advSearch-group" v-bind:class="iconClass">
        <label v-html="label"></label>
        <div :id="sliderId"></div>
        <span>
            <input class="advSearch-text" v-if="first" v-model="from" :id="first" type="text">
            <input class="advSearch-text" v-if="second" v-model="to" :id="second" type="text">
        </span>
        <div class="valMessage"></div>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import nouislider from 'nouislider';
    import numeral from 'numeral';

    export default {
        props: ['sliderId', 'label', 'iconClass', 'first', 'second', 'start', 'step', 'min', 'max', 'type', 'connect'],
        data: function() {
            const self = this
            return {
                from: self.start && self.start.length > 0 && self.start[0] ? self.start[0] : '',
                to: self.start && self.start.length > 1 && self.start[1] ? self.start[1] : ''
            }
        },
        methods: {
            formatVal: function(fieldId) {
                const self = this
                self[fieldId] = numeral(self[fieldId]).format('$0,0')
            },
            broadcast: function(){
                const self = this
                var eventObj = {}
                eventObj.from = self.type=='currency' ? numeral(self.from).value() : self.from
                eventObj.to = self.type=='currency' ? numeral(self.to).value() : self.to
                EventBus.$emit(self.sliderId, eventObj)
            }
        },
        mounted: function() {
            var self = this;
            var slider = document.getElementById(self.sliderId)
            nouislider.create(slider, {
                start: self.start,
                connect: self.connect,
                /*tooltips: true,*/
                step: self.step,
                range: {
                    'min': self.min ? parseInt(self.min) : 0,
                    'max': self.max ? (parseInt(self.max) && parseInt(self.max)!=parseInt(self.min) ? parseInt(self.max) : parseInt(self.max)+parseInt(self.step)) : 0
                }
            });

            var minValInput = document.querySelector('#'+self.first);
            var maxValInput = document.querySelector('#'+self.second);

            slider.noUiSlider.on('change', function( values, handle ) {

                // This version updates both inputs.
                var rangeValues = values;
                minValInput.value = self.type == 'currency' ? numeral(rangeValues[0]).format('$0,0') : rangeValues[0];
                self.from = minValInput.value
                if (maxValInput) {
                    maxValInput.value = self.type == 'currency' ? numeral(rangeValues[1]).format('$0,0') : rangeValues[1];
                    self.to = maxValInput.value
                }
                self.broadcast();
            });

            minValInput.addEventListener('change', function(){
                self.from = self.type == 'currency' && this.value ? numeral(this.value).format('$0,0') : this.value
                slider.noUiSlider.set([numeral(this.value).value(), null]);
                self.broadcast();
            });

            if (maxValInput) {
                maxValInput.addEventListener('change', function(){
                    self.to = self.type == 'currency' && this.value ? numeral(this.value).format('$0,0') : this.value
                    slider.noUiSlider.set([null, numeral(this.value).value()]);
                    self.broadcast();
                });
            }
        }
    }
</script>
<style>
    /*! nouislider - 10.0.0 - 2017-05-28 14:52:48 */
    /* Functional styling;
     * These styles are required for noUiSlider to function.
     * You don't need to change these rules to apply your design.
     */
    .noUi-target,
    .noUi-target * {
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        -webkit-user-select: none;
        -ms-touch-action: none;
        touch-action: none;
        -ms-user-select: none;
        -moz-user-select: none;
        user-select: none;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }
    .noUi-target {
        position: relative;
        direction: ltr;
    }
    .noUi-base {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 1;
        /* Fix 401 */
    }
    .noUi-connect {
        position: absolute;
        right: 0;
        top: 0;
        left: 0;
        bottom: 0;
    }
    .noUi-origin {
        position: absolute;
        height: 0;
        width: 0;
    }
    .noUi-handle {
        position: relative;
        z-index: 1;
    }
    .noUi-state-tap .noUi-connect,
    .noUi-state-tap .noUi-origin {
        -webkit-transition: top 0.3s, right 0.3s, bottom 0.3s, left 0.3s;
        transition: top 0.3s, right 0.3s, bottom 0.3s, left 0.3s;
    }
    .noUi-state-drag * {
        cursor: inherit !important;
    }
    /* Painting and performance;
     * Browsers can paint handles in their own layer.
     */
    .noUi-base,
    .noUi-handle {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
    /* Slider size and handle placement;
     */
    .noUi-horizontal {
        height: 18px;
    }
    .noUi-horizontal .noUi-handle {
        width: 34px;
        height: 28px;
        left: -17px;
        top: -6px;
    }
    .noUi-vertical {
        width: 18px;
    }
    .noUi-vertical .noUi-handle {
        width: 28px;
        height: 34px;
        left: -6px;
        top: -17px;
    }
    /* Styling;
     */
    .noUi-target {
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #D3D3D3;
        box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;
    }
    .noUi-connect {
        background: #3FB8AF;
        border-radius: 4px;
        box-shadow: inset 0 0 3px rgba(51, 51, 51, 0.45);
        -webkit-transition: background 450ms;
        transition: background 450ms;
    }
    /* Handles and cursors;
     */
    .noUi-draggable {
        cursor: ew-resize;
    }
    .noUi-vertical .noUi-draggable {
        cursor: ns-resize;
    }
    .noUi-handle {
        border: 1px solid #D9D9D9;
        border-radius: 3px;
        background: #FFF;
        cursor: default;
        box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;
    }
    .noUi-active {
        box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB;
    }
    /* Handle stripes;
     */
    .noUi-handle:before,
    .noUi-handle:after {
        content: "";
        display: block;
        position: absolute;
        height: 14px;
        width: 1px;
        background: #E8E7E6;
        left: 14px;
        top: 6px;
    }
    .noUi-handle:after {
        left: 17px;
    }
    .noUi-vertical .noUi-handle:before,
    .noUi-vertical .noUi-handle:after {
        width: 14px;
        height: 1px;
        left: 6px;
        top: 14px;
    }
    .noUi-vertical .noUi-handle:after {
        top: 17px;
    }
    /* Disabled state;
     */
    [disabled] .noUi-connect {
        background: #B8B8B8;
    }
    [disabled].noUi-target,
    [disabled].noUi-handle,
    [disabled] .noUi-handle {
        cursor: not-allowed;
    }
    /* Base;
     *
     */
    .noUi-pips,
    .noUi-pips * {
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }
    .noUi-pips {
        position: absolute;
        color: #999;
    }
    /* Values;
     *
     */
    .noUi-value {
        position: absolute;
        white-space: nowrap;
        text-align: center;
    }
    .noUi-value-sub {
        color: #ccc;
        font-size: 10px;
    }
    /* Markings;
     *
     */
    .noUi-marker {
        position: absolute;
        background: #CCC;
    }
    .noUi-marker-sub {
        background: #AAA;
    }
    .noUi-marker-large {
        background: #AAA;
    }
    /* Horizontal layout;
     *
     */
    .noUi-pips-horizontal {
        padding: 10px 0;
        height: 80px;
        top: 100%;
        left: 0;
        width: 100%;
    }
    .noUi-value-horizontal {
        -webkit-transform: translate3d(-50%, 50%, 0);
        transform: translate3d(-50%, 50%, 0);
    }
    .noUi-marker-horizontal.noUi-marker {
        margin-left: -1px;
        width: 2px;
        height: 5px;
    }
    .noUi-marker-horizontal.noUi-marker-sub {
        height: 10px;
    }
    .noUi-marker-horizontal.noUi-marker-large {
        height: 15px;
    }
    /* Vertical layout;
     *
     */
    .noUi-pips-vertical {
        padding: 0 10px;
        height: 100%;
        top: 0;
        left: 100%;
    }
    .noUi-value-vertical {
        -webkit-transform: translate3d(0, 50%, 0);
        transform: translate3d(0, 50%, 0);
        padding-left: 25px;
    }
    .noUi-marker-vertical.noUi-marker {
        width: 5px;
        height: 2px;
        margin-top: -1px;
    }
    .noUi-marker-vertical.noUi-marker-sub {
        width: 10px;
    }
    .noUi-marker-vertical.noUi-marker-large {
        width: 15px;
    }
    .noUi-tooltip {
        display: block;
        position: absolute;
        border: 1px solid #D9D9D9;
        border-radius: 3px;
        background: #fff;
        color: #000;
        padding: 5px;
        text-align: center;
        white-space: nowrap;
    }
    .noUi-horizontal .noUi-tooltip {
        -webkit-transform: translate(-50%, 0);
        transform: translate(-50%, 0);
        left: 50%;
        bottom: 120%;
    }
    .noUi-vertical .noUi-tooltip {
        -webkit-transform: translate(0, -50%);
        transform: translate(0, -50%);
        top: 50%;
        right: 120%;
    }
</style>

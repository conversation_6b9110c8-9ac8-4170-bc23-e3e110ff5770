<template>
    <div class="advSearch-group advSearch-row-dependent disabled">
        <label v-html="label"></label>
        <span class="fieldTwo" v-bind:class="filterId">
            <multi-select
                    select-all="Select all"
                    :options="options"
                    :title="'Select...'"
                    show-label="false"
                    @select="onChange"
                    limit-to-show-label="1"
                    limit-to-show-val="6"
                    :field-name="filterId"
                    :button-title="buttonTitle"
                    all-selected-text="All Regions Selected"
                    enable-case-insensitive-filtering="true"
                    multiple="true"
            >

            </multi-select>
        </span>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { mapGetters } from 'vuex';
    import MultiSelect from '../common/MultiSelect.vue'

    export default {
        components: {
            MultiSelect
        },
        computed: {
            ...mapGetters([
                'getCategoryClassifications',
            ]),
            options() {
                const categoryClassifications = this.getCategoryClassifications(this.category);
                const options = [];
                for (const option of categoryClassifications) {
                    options.push({
                            label: this.showCode ? `${option.code.trim()} - ${option.description}` : option.description,
                            value: option.code.trim(),
                        });
                }
                this.applyStyling();
                return options;
            },
        },
        props: ['styleAttributes', 'filterId', 'label', 'group', 'propertyName', 'includeCodeInLabel', 'category', 'fieldName', 'showCode'],
        data: function() {
            return {
                buttonTitle: "",
            }
        },
        methods: {
            onChange: function(obj) {
                const self = this;
                var selected = [];
                $(Object.keys(obj.values)).each(function(index, key){
                    selected.push(obj.values[key].value);
                    console.log(selected[0]);
                });
                EventBus.$emit(self.group, selected.length == 0 ? null : selected);
                this.buttonTitle = this.buttonText(obj.values);
            },
            buttonText: function(values) {
                const self = this;
                var numberOfOptions = Object.keys(values).length;
                if (numberOfOptions === 0) {
                    return 'Select...';
                }else if(numberOfOptions == 1) {
                    var label = "";
                    try{
                        $.each(Object.keys(values), function(index, key) {
                            label = values[key].label;
                        });
                    }catch(e){}
                    return label;
                }
                else{
                    if (numberOfOptions >= 1) {
                        if(numberOfOptions === self.options.length){
                            return 'All Selected ('+numberOfOptions+')';
                        }
                        else {
                            return numberOfOptions + ' selected';
                        }
                    }
                }
            },
            applyStyling() {
                for (const styleAttribute of this.styleAttributes) {
                    $(`.${this.filterId}`).find('ul').css(styleAttribute.key, styleAttribute.value);
                }
            },
        },
    }
</script>

<template>
    <div class="advSearch-group" v-bind:class="[errorMessage && errorMessage != '' ? 'valError' : '', iconClass]" :id="id">
        <label v-html="label"></label>
        <select v-bind:id="filterId" v-bind:multiple="multiple" class="advSearch-singleselect monarch-multiselect" v-bind:class="[selectClass, 'advSearch-'+filterId+'-multiselect']">
            <option v-for="option in options" :value="option.value">{{ option.label }}</option>
        </select>
        <div class="valMessage"><label v-html="errorMessage"></label></div>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import commonUtils from '../../utils/CommonUtils';

    export default {
        props: ['id', 'currVal','iconClass','filterId', 'label', 'attrName', 'componentName', 'selectClass', 'chooseHere', 'dataToFetch', 'multiple', 'singleMap', 'objKey', 'parentAttrName', 'onDropdownHide', 'requestType', 'errorMessage', 'useShortDescription', 'sort', 'isSetupComplete'],
        mixins: [commonUtils],
        data: function() {
            return {
                options: [],
                data: []
            }
        },
        methods: {
            notifyComponent: function(selected){
                const self = this
                if (self.objKey) {
                    if (self.multiple) {
                        var obj = {}
                        obj.parentAttrName = self.parentAttrName
                        obj.attrName = self.attrName
                        obj.key = self.objKey
                        obj.val = selected
                        if (self.singleMap) {
                            EventBus.$emit('notify-single-nested-'+self.componentName, obj)
                        }
                        else {
                            EventBus.$emit('notify-multi-nested-'+self.componentName, obj)
                        }
                    }
                    else {
                        var obj = {}
                        obj.parentAttrName = self.parentAttrName
                        obj.attrName = self.attrName
                        obj.key = self.objKey
                        obj.val = selected[0]
                        if (self.singleMap) {
                            EventBus.$emit('notify-single-nested-'+self.componentName, obj)
                        }
                        else {
                            EventBus.$emit('notify-multi-nested-'+self.componentName, obj)
                        }
                    }
                }
                else {
                    if (self.multiple) {
                        var obj = {}
                        obj.attrName = self.attrName
                        obj.val = selected
                        EventBus.$emit('notify-multi-'+self.componentName, obj)
                    }
                    else {
                        var obj = {}
                        obj.attrName = self.attrName
                        obj.val = selected[0]
                        EventBus.$emit('notify-multi-'+self.componentName, obj)
                    }
                }
            },
            setupMultiSelect : function(response){
                const self = this
                var options = [];
                if (!self.multiple) {
                    options.push({label: ' ', value: '', title: '', sortOrder: ''});
                }
                $.each(response, function() {
                    options.push({label: self.useShortDescription ? this.shortDescription : this.description, value: this.code.trim(), title: this.category, sortOrder: this.sortOrder});
                });

                if(self.sort){
                    options.sort(function(a,b) {
                        return parseInt(a.sortOrder) - parseInt(b.sortOrder);
                    });
                }

                $('.advSearch-'+self.filterId+'-multiselect').multiselect('dataprovider', options);
                if (!self.multiple) {
                    if (self.currVal) {
                        $('.advSearch-'+self.filterId+'-multiselect').multiselect('select',self.currVal.code);
                    }
                    $('div#' + self.id + ' ul.multiselect-container li').off("mousedown").on('mousedown', function (e) {
                        var item = $(this);
                        if(item.hasClass('active')) {
                            $('.advSearch-'+self.filterId+'-multiselect').multiselect('select','');
                            self.notifyComponent([]);
                            $('div#' + self.id + ' ul.multiselect-container li').first().click();
                            $('.advSearch-'+self.filterId+'-multiselect').multiselect('refresh');
                        }
                    });
                 }
                else {
                    $.each(self.currVal, function(index, value) {
                        if(value) $('.advSearch-'+self.filterId+'-multiselect').multiselect('select', value.code.trim());
                        $('.advSearch-'+self.filterId+'-multiselect').multiselect('refresh');
                    });
                }
            }
        },
        mounted: function() {
            var self = this;
            $('.advSearch-'+self.filterId+'-multiselect').multiselect({
                nonSelectedText: '',
                nSelectedText: 'selected',
                allSelectedText: 'All Selected',
                maxHeight: 500,
                numberDisplayed: 1,
                multiple: self.multiple ? self.multiple : false,
                onChange: function(element, checked) {
                    var agts = $('.advSearch-'+self.filterId+'-multiselect option:selected');
                    var selected = [];
                    $(agts).each(function(index, ta){
                        var obj = self.getClassificationObject(self.dataToFetch, $(this).val());
                        if (obj && obj.code && obj.code != '') {
                            selected.push(obj);
                        }
                    });
                    $('#'+self.id).removeClass('valError')
                    self.notifyComponent(selected);
                },
                onDropdownHide: function (event) {
                    if (self.onDropdownHide) {
                        var agts = $('.advSearch-'+self.filterId+'-multiselect option:selected');
                        var selected = [];
                        $(agts).each(function(index, ta){
                            var obj = self.getClassificationObject(self.dataToFetch, $(this).val());
                            if (obj && obj.code && obj.code != '') {
                                selected.push(obj);
                            }
                        });

                        var obj = {}
                        obj.attrName = self.attrName
                        obj.val = selected
                        obj.objKey = self.objKey
                        self.onDropdownHide(obj);
                    }
                },
                buttonText: function(options, select) {
                    if(options.length > 0) {
                        if (self.multiple) {
                            var numberOfOptions = select[0].length;
                            if (options.length === numberOfOptions) {
                                return 'All Items\'s Selected (' + options.length + ')';
                            } else {
                                var selected = '';
                                options.each(function(index, obj){
                                    var label = $(obj).attr('label');
                                    if(index == 0) {
                                        selected = label;
                                    } else {
                                        selected +=  ', ' + label;
                                    }
                                });
                                return selected.substr(0, 140);
                            }
                        } else {
                            return $(options[0]).attr('label');
                        }
                    } else {
                        return "";
                    }
                }
            });
            var optionsForClassification = self.$store.getters.getCategoryClassifications(self.dataToFetch);
            if (optionsForClassification) {
                if(self.isSetupComplete && self.dataToFetch === 'ValuationReportType' && (self.currVal && !self.currVal.code.includes('VQV'))){
                    let newVal = optionsForClassification.filter(function (el){
                        return !el.code.includes('VQV');
                    })
                    self.setupMultiSelect(newVal);
                }
                else {
                    self.setupMultiSelect(optionsForClassification);
                }
            }
            else {
                if (self.requestType && self.requestType == 'POST') {
                    var criteria = {};
                    criteria.category = self.dataToFetch
                    criteria.sort = self.sort ? self.sort : 'CODE'
                    criteria.order = 'ASC'

                    var m = jsRoutes.controllers.ReferenceData.searchClassifications();
                    $.ajax({
                        type: "POST",
                        url: m.url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(criteria),
                        dataType: "json",
                        success: function (response) {
                            var classificationToStore = {}
                            classificationToStore.key = self.dataToFetch
                            classificationToStore.data = response
                            self.$store.commit("addClassification", classificationToStore);
                            self.setupMultiSelect(response)
                        },
                        error: function (response) {
                            console.log(response);
                        }
                    });
                }
                else {
                    var m = jsRoutes.controllers.ReferenceData.displayClassification(self.dataToFetch, true);
                    this.$nextTick(function () {
                        $.ajax({
                            type: "GET",
                            url: m.url,
                            cache: false,
                            success: function (response) {
                                var classificationToStore = {}
                                classificationToStore.key = self.dataToFetch
                                classificationToStore.data = response
                                self.$store.commit("addClassification", classificationToStore);
                                self.setupMultiSelect(response)
                            },
                            error: function (response) {
                                console.log(response);
                            }
                        });
                    });
                }
            }
        }
    }
</script>

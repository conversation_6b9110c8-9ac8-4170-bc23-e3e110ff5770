<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div v-if="refresh" class="advSearch-group twentyfivePct icons8-crosshair-filled" :id="id">
        <label>{{ title }}</label>
        <multi-select
                :options="options"
                title=""
                showLabel="false"
                @select="onChange"
                :field-name="filterId"
                enable-filtering = "false"
                span-class="multiselect-native-select"
                :value="defaultValue"
                :button-title="buttonTitle"
                multiple="false"
        >
        </multi-select>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import MultiSelect from '../common/MultiSelect.vue'
    export default {
        props: ['id', 'iconClass', 'title', 'value', 'options', 'hasError', 'errorMessage', 'unselect', 'filterId'],
        components: {
            MultiSelect
        },
        methods: {
            triggerRefresh(trigger){
                if(trigger){
                    this.refresh = false;
                }
                this.refresh = true;
            },
            onChange(input) {
                const key = Object.keys(input.values)[0];
                if(input.values[key]){
                    let selectedValue = input.values[key].value;
                    const selectedZoneOption = this.options.filter(zoneOption => zoneOption.value === selectedValue)[0];
                    EventBus.$emit(this.filterId+'-multiselect-update', {attrName: this.filterId, val: selectedZoneOption});
                    this.defaultValue = selectedZoneOption;
                    this.buttonTitle = selectedZoneOption.label;
                }
                else {
                    this.defaultValue = '';
                    this.buttonTitle = '';
                    EventBus.$emit(this.filterId+'-multiselect-update', {attrName: this.filterId, val: null});
                }
                this.triggerRefresh(true);
               
            }
        },
        data: function() {
            return {
                sourceData: [],
                buttonTitle: "",
                defaultValue: '',
                zoneOptions: [],
                isDefaultValueLoaded: false,
                refresh: true
            }
        },
        created: function() {
            if(this.value){
                this.defaultValue = this.options.find(e => e.value == this.value.code);
                this.buttonTitle = this.defaultValue.label;
            }
            this.triggerRefresh(true);
        },
    }
</script>

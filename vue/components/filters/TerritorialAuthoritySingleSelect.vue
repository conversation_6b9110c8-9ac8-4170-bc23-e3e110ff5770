<template>
    <div id="viewDashboardDiv" class="taDashboard-selector advSearch-group qvToolbar-leftMenu lefty" v-show="isInternalUser || externalObjectionAccess">
        <span class="multiselect-native-select">
            <select class="dashboard-internal-single-select">
                <option v-for="ta in territorialAuthorities" :value="ta.value">{{ ta.label }}</option>
            </select>
        </span>
    </div>
</template>
<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';

    export default {
        data: function() {
            return {
                territorialAuthorities: [],
            }
        },
        computed: {
            ...mapState('userData', [
                'userId',
                'isInternalUser',
                'externalObjectionAccess',
                'allowedTACodes'
            ]),
        },
        mounted: function() {
            EventBus.$on("clear-ta-dashboard-single-select", function(event){
                $('.dashboard-internal-single-select').multiselect('deselectAll', false);
                $('.dashboard-internal-single-select').multiselect('refresh');
                $('.dashboard-internal-single-select').multiselect('updateButtonText')
            });
            $('.dashboard-internal-single-select').multiselect({
                nonSelectedText: 'Select...',
                enableFiltering: true,
                filterPlaceholder: 'Filter List...',
                enableFullValueFiltering: false,
                enableCaseInsensitiveFiltering: true,
                maxHeight: 400,
                numberDisplayed: 1,
                multiple: false,
                onChange: function(element, checked) {
                    var taCode = isNaN(parseInt(element[0].value)) ? 0 : parseInt(element[0].value);
                    if (taCode && taCode != '' && taCode > 0) {
                        localStorage.setItem(self.userId+'current-home-selection', JSON.stringify({'TA': taCode}))
                        $('.advSearch-wrapper').hide();
                        var eventObj = {};
                        eventObj.taCode = taCode;
                        eventObj.searchType = 'ta-dashboard';
                        eventObj.onHomePage = true;
                        EventBus.$emit('display-content', eventObj);
                    }
                }
            });
            const self = this;
            this.$nextTick(function () {
                var m = jsRoutes.controllers.Application.fetchTerritorialAuthorities();
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        var tas = response;
                        var items = [];
                        items.push({label: 'View Dashboard', value: '', id: ''});
                        for(var taCode in tas){
                            var taName = tas[taCode];
                            var formattedTaCode = taCode < 10 ? '0' + taCode : taCode;
                            if (self.externalObjectionAccess && $.inArray(taCode, self.allowedTACodes) >= 0) {
                                items.push({ label: taCode + ' - ' + taName, value: taCode, id: taCode });
                            }
                            else if (self.isInternalUser || $.inArray(taCode, self.allowedTACodes) >= 0) {
                                items.push({ label: taCode + ' - ' + taName, value: taCode, id: taCode });
                            }
                        }
                        $('.dashboard-internal-single-select').multiselect('dataprovider', items);
                        var selectedTA = JSON.parse(localStorage.getItem(self.userId+'current-home-selection'));
                        if (selectedTA && selectedTA.TA) {
                            $('.dashboard-internal-single-select').multiselect('select', selectedTA.TA);
                        }
                    },
                    error: function (response) {
                        console.error(response);
                    }
                });
            });
        }
    }
</script>

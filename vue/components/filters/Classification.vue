<script setup>
import { ref, watch, watchEffect } from 'vue';

const props = defineProps(['classificationOptions', 'value', 'customLabelStyle' ,'disabled']);
const emit = defineEmits(['input']);
const options = ref([]);
const dropdown = ref(null);
const templateWrapperDiv = ref(null);

watchEffect(() => options.value = props.classificationOptions?.map((e) => ({ ...e })) || []);
watch(
  () => props.value,
  () => options.value?.forEach(
    (option) => option.checked = isOptionSelected(option)
  )
);

function onChange(opt) {
  const allValues = ['marketSales', 'salesToCheck', 'saleType', 'saleTenure', 'priceValueRelationship'];
  const marketSalesValues = ['marketSales', '0-S', '1-1', '2-1', '2-2'];
  const salesToCheckValues = ['salesToCheck', '0-S', '0-P', '0-M', '1-1', '2-2'];

  options.value?.forEach((e) => {
    let isChecked = false;
    switch (opt.value) {
      case 'all':
        if (opt.checked && (allValues.includes(e.value))) {
          isChecked = false;
        } else {
          isChecked = opt.checked;
        }
        break;
      case 'marketSales':
        if (marketSalesValues.includes(e.value)) {
          isChecked = opt.checked;
        }
        break;
      case 'salesToCheck':
        if (salesToCheckValues.includes(e.value)) {
          isChecked = opt.checked;
        }
        break;
      default:
        isChecked = e.checked;
        break;
    }
    e.checked = isChecked;
  });
  emit('input', getSelectedClassifications());
}

function isOptionSelected(option) {
  return !!props.value?.find((selected) => option.value == selected.value);
}

function getSelectedClassifications() {
  return options.value.filter((e) => e.checked);
}

function dropdownDisplayHandler(event) {
  const el = dropdown.value;
    if (templateWrapperDiv.value.contains(event.target)) {
        el.classList.add('open');
    }
}

</script>


<template>
    <div id="group-salesClassification" class="advSearch-group advSearch-row-dependent" :class="{disabled: disabled}" @click="dropdownDisplayHandler">
      <label :style="customLabelStyle">Sales Classification</label>
      <span class="fieldTwo">
        <span class="multiselect-native-select classification-parent">
          <div class="btn-group" ref="dropdown">
            <button
              type="button"
              class="multiselect dropdown-toggle btn btn-default"
              data-toggle="dropdown"
              title="Market Sales, S - Whole, 1 - Freehold, 1 - Arm's-length, 2 - Review required"
              aria-expanded="true"
            >
              <span class="multiselect-selected-text">{{`${getSelectedClassifications().length} selected`}}</span>
              <b class="caret"></b>
            </button>
            <ul class="classification multiselect-container dropdown-menu" style="margin-top:0;" ref="templateWrapperDiv">
              <li v-for="(opt,index) in options" :key="index" :class="{ active: opt.checked , 'multiselect-item multiselect-group': opt.group }">
                <label v-if="opt.group">{{ opt.label }}</label>
                <a v-else>
                  <label class="checkbox"><input type="checkbox" value="opt.value" v-model="opt.checked" @change="onChange(opt)"/> {{ opt.label }}</label>
                </a>
              </li>
            </ul>
          </div>
        </span>
      </span>
    </div>
</template>

<style>
ul.classification {
  max-height: 340px; 
  overflow: hidden auto;
}
</style>

<template>
    <div class="advSearch-group advSearch-group-ta">
        <label>TA Range</label>
        <span class="conjunctionJnxn"><input id="fromtarange" class="taFrom taRange advSearch-text" type="text" onkeypress='return event.charCode >= 48 && event.charCode <= 57'></span>
        <span><input id="totarange" class="taTo taRange advSearch-text" type="text" onkeypress='return event.charCode >= 48 && event.charCode <= 57'></span>
        <div class="valMessage ta"><label></label></div>
    </div>
</template>
<script>
    import { mapState } from 'vuex';
    import {EventBus} from '../../EventBus.js';
    import { store } from '../../DataStore';

    export default {
        computed: {
            ...mapState('userData', [
                'isInternalUser',
                'allowedTACodes',
            ]),
        },
        mounted: function() {
            var self = this;
            $('.taRange').donetyping(function () {
                var taFrom = $('.taFrom').val();
                var taTo = $('.taTo').val();
                var selected = [];
                var isErrorState = false
                if (taFrom && taTo) {
                    taFrom = parseInt(taFrom);
                    taTo = parseInt(taTo);
                    if (taFrom > taTo) {
                        var message = "<label>The To number must be equal to or greater than the From number.</label>";
                        $('.ta').html(message);
                        $('.advSearch-group-ta').addClass("valError");
                        isErrorState = true
                    } else {
                        $('.advSearch-group-ta').removeClass("valError");
                        var selected = [];
                        for (var i = taFrom; i <= taTo; i++) {
                            if (self.isInternalUser || $.inArray(i, self.allowedTACodes) >= 0) {
                                selected.push(i);
                            }
                        }
                        if (selected.length == 0) {
                            selected.push(-1);
                        }
                    }
                } else if (taFrom || taTo) {
                    var message = "<label>Please finish entering your TA range.</label>";
                    $('.ta').html(message);
                    $('.advSearch-group-ta').addClass("valError");
                    isErrorState = true
                } else {
                    $('.advSearch-group-ta').removeClass("valError");
                }
                if (!isErrorState) {
                    EventBus.$emit('taRange', selected);
                }
                else {
                    $('.advSearch-row-dependent').addClass('disabled');
                }

            });
        }
    }
</script>

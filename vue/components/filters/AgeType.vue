<template>
    <div id="ageDiv" class="advSearch-group noMargin advSearch-row-dependent disabled">
        <label>Age</label>
        <span class="fieldTwo noMargin ageType">
            <select id="age" v-bind:class="'advSearch-'+group+'-multiselect'" class="monarch-multiselect" multiple="multiple">
                <option v-for="ageType in ageTypes" :value="ageType.value">{{ ageType.label }}</option>
            </select>
        </span>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';

    export default {
        props: ['group'],
        data: function() {
            return {
                ageTypes: []
            }
        },
        mounted: function() {
            var self = this;
            this.$nextTick(function () {

                var criteria = {}
                criteria.category = "BuildingAge"
                criteria.isActive = true

                var m = jsRoutes.controllers.ReferenceData.searchClassifications();
                $.ajax({
                    type: "POST",
                    url: m.url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(criteria),
                    dataType: "json",
                    success: function (response) {
                        var ageTypes = [];
                        $.each(response, function() {
                            ageTypes.push({label: this.description, value: this.code.trim()});
                        });
                        $('.advSearch-'+self.group+'-multiselect').multiselect({
                            nonSelectedText: 'Select...',
                            nSelectedText: 'selected',
                            enableFiltering: true,
                            dropUp: true,
                            filterPlaceholder: 'Filter List...',
                            enableFullValueFiltering: false,
                            includeSelectAllOption: true,
                            enableCaseInsensitiveFiltering: true,
                            allSelectedText: 'All Selected',
                            maxHeight: 500,
                            numberDisplayed: 1,
                            multiple: true,
                            onChange: function(element, checked) {
                                var agts = $('.advSearch-'+self.group+'-multiselect option:selected');
                                var selected = [];
                                $(agts).each(function(index, ta){
                                    selected.push($(this).val());
                                });
                                EventBus.$emit(self.group, selected.length == 0 ? null : selected);
                            }
                        });
                        //self.ageTypes = ageTypes;
                        $('.advSearch-'+self.group+'-multiselect').multiselect('dataprovider', ageTypes);
                        $('.ageType').find( "ul" ).css('margin-top', '-200px');
                        $('.ageType').find( "ul" ).css('max-height', '300px');
                        $('.ageType').find( "ul" ).css('margin-left', '-100px');
                    },
                    error: function (response) {
                        console.log(response);
                    }
                });
            });

        }
    }
</script>

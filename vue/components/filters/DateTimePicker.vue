<template>
    <div>
        <label>{{ label }}</label>
        <input type="text" :name="attrName" :value="formatDisplayDate()" class="advSearch-text" readonly="true"/>
        <div class="valMessage"><label>{{ errorMessage }}</label></div>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import daterangepicker from 'daterangepicker';
    import moment from 'moment';
    import momentTz from 'moment-timezone';

    export default {
        props: ['componentName', 'attrName', 'label', 'value', 'readonly', 'hasError', 'errorMessage'],
        methods: {
            formatDisplayDate: function() {
                var self = this;
                var value = self.value;
                var displayDate = null;
                if(value) {
                    displayDate = moment(value).tz(momentTz.tz.guess()).format("DD/MM/YYYY hh:mm A");
                }
                return displayDate;
            },
            callback: function(start, end) {
                var self = this;
                if(start) {
                    var currentTz = momentTz.tz.guess();
                    var momentDate = momentTz.tz(start, currentTz).utc();
                    self.emitData(momentDate.isValid() ? momentDate.toDate() : null);
                } else {
                    self.emitData(null);
                }
            },
            emitData: function(momentDate) {
                var self = this;
                EventBus.$emit(self.componentName, { attrName: self.attrName, val: momentDate });
            }
        },
        mounted: function() {
            var self = this;
            self.formatDisplayDate();
            $('input[name="' + self.attrName + '"]').daterangepicker({
                autoUpdateInput: false,
                timePicker: true,
                timePickerIncrement: 5,
                singleDatePicker: true,
                locale: {
                    cancelLabel: 'Clear',
                    format: 'DD/MM/YYYY hh:mm A'
                },
            }, self.callback);

                
            $('input[name="' + self.attrName + '"]').on('apply.daterangepicker', function(ev, picker) {         
                self.callback(picker.startDate._d);  
            });

            $('input[name="' + self.attrName + '"]').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
                self.emitData(null);
            });

            $('input[name="' + self.attrName + '"]').bind("mouseup", function(e){
                var $input = $(this), oldValue = $input.val();
                if (oldValue == "") return;
                setTimeout(function(){
                    var newValue = $input.val();
                    if (newValue == ""){
                        $input.trigger("cleared");
                        self.emitData(null);
                    }
                }, 1);
            });
        }
    }
</script>

<script setup>
import { ref, watch, watchEffect } from 'vue';

const props = defineProps(['disabled', 'saleOptions', 'customLabelStyle', 'value']);
const emit = defineEmits(['input']);
const options = ref([]);

watchEffect(() => options.value = props.saleOptions?.map((e) => ({ ...e })) || []);
watch(
  () => props.value,
  () => options.value?.forEach(
    (option) => option.checked = isOptionSelected(option)
  )
);

function isOptionSelected(option) {
  return !!props.value?.find((selected) => option.value == selected.value);
}

function onChange() {
    emit('input', options.value.filter((e) => e.checked));
}
</script>

<template>
    <div class="advSearch-group advSearch-row-dependent" v-bind:class="{ disabled: disabled }">
        <label :style="customLabelStyle">Sales Status</label>
        <div class="saleStatus"  v-for="(opt,index) in options" :key="index">
            <input type="checkbox" v-bind:value="opt.value" v-model="opt.checked" @change="onChange(opt)" :id="opt.value+'-'+index">
            <label :for="opt.value+'-' + index"><span :style="customLabelStyle">{{ opt.label }}</span></label>
        </div>
    </div>
</template>


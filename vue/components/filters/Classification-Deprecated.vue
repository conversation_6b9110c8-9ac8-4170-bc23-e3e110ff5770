<template>
    <div class="advSearch-group advSearch-row-dependent" id="group-salesClassification" v-bind:class="{disabled: disabled}">
        <label :style="customLabelStyle">Sales Classification</label>
        <span class="fieldTwo">
            <span class="multiselect-native-select classification-parent">
                <select id="classification" class="classification monarch-multiselect" multiple="multiple">
                    <option id="all" value="all">Select All</option>
                    <option id="marketSales" value="marketSales" selected="selected">Market Sales</option>
                    <option id="salesToCheck" value="salesToCheck">Sales to Check</option>
                    <optgroup class="saleType" label="Sale Type">
                        <option class="salesToCheck all" value="0-M">M - Multi</option>
                        <option class="salesToCheck all" value="0-P">P - Part</option>
                        <option class="marketSales salesToCheck all" selected="selected" value="0-S">S - Whole</option>
                    </optgroup>
                    <optgroup class="saleTenure" label="Sale Tenure">
                        <option class="marketSales salesToCheck all" selected="selected" value="1-1">1 - Freehold</option>
                        <option class="all" value="1-2">2 - Leasehold</option>
                        <option class="all" value="1-3">3 - Part Interest</option>
                        <option class="all" value="1-4">4 - Other</option>
                    </optgroup>
                    <optgroup class="priceValueRel" label="Price Value Relationship">
                        <option class="marketSales all" selected="selected" value="2-1">1 - Arm's-length</option>
                        <option class="marketSales salesToCheck all" selected="selected" value="2-2">2 - Review required</option>
                        <option class="all" value="2-3">3 - Non arm's-length</option>
                    </optgroup>
                </select>
            </span>
        </span>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';

    export default {
        props: ['disabled', 'customLabelStyle'],
        methods: {
            onSelect: function(optGroups, checked, selected) {
                if (checked) {
                    $.each(optGroups, function (index, value) {
                        $('.classification').multiselect('deselect', value, true);
                    });
                    $.each($('.' + selected), function (index, value) {
                        $('.classification').multiselect('select', $(value).val());
                    });
                } else {
                    $.each($('.' + selected), function (index, value) {
                        $('.classification').multiselect('deselect', $(value).val());
                    });
                }
            }
        },
        mounted: function() {
            var self = this;
            $('.classification').multiselect({
                nonSelectedText: 'Select...',
                nSelectedText: 'selected',
                filterPlaceholder: 'Filter List...',
                allSelectedText: 'All Selected',
                numberDisplayed: 1,
                maxHeight: 500,
                multiple: true,
                onChange: function(element, checked) {
                    var elementValue = element.val();
                    if(elementValue == "all") {
                        self.onSelect(["marketSales", "salesToCheck"], checked, elementValue);
                    } else if(elementValue == "marketSales") {
                        self.onSelect(["all", "salesToCheck"], checked, elementValue);
                    } else if(elementValue == "salesToCheck") {
                        self.onSelect(["marketSales", "all"], checked, elementValue);
                    }
                    var saleTypeCodes = $('.saleType option:selected').map(function() { return this.value[this.value.length-1]; }).get();
                    var saleTenureCodes = $('.saleTenure option:selected').map(function() { return this.value[this.value.length-1]; }).get();
                    var priceValueRelationshipCodes = $('.priceValueRel option:selected').map(function() { return this.value[this.value.length-1]; }).get();
                    EventBus.$emit('classificationCriteria', {
                        saleTypeCodes: saleTypeCodes.length == 0 ? null : saleTypeCodes,
                        saleTenureCodes: saleTenureCodes.length == 0 ? null : saleTenureCodes,
                        priceValueRelationshipCodes: priceValueRelationshipCodes.length == 0 ? null : priceValueRelationshipCodes
                    });
                }
            });
            $('.classification-parent').find( "ul" ).css('margin-top', '-80px');
            $('.classification-parent').find( "ul" ).css('max-height', '340px');
        }
    }
</script>

<template xmlns:v-on="http://www.w3.org/1999/xhtml">
    <div class="salesGroup-wrapper salesGroupForm" id="salesGroupsAndRollsWrapperDiv" v-if="dispaySalesGroups">
        <div v-for="ta in territorialAuthorities" :class="taClass('salesGroup-ta salesGroup-ta', ta.code)">
            <div class="advSearch-group noMargin divider">
                <div class="salesGroup-taname">
                    <span>
                        <input v-on:click="onChange(taClass('ta',ta.code), taClass('ta',ta.code), null)"
                               class="taCheckbox"
                               :data-group="taClass('ta',ta.code)"
                               type="checkbox"
                               :data-ta="ta.code"
                               :id="taClass('ta',ta.code)">
                        <label :for="taClass('ta',ta.code)"><span>{{ ta.formattedCode }}</span>{{ ta.name }}</label>
                    </span>
                </div>
            </div>
            <div v-for="sg in ta.salesGroups" class="advSearch-group noMargin">
                <div class="salesGroup-name">
                <span>
                    <input :class="sgClass('sgCheckbox ','ta', ta.code)"
                           :data-group="sgClass('sg',ta.code,sg.code)"
                           :data-parent="taClass('ta',ta.code)"
                           :data-ta="ta.code"
                           type="checkbox"
                           :id="sgClass('sg',ta.code,sg.code)"
                           v-on:click="onChange(sgClass('sg',ta.code,sg.code), sgClass('sg',ta.code,sg.code), taClass('ta',ta.code))">
                    <label :for="sgClass('sg',ta.code,sg.code)">{{sg.salesGroupNumber}} - {{sg.description}}</label>
                </span>
                </div>
                <fieldset class="rollValues">
                    <legend>{{sg.description}}</legend>
                    <span  v-for="ro in sg.salesGroupRolls" class="tag">
                        <input :class="'rnCheckBox ' + taClass('ta',ta.code) + ' ' + sgClass('sg',ta.code,sg.code)"
                               :data-parent="sgClass('sg',ta.code,sg.code)"
                               :data-ta="ta.code"
                               :data-roll="ro.rollNumber"
                               type="checkbox"
                               :id="'rn' + sgClass(ta.code, sg.code, ro.rollNumber)"
                               v-on:click="onChange('rn' + sgClass(ta.code, sg.code, ro.rollNumber), null, sgClass('sg',ta.code,sg.code))">
                        <label :for="'rn' + sgClass(ta.code, sg.code, ro.rollNumber)">{{ro.rollNumber}}</label>
                    </span>
                </fieldset>
            </div>
        </div>
    </div>
</template>
<script>
    import {EventBus} from '../../EventBus.js';
    import { store } from '../../DataStore.js';

    export default {
        data: function() {
            return {
                territorialAuthorities: [],
                checked: [],
                dispaySalesGroups: false
            }
        },
        methods: {
            taClass: function(ent, id) {
                var value = ent + (id.length == 1 ? '0' + id : id);
                return value
            },
            sgClass: function(ent, id1, id2) {
                var value = ent + (id1.length == 1 ? '0' + id1 : id1) + (id2.length == 1 ? '0' + id2 : id2);
                return value
            },
            taCodeSalesGroupNumberFilter: function (code, taList, taCodeSalesGroupNumber) {
                var self = this;

                var data = [];

                for(var i= 0, len = taList.length; i < len; i++){
                    if(taList[i].taCodeSalesGroupNumber === taCodeSalesGroupNumber){
                        data.push(taList[i]);
                    }
                }

                var rollwithSales = [];

                for(var i= 0, len = data.length; i < len; i++){
                    var text = {};
                    text.rollNumber = data[i].rollNumber;
                    text.saleGroupId = data[i].saleGroupId;
                    rollwithSales.push(text);
                }

                var element = data[0];

                var result = {};
                result.code = code;
                result.description = element.description;
                result.salesGroupNumber = element.salesGroupNumber;
                result.salesGroupRolls = rollwithSales;
                result.taCode = element.taCode;
                result.taCodeSalesGroupNumber = element.taCodeSalesGroupNumber;

                return result;
            },
            salesGroupFilter: function (salesGroups, taCode) {
                var self = this;
                var result = [];
                var taList = [];
                var temp = [];

                for( var i= 0, len = salesGroups.length; i < len; i++) {
                    if( salesGroups[i].taCode === taCode ) {
                        taList.push( salesGroups[i] );
                        temp.push(salesGroups[i].taCodeSalesGroupNumber)
                    }
                }
                var tcsg = Array.from(new Set(temp));

                for(var i= 0, len = tcsg.length; i < len; i++){
                    result.push(self.taCodeSalesGroupNumberFilter(i, taList, tcsg[i]));
                }
                return result;
            },
            getSalesGroups: function(taResponse, taCodes) {
                var self = this;
                $.ajax({
                    type: "GET",
                    cache: false,
                    url: jsRoutes.controllers.ReferenceData.displaySalesGroups(taCodes).url,
                    success: function (response) {
                        var sgs = response;
                        var territorialAuthorities = [];
                        for(var obj in taResponse) {
                            var taName = taResponse[obj];
                            var taCode = obj < 10 ? '0' + obj : obj;
                            territorialAuthorities.push({formattedCode: taCode, name: taName, code: obj, salesGroups: self.salesGroupFilter(sgs, taCode)});
                        }
                        self.territorialAuthorities = territorialAuthorities;
                        self.dispaySalesGroups = true;
                    },
                    error: function (response) {
                        console.log(response);
                    }
                });
            },
            onChange: function(me, group, parent) {
                var checked =  $('#'+me).prop('checked');
                if(group) {
                    $('.' + group ).prop("checked", checked);
                }
                if(parent) {
                    if(checked == false) {
                        $('#' + parent ).prop("checked", false);
                        var taParent = $('#' + parent ).data('parent');
                        if(taParent) {
                            $('#' + taParent).prop("checked", false);
                        }
                    } else {
                        var checked = $("." + parent  + ":checked").length;
                        var total = $("." + parent).length;
                        var taParent = $('#' + parent ).data('parent');
                        if(total == checked) {
                            $('#' + parent ).prop("checked", true);
                        }
                        if(taParent) {
                            var taChecked = $("." + taParent  + ":checked").length;
                            var taTotal = $("." + taParent).length;
                            if(taTotal == taChecked) {
                                $('#' + taParent ).prop("checked", true);
                            }
                        }
                    }
                }
            }
        },
        mounted: function() {
            const self =this;
            $.ajax({
                type: "GET",
                cache: false,
                url: jsRoutes.controllers.Application.fetchTerritorialAuthorities().url,
                success: function (response) {
                    var jsonData = response;
                    var taCodes = [];
                    for(var obj in jsonData){
                        taCodes.push( obj < 10 ? '0' + obj : obj);
                    }
                    self.getSalesGroups(jsonData, taCodes.join(','));
                },
                error: function (response) {
                    console.log(response);
                }
            });

        }
    }
</script>
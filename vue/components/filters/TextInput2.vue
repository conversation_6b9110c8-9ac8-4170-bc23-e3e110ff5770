<template>
    <div class="advSearch-group" v-bind:class="[iconClass, errorMessage && errorMessage != '' ? 'valError' : '']">
        <div :title="title">
            <label :id="id+'label'" v-html="label"></label>
        </div>
        <span v-if="readOnly && readOnly == 'true'">
            <input readOnly tabindex="-1" :id="id" :min="min" :max="max" :data-cy="dataCy" :placeholder="placeHolder" :minlength="minlength" :maxlength="maxlength" step="any" class="advSearch-text integerOnly" v-if="fieldType=='number'" @change="notifyComponent()" type="number" v-model="val" data-type="number">
            <input readOnly tabindex="-1" :id="id" :maxlength="maxlength" :data-cy="dataCy" :placeholder="placeHolder" class="advSearch-text" v-if="fieldType=='text' || fieldType == 'date' || fieldType == 'price'" type="text" @change="notifyComponent()" v-model="val">
        </span>
        <span v-if="!readOnly || (readOnly && readOnly == 'false')">
            <input :id="id" :min="min" :max="max" :data-cy="dataCy" :placeholder="placeHolder" :minlength="minlength" :maxlength="maxlength" step="any" class="advSearch-text integerOnly" v-if="fieldType=='number'" @change="notifyComponent()" type="number" v-model="val" data-type="number">
            <input :id="id" :maxlength="maxlength" :data-cy="dataCy" :placeholder="placeHolder" class="advSearch-text" v-if="fieldType=='text' || fieldType == 'date' || fieldType == 'price'" type="text" @change="notifyComponent()" v-model="val">
        </span>
        <div :class="['valMessage', attrName]" v-if="errorMessage"><label>{{ errorMessage }}</label></div>
    </div>
</template>
<script>
    import {EventBus} from '../../EventBus.js';
    import numeral from 'numeral';
    import moment from 'moment';

    export default {
        props: ['dataCy', 'title', 'iconClass', 'label', 'objKey', 'componentName', 'attrName', 'parentAttrName', 'fieldType', 'currVal', 'format', 'placeHolder', 'id', 'min', 'max', 'minlength', 'maxlength', 'decimal', 'errorMsg', 'readOnly', 'allowFuture'],
        data: function() {
            return {
                val: '',
                raw: '',
                errorMessage: ""
            }
        },
        watch: {
            currVal(newValue) {
                this.val = newValue;
            }
        },
        methods: {
            validate(){
                const self = this
                if(self.fieldType=='number' && self.val && self.val.trim() !== '') {
                    if (!$.isNumeric(self.val)) {
                        self.errorMessage = "Invalid Input. Only numbers allowed.";
                        self.val = '';
                        return false;
                    }
                    if (self.val < 0) {
                        self.errorMessage = "Input value cannot be negative.";
                        self.val = '';
                        return false;
                    }
                    if (!self.decimal) {
                        var indexOfPeriod = self.val.indexOf('.')
                        if (indexOfPeriod >= 0) {
                            self.errorMessage = "Only Positive Whole numbers allowed.";
                            self.val = '';
                            return false;
                        }
                    }
                    if (self.decimal) {
                        var periods = self.val.split('.')
                        if (periods.length > 2) {
                            self.errorMessage = "Invalid Number format.";
                            self.val = '';
                            return false;
                        }
                        self.val = parseFloat(self.val).toFixed(parseInt(self.decimal))
                        self.errorMessage = ''
                        return true;
                    }
                    if (parseInt(self.maxlength) < self.val.length || parseInt(self.minlength) > self.val.length){
                        self.errorMessage = "Invalid Number length.";
                        self.val = '';
                        return false;
                    }
                    self.val = parseInt(self.val);
                    self.errorMessage = '';
                    return true;
                }
                return true;
            },
            notifyComponent(){
                const self = this;
                var obj = {};
                if (!self.validate()) {
                    console.log('DID NOT VALIDATE')
                    return;
                }
                if (self.objKey) {
                    self.errorMessage = "";
                    obj.attrName = self.attrName;
                    obj.parentAttrName = self.parentAttrName;
                    obj.val = self.val;
                    obj.raw = self.raw;
                    obj.fieldType = self.fieldType;
                    obj.key = self.objKey;
                    EventBus.$emit('notify-simple-nested-' + self.componentName, obj);
                    return;
                }
                self.errorMessage = "";
                obj.attrName = self.attrName;
                obj.parentAttrName = self.parentAttrName;
                obj.val = self.val;
                obj.fieldType = self.fieldType;
                obj.raw = self.raw;
                EventBus.$emit('notify-simple-' + self.componentName, obj);
            }
        },
        created() {
            this.val = this.currVal
            if (this.errorMsg) {
                this.errorMessage = this.errorMsg
            }
        }
    }
</script>

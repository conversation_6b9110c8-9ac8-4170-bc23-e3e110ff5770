<template>
    <div class="typeahead__field simpleSearch">
        <span class="typeahead__query">
        <input id="typeAhead" class="js-typeahead search-box" name="searchQuery" type="search" value="" placeholder="Search" autocapitalize="off" autocomplete="off" autocorrect="off">
        </span>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import typeahead from '../../jquery.typeahead.js';
    import commonUtils from '../../utils/CommonUtils';

    export default {
        mixins: [commonUtils],
        data: function() {
            return {
                taCodes: [],
                enterHit: true
            }
        },
        mounted: function() {
            var self = this;
            EventBus.$on('taCodes', function(taCodes) {
                self.taCodes = taCodes;
            });
            $('.search-box').keypress(function(e){
                if(e.which === 13){ // Enter
                    var query = $('.search-box').val();
                    var eventObj = {};
                    eventObj.searchParams = {basicSearchString: self.isHtml(query) ? null : query, taCodes: (self.taCodes.length === 0 || self.isHtml(query)) ? [0] : self.taCodes,
                        sort: ['COMPARABILITY_SCORE'],
                        order: 'asc'};
                    eventObj.searchType = 'property-search';
                    EventBus.$emit('display-content', eventObj);
                    $('.typeahead__result').hide();
                    self.enterHit = true;
                } else {
                    self.enterHit = false;
                }
            });
            $('.search-box').mousedown(function() {
                $(".js-typeahead").eq(0).val($('.search-box').val()).trigger("input");
                self.enterHit = false;
            });
            $.typeahead({
                input: '.js-typeahead',
                dynamic: true,
                delay: 1000,
                maxItem: 0,
                emptyTemplate: function (query) {
                    return "No results found for \"" + self.sanitize(query) + "\"";
                },
                filter: false,
                template: function (query, item) {
                    return '<i class="material-icons md-dark md-18">&#xE0C8;</i>' +
                            '<span class="listBox-address">{{address}}</span>' +
                            '<span class="listBox-reference">{{valRef}}</span>' +
                            '<span class="listBox-category">{{category}}</span>' +
                            '<span class="listBox-category">{{qpid}}</span>' +
                            '<span class="listBox-category">{{certificateOfTitles}}</span>'
                },
                source: {
                    "property": {
                        display: ['address', 'valRef', 'category', 'qpid', 'certificateOfTitles'],
                        ajax: function (query) {
                            return {
                                type: "POST",
                                contentType: "application/json; charset=utf-8",
                                url: jsRoutes.controllers.PropertyController.displayTypeAheadResult().url,
                                dataType: "json",
                                cache: false,
                                data: JSON.stringify({
                                    basicSearchString: self.isHtml(query) ? null : query,
                                    taCodes: (self.taCodes.length === 0 || self.isHtml(query)) ? [0] : self.taCodes,
                                    sort: ['COMPARABILITY_SCORE'],
                                    order: 'asc'
                                }),
                                statusCode: {
                                    401: function () {
                                        self.errorHandler({status: 401});
                                    }
                                }
                            }
                        }
                    }
                },
                callback: {
                    onNavigateAfter: function (node, lis, a, item, query, event) {
                        $('.js-typeahead').val(item.address);
                    },
                    onLayoutBuiltBefore: function (node, query, result, resultHtmlList) {
                        if($('.typeahead__result').css('display') == 'none' && self.enterHit == false) {
                            $('.typeahead__result').slideToggle();
                        }
                        if(self.enterHit) {
                            $('.typeahead__result').hide();
                        }
                        return resultHtmlList;
                    },
                    onClick: function(node, a, item, ev) {
                        var event = {};
                        event.searchType = 'master-details';
                        event.propertyId = item.qpid;
                        EventBus.$emit('display-content', event);
                    }
                }
            });
        }
    }
</script>

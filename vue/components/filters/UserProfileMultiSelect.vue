<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="advSearch-group thirtythreePct icons8-edit-user-male" v-bind:class="{iconClass: true, valError: hasError}">
        <label>{{ title }}</label>
        <!--<select class="advSearch-singleselect monarch-multiselect" v-bind:class="selectClass" ref="multiSelect" v-model="value.id"></select>-->
        <multi-select
                :options="options"
                title=""
                filter-class="advSearch-singleselect"
                showLabel="false"
                @select="onChange"
                limit-to-show-label="1"
                limit-to-show-val="6"
                :field-name="filterId"
                enable-filtering = "true"
                span-class="multiselect-native-select"
                :value="defaultValues"
                :button-title="buttonTitle"
                multiple="false"
        >
        </multi-select>
        <div class="valMessage"><label>{{ errorMessage }}</label></div>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import MultiSelect from '../common/MultiSelect.vue'
    import { store } from '../../DataStore';
    export default {
        props: ['selectClass', 'iconClass', 'title', 'value', 'url', 'hasError', 'errorMessage', 'unselect', 'filterId'],
        components: {
            MultiSelect
        },
        methods: {
            onChange: function(input) {
                const self = this;
                var selected = [];
                $(Object.keys(input.values)).each(function(index, key){
                    selected.push(input.values[key].value);
                });
                if(typeof selected[0] != 'undefined' && selected[0] != "") {
                    self.hasError == false;
                }
                self.$emit('input', self.sourceData.filter(function(e) { return e.id == selected[0]; })[0]);
                self.manual = true;
                self.buttonTitle = self.buttonText(input.values, true);
            },
            buttonText: function(values, isObject) {
                const self = this;
                var numberOfOptions = Object.keys(values).length;
                if (numberOfOptions === 0) {
                    return '';
                }else if(numberOfOptions == 1){
                    var label = "";
                    if(isObject) {
                        $.each(Object.keys(values), function(index, key) {
                            label = values[key].label;
                        });
                    }else {
                        try{
                            if($.type(label) == "undefined" || label == "") {
                                for(var i in self.options){
                                    if(self.options[i].value == values[0] || self.options[i].label == values[0]) {
                                        label = self.options[i].label;
                                        break;
                                    }
                                }
                            }
                        }catch(e){
                            //Suppress errors, page must still work even if there is an error in above logic
                        }
                    }
                    return label;
                }
                else{
                    if (numberOfOptions >= 1) {
                        if(numberOfOptions === self.options.length){
                            return 'All Selected ('+numberOfOptions+')';
                        }
                        else {
                            return numberOfOptions + ' selected';
                        }
                    }
                }
            }
        },
        watch: {
            value: function(newVal, oldVal) {
                this.defaultValues = newVal ? [newVal.id] : [];
                EventBus.$emit("multiselect-select-values-"+this.filterId, this.defaultValues);
                this.buttonTitle = this.buttonText(this.defaultValues, false);
            }
        },
        data: function() {
            return {
                options: [],
                sourceData: [],
                selected: { id: "" },
                manualChange: false,
                buttonTitle: "",
                defaultValues: []
            }
        },
        mounted: function() {
            var self = this;
            $.ajax({
                type: "GET",
                url: self.url,
                cache: false,
                async: false,
                success: function (response) {
                    var options = [];
                    self.sourceData = response;
                    if(!self.unselect) {
                        options.push({label: '', value: '', title: '', id: ''});
                    }
                    $.each(response, function() {
                        options.push({label: this.name, value: this.id, id: this.id});
                    });
                    self.options = options;
                    //$("." + self.selectClass).multiselect('dataprovider', self.options);
                    var classificationToStore = {}
                    classificationToStore.key = self.filterId
                    classificationToStore.data = response
                    self.$store.commit("addClassification", classificationToStore);
                },
                error: function (response) {
                    console.log(response);
                }
            });
        },
        updated: function() {
            var self = this;
            if(self.manual) {
                self.manual = false;
            } else {
                $('.'+self.selectClass).multiselect('refresh');
            }
        }
    }
</script>

<template>
    <div class="advSearch-group" v-bind:class="[iconClass, errorMessage && errorMessage != '' ? 'valError' : '']">
        <label :id="id+'label'" v-html="label"></label>
        <span v-if="readOnly && readOnly == 'true'">
            <input readOnly tabindex="-1" :id="id" :min="min" :max="max" :maxlength="maxlength" step="any" class="advSearch-text integerOnly" v-if="fieldType=='number'" @change="notifyComponent()" type="number" v-model="val" data-type="number">
            <input readOnly tabindex="-1" :id="id" :maxlength="maxlength" :placeholder="placeHolder" class="advSearch-text" v-if="fieldType=='text' || fieldType == 'date' || fieldType == 'price'" type="text" @change="notifyComponent()" v-model="val">
        </span>
        <span v-if="!readOnly || (readOnly && readOnly == 'false')">
            <input :id="id" :min="min" :max="max" :maxlength="maxlength" step="any" class="advSearch-text integerOnly" v-if="fieldType=='number'" @change="notifyComponent()" type="number" v-model="val" data-type="number">
            <input :id="id" :maxlength="maxlength" :placeholder="placeHolder" class="advSearch-text" v-if="fieldType=='text' || fieldType == 'date' || fieldType == 'price'" type="text" @change="notifyComponent()" v-model="val">
        </span>
        <div :class="['valMessage', attrName]" v-if="errorMessage"><label>{{ errorMessage }}</label></div>
    </div>
</template>
<script>
    import {EventBus} from '../../EventBus.js';
    import numeral from 'numeral';
    import moment from 'moment';

    export default {
        props: ['iconClass', 'label', 'objKey', 'componentName', 'attrName', 'parentAttrName', 'fieldType', 'currVal', 'format', 'placeHolder', 'id', 'min', 'max', 'maxlength', 'decimal', 'errorMsg', 'readOnly', 'allowFuture'],
        data: function() {
            return {
                val: '',
                raw: '',
                errorMessage: ""
            }
        },
        watch: {
            currVal(newValue) {
                this.val = newValue;
            }
        },
        methods: {
            validate(){
                const self = this
                if(self.fieldType=='number' && self.val && self.val.trim() !== '') {
                    if (!$.isNumeric(self.val)) {
                        self.errorMessage = "Invalid Input. Only numbers allowed.";
                        self.val = '';
                        return false;
                    }
                    else if (self.val < 0) {
                        self.errorMessage = "Input value cannot be negative.";
                        self.val = '';
                        return false;
                    }
                    else if (!self.decimal) {
                        var indexOfPeriod = self.val.indexOf('.')
                        if (indexOfPeriod >= 0) {
                            self.errorMessage = "Only Positive Whole numbers allowed.";
                            self.val = '';
                            return false;
                        }
                    }
                    else if (self.decimal) {
                        var periods = self.val.split('.')
                        if (periods.length > 2) {
                            self.errorMessage = "Invalid Number format.";
                            self.val = '';
                            return false;
                        }
                        else {
                            if (self.maxlength && self.val.indexOf('.') == (self.maxlength-1)) {
                                self.errorMessage = "Invalid Number format.";
                                self.val = '';
                                return false;
                            }
                            self.val = parseFloat(self.val).toFixed(parseInt(self.decimal))
                            self.errorMessage = ''
                            return true;
                        }
                    }
                    self.errorMessage = ''
                    return true;
                }
                else if (self.fieldType == 'date' && self.val && self.val.trim() !== '') {
                    if (!moment(self.val, self.format, true).isValid()) {
                        self.errorMessage = "Invalid Date Format";
                        self.val = '';
                        return false;
                    } else if (self.allowFuture !== true && moment().isBefore(moment(self.val, self.format))) {
                        self.errorMessage = "Date cannot be in the future.";
                        self.val = '';
                        return false;
                    } else {
                        var momentDate = moment.utc(self.val, self.format);
                        if(momentDate.isValid()) {
                            self.raw = momentDate.toDate();
                        }
                    }
                    return true;
                }
                else if (self.fieldType == 'price' && self.val && self.val.trim() !== '') {
                    if(self.val.slice(-1) == '%') {
                        self.val =  self.val.substring(0, self.val.length - 1);
                    }
                    var price = numeral(self.val).value();
                    if(!price) {
                        self.errorMessage = "Invalid Input.";
                        self.val = '';
                        return false;
                    } else {
                        self.val =  numeral(price).format(self.format);
                        self.raw = price;
                    }
                    self.errorMessage = ''
                    return true;
                }
                return true;
            },
            notifyComponent(){
                const self = this;
                var obj = {};
                if (!self.validate()) {
                    console.log('DID NOT VALIDATE')
                    return;
                }
                if (self.objKey) {
                    self.errorMessage = "";
                    obj.attrName = self.attrName;
                    obj.parentAttrName = self.parentAttrName;
                    obj.val = self.val;
                    obj.raw = self.raw;
                    obj.fieldType = self.fieldType;
                    obj.key = self.objKey;
                    EventBus.$emit('notify-simple-nested-' + self.componentName, obj);
                }
                else {
                    self.errorMessage = "";
                    obj.attrName = self.attrName;
                    obj.parentAttrName = self.parentAttrName;
                    obj.val = self.val;
                    obj.fieldType = self.fieldType;
                    obj.raw = self.raw;
                    EventBus.$emit('notify-simple-' + self.componentName, obj);
                }
            }
        },
        created() {
            this.val = this.currVal
            if (this.errorMsg) {
                this.errorMessage = this.errorMsg
            }
        }
    }
</script>

<template>
    <div data-cy="territorialAuthoritiesDiv" id="territorialAuthoritiesDiv" class="advSearch-group">
        <label :style="labelStyle()">Territorial Authorities</label>
        <span class="fieldTwo">
            <select :class="setClass()" multiple="multiple" data-cy="ta-select">
                <option v-for="ta in territorialAuthorities" :value="ta.value">{{ ta.label }}</option>
            </select>
        </span>
    </div>
</template>
<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js';

    export default {
        props: ['showLabel', 'taId', 'validationError', 'customLabelStyle'],
        methods: {
            labelStyle: function () {
                var style = "display: block;";
                if(this.showLabel == "false") {
                    style = "display: none;";
                }
                if (this.customLabelStyle) {
                    style = {
                      ...style,
                      ...this.customLabelStyle
                    };
                }
                return style;
            },
            setClass: function () {
                return `advSearch-multiselect monarch-multiselect advSearch-multiselect-${this.taId}`;
            }
        },
        data: function() {
            return {
                territorialAuthorities: [],
                taUserPurposelySelectedNone: false,
                totalTAs: 0,
            }
        },
        watch: {
            validationError(newVal, oldVal) {
                if (newVal === true) {
                    $('button.multiselect.dropdown-toggle.btn').addClass('validation-error');
                    return
                }
                $('button.multiselect.dropdown-toggle.btn').removeClass('validation-error');
            },
            taUserPurposelySelectedNone(newVal) {
                localStorage.setItem(this.taNoneStorageString, JSON.stringify(newVal));
            }
        },
        computed: {
            ...mapState('userData', [
                'userId',
                'isInternalUser',
                'allowedTACodes',
            ]),
            taNoneStorageString() {
                return (this.userId ?? 'Unknown User') + '-ta-none';
            },
            taCodesStorageString() {
                return (this.userId ?? 'Unknown User') + '-ta-codes';
            },
        },
        created: function() {
            if (this.$route.name === 'roll-maintenance') {
                this.taUserPurposelySelectedNone = JSON.parse(localStorage.getItem(this.taNoneStorageString)) || false;
            }
        },
        mounted: function() {
            var self = this;
            $('#territorialAuthoritiesDiv').off("click").click(function (evt) {
                $('.typeahead__result').hide();
                $('.multiselect-search').focus(); //todo
            });



            $('.advSearch-multiselect-' + self.taId).multiselect({
                nonSelectedText: 'Select Authorities',
                nSelectedText: 'selected',
                enableFiltering: true,
                filterPlaceholder: 'Filter List...',
                enableFullValueFiltering: false,
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                allSelectedText: 'All Selected',
                maxHeight: 400,
                numberDisplayed: 1,
                onChange: function(element, checked) {
                    var tas = $('.advSearch-multiselect-' + self.taId + ' option:selected');
                    var selected = [];
                    $(tas).each(function(index, ta){
                        selected.push(parseInt(ta.value));
                    });
                    EventBus.$emit('taCodes', selected);
                    self.taUserPurposelySelectedNone = selected.length === 0;
                    EventBus.$emit('taCodesSelectAll', selected.length === self.totalTAs);
                    localStorage.setItem(self.taCodesStorageString, JSON.stringify(selected));
                    $.each($('.advSearch-multiselect'), function( index, value ) {
                        if(!$(value).hasClass('advSearch-multiselect-' + self.taId)) {
                            $(value).val(selected);
                            $(value).multiselect('refresh');
                        }
                    });
                },
                onSelectAll: function(options) {
                    EventBus.$emit('taCodesSelectAll', true);
                },
                buttonText: function(options, select) {
                    var numberOfOptions = select[0].length;
                    if (options.length === 0) {
                        return 'Select Authorities';
                    }
                    else{
                        if (options.length > 1) {
                            if(options.length === numberOfOptions){
                                return 'All TA\'s Selected ('+options.length+')';
                            }
                            else if (options.length >=2 && options.length <= 6) {
                                var selected='';
                                options.each(function() {
                                    var label = $(this).attr('value') < 10 ? '0'+$(this).attr('value') : $(this).attr('value');
                                    selected += label + ', ';
                                });
                                return selected.substr(0, selected.length - 2);
                            }
                            else {
                                return options.length + ' selected';
                            }
                        } else {
                            var selected = '';
                            options.each(function() {
                                var label = ($(this).attr('label') !== undefined) ?
                                    $(this).attr('label'):$(this).html();
                                selected = label;
                            });
                            return selected;
                        }
                    }
                }
            });
            this.$nextTick(function () {
                var m = jsRoutes.controllers.Application.fetchTerritorialAuthorities();
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        var tas = response;
                        var items = [];
                        for(var taCode in tas){
                            var taName = tas[taCode];
                            var formattedTaCode = taCode < 10 ? '0' + taCode : taCode;
                            if (self.isInternalUser || $.inArray(taCode, self.allowedTACodes) >= 0) {
                                items.push({label: formattedTaCode + ' - ' + taName, value: taCode, id: taCode});
                            }
                        }
                        self.totalTAs = Object.keys(tas).length;

                        /* TODO Legacy code that should use reactivity instead of JQuery */
                        $('.advSearch-multiselect-' + self.taId).multiselect('dataprovider', items);
                        var selected = JSON.parse(localStorage.getItem(self.taCodesStorageString));
                        $('.advSearch-multiselect-' + self.taId).multiselect('select', selected);

                        if ((!selected || selected.length == 0) && !self.taUserPurposelySelectedNone) {
                            /* Select all */
                            selected = [];
                            $.each(items, function(index, item) {
                                if(item && item.id) {
                                    selected.push(parseInt(item.id));
                                }
                            });
                            $('.advSearch-multiselect-' + self.taId).multiselect('select', selected);
                        }

                        EventBus.$emit('taCodes', selected);
                    },
                    error: function (response) {
                        console.error(response);
                    }
                });
            });

            if (self.taUserPurposelySelectedNone) {
                $('button.multiselect.dropdown-toggle.btn').addClass('validation-error');
            }

        },
        destroyed() {
            if (this.$route.name !== 'roll-maintenance') {
                localStorage.removeItem(this.taNoneStorageString);
            }
        }
    }
</script>

<style lang="scss">
.validation-error {
    -webkit-box-shadow:inset 0 0 0 .1rem #ea2e2d !important;
    -moz-box-shadow:inset 0 0 0 .1rem #ea2e2d !important;
    box-shadow:inset 0 0 0 .1rem #ea2e2d !important;
}
</style>

<template>
    <div class="advSearch-group" v-bind:class="[iconClass, errorMessage && errorMessage != '' ? 'valError' : '']">
        <div>
            <!-- <label>{{label}}<span><i v-if="refreshMethod" class="material-icons md-dark" @click="refreshDescription()">&#xE5D5;</i></span></label> -->
            <label v-html="label"></label>
            <span><i v-if="refreshMethod" class="material-icons md-dark" @click="refreshDescription()">&#xE5D5;</i></span>
        </div>
        <span><textarea :id="id" class="advSearch-text" :maxlength="maxlength" @change="notifyComponent" v-model="val"></textarea></span>
        <div :class="['valMessage', attrName]"><label v-if="errorMessage.length > 0">{{ errorMessage }}</label></div>
    </div>
</template>
<script>
    import {EventBus} from '../../EventBus.js';

    export default {
        props: ['iconClass', 'label', 'objKey', 'componentName', 'attrName', 'parentAttrName', 'fieldType', 'currVal', 'id', 'maxlength', 'errorMsg', 'refreshMethod'],
        data: function() {
            return {
                val: '',
                errorMessage: ''
            }
        },
        methods: {
            notifyComponent: function(){
                const self = this;
                var obj = {};

                if (self.objKey) {
                    self.errorMessage = "";
                    obj.attrName = self.attrName;
                    obj.parentAttrName = self.parentAttrName;
                    obj.val = self.val;
                    obj.key = self.objKey
                    EventBus.$emit('notify-simple-nested-' + self.componentName, obj);
                }
                else {
                    self.errorMessage = "";
                    obj.attrName = self.attrName;
                    obj.val = self.val;
                    EventBus.$emit('notify-simple-' + self.componentName, obj);
                }
            },
            refreshDescription: function() {
                const self = this;
                var obj = {};
                obj.attrName = self.attrName;
                obj.objKey = self.objKey;
                self.refreshMethod(obj);
            }
        },
        created: function() {
            const self = this
            self.val = self.currVal
            if (self.errorMsg) {
                self.errorMessage = self.errorMsg
                $("."+self.attrName).show();
            }
        }
    }
</script>
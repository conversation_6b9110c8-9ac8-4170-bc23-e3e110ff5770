<template>
    <div id="regionTypeDiv" class="advSearch-group">
        <label>Region</label>
        <multi-select
                select-all="Select all"
                :options="regions"
                title="Select Regions"
                showLabel="false"
                @select="onChange"
                limit-to-show-label="1"
                limit-to-show-val="6"
                field-name="regionType"
                :button-title="buttonTitle"
                all-selected-text="All Regions Selected"
                data-cy="region-type"
        >

        </multi-select>
    </div>
</template>
<script>
    import { mapState, mapGetters } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import MultiSelect from '../common/MultiSelect.vue'

    export default {
        components: {
            MultiSelect
        },
        computed: {
            ...mapState('userData', [
                'isInternalUser',
                'allowedRCCodes',
            ]),
            ...mapGetters([
                'getCategoryClassifications',
            ]),
            regions() {
                const categoryClassifications = this.getCategoryClassifications('RegionType');
                const output = [];
                categoryClassifications.forEach((regionType) => {
                    const regionCode = regionType.code < 10 ? `0${regionType.code}` : regionType.code;
                    if (this.isInternalUser || $.inArray(regionCode, this.allowedRCCodes) >= 0) {
                        output.push({ label: regionType.description, value: regionCode });
                    }
                });
                return output;
            },
        },
        methods: {
            onChange: function(obj){
                var selected = [];
                $(Object.keys(obj.values)).each(function(index, key){
                    //selected.push(parseInt(obj.values[key].value));
                    selected.push(obj.values[key].value); //regioncode is String base in the model
                });
                EventBus.$emit('regionCodes', selected);
                this.buttonTitle = this.buttonText(obj.values);
            },
            buttonText: function(values) {
                const self = this;
                var numberOfOptions = Object.keys(values).length;
                if (numberOfOptions === 0) {
                    return 'Select Regions';
                } else if(numberOfOptions == 1) {
                    var label = "";
                    try{
                        $.each(Object.keys(values), function(index, key) {
                            label = values[key].label;
                        });
                    }catch(e){}
                    return label;
                }
                else{
                    if (numberOfOptions >= 1) {
                        if(numberOfOptions === self.regions.length){
                            return 'All Regions Selected ('+numberOfOptions+')';
                        }
                        else {
                            return numberOfOptions + ' selected';
                        }
                    }
                }
            }
        },
        data: function() {
            return {
                multiSelect: null,
                buttonTitle: ""
            }
        },
    }
</script>

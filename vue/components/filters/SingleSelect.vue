<template>
    <div v-bind:id="id" class="advSearch-group noMargin advSearch-row-dependent disabled">
        <label>{{ title }}</label>
        <span class="fieldTwo noMargin">
            <multi-select
                    select-all="Select all"
                    :options="options"
                    title="Select..."
                    showLabel="false"
                    taId="quickSearchTa"
                    @select="onChange"
                    limit-to-show-label="1"
                    limit-to-show-val="6"
                    :field-name="selectid"
                    multiple="false"
                    :button-title="buttonTitle"
            >

            </multi-select>
        </span>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import MultiSelect from '../common/MultiSelect.vue';

    export default {
        components: {
            MultiSelect
        },
        data: function() {
            return {
                buttonTitle: ""
            }
        },
        props: ['id', 'selectid', 'title', 'event', 'options'],
        methods: {
            onChange: function(obj) {
                var message = {};
                $(Object.keys(obj.values)).each(function(index, key){
                    message.val = obj.values[key].value;
                    message.text = obj.values[key].label;
                });
                EventBus.$emit(this.event, message);
                this.buttonTitle = this.buttonText(obj.values);
            },
            buttonText: function(values) {
                const self = this;
                var numberOfOptions = Object.keys(values).length;
                if (numberOfOptions === 0) {
                    return 'Select...';
                }
                else{
                    if (numberOfOptions == 1) {
                        return values[Object.keys(values)[0]].label;
                    }
                }
            }
        },
    }
</script>

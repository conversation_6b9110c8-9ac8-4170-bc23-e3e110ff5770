<script setup>

import MaterialIcon from 'Common/MaterialIcon.vue';
import { ref } from 'vue';

const props = defineProps(['icon', 'showOn', 'showOnHover']);
const hovered = ref(false);

function setHovered(value) {
    hovered.value = value;
}
</script>

<template>
    <div class="badge qv-flex-row qv-justify-center qv-align-center" @mouseenter="(e) => setHovered(true)" @mouseleave="(e) => setHovered(false)">
        <div v-if="icon" class="qv-flex-column">
            <MaterialIcon :icon="icon" class="badge-icon" />
        </div>
        <transition v-if="showOn != null || showOnHover != null" name="expand-right">
            <div v-show="showOn || hovered" class="expand-right qv-pr-1">
                <slot />
            </div>
        </transition>
        <slot v-else />
    </div>
</template>

<style lang="scss">
.expand-right {
    width: auto;
    overflow: hidden;
}

.expand-right-enter-active, .expand-right-leave-active {
    transition: max-width 0.3s ease-in-out;
}

.expand-right-enter-to, .expand-right-leave {
    max-width: 300px;
}

.expand-right-enter, .expand-right-leave-to {
    max-width: 0;
}

.badge {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--color-blue-700);
    line-height: 1.9;
    text-align: center;
    background-color: var(--color-lightblue-300);
    border: .1rem solid var(--color-lightblue-500);
    border-radius: .3rem;
    padding: 0rem 0.2rem;
    width: max-content;
    min-height: 2rem;
    height: 2rem;

    &.red {
        color: white;
        background-color: var(--color-red-400);
        border-color: var(--color-red-400);
    }

    &.gray {
        color: white;
        background-color: grey;
        border-color: grey;
    }

    &.warning {
        color: white;
        background-color: var(--color-orange-500);
        border-color: var(--color-darkorange-500);
    }
}

.badge-icon {
    font-size: 1.4rem;
}

</style>

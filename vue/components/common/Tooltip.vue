<template>
    <div
        class="tooltip-container"
        :class="[tooltipClass, show ? 'tooltip-container-show' : '']"
    >
        <slot />
        <div
            v-if="text"
            class="tooltip"
        >
            <span
                class="text"
            >
                {{ text }}
            </span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'tooltip',
    props: {
        text: {
            type: String,
            required: false,
        },
        show: {
            type: Boolean,
            required: false,
            default: false,
        },
        displayMode: {
            type: String,
            required: false,
        },
        border: {
          type: Boolean,
          required: false,
        }
    },
    computed: {
        tooltipClass() {
            return {
                'tooltip-error': this.displayMode === 'error',
                'tooltip-warning': this.displayMode === 'warning',
                'tooltip-border': this.border === true && this.text,
            };
        },
    },
};
</script>

<style scoped>
.tooltip-container {
    position: relative;
}

.tooltip-container:hover .tooltip {
    opacity: 1;
}

.tooltip-container-show .tooltip {
    opacity: 1;
}

.tooltip-border {
  border-color: var(--color-red-400);
  border-width: 2px;
  border-radius: 5px;
  border-style: solid;
}

.tooltip {
    color: #ffffff;
    text-align: center;
    padding: 5px 5px;
    border-radius: 5px;

    min-width: 150px;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-10px);

    opacity: 0;
    transition: opacity 0.2s;

    position: absolute;
    z-index: 100;

    background: var(--color-red-400);
    pointer-events: none;
}

.tooltip-error .tooltip {
    background: var(--color-red-400);
}

.tooltip-warning .tooltip {
    background: var(--color-orange-200);
}

.text::after {
    content: " ";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--color-red-400) transparent transparent transparent;
}

.tooltip-error .text::after {
    border-color: var(--color-red-400) transparent transparent transparent;
}

.tooltip-warning .text::after {
    border-color: var(--color-orange-200) transparent transparent transparent;
}

</style>

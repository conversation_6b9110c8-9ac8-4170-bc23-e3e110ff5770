<template>
    <div>
        <expander v-if="component.expandable" class="righty" v-model="expanded" />
        <h1 class="title">
            {{component.title}}
        </h1>
        <component 
            v-bind:is="component.name" 
            v-bind="objects" 
            v-show="expanded" 
            v-on="$listeners"
        ></component>
    </div>
</template>

<script>
import Vue from 'vue'

export default {
    components: {
        'expander': () => import(/* webpackChunkName: "ProcessWrapper" */ '../../common/Expander.vue'),
    },
    props: {
        objects: {
            type: Object,
            required: true,
        },
        component: {
            type: Object,
            required: true,
        },
    },
    data(){
        return{
            expanded: this.component.expanded
        }
    },
    methods: {
        loadComponent() {
            this.$options.components[this.component.name] = () => import(/* webpackChunkName: "ProcessWrapper" */ `../../../components${this.objects.componentDirectory}/${this.component.name}.vue`);
        },
    },
    watch: {
        '$route.params.step': function (step) {
            this.loadComponent();
            this.expanded = this.component.expanded;
        },
        expanded: {
            handler: function () {
                this.component.expanded = this.expanded;
            }
        }
    },
    created () {
        this.loadComponent();
    }
}

</script>

<template>
    <div class="col-row" v-if="this.$parent">
        <div class="col col-6">
            <button v-if="previousStep !== null"
                class="mdl-button mdl-js-button
                    mdl-button--raised mdl-js-ripple-effect
                "
                @click="$emit('previous')"
            >
                Back
            </button>
            <button v-if="(steps.length-1) === activeStep"
                class="mdl-button mdl-js-button
                    mdl-button--raised mdl-js-ripple-effect
                "
            >
                Delete
            </button>
        </div>
        <div class="col col-6">
            <div class="righty">
                <button
                    @click="$emit('save')"
                    class="mdl-button mdl-js-button
                        mdl-button--raised mdl-js-ripple-effect
                    "
                >
                    Save
                </button>
                <button v-if="nextStep"
                    class="mdl-button mdl-js-button
                        mdl-button--raised mdl-js-ripple-effect
                        mdl-button--colored
                    "
                    @click="$emit('next')"
                >
                    Next
                </button>
                <button v-if="(steps.length-1) === activeStep"
                    class="mdl-button mdl-js-button
                        mdl-button--raised mdl-js-ripple-effect
                        mdl-button--colored
                    "
                >
                    Complete
                </button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        steps: {
            type: Array,
            required: true,
        },
        activeStep: {
            type: Number
        },
        nextStep: {
            type: Number
        },
        previousStep: {
            type: Number
        },
    },
}
</script>

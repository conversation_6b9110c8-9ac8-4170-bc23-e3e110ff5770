<template>
    <div class="col-row">
        <div class="col col-2">
            <process-stepper 
                :steps="process.steps" 
                :active-step="activeStep" 
                :disabledSteps="process.disabledSteps" 
                v-on="$listeners"
            />
            <component 
                v-if="process.actionComponent" 
                v-bind:is="process.actionComponent.name" 
            />
        </div>
        <div class="col col-10">
            <div v-for="component in activeComponents" :key="component.key" class="col-container mdl-shadow--3dp draft-property">
                <process-component 
                    :objects="process.objects" 
                    :component="component" 
                    :expanded="component.expanded"
                    v-on="$listeners"
                />
            </div>
            <div class="col-container">
                <process-actions 
                    :steps="process.steps" 
                    :active-step="activeStep"
                    :next-step="nextStep"
                    :previous-step="previousStep"
                    v-on="$listeners"
                />
            </div>
        </div>
    </div>
</template>

<script>

export default {
    components: {
        'process-stepper': () => import(/* webpackChunkName: "ProcessWrapper" */ '../../common/process/ProcessStepper.vue'),
        'process-actions': () => import(/* webpackChunkName: "ProcessWrapper" */ '../../common/process/ProcessActions.vue'),
        'process-component': () => import(/* webpackChunkName: "ProcessWrapper" */ '../../common/process/ProcessComponent.vue'),
    },
    props: {
        process: {
            type: Object,
            required: true,
        },
        activeStep: {
            type: Number,
            default: 0,
        },
        nextStep: {
            type: Number
        },
        previousStep: {
            type: Number
        },
    },
    computed: {
        activeComponents() {
            return this.process.steps[this.activeStep].components;
        },
    },
    methods: {
        loadComponents() {
            // load action component
            if (this.process.actionComponent) this.$options.components[this.process.actionComponent.name] = () => import(/* webpackChunkName: "ProcessWrapper" */ `../../../components${this.process.objects.componentDirectory}/${this.process.actionComponent.name}.vue`);
        },
    },
    watch: {
        '$route.params.step': function (step) {
            this.loadComponents();
        }
    },
    created () {
        this.loadComponents();
    }
}
</script>
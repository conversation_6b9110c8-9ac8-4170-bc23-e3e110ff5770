<template>
    <span :class="spanClass?spanClass:'fieldTwo'">
        <div :class="['btn-group', 'root-div-'+fieldName]">
            <button type="button" class="multiselect dropdown-toggle btn btn-default" data-toggle="dropdown" :title="selectedValues" aria-expanded="true" v-on:click="updateSelectAll()">
                <span class="multiselect-selected-text">{{ buttonTitleVal }}</span>
                <b class="caret"></b>
            </button>
            <ul :class="['multiselect-container', 'dropdown-menu', 'ul-'+fieldName]" style="max-height: 400px; overflow-y: auto; overflow-x: hidden;">
                <li class="multiselect-item filter" value="0" v-if="isFilter">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <i class="glyphicon glyphicon-search"></i>
                        </span>
                        <input :class="filterClassComputed" type="text" placeholder="Filter List...">
                        <span class="input-group-btn">
                            <button :class="['btn', 'btn-default', 'multiselect-clear-filter', 'clearButton-'+fieldName]" type="button">
                                <i class="glyphicon glyphicon-remove-circle"></i>
                            </button>
                        </span>
                    </div>
                </li>
                <li v-if="isMultiple" :class="['multiselect-item multiselect-all', isAllSelected?'active':'']">
                    <a tabindex="0" class="multiselect-all">
                        <label class="checkbox" >
                            <input type="checkbox" :id="fieldName+'multiselect-all'" value="multiselect-all" v-on:click="labelClicked(this, 'multiselect-all','multiselect-all','multiselect-all')">{{ selectAll }}
                        </label>
                    </a>
                </li>
                <li v-for="opt in options" :class="(select[getArrayIndex(opt)])?'active':''" :id="'label-'+fieldName+'-'+ opt.label">
                    <a tabindex="0" >
                        <label class="checkbox" v-if="isMultiple">
                            <input v-on:click="labelClicked($event.target, opt.value, opt.label, opt.id)" v-if="isMultiple" type="checkbox" :checked="(select[getArrayIndex(opt)])" :ref="fieldName + opt.label" :id="fieldName + opt.label" :title="opt.value" :value="opt.value"/>{{ opt.label }}
                        </label>
                        <label :class="['checkbox', (select[getArrayIndex(opt)])?'active':'']" v-else v-on:click="labelClicked($event.target, opt.value, opt.label, opt.id)">{{ opt.label }}</label>
                    </a>
                </li>
            </ul>
        </div>
    </span>
</template>

<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import Vue from 'vue';
    export default {
        props: ['unique-identifier',
                'allSelected', 'spanClass', 'multiple',
                'enableCaseInsensitiveFiltering', 'enableFiltering', 'filterClass',
                'allSelectedText', 'buttonTitle', 'fieldName', 'options','selectAll','title', 'limitToShowLabel', 'limitToShowVal', 'value'],
        methods: {
            labelStyle: function () {
                var style = "display: block;";
                if(this.showLabel == "false") {
                    style = "display: none;";
                }
                return style;
            },
            setClass: function () {
                return "advSearch-multiselect monarch-multiselect advSearch-multiselect-" + this.taId;
            },
            hasSelectAll: function() {
                return $('li.multiselect-all', $(".root-div-"+this.fieldName)).length > 0;
            },
            updateSelectAll: function() {
                if (this.hasSelectAll()) {
                    var allBoxes = $("li:not(.multiselect-item):not(.filter-hidden) input:enabled", $(".root-div-"+this.fieldName));
                    var allBoxesLength = allBoxes.length;
                    var checkedBoxesLength = allBoxes.filter(":checked").length;
                    var selectAllLi  = $("li.multiselect-all", $(".root-div-"+this.fieldName));
                    var selectAllInput = selectAllLi.find("input");

                    if (checkedBoxesLength > 0 && checkedBoxesLength === allBoxesLength) {
                        selectAllInput.prop("checked", true);
                        selectAllLi.addClass(this.selectedClass);
                        var obj = null;
                        this.$emit('selectAll', obj);
                    }
                    else {
                        selectAllInput.prop("checked", false);
                        selectAllLi.removeClass(this.selectedClass);
                    }
                }
            },
            setTitle: function(label){
                const self = this;
                self.selectedValues = "";
                $.each($('li', $(".root-div-"+self.fieldName)), function(index, element){
                    var value = $('input', element).length > 0 ? $('input', element).val() : "";
                    var text = $('label', element).text();
                    if($(element).hasClass("active") && text && text.indexOf("Select") < 0) {
                        self.selectedValues += text.trim() + ",";
                    }
                });
                if(self.isMultiple) {
                    self.selectedValues = self.selectedValues .substr(0, self.selectedValues.length>1?(self.selectedValues.length - 1):0);
                }else {
                    self.selectedValues = label;
                }
            },
            emitEvents: function(field) {
                var obj = {};
                obj.field = field;
                obj.values = this.selectedVals;
                this.$emit('select', obj);
            },
            keepDivOpened: function() {
                //Refresh the opened div
                $(".root-div-"+this.fieldName).removeClass("close");
                $(".root-div-"+this.fieldName).removeClass("open");
                $(".root-div-"+this.fieldName).addClass("open");
            },
            labelClicked: function(field, value, label, id) {
                const self = this;
                if(self.isMultiple) {
                    if(label == 'multiselect-all'){
                        self.selectedVals = [];
                        self.select = [];
                        var checked = !self.isAllSelected;
                        self.isAllSelected = checked;
                        document.getElementById(self.fieldName+'multiselect-all').checked = self.isAllSelected;
                        for(var i in self.options) {
                            self.select[self.getArrayIndex(self.options[i])] = checked;
                            if(checked){
                                self.selectedVals[self.options[i].label.trim()+"-"+self.options[i].id] = {value: self.options[i].value, label: self.options[i].label, id: self.options[i].id};
                            }
                        }
                    }else {
                        var checked = false;
                        for(var i in self.options){
                            if(self.options[i].label == label && (self.options[i].id == id)) {
                                checked = !self.select[self.getArrayIndex(self.options[i])];
                                self.select[self.getArrayIndex(self.options[i])] = checked;
                            }

                            if(self.select[self.getArrayIndex(self.options[i])]) {
                                self.selectedVals[self.options[i].label.trim()+"-"+self.options[i].id] = {value: self.options[i].value, label: self.options[i].label, id: self.options[i].id};
                            }else {
                                delete self.selectedVals[self.options[i].label.trim()+"-"+self.options[i].id];
                            }
                        }
                        self.updateSelectAll();
                    }
                }else {
                    self.isAllSelected = false;
                    self.selectedVals = [];
                    self.select = []
                    if(field.className == 'checkbox active') {
                        field.className = 'checkbox';
                        value = '';
                        label = '';
                        id = '';
                    }
                    for(var i in self.options){
                        if(self.options[i].label == label && (self.options[i].id == id)) {
                            self.select[self.getArrayIndex(self.options[i])] = !self.select[self.getArrayIndex(self.options[i])];
                            if(self.select[self.getArrayIndex(self.options[i])]) {
                                self.selectedVals[self.options[i].label.trim()+"-"+self.options[i].id] = {value: self.options[i].value, label: self.options[i].label, id: self.options[i].id};
                            }
                        }
                    }
                }
                self.keepDivOpened();
                self.setTitle(label);
                self.emitEvents(field);

            },
            getArrayIndex: function(option, isObject) {
                if(option.id) {
                    return option.id.replace(/-/g,"$");
                }else if(option.value) {
                    return option.value.replace(/-/g,"$");
                }

            },
            asyncFunction: function(callback, timeout, self) {
                var args = Array.prototype.slice.call(arguments, 3);
                return setTimeout(function() {
                    callback.apply(self || window, args);
                }, timeout);
            },
            filterFieldOnChange: function() {
                const self = this;
                $(".multiselect-search-"+self.fieldName).val(this.query).on('click', function(event) {
                    event.stopPropagation();
                }).on('input keydown', $.proxy(function(event) {
                    // Cancel enter key default behaviour
                    if (event.which === 13) {
                        event.preventDefault();
                    }

                    // This is useful to catch "keydown" events after the browser has updated the control.
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = this.asyncFunction($.proxy(function() {
                        if (self.query !== event.target.value) {
                            self.query = event.target.value;

                            var currentGroup, currentGroupVisible;
                            $.each($('li', $(".root-div-"+self.fieldName)), $.proxy(function(index, element){
                                var value = $('input', element).length > 0 ? $('input', element).val() : "";
                                var text = $('label', element).text();

                                if (value !== self.selectAllValue && text) {
                                    // By default lets assume that element is not
                                    // interesting for this search.
                                    var showElement = false;

                                    if (self.enableCaseInsensitiveFilteringComputed && text.toLowerCase().indexOf(self.query.toLowerCase()) > -1) {
                                        showElement = true;
                                    }
                                    else if (text.indexOf(self.query) > -1) {
                                        showElement = true;
                                    }
                                    // Toggle current element (group or group item) according to showElement boolean.
                                    $(element).toggle(showElement).toggleClass('filter-hidden', !showElement);
                                }
                            }, this));
                        }
                        this.updateSelectAll();
                    }, this), 300, this);
                }, this));
            },
            onMouseDown: function() {
                const self = this;
                $('li a', self.rootDiv).on('mousedown', function(e) {
                    if (e.shiftKey) {
                        // Prevent selecting text by Shift+click
                        return false;
                    }
                });
            },
            anchorClicked: function() {
                const self = this;
                $('li a', self.rootDiv).on('touchstart click', $.proxy(function(event) {
                    event.stopPropagation();
                    var $target = $(event.target);
                    if (event.shiftKey && self.isMultiple) {
                        if($target.is("label")){ // Handles checkbox selection manually (see https://github.com/davidstutz/bootstrap-multiselect/issues/431)
                            event.preventDefault();
                            $target = $target.find("input");
                            try{
                                self.setChecked($target.prop("label"), $target.prop("value"), $target.prop("id"));
                            }catch(e){}

                        }
                        var checked = $target.prop('checked') || false;
                    }
                    $target.blur();
                }, this));
            }
        },
        computed: {
            buttonTitleVal: function() {
                return this.buttonTitle ==''?this.title:this.buttonTitle;
            },
            enableCaseInsensitiveFilteringComputed: function(){
                return (this.enableCaseInsensitiveFiltering && (this.enableCaseInsensitiveFiltering=='true' || this.enableCaseInsensitiveFiltering == true))?true:(this.enableCaseInsensitiveFiltering && (this.enableCaseInsensitiveFiltering=='false' || this.enableCaseInsensitiveFiltering == false))?false:true;
            },
            filterClassComputed: function(){
                if(!this.filterClass && this.filterClass != null && this.filterClass.trim() != "") {
                    return 'form-control ' + this.filterClass + ' multiselect-search-'+this.fieldName;
                } else {
                    return 'form-control multiselect-search multiselect-search-'+this.fieldName;
                }
            }
        },
        data: function() {
            return {
                selectedVals: [],
                selectedValues: "",
                selectAllValue: "multiselect-all",
                filterBehavior: "text",
                isMultiple: true,
                defaultSelection: [],
                checkedArray: [],
                isAllSelected: false,
                selectedClass: 'active',
                select: [],
                isFilter: true
            }
        },
        mounted: function() {
            var self = this;
            self.selectedValues = self.title;
            self.filterBehavior = 'text';
            self.isMultiple = (this.multiple && (this.multiple=='true' || this.multiple == true))?true:(this.multiple && (this.multiple=='false' || this.multiple == false))?false:true;
            self.isFilter = (this.enableFiltering && (this.enableFiltering=='true' || this.enableFiltering == true))?true:(this.enableFiltering && (this.enableFiltering=='false' || this.enableFiltering == false))?false:true;

            self.filterFieldOnChange();
            $(".clearButton-"+self.fieldName).on('click', $.proxy(function(event){
                clearTimeout(self.searchTimeout);
                $('.multiselect-search-'+self.fieldName).val('');
                $('li', $(".root-div-"+self.fieldName)).show().removeClass("filter-hidden");
                self.updateSelectAll();
                setTimeout(function(){
                    self.keepDivOpened();
                }, 100);

            }, this));

            EventBus.$on("multiselect-select-values-"+self.fieldName, function(valuesToBeSelected) {
                self.select = [];
                $.each(valuesToBeSelected, function(index, val){
                    self.select[val.replace(/-/g,"$")] = true;
                });
            });

            EventBus.$on("multiselect-clear-values-"+self.fieldName, function(obj) {
                self.select = [];
                self.selectedVals = [];
                self.selectedValues = [];
                self.buttonTitle = "";
            });
        }
    }
</script>
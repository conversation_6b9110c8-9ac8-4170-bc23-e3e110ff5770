<template>
    <multiselect
        :value="{code: `${value}`}"
        :options="opts"
        track-by="code"
        :label="label"
        :allowEmpty="allowEmpty"
        selectLabel="⏎ select"
        deselectLabel="⏎ remove"
        @input="input"
        @change="input"
        placeholder=""
        ref="multiSelect"
        :disabled="disabled"
    >
        <template slot="singleLabel" slot-scope="props">
            {{ optLabel(props.option.code) }}
        </template>

    </multiselect>
</template>

<script>
import Multiselect from 'vue-multiselect';
import dropDownMixin from './DropDownMixin'

export default {
    components: {
        Multiselect
    },
    mixins: [dropDownMixin],
    props: {
        id: {
            type: String,
        },
        value: {
            type: Boolean,
            required: false,
            default: null,
        },
        allowEmpty: {
            type: Boolean,
            default: true,
        },
        opts: {
            type: Array,
            default: () => [
                {
                    code: "true",
                    label: 'Yes',
                },
                {
                    code: "false",
                    label: 'No',
                },
            ],
        },
        label: {
            type: String,
            default: 'label',
        },
        disabled: {
            type: <PERSON>olean,
            default: false,
        },
    },
    methods: {
        input(opt) {
            this.$emit('input', {
                value: opt && opt.code ? this.optCodeToBool(opt.code) : null,
                id: this.id,
            });
            /* HACK Work around to close the dropdown if the user selects a single value. */
            var self = this;
            this.$nextTick(() => {
                setTimeout(function() {
                    self.$refs.multiSelect.deactivate();
                },10);
            });
        },
        optLabel(code) {
            const theOpt = this.opts.find(
                (opt) => (opt.code === code)
            );
            if (!theOpt) return '';
            return theOpt.label;
        },
        optCodeToBool(code) {
            if (code === null) return null;
            if (code === 'true') return true;
            if (code === 'false') return false;
        }
    },
    mounted() {
        this.bindMultiSelect(this.$refs.multiSelect);
    }
}
</script>



<template>
    <input
        :value="_value"
        v-on="_listeners"
        v-bind="$attrs"
        :type="_type"
        ref="inputElement"
    />
</template>

<script>
// DEPRECATED Use InputNumber.vue instead
export default {
    inheritAttrs: false,
    data(){
        return {
            focused: false,
        };
    },

    props: {
        value: {
            type: Number,
        },
        format: {
            type: String,
        },
    },

    computed: {
        _listeners() {
            return {
                ...this.$listeners,
                blur: ($event) => { this.focused = false; this.$emit('blur', $event) },
                focus: this.focus,
            };
        },
        _type() {
            if(this.focused) return 'number';
            return 'text';
        },
        _value() {
            if(this.focused) return this.value;
            return this.$options.filters.numeral(this.value, this.format);
        }
    },
    watch: {
        _value() {
            this.$emit('valueChanged', this.value);
        }
    },
    methods: {
        focus(evt) {
            this.focused = true;
            if (this.$refs.inputElement.selectionStart || this.$refs.inputElement.selectionEnd) {
                this.$nextTick(() => {
                    this.$refs.inputElement.select();
                });
            }
            this.$nextTick(() => {
                this.$refs.inputElement.focus();
            });
            this.$emit('focus', evt);
        },
    },
}
</script>

<style></style>

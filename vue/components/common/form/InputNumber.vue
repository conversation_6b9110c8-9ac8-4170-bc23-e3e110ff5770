<script>
export default {
    inheritAttrs: false,
}
</script>

<script setup>
import { computed, ref, useAttrs } from 'vue';
import numeral from 'numeral';
import FormItemErrors from './FormItemErrors.vue';
import { BaseInput } from '@/components/ui/input';

const emit = defineEmits(['input']);
const element = ref();
const isFocused = computed(() => element.value?.isFocused);
const attrs = useAttrs();
const props = defineProps({
    value: {
        required: true,
    },
    minValue: {
      default: 0,
    },
    format: {
        type: String,
        default: '$0,0',
    },
    displayEmpty: {
        type: Boolean,
        default: false,
    },
    errors: {
        type: Array,
        default: () => [],
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    scalePercent : {
        type: Boolean,
        default: false,
    },
    defaultValue: {
        type: Number,
        default: null,
    },
});
const focused = ref(false);
const formattedNumber = computed(() => {
    const originalScalePercentBy100 = numeral.options.scalePercentBy100;
    numeral.options.scalePercentBy100 = props.scalePercent;

    if (props.displayEmpty) {
        return [null, undefined, ''].includes(props.value) ? '' : numeral(props.value).format(props.format);
    }

    if (!props.value) {
        return props.defaultValue !== null ? numeral(props.defaultValue).format(props.format) : null;
    }

    const formattedValue = numeral(props.value).format(props.format);
    numeral.options.scalePercentBy100 = originalScalePercentBy100;
    return formattedValue;
});
const tabindex = computed(() => {
    if (props.readonly) {
        return '-1';
    }
    return '0';
});
const shouldRound = computed(() => (attrs.round !== undefined ? Boolean.valueOf(attrs.round) : undefined));
const fixedDecimal = computed(() => (attrs.fixed !== undefined ? parseInt(attrs.fixed) : undefined));

function handleInput(value) {
    const num = numeral(value).value();
    if (props.minValue !== false && num < props.minValue) {
        emit('input', numeral(props.minValue).value());
        return;
    }
    emit('input', num);
}

function onFocusOut() {
    let value = props.value; 

    if (!value) {
        return;
    }

    if (shouldRound.value) {
        value = Math.round(value);
    }

    if (fixedDecimal.value !== undefined) {
        value = parseFloat(value).toFixed(fixedDecimal.value);
    }

    emit('input', value);
}

</script>

<template>
    <div>
        <BaseInput
            v-bind="attrs"
            ref="element"
            :type="isFocused ? 'number' : 'text'"
            :value="isFocused ? props.value : formattedNumber"
            :class="{ 'qv-form-input-error': props.errors.length }"
            :tabindex="tabindex"
            :readonly="props.readonly"
            :disabled="props.readonly"
            :min="minValue"
            @input="handleInput"
            @blur="() => onFocusOut()"
        />
        <form-item-errors v-if="props.errors.length" class="qv-mt-1" :errors="props.errors" />
    </div>
</template>

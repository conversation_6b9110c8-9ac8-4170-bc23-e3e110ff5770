<template>
    <multiselect v-if="opts && opts.length > 0"
        :value="selectedValue"
        :options="opts"
        :label="label"
        :custom-label="getLabelFunction"
        track-by="ntUsername"
        :multiple="multiple"
        :taggable="taggable"
        :allowEmpty="allowEmpty"
        selectLabel="⏎ select"
        deselectLabel="⏎ remove"
        @select="change"
        @input="input"
        @remove="unset"
        :loading="!loaded"
        :limit="limit"
        :placeholder="placeholder"
        ref="multiSelect"
    >
        <template slot="singleLabel" slot-scope="props">
            <span>{{singleLabelFunction(props.option)}}</span>
        </template>
        <template slot="tag" slot-scope="props">
            <span class="multiselect__tag">
                <span v-text="props.option[singleLabel]"></span>
                <i aria-hidden="true"
                    tabindex="1"
                    @keypress.enter.prevent="props.remove(props.option)"
                    @mousedown.prevent="props.remove(props.option)"
                    class="multiselect__tag-icon"
                >
                </i>
            </span>
        </template>
    </multiselect>
</template>

<script>
import Multiselect from 'vue-multiselect';
import { mapState } from 'vuex';

export default {
    components: {
        Multiselect
    },
    props: {
        id: {
            type: String,
        },
        value: {
            type: [Object, Array],
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        taggable: {
            type: Boolean,
            default: false,
        },
        allowEmpty: {
            type: Boolean,
            default: true,
        },
        label: {
            type: String,
            default: 'description',
        },
        singleLabel: {
            type: String,
            default: 'description',
        },
        labelFunction: {
            type: Function,
            default: (opt) => {
                if (opt.code === opt.name) return opt.ntUsername;
                return `${opt.ntUsername} — ${opt.name}`;
            },
        },
        singleLabelFunction: {
            type: Function,
            default: (opt) => {
                return opt.name;
            },
        },
        sortFunction: {
            type: Function,
            default(a, b) {
                if (a.name !== b.name) {
                    return a.name - b.name;
                }
                // sortOrders are equal
                if (a.ntUsername < b.ntUsername) {
                    return -1;
                }
                return 1;
            }
        },
        hideCodes:  {
            type: Boolean,
            default: false,
        },
        limit: {
            type: Number,
        },
        filterOptionsFunction: {
            type: Function
        },
        placeholder: {
            type: String
        }
    },
    computed: {
        loaded() {
            return ! this.$store.state.valuersList.loading;
        },
        opts() {
            if (! this.loaded) return [];
            const valuers = this.$store.state.valuersList.valuers;
            if (! valuers) return [];
            if (this.filterOptionsFunction) return (this.filterOptionsFunction([...valuers].sort(this.sortFunction)));
            return [...valuers].sort(this.sortFunction);
        },
        getLabelFunction() {
            function descriptionLabel(opt) {
                return `${opt.name}`;
            }

            /* If hiding codes then use the description only */
            if(this.hideCodes)
                return descriptionLabel;

            /* In all other cases use label function provided or code + description by default */
            return this.labelFunction;
        },
        selectedValue() {
            if (this.multiple) {
                return this.opts.filter(c => this.value && this.value.find(val => val.ntUsername === c.ntUsername));
            } else {
                return this.opts.find(c => this.value && this.value.ntUsername === c.ntUsername);
            }
        }
    },
    async created() {
        await this.$store.dispatch('valuersList/getValuers');
    },
    methods: {
        change(value) {
            if (this.multiple) return;
            this.$emit('input', {
                value,
                id: this.id,
            });
            /* HACK Work around to close the dropdown if the user selects a single value. */
            var self = this;
            this.$nextTick(() => {
                setTimeout(function() {
                    self.$refs.multiSelect.deactivate();
                },10);
            });
        },
        unset(value) {
            if (this.multiple) return;
            this.$emit('input', {
                value: null,
                id: this.id,
            });
        },
        input(value) {
            if (!this.multiple) return;
            this.$emit('input', {
                value,
                id: this.id,
            });
        },
    },
}
</script>



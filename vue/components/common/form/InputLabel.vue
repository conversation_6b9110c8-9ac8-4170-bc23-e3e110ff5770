<script>
export default {
    inheritAttrs: false,
}
</script>

<script setup>
import { useAttrs } from 'vue';

const attrs = useAttrs();
const props = defineProps({
    label: {
        type: String,
        required: true,
    },
    labelStyle: {
        type: String,
        default: '',
    },
});

</script>

<template>
    <div class="qv-flex-column qv-gap-1 qv-w-full">
        <span
            v-bind="attrs"
            :class="`qv-color-blue qv-text-sm ${labelStyle}`"
        >
            {{label}}
        </span>
        <slot/>
    </div>
</template>

<template>
    <div v-if="validationSet">
        <div
            v-if="failed && hasErrors"
            class="message message-error"
            data-cy="validation-header-errors"
        >
            {{ errorText }}
            <ul v-if="showErrors">
                <li
                    v-for="(error, index) in formattedErrors"
                    :key="index"
                >
                    - {{ error }}
                </li>
            </ul>
        </div>
        <div
            v-if="hasWarnings && showWarnings"
            class="message message-warning"
            data-cy="validation-header-warnings"
        >
            {{ warningText }}
            <ul>
                <li
                    v-for="(warning, index) in formattedWarnings"
                    :key="index"
                >
                    - {{ warning }}
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
const dict = {
    'dvrSnapshot.wallCondition' : 'Derived Wall Condition',
    'dvrSnapshot.roofCondition' : 'Derived Roof Condition',
    'wallConstruction.definition': 'Wall Construction',
    'wallConstruction.quality': 'Wall Condition',
    'floorConstruction.definition': 'Floor Construction',
    'foundation.definition': 'Foundation',
    'roofConstruction.definition': 'Roof Construction',
    'roofConstruction.quality': 'Roof Condition',
    'siteDevelopment.quality' : 'Site Development Quality',
    'siteDevelopment.description' : 'Site Development Description',
    rollMaintenanceActivityIds : 'Roll Maintenance Activity',
    'adoptedRevisionValue.capitalValue' : 'Revision Capital Value',
    'adoptedRevisionValue.landValue'  : 'Revision Land Value',
    'adoptedRevisionValue.valueOfImprovements' : 'Revision Value of Improvements',
};
// Used for converting a message into a custom message
const customMessages = {
    // Custom text for spaces is empty error message
    'Spaces is empty': 'There is a space missing',
};

export default {
    props: {
        validationSet: {
            type: Object,
            default: () => null,
        },
        action: {
            type: String,
            default: 'continue the process',
        },
        showErrors: {
            type: Boolean,
            default: false,
        },
        showWarnings: {
            type: Boolean,
            default: false,
        },
        showWarningsMessage: {
            type: Boolean,
            default: false,
        },
        message: {
            type: String,
            default: () => null,
        },
        warningMessage: {
            type: String,
            default: () => null,
        },
    },
    computed: {
        failed() { return this.validationSet ? !this.validationSet.success : false; },
        errorCount() { return this.formattedErrors.length; },
        hasErrors() { return this.errorCount > 0},
        hasWarnings() { return this.validationSet.warnings ? this.validationSet.warnings.length > 0 : false; },
        errorText() {
            if (this.message || this.message === '') return this.message;
            return `Unable to ${this.action} until the following ${this.errorCount} ${this.errorCount === 1 ? 'issue is' : 'issues are'} resolved.`;
        },
        warningText() {
            if(!this.showWarningsMessage) return "";
            if (this.warningMessage || this.warningMessage === '') return this.warningMessage;
            return `The following warnings occurred when attempting to ${this.action}.`;
        },
        formattedErrors() {
            if (this.validationSet && this.validationSet.errors) {
                // Format the errors
                const errors = this.validationSet.errors.map(error => this.formatError(error));
                // Deduplicate the error messages
                return [...new Set(errors)];
            }
            return [];
        },
        formattedWarnings() {
            if (this.validationSet && this.validationSet.warnings) {
                // Format the warnings
                const warnings = this.validationSet.warnings.map(warning => this.formatError(warning));
                // Deduplicate the warning messages
                return [...new Set(warnings)];
            }
            return [];
        },
    },
    methods: {
        /* TODO HACK format temporary messages */
        formatError(error) {
            if (error.field && error.field.length > 0) {
                let message = '';
                const definedField = Object.keys(dict)
                    .find(fieldName => error.field.includes(fieldName));
                if (definedField) {
                    message = `${dict[definedField]} ${error.message}`;
                } else {
                    let field = error.field.split(/[\s.]+/).pop();
                    field = field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                    message = `${field} ${error.message}`;
                }
                // Check if message is defined in the custom message map.
                if (customMessages[message] != null) {
                    message = customMessages[message];
                }
                return `${message}.`;
            }
            return `${error.message}.`;
        },
    },
};
</script>

<style lang="scss" scoped src="../../rollMaintenance/rollMaintenance.scss"></style>

<template>
    <div :class="type" v-if="validationSet && !validationSet.success" data-cy="validation-messages">
        {{ message }}
    </div>
</template>

<script>
import commonUtils from  '../../../utils/CommonUtils';

export default {
    mixins: [commonUtils],
    props: {
        validationSet: {},
        field: {
            type: String,
            required: true,
        },
    },
    computed: {
        type() {
            return 'error';
        },
        message() {
            if (!this.validationSet || !this.validationSet.errors) return '';
            const matchingErrors = this.findObject(this.validationSet.errors, 'field', this.field);
            if (matchingErrors.length === 0) return '';
            return matchingErrors[0].message;
        }
    },
}
</script>

<style lang="scss" scoped>
    .error {
        color: #ea2e2d;
        border: none;
    }
</style>

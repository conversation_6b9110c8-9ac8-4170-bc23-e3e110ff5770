<script setup>
import { computed } from 'vue';
import Multiselect from 'vue-multiselect';

const props = defineProps({
    value: {
        type: Object,
        required: false,
        default: null,
    },
    options: {
        type: Array,
        required: true,
    }
});

const emit = defineEmits(['input']);

const modelValue = computed({
  get: () => props.value,
  set: (val) => emit('input', val),
});

const getLabelFunction = (opt) => opt.description;
</script>

<template>
    <multiselect
        v-if="Array.isArray(options) && options.length > 0"
        v-model="modelValue"
        :options="options"
        :label="'description'"
        :custom-label="getLabelFunction"
        track-by="id"
        :multiple="false"
        :taggable="false"
        :allowEmpty="true"
        selectLabel="⏎ select"
        deselectLabel="⏎ remove"
        placeholder=""
        ref="multiSelect"
    >
        <template #singleLabel="{ option }">
            <span>{{ option.description }}</span>
        </template>
        <template #tag="{ option, remove }">
            <span class="multiselect__tag">
            <span>{{ option.description }}</span>
            <i
                aria-hidden="true"
                tabindex="1"
                @keypress.enter.prevent="remove(option)"
                @mousedown.prevent="remove(option)"
                class="multiselect__tag-icon"
            />
            </span>
        </template>
    </multiselect>
  </template>
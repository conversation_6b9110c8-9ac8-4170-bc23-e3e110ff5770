/*  NOTE:
    As a quick hack this assumes the related multiselect is tracking by a "code" attribute on each option.
    In future it would be nice to do it properly and make comparison dynamic/configurable.
 */
export default {
    data() {
        return {
            multiSelect: null,
        };
    },
    props: {
        singleSelectOnTab: {
            type: Boolean,
            default: true,
        },
        multiSelectOnTab: {
            type: Boolean,
            default: false,
        },
        multiOpenOnEnter: {
            type: Boolean,
            default: true,
        },
        addMultiClass: {
            type: String,
            default: 'multiselect__select--multiple',
        },
        showCurrentOptionOnFocus: {
            type: Boolean,
            default: true,
        },
    },
    methods: {
        bindMultiSelect(multiSelect) {
            // If already bound then dont bind twice (HACK) and if nothing to bind then skip out
            if(this.multiSelect || !multiSelect)
                return;

            this.multiSelect = multiSelect;

            // Bind event listener
            this.multiSelect.$refs.search.addEventListener('keydown', this.keydown);

            if (this.showCurrentOptionOnFocus) {
                this.multiSelect.$refs.search.addEventListener('blur', this.blur);
                this.multiSelect.$refs.search.addEventListener('focus', this.focus);
            }

            // If multiselect then add class to allow us to style things differently
            if (this.multiSelect.multiple && this.addMultiClass) {
                const selector = this.$el.querySelector('.multiselect__select');
                if (selector) selector.classList.add(this.addMultiClass);
            }
        },
        blur(event) {
            // Dont think need to do anything with this yet ...
        },
        focus(event) {
            if (this.multiSelect.multiple) return;
            this.multiSelect.$refs.search.placeholder = this.multiSelect.currentOptionLabel;
        },
        keydown(event) {
            // If user pushed enter in a multi select and option to keep open then
            if (event.key == 'Enter' && this.multiSelect.multiple && this.multiOpenOnEnter) {
                // reactivate dropdown
                setTimeout(() => {
                    this.multiSelect.activate();
                }, 10);
            }
            // If user tabbed (without shift) ...
            if (event.key == 'Tab' && !event.shiftKey) {
                // ... and there is something to select and user has moved around
                if (this.multiSelect.filteredOptions.length > 0 && this.multiSelect.pointerDirty) {
                    const option = this.multiSelect.filteredOptions[this.multiSelect.pointer];
                    const currentSelection = this.multiSelect.getValue();
                    // Multiple Option Selection
                    if (this.multiSelect.multiple && this.multiSelectOnTab) {
                        // If didnt find this option in the current selection
                        if (!currentSelection.find(item => item.code === option.code)) {
                            // then fake the user selecting the option under the pointer
                            setTimeout(() => {
                                this.multiSelect.select(option, 'Enter');
                            }, 10);
                        }
                    }
                    // Single Option selection
                    if (!this.multiSelect.multiple && this.singleSelectOnTab) {
                        // - if current option isn't already selected
                        if (!currentSelection || option.code != currentSelection.code)
                        // then fake the user selecting the option under the pointer
                        {
                            setTimeout(() => {
                                this.multiSelect.select(option, 'Enter');
                            }, 10);
                        }
                    }
                }
            }
        },
    },
};

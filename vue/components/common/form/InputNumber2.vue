<script>
export default {
    inheritAttrs: false,
};
</script>

<script setup>
import { computed, ref, useAttrs } from 'vue';
import numeral from 'numeral';
import FormItemErrors from './FormItemErrors.vue';
import _ from 'lodash';

const emit = defineEmits(['input']);
const attrs = useAttrs();
const props = defineProps({
    value: {
        required: true,
    },
    format: {
        type: String,
        default: '$0,0',
    },
    customFormat: {
        type: Function,
        default: null,
    },
    postFormatFormat: {
        type: Function,
        default: null,
    },
    displayEmpty: {
        type: Boolean,
        default: true,
    },
    errors: {
        type: Array,
        default: () => [],
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    handleKeyup: {
        type: Function,
        default: () => {},
    },
    round: {
        type: Boolean,
        default: false,
    },
    warnings : {
        type: Boolean,
        default: false,
    }
});

const inputRef = ref(null);
const focused = ref(false);

const formattedNumber = computed(() => {
    if (props.displayEmpty) {
        return [null, undefined, ''].includes(props.value) ? '' : format(props.value);
    }

    if (!props.value) {
        return '';
    }

    return format(props.value);
});
const tabindex = computed(() => {
    if (props.readonly) {
        return '-1';
    }
    return '0';
});

function format(value) {
    if (props.customFormat) {
        return props.customFormat(value);
    }
    const numeralFormat = numeral(value).format(props.format);
    if (props.postFormatFormat) {
        return props.postFormatFormat(numeralFormat);
    }
    return numeralFormat;
}

function handleInput(event) {
    const inputValue = event.target.value;
    const num = _.isNil(inputValue) ? null : numeral(inputValue).value();
    emit('input', num && props.round ? Math.round(num) : num);
}
function handleChange(event) {
    const inputValue = event.target.value;
    if (_.isNil(inputValue) || inputValue === '') {
        return;
    }
    const num = numeral(inputValue).value();
    const min = numeral(inputRef.value.getAttribute('min') ?? 0).value();
    const max = numeral(inputRef.value.getAttribute('max') ?? Infinity).value();
    if (num < min) {
        emit('input', min);
        return;
    }
    if (num > max) {
        emit('input', max);
        return;
    }
}
</script>

<template>
    <div>
        <input ref="inputRef" v-bind="attrs" :type="focused ? 'number' : 'text'" class="qv-form-input" :class="{ 'qv-form-input-error': props.errors.length, 'qv-form-input-warning': props.warnings }" :tabindex="tabindex" :readonly="props.readonly" :disabled="props.readonly" :value="focused ? props.value : formattedNumber" @input="handleInput" @change="handleChange" @keydown="handleKeyup" @focusin="focused = true" @focusout="focused = false" />
        <form-item-errors v-if="props.errors.length" class="qv-mt-1" :errors="props.errors" />
    </div>
</template>

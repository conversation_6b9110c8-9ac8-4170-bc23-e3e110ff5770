<script>
export default {
    inheritAttrs: false,
};
</script>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router/composables';

const route = useRoute();
const props = defineProps({
    to: {
        type: Object,
    },
    height: {
        type: String,
        default: '6rem',
    },
});

const active = computed(() => route.name === props.to.name);
</script>

<template>
    <router-link :to="to" class="qv-text-base qv-px-3" :style="{ 'margin-top': 'auto', 'background-color': active ? 'rgba(255, 255, 255, 0.05)' : 'transparent' }" :data-cy="to.name">
        <div :style="{ 'min-height': height, 'border-bottom': active ? '3px solid white' : '' }" class="qv-h-full qv-flex-column qv-justify-end qv-pb-1" :class="active ? 'qv-color-light' : 'qv-color-lightbuff qv-color-light:hover'">
            <slot />
        </div>
    </router-link>
</template>

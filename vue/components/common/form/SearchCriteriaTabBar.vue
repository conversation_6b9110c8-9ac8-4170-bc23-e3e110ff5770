<script setup>
const props = defineProps({
    barHeight: {
        default: '6rem',
    },
});
</script>

<template>
    <div class="qv-bg-stoneblue" data-cy="search-criteria-tab-bar-header">
        <div class="qv-flex-row qv-gap-0 qv-mx-3 qv-mt-3 qv-h-full">
            <div class="qv-h-full qv-flex-column qv-color-light qv-divider-light qv-pr-3 qv-pb-1 qv-justify-end" :style="{ 'min-height': barHeight, 'max-height': barHeight, 'border-right': '1px solid rgba(255, 255, 255, 0.15)' }" data-cy="search-criteria-tab-bar-subheader">
                <h2 class="qv-font-bold qv-text-md">
                    <slot name="title"></slot>
                </h2>
            </div>
            <div class="qv-flex-row qv-pl-3 qv-flex-grow qv-divider-light qv-align-self-end" :style="{ 'min-height': barHeight, 'max-height': barHeight }">
                <slot />
            </div>
        </div>
    </div>
</template>

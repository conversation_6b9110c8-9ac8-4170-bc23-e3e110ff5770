<script setup>
import InputLabel from 'Common/form/InputLabel.vue';
import { computed, ref } from 'vue';
import MaterialIcon from 'Common/MaterialIcon.vue';

const props = defineProps({
    results: {
        type: Array,
    },
    highlighted: {
        type: Array,
        default: ()=> [],
    },
    value: {
        type: String,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    errors: {
        type: Array,
        default: () => [],
    },
});
const emit = defineEmits(['select', 'input']);
const focused = ref(false);
const hide = ref(false);
const showResults = computed(() => props.results.length > 0 && focused.value && props.value.length > 0 && !hide.value)
const selectedProperty = ref(0);
const searchInput = ref(null);

const queryValue = computed({
    get() {
        return props.value;
    },
    set(value) {
        emit('input', value);
    },
});

function onInput(e) {
    if (hide.value) {
        hide.value = false;
    }

    selectedProperty.value = 0;
}

function onFocusOut(event) {
    if (event.relatedTarget?.classList.contains('qv-typeahead-result')) {
        event.preventDefault();
        return;
    }

    focused.value = false;
}

function onClickResult(property) {
    selectProperty(property);

    if (searchInput.value) {
        searchInput.value.focus();
    }
}

function selectProperty(property) {
    emit('select', property);
}

function cycleSelection(dir) {
    selectedProperty.value = Math.max(0, (selectedProperty.value + dir) % props.results.length);
}

</script>

<template>
    <input-label class="qv-position-relative" label="Property Search" style="display: block;">
        <input ref="searchInput"
               v-model="queryValue"
               class="qv-form-input"
               :class="{ 'qv-form-input-error': errors.length > 0 }"
               placeholder="Search for a property..."
               type="text"
               :focus="focused"
               :readonly="readonly"
               @focusin="focused = true"
               @focusout="onFocusOut"
               @input="onInput"
               @keydown.up.prevent="cycleSelection(-1)"
               @keydown.down.prevent="cycleSelection(1)"
               @keydown.enter="selectProperty(results[selectedProperty])"
               @keydown.esc="hide = true"
        >
        <div class="qv-form-typeahead-icon">
            <slot/>
        </div>
        <ul v-if="showResults" class="qv-form-typeahead qv-w-full qv-position-absolute">
            <li v-for="(result, index) in results"
                :selected="index === selectedProperty"
                @click="() => onClickResult(result)"
                @mouseover="selectedProperty = index"
                class="qv-typeahead-result qv-cursor-pointer"
                tabindex="-1"
            >
                <div class="qv-flex-row qv-justify-space-between qv-pointer-events-none">
                    <p class="qv-text-capitalize qv-flex-grow">{{ result.address }}</p>
                    <p class="qv-color-blue qv-text-right qv-w-1/8">{{ result.valRef }}</p>
                    <p class="qv-color-blue qv-text-right qv-w-1/8">{{ result.qpid }}</p>
                    <div class="qv-text-right qv-w-1/8">
                        <transition name="fade" :duration="150">
                          <material-icon v-show="highlighted.includes(parseInt(result.qpid))" class="qv-color-success" icon="done"/>
                        </transition>
                    </div>
                </div>
            </li>
        </ul>
    </input-label>
</template>

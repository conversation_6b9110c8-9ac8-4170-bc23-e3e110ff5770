<script>
export default {
    inheritAttrs: false,
};
</script>

<script setup>
import { computed } from 'vue';
import moment from 'moment';
import FormItemErrors from './FormItemErrors.vue';

const props = defineProps({
    value: {
        required: true,
    },
    errors: {
        type: Array,
        default: () => [],
    },
    readonly: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:value']);

const date = computed({
    get() {
        return moment(props.value).format('YYYY-MM-DD');
    },
    set(value) {
        emit('input', value);
    },
});
const tabindex = computed(() => {
    if (props.readonly) {
        return '-1';
    }
    return '0';
});
</script>

<template>
    <div>
        <input v-model="date" type="date" class="qv-form-input" :class="{ 'qv-form-input-error': props.errors.length }" v-bind="$attrs" :readonly="props.readonly" :tabindex="tabindex" />
        <form-item-errors v-if="props.errors.length" :errors="props.errors" />
    </div>
</template>

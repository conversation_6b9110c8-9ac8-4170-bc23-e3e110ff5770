<script>
export default {
    inheritAttrs: false,
}
</script>

<script setup>
import { computed, ref, useAttrs } from 'vue';
import FormItemErrors from './FormItemErrors.vue';

const attrs = useAttrs();
const props = defineProps({
    value: {
        required: true,
    },
    errors: {
        type: Array,
        default: () => [],
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    shouldTrim: {
        type: Boolean,
        default: false,
    },
    customClass: {
        type: String,
        default: '',
    },
    shouldAutoGrow: {
        type: Boolean,
        default: false,
    },
    preventEnter: {
        type: Boolean,
        default: false,
    },
});
const focused = ref(false);
const tabindex = computed(() => {
    if (props.readonly) {
        return '-1';
    }
    return '0';
});
const inputRef = ref(null);

function preventEnter(event) {
    if(props.preventEnter) {
        event.preventDefault();
    }
}

defineExpose({
    focus() {
        inputRef.value.focus();
    },
});

</script>

<template>
    <div>
        <textarea
            ref="inputRef"
            v-bind="attrs"
            class="qv-form-input"
            :value="props.value"
            :class="{ 'qv-form-input-error': props.errors.length, 'overflow-hidden': props.shouldAutoGrow, [props.customClass]: props.customClass }"
            :tabindex="tabindex"
            :readonly="props.readonly"
            @change="$emit('input', props.shouldTrim ? ($event.target.value || '' ).trim() : $event.target.value)"
            @focusout="focused = false"
            @focusin="focused = true"
            v-auto-grow="props.shouldAutoGrow"
            @keydown.enter.prevent="preventEnter"
        />
        <form-item-errors v-if="props.errors.length" :errors="props.errors" />
    </div>
</template>

<style>
.overflow-hidden {
    overflow: hidden !important;
}
</style>
<script>
export default {
    inheritAttrs: false,
}
</script>
<script setup>
import { computed, onMounted, ref, useAttrs, watch, nextTick } from 'vue';
import Multiselect from 'vue-multiselect';
import FormItemErrors from './FormItemErrors.vue';

const props = defineProps({
    value: { required: true },
    options: { required: true },
    optionName: {
        type: Function,
    },
    includeEmptyOption: {
        type: Boolean,
        default: false,
    },
    readonly: { type: Boolean, default: false },
    selectRef: { type: String, default: '' },
    errors: { type: Array, default: () => [] },
    selectClass: { type: String, default: '' },
    hasBooleanSelection: { type: Boolean, default: false },
});
const emit = defineEmits(['change', 'input']);
const attrs = useAttrs();
const objectValue = ref(null);
const multiselectRef = ref(null);
const formSelectRef = ref(null);
let isWidthAdjusted = false;

const selection = computed({
    get() {
        return objectValue.value;
    },
    set(value) {
        objectValue.value = value;
        emit('input', value?.id ?? null);
        emit('change', value?.id ?? null);
    },
});

onMounted(() => {
    refreshObjectValue();
});

watch(() => props.value, refreshObjectValue);

function refreshObjectValue() {
    if (props.value !== null) {
        return objectValue.value = props.options.find((option) => option.id === props.value);
    }

    objectValue.value = null;
}

function parseOptionName(option) {
    return props.optionName ? props.optionName(option) : option.description;
}

function updateMinWidth() {
    if (multiselectRef.value && !isWidthAdjusted) {
    const contentWrapper = multiselectRef.value.$el.querySelector('.multiselect__content-wrapper');
    if(props.hasBooleanSelection) {
        contentWrapper.style.minWidth = '175px';
        isWidthAdjusted = true;
        return;
    }
    nextTick(() => {
      const options = contentWrapper.querySelectorAll('.multiselect__option');
      let maxWidth = 0;

      options.forEach(option => {
        maxWidth = Math.max(maxWidth, option.clientWidth);
      });
      const minWidth = maxWidth + 100;

      contentWrapper.style.minWidth = `${minWidth}px`;
      isWidthAdjusted = true;
    });
  }
}

defineExpose({
    selection,
});

</script>

<template>
    <div ref="formSelectRef">
        <Multiselect
            v-model="selection"
            v-bind="attrs"
            :disabled="readonly"
            :options="props.options"
            :custom-label="parseOptionName"
            :close-on-select="true"
            class="qv-multiselect"
            :class="{ 'qv-multiselect-error': errors && errors.length > 0 }"
            track-by="id"
            select-label="⏎ select"
            deselect-label="⏎ remove"
            aria-placeholder=""
            ref="multiselectRef"
            @open="updateMinWidth"
        >
            <template #placeholder>&nbsp;</template>
        </Multiselect>
        <form-item-errors v-if="errors.length" class="qv-mt-1" :errors="props.errors" />
    </div>
</template>

<script setup>
const props = defineProps({
    label: {
        type: String,
        required: true
    },
    labelStyle: {
        type: String,
        default: ''
    },
    wrapperStyle: {
        type: String,
        default: ''
    }

})
</script>

<template>
    <div :class="`qv-flex-row ${wrapperStyle}`" style="gap: 5px;">
        <div :class="`qv-color-blue qv-text-sm ${labelStyle}`">
            {{ label }}
        </div>
        <div class="qv-w-full">
            <slot/>
        </div>
    </div>
</template>

<script>
export default {
    inheritAttrs: false,
}
</script>

<script setup>
import { computed, ref, useAttrs } from 'vue';
import FormItemErrors from './FormItemErrors.vue';

const attrs = useAttrs();
const props = defineProps({
    value: {
        required: true,
    },
    errors: {
        type: Array,
        default: () => [],
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    textCenter: {
        type: Boolean,
        default: false,
    },
    handleKeyup: {
        type: Function,
        default: () => {},
    },
});
const focused = ref(false);
const tabindex = computed(() => {
    if (props.readonly) {
        return '-1';
    }
    return '0';
});
const inputRef = ref(null);

defineExpose({
    focus() {
        inputRef.value.focus();
    },
});

</script>

<template>
    <div>
        <input
            ref="inputRef"
            v-bind="attrs"
            class="qv-form-input"
            :value="props.value" type="text"
            :class="{ 'qv-form-input-error': props.errors.length }"
            :tabindex="tabindex"
            :readonly="props.readonly"
            @change="$emit('input', $event.target.value)"
            @focusout="focused = false"
            @focusin="focused = true"
            @keyup="handleKeyup"
            :style="{ 'text-align': props.textCenter ? 'center' : 'left' }"
        >
        <form-item-errors v-if="props.errors.length" class="qv-mt-1" :errors="props.errors" />
    </div>
</template>

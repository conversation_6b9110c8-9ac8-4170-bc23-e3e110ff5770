<script setup>
import InputLabel from 'Common/form/InputLabel.vue';
import { computed, onMounted, ref } from 'vue';
import { store } from '@/DataStore';
import LoadingSpinner from 'Common/LoadingSpinner.vue';
import FormSelect from 'Common/form/FormSelect.vue';
import FormTextArea from 'Common/form/FormTextArea.vue';

const props = defineProps({
    rfc: {
        required: true,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    outputErrors: {
        type: Array,
        default: () => [],
    },
    sourceErrors: {
        type: Array,
        default: () => [],
    },
    reasonErrors: {
        type: Array,
        default: () => [],
    },
    showHeader: {
        type: Boolean,
        default: true,
    },
    sourceOptions: {
        type: Array,
        default: () => [],
    },
    outputOptions : {
        type: Array,
        default: () => [],
    },
    isFilteredOptions: {
        type: Boolean,
        default: false
    }
});

const classifications = computed(() => store.state.saleClassifications.classifications);
const computedOutputOptions = computed(() => props.isFilteredOptions ? props.outputOptions : classifications.value.ReasonOutput);
const computedSourceOptions = computed(() => props.isFilteredOptions ? props.sourceOptions : classifications.value.ReasonSource);

</script>

<template>
    <div v-if="!classifications">
        <loading-spinner/>
    </div>
    <div v-else class="qv-flex-col" data-cy="reason-for-change">
        <div class="qv-flex-row qv-mb-2 qv-justify-space-between" v-if="showHeader">
            <h3>Reason for Change</h3>
            <slot name="header" />
        </div>
        <div class="qv-flex-row qv-gap-2 ">
            <div class="qv-w-1/3">
                <input-label label="Output">
                    <form-select
                        v-model="rfc.output"
                        :options="computedOutputOptions"
                        :option-name="option => `${option.code ? option.code : ''} - ${option.description}`"
                        :include-empty-option="true"
                        :readonly="props.readonly"
                        :errors="props.outputErrors"
                        data-cy="reason-for-change-output"
                    />
                </input-label>
            </div>
            <div class="qv-w-1/3">
                <input-label label="Source">
                    <form-select
                        v-model="rfc.source"
                        :options="computedSourceOptions"
                        :option-name="option => `${option.code ? option.code : ''} - ${option.description}`"
                        :include-empty-option="true"
                        :readonly="props.readonly"
                        :errors="props.sourceErrors"
                        data-cy="reason-for-change-source"
                    />
                </input-label>
            </div>
            <div class="qv-flex-grow">
                <input-label label="Reason For Change" label-style="qv-sale-input-label" >
                    <form-text-area
                        :shouldAutoGrow="true" :rows="1"
                        maxlength="100"
                        v-model="rfc.reason"
                        :readonly="props.readonly"
                        :errors="props.reasonErrors"
                        class="qv-h-full"
                        data-cy="reason-for-change-reason"
                    />
                </input-label>
            </div>
        </div>
    </div>
</template>

<template>
    <multiselect
        :value="selectedValue"
        :options="opts"
        :label="label"
        :custom-label="getLabelFunction"
        track-by="code"
        :multiple="multiple"
        :taggable="taggable"
        :allow-empty="allowEmpty"
        select-label="⏎ select"
        deselect-label="⏎ remove"
        :loading="!classificationsLoaded"
        :limit="limit"
        :placeholder="placeholder"
        :disabled="disabled || shouldDisable"
        @select="change"
        @input="input"
        @remove="unset"
        ref="multiSelect"
        :class="{ 'qv-multiselect-error': errors && errors.length > 0 }"
    >
        <template
            slot="singleLabel"
            slot-scope="props"
            v-if="!shouldDisable"
        >
            <span>{{ singleLabelFunction(props.option) }}</span>
        </template>
        <template
            slot="tag"
            slot-scope="props"
            v-if="!shouldDisable"
        >
            <span class="multiselect__tag">
                <span v-text="props.option[singleLabel]" />
                <i
                    aria-hidden="true"
                    tabindex="1"
                    class="multiselect__tag-icon"
                    @keypress.enter.prevent="props.remove(props.option)"
                    @mousedown.prevent="props.remove(props.option)"
                />
            </span>
        </template>
    </multiselect>
</template>

<script>
import Multiselect from 'vue-multiselect';
import dropDownMixin from './DropDownMixin'

export default {
    name: 'classification-dropdown',
    components: {
        Multiselect,
    },
    mixins: [dropDownMixin],
    props: {
        id: {
            type: String,
            default: '',
        },
        category: {
            type: String,
            required: true,
        },
        value: {
            type: [Object, Array, String, Number],
            default: () => null,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
        taggable: {
            type: Boolean,
            default: false,
        },
        allowEmpty: {
            type: Boolean,
            default: true,
        },
        label: {
            type: String,
            default: 'description',
        },
        singleLabel: {
            type: String,
            default: 'description',
        },
        labelFunction: {
            type: Function,
            default: (opt) => {
                if (opt.code === opt.description) return opt.code;
                return `${opt.code} — ${opt.description}`;
            },
        },
        singleLabelFunction: {
            type: Function,
            default: opt => opt.description,
        },
        sortFunction: {
            type: Function,
            default: undefined,
        },
        hideCodes: {
            type: Boolean,
            default: false,
        },
        limit: {
            type: Number,
            default: undefined,
        },
        filterOptionsFunction: {
            type: Function,
            default: null,
        },
        placeholder: {
            type: String,
            default: null,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        errors: {
            type: Array,
            default: () => [],
        },
    },
    watch: {
        'opts': {
            immediate: true,
            handler(newVal, oldVal) {
                if(newVal && newVal.length > 0)
                    this.$nextTick(() => {
                        this.bindMultiSelect(this.$refs.multiSelect);
                    });
          	}
        },
    },
    computed: {
        classificationsLoaded() {
            return this.$store.getters.classificationsLoaded;
        },
        opts() {
            if (!this.classificationsLoaded) return [];
            if (!this.category) return [];
            const classifications = this.$store.getters.getCategoryClassifications(this.category);
            if (!classifications) throw new Error(`Couldn't find classification for ${this.category}`);
            if (this.filterOptionsFunction) {
                return (this.filterOptionsFunction([...classifications].sort(this.getSortFunction)));
            }
            return [...classifications].sort(this.getSortFunction);
        },
        getSortFunction() {
            const self = this;
            function defaultSort(a,b) {
                // Use sort order if different
                if (a.sortOrder !== b.sortOrder) {
                    return a.sortOrder - b.sortOrder;
                }
                // Use description order if hiding codes
                if(self.hideCodes) {
                    if (a.description < b.description) {
                        return -1;
                    }
                    return 1;
                }
                // Otherwise use codes
                if (a.code < b.code) {
                    return -1;
                }
                return 1;
            }

            if(this.sortFunction) {
                return this.sortFunction;
            }

            return defaultSort;
        },
        getLabelFunction() {
            function descriptionLabel(opt) {
                return `${opt.description}`;
            }

            /* If hiding codes then use the description only */
            if (this.hideCodes) return descriptionLabel;

            /* In all other cases use label function provided or code + description by default */
            return this.labelFunction;
        },
        selectedValue() {
            if (this.multiple) {
                return this.opts.filter(
                    c => this.value && this.value.find(val => val && val.code === c.code),
                );
            }
            return this.opts.find(c => this.value && this.value.code === c.code);
        },
        shouldDisable() {
            return !this.opts || this.opts.length === 0 || !this.classificationsLoaded;
        }
    },
    methods: {
        change(value) {
            if (this.multiple) return;
            this.$emit('input', {
                value,
                id: this.id,
            });
            /* HACK Work around to close the dropdown if the user selects a single value. */
            var self = this;
            this.$nextTick(() => {
                setTimeout(function() {
                    self.$refs.multiSelect.deactivate();
                },10);
            });
        },
        unset() {
            if (this.multiple) return;
            this.$emit('input', {
                value: null,
                id: this.id,
            });
            /* HACK Work around to close the dropdown if the user selects a single value. */
            var self = this;
            this.$nextTick(() => {
                setTimeout(function() {
                    self.$refs.multiSelect.deactivate();
                },10);
            });
        },
        input(value) {
            if (!this.multiple) return;
            this.$emit('input', {
                value,
                id: this.id,
            });
        }
    }
};
</script>

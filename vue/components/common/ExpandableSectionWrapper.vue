<template>
    <div class="qvtd-rural-expander">
        <expander
            v-if="expandable"
            class="righty"
            v-model="expanded"
            displayMode="light"
            data-cy="expander"
        />
        <h3 class="section-title" :data-cy="titleDataCy">
            {{ title }}
            <div v-if="hasButtons" class="righty">
                <ul>
                    <slot name="buttons"></slot>
                </ul>
            </div>
        </h3>
        <slot v-if="expanded">Put a component in me.</slot>
    </div>
</template>

<script>
export default {
    components: {
        'expander': () => import(/* webpackChunkName: "Expander" */ '../common/Expander.vue'),
    },
    props: {
        title: {
            type: String,
        },
        titleDataCy: {
            type: String,
            required: false,
            default: '',
        },
        expandable: {
            type: Boolean,
            default: true,
            required: false,
        },
        initialState: {
            type: String,
            default: 'expanded',
            required: false,
        },
    },
    data() {
        return {
            expanded: this.initialState === 'expanded',
        };
    },
    computed: {
        hasButtons() {
            return !!this.$slots['buttons'];
        },
    },
};

</script>

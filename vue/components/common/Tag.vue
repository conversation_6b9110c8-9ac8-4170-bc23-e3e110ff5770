<script setup>
import MaterialIcon from 'Common/MaterialIcon.vue';

const {icon, type, size} = defineProps({
    type: {
        required: false,
        default: 'success',
    },
    size: {
        required: false,
        default: 'base',
    },
    icon: {
        required: false,
        default: null,
    },
});
</script>

<template>
    <div :class="[`qv-bg-${type}`, `qv-tag-${size}`]" class="qv-flex-row qv-gap-1 qv-justify-center qv-align-center">
        <material-icon class="qv-color-light qv-text-md" v-if="icon" :icon="icon"/>
        <p class="qv-color-light qv-font-semibold qv-text-sm">
            <slot />
        </p>
    </div>
</template>

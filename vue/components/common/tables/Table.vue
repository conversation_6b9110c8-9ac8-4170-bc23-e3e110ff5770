<template>
    <div class="table">
        <slot></slot>
    </div>
</template>

<script>
export default {
    name: 'table-body',
    props: {
        fieldName: String,
        fieldValue: String,
        bold: {
            type: Boolean,
            default: false,
        },
    },
};
</script>


<style lang="scss" scoped>
.table {
    display: flex;
    flex-direction: column;
    background-color: white;
    box-shadow: 0px 0px 8px rgba(114, 114, 113, 0.2);
    border-radius: 0.3rem;
    margin: 5px;
    overflow: hidden;
}
</style>

<template>
    <div class="table-row">
        <p :class="`col-${nameColWidth}`" v-if="fieldName">{{ fieldName }}</p>
        <p :class="`col-${nameColWidth}`" v-else>&nbsp;</p>
        <div v-if="!fieldValues" :class="`col-${valueColWidth}`">
            <p v-if="!fieldValue && emptyValue">&nbsp;</p>
            <p v-else-if="!fieldValue">-</p>
            <p v-else>{{ fieldValue }}</p>
        </div>
        <div v-else :class="`col-${valueColWidth} multi-values`">
            <div v-for="(value, index) in fieldValues" :key="index" :class="`col-${multiColWidth}`">
                <p v-if="!value">-</p>
                <p v-else>{{ value }}</p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'table-row',
    props: {
        fieldName: {
            type: String,
        },
        fieldValue: {
            type: String,
        },
        fieldValues: {
            type: Array,
            default: null,
        },
        bold: {
            type: Boolean,
            default: false,
        },
        emptyValue: {
            type: Boolean,
            default: false,
        },
        defaultColWidth: {
            type: Number,
            default: 4,
        },
    },
    computed: {
        multiColWidth() {
            return 12 / this.fieldValues.length;
        },
        valueColWidth() {
            if (this.fieldValues) {
                return this.defaultColWidth + this.fieldValues.length;
            }

            return this.defaultColWidth;
        },
        nameColWidth() {
            return 12 - this.defaultColWidth;
        },
    },
};
</script>

<style lang="scss">
$indent-levels: 6;
$indent-size: 5px;

@mixin indent-x {
    @for $i from 1 through $indent-levels {
        .indent-#{$i} {
            p:first-child {
                margin-left: $indent-size * $i;
            }
        }
    }
}

@include indent-x;

.bold {
    p {
        font-weight: bold;
    }
}

.table-row {
    display: flex;
    flex-direction: row;
    padding: 3px;
    color: var(--color-darkblue-700);
    border-bottom: 0.5px solid transparentize(black, 0.90);

    &.header, &.footer, &.footer-total {
        background: var(--color-blue-700);
        border-bottom-color: transparent;
        color: white;

        p {
            font-weight: bold;
        }
    }

    p {
        font-size: 1.1rem;
        padding: 0.5rem;
        cursor: unset;
    }

    p:first-child {
        flex-grow: 1;
    }

    .multi-values {
        display: flex;
    }
}
</style>

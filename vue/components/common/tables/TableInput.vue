<template>
    <div class="input-wrapper" :class="{invalid: invalid}">
        <span v-if="label" class="label">{{ label }}</span>
        <div class="input-group">
            <input
                v-bind="$attrs"
                :type="typeLookup[type]"
                :value="useDisplayValue ? displayValue : value"
                :disabled="disabled"
                @focusin="handleFocusIn"
                @focusout="useDisplayValue = true"
                @change="updateValue($event.target.value)"
            >
        </div>
    </div>
</template>

<script>
import formatUtils from '@/utils/FormatUtils';

export default {
    name: 'table-input',
    mixins: [formatUtils],
    props: {
        label: {
            type: String,
            required: false,
        },
        value: {
            type: [String, Number, Boolean],
            required: false,
        },
        type: {
            type: String,
            default: 'text',
            required: false,
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        invalid: {
            required: false,
            default: false,
        },
        nullEmptyValues: {
          required: false,
          default: false,
        },
        selectOnFocus: {
            required: false,
            default: false,
        }
    },
    data() {
        return {
            typeLookup: {
                number: 'number',
                money: 'text',
                area: 'number',
                text: 'text',
            },
            useDisplayValue: true,
        };
    },
    computed: {
        displayValue() {
            if (this.value === null) {
                return '';
            }

            if (this.type === 'money') {
              return this.formatPrice(this.value, '$0,0');
            }

            if (this.type === 'area') {
              return this.formatDecimal(this.value, 4)
            }

            return this.value;
        },
    },
    methods: {
        updateValue(newValue) {
            if (this.type === 'money' || this.type === 'number' || this.type === 'area') {
                newValue = this.toNumber(newValue);
            }

            if (this.type === 'money' && newValue) {
                newValue = parseFloat(newValue.toFixed(1));
            }

            this.$emit('update:value', newValue);
        },
        clearEmptyValues() {
          if (this.nullEmptyValues && !this.value && this.value !== 0) {
            this.$emit('update:value', null);
          }
        },
        handleFocusIn(event) {
            this.useDisplayValue = false;
            if (this.selectOnFocus) {
                this.$nextTick(() => {
                    event.target.select();
                });
            }
        }
    },
  watch: {
    'value': function (val) {
      this.clearEmptyValues()
    }
  },
  mounted() {
    this.clearEmptyValues();
  }
};
</script>

<style lang="scss" scoped>
.input-wrapper {
    display: flex;
    flex-direction: column;

    .label {
        font-size: 1.1rem;
        line-height: 1.6;
        color: var(--color-blue-600);

        height: 2.2rem;
        display: block;
    }

    &.invalid {
        input {
            border-color: var(--color-red-400);
            border-width: 2px;
        }
    }
}

.input-group {
    position: relative;
    display: inline-flex;
    height: 100%;

    input {
        font-size: 1.2rem;
        padding: 0.5rem;
        border-radius: 5px;
        height: 100%;
        min-height: 3.25rem;
        border: 1px solid #d2d2d2;
        background-color: #fff;
        margin: 0;
        width: 100%;
        min-width: 50px;

        &:disabled {
            color: var(--color-blue-700);
            background-color: var(--color-lightblue-300);
            box-shadow: 0 0 0 1px var(--color-lightblue-500);
            border: none;
        }
    }
}
</style>

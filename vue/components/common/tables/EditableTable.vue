<template>
    <div class="editable-table">
        <div class="title-wrapper">
            <div class="title">
                <slot name="title"/>
            </div>
            <div class="title-right">
                <slot name="title-right"/>
                <expander
                    v-if="collapsable"
                    v-model="collapsed"
                    displayMode="dark"
                    data-cy="expander"
                />
            </div>
        </div>
        <transition>
            <div class="editable-table-rows" v-show="!collapsed">
                <div class="editable-row-wrapper">
                    <div class="editable-row-header">
                        <div class="editable-row-columns" :class="{'compact': !showTotal && showTotalColumn}">
                            <slot name="columns"/>
                        </div>
                        <div v-if="showTotalColumn && showTotal" class="editable-row-total-column">
                            <p>
                                <slot name="total-column"/>
                            </p>
                        </div>
                    </div>
                </div>
                <slot/>
            </div>
        </transition>
    </div>
</template>

<script>
import Expander from 'Common/Expander.vue';

export default {
    name: 'editable-table',
    components: {
        Expander,
    },
    props: {
        title: {
            type: String,
        },
        collapsable: {
            type: Boolean,
            default: false,
            required: false,
        },
        startCollapsed: {
            type: Boolean,
            default: false,
            required: false,
        },
        showTotalColumn: {
            type: Boolean,
            default: true,
            required: false,
        },
        showTotal: {
          type: Boolean,
          required: false,
          default: true,
        }
    },
    data() {
        return {
            collapsed: this.startCollapsed,
        };
    },
    methods: {},
};
</script>

<style lang="scss">
@import '~@/_helpers.scss';

$border-radius: 0.3rem;

.v-enter-active, .v-leave-active {
    transition: all 0.25s;
    overflow: hidden;
    max-height: 50rem;
}

.v-enter-from, .v-leave-to {
    max-height: 0;
}

.editable-table {
    margin-bottom: 3rem;

    .title-wrapper {
        @extend .flex-row;
        justify-content: space-between;
        width: 100%;
    }

    .title {
        font-weight: 600;
        font-size: 1.75rem;
        color: var(--color-blue-900);
        margin-top: auto;
        width: 100%;
    }

    .title-right {
        display: flex;
        gap: 3rem;

        > * {
            margin-top: auto;
        }
    }

    .editable-table-rows {
        @extend .flex-table;
        margin-top: 1rem;

        .editable-row-wrapper {
            &:first-child {
                .editable-row, .editable-row-header {
                    border-top-right-radius: $border-radius;
                    border-top-left-radius: $border-radius;
                }
            }

            &:last-child {
                .editable-row, .editable-row-header {
                    border-bottom-right-radius: $border-radius;
                    border-bottom-left-radius: $border-radius;
                }
            }
        }

        .editable-row {
            @extend .flex-row;
            background: var(--color-lightblue-200);
            font-size: 1.2rem;
            min-height: 53.5px;

            .editable-row-columns {
                @extend .flex-row;
                padding: 1rem;
                gap: 1rem;
                border: var(--color-lightblue-300) 1px solid;
                border-top: 0;
                border-right: 0;
                align-items: center;
              
              &.compact {
                margin-right: 10rem;
              }
            }

            .editable-row-total-column {
                @extend .flex-row;
                padding: 1rem;
                width: 35rem;
                background: var(--color-lightblue-400);
            }
        }

        .editable-row-header {
            @extend .editable-row;
            color: white;
            background-color: var(--color-blue-900);
            overflow: hidden;
            min-height: unset;

            .editable-row-columns {
                border: none;
            }

            .editable-row-total-column {
                background-color: inherit;

                p {
                    width: 100%;
                    text-align: center;
                }
            }
        }
    }
}
</style>

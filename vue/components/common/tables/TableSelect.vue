<template>
  <div class="select" tabindex="0" @focusout="open = false" :class="{invalid: invalid}">
    <div class="selected" :class="{ open: open && !disabled, disabled: disabled}" @click="open = !open">
      {{ selectedDisplayValue }}
    </div>
    <div class="items" :class="{ hide: !open }" v-if="!disabled">
      <div v-for="(option, key) of [{id: null, code: ' ', description: ' '},...options]"
           v-if="shouldDisplayOption(option)" :key="key" @click="select(option)">
        &nbsp;{{ option[descriptionKey] }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'table-select',
  props: {
    options: {
      type: Array,
      required: true,
    },
    value: {
      required: false,
    },
    descriptionKey: {
      required: true,
    },
    uniqueSelections: {
      required: false,
      default: false,
    },
    invalid: {
      required: false,
      default: false,
    },
    isObject: {
      required: false,
      default: false,
    },
    disabled: {
      required: false,
      default: false,
    }
  },
  data() {
    return {
      open: false,
      selected: null,
    };
  },
  computed: {
    selectedDisplayValue() {
      if (this.selected) {
        return this.selected[this.descriptionKey];
      }

      return '';
    },
  },
  watch: {
    options: {
      handler: function (val, oldVal) {
        this.optionsChanged();
      },
    },
    value: {
      handler: function (val, oldVal) {
        this.optionsChanged();
      },
    },
  },
  methods: {
    select(option) {
      if (this.uniqueSelections && this.selected) {
        this.selected.selected = false;
      }

      this.selected = option;
      this.selected.selected = true;
      this.$emit('update:value', option.id);
      this.$emit('update');
      this.open = false;
    },
    optionsChanged() {
      if (this.uniqueSelections && this.selected != null) {
        this.selected.selected = false;
      }

      this.selected = this.options.find(o => {
        return o.id === this.value;
      });

      if (this.selected) {
        this.selected.selected = true;
      }
    },
    shouldDisplayOption(option) {
      return this.uniqueSelections === true ? option.selected !== true : true
    }
  },
  mounted() {
    this.optionsChanged();
  },
};
</script>

<style scoped lang="scss">

$highlight-color: var(--color-lightblue-600);

.select {
  position: relative;
  width: 100%;
  text-align: left;
  outline: none;
  height: 3.25rem;
  line-height: 47px;

  &.invalid {
    .selected {
      border-color: var(--color-red-400);
      border-width: 2px;
    }
  }
}

.select .selected {
  height: 100%;
  border-radius: 5px;
  border: 1px solid #d2d2d2;
  background-color: white;
  padding: 0.5rem;
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
  overflow: hidden;

  &.disabled {
    color: var(--color-blue-700);
    background-color: var(--color-lightblue-300);
    box-shadow: 0 0 0 1px var(--color-lightblue-500);
    border: none;
  }
}

.select .selected.open {
  border: 1px solid $highlight-color;
  border-radius: 6px 6px 0px 0px;
}

.select .selected:after {
  position: absolute;
  content: "";
  top: 1.45rem;
  right: 1em;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-color: #d2d2d2 transparent transparent transparent;
}

.disabled:after {
  content: none !important;
}

.select .items {
  border-radius: 0px 0px 5px 5px;
  overflow: hidden;
  border-right: 1px solid $highlight-color;
  border-left: 1px solid $highlight-color;
  border-bottom: 1px solid $highlight-color;
  position: absolute;
  background-color: white;
  left: 0;
  right: 0;
  z-index: 1;
}

.select .items div {
  padding: 0.5rem;
  cursor: pointer;
  user-select: none;
}

.select .items div:hover {
  color: white;
  background-color: $highlight-color;
}

.hide {
  display: none;
}
</style>

<script setup>
const props = defineProps({
    iconStyle: {
        type: String,
        required: false,
        default: () => "",
        validator(value) {
            return ["outlined", "", "round"].includes(value);
        }
    },
    /** Google Material Icon name**/
    icon: {
        type: String,
        required: true,
    }
});
</script>
<template>
    <span :class="`material-icons${iconStyle ? `-${iconStyle}` : ''}`">
        {{icon}}
    </span>
</template>

<template>
    <div :class="classes" data-backdrop="static" data-keyboard="false">
        <div class="alert mdl-shadow--24dp" data-cy="alert-modal">
            <slot #default>
                <h1>Default Message</h1>
                <p>Don't forget about slots!</p>
            </slot>
            <ul class="alertButtons">
                <slot name="buttons">
                    <modal-button primary @click="close" data-cy="modal-close-button">
                        Close
                    </modal-button>
                </slot>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    components: {
        'modal-button': () => import(/* webpackChunkName: "ModalButton" */ './ModalButton.vue'),
    },
    props: {
        warning: {
            type: Boolean,
            default: false,
        },
        caution: {
            type: Boolean,
            default: false,
        },
        success: {
            type: Boolean,
            default: false,
        },
        info: {
            type: Boolean,
            default: true,
        }
    },
    computed: {
        classes() {
            const type = this.warning ? 'warning'
                : this.caution ? 'caution'
                : this.success ? 'success'
                : 'info';
            return {
                alertWrapper: true,
                modal: true,
                [type]: type
            };
        },
    },
    methods: {
        close() {
            this.$emit('close');
        },
    },
};
</script>

<style lang="scss" scoped>
.success {

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        color: green;
    }
}

.caution {

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        color: #db9514;
    }
}

.warning {

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        color: #ea2e2d;
    }
}

.info {

h1,
h2,
h3,
h4,
h5,
h6 {
    color: rgb(74,144,226);
   }
}

.alert {
    width: 60rem !important;
    min-height: 30rem;
    height: unset;
    max-height: 75rem;
}

.alertButtons {
    display: block;
}
</style>

<script setup>

const props = defineProps({
  title: {
    type: String,
    default: 'Attention Required',
  },
  message: {
    type: [String, Array],
    default: '',
  },
  confirmText: {
    type: String,
    default: 'Confirm',
  },
  cancelText: {
    type: String,
    default: 'Cancel',
  },
  showCancelBtn: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['close']);
</script>

<template>
  <div class="qv-flex-column qv-flex-grow qv-justify-space-between" data-cy="report-modal">
    <div class="qv-dialog-content">
      <h1 class="cw-color-caution qv-text-lg" data-cy="cw-dialog-title" style="margin-bottom: 1rem;">{{ title }}</h1>
      <p class="cw-modal-message" data-cy="cw-modal-message">{{ message }}</p>
    </div>
    <div :class="{ 'qv-flex-row qv-justify-space-between': showCancelBtn }">
      <button v-if="showCancelBtn" class="qv-dialog-button qv-color-dark qv-bg-lightbuff" @click="() => emit('close',false)" data-cy="report-modal-cancel">{{ cancelText }}</button>
      <button style="float:right" class="qv-dialog-button qv-color-light qv-bg-mediumblue" @click="() => emit('close',true)" data-cy="report-modal-confirm">{{ confirmText }}</button>
    </div>
  </div>
</template>
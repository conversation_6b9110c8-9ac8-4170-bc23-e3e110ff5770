<template>
    <li @click="click" :class="classes"><slot></slot></li>
</template>

<script>
export default {
    props: {
        id: {
            type: String,
            default: null
        },
        primary: {
            type: Boolean,
        },
    },
    computed: {
        classes() {
            return {
                'mdl-button--primary': this.primary,
                'mdl-button--raised': this.primary || this.warning,
                'mdl-button': true,
                'mdl-button--mini': true,
            };
        },
    },
    methods: {
        click() {
            this.$emit('click', this.id);
        },
    },
}
</script>

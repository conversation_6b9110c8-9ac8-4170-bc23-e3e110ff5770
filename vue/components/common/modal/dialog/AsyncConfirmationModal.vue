<script setup>
/**
 * Base Component for an Asynchronous Modal
 * Create variants if more specific control or display is required.
 */
import useModal from '@/composables/useModal';

const props = defineProps({
  isError: {
    default: false,
  },
  isWarning: {
    default: false,
  },
  title: {
    type: String,
    default: 'Attention Required',
  },
  message: {
    type: [String, Array],
    default: '',
  },
  confirmText: {
    type: String,
    default: 'Confirm',
  },
  cancelText: {
    type: String,
    default: 'Cancel',
  },
  cancelErrorText: {
    type: String,
    default: 'Close',
  },
  onlyConfirm: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close']);
</script>

<template>
  <div class="qv-flex-column qv-flex-grow qv-justify-space-between">
    <div class="qv-dialog-content">
      <h1 :class="{ 'qv-color-error': isError || isWarning }" class="qv-text-lg" style="margin-bottom: 1rem;" data-cy="dialog-title">{{ title }}</h1>
      <p v-if="typeof(message) === 'string'" class="qv-text-base">{{ message }}</p>
      <p v-for="m in message" v-else-if="Array.isArray(message)" class="qv-text-base">{{ m }}</p>
      <p v-else>Don't know how to handle this payload</p>
    </div>
    <div :class="{'qv-flex-row qv-justify-space-between': !isError, 'qv-justify-end': onlyConfirm }">
      <button v-if="!onlyConfirm" class="qv-dialog-button qv-color-dark qv-bg-lightbuff" @click="() => emit('close',false)" data-cy="button-dialog-cancel">{{ isError ? cancelErrorText : cancelText }}</button>
      <button v-if="!isError" class="qv-dialog-button qv-color-light qv-bg-mediumblue" @click="() => emit('close',true)" data-cy="button-dialog-confirm">{{ confirmText }}</button>
    </div>
  </div>
</template>
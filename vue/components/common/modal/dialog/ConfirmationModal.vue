<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false,
    },
    dialogTitle: {
        type: String,
        default: 'Attention Required',
    },
    warning: {
        type: Boolean,
        default: false,
    },
    caution: {
        type: Boolean,
        default: false,
    },
    confirmDisabled: {
        type: Boolean,
        default: false,
    },
    confirmText: {
        type: String,
        default: 'Confirm',
    },
    confirmClass: {
        type: String,
        default: '',
    },
    cancelText: {
        type: String,
        default: 'Cancel',
    },
    showCancelButton: {
        type: Boolean,
        default: true,
    },
    showConfirmButton: {
        type: Boolean,
        default: true,
    },
    shouldConfirmClose: {
        type: Boolean,
        default: true,
    },
});
const emit = defineEmits(['close', 'confirm']);

watch(() => props.isOpen, (isOpen) => {
    if (isOpen) {
        dialog.value.showModal();
    }
    else {
        dialog.value.close();
    }
});

const dialog = ref(null);

function closeDialog() {
    emit('close');
}
function confirm() {
    emit('confirm');
    if (props.shouldConfirmClose) {
        emit('close');
    }
}

function clickToClose(e) {
    const dialogDimensions = dialog.value.getBoundingClientRect();
    if (
        e.clientX < dialogDimensions.left
        || e.clientX > dialogDimensions.right
        || e.clientY < dialogDimensions.top
        || e.clientY > dialogDimensions.bottom
    ) {
        emit('close');
    }
}
</script>

<template>
    <dialog ref="dialog" class="qv-dialog" @click="clickToClose($event)">
        <div class="qv-dialog-content qv-mb-3">
            <h1 :class="{ 'warning': props.warning, 'caution' : props.caution }" style="margin-bottom: 1rem;">{{ props.dialogTitle }}</h1>
            <slot />
        </div>
        <div :class="{ 'qv-flex-row qv-justify-space-between': props.showCancelButton }">
            <button
                class="qv-dialog-button qv-color-dark qv-bg-lightbuff" @click="closeDialog"
                v-if="props.showCancelButton">{{ cancelText }}</button>
            <button
                v-if="props.showConfirmButton"
                class="qv-dialog-button qv-color-light qv-bg-mediumblue" :class="props.confirmClass" :disabled="props.confirmDisabled"
                @click.stop="confirm" data-cy="button-modal-confirm">{{ confirmText }}</button>
        </div>
    </dialog>
</template>

<style lang="scss" scoped>
// TODO: Should not be in component styling
.qv-dialog[open] {
    border: none;
    z-index: 421;
    position: fixed;
    width: 60rem;
    min-height: 30rem;
    max-height: 75rem;
    border: none;
    border-radius: 3px;
    box-shadow: 10px 10px 500px 100px rgba(0, 0, 0, 0.2), 0 11px 15px -7px rgba(0, 0, 0, 0.12), 0 24px 38px 3px rgba(0, 0, 0, 0.2);

    display: flex;
    flex-direction: column;
    justify-content: space-between;

    button:hover {
        opacity: 0.8;
    }

    h1 {
        font-size: large;
    }
}

.qv-dialog-button {
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    text-transform: uppercase;
    line-height: 1;
    line-height: 36px;
    border: none;
    border-radius: 2px;
    vertical-align: middle;
    padding: 0 16px;
    margin: 0;
    min-width: 8.5rem;
    height: 36px;
}

.qv-dialog-button:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.warning {
    color: var(--qv-color-error);
}

.caution {
    color: var(--qv-color-orange);
}
</style>

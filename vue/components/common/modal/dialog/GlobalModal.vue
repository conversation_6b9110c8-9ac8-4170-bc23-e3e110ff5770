<script setup>
import useModal from '@/composables/useModal';
const {
    dialog,
    payload,
    modal,
    close,
    clickToClose,
} = useModal();
</script>

<template>
  <dialog ref="dialog" class="qv-dialog" @click="clickToClose($event)" data-cy="global-modal">
    <component :is="modal" v-bind="payload" @close="(response) => close(response)"/>
  </dialog>
</template>

<style lang="scss" scoped>
// TODO: Should be global styling
.qv-dialog[open] {
  border: none;
  z-index: 421;
  position: fixed;
  width: 60rem;
  min-height: 30rem;
  max-height: 75rem;
  border-radius: 3px;
  box-shadow: 10px 10px 500px 100px rgba(0, 0, 0, 0.2), 0 11px 15px -7px rgba(0, 0, 0, 0.12), 0 24px 38px 3px rgba(0, 0, 0, 0.2);

  display: flex;
  flex-direction: column;
  justify-content: space-between;

  button:hover {
    opacity: 0.8;
  }

  .qv-dialog-button {
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    text-transform: uppercase;
    line-height: 36px;
    border: none;
    border-radius: 2px;
    vertical-align: middle;
    padding: 0 16px;
    margin: 0;
    min-width: 8.5rem;
    height: 36px;
  }

  .qv-dialog-button:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
}
</style>

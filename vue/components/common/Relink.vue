<template>
    <div id="relink" class="relink alertWrapper modal" data-backdrop="static" data-keyboard="false" style="display: none;">
        <div class="alert mdl-shadow--24dp">
            <h3>Re-link Photo</h3>
            <div class="relinkPhoto">
            <span class="primaryThumb-Wrapper">
                <img class="image" :src="thumbnail">
            </span>
                <ul class="relinkControls">
                    <li>
                        <div class="relinkQPID">
                            <i class="material-icons md-dark">&#xE88A;</i>
                            <input id="" class="relink-box" name="" type="text" value="" required placeholder="Enter QPID" autocapitalize="off" autocomplete="off" autocorrect="off">
                            <button class="relink-close-icon close-icon mdl-button mdl-js-button mdl-button--icon" type="reset">
                            </button>
                            <div class="relink-validation"></div>
                        </div>
                    </li>
                    <li>
                        <div class="relinkQupid">
                            <input type="checkbox" id="primaryPhotoRelink">
                            <label for="primaryPhotoRelink"><span>Primary photo</span>Use this photo as the main image for the linked property</labeL>
                        </div>
                    </li>
                </ul>
            </div>
            <ul class="alertButtons">
                <li @click="cancel()" id="relinkCancel" class="mdl-button mdl-button--mini"><a href="#">Cancel</a></li>
                <li @click="relink()" id="relinkRelink" class="mdl-button mdl-button--mini"><a href="#">Re-link</a></li>
            </ul>
        </div>
    </div>
</template>
<script>
    export default {
        props: ['thumbnail', 'okHandler'],
        methods: {
            cancel: function () {
                $('.relink').hide();
            },
            relink: function() {
                var self = this;
                $("#relinkCancel").addClass('disabled');
                $("#relinkCancel").removeClass('active');
                $("#relinkRelink").addClass('disabled');
                $("#relinkRelink").removeClass('active');
                self.okHandler();
            }
        }
    }
</script>

<template>
    <a href="#" :title="active ? currentDirectionTitleText : undefined" class="qvtd-sort-header" :class="classNames" @click.prevent="onClick">
        <span v-if="active" class="icon">
            <i :class="['sorter', 'material-icons', 'md-18', currentDirectionClass]">{{ currentIcon }}</i>
        </span>
        <template v-if="label">{{ label }}</template>
        <slot v-else />
    </a>
</template>

<script>
const UP_ICON = '';
const DOWN_ICON = '';

export default {
    props: {
        direction: {
            type: String,
            default: 'ASC',
        },
        label: {
            type: String,
            default: '',
        },
        columnName: {
            type: String,
            default: null,
        },
        active: {
            type: Boolean,
        },
        directionTitleTextPrefix: {
            type: String,
            default: ''
        }
    },
    computed: {
        currentIcon() {
            return this.direction === 'ASC' ? UP_ICON : DOWN_ICON;
        },
        currentDirectionClass() {
            return this.direction === 'ASC' ? 'down' : 'up';
        },
        currentDirectionTitleText() {
            let directionTitleText = this.direction === 'ASC' ? 'sort ascending' : 'sort descending';

            if (this.directionTitleTextPrefix !== ''){
                directionTitleText += ' ' + this.directionTitleTextPrefix;
            }
            return directionTitleText;
        },
        classNames() {
            const classes = [];
            if (this.active) {
                classes.push('active');
            }
            if (!this.columnName) {
                classes.push('not-sortable');
            }
            return classes;
        },
    },
    methods: {
        onClick() {
            if (!this.columnName) {
                return;
            }
            if (this.direction === 'ASC') {
                this.$emit('onchange', { columnName: this.columnName, direction: 'DESC' });
            }
            else {
                this.$emit('onchange', { columnName: this.columnName, direction: 'ASC' });
            }
        },
    },
};
</script>

<style lang="scss">
.qvtd-sort-header {
    a {
        line-height: 1.2;
    }

    a.not-sortable {
        text-decoration: none;
        cursor: default;

        &:hover {
            color: rgba(0, 0, 0, .54);
            opacity: 1;
            text-decoration: none;
        }
    }

    a.not-sortable > span.icon {
        display: none;
    }

    .conscomp {
        white-space: pre-line;
    }
}
</style>

<template>
    <div class="warning alertWrapper modal" data-backdrop="static" data-keyboard="false" style="display: none">
        <div class="alert mdl-shadow--24dp">
            <h3 id="errorHeader">{{ header }}</h3>
            <p id="errorMessage1" v-show="!Array.isArray(message)">{{ message }}</p>
            <p id="errorMessage2" v-show="Array.isArray(message)" v-for="msg,key in message">{{ msg }}</p>
            <ul class="alertButtons">
                <li @click="cancel()" id="errorCancel" class="mdl-button mdl-button--mini">{{ close }}</li>
            </ul>
        </div>
    </div>
</template>
<script>
    export default {
        props: ['header', 'message', 'close'],
        methods: {
            cancel: function () {
                $('.warning').hide();
            }
        }
    }
</script>

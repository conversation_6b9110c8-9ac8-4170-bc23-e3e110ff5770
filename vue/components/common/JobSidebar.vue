<script setup>
import { formatDate } from '@/utils/FormatUtils';
import { computed } from 'vue';
import { store } from '@/DataStore';

const props = defineProps({
    isExternalUser: {
        type: Boolean,
        default: true,
    },
    lastUpdatedBy: {
        type: Object,
        default: () => ({
            name: '',
            date: '',
        }),
    },
    labels: {
        type: Array,
        default: () => [],
    },
    warnings: {
        type: Array,
        default: () => [],
    },
    shouldHideWarnings: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['labelClicked']);

const isInternalUser = computed(() => store.state.userData.isInternalUser);

</script>

<template>
    <div>
        <div v-if="isInternalUser && props.lastUpdatedBy.name">
            <h3 class="section-title" style="">Last updated by</h3>
            <div class="qv-icon-row">
                <i class="icons8-edit-user-male qv-icon qv-pt-1" 
                    :class="{'qv-pt-2' : props.lastUpdatedBy.date }"
                />
                <div class="qv-flex-column qv-gap-0">
                    <p>{{ props.lastUpdatedBy.name }}</p>
                    <p v-if="props.lastUpdatedBy.date" class="qv-text-sm">{{ formatDate(props.lastUpdatedBy.date) }}</p>
                </div>
            </div>
        </div>
        <div class="top-buttons">
            <slot name="top-buttons" />
        </div>
        <div v-for="label in props.labels" :key="label.title">
            <div v-if="!label.hide">
                <h3 class="section-title">{{ label.title }}</h3>
                <ul>
                    <li class="job-sidebar md-qivs md-qivs-wide" :class="label.class" @click="emit('labelClicked', label.title)">
                        <label>{{ label.value }}</label>
                    </li>
                </ul>
            </div>
        </div>
        <div v-if="props.warnings.length && !props.shouldHideWarnings">
            <h3 class="section-title">Warnings</h3>
            <ul>
                <li v-for="warning in props.warnings" :key="warning" class="md-qivs danger md-qivs-wide">
                    <label>{{ warning }}</label>
                </li>
            </ul>
        </div>
        <div class="bottom-buttons">
            <slot name="bottom-buttons" />
        </div>
    </div>
</template>

<style lang="scss">

.top-buttons, .bottom-buttons {
    margin: 1rem 0;
    button {
        width: 100%;
    }
}

.qv-icon {
    padding-top: 2px;
}

.job-sidebar label {
    font-size: 1.2rem;
}
</style>

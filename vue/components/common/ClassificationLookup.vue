<template>
    <span>
        {{ output | emptyToDash }}
    </span>
</template>

<script>
export default {
    props: {
        category: {
            type: String,
            required: true,
        },
        value: {
            type: [Object, Array],
            default() {
                return {};
            },
        },
        labelFunction: {
            type: Function,
            default: opt => `${opt && opt.description}`,
        },
        multiple: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        classificationsLoaded() {
            return this.$store.getters.classificationsLoaded;
        },
        opts() {
            if (!this.classificationsLoaded) return [];
            if (!this.category) return [];
            const classifications = this.$store.getters.getCategoryClassifications(this.category);
            if (!classifications) throw new Error(`Couldn't find classification for ${this.category}`);
            return [...classifications].sort(this.sortFunction);
        },

        output() {
            if (!this.value) return '';
            const theOpt = this.opts.find(opt => opt && opt.code === this.value.code?.trim());
            try {
                if (this.multiple) {
                    return this.value.map(this.labelFunction).join(', ') || '—';
                }
                return this.labelFunction(theOpt) || '—';
            } catch (err) {
                console.error(`ClassificationLookup with category ${this.category}'s labelFunction had error`);
                throw err;
            }
        },
    },
};
</script>

<style>

</style>

<script setup>
const props = defineProps(['message']);
</script>

<template>
    <div class="qv-w-full qv-flex-column qv-align-center">
        <div class="loading-spinner"/>
        <p class="qv-text-base qv-font-normal">{{ message }}</p>
    </div>
</template>

<style>
.loading-spinner {
    width: 30px;
    height: 30px;
    background: url('../../../public/images/spinner.gif') no-repeat center center;
    background-size: 30px 30px;
}
</style>

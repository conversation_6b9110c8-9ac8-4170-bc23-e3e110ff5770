<script setup>
import Expander from 'Common/Expander.vue';
import { computed } from 'vue';

const props = defineProps(['value', 'title']);
const emit = defineEmits(['input'])
const expanded = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit('input', value)
  }
});
</script>

<template>
    <div class="mdl-shadow--3dp qv-px-3 qv-py-2">
        <div class="qv-flex-row qv-justify-space-between">
            <div>
                <h3 class="qv-text-md qv-font-normal">{{ title }}</h3>
            </div>
            <slot name="actions"/>
            <expander v-model="expanded"/>
        </div>
        <transition name="expand">
            <div v-show="expanded" class="expand">
                <slot/>
            </div>
        </transition>
    </div>
</template>

<style lang="css">
.expand {
    height: auto;
}

.expand-leave-active {
    transition: max-height 225ms ease-in, opacity 225ms ease-in;
}

.expand-enter-active {
    transition: max-height 225ms ease-out, opacity 225ms ease-out;
}

.expand-enter, .expand-leave-to {
    opacity: 0;
    max-height: 0;

}

.expand-enter-to, .expand-leave {
    opacity: 1;
    max-height: 100rem;
}
</style>

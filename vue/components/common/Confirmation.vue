<template>
    <div :id="id" class="confirmation alertWrapper modal" data-backdrop="static" data-keyboard="false" style="display: none;">
        <div class="alert mdl-shadow--24dp">
            <h3 id="confirmationHeader">{{ header }}</h3>
            <p id="confirmationMessage" v-if="!Array.isArray(message)">{{ message }}</p>
            <p id="confirmationMessage" v-else v-for="msg in message">{{ msg }}</p>
            <ul class="alertButtons">
                <li @click="cancel()" id="confirmationCancel" class="mdl-button mdl-button--mini">{{ cancelLabel }}</li>
                <li @click="confirm()" id="confirmationClose" class="mdl-button mdl-button--mini">{{ okLabel }}</li>
            </ul>
        </div>
    </div>
</template>
<script>
    export default {
        props: ['id', 'header', 'message', "okLabel", 'cancelLabel', 'okHandler', 'cancelHandler'],
        methods: {
            cancel: function () {
                $('#' + this.id).hide();
            },
            confirm: function() {
                var self = this;
                self.okHandler();
                $('#' + this.id).hide();
            }
        }
    }
</script>

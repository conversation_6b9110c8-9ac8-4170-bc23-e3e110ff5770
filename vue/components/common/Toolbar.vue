<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router/composables';
import { store } from '../../DataStore';

const loading = ref(false);
const userData = computed(() => store.state.userData);

const router = useRouter();
const route = useRoute();
const currentTab = computed(() => route.query.tab || 'buildingConsents');
const onRollMaintenancePage = computed(() => router.currentRoute.name === 'roll-maintenance');
const viewBuildingConsents = computed(() => onRollMaintenancePage.value && currentTab.value === 'buildingConsents');
const viewObjections = computed(() => onRollMaintenancePage.value && currentTab.value === 'objections');
const viewLinzSearch = computed(() => onRollMaintenancePage.value && currentTab.value === 'linzSearch');
const viewSalesSearch = computed(() => onRollMaintenancePage.value && currentTab.value === 'salesSearch');
const viewDashboard = computed(() => router.currentRoute.name === 'valuer-metrics');
const viewReports = computed(() => router.currentRoute.name === 'report-dashboard-my-reports');

defineProps({
    title: {
        type: String,
        default: ''
    }
})

function openQIVSLink() {
    window.open(userData.value.qivsUrl, 'QIVS');
}
</script>

<template>
    <div v-if="userData.isInternalUser || userData.isTAUser || userData.externalObjectionAccess" class="taRollMaintenance-toolbar qvToolbar-wrapper">
        <div class="taRollMaintenance-title advSearch-group qvToolbar-leftMenu lefty">
            <span>
                {{ title }}
            </span>
        </div>
        <div class="md-full qvToolbar">
            <div class="qvToolbar-links lefty qv-flex-row">
                <router-link v-if="userData.isInternalUser" class="building-consents qvToolbar-link" :to="{name: 'consents-search'}" :active-class="'active'" data-cy="building-consents-tab">
                    <label>Consents</label>
                </router-link>
                <router-link class="qvToolbar-link" :to="{name: 'objections-search'}" :active-class="'active'" data-cy="objections-tab">
                    <label>Objections</label>
                </router-link>
                <router-link class="linz-search qvToolbar-link" :to="{name: 'linz-search'}" :active-class="'active'" data-cy="linz-search-tab">
                    <label>LINZ Search</label>
                </router-link>
                <router-link v-if="userData.isInternalUser" class="sales-search qvToolbar-link" :to="{name: 'sales-dashboard'}" :active-class="'active'" data-cy="sales-search-tab">
                    <label>Sales Processing</label>
                </router-link>
            </div>
            <ul class="qvToolbar-qivs righty">
                <li class="md-qivs" @click="openQIVSLink"><label>QIVS</label> <i class="material-icons">call_made</i></li>
            </ul>
            <ul class="qvToolbar-links righty">
                <li
                    v-if="userData.isInternalUser"
                    :class="{active: viewDashboard}"
                     @click="router.push({ name: 'valuer-metrics' })"
                    data-cy="metrics-dashboard-tab"
                >
                    <label>My Dashboard</label>
                </li>
                <li
                    :class="{active: viewReports}"
                     @click="router.push({ name: 'report-dashboard-my-reports' })"
                    data-cy="reports-dashboard-tab"
                >
                    <label>Reports</label>
                </li>
            </ul>
        </div>
    </div>
</template>

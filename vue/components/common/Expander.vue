<template>
    <div data-cy="expand-all" class="expandAll"
        :class="{'down' : expandedLocal}"
        @click="expandedLocal = !expandedLocal">
        <span :title="title"  class="mdl-button mdl-js-button mdl-button--icon">
            <i class="material-icons" :class="iconTheme"></i>
        </span>
    </div>
</template>

<script>

export default {
    props: {
        expanded: {
            type: Boolean,
            default: true
        },
        displayMode: {
            type: String,
            default: "dark"
        },
    },
    model: {
        prop: 'expanded',
        event: 'change'
    },
    data(){
        return {
            iconTheme: { 
                'md-light': this.displayMode === 'light', 
                'md-dark': this.displayMode !== 'light' 
            }
        }  
    },
    computed: {
        expandedLocal: {
            get: function() {
                return this.expanded;
            },
            set: function(value) {
                this.$emit('change', value);
            },
        },
        title() {
            return this.expanded ? 'Collapse' : 'Expand'
        }
    },
}
</script>


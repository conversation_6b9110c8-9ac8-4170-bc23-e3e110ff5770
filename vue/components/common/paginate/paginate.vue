<template>
    <paginate
        v-model="thePage"
        :page-range="7"
        :margin-pages="1"
        :container-class="`paginate-component ${paginateStyle}`"
        v-bind="$attrs"
    />
</template>

<script>
export default {
    components: {
        paginate: () => import(/* webpackChunkName: "vuejsPaginate" */ 'vuejs-paginate'),
    },
    model: {
        prop: 'page',
        event: 'change',
    },
    props: {
        page: {
            type: Number,
            required: true,
        },
        paginateStyle: {
            type: String,
            default: '',
        }
    },
    computed: {
        thePage: {
            get() {
                return this.page;
            },
            set(value) {
                this.changeEvent(value);
            },
        },
    },
    methods: {
        changeEvent(value) {
            this.$emit('change', value);
        },
    },
};
</script>

<style lang="scss">
.paginate-component {
    display: block;
    margin: auto;
    font-size: 14px;
    text-align: center;

    li {
        display: inline-block;

        &.disabled {
            pointer-events: none;
            opacity: .8;
        }

        &.active {
            background-color: rgb(74,144,226);
            a {
                color: #fff;
            }
        }

        a {
            display: block;
            text-align: center;
            width: 4em;
            height: 4em;
            padding: 0 2px;
            line-height: 4;
        }
    }
}
</style>

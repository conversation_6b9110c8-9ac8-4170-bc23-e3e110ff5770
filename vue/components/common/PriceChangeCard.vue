<template>
    <div class="card bg-blue-700">
        <div class="left">
            <p class="title">{{ title }}</p>
            <p class="price">{{ formatPrice(currentPrice) }}</p>
            <p v-if="subheading" class="subheading">{{ subheading }}</p>
        </div>
        <div class="right">
            <p v-if="percentageChange !== null"
               class="percentage"
               :class="{'positive': percentageChange > 0, 'negative': percentageChange < 0}">
                {{ formatDecimal(percentageChange, 1) }}%
            </p>
        </div>
    </div>
</template>

<script>
import formatUtils from '../../utils/FormatUtils';

export default {
    name: 'price-change-card',
    mixins: [formatUtils],
    props: {
        title: {
            type: String,
            required: true,
        },
        currentPrice: {
            type: Number,
            required: false,
        },
        oldPrice: {
            type: Number,
            required: false,
        },
        subheading: {
            type: String,
            required: false,
        },
    },
    computed: {
        percentageChange() {
            if (this.oldPrice === 0) {
                return null;
            }

            return ((this.currentPrice / this.oldPrice) - 1) * 100;
        },
    },
};
</script>

<style lang="scss" scoped>
.card {
    display: flex;
    flex-direction: row;
    border-radius: 0.3rem;
    color: white;
    width: 25rem;
    max-width: 25rem;

    p {
        color: white;
        font-size: 1.2rem;
        font-weight: normal;
        width: 100%;
        text-align: right;
    }


    .price {
        font-size: 1.5rem;
        font-weight: bold;
    }

    .percentage {
        font-size: 1.8rem;
        font-weight: bold;
        text-align: center;

        &.positive {
            color: green;

            &:before {
                content: '+';
            }
        }

        &.negative {
            color: red;
        }
    }

    .left, .right {
        padding: 1rem;
    }

    .left {
        width: 60%;
        border-right-color: transparentize(white, 0.25);
        border-right-width: 1px;
        border-right-style: solid;
    }

    .right {
        height: 100%;
        width: 40%;
        margin: auto 0;
    }
}
</style>

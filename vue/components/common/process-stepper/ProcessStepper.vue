<template>
    <ul class="process-stepper">
        <li v-for="(step, index) in steps"
            :key="index"
            class="stepper"
            :data-cy="`step-${step.label.replaceAll(/ /g, '-').toLowerCase()}`"
            v-bind:class="{ disabled: step.disabled || false, active: activeStep === index }"
        >
            <a href="#" style="text-decoration: none !important;" @click.prevent.stop="step.click">
                <span class="stepperCount">{{ index + 1 }}</span>
                <span>{{ step.label }}</span>
                <div v-if="step.errorCount" class="qv-number-circle qv-number-circle-error" style="margin-left: 2px">{{ step.errorCount }}</div>
                <div v-if="step.warningCount" class="qv-number-circle qv-number-circle-warning" style="margin-left: 2px">{{ step.warningCount }}</div>
            </a>
        </li>
    </ul>
</template>

<script>
export default {
    props: {
        steps: {
            type: Array,
            required: true,
        },
        activeStep: {
            type: Number,
            default: 0,
        },
    },
};
</script>

<style lang="scss" scoped>
$color_dark: #112358;
$color_light: #5290db;
$transparent: transparent;
$translucent_white: rgba(255,255,255,.7);
$white: #fff;

.process-stepper {
    border-left: 4px solid $color_dark;
    margin-top: 3rem;
    margin-left: 1.5rem;
    margin-bottom: 3rem;

    li {
        margin: 0 0 2rem -1.7rem;
        cursor: pointer;

        &.disabled {
            pointer-events: none;

            .stepperCount {
                background: rgba(237,241,245,1);
                box-shadow: inset 0 0 0 3px $color_dark;
                pointer-events: none;
                &:before {
                    content: "\E897"; /* lock */
                    color: $color_dark;
                }
            }
        }

        &.active {
            .stepperCount {
                background: $color_light;
                &:before {
                    content: "\E838"; /* star */
                    color: $white;
                }
            }
        }

        span {
            display: inline-block;
            line-height: 1.2;
        }

        .stepperCount {
            position: relative;
            font-size: 1.4rem;
            font-weight: 400;
            color: $transparent;
            text-align: center;
            background: $color_dark;
            padding-top: .7rem;
            border-radius: 50%;
            margin-right: .8rem;
            width: 3rem;
            height: 3rem;
            box-sizing: border-box;
            cursor: pointer;

            &:before {
                position: absolute;
                top: 0;
                left: 0;
                content: "";
                font-family: "Material Icons";
                font-size: 1.3rem;
                line-height: 2.3;
                width: 3rem;
                height: 3rem;
                content: "\E83A"; /* star-border */
                color: $translucent_white;
            }
        }
    }
    a {
        display: block;
        font-size: 1.2rem;
        font-weight: 600;
        color: $color_dark;

        &:hover {
            opacity: 1;
            color: $color_light;
        }
    }
}

</style>

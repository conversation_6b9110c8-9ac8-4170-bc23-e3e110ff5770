<script setup>

const props = defineProps({
    errors: {
        type: Array,
        default: () => [],
    },
    warnings: {
        type: Array,
        default: () => [],
    },
});

function formatField(field) {
    return field.split('.').map(key => key.charAt(0).toUpperCase() + key.slice(1)).join('.');
}

</script>

<template>
    <div class="qv-flex-column" style="gap: 1rem;">
        <div
            v-if="props.errors.length" data-cy="validation-messages-errors" class="qv-objection-job-validation-errors qv-flex-row"
            style="justify-content: space-between; gap: 5rem;">
            <ul data-cy="validation-messages-errors-ul">
                <li v-for="error in props.errors" data-cy="validation-messages-errors-li">- {{ error.field ? `${formatField(error.field)} - ${error.message}` :
                    error.message }}</li>
            </ul>
            <div>Errors</div>
        </div>
        <div
            v-if="props.warnings.length" class="qv-objection-job-validation-warnings qv-flex-row"
            style="justify-content: space-between; gap: 5rem;">
            <ul>
                <li v-for="warning in props.warnings" data-cy="validation-messages-warnings-li">
                    - {{ warning.field ? `${formatField(warning.field)} - ${warning.message}` : warning.message }}
                </li>
            </ul>
            <div>Warnings</div>
        </div>
    </div>
</template>

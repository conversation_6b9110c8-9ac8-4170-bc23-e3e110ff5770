<template>
    <div>
        <div class="title-wrapper">
            <div class="title">
                <slot name="title"/>
            </div>
            <div class="title-right">
                <slot name="title-right"/>
            </div>
        </div>
        <div class="divider"></div>
        <div class="content">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'container',
}
</script>

<style lang="scss">
.title-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
}

.title {
    font-weight: 600;
    font-size: 2rem;
    color: var(--color-blue-700);
}

.content {
    width: 100%;
    padding: 1rem;
    background-color: var(--color-lightblue-100);
    border-style: solid;
    border-color: #fff;
    border-radius: 0.3rem;
    border-top-color: var(--color-blue-700);
    border-top-width: 1rem;
}
</style>

<template>
    <div class="success alertWrapper modal" data-backdrop="static" data-keyboard="false" style="display: none">
        <div class="alert mdl-shadow--24dp">
            <h3 id="successHeader">{{ header }}</h3>
            <p id="successMessage">{{ message }}</p>
            <ul class="alertButtons">
                <li @click="success()" id="successCancel" class="mdl-button mdl-button--mini"><a href="#" v-html="close"></a></li>
            </ul>
        </div>
    </div>
</template>
<script>
    export default {
        props: ['header', 'message', 'close', 'okHandler'],
        methods: {
            success: function () {
                var self = this;
                $('.success').hide();
                if(self.okHandler) self.okHandler();
            }
        }
    }
</script>

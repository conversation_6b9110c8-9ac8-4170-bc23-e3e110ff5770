<template xmlns:v-on="http://www.w3.org/1999/xhtml">
    <div class="relinkWrapper mdl-shadow--3dp">
        <div class="relinkTitle">
            <h1 class="lefty" >Relink photos</h1>
        </div>

        <div v-for="inPropertyPhoto in inPropertyPhotos" class="relinkRow">
            <div class="relinkCell valRef-current">
                <h3>{{ inPropertyPhoto.property.rollNumber + '/' + inPropertyPhoto.property.assessmentNumber + (inPropertyPhoto.property.suffix ? ' ' + inPropertyPhoto.property.suffix : '')}}</h3>
            </div>
            <div class="relinkCell relinkPhoto-photoDetails">
                        <span class="thumbWrapper">
                            <img class="photoGallery_thumb" :src="inPropertyPhoto.mediaItem.mediumImageUrl">
                        </span>
                <p class="Description">{{ inPropertyPhoto.description }}</p>
            </div>
            <div class="relinkCell valRef-select">
                <select name="propOut" :id="inPropertyPhoto.mediaItem.id" class="subSelect">
                    <option v-for="op in outProperties" :value="op.id" :selected="isSelected(inPropertyPhoto.mediaItem.id, op.id)">{{ op.rollNumber + '/' + op.assessmentNumber + (op.suffix ? ' ' +  op.suffix : '')}}</option>
                </select>
                <i class="material-icons md-dark">&#xE5C5;</i>
            </div>
        </div>
        <div class="relinkRow">
            <ul class="relinkButtons">
                <li class="mdl-button mdl-button--mini relink-submit" @click="cancel()">Cancel</li>
                <li class="mdl-button mdl-button--mini relink-submit" @click="relink()">Relink</li>
            </ul>
        </div>
        <warning header="Photo Relink" message="Some photos are not assigned. Please relink all photos." close="Go Back"></warning>
        <success header="Photo Relink" message="Photo relink done." close="Ok" :okHandler="okHandler"></success>
    </div>
</template>
<script>
    import Warning from '../common/Warning.vue';
    import Success from '../common/Success.vue';
    import commonUtils from '../../utils/CommonUtils';

    export default {
        components: {
            Warning,
            Success
        },
        mixins: [commonUtils],
        data: function() {
            return {
                inPropertyPhotos: null,
                newPropertyPhotos: {},
                outProperties: []
            }
        },
        methods: {
            getSubdivisionData: function(){
                var self = this;
                var qupidsIn = self.getURLParam('In') ? self.getURLParam('In') : self.getURLParam('In', document.referrer.substring(document.referrer.indexOf("?") + 1)) ;
                var qupidsOut = self.getURLParam('Out') ? self.getURLParam('Out') : self.getURLParam('Out', document.referrer.substring(document.referrer.indexOf("?") + 1));
                if(qupidsIn != null && qupidsOut != null) {
                    $.ajax({
                        type: "GET",
                        url: jsRoutes.controllers.Subdivision.getSubdivisionData(qupidsIn, qupidsOut).url,
                        cache: false,
                        success: function (response) {
                            self.inPropertyPhotos = response.in;
                            self.outProperties = response.out;
                            $.each(self.inPropertyPhotos, function(i, mediaEntry) {
                                self.newPropertyPhotos[mediaEntry.mediaItem.id] = {
                                    "id": "",
                                    "ownerId": "",
                                    "isPrimary": mediaEntry.isPrimary,
                                    "category": mediaEntry.category,
                                    "description": mediaEntry.description,
                                    "tags": mediaEntry.tags,
                                    "mediaItem": mediaEntry.mediaItem
                                };
                            });
                        },
                        error: function (response) {
                            console.log("Error in showing subdivision data: " + response);
                            self.errorHandler(response);
                        }
                    });
                }
            },
            getURLParam: function(sParam, sUrl){
                var sPageURL = window.location.search.substring(1);
                if(sUrl) {
                    sPageURL = sUrl;
                }
                var sURLVariables = sPageURL.split('&');
                for (var i = 0; i < sURLVariables.length; i++){
                    var sParameterName = sURLVariables[i].split('=');
                    if (sParameterName[0] == sParam){
                        return sParameterName[1];
                    }
                }
            },
            checkIfAllPhotosAreAssigned: function() {
                var self = this;
                var check = true;
                $.each(self.newPropertyPhotos, function(i, relink) {
                    if(relink.ownerId == "") {
                        self.newPropertyPhotos[relink.mediaItem.id].ownerId = self.outProperties[0].id;
                    }
                });
                return check;
            },
            doRelinking: function(relink) {
                var self = this;
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.MediaController.linkMedia().url,
                    cache: false,
                    processData: false,
                    contentType: 'application/json',
                    data: JSON.stringify(relink),
                    async: false,
                    success: function (response) {
                        self.newPropertyPhotos[response.mediaItem.id] = response;
                    },
                    error: function (response) {
                        console.log("Error in subdivision relink");
                        self.errorHandler(response);
                    }
                });
            },
            relink: function() {
                var self = this;
                if(self.checkIfAllPhotosAreAssigned()) {
                    $('.relink-submit').addClass('disabled');
                    var newPropPhotos = Object.keys(self.newPropertyPhotos).map(function( id ){
                        return  self.newPropertyPhotos[id];
                    });
                    $.each(self.outProperties, function(i, property) {
                        console.log(i+1)
                        var photos = newPropPhotos.filter(function(e) {
                            return e.ownerId == property.id;
                        });
                        if(photos.length > 0) {
                            var primaryOutsideSubdivision = property.photos.filter(function(p1){
                                  return p1.isPrimary;
                            });
                            console.log("primaryOutsideSubdivision: "  + (primaryOutsideSubdivision.length > 0));
                            var setAllToFalse = primaryOutsideSubdivision.length > 0;
                            if(setAllToFalse) {
                                var newPhotos = [];
                                $.each(photos, function(i, photo) {
                                    if(primaryOutsideSubdivision[0].mediaItem.id == photo.mediaItem.id) {
                                        photo.isPrimary = true;
                                    } else {
                                        photo.isPrimary = false;
                                    }
                                    newPhotos.push(photo);
                                });
                                photos = newPhotos.sort(function(a,b){
                                    return b.isPrimary - a.isPrimary;
                                });
                            } else {
                                var hasPrimary = photos.filter(function(e) {
                                    return e.isPrimary == true;
                                });
                                photos = photos.sort(function(a,b){
                                    return new Date(a.mediaItem.captureDate) - new Date(b.mediaItem.captureDate);
                                });
                                if(hasPrimary.length == 0) {
                                    photos[0].isPrimary = true
                                    console.log("no primary")
                                } else if(hasPrimary.length > 1) {
                                    photos = photos.reverse();
                                    var newPhotos = [];
                                    var primary = false;
                                    $.each(photos, function(i, photo) {
                                        if(photo.isPrimary && !primary) {
                                            primary = true
                                        } else {
                                            photo.isPrimary = false;
                                        }
                                        newPhotos.push(photo);
                                    });
                                    photos = newPhotos.sort(function(a,b){
                                        return b.isPrimary - a.isPrimary;
                                    });
                                    console.log("has primary")
                                }
                            }
                            $.each(photos, function(i, relink) {
                                console.log(relink)
                                if(relink.id != "") {
                                    $.ajax({
                                        type: "GET",
                                        url: jsRoutes.controllers.MediaController.unLinkMedia(relink.id).url,
                                        cache: false,
                                        processData: false,
                                        async: false,
                                        success: function (response) {
                                            var newRelink = relink;
                                            newRelink.id = "";
                                            self.doRelinking(newRelink);
                                        },
                                        error: function (response) {
                                            console.log("Error in subdivision relink");
                                            self.errorHandler(response);
                                            $('.relink-submit').removeClass('disabled');
                                        }
                                    });
                                } else {
                                    self.doRelinking(relink);
                                }
                            });
                        }
                    });
                    $('.success').show();
                    $('.relink-submit').removeClass('disabled');
                } else {
                    $('.warning').show();
                }
            },
            isSelected: function(mediaItemId, propertyId) {
                var self = this;
                var isSelected = false;
                var property = self.outProperties.filter(function(property){ return property.id == propertyId })[0];
                if(property.photos.length > 0) {
                    var photoList = property.photos.filter(function(photo){ return photo.mediaItem.id == mediaItemId });
                    isSelected = photoList.length > 0;
                    if(isSelected) {
                        self.newPropertyPhotos[photoList[0].mediaItem.id].id = photoList[0].id;
                        self.newPropertyPhotos[photoList[0].mediaItem.id].ownerId = photoList[0].ownerId;
                    }
                }
                return isSelected;
            },
            okHandler: function() {
                window.open('','_self').close();
                window.top.close();
            },
            cancel: function() {
                window.open('','_self').close();
                window.top.close();
            }
        },
        mounted: function() {
            var self = this;
            self.getSubdivisionData();
        },
        updated: function() {
            var self = this;
            $('.subSelect').on('change', function() {
                self.newPropertyPhotos[this.id].ownerId = this.value;
            });
        }
    }
</script>

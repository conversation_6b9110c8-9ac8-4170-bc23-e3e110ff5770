<template>
    <div class="dashboardWrapper" v-if="viewTaDashboard" style="display: block">
        <div class="taSummary-wrapper">
            <div class="taBranding-wrapper">
                <img v-bind:src="taDashboardData.taLogoURL" @error="getDefaultLogo">
            </div>
            <div class="taTotals-wrapper" v-if="taDashboardData && taDashboardData.userPropertyValue">
                <div class="taTotals-value">
                    <h4>Total Capital Value <span>Since {{taDashboardData.userPropertyValue.financialYearStartDate}}</span></h4>

                    <h3>{{ taDashboardData.userPropertyValue.totalCapitalValue }}</h3>
                    <p class="negVal" v-if="taDashboardData.userPropertyValue.totalCapitalValuePercentage.indexOf('-') != -1">
                        <span>{{ taDashboardData.userPropertyValue.totalCapitalValuePercentage }}%</span>
                    </p>
                    <p class="posVal" v-else>
                        <span>+{{ taDashboardData.userPropertyValue.totalCapitalValuePercentage }}%</span>
                    </p>
                </div>
                <div class="taTotals-value">
                    <h4>Total Land Value</h4>

                    <h3>{{ taDashboardData.userPropertyValue.totalLandValue }}</h3>
                    <p class="negVal" v-if="taDashboardData.userPropertyValue.totalLandValuePercentage.indexOf('-') != -1">
                        <span>{{ taDashboardData.userPropertyValue.totalLandValuePercentage }}%</span>
                    </p>
                    <p class="posVal" v-else>
                        <span>+{{ taDashboardData.userPropertyValue.totalLandValuePercentage }}%</span>
                    </p>
                </div>
                <div class="taTotals-value">
                    <h4>Total Rating Units</h4>
                    <h3>{{ taDashboardData.userPropertyValue.totalRatingUnits }}</h3>
                    <p class="negVal" v-if="taDashboardData.userPropertyValue.totalChangeInRatingUnits.indexOf('-') != -1">
                        <span>{{ taDashboardData.userPropertyValue.totalChangeInRatingUnits }}</span>
                    </p>
                    <p class="posVal" v-else>
                        <span>+{{ taDashboardData.userPropertyValue.totalChangeInRatingUnits }}</span>
                    </p>
                </div>
                <div class="revalDate">
                    <h4>Rating Valuation</h4>
                    <p>{{ taDashboardData.ratingValuationDate }}</p>
                </div>

                <div class="revalDate">
                    <h4>Next Rating Valuation</h4>
                    <p>{{ taDashboardData.nextRatingValuationDate }}</p>
                </div>
            </div>
            <div class="reportCentre-wrapper">
                <div class="reportCentre-left">
                    <h3>Report Centre</h3>
                    <ul>
                        <li class="md-building-consents-report" v-on:click="openReport('/Reports_Consents.asp?Action=Listing', false)">
                            <label>Building Consents Listing</label>
                        </li>
                        <li class="md-objections-report" v-on:click="openReport('/Reports_objections.asp?Action=Listing', false)">
                            <label>Objections Listing</label>
                        </li>
                        <li class="md-outstanding-objections-report" v-on:click="openReport('/Reports_Objections.asp?Action=Outstanding', false)">
                            <label>Outstanding Objections</label>
                        </li>
                        <li class="md-subdivisions-report" v-on:click="openReport('/Reports_Miscellaneous.asp?Action=SubdivisionsListing', false)">
                            <label>Subdivisions Listing</label>
                        </li>
                        <li class="md-sales-report" v-on:click="openReport('/Reports_Miscellaneous.asp?Action=SalesList', false)">
                            <label>Sales Listing</label>
                        </li>
                    </ul>
                </div>
                <div class="reportCentre-right">
                    <h3>QV Insights</h3>

                    <p>We offer a wide variety of reports and commentary to help you analyse your local property market.</p>
                    <span class="md-more-reports" v-on:click="openReport('/reports.asp?sBackToPageText=Main&sBackToURL=main.asp', false)">
                        <label>More Reports<i class="material-icons">&#xE24B;</i></label>
                    </span>
                    <span class="md-more-analysis" v-on:click="openReport('https://qvgroup.qv.co.nz/knowledge-centre', true)">
                        <label>More Analysis<i class="material-icons">&#xE6DD;</i></label>
                    </span>

                </div>
            </div>

            <div class="taNames-wrapper" v-if="taDashboardData && taDashboardData.userQVContact && taDashboardData.userQVContact.length">
                <h3>Your QV Contacts</h3>
                <ul class="vcard qvContact" v-if="taDashboardData.userQVContact[0]">
                    <li class="lead fn qvName">{{ taDashboardData.userQVContact[0].name }}</li>
                    <li class="title qvRole">{{ taDashboardData.userQVContact[0].role }}</li>
                    <li class="tel qvPhone"><i
                            class="material-icons">&#xE0CD;</i>{{ taDashboardData.userQVContact[0].phone }}</li>
                    <li class="email qvMail"><i class="material-icons">&#xE0BE;</i><a v-bind:href="`mailto:${taDashboardData.userQVContact[0].email}`">{{ taDashboardData.userQVContact[0].email }}</a>
                    </li>
                </ul>
                <ul class="vcard qvContact" v-if="taDashboardData.userQVContact[1]">
                    <li class="lead fn qvName">{{ taDashboardData.userQVContact[1].name }}</li>
                    <li class="title qvRole">{{ taDashboardData.userQVContact[1].role }}</li>
                    <li class="tel qvPhone"><i
                            class="material-icons">&#xE0CD;</i>{{ taDashboardData.userQVContact[1].phone }}</li>
                    <li class="email qvMail"><i class="material-icons">&#xE0BE;</i><a
                            v-bind:href="`mailto:${taDashboardData.userQVContact[1].email}`">{{ taDashboardData.userQVContact[1].email }}</a>
                    </li>
                </ul>
            </div>

        </div>
        <div id="dashboardDragSection">
            <div id="dashboardDragBlock2" class="graphRow mdl-shadow--2dp dragBlock dragBlockBuildingConsentsAndSubDivisions" draggable="true">
                <div id="dragBlockBuildingConsentsAndSubDivisions">
                    <div class="buildingConsents">
                        <h2>Building Consents</h2>
                        <div class="graphBox stackedCol">
                            <h3>Completed</h3>
                            <ul>
                                <li>
                                    <label>This Month</label>
                                    <h3>{{ completedConsents }}</h3>
                                </li>
                                <li>
                                    <label>Since 1 July</label>
                                    <h3>{{ yearToDateCompletedConsents }}</h3>
                                </li>
                                <li :style="computedStyleForCompletedConsents"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="subdivisions">
                        <h2>&nbsp;</h2>
                        <div class="graphBox chart">
                            <h3>COMPLETED</h3>
                            <div class="highCharts-wrapper">
                                <bar-graph></bar-graph>
                            </div>
                        </div>
                    </div>
                        <div class="subConsents">
                          <h2>Subdivisions</h2>
                        <div class="graphBox stackedCol">
                           <h3>Completed</h3>
                            <ul>
                                <li>
                                    <label>This Month</label>
                                    <h3>{{ completedSubdivisions }}</h3>
                                </li>
                                <li>
                                    <label>Since 1 July</label>
                                    <h3>{{ yearlyCompletedSubdivisions }}</h3>
                                </li>
                                <li :style="computedStyleForCompletedConsents"></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div id="dashboardDragBlock3" class="graphRow mdl-shadow--2dp dragBlock dragBlockSalesInfo" draggable="true" >
                <div id="dragBlockSalesInfo">
                    <h2>Sales Information</h2>
                    <div class="sales-left">
                        <div class="graphBox stackedCol">
                            <h3>Processed</h3>
                            <ul>
                                <li>
                                    <label>This Month</label>
                                    <h3>{{ confirmedSales }}</h3>
                                </li>
                                <li>
                                    <label>Since 1 July</label>
                                    <h3>{{ yearToDateConfirmedSales }}</h3>
                                </li>
                                <li :style="computedStyleForSales"></li>
                            </ul>
                        </div>
                        <div class="graphBox chart">
                            <h3>Market Movement</h3>
                            <div class="highCharts-wrapper">
                                <column-graph :colors="colors"></column-graph>
                            </div>
                        </div>
                    </div>
                    <div class="sales-right">
                        <div class="graphBox chart">
                            <h3>QV House Price Index - Average Values</h3>
                            <div class="highCharts-wrapper">
                                <spline-graph :colors="colors"></spline-graph>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="dashboardDragBlock1" class="graphRow mdl-shadow--2dp dragBlock dragBlockRevisionObjections" draggable="true">
                <div id="dragBlockRevisionObjections" >
                    <h2>Revision Objections</h2>
                    <div class="revObj-left">
                        <div class="graphBox chart">
                            <h3>Objections Trendline</h3>
                            <div class="highCharts-wrapper">
                                <line-graph :colors="colors"></line-graph>
                            </div>
                        </div>
                        <div class="graphBox chart">
                            <h3>Decisions Issued</h3>
                            <div class="highCharts-wrapper">
                                <gauge-graph></gauge-graph>
                            </div>
                        </div>
                    </div>
                    <div class="revObj-right">
                        <div class="graphBox chart">
                            <h3>Objections by Category</h3>
                            <div class="highCharts-wrapper">
                                <tree-map-graph></tree-map-graph>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="dashboardDragBlock4" class="graphRow mdl-shadow--2dp dragBlock dragBlockMaintenanceObjections" draggable="true">
                <div id="dragBlockMaintenanceObjections">
                    <h2>Maintenance Objections</h2>
                    <div class="mainObj">
                        <div class="graphBox stackedCol">
                            <h3>Decisions Issued</h3>
                            <ul>
                                <li>
                                    <label>This Month</label>
                                    <h3>{{ completedObjections }}</h3>
                                </li>
                                <li>
                                    <label>Since 1 July {{objectionsYear}}</label>
                                    <h3>{{ yearToDateCompletedObjections }}</h3>
                                </li>
                                <li :style="computedStyleForObjections"></li>
                            </ul>
                        </div>

                        <div class="graphBox simpleBar">
                            <h3>THIS MONTH</h3>
                            <ul>
                                <li class="darkBar">
                                    <label>Decisions Issued</label>
                                    <h3>{{ completedObjections }}</h3>
                                    <span :style="computedStyle1Objections"></span>
                                </li>
                                <li class="lightBar">
                                    <label>To Action</label>
                                    <h3>{{inProgressObjections}}</h3>
                                    <span :style="computedStyle2Objections"></span>
                                </li>
                            </ul>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import Vue from 'vue';
    import LineGraph from './LineGraph.vue'
    import BarGraph from './BarGraph.vue'
    import ColumnGraph from './ColumnGraph.vue'
    import SplineGraph from './SplineGraph.vue'
    import GaugeGraph from './GaugeGraph.vue'
    import TreeMapGraph from './TreeMapGraph.vue'
    import moment from 'moment'

    export default {
        components: {
            LineGraph,
            BarGraph,
            ColumnGraph,
            SplineGraph,
            GaugeGraph,
            TreeMapGraph
        },
        data: function() {
            return {
                taDashboardData: [],
                taDashboardGraphsData: [],
                viewTaDashboard: false,
                showGraph: false,
                tacodeParam: "",
                monthlyCompletedSubdivisions: [],
                yearlyCompletedSubdivisions: "",
                inprogressConsents: 0,
                readyToCheckConsents: 0,
                completedConsents: 0,
                yearToDateCompletedConsents: 0,
                computedStyle1ForConsent: "",
                computedStyle2ForConsent: "",
                computedStyleForCompletedConsents: "",
                completedSubdivisions: 0,
                outstandingSubdivisions: 0,
                computedStyle1ForSubdivisions: "",
                computedStyle2ForSubdivisions: "",
                confirmedSales: 0,
                yearToDateConfirmedSales: 0,
                computedStyleForSales: "",
                taMarketMovement: [],
                nzMarketMovement: [],
                taName: "",
                monthlyTAHousePriceIndex: new Array(),
                monthlyNZHousePriceIndex: new Array(),
                completedObjections: 0,
                yearToDateCompletedObjections: 0,
                computedStyleForObjections: "",
                inProgressObjections: 0,
                computedStyle1Objections: "",
                computedStyle2Objections: "",
                historicalTrendDataChartSeries: new Array(),
                decisionsIssuedPercentage: 0,
                yearToDateReceivedObjections: 0,
                dragSrcEl: null,
                boxes_: null,
                taDashboardRevisionSumary: {},
                displaySalesInfo: false,
                displayRevision: false,
                displayMaintenanceObjections: false,
                ta: {},
                updateGraphBlocks: false,
                objectionsYear: '',
                monthlyCompletedConsents: [],
                yearlyCompletedConsents: "",
                colors: [
                    '#7eb4ec',
                    '#37474f',
                ]
            }
        },
        computed: {
            ...mapState('userData', [
                'userId',
                'qivsUrl'
            ]),
        },
        mounted: function() {
            const today = new Date();
            this.objectionsYear = today.getFullYear() - (today.getMonth() <= 6 ? 2 : 1);
            const self = this;
            EventBus.$on('display-content', function(event) {
                var tacodeParam = event.taCode;
                if(event.searchType == 'ta-dashboard') {
                    self.viewTaDashboard = true;
                    $('.dashboardWrapper').show();
                    //console.log("inside display-ta-dashboard event's method");
                    self.tacodeParam = tacodeParam;
                    var m = jsRoutes.controllers.TADashboardController.loadTADashboard(tacodeParam);
                    $.ajax({
                        type: "GET",
                        url: m.url,
                        cache: false,
                        success: function (response) {
                            self.displaySalesInfo = false;
                            self.displayRevision = false;
                            self.displayMaintenanceObjections = false;
                            self.taDashboardData = response;
                            self.taName = self.taDashboardData.userPropertyValue.taName;
                            self.ta.currentDate = new Date();

                            self.showGraph = response.showGraph;
                            self.onLoad();
                            self.fetchGraphs(self.tacodeParam);
                            if (event.showSplashPage) {
                                EventBus.$emit('display-splash-page')
                            }
                        },
                        error: function (response) {
                            //console.log(response);
                        }
                    });
                } else {
                    self.viewTaDashboard = false;
                    $('.dashboardWrapper').hide();
                }
            });
        },
        updated: function(){
            const self = this;
            if (self.updateGraphBlocks){
                self.initTADashboardCustomisation(self.userId);
                self.updateGraphBlocks = false
            }
        },
        methods: {
            getDefaultLogo: function()  {
                if(this.taDashboardData != null && this.taDashboardData != 'undefined'){
                    this.taDashboardData.taLogoURL = this.taDashboardData.defaultTaLogoURL;
                } else {
                    this.taDashboardData.taLogoURL = "http://s3-ap-southeast-2.amazonaws.com/qv-webassets/ta-logos/QV__Default_Logo.png"
                }
            },
            fetchGraphs: function(tacodeParam){
                var m = jsRoutes.controllers.TADashboardController.displayGraphs(tacodeParam);
                var self = this;
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        self.taDashboardGraphsData = response;

                        self.monthlyCompletedSubdivisions= [];
                        if(self.taDashboardGraphsData.taSubdivisionSummary!=null && self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries!=null && self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries.length > 0) {
                            for(var i=0;i<self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries.length;i++) {
                                self.monthlyCompletedSubdivisions.push(self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries[i].completedSubdivisions);
                            }
                            self.yearlyCompletedSubdivisions = self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries[self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries.length-1].yearToDateCompletedSubdivisions;
                        }

                        self.inprogressConsents = (self.taDashboardGraphsData.taConsentSummary !=null && Object.keys(self.taDashboardGraphsData.taConsentSummary).length) > 0 ? self.taDashboardGraphsData.taConsentSummary.inProgressConsents : 0;
                        self.readyToCheckConsents = (self.taDashboardGraphsData.taConsentSummary !=null && Object.keys(self.taDashboardGraphsData.taConsentSummary).length > 0) ? self.taDashboardGraphsData.taConsentSummary.readyToCheckConsents : 0;

                        self.computedStyle1ForConsent = 'width:'+((self.inprogressConsents >= self.readyToCheckConsents && self.inprogressConsents > 0)? 100 : (self.inprogressConsents == 0 || self.readyToCheckConsents == 0) ? 0 : ((self.inprogressConsents*100)/self.readyToCheckConsents))+'%';
                        self.computedStyle2ForConsent='width:'+((self.inprogressConsents <= self.readyToCheckConsents && self.readyToCheckConsents > 0) ? 100 : ((self.readyToCheckConsents == 0 || self.inprogressConsents == 0) ? 0 : ((self.readyToCheckConsents*100)/self.inprogressConsents)))+'%';

                        var monthlyConsentSummaries = self.taDashboardGraphsData && self.taDashboardGraphsData.taConsentSummary && self.taDashboardGraphsData.taConsentSummary.monthlyConsentSummaries ? self.taDashboardGraphsData.taConsentSummary.monthlyConsentSummaries : undefined
                        self.completedConsents = monthlyConsentSummaries ? (monthlyConsentSummaries[monthlyConsentSummaries.length -1].completedConsents) : 0;
                        self.yearToDateCompletedConsents = monthlyConsentSummaries ? (monthlyConsentSummaries[monthlyConsentSummaries.length -1].yearToDateCompletedConsents) : 0;

                        self.monthlyCompletedConsents = [];
                        if( self.taDashboardGraphsData.taConsentSummary!=null
                            && self.taDashboardGraphsData.taConsentSummary.monthlyConsentSummaries!=null
                            && self.taDashboardGraphsData.taConsentSummary.monthlyConsentSummaries.length > 0){
                                for(var i=0;i<self.taDashboardGraphsData.taConsentSummary.monthlyConsentSummaries.length;i++) {
                                self.monthlyCompletedConsents.push(self.taDashboardGraphsData.taConsentSummary.monthlyConsentSummaries[i].completedConsents);
                                }
                                self.yearlyCompletedConsents = self.taDashboardGraphsData.taConsentSummary.monthlyConsentSummaries[self.taDashboardGraphsData.taConsentSummary.monthlyConsentSummaries.length-1].yearToDateCompletedConsents;
                            }

                        self.completedSubdivisions = (self.taDashboardGraphsData.taSubdivisionSummary !=null && Object.keys(self.taDashboardGraphsData.taSubdivisionSummary).length > 0 && self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries.length > 0) ? self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries[self.taDashboardGraphsData.taSubdivisionSummary.monthlySubdivisionSummaries.length-1].completedSubdivisions : 0;
                        self.outstandingSubdivisions = (self.taDashboardGraphsData.taSubdivisionSummary !=null && Object.keys(self.taDashboardGraphsData.taSubdivisionSummary).length > 0) ? self.taDashboardGraphsData.taSubdivisionSummary.outstandingSubdivisions : 0;
                        self.computedStyle1ForSubdivisions = self.horizontalStyle1(self.completedSubdivisions, self.outstandingSubdivisions);
                        self.computedStyle2ForSubdivisions = self.horizontalStyle2(self.completedSubdivisions, self.outstandingSubdivisions);

                        self.confirmedSales = (self.taDashboardGraphsData.taSalesSummary !=null && Object.keys(self.taDashboardGraphsData.taSalesSummary).length > 0 && self.taDashboardGraphsData.taSalesSummary.monthlySalesSummaries.length > 0) ? self.taDashboardGraphsData.taSalesSummary.monthlySalesSummaries[self.taDashboardGraphsData.taSalesSummary.monthlySalesSummaries.length-1].confirmedSales : 0;


                        self.yearToDateConfirmedSales = (self.taDashboardGraphsData.taSalesSummary !=null && Object.keys(self.taDashboardGraphsData.taSalesSummary).length > 0 && self.taDashboardGraphsData.taSalesSummary.monthlySalesSummaries.length > 0) ? self.taDashboardGraphsData.taSalesSummary.monthlySalesSummaries[self.taDashboardGraphsData.taSalesSummary.monthlySalesSummaries.length-1].yearToDateConfirmedSales : 0;
                        self.computedStyleForSales = self.singleBarStyle(self.confirmedSales, self.yearToDateConfirmedSales);


                        self.taMarketMovement = [];
                        self.nzMarketMovement = [];

                        if(self.taDashboardGraphsData.taMarketSummary!=null && Object.keys(self.taDashboardGraphsData.taMarketSummary).length > 0){
                            self.taMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.taOneMonthMarketMovement);
                            self.nzMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.nzOneMonthMarketMovement);

                            self.taMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.taThreeMonthMarketMovement);
                            self.nzMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.nzThreeMonthMarketMovement);

                            self.taMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.taSixMonthMarketMovement);
                            self.nzMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.nzSixMonthMarketMovement);

                            self.taMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.taOneYearMarketMovement);
                            self.nzMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.nzOneYearMarketMovement);

                            self.taMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.taSinceRevalMarketMovement);
                            self.nzMarketMovement.push(self.taDashboardGraphsData.taMarketSummary.nzSinceRevalMarketMovement);

                            var monthlyTA = new Array();
                            var monthlyNZ = new Array();

                            let currentRevisionDate= moment(self.taDashboardGraphsData.taMarketSummary.currentRevisionDate, "YYYY-MM-DD");
                            let previousRevisionDate= moment(self.taDashboardGraphsData.taMarketSummary.previousRevisionDate, "YYYY-MM-DD");

                            for(var i=0;i<self.taDashboardGraphsData.housePriceIndexData.length;i++) {
                                var obj = self.taDashboardGraphsData.housePriceIndexData[i];

                                //console.log("self.taDashboardGraphsData.taMarketSummary.currentRevisionDate === "+ self.taDashboardGraphsData.taMarketSummary.currentRevisionDate);
                                //console.log("self.taDashboardGraphsData.taMarketSummary.previousRevisionDate === "+ self.taDashboardGraphsData.taMarketSummary.previousRevisionDate);
                                //console.log("obj.date "+obj.date);


                                if(obj.date == currentRevisionDate.startOf('month').format("YYYY-MM-DD")){
                                    monthlyTA.push({x: +new Date(obj.date), y: +obj.taHousePriceIndex, marker: {enabled: true,symbol: 'diamond',fillColor: '#FF990F',radius: 6}});
                                }
                                else if(obj.date == previousRevisionDate.startOf('month').format("YYYY-MM-DD")){
                                    monthlyTA.push({x: +new Date(obj.date), y: +obj.taHousePriceIndex, marker: {enabled: true,symbol: 'diamond',fillColor: '#FF990F',radius: 6}});
                                } else {
                                    monthlyTA.push({x: +new Date(obj.date), y: +obj.taHousePriceIndex});
                                }
                                monthlyNZ.push({x: + new Date(obj.date), y: +obj.nzHousePriceIndex});
                            }
                            self.taName = self.taDashboardGraphsData.taMarketSummary.taName;
                        }
                        self.monthlyTAHousePriceIndex = monthlyTA;
                        self.monthlyNZHousePriceIndex = monthlyNZ;

                        var monthlyObjectionSummaries = self.taDashboardGraphsData && self.taDashboardGraphsData.taObjectionSummary && self.taDashboardGraphsData.taObjectionSummary.maintenanceObjectionSummary && self.taDashboardGraphsData.taObjectionSummary.maintenanceObjectionSummary.monthlyObjectionSummaries ? self.taDashboardGraphsData.taObjectionSummary.maintenanceObjectionSummary.monthlyObjectionSummaries : undefined
                        self.completedObjections = monthlyObjectionSummaries ? monthlyObjectionSummaries[monthlyObjectionSummaries.length -1].completedObjections : 0
                        self.yearToDateCompletedObjections = monthlyObjectionSummaries ? monthlyObjectionSummaries[monthlyObjectionSummaries.length -1].yearToDateCompletedObjections : 0
                        self.computedStyleForObjections = self.singleBarStyle(self.completedObjections, self.yearToDateCompletedObjections);

                        self.inProgressObjections=(self.taDashboardGraphsData.taObjectionSummary != null && Object.keys(self.taDashboardGraphsData.taObjectionSummary).length > 0 && self.taDashboardGraphsData.taObjectionSummary.maintenanceObjectionSummary!=null) ? self.taDashboardGraphsData.taObjectionSummary.maintenanceObjectionSummary.inProgressObjections : 0;
                        self.computedStyle1Objections = self.horizontalStyle1(self.completedObjections, self.inProgressObjections);
                        self.computedStyle2Objections = self.horizontalStyle2(self.completedObjections, self.inProgressObjections);


                        if(self.taDashboardGraphsData.taObjectionSummary != null && Object.keys(self.taDashboardGraphsData.taObjectionSummary).length > 0 && self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary!=null && self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.historicalTrendData!=null && self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.historicalTrendData.length > 0) {
                            self.historicalTrendDataChartSeries = []
                            for(var i=0;i<self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.historicalTrendData.length;i++) {
                                if (new Date(self.taDashboardData.ratingValuationDate) <= new Date(self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.historicalTrendData[i].date)) {
                                    self.historicalTrendDataChartSeries.push([self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.historicalTrendData[i].date, self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.historicalTrendData[i].inProgressObjections]);
                                }
                            }
                        }
                        var taDashboardRevisionSumary = [];
                        if(self.taDashboardGraphsData.taObjectionSummary != null && Object.keys(self.taDashboardGraphsData.taObjectionSummary).length > 0 && self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary!=null) {
                            self.decisionsIssuedPercentage = 0;
                            self.yearToDateReceivedObjections = self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.yearToDateReceivedObjections;
                            var yearToDateCompletedRevisionObjections = self.taDashboardGraphsData && self.taDashboardGraphsData.taObjectionSummary && self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary && self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.monthlyObjectionSummaries ? self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.monthlyObjectionSummaries[self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary.monthlyObjectionSummaries.length - 1].yearToDateCompletedObjections : undefined
                            self.decisionsIssuedPercentage = (self.yearToDateReceivedObjections > 0 && yearToDateCompletedRevisionObjections) ? (100*yearToDateCompletedRevisionObjections)/self.yearToDateReceivedObjections : 0;
                            if (self.decisionsIssuedPercentage > 99 && self.decisionsIssuedPercentage < 100) {
                                self.decisionsIssuedPercentage = Math.round(self.decisionsIssuedPercentage*10) / 10;
                            }
                            else {
                                self.decisionsIssuedPercentage = Math.round(self.decisionsIssuedPercentage);
                            }


                            self.taDashboardRevisionSumary = self.taDashboardGraphsData.taObjectionSummary.revisionObjectionSummary;
                        }



                        //console.log(" self.decisionsIssuedPercentage " + self.decisionsIssuedPercentage);
                        //console.log(" self.yearToDateReceivedObjections " + self.yearToDateReceivedObjections);

                        //console.log(" self.historicalTrendDataChartSeries " + self.historicalTrendDataChartSeries);

                        //console.log(" self.yearlyCompletedSubdivisions " + self.yearlyCompletedSubdivisions);
                        //console.log(" self.monthlyCompletedSubdivisions " + self.monthlyCompletedSubdivisions);

                        //console.log(" self.inprogressConsents " + self.inprogressConsents);
                        //console.log(" self.readyToCheckConsents " + self.readyToCheckConsents);

                        //console.log(" self.computedStyle1ForConsent " + self.computedStyle1ForConsent);
                        //console.log(" self.computedStyle2ForConsent " + self.computedStyle2ForConsent);

                        //console.log(" self.completedConsents " + self.completedConsents);
                        //console.log(" self.yearToDateCompletedConsents " + self.yearToDateCompletedConsents);

                        //console.log(" self.completedSubdivisions " + self.completedSubdivisions);
                        //console.log(" self.outstandingSubdivisions " + self.outstandingSubdivisions);

                        //console.log(" self.computedStyle1ForSubdivisions " + self.computedStyle1ForSubdivisions);
                        //console.log(" self.computedStyle2ForSubdivisions " + self.computedStyle2ForSubdivisions);

                        //console.log(" self.confirmedSales " + self.confirmedSales);
                        //console.log(" self.yearToDateConfirmedSales " + self.yearToDateConfirmedSales);
                        //console.log(" self.computedStyleForSales " + self.computedStyleForSales);

                        //console.log(" self.taMarketMovement " + self.taMarketMovement);
                        //console.log(" self.nzMarketMovement " + self.nzMarketMovement);

                        //console.log(" self.completedObjections " + self.completedObjections);
                        //console.log(" self.yearToDateCompletedObjections " + self.yearToDateCompletedObjections);
                        //console.log(" self.computedStyleForObjections " + self.computedStyleForObjections);
                        //console.log(" self.inProgressObjections " + self.inProgressObjections);

                        //console.log(" self.taName "+ self.taName);

                        //console.log("Emitting Event display-ta-barchart");
                        EventBus.$emit('display-ta-barchart', self.monthlyCompletedSubdivisions, self.yearlyCompletedSubdivisions,self.monthlyCompletedConsents, self.yearlyCompletedConsents);

                        //console.log("Emitting Event display-ta-columnchart");
                        EventBus.$emit('display-ta-columnchart', self.taMarketMovement, self.nzMarketMovement, self.taName);

                        //console.log("Emitting Event display-ta-splinechart");
                        EventBus.$emit('display-ta-splinechart', self.monthlyTAHousePriceIndex, self.monthlyNZHousePriceIndex, self.taName);

                        //console.log("Emitting Event display-ta-linechart");
                        EventBus.$emit('display-ta-linechart', self.historicalTrendDataChartSeries, self.taName);


                        //console.log("Emitting Event display-ta-gaugechart");
                        EventBus.$emit('display-ta-gaugechart', self.decisionsIssuedPercentage, self.yearToDateReceivedObjections, self.taName);

                        //console.log("Emitting Event display-ta-tree mapchart");
                        EventBus.$emit('display-ta-treemapchart', self.taDashboardData.userPropertyValue, self.taDashboardRevisionSumary, self.taName);

                        Vue.nextTick(function () {
                            //console.log("Next tick is required to be called for IE");
                            //console.log("For more details check https://012.vuejs.org/guide/best-practices.html");

                                //console.log("Inside next tick method");
                        })
                        self.updateGraphBlocks = true

                    },
                    error: function (response) {
                        //console.log(response);

                    }
                });
            },
            onLoad: function(){
                $(document).ready( function() {
                        if (navigator.userAgent.indexOf('MSIE') !== -1 || navigator.appVersion.indexOf('Trident/') > 0) {
                            var evt = document.createEvent('UIEvents');
                            evt.initUIEvent('resize', true, false, window, 0);
                            window.dispatchEvent(evt);
                        } else {
                            window.dispatchEvent(new Event('resize'));
                        }
                    }
                );
            },
            refreshGraphs: function(){
                const self = this;
                //console.log("Emitting Event display-ta-barchart");
                EventBus.$emit('display-ta-barchart', self.monthlyCompletedSubdivisions, self.yearlyCompletedSubdivisions, self.monthlyCompletedConsents, self.yearlyCompletedConsents);

                //console.log("Emitting Event display-ta-columnchart");
                EventBus.$emit('display-ta-columnchart', self.taMarketMovement, self.nzMarketMovement, self.taName);

                //console.log("Emitting Event display-ta-splinechart");
                EventBus.$emit('display-ta-splinechart', self.monthlyTAHousePriceIndex, self.monthlyNZHousePriceIndex, self.taName);

                //console.log("Emitting Event display-ta-linechart");
                EventBus.$emit('display-ta-linechart', self.historicalTrendDataChartSeries, self.taName);


                //console.log("Emitting Event display-ta-gaugechart");
                EventBus.$emit('display-ta-gaugechart', self.decisionsIssuedPercentage, self.yearToDateReceivedObjections, self.taName);

                //console.log("Emitting Event display-ta-tree mapchart");
                EventBus.$emit('display-ta-treemapchart', self.taDashboardData.userPropertyValue, self.taDashboardRevisionSumary, self.taName);
            },
            horizontalStyle1: function(topValue, bottomValue) {
                return "width:"+((topValue >= bottomValue && topValue > 0)? 100 : (topValue == 0 || bottomValue == 0) ? 0 : ((topValue*100)/bottomValue))+"%";
            },
            horizontalStyle2: function(topValue, bottomValue) {
                return 'width:'+((topValue <= bottomValue && bottomValue > 0) ? 100 : ((bottomValue == 0 || topValue == 0) ? 0 : ((bottomValue*100)/topValue)))+'%';
            },
            singleBarStyle: function(topValue, bottomValue) {
                    return 'height:'+((this.bottomValue > 0) ? (this.topValue/this.bottomValue)*100 : 0)+'%';
            },
            openReport: function(path, isStatic) {

                var url = this.qivsUrl + path;
                if(isStatic) {
                    url = path;
                }
                //console.log("URL to Display "+url);
                //console.log("isStatic URL "+isStatic);
                window.open(url, "Map");
            },
            handleDragStart: function (e) {
                //console.log(" --- --- --- --- --- --- event target in handleDragStart: " + e.target.innerHTML);
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text', e.target.innerHTML);

                this.dragSrcEl_ = e.target;
                e.target.style.opacity = '0.5';
                $(e.target).addClass('moving');

            },
            handleDragOver: function (e) {
                if (e.preventDefault) {
                    e.preventDefault(); // Allows us to drop.
                }
                e.dataTransfer.dropEffect = 'move';
                return false;
            },

            handleDragEnter: function (e) {
                $(e.target).addClass('over');
            },
            handleDragLeave: function (e) {
                $(e.target).removeClass('over');
            },
            handleDrop: function (e) {
                if (e.stopPropagation) {
                    e.stopPropagation();
                }

                //console.log(" --------- adfasdadf ------ "+ e.target || e.srcElement);

                var target = e.target || e.srcElement;
                while(true) {
                    if(target==null || target.nodeName == "body" || target.getAttribute("draggable") == "true" || target.getAttribute("draggable") == true) {
                        break;
                    } else {
                        target = target.parentElement;
                    }
                }

                if (this.dragSrcEl_ != target) {
                    //console.log("target = " + target);
                    //console.log("target id = " + target.id);
                    //console.log("this.dragSrcEl_.children[0].id = " + this.dragSrcEl_.children[0].id);
                    //console.log("this.dragSrcEl_.id = " + this.dragSrcEl_.id);
                    //console.log("e.target.children[0].id = " +target.children[0].id);
                    //console.log("this.dragSrcEl_.innerHTML = " +this.dragSrcEl_.innerHTML);
                    //console.log("e.target.innerHTML = " +e.target.innerHTML);

                    localStorage.setItem(this.userId+'dashboardItem'+target.id, this.dragSrcEl_.children[0].id);
                    localStorage.setItem(this.userId+'dashboardItem'+this.dragSrcEl_.id, target.children[0].id);

                    this.dragSrcEl_.innerHTML = target.innerHTML;

                    target.innerHTML = e.dataTransfer.getData('text');
                    this.refreshGraphs();
                }
                return false;
            },
            handleDragEnd: function (e) {
                e.target.style.opacity = '1';
                [].forEach.call(this.boxes_, function (box) {
                    $(box).removeClass('over');
                    $(box).removeClass('moving');
                });
            },
            initTADashboardCustomisation: function(forUserId){
                var id_ = 'dashboardDragSection';
                this.boxes_ = document.querySelectorAll('#' + id_ + ' .dragBlock');
                var self = this;

                [].forEach.call(self.boxes_, function (box) {
                    box.setAttribute('draggable', 'true');
                    box.addEventListener('dragstart', self.handleDragStart, false);
                    box.addEventListener('dragenter', self.handleDragEnter, false);
                    box.addEventListener('dragover', self.handleDragOver, false);
                    box.addEventListener('dragleave', self.handleDragLeave, false);
                    box.addEventListener('drop', self.handleDrop, false);
                    box.addEventListener('dragend', self.handleDragEnd, false);
                });

                var dragSectionHtml = $($('#dashboardDragSection').html());
                for(var key in localStorage) {
                    if (key.indexOf(forUserId+'dashboardItem'+'dashboardDragBlock') >= 0) {
                        $('#' + key.substr(key.lastIndexOf('dashboardDragBlock'), key.length)).html(dragSectionHtml.filter('.' + localStorage[key]).html());
                    }
                }

                this.refreshGraphs();
                var boxesToHide = document.querySelectorAll('#' + id_ + ' .dragBlock .hide');
                [].forEach.call(boxesToHide, function (box) {
                    var parentId = $('#'+box.id).parent().attr('id');
                    if (parentId && parentId != '') {
                        $('#'+parentId).addClass('hide');
                    }
                });

            },
            computeGraph: function(){
                var decisionsIssuedPercentage = 0;
                var yearToDateReceivedObjections = 0;
                var yearToDateResidentialObjections = 0;
                var yearToDateLifestyleObjections = 0;
                var yearToDateCommercialObjections = 0;
                var yearToDateRuralObjections = 0;
                var yearToDateOtherObjections = 0;
                var totalResidentialRatingUnits = 0;
                var totalCommercialRatingUnits = 0;
                var totalLifestyleRatingUnits = 0;
                var totalRuralRatingUnits = 0;
                var totalOtherRatingUnits = 0;









                if(this.showGraph && (this.taDashboardData.taRevisionObjectionsSummary!=null && Object.keys(this.taDashboardData.taRevisionObjectionsSummary).length > 0)) {
                    var hasTaRevisionObjectionsSummaryData = (this.taDashboardData.taRevisionObjectionsSummary!=null && Object.keys(this.taDashboardData.taRevisionObjectionsSummary).length > 0);
                    var yearToDateCompletedObjections = this.taDashboardData.taRevisionObjectionsSummary.monthlyObjectionSummaries.yearToDateCompletedObjections;
                    yearToDateReceivedObjections = this.taDashboardData.taRevisionObjectionsSummary.yearToDateReceivedObjections;
                    decisionsIssuedPercentage = (yearToDateReceivedObjections > 0) ? (100*yearToDateCompletedObjections)/yearToDateReceivedObjections : 0;
                    if (decisionsIssuedPercentage > 99 && decisionsIssuedPercentage < 100) {
                        decisionsIssuedPercentage = Math.round(decisionsIssuedPercentage*10) / 10;
                    }
                    else {
                        decisionsIssuedPercentage = Math.round(decisionsIssuedPercentage);
                    }
                    yearToDateResidentialObjections = this.taDashboardData.taRevisionObjectionsSummary.yearToDateResidentialObjections;
                    yearToDateLifestyleObjections = this.taDashboardData.taRevisionObjectionsSummary.yearToDateLifestyleObjections;
                    yearToDateCommercialObjections = this.taDashboardData.taRevisionObjectionsSummary.yearToDateCommercialObjections;
                    yearToDateRuralObjections = this.taDashboardData.taRevisionObjectionsSummary.yearToDateRuralObjections;
                    yearToDateOtherObjections = this.taDashboardData.taRevisionObjectionsSummary.yearToDateOtherObjections;

                    //the code for setting historicalTrendDataChartSeries is removed from here as it is not required.
                    //that code is already moved to LineGraph.vue
                }

                if(this.showGraph && (this.taDashboardData.userPropertyValue!=null && Object.keys(this.taDashboardData.userPropertyValue).length > 0)) {
                    totalResidentialRatingUnits = this.taDashboardData.userPropertyValue.totalResidentialRatingUnits;
                    totalCommercialRatingUnits = this.taDashboardData.userPropertyValue.totalCommercialRatingUnits;
                    totalLifestyleRatingUnits = this.taDashboardData.userPropertyValue.totalLifestyleRatingUnits;
                    totalRuralRatingUnits = this.taDashboardData.userPropertyValue.totalRuralRatingUnits;
                    totalOtherRatingUnits = this.taDashboardData.userPropertyValue.totalOtherRatingUnits;
                }

                var monthlyCompletedSubdivisions = new Array();
                var yearlyCompletedSubdivisions = 0;
                if(this.showGraph && (this.taDashboardData.taSubDivisionData!=null && Object.keys(this.taDashboardData.taSubDivisionData).length > 0)) {
                    for(var i=0;i<this.taDashboardData.taSubDivisionData.monthlySubdivisionSummaries.length;i++) {
                        monthlyCompletedSubdivisions.push(this.taDashboardData.taSubDivisionData.monthlySubdivisionSummaries.completedSubdivisions);
                    }
                    yearlyCompletedSubdivisions = this.taDashboardData.taSubDivisionData.monthlySubdivisionSummaries[this.taDashboardData.taSubDivisionData.monthlySubdivisionSummaries.length-1].yearToDateCompletedSubdivisions;
                }

                var taMarketMovement = new Array();
                var nzMarketMovement = new Array();
                var monthlyTAHousePriceIndex = new Array();
                var monthlyNZHousePriceIndex = new Array();
                var userTAName = "";


                if(this.showGraph && (this.taDashboardData.taHousePriceIndexSummary!=null && Object.keys(this.taDashboardData.taHousePriceIndexSummary).length > 0)) {
                    taMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.taOneMonthMarketMovement);
                    nzMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.nzOneMonthMarketMovement);

                    taMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.taThreeMonthMarketMovement);
                    nzMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.nzThreeMonthMarketMovement);

                    taMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.taSixMonthMarketMovement);
                    nzMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.nzSixMonthMarketMovement);

                    taMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.taOneYearMarketMovement);
                    nzMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.nzOneYearMarketMovement);

                    taMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.taSinceRevalMarketMovement);
                    nzMarketMovement.push(this.taDashboardData.taHousePriceIndexSummary.nzSinceRevalMarketMovement);

                    for(var j=0;j<this.taDashboardData.taHousePriceIndexSummary.priceIndexData.housePriceIndexData.length;i++) {
                        var obj = this.taDashboardData.taHousePriceIndexSummary.priceIndexData.housePriceIndexData[j];
                        if(obj.date == this.taDashboardData.taHousePriceIndexSummary.priceIndexData.currentRevisionDate){
                            monthlyTAHousePriceIndex.push({x: +new Date(obj.date), y: +obj.taHousePriceIndex, marker: {enabled: true,symbol: 'diamond',fillColor: '#FF990F',radius: 6}})
                        }
                        else if(obj.date == this.taDashboardData.taHousePriceIndexSummary.priceIndexData.previousRevisionDate){
                            monthlyTAHousePriceIndex.push({x: +new Date(obj.date), y: +obj.taHousePriceIndex, marker: {enabled: true,symbol: 'diamond',fillColor: '#FF990F',radius: 6}})
                        } else {
                            monthlyTAHousePriceIndex.push({x: +new Date(obj.date), y: +obj.taHousePriceIndex});
                        }
                        monthlyNZHousePriceIndex.push({x: +new Date("${it.date}"), y: +"${it.nzHousePriceIndex}"});
                    }
                }
                userTAName = this.taDashboardData.taHousePriceIndexSummary.priceIndexData.taName;
            }
        }
    }

</script>

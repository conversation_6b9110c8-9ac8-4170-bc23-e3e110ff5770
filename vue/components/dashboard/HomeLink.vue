<template>
    <!--<li class="dashboard-link" v-if="isExternalUser" @click="loadDashboard"><a href="#" class=""><label>My Dashboard</label></a></li>-->
    <li class="homeLink"
        v-if="isExternalUser || isInternalUser"
        @click="loadDashboard"
        title="Home"
    >
        <i class="material-icons">&#xE88A;</i>
    </li>
</template>

<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js'
    import { store } from '../../DataStore';
    import commonUtils from '../../utils/FormatUtils';

    export default {
        mixins: [commonUtils],
        computed: {
            ...mapState('userData', [
                'isExternalUser',
                'isInternalUser',
                'userTACode',
                'userId',
                'externalObjectionAccess'
            ]),
        },
        methods: {
            goToTaDashboard(taCode) {
                var eventObj = {};
                eventObj.taCode = taCode;
                eventObj.searchType = 'ta-dashboard';
                eventObj.onHomePage = true;
                this.emitDisplayEvent(eventObj);
            },
            goToSplashPage() {
                var eventObj = {};
                eventObj.searchType = 'splash-page';
                EventBus.$emit('display-content', eventObj);
                this.$router.push({ name: 'home' });
            },
            goToValuationJobs() {
                var eventObj = {};
                eventObj.searchType = 'valuation-jobs';
                eventObj.onHomePage = true;
                eventObj.viewValuationJobs = true;
                this.emitDisplayEvent(eventObj);
            },
            hasTACode() {
                return this.userTACode && this.userTACode != '';
            },
            loadDashboard() {
                if (this.isExternalUser && !this.externalObjectionAccess) {
                    if (this.hasTACode()) {
                        this.goToTaDashboard(this.userTACode);
                        return;
                    }
                    this.goToSplashPage();
                    return;
                }

                if (this.isInternalUser || this.externalObjectionAccess) {
                    var currentHomeSelection = localStorage.getItem(this.userId + 'current-home-selection')
                    if (currentHomeSelection) {
                        currentHomeSelection = JSON.parse(currentHomeSelection)
                        if (currentHomeSelection.valuers) {
                            this.goToValuationJobs();
                        }

                        if (currentHomeSelection.TA) {
                            this.goToTaDashboard(currentHomeSelection.TA);
                        }
                    } else {
                        this.goToValuationJobs();
                    }
                }
            },
            emitDisplayEvent: function(event) {
                if (this.$route.name === 'home') {
                    EventBus.$emit('display-content', event);
                }
                else {
                    this.$router.push({ name: 'home' }, () => {
                        this.$nextTick(() => {
                            EventBus.$emit('display-content', event);
                        });
                    });
                }
            }
        },
    }
</script>

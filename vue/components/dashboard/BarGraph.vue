<template>
    <div id="subdivisionchart"></div>
</template>



<script>

    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import _ from 'underscore';
    import Highcharts from "highcharts";

    export default {
        props: ['taCodeParam', 'showGraph'],
        data: function() {
            return {
                monthlyCompletedSubdivisions: [],
                yearlyCompletedSubdivisions: "",
                monthlyCompletedConsents: [],
                yearlyCompletedConsents: "",
            }
        },
        mounted: function() {
            const self = this;

            EventBus.$on('display-ta-barchart', function(monthlyCompletedSubdivisions, yearlyCompletedSubdivisions, monthlyCompletedConsents, yearlyCompletedConsents) {
                self.monthlyCompletedSubdivisions = monthlyCompletedSubdivisions;
                self.yearlyCompletedSubdivisions = yearlyCompletedSubdivisions;
                self.monthlyCompletedConsents = monthlyCompletedConsents;
                self.yearlyCompletedConsents = yearlyCompletedConsents;

                self.initialize(self.monthlyCompletedSubdivisions, self.yearlyCompletedSubdivisions, self.monthlyCompletedConsents, self.yearlyCompletedConsents);
            });
        },
        methods: {
            initialize: function (monthlyCompletedSubdivisions, yearlyCompletedSubdivisions, monthlyCompletedConsents, yearlyCompletedConsents) {
                Highcharts.chart("subdivisionchart", {
                        chart: {
                            zoomType: 'xy',
                            height: 294,
                            width: 830,
                        },
                        legend: {
                            itemStyle: {
                                fontSize: '12px',
                                fontWeight: 'bold',
                            }
                        },
                        credits: {
                            enabled: false
                        },
                        title: {
                            text: null
                        },
                        xAxis: [{
                            categories: ['JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC','JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN'],
                            crosshair: true,
                            lineColor: '#ccd6eb',
                            tickColor: '#ccd6eb',
                            tickWidth: 1,
                        }],
                        yAxis:[{ // Primary yAxis
                            labels: {
                                style: {
                                    color: Highcharts.getOptions().colors[1]
                                }
                            },
                            title: {
                                text: null,
                                style: {
                                    color: Highcharts.getOptions().colors[1]
                                }
                            }
                        }],
                        tooltip: {
                            pointFormat: 'Completed: <b>{point.y}</b>'
                        },
                        series: [ {
                            name: 'Building Consents',
                            type: 'column',
                            data: monthlyCompletedConsents,
                            color: '#7eb4ec',
                        }, {
                            name: 'Subdivisions',
                            type:'column',
                            data: monthlyCompletedSubdivisions,
                            color: '#434348',
                        }]
                    },
                );
            }
        }
    }

</script>

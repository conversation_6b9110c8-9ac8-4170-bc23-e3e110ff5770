<template>
    <div id="housepriceindexchart" style="height: 290px; margin: 0 auto"></div>
</template>

<script>

    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import _ from 'underscore';
    import Highcharts from "highcharts";

    export default {
        props: ['taCodeParam', 'showGraph', 'colors'],
        data: function() {
            return {
                monthlyTAHousePriceIndex: [],
                monthlyNZHousePriceIndex: [],
                userTAName: ""
            }
        },
        mounted: function() {
            const self = this;
            EventBus.$on('display-ta-splinechart', function(monthlyTAHousePriceIndex, monthlyNZHousePriceIndex, userTAName) {
                self.initialize(monthlyTAHousePriceIndex, monthlyNZHousePriceIndex, userTAName);
            });


        },
        methods: {
            initialize: function (monthlyTAHousePriceIndex, monthlyNZHousePriceIndex, userTAName) {
                Highcharts.chart("housepriceindexchart", {
                    chart: {
                        type: 'spline'
                    },
                    title: {
                        text: null
                    },
                    legend: {
                        itemStyle: {
                            fontSize: '12px',
                            fontWeight: 'bold',
                        }
                    },
                    xAxis: {
                        type: 'datetime',
                        dateTimeLabelFormats: {
                            year: '%Y'
                        },
                        lineColor: '#ccd6eb',
                        tickColor: '#ccd6eb',
                    },
                    credits: {
                        enabled: false
                    },
                    yAxis: {
                        title: {
                            text: null
                        },
                        labels: {
                            formatter: function () {
                                return this.value/1000 + 'k';
                            }
                        }
                    },
                    tooltip: {
                        xDateFormat: '%b-%Y',
                        crosshairs: true,
                        shared: true
                    },
                    plotOptions: {
                        spline: {
                            marker: {
                                enabled: false
                            }
                        }
                    },
                    colors: this.colors,
                    series: [{
                        name: userTAName,
                        marker: {
                            symbol: 'square'
                        },
                        data: monthlyTAHousePriceIndex,
                        lineWidth: 2,
                    }, {
                        name: 'New Zealand',
                        marker: {
                            symbol: 'diamond'
                        },
                        data: monthlyNZHousePriceIndex,
                        lineWidth: 2,
                    }]
                });
            }
        }
    }

</script>

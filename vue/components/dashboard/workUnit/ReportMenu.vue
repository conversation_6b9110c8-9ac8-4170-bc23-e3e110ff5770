<script setup>
import { computed } from 'vue';
import { store } from '@/DataStore';

const isReportingManager = computed(() => store.state.userData.isReportingManager);
</script>

<template>
    <div class="menu-area">
        <div class="report-category-name">
            Performance Metrics
        </div>
        <div class="report-category qv-flex-column">
            <router-link class="report-name" active-class="active" to="/dashboard/valuer-metrics">
                Valuer
            </router-link>
            <router-link
                v-if="isReportingManager"
                class="report-name"
                active-class="active"
                to="/reports/COST_CENTRE_REVENUE_SUMMARY"
            >
                Cost Centre Revenue Summary
            </router-link>
            <router-link
                v-if="isReportingManager"
                class="report-name"
                active-class="active"
                to="/reports/TA_RATING_SUMMARY"
            >
                TA Rating Summary
            </router-link>
        </div>
    </div>
</template>

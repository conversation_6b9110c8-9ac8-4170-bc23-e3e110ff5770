<script setup>
import { ref, onMounted, watch } from 'vue';
import HighCharts from 'highcharts';

const chart = ref(null)

const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    legend: {
        type: Array,
        default: () => []
    },
    data: {
        type: Array,
        default: () => []
    },
    dataField: {
        type: String,
        default: ''
    },
    chartColors: {
        type: Object,
        default: () => {}
    },
});

watch(() => props.data, () => {
    renderChart();
});

onMounted(() => {
    renderChart();
});

function formatChartData() {
    const data = [];
    
    for (const item of props.legend) {
        const total = props.data.filter(x => x.activity_id === item.value).reduce((a, item) => a + item[props.dataField], 0);
        data.push({
            name: item.name, 
            color: props.chartColors[item.value],
            y: total 
        });
    }

    return data;
}

function renderChart() {
    HighCharts.chart(
        chart.value,
        {
            chart: {
                type: 'pie',
                backgroundColor: 'transparent',
                dataLabels: {
                    enabled: true,
                    format: '<b>{point.name}</b>'
                },
                margin: [0, 0, 0, 0],
                spacingTop: 0,
                spacingBottom: 0,
                accessibility: {
                    enabled: false,
                },
            },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    size:'100%',
                    innerSize: '30%',
                    dataLabels: {
                        enabled: false,
                    }
                }
            },
            credits: {
                enabled: false,
            },

            title: {
                text: props.title,
                align: 'left',
            },

            series: [{
                name: props.title,
                data: formatChartData(),
            }],
        }
    )
}

</script>

<template>
    <div ref="chart"></div>
</template>
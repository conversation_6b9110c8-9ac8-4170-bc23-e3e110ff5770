<script setup>
import { ref, watch } from 'vue';
import DataCard from '@/components/dashboard/workUnit/DataCard.vue';
import AnalysisCard from '@/components/dashboard/workUnit/AnalysisCard.vue';
import ContainerCollapsible from 'Common/ContainerCollapsible.vue';

const props = defineProps({
    title: {
        type: String,
        default: 'Rating',
    },
    cards: {
        type: Array,
        require: true,
    },
    showAnalysis: {
        type: Boolean,
        default: false,
    },
    showTitle: {
        type: Boolean,
        default: true,
    },
    isExpanded: {
        type: Boolean,
        default: false,
    },
});

const expandView = ref(props.isExpanded);
const emit = defineEmits(['expandViewChange']);

watch(expandView, (newValue) => {
    emit('expandViewChange', newValue);
});

</script>

<template>
    <container-collapsible v-model="expandView" :title="title" class="dashboard-card-container">
        <div class="qv-flex-row cards">
            <data-card
                v-for="card in cards"
                :key="card.title"
                :card-color="card.color"
                :icon="card.icon"
                :title="card.title"
                :count="card.count"
                :calculation-based-on-hrs="card.calculationBasedOnHrs"
                data-cy="vd-data-card" />
        </div>
        <div v-if="props.showAnalysis" class="qv-flex-row cards">
            <analysis-card
                v-for="card in cards"
                :key="card.title"
                :card-color="card.color"
                :hours="card.hours"
                :rate-per-day="card.ratePerDay"
                :rate-per-activity="card.ratePerActivity"
                :stat="card.stat"
                :calculation-based-on-hrs="card.calculationBasedOnHrs"
                :show-title="showTitle"
                data-cy="vd-analysis-card" />
        </div>
    </container-collapsible>
</template>
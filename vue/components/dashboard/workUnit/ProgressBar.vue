<script setup>
import { ref, watch, onMounted } from 'vue';
import HighCharts from 'highcharts';

const container = ref(null);

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    target: {
        type: Number,
        default: 0
    },
});

watch(() => props.data, () => {
    renderChart();
});

watch(() => props.target, () => {
    renderChart();
});

onMounted(() => {
    renderChart();
});

function getFormattedBarData() {
    const barData = [];
    props.data?.reverse().forEach((e) => {
        barData.push({
            name: e.title,
            data: [Math.round((e.data / props.target * 100) * 100) / 100],
            color: e.color
        });
    });
    return barData;
}

function renderChart() {
    HighCharts.chart(container.value, {
        chart: {
            type: 'bar',
            height: 100
        },
        title: {
            text: null,
        },
        xAxis: {
            categories: ['Actual %'],
        },
        yAxis: {
            min: 0,
            title: {
                text: 'Target %'
            },
        },
        legend: {
            enabled: false
        },
        plotOptions: {
            series: {
                stacking: 'normal',
                dataLabels: {
                    enabled: false,
                }
            }
        },
        series: getFormattedBarData(),
        credits: {
            enabled: false
        }
    });
}
</script>

<style scoped>
#container {
    width: 100%;
}
</style>

<template>
    <div ref="container"></div>
</template>
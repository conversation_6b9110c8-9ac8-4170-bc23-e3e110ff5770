<script setup>
import { watch, computed, inject, ref } from 'vue';
import CustomProgressBar from '@/components/dashboard/workUnit/CustomProgressBar.vue';
import DashboardCard from '@/components/dashboard/workUnit/DashboardCard.vue';
import Multiselect from 'vue-multiselect';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import { getTodayDate, getStartOfCurrentMonth } from '@/components/dashboard/workUnit/utils.js';
import { useTaList } from '@/composables/taList';
import useValuerInfo from '@/composables/useValuerInfo';
import useValuerDashboard from '@/composables/useValuerDashboard';
import { formatPrice } from '@/utils/FormatUtils';

const { valuers, findValuerByNtUsername } = useValuerInfo();
const {
    loading,
    datePeriods,
    selectedTAs,
    ratingData,
    consultancyData,
    revenueBarData,
    chargeableHrsBarData,
    showAnalysisData,
    userData,
    trainingHours,
    percentTrainingHrs,
    currentData,
    loadDashboardData,
    setCustomRangeStatus,
    valuerRevenueTarget,
    actualRevenue,
    chargeableHours,
    chargeableHourRate,
    expandRating,
    expandConsultancy,
    toggleExpand,
    showProgressBar,
} = useValuerDashboard();
const taList = useTaList();

const dashboardUser = inject('dashboardUser');
const selectedDateFilter = inject('selectedDateFilter');
const selectedDateRange = inject('selectedDateRange');
const customRangeStatus = inject('customRangeStatus');

const filteredTAs = computed(() => taList.value?.filter(x => currentData.value?.map(y => y.rating_authority_id).includes(parseInt(x.id))) || []);
const totalHrs = computed(() => trainingHours.value + chargeableHours.value);
const percentActualRevenue = computed(() => Math.round((actualRevenue.value / valuerRevenueTarget.value) * 100));

const isCustomDateRange = ref((selectedDateFilter.value.code !== 'CustomRange'));
var disableCustomRangeWatch = (selectedDateFilter.value.code !== 'CustomRange');

watch(selectedDateRange.value, async () => {
    if (disableCustomRangeWatch) {
        return;
    }
    const hasPassedCheck = checkCustomRange();
    if (hasPassedCheck) {
        await loadDashboardData(dashboardUser.value, selectedDateFilter.value, selectedDateRange.value, customRangeStatus.value, false);
    }
});

watch(selectedDateFilter, async () => {
    disableCustomRangeWatch = true;
    await resetCustomRangeStatus();
    isCustomDateRange.value = (selectedDateFilter.value.code !== 'CustomRange');
    if (selectedDateFilter.value.code !== 'CustomRange') {
        setCustomRangeBlank();
        await loadDashboardData(dashboardUser.value, selectedDateFilter.value, selectedDateRange.value, customRangeStatus.value, true);
    }
    else {
        resetCustomRangeToDefault();
        disableCustomRangeWatch = false;
        await loadDashboardData(dashboardUser.value, selectedDateFilter.value, selectedDateRange.value, customRangeStatus.value, false);
    }
});

watch(dashboardUser, async () => {
    if(selectedTAs) {
        selectedTAs.value = [];
    }
    disableCustomRangeWatch = true;
    await resetCustomRangeStatus();
    isCustomDateRange.value = (selectedDateFilter.value.code !== 'CustomRange');
    if (selectedDateFilter.value.code === 'CustomRange') {
        const hasPassedCheck = checkCustomRange();
        await resetCustomRangeStatus();
        if (!hasPassedCheck) {            
            resetCustomRangeToDefault();
            updateCustomRangeStatus('general', 'info', 'Invalid date range: custom \"From\" date and \"To\" date have been reset to default dates');
        }
        disableCustomRangeWatch = false;
        await loadDashboardData(dashboardUser.value, selectedDateFilter.value, selectedDateRange.value, customRangeStatus.value, false);
        return;
    }
    await loadDashboardData(dashboardUser.value, selectedDateFilter.value, selectedDateRange.value, customRangeStatus.value, true);
});

watch(valuers, async () => {
    if(valuers.value.length > 0 && !dashboardUser?.value){
        dashboardUser.value = findValuerByNtUsername(`QVNZ\\${userData.value.userName}`);
    }
});

function updateCustomRangeStatus(field, type, message) {
    if(field === 'from') {
        customRangeStatus.value.from.type = type;
        customRangeStatus.value.from.message = message;
    }
    if(field === 'to') {
        customRangeStatus.value.to.type = type;
        customRangeStatus.value.to.message = message;
    }
    if(field === 'general') {
        customRangeStatus.value.general.type = type;
        customRangeStatus.value.general.message = message;
    }
}

async function resetCustomRangeStatus() {
    customRangeStatus.value.from.type = '';
    customRangeStatus.value.from.message = '';
    
    customRangeStatus.value.to.type = '';
    customRangeStatus.value.to.message = '';
    
    customRangeStatus.value.general.type = '';
    customRangeStatus.value.general.message = '';
    
    await setCustomRangeStatus(customRangeStatus.value);
}

function resetCustomRangeStatusField(field) {
    if(field === 'from') {
        customRangeStatus.value.from.type = '';
        customRangeStatus.value.from.message = '';
    }
    if(field === 'to') {
        customRangeStatus.value.to.type = '';
        customRangeStatus.value.to.message = '';
    }
    if(field === 'general') {
        customRangeStatus.value.general.type = '';
        customRangeStatus.value.general.message = '';
    }
}

function checkCustomRange() {
    let HasPassedTODateCheck = true;
    let HasPassedFROMDateCheck = true;
    
    const todayDate = getTodayDate();
    const todayDateFormatted = getTodayDate('D/M/YYYY');

    if (isBlank(selectedDateRange.value.from)) {
        updateCustomRangeStatus('from', 'error', 'Invalid date range: \"From\" date needs to be selected'); 
        HasPassedFROMDateCheck = false;
    }
    if (isBlank(selectedDateRange.value.to)) {
        updateCustomRangeStatus('to', 'error', 'Invalid date range: \"To\" date needs to be selected'); 
        HasPassedTODateCheck = false;
    }
    if (selectedDateRange.value.to > todayDate) {
        updateCustomRangeStatus('to', 'error', `Invalid date range: \"To\" date was greater than Today\'s date (${todayDateFormatted})`);
        HasPassedTODateCheck = false;
    }
    if (selectedDateRange.value.from > todayDate) {
        updateCustomRangeStatus('from', 'error', `Invalid date range: \"From\" date was greater than Today\'s date (${todayDateFormatted})`); 
        HasPassedFROMDateCheck = false;
    }    
    if (selectedDateRange.value.from > selectedDateRange.value.to) {
        updateCustomRangeStatus('from', 'error', 'Invalid date range: \"From\" date was greater than \"To\" date');
        HasPassedFROMDateCheck = false;
    }
    if (selectedDateRange.value.from > todayDate && selectedDateRange.value.from > selectedDateRange.value.to) {
        updateCustomRangeStatus('from', 'error', `Invalid date range: \"From\" date was greater than \"To\" date and Today\'s date (${todayDateFormatted})`); 
        HasPassedFROMDateCheck = false;
    }

    if(selectedDateRange.value.from !== getStartOfCurrentMonth() || selectedDateRange.value.to !== todayDate) {
        resetCustomRangeStatusField('general');
    }
    
    if(HasPassedTODateCheck) {
        resetCustomRangeStatusField('to');
    }
    if(HasPassedFROMDateCheck) {
        resetCustomRangeStatusField('from');
    }

    return HasPassedFROMDateCheck && HasPassedTODateCheck;
}

function resetCustomRangeToDefault() {
    selectedDateRange.value.from = getStartOfCurrentMonth();
    selectedDateRange.value.to = getTodayDate();
}

function setCustomRangeBlank() {
    selectedDateRange.value.from = null;
    selectedDateRange.value.to = null;
}

function isSetToCustomRange() {
    return (selectedDateFilter.value.code === 'CustomRange');
}

function isBlank(value) {
    return value === null || value === undefined || value === '';
}

function dataSummaryClass(value, isNonChargeable = false){
    if(value < 50){
        return isNonChargeable ? 'target-green' : 'target-red';
    }
    else if(value < 85){
        return 'target-orange';
    }
    else{
        return isNonChargeable ? 'target-red' : 'target-green';
    }
}

</script>

<template>
    <div class="qv-flex-column content-area mdl-shadow--3dp">
        <div class="qv-flex-row filter" data-cy="vd-filer-section">
            <div class="qv-flex-row"
                :class="{'graph-container' : showAnalysisData, 'filter-container': !showAnalysisData}"
            >
                    <div class="qv-flex-valuer-dashboard-filter-container">
                        <multiselect
                            class="qv-flex-3-multiselect"
                            v-model="dashboardUser"
                            :options="valuers || []"
                            track-by="id"
                            :preselect-first="true"
                            label="name"
                            placeholder="Select a Valuer"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            data-cy="vd-filter-valuer"
                            :allow-empty="false"
                        />
                        <multiselect
                            class="qv-flex-3-multiselect"
                            v-model="selectedDateFilter"
                            :options="datePeriods || []"
                            track-by="code"
                            :preselect-first="true"
                            label="description"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            data-cy="vd-filter-datePeriod"
                            :allow-empty="false"
                        />
                        <label class="qv-flex-0-date-label">From</label>                        
                        <date-picker
                            class="qv-flex-2-date-picker"
                            v-model="selectedDateRange.from"
                            format="D/M/YYYY"
                            type="date"
                            value-type="YYYY-MM-DD"
                            data-cy="vd-filter-custom-from-date"
                            :disabled="isCustomDateRange"
                        ></date-picker>
                        <label class="qv-flex-0-date-label">To</label>                        
                        <date-picker
                            class="qv-flex-2-date-picker"
                            v-model="selectedDateRange.to"
                            format="D/M/YYYY"
                            type="date"
                            value-type="YYYY-MM-DD"
                            data-cy="vd-filter-custom-to-date"
                            :disabled="isCustomDateRange"
                        ></date-picker>                        
                        <multiselect
                            class="qv-flex-4-multiselect"
                            v-model="selectedTAs"
                            :options="filteredTAs || []"
                            :multiple="true"
                            track-by="code"
                            label="description"
                            placeholder="Select TAs for Rating Tiles"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            data-cy="vd-filter-ta"
                        />
                    </div>
                </div>
            <div class="qv-flex-column data-container" v-if="showAnalysisData">
                <div class="dashboard-card qv-flex-column data-summary" :class="dataSummaryClass(percentTrainingHrs, true)">
                    <div class="title">NON CHARGEABLE HRS</div>
                    <div class="qv-flex-row value">
                        <span class="item"> {{ trainingHours }}</span>
                        <span class="item"> {{ `${percentTrainingHrs}%` }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="loading" class="spinner"></div>
        <template v-else>
            <div class="qv-flex-row" v-if="showAnalysisData">
                <div class="qv-flex-column graph-container">
                    <custom-progress-bar
                        :data="chargeableHrsBarData"
                        :target="totalHrs"
                        :label="'Chargeable Hrs'"
                        data-cy="vd-progress-bar"
                    />
                    <custom-progress-bar
                        v-if="showProgressBar"
                        :data="revenueBarData"
                        :target="valuerRevenueTarget"
                        :label="'Revenue'"
                        data-cy="vd-progress-bar"
                    />
                    <div v-else class="dashboard-card qv-flex-column notification-area mdl-shadow--3dp">
                        <strong>No Revenue Target assigned to the selected valuer. Unable to generate graph.</strong>
                    </div>
                </div>
                <div class="qv-flex-column data-container">
                    <div class="dashboard-card qv-flex-column data-summary" :class="dataSummaryClass(chargeableHourRate)">
                        <div class="title">
                            CHARGEABLE HRS
                        </div>
                        <div class="qv-flex-row value">
                            <span class="item"> {{ chargeableHours }}</span>
                            <span class="item"> {{ `${chargeableHourRate}%` }}</span>
                        </div>
                    </div>
                    <div class="dashboard-card qv-flex-column data-summary" :class="dataSummaryClass(percentActualRevenue)">
                        <div class="qv-flex-row title value">
                            <span class="item"> ACTUAL </span>
                            <span class="item"> TARGET </span>
                        </div>
                        <div class="qv-flex-row value">
                            <span class="item"> {{ formatPrice(actualRevenue) }}</span>
                            <span class="item"> {{ formatPrice(valuerRevenueTarget) }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <dashboard-card
                :title="'Rating'"
                :cards="ratingData"
                :show-analysis="showAnalysisData"
                :show-title=true
                :is-expanded="expandRating"
                @expandViewChange="toggleExpand('rating')"
                data-cy="vd-rating"
            />
            <dashboard-card
                :title="'Consultancy'"
                :cards="consultancyData"
                :show-analysis="showAnalysisData"
                :show-title=false
                :is-expanded="expandConsultancy"
                @expandViewChange="toggleExpand('consultancy')"
                data-cy="vd-consultancy"
            />
        </template>
    </div>
</template>

<style>
.router .qv-flex-valuer-dashboard-filter-container{
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    gap: 20px;
}

.router .multiselect.qv-flex-3-multiselect {
    flex:3;
}

.router .multiselect.qv-flex-4-multiselect {
    flex:4;
}

.router .mx-date-picker, .qv-flex-2-date-picker {
    flex: 2;
}

.router .mx-date-picker, .qv-flex-2-date-picker .mx-input {
    font-size: 0.8em; 
    height: 38px;
}

.router .qv-flex-0-date-label {
    margin-right: -15px;
    font-size: 0.9em;
    text-align: right;
    align-self: center;
    flex: 0;
}
</style>
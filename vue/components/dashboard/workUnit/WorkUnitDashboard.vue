<script setup>
import { computed, provide } from 'vue';
import Toolbar from '@/components/common/Toolbar.vue';
import ReportMenu from '@/components/dashboard/workUnit/ReportMenu.vue';
import useWorkUnitNotifications from '@/composables/useWorkUnitNotifications';
import { store } from '../../../DataStore';

const { notifications, dashboardUser, selectedDateFilter, selectedDateRange, customRangeStatus } = useWorkUnitNotifications();
const userData = computed(() => store.state.userData);
provide('dashboardUser', dashboardUser);
provide('selectedDateFilter', selectedDateFilter);
provide('selectedDateRange', selectedDateRange);
provide ('customRangeStatus', customRangeStatus);
</script>

<template>
    <div class="contentWrapper">
        <toolbar title="My Dashboard" />
        <div
            v-if="userData.isInternalUser"
            class="resultsWrapper"
        >
            <div class="qv-flex-row resultsInner-wrapper report-dashboard work-unit-dashboard mdl-shadow--3dp">
                <report-menu />
                <div class="qv-flex-column content-wrapper">
                    <div
                        v-if="notifications.length > 0"
                        class="qv-flex-column notification-area mdl-shadow--3dp"
                        data-cy="my-dashboard-notification-list"
                    >
                        <div
                            v-for="(notification, index) in notifications"
                            :key="index"
                            :class="notification.type"
                        >
                            <span>{{ notification.message }}</span>
                        </div>
                    </div>
                    <router-view />
                </div>
            </div>
        </div>
    </div>
</template>
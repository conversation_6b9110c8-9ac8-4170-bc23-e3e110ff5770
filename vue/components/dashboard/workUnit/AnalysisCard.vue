<script setup>
import { computed } from 'vue';
import { formatPrice } from '@/utils/FormatUtils';
const props = defineProps({
    cardColor: {
        type: String,
        default: ''
    },
    stat: {
        type: Number,
        required: true
    },
    ratePerDay: {
        type: Number,
        default: null,
    },
    hours: {
        type: Number,
    },
    ratePerActivity: {
        type: Number,
        default: null,
    },
    calculationBasedOnHrs: {
        type: Boolean,
        default: false
    },
    showTitle: {
        type: Boolean,
        default: true
    }
})

const title = computed(() => {
    if (props.showTitle) {
        return props.calculationBasedOnHrs ? 'Revenue calculated based on charge out rate per hour.' : 'Revenue calculated based on work unit rate per activity count.'
    }
    return 'Revenue calculated to the nearest dollar.';
});

function formatDecimal(val,decimal) {
    return val > 0 ? parseFloat(val).toFixed(decimal): 0;
}

</script>

<template>
    <div class="analysis-card dashboard-card qv-flex-column" :class="cardColor" :title="title">
        <span class="stat">{{ formatPrice(props.stat) }}</span>
        <span class="fw-b">{{ props.hours !== null ? `${props.hours} hrs` : '' }}</span>
        <span class="fw-b">{{ props.ratePerDay != null ? `${formatDecimal(props.ratePerDay, 1)} per day` : '' }}</span>
        <span class="fw-b">{{ props.ratePerActivity != null ? `${formatDecimal(props.ratePerActivity, 0)} per activity` : '' }}</span>
    </div>
</template>
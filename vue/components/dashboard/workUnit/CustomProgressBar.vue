<script setup>
import { computed } from 'vue';
import _ from 'lodash';

const props = defineProps({
    label: {
        type: String,
        default: ''
    },
    data: {
        type: Array,
        default: () => []
    },
    target: {
        type: Number,
        default: 0
    }
});

const INTERVAL = 5;
const totalActualValue = computed(() => props.data?.reduce((sum, item) => sum + item.data, 0));
const maxScale = computed(() =>  {
    const defaultScale = (100 / INTERVAL) + 1;
    const scale = Math.max(Math.round((totalActualValue.value / props.target * 100) / INTERVAL) + 1,defaultScale);
    return _.isNaN(scale) || !_.isFinite(scale) ? defaultScale : scale;
 });
const progressContainerWidth = computed(() => {
    if (totalActualValue.value <= props.target) {
        return '100%';
    } else {
        const nearestTens = Math.ceil(totalActualValue.value / 10) * 10;
        return `${(props.target / nearestTens) * 100}%`;
    }
});

function getFormattedData(data) {
    return data ? Math.round((data / props.target * 100) * 100) / 100 : '';
}
</script>

<template>
    <div>
        <div class="qv-flex-row bar">
            <div class="label">{{ label }}</div>
            <div class="progress-container">
                <div class="background" :style="{ width: progressContainerWidth }"></div>
                <div class="progress">
                    <div class="segment" v-if="getFormattedData(el.data) > 0" v-for="(el, index) in props.data"
                        :key="index" :style="{ width: `${getFormattedData(el.data)}%`, backgroundColor: el.color }">
                        <span class="tooltip" :style="{ color: el.color }">
                            {{ `${el.title}: ${getFormattedData(el.data)}%` }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="label"></div>
            <div class="progress-container scale">
                <div class="scale-container" :style="{ width: progressContainerWidth }">
                    <div v-for="n in maxScale" :key="n" class="scale-marker" :style="{ left: `${(n - 1) * INTERVAL}%` }">{{ (n - 1) * INTERVAL }}%</div>
                </div>
            </div>
        </div>
    </div>
</template>

<style>
.label {
    font-size: 1.2rem;
    width: 10%;
    font-weight: 600;
    padding-top: 7px;
}

.progress-container {
    width: 88%;
    position: relative;
}

.background {
    background-color: #faf4ba;
    height: 30px;
    position: absolute;
}

.progress {
    width: 100%;
    height: 30px;
    position: absolute;
    display: flex;
}

.segment {
    height: 60%;
    position: relative;
    top:0.5em;
}

.segment .tooltip {
    visibility: hidden;
    width: max-content;
    background-color: white;
    border: 1px solid #ccc;
    padding: 5px;
    border-radius: 3px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    position: absolute;
    top: -50px;
    left: 7cap;
    transform: translateX(-50%);
    z-index: 1;
    font-weight: 600;
    padding: 10px;
}

.segment:hover .tooltip {
    visibility: visible;
}

.scale {
    margin-top: 5px;
    border-top:0.5px solid;
}

.scale-container {
    width: 92%;
    position: relative;
    height: 12px;
}

.scale-marker {
    position: absolute;
    bottom: 0;
    transform: translateX(-50%);
    font-size: 0.8rem;
    font-weight: 600;
    color: #898d94;
    white-space: nowrap;
}
</style>
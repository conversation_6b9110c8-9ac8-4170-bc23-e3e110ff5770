<script setup>
import { ref, onMounted, watch } from 'vue';
import HighCharts from 'highcharts';

const chart = ref(null);

const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    legend: {
        type: Array,
        default: () => []
    },
    data: {
        type: Array,
        default: () => []
    },
    dataField: {
        type: String,
        default: () => []
    },
    chartColors: {
        type: Object,
        default: () => {}
    },
});

watch(() => props.data, () => {
    renderChart();
});

onMounted(() => {
    renderChart();
});

function formatChartData() {
    const chartData = [];

    for (const dataset of props.data) {
        const data = [];
        
        for (const item of props.legend) {
            const total = dataset.data.filter(x => `${x.year}-${x.month}` === item.value).reduce((a, item) => a + item[props.dataField], 0);
            data.push({
                color: props.chartColors[dataset.activity_id],
                y: total
            });
        }
        
        chartData.push({
            name: dataset.name,
            data: data,
        });
    }

    return chartData;
}

function renderChart() {
    HighCharts.chart(
        chart.value, 
        {
            chart: {
                type: 'column',
                backgroundColor: 'transparent',
                accessibility: {
                    enabled: false,
                },
            },

            credits: {
                enabled: false,
            },

            title: {
                text: props.title,
                align: 'left',
            },

            legend: {
                enabled: false,
            },

            xAxis: {
                categories: props.legend.map(x => x.name),
            },

            yAxis: {
                min: 0,
            },

            series: formatChartData(),
        }
    )
}

</script>

<template>
    <div ref="chart"></div>
</template>
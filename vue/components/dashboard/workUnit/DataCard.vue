<script setup>

const props = defineProps({
    cardColor: {
        type: String,
        default: ''
    },
    icon: {
        type: String,
        required: true,
    },
    title: {
        type: String,
        required: true
    },
    count: {
        type: Number
    },
    calculationBasedOnHrs: {
        type: Boolean,
        default: false
    }
})

</script>

<template>
    <div class="dashboard-card qv-flex-column" :class="props.cardColor">
        <i class="material-symbols-outlined">{{ props.icon }}</i>
        <span class="title">{{ props.title }}</span>
        <span class="stat">{{ props.calculationBasedOnHrs ? `${props.count} hrs` : props.count }}</span>
    </div>
</template>
import moment from 'moment';
import { DateTime } from 'luxon';

export function getYearLegend(data) {
    const legend = [];
    const values =  data.map(x => ({ name: `${moment(x.month, 'M').format('MMM')} ${x.year}`, value: `${x.year}-${x.month}`}));
    for (const value of values) {
        if (!legend.map(x=> x.name).includes(value.name)) {
            legend.push(value);
        }
    }
    return legend;
}

export function getTodayDate(format = 'YYYY-MM-DD') {
    return moment().local().format(format);
}

export function getStartOfCurrentMonth(format = 'YYYY-MM-DD') {
    return moment().startOf('month').local().format(format);
}

export function getPresetDatePeriod(periodDescription, yearOffset = 0) {
    if (periodDescription === 'CurrentMonth') {
        return {
            from: moment().startOf('month').add(yearOffset,'year').local().format('YYYY-MM-DD'),
            to: moment().add(yearOffset,'year').local().format('YYYY-MM-DD'),
        }
    }
    if (periodDescription === 'PreviousMonth') {
        return {
            from: moment().subtract(1, 'month').startOf('month').add(yearOffset,'year').local().format('YYYY-MM-DD'),
            to: moment().subtract(1, 'month').endOf('month').add(yearOffset,'year').local().format('YYYY-MM-DD'),
        }
    }
    if (periodDescription === 'CurrentQuarter') {
        return {
            from: moment().subtract(moment().month() % 3, 'month').startOf('month').add(yearOffset,'year').local().format('YYYY-MM-DD'),
            to: moment().add(yearOffset,'year').local().format('YYYY-MM-DD'),
        }
    }
    if (periodDescription === 'CurrentFinancialYear') {
        const year = moment().subtract(6, 'month').add(yearOffset,'year').year();
        return {
            from: year + '-07-01',
            to: moment().add(yearOffset,'year').local().format('YYYY-MM-DD'),
        }
    }
    if (periodDescription === 'LastFinancialYear') {
        const year = moment().subtract(6, 'month').add(yearOffset,'year').year();
        return {
            from: (year - 1) + '-07-01',
            to: year + '-06-30',
        }
    }
    if (periodDescription === 'LastFinancialQuarter') {
        const currentMonth = moment().month();
        let year = moment().year();
        let quarter = Math.floor(currentMonth / 3);
        if (quarter === 0) {
            quarter = 4;
            year--;
        }
        const startMonth = (quarter - 1) * 3 + 1;
        const endMonth = (quarter - 1) * 3 + 3;
        return {
            from: moment(`${year}-${String(startMonth).padStart(2, '0')}-01`).format('YYYY-MM-DD'),
            to: moment(`${year}-${String(endMonth).padStart(2, '0')}-${moment(`${year}-${String(endMonth).padStart(2, '0')}`, 'YYYY-MM').daysInMonth()}`).format('YYYY-MM-DD'),
        }
    }
}

export function isPreviousFinancialYear(selectedPeriod, date) {
    const month = DateTime.fromISO(date).month;
    const isLastFinancialYear = selectedPeriod === 'LastFinancialYear';
    const isLastFinancialQuarter = selectedPeriod === 'LastFinancialQuarter' && month >= 4 && month <= 6;
    return isLastFinancialYear || isLastFinancialQuarter;
}

export function getTarget(isPrevFinancialYear, revenueRoleTargetList, dashboardUser) {
    const targetCode = isPrevFinancialYear ? dashboardUser?.prevTarget : dashboardUser?.target;
    const target = revenueRoleTargetList?.find(t => t.code === targetCode)?.shortDescription || 0;
    return Number(target);
}

export function getPeriodLength(selectedDateFilter) {
    if (selectedDateFilter.endsWith('Quarter')) {
        return 3;
    }
    if (selectedDateFilter.endsWith('Year')) {
        return 12;
    }
    return 1;
}
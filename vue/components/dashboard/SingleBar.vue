<template>
    <div class="graphBox stackedCol">
        <h3>{{ title }}</h3>
        <ul>
            <li>
                <label>{{ topLabel }}</label>
                <h3>{{ topValue }}</h3>
            </li>
            <li>
                <label>{{ bottomLabel }}</label>
                <h3>{{ bottomValue }}</h3>
            </li>
            <li :style="computedStyle"></li>
        </ul>
    </div>

</template>

<script>
    export default {
        props: ['title', 'topLabel', 'topValue', 'bottomLabel', 'bottomValue'],
        data: function(){
            return {

            }

        },
        computed: {
            computedStyle: function(){
                return 'height:'+((this.bottomValue > 0) ? (this.topValue/this.bottomValue)*100 : 0)+'%';
            }
        },
        mounted: function() {
            self = this;
        }
    }
</script>
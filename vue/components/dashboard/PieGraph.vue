<template>
    <div id="piechart"></div>
</template>


<script>

    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import _ from 'underscore';
    import Highcharts from "highcharts";

    export default {
        props: ['taCodeParam', 'showGraph'],
        data: function() {
            return {
                series: []
            }

        },
        mounted: function() {
            this.initialize();
        },
        methods: {
            initialize: function () {
                var chart = new Highcharts.Chart({
                    chart: {
                        renderTo: "piechart",
                        type: 'pie',
                        width: 228,
                        height: 228,
                        spacing: [0, 0, 0, 0],
                    },
                    title: {
                        text: null,
                    },
                    tooltip: {
                        enabled: false,
                    },
                    credits: {
                        enabled: false
                    },
                    plotOptions: {
                        pie: {
                            size: '100%',
                            innerSize: '85%',
                            colors:['#0091dc','#173c64'],
                            dataLabels: {
                                enabled: false
                            },
                        }
                    },
                    series: [{
                        data: this.series
                    }]
                }, onComplete
                );
            }
        }
    }

</script>
<template>
    <div>
        <div id="revisionObjectionsChart"></div>
        <div id="revisionObjectionsChartText"></div>
    </div>
</template>


<script>

    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import _ from 'underscore';
    import Highcharts from "highcharts";
    import highchartsMore from 'highcharts/highcharts-more';
    import solidGauge from 'highcharts/modules/solid-gauge';

    highchartsMore(Highcharts);
    solidGauge(Highcharts);

    export default {
        props: ['taCodeParam', 'showGraph'],
        data: function(){
            return {
                decisionsIssuedPercentageSeries: new Array(),
                labelFormat: ""
            }

        },
        mounted: function() {
            const self = this;
            EventBus.$on('display-ta-gaugechart', function(decisionsIssuedPercentage, yearToDateReceivedObjections, userTAName) {
                self.decisionsIssuedPercentageSeries = [decisionsIssuedPercentage];
                self.labelFormat = '<div style="text-align:center; margin:-60px;"><span style="font-size:25px;color:black">'+decisionsIssuedPercentage+'%</span><br/>' +
                    '<span>Decisions Issued</span><hr/><span style="font-size:25px;color:black">'+yearToDateReceivedObjections+'</span></br><span>Objections Received</span></div>';

                self.initialize(self.decisionsIssuedPercentageSeries, self.labelFormat, userTAName);
            });
        },
        methods: {
            initialize: function (decisionsIssuedPercentageSeries, labelFormat, userTAName) {

                var gaugeOptions = {
                    chart: {
                        type: 'solidgauge',
                        width: 290,
                        height: 290
                    },
                    title: userTAName,
                    pane: {
                        center: ['50%', '50%'],
                        size: '90%',
                        startAngle: -90,
                        endAngle: 90,
                        background: {
                            innerRadius: '60%',
                            outerRadius: '100%',
                            shape: 'arc'
                        }
                    },
                    tooltip: {
                        enabled: false
                    },
                    // the value axis
                    yAxis: {
                        stops: [
                            [0, '#203E79'],
                            [1, '#203E79']
                        ],
                        lineWidth: 0,
                        tickWidth: 0,
                        minorTickInterval: null,
                        tickAmount: 2,
                        min: 0,
                        max: 100,
                        showFirstLabel: false,
                        showLastLabel: false
                    },

                    plotOptions: {
                        solidgauge: {
                            dataLabels: {
                                borderWidth: 0,
                                useHTML: true
                            }
                        }
                    },
                    credits: {
                        enabled: false
                    }
                };

                Highcharts.chart("revisionObjectionsChart", Highcharts.merge(gaugeOptions, {
                    series: [{
                        data: decisionsIssuedPercentageSeries,
                        dataLabels: {
                            format: labelFormat
                        }
                    }]
                }));
            }
        }
    }

</script>

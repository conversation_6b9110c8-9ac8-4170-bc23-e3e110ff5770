<template>
    <div id="historicalTrendDataChart"></div>
</template>
<script>

    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import _ from 'underscore';
    import Highcharts from "highcharts";

    export default {
        props: ['taCodeParam', 'colors'],
        data: function() {
            return {
                historicalTrendDataChartSeries: []
            }
        },
        mounted: function() {
            const self = this;
            EventBus.$on('display-ta-linechart', function(historicalTrendDataChartSeries, userTAName) {
                self.initialize(historicalTrendDataChartSeries, userTAName);
            });
        },
        methods: {
            initialize: function (historicalTrendDataChartSeries, userTAName) {
                var seriesData = _.map(historicalTrendDataChartSeries, function(obj){ return [Date.parse(obj[0]), obj[1]]; });
                Highcharts.chart("historicalTrendDataChart", {
                    chart: {
                        height: 294
                    },
                    title: {
                        text: null
                    },
                    credits: {
                        enabled: false
                    },
                    xAxis: {
                        type: 'datetime',
                        dateTimeLabelFormats: {
                            month: '%b'
                        },
                        lineColor: '#ccd6eb',
                        tickColor: '#ccd6eb',
                    },
                    yAxis: {
                        title: {
                            text: ''
                        }
                    },
                    tooltip: {
                        dateTimeLabelFormats: {
                            week: '%A, %b %e, %Y'
                        }
                    },
                    colors: this.colors,
                    lineWidth: 2,
                    series: [{
                        showInLegend: false,
                        name: 'Revision Objections',
                        data: seriesData,
                        marker: { 
                            enabled: false 
                        },
                        lineWidth: 2,
                    }]
                });
            }
        }
    }
</script>

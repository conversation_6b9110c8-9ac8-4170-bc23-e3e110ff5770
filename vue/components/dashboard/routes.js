export const dashboardRoutes = [
    {
        path: '/dashboard/valuer-metrics',
        name: 'valuer-metrics',
        component: () => import(/* webpackChunkName: "WorkUnitDashboard" */ '@/components/dashboard/workUnit/ValuerDashboard.vue'),
    },
    {
        path: 'COST_CENTRE_REVENUE_SUMMARY',
        name: 'report-COST_CENTRE_REVENUE_SUMMARY',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/CostCentreRevenueCriteria.vue')
    },
    {
        path: 'TA_RATING_SUMMARY',
        name: 'report-TA_RATING_SUMMARY',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/TaRatingCriteria.vue'),
    }
];
<template>
    <div class="graphBox simpleBar">
        <h3>{{ title }}</h3>
        <ul>
            <li class="darkBar">
                <label>{{ topLabel }}</label>
                <h3>{{ topValue }}</h3>
                <span :style="computedStyle1"></span>
            </li>
            <li class="lightBar">
                <label>{{ bottomLabel }}</label>
                <h3>{{ bottomValue }}</h3>
                <span :style="computedStyle2"></span>
            </li>
        </ul>
    </div>
</template>


<script>

    export default {
        props: ['title', 'topLabel', 'topValue', 'bottomLabel', 'bottomValue'],
        data: function(){
            return {

            }
        },
        computed: {
            computedStyle1: function() {
                return 'width:'+((this.topValue >= this.bottomValue && this.topValue > 0)? 100 : (this.topValue == 0 || this.bottomValue == 0) ? 0 : ((this.topValue*100)/this.bottomValue))+'%';
            },
            computedStyle2: function() {
                return 'width:'+((this.topValue <= this.bottomValue && this.bottomValue > 0) ? 100 : ((this.bottomValue == 0 || this.topValue == 0) ? 0 : ((this.bottomValue*100)/this.topValue)))+'%';
            }
        },
        mounted: function(){
            self = this;

        }
    }
</script>

<!--
computed: {
            width1 () {
                return  +   + '%';
            },
            width2 () {
                return ;
            }
        }-->
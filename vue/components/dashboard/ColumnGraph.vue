<template>
    <div id="marketmovementchart" style="height: 310px"></div>
</template>



<script>

    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import _ from 'underscore';
    import Highcharts from "highcharts";

    export default {
        props: ['taCodeParam', 'showGraph', 'colors'],
        data: function() {
            return {
                taMarketMovement: [],
                nzMarketMovement: [],
                userTAName: ""
            }
        },
        mounted: function() {
            const self = this;
            EventBus.$on('display-ta-columnchart', function(taMarketMovement, nzMarketMovement, taName) {
                self.taMarketMovement = taMarketMovement;
                self.nzMarketMovement = nzMarketMovement;

                self.initialize(self.taMarketMovement, self.nzMarketMovement, taName);
            });

        },
        methods: {
            initialize: function (taMarketMovement, nzMarketMovement, taName) {
                Highcharts.chart("marketmovementchart", {
                    chart: {
                        type: 'column',
                        //height: 310
                        // width: 900
                    },
                    title: {
                        text: null
                    },
                    legend: {
                        itemStyle: {
                            fontSize: '12px',
                            fontWeight: 'bold',
                        }
                    },
                    plotOptions: {
                        column: {
                            minPointLength: 10,
                        }
                    },
                    xAxis: {
                        categories: ['1 Month', '3 Month', '6 Month', '1 year', 'Since Reval'],
                        lineColor: '#ccd6eb',
                        tickColor: '#ccd6eb',
                        tickWidth: 1,
                    },
                    credits: {
                        enabled: false
                    },
                    yAxis: {
                        title: {
                            text: null
                        },
                        labels: {
                            formatter: function () {
                                return this.value + '%';
                            }
                        }
                    },
                    colors: this.colors,
                    tooltip: {
                        pointFormat: 'Movement: <b>{point.y:.1f} %</b>'
                    },
                    series: [{
                        name: taName,
                        data: taMarketMovement,
                    }, {
                        name: 'New Zealand',
                        data: nzMarketMovement,
                    }]
                });
            }
        }
    }

</script>

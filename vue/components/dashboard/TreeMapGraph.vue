<template>
    <div id="objectionsByCategory"></div>
</template>



<script>

    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import _ from 'underscore';
    import Highcharts from "highcharts";
    import highchartsTreeMap from 'highcharts/modules/treemap';

    highchartsTreeMap(Highcharts);

    export default {
        props: ['taCodeParam', 'showGraph'],
        data: function() {
            return {
                treeMap: [],
                taRevisionObjectionsSummary: {},
                userPropertyValue: {}
            }
        },
        mounted: function() {
            const self = this;

            EventBus.$on('display-ta-treemapchart', function(userPropertyValue, taRevisionObjectionsSummary, userTAName) {
                var totalResidentialRatingUnits = 0;
                var yearToDateResidentialObjections = 0;
                var yearToDateReceivedObjections = 0;
                var totalLifestyleRatingUnits = 0;
                var yearToDateLifestyleObjections = 0;
                var totalCommercialRatingUnits = 0;
                var yearToDateCommercialObjections = 0;
                var totalRuralRatingUnits = 0;
                var totalOtherRatingUnits = 0;
                var yearToDateRuralObjections = 0;
                var yearToDateOtherObjections = 0;

                if((userPropertyValue!=null && Object.keys(userPropertyValue).length > 0)) {
                    totalResidentialRatingUnits = userPropertyValue.totalResidentialRatingUnits;
                    totalLifestyleRatingUnits = userPropertyValue.totalLifestyleRatingUnits;
                    totalCommercialRatingUnits = userPropertyValue.totalCommercialRatingUnits;
                    totalRuralRatingUnits = userPropertyValue.totalRuralRatingUnits;
                    totalOtherRatingUnits = userPropertyValue.totalOtherRatingUnits;
                }

                if((taRevisionObjectionsSummary!=null && Object.keys(taRevisionObjectionsSummary).length > 0)){
                    yearToDateResidentialObjections = taRevisionObjectionsSummary.yearToDateResidentialObjections;
                    yearToDateReceivedObjections = taRevisionObjectionsSummary.yearToDateReceivedObjections;
                    yearToDateLifestyleObjections = taRevisionObjectionsSummary.yearToDateLifestyleObjections;
                    yearToDateCommercialObjections = taRevisionObjectionsSummary.yearToDateCommercialObjections;
                    yearToDateRuralObjections = taRevisionObjectionsSummary.yearToDateRuralObjections;
                    yearToDateOtherObjections = taRevisionObjectionsSummary.yearToDateOtherObjections;
                }


                self.treeMap = [{
                    name:'Residential',
                    color:'#204178',
                    rate: (totalResidentialRatingUnits > 0) ? (100*yearToDateResidentialObjections)/totalResidentialRatingUnits : 0,
                    value: (yearToDateReceivedObjections > 0) ? (100*yearToDateResidentialObjections)/yearToDateReceivedObjections : 0,
                    total: yearToDateResidentialObjections
                },
                    {
                        name:'Lifestyle',
                        color:'#7eb4ec',
                        rate: (totalLifestyleRatingUnits > 0) ? (100*yearToDateLifestyleObjections)/totalLifestyleRatingUnits : 0,
                        value: (yearToDateReceivedObjections > 0) ? (100*yearToDateLifestyleObjections)/yearToDateReceivedObjections : 0,
                        total: yearToDateLifestyleObjections
                    },
                    {
                        name:'Commercial/Industrial',
                        color: '#a9aaa4',
                        rate: (totalCommercialRatingUnits > 0) ? (100*yearToDateCommercialObjections)/totalCommercialRatingUnits : 0,
                        value: (yearToDateReceivedObjections > 0) ? (100*yearToDateCommercialObjections)/yearToDateReceivedObjections : 0,
                        total: yearToDateCommercialObjections
                    },
                    {
                        name:'Rural',
                        color: '#accc0c',
                        rate: (totalRuralRatingUnits > 0) ? (100*yearToDateRuralObjections)/totalRuralRatingUnits : 0,
                        value: (yearToDateReceivedObjections > 0) ? (100*yearToDateRuralObjections)/yearToDateReceivedObjections : 0,
                        total: yearToDateRuralObjections
                    },
                    {
                        name:'Utility/Other',
                        color: '#fc932f',
                        rate: (totalOtherRatingUnits > 0) ? (100*yearToDateOtherObjections)/totalOtherRatingUnits : 0,
                        value: (yearToDateReceivedObjections > 0) ? (100*yearToDateOtherObjections)/yearToDateReceivedObjections : 0,
                        total: yearToDateOtherObjections
                    }]
                self.initialize(self.treeMap);
            });
        },
        methods: {
            initialize: function (treeMap) {
                Highcharts.chart("objectionsByCategory", {
                    chart: {
                        height: 290,
                        width: 560
                    },
                    legend: {
                        itemStyle: {
                            fontSize: '11px',
                        }
                    },
                    series: [{
                        type: "treemap",
                        layoutAlgorithm: 'stripes',
                        alternateStartingDirection: true,
                        levels: [{
                            level: 1,
                            layoutAlgorithm: 'sliceAndDice',
                            dataLabels: {
                                enabled: false
                            }
                        }],
                        data: treeMap,
                        legendType: 'point',
                        showInLegend: true
                    }],
                    title: {
                        text: null
                    },
                    credits: {
                        enabled: false
                    },
                    tooltip: {
                        valueSuffix: 'E',
                        valueDecimals: 2,
                        formatter: function() { return ' ' +
                            this.point.name + '<br />' +
                            this.point.total + ' Objections' + '<br />' +
                            this.point.value.toFixed(1) + '% of Total Received' + '<br />' +
                            this.point.rate.toFixed(1) + '% Objection Rate'
                        }
                    },
                });
            }
        }
    }

</script>

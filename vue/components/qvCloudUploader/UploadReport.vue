<template>
    <div v-if="isAdminUser" class="QVHV-Container">
        <div class="QVHV-formSection">
            <div class="advSearch-row">
                <h2>QV Cloud Uploader</h2>
                <div class="advSearch-group twentyfivePct icons8-geo-fence">
                    <label>Report QPID</label>
                    <input type="number" v-model="formData.qpid" :disabled="!!qpid" class="advSearch-text"/>
                    <span class="error" v-if="formErrors.qpid">{{ formErrors.qpid }}</span>
                </div>
                <div class="advSearch-group twentyfivePct icons8-news-filled">
                    <label>Report Type</label>
                    <select v-model="formData.reportType" :disabled="!!reportType" v-if="classificationsLoaded"
                            class="advSearch-text">
                        <option v-for="option in reportTypes" :value="option.code">{{ option.description }}</option>
                    </select>
                    <span class="label" v-else>loading report types...</span>
                    <span class="error" v-if="formErrors.reportType">{{ formErrors.reportType }}</span>
                </div>
                <div class="advSearch-group twentyfivePct icons8-contacts">
                    <label>Website User ID</label>
                    <input type="number" v-model="formData.userId" :disabled="!!userId" class="advSearch-text"/>
                    <span class="error" v-if="formErrors.userId">{{ formErrors.userId }}</span>
                </div>
                <div class="advSearch-group twentyfivePct icons8-category-filled">
                    <label>Subscription ID</label>
                    <input type="number" v-model="formData.subscriptionId" class="advSearch-text"/>
                    <span class="error" v-if="formErrors.subscriptionId">{{ formErrors.subscriptionId }}</span>
                </div>
                <file-selector :multiple="false" accept-extensions=".pdf"
                               @validated="handleFilesValidated"
                               @changed="handleFilesChanged">
                    <div class="uploaderBody ">
                        <div class="dropzone">
                            <div class="dz-message">
                                <h3>{{ formData.file ? formData.file.name : 'Drop a PDF here to upload' }}</h3>
                                <div class="fileUploader-button">
                                    <span class="mdl-button mdl-js-button mdl-button--raised">
                                        or choose a{{ formData.file ? 'nother' : '' }} PDF from your computer
                                    </span>
                                </div>
                                <span class="error" v-if="formErrors.file">{{ formErrors.file }}</span>
                            </div>
                        </div>
                    </div>
                </file-selector>
                <div style="padding-top: 1.5em">
                    <button @click="submit" class="primary" type="button">Upload</button>
                    <div class="error" v-if="formErrors.form">{{ formErrors.form }}</div>
                    <div class="success" v-if="successMessage">{{ successMessage }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import {mapGetters, mapState} from 'vuex';

export default {
    props: {
        qpid: {
            default: null
        },
        reportType: {
            default: null
        },
        userId: {
            default: null
        }
    },
    data() {
        return {
            report: null,
            reportUploadUrl: null,
            formData: {
                qpid: null,
                userId: null,
                subscriptionId: null,
                file: null,
                reportType: null,
            },
            successMessage: null,
            formErrors: {
                qpid: null,
                userId: null,
                subscriptionId: null,
                file: null,
                reportType: null,
            },
        }
    },
    computed: {
        ...mapGetters([
            'classificationsLoaded',
        ]),
        ...mapState('userData', [
            'isAdminUser',
        ]),
        reportTypes() {
            return this.$store.getters.getCategoryClassifications('ValuationReportType');
        }
    },
    watch: {
        qpid: {
            handler(qpid) {
                this.formData.qpid = qpid;
            },
            immediate: true
        },
        userId: {
            handler(userId) {
                this.formData.userId = userId;
            },
            immediate: true
        },
        reportType: {
            handler(reportType) {
                this.formData.reportType = reportType;
            },
            immediate: true
        },
    },
    methods: {
        formValid() {
            this.successMessage = null;
            for (const key of Object.keys(this.formErrors)) {
                this.formErrors[key] = null;
            }

            if (!this.formData.qpid) {
                this.formErrors.qpid = 'QPID is required';
            }
            if (!this.formData.userId) {
                this.formErrors.userId = 'Website user ID is required';
            }
            if (!this.formData.file) {
                this.formErrors.file = 'Please select a file.';
            }
            if (!this.formData.reportType) {
                this.formErrors.reportType = 'reportType is required.';
            }

            for (const key of Object.keys(this.formErrors)) {
                if (this.formErrors[key]) {
                    return false;
                }
            }
            return true;
        },
        async submit() {
            if (!this.formValid()) {
                return;
            }
            if (!await this.createUploadedReportEntry()) {
                this.formErrors.form = 'Failed to create report.';
                return;
            }
            if (!await this.uploadReport()) {
                this.formErrors.form = 'Failed to upload report.';
                return;
            }
            if (!await this.completeReportUpload()) {
                this.formErrors.form = 'Failed to complete report.';
                return;
            }
            this.successMessage = 'Report uploaded successfully: ' + this.report.name;
        },
        async createUploadedReportEntry() {
            const data = {
                qpid: parseInt(this.formData.qpid),
                reportType: this.formData.reportType
            };
            try {
                const response = await axios({
                    method: 'post',
                    data: data,
                    url: jsRoutes.controllers.ReportUploadController.createUploadedReportEntry().url,
                });
                if (response.status !== 200) {
                    console.error(`Server responded with ${response.status} when creating report.`);
                    return false;
                }
                if (!response.data) {
                    return false;
                }
                this.report = response.data.report;
                this.reportUploadUrl = response.data.uploadURL;
            } catch (error) {
                console.error(error);
                return false;
            }
            return true;
        },
        async uploadReport() {
            try {
                const response = await axios({
                    method: 'put',
                    data: this.formData.file,
                    url: this.reportUploadUrl,
                    headers: {
                        'Content-Type': this.formData.file.type,
                    }
                });
                if (response.status !== 200) {
                    console.error(`Server responded with ${response.status} when uploading report.`);
                    return false;
                }
            } catch (error) {
                console.error(error);
                return false;
            }
            return true;
        },
        async completeReportUpload() {
            const data = {
                jobId: this.report.jobId,
                userId: parseInt(this.formData.userId),
            };
            if (this.formData.subscriptionId) {
                data.subscriptionId = parseInt(this.formData.subscriptionId);
            }
            try {
                const response = await axios({
                    method: 'post',
                    data: data,
                    url: jsRoutes.controllers.ReportUploadController.completeReportUpload().url,
                });
                if (response.status !== 200) {
                    console.error(`Server responded with ${response.status} when completing report.`);
                    return false;
                }
            } catch (error) {
                console.error(error);
                return false;
            }
            return true;
        },
        handleFilesValidated(result) {
            this.formErrors.file = '';
            this.formData.file = null;
            if (result === 'EXTENSION_ERROR') {
                this.formErrors.file = 'You can only upload a PDF';
            }
            if (result === 'FILE_SIZE_ERROR') {
                this.formErrors.file = 'File is too large';
            }
            if (result === 'MULTIFILES_ERROR') {
                this.formErrors.file = 'You can only upload one file';
            }
        },
        handleFilesChanged(files) {
            const [file] = Array.from(files);
            if (file.type !== 'application/pdf') {
                console.log(`file type is ${file.type}`)
                this.formErrors.file = 'You can only upload a PDF';
                return;
            }
            this.formData.file = file;
        },
    }
}
</script>

<style scoped lang="scss">
.QVHV-Container {
    display: block;
}

.QVHV-formSection {
    padding-bottom: 0;
    border-top: 1px solid rgba(237, 241, 245, 1);
}

h2 {
    margin: 0 !important;
    width: 100%;
    font-weight: 700;
    font-size: 2rem;
}

button.primary {
    color: rgb(255, 255, 255);
    background-color: rgb(84, 144, 219);
    min-width: 15rem;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    border: none;
    border-radius: 2px;
    padding: 0 16px;
    margin: 0;
    height: 36px;
}

.error, input {
    width: 100%;
}

.error {
    color: #ea2e2d;
}

.success {
    color: #009e40;
}

::v-deep {
    .fs-btn-select {
        width: 100%;
    }
}
</style>

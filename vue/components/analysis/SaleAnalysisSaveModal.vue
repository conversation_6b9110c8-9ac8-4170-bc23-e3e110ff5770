<template>
    <div class="sa-save-modal" :class="{closed: !open}">
        <div class="sa-modal">
            <div class="sa-modal-content" @click.prevent="" v-if="type === 'error'">
                <div class="loading" v-if="saving">
                    <img src="/assets/images/spinner.gif"/>
                </div>
                <div v-else-if="!saving && errors && errors.length > 0">
                    <h1 class="text-red-500">Invalid Analysis</h1>
                    <p>The following validations are failing:</p>
                    <div class="list error-list">
                        <ul>
                            <li v-for="(error, index) in errors" :key="index">
                                - {{ error }}
                            </li>
                        </ul>
                    </div>
                    <div class="button error-button">
                        <button @click="toggleOpen">Return to Analysis</button>
                    </div>
                </div>
            </div>
            <div class="sa-modal-content" @click.prevent="" v-if="type === 'warning'">
                <div v-if="warnings && warnings.length > 0">
                    <h1 class="text-orange-500">Warning</h1>
                    <div class="list warning-list">
                        <ul>
                            <li v-for="(warning, index) in warnings" :key="index">
                                - {{ warning }}
                            </li>
                        </ul>
                    </div>
                    <div class="button left warning-button">
                        <button @click="toggleOpen">Return to Analysis</button>
                    </div>
                    <div class="button right warning-button">
                        <button @click="callback">Continue</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'sa-save-modal',
    props: {
        open: {
            required: true,
        },
        saving: {
            required: false,
        },
        errors: {
            required: false,
        },
        warnings: {
            required: false,
        },
        type: {
            required: true,
            validator(value) {
                return ['warning', 'error'].includes(value);
            },
        },
    },
    methods: {
        toggleOpen() {
            this.$emit('modal:toggle');
        },
        callback() {
            this.$emit('modal:callback');
        },
    },
};
</script>

<style lang="scss" scoped>

$modal-height: 30rem;
$modal-width: 50rem;
$modal-padding: 2rem;

.sa-save-modal {
    z-index: 100000;
    background-color: transparentize(black, 0.8);
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    opacity: 1;
    transition: opacity 0.5s;

    &.closed {
        opacity: 0;
        pointer-events: none;
    }
}

.sa-modal {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 100000;
    top: calc(50% - (#{$modal-height} / 2));
}

.sa-modal-content {
    border-radius: 5px;
    background-color: white;
    margin-left: auto;
    margin-right: auto;
    padding: $modal-padding;
    width: $modal-width;
    height: $modal-height;
    font-weight: bold;
    font-size: 1.2rem;
    position: relative;

    .loading {
        padding-top: calc(#{$modal-height} / 3);

        img {
            width: 36px;
            height: 36px;
            margin-left: auto;
            margin-right: auto;
        }
    }
}


.error-list {
    color: var(--color-red-500);
}

.error-button {
    button {
        background-color: var(--color-red-500);

        &:hover {
            background-color: var(--color-red-800);
        }
    }
}

.warning-list {
    color: var(--color-orange-500);
}

.warning-button {
    button {
        background-color: var(--color-orange-500);

        &:hover {
            background-color: var(--color-orange-800);
        }
    }
}

.list {
    border: 1px solid;
    border-radius: 5px;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    margin-top: 1rem;
    font-size: 1.3rem;

}

.button {
    $button-width: 15rem;

    position: absolute;
    bottom: $modal-padding;
    left: calc((#{$modal-width} / 2) - 0.5rem - (#{$button-width} / 2));
    width: $button-width;

    &.left {
        left: $modal-padding;
    }

    &.right {
        left: unset;
        right: $modal-padding;
    }

    button {
        width: 100%;
        font-weight: 600;
        font-size: 1.3rem;
        padding: 0.5rem;
        color: white;
        border: none;
        border-radius: 5px;

        &:hover {
        }
    }
}
</style>

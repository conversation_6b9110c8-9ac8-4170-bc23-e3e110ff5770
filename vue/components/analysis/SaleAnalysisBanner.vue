<template>
    <div>
        <ul class="sa-analysisStatus"
            v-bind:class="{verified: analysis.analysisKind==='Verified' && !analysis.saleHasChanged, unVerified: analysis.analysisKind==='Unverified'}">
            <li class="sa-analysedBy" v-html="analysis.analysedBy"></li>
            <li class="sa-analysedDate" v-html="analysedOn"></li>
            <li class="sa-sale-to-process" v-if="sale.saleProcessingStatusId == 1">Processing Status: To Process</li>
        </ul>
        <div class="openProp"
             v-bind:class="{maoriLand: primaryProperty.landUse.isMaoriLand==='Y', unconfirmedSale: sale.saleStatus==='Unconfirmed', pendingSale: sale.saleStatus==='Pending'}">
            <div class="colCell address">
                <div class="fullAddress" v-bind:data-qupid=analysis.qpid>
                    <span v-html="this.displayAddressPrimary"></span>
                    <span v-html="this.displayAddressSecondary"></span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import PropertyUtils from '../../utils/PropertyUtils';
import FormatUtils from '../../utils/FormatUtils';

export default {
    name: 'sa-banner',
    mixins: [PropertyUtils, FormatUtils],
    props: {
        sale: {
            type: Object,
            required: true,
        },
        analysis: {
            type: Object,
            required: true,
        },
        primaryProperty: {
            type: Object,
            required: true,
        },
    },
    data: function() {
        return {
            analysedOn: null,
        };
    },
    computed: {
        displayAddressPrimary() {
            return this.generateAddress1(this.primaryProperty.address);
        },
        displayAddressSecondary() {
            return this.generateAddress2(this.primaryProperty.address,
                this.primaryProperty.territorialAuthority);
        },

    },
    methods: {},
    mounted() {
        this.analysedOn = this.formatDate(this.analysis.analysedOn, 'DD/MM/YYYY', false);
    },
};
</script>

<style lang="scss" scoped>
.openProp:before {
    content: unset;
}
</style>

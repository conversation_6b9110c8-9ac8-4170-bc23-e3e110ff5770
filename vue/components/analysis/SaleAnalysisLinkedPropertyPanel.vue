<template>
    <analysis-table :show-total-column="false">
        <template #title>
            <div>
                <div class="title">
                    Multi-Sale Linked Properties
                </div>
            </div>
        </template>
        <template #title-right>
            <div>
                <button
                    @click="openMap"
                    class="sa-button mdl-button mdl-js-button mdl-js-ripple-effect">
                    Map <i class="material-icons">call_made</i>
                </button>
            </div>
        </template>
        <template #columns>
            <div class="col-1">Description</div>
            <div class="col-1">&nbsp;</div>
            <div class="col-1">QPID</div>
            <div class="col-1">Category</div>
            <div class="col-2">Capital Value</div>
            <div class="col-2">Land Value</div>
            <div class="col-2">Value of Improvements</div>
            <div class="col-2">Land Area</div>
        </template>
        <div v-for="(property, index) in linkedProperties" :key="index"
             class="analysis-row-wrapper">
            <analysis-row :index="index"
                          v-on="$listeners" :show-total-column="false">
                <div class="col-1">
                    <div>
                        {{ property.valRef }}
                    </div>
                </div>
                <div class="col-1">
                    <button
                        @click="openProperty(property.qpid)"
                        class="sa-button mdl-button mdl-js-button mdl-js-ripple-effect">
                        PROPERTY <i class="material-icons">call_made</i>
                    </button>
                </div>
                <div class="col-1">
                    <table-input disabled :value="property.qpid"></table-input>
                </div>
                <div class="col-1">
                    <table-input disabled :value="property.category.code"></table-input>
                </div>
                <div class="col-2">
                    <table-input disabled :value="property.capitalValue" type="money"></table-input>
                </div>
                <div class="col-2">
                    <table-input disabled :value="property.landValue" type="money"></table-input>
                </div>
                <div class="col-2">
                    <table-input disabled :value="(property.capitalValue - property.landValue)"
                                 type="money"></table-input>
                </div>
                <div class="col-2">
                    <table-input disabled :value="property.landArea" type="number"></table-input>
                </div>
            </analysis-row>
        </div>
        <analysis-row :show-total-column="false">
            <div class="col-1"></div>
            <div class="col-1"></div>
            <div class="col-1"></div>
            <div class="col-1">
                Total Values
            </div>
            <div class="col-2">
                <table-input disabled :value="totalCV" type="money"></table-input>
            </div>
            <div class="col-2">
                <table-input disabled :value="totalLV" type="money"></table-input>
            </div>
            <div class="col-2">
                <table-input disabled :value="totalVI"
                             type="money"></table-input>
            </div>
            <div class="col-2">
                <table-input disabled :value="totalLandArea" type="number"></table-input>
            </div>
        </analysis-row>
    </analysis-table>
</template>
<script>
import AnalysisTable from './tables/AnalysisTable.vue';
import AnalysisRow from './tables/AnalysisRow.vue';
import TableInput from 'Common/tables/TableInput.vue';
import {openUrlInNewTab} from '@/utils/QivsUtils';

export default {
    name: 'sa-linked-property-panel',
    components: {AnalysisTable, AnalysisRow, TableInput},
    props: {
        analysis: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            linkedProperties: null,
            childProperties: null,
            totalCV: null,
            totalLV: null,
            totalVI: null,
            totalLandArea: null,
        };
    },
    computed: {},
    methods: {
        openProperty(qpid) {
            const route = this.$router.resolve(
                {name: 'property', params: {qpid: qpid}});
            openUrlInNewTab(route.href);
        },
        openMap() {
            this.$emit('openMap');
        },
    },
    mounted() {
        this.linkedProperties = this.analysis.linkedProperties.filter(
            (p) => [0, 2, 5].includes(parseInt(p.apportionmentCode)));
        this.totalCV = this.linkedProperties.reduce((acc, p) => acc += p.capitalValue, 0);
        this.totalLV = this.linkedProperties.reduce((acc, p) => acc += p.landValue, 0);
        this.totalVI = this.totalCV - this.totalLV;
        this.totalLandArea = this.linkedProperties.reduce((acc, p) => acc += p.landArea, 0);
        this.totalLandArea = parseFloat(this.totalLandArea.toFixed(4));
        this.childProperties = this.analysis.linkedProperties.reduce((map, property) => {
            if (property.parentQpid && parseInt(property.apportionmentCode) === 1) {
                return map.set(property.parentQpid,
                    [...(map.get(property.parentQpid) || []), property]);
            }

            return map;
        }, new Map());
    },
};
</script>

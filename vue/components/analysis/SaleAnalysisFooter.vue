<template>
    <div>
        <div class="sa-footer-margin">
        </div>
        <div class="sa-footer-wrapper">
            <div class="saControls">
                <div id="saTotalsNetSalePriceDiv" class="sa-Totals">
                    <label>Net Sale Price</label>
                    <span>&nbsp;{{ formatMoneyField(sale.price.net) }}</span>
                </div>
                <div id="saTotalsCurrentDifferenceDiv" class="sa-Totals">
                    <label>Current Difference</label>
                    <span>&nbsp;{{ formatMoneyField(currentDifference) }}</span>
                </div>
                <div id="saTotalsMainBuildingsRateDiv" class="sa-Totals">
                    <label>Land Sale Price /Ha</label>
                    <span>&nbsp;{{
                            formatMoneyField(
                                lspHa)
                        }}</span>
                </div>
                <div id="" class="sa-Totals">
                    <label>Land Sale Price /Prodn</label>
                    <span>
                        &nbsp;{{ formatMoneyField(lspProduction) }}
                    </span>
                </div>

                <i title="Restore" class="material-icons saButton restoreSalesAnalysis"
                   @click="restore">&#xE8B3;</i>
                <i title="Delete"
                   class="material-icons saButton deleteSalesAnalysis"
                   @click="deleteAnalysis"
                >delete_forever</i>
                <i
                    title="Save & Print"
                    class="material-icons saButton"
                    @click="print"
                >print</i>
                <span class="sa-useBenchmark">
                    <label for="Benchmark Property">Use as Benchmark</label>
                    <input type="checkbox" id="Benchmark Property" v-model="analysis.isBenchmark"
                           class="benchmark-trigger">
                </span>
            </div>
        </div>
    </div>
</template>

<script>
import FormatUtils from '@/utils/FormatUtils';
import PropertyUtils from '@/utils/PropertyUtils';

export default {
    name: 'sa-footer',
    mixins: [FormatUtils, PropertyUtils],
    props: {
        sale: {
            type: Object,
            required: true,
        },
        analysis: {
            type: Object,
            required: true,
        },
        primaryProperty: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {};
    },
    computed: {
        lspProduction() {
            return this.primaryProperty.landUse.production > 0
                ? this.analysis.analysedLV / this.primaryProperty.landUse.production
                : null;
        },
        lspHa() {
            return this.primaryProperty.landUse.landArea > 0 ? this.analysis.analysedLV /
                this.primaryProperty.landUse.landArea : null;
        },
        currentDifference() {
            return (this.analysis.totalVI + this.analysis.totalLV) - this.sale.price.net;
        },
    },
    methods: {
        print() {
            this.$emit('print');
        },
        deleteAnalysis() {
            this.$emit('delete');
        },
        restore() {
            this.$emit('restore');
        },
        formatMoneyField(value) {
            if (!value && value !== 0) {
                return null;
            }
            return this.formatPrice(value, '$0,0');
        },
    },
};
</script>

<style lang="scss" scoped>
$footer-height: 82px;

.sa-footer-wrapper {
    position: fixed;
    height: $footer-height;
    width: 100%;
    bottom: 0;
    z-index: 1000;
}

.sa-footer-margin {
    padding-top: 5rem;
    height: $footer-height;
}

</style>

<template>
<div class="salesAnalysis-wrapper">
    <div class="uploaderHeader mdl-tabs mdl-js-tabs mdl-js-ripple-effect mdl-js-ripple-effect--ignore-events is-upgraded" data-upgraded=",MaterialTabs,MaterialRipple">
        <div class="mdl-tabs__tab-bar">
            <a href="#" class="mdl-tabs__tab is-active">Sales Analysis<span class="mdl-tabs__ripple-container mdl-js-ripple-effect" data-upgraded=",MaterialRipple"><span class="mdl-ripple"></span></span></a>
            <!--<a href="#" class="mdl-tabs__tab">Property Information<span class="mdl-tabs__ripple-container mdl-js-ripple-effect" data-upgraded=",MaterialRipple"><span class="mdl-ripple"></span></span></a>-->
        </div>
        <span class="righty" href="#"><i id="save-and-close" class="save-and-close mdl-button blanc active">Verify &amp; Close</i></span>
    </div>


    <div class="salesAnalysis-form">
        <div id="error-calculation" v-html="calculationError"></div>
        <ul class="sa-analysisStatus" v-bind:class="{verified: analysis.analysisKind==='Verified', unVerified: analysis.analysisKind==='Unverified'}">
            <li class="sa-analysedBy" v-html="analysis.analysedBy"></li>
            <li class="sa-analysedDate" v-html="analysis.analysisDate"></li>
        </ul>
        <div class="resultsRow openProp salesRow" v-bind:class="{maoriLand: sale.isMaoriLand==='Yes', unconfirmedSale: sale.status==='Unconfirmed', pendingSale: sale.status==='Pending'}">
            <div class="colCell address">
                <span class="primaryThumb-Wrapper"><img class="primaryPhoto_thumb" src="images/2470415_1-thumb.jpeg"></span>
                <div class="fullAddress" v-bind:data-qupid=sale.qupid>
                    <span v-html="sale.address1"></span>
                    <span v-html="sale.address2"></span>
                    <div class="md-link">
                        <div class="colCell qpid" v-html="sale.qupid"></div>
                        <div class="colCell valref" v-html="sale.valRef"></div>
                        <div class="colCell sa-category" v-html="sale.category"></div>
                        <ul class="lastReval-date">
                            <li v-html="sale.currentRevisionDate"></li>
                        </ul>
                    </div>
                </div>
                <div class="sales-trafficLights">
                    <div class="colCell saleClassification" v-bind:class="{classOne: sale.priceValueRelationship=='1', classTwo: sale.priceValueRelationship=='2', classThree: sale.priceValueRelationship=='3'}" v-html="sale.classification"></div>
                    <div class="colCell saleStatus" v-html="sale.status"></div>	<!-- THIS ELEMENT CAN INCLUDE ".statusPending" or ".statusUnconfirmed" BASED ON THE SALE STATE -->
                    <div class="colCell saleDate" v-html="sale.saleDate"></div> 	<!-- THIS ELEMENT CAN INCLUDE ".statusPending" or ".statusUnconfirmed" BASED ON THE SALE STATE -->
                    <ul class="vendPurchase">
                        <li v-html="sale.parties"></li>
                    </ul>
                    <span class="saleId" v-html="sale.qivsSaleId"></span>
                </div>
            </div>
            <div class="colCell valref"></div>
            <div class="searchDetails-wrapper">
                <div id="saleSaleDateDiv" class="colCell saleDate" v-html="sale.saleDate"></div>
                <div id="saleGrossPriceDiv" class="colCell gsp" v-html="sale.grossPrice"></div>
                <div id="saleNetPriceDiv" class="colCell nsp">{{ sale.netPrice }}<div class="nsptfa">{{ sale.saleNetRate }}<span>/ m<sup>2</sup></span></div></div>
                <div id="saleNspCVDiv" class="colCell nsptocv" v-html="sale.nspCV"></div>
                <div id="saleCapitalValueDiv" class="colCell capval">{{ sale.capitalValue }}<div class="cvnr">{{ sale.cvNetRate }}<span>/ m<sup>2</sup></span></div></div>
                <div id="saleLandValueDiv" class="colCell landval">{{ sale.landValue }}<div class="lvnr">{{ sale.lvNetRate }}<span>/ m<sup>2</sup></span></div></div>
                <div id="saleValueOfImprovementsDiv" class="colCell valimp">{{ sale.valueOfImprovements }}<div class="vinr">{{ sale.viNetRate }}<span>/ m<sup>2</sup></span></div></div>
                <div id="saleChattelSDiv" class="colCell chattels" v-html="sale.chattels"></div>
                <div id="saleStatusDiv" class="colCell saleStatus" v-html="sale.status"></div>
                <div id="saleLandAreaDiv" class="colCell landarea" v-html="sale.landArea"></div>
                <div id="saleTfaTlaDiv" class="tfaTla-wrapper">
                    <span id="saleTFASpan" class="colCell tfa">{{ sale.TFA }}<span>m<sup>2</sup></span></span>
                    <span  id="saleTLASpan" class="colCell tla">{{ sale.TLA }}<span>m<sup>2</sup></span></span>
                </div>
                <div id="saleGstDiv" class="colCell salegst" v-html="sale.gst"></div>
                <div id="saleOtherDiv" class="colCell saleother" v-html="sale.other"></div>
            </div>
        </div>

        <div class="salesAnalysis-table sa-details ">
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-th spacer"><span></span></div>
                <div class="salesAnalysis-th sa-analysedLV"><span>NSP</span></div>
                <div class="salesAnalysis-th sa-analysedLV"><span>Analysed LV</span></div>
                <div class="salesAnalysis-th sa-analysedVI"><span>Analysed VI</span></div>
                <div class="salesAnalysis-th sa-nsp_tfa"><span>NSP/TFA</span></div>
                <div class="salesAnalysis-th sa-nsp_tla"><span>NSP/TLA</span></div>
                <div class="salesAnalysis-th sa-analysedLNR"><span>Analysed LNR</span></div>
                <div class="salesAnalysis-th sa-nsp_cv"><span>NSP/CV</span></div>
                <div class="salesAnalysis-th sa-alv_lv"><span>ALV/LV</span></div>
                <div class="salesAnalysis-th sa-alv_vi"><span>AVI/VI</span></div>
                <div class="salesAnalysis-th sa-qvhpi"><span>QV HPI</span></div>
                <div class="salesAnalysis-th sa-qvlvi"><span>QV LVI</span></div>
                <div class="salesAnalysis-th spacer"><span></span></div>
            </div>
            <div id="salesAnalysisSummaryTableDiv" class="salesAnalysis-row">
                <div class="salesAnalysis-td spacer"><span></span></div>
                <div id="saleNetPrice" class="salesAnalysis-td sa-nsp calculated"><span><input tabindex="-1" type="text" v-model="sale.netPrice"></span></div>
                <div id="analysisAnalysedLV" class="salesAnalysis-td sa-analysedLV calculated"><span><input tabindex="-1" type="text" v-model = "analysis.analysedLV"></span></div>
                <div id="analysisAnalysedVI" class="salesAnalysis-td sa-analysedVI calculated"><span><input tabindex="-1" type="text" v-model = "analysis.analysedVI"></span></div>
                <div id="saleNspTfa" class="salesAnalysis-td sa-nsp_tfa calculated"><span><input tabindex="-1" type="text" v-model = "sale.nsp_tfa"></span></div>
                <div id="saleNspTla" class="salesAnalysis-td sa-nsp_tla calculated"><span><input tabindex="-1" type="text" v-model = "sale.nsp_tla"></span></div>
                <div id="analysisAnalysedLNR" class="salesAnalysis-td sa-analysedLNR calculated"><span><input tabindex="-1" type="text" v-model="analysis.analysedLNR"></span></div>
                <div id="saleNspCV" class="salesAnalysis-td sa-nsp_cv calculated"><span><input tabindex="-1" type="text" v-model = "sale.nspCV"></span></div>
                <div id="analysisAlvLv" class="salesAnalysis-td sa-alv_lv calculated"><span><input tabindex="-1" type="text" v-model = "analysis.alv_lv"></span></div>
                <div id="analysisAviVi" class="salesAnalysis-td sa-alv_vi calculated"><span><input tabindex="-1" type="text" v-model = "analysis.avi_vi"></span></div>
                <div id="analysisHousePriceIndex" class="salesAnalysis-td sa-qvhpi calculated"><span><input tabindex="-1" type="text" v-model = "analysis.housePriceIndex"></span></div>
                <div id="analysisLandValueIndex" class="salesAnalysis-td sa-qvlvi calculated"><span><input tabindex="-1" type="text" v-model = "analysis.landValueIndex"></span></div>
                <div class="salesAnalysis-td spacer"><span></span></div>
            </div>
        </div>



        <div class="sa-summary md-summary">
            <h3>Property Summary</h3>
            <!--<div class="edit_area" id="div_4" v-html="summary"></div>-->
            <textarea type="text" placeholder="Add a summary..." v-model="summary"></textarea>
        </div>
        <div class="sa-salesComment md-summary">
            <label>Sales Analysis Comment<span class="sa-salesRemarks" v-html="analysis.remarks"></span></label>
            <textarea type="text" placeholder="Add a comment..." v-model="analysis.comment"></textarea>
        </div>

        <div id="saResidualLandCalculationsDiv" class="calculationPanel salesAnalysis-table">
            <input type="checkbox" id="Residual Land Analysis" class="calculationPanel-trigger" @click="showResidualLandCalculations = !showResidualLandCalculations; scrollToEnd('saResidualLandCalculationsDiv');">
            <label for="Residual Land Analysis">
                <h3>Residual Land Analysis</h3>
            </label>
            <div v-if="showResidualLandCalculations ">
                <residual-land-analysis v-bind:analysis="residualLandAnalysis"></residual-land-analysis>
            </div>
        </div>

        <div id="saNetRateCalculationsDiv" class="calculationPanel salesAnalysis-table">
            <input type="checkbox" checked id="Net Rate Calculations" class="calculationPanel-trigger" @click="showNetRateCalculations = !showNetRateCalculations; scrollToEnd('saNetRateCalculationsDiv');">
            <label for="Net Rate Calculations">
                <h3>Net Rate Calculations</h3>
            </label>
            <div v-if="showNetRateCalculations">
                <div id="saLandTableDiv" class="salesAnalysis-table advSearch-row sa-land ">
                    <h3>Land</h3>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                        <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                        <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                        <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                        <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                        <div class="salesAnalysis-th sa-addRemove"></div>
                    </div>
                    <div class="salesAnalysis-row" v-for="land, key in analysis.landAnalysis">
                        <div class="salesAnalysis-td sa-description"><span><input type="text" v-model="land.description"></span></div>
                        <div class="salesAnalysis-td sa-area"><span><input type="text" v-model="land.areaInSquareMeter" @change="analyseLandChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-rate"><span><input type="text" v-model="land.pricePerSquareMeter" @change="analyseLandChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-value"><span><input type="text" v-model="land.price" @change="analyseManualLandChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-runnintgTotal"><span v-if="key==(analysis.landAnalysis.length-1)" v-html="analysis.landAnalysis.runningTotal"></span></div>
                        <div class="salesAnalysis-td sa-addRemove"><i v-bind:data-index="key" class="saRow-add addLandRow material-icons">&#xE147;</i><i v-bind:data-index="key" v-if="key>0" class="saRow-remove removeLandRow material-icons">&#xE15C;</i></div>
                    </div>
                </div>

                <div id="saOtherImprovementsTableDiv" class="salesAnalysis-table advSearch-row sa-oli ">
                    <h3>Other Improvements</h3>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                        <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                        <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                        <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                        <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                        <div class="salesAnalysis-th sa-addRemove"></div>
                    </div>
                    <div class="salesAnalysis-row" v-for="otherImprovement, key in analysis.otherImprovementsAnalysis">
                        <div class="salesAnalysis-td sa-description" v-bind:class="{calculated: otherImprovement.calculated}"><span><input type="text" tabindex="-1" v-model="otherImprovement.description"></span></div>
                        <div class="salesAnalysis-td sa-area"><span><input type="text" v-model="otherImprovement.areaInSquareMeter" @change="analyseOtherImprovementChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-rate"><span><input type="text" v-model="otherImprovement.pricePerSquareMeter" @change="analyseOtherImprovementChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-value"><span><input type="text" v-model="otherImprovement.price" @change="analyseManualOtherImprovementChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-runnintgTotal"><span v-if="key==(analysis.otherImprovementsAnalysis.length-1)" v-html="analysis.otherImprovementsAnalysis.runningTotal"></span></div>
                        <div class="salesAnalysis-td sa-addRemove"><i v-bind:data-index="key" class="saRow-add addOtherImprovementsRow material-icons">&#xE147;</i><i v-bind:data-index="key" v-if="key>0" class="saRow-remove removeOtherImprovementsRow material-icons">&#xE15C;</i></div>
                    </div>
                </div>

                <div id="saOtherBuildingsTableDiv" class="salesAnalysis-table advSearch-row sa-otherBuildings ">
                    <h3>Other Buildings</h3>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                        <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                        <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                        <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                        <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                        <div class="salesAnalysis-th sa-addRemove"></div>
                    </div>
                    <div class="salesAnalysis-row" v-for="otherBuilding, key in analysis.otherBuildingsAnalysis">
                        <div class="salesAnalysis-td sa-description" v-bind:class="{calculated: otherBuilding.calculated}"><span><input type="text" tabindex="-1" v-model="otherBuilding.description"></span></div>
                        <div class="salesAnalysis-td sa-area"><span><input type="text" v-model="otherBuilding.areaInSquareMeter" @change="analyseOtherBuildingChanges(key, otherBuilding.description)"></span></div>
                        <div class="salesAnalysis-td sa-rate"><span><input type="text" v-model="otherBuilding.pricePerSquareMeter" @change="analyseOtherBuildingChanges(key, otherBuilding.description)"></span></div>
                        <div class="salesAnalysis-td sa-value"><span><input type="text" v-model="otherBuilding.price" @change="analyseManualOtherBuildingChanges(key, otherBuilding.description)"></span></div>
                        <div class="salesAnalysis-td sa-runnintgTotal"><span v-if="key==(analysis.otherBuildingsAnalysis.length-1)" v-html="analysis.otherBuildingsAnalysis.runningTotal"></span></div>
                        <div class="salesAnalysis-td sa-addRemove"><i v-bind:data-index="key" class="saRow-add addOtherBuildingsRow material-icons">&#xE147;</i><i v-bind:data-index="key" v-if="key>0" class="saRow-remove removeOtherBuildingsRow material-icons">&#xE15C;</i></div>
                    </div>
                </div>

                <div id="saMainBuildingsTableDiv" class="salesAnalysis-table advSearch-row sa-mainBuildings ">
                    <h3>Main Buildings</h3>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                        <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                        <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                        <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                        <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                        <div class="salesAnalysis-th sa-addRemove"></div>
                    </div>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-td sa-description"><span><input type="text" v-model="analysis.mainBuildingAnalysis.description"></span></div>
                        <div class="salesAnalysis-td sa-area"><span><input type="text" v-model="analysis.mainBuildingAnalysis.areaInSquareMeter" @change="analyseMainBuildingChanges()"></span></div>
                        <div class="salesAnalysis-td sa-rate calculated"><span><input tabindex="-1" type="text" v-model="analysis.mainBuildingAnalysis.pricePerSquareMeter" ></span></div>
                        <div class="salesAnalysis-td sa-value calculated"><span><input tabindex="-1" type="text" v-model="analysis.mainBuildingAnalysis.price"></span></div>
                        <div class="salesAnalysis-td sa-runnintgTotal"><span v-html="analysis.mainBuildingAnalysis.price"></span></div>
                        <div class="salesAnalysis-td sa-addRemove"></div>
                    </div>
                </div>
            </div>
        </div>

        <div id="saGrossRateCalculationsDiv" class="salesAnalysis-table calculationPanel">
            <input type="checkbox" id="Gross Rate Calculations" class="calculationPanel-trigger" @click="showGrossRateCalculations = !showGrossRateCalculations; scrollToEnd('saGrossRateCalculationsDiv');">
            <label for="Gross Rate Calculations">
                <h3>Gross Rate Calculations</h3>
            </label>
            <div v-if="showGrossRateCalculations">
                <div id="SaleValueTableDiv" class="salesAnalysis-table advSearch-row">
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-th sa-description"><span></span></div>
                        <div class="salesAnalysis-th sa-area"><span>Gross Sale Price</span></div>
                        <div class="salesAnalysis-th sa-rate"><span>Chattels</span></div>
                        <div class="salesAnalysis-th sa-value"><span>Net Sale Price</span></div>
                        <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                        <div class="salesAnalysis-th sa-addRemove"></div>
                    </div>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-td sa-description calculated"><span><input type="text" tabindex="-1" value="Sale Values"></span></div>
                        <div class="salesAnalysis-td sa-area calculated"><span><input type="text" v-model="sale.grossPrice"></span></div>
                        <div class="salesAnalysis-td sa-rate calculated"><span><input type="text" v-model="sale.chattels"></span></div>
                        <div class="salesAnalysis-td sa-value calculated"><span><input type="text" v-model="sale.netPrice"></span></div>
                        <div class="salesAnalysis-td sa-runnintgTotal"><span v-html="sale.netPrice"></span></div>
                        <div class="salesAnalysis-td sa-addRemove"></div>
                    </div>
                </div>

                <div id="DeductionsTableDiv" class="salesAnalysis-table advSearch-row">
                    <h3>Deductions</h3>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                        <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                        <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                        <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                        <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                        <div class="salesAnalysis-th sa-addRemove"></div>
                    </div>
                    <div class="salesAnalysis-row" v-for="deduction, key in analysis.grossRateDeductionsAnalysis">
                        <div class="salesAnalysis-td sa-description"><span><input type="text" v-model="deduction.description"></span></div>
                        <div class="salesAnalysis-td sa-area"><span><input type="text" v-model="deduction.areaInSquareMeter" @change="analyseGrossRateDeductionsChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-rate"><span><input type="text" v-model="deduction.pricePerSquareMeter" @change="analyseGrossRateDeductionsChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-value"><span><input type="text" v-model="deduction.price" @change="analyseManualGrossRateDeductionsChanges(key)"></span></div>
                        <div class="salesAnalysis-td sa-runnintgTotal"><span v-if="key==(analysis.grossRateDeductionsAnalysis.length-1)" v-html="analysis.grossRateDeductionsAnalysis.runningTotal"></span></div>
                        <div class="salesAnalysis-td sa-addRemove"><i v-bind:data-index="key" class="saRow-add addGrossRateDeductionsRow material-icons">&#xE147;</i><i v-bind:data-index="key" v-if="key>0" class="saRow-remove removeGrossRateDeductionsRow material-icons">&#xE15C;</i></div>
                    </div>
                </div>

                <div id="MainUnitTableDiv" class="salesAnalysis-table advSearch-row">
                    <h3>Main Unit Adjusted Gross Rate</h3>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                        <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                        <div class="salesAnalysis-th sa-rate"><span> Gross Rate /m<sup>2</sup></span></div>
                        <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                        <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                        <div class="salesAnalysis-th sa-addRemove"></div>
                    </div>
                    <div class="salesAnalysis-row">
                        <div class="salesAnalysis-td sa-description"><span><input type="text" v-model="analysis.mainUnitDescription"></span></div>
                        <div class="salesAnalysis-td sa-area "><span><input type="text" v-model="analysis.mainUnitArea"></span></div>
                        <div class="salesAnalysis-td sa-rate calculated"><span><input type="text" v-model="mainUnitGrossRate"></span></div>
                        <div class="salesAnalysis-td sa-value calculated"><span><input type="text" v-model="mainUnitAnalysedValue"></span></div>
                        <div class="salesAnalysis-td sa-runnintgTotal"><span v-html="mainUnitAnalysedValue"></span></div>
                        <div class="salesAnalysis-td sa-addRemove"></div>
                    </div>
                </div>
            </div>
        </div>


        <div id="saInvestmentMethodCalculationDiv" class="salesAnalysis-table calculationPanel sa-mainBuildings ">
            <input type="checkbox" id="Investment Method Calculation" class="calculationPanel-trigger" @click="showInvestmentMethod = !showInvestmentMethod; scrollToEnd('saInvestmentMethodCalculationDiv');">
            <label for="Investment Method Calculation">
                <h3>Investment Method Calculation</h3>
            </label>
            <div v-if="showInvestmentMethod" class="salesAnalysis-table advSearch-row">
                <div class="salesAnalysis-row">
                    <div class="salesAnalysis-th sa-description">
                        <span>Description</span>
                    </div>
                    <div class="salesAnalysis-th sa-rentWeekly">
                        <span>Analysed Rent (Weekly)</span>
                    </div>
                    <div class="salesAnalysis-th sa-rentAnnual">
                        <span>Analysed Rent (Annual)</span>
                    </div>
                    <div class="salesAnalysis-th sa-salePrice">
                        <span>Sale Price</span>
                    </div>
                    <div class="salesAnalysis-th sa-runnintgTotal">
                        <span></span>
                    </div>
                    <div class="salesAnalysis-th sa-addRemove">
                    </div>
                </div>
                <div class="salesAnalysis-row advSearch-row">
                    <div class="salesAnalysis-td sa-description calculated">
                        <span>
                            <input type="text" tabindex="-1" value="Gross Yields">
                        </span>
                    </div>
                    <div class="salesAnalysis-td sa-rentWeekly">
                        <span>
                            <input type="text" v-model="analysis.analysedWeeklyRent" @keyup="calculateRent(0)">
                        </span>
                    </div>
                    <div class="salesAnalysis-td sa-rentAnnual">
                        <span>
                            <input type="text" v-model="analysis.analysedAnnualRent" @keyup="calculateRent(1)">
                        </span>
                    </div>
                    <div class="salesAnalysis-td sa-salePrice calculated">
                        <div><label>Net Sale</label><span><input tabindex="-1" type="text" :value="sale.netPrice" readonly></span></div>
                        <div><label>Gross Sale</label><span><input tabindex="-1" type="text" :value="sale.grossPrice" readonly></span></div>
                    </div>
                    <div class="salesAnalysis-td sa-runnintgTotal">
                        <div><label>Net Sale Price Gross Yield</label><span>{{ formatDecimal(analysis.netSalePriceGrossYield, 2) }}%</span></div>
                        <div><label>Gross Sale Price Gross Yield</label><span>{{ formatDecimal(analysis.grossSalePriceGrossYield, 2) }}%</span></div>
                    </div>
                    <div class="salesAnalysis-td sa-addRemove"></div>
                </div>
            </div>
        </div>


        <div id="saExtraInvestmentDetailsDiv" class="salesAnalysis-table calculationPanel">
            <input type="checkbox" id="Investment Details" class="calculationPanel-trigger" @click="showInvestmentDetails = !showInvestmentDetails; scrollToEnd('saExtraInvestmentDetailsDiv');">
            <label for="Investment Details">
                <h3>Extra Property Investment Details</h3>
            </label>
            <div v-if="showInvestmentDetails">
                <investment-details v-if="sale.propertyId" :property="sale.propertyId" :qupid="sale.qupid" :units="sale.propertyUnits"
                                    :rent="{amount: revision.predictedWeeklyMarketRent, basis: revision.marketRentBasis}"></investment-details>
            </div>
        </div>

        <div class="salesAnalysis-table sa-revisionAnalysis" v-if="revision.hasRevision">
            <h3>Revision Value Analysis</h3>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-td"><label>Revision CV:</label><span v-html="revision.revisionCapVal"></span></div>
                <div class="salesAnalysis-td"><label>Revision Gross Rate:</label><span v-html="revision.grossRate"></span></div>
                <div class="salesAnalysis-td"><label>RCV/NSP:</label><span v-html="formatDecimal(revision.revalrcvtonsp, 2)"></span></div>
                <div class="salesAnalysis-td"><label>RCV/CV:</label><span v-html="formatDecimal(revision.revalrcvtocv, 2)"></span></div>
            </div>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-td"><label>Revision LV:</label><span v-html="revision.revisionLandVal"></span></div>
                <div class="salesAnalysis-td"><label>Revision Land Rate:</label><span v-html="revision.landRate"></span></div>
                <div class="salesAnalysis-td"><label>RLV/ALV:</label><span v-html="formatDecimal(revision.revalrlvtoalv, 2)"></span></div>
                <div class="salesAnalysis-td"><label>RLV/LV:</label><span v-html="formatDecimal(revision.revalrlvtolv, 2)"></span></div>
            </div>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-td"><label>Revision VI:</label><span v-html="revision.revalValueOfImprovements"></span></div>
                <div class="salesAnalysis-td"><label>Auto Revision Net Rate:</label><span v-html="revision.netRate"></span></div>
                <div class="salesAnalysis-td"><label>RVI/AVI:</label><span v-html="formatDecimal(revision.revalrvitoavi, 2)"></span></div>
                <div class="salesAnalysis-td"><label>RVI/VI:</label><span v-html="formatDecimal(revision.revalrvitovi, 2)"></span></div>
            </div>
        </div>
   
        
        <div style="display:none" v-html="originalResponse"></div>
        <div style="display:none" v-html="loggedInUserName"></div>
    </div>
    <div class="sa-footer-margin">
    </div>
    <div class="sa-footer-wrapper">
        <div class="saControls">
            <div id="saTotalsNetSalePriceDiv" class="sa-Totals">
                <label>Net Sale Price</label>
                <span v-html="sale.netPrice"></span>
            </div>
            <div id="saTotalsCurrentDifferenceDiv" class="sa-Totals">
                <label>Current Difference</label>
                <span v-html="analysis.currentDifference"></span>
            </div>
            <div id="saTotalsMainBuildingsRateDiv" class="sa-Totals">
                <label>Main Buildings Rate /m<sup>2</sup></label>
                <span v-html="analysis.mainBuildingAnalysis.pricePerSquareMeter"></span>
            </div>
            <div id="" class="sa-Totals">
                <label>Net Sale Price Gross Yield</label>
                <span>{{ formatDecimal(analysis.netSalePriceGrossYield, 2) }}%</span>
            </div>
            <div id="" class="sa-Totals">
                <label>Gross Rate /m <sup>2</sup></label>
                <span>{{mainUnitGrossRate}}</span>
            </div>
            <i title="Restore" class="material-icons saButton restoreSalesAnalysis">&#xE8B3;</i>
            <i title="Delete" class="material-icons saButton deleteSalesAnalysis">delete_forever</i>
            <span class="sa-useBenchmark">
                <label for="Benchmark Property">Use as Benchmark</label>
                <input type="checkbox" id="Benchmark Property" v-model="analysis.isBenchmark" class="benchmark-trigger">
            </span>
        </div>
    </div>
</div>
</template>

<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore.js';
    import numeral from 'numeral';
    import propertyUtils from '../../utils/PropertyUtils';
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import InvestmentDetails from './InvestmentDetails.vue';
    import ResidualLandAnalysis from './ResidualLandAnalysis.vue';
    import moment from 'moment';
    import { displaySalesAnalysis } from '@/services/SalesAnalysisController';

    export default {
        mixins: [propertyUtils, formatUtils, commonUtils],
        components: {
            InvestmentDetails,
            ResidualLandAnalysis
        },
        inject: ['saleData'],
        data: function() {
            return {
                sale: {},
                analysis: { mainBuildingAnalysis: {}},
                residualLandAnalysis: { mainBuildingAnalysis: {}, landAnalysis: {} },
                summary: {},
                revision: {},
                originalResponse: {},
                loggedInUserName: '',
                calculationError: '',
                showResidualLandCalculations: false,
                showNetRateCalculations: true,
                showGrossRateCalculations: false,
                showInvestmentMethod: false,
                showInvestmentDetails: false,

            }
        },
        computed: {
            ...mapState('userData', [
                'userFullName',
            ]),
            mainUnitGrossRate: function(){
                var self=this;
                return numeral(Math.round(self.analysis.mainUnitGrossRate)).format('$0,0');
            },
            mainUnitAnalysedValue: function(){
                var self=this;
                return numeral(numeral(self.sale.netPrice).value() - self.analysis.totalAnalysedGrossRateDeductionsValueNum).format('$0,0');
            },
        },
        methods: {
            calculateRent: function(key) {
                var self = this;
                var analysedWeeklyRent = numeral(self.analysis.analysedWeeklyRent).value();
                var analysedAnnualRent = numeral(self.analysis.analysedAnnualRent).value();
                if(key == 0) {
                    if(analysedWeeklyRent) {
                        analysedAnnualRent = analysedWeeklyRent * 52;
                    } else {
                        analysedAnnualRent = null;
                    }
                } else {
                    if(analysedAnnualRent) {
                        analysedWeeklyRent = analysedAnnualRent / 52;
                    } else {
                        analysedWeeklyRent = null;
                    }
                }
                var netPrice = numeral(self.sale.netPrice).value();
                var grossPrice = numeral(self.sale.grossPrice).value();
                self.analysis.netSalePriceGrossYield = (analysedAnnualRent && netPrice) ? ((analysedAnnualRent / netPrice) * 100) : 0;
                self.analysis.grossSalePriceGrossYield = (analysedAnnualRent && grossPrice) ? ((analysedAnnualRent / grossPrice) * 100) : 0;
                self.analysis.analysedAnnualRent = self.formatPrice(analysedAnnualRent,'$0,0');
                self.analysis.analysedWeeklyRent = self.formatPrice(analysedWeeklyRent,'$0,0');
            },
            analyseManualGrossRateDeductionsChanges: function(index){
                var self = this
                var grossRateDeductionsAnalysis = self.analysis.grossRateDeductionsAnalysis
                $.each(grossRateDeductionsAnalysis, function(i, obj) {
                    if (i == index) {
                        var grossRateDeductionsArea = numeral(obj.areaInSquareMeter).value()
                        var price = numeral(obj.price).value()
                        if (grossRateDeductionsArea && price) {
                            obj.pricePerSquareMeter = numeral(price).divide(numeral(grossRateDeductionsArea).value()).format('$0,0')
                        }
                    }
                });
                self.analyseGrossRateDeductionsChanges(-1)
            },

            analyseManualLandChanges: function(index){
                var self = this
                var landAnalysis = self.analysis.landAnalysis
                $.each(landAnalysis, function(i, obj) {
                    if (i == index) {
                        var landArea = numeral(obj.areaInSquareMeter).value()
                        var price = numeral(obj.price).value()
                        if (landArea && price) {
                            obj.pricePerSquareMeter = numeral(price).divide(numeral(landArea).value()).format('$0,0')
                        }
                    }
                });
                self.analyseLandChanges(-1)
            },

            analyseManualOtherImprovementChanges: function(index){
                var self = this
                var otherImprovementsAnalysis = self.analysis.otherImprovementsAnalysis
                $.each(otherImprovementsAnalysis, function(i, obj) {
                    if (i == index) {
                        var landArea = numeral(obj.areaInSquareMeter).value()
                        var price = numeral(obj.price).value()
                        if (landArea && price) {
                            obj.pricePerSquareMeter = numeral(price).divide(numeral(landArea).value()).format('$0,0')
                        }
                    }
                });
                self.analyseOtherImprovementChanges(-1)
            },

            analyseManualOtherBuildingChanges: function(index, fieldName){
                var self = this
                var otherBuildingsAnalysis = self.analysis.otherBuildingsAnalysis
                $.each(otherBuildingsAnalysis, function(i, obj) {
                    if (i == index) {
                        var landArea = numeral(obj.areaInSquareMeter).value()
                        var price = numeral(obj.price).value()
                        if (landArea && price) {
                            obj.pricePerSquareMeter = numeral(price).divide(numeral(landArea).value()).format('$0,0')
                        }
                    }
                });
                self.analyseOtherBuildingChanges(-1, fieldName)
            },

            analyseMainBuildingChanges: function(){
                var self = this
                var price = numeral(self.analysis.mainBuildingAnalysis.price).value()
                var area = numeral(self.analysis.mainBuildingAnalysis.areaInSquareMeter).value()
                if (area && price) {
                    self.analysis.mainBuildingAnalysis.pricePerSquareMeter = numeral(price).divide(numeral(area).value()).format('$0,0')
                }
                 self.updateRunningTotals()
            },

            analyseOtherImprovementChanges: function(index){
                var self = this
                var otherImprovementsAnalysis = self.analysis.otherImprovementsAnalysis
                $.each(otherImprovementsAnalysis, function(i, obj) {
                    if (i == index) {
                        var landArea = numeral(obj.areaInSquareMeter).value()
                        var price = numeral(obj.pricePerSquareMeter).value()
                        if (landArea && price) {
                            obj.price = numeral(landArea).multiply(numeral(price).value()).format('$0,0')
                        }
                    }
                });
                self.updateRunningTotals()

                var netSalePrice = numeral(self.sale.netPrice).value()
                var totalAnalysedLandValueNum = numeral(self.analysis.landAnalysis.runningTotal).value()

                var totalOtherImprovementsAnalysedValueNum = numeral(self.analysis.otherImprovementsAnalysis.runningTotal).value()

                var underMainRoofGaragesAreaInM2 = numeral(self.sale.TFA).subtract(numeral(self.sale.TLA).value())
                var numOfFreeStandingGarages = numeral(self.sale.freeStandingGarages).value()

                var otherBuildingsAnalysis = self.analysis.otherBuildingsAnalysis

                $.each(otherBuildingsAnalysis, function(i, obj) {
                    var landAreaInSquareMeter = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var description = obj.description
                    if (description && description.toLowerCase().indexOf('garage fs') === 0){
                        obj.price = numeral(self.analysis.analysedVI).multiply(0.03*numOfFreeStandingGarages).format('$0,0')
                        obj.pricePerSquareMeter = numeral(obj.price).divide(numeral(obj.areaInSquareMeter).value()).format('$0,0')
                    }
                });
                self.updateRunningTotals()

                $.each(otherBuildingsAnalysis, function(i, obj) {
                    var landAreaInSquareMeter = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var description = obj.description
                    if (description && description.toLowerCase().indexOf('garage umr') === 0){
                        var p1_1 = numeral(self.analysis.analysedVI).value()
                        var p1_2 = numeral(self.analysis.otherImprovementsAnalysis.runningTotal).value()
                        var p1_3 = numeral(self.analysis.otherBuildingsAnalysis.runningTotal).value()
                        var p1_4 = (obj.price && obj.price != '') ? numeral(obj.price).value() : 0

                        //TODO: Someone please clean up this code! It appears in three different places
                        if (underMainRoofGaragesAreaInM2.value() != landAreaInSquareMeter && landAreaInSquareMeter) {
                            underMainRoofGaragesAreaInM2 = numeral(landAreaInSquareMeter)
                        }
                        if(underMainRoofGaragesAreaInM2.value() > 0){
                            var p1 = 0.65* (p1_1 - p1_2 - (p1_3 - p1_4))

                            var p2 = numeral(self.sale.TLA).value() + (0.65 * underMainRoofGaragesAreaInM2.value())
                            obj.pricePerSquareMeter = numeral(p1 / p2).format('$0,0')

                            obj.price = numeral(underMainRoofGaragesAreaInM2).multiply(numeral(obj.pricePerSquareMeter).value()).format('$0,0')
                        }
                    }
                });

                self.updateRunningTotals()
                var totalOtherBuildingsAnalysedValueNum = numeral(self.analysis.otherBuildingsAnalysis.runningTotal).value()

                var mainBuildingAnalysedVal = netSalePrice - totalAnalysedLandValueNum - totalOtherImprovementsAnalysedValueNum - totalOtherBuildingsAnalysedValueNum
                self.analysis.mainBuildingAnalysis.pricePerSquareMeter = numeral(mainBuildingAnalysedVal).divide(numeral(self.analysis.mainBuildingAnalysis.areaInSquareMeter).value()).format('$0,0')
                self.analysis.mainBuildingAnalysis.price = numeral(mainBuildingAnalysedVal).format('$0,0')
                self.updateRunningTotals()

            },

            analyseOtherBuildingChanges: function(index, fieldName) {
                var self = this
                var otherBuildingsAnalysis = self.analysis.otherBuildingsAnalysis
                $.each(otherBuildingsAnalysis, function(i, obj) {
                    if (i == index) {
                        var landArea = numeral(obj.areaInSquareMeter).value()
                        var price = numeral(obj.pricePerSquareMeter).value()
                        if (landArea && price) {
                            obj.price = numeral(price).multiply(numeral(landArea).value()).format('$0,0')
                        }
                    }
                });
                self.updateRunningTotals()

                var isUMREdited = false
                if (fieldName && fieldName.toLowerCase().indexOf('garage umr') === 0) {
                    isUMREdited = true
                }

                var underMainRoofGaragesAreaInM2 = numeral(self.sale.TFA).subtract(numeral(self.sale.TLA).value())
                $.each(otherBuildingsAnalysis, function(i, obj) {
                    var landAreaInSquareMeter = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var description = obj.description
                    if (description && description.toLowerCase().indexOf('garage umr') === 0 && !isUMREdited){
                        var p1_1 = numeral(self.analysis.analysedVI).value()
                        var p1_2 = numeral(self.analysis.otherImprovementsAnalysis.runningTotal).value()
                        var p1_3 = numeral(self.analysis.otherBuildingsAnalysis.runningTotal).value()
                        var p1_4 = (obj.price && obj.price != '') ? numeral(obj.price).value() : 0

                        //TODO: Someone please clean up this code! It appears in three different places
                        if (underMainRoofGaragesAreaInM2.value() != landAreaInSquareMeter && landAreaInSquareMeter) {
                            underMainRoofGaragesAreaInM2 = numeral(landAreaInSquareMeter)
                        }
                        if(underMainRoofGaragesAreaInM2.value() > 0){
                            var p1 = 0.65* (p1_1 - p1_2 - (p1_3 - p1_4))

                            var p2 = numeral(self.sale.TLA).value() + (0.65 * underMainRoofGaragesAreaInM2.value())
                            obj.pricePerSquareMeter = numeral(p1 / p2).format('$0,0')

                            obj.price = numeral(underMainRoofGaragesAreaInM2).multiply(numeral(obj.pricePerSquareMeter).value()).format('$0,0')
                        }
                    }
                });

                self.updateRunningTotals()

                var netSalePrice = numeral(self.sale.netPrice).value()
                var totalAnalysedLandValueNum = numeral(self.analysis.landAnalysis.runningTotal).value()
                var totalOtherImprovementsAnalysedValueNum = numeral(self.analysis.otherImprovementsAnalysis.runningTotal).value()

                var totalOtherBuildingsAnalysedValueNum = numeral(self.analysis.otherBuildingsAnalysis.runningTotal).value()

                 var mainBuildingAnalysedVal = netSalePrice - totalAnalysedLandValueNum - totalOtherImprovementsAnalysedValueNum - totalOtherBuildingsAnalysedValueNum
                 self.analysis.mainBuildingAnalysis.pricePerSquareMeter = numeral(mainBuildingAnalysedVal).divide(numeral(self.analysis.mainBuildingAnalysis.areaInSquareMeter).value()).format('$0,0')
                 self.analysis.mainBuildingAnalysis.price = numeral(mainBuildingAnalysedVal).format('$0,0')
                 self.updateRunningTotals()
                 //self.checkForWarnings()
            },

            analyseGrossRateDeductionsChanges: function(index){
                var self = this;
                var grossRateDeductionsAnalysis = self.analysis.grossRateDeductionsAnalysis
                $.each(grossRateDeductionsAnalysis, function(i, obj) {
                    if (i == index) {
                        var landArea = numeral(obj.areaInSquareMeter).value()
                        var price = numeral(obj.pricePerSquareMeter).value()
                        if (landArea && price) {
                                obj.price = numeral(Math.round(landArea * price)).format('$0,0')
                            }
                        }
                });
                self.updateRunningTotals();

            },

            analyseLandChanges: function(index){
                var self = this
                var landAnalysis = self.analysis.landAnalysis
                $.each(landAnalysis, function(i, obj) {
                    if (i == index) {
                        var landArea = numeral(obj.areaInSquareMeter).value()
                        var price = numeral(obj.pricePerSquareMeter).value()
                        if (landArea && price) {
                                obj.price = numeral(Math.round(landArea * price)).format('$0,0')
                            }
                        }
                });
                self.updateRunningTotals()

                var totalAnalysedLandValueNum = numeral(self.analysis.landAnalysis.runningTotal).value()
                self.analysis.analysedLV = numeral(self.analysis.landAnalysis.runningTotal).format('$0,0')

                var netSalePrice = numeral(self.sale.netPrice).value()

                if (totalAnalysedLandValueNum == netSalePrice) {
                    $.each(self.analysis.otherImprovementsAnalysis, function(i, obj) {
                        obj.pricePerSquareMeter = numeral(0).format('$0,0')
                        obj.price = numeral(0).format('$0,0')
                    });
                    $.each(self.analysis.otherBuildingsAnalysis, function(i, obj) {
                        obj.pricePerSquareMeter = numeral(0).format('$0,0')
                        obj.price = numeral(0).format('$0,0')
                    });
                    self.analysis.mainBuildingAnalysis.pricePerSquareMeter = numeral(0).format('$0,0')
                    self.analysis.mainBuildingAnalysis.price = numeral(0).format('$0,0')
                    self.updateRunningTotals()
                    return
                }

                self.analysis.analysedVI = numeral(netSalePrice).subtract(numeral(self.analysis.analysedLV).value()).format('$0,0')

                var landArea = numeral(self.sale.landArea).value()
                if (totalAnalysedLandValueNum > 0 && landArea > 0) {
                    var analysedLNRNum = totalAnalysedLandValueNum/(landArea * 10000)
                    self.analysis.analysedLNR = numeral(Math.round(analysedLNRNum)).format('$0,0')
                }

                var landValue = numeral(self.sale.landValue).value()
                var capitalValue = numeral(self.sale.capitalValue).value()
                var valueOfImprovements = 0
                if (landValue > 0) {
                    self.analysis.alv_lv = (Math.round((totalAnalysedLandValueNum/landValue)*100))/100
                }

                if (landValue && landValue > 0 && capitalValue && capitalValue > 0) {
                    valueOfImprovements = Math.round(capitalValue - landValue)
                }

                if (valueOfImprovements > 0) {
                    self.analysis.avi_vi = (Math.round(((netSalePrice-totalAnalysedLandValueNum)/valueOfImprovements)*100))/100
                }

                var otherImprovementsAnalysis = self.analysis.otherImprovementsAnalysis
                $.each(otherImprovementsAnalysis, function(i, obj) {
                    var landAreaInSquareMeter = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var description = obj.description
                    if (description && description.toLowerCase().indexOf('other improvement') === 0){
                        obj.price = numeral(self.analysis.analysedVI).divide(10).format('$0,0')
                        if (landAreaInSquareMeter && landAreaInSquareMeter > 0 && pricePerSquareMeter) {
                            obj.pricePerSquareMeter = numeral(obj.price).divide(landAreaInSquareMeter).format('$0,0')
                        }
                    }
                    if (description && description.toLowerCase().indexOf('large other improvement') === 0){
                        obj.price = numeral(self.analysis.analysedVI).divide(20).format('$0,0')
                        if (landAreaInSquareMeter && landAreaInSquareMeter > 0 && pricePerSquareMeter) {
                            obj.pricePerSquareMeter = numeral(obj.price).divide(landAreaInSquareMeter).format('$0,0')
                        }
                    }

                });

                self.updateRunningTotals()
                var totalOtherImprovementsAnalysedValueNum = numeral(self.analysis.otherImprovementsAnalysis.runningTotal).value()

                var underMainRoofGaragesAreaInM2 = numeral(self.sale.TFA).subtract(numeral(self.sale.TLA).value())
                var numOfFreeStandingGarages = numeral(self.sale.freeStandingGarages).value()

                var otherBuildingsAnalysis = self.analysis.otherBuildingsAnalysis

                $.each(otherBuildingsAnalysis, function(i, obj) {
                    var landAreaInSquareMeter = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var description = obj.description
                    if (description && description.toLowerCase().indexOf('garage fs') === 0){
                        obj.price = numeral(self.analysis.analysedVI).multiply(0.03*numOfFreeStandingGarages).format('$0,0')
                        if (landAreaInSquareMeter && landAreaInSquareMeter > 0) {
                            obj.pricePerSquareMeter = numeral(obj.price).divide(numeral(obj.areaInSquareMeter).value()).format('$0,0')
                        }

                    }
                });
                self.updateRunningTotals()

                $.each(otherBuildingsAnalysis, function(i, obj) {
                    var landAreaInSquareMeter = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var description = obj.description
                    if (description && description.toLowerCase().indexOf('garage umr') === 0){
                        var p1_1 = numeral(self.analysis.analysedVI).value()
                        var p1_2 = numeral(self.analysis.otherImprovementsAnalysis.runningTotal).value()
                        var p1_3 = numeral(self.analysis.otherBuildingsAnalysis.runningTotal).value()
                        var p1_4 = (obj.price && obj.price != '') ? numeral(obj.price).value() : 0

                        //TODO: Someone please clean up this code! It appears in three different places
                        if (underMainRoofGaragesAreaInM2.value() != landAreaInSquareMeter && landAreaInSquareMeter) {
                            underMainRoofGaragesAreaInM2 = numeral(landAreaInSquareMeter)
                        }
                        if(underMainRoofGaragesAreaInM2.value() > 0){
                            var p1 = 0.65* (p1_1 - p1_2 - (p1_3 - p1_4))

                            var p2 = numeral(self.sale.TLA).value() + (0.65 * underMainRoofGaragesAreaInM2.value())
                            obj.pricePerSquareMeter = numeral(p1 / p2).format('$0,0')

                            obj.price = numeral(underMainRoofGaragesAreaInM2).multiply(numeral(obj.pricePerSquareMeter).value()).format('$0,0')
                        }
                    }
                });

                self.updateRunningTotals()
                var totalOtherBuildingsAnalysedValueNum = numeral(self.analysis.otherBuildingsAnalysis.runningTotal).value()

                var mainBuildingAnalysedVal = netSalePrice - totalAnalysedLandValueNum - totalOtherImprovementsAnalysedValueNum - totalOtherBuildingsAnalysedValueNum
                self.analysis.mainBuildingAnalysis.pricePerSquareMeter = numeral(mainBuildingAnalysedVal).divide(numeral(self.analysis.mainBuildingAnalysis.areaInSquareMeter).value()).format('$0,0')
                self.analysis.mainBuildingAnalysis.price = numeral(mainBuildingAnalysedVal).format('$0,0')
                self.updateRunningTotals()
            },
            updateRunningTotals: function(){
                var self = this;
                var grossRateDeductionsAnalysis = self.analysis.grossRateDeductionsAnalysis;
                var landAnalysis = self.analysis.landAnalysis;
                var otherImprovementsAnalysis = self.analysis.otherImprovementsAnalysis;
                var otherBuildingsAnalysis = self.analysis.otherBuildingsAnalysis;
                var mainBuildingAnalysis = self.analysis.mainBuildingAnalysis;

                var grossRateDeductionsAnalysisRunningTotal = 0;
                $.each(grossRateDeductionsAnalysis, function(i, obj) {
                    grossRateDeductionsAnalysisRunningTotal += obj.price ? Number((''+obj.price).replace(/[^0-9\.-]+/g,"")) : 0;
                    obj.price = (obj.price && obj.price != '' && obj.price != '$0') ? self.formatDollarValue(obj.price) : '';
                    obj.pricePerSquareMeter = (obj.pricePerSquareMeter && obj.pricePerSquareMeter != '' && obj.pricePerSquareMeter != '$0') ? self.formatDollarValue(obj.pricePerSquareMeter) : ''
                });
                grossRateDeductionsAnalysis.runningTotal = self.formatDollarValue(-grossRateDeductionsAnalysisRunningTotal);
                self.analysis.totalAnalysedGrossRateDeductionsValueNum = grossRateDeductionsAnalysisRunningTotal;

                var landAnalysisRunningTotal = 0;
                $.each(landAnalysis, function(i, obj) {
                    landAnalysisRunningTotal += obj.price ? Number((''+obj.price).replace(/[^0-9\.-]+/g,"")) : 0;
                    obj.price = (obj.price && obj.price != '' && obj.price != '$0') ? self.formatDollarValue(obj.price) : '';
                    obj.pricePerSquareMeter = (obj.pricePerSquareMeter && obj.pricePerSquareMeter != '' && obj.pricePerSquareMeter != '$0') ? self.formatDollarValue(obj.pricePerSquareMeter) : ''
                });
                landAnalysis.runningTotal = self.formatDollarValue(landAnalysisRunningTotal);
                self.analysis.totalAnalysedLandValueNum = landAnalysisRunningTotal;

                var analysedValue= numeral(self.mainUnitAnalysedValue).value();
                var mainUnitArea= numeral(self.analysis.mainUnitArea).value();
                var grossRate= analysedValue/ mainUnitArea;
                if (!isNaN(grossRate) && mainUnitArea > 0) {
                    self.analysis.mainUnitGrossRate= Math.round(grossRate);
                }
                else{
                    self.analysis.mainUnitGrossRate= null;
                }

                var otherImprovementsAnalysisRunningTotal = 0;
                $.each(otherImprovementsAnalysis, function(i, obj) {
                    otherImprovementsAnalysisRunningTotal += obj.price ? Number((''+obj.price).replace(/[^0-9\.-]+/g,"")) : 0;
                    obj.price = (obj.price && obj.price != '' && obj.price != '$0') ? self.formatDollarValue(obj.price) : '';
                    obj.pricePerSquareMeter = (obj.pricePerSquareMeter && obj.pricePerSquareMeter != '' && obj.pricePerSquareMeter != '$0') ? self.formatDollarValue(obj.pricePerSquareMeter) : ''
                });
                otherImprovementsAnalysis.runningTotal = self.formatDollarValue(otherImprovementsAnalysisRunningTotal);
                self.analysis.totalAnalysedOtherImprovementsNum = otherImprovementsAnalysisRunningTotal;

                var otherBuildingsAnalysisRunningTotal = 0;
                $.each(otherBuildingsAnalysis, function(i, obj) {
                    otherBuildingsAnalysisRunningTotal += obj.price ? Number((''+obj.price).replace(/[^0-9\.-]+/g,"")) : 0;
                    obj.price = (obj.price && obj.price != '' && obj.price != '$0') ? self.formatDollarValue(obj.price) : '';
                    obj.pricePerSquareMeter = (obj.pricePerSquareMeter && obj.pricePerSquareMeter != '' && obj.pricePerSquareMeter != '$0') ? self.formatDollarValue(obj.pricePerSquareMeter) : ''
                });
                otherBuildingsAnalysis.runningTotal = self.formatDollarValue(otherBuildingsAnalysisRunningTotal);

                var mainBuildingAnalysisRunningTotal = Number((''+mainBuildingAnalysis.price).replace(/[^0-9\.-]+/g,""));

                var salesAnalysisTotal = landAnalysisRunningTotal+otherImprovementsAnalysisRunningTotal+otherBuildingsAnalysisRunningTotal+mainBuildingAnalysisRunningTotal;
                self.analysis.salesAnalysisTotal = self.formatDollarValue(salesAnalysisTotal);

                var netPrice = Number((''+self.sale.netPrice).replace(/[^0-9\.-]+/g,""));
                self.analysis.currentDifference = self.formatDollarValue(salesAnalysisTotal-netPrice)
            },
            generateDataForCalculation: function(){
                var self = this;
                var analysis = JSON.parse(JSON.stringify(self.analysis));
                var grossRateDeductions = JSON.parse(JSON.stringify(self.analysis.grossRateDeductionsAnalysis));
                var landAnalysis = JSON.parse(JSON.stringify(self.analysis.landAnalysis));
                var otherImprovementsAnalysis = JSON.parse(JSON.stringify(self.analysis.otherImprovementsAnalysis));
                var otherBuildingsAnalysis = JSON.parse(JSON.stringify(self.analysis.otherBuildingsAnalysis));
                var mainBuildingAnalysis = JSON.parse(JSON.stringify(self.analysis.mainBuildingAnalysis));
                var residualLandAnalysis = self.residualLandAnalysis;

                var grossRateDeductionsTotal = 0
                $.each(grossRateDeductions, function(i, obj) {
                    obj.areaInSquareMeter = (obj.areaInSquareMeter && obj.areaInSquareMeter != '') ? numeral(obj.areaInSquareMeter).value() : null;
                    obj.pricePerSquareMeter = (obj.pricePerSquareMeter && obj.pricePerSquareMeter != '') ? numeral(obj.pricePerSquareMeter).value() : null;
                    obj.price = (obj.price && obj.price != '') ? numeral(obj.price).value() : null;
                    grossRateDeductionsTotal += numeral(obj.price).value()
                });

                var landAnalysisTotal = 0
                $.each(landAnalysis, function(i, obj) {
                    obj.areaInSquareMeter = (obj.areaInSquareMeter && obj.areaInSquareMeter != '') ? numeral(obj.areaInSquareMeter).value() : null;
                    obj.pricePerSquareMeter = (obj.pricePerSquareMeter && obj.pricePerSquareMeter != '') ? numeral(obj.pricePerSquareMeter).value() : null;
                    obj.price = (obj.price && obj.price != '') ? numeral(obj.price).value() : null;
                    landAnalysisTotal += numeral(obj.price).value()
                });

                var otherImprovementsAnalysisTotal = 0
                $.each(otherImprovementsAnalysis, function(i, obj) {
                    obj.areaInSquareMeter = (obj.areaInSquareMeter && obj.areaInSquareMeter != '') ? numeral(obj.areaInSquareMeter).value() : null;
                    obj.pricePerSquareMeter = (obj.pricePerSquareMeter && obj.pricePerSquareMeter != '') ? numeral(obj.pricePerSquareMeter).value() : null;
                    obj.price =  (obj.price && obj.price != '') ? numeral(obj.price).value() : null;
                    otherImprovementsAnalysisTotal += numeral(obj.price).value()
                });
                $.each(otherBuildingsAnalysis, function(i, obj) {
                    obj.areaInSquareMeter = (obj.areaInSquareMeter && obj.areaInSquareMeter != '') ? numeral(obj.areaInSquareMeter).value() : null;
                    obj.pricePerSquareMeter = (obj.pricePerSquareMeter && obj.pricePerSquareMeter != '') ? numeral(obj.pricePerSquareMeter).value() : null;
                    obj.price = (obj.price && obj.price != '') ? numeral(obj.price).value() : null;
                });
                mainBuildingAnalysis.areaInSquareMeter = (mainBuildingAnalysis.areaInSquareMeter && mainBuildingAnalysis.areaInSquareMeter != '') ? numeral(mainBuildingAnalysis.areaInSquareMeter).value() : null;
                mainBuildingAnalysis.pricePerSquareMeter = (mainBuildingAnalysis.pricePerSquareMeter && mainBuildingAnalysis.pricePerSquareMeter != '') ? numeral(mainBuildingAnalysis.pricePerSquareMeter).value() : null;
                mainBuildingAnalysis.price = (mainBuildingAnalysis.price && mainBuildingAnalysis.price != '') ? numeral(mainBuildingAnalysis.price).value() : null;

                var analysisDataToSend = {};
                analysisDataToSend.id = self.sale.id;
                analysisDataToSend.saleId = self.saleData.saleId;
                analysisDataToSend.analysisDate = moment().format('YYYY-MM-DD');
                analysisDataToSend.analysisKind = 'Verified';
                analysisDataToSend.analysedBy = self.userFullName;
                analysisDataToSend.analysedDeductions = grossRateDeductions;
                analysisDataToSend.totalAnalysedDeductions = grossRateDeductionsTotal;
                analysisDataToSend.analysedMainUnitGrossRate= self.analysis.mainUnitGrossRate;
                analysisDataToSend.analysedMainUnitArea = analysis.mainUnitArea;
                analysisDataToSend.analysedMainUnitDescription = analysis.mainUnitDescription;
                analysisDataToSend.analysedLand = landAnalysis;
                analysisDataToSend.totalAnalysedLandValue = landAnalysisTotal;
                analysisDataToSend.analysedOtherImprovements = otherImprovementsAnalysis;
                analysisDataToSend.totalAnalysedOtherImprovements = otherImprovementsAnalysisTotal;
                analysisDataToSend.analysedOtherBuildings = otherBuildingsAnalysis;
                analysisDataToSend.analysedMainBuilding = mainBuildingAnalysis;
                analysisDataToSend.housePriceIndex = analysis.housePriceIndex;
                analysisDataToSend.sale = analysis.sale;
                analysisDataToSend.saleComment = analysis.comment;
                analysisDataToSend.propertySummary = self.summary;
                analysisDataToSend.analysedAnnualRent = numeral(analysis.analysedAnnualRent).value();
                analysisDataToSend.isBenchmark = analysis.isBenchmark;

                analysisDataToSend.residualMainBuildingAnalysis = residualLandAnalysis.mainBuildingAnalysis;
                analysisDataToSend.residualLandAnalysisPriceArea = residualLandAnalysis.landAnalysis;
                analysisDataToSend.residualOtherBuildingsAnalysis = residualLandAnalysis.otherBuildingsAnalysis;
                analysisDataToSend.residualOtherBuildingsValueTotal = residualLandAnalysis.otherBuildingsValueTotal;
                analysisDataToSend.residualOtherImprovementsAnalysis = residualLandAnalysis.otherImprovementsAnalysis;
                analysisDataToSend.residualOtherImprovementsValueTotal = residualLandAnalysis.otherImprovementsValueTotal;

                return analysisDataToSend
            },
            generateSalesAnalysisData: function(response){
                var self = this;
                self.sale = self.generateSaleFromResponse(response);
                self.summary = self.generateSummaryFromResponse(response);
                self.analysis = self.generateAnalysisFromResponse(response);
                self.revision = self.generateRevisionFromResponse(response);
                self.residualLandAnalysis = self.generateResidualLandAnalaysisFromResponse(response);

            },
            checkForWarnings: function(){
                var self = this

                var landAnalysis = self.analysis.landAnalysis
                var otherImprovementsAnalysis = self.analysis.otherImprovementsAnalysis
                var otherBuildingsAnalysis = self.analysis.otherBuildingsAnalysis
                var mainBuildingAnalysis = self.analysis.mainBuildingAnalysis

                var isAnalysedFieldNegative = false
                var isSectionTotalNegative = false

                $.each(landAnalysis, function(i, obj) {
                    var area = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var price = numeral(obj.price).value()
                    if (area < 0 || pricePerSquareMeter < 0 || price < 0) {
                        isAnalysedFieldNegative = true
                    }
                });
                $.each(otherImprovementsAnalysis, function(i, obj) {
                    var area = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var price = numeral(obj.price).value()
                    if (area < 0 || pricePerSquareMeter < 0 || price < 0) {
                        isAnalysedFieldNegative = true
                    }
                });
                $.each(otherBuildingsAnalysis, function(i, obj) {
                    var area = numeral(obj.areaInSquareMeter).value()
                    var pricePerSquareMeter = numeral(obj.pricePerSquareMeter).value()
                    var price = numeral(obj.price).value()
                    if (area < 0 || pricePerSquareMeter < 0 || price < 0) {
                        isAnalysedFieldNegative = true
                    }
                });
                if (numeral(mainBuildingAnalysis.areaInSquareMeter).value() < 0 || numeral(mainBuildingAnalysis.pricePerSquareMeter).value() < 0 || numeral(mainBuildingAnalysis.price).value() < 0) {
                    isAnalysedFieldNegative = true
                }
                if (isAnalysedFieldNegative) {
                    alert('No field (Area, Rate /m² or Analysed Value) can contain a negative value.')
                    return false
                }

                var landAnalysisTotal = numeral(landAnalysis.runningTotal).value()
                var otherImprovementsAnalysisTotal = numeral(otherImprovementsAnalysis.runningTotal).value()
                var otherBuildingsAnalysisTotal = numeral(otherBuildingsAnalysis.runningTotal).value()
                var mainBuildingAnalysisTotal = numeral(mainBuildingAnalysis.price).value()

                if (landAnalysisTotal < 0 || otherImprovementsAnalysisTotal < 0 || otherBuildingsAnalysisTotal < 0 || mainBuildingAnalysisTotal < 0) {
                    alert('No field (Area, Rate /m² or Analysed Value) can contain a negative value.')
                    return false
                }

                var netSalePrice = numeral(self.sale.netPrice).value()

                if (landAnalysisTotal > netSalePrice || otherImprovementsAnalysisTotal > netSalePrice || otherBuildingsAnalysisTotal > netSalePrice || mainBuildingAnalysisTotal > netSalePrice) {
                    alert('No Section total (Land, Other Improvements, Other Buildings, Main Buildings) can be higher than the Net Sale Price.')
                    return false
                }

                return true
            },
            generateSaleFromResponse: function(response){
                var self = this;
                var saleObjToRender = {};
                var currSale = response.sale
                saleObjToRender.id = currSale.id;
                saleObjToRender.saleDate = self.getFormattedDate(currSale.saleDate);
                saleObjToRender.qivsSaleId = currSale.qivsSaleId;
                saleObjToRender.parties = currSale.parties;
                saleObjToRender.grossPrice = currSale.price ? self.formatDollarValue(currSale.price.gross) : '';
                saleObjToRender.netPrice = currSale.price ? self.formatDollarValue(currSale.price.net) : '';
                saleObjToRender.chattels = currSale.price ? self.formatDollarValue(currSale.price.chattels) : '';
                saleObjToRender.other = currSale.price ? self.formatDollarValue(currSale.price.other) : '';
                saleObjToRender.gst = currSale.price ? self.formatDollarValue(currSale.price.gst) : '';
                saleObjToRender.currentRevisionDate = self.getFormattedDate(currSale.revisionDate);
                saleObjToRender.classification = currSale.classifications ? (currSale.classifications.saleType ? currSale.classifications.saleType.code : '') : '';
                saleObjToRender.classification += currSale.classifications ? (currSale.classifications.saleTenure ? currSale.classifications.saleTenure.code : '') : '';
                saleObjToRender.classification += currSale.classifications ? (currSale.classifications.priceValueRelationship ? currSale.classifications.priceValueRelationship.code : '') : '';
                saleObjToRender.priceValueRelationship = currSale.classifications ? (currSale.classifications.priceValueRelationship ? currSale.classifications.priceValueRelationship.code : '') : '';
                saleObjToRender.status = currSale.status ? currSale.status.description : '';
                saleObjToRender.qupid = currSale.primaryProperty ? currSale.primaryProperty.qupid : '';
                saleObjToRender.propertyId = response.propertyID ? response.propertyID : '';
                saleObjToRender.propertyUnits = response.units ? response.units : '';
                saleObjToRender.valRef = currSale.primaryProperty ? currSale.primaryProperty.rollNumber : '';
                saleObjToRender.valRef += currSale.primaryProperty ? '/'+currSale.primaryProperty.assessmentNumber : '';
                saleObjToRender.valRef += currSale.primaryProperty ? ' ' + (currSale.primaryProperty.suffix ? currSale.primaryProperty.suffix : '') : '';
                if (saleObjToRender.valRef.indexOf('undefined') !== -1) {
                    saleObjToRender.valRef = ''
                }
                saleObjToRender.legalDescription = currSale.primaryProperty ? currSale.primaryProperty.legalDescription : '';
                saleObjToRender.address1 = self.generateAddress1(currSale.primaryProperty.address);
                saleObjToRender.address2 = self.generateAddress2(currSale.primaryProperty.address, currSale.primaryProperty.territorialAuthority);
                saleObjToRender.isMaoriLand = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.isMaoriLand ? 'Yes' : 'No') : 'No') : 'No';
                saleObjToRender.category = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.category ? currSale.primaryProperty.category.code : '') : '');
                saleObjToRender.landValue = self.formatDollarValue(currSale.landValue);
                saleObjToRender.capitalValue = self.formatDollarValue(currSale.capitalValue);
                saleObjToRender.landArea = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.landArea : '') : '';
                if (typeof saleObjToRender.landArea == 'number') {
                    saleObjToRender.landArea = saleObjToRender.landArea.toFixed(4)
                }
                saleObjToRender.TLA = currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.totalLivingArea ? currSale.primaryProperty.massAppraisalData.totalLivingArea : '0') : '0') : '0';
                saleObjToRender.TFA = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.totalFloorArea ? currSale.primaryProperty.landUseData.totalFloorArea : '0') : '0') : '0';
                saleObjToRender.effectiveYearBuilt = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.effectiveYearBuilt : '') : '');
                saleObjToRender.landUse = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.landUse ? currSale.primaryProperty.landUseData.landUse.description : '') : '') : '');
                saleObjToRender.units = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.units : '') : '');
                saleObjToRender.bedrooms = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.bedrooms : '') : '');
                saleObjToRender.toilets = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.toilets : '') : '');
                saleObjToRender.wallConstructionAndCondition = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.wallConstruction ? currSale.primaryProperty.landUseData.wallConstruction.description : '') : '') : ''
                saleObjToRender.wallConstructionAndCondition += currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.wallCondition ? ' '+currSale.primaryProperty.landUseData.wallCondition.description : '') : '') : ''
                saleObjToRender.wallConstructionAndCondition = self.getRenderableValue(saleObjToRender.wallConstructionAndCondition);
                saleObjToRender.roofConstructionAndCondition = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.roofConstruction ? currSale.primaryProperty.landUseData.roofConstruction.description : '') : '') : ''
                saleObjToRender.roofConstructionAndCondition += currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? (currSale.primaryProperty.landUseData.roofCondition ? ' '+currSale.primaryProperty.landUseData.roofCondition.description : '') : '') : ''
                saleObjToRender.roofConstructionAndCondition = self.getRenderableValue(saleObjToRender.roofConstructionAndCondition);
                saleObjToRender.underMainRoofGarages = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.underMainRoofGarages : '') : '');
                saleObjToRender.freeStandingGarages = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.freestandingGarages : '') : '');
                saleObjToRender.otherLargeImprovements = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.hasLargeOtherImprovements : '') : '');
                saleObjToRender.modernisation = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.isModernised : '') : '');
                saleObjToRender.zone = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.landZone : '') : '');
                saleObjToRender.lotPosition = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.lotPosition ? currSale.primaryProperty.massAppraisalData.classifications.lotPosition.description : '') : '') : '') : '')
                saleObjToRender.contour = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.contour ? currSale.primaryProperty.massAppraisalData.classifications.contour.description : '') : '') : '') : '');
                saleObjToRender.viewScope = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? (currSale.primaryProperty.massAppraisalData.classifications ? (currSale.primaryProperty.massAppraisalData.classifications.viewScope ? currSale.primaryProperty.massAppraisalData.classifications.viewScope.description : '') : '') : '') : '');
                saleObjToRender.production = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.production : '-') : '-');

                //Derived Values
                var valueOfImprovements = 0;
                var landValue = currSale.landValue;
                var capitalValue = currSale.capitalValue;
                var totalFloorArea = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.totalFloorArea : 0) : 0;
                var totalLivingArea = currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.totalLivingArea : 0) : 0;
                var landArea = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.landArea : 0) : 0;
                var netPrice = currSale.price ? currSale.price.net : 0;

                if (landValue && landValue > 0 && capitalValue && capitalValue > 0) {
                    valueOfImprovements = Math.round(capitalValue - landValue)
                }
                saleObjToRender.valueOfImprovements = self.formatDollarValue(valueOfImprovements);

                var cvNetRate = 0;
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0) {
                    cvNetRate = Math.round(capitalValue/totalFloorArea)
                }
                saleObjToRender.cvNetRate = self.formatDollarValue(cvNetRate);

                var lvNetRate = 0;
                if (landArea > 0 && landValue && landValue > 0) {
                    lvNetRate = Math.round(landValue/(landArea*10000))
                }
                saleObjToRender.lvNetRate = self.formatDollarValue(lvNetRate);

                var viNetRate = 0;
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0 && landValue && landValue > 0) {
                    viNetRate = Math.round((capitalValue - landValue)/totalFloorArea)
                }
                saleObjToRender.viNetRate = self.formatDollarValue(viNetRate);

                var nspCV = 0;
                if (capitalValue && capitalValue > 0) {
                    nspCV = (Math.round((netPrice/capitalValue)*100))/100
                }
                saleObjToRender.nspCV = nspCV;

                var saleNetRate = 0;
                if (netPrice > 0 && totalFloorArea > 0) {
                    saleNetRate = Math.round(netPrice/totalFloorArea)
                }
                saleObjToRender.saleNetRate = self.formatDollarValue(saleNetRate);
                saleObjToRender.nsp_tfa = saleObjToRender.saleNetRate;

                var nsp_tla = 0;
                if (netPrice > 0 && totalLivingArea > 0) {
                    nsp_tla = Math.round(netPrice/totalLivingArea)
                }
                saleObjToRender.nsp_tla = self.formatDollarValue(nsp_tla);

                return saleObjToRender
            },
            generateAnalysisFromResponse: function(response){
                var self = this;
                var valueOfImprovements = 0;
                var currSale = response.sale;
                var landValue = currSale.landValue;
                var capitalValue = currSale.capitalValue;
                var totalFloorArea = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.totalFloorArea : 0) : 0;
                var totalLivingArea = currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.totalLivingArea : 0) : 0;
                var landArea = currSale.primaryProperty ? (currSale.primaryProperty.landUseData ? currSale.primaryProperty.landUseData.landArea : 0) : 0;
                var netPrice = currSale.price ? currSale.price.net : 0;
                var grossPrice = currSale.price ? currSale.price.gross : 0;

                if (landValue && landValue > 0 && capitalValue && capitalValue > 0) {
                    valueOfImprovements = Math.round(capitalValue - landValue)
                }
                var analysisObjToRender = {};

                analysisObjToRender.sale = response.sale;
                analysisObjToRender.housePriceIndex = self.formatDecimal(response.housePriceIndex, 2);
                analysisObjToRender.landValueIndex = self.formatDecimal(response.landValueIndex ? response.landValueIndex : 1, 2);

                analysisObjToRender.analysisDate = response.analysisDate ? self.getFormattedDate(response.analysisDate) : ''
                analysisObjToRender.analysedBy = response.analysedBy
                analysisObjToRender.analysedLV = self.formatDollarValue(response.totalAnalysedLandValue)

                analysisObjToRender.totalAnalysedGrossRateDeductionsValueNum = response.totalAnalysedGrossRateDeductionsValueNum ? response.totalAnalysedGrossRateDeductionsValueNum : 0;
                var totalAnalysedLandValueNum = response.totalAnalysedLandValue
                analysisObjToRender.totalAnalysedLandValueNum = totalAnalysedLandValueNum;
                var nspNum = netPrice
                analysisObjToRender.totalAnalysedOtherImprovementsNum = response.totalAnalysedOtherImprovements;
                analysisObjToRender.analysedVI = self.formatDollarValue(nspNum - totalAnalysedLandValueNum);

                if (landValue > 0) {
                    analysisObjToRender.alv_lv = self.formatDecimal(((Math.round((totalAnalysedLandValueNum/landValue)*100))/100), 2);
                }

                if (valueOfImprovements > 0) {
                    analysisObjToRender.avi_vi = self.formatDecimal(((Math.round(((nspNum-totalAnalysedLandValueNum)/valueOfImprovements)*100))/100), 2);
                }


                var analysedOtherImprovements = response.analysedOtherImprovements;
                if (totalAnalysedLandValueNum > 0 && landArea > 0) {
                    var analysedLNRNum = totalAnalysedLandValueNum/(landArea * 10000)
                    analysisObjToRender.analysedLNR = self.formatDollarValue(Math.round(analysedLNRNum))
                }

                analysisObjToRender.comment = response.saleComment;
                analysisObjToRender.remarks = currSale.remarks;
                analysisObjToRender.analysisKind = response.analysisKind;

                var mainBuilding = {};
                mainBuilding.description = response.analysedMainBuilding ? response.analysedMainBuilding.description : ''
                mainBuilding.areaInSquareMeter = response.analysedMainBuilding ? (response.analysedMainBuilding.areaInSquareMeter ? Math.round(response.analysedMainBuilding.areaInSquareMeter) : '') : ''
                mainBuilding.pricePerSquareMeter = response.analysedMainBuilding ? (response.analysedMainBuilding.pricePerSquareMeter ? self.formatDollarValue(Math.round(response.analysedMainBuilding.pricePerSquareMeter)) : '$0') : '$0'
                mainBuilding.price = response.analysedMainBuilding ? (response.analysedMainBuilding.price ? self.formatDollarValue(Math.round(response.analysedMainBuilding.price)) : '$0') : '$0'

                var salesAnalysisTotal = response.analysedMainBuilding ? (response.analysedMainBuilding.price ? Math.round(response.analysedMainBuilding.price) : 0) : 0

                var grossRateDeductionsAnalysis = [];
                var grossRateDeductionsAnalysisRunningTotal = 0;
                if (response && response.analysedDeductions && response.analysedDeductions.length > 0) {
                    var analysisFromResp = response.analysedDeductions;
                    $.each(analysisFromResp, function(i, obj) {
                        var la = {};
                        la.description = obj.description;
                        la.areaInSquareMeter = obj.areaInSquareMeter ? Math.round(obj.areaInSquareMeter) : '';
                        la.pricePerSquareMeter = obj.pricePerSquareMeter ? self.formatDollarValue(Math.round(obj.pricePerSquareMeter)) : '';
                        var price = obj.price ? Math.round(obj.price) : 0;
                        la.price = obj.price ? self.formatDollarValue(price) : '';
                        grossRateDeductionsAnalysisRunningTotal += price;
                        grossRateDeductionsAnalysis.push(la);
                    });
                }
                else {
                    var carparkRow= self.getNewAnalysisRowObj()
                    carparkRow.description='Carpark';
                    grossRateDeductionsAnalysis.push(carparkRow);
                    var balconyRow= self.getNewAnalysisRowObj()
                    balconyRow.description='Balcony';
                    grossRateDeductionsAnalysis.push(balconyRow);
                }
                analysisObjToRender.grossRateDeductionsAnalysis = grossRateDeductionsAnalysis;
                salesAnalysisTotal -= grossRateDeductionsAnalysisRunningTotal;
                analysisObjToRender.grossRateDeductionsAnalysis.runningTotal = self.formatDollarValue(-grossRateDeductionsAnalysisRunningTotal);

                analysisObjToRender.mainUnitDescription= response.analysedMainUnitDescription ? response.analysedMainUnitDescription : 'Unit';
                analysisObjToRender.mainUnitArea= response.analysedMainUnitArea ? response.analysedMainUnitArea : self.sale.TFA;
                analysisObjToRender.mainUnitGrossRate= response.analysedMainUnitGrossRate;


                var landAnalysis = [];
                var landAnalysisRunningTotal = 0;
                if (response && response.analysedLand && response.analysedLand.length > 0) {
                    var analysisFromResp = response.analysedLand;
                    $.each(analysisFromResp, function(i, obj) {
                        var la = {};
                        la.description = obj.description;
                        la.areaInSquareMeter = obj.areaInSquareMeter ? Math.round(obj.areaInSquareMeter) : '';
                        la.pricePerSquareMeter = obj.pricePerSquareMeter ? self.formatDollarValue(Math.round(obj.pricePerSquareMeter)) : '';
                        var price = obj.price ? Math.round(obj.price) : 0;
                        la.price = obj.price ? self.formatDollarValue(price) : '';
                        landAnalysisRunningTotal += price;
                        landAnalysis.push(la)
                    });
                }
                else {
                    landAnalysis.push(self.getNewAnalysisRowObj())
                }
                analysisObjToRender.landAnalysis = landAnalysis;
                salesAnalysisTotal += landAnalysisRunningTotal;
                analysisObjToRender.landAnalysis.runningTotal = self.formatDollarValue(landAnalysisRunningTotal);

                var otherImprovementsAnalysis = [];
                var otherImprovementsRunningTotal = 0;
                if (response && response.analysedOtherImprovements && response.analysedOtherImprovements.length > 0) {
                    var analysisFromResp = response.analysedOtherImprovements;
                    $.each(analysisFromResp, function(i, obj) {
                        var oia = {};
                        oia.description = obj.description;
                        if (oia.description && (oia.description.toLowerCase().indexOf('other improvement') === 0 || oia.description.toLowerCase().indexOf('large other improvement') === 0)){
                            oia.calculated = true;
                        }
                        oia.areaInSquareMeter = obj.areaInSquareMeter ? Math.round(obj.areaInSquareMeter) : '';
                        oia.pricePerSquareMeter = obj.pricePerSquareMeter ? self.formatDollarValue(Math.round(obj.pricePerSquareMeter)) : '';
                        var price = obj.price ? Math.round(obj.price) : 0;
                        oia.price = obj.price ? self.formatDollarValue(price) : '';
                        otherImprovementsRunningTotal += price;
                        otherImprovementsAnalysis.push(oia)
                    });
                }
                else {
                    otherImprovementsAnalysis.push(self.getNewAnalysisRowObj())
                }
                analysisObjToRender.otherImprovementsAnalysis = otherImprovementsAnalysis;
                salesAnalysisTotal += otherImprovementsRunningTotal;
                analysisObjToRender.otherImprovementsAnalysis.runningTotal = self.formatDollarValue(otherImprovementsRunningTotal);

                var otherBuildingsAnalysis = [];
                var otherBuildingsAnalysisRunningTotal = 0;
                if (response && response.analysedOtherBuildings && response.analysedOtherBuildings.length > 0) {
                    var analysisFromResp = response.analysedOtherBuildings
                    $.each(analysisFromResp, function(i, obj) {
                        var oba = {};
                        oba.description = obj.description;
                        if (oba.description && (oba.description.toLowerCase().indexOf('garage umr') === 0 || oba.description.toLowerCase().indexOf('garage fs') === 0)){
                            oba.calculated = true;
                        }
                        oba.areaInSquareMeter = obj.areaInSquareMeter ? Math.round(obj.areaInSquareMeter) : '';
                        oba.pricePerSquareMeter = obj.pricePerSquareMeter ? self.formatDollarValue(Math.round(obj.pricePerSquareMeter)) : '';
                        var price = obj.price ? Math.round(obj.price) : 0;
                        oba.price = obj.price ? self.formatDollarValue(price) : '';
                        otherBuildingsAnalysisRunningTotal += price;
                        otherBuildingsAnalysis.push(oba)
                    });
                }
                else {
                    otherBuildingsAnalysis.push(self.getNewAnalysisRowObj());
                }
                analysisObjToRender.otherBuildingsAnalysis = otherBuildingsAnalysis;
                salesAnalysisTotal += otherBuildingsAnalysisRunningTotal;
                analysisObjToRender.otherBuildingsAnalysis.runningTotal = self.formatDollarValue(otherBuildingsAnalysisRunningTotal);

                analysisObjToRender.mainBuildingAnalysis = mainBuilding;

                analysisObjToRender.salesAnalysisTotal = self.formatDollarValue(salesAnalysisTotal);
                analysisObjToRender.currentDifference = self.formatDollarValue(salesAnalysisTotal - netPrice);

                analysisObjToRender.analysedAnnualRent = response.analysedAnnualRent ? self.formatPrice(response.analysedAnnualRent,'$0,0') : '';
                analysisObjToRender.analysedWeeklyRent = response.analysedAnnualRent ? self.formatPrice((response.analysedAnnualRent / 52),'$0,0') : '';
                analysisObjToRender.netSalePriceGrossYield = (response.analysedAnnualRent && netPrice) ? ((response.analysedAnnualRent / netPrice) * 100) : 0;
                analysisObjToRender.grossSalePriceGrossYield = (response.analysedAnnualRent && grossPrice) ? ((response.analysedAnnualRent / grossPrice ) * 100) : 0;
                analysisObjToRender.isBenchmark = response.isBenchmark;
                return analysisObjToRender
            },

            generateResidualLandAnalaysisFromResponse: function(response) {
                const analysis = response;
                const residualLandAnalysis = {};

                if (analysis && analysis.residualMainBuildingAnalysis) {
                    residualLandAnalysis.mainBuildingAnalysis = analysis.residualMainBuildingAnalysis;
                } else {
                    residualLandAnalysis.mainBuildingAnalysis = this.getNewAnalysisRowObj();
                }

                var otherBuildingsAnalysis = [];
                var otherBuildingsAnalysisRunningTotal = 0;
                if (analysis && analysis.residualOtherBuildingsAnalysis && analysis.residualOtherBuildingsAnalysis.length > 0) {
                    otherBuildingsAnalysis = analysis.residualOtherBuildingsAnalysis;

                    otherBuildingsAnalysis.forEach((buildingAnalysis) => {
                        const description = buildingAnalysis.description.toLowerCase();

                        if (description === 'garage fs' || description === 'garage umr') {
                            buildingAnalysis.calculated = true;
                        }
                    });
                }
                else {
                    otherBuildingsAnalysis.push(this.getNewAnalysisRowObj());
                }

                residualLandAnalysis.otherBuildingsAnalysis = otherBuildingsAnalysis;
                residualLandAnalysis.otherBuildingsAnalysisRunningTotal = otherBuildingsAnalysisRunningTotal;

                var otherImprovementsAnalysis = [];
                var otherImprovementsRunningTotal = 0;
                if (analysis && analysis.residualOtherImprovementsAnalysis && analysis.residualOtherImprovementsAnalysis.length > 0) {
                    otherImprovementsAnalysis = analysis.residualOtherImprovementsAnalysis;

                    otherImprovementsAnalysis.forEach((improvementAnalysis) => {
                        const description = improvementAnalysis.description.toLowerCase();

                        if (description === 'other improvements' || description === 'large other improvements') {
                            improvementAnalysis.calculated = true;
                        }
                    });
                }
                else {
                    otherImprovementsAnalysis.push(this.getNewAnalysisRowObj());
                }

                residualLandAnalysis.otherImprovementsAnalysis = otherImprovementsAnalysis;
                residualLandAnalysis.otherImprovementsRunningTotal = otherImprovementsRunningTotal;

                if (analysis && analysis.residualLandAnalysisPriceArea) {
                    residualLandAnalysis.landAnalysis = analysis.residualLandAnalysisPriceArea;
                } else {
                    residualLandAnalysis.landAnalysis = this.getNewAnalysisRowObj();
                }

                residualLandAnalysis.analysis = analysis;
                return residualLandAnalysis;
            },
            generateRevisionFromResponse: function(response){
                var self = this;
                var revisionAnalysis = {};
                var valueOfImprovements = 0;
                var currSale = response;
                var landValue = currSale.landValue;
                var capitalValue = currSale.capitalValue;
                var totalFloorArea = currSale.totalFloorArea
                var totalLivingArea = currSale.totalLivingArea
                var landArea = currSale.landArea
                var netPrice = currSale.netPrice
                if (landValue && landValue > 0 && capitalValue && capitalValue > 0) {
                    valueOfImprovements = Math.round(capitalValue - landValue)
                }
                var nspNum = currSale.netPrice;
                var totalAnalysedLandValueNum = response.totalAnalysedLandValue
                var revisionInfo = response;
                revisionAnalysis.revisionCapVal = self.formatDollarValue(revisionInfo.revisionCapitalValue);
                revisionAnalysis.revisionLandVal = self.formatDollarValue(revisionInfo.revisionLandValue);
                revisionAnalysis.hasRevision = revisionInfo.revisionCapitalValue || revisionInfo.revisionLandValue;
                revisionAnalysis.predictedWeeklyMarketRent = revisionInfo.predictedWeeklyMarketRent ? revisionInfo.predictedWeeklyMarketRent : 0;
                revisionAnalysis.marketRentBasis = revisionInfo.marketRentBasis ? revisionInfo.marketRentBasis : '';

                var revalCapVal = revisionInfo.revisionCapitalValue ? revisionInfo.revisionCapitalValue : 0;
                var revalLandVal = revisionInfo.revisionLandValue ? revisionInfo.revisionLandValue : 0;
                var revalValueOfImprovements = 0;
                if (revalCapVal > 0 && revalLandVal > 0) {
                    revalValueOfImprovements = Math.round(revalCapVal - revalLandVal)
                }
                revisionAnalysis.revalValueOfImprovements = self.formatDollarValue(revalValueOfImprovements);

                var revalCapValNetRate = 0;
                if (revalCapVal > 0 && totalFloorArea > 0) {
                    revalCapValNetRate = Math.round(revalCapVal/totalFloorArea)
                }
                revisionAnalysis.grossRate = self.formatDollarValue(revalCapValNetRate);

                var revalLandValNetRate = 0;
                if (revalLandVal > 0 && landArea > 0) {
                    revalLandValNetRate = Math.round(revalLandVal/(landArea*10000))
                }
                revisionAnalysis.landRate = self.formatDollarValue(revalLandValNetRate);


                currSale = currSale.sale
                var freeStandingGarages = currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.freestandingGarages : 0) : 0;
                var underMainRoofGarages = currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.underMainRoofGarages : 0) : 0;
                var otherLargeImprovements_1 = self.getRenderableValue(currSale.primaryProperty ? (currSale.primaryProperty.massAppraisalData ? currSale.primaryProperty.massAppraisalData.hasLargeOtherImprovements : '') : '');

                var otherLargeImprovements = currSale.primaryProperty ? currSale.primaryProperty.massAppraisalData.hasLargeOtherImprovements : 0

                var revalNetRate = 0;
                var otherLargeImp = (otherLargeImprovements ? (0.05*revalValueOfImprovements) : 0); //0.05, (0.05*revalValueOfImprovements) it should be there only if LOI = yes

                var revalNetRateP1 = revalValueOfImprovements - (0.1*revalValueOfImprovements) - otherLargeImp;
                var revalNetRateP2 = 0;

                if(underMainRoofGarages > 0){
                    revalNetRateP2 = (
                                        0.65 * ((revalNetRateP1 - (revalValueOfImprovements*0.03*freeStandingGarages)) / (totalLivingArea + (0.65*(totalFloorArea-totalLivingArea))))
                                     ) * (totalFloorArea-totalLivingArea); // (revalValueOfImprovements*0.03*2) it should not be hardcoded to 2, it should be the number of free standing garges
                }
                var revalNetRateP3 = 0;

                if(freeStandingGarages > 0){
                    revalNetRateP3 = revalValueOfImprovements*0.03*freeStandingGarages; // (revalValueOfImprovements*0.03*freeStandingGarages) it should not be hardcoded to 2, it should be the number of free standing garges
                }

                revisionAnalysis.netRate = self.formatDollarValue(Math.round((revalNetRateP1 - revalNetRateP2 - revalNetRateP3)/totalLivingArea)); // it should be divided by TLA , it is a rate per Sqm
                var revalrcvtonsp = 0;
                if (revalCapVal > 0) {
                    revalrcvtonsp = (Math.round((revalCapVal/netPrice)*100))/100
                }
                revisionAnalysis.revalrcvtonsp = revalrcvtonsp;

                var revalrlvtoalv = 0;
                if (revalLandVal > 0 && response && response.totalAnalysedLandValue && response.totalAnalysedLandValue > 0) {
                    var analysedLandValue = response.totalAnalysedLandValue;
                    revalrlvtoalv = (Math.round((revalLandVal/analysedLandValue)*100))/100
                }
                revisionAnalysis.revalrlvtoalv = revalrlvtoalv;

                var revalrvitoavi = 0;
                if (nspNum > 0 && totalAnalysedLandValueNum > 0) {
                    var rvi = revalCapVal - revalLandVal;
                    var avi = nspNum - totalAnalysedLandValueNum;
                    if (avi > 0) {
                        revalrvitoavi = (Math.round((rvi/avi)*100))/100
                    }

                }
                revisionAnalysis.revalrvitoavi = revalrvitoavi;

                var revalrcvtocv = 0;
                if (revalCapVal > 0 && capitalValue > 0) {
                    revalrcvtocv = (Math.round((revalCapVal/capitalValue)*100))/100
                }
                revisionAnalysis.revalrcvtocv = revalrcvtocv;

                var revalrlvtolv = 0;
                if (revalLandVal > 0 && landValue > 0) {
                    revalrlvtolv = (Math.round((revalLandVal/landValue)*100))/100
                }
                revisionAnalysis.revalrlvtolv = revalrlvtolv;

                var revalrvitovi = 0;
                if (revalValueOfImprovements > 0 && valueOfImprovements > 0) {
                    revalrvitovi = (Math.round((revalValueOfImprovements/valueOfImprovements)*100))/100
                }
                revisionAnalysis.revalrvitovi = revalrvitovi;

                revisionAnalysis.effectiveDate = revisionInfo ? self.getFormattedDate(revisionInfo.marketEstimateDate) : '';
                return revisionAnalysis
            },
            generateSummaryFromResponse: function(response){
                return response.summaryText
            },
            getNewAnalysisRowObj: function(){
                var analysisRowObj = {};
                analysisRowObj.description = '';
                analysisRowObj.areaInSquareMeter = '';
                analysisRowObj.pricePerSquareMeter = '';
                analysisRowObj.price='';
                return analysisRowObj
            },
            formatDollarValue: function(val) {
                var self = this;
                val = val ? (''+Number((''+val).replace(/[^0-9\.-]+/g,""))) : '';
                val = val ? self.formatPrice(val, '$0,0') : '$0';
                return val;
            },
            getFormattedDate: function(date) {
                if (date && date != '') {
                    return moment(String(date)).format('DD/MM/YYYY')
                }
                return ''
            },
            getRenderableValue: function(val) {
                return (val && val != '') ? val : '-'
            },
            scrollToEnd: function(id){
                    this.$nextTick(() => {
                        let container= $("#"+ id);
                        if( container.length){
                                container.get(0).scrollIntoView(true);
                        }
                    });


            }
        },
        async mounted() {
            if (!this.saleData) {
                console.error('No sale data found')
            }

            let salesAnalysis;
            try {
                salesAnalysis = await displaySalesAnalysis(this.saleData.saleId);
                this.generateSalesAnalysisData(salesAnalysis);
                this.originalResponse = JSON.stringify(salesAnalysis);
                this.updateRunningTotals();
            } catch (error) {
                this.errorHandler(salesAnalysis);
                console.error('Something went wrong while fetching sales analysis data', error);
            }
        },
        updated: function() {
            const self = this;
            $('.addGrossRateDeductionsRow').off("click").click(function(evt){
                var index = parseInt($(this).attr('data-index'));
                var newLandRowObj = self.getNewAnalysisRowObj();
                self.analysis.grossRateDeductionsAnalysis.splice(index+1, 0, newLandRowObj)
            });
            $('.removeGrossRateDeductionsRow').off("click").click(function(evt){
                var index = $(this).attr('data-index');
                var currentRowObj = self.analysis.grossRateDeductionsAnalysis[index]
                self.analysis.grossRateDeductionsAnalysis.splice(index, 1);
                if (currentRowObj.price && currentRowObj.price != '') {
                    self.analyseLandChanges(-1)
                }
            });
            $('.addLandRow').off("click").click(function(evt){
                var index = parseInt($(this).attr('data-index'));
                var newLandRowObj = self.getNewAnalysisRowObj();
                self.analysis.landAnalysis.splice(index+1, 0, newLandRowObj)
            });
            $('.removeLandRow').off("click").click(function(evt){
                var index = $(this).attr('data-index');
                var currentRowObj = self.analysis.landAnalysis[index]
                self.analysis.landAnalysis.splice(index, 1);
                if (currentRowObj.price && currentRowObj.price != '') {
                    self.analyseLandChanges(-1)
                }
            });
            $('.addOtherImprovementsRow').off("click").click(function(evt){
                var index = parseInt($(this).attr('data-index'));
                var newLandRowObj = self.getNewAnalysisRowObj();
                self.analysis.otherImprovementsAnalysis.splice(index+1, 0, newLandRowObj)
            });
            $('.removeOtherImprovementsRow').off("click").click(function(evt){
                var index = $(this).attr('data-index');
                var currentRowObj = self.analysis.otherImprovementsAnalysis[index]
                self.analysis.otherImprovementsAnalysis.splice(index, 1);
                if (currentRowObj.price && currentRowObj.price != '') {
                    self.analyseOtherImprovementChanges(-1)
                }
            });

            $('.addOtherBuildingsRow').off("click").click(function(evt){
                var index = parseInt($(this).attr('data-index'));
                var newLandRowObj = self.getNewAnalysisRowObj();
                self.analysis.otherBuildingsAnalysis.splice(index+1, 0, newLandRowObj)
            });
            $('.removeOtherBuildingsRow').off("click").click(function(evt){
                var index = $(this).attr('data-index');
                var currentRowObj = self.analysis.otherBuildingsAnalysis[index]
                self.analysis.otherBuildingsAnalysis.splice(index, 1);
                if (currentRowObj.price && currentRowObj.price != '') {
                    self.analyseOtherBuildingChanges(-1)
                }
            });

            $('.restoreSalesAnalysis').off("click").click(function(evt){
                self.generateSalesAnalysisData(JSON.parse(self.originalResponse))
            });

            $('.deleteSalesAnalysis').off("click").click(function(evt){
                var m = jsRoutes.controllers.SalesAnalysis.deleteSalesAnalysis(self.sale.id);
                $.ajax({
                    type: "DELETE",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        console.log('sales analysis deleted successfully');
                        window.close()
                    },
                    error: function (response) {
                        self.errorHandler(response);
                        alert(response.statusText)
                    }
                });
            });

            $('.save-and-close').off("click").click(function(evt){
                if (self.checkForWarnings()){
                    var analysisData = JSON.stringify(self.generateDataForCalculation());
                    var m = jsRoutes.controllers.SalesAnalysis.updateSalesAnalysis();
                    $.ajax({
                        type: "POST",
                        url: m.url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: analysisData,
                        dataType: "json",
                        success: function (response) {
                            console.log('success -> '+response);
                            window.close()
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            self.calculationError = response;
                            alert(response.statusText)
                        }
                    });
                }
            });
        }
    }
</script>
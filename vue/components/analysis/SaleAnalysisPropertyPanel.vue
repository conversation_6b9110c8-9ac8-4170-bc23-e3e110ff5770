<template>
    <div class="resultsRow openProp salesRow" style="padding: 0px; width: 100%; margin: 0px;">
        <div class="panel-wrapper">
            <div class="panel">
                <div class="cell">
                    <div class="field">
                        <p>Category:</p>
                        <p>{{ formatField(property.category.code) }}</p>
                    </div>
                    <div class="field">
                        <p>QPID:</p>
                        <p>{{ formatField(property.qpid) }}</p>
                    </div>
                    <div class="field">
                        <p>Valuation Reference:</p>
                        <p>{{ formatField(property.valRef) }}</p>
                    </div>
                </div>
                <div class="cell">
                    <div class="field">
                        <p>Land Area (ha):</p>
                        <p>{{ formatField(property.landUse.landArea) }}</p>
                    </div>
                    <div class="field">
                        <p>Production (kg/MS):</p>
                        <p>{{ formatField(property.landUse.production) }}</p>
                    </div>
                    <div class="field">
                        <p>TFA (m<sup>2</sup>):</p>
                        <p>{{ formatField(property.landUse.totalFloorArea) }}</p>
                    </div>
                </div>
                <div class="cell">
                    <div class="field">
                        <p>Sale ID:</p>
                        <p>{{ formatField(sale.saleId) }}</p>
                    </div>
                    <div class="field">
                        <p>Classification:</p>
                        <p>{{ formatField(displayClassification) }}</p>
                    </div>
                    <div class="field">
                        <p>Status:</p>
                        <p>{{ formatField(sale.saleStatus) }}</p>
                    </div>
                </div>
                <div class="cell">
                    <div class="field">
                        <p>Sale Date:</p>
                        <p>{{ formatField(formatDate(sale.saleDate, 'DD/MM/YYYY', true)) }}</p>
                    </div>
                    <div class="field">
                        <p>Settlement Date:</p>
                        <p>{{ formatField(formatDate(sale.settlementDate, 'DD/MM/YYYY', true)) }}</p>
                    </div>
                    <div class="field">
                        <p>Vendor Purchaser:</p>
                        <p>{{ formatField(sale.parties) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import propertyUtils from '../../utils/PropertyUtils';

export default {
    name: 'sa-property-panel',
    mixins: [propertyUtils],
    props: {
        sale: {
            type: Object,
            required: true,
        },
        property: {
            type: Object,
            required: true,
        },
    },
    data: function() {
        return {
            displayClassification: this.formatClassification(this.sale.classifications),
        };
    },
    computed: {},
    methods: {
        formatClassification(classifications) {
            let formatted = classifications ? (classifications.saleType
                ? classifications.saleType.code
                : '') : '';
            formatted += classifications ? (classifications.saleTenure
                ? classifications.saleTenure.code
                : '') : '';
            formatted += classifications ? (classifications.priceValueRelationship
                ? classifications.priceValueRelationship.code
                : '') : '';

            return formatted;
        },
        formatField(value) {
            return value ? value : '-';
        },
    },
};
</script>

<style lang="scss" scoped>
.panel-wrapper {
    width: 100%;
}

.panel {
    height: inherit;
    display: flex;
    background: var(--color-blue-900);
    border-radius: 0.3rem;

    .cell {
        color: white;
        height: inherit;
        width: 25%;
        padding: 1rem;

        border-right-style: solid;
        border-right-width: 1px;
        border-right-color: rgba(255, 255, 255, 0.15);
    }

    .field {
        font-size: 1.2rem;
        display: flex;
        justify-content: space-between;

        p:first-child {
            width: 55%;
        }

        p:last-child {
            width: 45%;
        }
    }
}
</style>

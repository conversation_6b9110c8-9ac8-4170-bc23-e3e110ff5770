<template>
    <div>
        <div class="uploader-header-margin"></div>
        <div class="uploader-header-wrapper">
            <div
                class="uploader-header uploaderHeader mdl-tabs mdl-js-tabs mdl-js-ripple-effect mdl-js-ripple-effect--ignore-events is-upgraded"
                data-upgraded=",MaterialTabs,MaterialRipple">
                <div class="mdl-tabs__tab-bar">
                    <a href="#" class="mdl-tabs__tab is-active">
                        Sales Analysis
                        <span class="mdl-tabs__ripple-container mdl-js-ripple-effect"
                              data-upgraded=",MaterialRipple">
                            <span class="mdl-ripple"></span>
                        </span>
                    </a>
                </div>
                <span class="righty" href="#">
                    <i id="save-and-close" class="save-and-close mdl-button blanc active"
                       @click="verifyAndClose">
                        Verify &amp; Close
                    </i>
                </span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'sa-header',
    methods: {
        verifyAndClose() {
            this.$emit('verify-and-close');
        },
    },
};
</script>

<style lang="scss" scoped>
$header-height: 50px;

.uploader-header-margin {
    margin-top: $header-height;
}

.uploader-header-wrapper {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.uploader-header {
    height: $header-height;
    background-color: var(--color-blue-600);
    background: linear-gradient(to right, var(--color-blue-600) 20%, var(--color-blue-700) 100%)
}
</style>

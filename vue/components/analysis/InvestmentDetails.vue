<template xmlns:v-on="http://www.w3.org/1999/xhtml">
    <div class="QVHV-formSection investmentdetails" v-if="showPropertyPlusTemplate">
            <!-- RENT ROW -->
            <div class="advSearch-row">
                <div class="advSearch-group twentyPct icons8-sell-property-filled">
                    <h4 class="investmentUnit">Total Property Rent</h4>
                </div>
                <div class="advSearch-group tenPct"></div>
                <text-input id="rentPropPlus"
                            :curr-val="formatPrice(rent.amount,'$0,0')"
                            attr-name="rentPropPlus"
                            fieldType="price"
                            iconClass="fifteenPct icons8-sell-property-filled calculated"
                            label="Estimated ($ Per Week)"
                            component-name="investment-details"
                            format="$0,0"
                            readOnly="true">
                </text-input>
                <text-input id="rentBasisPropPlus"
                            :curr-val="rent.basis"
                            attr-name="rentBasisPropPlus"
                            fieldType="text"
                            iconClass="twentyfivePct icons8-legal-description calculated"
                            label="Rental Estimate Basis"
                            component-name="investment-details"
                            readOnly="true">
                </text-input>
                <text-input id="weeklyRentalAmountPropPlus"
                            :curr-val="formatPrice(qvPropertyDetails.aggregateUnitDetails.rentalAmount.weeklyRentalAmount,'$0,0')"
                            attr-name="weeklyRentalAmount"
                            parent-attr-name="rentalAmount"
                            fieldType="price"
                            iconClass="fifteenPct icons8-sell-property-filled"
                            label="Actual ($ Per Week)"
                            format="$0,0"
                            component-name="property-plus"
                            maxlength="8">
                </text-input>
                <text-input id="knownDateAmountPropPlus"
                            :curr-val="formatDate(qvPropertyDetails.aggregateUnitDetails.rentalAmount.knownDate,'MM/YYYY')"
                            attr-name="knownDate"
                            parent-attr-name="rentalAmount"
                            fieldType="date"
                            iconClass="fifteenPct icons8-calendar-filled"
                            label="Known (MM/YYYY)"
                            format="MM/YYYY"
                            maxlength="7"
                            component-name="property-plus">
                </text-input>
            </div>

            <!-- NEW ROW -->
            <div class="advSearch-row" v-for="unit,key in qvPropertyDetails.unitDetails">
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyPct icons8-room-filled" v-if="units > 4">
                        <h4>Combined Property Details</h4>
                    </div>
                    <valuation-multi-select-filter
                            v-if="units <= 4"
                            :id="'unitTypeUnitDetails'+(key+1)"
                            :obj-key="key+1"
                            :curr-val="unit.unitType"
                            iconClass="twentyPct icons8-room-filled"
                            component-name="unit-details"
                            attrName="unitType"
                            :filter-id="'unit-details-unitType'+(key+1)"
                            :label="'Unit '+unit.unitNumber"
                            :selectClass="'monarch-multiselect-unit-details-unitType'+(key+1)"
                            data-to-fetch="UnitType">
                    </valuation-multi-select-filter>
                    <div class="advSearch-group tenPct"></div>
                    <text-input :id="'effectiveFloorAreaUnitUnitDetails'+(key+1)"
                                :curr-val="unit.effectiveFloorArea"
                                :obj-key="key+1"
                                attr-name="effectiveFloorArea"
                                fieldType="number"
                                maxlength="7"
                                iconClass="tenPct icons8-floor-plan-filled"
                                label="Floor Area"
                                component-name="unit-details">
                    </text-input>
                    <text-input :id="'numberOfSingleBedroomsUnitDetails'+(key+1)"
                                :curr-val="unit.numberOfSingleBedrooms"
                                :obj-key="key+1"
                                attr-name="numberOfSingleBedrooms"
                                fieldType="number"
                                iconClass="tenPct icons8-bed-filled"
                                label="Single Bed"
                                component-name="unit-details"
                                maxlength="2">
                    </text-input>
                    <text-input :id="'numberOfDoubleBedroomsUnitDetails'+(key+1)"
                                :curr-val="unit.numberOfDoubleBedrooms"
                                :obj-key="key+1"
                                attr-name="numberOfDoubleBedrooms"
                                fieldType="number"
                                iconClass="tenPct icons8-bed-filled"
                                label="Double Bed"
                                component-name="unit-details"
                                maxlength="2">
                    </text-input>
                    <text-input :id="'numberOfBathroomsUnitDetails'+(key+1)"
                                :curr-val="unit.numberOfBathrooms"
                                :obj-key="key+1"
                                attr-name="numberOfBathrooms"
                                fieldType="number"
                                iconClass="tenPct icons8-total-bathrooms-filled"
                                label="Bathrooms"
                                component-name="unit-details"
                                maxlength="2">
                    </text-input>
                    <text-input :id="'weeklyRentalAmountUnitDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="formatPrice(unit.rentalAmount.weeklyRentalAmount,'$0,0')"
                                attr-name="weeklyRentalAmount"
                                parent-attr-name="rentalAmount"
                                fieldType="price"
                                iconClass="fifteenPct icons8-sell-property-filled"
                                label="Rental Income ($/week)"
                                component-name="unit-details"
                                format="$0,0"
                                maxlength="8">
                    </text-input>
                    <text-input :id="'knownDateUnitDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="formatDate(unit.rentalAmount.knownDate,'MM/YYYY')"
                                attr-name="knownDate"
                                parent-attr-name="rentalAmount"
                                fieldType="date"
                                iconClass="fifteenPct icons8-calendar-filled"
                                label="Known (MM/YYYY)"
                                format="MM/YYYY"
                                maxlength="7"
                                component-name="unit-details">
                    </text-input>
                </div>
                <label for="Investment Unit Extras 1" class="investmentExtras-trigger" @click="showUnitExtraDetails(key)">Add Extra Details<i class="material-icons">&#xE5C5;</i></label>
                <!-- OPTIONAL/HIDDEN FIELDS ROW -->
                <div :id="'extraDetailsUnit'+key" class="advSearch-Subrow">
                    <div class="advSearch-group twentyPct"></div>
                    <valuation-multi-select-filter
                            :id="'kitchenAgeUnitDetails'+(key+1)"
                            :obj-key="key+1"
                            :curr-val="unit.kitchenAge"
                            iconClass="twentyPct icons8-fridge-filled"
                            component-name="unit-details"
                            attrName="kitchenAge"
                            :filter-id="'unit-details-kitchenAge'+(key+1)"
                            label="Kitchen Age"
                            :selectClass="'monarch-multiselect-unit-details-kitchenAge'+(key+1)"
                            data-to-fetch="Age">
                    </valuation-multi-select-filter>
                    <valuation-multi-select-filter
                            :id="'heatingTypeUnitDetails'+(key+1)"
                            :obj-key="key+1"
                            :curr-val="unit.heatingType"
                            iconClass="twentyPct icons8-fire-station-filled"
                            component-name="unit-details"
                            attrName="heatingType"
                            :filter-id="'unit-details-heatingType'+(key+1)"
                            label="Heating Type"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            :selectClass="'monarch-multiselect-unit-details-heatingType'+(key+1)"
                            data-to-fetch="HeatingType">
                    </valuation-multi-select-filter>
                    <valuation-multi-select-filter
                            :id="'insulationUnitDetails'+(key+1)"
                            :obj-key="key+1"
                            :curr-val="unit.insulation"
                            iconClass="twentyPct icons8-heating-room-filled"
                            component-name="unit-details"
                            attrName="insulation"
                            :filter-id="'unit-details-insulation'+(key+1)"
                            label="Insulation"
                            :selectClass="'monarch-multiselect-unit-details-insulation'+(key+1)"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="Insulation">
                    </valuation-multi-select-filter>
                    <valuation-multi-select-filter
                            :id="'doubleGlazingDetails'+(key+1)"
                            :obj-key="key+1"
                            :curr-val="unit.doubleGlazing"
                            iconClass="twentyPct icons8-closed-window-filled"
                            component-name="unit-details"
                            attrName="doubleGlazing"
                            :filter-id="'unit-details-doubleGlazing'+(key+1)"
                            label="Glazing"
                            :selectClass="'monarch-multiselect-unit-details-doubleGlazing'+(key+1)"
                            data-to-fetch="DoubleGlazing">
                    </valuation-multi-select-filter>
                </div>
            </div>

        </div>
</template>

<script>
    import {EventBus} from '../../EventBus.js';
    import { store } from '../../DataStore.js';
    import TextInput from '../filters/TextInput.vue';
    import TextAreaInput from '../filters/TextAreaInput.vue';
    import ValuationMultiSelectFilter from '../filters/ValuationMultiSelectFilter.vue';
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import moment from 'moment';

    export default {
        props: ['property', 'qupid', 'units', 'rent'],
        mixins: [formatUtils, commonUtils],
        components: {
            TextInput,
            TextAreaInput,
            ValuationMultiSelectFilter
        },
        data: function() {
            return {
                hasQvDetails: false,
                showPropertyPlusTemplate: true,
                refreshPropertyPlusTemplate : false,
                qvPropertyDetails: {
                    id: this.property,
                    qupid: this.qupid,
                    effectiveDateOfCollection: null,
                    effectiveLandArea: null,
                    aggregateUnitDetails: {
                        unitNumber: null,
                        unitType: null,
                        numberOfSingleBedrooms: null,
                        numberOfDoubleBedrooms: null,
                        numberOfHomeOfficesOrStudies: null,
                        numberOfBathrooms: null,
                        mainBathroomAge: null,
                        mainBathroomQuality: null,
                        ensuiteAge: null,
                        ensuiteQuality: null,
                        kitchenAge: null,
                        kitchenQuality: null,
                        redecorationAge: null,
                        internalCondition: null,
                        heatingType: [],
                        insulation: [],
                        plumbingAge: null,
                        wiringAge: null,
                        doubleGlazing: null,
                        alternativeEnergy: [],
                        effectiveFloorArea: null,
                        studHeight: null,
                        additionalFeatures: null,
                        rentalAmount: {
                            source: null,
                            knownDate: null,
                            weeklyRentalAmount: null
                        }
                    },
                    unitDetails: [],
                    hazards: [],
                    hazardNotes: null,
                    heritageFeatures: [],
                    averageDailySunshineHours: null
                }
            }
        },
        methods: {
            hasQvProperty: function (callback) {
                var self = this;
                var get = jsRoutes.controllers.PropertyMasterData.hasQvProperty(self.property);
                $.ajax({
                    type: "GET",
                    dataType: 'json',
                    url: get.url,
                    cache: false,
                    success: function (response) {
                        if(response == true) {
                            self.hasQvDetails = true;
                            if(callback) callback();
                        } else {
                            self.addUnits();
                        }
                    },
                    error: function (response) {
                        console.log('error checking property has qv property details: ' + response);
                        self.errorHandler(response);
                    }
                });
            },
            getQvProperty: function () {
                var self = this;
                var get = jsRoutes.controllers.PropertyMasterData.getQvProperty(self.property);
                $.ajax({
                    type: "GET",
                    dataType: 'json',
                    url: get.url,
                    cache: false,
                    success: function (response) {
                        self.qvPropertyDetails = response;
                        if(!self.qvPropertyDetails.aggregateUnitDetails.rentalAmount) {
                            self.qvPropertyDetails.aggregateUnitDetails.rentalAmount = {
                                source: null,
                                knownDate: null,
                                weeklyRentalAmount: null
                            };
                        }
                        self.addUnits();
                        self.refreshPropertyPlusDisplay();
                    },
                    error: function (response) {
                        console.error('[Monarch error]: Failed to get qv property details', response);
                        self.errorHandler(response);
                    }
                });
            },
            saveQvProperty: function() {
                var self = this;
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.PropertyMasterData.saveQvProperty().url,
                    cache: false,
                    processData: false,
                    contentType: 'application/json',
                    data: JSON.stringify(self.qvPropertyDetails),
                    success: function (response) {
                        if(response.success == true) {
                            self.qvPropertyDetails = response.value;
                        } else {
                            console.log('Error in saving qvPropertyDetails' + response.errors);
                        }
                    },
                    error: function (response) {
                        console.log('Error in saving qvPropertyDetails');
                        self.errorHandler(response);
                    }
                });
            },
            refreshPropertyPlusDisplay: function() {
                const self = this;
                self.showPropertyPlusTemplate = !self.showPropertyPlusTemplate;
                if (!self.showPropertyPlusTemplate) {
                    self.refreshPropertyPlusTemplate = true;
                }
            },
            saveOnDropDownHide: function() {
                this.saveQvProperty();
            },
            addUnits: function() {
                var self = this;
                var units = self.units <= 4 ? self.units : 1;
                var existingUnits = self.qvPropertyDetails.unitDetails.length;
                if(units > existingUnits) {
                    var start = (existingUnits > 0 && existingUnits < units) ? +existingUnits + 1 : 1;
                    for(var i = start; i <= units; i++ ) {
                        self.qvPropertyDetails.unitDetails.push({
                            unitNumber: i,
                            unitType: null,
                            numberOfSingleBedrooms: null,
                            numberOfDoubleBedrooms: null,
                            numberOfBathrooms: null,
                            kitchenAge: null,
                            heatingType: [],
                            insulation: [],
                            doubleGlazing: null,
                            effectiveFloorArea: null,
                            rentalAmount: {
                                source: null,
                                knownDate: null,
                                weeklyRentalAmount: null
                            }
                        });
                    }
                } else if(units < existingUnits) {
                    self.qvPropertyDetails.unitDetails = self.qvPropertyDetails.unitDetails.slice(0, units);
                    self.saveQvProperty();
                }
            },
            showUnitExtraDetails: function(key) {
                $('#extraDetailsUnit'+key).toggle();
            }
        },
        mounted: function() {
            const self = this;
            self.hasQvProperty(self.getQvProperty);
            EventBus.$on('notify-simple-property-plus', function(data){
                if(data.parentAttrName) {
                    self.qvPropertyDetails.aggregateUnitDetails[data.parentAttrName][data.attrName] = (data.fieldType == 'date' || data.fieldType == 'price')  ? data.raw : data.val;
                } else {
                    self.qvPropertyDetails.aggregateUnitDetails[data.attrName] = (data.fieldType == 'date' || data.fieldType == 'price')  ? data.raw : data.val;
                }
                self.saveQvProperty();
            });
            EventBus.$on('notify-simple-nested-unit-details', function (data) {
                if(data.parentAttrName) {
                    self.qvPropertyDetails.unitDetails[data.key - 1][data.parentAttrName][data.attrName] = (data.fieldType == 'date' || data.fieldType == 'price') ? data.raw : data.val;
                } else {
                    self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = (data.fieldType == 'date' || data.fieldType == 'price') ? data.raw : data.val;
                }
                self.saveQvProperty();
            });
            EventBus.$on('notify-multi-nested-unit-details', function (data) {
                if($.type(data.val)==='object'){
                    var classificationList = self.$store.getters.getCategoryClassifications(data.val.category);
                    var exists = classificationList.filter(function(e) { return e.code == data.val.code; });
                    if(exists.length > 0) {
                        self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = exists[0];
                        self.saveQvProperty();
                    }
                } else if($.type(data.val)==='array'){
                    if(data.val.length == 0) {
                        self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = data.val;
                    } else {
                        var classificationList = self.$store.getters.getCategoryClassifications(data.val[0].category);
                        var classifications = [];
                        $.each(data.val, function (i, obj) {
                            var exists = classificationList.filter(function(e) { return e.code == obj.code; });
                            if(exists.length > 0) {
                                classifications.push(exists[0]);
                            }
                        });
                        self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = classifications;
                    }
                } else if(data.val == undefined){
                    self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName]  = null;
                    self.saveQvProperty();
                }
            });
        },
        updated: function() {
            if (this.refreshPropertyPlusTemplate) {
                this.showPropertyPlusTemplate = true;
                this.refreshPropertyPlusTemplate = false;
            }
        },
        destroyed: function() {
            EventBus.$off('notify-simple-property-plus', this.listener);
            EventBus.$off('notify-simple-nested-unit-details', this.listener);
            EventBus.$off('notify-multi-nested-unit-details', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('notify-simple-property-plus', this.listener);
            EventBus.$off('notify-simple-nested-unit-details', this.listener);
            EventBus.$off('notify-multi-nested-unit-details', this.listener);
        }
    }
</script>

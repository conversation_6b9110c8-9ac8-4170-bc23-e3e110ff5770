<template>
    <div class="salesRow">
        <div class="panel-wrapper">
            <div class="panel">
                <table-body class="cell">
                    <table-row field-name="Sale Price" :empty-value="true" class="header"/>
                    <table-row field-name="Gross:" :field-value="formatMoneyField(gross)"/>
                    <table-row field-name="Less GST:" :field-value="formatMoneyField(gst)"/>
                    <table-row field-name="Chattels:" :field-value="formatMoneyField(chattels)"/>
                    <table-row field-name="Other:" :field-value="formatMoneyField(other)"/>
                    <table-row field-name="Net Sale Price (NSP):"
                               :field-value="formatMoneyField(nsp)"
                               class="footer-total"/>
                </table-body>
                <table-body class="cell">
                    <table-row field-name="Sale Price Ratios" :empty-value="true" class="header"/>
                    <table-row field-name="Net Sale Price/Prodn:"
                               :field-value="formatMoneyField(nspProduction)"/>
                    <table-row field-name="Net Sale Price/Ha:"
                               :field-value="formatMoneyField(nspHa)"/>
                    <table-row field-name="Land Sale Price/Prodn:"
                               :field-value="formatMoneyField(lspProduction)"/>
                    <table-row field-name="Land Sale Price/Ha:"
                               :field-value="formatMoneyField(lspHa)"/>
                    <table-row field-name="Analysed Land Sale Price:"
                               :field-value="formatMoneyField(analysis.analysedLV)"
                               class="footer-total"/>
                </table-body>
                <table-body class="cell">
                    <table-row field-name="Analysed Ratios" :empty-value="true" class="header"/>
                    <table-row :field-values="['Ratio', 'RV/Ha']" class="bold"/>
                    <table-row field-name="Net Sale Price/CV:"
                               :field-values="[formatDecimal(cvNspRv, 2), formatMoneyField(cvRvHa)]"/>
                    <table-row field-name="Analysed LV/LV:"
                               :field-values="[formatDecimal(lvNspRv, 2), formatMoneyField(lvRvHa)]"/>
                    <table-row field-name="Analysed VI/VI:"
                               :field-values="[formatDecimal(viNspRv, 2), null]"/>
                    <table-row field-name="Analysed Improvements:"
                               :field-value="formatMoneyField(analysis.analysedVI)"
                               class="footer-total"/>                   
                </table-body>
                <table-body class="cell">
                    <table-row field-name="Rating Valuation" :empty-value="true" class="header"/>
                    <table-row field-name="Effective Date:"
                               :field-value="formatDate(property.ratingValuation.effectiveDate, 'DD/MM/YYYY', true)"/>
                    <table-row field-name="Capital Value:"
                               :field-value="formatMoneyField(property.ratingValuation.capitalValue)"/>
                    <table-row field-name="Land Value:"
                               :field-value="formatMoneyField(property.ratingValuation.landValue)"/>
                    <table-row field-name="Value of Improvements"
                               :field-value="formatMoneyField(vi)"/>
                    <table-row :empty-value="true" class="footer"/>
                </table-body>
            </div>
        </div>
    </div>
</template>

<script>
import TableBody from 'Common/tables/Table.vue';
import TableRow from 'Common/tables/TableRow.vue';
import PropertyUtils from '@/utils/PropertyUtils';
import FormatUtils from '@/utils/FormatUtils';

export default {
    name: 'sa-sale-panel',
    components: {
        TableBody,
        TableRow,
    },
    mixins: [PropertyUtils, FormatUtils],
    props: {
        sale: {
            type: Object,
            required: true,
        },
        property: {
            type: Object,
            required: true,
        },
        analysis: {
            type: Object,
            required: true,
        },
        showSaleDetails: {
            type: Boolean,
            default: true,
        },
    },
    data: function() {
        return {
            landUseData: this.property.landUse,
        };
    },
    computed: {
        lspProduction() {
            return (this.landUseData.production > 0)
                ? this.analysis.analysedLV / this.landUseData.production
                : null;
        },
        lspHa() {
            return this.landUseData.landArea ? this.analysis.analysedLV /
                this.landUseData.landArea : null;
        },
    },
    methods: {
        formatMoneyField(value) {
            if (!value) {
                return null;
            }
            return this.formatPrice(value, '$0,0');
        },
    },
    created: function() {
        const hasProduction = (this.landUseData.production > 0);
        const {landValue, capitalValue} = this.property.ratingValuation;

        this.gross = this.sale.price.gross;
        this.gst = this.sale.price.gst;
        this.chattels = this.sale.price.chattels;
        this.other = this.sale.price.other;
        this.nsp = this.sale.price.net;
        this.nspProduction = hasProduction
            ? (this.nsp / this.landUseData.production)
            : null;
        this.nspHa = this.landUseData.landArea > 0
            ? (this.nsp / this.landUseData.landArea)
            : null;

        this.vi = capitalValue - landValue;
        this.cvNspRv = this.nsp / capitalValue;
        this.lvNspRv = this.analysis.analysedLV / landValue;
        this.viNspRv = this.vi > 0 ? this.analysis.analysedVI / this.vi : null;
        this.cvRvHa = capitalValue / this.landUseData.landArea;
        this.lvRvHa = landValue / this.landUseData.landArea;
    },
};
</script>

<style lang="scss" scoped>
.panel-wrapper {
    width: 100%;
}

.panel {
    height: inherit;
    display: flex;
    margin-bottom: 3rem;

    @media (max-width: 825px) {
        flex-direction: column;
    }

    .cell {
        height: max-content;
        width: 25%;
        margin: 1rem;

        @media (max-width: 825px) {
            width: calc(100% - 11px);
            margin: 1rem auto;
        }
    }

    .cell:first-of-type {
        margin-left: 0 !important;
    }

    .cell:last-of-type {
        margin-right: 0 !important;
    }
}
</style>

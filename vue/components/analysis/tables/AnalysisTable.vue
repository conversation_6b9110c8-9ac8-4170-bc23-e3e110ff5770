<template>
    <div class="analysis-table">
        <div class="title-wrapper">
            <div class="title">
                <slot name="title"/>
            </div>
            <div class="title-right">
                <slot name="title-right"/>
                <expander
                    v-if="collapsable"
                    v-model="collapsed"
                    displayMode="dark"
                    data-cy="expander"
                />
            </div>
        </div>
        <transition>
            <div class="analysis-table-rows" v-show="!collapsed">
                <div class="analysis-row-wrapper">
                    <div class="analysis-row-header">
                        <div class="analysis-row-columns">
                            <slot name="columns"/>
                        </div>
                        <div v-if="showTotalColumn" class="analysis-row-total-column">
                            <p>
                                <slot name="total-column"/>
                            </p>
                        </div>
                    </div>
                </div>
                <slot/>
            </div>
        </transition>
    </div>
</template>

<script>
import Expander from 'Common/Expander.vue';

export default {
    name: 'analysis-table',
    components: {
        Expander,
    },
    props: {
        title: {
            type: String,
        },
        collapsable: {
            type: Boolean,
            default: false,
            required: false,
        },
        startCollapsed: {
            type: Boolean,
            default: false,
            required: false,
        },
        showTotalColumn: {
            type: Boolean,
            default: true,
            required: false,
        },
    },
    data() {
        return {
            collapsed: this.startCollapsed,
        };
    },
    methods: {},
};
</script>

<style lang="scss">
@import '~@/_helpers.scss';

$border-radius: 0.3rem;

.v-enter-active, .v-leave-active {
    transition: all 0.25s;
    overflow: hidden;
    max-height: 50rem;
}

.v-enter-from, .v-leave-to {
    max-height: 0;
}

.analysis-table {
    margin-top: 2rem;
    margin-bottom: 3rem;

    .title-wrapper {
        @extend .flex-row;
        justify-content: space-between;
        width: 100%;
    }

    .title {
        font-weight: 600;
        font-size: 1.75rem;
        color: var(--color-blue-900);
        margin-top: auto;
        width: 100%;
    }

    .title-right {
        display: flex;
        gap: 3rem;

        > * {
            margin-top: auto;
        }
    }

    .analysis-table-rows {
        @extend .flex-table;
        margin-top: 1rem;

        .analysis-row-wrapper {
            &:first-child {
                .analysis-row, .analysis-row-header {
                    border-top-right-radius: $border-radius;
                    border-top-left-radius: $border-radius;
                }
            }

            &:last-child {
                .analysis-row, .analysis-row-header {
                    border-bottom-right-radius: $border-radius;
                    border-bottom-left-radius: $border-radius;
                }
            }
        }

        .analysis-row {
            @extend .flex-row;
            background: var(--color-lightblue-200);
            font-size: 1.2rem;
            min-height: 53.5px;

            .analysis-row-columns {
                @extend .flex-row;
                padding: 1rem;
                gap: 1rem;
                border: var(--color-lightblue-300) 1px solid;
                border-top: 0;
                border-right: 0;
                align-items: center;
            }

            .analysis-row-total-column {
                @extend .flex-row;
                padding: 1rem;
                width: 35rem;
                background: var(--color-lightblue-400);
            }
        }

        .analysis-row-header {
            @extend .analysis-row;
            color: white;
            background-color: var(--color-blue-900);
            overflow: hidden;
            min-height: unset;

            .analysis-row-columns {
                border: none;
            }

            .analysis-row-total-column {
                background-color: inherit;

                p {
                    width: 100%;
                    text-align: center;
                }
            }
        }
    }
}
</style>

<template>
    <div v-if="hasWorksheet">
        <analysis-row :index="index" v-on="$listeners">
            <div class="col-1">
                <tooltip :text="errors.qpid">
                    <table-input
                        type="text"
                        :value.sync="improvement.qpid"
                        :invalid="errors.qpid"
                    />
                </tooltip>
            </div>
            <div class="col-2">
                <tooltip :text="errors.valRef">
                    <table-input
                        type="text"
                        :value.sync="improvement.valRef"
                        :invalid="errors.valRef"
                    />
                </tooltip>
            </div>
            <div class="col-3">
                <div>
                    <table-input
                        type="text"
                        :value.sync="improvement.description"
                    />
                </div>
            </div>
            <div class="col-3">
                <tooltip :text="errors.improvementId">
                    <table-select
                        :value.sync="improvement.improvementId"
                        :options="classifications.improvement || []"
                        :invalid="errors.improvementId"
                        description-key="description"
                        @update:value="updateImprovement"
                    />
                </tooltip>
            </div>
            <div class="col-1">
                <tooltip :text="errors.size">
                    <table-input
                        type="number"
                        min="0"
                        :value.sync="improvement.size"
                        :invalid="errors.size"
                        @update:value="updateSize"
                    />
                </tooltip>
            </div>
            <div class="col-2">
                <tooltip :text="errors.rate">
                    <table-input
                        type="money"
                        min="0"
                        step="0.0001"
                        :value.sync="improvement.rate"
                        :invalid="errors.rate"
                        @update:value="updateRate"
                    />
                </tooltip>
            </div>
            <div class="col-2">
                <div>
                    <table-input
                        type="money"
                        min="0"
                        step="0.0001"
                        :value.sync="improvement.value"
                        @update:value="updateValue"
                    />
                </div>
            </div>
            <template #total>
                <h3 v-if="total">{{ total }}</h3>
            </template>
        </analysis-row>
    </div>
    <div v-else>
        <analysis-row :index="index" v-on="$listeners" :show-buttons="false">
            <div class="col-1">
                <tooltip :text="errors.qpid">
                    <table-input
                        type="text"
                        :disabled="true"
                        :value.sync="improvement.qpid"
                        :invalid="errors.qpid"
                    />
                </tooltip>
            </div>
            <div class="col-2">
                <tooltip :text="errors.valRef">
                    <table-input
                        type="text"
                        :disabled="true"
                        :value.sync="improvement.valRef"
                        :invalid="errors.valRef"
                    />
                </tooltip>
            </div>
            <div class="col-3">
                <div>
                    <table-input
                        type="text"
                        :value.sync="improvement.description"
                    />
                </div>
            </div>
            <div class="col-3">
                <table-input
                    :value.sync="improvement.natureOfImprovements"
                    type="text"
                />
            </div>
            <div class="col-1">
                <table-input
                    :value.sync="improvement.category"
                    type="text"
                />
            </div>
            <div class="col-2">
                <div>
                    <table-input
                        type="money"
                        min="0"
                        :value.sync="improvement.value"
                        @update:value="updateValue"
                    />
                </div>
            </div>
            <template #total>
                <h3 v-if="total">{{ total }}</h3>
            </template>
        </analysis-row>
    </div>
</template>

<script>
import AnalysisRow from './AnalysisRow.vue';
import ClassificationDropdown from 'Common/form/ClassificationDropdown.vue';
import TableInput from 'Common/tables/TableInput.vue';
import TableSelect from 'Common/tables/TableSelect.vue';
import Tooltip from 'Common/Tooltip.vue';
import {mapActions, mapState} from 'vuex';
import AnalysisUtils from '@/utils/AnalysisUtils';

export default {
    name: 'improvements-multi-row',
    components: {
        AnalysisRow,
        ClassificationDropdown,
        TableInput,
        TableSelect,
        Tooltip,
    },
    mixins: [AnalysisUtils],
    props: {
        hasWorksheet: {
            type: Boolean,
            required: false,
            default: true,
        },
        improvement: {
            type: Object,
            required: true,
        },
        index: {
            type: Number,
            required: true,
        },
        total: {
            required: false,
        },
    },
    computed: {
        errors() {
            return this.improvement.error ? this.improvement.error : {};
        },
        ...mapState('saleAnalysis', {
            classifications: 'ruralClassifications',
        }),
    },
    watch: {
        improvement: {
            handler: function(newImp, oldImp) {
                this.calculateAnalysedLV();
            },
            deep: true,
        },
    },
    methods: {
        ...mapActions('saleAnalysis', ['calculateAnalysedLV']),
        updateSize() {
            const {size, rate} = this.improvement;

            if (this.errors.size) {
                this.errors.size = null;
            }

            if (rate) {
                this.improvement.value = this.calculateValue(size, rate);
            }
        },
        updateRate() {
            const {size, rate} = this.improvement;

            if (this.errors.rate) {
                this.errors.rate = null;
            }

            if (size) {
                this.improvement.value = this.calculateValue(size, rate);
            }
        },
        updateValue() {
            const {size, rate, value} = this.improvement;

            if (size && rate && value) {
                this.improvement.rate = this.calculateRate(value, size);
            }
        },
        updateImprovement() {
            if (this.errors.improvementId) {
                this.errors.improvementId = null;
            }
        },
    },
};
</script>

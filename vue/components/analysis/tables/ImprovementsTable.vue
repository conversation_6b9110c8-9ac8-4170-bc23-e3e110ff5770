<template>
    <analysis-table>
        <template #title>
            <div style="position: absolute;">
                <div class="title " style="position: relative; top: -4rem;">
                    Improvements
                </div>
            </div>
        </template>
        <template #title-right>
        </template>
        <template #columns>
            <div class="col-4">Description</div>
            <div class="col-4">Improvement Type</div>
            <div class="col-1">Size/No.</div>
            <div class="col-2">Rate</div>
            <div class="col-3">Value</div>
        </template>
        <template #total-column>Total VI</template>
        <div v-for="(improvement, index) in improvements" :key="index" class="analysis-row-wrapper">
            <improvements-row v-if="!improvement.delete" :improvement="improvement" :index="index"
                              :total="displayTotal(index)"
                              @row:add="addRow"
                              @row:delete="deleteRow"/>
        </div>
    </analysis-table>
</template>

<script>
import {mapActions, mapState} from 'vuex';

import AnalysisTable from './AnalysisTable.vue';
import ImprovementsRow from './ImprovementsRow.vue';
import FormatUtils from '@/utils/FormatUtils';
import PriceChangeCard from 'Common/PriceChangeCard.vue';

export default {
    name: 'improvements-table',
    mixins: [FormatUtils],
    components: {
        AnalysisTable,
        ImprovementsRow,
        PriceChangeCard,
    },
    props: {
        improvements: {
            type: Array,
            required: true,
        },
    },
    computed: {
        ...mapState('saleAnalysis', {
            sale: 'sale',
            primaryProperty: 'property',
            analysis: 'analysis',
        }),
        runningTotal() {
            return this.formatPrice(this.analysis.totalVI, '$0,0');
        },
    },
    watch: {
        'improvements'(newValue, oldValue) {
            if (newValue.length === 0) {
                this.addRow(0);
            }
        },
    },
    methods: {
        ...mapActions('saleAnalysis',
            {
                deleteAnalysisRow: 'deleteRow',
                calculateAnalysedLV: 'calculateAnalysedLV',
            }),
        addRow(index) {
            this.$emit('update:improvements',
                this.improvements.splice(index + 1, 0, this.newImprovement()));
        },
        deleteRow(index) {
            const improvement = this.improvements[index];
            this.$emit('update:improvements', this.improvements.splice(index, 1));

            if (improvement.saleAnalysisRowId) {
                this.deleteAnalysisRow(improvement);
            } else {
                this.calculateAnalysedLV();
            }
        },
        newImprovement() {
            return {
                saleAnalysisRowId: null,
                qpid: this.primaryProperty.qpid,
                valRef: this.primaryProperty.valRef,
                improvementId: null,
                size: null,
                description: null,
                rate: null,
                value: null,
            };
        },
        displayTotal(index) {
            if (this.improvements.length === index + 1) {
                return this.runningTotal;
            }

            return null;
        },
    },
    mounted() {
        if (this.improvements.length === 0) {
            this.addRow(0);
        }
    },
};
</script>

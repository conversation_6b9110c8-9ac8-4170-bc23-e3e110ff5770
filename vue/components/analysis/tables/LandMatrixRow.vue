<template>
    <analysis-row :index="index" :show-buttons="false" v-on="$listeners">
        <div class="col-2">
            <div>
                <table-input
                    type="text"
                    :disabled="true"
                    :value.sync="land.contourName"
                />
            </div>
        </div>
        <div class="col-7">
            <div>
                <table-input
                    type="text"
                    min="0"
                    :disabled="true"
                    :value.sync="land.coverName"
                />
            </div>
        </div>
        <div class="col-2">
            <div>
                <table-input
                    type="text"
                    min="0"
                    :disabled="true"
                    :value.sync="land.qvCoverName"
                />
            </div>
        </div>
        <div class="col-1">
            <div>
                <table-input
                    type="number"
                    min="0"
                    step="0.0001"
                    :disabled="true"
                    :value.sync="land.itemSize"
                />
            </div>
        </div>
        <template #total>
            <h3 v-if="total">{{ total }}</h3>
        </template>
    </analysis-row>
</template>

<script>
import AnalysisRow from './AnalysisRow.vue';
import ClassificationDropdown from 'Common/form/ClassificationDropdown.vue';
import TableInput from 'Common/tables/TableInput.vue';

export default {
    name: 'land-matrix-row',
    components: {
        AnalysisRow,
        ClassificationDropdown,
        TableInput,
    },
    props: {
        land: {
            type: Object,
            required: true,
        },
        index: {
            type: Number,
            required: true,
        },
        total: {
            required: false,
        },
    },
};
</script>

<template>
    <div class="analysis-row" :class="{ 'no-background': hideBackground }">
        <div class="analysis-row-columns" :class="{ 'no-background': hideBackground }">
            <slot/>
        </div>
        <div v-if="showTotalColumn" class="analysis-row-total-column">
            <div class="analysis-row-total">
                <slot name="total"/>
            </div>
            <div class="analysis-row-buttons">
                <div class="button-wrapper">
                    <div v-if="showButtons">
                        <i class="button-delete material-icons" @click="deleteRow">
                            &#xE15C;
                        </i>
                        <i class="button-fill"></i>
                    </div>
                </div>
                <div class="button-wrapper">
                    <div v-if="showButtons">
                        <i class="button-add material-icons" @click="addRow">
                            &#xE147;
                        </i>
                        <i class="button-fill"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'analysis-row',
    props: {
        index: {
            type: Number,
            required: false,
        },
        showButtons: {
            required: false,
            default: true,
        },
        showTotalColumn: {
            required: false,
            default: true,
        },
        hideBackground: {
            required: false,
            default: false
        }
    },
    methods: {
        addRow() {
            this.$emit('row:add', this.index);
        },
        deleteRow() {
            this.$emit('row:delete', this.index);
        },
    },
};
</script>

<style lang="scss" scoped>
.no-background {
    background-color: transparent !important;
    border: unset !important;
}

.analysis-row-total {
    flex-grow: 1;
    border: var(--color-lightblue-500) 1px solid;
    border-top: 0;
    border-right: 0;

    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 1.5rem;

    h3 {
        font-weight: bold;
        font-style: italic;
        font-size: 2rem;
        color: var(--color-blue-700);
    }
}

.analysis-row-buttons {
    width: 10rem;
    background: var(--color-lightblue-500);
    border: var(--color-lightblue-600) 1px solid;
    border-top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.button-wrapper {
    position: relative;
    min-width: 24px;

    .button-fill {
        position: absolute;
        top: 0.3rem;
        left: 0.3rem;
        content: "";
        background-color: #fff;
        border-radius: 50%;
        width: 1.8rem;
        height: 1.8rem;
        z-index: 1;
    }

    .button-add {
        position: relative;
        color: var(--color-blue-500);
        z-index: 2;
    }

    .button-delete {
        position: relative;
        color: #d2362b;
        z-index: 2;
    }
}

.analysis-row-total-column {
    padding: 0 !important;
}

@media print {
    .analysis-row-total {
        border: none !important;
    }
    .analysis-row-buttons {
        display: none;
    }
}
</style>

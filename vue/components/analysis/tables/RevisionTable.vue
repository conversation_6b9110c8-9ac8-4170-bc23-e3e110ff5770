<template>
    <analysis-table class="revision-table" :collapsable="true"
                    :start-collapsed="!revisionValues.capitalValue" :show-total-column="false">
        <template #title>
            Proposed Revision Values
        </template>
        <template #title-right>
            <div class="flag" v-if="typeof revisionValues.allLinkedPropertiesHaveValues === 'boolean'">
                <p>All linked assessments have Revision Values:</p>
                <badge
                    :class="{ gray: revisionValues.allLinkedPropertiesHaveValues, red: !revisionValues.allLinkedPropertiesHaveValues }">
                    {{ revisionValues.allLinkedPropertiesHaveValues ? 'YES' : 'NO' }}
                </badge>
            </div>
        </template>
        <template #columns>
            <div class="col-12">Effective Date: {{ formatDate(revisionValues.effectiveDate) }}</div>
        </template>
        <analysis-row :show-buttons="false" :show-total-column="false">
            <div class="col-3 flex-row">
                <h3 class="col-7">Revision CV:</h3>
                <table-input disabled :value="revisionValues.capitalValue"
                             type="money"></table-input>
            </div>
            <div class="col-3 flex-row">
                <h3 class="col-7">RCV/Ha:</h3>
                <table-input disabled
                             :value="(landUse.landArea > 0 && hasRCV) ? revisionValues.capitalValue/landUse.landArea : null"
                             type="money"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RCV/NSP:
                </h3>
                <table-input disabled
                             :value="hasRCV ? formatDecimal(revisionValues.capitalValue/sale.price.net, 2) : null"
                             type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RCV/RTV:
                </h3>
                <table-input disabled :value="null" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RCV/CV:
                </h3>
                <table-input disabled
                             :value="hasRCV ? formatDecimal(revisionValues.capitalValue/capitalValue, 2) : null"
                             type="number" class="col-4"></table-input>
            </div>
        </analysis-row>
        <analysis-row :show-buttons="false" :show-total-column="false">
            <div class="col-3 flex-row">
                <h3 class="col-7">Revision LV:</h3>
                <table-input disabled :value="revisionValues.landValue" type="money"></table-input>
            </div>
            <div class="col-3 flex-row">
                <h3 class="col-7">RLV/Ha:</h3>
                <table-input disabled
                             :value="(landUse.landArea > 0 && hasRLV)  ? revisionValues.landValue/landUse.landArea : null"
                             type="money"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RLV/ALSP:
                </h3>
                <table-input disabled
                             :value="hasRLV ? formatDecimal(revisionValues.landValue/analysis.analysedLV, 2) : null"
                             type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RLV/RTLV:
                </h3>
                <table-input disabled :value="null" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RLV/LV:
                </h3>
                <table-input disabled
                             :value="hasRLV ? formatDecimal(revisionValues.landValue/landValue, 2) : null"
                             type="number"
                             class="col-4"></table-input>
            </div>
        </analysis-row>
        <analysis-row :show-buttons="false" :show-total-column="false">
            <div class="col-3 flex-row">
                <h3 class="col-7">Revision VI:</h3>
                <table-input disabled :value="revisionValues.improvementValue"
                             type="money"></table-input>
            </div>
            <div class="col-3 flex-row">
                <h3 class="col-7">RCV/Prodn:</h3>
                <table-input disabled
                             :value="hasRCV && landUse.production > 0 ? revisionValues.capitalValue/landUse.production : null"
                             type="money"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RVI/AVI:
                </h3>
                <table-input disabled
                             :value="hasRVI ? formatDecimal(revisionValues.improvementValue/analysis.analysedVI, 2) : null"
                             type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RVI/RTVI:
                </h3>
                <table-input disabled :value="null" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RVI/VI:
                </h3>
                <table-input disabled
                             :value="hasRVI ? formatDecimal(revisionValues.improvementValue/(capitalValue-landValue), 2) : null"
                             type="number" class="col-4"></table-input>
            </div>
        </analysis-row>

    </analysis-table>
</template>

<script>
import AnalysisTable from './AnalysisTable.vue';
import AnalysisRow from './AnalysisRow.vue';
import FormatUtils from '@/utils/FormatUtils';
import Badge from 'Common/Badge.vue';
import TableInput from 'Common/tables/TableInput.vue';

export default {
    name: 'revision-table',
    mixins: [FormatUtils],
    components: {
        AnalysisTable,
        AnalysisRow,
        TableInput,
        Badge,
    },
    props: {
        revisionValues: {
            type: Object,
            required: true,
        },
        sale: {
            type: Object,
            required: true,
        },
        analysis: {
            type: Object,
            required: true,
        },
        primaryProperty: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            landUse: this.primaryProperty.landUse,
            landValue: this.primaryProperty.ratingValuation.landValue,
            capitalValue: this.primaryProperty.ratingValuation.capitalValue,
            hasRCV: false,
            hasRLV: false,
            hasRVI: false,
        };
    },
    mounted() {
        this.hasRCV = this.revisionValues.capitalValue > 0;
        this.hasRLV = this.revisionValues.landValue > 0;
        this.hasRVI = this.revisionValues.improvementValue > 0;
    },
};
</script>

<style lang="scss" scoped>
.revision-table {
    .flex-row {
        align-items: center;
    }

    h3 {
        font-size: 1.25rem;
    }
}

.flag {
    display: flex;
    width: max-content;

    p {
        font-size: 1.2rem;
        font-weight: bold;
        font-style: italic;
        margin-top: auto;
        margin-bottom: auto;
        margin-right: 8px;
    }
}
</style>

<template>
    <analysis-row :index="index" v-on="$listeners">
        <div class="col-2">
            <tooltip :text="errors.description">
                <table-input
                    type="text"
                    :value.sync="land.description"
                    :invalid="errors.description"
                />
            </tooltip>
        </div>
        <div class="col-2">
            <tooltip :text="errors.contourId">
                <table-select
                    :options="classifications.contour || []"
                    :value.sync="land.contourId"
                    :invalid="errors.contourId"
                    @update:value="updateContour"
                    description-key="description"
                />
            </tooltip>
        </div>
        <div class="col-2">
            <tooltip :text="errors.ruralUseId">
                <table-select
                    :options="classifications.use || []"
                    :value.sync="land.ruralUseId"
                    :invalid="errors.ruralUseId"
                    @update:value="updateRuralUse"
                    description-key="description"
                />
            </tooltip>
        </div>
        <div class="col-2">
            <div>
                <table-select
                    :options="classifications.irrigation || []"
                    :value.sync="land.irrigationId"
                    description-key="description"
                />
            </div>
        </div>
        <div class="col-1">
            <tooltip :text="errors.size">
                <table-input
                    type="number"
                    min="0"
                    step="0.01"
                    :value.sync="land.size"
                    :invalid="errors.size"
                    @update:value="updateSize"
                />
            </tooltip>
        </div>
        <div class="col-1">
            <tooltip :text="errors.rate">
                <table-input
                    type="money"
                    min="0"
                    step="0.0001"
                    :value.sync="land.rate"
                    :invalid="errors.rate"
                    @update:value="updateRate"
                />
            </tooltip>
        </div>
        <div class="col-2">
            <div>
                <table-input
                    type="money"
                    min="0"
                    step="0.0001"
                    :value.sync="land.value"
                    @update:value="updateValue"
                />
            </div>
        </div>
        <template #total>
            <h3 v-if="total">{{ total }}</h3>
        </template>
    </analysis-row>
</template>

<script>
import AnalysisRow from './AnalysisRow.vue';
import ClassificationDropdown from 'Common/form/ClassificationDropdown.vue';
import TableInput from 'Common/tables/TableInput.vue';
import TableSelect from 'Common/tables/TableSelect.vue';
import Tooltip from 'Common/Tooltip.vue';
import {mapState} from 'vuex';
import AnalysisUtils from '@/utils/AnalysisUtils';

export default {
    name: 'land-row',
    components: {
        AnalysisRow,
        ClassificationDropdown,
        TableInput,
        TableSelect,
        Tooltip,
    },
    mixins: [AnalysisUtils],
    props: {
        land: {
            type: Object,
            required: true,
        },
        index: {
            type: Number,
            required: true,
        },
        total: {
            required: false,
        },
    },
    computed: {
        errors() {
            return this.land.error ? this.land.error : {};
        },
        ...mapState('saleAnalysis', {
            classifications: 'ruralClassifications',
        }),
    },
    methods: {
        updateSize() {
            const {size, rate} = this.land;

            if (this.errors.size) {
                this.errors.size = null;
            }

            if (rate) {
                this.land.value = this.calculateValue(size, rate);
            }
        },
        updateRate() {
            const {size, rate} = this.land;

            if (this.errors.rate) {
                this.errors.rate = null;
            }

            if (size) {
                this.land.value = this.calculateValue(size, rate);
            }
        },
        updateValue() {
            const {size, rate, value} = this.land;

            if (size && rate && value) {
                this.land.rate = this.calculateRate(value, size);
            }
        },
        updateContour() {
            if (this.errors.contourId) {
                this.errors.contourId = null;
            }
        },
        updateRuralUse() {
            if (this.errors.ruralUseId) {
                this.errors.ruralUseId = null;
            }
        },
    },
};
</script>

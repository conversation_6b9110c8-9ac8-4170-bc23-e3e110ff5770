<template>
    <div>
        <analysis-table style="margin-top: 0">
            <template #title>
                <div class="title ">
                    Assessments with Rural Worksheets
                </div>
            </template>
            <template #title-right>
            </template>
            <template #columns>
                <div class="col-1">QPID</div>
                <div class="col-2">Val/Ref</div>
                <div class="col-3">Description</div>
                <div class="col-3">Improvement Type</div>
                <div class="col-1">Size/No.</div>
                <div class="col-2">Rate</div>
                <div class="col-2">Value</div>
            </template>
            <template #total-column>Total VI</template>
            <div v-for="(improvement, index) in improvements" :key="index"
                 class="analysis-row-wrapper">
                <improvements-multi-row v-if="!improvement.delete" :improvement="improvement"
                                        :index="index"
                                        :total="displayWorksheetTotal(index)"
                                        @row:add="addRow"
                                        @row:delete="deleteRow"/>
            </div>
            <div class="analysis-row-wrapper" v-if="improvementsWithoutWorksheet.length <= 0">
                <analysis-row :show-buttons="false" :hide-background="true">
                    <div class="title" style="text-align: right">
                        Total Improvement Value:
                    </div>
                    <template #total>
                        <h3>{{ runningTotal }}</h3>
                    </template>
                </analysis-row>
            </div>
        </analysis-table>
        <analysis-table style="margin-top: 0" v-if="improvementsWithoutWorksheet.length > 0">
            <template #title>
                <div class="title ">
                    Assessments without Rural Worksheets
                </div>
            </template>
            <template #title-right>
            </template>
            <template #columns>
                <div class="col-1">QPID</div>
                <div class="col-2">Val/Ref</div>
                <div class="col-3">Description</div>
                <div class="col-3">Nature of Improvements</div>
                <div class="col-1">Category</div>
                <div class="col-2">Value</div>
            </template>
            <template #total-column>Total VI</template>
            <div v-for="(improvement, index) in improvementsWithoutWorksheet" :key="index"
                 class="analysis-row-wrapper">
                <improvements-multi-row v-if="!improvement.delete"
                                        :has-worksheet="false"
                                        :improvement="improvement"
                                        :index="index"
                                        :total="formatPrice(improvement.value,'$0,0')"
                                        @row:add="addRow"
                                        @row:delete="deleteRow"/>
            </div>
            <div class="analysis-row-wrapper">
                <analysis-row :show-buttons="false" :hide-background="true">
                    <div class="title" style="text-align: right">
                        Total Improvement Value:
                    </div>
                    <template #total>
                        <h3>{{ runningTotal }}</h3>
                    </template>
                </analysis-row>
            </div>
        </analysis-table>
    </div>
</template>

<script>
import AnalysisTable from './AnalysisTable.vue';
import AnalysisRow from './AnalysisRow.vue';
import ImprovementsMultiRow from './ImprovementsMultiRow.vue';
import FormatUtils from '@/utils/FormatUtils';
import PriceChangeCard from 'Common/PriceChangeCard.vue';
import {mapState, mapGetters, mapActions} from 'vuex';

export default {
    name: 'improvements-multi-table',
    mixins: [FormatUtils],
    components: {
        AnalysisTable,
        AnalysisRow,
        ImprovementsMultiRow,
        PriceChangeCard,
    },
    props: {
        improvements: {
            type: Array,
            required: true,
        },
        improvementsWithoutWorksheet: {
            type: Array,
            required: true,
        },
    },
    computed: {
        ...mapState('saleAnalysis', [
            'sale', 'property', 'analysis',
        ]),
        ...mapGetters('saleAnalysis', ['getTotalWorksheetVI']),
        runningTotal() {
            return this.formatPrice(this.analysis.totalVI, '$0,0');
        },
    },
    watch: {
        'improvements'(newValue, oldValue) {
            if (newValue.length === 0) {
                this.addRow(0);
            }
        },
    },
    methods: {
        ...mapActions('saleAnalysis',
            {
                deleteAnalysisRow: 'deleteRow',
                calculateAnalysedLV: 'calculateAnalysedLV',
            }),
        addRow(index) {
            const improvement = this.newImprovement();
            this.$emit('update:improvements',
                this.improvements.splice(index + 1, 0, improvement));

            return improvement;
        },
        deleteRow(index) {
            const improvement = this.improvements[index];
            this.$emit('update:improvements', this.improvements.splice(index, 1));

            if (improvement.saleAnalysisRowId) {
                this.deleteAnalysisRow(improvement);
            } else {
                this.calculateAnalysedLV();
            }
        },
        newImprovement() {
            return {
                saleAnalysisRowId: null,
                qpid: null,
                valRef: null,
                improvementId: null,
                size: null,
                description: null,
                rate: null,
                value: null,
            };
        },
        displayWorksheetTotal(index) {
            if (this.improvements.length === index + 1) {
                return this.formatPrice(this.getTotalWorksheetVI, '$0,0');
            }

            return null;
        },
    },
    mounted() {
        if (this.improvements.length === 0) {
            this.addRow(0);
        }
    },
};
</script>

<template>
    <analysis-row :index="index" v-on="$listeners" :show-buttons="false">
        <div class="col-2">
            <tooltip :text="errors.valRef">
                <table-input
                    type="text"
                    :disabled="true"
                    :value.sync="landFeature.valRef"
                    :invalid="errors.valRef"
                />
            </tooltip>
        </div>
        <div class="col-3">
            <div>
                <table-input
                    type="text"
                    :value.sync="landFeature.description"
                />
            </div>
        </div>
        <div class="col-2">
            <table-input
                :value.sync="landFeature.natureOfImprovements"
                type="text"
            />
        </div>
        <div class="col-1">
            <table-input
                :value.sync="landFeature.category"
                type="text"
            />
        </div>
        <div class="col-1">
            <div>
                <table-input
                    type="number"
                    min="0"
                    step="0.01"
                    :value.sync="landFeature.area"
                    @update:value="updateSize"
                />
            </div>
        </div>
        <div class="col-1">
            <div>
                <table-input
                    type="money"
                    min="0"
                    step="0.0001"
                    :value.sync="landFeature.rate"
                    @update:value="updateRate"
                />
            </div>
        </div>
        <div class="col-2">
            <div>
                <table-input
                    type="money"
                    min="0"
                    step="0.0001"
                    :value.sync="landFeature.value"
                    @update:value="updateValue"
                />
            </div>
        </div>
        <template #total>
            <h3 v-if="total">{{ total }}</h3>
        </template>
    </analysis-row>
</template>

<script>
import AnalysisRow from './AnalysisRow.vue';
import ClassificationDropdown from 'Common/form/ClassificationDropdown.vue';
import TableInput from 'Common/tables/TableInput.vue';
import TableSelect from 'Common/tables/TableSelect.vue';
import Tooltip from 'Common/Tooltip.vue';
import {mapState} from 'vuex';
import AnalysisUtils from '@/utils/AnalysisUtils';

export default {
    name: 'land-multi-row',
    components: {
        AnalysisRow,
        ClassificationDropdown,
        TableInput,
        TableSelect,
        Tooltip,
    },
    mixins: [AnalysisUtils],
    props: {
        landFeature: {
            type: Object,
            required: true,
        },
        index: {
            type: Number,
            required: true,
        },
        total: {
            required: false,
        },
    },
    computed: {
        errors() {
            return this.landFeature.error ? this.landFeature.error : {};
        },
        ...mapState('saleAnalysis', {
            classifications: 'ruralClassifications',
        }),
    },
    methods: {
        updateSize() {
            const {area, rate} = this.landFeature;

            if (rate) {
                this.landFeature.value = this.calculateValue(area, rate);
            }
        },
        updateRate() {
          const {area, rate} = this.landFeature;

            if (area) {
                this.landFeature.value = this.calculateValue(area, rate);
            }
        },
        updateValue() {
            const {area, rate, value} = this.landFeature;

            if (area && rate && value) {
                this.landFeature.rate = this.calculateRate(value, area)
            }
        },
        updateImprovement() {
            if (this.errors.improvementId) {
                this.errors.improvementId = null;
            }
        },
    },
};
</script>

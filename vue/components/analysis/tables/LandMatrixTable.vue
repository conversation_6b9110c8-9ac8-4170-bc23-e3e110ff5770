<template>
    <analysis-table :collapsable="true" :start-collapsed="!landMatrix || landMatrix.length === 0">
        <template #title>
            Land Matrix as Worksheet - <i>for reference only</i>
        </template>
        <template #title-right>
        </template>
        <template #columns>
            <div class="col-2">Contour</div>
            <div class="col-7">Land Cover</div>
            <div class="col-2">QV Land Cover</div>
            <div class="col-1">Area</div>
        </template>
        <template #total-column>Total Area</template>
        <div v-for="(land, index) in landMatrix" :key="index" class="analysis-row-wrapper">
            <land-matrix-row :land="land" :index="index" :total="displayTotal(index)"/>
        </div>
    </analysis-table>
</template>

<script>
import FormatUtils from '@/utils/FormatUtils';

import AnalysisTable from './AnalysisTable.vue';
import LandMatrixRow from '@/components/analysis/tables/LandMatrixRow.vue';

export default {
    name: 'land-matrix-table',
    mixins: [FormatUtils],
    components: {
        LandMatrixRow,
        AnalysisTable,
        FormatUtils,
    },
    props: {
        landMatrix: {
            type: Array,
            required: true,
        },
    },
    computed: {
        runningTotal() {
            const total = this.landMatrix.reduce((acc, o) => {
                return acc + o.itemSize;
            }, 0);
            return `${this.formatDecimal(total, 4)} ha`;
        },
    },
    methods: {
        displayTotal(index) {
            if (this.landMatrix.length === index + 1) {
                return this.runningTotal;
            }

            return null;
        },
    },
};
</script>

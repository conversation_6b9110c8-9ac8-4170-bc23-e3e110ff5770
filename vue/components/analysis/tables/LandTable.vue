<template>
    <analysis-table>
        <template #title>
            <slot name="title">Land</slot>
        </template>
        <template #title-right>
            <slot name="title-right"/>
            <div class="flag">
                <p>PD Has Irrigation:</p>
                <badge :class="{ gray: !hasIrrigation, red: hasIrrigation }">
                    {{ hasIrrigation ? 'YES' : 'NO' }}
                </badge>
            </div>
            <div class="flag" v-if="showMatchesMatrix">
                <p>Matches Land Matrix:</p>
                <badge :class="{ gray: matchesLandMatrix, red: !matchesLandMatrix }">
                    {{ matchesLandMatrix ? 'YES' : 'NO' }}
                </badge>
            </div>
            <div v-if="showPriceCard">
                <price-change-card title="Analysed Land Value"
                                   :current-price="totalAnalysedLV"
                                   :old-price="totalLV"
                                   :subheading="landValuePerHa"/>
            </div>
        </template>
        <template #columns>
            <div class="col-2">Description</div>
            <div class="col-2">Contour</div>
            <div class="col-2">Land Cover</div>
            <div class="col-2">Irrigation</div>
            <div class="col-1">Area</div>
            <div class="col-1">Rate</div>
            <div class="col-2">Value</div>
        </template>
        <template #total-column>Total LV</template>
        <div v-for="(land, index) in landFeatures" :key="index">
            <land-row v-if="!land.delete" :land="land" :index="index"
                      @row:add="addLandRow"
                      @row:delete="deleteLandRow"/>
        </div>
        <div class="analysis-row-wrapper">
            <analysis-row :show-buttons="false" class="site-value-row">
                <div class="col-2">
                    <h3 class="site-value-title">Site Value</h3>
                </div>
                <div class="col-5">&nbsp;</div>
                <div class="col-2">
                    <h3 class="site-value-text">Views</h3>
                </div>
                <div class="col-1">
                    <h3 class="site-value-text">Area</h3>
                </div>
                <div class="col-2">
                    <h3 class="site-value-text">Lump Sum</h3>
                </div>
            </analysis-row>

            <div v-for="(site, index) in sites" :key="index">
                <site-row v-if="!site.delete" :site="site" :index="index" @row:add="addSiteRow"
                          @row:delete="deleteSiteRow" :total="displayTotal(index)"/>
            </div>
        </div>
        <slot/>
    </analysis-table>
</template>

<script>
import AnalysisTable from './AnalysisTable.vue';
import AnalysisRow from './AnalysisRow.vue';
import LandRow from './LandRow.vue';
import SiteRow from './SiteRow.vue';
import TableInput from 'Common/tables/TableInput.vue';
import TableSelect from 'Common/tables/TableSelect.vue';
import FormatUtils from '@/utils/FormatUtils';
import WorksheetUtils from '@/utils/WorksheetUtils';
import PriceChangeCard from 'Common/PriceChangeCard.vue';
import Badge from 'Common/Badge.vue';
import {mapState} from 'vuex';

export default {
    name: 'land-table',
    mixins: [FormatUtils, WorksheetUtils],
    components: {
        Badge,
        PriceChangeCard,
        AnalysisTable,
        AnalysisRow,
        LandRow,
        TableInput,
        TableSelect,
        SiteRow,
    },
    props: {
        landFeatures: {
            type: Array,
            required: true,
        },
        landMatrix: {
            type: Array,
            required: false,
        },
        sites: {
            type: Array,
            required: true,
        },
        sale: {
            type: Object,
            required: true,
        },
        analysis: {
            type: Object,
            required: true,
        },
        primaryProperty: {
            type: Object,
            required: true,
        },
        showPriceCard: {
            required: false,
            default: true,
        },
        totalAnalysedLV: {
            required: false,
        },
        totalLV: {
            required: false,
        },
        totalLandArea: {
            required: false,
        },
        showMatchesMatrix: {
            required: false,
            default: true,
        },
    },
    computed: {
        ...mapState('saleAnalysis', {
            saleProperty: 'property',
        }),
        runningTotal() {
            return this.formatPrice(this.tableTotalLV, '$0,0');
        },
        tableTotalLV() {
            const landValue = this.landFeatures.reduce((acc, o) => {
                return acc + o.value;
            }, 0);

            const siteValue = this.sites.reduce((acc, o) => {
                return acc + o.value;
            }, 0);

            const totalLV = siteValue + landValue;

            if (this.analysis) {
                this.analysis.totalLV = totalLV;
                this.$emit('update:analysis', this.analysis);
            }

            return totalLV;
        },
        landValuePerHa() {
            return this.formatPrice(
                    this.saleProperty.landUse.landArea ? this.totalAnalysedLV /
                        this.saleProperty.landUse.landArea : null, '$0,0') +
                ' / ha';
        },
        matchesLandMatrix() {
            return this.landUseMatchesLandMatrix(this.landFeatures,
                this.landMatrix);
        },
        hasIrrigation() {
            return (this.analysis.hasIrrigation || this.primaryProperty.hasIrrigation)
                ? true
                : false;
        },
    },
    watch: {
        'landFeatures'(newValue, oldValue) {
            if (newValue.length === 0) {
                this.addLandRow(0);
            }
        },
        'sites'(newValue, oldValue) {
            if (newValue.length === 0) {
                this.addSiteRow(0);
            }
        },
    },
    methods: {
        addLandRow(index) {
            this.$emit('update:land-features',
                this.landFeatures.splice(index + 1, 0, this.newLandFeature()));
        },
        deleteLandRow(index) {
            const land = this.landFeatures[index];
            this.$emit('update:land-features', this.landFeatures.splice(index, 1));

            if (land.saleAnalysisRowId) {
                this.$store.dispatch('saleAnalysis/deleteRow', land);
            }
        },
        addSiteRow(index) {
            this.$emit('update:sites', this.sites.splice(index + 1, 0, this.newSite()));
        },
        deleteSiteRow(index) {
            const site = this.sites[index];
            this.$emit('update:sites', this.sites.splice(index, 1));

            if (site.saleAnalysisRowId) {
                this.$store.dispatch('saleAnalysis/deleteRow', site);
            }
        },
        newLandFeature() {
            return {
                saleAnalysisRowId: null,
                qpid: this.primaryProperty.qpid,
                valRef: this.primaryProperty.valRef,
                contourId: null,
                irrigationId: null,
                ruralUseId: null,
                size: null,
                description: null,
                rate: null,
                value: null,
            };
        },
        newSite() {
            return {
                saleAnalysisRowId: null,
                qpid: this.primaryProperty.qpid,
                valRef: this.primaryProperty.valRef,
                viewId: null,
                size: null,
                value: null,
            };
        },
        displayTotal(index) {
            if (this.sites.length === index + 1) {
                return this.runningTotal;
            }

            return null;
        },
    },
};
</script>

<style lang="scss" scoped>
.site-value-text {
    font-weight: bold;
    font-size: 1.25rem;
    color: var(--color-blue-900);
}

.site-value-title {
    font-weight: bold;
    font-size: 1.25rem;
}

.flag {
    display: flex;
    width: max-content;

    p {
        font-size: 1.2rem;
        font-weight: bold;
        font-style: italic;
        margin-top: auto;
        margin-bottom: auto;
        margin-right: 8px;
    }
}
</style>

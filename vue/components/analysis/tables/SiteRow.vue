<template>
    <analysis-row :index="index" class="site-value-row" v-on="$listeners">
        <template #total>
            <h3 v-if="total">{{ total }}</h3>
        </template>
        <div class="col-7">
        </div>
        <div class="col-2">
            <tooltip :text="errors.viewId">
                <table-select
                    :options="classifications.view || []"
                    :value.sync="site.viewId"
                    description-key="description"
                    :invalid="errors.viewId"
                    @update:value="updateView"
                />
            </tooltip>
        </div>
        <div class="col-1">
            <tooltip :text="errors.size">
                <table-input
                    type="number"
                    min="0"
                    step="0.01"
                    :value.sync="site.size"
                    :invalid="errors.size"
                    @update:value="updateSize"
                />
            </tooltip>
        </div>
        <div class="col-2">
            <table-input
                type="money"
                min="0"
                step="0.0001"
                :value.sync="site.value"
            />
        </div>
    </analysis-row>
</template>

<script>
import AnalysisRow from './AnalysisRow.vue';
import Tooltip from 'Common/Tooltip.vue';
import TableInput from 'Common/tables/TableInput.vue';
import TableSelect from 'Common/tables/TableSelect.vue';
import {mapState} from 'vuex';

export default {
    name: 'site-row',
    components: {
        AnalysisRow,
        TableInput,
        TableSelect,
        Tooltip,
    },
    props: {
        site: {
            type: Object,
            required: true,
        },
        index: {
            type: Number,
            required: true,
        },
        total: {
            required: false,
        },
    },
    computed: {
        errors() {
            return this.site.error ? this.site.error : {};
        },
        ...mapState('saleAnalysis', {
            classifications: 'ruralClassifications',
        }),
    },
    methods: {
        updateSize() {
            if (this.errors.size) {
                this.errors.size = null;
            }
        },
        updateView() {
            if (this.errors.viewId) {
                this.errors.viewId = null;
            }
        },
    },
};
</script>

<style lang="scss">
.site-value-text {
    font-weight: bold;
    font-size: 1.25rem;
    color: var(--color-blue-900);
}

.site-value-title {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--color-blue-900);
}
</style>

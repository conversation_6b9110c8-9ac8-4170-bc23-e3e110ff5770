<template>
    <div>
        <div v-for="(worksheet, index) in worksheets" :key="index">
            <land-table :analysis="analysis.land[worksheet]"
                        :land-features="analysis.land[worksheet].land"
                        :primary-property="analysis.land[worksheet].property"
                        :sale="sale"
                        :sites="analysis.land[worksheet].sites"
                        :show-price-card="index === 0"
                        :total-analysed-l-v="analysis.analysedLV"
                        :total-l-v="analysis.totalLV"
                        :total-land-area="analysis.totalLandArea"
                        :show-matches-matrix="false"
                        @update:analysis="calculateTotalLV">
                <template #title>
                    Worksheet: {{ worksheet }}
                </template>
                <template #title-right>
                    <div>
                        <button @click="openWorksheet(analysis.land[worksheet])"
                                class="sa-button mdl-button mdl-js-button mdl-js-ripple-effect">
                            Worksheet <i class="material-icons">call_made</i>
                        </button>
                    </div>
                </template>
                <div class="analysis-row-wrapper"
                     v-if="analysis.landWithoutWorksheet.length <= 0 && index + 1 === worksheets.length">
                    <analysis-row :show-buttons="false" :hide-background="true">
                        <div class="title" style="text-align: right">
                            Total Land Value:
                        </div>
                        <template #total>
                            <h3>{{ formatPrice(analysis.totalLV, '$0,0') }}</h3>
                        </template>
                    </analysis-row>
                </div>
            </land-table>
        </div>
        <analysis-table style="margin-top: 0" v-if="analysis.landWithoutWorksheet.length > 0">
            <template #title>
                <div class="title ">
                    Assessments without Rural Worksheets
                </div>
            </template>
            <template #title-right>
            </template>
            <template #columns>
                <div class="col-2">Val/Ref</div>
                <div class="col-3">Description</div>
                <div class="col-2">Nature of Improvements</div>
                <div class="col-1">Category</div>
                <div class="col-1">Area</div>
                <div class="col-1">Rate</div>
                <div class="col-2">Value</div>
            </template>
            <template #total-column>Total LV</template>
            <div v-for="(landFeature, index) in analysis.landWithoutWorksheet" :key="index"
                 class="analysis-row-wrapper">
                <land-multi-row v-if="!landFeature.delete"
                                :land-feature="landFeature"
                                :index="index"
                                :total="formatPrice(landFeature.value,'$0,0')"/>
            </div>
            <div class="analysis-row-wrapper">
                <analysis-row :show-buttons="false" :hide-background="true">
                    <div class="title" style="text-align: right">
                        Total Land Value:
                    </div>
                    <template #total>
                        <h3>{{ formatPrice(analysis.totalLV, '$0,0') }}</h3>
                    </template>
                </analysis-row>
            </div>
        </analysis-table>
    </div>
</template>

<script>
import AnalysisTable from './AnalysisTable.vue';
import AnalysisRow from './AnalysisRow.vue';
import LandRow from './LandRow.vue';
import LandMultiRow from './LandMultiRow.vue';
import LandTable from './LandTable.vue';
import SiteRow from './SiteRow.vue';
import TableInput from 'Common/tables/TableInput.vue';
import TableSelect from 'Common/tables/TableSelect.vue';
import FormatUtils from '@/utils/FormatUtils';
import WorksheetUtils from '@/utils/WorksheetUtils';
import PriceChangeCard from 'Common/PriceChangeCard.vue';
import Badge from 'Common/Badge.vue';
import {openUrlInNewTab} from '@/utils/QivsUtils';

export default {
    name: 'land-multi-table',
    mixins: [FormatUtils, WorksheetUtils],
    components: {
        Badge,
        PriceChangeCard,
        AnalysisTable,
        AnalysisRow,
        LandRow,
        LandMultiRow,
        LandTable,
        TableInput,
        TableSelect,
        SiteRow,
    },
    props: {
        sale: {
            type: Object,
            required: true,
        },
        analysis: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            linkedProperties: this.analysis.linkedProperties,
        };
    },
    computed: {
        worksheets() {
            return Object.keys(this.analysis.land);
        },
    },
    methods: {
        calculateTotalLV() {
            const rows = Object.values(this.analysis.land).reduce((rows, worksheet) => {
                return [...rows, ...worksheet.land, ...worksheet.sites];
            }, []).concat(this.analysis.landWithoutWorksheet);

            const totalLV = rows.reduce((acc, row) => {
                return acc + row.value;
            }, 0);

            if (this.analysis) {
                this.analysis.totalLV = totalLV;
                this.$emit('update:analysis', this.analysis);
            }

            return totalLV;
        },
        openWorksheet(worksheet) {
            const route = this.$router.resolve(
                {name: 'rural-worksheet', params: {id: worksheet.property.qpid}});
            openUrlInNewTab(route.href);
        },
    },
    mounted() {},
};
</script>

<style lang="scss" scoped>
.site-value-text {
    font-weight: bold;
    font-size: 1.25rem;
    color: var(--color-blue-900);
}

.site-value-title {
    font-weight: bold;
    font-size: 1.25rem;
}

.flag {
    display: flex;
    width: max-content;

    p {
        font-size: 1.3rem;
        font-weight: bold;
        margin-top: auto;
        margin-bottom: auto;
        margin-right: 8px;
    }
}
</style>

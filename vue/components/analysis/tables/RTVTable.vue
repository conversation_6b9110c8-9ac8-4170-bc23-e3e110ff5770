<template>
    <analysis-table class="rtv-table" :collapsable="true">
        <template #title>
            QV (RTV) Values
        </template>
        <template #columns>
            <div class="col-12">RTV as at 01/12/2021</div>
        </template>
        <analysis-row :show-buttons="false">
            <div class="col-3 flex-row">
                <h3 class="col-7">RTV:</h3>
                <table-input disabled :value="4800000" type="money"></table-input>
            </div>
            <div class="col-3 flex-row">
                <h3 class="col-7">RTV/Ha:</h3>
                <table-input disabled :value="36923" type="money"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTV/NSP:
                </h3>
                <table-input disabled :value="1.02" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTV/RCV:
                </h3>
                <table-input disabled :value="1.02" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTV/CV:
                </h3>
                <table-input disabled :value="1.09" type="number" class="col-4"></table-input>
            </div>
        </analysis-row>
        <analysis-row :show-buttons="false">
            <div class="col-3 flex-row">
                <h3 class="col-7">Land RTV (RTLV):</h3>
                <table-input disabled :value="4100000" type="money"></table-input>
            </div>
            <div class="col-3 flex-row">
                <h3 class="col-7">RTLV/Ha:</h3>
                <table-input disabled :value="36923" type="money"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTLV/ALSP:
                </h3>
                <table-input disabled :value="1.02" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTV/RLV:
                </h3>
                <table-input disabled :value="1.02" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTLV/LV:
                </h3>
                <table-input disabled :value="1.09" type="number" class="col-4"></table-input>
            </div>
        </analysis-row>
        <analysis-row :show-buttons="false">
            <div class="col-3 flex-row">
                <h3 class="col-7">Difference (RTVI):</h3>
                <table-input disabled :value="700000" type="money"></table-input>
            </div>
            <div class="col-3 flex-row">
                <h3 class="col-7">RTV/Prodn:</h3>
                <table-input disabled :value="36923" type="money"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTVI/AVI:
                </h3>
                <table-input disabled :value="1.02" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTVI/RVI:
                </h3>
                <table-input disabled :value="1.02" type="number" class="col-4"></table-input>
            </div>
            <div class="col-2 flex-row">
                <h3 class="col-8">
                    RTVI/VI:
                </h3>
                <table-input disabled :value="1.09" type="number" class="col-4"></table-input>
            </div>
        </analysis-row>

    </analysis-table>
</template>

<script>
import AnalysisTable from './AnalysisTable.vue';
import AnalysisRow from './AnalysisRow.vue';
import FormatUtils from '@/utils/FormatUtils';
import TableInput from 'Common/tables/TableInput.vue';

export default {
    name: 'rtv-table',
    mixins: [FormatUtils],
    components: {
        AnalysisTable,
        AnalysisRow,
        TableInput,
    },
    props: {
        rtv: {
            type: Object,
            required: true,
        },
        sale: {
            type: Object,
            required: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.rtv-table ::v-deep .analysis-row-header {
    background-color: var(--color-darkorange-500);
}

.rtv-table {
    .flex-row {
        align-items: center;
    }

    h3 {
        font-size: 1.25rem;
    }
}
</style>

<template>
    <transition name="fade">
        <div key="1" v-if="this.loading && !this.error" class="loading">
            <div class="spinner"></div>
            <div class="message">{{ loadingMessage }}</div>
        </div>
        <div key="2" v-else-if="this.error" class="error">
            <h1 class="error-message">
                {{ this.error }}
            </h1>
        </div>
        <div key="3" v-else class="saleAnalysis-wrapper">
            <sa-save-modal :errors="saveErrors"
                           :warnings="warnings"
                           :saving="saving"
                           :open="saveModalOpen"
                           :type="saveModalType"
                           @modal:callback="saveModalCallback !== null ? saveModalCallback() : toggleModal();"
                           @modal:toggle="toggleModal"/>
            <sa-header @verify-and-close="verifyAndClose"/>
            <div class="salesAnalysis-form">
                <div class="sa-warning"
                     v-if="!isMultiSale & property.worksheetLandValue !== property.ratingValuation.landValue">
                    <i class="material-icons">warning_amber</i>
                    <p>Land value on rural worksheet does not match the sale land value so land rows
                        will need manual adjustment.</p>
                </div>
                <div class="sa-warning"
                     v-if="isMultiSale & analysis.improvementsWithoutWorksheets.length > 0">
                    <i class="material-icons">warning_amber</i>
                    <p>Some data is from the current property data and it may not reflect the
                        snapshot of the property at the time of the sale.</p>
                </div>
                <sa-banner :sale="sale" :primaryProperty="property" :analysis="analysis"/>
                <sa-property-panel :property="property" :sale="sale"/>
                <sa-sale-panel :property="property" :sale="sale" :analysis="analysis"/>
                <sa-linked-property-panel v-if="analysis.saleType === 'M'" :analysis="analysis"
                                          @openMap="openMap"/>
                <alert-modal v-if="isRefreshAnalysis" :caution='true' >
                    <h1>{{refreshSaleAnalysisHeader}}</h1>
                    <p >{{ refreshSaleAnalysisMessage }}</p>
                    <template #buttons>
                        <div class="alertButtons">
                            <button
                                class="mdl-button mdl-button--mini lefty"
                                @click="toggleRefreshAnalysisModal"
                            >
                                Cancel
                            </button>
                            <button
                                class="mdl-button mdl-button--mini"
                                @click="refreshAnalysis"
                            >
                                Confirm
                            </button>
                        </div>
                    </template>
                </alert-modal>

                <div class="sa-summary" v-if="!isMultiSale">
                    <div class="sa-text-heading">
                        <h3>Worksheet Comment</h3>
                        <button
                            @click="openWorksheet"
                            class="sa-button mdl-button mdl-js-button mdl-js-ripple-effect">
                            Worksheet <i class="material-icons">call_made</i>
                        </button>
                        <button
                            @click="openMap"
                            class="sa-button mdl-button mdl-js-button mdl-js-ripple-effect">
                            Map <i class="material-icons">call_made</i>
                        </button>
                    </div>
                    <textarea type="text" class="sa-rounded"
                              disabled>{{ analysis.worksheetComment }}</textarea>
                </div>
                <div class="sa-summary">
                    <div class="sa-text-heading">
                        <h3>Sales Analysis Comment</h3>
                        <badge>Sale Remarks: {{ sale.remarks }}</badge>
                    </div>
                    <textarea type="text" class="sa-rounded" v-model="analysis.analysisComment"
                              placeholder="Add a summary..."></textarea>
                </div>
                <container>
                    <template #title>
                        Analysis
                    </template>
                    <template #title-right>
                        <button
                            @click="toggleRefreshAnalysisModal"
                            class="sa-button mdl-button mdl-js-button mdl-js-ripple-effect">
                            Refresh Sales Analysis
                        </button>
                    </template>

                    <div class="sa-classifications">
                        <div class="col-4 sa-classifications-categories">
                            <div>
                                <label>
                                    <span class="label">QV Category</span>
                                </label>
                                <vue-multiselect
                                    v-model="analysis.qvCategory"
                                    :options="picklists.qvCategoryTypes || []"
                                    :searchable="true"
                                    track-by="code"
                                    label="description"
                                />
                            </div>

                            <div>
                                <label>
                                    <span class="label">Zone</span>
                                    <table-input type="text" style="height: 40px"
                                                 :value="property.landUse.landZone"
                                                 disabled/>
                                </label>
                            </div>
                        </div>
                        <div class="col-6 sa-classifications-qualities flex-grid-2">
                            <div class="flex-col">
                                <label>
                                    <span class="label">Grouping</span>
                                </label>
                                <vue-multiselect
                                    v-model="analysis.grouping"
                                    :options="picklists.propertyGroupingTypes || []"
                                    :searchable="true"
                                    track-by="code"
                                    label="description"
                                />
                            </div>
                            <div class="flex-col">
                                <label>
                                    <span class="label">Quality Rating</span>
                                </label>
                                <vue-multiselect
                                    v-model="analysis.ratingQuality"
                                    :options="picklists.qualityRatingTypes || []"
                                    :searchable="true"
                                    track-by="id"
                                    label="description"
                                />
                            </div>
                            <div class="flex-col">
                                <label>
                                    <span class="label">Nutrient Score</span>
                                </label>
                                <vue-multiselect
                                    v-model="analysis.nutrientQuality"
                                    :options="picklists.qualityRatingTypes || []"
                                    :searchable="true"
                                    track-by="code"
                                    label="description"
                                />
                            </div>
                            <div class="flex-col">
                                <label class="flex-col">
                                    <span class="label">Quality of Water</span>
                                </label>
                                <vue-multiselect
                                    v-model="analysis.waterQuality"
                                    :options="picklists.qualityRatingTypes || []"
                                    :searchable="true"
                                    track-by="code"
                                    label="description"
                                />
                            </div>
                            <label class="flex-col">
                                <span class="label">Area (Ha)</span>
                                <table-input type="text" style="height: 40px"
                                             :value="property.landUse.landArea"
                                             disabled/>
                            </label>
                            <label class="flex-col">
                                <span class="label">Production</span>
                                <table-input type="text" style="height: 40px"
                                             :value="property.landUse.production"
                                             disabled/>
                            </label>
                        </div>
                        <div class="col-4" style="position: relative;">
                            <div style="position: absolute; bottom: 0; right: 0;">
                                <price-change-card title="Net Sale Price"
                                                   :current-price="sale.price.net"
                                                   :old-price="property.ratingValuation.capitalValue"/>
                            </div>
                        </div>
                    </div>
                    <div class="sa-improvements" v-if="!isMultiSale">
                        <improvements-table :improvements="analysis.improvements"/>
                    </div>
                    <div class="sa-improvements" v-else>
                        <div class="title " style="position: relative; top: -2rem;">
                            Improvements
                        </div>
                        <improvements-multi-table :improvements="analysis.improvements"
                                                  :improvements-without-worksheet="analysis.improvementsWithoutWorksheets"/>
                    </div>
                    <div class="sa-land-features" v-if="!isMultiSale">
                        <land-table :primary-property="property"
                                    :land-features="analysis.land"
                                    :sites="analysis.sites"
                                    :analysis="analysis"
                                    :sale="sale"
                                    :land-matrix="landMatrix"
                                    :total-analysed-l-v="analysis.analysedLV"
                                    :total-l-v="analysis.totalLV"
                                    :total-land-area="analysis.totalLandArea">
                            <template #title-right>
                                <div>
                                    <button @click="openWorksheet"
                                            class="sa-button mdl-button mdl-js-button mdl-js-ripple-effect">
                                        Worksheet <i class="material-icons">call_made</i>
                                    </button>
                                </div>
                            </template>
                        </land-table>
                    </div>
                    <div class="sa-land-features" v-else>
                        <div class="title" style="position: absolute">
                            Land Values
                        </div>
                        <land-multi-table :analysis="analysis"
                                          :sale="sale">
                        </land-multi-table>

                    </div>
                    <div v-if="!isMultiSale" class="sa-land-matrix">
                        <land-matrix-table :land-matrix="landMatrix"/>
                    </div>
                    <!-- Disabled until RTV implemented
                    <div class="sa-rtv">
                        <RTVTable :rtv="analysis.revaluation" :sale="sale"/>
                    </div>
                    -->
                    <div class="sa-revision">
                        <revision-table :primary-property="property"
                                        :revision-values="analysis.revision" :sale="sale"
                                        :analysis="analysis"/>
                    </div>
                </container>
            </div>

            <sa-footer
                class="sa-footer"
                :primary-property="property"
                :sale="sale"
                :analysis="analysis"
                @print="verifyAndPrint()"
                @delete="deleteAnalysis()"
                @restore="restoreAnalysis()"
            />
        </div>
    </transition>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex';

import SaBanner from './SaleAnalysisBanner.vue';
import SaPropertyPanel from './SaleAnalysisPropertyPanel.vue';
import SaLinkedPropertyPanel from './SaleAnalysisLinkedPropertyPanel.vue';
import SaSalePanel from './SaleAnalysisSalePanel.vue';
import SaHeader from './SaleAnalysisHeader.vue';
import SaFooter from './SaleAnalysisFooter.vue';
import SaSaveModal from './SaleAnalysisSaveModal.vue';

import LandTable from './tables/LandTable.vue';
import LandMultiTable from './tables/LandMultiTable.vue';
import ImprovementsTable from './tables/ImprovementsTable.vue';
import ImprovementsMultiTable from './tables/ImprovementsMultiTable.vue';
import RTVTable from './tables/RTVTable.vue';
import RevisionTable from './tables/RevisionTable.vue';
import LandMatrixTable from './tables/LandMatrixTable.vue';

import Container from 'Common/Container.vue';
import ClassificationDropdown from 'Common/form/ClassificationDropdown.vue';
import PriceChangeCard from 'Common/PriceChangeCard.vue';
import Badge from 'Common/Badge.vue';
import TableInput from 'Common/tables/TableInput.vue';
import AlertModal from '../common/modal/AlertModal.vue';
import {openQivsInNewTab, openUrlInNewTab, openBase64DataInNewTab} from '@/utils/QivsUtils';

export default {
    components: {
        SaFooter,
        RevisionTable,
        LandMatrixTable,
        ClassificationDropdown,
        Container,
        SaSalePanel,
        SaPropertyPanel,
        SaBanner,
        SaHeader,
        SaSaveModal,
        PriceChangeCard,
        ImprovementsTable,
        ImprovementsMultiTable,
        LandTable,
        LandMultiTable,
        Badge,
        RTVTable,
        TableInput,
        SaLinkedPropertyPanel,
        'vue-multiselect': () => import(/* webpackChunkName: "vue-multiselect" */ 'vue-multiselect'),
        AlertModal,
    },
    inject: ['saleData'],
    data() {
        return {
            saleId: null,
            loading: true,
            loadingMessage: null,
            saving: false,
            warnings: [],
            saveModalCallback: null,
            saveModalOpen: false,
            saveModalType: 'error',
            isRefreshAnalysis: false,
            refreshSaleAnalysisHeader :'Refresh Sales Analysis',
            refreshSaleAnalysisMessage : 'This will overwrite the sales analysis with the current sale and worksheet data from linked property/ies. Are you sure?'
        };
    },
    computed: {
        ...mapState('saleAnalysis', {
            sale: 'sale',
            property: 'property',
            analysis: 'analysis',
            error: 'error',
            saveErrors: 'saveErrors',
            picklists: 'picklistValues',
            classifications: 'ruralClassifications',
            landMatrix: 'landMatrix',
            pdfFileStream: 'pdfFileStream',
        }),
        ...mapGetters('saleAnalysis', [
            'analysisHasChanged',
        ]),
        ...mapState('userData', {
            userData: 'userData',
        }),
        isMultiSale() {
            return this.analysis.isMulti;
        },
    },
    methods: {
        ...mapActions('saleAnalysis', [
            'loadSaleAnalysis',
            'loadPickListValues',
            'refreshSaleAnalysis',
            'updateSaleAnalysis']),
        async loadAnalysis() {
            this.loading = true;

            await Promise.all([
                this.loadPickListValues(),
                this.loadSaleAnalysis(this.saleId),
            ]);

            this.loading = false;
            this.loadingMessage = null;
        },
        openWorksheet() {
            const route = this.$router.resolve(
                {name: 'rural-worksheet', params: {id: this.analysis.qpid}});
            openUrlInNewTab(route.href);
        },
        openMap() {
            const route = this.$router.resolve({
                name: 'qv-map',
                params: {
                    lat: this.analysis.coordinates.lat,
                    lng: this.analysis.coordinates.lng,
                    qpid: this.analysis.qpid,
                },
            });

            openUrlInNewTab(route.href);
        },
        async doPrint() {
            this.loading = true;
            this.loadingMessage = 'Preparing PDF...';

            await this.$store.dispatch('saleAnalysis/generateRuralSalesPdfReport', this.saleId);

            this.loading = false;
            this.loadingMessage = null;

            if (this.pdfFileStream) {
                openBase64DataInNewTab(this.pdfFileStream, 'application/pdf');
            }
        },
        toggleRefreshAnalysisModal() {
            this.isRefreshAnalysis = !this.isRefreshAnalysis;
        },
        async refreshAnalysis() {
            this.loading = true;
            this.loadingMessage = 'Refreshing sale analysis...';

            await this.refreshSaleAnalysis(this.saleId);

            this.loading = false;
            this.loadingMessage = null;
            this.toggleRefreshAnalysisModal();
        },
        toggleModal() {
            this.saveModalOpen = !this.saveModalOpen;
        },
        showWarnings(callback) {
            this.saveModalCallback = callback;
            this.saveModalOpen = true;
            this.saveModalType = 'warning';
        },
        showSaving() {
            this.saveModalCallback = null;
            this.saveModalOpen = true;
            this.saveModalType = 'error';
        },
        verify() {
            this.warnings = [];
            if (Math.abs(
                    (this.analysis.totalVI + this.analysis.totalLV) - this.sale.price.net) >
                100) {
                this.warnings.push('The current difference exceeds $100.');
                return false;
            }
            return true;
        },

        async verifyAndPrint() {
            const valid = this.verify();
            if (!valid) {
                return this.showWarnings(() => {
                    this.toggleModal();
                    return this.saveAndPrint();
                });
            } else {
                return this.saveAndPrint();
            }
        },

        async verifyAndClose() {
            const valid = this.verify();
            if (!valid) {
                this.showWarnings(this.saveAnalysis);
            } else {
                await this.saveAnalysis();
            }
        },

        async saveAnalysis(closeAfter = true) {
            this.saving = true;
            this.showSaving();
            this.analysis.analysedBy = this.userData.userFullName;
            this.analysis.analysisKind = 'Verified';
            await this.updateSaleAnalysis();

            if (!this.saveErrors) {
                this.toggleModal();
                if (closeAfter) {
                    window.close();
                }
            }
            this.saving = false;
            return !this.saveErrors;
        },
        async saveAndPrint() {
            if (this.analysisHasChanged) {
                const success = await this.saveAnalysis(false);

                if (!success) {
                    return;
                }
            } else {
                await this.updateSaleAnalysis(true);

                if (this.saveErrors && this.saveErrors.length > 0) {
                    return this.showSaving();
                }
            }

            await this.doPrint();
            await this.loadAnalysis();
        },

        async deleteAnalysis() {
            await this.$store.dispatch('saleAnalysis/deleteAnalysis', this.saleId);
            window.close();
        },
        async restoreAnalysis() {
            this.loading = true;
            this.loadingMessage = 'Restoring Analysis';

            await this.loadSaleAnalysis(this.saleId);

            this.loading = false;
            this.loadingMessage = null;
        }
    },
    mounted() {
        this.saleId = this.saleData.saleId;
        this.loadAnalysis();
    },
};
</script>

<style lang="scss" scoped>

.fade-enter-active,
.fade-leave-active {
    transition: opacity .5s
}

.fade-enter,
.fade-leave-to {
    opacity: 0
}

.loading {
    position: fixed;
    width: 100vw;
    height: 100vh;

    .message {
        position: absolute;
        top: 53%;
        left: 0;
        width: 100%;
        height: 100%;
        color: var(--color-blue-700);
        z-index: 100000;
        text-align: center;
        font-weight: bold;
        font-size: 1.2rem;
    }
}

.sa-summary {
    margin-bottom: 2rem;

    h3 {
        margin-top: auto;
        font-size: 1.5rem;
        font-weight: 600;
    }

    textarea {
        border: .1rem solid #fff;
    }
}

.error {
    width: 100%;
    height: 100vh;

    display: flex;
    align-items: center;
    justify-content: center;

    .error-message {
        font-size: 1.4rem;
        color: var(--color-red-600);
        padding: 1rem 4rem;
    }
}

.label {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--color-blue-600);
    height: 2rem;
    display: block;
}

.sa-classifications {
    display: flex;
    flex-direction: row;
    gap: 1.5rem;
}

.sa-classifications-categories {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    div {
        max-width: 35rem;
    }
}

.sa-classifications-qualities {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.sa-pricechange {
    position: relative;
    height: 100%;

    .sa-pricechange-badge {
        position: absolute;
        width: 100%;
        bottom: 0;
    }
}

textarea:disabled {
    color: var(--color-blue-700);
    background-color: var(--color-lightblue-300);
    box-shadow: 0 0 0 1px var(--color-lightblue-500);
    border: none;
}

.flex-grid-2 {
    display: flex;
    justify-content: space-between;

    .flex-col {
        width: 49%;
    }
}
</style>

<style lang="scss">
.sa-text-heading {
    display: flex;
    flex-direction: row;
    padding-bottom: 0.75rem;

    h3 {
        color: var(--color-darkblue-500);
        flex-grow: 1;
    }
}

.sa-badge {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-lightblue-900);
    line-height: 1.9;
    text-align: center;
    background-color: var(--color-lightblue-300);
    border: .1rem solid var(--color-lightblue-400);
    border-radius: .3rem;
    pointer-events: none;
    padding: 0.25rem 1rem;
    margin: 0.5rem 0;
}

.sa-button {
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.9;

    height: max-content;
    width: max-content;
    padding: 0.25rem 1rem;

    border: none;
    border-radius: .3rem;
    background-color: var(--color-blue-700);
    color: white;

    overflow: visible;
    -webkit-appearance: none;

    &:hover {
        background-color: var(--color-blue-500);
    }

    &:active, &:focus:not(:active) {
        background-color: var(--color-blue-300);
    }

    > i {
        font-size: 1.2rem;
        margin-bottom: 2px;
    }
}

.sa-rounded {
    border-radius: 5px;
}

.sa-warning {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--color-red-900);
    background-color: var(--color-red-0);
    padding: 5px;
    border-radius: 0.25rem;
    font-size: small;
    margin-bottom: 1rem;

    p {
        font-weight: 600;
    }
}
</style>

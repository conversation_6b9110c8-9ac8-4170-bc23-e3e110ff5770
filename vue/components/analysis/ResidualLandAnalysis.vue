<template>
    <div>
        <div id="rsaMainBuildingsTable" class="salesAnalysis-table advSearch-row sa-mainBuildings ">
            <h3>Main Buildings</h3>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-th sa-description">
                    <span>Description</span>
                </div>
                <div class="salesAnalysis-th sa-area">
                    <span>Area</span>
                </div>
                <div class="salesAnalysis-th sa-rate">
                    <span>Rate /m<sup>2</sup></span>
                </div>
                <div class="salesAnalysis-th sa-value">
                    <span>Analysed Value</span>
                </div>
                <div class="salesAnalysis-th sa-runnintgTotal">
                    <span></span>
                </div>
                <div class="salesAnalysis-th sa-addRemove"></div>
            </div>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-td sa-description">
                    <span>
                        <input type="text" v-model="analysis.mainBuildingAnalysis.description" />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-area">
                    <span>
                        <input
                            type="text"
                            v-model="analysis.mainBuildingAnalysis.areaInSquareMeter"
                            @change="calculateMainBuildingAnalysedValue()"
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-rate">
                    <span>
                        <input
                            tabindex="-1"
                            type="text"
                            :value="
                                formatDollarValue(analysis.mainBuildingAnalysis.pricePerSquareMeter)
                            "
                            @change="
                                analyseMainBuildingChanges({
                                    pricePerSquareMeter: $event.target.value
                                })
                            "
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-value">
                    <span>
                        <input
                            tabindex="-1"
                            type="text"
                            :value="formatDollarValue(analysis.mainBuildingAnalysis.price)"
                            @change="
                                analyseMainBuildingChanges({
                                    price: $event.target.value
                                })
                            "
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-runnintgTotal">
                    <span v-html="formatDollarValue(mainBuildingsTotal) || '$0'"></span>
                </div>
                <div class="salesAnalysis-td sa-addRemove"></div>
            </div>
        </div>

        <div
            id="rsaOtherBuildingsTable"
            class="salesAnalysis-table advSearch-row sa-otherBuildings"
        >
            <h3>Other Buildings</h3>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                <div class="salesAnalysis-th sa-rate">
                    <span>Rate /m<sup>2</sup></span>
                </div>
                <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                <div class="salesAnalysis-th sa-addRemove"></div>
            </div>
            <div
                class="salesAnalysis-row"
                v-for="(otherBuilding, key) in analysis.otherBuildingsAnalysis"
                :key="key"
            >
                <div
                    class="salesAnalysis-td sa-description"
                    v-bind:class="{ calculated: otherBuilding.calculated }"
                >
                    <span>
                        <input type="text" tabindex="-1" v-model="otherBuilding.description" />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-area">
                    <span
                        ><input
                            type="text"
                            v-model.number="otherBuilding.areaInSquareMeter"
                            @change="calculateOtherBuildingsAnalysedValue()"
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-rate">
                    <span
                        ><input
                            type="text"
                            :value="formatDollarValue(otherBuilding.pricePerSquareMeter)"
                            @change="
                                analyseOtherBuildingsChanges(key, {
                                    pricePerSquareMeter: $event.target.value
                                })
                            "
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-value">
                    <span>
                        <input
                            type="text"
                            :value="formatDollarValue(otherBuilding.price)"
                            @change="
                                analyseOtherBuildingsChanges(key, {
                                    price: $event.target.value
                                })
                            "
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-runnintgTotal">
                    <span
                        v-if="key == analysis.otherBuildingsAnalysis.length - 1"
                        v-html="formatDollarValue(otherBuildingsTotal) || '$0'"
                    ></span>
                </div>
                <div class="salesAnalysis-td sa-addRemove">
                    <i
                        v-bind:data-index="key"
                        v-bind:row-type="'otherBuildingsAnalysis'"
                        class="saRow-add rsAddOtherBuildingsRow material-icons"
                        @click="addRow"
                    >
                        &#xE147;
                    </i>
                    <i
                        v-bind:data-index="key"
                        v-bind:row-type="'otherBuildingsAnalysis'"
                        v-if="key > 0 && !otherBuilding.calculated"
                        class="saRow-remove rsRemoveOtherBuildingsRow material-icons"
                        @click="removeRow"
                    >
                        &#xE15C;
                    </i>
                </div>
            </div>
        </div>

        <div id="rsaOtherImprovementsTable" class="salesAnalysis-table advSearch-row sa-oli">
            <h3>Other Improvements</h3>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                <div class="salesAnalysis-th sa-rate">
                    <span>Rate /m<sup>2</sup></span>
                </div>
                <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                <div class="salesAnalysis-th sa-addRemove"></div>
            </div>
            <div
                class="salesAnalysis-row"
                v-for="(otherImprovement, key) in analysis.otherImprovementsAnalysis"
                :key="key"
            >
                <div
                    class="salesAnalysis-td sa-description"
                    v-bind:class="{ calculated: otherImprovement.calculated }"
                >
                    <span>
                        <input type="text" tabindex="-1" v-model="otherImprovement.description" />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-area">
                    <span>
                        <input
                            type="text"
                            v-model.number="otherImprovement.areaInSquareMeter"
                            @change="calculateOtherImprovementsAnalysedValue()"
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-rate">
                    <span>
                        <input
                            type="text"
                            :value="formatDollarValue(otherImprovement.pricePerSquareMeter)"
                            @change="
                                analyseOtherImprovementsChanges(key, {
                                    pricePerSquareMeter: $event.target.value
                                })
                            "
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-value">
                    <span>
                        <input
                            type="text"
                            :value="formatDollarValue(otherImprovement.price)"
                            @change="
                                analyseOtherImprovementsChanges(key, {
                                    price: $event.target.value
                                })
                            "
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-runnintgTotal">
                    <span
                        v-if="key == analysis.otherImprovementsAnalysis.length - 1"
                        v-html="formatDollarValue(otherImprovementsTotal) || '$0'"
                    ></span>
                </div>
                <div class="salesAnalysis-td sa-addRemove">
                    <i
                        v-bind:data-index="key"
                        v-bind:row-type="'otherImprovementsAnalysis'"
                        class="saRow-add rsAddOtherImprovementsRow material-icons"
                        @click="addRow"
                    >
                        &#xE147;
                    </i>
                    <i
                        v-bind:data-index="key"
                        v-bind:row-type="'otherImprovementsAnalysis'"
                        v-if="key > 0 && !otherImprovement.calculated"
                        class="saRow-remove rsRemoveOtherImprovementsRow material-icons"
                        @click="removeRow"
                    >
                        &#xE15C;
                    </i>
                </div>
            </div>
        </div>

        <div id="rsaLandTable" class="salesAnalysis-table advSearch-row sa-land">
            <h3>Land</h3>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                <div class="salesAnalysis-th sa-rate">
                    <span>Rate /m<sup>2</sup></span>
                </div>
                <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                <div class="salesAnalysis-th sa-addRemove"></div>
            </div>
            <div class="salesAnalysis-row">
                <div class="salesAnalysis-td sa-description">
                    <span><input type="text" v-model="analysis.landAnalysis.description"/></span>
                </div>
                <div class="salesAnalysis-td sa-area">
                    <span>
                        <input
                            type="text"
                            v-model="analysis.landAnalysis.areaInSquareMeter"
                            @change="calculateLandAnalysedValue()"
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-rate calculated">
                    <span>
                        <input
                            type="text"
                            :value="formatDollarValue(analysis.landAnalysis.pricePerSquareMeter)"
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-value calculated">
                    <span>
                        <input
                            type="text"
                            :value="formatDollarValue(analysis.landAnalysis.price)"
                        />
                    </span>
                </div>
                <div class="salesAnalysis-td sa-runnintgTotal">
                    <span v-html="formatDollarValue(analysis.landAnalysis.price) || '$0'"> </span>
                </div>
                <div class="salesAnalysis-td sa-addRemove"></div>
            </div>
        </div>
    </div>
</template>

<script>
import numeral from 'numeral';
import propertyUtils from '../../utils/PropertyUtils';
import formatUtils from '../../utils/FormatUtils';
import commonUtils from '../../utils/CommonUtils';

export default {
    mixins: [propertyUtils, formatUtils, commonUtils],
    props: ['analysis'],
    data: function() {
        return {};
    },
    computed: {
        mainBuildingsTotal: {
            get: function() {
                return this.analysis.mainBuildingAnalysis.price;
            }
        },
        otherBuildingsTotal: {
            get: function() {
                const sum = this.analysis.otherBuildingsAnalysis.reduce((sum, obj) => {
                    const price = parseInt(obj.price);
                    return sum + (price ? price : 0);
                }, 0);

                this.analysis.otherBuildingsValueTotal = sum;
                return sum;
            }
        },
        otherImprovementsTotal: {
            get: function() {
                const sum = this.analysis.otherImprovementsAnalysis.reduce((sum, obj) => {
                    const price = parseInt(obj.price);
                    return sum + (price ? price : 0);
                }, 0);

                this.analysis.otherImprovementsValueTotal = sum;
                return sum;
            }
        }
    },
    methods: {
        formatDollarValue: function(val) {
            val = val ? '' + Number(('' + val).replace(/[^0-9\.-]+/g, '')) : '';
            val = val ? this.formatPrice(val, '$0,0') : null;
            return val;
        },
        getNewAnalysisObject: function() {
            return {
                description: '',
                areaInSquareMeter: '',
                pricePerSquareMeter: '',
                price: ''
            };
        },
        calculateMainBuildingRateM2: function() {
            const priceArea = this.analysis.mainBuildingAnalysis;

            if (priceArea.areaInSquareMeter && priceArea.price) {
                priceArea.pricePerSquareMeter = Math.round(
                    priceArea.price / priceArea.areaInSquareMeter
                );
            }
        },
        calculateMainBuildingAnalysedValue: function() {
            const priceArea = this.analysis.mainBuildingAnalysis;

            if (priceArea.areaInSquareMeter && priceArea.pricePerSquareMeter) {
                priceArea.price = Math.round(
                    priceArea.areaInSquareMeter * priceArea.pricePerSquareMeter
                );
            }
            this.calculateOtherImprovementsAnalysedValue();
        },
        calculateOtherBuildingsRateM2: function() {
            this.analysis.otherBuildingsAnalysis.forEach(priceArea => {
                if (typeof priceArea.areaInSquareMeter === 'number') {
                    priceArea.pricePerSquareMeter = priceArea.price/priceArea.areaInSquareMeter;
                }
            });
        },
        calculateOtherBuildingsAnalysedValue: function() {
            this.analysis.otherBuildingsAnalysis.forEach(priceArea => {
                if (priceArea.areaInSquareMeter && priceArea.pricePerSquareMeter) {
                    priceArea.price = Math.round(
                        priceArea.areaInSquareMeter * priceArea.pricePerSquareMeter
                    );
                }
            });

            this.calculateOtherImprovementsAnalysedValue();
        },
        calculateOtherImprovementsRateM2: function() {
            this.analysis.otherImprovementsAnalysis.forEach(priceArea => {
                if (typeof priceArea.areaInSquareMeter === 'number') {
                    priceArea.pricePerSquareMeter = priceArea.price/priceArea.areaInSquareMeter;
                }
            })
        },
        calculateOtherImprovementsAnalysedValue: function() {
            this.analysis.otherImprovementsAnalysis.forEach(priceArea => {
                if (priceArea.areaInSquareMeter && priceArea.pricePerSquareMeter) {
                    priceArea.price = Math.round(priceArea.areaInSquareMeter * priceArea.pricePerSquareMeter)
                }
            });

            this.calculateLandAnalysedValue();
        },
        calculateLandAnalysedValue: function() {
            const netPrice = this.analysis.analysis.netPrice;
            const priceArea = this.analysis.landAnalysis;
            priceArea.price = Math.round(
                netPrice -
                    (this.otherBuildingsTotal +
                        this.otherImprovementsTotal +
                        this.mainBuildingsTotal)
            );

            this.calculateLandRateM2();
        },
        calculateLandRateM2: function() {
            const priceArea = this.analysis.landAnalysis;
            priceArea.pricePerSquareMeter = Math.round(
                priceArea.price / priceArea.areaInSquareMeter
            );
        },
        analyseMainBuildingChanges: function(changes) {
            const priceArea = this.analysis.mainBuildingAnalysis;

            let value;

            if ('pricePerSquareMeter' in changes) {
                value = numeral(changes.pricePerSquareMeter).value() || '';
                priceArea.pricePerSquareMeter = Math.round(value);

                this.calculateOtherBuildingsRateM2();
                this.calculateOtherBuildingsAnalysedValue();
                this.calculateMainBuildingAnalysedValue();
            }

            if ('price' in changes) {
                value = numeral(changes.price).value() || '';
                priceArea.price = Math.round(value);

                this.calculateMainBuildingRateM2();
                this.calculateOtherBuildingsRateM2();
                this.calculateOtherBuildingsAnalysedValue();
                this.calculateOtherImprovementsAnalysedValue();
            }
        },
        analyseOtherBuildingsChanges: function(key, changes) {
            const priceArea = this.analysis.otherBuildingsAnalysis[key];

            let value;

            if ('pricePerSquareMeter' in changes) {
                value = numeral(changes.pricePerSquareMeter).value() || '';
                priceArea.pricePerSquareMeter = Math.round(value);

                this.calculateOtherBuildingsAnalysedValue();
            }

            if ('price' in changes) {
                value = numeral(changes.price).value() || '';
                priceArea.price = Math.round(value);

                this.calculateOtherBuildingsRateM2();
                this.calculateOtherImprovementsAnalysedValue();
            }
        },
        analyseOtherImprovementsChanges: function(key, changes) {
            const priceArea = this.analysis.otherImprovementsAnalysis[key];

            let value;

            if ('pricePerSquareMeter' in changes) {
                value = numeral(changes.pricePerSquareMeter).value() || '';
                priceArea.pricePerSquareMeter = Math.round(value);

                this.calculateOtherImprovementsAnalysedValue();
            }

            if ('price' in changes) {
                value = numeral(changes.price).value() || '';
                priceArea.price = Math.round(value);

                this.calculateOtherImprovementsRateM2();
                this.calculateLandAnalysedValue();
            }
        },
        removeRow: function(e) {
            var index = e.target.getAttribute('data-index');
            var rowType = e.target.getAttribute('row-type');
            this.analysis[rowType].splice(index, 1);
        },
        addRow: function(e) {
            var index = parseInt(e.target.getAttribute('data-index')) + 1;
            var rowType = e.target.getAttribute('row-type');
            var newRow =  this.getNewAnalysisObject();
            this.analysis[rowType].splice(index, 0 , newRow);
        }
    }
};
</script>

<style></style>

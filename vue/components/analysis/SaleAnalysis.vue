<script setup>
import { useRouterParam } from '@/composables/useRouterParam';
import { onMounted, provide, ref } from 'vue';
import { getSale } from '@/services/ApiSalesController';
import { useSaleAnalysis } from '@/composables/useSaleAnalysis';
import { useRouter } from 'vue-router/composables';

const router = useRouter();
const saleId = useRouterParam('saleId', null, {
    format: (value) => parseInt(value),
});
const saleAnalysis = useSaleAnalysis();
const sale = ref(null);
provide('saleData', sale);
const analysisCategory = ref(null);
const loaded = ref(false);


onMounted(async () => {
    sale.value = await getSale(saleId.value);
    analysisCategory.value = saleAnalysis.getAnalysisCategory(sale.value.propertyInfo.category.code.substring(0, 1));

    if (sale && analysisCategory && router.currentRoute.name === 'sale-analysis') {
        await saleAnalysis.tryOpenAnalysisById(sale.value.saleId, false);
    }

    loaded.value = true;
});

</script>

<template>
    <div v-if="loaded">
        <router-view v-slot="{ Component }"/>
    </div>
    <div v-else>
    </div>
</template>

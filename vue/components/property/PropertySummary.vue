<template>
    <div v-if="property">
        <div class="listingControls">
            <span v-if="canNavigate">
                <i title="Previous Assessment" class="listingButton material-icons assessmentNav"
                   @click="showProperty(previousPropertyId, 'back')" data-direction="back">arrow_back</i>
                <i title="Next Assessment" class="listingButton material-icons assessmentNav"
                   @click="showProperty(nextPropertyId, 'forward')" data-direction="forward">arrow_forward</i>
            </span>
            <ul>
                <li v-if="propertyStore && propertyStore.status && propertyStore.status.code === 'I'" id="inactive-assessment-text">THIS IS AN INACTIVE ASSESSMENT</li>
                <li class="md-qpid">QPID: <strong>{{ property.qupid }}</strong></li>
                <li v-if="property.tapid" class="md-qpid">TAPID: <strong>{{ property.tapid }}</strong></li>
                <li class="md-valRef">
                    <div v-if="property.valRefList && property.valRefList.length > 1 && this.canNavigate">
                        <label>VAL REF:</label>
                        <select class="valRefOption valRef-select" @change="showPlate()" v-model="selectedPropertyId">
                            <option v-for="valRef in property.valRefList"
                                :selected="selectedPropertyId == valRef.propertyId ? 'true' : 'false'"
                                :value="valRef.propertyId"
                                :key="valRef.propertyId">
                                    {{ valRef.valuationReference }}
                            </option>
                        </select>
                    </div>
                    <div v-else>
                        VAL REF:<strong>{{ property.valuationReference }}</strong>
                    </div>
                <li v-if="property.valRefList && property.valRefList.length > 1" class="md-extensions">
                    <span>{{ property.valRefList.length - 1 }}</span>
                </li>
                <li class="md-copyValref" title="Copy Val Ref">
                    <i id="ValRef-to-Clipboard" class="material-icons md-dark" @click="copyValref">content_copy</i>
                </li>
            </ul>
        </div>
        <div :class="{maoriLand: property.maoriLand == 'Yes'}">
            <div class="md-full md-summaryHeader" data-cy="property-summary-header">
                <div class="md-propertyOverview">
                    <ul class="md-categoryDescription">
                        <li>{{ (property.category && property.category.code) | emptyToDash }}: <em>{{ (property.category && property.category.description) | emptyToDash }}</em></li>
                        <li><em>Effective: <strong>{{ property.currentRevisionDate | date }}</strong></em></li>
                    </ul>
                    <div v-if="!isInSubdivision" class="md-summaryAddress">
                        <router-link :to="{name: 'property', params: {qpid: qpid || 0}}" target="_blank">
                            <h1>{{ property.address1 }}<span>{{ property.address2 }}</span></h1>
                        </router-link>
                    </div>
                    <div v-else class="md-summaryAddress">
                        <h1>{{ property.address1 }}<span>{{ property.address2 }}</span></h1>
                    </div>
                    <ul class="md-summaryOwners">
                        <li><label>Nature of Imps:</label>{{ property.natureOfImprovements }}</li>
                        <li v-if="property.occupiers && property.occupiers.length > 0"><label>Occupier:</label>{{ property.occupiers[0].isNameSecret ? 'Not Available' : property.occupiers[0].fullName }}</li>
                        <li v-if="property.occupiers && property.occupiers.length > 1"><label>Occupier:</label>{{ property.occupiers[1].isNameSecret ? 'Not Available' : property.occupiers[1].fullName }}</li>
                        <li v-if="property.owners && property.owners.length > 0"><label>Owner:</label>{{ property.owners[0].isNameSecret ? 'Not Available' : property.owners[0].fullName }}</li>
                    </ul>
                    <ul class="md-summaryTotals">
                        <li><label>Units: </label>{{property.units}}</li>
                        <li><label>Total Floor:</label>{{property.TFA}}<span>m<sup>2</sup></span></li>
                        <li><label>Total Living:</label>{{property.TLA}}<span>m<sup>2</sup></span></li>
                        <li><label>Land Area:</label>{{property.landArea}}<span>ha</span></li>
                    </ul>
                    <ul class="md-summaryValues">
                        <li><label>Capital Value</label>{{property.capitalValue | numeral('$0,0')}}<span><strong>{{property.cvNetRate | numeral('$0,0')}}</strong>m<sup>2</sup></span>
                        </li>
                       <li><label>Land Value</label>{{property.landValue | numeral('$0,0')}}<span v-if="!property.showHectares"><strong>{{property.lvNetRate | numeral('$0,0')}}</strong>m<sup>2</sup></span><span v-else><strong>{{property.lvNetRate | numeral('$0,0')}}</strong>ha</span>
                        </li>
                        <li><label>Improvements</label>{{property.valueOfImprovements | numeral('$0,0')}}<span><strong>{{property.viNetRate | numeral('$0,0')}}</strong>m<sup>2</sup></span>
                        </li>
                        <li><label v-if="isInternalUser">Building Net Rate</label>{{ isInternalUser ? (property.buildingNetRate || 0) : '' | numeral('$0,0') }}<span v-if="isInternalUser">m<sup>2</sup></span></li>
                    </ul>
                </div>

                <div class="md-photoGallery master-details-carousel"></div>

                <div class="md-rtvSales-overview">

                    <div class="md-estimates-wrapper" v-if="isInternalUser">
                        <ul class="md-marketEstimate mev">
                            <li><em>RealTime Value: <strong>{{ marketEstimateDate }}</strong></em></li>
                            <li><h3>{{property.marketEstimateValue}}</h3></li>
                            <li class="me-comparison me-compare-CV">
                                <label>Compared to Current CV</label>
                                <span v-bind:class="[property.marketEstimateToCapitalValueUpDown != null ? (property.marketEstimateToCapitalValueUpDown < 0 ? 'valueDown' : 'valueUp') : '']">{{property.marketEstimateToCapitalValue}}</span>
                            </li>
                        </ul>
                        <ul class="md-marketEstimate mev">
                            <li><em>RealTime Land Value: </em></li>
                            <li><h3>{{property.realTimeLandValue}}</h3></li>
                            <li class="me-comparison me-compare-LV">
                                <label>Compared to Current LV</label>
                                <span v-bind:class="[property.realTimeLandValueToLandValueUpDown != null ? (property.realTimeLandValueToLandValueUpDown < 0 ? 'valueDown' : 'valueUp') : '']">{{property.realTimeLandValueToLandValue}}</span>
                            </li>
                        </ul>
                    </div>

                    <!-- LATEST SALE information to be displayed in This Area of the New Header Starts Here -->
                    <ul class="md-summaryValues md-summaryLastSale" v-if="property.maoriLand != 'Yes'">
                        <li>
                            Last Sale:
                            <strong v-if="!beenSold">No sale on record</strong>
                            <strong v-else>{{ lastSale.date | date}}</strong>
                            <strong class="saleClass" v-if="beenSold">{{ lastSale.saleType && lastSale.saleType.code }}{{ lastSale.saleTenure && lastSale.saleTenure.code }}{{ lastSale.priceValueRelationship && lastSale.priceValueRelationship.code  }}</strong>
                            <ul class="saleClass-description mdl-shadow--2dp"  v-if="beenSold">
                                <li v-if="lastSale.saleType"><em>Sale Type</em><span>{{ lastSale.saleType.code }}</span><span>{{ lastSale.saleType.description }}</span></li>
                                <li v-if="lastSale.saleTenure"><em>Sale Tenure</em><span>{{ lastSale.saleTenure.code }}</span><span>{{ lastSale.saleTenure.description }}</span></li>
                                <li v-if="lastSale.priceValueRelationship"><em>Price Value Relationship</em><span>{{ lastSale.priceValueRelationship.code }}</span><span>{{ lastSale.priceValueRelationship.description }}</span></li>
                            </ul>
                            <span class="saleStatus" v-if="lastSale.status">{{ lastSale.status }}</span>
                        </li>
                        <li><label>Net Sale Price</label>{{ lastSale.netPrice | numeral('$0,0', '--') }}</li>
                        <li><label>Chattels</label>{{ lastSale.chattels | numeral('$0,0', '--') }}</li>
                        <li v-if="!isRuralProperty"><label>Analysed Land</label>{{ isInternalUser && lastSale.analysedLV ? lastSale.analysedLV : null | numeral('$0,0', '--') }}</li>
                        <li v-else><label>Analysed Land</label>{{ isInternalUser && ruralAnalysedLV ? ruralAnalysedLV : '--'}}</li>
                        <li><label>NSP/CV</label>{{ lastSale.NSPCV | numeral('0.[00]', '--')}}</li>
                        <li v-if="!isRuralProperty"><label>Gross Rate</label>{{ lastSale.buildingGrossRate | numeral('$0,0', '--') }}<span v-if="lastSale.buildingGrossRate">m<sup>2</sup></span></li>
                        <li v-else><label>Analysed Land/Ha</label>{{ isInternalUser && ruralAnalysedLandPerHa ? ruralAnalysedLandPerHa : '--' }}</li>
                        <li v-if="!isRuralProperty"><label>Sale BNR</label>{{ isInternalUser && lastSale.buildingNetRate ? lastSale.buildingNetRate : null | numeral('$0,0', '--') }}<span v-if="lastSale.buildingNetRate && isInternalUser">m<sup>2</sup></span></li>
                        <li v-else><label>Analysed Land/Prodn</label>{{ isInternalUser && ruralAnalysedLandPerProdn ? ruralAnalysedLandPerProdn : '--' }}</li>
                    </ul>
                    <div class="md-newMonarchDataCheckFlags" v-show="false">
                         <span>Data Issues: <button id="dataIssuesButton" title="Some property data on this assessment fails validation checks." disabled>{{dataIssuesYN}}</button></span>
                         <span>Issues Checked: <button id="issuesCheckedButton" title="If the failed validation is acceptable then change this to “Yes”, if the failed validation is not acceptable then fix the data.">{{issuesCheckedYN}}</button></span>
                    </div>
                    <!-- For Maori Land Properties -->
                    <ul class="md-summaryValues" v-if="property.maoriLand == 'Yes'">
                        <li>
                            <label>Unadjusted CV</label>{{ property.cml.capitalValue | numeral('$0,0')}}
                            <span><strong>{{ property.cml.cvNetRate | numeral('$0,0')}}</strong>m<sup>2</sup></span>
                        </li>
                        <li>
                            <label>Unadjusted LV</label>{{ property.cml.landValue | numeral('$0,0') }}
                            <span><strong>{{ property.cml.lvNetRate | numeral('$0,0') }}</strong>m<sup>2</sup></span>
                        </li>
                        <li>
                            <label>Unadjusted VI</label>{{ property.cml.vi | numeral('$0,0') }}
                            <span><strong>{{ property.cml.viNetRate | numeral('$0,0') }}</strong>m<sup>2</sup></span>
                        </li>
                        <li class="maoriLand-adjustment significancePct"><span>{{ property.cml.significance | numeral('0,0.0')}}%</span><label>Significance</label></li>
                        <li class="maoriLand-adjustment ownersPct"><span>{{ property.cml.owners | numeral('0,0.0')}}%</span><label>Owners</label></li>
                        <li class="maoriLand-adjustment adjustmentPct"><span>{{ property.cml.adjustment | numeral('0,0.0')}}%</span><label>Adjustment</label></li>
                        <li class="maoriLand-adjustment totalOwners"><span>{{ property.mlnoOfOwners }}</span><label>Number of Owners</label></li>
                    </ul>
                </div>

                <slot></slot>

                <property-toolbar
                    v-if="displayToolbar"
                    :property="property"
                    :moreThanTwentyPhotos="moreThanTwentyPhotos"
                    :twentyFirstPhotoId="twentyFirstPhotoId"
                    @open-photo-uploader="openPhotoUploader()"
                />
                <div v-else class="qvToolbar-combined">
                    <ul class="qvToolbar-icons righty" >
                        <li class="photo-uploader"
                            :data-property="property.id"
                            :class="{disabled: isReadOnly == true}"
                            @click="openPhotoUploader()"
                            title="Upload Photos"
                            data-upgraded=",MaterialButton"
                        >
                            <i class="material-icons md-light">&#xE251;</i>
                        </li>
                    </ul>

                    <ul class="qvToolbar-qivs righty">
                        <li title="More Photos">
                            <div class="morePhotos" v-show="moreThanTwentyPhotos" :data-id="twentyFirstPhotoId" :data-property="property.id"><i class="material-icons">add_to_photos</i>
                            </div>
                        </li>
                        <li class="md-qivs" @click="openQivs(masterDetailsUrl)">
                            <label>QIVS</label> <i class="material-icons">call_made</i>
                        </li>
                        <li class="md-qivs" @click="openQivs(floorPlanUrl)"><label>FLOOR PLANS</label> <i class="material-icons">call_made</i></li>
                        <li class="md-qvms" v-if="isInternalUser"
                            @click="openMap()">
                            <label>MAP</label> <i class="material-icons">call_made</i>
                        </li>
                        <li class="md-qvms"
                            @click="openGoogleSearchTab()">
                            <label>WEB</label> <i class="material-icons icon--flipped">search</i>
                        </li>
                    </ul>
                </div>
            </div>
            <div v-if="exception" class="bAlert bAlert-danger">
                {{ exception }}
            </div>
        </div>
    </div>
</template>
<script>
import { mapState, mapGetters } from 'vuex';
import numeral from 'numeral';
import moment from 'moment';
import { openMap, openQivsInNewTab, openMapInNewTab, openUrlInNewTab } from '../../utils/QivsUtils';
import { formatDate } from '@/utils/FormatUtils';

export default {
    data: function() {
        return {
            property: {},
            moreThanTwentyPhotos: false,
            twentyFirstPhotoId: null,
            selectedPropertyId: null,
            /* TODO ugly but isolates what is being calculated as lastSale */
            lastSale: {
                date: null,
                status: null,
                netPrice: null,
                chattels: null,
                analysedLV: null,
                NSPCV: null,
                buildingGrossRate: null,
                buildingNetRate: null
            },

            sales: [],
            exception: null,
            dataIssuesYN: 'NO',
            issuesCheckedYN: 'NO',
            ruralSales: null,
        }
    },
    // TODO confirm if qpid or property id
    // TODO confirm if property or rating unit (probably property at the moment)
    props: {
        propertyId: {
            type: String
        },
        canNavigate: {
            type: Boolean,
            default: true
        },
        displayToolbar:{
            type: Boolean,
            default: true
        },
        isInSubdivision: {
            type: Boolean,
            default: false
        }
    },
    components: {
        'property-toolbar': () => import(/* webpackChunkName: "PropertyToolbar" */'./PropertyToolbar.vue')
/*        'classification-dropdown': () => import('../common/form/ClassificationDropdown.vue'), */
    },
    computed: {
        ...mapState('property', [
            'loading',
            'error',
            'properties',
            'propertyPhotos',
            'previousPropertyId',
            'nextPropertyId',
        ]),
        ...mapState('property', {
            'propertyStore': 'property'
        }),
        ...mapState('userData', [
            'isInternalUser',
            'isReadOnlyUser'
        ]),
        ...mapGetters(['getCategoryClassifications']),
        ...mapState('ruralRtv' , {
            ruralRtvCategories: 'baseCategories',
            ruralRtvValues: 'rtvValues'
        }),
        marketEstimateDate() {
            return formatDate(this.property.marketEstimateDate);
        },
        improvementDateRangeDescriptions() {
            const improvementDateRangeClassifications = this.getCategoryClassifications('ImprovementDateRange');
            // Convert the array of ImprovementDateRange Classifications into a key-value pair of classification code to classification description.
            return improvementDateRangeClassifications.reduce((accumulator, currentValue) => ({ ...accumulator, [currentValue.code]: currentValue.description }), {});
        },
        tagsDescriptions() {
            const tagsClassifications = this.getCategoryClassifications('Tags');
            // Convert the array of Tags Classifications into a key-value pair of classification code to classification description.
            return tagsClassifications.reduce((accumulator, currentValue) => ({ ...accumulator, [currentValue.code]: currentValue.description }), {});
        },
        isReadOnly: function() {
            return this.isReadOnlyUser;
        },
        mapUrl: function() {
            return this.$store.getters['userData/qvmsMapUrl'](this.property.qupid);
        },
        floorPlanUrl: function() {
            return this.$store.getters['userData/qivsFloorPlanUrl'](this.property.qupid);
        },
        masterDetailsUrl: function() {
            return this.$store.getters['userData/qivsMasterDetailsUrl'](this.property.qupid);
        },
        beenSold: function() {
            return (this.lastSale.date ? true : false);
        },
        qpid() {
            return this.property && this.property.qupid;
        },
        isRuralProperty() {
            if (!this.property
                || !this.property.category
                || !this.property.category.code) {
                return false;
            }
            const propertyBaseCategory = this.property.category.code.charAt(0);
            const valuableRuralCategories = 'ADFHPS';
            if (valuableRuralCategories.includes(propertyBaseCategory)) {
                return true;
            }
            return false;
        },
        ruralAnalysedLV() {
            return this.ruralSales
                ? numeral(this.ruralSales.analysedLV ? this.ruralSales.analysedLV : 0).format('$0,0')
                : null;
        },
        ruralAnalysedLandPerHa() {
            if (this.ruralSales) {
                const landArea = this.ruralSales.primaryProperty && this.ruralSales.primaryProperty.landUse
                    ? this.ruralSales.primaryProperty.landUse.landArea
                    : null;
                return this.property && landArea && this.ruralSales.analysedLV
                    ? numeral(this.ruralSales.analysedLV / landArea).format('$0,0')
                    : null;
            }
            return null;
        },
        ruralAnalysedLandPerProdn() {
            if (this.ruralSales) {
                const production = this.ruralSales.primaryProperty && this.ruralSales.primaryProperty.landUse
                    ? numeral(this.ruralSales.primaryProperty.landUse.production)
                    : null;
                return this.property && production && this.ruralSales.analysedLV
                    ? numeral(this.ruralSales.analysedLV / production).format('$0,0')
                    : null;
            }
            return null;
        },
        hasRuralSales() {
            return this.isRuralProperty && this.ruralSales ? true : false;
        },
    },
    watch: {
        'propertyId': {
            immediate: true,
            handler(newVal, oldVal) {
                if(newVal)
                    this.showProperty(newVal);
          	}
        },
    },
    mounted() {
        this.registerCarousel();
    },
    methods: {
            showPlate: function () {
                if (this.canNavigate) {
                    this.showProperty(this.selectedPropertyId);
                }
            },

            showProperty(propertyId) {
                var self = this;

                /* Clear any existing photos in case of errors etc*/
                $('.master-details-carousel').slick('slickRemove', null, null, true);

                this.$store.dispatch('property/getProperty', propertyId).then(() => {
                    self.selectedPropertyId = self.propertyStore.id;
                    self.fillProperty();

                    var taCode = self.propertyStore.territorialAuthority && self.propertyStore.territorialAuthority.code;
                    self.overlayCurrentRevisionDate(taCode);

                    var qpid = self.propertyStore.qupid;
                    self.overlaySales(qpid);

                    // TODO in the future this would be corrected to gather all property info together instead of separate calls
                    // which are based on underlying data source
                    self.overlayOther(qpid);

                    self.$nextTick(function () {
                        self.getPhotos(propertyId);
                    });
                }
                );
            },

            overlaySales: function (qupid) {
                // Used for calculations
                var totalFloorArea = this.propertyStore.landUseData ? this.propertyStore.landUseData.totalFloorArea : 0;
                var capitalValue = this.propertyStore.currentValuation ? this.propertyStore.currentValuation.capitalValue : 0;

                var postData = {};
                postData.locationCriteria = {};
                postData.locationCriteria.qupid = qupid;
                postData.sort = ['SALE_DATE'];
                postData.order = 'DESC';
                if(!this.isInternalUser) {
                    postData.classificationCriteria = { saleStatusCodes : ['1'] };
                }
                const self = this;
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.SalesSearch.getSalesByQupid().url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(postData),
                    dataType: "json",
                    success: function (response) {
                        var sales = response;
                        var latestSale =  sales && sales.length > 0 ? sales[0] : null;
                        if(latestSale == null) {
                            self.lastSale = {};
                            return;
                        }
                        var lastSale = {};
                        lastSale.date = latestSale.saleDate;
                        lastSale.status = latestSale.status.description;
                        lastSale.netPrice = latestSale.price ? latestSale.price.net : null;
                        lastSale.chattels = latestSale.price ? latestSale.price.chattels : null;
                        lastSale.analysedLV = latestSale.saleAnalysis ? latestSale.saleAnalysis.totalAnalysedLandValue: null;
                        lastSale.saleType = latestSale.saleType;
                        lastSale.saleTenure = latestSale.saleTenure;
                        lastSale.priceValueRelationship = latestSale.priceValueRelationship;

                        var netPrice = latestSale.price ? latestSale.price.net : 0;
                        if (capitalValue > 0)
                            lastSale.NSPCV = (Math.round((netPrice/capitalValue)*100))/100;


                        if (latestSale.saleAnalysis && latestSale.saleAnalysis.analysedMainUnitGrossRate)
                            lastSale.buildingGrossRate = latestSale.saleAnalysis.analysedMainUnitGrossRate;

                        //TODO: this is an intermittent step to deal with existing Sale Analysis that have not yet filled the gross rate.
                        // It should be removed eventually - Chris L 02 Oct 2020
                        else if(latestSale.saleAnalysis && totalFloorArea > 0)
                            lastSale.buildingGrossRate = Math.round(netPrice/totalFloorArea);

                        else
                            lastSale.buildingGrossRate = null;


                        lastSale.buildingNetRate = latestSale.saleAnalysis && (latestSale.saleAnalysis.analysedMainBuilding && latestSale.saleAnalysis.analysedMainBuilding.pricePerSquareMeter);

                        self.lastSale = lastSale;

                        if (self.isRuralProperty) {
                            self.ruralSales = null;
                            self.getRuralSaleAnalysis(latestSale.qivsSaleId);
                        }
                    },
                    error: function (response) {
                        self.lastSale = {};
                        self.errorHandler('Failed to get Sales.', response);
                    }
                });
            },
            overlayOther: function (qpid) {
                var self = this
                var p = this.propertyStore;

                var totalFloorArea = p.landUseData ? p.landUseData.totalFloorArea : 0;
                var landArea = p.landUseData ? p.landUseData.landArea : 0;
                var capitalValue = p.currentValuation ? p.currentValuation.capitalValue : 0;
                var landValue = p.currentValuation ? p.currentValuation.landValue : 0;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.PropertyMasterData.getStatsSummary(qpid).url,
                    cache: false,
                    success: function (response) {
                        if (!response) {
                            self.errorHandler('Failed to get Reval and RTV values.', response);
                        }
                        var data = response;

                        self.property.tapid = data.tapid;
                        self.property.revisionCapitalValue = data.revisionCapitalValue;
                        self.property.revisionLandValue = data.revisionLandValue;

                        self.property.hasRevision = data.revisionCapitalValue || data.revisionLandValue;
                        self.property.predictedWeeklyMarketRent = data.predictedWeeklyMarketRent;
                        self.property.marketRentBasis = data.marketRentBasis;
                        var revalCapVal = data.revisionCapitalValue ? data.revisionCapitalValue : 0;
                        var revalLandVal = data.revisionLandValue ? data.revisionLandValue : 0;
                        var revalValueOfImprovements = 0;
                        if (revalCapVal > 0 && revalLandVal > 0) {
                            revalValueOfImprovements = Math.round(revalCapVal - revalLandVal);
                        }
                        self.property.revalValueOfImprovements = revalValueOfImprovements;
                        var revalCapValNetRate = 0;
                        if (revalCapVal > 0 && totalFloorArea > 0) {
                            revalCapValNetRate = Math.round(revalCapVal / totalFloorArea);
                        }
                        self.property.revalCapValNetRate = revalCapValNetRate;
                        var revalLandValNetRate = 0;
                        if (revalLandVal > 0 && landArea > 0) {
                            revalLandValNetRate = Math.round(revalLandVal / (landArea * 10000));
                        }
                        self.property.revalLandValNetRate = revalLandValNetRate;
                        var revalValueOfImprovementsNetRate = 0;
                        if (totalFloorArea > 0) {
                            revalValueOfImprovementsNetRate = Math.round((revalCapVal - revalLandVal) / totalFloorArea);
                        }
                        self.property.revalValueOfImprovementsNetRate = revalValueOfImprovementsNetRate;
                        var capitalValueDiff = 0;
                        if (capitalValue && capitalValue > 0 && revalCapVal > 0) {
                            capitalValueDiff = ((revalCapVal * 100) / capitalValue) - 100;
                        }
                        self.property.capitalValueDiff = (Math.round(capitalValueDiff * 10)) / 10;
                        var landValueDiff = 0
                        if (landValue && landValue > 0 && revalLandVal > 0) {
                            landValueDiff = ((revalLandVal * 100) / landValue) - 100;
                        }
                        self.property.landValueDiff = (Math.round(landValueDiff * 10)) / 10;
                        var valueOfImprovements = 0
                        if (capitalValue) {
                            valueOfImprovements = Math.round(capitalValue - landValue);
                        }

                        var valueOfImprovementsDiff = 0;
                        if (valueOfImprovements > 0) {
                            valueOfImprovementsDiff = (((revalCapVal - revalLandVal) * 100) / valueOfImprovements) - 100;
                        }
                        self.property.valueOfImprovementsDiff = (Math.round(valueOfImprovementsDiff * 10)) / 10;
                        self.property.marketEstimateDate = data.marketEstimateDate;
                        self.property.realTimeLandValue = data.realTimeLandValue ? numeral(data.realTimeLandValue).format('$0,0') : 'N/A';
                        self.property.marketEstimateValue = data.marketEstimateValue ? numeral(data.marketEstimateValue).format('$0,0') : 'N/A';
                        self.property.realTimeValue = data.realTimeValue && Math.round(data.realTimeValue / 1000) * 1000;

                        var evaluer = data.marketEstimateValue ? data.marketEstimateValue : 0;
                        self.property.marketEstimateToCapitalValue = capitalValue && data.marketEstimateValue ? Math.round(((evaluer * 100) / capitalValue) - 100) + '%' : '-';
                        self.property.marketEstimateToCapitalValueUpDown = capitalValue && data.marketEstimateValue ? Math.round(((evaluer * 100) / capitalValue) - 100) : null;

                        //TODO: Remove rtv
                        var rtv = data.realTimeValue ? data.realTimeValue : 0;
                        self.property.realTimeToCapitalValue = capitalValue &&  data.realTimeValue ? Math.round(((rtv * 100) / capitalValue) - 100) + '%' : '-';
                        self.property.realTimeToCapitalValueUpDown = capitalValue &&  data.realTimeValue ? Math.round(((rtv * 100) / capitalValue) - 100) : null;

                        self.property.estimatedLandValue = numeral(data.estimatedLandValue).format('$0,0');
                        self.property.realTimeLandValueToLandValue = landValue && data.realTimeLandValue ? Math.round(((data.realTimeLandValue * 100) / landValue) - 100) + '%' : '-';
                        self.property.realTimeLandValueToLandValueUpDown = landValue && data.realTimeLandValue ? Math.round(((data.realTimeLandValue * 100) / landValue) - 100) : null;

                        self.loadRuralRtvValues();
                    },
                    error: function (response) {
                        self.errorHandler('Failed to get Reval and RTV values.', response);
                    }
                });
            },
            overlayCurrentRevisionDate: function (taCode) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.PropertyMasterData.getTASummary(taCode).url,
                    cache: false,
                    success: function (response) {
                        self.property.currentRevisionDate = response.currentRevisionDate;
                        self.$store.commit('property/setTaSummary', response);
                    },
                    error: function (response) {
                        self.errorHandler('Failed to get Current Revision Date', response);
                    }
                });
            },
            fillProperty: function () {
                var self = this;
                // Get the raw property from the property store and copy portions into component data
                var data = this.propertyStore;

                // Other property data which gets overlaid in another call ... this is to keep reactivity
                var property = {
                    capitalValueDiff: null,
                    hasRevision: null,
                    landValueDiff: null,
                    marketEstimateDate: null,
                    marketEstimateToCapitalValue: null,
                    marketEstimateToCapitalValueUpDown: null,
                    marketEstimateValue: null,
                    marketRentBasis: null,
                    predictedWeeklyMarketRent: null,
                    realTimeToCapitalValue: null,
                    realTimeToCapitalValueUpDown: null,
                    realTimeValue: null,
                    revalCapValNetRate: null,
                    revalLandValNetRate: null,
                    revalValueOfImprovements: null,
                    revalValueOfImprovementsNetRate: null,
                    revisionCapitalValue: null,
                    revisionLandValue: null,
                    tapid: null,
                    valueOfImprovementsDiff: null,
                    // TA Summary
                    currentRevisionDate: null,
                };

                property.classifications = data.massAppraisalData ? data.massAppraisalData.classifications : [];
                var valRefList = [];

                $.each(this.properties, function (i, obj) {
                    var valRef = obj.rollNumber + '/' + obj.assessmentNumber + ' ' + obj.suffix;
                    var propertyId = obj.id;
                    var valRefObj = {};
                    valRefObj.valuationReference = valRef;
                    valRefObj.propertyId = propertyId;
                    valRefList.push(valRefObj);
                });

                property.valRefList = valRefList;
                property.valuationReference = (data.rollNumber ? data.rollNumber + '/' : '') + (data.assessmentNumber ? data.assessmentNumber : '') + ' ' + (data.suffix ? data.suffix : '');
                property.id = data.id;
                property.qupid = data.qupid;
                property.taCode = data.territorialAuthority && data.territorialAuthority.code;
                property.certificateOfTitle = data.certificateOfTitle;
                property.legalDescription = data.legalDescription;
                property.extensions = data.massAppraisalData ? data.massAppraisalData.extensions : 0;
                property.maoriLand = data.landUseData ? (data.landUseData.isMaoriLand ? 'Yes' : 'No') : 'No';
                property.planNumber = data.planNumber;

                var streetNumberSuffix = data.address ? (data.address.streetNumberSuffix ? ' ' + data.address.streetNumberSuffix : '') : '';
                property.address1 = data.address ? ((data.address.streetNumber ? data.address.streetNumber : '') + streetNumberSuffix + ' ' + (data.address.streetName ? data.address.streetName : '')
                + (data.address.streetType ? ' ' + data.address.streetType.description + ',' : '')) : '';
                if (property.address1.indexOf('undefined') !== -1) {
                    property.address1 = '';
                }
                property.address2 = data.address ? (data.address.suburb ? (data.address.suburb + ', ') : '') : '';
                property.address2 += data.address ? (data.address.town ? data.address.town + ', ' : '') : '';
                property.address2 += data.territorialAuthority ? data.territorialAuthority.name : '';
                if (property.address2.indexOf('undefined') !== -1) {
                    property.address2 = '';
                }

                property.category = data.category;
                property.TLA = data.massAppraisalData ? (data.massAppraisalData.totalLivingArea ? data.massAppraisalData.totalLivingArea : '0') : '0';
                property.TFA = data.landUseData ? (data.landUseData.totalFloorArea ? data.landUseData.totalFloorArea : '0') : '0';
                property.MLA = data.massAppraisalData ? (data.massAppraisalData.mainLivingArea ? data.massAppraisalData.mainLivingArea : '0') : '0';
                property.landArea = data.landUseData ? (data.landUseData.landArea ? data.landUseData.landArea : 0) : 0;
                if (typeof property.landArea == 'number') {
                    property.landArea = property.landArea.toFixed(4);
                }

                property.capitalValue = data.currentValuation ? data.currentValuation.capitalValue : 0;
                property.landValue = data.currentValuation ? data.currentValuation.landValue : 0;
                property.buildingNetRate = data.buildingNetRate;
                property.revisedBuildingNetRate = data.revisedBuildingNetRate;

                var valueOfImprovements = 0;
                var landValue = data.currentValuation ? data.currentValuation.landValue : 0;
                var capitalValue = data.currentValuation ? data.currentValuation.capitalValue : 0;
                var totalFloorArea = data.landUseData ? data.landUseData.totalFloorArea : 0;
                var landArea = data.landUseData ? data.landUseData.landArea : 0;

                if (capitalValue) {
                    valueOfImprovements = Math.round(capitalValue - landValue);
                }
                property.valueOfImprovements = valueOfImprovements;

                var cvNetRate = 0;
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0) {
                    cvNetRate = Math.round(capitalValue / totalFloorArea);
                }
                property.cvNetRate = cvNetRate;
                var lvNetRate = 0;
                property.showHectares = false;
                if (landArea < 1 && landArea > 0 && landValue && landValue > 0) {
                    lvNetRate = Math.round(landValue / (landArea * 10000));
                }
                if (landArea >= 1 && landValue && landValue > 0) {
                    lvNetRate = Math.round(landValue / landArea);
                    property.showHectares = true;
                }
                property.lvNetRate = lvNetRate;

                var viNetRate = 0;
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0 && landValue && landValue > 0) {
                    viNetRate = Math.round((capitalValue - landValue) / totalFloorArea);
                }
                property.viNetRate = viNetRate;

                property.propertyPhotos = self.getPropertyPhotos(this.propertyPhotos, property.qupid).list;
                property.natureOfImprovements = data.natureOfImprovements;
                property.units = data.landUseData && data.landUseData.units;
                /* Not needed for banner ...
                property.buildingAge = self.getRenderableValue(data.landUseData ? (data.landUseData.buildingAge ? data.landUseData.buildingAge.description : '') : '');
                property.siteCoverage = self.getRenderableValue(data.landUseData ? data.landUseData.buildingSiteCover : '');
                property.carParks = self.getRenderableValue(data.landUseData ? data.landUseData.carparks : '');
                property.csi = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.classOfSurroundingImprovements ? data.massAppraisalData.classifications.classOfSurroundingImprovements.description : '') : '') : '');
                property.houseType = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.houseType ? data.massAppraisalData.classifications.houseType.description : '') : '') : '');
                property.houseTypeObj = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.houseType ? data.massAppraisalData.classifications.houseType : null) : null) : null);
                property.landscaping = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.landscapingQuality ? data.massAppraisalData.classifications.landscapingQuality.description : '') : '') : '');
                property.deck = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasDeck ? 'Yes' : 'No') : '');
                property.foundation = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasPoorFoundations ? 'Yes' : 'No') : '');
                property.laundryWorkshop = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasLaundry ? 'Yes' : 'No') : '');
                property.carAccess = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasCarAccess ? 'Yes' : 'No') : '');
                property.driveway = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasDriveway ? 'Yes' : 'No') : '');
                property.outlier = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.isOutlier ? 'Yes' : 'No') : '');
                property.effectiveYearBuilt = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.effectiveYearBuilt : '');
                property.landUse = self.getRenderableValue(data.landUseData ? (data.landUseData.landUse ? data.landUseData.landUse.description : '') : '');
                property.bedrooms = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.bedrooms : '');
                property.toilets = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.toilets : '');
                property.wallConstructionAndCondition = data.landUseData ? (data.landUseData.wallConstruction ? data.landUseData.wallConstruction.description : '') : '';
                property.wallConstruction = data.landUseData ? (data.landUseData.wallConstruction ? data.landUseData.wallConstruction : null) : null;
                property.wallConstructionAndCondition += data.landUseData ? (data.landUseData.wallCondition ? ' ' + data.landUseData.wallCondition.description : '') : '';
                property.wallConstructionAndCondition = self.getRenderableValue(property.wallConstructionAndCondition);
                property.roofConstruction = data.landUseData ? (data.landUseData.roofConstruction ? data.landUseData.roofConstruction : null) : null;
                property.roofConstructionAndCondition = data.landUseData ? (data.landUseData.roofConstruction ? data.landUseData.roofConstruction.description : '') : '';
                property.roofConstructionAndCondition += data.landUseData ? (data.landUseData.roofCondition ? ' ' + data.landUseData.roofCondition.description : '') : '';
                property.roofConstructionAndCondition = self.getRenderableValue(property.roofConstructionAndCondition);
                property.underMainRoofGarages = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.underMainRoofGarages : '');
                property.freeStandingGarages = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.freestandingGarages : '');
                property.otherLargeImprovements = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasLargeOtherImprovements ? 'Yes' : 'No') : 'No');
                property.modernisation = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.isModernised ? 'Yes' : 'No') : 'No');
                property.zone = self.getRenderableValue(data.landUseData ? data.landUseData.landZone : '');
                property.lotPosition = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.lotPosition ? data.massAppraisalData.classifications.lotPosition.description : '') : '') : '');
                property.contour = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.contour ? data.massAppraisalData.classifications.contour.description : '') : '') : '');
                var viewDescription = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.view ? (data.massAppraisalData.classifications.view.description + ' ') : '') : '') : '');
                var viewCode = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.view ? (data.massAppraisalData.classifications.view.code + ' ') : '') : '') : '');
                if (viewCode.trim() == 'N') {
                    property.viewScope = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.viewScope ? data.massAppraisalData.classifications.viewScope.description : '') : '') : '')
                } else {
                    property.viewScope = ((viewDescription && viewDescription != '-') ? viewDescription.substring(viewDescription.trim().lastIndexOf(" ") + 1) : '') + self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.viewScope ? data.massAppraisalData.classifications.viewScope.description : '') : '') : '');
                }
                property.production = self.getRenderableValue(data.landUseData ? data.landUseData.production : '');
                */

                var tora = ''
                if (data.tenure && data.tenure.code) {
                    tora += data.tenure.code;
                    property.tenure = data.tenure.description;
                }
                if (data.ownership && data.ownership.code) {
                    tora += data.ownership.code;
                    property.ownership = data.ownership.description;
                }
                if (data.rateability && data.rateability.code) {
                    tora += data.rateability.code;
                    property.rateability = data.rateability.description;
                }
                if (data.apportionment && data.apportionment.code) {
                    tora += data.apportionment.code;
                    property.apportionment = data.apportionment.description;
                }
                property.tora = tora;

                var owners = data.owners;
                var occupiers = data.occupiers;
                var propertyOwnersAndOccupiers = [];
                var propertyOwners = [];
                var propertyOccupiers = [];
                $.each(owners, function (i, obj) {
                    var propertyOwner = {};
                    propertyOwner.fullName = (obj.firstName ? obj.firstName + ' ' : '') + (obj.secondName ? obj.secondName + ' ' : '') + (obj.thirdName ? obj.thirdName + ' ' : '' )
                            + (obj.lastName ? obj.lastName + ' ' : '');
                    propertyOwner.type = 'Owner';
                    propertyOwner.order = obj.order;
                    propertyOwner.isNameSecret = obj.isNameSecret;
                    if (obj.mailingAddress) {
                        var mailingAddress = obj.mailingAddress;
                        propertyOwner.address = (mailingAddress.careOf ? mailingAddress.careOf + ', ' : '') +
                                (mailingAddress.organisation ? mailingAddress.organisation + ', ' : '') +
                                (mailingAddress.unit ? mailingAddress.unit + ', ' : '') +
                                (mailingAddress.building ? mailingAddress.building + ', ' : '') +
                                (mailingAddress.streetAddress ? mailingAddress.streetAddress + ', ' : '') +
                                (mailingAddress.suburb ? mailingAddress.suburb + ', ' : '') +
                                (mailingAddress.town ? mailingAddress.town : '') +
                                (mailingAddress.postcode ? ' ' + mailingAddress.postcode : '') +
                                (mailingAddress.country ? ', ' + mailingAddress.country : '');
                    }
                    propertyOwners.push(propertyOwner);
                });

                $.each(occupiers, function (i, obj) {
                    var propertyOccupier = {};
                    propertyOccupier.fullName = (obj.firstName ? obj.firstName + ' ' : '') + (obj.secondName ? obj.secondName + ' ' : '') + (obj.thirdName ? obj.thirdName + ' ' : '' )
                            + (obj.lastName ? obj.lastName + ' ' : '');
                    propertyOccupier.type = 'Occupier';
                    propertyOccupier.order = obj.order;
                    propertyOccupier.isNameSecret = obj.isNameSecret;
                    if (obj.mailingAddress) {
                        var mailingAddress = obj.mailingAddress
                        propertyOccupier.address = (mailingAddress.careOf ? mailingAddress.careOf + ', ' : '') +
                                (mailingAddress.organisation ? mailingAddress.organisation + ', ' : '') +
                                (mailingAddress.unit ? mailingAddress.unit + ', ' : '') +
                                (mailingAddress.building ? mailingAddress.building + ', ' : '') +
                                (mailingAddress.streetAddress ? mailingAddress.streetAddress + ', ' : '') +
                                (mailingAddress.suburb ? mailingAddress.suburb + ', ' : '') +
                                (mailingAddress.town ? mailingAddress.town : '') +
                                (mailingAddress.postcode ? ' ' + mailingAddress.postcode : '') +
                                (mailingAddress.country ? ', ' + mailingAddress.country : '');
                    }
                    propertyOccupiers.push(propertyOccupier);
                });
                property.owners = propertyOwners;
                property.occupiers = propertyOccupiers;
                property.ownersAndOccupiers = propertyOwners.concat(propertyOccupiers);

                property.massAppraisal = data.massAppraisalData;
                property.rawLandUse = data.landUseData;

                if(property.maoriLand == 'Yes') {
                    property.cml = self.calculateMaorilandValues(data.maoriLandData.currentMaoriLandAdjustment, landArea, totalFloorArea);
                    property.rml = self.calculateMaorilandValues(data.maoriLandData.revisedMaoriLandAdjustment, landArea, totalFloorArea, property.cml);
                    property.mlnoOfOwners = data.maoriLandData.numberOfOwners;
                }

                // set the local property store
                this.property = property;
            },
            calculateMaorilandValues: function (adjustment, landArea, totalFloorArea, currentAdjustment) {
                var self = this;
                var maoriLand = {}
                var mlCvNetRate = 0;
                var mlLvNetRate = 0;
                var mlViNetRate = 0;
                var unadjustedValuation = adjustment.unadjustedValuation;
                var mlCapitalValue = unadjustedValuation.capitalValue ? unadjustedValuation.capitalValue : 0;
                var mlLandValue = unadjustedValuation.landValue ? unadjustedValuation.landValue : 0;
                var mlVi = mlCapitalValue - mlLandValue;

                if (totalFloorArea > 0 && mlCapitalValue > 0) {
                    mlCvNetRate = Math.round(mlCapitalValue / totalFloorArea);
                }
                maoriLand.capitalValue = mlCapitalValue;
                maoriLand.cvNetRate = mlCvNetRate;

                if (landArea > 0 && mlLandValue > 0) {
                    mlLvNetRate = Math.round(mlLandValue / (landArea * 10000));
                }
                maoriLand.landValue = mlLandValue;
                maoriLand.lvNetRate = mlLvNetRate;

                if (totalFloorArea > 0 && mlVi > 0) {
                    mlViNetRate = Math.round(mlVi / totalFloorArea);
                }
                maoriLand.rawVi = mlVi;
                maoriLand.vi = Math.round(mlCapitalValue - mlLandValue);
                maoriLand.viNetRate = mlViNetRate;

                var mlSignificance = adjustment.siteSignificanceAdjustmentPercentage ? adjustment.siteSignificanceAdjustmentPercentage : 0;
                var mlOwners = adjustment.multipleOwnerAdjustmentPercentage ? adjustment.multipleOwnerAdjustmentPercentage : 0;
                maoriLand.adjustment = self.round((+mlSignificance + +mlOwners), 1);
                maoriLand.significance = self.round(mlSignificance, 1);
                maoriLand.owners = self.round(mlOwners, 1);

                if(currentAdjustment) {
                    var capitalValueDiff = 0;
                    if (currentAdjustment.capitalValue > 0 && mlCapitalValue > 0) {
                        capitalValueDiff = ((mlCapitalValue * 100) / currentAdjustment.capitalValue) - 100;
                    }
                    maoriLand.capitalValueDiff = (Math.round(capitalValueDiff * 10)) / 10;
                    var landValueDiff = 0
                    if (currentAdjustment.landValue > 0 && mlLandValue > 0) {
                        landValueDiff = ((mlLandValue * 100) / currentAdjustment.landValue) - 100;
                    }
                    maoriLand.landValueDiff = (Math.round(landValueDiff * 10)) / 10;
                    var valueOfImprovementsDiff = 0;
                    if (currentAdjustment.rawVi > 0 && mlVi > 0) {
                        valueOfImprovementsDiff = ((mlVi * 100) / currentAdjustment.rawVi) - 100;
                    }
                    maoriLand.valueOfImprovementsDiff = (Math.round(valueOfImprovementsDiff * 10)) / 10;
                }
                return maoriLand;
            },
            registerPhotoClickHandler: function () {
                var self = this;
                $('.master-details-carousel-photo, .morePhotos').off("click").click(function (evt) {
                    var propertyId = $(this).attr('data-property');
                    var photoId = $(this).data('id');
                    var path = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?propertyId=' + propertyId + '&photoId=' + photoId;
                    var searchWindow = window.open(path, 'PropertyPhotos', 'scrollbars=no,resizable=yes,height=800,width=1024');
                    searchWindow.focus();
                    var timer = setInterval(function () {
                        if (searchWindow.closed == true) {
                            self.getPhotos(propertyId);
                            clearInterval(timer);
                        }
                    }, 1000);
                });
            },
            openPhotoUploader() {
                const self = this;
                const path = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?propertyId=' + this.property.id;
                const searchWindow = window.open(path, 'PropertyPhotos', 'scrollbars=no,resizable=yes,height=800,width=1024');
                searchWindow.focus();
                const timer = setInterval(function () {
                        if (searchWindow.closed == true) {
                            self.getPhotos(self.property.id);
                            clearInterval(timer);
                        }
                    }, 1000);
            },
            getPhotos: function (propertyId) {
                /* Clear any photos */
                $('.master-details-carousel').slick('slickRemove', null, null, true);

                /* Get property photos */
                var self = this;
                var media = jsRoutes.controllers.MediaController.getMediaByOwner(propertyId);
                $.ajax({
                    type: "GET",
                    url: media.url,
                    cache: false,
                    success: function (response) {
                        /* Manipulate the DOM (copied from Monarch 2.0) */
                        self.moreThanTwentyPhotos = response.length > 20;
                        self.twentyFirstPhotoId = self.moreThanTwentyPhotos ? response[20].id : '';
                        $.each(response.slice(0,20), function (index, photo) {
                            // Retrieve the description for the improvement date range
                            const improvementDateRange = self.improvementDateRangeDescriptions[photo.mediaItem.improvementDateRange];
                            // Sort the tags, then for each tag retrieve the tag description.
                            const tags = photo.mediaItem.tags.sort().map(tag => self.tagsDescriptions[tag]);
                            // Array of photo tags and the date range.
                            const displayedTagsArray = [improvementDateRange, ...tags].filter(description => !!description);
                            const displayedTagsText = displayedTagsArray.join(', ');
                            $('.master-details-carousel').slick('slickAdd',
                                `<div>
                                    <img class=\"md-primary master-details-carousel-photo\" data-id=\"${photo.id}\" data-property=\"${photo.ownerId}\" src=\"${photo.mediaItem.largeImageUrl}\"/>
                                    <ul class=\"galleryCaption\">
                                        <li><strong> ${(photo.description ? photo.description : '')} </strong></li>
                                        <li> ${displayedTagsText} </li>
                                        <li> ${self.$options.filters.date(photo.mediaItem.captureDate)} </li>
                                    </ul>
                                    <div style="clear:both" />
                                </div>`
                            );
                        });
                        self.registerPhotoClickHandler();
                    },
                    error: function (response) {
                        self.errorHandler('Failed to get Photos.', response);
                    }
                });
            },
            registerCarousel: function () {
                $(".master-details-carousel").not('.slick-initialized').slick({
                    dots: true,
                    infinite: false,
                    speed: 300,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    swipe: false
                });
            },
            getPropertyPhotos: function (photos, qupid) {
                var propertyPhotos = [];
                var primaryPhoto = 'assets/images/property/addPhotos.png';
                $.each(photos, function (i, obj) {
                    if (obj.isPrimary) {
                        primaryPhoto = obj.mediaItem.smallImageUrl;
                    }
                    propertyPhotos.push({
                        'id': obj.id,
                        'propertyId': obj.ownerId,
                        'qupid': qupid,
                        'link': obj.mediaItem.mediumImageUrl
                    });
                });
                var result = {primary: primaryPhoto, list: propertyPhotos};
                return result;
            },
            errorHandler: function(message, response) {
                this.exception =  'Unexpected Error: ' + message;
                console.error('[Monarch error]: ' + message, response);
                // Authorisation failures attempt to redirect to Auth0 login page, which is not allowed by CORS, resulting in a 0 status.
                // Display login page in this scenario.
                if(response && (response.status == 0)) {
                    window.location = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?home=true';
                }
            },
            openQivs(url) {
                openQivsInNewTab(url);
            },
            openMap() {
                openMapInNewTab(this.mapUrl);
            },
            openGoogleSearchTab() {
                openUrlInNewTab(`https://google.co.nz/search?near=New+Zealand&q=${this.property.address1}+${this.property.address2}`);
            },
            round(value, precision) {
                var multiplier = Math.pow(10, precision || 0);
                return Math.round(value * multiplier) / multiplier;
            },
            copyValref(){
                if(this.property.valRefList){
                    let valRefObj =  this.property.valRefList.find(v => {
                        return v.propertyId === this.selectedPropertyId;
                        });
                    this.$copyText(valRefObj.valuationReference).then(this.succesfullyCopiedValRef());
                }
            },
            succesfullyCopiedValRef(){
                $('#ValRef-to-Clipboard').addClass('md-light');
                setTimeout(function(){$('#ValRef-to-Clipboard').removeClass('md-light');}, 1000);
            },
            getRuralSaleAnalysis: function (saleId) {
                const self = this;
                const m = jsRoutes.controllers.RuralSaleAnalysisController.getRuralSaleAnalysis(saleId);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        self.ruralSales = response;
                    },
                    error: function (response) {
                        if (response.status === 404) {
                            return;
                        }
                        // TODO: Hotfix for v4.1.1 to be revisited in v4.2.0
                        // self.errorHandler(response.message);
                        console.error('Error while fetching Rural Sale Analysis: ', response);
                    }
                });
            },
            async loadRuralRtvValues() {
                await this.$store.dispatch('ruralRtv/getLookupLists');

                const isValidForRuralRtv = (this.ruralRtvCategories.map(i => i.code).includes(this.property.category.code[0])
                    && ['SEPARATE VALUATION','APPORTIONMENT FOLLOWS','NOT APPLICABLE'].includes(this.property.apportionment));

                if (isValidForRuralRtv){
                    await this.$store.dispatch('ruralRtv/getRuralPropertyRtvValues', this.property.qupid);

                    if (this.ruralRtvValues.qpid === this.property.qupid && this.ruralRtvValues.rtv_cv) {
                        const rawMarketEstimateValue = this.ruralRtvValues.rtv_cv
                            ? this.ruralRtvValues.rtv_cv
                            : 0;

                        this.property.marketEstimateValue = this.ruralRtvValues.rtv_cv
                            ? numeral(this.ruralRtvValues.rtv_cv).format('$0,0')
                            : 'N/A';

                        this.property.realTimeLandValue = this.ruralRtvValues.rtv_lv
                            ? numeral(this.ruralRtvValues.rtv_lv).format('$0,0')
                            : 'N/A';

                        this.property.marketEstimateDate = this.ruralRtvValues.rtv_date
                            ? moment(this.ruralRtvValues.rtv_date).tz('Pacific/Auckland').format('DD/MM/YYYY')
                            : '-';

                        this.property.marketEstimateToCapitalValue = this.property.capitalValue && rawMarketEstimateValue
                            ? Math.round(((rawMarketEstimateValue * 100) / this.property.capitalValue) - 100) + '%'
                            : '-';

                        this.property.marketEstimateToCapitalValueUpDown = this.property.capitalValue && rawMarketEstimateValue
                            ? Math.round(((rawMarketEstimateValue * 100) / this.property.capitalValue) - 100)
                            : null;

                        this.property.realTimeLandValueToLandValue = this.property.landValue && this.ruralRtvValues.rtv_lv
                            ? Math.round(((this.ruralRtvValues.rtv_lv * 100) / this.property.landValue) - 100) + '%'
                            : '-';

                        this.property.realTimeLandValueToLandValueUpDown = this.property.landValue && this.ruralRtvValues.rtv_lv
                            ? Math.round(((this.ruralRtvValues.rtv_lv * 100) / this.property.landValue) - 100)
                            : null;
                    }
                }
            },
    },
}
</script>

<style lang="scss" scoped>

.listingControls {
    margin-top: 0;
}

/* Temporary colors for errors taken from bootstrap. */
.bAlert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.bAlert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
}

/* TODO Temporary HACK */
.md-summaryAddress > a > h1 {
    color: #fff;
    margin-left: -5px;
    padding-left: 5px;
    margin-bottom: -5px;
    padding-bottom: 5px;
}

.md-summaryAddress > a:hover > h1 {
    box-shadow:0 0 0 .15rem rgba(255,111,0,.5);
    box-sizing: border-box;
    border-radius:.5rem;

    background:rgba(255,255,255,.25);
}

.icon--flipped {
    transform: scale(-1,1);
}
</style>

<template>
  <div
    id="property-info-skinny"
    class="property-info-skinny table"
    v-if="propertyInfoLoaded"
    style="margin-top: 2rem"
  >
    <div
      class="md-propertyInfo-worksheets"
      v-if="
        propertyInfo.displayRuralWorksheetSection ||
        propertyInfo.displayCommercialWorksheetSection
      "
    >
      <h3>Worksheets</h3>
      <div
        v-if="
          propertyInfo.displayRuralWorksheetSection &&
          !propertyInfo.displayCommercialWorksheetSection
        "
      >
        <ul class="md-worksheet">
          <li
            v-if="propertyInfo.hasRuralWorksheet"
            @click="goToRuralWorksheet()"
          >
            <a data-cy='singleRuralWorksheetHeader' >Rural Worksheet</a>
          </li>
          <li class="title" v-else>Rural Worksheet</li>
          <li class="yes-no">
            <span
              data-cy='singleRuralWorksheetYn'
              v-bind:class="[
                { yesText: propertyInfo.hasRuralWorksheet },
                { noText: !propertyInfo.hasRuralWorksheet },
              ]"
            >
              {{ propertyInfo.hasRuralWorksheet ? "YES" : "NO" }}
            </span>
          </li>
        </ul>
        <ul class="md-worksheet">
          <li
            class="create-worksheet"
            v-if="!propertyInfo.hasRuralWorksheet"
            @click="goToRuralWorksheet()"
          >
            <a data-cy='createRuralWorksheetLink'>Create Rural Worksheet</a>
          </li>
        </ul>
      </div>
      <div
        v-else-if="
          propertyInfo.displayCommercialWorksheetSection &&
          !propertyInfo.displayRuralWorksheetSection
        "
      >
        <ul class="md-worksheet">
          <li
            v-if="propertyInfo.hasCommercialWorksheet"
            @click="goToCommercialWorksheet()"
          >
            <a data-cy='singleCommercialWorksheetHeader'>Commercial Worksheet</a>
          </li>
          <li class="title" v-else data-cy=singleCommercialWorksheetHeader>Commercial Worksheet</li>
          <li class="yes-no">
            <span
              data-cy='singleCommercialWorksheetYn'
              v-bind:class="[
                { yesText: propertyInfo.hasCommercialWorksheet },
                { noText: !propertyInfo.hasCommercialWorksheet },
              ]"
            >
              {{ propertyInfo.hasCommercialWorksheet ? "YES" : "NO" }}
            </span>
          </li>
        </ul>
        <ul class="md-worksheet">
          <li
            class="create-worksheet"
            v-if="!propertyInfo.hasCommercialWorksheet"
            @click="goToCommercialWorksheet()"
          >
            <a data-cy='createCommercialWorksheetLink'>Create Commercial Worksheet</a>
          </li>
        </ul>
      </div>
      <div v-else>
        <ul class="md-worksheet">
          <li
            v-if="propertyInfo.hasRuralWorksheet"
            @click="goToRuralWorksheet()"
          >
            <a data-cy='ruralWorksheetHeader'>Rural Worksheet</a>
          </li>
          <li class="title" data-cy=ruralWorksheetHeader v-else>Rural Worksheet</li>
          <li class="yes-no">
            <span
              data-cy='ruralWorksheetYn'
              v-bind:class="[
                { yesText: propertyInfo.hasRuralWorksheet },
                { noText: !propertyInfo.hasRuralWorksheet },
              ]"
            >
              {{ propertyInfo.hasRuralWorksheet ? "YES" : "NO" }}
            </span>
          </li>
        </ul>
        <ul class="md-worksheet">
          <li
            class="create-worksheet"
            v-if="
              !propertyInfo.hasCommercialWorksheet &&
              !propertyInfo.hasRuralWorksheet
            "
            @click="goToRuralWorksheet()"
          >
            <a data-cy='createRuralWorksheetLink'>Create Rural Worksheet</a>
          </li>
        </ul>
        <ul class="md-worksheet">
          <li
            v-if="propertyInfo.hasCommercialWorksheet"
            @click="goToCommercialWorksheet()"
          >
            <a data-cy=commercialWorksheetHeader>Commercial Worksheet</a>
          </li>
          <li class="title" data-cy='commercialWorksheetHeader' v-else>Commercial Worksheet</li>
          <li class="yes-no">
            <span
              data-cy='commercialWorksheetYn'
              v-bind:class="[
                { yesText: propertyInfo.hasCommercialWorksheet },
                { noText: !propertyInfo.hasCommercialWorksheet },
              ]"
            >
              {{ propertyInfo.hasCommercialWorksheet ? "YES" : "NO" }}
            </span>
          </li>
        </ul>
        <ul class="md-worksheet">
          <li
            class="create-worksheet"
            v-if="
              !propertyInfo.hasCommercialWorksheet &&
              !propertyInfo.hasRuralWorksheet
            "
            @click="goToCommercialWorksheet()"
          >
            <a data-cy='createCommercialWorksheetLink'>Create Commercial Worksheet</a>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="property-info-skinny section">
      <h3 data-cy='propertyInfoHeader'>Property Information</h3>
      <div class="property-info-skinny table">
        <table>
          <tr>
            <td class="title" @click="showRollMaintenance()">
              <a data-cy='consentsLink'>Consents:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasConsents },
                { noText: !propertyInfo.hasConsents },
              ]"
            >
              <span data-cy='consentsYn'>{{ propertyInfo.hasConsents ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td
              class="title"
              @click="
                initQivsLink(qivsUrl, 'subdivisions', qpid, assessmentNumber)
              "
            >
              <a data-cy='subdivisionsLink'>Subdivisions:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasSubdivisions },
                { noText: !propertyInfo.hasSubdivisions },
              ]"
            >
              <span data-cy='subdivisionsYn'>{{ propertyInfo.hasSubdivisions ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td
              class="title"
              @click="initQivsLink(qivsUrl, 'objections', qpid)"
            >
              <a data-cy='objectionsLink'>Objections:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasObjections },
                { noText: !propertyInfo.hasObjections },
              ]"
            >
              <span data-cy='objectionsYn'>{{ propertyInfo.hasObjections ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td
              class="title"
              @click="initQivsLink(qivsUrl, 'valuationData', qpid)"
            >
              <a data-cy='valuationDataLink'>Valuation Data:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasValuationData },
                { noText: !propertyInfo.hasValuationData },
              ]"
            >
              <span data-cy='valuationDataYn'>{{ propertyInfo.hasValuationData ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td
              class="title"
              @click="
                goToSraValues()
              "
            >
              <a data-cy='sraValuesLink'>SRA Values:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasSRAs },
                { noText: !propertyInfo.hasSRAs },
              ]"
            >
              <span data-cy='sraValuesYn'>{{ propertyInfo.hasSRAs ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td
              class="switch-title"
              @click="initQivsLink(qivsUrl, 'autoMas', qpid)"
            >
              <span data-cy='autoMasHeader'>Auto MAS:</span>
            </td>
            <span data-cy='autoMasSwitch'>
            <toggle-button
              :disabled="readonly"
              :value="propertyInfo.isAutoMAS"
              color="#ff0000"
              :width="40"
              :labels="false"
              @change="updatePropertyInfoToggle($event, 'isAutoMAS')"
            />
            </span>
          </tr>
          <tr v-if="propertyInfo.hasRuralWorksheet || propertyInfo.hasCommercialWorksheet">
              <td class="switch-title">
                  <span data-cy="autoWorksheetHeader">Auto Worksheet:</span>
              </td>
              <span data-cy="autoWorksheetSwitch">
                <toggle-button
                  :disabled="readonly"
                  :value="propertyInfo.hasRuralWorksheet ? propertyInfo.isAutoPopulateRuralWorksheet : propertyInfo.isAutoPopulateCommercialWorksheet"
                  color="#ff0000"
                  :width="40"
                  :labels="false"
                  @change="propertyInfo.hasRuralWorksheet ? updatePropertyInfoToggle($event, 'isAutoPopulateRuralWorksheet') : updatePropertyInfoToggle($event, 'isAutoPopulateCommercialWorksheet')"
                />
              </span>
          </tr>
          <tr>
            <td
              class="title"
              @click="openMonarchFloorPlanTab()"
            >
              <a data-cy='floorPlansLink'>Floor Plans:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasFloorPlans },
                { noText: !propertyInfo.hasFloorPlans },
              ]"
            >
              <span data-cy='floorPlansYn'>{{ propertyInfo.hasFloorPlans ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td
              class="title"
              @click="initQivsLink(qivsUrl, 'surveyPlans', qpid)"
            >
              <a data-cy='surveyPlansLink'>Survey Plans:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasSurveyPlans },
                { noText: !propertyInfo.hasSurveyPlans },
              ]"
            >
              <span data-cy='surveyPlansYn'>{{ propertyInfo.hasSurveyPlans ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td class="title" @click="initQivsLink(qivsUrl, 'sitePlans', qpid)">
              <a data-cy='sitePlansLink'>Site Plans:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasSitePlans },
                { noText: !propertyInfo.hasSitePlans },
              ]"
            >
              <span data-cy='sitePlansYn'>{{ propertyInfo.hasSitePlans ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td
              class="title"
              @click="initQivsLink(qivsUrl, 'attachments', qpid)"
            >
              <a data-cy='attachmentsLink'>Attachments:</a>
            </td>
            <td
              v-bind:class="[
                { yesText: propertyInfo.hasAttachments },
                { noText: !propertyInfo.hasAttachments },
              ]"
            >
              <span data-cy='attachmentsYn'>{{ propertyInfo.hasAttachments ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr v-if="isInternalUser">
            <td
              title="Export Results"
              class="title"
              @click="() => scheduleSurveyReport()"
            >
              <a data-cy="reval-survey-link">Reval Survey:</a>
            </td>
            <td
              :title="latestRevalSurveyYear"
              v-bind:class="[
                { yesText: propertyInfo.revalSurveys },
                { noText: !propertyInfo.revalSurveys },
              ]"
            >
              <span data-cy="reval-survey-yn">{{ propertyInfo.revalSurveys ? "YES" : "NO" }}</span>
            </td>
          </tr>
          <tr>
            <td
              class="switch-title"
              @click="initQivsLink(qivsUrl, 'propertyFile', qpid)"
            >
              <span data-cy='propertyFileHeader'>Property File:</span>
            </td>
            <span data-cy='propertyFileSwitch'>
            <toggle-button
              :disabled="readonly"
              :value="propertyInfo.hasPropertyFile"
              color="#ff0000"
              :width="40"
              :labels="false"
              @change="updatePropertyInfoToggle($event, 'hasPropertyFile')"
            />
            </span>
          </tr>
          <tr>
            <td
              class="switch-title"
              @click="initQivsLink(qivsUrl, 'suspectValuation', qpid)"
            >
              <span data-cy='suspectValuationHeader'>Suspect Valuation:</span>
            </td>
            <span data-cy='suspectValuationSwitch'>
            <toggle-button
              :disabled="readonly"
              :value="propertyInfo.isSuspectValuation"
              color="#ff0000"
              :width="40"
              :labels="false"
              @change="updatePropertyInfoToggle($event, 'isSuspectValuation')"
            />
            </span>
          </tr>
        </table>
      </div>
      <property-risk-hazard
        v-if="isInternalUser"
        v-model="propertyInfo.hazardNotes"
        @input="saveHazardNotes"
      />
    </div>
    <alert-modal
      v-if="modal.isOpen"
      :success="modal.mode==='success'"
      :caution="modal.mode==='caution'"
      :warning="modal.mode==='warning'"
      class="reval-survey-alert-modal"
    >
      <h1>{{ modal.heading }}</h1>
      <p v-if="modal.message !== ''" style="white-space:pre-wrap;">{{ modal.message }}</p>
      <template #buttons>
          <div class="alertButtons">
              <button
                  v-if="modal.cancelText"
                  id="errorCancel"
                  class="mdl-button mdl-button--mini lefty"
                  @click="modal.cancelAction"
                  data-cy="modal-cancel-button"
              >
                  {{ modal.cancelText }}
              </button>
              <button
                  v-if="modal.confirmText"
                  id="continue"
                  class="mdl-button mdl-button--mini"
                  @click="modal.confirmAction"
                  data-cy="modal-confirm-button"
              >
                  {{ modal.confirmText }}
              </button>
          </div>
      </template>
    </alert-modal>
  </div>
</template>
<script>
import PropertyRiskHazard from './PropertyRiskHazard.vue';
import { openUrlInNewTab } from '../../utils/QivsUtils';
import commonUtils from "../../utils/CommonUtils";
import { scheduleSurveyReportForQpid } from '../reports/utils';
import { mapState } from "vuex";
import AlertModal from '../common/modal/AlertModal.vue';
import usePropertyInfo from '../../composables/usePropertyInfo.js';
import { saveHazardNotes } from '../../services/PropertyMasterDataController';

const { getPropertyInfo } = usePropertyInfo();

export default {
  props: ["qpid", 'readonly'],
  mixins: [commonUtils],
  data() {
    return {
      monarchPropertyId: null,
      propertyInfo: {},
      propertyInfoLoaded: false,
      rollNumber: null,
      assessmentNumber: null,
      apportionmentCode: null,
      modal: {},
    };
  },
  components: {
    AlertModal,
    PropertyRiskHazard
  },
  computed: {
    ...mapState("userData", [
      "userId",
      "isInternalUser",
      "isRtvUser",
      "isReportingRevalUser",
      "isReadOnlyUser",
      "qivsUrl",
    ]),
    latestRevalSurveyYear() {
        const dateString = this.propertyInfo.revalSurveys?.[0]?.createdDate;
        if (dateString) {
            return new Date(dateString).getFullYear();
        }
        return '';
    },
  },
  methods: {
    async saveHazardNotes(newNotes) {
      this.propertyInfo.hazardNotes = newNotes;
      
      const updatePayload = {
        qpid: this.qpid,
        id: this.monarchPropertyId,
        hazardNotes: newNotes,
      };

      try {
        const response = await saveHazardNotes(updatePayload);
        if (!response.status === 'SUCCESS') {
          console.error(`Failed to save hazard notes`, response);
        }
      } catch (error) {
        console.error('Error updating hazard notes:', error);
      }
    },
    async scheduleSurveyReport() {
        if (!this.isRtvUser && !this.isReportingRevalUser) {
            return;
        }
        const latestSurveyType = this.propertyInfo.revalSurveys?.[0]?.surveyTypeCode;
        const res = await scheduleSurveyReportForQpid(this.qpid, latestSurveyType);
        if (res) {
            this.setRevalSurveyModal();
            this.modal.isOpen = true;
        }
    },
    setRevalSurveyModal() {
      this.modal = {
          mode: 'message',
          isOpen: true,
          heading: 'Export Scheduled',
          message: 'Your export has been acknowledged and can be viewed in View My Reports.',
          messages: [],
          cancelText: 'View My Reports',
          cancelAction: () => {
              this.modal.isOpen = false;
              const routeData = this.$router.resolve({ name: 'report-dashboard-my-reports' });
              window.open(routeData.href, '_blank');
          },
          confirmText: 'OK',
          confirmAction: () => {},
          code: 'EXPORT_SCHEDULED_MESSAGE',
      };
    },

    showRollMaintenance() {
        this.$router.push({ name: 'roll-maintenance',  query: { qpid: this.qpid }});
    },

    goToRuralWorksheet() {
      this.$router.push({ name: 'rural-worksheet',  params: { id: this.qpid }});
    },

    goToCommercialWorksheet() {
      this.$router.push({ name: 'commercial-worksheet',  params: { qpid: this.qpid }});
    },

    goToSraValues() {
      this.$router.push({name: 'property-sra-values', params: {qpid: this.qpid}})
    },

    openMonarchFloorPlanTab(){
        openUrlInNewTab(`${window.location.protocol}//${window.location.hostname}:${window.location.port}/roll-maintenance/floorPlan/${this.qpid}`);
    },

    async getProperty() {
      const self = this;
      let getProperty = jsRoutes.controllers.PropertyMasterData.getProperty(
        this.qpid
      );
      $.ajax({
        type: "GET",
        url: getProperty.url,
        cache: false,
        success: function (response) {
          self.monarchPropertyId = response.property.id;
          self.rollNumber = response.property.rollNumber;
          self.apportionmentCode = response.property.apportionment.code;
          self.assessmentNumber = response.property.assessmentNumber;
        },
        error: function (response) {
          console.log("error fetching property data: " + response);
        },
      });
    },
    async getPropertyInfo() {
      try {
        this.propertyInfo = await getPropertyInfo(this.qpid);
        this.propertyInfoLoaded = true;
      } catch (error) {
        console.log('Error fetching property info: ' + error);
      }
    },

    async updatePropertyInfoToggle(event, field) {
      const updatePayload = {
        qpid: this.qpid,
        fieldName: field,
        value: event.value,
      };

      $.ajax({
        type: "POST",
        url: jsRoutes.controllers.PropertyMasterData.updatePropertyInfoField()
          .url,
        cache: false,
        contentType: "application/json; charset=utf-8",
        data: JSON.stringify(updatePayload),
        dataType: "json",
        success: function (response) {
          console.log("Successfuly Updated Field: ", updatePayload.fieldName);
        },
        error: function (response) {
          self.errorHandler(response);
        },
      });
    },

    resetFieldsBetweenPageLoads() {
      this.monarchPropertyId = null;
      this.rollNumber = null;
      this.apportionmentCode = null;
      this.assessmentNumber = null;
      this.propertyInfo = null;
      this.propertyInfoLoaded = false;
    }
  },
  watch: {
    qpid: {
      immediate: true,
      handler(newQpid) {
        if (!newQpid) {
          return;
        }
        this.resetFieldsBetweenPageLoads();
        this.getProperty();
        this.getPropertyInfo();
      }
    }
  }
};
</script>

<style land="scss" scoped>
.reval-survey-alert-modal {
  h1 {
    font-size: 2rem;
    margin: 0;
  }
  #errorCancel {
    font-weight: 600;
  }
  #continue {
    font-weight: 600;
  }
}

</style>
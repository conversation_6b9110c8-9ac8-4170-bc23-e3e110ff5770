<script setup>
    import { computed, ref } from 'vue';
    import { store } from '../../DataStore';
    import { defineProps,defineEmits } from 'vue';
    import AlertModal from '../../components/common/modal/AlertModal.vue'; // Direct import with alias

    //Accept hazard notes as a prop
    const props = defineProps({
        value:String
    });
    const emit = defineEmits(['input']);
    const draftNotes = ref(props.value);
    const showActionModal = ref(false);
    const note = ref('');
    const action = ref('');
    const requestPlans = ref('');
    const actionTitle = ref('');

    function startAction(actionName, title) {
        action.value = actionName;
        actionTitle.value = title;
        requestPlans.value = false;
        note.value = '';
        showActionModal.value = true;
    }

    // Callback function to handle confirm action
    function actionCallback() {
        emit('input', draftNotes.value);
        showActionModal.value = false;
    }

    function cancelAction() {
        draftNotes.value = props.value;
        showActionModal.value = false;
    }
</script>

<template>
    <div style="margin-top: 2rem">
        <div class="action-buttons">
            <button
                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect button-red-text"
                @click="startAction('requireMoreInformation', 'Notes for Valuer')"
                data-cy="openModalButton"
            >
            RISKS AND HAZARDS - NOTES
            </button>
        </div>
        <div style="margin-top: 1rem; margin-right: 1rem">
            <textarea 
            class="advSearch-text grey-border"
            :value="props.value"
            readonly
            data-cy="readOnlyNotesArea"
            />
        </div> 
        <alert-modal
            v-if="showActionModal"
            warning
            data-cy="alertModal"
        >
            <h3 data-cy="modalTitle">
                {{ actionTitle }}
            </h3>
            <p>Enter any note below:</p>
            <textarea
                class="advSearch-text grey-border"
                v-model="draftNotes"
                maxlength="5000"
                data-cy="notesInput"
            />
            <template #buttons>
                <div class="alertButtons">
                    <button
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="cancelAction"
                        data-cy="cancelButton"
                    >
                        Cancel
                    </button>
                    <button
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="actionCallback"
                        data-cy="confirmButton"
                    >
                        Confirm
                    </button>
                </div>
            </template>
        </alert-modal>
    </div>
</template>
  
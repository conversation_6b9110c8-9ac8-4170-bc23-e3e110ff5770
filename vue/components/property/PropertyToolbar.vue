<template>
    <div class="md-full qvToolbar" data-cy="propertyToolbar">
        <ul class="qvToolbar-links lefty">
            <li id="propertySummary" class="summary property-details-tabs" data-cy="propertyToolbarSummaryTab">
                <a @click="tabActionHandler(0, qpid)">Summary</a>
            </li>
            <li class="extra-details property-details-tabs" data-cy="propertyToolbarExtraDetailsTab">
                <a @click="tabActionHandler(1, property.id)">Extra Details</a>
            </li>
            <li
                id="valuationJob"
                class="valuationjobs-class property-details-tabs"
                v-if="isInternalUser"
                data-cy="propertyToolbarValuationJobsTab"
            >
                <label
                    @click="valuationJobsMenu = !valuationJobsMenu;"
                    class="menu valuation"
                >
                    Valuation Jobs
                    <i class="material-icons valuation">arrow_drop_down</i>
                </label>
                <ul v-if="valuationJobsMenu" class="valJobs-menu mdl-shadow--2dp">
                    <li class="divider" @click="tabActionHandler(2)">New Valuation Job</li>
                    <li
                        v-for="job in valuationJobs" v-bind:ref="job.id+'-valJob'"
                        @click="tabActionHandler(2, job.id)"
                        :key="job.id"
                    >
                        {{ formatDate(job.valuationCreatedDate, 'DD MMMM YYYY') }}
                    </li>
                </ul>
            </li>
            <li id="rollMaintenance" class="property-details-tabs" v-if="isInternalUser" data-cy="propertyToolbarRollMaintenanceTab">
                <a v-on:click="tabActionHandler(3, property.qupid)">Consents</a>
            </li>
            <li class="property-details-tabs" v-if="isInternalUser || isTAUser || externalObjectionAccess">
                <a @click="$router.push({ name: 'objections-search', query: { qpid: property.qupid } })">Objections</a>
            </li>
            <li
                v-if="isQVMapUser"
                class="qv-map property-details-tabs"
                data-cy="propertyToolbarMapTab"
            >
                <a @click="tabActionHandler(4, property.qupid)">Map</a>
            </li>
        </ul>
        <ul class="qvToolbar-icons righty">
            <li class="photo-uploader"
                :data-property="property.id"
                :class="{disabled: isReadOnly == true}"
                title="Upload Photos"
                @click="$emit('open-photo-uploader')"
                data-upgraded=",MaterialButton"
                data-cy="propertyToolbarPhotoUploaderLink"
            >
                <i class="material-icons md-light">&#xE251;</i>
            </li>
        </ul>
        <ul class="qvToolbar-qivs righty">
            <li title="More Photos">
                <div class="morePhotos" v-show="moreThanTwentyPhotos" :data-id="twentyFirstPhotoId" :data-property="property.id"><i class="material-icons">add_to_photos</i>
                </div>
            </li>
            <li
                class="md-qivs"
                @click="openQivs(masterDetailsUrl)"
                data-cy="propertyToolbarQivsLink"
            >
                <label>QIVS</label> <i class="material-icons">call_made</i>
            </li>
            <li
                class="md-qivs"
                @click="openMonarchFloorPlanTab()"
                data-cy="propertyToolbarFloorPlansLink"
            >
                <label>FLOOR PLANS</label>
                <i class="material-icons">call_made</i>
            </li>
            <li
                v-if="isQVMapUser"
                class="md-qvms"
                @click="openQVMap(property.qupid)"
                data-cy="propertyToolbarMapLink"
            >
                <label>MAP</label>
                <i class="material-icons">call_made</i>
            </li>
            <li
                class="md-qvms"
                @click="openGoogleSearchTab()"
                data-cy="propertyToolbarGoogleLink"
            >
                <label>WEB</label> <i class="material-icons icon--flipped">search</i>
            </li>
        </ul>
    </div>
</template>

<script>
import commonUtils from "../../utils/CommonUtils";
import { openQivsInNewTab, openUrlInNewTab, openMap } from '../../utils/QivsUtils';
import formatUtils from '../../utils/FormatUtils';
import { mapState, mapGetters } from "vuex";

export default {
  props: ['property','moreThanTwentyPhotos','twentyFirstPhotoId'],
  mixins: [formatUtils, commonUtils],
  data() {
    return {
        valuationJobs: [],
        valuationJobsMenu: false
    };
  },
  computed: {
    ...mapState('userData', [
        'isInternalUser',
        'isReadOnlyUser',
        'isQVMapUser',
        'isTAUser',
        'externalObjectionAccess',
    ]),
    mapUrl() {
        return this.$store.getters['userData/qvmsMapUrl'](this.property.qupid);
    },
    floorPlanUrl() {
        return this.$store.getters['userData/qivsFloorPlanUrl'](this.property.qupid);
    },
    masterDetailsUrl() {
        return this.$store.getters['userData/qivsMasterDetailsUrl'](this.property.qupid);
    },
    qpid() {
        return this.property && this.property.qupid;
    },
    isReadOnly: function() {
        return this.isReadOnlyUser;
    }
  },
  methods: {
    getHomeValuationJobs() {
        if (this.property && this.property.id) {
            var self = this;
            var homeValuationByProperty = jsRoutes.controllers.HomeValuation.getHomeValuationByProperty(self.property.id);
            $.ajax({
                type: "GET",
                url: homeValuationByProperty.url,
                cache: false,
                success: function (response) {
                    self.valuationJobs = response.homeValuations;
                },
                error: function (response) {
                    console.log('Error while fetching Valuation Jobs: ' + response);
                    self.errorHandler(response);
                }
            });
        }
    },
    tabActionHandler(index, id) {
        if(index == 0) {
            this.$router.push({ name: 'property',  params: { qpid: id }});
        }
        if(index == 1) {
            this.$router.push({ name: 'property-detail',  params: { qpid: id }});
        }
        if(index == 2) {
            if (!id) {
                id = 0; // set to zero for new valuation jobs
            }
            this.$router.push({ name: 'property-home-valuation',  params: { qpid: this.qpid, jobId: id }});
        }
        if(index == 3) {
            this.$router.push({ name: 'consents-search',  query: { qpid: id }});
        }
        if (index == 4) {
            this.openQVMap(this.property.qupid);
        }
    },
    openQivs(url) {
        openQivsInNewTab(url);
    },
    openQVMap(qpid) {
        openMap(qpid)
    },
    openMonarchFloorPlanTab(){
        openUrlInNewTab(`${window.location.protocol}//${window.location.hostname}:${window.location.port}/roll-maintenance/floorPlan/${this.property.qupid}`);
    },
    openGoogleSearchTab() {
        openUrlInNewTab(`https://google.co.nz/search?near=New+Zealand&q=${this.property.address1}+${this.property.address2}`);
    },
  },
  watch: {
      property() {
          this.getHomeValuationJobs();
      }
  },
};
</script>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    proposedValues: {
        type: Object,
    },
    workingNetRate: {
        type: Number,
    },
    currentValues: {
        type: Object,
    },
    currentUnadjustedValues: {
        type: Object,
    },
    unadjustedValues: {
        type: Object,
    },
    showUnadjustedValues: {
        type: Boolean,
        default: false,
    },
});

const reducedClass = 'qv-bg-red';

const hasReducedCV = computed(() => props.proposedValues
        && props.currentValues
        && props.proposedValues.capitalValue < props.currentValues.capitalValue);

const hasReducedLV = computed(() => props.proposedValues
        && props.currentValues
        && props.proposedValues.landValue < props.currentValues.landValue);

const hasReducedUCV = computed(() => props.unadjustedValues
        && props.currentUnadjustedValues
        && props.unadjustedValues.capitalValue < props.currentUnadjustedValues.capitalValue);

const hasReducedULV = computed(() => props.unadjustedValues
        && props.currentUnadjustedValues
        && props.unadjustedValues.landValue < props.currentUnadjustedValues.landValue);

const proposedCV = computed(() => props.proposedValues?.capitalValue || 0);
const proposedLV = computed(() => props.proposedValues?.landValue || 0);
const proposedVI = computed(() => props.proposedValues?.valueOfImprovements || 0);

const proposedUCV = computed(() => props.unadjustedValues?.capitalValue || 0);
const proposedULV = computed(() => props.unadjustedValues?.landValue || 0);
const proposedUVI = computed(() => props.unadjustedValues?.valueOfImprovements || 0);
</script>

<template>
    <div class="qv-flex-row qv-w-full qv-my-2 qv-py-2 qv-justify-space-between">
        <div class="qv-flex-row qv-ml-2">
            <div :class="{[reducedClass]: hasReducedCV}" class="qv-proposed-value" data-cy="proposed-cv">
                <label class="qv-proposed-value-label">Proposed CV</label>
                <span>{{ proposedCV | currency }}</span>
            </div>
            <div :class='{[reducedClass]: hasReducedLV}' class="qv-proposed-value" data-cy="proposed-lv">
                <label class="qv-proposed-value-label">Proposed LV</label>
                <span>{{ proposedLV | currency }}</span>
            </div>
            <div class="qv-proposed-value" data-cy="proposed-vi">
                <label class="qv-proposed-value-label">Proposed VI</label>
                <span>{{ proposedVI | currency }}</span>
            </div>
            <div class="qv-proposed-value" data-cy="proposed-net-rate">
                <label class="qv-proposed-value-label">Proposed Net Rate</label>
                <span>{{ (workingNetRate || 0) | numeral('$0,0') }}</span>
                <span> / m<sup>2</sup></span>
            </div>
        </div>

        <div v-if="showUnadjustedValues" class="qv-flex-row qv-mr-2" data-cy="unadjusted-values-section">
            <div :class="{[reducedClass]: hasReducedUCV}" class="qv-proposed-value" data-cy="proposed-ucv">
                <label class="qv-proposed-value-label">Proposed UCV</label>
                <span>{{ proposedUCV | currency }}</span>
            </div>
            <div :class="{[reducedClass]: hasReducedULV}" class="qv-proposed-value" data-cy="proposed-ulv">
                <label class="qv-proposed-value-label">Proposed ULV</label>
                <span>{{ proposedULV | currency }}</span>
            </div>
            <div class="qv-proposed-value" data-cy="proposed-uvi">
                <label class="qv-proposed-value-label">Proposed UVI</label>
                <span>{{ proposedUVI | currency }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import Format from '@/utils/FormatUtils';

const { formatDecimal } = Format.methods;
const STATUS_PART_CANCELLED = 2;

const props = defineProps(['value']);

const totalLinzArea = computed(() => {
    let total = 0;
    props.value.forEach(title => {
        title.linzTitles.forEach(linz => {
            total += linz.totalArea;
        });
    });
    return formatDecimal(total, 4);
});
const totalQivsArea = computed(() => {
    let total = 0;
    props.value.forEach(title => {
        total += title.landArea ?? 0;
    });
    return formatDecimal(total, 4);
});
const areaMatches = computed(() => totalQivsArea.value === totalLinzArea.value);

function getQivsOwnerOccupiers(title) {
    return [
        ...title.owners.map(o => `Owner - ${o.fullName}`),
        ...title.occupiers.map(o => `Occupier - ${o.fullName}`)
    ];
}
</script>

<template>
    <table class="qv-table" data-cy="property-titles">
        <thead class="qv-bg-darkblue qv-color-light">
        <tr>
            <th></th>
            <th>Category</th>
            <th>Title</th>
            <th>Legal</th>
            <th>Total Area</th>
            <th>Owner/Occupier</th>
        </tr>
        </thead>
        <tbody>
        <template v-for="(title, titleKey) in value">
            <tr :key="title.certificateOfTitle" class="qv-table-row">
                <th class="qv-bg-mediumblue qv-color-light">QIVS</th>
                <td>{{ title.category }}</td>
                <td>{{ title.certificateOfTitle }}</td>
                <td>{{ title.legalDescription }}</td>
                <td :class="titleKey === 0 && !areaMatches ? ['qv-font-bold', 'qv-color-red'] : []">
                    {{ titleKey === 0 ? totalQivsArea : '' }}
                </td>
                <td>
                    <p v-for="(name) in getQivsOwnerOccupiers(title)" :key="name">
                        {{ name }}
                    </p>
                </td>
            </tr>
            <tr v-for="(linz, linzKey) in title.linzTitles" :key="linz.titleId" class="qv-table-row">
                <th :style="++linzKey === title.linzTitles.length ? '' : 'border-bottom: 0'" class="qv-bg-mediumblue qv-color-light">
                    {{ linzKey === 1 ? 'LINZ' : '' }}
                </th>
                <td></td>
                <td>{{ linz.statusType == STATUS_PART_CANCELLED ? `${linz.ct} (P)` : linz.ct }}</td>
                <td>{{ linz.legalDesc }}</td>
                <td>{{ linzKey === 1 && titleKey === 0 ? totalLinzArea : '' }}</td>
                <td>
                    <p v-for="(o) in linz.owners" :key="o.owner">
                        {{ o.owner }}
                    </p>
                </td>
            </tr>
        </template>
        </tbody>
    </table>
</template>

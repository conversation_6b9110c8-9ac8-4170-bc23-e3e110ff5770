<template>
    <div v-if="onHomePage && (isInternalUser || externalObjectionAccess)" class="qvToolbar-wrapper">
        <div class="md-full qvToolbar">
            <div v-if="isMobileView" class="qvToolbar-mobileButton">
                <label @click="showValuationJobs">My Jobs</label>
            </div>
            <territorial-authority-single-select></territorial-authority-single-select>
            <ul class="qvToolbar-links lefty">
                <li v-if="isInternalUser"
                    v-bind:class="{ active: viewValuationJobs }"
                    @click="showValuationJobs"
                    data-cy="valuation-jobs-tab"
                >
                    <label>Valuation Jobs</label>
                </li>

                <li data-cy="building-consents-tab" v-if="!isMobileView && onHomePage && isInternalUser">
                    <a @click="$router.push({ name: 'consents-search' })">Consents</a>
                </li>
                <li data-cy="objections-tab" v-if="!isMobileView">
                    <a @click="$router.push({ name: 'objections-search' })">Objections</a>
                </li>
                <li data-cy="sales-search-tab" v-if="!isMobileView && isInternalUser">
                    <a @click="$router.push({ name: 'sales-dashboard' })">Sales Processing </a>
                </li>
                <li data-cy="linz-search-tab" v-if="!isMobileView">
                    <a @click="$router.push({ name: 'linz-search' })">LINZ Search</a>
                </li>
            </ul>
            <ul class="qvToolbar-qivs righty">
                <li class="md-qivs" @click="openQIVSLink"><label>QIVS</label> <i class="material-icons">call_made</i></li>
            </ul>
            <ul class="qvToolbar-links righty" v-if="!isMobileView || !isInternalUser">
                <li
                    v-if="isInternalUser"
                    data-cy="metrics-dashboard-tab"
                >
                    <a @click="$router.push({ name: 'valuer-metrics' })">My Dashboard</a>
                </li>
                <li data-cy="reports-dashboard-tab">
                    <a @click="$router.push({ name: 'report-dashboard-my-reports' })">Reports</a>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import TerritorialAuthoritySingleSelect from '../filters/TerritorialAuthoritySingleSelect.vue'
    import { EventBus } from '../../EventBus.js';

    export default {
        components: {
            TerritorialAuthoritySingleSelect,
        },
        data: function() {
            return {
                onHomePage : true,
                viewValuationJobs: true,
                mobileValuer: null,
            }
        },
        async created(){
            await this.$store.dispatch('valuersList/getMonarchHomeValuers');
            this.setValuerToUser();
        },
        computed: {
            ...mapState('valuersList', {
                valuers: 'valuers',
            }),
            ...mapState('userData', [
                'isInternalUser',
                'isReportingManager',
                'userId',
                'userFullName',
                'qivsUrl',
                'externalObjectionAccess',
            ]),
            isMobileView: function () {
                var windowWidth = window.innerWidth;
                var isMobileView = false;
                if (windowWidth <= 768) {
                    isMobileView = true;
                }
                return isMobileView;
            },
        },
        methods: {
            setValuerToUser(){
                this.mobileValuer = this.valuers.filter((valuer) => valuer.name == this.userFullName)[0];
            },
            openQIVSLink: function(){
                window.open(this.qivsUrl, "QIVZ");
            },
            showValuationJobs: function() {
                const self = this
                localStorage.setItem(self.userId+'current-home-selection', JSON.stringify({'valuers':['1']}))
                localStorage.setItem(self.userId+'selected-valuers', JSON.stringify(self.mobileValuer ? [self.mobileValuer.id] : []));
                localStorage.setItem(self.userId+'selected-valuers-names', JSON.stringify(self.mobileValuer ? [{"value":self.mobileValuer.ntUsername,"label":self.mobileValuer.name,"id":self.mobileValuer.id}] : []));
                self.onHomePage = true
                self.viewValuationJobs = true
                var event = {}
                event.searchType = 'valuation-jobs'
                event.onHomePage = true
                event.viewValuationJobs = true
                EventBus.$emit('display-content', event)
                EventBus.$emit('clear-ta-dashboard-single-select', event)
            },
            refreshJobs: function(){
                EventBus.$emit('refresh-valuation-jobs')
            },

        },
        mounted: function() {
            const self = this;
            //if (isInternalUser) {
                EventBus.$on('display-content', function(event) {
                    if (event && event.onHomePage) {
                        self.onHomePage = true
                        if (event.viewValuationJobs) {
                            self.viewValuationJobs = true
                        }
                        else {
                            self.viewValuationJobs = false
                        }
                    }
                    else {
                        self.onHomePage = false
                        self.viewValuationJobs = false
                    }
                });
            //}

            EventBus.$emit('load-valuation-job', null);

        },
        destroyed: function() {
            EventBus.$off('display-content', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('display-content', this.listener);
        }
    }
</script>

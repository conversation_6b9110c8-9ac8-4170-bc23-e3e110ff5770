<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div>
        <div class="md-table mdl-shadow--2dp">
            <h2>Valuation Worksheets</h2>
            <div data-cy="valuation-expand-all" class="expandAll" @click="expandTabs()" v-bind:class="[tabState == 'open' ? 'down' : '']">
                <span title="Expand Form" class="mdl-button mdl-js-button mdl-button--icon"><i
                        class="material-icons md-dark"></i></span>
            </div>
            <ul id="tabsElementValuationWorksheet" v-bind:class="[tabState=='open' ? 'hide' : 'QVHV-tabs']">
                <li class="QVHVTab-1" @click="setCurrentTab('Current', 1, true)" data-tab="QVHVTab-1"
                    data-container="currentValuation">
                    <span v-bind:class="[currentTab == 'Current' || tabState == 'open' ? 'is-active' : '']">Current Valuation</span>
                </li>
                <li class="QVHVTab-2" @click="setCurrentTab('Proposed', 2, true)" data-tab="QVHVTab-2"
                    data-container="proposedValuation">
                    <span v-bind:class="[currentTab == 'Proposed' || tabState == 'open' ? 'is-active' : '']">Proposed Valuation</span>
                </li>
                <li class="QVHVTab-3" @click="setCurrentTab('IncomeMethod', 3, true)" data-tab="QVHVTab-3"
                    data-container="incomeMethod">
                    <span v-bind:class="[currentTab == 'IncomeMethod' || tabState == 'open' ? 'is-active' : '']">Income Method</span>
                </li>
                <hr align="left"/>
            </ul>

            <!-- CURRENT VALUATION STARTS -->
            <div class="QVHV-Container currentValuation"
                 v-bind:class="{disabled: readOnly, canOpener: tabState=='open', active: currentTab == 'Current'}"
                 v-if="showCurrentTemplate">

                <ul class="QVHV-tabs hide">
                    <li><span class="is-active">Current Valuation</span></li>
                    <hr align="left"/>
                </ul>
                <div class="QVHV-formSection">

                    <h3>Improvements</h3>
                    <div class="salesAnalysis-table sa-oli ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                            <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                            <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                            <div class="salesAnalysis-th sa-value"><span>Calculated Value</span></div>
                            <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                            <div class="salesAnalysis-th sa-addRemove"></div>
                        </div>
                        <div class="salesAnalysis-row" v-for="improvement,key in worksheet.improvements">
                            <div class="salesAnalysis-td sa-description"><span><input type="text"
                                                                                      v-model="improvement.description"></span>
                            </div>
                            <div class="salesAnalysis-td sa-area"><span><input type="text" @keypress="keyPress"
                                                                               @paste="pasted"
                                                                               v-model="improvement.areaInSquareMeter"
                                                                               @change="calculate('worksheet', 'improvements', key, 1)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-rate"><span><input type="text" @keypress="keyPress"
                                                                               @paste="pasted"
                                                                               v-model="improvement.pricePerSquareMeter"
                                                                               @change="calculate('worksheet', 'improvements', key, 2)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-value"><span><input type="text" @keypress="keyPress"
                                                                                @paste="pasted"
                                                                                v-model="improvement.price"
                                                                                @change="calculate('worksheet', 'improvements', key, 3)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-runnintgTotal">
                                <span>{{ (worksheet.improvements && worksheet.improvements.length == key + 1) ? runningTotal.improvements : ''
                                    }}</span></div>
                            <div class="salesAnalysis-td sa-addRemove">
                                <i class="saRow-add material-icons" @click="add('worksheet', 'improvements', key)">&#xE147;</i>
                                <i v-if="key>0" class="saRow-remove material-icons"
                                   @click="remove('worksheet', 'improvements', key)">&#xE15C;</i>
                            </div>
                        </div>
                    </div>

                    <h3>Land</h3>
                    <div class="salesAnalysis-table sa-land ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                            <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                            <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                            <div class="salesAnalysis-th sa-value"><span>Calculated Value</span></div>
                            <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                            <div class="salesAnalysis-th sa-addRemove"></div>
                        </div>
                        <div class="salesAnalysis-row" v-for="land,key in worksheet.land">
                            <div class="salesAnalysis-td sa-description"><span><input type="text"
                                                                                      v-model="land.description"></span>
                            </div>
                            <div class="salesAnalysis-td sa-area"><span><input type="text" @keypress="keyPress"
                                                                               @paste="pasted"
                                                                               v-model="land.areaInSquareMeter"
                                                                               @change="calculate('worksheet', 'land', key, 1)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-rate"><span><input type="text" @keypress="keyPress"
                                                                               @paste="pasted"
                                                                               v-model="land.pricePerSquareMeter"
                                                                               @change="calculate('worksheet', 'land', key, 2)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-value"><span><input type="text" @keypress="keyPress"
                                                                                @paste="pasted" v-model="land.price"
                                                                                @change="calculate('worksheet', 'land', key, 3)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-runnintgTotal">
                                <span>{{ (worksheet.land && worksheet.land.length == +key + 1) ? runningTotal.land : ''
                                    }}</span></div>
                            <div class="salesAnalysis-td sa-addRemove">
                                <i class="saRow-add material-icons" @click="add('worksheet', 'land', key)">&#xE147;</i>
                                <i v-if="key>0" class="saRow-remove material-icons" @click="remove('worksheet', 'land', key)">&#xE15C;</i>
                            </div>
                        </div>
                    </div>

                    <h3>Chattels</h3>
                    <div class="salesAnalysis-table sa-land ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                            <div class="salesAnalysis-th sa-value"><span>Calculated Value</span></div>
                            <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                            <div class="salesAnalysis-th sa-addRemove"></div>
                        </div>
                        <div class="salesAnalysis-row" v-for="chattel,key in worksheet.chattels">
                            <div class="salesAnalysis-td sa-description-long"><span><input type="text"
                                                                                      v-model="chattel.description"></span>
                            </div>
                            <div class="salesAnalysis-td sa-value"><span><input type="text" @keypress="keyPress"
                                                                                @paste="pasted" v-model="chattel.price"
                                                                                @change="calculate('worksheet', 'chattels', key, 1)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-runnintgTotal">
                                <span>{{ (worksheet.chattels && worksheet.chattels.length == +key + 1) ? runningTotal.chattels : ''}}</span>
                            </div>
                            <div class="salesAnalysis-td sa-addRemove">
                                <i class="saRow-add material-icons" @click="add('worksheet', 'chattels', key)">&#xE147;</i>
                                <i v-if="key>0" class="saRow-remove material-icons" @click="remove('worksheet', 'chattels', key)">&#xE15C;</i>
                            </div>
                        </div>
                    </div>

                    <h3>Worksheet Values</h3>
                    <div class="salesAnalysis-table sa-details ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th"><span>Total Improvements</span></div>
                            <div class="salesAnalysis-th"><span>Land Value</span></div>
                            <div class="salesAnalysis-th"><span>Market Value (ex Chattels)</span></div>
                            <div class="salesAnalysis-th"><span>Chattels</span></div>
                            <div class="salesAnalysis-th"><span>Market Value</span></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(calculateRunningTotal('worksheet', 'improvements', worksheet.improvements ? worksheet.improvements.length : 0))"></span>
                            </div>
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(calculateRunningTotal('worksheet', 'land', worksheet.land ? worksheet.land.length : 0))"></span>
                            </div>
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(+calculateRunningTotal('worksheet', 'improvements', worksheet.improvements ? worksheet.improvements.length : 0) + +calculateRunningTotal('worksheet', 'land', worksheet.land ? worksheet.land.length : 0))"></span>
                            </div>
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(calculateRunningTotal('worksheet', 'chattels', worksheet.chattels ? worksheet.chattels.length : 0))"></span>
                            </div>
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(+calculateRunningTotal('worksheet', 'improvements', worksheet.improvements ? worksheet.improvements.length : 0) + +calculateRunningTotal('worksheet', 'land', worksheet.land ? worksheet.land.length : 0) + +calculateRunningTotal('worksheet', 'chattels', worksheet.chattels ? worksheet.chattels.length : 0))"></span>
                            </div>
                        </div>
                    </div>

                    <h3>Valuation Summary</h3>
                    <div class="salesAnalysis-table sa-details ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th">
                                <span>Direct Comparison Method</span>
                            </div>
                            <div class="salesAnalysis-th">
                                <span>Income Method</span>
                            </div>
                            <div class="salesAnalysis-th">
                                <span>Net Rate Method</span>
                            </div>
                            <div class="salesAnalysis-th"></div>
                            <div class="salesAnalysis-th"></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td">
                                <span>
                                    <input type="text" @keypress="keyPress" @paste="pasted" @change="saveWorksheet(false, false)" v-model="worksheet.directComparisonMarketValue">
                                </span>
                            </div>
                            <div class="salesAnalysis-td calculated">
                                <span>
                                    <input type="text" readOnly
                                          :value="formatPrice(incomeMethodWorksheet.marketValue)">
                                </span>
                            </div>
                            <div class="salesAnalysis-td calculated">
                                <span>
                                    <input type="text" readOnly
                                           :value="formatPrice(+calculateRunningTotal('worksheet', 'improvements', worksheet.improvements ? worksheet.improvements.length : 0) + +calculateRunningTotal('worksheet', 'land', worksheet.land ? worksheet.land.length : 0) + +calculateRunningTotal('worksheet', 'chattels', worksheet.chattels ? worksheet.chattels.length : 0))">
                                </span>
                            </div>
                            <div class="salesAnalysis-td"></div>
                            <div class="salesAnalysis-td"></div>
                        </div>
                    </div>

                    <h3>Adopted Values for Report</h3>
                    <div class="salesAnalysis-table sa-details ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th"><span>Total Improvements</span></div>
                            <div class="salesAnalysis-th"><span>Land Value</span></div>
                            <div class="salesAnalysis-th"><span>Market Value (ex Chattels)</span></div>
                            <div class="salesAnalysis-th"><span>Chattels</span></div>
                            <div class="salesAnalysis-th"><span>Market Value</span></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateAdoptedValues(true)"
                                                                       v-model="worksheet.adoptedTotalImprovements"></span>
                            </div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateAdoptedValues(true)"
                                                                       v-model="worksheet.adoptedLandValue"></span>
                            </div>
                            <div class="salesAnalysis-td disabled"><span><input type="text"
                                                                                :value="formatPrice(adoptedValues.marketValueExChattels)"
                                                                                tabindex="-1"></span></div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateAdoptedValues(true)"
                                                                       v-model="worksheet.adoptedChattels"></span></div>
                            <div class="salesAnalysis-td disabled"><span><input type="text"
                                                                                :value="formatPrice(adoptedValues.marketValue)"
                                                                                tabindex="-1"></span></div>
                        </div>
                    </div>
                    <input type="checkbox" id="Mortgagee Value" class="hideshowMortgagee-trigger"
                           v-model="worksheet.includeMortgageeValuesInReport" @change="emptyOptionalVal('mortgagee')">
                    <label for="Mortgagee Value" class="hideshowMortgagee-trigger"><h3>Mortgagee Sales Values</h3>
                    </label>
                    <div class="salesAnalysis-table mortgageeValues">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th"><span>Discount % Low</span></div>
                            <div class="salesAnalysis-th"><span>Discount % High</span></div>
                            <div class="salesAnalysis-th"><span>Value Range Low</span></div>
                            <div class="salesAnalysis-th"><span>Value Range High</span></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="lowDiscountKeypress"
                                                                       @keyup="discountKeyup('low')" @paste="pasted"
                                                                       @change="calculateMortVal('low')"
                                                                       v-model="worksheet.lowMortgageeDiscountPercentage"></span>
                            </div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="highDiscountKeypress"
                                                                       @keyup="discountKeyup('high')" @paste="pasted"
                                                                       @change="calculateMortVal('high')"
                                                                       v-model="worksheet.highMortgageeDiscountPercentage"></span>
                            </div>
                            <div class="salesAnalysis-td"><span><input type="text" v-model="worksheet.lowMortgageeValue"
                                                                       @keypress="keyPress" @paste="pasted"
                                                                       tabindex="-1"></span></div>
                            <div class="salesAnalysis-td"><span><input type="text"
                                                                       v-model="worksheet.highMortgageeValue"
                                                                       @keypress="keyPress" @paste="pasted"
                                                                       tabindex="-1"></span></div>
                        </div>
                    </div>

                    <input type="checkbox" id="HNZPeriods" class="hideshowHNZPeriods-trigger"
                           v-model="worksheet.includeHousingNzEstimatesInReport" @change="emptyOptionalVal('hnz')">
                    <label for="HNZPeriods" class="hideshowHNZPeriods-trigger"><h3>
                        Housing New Zealand Marketing Sales Periods</h3></label>
                    <div class="salesAnalysis-table hnzPeriods">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th"><span>One to Three Months</span></div>
                            <div class="salesAnalysis-th"><span>Three to Six Months</span></div>
                            <div class="salesAnalysis-th"><span>Six to12 Months</span></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="formatPeriodsValues"
                                                                       v-model="worksheet.housingNzEstimateOneToThreeMonths"></span>
                            </div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="formatPeriodsValues"
                                                                       v-model="worksheet.housingNzEstimateThreeToSixMonths"></span>
                            </div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="formatPeriodsValues"
                                                                       v-model="worksheet.housingNzEstimateSixToTwelveMonths"></span>
                            </div>
                        </div>
                    </div>

                    <input type="checkbox" id="likelyRealisablePrice" class="hideshowLikelyRealisablePrice-trigger"
                           v-model="worksheet.includeLikelyRealisablePriceInReport" @change="emptyOptionalVal('likelyRealisablePrice')">
                    <label for="likelyRealisablePrice" class="hideshowLikelyRealisablePrice-trigger"><h3>
                        Likely Realisable Price Assuming Constrained Circumstances</h3></label>
                    <div class="salesAnalysis-table likelyRealisablePrice">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th"><span>Total Improvements</span></div>
                            <div class="salesAnalysis-th"><span>Land Value</span></div>
                            <div class="salesAnalysis-th"><span>Market Value (ex Chattels)</span></div>
                            <div class="salesAnalysis-th"><span>Chattels</span></div>
                            <div class="salesAnalysis-th"><span>Market Value</span></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateLikelyRealisableValues(true)"
                                                                       v-model="worksheet.likelyRealisableTotalImprovements"></span>
                            </div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateLikelyRealisableValues(true)"
                                                                       v-model="worksheet.likelyRealisableLandValue"></span>
                            </div>
                            <div class="salesAnalysis-td disabled"><span><input type="text"
                                                                                :value="formatPrice(likelyRealisablePrice.marketValueExChattels)"
                                                                                tabindex="-1"></span></div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateLikelyRealisableValues(true)"
                                                                       v-model="worksheet.likelyRealisableChattels"></span></div>
                            <div class="salesAnalysis-td disabled"><span><input type="text"
                                                                                :value="formatPrice(likelyRealisablePrice.marketValue)"
                                                                                tabindex="-1"></span></div>
                        </div>
                    </div>
                    <div class="salesAnalysis-table likelyRealisablePrice">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th"><span>Explanation of Constraints on Valuation</span></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td"><span><input type="text" maxlength="1500" v-model="worksheet.valuationConstraintsExplanation"></span>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- PROPOSED VALUATION STARTS -->

            <div class="QVHV-Container proposedValuation"
                 v-bind:class="{disabled: readOnly, canOpener: tabState=='open', active: currentTab == 'Proposed'}">
                <ul class="QVHV-tabs">
                    <li><span class="is-active">Proposed Valuation</span></li>
                    <hr align="left"/>
                </ul>
                <div class="QVHV-formSection">

                    <h3>Improvements</h3>
                    <div class="salesAnalysis-table sa-oli ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                            <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                            <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                            <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                            <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                            <div class="salesAnalysis-th sa-addRemove"></div>
                        </div>
                        <div class="salesAnalysis-row" v-for="improvement,key in proposedWorksheet.improvements">

                            <div class="salesAnalysis-td sa-description"><span><input type="text"
                                                                                      v-model="improvement.description"></span>
                            </div>
                            <div class="salesAnalysis-td sa-area"><span><input type="text" @keypress="keyPress"
                                                                               @paste="pasted"
                                                                               v-model="improvement.areaInSquareMeter"
                                                                               @change="calculate('proposedWorksheet', 'improvements', key, 1)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-rate"><span><input type="text" @keypress="keyPress"
                                                                               @paste="pasted"
                                                                               v-model="improvement.pricePerSquareMeter"
                                                                               @change="calculate('proposedWorksheet', 'improvements', key, 2)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-value"><span><input type="text" @keypress="keyPress"
                                                                                @paste="pasted"
                                                                                v-model="improvement.price"
                                                                                @change="calculate('proposedWorksheet', 'improvements', key, 3)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-runnintgTotal">
                                <span>{{ (proposedWorksheet.improvements && proposedWorksheet.improvements.length == key + 1) ? runningTotal.proposedImprovements : ''
                                    }}</span></div>
                            <div class="salesAnalysis-td sa-addRemove">
                                <i class="saRow-add material-icons" @click="add('proposedWorksheet', 'improvements', key)">&#xE147;</i>
                                <i v-if="key>0" class="saRow-remove material-icons"
                                   @click="remove('proposedWorksheet', 'improvements', key)">&#xE15C;</i>
                            </div>
                        </div>
                    </div>

                    <h3>Land</h3>
                    <div class="salesAnalysis-table sa-land ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                            <div class="salesAnalysis-th sa-area"><span>Area</span></div>
                            <div class="salesAnalysis-th sa-rate"><span>Rate /m<sup>2</sup></span></div>
                            <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                            <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                            <div class="salesAnalysis-th sa-addRemove"></div>
                        </div>
                        <div class="salesAnalysis-row" v-for="land,key in proposedWorksheet.land">
                            <div class="salesAnalysis-td sa-description"><span><input type="text"
                                                                                      v-model="land.description"></span>
                            </div>
                            <div class="salesAnalysis-td sa-area"><span><input type="text" @keypress="keyPress"
                                                                               @paste="pasted"
                                                                               v-model="land.areaInSquareMeter"
                                                                               @change="calculate('proposedWorksheet', 'land', key, 1)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-rate"><span><input type="text" @keypress="keyPress"
                                                                               @paste="pasted"
                                                                               v-model="land.pricePerSquareMeter"
                                                                               @change="calculate('proposedWorksheet', 'land', key, 2)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-value"><span><input type="text" @keypress="keyPress"
                                                                                @paste="pasted" v-model="land.price"
                                                                                @change="calculate('proposedWorksheet', 'land', key, 3)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-runnintgTotal">
                                <span>{{ (proposedWorksheet.land && proposedWorksheet.land.length == +key + 1) ? runningTotal.proposedLand : ''
                                    }}</span></div>
                            <div class="salesAnalysis-td sa-addRemove">
                                <i class="saRow-add material-icons" @click="add('proposedWorksheet', 'land', key)">&#xE147;</i>
                                <i v-if="key>0" class="saRow-remove material-icons" @click="remove('proposedWorksheet', 'land', key)">&#xE15C;</i>
                            </div>
                        </div>
                    </div>

                    <h3>Chattels</h3>
                    <div class="salesAnalysis-table sa-land ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th sa-description"><span>Description</span></div>
                            <div class="salesAnalysis-th sa-value"><span>Analysed Value</span></div>
                            <div class="salesAnalysis-th sa-runnintgTotal"><span></span></div>
                            <div class="salesAnalysis-th sa-addRemove"></div>
                        </div>
                        <div class="salesAnalysis-row" v-for="chattel,key in proposedWorksheet.chattels">
                            <div class="salesAnalysis-td sa-description-long"><span><input type="text"
                                                                                           v-model="chattel.description"></span>
                            </div>
                            <div class="salesAnalysis-td sa-value"><span><input type="text" @keypress="keyPress"
                                                                                @paste="pasted" v-model="chattel.price"
                                                                                @change="calculate('proposedWorksheet', 'chattels', key, 1)"></span>
                            </div>
                            <div class="salesAnalysis-td sa-runnintgTotal">
                                <span>{{ (proposedWorksheet.chattels && proposedWorksheet.chattels.length == +key + 1) ? runningTotal.proposedChattels : ''}}</span>
                            </div>
                            <div class="salesAnalysis-td sa-addRemove">
                                <i class="saRow-add material-icons" @click="add('proposedWorksheet', 'chattels', key)">&#xE147;</i>
                                <i v-if="key>0" class="saRow-remove material-icons" @click="remove('proposedWorksheet', 'chattels', key)">&#xE15C;</i>
                            </div>
                        </div>
                    </div>

                    <h3>Worksheet Values</h3>
                    <div class="salesAnalysis-table sa-details ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th"><span>Total Improvements</span></div>
                            <div class="salesAnalysis-th"><span>Land Value</span></div>
                            <div class="salesAnalysis-th"><span>Chattels</span></div>
                            <div class="salesAnalysis-th"><span>Market Value (ex Chattels)</span></div>
                            <div class="salesAnalysis-th"><span>Market Value</span></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(calculateRunningTotal('proposedWorksheet', 'improvements', proposedWorksheet.improvements ? proposedWorksheet.improvements.length : 0))"></span>
                            </div>
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(calculateRunningTotal('proposedWorksheet', 'land', proposedWorksheet.land ? proposedWorksheet.land.length : 0))"></span>
                            </div>
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(calculateRunningTotal('proposedWorksheet', 'chattels', proposedWorksheet.chattels ? proposedWorksheet.chattels.length : 0))"></span>
                            </div>
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(+calculateRunningTotal('proposedWorksheet', 'improvements', proposedWorksheet.improvements ? proposedWorksheet.improvements.length : 0) + +calculateRunningTotal('proposedWorksheet', 'land', proposedWorksheet.land ? proposedWorksheet.land.length : 0))"></span>
                            </div>
                            <div class="salesAnalysis-td calculated"><span><input type="text"
                                                                                  :value="formatPrice(+calculateRunningTotal('proposedWorksheet', 'improvements', proposedWorksheet.improvements ? proposedWorksheet.improvements.length : 0) + +calculateRunningTotal('proposedWorksheet', 'land', proposedWorksheet.land ? proposedWorksheet.land.length : 0) + +calculateRunningTotal('proposedWorksheet', 'chattels', proposedWorksheet.chattels ? proposedWorksheet.chattels.length : 0))"></span>
                            </div>
                        </div>
                    </div>
                    <h3>Adopted Values for Report</h3>
                    <div class="salesAnalysis-table sa-details ">
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-th"><span>Total Improvements</span></div>
                            <div class="salesAnalysis-th"><span>Land Value</span></div>
                            <div class="salesAnalysis-th"><span>Chattels</span></div>
                            <div class="salesAnalysis-th"><span>Market Value (ex Chattels)</span></div>
                            <div class="salesAnalysis-th"><span>Market Value</span></div>
                        </div>
                        <div class="salesAnalysis-row">
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateAdoptedProposedValues(true)"
                                                                       v-model="proposedWorksheet.adoptedTotalImprovements"></span>
                            </div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateAdoptedProposedValues(true)"
                                                                       v-model="proposedWorksheet.adoptedLandValue"></span>
                            </div>
                            <div class="salesAnalysis-td"><span><input type="text" @keypress="keyPress" @paste="pasted"
                                                                       @change="calculateAdoptedProposedValues(true)"
                                                                       v-model="proposedWorksheet.adoptedChattels"></span></div>
                            <div class="salesAnalysis-td disabled"><span><input type="text"
                                                                                :value="formatPrice(adoptedValues.proposedMarketValueExChattels)"
                                                                                tabindex="-1"></span></div>

                            <div class="salesAnalysis-td disabled"><span><input type="text"
                                                                                :value="formatPrice(adoptedValues.proposedMarketValue)"
                                                                                tabindex="-1"></span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- INCOME METHOD STARTS -->
            <income-method :readOnly="readOnly" :tabState="tabState" :currentTab="currentTab"></income-method>

        </div>
        <div class="QVHV-buttons" v-bind:class="{disabled: readOnly}">
            <div class="QVHV-buttons-left" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="primary" v-on:click="saveWorksheet(false, true)">Save</button>
            </div>
            <div class="QVHV-buttons-right" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="secondary" v-on:click="previousStep()">Back</button>
                <button class="primary" v-on:click="saveWorksheet(true, true)">Next Step</button>
            </div>
        </div>
    </div>
</template>
<script>
    import {EventBus} from '../../EventBus.js';
    import {store} from '../../DataStore';
    import numeral from 'numeral';
    import deepEqual from 'deep-equal';
    import IncomeMethod from './worksheet/IncomeMethod.vue';

    export default {
        props: ['readOnly'],
        data: function() {
            return {
                proposedWorksheet: {},
                worksheet: {},
                incomeMethodWorksheet: {},
                currentTab: 'Current',
                tabState: 'closed',
                isDataSaving: false,
                showCurrentTemplate: true,
                refreshCurrentTemplate: false,
                runningTotal: {
                    improvements: 0,
                    land: 0,
                    chattels: 0,

                    proposedImprovements: 0,
                    proposedLand: 0,
                    proposedChattels: 0
                },
                adoptedValues: {
                    marketValueExChattels: 0,
                    marketValue: 0,

                    proposedMarketValueExChattels: 0,
                    proposedMarketValue: 0
                },
                mortgageeValues: {
                    lastValidLowDiscount: 0,
                    lastValidHighDiscount: 0
                },
                likelyRealisablePrice: {
                    marketValueExChattels: 0,
                    marketValue: 0,
                },
                homeValuationId: undefined,
                worksheetCopy: null,
                proposedWorksheetCopy: null,
                incomeMethodWorksheetCopy: null,
                isDirty: false
            }
        },
        components: {
            IncomeMethod
        },

        methods: {
            expandTabs: function() {
                const self = this
                if (self.tabState == 'closed') {
                    self.tabState = 'open'
                }
                else {
                    self.tabState = 'closed'
                }
            },
            setCurrentTab: function(name, index, save) {
                const self = this;
                self.currentTab = name;
                $('#tabsElementValuationWorksheet').find('hr')
                    .removeClass()
                    .addClass('QVHVTab-' + index);
                if (save) {
                    setTimeout(function() {
                        self.saveWorksheet(false, true)
                    }, 1000);
                }
            },
            refreshView: function() {
                const self = this;
                self.showCurrentTemplate = !self.showCurrentTemplate;
                if (!self.showCurrentTemplate) {
                    self.refreshCurrentTemplate = true
                }
            },
            add: function(worksheet, item, key) {
                const self = this;
                if (!self[worksheet][item]) {
                    self[worksheet][item] = [];
                }
                self[worksheet][item].splice(key + 1, 0, {});
                self.refreshView();
            },
            remove: function(worksheet, item, key) {
                const self = this;
                self[worksheet][item].splice(key, 1);
                if (worksheet === 'worksheet') {
                    self.runningTotal[item] = self.calculateRunningTotal(worksheet, item, self[worksheet][item].length);
                }
                else if (worksheet === 'proposedWorksheet') {
                    var itemToUpper = item.charAt(0).toUpperCase() + item.substr(1);
                    self.runningTotal[item] = self.calculateRunningTotal(worksheet, item, self[worksheet][item].length);
                }
                self.refreshView();
            },
            keyPress: function(event) {
                if (event.charCode != 46 && event.charCode > 31 && (event.charCode < 48 || event.charCode > 57)) {
                    return event.preventDefault();
                }
            },
            pasted: function(clipboardEvent) {
                var clipboardData, pastedData;
                // Stop data actually being pasted into the text field
                clipboardEvent.stopPropagation();
                clipboardEvent.preventDefault();

                // Get pasted data via clipboard API
                clipboardData = clipboardEvent.clipboardData || window.clipboardData;
                pastedData = clipboardData.getData('Text');

                if ($.isNumeric(pastedData)) {
                    var result = document.execCommand('paste', false, pastedData);
                    if (!result) {
                        document.execCommand('insertText', false, pastedData);
                    }
                } else {
                    return false;
                }
            },
            formatPrice: function(price, whole) {
                if(whole) {
                    return numeral(price).format('$0,0');

                } else {
                    return numeral(price).format('$0,0.[00000]');
                }
            },
            calculate: function(worksheet, item, key, index, initialLoading) {
                var self = this;
                // var imp = self.worksheet[item][key];
                var imp = self[worksheet][item][key];
                var pricePerSquareMeter = numeral(imp.pricePerSquareMeter).value();
                var price = numeral(imp.price).value();
                if(!initialLoading) {
                    if(index == 3) {
                        if(imp.areaInSquareMeter && imp.areaInSquareMeter > 0) {
                            imp.pricePerSquareMeter = price / imp.areaInSquareMeter;
                        }
                    } else {
                        if (imp.areaInSquareMeter == 0) {
                            imp.pricePerSquareMeter = "";
                        } else if (imp.pricePerSquareMeter == 0) {
                            imp.price = 0;
                        } else if (imp.pricePerSquareMeter && imp.areaInSquareMeter && imp.areaInSquareMeter > 0) {
                            imp.price = imp.areaInSquareMeter * pricePerSquareMeter;
                        } else if (imp.price && imp.areaInSquareMeter && !imp.pricePerSquareMeter && imp.areaInSquareMeter > 0) {
                            imp.pricePerSquareMeter = price / imp.areaInSquareMeter;
                        }
                    }
                }
                if (worksheet === 'worksheet') {
                    self.runningTotal[item] = self.formatPrice(self.calculateRunningTotal(worksheet, item, self[worksheet][item].length));
                }
                else if (worksheet === 'proposedWorksheet') {
                    var itemUpperCase = item.charAt(0).toUpperCase() + item.substr(1);
                    self.runningTotal['proposed'+itemUpperCase] = self.formatPrice(self.calculateRunningTotal(worksheet, item, self[worksheet][item].length));
                }
                if (imp.pricePerSquareMeter || imp.pricePerSquareMeter == 0) imp.pricePerSquareMeter = self.formatPrice(imp.pricePerSquareMeter, index == 3 ? true : false);
                if (imp.price || imp.price == 0) imp.price = self.formatPrice(numeral(imp.price).value());
                self.saveWorksheet(false, false);
            },
            calculateRunningTotal: function(worksheet, item, key) {
                var self = this;
                var total = 0;
                $.each(self[worksheet][item], function(index, imp) {
                    if (index <= key && imp.price) {
                        var price = numeral(imp.price).value();
                        total = total + price;
                    }
                });
                return total;
            },
            getRTVAdoptedValues: function(qupid){
                let self = this;
                let controllerRoute = jsRoutes.controllers.PropertyMasterData.getStatsSummary(qupid);
                return $.ajax({
                    type: "GET",
                    url: controllerRoute.url,
                    cache: false,
                    success: function (response) {
                        let summary = {};
                        summary.marketEstimateValue = response.marketEstimateValue;
                        summary.marketEstimateValueGross = response.marketEstimateValueGross;
                        summary.estimatedLandValue = response.estimatedLandValue;
                        return summary;
                    },
                    error: function (response) {
                        console.error('ERR-VAL-001: Error fetching stats summary for qupid: ' + qupid, response);
                        self.errorHandler(response);
                    }
                });
            },
           
            calculateAdoptedValuesVerifiedReport: async function(save, qupid) {
                let self = this;
                let estimateValues = await self.getRTVAdoptedValues(qupid);
                let adoptedMarketValueExChattels = numeral(estimateValues.marketEstimateValue).value(); 
                let adoptedMarketValue = numeral(estimateValues.marketEstimateValueGross).value();
                let adoptedLandValue = self.worksheet.adoptedLandValue ? numeral(self.worksheet.adoptedLandValue).value() : numeral(estimateValues.estimatedLandValue).value();
                let adoptedChattels = self.worksheet.adoptedChattels ? numeral(self.worksheet.adoptedChattels).value() : numeral(adoptedMarketValue - adoptedMarketValueExChattels).value();
                let adoptedTotalImprovements = self.worksheet.adoptedTotalImprovements ? numeral(self.worksheet.adoptedTotalImprovements).value() : numeral(adoptedMarketValueExChattels - adoptedLandValue).value();       
                self.adoptedValues.marketValueExChattels =  adoptedMarketValueExChattels ? self.formatPrice(adoptedMarketValueExChattels) : 0;
                self.adoptedValues.marketValue =  adoptedMarketValueExChattels ? self.formatPrice(adoptedMarketValue) : 0;
                self.worksheet.adoptedLandValue = self.formatPrice(adoptedLandValue);
                self.worksheet.adoptedTotalImprovements = self.formatPrice(adoptedTotalImprovements);
                self.worksheet.adoptedChattels = self.formatPrice(adoptedChattels);
                /* HACK setTimeout to wait for main thread to finish before saving as worksheet was getting overwritten from other save */
                if (save) {
                    setTimeout(function() {
                        self.saveWorksheet(false, true, true);
                    }, 1000);
                }
            },
            calculateAdoptedValues: function(save) {
                var self = this;
                var adoptedTotalImprovements = numeral(self.worksheet.adoptedTotalImprovements).value();
                var adoptedLandValue = numeral(self.worksheet.adoptedLandValue).value();
                var adoptedChattels = numeral(self.worksheet.adoptedChattels).value();
                self.adoptedValues.marketValueExChattels = self.formatPrice(+(adoptedTotalImprovements ? adoptedTotalImprovements : 0) + +(adoptedLandValue ? adoptedLandValue : 0));
                self.adoptedValues.marketValue = self.formatPrice(+(adoptedTotalImprovements ? adoptedTotalImprovements : 0) + +(adoptedLandValue ? adoptedLandValue : 0) + +(adoptedChattels ? adoptedChattels : 0));
                if (self.worksheet.adoptedTotalImprovements) self.worksheet.adoptedTotalImprovements = self.formatPrice(self.worksheet.adoptedTotalImprovements);
                if (self.worksheet.adoptedLandValue) self.worksheet.adoptedLandValue = self.formatPrice(self.worksheet.adoptedLandValue);
                if (self.worksheet.adoptedChattels) self.worksheet.adoptedChattels = self.formatPrice(self.worksheet.adoptedChattels);
                if(save) self.saveWorksheet(false, false);
            },
            calculateAdoptedProposedValues: function(save) {
                var self = this;
                var adoptedProposedTotalImprovements = numeral(self.proposedWorksheet.adoptedTotalImprovements).value();
                var adoptedProposedLandValue = numeral(self.proposedWorksheet.adoptedLandValue).value();
                var adoptedProposedChattels = numeral(self.proposedWorksheet.adoptedChattels).value();
                self.adoptedValues.proposedMarketValueExChattels = self.formatPrice(+(adoptedProposedTotalImprovements ? adoptedProposedTotalImprovements : 0) + +(adoptedProposedLandValue ? adoptedProposedLandValue : 0));
                self.adoptedValues.proposedMarketValue = self.formatPrice(+(adoptedProposedTotalImprovements ? adoptedProposedTotalImprovements : 0) + +(adoptedProposedLandValue ? adoptedProposedLandValue : 0) + +(adoptedProposedChattels ? adoptedProposedChattels : 0));
                if (self.proposedWorksheet.adoptedTotalImprovements) self.proposedWorksheet.adoptedTotalImprovements = self.formatPrice(self.proposedWorksheet.adoptedTotalImprovements);
                if (self.proposedWorksheet.adoptedLandValue) self.proposedWorksheet.adoptedLandValue = self.formatPrice(self.proposedWorksheet.adoptedLandValue);
                if (self.proposedWorksheet.adoptedChattels) self.proposedWorksheet.adoptedChattels = self.formatPrice(self.proposedWorksheet.adoptedChattels);
                if(save) self.saveWorksheet(false, false);
            },
            calculateLikelyRealisableValues: function(save) {
                var self = this;
                var likelyRealisableTotalImprovements = numeral(self.worksheet.likelyRealisableTotalImprovements).value();
                var likelyRealisableLandValue = numeral(self.worksheet.likelyRealisableLandValue).value();
                var likelyRealisableChattels = numeral(self.worksheet.likelyRealisableChattels).value();
                self.likelyRealisablePrice.marketValueExChattels = self.formatPrice(+(likelyRealisableTotalImprovements ? likelyRealisableTotalImprovements : 0) + +(likelyRealisableLandValue ? likelyRealisableLandValue : 0));
                self.likelyRealisablePrice.marketValue = self.formatPrice(+(likelyRealisableTotalImprovements ? likelyRealisableTotalImprovements : 0) + +(likelyRealisableLandValue ? likelyRealisableLandValue : 0) + +(likelyRealisableChattels ? likelyRealisableChattels : 0));
                if (self.worksheet.likelyRealisableTotalImprovements) self.worksheet.likelyRealisableTotalImprovements = self.formatPrice(self.worksheet.likelyRealisableTotalImprovements);
                if (self.worksheet.likelyRealisableLandValue) self.worksheet.likelyRealisableLandValue = self.formatPrice(self.worksheet.likelyRealisableLandValue);
                if (self.worksheet.likelyRealisableChattels) self.worksheet.likelyRealisableChattels = self.formatPrice(self.worksheet.likelyRealisableChattels);
                if(save) self.saveWorksheet(false, false);
            },
            removeEmptyAndConvertEntries: function(worksheetItem) {
                var worksheets = [];
                $.each(worksheetItem, function(index, pa) {
                    if (pa && ((pa.description && pa.description.trim().length > 0) || pa.areaInSquareMeter || pa.price || pa.pricePerSquareMeter)) {
                        if (pa.pricePerSquareMeter) {
                            pa.pricePerSquareMeter = numeral(pa.pricePerSquareMeter).value();
                        }
                        if (pa && pa.price) {
                            pa.price = numeral(pa.price).value();
                        }
                        worksheets.push(pa);
                    }
                });
                return worksheets;
            },
            saveWorksheet: function(nextStep, persist, fromAdopted) {
                const self = this;
                self.worksheet.directComparisonMarketValue = self.formatPrice(self.worksheet.directComparisonMarketValue);
                var worksheet = JSON.parse(JSON.stringify(self.worksheet));
                var proposedWorksheet = JSON.parse(JSON.stringify(self.proposedWorksheet));
                worksheet['improvements'] = self.removeEmptyAndConvertEntries(worksheet['improvements']);
                worksheet['land'] = self.removeEmptyAndConvertEntries(worksheet['land']);
                worksheet['chattels'] = self.removeEmptyAndConvertEntries(worksheet['chattels']);

                // Proposed Valuation
                proposedWorksheet['improvements'] = self.removeEmptyAndConvertEntries(proposedWorksheet['improvements']);
                proposedWorksheet['land'] = self.removeEmptyAndConvertEntries(proposedWorksheet['land']);
                proposedWorksheet['chattels'] = self.removeEmptyAndConvertEntries(proposedWorksheet['chattels']);

                if (worksheet.adoptedTotalImprovements) worksheet.adoptedTotalImprovements = numeral(worksheet.adoptedTotalImprovements).value();
                if (worksheet.adoptedLandValue) worksheet.adoptedLandValue = numeral(worksheet.adoptedLandValue).value();
                if (worksheet.adoptedChattels) worksheet.adoptedChattels = numeral(worksheet.adoptedChattels).value();

                // Proposed
                if (proposedWorksheet.adoptedTotalImprovements) proposedWorksheet.adoptedTotalImprovements = numeral(proposedWorksheet.adoptedTotalImprovements).value();
                if (proposedWorksheet.adoptedLandValue) proposedWorksheet.adoptedLandValue = numeral(proposedWorksheet.adoptedLandValue).value();
                if (proposedWorksheet.adoptedChattels) proposedWorksheet.adoptedChattels = numeral(proposedWorksheet.adoptedChattels).value();

                // Likely Realisable Price
                if (worksheet.likelyRealisableTotalImprovements) worksheet.likelyRealisableTotalImprovements = numeral(worksheet.likelyRealisableTotalImprovements).value();
                if (worksheet.likelyRealisableLandValue) worksheet.likelyRealisableLandValue = numeral(worksheet.likelyRealisableLandValue).value();
                if (worksheet.likelyRealisableChattels) worksheet.likelyRealisableChattels = numeral(worksheet.likelyRealisableChattels).value();

                if (worksheet.lowMortgageeDiscountPercentage) worksheet.lowMortgageeDiscountPercentage = numeral(worksheet.lowMortgageeDiscountPercentage).value();
                if (worksheet.highMortgageeDiscountPercentage) worksheet.highMortgageeDiscountPercentage = numeral(worksheet.highMortgageeDiscountPercentage).value();
                if (worksheet.lowMortgageeValue) worksheet.lowMortgageeValue = numeral(worksheet.lowMortgageeValue).value();
                if (worksheet.highMortgageeValue) worksheet.highMortgageeValue = numeral(worksheet.highMortgageeValue).value();
                if (worksheet.housingNzEstimateOneToThreeMonths) worksheet.housingNzEstimateOneToThreeMonths = numeral(worksheet.housingNzEstimateOneToThreeMonths).value();
                if (worksheet.housingNzEstimateThreeToSixMonths) worksheet.housingNzEstimateThreeToSixMonths = numeral(worksheet.housingNzEstimateThreeToSixMonths).value();
                if (worksheet.housingNzEstimateSixToTwelveMonths) worksheet.housingNzEstimateSixToTwelveMonths = numeral(worksheet.housingNzEstimateSixToTwelveMonths).value();
                if (worksheet.directComparisonMarketValue) worksheet.directComparisonMarketValue = numeral(worksheet.directComparisonMarketValue).value();

                var event = {};
                event.worksheet = worksheet;
                event.proposedWorksheet = proposedWorksheet;
                event.incomeMethodWorksheet = self.incomeMethodWorksheet;
                event.next = nextStep;
                event.persist = persist;
                self.isDataSaving = persist;
                var haveChanges = !self.jsonEqual(self.worksheet, self.worksheetCopy) || !self.jsonEqual(self.proposedWorksheet, self.proposedWorksheetCopy) || !self.jsonEqual(self.incomeMethodWorksheet, self.incomeMethodWorksheetCopy);
                event.isDirty = haveChanges;
                
                /* HACK forced isDirty to be true on intial load for verified reports so it can be persisted */
                if(fromAdopted){
                    event.isDirty = true;
                }
                EventBus.$emit('home-valuation-job-worksheet', event);
            },
            previousStep: function() {
                const self = this
                self.saveWorksheet(false, true)
                EventBus.$emit("home-valuation-back", "comparablePropertiesStepper");
            },
            calculateMortVal: function(calType) {
                const self = this;
                var marketValExChat = numeral(self.adoptedValues.marketValueExChattels).value();
                if (calType === "low") {
                    var lowDiscount = numeral(self.worksheet.lowMortgageeDiscountPercentage).value();
                    self.worksheet.lowMortgageeValue = lowDiscount ? marketValExChat * (1 - lowDiscount / 100) : 0;
                    self.worksheet.lowMortgageeValue = Math.round(self.worksheet.lowMortgageeValue / 1000) * 1000;
                    self.worksheet.lowMortgageeValue = self.formatPrice(self.worksheet.lowMortgageeValue);
                    self.saveWorksheet(false, false);
                } else if (calType === "high") {
                    var highDiscount = numeral(self.worksheet.highMortgageeDiscountPercentage).value();
                    self.worksheet.highMortgageeValue = highDiscount ? marketValExChat * (1 - highDiscount / 100) : 0;
                    self.worksheet.highMortgageeValue = Math.round(self.worksheet.highMortgageeValue / 1000) * 1000;
                    self.worksheet.highMortgageeValue = self.formatPrice(self.worksheet.highMortgageeValue);
                    self.saveWorksheet(false, false);
                }
                self.refreshView();
            },
            lowDiscountKeypress: function(event) {
                const self = this;
                var lowDiscountPct = 0;
                if (self.worksheet.lowMortgageeDiscountPercentage) {
                    lowDiscountPct = numeral(self.worksheet.lowMortgageeDiscountPercentage).value();
                }
                if (isNaN(lowDiscountPct + "" + String.fromCharCode(event.charCode))) {
                    return event.preventDefault();
                }
                if (lowDiscountPct > 100) {
                    self.worksheet.lowMortgageeDiscountPercentage = self.mortgageeValues.lastValidLowDiscount ? self.mortgageeValues.lastValidLowDiscount : 0;
                } else {
                    self.mortgageeValues.lastValidLowDiscount = lowDiscountPct;
                }
            },
            highDiscountKeypress: function(event) {
                const self = this;
                var highDiscountPct = 0;
                if (self.worksheet.highMortgageeDiscountPercentage) {
                    highDiscountPct = numeral(self.worksheet.highMortgageeDiscountPercentage).value();
                }
                if (isNaN(highDiscountPct + "" + String.fromCharCode(event.charCode))) {
                    return event.preventDefault();
                }
                if (highDiscountPct > 100) {
                    self.worksheet.highMortgageeDiscountPercentage = self.mortgageeValues.lastValidHighDiscount ? self.mortgageeValues.lastValidHighDiscount : 0;
                } else {
                    self.mortgageeValues.lastValidHighDiscount = highDiscountPct;
                }
            },
            discountKeyup: function(discountType) {
                const self = this;
                var lowDiscountPct = numeral(self.worksheet.lowMortgageeDiscountPercentage).value();
                var highDiscountPct = numeral(self.worksheet.highMortgageeDiscountPercentage).value();
                if (discountType === "low") {
                    if (self.worksheet.lowMortgageeDiscountPercentage) {
                        if (self.worksheet.lowMortgageeDiscountPercentage.toString().indexOf('.') === 0) {
                            self.worksheet.lowMortgageeDiscountPercentage = numeral("0" + lowDiscountPct.toString()).value();
                        }
                        if (lowDiscountPct > 100) {
                            self.worksheet.lowMortgageeDiscountPercentage = self.mortgageeValues.lastValidLowDiscount;
                        }
                    }
                }
                if (discountType === "high") {
                    if (self.worksheet.highMortgageeDiscountPercentage) {
                        if (self.worksheet.highMortgageeDiscountPercentage.toString().indexOf('.') === 0) {
                            self.worksheet.highMortgageeDiscountPercentage = numeral("0" + highDiscountPct.toString()).value();
                        }
                        if (highDiscountPct > 100) {
                            self.worksheet.highMortgageeDiscountPercentage = self.mortgageeValues.lastValidHighDiscount;
                        }
                    }
                }
            },
            formatPeriodsValues: function() {
                const self = this;
                if (self.worksheet.housingNzEstimateOneToThreeMonths) self.worksheet.housingNzEstimateOneToThreeMonths = self.formatPrice(self.worksheet.housingNzEstimateOneToThreeMonths);
                if (self.worksheet.housingNzEstimateThreeToSixMonths) self.worksheet.housingNzEstimateThreeToSixMonths = self.formatPrice(self.worksheet.housingNzEstimateThreeToSixMonths);
                if (self.worksheet.housingNzEstimateSixToTwelveMonths) self.worksheet.housingNzEstimateSixToTwelveMonths = self.formatPrice(self.worksheet.housingNzEstimateSixToTwelveMonths);
                self.saveWorksheet(false, false);
            },
            repopulateMortgagee: function() {
                const self = this;
                if (self.worksheet.lowMortgageeValue) self.worksheet.lowMortgageeValue = self.formatPrice(self.worksheet.lowMortgageeValue);
                if (self.worksheet.highMortgageeValue) self.worksheet.highMortgageeValue = self.formatPrice(self.worksheet.highMortgageeValue);
            },
            repopulateHNZPeriods: function() {
                const self = this;
                if (self.worksheet.housingNzEstimateOneToThreeMonths) self.worksheet.housingNzEstimateOneToThreeMonths = self.formatPrice(self.worksheet.housingNzEstimateOneToThreeMonths);
                if (self.worksheet.housingNzEstimateThreeToSixMonths) self.worksheet.housingNzEstimateThreeToSixMonths = self.formatPrice(self.worksheet.housingNzEstimateThreeToSixMonths);
                if (self.worksheet.housingNzEstimateSixToTwelveMonths) self.worksheet.housingNzEstimateSixToTwelveMonths = self.formatPrice(self.worksheet.housingNzEstimateSixToTwelveMonths);
            },
            emptyOptionalVal: function(optionalSection) {
                const self = this;
                if (optionalSection === "mortgagee") {
                    if (!self.worksheet.includeMortgageeValuesInReport) {
                        self.worksheet.lowMortgageeValue = "";
                        self.worksheet.highMortgageeValue = "";
                        self.worksheet.highMortgageeDiscountPercentage = "";
                        self.worksheet.lowMortgageeDiscountPercentage = "";
                    }
                } else if (optionalSection === "hnz") {
                    if (!self.worksheet.includeHousingNzEstimatesInReport) {
                        self.worksheet.housingNzEstimateOneToThreeMonths = "";
                        self.worksheet.housingNzEstimateThreeToSixMonths = "";
                        self.worksheet.housingNzEstimateSixToTwelveMonths = "";
                    }
                } else if (optionalSection === "likelyRealisablePrice") {
                    if (!self.worksheet.includeLikelyRealisablePriceInReport) {
                        self.worksheet.likelyRealisableTotalImprovements = "";
                        self.worksheet.likelyRealisableLandValue = "";
                        self.worksheet.likelyRealisableChattels = "";
                        self.worksheet.valuationConstraintsExplanation = null;
                        self.calculateLikelyRealisableValues(false);
                    }
                }
                self.saveWorksheet(false, false);
                self.refreshView();
            },
            isEmpty: function(obj) {
                for (var key in obj) {
                    if (obj.hasOwnProperty(key) && obj[key] !== undefined && obj[key] !== null) {
                        return false;
                    }
                }
                return true;
            },
            jsonEqual: function jsonEqual(a,b) {
                return deepEqual(a,b);
            }
        },
        updated: function() {
            const self = this;
            if (self.refreshCurrentTemplate) {
                self.showCurrentTemplate = true;
                self.refreshCurrentTemplate = false;
            }
        },

        created: function() {
            var self = this;
            EventBus.$on('enable-buttons', function() {
                self.isDataSaving = false
            })
            EventBus.$on('home-valuation-saved', function(obj) {
                var loadingNewJob = self.homeValuationId != obj.homeValuation.id;
                if(loadingNewJob || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.homeValuationId = homeValuation.id;
                    self.worksheet = homeValuation.worksheet ? JSON.parse(JSON.stringify(homeValuation.worksheet)) : {};
                    self.proposedWorksheet = homeValuation.proposedWorksheet ? JSON.parse(JSON.stringify(homeValuation.proposedWorksheet)) : {};
                    self.incomeMethodWorksheet = homeValuation.incomeMethodWorksheet ? JSON.parse(JSON.stringify(homeValuation.incomeMethodWorksheet)) : {};
                    $.each(['worksheet', 'proposedWorksheet'], function(i, worksheet){
                        $.each(['improvements', 'land', 'chattels'], function(index, item) {
                            if (!self[worksheet][item] || self[worksheet][item].length == 0) {
                                if (index == 0) {
                                    self[worksheet].improvements = [{description: 'Dwelling'}, {description: 'Garage'}, {description: 'Other Improvements'}];
                                } else if (index == 2) {
                                    self[worksheet].chattels = [{description: 'Chattels'}];
                                } else {
                                    self.add(worksheet, item, 0);
                                }
                            } else {
                                $.each(self[worksheet][item], function(index, it) {
                                    self.calculate(worksheet, item, index, 1, true);
                                });
                            }
                            if (worksheet === 'worksheet') {
                                self.runningTotal[item] = self.formatPrice(self.calculateRunningTotal(worksheet, item, self[worksheet][item].length));
                            }
                            else if (worksheet === 'proposedWorksheet') {
                                var itemToUpper = item.charAt(0).toUpperCase() + item.substr(1);
                                self.runningTotal[itemToUpper] = self.formatPrice(self.calculateRunningTotal(worksheet, item, self[worksheet][item].length));
                            }
                        });
                    });  
                    if(homeValuation.reportType.code.includes('VQV')){ 
                        self.calculateAdoptedValuesVerifiedReport(true, homeValuation.propertySummary.qupid);
                    }
                    else {
                        self.calculateAdoptedValues(false);
                    }
                    self.calculateAdoptedProposedValues(false);
                    self.calculateLikelyRealisableValues(false);

                    if (self.worksheet.lowMortgageeDiscountPercentage
                        || self.worksheet.highMortgageeDiscountPercentage
                        || self.worksheet.lowMortgageeValue
                        || self.worksheet.highMortgageeValue) {
                        self.worksheet.includeMortgageeValuesInReport = true;
                        self.repopulateMortgagee();
                    }
                    if (self.worksheet.housingNzEstimateOneToThreeMonths
                        || self.worksheet.housingNzEstimateThreeToSixMonths
                        || self.worksheet.housingNzEstimateSixToTwelveMonths) {
                        self.worksheet.includeHousingNzEstimatesInReport = true;
                        self.repopulateHNZPeriods();
                    }

                    if (!self.worksheet.land) {
                        self.worksheet.land = [{
                            description: 'Land',
                            areaInSquareMeter: 0,
                            pricePerSquareMeter: 0
                        }];
                    }
                    if (!self.proposedWorksheet.land) {
                        self.proposedWorksheet.land = [{
                            description: 'Land',
                            areaInSquareMeter: 0,
                            pricePerSquareMeter: 0
                        }];
                    }

                    self.refreshView();
                    self.worksheetCopy = JSON.parse(JSON.stringify(self.worksheet));
                    self.proposedWorksheetCopy = JSON.parse(JSON.stringify(self.proposedWorksheet));
                    self.incomeMethodWorksheetCopy = JSON.parse(JSON.stringify(self.incomeMethodWorksheet));
                    self.saveWorksheet(false, false);
                    self.setCurrentTab('Current', 1, true);
                } else {
                    self.worksheetCopy = JSON.parse(JSON.stringify(self.worksheet));
                    self.proposedWorksheetCopy = JSON.parse(JSON.stringify(self.proposedWorksheet));
                    self.incomeMethodWorksheetCopy = JSON.parse(JSON.stringify(self.incomeMethodWorksheet));
                }
                self.showCurrentTemplate = false;
                self.refreshCurrentTemplate = true;
                self.isDataSaving = false;
            });

            EventBus.$on('home-valuation-new', function(event) {
                self.worksheet = {};
                self.proposedWorksheet = {};
                self.showMortgageeSection = false;
                self.showHNZPeriods = false;
                var areaInSquareMeter = 0;
                var pricePerSquareMeter = 0;
                const landValueIndex = event.landValueIndex != null ? event.landValueIndex : 1;
                if (event.landArea && event.landArea > 0) {
                    areaInSquareMeter = (event.landArea * 10000).toFixed(2);
                }
                if (event.lvNetRate && areaInSquareMeter > 0) {
                    pricePerSquareMeter = (numeral(event.lvNetRate).value())*landValueIndex;
                }
                self.worksheet.land = [{
                    description: 'Land',
                    areaInSquareMeter: areaInSquareMeter,
                    pricePerSquareMeter: Math.round(pricePerSquareMeter),
                    price: Math.round((areaInSquareMeter * pricePerSquareMeter)/1000)*1000,
                }];
                self.proposedWorksheet.land = [{
                    description: 'Land',
                    areaInSquareMeter: areaInSquareMeter,
                    pricePerSquareMeter: Math.round(pricePerSquareMeter),
                    price: Math.round((areaInSquareMeter * pricePerSquareMeter)/1000)*1000,
                }];
                self.saveWorksheet(false, false);
            });
            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                if (reportTypeCode == "HA") {
                    self.worksheet.includeHousingNzEstimatesInReport = true
                }
                else {
                    self.worksheet.includeHousingNzEstimatesInReport = false
                }
                self.saveWorksheet(false, false)
            });
            EventBus.$on('home-valuation-job-income-method', function (event) {
                self.incomeMethodWorksheet = JSON.parse(JSON.stringify(event.incomeMethodWorksheet));
            });

        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('home-valuation-job-income-method', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('home-valuation-job-income-method', this.listener);
        }
    }
</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div>
        <div class="md-table mdl-shadow--2dp">
            <h2>Photos and Attachments</h2>
            <ul class="toolbar righty" @click="uploadDone()">
                <li class="selectedComps-count"><span>{{ mediaCount }}</span></li>
                <li :title="showMediaLabel" class="mdl-button mdl-button--icon">
                    <i class="material-icons md-dark">burst_mode</i>
                </li>
            </ul>
            <ul id="tabsElementPhotosAttachment" class="QVHV-tabs">
                <li class="QVHVTab-1" id="photosTab" @click="setCurrentTab('Photos', 1)" data-tab="QVHVTab-1" data-container="photouploads">
                    <span class="is-active">Photos</span>
                </li>
                <li class="QVHVTab-2" data-cy="attachments-tab" id="attachmentsTab" @click="setCurrentTab('Attachments', 2)" data-tab="QVHVTab-2" data-container="fileuploads">
                    <span>Attachments</span>
                </li>
                <li class="QVHVTab-3" data-cy="aerial-and-location-tab" id="aerialAndLocationTab" @click="setCurrentTab('Maps', 3)" data-tab="QVHVTab-3" data-container="mapuploads">
                    <span>Aerial & Location</span>
                </li>
                <li class="QVHVTab-4" data-cy="notes-and-final-reports" id="reportsTab" @click="setCurrentTab('Reports', 4)" data-tab="QVHVTab-4" data-container="reportuploads">
                    <!--<span>Generated Reports</span>-->
                    <span>Notes & Final Reports</span>
                </li>
                <hr align="left"/>
            </ul>

            <div class="QVHV-Container photouploads" v-bind:class="{disabled: readOnly || !homeValuation.id, active: currentTab == 'Photos'}">
                <ul class="QVHV-tabs hide">
                    <li><span class="is-active">Photos</span></li>
                    <hr align="left"/>
                </ul>

                <upload v-if="photoUpload || (photos.included.length == 0 && photos.excluded.length == 0)" :continueHandler="uploadDone" :ownerId="homeValuation.id" category="HomeValuationPhoto"></upload>

                <div class="QVHV-formSection" v-if="!photoUpload && (photos.included.length > 0 || photos.excluded.length > 0)">
                    <div class="advSearch-row">
                        <h2>Select Photos to Include with this Report<span>Photos that you choose will also be added to the Property Details Gallery of this property</span></h2>
                        <div class="QVHV-buttons righty">
                            <button data-cy="add-details" :data-href="getManagePhotoHref()" class="primary" v-bind:class="{disabled: photos.included.length == 0}" @click="managePhoto">{{ hasValidPhotosSelected() ? 'Update' : 'Add Details' }}</button>
                            <span title="Upload Photos" id="photoUploadButton" data-upgraded="MaterialButton" @click="photoUpload = true"><i class="material-icons md-dark"></i></span>
                        </div>

                        <div class="reportPhoto mdl-shadow--2dp" v-for="photo,key in photos.included" :key="photo.id">
                            <span class="includePhoto">
                                <input :id="photo.id" :value="photo.id" type="checkbox" v-model="photo.selected" @click="includePhoto(key,'included',$event)">
                                <label :for="photo.id"><h3>Include in report and save to property</h3></label>
                                <i class="material-icons md-dark download" @click="downloadMedia(photo.mediaItem.originalImageUrl)" title="Original Photo">cloud_download</i>
                                <i class="material-icons md-dark" @click="deleteMedia(photo.id)" title="Delete Photo">delete_forever</i>
                            </span>
                            <img :id="'image'+photo.id" :src="photo.mediaItem.mediumImageUrl" @error="imgError(photo)"/>
                            <ul class="photoCaption" v-bind:class="{noDetails: photo.mediaItem.tags.length == 0 && (!photo.description || photo.description.trim() == '')}">
                                <li><strong>{{ photo.description ? photo.description : '' }}</strong></li>
                                <li>{{ generatePhotoTags(photo.mediaItem.improvementDateRange, photo.mediaItem.tags) }}</li>
                                <li>{{ formatDate(photo.mediaItem.captureDate) }}</li>
                            </ul>
                        </div>
                    </div>
                    <div class="advSearch-row">
                        <div class="reportPhoto mdl-shadow--2dp" v-for="photo,key in photos.excluded" :key="photo.id">
                            <span data-cy="photos-included" class="includePhoto">
                                <input :id="photo.id" :value="photo.id" type="checkbox" v-model="photo.selected" @click="includePhoto(key,'excluded',$event)">
                                <label :for="photo.id"><h3>Include in report and save to property</h3></label>
                                <i class="material-icons md-dark download" @click="downloadMedia(photo.mediaItem.originalImageUrl)" title="Original Photo">cloud_download</i>
                                <i class="material-icons md-dark" @click="deleteMedia(photo.id)" title="Delete Photo">delete_forever</i>
                            </span>
                            <img :id="'image'+photo.id" :src="photo.mediaItem.mediumImageUrl" @error="imgError(photo)"/>
                            <ul class="photoCaption">
                                <li><strong>{{ photo.description ? photo.description : '' }}</strong></li>
                                <li>{{ generatePhotoTags(photo.mediaItem.improvementDateRange, photo.mediaItem.tags) }}</li>
                                <li>{{ formatDate(photo.mediaItem.captureDate) }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="QVHV-Container fileuploads" v-bind:class="{disabled: readOnly || !homeValuation.id, active: currentTab == 'Attachments'}">
                <ul class="QVHV-tabs hide">
                    <li><span class="is-active">Attachments</span></li>
                    <hr align="left"/>
                </ul>

                <upload v-if="fileUpload || files.length == 0" :continueHandler="uploadDone" :ownerId="homeValuation.id" category="HomeValuationAttachment"></upload>

                <div class="QVHV-formSection" v-if="!fileUpload && files.length > 0">
                    <div class="advSearch-row">
                        <h2>Select Files to Attach to this Report</h2>
                        <div class="QVHV-buttons noDivider righty">
                            <span title="Upload Photos" data-upgraded="MaterialButton" @click="fileUpload = true"><i class="material-icons md-dark"></i></span>
                        </div>
                        <ul class="qvhvReport-files mdl-shadow--2dp" v-if="files.length > 0">
                            <li v-for="file,key in files">
                                <input :id="file.id" type="checkbox" v-model="file.selected" @click="includeReport(key, $event)">
                                <label :for="file.id"><h3>{{ file.mediaItem.fileName }}</h3></label>
                                <i class="material-icons md-dark download" @click="downloadMedia(file.mediaItem.originalImageUrl)" title="Download File">cloud_download</i>
                                <i class="material-icons md-dark" @click="deleteMedia(file.id)" title="Delete Photo">delete_forever</i>

                                <!-- NEW FORM ROW ELEMENT FOR FILE ATTACHMENTS -->
                                <div class="advSearch-row">
                                    <valuation-multi-select-filter
                                            :currVal="file.attachmentType"
                                            :objKey="key+1"
                                            parentAttrName="attachmentTypes"
                                            iconClass="twentyfivePct icons8-todo-list-filled"
                                            component-name="photo-attachments"
                                            attrName="attachmentType"
                                            :filterId="file.id"
                                            label="Attachment Type"
                                            :selectClass="file.id"
                                            chooseHere="true"
                                            data-to-fetch="AttachmentTypes"
                                            :class=[fields.attachments.attachmentType]>
                                    </valuation-multi-select-filter>
                                    <div class="advSearch-group seventyfivePct icons8-pencil" v-bind:class=[fields.attachments.description]>
                                        <label>Add Label or Description</label>
                                        <span><input class="advSearch-text" type="text" v-model="file.description" @change="saveMedia(file)"></span>
                                        <div class="valMessage"></div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="QVHV-Container mapuploads"  v-bind:class="{disabled: readOnly || !homeValuation.id, active: currentTab == 'Maps'}">
                <ul class="QVHV-tabs hide">
                    <li><span class="is-active">Map and Aerial View</span></li>
                    <hr align="left"/>
                </ul>

                <upload v-if="mapUpload || maps.length == 0" :continueHandler="uploadDone" :ownerId="homeValuation.id" category="HomeValuationMap"></upload>

                <div class="QVHV-formSection"  v-if="!mapUpload && maps.length > 0">
                    <div class="advSearch-row">
                        <h2>Aerial View & Location Map Images<span>An aerial view and a location map of the property are required for the report.</span></h2>
                        <div class="QVHV-buttons noDivider righty">
                            <span title="Upload Photos" data-upgraded="MaterialButton" @click="mapUpload = true"><i class="material-icons md-dark"></i></span>
                        </div>
                        <span class="reportPhoto mdl-shadow--2dp" v-for="map,key in maps">
                            <span class="includePhoto">
                                  <fieldset role="radiogroup" v-bind:class="{selected: map.aerialView || map.locationMap }">
                                    <span class="aerialView">
                                        <input type="radio" :name="'AerialView'+(map.id)" :id="'AerialView'+(map.id)" value="true" v-model="map.aerialView" @click="setMapTag(map.id,'aerialView')">
                                        <label :for="'AerialView'+(map.id)">Aerial View</label>
                                    </span>
                                    <span class="locationMap">
                                        <input type="radio" :name="'LocationMap'+(map.id)" :id="'LocationMap'+(map.id)" value="true" v-model="map.locationMap" @click="setMapTag(map.id,'locationMap')">
                                        <label :for="'LocationMap'+(map.id)">Location Map</label>
                                    </span>
                                </fieldset>
                                <i class="material-icons md-dark download" @click="downloadMedia(map.mediaItem.originalImageUrl)" title="Download File">cloud_download</i>
                                <i class="material-icons md-dark" @click="deleteMedia(map.id)" title="Delete Photo">delete_forever</i>
                            </span>
                            <img :class="'image'+map.id" :src="map.mediaItem.originalImageUrl" @error="imgError(map)"/>
                            <span class="advSearch-group hundyPct icons8-pencil" v-bind:class=[fields.aerialAndLocation.source]>
                                <label>Image Source</label>
                                <span><input type="text" v-model="map.description" @change="saveMedia(map)"></span>
                                <span class="valMessage"></span>
                            </span>
                            <span class="advSearch-group hundyPct icons8-pencil" v-bind:class=[fields.aerialAndLocation.comment]>
                                <label>Comment</label>
                                <span><input type="text" maxlength="1000" v-model="map.comment" @change="setComment(map)"></span>
                                <span class="valMessage"></span>
                            </span>
                        </span>
                    </div>
                </div>
            </div>

            <div class="QVHV-Container reportuploads" v-bind:class="{disabled: readOnly || !homeValuation.id, active: currentTab == 'Reports'}">
                <ul class="QVHV-tabs hide">
                    <li><span class="is-active">Reports</span></li>
                    <hr align="left"/>
                </ul>

                <upload v-if="reportUpload || reports.length == 0" :continueHandler="uploadDone" :ownerId="homeValuation.id" category="HomeValuationReport"></upload>

                <!--<div class="QVHV-formSection">-->
                    <!--<div class="advSearch-row" v-if="reports.length > 0">-->
                        <!--<ul class="qvhvReport-files mdl-shadow&#45;&#45;2dp" v-if="reports.length > 0">-->
                            <!--<li v-for="report,key in reports">-->
                                <!--<label><h3>{{ report.mediaItem.fileName }}</h3></label>-->
                                <!--<i class="material-icons md-dark download" @click="downloadMedia(report.mediaItem.originalImageUrl)" title="Download File">cloud_download</i>-->
                            <!--</li>-->
                        <!--</ul>-->
                    <!--</div>-->
                <!--</div>-->

                <div class="QVHV-formSection" v-if="!reportUpload && reports.length > 0">
                    <div class="advSearch-row">
                        <h2>Select Files to Attach to this Report</h2>
                        <div class="QVHV-buttons noDivider righty">
                            <span title="Upload Photos" data-upgraded="MaterialButton" @click="reportUpload = true"><i class="material-icons md-dark"></i></span>
                        </div>
                        <ul class="qvhvReport-files mdl-shadow--2dp" v-if="reports.length > 0">
                            <li v-for="file,key in reports">
                                <!--<input :id="file.id" type="checkbox" v-model="file.selected" @click="includeReport(key)">-->
                                <label :for="file.id"><h3>{{ file.mediaItem.fileName }}</h3></label>
                                <i class="material-icons md-dark download" @click="downloadMedia(file.mediaItem.originalImageUrl)" title="Download File">cloud_download</i>
                                <!--<i class="material-icons md-dark" @click="deleteMedia(file.id)" title="Delete Photo">delete_forever</i>-->

                                <!-- NEW FORM ROW ELEMENT FOR REPORT ATTACHMENTS -->
                                <div class="advSearch-row" v-if="file.attachmentReportType && file.attachmentReportType.code && file.attachmentReportType.code == 'FinalReport' && file.category == 'GeneratedHomeValuationReport'" >
                                    <div class="advSearch-group twentyfivePct icons8-todo-list-filled" v-bind:class=[fields.notesAndFinalReports.attachmentType]>
                                        <label>Attachment Type</label>
                                        <span><input class="advSearch-text" type="text" value="Final Report" disabled></span>
                                        <div class="valMessage"></div>
                                    </div>

                                    <div class="advSearch-group seventyfivePct icons8-pencil" v-bind:class=[fields.notesAndFinalReports.description]>
                                        <label>Add Label or Description</label>
                                        <span><input class="advSearch-text" type="text" v-model="file.description" disabled></span>
                                        <div class="valMessage"></div>
                                    </div>
                                </div>

                                <div class="advSearch-row" v-if="!file.attachmentReportType || !file.attachmentReportType.code || (file.attachmentReportType && file.attachmentReportType.code && !(file.attachmentReportType.code == 'FinalReport' && file.category == 'GeneratedHomeValuationReport'))" >
                                    <valuation-multi-select-filter
                                            :currVal="file.attachmentReportType"
                                            :objKey="+key+1"
                                            :id="file.id"
                                            parentAttrName="attachmentReportTypes"
                                            iconClass="twentyfivePct icons8-todo-list-filled"
                                            component-name="report-attachments"
                                            attrName="attachmentReportType"
                                            :filterId="'attachmentReportType-'+file.id"
                                            label="Attachment Type"
                                            :selectClass="'attachmentReportType-'+(+key+1)"
                                            chooseHere="true"
                                            data-to-fetch="FileType"
                                            :class=[fields.notesAndFinalReports.attachmentType]>
                                    </valuation-multi-select-filter>
                                    <div class="advSearch-group seventyfivePct icons8-pencil" v-bind:class=[fields.notesAndFinalReports.description]>
                                        <label>Add Label or Description</label>
                                        <span><input class="advSearch-text" type="text" v-model="file.description" @change="saveMedia(file)"></span>
                                        <div class="valMessage"></div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="QVHV-buttons" v-bind:class="{disabled: readOnly}">
            <div class="QVHV-buttons-left" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="primary" @click="saveAllMedia(false, true)">Save</button>
            </div>
            <div class="QVHV-buttons-right" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="secondary" @click="previousStep">Back</button>
                <button class="primary" @click="saveAllMedia(true, true)">Next Step</button>
            </div>
        </div>
        <warning :header="warningHeader" :message="warningMessage" class="homeValuationPhotoError" close="Ok"></warning>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { mapGetters } from 'vuex';
    import { store } from '../../DataStore';
    import Upload from '../valuation/photosAttachments/Upload.vue'
    import ValuationMultiSelectFilter from '../filters/ValuationMultiSelectFilter.vue';
    import Warning from '../common/Warning.vue';
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import moment from 'moment';


    export default {
        props: ['readOnly'],
        components: {
            Upload,
            ValuationMultiSelectFilter,
            Warning
        },
        mixins: [formatUtils, commonUtils],
        data: function() {
            return {
                photoUpload: false,
                fileUpload: false,
                mapUpload: false,
                reportUpload:false,
                homeValuation: {},
                photos: { included: [], excluded: []},
                files: [],
                maps: [],
                reports: [],
                selectedPhotos: [],
                currentTab: "Photos",
                isDataSaving: false,
                warningHeader: "Home Valuation Photos",
                warningMessage: "Only 11 photos are allowed to be included in the report.",
                fields: {
                    "attachments" : {},
                    "aerialAndLocation" : {},
                    "notesAndFinalReports" : {}
                }
            }
        },
        computed: {
            ...mapGetters(['getCategoryClassifications']),

            improvementDateRangeDescriptions() {
                const improvementDateRangeClassifications = this.getCategoryClassifications('ImprovementDateRange');
                // Convert the array of ImprovementDateRange Classifications into a key-value pair of classification code to classification description.
                return improvementDateRangeClassifications.reduce((accumulator, currentValue) => ({ ...accumulator, [currentValue.code]: currentValue.description }), {});
            },

            tagsDescriptions() {
                const tagsClassifications = this.getCategoryClassifications('Tags');
                // Convert the array of Tags Classifications into a key-value pair of classification code to classification description.
                return tagsClassifications.reduce((accumulator, currentValue) => ({ ...accumulator, [currentValue.code]: currentValue.description }), {});
            },

            mediaCount: function () {
                const self = this;
                var count = 0;
                if(self.currentTab == 'Photos') {
                    count = self.photos.included.length + self.photos.excluded.length;
                } else if (self.currentTab == 'Attachments'){
                    count = self.files.length;
                } else if (self.currentTab == 'Maps'){
                    count = self.maps.length;
                } else if (self.currentTab == "Reports"){
                    count = self.reports.length;
                }
                return count;
            },
            showMediaLabel: function() {
                const self = this;
                var label = "Show Uploaded Photos";
                if(self.currentTab == 'Photos') {
                    label = "Show Uploaded Photos";
                } else if (self.currentTab == 'Attachments'){
                    label = "Show Uploaded Attachments";
                } else if (self.currentTab == 'Maps'){
                    label = "Show Uploaded Maps";
                } else if (self.currentTab == "Reports"){
                    label = "Show Uploaded Reports";
                }
                return label;
            }
        },
        created: function() {
            var self = this;
            EventBus.$on('home-valuation-saved', function(obj) {
                var homeValuation = obj.homeValuation;
                if(self.homeValuation.id != homeValuation.id) {
                    Object.assign(self.$data, self.$options.data.apply(self));
                    self.homeValuation = JSON.parse(JSON.stringify(homeValuation));
                    self.getMedia(function() {
                        self.photoUpload = false;
                        self.fileUpload = false;
                        self.mapUpload = false;
                        self.reportUpload = false;
                        self.setCurrentTab('Photos', 1);
                    });
                } else {
                    self.isDataSaving = false;
                    if((self.currentTab == 'Photos' && self.photoUpload == true)
                            || (self.currentTab == 'Attachments' && self.fileUpload == true)
                            || (self.currentTab == 'Maps' && self.mapUpload == true)
                            || (self.currentTab == 'Reports' && self.reportUpload == true)) {
                        self.uploadDone();
                    }
                    if(self.homeValuation.status.code != homeValuation.status.code && homeValuation.status.code == 'C') {
                        var selectedPhotos = JSON.parse(JSON.stringify(self.photos.included));
                        $.each(selectedPhotos, function(i, relink) {
                            relink.id = "";
                            relink.ownerId = self.homeValuation.propertySummary.id;
                            relink.category = "Property";
                            self.doRelinking(relink);
                        });
                        EventBus.$emit('home-valuation-completed-photos', self.homeValuation.propertySummary.id);
                        self.getMedia(function() {
                            self.photoUpload = false;
                            self.fileUpload = false;
                            self.mapUpload = false;
                            self.reportUpload = false;
                        });
                    }
                    self.homeValuation = JSON.parse(JSON.stringify(homeValuation));
                }
            });
            EventBus.$on('home-valuation-new', function(event) {
                self.setCurrentTab('Photos', 1);
                Object.assign(self.$data, self.$options.data.apply(self));
                self.photoUpload = false;
                self.fileUpload = false;
                self.mapUpload = false;
                self.reportUpload = false;
                self.isDataSaving = false;
            });
            EventBus.$on('notify-multi-nested-photo-attachments', function(data){
                self.files[+data.key-1][data.attrName] = data.val;
                self.files[+data.key-1].description = data.val.description;
                if(self.files[+data.key-1].selected) {
                    self.files[+data.key-1].tags.push('selected');
                    self.files[+data.key-1].tags.push(data.val.code);
                } else {
                    self.files[+data.key-1].tags.push(data.val.code);
                }
                self.saveMedia(self.files[+data.key-1]);
            });EventBus.$on('notify-multi-nested-report-attachments', function(data){
                self.reports[+data.key-1][data.attrName] = data.val;
                self.reports[+data.key-1].description = data.val.description;
                self.reports[+data.key-1].tags = [];
                self.reports[+data.key-1].tags.push(data.val.code);
                self.saveMedia(self.reports[+data.key-1]);
            });
            EventBus.$on('home-valuation-loaded', function () {
                self.setCurrentTab('Photos', 1);
            });
            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.photosAndAttachments;
                }
            });
        },
        methods: {
            previousStep: function(){
                this.saveAllMedia(false, false);
                EventBus.$emit("home-valuation-back", "locationDetailsStepper");
            },
            setCurrentTab: function(name, index){
                const self = this;
                self.currentTab = name;
                $('#tabsElementPhotosAttachment').find('hr')
                        .removeClass()
                        .addClass('QVHVTab-'+ index );
                if(index == 1 || index == 3 || index == 4) {
                    self.getMedia(function() {
                        if(index == 3) self.mapUpload = false;
                        if(index == 4) self.reportUpload = false;
                    });
                }
                self.saveAllMedia(false, false);
            },
            imgError: function(photo) {
                $('#image' + photo.id).parent('div').addClass('spinner');
                var img = document.getElementById('image' + photo.id);
                setTimeout(function () {
                    img.setAttribute('src', photo.mediaItem.mediumImageUrl + "#" + new Date().getTime());
                    $('#image' + photo.id).parent('div').removeClass('spinner');
                }, 4000);
            },
            getMedia: function(callback) {
                var self = this;
                if(self.homeValuation && self.homeValuation.id) {
                    $.ajax({
                        type: "GET",
                        cache: false,
                        dataType: 'json',
                        url:  jsRoutes.controllers.MediaController.getMediaByOwner(self.homeValuation.id).url,
                        error: function(response) {
                            self.errorHandler(response);
                        },
                        success: function(response) {
                            self.files = [];
                            self.maps = [];
                            self.photos = { included: [], excluded: [] };
                            self.reports = [];
                            $.each(response, function(i, media) {
                                media.tags = $.map(media.tags, function(value, index) {
                                    return value.trim();
                                });

                                if(media.category == 'HomeValuationPhoto') {
                                    var tags = media.tags;
                                    if(tags.length > 0) {
                                        media.selected = tags.indexOf('selected') > -1;
                                    }
                                    if(media.tags.indexOf('selected') > -1)  {
                                        self.photos.included.push(media);
                                    } else {
                                        self.photos.excluded.push(media);
                                    }
                                } else if (media.category == 'HomeValuationAttachment') {
                                    var tags = media.tags;
                                    if(tags.length > 0) {
                                        media.selected = tags.indexOf('selected') > -1;
                                        media.attachmentType = self.checkTag(tags);
                                    }
                                    self.files.push(media);
                                } else if (media.category == 'HomeValuationMap') {
                                    media.aerialView = false;
                                    media.locationMap = false;
                                    media.tags.forEach(function(tag) {
                                        if (tag === 'aerialView') {
                                            media.aerialView = true;
                                        } else if (tag === 'locationMap') {
                                            media.locationMap = true;
                                        } else if (tag.startsWith('comment=')) {
                                            media.comment = tag.substring(8);
                                        }
                                    });
                                    self.maps.push(media);
                                } else if (media.category == 'HomeValuationReport') {
                                    var tags = media.tags;
                                    if(tags.length > 0) {
                                        media.attachmentReportType = self.checkTagFileType(tags);
                                    } else {
                                        media.attachmentReportType = {};
                                        media.attachmentReportType.code = "";
                                    }
                                    self.reports.push(media);
                                } else if (media.category == 'GeneratedHomeValuationReport' && (self.homeValuation.status && self.homeValuation.status.code == 'C')) {
                                    var tags = media.tags;
                                    if(tags.length > 0) {
                                        media.attachmentReportType = self.checkTagFileType(tags);
                                    }
                                    self.reports.push(media);
                                }
                            });
                            if(callback) callback();
                        }
                    });
                }
            },
            checkTag: function(tags) {
                var tag = { code: ""};
                if(tags.indexOf("Certificate of Title") >= 0 || tags.indexOf("certificateOfTitle") >= 0) tag = { code: "certificateOfTitle"};
                if(tags.indexOf("Proposed Plans") >= 0 || tags.indexOf("proposedPlans") >= 0) tag = { code: "proposedPlans"};
                if(tags.indexOf("Specifications") >= 0|| tags.indexOf("specifications") >= 0) tag = { code: "specifications"};
                if(tags.indexOf("Other") >= 0 || tags.indexOf("other") >= 0) tag = { code: "other"};
                return tag;
            },
            checkTagFileType: function(tags) {
                var tag = { code: ""};
                if(tags.indexOf("Final Report") >= 0 || tags.indexOf("FinalReport") >= 0) tag = { code: "FinalReport"};
                if(tags.indexOf("Other") >= 0 || tags.indexOf("OtherFileType") >= 0) tag = { code: "OtherFileType"};
                if(tags.indexOf("Scope of Works") >= 0|| tags.indexOf("ScopeOfWorks") >= 0) tag = { code: "ScopeOfWorks"};
                if(tags.indexOf("Valuer's Notes") >= 0 || tags.indexOf("ValuersNotes") >= 0) tag = { code: "ValuersNotes"};
                return tag;
            },
            uploadDone: function() {
                var self = this;
                if(self.currentTab == 'Photos') {
                    self.getMedia(function() { self.photoUpload = false; });
                } else if (self.currentTab == 'Attachments'){
                    self.getMedia(function() { self.fileUpload = false; });
                } else if (self.currentTab == 'Maps'){
                    self.getMedia(function() { self.mapUpload = false; });
                } else if (self.currentTab == "Reports"){
                    self.getMedia(function() { self.reportUpload = false; });
                }
            },
            getManagePhotoHref : function () {
                return '?jobId='+this.homeValuation.id;
            },
            managePhoto: function(){
                var self = this;
                var path = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?jobId='+this.homeValuation.id;
                var searchWindow = window.open(path,'PropertyPhotos','scrollbars=no,resizable=yes,height=800,width=1024');
                searchWindow.focus();
                var timer = setInterval(function() {
                    if(searchWindow.closed == true) {
                        self.getMedia();
                        clearInterval(timer);
                    }
                }, 1000);
            },
            deleteMedia: function(id) {
                var self = this;
                if(self.currentTab == 'Photos') {
                    self.photos.included = self.photos.included.filter(function( obj ) {
                        if(obj.id == id) {
                            obj.mediaItem.status = 'DELETED';
                            self.saveMedia(obj);
                        }
                        return obj.id !== id;
                    });
                    self.photos.excluded = self.photos.excluded.filter(function( obj ) {
                        if(obj.id == id) {
                            obj.mediaItem.status = 'DELETED';
                            self.saveMedia(obj);
                        }
                        return obj.id !== id;
                    });
                } else if (self.currentTab == 'Attachments'){
                    self.files = this.files.filter(function( obj ) {
                        if(obj.id == id) {
                            obj.mediaItem.status = 'DELETED';
                            self.saveMedia(obj);
                        }
                        return obj.id !== id;
                    });
                } else if (self.currentTab == 'Maps') {
                    self.maps = this.maps.filter(function( obj ) {
                        if(obj.id == id) {
                            obj.mediaItem.status = 'DELETED';
                            self.saveMedia(obj);
                        }
                        return obj.id !== id;
                    });
                } else if (self.currentTab == 'Reports') {
                    self.reports = this.reports.filter(function( obj ) {
                        if(obj.id == id) {
                            obj.mediaItem.status = 'DELETED';
                            self.saveMedia(obj);
                        }
                        return obj.id !== id;
                    });
                }
            },
            downloadMedia: function(url) {
                window.open(url, url);
            },
            saveMedia: function(data, callback) {
                var url = jsRoutes.controllers.MediaController.updateMedia().url;
                $.ajax({
                    type: "POST",
                    cache: false,
                    url:  url,
                    processData: false,
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    async: false
                }).done(function (response) {
                    if(callback) callback();
                });
            },
            saveAllMedia: function(next, persist) {
                var self = this;
                self.isDataSaving = persist;
                $.each(self.photos.included.concat(self.photos.excluded).concat(self.files).concat(self.maps).concat(self.reports), function(i, media) {
                    self.saveMedia(media);
                });
                var event = {};
                event.next = next;
                self.sendToFramework(event);
            },
            sendToFramework: function(event) {
                var self = this;
                EventBus.$emit('home-valuation-photos-attachments', event);
                setTimeout(function() {
                    self.isDataSaving = false;
                }, 1000);
            },
            includeReport: function(key, event) {
                var self = this;
                var tags = self.files[key].tags;
                var include = event.target.checked;
                if(include) {
                    if(tags.indexOf('selected') < 0) tags.push('selected');
                } else {
                    tags = tags.filter(function( obj ) {
                        return obj !== "selected";
                    });
                }
                self.files[key].tags = tags;
                self.saveMedia(self.files[key]);
            },
            setMapTag: function(id, value) {
                var self = this;
                var otherTag = (value == 'aerialView') ? 'locationMap' : 'aerialView';
                $(this.maps).each(function(index, map){
                    if(map.id != id) {
                        map.tags = this.tags.filter(function( obj ) {
                            return obj !== value;
                        });
                        map[value] = false;
                    } else {
                        if(map.tags.indexOf(value) < 0) {
                            map.tags.push(value);
                            map[value] = true;
                            if(map.tags.indexOf(otherTag) > -1) {
                                map.tags = this.tags.filter(function( obj ) {
                                    return obj !== otherTag;
                                });
                                map[otherTag] = false;
                            }
                        }
                    }
                    self.saveMedia(self.maps[index]);
                });
            },
            setComment: function(map) {
                var self = this;
                map.tags = map.tags.filter(function( obj ) {
                    return !obj.startsWith("comment=");
                });
                if (map.comment) {
                    map.tags.push("comment=" + map.comment);
                }
                self.saveMedia(map);
            },
            hasValidPhotosSelected: function() {
                var hasValidPhotosSelected = this.photos.included.filter(function( obj ) {
                    return obj.tags.indexOf('selected') > -1 && (obj.mediaItem.tags.length > 0 || (obj.description && obj.description != ''));
                });
                return hasValidPhotosSelected.length > 0 ? true : false;
            },
            includePhoto: function(key, group, event) {
                var self = this;
                var media = self.photos[group][key];
                if(group == 'included') {
                    media.selected = false;
                    media.tags = media.tags.filter(function( obj ) {
                        return obj !== 'selected';
                    });
                    media.isPrimary = false;
                    self.saveMedia(media, function() {
                        if(self.photos.excluded.filter(function(m){ return m.id == media.id }).length == 0) {
                            self.photos.excluded.push(media);
                        }
                        self.photos.included = self.photos.included.filter(function( obj ) {
                            return obj.id !== media.id;
                        });
                    });
                } else {
                    if (self.photos.included.length >= 11) {
                        $('.homeValuationPhotoError').show();
                        self.photos[group][key].selected = false;
                        event.preventDefault();
                    } else {
                        media.tags.push('selected');
                        media.selected = true;
                        self.saveMedia(media, function() {
                            self.photos.included.push(media);
                            self.photos.excluded = self.photos.excluded.filter(function( obj ) {
                                return obj.id !== media.id;
                            });
                        });
                    }
                }
            },
            doRelinking: function(relink) {
                var self = this;
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.MediaController.linkMedia().url,
                    cache: false,
                    processData: false,
                    contentType: 'application/json',
                    data: JSON.stringify(relink),
                    async: false,
                    success: function (response) {
                        console.log("Photo moved to property " + relink.ownerId);
                    },
                    error: function (response) {
                        console.log("Error in subdivision relink");
                        self.errorHandler(response);
                    }
                });
            },
            /**
             * This method takes Improvement Date Range code and Tags code and returns the comma-separated list of descriptions
             */
            generatePhotoTags(improvementDateRange, tags) {
                // Retrieve the description for the improvement date range
                const improvementDateRangeDescription = this.improvementDateRangeDescriptions[improvementDateRange];
                // Sort the tags, then for each tag retrieve the classification
                // Note: Need to spread the tags array into a new array because sort method changes the original array.
                const tagsToDisplay = [...tags].sort().map(tag => this.tagsDescriptions[tag]);
                // Array of tags and improvement date range description
                const displayedTagsArray = [improvementDateRangeDescription, ...tagsToDisplay].filter(description => !!description);
                return displayedTagsArray.join(', ');
            },
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
            EventBus.$off('notify-multi-nested-photo-attachments', this.listener);
            EventBus.$off('notify-multi-nested-report-attachments', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
            EventBus.$off('notify-multi-nested-photo-attachments', this.listener);
            EventBus.$off('notify-multi-nested-report-attachments', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

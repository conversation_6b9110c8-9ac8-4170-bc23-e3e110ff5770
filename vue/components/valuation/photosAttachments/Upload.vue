<template>
    <div class="QVHV-formSection">
        <div class="advSearch-row">
            <div class="uploaderBody dragNdrop">
                <div action="no/presigned/url/assigned" class="dropzone" :id="'photo-dropzone-'+category"></div>
            </div>
            <div data-cy="upload-confirm" class="uploadConfirm" v-if="mediaProcessed.length == mediaDropped.length && mediaProcessed.length > 0">
                <i title="Continue" @click="showViewer" class="material-icons md-48"></i>
            </div>
        </div>
    </div>
</template>

<script>
    import * as EXIF from '../../../exif.js';
    import * as Dropzone from '../../../dropzone';

    export default {
        props: ["continueHandler", "ownerId", "category", "maxFiles"],
        data: function() {
            return {
                mediaDropped: [],
                mediaProcessed: []
            }
        },
        methods: {
            maxFilesAllowed: function() {
                var result = null;
                if(this.maxFiles != 'undefined' && this.maxFiles != null && this.maxFiles > 0) {
                    console.log("Is allow Multiple is: " + this.maxFiles);
                    result = this.maxFiles;
                }
                return result;
            },
            showViewer: function() {
                this.continueHandler();
                this.mediaDropped = [];
                this.mediaProcessed = [];
            },
            getCapturedDate: function(exif) {
                var dateString = new Date();
                try{
                    console.log("dateString: " + dateString);
                    console.log("exif: " + exif + " exif.DateTimeOriginal: " + exif.DateTimeOriginal + " exif.DateTime: " + exif.DateTime);
                    if (exif && (exif.DateTimeOriginal || exif.DateTime)){
                        console.log("inside exif = OK");
                        var dateSplit = (exif.DateTimeOriginal || exif.DateTime).split(" ");
                        dateSplit[0] = dateSplit[0].split(":").join("-");
                        var date = new Date(dateSplit[0] + "T" + dateSplit[1]);
                        if(date != "Invalid Date"){
                            var today = new Date();
                            var timeinmilisec = today.getTime() - date.getTime();
                            var days = Math.ceil(timeinmilisec / (1000 * 60 * 60 * 24));
                            console.log( "days: " + days);
                            if (days < 365 && date <= today) {
                                //dateString = date.toUTCString(); //testing only
                                dateString = date;
                            }
                            console.log("dateString(valid): " + dateString);
                        }
                    }
                } catch(e){
                    dateString = new Date();
                    console.log("dateString(ex): " + dateString);
                }
                return dateString.setHours(13,0,0,0);
            },
            getMediaIndexField: function(propertyName, value) {
                var self = this;
                for (var i = 0; i < self.mediaDropped.length; i++)
                    if (self.mediaDropped[i][propertyName] === value && self.mediaDropped[i].uploaded === false) {
                        return i;
                    }
                return -1;
            },
            onDropzoneDone: function(success, file) {
                var self = this;
                var i = self.getMediaIndexField("mediaId", file.mediaId);
                if(self.mediaDropped[i] && file.accepted == true) {
                    var mediaEntry = self.mediaDropped[i].mediaEntry;
                    mediaEntry.mediaItem.status = success ? "UPLOAD_COMPLETED" : "UPLOAD_FAILED";
                    var url = jsRoutes.controllers.MediaController.saveMedia().url;
                    $.ajax({
                        type: "POST",
                        cache: false,
                        url:  url,
                        processData: false,
                        contentType: 'application/json',
                        data: JSON.stringify(mediaEntry)
                    }).done(function (response) {
                        if(self.mediaProcessed.indexOf(file.mediaId) < 0 && file.mediaId) self.mediaProcessed.push(file.mediaId);
                    });
                } else {
                    if(self.mediaProcessed.indexOf(file.mediaId) < 0 && file.mediaId) self.mediaProcessed.push(file.mediaId);
                }
            }
        },
        mounted: function() {
            var self = this;
            var acceptedFiles = '.png,.jpg,.gif,.bmp,.jpeg';
            if(self.category == 'HomeValuationAttachment' || self.category == 'HomeValuationReport') {
                acceptedFiles = '.png,.jpg,.gif,.bmp,.jpeg,.pdf,.xls,.xlsx,.doc,.docx,.tif,.tiff';
            }
            Dropzone.autoDiscover = false;
            var dropzone = new Dropzone("#photo-dropzone-" + self.category,{
                autoDiscover: false,
                acceptedFiles: acceptedFiles,
                autoProcessQueue: false,
                method: 'PUT',
                contentType: false,
                maxFiles: self.maxFilesAllowed(),
                uploadMultiple: true});

            dropzone.on("addedfile", function (file) {
                EXIF.getData(file, function() {
                    var dateString = self.getCapturedDate(EXIF.getAllTags(this));
                    var data = {
                        "ownerId": self.ownerId,
                        "category": self.category,
                        "isPrimary":false,
                        "description":"",
                        "tags": [],
                        "mediaItem":{
                            "fileName": file.name,
                            "contentType": (file.type == 'image/tif') ? 'image/tiff' : file.type,
                            "uploadedDate": new Date(Date.now()).toUTCString(),
                            "captureDate": dateString,
                            "tags": [],
                            "improvementDateRange": ""
                        }
                    };
                    $.ajax({
                        type: "POST",
                        cache: false,
                        url:  jsRoutes.controllers.MediaController.generateMediaEntryID().url,
                        processData: false,
                        contentType: 'application/json',
                        data: JSON.stringify(data)
                    }).done(function (response) {
                        dropzone.options.url = response.mediaItem.imageUrl;
                        dropzone.options.headers = {'Content-Type': (file.type == 'image/tif') ? 'image/tiff' : file.type};
                        self.mediaDropped.push({
                            mediaId: response.mediaItem.id,
                            fileName: response.mediaItem.fileName,
                            mediaEntry: response,
                            uploaded: false
                        });
                        file.mediaId = response.mediaItem.id;
                        dropzone.processFile(file);
                    });
                });
            });

            dropzone.on("success", function (file) {
                self.onDropzoneDone(true, file);
            });

            dropzone.on("error", function (file) {
                self.onDropzoneDone(false, file);
            });
        }
    }
</script>

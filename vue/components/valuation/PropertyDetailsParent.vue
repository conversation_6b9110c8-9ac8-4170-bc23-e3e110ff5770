<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div>
        <div class="md-table mdl-shadow--2dp">
            <h2>Property Details</h2>
            <div data-cy="property-details-expand-all" class="expandAll" @click="expandTabs()" v-bind:class="[tabState == 'open' ? 'down' : '']">
                <span title="Expand Form" class="mdl-button mdl-js-button mdl-button--icon"><i class="material-icons md-dark"></i></span>
            </div>
            <ul id="tabsElement" v-bind:class="[tabState=='open' ? 'hide' : 'QVHV-tabs']">
                <!-- ^^ THE ELEMENT ".QVHV-tabs" SHOULD GET THE .hide CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->
                <li class="QVHVTab-1" @click="setCurrentTab('Overview', 1, true)" data-tab="QVHVTab-1" data-container="housedetails"><span v-bind:class="[currentTab == 'Overview' || tabState == 'open' ? 'is-active' : '']">Overview</span></li>
                <li class="QVHVTab-2" @click="setCurrentTab('Interior', 2, true)" data-tab="QVHVTab-2" data-container="mainbuilding"><span v-bind:class="[currentTab == 'Interior' || tabState == 'open' ? 'is-active' : '']">Interior</span></li>
                <li class="QVHVTab-3" @click="setCurrentTab('Bedrooms', 3, true)" data-tab="QVHVTab-3" data-container="bedrooms"><span v-bind:class="[currentTab == 'Bedrooms' || tabState == 'open' ? 'is-active' : '']">Bedrooms</span></li>
                <li class="QVHVTab-4" @click="setCurrentTab('Kitchen', 4, true)" data-tab="QVHVTab-4" data-container="kitchen"><span v-bind:class="[currentTab == 'Kitchen' || tabState == 'open' ? 'is-active' : '']">Kitchen</span></li>
                <li class="QVHVTab-5" @click="setCurrentTab('Bathrooms', 5, true)" data-tab="QVHVTab-5" data-container="bathrooms"><span v-bind:class="[currentTab == 'Bathrooms' || tabState == 'open' ? 'is-active' : '']">Bathrooms</span></li>
                <li class="QVHVTab-6" @click="setCurrentTab('Garaging', 6, true)" data-tab="QVHVTab-6" data-container="garaging"><span v-bind:class="[currentTab == 'Garaging' || tabState == 'open' ? 'is-active' : '']">Garaging </span></li>
                <li class="QVHVTab-7" @click="setCurrentTab('Improvements', 7, true)" data-tab="QVHVTab-7" data-container="siteimprovements"><span v-bind:class="[currentTab == 'Improvements' || tabState == 'open' ? 'is-active' : '']">Improvements</span></li>
                <hr align="left"/>
            </ul>

            <overview v-show="currentTab=='Overview' || tabState == 'open'" class="homeValTab" v-bind:class="{disabled: readOnly}"></overview>
            <interior v-show="currentTab=='Interior' || tabState == 'open'" class="homeValTab" v-bind:class="{disabled: readOnly}"></interior>
            <bedrooms v-show="currentTab=='Bedrooms' || tabState == 'open'" class="homeValTab" v-bind:class="{disabled: readOnly}"></bedrooms>
            <kitchen v-show="currentTab=='Kitchen' || tabState == 'open'" class="homeValTab" v-bind:class="{disabled: readOnly}"></kitchen>
            <bathrooms v-show="currentTab=='Bathrooms' || tabState == 'open'" class="homeValTab" v-bind:class="{disabled: readOnly}"></bathrooms>
            <garaging v-show="currentTab=='Garaging' || tabState == 'open'" class="homeValTab" v-bind:class="{disabled: readOnly}"></garaging>
            <improvements v-show="currentTab=='Improvements' || tabState == 'open'" class="homeValTab" v-bind:class="{disabled: readOnly}"></improvements>
        </div>
        <div class="QVHV-buttons" v-bind:class="{disabled: readOnly}">
            <div class="QVHV-buttons-left" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="primary" v-on:click="savePropertyDetailsData(false, true, true)">Save</button>
            </div>
            <div class="QVHV-buttons-right" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="secondary" v-on:click="previousStep()">Back</button>
                <button class="primary" v-on:click="savePropertyDetailsData(true, true, true)">Next Step</button>
            </div>
        </div>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import deepEqual from 'deep-equal';
    import Overview from './propertyDetails/Overview.vue';
    import Interior from './propertyDetails/Interior.vue';
    import Bedrooms from './propertyDetails/Bedrooms.vue';
    import Kitchen from './propertyDetails/Kitchen.vue';
    import Bathrooms from './propertyDetails/Bathrooms.vue';
    import Garaging from './propertyDetails/Garaging.vue';
    import Improvements from './propertyDetails/Improvements.vue';
    import moment from 'moment';

    export default {
        props: ['readOnly'],
        components: {
            Overview,
            Interior,
            Bedrooms,
            Kitchen,
            Bathrooms,
            Garaging,
            Improvements
        },
        data: function() {
            return {
                propertyDetails: {
                    houseDetails: {},
                    interiorDetails: {},
                    bedroomDetails: {},
                    kitchenDetails: {},
                    bathroomDetails: {},
                    garagingDetails: [],
                    otherBuildingDetails: [],
                    improvementDetails: {}
                },
                currentTab: 'Overview',
                tabState: 'closed',
                isDataSaving: false,
                homeValuation: {},
                propertyDetailsCopy:  {
                    houseDetails: {},
                    interiorDetails: {},
                    bedroomDetails: {},
                    kitchenDetails: {},
                    bathroomDetails: {},
                    garagingDetails: [],
                    otherBuildingDetails: [],
                    improvementDetails: {}
                },
                isDirty: false
            }
        },
        methods: {
            expandTabs: function() {
                const self = this
                if (self.tabState == 'closed') {
                    self.tabState = 'open'
                }
                else {
                    self.tabState = 'closed'
                }
                EventBus.$emit('property-details-tabs', self.tabState)
            },
            savePropertyDetailsData: function(nextStep, persist, disableButtons){
                const self = this
                //Save data to service here
                var event = {};
                event.propertyDetails = self.propertyDetails;
                event.next = nextStep;
                event.persist = persist;
                if(disableButtons) self.isDataSaving = persist;
                var noChanges = true;
                $.each(['houseDetails','interiorDetails','bedroomDetails','kitchenDetails','bathroomDetails','garagingDetails','otherBuildingDetails','improvementDetails'], function (index, item) {
                    noChanges = self.jsonEqual(self.propertyDetails[item], self.propertyDetailsCopy[item]) && noChanges;
                    if(!noChanges) {
                        console.log(JSON.stringify(self.propertyDetails[item]));
                        console.log(JSON.stringify(self.propertyDetailsCopy[item]));
                        return false;
                    }
                });
                event.isDirty = !noChanges;
                EventBus.$emit('home-valuation-property-details', event);
            },
            previousStep: function(){
                const self = this
                self.savePropertyDetailsData(false, true, false)
                EventBus.$emit("home-valuation-back", "jobsetupStepper");
            },
            setCurrentTab: function(name, index, save){
                const self = this;
                self.currentTab = name;
                $('#tabsElement').find('hr')
                    .removeClass()
                    .addClass('QVHVTab-'+ index );
                if (save) {
                    setTimeout(function() {
                        self.savePropertyDetailsData(false, true, false)
                    }, 1000);
                }
            },
            jsonEqual: function jsonEqual(a,b) {
                return deepEqual(a,b);
            }
        },
        mounted: function() {
        },
        created: function() {
            var self = this;
            EventBus.$on('enable-buttons', function(){
                self.isDataSaving = false
            })
            EventBus.$on('notify-parent', function(obj){
                self.propertyDetails[obj.key] = obj.value;
                self.savePropertyDetailsData(false,false,false);
            })
            EventBus.$on('home-valuation-saved', function(obj){
                var fromStep = obj.fromStep;
                if(fromStep != 2 || obj.reload) {
                    if(self.homeValuation == {} || self.homeValuation.id != obj.homeValuation.id) {
                        self.homeValuation = JSON.parse(JSON.stringify(obj.homeValuation));
                        self.propertyDetails = self.homeValuation.propertyDetails ? self.homeValuation.propertyDetails : {};
                    }
                }
                self.propertyDetailsCopy = JSON.parse(JSON.stringify(self.propertyDetails));
                self.isDataSaving = false;
            })
            EventBus.$on('home-valuation-new', function(propertyData) {
                self.setCurrentTab('Overview', 1, false)
                self.propertyDetails = {}
                self.propertyDetails.houseDetails= {}
                self.propertyDetails.interiorDetails = {}
                self.propertyDetails.bedroomDetails = {}
                self.propertyDetails.kitchenDetails = {}
                self.propertyDetails.bathroomDetails = {}
                self.propertyDetails.garagingDetails = []
                self.propertyDetails.otherBuildingDetails = []
                self.propertyDetails.improvementDetails = {}
            });
            EventBus.$on('home-valuation-loaded', function(){
                self.setCurrentTab('Overview', 1, false)
            })
        },
        destroyed: function() {
            EventBus.$off('notify-parent', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('notify-parent', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
        }
    }
</script>

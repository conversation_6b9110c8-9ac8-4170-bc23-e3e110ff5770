<template>
    <!-- RATING INFORMATION STARTS -->
    <div class="QVHV-Container ratinginformation active" v-bind:class="[tabState=='open' ? 'canOpener' : '']"
         v-if="showRatingInformationTemplate">
        <ul class="QVHV-tabs hide">
            <li><span class="is-active">Rating Information</span></li>
            <hr align="left"/>
        </ul>
        <div class="QVHV-formSection">
            <div class="advSearch-row">
                <text-input id="ratingInfoCapitalValue" :curr-val="getFormattedDollarValue(ratingDetails.capitalValue)" attr-name="capitalValue"
                            fieldType="text" iconClass="advSearch-group twentyfivePct icons8-sell-property-filled"
                            label="Capital Value" component-name="rating-information" :class="[fields.capitalValue]"></text-input>
                <text-input id="ratingInfoLandValue" :curr-val="getFormattedDollarValue(ratingDetails.landValue)" attr-name="landValue"
                            fieldType="text" iconClass="advSearch-group twentyfivePct icons8-sell-property-filled"
                            label="Land Value" component-name="rating-information" :class="[fields.landValue]"></text-input>
                <div class="advSearch-group twentyfivePct icons8-sell-property-filled" :class="[fields.valueOfImprovements]">
                    <label>Value of Improvements</label>
                    <span><input class="advSearch-text" v-model="valueOfImprovements" type="text"
                                 disabled="disabled"></span>
                    <div class="valMessage"></div>
                </div>
                <text-input id="ratingInfoEffectiveDate" :curr-val="getFormattedDate(ratingDetails.currentRevisionDate)"
                            attr-name="currentRevisionDate" fieldType="date" :format="validDateFormats"
                            iconClass="advSearch-group twentyfivePct icons8-calendar-filled"
                            label="Effective Date" component-name="rating-information" :class="[fields.currentRevisionDate]"></text-input>
            </div>
            <div class="advSearch-row">
                <text-input id="ratingInfoCurrentValue" :curr-val="getFormattedDollarValue(ratingDetails.currentRates, '$0,0.[00]')" attr-name="currentRates"
                            fieldType="text" iconClass="advSearch-group twentyfivePct icons8-money-filled"
                            label="Current Rates" component-name="rating-information" :class="[fields.currentRates]"></text-input>
                <text-input id="ratingInfoValuationReference" :curr-val="ratingDetails.valuationReference" attr-name="valuationReference"
                            fieldType="text" iconClass="advSearch-group twentyfivePct icons8-legal-description"
                            label="Valuation Reference" component-name="rating-information" :class="[fields.valuationReference]"></text-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" id="commentsArea" :curr-val="ratingDetails.comments"
                                 attr-name="comments" fieldType="text"
                                 iconClass="advSearch-group hundyPct icons8-pencil" label="Comment on Rating Valuation"
                                 component-name="rating-information" :class="[fields.comments]"></text-area-input>
            </div>
        </div>
    </div>
</template>

<script>
    import TextInput from '../../filters/TextInput.vue';
    import TextAreaInput from '../../filters/TextAreaInput.vue';
    import {EventBus} from '../../../EventBus.js';
    import {store} from '../../../DataStore';
    import numeral from 'numeral';
    import moment from 'moment';

    export default {
        components: {
            TextInput,
            TextAreaInput
        },
        data: function () {
            return {
                validDateFormats: ['DD/MM/YYYY', 'D/MM/YYYY', 'DD/M/YYYY', 'D/M/YYYY'],
                ratingDetails: {},
                valueOfImprovements: '$0',
                showRatingInformationTemplate: true,
                refreshRatingInformationTemplate: false,
                tabState: 'closed',
                defaultPropertyData: {},
                homeValuationId: undefined,
                reportType: {},
                fields: {}
            }
        },
        computed: {
            currentRevisionDate: function(){
                var self = this;
                var rdd = null;
                if(self.ratingDetails && self.ratingDetails.currentRevisionDate) {
                    var rDate = self.ratingDetails.currentRevisionDate;
                    var momentDate = moment.utc(rDate, 'DD/MM/YYYY');
                    if(momentDate.isValid()) {
                        rdd = momentDate.toDate();
                    }
                }
                return rdd;
            }
        },
        methods: {
            notifyParent: function () {
                const self = this;
                EventBus.$emit('notify-location-parent', [{'key': 'ratingDetails', 'value': self.ratingDetails}]);
            },
            getFormattedDollarValue: function (val, format) {
                if (val) {
                    if (format) {
                        return numeral(val).format(format)
                    }
                    return numeral(val).format('$0,0');
                }
                return '';
            },
            getFormattedDate: function (date) {
                if (date && date !== '') {
                    return moment.utc(String(date)).format('DD/MM/YYYY')
                }
                return ''
            },
            populateRatingDetails: function (propertyData) {
                const self = this;
                self.ratingDetails.capitalValue = propertyData.rawCapitalValue;
                self.ratingDetails.landValue = propertyData.rawLandValue;
                if (propertyData.rawCapitalValue > propertyData.rawLandValue) {
                    self.valueOfImprovements = self.getFormattedDollarValue(propertyData.rawCapitalValue - propertyData.rawLandValue);
                } else {
                    self.valueOfImprovements = '$0';
                }
                if (!self.ratingDetails.currentRevisionDate) {
                    self.ratingDetails.currentRevisionDate = propertyData.rawCurrentRevisionDate;
                }

                self.ratingDetails.valuationReference = propertyData.valuationReference;
                self.notifyParent();
            },
            reCalculateValueOfImprovements: function () {
                const self = this;
                if (numeral(self.ratingDetails.capitalValue).value() > numeral(self.ratingDetails.landValue).value()) {
                    var result = numeral(self.ratingDetails.capitalValue).value() - numeral(self.ratingDetails.landValue).value();
                    self.valueOfImprovements = self.getFormattedDollarValue(result);
                } else {
                    self.valueOfImprovements = '$0';
                }
            },
            repopulateRatingDetails: function (ratingDetails) {
                const self = this;
                self.ratingDetails.capitalValue = ratingDetails.capitalValue;
                self.ratingDetails.landValue = ratingDetails.landValue;
                self.ratingDetails.valueOfImprovements = self.reCalculateValueOfImprovements();
                self.ratingDetails.currentRevisionDate = ratingDetails.currentRevisionDate;
                self.ratingDetails.currentRates = ratingDetails.currentRates;
                self.ratingDetails.comments = ratingDetails.comments;
                self.ratingDetails.valuationReference = ratingDetails.valuationReference;
            },
            isEmpty: function (ratingDetails) {
                for (var key in ratingDetails) {
                    if (ratingDetails.hasOwnProperty(key)) {
                        return false;
                    }
                }
                return true;
            }
        },
        mounted: function () {
            const self = this;
            EventBus.$on('notify-simple-rating-information', function (data) {
                self.ratingDetails[data.attrName] = data.val;
                if (data.attrName == 'capitalValue' || data.attrName == 'landValue' || data.attrName == 'currentRates') {
                    self.ratingDetails.capitalValue = numeral(self.ratingDetails.capitalValue).value();
                    self.ratingDetails.landValue = numeral(self.ratingDetails.landValue).value();
                    self.ratingDetails.currentRates = numeral(self.ratingDetails.currentRates).value();
                    self.reCalculateValueOfImprovements();
                    self.showRatingInformationTemplate = false;
                    self.refreshRatingInformationTemplate = true;
                }
                if (data.attrName == 'currentRevisionDate') {
                    self.ratingDetails.currentRevisionDate = self.currentRevisionDate
                }
                self.notifyParent();
            });
            EventBus.$on('notify-multi-rating-information', function (data) {
                self.ratingDetails[data.attrName] = data.val;
                self.notifyParent();
            });
        },
        created: function () {
            var self = this;
            EventBus.$on('home-valuation-saved', function (obj) {
                var loadingNewJob = self.homeValuationId != obj.homeValuation.id;
                if(loadingNewJob || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.homeValuationId = homeValuation.id;
                    self.ratingDetails = JSON.parse(JSON.stringify(homeValuation.locationDetails ? (homeValuation.locationDetails.ratingDetails ? homeValuation.locationDetails.ratingDetails : {}) : {}));
                    if (self.isEmpty(self.ratingDetails)) {
                        self.populateRatingDetails(self.defaultPropertyData);
                    } else {
                        self.repopulateRatingDetails(self.ratingDetails);
                    }
                    self.showRatingInformationTemplate = false;
                    if (!self.showRatingInformationTemplate) {
                        self.refreshRatingInformationTemplate = true;
                    }
                }
            });

            EventBus.$on('home-valuation-new', function (propertyData) {
                self.ratingDetails = {};
                self.populateRatingDetails(propertyData);
                self.defaultPropertyData = propertyData;
                self.showRatingInformationTemplate = !self.showRatingInformationTemplate;
                self.refreshRatingInformationTemplate = true;
            });

            EventBus.$on('location-details-tabs', function (state) {
                self.tabState = state;
            });

            EventBus.$on('load-default-property-data', function (propertyData) {
                self.defaultPropertyData = propertyData;
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.locationDetails.ratingInformation;
                }
            });

        },
        updated: function () {
            const self = this;
            if (self.refreshRatingInformationTemplate) {
                self.showRatingInformationTemplate = true;
                self.refreshRatingInformationTemplate = false;
            }
        },
        destroyed: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-rating-information', this.listener);
            EventBus.$off('notify-multi-rating-information', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-rating-information', this.listener);
            EventBus.$off('notify-multi-rating-information', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

<template>
    <div class="QVHV-Container zoning active" v-bind:class="[tabState=='open' ? 'canOpener' : '']"
         v-if="showZoningTemplate">
        <ul class="QVHV-tabs">
            <li><span class="is-active">Zoning</span></li>
            <hr>
        </ul>
        <div class="QVHV-formSection">
            <div class="advSearch-row">
                <valuation-multi-select-filter :curr-val="currentUse"
                                               :on-dropdown-hide="callback"
                                               iconClass="advSearch-group fiftyPct icons8-todo-list-filled"
                                               component-name="currentUse" attrName="currentUse" filter-id="currentUse"
                                               label="Current Use"
                                               selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="false"
                                               data-to-fetch="CurrentUse"
                                               :class="[fields.current.currentUse]"></valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter use-short-description="true"
                                               :curr-val="operativeZoningDetails.zoning"
                                               :on-dropdown-hide="callback"
                                               iconClass="advSearch-group fiftyPct icons8-todo-list-filled"
                                               component-name="operative-zoning" attrName="zoning"
                                               filter-id="operativeZoning" label="Operative Zoning"
                                               selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="true" data-to-fetch="TAZoning" :class="[fields.current.operativeZoning]">
                </valuation-multi-select-filter>
                <div class="advSearch-group advSearch-group twentyfivePct icons8-surface-filled required">
                    <label>EQC - Minimum Site Size(m<sup>2</sup>)</label>
                    <number-input maxlength="50" attr-name="eqcMinimumSiteSize"
                            :value="operativeZoningDetails.eqcMinimumSiteSize" format="0,0" @change="updateCalculatedField(parseFloat($event.target.value), 'eqcMinimumSiteSize')"/>
                </div>   
                <div class="advSearch-group advSearch-group twentyfivePct icons8-surface-filled required">
                    <label>EQC - Minimum Site Value</label>
                    <number-input maxlength="50" attr-name="eqcMinimumSiteValue"
                            :value="operativeZoningDetails.eqcMinimumSiteValue" format="$0,0" @change="updateCalculatedField(parseFloat($event.target.value), 'eqcMinimumSiteValue')"/>
                </div>    
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="2000" :curr-val="operativeZoningDetails.zoneDescription"
                                 attr-name="zoneDescription"
                                 fieldType="text" iconClass="advSearch-group hundyPct icons8-pencil"
                                 label="Zone Description" component-name="operative-zoning" :class="[fields.current.zoneDescription]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" :curr-val="operativeZoningDetails.siteDensity" attr-name="siteDensity"
                                 fieldType="text"
                                 iconClass="advSearch-group hundyPct icons8-pencil" label="Site Density"
                                 component-name="operative-zoning" :class="[fields.current.siteDensity]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" :curr-val="operativeZoningDetails.zoneComments"
                                 attr-name="zoneComments" fieldType="text"
                                 iconClass="advSearch-group hundyPct icons8-pencil"
                                 label="Zone Comments"
                                 component-name="operative-zoning" :class="[fields.current.zoneComments]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-input maxlength="1000" :curr-val="operativeZoningDetails.doesCurrentUseComply"
                            attr-name="doesCurrentUseComply"
                            fieldType="text" iconClass="advSearch-group hundyPct icons8-pencil"
                            label="Does current use comply?" component-name="operative-zoning" :class="[fields.current.doesCurrentUseComply]"></text-input>
            </div>
            <div class="advSearch-row">
                <text-input maxlength="1000" :curr-val="operativeZoningDetails.subdivisionPotential"
                            attr-name="subdivisionPotential"
                            fieldType="text" iconClass="advSearch-group hundyPct icons8-pencil"
                            label="Subdivision Potential" component-name="operative-zoning" :class="[fields.current.subdivisionPotential]"></text-input>
            </div>

            <!-- PROPOSED ZONING -->
            <input type="checkbox" id="Proposed Zoning Values" class="proposedZone-trigger"
                   v-model="showProposedZoningTemplate" @change="EmptyProposedZoning()">
            <label for="Proposed Zoning Values" class="proposedZone-trigger"><h3>Proposed Zoning Details</h3></label>
            <div class="propsedZoning-values">
                <div class="advSearch-row">
                    <valuation-multi-select-filter use-short-description="true"
                                                   :curr-val="proposedZoningDetails.zoning"
                                                   :on-dropdown-hide="callback"
                                                   iconClass="advSearch-group fiftyPct icons8-todo-list-filled"
                                                   component-name="proposed-zoning" attrName="zoning"
                                                   filter-id="proposedZoning"
                                                   label="Proposed Zoning"
                                                   selectClass="advSearch-singleselect monarch-multiselect"
                                                   chooseHere="true" data-to-fetch="TAPZoning" :class="[fields.proposed.proposedZoning]">
                    </valuation-multi-select-filter>
                </div>
                <div class="advSearch-row">
                    <text-area-input maxlength="2000" :curr-val="proposedZoningDetails.zoneDescription"
                                     attr-name="zoneDescription"
                                     fieldType="text" iconClass="advSearch-group hundyPct icons8-pencil"
                                     label="Proposed Zone Description"
                                     component-name="proposed-zoning" :class="[fields.proposed.zoneDescription]"></text-area-input>
                </div>
                <div class="advSearch-row">
                    <text-area-input maxlength="1000" :curr-val="proposedZoningDetails.siteDensity"
                                     attr-name="siteDensity"
                                     fieldType="text" iconClass="advSearch-group hundyPct icons8-pencil"
                                     label="Proposed Site Density" component-name="proposed-zoning" :class="[fields.proposed.siteDensity]"></text-area-input>
                </div>
                <div class="advSearch-row">
                    <text-area-input maxlength="1000" :curr-val="proposedZoningDetails.zoneComments"
                                     attr-name="zoneComments" fieldType="text"
                                     iconClass="advSearch-group hundyPct icons8-pencil"
                                     label="Proposed Zone Comments"
                                     component-name="proposed-zoning" :class="[fields.proposed.zoneComments]"></text-area-input>
                </div>
                <div class="advSearch-row">
                    <text-input maxlength="1000" :curr-val="proposedZoningDetails.doesCurrentUseComply"
                                attr-name="doesCurrentUseComply"
                                fieldType="text" iconClass="advSearch-group hundyPct icons8-pencil"
                                label="Does current use comply?"
                                component-name="proposed-zoning" :class="[fields.proposed.doesCurrentUseComply]"></text-input>
                </div>
                <div class="advSearch-row">
                    <text-input maxlength="1000" :curr-val="proposedZoningDetails.subdivisionPotential"
                                attr-name="subdivisionPotential"
                                fieldType="text" iconClass="advSearch-group hundyPct icons8-pencil"
                                label="Subdivision Potential" component-name="proposed-zoning" :class="[fields.proposed.subdivisionPotential]"></text-input>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue';
    import TextInput from '../../filters/TextInput.vue';
    import TextAreaInput from '../../filters/TextAreaInput.vue';
    import {EventBus} from '../../../EventBus.js';
    import {store} from '../../../DataStore';
    import Vue from 'vue';
    import commonUtils from '../../../utils/CommonUtils';
    import numeral from 'numeral';
    import NumberInput from '../../common/form/NumberInput.vue';

    export default {
        components: {
            TextInput,
            TextAreaInput,
            ValuationMultiSelectFilter,
            NumberInput
        },
        mixins: [commonUtils],
        data: function () {
            return {
                currentUse: {},
                operativeZoningDetails: {},
                proposedZoningDetails: {},
                eqcMinimumSiteSize: null,
                eqcMinimumSiteValue: null,
                showZoningTemplate: true,
                refreshZoningTemplate: false,
                tabState: 'closed',
                parentCode: "",
                showProposedZoningTemplate: false,
                operativeDescriptionMap: {},
                proposedDescriptionMap: {},
                operativeZoningBuffer: {},
                proposedZoningBuffer: {},
                operativeDetailsBuffer: {},
                proposedDetailsBuffer: {},
                defaultDoesCurrentUseComply: "We have carried out appropriate investigations and it would appear that the property complies with the provisions of the District Plan. However we have not sought town planning advice or written confirmation to verify such. If required the zoning should be verified by application to council for the issue of a Land Information Memorandum.",
                defaultSubdivisionPotential: "No",
                homeValuationId: undefined,
                reportType: {},
                fields: {
                    current: {},
                    proposed: {}
                }
            }
        },
        methods: {
            callback: function () {
                var self = this;
                self.showZoningTemplate = false;
                self.refreshZoningTemplate = true;
            },
            notifyParent: function () {
                const self = this;
                EventBus.$emit('notify-location-parent', [
                    {'key': 'currentUse', 'value': self.currentUse},
                    {'key': 'operativeZoningDetails', 'value': self.operativeZoningDetails},
                    {'key': 'proposedZoningDetails', 'value': self.proposedZoningDetails}
                ]);
            },
            repopulateCurrentUseType: function (currentUse) {
                const self = this;
                self.currentUse = currentUse;
            },
            repopulateOperativeZoningDetails: function (operativeZoningInfo) {
                const self = this;
                self.operativeZoningDetails.zoning = operativeZoningInfo.zoning;
                self.operativeZoningDetails.zoneDescription = operativeZoningInfo.zoneDescription;
                self.operativeZoningDetails.siteDensity = operativeZoningInfo.siteDensity;
                self.operativeZoningDetails.zoneComments = operativeZoningInfo.zoneComments;
                self.operativeZoningDetails.doesCurrentUseComply = operativeZoningInfo.doesCurrentUseComply;
                self.operativeZoningDetails.subdivisionPotential = operativeZoningInfo.subdivisionPotential;
            },
            repopulateProposedZoningDetails: function (proposedZoningInfo) {
                const self = this;
                self.proposedZoningDetails.zoning = proposedZoningInfo.zoning;
                self.proposedZoningDetails.zoneDescription = proposedZoningInfo.zoneDescription;
                self.proposedZoningDetails.siteDensity = proposedZoningInfo.siteDensity;
                self.proposedZoningDetails.zoneComments = proposedZoningInfo.zoneComments;
                self.proposedZoningDetails.doesCurrentUseComply = proposedZoningInfo.doesCurrentUseComply;
                self.proposedZoningDetails.subdivisionPotential = proposedZoningInfo.subdivisionPotential;
            },
            getFormattedDollarValue: function (val) {
                if (val) {
                    return numeral(val).format('$0,0');
                }
                return '';
            },
            updateCalculatedField: function(value, fieldToUpdate) {
                if (isNaN(value)) {
                    this.operativeZoningDetails[fieldToUpdate] = null;
                } else {
                    this.operativeZoningDetails[fieldToUpdate] = parseFloat(value.toFixed(0));
                }
                this.notifyParent();
            },
            populateZoningData: function (propertyData) {
                const self = this;
                var category = propertyData.category;
                var currentOption = {};
                if (category.code.startsWith("R")
                    && (!category.code.startsWith("RB") && !category.code.startsWith("RM") && !category.code.startsWith("RV"))) {
                    currentOption['category'] = category.category;
                    currentOption['code'] = "04";
                    currentOption['description'] = "Residential";
                } else if (category.code.startsWith("CR")) {
                    currentOption['category'] = category.category;
                    currentOption['code'] = "05";
                    currentOption['description'] = "Retail";
                } else if (category.code.startsWith("C") && (!category.code.startsWith("CR") && !category.code.startsWith("CV"))) {
                    currentOption['category'] = category.category;
                    currentOption['code'] = "01";
                    currentOption['description'] = "Commercial";
                } else if (category.code.startsWith("I") && !category.code.startsWith("IV")) {
                    currentOption['category'] = category.category;
                    currentOption['code'] = "02";
                    currentOption['description'] = "Industrial";
                } else if (category.code.startsWith("L") && (!category.code.startsWith("LB") && !category.code.startsWith("LV"))) {
                    currentOption['category'] = category.category;
                    currentOption['code'] = "03";
                    currentOption['description'] = "Lifestyle";
                } else if ((category.code.startsWith("O") || category.code.startsWith("U")) && !category.code.startsWith("OV")) {
                    currentOption['category'] = category.category;
                    currentOption['code'] = "08";
                    currentOption['description'] = "Other";
                } else if (category.code.startsWith("A")
                    || category.code.startsWith("D")
                    || category.code.startsWith("F")
                    || category.code.startsWith("H")
                    || category.code.startsWith("M")
                    || category.code.startsWith("P")
                    || category.code.startsWith("S")) {
                    currentOption['category'] = category.category;
                    currentOption['code'] = "06";
                    currentOption['description'] = "Rural";
                } else if (category.code.startsWith("RB")
                    || category.code.startsWith("RM")
                    || category.code.startsWith("RV")
                    || category.code.startsWith("CV")
                    || category.code.startsWith("IV")
                    || category.code.startsWith("LB")
                    || category.code.startsWith("LV")
                    || category.code.startsWith("OV")) {
                    currentOption['category'] = category.category;
                    currentOption['code'] = "07";
                    currentOption['description'] = "Vacant";
                }
                self.currentUse = currentOption;
                self.operativeZoningDetails.doesCurrentUseComply = self.defaultDoesCurrentUseComply;
                self.operativeZoningDetails.subdivisionPotential = self.defaultSubdivisionPotential;
                self.notifyParent();
            },
            getZoningOptions: function (groupCode, filterId, storeKey) {
                const self = this;
                var criteria = {
                    "parentCode": self.parentCode,
                    "groupCode": groupCode,
                    "sort": "SHORT_DESCRIPTION",
                    "order": "ASC"
                };
                this.$nextTick(function () {
                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ReferenceData.searchClassifications().url,
                        cache: false,
                        contentType: "application/json",
                        data: JSON.stringify(criteria),
                        success: function (response) {
                            self.setupZoningDropdown(response, filterId, storeKey);
                            if (groupCode === 'TAZ') {
                                self.operativeZoningBuffer[self.parentCode] = response;
                            }
                            if (groupCode === 'TAPZ') {
                                self.proposedZoningBuffer[self.parentCode] = response;
                            }
                        },
                        error: function (response) {
                            self.errorHandler(response);
                        }
                    });
                });
            },
            setupZoningDropdown: function (response, filterId, storeKey) {
                const self = this;
                $('.advSearch-' + filterId + '-multiselect').multiselect('dataprovider', []);
                if (response.length) {
                    var classificationData = [];
                    var options = [];
                    $.each(response, function () {
                        options.push({
                            label: this.shortDescription,
                            value: this.code.trim(),
                            title: this.category
                        });
                        if (filterId === 'operativeZoning') {
                            self.operativeDescriptionMap[this.category] = this.description;
                        }
                        else if (filterId === 'proposedZoning') {
                            self.proposedDescriptionMap[this.category] = this.description;
                        }
                        classificationData.push(this);
                    });
                    //commit classification data to store
                    var classificationToStore = {};
                    classificationToStore.key = storeKey;
                    classificationToStore.data = classificationData;
                    store.commit("addClassification", classificationToStore);

                    $('.advSearch-' + filterId + '-multiselect').multiselect('dataprovider', options);

                    self.showZoningTemplate = !self.showZoningTemplate;
                    if (!self.showZoningTemplate) {
                        self.refreshZoningTemplate = true
                    }
                    $('.advSearch-' + filterId + '-multiselect').multiselect('refresh');
                }
            },
            setupZoningOptions: function () {
                const self = this;
                if (self.operativeZoningBuffer[self.parentCode]) {
                    self.setupZoningDropdown(self.operativeZoningBuffer[self.parentCode], 'operativeZoning', 'TAZoning');
                } else {
                    self.getZoningOptions('TAZ', 'operativeZoning', 'TAZoning');
                }

                if (self.proposedZoningBuffer[self.parentCode]) {
                    self.setupZoningDropdown(self.proposedZoningBuffer[self.parentCode], 'proposedZoning', 'TAPZoning');
                } else {
                    self.getZoningOptions('TAPZ', 'proposedZoning', 'TAPZoning');
                }
            },
            getZoningDescription: function (data, zoningType) {
                const self = this;
                var requestObj = {
                    "parentCategory": data.val.category,
                    "group_code": data.val.group_code,
                    "sort": "CODE"
                };
                this.$nextTick(function () {
                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ReferenceData.searchClassifications().url,
                        cache: false,
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify(requestObj),
                        success: function (response) {
                            self.populateZoningDescription(data, zoningType, response);
                            if (zoningType === "operativeZoning") {
                                self.operativeDetailsBuffer[data.val.category] = response;
                            } else if (zoningType === "proposedZoning") {
                                self.proposedDetailsBuffer[data.val.category] = response;
                            }
                        },
                        error: function (response) {
                            self.errorHandler(response);
                        }
                    });
                });
            },
            populateZoningDescription: function (data, zoningType, response) {
                const self = this;
                if ("operativeZoning" === zoningType) {
                    self.operativeZoningDetails.zoneDescription = "";
                    self.operativeZoningDetails.siteDensity = "";
                    if (data.val) {
                        $.each(response, function() {
                           if (this.category.indexOf("ZoneDescription_" + data.val.category) !== -1) {
                               self.operativeZoningDetails.zoneDescription = this.description
                           } else if (this.category == "Attributes_" + data.val.category) {
                               self.operativeZoningDetails.siteDensity = this.description;
                           }
                        });
                    }
                    self.notifyParent();
                    self.callback();
                }
                else if ("proposedZoning" === zoningType) {
                    if (data.val) {
                        self.proposedZoningDetails.zoneDescription = "";
                        self.proposedZoningDetails.siteDensity = "";
                        $.each(response, function() {
                            if (this.category.indexOf("ZoneDescription_" + data.val.category) !== -1) {
                                self.proposedZoningDetails.zoneDescription = this.description
                            } else if (this.category == "Attributes_" + data.val.category) {
                                self.proposedZoningDetails.siteDensity = this.description;
                            }
                        });
                    }
                    self.notifyParent();
                    self.callback();
                }
            },
            EmptyProposedZoning: function () {
                const self = this;
                if (!self.showProposedZoningTemplate) {
                    self.proposedZoningDetails = {};
                    self.notifyParent();
                    self.callback();
                }
            },
            isObjectEmpty: function (obj) {
                for (var key in obj) {
                    if (obj.hasOwnProperty(key) && obj[key] !== undefined && obj[key] !== null) {
                        return false;
                    }
                }
                return true;
            }
        },
        mounted: function () {
            const self = this;
            EventBus.$on('notify-multi-currentUse', function (data) {
                self.currentUse = data.val;
                self.notifyParent();
            });
            EventBus.$on('notify-simple-operative-zoning', function (data) {
                self.operativeZoningDetails[data.attrName] = data.val;
                self.notifyParent();
            });
            EventBus.$on('notify-multi-operative-zoning', function (data) {
                self.operativeZoningDetails[data.attrName] = data.val;
                if (data.attrName === "zoning") {
                    self.getZoningDescription(data, "operativeZoning");
                }
                self.notifyParent();
            });
            EventBus.$on('notify-simple-proposed-zoning', function (data) {
                self.proposedZoningDetails[data.attrName] = data.val;
                self.notifyParent();
            });
            EventBus.$on('notify-multi-proposed-zoning', function (data) {
                self.proposedZoningDetails[data.attrName] = data.val;
                if (data.attrName === "zoning") {
                    self.getZoningDescription(data, "proposedZoning");
                }
                self.notifyParent();
            });
        },
        created: function () {
            var self = this;
            EventBus.$on('home-valuation-saved', function (obj) {
                var loadingNewJob = self.homeValuationId != obj.homeValuation.id;
                if(loadingNewJob || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.homeValuationId = homeValuation.id;
                    self.currentUse = homeValuation.locationDetails ? (homeValuation.locationDetails.currentUse ? homeValuation.locationDetails.currentUse : {}) : {};
                    self.operativeZoningDetails = homeValuation.locationDetails ? (homeValuation.locationDetails.operativeZoningDetails ? homeValuation.locationDetails.operativeZoningDetails : {}) : {};
                    self.proposedZoningDetails = homeValuation.locationDetails ? (homeValuation.locationDetails.proposedZoningDetails ? homeValuation.locationDetails.proposedZoningDetails : {}) : {};
                    self.parentCode = homeValuation.propertySummary.territorialAuthority.code;
                    self.setupZoningOptions();

                    self.repopulateCurrentUseType(self.currentUse);
                    self.repopulateOperativeZoningDetails(self.operativeZoningDetails);
                    if (!self.isObjectEmpty(self.proposedZoningDetails)) {
                        self.showProposedZoningTemplate = true;
                        self.repopulateProposedZoningDetails(self.proposedZoningDetails);
                    }
                    self.showZoningTemplate = false;
                    if (!self.showZoningTemplate) {
                        self.refreshZoningTemplate = true;
                    }
                }
            });

            EventBus.$on('home-valuation-new', function (propertyData) {
                self.currentUse = {};
                self.operativeZoningDetails = {};
                self.proposedZoningDetails = {};
                self.parentCode = "";
                self.operativeDescriptionMap = {};
                self.proposedDescriptionMap = {};
                self.showProposedZoningTemplate = false;
                self.populateZoningData(propertyData);
                self.parentCode = propertyData.territorialAuthorityId;
                self.setupZoningOptions();
                self.showZoningTemplate = !self.showZoningTemplate;
                self.refreshZoningTemplate = true;
            });

            EventBus.$on('location-details-tabs', function (state) {
                self.tabState = state;
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.locationDetails.zoning;
                }
            });
        },
        updated: function () {
            const self = this;
            if (self.refreshZoningTemplate) {
                self.showZoningTemplate = true;
                self.refreshZoningTemplate = false;
            }
        },
        destroyed: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-multi-currentUse', this.listener);
            EventBus.$off('notify-simple-operative-zoning', this.listener);
            EventBus.$off('notify-simple-proposed-zoning', this.listener);
            EventBus.$off('notify-multi-operative-zoning', this.listener);
            EventBus.$off('notify-multi-proposed-zoning', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-multi-currentUse', this.listener);
            EventBus.$off('notify-simple-operative-zoning', this.listener);
            EventBus.$off('notify-simple-proposed-zoning', this.listener);
            EventBus.$off('notify-multi-operative-zoning', this.listener);
            EventBus.$off('notify-multi-proposed-zoning', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

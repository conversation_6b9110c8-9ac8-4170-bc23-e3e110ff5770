<template>
    <!-- LEGAL DECRIPTION STARTS -->
    <div class="QVHV-Container legaldescription active" v-bind:class="[tabState=='open' ? 'canOpener' : '']"
         v-if="showLegalDescriptionTemplate">
        <ul class="QVHV-tabs hide">
            <li><span class="is-active">Legal Description</span></li>
            <hr align="left"/>
        </ul>
        <div class="QVHV-formSection">
            <div class="advSearch-row">
                <text-input id="legalDetailsComputerRegistered" maxlength="1000"
                            :curr-val="legalDetails.computerRegister" attr-name="computerRegister" fieldType="text"
                            iconClass="advSearch-group twentyfivePct icons8-hashtag" label="Record of Title"
                            component-name="legal-description"
                            :class="[fields.computerRegister]"></text-input>
                <text-input id="legalDetailsLegalDescription" maxlength="1000" :curr-val="legalDetails.legalDescription"
                            attr-name="legalDescription" fieldType="text"
                            iconClass="advSearch-group seventyfivePct icons8-legal-description"
                            label="Legal Description"
                            component-name="legal-description"
                            :class="[fields.legalDescription]"></text-input>
            </div>
            <div class="advSearch-row">
                <text-input id="legalDetailsLandArea" maxlength="50" :curr-val="legalDetails.landArea"
                            attr-name="landArea" fieldType="number" decimal="4"
                            iconClass="advSearch-group twentyfivePct icons8-surface-filled" label="Land Area"
                            component-name="legal-description" :class="[fields.landArea]"></text-input>
                <valuation-multi-select-filter id="legalDetailsTenure" :curr-val="legalDetails.tenure"
                                               iconClass="advSearch-group twentyfivePct icons8-todo-list-filled"
                                               component-name="legal-description"
                                               attrName="tenure" filter-id="tenure"
                                               label="Tenure"
                                               selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="true"
                                               data-to-fetch="Tenure"
                                               :class="[fields.tenure]"></valuation-multi-select-filter>
                <text-input maxlength="500" id="legalDetailsOwner" :curr-val="legalDetails.owner" attr-name="owner"
                            fieldType="text"
                            iconClass="advSearch-group fiftyPct icons8-contacts" label="Proprietors"
                            component-name="legal-description"
                            :class="[fields.owner]"></text-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="4000" id="legalDetailsRegisteredInterestsComment"
                                 :curr-val="legalDetails.registeredInterestsComment"
                                 attr-name="registeredInterestsComment" fieldType="text"
                                 iconClass="advSearch-group hundyPct icons8-pencil"
                                 label="Comment on Registered Interests"
                                 component-name="legal-description"
                                 :class="[fields.registeredInterestsComment]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" id="legalDetailsTenureOrSaleability"
                                 :curr-val="legalDetails.tenureOrSaleabilityComment"
                                 attr-name="tenureOrSaleabilityComment"
                                 fieldType="text" iconClass="advSearch-group hundyPct icons8-pencil"
                                 label="Comment on Tenure or Saleability"
                                 component-name="legal-description"
                                 :class="[fields.tenureOrSaleabilityComment]"></text-area-input>
            </div>
        </div>
    </div>
</template>

<script>
    import TextInput from '../../filters/TextInput.vue';
    import TextAreaInput from '../../filters/TextAreaInput.vue';
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue';
    import {EventBus} from '../../../EventBus.js';
    import {store} from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            TextAreaInput,
            ValuationMultiSelectFilter
        },
        data: function () {
            return {
                legalDetails: {},
                showLegalDescriptionTemplate: true,
                refreshLegalDescriptionTemplate: false,
                tabState: 'closed',
                defaultPropertyData: {},
                homeValuationId: undefined,
                reportType: {},
                fields: {}
            }
        },
        methods: {
            notifyParent: function () {
                const self = this;
                EventBus.$emit('notify-location-parent', [{'key': 'legalDetails', 'value': self.legalDetails}])
            },
            populateLegalDetails: function (propertyData) {
                const self = this;
                self.legalDetails.computerRegister = propertyData.certificateOfTitle;
                self.legalDetails.legalDescription = propertyData.legalDescription;
                self.legalDetails.landArea = propertyData.landArea;
                self.legalDetails.owner = propertyData.occupiers.map(function(occ){
                    return occ.fullName.trim();
                }).join(', ');
                self.notifyParent();
            },
            repopulateLegalDetails: function (legalDetails) {
                const self = this;
                self.legalDetails.computerRegister = legalDetails.computerRegister;
                self.legalDetails.legalDescription = legalDetails.legalDescription;
                self.legalDetails.landArea = legalDetails.landArea;
                self.legalDetails.tenure = legalDetails.tenure;
                self.legalDetails.owner = legalDetails.owner;
                self.legalDetails.registeredInterestsComment = legalDetails.registeredInterestsComment;
                self.legalDetails.tenureOrSaleabilityComment = legalDetails.tenureOrSaleabilityComment;
            },
            isEmpty: function (legalDetails) {
                for (var key in legalDetails) {
                    if (legalDetails.hasOwnProperty(key)) {
                        return false;
                    }
                }
                return true;
            }
        },
        mounted: function () {
            const self = this;
            EventBus.$on('notify-simple-legal-description', function (data) {
                self.legalDetails[data.attrName] = data.val;
                self.notifyParent();
            });
            EventBus.$on('notify-multi-legal-description', function (data) {
                self.legalDetails[data.attrName] = data.val;
                self.notifyParent();
            });
        }
        ,
        created: function () {
            var self = this;
            EventBus.$on('home-valuation-saved', function (obj) {
                var loadingNewJob = self.homeValuationId != obj.homeValuation.id;
                if(loadingNewJob || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.homeValuationId = homeValuation.id;
                    self.legalDetails = homeValuation.locationDetails ? (homeValuation.locationDetails.legalDetails ? homeValuation.locationDetails.legalDetails : {}) : {};
                    if (self.isEmpty(self.legalDetails)) {
                        self.populateLegalDetails(self.defaultPropertyData);
                    } else {
                        self.repopulateLegalDetails(self.legalDetails);
                    }
                    self.showLegalDescriptionTemplate = false;
                    if (!self.showLegalDescriptionTemplate) {
                        self.refreshLegalDescriptionTemplate = true;
                    }
                }
            });

            EventBus.$on('home-valuation-new', function (propertyData) {
                self.legalDetails = {};
                self.populateLegalDetails(propertyData);
                self.defaultPropertyData = propertyData;
                self.showLegalDescriptionTemplate = !self.showLegalDescriptionTemplate;
                self.refreshLegalDescriptionTemplate = true;
            });

            EventBus.$on('location-details-tabs', function (state) {
                self.tabState = state;
            });

            EventBus.$on('load-default-property-data', function (propertyData) {
                self.defaultPropertyData = propertyData.property;
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.locationDetails.legalDescription;
                }
            });
        }
        ,
        updated: function () {
            const self = this;
            if (self.refreshLegalDescriptionTemplate) {
                self.showLegalDescriptionTemplate = true;
                self.refreshLegalDescriptionTemplate = false;
            }
        }
        ,
        destroyed: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-legal-description', this.listener);
            EventBus.$off('notify-multi-legal-description', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
        ,
        beforeDestroy: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-legal-description', this.listener);
            EventBus.$off('notify-multi-legal-description', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

<template>
    <!-- GENERAL PROPERTY INFORMATION STARTS -->
    <div class="QVHV-Container salehistory active" v-bind:class="[tabState=='open' ? 'canOpener' : '']"
         v-if="showSaleHistoryTemplate">
        <ul class="QVHV-tabs">
            <li><span class="is-active">Sale History</span></li>
            <hr align="left"/>
        </ul>
        <div class="QVHV-formSection">
            <div class="advSearch-row">
                <div v-for="record in saleSummaries" class="rowSpacer">
                    <div class="advSearch-group twentyfivePct icons8-calendar-filled" :class="[fields.saleDate]">
                        <label>Sale Date</label>
                        <span><input class="advSearch-text" :value='getFormattedDate(record.saleDate)'
                                     type="text" disabled="disabled"></span>
                        <div class="valMessage"></div>
                    </div>
                    <div class="advSearch-group twentyfivePct icons8-sell-property-filled" :class="[fields.grossSalePrice]">
                        <label>Sale Price</label>
                        <span><input class="advSearch-text calculated"
                                     :value='getFormattedDollarValue(record.grossSalePrice)'
                                     type="text" disabled="disabled"></span>
                        <div class="valMessage"></div>
                    </div>
                    <div class="advSearch-group twentyfivePct">
                        <input type="checkbox" :id='record.saleDate' class="includeSales-trigger"
                               v-model="record.selected">
                        <label :for='record.saleDate'><span>Include in Report</span></label>
                    </div>
                </div>

                <div>
                    <text-area-input maxlength="1000" :curr-val="saleComments"
                                     attr-name="saleComments" fieldType="text"
                                     iconClass="advSearch-group hundyPct icons8-pencil"
                                     label="Sales Comment"
                                     component-name="sale-comments"
                                     data-cy="saleComments"
                                     :class="[fields.saleComments]"></text-area-input>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {EventBus} from '../../../EventBus.js';
    import {store} from '../../../DataStore';
    import TextAreaInput from '../../filters/TextAreaInput.vue';
    import numeral from 'numeral';
    import moment from 'moment';

    export default {
        components: {
            TextAreaInput
        },
        data: function () {
            return {
                saleSummaries: [],
                saleComments: "",
                showSaleHistoryTemplate: true,
                refreshSaleHistoryTemplate: false,
                tabState: 'closed',
                defaultPropertyData: {},
                defaultSales: [],
                hasHistory: undefined,
                homeValuationId: undefined,
                reportType: {},
                fields: {}
            }
        },
        methods: {
            notifyParent: function () {
                const self = this;
                EventBus.$emit('notify-location-parent', [
                    {'key': 'saleSummaries', 'value': self.saleSummaries},
                    {'key': 'saleComments', 'value': self.saleComments}
                ]);
            },
            getFormattedDate: function (date) {
                if (date && date != '') {
                    return moment(String(date)).format('DD/MM/YYYY')
                }
                return ''
            },
            getFormattedDollarValue: function (val) {
                if (val) {
                    return numeral(val).format('$0,0');
                }
                return '';
            },
            displaySales: function (sales) {
                const self = this;
                if (sales && sales.length > 0) {
                    self.hasHistory = true;
                    for (var i = 0; i < sales.length; i++) {

                        var today = new Date();
                        var diff =(today.getTime() - (new Date(sales[i].saleDate).getTime())) / 1000;
                        diff /= (60 * 60 * 24);
                        var yearDiff =  Math.abs(Math.round(diff/365.25));

                        if (yearDiff <= 10) {
                            var saleSummary = {};
                            saleSummary.saleDate = sales[i].saleDate;
                            saleSummary.grossSalePrice = sales[i].price.gross;
                            saleSummary.selected = true;
                            self.saleSummaries[i] = saleSummary;
                        }
                    }
                    if(!self.saleSummaries.length) {
                        self.populateSaleComments("No sales recorded against this property.");
                    }
                } else {
                    self.hasHistory = false;
                    self.saleComments = "No sales recorded against this property.";
                }
                self.notifyParent();
            },
            repopulateSales: function (saleSummaries, saleComments) {
                const self = this;
                self.saleSummaries = saleSummaries;
                self.saleComments = saleComments;
            },
            populateSaleComments: function (saleComments) {
                const self = this;
                self.saleComments = saleComments;
            }
        },
        mounted: function () {
            const self = this;
            EventBus.$on('notify-simple-sale-comments', function (data) {
                self.saleComments = data.val;
                self.notifyParent();
            });
        },
        created: function () {
            var self = this;
            EventBus.$on('home-valuation-saved', function (obj) {
                var loadingNewJob = self.homeValuationId != obj.homeValuation.id;
                if(loadingNewJob || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.homeValuationId = homeValuation.id;
                    self.saleSummaries = homeValuation.locationDetails ? (homeValuation.locationDetails.saleSummaries ? homeValuation.locationDetails.saleSummaries : []) : [];
                    self.saleComments = homeValuation.locationDetails ? (homeValuation.locationDetails.saleComments ? homeValuation.locationDetails.saleComments : "") : "";
                    if (self.hasHistory && self.saleSummaries.length > 0) {
                        self.repopulateSales(self.saleSummaries, self.saleComments);
                    } else {
                        self.populateSaleComments(self.saleComments);
                    }
                    self.showSaleHistoryTemplate = false;
                    self.refreshSaleHistoryTemplate = true;
                }
            });

            EventBus.$on('home-valuation-new', function (data) {
                self.saleSummaries = [];
                self.saleComments = "";
                self.displaySales(data.sales);
                self.defaultPropertyData = data;
                self.showSaleHistoryTemplate = false;
                self.refreshSaleHistoryTemplate = true;
            });

            EventBus.$on('location-details-tabs', function (state) {
                self.tabState = state;
            });

            EventBus.$on('load-default-property-data', function (propertyData) {
                self.defaultPropertyData = propertyData.property;
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.locationDetails.saleHistory;
                }
            });
        },
        updated: function () {
            const self = this;
            if (self.saleSummaries) {
                self.notifyParent();
            }
            if (self.refreshSaleHistoryTemplate) {
                self.showSaleHistoryTemplate = true;
                self.refreshSaleHistoryTemplate = false;
            }
        },
        destroyed: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('notify-simple-sale-comments', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('notify-simple-sale-comments', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

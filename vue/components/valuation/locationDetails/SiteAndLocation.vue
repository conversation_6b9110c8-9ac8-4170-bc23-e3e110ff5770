<template>
    <!-- SITE AND LOCATION STARTS -->
    <div class="QVHV-Container siteandlocation active" v-bind:class="[tabState=='open' ? 'canOpener' : '']"
         v-if="showSiteAndLocationTemplate">
        <ul class="QVHV-tabs hide">
            <li><span class="is-active">Site and Location</span></li>
            <hr align="left"/>
        </ul>
        <div class="QVHV-formSection">
            <div class="advSearch-row">
                <valuation-multi-select-filter id="countourDropDown" :curr-val="siteDetails.contour"
                                               iconClass="advSearch-group twentyfivePct icons8-field-filled"
                                               component-name="site-and-location" attrName="contour" filter-id="contour"
                                               label="Contour" selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="false" multiple="true"
                                               data-to-fetch="Contour" :class="[fields.contour]"></valuation-multi-select-filter>
                <valuation-multi-select-filter id="shapeDropDown" :curr-val="siteDetails.shape"
                                               iconClass="advSearch-group twentyfivePct icons8-view-module-filled"
                                               component-name="site-and-location" attrName="shape" filter-id="shape"
                                               label="Shape" selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="true" multiple="true"
                                               data-to-fetch="Shape" :class="[fields.shape]"></valuation-multi-select-filter>
                <valuation-multi-select-filter id="accessDropDown" :curr-val="siteDetails.access"
                                               iconClass="advSearch-group twentyfivePct icons8-lot-position-filled"
                                               component-name="site-and-location" attrName="access" filter-id="access"
                                               label="Access" selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="false" multiple="true"
                                               data-to-fetch="Access" :class="[fields.access]"></valuation-multi-select-filter>
                <valuation-multi-select-filter id="outlookDropDown" :curr-val="siteDetails.outlook"
                                               iconClass="advSearch-group twentyfivePct icons8-panorama"
                                               component-name="site-and-location" attrName="outlook" filter-id="outlook"
                                               label="Outlook" selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="false" multiple="true"
                                               data-to-fetch="Views" :class="[fields.outlook]"></valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter id="siteAndLocationAspect" :curr-val="siteDetails.aspect"
                                               iconClass="advSearch-group twentyfivePct icons8-field-filled"
                                               component-name="site-and-location" attrName="aspect" filter-id="siteAndLocationAspect"
                                               label="Aspect" selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="true"
                                               data-to-fetch="Aspect" :class="[fields.aspect]"></valuation-multi-select-filter>
                <text-input :class="[fields.positionOfImprovementsOnSite]" maxlength="300" id="siteAndLocationPositionOfImprovementsOnSite" :curr-val="siteDetails.positionOfImprovementsOnSite" attr-name="positionOfImprovementsOnSite" fieldType="text" iconClass="seventyfivePct icons8-lot-position-filled" label="Position of improvements on site" component-name="site-and-location"></text-input>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter id="servicesDropDown" :curr-val="siteDetails.services"
                                               iconClass="advSearch-group hundyPct icons8-todo-list-filled"
                                               component-name="site-and-location" attrName="services"
                                               filter-id="services"
                                               label="Services" selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="true" multiple="true"
                                               data-to-fetch="Services" :class="[fields.services]"></valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" id="siteCommentsArea" :curr-val="siteDetails.siteComments"
                                 attr-name="siteComments" fieldType="text"
                                 iconClass="advSearch-group hundyPct icons8-pencil" label="Site Comments"
                                 component-name="site-and-location" :class="[fields.siteComments]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" id="featuresAndHazardsArea" :curr-val="siteDetails.featuresAndHazards"
                                 attr-name="featuresAndHazards"
                                 fieldType="text" iconClass="advSearch-group hundyPct icons8-floods-filled"
                                 label="Features and Hazards" component-name="site-and-location" :class="[fields.featuresAndHazards]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" id="environmentalConsiderationsArea"
                                 :curr-val="siteDetails.environmentalConsiderations"
                                 attr-name="environmentalConsiderations"
                                 fieldType="text" iconClass="advSearch-group hundyPct icons8-forest-filled"
                                 label="Environmental, Social and Governance ‘ESG’ considerations"
                                 component-name="site-and-location" :class="[fields.environmentalConsiderations]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <!--Suburb/Town-->
                <valuation-multi-select-filter use-short-description="true" id="suburbAndTownDropdown" :curr-val="siteDetails.suburbOrTown"
                                               :on-dropdown-hide="callback"
                                               iconClass="advSearch-group seventyFivePct icons8-land-use-new"
                                               component-name="site-and-location" attrName="suburbOrTown"
                                               filter-id="suburbOrTown" label="Suburb/Town"
                                               selectClass="advSearch-singleselect monarch-multiselect"
                                               chooseHere="true"
                                               data-to-fetch="SuburbOrTown" :class="[fields.suburbOrTown]"></valuation-multi-select-filter>
                <text-input maxlength="1000" id="proximityOfSuburbToCBDText" :curr-val="siteDetails.proximityToCbd"
                            attr-name="proximityToCbd"
                            fieldType="text" iconClass="advSearch-group twentyfivePct icons8-map-marker-filled"
                            label="Proximity of Suburb to CBD" component-name="site-and-location" :class="[fields.proximityToCbd]"></text-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" id="surroundingDevelopmentArea"
                                 :curr-val="siteDetails.surroundingDevelopment" attr-name="surroundingDevelopment"
                                 fieldType="text" iconClass="advSearch-group hundyPct icons8-small-business-filled"
                                 label="Surrounding Development" component-name="site-and-location" :class="[fields.surroundingDevelopment]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-area-input id="facilitiesArea" maxlength="1000" :curr-val="siteDetails.facilities"
                                 attr-name="facilities"
                                 fieldType="text" iconClass="advSearch-group hundyPct icons8-museum-filled"
                                 label="Facilities" component-name="site-and-location" :class="[fields.facilities]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" id="publicTransportArea" :curr-val="siteDetails.publicTransport"
                                 attr-name="publicTransport"
                                 fieldType="text" iconClass="advSearch-group hundyPct icons8-bus-filled"
                                 label="Public Transport" component-name="site-and-location" :class="[fields.publicTransport]"></text-area-input>
            </div>
            <div class="advSearch-row">
                <text-area-input maxlength="1000" id="localityFeaturesArea" :curr-val="siteDetails.localityFeatures"
                                 attr-name="localityFeatures"
                                 fieldType="text" iconClass="advSearch-group hundyPct icons8-playground-filled"
                                 label="Locality Features" component-name="site-and-location" :class="[fields.localityFeatures]"></text-area-input>
            </div>
        </div>
    </div>
</template>

<script>
    import TextInput from '../../filters/TextInput.vue';
    import TextAreaInput from '../../filters/TextAreaInput.vue';
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue';
    import {EventBus} from '../../../EventBus.js';
    import {store} from '../../../DataStore';
    import Vue from 'vue';
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        components: {
            TextInput,
            TextAreaInput,
            ValuationMultiSelectFilter
        },
        mixins: [commonUtils],
        data: function () {
            return {
                siteDetails: {},
                showSiteAndLocationTemplate: true,
                refreshSiteAndLocationTemplate: false,
                tabState: 'closed',
                defaultPropertyData: {},
                parentCode: "",
                dataToFetch: {},
                SuburbTownDetails: {},
                defaultEnvConsiderations: undefined,
                defaultFeaturesHazards: "N/A",
                homeValuationId: undefined,
                defaultSiteComments: "It would appear from our on-site investigation that the improvements lay within the title boundaries, however only a survey would be able to verify such. If any encroachments were to be noted by any survey report, this should be referred to us for a review of the valuation as deemed appropriate.",
                reportType: {},
                fields: {}
            }
        },
        methods: {
            callback: function (obj) {
                var self = this;
                self.showSiteAndLocationTemplate = false;
                self.refreshSiteAndLocationTemplate = true;
            },
            notifyParent: function () {
                const self = this;
                EventBus.$emit('notify-location-parent', [{'key': 'siteDetails', 'value': self.siteDetails}]);
            },
            populateDefaultSiteDetails: function (propertyData) {
                const self = this;
                if (!self.siteDetails.services) {
                    self.siteDetails["services"] = [];
                    $.each(['ELETY', 'SEWGE', 'TELNE', 'WATMS)'], function (index, item) {
                        var serv = self.getClassificationObject('Services', item);
                        if(serv) self.siteDetails.services.push(serv);
                    });
                    EventBus.$emit('notify-location-parent', [{'key': 'siteDetails', 'value': self.siteDetails}]);
                }
                if (!self.siteDetails.contour) {
                    self.siteDetails["contour"] = [];
                    self.siteDetails.contour = propertyData.massAppraisal.classifications.contour ? [propertyData.massAppraisal.classifications.contour] : [];
                    EventBus.$emit('notify-location-parent', [{'key': 'siteDetails', 'value': self.siteDetails}]);
                }
                if (!self.siteDetails.access) {
                    self.siteDetails["access"] = [];
                    var hasCarAccess = propertyData.massAppraisal.hasCarAccess;
                    var accessObj = {};
                    if (hasCarAccess) {
                        accessObj.code = 'DRISS';
                        accessObj.description = 'Drive on access';
                    } else {
                        accessObj.code = 'WALSS';
                        accessObj.description = 'Walk on access';
                    }
                    self.siteDetails.access = [accessObj];
                    EventBus.$emit('notify-location-parent', [{'key': 'siteDetails', 'value': self.siteDetails}]);
                }
                if (!self.siteDetails.outlook) {
                    self.siteDetails["outlook"] = [];
                    var viewOptions = $('.advSearch-outlook-multiselect option').toArray();
                    for (var i in viewOptions) {
                        if (propertyData.massAppraisal.classifications.view && propertyData.massAppraisal.classifications.view.description.toUpperCase() === viewOptions[i].label.toUpperCase()) {
                            self.siteDetails.outlook.push(
                                {
                                    category: viewOptions[i].title,
                                    code: viewOptions[i].value,
                                    description: viewOptions[i].label,
                                    isActive: true,
                                    parentClassification: null,
                                    shortDescription: null
                                }
                            );
                        }
                    }
                    EventBus.$emit('notify-location-parent', [{'key': 'siteDetails', 'value': self.siteDetails}]);
                }

                if (!self.siteDetails.siteComments) {
                    self.siteDetails.siteComments = self.defaultSiteComments;
                }

                if (!self.siteDetails.featuresAndHazards) {
                    self.siteDetails.featuresAndHazards = self.defaultFeaturesHazards;
                }

                if (!self.siteDetails.environmentalConsiderations) {
                    self.populateEnvConsiderations();
                }
                self.notifyParent();
            },
            populateEnvConsiderations: function () {
                const self = this;
                if (self.defaultEnvConsiderations) {
                    self.siteDetails.environmentalConsiderations = self.defaultEnvConsiderations;
                } else {
                    var environmentalConsiderations = self.$store.getters.getCategoryClassifications('EnvironmentalConsiderations');
                    if (environmentalConsiderations[0]) {
                        self.siteDetails.environmentalConsiderations = environmentalConsiderations[0].description;
                        self.defaultEnvConsiderations = environmentalConsiderations[0].description;
                        self.notifyParent();
                    }
                }
            },
            repopulateSiteDetails: function (siteDetails) {
                const self = this;
                self.siteDetails.contour = siteDetails.contour;
                self.siteDetails.shape = siteDetails.shape;
                self.siteDetails.access = siteDetails.access;
                self.siteDetails.outlook = siteDetails.outlook;
                self.siteDetails.services = siteDetails.services;
                self.siteDetails.siteComments = siteDetails.siteComments;
                self.siteDetails.featuresAndHazards = siteDetails.featuresAndHazards;
                self.siteDetails.environmentalConsiderations = siteDetails.environmentalConsiderations;
                self.siteDetails.suburbOrTown = siteDetails.suburbOrTown;
                self.siteDetails.proximityToCbd = siteDetails.proximityToCbd;
                self.siteDetails.surroundingDevelopment = siteDetails.surroundingDevelopment;
                self.siteDetails.facilities = siteDetails.facilities;
                self.siteDetails.publicTransport = siteDetails.publicTransport;
                self.siteDetails.localityFeatures = siteDetails.localityFeatures;
                self.siteDetails.aspect = siteDetails.aspect;
                self.siteDetails.positionOfImprovementsOnSite = siteDetails.positionOfImprovementsOnSite;
            },
            getSuburbTowOptions: function (address) {
                const self = this;
                if (self.dataToFetch[self.parentCode]) {
                    self.setupSuburbTown(self.dataToFetch[self.parentCode], address);
                } else {
                    var requestObj = {
                        "parentCode": self.parentCode,
                        "groupCode": "TAL",
                        "sort": "SHORT_DESCRIPTION",
                        "order": "ASC"
                    };
                    this.$nextTick(function () {
                        $.ajax({
                            type: "POST",
                            url: jsRoutes.controllers.ReferenceData.searchClassifications().url,
                            cache: false,
                            contentType: "application/json",
                            data: JSON.stringify(requestObj),
                            success: function (response) {
                                //commit classification data to store
                                var classificationToStore = {};
                                classificationToStore.key = "SuburbOrTown";
                                self.dataToFetch[self.parentCode] = response;
                                classificationToStore.data = response;
                                store.commit("addClassification", classificationToStore);
                                self.setupSuburbTown(response, address);
                            },
                            error: function (response) {
                                self.errorHandler(response);
                            }
                        });
                    });
                }
            },
            setupSuburbTown: function (response, address) {
                const self = this;
                // clear the market comment dropdown first
                $('.advSearch-suburbOrTown-multiselect').multiselect('dataprovider', []);

                var options = [];
                $.each(response, function () {
                    options.push({
                        label: this.shortDescription,
                        value: this.code.trim(),
                        title: this.category
                    });
                    if (!self.siteDetails.suburbOrTown) {
                        if (address.suburb && address.suburb.toUpperCase() == this.shortDescription.toUpperCase().trim()) {
                            self.siteDetails.suburbOrTown = {
                                "category": this.category,
                                "code": this.code.trim(),
                                "description": this.shortDescription,
                                "shortDescription": this.shortDescription
                            };
                            self.getSuburbTownDetails(this.category);
                        }
                    }
                });

                $('.advSearch-suburbOrTown-multiselect').multiselect('dataprovider', options);

                self.showSiteAndLocationTemplate = !self.showSiteAndLocationTemplate;
                if (!self.showSiteAndLocationTemplate) {
                    self.refreshSiteAndLocationTemplate = true;
                }
                $('.advSearch-suburbOrTown-multiselect').multiselect('refresh');
            },
            getSuburbTownDetails: function (parentCategory) {
                const self = this;
                var suburbDetails = self.SuburbTownDetails[parentCategory];
                if (suburbDetails) {
                    self.populateSuburbTownDetails(suburbDetails);
                } else {
                    var requestObj = {
                        "parentCategory": parentCategory,
                        "groupCode": "TAL",
                        "sort": "CODE"
                    };
                    this.$nextTick(function () {
                        $.ajax({
                            type: "POST",
                            url: jsRoutes.controllers.ReferenceData.searchClassifications().url,
                            cache: false,
                            contentType: "application/json; charset=utf-8",
                            data: JSON.stringify(requestObj),
                            success: function (response) {
                                self.SuburbTownDetails[parentCategory] = response;
                                self.populateSuburbTownDetails(response);
                            },
                            error: function (response) {
                                self.errorHandler(response);
                            }
                        });
                    });
                }
            },
            populateSuburbTownDetails: function (suburbDetails) {
                const self = this;
                var index = 0;

                if (suburbDetails[0].category.indexOf("ZoneDescription") !== -1) {
                    index = 1;
                }
                console.log(">>>>>>suburbDetails JSON>>>>>"+JSON.stringify(suburbDetails));
                if (suburbDetails.length > 0) {
                    self.siteDetails.proximityToCbd = suburbDetails[index+0].description;
                    if (suburbDetails[index+1]) {
                        self.siteDetails.surroundingDevelopment = suburbDetails[index+1].description;
                    } else {
                        self.siteDetails.surroundingDevelopment = "";
                    }
                    if (suburbDetails[index+2]) {
                        self.siteDetails.facilities = suburbDetails[index+2].description;
                    } else {
                        self.siteDetails.facilities = "";
                    }
                    if (suburbDetails[index+3]) {
                        self.siteDetails.publicTransport = suburbDetails[index+3].description;
                    } else {
                        self.siteDetails.publicTransport = "";

                    }
                    if (suburbDetails[index+4]) {
                        self.siteDetails.localityFeatures = suburbDetails[index+4].description;
                    } else {
                        self.siteDetails.localityFeatures = "";
                    }
                    self.notifyParent();
                }
                self.callback("");
            },
            isEmpty: function (siteDetails) {
                for (var key in siteDetails) {
                    if (siteDetails.hasOwnProperty(key)) {
                        return false;
                    }
                }
                return true;
            },
            updateSiteComments: function (comments) {
                const self = this;
                if (comments) {
                    self.siteDetails.siteComments = comments;
                }
                else {
                    if (self.reportType.code === 'VL') {
                        self.siteDetails.siteComments = '';
                    }
                    else {
                        self.siteDetails.siteComments = self.defaultSiteComments;
                    }
                }
                self.refreshView();
            },
            refreshView: function () {
                const self = this
                self.showSiteAndLocationTemplate = !self.showSiteAndLocationTemplate
                if (!self.showSiteAndLocationTemplate) {
                    self.refreshSiteAndLocationTemplate = true
                }
            }
        },
        mounted: function () {
            const self = this;
            EventBus.$on('notify-simple-site-and-location', function (data) {
                self.siteDetails[data.attrName] = data.val;
                self.notifyParent();
            });
            EventBus.$on('notify-multi-site-and-location', function (data) {
                self.siteDetails[data.attrName] = data.val;
                if (data.attrName == "suburbOrTown") {
                    self.getSuburbTownDetails(data.val.category);
                }
                self.notifyParent();
            });
        },
        created: function () {
            var self = this;
            EventBus.$on('home-valuation-saved', function (obj) {
                var loadingNewJob = self.homeValuationId != obj.homeValuation.id;
                if(loadingNewJob || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.homeValuationId = homeValuation.id;
                    self.siteDetails = homeValuation.locationDetails ? (homeValuation.locationDetails.siteDetails ? homeValuation.locationDetails.siteDetails : {}) : {};
                    self.parentCode = homeValuation.propertySummary.territorialAuthority.code;
                    self.getSuburbTowOptions(homeValuation.propertySummary.address);
                    if (self.isEmpty(self.siteDetails)) {
                        self.populateDefaultSiteDetails(self.defaultPropertyData);
                    } else {
                        self.repopulateSiteDetails(self.siteDetails);
                    }

                    self.showSiteAndLocationTemplate = false;
                    if (!self.showSiteAndLocationTemplate) {
                        self.refreshSiteAndLocationTemplate = true;
                    }
                }
            });

            EventBus.$on('home-valuation-new', function (propertyData) {
                self.siteDetails = {};
                self.defaultPropertyData = {};
                self.parentCode = "";
                self.populateDefaultSiteDetails(propertyData);
                self.parentCode = propertyData.territorialAuthorityId;
                self.getSuburbTowOptions(propertyData.propertySummary.address);
                self.defaultPropertyData = propertyData;
                self.showSiteAndLocationTemplate = !self.showSiteAndLocationTemplate;
                self.refreshSiteAndLocationTemplate = true;
            });

            EventBus.$on('location-details-tabs', function (state) {
                self.tabState = state;
            });

            EventBus.$on('load-default-property-data', function (propertyData) {
                self.defaultPropertyData = propertyData.property;
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode;
                self.updateSiteComments();
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.locationDetails.siteAndLocation;
                }
            });
        },
        updated: function () {
            const self = this;
            if (self.refreshSiteAndLocationTemplate) {
                self.showSiteAndLocationTemplate = true;
                self.refreshSiteAndLocationTemplate = false;
            }
        },
        destroyed: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-site-and-location', this.listener);
            EventBus.$off('notify-multi-site-and-location', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);

        },
        beforeDestroy: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-site-and-location', this.listener);
            EventBus.$off('notify-multi-site-and-location', this.listener);
            EventBus.$off('location-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div>
        <div class="md-table mdl-shadow--2dp">
            <h2>QA and Review</h2>
            <div data-cy="qaandreview-expand-all" class="expandAll" @click="expandTabs()" v-bind:class="[tabState == 'open' ? 'down' : '']">
                <span title="Expand Form" class="mdl-button mdl-js-button mdl-button--icon"><i class="material-icons md-dark"></i></span>
            </div>
            <ul id="tabsElementQAAndReview" v-bind:class="[tabState=='open' ? 'hide' : 'QVHV-tabs']">
                <li class="QVHVTab-1" @click="setCurrentTab('ReportAndCompliance', 1, true)" data-tab="QVHVTab-1" data-container="reportAndCompliance"><span v-bind:class="[currentTab == 'ReportAndCompliance' || tabState == 'open' ? 'is-active' : '']">Report & Compliance</span></li>
                <li class="QVHVTab-2" @click="setCurrentTab('PeerReview', 2, true)" data-tab="QVHVTab-2" data-container="peerReview"><span v-bind:class="[currentTab == 'PeerReview' || tabState == 'open' ? 'is-active' : '']">Countersigner Review</span></li>
                <hr align="left"/>
            </ul>

            <div class="QVHV-Container reportAndCompliance active" v-show="currentTab=='ReportAndCompliance' || tabState == 'open'" v-bind:class="[tabState=='open' ? 'canOpener' : '', readOnly ? 'disabled' : '']">
                <ul class="QVHV-tabs hide">
                    <li><span class="is-active">Report & Compliance</span></li>
                    <hr>
                </ul>
                <div class="QVHV-formSection">
                    <div class="advSearch-row">
                        <text-input :class="[fields.reportAndCompliance.valuationDate]" place-holder="DD/MM/YYYY" :curr-val="qaDetails.valuationDate" attr-name="valuationDate" fieldType="date" :format="validDateFormats" iconClass="twentyfivePct icons8-calendar-filled" label="Date of Valuation" component-name="qaDetails" v-if="showMultiSelects"></text-input>
                        <!--<text-input place-holder="DD/MM/YYYY" :curr-val="qaDetails.issueDate" attr-name="issueDate" fieldType="date" format="MM/DD/YYYY" iconClass="icons8-bed-filled" label="Date Report Issued" component-name="qaDetails" v-if="showMultiSelects"></text-input>
                        <text-input :curr-val="qaDetails.currentVersion" attr-name="currentVersion" fieldType="text" iconClass="icons8-pc-on-desk-filled" label="Version No." component-name="qaDetails" v-if="showMultiSelects"></text-input>-->
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                                :curr-val="qaDetails.valuationStandards"
                                iconClass="fiftyPct icons8-rating-filled"
                                component-name="qaDetails"
                                attrName="valuationStandards"
                                filter-id="valuationStandards"
                                label="Valuation Standards"
                                selectClass="monarch-multiselect qa-valuation-standards"
                                multiple="true"
                                requestType="POST"
                                data-to-fetch="ValuationStandards"
                                sort="SORT_ORDER"
                                v-if="showMultiSelects"
                                :class="[fields.reportAndCompliance.valuationStandards]">
                        </valuation-multi-select-filter>
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                                :curr-val="qaDetails.reportLogo"
                                iconClass="fiftyPct icons8-apple"
                                component-name="qaDetails"
                                attrName="reportLogo"
                                filter-id="reportLogo"
                                label="Logos"
                                selectClass="monarch-multiselect qa-report-logo"
                                chooseHere="true"
                                data-to-fetch="LogoforReport"
                                :class="[fields.reportAndCompliance.reportLogo]"
                                v-if="showMultiSelects">
                        </valuation-multi-select-filter>
                    </div>
                    <div class="advSearch-row">
                        <text-area-input :curr-val="qaDetails.complianceStatement" attr-name="complianceStatement" fieldType="text"
                                         iconClass="hundyPct icons8-pencil" label="Compliance Statement"
                                         component-name="qaDetails" v-if="showMultiSelects" :class="[fields.reportAndCompliance.complianceStatement]"></text-area-input>
                    </div>
                    <div class="advSearch-row">
                        <text-area-input :class="[fields.reportAndCompliance.documentsSighted]" :curr-val="qaDetails.documentsSighted" attr-name="documentsSighted" fieldType="text" iconClass="hundyPct icons8-pencil" label="Documents Sighted" component-name="qaDetails" v-if="showMultiSelects"></text-area-input>
                    </div>
                    <div class="advSearch-row">
                        <div class="advSearch-group fiftyPct icons8-news-filled" :class="[fields.reportAndCompliance.reportFileType]">
                            <label>Report File Type</label>
                            <fieldset role="radiogroup">
                                <input type="radio" name="" id="reportFile-pdf" value="PDF" v-model="qaDetails.reportFormat" v-if="(reportType && !(['IV', 'LDVA', 'LDVNHI'].includes(reportType.code)))" @click="saveQaPeerReview(false)">
                                <label for="reportFile-pdf" v-if="(reportType && !(['IV', 'LDVA', 'LDVNHI'].includes(reportType.code)))">PDF </label>
                                <input type="radio" name="" id="reportFile-word" value="DOCX" v-model="qaDetails.reportFormat" v-if="(reportType.code && !reportType.code.includes('VQV'))" @click="saveQaPeerReview(false)">
                                <label for="reportFile-word" v-if="(reportType.code && !reportType.code.includes('VQV'))">Word (.doc)</label>
                            </fieldset>
                        </div>
                        <div class="advSearch-group fiftyPct icons8-news-filled righty"  v-if="(reportType.code && !reportType.code.includes('VQV'))">
                            <!-- Radio button for selecting which home valuation report branding to use -->
                            <label>Report Branding</label>
                            <fieldset role="radiogroup">
                                <input type="radio" id="reportBrand-QV" value="QV" v-model="qaDetails.reportBranding" @change="valuationStandardsOnBranding()" checked>
                                <label for="reportBrand-QV">QV</label>
                                <input type="radio" id="reportBrand-Darroch" value="DARROCH" v-model="qaDetails.reportBranding" @change="valuationStandardsOnBranding()">
                                <label for="reportBrand-Darroch">Darroch</label>
                            </fieldset>
                        </div>
                    </div>
                </div>
            </div>

            <div class="QVHV-Container peerReview active" v-show="currentTab=='PeerReview' || tabState == 'open'" v-bind:class="[tabState=='open' ? 'canOpener' : '', readOnly ? 'disabled' : '']">
                <ul class="QVHV-tabs hide">
                    <li><span class="is-active">Countersigner Review</span></li>
                    <hr>
                </ul>
                <div class="QVHV-formSection">
                    <!-- REQUEST COUNTERSIGNER REVIEW STARTS -->
                    <div class="advSearch-row" v-if="homeValCopy == {} || ((homeValCopy.status && homeValCopy.status.code != 'P') && (!qaDetails.qaStatus || isEmptyObject(qaDetails.qaStatus) || !qaDetails.qaStatus.code || qaDetails.qaStatus.code == ''))">
                        <h2>Request Countersigner Review<span>Please provide any relevant notes or information to assist the countersigning valuer designated for this job.</span></h2>
                        <ul class="qvhvReport-files reviewRequest mdl-shadow~~2dp">
                            <li>
                                <input id="peerReview-1" type="checkbox" v-model="qaDetails.peerReviewNeeded" @click="saveQaPeerReview(false)">
                                <label for="peerReview-1"><h3>Countersigner Review Required</h3></label>
                                <div class="advSearch-row">
                                    <valuation-multi-select-filter
                                            :curr-val="qaDetails.peerReviewReason"
                                            iconClass="fiftyPct icons8-checked-user-male"
                                            component-name="qaDetails"
                                            attrName="peerReviewReason"
                                            filter-id="peerReviewReason"
                                            label="Reason for Countersigner Review"
                                            selectClass="monarch-multiselect-peer-review-reason"
                                            chooseHere="true"
                                            data-to-fetch="ReasonForPeerReview"
                                            v-if="showMultiSelects"
                                            :class="[fields.peerReview.peerReviewReason]">
                                    </valuation-multi-select-filter>
                                    <div>
                                        <div class="advSearch-group hundyPct icons8-pencil" :class="[fields.peerReview.peerReviewerNotes]">
                                            <label>Notes for Countersigning Valuer</label>
                                            <span><textarea class="advSearch-text" v-model="qaDetails.peerReviewerNotes" @change="saveQaPeerReview(false)"></textarea></span>
                                            <div class="valMessage"></div>
                                        </div>
                                    </div>
                                    <div class="QVHV-buttons">
                                        <div class="QVHV-buttons-right">
                                            <button class="primary" @click="sendToReview(false)" v-bind:class="[isDataSaving ? 'disabled' : '']">Send to Review</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="advSearch-row" v-if="homeValCopy != {} && homeValCopy.status && homeValCopy.status.code == 'P' && (!qaDetails.qaStatus || isEmptyObject(qaDetails.qaStatus) || !qaDetails.qaStatus.code || qaDetails.qaStatus.code == '')" v-bind:class="[(qaDetails.qaStatus && qaDetails.qaStatus.code && qaDetails.qaStatus.code == 'P') ? 'disabled' : '']">
                        <h2>Countersigner Review Requested<span>{{ homeValCopy.valuer.name + ' has requested you to countersign this report.' }}</span></h2>
                        <div class="peerReview-wrapper mdl-shadow--2dp">
                            <span class="reviewDate">{{ formattedReviewRequestedDate }}</span>

                            <div class="advSearch-row">
                                <h5>Reason for Countersigner Review</h5>
                                <p>{{ qaDetails.peerReviewReason ? qaDetails.peerReviewReason.description : '' }}</p>
                                <h5 v-if="qaDetails.peerReviewerNotes && qaDetails.peerReviewerNotes != ''">Notes for Countersigning Valuer</h5>
                                <p>{{ qaDetails.peerReviewerNotes }}</p>
                            </div>

                            <div class="advSearch-row">
                                <valuation-multi-select-filter
                                        v-bind:class="[(qaDetails.qaStatus && qaDetails.qaStatus.code && qaDetails.qaStatus.code == 'P') ? 'disabled' : '', fields.peerReview.seniorValuerInvolvement]"
                                        :curr-val="qaDetails.seniorValuerInvolvement"
                                        iconClass="fiftyPct icons8-checked-user-male"
                                        component-name="qaDetails"
                                        attrName="seniorValuerInvolvement"
                                        filter-id="seniorValuerInvolvement"
                                        label="Senior Valuer Involvement"
                                        selectClass="monarch-multiselect-qa-peer-review"
                                        chooseHere="true"
                                        data-to-fetch="SeniorValuerInvolvement"
                                        v-if="showMultiSelects">
                                </valuation-multi-select-filter>
                            <div class="advSearch-group fiftyPct icons8-checked-user-male righty">
                                <label>Type of Review</label>
                                    <fieldset role="radiogroup">
                                        <input type="radio" name="reviewTypeRadio" value="countersigner" v-model="peerReviewOrCountersigner" @change="updateReviewType()" checked>
                                        <label>Countersigned</label>
                                        <input type="radio" name="reviewTypeRadio" value="peerReview" v-model="peerReviewOrCountersigner" @change="updateReviewType()" >
                                        <label>Peer Reviewed</label>
                                    </fieldset>
                            </div>
                                <ul class="peerReview-form" v-bind:class="[(qaDetails.qaStatus && qaDetails.qaStatus.code && qaDetails.qaStatus.code == 'P') ? 'disabled' : '']">
                                    <li>
                                        <fieldset role="radiogroup">
                                            <input id="counterSign-1-NO" class="canOpener" name="counterSign-1" value="false" type="radio" v-model="qaDetails.valuedUsingAppropriateMethodology" @click="saveQaPeerReview(false)"/><label for="counterSign-1-NO">No</label>
                                            <input id="counterSign-1-YES" class="" name="counterSign-1" value="true" type="radio" v-model="qaDetails.valuedUsingAppropriateMethodology" @click="saveQaPeerReview(false)"/><label for="counterSign-1-YES">Yes</label>
                                            <h3>The property was valued using the appropriate methodology</h3>
                                            <div class="advSearch-row">
                                                <div>
                                                    <div class="advSearch-group hundyPct icons8-brake-warning-filled" :class="[fields.peerReview.valuedUsingAppropriateMethodologyNotes]">
                                                        <label>Reason for failure and suggestions for improvement</label>
                                                        <textarea class="advSearch-text" v-model="qaDetails.valuedUsingAppropriateMethodologyNotes" @change="saveQaPeerReview(false)"></textarea>
                                                        <div class="valMessage">&nbsp;</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </li>
                                    <li>
                                        <fieldset role="radiogroup">
                                            <input id="counterSign-2-NO" class="canOpener" name="counterSign-2" type="radio" value="false" v-model="qaDetails.appropriateSalesUsed" @click="saveQaPeerReview(false)"/> <label for="counterSign-2-NO">No</label>
                                            <input id="counterSign-2-YES" class="" name="counterSign-2" type="radio" value="true" v-model="qaDetails.appropriateSalesUsed" @click="saveQaPeerReview(false)"/> <label for="counterSign-2-YES">Yes</label>
                                            <h3>Relevant sales were used</h3>
                                            <div class="advSearch-row">
                                                <div>
                                                    <div class="advSearch-group hundyPct icons8-brake-warning-filled" :class="[fields.peerReview.appropriateSalesUsedNotes]">
                                                        <label>Reason for failure and suggestions for improvement</label>
                                                        <textarea class="advSearch-text" v-model="qaDetails.appropriateSalesUsedNotes" @change="saveQaPeerReview(false)"></textarea>
                                                        <div class="valMessage">&nbsp;</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </li>
                                    <li>
                                        <fieldset role="radiogroup">
                                            <input id="counterSign-3-NO" class="canOpener" name="counterSign-3" type="radio" value="false" v-model="qaDetails.appropriateValuesArrivedAt" @click="saveQaPeerReview(false)"/> <label for="counterSign-3-NO">No</label>
                                            <input id="counterSign-3-YES" class="" name="counterSign-3" type="radio" value="true" v-model="qaDetails.appropriateValuesArrivedAt" @click="saveQaPeerReview(false)"/> <label for="counterSign-3-YES">Yes</label>
                                            <h3>The values that were arrived at are appropriate</h3>
                                            <div class="advSearch-row">
                                                <div>
                                                    <div class="advSearch-group hundyPct icons8-brake-warning-filled" :class="[fields.peerReview.appropriateValuesArrivedAt]">
                                                        <label>Reason for failure and suggestions for improvement</label>
                                                        <textarea class="advSearch-text" v-model="qaDetails.appropriateValuesArrivedAtNotes" @change="saveQaPeerReview(false)"></textarea>
                                                        <div class="valMessage">&nbsp;</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </li>
                                    <li>
                                        <fieldset role="radiogroup">
                                            <input id="counterSign-4-NO" class="canOpener" name="counterSign-4" type="radio" value="false" v-model="qaDetails.appropriateDisclaimersUsed" @click="saveQaPeerReview(false)"/> <label for="counterSign-4-NO">No</label>
                                            <input id="counterSign-4-YES" class="" name="counterSign-4" type="radio" value="true" v-model="qaDetails.appropriateDisclaimersUsed" @click="saveQaPeerReview(false)"/> <label for="counterSign-4-YES">Yes</label>
                                            <h3>Applicable disclaimers were used throughout the report</h3>
                                            <div class="advSearch-row">
                                                <div>
                                                    <div class="advSearch-group hundyPct icons8-brake-warning-filled" :class="[fields.peerReview.appropriateDisclaimerUsedNotes]">
                                                        <label>Reason for failure and suggestions for improvement</label>
                                                        <textarea class="advSearch-text" v-model="qaDetails.appropriateDisclaimersUsedNotes" @change="saveQaPeerReview(false)"></textarea>
                                                        <div class="valMessage">&nbsp;</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </fieldset>
                                    </li>
                                </ul>
                                <div class="advSearch-row">
                                    <div class="QVHV-buttons">
                                        <div class="QVHV-buttons-right">
                                            <button class="secondary" @click="approvePeerReview('P')" v-bind:class="[approveClass || isDataSaving || (qaDetails.qaStatus && qaDetails.qaStatus.code && qaDetails.qaStatus.code == 'P') ? 'disabled' : '']">Approve</button>
                                            <button class="secondary" @click="approvePeerReview('F')" v-bind:class="[rejectClass || isDataSaving || (qaDetails.qaStatus && qaDetails.qaStatus.code && qaDetails.qaStatus.code == 'P')? 'disabled' : '']">Reject</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="advSearch-row" v-if="qaDetails.qaStatus && qaDetails.qaStatus.code && qaDetails.qaStatus.code == 'P'">
                        <h2>Countersigner Review Passed
                            <span>{{ homeValCopy.countersigner.name + ' passed this job on ' + formattedQaApprovalDate }}</span>
                        </h2>

                        <div class="peerReview-wrapper mdl-shadow--2dp">
                            <div class="advSearch-row">
                                <span class="reviewDate">{{ formattedQaApprovalDate }}</span>
                                <div class="advSearch-group fiftyPct icons8-checked-user-male righty">
                                    <label>Type of Review</label>
                                        <fieldset role="radiogroup">
                                            <input type="radio" name="reviewTypeRadio" value="countersigner" v-model="peerReviewOrCountersigner" @change="updateReviewType()" checked>
                                            <label>Countersigned</label>
                                            <input type="radio" name="reviewTypeRadio" value="peerReview" v-model="peerReviewOrCountersigner" @change="updateReviewType()">
                                            <label>Peer Reviewed</label>
                                        </fieldset>
                                </div>
                                <h5>Senior Valuer Involvement</h5>
                                <ul class="reviewValuer">
                                    <li><strong>{{ homeValCopy.countersigner.name }}</strong></li>
                                    <li class="reviewValuerDescription">- {{ qaDetails.seniorValuerInvolvement.description }}</li>
                                </ul>
                                <h5>Review Outcome</h5>
                                <ul class="reviewOutcome">
                                    <li class="pass">The property was valued using the appropriate methodology</li>
                                    <li class="pass">Relevant sales were used</li>
                                    <li class="pass">The values that were arrived at are appropriate</li>
                                    <li class="pass">Applicable disclaimers were used throughout the report</li>
                                </ul>
                            </div>
                            <div class="advSearch-row">
                                <span class="reviewDate">{{ formattedQaApprovalDate }}</span>
                                <h5>Reason for Countersigner Review</h5>
                                <p>{{ qaDetails.peerReviewReason ? qaDetails.peerReviewReason.description : '' }}</p>
                                <h5 v-if="qaDetails.peerReviewerNotes && qaDetails.peerReviewerNotes != ''">Notes for Countersigning Valuer</h5>
                                <p>{{ qaDetails.peerReviewerNotes }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="advSearch-row" v-if="qaDetails.qaStatus && qaDetails.qaStatus.code && qaDetails.qaStatus.code == 'F'">
                        <h2>Countersigner Review Failed
                            <span>{{ homeValCopy.countersigner.name + ' rejected this job on ' + formattedRejectedDate + '.' }}</span>
                            <span>Please review the information below and make any necessary changes to the report.</span>
                        </h2>

                        <ul class="qvhvReport-files reviewRequest reviewFailed">
                            <li>
                                <input id="peerReview-failed" type="checkbox" v-model="qaDetails.peerReviewNeeded">
                                <label for="peerReview-failed"><h3>Countersigner Review Required</h3></label>
                                <div class="advSearch-row">
                                    <valuation-multi-select-filter
                                            :curr-val="qaDetailsFailed.peerReviewReason"
                                            iconClass="fiftyPct icons8-checked-user-male"
                                            component-name="qaDetailsFailed"
                                            attrName="peerReviewReason"
                                            filter-id="peerReviewReason"
                                            label="Reason for Countersigner Review"
                                            selectClass="monarch-multiselect-failed-peer-review-reason"
                                            chooseHere="true"
                                            data-to-fetch="ReasonForPeerReview"
                                            v-if="showMultiSelects"
                                            :class="[fields.peerReview.peerReviewReason]">
                                    </valuation-multi-select-filter>
                                    <div>
                                        <div class="advSearch-group hundyPct icons8-pencil" :class="[fields.peerReview.peerReviewerNotes]">
                                            <label>Notes for Countersigning Valuer</label>
                                            <span><textarea class="advSearch-text" v-model="qaDetailsFailed.peerReviewerNotes"></textarea></span>
                                            <div class="valMessage"></div>
                                        </div>
                                    </div>
                                    <div class="QVHV-buttons">
                                        <div class="QVHV-buttons-right">
                                            <button class="primary" @click="sendToReview(true)" v-bind:class="[isDataSaving ? 'disabled' : '']">Send to Review</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <div class="peerReview-wrapper mdl-shadow--2dp">
                            <div class="advSearch-row">
                                <span class="reviewDate">{{ formattedRejectedDate }}</span>
                                <h5>Senior Valuer Involvement</h5>
                                <ul class="reviewValuer">
                                    <li><strong>{{ homeValCopy.valuer.name }}</strong>- {{ qaDetails.seniorValuerInvolvement.description }}</li>
                                </ul>
                                <h5>Review Outcome</h5>
                                <ul class="reviewOutcome">
                                    <li v-bind:class="[qaDetails.valuedUsingAppropriateMethodology == true || qaDetails.valuedUsingAppropriateMethodology == 'true' ? 'pass': 'fail']">The property was valued using the appropriate methodology<span>{{ qaDetails.valuedUsingAppropriateMethodologyNotes }}</span></li>
                                    <li v-bind:class="[qaDetails.appropriateSalesUsed == true || qaDetails.appropriateSalesUsed == 'true' ? 'pass': 'fail']">Relevant sales were used<span>{{ qaDetails.appropriateSalesUsedNotes }}</span></li>
                                    <li v-bind:class="[qaDetails.appropriateValuesArrivedAt == true || qaDetails.appropriateValuesArrivedAt == 'true' ? 'pass': 'fail']">The values that were arrived at are appropriate<span>{{ qaDetails.appropriateValuesArrivedAtNotes }}</span></li>
                                    <li v-bind:class="[qaDetails.appropriateDisclaimersUsed == true || qaDetails.appropriateDisclaimersUsed == 'true' ? 'pass': 'fail']">Applicable disclaimers were used throughout the report<span>{{ qaDetails.appropriateDisclaimersUsedNotes }}</span></li>
                                </ul>
                            </div>
                            <div class="advSearch-row">
                                <span class="reviewDate">{{ formattedReviewRequestedDate }}</span>
                                <h5>Reason for Countersigner Review</h5>
                                <p>{{ qaDetails.peerReviewReason ? qaDetails.peerReviewReason.description : '' }}</p>
                                <h5 v-if="qaDetails.peerReviewerNotes && qaDetails.peerReviewerNotes != ''">Notes for Countersigning Valuer</h5>
                                <p>{{ qaDetails.peerReviewerNotes }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <upload-report v-if="!isExternalUser && isAdminUser && homeValCopy && homeValCopy.propertySummary && homeValCopy.propertySummary.qupid"
                           :qpid="homeValCopy.propertySummary.qupid"
                           :report-type="homeValCopy.reportType.code"
                           :user-id="homeValCopy.websiteUserId"
            />
        </div>
        <div class="QVHV-buttons" v-bind:class="{disabled: readOnly}">
            <div class="QVHV-buttons-right" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="secondary" v-on:click="previousStep()">Back</button>
                <button class="primary createReport" v-bind:class="[(!homeValCopy || homeValCopy == {} || (homeValCopy && homeValCopy.id == '') || (homeValCopy.status && homeValCopy.status.code == 'S')) ? 'disabled' : '']" v-on:click="createReport()">Create Report</button>
            </div>
            <div class="QVHV-buttons-left" v-bind:class="[(isDataSaving || (!homeValCopy || homeValCopy == {} || (homeValCopy && homeValCopy.id == ''))) ? 'disabled' : '']">
                <button class="primary" v-on:click="saveQaPeerReview(true)">Save</button>
                <button class="primary" v-bind:class="[(!homeValCopy || homeValCopy == {} || (homeValCopy && homeValCopy.id == '') || (homeValCopy.status && homeValCopy.status.code == 'S')) ? 'disabled' : '']" v-on:click="completeJob()">Complete</button>
            </div>
        </div>
        <warning :header="warningHeader" :message="warningMessage" class="createReportError" close="Ok"></warning>
        <confirmation id="confirmationCompleteJob" okLabel="Yes" cancelLabel="No" :header="confirmationHeader" :message="confirmationMessage" :okHandler="okHandler"></confirmation>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { mapState } from 'vuex';
    import deepEqual from 'deep-equal';
    import ValuationMultiSelectFilter from '../filters/ValuationMultiSelectFilter.vue';
    import TextInput from '../filters/TextInput.vue';
    import TextAreaInput from '../filters/TextAreaInput.vue';
    import Warning from '../common/Warning.vue';
    import Confirmation from '../common/Confirmation.vue';
    import commonUtils from '../../utils/CommonUtils';
    import moment from 'moment';
    import UploadReport from "../qvCloudUploader/UploadReport.vue";

    export default {
        props: ['readOnly'],
        components: {
            ValuationMultiSelectFilter,
            TextInput,
            TextAreaInput,
            Warning,
            Confirmation,
            UploadReport
        },
        mixins: [commonUtils],
        data: function() {
            return {
                validDateFormats: ['DD/MM/YYYY', 'D/MM/YYYY', 'DD/M/YYYY', 'D/M/YYYY'],
                peerReviewOrCountersigner: "",
                qaDetails: {},
                qaDetailsFailed: {peerReviewReason: {}, peerReviewerNotes: ""},
                showMultiSelects: true,
                refreshMultiSelects: false,
                isDataSaving: false,
                persist: false,
                qvDefaultValuationStandardCodes: ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'P', 'V'],
                currentTab: 'ReportAndCompliance',
                tabState: 'closed',
                defaultComplianceStatement: '',
                homeValCopy: {},
                warningHeader: '',
                warningMessage: '',
                hasValidPhotos: true,
                hasPrimary: true,
                reportType: {},
                qaDetailsCopy: null,
                isDirty: false,
                confirmationHeader: '',
                confirmationMessage: '',
                okHandler: undefined,
                fields: {
                    reportAndCompliance: {},
                    peerReview: {}
                },
            }
        },
        computed: {
            ...mapState('userData', [
                'isExternalUser',
                'isAdminUser',
                'userFullName',
            ]),
            approveClass: function(){
                var self = this;
                var disabled = true;
                if((self.qaDetails || self.qaDetails != {}) && (self.qaDetails.seniorValuerInvolvement && self.qaDetails.seniorValuerInvolvement.code && self.qaDetails.seniorValuerInvolvement.code != "") &&
                        ((self.qaDetails.valuedUsingAppropriateMethodology == true || self.qaDetails.valuedUsingAppropriateMethodology == 'true') &&
                        (self.qaDetails.appropriateSalesUsed == true || self.qaDetails.appropriateSalesUsed == 'true') &&
                        (self.qaDetails.appropriateValuesArrivedAt == true || self.qaDetails.appropriateValuesArrivedAt == 'true') &&
                        (self.qaDetails.appropriateDisclaimersUsed == true || self.qaDetails.appropriateDisclaimersUsed == 'true'))) {
                    disabled = false;
                }
                return disabled;
            },
            rejectClass: function() {
                var self = this;
                var disabled = true;
                if((self.qaDetails || self.qaDetails != {}) && (self.qaDetails.seniorValuerInvolvement && self.qaDetails.seniorValuerInvolvement.code && self.qaDetails.seniorValuerInvolvement.code != "") &&
                        ((self.qaDetails.valuedUsingAppropriateMethodology != "" && (self.qaDetails.valuedUsingAppropriateMethodology == false || self.qaDetails.valuedUsingAppropriateMethodology == 'false')) ||
                        (self.qaDetails.appropriateSalesUsed != "" && (self.qaDetails.appropriateSalesUsed == false || self.qaDetails.appropriateSalesUsed == 'false')) ||
                        (self.qaDetails.appropriateValuesArrivedAt != "" && (self.qaDetails.appropriateValuesArrivedAt == false || self.qaDetails.appropriateValuesArrivedAt == 'false')) ||
                        (self.qaDetails.appropriateDisclaimersUsed != "" && (self.qaDetails.appropriateDisclaimersUsed == false || self.qaDetails.appropriateDisclaimersUsed == 'false')))) {
                    disabled = false;
                }
                return disabled;
            },
            formattedQaApprovalDate: function() {
                var self = this;
                var date = '';
                if (self.qaDetails.qaApprovalDate) {
                    date = moment(self.qaDetails.qaApprovalDate).format('DD/MM/YYYY');
                }
                return date;
            },
            formattedRejectedDate: function() {
                var self = this;
                var date = '';
                if (self.qaDetails.rejectedDate) {
                    date = moment(self.qaDetails.rejectedDate).format('DD/MM/YYYY');
                }
                return date;
            },
            formattedReviewRequestedDate: function() {
                var self = this;
                var date = '';
                if (self.qaDetails.reviewRequestedDate) {
                    date = moment(self.qaDetails.reviewRequestedDate).format('DD/MM/YYYY');
                }
                return date;
            }
        },
        methods: {

            setReviewType: function() {
                var self = this;
                if(self.qaDetails.hasBeenPeerReviewed){
                    this.peerReviewOrCountersigner = 'peerReview'
                }
                else {
                    this.peerReviewOrCountersigner = 'countersigner'
                }
            },

            updateReviewType: function() {
                var self = this;

                if(self.peerReviewOrCountersigner === "peerReview"){
                    self.qaDetails.hasBeenPeerReviewed = true;
                     self.qaDetails.hasBeenCountersigned = null;
                }
                if(self.peerReviewOrCountersigner === "countersigner"){
                    self.qaDetails.hasBeenCountersigned = true;
                    self.qaDetails.hasBeenPeerReviewed =null;
                }

                self.saveQaPeerReview(true);
            },
            isEmptyObject: function(obj) {
                var self = this;
                return $.isEmptyObject(obj);
            },
            expandTabs: function() {
                const self = this
                if (self.tabState == 'closed') {
                    self.tabState = 'open'
                }
                else {
                    self.tabState = 'closed'
                }
            },
            setCurrentTab: function(name, index, save){
                const self = this;
                self.currentTab = name;
                $('#tabsElementQAAndReview').find('hr')
                        .removeClass()
                        .addClass('QVHVTab-'+ index );
                if(save) {
                    setTimeout(function() {
                        self.saveQaPeerReview(save);
                    }, 1000);
                }
            },
            refreshView: function() {
                const self = this;
                self.showMultiSelects = !self.showMultiSelects;
                if (!self.showMultiSelects) {
                    self.refreshMultiSelects = true
                }
            },
            saveQaPeerReview: function(persist, status, callback){
                const self = this;
                var event = {};
                self.qaDetails.currentVersion = "V1";
                if (status == 'C') {
                    self.qaDetails.issueDate = moment();
                }

                event.qaDetails = JSON.parse(JSON.stringify(self.qaDetails));
                if (event.qaDetails.valuationDate) {
                    event.qaDetails.valuationDate = moment.utc(event.qaDetails.valuationDate, 'DD/MM/YYYY').toDate()
                }
                event.next = false;
                event.persist = persist;
                event.status = status;
                self.isDataSaving = persist;

                var haveChanges = !self.jsonEqual(self.qaDetails, self.qaDetailsCopy);
                event.isDirty = haveChanges || status != null;
                EventBus.$emit('home-valuation-qa-peer-review', event);
                if(callback) callback();
            },
            previousStep: function(){
                const self = this;
                self.saveQaPeerReview(true);
                EventBus.$emit("home-valuation-back", "photoAndAttachmentsStepper");
            },
            generateErrMsg: function(stepNumber, stepName, fieldName, errMsg){
                var msg = 'Step '+stepNumber+'('+stepName+') : \n\n'
                msg += ''+fieldName+' ' + errMsg + '\n\n'
                return msg
            },
            runAllValidations: function(title, completing){
                const self = this
                var homeValuation = self.homeValCopy
                var errorMsg = ''
                var stepsInError = []
                if (homeValuation) {
                    self.validateMedia();
                    var currentDate = moment();
                    if (homeValuation.valuationDueDate) {
                        /*if (currentDate.isAfter(homeValuation.valuationDueDate)) {
                            errorMsg += self.generateErrMsg(1, 'Job Setup', 'Valuation Due Date/Valuation Due Time', 'cannot be in the past.')
                            //'Valuation Due Date/Valuation Due Time cannot be in the past.\n';
                        }*/
                    }
                    if (!homeValuation.inspectionDate) {
                        errorMsg += self.generateErrMsg(1, 'Job Setup', 'Inspection Date/Inspection Time', 'cannot be empty.')
                        //'Inspection Date/Inspection Time cannot be empty.\n\n';
                    }
                    else if (homeValuation.inspectionDate) {
                        /*if (currentDate.isAfter(homeValuation.inspectionDate) && (homeValuation.status.code == "S" || homeValuation.status.code == "I")) {
                            errorMsg += self.generateErrMsg(1, 'Job Setup', 'Inspection Date/Inspection Time', 'cannot be in the past')
                            //'Inspection Date/Inspection Time cannot be in the past.\n';
                        }*/
                    }

                    // TODO: As required, comment out for the moment, dont delete
                    // if (homeValuation.valuationDueDate && homeValuation.inspectionDate) {
                    //     if (moment(homeValuation.inspectionDate).isAfter(moment(homeValuation.valuationDueDate))) {
                    //         errorMsg += self.generateErrMsg(1, 'Job Setup', 'Inspection Date/Inspection Time', 'cannot be after Valuation Due Date')
                    //     }
                    // }

                    // TODO: As required, comment out for the moment, dont delete
                    // if (homeValuation.valuationDueDate && self.qaDetails && self.qaDetails.valuationDate) {
                    //     var momQaValDate = moment(self.qaDetails.valuationDate, 'DD/MM/YYYY')
                    //     if (momQaValDate.isAfter(homeValuation.valuationDueDate)) {
                    //         errorMsg += self.generateErrMsg(8, 'QA & Review', 'Valuation Date', 'cannot be greater than the Valuation Due Date on Step 1:Job Setup')
                    //     }
                    // }

                    if (homeValuation.jobInstruction) {
                        if (!homeValuation.jobInstruction.clientName || homeValuation.jobInstruction.clientName === '') {
                            errorMsg += self.generateErrMsg(1, 'Job Setup', 'Client Name', 'cannot be empty.')
                            //'Client name cannot be empty.\n';
                        }
                    }

                    if (!homeValuation.valuer) {
                        errorMsg += self.generateErrMsg(1, 'Job Setup', 'Valuer', 'must be specified.');
                    }
                    else if (homeValuation.valuer.id == ''){
                        errorMsg += self.generateErrMsg(1, 'Job Setup', 'Valuer', 'must be specified.');
                    }

                    if (homeValuation.reportDetails && homeValuation.reportDetails.riskFactors) {
                        for (var i = 0; i < homeValuation.reportDetails.riskFactors.length; i++) {
                            if (homeValuation.reportDetails.riskFactors[i].score && homeValuation.reportDetails.riskFactors[i].score > 2) {
                                if (!homeValuation.reportDetails.riskFactors[i].headline || homeValuation.reportDetails.riskFactors[i].headline === '') {
                                    errorMsg += homeValuation.reportDetails.riskFactors[i].riskFactor.description + ' cannot be empty as it has a score of more than 2.\n'
                                }
                            }
                        }
                    }

                    if (homeValuation.extraPropertyDetails) {
                        if (homeValuation.extraPropertyDetails.rentalIncomeKnownDate) {
                            if (currentDate.isBefore(homeValuation.extraPropertyDetails.rentalIncomeKnownDate)) {
                                errorMsg += self.generateErrMsg(1, 'Job Setup', 'Rental Income Known', 'cannot be in the future.')
                            }
                        }
                    }

                    if(!self.hasValidPhotos) {
                        errorMsg += self.generateErrMsg(7, 'Photos & Attachments', 'Included Photos', 'cannot have empty tag and description.')
                    }

                    if(!self.hasPrimary) {
                        errorMsg += self.generateErrMsg(7, 'Photos & Attachments', 'Included Photos', 'should have a primary photo.')
                    }

                    if (completing 
                        && (homeValuation.worksheet.adoptedTotalImprovements + homeValuation.worksheet.adoptedLandValue) == 0 
                        && !['IV','LDVA','LDVNHI'].includes(homeValuation.reportType.code)
                    ) {
                        errorMsg += self.generateErrMsg(4, 'Valuation', 'Current Valuation - Adopted Values for Report - Market Value (ex Chattels)', 'cannot be $0.');
                    }
                }
                if (errorMsg && errorMsg != '') {
                    self.warningHeader = title
                    self.warningMessage = errorMsg
                    $('.createReportError').show()
                    return false;
                }
                else {
                    return true;
                }
            },
            createReport: function () {
                var self = this;
                $('.createReport').addClass('disabled');
                if (self.runAllValidations('Create Report Errors')) {
                    self.saveQaPeerReview(true, null, function() {
                        setTimeout(function() {
                            self.generateReport(true);
                        }, 1000);
                    });
                } else {
                    $('.createReport').removeClass('disabled');
                }
            },
            generateReport: function(showReport) {
                var self = this;
                var m = jsRoutes.controllers.ValuationReport.generateValuationReport(self.homeValCopy.id);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        if(showReport) window.open(response, self.homeValCopy.id);
                        $('.createReport').removeClass('disabled');
                    },
                    error: function (response) {
                        self.errorHandler(response);
                        self.warningHeader = "Create Report Errors";
                        self.warningMessage = "Error in creating the report.";
                        $('.createReportError').show();
                        $('.createReport').removeClass('disabled');
                    }
                });
            },
            valuationStandardsOnBranding: function(){
                const self = this;
                // TODO: This is not replicatable for other report types
                if(self.reportType.code !== 'KS'){
                    const valuationStandards = self.$store.getters.getCategoryClassifications('ValuationStandards') || [];
                    const newValuationStandardCodes = self.homeValCopy?.jobInstruction?.isSundryClient? ['A'].concat(self.qvDefaultValuationStandardCodes) : self.qvDefaultValuationStandardCodes;
                    const newValuationStandards = valuationStandards.filter((valuationStandard) => newValuationStandardCodes.includes(valuationStandard.code));
                    self.qaDetails.valuationStandards = [...newValuationStandards];
                }
                self.refreshView();
            },
            populateDefaultQADetails: function(homeValuation, fromSavedEvent){
                const self = this
                var modified = false
                if (!self.qaDetails.valuationDate) {
                    if (homeValuation && homeValuation.inspectionDate) {
                        self.qaDetails.valuationDate = moment(homeValuation.inspectionDate).format('DD/MM/YYYY')
                        modified = true
                    }
                }

                if (!self.qaDetails.complianceStatement) {
                    self.qaDetails.complianceStatement = self.defaultComplianceStatement
                    modified = true
                }


                if(!self.qaDetails.reportBranding){
                    self.qaDetails.reportBranding = 'QV';
                }

                if (!self.qaDetails.valuationStandards) {
                    // Previously default valuation standards were hard coded here....
                    const valuationStandards = self.$store.getters.getCategoryClassifications('ValuationStandards') || [];
                    const defaultValuationStandards = valuationStandards.filter((valuationStandard) => self.qvDefaultValuationStandardCodes.includes(valuationStandard.code));
                    self.qaDetails.valuationStandards = [...defaultValuationStandards];
                    modified = true;
                }

                if(!self.qaDetails.hasBeenCountersigned && !self.qaDetails.hasBeenPeerReviewed){
                    self.qaDetails.hasBeenCountersigned = true;
                    self.qaDetails.hasBeenPeerReviewed = null;
                }
                self.setReviewType();
                self.saveQaPeerReview(modified && fromSavedEvent);
            },
            populateDefaultComplianceStatement: function() {
                const self = this;
                var complianceStatement = self.$store.getters.getCategoryClassifications('ComplianceStatement');
                self.defaultComplianceStatement = complianceStatement && complianceStatement.length > 0 ? complianceStatement[0].description : '';
                self.qaDetails.complianceStatement = self.defaultComplianceStatement;
                self.refreshView();
            },
            assignQAStatus: function(code) {
                var self = this;
                var statuses = {};
                statuses['P'] = 'Passed';
                statuses['F'] = 'Failed';
                if(code != "") {
                    self.qaDetails.qaStatus = {
                        code: code,
                        description: statuses[code],
                        category: "QaStatus"
                    };
                }
            },
            sendToReview: function(isFailed) {
                var self = this;
                if ((!self.homeValCopy.valuer || (self.homeValCopy.valuer && (!self.homeValCopy.valuer.id || self.homeValCopy.valuer.id == ""))) && (!self.homeValCopy.countersigner || (self.homeValCopy.countersigner && (!self.homeValCopy.countersigner.id || self.homeValCopy.countersigner.id == "")))) {
                    self.warningHeader = 'Send to Review';
                    self.warningMessage = 'No Valuer and Countersigner assigned';
                    $('.createReportError').show()
                } else if (!self.homeValCopy.valuer || (self.homeValCopy.valuer && (!self.homeValCopy.valuer.id || self.homeValCopy.valuer.id == ""))) {
                    self.warningHeader = 'Send to Review';
                    self.warningMessage = 'No Valuer assigned';
                    $('.createReportError').show()
                } else if (!self.homeValCopy.countersigner || (self.homeValCopy.countersigner && (!self.homeValCopy.countersigner.id || self.homeValCopy.countersigner.id == ""))) {
                    self.warningHeader = 'Send to Review';
                    self.warningMessage = 'No Countersigner assigned';
                    $('.createReportError').show()
                } else if ((!isFailed && (!self.qaDetails.peerReviewReason || (self.qaDetails.peerReviewReason && self.qaDetails.peerReviewReason.code == ""))) ||
                        (isFailed && (!self.qaDetailsFailed.peerReviewReason || (self.qaDetailsFailed.peerReviewReason && self.qaDetailsFailed.peerReviewReason.code == "")))) {
                    self.warningHeader = 'Send to Review';
                    self.warningMessage = 'No Reason For Countersigner Review selected';
                    $('.createReportError').show()
                }
                else {
                    self.qaDetails.seniorValuerInvolvement = {};
                    self.qaDetails.valuedUsingAppropriateMethodology = "";
                    self.qaDetails.valuedUsingAppropriateMethodologyNotes = "";
                    self.qaDetails.appropriateSalesUsed = "";
                    self.qaDetails.appropriateSalesUsedNotes = "";
                    self.qaDetails.appropriateValuesArrivedAt = "";
                    self.qaDetails.appropriateValuesArrivedAtNotes = "";
                    self.qaDetails.appropriateDisclaimersUsed = "";
                    self.qaDetails.appropriateDisclaimersUsedNotes = "";
                    self.qaDetails.qaApprovalDate = "";
                    self.qaDetails.qaStatus = {};
                    self.qaDetails.reviewRequestedDate = new Date(Date.now());
                    self.qaDetails.reviewRequestedDate.setHours(13,0,0,0);
                    if(isFailed){
                        self.qaDetails.peerReviewReason = self.qaDetailsFailed.peerReviewReason;
                        self.qaDetails.peerReviewerNotes = self.qaDetailsFailed.peerReviewerNotes;
                    }
                    self.saveQaPeerReview(true, 'P');
                    self.sendEmail('SendForReview');

                }
            },
            sendEmail: function(reason) {
                var self = this;
                var reportUrl = "";
                if(reason == 'SendForReview') {
                    reportUrl = jsRoutes.controllers.EmailController.sendEmailForPeerReview(self.homeValCopy.id).url;
                }else if(reason == 'SendApproved') {
                    reportUrl = jsRoutes.controllers.EmailController.sendEmailOnReviewApproved(self.homeValCopy.id).url;
                }else if(reason == 'SendRejected') {
                    reportUrl = jsRoutes.controllers.EmailController.sendEmailOnReviewRejected(self.homeValCopy.id).url;
                }
                console.log("Sending email for " + reason);
                setTimeout(function(){
                    $.ajax({
                        type: "GET",
                        url: reportUrl,
                        cache: false,
                        success: function (response) {
                            console.log("Email sent successfully: " + response);
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            console.log("Send email failed: " + response);
                        }
                    });
                }, 3000);

            },
            approvePeerReview: function(code) {
                var self = this;
                self.assignQAStatus(code);
                if(code == 'P') {
                    self.qaDetails.rejectedDate = "";
                    self.qaDetails.qaApprovalDate = new Date(Date.now());
                    self.qaDetails.qaApprovalDate.setHours(13,0,0,0);
                    this.saveQaPeerReview(true, 'W');
                    self.sendEmail('SendApproved');
                } else {
                    self.qaDetails.qaApprovalDate = "";
                    self.qaDetails.rejectedDate = new Date(Date.now());
                    self.qaDetails.rejectedDate.setHours(13,0,0,0);
                    this.saveQaPeerReview(true, 'R');
                    self.sendEmail('SendRejected');
                }
            },
            completeJob: function() {
                var self = this;
                self.confirmationHeader = 'Complete Valuation Job';
                self.confirmationMessage = 'Do you want to complete the Job?';
                self.okHandler = function() {
                    if (self.runAllValidations('Complete Valuation Job', true)) {
                        self.saveQaPeerReview(true, 'C', function() {
                            setTimeout(function() {
                                self.generateReport(false);
                            }, 1000);
                        });
                    }
                }
                $('#confirmationCompleteJob').show();
            },
            validateMedia: function() {
                var self = this;
                $.ajax({
                    type: "GET",
                    cache: false,
                    dataType: 'json',
                    async: false,
                    url:  jsRoutes.controllers.MediaController.getMediaByOwner(self.homeValCopy.id).url,
                    error: function(response) {
                        self.hasValidPhotos = false;
                        self.hasPrimary = false;
                        self.errorHandler(response);
                    },
                    success: function(response) {
                        var hasPrimary = false;
                        var photos = 0;
                        self.hasValidPhotos = true;
                        self.hasPrimary = true;
                        $.each(response, function(i, media) {
                            if(media.category == 'HomeValuationPhoto') {
                                media.tags = $.map(media.tags, function(value, index) {
                                    return value.trim();
                                });
                                if(media.tags.indexOf('selected') > -1)  {
                                    photos++;
                                    if(media.mediaItem.tags.length == 0 && media.description.trim() == '') {
                                        self.hasValidPhotos = false;
                                    }
                                    if(media.isPrimary == true) {
                                        hasPrimary = true;
                                    }
                                }
                            }
                        });
                        if(!hasPrimary && photos.length > 0) {
                            self.hasPrimary = false;
                        }
                    }
                });
            },
            jsonEqual: function jsonEqual(a,b) {
                return deepEqual(a,b);
            }
        },
        mounted: function() {
            var self = this;

            EventBus.$on('notify-simple-qaDetails', function(data){
                self.qaDetails[data.attrName] = data.val;
                self.saveQaPeerReview(false);
            });

            EventBus.$on('notify-multi-qaDetails', function(data){
                if(data.attrName == 'reportLogo') {
                    var logoForReportList = self.$store.getters.getCategoryClassifications('LogoforReport');
                    var exists = logoForReportList.filter(function(e) { return e.code == data.val.code; });
                    if(exists.length > 0) {
                        self.qaDetails.reportLogo = exists[0];
                    }
                } else {
                    self.qaDetails[data.attrName] = data.val;
                }
                self.saveQaPeerReview(false);
            });

            EventBus.$on('notify-simple-qaDetailsFailed', function(data){
                self.qaDetailsFailed[data.attrName] = data.val;
            });

            EventBus.$on('notify-multi-qaDetailsFailed', function(data){
                self.qaDetailsFailed[data.attrName] = data.val;
            });

            EventBus.$on('notify-sundry-client', function(value){
                const valuationStandards = self.$store.getters.getCategoryClassifications('ValuationStandards') || [];
                const IVS101StandardCode = 'A'
                const IVS101Standard = valuationStandards.find((valuationStandard) => valuationStandard.code == IVS101StandardCode );
                if(self.qaDetails.hasOwnProperty('valuationStandards')) {
                    if(value && !self.qaDetails.valuationStandards.includes(IVS101Standard)) {
                        self.qaDetails.valuationStandards = [IVS101Standard, ...self.qaDetails.valuationStandards];
                        self.saveQaPeerReview(false);
                    }
                    if(!value) {
                        self.qaDetails.valuationStandards = self.qaDetails.valuationStandards.filter((valuationStandard) => valuationStandard.code !== IVS101StandardCode)
                        self.saveQaPeerReview(false);
                    }
                }
            });
        },
        created: function() {

            var self = this;
            EventBus.$on('enable-buttons', function(){
                self.isDataSaving = false
            })
            EventBus.$on('home-valuation-saved', function(obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 8 || (self.homeValCopy == {} || self.homeValCopy.id != obj.homeValuation.id) || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.homeValCopy = JSON.parse(JSON.stringify(homeValuation));
                    if (homeValuation.qaDetails) {
                        self.qaDetails = JSON.parse(JSON.stringify(homeValuation.qaDetails));
                        if (!self.qaDetails.qaStatus) {
                            self.qaDetails.qaStatus = {}
                            self.qaDetails.qaStatus.code = ''
                        }
                    }
                    else {
                        self.qaDetails = {}
                        self.qaDetails.qaStatus = {}
                        self.qaDetails.qaStatus.code = ''
                    }
                    if (self.qaDetails.valuationDate) {
                        self.qaDetails.valuationDate = moment.utc(self.qaDetails.valuationDate).format('DD/MM/YYYY')
                    }
                    if (!self.defaultComplianceStatement || self.defaultComplianceStatement === '') {
                        self.populateDefaultComplianceStatement();
                    }
                    self.populateDefaultQADetails(homeValuation,true);

                } else {
                    self.homeValCopy = JSON.parse(JSON.stringify(obj.homeValuation));
                    self.qaDetails = self.homeValCopy.qaDetails;
                    if (self.qaDetails.valuationDate) {
                        self.qaDetails.valuationDate = moment.utc(self.qaDetails.valuationDate).format('DD/MM/YYYY')
                    }
                }
                if(!self.qaDetails.reportFormat) {
                    self.qaDetails.reportFormat = "PDF";
                }
                self.qaDetailsCopy = JSON.parse(JSON.stringify(self.qaDetails));
                self.refreshView();
                self.isDataSaving = false;
            });

            EventBus.$on('home-valuation-new', function(homeValuation) {
                Object.assign(self.$data, self.$options.data.apply(self));
                self.qaDetails = {}
                self.qaDetails.qaStatus = {}
                self.qaDetails.qaStatus.code = ''

                self.qaDetails.reportLogo = {}
                if (!self.defaultComplianceStatement || self.defaultComplianceStatement === '') {
                    self.populateDefaultComplianceStatement();
                }
                self.populateDefaultQADetails(homeValuation,false);
                self.refreshView();
                self.setCurrentTab('ReportAndCompliance', 1, false);
            });

            EventBus.$on('home-valuation-loaded', function () {
                self.setCurrentTab('ReportAndCompliance', 1, false);
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.qaDetails.complianceStatement = self.defaultComplianceStatement;
                self.qaDetails.reportLogo = {};
                if(reportTypeCode) {
                    var logoForReportList = self.$store.getters.getCategoryClassifications('LogoforReport');
                    var exists = [];
                    if (reportTypeCode == "HB") {
                        exists = logoForReportList.filter(function(e) { return e.code == "1"; });
                    } else if (reportTypeCode == "HTTB" || reportTypeCode == "HA") {
                        exists = logoForReportList.filter(function(e) { return e.code == "2"; });
                    } else if (['IV', 'LDVA', 'LDVNHI'].includes(reportTypeCode)) {
                        self.qaDetails.reportFormat = "DOCX";
                    } else if (reportTypeCode.includes('VQV')){
                        self.qaDetails.reportFormat = "PDF";
                    }
                    // Set the ComplianceStatement using ReportTypeCode
                    var complianceStatements = self.$store.getters.getCategoryClassifications('ComplianceStatement');
                    if (complianceStatements && complianceStatements.length > 0){
                        // Filter the compliance statements where the Code matches ReportTypeCode
                        var complianceStatement = complianceStatements.filter(function(compStatement){
                            return compStatement.code === reportTypeCode;
                        });
                        self.qaDetails.complianceStatement = complianceStatement && complianceStatement.length > 0 ? complianceStatement[0].description : '';
                    }
                    if(exists.length > 0) {
                        self.qaDetails.reportLogo = exists[0];
                    }
                }
                self.saveQaPeerReview(false);
                self.reportType.code = reportTypeCode
                self.refreshView();
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.qaAndReview;
                }
            });

            //populates report type code when job is first loaded
            EventBus.$on('home-valuation-report-detail-initial-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode;
            });


        },
        updated: function() {
            const self = this;
            if (self.refreshMultiSelects) {
                self.showMultiSelects = true;
                self.refreshMultiSelects = false;
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
            EventBus.$off('notify-simple-qaDetails', this.listener);
            EventBus.$off('notify-multi-qaDetails', this.listener);
            EventBus.$off('notify-simple-qaDetailsFailed', this.listener);
            EventBus.$off('notify-multi-qaDetailsFailed', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
            EventBus.$off('notify-simple-qaDetails', this.listener);
            EventBus.$off('notify-multi-qaDetails', this.listener);
            EventBus.$off('notify-simple-qaDetailsFailed', this.listener);
            EventBus.$off('notify-multi-qaDetailsFailed', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

<template>
    <!-- VALUATION CONCLUSION STARTS -->
    <div class="QVHV-Container valuationconclusion active" v-bind:class="[tabState=='open' ? 'canOpener' : '']" v-if="showValuationConclusionTemplate">
        <ul class="QVHV-tabs hide">
            <li><span class="is-active">Valuation Conclusion</span></li>
            <hr align="left"/>
        </ul>
        <div class="QVHV-formSection">
            <div class="advSearch-row">
                <valuation-multi-select-filter use-short-description="true" :class="[fields.marketComment]" :curr-val="valuationConclusion.marketComment" :on-dropdown-hide="callback" iconClass="fiftyPct icons8-todo-list-filled" component-name="valuation-conclusion" attrName="marketComment" filter-id="marketComment" label="Market Comments" selectClass="monarch-multiselect marketComment" chooseHere="true" data-to-fetch="MarketCommentDS"></valuation-multi-select-filter>
                <text-area-input :class="[fields.marketCommentDescription]" maxlength="5000" :curr-val="valuationConclusion.marketCommentDescription" attr-name="marketCommentDescription" fieldType="" iconClass="hundyPct icons8-pencil" label="Market Comment Description" component-name="valuation-conclusion"></text-area-input>
            </div>

            <div class="advSearch-row">
                <text-area-input :class="[fields.propertyDescription]" maxlength="5000" :curr-val="valuationConclusion.propertyDescription" attr-name="propertyDescription" fieldType="" iconClass="hundyPct icons8-pencil" label="Property Description" component-name="valuation-conclusion" :refreshMethod="populateDefaultPropertyDescription"></text-area-input>
            </div>

            <div class="advSearch-row" v-for="va,key in valuationConclusion.valuationApproaches">
                <valuation-multi-select-filter
                        use-short-description="true"
                        :curr-val="va.valuationApproach"
                        :id="'valuationApproaches_'+(key+1)"
                        :obj-key="key+1"
                        :on-dropdown-hide="callback"
                        :data-index="key"
                        iconClass="fiftyPct icons8-todo-list-filled"
                        parentAttrName="valuationApproaches"
                        component-name="valuation-conclusion"
                        attrName="valuationApproach"
                        :filter-id="'valuationApproach'+(key+1)"
                        label="Valuation Approach"
                        :selectClass="'monarch-multiselect valuationApproach'+(key+1)"
                        chooseHere="true"
                        data-to-fetch="ValuationApproachDS"
                        :class="[fields.valuationApproach]">
                </valuation-multi-select-filter>
                <div class="advSearch-group sa-addRemove">
                    <i class="saRow-add material-icons" @click="addValuationApproach(key)"></i>
                    <i v-if="key>0" class="saRow-remove material-icons" @click="removeValuationApproach(key)"></i>
                </div>
                <text-area-input maxlength="5000"
                                 :obj-key="key+1"
                                 parentAttrName="valuationApproaches"
                                 :curr-val="va.valuationApproachDescription"
                                 attr-name="valuationApproachDescription"
                                 fieldType=""
                                 iconClass="hundyPct icons8-pencil"
                                 label="Valuation Approach Description"
                                 component-name="valuation-conclusion"
                                 :class="[fields.valuationApproachDescription]">
                </text-area-input>

                <text-area-input maxlength="10000"
                                 :obj-key="key+1"
                                 parentAttrName="valuationApproaches"
                                 :curr-val="va.valuationApproachConclusions"
                                 attr-name="valuationApproachConclusions"
                                 fieldType=""
                                 iconClass="hundyPct icons8-pencil"
                                 label="Valuation Conclusions"
                                 component-name="valuation-conclusion"
                                 :refreshMethod="populateDefaultValuationConclusionDescription"
                                 :class="[fields.valuationConclusions]">
                </text-area-input>
            </div>

            <div class="advSearch-row">
                <valuation-multi-select-filter use-short-description="true" :class="[fields.demandForSubjectProperty]" id="demandForSubjectProperty" :curr-val="valuationConclusion.demandForSubjectProperty" :on-dropdown-hide="callback" iconClass="fiftyPct icons8-todo-list-filled" parentAttrName="demandForSubjectProperty" component-name="valuation-conclusion" attrName="demandForSubjectProperty" filter-id="demandForSubjectProperty" label="Demand for Subject Property" selectClass="monarch-multiselect demandForSubjectProperty" multiple="true" data-to-fetch="DemandForSubjectPropertyDS"></valuation-multi-select-filter>
                <text-area-input :class="[fields.demandForSubjectPropertyDescription]" maxlength="1000" id="demandForSubjectPropertyDesccription" :curr-val="valuationConclusion.demandForSubjectPropertyDescription" attr-name="demandForSubjectPropertyDescription" fieldType="" iconClass="hundyPct icons8-pencil" label="Demand for Subject Property Description" component-name="valuation-conclusion"></text-area-input>
            </div>

            <div class="advSearch-row">
                <valuation-multi-select-filter use-short-description="true" :class="[fields.basisOfValue]" id="basisOfValue" :curr-val="valuationConclusion.basisOfValue" :on-dropdown-hide="callback" iconClass="fiftyPct icons8-todo-list-filled" parentAttrName="basisOfValue" component-name="valuation-conclusion" attrName="basisOfValue" filter-id="basisOfValue" label="Basis of Value" selectClass="monarch-multiselect basisOfValue" chooseHere="true" data-to-fetch="BasisOfValuationDS"></valuation-multi-select-filter>
                <text-area-input :class="[fields.basisOfValueDescription]" maxlength="1000" id="basisOfValueDescription" :curr-val="valuationConclusion.basisOfValueDescription" attr-name="basisOfValueDescription" fieldType="" iconClass="hundyPct icons8-pencil" label="Basis of Value Description" component-name="valuation-conclusion"></text-area-input>
            </div>

            <div class="advSearch-row">
                <valuation-multi-select-filter use-short-description="true" :class="[fields.specialConditions]" id="specialConditions" :curr-val="valuationConclusion.specialConditions" :on-dropdown-hide="callback" iconClass="fiftyPct icons8-todo-list-filled" parentAttrName="specialConditions" component-name="valuation-conclusion" attrName="specialConditions" filter-id="specialConditions" label="Special Conditions" selectClass="monarch-multiselect specialConditions" multiple="true" data-to-fetch="SpecialConditionsDS"></valuation-multi-select-filter>
                <text-area-input :class="[fields.specialConditionsDescription]" maxlength="5000" id="specialConditionsDescription" :curr-val="valuationConclusion.specialConditionsDescription" attr-name="specialConditionsDescription" fieldType="" iconClass="hundyPct icons8-pencil" label="Special Conditions Description" component-name="valuation-conclusion"></text-area-input>

            </div>

            <div class="advSearch-row">
                <valuation-multi-select-filter use-short-description="true" :class="[fields.additionalComments]" id="additionalComments" :curr-val="valuationConclusion.additionalComments" :on-dropdown-hide="callback" iconClass="fiftyPct icons8-todo-list-filled" parentAttrName="additionalComments" component-name="valuation-conclusion" attrName="additionalComments" filter-id="additionalComments" label="Additional Comments" selectClass="monarch-multiselect additionalComments" multiple="true" data-to-fetch="AdditionalCommentsDS"></valuation-multi-select-filter>
                <text-area-input :class="[fields.additionalCommentsDescription]" maxlength="5000" id="additionalCommentsDescription" :curr-val="valuationConclusion.additionalCommentsDescription" attr-name="additionalCommentsDescription" fieldType="" iconClass="hundyPct icons8-pencil" label="Additional Comments Description" component-name="valuation-conclusion"></text-area-input>
            </div>

            <div class="advSearch-row">

                <text-input :class="[fields.marketRental]" id="marketRental" :curr-val="valuationConclusion.marketRental" attr-name="marketRental" fieldType="number" iconClass="twentyfivePct icons8-sell-property-filled" label="Market Rental" component-name="valuation-conclusion"></text-input>
                <text-input :class="[fields.minimumWeeklyRental]" id="minimumWeeklyRental" :curr-val="valuationConclusion.minimumWeeklyRental" attr-name="minimumWeeklyRental" fieldType="number" iconClass="twentyfivePct icons8-low-price-filled" label="Minimum Weekly Rental" component-name="valuation-conclusion"></text-input>
                <text-input :class="[fields.maximumWeeklyRental]" id="maximumWeeklyRental" :curr-val="valuationConclusion.maximumWeeklyRental" attr-name="maximumWeeklyRental" fieldType="number" iconClass="twentyfivePct icons8-high-price-filled" label="Maximum Weekly Rental" component-name="valuation-conclusion"></text-input>
                <text-input :class="[fields.propertyOutgoings]" id="propertyOutgoing" :curr-val="valuationConclusion.propertyOutgoings" attr-name="propertyOutgoings" fieldType="number" iconClass="twentyfivePct icons8-sell-property-filled" label="Property Outgoings" component-name="valuation-conclusion"></text-input>

            </div>

            <div class="advSearch-row">
                <text-area-input :class="[fields.cashflowSustainability]" id="cashflowSustainability" maxlength="1000" :curr-val="valuationConclusion.cashflowSustainability" attr-name="cashflowSustainability" fieldType="" iconClass="hundyPct icons8-sell-property-filled" label="Cashflow Sustainability" component-name="valuation-conclusion"></text-area-input>
            </div>
        </div>
    </div>
</template>

<script>
    import TextInput from '../../filters/TextInput.vue'
    import TextAreaInput from '../../filters/TextAreaInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import { EventBus } from '../../../EventBus.js';
    import { store } from '../../../DataStore';
    import Vue from 'vue';
    import commonUtils from '../../../utils/CommonUtils';


    export default {
        components: {
            TextInput,
            TextAreaInput,
            ValuationMultiSelectFilter
        },
        mixins: [commonUtils],
        data: function() {
            return {
                valuationConclusion:{},
                defaultPropertyData: undefined,
                showValuationConclusionTemplate: true,
                refreshValuationConclusionTemplate : false,
                tabState: 'closed',
                defaultValuationApproachCode: "ComparableTransactionsMethod",
                defaultBasisOfValueCode: "1",
                defaultDemandSubjectPropertyCode: "F",
                defaultApproachDesc: "Where reliable, verifiable and relevant information is available the market approach is the preferred valuation approach and is appropriate for this valuation. This provides an indication of value by comparing the property being valued with other comparable/similar properties. Within this approach the method we have adopted is the comparable transactions method. In this method we derive a value by identifying and directly comparing relevant transactions to the property being valued. Allowances have then been made for differences such as location, dwelling type, size, condition and quality of accommodation, other buildings and improvements, site size and attributes, views, zoning and other features or issues as deemed appropriate.",
                defaultDemandSubjectPropertyDesc: "We consider the subject property to have a typical level of saleability in the current market.",
                homeValuationId: undefined,
                reportType: {},
                fields: {}
            }
        },
        methods: {
            callback: function(obj) {
                var self = this;
                if (obj.attrName == "marketComment") {
                    var event = {};
                    event.attrNameDesc = "marketCommentDescription";
                    event.data = obj;
                    self.setDescriptions(event);
                }

                if (obj.attrName == "specialConditions") {
                    var event = {};
                    event.attrNameDesc = "specialConditionsDescription";
                    event.data = obj;
                    self.setDescriptions(event);
                }

                if (obj.attrName == "additionalComments") {
                    var event = {};
                    event.attrNameDesc = "additionalCommentsDescription";
                    event.data = obj;
                    self.setDescriptions(event);
                }

                if (obj.attrName == "valuationApproach") {
                    var event = {};
                    event.attrNameDesc = "valuationApproachDescription";
                    event.index = obj.objKey-1;
                    event.data = obj;
                    self.setDescriptions(event);
                }

                if(obj.attrName == "demandForSubjectProperty") {
                    var event = {};
                    event.attrNameDesc = "demandForSubjectPropertyDescription";
                    event.data = obj;
                    self.setDescriptions(event);
                }

                if(obj.attrName == "basisOfValue") {
                    var event = {};
                    event.attrNameDesc = "basisOfValueDescription";
                    event.data = obj;
                    self.setDescriptions(event);
                }

                self.showValuationConclusionTemplate = false;
                self.refreshValuationConclusionTemplate = true;
            },
            notifyParent: function(data){
                EventBus.$emit('notify-report-parent',
                        {'key':data.attrName, 'value':data.val}
                )
            },
            resetDefaultValuationApproach: function(reportTypeCode){
                const self = this;
                if (typeof self.valuationApproachSelected === undefined || self.valuationApproachSelected === false){
                    const defaultTypes = ["HB", "HA", "HTTB", "KS", "KSA", "MVR", "PWNB", "VL"];
                    if(reportTypeCode && defaultTypes.includes(reportTypeCode)){
                        self.defaultValuationApproachCode = "ComparableTransactionsMethod";
                    }else{
                        self.defaultValuationApproachCode = "Directcomparisonapproach";
                    }
                    if(self.valuationConclusion.valuationApproaches.length == 1){
                        self.removeValuationApproach(0);
                    }
                    self.addValuationApproach(0);
                }
            },
            initiateSetupMultiSelect: function() {
                const self = this;
                var data = {};
                var criteria = {};

                criteria.category = "MarketCommentsList";
                criteria.sort = "SHORT_DESCRIPTION";
                criteria.order = "ASC";
                data.criteria = criteria;
                data.filterId = "marketComment";
                data.storeKey = "MarketCommentDS";
                self.setupMultiSelect(data);

                data = {};
                criteria = {};
                criteria.category = "ValuationApproaches";
                criteria.sort = "SHORT_DESCRIPTION";
                criteria.order = "ASC";
                data.criteria = criteria;
                data.filterId = "valuationApproach";
                data.storeKey = "ValuationApproachDS";
                self.setupMultiSelect(data);

                data = {};
                criteria = {};
                criteria.category = "SpecialConditions"
                criteria.sort = "SHORT_DESCRIPTION";
                criteria.order = "ASC";
                data.criteria = criteria;
                data.filterId = "specialConditions";
                data.storeKey = "SpecialConditionsDS";
                self.setupMultiSelect(data);

                data = {};
                criteria = {};
                criteria.category = "AdditionalComments";
                criteria.sort = "SHORT_DESCRIPTION";
                criteria.order = "ASC";
                data.criteria = criteria;
                data.filterId = "additionalComments";
                data.storeKey = "AdditionalCommentsDS";
                self.setupMultiSelect(data);

                data = {};
                criteria = {};
                criteria.category = "BasisofValuation";
                criteria.sort = "SHORT_DESCRIPTION";
                criteria.order = "ASC";
                data.criteria = criteria;
                data.filterId = "basisOfValue";
                data.storeKey = "BasisOfValuationDS";
                self.setupMultiSelect(data);

                data = {};
                criteria = {};
                criteria.category = "DemandforSubjectProperty";
                criteria.sort = "SHORT_DESCRIPTION";
                criteria.order = "ASC";
                data.criteria = criteria;
                data.filterId = "demandForSubjectProperty";
                data.storeKey = "DemandForSubjectPropertyDS";
                self.setupMultiSelect(data);
            },
            setupMultiSelect: function(data) {
                const self = this;

                $('.advSearch-'+data.filterId+'-multiselect').multiselect('dataprovider', []);

                var optionsForClassification = self.$store.getters.getCategoryClassifications(data.storeKey);
                if (optionsForClassification && optionsForClassification.length > 0) {
                    var options = [];
                    if (data.filterId == "marketComment") {
                        options.push({label: ' ', value: '', title: ''});
                    }

                    $.each(optionsForClassification, function() {
                        options.push({label: this.shortDescription, value: this.code.trim(), title: this.category});
                    });

                    $('.advSearch-'+data.filterId+'-multiselect').multiselect('dataprovider', options);

                    self.populateDefaultDropdown(data.filterId);

                    self.showValuationConclusionTemplate = !self.showValuationConclusionTemplate
                    if (!self.showValuationConclusionTemplate) {
                        self.refreshValuationConclusionTemplate = true
                    }
                    $('.advSearch-'+data.filterId+'-multiselect').multiselect('refresh');

                } else {
                    var jsRoute = jsRoutes.controllers.ReferenceData.searchClassifications();
                    this.$nextTick(function () {
                        $.ajax({
                            type: "POST",
                            url: jsRoute.url,
                            cache: false,
                            contentType: "application/json; charset=utf-8",
                            data: JSON.stringify(data.criteria),
                            dataType: "json",
                            success: function (response) {
                                var classificationData = [];
                                var options = [];

                                if (data.filterId == "marketComment") {
                                    options.push({label: ' ', value: '', title: ''});
                                }

                                $.each(response, function () {
                                    options.push({
                                        label: this.shortDescription,
                                        value: this.code.trim(),
                                        title: this.category
                                    });
                                    classificationData.push(this);
                                });

                                //commit classification data to store
                                var classificationToStore = {};
                                classificationToStore.key = data.storeKey;
                                classificationToStore.data = classificationData;
                                store.commit("addClassification", classificationToStore);

                                $('.advSearch-' + data.filterId + '-multiselect').multiselect('dataprovider', options);

                                self.showValuationConclusionTemplate = !self.showValuationConclusionTemplate
                                if (!self.showValuationConclusionTemplate) {
                                    self.refreshValuationConclusionTemplate = true
                                }
                                $('.advSearch-' + data.filterId + '-multiselect').multiselect('refresh');
                            },
                            error: function (response) {
                                self.errorHandler(response);
                            },
                            complete: function () {
                                self.populateDefaultDropdown(data.filterId);
                            }
                        });
                    });
                }
            },
            setDescriptions: function(event) {
                const self = this;
                if(event.data.attrName == "valuationApproach") {
                    self.valuationApproachSelected = true;
                    self.valuationConclusion.valuationApproaches[event.index][event.attrNameDesc] = "";
                } else {
                    self.valuationConclusion[event.attrNameDesc] = "";
                }

                var sortedVal = event.data.val.sort(function(a, b) {
                    return a.category > b.category;
                });

                sortedVal.forEach(function(mainVal) {
                    if (mainVal.code) {

                        var jsRoute = jsRoutes.controllers.ReferenceData.searchClassifications();

                        //get the long description
                        var criteria = {};
                        criteria.category = mainVal.category;
                        criteria.code = mainVal.code;
                        criteria.sort = "CODE"
                        criteria.order = "ASC"

                        $.ajax({
                            type: "POST",
                            url: jsRoute.url,
                            async: false,
                            cache: false,
                            contentType: "application/json; charset=utf-8",
                            data: JSON.stringify(criteria),
                            dataType: "json",
                            success: function (response) {
                                var index = 0
                                $.each(response, function() {
                                    var val = response[index];
                                    if (val.code) {
                                        if(event.data.attrName == "valuationApproach") {
                                            self.valuationConclusion.valuationApproaches[event.index][event.attrNameDesc] = val.description;
                                        } else {
                                            if (self.valuationConclusion[event.attrNameDesc] == "") {
                                                self.valuationConclusion[event.attrNameDesc] = val.description;
                                            } else {
                                                self.valuationConclusion[event.attrNameDesc] = self.valuationConclusion[event.attrNameDesc] + "\n\n" + val.description;
                                            }
                                        }
                                    }
                                    index += 1;
                                });
                            },
                            error: function (response) {
                                self.errorHandler(response);
                            }
                        });
                    }
                });

                if (event.data.attrName == "marketComment" || event.data.attrName == "basisOfValue") {
                    self.valuationConclusion[event.data.attrName] = event.data.val[0];
                } else if (event.data.attrName == "valuationApproach") {
                    self.valuationConclusion.valuationApproaches[event.index][event.data.attrName] = event.data.val[0];
                } else {
                    self.valuationConclusion[event.data.attrName] = event.data.val;
                }

                if (event.data.attrName == "valuationApproach") {
                    EventBus.$emit('notify-report-parent', {
                        'key': 'valuationApproaches',
                        'value': self.valuationConclusion.valuationApproaches
                    });
                } else {
                    EventBus.$emit('notify-report-parent', {'key':event.attrNameDesc, 'value':self.valuationConclusion[event.attrNameDesc]});
                    self.notifyParent(event.data);
                }
            },
            populateDefaultDropdown: function (filterId) {
                const self = this;
                if (filterId == "basisOfValue" && !self.valuationConclusion.basisOfValue) {
                    self.valuationConclusion.basisOfValue = [self.getClassificationObject('BasisOfValuationDS', self.defaultBasisOfValueCode)];
                    EventBus.$emit('notify-report-parent', {
                        'key': 'basisOfValue',
                        'value': self.valuationConclusion.basisOfValue
                    });
                }
            },
            populateDefaultValuationConclusion: function () {
                const self = this;
                if (!self.valuationConclusion.basisOfValueDescription) {
                    const classificationObject = self.getClassificationObject('BasisOfValuationDS', self.defaultBasisOfValueCode);
                    self.valuationConclusion.basisOfValueDescription = classificationObject?.description ?? '';
                    EventBus.$emit('notify-report-parent', {'key': 'basisOfValueDescription', 'value': self.valuationConclusion.basisOfValueDescription});
                }
            },
            populateDefaultPropertyDescription: function() {
                const self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.HomeValuation.getHomeValuationPropertyDescription(self.homeValuationId).url,
                    cache: false,
                    success: function (response) {
                        self.valuationConclusion.propertyDescription = decodeURIComponent(response.toString());
                    },
                    error: function (response) {
                        self.valuationConclusion.propertyDescription = "";
                        self.errorHandler(response);
                    },
                    complete: function () {
                        EventBus.$emit('notify-report-parent', {
                            'key': 'propertyDescription',
                            'value': self.valuationConclusion.propertyDescription
                        });
                        self.showValuationConclusionTemplate = false;
                        self.refreshValuationConclusionTemplate = true;
                    }
                });
            },
            populateDefaultValuationConclusionDescription: function(obj) {
                const self = this;
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.HomeValuation.generateValuationConclusion().url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify({jobId: self.homeValuationId, classification: self.valuationConclusion.valuationApproaches[obj.objKey-1].valuationApproach}),
                    dataType: "json",
                    success: function (response) {
                        var valuationConclusions = response.toString();
                        if(obj && obj.objKey) {
                            self.valuationConclusion.valuationApproaches[obj.objKey-1].valuationApproachConclusions = valuationConclusions;
                        } else {
                            $.each( self.valuationConclusion.valuationApproaches, function(i, vc) {
                                self.valuationConclusion.valuationApproaches[i].valuationApproachConclusions = valuationConclusions;
                            });
                        }
                    },
                    error: function (response) {
                        self.errorHandler(response);
                        if(obj && obj.objKey) {
                            self.valuationConclusion.valuationApproaches[obj.objKey-1].valuationApproachConclusions = "";
                        } else {
                            $.each( self.valuationConclusion.valuationApproaches, function(i, vc) {
                                self.valuationConclusion.valuationApproaches[i].valuationApproachConclusions = "";
                            });
                        }
                    },
                    complete: function () {
                        EventBus.$emit('notify-report-parent', {
                            'key': 'valuationApproaches',
                            'value': self.valuationConclusion.valuationApproaches
                        });
                        self.showValuationConclusionTemplate = false;
                        self.refreshValuationConclusionTemplate = true;
                    }
                });
            },
            addValuationApproach: function(key) {
                const self = this;
                if (!self.valuationConclusion.valuationApproaches) {
                    self.valuationConclusion.valuationApproaches = []
                }
                self.valuationConclusion.valuationApproaches.splice(key+1, 0, { valuationApproach: self.getClassificationObject('ValuationApproachDS', self.defaultValuationApproachCode), valuationApproachDescription: self.defaultApproachDesc, valuationApproachConclusions: ""})
                var obj = {}
                obj.attrName = 'valuationApproaches'
                obj.val = self.valuationConclusion.valuationApproaches
                self.notifyParent(obj);
                self.showValuationConclusionTemplate = false;
                self.refreshValuationConclusionTemplate = true;
            },
            removeValuationApproach: function(key) {
                const self = this;
                self.valuationConclusion.valuationApproaches.splice(key, 1);
                var obj = {}
                obj.attrName = 'valuationApproaches'
                obj.val = self.valuationConclusion.valuationApproaches
                self.notifyParent(obj);
                self.showValuationConclusionTemplate = false;
                self.refreshValuationConclusionTemplate = true;
            }
        },
        mounted: function() {
            const self = this;

            EventBus.$on('notify-simple-valuation-conclusion', function(data){
                self.valuationConclusion[data.attrName] = data.val;
                self.notifyParent(data);
            });

            EventBus.$on('notify-simple-nested-valuation-conclusion', function(data){
                self.valuationConclusion[data.parentAttrName][(data.key-1)][data.attrName] = data.val;
                EventBus.$emit('notify-report-parent', {
                    'key': 'valuationApproaches',
                    'value': self.valuationConclusion.valuationApproaches
                });
            });

            EventBus.$on('notify-multi-nested-valuation-conclusion', function(data){
                self.valuationConclusion[data.parentAttrName][(data.key-1)][data.attrName] = data.val;
                EventBus.$emit('notify-report-parent', {
                    'key': 'valuationApproaches',
                    'value': self.valuationConclusion.valuationApproaches
                });
            });
        },
        created: function() {
            var self = this;

            EventBus.$on('home-valuation-saved', function(obj) {
                var loadingNewJob = self.homeValuationId != obj.homeValuation.id;
                self.valuationApproachSelected = true;
                if(loadingNewJob || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.valuationConclusion = homeValuation.reportDetails ? homeValuation.reportDetails : {};
                    self.initiateSetupMultiSelect();
                    if (!self.valuationConclusion.valuationApproaches || self.valuationConclusion.valuationApproaches.length == 0) {
                        self.addValuationApproach(0);
                    }
                    self.showValuationConclusionTemplate = false;
                    self.refreshValuationConclusionTemplate = true;
                    self.homeValuationId = homeValuation.id;
                }
            });

            EventBus.$on('home-valuation-new', function(homeValuation) {
                self.valuationConclusion = {};
                self.populateDefaultValuationConclusion();
                self.initiateSetupMultiSelect();
                self.valuationConclusion.valuationApproaches = [{ valuationApproach: self.getClassificationObject('ValuationApproachDS', self.defaultValuationApproachCode), valuationApproachDescription: self.defaultApproachDesc, valuationApproachConclusions: ""}];
                self.showValuationConclusionTemplate = false;
                self.refreshValuationConclusionTemplate = true;
                self.valuationApproachSelected = false;
                self.homeValuationId = homeValuation.id;
            });

            EventBus.$on('load-default-property-data', function(propertyData) {
                self.defaultPropertyData = propertyData.property
            });


            EventBus.$on('report-details-tabs', function(state){
                self.tabState = state
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                var values = {};
                var obj =  {};
                obj.attrName = "specialConditions";
                self.resetDefaultValuationApproach(reportTypeCode);
                if (!self.valuationConclusion.specialConditions) {
                    self.valuationConclusion.specialConditions = [];
                }
                if (reportTypeCode == "HTTB") {
                    self.valuationConclusion.specialConditions.code = "HNZTenanttoBuy";
                    values.category = "SpecialConditions";
                    values.code = "HNZTenanttoBuy";
                    obj.val = [];
                    obj.val.push(values);
                    self.callback(obj);
                } else if (reportTypeCode == "KS" || reportTypeCode == "KSA") {
                    self.valuationConclusion.specialConditions.code = "KerbsideInspection";
                    values.category = "SpecialConditions";
                    values.code = "KerbsideInspection";
                    obj.val = [];
                    obj.val.push(values);
                    self.callback(obj);
                } else if (reportTypeCode === 'PWNB') {
                    self.valuationConclusion.specialConditions.code = "Valuationfromplans";
                    values.category = "SpecialConditions";
                    values.code = "Valuationfromplans";
                    obj.val = [];
                    obj.val.push(values);
                    self.callback(obj);
                }

                self.reportType.code = reportTypeCode
            });

            EventBus.$on('set-fields', function(fields) {
                self.fields = fields.reportDetails.valuationConclusion;
            });
        },
        updated: function() {
            const self = this
            if (self.refreshValuationConclusionTemplate) {
                self.showValuationConclusionTemplate = true;
                self.refreshValuationConclusionTemplate = false;
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-valuation-conclusion', this.listener);
            EventBus.$off('report-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('notify-simple-nested-valuation-conclusion', this.listener);
            EventBus.$off('notify-multi-nested-valuation-conclusion', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-valuation-conclusion', this.listener);
            EventBus.$off('report-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('notify-simple-nested-valuation-conclusion', this.listener);
            EventBus.$off('notify-multi-nested-valuation-conclusion', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

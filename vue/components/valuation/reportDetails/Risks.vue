<template>
    <!-- RISKS TAB STARTS -->
    <div class="QVHV-Container risks active" v-bind:class="[tabState=='open' ? 'canOpener' : '']" v-if="showRisksTemplate">
        <ul class="QVHV-tabs">
            <li><span class="is-active">Risks</span></li>
            <hr align="left"/>
        </ul>
        <div class="QVHV-formSection">

            <!-- Static Risks -->
            <div class="advSearch-row" v-for="staticRiskType,key in staticRisks">
                <div class="advSearch-Subrow">
                    <div :class="[fields.staticRisks[staticRiskType.riskFactor.description]]" class="advSearch-group twentyfivePct icons8-medium-priority-filled"><h4>
                        {{ staticRiskType.riskFactor.description }}</h4></div>
                    <div class="advSearch-group seventyfivePct-tenRem rangeSlider">
						<span>
                            <!-- <input class="advSearch-text" v-if="fieldType=='text'" type="text" @change="notifyComponent()" v-model="val"> -->
							<input :id="'riskRange_'+(key+1)" v-model="staticRisks[key].score" type="range"
                                   :name="staticRiskType.riskFactor.description" min="1" max="5" step="1" value="1"
                                   @change="notifyStaticRisksScore(key)"/>
							<ul class="sliderLabel-row">
								<li>1</li>
								<li>2</li>
								<li>3</li>
								<li>4</li>
								<li>5</li>
							</ul>
						</span>
                    </div>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <text-input :id="'riskTitle_'+(key+1)" maxlength="100" :obj-key="key+1" :curr-val="staticRiskType.headline" attr-name="headline"
                                fieldType="text" iconClass="seventyfivePct-tenRem"
                                :label="staticRiskType.riskFactor.description + ' Headline'"
                                component-name="staticRiskType"
                                :errorMsg="staticRiskType.score > 2 && !staticRiskType.headline ? 'This field is required for a score greater than 2': ''"
                                parentAttrName="staticRiskType"></text-input>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <text-area-input :id="'riskDescription_'+(key+1)" maxlength="5000" :obj-key="key+1" :curr-val="staticRiskType.comment" attr-name="comment"
                                     fieldType="text" iconClass="seventyfivePct-tenRem"
                                     :label="staticRiskType.riskFactor.description + ' Comment'"
                                     :errorMsg="staticRiskType.score > 2 && !staticRiskType.comment ? 'This field is required for a score greater than 2': ''"
                                     component-name="staticRiskType"
                                     parentAttrName="staticRiskType"></text-area-input>
                </div>
            </div>

            <!-- Other Risks -->
            <div class="advSearch-row" v-for="otherRiskType,key in otherRisks">
                <div class="advSearch-Subrow">
                    <valuation-multi-select-filter :class="[fields.otherRisks]" :obj-key="key+1" :curr-val="otherRiskType.riskFactor"
                                                   iconClass="twentyfivePct icons8-medium-priority-filled"
                                                   component-name="otherRiskType" parentAttrName="otherRiskType"
                                                   attrName="riskFactor" :filter-id="'riskFactor'+(key+1)"
                                                   label="Other Risks"
                                                   :selectClass="'monarch-multiselect otherRiskType'+(key+1)"
                                                   data-to-fetch="OtherRisks"></valuation-multi-select-filter>

                    <div class="advSearch-group seventyfivePct-tenRem rangeSlider" v-bind:class="[!otherRiskType.riskFactor ? 'disabled' : '']">
						<span>
							<input v-model="otherRisks[key].score" type="range" min="1" max="5" step="1" value="1"
                                   @change="notifyOtherRisksScore(key)"/>
							<ul class="sliderLabel-row">
								<li>1</li>
								<li>2</li>
								<li>3</li>
								<li>4</li>
								<li>5</li>
							</ul>
						</span>
                    </div>

                    <div class="advSearch-group sa-addRemove" v-bind:class="[!otherRiskType.riskFactor ? 'disabled' : '']">
                        <i class="saRow-add addLivingArea material-icons" @click="addOtherRisks(key)"></i>
                        <i v-if="key>0" class="saRow-remove material-icons" @click="removeOtherRisks(key)"></i>
                    </div>
                </div>
                <div class="advSearch-Subrow" v-bind:class="[!otherRiskType.riskFactor ? 'disabled' : '']">
                    <div class="advSearch-group twentyfivePct"></div>
                    <text-input maxlength="100" :obj-key="key+1" :curr-val="otherRiskType.headline" attr-name="headline"
                                fieldType="text" iconClass="seventyfivePct-tenRem" label="Other Risk Headline"
                                :errorMsg="otherRiskType.score > 2 && !otherRiskType.headline ? 'This field is required for a score greater than 2': ''"
                                component-name="otherRiskType" parentAttrName="otherRiskType"></text-input>
                </div>
                <div class="advSearch-Subrow" v-bind:class="[!otherRiskType.riskFactor ? 'disabled' : '']">
                    <div class="advSearch-group twentyfivePct"></div>
                    <text-area-input maxlength="1000" :obj-key="key+1" :curr-val="otherRiskType.comment" attr-name="comment"
                                     fieldType="text" iconClass="seventyfivePct-tenRem" label="Other Risk Comment"
                                     :errorMsg="otherRiskType.score > 2 && !otherRiskType.comment ? 'This field is required for a score greater than 2': ''"
                                     component-name="otherRiskType" parentAttrName="otherRiskType"></text-area-input>
                </div>
            </div>

            <div class="advSearch-row">
                <div class="advSearch-Subrow">
                    <text-area-input :class="[fields.specialAssumptions]" maxlength="5000" :curr-val="risksDetails.specialAssumptions" attr-name="specialAssumptions"
                                     fieldType="text" iconClass="hundyPct icons8-pencil" label="Special Assumptions"
                                     component-name="specialAssumptions"></text-area-input>
                </div>
                <div class="advSearch-Subrow">
                    <text-area-input :class="[fields.conflictOfInterest]" maxlength="1000" :curr-val="risksDetails.conflictOfInterest" attr-name="conflictOfInterest"
                                     fieldType="text" iconClass="hundyPct icons8-pencil" label="Conflict of Interest"
                                     component-name="conflictOfInterest"></text-area-input>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import TextInput from '../../filters/TextInput.vue'
    import TextAreaInput from '../../filters/TextAreaInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import {EventBus} from '../../../EventBus.js';
    import {store} from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            TextAreaInput,
            ValuationMultiSelectFilter
        },
        data: function () {
            return {
                risksDetails: {},
                staticRisks: [],
                otherRisks: [],
                staticRisksMaxSize: 0,
                showRisksTemplate: true,
                refreshRisksTemplate: false,
                tabState: 'closed',
                tempInputVal: 1,
                homeValuationId: undefined,
                reportType: {},
                fields: {
                    staticRisks: {},
                    otherRisks: {}
                }
            }
        },
        methods: {
            notifyParent: function (key, value) {
                EventBus.$emit('notify-report-parent', {'key': key, 'value': value})
            },
            notifyStaticRisksScore: function (key) {
                const self = this;
                var obj = {};

                obj.attrName = "score";
                obj.val = self.staticRisks[key].score;
                obj.key = key + 1;
                EventBus.$emit('notify-simple-nested-staticRiskType', obj);
                self.refreshView();
            },
            notifyOtherRisksScore: function (key) {
                const self = this;
                var obj = {};

                obj.attrName = "score";
                obj.val = self.otherRisks[key].score;
                obj.key = key + 1;
                EventBus.$emit('notify-simple-nested-otherRiskType', obj);
                self.refreshView();
            },
            refreshView: function () {
                const self = this
                self.showRisksTemplate = !self.showRisksTemplate
                if (!self.showRisksTemplate) {
                    self.refreshRisksTemplate = true
                }
            },
            getStoreStaticRisksLength: function () {
                return this.$store.getters.getCategoryClassifications('Risks').length;
            },
            addStaticRisksType: function () {
                const self = this
                if (!self.staticRisks || self.staticRisks.length == 0) {
                    self.staticRisks = [];
                    var optionsForClassification = self.$store.getters.getCategoryClassifications('Risks');
                    if (optionsForClassification && optionsForClassification.length > 0) {
                        var index = 0
                        $.each(optionsForClassification, function () {
                        self.staticRisks.push({riskFactor: optionsForClassification[index], score: 1});
                                index++;
                        });
                        self.staticRisksMaxSize = index;
                        self.refreshView();
                    }
                }
            },
            updateStaticRisks: function () {
                const optionsForClassification = this.$store.getters.getCategoryClassifications('Risks');
                const existingRiskCodes = new Set(this.staticRisks.map(risk => risk.riskFactor.code));

                for (const classification of optionsForClassification) {
                    if (!existingRiskCodes.has(classification.code)) {
                        this.staticRisks.push({
                            headline: null,
                            comment: null,
                            riskFactor: classification,
                            score: 1
                        });
                    }
                }
            },
            addOtherRisks: function (key) {
                const self = this
                if (!self.otherRisks || self.otherRisks.length == 0) {
                    self.otherRisks = []
                }
                self.otherRisks.splice(key + 1, 0, {score: 1})
                self.refreshView();
            },
            removeOtherRisks: function (key) {
                const self = this
                self.otherRisks.splice(key, 1);
                self.risksDetails.riskFactors = self.staticRisks.concat(self.otherRisks); //merge static and other to risksDetails
                self.notifyParent('riskFactors', self.risksDetails.riskFactors);
                self.refreshView();
            },
            populateSpecialAssumptions: function (specialAssumptions) {
                const self = this;
                if(specialAssumptions) {
                    self.risksDetails.specialAssumptions = specialAssumptions;
                } else {
                    var specialAssumptions = self.$store.getters.getCategoryClassifications('SpecialAssumptions');
                    if(specialAssumptions[0]) {
                        self.risksDetails.specialAssumptions = specialAssumptions[0].description;
                        self.notifyParent('specialAssumptions', self.risksDetails.specialAssumptions);
                        self.refreshView();
                    }
                }
            },
            sendRiskDetailsToParent: function () {
                this.risksDetails.riskFactors = this.staticRisks.concat(this.otherRisks);
                this.notifyParent('riskFactors', this.risksDetails.riskFactors);
            }
        },
        mounted: function () {
            const self = this
            EventBus.$on('notify-simple-nested-staticRiskType', function (data) {
                self.staticRisks[data.key - 1][data.attrName] = data.val
                self.risksDetails.riskFactors = self.staticRisks.concat(self.otherRisks);
                self.notifyParent('riskFactors', self.risksDetails.riskFactors);
            })
            EventBus.$on('notify-simple-nested-otherRiskType', function (data) {
                self.otherRisks[data.key - 1][data.attrName] = data.val
                self.risksDetails.riskFactors = self.staticRisks.concat(self.otherRisks);
                self.notifyParent('riskFactors', self.risksDetails.riskFactors);
            })
            EventBus.$on('notify-multi-nested-otherRiskType', function (data) {
                self.otherRisks[(data.key - 1)][data.attrName] = data.val
                self.risksDetails.riskFactors = self.staticRisks.concat(self.otherRisks);
                self.notifyParent('riskFactors', self.risksDetails.riskFactors);
                self.refreshView()
            })
            EventBus.$on('notify-simple-specialAssumptions', function (data) {
                self.risksDetails[data.attrName] = data.val
                self.notifyParent(data.attrName, data.val);
            })
            EventBus.$on('notify-simple-conflictOfInterest', function (data) {
                self.risksDetails[data.attrName] = data.val
                self.notifyParent(data.attrName, data.val);
            })

        },
        created: function () {
            var self = this;
            EventBus.$on('home-valuation-saved', function (obj) {
                var loadingNewJob = self.homeValuationId != obj.homeValuation.id;
                if(loadingNewJob || obj.reload) {
                    var homeValuation = obj.homeValuation;
                    self.homeValuationId = homeValuation.id;
                self.risksDetails = homeValuation.reportDetails ? homeValuation.reportDetails : {}
                self.staticRisks = [];
                self.otherRisks = [];
                if (!self.risksDetails.riskFactors) {
                    self.addStaticRisksType();
                    self.addOtherRisks(0);
                } else if (self.risksDetails.riskFactors.length == self.staticRisksMaxSize) {
                    self.addOtherRisks(0);
                } else {
                    var index = 0;
                    $.each(self.risksDetails.riskFactors, function () {
                        if (self.risksDetails.riskFactors && self.risksDetails.riskFactors[index] && self.risksDetails.riskFactors[index].riskFactor && self.risksDetails.riskFactors[index].riskFactor.category && self.risksDetails.riskFactors[index].riskFactor.category == "Risks") { //Check the classification category if it is `Risks`
                            self.staticRisks.push(self.risksDetails.riskFactors[index]);
                        } else {
                            self.otherRisks.push(self.risksDetails.riskFactors[index]);
                        }
                        index++;
                    });
                }

                const expectedStaticRisksLength = self.getStoreStaticRisksLength();
                if (self.staticRisks.length < expectedStaticRisksLength) {
                    self.updateStaticRisks();
                    self.sendRiskDetailsToParent();
                }

                self.populateSpecialAssumptions(self.risksDetails.specialAssumptions);
                self.showRisksTemplate = false;
                self.refreshRisksTemplate = true;
                }
            });

            EventBus.$on('home-valuation-new', function (homeValuation) {
                self.risksDetails = {};
                self.staticRisks = [];
                self.otherRisks = [];
                if (!self.risksDetails.riskFactors) {
                    self.addStaticRisksType();
                    self.addOtherRisks(0);
                } else if (self.risksDetails.riskFactors.length == self.staticRisksMaxSize) {
                    self.addOtherRisks(0);
                } else {
                    var index = 0;
                    $.each(self.risksDetails.riskFactors, function () {
                        if (self.risksDetails.riskFactors[index].riskFactor.category == "Risks") {
                            self.staticRisks.push(self.risksDetails.riskFactors[index]);
                        } else {
                            self.otherRisks.push(self.risksDetails.riskFactors[index]);
                        }
                        index++;
                    });
                }
                self.populateSpecialAssumptions();
                self.risksDetails.riskFactors = self.staticRisks.concat(self.otherRisks);
                self.notifyParent('riskFactors', self.risksDetails.riskFactors);
                self.showRisksTemplate = false;
                self.refreshRisksTemplate = true;
            });

            EventBus.$on('report-details-tabs', function (state) {
                self.tabState = state
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                if(reportTypeCode) {
                    var specialAssumptions = self.$store.getters.getCategoryClassifications('SpecialAssumptions');
                    if (specialAssumptions && specialAssumptions.length > 0) {
                        var specialAssumptions = specialAssumptions.filter(function (specAssump) {
                            return specAssump.code === reportTypeCode;
                        });
                        // self.risksDetails.specialAssumptions = specialAssumptions && specialAssumptions.length > 0 ? specialAssumptions[0].description : '';
                        self.risksDetails.specialAssumptions = specialAssumptions && specialAssumptions.length > 0 ? specialAssumptions[0].description : '';
                        self.notifyParent('specialAssumptions', self.risksDetails.specialAssumptions);
                    }
                }
                self.reportType.code = reportTypeCode
                self.refreshView();
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.reportDetails.risks;
                }
            });
        },
        updated: function () {
            const self = this
            if (self.refreshRisksTemplate) {
                self.showRisksTemplate = true
                self.refreshRisksTemplate = false
            }
        },
        destroyed: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('report-details-tabs', this.listener);
            EventBus.$off('notify-simple-specialAssumptions', this.listener);
            EventBus.$off('notify-simple-conflictOfInterest', this.listener);
            EventBus.$off('notify-simple-nested-otherRiskType', this.listener);
            EventBus.$off('notify-multi-nested-otherRiskType', this.listener);
            EventBus.$off('notify-simple-nested-staticRiskType', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('report-details-tabs', this.listener);
            EventBus.$off('notify-simple-specialAssumptions', this.listener);
            EventBus.$off('notify-simple-conflictOfInterest', this.listener);
            EventBus.$off('notify-simple-nested-otherRiskType', this.listener);
            EventBus.$off('notify-multi-nested-otherRiskType', this.listener);
            EventBus.$off('notify-simple-nested-staticRiskType', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div>
        <div class="md-table mdl-shadow--2dp">
            <h2>Report Details</h2>
            <div data-cy="report-details-expand-all" class="expandAll" @click="expandTabs()" v-bind:class="[tabState == 'open' ? 'down' : '']">
                <span title="Expand Form" class="mdl-button mdl-js-button mdl-button--icon"><i class="material-icons md-dark"></i></span>
            </div>
            <ul id="tabsElementReportDetails" v-bind:class="[tabState=='open' ? 'hide' : 'QVHV-tabs']">
                <li class="QVHVTab-1" @click="setCurrentTab('Valuation Conclusion', 1, true)" data-tab="QVHVTab-1" data-container="valuationconclusion"><span  v-bind:class="[currentTab == 'Valuation Conclusion' || tabState == 'open' ? 'is-active' : '']">Valuation Conclusion</span></li>
                <li class="QVHVTab-2" @click="setCurrentTab('Risks', 2, true)" data-tab="QVHVTab-2" data-container="risks"><span  v-bind:class="[currentTab == 'Risks' || tabState == 'open' ? 'is-active' : '']">Risks</span></li>
                <hr align="left"/>
            </ul>
            <valuation-conclusion v-show="currentTab=='Valuation Conclusion' || tabState == 'open'" v-bind:class="{disabled: readOnly}"></valuation-conclusion>
            <risks v-show="currentTab=='Risks' || tabState == 'open'" v-bind:class="{disabled: readOnly}"></risks>
        </div>
        <div class="QVHV-buttons" v-bind:class="{disabled: readOnly}">
            <div class="QVHV-buttons-left" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="primary" v-on:click="saveReportDetailsData(false, true)">Save</button>
            </div>
            <div class="QVHV-buttons-right" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="secondary" v-on:click="previousStep()">Back</button>
                <button class="primary" v-on:click="saveReportDetailsData(true, true)">Next Step</button>
            </div>
        </div>
    </div>
</template>

<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import deepEqual from 'deep-equal';
    import ValuationConclusion from './reportDetails/ValuationConclusion.vue';
    import Risks from './reportDetails/Risks.vue';
    import moment from 'moment';

    export default {
        props: ['readOnly'],
        components: {
            ValuationConclusion,
            Risks
        },
        data: function() {
            return {
                reportDetails: {
                    marketComment: {},
                    valuationApproaches: [],
                    demandForSubjectProperty: {},
                    specialConditions: {},
                    additionalComments: {},
                    riskFactors: {}
                },
                jobStatusCode: "",
                currentTab: 'Valuation Conclusion',
                tabState: 'closed',
                isDataSaving: false,
                homeValuation: {},
                reportDetailsCopy: null,
                isDirty: false
            }
        },
        methods: {
            expandTabs: function() {
                const self = this
                if (self.tabState == 'closed') {
                    self.tabState = 'open'
                }
                else {
                    self.tabState = 'closed'
                }
                EventBus.$emit('report-details-tabs', self.tabState)
            },
            saveReportDetailsData: function(nextStep, persist){
                const self = this;
                var event = {};
                event.reportDetails = self.reportDetails;
                event.next = nextStep;
                event.persist = persist;
                self.isDataSaving = persist;

                var haveChanges = !self.jsonEqual(self.reportDetails, self.reportDetailsCopy);
                event.isDirty = haveChanges;
                EventBus.$emit('home-valuation-report-details', event);
            },
            previousStep: function(){
                const self = this;
                if(self.jobStatusCode === "S" || !self.jobStatusCode || self.jobStatusCode == "" || self.jobStatusCode == null) {
                    EventBus.$emit('home-valuation-back', "propertyDetailsStepper");
                } else {
                    EventBus.$emit("home-valuation-back", "valuationStepper");
                }
                self.saveReportDetailsData(false, true);
            },
            setCurrentTab: function(name, index, save){
                const self = this;
                self.currentTab = name;
                $('#tabsElementReportDetails').find('hr').removeClass().addClass('QVHVTab-'+ index );
                if(save) self.saveReportDetailsData(false, true);
            },
            jsonEqual: function jsonEqual(a,b) {
                return deepEqual(a,b);
            }
        },
        created: function() {
            var self = this;
            EventBus.$on('enable-buttons', function(){
                self.isDataSaving = false
            })

            EventBus.$on('notify-report-parent', function(obj){
                if (obj.key == "marketComment" || obj.key == "basisOfValue") {
                    self.reportDetails[obj.key] = obj.value[0];
                } else if(obj.key == "valuationApproaches" && obj.attribute) {
                        self.reportDetails.valuationApproaches[obj.index][obj.attribute] = obj.value;
                } else {
                    self.reportDetails[obj.key] = obj.value;
                }
                self.saveReportDetailsData(false, false);
            })
            EventBus.$on('home-valuation-saved', function(obj){
                var fromStep = obj.fromStep;
                if(fromStep != 5 || (self.homeValuation == {} || self.homeValuation.id != obj.homeValuation.id) || obj.reload) {
                    self.homeValuation = JSON.parse(JSON.stringify(obj.homeValuation));
                    self.reportDetails = self.homeValuation.reportDetails ? self.homeValuation.reportDetails : {}
                }
                self.reportDetailsCopy = JSON.parse(JSON.stringify(self.reportDetails));
                self.isDataSaving = false;
                self.jobStatusCode = self.homeValuation.status.code;
            })
            EventBus.$on('home-valuation-new', function(propertyData) {
                self.setCurrentTab('Valuation Conclusion', 1, false)
                self.reportDetails = {}
            });
            EventBus.$on('home-valuation-loaded', function(){
                self.setCurrentTab('Valuation Conclusion', 1, false)
            })

        },
        destroyed: function() {
            EventBus.$off('notify-report-parent', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
            EventBus.$off('enable-buttons', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('notify-report-parent', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
            EventBus.$off('enable-buttons', this.listener);
        }
    }
</script>

<template>
    <div class="resultsWrapper roll-maintenance-activity-list router" v-if="isInternalUser" v-show="showTemplate">
        <div class="resultsInner-wrapper mdl-shadow--3dp">
            <div class="resultsTitle qv-search-title">
                <h1>Valuation Jobs</h1>
            </div>
            <valuation-search-criteria
                v-show="!isMobileView"
                :taCodes="taCodes"
                :valuation-jobs="valuationJobs"
                :export-results-disabled="exportResultsDisabled"
                @search="handleSearch"
                @exportResults="exportResults"
                @display-valuation-jobs="onDisplayValuationJobs"
            />
            <div class="resultsFound">
                <p>{{totalJobs}} jobs found </p>
            </div>

            <div id="valuationDesktop" class="sortRow-wrapper" v-if="totalJobs > 0">
                <div class="sortRow valjobRow">
                    <!-- ^^ THE ELEMENT ".sortrow" CONTROLS THE VIEW OF THE SEARCH RESULTS SORTING ROW -->
                    <!-- ^^ THE ELEMENT ".sortrow" SHOULD INCLUDE ".valJobRow" IF A THE VALUATION JOB DASHBOARD IS SELECTED -->

                    <div data-value="value 1" data-sort="addressDefaultSort" class="colHeader address ">
                        Address<i class="material-icons md-dark">&#xE5C5;</i>
                    </div>
                    <div data-value="value 2" data-sort="valuationReference" class="colHeader valref">
                            Val Ref<i class="material-icons md-dark">&#xE5C5;</i>
                    </div>
                    <div class="searchDetails-wrapper">
                        <div data-value="value 2" data-sort="INSPECTION_DATE" class="colHeader inspectiontime">
                            <a href="#"><span class="icon"><i class="sorter material-icons md-18 up">&#xE5DB;</i></span>
                                Inspection Date<i class="material-icons md-dark">&#xE5C5;</i></a>
                        </div>
                        <div data-value="value 2" data-sort="DUE_DATE" class="colHeader jobdue active">
                            <a href="#">
                                <span class="icon"><i class="sorter material-icons md-18 up">&#xE5DB;</i></span>
                                Job Due<i class="material-icons md-dark">&#xE5C5;</i>
                            </a>
                        </div>
                        <div data-value="value 2" data-sort="reportType" class="colHeader reporttype">
                            Report Type<i class="material-icons md-dark">&#xE5C5;</i>
                        </div>
                        <div data-value="value 2" data-sort="assignedValuer" class="colHeader assignedvaluer">
                            Assigned To<i class="material-icons md-dark">&#xE5C5;</i>
                        </div>
                        <div data-value="value 2" data-sort="countersignedValuer" class="colHeader countersignedvaluer">
                            Countersigned By<i class="material-icons md-dark">&#xE5C5;</i>
                        </div>
                        <div data-value="value 2" data-sort="STATUS" class="colHeader jobstatus">
                            <a href="#">
                                <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>
                                Job Status<i class="material-icons md-dark">&#xE5C5;</i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="totalJobs > 0">
                <div v-for="job in valuationJobs"
                    :key="job.id"
                    class="resultsRow valjobRow"
                    v-bind:class="{overdueJob: job.status==='Overdue', completedJob: job.status==='Completed'}"
                    @click="loadValuationJob(job.id, job.propertyId, job.territorialAuthorityCode)"
                >
                    <!--
                        ^^ THE ELEMENT ".resultsRow.valjobRow" CAN INCLUDE
                        ".overdueJob" , ".completedJob" BASED ON THE ITS DUE DATE OR STATUS
                    -->
                    <div class="colCell address">
                        <!--<a class="mobileComps-linker" @click="showMobileComps(job.id)"></a>-->
                        <span class="primaryThumb-Wrapper"><img class="primaryPhoto_thumb" v-bind:src="job.primaryPhoto"></span>
                        <div class="fullAddress">
                            <span v-html="job.address1"></span>
                            <span v-html="job.address2"></span>
                        </div>
                    </div>
                    <div class="colCell valref" v-html="job.valRef"></div>
                    <div class="searchDetails-wrapper">
                        <div class="colCell inspectiontime" v-html="job.jobInspectionDate"></div>
                        <div class="colCell jobdue" v-html="job.jobDueDate"></div>
                        <div class="colCell reporttype" v-html="job.reportType"></div>
                        <div class="colCell assignedvaluer" v-html="job.valuer"></div>
                        <div class="colCell countersignedvaluer" v-html="job.counterSignedValuer"></div>
                        <div class="colCell jobstatus" v-html="job.status"></div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="exportResultsDisabled" class="loadingSpinner loadingSpinnerSearchResults"></div>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
            />
        </alert-modal>
    </div>
</template>
<script>
    import moment from 'moment';
    import { mapState } from 'vuex';
    import ValuerUser from '../filters/ValuerUser.vue'
    import AlertModal from '../common/modal/AlertModal.vue'
    import ValuationSearchCriteria from './ValuationSearchCriteria.vue';
    import { EventBus } from '../../EventBus.js';
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import { submitMonarchExport } from '../reports/utils.js';
    import { store } from '../../DataStore';
    import * as HomeValuationController from '@/services/HomeValuationController';

    export default {
        props: ['isMobileView'],
        components: {
            ValuerUser,
            AlertModal,
            ValuationSearchCriteria
        },
        mixins: [formatUtils, commonUtils],
        data: function() {
            return {
                showTemplate : false,
                onHomePage: false,
                totalJobs: 0,
                valuationJobs: [],
                searchCriteria: {},
                windowWidth: 993,
                exportResultsDisabled: false,
                modal: {
                    mode: 'warning',
                    isOpen: false,
                    heading: 'heading',
                    message: '',
                    messages: [],
                    cancelText: 'No',
                    cancelAction: () => { },
                    confirmText: 'Yes',
                    confirmAction: () => { },
                    code: '',
                },
            }
        },
        computed: {
            ...mapState('userData', {
                userLoaded: 'loaded',
                userName: 'userName'
            }),
            ...mapState('userData', [
                'isInternalUser',
                'userId',
                'qivsUrl',
            ]),
            ...mapState({
                monarchUserId: state => state.application.monarchUser.id
            }),
            ...mapState('rollMaintenanceSearch', {
                isUnassigned: state => state.searchCriteria.valuationSearchCriteria.isUnassigned,
                valuationReportTypes: state => state.searchCriteria.valuationSearchCriteria.valuationReportTypes,
                valuationJobStatuses: state => state.searchCriteria.valuationSearchCriteria.valuationJobStatuses,
                valuers: state => state.searchCriteria.valuationSearchCriteria.valuers,
                inspectionDateFrom: state => state.searchCriteria.valuationSearchCriteria.inspectionDateFrom,
                inspectionDateTo: state => state.searchCriteria.valuationSearchCriteria.inspectionDateTo,
            }),
            ...mapState({
                taCodes: state => state.taCodes.taCodes,
                taCodesLoaded: state => state.taCodes.taCodesLoaded,
                }),
            isTAUser() {
                return !store.state.userData.isInternalUser && store.state.userData.isTAUser;
            },
            taCodes() {
                return this.isTAUser
                    ? [store.state.userData.userTACode].map(item => parseInt(item, 10))
                    : store.state.taCodes.taCodes.map(item => parseInt(item, 10));
            },

        },
        methods: {
            showMobileComps: function(id) {
                window.location = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?valJobId=' + id;
            },
            refreshJobs:async function(){
                const self = this

                try {
                    const data = await HomeValuationController.displayMarketValuationJobSearchResult(self.searchCriteria);
                    self.valuationJobs = self.generateValuationJobs(data);
                } catch (error) {
                    console.error('Error refreshing valuation jobs:', error);

                }
            },
            openQIVSLink: function(){
                window.open(this.qivsUrl, "QIVZ");
            },
            loadValuationJob: function(jobId, propertyId, territorialAuthorityCode) {
                if(this.isMobileView) {
                    window.location = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?valJobId=' + jobId + '&propertyId=' + propertyId;
                } else {
                    var event={}
                    event.searchType = 'master-details';
                    event.propertyId = propertyId
                    event.valuationJobId = jobId
                    event.territorialAuthorityCode = territorialAuthorityCode
                    EventBus.$emit('display-content', event)
                }
            },
            getSortFields: function(sortFieldName) {
                var sortFields = []
                if (sortFieldName == 'DUE_DATE' || sortFieldName == 'INSPECTION_DATE') {
                    sortFields=[sortFieldName,'STATUS']
                }
                else {
                    sortFields.push(sortFieldName)
                }
                return sortFields
            },
            generateValuationJobs : function(response) {
                const self = this
                var valuationJobs = []

                for (var i = 0; i <  response.length; i++) {
                    var valuationJobToRender = {}
                    var currJob = response[i]
                    var currPrimaryMediaEntry = response[i].photoURLs
                    valuationJobToRender.status = currJob.jobStatus
                    valuationJobToRender.id = currJob.jobId
                    valuationJobToRender.jobInspectionDate = self.formatDate(currJob.jobInspectionDate, 'DD/MM/YYYY @ HH:mm')
                    valuationJobToRender.jobDueDate = self.formatDate(currJob.jobDueDate, 'DD/MM/YYYY @ HH:mm')
                    valuationJobToRender.propertyId = currJob.propertySummary ? currJob.propertySummary.id : ''
                    valuationJobToRender.territorialAuthorityCode = currJob.propertySummary && currJob.propertySummary.territorialAuthority ? currJob.propertySummary.territorialAuthority.code : ''
                    var noPrimary = "assets/images/property/addPhotos.png"
                    valuationJobToRender.primaryPhoto = currPrimaryMediaEntry && currPrimaryMediaEntry.isPrimary ? (currPrimaryMediaEntry.mediaItem ? (currPrimaryMediaEntry.mediaItem.smallImageUrl ? currPrimaryMediaEntry.mediaItem.smallImageUrl : noPrimary) : noPrimary) : noPrimary

                    var address = currJob.propertySummary ? (currJob.propertySummary.address ? currJob.propertySummary.address : undefined) : undefined

                    if (address) {
                        var streetNumberSuffix = address.streetNumberSuffix ? ' '+address.streetNumberSuffix : ''
                        valuationJobToRender.address1 = ((address.streetNumber ? address.streetNumber : '')+streetNumberSuffix+ ' ' + (address.streetName ? address.streetName : '')  + (address.streetType ? ' '+address.streetType.description : '') + ',')
                        if (valuationJobToRender.address1.indexOf('undefined') !== -1) {
                            valuationJobToRender.address1 = ''
                        }
                        valuationJobToRender.address2 = address.suburb ? (address.suburb+', ') : ''
                        valuationJobToRender.address2 += address.town ? address.town + ', ' : ''
                        valuationJobToRender.address2 += currJob.propertySummary.territorialAuthority ? currJob.propertySummary.territorialAuthority.name : ''
                        if (valuationJobToRender.address2.indexOf('undefined') !== -1) {
                            valuationJobToRender.address2 = ''
                        }
                    } else {
                        valuationJobToRender.address1 = ''
                        valuationJobToRender.address2 = ''
                    }

                    var propertySummary = currJob.propertySummary ? currJob.propertySummary : undefined
                    if (propertySummary) {
                        valuationJobToRender.valRef = propertySummary.rollNumber ? propertySummary.rollNumber : ''
                        valuationJobToRender.valRef += propertySummary.assessmentNumber ? '/'+propertySummary.assessmentNumber : ''
                        valuationJobToRender.valRef += propertySummary.suffix ? ' ' + propertySummary.suffix : ''
                        if (valuationJobToRender.valRef.indexOf('undefined') !== -1) {
                            valuationJobToRender.valRef = ''
                        }
                    } else {
                        valuationJobToRender.valRef = ''
                    }

                    valuationJobToRender.reportType = currJob.reportType
                    valuationJobToRender.valuer = currJob.valuer
                    valuationJobToRender.counterSignedValuer = currJob.countersigner
                    valuationJobs.push(valuationJobToRender)
                }

                //Moved it down after the loop,
                //div should be shown once  all the processing is completed.
                if (response && response.length > 0) {
                    self.totalJobs = response[0].totalResult
                } else {
                    self.totalJobs = 0
                }
                return valuationJobs
            },
            displayContent(event) {
                if (event && this.isInternalUser && event.searchType && event.searchType == 'valuation-jobs' && event.onHomePage) {
                    this.showTemplate = true
                    this.onHomePage = true
                    if(JSON.stringify(this.searchCriteria) != '{}') this.refreshJobs();
                } else {
                    this.showTemplate = false
                    $('#valuationDesktop').find($('a')).off("click")
                    $(window).off('scroll');
                    $(window).unbind('scroll');
                    $('.searchbarWrapper').removeClass('fixed');
                }
            },
            async exportResults() {
                this.exportResultsDisabled = true;

                const submitResult = await submitMonarchExport(
                    'MONARCH_HOME_VALUATION_EXPORT',
                    this.searchCriteria,
                    this.totalJobs
                );

                if (submitResult) {
                    if (submitResult.cancelText === 'View My Reports') {
                        submitResult.cancelAction = () => { this.$router.push({ name: 'report-dashboard-my-reports' }); }
                    }
                    this.setModal(submitResult);
                }

                this.exportResultsDisabled = false;
            },
            setModal(modal) {
                this.modal = modal;
            },
            modalCancel() {
                this.modal.isOpen = false;
                this.modal.cancelAction();
            },
            modalConfirm() {
                this.modal.isOpen = false;
                this.modal.confirmAction();
            },

            toDate(text){
                const formattedDate = moment(text, 'DD/MM/YYYY');
                if (!formattedDate.isValid()) {
                    return null;
                }
                return formattedDate.format('YYYY-MM-DD');
            },

            async handleSearch() {
                if (this.onHomePage) {
                    this.showTemplate = true
                }
                this.searchCriteria.userIds = this.isMobileView && this.monarchUserId ? [this.monarchUserId] : this.valuers;
                this.searchCriteria.statusCodes = this.isMobileView ? ['S', 'R', 'P'] : this.valuationJobStatuses ? this.valuationJobStatuses.map(type => type.code) : [];
                this.searchCriteria.isUnassigned = this.isMobileView  ? null : this.isUnassigned ? this.isUnassigned : null;
                this.searchCriteria.taCodes = this.taCodes;

                this.searchCriteria.valuationReportTypes = this.valuationReportTypes ? this.valuationReportTypes.map(type => type.code) : [];
                this.searchCriteria.inspectionDateFrom = this.toDate(this.inspectionDateFrom);
                this.searchCriteria.inspectionDateTo = this.toDate(this.inspectionDateTo);

                try {
                    const data = await HomeValuationController.displayMarketValuationJobSearchResult(this.searchCriteria);
                    this.valuationJobs = this.generateValuationJobs(data);
                } catch (error) {
                    console.error('Error fetching valuation jobs:', error);
                    this.valuationJobs = [];
                    this.totalJobs = 0;
                }
            },
            async onDisplayValuationJobs(event) {
                if (this.onHomePage) {
                    this.showTemplate = true
                }

                while (!this.taCodesLoaded) {
                    await new Promise(resolve => setTimeout(resolve, 100)); // wait for 100ms
                }
                var searchCriteria = {}

                searchCriteria.userIds = this.isMobileView && this.monarchUserId ? [this.monarchUserId] : event.valuers;
                searchCriteria.statusCodes = this.isMobileView ? ['S', 'R', 'P'] : null;
                searchCriteria.isUnassigned = this.isMobileView  ? null : event.isUnassigned ? event.isUnassigned : null;
                searchCriteria.offset = 0;
                searchCriteria.max = 25;
                searchCriteria.sort = this.isMobileView ? ['INSPECTION_DATE', 'STATUS'] : ['DUE_DATE', 'STATUS'];
                searchCriteria.order = this.isMobileView ? 'ASC' : 'DESC';
                this.searchCriteria = searchCriteria;
                if(!this.isMobileView){
                    this.searchCriteria.taCodes = this.taCodes;
                }
                try {
                    const data = await HomeValuationController.displayMarketValuationJobSearchResult(this.searchCriteria);
                    this.valuationJobs = this.generateValuationJobs(data);
                } catch (error) {
                    console.error('Error fetching valuation jobs:', error);
                    this.valuationJobs = [];
                    this.totalJobs = 0;
                }
            }
        },
        mounted() {
            const self = this;
            self.windowWidth = window.innerWidth;

            EventBus.$on('display-content', function(event) {
                self.displayContent(event);
            });

            this.$nextTick(function () {
                window.addEventListener('resize', function (event) {
                    if(self.showTemplate) {
                        var oldWidth = self.windowWidth;
                        var newWidth = event.target.innerWidth;
                        var fromMobileToDesktop = oldWidth <= 992 && newWidth > 992;
                        var fromDesktopToMobile = oldWidth > 993 && newWidth <= 992;
                        self.windowWidth = newWidth;
                        if (fromMobileToDesktop || fromDesktopToMobile) {
                            window.location = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?home=true';
                        }
                    }
                });
            });
        },
        updated: function() {
            const self = this;
            if (self.showTemplate == true) {
                $('#valuationDesktop').find($('a')).off("click").click(async function () {
                    var liParent = $(this).parent();
                    var allOptions = $('#valuationDesktop').find('.sortRow').find('.colHeader');
                    var allOptionsMobile = $('#mobile').find('.sortRow').find('.colHeader');

                    if (liParent.hasClass('active')) {
                        if ($(this).find('i').hasClass('up')){
                            $(this).find('i').addClass('down');
                            $(this).find('i').removeClass('up');
                        }
                        else {
                            $(this).find('i').addClass('up');
                            $(this).find('i').removeClass('down');
                        }
                    } else {
                        allOptions.removeClass('active');
                        allOptions.find('i').removeClass('up');
                        allOptions.find('i').addClass('down');
                        liParent.addClass('active');
                    }

                    var sort = liParent.data('sort');

                    var order = $(this).find('i').hasClass('up');
                    if (order == true) {
                        order = "DESC";
                    } else {
                        order = "ASC";
                    }
                    self.searchCriteria.order=order
                    self.searchCriteria.sort = self.getSortFields(sort)
                    self.searchCriteria.offset = 0

                    try {
                        const data = await HomeValuationController.displayMarketValuationJobSearchResult(self.searchCriteria);
                        self.valuationJobs = self.generateValuationJobs(data);
                    } catch (error) {
                        console.error('Error updating valuation jobs:', error);
                    }
                });


                $(window).data('ajaxready', true).scroll(async function (e) {
                    var sticky = $('.searchbarWrapper'),
                        scroll = $(window).scrollTop(),
                        winHeight = $(window).height();
                    if (scroll >= 153) sticky.addClass('fixed');
                    else sticky.removeClass('fixed');
                    if ($(window).data('ajaxready') == false) return;
                    if (Math.ceil(scroll + winHeight) >= ($(document).height() - 130)) {
                        $(window).data('ajaxready', false);
                        if ($('.resultsRow:visible').length >= 25 && $('.resultsRow:visible').length < self.totalJobs) {
                            $('.loadingSpinnerSearchResults').show();
                            if ($('.resultsRow').length > 1) {
                                self.searchCriteria.offset = $('.resultsRow').length

                                try {
                                    const data = await HomeValuationController.displayMarketValuationJobSearchResult(self.searchCriteria);
                                    $('.loadingSpinnerSearchResults').hide();
                                    var newValuationJobs = self.generateValuationJobs(data)
                                    var newResultSet = self.valuationJobs.concat(newValuationJobs)
                                    self.valuationJobs = newResultSet;
                                    $(window).data('ajaxready', true);
                                } catch (error) {
                                    console.error('Error updating valuation jobs:', error);
                                }
                            }
                        }
                    }
                });
            }
        },
        destroyed: function () {
            EventBus.$off('display-content');
        },
        beforeDestroy: function () {
            EventBus.$off('display-content');
        }
    }
</script>

<script setup>
import { ref, computed, reactive, watch, defineProps, onMounted } from 'vue';
import { store } from '../../DataStore';
import { mapState } from 'vuex';
import Multiselect from 'vue-multiselect';
import moment from 'moment';
import ClassificationDropdown from '../common/form/ClassificationDropdown.vue';
import TerritorialAuthority from '../filters/TerritorialAuthority.vue';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import useValuerInfo from '@/composables/useValuerInfo';
const { users, usersLoaded, loadUsers } = useValuerInfo();
const props = defineProps([ 'taCodes', 'valuationJobs', 'exportResultsDisabled']);
const emit = defineEmits([ 'search', 'exportResults', 'display-valuation-jobs']);
const invalidTaCodesMessage = ref('Territorial Authority is required');
const taLabelStyle = reactive({
    color: '#fff'
});
const allValuersIds = ref([]);
const userId = computed(() => store.state.userData.userId);
const userName = computed(() => store.state.userData.userName);
const searchCriteria = computed(() => store.state.rollMaintenanceSearch.searchCriteria);
const valuersSelected = ref([]);
const valuersList = computed(() => users?.value ?? []);
const valuersLoading = computed(() => !usersLoaded.value);
const isTAUser = computed(() => !store.state.userData.isInternalUser && store.state.userData.isTAUser);
const isValid = computed(() => {
      return isValidRange('date', searchCriteria.value.valuationSearchCriteria.inspectionDateFrom, searchCriteria.value.valuationSearchCriteria.inspectionDateTo);
});
const emptyTACodes = computed(() => !taCodes.value || taCodes.value.length === 0 );
function isValidRange(type, from, to) {

    if (type === 'number') {
        if (from != null && to != null) {
            return result = (!Number.isNaN(from) && !Number.isNaN(to) && from <= to);
        }
    } else if (type === 'date') {
        if (from && typeof from === 'string') {
            from = moment(from, 'DD/MM/YYYY').toDate();
        }
        if (to && typeof to === 'string') {
            to = moment(to, 'DD/MM/YYYY').toDate();
        }

        if (from && to) {
            return (from instanceof Date && to instanceof Date && !isNaN(from) && !isNaN(to) && to >= from);
        }
        if (from) {
            return (from instanceof Date && !isNaN(from));
        }
        if (to) {
            return (to instanceof Date && !isNaN(to));
        }
    }
    return true;
}

function activeReportTypes() {
    const types = store.getters.getCategoryClassifications('ValuationReportType');
    updateValuationCriteriaItem({ id: 'valuationReportTypes', value: types });
}
function clearReportTypes() {
    updateValuationCriteriaItem({ id: 'valuationReportTypes', value: null });
}

function activeJobStatuses() {
    const statuses = store.getters.getCategoryClassifications('HomeValuationJobStatus');
    updateValuationCriteriaItem({ id: 'valuationJobStatuses', value: statuses });
}

function clearJobStatuses() {
    updateValuationCriteriaItem({ id: 'valuationJobStatuses', value: null });
}

function clearAllValuers(){
    valuersSelected.value = [];
    updateValuationCriteriaItem({ id: 'isUnassigned', value: null });
}

function updateValuationCriteriaItem(item){
    if (item.value === '') {
        data.value = null;
    }
    store.commit('rollMaintenanceSearch/setValuationCriteriaItem', item);
}

const taCodes = computed(() => isTAUser.value 
    ? [store.state.userData.userTACode].map(item => parseInt(item,10)) 
    : store.state.taCodes.taCodes.map(item => parseInt(item,10))
);

function clearSearchCriteria(){
    clearReportTypes();
    clearJobStatuses();
    updateValuationCriteriaItem({ id: 'inspectionDateFrom', value: null });
    updateValuationCriteriaItem({ id: 'inspectionDateTo', value: null });
    clearAllValuers();
}

function saveValuersSelectedToLocalStorage() {
    localStorage.setItem(userId.value + 'valuers-selected-in-valuation', JSON.stringify(valuersSelected.value));
    var selected = [];
    var selectedValuersLocal = [];
    var selectedValuerNames = [];
    valuersSelected.value.forEach(valuer => {
        selected.push(valuer.ntUsername);
        selectedValuersLocal.push(valuer.id);
        selectedValuerNames.push({ value: valuer.ntUsername, label: valuer.name, id: valuer.id });
    });
    const validSelectedValuers = selectedValuersLocal.filter(id => id && id !== null && id !== ''&& id !== "unAssigned");
    localStorage.setItem(userId.value + 'current-home-selection', JSON.stringify({ 'valuers': selected }));
    localStorage.setItem(userId.value + 'selected-valuers', JSON.stringify(validSelectedValuers));
    localStorage.setItem(userId.value + 'selected-valuers-names', JSON.stringify(selectedValuerNames));
}

function setupValuersComponent(){
    allValuersIds.value = valuersList.value.slice(1).map(valuer =>(valuer.id));
    const selectedValuersObjects = JSON.parse(localStorage.getItem(userId.value + 'selected-valuers-names'));
    const selectedValuersIds = JSON.parse(localStorage.getItem(userId.value + 'selected-valuers'));

    if (selectedValuersIds && selectedValuersIds.length > 0) {
        const eventObj = {};
        if(selectedValuersIds.length == valuersList.value.length){
            eventObj.valuers = allValuersIds.value;
            eventObj.isUnassigned = true;
        }
        else{
            const unassignedIndex = selectedValuersIds.indexOf('Unassigned')
            if (unassignedIndex > -1) {
                selectedValuersIds.splice(unassignedIndex, 1)
                eventObj.isUnassigned = true
            }
            eventObj.valuers = selectedValuersIds;
        }
        valuersSelected.value = selectedValuersObjects.map(valuer => ({
            id: valuer.id,
            name: valuer.label, 
            ntUsername: valuer.value
        }));
        emit('display-valuation-jobs', eventObj);
    } 
    else {
        var eventObj = {};
        eventObj.valuers = [];
        var isLoggedInUserAValuer = false;
        valuersList.value.some(valuer => {
            const auth0name = 'QVNZ\\' + userName.value;
            const currentauth0Name = valuer.ntUsername;
            if (auth0name === currentauth0Name) {
                eventObj.valuers = [valuer.id];
                isLoggedInUserAValuer = true;
                valuersSelected.value = [valuer];
                return true; // Breaks out of the loop
            }
            return false; // Continue the loop
        });

        emit('display-valuation-jobs', eventObj);
    }
}

watch(valuersSelected, (newValue, oldValue) => {
    let selectedIdsToSave = newValue.map(value => value.id);
    const unassignedIndexNew = selectedIdsToSave.indexOf('Unassigned')
    if (unassignedIndexNew > -1) {
        selectedIdsToSave.splice(unassignedIndexNew, 1);
        updateValuationCriteriaItem({ id: 'isUnassigned', value: true });
    }
    
    updateValuationCriteriaItem({ id: 'valuers', value: selectedIdsToSave });
    saveValuersSelectedToLocalStorage();
})

onMounted(async () => {
    await loadUsers();
    await setupValuersComponent();
});

</script>
<template>
        <div class="qv-search-background col-container">
            <div class="col-row">

                <div data-cy="territorial-authorities" class="col col-2">
                    <div class="qv-ta-search-container" v-if="!isTAUser">
                        <territorial-authority :validation-error="emptyTACodes" :customLabelStyle="taLabelStyle" />
                    </div>
                    <span data-cy="territorial-authorities-error-message" v-if="emptyTACodes" class="error-message">{{ invalidTaCodesMessage }}</span>
                </div>  

                <div data-cy="report-types" class="col col-3">
                    <div class="righty status-actions">
                        <a data-cy="report-types-all-active" @click="activeReportTypes">All Active</a> |
                        <a data-cy="report-types-clear" @click="clearReportTypes">Clear</a>
                    </div>
                    <label>
                        <span class="label qv-label-white">Report Type</span>
                        <classification-dropdown
                            v-if = "!isTAUser"
                            id="valuationReportTypes"
                            data-cy="report-types-dropdown"
                            type="text"
                            :value="searchCriteria.valuationSearchCriteria.valuationReportTypes"
                            category="ValuationReportType"
                            :taggable="true"
                            :multiple="true"
                            hide-codes
                            @input="updateValuationCriteriaItem"
                        />
                        <input v-else type="text" :value="searchCriteria.valuationSearchCriteria.valuationReportTypes?.[0]?.description" class="grey-out">
                    </label>
                </div>

                <div data-cy="job-status" class="col col-2">
                    <div class="righty status-actions">
                        <a data-cy="job-status-all-active" @click="activeJobStatuses">All Active</a> |
                        <a data-cy="job-status-clear" @click="clearJobStatuses">Clear</a>
                    </div>
                    <label>
                        <span class="label qv-label-white">Job Status</span>
                        <classification-dropdown
                            v-if = "!isTAUser"
                            id="valuationJobStatuses"
                            data-cy="job-status-dropdown"
                            type="text"
                            :value="searchCriteria.valuationSearchCriteria.valuationJobStatuses"
                            category="HomeValuationJobStatus"
                            :taggable="true"
                            :multiple="true"
                            hide-codes
                            @input="updateValuationCriteriaItem"
                        />
                        <input v-else type="text" :value="searchCriteria.valuationSearchCriteria.valuationJobStatuses?.[0]?.description" class="grey-out">
                    </label>
                </div>

                <div data-cy="valuation-valuers" class="col col-2">
                    <div class="righty status-actions">
                        <a data-cy="valuation-valuers-clear" @click="clearAllValuers">Clear</a>
                    </div>
                    <label>
                        <span class="label qv-label-white">Valuers</span>
                        <multiselect
                            v-model="valuersSelected"
                            data-cy="valuation-valuers-dropdown"
                            :options="valuersList"
                            :multiple="true"
                            :close-on-select="false"
                            :loading="valuersLoading"
                            label="name"
                            track-by="name"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                        >
                            <template slot="selection" slot-scope="{ values, search, isOpen }">
                                <span class="multiselect__single" v-if="values.length > 2 && !isOpen">
                                    {{ values.length }} options selected
                                </span>
                            </template>
                        </multiselect>
                    </label>

                </div>
               
                <div data-cy="inspection-date" class="col col-3" :class="{valError: !isValidRange('date',
                        searchCriteria.valuationSearchCriteria.inspectionDateFrom,
                        searchCriteria.valuationSearchCriteria.inspectionDateTo
                )}">

                    <label class="col col-5.5 sp-date-label">
                        <span class="label qv-label-white">Inspection Date</span>
                        <div class="input-date-wrapper">
                            <date-picker id="inspectionDateFrom" v-model="searchCriteria.valuationSearchCriteria.inspectionDateFrom" format="DD/MM/YYYY"
                                            type="date" value-type="DD/MM/YYYY"></date-picker>
                        </div>
                    </label>
                    <label class="col col-1">
                        <span class="label qv-label-white">&nbsp;</span>
                        <div class="qv-label-white" style="font-size: 1.1rem; padding-top: 10px;">to</div>
                    </label>
                    <label class="col col-5.5 sp-date-label">
                        <span class="label qv-label-white">&nbsp;</span>
                        <div class="input-date-wrapper">
                            <date-picker id="inspectionDateTo"  v-model="searchCriteria.valuationSearchCriteria.inspectionDateTo" format="DD/MM/YYYY"
                                            type="date" value-type="DD/MM/YYYY"></date-picker>
                        </div>
                    </label>
                    <div
                        class="valMessage"
                    >
                        <label data-cy="valuation-inspection-date-error-message">The To date must be equal to or greater than the From date.</label>
                    </div>
                </div>
            
            </div>
            
            <div class="col-row">
                <div class="righty search-control-buttons">                  
                    <div 
                        data-cy="export-button"
                        title="Export Results (limit 100,000)" 
                        class="exportResults mdl-button mdl-js-button mdl-button--icon" 
                        :class="{'disabled': valuationJobs.length === 0 || exportResultsDisabled }"
                        @click="emit('exportResults')"
                    >
                        <i class="material-icons md-dark">&#xE06F;</i>
                    </div>
                    <button
                        data-cy="clear-btn"
                        class="mdl-button mdl-js-button mdl-button--raised
                            mdl-js-ripple-effect advSearchClear
                        "
                        title="Clear Search Criteria"
                        @click="clearSearchCriteria"
                    >
                        Clear
                    </button>
                    <button
                        data-cy="search-btn"
                        class="mdl-button mdl-js-button mdl-button--raised
                        mdl-js-ripple-effect mdl-button--colored"
                        :class="{disabled: !isValid || emptyTACodes || valuersLoading}"
                        @click="emit('search')"
                    >
                        Search
                    </button>
                </div>
            </div> 
        </div>       
</template>

<style lang="scss" scoped>

@import '../rollMaintenance/rollMaintenance.scss';

.router .col {
    margin:0;
}

.search-control-buttons {
    line-height: 5.5;
    margin-bottom: -7.5em;
}

.sc-input {
    &--range {
        width: 45% !important;
    }
}

.status-actions {
    font-size: 1.1rem;
    color: rgb(252,147,47);
    a {
        color: rgb(252,147,47);
    }
}

.sc-input-text {
    color : #35495e;
}

.router .col-container {
    background: #162b3f;
}

.advSearch-group {
    margin-right: 0.4rem;
    margin-left: -.1rem;
}

</style>
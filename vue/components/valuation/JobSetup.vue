<template xmlns:v-validate="http://www.w3.org/1999/xhtml">
    <div>
        <div class="md-table mdl-shadow--2dp qv-block-table">
            <h2>Set-up Valuation Job</h2>
            <div v-if="hasValuationJobUpdateInfo" class="updateInformation">
                <ul>
                    <li>Job Created By: <span>{{createdBy}}</span></li>
                    <li>Created On: <span>{{createdAt}}</span></li>
                </ul>
            </div>
            <!-- CHANGE ADDRESS DETAILS STARTS -->
            <input type="checkbox" v-model="jobInstruction.changeSubjectAddress" id="Change Subject Address" class="changeAddress-trigger" v-bind:class="{disabled: readOnly}" @click="isChangeAddClicked = !isChangeAddClicked">
            <label for="Change Subject Address" class="changeAddress-trigger">Change Subject Address</label>

            <div class="QVHV-Container changeaddressdetails" v-bind:class="{disabled: readOnly}">
                <ul class="QVHV-tabs">
                    <li class="QVHVTab-1" data-tab="QVHVTab-1" data-container="housedetails"><span class="is-active">New Address</span></li>
                    <hr>
                </ul>
                <div class="QVHV-formSection">
                    <div class="advSearch-row">
                        <div class="advSearch-group twentyfivePct icons8-category-filled" :class="[fields.changeSubjectAddress.category]">
                            <label>Category</label>
                            <span><input id="jobSetupCagetoryOverride" maxlength="50" class="advSearch-text" type="text" v-model="jobInstruction.categoryOverride"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>
                    <div class="advSearch-row">
                        <div class="advSearch-group hundyPct icons8-mailbox-opened-flag-up-filled" :class="[fields.changeSubjectAddress.streetAddress]">
                            <label>Street Address</label>
                            <span><input id="jobSetupStreetAddressOverride" maxlength="250" class="advSearch-text" type="text" v-model="jobInstruction.streetAddressOverride"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>
                    <div class="advSearch-row">
                        <div class="advSearch-group hundyPct icons8-legal-description" :class="[fields.changeSubjectAddress.legalDescription]">
                            <label>Legal Description</label>
                            <span><input id="jobSetupLegalDescriptionOverride" maxlength="250" class="advSearch-text" type="text" v-model="jobInstruction.legalDescriptionOverride"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div data-cy="job-setup-expand-all" class="expandAll" @click="expandTabs()" v-bind:class="[tabState == 'open' ? 'down' : '']">
                <span title="Expand Form" class="mdl-button mdl-js-button mdl-button--icon"><i class="material-icons md-dark"></i></span>
            </div>
            <ul id="tabsElementJobSetup" v-bind:class="[tabState=='open' ? 'hide' : 'QVHV-tabs']">
                <!-- ^^ THE ELEMENT ".QVHV-tabs" SHOULD GET THE .hide CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->
                <li class="QVHVTab-1" @click="setCurrentTab('JobSetup', 1, true, true)" data-tab="QVHVTab-1" data-container="reportdetails"><span v-bind:class="[currentTab == 'JobSetup' || tabState == 'open' ? 'is-active' : '']">Report Details</span></li>
                <li class="QVHVTab-2" @click="setCurrentTab('ExtraDetails', 2, true, true)" data-tab="QVHVTab-2" data-container="extraPropertyDetails"><span v-bind:class="[currentTab == 'ExtraDetails' || tabState == 'open' ? 'is-active' : '']">Extra Details</span></li>
                <hr align="left"/>
            </ul>

            <!-- JOB SET-UP STARTS -->
            <div class="QVHV-Container reportdetails active" v-show="currentTab=='JobSetup' || tabState == 'open'" v-bind:class="[tabState=='open' ? 'canOpener' : '', readOnly ? 'disabled' : '']">
                <ul class="QVHV-tabs">
                    <li><span class="is-active">Report Details</span></li>
                    <hr align="left"/>
                </ul>
                <div class="QVHV-formSection">

                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                                :isSetupComplete="setupComplete"
                                id="jobSetupReportDetailsReportType" :curr-val="reportType"
                                iconClass="twentyfivePct icons8-news-filled"
                                component-name="job-setup"
                                attrName="reportType"
                                filter-id="reportType"
                                label="* Valuation Report Type"
                                selectClass="monarch-multiselect-report-type"
                                chooseHere="true"
                                data-to-fetch="ValuationReportType"
                                :errorMessage="error.reportType.errorMessage"
                                v-if="showMultiSelects"
                                :on-dropdown-hide="updateReportTypeRelatedFields"
                                :class="[fields.reportDetails.valuationReportType]">
                        </valuation-multi-select-filter>
                        <valuation-multi-select-filter
                                id="jobSetupReportDetailsInspectionType"
                                :curr-val="jobInstruction.inspectionType"
                                iconClass="twentyfivePct icons8-happy"
                                component-name="job-setup"
                                attrName="inspectionType"
                                filter-id="inspectionType"
                                label="Inspection Type"
                                selectClass="monarch-multiselect-marketing-lead"
                                chooseHere="true"
                                data-to-fetch="InspectionType"
                                v-if="showMultiSelects"
                                :class="[fields.reportDetails.inspectionType]">
                        </valuation-multi-select-filter>
                        <valuation-multi-select-filter
                                id="jobSetupReportDetailsPropertyType"
                                :curr-val="jobInstruction.propertyType"
                                iconClass="fiftyPct icons8-land-use-new"
                                component-name="job-setup"
                                attrName="propertyType"
                                filter-id="propertyType"
                                label="Type of Property"
                                selectClass="monarch-multiselect-property-type"
                                chooseHere="true"
                                data-to-fetch="PropertyType"
                                v-if="showMultiSelects"
                                :class="[fields.reportDetails.propertyType]">
                        </valuation-multi-select-filter>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <date-time-picker rangeId="valuationDueDateTime"
                              label="Valuation Due Date and Time"
                              class="advSearch-group fiftyPct icons8-calendar-filled"
                              :class="[fields.reportDetails.valuationDueDate]"
                              component-name="job-setup-date-time"
                              attr-name="valuationDueDate"
                              :value="valuationDueDate">
                        </date-time-picker>
                        <date-time-picker rangeId="inspectionDateTime"
                              label="* Inspection Date and Time"
                              class="advSearch-group fiftyPct icons8-calendar-filled"
                              :class="[fields.reportDetails.inspectionDate, error.inspectionDate.hasError ? 'valError' : '']"
                              component-name="job-setup-date-time"
                              attr-name="inspectionDate"
                              :value="inspectionDate"
                              :has-error="error.inspectionDate.hasError"
                              :error-message="error.inspectionDate.errorMessage">
                        </date-time-picker>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <div class="advSearch-group fiftyPct icons8-contacts cnGroup" :class="[fields.reportDetails.clientName]">
                            <label>* Client Name</label>
                            <span><input id="jobSetupReportDetailsClientName" maxlength="300" class="advSearch-text" type="text" v-model="jobInstruction.clientName"></span>
                            <div class="valMessage cnValMessage"></div>
                        </div>
                        <div class="advSearch-group twentyfivePct icons8-contacts" :class="[fields.reportDetails.clientReference]">
                            <label>Client Reference</label>
                            <span><input id="jobsetupReportDetailsClientReference" maxlength="20" class="advSearch-text" type="text" v-model="clientReference"></span>
                            <div class="valMessage"></div>
                        </div>
                        <div class="advSearch-group twentyfivePct">
                            <input type="checkbox" id="jobSetupReportDetailsIsSundryClient" class="sundry-client"
                                v-model="jobInstruction.isSundryClient" @change="checkIsSundryClient()"
                                style="width: 10px; height: 10px;">
                            <label for="jobSetupReportDetailsIsSundryClient" class="no-icon"><span>Sundry Client</span></label>
                        </div>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row" v-if="reportType.code == 'LDVA' || reportType.code == 'LDVNHI'">
                        <valuation-multi-select-filter
                                :isSetupComplete="setupComplete"
                                id="jobSetupReportDetailsInsuranceCompany" :curr-val="eqcFields.insuranceCompany"
                                iconClass="fiftyPct icons8-museum-filled"
                                component-name="job-setup"
                                attrName="eqcFields.insuranceCompany"
                                filter-id="insuranceCompany"
                                label="Insurance Company"
                                selectClass="monarch-multiselect-insurance-company"
                                chooseHere="true"
                                data-to-fetch="ValuationInsuranceCompany"
                                v-if="showMultiSelects"
                                sort="SORT_ORDER"
                                :class="[fields.reportDetails.insuranceCompany]">
                        </valuation-multi-select-filter>
                        <valuation-multi-select-filter
                                :isSetupComplete="setupComplete"
                                id="jobSetupReportDetailsManagementCompany" :curr-val="eqcFields.managementCompany"
                                iconClass="fiftyPct icons8-museum-filled"
                                component-name="job-setup"
                                attrName="eqcFields.managementCompany"
                                filter-id="managementCompany"
                                label="Management Company"
                                selectClass="monarch-multiselect-management-company"
                                chooseHere="true"
                                data-to-fetch="ValuationManagementCompany"
                                v-if="showMultiSelects"
                                sort="SORT_ORDER"
                                :class="[fields.reportDetails.managementCompany]">
                        </valuation-multi-select-filter>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row" v-if="reportType.code == 'LDVA' || reportType.code == 'LDVNHI'">
                        <valuation-multi-select-filter
                                :isSetupComplete="setupComplete"
                                id="jobSetupReportDetailsEngineerName" :curr-val="eqcFields.engineerName"
                                iconClass="fiftyPct icons8-museum-filled"
                                component-name="job-setup"
                                attrName="eqcFields.engineerName"
                                filter-id="engineerName"
                                label="Engineer Name"
                                selectClass="monarch-multiselect-engineer-name"
                                chooseHere="true"
                                data-to-fetch="ValuationEngineerName"
                                v-if="showMultiSelects"
                                sort="SORT_ORDER"
                                :class="[fields.reportDetails.engineerName]">
                        </valuation-multi-select-filter>

                        <div class="advSearch-group twentyfivePct icons8-calendar-filled">
                            <label>Date of Engineers Report</label>
                            <date-picker
                                v-model="eqcFields.dateOfEngineersReport"
                                type="date"
                                format="DD/MM/YYYY"
                                value-type="DD/MM/YYYY">
                            </date-picker>
                        </div>

                        <valuation-multi-select-filter
                                :isSetupComplete="setupComplete"
                                id="jobSetupReportDetailsPropertyHasRetainingWalls" :curr-val="eqcFields.propertyHasRetainingWalls"
                                iconClass="twentyfivePct icons8-brick-wall-filled"
                                component-name="job-setup"
                                attrName="eqcFields.propertyHasRetainingWalls"
                                filter-id="propertyHasRetainingWalls"
                                label="Property has retaining wall/s"
                                selectClass="monarch-multiselect-property-has-retaining-walls"
                                chooseHere="true"
                                data-to-fetch="ValuationPropertyHasRetainingWalls"
                                v-if="showMultiSelects"
                                sort="SORT_ORDER"
                                :class="[fields.reportDetails.propertyHasRetainingWalls]">
                        </valuation-multi-select-filter>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <div class="advSearch-group hundyPct icons8-mailbox-opened-flag-up-filled" :class="[fields.reportDetails.postalAddress]">
                            <label>Postal Address</label>
                            <span><input id="jobSetupReportDetailsPostalAddress" maxlength="100" class="advSearch-text" type="text" v-model="jobInstruction.postalAddress"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <div class="advSearch-group twentyfivePct icons8-email emailGroup" :class="[fields.reportDetails.emailAddress]">
                            <label>Email Address</label>
                            <span><input id="jobSetupReportDetailsEmailAddress" maxlength="50" class="advSearch-text advSearch-validation" type="text" v-model="jobInstruction.emailAddress" data-type="email" data-group="emailGroup" data-error="emailValMessage"></span>
                            <div class="valMessage emailValMessage"></div>
                        </div>
                        <div class="advSearch-group twentyfivePct icons8-shake-phone phoneGroup" :class="[fields.reportDetails.mobileNumber]">
                            <label>Mobile Number</label>
                            <span><input id="jobSetupReportDetailsMobileNumber" maxlength="20" class="advSearch-text" type="text" v-model="jobInstruction.mobilePhone"></span>
                            <div class="valMessage"></div>
                        </div>
                        <div class="advSearch-group twentyfivePct icons8-phone-daytime-filled phoneGroup" :class="[fields.reportDetails.daytimePhone]">
                            <label>Daytime Phone</label>
                            <span><input id="jobSetupReportDetailsDaytimePhone" maxlength="20" class="advSearch-text" type="text" v-model="jobInstruction.daytimePhone"></span>
                            <div class="valMessage"></div>
                        </div>
                        <div class="advSearch-group twentyfivePct icons8-phone-night-filled phoneGroup" :class="[fields.reportDetails.eveningPhone]">
                            <label>Evening Phone</label>
                            <span><input id="jobSetupReportDetailsEveningPhone" maxlength="20" class="advSearch-text" type="text" v-model="jobInstruction.eveningPhone"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <div class="advSearch-group hundyPct icons8-museum-filled" :class="[fields.reportDetails.lenderName]">
                            <label>Lender Name</label>
                            <span><input id="jobSetupReportDetailsLenderName" maxlength="200" class="advSearch-text" type="text" v-model="jobInstruction.lenderName"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <div class="advSearch-group hundyPct icons8-mailbox-opened-flag-up-filled laGroup" :class="[fields.reportDetails.lenderAddress]">
                            <label>Lender Address</label>
                            <span><input id="jobSetupReportDetailsLenderAddress" maxlength="300" class="advSearch-text" type="text" v-model="jobInstruction.lenderAddress"></span>
                            <div class="valMessage laValMessage"></div>
                        </div>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <div class="advSearch-group fiftyPct icons8-contacts ibGroup" :class="[fields.reportDetails.instructedBy]">
                            <label v-if="reportType.code=='LDVA' || reportType.code == 'LDVNHI'">Instructed By</label>
                            <label v-else>* Instructed By</label>
                            <span><input id="jobSetupReportDetailsInstructedBy" maxlength="50" class="advSearch-text" type="text" v-model="jobInstruction.instructedBy"></span>
                            <div class="valMessage ibValMessage"></div>
                        </div>
                        <div class="advSearch-group twentyfivePct icons8-contacts" :class="[fields.reportDetails.borrower]">
                            <label>Borrower</label>
                            <span><input maxlength="50" id="jobSetupReportDetailsBorrower" class="advSearch-text" type="text" v-model="jobInstruction.borrower"></span>
                            <div class="valMessage"></div>
                        </div>
                        <div class="advSearch-group twentyfivePct icons8-contacts" :class="[fields.reportDetails.websiteUserId]">
                            <label>Website User ID</label>
                            <span><input id="jobsetupReportDetailsWebsiteUserId" maxlength="20" class="advSearch-text" type="text" v-model="websiteUserId"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <div class="advSearch-group hundyPct icons8-contacts" :class="[fields.reportDetails.extendedTo]">
                            <label>Extended To</label>
                            <span><input id="jobSetupReportDetailsExtendedTo" maxlength="300" class="advSearch-text" type="text" v-model="jobInstruction.extendedTo"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>

                    <!-- NEW ROW STARTS -->
                    <div class="advSearch-row">
                        <div class="advSearch-group hundyPct icons8-pencil" :class="[fields.reportDetails.otherInstructions]">
                            <label>Other Instructions</label>
                            <span><input id="jobSetupReportDetailsOtherInstructions" maxlength="2000" class="advSearch-text" type="text" v-model="jobInstruction.otherInstructions"></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>

                    <div class="advSearch-row">
                        <div class="advSearch-group hundyPct icons8-pencil" :class="[fields.reportDetails.siteInspectionNotes]">
                            <label>Site Inspection Notes</label>
                            <span><textarea id="jobSetupReportDetailsSiteInspectionNotes" maxlength="2000" class="advSearch-text" v-model="jobInstruction.siteInspectionNotes"></textarea></span>
                            <div class="valMessage"></div>
                        </div>
                    </div>

                    <div class="advSearch-row">
                        <user-multi-select
                                v-model="valuer"
                                url="/displayValuers"
                                iconClass="thirtythreePct icons8-edit-user-male jsValuerGroup"
                                title="* Valuer"
                                selectClass="monarch-multiselect-job-setup-valuer"
                                :hasError="error.valuer.hasError"
                                :errorMessage="error.valuer.errorMessage"
                                filter-id="Valuers"
                                :class="[fields.reportDetails.valuer]">
                        </user-multi-select>
                        <user-multi-select
                                v-model="countersigner"
                                url="/displayCountersigners"
                                iconClass="thirtythreePct icons8-checked-user-male"
                                title="Countersigned By"
                                selectClass="monarch-multiselect-job-setup-countersigner"
                                filter-id="CounterSigners"
                                :class="[fields.reportDetails.countersignedBy]">
                        </user-multi-select>
                        <valuation-multi-select-filter
                                id="jobSetupReportDetailsPurposeOfValuation" :curr-val="purpose"
                                iconClass="thirtythreePct icons8-ask-question"
                                component-name="job-setup"
                                attrName="purpose"
                                filter-id="purpose"
                                label="* Purpose of Valuation"
                                selectClass="monarch-multiselect-purpose"
                                chooseHere="true"
                                data-to-fetch="ValuationPurpose"
                                :errorMessage="error.purpose.errorMessage"
                                v-if="showMultiSelects"
                                :class="[fields.reportDetails.valuationPurpose]">
                        </valuation-multi-select-filter>
                    </div>

                    <div class="advSearch-row">
                        <office-multi-select
                                v-model="qvOffice"
                                url="/displayOffices"
                                iconClass="thirtythreePct icons8-qv-logo-new"
                                title="QV Office"
                                selectClass="monarch-multiselect-job-setup-qvoffice"
                                filter-id="QvOffice"
                                :class="[fields.reportDetails.qvOffice]">
                        </office-multi-select>
                        <valuation-multi-select-filter
                                id="jobSetupReportDetailsPeerReview" :curr-val="peerReview"
                                iconClass="fourtyPct icons8-contacts"
                                component-name="job-setup"
                                attrName="peerReview"
                                filter-id="peerReview"
                                label="* Peer Review"
                                selectClass="monarch-multiselect-peer-review"
                                chooseHere="true"
                                data-to-fetch="PeerReviewSetup"
                                :errorMessage="error.peerReview.errorMessage"
                                v-if="showMultiSelects"
                                :class="[fields.reportDetails.peerReview]"
                                sort="SORT_ORDER">
                        </valuation-multi-select-filter>
                    </div>
                </div>
            </div>
            <div class="QVHV-Container extraPropertyDetails active" v-show="currentTab=='ExtraDetails' || tabState == 'open'" v-bind:class="[tabState=='open' ? 'canOpener' : '', (jobStatus && (jobStatus != 'S')) || readOnly ? 'disabled' : '']">
                <!-- ^^ THE ELEMENT ".QVHV-Container" SHOULD GET THE .canOpener CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->
                <ul class="QVHV-tabs">
                    <li><span class="is-active">Extra Details</span></li>
                    <hr align="left"/>
                </ul>
                <div class="QVHV-formSection">
                    <div class="advSearch-row">
                        <text-input :class="[fields.extraDetails.numberOfSingleBedrooms]" maxlength="2" id="jobSetupExtraDetailsNumberOfSingleBedrooms" :curr-val="extraPropertyDetails.numberOfSingleBedrooms" attr-name="numberOfSingleBedrooms" fieldType="number" iconClass="icons8-bed-filled" label="Single Bedrooms" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                        <text-input :class="[fields.extraDetails.numberOfDoubleBedrooms]" maxlength="2" id="jobSetupExtraDetailsNumberOfDoubleBedrooms" :curr-val="extraPropertyDetails.numberOfDoubleBedrooms" attr-name="numberOfDoubleBedrooms" fieldType="number" iconClass="icons8-bed-filled" label="Double Bedrooms" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                        <text-input :class="[fields.extraDetails.numberOfHomeOfficeOrStudies]" maxlength="2" id="jobSetupExtraDetailsNumberOfHomeOfficesOrStudies" :curr-val="extraPropertyDetails.numberOfHomeOfficesOrStudies" attr-name="numberOfHomeOfficesOrStudies" fieldType="number" iconClass="icons8-pc-on-desk-filled" label="Home Office or Study" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                        <text-input :class="[fields.extraDetails.numberOfLivingAreas]" maxlength="2" id="jobSetupExtraDetailsNumberOfLivingAreass" :curr-val="extraPropertyDetails.numberOfLivingAreas" attr-name="numberOfLivingAreas" fieldType="number" iconClass="icons8-living-areas-new" label="Living Areas" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                        <text-input :class="[fields.extraDetails.garaging]" maxlength="2" id="jobSetupExtraDetailsGaraging" :curr-val="extraPropertyDetails.garaging" attr-name="garaging" fieldType="number" iconClass="icons8-garage-filled" label="Garaging" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                        <text-input :class="[fields.extraDetails.offstreetParking]" maxlength="2" id="jobSetupExtraDetailsOffStreetParking" :curr-val="extraPropertyDetails.offstreetParking" attr-name="offstreetParking" fieldType="number" iconClass="icons8-traffic-jam-filled" label="Offstreet Parking" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                    </div>
                    <div class="advSearch-row">
                        <text-input :class="[fields.extraDetails.numberOfBathrooms]" maxlength="2" id="jobSetupExtraDetailsNumberOfBathrooms" :curr-val="extraPropertyDetails.numberOfBathrooms" attr-name="numberOfBathrooms" fieldType="number" iconClass="icons8-total-bathrooms-filled" label="Bathrooms" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                        <text-input :class="[fields.extraDetails.numberOfToilets]" maxlength="2" id="jobSetupExtraDetailsNumberOfToilets" :curr-val="extraPropertyDetails.numberOfToilets" attr-name="numberOfToilets" fieldType="number" iconClass="icons8-toilet-bowl-filled" label="Toilets" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter :class="[fields.extraDetails.mainBathroomAge]" id="jobSetupExtraDetailsMainBathroomAge" :curr-val="extraPropertyDetails.mainBathroomAge" iconClass="twentyfivePct icons8-shower-and-tub-filled" component-name="extraPropertyDetails" attrName="mainBathroomAge" filter-id="propertyPlusmainBathroomAge" label="Main Bathroom Age" selectClass="monarch-multiselect propertyPlusmainBathroomAge" request-type="POST" data-to-fetch="MainBathroomAge" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.mainBathroomQuality]" id="jobSetupExtraDetailsMainBathroomQuality" :curr-val="extraPropertyDetails.mainBathroomQuality" iconClass="twentyfivePct icons8-rating-filled" component-name="extraPropertyDetails" attrName="mainBathroomQuality" filter-id="propertyPlusmainBathroomQuality" label="Main Bathroom Quality" selectClass="monarch-multiselect propertyPlusmainBathroomQuality" data-to-fetch="MainBathroomQuality" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.ensuiteAge]" id="jobSetupExtraDetailsEnsuiteAge" :curr-val="extraPropertyDetails.ensuiteAge" iconClass="twentyfivePct icons8-shower-and-tub-filled" component-name="extraPropertyDetails" attrName="ensuiteAge" filter-id="propertyPlusensuiteAge" label="Ensuite Age" selectClass="monarch-multiselect propertyPlusensuiteAge" request-type="POST" data-to-fetch="EnsuiteAge" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.ensuiteQuality]" id="jobSetupExtraDetailsEnsuiteQuality" :curr-val="extraPropertyDetails.ensuiteQuality" iconClass="twentyfivePct icons8-rating-filled" component-name="extraPropertyDetails" attrName="ensuiteQuality" filter-id="propertyPlusensuiteQuality" label="Ensuite Quality" selectClass="monarch-multiselect propertyPlusensuiteQuality" data-to-fetch="EnsuiteQuality" v-if="showMultiSelects"></valuation-multi-select-filter>
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter :class="[fields.extraDetails.kitchenAge]" id="jobSetupExtraDetailsKitchenAge" :curr-val="extraPropertyDetails.kitchenAge" iconClass="twentyfivePct icons8-fridge-filled" component-name="extraPropertyDetails" attrName="kitchenAge" filter-id="propertyPluskitchenAge" label="Kitchen Age" selectClass="monarch-multiselect propertyPluskitchenAge" request-type="POST" data-to-fetch="KitchenAge" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.kitchenQuality]" id="jobSetupExtraDetailsKitchenQuality" :curr-val="extraPropertyDetails.kitchenQuality" iconClass="twentyfivePct icons8-rating-filled" component-name="extraPropertyDetails" attrName="kitchenQuality" filter-id="propertyPluskitchenQuality" label="Kitchen Quality" selectClass="monarch-multiselect propertyPluskitchenQuality" data-to-fetch="KitchenQuality" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.redecorationAge]" id="jobSetupExtraDetailsRedecorationAge" :curr-val="extraPropertyDetails.redecorationAge" iconClass="twentyfivePct icons8-roller-brush-filled" component-name="extraPropertyDetails" attrName="redecorationAge" filter-id="propertyPlusredecorationAge" label="Redecoration Age" selectClass="monarch-multiselect propertyPlusredecorationAge" request-type="POST" data-to-fetch="RedecorationAge" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.internalCondition]" id="jobSetupExtraDetailsInteriorPresentation" :curr-val="extraPropertyDetails.interiorPresentation" iconClass="twentyfivePct icons8-rating-filled" component-name="extraPropertyDetails" attrName="interiorPresentation" filter-id="propertyPlusinteriorPresentation" label="Internal Condition" selectClass="monarch-multiselect propertyPlusinteriorPresentation" data-to-fetch="InternalCondition" v-if="showMultiSelects"></valuation-multi-select-filter>
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter :class="[fields.extraDetails.heatingType]" id="jobSetupExtraDetailsHeatingType" :curr-val="extraPropertyDetails.heatingType" iconClass="twentyfivePct icons8-fire-station-filled" component-name="extraPropertyDetails" attrName="heatingType" filter-id="propertyPlusheatingType" label="Heating Type" selectClass="monarch-multiselect propertyPlusheatingType" multiple="true" data-to-fetch="HeatingType" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.insulation]" id="jobSetupExtraDetailsInsulation" :curr-val="extraPropertyDetails.insulation" iconClass="twentyfivePct icons8-heating-room-filled" component-name="extraPropertyDetails" attrName="insulation" filter-id="propertyPlusinsulation" label="Insulation" selectClass="monarch-multiselect propertyPlusinsulation" multiple="true" data-to-fetch="Insulation" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.plumbingAge]" id="jobSetupExtraDetailsPlumbingAge" :curr-val="extraPropertyDetails.plumbingAge" iconClass="twentyfivePct  icons8-piping-filled" component-name="extraPropertyDetails" attrName="plumbingAge" filter-id="propertyPlusplumbingAge" label="Plumbing Age" selectClass="monarch-multiselect propertyPlusplumbingAge" request-type="POST" data-to-fetch="PlumbingAge" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.wiringAge]" id="jobSetupExtraDetailsWiringAge" :curr-val="extraPropertyDetails.wiringAge" iconClass="twentyfivePct icons8-plug-4-filled" component-name="extraPropertyDetails" attrName="wiringAge" filter-id="propertyPluswiringAge" label="Wiring Age" selectClass="monarch-multiselect propertyPluswiringAge" request-type="POST" data-to-fetch="WiringAge" v-if="showMultiSelects"></valuation-multi-select-filter>
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter :class="[fields.extraDetails.doubleGlazing]" id="jobSetupExtraDetailsDoubleGlazing" :curr-val="extraPropertyDetails.doubleGlazing" iconClass="twentyfivePct icons8-closed-window-filled" component-name="extraPropertyDetails" attrName="doubleGlazing" filter-id="propertyPlusdoubleGlazing" label="Double Glazing" selectClass="monarch-multiselect propertyPlusdoubleGlazing" data-to-fetch="DoubleGlazing" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.alternativeEnergy]" id="jobSetupExtraDetailsAlternativeEnergy" :curr-val="extraPropertyDetails.alternativeEnergy" iconClass="twentyfivePct icons8-solar-panel-filled" component-name="extraPropertyDetails" attrName="alternativeEnergy" filter-id="propertyPlusalternativeEnergy" label="Alternative Energy" selectClass="monarch-multiselect propertyPlusalternativeEnergy" multiple="true" data-to-fetch="AlternativeEnergy" v-if="showMultiSelects"></valuation-multi-select-filter>
                        <valuation-multi-select-filter :class="[fields.extraDetails.ventilation]" id="jobSetupExtraDetailsVentilation" :curr-val="extraPropertyDetails.ventilation" iconClass="twentyfivePct icons8-air-conditioner-filled" component-name="extraPropertyDetails" attrName="ventilation" filter-id="propertyPlusventilation" label="Ventilation" selectClass="monarch-multiselect propertyPlusventilation" data-to-fetch="Ventilation" v-if="showMultiSelects"></valuation-multi-select-filter>
                    </div>
                    <div class="advSearch-row">
                        <text-input :class="[fields.extraDetails.weeklyRentalIncome]" maxlength="8" id="jobSetupExtraDetailsRentalIncomePerWeek" :curr-val="extraPropertyDetails.weeklyRentalIncome" attr-name="weeklyRentalIncome" fieldType="number" iconClass="fiftyPct icons8-sell-property-filled" label="Rental Income ($ Per Week)" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                        <text-input :class="[fields.extraDetails.rentalIncomeKnownDate]" maxlength="7" id="jobSetupExtraDetailsRentalIncomeKnownDate" :curr-val="extraPropertyDetails.rentalIncomeKnownDate" attr-name="rentalIncomeKnownDate" fieldType="date" iconClass="fiftyPct icons8-calendar-filled" label="Rental Income Known (MM/YYYY)" format="MM/YYYY" component-name="extraPropertyDetails" v-if="showMultiSelects"></text-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="QVHV-buttons" v-bind:class="{disabled: readOnly}">
            <div class="QVHV-buttons-left jobSetupSave" @click="saveJob(false,true,false)">
                <button class="primary">Save</button>
            </div>
            <div class="QVHV-buttons-right jobSetupCompleteSetup" v-bind:class="[jobStatus && (jobStatus != 'S') ? 'disabled' : '']" @click="saveJob(true,true,false)">
                <button class="secondary">Complete Setup</button>
            </div>
        </div>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import deepEqual from 'deep-equal';
    import TextInput from '../filters/TextInput.vue';
    import ValuationMultiSelectFilter from '../filters/ValuationMultiSelectFilter.vue';
    import UserMultiSelect from '../filters/UserProfileMultiSelect.vue';
    import OfficeMultiSelect from '../filters/OfficeMultiSelect.vue';
    import DateTimePicker from '../filters/DateTimePicker.vue';
    import moment from 'moment';
    import { mapState } from 'vuex';
    import DatePicker from 'vue2-datepicker';
    import 'vue2-datepicker/index.css';
    import usePropertyInfo from '../../composables/usePropertyInfo.js';

    const { getPropertyInfo } = usePropertyInfo();

    export default {
        props: ['readOnly', 'propertyQupid'],
        components: {
            TextInput,
            ValuationMultiSelectFilter,
            UserMultiSelect,
            OfficeMultiSelect,
            DateTimePicker,
            DatePicker,
        },
        data: function() {
            return {
                propertyInfo: {},
                propertyInfoLoaded: false,
                isTabClicked: false,
                jobInstruction: {
                    clientName: "",
                    borrower: "",
                    postalAddress: "",
                    emailAddress: "",
                    mobilePhone: "",
                    daytimePhone: "",
                    eveningPhone: "",
                    lenderName: "",
                    lenderAddress: "",
                    instructedBy: "",
                    extendedTo: "",
                    otherInstructions: "",
                    inspectionType: {},
                    propertyType:{},
                    categoryOverride: "",
                    streetAddressOverride: "",
                    legalDescriptionOverride: "",
                    changeSubjectAddress: false,
                    isSundryClient: false
                },
                isNewValuation: false,
                clientReference: '',
                websiteUserId: '',
                reportType: { code: "" },
                purpose: { code: "" },
                valuer: { id: "" },
                countersigner: { id: "" },
                peerReview: { code: "" },
                qvOffice: { id: "" },
                eqcFields:{
                    insuranceCompany: { code: "" },
                    managementCompany: { code: "" },
                    engineerName: {code: "" },
                    dateOfEngineersReport: null,
                    propertyHasRetainingWalls: { code: ""},
                },
                valuationDueDate: null,
                inspectionDate: null,
                propertySummary: null,
                reportBrand:'QV',
                extraPropertyDetails: {},
                propertyDetails:{
                    houseDetails: {},
                    kitchenDetails: {},
                    bedroomDetails: {},
                    bathroomDetails: {},
                    interiorDetails: {}
                },
                showMultiSelects: true,
                refreshMultiSelects : false,
                currentTab: 'JobSetup',
                tabState: 'closed',
                defaultPropertyData: undefined,
                jobStatus: undefined,
                error: {
                    valuer: {
                        hasError: false,
                        errorMessage: ""
                    },
                    purpose: {
                        errorMessage: ''
                    },
                    reportType: {
                        errorMessage: ''
                    },
                    inspectionDate: {
                        hasError: false,
                        errorMessage: ""
                    },
                    peerReview: {
                        errorMessage: ''
                    },
                },
                isChangeAddClicked: false,
                homeValuation: {},
                hnzTenantToBuyExtendedTo: "Genworth Financial & QBE Lenders' Mortgage insurance Ltd",
                hnzClientName: "Housing New Zealand Corporation",
                hnzInstructedBy: "Housing New Zealand Corporation",
                homeValuationCopy: null,
                isDirty: false,
                fields: {
                    reportDetails : {},
                    extraDetails: {},
                    changeSubjectAddress: {}
                },
                valuationJobUpdateInfo: null,
            }
        },
        computed: {

            ...mapState('userData', [
                'userName',
            ]),

            hasValuationJobUpdateInfo(){
                if(this.valuationJobUpdateInfo){
                    return true;
                }
                else {
                    return false;
                }
            },

            createdBy: function() {
                if(this.hasValuationJobUpdateInfo &&  this.$store.state.users){
                    let users = this.$store.state.users;
                    const match = users.filter(user => user.ntUsername === this.valuationJobUpdateInfo.createdBy);
                    if(match && match.length > 0) {
                        return match[0].name;
                    }
                }
            },

            createdAt: function() {
                if(this.hasValuationJobUpdateInfo){
                    return this.formatDate(this.valuationJobUpdateInfo.createdAt);
                }
            },

            setupComplete: function() {
                let self = this;
                if(self.homeValuation){
                    if(self.homeValuation.status && self.homeValuation.status.code !== 'S'){
                        return true;
                    }
                    else {
                        return false;
                    }
                }
            },

            rentalIncomeKnownDate: function(){
                var self = this;
                var rdd = null;
                if(self.extraPropertyDetails && self.extraPropertyDetails.rentalIncomeKnownDate) {
                    var rDate = self.extraPropertyDetails.rentalIncomeKnownDate;
                    var momentDate = moment.utc(rDate, 'MM/YYYY');
                    if(momentDate.isValid()) {
                        rdd = momentDate.toDate();
                    }
                }
                return rdd;
            },
        },

        watch: {
            setupComplete: function() {
                let self = this;
                if(self.setupComplete){
                    self.refreshMultiSelects = true;
                }
            }
        },
        methods: {

            formatDate(date){
                const splitDate = date.split("-");
                return splitDate[2] + '/' + splitDate[1] + '/' + splitDate[0];
            },
            populateDefaultData: function(data) {
                const self = this

                if (!self.extraPropertyDetails.numberOfToilets) {
                    self.extraPropertyDetails.numberOfToilets = $.isNumeric(data.toilets)?data.toilets:null;
                }

                if (!self.extraPropertyDetails.garaging) {
                    if(($.isNumeric(data.freeStandingGarages) && $.isNumeric(data.underMainRoofGarages))) {
                        self.extraPropertyDetails.garaging = (data.freeStandingGarages+data.underMainRoofGarages);
                    } else if($.isNumeric(data.freeStandingGarages)) {
                        self.extraPropertyDetails.garaging = data.freeStandingGarages;
                    }else if($.isNumeric(data.underMainRoofGarages)){
                        self.extraPropertyDetails.garaging = data.underMainRoofGarages;
                    }
                }

                if (!self.extraPropertyDetails.offstreetParking) {
                    self.extraPropertyDetails.offstreetParking = $.isNumeric(data.carParks)?data.carParks:null;
                }

                if (!self.jobInstruction.streetAddressOverride) {
                    // Need to strip TA name from address
                    self.jobInstruction.streetAddressOverride = data.address1 + " " + data.address2.substring(0, data.address2.lastIndexOf(','));
                }
            },
            expandTabs: function() {
                const self = this
                if (self.tabState == 'closed') {
                    self.tabState = 'open'
                }
                else {
                    self.tabState = 'closed'
                }
            },
            setCurrentTab: function(name, index, saveJob, isTabClicked){
                const self = this;
                self.currentTab = name;
                $('#tabsElementJobSetup').find('hr')
                    .removeClass()
                    .addClass('QVHVTab-'+ index );
                self.isTabClicked = isTabClicked
                if (saveJob) {
                    if(self.currentTab == 'ExtraDetails' && (self.homeValuation && self.homeValuation.id)) {
                        setTimeout(function() {
                            self.saveJob(false, true, true);
                        }, 1000);
                    }
                }
            },
            enableButtons: function() {
                const self = this
                setTimeout(function() {
                    $('.jobSetupSave').removeClass('disabled');
                    if (!self.jobStatus || (self.jobStatus && self.jobStatus == 'S')) {
                        $('.jobSetupCompleteSetup').removeClass('disabled');
                    }
                }, 2000);
            },
            disableButtons: function() {
                $('.jobSetupSave').addClass('disabled');
                $('.jobSetupCompleteSetup').addClass('disabled');
            },
            beforeSaving: function() {
                var self = this;
                var homeValuation = {};
                homeValuation.jobInstruction = self.jobInstruction;
                homeValuation.valuationDueDate = self.valuationDueDate;
                homeValuation.inspectionDate = self.inspectionDate;
                homeValuation.purpose = self.purpose;
                homeValuation.reportType = self.reportType;
                homeValuation.valuer = self.valuer;
                homeValuation.countersigner = self.countersigner;
                homeValuation.qvOffice = self.qvOffice;
                homeValuation.peerReview = self.peerReview;
                homeValuation.clientReference = self.clientReference;
                homeValuation.websiteUserId = self.websiteUserId;
                homeValuation.extraPropertyDetails = JSON.parse(JSON.stringify(self.extraPropertyDetails));
                if (homeValuation.extraPropertyDetails && homeValuation.extraPropertyDetails.rentalIncomeKnownDate) {
                    homeValuation.extraPropertyDetails.rentalIncomeKnownDate = self.rentalIncomeKnownDate
                }
                homeValuation.propertyDetails = self.propertyDetails;
                homeValuation.eqcFields = self.eqcFields;
                return homeValuation;
            },
            validate: function(event) {
                const self = this;
                var errors = [];

                if(($.isEmptyObject(event.valuer) || !event.valuer || (event.valuer && event.valuer.id == ""))){
                    self.error.valuer.hasError = true;
                    self.error.valuer.errorMessage = "This field is required.";
                    errors.push({step: 1, name: 'Job Setup', error: 'Valuer must be specified.'});
                } else {
                    self.error.valuer.hasError = false;
                    self.error.valuer.errorMessage = "";
                }
                if($.isEmptyObject(event.purpose) || !event.purpose || (event.purpose && event.purpose.code == "")) {
                    self.error.purpose.errorMessage = "This field is required.";
                    errors.push({step: 1, name: 'Job Setup', error: 'Purpose of Valuation must be specified.'});
                } else {
                    self.error.purpose.errorMessage = "";
                }
                if($.isEmptyObject(event.reportType) || !event.reportType || (event.reportType && event.reportType.code == "")) {
                    self.error.reportType.errorMessage = "This field is required.";
                    errors.push({step: 1, name: 'Job Setup', error: 'Valuation Report Type must be specified.'});
                } else {
                    self.error.reportType.errorMessage = "";
                }
                if(($.isEmptyObject(event.peerReview) || !event.peerReview || event.peerReview && event.peerReview.code == "")){
                    if(self.isNewValuation){
                        self.error.peerReview.errorMessage = "This field is required."
                        errors.push({step: 1, name: 'Job Setup', error: 'Peer Review must be specified.'});
                    }
                    else {
                        self.error.peerReview.errorMessage = "This field is required.";
                    }
                }
                else {
                    self.error.peerReview.errorMessage = "";
                }
                var message = "<label>This field is required.</label>";
                if(!self.inspectionDate) {
                    self.error.inspectionDate.hasError = true;
                    self.error.inspectionDate.errorMessage = "This field is required.";
                    errors.push({step: 1, name: 'Job Setup', error: 'Inspection Date and Time cannot be empty.'});
                } else {
                    self.error.inspectionDate.hasError = false;
                    self.error.inspectionDate.errorMessage = "";
                }
                if(!event.jobInstruction || (event.jobInstruction && event.jobInstruction.clientName.trim() == "")) {
                    $('.cnValMessage').html(message);
                    $('.cnGroup').addClass("valError");
                    errors.push({step: 1, name: 'Job Setup', error: 'Client Name cannot be empty.'});
                } else {
                    $('.cnGroup').removeClass("valError");
                }
                if(!event.jobInstruction || (event.jobInstruction && event.jobInstruction.instructedBy.trim() == "") && self.reportType.code != 'LDVA' && self.reportType.code != 'LDVNHI') {
                    $('.ibValMessage').html(message);
                    $('.ibGroup').addClass("valError");
                    errors.push({step: 1, name: 'Job Setup', error: 'Instructed By cannot be empty.'});
                } else {
                    $('.ibGroup').removeClass("valError");
                }

                if (event.extraPropertyDetails) {
                    if (event.extraPropertyDetails.rentalIncomeKnownDate) {
                        if (moment().isBefore(event.extraPropertyDetails.rentalIncomeKnownDate)) {
                            errors.push({step: 1, name: 'Job Setup', error: 'Rental Income Known cannot be in the future.'});
                        }
                    }
                }
                if(event.jobInstruction && event.jobInstruction.emailAddress && !self.isValidEmailAddress(event.jobInstruction.emailAddress)) {
                    errors.push({step: 1, name: 'Job Setup', error: 'Email Address is invalid.'});
                }
                // TODO: As discussed with BA, comment out for the moment. Don't delete.
                // if (event.valuationDueDate && event.inspectionDate) {
                //     if (moment(event.inspectionDate).isAfter(event.valuationDueDate)) {
                //         errors.push({step: 1, name: 'Job Setup', error: 'Inspection Date/Inspection Time cannot be after Valuation Due Date'});
                //     }
                // }
                return errors;
            },
            saveJob: function(completeSetup, persist, fromUpdate) {
                var self = this;
                var event = self.beforeSaving();
                event.completeSetup = completeSetup;
                if(!fromUpdate) self.disableButtons();
                var errors = self.validate(event)
                if(errors.length > 0) {
                    if(!fromUpdate) {
                        self.enableButtons();
                    }
                    event.hasError = true;
                    event.persist = false;
                } else {
                    self.error.valuer.hasError = false;
                    self.error.valuer.errorMessage = "";
                    self.error.purpose.errorMessage = "";
                    self.error.reportType.errorMessage = "";
                    self.error.inspectionDate.hasError = false;
                    self.error.inspectionDate.errorMessage = "";
                    $('.cnGroup').removeClass("valError");
                    $('.ibGroup').removeClass("valError")
                    $('.laGroup').removeClass("valError")
                    event.hasError = false;
                    event.persist = persist;
                }

                var noChanges = true;
                $.each(['clientReference','websiteUserId','jobInstruction','reportType','valuer','purpose','countersigner','qvOffice','peerReview','valuationDueDate', 'inspectionDate', 'extraPropertyDetails', 'eqcFields'], function (index, item) {
                    if(self.homeValuationCopy == null) {
                        noChanges = false
                    } else {
                        if(self[item] === undefined){
                            self[item] = {};
                        }
                        noChanges = self.jsonEqual(self.homeValuationCopy[item], JSON.parse(JSON.stringify(self[item])));
                    }
                    if(!noChanges) {
                        return false;
                    }
                });
                event.isDirty = !noChanges;
                event.errors = errors;
                EventBus.$emit('home-valuation-job-instruction', event);
            },
            isValidEmailAddress: function (email) {
                var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(email);
            },
            isValidDate: function(date) {
                var re =/^(3[0-1]|2[0-9]|1[0-9]|0?[1-9])\/(0?[1-9]|1[0-2])\/([0-9]{4})$/;
                return re.test(date) && moment(date, ['DD/MM/YYYY', 'D/MM/YYYY', 'DD/M/YYYY', 'D/M/YYYY']).isValid();
            },
            isValidTime: function(time) {
                var re =/^(1[0-2]|0?[1-9]):[0-5][0-9]$/;
                return re.test(time);
            },
            validation: function() {
                var self = this;
                $('.advSearch-validation').donetyping(function(evt){
                    var inputValue = $(evt).val();
                    var type = $(evt).data('type');
                    var group = $(evt).data('group');
                    var errMessage = $(evt).data('error');
                    var cluster = $(evt).data('cluster');
                    var dateType = $(evt).data('label');
                    var label = 'Email';
                    if(type == 'date') label = 'Date';
                    if(type == 'time') label = 'Time';
                    if(inputValue) {
                        if((type == 'email' && !self.isValidEmailAddress(inputValue)) ||
                                (type == 'date' && !self.isValidDate(inputValue)) ||
                                (type == 'time' && !self.isValidTime(inputValue))) {
                            var message = "<label>" + label + " is not valid.</label>";
                            self.disableButtons();
                            $('.' + errMessage).html(message);
                            $('.' + group).addClass("valError");
                        } else if(label == 'Date' || label == 'Time') {
                            var date = $('.' + cluster + 'Date').val();
                            var time = $('.' + cluster + 'Time').val();
                            if ((date != "" && time == "") || (date == "" && time != "")) {
                                self.disableButtons();
                                $('.' + errMessage).html("<label>Please provide both " + dateType + " date and time.</label>");
                                $('.' + group).addClass("valError");
                            } else {
                                self.enableButtons();
                                $('.' + cluster + 'Group').removeClass("valError");
                            }
                        } else {
                            self.enableButtons();
                            $('.' + group).removeClass("valError");
                        }
                    } else {
                        if(label == 'Date' || label == 'Time') {
                            var date = $('.' + cluster + 'Date').val();
                            var time = $('.' + cluster + 'Time').val();
                            if ((date != "" && time == "") || (date == "" && time != "")) {
                                self.disableButtons();
                                $('.' + errMessage).html("<label>Please provide both " + dateType + " date and time.</label>");
                                $('.' + group).addClass("valError");
                            } else {
                                self.enableButtons();
                                $('.' + cluster + 'Group').removeClass("valError");
                            }
                        } else {
                            self.enableButtons();
                            $('.' + group).removeClass("valError");
                        }
                    }
                });
            },
            loadReferenceData: function() {
                var self = this;
                self.validation();
            },
            refreshView: function() {
                const self = this;
                self.showMultiSelects = !self.showMultiSelects;
                if (!self.showMultiSelects) {
                    self.refreshMultiSelects = true;
                }
            },
            updateReportTypeRelatedFields: function(obj) {
                const self = this;
                // If no report type selected then exit
                if(!obj.val[0])
                    return;

                var reportTypeCode = obj.val[0].code;
                EventBus.$emit('home-valuation-report-detail-report-type', reportTypeCode);
                if (reportTypeCode == "HTTB" || reportTypeCode == "HA") {
                    if (reportTypeCode == "HTTB") {
                        self.jobInstruction.extendedTo = self.hnzTenantToBuyExtendedTo;
                    }
                    self.jobInstruction.clientName = "Housing New Zealand Corporation";
                    self.jobInstruction.instructedBy = "Housing New Zealand Corporation";
                }
                if (reportTypeCode != "HTTB" && (self.jobInstruction.extendedTo.trim() ==  self.hnzTenantToBuyExtendedTo)) {
                    self.jobInstruction.extendedTo = "";
                };

                if (reportTypeCode != "HTTB" && reportTypeCode != "HA") {
                    if (self.jobInstruction.clientName.trim() ==  self.hnzClientName) {
                        self.jobInstruction.clientName = "";
                    }
                    if (self.jobInstruction.instructedBy.trim() ==  self.hnzInstructedBy) {
                        self.jobInstruction.instructedBy = "";
                    }
                }

                if(reportTypeCode.includes('VQV')){
                    self.purpose = self.$store.getters.getCategoryClassifications('ValuationPurpose').filter((valuationPurpose) => valuationPurpose.code.includes('CMV'))[0] || [];
                    self.peerReview = self.$store.getters.getCategoryClassifications('PeerReviewSetup').filter((peerReview) => peerReview.code.includes('PRNR'))[0] || [];
                    self.refreshView();
                }
                // This updates the valuation standards
                if (reportTypeCode == "KS") {
                    const self = this;
                    const valuationStandards = self.$store.getters.getCategoryClassifications('ValuationStandards') || [];
                    let kerbsideValuationStandardCodes = {};
                    kerbsideValuationStandardCodes = ['C', 'D', 'E', 'F', 'G', 'H', 'I', 'P', 'V'];
                    const newValuationStandards = valuationStandards.filter((valuationStandard) => kerbsideValuationStandardCodes.includes(valuationStandard.code));
                    console.log('valuationStandards: ', newValuationStandards);
                    var obj = {}
                    obj.attrName = 'valuationStandards';
                    obj.val = newValuationStandards
                    EventBus.$emit('notify-multi-qaDetails', obj)

                }
                self.checkIsSundryClient();
                self.setFields(reportTypeCode);
            },

            populateHouseDetails: function(data) {
                const self = this

                if (!self.propertyDetails.houseDetails.numberOfBedrooms) {
                    self.propertyDetails.houseDetails.numberOfBedrooms = $.isNumeric(data.bedrooms)?data.bedrooms:null;
                }

                if (!self.propertyDetails.houseDetails.numberOfToilets) {
                    self.propertyDetails.houseDetails.numberOfToilets = $.isNumeric(data.toilets)?data.toilets:null;
                }

                if (!self.propertyDetails.houseDetails.garaging) {
                    if(($.isNumeric(data.freeStandingGarages) && $.isNumeric(data.underMainRoofGarages))) {
                        self.propertyDetails.houseDetails.garaging = (data.freeStandingGarages+data.underMainRoofGarages);
                    } else if($.isNumeric(data.freeStandingGarages)) {
                        self.propertyDetails.houseDetails.garaging = data.freeStandingGarages;
                    }else if($.isNumeric(data.underMainRoofGarages)){
                        self.propertyDetails.houseDetails.garaging = data.underMainRoofGarages;
                    }
                }

                if (!self.propertyDetails.houseDetails.offstreetParking) {
                    self.propertyDetails.houseDetails.offstreetParking = $.isNumeric(data.carParks)?data.carParks:null;
                }

                if (!self.propertyDetails.houseDetails.estimatedYearOfConstruction) {
                    self.propertyDetails.houseDetails.estimatedYearOfConstruction = $.isNumeric(data.effectiveYearBuilt)?data.effectiveYearBuilt:null;
                }

                if (!self.propertyDetails.houseDetails.totalFloorArea) {
                    self.propertyDetails.houseDetails.totalFloorArea = $.isNumeric(data.TFA)?data.TFA:null;
                }

                if (!self.propertyDetails.houseDetails.exteriorCladding) {
                    var exteriorCladdingList = self.$store.getters.getCategoryClassifications('ExteriorCladding');
                    if($.type(data.wallConstruction)==='object'){
                        var wallConstruction = data.wallConstruction;
                        var exists = exteriorCladdingList.filter(function(e) { return e.code == wallConstruction.code; });
                        if(exists.length > 0) {
                            self.propertyDetails.houseDetails.exteriorCladding = exists;
                        }
                    }else if($.type(data.wallConstruction)==='array'){
                        var wallConstructions = [];
                        $.each(data.wallConstruction, function (i, obj) {
                            var exists = exteriorCladdingList.filter(function(e) { return e.code == obj.code; });
                            if(exists.length > 0) {
                                wallConstructions.push(exists[0]);
                            }
                        });
                        self.propertyDetails.houseDetails.exteriorCladding = wallConstructions;
                    }
                }
                if (!self.propertyDetails.houseDetails.roofConstruction) {
                    var roofConstructionList = self.$store.getters.getCategoryClassifications('RoofConstruction');
                    if($.type(data.roofConstruction)==='object'){
                        var roofConstruction = data.roofConstruction;
                        var exists = roofConstructionList.filter(function(e) { return e.code == roofConstruction.code; });
                        if(exists.length > 0) {
                            self.propertyDetails.houseDetails.roofConstruction = exists;
                        }
                    }else if($.type(data.roofConstruction)==='array'){
                        var roofConstructions = [];
                        $.each(data.roofConstruction, function (i, obj) {
                            var exists = roofConstructionList.filter(function(e) { return e.code == obj.code; });
                            if(exists.length > 0) {
                                roofConstructions.push(exists[0]);
                            }
                        });
                        self.propertyDetails.houseDetails.roofConstruction = roofConstructions;
                    }
                }
                if (!self.propertyDetails.houseDetails.houseType) {
                    var houseTypeList = self.$store.getters.getCategoryClassifications('HouseType_HV');
                    if($.type(data.houseTypeObj)==='object') {
                        if (data.houseTypeObj.code && (data.houseTypeObj.code == 'QB'  || data.houseTypeObj.code == 'QO' || data.houseTypeObj.code == 'SR')) {
                            data.houseTypeObj.code = 'BN'
                        }
                        /*if (data.houseTypeObj.code && (data.houseTypeObj.code == 'SR')) {
                            data.houseTypeObj.code = 'BN'
                        }*/
                        var exists = houseTypeList.filter(function (e) {
                            return e.code == data.houseTypeObj.code;
                        });
                        if (exists.length > 0) {
                            self.propertyDetails.houseDetails.houseType = exists[0];
                        }
                    }
                }
                var codeCompliance = self.$store.getters.getCategoryClassifications('CodeCompliance');
                if(codeCompliance[0]) {
                    self.propertyDetails.houseDetails.codeCompliance = codeCompliance[0].description;
                }
                var totalFloorArea = self.propertyDetails.houseDetails.totalFloorArea
                if (totalFloorArea && !self.propertyDetails.houseDetails.totalFloorDescription) {
                    self.propertyDetails.houseDetails.totalFloorDescription = totalFloorArea+' m²';
                }
            },
            jsonEqual: function jsonEqual(a,b) {
                return deepEqual(a,b);
            },
            setFields: function (reportTypeCode) {
                const self = this;
                // We will just set the default for the fields as the MVR as this is the general report type
                var reportTypeCode = reportTypeCode ? reportTypeCode : (self.reportType.code && self.reportType.code.length > 0) ? self.reportType.code : 'MVR';
                var fields;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.HomeValuation.getFields(reportTypeCode).url,
                    cache: false,
                    success: function (response) {
                        //Then Here we will set the fields
                        fields = JSON.parse(response);
                        EventBus.$emit('set-fields', fields);
                    },
                    error: function (response) {
                        console.log('Error while fetching FIELDS: ' + response);
                    }
                });
            },

            checkIsSundryClient: function() {
                EventBus.$emit('notify-sundry-client', this.jobInstruction.isSundryClient);
            },

            async getValuationJobUpdateInformation() {
                const self = this;
                await $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.HomeValuation.getValuationJobUpdateInformation(self.homeValuation.id).url,
                    cache: false,
                    success: function (response) {
                        self.valuationJobUpdateInfo = response;
                    },
                    error: function (response) {
                        console.error('ERR-JSU-001: Error fetching valuation job update inforamtion: ', response);
                    }
                });
            },

            async getPropertyInfo() {
                try {
                    console.log('job setup loading values for: ', this.propertyQupid);
                    this.propertyInfo = await getPropertyInfo(this.propertyQupid);
                    this.propertyInfoLoaded = true;
                } catch (error) {
                    console.log('Error fetching property info: ', error);
                }
            },

            async saveValuationJobUpdateInformation(homeval){
                const self = this;
                const userName = 'QVNZ\\' + this.userName;
                const qupid = homeval.propertySummary.qupid;
                const jobId = homeval.id;
                await $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.HomeValuation.saveValuationJobUpdateInformation(jobId, userName, qupid).url,
                    cache: false,
                    success: function (response) {
                        self.valuationJobUpdateInfo = response;
                    },
                    error: function (response) {
                        console.error('ERR-JSU-002: Error fetching valuation job update inforamtion: ' + response);
                    }
                });
            },

        },
        mounted() {
            var self = this;
            self.loadReferenceData();
            EventBus.$on('enable-buttons', function(){
                self.enableButtons()
            })
            EventBus.$on('notify-multi-job-setup', function(data){
                if(data.attrName == 'inspectionType') {
                    self.jobInstruction[data.attrName] = data.val;
                }
                else if(data.attrName == 'propertyType') {
                    self.jobInstruction[data.attrName] = data.val;
                }
                else if(data.attrName.split('.')[0] == 'eqcFields') {
                    self.eqcFields[data.attrName.split('.')[1]] = data.val;
                } else {
                    self[data.attrName] = data.val;
                }
            });
            EventBus.$on('notify-simple-extraPropertyDetails', function(data){
                var val = data.val;
                self.extraPropertyDetails[data.attrName] = val;
                if (data.attrName == 'numberOfivingAreas' || data.attrName == 'garaging' || data.attrName == 'offstreetParking'
                        || data.attrName == 'numberOfBathrooms' || data.attrName == 'numberOfToilets' || data.attrName == 'numberOfLivingAreas') {
                    self.propertyDetails.houseDetails[data.attrName] = val
                }
                if (data.attrName == 'numberOfSingleBedrooms') {
                    var doubleBedrooms = self.extraPropertyDetails.numberOfDoubleBedrooms
                    if (doubleBedrooms) {
                        doubleBedrooms = parseInt(doubleBedrooms)
                    }
                    else {
                        doubleBedrooms = 0
                    }
                    self.propertyDetails.houseDetails.numberOfBedrooms = parseInt(data.val) + doubleBedrooms
                    var singleBedrooms = parseInt(data.val)
                    var allBedroomObjs = []
                    var bedroomCounter = 0
                    for (var i = 0; i < singleBedrooms && bedroomCounter < 10; i ++) {
                        var obj = {}
                        obj.category = 'Bedroom1'
                        obj.code = 'SINOM'
                        obj.description = 'Single Bedroom'
                        var singleBedroomObj = {}
                        singleBedroomObj.key = obj
                        allBedroomObjs.push(singleBedroomObj)
                        bedroomCounter++
                    }

                    for (var i = 0; i < doubleBedrooms && bedroomCounter < 10; i ++) {
                        var obj = {}
                        obj.category = 'Bedroom1'
                        obj.code = 'DOUOM'
                        obj.description = 'Double Bedroom'
                        var doubleBedroomObj = {}
                        doubleBedroomObj.key = obj
                        allBedroomObjs.push(doubleBedroomObj)
                        bedroomCounter++
                    }
                    self.propertyDetails.bedroomDetails.bedrooms = allBedroomObjs
                }
                if (data.attrName == 'numberOfDoubleBedrooms') {
                    var singleBedrooms = self.extraPropertyDetails.numberOfSingleBedrooms
                    if (singleBedrooms) {
                        singleBedrooms = parseInt(singleBedrooms)
                    }
                    else {
                        singleBedrooms = 0
                    }
                    self.propertyDetails.houseDetails.numberOfBedrooms = parseInt(data.val) + singleBedrooms
                    var doubleBedrooms = parseInt(data.val)
                    var allBedroomObjs = []
                    var bedroomCounter = 0
                    for (var i = 0; i < singleBedrooms && bedroomCounter < 10; i ++) {
                        var obj = {}
                        obj.category = 'Bedroom1'
                        obj.code = 'SINOM'
                        obj.description = 'Single Bedroom'
                        var singleBedroomObj = {}
                        singleBedroomObj.key = obj
                        allBedroomObjs.push(singleBedroomObj)
                        bedroomCounter++
                    }

                    for (var i = 0; i < doubleBedrooms && bedroomCounter < 10; i ++) {
                        var obj = {}
                        obj.category = 'Bedroom1'
                        obj.code = 'DOUOM'
                        obj.description = 'Double Bedroom'
                        var doubleBedroomObj = {}
                        doubleBedroomObj.key = obj
                        allBedroomObjs.push(doubleBedroomObj)
                        bedroomCounter++
                    }
                    self.propertyDetails.bedroomDetails.bedrooms = allBedroomObjs
                }
                self.saveJob(false,false,true);
            });

            EventBus.$on('notify-multi-extraPropertyDetails', function(data){
                self.extraPropertyDetails[data.attrName] = data.val;
                if (data.attrName == 'mainBathroomAge' || data.attrName == 'mainBathroomQuality' || data.attrName == 'ensuiteAge'
                    || data.attrName == 'ensuiteQuality') {
                    self.propertyDetails.bathroomDetails[data.attrName] = data.val
                }
                if (data.attrName == 'kitchenAge') {
                    self.propertyDetails.kitchenDetails.age = data.val
                }
                if (data.attrName == 'kitchenQuality') {
                    self.propertyDetails.kitchenDetails.quality = data.val
                }
                if (data.attrName == 'redecorationAge' || data.attrName == 'heatingType' || data.attrName == 'insulation'
                     || data.attrName == 'plumbingAge' || data.attrName == 'wiringAge' || data.attrName == 'doubleGlazing'
                        || data.attrName == 'alternativeEnergy' || data.attrName == 'ventilation') {
                    self.propertyDetails.interiorDetails[data.attrName] =  data.val
                }
                self.saveJob(false,false,true);
            });

            EventBus.$on('job-setup-date-time', (data) => {
                self[data.attrName] = data.val;
                self.saveJob(false, false, true);
            });

            EventBus.$on('notify-simple-engineer-report-date', (data) => {
                this.eqcFields.dateOfEngineersReport = data.val;
                this.saveJob(false, false, true);
            });

        },
        created() {
            var self = this;
            EventBus.$on('home-valuation-saved', function(obj) {
                var homeValuation = obj.homeValuation;
                if(obj && obj.fromStep === 1){
                    self.saveValuationJobUpdateInformation(obj.homeValuation);
                }
                if(self.homeValuation == {} || self.homeValuation.id != obj.homeValuation.id || obj.reload) {
                    self.homeValuation = JSON.parse(JSON.stringify(obj.homeValuation));
                    self.clientReference = homeValuation.clientReference;
                    self.websiteUserId = homeValuation.websiteUserId;
                    self.propertySummary = homeValuation.propertySummary;
                    self.jobInstruction = homeValuation.jobInstruction;
                    self.reportType = homeValuation.reportType;
                    self.purpose = homeValuation.purpose;
                    self.valuer = homeValuation.valuer;
                    self.countersigner = homeValuation.countersigner;
                    self.qvOffice = homeValuation.qvOffice;
                    self.peerReview = homeValuation.peerReview;
                    self.valuationDueDate = homeValuation.valuationDueDate;
                    self.inspectionDate = homeValuation.inspectionDate;
                    self.eqcFields = homeValuation.eqcFields;
                    if (homeValuation.valuer == null || homeValuation.valuer.id == null) {
                        self.valuer = { id: ""};
                    }
                    if (homeValuation.countersigner == null || homeValuation.countersigner.id == null) {
                        self.countersigner = { id: ""};
                    }
                    if (homeValuation.qvOffice == null || homeValuation.qvOffice.id == null) {
                        self.qvOffice = { id: ""};
                    }
                    if (homeValuation.peerReview == null || homeValuation.peerReview.code == null) {
                        self.peerReview = { code: ""};
                    }
                    if (homeValuation.jobInstruction == null) {
                        self.jobInstruction = {
                            clientName: "",
                            borrower: "",
                            postalAddress: "",
                            emailAddress: "",
                            mobilePhone: "",
                            daytimePhone: "",
                            eveningPhone: "",
                            lenderName: "",
                            lenderAddress: "",
                            instructedBy: "",
                            extendedTo: "",
                            otherInstructions: "",
                            inspectionType: {},
                            propertyType:{},
                            changeSubjectAddress: false,
                            isSundryClient: false
                        };
                    }
                    if(homeValuation.eqcFields == null) {
                        self.eqcFields = {
                            insuranceCompany: {},
                            managementCompany: {},
                            engineerName: {},
                            dateOfEngineersReport: null,
                            propertyHasRetainingWalls: {},
                        }
                    }
                    if (homeValuation.reportType == null) {
                        self.reportType = { code: "" };
                    }
                    if (homeValuation.purpose == null) {
                        self.purpose = { code: "" };
                    }
                    self.error = {
                        valuer: {
                            hasError: false,
                            errorMessage: ""
                        },
                        purpose:{
                            errorMessage: ''
                        },
                        peerReview:{
                            errorMessage: ''
                        },
                        reportType: {
                            errorMessage: ''
                        },
                        inspectionDate: {
                            hasError: false,
                            errorMessage: ""
                        },
                        websiteUserId: {
                            errorMessage: ''
                        }
                    };
                    if (self.defaultPropertyData && obj.loadDefaultData) {
                        self.populateDefaultData(self.defaultPropertyData)
                    }
                    if (!self.isTabClicked) {
                        self.setCurrentTab('JobSetup', 1, false)
                    }
                    self.refreshView();
                    $(".changeAddress-trigger").prop("checked", self.isChangeAddClicked);
                } else {
                    self.homeValuation = JSON.parse(JSON.stringify(obj.homeValuation));
                    self.getValuationJobUpdateInformation();
                }
                if (homeValuation.extraPropertyDetails) {
                    self.extraPropertyDetails = homeValuation.extraPropertyDetails
                }
                if (homeValuation.extraPropertyDetails && homeValuation.extraPropertyDetails.rentalIncomeKnownDate) {
                    self.extraPropertyDetails.rentalIncomeKnownDate = moment.utc(homeValuation.extraPropertyDetails.rentalIncomeKnownDate).format('MM/YYYY')
                }
                if (homeValuation.propertyDetails) {
                    self.propertyDetails = homeValuation.propertyDetails;
                }
                self.jobStatus = homeValuation.status.code;
                self.homeValuationCopy = JSON.parse(JSON.stringify(
                        { clientReference: self.clientReference,
                          websiteUserId: self.websiteUserId,
                          jobInstruction: self.jobInstruction,
                          eqcFields: self.eqcFields,
                          reportType: self.reportType,
                          purpose: self.purpose,
                          valuer: self.valuer,
                          countersigner: self.countersigner,
                          qvOffice: self.qvOffice,
                          peerReview: self.peerReview,
                          valuationDueDate: self.valuationDueDate,
                          inspectionDate: self.inspectionDate,
                          extraPropertyDetails: self.extraPropertyDetails}));
                if (self.homeValuation.eqcFields) {
                    self.eqcFields = JSON.parse(JSON.stringify(self.homeValuationCopy.eqcFields));
                    if (self.eqcFields.dateOfEngineersReport) {
                        self.eqcFields.dateOfEngineersReport = moment.utc(self.eqcFields.dateOfEngineersReport).format('DD/MM/YYYY')
                    } else {
                        self.eqcFields.dateOfEngineersReport = null;
                    }
                }
                self.enableButtons();
            });

            EventBus.$on('home-valuation-new', async function(homeValuation) {
                Object.assign(self.$data, self.$options.data.apply(self));
                self.isNewValuation = true;
                self.extraPropertyDetails = {};
                self.populateDefaultData(homeValuation);
                self.populateHouseDetails(homeValuation);
                self.defaultPropertyData = homeValuation;
                if (!self.isTabClicked) {
                    self.setCurrentTab('JobSetup', 1, false)
                }
                self.loadReferenceData();
                self.setFields();
                self.refreshView();
                $(".changeAddress-trigger").prop("checked", false);
                await self.getPropertyInfo();
                if (self.propertyInfo.hazardNotes && !self.jobInstruction.siteInspectionNotes) {
                    self.jobInstruction.siteInspectionNotes = self.propertyInfo.hazardNotes;
                }
            });

            EventBus.$on('load-default-property-data', function(propertyData) {
                self.defaultPropertyData = propertyData.property;
            });

            EventBus.$on('home-valuation-loaded', function() {
                EventBus.$emit('home-valuation-report-detail-initial-report-type', self.reportType.code);
                self.setFields();
            });
            EventBus.$on('set-fields', function(fields) {
                if(fields){
                    self.fields = fields.jobSetup;
                }
            });
        },
        updated: function() {
            const self = this;
            if (self.refreshMultiSelects) {
                self.showMultiSelects = true;
                self.refreshMultiSelects = false;
            }
            self.saveJob(false,false,true);
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-multi-job-setup', this.listener);
            EventBus.$off('notify-simple-extraPropertyDetails', this.listener);
            EventBus.$off('notify-multi-extraPropertyDetails', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
            EventBus.$off('set-fields', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('job-setup-date-time', this.listener);
            EventBus.$off('notify-simple-engineer-report-date', this.listener);

        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-multi-job-setup', this.listener);
            EventBus.$off('notify-simple-extraPropertyDetails', this.listener);
            EventBus.$off('notify-multi-extraPropertyDetails', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
            EventBus.$off('set-fields', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('job-setup-date-time', this.listener);
            EventBus.$off('notify-simple-engineer-report-date', this.listener);
        }
    }
</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="md-QVHV-wrapper" style="display: none">
        <h1 v-if="isSaved">Valuation Job Created on<span>{{ formattedValuationCreatedDate }}</span></h1>
        <div class="md-left-QVHV">
            <ul class="QVHV-stepper">
                <li class="stepper jobsetup jobsetupStepper"
                    v-bind:class="{disabled: homeValuation.status && homeValuation.status.code == 'I', active: activeStep == 1}"
                    data-step="jobSetup">
                    <span class="stepperCount">1</span><span>Job Set-up</span>
                </li>
                <li class="stepper propertyDetailsStepper"
                    v-bind:class="{disabled: !homeValuation.status || (homeValuation.status && homeValuation.status.code == 'I')}"
                    data-step="propertyDetails">
                    <span class="stepperCount">2</span><span>Property Details</span>
                </li>
                <li class="stepper comparablePropertiesStepper"
                    v-bind:class="{disabled: !homeValuation.status || (homeValuation.status && homeValuation.status.code == 'S'), active: activeStep == 3}"
                    data-step="comparableProperties">
                    <span class="stepperCount">3</span><span>Comparable Properties</span>
                </li>
                <li class="stepper valuationStepper"
                    v-bind:class="{disabled: !homeValuation.status || (homeValuation.status && homeValuation.status.code == 'S')}"
                    data-step="valuation">
                    <span class="stepperCount">4</span><span>Valuation</span>
                </li>
                <li class="stepper reportDetailsStepper"
                    data-step="reportDetails">
                    <span class="stepperCount">5</span><span>Report Details</span>
                </li>
                <li class="stepper locationDetailsStepper"
                    data-step="locationDetails">
                    <span class="stepperCount">6</span><span>Location Details</span>
                </li>
                <li class="stepper photoAndAttachmentsStepper"
                    data-step="photoAndAttachments">
                    <span class="stepperCount">7</span><span>Photos & Attachments</span>
                </li>
                <li class="stepper qaAndReviewStepper"
                    data-step="qaAndReview">
                    <span class="stepperCount">8</span><span>QA and Review</span>
                </li>
            </ul>
            <ul class="peerReviewSection" v-if="homeValuation.peerReview && homeValuation.peerReview.code != 'PRNR' && homeValuation.peerReview.code != ''">
                <li class="peerReviewFormRequiredTitle">Peer Review Form Required</li>
                <li class="peerReviewFormButton">
                    <button class="exportResults mdl-button mdl-js-button mdl-button--icon search-control-buttons" @click="getPeerReviewPDF"><i class="material-icons md-dark">&#xE06F;</i></button>{{homeValuation.peerReview.description}}</li>
                </ul>
            <ul data-cy="job-status-board" class="jobStatusboard">
                <li>Job Status<span>{{ homeValuation.status ? homeValuation.status.description : 'Setup' }}
                    <span class="jobLock" v-if="homeValuation.status && homeValuation.status.code == 'I'" @click="forcePastInspection"><i class="material-icons">lock</i></span></span>
                </li>
                <li>Report Due<span>{{ formattedValuationDueDate}}</span></li>
                <li>Valuer<span>{{ homeValuation.valuer ? homeValuation.valuer.name : '' }}</span></li>
                <li>Countersigner<span>{{ homeValuation.countersigner ? homeValuation.countersigner.name : '' }}</span></li>
                <li v-if="homeValuation.qaDetails && homeValuation.qaDetails.reviewRequestedDate">Review Requested<span>{{ formattedReviewRequestedDate }}</span></li>
                <li v-if="homeValuation.qaDetails && homeValuation.qaDetails.rejectedDate">Countersigner Review Failed<span>{{ formattedRejectedDate }}</span></li>
                <li v-if="homeValuation.qaDetails && homeValuation.qaDetails.qaApprovalDate">Countersigner Review Passed<span>{{ formattedQaApprovalDate }}</span></li>
                <li
                    v-if="isAdminUser && homeValuation.status && homeValuation.status.code === 'C'"
                    class="QVHV-buttons"
                >
                    <button class="cancel" @click="reinstateJob">Reinstate Job</button>
                </li>
                <li class="QVHV-buttons"
                    v-bind:class="{disabled: !homeValuation.id || isReadOnly}">
                    <button class="cancel" @click="clickCancelButton">Cancel Job</button>
                </li>
            </ul>
            <dl data-cy="icon-legend" class="iconLegend">
                <dt>Legend</dt>
                <dd><i class="icons8-qv-logo-new">Required for Selected Report</i></dd>
                <dd><i class="icons8-qv-logo-new optional">Included in Report if Entered</i> </dd>
                <dd><i class="icons8-qv-logo-new notRequired">Values Not in Report</i></dd>
            </dl>
            <property-info
                v-if="isInternalUser"
                :qpid="qupid"
            />
        </div>
        <div class="md-right-QVHV">
            <job-setup
                 :readOnly="isReadOnly"
                 :property-qupid="qupid"
                 class="jobSetup wizard"
            ></job-setup>
            <property-details-parent class="propertyDetails wizard" style="display:none"
                 v-bind:readOnly="isReadOnly"></property-details-parent>
            <comparable-properties class="comparableProperties wizard" style="display: none"
                 v-bind:class="{disabled: isReadOnly}"></comparable-properties>
            <valuation class="valuation wizard" style="display: none"
                 v-bind:readOnly="isReadOnly"></valuation>
            <report-details class="reportDetails wizard" style="display: none"
                 v-bind:readOnly="isReadOnly"></report-details>
            <location-details class="locationDetails wizard" style="display: none"
                 v-bind:readOnly="isReadOnly"></location-details>
            <photos-attachments class="photoAndAttachments wizard" style="display: none"
                v-bind:readOnly="isReadOnly"></photos-attachments>
            <qa-and-review class="qaAndReview wizard" style="display: none"
               v-bind:readOnly="isReadOnly"></qa-and-review>
        </div>
        <confirmation id="confirmationFramework" okLabel="Yes" cancelLabel="No" :header="confirmationHeader"
                      :message="confirmationMessage" :okHandler="okHandler"></confirmation>
        <warning :header="warningHeader" :message="warningMessage" class="valuationJobError" close="Ok"></warning>
    </div>
</template>
<script>
    import { mapState } from 'vuex';
    import {EventBus} from '../../EventBus.js';
    import {store} from '../../DataStore';
    import deepEqual from 'deep-equal';
    import JobSetup from '../valuation/JobSetup.vue';
    import PropertyDetailsParent from '../valuation/PropertyDetailsParent.vue';
    import ComparableProperties from '../valuation/ComparableProperties.vue';
    import Valuation from '../valuation/Valuation.vue';
    import ReportDetails from '../valuation/ReportDetails.vue';
    import LocationDetails from '../valuation/LocationDetails.vue';
    import PhotosAttachments from '../valuation/PhotosAttachments.vue';
    import QaAndReview from '../valuation/QaAndReview.vue';
    import Confirmation from '../common/Confirmation.vue';
    import Warning from '../common/Warning.vue';
    import commonUtils from '../../utils/CommonUtils';
    import moment from 'moment';
    import PropertyInfo from '../property/PropertyInfo.vue';

    export default {
        components: {
            JobSetup,
            PropertyDetailsParent,
            ComparableProperties,
            Valuation,
            ReportDetails,
            LocationDetails,
            PhotosAttachments,
            QaAndReview,
            Confirmation,
            Warning,
            PropertyInfo,
        },
        mixins: [commonUtils],
        props: ['property', 'qupid'],
        data: function () {
            return {
                newJobInitialized: false,
                initialDisplay: true,
                warningHeader: '',
                warningMessage: '',
                confirmationHeader: '',
                confirmationMessage: '',
                okHandler: undefined,
                homeValuation: {
                    id: null,
                    valuationCreatedDate: null,
                    valuationDueDate: null,
                    inspectionDate: null,
                    jobInstruction: null,
                    purpose: null,
                    clientReference: null,
                    qvOffice: null,
                    peerReview: null,
                    valuer: null,
                    countersigner: null,
                    reportType: null,
                    extraPropertyDetails: {},
                    propertyDetails: {},
                    locationDetails: {},
                    reportDetails: {},
                    qaDetails: { currentVersion: "V1" },
                    propertySummary: {
                        id: null
                    },
                    status: {
                        code: "S",
                        description: "Setup"
                    },
                    worksheet: {},
                    proposedWorksheet: {},
                    incomeMethodWorksheet: {},
                    eqcFields: null,
                },
                homeValuationCopy: {},
                defaultPropertyData: {},
                propertySummary: {},
                isJobSaving: false,
                saveCounter: 0,
                activeStep: 1,
                step1isDirty: false,
                step2isDirty: false,
                step3isDirty: false,
                step4isDirty: false,
                step5isDirty: false,
                step6isDirty: false,
                step7isDirty: false,
                errors: {
                    step1: []
                }
            }
        },
        computed: {
            ...mapState('userData', [
                'isAdminUser',
                "isInternalUser",
            ]),
            ...mapState({
                monarchUserId: state => state.application.monarchUser.id
            }),
            isSaved: function () {
                return this.homeValuation.id != null;
            },
            isReadOnly: function () {
                var isReadOnly = true;
                if(this.homeValuation.status){
                    var statusCode = this.homeValuation.status.code;
                    if(statusCode == 'S') {
                    isReadOnly = false;
                    } else if($.inArray(statusCode, ['I','R','P','W']) !== -1) {
                        //Only allow Admin, Valuer and Countersigner at this point
                        var isAdmin = this.isAdminUser;
                        var isValuer = this.monarchUserId && this.homeValuation.valuer.id && this.homeValuation.valuer.id == this.monarchUserId;
                        var isCountersigner = this.monarchUserId && this.homeValuation.countersigner.id && this.homeValuation.countersigner.id == this.monarchUserId;
                        if(isAdmin || isValuer || isCountersigner) {
                            isReadOnly = false;
                            //Only allow Admin and Countersigner if status is Peer Review
                            if(statusCode == 'P' && isValuer && !isAdmin) {
                                isReadOnly = true;
                            }
                        }
                    }
                } else {
                    isReadOnly = false;
                }
                return isReadOnly;
            },
            formattedValuationCreatedDate: function () {
                var date = this.homeValuation.valuationCreatedDate;
                if (date != null) {
                    date = moment(date).format('DD MMMM YYYY');
                }
                return date;
            },
            formattedValuationDueDate: function () {
                var date = this.homeValuation.valuationDueDate;
                if (date != null) {
                    date = moment(date).format('DD MMMM YYYY');
                }
                return date;
            },
            formattedQaApprovalDate: function() {
                var self = this;
                var date = self.homeValuation.qaDetails.qaApprovalDate;
                if (date != null) {
                    date = moment(date).format('DD/MM/YYYY');
                }
                return date;
            },
            formattedReviewRequestedDate: function() {
                var self = this;
                var date = self.homeValuation.qaDetails.reviewRequestedDate;
                if (date != null) {
                    date = moment(date).format('DD/MM/YYYY');
                }
                return date;
            },
            formattedRejectedDate: function() {
                var self = this;
                var date = self.homeValuation.qaDetails.rejectedDate;
                if (date != null) {
                    date = moment(date).format('DD/MM/YYYY');
                }
                return date;
            }
        },
        methods: {
            getPeerReviewPDF: function() {
                var self = this;
                var m = jsRoutes.controllers.ValuationReport.getPeerReviewPDF();
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        window.open(response);
                    },
                    error: function (response) {
                        self.errorHandler(response);
                        self.warningHeader = "Peer Review Form Retrieval Error";
                    }
                });
            },
            isRegisteredValuer: function(){
                const self = this
                var registeredValuer = false
                if (self.homeValuation.valuer) {
                    var counterSignersList = self.$store.getters.getCategoryClassifications('CounterSigners');
                    if (counterSignersList) {
                        $.each(counterSignersList, function (i, obj) {
                            if (obj.id === self.homeValuation.valuer.id) {
                                registeredValuer = true
                            }
                        });
                    }
                    if(registeredValuer){
                        return true
                    }
                    return false;
                }
                return true;
            },
            isVerifiedQvBankReport: function () {
                const self = this;
                return self.homeValuation.reportType && self.homeValuation.reportType.code.includes('VQVB');
            },
            reinstateJob: function() {
                const self = this;
                // Set the status of the home valuation job to R -> Report
                self.assignStatus('R');
                self.saveHomeValuation(1);
            },
            completeJob: function(){
                const self = this;
                self.assignStatus("C");
                self.saveHomeValuation(1);
            },
            completeJobSetup: function(){
                const self = this;
                self.assignStatus("R");
                self.saveHomeValuation(1, function () {
                    $('.propertyDetailsStepper').click();
                    self.activeStep = 2;
                });
            },
            generateErrMsg: function(stepNumber, stepName, errMsg){
                var msg = 'Step '+stepNumber+'('+stepName+') : \n\n'
                msg += ''+ errMsg + '\n\n'
                return msg
            },
            runAllValidations: function(status, skip){
                const self = this;
                const hasEmptyWebsiteUserId = !self.homeValuation.websiteUserId || self.homeValuation.websiteUserId === '';
                const isRegisteredValuer = self.isRegisteredValuer();
                const setConfirmationMessage = (hasEmptyWebsiteUserId, isRegisteredValuer) => {
                    self.confirmationMessage = [];
                    if (hasEmptyWebsiteUserId && self.homeValuation.reportType.code !== 'LDVA' && self.homeValuation.reportType.code !== 'LDVNHI') {
                        self.confirmationMessage.push('Website User ID should not be empty for this report type.');
                    }
                    if (!isRegisteredValuer) {
                        self.confirmationMessage.push('The Valuer for this job is not registered.');
                    }
                    if (self.homeValuation.reportType.code == 'LDVA' || self.homeValuation.reportType.code == 'LDVNHI') {
                        if (!self.homeValuation.eqcFields.engineerName.code) {
                            self.confirmationMessage.push('Engineer Name is a required field for this report type.');
                        }
                        if (!self.homeValuation.eqcFields.dateOfEngineersReport) {
                            self.confirmationMessage.push('Date of Engineers Report is a required field for this report type.');
                        }
                    }
                    if(self.confirmationMessage.length > 0) {
                        self.confirmationMessage.push('Are you sure you want to proceed?');
                    }
                }

                if(skip && self.homeValuation && self.homeValuation.status.code && self.homeValuation.status.code == 'S') {
                    return true;
                }
                var homeValuation = self.homeValuation
                var errors = []
                if (homeValuation) {
                    if(self.errors.step1.length > 0) {
                        $.each(self.errors.step1, function (i, obj) {
                            errors.push(self.generateErrMsg(obj.step, obj.name, obj.error))
                        });
                    }
                }
                if (errors.length > 0) {
                    if (status == 'S') {
                        self.warningHeader = 'Job Setup Errors'
                    }
                    else if (status == 'C') {
                        self.warningHeader = 'Job Complete Errors'
                    }
                    else {
                        self.warningHeader = 'Job Errors'
                    }
                    self.warningMessage = errors
                    $('.valuationJobError').show()
                    return false;
                }
                else {
                    if (self.homeValuation && self.homeValuation.status && self.homeValuation.status.code == 'S') {
                        if(
                            self.isRegisteredValuer()
                            || (self.homeValuation.countersigner.id !== '' && !self.isRegisteredValuer())
                        ){
                            if (status == 'C') {
                                self.completeJob();
                            }
                            else {
                                if(((hasEmptyWebsiteUserId || !isRegisteredValuer) && "VQV" == self.homeValuation.reportType.code)
                                    || "LDVA" == self.homeValuation.reportType.code || "LDVNHI" == self.homeValuation.reportType.code) {
                                setConfirmationMessage(hasEmptyWebsiteUserId, isRegisteredValuer);
                                self.confirmationHeader = 'Complete Setup'
                                self.okHandler = self.completeJobSetup;
                                if (self.confirmationMessage.length > 0) {
                                    $('#confirmationFramework').show();
                                    return false;
                                }
                            }
                                self.completeJobSetup();
                            }
                            return true;
                        }
                        else {
                            if(self.isVerifiedQvBankReport()){
                                self.warningHeader = "Error";
                                self.warningMessage = 'The Valuer must be registered for this report type.';
                                $('.valuationJobError').show()
                                return false;
                            }
                            else if (status == 'C') {
                                self.confirmationHeader = 'Complete Job'
                                self.okHandler = self.completeJob;
                            }
                            else {
                                self.confirmationHeader = 'Complete Setup'
                                self.okHandler = self.completeJobSetup;
                            }
                            setConfirmationMessage(hasEmptyWebsiteUserId, isRegisteredValuer);
                            if (self.confirmationMessage.length > 0) {
                                $('#confirmationFramework').show()
                                return false;
                            }
                        }
                    }
                    return true;
                }
            },
            saveHomeValuation: function (step, callback) {
                var self = this;
                self.checkVersion(function() {
                    self.assignPropertySummary();
                    if (self.homeValuation && self.homeValuation.id) {
                        if (!self.homeValuation.comparableSalesUrl) {
                            self.homeValuation.comparableSalesUrl = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?valJobId='/*+self.homeValuation.id*/ ;
                        }
                    }
                    else {
                        self.homeValuation.comparableSalesUrl = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?valJobId=';
                    }
                    self.isJobSaving = true;
                    if(self.homeValuation.extraPropertyDetails && self.homeValuation.extraPropertyDetails.rentalIncomeKnownDate) {
                        var rDate = self.homeValuation.extraPropertyDetails.rentalIncomeKnownDate;
                        var momentDate = moment.utc(rDate, 'MM/YYYY');
                        if(momentDate.isValid()) {
                            self.homeValuation.extraPropertyDetails.rentalIncomeKnownDate = momentDate.toDate()
                        } else {
                            self.homeValuation.extraPropertyDetails.rentalIncomeKnownDate = null;
                        }
                    }
                    if(self.homeValuation.eqcFields && self.homeValuation.eqcFields.dateOfEngineersReport){
                        const engineersReportDate = self.homeValuation.eqcFields.dateOfEngineersReport;
                        const momentDate = moment.utc(engineersReportDate, 'DD/MM/YYYY');
                        if(momentDate.isValid()) {
                            self.homeValuation.eqcFields.dateOfEngineersReport = momentDate.toDate()
                        } else {
                            self.homeValuation.eqcFields.dateOfEngineersReport = null;
                        }
                    }

                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.HomeValuation.saveHomeValuation().url,
                        cache: false,
                        processData: false,
                        contentType: 'application/json',
                        data: JSON.stringify(self.homeValuation),
                        success: function (response) {
                            self.isJobSaving = false;
                            var newValuation = self.homeValuation.id == null || !self.homeValuation.id;
                            self.homeValuation = response;
                            if(newValuation) self.$emit('updateList', self.homeValuation);
                            var eventObj = {};
                            eventObj.homeValuation = self.homeValuation;
                            eventObj.fromStep = step;
                            if (!self.newJobInitialized) EventBus.$emit('home-valuation-saved', eventObj);
                            if (callback) callback();
                            self.saveCounter = 0;
                            self.step1isDirty = false;
                            self.step2isDirty = false;
                            self.step3isDirty = false;
                            self.step4isDirty = false;
                            self.step5isDirty = false;
                            self.step6isDirty = false;
                            self.step8isDirty = false;
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            if (self.saveCounter < 5){
                                console.log('Retrying save: ' + self.saveCounter);
                                self.saveCounter++;
                                setTimeout(function() {
                                    self.saveHomeValuation(step, callback);
                                }, 1000);
                            } else {
                                console.log('There was an error saving the valuation job.');
                                self.saveCounter = 0;
                                EventBus.$emit('enable-buttons');
                                self.isJobSaving = false;
                            }
                        }
                    });
                }, function() {
                    self.warningHeader = 'Warning';
                    self.warningMessage = 'Another user/process has modified this job. Latest job will be reloaded and will override your changes.';
                    $('.valuationJobError').show();
                }, function() {
                    console.log('There was an error saving the valuation job.');
                    self.saveCounter = 0;
                    EventBus.$emit('enable-buttons');
                    self.isJobSaving = false;
                });

            },
            checkVersion: function (callback1, callback2, callback3) {
                var self = this;
                if(self.homeValuation.id == null) {
                    if (callback1) callback1();
                } else {
                    $.ajax({
                        type: "GET",
                        url: jsRoutes.controllers.HomeValuation.getHomeValuation(self.homeValuation.id).url,
                        cache: false,
                        success: function (response) {
                            if(response.entityVersion == self.homeValuation.entityVersion) {
                                if (callback1) callback1();
                            } else {
                                self.homeValuation = response;
                                if (callback2) callback2();
                                var eventObj = {};
                                eventObj.homeValuation = self.homeValuation;
                                eventObj.fromStep = 0;
                                eventObj.reload = true;
                                EventBus.$emit('home-valuation-saved', eventObj);
                            }
                        },
                        error: function (response) {
                            console.log("Error in getting home valuation");
                            self.errorHandler(response);
                            if (callback3) callback3();
                        }
                    });
                }
            },
            getValuation: function (selectedValuation, units, callback) {
                var self = this;
                self.isJobSaving = true
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.HomeValuation.getHomeValuation(selectedValuation.jobId).url,
                    cache: false,
                    success: function (response) {
                        self.isJobSaving = false;
                        self.homeValuationCopy = JSON.parse(JSON.stringify(response));
                        self.homeValuation = response;
                        self.homeValuation.valuationCreatedDate = response.valuationCreatedDate;
                        self.homeValuation.id = response.id;
                        self.assignPropertySummary();
                        var eventObj = {}
                        if (selectedValuation && selectedValuation.loadDefaultData) {
                            eventObj.loadDefaultData = selectedValuation.loadDefaultData
                        }
                        eventObj.homeValuation = self.homeValuation;
                        eventObj.units = units;
                        EventBus.$emit('home-valuation-saved', eventObj);
                        EventBus.$emit('home-valuation-loaded');
                        if (callback) callback();
                    },
                    error: function (response) {
                        self.isJobSaving = false;
                        console.log("Error in getting home valuation");
                        self.errorHandler(response);
                    }
                });
            },
            assignStatus: function (code) {
                var self = this;
                self.homeValuation.status = self.getClassificationObject('HomeValuationJobStatus', code);
            },
            cancelJob: function () {
                var self = this;
                self.checkVersion(function() {
                    self.assignStatus('X');
                    self.saveHomeValuation(0);
                }, function() {
                    self.assignStatus('X');
                    self.saveHomeValuation(0);
                });
            },
            clickCancelButton: function () {
                const self = this;
                self.confirmationHeader = 'Cancel Valuation Job';
                self.confirmationMessage = 'Do you want to cancel the Job?';
                self.okHandler = self.cancelJob
                $('#confirmationFramework').show();
            },
            forcePastInspection: function () {
                const self = this;
                self.confirmationHeader = 'Warning'
                self.confirmationMessage = 'This will change the current status of the Valuation Job to “Report” which means it will no longer accept any updates from vWork. Are you sure you want to proceed?';
                self.okHandler = function() {
                    self.assignStatus('R');
                    self.saveHomeValuation(0);
                };
                $('#confirmationFramework').show();
            },
            assignPropertySummary: function () {
                var self = this;
                if(self.propertySummary) {
                    if(!self.homeValuation.propertySummary.id) self.homeValuation.propertySummary.id = self.property;
                    if(!self.homeValuation.propertySummary.qupid) self.homeValuation.propertySummary.qupid = self.qupid;
                    if(!self.homeValuation.propertySummary.rollNumber && self.propertySummary.rollNumber) self.homeValuation.propertySummary.rollNumber = self.propertySummary.rollNumber;
                    if(!self.homeValuation.propertySummary.assessmentNumber && self.propertySummary.assessmentNumber) self.homeValuation.propertySummary.assessmentNumber = self.propertySummary.assessmentNumber;
                    if(!self.homeValuation.propertySummary.suffix && self.propertySummary.suffix) self.homeValuation.propertySummary.suffix = self.propertySummary.suffix;
                    if(!self.homeValuation.propertySummary.address && self.propertySummary.address) self.homeValuation.propertySummary.address = self.propertySummary.address;
                    if(!self.homeValuation.propertySummary.territorialAuthority && self.propertySummary.territorialAuthority) self.homeValuation.propertySummary.territorialAuthority = self.propertySummary.territorialAuthority;
                }
            },
            nextStep: function(next, step) {
                var self = this;
                if (next) {
                    if (step == 2) {
                        if (self.homeValuation.status.code == "S") {
                            $('.reportDetailsStepper').click();
                        } else {
                            $('.comparablePropertiesStepper').click();
                        }
                    } else if (step == 3) {
                        $('.valuationStepper').click();
                    } else if (step == 4) {
                        $('.reportDetailsStepper').click();
                    } else if (step == 5) {
                        $('.locationDetailsStepper').click();
                    } else if (step == 6) {
                        $('.photoAndAttachmentsStepper').click();
                    }
                }
            }
        },
        mounted: function () {
            var self = this;
            $('.stepper').off("click").click(function (evt) {
                var saveJob = !self.newJobInitialized && !self.initialDisplay && (self.step1isDirty || self.step2isDirty || self.step3isDirty
                        || self.step4isDirty || self.step5isDirty || self.step6isDirty || self.step8isDirty || self.errors.step1.length > 0);
                if (saveJob) {
                    if(self.homeValuationCopy && self.homeValuationCopy.status && self.homeValuationCopy.status.code != 'S'
                            && self.runAllValidations(self.homeValuationCopy.status.code, true)) {
                        self.saveHomeValuation(0)
                        $('.stepper').removeClass('active');
                        $(this).addClass('active');
                        $('.wizard').hide();
                        $('.' + $(this).data('step')).show();
                    } else {
                        self.saveHomeValuation(0)
                        $('.stepper').removeClass('active');
                        $(this).addClass('active');
                        $('.wizard').hide();
                        $('.' + $(this).data('step')).show();
                    }
                } else {
                    $('.stepper').removeClass('active');
                    $(this).addClass('active');
                    $('.wizard').hide();
                    $('.' + $(this).data('step')).show();
                }
                self.newJobInitialized = false;
                self.initialDisplay = false;
            });
        },
        created: function () {
            var self = this;

            EventBus.$on('home-valuation-job-display', function (obj) {
                if (obj && obj.type == 'existing' && obj.data) {
                    var jobId = obj.data;
                    self.propertySummary = obj.propertySummary;
                    var eventObj = {}
                    eventObj.jobId = jobId
                    eventObj.loadDefaultData = obj.loadDefaultData
                    self.getValuation(eventObj, obj.property.units, function () {
                        self.initialDisplay = true;
                        if (self.homeValuation.status && self.homeValuation.status.code == "I") {
                            self.activeStep = 3;
                            $('.comparablePropertiesStepper').click();
                        } else {
                            self.activeStep = 1;
                            $('.jobsetupStepper').click();
                        }
                    });
                } else if (obj && obj.type == 'new' && obj.data) {
                    self.homeValuation = {};
                    self.homeValuation.valuationCreatedDate = null;
                    self.homeValuation.id = null;
                    self.homeValuation.propertySummary = obj.propertySummary;
                    self.propertySummary = obj.propertySummary;
                    obj.data.propertySummary = obj.propertySummary;
                    obj.data.sales = obj.sales;
                    obj.data.units = obj.property.units;
                    self.newJobInitialized = true;
                    $('.jobsetupStepper').click();
                    self.activeStep = 1;
                    EventBus.$emit('home-valuation-new', obj.data);
                }
            });

            EventBus.$on("home-valuation-back", function (prevStepClassName) {
                $("." + prevStepClassName).click();
            });

            EventBus.$on('home-valuation-job-instruction', function (jobSetup) {
                self.step1isDirty = jobSetup.isDirty;
                self.errors.step1 = jobSetup.errors;
                if(!jobSetup.isDirty && !jobSetup.completeSetup) {
                    EventBus.$emit('enable-buttons');
                } else {
                    if(jobSetup.isDirty) {
                        self.homeValuation.jobInstruction = jobSetup.jobInstruction;
                        self.homeValuation.reportType = jobSetup.reportType;
                        self.homeValuation.purpose = jobSetup.purpose;
                        self.homeValuation.valuer = jobSetup.valuer;
                        self.homeValuation.countersigner = jobSetup.countersigner;
                        self.homeValuation.qvOffice = jobSetup.qvOffice;
                        self.homeValuation.peerReview = jobSetup.peerReview;
                        self.homeValuation.clientReference = jobSetup.clientReference;
                        self.homeValuation.valuationDueDate = jobSetup.valuationDueDate;
                        self.homeValuation.inspectionDate = jobSetup.inspectionDate;
                        self.homeValuation.extraPropertyDetails = jobSetup.extraPropertyDetails;
                        self.homeValuation.propertyDetails = jobSetup.propertyDetails;
                        self.homeValuation.websiteUserId = jobSetup.websiteUserId;
                        self.homeValuation.eqcFields = jobSetup.eqcFields;
                    }
                    if (jobSetup.completeSetup) {
                        if (self.runAllValidations('S'), false) {
                            self.assignStatus(jobSetup.completeSetup ? "R" : self.homeValuation.status ? self.homeValuation.status.code : "S");
                            if(jobSetup.persist && !jobSetup.hasError) {
                                self.saveHomeValuation(1, function () {
                                    if (jobSetup.completeSetup) {
                                        $('.propertyDetailsStepper').click();
                                        self.activeStep = 2;
                                    }
                                });
                            }
                        }
                        else {
                            EventBus.$emit('enable-buttons');
                        }
                    }
                    else {
                        if (jobSetup.persist && !jobSetup.hasError) {
                            self.saveHomeValuation(1)
                        }
                    }
                }
            });

            EventBus.$on('home-valuation-property-details', function (event) {
                self.step2isDirty = event.isDirty;
                if(!event.isDirty) {
                    EventBus.$emit('enable-buttons');
                    self.nextStep(event.next, 2);
                } else {
                    self.assignStatus(self.homeValuation.status ? self.homeValuation.status.code : "S");
                    self.homeValuation.propertyDetails = event.propertyDetails;
                    if (event.persist) {
                        self.saveHomeValuation(2, function () {
                            self.nextStep(event.next, 2);
                        });
                    }
                }
            });

            EventBus.$on('home-valuation-comparable-properties', function (event) {
                self.step3isDirty = event.isDirty;
                if(!event.isDirty) {
                    EventBus.$emit('enable-buttons');
                    self.nextStep(event.next, 3);
                } else {
                    self.assignStatus(self.homeValuation.status ? self.homeValuation.status.code : "S");
                    self.homeValuation.comparableSales = event.comparableSales;
                    if (event.persist) {
                        self.saveHomeValuation(3, function () {
                            self.nextStep(event.next, 3);
                        });
                    }
                }
            });

            EventBus.$on('home-valuation-job-worksheet', function (event) {
                self.step4isDirty = event.isDirty;
                if(!event.isDirty) {
                    EventBus.$emit('enable-buttons');
                    self.nextStep(event.next, 4);
                } else {
                    self.homeValuation.worksheet = event.worksheet;
                    //Save the proposed worksheet as well if exists in the event
                    self.homeValuation.proposedWorksheet = event.proposedWorksheet ? event.proposedWorksheet : self.homeValuation.proposedWorksheet;
                    self.homeValuation.incomeMethodWorksheet = event.incomeMethodWorksheet;
                    self.assignStatus(self.homeValuation.status ? self.homeValuation.status.code : "S");
                    if (event.persist) {
                        self.saveHomeValuation(4, function () {
                            self.nextStep(event.next, 4);
                        });
                    }
                }
            });

            EventBus.$on('home-valuation-report-details', function (event) {
                self.step5isDirty = event.isDirty;
                if(!event.isDirty) {
                    EventBus.$emit('enable-buttons');
                    self.nextStep(event.next, 5);
                } else {
                    self.assignStatus(self.homeValuation.status ? self.homeValuation.status.code : "S");
                    self.homeValuation.reportDetails = event.reportDetails;
                    if (event.persist) {
                        self.saveHomeValuation(5, function () {
                            self.nextStep(event.next, 5);
                        });
                    }
                }
            });

            EventBus.$on('home-valuation-location-details', function (event) {
                self.step5isDirty = event.isDirty;
                if(!event.isDirty) {
                    EventBus.$emit('enable-buttons');
                    self.nextStep(event.next, 6);
                } else {
                    self.assignStatus(self.homeValuation.status ? self.homeValuation.status.code : "S");
                    self.homeValuation.locationDetails = event.locationDetails;
                    if (event.persist) {
                        self.saveHomeValuation(6, function () {
                            self.nextStep(event.next, 6);
                        });
                    }
                }
            });

            EventBus.$on('home-valuation-photos-attachments', function (event) {
                if (event.next) {
                    $('.qaAndReviewStepper').click();
                }
            });

            EventBus.$on('home-valuation-qa-peer-review', function (event) {
                self.step8isDirty = event.isDirty;
                if(!event.isDirty) {
                    EventBus.$emit('enable-buttons');
                    self.nextStep(event.next, 6);
                } else {
                    self.assignStatus(event.status ? event.status : self.homeValuation.status ? self.homeValuation.status.code : "S");
                    self.homeValuation.qaDetails = event.qaDetails;
                    if (event.persist) {
                        self.saveHomeValuation(8);
                    }
                }
            });

            EventBus.$on('load-property', function (property) {
                self.defaultPropertyData = property;
            });
        },
        destroyed: function () {
            EventBus.$off('home-valuation-property-details', this.listener);
            EventBus.$off('home-valuation-comparable-properties', this.listener)
            EventBus.$off('home-valuation-report-details', this.listener);
            EventBus.$off('home-valuation-location-details', this.listener);
            EventBus.$off('load-property', this.listener);
            EventBus.$off('home-valuation-job-instruction', this.listener);
            EventBus.$off('home-valuation-job-display', this.listener);
            EventBus.$off('home-valuation-back', this.listener);
            EventBus.$off('home-valuation-job-worksheet', this.listener);
            EventBus.$off('home-valuation-photos-attachments', this.listener);
            EventBus.$off('home-valuation-qa-peer-review', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('home-valuation-property-details', this.listener);
            EventBus.$off('home-valuation-comparable-properties', this.listener)
            EventBus.$off('home-valuation-report-details', this.listener);
            EventBus.$off('home-valuation-location-details', this.listener);
            EventBus.$off('load-property', this.listener);
            EventBus.$off('home-valuation-job-instruction', this.listener);
            EventBus.$off('home-valuation-job-display', this.listener);
            EventBus.$off('home-valuation-back', this.listener);
            EventBus.$off('home-valuation-job-worksheet', this.listener);
            EventBus.$off('home-valuation-photos-attachments', this.listener);
            EventBus.$off('home-valuation-qa-peer-review', this.listener);
        }
    }
</script>

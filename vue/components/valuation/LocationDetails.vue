<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div>
        <div class="md-table mdl-shadow--2dp">
            <h2>Location Details</h2>
            <div data-cy="location-details-expand-all" class="expandAll" @click="expandTabs()" v-bind:class="[tabState == 'open' ? 'down' : '']">
                <span title="Expand Form" class="mdl-button mdl-js-button mdl-button--icon"><i
                        class="material-icons md-dark"></i></span>
            </div>
            <ul id="tabsElementLocationDetails" v-bind:class="[tabState=='open' ? 'hide' : 'QVHV-tabs']">
                <li class="QVHVTab-1" @click="setCurrentTab('Sale History', 1, true)" data-tab="QVHVTab-1"
                    data-container="salehistory"><span
                        v-bind:class="[currentTab == 'Sale History' || tabState == 'open' ? 'is-active' : '']">Sale History</span>
                </li>
                <li class="QVHVTab-2" @click="setCurrentTab('Zoning', 2, true)" data-tab="QVHVTab-2" data-container="zoning">
                    <span v-bind:class="[currentTab == 'Zoning' || tabState == 'open' ? 'is-active' : '']">Zoning</span>
                </li>
                <li class="QVHVTab-3" @click="setCurrentTab('Legal Description', 3, true)" data-tab="QVHVTab-3"
                    data-container="legaldescription"><span
                        v-bind:class="[currentTab == 'Legal Description' || tabState == 'open' ? 'is-active' : '']">Legal Description</span>
                </li>
                <li class="QVHVTab-4" @click="setCurrentTab('Site and Location', 4, true)" data-tab="QVHVTab-4"
                    data-container="siteandlocation"><span
                        v-bind:class="[currentTab == 'Site And Location' || tabState == 'open' ? 'is-active' : '']">Site and Location</span>
                </li>
                <li class="QVHVTab-5" @click="setCurrentTab('Rating Information', 5, true)" data-tab="QVHVTab-5"
                    data-container="ratinginformation"><span
                        v-bind:class="[currentTab == 'Rating Information' || tabState == 'open' ? 'is-active' : '']">Rating Information</span>
                </li>
                <hr align="left"/>
            </ul>
            <sale-history v-show="currentTab=='Sale History' || tabState == 'open'"
                          v-bind:class="{disabled: readOnly}"></sale-history>
            <zoning v-show="currentTab=='Zoning' || tabState == 'open'" v-bind:class="{disabled: readOnly}"></zoning>
            <legal-description v-show="currentTab=='Legal Description' || tabState == 'open'"
                               v-bind:class="{disabled: readOnly}"></legal-description>
            <site-and-location v-show="currentTab=='Site and Location' || tabState == 'open'"
                               v-bind:class="{disabled: readOnly}"></site-and-location>
            <rating-information v-show="currentTab=='Rating Information' || tabState == 'open'"
                                v-bind:class="{disabled: readOnly}"></rating-information>
        </div>
        <div class="QVHV-buttons" v-bind:class="{disabled: readOnly}">
            <div class="QVHV-buttons-left" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="primary" v-on:click="saveLocationDetailsData(false, true)">Save</button>
            </div>
            <div class="QVHV-buttons-right" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="secondary" v-on:click="previousStep()">Back</button>
                <button class="primary" v-on:click="saveLocationDetailsData(true, true)">Next Step</button>
            </div>
        </div>
    </div>
</template>

<script>
    import {EventBus} from '../../EventBus.js';
    import {store} from '../../DataStore';
    import deepEqual from 'deep-equal';
    import SaleHistory from './locationDetails/SaleHistory.vue';
    import Zoning from './locationDetails/Zoning.vue';
    import LegalDescription from './locationDetails/LegalDescription.vue';
    import SiteAndLocation from './locationDetails/SiteAndLocation.vue';
    import RatingInformation from './locationDetails/RatingInformation.vue';
    import moment from 'moment';

    export default {
        props: ['readOnly'],
        components: {
            SaleHistory,
            Zoning,
            LegalDescription,
            SiteAndLocation,
            RatingInformation
        },
        data: function () {
            return {
                locationDetails: {
                    saleSummaries: [],
                    saleComments: "",
                    currentUse: {},
                    operativeZoningDetails: {},
                    proposedZoningDetails: {},
                    legalDetails: {},
                    siteDetails: {},
                    ratingDetails: {}
                },
                currentTab: 'Sale History',
                tabState: 'closed',
                isDataSaving: false,
                homeValuation: {},
                locationDetailsCopy: null,
                isDirty: false
            }
        },
        methods: {
            expandTabs: function () {
                const self = this;
                if (self.tabState === 'closed') {
                    self.tabState = 'open';
                }
                else {
                    self.tabState = 'closed';
                }
                EventBus.$emit('location-details-tabs', self.tabState);
            },
            saveLocationDetailsData: function (nextStep, persist) {
                const self = this;
                var event = {};
                event.locationDetails = self.locationDetails;
                event.next = nextStep;
                self.isDataSaving = persist;
                event.persist = persist;

                var haveChanges = !self.jsonEqual(self.locationDetails, self.locationDetailsCopy);
                event.isDirty = haveChanges;
                EventBus.$emit('home-valuation-location-details', event);
            },
            previousStep: function () {
                const self = this
                EventBus.$emit("home-valuation-back", "reportDetailsStepper");
                self.saveLocationDetailsData(false, true)
            },
            setCurrentTab: function (name, index, save) {
                const self = this;
                self.currentTab = name;
                $('#tabsElementLocationDetails').find('hr')
                    .removeClass()
                    .addClass('QVHVTab-' + index);
                if (save) {
                    setTimeout(function() {
                        self.saveLocationDetailsData(false, true)
                    }, 1000);
                }
            },
            jsonEqual: function jsonEqual(a,b) {
                return deepEqual(a,b);
            }
        },
        created: function () {
            var self = this;
            EventBus.$on('enable-buttons', function(){
                self.isDataSaving = false
            })
            EventBus.$on('notify-location-parent', function (obj) {
                for (var i in obj) {
                    var k = obj[i].key;
                    self.locationDetails[k] = obj[i].value;
                }
                self.saveLocationDetailsData(false, false);
            });
            EventBus.$on('home-valuation-saved', function (obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 6 || (self.homeValuation == {} || self.homeValuation.id != obj.homeValuation.id) || obj.reload) {
                    self.homeValuation = JSON.parse(JSON.stringify(obj.homeValuation));
                    self.locationDetails = self.homeValuation.locationDetails  ? self.homeValuation.locationDetails : {};
                }
                self.locationDetailsCopy = JSON.parse(JSON.stringify(self.locationDetails));
                self.isDataSaving = false;
            });
            EventBus.$on('home-valuation-new', function (propertyData) {
                self.setCurrentTab('Sale History', 1, false);
                self.locationDetails = {};
                self.locationDetails.saleSummaries = [];
                self.locationDetails.saleComments = "";
                self.locationDetails.currentUse = {};
                self.locationDetails.operativeZoningDetails = {};
                self.locationDetails.proposedZoningDetails = {};
                self.locationDetails.legalDetails = {};
                self.locationDetails.siteDetails = {};
                self.locationDetails.ratingDetails = {};
            });
            EventBus.$on('home-valuation-loaded', function () {
                self.setCurrentTab('Sale History', 1, false);
            });
        },
        destroyed: function () {
            EventBus.$off('notify-location-parent', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('notify-location-parent', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            EventBus.$off('home-valuation-loaded', this.listener);
        }
    }
</script>

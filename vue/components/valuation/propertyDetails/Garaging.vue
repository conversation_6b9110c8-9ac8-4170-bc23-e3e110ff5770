<template>
    <div class="QVHV-Container garaging active" v-bind:class="[tabState=='open' ? 'canOpener' : '']" v-if="showGaragingTemplate">
        <!-- ^^ THE ELEMENT ".QVHV-Container" SHOULD GET THE .canOpener CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->

        <ul class="QVHV-tabs">
            <li><span class="is-active">Garaging</span></li>
            <hr align="left"/>
        </ul>

        <div class="QVHV-formSection">

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row" v-for="garageType,key in garaging">
                <div class="advSearch-Subrow">
                    <valuation-multi-select-filter :class="[fields.garaging.buildingType]" :obj-key="key+1" :curr-val="garageType.buildingType" iconClass="twentyfivePct icons8-garage-filled" component-name="garaging" parentAttrName="garageTypes" attrName="buildingType" :filter-id="'garageType'+(key+1)" label="Garage Type" :selectClass="'monarch-multiselect garageTypes'+(key+1)" data-to-fetch="GarageType"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.garaging.descriptions]" :obj-key="key+1" :curr-val="garageType.descriptions" iconClass="seventyfivePct-tenRem icons8-todo-list-filled" component-name="garaging" parentAttrName="garageTypes" attrName="descriptions" :filter-id="'garageTypeDesc'+(key+1)" label="Description" :selectClass="'monarch-multiselect garageTypesDesc'+(key+1)" multiple="true" data-to-fetch="GarageTypeDescription"></valuation-multi-select-filter>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <valuation-multi-select-filter :class="[fields.garaging.age]" :obj-key="key+1" :curr-val="garageType.age" iconClass="twentyfivePct-threeRem icons8-calendar-filled" component-name="garaging" parentAttrName="garageTypes" attrName="age" :filter-id="'garageTypeAge'+(key+1)" label="Garage Age" :selectClass="'monarch-multiselect garageTypesAge'+(key+1)" request-type="POST" data-to-fetch="GarageAge"></valuation-multi-select-filter>
                    <text-input :class="[fields.garaging.floorArea]" maxlength="5" decimal="1" :obj-key="key+1" :curr-val="garageType.floorArea" attr-name="floorArea" fieldType="number" iconClass="twentyfivePct-threeRem icons8-floor-plan-filled" label="Garage Floor Area" component-name="garaging" parentAttrName="garageTypes"></text-input>
                    <valuation-multi-select-filter :class="[fields.garaging.modernisation]" :obj-key="key+1" :curr-val="garageType.modernisation" iconClass="twentyfivePct-threeRem icons8-maintenance-filled" component-name="garaging" parentAttrName="garageTypes" attrName="modernisation" :filter-id="'garageTypeModernisation'+(key+1)" label="Modernisation" :selectClass="'monarch-multiselect garageTypeModernisation'+(key+1)" data-to-fetch="Modernisation"></valuation-multi-select-filter>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <valuation-multi-select-filter :class="[fields.garaging.externalWallConstruction]" :obj-key="key+1" :curr-val="garageType.externalWallConstruction" iconClass="twentyfivePct-threeRem icons8-brick-wall-filled" component-name="garaging" parentAttrName="garageTypes" attrName="externalWallConstruction" :filter-id="'garageTypeExternalWallConstruction'+(key+1)" label="Exterior Cladding" :selectClass="'monarch-multiselect garageTypeExternalWallConstruction'+(key+1)" multiple="true" data-to-fetch="ExteriorCladding"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.garaging.roofConstruction]" :obj-key="key+1" :curr-val="garageType.roofConstruction" iconClass="twentyfivePct-threeRem icons8-structural-filled" component-name="garaging" parentAttrName="garageTypes" attrName="roofConstruction" :filter-id="'garageTypeRoofConstruction'+(key+1)" label="Roof Construction" :selectClass="'monarch-multiselect garageTypeRoofConstruction'+(key+1)" multiple="true" data-to-fetch="RoofConstruction"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.garaging.foundation]" :obj-key="key+1" :curr-val="garageType.foundation" iconClass="twentyfivePct-threeRem icons8-foundation-new" component-name="garaging" parentAttrName="garageTypes" attrName="foundation" :filter-id="'garageTypeFoundation'+(key+1)" label="Foundation" :selectClass="'monarch-multiselect garageTypeFoundation'+(key+1)" multiple="true" data-to-fetch="Foundation"></valuation-multi-select-filter>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <text-input maxlength="1000" :class="[fields.garaging.notes]" :obj-key="key+1" :curr-val="garageType.notes" attr-name="notes" fieldType="text" iconClass="seventyfivePct-tenRem icons8-pencil" label="Notes" component-name="garaging" parentAttrName="garageTypes"></text-input>
                    <div class="advSearch-group sa-addRemove">
                        <i class="saRow-add addLivingArea material-icons" @click="addGarageType(key)"></i>
                        <i v-if="key>0" class="saRow-remove material-icons" @click="removeGarageType(key)"></i>
                    </div>
                </div>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row" v-for="otherBuilding,key in otherBuildings">
                <div class="advSearch-Subrow">
                    <valuation-multi-select-filter :class="[fields.otherBuildings.buildingType]" :obj-key="key+1" :curr-val="otherBuilding.buildingType" iconClass="twentyfivePct icons8-garage-filled" component-name="otherBuildings" parentAttrName="otherBuildings" attrName="buildingType" :filter-id="'otherBuilding'+(key+1)" label="Other Buildings" :selectClass="'monarch-multiselect otherBuildings'+(key+1)" data-to-fetch="OtherBuildings"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.otherBuildings.descriptions]" :obj-key="key+1" :curr-val="otherBuilding.descriptions" iconClass="seventyfivePct-tenRem icons8-todo-list-filled" component-name="otherBuildings" parentAttrName="otherBuildings" attrName="descriptions" :filter-id="'otherBuildingDesc'+(key+1)" label="Description" :selectClass="'monarch-multiselect otherBuildingsDesc'+(key+1)" multiple="true" data-to-fetch="OtherBuildingsDescription"></valuation-multi-select-filter>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <valuation-multi-select-filter :class="[fields.otherBuildings.age]" :obj-key="key+1" :curr-val="otherBuilding.age" iconClass="twentyfivePct-threeRem icons8-calendar-filled" component-name="otherBuildings" parentAttrName="garageTypes" attrName="age" :filter-id="'otherBuildingAge'+(key+1)" label="Age" :selectClass="'monarch-multiselect otherBuildingAge'+(key+1)" request-type="POST" data-to-fetch="OtherBuildingsAge"></valuation-multi-select-filter>
                    <text-input :class="[fields.otherBuildings.floorArea]" :obj-key="key+1" maxlength="5" decimal="1" :curr-val="otherBuilding.floorArea" attr-name="floorArea" fieldType="number" iconClass="twentyfivePct-threeRem icons8-floor-plan-filled" label="Floor Area" component-name="otherBuildings" parentAttrName="otherBuildings"></text-input>
                    <valuation-multi-select-filter :class="[fields.otherBuildings.modernisation]" :obj-key="key+1" :curr-val="otherBuilding.modernisation" iconClass="twentyfivePct-threeRem icons8-maintenance-filled" component-name="otherBuildings" parentAttrName="otherBuildings" attrName="modernisation" :filter-id="'otherBuildingModernisation'+(key+1)" label="Modernisation" :selectClass="'monarch-multiselect otherBuildingModernisation'+(key+1)" data-to-fetch="Modernisation"></valuation-multi-select-filter>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <valuation-multi-select-filter :class="[fields.otherBuildings.externalWallConstruction]" :obj-key="key+1" :curr-val="otherBuilding.externalWallConstruction" iconClass="twentyfivePct-threeRem icons8-brick-wall-filled" component-name="otherBuildings" parentAttrName="otherBuildings" attrName="externalWallConstruction" :filter-id="'otherBuildingExternalWallConstruction'+(key+1)" label="Exterior Cladding" :selectClass="'monarch-multiselect otherBuildingExternalWallConstruction'+(key+1)" multiple="true" data-to-fetch="ExteriorCladding"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.otherBuildings.roofConstruction]" :obj-key="key+1" :curr-val="otherBuilding.roofConstruction" iconClass="twentyfivePct-threeRem icons8-structural-filled" component-name="otherBuildings" parentAttrName="otherBuildings" attrName="roofConstruction" :filter-id="'otherBuildingRoofConstruction'+(key+1)" label="Roof Construction" :selectClass="'monarch-multiselect otherBuildingRoofConstruction'+(key+1)" multiple="true" data-to-fetch="RoofConstruction"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.otherBuildings.foundation]" :obj-key="key+1" :curr-val="otherBuilding.foundation" iconClass="twentyfivePct-threeRem icons8-foundation-new" component-name="otherBuildings" parentAttrName="otherBuildings" attrName="foundation" :filter-id="'otherBuildingFoundation'+(key+1)" label="Foundation" :selectClass="'monarch-multiselect otherBuildingFoundation'+(key+1)" multiple="true" data-to-fetch="Foundation"></valuation-multi-select-filter>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <text-input :class="[fields.otherBuildings.notes]" maxlength="1000" :obj-key="key+1" :curr-val="otherBuilding.notes" attr-name="notes" fieldType="text" iconClass="seventyfivePct-tenRem icons8-pencil" label="Notes" component-name="otherBuildings" parentAttrName="otherBuildings"></text-input>
                    <div class="advSearch-group sa-addRemove">
                        <i class="saRow-add addLivingArea material-icons" @click="addOtherBuilding(key)"></i>
                        <i v-if="key>0" class="saRow-remove material-icons" @click="removeOtherBuilding(key)"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    import TextInput from '../../filters/TextInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import { EventBus } from '../../../EventBus.js';
    import { store } from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            ValuationMultiSelectFilter
        },
        data: function() {
            return {
                garaging: [],
                otherBuildings: [],
                showGaragingTemplate: true,
                refreshGaragingTemplate: false,
                tabState: 'closed',
                reportType: {},
                fields: {
                    garaging: {},
                    otherBuildings: {}
                }
            }
        },
        methods: {
            notifyParent: function(key, value){
                const self = this
                EventBus.$emit('notify-parent', {'key':key, 'value':value})
            },
            refreshView: function() {
                const self = this
                self.showGaragingTemplate = !self.showGaragingTemplate
                 if (!self.showGaragingTemplate) {
                    self.refreshGaragingTemplate = true
                 }
            },
            addGarageType: function(key) {
                const self = this
                if (!self.garaging) {
                    self.garaging = []
                }
                self.garaging.splice(key+1, 0, {})
                self.refreshView();
            },
            removeGarageType: function(key){
                const self = this
                self.garaging.splice(key, 1);
                self.refreshView();
            },
            addOtherBuilding: function(key) {
                const self = this
                if (!self.otherBuildings) {
                    self.otherBuildings = []
                }
                self.otherBuildings.splice(key+1, 0, {})
                self.refreshView();
            },
            removeOtherBuilding: function(key){
                const self = this
                self.otherBuildings.splice(key, 1);
                self.refreshView();
            }
        },
        mounted: function() {
            const self = this
            EventBus.$on('notify-simple-nested-garaging', function(data){
                self.garaging[data.key-1][data.attrName] = data.val
                self.notifyParent('garagingDetails', self.garaging);
            })
            EventBus.$on('notify-multi-nested-garaging', function(data){
                self.garaging[(data.key-1)][data.attrName] = data.val
                self.notifyParent('garagingDetails', self.garaging);
            })
            EventBus.$on('notify-simple-nested-otherBuildings', function(data){
                self.otherBuildings[data.key-1][data.attrName] = data.val
                self.notifyParent('otherBuildingDetails', self.otherBuildings);
            })
            EventBus.$on('notify-multi-nested-otherBuildings', function(data){
                self.otherBuildings[(data.key-1)][data.attrName] = data.val
                self.notifyParent('otherBuildingDetails', self.otherBuildings);
            })
        },
        created: function() {
            var self = this;
            EventBus.$on('home-valuation-saved', function(obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 2 || obj.reload) {
                    var homeValuation = obj.homeValuation
                    self.garaging = homeValuation.propertyDetails ? (homeValuation.propertyDetails.garagingDetails ? homeValuation.propertyDetails.garagingDetails : []) : []
                    self.otherBuildings = homeValuation.propertyDetails ? (homeValuation.propertyDetails.otherBuildingDetails ? homeValuation.propertyDetails.otherBuildingDetails : []) : []
                    if (!self.garaging || self.garaging.length == 0) {
                        self.addGarageType(0)
                    }
                    if (!self.otherBuildings || self.otherBuildings.length == 0) {
                        self.addOtherBuilding(0)
                    }
                    self.showGaragingTemplate = false
                    self.refreshGaragingTemplate = true
                }
            });

            EventBus.$on('home-valuation-new', function(propertyData) {
                console.log("home-valuation-new");
                self.garaging = []
                self.otherBuildings = []
                self.addGarageType(0)
                self.addOtherBuilding(0)
                self.showGaragingTemplate = false
                self.refreshGaragingTemplate = true
            });

            EventBus.$on('property-details-tabs', function(state){
                self.tabState = state
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                   self.fields = fields.propertyDetails.garaging;
                }
            });
        },
        updated: function() {
            const self = this
            if (self.refreshGaragingTemplate) {
                self.showGaragingTemplate = true
                self.refreshGaragingTemplate = false
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-nested-garaging', this.listener);
            EventBus.$off('notify-multi-nested-garaging', this.listener);
            EventBus.$off('notify-simple-nested-otherBuildings', this.listener);
            EventBus.$off('notify-multi-nested-otherBuildings', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-nested-garaging', this.listener);
            EventBus.$off('notify-multi-nested-garaging', this.listener);
            EventBus.$off('notify-simple-nested-otherBuildings', this.listener);
            EventBus.$off('notify-multi-nested-otherBuildings', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>
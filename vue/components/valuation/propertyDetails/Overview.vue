<template>
    <div class="QVHV-Container housedetails active" v-bind:class="[tabState=='open' ? 'canOpener' : '']" v-if="showOverviewTemplate">
        <!-- ^^ THE ELEMENT ".QVHV-Container" SHOULD GET THE .canOpener CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->
        <ul class="QVHV-tabs">
            <li><span class="is-active">Overview</span></li>
            <hr align="left"/>
        </ul>
        <div class="QVHV-formSection">
            <div class="advSearch-row">
                <text-input :class="[fields.numberOfBedrooms]" maxlength="2" id="overviewNumberOfBedrooms" :curr-val="overview.numberOfBedrooms" attr-name="numberOfBedrooms" fieldType="number" iconClass="icons8-bed-filled" label="Bedrooms" component-name="overview"></text-input>
                <text-input :class="[fields.numberOfBathrooms]" maxlength="2" id="overviewNumberOfBathrooms" :curr-val="overview.numberOfBathrooms" attr-name="numberOfBathrooms" fieldType="number" iconClass="icons8-total-bathrooms-filled" label="Bathrooms" component-name="overview"></text-input>
                <text-input :class="[fields.numberOfToilets]" maxlength="2" id="overviewNumberOfToilets" :curr-val="overview.numberOfToilets" attr-name="numberOfToilets" fieldType="number" iconClass="icons8-toilet-bowl-filled" label="Toilets" component-name="overview"></text-input>
                <text-input :class="[fields.numberOfLivingAreas]" maxlength="2" id="overviewNumberOfLivingAreas" :curr-val="overview.numberOfLivingAreas" attr-name="numberOfLivingAreas" fieldType="number" iconClass="icons8-living-areas-new" label="Living Areas" component-name="overview"></text-input>
                <text-input :class="[fields.garaging]" maxlength="2" id="overviewNumberOfGaraging" :curr-val="overview.garaging" attr-name="garaging" fieldType="number" iconClass="icons8-garage-filled" label="Garaging" component-name="overview"></text-input>
                <text-input :class="[fields.offstreetParking]" maxlength="2" id="overviewNumberOfOffstreetParking" :curr-val="overview.offstreetParking" attr-name="offstreetParking" fieldType="number" iconClass="icons8-traffic-jam-filled" label="Offstreet Parking" component-name="overview"></text-input>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.houseType]" id="overviewHouseType" :curr-val="overview.houseType" iconClass="div.advSearch-group thirtythreePct icons8-house-filled" component-name="overview" attrName="houseType" filter-id="houseType" label="House Type" selectClass="monarch-multiselect housetype" chooseHere="true" data-to-fetch="HouseType_HV"></valuation-multi-select-filter>
                <text-input maxlength="4" :class="[fields.estimatedYearOfConstruction]"  id="overviewEstimatedYearOfConstruction" :curr-val="overview.estimatedYearOfConstruction" attr-name="estimatedYearOfConstruction" fieldType="number" iconClass="sixteenPct icons8-calendar-filled" label="Year Built" component-name="overview"></text-input>
                <text-input :class="[fields.totalFloorArea]" maxlength="7" decimal="1"
                            id="overviewTotalFloorArea" :curr-val="overview.totalFloorArea"
                            attr-name="totalFloorArea" fieldType="number"
                            iconClass="sixteenPct icons8-floor-plan-filled"
                            label="Total Floor Area" component-name="overview"
                            :errorMsg="error.floorArea.errorMessage">
                </text-input>
                <text-input id="totalFloorDescription" :class="[fields.totalFloorDescription]" :curr-val="overview.totalFloorDescription" attr-name="totalFloorDescription" fieldType="text" iconClass="advSearch-group thirtythreePct icons8-pencil" label="Total Floor Area Description" component-name="overview"></text-input>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.exteriorCladding]" id="overviewExteriorCladding" :curr-val="overview.exteriorCladding" iconClass="thirtythreePct icons8-rating-filled" component-name="overview" attrName="exteriorCladding" filter-id="externalWallConstruction" label="Exterior Cladding" selectClass="monarch-multiselect external-wall-construction" multiple="true" chooseHere="true" data-to-fetch="ExteriorCladding"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.foundation]" id="overviewFoundation" :curr-val="overview.foundation" iconClass="thirtythreePct icons8-foundation-new" component-name="overview" attrName="foundation" filter-id="foundation" label="Foundation" selectClass="monarch-multiselect foundation" chooseHere="true" data-to-fetch="Foundation" multiple="true"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.joinery]" id="overviewJoinery" :curr-val="overview.joinery" iconClass="thirtythreePct icons8-structural-filled" component-name="overview" attrName="joinery" filter-id="joinery" label="Joinery" selectClass="monarch-multiselect joinery" chooseHere="true" data-to-fetch="Joinery" multiple="true"></valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.roofStyle]" id="overviewRoofStyle" :curr-val="overview.roofStyle" iconClass="thirtythreePct icons8-structural-filled" component-name="overview" attrName="roofStyle" filter-id="roofStyle" label="Roof Style" selectClass="monarch-multiselect roof-style" chooseHere="true" data-to-fetch="RoofStyle" multiple="true"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.roofConstruction]" id="overviewRoofConstruction" :curr-val="overview.roofConstruction" iconClass="thirtythreePct icons8-structural-filled" component-name="overview" attrName="roofConstruction" filter-id="roofConstruction" label="Roof Construction" selectClass="monarch-multiselect roof-construction" multiple="true" chooseHere="true" data-to-fetch="RoofConstruction"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.spouting]" id="overviewSpouting" :curr-val="overview.spouting" iconClass="thirtythreePct icons8-structural-filled" component-name="overview" attrName="spouting" filter-id="spouting" label="Spouting" selectClass="monarch-multiselect spouting" chooseHere="true" data-to-fetch="Spouting" multiple="true"></valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.externalCondition]" id="overviewExternalCondition" :curr-val="overview.externalCondition" iconClass="fiftyPct icons8-rating-filled" component-name="overview" attrName="externalCondition" filter-id="qoep" label="External Condition" selectClass="monarch-multiselect quality-of-external-pres" chooseHere="false" multiple="true" data-to-fetch="ExternalCondition"></valuation-multi-select-filter>
                <valuation-multi-select-filter
                        id="overviewQualityOfExternalPresentation"
                        :curr-val="overview.qualityOfExternalPresentation"
                        iconClass="fiftyPct icons8-rating-filled"
                        component-name="overview"
                        attrName="qualityOfExternalPresentation"
                        filter-id="qualityOfExternalPresentation"
                        label="Quality of External Presentation"
                        selectClass="monarch-multiselect quality-of-external-presentation"
                        chooseHere="true"
                        data-to-fetch="QualityofExternalPresentation"
                        requestType="POST"
                        :errorMessage="error.qualExternal.errorMessage"
                        :class="[fields.qualityOfExternalPresentation]">
                </valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.internalCondition]" id="overviewInternalCondition" :curr-val="overview.internalCondition" iconClass="fiftyPct icons8-rating-filled" component-name="overview" attrName="internalCondition" filter-id="qoip" label="Internal Condition" selectClass="monarch-multiselect quality-of-internal-pres" chooseHere="false" multiple="true" data-to-fetch="InternalCondition_HV"></valuation-multi-select-filter>
                <valuation-multi-select-filter
                        id="overviewQualityOfInternalPresentation"
                        :curr-val="overview.qualityOfInternalPresentation"
                        iconClass="fiftyPct icons8-rating-filled"
                        component-name="overview"
                        attrName="qualityOfInternalPresentation"
                        filter-id="qualityOfInternalPresentation"
                        label="Quality of Internal Presentation"
                        selectClass="monarch-multiselect quality-of-internal-presentation"
                        chooseHere="true"
                        data-to-fetch="QualityofInternalPresentation"
                        requestType="POST"
                        :errorMessage="error.qualInternal.errorMessage"
                        :class="[fields.qualityOfInternalPresentation]">
                </valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.standardOfAccommodation]" id="overviewStandardOfAccommodation" :curr-val="overview.standardOfAccommodation" iconClass="fiftyPct icons8-rating-filled" component-name="overview" attrName="standardOfAccommodation" filter-id="standardOfAccommodation" label="Standard of Accommodation" selectClass="monarch-multiselect standard-of-accommodation" chooseHere="true" data-to-fetch="StandardOfAccommodation"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.layoutDescription]" id="overviewLayoutDesccription" :curr-val="overview.layoutDescriptions" iconClass="fiftyPct icons8-rating-filled" component-name="overview" attrName="layoutDescriptions" filter-id="qualityOfLayout" label="Layout Description" selectClass="monarch-multiselect quality-of-layout" multiple="true" data-to-fetch="LayoutDescription"></valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <text-area-input 
                        :class="[fields.maintenanceRequired]" 
                        maxlength="5000" 
                        :curr-val="overview.maintenanceRequired" 
                        attr-name="maintenanceRequired"
                        fieldType="text" 
                        iconClass="hundyPct icons8-maintenance-filled" 
                        label="Maintenance Required"
                        component-name="overview">
                </text-area-input>
            </div>
            <div class="advSearch-row">
                <text-input :class="[fields.immediateMaintenanceRequired]" id="overviewImmediateMaintenanceRequired" maxlength="1200" :curr-val="overview.immediateMaintenanceRequired" attr-name="immediateMaintenanceRequired" fieldType="text" iconClass="hundyPct icons8-brake-warning-filled" label="Immediate Maintenance Required" component-name="overview"></text-input>
            </div>
            <div class="advSearch-row">
                <text-input :class="[fields.recentAlterations]" id="overviewRecentAlterations" maxlength="1000" :curr-val="overview.recentAlterations" attr-name="recentAlterations" fieldType="text" iconClass="hundyPct icons8-maintenance-filled" label="Recent Alterations" component-name="overview"></text-input>
            </div>
            <div class="advSearch-row">
                <text-input :class="[fields.codeCompliance]" id="overviewCodeCompliance" maxlength="500" :curr-val="overview.codeCompliance" attr-name="codeCompliance" fieldType="text" iconClass="hundyPct icons8-paste-special-filled" label="Code Compliance" component-name="overview"></text-input>
            </div>
            <div class="advSearch-row">
                <text-input :class="[fields.notes]" id="overviewNotes" maxlength="1000" :curr-val="overview.notes" attr-name="notes" fieldType="text" iconClass="hundyPct icons8-pencil" label="Notes" component-name="overview"></text-input>
            </div>
        </div>
    </div>
</template>
<script>
    import TextInput from '../../filters/TextInput.vue'
    import TextAreaInput from '../../filters/TextAreaInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import { EventBus } from '../../../EventBus.js';
    import { store } from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            TextAreaInput,
            ValuationMultiSelectFilter
        },
        data: function() {
            return {
                property: {},
                overview: {
                },
                showOverviewTemplate: true,
                refreshOverviewTemplate : false,
                tabState: 'closed',
                defaultPropertyData: undefined,
                isHeartlandBank: false,
                error: {
                    qualExternal: {
                        errorMessage: ''
                    },
                    qualInternal: {
                        errorMessage: ''
                    },
                    floorArea: {
                        errorMessage: ''
                    }
                },
                reportType:{},
                fields: {}

            }
        },
        methods: {
            notifyParent: function(){
                const self = this
                EventBus.$emit('notify-parent', {'key':'houseDetails', 'value':self.overview})
            },
            populateDefaultData: function(data) {
                const self = this

                if (!self.overview.numberOfBedrooms) {
                    self.overview.numberOfBedrooms = $.isNumeric(data.bedrooms)?data.bedrooms:null;
                }

                if (!self.overview.numberOfToilets) {
                    self.overview.numberOfToilets = $.isNumeric(data.toilets)?data.toilets:null;
                }


                if (!self.overview.garaging) {
                    if(($.isNumeric(data.freeStandingGarages) && $.isNumeric(data.underMainRoofGarages))) {
                        self.overview.garaging = (data.freeStandingGarages+data.underMainRoofGarages);
                    } else if($.isNumeric(data.freeStandingGarages)) {
                        self.overview.garaging = data.freeStandingGarages;
                    }else if($.isNumeric(data.underMainRoofGarages)){
                        self.overview.garaging = data.underMainRoofGarages;
                    }
                }


                if (!self.overview.offstreetParking) {
                    self.overview.offstreetParking = $.isNumeric(data.carParks)?data.carParks:null;
                }

                if (!self.overview.estimatedYearOfConstruction) {
                    self.overview.estimatedYearOfConstruction = $.isNumeric(data.effectiveYearBuilt)?data.effectiveYearBuilt:null;
                }

                if (!self.overview.totalFloorArea) {
                    self.overview.totalFloorArea = $.isNumeric(data.TFA)?data.TFA:null;
                }

                if (!self.overview.exteriorCladding) {
                    var exteriorCladdingList = self.$store.getters.getCategoryClassifications('ExteriorCladding');
                    if($.type(data.wallConstruction)==='object'){
                        var wallConstruction = data.wallConstruction;
                        var exists = exteriorCladdingList.filter(function(e) { return e.code == wallConstruction.code; });
                        if(exists.length > 0) {
                            self.overview.exteriorCladding = exists;
                        }
                    }else if($.type(data.wallConstruction)==='array'){
                        var wallConstructions = [];
                        $.each(data.wallConstruction, function (i, obj) {
                            var exists = exteriorCladdingList.filter(function(e) { return e.code == obj.code; });
                            if(exists.length > 0) {
                                wallConstructions.push(exists[0]);
                            }
                        });
                        self.overview.exteriorCladding = wallConstructions;
                    }
                }


                if (!self.overview.roofConstruction) {
                    var roofConstructionList = self.$store.getters.getCategoryClassifications('RoofConstruction');
                    if($.type(data.roofConstruction)==='object'){
                        var roofConstruction = data.roofConstruction;
                        var exists = roofConstructionList.filter(function(e) { return e.code == roofConstruction.code; });
                        if(exists.length > 0) {
                            self.overview.roofConstruction = exists;
                        }
                    }else if($.type(data.roofConstruction)==='array'){
                        var roofConstructions = [];
                        $.each(data.roofConstruction, function (i, obj) {
                            var exists = roofConstructionList.filter(function(e) { return e.code == obj.code; });
                            if(exists.length > 0) {
                                roofConstructions.push(exists[0]);
                            }
                        });
                        self.overview.roofConstruction = roofConstructions;
                    }
                }

                if (!self.overview.houseType) {
                    var houseTypeList = self.$store.getters.getCategoryClassifications('HouseType_HV');
                    if($.type(data.houseTypeObj)==='object') {
                        if (data.houseTypeObj.code && (data.houseTypeObj.code == 'QB' || data.houseTypeObj.code == 'SR' || data.houseTypeObj.code == 'QO')) {
                            data.houseTypeObj.code = 'BN'
                        }
/*                        if (data.houseTypeObj.code && (data.houseTypeObj.code == 'SR')) {
                            data.houseTypeObj.code = 'BN'
                        }*/
                        var exists = houseTypeList.filter(function (e) {
                            return e.code == data.houseTypeObj.code;
                        });
                        if (exists.length > 0) {
                            self.overview.houseType = exists[0];
                        }
                    }
                }

                self.populateCodeCompliance();
                self.populateTotalFloorAreaDescription();
                self.notifyParent();
            },
            populateCodeCompliance: function () {
                const self = this;
                var codeCompliance = self.$store.getters.getCategoryClassifications('CodeCompliance');
                if(codeCompliance[0]) {
                    self.overview.codeCompliance = codeCompliance[0].description;
                    self.notifyParent();
                    self.refreshView();
                }
            },
            populateTotalFloorAreaDescription:  function() {
                const self = this
                var totalFloorArea = self.overview.totalFloorArea
                if (totalFloorArea) {
                    self.overview.totalFloorDescription = self.overview.totalFloorArea+' m²';
                    self.notifyParent()
                    self.refreshView()
                }

            },
            refreshView: function () {
                const self = this;
                self.showOverviewTemplate = !self.showOverviewTemplate;
                if (!self.showOverviewTemplate) {
                    self.refreshOverviewTemplate = true;
                }
            },
            setHeartlandBankFields: function() {
                console.log("in setHeartlandBankFields");
                const self = this;

                self.error.qualExternal.errorMessage = "";
                self.error.qualInternal.errorMessage = "";
                self.error.floorArea.errorMessage = "";
                if(!self.overview.qualityOfExternalPresentation || (self.overview.qualityOfExternalPresentation == "")) {
                    self.error.qualExternal.errorMessage = "This field is required.";
                } else {
                    self.error.qualExternal.errorMessage = "";
                }

                if(!self.overview.qualityOfInternalPresentation || (self.overview.qualityOfInternalPresentation == "")) {
                    self.error.qualInternal.errorMessage = "This field is required.";
                } else {
                    self.error.qualInternal.errorMessage = "";
                }

                if(!self.overview.totalFloorArea || (self.overview.totalFloorArea == "")) {
                    self.error.floorArea.errorMessage = "This field is required.";
                } else {
                    self.error.floorArea.errorMessage = "";
                }
            }
        },
        mounted: function() {
            const self = this
            EventBus.$on('notify-simple-overview', function(data){
                self.overview[data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-multi-overview', function(data){
                self.overview[data.attrName] = data.val
                self.notifyParent();
            })
        },
        created: function() {
            var self = this;
            EventBus.$on('home-valuation-saved', function(obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 2 || obj.reload) {
                    var homeValuation = obj.homeValuation
                    self.overview = homeValuation.propertyDetails ? (homeValuation.propertyDetails.houseDetails ? homeValuation.propertyDetails.houseDetails : {}) : {}
//                    if (self.defaultPropertyData && obj.loadDefaultData) {
//                        self.populateDefaultData(self.defaultPropertyData)
//                    }
                    self.reportType = homeValuation && homeValuation.reportType ? homeValuation.reportType : ''
                    self.showOverviewTemplate = false
                    if (!self.showOverviewTemplate) {
                        self.refreshOverviewTemplate = true
                    }
                }
            });

            EventBus.$on('home-valuation-new', function(propertyData) {
                Object.assign(self.$data, self.$options.data.apply(self));
                self.overview = {}
                self.populateDefaultData(propertyData)
                self.defaultPropertyData = propertyData
                self.showOverviewTemplate = !self.showOverviewTemplate
                self.refreshOverviewTemplate = true
            });

            EventBus.$on('load-default-property-data', function(propertyData) {
                self.defaultPropertyData = propertyData.property
            });

            EventBus.$on('property-details-tabs', function(state){
                self.tabState = state
            })

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.error.qualExternal.errorMessage = "";
                self.error.qualInternal.errorMessage = "";
                self.error.floorArea.errorMessage = "";
                $('#overviewStandardOfAccommodation').removeClass('md-inactive');

                if (reportTypeCode == "HB") {
                    self.setHeartlandBankFields();
                } else if (reportTypeCode == "KS" || reportTypeCode == "KSA") {
                    $('#overviewStandardOfAccommodation').addClass('md-inactive');
                }
                self.reportType.code = reportTypeCode
                self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.propertyDetails.overview;
                }
            });
        },
        updated: function() {
            const self = this
            if (self.refreshOverviewTemplate) {
                self.showOverviewTemplate = true
                self.refreshOverviewTemplate = false
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-overview', this.listener);
            EventBus.$off('notify-multi-overview', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-overview', this.listener);
            EventBus.$off('notify-multi-overview', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('load-default-property-data', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

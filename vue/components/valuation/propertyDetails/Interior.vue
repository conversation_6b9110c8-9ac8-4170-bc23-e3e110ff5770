<template>
    <div class="QVHV-Container mainbuilding active" v-bind:class="[tabState=='open' ? 'canOpener' : '']" v-if="showInteriorTemplate">
        <!-- ^^ THE ELEMENT ".QVHV-Container" SHOULD GET THE .canOpener CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->

        <ul class="QVHV-tabs">
            <li><span class="is-active">Interior</span></li>
            <hr align="left"/>
        </ul>

        <div class="QVHV-formSection">

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row" v-for="livingArea,key in interior.livingAreas">
                <valuation-multi-select-filter :class="[fields.livingArea]" :obj-key="key+1" :curr-val="livingArea.key" iconClass="twentyfivePct icons8-living-areas-new" component-name="interior" parentAttrName="livingAreas" attrName="key" :filter-id="'livingArea'+(key+1)" :label="'Living Area '+(key+1)" :selectClass="'monarch-multiselect livingArea'+(key+1)" data-to-fetch="LivingArea"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.description]" :obj-key="key+1" :curr-val="livingArea.values" iconClass="seventyfivePct-tenRem icons8-todo-list-filled" component-name="interior" parentAttrName="livingAreas" attrName="values" :filter-id="'livingAreaDesc'+(key+1)" label="Description" :selectClass="'monarch-multiselect livingAreaDesc'+(key+1)" multiple="true" data-to-fetch="LivingAreaDescription"></valuation-multi-select-filter>
                <div class="advSearch-group sa-addRemove">
                    <i class="saRow-add addLivingArea material-icons" @click="addLivingArea(key)"></i>
                    <i v-if="key>0" class="saRow-remove material-icons" @click="removeLivingArea(key)"></i>
                </div>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.laundry]" :curr-val="interior.laundry" iconClass="hundyPct icons8-washing-machine-filled" component-name="interior" attrName="laundry" filter-id="laundry" label="Laundry" selectClass="monarch-multiselect laundry" multiple="true" data-to-fetch="Laundry"></valuation-multi-select-filter>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.internalWallLinings]" :curr-val="interior.internalWallLinings" iconClass="fiftyPct icons8-brick-wall-filled" component-name="interior" attrName="internalWallLinings" filter-id="internalWallLinings" label="Internal Linings" selectClass="monarch-multiselect internalWallLinings" multiple="true" data-to-fetch="InternalLinings"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.floors]" :curr-val="interior.floors" iconClass="fiftyPct icons8-carpet-filled" component-name="interior" attrName="floors" filter-id="floors" label="Floors" selectClass="monarch-multiselect floors" multiple="true" data-to-fetch="Floors"></valuation-multi-select-filter>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.chattels]" :curr-val="interior.chattels" iconClass="hundyPct icons8-curtains-filled" component-name="interior" attrName="chattels" filter-id="chattels" label="Chattels" selectClass="monarch-multiselect c" multiple="true" data-to-fetch="Chattels"></valuation-multi-select-filter>
            </div>

            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.heatingType]" :curr-val="interior.heatingType" iconClass="twentyfivePct icons8-fire-station-filled" component-name="interior" attrName="heatingType" filter-id="heatingType" label="Heating Type" selectClass="monarch-multiselect propertyPlusheatingType" multiple="true" data-to-fetch="HeatingType"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.insulation]" :curr-val="interior.insulation" iconClass="twentyfivePct icons8-heating-room-filled" component-name="interior" attrName="insulation" filter-id="insulation" label="Insulation" selectClass="monarch-multiselect propertyPlusinsulation" multiple="true" data-to-fetch="Insulation"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.plumbingAge]" :curr-val="interior.plumbingAge" iconClass="twentyfivePct  icons8-piping-filled" component-name="interior" attrName="plumbingAge" filter-id="plumbingAge" label="Plumbing Age" selectClass="monarch-multiselect propertyPlusplumbingAge" chooseHere="true" data-to-fetch="PlumbingAge" requestType="POST"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.wiringAge]" :curr-val="interior.wiringAge" iconClass="twentyfivePct icons8-plug-4-filled" component-name="interior" attrName="wiringAge" filter-id="wiringAge" label="Wiring Age" selectClass="monarch-multiselect propertyPluswiringAge" chooseHere="true" data-to-fetch="WiringAge" requestType="POST"></valuation-multi-select-filter>
            </div>
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.doubleGlazing]" :curr-val="interior.doubleGlazing" iconClass="twentyfivePct icons8-closed-window-filled" component-name="interior" attrName="doubleGlazing" filter-id="doubleGlazing" label="Double Glazing" selectClass="monarch-multiselect propertyPlusdoubleGlazing" chooseHere="true" data-to-fetch="DoubleGlazing"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.alternativeEnergy]" :curr-val="interior.alternativeEnergy" iconClass="twentyfivePct icons8-solar-panel-filled" component-name="interior" attrName="alternativeEnergy" filter-id="alternativeEnergy" label="Alternative Energy" selectClass="monarch-multiselect propertyPlusalternativeEnergy" multiple="true" data-to-fetch="AlternativeEnergy"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.ventilation]" :curr-val="interior.ventilation" iconClass="twentyfivePct icons8-air-conditioner-filled" component-name="interior" attrName="ventilation" filter-id="ventilation" label="Ventilation" selectClass="monarch-multiselect propertyPlusventilation" chooseHere="true" data-to-fetch="Ventilation"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.redecorationAge]" :curr-val="interior.redecorationAge" iconClass="twentyfivePct icons8-roller-brush-filled" component-name="interior" attrName="redecorationAge" filter-id="redecorationAge" label="Redecoration Age" selectClass="monarch-multiselect propertyPlusredecorationAge" chooseHere="true" data-to-fetch="RedecorationAge" requestType="POST"></valuation-multi-select-filter>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.otherFeatures]" :curr-val="interior.otherFeatures" iconClass="hundyPct icons8-temperature-inside-filled" component-name="interior" attrName="otherFeatures" filter-id="otherFeatures" label="Other Features" selectClass="monarch-multiselect otherFeatures" multiple="true" data-to-fetch="OtherFeatures"></valuation-multi-select-filter>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <text-input :class="[fields.notes]" maxlength="1000" :curr-val="interior.notes" attr-name="notes" fieldType="text" iconClass="hundyPct icons8-pencil" label="Notes" component-name="interior"></text-input>
            </div>
        </div>
    </div>
</template>
<script>
    import TextInput from '../../filters/TextInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import { EventBus } from '../../../EventBus.js';
    import { store } from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            ValuationMultiSelectFilter
        },
        data: function() {
            return {
                interior: {
                },
                showInteriorTemplate: true,
                refreshInteriorTemplate : false,
                tabState: 'closed',
                reportType: {},
                fields: {}
            }
        },
        methods: {
            notifyParent: function(){
                const self = this
                EventBus.$emit('notify-parent', {'key':'interiorDetails', 'value':self.interior})
            },
            refreshView: function() {
                const self = this
                self.showInteriorTemplate = !self.showInteriorTemplate
                if (!self.showInteriorTemplate) {
                    self.refreshInteriorTemplate = true
                }
            },
            addLivingArea: function(key) {
                const self = this
                if (!self.interior.livingAreas) {
                    self.interior.livingAreas = []
                }
                self.interior.livingAreas.splice(key+1, 0, {})
                self.refreshView();
            },
            removeLivingArea: function(key){
                const self = this
                self.interior.livingAreas.splice(key, 1);
                self.refreshView();
            }
        },
        mounted: function() {
            const self = this
            EventBus.$on('notify-simple-interior', function(data){
                self.interior[data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-multi-interior', function(data){
                self.interior[data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-multi-nested-interior', function(data){
                self.interior[data.parentAttrName][(data.key-1)][data.attrName] = data.val
                self.notifyParent();
            })
        },
        created: function() {
            var self = this;
            EventBus.$on('home-valuation-saved', function(obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 2 || obj.reload) {
                    var homeValuation = obj.homeValuation
                    self.interior = homeValuation.propertyDetails ? (homeValuation.propertyDetails.interiorDetails ? homeValuation.propertyDetails.interiorDetails : {}) : {}
                    if (!self.interior.livingAreas || self.interior.livingAreas.length == 0) {
                        self.addLivingArea()
                    }
                    self.showInteriorTemplate = false
                    self.refreshInteriorTemplate = true
                }
            });

            EventBus.$on('home-valuation-new', function(propertyData) {
                console.log("home-valuation-new");
                self.interior = {}
                self.addLivingArea();
                self.showInteriorTemplate = false
                self.refreshInteriorTemplate = true
            });

            EventBus.$on('property-details-tabs', function(state){
                self.tabState = state
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.propertyDetails.interior;
                }
            });
        },
        updated: function() {
            const self = this
            if (self.refreshInteriorTemplate) {
                self.showInteriorTemplate = true
                self.refreshInteriorTemplate = false
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-interior', this.listener);
            EventBus.$off('notify-multi-interior', this.listener);
            EventBus.$off('notify-multi-nested-interior', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-interior', this.listener);
            EventBus.$off('notify-multi-interior', this.listener);
            EventBus.$off('notify-multi-nested-interior', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>
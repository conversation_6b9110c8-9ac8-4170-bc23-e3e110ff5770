<template>
    <!-- <PERSON>ITCHEN SECTION STARTS -->
    <div class="QVHV-Container kitchen active" v-bind:class="[tabState=='open' ? 'canOpener' : '']" v-if="showKitchenTemplate">

        <ul class="QVHV-tabs">
            <li><span class="is-active">Kitchen</span></li>
            <hr align="left"/>
        </ul>

        <div class="QVHV-formSection">

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.layout]" :curr-val="kitchen.layouts" iconClass="thirtythreePct icons8-fridge-filled" component-name="kitchen" attrName="layouts" filter-id="kitchenLayout" label="Kitchen Layout" selectClass="monarch-multiselect kitchenLayout" multiple="true" data-to-fetch="KitchenLayout"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.age]" :curr-val="kitchen.age" iconClass="thirtythreePct icons8-calendar-filled" component-name="kitchen" attrName="age" filter-id="kitchenAge" label="Kitchen Age" selectClass="monarch-multiselect kitchenAge" chooseHere="true" request-type="POST" data-to-fetch="KitchenAge_HV" sort="sortOrder"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.quality]" :curr-val="kitchen.quality" iconClass="thirtythreePct icons8-rating-filled" component-name="kitchen" attrName="quality" filter-id="kitchenQuality" label="Kitchen Quality" selectClass="monarch-multiselect kitchenQuality" chooseHere="true" data-to-fetch="KitchenQuality"></valuation-multi-select-filter>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <valuation-multi-select-filter :class="[fields.appliances]" :curr-val="kitchen.appliances" iconClass="thirtythreePct icons8-dishwasher-filled" component-name="kitchen" attrName="appliances" filter-id="appliances" label="Appliances" selectClass="monarch-multiselect appliances" multiple="true" data-to-fetch="Appliances"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.benchAndSink]" :curr-val="kitchen.benchAndSink" iconClass="thirtythreePct icons8-bench-sink-filled" component-name="kitchen" attrName="benchAndSink" filter-id="benchAndSink" label="Bench and Sink" selectClass="monarch-multiselect benchAndSink" multiple="true" data-to-fetch="BenchandSink" ></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.floor]" :curr-val="kitchen.floor" iconClass="thirtythreePct icons8-kitchen-floor-new" component-name="kitchen" attrName="floor" filter-id="kitchenFloor" label="Kitchen Floor" selectClass="monarch-multiselect kitchenFloor" chooseHere="true" data-to-fetch="KitchenFloor"></valuation-multi-select-filter>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <text-input :class="[fields.notes]" maxlength="1000" attr-name="notes" :curr-val="kitchen.notes" fieldType="text" iconClass="hundyPct icons8-pencil" label="Notes" component-name="kitchen"></text-input>
            </div>
        </div>
    </div>
</template>
<script>
    import TextInput from '../../filters/TextInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import { EventBus } from '../../../EventBus.js';
    import { store } from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            ValuationMultiSelectFilter
        },
        data: function() {
            return {
                kitchen: {
                },
                showKitchenTemplate: true,
                showMultiSelects: true,
                refreshKitchenTemplate : false,
                tabState: 'closed',
                reportType: {},
                fields: {}
            }
        },
        methods: {
            notifyParent: function(){
                const self = this
                EventBus.$emit('notify-parent', {'key':'kitchenDetails', 'value':self.kitchen})
            }
        },
        mounted: function() {
            const self = this
            EventBus.$on('notify-simple-kitchen', function(data){
                self.kitchen[data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-multi-kitchen', function(data){
                self.kitchen[data.attrName] = data.val
                self.notifyParent();
            })
        },
        created: function() {
            var self = this;
            EventBus.$on('home-valuation-saved', function(obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 2 || obj.reload) {
                    var homeValuation = obj.homeValuation
                    self.kitchen = homeValuation.propertyDetails ? (homeValuation.propertyDetails.kitchenDetails ? homeValuation.propertyDetails.kitchenDetails : {}) : {}
                    self.showKitchenTemplate = false
                    self.refreshKitchenTemplate = true

                }
            });

            EventBus.$on('home-valuation-new', function(propertyData) {
                console.log("home-valuation-new");
                self.kitchen = {}
                self.showKitchenTemplate = false
                self.refreshKitchenTemplate = true
            });

            EventBus.$on('property-details-tabs', function(state){
                self.tabState = state
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.propertyDetails.kitchen;
                }
            });
        },
        updated: function() {
            const self = this
            if (self.refreshKitchenTemplate) {
                self.showKitchenTemplate = true
                self.refreshKitchenTemplate = false
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-kitchen', this.listener);
            EventBus.$off('notify-multi-kitchen', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-kitchen', this.listener);
            EventBus.$off('notify-multi-kitchen', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>

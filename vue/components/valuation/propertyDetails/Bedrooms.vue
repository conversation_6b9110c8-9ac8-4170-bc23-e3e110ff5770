<template>
    <!-- BEDROOMS SECTION STARTS -->
    <div class="QVHV-Container bedrooms active" v-bind:class="[tabState=='open' ? 'canOpener' : '']" v-if="showBedroomsTemplate">
        <!-- ^^ THE ELEMENT ".QVHV-Container" SHOULD GET THE .canOpener CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->

        <ul class="QVHV-tabs">
            <li><span class="is-active">Bedrooms</span></li>
            <hr align="left"/>
        </ul>

        <div class="QVHV-formSection">

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row" v-for="bedroom,key in bedrooms.bedrooms" >
                <valuation-multi-select-filter :class="[fields.bedroom]" :id="'pd_bedrooms_'+(key+1)" :obj-key="key+1" :curr-val="bedroom.key" iconClass="twentyfivePct icons8-bed-filled" component-name="bedrooms" parentAttrName="bedrooms" attrName="key" :filter-id="'bedroom'+(key+1)" :label="'Bedroom '+(key+1)" :selectClass="'monarch-multiselect bedroom'+(key+1)" data-to-fetch="Bedroom"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.bedroomDescription]" :id="'pd_bedrooms_description_'+(key+1)" :obj-key="key+1" :curr-val="bedroom.values" iconClass="seventyfivePct-tenRem icons8-todo-list-filled" component-name="bedrooms" parentAttrName="bedrooms" attrName="values" :filter-id="'bedroomDescription'+(key+1)" label="Description" :selectClass="'monarch-multiselect bedroomDescription'+(key+1)" multiple="true" data-to-fetch="BedroomDescriptions"></valuation-multi-select-filter>
                <div class="advSearch-group sa-addRemove">
                    <i class="saRow-add material-icons" @click="addBedrooms(key)"></i>
                    <i v-if="key>0" class="saRow-remove material-icons" @click="removeBedrooms(key)"></i>
                </div>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row" v-for="study, key in bedrooms.studies" :id="'pd_homeoffice'+key">
                <valuation-multi-select-filter :class="[fields.study]" :id="'pd_bedrooms_homeoffice_'+(key+1)" :obj-key="key+1" :curr-val="study.key" iconClass="twentyfivePct icons8-pc-on-desk-filled" component-name="bedrooms" parentAttrName="studies" attrName="key" :filter-id="'study'+(key+1)" label="Home Office or Study" :selectClass="'monarch-multiselect homeofficeOrStudy'+(key+1)" data-to-fetch="HomeofficeorStudy"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.studyDescription]" :id="'pd_bedrooms_homeoffice_description_'+(key+1)" :obj-key="key+1" :curr-val="study.values" iconClass="seventyfivePct-tenRem icons8-todo-list-filled" component-name="bedrooms" parentAttrName="studies" attrName="values" :filter-id="'homeOfficeDescriptions'+(key+1)" label="Description" :selectClass="'monarch-multiselect homeOfficeDescriptions'+(key+1)" multiple="true" data-to-fetch="HomeOfficeDescriptions"></valuation-multi-select-filter>
                <div class="advSearch-group sa-addRemove">
                    <i class="saRow-add material-icons" @click="addStudy(key)"></i>
                    <i v-if="key>0" class="saRow-remove material-icons" @click="removeStudy(key)"></i>
                </div>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <text-input :class="[fields.notes]" maxlength="1000" id="pd_bedrooms_notes" :curr-val="bedrooms.notes" attr-name="notes" fieldType="text" iconClass="hundyPct icons8-pencil" label="Notes" component-name="bedrooms"></text-input>
            </div>
        </div>
    </div>
</template>
<script>
    import TextInput from '../../filters/TextInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import { EventBus } from '../../../EventBus.js';
    import { store } from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            ValuationMultiSelectFilter
        },
        data: function() {
            return {
                bedrooms: {
                },
                showBedroomsTemplate: true,
                refreshBedroomsTemplate : false,
                tabState: 'closed',
                reportType: {},
                fields: {}
            }
        },
        methods: {
            notifyParent: function(){
                const self = this
                EventBus.$emit('notify-parent', {'key':'bedroomDetails', 'value':self.bedrooms})
            },
            refreshView: function() {
                const self = this
                self.showBedroomsTemplate = !self.showBedroomsTemplate
                if (!self.showBedroomsTemplate) {
                    self.refreshBedroomsTemplate = true
                }
            },
            addBedrooms: function(key) {
                const self = this
                if (!self.bedrooms.bedrooms) {
                    self.bedrooms.bedrooms = []
                }
                self.bedrooms.bedrooms.splice(key+1, 0, {})
                self.refreshView();
            },
            removeBedrooms: function(key){
                const self = this
                self.bedrooms.bedrooms.splice(key, 1);
                self.refreshView();
            },
            addStudy: function(key) {
                const self = this
                if (!self.bedrooms.studies) {
                    self.bedrooms.studies = []
                }
                self.bedrooms.studies.splice(key+1, 0, {})
                self.refreshView();
            },
            removeStudy: function(key){
                const self = this
                self.bedrooms.studies.splice(key, 1);
                self.refreshView();
            },

        },
        mounted: function() {
            const self = this
            EventBus.$on('notify-simple-bedrooms', function(data){
                self.bedrooms[data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-multi-bedrooms', function(data){
                self.bedrooms[data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-multi-nested-bedrooms', function(data){
                self.bedrooms[data.parentAttrName][(data.key-1)][data.attrName] = data.val
                self.notifyParent();
            })
        },
        created: function() {
            var self = this;
            EventBus.$on('home-valuation-saved', function(obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 2 || obj.reload) {
                    var homeValuation = obj.homeValuation
                    self.bedrooms = homeValuation.propertyDetails ? (homeValuation.propertyDetails.bedroomDetails ? homeValuation.propertyDetails.bedroomDetails : {}) : {}
                    if (!self.bedrooms.bedrooms || self.bedrooms.bedrooms.length == 0) {
                        self.addBedrooms(0)
                        self.addBedrooms(1);
                    }
                    if (!self.bedrooms.studies || self.bedrooms.studies.length == 0) {
                        self.addStudy(0)
                    }
                    self.showBedroomsTemplate = false
                    self.refreshBedroomsTemplate = true
                }
            });

            EventBus.$on('home-valuation-new', function(propertyData) {
                self.bedrooms = {}
                self.addBedrooms(0);
                self.addBedrooms(1);
                self.addStudy(0);
                self.showBedroomsTemplate = false
                self.refreshBedroomsTemplate = true
            });

            EventBus.$on('property-details-tabs', function(state){
                self.tabState = state
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                self.fields = fields.propertyDetails.bedrooms;
            });
        },
        updated: function() {
            const self = this
            if (self.refreshBedroomsTemplate) {
                self.showBedroomsTemplate = true
                self.refreshBedroomsTemplate = false
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-bedrooms', this.listener);
            EventBus.$off('notify-multi-bedrooms', this.listener);
            EventBus.$off('notify-multi-nested-bedrooms', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-bedrooms', this.listener);
            EventBus.$off('notify-multi-bedrooms', this.listener);
            EventBus.$off('notify-multi-nested-bedrooms', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>
<template>
    <!-- BATHROOMS SECTION STARTS -->
    <div class="QVHV-Container bathrooms active" v-bind:class="[tabState=='open' ? 'canOpener' : '']" v-if="showBathroomsTemplate">
        <!-- ^^ THE ELEMENT ".QVHV-Container" SHOULD GET THE .canOpener CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->

        <ul class="QVHV-tabs">
            <li><span class="is-active">Bathrooms</span></li>
            <hr align="left"/>
        </ul>

        <div class="QVHV-formSection">

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <div class="advSearch-Subrow">
                    <valuation-multi-select-filter :class="[fields.mainBathroom]" obj-key="1" singleMap="true" :curr-val="bathrooms.mainBathroom.key" iconClass="twentyfivePct icons8-shower-and-tub-filled" parentAttrName="mainBathroom" attrName="key" component-name="bathrooms" filter-id="mainBathroom" label="Main Bathroom" selectClass="monarch-multiselect mainBathroom" data-to-fetch="MainBathroom"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.mainBathroomDescription]" obj-key="1" singleMap="true" :curr-val="bathrooms.mainBathroom.values" iconClass="seventyfivePct-tenRem icons8-todo-list-filled" parentAttrName="mainBathroom" component-name="bathrooms" attrName="values" filter-id="mainBathroomDescription" label="Main Bathroom Description" selectClass="monarch-multiselect mainBathroomDescription" multiple="true" data-to-fetch="MainBathroomDescription"></valuation-multi-select-filter>
                    <div class="advSearch-group sa-addRemove"></div>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <valuation-multi-select-filter :class="[fields.mainBathroomAge]" singleMap="true" :curr-val="bathrooms.mainBathroomAge" iconClass="thirtysevenfivePct-fiveRem icons8-calendar-filled" component-name="bathrooms" attrName="mainBathroomAge" filter-id="mainBathroomAge" label="Main Bathroom Age" selectClass="monarch-multiselect mainBathroomAge" request-type="POST" data-to-fetch="MainBathroomAge" sort="sortOrder"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.mainBathroomQuality]" singleMap="true" :curr-val="bathrooms.mainBathroomQuality" iconClass="thirtysevenfivePct-fiveRem icons8-rating-filled" component-name="bathrooms" attrName="mainBathroomQuality" filter-id="mainBathroomQuality" label="Main Bathroom Quality" selectClass="monarch-multiselect mainBathroomQuality" data-to-fetch="MainBathroomQuality"></valuation-multi-select-filter>
                </div>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <div class="advSearch-Subrow">
                    <valuation-multi-select-filter :class="[fields.ensuite]" singleMap="true" obj-key="1" :curr-val="bathrooms.ensuiteBathroom.key" iconClass="twentyfivePct icons8-shower-and-tub-filled" parentAttrName="ensuiteBathroom" component-name="bathrooms" attrName="key" filter-id="ensuite" label="Ensuite" selectClass="monarch-multiselect ensuite" data-to-fetch="Ensuite"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.ensuiteDescription]" singleMap="true" obj-key="1" :curr-val="bathrooms.ensuiteBathroom.values" iconClass="seventyfivePct-tenRem icons8-todo-list-filled" parentAttrName="ensuiteBathroom" component-name="bathrooms" attrName="values" filter-id="ensuiteDescription" label="Ensuite Description" selectClass="monarch-multiselect ensuite" multiple="true" data-to-fetch="EnsuiteDescription"></valuation-multi-select-filter>
                </div>
                <div class="advSearch-Subrow">
                    <div class="advSearch-group twentyfivePct"></div>
                    <valuation-multi-select-filter :class="[fields.ensuiteAge]" :curr-val="bathrooms.ensuiteAge" iconClass="thirtysevenfivePct-fiveRem icons8-calendar-filled" component-name="bathrooms" attrName="ensuiteAge" filter-id="ensuiteAge" label="Ensuite Age" selectClass="monarch-multiselect ensuiteAge" request-type="POST" data-to-fetch="EnsuiteAge" sort="SORT_ORDER"></valuation-multi-select-filter>
                    <valuation-multi-select-filter :class="[fields.ensuiteQuality]" :curr-val="bathrooms.ensuiteQuality" iconClass="thirtysevenfivePct-fiveRem icons8-rating-filled" component-name="bathrooms" attrName="ensuiteQuality" filter-id="ensuiteQuality" label="Ensuite Quality" selectClass="monarch-multiselect ensuiteQuality" data-to-fetch="EnsuiteQuality"></valuation-multi-select-filter>
                    <div class="advSearch-group sa-addRemove"></div>
                </div>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row" v-for="otherBathroom,key in bathrooms.otherBathrooms">
                <valuation-multi-select-filter :class="[fields.bathroomOrToilet]" :obj-key="key+1" :curr-val="otherBathroom.key" iconClass="twentyfivePct icons8-toilet-bowl-filled" component-name="bathrooms" parentAttrName="otherBathrooms" attrName="key" :filter-id="'otherBathrooms'+(key+1)" label="Bathroom or Toilet" :selectClass="'monarch-multiselect bathroomOrToilet'+(key+1)" data-to-fetch="BathroomorToilet"></valuation-multi-select-filter>
                <valuation-multi-select-filter :class="[fields.bathroomOrToiletDescription]" :obj-key="key+1" :curr-val="otherBathroom.values" iconClass="seventyfivePct-tenRem icons8-todo-list-filled" component-name="bathrooms" parentAttrName="otherBathrooms" attrName="values" :filter-id="'otherBathroomsDesc'+(key+1)" label="Description" :selectClass="'monarch-multiselect bathroomOrToiletDesc'+(key+1)" multiple="true" data-to-fetch="MainBathroomDescription"></valuation-multi-select-filter>
                <div class="advSearch-group sa-addRemove">
                    <i class="saRow-add material-icons" @click="addOtherBathroom(key)"></i>
                    <i v-if="key>0" class="saRow-remove material-icons" @click="removeOtherBathroom(key)"></i>
                </div>
            </div>

            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <text-input :class="[fields.notes]" maxlength="1000" attr-name="notes" :curr-val="bathrooms.notes" fieldType="text" iconClass="hundyPct icons8-pencil" label="Notes" component-name="bathrooms"></text-input>
            </div>
        </div>
    </div>
</template>

<script>
    import TextInput from '../../filters/TextInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import { EventBus } from '../../../EventBus.js';
    import { store } from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            ValuationMultiSelectFilter
        },
        data: function() {
            return {
                bathrooms: {
                    mainBathroom:{},
                    ensuiteBathroom: {}
                },
                showBathroomsTemplate: true,
                refreshBathroomsTemplate : false,
                tabState: 'closed',
                reportType: {},
                fields: {}
            }
        },
        methods: {
            notifyParent: function(){
                const self = this
                EventBus.$emit('notify-parent', {'key':'bathroomDetails', 'value':self.bathrooms})
            },
            refreshView: function() {
                const self = this
                self.showBathroomsTemplate = !self.showBathroomsTemplate
                if (!self.showBathroomsTemplate) {
                    self.refreshBathroomsTemplate = true
                }
            },
            addOtherBathroom: function(key) {
                const self = this
                if (!self.bathrooms.otherBathrooms) {
                    self.bathrooms.otherBathrooms = []
                }
                self.bathrooms.otherBathrooms.splice(key+1, 0, {})
                self.refreshView();
            },
            removeOtherBathroom: function(key){
                const self = this
                self.bathrooms.otherBathrooms.splice(key, 1);
                self.refreshView();
            }
        },
        mounted: function() {
            const self = this
            EventBus.$on('notify-simple-bathrooms', function(data){
                self.bathrooms[data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-multi-bathrooms', function(data){
                self.bathrooms[data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-multi-nested-bathrooms', function(data){
                self.bathrooms[data.parentAttrName][(data.key-1)][data.attrName] = data.val
                self.notifyParent();
            })
            EventBus.$on('notify-single-nested-bathrooms', function(data){
                self.bathrooms[data.parentAttrName][data.attrName] = data.val
                self.notifyParent();
            })
        },
        created: function() {
            var self = this;
            EventBus.$on('home-valuation-saved', function(obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 2 || obj.reload) {
                    var homeValuation = obj.homeValuation
                    self.bathrooms = homeValuation.propertyDetails ? (homeValuation.propertyDetails.bathroomDetails ? homeValuation.propertyDetails.bathroomDetails : {}) : {}
                    if (!self.bathrooms.mainBathroom) {
                        self.bathrooms.mainBathroom = {}
                    }
                    if (!self.bathrooms.ensuiteBathroom) {
                        self.bathrooms.ensuiteBathroom = {}
                    }
                    if (!self.bathrooms.otherBathrooms || self.bathrooms.otherBathrooms.length == 0) {
                        self.addOtherBathroom(0)
                    }
                    self.showBathroomsTemplate = false
                    self.refreshBathroomsTemplate = true
                }
            });

            EventBus.$on('home-valuation-new', function(propertyData) {
                self.bathrooms = {}
                self.bathrooms.mainBathroom = {}
                self.bathrooms.ensuiteBathroom = {}
                self.addOtherBathroom(0)
                self.showBathroomsTemplate = false
                self.refreshBathroomsTemplate = true
            });

            EventBus.$on('property-details-tabs', function(state){
                self.tabState = state
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });
            
            EventBus.$on('set-fields', function(fields) {
                self.fields = fields.propertyDetails.bathrooms;
            });
        },
        updated: function() {
            const self = this
            if (self.refreshBathroomsTemplate) {
                self.showBathroomsTemplate = true
                self.refreshBathroomsTemplate = false
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-bathrooms', this.listener);
            EventBus.$off('notify-multi-bathrooms', this.listener);
            EventBus.$off('notify-multi-nested-bathrooms', this.listener);
            EventBus.$off('notify-single-nested-bathrooms', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-bathrooms', this.listener);
            EventBus.$off('notify-multi-bathrooms', this.listener);
            EventBus.$off('notify-multi-nested-bathrooms', this.listener);
            EventBus.$off('notify-single-nested-bathrooms', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>
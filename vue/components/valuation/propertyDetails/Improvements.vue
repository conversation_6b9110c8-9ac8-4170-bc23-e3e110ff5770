<template>
    <div class="QVHV-Container siteimprovements active" v-bind:class="[tabState=='open' ? 'canOpener' : '']"
         v-if="showImprovementsTemplate">
        <!-- ^^ THE ELEMENT ".QVHV-Container" SHOULD GET THE .canOpener CLASS WHEN THE USER CLICKS THE EXPAND ARROW -->

        <ul class="QVHV-tabs">
            <li><span class="is-active">Improvements</span></li>
            <hr align="left"/>
        </ul>

        <div class="QVHV-formSection">
            <!-- NEW ROW STARTS -->
            <div class="advSearch-row" v-for="(majorSiteImprovement, index) in improvements.majorSiteImprovements">
                <div class="advSearch-Subrow">
                    <valuation-multi-select-filter :obj-key="index+1" :curr-val="majorSiteImprovement.improvement"
                                                   iconClass="twentyfivePct icons8-swimming-pool" component-name="improvements"
                                                   parentAttrName="majorSiteImprovements" attrName="improvement"
                                                   :filter-id="'improvement'+(index+1)" label="Major Site Improvements"
                                                   :selectClass="'monarch-multiselect majorImprovements'+(index+1)"
                                                   data-to-fetch="MajorSiteImprovements"
                                                   :class="[fields.improvement]"></valuation-multi-select-filter>
                    <text-input :class="[fields.description]" :obj-key="index+1" :curr-val="majorSiteImprovement.description" attr-name="description"
                                fieldType="text" iconClass="seventyfivePct-tenRem icons8-pencil" label="Description"
                                component-name="improvements" parentAttrName="majorSiteImprovements"></text-input>
                    <div class="advSearch-group sa-addRemove">
                        <i class="saRow-add material-icons" @click="addMajorImprovement(index)"></i>
                        <i v-if="index>0" class="saRow-remove material-icons"
                           @click="removeMajorImprovement(index)"></i>
                    </div>
                </div>
            </div>
            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <valuation-multi-select-filter :curr-val="improvements.minorSiteImprovements"
                                               iconClass="hundyPct icons8-front-gate-closed-filled"
                                               component-name="improvements" attrName="minorSiteImprovements"
                                               filter-id="minorSiteImprovements" label="Minor Site Improvements"
                                               selectClass="monarch-multiselect minorSiteImprovements" multiple="true"
                                               data-to-fetch="MinorSiteImprovements"
                                               :class="[fields.minorSiteImprovements]"></valuation-multi-select-filter>
            </div>
            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <valuation-multi-select-filter id="improvementsDriveWay" :curr-val="improvements.driveway"
                                               iconClass="thirtythreePct icons8-driveway-new"
                                               component-name="improvements" attrName="driveway" filter-id="driveway"
                                               label="Driveway" selectClass="monarch-multiselect driveway"
                                               multiple="true" data-to-fetch="Driveway"
                                               :class="[fields.driveway]"></valuation-multi-select-filter>
                <valuation-multi-select-filter id="improvementsLandscaping" :curr-val="improvements.landscaping"
                                               iconClass="thirtythreePct icons8-forest-filled"
                                               component-name="improvements" attrName="landscaping"
                                               filter-id="landscaping" label="Landscaping"
                                               selectClass="monarch-multiselect landscaping" multiple="true"
                                               data-to-fetch="Landscaping"
                                               :class="[fields.landscaping]"></valuation-multi-select-filter>
                <valuation-multi-select-filter id="improvementsFencing" :curr-val="improvements.fencing"
                                               iconClass="thirtythreePct icons8-defensive-wood-wall-filled"
                                               component-name="improvements" attrName="fencing" filter-id="fencing"
                                               label="Fencing" selectClass="monarch-multiselect fencing" multiple="true"
                                               data-to-fetch="Fencing"
                                               :class="[fields.fencing]"></valuation-multi-select-filter>
            </div>
            <!-- NEW ROW STARTS -->
            <div class="advSearch-row">
                <text-input :class="[fields.notes]" maxlength="1000" id="improvementsNotes" :curr-val="improvements.notes" attr-name="notes" fieldType="text"
                            iconClass="hundyPct icons8-pencil" label="Notes" component-name="improvements"></text-input>
            </div>
        </div>
    </div>
</template>
<script>
    import TextInput from '../../filters/TextInput.vue'
    import ValuationMultiSelectFilter from '../../filters/ValuationMultiSelectFilter.vue'
    import {EventBus} from '../../../EventBus.js';
    import {store} from '../../../DataStore';
    import Vue from 'vue';


    export default {
        components: {
            TextInput,
            ValuationMultiSelectFilter
        },
        data: function () {
            return {
                improvements: {},
                showImprovementsTemplate: true,
                refreshImprovementsTemplate: false,
                tabState: 'closed',
                reportType: {},
                fields: {}
            }
        },
        methods: {
            notifyParent: function () {
                const self = this
                EventBus.$emit('notify-parent', {'key': 'improvementDetails', 'value': self.improvements})
            },
            refreshView: function () {
                const self = this
                self.showImprovementsTemplate = !self.showImprovementsTemplate
                if (!self.showImprovementsTemplate) {
                    self.refreshImprovementsTemplate = true
                }
            },
            addMajorImprovement: function (index) {
                const self = this;
                if (!self.improvements.majorSiteImprovements) {
                    self.improvements.majorSiteImprovements = [];
                }
                self.improvements.majorSiteImprovements.splice(index + 1, 0, {});
                self.refreshView();
            },
            removeMajorImprovement: function (index) {
                const self = this;
                self.improvements.majorSiteImprovements.splice(index, 1);
                self.refreshView();
            }
        },
        mounted: function () {
            const self = this;
            EventBus.$on('notify-simple-improvements', function (data) {
                self.improvements[data.attrName] = data.val;
                self.notifyParent();
            });
            EventBus.$on('notify-multi-improvements', function (data) {
                self.improvements[data.attrName] = data.val;
                self.notifyParent();
            });

            EventBus.$on('notify-simple-nested-improvements', function (data) {
                self.improvements[data.parentAttrName][data.key - 1][data.attrName] = data.val;
                self.notifyParent();
            });
            EventBus.$on('notify-multi-nested-improvements', function (data) {
                self.improvements[data.parentAttrName][(data.key - 1)][data.attrName] = data.val;
                self.notifyParent();
            });
        },
        created: function () {
            var self = this;
            EventBus.$on('home-valuation-saved', function (obj) {
                var fromStep = obj.fromStep;
                if(fromStep != 2 || obj.reload) {
                    var homeValuation = obj.homeValuation
                    self.improvements = homeValuation.propertyDetails ? (homeValuation.propertyDetails.improvementDetails ? homeValuation.propertyDetails.improvementDetails : {}) : {}
                    if (!self.improvements.majorSiteImprovements || self.improvements.majorSiteImprovements.length === 0) {
                        self.addMajorImprovement(0);
                    }
                    self.showImprovementsTemplate = false
                    self.refreshImprovementsTemplate = true
                }
            });

            EventBus.$on('home-valuation-new', function (propertyData) {
                console.log("home-valuation-new");
                self.improvements = {};
                self.addMajorImprovement(0);
                self.showImprovementsTemplate = false;
                self.refreshImprovementsTemplate = true;
            });

            EventBus.$on('property-details-tabs', function (state) {
                self.tabState = state
            });

            EventBus.$on('home-valuation-report-detail-report-type', function(reportTypeCode) {
                self.reportType.code = reportTypeCode
                //self.refreshView()
            });

            EventBus.$on('set-fields', function(fields) {
                if(fields) {
                    self.fields = fields.propertyDetails.improvements;
                }
            });
        },
        updated: function () {
            const self = this
            if (self.refreshImprovementsTemplate) {
                self.showImprovementsTemplate = true
                self.refreshImprovementsTemplate = false
            }
        },
        destroyed: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-improvements', this.listener);
            EventBus.$off('notify-multi-improvements', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('notify-multi-nested-improvements', this.listener);
            EventBus.$off('notify-simple-nested-improvements', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('notify-simple-improvements', this.listener);
            EventBus.$off('notify-multi-improvements', this.listener);
            EventBus.$off('property-details-tabs', this.listener);
            EventBus.$off('notify-multi-nested-improvements', this.listener);
            EventBus.$off('notify-simple-nested-improvements', this.listener);
            EventBus.$off('home-valuation-report-detail-report-type', this.listener);
            EventBus.$off('set-fields', this.listener);
        }
    }
</script>
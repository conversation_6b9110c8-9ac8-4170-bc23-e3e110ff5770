<template xmlns:v-on="http://www.w3.org/1999/xhtml">
    <div class="QVHV-Container incomeMethod" v-bind:class="{disabled: readOnly, canOpener: tabState=='open', active: currentTab == 'IncomeMethod'}">

        <ul class="QVHV-tabs hide">
            <li><span class="is-active">Income Method</span></li>
            <hr align="left"/>
        </ul>
         <div class="QVHV-formSection">
            <h3>Gross Rate Calculation</h3>
            <div class="salesAnalysis-table">
                <div class="salesAnalysis-row">
                    <div class="salesAnalysis-th sa-description-medium">
                        <span>Description</span>
                    </div>
                    <div class="salesAnalysis-th sa-rent">
                        <span>Analysed Rent (Weekly)</span>
                    </div>
                    <div class="salesAnalysis-th sa-rent">
                        <span>Analysed Rent (Annual)</span>
                    </div>
                    <div class="salesAnalysis-th sa-runnintgTotal">
                        <span></span>
                    </div>
                    <div class="salesAnalysis-th sa-addRemove">
                    </div>
                </div>
                <div class="salesAnalysis-row" v-for="unit, key in incomeMethodWorksheet.units">
                    <div class="salesAnalysis-td sa-description-medium">
					<span>
						<input type="text" v-model="unit.description">
					</span>
                    </div>
                    <div class="salesAnalysis-td sa-rent">
					<span>
						<input type="text" v-model="unit.analysedWeeklyRentDisplay" @change="calculateRent(key, 0)">
					</span>
                    </div>
                    <div class="salesAnalysis-td sa-rent">
					<span>
						<input type="text" v-model="unit.analysedAnnualRentDisplay" @change="calculateRent(key, 1)">
					</span>
                    </div>
                    <div class="salesAnalysis-td sa-runnintgTotal">
                        <span>{{ formatPrice(unit.total,'$0,0') }}</span>
                    </div>
                    <div class="salesAnalysis-td sa-addRemove">
                        <i class="saRow-add material-icons" @click="addField(key, 'units')"></i>
                        <i v-if="+key > 0" class="saRow-remove material-icons" @click="removeField(key, 'units')"></i>
                    </div>
                </div>
            </div>


            <h3>Cap Rates</h3>
            <div class="salesAnalysis-table">
                <div class="salesAnalysis-row">
                    <div class="salesAnalysis-th sa-description-medium">
                        <span></span>
                    </div>
                    <div class="salesAnalysis-th sa-capRate">
                        <span></span>
                    </div>
                    <div class="salesAnalysis-th sa-capRate">
                        <span>Rate %</span>
                    </div>
                    <div class="salesAnalysis-th sa-runnintgTotal">
                        <span></span>
                    </div>
                    <div class="salesAnalysis-th sa-addRemove">
                    </div>
                </div>
                <div class="salesAnalysis-row" v-for="grossCapRate, key in incomeMethodWorksheet.grossCapRates">
                    <div class="salesAnalysis-td sa-description-medium">
                        <span></span>
                    </div>
                    <div class="salesAnalysis-td sa-capRate">
                        <span></span>
                    </div>
                    <div class="salesAnalysis-td sa-capRate">
					<span>
						<input type="text" v-model="incomeMethodWorksheet.grossCapRates[key]" @change="formatGrossCapRates(key)">
					</span>
                    </div>
                    <div class="salesAnalysis-td sa-runnintgTotal">
                        <span>{{ (incomeMethodWorksheet.grossCapRates.length > 0 && incomeMethodWorksheet.grossCapRates[key]) ? formatPrice((((incomeMethodWorksheet.units && incomeMethodWorksheet.units.length) > 0 ? incomeMethodWorksheet.units[incomeMethodWorksheet.units.length - 1].total : 0)/(incomeMethodWorksheet.grossCapRates[key]/100)),'$0,0') : '' }}</span>
                    </div>
                    <div class="salesAnalysis-td sa-addRemove">
                        <i class="saRow-add material-icons" @click="addField(key, 'grossCapRates')"></i>
                        <i v-if="+key > 0" class="saRow-remove material-icons" @click="removeField(key, 'grossCapRates')"></i>
                    </div>
                </div>
            </div>


            <h3>Market Values - Income Method Approach</h3>
            <div class="salesAnalysis-table sa-details ">
                <div class="salesAnalysis-row">
                    <div class="salesAnalysis-th sa-marketValue">
                        <span>Market Value</span>
                    </div>
                    <div class="salesAnalysis-th sa-spacer5">
                        <span></span>
                    </div>
                </div>
                <div class="salesAnalysis-row">
                    <div class="salesAnalysis-td sa-marketValue">
					<span v-if="showIncomeMethodTemplate">
						<input type="text" v-model="incomeMethodWorksheet.marketValueDisplay" @change="calculateMarketValueDisplay()">
					</span>
                    </div>
                    <div class="salesAnalysis-td sa-spacer5">
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {EventBus} from '../../../EventBus.js';
    import { store } from '../../../DataStore.js';
    import numeral from 'numeral';
    import formatUtils from '../../../utils/FormatUtils';
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['readOnly', 'tabState', 'currentTab'],
        mixins: [formatUtils, commonUtils],
        data: function() {
            return {
                incomeMethodWorksheet: {
                    units: [],
                    grossCapRates: [],
                    marketValue: null,
                    marketValueDisplay: null
                },
                homeValuationId: null,
                showIncomeMethodTemplate: true,
                refreshIncomeMethodTemplate : false
            }
        },
        computed: {
            totalRent: function () {
                 const self = this;
                 return self.incomeMethodWorksheet.units.length > 0 ? self.incomeMethodWorksheet.units[self.incomeMethodWorksheet.units.length - 1].total : 0;
             }
        },
        methods: {
            addField: function(key, field) {
                const self = this;
                if (!self.incomeMethodWorksheet[field]) {
                    self.incomeMethodWorksheet[field] = [];
                }
                self.incomeMethodWorksheet[field].splice(key+1, 0, (field == 'units') ? {description: null, analysedAnnualRent: null, analysedWeeklyRentDisplay: null, analysedAnnualRentDisplay: null, total: null} : null);
                self.calculateRunningTotal("units");
                self.saveWorksheet();
            },
            removeField: function(key, field){
                const self = this;
                self.incomeMethodWorksheet[field].splice(key, 1);
                self.calculateRunningTotal("units");
                self.saveWorksheet();
            },
            saveWorksheet: function() {
                const self = this;
                var incomeMethodWorksheet = JSON.parse(JSON.stringify(self.incomeMethodWorksheet));
                var gcrWithNoNulls = [];
                $.each(incomeMethodWorksheet.grossCapRates, function(index, gcr) {
                    if(gcr) {
                        gcrWithNoNulls.push(gcr);
                    }
                });
                incomeMethodWorksheet.grossCapRates = gcrWithNoNulls;
                var event = {};
                event.incomeMethodWorksheet = incomeMethodWorksheet;
                EventBus.$emit('home-valuation-job-income-method', event);
            },
            addUnits: function(units) {
                const self = this;
                self.incomeMethodWorksheet.units = [];
                if(units <= 4) {
                    for(var i= 1, len = units; i <= len; i++){
                        self.incomeMethodWorksheet.units.push({description: 'Flat ' + i, analysedAnnualRent: null, analysedWeeklyRentDisplay: null, analysedAnnualRentDisplay: null, total: null});
                    }
                } else {
                    self.incomeMethodWorksheet.units = [{description: 'Total Property', analysedAnnualRent: null, analysedWeeklyRent: null, analysedAnnualRentDisplay: null, total: null}];
                }
                self.saveWorksheet();
            },
            addgGrossCapRates: function() {
                const self = this;
                self.incomeMethodWorksheet.grossCapRates = [null, null, null];
                self.saveWorksheet();
            },
            calculateRent: function(key, index) {
                const self = this;
                var analysedWeeklyRent = numeral(self.incomeMethodWorksheet.units[key].analysedWeeklyRentDisplay).value();
                var analysedAnnualRent = numeral(self.incomeMethodWorksheet.units[key].analysedAnnualRentDisplay).value();
                if(index == 0) {
                    if(analysedWeeklyRent) {
                        analysedAnnualRent = analysedWeeklyRent * 52;
                    } else {
                        analysedAnnualRent = null;
                    }
                } else {
                    if(analysedAnnualRent) {
                        analysedWeeklyRent = analysedAnnualRent / 52;
                    } else {
                        analysedWeeklyRent = null;
                    }
                }
                self.incomeMethodWorksheet.units[key].analysedAnnualRent = analysedAnnualRent;
                self.incomeMethodWorksheet.units[key].analysedAnnualRentDisplay = self.formatPrice(analysedAnnualRent,'$0,0');
                self.incomeMethodWorksheet.units[key].analysedWeeklyRentDisplay = self.formatPrice(analysedWeeklyRent,'$0,0');
                self.calculateRunningTotal("units");
                self.saveWorksheet();
                self.refreshView();
            },
            calculateRunningTotal: function(item) {
                const self = this;
                var total = 0;
                $.each(self.incomeMethodWorksheet[item], function(index, imp) {
                    total += (imp.analysedAnnualRent ? imp.analysedAnnualRent : 0);
                    if ((+index + 1) == self.incomeMethodWorksheet[item].length) {
                        var price = numeral(imp.analysedAnnualRent).value();
                        self.incomeMethodWorksheet[item][index].total = total;
                    } else {
                        self.incomeMethodWorksheet[item][index].total = null;
                    }
                });
            },
            calculateMarketValueDisplay: function() {
                const self = this;
                self.incomeMethodWorksheet.marketValue = numeral(self.incomeMethodWorksheet.marketValueDisplay).value();
                self.incomeMethodWorksheet.marketValueDisplay = self.formatPrice(numeral(self.incomeMethodWorksheet.marketValueDisplay).value(),'$0,0');
                self.saveWorksheet();
                self.refreshView();
            },
            formatGrossCapRates: function(key) {
                const self = this;
                self.incomeMethodWorksheet.grossCapRates[key] = self.formatDecimal(numeral(self.incomeMethodWorksheet.grossCapRates[key]).value(), 2);
                self.saveWorksheet();
                self.refreshView();
            },
            refreshView: function() {
                const self = this;
                self.showIncomeMethodTemplate = !self.showIncomeMethodTemplate;
                if (!self.showIncomeMethodTemplate) {
                    self.refreshIncomeMethodTemplate = true;
                }
            }
        },
        mounted: function() {
            const self = this;
            EventBus.$on('home-valuation-new', function(event) {
                var units = event.units;
                self.incomeMethodWorksheet = { units: [], grossCapRates: [], marketValue: null, marketValueDisplay: null };
                self.homeValuationId = null;
                self.addUnits(units);
                self.addgGrossCapRates();
            });

            EventBus.$on('home-valuation-saved', function(obj) {
                var homeValuation = obj.homeValuation;
                if(self.homeValuationId != homeValuation.id) {
                    self.homeValuationId = homeValuation.id;
                    self.incomeMethodWorksheet = JSON.parse(JSON.stringify(homeValuation.incomeMethodWorksheet));

                    if(!self.incomeMethodWorksheet.units) {
                        self.addUnits(obj.units);
                    } else {
                        $.each(self.incomeMethodWorksheet.units, function(index, imp) {
                            if(imp) {
                                imp.analysedAnnualRent = imp.analysedAnnualRent ? imp.analysedAnnualRent : 0;
                                imp.analysedAnnualRentDisplay = self.formatPrice(imp.analysedAnnualRent,'$0,0');
                                imp.analysedWeeklyRentDisplay = self.formatPrice((imp.analysedAnnualRent/52),'$0,0');
                                self.calculateRunningTotal('units');
                            } else {
                                self.removeField(index, 'units');
                            }
                        });
                    }

                    if(!self.incomeMethodWorksheet.grossCapRates || (self.incomeMethodWorksheet.grossCapRates && self.incomeMethodWorksheet.grossCapRates.length == 0)) {
                        self.addgGrossCapRates();
                        self.saveWorksheet();
                    } else {
                        $.each(self.incomeMethodWorksheet.grossCapRates, function(index, gcr) {
                            self.incomeMethodWorksheet.grossCapRates[index] = self.formatDecimal(numeral(self.incomeMethodWorksheet.grossCapRates[index]).value(), 2);
                        });
                    }

                    if(self.incomeMethodWorksheet.marketValue) {
                        self.incomeMethodWorksheet.marketValueDisplay = self.formatPrice(self.incomeMethodWorksheet.marketValue ,'$0,0');
                    }

                    self.refreshView();
                }
            });
        },
        updated: function () {
            const self = this;
            if (self.refreshIncomeMethodTemplate) {
                self.showIncomeMethodTemplate = true;
                self.refreshIncomeMethodTemplate = false;
            }
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
        }
    }
</script>

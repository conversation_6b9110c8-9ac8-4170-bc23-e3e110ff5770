<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div>
        <div class="md-table mdl-shadow--2dp" v-show="showCompsSearchView">
            <h2>Comparable Properties<span>Matched {{filteredCompsCount}} sales of {{totalCompsCountFilterById}} with {{selectedCompsCount}} selected</span></h2>
            <ul class="QVHV-tabs">
                <li class="QVHVTab-1" data-tab="QVHVTab-1" data-container="reportcompliance"><span class="is-active">Comps Search</span></li>
                <hr>
            </ul>


            <div class="QVHV-Container reportcompliance active">
                <ul class="QVHV-tabs hide">
                    <li><span class="is-active">Comps Filter</span></li>
                    <hr>
                </ul>

                <!-- COMPS FILTER CONTROLS START BELOW -->

                <div class="QVHV-formSection compsList-filters">
                    <input type="checkbox" checked id="compsFilters-trigger" class="compsFilters-trigger">
                    <label title="Filter Comps List" for="compsFilters-trigger"></label>
                    <div class="selectComps-confirm" @click="goToTinderView()">
                        <i title="Continue" id="" class="material-icons md-48"></i>
                    </div>
                    <div class="compsFilters">
                        <div class="advSearch-row">
                            <slider v-if="showSlider" sliderId="grossPriceDualSlider" label="Gross Price Range" iconClass="fiftyPct icons8-sell-property-filled" first="grossPriceFrom" second="grossPriceTo" :start="grossPriceSliderData.start" :step="grossPriceSliderData.step" :min="grossPriceSliderData.min" :max="grossPriceSliderData.max" type="currency" :connect="grossPriceSliderData.connect" data-cy="comparable-properties-gross-price"></slider>
                            <slider v-if="showSlider" sliderId="propertyDistanceSlider" label="Distance from Property (km)" iconClass="fiftyPct icons8-geo-fence" first="propertyFrom" :start="propertyDistanceSliderData.start" :step="propertyDistanceSliderData.step" :min="propertyDistanceSliderData.min" :max="propertyDistanceSliderData.max" type="decimal" :connect="propertyDistanceSliderData.connect" data-cy="comparable-properties-distance"></slider>
                        </div>
                        <div class="advSearch-row">
                            <date-range-filter v-if="showSlider" readonly="true" label="Comparable Sales Date Range" rangeId="comparableSalesDateRange" filterClass="advSearch-group fiftyPct icons8-calendar-filled" :defaultVal="compDefaultSaleDateRange" showCustomRange=false data-cy="comparable-properties-date-range"></date-range-filter>
                            <div class="advSearch-group twentyfivePct icons8-category-filled">
                                <label>Categories</label>
                                <span>
                                    <input class="advSearch-text" @change="loadCompSales(true)" type="text" v-model="searchParams.categories" data-cy="comparable-properties-categories">
                                </span>
                                <div class="valMessage"></div>
                            </div>
                            <div class="advSearch-group twentyfivePct icons8-crosshair-filled">
                                <label>Zones</label>
                                <span>
                                    <input class="advSearch-text" @change="loadCompSales(true)" type="text" v-model="searchParams.zones" data-cy="comparable-properties-zones">
                                </span>
                                <div class="valMessage"></div>
                            </div>
                        </div>
                    </div>

                    <!-- COMPS FILTER CONTROLS END ABOVE -->

                    <!-- COMPS LIST STARTS BELOW -->

                    <div class="compsList salesResults">
                        <div class="resultsInner-wrapper">

                            <!-- COMP LIST SORTING ROW STARTS BELOW -->

                            <div id="compsDesktop" class="sortRow-wrapper">
                                <div class="sortRow salesRow">
                                    <!-- ^^ THE ELEMENT ".sortrow" CONTROLS THE VIEW OF THE SEARCH RESULTS SORTING ROW -->
                                    <!-- ^^ THE ELEMENT ".sortrow" SHOULD INCLUDE ".salesRow" IF A SALES SEARCH HAS BEEN RUN -->
                                    <div data-value="value 0" data-sort="SELECTED" class="colHeader compSelect">
                                        <a>
                                            <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>
                                            Include<i class="material-icons md-dark">&#xE5C5;</i>
                                        </a>
                                    </div>
                                    <div data-value="value 1" data-sort="FULL_ADDRESS" class="colHeader address">
                                        <a>
                                            <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>
                                            Address<i class="material-icons md-dark">&#xE5C5;</i>
                                        </a>
                                    </div>
                                    <div data-value="value 2" data-sort="VAL_REF" class="colHeader valref">
                                        <a>
                                            <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>
                                            Val Ref<i class="material-icons md-dark">&#xE5C5;</i>
                                        </a>
                                    </div>
                                    <div class="searchDetails-wrapper">

                                        <div data-value="value 10" data-sort="QIVS_SALE_ID" class="colHeader qv-col-sale-id">
                                            <a>
                                                <span class="icon"><i class="sorter material-icons md-18 up">&#xE5DB;</i></span>Sale ID<i class="material-icons md-dark">&#xE5C5;</i>
                                            </a>
                                        </div>
                                        <div data-value="value 3" data-sort="SALE_DATE" class="colHeader saleDate active">
                                            <a>
                                                <span class="icon"><i class="sorter material-icons md-18 up">&#xE5DB;</i></span>Sale Date<i class="material-icons md-dark">&#xE5C5;</i>
                                            </a>
                                        </div>
                                        <div data-value="value 4" data-sort="GROSS_PRICE" class="colHeader grosssale">
                                            <a>
                                                <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Gross Sale<i class="material-icons md-dark">&#xE5C5;</i>
                                            </a>
                                        </div>
                                        <div data-value="value 5" data-sort="LAND_AREA" class="colHeader landarea">
                                            <a>
                                                <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Land Area (Ha)<i class="material-icons md-dark">&#xE5C5;</i>
                                            </a>
                                        </div>
                                        <div data-value="value 6" data-sort="TOTAL_FLOOR_AREA" class="colHeader tfa">
                                            <a>
                                                <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>TFA<i class="material-icons md-dark">&#xE5C5;</i>
                                            </a>
                                        </div>
                                        <div data-value="value 7" data-sort="TOTAL_LIVING_AREA" class="colHeader tla">
                                            <a>
                                                <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>TLA<i class="material-icons md-dark">&#xE5C5;</i>
                                            </a>
                                        </div>
                                    </div>
                                    <div data-value="value 8" data-sort="CATEGORY" class="colHeader category">
                                        <a>
                                            <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Category<i class="material-icons md-dark">&#xE5C5;</i>
                                        </a>
                                    </div>
                                    <div data-value="value 9" data-sort="DISTANCE" class="colHeader distance">
                                        <a>
                                            <span class="icon"><i class="sorter material-icons md-18 down">&#xE5DB;</i></span>Dist (km)<i class="material-icons md-dark">&#xE5C5;</i>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- COMP LIST SORTING ROW ENDS ABOVE -->

                            <!-- COMPARABLE PROPERTY LISTING STARTS BELOW ~~~~~ -->
                            <!-- THIS IS BASED ON SALES LISTINGS ~~~~~~~~~~~~~~ -->
                            <!-- NEW ELEMENTS ARE INDICATED WITH COMMENTS BELOW -->

                            <div class="compsRow-wrapper" v-for="sale,key in comparableSales" v-show="sale.showInSearchView">
                                <!-- ^^ THE ELEMENT ".resultsRow" CONTROLS THE VIEW OF THE OPEN PROPERTY CARD -->
                                <div  class="colCell compSelect">
                                    <input type="checkbox" v-model="sale.isTempSelected" @click="selectComp($event, key, false)" :id="'SelectCompSearch'+sale.id">
                                    <label :for="'SelectCompSearch'+sale.id">Include Comp</label>
                                </div> <!-- THE COMPSELECT CHECKBOX IS A NEW ELEMENT -->
                                <div class="resultsRow salesRow compsRow">
                                    <div class="colCell address">
                                        <span class="primaryThumb-Wrapper" v-bind:id="'primaryPhoto_'+sale.qupid"><img class="primaryPhoto_thumb" v-bind:src=sale.primaryPhoto></span>
                                        <div id="'fullAddressDiv_'+sale.qupid" class="fullAddress"> <!-- DEAD CODE @click="loadMasterDetails(sale.qupid)" --><!-- DEADER CODE @click="showMasterDetails(sale.qupid)"-->
                                            <span id="address1Span" v-html="sale.address1"></span>
                                            <span id="address2Span" v-html="sale.address2"></span>
                                            <div class="md-link">
                                                <div class="colCell qpid" v-html="sale.qupid"></div>
                                                <div class="colCell valref" v-html="sale.valRef"></div>
                                                <div class="colCell legaldesc" v-html="sale.legalDescription"></div>
                                                <ul class="occupier">
                                                    <li></li>
                                                    <li></li>
                                                </ul>
                                                <ul class="lastReval-date">
                                                    <li v-html="sale.effectiveDate"></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="sales-trafficLights" v-bind:data-id=sale.id v-bind:saleAnalysisType=sale.saleAnalysis>
                                            <div class="colCell qv-col-sale-id">{{ sale.qivsSaleId }}</div>
                                            <div class="colCell saleClassification" v-bind:class="{classOne: sale.priceValueRelationship=='1', classTwo: sale.priceValueRelationship=='2', classThree: sale.priceValueRelationship=='3'}" v-html="sale.classification"></div>
                                            <div id="saleStatusDiv" class="colCell saleStatus" v-html="sale.status" v-bind:class="[sale.status=='Pending' ? 'statusPending' : (sale.status=='Unconfirmed' ? 'statusUnconfirmed' : '')]"></div>	<!-- THIS ELEMENT CAN INCLUDE ".statusPending" or ".statusUnconfirmed" BASED ON THE SALE STATE -->
                                            <div id="saleDateDiv" class="colCell saleDate" v-html="sale.lastSaleDate" v-bind:class="[sale.status=='Pending' ? 'statusPending' : (sale.status=='Unconfirmed' ? 'statusUnconfirmed' : '')]"></div> 	<!-- THIS ELEMENT CAN INCLUDE ".statusPending" or ".statusUnconfirmed" BASED ON THE SALE STATE -->
                                            <div class="colCell saleAnalysis" v-html="sale.saleAnalysis"></div>		<!-- THE VALUE WILL CHANGE TO 'No' IF A SALES ANALYSIS FOR THIS PROPERTY IS NOT AVAILABLE IN MONARCH -->
                                            <ul class="vendPurchase">
                                                <li v-html="sale.parties"></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="colCell valref" v-html="sale.valRef" data-cy="comparable-property-valref"></div>
                                    <div class="searchDetails-wrapper">
                                        <div class="colCell qv-col-sale-id">{{ sale.qivsSaleId }}</div>
                                        <div class="colCell saleDate" v-html="sale.lastSaleDate" data-cy="comparable-property-sale-date"></div>
                                        <div id="salesNetRateDiv" class="colCell nsp">{{sale.netPrice}}<div class="nsptfa">{{sale.saleNetRate}}<span>/ m<sup>2</sup></span></div></div>
                                        <div class="colCell nsptocv" v-html="sale.nspCV"></div>
                                        <div class="colCell grosssale" v-html="sale.grossPrice" data-cy="comparable-property-gross-price"></div>
                                        <div class="colCell capval">{{sale.capitalValue}}<div class="cvnr">{{sale.cvNetRate}}<span>/ m<sup>2</sup></span></div></div>
                                        <div class="colCell landval">{{sale.landValue}}<div class="lvnr">{{sale.lvNetRate}}<span>/ m<sup>2</sup></span></div></div>
                                        <div class="colCell valimp">{{sale.valueOfImprovements}}<div class="vinr">{{sale.viNetRate}}<span>/ m<sup>2</sup></span></div></div>
                                        <div id ="chattelsDiv" class="colCell chattels" v-html="sale.chattels"></div>
                                        <div id="saleStatusDiv" class="colCell saleStatus" v-html="sale.status"></div>
                                        <div id="landAreaHaDiv" class="colCell landarea" v-html="sale.landArea" data-cy="comparable-property-land-area"></div>
                                        <div id="tfaAndTlaDiv" class="tfaTla-wrapper">
                                            <span id="tfaSpan" class="colCell tfa">{{sale.TFA}}<span>m<sup>2</sup></span></span>
                                            <span id="tlaSpan" class="colCell tla">{{sale.TLA}}<span>m<sup>2</sup></span></span>
                                        </div>
                                        <div class="colCell salegst" v-html="sale.gst"></div>
                                        <div class="colCell saleother" v-html="sale.other"></div>
                                    </div>
                                    <div id="categoryDiv" class="colCell category" v-html="sale.category" data-cy="comparable-property-category"></div>
                                    <div class="colCell distance" v-html="sale.tDist"></div> <!-- DISTANCE IS A NEW ELEMENT -->
                                    <div class="revalRates" v-if="((isInternalUser == false && isReadOnly == false) || isInternalUser) && sale.hasRevision">
                                        <div class="reval-capval">{{sale.revisionCapVal}}<span v-bind:class="[sale.capitalValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{sale.capitalValueDiff}}%</span><div class="cvnr">{{sale.revalCapValNetRate}}<span>/ m<sup>2</sup></span></div></div> 	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
                                        <div class="reval-landval">{{sale.revisionLandVal}}<span v-bind:class="[sale.landValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{sale.landValueDiff}}%</span><div class="lvnr">{{sale.revalLandValNetRate}}<span>/ m<sup>2</sup></span></div></div>	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
                                        <div class="reval-valimp">{{sale.revalValueOfImprovements}}<span v-bind:class="[sale.valueOfImprovementsDiff >= 0 ? 'valueUp' : 'valueDown']">{{sale.valueOfImprovementsDiff}}%</span><div class="vinr">{{sale.revalValueOfImprovementsNetRate}}<span>/ m<sup>2</sup></span></div></div>	<!-- THE CLASS ".valueUP" CAN BE CHANGED TO ".valueDOWN" BASED ON THE VALUE -->
                                        <div class="reval-nsptorcv">{{sale.revalNspToRCV}}</div>
                                    </div>
                                    <div class="photoGallery_propSearch" v-bind:id="'photos_'+sale.qupid">
                                        <span class="thumbWrapper" v-for="photo in sale.propertyPhotos"><img class="photoGallery_thumb" v-bind:data-id=photo.id v-bind:data-qupid=photo.qupid v-bind:data-property=photo.propertyId v-bind:src=photo.link></span>
                                    </div>
                                    <div class="extras">
                                        <ul class="md-landMas searchList">
                                            <li class="md-masIcon-category"><strong v-html="sale.category"></strong>Category</li>
                                            <li class="md-masIcon-eyb"><strong v-html="sale.effectiveYearBuilt"></strong>Effective Year Built</li>
                                            <li class="md-masIcon-landUse"><strong v-html="sale.landUse"></strong>Land Use</li>
                                            <li class="md-masIcon-units"><strong v-html="sale.units"></strong>Units</li>
                                            <li class="md-masIcon-bedrooms"><strong v-html="sale.bedrooms"></strong>Bedrooms</li>
                                            <li class="md-masIcon-toilets"><strong v-html="sale.toilets"></strong>Toilets</li>
                                            <li class="md-masIcon-walls" data-cy="comparable-property-walls"><strong v-html="sale.wallConstructionAndCondition"></strong>Wall Construction and Condition</li>
                                            <li class="md-masIcon-roof" data-cy="comparable-property-roof"><strong v-html="sale.roofConstructionAndCondition"></strong>Roof Construction and Condition</li>
                                            <li class="md-masIcon-umrg"><strong v-html="sale.underMainRoofGarages"></strong>Under Main Roof Garaging</li>
                                            <li class="md-masIcon-fsg"><strong v-html="sale.freeStandingGarages"></strong>Free Standing Garaging</li>
                                            <li class="md-masIcon-oli"><strong v-html="sale.otherLargeImprovements"></strong>Other Large Improvements</li>
                                            <li class="md-masIcon-modernisation"><strong v-html="sale.modernisation"></strong>Modernisation</li>
                                            <li class="md-masIcon-zone"><strong v-html="sale.zone" data-cy="comparable-property-zone"></strong>Zone</li>
                                            <li class="md-masIcon-lotPosition"><strong v-html="sale.lotPosition"></strong>Lot Position</li>
                                            <li class="md-masIcon-contour"><strong v-html="sale.contour"></strong>Contour</li>
                                            <li class="md-masIcon-viewScope"><strong v-html="sale.viewScope"></strong>View and Scope</li>
                                            <li class="md-masIcon-production"><strong v-html="sale.production"></strong>Production</li>
                                            <li class="md-masIcon-maoriLand"><strong v-html="sale.maoriLand"></strong>Maori Land</li>
                                        </ul>
                                    </div>
                                    <ul class="toolbar listingControls righty ">
                                        <li class="md-sales" @click="externalSalesLinkHandler(sale.qupid, sale.qivsSaleId)"><label>SALE</label><i class="material-icons">call_made</i></li>	<!-- THIS ITEM LINKS TO THIS SALE IN QIVS -->
                                        <li class="md-qivs" @click="externalQIVSLinkHandler(sale.qupid)"><label>QIVS</label><i class="material-icons">call_made</i></li>		<!-- THIS ITEM LINKS TO THIS PROPERTY RECORD IN QIVS -->
                                        <li class="md-qvms" v-if="isInternalUser" @click="externalMapLinkHandler(sale.qupid)"><label>MAP</label><i class="material-icons">call_made</i></li>		<!-- THIS ITEM LINKS TO THIS PROPERTY RECORD IN THE MAPPING APPLICATION -->
                                        <!--<li class="mdl-button mdl-js-button mdl-button&#45;&#45;icon" v-bind:class="{disabled: isReadOnly == true}" title="Upload Photos"><a class="photo-uploader" href="javascript:void(0)" v-bind:data-property="sale.propertyId" v-bind:data-qupid="sale.qupid"><i class="material-icons md-dark">&#xE251;</i></a></li>-->
                                        <li class="closer mdl-button mdl-js-button mdl-button--icon" title="Close"><i class="material-icons md-dark">&#xE5CD;</i></li>
                                    </ul>
                                </div>
                            </div>

                            <!-- COMPARABLE PROPERTY ENDS HERE -->

                        </div>
                    </div>

                    <!-- COMPS LIST ENDS HERE -->
                    <div class="loadingSpinner loadingSpinnerComps"></div>
                </div>
            </div>
        </div>
        <div class="md-table mdl-shadow--2dp" v-show="showCompsTinderView">
            <div v-bind:class="[currentState == 'open' ? 'selectedComps-open': '']">
                <h2>Comparable Properties <a @click="goBack()">BACK</a><span>{{selectedCompsCount}} of {{selectedComparableSales.length}} comparable properties selected</span></h2>
                <ul class="toolbar righty">
                    <li class="selectedComps-count"><span v-html="selectedCompsCount"></span></li>
                    <li class="addComp"><input type="text" id="comparablePropertiesSaleIdText" v-model="saleId" @change="fetchSaleById()" @keypress="keyPress" placeholder="Add SaleID"><button><i id="comparablePropertiesSaleIdButton" @click="fetchSaleById()" class="material-icons">&#xE146;</i></button></li>
                    <li class="mdl-button mdl-button--icon" v-bind:class="[comparableSales && comparableSales.length == 0 ? 'disabled' : '']" @click="refreshComps()" title="Refresh comps"><i class="material-icons md-dark">&#xE8B3;</i></li>
                    <li class="mdl-button mdl-button--icon" v-bind:class="[exportingComps == true || (comparableSales && comparableSales.length == 0) ? 'disabled' : '']" @click="exportComps()" title="Export comps"><i class="material-icons md-dark">&#xE06F;</i></li>
                    <li class="mdl-button mdl-button--icon" title="Show selected comps" v-bind:class="[currentState == 'open' ? 'down' : '', (selectedCompsCount<2 && currentState == 'closed') ? 'disabled' : '']" @click="showSelectedComps()"><i class="material-icons md-dark">&#xE8D7;</i></li>
                </ul>
                <div class="md-full compProperty" v-for="sale,key in comparableSales" v-show="((currentState=='closed' && key==currentCompSale) || (currentState=='open' && sale.isSelected))">
                    <div class="md-propertyOverview ">
                        <div class="md-propertyOverview-inner">
                            <div class="md-address">
                                <em>
                                    <span>Property details at date of last sale: <strong v-html="sale.lastSaleDate"></strong></span>
                                    <span>Effective Date: <strong v-html="sale.effectiveDate"></strong></span>
                                </em>
                                <h3>{{sale.address1}}<span>{{sale.address2}}</span></h3>
                            </div>
                            <ul class="md-totals">
                                <li v-html="sale.valRef"></li>
                                <li><label>Total Floor:</label>{{sale.TFA}}<span>m<sup>2</sup></span></li>
                                <li><label>Total Living:</label>{{sale.TLA}}<span>m<sup>2</sup></span></li>
                                <li><label>Land Area:</label>{{sale.landArea}}<span>Ha</span></li>
                            </ul>
                            <ul class="md-values">
                                <li><label>Capital Value</label>{{sale.capitalValue}}<span><strong>{{sale.cvNetRate}}</strong>m<sup>2</sup></span></li>
                                <li><label>Land Value</label>{{sale.landValue}}<span><strong>{{sale.lvNetRate}}</strong>m<sup>2</sup></span></li>
                                <li><label>Value of Improvements</label>{{sale.valueOfImprovements}}
                                    <span><strong>{{sale.viNetRate}}</strong>m<sup>2</sup></span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- SEE NOTES 13–15: Property photos -->

                    <div class="md-photoGallery" v-bind:class="'comp-carousel'+sale.id">
                     <!--<ul class="photoUploader-icon">
                            <li v-bind:class="[isReadOnly == true ? 'disabled' : '', 'photo-uploader'+key]" v-bind:data-property="sale.propertyId"
                                title="Upload Photos"
                                data-upgraded=",MaterialButton">
                                <i class="material-icons md-light">&#xE251;</i>
                            </li>
                        </ul>-->
                    </div>
                    <div class="md-propertySale">
                        <ul class="md-values md-lastSale">
                            <li><label>Net Sale Price</label>{{sale.netPrice}}</li>
                            <li><label>Chattels</label>{{sale.chattels}}</li>
                            <li><label>Analysed Land Value</label>{{sale.analysedLandValue}}</li>
                            <li><label>NSP/CV</label>{{sale.nspCV}}</li>
                            <li><label>Building Net Rate</label>{{sale.buildingNetRate}}<span>m<sup>2</sup></span></li>
                            <li><label>Gross Rate</label>{{sale.mainUnitGrossRate}}<span>m<sup>2</sup></span></li>
                            <li class="saleStatus">{{sale.status}}</li>
                        </ul>
                    </div>

                    <!-- SEE NOTES 11–12: Sales Analysis and QIVS application links -->

                    <ul class="qivsLinks">
                        <li class="md-salesAnalysis" data-qupid="" @click="openSalesAnalysis(sale.qivsSaleId)"><label>ANALYSIS</label> <i class="material-icons">call_made</i></li>
                        <li class="md-qv-col-sale-id" data-qupid=""><label>SALE ID: {{ sale.qivsSaleId }}</label></li>
                        <li class="md-qivs" data-qupid="" @click="openSale(sale.qivsSaleId, sale.qupid)"><label>SALE</label> <i class="material-icons">call_made</i></li>
                        <li class="md-qvms" v-if="isInternalUser" data-qupid="" @click="externalMapLinkHandler(sale.qupid)"><label>MAP</label> <i class="material-icons">call_made</i></li>
                        <li class="md-qvms" @click="openGoogleSearchTab(sale.address1, sale.address2)"><label>WEB</label> <i class="material-icons icon--flipped">search</i></li>
                        <li v-bind:class="[isReadOnly == true ? 'disabled' : '', 'photo-uploader'+sale.id, 'comp-photoUploader']" v-bind:data-property="sale.propertyId"
                            title="Upload Photos"
                            data-upgraded=",MaterialButton">
                            <i class="material-icons md-light">&#xE251;</i>
                        </li>
                    </ul>
                    <div class="extras">
                        <ul class="md-landMas searchList">
                            <li class="md-masIcon-category"><strong v-html="sale.category"></strong>Category</li>
                            <li class="md-masIcon-eyb"><strong v-html="sale.effectiveYearBuilt"></strong>Effective Year Built</li>
                            <li class="md-masIcon-landUse"><strong v-html="sale.landUse"></strong>Land Use</li>
                            <li class="md-masIcon-units"><strong v-html="sale.units"></strong>Units</li>
                            <li class="md-masIcon-bedrooms"><strong v-html="sale.bedrooms"></strong>Bedrooms</li>
                            <li class="md-masIcon-toilets"><strong v-html="sale.toilets"></strong>Toilets</li>
                            <li class="md-masIcon-walls"><strong v-html="sale.wallConstructionAndCondition"></strong>Wall Construction and Condition</li>
                            <li class="md-masIcon-roof"><strong v-html="sale.roofConstructionAndCondition"></strong>Roof Construction and Condition</li>
                            <li class="md-masIcon-umrg"><strong v-html="sale.underMainRoofGarages"></strong>Under Main Roof Garaging</li>
                            <li class="md-masIcon-fsg"><strong v-html="sale.freeStandingGarages"></strong>Free Standing Garaging</li>
                            <li class="md-masIcon-oli"><strong v-html="sale.otherLargeImprovements"></strong>Other Large Improvements</li>
                            <li class="md-masIcon-modernisation"><strong v-html="sale.modernisation"></strong>Modernisation</li>
                            <li class="md-masIcon-zone"><strong v-html="sale.zone"></strong>Zone</li>
                            <li class="md-masIcon-lotPosition"><strong v-html="sale.lotPosition"></strong>Lot Position</li>
                            <li class="md-masIcon-contour"><strong v-html="sale.contour"></strong>Contour</li>
                            <li class="md-masIcon-viewScope"><strong v-html="sale.viewScope"></strong>View and Scope</li>
                            <li class="md-masIcon-production"><strong v-html="sale.production"></strong>Production</li>
                            <li class="md-masIcon-maoriLand"><strong v-html="sale.maoriLand"></strong>Maori Land</li>
                        </ul>
                        <div class="QVHV-formSection">
                            <h3><button @click="toggleAddressEdit()" class="icons8-mailbox-opened-flag-up-filled"></button>  Change Comparable Address</h3>
                                <div class="advSearch-group hundyPct" v-if="showAddressEdit">
                                        <span>
                                            <input type="text" maxlength="300" v-model="sale.comparableAddressEdited" @change="saveComparableProperties(false, false, false)" style="color: #000000;">
                                        </span>
                                </div>
                            <h3>Additional Information for this comparable sale</h3>
                            <div class="advSearch-row">
                                <div class="advSearch-group twentyfivePct">
                                    <label>Label (e.g. Bedrooms, Sleepout, etc. )</label>
                                    <span>
                                        <input type="text" maxlength="100" v-model="sale.otherFeaturesLabel" @change="saveComparableProperties(false, false, false)" style="color: #000000;">
                                    </span>
                                </div>
                                <div class="advSearch-group seventyfivePct">
                                    <label>Description for Label</label>
                                    <span>
                                        <input type="text" maxlength="300" v-model="sale.otherFeatures" @change="saveComparableProperties(false, false, false)" style="color: #000000;">
                                    </span>
                                </div>
                            </div>
                            <div class="advSearch-row">
                                <div class="advSearch-group twentyfivePct">
                                    <span>
                                        <input type="text" maxlength="100" v-model="sale.otherFeaturesLabel2" @change="saveComparableProperties(false, false, false)" style="color: #000000;">
                                    </span>
                                </div>
                                <div class="advSearch-group seventyfivePct">
                                    <span>
                                        <input type="text" maxlength="300" v-model="sale.otherFeatures2" @change="saveComparableProperties(false, false, false)" style="color: #000000;">
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEE NOTES 17–18: The <div.edit_area> element is based on the jeditable.js library  -->

                    <div class="comparableStatement">
                        <span>
                            <select @change="saveComparableProperties(false, true, false)" v-model="sale.comparableQuality">
                                <option value="I">Inferior</option>
                                <option value="C">Comparable</option>
                                <option value="S">Superior</option>
                            </select>
                        </span>
                        <textarea class="edit_area" @change="saveComparableProperties(false, true, false)" v-model="sale.summary"></textarea>
                    </div>

                    <div class="compControls">
                        <span>
                            <i @click="loadPrevCompSale(key)" v-show="prev(key)" title="Previous Assessment" class="listingButton material-icons assessmentNav" data-direction="back">arrow_back</i>
                            <input type="checkbox" @click="selectComp($event, key, true)" v-model="sale.isTempSelected" :id="'SelectComp'+sale.id" class="selectComp-trigger">
                            <label :for="'SelectComp'+sale.id">Include this sale</label>
                            <i @click="loadNextCompSale(key)" v-show="next(key)" title="Next Assessment" class="listingButton material-icons assessmentNav" data-direction="forward">arrow_forward</i>
                        </span>
                        <ul class="compBubbles">
                            <li v-for="compBubble,key in comparableSales" @click="setActiveComp(key)" v-bind:class="[currentCompSale == key ? 'active' : (compBubble.isTempSelected ? 'selectedComp' : '')]"><span></span></li>
                        </ul>
                    </div>

                </div>
            </div>
        </div>
        <div class="QVHV-buttons">
            <div class="QVHV-buttons-left" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="primary" v-on:click="saveComparableProperties(false, true, false)">Save</button>
            </div>
            <div class="QVHV-buttons-right" v-bind:class="[isDataSaving ? 'disabled' : '']">
                <button class="secondary" v-if="jobStatus == 'R'" v-on:click="previousStep()">Back</button>
                <button class="primary" v-on:click="saveComparableProperties(true, true, false)">Next Step</button>
            </div>
        </div>
        <warning :header="warningHeader" :message="warningMessage" class="compSales" close="Ok"></warning>
    </div>
</template>
<script>
    import { mapState } from 'vuex';
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import numeral from 'numeral';
    import * as json2csv from "json2csv";
    import * as fileSaver from "file-saver";
    import deepEqual from 'deep-equal';
    import Warning from '../common/Warning.vue';
    import * as ajaxq from '../../ajaxq.js';
    import Slider from '../filters/Slider.vue';
    import DateRangeFilter from '../filters/DateRangeFilter.vue';
    import commonUtils from '../../utils/CommonUtils';
    import { openUrlInNewTab, openMap } from '../../utils/QivsUtils';
    // const download = require('../../download');
    import moment from 'moment';
    import { useSaleAnalysis } from '@/composables/useSaleAnalysis';

    export default {
        components: {
            Warning,
            Slider,
            DateRangeFilter
        },
        mixins: [commonUtils],
        setup() {
            const { tryOpenAnalysisById } = useSaleAnalysis();

            return {
                tryOpenAnalysisById
            };
        },
        data: function() {
            const self = this
            var start = moment().subtract(3, 'month').startOf('month');
            var end = moment();
            return {
                showAddressEdit: false,
                showComparableTemplate: false,
                refreshComparableTemplate: false,
                comparableSales: [],
                saleFetched: false,
                selectedComparableSales: [],
                currentState: 'closed',
                currentCompSale: 0,
                selectedCompsCount: 0,
                selectedCompKeys: [],
                saleId: '',
                isDataSaving: false,
                newSaleAdded: false,
                loadExistingCompSales: false,
                valuationJobId: null,
                refreshPhotos: false,
                warningHeader: 'No Sale Found',
                warningMessage: '',
                exportingComps: false,
                fetchingSale: false,
                saveAllCompSales: true,
                jobStatus: undefined,
                showCompsSearchView: true,
                showCompsTinderView: false,
                grossPriceSliderData: {
                    start: [0, 0],
                    connect: true,
                    step: 1000,
                    min: 0,
                    max: 2000000
                },
                propertyDistanceSliderData: {
                    start: [0.5],
                    connect: [true, false],
                    step: 0.5,
                    min: 0,
                    max: 10
                },
                filters: {
                    grossSale:{
                        from: moment(start),
                        to: moment(end)
                    },
                    grossPrice: {
                    },
                    propertyDistance: {
                        from: 0.0001,
                        to: 0.5
                    },
                    categories: 'R*',
                    zones: ''
                },
                compDefaultSaleDateRange : start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'),
                showSlider: false,
                defaultPropertyData: undefined,
                lowestCompSalePrice: undefined,
                highestCompSalePrice: undefined,
                searchSort: 'rawSaleDate',
                searchSortOrder: 'DESC',
                isCompsSummariesDirty:  false,
                saleSummaryCopy: null,
                isDirty: false,
                searchParams: {
                    useBestMatch: false,
                    homeValuationId: null,
                    includedSaleIds: [],
                    excludedSaleIds: [],
                    selected: null,
                    grossPriceFrom: null,
                    grossPriceTo: null,
                    maxDistance: 0.5,
                    saleDateFrom: moment(start),
                    saleDateTo: moment(end),
                    categories: 'R*',
                    zones: null,
                    offset: 0,
                    max: null,
                    sort: ['SALE_DATE'],
                    order: 'DESC'
                },
                totalCompsCountFilterById: 0,
                saleSummary: [],
                loadCounter: 0,
                filteredCompsCount: 0,
                isNewValuation: false,
                initialLoadComplete: false,
                homeValuationReportTypeCode: '',
                defaultCompsSelected: false,
                selectedCompSaleKeyList: [],
            }
        },
        computed: {
            ...mapState('userData', [
                'isInternalUser',
                'isReadOnlyUser',
                'qivsUrl'
            ]),
            isReadOnly: function () {
                return self.isReadOnlyUser;
            },

        },
        watch: {
            selectedCompSaleKeyList: function () {
                let self = this;
                if(self.useBestMatchDefaultComps()){
                    self.selectedCompsCount = 0;
                    self.selectDefaultComps();
                }
            },
        },
        methods: {

            useBestMatchDefaultComps: function() {
                let self = this;
                if(
                    self.filteredCompsCount > 0
                    && self.filteredCompsCount === self.comparableSales.length
                    && !self.defaultCompsSelected
                    && self.searchParams.useBestMatch
                ){
                    return true;
                }
                else {
                    return false;
                }
            },
            toggleAddressEdit: function(event){
                var self = this;
                if(self.showAddressEdit == false){
                    self.showAddressEdit = true;
                }
                else {
                    self.showAddressEdit = false;
                }
            },
            calculateQuality: function(sale){
                const self = this
                var netPrice = numeral(sale.netPrice).value()
                var marketEstimate = self.defaultPropertyData ? (self.defaultPropertyData.marketEstimateValue ? numeral(self.defaultPropertyData.marketEstimateValue).value() : 0) : 0

                var tenPMarketEstimate = Math.round(marketEstimate/10)

                if (tenPMarketEstimate && netPrice) {
                    var priceDiff = marketEstimate - netPrice
                    if (priceDiff > 0) {
                        if (priceDiff <= tenPMarketEstimate) {
                            sale.comparableQuality = 'C'
                        }
                        else {
                            sale.comparableQuality = 'I'
                        }
                    }
                    else {
                        if (Math.abs(priceDiff) <= tenPMarketEstimate) {
                            sale.comparableQuality = 'C'
                        }
                        else {
                            sale.comparableQuality = 'S'
                        }
                    }
                }
                else {
                    sale.comparableQuality = 'C'
                }
            },
            prev: function(index){
                var self = this;
                var showPrev = false
                for (var i = index; i > 0; i--) {
                    showPrev = true
                    break;
                }
                return showPrev;
            },
            next: function(index){
                var self = this;
                var showNext = false
                for (var i = index; i < self.comparableSales.length; i++) {
                    showNext = true
                    break
                }
                return showNext;
            },
            getFormattedDate: function(date) {
                if (date && date != '') {
                    return moment(String(date)).format('DD/MM/YYYY')
                }
                return ''
            },
            getSaleAnalysisFromPropertyData: function(hasSaleAnalysis, categoryType) {
                var saleAnalysis = ''
                if (categoryType == 'R' || categoryType == 'L') {
                    if (hasSaleAnalysis) {
                        saleAnalysis = 'Yes'
                    }
                    else {
                        saleAnalysis = 'New'
                    }
                }
                else {
                    saleAnalysis = 'N/A'
                }
                return saleAnalysis
            },
            externalMapLinkHandler: function(qpid) {
                openMap(qpid)
            },
            externalSalesLinkHandler: function(qupid, saleId) {
                var url = this.qivsUrl;
                if (url && url != '') {
                    var ua = window.navigator.userAgent;
                    var old_ie = ua.indexOf('MSIE ');
                    var new_ie = ua.indexOf('Trident/');

                    var isIE = ((old_ie > -1) || (new_ie > -1));

                    if(qupid && saleId) {
                        url = url + '/default.asp?Property/Sales/Sales.asp?sAction=ViewSale&SaleID='+saleId+'&QPID='+qupid;
                    }
                    if(isIE) {
                        var w = window.open(url, "QIVZ");
                        w.close();
                    }
                    window.open(url, "QIVZ");
                }
            },
            externalQIVSLinkHandler: function(qupid) {
                var url = this.qivsUrl;
                if (url && url != '') {
                    var ua = window.navigator.userAgent;
                    var old_ie = ua.indexOf('MSIE ');
                    var new_ie = ua.indexOf('Trident/');

                    var isIE = ((old_ie > -1) || (new_ie > -1));

                    if(qupid) {
                        url = url + "/default.asp?Property/masterdetails.aspx?Qpid=" + qupid;
                    }
                    if(isIE) {
                        var w = window.open(url, "QIVZ");
                        w.close();
                    }
                    window.open(url, "QIVZ");
                }
            },
            toggleSale: function(sale, open){
                sale.openProp = open
            },
            generateSummaryForComps: function(comp, callback){
                const self = this
                var searchCriteria = {}
                searchCriteria.qivsSaleIds = [comp.qivsSaleId]
                var compSaleCriteria = {}
                compSaleCriteria.saleSearchCriteria = searchCriteria
                compSaleCriteria.homeValuationId = self.valuationJobId
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.ComparableSales.getComparableSaleBySaleId().url,
                    data: JSON.stringify(compSaleCriteria),
                    contentType: 'application/json',
                    cache: false,
                    success: function (response) {
                        if(response.length > 0) {
                            comp.comparableDescription = response[0].summary;
                        }
                        callback();
                    },
                    error: function (response) {
                        self.errorHandler(response);
                        callback();
                    }
                });
            },
            goToTinderView: function(){
                const self = this
                self.refreshComps();
            },
            goToCompsSearchView: function(){
                const self = this
                self.showCompsSearchView = true
                self.showCompsTinderView = false
            },
            previousStep: function(){
                const self = this
                self.saveComparableProperties(false, false, false);
                EventBus.$emit("home-valuation-back", "propertyDetailsStepper");
            },
            openSalesAnalysis: function(saleId){
                this.tryOpenAnalysisById(saleId);
            },
            openSale: function(saleId, qupid){
                const url = this.$router.resolve({
                    name: 'property-sale',
                    params: {
                        id: saleId,
                        qpid: qupid,
                    },
                });
                window.open(url.href, '_blank');
            },
            getRenderableValue: function(val) {
                return (val && val != '') ? val : '-'
            },
            getPhotos: function(property, key) {
                var self = this;
                var media = jsRoutes.controllers.MediaController.getMediaByOwner(property);
                $.ajax({
                    type: "GET",
                    url: media.url,
                    cache: false,
                    success: function (response) {
                        self.registerCarousel(key);
                        $('.comp-carousel'+key).slick('removeSlide', null, null, true);
                        $.each(response, function(index, photo) {
                            $('.comp-carousel'+key).slick('slickAdd',
                                "<img class=\"md-primary comp-carousel-photo"+key+"\" data-id=\"" + photo.id + "\" data-property=\"" + photo.ownerId + "\" src=\""+ photo.mediaItem.mediumImageUrl +"\"/>");
                        });
                        self.registerPhotoClickHandler(key);
                    },
                    error: function (response) {
                        console.log("Unable to get photos");
                        self.errorHandler(response);
                    }
                });
            },
            registerCarousel: function(key) {
                $(".comp-carousel"+key).not('.slick-initialized').slick({
                    dots: true,
                    infinite: false,
                    speed: 300,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    swipe: false
                });
            },
            registerPhotoClickHandler: function(key){
                var self = this;
                $('.comp-carousel-photo'+key).off("click").click(function(evt){
                    var propertyId = $(this).data('property');
                    var photoId = $(this).data('id');
                    var path = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?propertyId='+propertyId+'&photoId='+photoId;
                    var searchWindow = window.open(path,'PropertyPhotos','scrollbars=no,resizable=yes,height=800,width=1024');
                    searchWindow.focus();
                    var timer = setInterval(function() {
                        if(searchWindow.closed == true) {
                            self.getPhotos(propertyId, key);
                            clearInterval(timer);
                        }
                    }, 1000);
                });
            },
            registerPhotoUploaderHandler: function(key) {
                var self = this;
                $('.photo-uploader'+key).off("click").click(function(evt){
                    var propertyId = $(this).data('property');
                    var path = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?propertyId='+propertyId;
                    var searchWindow =  window.open(path,'PropertyPhotos','scrollbars=no,resizable=yes,height=800,width=1024');
                    searchWindow.focus();
                    var timer = setInterval(function() {
                        if(searchWindow.closed == true) {
                            self.getPhotos(propertyId, key);
                            clearInterval(timer);
                        }
                    }, 1000);
                });
            },
            generateDataForSaving: function(){
                const self = this
                var compData = []
                for (var i = 0; i < self.comparableSales.length; i++) {
                    var currComp = self.comparableSales[i]
                    if (currComp.isTempSelected) {
                        var compSummary = {}
                        compSummary.id = currComp.id
                        compSummary.qivsSaleId = currComp.qivsSaleId
                        compSummary.qupid = currComp.qupid
                        compSummary.comparableDescription = currComp.summary
                        compSummary.distance = currComp.dist
                        compSummary.otherFeatures = currComp.otherFeatures
                        compSummary.otherFeatures2 = currComp.otherFeatures2
                        compSummary.otherFeaturesLabel = currComp.otherFeaturesLabel
                        compSummary.otherFeaturesLabel2 = currComp.otherFeaturesLabel2
                        compSummary.comparableAddressEdited = currComp.comparableAddressEdited
                        compSummary.saleDate = currComp.saleDate
                        compSummary.grossSalePrice = currComp.rawGrossPrice
                        compSummary.selected = true
                        compSummary.added = currComp.added
                        if (currComp.comparableQuality) {
                            var obj = {}
                            obj.code = currComp.comparableQuality
                            obj.description = obj.code=='C' ? 'Comparable' : (obj.code=='I' ? 'Inferior' : 'Superior')
                            compSummary.comparableQuality = obj
                        }
                        compData.push(compSummary)
                    }
                }
                self.saleSummary = compData;
            },
            saveComparableProperties: function(nextStep, persist, skipGenerate, callback){
                const self = this
                var event = {};
                if(self.showCompsTinderView && !skipGenerate) self.generateDataForSaving();
                event.comparableSales = self.saleSummary;
                event.next = nextStep;
                event.persist = persist;
                self.isDataSaving = persist ? true : false;
                var haveChanges = !self.jsonEqual(self.saleSummary, self.saleSummaryCopy);
                event.isDirty = haveChanges;
                self.isDirty = haveChanges;
                EventBus.$emit('home-valuation-comparable-properties', event);
                if(callback) callback();
            },
            getSaleIndex: function(saleId){
                const self = this;
                for (var i = 0; i < self.comparableSales.length; i++) {
                    if (self.comparableSales[i].qivsSaleId == saleId) {
                        return i
                    }
                }
                return -1
            },
            getPropertyPhotos: function(photoURLs, propertyId, qupid) {
                var propertyPhotos = [];
                var primaryPhoto = 'assets/images/property/addPhotos.png';
                $.each(photoURLs, function (i, obj) {
                    if(obj.isPrimary) {
                        primaryPhoto = obj.mediaItem.smallImageUrl;
                    }
                    propertyPhotos.push({
                        'id': obj.id,
                        'propertyId': propertyId,
                        'qupid': qupid,
                        'link': obj.mediaItem.mediumImageUrl
                    });
                });
                var result = {primary: primaryPhoto, list: propertyPhotos};
                return result;
            },
            generateCompSale: function(comp, updatePhotos) {
                const self = this

                var sale = comp.sale
                var property = comp.primaryProperty
                var saleAnalysis = comp.saleAnalysis
                var hasSaleAnalysis = comp.hasSaleAnalysis
                var photoURLs = comp.photoURLs
                var compToRender = {}
                compToRender.id = sale.id
                if (comp.distance) {
                    compToRender.dist = comp.distance
                    compToRender.tDist = Number(comp.distance).toFixed(1)
                }
                 if(comp.selected){
                    compToRender.isSelected = true;
                    compToRender.isTempSelected = true;
                }
                compToRender.summary = comp.summary
                compToRender.qivsSaleId = sale.qivsSaleId
                compToRender.parties = sale.parties
                compToRender.qupid = sale.primaryProperty ? sale.primaryProperty.qupid : ''
                //compToRender.propertyId = comp.property.properties[0].id
                compToRender.propertyId = comp.propertyID
                compToRender.lastSaleDate = self.getFormattedDate(sale.saleDate)
                compToRender.rawSaleDate = moment(sale.saleDate).valueOf()
                compToRender.saleDate = sale.saleDate
                compToRender.effectiveDate = self.getFormattedDate(sale.revisionDate)
                compToRender.other = sale.other ? numeral(sale.other).format('$0,0') : '$0'
                compToRender.gst = sale.gst ? numeral(sale.gst).format('$0,0') : '$0'
                var classifications = sale.classifications
                compToRender.classification = classifications ? (classifications.saleType ? classifications.saleType.code : '') : ''
                compToRender.classification += classifications ? (classifications.saleTenure ? classifications.saleTenure.code : '') : ''
                compToRender.classification += classifications ? (classifications.priceValueRelationship ? classifications.priceValueRelationship.code : '') : ''
                compToRender.priceValueRelationship = classifications ? (classifications.priceValueRelationship ? classifications .priceValueRelationship.code : '') : ''
                var currProperty = sale.primaryProperty
                var streetNumberSuffix = currProperty.address ? (currProperty.address.streetNumberSuffix ? ' '+currProperty.address.streetNumberSuffix : '') : ''
                compToRender.address1 = currProperty.address ? ((currProperty.address.streetNumber ? currProperty.address.streetNumber : '')+streetNumberSuffix+ ' ' + (currProperty.address.streetName ? currProperty.address.streetName : '')  + (currProperty.address.streetType ? ' '+currProperty.address.streetType.description + ',' : '')) : ''
                if (compToRender.address1.indexOf('undefined') !== -1) {
                    compToRender.address1 = ''
                }
                compToRender.address2 = currProperty.address ? (currProperty.address.suburb ? (currProperty.address.suburb+', ') : '') : ''
                compToRender.address2 += currProperty.address ? (currProperty.address.town ? currProperty.address.town + ', ' : '') : ''
                let streetAndSuburb = currProperty.address.streetAddress && currProperty.address.suburb ? currProperty.address.streetAddress + ", " + currProperty.address.suburb: '';
                compToRender.comparableAddressEdited = compToRender.comparableAddressEdited
                    ? compToRender.comparableAddressEdited
                    : (streetAndSuburb != ''
                    ? streetAndSuburb
                    : currProperty.address.streetAddress && !currProperty.address.suburb
                    ? currProperty.address.streetAddress
                    : !currProperty.address.streetAddress && currProperty.address.suburb
                    ? currProperty.address.suburb
                    : '');
                compToRender.address2 += currProperty.territorialAuthority ? currProperty.territorialAuthority.name : ''
                if (compToRender.address2.indexOf('undefined') !== -1) {
                    compToRender.address2 = ''
                }
                compToRender.streetName = currProperty.address ? (currProperty.address.streetName ? currProperty.address.streetName : '') : ''
                compToRender.streetNumber = currProperty.address ? (currProperty.address.streetNumber ? currProperty.address.streetNumber : '') : ''
                compToRender.streetNumberSuffix = currProperty.address ? (currProperty.address.streetNumberSuffix ? currProperty.address.streetNumberSuffix : '') : ''
                compToRender.suburb = currProperty.address ? currProperty.address.suburb : ''
                compToRender.town = currProperty.address ? currProperty.address.town : ''
                compToRender.fullAddress = compToRender.address1 + compToRender.address2

                compToRender.valRef = currProperty.rollNumber ? currProperty.rollNumber : ''
                compToRender.valRef += currProperty.assessmentNumber ? '/'+currProperty.assessmentNumber : ''
                compToRender.valRef += currProperty.suffix ? ' ' + currProperty.suffix : ''
                if (compToRender.valRef.indexOf('undefined') !== -1) {
                    compToRender.valRef = ''
                }

                compToRender.legalDescription = currProperty.legalDescription
                compToRender.category = self.getRenderableValue(currProperty.category ? currProperty.category.code : '')
                compToRender.TLA = currProperty.massAppraisalData ? (currProperty.massAppraisalData.totalLivingArea ? currProperty.massAppraisalData.totalLivingArea : '0') : '0'
                compToRender.TFA = currProperty.landUseData ? (currProperty.landUseData.totalFloorArea ? currProperty.landUseData.totalFloorArea : '0') : '0'
                //compToRender.dist = 10
                compToRender.saleAnalysis = self.getSaleAnalysisFromPropertyData(hasSaleAnalysis, compToRender.category.charAt(0))

                compToRender.landArea = currProperty.landUseData ? currProperty.landUseData.landArea : ''
                if (typeof compToRender.landArea == 'number') {
                    compToRender.landArea = compToRender.landArea.toFixed(4)
                }

                compToRender.landValue = numeral(sale.landValue).format('$0,0')
                compToRender.capitalValue = numeral(sale.capitalValue).format('$0,0')

                var valueOfImprovements = 0
                var landValue = sale.landValue
                var capitalValue = sale.capitalValue
                var totalFloorArea = currProperty.landUseData ? currProperty.landUseData.totalFloorArea : 0
                var landArea = currProperty.landUseData ? currProperty.landUseData.landArea : 0
                var netPrice = sale.price ? sale.price.net : 0

                var saleNetRate = 0
                if (netPrice > 0 && totalFloorArea > 0) {
                    saleNetRate = Math.round(netPrice/totalFloorArea)
                }
                compToRender.saleNetRate = numeral(saleNetRate).format('$0,0')

                if (landValue && landValue > 0 && capitalValue && capitalValue > 0) {
                    valueOfImprovements = Math.round(capitalValue - landValue)
                }
                compToRender.valueOfImprovements = numeral(valueOfImprovements).format('$0,0')

                var cvNetRate = 0
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0) {
                    cvNetRate = Math.round(capitalValue/totalFloorArea)
                }
                compToRender.cvNetRate = numeral(cvNetRate).format('$0,0')

                var lvNetRate = 0
                if (landArea > 0 && landValue && landValue > 0) {
                    lvNetRate = Math.round(landValue/(landArea*10000))
                }
                compToRender.lvNetRate = numeral(lvNetRate).format('$0,0')

                var viNetRate = 0
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0 && landValue && landValue > 0) {
                    viNetRate = Math.round((capitalValue - landValue)/totalFloorArea)
                }
                compToRender.viNetRate = numeral(viNetRate).format('$0,0')

                compToRender.netPrice = numeral(sale.price ? sale.price.net : 0).format('$0,0')
                compToRender.grossPrice = numeral(sale.price ? sale.price.gross : 0).format('$0,0')
                compToRender.rawGrossPrice = sale.price ? sale.price.gross : 0
                compToRender.chattels = numeral(sale.price ? sale.price.chattels : 0).format('$0,0')

                compToRender.analysedLandValue = numeral(saleAnalysis.totalAnalysedLandValue).format('$0,0')

                var nspCV = 0
                if (capitalValue && capitalValue > 0) {
                    nspCV = (Math.round((netPrice/capitalValue)*100))/100
                }
                compToRender.nspCV = nspCV

                compToRender.buildingNetRate = numeral(saleAnalysis.analysedMainBuilding ? saleAnalysis.analysedMainBuilding.pricePerSquareMeter : 0).format('$0,0')


                if(saleAnalysis.analysedMainUnitGrossRate > 0){
                compToRender.mainUnitGrossRate = numeral(saleAnalysis.analysedMainUnitGrossRate).format('$0,0');
                }
                else{
                    var grossRate = 0
                    if (netPrice > 0 && totalFloorArea > 0) {
                        grossRate = Math.round(netPrice/totalFloorArea)
                    }
                    compToRender.mainUnitGrossRate = numeral(grossRate).format('$0,0');
                }

                compToRender.status = sale.status ? sale.status.description : ''

                var data = currProperty
                compToRender.buildingAge = self.getRenderableValue(data.landUseData ? (data.landUseData.buildingAge ? data.landUseData.buildingAge.description : '') : '')
                compToRender.siteCoverage = self.getRenderableValue(data.landUseData ? data.landUseData.buildingSiteCover : '')
                compToRender.carParks = self.getRenderableValue(data.landUseData ? data.landUseData.carparks : '')
                compToRender.csi = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.classOfSurroundingImprovements ? data.massAppraisalData.classifications.classOfSurroundingImprovements.description : '') : '') : '')
                compToRender.houseType = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.houseType ? data.massAppraisalData.classifications.houseType.description : '') : '') : '')
                compToRender.houseTypeObj = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.houseType ? data.massAppraisalData.classifications.houseType : null) : null) : null)

                compToRender.landscaping = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.landscapingQuality ? data.massAppraisalData.classifications.landscapingQuality.description : '') : '') : '')
                compToRender.deck = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasDeck ? 'Yes' : 'No') : '')
                compToRender.foundation = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasPoorFoundations ? 'Yes' : 'No') : '')
                compToRender.laundryWorkshop = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasLaundry ? 'Yes' : 'No') : '')
                compToRender.carAccess = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasCarAccess ? 'Yes' : 'No') : '')
                compToRender.driveway = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasDriveway ? 'Yes' : 'No') : '')
                compToRender.outlier = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.isOutlier ? 'Yes' : 'No') : '')
                compToRender.effectiveYearBuilt = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.effectiveYearBuilt : '')
                compToRender.landUse = self.getRenderableValue(data.landUseData ? (data.landUseData.landUse ? data.landUseData.landUse.description : '') : '')
                compToRender.units = self.getRenderableValue(data.landUseData ? data.landUseData.units : '')
                compToRender.bedrooms = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.bedrooms : '')
                compToRender.toilets = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.toilets : '')
                compToRender.wallConstructionAndCondition = data.landUseData ? (data.landUseData.wallConstruction ? data.landUseData.wallConstruction.description : '') : ''
                compToRender.wallConstruction = data.landUseData ? (data.landUseData.wallConstruction ? data.landUseData.wallConstruction : null) : null;
                compToRender.wallConstructionAndCondition += data.landUseData ? (data.landUseData.wallCondition ? ' '+data.landUseData.wallCondition.description : '') : ''
                compToRender.wallConstructionAndCondition = self.getRenderableValue(compToRender.wallConstructionAndCondition)
                compToRender.roofConstruction = data.landUseData ? (data.landUseData.roofConstruction ? data.landUseData.roofConstruction : null) : null;
                compToRender.roofConstructionAndCondition = data.landUseData ? (data.landUseData.roofConstruction ? data.landUseData.roofConstruction.description : '') : ''
                compToRender.roofConstructionAndCondition += data.landUseData ? (data.landUseData.roofCondition ? ' '+data.landUseData.roofCondition.description : '') : ''
                compToRender.roofConstructionAndCondition = self.getRenderableValue(compToRender.roofConstructionAndCondition)
                compToRender.underMainRoofGarages = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.underMainRoofGarages : '')
                compToRender.freeStandingGarages = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.freestandingGarages : '')
                compToRender.otherLargeImprovements = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasLargeOtherImprovements ? 'Yes' : 'No') : 'No')
                compToRender.modernisation = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.isModernised ? 'Yes' : 'No') : 'No')
                compToRender.zone = self.getRenderableValue(data.landUseData ? data.landUseData.landZone : '')
                compToRender.lotPosition = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.lotPosition ? data.massAppraisalData.classifications.lotPosition.description : '') : '') : '')
                compToRender.contour = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.contour ? data.massAppraisalData.classifications.contour.description : '') : '') : '')
                var viewDescription = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.view ? (data.massAppraisalData.classifications.view.description+' ') : '') : '') : '')
                var viewCode = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.view ? (data.massAppraisalData.classifications.view.code+' ') : '') : '') : '')
                if(viewCode.trim() == 'N'){
                    compToRender.viewScope = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.viewScope ? data.massAppraisalData.classifications.viewScope.description : '') : '') : '')
                }else{
                    compToRender.viewScope = ((viewDescription && viewDescription != '-') ? viewDescription.substring(viewDescription.trim().lastIndexOf(" ")+1) : '') + self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.viewScope ? data.massAppraisalData.classifications.viewScope.description : '') : '') : '')
                }

                compToRender.production = self.getRenderableValue(data.landUseData ? data.landUseData.production : '')
                compToRender.maoriLand = data.landUseData ? (data.landUseData.isMaoriLand ? 'Yes' : 'No') : 'No'

                if (updatePhotos) {
                    var propertyPhotos = self.getPropertyPhotos(photoURLs, compToRender.propertyId, compToRender.qupid)
                    compToRender.propertyPhotos = propertyPhotos.list
                    compToRender.primaryPhoto = propertyPhotos.primary
                }
                else {
                    self.refreshPhotos = true
                }
                return compToRender
            },


            getComparableSale: function(compSaleCriteria) {
                let self = this;
                $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ComparableSales.getComparableSaleBySaleId().url,
                        data: JSON.stringify(compSaleCriteria),
                        contentType: 'application/json',
                        cache: false,
                        success: function (response) {
                            if(response.length > 0){
                                var comparableSalesClone = JSON.parse(JSON.stringify(self.comparableSales));
                                self.compSalesClone = comparableSalesClone
                                self.comparableSales = []
                                //self.selectedComparableSales = []
                                self.selectedCompsCount = 0
                                self.$nextTick(function () {
                                    var compSale = self.generateCompSale(response[0], true)
                                    self.calculateQuality(compSale)
                                    compSale.isTempSelected = true
                                    compSale.isSelected = true
                                    compSale.showInTinderView = true
                                    compSale.otherFeatures = "";
                                    compSale.otherFeatures2 = "";
                                    compSale.otherFeaturesLabel = "";
                                    compSale.otherFeaturesLabel2 = "";
                                    compSale.comparableAddressEdited = "";
                                    compSale.added = true
                                    var comparableSalesClone = self.compSalesClone
                                    self.comparableSales.push(compSale)
                                    self.selectedComparableSales.push(compSale)
                                    for (var i = 0; i < comparableSalesClone.length; i++) {
                                        self.comparableSales.push(comparableSalesClone[i])
                                    }
                                    self.setActiveComp(0)
                                    self.loadExistingCompSales = true
                                    self.saveComparableProperties(false, true, false) //CHANGE middle to false to get rid of persist error
                                });
                            }
                            else {
                                self.warningHeader = 'No Sale Found'
                                self.warningMessage = 'Sale ' + self.saleId + ' does not exist'
                                $('.compSales').show();
                            }
                            self.fetchingSale = false
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            self.warningHeader = 'No Sale Found'
                            self.warningMessage = 'Sale ' + self.saleId + ' does not exist'
                            $('.compSales').show();
                            self.fetchingSale = false
                        }
                    });
            },

            fetchSaleById(){
                const self = this;
                if(self.homeValuationReportTypeCode.includes('VQV') && self.selectedCompsCount === 5){
                    self.warningMessage = 'Maximum of 5 Comparable Sales allowed for this report type. Please unselect one if you wish to add another comparable.';
                    self.warningHeader = "Error";
                    $('.compSales').show()
                    return;
                }
                if (!self.fetchingSale) {
                    self.fetchingSale = true
                    if (self.saleId.trim() != '') {
                        console.log('fetch sale id for: '+self.saleId);
                        var searchCriteria = {}
                        var qivsSaleIds = []
                        qivsSaleIds.push(self.saleId)
                        searchCriteria.qivsSaleIds = qivsSaleIds

                        var compSaleCriteria = {}
                        compSaleCriteria.saleSearchCriteria = searchCriteria
                        compSaleCriteria.homeValuationId = self.valuationJobId

                        var saleIndex = self.getSaleIndex(self.saleId)
                        if (saleIndex >= 0) {
                            self.comparableSales[saleIndex].isTempSelected = true
                            self.comparableSales[saleIndex].isSelected = true
                            if (!self.comparableSales[saleIndex].showInTinderView) {
                                self.comparableSales[saleIndex].showInTinderView = true
                                self.selectedComparableSales.push(self.comparableSales[saleIndex])
                                self.loadExistingCompSales = true
                                //self.selectedCompsCount++
                            }
                            self.setActiveComp(saleIndex)
                            self.fetchingSale = false
                        }
                        else {
                            self.getComparableSale(compSaleCriteria);
                        }
                    }
                    else {
                        self.fetchingSale = false
                    }
                }
            },

            keyPress: function(event) {
                const self = this
                if(event.charCode == 13) {
                    self.fetchSaleById();
                }
            },

            showSelectedComps: function(){
                const self = this
                if (self.currentState == 'closed') {
                    self.currentState = 'open'
                    for (var i = 0; i < self.comparableSales.length; i++) {
                        self.getPhotos(self.comparableSales[i].propertyId, self.comparableSales[i].id);
                    }
                }
                else {
                    self.currentState = 'closed'
                    for (var i = 0; i < self.comparableSales.length; i++) {
                        var currentComp = self.comparableSales[i]
                        if (currentComp.isSelected != currentComp.isTempSelected) {
                            currentComp.isSelected = currentComp.isTempSelected
                        }
                    }
                }
            },

            selectDefaultComps: async function() {
                let self = this;
                let qivsSaleIds = [];
                var existingSaleSummary = JSON.parse(JSON.stringify(self.saleSummary));
                let compSummaries = [];
                //Setup and select default 5 Best Match Comps with available information
                self.selectedCompSaleKeyList.forEach(function (key, i){
                    if(self.comparableSales[key] && self.comparableSales[key].isSelected){
                        qivsSaleIds.push(self.comparableSales[key].qivsSaleId);
                        self.comparableSales[key].isSelected = true
                        self.comparableSales[key].isTempSelected = true
                        self.selectedCompsCount++
                        let currComp = self.comparableSales[key]
                        let compSummary = {}
                        compSummary.id = currComp.id
                        compSummary.qivsSaleId = currComp.qivsSaleId
                        compSummary.qupid = currComp.qupid
                        compSummary.comparableDescription = currComp.summary
                        compSummary.distance = currComp.dist
                        compSummary.otherFeatures = currComp.otherFeatures
                        compSummary.otherFeatures2 = currComp.otherFeatures2
                        compSummary.otherFeaturesLabel = currComp.otherFeaturesLabel
                        compSummary.otherFeaturesLabel2 = currComp.otherFeaturesLabel2
                        compSummary.comparableAddressEdited = currComp.comparableAddressEdited
                        compSummary.selected = true
                        compSummary.saleDate = currComp.saleDate
                        compSummary.grossSalePrice = currComp.rawGrossPrice
                        compSummary.added = currComp.added == null ? false : currComp.added
                        compSummaries.push(compSummary);
                    }
                });
                //Build Search Criteria to get the summaries for the default Best match comps
                let searchCriteria = {}
                searchCriteria.qivsSaleIds = qivsSaleIds
                let compSaleCriteria = {}
                compSaleCriteria.saleSearchCriteria = searchCriteria
                compSaleCriteria.homeValuationId = self.valuationJobId

                const getCompSummaries = $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.ComparableSales.getComparableSaleBySaleId().url,
                    data: JSON.stringify(compSaleCriteria),
                    contentType: 'application/json',
                    cache: false,
                    success: function (response) {
                        if(response.length > 0) {
                            return response;
                        }
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });

                //Assign the default best match comps generated summaries
                await getCompSummaries.then(response => {
                    response.forEach(element => {
                        for(let comp of compSummaries){
                            if(element.sale.id === comp.id){
                                comp.comparableDescription = element.summary;
                                existingSaleSummary.push(comp);
                            }
                        }
                    });
                    self.saleSummary = existingSaleSummary;
                    self.saveComparableProperties(false, false, true);
                    self.defaultCompsSelected = true;
                });

                self.searchParams.max = null;
            },

            selectComp: function(e, key, persist) {
                const self = this
                var existingSaleSummary = JSON.parse(JSON.stringify(self.saleSummary))

                if (self.currentState == 'open') {
                    if (e.target.checked) {
                        self.comparableSales[key].isTempSelected = true
                        self.selectedCompsCount++
                    }
                    else {
                        self.comparableSales[key].isTempSelected = false
                        self.selectedCompsCount--
                    }
                }
                else {
                    if(e.target.checked && self.homeValuationReportTypeCode.includes('VQV') && self.selectedCompsCount === 5){
                        self.warningMessage = 'Maximum of 5 Comparable Sales allowed for this report type. Please unselect one if you wish to change the sale.';
                        self.warningHeader = "Error";
                        $('.compSales').show()
                        self.comparableSales[key].isSelected = false;
                        self.comparableSales[key].isTempSelected = false;
                        e.target.checked = false;
                    }
                    else if (e.target.checked) {
                        self.comparableSales[key].isSelected = true
                        self.comparableSales[key].isTempSelected = true
                        self.selectedCompsCount++
                    }
                    else {
                        self.comparableSales[key].isSelected = false
                        self.comparableSales[key].isTempSelected = false
                        self.selectedCompsCount--
                    }
                }
                if(self.comparableSales[key].isTempSelected) {
                    var currComp = self.comparableSales[key]
                    var compSummary = {}
                    compSummary.id = currComp.id
                    compSummary.qivsSaleId = currComp.qivsSaleId
                    compSummary.qupid = currComp.qupid
                    compSummary.comparableDescription = currComp.summary
                    compSummary.distance = currComp.dist
                    compSummary.otherFeatures = currComp.otherFeatures
                    compSummary.otherFeatures2 = currComp.otherFeatures2
                    compSummary.otherFeaturesLabel = currComp.otherFeaturesLabel
                    compSummary.otherFeaturesLabel2 = currComp.otherFeaturesLabel2
                    compSummary.comparableAddressEdited = currComp.comparableAddressEdited
                    compSummary.selected = true
                    compSummary.saleDate = currComp.saleDate
                    compSummary.grossSalePrice = currComp.rawGrossPrice
                    compSummary.added = currComp.added == null ? false : currComp.added
                    if (currComp.comparableQuality) {
                        var obj = {}
                        obj.code = currComp.comparableQuality
                        obj.description = obj.code=='C' ? 'Comparable' : (obj.code=='I' ? 'Inferior' : 'Superior')
                        compSummary.comparableQuality = obj
                    }
                    if(!compSummary.comparableDescription) {
                        self.generateSummaryForComps(compSummary, function() {
                            existingSaleSummary.push(compSummary);
                            self.saleSummary = existingSaleSummary;
                            self.saveComparableProperties(false, persist, true);
                        });
                    } else {
                        existingSaleSummary.push(compSummary);
                        self.saleSummary = existingSaleSummary;
                        self.saveComparableProperties(false, persist, true)
                    }
                } else {
                    self.saleSummary = existingSaleSummary.filter(function(obj) {
                        return obj.id !== self.comparableSales[key].id;
                    });
                    self.saveComparableProperties(false, persist, true)
                }
            },

            setActiveComp: function(key) {
                const self = this
                if (self.currentState == 'closed') {
                    self.currentCompSale = key
                }
                else {
                    self.comparableSales[key].isSelected = true
                }

            },

            loadNextCompSale: function(key) {
                const self = this
                self.saveComparableProperties(false, true, false)
                var allComps = self.comparableSales
                for (var i = key+1; i < allComps.length; i++) {
                    if (allComps[i] && allComps[i].showInTinderView) {
                        self.currentCompSale = i
                        self.setActiveComp(i)
                        setTimeout(function() {
                            self.getPhotos(self.comparableSales[i].propertyId, self.comparableSales[i].id)
                        }, 100);
                        break
                    }
                }
            },

            loadPrevCompSale: function(key) {
                const self = this
                self.saveComparableProperties(false, true, false)
                var allComps = self.comparableSales
                for (var i = key-1; i >= 0; i--) {
                    if (allComps[i] && allComps[i].showInTinderView) {
                        self.currentCompSale = i
                        self.setActiveComp(i)
                        setTimeout(function() {
                            self.getPhotos(self.comparableSales[i].propertyId, self.comparableSales[i].id)
                        }, 100);

                        break
                    }
                }
            },

            displayComparableProperties: function(updatePhotos) {
                let self = this;
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.ComparableSales.displayComparableProperties().url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(self.searchParams),
                    dataType: "json",
                    success: function (response) {
                        self.comparableSales = []
                        self.filteredCompsCount = response.length > 0 ? response[0].totalResults : 0
                        for (var i = 0; i < response.length; i++) {
                            var compSale = self.generateCompSale(response[i], updatePhotos)
                            if(compSale.isSelected && !self.selectedCompSaleKeyList.includes(i)){
                                self.selectedCompSaleKeyList.push(i);
                            }
                            self.calculateQuality(compSale)
                            $.each(self.saleSummary, function(i, obj) {
                                if(obj.id === compSale.id) {
                                    compSale.isSelected = true
                                    compSale.isTempSelected = true
                                    if(obj.comparableDescription) {
                                        compSale.summary = obj.comparableDescription
                                    }
                                    if(obj.otherFeatures) {
                                        compSale.otherFeatures = obj.otherFeatures;
                                    }
                                    if(obj.otherFeatures2) {
                                        compSale.otherFeatures2 = obj.otherFeatures2;
                                    }
                                    if(obj.otherFeaturesLabel) {
                                        compSale.otherFeaturesLabel = obj.otherFeaturesLabel;
                                    }
                                    if(obj.otherFeaturesLabel2) {
                                        compSale.otherFeaturesLabel2 = obj.otherFeaturesLabel2;
                                    }
                                    if(obj.comparableAddressEdited){
                                        compSale.comparableAddressEdited = obj.comparableAddressEdited;
                                    }
                                    if (obj.distance) {
                                        compSale.dist = obj.distance
                                        compSale.tDist = Number(obj.distance).toFixed(1)
                                    }
                                    if(obj.added == null) {
                                        compSale.added = false
                                    } else {
                                        compSale.added = obj.added
                                    }
                                    if (obj.comparableQuality && obj.comparableQuality.code) {
                                        compSale.comparableQuality = obj.comparableQuality.code;
                                    }
                                }
                            });
                            var compSalePrice = numeral(compSale.grossPrice).value()
                            if (!self.lowestCompSalePrice || compSalePrice < self.lowestCompSalePrice) {
                                self.lowestCompSalePrice = compSalePrice
                            }
                            if (!self.highestCompSalePrice || compSalePrice > self.highestCompSalePrice) {
                                self.highestCompSalePrice = compSalePrice
                            }
                            compSale.showInTinderView = false
                            compSale.showInSearchView = true
                            self.comparableSales.push(compSale)
                        }
                        self.$nextTick(function(){
                            self.selectedCompsCount = self.saleSummary.length
                        });
                        self.grossPriceSliderData.min=self.lowestCompSalePrice ? self.lowestCompSalePrice : 0
                        self.grossPriceSliderData.max=self.highestCompSalePrice ? self.highestCompSalePrice : 2000000
                        self.showSlider = true
                        self.goToCompsSearchView();
                        if (self.totalCompsCountFilterById == 0 && self.comparableSales.length > 0){
                            self.getCompsCount();
                        }
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            },

            goBack: function(){
                let self = this;
                self.searchParams.useBestMatch = false;
                self.loadCompSales(true);
            },

            loadCompSales: function(updatePhotos) {
                var self = this;
                if(self.searchParams.categories != null && self.searchParams.categories.trim().length == 0) {
                    self.searchParams.categories = null
                }
                if(self.searchParams.zones != null && self.searchParams.zones.trim().length == 0) {
                    self.searchParams.zones = null
                }
                self.displayComparableProperties(updatePhotos);

            },

            //REMEMBER TO REMOVE SEARCH PARAM MAX FOR ONE OF HE GET COMPS COUTN
            getCompsCount: function() {
                var self = this;
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.ComparableSales.displayComparableProperties().url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify({ useBestMatch: false, homeValuationId: self.searchParams.homeValuationId, max: 1, offset: 0}),
                    dataType: "json",
                    success: function (response) {
                        self.totalCompsCountFilterById =  response.length > 0 ? response[0].totalResults : 0;;
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            },
            refreshComps: function(){
                const self = this
                var saleIds = []
                for (var i = 0; i < self.saleSummary.length; i++) {
                    saleIds.push(self.saleSummary[i].qivsSaleId)
                }
                if (saleIds && saleIds.length > 0) {
                    var searchCriteria = {}
                    searchCriteria.qivsSaleIds = saleIds
                    searchCriteria.max = saleIds.length
                    searchCriteria.sort = ["NET_PRICE"]
                    searchCriteria.order = "ASC"

                    var compSaleCriteria = {}
                    compSaleCriteria.saleSearchCriteria = searchCriteria
                    compSaleCriteria.homeValuationId = self.valuationJobId

                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ComparableSales.getComparableSaleBySaleId().url,
                        data: JSON.stringify(compSaleCriteria),
                        contentType: 'application/json',
                        cache: false,
                        success: function (response) {
                            if (response.length > 0) {
                                var compSales = []
                                for (var i = 0; i < response.length; i++) {
                                    var activeCompSet = false
                                    var compSale = self.generateCompSale(response[i], true)
                                    var saleSummary = self.saleSummary.filter(function(obj) { return obj.id === compSale.id; })
                                    if(saleSummary.length > 0) {
                                        var obj = saleSummary[0]
                                        compSale.isSelected = true
                                        compSale.isTempSelected = true
                                        compSale.showInSearchView = false
                                        compSale.showInTinderView = true
                                        if(obj.comparableDescription) {
                                            compSale.summary = obj.comparableDescription
                                        }
                                        if(obj.otherFeatures) {
                                            compSale.otherFeatures = obj.otherFeatures;
                                        }
                                        if(obj.otherFeatures2) {
                                            compSale.otherFeatures2 = obj.otherFeatures2;
                                        }
                                        if(obj.otherFeaturesLabel) {
                                            compSale.otherFeaturesLabel = obj.otherFeaturesLabel;
                                        }
                                        if(obj.otherFeaturesLabel2) {
                                            compSale.otherFeaturesLabel2 = obj.otherFeaturesLabel2;
                                        }
                                        if(obj.comparableAddressEdited){
                                            compSale.comparableAddressEdited = obj.comparableAddressEdited;
                                        }
                                        if (obj.distance) {
                                            compSale.dist = obj.distance
                                            compSale.tDist = Number(obj.distance).toFixed(1)
                                        }
                                        if(obj.added == null) {
                                            compSale.added = false
                                        } else {
                                            compSale.added = obj.added
                                        }
                                        if (obj.comparableQuality && obj.comparableQuality.code) {
                                            compSale.comparableQuality = obj.comparableQuality.code;
                                        }
                                        else {
                                            self.calculateQuality(compSale)
                                        }
                                    }
                                    if (!activeCompSet){
                                        self.setActiveComp(0)
                                        self.getPhotos(compSale.propertyId,compSale.id)
                                        activeCompSet = true
                                    }
                                    compSales.push(compSale)
                                }


                                self.$nextTick(function(){
                                    self.comparableSales = compSales
                                    self.selectedComps = JSON.parse(JSON.stringify(compSales))
                                    self.selectedComparableSales = self.selectedComps
                                    self.refreshPhotos = true
                                    self.selectedCompsCount = self.selectedComps.length
                                    self.showCompsSearchView = false
                                    self.showCompsTinderView = true
                                    self.saveComparableProperties(false, true, false)
                                })
                            }
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            console.log('Error while refreshing comparable sales => ' + response)
                        }
                    });
                } else {
                    self.showCompsSearchView = false
                    self.showCompsTinderView = true
                }
            },
            getFormattedDateExport : function () {

                var currDate = new Date(Date.now());
                var curr_date = currDate.getDate();
                var curr_month = currDate.getMonth() + 1; //Months are zero based
                var curr_year = currDate.getFullYear();
                currDate = curr_year+''+curr_month+''+curr_date;
                return currDate;
            },
            exportComps: function(){
                const self = this
                self.exportingComps = true

                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.ComparableSales.exportComparableSale(self.valuationJobId).url,
                    cache: false,
                    success: function (response) {
                        if (response && response.sales && response.sales.length > 0) {
                            var fields = Object.keys(response.sales[0]);
                            var fieldNames = ['Region', 'TA', 'SG No.', 'Sale Group', 'Qpid', 'Roll', 'Assmt', 'Suf', 'St No', 'Add No', 'Street', 'Suburb', 'Town', 'Cat', 'Sale Date', 'Gross SP', 'Net Sale $', 'Chatts', 'Other', 'GST', 'Analysis Type', 'ALSP', 'ALSP/m2', 'ALSP/ha', 'AOI SP', 'AOB SP', 'AMB SP', 'OB LOI $', 'Build NR', 'Rent Wk', 'Rent An', 'NSP Yield', 'GSP Yield', 'SA Comment', 'Est Date', 'RTV', 'Land RTV', 'Rent Est', 'Rent Basis', 'Type', 'Ten', 'PVR', 'Sale Status', 'Sale Reval Date', 'Sale CV', 'Sale LV', 'Sale VI', 'Reval Date', 'CV', 'LV', 'VI', 'RCV', 'RLV', 'RVI', 'NSP/SCV', 'NSP/RCV', 'RCV/CV', 'RLV/LV', 'BM', 'CV/TFA', 'LV/m2', 'LV/ha', 'VI/TFA', 'NOI', 'Land Area', 'M Land', 'Production', 'Zone', 'Use', 'LU Description', 'Units', 'Beds', 'WCs', 'Car', 'Age', 'Wall', 'Wall Cond', 'Roof', 'Roof Cond', 'Site', 'TFA', 'CSI', 'Lot', 'Cont', 'Lndscp', 'View', 'Scp', 'HT', 'Mod', 'EYB', 'Main', 'TLA', 'Fdns', 'Deck', 'Ldy', 'Ois', 'Acc', 'Drv', 'UMR', 'FS', 'Out', 'Legal Description', 'Vendor/Purchaser', 'Comments', 'Property Description', 'Occupier1 Name', 'Occupier2 Name', 'Occupier1 Street', 'Occupier1 Town', 'Occupier1 Postcode', 'Owner1 Name', 'Owner1 Street', 'Owner1 Town', 'Owner1 Postcode', 'Worksheet', 'plan_number', 'Photos', 'Tenure', 'Ownership', 'Rateability', 'Apportionment', 'Extens'];
                            var opts = {
                                data: response.sales,
                                fields: fields,
                                fieldNames: fieldNames,
                                quotes: '"'
                            };
                            var csv = json2csv(opts);
                            try {
                                var fileName = "Comparable_Sales_" + self.getFormattedDateExport();
                                //download(csv, fileName+".csv", "text/plain");

                                var blobData = new Blob([csv], {type: "text/plain;charset=utf-8"});
                                fileSaver.saveAs(blobData, fileName + ".csv");
                            }
                            catch (err) {
                                console.log("Download failed!");
                                console.log("Exception: ", err);
                                self.warningHeader = "Error";
                                self.warningMessage = "Exception: " + response.toString();
                                $('.compSales').show();
                            }
                        }
                        else {
                            self.warningHeader = "Error";
                            self.warningMessage = "Exception: " + response.toString();
                            $('.compSales').show();
                        }
                        self.exportingComps = false
                    },
                    error: function (response) {
                        self.errorHandler(response);
                        self.warningHeader = "Error";
                        self.warningMessage = "Exception: " + response.toString();
                        $('.compSales').show();
                        self.exportingComps = false
                    }
                });
            },
            jsonEqual: function jsonEqual(a,b) {
                return deepEqual(a,b);
            },
            openGoogleSearchTab(address1, address2) {
                openUrlInNewTab(`https://google.co.nz/search?near=New+Zealand&q=${address1}+${address2}`);
            },
        },
        created: function() {
            const self = this

            $('.daterangepicker').hide();

            EventBus.$on('comparableSalesDateRange', function(obj){
                var from = moment(obj.from, 'YYYY-MM-DD')
                var to = moment(obj.to, 'YYYY-MM-DD')
                self.searchParams.saleDateFrom = from
                self.searchParams.to = to
                self.searchParams.useBestMatch = false;
                self.searchParams.offset = 0;
                self.loadCompSales(true)
            })

            EventBus.$on('grossPriceDualSlider', function(obj){
                var from = obj.from
                var to = obj.to
                self.searchParams.grossPriceFrom = from
                self.searchParams.grossPriceTo = to
                self.searchParams.useBestMatch = false;
                self.searchParams.offset = 0;
                self.loadCompSales(true)
            })

            EventBus.$on('propertyDistanceSlider', function(obj){
                var to = obj.from
                self.searchParams.maxDistance = to
                self.searchParams.useBestMatch = false;
                self.searchParams.offset = 0;
                self.loadCompSales(true)
            })

            EventBus.$on('enable-buttons', function(){
                self.isDataSaving = false
            })

            EventBus.$on('home-valuation-saved', function(obj) {
                if(!obj.homeValuation.reportType.code.includes('VQV')){
                    self.searchParams.useBestMatch = false;
                }
                else {
                    self.searchParams.useBestMatch = true;
                }
                var homeValuation = obj.homeValuation;
                self.newSaleAdded = false;
                self.loadExistingCompSales = false;
                self.updatePhotos = false;
                self.searchParams.homeValuationId = homeValuation.id;
                if (self.valuationJobId != homeValuation.id) {
                    self.comparableSales = [];
                    self.selectedCompSaleKeyList = [];
                    self.currentState = 'closed';
                    self.currentCompSale = 0;
                    self.selectedCompKeys = [];
                    self.saleId = '';
                    self.loadCounter = 0;
                    self.saleSummary = homeValuation.comparableSales ? homeValuation.comparableSales : [];
                    self.selectedCompsCount = self.saleSummary.length;
                    self.searchParams.useBestMatch = false;
                    self.loadCompSales(true, true);
                    self.updatePhotos = true;
                    self.getCompsCount();
                    self.initialLoadComplete = false;
                }
                if(obj.homeValuation && obj.homeValuation.reportType && obj.homeValuation.reportType.code.includes('VQV') && self.initialLoadComplete === false){
                    self.comparableSales = [];
                    self.selectedCompSaleKeyList = [];
                    self.currentState = 'closed';
                    self.currentCompSale = 0;
                    self.selectedCompKeys = [];
                    self.saleId = '';
                    self.loadCounter = 0;
                    self.saleSummary = homeValuation.comparableSales ? homeValuation.comparableSales : [];
                    self.selectedCompsCount = self.saleSummary.length;
                    if(homeValuation.comparableSales && homeValuation.comparableSales.length > 0){
                        self.defaultCompsSelected = true;
                    }
                    self.searchParams.useBestMatch = true;
                    self.homeValuationReportTypeCode = obj.homeValuation.reportType.code;
                    self.searchParams.maxDistance = null;
                    self.searchParams.saleDateFrom = null;
                    self.searchParams.saleDateTo = null;

                    self.getCompsCount();
                    self.searchParams.max = 1000;
                    self.loadCompSales(true);
                    self.updatePhotos = true;
                    self.initialLoadComplete = true;
                    self.searchParams.useBestMatch = false;
                }
                self.saleSummaryCopy = JSON.parse(JSON.stringify(homeValuation.comparableSales));
                self.valuationJobId = homeValuation.id;
                self.isDataSaving = false;
                self.jobStatus = homeValuation.status.code;

                if (self.defaultPropertyData && self.defaultPropertyData.category) {
                    if (self.defaultPropertyData.category.code.startsWith('R') || self.defaultPropertyData.category.code.startsWith('C') ||
                            self.defaultPropertyData.category.code.startsWith('I') || self.defaultPropertyData.category.code.startsWith('O') ||
                            self.defaultPropertyData.category.code.startsWith('U')) {
                        self.propertyDistanceSliderData.max = 3;
                    } else if (self.defaultPropertyData.category.code.startsWith('L') || self.defaultPropertyData.category.code.startsWith('A') ||
                            self.defaultPropertyData.category.code.startsWith('D') || self.defaultPropertyData.category.code.startsWith('F') ||
                            self.defaultPropertyData.category.code.startsWith('H') || self.defaultPropertyData.category.code.startsWith('M') ||
                            self.defaultPropertyData.category.code.startsWith('P') || self.defaultPropertyData.category.code.startsWith('S')) {
                        self.propertyDistanceSliderData.max = 30;
                        self.searchParams.categories = 'L*';
                    }
                }
            });

            EventBus.$on('home-valuation-new', function(propertyData) {
                self.comparableSales = [];
                self.currentState = 'closed';
                self.currentCompSale = 0;
                self.selectedCompsCount = 0;
                self.selectedCompKeys = [];
                self.saleId = '';
                self.isNewValuation = true;
                self.saleSummary = [];
                self.defaultPropertyData = propertyData;
                self.grossPriceSliderData = {};
                self.grossPriceSliderData.start = [0,0];
                self.grossPriceSliderData.connect = true;
                self.grossPriceSliderData.step = 1000;
                self.grossPriceSliderData.min = 0;
                self.grossPriceSliderData.max = 2000000;
                self.defaultCompsSelected = false;
                self.propertyDistanceSliderData = {};
                self.propertyDistanceSliderData.start = [0.5];
                self.propertyDistanceSliderData.connect = [true, false];
                self.propertyDistanceSliderData.step = 0.5;
                self.propertyDistanceSliderData.min = 0;
                self.propertyDistanceSliderData.max = 10;
                self.initialLoadComplete = false;
                var start = moment().subtract(3, 'month').startOf('month');
                var end = moment();

                self.searchParams.saleDateFrom = moment(start);
                self.searchParams.saleDateTo = moment(end);
                self.searchParams.categories = 'R*';
                self.searchParams.zones = null;

                self.showCompsSearchView = true;
                self.showCompsTinderView = false;

                self.showSlider = false;
            });

            EventBus.$on('load-default-property-data', function(propertyData) {
                self.defaultPropertyData = propertyData.property
            });


        },
        updated: function() {
            const self = this
            if (self.loadExistingCompSales) {
                self.saveAllCompSales = false
                self.selectedCompsCount = 0
                for (var i = 0; i < self.comparableSales.length; i++) {
                    if (self.comparableSales[i].isTempSelected) {
                        self.selectedCompsCount++;
                    }
                }
                self.loadExistingCompSales = false
                self.saveAllCompSales = true
            }

            if (self.showCompsTinderView) {
                for (var i = 0; i < self.comparableSales.length; i++) {
                    if (self.comparableSales[i].isTempSelected) {
                        self.registerPhotoUploaderHandler(self.comparableSales[i].id);
                        self.registerCarousel(self.comparableSales[i].id);
                        self.registerPhotoClickHandler(self.comparableSales[i].id);
                    }
                }
            }
            else {
                if (self.valuationJobId && self.loadCounter < 10 && self.totalCompsCountFilterById == 0){
                    self.loadCounter++;
                    setTimeout(function() {
                        self.getCompsCount()
                        if(self.selectedCompSaleKeyList.length === 0){
                            self.loadCompSales(true)
                        }
                    }, 1000);
                }
            }

            $('.daterangepicker').hide();

            $('.resultsRow').off("click").click(function(evt) {
                if ($(evt.target).closest('.closer').length == 0) {
                    $(this).addClass('openProp mdl-shadow--2dp');
                }
            });

            $('.closer').off("click").click(function(evt){
                $(evt.target).closest('.resultsRow').removeClass('openProp mdl-shadow--2dp');
            });

            $('#compsDesktop').find($('a')).off("click").click(function () {
                var liParent = $(this).parent();
                var allOptions = $('#compsDesktop').find('.sortRow').find('.colHeader');
                var allOptionsMobile = $('#mobile').find('.sortRow').find('.colHeader');

                if (liParent.hasClass('active')) {
                    if ($(this).find('i').hasClass('up')){
                        $(this).find('i').addClass('down');
                        $(this).find('i').removeClass('up');
                    }
                    else {
                        $(this).find('i').addClass('up');
                        $(this).find('i').removeClass('down');
                    }
                } else {
                    allOptions.removeClass('active');
                    allOptions.find('i').removeClass('up');
                    allOptions.find('i').addClass('down');
                    liParent.addClass('active');
                }

                var sort = liParent.data('sort');
                var sortParams = [sort]
                if(sort == 'VAL_REF') {
                    sortParams = ['ROLL_NUMBER', 'ASSESSMENT_NUMBER', 'ASSESSMENT_NUMBER_SUFFIX']
                } else if(sort == 'FULL_ADDRESS') {
                    sortParams = ['TA_CODE', 'STREET_NAME', 'STREET_TYPE', 'STREET_NUMBER', 'STREET_NUMBER_SUFFIX']
                }
                self.searchParams.sort = sortParams

                var order = $(this).find('i').hasClass('up');
                if (order == true) {
                    order = "DESC";
                } else {
                    order = "ASC";
                }
                self.searchParams.order = order
                self.searchParams.offset = 0
                self.searchParams.useBestMatch = false;
                if(self.isDirty && sort == 'SELECTED') {
                    self.saveComparableProperties(false, true, true, self.loadCompSales(true));
                } else {
                    self.loadCompSales(true);
                }
            });

            $(window).data('ajaxready', true).scroll(function(e) {
                var  scroll = $(window).scrollTop(),
                     winHeight = $(window).height();
                if ($(window).data('ajaxready') == false) return;
                if(Math.ceil(scroll + winHeight) >= ($(document).height()-130)) {
                    $(window).data('ajaxready', false);
                    if($('.compsRow:visible').length >= 25) {
                        $('.loadingSpinnerComps').show();
                        if($('.compsRow').length > 1) {
                            self.searchParams.offset = self.comparableSales.length;
                            $.ajax({
                                type: "POST",
                                url: jsRoutes.controllers.ComparableSales.displayComparableProperties().url,
                                cache: false,
                                contentType: "application/json; charset=utf-8",
                                data: JSON.stringify(self.searchParams),
                                dataType: "json",
                                success: function (response) {
                                    $('.loadingSpinnerComps').hide()
                                    self.filteredCompsCount = response.length > 0 ? response[0].totalResults : 0
                                    for (var i = 0; i < response.length; i++) {
                                        var compSale = self.generateCompSale(response[i], true)
                                        $.each(self.saleSummary, function(i, obj) {
                                            if(obj.id === compSale.id) {
                                                compSale.isSelected = true
                                                compSale.isTempSelected = true
                                                if(obj.comparableDescription) {
                                                    compSale.summary = obj.comparableDescription
                                                }
                                                if(obj.otherFeatures) {
                                                    compSale.otherFeatures = obj.otherFeatures;
                                                }
                                                if(obj.otherFeatures2) {
                                                    compSale.otherFeatures2 = obj.otherFeatures2;
                                                }
                                                if(obj.otherFeaturesLabel) {
                                                    compSale.otherFeaturesLabel = obj.otherFeaturesLabel;
                                                }
                                                if(obj.otherFeaturesLabel2) {
                                                    compSale.otherFeaturesLabel2 = obj.otherFeaturesLabel2;
                                                }
                                                if(obj.comparableAddressEdited) {
                                                    compSale.comparableAddressEdited = obj.comparableAddressEdited;
                                                }
                                                if (obj.distance) {
                                                    compSale.dist = obj.distance
                                                    compSale.tDist = Number(obj.distance).toFixed(1)
                                                }
                                                if(obj.added == null) {
                                                    compSale.added = false
                                                } else {
                                                    compSale.added = obj.added
                                                }
                                                if (obj.comparableQuality && obj.comparableQuality.code) {
                                                    compSale.comparableQuality = obj.comparableQuality.code;
                                                }
                                                else {
                                                    self.calculateQuality(compSale)
                                                }
                                            }
                                        });
                                        self.selectedCompsCount = self.saleSummary.length
                                        var compSalePrice = numeral(compSale.grossPrice).value()
                                        if (!self.lowestCompSalePrice || compSalePrice < self.lowestCompSalePrice) {
                                            self.lowestCompSalePrice = compSalePrice
                                        }
                                        if (!self.highestCompSalePrice || compSalePrice > self.highestCompSalePrice) {
                                            self.highestCompSalePrice = compSalePrice
                                        }
                                        compSale.showInTinderView = false
                                        compSale.showInSearchView = true
                                        self.comparableSales.push(compSale)
                                    }
                                    $(window).data('ajaxready', true);
                                },
                                error: function (response) {
                                    self.errorHandler(response);
                                }
                            });
                        }
                    }
                }
            });
        },
        destroyed: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            $('.resultsRow').off("click")
            $('.closer').off("click")
            $('.resultsRow').removeClass('openProp mdl-shadow--2dp');
            $('#compsDesktop').find($('a')).off("click")
        },
        beforeDestroy: function() {
            EventBus.$off('home-valuation-new', this.listener);
            EventBus.$off('home-valuation-saved', this.listener);
            EventBus.$off('enable-buttons', this.listener);
            $('.resultsRow').off("click")
            $('.closer').off("click")
            $('.resultsRow').removeClass('openProp mdl-shadow--2dp');
            $('#compsDesktop').find($('a')).off("click")
        }
    }
</script>

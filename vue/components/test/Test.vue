<template>
    <div class="contentWrapper resultsWrapper">

        <p>
            <button @click="openAlertModal">Open Alert</button>
            <button @click="openConfirmModal">Open Confirm</button>

        </p>

        <validation-header-message :validationSet="validationSet" />

        <p>
            <label>
                <span class="label">Property Name</span>
                <input
                    type="text"
                    id="propertyName"
                    v-model="propertyName"
                    :class="errorClasses('propertyName')"
                />
                <validation-message
                    :validationSet="validationSet"
                    field="propertyName"
                />
            </label>
        </p>
        <p>
            <label>
                <span class="label">Property Value</span>
                <input
                    type="text"
                    id="propertyValue"
                    v-model="propertyValue"
                    :class="errorClasses('propertyValue')"
                />
                <validation-message
                    :validationSet="validationSet"
                    field="propertyValue"
                />
            </label>
        </p>
        <p>
            <button @click="validateForm">Validate</button>
        </p>


        <alert-modal warning v-show="confirmModalIsOpen" @close="close">
            <h1>Are you sure?</h1>
            <p>We're about to do absolutely nothing. This cannot be undone.</p>
            <template #buttons>
                <modal-button id="cancel" @click="cancel">Cancel</modal-button>
                <modal-button id="ok" @click="okay" primary>Okay</modal-button>
            </template>
        </alert-modal>

        <alert-modal warning v-show="alertModalIsOpen" @close="close">
            <h1>Contents</h1>
            <p>Things go here</p>
        </alert-modal>
    </div>
</template>

<script>
export default {
    components: {
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../common/modal/AlertModal.vue'),
        'modal-button': () => import(/* webpackChunkName: "ModalButton" */ '../common/modal/ModalButton.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../common/form/ValidationMessage.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "ValidationHeaderMessage" */ '../common/form/ValidationHeaderMessage.vue'),
    },
    data() {
        return {
            alertModalIsOpen: false,
            confirmModalIsOpen: false,
            propertyName: '',
            propertyValue: '',
            validationSet: undefined,
        };
    },
    computed: {
        errors() { return (this.validationSet && this.validationSet.errors) || []; }
    },
    methods: {
        errorClasses(field) {
            return { 'error': this.errors.find(this.findError(field)) };
        },
        findError(fieldName) {
            return (err) => err.field === fieldName;
        },
        openAlertModal() {
            this.alertModalIsOpen = true;
        },
        openConfirmModal() {
            this.confirmModalIsOpen = true;
        },
        close() {
            this.alertModalIsOpen = false;
            this.confirmModalIsOpen = false;
        },
        cancel() {
            console.log('cancel');
            this.close();
        },
        okay() {
            console.log('okay');
            this.close();
        },
        validateForm() {
            console.log('validate', this.propertyName);
            const invalid = {
                value: null,
                errors: [
                    {
                        field: 'propertyName',
                        message: 'Required',
                    },
                    {
                        field: 'propertyValue',
                        message: 'Required',
                    },
                ],
                success: false,
            };
            const valid = {
                success: true,
            };
            this.validationSet = this.propertyName.length > 0 ? valid : invalid;
        },
    },
}
</script>

<style lang="scss" scoped>
    input[type='text'] {
        border: 1px solid #000;

        &.error {
            border: 1px solid #f00;
        }
    }
</style>

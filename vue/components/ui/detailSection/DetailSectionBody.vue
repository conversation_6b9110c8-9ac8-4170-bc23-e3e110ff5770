<script setup>
import { inject } from 'vue';

import { DETAIL_SECTION_CONTEXT } from '@/components/ui/detailSection/index';

const {
    state,
} = inject(DETAIL_SECTION_CONTEXT);
</script>

<template>
    <div :class="['qv-detail-section-body', state.variant.body || '']" data-cy="detail-section-body">
        <div v-show="state.isExpanded" data-cy="detail-section-content">
            <slot />
        </div>
    </div>
</template>

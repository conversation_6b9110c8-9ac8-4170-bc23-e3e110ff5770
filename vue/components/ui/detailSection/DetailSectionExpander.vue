<script setup>
import { inject } from 'vue';

import { DETAIL_SECTION_CONTEXT } from '@/components/ui/detailSection/index';
import MaterialIcon from '@/components/common/MaterialIcon.vue';

const {
    state,
    expand,
    collapse,
} = inject(DETAIL_SECTION_CONTEXT)

function toggle() {
    if (state.isExpanded) {
        collapse();
    } else {
        expand();
    }
}
</script>

<template>
    <div @click="toggle" class="qv-flex-row qv-align-center qv-justify-center" data-cy="detail-section-expander" :title="state.isExpanded ? 'Collapse' : 'Expand'">
        <MaterialIcon icon="" :class="['qv-text-lg qv-font-lighter qv-flip', state.variant.title, state.isExpanded ? 'qv-flipped' : '']" />
    </div>
</template>

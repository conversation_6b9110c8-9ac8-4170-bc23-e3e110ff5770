<script setup>
import { provide, reactive } from 'vue';
import { DETAIL_SECTION_CONTEXT, DETAIL_SECTION_VARIANTS, validateDetailSectionVariant } from '@/components/ui/detailSection/index';

const emits = defineEmits(['onCollapse', 'onExpand']);

const props = defineProps({
    defaultExpanded: {
        type: Boolean,
        default: true,
    },
    variant: {
        type: String,
        default: 'default',
        validator: validateDetailSectionVariant,
    },
});

const state = reactive({
    isExpanded: props.defaultExpanded,
    variant: DETAIL_SECTION_VARIANTS[props.variant],
});

function collapse() {
    state.isExpanded = false;
    emits('onCollapse');
}

function expand() {
    state.isExpanded = true;
    emits('onExpand');
}

provide(DETAIL_SECTION_CONTEXT,
    {
        state,
        collapse,
        expand,
    });

defineExpose(state);
</script>

<template>
    <div class="qv-w-full qv-flex-column">
        <slot />
    </div>
</template>

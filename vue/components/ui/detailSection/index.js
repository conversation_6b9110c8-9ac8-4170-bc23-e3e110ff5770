export { default as DetailSection } from './DetailSection.vue';
export { default as DetailSectionHeader } from './DetailSectionHeader.vue';
export { default as DetailSectionBody } from './DetailSectionBody.vue';
export { default as DetailSectionTitle } from './DetailSectionTitle.vue';
export { default as DetailSectionExpander } from './DetailSectionExpander.vue';

export const DETAIL_SECTION_CONTEXT = Symbol("DetailSectionContext");

/**
 * Enum for detail section styles
 * @enum {String}
 */
export const DETAIL_SECTION_VARIANTS = Object.freeze({
    default: {
        background: 'monarch-bg-dullblue',
        title: 'qv-color-light'
    },
})

export function validateDetailSectionVariant(value) {
    if (!Object.keys(DETAIL_SECTION_VARIANTS).includes(value)) {
        throw new Error(`Detail variant "${value}" is not valid. Valid variants are: ${Object.keys(DETAIL_SECTION_VARIANTS).join(', ')}`);
    }

    return true;
}

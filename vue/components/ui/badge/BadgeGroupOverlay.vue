<script setup>
const props = defineProps({
    alignment: {
        type: String,
        default: 'right',
        validator: (value) => {
            return ['left', 'right'].includes(value);
        },
    },
})
</script>

<template>
    <div class="qv-position-relative">
        <span :class="['qv-badge-group-overlay', alignment]">
            <slot name="badges"/>
        </span>
        <slot/>
    </div>
</template>

<style scoped lang="scss">

</style>

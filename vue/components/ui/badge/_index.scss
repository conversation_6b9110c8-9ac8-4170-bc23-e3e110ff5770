@use "../../../qv-colors" as *;

$badge-radius: 15px;
$badge-font-size: 10px;

.qv-badge {
  width: $badge-radius;
  height: $badge-radius;
  border-radius: 50%;
  text-align: center;
  vertical-align: middle;
  font-size: $badge-font-size;
  line-height: normal;
  outline: 1px solid white;

  &-overlay {
    position: absolute;
    top: 0;
    right: 0;
    translate: 35% -35%;
  }
}

.qv-badge-group-overlay {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  top: 0;
  right: 0;
  min-width: $badge-radius * 2;
  gap: 2px;

  &.left {
    flex-direction: row-reverse;
    translate: -65% -45%;
  }

  &.right {
    flex-direction: row;
    translate: 65% -45%;
  }
}

.qv-badge-default, .qv-badge-info {
    background-color: $color-mediumblue;
    color: $color-light;
}

.qv-badge-error {
    background-color: $color-red;
    color: $color-light;
}

.qv-badge-warning {
    background-color: $color-orange;
    color: $color-light;
}

.qv-badge-success {
    background-color: $color-success;
    color: $color-light;
}
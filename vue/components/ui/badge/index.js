export { default as Badge } from './Badge.vue';
export { default as BadgeOverlay } from './BadgeOverlay.vue';
export { default as BadgeGroupOverlay } from './BadgeGroupOverlay.vue';

export const BADGE_VARIANTS = Object.freeze({
    default: 'qv-badge-default',
    info: 'qv-badge-info',
    error: 'qv-badge-error',
    warning: 'qv-badge-warning',
    success: 'qv-badge-success',
});


export function validateBadgeVariant(variant) {
    const valid = Object.keys(BADGE_VARIANTS).includes(variant);

    if (!valid) {
        console.error(`Invalid badge variant: ${variant}`, Object.keys(BADGE_VARIANTS));
    }

    return valid;
}

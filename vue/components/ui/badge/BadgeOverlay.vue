<script setup>
import { BADGE_VARIANTS, validateBadgeVariant } from '@/components/ui/badge/index';

const props = defineProps({
    variant: {
        type: String,
        default: 'default',
        validator: validateBadgeVariant,
    },
    value: {
        type: [String, Number],
        default: '',
    },
})
</script>

<template>
    <div class="qv-position-relative">
        <span :class="['qv-badge', 'qv-badge-overlay', BADGE_VARIANTS[variant]]">
            {{ value }}
        </span>
        <slot/>
    </div>
</template>

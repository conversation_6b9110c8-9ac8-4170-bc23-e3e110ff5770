<script setup>
import { computed, provide } from 'vue';
import { VALIDATION_CONTEXT } from '@/components/ui/validation/index';
import { ValidationSet } from '@quotable-value/validation';

const props = defineProps({
    validationSet: {
        type: ValidationSet,
        required: false,
    }
});

const validations = computed(() => props.validationSet || new ValidationSet());
provide(VALIDATION_CONTEXT, validations);
</script>

<template>
    <div>
        <slot></slot>
    </div>
</template>

<script setup>
import Tooltip from '@/components/common/Tooltip.vue';
import { computed, ref, useAttrs } from 'vue';
import { injectValidationContext } from '@/components/ui/validation/index';

const props = defineProps({
    errors: {
        type: Array,
        required: false,
        default: () => [],
    },
    warnings: {
        type: Array,
        required: false,
        default: () => [],
    },
    path: {
        type: String,
        required: false,
        default: '',
    },
    index: {
        required: false,
        default: null,
    },
    tag: {
        required: false,
        default: 'div',
    },
    hideWrapper: {
        type: Boolean,
        required: false,
        default: false,
    }
});
const validations = injectValidationContext();
const errors = computed(() => {

    if (props.path) {
        return validations.value?.getErrors(props.path, props.index) || [];
    }

    return props.errors || [];
});
const warnings = computed(() => {
    if (props.path) {
        return validations.value?.getWarnings(props.path, props.index) || [];
    }

    return props.warnings || [];
});
const hasErrors = computed(() => errors.value.length > 0);
const hasWarnings = computed(() => warnings.value.length > 0);
const hasErrorHint = computed(() => hasErrors && errors.value[0]?.hint);
const hasWarningHint = computed(() => hasWarnings && warnings.value[0]?.hint);

function classes(validationProps) {
    return {
        'qv-validation-error': validationProps.hasErrors && !props.hideWrapper,
        'qv-validation-warning': !validationProps.hasErrors && validationProps.hasWarnings && !props.hideWrapper,
    };
}

function formatMessage(validation) {
    if (!validation) {
        return '';
    }

    return validation.message.charAt(0).toUpperCase() + validation.message.slice(1);
}

const wrapperElement = ref();
const hasFocus = ref(false);
const hasHover = ref(false);

const showTooltip = computed(() => {
    return hasFocus.value || hasHover.value;
});
</script>

<template>
    <component :is="tag" ref="wrapperElement"
         :class="['qv-validation-wrapper', classes({ hasErrors, hasWarnings })]"
         @focusin="hasFocus = true"
         @focusout="hasFocus = false"
         @mouseleave="hasHover = false"
         @mouseover="hasHover = true"
    >
        <Tooltip v-if="hasErrors && !hideWrapper" :show="showTooltip" :text="formatMessage(errors[0])" display-mode="error" />
        <Tooltip v-else-if="hasWarnings && !hideWrapper" :show="showTooltip" :text="formatMessage(warnings[0])" display-mode="warning" />
        <slot ref="element" />
        <template v-if="hasErrorHint">
            <p class="qv-alert-error">
                {{ errors[0].hint }}
            </p>
        </template>
        <template v-if="hasWarningHint">
            <p class="qv-alert-warning">
                {{ warnings[0].hint }}
            </p>
        </template>
    </component>
</template>

<script setup>
import { injectValidationContext } from '@/components/ui/validation/index';
import { computed } from 'vue';

const props = defineProps({
    path: {
        type: String,
        required: false,
        default: '',
    },
    step: {
        type: String,
        required: false,
        default: '',
    }
});

if (!props.path && !props.step) {
    throw new Error('ValidationProvider requires either a path or a step prop');
}

const validationSet = injectValidationContext();
const errors = computed(() => {
    if (props.path && validationSet.value) {
        return validationSet.value.getErrorsAtPath(props.path);
    }

    if (props.step && validationSet.value) {
        return validationSet.value.getErrorsAtStep(props.step);
    }
})
const warnings = computed(() => {
    if (props.path && validationSet.value) {
        return validationSet.value.getWarningsAtPath(props.path);
    }

    if (props.step && validationSet.value) {
        return validationSet.value.getWarningsAtStep(props.step);
    }
})
const errorCount = computed(() => errors.value?.length);
const warningCount = computed(() => warnings.value?.length);
const hasErrors = computed(() => errorCount.value > 0);
const hasWarnings = computed(() => warningCount.value > 0);

function getErrorCountForIndex(index) {
    return errors.value?.filter(error => error.index === index).length;
}

function getErrorsForIndex(index) {
    return errors.value?.filter(error => error.index === index);
}

function hasErrorsForIndex(index) {
    return getErrorCountForIndex(index) > 0;
}
</script>

<template>
    <div>
        <slot v-bind="{ errors, warnings, errorCount, warningCount, hasErrors, hasWarnings, getErrorCountForIndex, getErrorsForIndex, hasErrorsForIndex }"></slot>
    </div>
</template>

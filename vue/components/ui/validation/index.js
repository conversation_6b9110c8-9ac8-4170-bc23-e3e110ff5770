import { inject } from 'vue';
import { ValidationSet } from '@quotable-value/validation';
import { ComputedRef } from 'vue';

export { default as ValidationContext } from './ValidationContext.vue';
export { default as ValidationProvider } from './ValidationProvider.vue';
export { default as ValidationWrapper } from './ValidationWrapper.vue';

export const VALIDATION_CONTEXT = Symbol('VALIDATION_CONTEXT');
export const VALIDATION_CONTEXT_FALLBACK = {};

/**
 * Inject the validation context.
 * @returns {ComputedRef<ValidationSet>}
 */
export function injectValidationContext() {
    return inject(VA<PERSON><PERSON><PERSON><PERSON>_CONTEXT, VAL<PERSON>ATION_CONTEXT_FALLBACK);
}

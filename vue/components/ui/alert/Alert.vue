<script setup>
import { ALERT_VARIANTS, validateAlertVariant } from '.';

const props = defineProps({
    variant: {
        type: String,
        default: 'default',
        validator: validateAlertVariant,
    },
    iconAlignment: {
        type: String,
        default: 'left',
        validator: (value) => {
            return ['left', 'right'].includes(value);
        },
    },
})
</script>

<template>
    <div :class="['qv-w-full', 'qv-alert', `qv-alert-${iconAlignment}`, ALERT_VARIANTS[variant]]" role="alert">
        <slot></slot>
    </div>
</template>

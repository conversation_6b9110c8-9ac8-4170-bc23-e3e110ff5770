<script setup>
const props = defineProps({
    validation: {
        type: Object,
        required: true,
        validate: (value) => {
            return value.hasOwnProperty('message')
                && value.hasOwnProperty('field');
        }
    },
})
</script>

<template>
    <li>{{ validation.field.displayName }}{{ validation.index !== undefined && validation.index !== null ? ` (${parseInt(validation.index) + 1})` : ''}} {{ validation.message }}</li>
</template>

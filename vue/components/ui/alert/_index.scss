@use '../../../qv-colors' as *;

$alert-default-color: $color-mediumblue;
$alert-error-color: $color-error;
$alert-warning-color: $color-orange;

$alert-border-width: 1px;
$alert-radius: 0.5rem;
$alert-padding: 1rem;

$alert-icon-spacing: 2.5rem;

@mixin alertMixin($color, $darken: 25%, $lighten: 30%) {
  border-color: darken($color, $darken);
  color: darken($color, $darken);
  background-color: lighten($color, $lighten);
}

@mixin iconSelectorMixin {
  & > svg, & > i, & > [class*='material-icons'] {
    position: absolute;
    top: 1rem;
    cursor: pointer;
    @content;
  }
}

@mixin hasIconMixin {
  &:has(svg), &:has(i), &:has([class*='material-icons']) {
    @content;
  }
}

.qv-alert {
  border-radius: $alert-radius;
  border-style: solid;
  border-width: $alert-border-width;
  box-shadow: 0 0 0.5rem rgba(0, 0, 0, 0.1);
  padding: $alert-padding;
  position: relative;

  &.qv-alert-left {
    @include iconSelectorMixin {
      left: 1rem;
      right: auto;
    }

    @include hasIconMixin {
      & > .qv-alert-title, & > .qv-alert-body {
        padding-left: $alert-icon-spacing;
      }
    }
  }

  &.qv-alert-right {
    @include iconSelectorMixin {
      right: 1rem;
      left: auto;
    }

    @include hasIconMixin {
      & > .qv-alert-title, & > .qv-alert-body {
        padding-right: $alert-icon-spacing;
      }
    }
  }
}

.qv-alert-default, .qv-alert-info {
  @include alertMixin($alert-default-color);
}

.qv-alert-error {
  @include alertMixin($alert-error-color);
}

.qv-alert-warning {
  @include alertMixin($alert-warning-color, 10%, 35%);
}
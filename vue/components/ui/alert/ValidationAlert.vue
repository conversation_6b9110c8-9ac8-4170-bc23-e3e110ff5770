<script setup>
import { validateAlertVariant } from '@/components/ui/alert/index';
import Alert from '@/components/ui/alert/Alert.vue';
import MaterialIcon from '@/components/common/MaterialIcon.vue';

const props = defineProps({
    variant: {
        type: String,
        default: 'error',
        validator: validateAlertVariant,
    }
})
</script>

<template>
    <Alert :variant="variant" icon-alignment="right" class="qv-mb-2">
        <template v-if="variant === 'error'">
            <MaterialIcon icon="error" />
        </template>
        <template v-else-if="variant === 'warning'">
            <MaterialIcon icon="warning" />
        </template>
        <slot></slot>
    </Alert>
</template>

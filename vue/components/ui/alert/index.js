export { default as Alert } from './Alert.vue';
export { default as AlertTitle } from './AlertTitle.vue';
export { default as AlertBody } from './AlertBody.vue';
export { default as ValidationAlert } from './ValidationAlert.vue';
export { default as ValidationAlertList } from './ValidationAlertList.vue';
export { default as ValidationAlertListItem } from './ValidationAlertListItem.vue';

/**
 * Enum for panel styles
 * @readonly
 * @enum {string}
 */
export const ALERT_VARIANTS = Object.freeze({
    default: 'qv-alert-default',
    info: 'qv-alert-info',
    error: 'qv-alert-error',
    warning: 'qv-alert-warning',
    success: 'qv-alert-success',
});

export function validateAlertVariant(variant) {
    if (!Object.keys(ALERT_VARIANTS).includes(variant)) {
        throw new Error(`Invalid alert variant: ${variant}`);
    }

    return variant;
}

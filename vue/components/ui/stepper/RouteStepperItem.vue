<script setup>
import { StepperContext } from '@/components/ui/stepper/index';
import { computed, inject } from 'vue';
const props = defineProps({
    name: {
        type: String,
        required: true,
    },
})

const {
    setStep,
    getStep,
    activeStep,
} = inject(StepperContext);
const isActive = computed(() => activeStep.value === props.name);
</script>

<template>
    <button class="qv-stepper-item"  @click="() => setStep(name)" :data-cy="`stepper-item-${name}`">
        <slot :isActive="isActive"></slot>
    </button>
</template>

<style scoped lang="scss">

</style>

<script setup>
import { StepperContext } from '@/components/ui/stepper/index';
import { computed, provide } from 'vue';

const props = defineProps({
    direction: {
        type: String,
        default: 'vertical',
        validate: (value) => {
            return ['horizontal', 'vertical'].includes(value);
        },
    },
    activeStep: {
        type: Number|String,
        required: true,
    },
})

const emits = defineEmits([
    'setStep',
]);

const activeStep = computed(() => props.activeStep);

function setStep(step) {
    emits('setStep', step);
}

provide(StepperContext, {
    setStep,
    activeStep,
});
</script>

<template>
    <div :class="['qv-stepper', `qv-stepper-${direction}`]">
        <slot></slot>
    </div>
</template>

<style scoped lang="scss">

</style>

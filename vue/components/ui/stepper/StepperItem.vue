<script setup>
import { StepperContext } from '@/components/ui/stepper/index';
import { computed, inject } from 'vue';
const props = defineProps({
    step: {
        type: Number|String,
        required: true,
    },
})

const {
    setStep,
    activeStep,
} = inject(StepperContext);
const isActive = computed(() => activeStep.value === props.step);
</script>

<template>
    <button class="qv-stepper-item"  @click="() => setStep(step)">
        <slot :isActive="isActive" ></slot>
    </button>
</template>

<style scoped lang="scss">

</style>

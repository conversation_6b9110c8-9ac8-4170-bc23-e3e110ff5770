@use "../../../qv-text" as *;

$icon-size: 3rem;
$vertical-track-width: 5px;
$vertical-track-height: 20px;

.qv-stepper {
  position: relative;
  display: flex;
}

.qv-stepper-icon {
  width: $icon-size;
  height: $icon-size;

  display: flex;
  align-items: center;

  > * {
    margin: auto;
  }
}

.qv-stepper-item {
  all: unset;
  display: flex;
  gap: 1rem;
  cursor: pointer;
  z-index: 10;

  &:disabled {
    cursor: not-allowed;
    pointer-events: none;
  }
}

.qv-stepper-title {
  user-select: none;
}

.qv-stepper-track {
  position: absolute;
  z-index: 0;
}

.qv-stepper-separator {
    position: relative;
    z-index: 0;
}

.qv-stepper-horizontal {
  flex-direction: row;
}

.qv-stepper-vertical {
  flex-direction: column;

  .qv-stepper-item {
    @extend .qv-text-sm;
    flex-direction: row;
    align-items: center;
    vertical-align: center;
  }

  .qv-stepper-track {
    width: $vertical-track-width;
    height: 100%;
    left: calc($icon-size/2 - $vertical-track-width/2);
    top: 0;
  }

  .qv-stepper-separator {
    width: $vertical-track-width;
    height: $vertical-track-height;
    left: calc($icon-size/2 - $vertical-track-width/2);
    top: 0;
  }
}
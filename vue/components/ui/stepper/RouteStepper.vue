<script setup>
import { StepperContext } from '@/components/ui/stepper/index';
import { computed, provide } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

const props = defineProps({
    direction: {
        type: String,
        default: 'vertical',
        validate: (value) => {
            return ['horizontal', 'vertical'].includes(value);
        },
    },
    routes: {
        type: Array,
        required: true,
    },
});

const emits = defineEmits([
    'setStep',
]);

const router = useRouter();
const route = useRoute();
const activeStep = computed(() => route.name);

function getStep(step) {
    return props.routes.find((route) => route.name === step);
}

function setStep(step) {
    router.push(getStep(step));
}

provide(StepperContext, {
    setStep,
    getStep,
    activeStep,
});
</script>

<template>
    <div :class="['qv-stepper', `qv-stepper-${direction}`]">
        <slot></slot>
    </div>
</template>

<style lang="scss" scoped>

</style>

<script setup>
import MaterialButton from '@/components/ui/button/MaterialButton.vue';

defineProps({
    canRemove: {
        type: Boolean,
        required: false,
        default: true,
    },
});

const emit = defineEmits(['add', 'remove']);
</script>

<template>
    <div class="qv-w-full qv-position-relative" style="width: 55px; max-height: 24px;" data-cy="add-remove-button">
        <MaterialButton classes="qv-color-mediumblue qv-color-mediumblue:75%:hover qv-color-mediumblue:75%:active" icon="add_circle" @click="() => emit('add')" data-cy="add"/>
        <MaterialButton v-if="canRemove" classes="qv-color-red qv-color-red:75%:hover qv-color-red:75%:active" icon="do_not_disturb_on" @click="() => emit('remove')" data-cy="remove"/>
    </div>
</template>

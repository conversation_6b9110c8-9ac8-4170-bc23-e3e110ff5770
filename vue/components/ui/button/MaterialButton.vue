<script setup>
import BaseButton from '@/components/ui/button/BaseButton.vue';
import MaterialIcon from 'Common/MaterialIcon.vue';

defineProps({
    icon: {
        type: String,
        required: true,
    },
    classes: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['click']);
</script>

<template>
    <BaseButton @click="() => emit('click')">
        <MaterialIcon :icon="icon" :class="classes"/>
    </BaseButton>
</template>

<style scoped lang="scss">

</style>

<script>
export default {
    inheritAttrs: false,
}
</script>

<script setup>
import { computed, nextTick, onMounted, ref, useAttrs, watch } from 'vue';
import BaseInput from '@/components/ui/input/BaseInput.vue';

const props = defineProps({
    value: {
        type: String|null,
        required: true,
    }
});

const attrs = useAttrs();
const emit = defineEmits(['input', 'changed']);
const baseInput = ref();
const isFocused = computed(() => baseInput.value?.isFocused);
const isHovered = computed(() => baseInput.value?.isHovered);
const propsValue = computed({
  get: () => props.value,
  set: (value) => {
    emit('input', value);
    nextTick(() => {
      emit('changed', value);
    });
  },
});

defineExpose({
    isFocused,
    isHovered,
});
</script>

<template>
    <BaseInput
        ref="baseInput"
        v-bind="attrs"
        v-model="propsValue"
        :type="'text'"
    />
</template>
export { default as BaseInput } from './BaseInput.vue';
export { default as NumeralInput } from './NumeralInput.vue';
export { default as TextInput } from './TextInput.vue';

export const NUMERAL_PRESET = Object.freeze({
    PERCENT: {
        format: '0.00%',
    },
    PERCENT_ONE_DECIMAL: {
        format: '0.0%',
    },
    PERCENT_SCALED: {
        format: '0.00%',
        scalePercentBy100: true,
    },
    MONEY: {
        format: '$0,0',
    },
    MONEY_POSITIVE: {
        format: '$0,0',
        positive: true,
    },
    MONEY_2DP: {
        format: '$0,0.00',
        fixed: 2,
    },
    WHOLE: {
        format: '0'
    },
    WHOLE_POSITIVE: {
        format: '0',
        positive: true,
    },
    AREA: {
        format: '0',
        round: true
    },
    AREA_HECTARES: {
        format: '0.0000',
        step: 0.0001,
    },
})

export function validateNumeralPreset(value) {
    let valid = false;
    if (typeof value === 'object') {
        valid = Object.values(NUMERAL_PRESET).includes(value);
    } else if (typeof value === 'string') {
        valid = Object.keys(NUMERAL_PRESET).includes(value);
    }

    if (!valid) {
        throw new Error(`Invalid NUMERAL_PRESET: ${value}`);
    }

    return true;
}

<script setup>
import { computed, ref, useAttrs } from 'vue';
import { useVModel } from '@/composables/useVModel';

const props = defineProps({
    value: {
        required: true,
    },
    digitsOnly : {
        required: false,
        default: false,
    }
});

const emit = defineEmits(['input', 'changed', 'blur', 'focus']);
const attrs = useAttrs();

const modelValue = useVModel(props, emit);
const isFocused = ref(false);
const isHovered = ref(false);
const isReadonly = computed(() => attrs.readonly);

function onBlur() {
    isFocused.value = false;
    emit('blur');
}

function onFocus() {
    isFocused.value = true;
    emit('focus');
}

const ensureDigitsOnly = (event) => {
    if (props.digitsOnly && !event.ctrlKey && event.key.length === 1 && isNaN(Number(event.key))) {
        event.preventDefault();
    }
}

defineExpose({
    isFocused,
    isHovered,
    isReadonly,
});
</script>

<template>
    <input
        v-model="modelValue"
        class="qv-form-input"
        v-bind="attrs"
        @focus="onFocus"
        @blur="onBlur"
        @mouseenter="isHovered = true"
        @mouseleave="isHovered = false"
        :disabled="isReadonly"
        :tabindex="isReadonly ? -1 : 0"
        @keydown="ensureDigitsOnly"
    />
</template>

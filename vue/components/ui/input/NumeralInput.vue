<script>
export default {
    inheritAttrs: false,
}
</script>

<script setup>
import numeral from 'numeral';
import { NUMERAL_PRESET, validateNumeralPreset } from '@/components/ui/input/index';
import { computed, nextTick, onMounted, ref, useAttrs, watch } from 'vue';
import BaseInput from '@/components/ui/input/BaseInput.vue';
import { debounce } from 'lodash';

const props = defineProps({
    value: {
        type: Number|null,
        required: true,
    },
    preset: {
        type: String|Object,
        required: false,
        default: 'WHOLE',
        validate: validateNumeralPreset
    },
    default: {
        type: Number,
        required: false,
        default: null,
    },
});

const attrs = useAttrs();
const emit = defineEmits(['input', 'changed']);
const preset = computed(() => typeof props.preset === 'string' ? NUMERAL_PRESET[props.preset] : props.preset);
const baseInput = ref();
const isFocused = computed(() => baseInput.value?.isFocused);
const isHovered = computed(() => baseInput.value?.isHovered);
const fixedDecimal = computed(() => attrs.fixed !== undefined ? parseInt(attrs.fixed) : preset.value.fixed);
const shouldRound = computed(() => attrs.round !== undefined ? Boolean.valueOf(attrs.round) : preset.value.round);
const positiveOnly = computed( () => attrs.positive !== undefined ? Boolean.valueOf(attrs.positive) : preset.value.positive);
const shouldScalePercent = computed(() => attrs.scalePercentBy100 !== undefined ? Boolean.valueOf(attrs.scalePercentBy100) : preset.value.scalePercentBy100);
const minValue = computed(() => attrs.min !== undefined ? parseInt(attrs.min) : preset.value.min);
const stepValue = computed(() => attrs.step !== undefined ? parseInt(attrs.step) : preset.value.step);

const formattedValue = computed(() => {
    try {
        if (shouldScalePercent.value) {
            numeral.options.scalePercentBy100 = true;
        } else {
            numeral.options.scalePercentBy100 = false;
        }

        if ([null, undefined, ''].includes(props.value)) {
            return '';
        }

        return numeral(props.value).format(preset.value.format);
    } finally {
        numeral.options.scalePercentBy100 = true;
    }
});

const propsValue = computed(() => props.value === 0 ? null : props.value);

function setValue(value) {
    if (isNaN(value)) {
        return;
    }

    if (minValue.value !== undefined && value < minValue.value) {
        value = minValue.value;
    }

    let parsedValue = parseFloat(value);
    emit('input', parsedValue);
    nextTick(() => {
        emit('changed', parsedValue);
    });
}

function onFocusOut() {
    let value = props.value;

    if (!value) {
        return;
    }

    if (shouldRound.value) {
        value = Math.round(value);
    }

    if (fixedDecimal.value !== undefined) {
        value = parseFloat(value).toFixed(fixedDecimal.value);
    }

    emit('input', value);
    nextTick(() => {
        emit('changed', value);
    });
}

onMounted(() => {
    if (props.default !== null && props.value === null) {
        emit('input', props.default);
    }
});

defineExpose({
    isFocused,
    isHovered,
});
</script>

<template>
    <BaseInput ref="baseInput"
               v-bind="attrs"
               :title="attrs.title || ''"
               v-model="isFocused ? propsValue : formattedValue"
               @input="(value) => setValue(value)"
               @blur="() => onFocusOut()"
               :type="isFocused ? 'number' : 'text'"
               :step="stepValue"
               :digitsOnly="positiveOnly"
    />
</template>

<style scoped lang="scss">

</style>

<script setup>
import BaseTableRow from '@/components/ui/table/BaseTableRow.vue';

defineProps({
    errored: {
        type: Boolean,
        default: false,
    },
    last: {
        type: Boolean,
        default: false,
    }
});
</script>

<template>
    <BaseTableRow
        :class="[
            errored ?
            'qv-border-bottom-red:50% qv-bg-red:10%' :
            [
                last ?
                'qv-bg-lightblue:25%':
                'qv-border-bottom-lightblue:50% qv-bg-lightblue:25%'
            ]
        ]" :data-cy="`worksheet-table-row`">
        <slot/>
    </BaseTableRow>
</template>

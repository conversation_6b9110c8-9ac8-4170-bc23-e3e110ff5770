import _ from 'lodash';
import Vue from 'vue';

/**
 * @typedef TableOptions
 * @property {Object} defaultValue The default value for a new row
 * @property {boolean?} keepAtLeastOneRow Whether to keep at least one row in the table, will create a new row if the last row is removed
 * */

/**
 * @param data
 * @param {TableOptions} options
 */
export function useTable(data, options) {
    const defaults = {
        defaultValue: {},
        keepAtLeastOneRow: true,
    };
    options = _.merge(defaults, options);

    function createRow() {
        return structuredClone(options.defaultValue);
    }

    function addRow(index = 0) {
        const newIndex = index + 1;
        const newRow = createRow();


        data.value.splice(newIndex, 0, newRow);
        Vue.set(data, newIndex, data.value[newIndex]);
    }

    function removeRow(index) {
        data.value.splice(index, 1);

        if (options.keepAtLeastOneRow && data.value.length === 0) {
            addRow();
        }
    }

    if (options.keepAtLeastOneRow && data.value.length === 0) {
        addRow();
    }

    return {
        addRow, removeRow,
    };
}

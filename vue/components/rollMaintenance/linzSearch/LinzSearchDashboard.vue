<template>
    <div class="resultsWrapper linz-search-activity-list">
        <div class="resultsInner-wrapper mdl-shadow--3dp">
            <linz-search-criteria
                @search="search"
            />
            <div class="resultsFound">
                <p v-if="totalResultCount > 0">
                    Showing {{ firstResultIndex | numeral }}
                    to {{ lastResultIndex | numeral }}
                    of {{ totalResultCount | numeral }} results found.
                </p>
                <p v-else></p>
                <p
                    v-if="error"
                    class="message message-error"
                >
                    {{ error }}
                </p>
            </div>
            <div v-if="totalResultCount > 0">
                <div class="paginator--top" >
                    <paginate
                        v-if="!linzSearchLoading || totalResultsVisible > 0"
                        v-model="page"
                        :page-count="totalPageCount"
                    />
                </div>
                <div class="search-result">
                    <hr class="certificate-title-line-parser" />
                    <linz-search-result
                            v-for="(referenceDetail,referenceId) in results"
                            :key="referenceId"
                            :referenceDetail="referenceDetail"
                            :referenceId="referenceId"
                            :isMapMappingEnabled="isMapMappingEnabled"
                        />
                    <paginate
                        v-if="!linzSearchLoading || totalResultsVisible > 0"
                        v-model="page"
                        :page-count="totalPageCount"
                    />
                </div>
            </div>
            <div
                v-if="!linzSearchLoading && totalResultCount === 0 && linzLastResults.results !== null"
                class="no-results"
            >
                <p>No records were found that match this criteria</p>
            </div>
            <div
                v-if="linzSearchLoading"
                class="results-loading"
            >
                <div class="loadingSpinner loadingSpinnerSearchResults" />
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
export default {
    components: {
        LinzSearchCriteria: () => import(/* webpackChunkName: "Linz Search Criteria" */ './LinzSearchCriteria.vue'),
        LinzSearchResult: () => import(/* webpackChunkName: "Linz Search Criteria" */ './LinzSearchResult.vue'),
        paginate: () => import(/* webpackChunkName: "Paginate" */ '../../common/paginate/paginate.vue')
    },
    data() {
        return {
            isFirstTime: true,
            reportException: null,
            isMapMappingEnabled: false,
        };
    },
    computed: {
        ...mapState('linzSearch', [
            'linzSearchCriteria',
            'linzLastResults',
            'linzSearchLoading',
            'linzLastResults',
            'linzFilterByTitles',
            'error'
        ]),
        ...mapGetters('linzSearch', [
            'totalPageCount',
            'currentPage',
        ]),
        results() {
            if (!this.linzLastResults.results) return false;
            return this.linzLastResults.results.searchResult;
        },
        totalResultCount() {
            if (!this.linzLastResults || !this.linzLastResults.results) return 0;
            return this.linzLastResults.results.totalResultCount;
        },
        firstResultIndex() {
            return this.totalResultCount > 0 ? this.linzSearchCriteria.offset + 1 : 0;
        },
        lastResultIndex() {
            return this.linzSearchCriteria.offset + this.totalResultsVisible;
        },
        totalResultsVisible() {
            if (!this.results) return 0;
            return Object.keys(this.linzLastResults.results.searchResult).length;
        },
        page: {
            get() {
                return this.currentPage;
            },
            set(value) {
                this.changePage(value);
            },
        },
    },
    methods: {
        changePage(data) {
            this.$store.dispatch('linzSearch/changePage', data);
        },
        search(useLastCriteria) {
            this.$store.dispatch('linzSearch/searchResult', false);
        }
    },
};
</script>

<style lang="scss" scoped>
.disable-rows {
    opacity: 0.5;
    pointer-events: none;
}
.paginator--top {
    border-bottom: 1em solid white;
}
.no-results {
    text-align: center;
    font-size: 1.5em;
    padding: 1em 0;
}
</style>
<style lang="scss" scoped="true" src="../rollMaintenance.scss"></style>
<style lang="scss" scoped="true" src="./linzSearchDashBoard.scss"></style>

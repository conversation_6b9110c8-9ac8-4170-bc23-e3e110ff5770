<template>
    <div class="certificate-title-detail">
        <div class="certificate-title fullAddress">
            <span v-if="referenceId.includes('noTitle-')" class="no-title-msg">The following parcels are not part of any title</span>
            <span class="title-reference">Title Reference :
                <template v-if="referenceId !== '' && !referenceId.includes('noTitle-')">
                    {{ referenceId }}
                </template>
                <template v-else>
                    <span class="no-title">No Title</span>
                </template>
            </span>
            <div class="div-land-district">
                Land District :
                <template v-if="referenceDetail.landDistrict !== ''">{{ referenceDetail.landDistrict }}</template>
                <template v-else>-</template>
            </div>
            <div class="div-land-district">
                TA :
                <template v-if="referenceDetail.territorialAuthorityCode !== null && referenceDetail.territorialAuthorityName !== null">
                    {{ referenceDetail.territorialAuthorityCode }} {{referenceDetail.territorialAuthorityName | trim }}
                </template>
                <template v-else>-</template>
            </div>
            <div :class="currentStatusClass" v-if="referenceDetail.status !== ''">Title Status : {{ referenceDetail.status !== null ? referenceDetail.status : '-' }}</div>
            <div class="div-land-district righty" v-if="referenceDetail.issueDate !== null">Date Issued : {{ referenceDetail.issueDate }}</div>
            <div class="div-land-district righty" v-if="referenceDetail.effectiveAsAt !== ''">Effective as : {{ referenceDetail.effectiveAsAt }}</div>
            <ul class="toolbar righty">
                <li class="md-qivs" v-if="isMapMappingEnabled"><label>MAP</label><i class="material-icons">call_made</i></li>
            </ul>
        </div>
        <div class="col-container certificate-legal-description">
            <div class="col-row">
                <div class="col col-12">
                    <table class="certificate-detail-table">
                        <tr>
                            <th class="col-3">Title Legal Description</th>
                            <th class="col-1">Estate Type</th>
                            <th class="col-1">Estate Share</th>
                            <th class="col-1">Title Land Area</th>
                            <th class="col-2">Parcel Id</th>
                            <th class="col-4 no-border">Title Type</th>
                        </tr>
                        <template v-for="(descriptionDetail,descriptionIndex) in referenceDetail.titleLegalDetail">
                            <tr>
                                <td v-html="descriptionDetail.legalDesc"></td>
                                <td v-text="descriptionDetail.estateType"></td>
                                <td v-text="descriptionDetail.estateShare"></td>
                                <td v-text="formatArea(descriptionDetail.totalArea)"></td>
                                <td v-html="formatDescriptionParcelIds(descriptionDetail.parcelId)"></td>
                                <td class="no-border">
                                    <template v-if="descriptionIndex === 0">{{ referenceDetail.titleType }}</template>
                                </td>
                            </tr>
                        </template>
                    </table>
                </div>
            </div>

            <div class="col-row certificate-other-detail">
                <div class="col col-8 no-padding">
                    <table class="certificate-detail-table">
                        <tr>
                            <th class="col-3">Parcel Legal Description</th>
                            <th class="col-1">Estate Type</th>
                            <th class="col-1">Estate Share</th>
                            <th class="col-1">Parcel Land Area</th>
                            <th class="col-2 no-border">Parcel Id</th>
                        </tr>
                        <template v-for="(parcelDetail,parcelIndex) in referenceDetail.parcelDetail">
                            <tr>
                                <td v-html="parcelDetail.legalDesc"></td>
                                <td v-text="parcelDetail.estateType"></td>
                                <td v-text="parcelDetail.estateShare"></td>
                                <td v-text="formatArea(parcelDetail.parcelArea)" :class="{'text-red-600': hasParcelTitleAreaMismatch}"></td>
                                <td class="no-border">
                                    {{ parcelDetail.parcelId }}
                                    <input type="checkbox" v-if="parcelDetail.isMappable === 1 && isMapMappingEnabled" v-model="parcelDetail.parcelId" class="righty" />
                                </td>
                            </tr>
                        </template>
                    </table>
                </div>
                <div class="col col-4">
                    Current Owner
                    <div class="no-padding">
                        <table class="owners-detail-table">
                            <tr v-for="(ownerDetail, ownerIndex) in referenceDetail.currentOwners" :key="'currentOwners'+ownerIndex">
                                <td class="col-6">{{ ownerDetail.owner }}</td>
                                <td class="col-2">{{ ownerDetail.ownerType }}</td>
                                <td class="col-1">{{ ownerDetail.status }}</td>
                                <td class="col-2">{{ ownerDetail.lodgedDateTime }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-row certificate-other-detail">
                <div class="col col-12">
                    <table class="certificate-detail-table">
                        <tr>
                            <th class="col-3">QIVS Legal Description</th>
                            <th class="col-1">Val Refs</th>
                            <th class="col-1">QPID</th>
                            <th class="col-1">QIVS Land Area</th>
                            <th class="col-2">Situation Address</th>
                            <th class="col-4 no-border">QIVS Occupier</th>
                        </tr>
                        <template v-for="(qivDetail,qivIndex) in referenceDetail.qivsDetail">
                            <tr>
                                <td v-html="qivDetail.textDescription"></td>
                                <td>
                                    <span>
                                        <template v-if="qivDetail.taAccess">
                                            <a href="javascript:void(0);" @click="externalQPIDLinkHandler(qivDetail.qpid)">{{ qivDetail.rollNumber+'/'+qivDetail.estateType }}</a>
                                        </template>
                                        <template v-else>
                                            {{ qivDetail.rollNumber+'/'+qivDetail.estateType }}
                                        </template>
                                    </span>
                                </td>
                                <td v-text="qivDetail.qpid"></td>
                                <td v-text="formatArea(qivDetail.landArea)"></td>
                                <td>
                                    {{ qivDetail.situationNumber + (qivDetail.additionalNumber !== null ? ' '+qivDetail.additionalNumber : '' ) + (qivDetail.streetName !== null ? ' '+qivDetail.streetName : '' ) + (qivDetail.streetNameSuffix !== null ? ' '+qivDetail.streetNameSuffix : '' ) }}
                                </td>
                                <td class="no-border">
                                    <span v-for="(occupierDetail,occupierIndex) in qivDetail.occupierList" :key="'occupierDetail_'+occupierIndex">
                                        {{ occupierDetail.firstGivenName !== null ? occupierDetail.firstGivenName : '' }}
                                        {{ occupierDetail.secondGivenName !== null ? occupierDetail.secondGivenName : '' }}
                                        {{ occupierDetail.thirdGivenName !== null ? occupierDetail.thirdGivenName : '' }}
                                        {{ occupierDetail.lastName !== null ? occupierDetail.lastName : '' }}
                                    </span>
                                </td>
                            </tr>
                        </template>
                    </table>
                </div>
            </div>

            <div class="col-row certificate-other-detail">
                <div class="col col-12">
                    <table class="certificate-detail-table">
                        <tr>
                            <th class="col-3">Prior Titles</th>
                            <th class="col-5">New Titles</th>
                            <th class="col-4 no-border">Previous Owner</th>
                        </tr>
                        <tr>
                            <td>
                                <span>
                                    <template v-for="(priorTitle , index) in referenceDetail.priorTitleReference">
                                        <a @click="selectCertificate(priorTitle)" href="javascript:void(0);">
                                            {{ priorTitle }}
                                        </a>
                                        {{ (index+1) !== referenceDetail.priorTitleReference.length ? ',' : ''}}
                                    </template>
                                </span>
                            </td>
                            <td>
                                <span>
                                    <template v-for="(newTitle , index) in referenceDetail.newTitleIssued">
                                        <a @click="selectCertificate(newTitle)" href="javascript:void(0);">
                                            {{ newTitle }}
                                        </a>
                                        {{ (index+1) !== referenceDetail.newTitleIssued.length ? ',' : ''}}
                                    </template>
                                </span>
                            </td>
                            <td class="no-border">
                                <div class="no-padding">
                                    <table class="owners-detail-table">
                                        <tr v-for="(ownerDetail, ownerIndex) in referenceDetail.previousOwners" :key="'previousOwner'+ownerIndex">
                                            <td class="col-6">{{ ownerDetail.owner }}</td>
                                            <td class="col-2">{{ ownerDetail.ownerType }}</td>
                                            <td class="col-1">{{ ownerDetail.status }}</td>
                                            <td class="col-2">{{ ownerDetail.lodgedDateTime }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { mapState } from 'vuex';

export default {
  components: {
      SplineGraph: () => import('../../dashboard/SplineGraph.vue')
    },
    props: {
        referenceDetail: {
            type: Object,
            required: true,
        },
        referenceId: {
            type: String,
            required: false,
        },
        isMapMappingEnabled: {
            type: Boolean,
            required: true,
        },
    },
    filters: {
  	    trim: function(string) {
    	    return string.trim()
        }
    },
    computed: {
        ...mapState('linzSearch', [
            'linzFilterByTitles'
        ]),
        ...mapState('userData', [
            'qivsUrl'
        ]),
        currentStatusClass() {
            return {
                        'title-status': true,
                        'righty': true,
                        'title-status-live': this.referenceDetail.status === 'LIVE' ? true : false,
                        'title-status-part-cancelled': this.referenceDetail.status === 'PRTC' ? true : false,
                        'title-status-cancelled': this.referenceDetail.status === 'CNCD' ? true : false,
                    };
        },
        parcelTotalArea() {
          return this.referenceDetail.parcelDetail.reduce((acc, par) => {
            return acc + par.parcelArea || 0
          }, 0);
        },
        titleTotalArea() {
          return this.referenceDetail.titleLegalDetail.reduce((acc, title) => {
            return acc + title.totalArea || 0
          }, 0);
        },
        hasParcelTitleAreaMismatch() {
          return this.parcelTotalArea !== this.titleTotalArea;
        }
    },
    methods: {
        selectCertificate(certificateIds) {
            this.$store.commit('linzSearch/setLinzFilterByTitles', certificateIds);
        },
        manageQivLines(qivOccupierList) {
            let returnHtml = '';
            qivOccupierList.forEach((element,index) => {
                index > 0 ? returnHtml += `<span></span>` : '';
            });
            return returnHtml;
        },
        externalQPIDLinkHandler(qpid) {
          const route = this.$router.resolve({name: 'property-detail', params: {qpid}})
          window.open(route.href, "_blank");
        },
        formatDescriptionParcelIds(parcelIds) {
            let parcelIdArray = parcelIds.split(',');
            let returnHtml = '';
            parcelIdArray.forEach(element => {
                returnHtml += `<p>${element}</p>`;
            });
            return returnHtml;
        },
        formatArea(value) {
            return value !== null ?
                `${value.toFixed(4).substring(value >= 1 ? 0 : 1)} ha` :
                null;
        }
    },
}
</script>

<style lang="scss" scoped="true" src="../rollMaintenance.scss"></style>
<style lang="scss" scoped="true" src="./linzSearchDashBoard.scss"></style>

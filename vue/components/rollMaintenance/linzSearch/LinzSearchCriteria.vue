<template>
    <div class="linz-search-criteria">
        <div class="resultsTitle">
            <div class="taLinzSearch-toolbar qvToolbar-wrapper">
                <div class="taLinzSearch-title advSearch-group qvToolbar-leftMenu lefty">
                    <span> LINZ Search </span>
                </div>
                <div class="md-full qvToolbar">
                    <ul class="qvToolbar-links lefty">
                        <li v-bind:class="{
                            'certificate-of-title': true,
                            active: certificateTabEnabled == true,
                        }"
                        @click="toggleSearchTab('certificateTab')">
                            <label>Certificate of Title</label>
                        </li>
                        <li
                            v-bind:class="{
                                'legal-description': true,
                                active: legalDescriptionTabEnabled == true,
                            }"
                            @click="toggleSearchTab('legalDescriptionTab')">
                            <label>Legal Description</label>
                        </li>
                        <li
                            v-bind:class="{ 'owner': true, active: ownerTabEnabled == true }"
                            @click="toggleSearchTab('ownerTab')">
                            <label>Owner</label>
                        </li>
                        <li
                            v-bind:class="{ 'parcel-id': true, active: parcelIdTabEnabled == true }"
                            @click="toggleSearchTab('parcelIdTab')">
                            <label>Parcel ID</label>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="message message-error linz-search-error-messages" v-if="this.formErrorMessages.length > 0">
            Unable to proceed further until the following {{ this.formErrorMessages.length }} issues are resolved.
            <ul>
                <li :key="errorMessage" v-for="errorMessage in this.formErrorMessages">
                    {{ errorMessage }}
                </li>
            </ul>
        </div>
        <div class="col-container">
            <div class="col-row" v-if="certificateTabEnabled">
                <div class="col col-10">
                    <label title="For old title reference, enter the Land District Code (2 characters) followed without a space by Volume number, then a forward slash, then Folio number. E.g. WN19A/576. For new Landonline title references, enter only the numeric title number. E.g. 708621">
                        <span class="label">Certificate of Title</span>
                        <input
                            type="text"
                            class="titleReferenceInput"
                            :value="linzSearchCriteria.titleReference"
                            @change="updateCriteriaItem('titleReference', allowOnlySlashCharacter($event.target.value))" />
                    </label>
                </div>
                <div class="col col-2">
                    <ul class="qvToolbar-qivs righty">
                        <li class="md-qivs" @click="openMaoriLandLink">
                            <label>Maori Land Link</label>
                            <i class="material-icons">call_made</i>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="col-row" v-if="legalDescriptionTabEnabled">
                <div class="col col-10 margin-zero">
                    <div class="col-container padding-zero">
                        <div class="col-row">
                            <div class="col col-legal-selection">
                                <input type="radio" value="no" v-model="isAutoLegalSearchEnabled" class="legalDescriptionSearch" />
                            </div>
                            <div class="col col-legal-search-criteria margin-zero">
                                <div class="col-container padding-zero">
                                    <div class="col-row">
                                        <div class="col col-3">
                                            <label>
                                                <span class="label">Land District</span>
                                            </label>
                                            <multiselect
                                                :value="selectedLandDistrict"
                                                :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                :options="landDistrictOptions"
                                                track-by="key"
                                                label="value"
                                                placeholder=""
                                                select-label="⏎ select"
                                                deselect-label="⏎ remove"
                                                :multiple="true"
                                                :taggable="true"
                                                :close-on-select="true"
                                                @select="updateLandDistrict"
                                                @remove="removeLandDistrict"
                                                :loading="isLandDistrictLoading"
                                                class="landDistrictInput"
                                                />
                                        </div>
                                        <div class="col col-part-section">
                                            <label>
                                                <span class="label">Part</span>
                                                <input type="checkbox" class="partInput" :checked="isPartSelected" v-model="isPartSelected" :disabled="isAutoLegalSearchEnabled !== 'no'" />
                                            </label>
                                        </div>
                                        <div class="col col-parcel-type-section">
                                            <label>
                                                <span class="label">Parcel Type</span>
                                            </label>
                                            <multiselect
                                                v-model="selectedParcelType"
                                                :options="parcelTypeOptions"
                                                :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                track-by="key"
                                                label="value"
                                                placeholder=""
                                                select-label="⏎ select"
                                                deselect-label=""
                                                :allow-empty="false"
                                                @select="updateParcelType"
                                                :loading="isParcelTypeLoading"
                                                class="parcelTypeInput"
                                                />
                                        </div>
                                        <div class="col col-1">
                                            <label>
                                                <span class="label">Parcel Number</span>
                                                <input
                                                    type="text"
                                                    class="parcelValueInput"
                                                    :value="linzSearchCriteria.parcelValue"
                                                    :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                    @change="updateCriteriaItem('parcelValue', allowLegalDescriptionString($event.target.value))" />
                                            </label>
                                        </div>
                                        <div class="col col-3">
                                            <label>
                                                <span class="label">Plan Type</span>
                                            </label>
                                            <multiselect
                                                class="planTypeInput"
                                                v-model="selectedPlanType"
                                                :options="planTypeOptions"
                                                :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                track-by="key"
                                                label="value"
                                                placeholder=""
                                                select-label="⏎ select"
                                                @select="updatePlanType"
                                                :loading="isPlanTypeLoading"
                                                :allow-empty="false"
                                                deselect-label=""
                                                />
                                        </div>
                                        <div class="col col-2">
                                            <label>
                                                <span class="label">Plan Number</span>
                                                <input
                                                    type="text"
                                                    class="planNumberInput"
                                                    :value="linzSearchCriteria.planValue"
                                                    :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                    @change="updateCriteriaItem('planValue', allowLegalDescriptionString($event.target.value))" />
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-row">
                                        <div class="col col-3">
                                            <label>
                                                <span class="label">Second Parcel Number</span>
                                                <input
                                                    type="text"
                                                    class="secondParcelNumberInput"
                                                    :value="linzSearchCriteria.secondParcelValue"
                                                    :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                    @change="updateCriteriaItem('secondParcelValue', allowOnlyWildCharacter($event.target.value))" />
                                            </label>
                                        </div>
                                        <div class="col col-3">
                                            <label>
                                                <span class="label">Block</span>
                                                <input
                                                    type="text"
                                                    :value="linzSearchCriteria.block"
                                                    :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                    class="blockInput"
                                                    @change="updateCriteriaItem('block', allowOnlyWildCharacter($event.target.value))" />
                                            </label>
                                        </div>
                                        <div class="col col-3">
                                            <label>
                                                <span class="label">MaoriName</span>
                                                <input
                                                    type="text"
                                                    :value="linzSearchCriteria.maoriName"
                                                    :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                    class="maoriNameInput"
                                                    @change="updateCriteriaItem('maoriName', allowOnlyWildCharacter($event.target.value))" />
                                            </label>
                                        </div>
                                        <div class="col col-3">
                                            <label>
                                                <span class="label">Other Appellation</span>
                                                <input
                                                    type="text"
                                                    :value="linzSearchCriteria.otherApp"
                                                    :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                    class="otherAppInput"
                                                    @change="updateCriteriaItem('otherApp', allowOnlyWildCharacter($event.target.value))" />
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-row" v-if="isComplexSearch">
                                        <div class="col col-legal-search-criteria">
                                            <label>
                                                <span class="label">Appellation</span>
                                                <input
                                                    class="appellationValueInput"
                                                    type="text"
                                                    :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                    :value="appellationValue"
                                                    readonly
                                                    />
                                            </label>
                                        </div>
                                        <div class="col col-legal-selection">
                                            <label>
                                                <span class="label">Suffix</span>
                                                <input
                                                    class="suffixInput"
                                                    type="checkbox"
                                                    :checked="isComplexSuffixSelected"
                                                    :disabled="isAutoLegalSearchEnabled !== 'no'"
                                                    v-model="isComplexSuffixSelected" />
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-row">
                             <div class="col col-legal-selection">
                                <input type="radio" value="yes" v-model="isAutoLegalSearchEnabled" class="legalDescriptionAutoSearch" />
                            </div>
                            <div class="col col-legal-search-criteria">
                                <label>
                                    <span class="label">Legal Description Search</span>
                                </label>
                                <multiselect
                                    v-model="selectedAutoLegalDescription"
                                    :disabled="isAutoLegalSearchEnabled !== 'yes'"
                                    :options="autoLegalDescritionOptions"
                                    :searchable="true"
                                    track-by="key"
                                    label="value"
                                    placeholder=""
                                    select-label="⏎ select"
                                    deselect-label="⏎ remove"
                                    @select="updateAutoLegalDescription"
                                    :loading="isAutoLegalDescriptionLoading"
                                    @search-change="autoLegalDescriptionRequest"
                                    class="autoLegalDescriptionInput"
                                    @remove="clearAutoLegalDescriptionSearchForm"
                                >
                                    <span slot="noOptions"></span>
                                </multiselect>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col col-2">
                    <ul class="qvToolbar-qivs righty">
                        <li class="md-qivs" @click="openMaoriLandLink">
                            <label>Maori Land Link</label>
                            <i class="material-icons">call_made</i>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="col-row" v-if="ownerTabEnabled">
                <div class="col col-10 margin-zero">
                    <div class="col-container padding-zero">
                        <div class="col-row">
                            <div class="col col-3">
                                <label>
                                    <span class="label">Land District</span>
                                </label>
                                <multiselect
                                    :value="selectedLandDistrict"
                                    :options="landDistrictOptions"
                                    track-by="key"
                                    label="value"
                                    placeholder=""
                                    select-label="⏎ select"
                                    deselect-label="⏎ remove"
                                    :multiple="true"
                                    :taggable="true"
                                    :close-on-select="true"
                                    @select="updateLandDistrict"
                                    @remove="removeLandDistrict"
                                    :loading="isLandDistrictLoading"
                                    class="landDistrictInput"
                                    />
                            </div>
                            <div class="col col-ownerby-selection">
                                <input type="radio" value="no" v-model="isCorporateSearch" class="lastNameSearch" />
                            </div>
                            <div class="col col-4">
                                <label>
                                    <span class="label">Last Name</span>
                                    <input
                                        type="text"
                                        class="lastNameInput"
                                        :value="linzSearchCriteria.lastName"
                                        :disabled="isCorporateSearch !== 'no'"
                                        @change="updateCriteriaItem('lastName', allowOnlyWildCharacter($event.target.value))" />
                                </label>
                            </div>
                            <div class="col col-4">
                                <label>
                                    <span class="label">Other Name</span>
                                    <input
                                        type="text"
                                        :value="linzSearchCriteria.firstName"
                                        :disabled="isCorporateSearch !== 'no'"
                                        class="firstNameInput"
                                        @change="updateCriteriaItem('firstName', $event.target.value)" />
                                </label>
                            </div>
                        </div>

                        <div class="col-row">
                            <div class="col col-3"></div>
                            <div class="col col-ownerby-selection">
                                <input type="radio" value="yes" v-model="isCorporateSearch" class="corporateSearch" />
                            </div>
                            <div class="col col-8">
                                <label>
                                    <span class="label">Corporate Name</span>
                                    <input
                                        type="text"
                                        class="corporateNameInput"
                                        :value="linzSearchCriteria.corporateName"
                                        :disabled="isCorporateSearch !== 'yes'"
                                        @change="updateCriteriaItem('corporateName', $event.target.value)" />
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col col-2">
                    <ul class="qvToolbar-qivs righty">
                        <li class="md-qivs" @click="openMaoriLandLink">
                            <label>Maori Land Link</label>
                            <i class="material-icons">call_made</i>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="col-row" v-if="parcelIdTabEnabled">
                <div class="col col-2">
                    <label>
                        <span class="label">Parcel ID</span>
                        <input
                            type="number"
                            class="parcelIdInput"
                            :value="linzSearchCriteria.parcelId"
                            @change="updateCriteriaItem('parcelId', allowOnlyNumbers($event.target.value))" />
                    </label>
                </div>
                <div class="col col-8">
                </div>
                <div class="col col-2">
                    <ul class="qvToolbar-qivs righty">
                        <li class="md-qivs" @click="openMaoriLandLink">
                            <label>Maori Land Link</label>
                            <i class="material-icons">call_made</i>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-4 icons8-news-filled">
                    <label>
                        <span class="label title-status-span">Title Status</span>
                    </label>
                    <div class="title-status">
                        <label class="linz-title-status-active"><input type="checkbox" :checked="isLinzActiveSelected" v-model="isLinzActiveSelected" data-cy="activeTitleStatusInput" /> Active</label>
                        <label class="linz-title-status-inactive"><input type="checkbox" :checked="isLinzInActiveSelected" v-model="isLinzInActiveSelected" data-cy="inactiveTitleStatusInput" /> Inactive</label>
                        <label class="linz-title-status-notitle" v-if="isNoTitleAllowed"><input type="checkbox" :checked="isLinzNoTitleSelected" v-model="isLinzNoTitleSelected" data-cy="noTitleStatusInput" /> No Title</label>
                    </div>
                </div>
                <div class="col col-4"></div>
                <div class="col col-4">
                    <div class="righty search-control-buttons">
                        <div title="Export Results (limit 100,000)" class="exportResults mdl-button mdl-js-button mdl-button--icon" v-if="isExportEnable">
                            <i class="material-icons md-dark">&#xE06F;</i>
                        </div>
                        <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect advSearchClear"
                            title="Clear Search Criteria"
                            @click="clearSearch" >
                            Clear
                        </button>
                        <button
                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored advlinzSearch"
                            :class="{ disabled: linzSearchLoading }"
                            @click="search">
                            Search
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from "vuex";
import Multiselect from 'vue-multiselect';
export default {
    components: {
        Multiselect,
    },
    data() {
        return {
            certificateTabEnabled: true,
            legalDescriptionTabEnabled: false,
            ownerTabEnabled: false,
            parcelIdTabEnabled: false,
            selectedLinzSearchTab: 'certificateTab',
            isNoTitleAllowed: false,
            formErrorMessages: [],
            landDistrictOptions: [],
            selectedLandDistrict: [],
            isLandDistrictLoading: true,
            parcelTypeOptions: [],
            selectedParcelType: [],
            defalutParcelTypeId: 10,
            isParcelTypeLoading: true,
            planTypeOptions: [],
            selectedPlanType: [],
            defalutPlanTypeId: 15,
            isPlanTypeLoading: true,
            appellationValue:null,
            isComplexSuffixSelected: false,
            isPartSelected: false,
            isLinzActiveSelected: true,
            isLinzInActiveSelected: true,
            isLinzNoTitleSelected: false,
            isAutoLegalSearchEnabled: 'no',
            autoLegalDescritionOptions: [],
            selectedautoLegalDescription: [],
            isAutoLegalDescriptionLoading: false,
            isChangedTab: false,
            isCorporateSearch: 'no',
            resetForm: false,
            isExportEnable: false,
        };
    },
    watch: {
        selectedLinzSearchTab(selectedLinzSearchTab,previousSelectedLinzSearchTab) {
            if (selectedLinzSearchTab !== previousSelectedLinzSearchTab) {
                this.isTabChanged = true;
                this.clearSearch();
                this.formErrorMessages = [];
                this.certificateTabEnabled = false;
                this.legalDescriptionTabEnabled = false;
                this.ownerTabEnabled = false;
                this.parcelIdTabEnabled = false;
                switch (selectedLinzSearchTab) {
                    case 'certificateTab' :
                        this.certificateTabEnabled = true;
                        this.isNoTitleAllowed = false;
                        break;
                    case 'ownerTab' :
                        this.ownerTabEnabled = true;
                        this.isNoTitleAllowed = false;
                        break;
                    case 'legalDescriptionTab' :
                        this.legalDescriptionTabEnabled = true;
                        this.isNoTitleAllowed = true;
                        break;
                    case 'parcelIdTab' :
                        this.parcelIdTabEnabled = true;
                        this.isNoTitleAllowed = true;
                        break;
                }
            }
            this.clearLastSearchResult();
        },
        isAutoLegalSearchEnabled(value) {
            this.formErrorMessages = [];
            this.resetTitleStatus();
            value === 'yes' ? this.clearLegalDescriptionSearchForm() : this.clearAutoLegalDescriptionSearchForm();
        },
        isCorporateSearch(value) {
            this.formErrorMessages = [];
            this.resetTitleStatus();
            value === 'yes' ? this.clearLastNameOwnerForm() : this.clearCorporateNameOwnerForm();
        },
        linzFilterByTitles(value) {
            if(value !== null) {
                this.filterByTitles(value);
            }
        }
    },
    computed: {
        ...mapState('linzSearch', [
            'linzSearchCriteria',
            'linzSearchLoading',
            'maoriLandLink',
            'linzFilterByTitles'
        ]),

        ...mapState('userData', [
            'isExternalUser',
            'userId'
        ]),
        isValid() {
            this.formErrorMessages = [];
            switch (this.selectedLinzSearchTab) {
                case 'certificateTab' :
                        if(!this.linzSearchCriteria.titleReference) {
                            this.formErrorMessages.push('Certificate of Title is required');
                        }
                        break;
                case 'legalDescriptionTab' :
                        if(this.isAutoLegalSearchEnabled === 'yes') {
                            if(!this.linzSearchCriteria.legalDescriptionId) {
                                this.formErrorMessages.push('Legal Description is required');
                            }
                        } else {
                            if(!this.linzSearchCriteria.landDistrictId || this.linzSearchCriteria.landDistrictId.length <= 0) {
                                this.formErrorMessages.push('You must select a Land District');
                            }
                            if(!this.linzSearchCriteria.parcelTypeDisplayOrder || this.linzSearchCriteria.parcelTypeDisplayOrder === 70) {
                                this.formErrorMessages.push('None is not a valid Parcel Type');
                            }
                            if(!this.linzSearchCriteria.parcelValue) {
                                this.formErrorMessages.push('You must enter a Parcel Number');
                            }
                            if(!this.linzSearchCriteria.planTypeId || this.linzSearchCriteria.planTypeId === 1) {
                                this.formErrorMessages.push('None is not a valid Plan Type');
                            }
                            if(!this.linzSearchCriteria.planValue) {
                                this.formErrorMessages.push('You must enter a Plan Number');
                            }
                        }
                        break;
                case 'ownerTab' :
                        if(!this.linzSearchCriteria.landDistrictId || this.linzSearchCriteria.landDistrictId.length <= 0) {
                            this.formErrorMessages.push('You must select a Land District');
                        }
                        if(this.isCorporateSearch === 'yes') {
                            if(!this.linzSearchCriteria.corporateName === null) {
                                this.formErrorMessages.push('You must enter a Family Name or a Corporate Name');
                            } else {
                                if(this.linzSearchCriteria.corporateName[0] === '*') {
                                    this.formErrorMessages.push('You cannot specify a wildcard as the first character or only character of your search');
                                }
                            }
                        } else {
                            if(!this.linzSearchCriteria.lastName) {
                                this.formErrorMessages.push('You must enter a Family Name or a Corporate Name');
                            } else {
                                if(this.linzSearchCriteria.lastName[0] === '*') {
                                    this.formErrorMessages.push('You cannot specify a wildcard as the first character or only character of your search');
                                } else if(this.linzSearchCriteria.firstName) {
                                    if(this.linzSearchCriteria.firstName[0] === '*') {
                                        this.formErrorMessages.push('You cannot specify a wildcard as the first character or only character of your search');
                                    }
                                }
                            }
                        }
                        break;
                case 'parcelIdTab' :
                        if(!this.linzSearchCriteria.parcelId) {
                            this.formErrorMessages.push('You must enter a valid numeric Parcel Id');
                        }
                        break;
            }
            if(!this.isTitleStatusSelected()) {
                this.formErrorMessages.push('Please select Title Status');
            }
            return this.formErrorMessages.length > 0 ? false : true;
        },
        isComplexSearch() {
            let isComplexSearch =  this.linzSearchCriteria.otherApp !== null || this.linzSearchCriteria.maoriName !== null || this.linzSearchCriteria.block !== null || this.linzSearchCriteria.secondParcelValue !== null ? true : false;
            let appellationValueList = [];
            if(isComplexSearch) {
                this.appellationValue = '';
                this.isPartSelected ? appellationValueList.push('pt') : '';
                this.linzSearchCriteria.maoriName !== null ? appellationValueList.push(this.linzSearchCriteria.maoriName) : '';
                this.linzSearchCriteria.parcelTypeDisplayOrder !== null && this.selectedParcelType.value.toLowerCase()  !== 'none' ? appellationValueList.push(this.selectedParcelType.value) : '';
                this.linzSearchCriteria.parcelValue !== null ? appellationValueList.push(this.linzSearchCriteria.parcelValue) : '';
                this.linzSearchCriteria.secondParcelValue !== null ? appellationValueList.push(this.linzSearchCriteria.secondParcelValue) : '';
                this.linzSearchCriteria.block !== null ? appellationValueList.push('Block '+this.linzSearchCriteria.block) : '';
                if(this.isComplexSuffixSelected) {
                    this.linzSearchCriteria.planValue !== null ? appellationValueList.push(this.linzSearchCriteria.planValue) : '';
                    this.linzSearchCriteria.planTypeId !== null && this.selectedPlanType.value.toLowerCase() !== 'none' ? appellationValueList.push(this.selectedPlanType.value) : '';
                } else {
                    this.linzSearchCriteria.planTypeId !== null && this.selectedPlanType.value.toLowerCase() !== 'none' ? appellationValueList.push(this.selectedPlanType.value) : '';
                    this.linzSearchCriteria.planValue !== null ? appellationValueList.push(this.linzSearchCriteria.planValue) : '';
                }
                this.linzSearchCriteria.otherApp !== null ? appellationValueList.push(this.linzSearchCriteria.otherApp) : '';
            }
            this.appellationValue = appellationValueList.join(' ');
            return isComplexSearch;
        }
    },
    methods: {
        toggleSearchTab(selectedLinzSearchTab) {
            this.selectedLinzSearchTab = selectedLinzSearchTab;
        },
        openMaoriLandLink: function () {
            window.open(this.maoriLandLink, "Maori Land Link");
        },
        updateCriteriaItem(elementKey,elementValue) {
            const data = {
                            id: elementKey,
                            value: elementValue === '' ? null :elementValue
                        };
            this.$store.commit('linzSearch/setLinzSearchCriteriaItem', data);
        },
        resetTitleStatus() {
            this.updateCriteriaItem('active', 1);
            this.isLinzActiveSelected = true;
            this.updateCriteriaItem('noTitle', 0);
            this.isLinzNoTitleSelected = false;
            if(this.selectedLinzSearchTab === 'ownerTab' || this.selectedLinzSearchTab === 'parcelIdTab' || (this.selectedLinzSearchTab === 'legalDescriptionTab' && this.isAutoLegalSearchEnabled === 'no')) {
                this.updateCriteriaItem('inActive', 0);
                this.isLinzInActiveSelected = true;
            } else {
                this.updateCriteriaItem('inActive', 1);
                this.isLinzInActiveSelected = true;
            }
        },
        clearSearch() {
            this.resetForm = true;
            this.clearLastSearchResult();
            switch (this.selectedLinzSearchTab) {
                case 'certificateTab' :
                    this.clearTitleReference();
                    break;
                case 'ownerTab' :
                    if(this.isTabChanged) {
                        this.isCorporateSearch = 'no';
                        this.clearCorporateNameOwnerForm();
                        this.clearLastNameOwnerForm();
                    } else {
                        if(this.isCorporateSearch === 'no') {
                            this.clearLastNameOwnerForm();
                        } else {
                            this.clearCorporateNameOwnerForm();
                        }
                    }
                    break;
                case 'legalDescriptionTab' :
                    if(this.isTabChanged) {
                        this.isAutoLegalSearchEnabled = 'no';
                        this.clearLegalDescriptionSearchForm();
                        this.clearAutoLegalDescriptionSearchForm();
                    } else {
                        if(this.isAutoLegalSearchEnabled === 'no') {
                            this.clearLegalDescriptionSearchForm();
                        } else {
                            this.clearAutoLegalDescriptionSearchForm();
                        }
                    }
                    break;
                case 'parcelIdTab' :
                    this.clearParcelID();
                    break;
            }
            this.resetTitleStatus();
            this.isTabChanged = false;
            this.resetForm = false;
        },
        clearTitleReference() {
            this.updateCriteriaItem('titleReference', null);
        },
        clearLegalDescriptionSearchForm() {
            if(this.selectedLandDistrict && this.selectedLandDistrict.length > 0) {
                this.selectedLandDistrict = [];
                this.updateCriteriaItem('landDistrictId', this.selectedLandDistrict);
            }
            this.updateCriteriaItem('part', null);
            if(this.selectedParcelType['key'] !== undefined) {
                this.selectedParcelType = this.parcelTypeOptions.find((element,index) => element.key === this.defalutParcelTypeId);
                this.updateCriteriaItem('parcelTypeDisplayOrder', this.selectedParcelType.key);
            }
            this.updateCriteriaItem('parcelValue', null);
            if(this.selectedPlanType['key'] !== undefined) {
                this.selectedPlanType = this.planTypeOptions.find((element,index) => element.key === this.defalutPlanTypeId);
                this.updateCriteriaItem('planTypeId', this.selectedPlanType.key);
            }
            this.updateCriteriaItem('planValue', null);
            this.updateCriteriaItem('secondParcelValue', null);
            this.updateCriteriaItem('block', null);
            this.updateCriteriaItem('maoriName', null);
            this.updateCriteriaItem('otherApp', null);
            this.isComplexSuffixSelected = false;
            this.isPartSelected = false;
        },
        clearAutoLegalDescriptionSearchForm() {
            this.autoLegalDescritionOptions = [];
            this.selectedAutoLegalDescription = [];
            this.isAutoLegalDescriptionLoading = false;
            this.updateCriteriaItem('legalDescriptionId', null);
            this.updateCriteriaItem('legalDescription', null);
        },
        clearLastNameOwnerForm() {
            if(this.selectedLandDistrict && this.selectedLandDistrict.length > 0) {
              this.selectedLandDistrict = [];
              this.updateCriteriaItem('landDistrictId', this.selectedLandDistrict);
            }
            this.updateCriteriaItem('lastName', null);
            this.updateCriteriaItem('firstName', null);
        },
        clearCorporateNameOwnerForm() {
            if(this.selectedLandDistrict && this.selectedLandDistrict.length > 0) {
              this.selectedLandDistrict = [];
              this.updateCriteriaItem('landDistrictId', this.selectedLandDistrict);
            }
            this.updateCriteriaItem('corporateName', null);
        },
        clearParcelID() {
            this.updateCriteriaItem('parcelId', null);
        },
        search() {
            if (!this.isValid || this.linzSearchLoading) {
                return;
            }

            if(this.selectedLinzSearchTab === 'legalDescriptionTab') {
                this.updateCriteriaItem('part', this.isPartSelected ? 1 : 0);
            }
            this.updateCriteriaItem('active', this.isLinzActiveSelected ? 1 : 0);
            this.updateCriteriaItem('inActive', this.isLinzInActiveSelected ? 1 : 0);
            this.updateCriteriaItem('noTitle', this.isLinzNoTitleSelected ? 1 : 0);
            this.updateCriteriaItem('isExternalUser', this.isExternalUser ? 1 : 0);
            // reset offset
            this.updateCriteriaItem('offset', 0);
            this.updateCriteriaItem('fetchRecordCount', 1);
            this.updateCriteriaItem('selectedLinzSearchTab', this.selectedLinzSearchTab);
            this.$emit('search');
            if(!this.isAutoLegalSearchEnabled) {
                this.autoLegalDescritionOptions = [];
                this.selectedAutoLegalDescription = [];
                this.isAutoLegalDescriptionLoading = false;
            }
        },
        allowOnlyWildCharacter(string) {
            return string.replace(/[^a-zA-Z0-9* ]/g, "").trim();
        },
        allowOnlySlashCharacter(string) {
            const updateString = string.replaceAll('.',',').trim();
            return updateString.replace(/[^a-zA-Z0-9/, ]/g, "").trim();
        },
        allowLegalDescriptionString(string) {
            return string.replace(/[^a-zA-Z0-9-,/&()!+:=. ]/g, "").trim();
        },
        allowOnlyNumbers(string) {
            return string.replace(/[^0-9]/g, "").trim();
        },
        isTitleStatusSelected() {
            return this.isLinzActiveSelected || this.isLinzInActiveSelected || this.isLinzNoTitleSelected;
        },
        updateLandDistrict: async function (event) {
            if (!this.selectedLandDistrict) {
              this.selectedLandDistrict = [];
            }

            if (this.selectedLandDistrict.filter((ld) => ld.key === event.key).length === 0) {
              this.selectedLandDistrict.push(event);
            }

            this.updateCriteriaItem('landDistrictId', this.selectedLandDistrict);
        },
        async removeLandDistrict(event) {
          this.selectedLandDistrict = this.selectedLandDistrict.filter(ld => ld.key !== event.key);
          this.updateCriteriaItem('landDistrictId', this.selectedLandDistrict);
        },
        updateParcelType: async function (event) {
            this.updateCriteriaItem('parcelTypeDisplayOrder', event.key);
        },
        updatePlanType: async function (event) {
            this.updateCriteriaItem('planTypeId', event.key);
        },
        autoLegalDescriptionRequest(filterText) {
            if(filterText.length < 1) {
                return;
            }
            if(filterText.length > 0) {
                let requestUrlAutoLegalDescription = jsRoutes.controllers.LinzSearchController.getLinzAutoSuggestLegalDescription().url;
                this.isAutoLegalDescriptionLoading = true;
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    data: JSON.stringify({"filterText": filterText}),
                    url: requestUrlAutoLegalDescription,
                    cache: false,
                    success: (response) => {
                        const reponseOpts = response.map((element, index) => {
                            return {value: element.textMatch, key: element.id};
                        });
                        this.autoLegalDescritionOptions = reponseOpts;
                        this.isAutoLegalDescriptionLoading = false;
                    },
                    error: (response) => {
                        console.error(response);
                    }
                });
            }
        },
        updateAutoLegalDescription: async function (event) {
            this.updateCriteriaItem('legalDescriptionId', event.key);
            this.updateCriteriaItem('legalDescription', event.value);
        },
        clearLastSearchResult() {
            this.$store.dispatch('linzSearch/clearLastResults');
        },
        fetchLandDistrict: async function (event) {
            const requestUrlLandDistrict = jsRoutes.controllers.LinzSearchController.getLinzLandDistrict().url;
            $.ajax({
                type: "GET",
                url: requestUrlLandDistrict,
                cache: false,
                success: (response) => {
                    const reponseOpts = response.map((element, index) => ({value: element.landDistrict, key: element.landDistrictId}));
                    this.landDistrictOptions = reponseOpts;
                    this.isLandDistrictLoading = false;
                },
                error: (response) => {
                    console.error(response);
                }
            });
        },
        fetchParcelType: async function (event) {
            const requestUrlParcelType = jsRoutes.controllers.LinzSearchController.getLinzParcelType().url;
            $.ajax({
                type: "GET",
                url: requestUrlParcelType,
                cache: false,
                success: (response) => {
                    const reponseOpts = response.map((element, index) => {
                        const optionElement =  {value: element.parcelTypeDisplayName, key: element.parcelTypeDisplayOrder};
                        if(optionElement.key === this.defalutParcelTypeId) {
                            this.selectedParcelType = optionElement;
                            this.updateCriteriaItem('parcelTypeDisplayOrder', optionElement.key);
                        }
                        return optionElement;
                    });
                    this.parcelTypeOptions = reponseOpts;
                    this.isParcelTypeLoading = false;
                },
                error: (response) => {
                    console.error(response);
                }
            });
        },
        fetchPlanType: async function (event) {
            const requestUrlParcelType = jsRoutes.controllers.LinzSearchController.getLinzPlanType().url;
            $.ajax({
                type: "GET",
                url: requestUrlParcelType,
                cache: false,
                success: (response) => {
                    const reponseOpts = response.map((element, index) => {
                        const optionElement =  {value: element.parentType, key: element.parentTypeId};
                        if(optionElement.key === this.defalutPlanTypeId) {
                            this.selectedPlanType = optionElement;
                            this.updateCriteriaItem('planTypeId', optionElement.key);
                        }
                        return optionElement;
                    });
                    this.planTypeOptions = reponseOpts;
                    this.isPlanTypeLoading = false;
                },
                error: (response) => {
                    console.error(response);
                }
            });
        },
        fetchUserGroup: async function (event) {
            if(this.isExternalUser) {
                const currentUserId = this.userId.split('|').pop();const self = this;
                const requestUrl = jsRoutes.controllers.LinzSearchController.getUserGroupId().url;
                $.ajax({
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    data: JSON.stringify({"userId": currentUserId}),
                    url: requestUrl,
                    cache: false,
                    success: (response) => {
                        let taGroup = 0;
                        if (response.taGroupId !== null) {
                            taGroup = response.taGroupId;
                        }
                        this.updateCriteriaItem('taGroup', taGroup);
                    },
                    error: (response) => {
                        console.error(response);
                    }
                });
            } else {
                this.updateCriteriaItem('taGroup', 0);
            }
        },
        filterByTitles: async function (filterValue) {
            if(filterValue !== null) {
                this.selectedLinzSearchTab = 'certificateTab';
                const self = this;
                setTimeout(function() {
                    self.updateCriteriaItem('titleReference', filterValue);
                    self.search();
                    self.$store.commit('linzSearch/setLinzFilterByTitles', null);
                },500);
            }
        }
    },
    mounted: function() {
        this.fetchLandDistrict();
        this.fetchParcelType();
        this.fetchPlanType();
        this.clearSearch();
        this.clearLastSearchResult();
        this.fetchUserGroup();
        this.filterByTitles(this.linzFilterByTitles);
    }
};
</script>

<style lang="scss" scoped>
.search-control-buttons {
  line-height: 3.5;
  margin-bottom: -3.5em;
  padding-right: 10px;
}

.sc-input {
  &--range {
    width: 45% !important;
  }
}

.status-actions {
  font-size: 1.1rem;
}
</style>

<style lang="scss" scoped="true" src="../rollMaintenance.scss"></style>
<style lang="scss" scoped="true" src="./linzSearchDashBoard.scss"></style>

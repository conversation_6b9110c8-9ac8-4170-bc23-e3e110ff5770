const DefaultRoute = {
    path: '',
    name: 'rating-valuation',
    component: () => import(/* webpackChunkName: "RatingValuation" */ './ratingValuation/Valuation.vue'),
};

const PropertyDetailsRoute = {
    path: 'property-details',
    name: 'rating-valuation-property-details',
    component: () => import(/* webpackChunkName: "RatingValuationPropertyDetails" */ './ratingValuation/ValuationPropertyDetails.vue'),
};

const ComparablesRoute = {
    path: 'comparables',
    name: 'rating-valuation-comparable-properties',
    component: () => import(/* webpackChunkName: "RatingValuationComparables" */ './ratingValuation/ValuationComparables.vue'),
};

const WorksheetRoute = {
    path: 'worksheet',
    name: 'rating-valuation-worksheet',
    component: () => import(/* webpackChunkName: "RatingValuationWorksheet" */ './ratingValuation/ValuationWorksheet.vue'),
};

const WriteUpRoute = {
    path: 'writeup',
    name: 'rating-valuation-writeup',
    component: () => import(/* webpackChunkName: "RatingValuationWriteup" */ './ratingValuation/ValuationWriteup.vue'),
};

export const ratingValuationRoute = {
    path: '/roll-maintenance/rating-valuation/:id',
    name: 'rating-valuation-job',
    component: () => import(/* webChunkName: "RatingValuationJob" */ './ratingValuation/ValuationConsentContext.vue'),
    children: [
        DefaultRoute,
        PropertyDetailsRoute,
        ComparablesRoute,
        WorksheetRoute,
        WriteUpRoute,
    ],
};

export const ratingValuationRoutes = {
    default: DefaultRoute,
    propertyDetails: PropertyDetailsRoute,
    comparables: ComparablesRoute,
    worksheet: WorksheetRoute,
    writeUp: WriteUpRoute,
};

const ObjectionPropertyDetailsRoute = {
    path: 'draft',
    name: 'rating-valuation-objection-draft',
    component: () => import(/* webpackChunkName: "Objection" */ './objections/step/DraftPropertyDetails.vue'),
}

const ObjectionComparablesRoute = {
    path: 'compare',
    name: 'rating-valuation-objection-compare',
    component: () => import(/* webpackChunkName: "Objection" */ './objections/step/ComparableSales.vue'),
}

const ObjectionWorksheetRoute = {
    path: 'valuation',
    name: 'rating-valuation-objection-valuation',
    component: () => import(/* webpackChunkName: "Objection" */ './objections/step/ValuationWorksheet.vue'),
}

const ObjectionCompleteRoute = {
    path: 'complete',
    name: 'rating-valuation-objection-complete',
    component: () => import(/* webpackChunkName: "Objection" */ './objections/step/JobCompletion.vue'),
}

const ObjectionActionRecordRoute = {
    path: 'action-record',
    name: 'rating-valuation-objection-action-record',
    component: () => import(/* webpackChunkName: "Objection" */ './objections/step/ActionRecord.vue'),
}

export const objectionRoute = {
    path: '/roll-maintenance/rating-valuation/:ratingValuationId/objection',
    component: () => import(/* webpackChunkName: "Objection" */ './objections/ValuationObjectionContext.vue'),
    children: [
        ObjectionPropertyDetailsRoute,
        ObjectionComparablesRoute,
        ObjectionWorksheetRoute,
        ObjectionCompleteRoute,
        ObjectionActionRecordRoute,
    ]
}

export const objectionRoutes = {
    propertyRoute: ObjectionPropertyDetailsRoute,
    comparableRoute: ObjectionComparablesRoute,
    worksheetRoute: ObjectionWorksheetRoute,
    writeupRoute: ObjectionCompleteRoute,
    actionRecordRoute: ObjectionActionRecordRoute,
}

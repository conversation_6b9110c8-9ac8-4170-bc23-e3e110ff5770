<script setup>
import ClassificationDropdown from '../../common/form/ClassificationDropdown.vue';
import Tooltip from '../../common/Tooltip.vue';
import { store } from '@/DataStore';
import { computed } from 'vue';

const props = defineProps({
    planScale: {
        type: Number,
        default: 0,
    },
    avgPixelLength: {
        type: Number,
        default: 0,
    },
    avgMetreLength: {
        type: Number,
        default: 0,
    },
    scales: {
        type: Array,
        default: () => [],
    },
    areas: {
        type: Array,
        default: () => [],
    },
});

const emit = defineEmits(['deleteShape', 'addScale', 'updateScale', 'renderCanvas']);
const selectedTool = computed(() => store.state.floorplan.selectedTool);
const disabledTool = computed(() => store.state.floorplan.disabledTool);

const planScaleExpression = computed(() => {
    if (!props.planScale) {
        if (props.scales.length) {
            return 'Fill out the length of each measurement in metres.';
        }
        return 'To measure floor areas, the scale needs to be defined.';
    }
    return scaleExpression(props.avgMetreLength, props.avgPixelLength);
});

function deleteShape(shapeId) {
    emit('deleteShape', shapeId);
}

function scaleExpression(metreLength, pixelLength) {
    if (typeof pixelLength !== 'number' || !pixelLength > 0) {
        return '';
    }
    if (typeof metreLength !== 'number' || !metreLength > 0) {
        return 'Enter a measurement in metres.';
    }
    return `1m = ${(pixelLength / metreLength).toFixed(5)}px; 1px = ${(metreLength / pixelLength).toFixed(5)}m`;
}

function noShapeExists(points){
    return !(points && Array.isArray(points) && points.length > 0);
}
</script>

<template>
    <div class="floorplan-control-panel">
        <div class="floorplan-panel control-panel">
            <div class="floorplan-panel-head">
                <div class="material-symbols-outlined">
                    straighten
                </div>
                <div>Scale</div>
                <div
                    class="floorplan-panel-buttons"
                >
                    <button
                        class="material-symbols-outlined"
                        :class="{'active': selectedTool === 'line' || disabledTool === 'line'}"
                        @click="emit('addScale')"
                    >
                        <span class="material-symbols-outlined">
                            add
                        </span>
                    </button>
                </div>
            </div>
            <div class="floorplan-panel-body">
                <div class="property-draft-section-row">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-sm-12">
                                <label>
                                    <span class="label">
                                        Floor Plan Scale
                                    </span>
                                    <input
                                        :value="planScaleExpression"
                                        disabled
                                        readonly
                                    >
                                </label>
                            </div>
                        </div>
                        <div
                            v-for="(scale, index) in scales"
                            :key="'scale_'+ index"
                            class="row"
                            :class="{ 'hover': scale.hover }"
                            @mouseenter="() => { scale.hover = true; emit('renderCanvas'); }"
                            @mouseleave="() => { scale.hover = false; emit('renderCanvas'); }"
                        >
                            <div class="col-lg-3 col-md-12">
                                <label>
                                    <span class="label">
                                        Measure {{ index + 1 }} (m)
                                    </span>
                                    <input
                                        v-model.number="scale.length"
                                        type="number"
                                        @input="emit('updateScale');"
                                    >
                                </label>
                            </div>
                            <div class="col-lg-7 col-md-8">
                                <label>
                                    <span class="label">
                                        Scale
                                    </span>
                                    <input
                                        :value="scaleExpression(scale.length, scale.pixelLength)"
                                        disabled
                                        readonly
                                    >
                                </label>
                            </div>
                            <div
                                class="col-lg-2 col-md-4"
                                style="text-align:right;padding-top:15px;"
                            >
                                <button
                                    class="button"
                                    @click="() => {
                                        deleteShape(scale.shapeId);
                                        emit('updateScale');
                                    }"
                                >   
                                    <span class="material-symbols-outlined">
                                        Delete
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="floorplan-panel control-panel">
            <div class="floorplan-panel-head">
                <div class="material-symbols-outlined">
                    space_dashboard
                </div>
                <div>Defined Areas</div>
                <div
                    class="floorplan-panel-buttons"
                >
                    <button
                        :class="{ 'active': selectedTool === 'polygon' || disabledTool === 'polygon' }"
                        @click="emit('addArea')"
                    >
                        <span class="material-symbols-outlined">
                            add
                        </span>
                    </button>
                </div>
            </div>
            <div class="floorplan-panel-body">
                <div class="property-draft-section-row">
                    <div class="container-fluid">
                        <div
                            v-for="(area, index) in areas"
                            :key="'area_'+ index"
                            class="row"
                            :class="{ 'hover': area.hover }"
                            @mouseenter="() => {
                                area.hover = true;
                                emit('renderCanvas');
                            }"
                            @mouseleave="() => {
                                area.hover = false;
                                emit('renderCanvas');
                            }"
                        >
                            <div class="col-lg-6 col-md-12">
                                <label>
                                    <span class="label">
                                        Description
                                    </span>
                                </label>
                                <classification-dropdown
                                    category="FloorAreaDescription"
                                    :value="area.description"
                                    :disabled="noShapeExists(area.points)"
                                    hide-codes
                                    @input="(input) => { area.description = input.value; }"
                                />
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <label>
                                    <span class="label">
                                        Area
                                    </span>
                                    {{ area.area.toFixed(2) }}m<sup>2</sup>
                                </label>
                            </div>
                            <div
                                class="col-lg-2 col-md-6"
                                style="text-align:right;padding-top:15px;"
                            >
                                <span class="label">
                                    &nbsp;
                                </span>
                                <button
                                    class="button"
                                    @click="deleteShape(area.shapeId)"
                                >
                                    <span class="material-symbols-outlined">
                                        delete
                                    </span>
                                </button>
                            </div>
                            <div
                                v-if="area.description.code === 'OTHER'"
                                class="col-sm-12"
                            >
                                <label>
                                    <span class="label">
                                        Other Description
                                    </span>
                                    <tooltip :text="area.validationError">
                                        <input
                                            v-model="area.otherDescription"
                                            :class="{ 'error': area.validationError }"
                                            type="text"
                                            maxlength="100"
                                            :disabled="noShapeExists(area.points)"
                                            :readonly="noShapeExists(area.points)"
                                        >
                                    </tooltip>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

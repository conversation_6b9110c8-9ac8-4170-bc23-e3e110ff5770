<script setup>
import { store } from '@/DataStore';
import { computed } from 'vue';

defineProps({
    selectedFloorPlan: {
        type: Object,
        default: null,
    },
    expanded: {
        type: Boolean,
        default: false,
    },
});

const floorPlans = computed(() => store.state.floorplan.floorPlans);
const resources = computed(() => store.state.floorplan.resources);
const listExpanded = computed(() => store.state.floorplan.listExpanded);


function imgSrc(resourceId) {
    const image = resources.value.find(i => i.resourceId === resourceId);
    if (image) {
        return `data:${image.content_type};base64,${image.data}`;
    }
    return '../../../../public/images/property/addPhotos.png';
}

function updateExpanded(expand) {
    store.commit('floorplan/setListExpanded', expand);
}

function getAreas(floorPlanAreas) {
    if (floorPlanAreas && floorPlanAreas.length) {
        return floorPlanAreas.filter(i => i.type === 'area');
    }
    return [];
}

function displayDescription(area) {
    if (!area || !area.description) {
        return '';
    }
    if (area.description.code === 'OTHER') {
        return area.otherDescription;
    }
    return area.description.description;
}

function displayArea(area) {
    if (!area || !area.area) {
        return '0';
    }
    return area.area.toFixed(2);
}
</script>

<template>
    <div
        v-if="floorPlans"
        class="floorplan-panel plan-list"
    >
        <div class="floorplan-panel-head">
            <div>Plans</div>
            <div class="floorplan-panel-buttons">
                <button
                    v-if="!listExpanded"
                    class="material-symbols-outlined"
                    @click="updateExpanded(true)"
                >
                    <span class="material-symbols-outlined">
                        expand_circle_right
                    </span>
                </button>
                <button
                    v-else
                    class="material-symbols-outlined"
                    @click="updateExpanded(false)"
                >
                    <span class="material-symbols-outlined flip-horizontal">
                        expand_circle_right
                    </span>
                </button>
            </div>
        </div>
        <div class="floorplan-panel-body">
            <div
                v-for="(floorPlan, index) in floorPlans"
                :key="floorPlan.thumbnailImgResourceId + '_thumbnail'"
                class="floorplan-list-item"
                :class="{ 'active': floorPlan.floorPlanId === selectedFloorPlan.floorPlanId }"
                @click="$emit('selectFloorPlan', index)"
            >
                <div
                    v-if="resources && resources.length"
                    class="container-fluid"
                >
                    <div class="row">
                        <div :class="{ 'col-lg-12': !listExpanded, 'col-lg-6': listExpanded }">
                            <img
                                :src="imgSrc(floorPlan.thumbnailImgResourceId)"
                                alt="thumbnail"
                            >
                            <span>{{ floorPlan.description }} </span>
                        </div>
                        <div
                            v-if="listExpanded"
                            class="col-lg-6"
                        >
                            <div class="container-fluid">
                                <div class="row">
                                    <div class="col-md-6 text-left">
                                        <span>Floor Plan ID:</span>
                                        <span>{{ floorPlan.floorPlanId }}</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 text-left">
                                        <span>Date:</span>
                                        <span>{{ floorPlan.date }}</span>
                                    </div>
                                    <div class="col-md-6 text-left">
                                        <span>Principal:</span>
                                        <span>{{ floorPlan.principal }}</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 text-left">
                                        <span>Building No:</span>
                                        <span>{{ floorPlan.buildingNo }}</span>
                                    </div>
                                    <div class="col-md-6 text-left">
                                        <span>Valued:</span>
                                        <span>{{ floorPlan.isValued }}</span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="container-fluid">
                                            <div class="row">
                                                <div class="col-md-6 text-left">
                                                    <strong>Space</strong>
                                                </div>
                                                <div class="col-md-6 text-right col-md-pull-3">
                                                    <strong>Area</strong>
                                                </div>
                                            </div>
                                            <div
                                                v-for="(area, areaIndex) in getAreas(floorPlan.areas)"
                                                :key="'listarea_' + areaIndex"
                                                class="row"
                                            >
                                                <div class="col-md-6 text-left">
                                                    {{ displayDescription(area) }}
                                                </div>
                                                <div class="col-md-6 text-right col-md-pull-2">
                                                    {{ displayArea(area) }}m<sup>2</sup>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

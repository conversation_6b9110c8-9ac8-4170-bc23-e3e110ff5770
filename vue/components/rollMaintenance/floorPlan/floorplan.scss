.router {

    .page-mask {
        position:fixed;
        top:0;
        left:0;
        height:100%;
        width:100%;
        background:rgba(220,220,220,0.5);
        z-index:500;

        .loadingSpinnerWrapper {
            position: relative;
            top:50%;
            left:50%;
        }

        .loadingSpinnerBox {
            position: relative;
            height:100px;
            width:100px;
            transform: translate(-50%, -50%);
            background:#FFFFFF;
            border:1px solid rgba(237,241,245,.75);
            border-radius:3px;

            .loadingSpinner {
                background-position: 50% 50%;
                background-size: 4rem;
                margin: 0.4rem 0 0 0;
                display:block;
            }
        }
    }
    
    label {
        font-size: 1.2rem;
        line-height: 3.0em;

        .label {
            display: block;
            font-size:10px;
            line-height: 1.5em;
        }

        input, textarea, select, .mx-datepicker input {
            height: 39px !important;
            padding: 0.5rem;
            border-radius: 5px;
            border: 1px solid #e8e8e8;
            background-color: #fff;
            margin: 0;
            width: 100%;

            &[readonly] {
                background-color: #eee;
            }
            
            &.error {
                outline: 3px solid var(--color-red-400);
                
                &:focus {
                    outline: 3px solid var(--color-red-400);
                }
            }
        }
    }

    &.floorplan-wrapper {
        height:calc(100vh - 11em);
        width:100%;
        display:inline-flex;
        flex-flow: row wrap;
        background:#FFF;

        button {
            height: 39px;
        }

        .QVHV-buttons {
            margin: 0;
            padding: 0;

            .primary {
                background-color: var(--color-blue-200);
                color: var(--qv-color-light);

                &:hover {
                    background-color: var(--color-blue-0);
                }
            }
            
            .qivs-link {
                background-color: var(--qv-color-orange);
                color: var(--qv-color-light);

                &:hover {
                    background-color: var(--qv-color-yellow);
                }
            }
        }

        .button {
            display:inline-block;
            padding: 5px;
            border: 1px #555 solid;
            border-radius: 3px;

            &.active {
                background: var(--color-blue-200);
                color: #FFF;
                border: 1px var(--color-blue-200) solid;
            }

            &:disabled {
                color: #CCC;
                border: 1px #CCC solid;
            }
            
            &:hover {
                cursor:pointer;
                background: var(--color-blue-0);
                color: #FFF;
                border: 1px var(--color-blue-0) solid;
                
                &:disabled {
                    cursor:auto;
                    background: #FFF;
                    color: #CCC;
                    border: 1px #CCC solid;
                }
            }
            
            &.material-symbols-outlined {
                min-width: 39px;
                font-variation-settings:
                'FILL' 0,
                'wght' 400,
                'GRAD' 0,
                'opsz' 48;                
            }
        }

        .floorplan-head {
            flex: 0 0 100%;
            height:0.2em;
            background:#FFF;

            button {
                height: 39px;
            }
        }

        .floorplan-list, .floorplan-canvas, .floorplan-control-panel {
            height: calc(100% - 7.8em);
        }

        .floorplan-list, .floorplan-canvas {
            height:100%;
            width:100%;

            canvas {
                height:100%;
                width:100%;
                background-image: linear-gradient(45deg, #e3dcd2 25%, transparent 25%), linear-gradient(-45deg, #e3dcd2 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e3dcd2 75%), linear-gradient(-45deg, transparent 75%, #e3dcd2 75%);
                background-size: 20px 20px;
                background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            }
        }

        .floorplan-control-panel {
            display:flex;
            flex-direction: column;
            flex:3;
            overflow-y: auto;
        }

        .floorplan-foot {
            flex: 0 0 100%;
            height:7em;
            padding:15px;
            border-top: 1px solid #DDD;
            box-shadow: 0px -5px 10px #ccc;

            .container-fluid {
                max-width: 144rem;
            }
        }

        &> .floorplan-panel {
            max-height: calc(100% - 7.8em);
        }

        .floorplan-panel {
            border-left:1px solid #FFF;
            border-right:1px solid #FFF;

            &-head {
                height: 2.5em;
                padding: 0 10px;
                background: var(--color-blue-800);
                font-size: 1.4em;
                font-weight: bold;
                line-height:2.4em;
                color: #FFF;
                border-top:1px solid #FFF;
                border-bottom:1px solid #FFF;

                div {
                    float:left;
                    margin:0 3px;
                    height:100%;
                    line-height:inherit;
                    font-weight:inherit;
                
                    &.material-symbols-outlined {
                        line-height:auto;
                        font-variation-settings:
                        'FILL' 0,
                        'wght' 400,
                        'GRAD' 0,
                        'opsz' 48
                    }
                }

                .floorplan-panel-buttons {
                    float:right;

                    button {
                        color: #FFF;
                        border: 1px solid transparent;
                        border-radius: 3px;
                        background: transparent;
                        margin:2px;
                        line-height:1.5em;
                        font-size:1.3em;
                        max-height: calc( 100% - 4px );

                        span {
                            &.flip-horizontal {
                                transform: scaleX(-1);
                            }
                        }

                        &:hover {
                            border: 1px solid #FFF;
                        }

                        &.active {
                            border: 1px solid #FFF;
                            color: var(--color-blue-800);
                            background: #FFF;
                        }
                    }
                }
            }

            &-body {
                height: calc(100% - 3.5em);
            }

            &.plan-list {
                flex:1.5;
                min-width:250px;
                flex-shrink: 1;

                &.expanded {
                    flex: 4;
                }

                .floorplan-panel-body {
                    overflow-y: auto;
                }
            }

            &.plan-canvas {
                flex:9;
                border-left:1px solid var(--color-blue-800);
                border-right:1px solid var(--color-blue-800);
            }

            &.control-panel {
                flex: 3;

                &:nth-child(2) {
                    flex: 6;
                }

                .row {
                    padding: 5px 0;
                    border-top:1px solid var(--color-lightblue-300);

                    &.hover {
                        background: var(--color-orange-100);
                    }
                    
                    &:hover {
                        background: var(--color-orange-100);
                    }
                }
                
                .row:first-child {
                    border-top:none;
                }
            }
            
        }

        .floorplan-list-item {
            padding:10px;
            width:auto;
            background:#FFF;
            border-bottom:#555 solid 1px;
            text-align:center;
    
            &:hover, &.active {
                cursor:pointer;
                background: #ff990f;
    
                img {
                    border:1px solid #000;
                    max-height:150px;
                    max-width:250px;
                }
            }
    
            span {
                padding:3px 0;
            }
    
            img {
                padding:3px;
                border:1px solid #000;
                width:auto;
                height:auto;
                margin:0 auto;
                max-height:80px;
                max-width:160px;
            }
        }
    }
}
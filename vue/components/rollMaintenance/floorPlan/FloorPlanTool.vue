<script setup>
import { store } from '@/DataStore';
import FloorPlanCanvas from './FloorPlanCanvas.vue';
import FloorPlanList from './FloorPlanList.vue';
import FloorPlanControlPanel from './FloorPlanControlPanel.vue';
import AlertModal from '../../common/modal/AlertModal.vue';
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router/composables';
import { initQivsLink } from '@/utils/CommonUtils.js';

const route = useRoute();
const qivsUrl = computed(() => store.state.userData.qivsUrl);
const canvas = ref(null);
const qpid = ref(route.params.qpid);
const selectedShape = ref(null);
const selectedFloorPlan = ref(null);
const originalFloorPlans = ref(null);
const avgMetreLength = ref(0);
const avgPixelLength = ref(0);
const modal = ref({
    mode: 'warning',
    isOpen: false,
    heading: 'heading',
    message: '',
    messages: [],
    cancelText: 'No',
    cancelAction: () => {
    },
    confirmText: 'Yes',
    confirmAction: () => {
    },
    code: '',
});
const floorPlans = computed(() => store.state.floorplan.floorPlans);
const loading = computed(() => store.state.floorplan.loading);
const saving = computed(() => store.state.floorplan.saving);
const warnings = computed(() => store.state.floorplan.warnings);
const errors = computed(() => store.state.floorplan.errors);
const listExpanded = computed(() => store.state.floorplan.listExpanded);
const selectedTool = computed(() => store.state.floorplan.selectedTool);
const disabledTool = computed(() => store.state.floorplan.disabledTool);
const userName = computed(() => store.state.userData.userName);
const isInternalUser = computed(() => store.state.userData.isInternalUser);
const classificationsLoaded = computed(() => store.getters.classificationsLoaded);
const loaded = computed(() => !loading.value && classificationsLoaded.value);
const currentFloorPlan = computed(() => {
    if (selectedFloorPlan.value === null) {
        return null;
    }
    return floorPlans.value[selectedFloorPlan.value];
});
const scales = computed(() => {
    if (currentFloorPlan.value) {
        return currentFloorPlan.value.areas.filter(i => i.type === 'scale');
    }
    return [];
});
const planScale = computed(() => {
    if (!avgMetreLength.value || !avgPixelLength.value) {
        return 0;
    }
    return avgMetreLength.value / avgPixelLength.value;
});
const areas = computed(() => {
    if (currentFloorPlan.value) {
        return currentFloorPlan.value.areas.filter(i => i.type === 'area');
    }
    return [];
});
// const floorPlansChanged = computed(() => JSON.stringify(floorPlans.value) !== JSON.stringify(originalFloorPlans.value));
const floorAreaDescriptions = computed(() => {
    if (!classificationsLoaded.value) {
        return [];
    }
    const classifications = store.getters.getCategoryClassifications('FloorAreaDescription');
    if (!classifications || !classifications.length) {
        return [];
    }
    return [...classifications];
});

watch(classificationsLoaded, () => {
    if (classificationsLoaded.value) {
        loadFloorPlans();
    }
});

watch(currentFloorPlan, handleFloorPlanChange);

function handleFloorPlanChange(newValue) {
    if (newValue?.floorPlanId !== undefined) {
        updatePlanScale();
    }
}

async function loadFloorPlans() {
    try {
        let floorPlanIndex = null;
        const { floorPlanId } = route.params;

        await store.dispatch('floorplan/getFloorPlans', qpid.value);
        mapFloorAreaDescriptions();
        originalFloorPlans.value = JSON.parse(JSON.stringify(floorPlans.value));

        if (floorPlans.value && floorPlans.value.length) {
            if (floorPlanId) {
                floorPlans.value.forEach((plan, index) => {
                    if (plan.floorPlanId === floorPlanId) {
                        floorPlanIndex = index;
                    }
                });
            }
        }

        selectedFloorPlan.value = floorPlanIndex || 0;

        updatePlanScale();
    }
    catch (error) {
        handleError(error);
    }
}

async function saveFloorPlans(ignoreWarnings = false) {
    try {
        const floorPlanSnapshot = JSON.parse(JSON.stringify(floorPlans.value));
        await store.dispatch('floorplan/saveFloorPlans', { floorPlans: floorPlans.value, username: userName.value });
        mapFloorAreaDescriptions();

        if (errors.value && errors.value.length) {
            setModal({
                mode: 'error',
                isOpen: true,
                heading: 'Save Floor Plans',
                message: 'There was a problem saving the floor plans',
                messages: errors.value.map(i => i.message),
                cancelText: null,
                cancelAction: () => {
                },
                confirmText: 'OK',
                confirmAction: () => {
                },
                code: 'SAVE_FLOOR_PLAN_ERROR',
            });
            return;
        }

        if (warnings.value && warnings.value.length && !ignoreWarnings) {
            setModal({
                mode: 'warning',
                isOpen: true,
                heading: 'Save Floor Plans',
                message: 'Are you sure?',
                messages: warnings.value.map(i => i.message),
                cancelText: 'No, Return to Measuring Tool',
                cancelAction: () => {
                },
                confirmText: 'Yes, Save Floor Plans',
                confirmAction: () => {
                    saveFloorPlans(true);
                },
                code: 'SAVE_FLOOR_PLAN_WARNING',
            });
            return;
        }

        setModal({
            mode: 'success',
            isOpen: true,
            heading: 'Saved Floor Plans',
            message: 'Success!',
            messages: [],
            cancelText: null,
            cancelAction: () => {
            },
            confirmText: 'OK',
            confirmAction: () => {
            },
            code: 'SAVE_FLOOR_PLAN_SUCCESS',
        });
        originalFloorPlans.value = floorPlanSnapshot;
    }
    catch (error) {
        handleError(error);
    }
}

// async function discardChanges(confirmed) {
//     try {
//         if (!confirmed && floorPlansChanged.value) {
//             this.setModal({
//                 mode: 'warning',
//                 isOpen: true,
//                 heading: 'Discard Changes',
//                 message: 'All your changes will be lost and will revert back to the last saved floor plans if any exists. Are you sure?',
//                 messages: [],
//                 cancelText: 'No, Return to Measuring Tool',
//                 cancelAction: () => {
//                 },
//                 confirmText: 'Yes, Discard Changes',
//                 confirmAction: () => {
//                     discardChanges(true);
//                 },
//                 code: 'DISCARD_CHANGES_WARNING',
//             });
//
//             return;
//         }
//
//         loadFloorPlans();
//     }
//     catch (error) {
//         handleError(error);
//     }
// }

function mapFloorAreaDescriptions() {
    if (floorPlans.value && floorPlans.value.length) {
        floorPlans.value.forEach((plan) => {
            plan.areas.forEach((area) => {
                if (area.type === null){
                    area.type = 'area';
                }
                if (area.type === 'area') {
                    const mappedDescription = floorAreaDescriptions.value
                        .find(i => i.code === area.description?.code || i.code === area.description);
                    if (!mappedDescription) {
                        if (area.description.constructor === String) {
                            area.otherDescription = area.description;
                        }
                        area.description = floorAreaDescriptions.value.find(i => i.code === 'OTHER');
                    }
                    else {
                        area.description = mappedDescription;
                    }
                }
                else if (area.type === 'scale'){
                    // force the existing scale to have a proper name
                    area.otherDescription = `scale ${area.shapeId}`;
                }
            });
        });
    }
}

function updatePlanScale() {
    if (selectedFloorPlan.value === null) {
        return;
    }

    const currentScales = currentFloorPlan.value?.areas.filter(i => i.type === 'scale' && i.length);
    if (currentScales && currentScales.length) {
        avgPixelLength.value = 0;
        avgMetreLength.value = 0;
        currentScales.forEach((scale) => {
            if (!scale.pixelLength) {
                scale.pixelLength = calculateLength(scale.points);
            }

            avgPixelLength.value += scale.pixelLength;
            avgMetreLength.value += scale.length;
        });
        avgPixelLength.value /= currentScales.length;
        avgMetreLength.value /= currentScales.length;
    } else {
        avgPixelLength.value = 0;
        avgMetreLength.value = 0;
    }
}

function calculateLength(points) {
    if (points && points.length === 2) {
        const x = points[0].x - points[1].x;
        const y = points[0].y - points[1].y;

        return Math.sqrt(x * x + y * y);
    }
    return null;
}

function selectFloorPlan(floorPlanIndex) {
    selectedFloorPlan.value = floorPlanIndex;
    updatePlanScale();
    canvas.value.renderCanvas();
}

function selectShape(index) {
    if (index === null) {
        selectedShape.value = null;
    }
    selectedShape.value = floorPlans.value[selectedFloorPlan.value].areas[index];
}

function handleError(error) {
    console.error(error);
    setModal({
        mode: 'error',
        isOpen: true,
        heading: 'An error occurred',
        message: `There was a problem while performing the requested action.\n\n${error}`,
        messages: [],
        cancelText: null,
        cancelAction: () => {
        },
        confirmText: 'OK',
        confirmAction: () => {
        },
        code: 'API_ERROR',
    });
}

function setModal(modalData) {
    modal.value = modalData;
}

function modalCancel() {
    modal.value.isOpen = false;
    modal.value.cancelAction();
}

function modalConfirm() {
    modal.value.isOpen = false;
    modal.value.confirmAction();
}
</script>

<template>
    <div class="floorplan-wrapper bootstrap-ify">
        <div
            v-if="!loaded || saving"
            class="page-mask"
            data-cy="rtvRuralIndexPageMask"
        >
            <div class="loadingSpinnerWrapper">
                <div
                    class="loadingSpinnerBox"
                    data-cy="rtvRuralIndexLoadingSpinner"
                >
                    <div class="loadingSpinner loadingSpinnerSearchResults" />
                </div>
            </div>
        </div>
        <template v-if="loaded && currentFloorPlan !== null">
            <div class="floorplan-head">
                &nbsp;
            </div>
            <floor-plan-list
                :selected-floor-plan="currentFloorPlan"
                class="floorplan-list"
                :class="{ 'expanded': listExpanded }"
                @selectFloorPlan="selectFloorPlan"
            />
            <div class="floorplan-panel plan-canvas">
                <div class="floorplan-panel-head">
                    &nbsp;
                </div>
                <div class="floorplan-panel-body">
                    <div class="floorplan-canvas">
                        <floor-plan-canvas
                            ref="canvas"
                            :floor-plan="currentFloorPlan"
                            :plan-scale="planScale"
                            @selectShape="selectShape"
                        />
                    </div>
                </div>
            </div>
            <floor-plan-control-panel
                v-if=isInternalUser
                :plan-scale="planScale"
                :avg-metre-length="avgMetreLength"
                :avg-pixel-length="avgPixelLength"
                :areas="areas"
                :scales="scales"
                @addScale="canvas.startLine()"
                @addArea="canvas.startShape()"
                @deleteShape="canvas.deleteShape($event)"
                @renderCanvas="canvas.renderCanvas()"
                @updateScale="updatePlanScale"
            />
            <div class="floorplan-foot">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-2 QVHV-buttons" v-if="isInternalUser">
                            <button
                                class="qivs-link"
                                @click="initQivsLink(qivsUrl, 'maintainFloorPlans', qpid)"
                            >
                                Maintain Plans ↗
                            </button>
                        </div>
                        <div
                            class="col-sm-1"
                            style="text-align:right;"
                        >
                            <button
                                class="button"
                                @click="canvas.zoom(true)"
                            >
                                <span class="material-symbols-outlined">
                                    add
                                </span>
                            </button>
                            <button
                                class="button"
                                @click="canvas.zoom()"
                            >
                                <span class="material-symbols-outlined">
                                    remove
                                </span>
                            </button>
                        </div>
                        <div
                            class="col-sm-6"
                            style="text-align:center;"
                            v-if="isInternalUser"
                        >
                            <button
                                :class="{ 'active': !selectedTool }"
                                class="button"
                                @click="canvas.deselectTool()"
                            >
                                <span class="material-symbols-outlined">
                                    arrow_selector_tool
                                </span>
                            </button>
                            <button
                                :class="{ 'active': selectedTool === 'move' }"
                                class="button"
                                @click="canvas.selectTool('move', 'toolbar')"
                            >
                                <span class="material-symbols-outlined">
                                    open_with
                                </span>
                            </button>
                            <button
                                :class="{
                                    'active': selectedTool === 'line'
                                        || disabledTool === 'line'
                                }"
                                class="button "
                                @click="canvas.startLine()"
                            >
                                <span class="material-symbols-outlined">
                                    straighten
                                </span>
                            </button>
                            <button
                                :class="{'active': selectedTool === 'polygon' || disabledTool === 'polygon'}"
                                class="button"
                                @click="canvas.startShape()"
                            >
                                <span class="material-symbols-outlined">
                                    space_dashboard
                                </span>
                            </button>
                            <button
                                class="button"
                                :disabled="selectedShape === null || selectedShape === undefined"
                                @click="canvas.deleteShape(selectedShape.shapeId)"
                            >
                                <span class="material-symbols-outlined">
                                    delete
                                </span>
                            </button>
                        </div>
                        <div class="col-sm-3 QVHV-buttons" v-if="isInternalUser">
                            <button
                                class="primary"
                                @click="saveFloorPlans()"
                            >
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
            data-cy="measuringToolModal"
        >
            <h1 data-cy="measuringToolModalHeading">
                {{ modal.heading }}
            </h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
                data-cy="measuringToolModalMessage"
            >
                {{ modal.message.trim() }}
            </p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div
                        class="message message-error"
                        :class="{
                            'message-error': modal.mode==='error',
                            'message-warning': modal.mode==='warning'
                        }"
                    >
                        <ul data-cy="measuringToolModalMessageList">
                            <li
                                v-for="(msg, index) in modal.messages"
                                :key="index"
                            >
                                - {{ msg }}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        data-cy="measuringToolModalCancelButton"
                        @click="modalCancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        data-cy="measuringToolModalConfirmButton"
                        @click="modalConfirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
                data-cy="measuringToolModalResponseCode"
            >
        </alert-modal>
    </div>
</template>

<style lang="scss" src="./floorplan.scss"></style>

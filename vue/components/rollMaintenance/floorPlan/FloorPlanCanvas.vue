<script setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { store } from '@/DataStore';

const props = defineProps({
    floorPlan: {
        type: Object,
        default: null,
    },
    planScale: {
        type: Number,
        default: 0,
    },
});

const emit = defineEmits(['selectShape', 'updateTool']);

let ctx;
let img;
const tools = [
    'move',
    'line',
    'polygon',
];

const canvasElement = ref(null);
const imageWidth = ref(0);
const imageHeight = ref(0);
const canvas = ref({
    width: 0,
    height: 0,
    offset: {
        x: 0,
        y: 0,
    },
    zoom: {
        level: 0,
        increment: 0.15,
        max: 20,
        min: 0,
    },
});
const toolSelector = ref(null);
const mouse = ref({
    currentPosition: {
        x: 0,
        y: 0,
    },
    startPosition: {
        x: 0,
        y: 0,
    },
});
const newShape = ref(null);
const selectedShape = ref(null);
const selectedPoint = ref(null);

const isInternalUser = computed(() => store.state.userData.isInternalUser);
const selectedTool = computed(() => store.state.floorplan.selectedTool);
const disabledTool = computed(() => store.state.floorplan.disabledTool);
const resources = computed(() => store.state.floorplan.resources);
const canvasCenter = computed(() => ({
    x: canvas.value.width / 2,
    y: canvas.value.height / 2,
}));
const imageScale = computed(() => ({
    x: canvas.value.width / imageWidth.value,
    y: canvas.value.height / imageHeight.value,
}));
const renderScale = computed(() => {
    const scaledWidthFitsCanvas = imageWidth.value
        * imageScale.value.y < canvas.value.width;
    const scaledHeightFitsCanvas = imageHeight.value
        * imageScale.value.x < canvas.value.height;

    if ((imageWidth.value > imageHeight.value && scaledHeightFitsCanvas)
        || !scaledWidthFitsCanvas) {
        return imageScale.value.x;
    }
    return imageScale.value.y;
});
const zoomScale = computed(() => 1 + (canvas.value.zoom.level * canvas.value.zoom.increment));
const floorPlanImage = computed(() => {
    const renderWidth = imageWidth.value * renderScale.value * zoomScale.value;
    const renderHeight = imageHeight.value * renderScale.value * zoomScale.value;

    return {
        position: {
            x: canvasCenter.value.x - renderWidth / 2 + canvas.value.offset.x,
            y: canvasCenter.value.y - renderHeight / 2 + canvas.value.offset.y,
        },
        center: {
            x: canvasCenter.value.x + canvas.value.offset.x,
            y: canvasCenter.value.y + canvas.value.offset.y,
        },
        width: renderWidth,
        height: renderHeight,
    };
});

watch(() => props.floorPlan, initializeFloorPlan);
watch(() => props.planScale, recalculateFloorPlanAreas);

onMounted(() => {
    ctx = canvasElement.value.getContext('2d');

    img = document.createElement('img');
    img.addEventListener('load', () => {
        imageWidth.value = img.width;
        imageHeight.value = img.height;
        renderCanvas();
    });

    window.addEventListener('resize', handleResize, true);
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    canvasElement.value.addEventListener('DOMMouseScroll', handleScroll);

    initializeFloorPlan();
});

onUnmounted(() => {
    window.removeEventListener('resize', handleResize, true);
    window.removeEventListener('keydown', handleKeyDown);
    window.removeEventListener('keyup', handleKeyUp);
});

defineExpose({
    startShape,
    deleteShape,
    renderCanvas,
    zoom,
    deselectTool,
    selectTool,
    startLine,
});

function handleResize() {
    renderCanvas();
}

function initializeFloorPlan() {
    if (props.floorPlan && img) {
        const image = resources.value.find(
            i => i.resourceId === props.floorPlan.imgResourceId,
        );
        if (typeof image !== 'undefined') {
            img.src = `data:${image.content_type};base64,${image.data}`;
        }
        initializeAllAreas();
        renderCanvas();
    }
}

function renderCanvas() {
    refreshCanvasDimensions();
    ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
    renderFloorplanImage();
    renderAreas();
}

function renderFloorplanImage() {
    ctx.drawImage(
        img,
        floorPlanImage.value.position.x,
        floorPlanImage.value.position.y,
        floorPlanImage.value.width,
        floorPlanImage.value.height,
    );
}

function renderAreas() {
    const sortFunction = (a, b) => {
        if (a.hover >= b.hover) {
            return 0;
        }
        if (a.selected > b.selected) {
            return 1;
        }
        return -1;
    };

    const renderShapes = [];
    props.floorPlan?.areas.forEach((area) => {
        renderShapes.push(area);
    });

    renderShapes.sort(
        (a, b) => sortFunction(a, b),
    ).forEach(
        area => area.draw(),
    );

    if (newShape.value) {
        // render incomplete shape
        const incompleteShape = JSON.parse(JSON.stringify(newShape.value));
        if (incompleteShape.points.length) {
            incompleteShape.points.push(
                translateCoordinates(mouse.value.currentPosition),
            );

            if (incompleteShape.type === 'scale') {
                renderLine(incompleteShape, true);
            }
            if (incompleteShape.type === 'area') {
                renderShape(incompleteShape, true);
            }
        }
    }
}

function renderLine(area, active = false) {
    if (!area.points || !area.points.length) {
        return false;
    }

    ctx.strokeStyle = '#3060d6';
    ctx.lineWidth = 2;
    if (area.hover || active) {
        ctx.strokeStyle = '#ffa223';
    }
    ctx.setLineDash([5, 3]);
    ctx.beginPath();

    const point1 = translateCoordinates(area.points[0], true);
    const point2 = translateCoordinates(area.points[1], true);

    ctx.moveTo(point1.x, point1.y);
    ctx.lineTo(point2.x, point2.y);

    ctx.stroke();

    if (area.hover || active) {
        renderLabel(area);
    }

    return true;
}

function renderShape(area, active = false) {
    if (!area.points || !area.points.length) {
        return false;
    }

    ctx.fillStyle = '#2550b844';
    ctx.strokeStyle = '#2550b899';
    if (area.hover || area.selected || active) {
        ctx.strokeStyle = '#ffa223FF';
        ctx.fillStyle = '#ffa22399';
        ctx.lineWidth = 4;
    }
    ctx.lineWidth = 2;
    ctx.setLineDash([]);
    ctx.beginPath();

    // render shape
    for (let i = 0; i < area.points.length; i++) {
        const point = translateCoordinates(area.points[i], true);
        if (i === 0) {
            ctx.moveTo(point.x, point.y);
        }
        else {
            ctx.lineTo(point.x, point.y);
        }
    }
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    // render points on shape (only visible for internal users)
    if (isInternalUser.value && (area.selected || active)) {
        for (let i = 0; i < area.points.length; i++) {
            const point = translateCoordinates(area.points[i], true);
            ctx.strokeStyle = '#df4b26';
            ctx.fillStyle = '#ffffff';
            ctx.lineJoin = 'round';
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.arc(
                point.x,
                point.y,
                3,
                0,
                2 * Math.PI,
                false,
            );
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
        }
    }

    if (area.hover || area.selected || active) {
        renderLabel(area);
    }

    return true;
}

function renderLabel(area) {
    let textToRender = null;
    if (area.length) {
        textToRender = `${area.length.toFixed(2)}m`;
    }
    if (area.area) {
        textToRender = `${area.area.toFixed(2)}m²`;
    }
    if (textToRender) {
        ctx.font = 'bold 14px "Open Sans", "Helvetica Neue", helvetica, sans-serif';
        ctx.fillStyle = 'white';
        ctx.strokeStyle = '#e58400FF';
        ctx.lineWidth = 4;
        ctx.setLineDash([]);
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        const center = translateCoordinates(
            calculateCenter(area.points),
            true,
        );
        if (center) {
            ctx.strokeText(textToRender, center.x, center.y);
            ctx.fillText(textToRender, center.x, center.y);
        }
    }
}

function refreshCanvasDimensions() {
    canvas.value.width = canvasElement.value.clientWidth;
    canvas.value.height = canvasElement.value.clientHeight;
}

function selectTool(tool, selector) {
    if (tools.includes(tool)) {
        if (selectedTool.value) {
            if (selectedTool.value === tool) {
                return false;
            }

            const disableToolWhileMoving = tool === 'move'
                && selector !== 'toolbar';

            if (disableToolWhileMoving) {
                store.commit('floorplan/setDisabledTool', selectedTool.value);
            }
        }

        store.commit('floorplan/setSelectedTool', tool);
        toolSelector.value = selector;

        canvasElement.value.style.cursor = selectedTool.value === 'move' ? 'move' : 'crosshair';
        if (selectedTool.value === 'move' && !disabledTool.value) {
            newShape.value = null;
        }
        if (['line', 'polygon'].includes(selectedTool.value)) {
            selectShape(null);
        }
    }
    emit('updateTool', { selected: selectedTool.value, disabled: disabledTool.value });

    return false;
}

function deselectTool() {
    if (!selectedTool.value) {
        return false;
    }

    store.commit('floorplan/setSelectedTool', disabledTool.value);
    store.commit('floorplan/setDisabledTool', null);
    canvasElement.value.style.cursor = selectedTool.value === 'move' ? 'move' : 'crosshair';
    if (!selectedTool.value) {
        canvasElement.value.style.cursor = 'auto';
        newShape.value = null;
        toolSelector.value = null;
    }
    emit('updateTool', { selected: selectedTool.value, disabled: disabledTool.value });

    return true;
}

function resetCanvas() {
    try {
        canvas.value.offset.x = 0;
        canvas.value.offset.y = 0;
        canvas.value.zoom.level = 0;
        ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
    }
    catch (err) {
        console.error('Error in resetCanvas', err);
    }
}

function handleMouseDown(evt) {
    try {
        mouse.value.startPosition.x = mouse.value.currentPosition.x;
        mouse.value.startPosition.y = mouse.value.currentPosition.y;

        if (isInternalUser.value && evt.which === 1) { // left mouse button
            if (selectedShape.value !== null) {
                const mousePosition = translateCoordinates(mouse.value.currentPosition);

                props.floorPlan?.areas[selectedShape.value].points.forEach(
                    (point, index) => {
                        if (
                            Math.abs(point.x - mousePosition.x) <= 3
                            && Math.abs(point.y - mousePosition.y) <= 3
                        ) {
                            selectedPoint.value = index;
                        }
                    },
                );

                if (selectedPoint.value !== null) {
                    selectTool('move', 'mouse');
                    return true;
                }
            }
        }
        if (evt.which === 3) { // right mouse button
            selectTool('move', 'mouse');
        }

        if (selectedTool.value === 'move') {
            selectShape(mouseIsInShape());
        }
    }
    catch (error) {
        console.error('Error in handleMouseDown event', error);
    }
    return false;
}

function handleMouseUp(evt) {
    try {
        if (isInternalUser.value && evt.which === 1) { // left mouse button
            if (['line', 'polygon'].includes(selectedTool.value)) {
                const newShapeHasPoints = newShape.value && newShape.value.points.length;

                const mouseIsOnLastPoint = newShapeHasPoints
                    && translateCoordinates(mouse.value.currentPosition).x
                    === newShape.value.points[newShape.value.points.length - 1].x
                    && translateCoordinates(mouse.value.currentPosition).y
                    === newShape.value.points[newShape.value.points.length - 1].y;

                if (!newShapeHasPoints || (newShapeHasPoints && !mouseIsOnLastPoint)) {
                    newShape.value.points.push(
                        translateCoordinates(mouse.value.currentPosition),
                    );
                }
                else {
                    newShape.value.points.splice(newShape.value.points.length - 1, 1);
                }

                if (selectedTool.value === 'line'
                    && newShape.value.points.length === 2) {
                    finishShape();
                    deselectTool();
                }
            }

            if (selectedPoint.value != null && props.floorPlan?.areas[selectedShape.value]) {
                selectedPoint.value = null;
                props.floorPlan.areas[selectedShape.value] = initializeArea(
                    props.floorPlan.areas[selectedShape.value],
                );
                if (selectedTool.value === 'move' && toolSelector.value !== 'toolbar') {
                    deselectTool();
                }
                renderCanvas();
                return true;
            }

            if (!selectedTool.value) {
                selectShape(mouseIsInShape());
            }
        }
        if (evt.which === 3) { // right mouse button
            if (selectedTool.value === 'move' && toolSelector.value !== 'toolbar') {
                deselectTool();
            }
        }

        renderCanvas();
    }
    catch (error) {
        console.error('Error in handleMouseUp event', error);
    }
    return false;
}

function handleMouseMove(evt) {
    try {
        mouse.value.currentPosition = getMousePosition(evt);

        if (selectedTool.value === 'move' && ([1, 3].includes(evt.which) || toolSelector.value === 'keyboard')) {
            const translate = {
                x: mouse.value.currentPosition.x - mouse.value.startPosition.x,
                y: mouse.value.currentPosition.y - mouse.value.startPosition.y,
            };

            if(isInternalUser.value){
                if (selectedPoint.value !== null) {
                props.floorPlan
                    .areas[selectedShape.value]
                    .points[selectedPoint.value]
                    .x += translate.x / zoomScale.value / renderScale.value;
                props.floorPlan
                    .areas[selectedShape.value]
                    .points[selectedPoint.value]
                    .y += translate.y / zoomScale.value / renderScale.value;
                }
                else if (selectedShape.value !== null) {
                    props.floorPlan.areas[selectedShape.value].points.forEach(
                        (point) => {
                            point.x += translate.x / zoomScale.value / renderScale.value;
                            point.y += translate.y / zoomScale.value / renderScale.value;
                        },
                    );
                }
                else {
                    canvas.value.offset.x += translate.x;
                    canvas.value.offset.y += translate.y;
                }
            }
        }

        props.floorPlan.areas.forEach(
            (area) => {
                area.hover = area.interior(
                    translateCoordinates(mouse.value.currentPosition),
                );
            },
        );

        mouse.value.startPosition.x = mouse.value.currentPosition.x;
        mouse.value.startPosition.y = mouse.value.currentPosition.y;

        renderCanvas();
    }
    catch (error) {
        console.error('Error in handleMouseMove event', error);
    }
}

function handleDoubleClick() {
    try {
        if (isInternalUser.value && selectedTool.value === 'polygon') {
            newShape.value.points.push(
                translateCoordinates(mouse.value.currentPosition),
            );

            finishShape();
            deselectTool();
        }

        renderCanvas();
    }
    catch (error) {
        console.error('Error in handleDoubleClick event', error);
    }
}

function handleScroll(evt) {
    try {
        const zoomIn = evt.deltaY < 0 || evt.detail < 0;
        const adjustOffset = zoom(zoomIn);
        if (adjustOffset) {
            const change = {
                x: (mouse.value.currentPosition.x - floorPlanImage.value.center.x)
                    * (zoomIn ? -1 : 1)
                    * (canvas.value.zoom.increment),
                y: (mouse.value.currentPosition.y - floorPlanImage.value.center.y)
                    * (zoomIn ? -1 : 1)
                    * (canvas.value.zoom.increment),
            };

            canvas.value.offset.x += change.x;
            canvas.value.offset.y += change.y;

            renderCanvas();
        }
    }
    catch (error) {
        console.error('Error in handleScroll event', error);
    }
    return false;
}

function handleKeyDown(evt) {
    try {
        if (evt.keyCode === 32) { // Space.
            return selectTool('move', 'keyboard');
        }
    }
    catch (error) {
        console.error('Error in handleKeyUp event', error);
    }
    return false;
}

function handleKeyUp(evt) {
    try {
        if (evt.keyCode === 13) { // Enter.
            if (isInternalUser.value && selectedTool.value === 'polygon'
                && newShape.value) {
                if (newShape.value.points.length > 2) {
                    finishShape();
                }
                deselectTool();
                renderCanvas();
            }
            return true;
        }

        if (evt.keyCode === 32) { // Space.
            if (selectedTool.value === 'move' && toolSelector.value === 'keyboard') {
                deselectTool();
            }
            return true;
        }
        if (evt.keyCode === 27) { // Escape.
            if (selectedTool.value) {
                deselectTool();
                renderCanvas();
                return true;
            }
            if (selectedShape.value !== null) {
                selectShape(null);
                renderCanvas();
                return true;
            }
        }
        if (evt.keyCode === 8 || evt.keyCode === 46) { // Backspace or Delete
            if (isInternalUser.value && selectedShape.value !== null) {
                deleteShape(props.floorPlan.areas[selectedShape.value].shapeId);
                renderCanvas();
                return true;
            }
            if (newShape.value && newShape.value.points.length) {
                newShape.value.points.splice(newShape.value.points.length - 1, 1);
                renderCanvas();
                return true;
            }
            if (newShape.value && !newShape.value.points.length) {
                deselectTool();
                renderCanvas();
                return true;
            }
        }
    }
    catch (error) {
        console.error('Error in handleKeyUp event', error);
    }
    return false;
}

function translateCoordinates(coordinates, forward = false) {
    if (forward) {
        return {
            x: coordinates.x
                * zoomScale.value
                * renderScale.value
                + floorPlanImage.value.center.x,
            y: coordinates.y
                * zoomScale.value
                * renderScale.value
                + floorPlanImage.value.center.y,
        };
    }
    return {
        x: (coordinates.x - floorPlanImage.value.center.x)
            / zoomScale.value
            / renderScale.value,
        y: (coordinates.y - floorPlanImage.value.center.y)
            / zoomScale.value
            / renderScale.value,
    };
}

function getMousePosition(evt) {
    const coords = { x: null, y: null };

    try {
        const rectangle = canvasElement.value.getBoundingClientRect();

        coords.x = parseFloat(evt.clientX - rectangle.left);
        coords.y = parseFloat(evt.clientY - rectangle.top);
    }
    catch (error) {
        console.error('Error while getting the mouse position from the canvas', error);
    }
    return coords;
}

function mouseIsInShape() {
    let shapeIndex = null;

    props.floorPlan.areas.forEach(
        (area, index) => {
            if (area.interior(
                translateCoordinates(mouse.value.currentPosition),
            )) {
                shapeIndex = index;
            }
        },
    );

    return shapeIndex;
}

function zoom(increaseZoom) {
    canvas.value.zoom.level += increaseZoom ? 1 : -1;
    if (canvas.value.zoom.level > canvas.value.zoom.max) {
        canvas.value.zoom.level = canvas.value.zoom.max;
        return false;
    }
    if (canvas.value.zoom.level < canvas.value.zoom.min) {
        canvas.value.zoom.level = canvas.value.zoom.min;
        return false;
    }
    renderCanvas();
    return true;
}

function startLine() {
    if (isInternalUser.value && selectedTool.value !== 'line') {
        selectTool('line');

        newShape.value = {
            type: 'scale',
            points: [],
        };
    }
}

function startShape() {
    if (isInternalUser.value && selectedTool.value !== 'polygon') {
        selectTool('polygon');

        newShape.value = {
            type: 'area',
            points: [],
        };
    }
}

function selectShape(shapeIndex) {
    selectedShape.value = shapeIndex;
    props.floorPlan.areas.forEach(
        (area, index) => {
            area.selected = index === shapeIndex;
        },
    );
    emit('selectShape', selectedShape.value);
}

function deleteShape(shapeId) {
    let shapeIndex = null;
    props.floorPlan.areas.forEach(
        (shape, index) => {
            if (shape.shapeId === shapeId) {
                shapeIndex = index;
            }
        },
    );
    if (shapeIndex !== null) {
        props.floorPlan.areas.splice(shapeIndex, 1);
    }
    selectShape(null);
    renderCanvas();
}

function finishShape() {
    if (newShape.value) {
        const minId = Math.min(...props.floorPlan.areas.map(i => i.shapeId));
        newShape.value.shapeId = (minId && minId < 0 ? minId : 0) - 1;
        const tempShape = initializeArea(
            Object.assign({ type: null, points: null }, newShape.value),
        );

        props.floorPlan.areas.push(tempShape);

        newShape.value = null;
        return true;
    }
    return false;
}

function calculateLength(points) {
    if (points && points.length === 2) {
        const x = points[0].x - points[1].x;
        const y = points[0].y - points[1].y;

        return Math.sqrt(x * x + y * y);
    }
    return null;
}

function calculateArea(points) {
    try {
        if (points.length > 2) {
            let sum = 0;

            for (let i = 0; i < points.length; i++) {
                const j = (i + 1) % points.length;

                sum += points[i].x * points[j].y
                    - points[i].y * points[j].x;
            }
            return Math.abs(sum) * 0.5;
        }
    }
    catch (error) {
        console.error('Error while calculating the area of the polygon', error);
    }
    return 0;
}

function calculateCenter(points) {
    if (points && points.length > 0) {
        const minX = Math.min(...points.map(i => i.x));
        const maxX = Math.max(...points.map(i => i.x));
        const minY = Math.min(...points.map(i => i.y));
        const maxY = Math.max(...points.map(i => i.y));

        const x = minX + ((maxX - minX) / 2);
        const y = minY + ((maxY - minY) / 2);
        return { x, y };
    }
    return null;
}

function initializeAllAreas() {
    if (!props.floorPlan.areas || !props.floorPlan.areas.length) {
        return;
    }
    props.floorPlan.areas.forEach(
        (area) => {
            initializeArea(area);
        },
    );
}

function initializeArea(area) {
    area.draw = area.type === 'scale' ? () => renderLine(area) : () => renderShape(area);
    area.hover = area.hover || false;
    area.selected = area.selected || false;
    area.description = area.description || {};
    area.otherDescription = area.otherDescription || null;
    if (area.type === 'scale') {
        area.pixelLength = calculateLength(area.points);
        area.length = area.length || 0;
        area.otherDescription = area.otherDescription || `scale ${area.shapeId}`;
        area.scale = () => area.length / area.pixelLength;
    }
    if (area.type === 'area'
        && area.points
        && area.points.length) {
        area.pixelArea = calculateArea(area.points);
        area.area = 0;
        if (props.planScale) {
            area.area = (Math.sqrt(area.pixelArea)
                * props.planScale) ** 2;
        }
    }
    const segments = [];

    for (let i = 0; i < area.points.length; i++) {
        const j = (i + 1) % area.points.length;
        const point1 = area.points[i];
        const point2 = area.points[j];

        if (point1.x !== point2.x) {
            segments.push(
                (x, y) => {
                    const t = (x - point1.x) / (point2.x - point1.x);
                    return (t >= 0) && (t < 1) && (y > t * point2.y + (1 - t) * point1.y);
                },
            );
        }
    }
    area.interior = (position) => {
        let z = 0;
        segments.forEach(
            (intersects) => {
                if (intersects(position.x, position.y)) {
                    z++;
                }
            },
        );
        return (z % 2) === 1;
    };

    return area;
}

function recalculateFloorPlanAreas() {
    if (!props.planScale || !props.floorPlan.areas) {
        return;
    }

    props.floorPlan.areas.forEach((area) => {
        if (area.points && area.points.length) {
            if (!area.pixelArea) {
                area.pixelArea = calculateArea(area.points);
            }

            area.area = (Math.sqrt(area.pixelArea) * props.planScale) ** 2;
        }
    });
}
</script>

<template>
    <canvas
        ref="canvasElement"
        :width="canvas.width"
        :height="canvas.height"
        @mousedown.prevent="handleMouseDown"
        @mousemove.prevent="handleMouseMove"
        @mouseup.prevent="handleMouseUp"
        @mousewheel.prevent="handleScroll"
        @dblclick.prevent="handleDoubleClick"
        @contextmenu.prevent="() => { return false; }"
    />
</template>

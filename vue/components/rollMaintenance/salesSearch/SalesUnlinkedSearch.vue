<script setup>
import TerritorialAuthority from '@/components/filters/TerritorialAuthority.vue';
import Paginate from 'Common/paginate/paginate.vue';
import SearchCriteriaHeader from 'Common/form/SearchCriteriaHeader.vue';
import SearchCriteriaForm from 'Common/form/SearchCriteriaForm.vue';
import InputNumber from 'Common/form/InputNumber.vue';
import InputLabel from 'Common/form/InputLabel.vue';
import InputDate from 'Common/form/InputDate.vue';
import MButton from 'Common/MButton.vue';
import useModal from '@/composables/useModal';
import { useSalePortalSearch } from '@/composables/useSalePortalSearch';
import { computed, onMounted, ref, watch } from 'vue';
import UnlinkedNoticeTable from '@/components/rollMaintenance/salesSearch/UnlinkedNoticeTable.vue';
import UnlinkedNoticeRow from '@/components/rollMaintenance/salesSearch/UnlinkedNoticeRow.vue';
import { linkNotice } from '@/services/ApiSalesController';
import { store } from '@/DataStore';

const STATE_INITIAL = 1;
const STATE_SEARCHING = 2;
const STATE_SEARCH_COMPLETE = 3;
const limit = ref(20);

const modal = useModal();
const { filter, clearFilter, searchTa } = useSalePortalSearch({ limit: limit.value });

const results = ref([]);
const total = ref(0);
const pageCount = computed(() => Math.ceil(total.value / limit.value));
const pageMin = computed(() => Math.max((filter.value.page - 1) * limit.value, 1));
const pageMax = computed(() => Math.min((filter.value.page - 1) * limit.value + limit.value, total.value));
const state = ref(STATE_INITIAL);
const showErrorModal = () => modal.showError('Error', 'Unexpected error occurred. Please try again.');

const allRatingAuthorities = computed(() => store.state.taCodes.allSelected);
const ratingAuthorityIds = computed(() => store.state.taCodes.taCodes.map(item => parseInt(item, 10)));

async function handleSearch() {
    filter.value.page = 1;
    await refreshResults();
}

async function refreshResults() {
    if (ratingAuthorityIds.value?.length <= 0 && !allRatingAuthorities.value) {
        return modal.showError('Error', 'Please select at least one territorial authority.');
    }

    state.value = STATE_SEARCHING;

    try {
        const res = await searchTa(ratingAuthorityIds.value, allRatingAuthorities.value);
        if (!res) {
            await showErrorModal();
            clear();
        }

        results.value = res.sales;
        total.value = res.total;
    } catch (error) {
        console.error(error);
        await showErrorModal();
        clear();
    }
    state.value = STATE_SEARCH_COMPLETE;
}

function clear() {
    results.value = []
    total.value = 0
    state.value = STATE_INITIAL
    clearFilter();
}

async function linkSalesNotice(payload) {
    try {
        const res = await linkNotice(payload);
        const body = await res.json();
        if (res.status !== 201) {
            console.error(body);
            const errorMessage = res.status === 400 ? body.message : 'Oops, something went wrong adding the sale.';
            return modal.showError('Error', errorMessage);
        }
        await refreshResults();
    } catch (error) {
        console.error(error);
        await showErrorModal();
    }
}
const tasLoaded = computed(() => store.state.taCodes.taCodesLoaded);
watch(tasLoaded, async (value) => {
    if (value) {
        await refreshResults();
    }
});
onMounted(async () => {
    if (filter.value.offset > pageCount.value) {
        filter.value.offset = 1;
    }
    if (tasLoaded.value) {
        await refreshResults();
    }
});
</script>
<template>
    <div class="qv-bg-light" data-cy="unlinked-search">
        <SearchCriteriaHeader>
            <SearchCriteriaForm class="qv-w-1/2 qv-gap-2">
                <InputLabel class="qv-ta-search-container" label="Territorial Authority" label-style="qv-color-light">
                    <TerritorialAuthority :validation-error="true" class="qv-form-select-ta-legacy" show-label="false" taId="advSearchTa" />
                </InputLabel>
                <div class="qv-flex-row">
                    <InputLabel label="Sale Input Date" label-style="qv-color-light">
                        <InputDate v-model="filter.saleDateFrom" />
                    </InputLabel>
                    <p class="qv-text-sm qv-color-light qv-align-self-end qv-mb-2">to</p>
                    <InputLabel class="qv-align-self-end" label="" label-style="qv-color-light">
                        <InputDate v-model="filter.saleDateTo" />
                    </InputLabel>
                </div>
                <InputLabel label="Job Number" label-style="qv-color-light">
                    <InputNumber
                        v-model="filter.jobNumber"
                        format="0"
                    />
                </InputLabel>
            </SearchCriteriaForm>
        </SearchCriteriaHeader>

        <UnlinkedNoticeTable :loading="state === STATE_SEARCHING">
            <UnlinkedNoticeRow data-cy="result-row" v-for="notice in results" :key="notice.noticeId" :notice="notice" @linkNotice="(payload) => linkSalesNotice(payload)" />
        </UnlinkedNoticeTable>

        <div class="qv-w-full qv-flex-column qv-justify-center qv-align-center qv-color-lightbuff qv-text-md qv-h-section" v-if="total === 0">
            <p v-if="state === STATE_SEARCHING">Searching...</p>
            <p v-else-if="state === STATE_SEARCH_COMPLETE">No Notices Found</p>
            <p v-else-if="state === STATE_INITIAL" data-cy="label-result-placeholder">Search for Sales Notices</p>
        </div>

        <div class="qv-px-2 qv-bg-light qv-border-top-1 qv-border-color-lightgray qv-position-sticky qv-bottom">
            <div class="qv-flex-row qv-justify-space-between" style="min-height: 6rem">
                <div class="qv-align-self-center">
                    <p v-if="total > 0" data-cy="label-result-count">Showing {{ pageMin }} to {{ pageMax }} of {{ total }} results found.</p>
                </div>
                <div class="qv-flex-grow">
                    <Paginate
                        v-if="total > 0"
                        v-model="filter.page"
                        :page-range="5"
                        :page-count="pageCount"
                        @change="refreshResults"
                    />
                </div>
                <div class="qv-align-self-center">
                    <div class="qv-flex-row qv-justify-end">
                        <MButton @click="clear" data-cy="button-clear">Clear</MButton>
                        <MButton class="mdl-button--colored" @click="handleSearch" data-cy="button-search">Search</MButton>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import SalesProcessingSearchCriteria from './SalesProcessingSearchCriteria.vue';
import SalesSearchResult from './SalesSearchResult.vue';
import paginate from '../../common/paginate/paginate.vue';
import SortHeader from '../../common/SortHeader.vue';
import { useSaleSearch } from '@/composables/useSaleSearch.js';
import { useRoute } from 'vue-router/composables';

const props = defineProps({
    searchType: {
        type: String,
    },
});


const route = useRoute();
const sortField = ref('SALE_DATE');
const direction = ref('DESC');
const shouldShowCheckbox = ref(false);

const {
    page,
    queryParams,
    searching,
    results,
    search,
    resetSearchCriteria,
    activeTab,
    isAdminUser,
    isCustomerCare,
} = useSaleSearch();

const totalPageCount = computed(() => Math.ceil(results.value.total / queryParams.value.limit));

async function onChangePage(newPage) {
    page.value = newPage;
    queryParams.value.offset = (newPage - 1) * queryParams.value.limit;
    await search();
}

async function sort(data) {
    direction.value = data.direction;
    sortField.value = data.columnName;
    queryParams.value.offset = 0;
    queryParams.value.sortBy = data.columnName;
    queryParams.value.sortDesc = data.direction === 'DESC' ? true : false;
    await search();
}

onMounted(() => {
    resetSearchCriteria(props.searchType);
});

watch(() => route.name, () => {
    resetSearchCriteria(props.searchType);
})
</script>

<template>
    <div>
        <sales-processing-search-criteria />
        <div class="resultsFound">
            <p v-if="results.total > 0">
                Showing {{ queryParams.offset + 1 | numeral }}
                to {{ (results.total > 100 && (queryParams.offset + 100) < results.total ? queryParams.offset + 100 : results.total) | numeral }}
                of {{ results.total | numeral }} results found.
            </p>
            <p v-else class="defaultSearchView"></p>
        </div>
        <div v-if="results.total > 0">
            <div class="paginator--top">
                <paginate v-if="!searching || queryParams.limit > 0" v-model="page" :page="page"
                          :page-count="totalPageCount" @change="onChangePage" data-cy="sp-sales-search-top-pagination" />
            </div>
            <div class="search-result">
                <div class="table">
                    <div class="sortRow roll-maintenance-activity-list__headerRow">
                        <div class="colHeader activity-list--select">
                            <input type="checkbox" v-if="shouldShowCheckbox" />
                        </div>
                        <div :class="sortField === 'ADDRESS' && 'active'" class="colHeader activity-list--address" data-cy="address-column-header">
                            <sort-header :direction="direction" :active="sortField === 'ADDRESS'" label="Address"
                                         column-name="ADDRESS" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'VALUATION_REFERENCE' && 'active'"
                             class="colHeader activity-list--valRef" data-cy="valuation-reference-column-header">
                            <sort-header :direction="direction" :active="sortField === 'VALUATION_REFERENCE'"
                                         label="Val Ref" column-name="VALUATION_REFERENCE" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'SALE_ID' && 'active'" class="colHeader activity-list--width-6" data-cy="sale-id-column-header">
                            <sort-header :direction="direction" :active="sortField === 'SALE_ID'" label="Sale Id"
                                         column-name="SALE_ID" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'SALE_DATE' && 'active'" class="colHeader activity-list--width-6" data-cy="sale-date-column-header">
                            <sort-header :direction="direction" :active="sortField === 'SALE_DATE'" label="Sale Date"
                                         column-name="SALE_DATE" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'NSP' && 'active'" class="colHeader activity-list--center" data-cy="nsp-column-header">
                            <sort-header :direction="direction" :active="sortField === 'NSP'" label="Net Sale Price"
                                         column-name="NSP" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'CHATTELS' && 'active'" class="colHeader activity-list--width-6" data-cy="chattels-column-header">
                            <sort-header :direction="direction" :active="sortField === 'CHATTELS'" label="Chattels"
                                         column-name="CHATTELS" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'CLASSIFICATION' && 'active'"
                             class="colHeader activity-list--width-6" data-cy="classification-column-header">
                            <sort-header :direction="direction" :active="sortField === 'CLASSIFICATION'"
                                         label="Classification" column-name="CLASSIFICATION" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'STATUS' && 'active'" class="colHeader activity-list--center" data-cy="status-column-header">
                            <sort-header :direction="direction" :active="sortField === 'STATUS'" label="Sale Status"
                                         column-name="STATUS" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'PROCESSING_STATUS' && 'active'"
                             class="colHeader activity-list--center" data-cy="processing-status-column-header">
                            <sort-header :direction="direction" :active="sortField === 'PROCESSING_STATUS'"
                                         label="Processing Status" column-name="PROCESSING_STATUS" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'LAND_AREA' && 'active'" class="colHeader activity-list--width-6" data-cy="land-area-column-header">
                            <sort-header :direction="direction" :active="sortField === 'LAND_AREA'"
                                         label="Land Area(Ha)" column-name="LAND_AREA" @onchange="sort" />
                        </div>
                        <div :class="sortField === 'CATEGORY' && 'active'" class="colHeader activity-list--center" data-cy="category-column-header">
                            <sort-header :direction="direction" :active="sortField === 'CATEGORY'" label="Category"
                                         column-name="CATEGORY" @onchange="sort" />
                        </div>
                    </div>
                    <sales-search-result
                        v-for="sale in results.sales"
                        :key="sale.saleId"
                        :sale="sale"
                        :active-tab="props.searchType"
                        :shouldShowCheckbox="shouldShowCheckbox"
                    />
                </div>
                <paginate v-if="!searching || queryParams.limit > 0" v-model="page" :page="page"
                          :page-count="totalPageCount" :paginateStyle="'qv-mt-10'" @change="onChangePage" data-cy="sp-sales-search-bottom-pagination" />
            </div>
        </div>
        <div v-if="!searching && results.total === 0 && results !== null" class="no-results"
             data-cy="salesSearchNoResult">
            <p>No records were found that match this criteria</p>
        </div>
        <div v-if="searching" class="results-loading">
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.disable-rows {
    opacity: 0.5;
    pointer-events: none;
}

.paginator--top {
    border-bottom: 1em solid white;
}

.no-results {
    text-align: center;
    font-size: 1.5em;
    padding: 1em 0;
}

.resultsFound {
    min-height: 10px;
}

.defaultSearchView {
    min-height: 60px;
}

.message {
    border: 1px solid transparent;
    border-radius: .25rem;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    margin-top: 0.5rem;
    font-size: 1.3rem;
}

.message-error {
    color: #721c24;
    border-color: #f5c6cb;
    background-color: #f8d7da;
}

.loadingSpinnerSearchResults {
    display: block;
    margin-top: 1.5rem;
}

.qv-mt-10 {
    margin-top: 10px !important;
}
</style>


import { computed } from 'vue';
import { store } from '@/DataStore';

const photoUrlMap = computed(() => store.state.propertyPhotos.photoUrlMap);

export function formatCurrency(value) {
    return '$' + value === null ? 0 : value?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

export function capitalise(words) {
    if (words?.trim()) {
        return words?.trim().split(' ').filter(word => word?.trim()).map(word => word[0].toUpperCase() + word.substring(1).toLowerCase()).join(' ');
    }
    return '';
}

export function getPhotoUrl(sale) {
    store.dispatch('propertyPhotos/getPropertyPhoto', sale.propertyId);
    if (!photoUrlMap.value) {
        return '';
    }
    const photo = photoUrlMap.value[sale.propertyId];
    return photo ? photo.smallImageUrl : store.state.propertyPhotos.noPhotoUrl;
}

export function formatFullAddress(sale) {
    const { situationNumber, additionalNumber, street, suburb, town, ratingAuthority } = sale;
    const suburbFormatted = capitalise(suburb);
    const townFormatted = capitalise(town);
    return `${situationNumber ? situationNumber : ''} ${additionalNumber && additionalNumber?.trim() ? additionalNumber + ' ' : ''}${capitalise(street)}, ${suburbFormatted ? suburbFormatted + ', ' : ''}${townFormatted ? townFormatted + ', ' : ''}${capitalise(ratingAuthority)}`;
}

export function formatAddressLineOne(sale) {
    return `${formatStreetAddress(sale)},`;
}

export function formatAddressLineTwo(property) {
    const { suburb, town } = property.address;
    const { name } = property.territorialAuthority
    const suburbFormatted = capitalise(suburb);
    const townFormatted = capitalise(town);
    return `${suburbFormatted ? suburbFormatted + ', ' : ''}${townFormatted ? townFormatted + ', ' : ''}${capitalise(name)}`;

}

export function formatStreetAddress(sale) {
    const { situationNumber, additionalNumber, street } = sale;
    return `${situationNumber ? situationNumber : ''} ${additionalNumber && additionalNumber?.trim() ? additionalNumber + ' ' : ''}${capitalise(street)}`;
}

<script setup>
import { formatFullAddress, getPhotoUrl } from './util.js';
import { formatDate, formatPrice } from '@/utils/FormatUtils';
import { RouterLink } from 'vue-router';
import { useSaleSearch } from '@/composables/useSaleSearch.js';

const props = defineProps({
    sale: { required: true },
    shouldShowCheckbox: { required: true }
});
const emit = defineEmits(['toggleResultView']);
const { activeTab, SALES_TO_PROCESS_TAB } = useSaleSearch();
</script>

<template>
    <div
        class="resultsRow activity-list__row"
        :class="{
            'sp-sale-to-process': sale.saleProcessingStatusId == 1 && activeTab !== SALES_TO_PROCESS_TAB,
            'sp-sale-autoProcessed': sale.saleProcessingStatusId == 3 && activeTab !== SALES_TO_PROCESS_TAB
        }"
        @click="emit('toggleResultView')"
    >
        <div class="colCell activity-list--select">
            <input v-if="shouldShowCheckbox" type="checkbox" />
        </div>
        <div class="colCell activity-list--address" data-cy="sp-address" @click.stop>
            <router-link class="row-link" :to="{ name: 'property', params: { qpid: sale.qpid } }" target="_blank">
                <span class="primaryThumb-Wrapper">
                    <img class="primaryPhoto_thumb" :src=getPhotoUrl(sale)>
                </span>
                <div class="activity-list--fullAddress"><span>{{ formatFullAddress(sale) }}</span></div>
            </router-link>
        </div>
        <div class="colCell colHeader activity-list--valRef" data-cy="sp-val-ref">
            {{ sale.rollNumber + '/' + sale.assessmentNumber }}{{ (sale.suffix !== null && sale.suffix !== '') ? '/' + sale.suffix :
                '' }}
        </div>
        <div class="colCell colHeader activity-list--width-6" data-cy="sp-sale-id">
            {{ sale.saleId }}
        </div>
        <div class="colCell colHeader activity-list--width-6" data-cy="sp-sale-date">
            {{ formatDate(sale.saleDate) }}
        </div>
        <div class="colCell activity-list--center" data-cy="sp-sale-price-net">
            {{ formatPrice(sale.salePriceNet, '$0,0') }}
        </div>
        <div class="colCell activity-list--width-6" data-cy="sp-chattels">
            {{ formatPrice(sale.chattels, '$0,0') }}
        </div>
        <div class="colCell colHeader activity-list--width-6" data-cy="sp-sale-classification">
            {{ sale.saleType + sale.saleTenure + sale.priceValueRelationship }}
        </div>
        <div class="colCell colHeader activity-list--center" data-cy="sp-sale-status">
            {{ sale.saleStatus }}
        </div>
        <div class="colCell colHeader activity-list--center" data-cy="sp-processing-status">
            {{ sale.saleProcessingStatus }}
        </div>
        <div class="colCell colHeader activity-list--width-6" data-cy="sp-land-area">
            {{ sale.area }}
        </div>
        <div class="colCell activity-list--center" data-cy="sp-category">
            {{ sale.categoryCode }}
        </div>
    </div>
</template>
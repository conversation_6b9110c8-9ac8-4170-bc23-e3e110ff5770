<script setup>
const props = defineProps(['loading']);
</script>

<template>
    <table class="qv-w-full qv-unlinked-table">
        <thead>
        <tr class="qv-unlinked-table-header">
            <th>
                <div>Val Ref</div>
            </th>
            <th>
                <div>TA</div>
            </th>
            <th>
                <div>Address</div>
            </th>
            <th>
                <div>Area (Ha)</div>
            </th>
            <th>
                <div>Title(s)</div>
            </th>
            <th>
                <div>Legal</div>
            </th>
            <th>
                <div>Owners</div>
            </th>
            <th>
                <div>Sale PDF #</div>
            </th>
            <th class="qv-text-center">
                <div>QPID</div>
            </th>
        </tr>
        </thead>
        <tbody :class="{ 'sp-saving-sale' : props.loading }">
        <slot />
        </tbody>
    </table>
</template>

<style src="./SalesSearchResult.scss" />

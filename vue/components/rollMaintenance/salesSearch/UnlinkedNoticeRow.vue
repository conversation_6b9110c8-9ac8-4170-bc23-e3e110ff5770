<script setup>
import { computed, ref } from 'vue';
import InputNumber from 'Common/form/InputNumber.vue';
import MButton from 'Common/MButton.vue';
import { formatStreetAddress } from '@/components/rollMaintenance/salesSearch/util';
import { openSalesNoticePreview } from '@/services/ApiSalesController';
import useModal from '@/composables/useModal';

const modal = useModal();

const props = defineProps({
    notice: {
        type: Object,
        required: true,
    },
});

const emit = defineEmits(['linkNotice']);

const address = computed(() => {
    const address = {
        situationNumber: props.notice.streetNumber,
        additionalNumber: props.notice.addnlStreetNumber,
        street: props.notice.streetName,
    };

    return formatStreetAddress(address);
});

const valRef = computed(() => {
    if (!props.notice.rollNumber || !props.notice.assessmentNumber) {
        return '';
    }

    return `${props.notice.rollNumber}/${props.notice.assessmentNumber} ${props.notice.suffix}`.trim();
});
const legals = computed(() => {
    return props.notice.titles
        .map((title) => title.legal)
        .filter((legal) => legal !== null && legal !== undefined)
        .reduce((acc, curr) => acc.concat(curr.split(',')), []);
});
const qpid = ref(null);

async function linkSaleNotice() {
    emit('linkNotice', {
        ...props.notice,
        qpid: qpid.value,
    });
}

async function openSalePdf() {
    try {
        await openSalesNoticePreview(props.notice.noticeId, 1);
    }
    catch (error) {
        console.error(error);
        await modal.showError('Failed to download PDF', 'Something went wrong retrieving the sales notice PDF');
    }
}
</script>

<template>
    <tr :class="{ 'qv-bg-red qv-color-light': notice.isMulti, 'qv-color-dark qv-bg-white': !notice.isMulti }" class="qv-text-sm qv-font-bold:hover qv-overlay:hover" style="position: relative;">
        <td>{{ valRef }}</td>
        <td>{{ notice.territorialAuthorityName }}</td>
        <td style="width: 20rem">{{ address }}</td>
        <td>{{ notice.landArea }}</td>
        <td>
            <ul>
                <li v-for="title in notice.titles" :key="title.title">{{ title.title }}</li>
            </ul>
        </td>
        <td style="max-width: 15rem">
            <ul class="qv-text-wrap">
                <li v-for="legal in legals" :key="legal">
                    {{ legal }}
                </li>
            </ul>
        </td>
        <td style="max-width: 15rem">
            <ul>
                <li v-for="owner in notice.previousOwners" :key="owner.firstName">{{ [owner.firstName, owner.secondName, owner.thirdName, owner.lastName].join(' ') }}</li>
            </ul>
        </td>
        <td>{{ notice.noticeId }}</td>
        <td>
            <div class="qv-flex-row qv-justify-end qv-mb-1">
                <InputNumber v-model="qpid" class="qv-color-dark" aria-placeholder="QPID" format="0" placeholder="QPID" />
                <MButton class="mdl-button--colored" @click="linkSaleNotice">Link</MButton>
            </div>
            <div class="qv-flex-row qv-justify-end">
                <MButton class="mdl-button--colored" @click="openSalePdf">View Sale PDF</MButton>
            </div>
        </td>
    </tr>
</template>

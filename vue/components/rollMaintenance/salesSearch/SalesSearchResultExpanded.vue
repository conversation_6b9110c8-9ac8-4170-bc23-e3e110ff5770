<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import { store } from '@/DataStore';
import { RouterLink } from 'vue-router';
import useExpandedSales from '@/composables/useExpandedSales.js';
import { useSaleSearch } from '@/composables/useSaleSearch.js';
import LoadingSpinner from 'Common/LoadingSpinner.vue';
import ConfirmationModal from '../../common/modal/dialog/ConfirmationModal.vue';
import RfsDetails from '@/components/rollMaintenance/propertySale/common/RfsDetails.vue';
import { formatAddressLineTwo } from './util.js';
import InputInlineLabel from 'Common/form/InputInlineLabel.vue';
import InputNumber from 'Common/form/InputNumber.vue';
import InputDate from 'Common/form/InputDate.vue';
import FormSelect from 'Common/form/FormSelect.vue';
import InputText from 'Common/form/InputText.vue';
import FormTextArea from 'Common/form/FormTextArea.vue';
import { useRouter } from 'vue-router/composables';
import { formatDate, formatPrice } from '@/utils/FormatUtils';
import AddSaleInspectionConsentModal from '@/components/rollMaintenance/propertySale/common/AddSaleInspectionConsentModal.vue';
import OwnershipUpdate from '../propertySale/common/OwnershipUpdate.vue';
import { getSalePortalSalePdfUrl } from '@/services/ApiSalesController';

const streetAddress = computed(() => property?.value?.address?.streetAddress ?? '');
const shouldShowAddConsentModal = ref(false);
const props = defineProps({
    saleId: {
        required: true,
    },
    qpid: {
        required: true,
    },
    activeTab: {
        required: true,
    },
});

const emit = defineEmits(['toggleResultView']);
const classifications = ref(null);
const loaded = ref(false);
const loadingMessage = ref('');
const router = useRouter();
const { openSalePdf, nspByRtv, saving, sale, dvr, titles, primaryProperty, qivsOwners, qivsOccupiers, loadSale, saveSale, deleteSale, relinkSaleRfs, getErrorsForLabel, propertyInfoFull, ownership, validationResult, failedInitialOwnershipValidation, toggleResultView, isSaleToProcess, linzWarnings } = useExpandedSales();
const {
    SALES_TO_PROCESS_CRITERIA,
    SALES_INSPECTION_CRITERIA,
} = useSaleSearch();

const qvMaintainsOwnershipData = computed(() => propertyInfoFull.value?.propertyInfo?.qvMaintainsOwnershipData);
const RURAL_CATEGORY = 'ADFHPS';

const user = computed(() => store.state.userData);
const qivsTitles = computed(() => titles?.value.map((t) => t.certificateOfTitle).join(', '));
const qivsTitleslegalDescription = computed(() => titles?.value.map((t) => t.legalDescription).join(', '));
const linzTitles = computed(() => sale.value?.linzSaleData?.titles?.map(t => t.title)?.join(', '));
const linzTitleslegalDescription = computed(() => sale.value?.linzSaleData?.titles?.map(t => t.legal)?.join(', '));
const linzSaleVendor = computed(() => sale.value?.linzSaleData?.previousOwners?.map(po => `${po.firstName ?? ''} ${po.lastName ?? ''}`.trim()) ?? []);
const linzSalePurchaser = computed(() => sale.value?.linzSaleData?.owners?.map(o => `${o.firstName ?? ''} ${o.lastName ?? ''}`.trim()) ?? []);


// TODO: Change to Linz sale notice Desc
const activeConsents = computed(() => propertyInfoFull.value?.activeConsents);
const property = computed(() => primaryProperty?.value?.property);
const isSaleTypeWarning = computed(() => sale.value.classifications.saleTypeId != 3);
const isAddSiConsentEnabled = computed(() => isS12OrM12?.value && isSaleProcessed?.value);
const isSaleProcessed = computed(() => [2, 3].includes(sale.value.monarchSaleProcessingStatusId));
const isS12OrM12 = computed(() => {
    const saleType = sale.value.classifications.saleTypeId;
    const saleTenureType = sale.value.classifications.saleTenureId;
    const priceValueRelationshipType = sale.value.classifications.priceValueRelationshipId;
    return [1, 3].includes(saleType)
      && saleTenureType === 1
      && priceValueRelationshipType === 2;
});
const isSalesInspection = computed(() => props.activeTab == SALES_INSPECTION_CRITERIA);
const isSalesToProcessTab = computed(() => props.activeTab == SALES_TO_PROCESS_CRITERIA);
const saleBtnLabel = computed(() => (sale?.value.monarchSaleProcessingStatusId == 1 ? 'CONFIRM SALE' : 'UPDATE SALE'));
const saleTypeWarningMessage = 'WARNING: Click on the \'Sale\' link to edit the Sale Details';
const ownershipWarningMessage = 'WARNING: Click on the \'QIVS\' link to edit the Ownership Details';
const linzAreaWarning = 'WARNING: Land area differs from sale notice';
const linzTitleWarning = 'WARNING: Please check the Titles involved in the sale';
const linzMultipleTaWarning = 'WARNING: Multiple TAs involved in the sale';
const shouldShowDeleteConfirmationModal = ref(false);
const shouldShowUpdateConfirmationModal = ref(false);
const isRfcOutputValid = computed(() => parseInt(sale.value?.rfc?.output) >= 0);
const isRfcSourceValid = computed(() => sale.value?.rfc?.source);
const isRfcReasonValid = computed(() => sale.value?.rfc?.reason);
const isRfcValid = computed(() => isRfcOutputValid.value && isRfcSourceValid.value && isRfcReasonValid.value);
const relinkFailureMessage = ref(null);
const showRelinkSuccessMessage = ref(false);
const shouldDeleteConfirmBeDisabled = computed(() => !isRfcValid.value || saleRfsStatus.value === 1);
const saleHasRfs = computed(() => sale.value?.rfs?.number);
const saleRfsStatus = computed(() => sale.value?.rfs?.status);
const saleRfsStatusText = computed(() => {
    if (sale.value?.rfs?.status === 1) {
        return 'OPEN';
    }
    if (sale.value?.rfs?.status === 2) {
        return 'CLOSED';
    }
    if (sale.value?.rfs?.status === 0) {
        return 'N/A';
    }
    return 'unknown';
});
const netPrice = computed(() => calculateNetSalePrice());
const crossReferencedQpids = computed(() => sale?.value.crossReferencedProperties.map((p) => p.qpid).join(', '));

const propertyOwners = computed(() => {
    const results = [];
    results.push(...(qivsOwners.value.length ? qivsOwners.value.map((owner) => owner.fullName) : qivsOccupiers.value.map((occupier) => occupier.fullName)));
    return results;
});

const saleAnalysis = computed(() => (isRural(sale.value.propertyInfo.category?.code) ? (sale.value.canBeAnalysed ? boolToYN(sale.value.hasAnalysis) : 'NA') : boolToYN(sale.value.hasAnalysis)));
const isAnyExistingQivsOwnerNonOccupier = computed(() => propertyInfoFull.value?.owners?.some(owner => owner.type !== 'Occupier'));
const isAnyExistingQivsOwnerHavingMultipleProperties = computed(() => propertyInfoFull.value?.owners?.some(owner => owner.numberOfActiveProperties > 1));
//const isSaleToProcess = computed(() => sale.value?.monarchSaleProcessingStatusId === 1);
const shouldOwnersBeUpdatedInQivs = computed(() => failedInitialOwnershipValidation.value || isAnyExistingQivsOwnerNonOccupier.value || isAnyExistingQivsOwnerHavingMultipleProperties.value);
const shouldShowOwnershipSection = computed(() => isSalesToProcessTab.value && qvMaintainsOwnershipData.value);
const shouldShowOwnershipWarning = computed(() => shouldOwnersBeUpdatedInQivs.value && shouldShowOwnershipSection.value);
const isOwnershipSectionReadOnly = computed(() => shouldOwnersBeUpdatedInQivs.value && !isSaleToProcess.value);

const isActionButtonDisabled = computed(() => isSaleTypeWarning.value || (shouldShowOwnershipWarning.value));
const isDeleteSaleDisabled = computed(() => isActionButtonDisabled.value);
const isUpdateSaleDisabled = computed(() => isActionButtonDisabled.value);
const isInvalidImpValue = computed(() => {
    const capitalValue = dvr?.value?.currentValuation?.capitalValue;
    const landvalue = dvr?.value?.currentValuation.landValue;
    return capitalValue < landvalue;
});
const viewAndScope = computed(() => {
    const viewId = dvr?.value?.site?.view;
    const viewNone = dvr?.value?.site?.view === 1;
    const view = dvr?.value?.site?.viewDesc;
    const scopeId = dvr?.value?.site?.viewScope;
    const scopeNone = dvr?.value?.site?.viewScope === 2;
    const scope = dvr?.value?.site?.viewScopeDesc;
    const shouldReturnDash = !viewId || !scopeId;
    const shouldReturnNone = viewNone || scopeNone;
    if (shouldReturnDash) {
        return '-';
    }
    if (shouldReturnNone) {
        return 'None';
    }
    return `${view} / ${scope}`;
});
const salePdfUrl = ref(null);
const production = computed(() => {
    const productionNumber = parseFloat(dvr?.value?.landUseData?.production);
    if (isNaN(productionNumber)) {
        return '-';
    }
    return productionNumber;
});

function trySaveSale() {
    saveSale({ isSavingOwnershipData: shouldShowOwnershipSection.value });
}

function isRural(category) {
    return RURAL_CATEGORY.indexOf(category?.charAt(0)) != -1;
}

function boolToYN(value) {
    return value ? 'YES' : 'NO';
}

watch(
    () => netPrice.value,
    () => {
        sale.value.price.net = netPrice.value ?? 0;
    }
);

watch(
    () => toggleResultView.value,
    () => {
        if(toggleResultView.value) {
            emit('toggleResultView');
        }
    }
);

watch(
    () => sale.value,
    (newSale) => {
        if (newSale) {
            sale.value.price.net = calculateNetSalePrice() ?? 0;
        }
    },
    { immediate: true }
);

onMounted(() => {
    classifications.value = store.state.saleClassifications.classifications;
    loadSaleDetails();
});

function calculateNetSalePrice() {
    return sale?.value?.price.gross - (sale?.value?.price.chattels + sale?.value?.price.other + sale?.value?.price.gst);
}

function viewSalePdf() {
    if (!salePdfUrl.value) {
        return;
    }
    openSalePdf(sale.value.salesDirectId, sale.value.saleSource === 'LINZ' ? 1 : 2);
}

function viewSale() {
    const url = router.resolve({
        name: 'property-sale',
        params: {
            id: sale.value.saleId,
            qpid: sale.value.qpid,
        },
    });
    window.open(url.href, '_blank');
}

function openQivs() {
    const qivsUrl = user?.value?.qivsUrl;
    const url = `${qivsUrl}/default.asp?Property/masterdetails.aspx?Qpid=${property?.value?.qupid}`;
    window.open(url, '_blank');
}

function webSearch() {
    const url = `https://google.co.nz/search?near=New+Zealand&q=${property?.value?.address.streetAddress}+${formatAddressLineTwo(property.value)}`;
    window.open(url, '_blank');
}

function handleDeleteConfirmationClose() {
    shouldShowDeleteConfirmationModal.value = false;
    relinkFailureMessage.value = null;
    showRelinkSuccessMessage.value = false;
}

async function relinkSale() {
    const result = await relinkSaleRfs();
    relinkFailureMessage.value = null;
    if (result?.status === 'INVALID') {
        relinkFailureMessage.value = result.message;
    } else if (result?.status === 'UPDATED') {
        showRelinkSuccessMessage.value = true;
        await loadSaleDetails();
    } else {
        relinkFailureMessage.value = 'Oops, something went wrong!';
    }
}

async function loadSaleDetails() {
    loadingMessage.value = 'Loading sale...';
    await loadSale(props.qpid, props.saleId), (loaded.value = true);
    getSalePdfUrl();
}

async function getSalePdfUrl() {
    if (!sale.value.salesDirectId || !['LINZ', 'SalesDirect'].includes(sale.value.saleSource)) {
        return;
    }
    const { pdfUrl } = await getSalePortalSalePdfUrl(sale.value.salesDirectId, sale.value.saleSource === 'LINZ' ? 1 : 2);
    salePdfUrl.value = pdfUrl;
}
const preferM2OverHa = computed(() => dvr.value?.site?.landArea < 1);
const landNetRate = computed(() => dvr.value?.currentValuation?.netRate?.landValue * (preferM2OverHa.value ? 1 : 10000));

</script>

<template>
    <div style="position: relative">
        <div v-if="!loaded" key="1" class="col-container" style="padding-top: 15rem; padding-bottom: 15rem">
            <loading-spinner :message="loadingMessage" />
        </div>
        <div v-else>
            <div :key="sale.saleId" class="sp-result-container mdl-shadow--2dp" :class="{ 'sp-saving-sale' : saving }" :id="'results-' + property.qupid">
                <div class="sp-top-section">
                    <div class="qv-w-half">
                        <router-link class="row-link" :to="{ name: 'property', params: { qpid: property.qupid } }" target="_blank">
                                    <div id="'full-dddress-div_'+property.qupid" class="sp-full-address">
                                        <span class="sp-address">{{ `${property.address.streetAddress},` }}</span>
                                        <span class="qv-text-base qv-font-semibold" data-cy="sp-expanded-address">{{ formatAddressLineTwo(property) }}</span>
                                        <div class="qv-flex-column qv-flex-flow sp-pd-container">
                                            <div class="sp-prop-detail qv-ellipsis"><span class="qv-font-normal" data-cy="sp-expanded-qpid">QPID: </span>{{ property.qupid }}</div>
                                            <div class="sp-prop-detail qv-ellipsis"><span class="qv-font-normal" data-cy="sp-expanded-sale-id">Sale Id: </span>{{ sale.saleId }}</div>
                                            <div class="sp-prop-detail qv-ellipsis"><span class="qv-font-normal" data-cy="sp-expanded-analysis">Analysis: </span>{{ saleAnalysis }}</div>
                                            <div class="sp-prop-detail qv-ellipsis"><span class="qv-font-normal" data-cy="sp-expanded-source">Source: </span>{{ sale.saleSource }}</div>
                                            <div class="sp-prop-detail qv-ellipsis"><span class="qv-font-normal" data-cy="sp-expanded-val-ref">Val Ref: </span>{{ `${property.rollNumber ? property.rollNumber : ''}/${property.assessmentNumber ? property.assessmentNumber : ''} ${property.suffix ? property.suffix : ''}` }}</div>
                                            <div class="sp-prop-detail qv-ellipsis"><span class="qv-font-normal" data-cy="sp-expanded-portal-id">Portal Id: </span>{{ sale.salesDirectId }}</div>
                                            <div class="sp-prop-detail qv-ellipsis" id="sale-status-div" :class="[sale.saleStatus == 'Pending' ? 'sale-status-pending' : sale.saleStatus == 'Unconfirmed' ? 'sale-status-unconfirmed' : '']"><span class="qv-font-normal" data-cy="sp-expanded-status">Status: </span>{{ sale.saleStatus }}</div>
                                            <div
                                                class="sp-prop-detail qv-ellipsis"
                                                :class="{
                                                    'sp-pvr-one': sale.classifications.priceValueRelationshipId == '1',
                                                    'sp-pvr-two': sale.classifications.priceValueRelationshipId == '2',
                                                    'sp-pvr-three': sale.classifications.priceValueRelationshipId == '3',
                                                }"
                                            >
                                                <span class="qv-font-normal"  data-cy="sp-expanded-sale-classification">Classification: </span>{{ `${sale.classifications.saleType}${sale.classifications.saleTenure}${sale.classifications.priceValueRelationship}` }}
                                            </div>
                                        </div>
                                    </div>
                        </router-link>
                    </div>
                    <div :data-id="sale.saleId" style="padding-top: 4.55rem;" class="qv-w-half">
                        <div class="sp-flex-right">
                            <button v-if="isSaleTypeWarning" class="sp-action-button sp-md-warning">{{ saleTypeWarningMessage }}</button>
                            <button v-if="shouldShowOwnershipWarning" class="sp-action-button sp-md-warning" @click="openQivs">{{ ownershipWarningMessage }}</button>
                            <button v-if="linzWarnings.landAreaWarning" class="sp-action-button sp-md-warning">{{ linzAreaWarning }}</button>
                            <button v-if="linzWarnings.titleWarning" class="sp-action-button sp-md-warning">{{ linzTitleWarning }}</button>
                            <button v-if="linzWarnings.multipleTaWarning" class="sp-action-button sp-md-warning">{{ linzMultipleTaWarning }}</button>
                        </div>
                        <div class="sp-flex-right">
                            <button data-cy="sp-sale-inspection-btn" class="sp-action-button sp-add-consent" :disabled="!isAddSiConsentEnabled" :class="{ 'sp-btn-disabled': !isAddSiConsentEnabled }" v-if="isSalesInspection" @click="shouldShowAddConsentModal = true">ADD 'SI' CONSENT</button>
                            <button data-cy="sp-delete-sale-btn" class="sp-action-button sp-delete-sale" :disabled="isDeleteSaleDisabled" :class="{ 'sp-btn-disabled': isDeleteSaleDisabled }" @click="shouldShowDeleteConfirmationModal = true">DELETE SALE</button>
                            <button data-cy="sp-update-sale-btn" class="sp-action-button sp-confirm-sale" :disabled="isUpdateSaleDisabled" :class="{ 'sp-btn-disabled': isUpdateSaleDisabled }" @click="trySaveSale">{{ saleBtnLabel }}</button>
                        </div>
                    </div>
                </div>
                <div class="sp-property-details-wrapper" data-cy="sp-expanded-property-banner">
                    <div class="qv-w-7/100 sp-property-detail" :class="{'sp-prop-failed-validation': (dvr.currentValuation?.capitalValue || 0) == 0}" data-cy="sp-expanded-capital-value">
                        <span class="sp-prop-heading">Capital Value</span>{{ `$${dvr.currentValuation?.capitalValue || 0}` }}
                        <div class="sp-prop-val" v-if="(dvr.currentValuation.netRate ?? null) !== null">
                            {{ formatPrice(Math.round(dvr.currentValuation.netRate.capitalValue)) }}
                            <span class="sp-prop-val-sm">/ m<sup class="sup">2</sup></span>
                        </div>
                    </div>
                    <div class="qv-w-7/100 sp-property-detail" data-cy="sp-expanded-land-value">
                        <span class="sp-prop-heading">Land Value</span>{{ `$${dvr.currentValuation.landValue}` }}
                        <div class="sp-prop-val" v-if="(dvr.currentValuation.netRate ?? null) !== null">
                            {{ formatPrice(Math.round(landNetRate)) }}
                            <span class="sp-prop-val-sm" v-if="preferM2OverHa">/ m<sup class="sup">2</sup></span>
                            <span class="sp-prop-val-sm" v-else>/ Ha</span>
                        </div>
                    </div>
                    <div class="qv-w-10/100 sp-property-detail" :class="{'sp-prop-failed-validation': isInvalidImpValue}" data-cy="sp-expanded-value-of-imp">
                        <span class="sp-prop-heading">Value of Improvements</span>{{ `$${dvr.currentValuation.valueOfImps}` }}
                        <div class="sp-prop-val" v-if="(dvr.currentValuation.netRate ?? null) !== null">
                            {{ formatPrice(Math.round(dvr.currentValuation.netRate.valueOfImps)) }} <span class="sp-prop-val-sm">/ m<sup class="sup">2</sup></span>
                        </div>
                    </div>
                    <div class="qv-w-14/100 sp-property-detail" data-cy="sp-expanded-land-area-TFA-TLA" :class="{'sp-prop-failed-validation': linzWarnings.landAreaWarning}">
                        <span class="sp-prop-heading">Land Area (Ha)</span>{{ dvr.site.landArea }}
                        <div class="sp-prop-val">
                            <span class="tfa-tla sp-prop-val-sm"
                                >TFA <span>{{ `${dvr.summary.totalFloorArea || 0} m` }}</span
                                ><sup class="sup">2</sup></span
                            >
                            <span class="tfa-tla sp-prop-val-sm"
                                >TLA <span>{{ `${dvr.summary.totalLivingArea || 0} m` }}</span
                                ><sup class="sup">2</sup></span
                            >
                        </div>
                    </div>
                    <div class="qv-w-6/100 sp-property-detail"><span class="sp-prop-heading" data-cy="sp-expanded-nsp-by-cv">NSP/CV</span>{{ sale.nspByCV }}</div>
                    <!-- TODO: After RTV -->
                    <div class="qv-w-6/100 sp-property-detail"><span class="sp-prop-heading" data-cy="sp-expanded-nsp-by-rtv">NSP/RTV</span>{{ nspByRtv }}</div>
                    <div class="sp-property-detail sp-prop-details-dark" :class="{'sp-prop-failed-validation': linzWarnings.titleWarning || !sale?.propertyInfo?.legalDescription }" :title="qivsTitles" data-cy="sp-expanded-qivs-title">
                        <span class="sp-prop-heading">QIVS Title</span>
                        <div class="qv-ellipsis">{{ qivsTitles }}</div>
                        <div class="qv-ellipsis qv-text-base" :title="qivsTitleslegalDescription">{{ qivsTitleslegalDescription }}</div>
                    </div>
                    <div class="sp-property-detail sp-prop-details-dark" :class="{'sp-prop-failed-validation': linzWarnings.titleWarning}" :title="linzTitles" data-cy="sp-expanded-linz-title">
                        <span class="sp-prop-heading">LINZ Title</span>
                        <div class="qv-ellipsis">{{ linzTitles }}</div>
                        <div class="qv-ellipsis qv-text-base" :title="linzTitleslegalDescription">{{ linzTitleslegalDescription }}</div>
                    </div>
                    <div class="sp-property-detail sp-prop-details-dark" :title="propertyOwners.join(',')" data-cy="sp-expanded-qivs-owner">
                        <span class="sp-prop-heading">QIVS Owner</span>
                        <div class="qv-ellipsis qv-text-base">{{ propertyOwners[0] }}</div>
                        <div class="qv-ellipsis qv-text-base">{{ propertyOwners[1] }}</div>
                    </div>
                    <div class="sp-property-detail sp-prop-details-dark" :title="linzSaleVendor?.join(',')"
                        :class="{'sp-prop-failed-validation': linzWarnings.partiesWarning}" data-cy="sp-expanded-sale-vendor">
                        <span class="sp-prop-heading">Sale Vendor</span>
                        <div class="qv-ellipsis qv-text-base">{{ linzSaleVendor[0] }}</div>
                        <div class="qv-ellipsis qv-text-base">{{ linzSaleVendor[1] }}</div>
                    </div>
                    <div class="sp-property-detail sp-prop-details-dark" :title="linzSalePurchaser?.join(',')" data-cy="sp-expanded-sale-purchaser">
                        <span class="sp-prop-heading">Sale Purchaser</span>
                        <div class="qv-ellipsis qv-text-base">{{ linzSalePurchaser[0] }}</div>
                        <div class="qv-ellipsis qv-text-base">{{ linzSalePurchaser[1] }}</div>
                    </div>
                </div>
                <div class="sp-property-extras" v-if="isSalesInspection" data-cy="sp-expanded-property-landMas">
                    <ul class="md-landMas">
                        <li class="md-masIcon-category" data-cy="sp-expanded-prop-category">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.category.code ?? '-' }}</strong
                            >Category
                        </li>
                        <li class="md-masIcon-eyb" data-cy="sp-expanded-prop-eyb">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.effectiveYearBuilt || '-' }}</strong
                            >Effective Year Built
                        </li>
                        <li class="md-masIcon-landUse" data-cy="sp-expanded-prop-land-use">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.landUseData.landUseDesc || '-' }}</strong
                            >Land Use
                        </li>
                        <li class="md-masIcon-units" data-cy="sp-expanded-prop-units">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.units ?? '-' }}</strong
                            >Units
                        </li>
                        <li class="md-masIcon-bedrooms" data-cy="sp-expanded-prop-bedrooms">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.totalBedrooms ?? '-' }}</strong
                            >Bedrooms
                        </li>
                        <li class="md-masIcon-toilets" data-cy="sp-expanded-prop-toilets">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.totalToilets ?? '-' }}</strong
                            >Toilets
                        </li>
                        <li class="md-masIcon-walls" data-cy="sp-expanded-prop-wall" >
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.wallConstructionAndCondition || '-' }}</strong
                            >Wall Construction and Condition
                        </li>
                        <li class="md-masIcon-roof" data-cy="sp-expanded-prop-roof" >
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.roofConstructionAndCondition || '-' }}</strong
                            >Roof Construction and Condition
                        </li>
                        <li class="md-masIcon-umrg" data-cy="sp-expanded-prop-umrg">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.underMainRoofGarages ?? '-' }}</strong
                            >Under Main Roof Garaging
                        </li>
                        <li class="md-masIcon-fsg" data-cy="sp-expanded-prop-fsg">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.freestandingGarages ?? '-' }}</strong
                            >Free Standing Garaging
                        </li>
                        <li class="md-masIcon-oli" data-cy="sp-expanded-prop-oli">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.hasLargeOtherImprovements ? 'Y' : 'N' }}</strong
                            >Other Large Improvements
                        </li>
                        <li class="md-masIcon-modernisation" data-cy="sp-expanded-prop-modernisation">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.summary.modernisation ? 'Y' : 'N' }}</strong
                            >Modernisation
                        </li>
                        <li class="md-masIcon-zone" data-cy="sp-expanded-prop-land-zone">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.landUseData.landZone ?? '-' }}</strong
                            >Zone
                        </li>
                        <li class="md-masIcon-lotPosition" data-cy="sp-expanded-prop-lot-position">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.site.lotPositionDesc || '-' }}</strong
                            >Lot Position
                        </li>
                        <li class="md-masIcon-contour" data-cy="sp-expanded-prop-contour">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.site.contourDesc || '-' }}</strong
                            >Contour
                        </li>
                        <li class="md-masIcon-viewScope" data-cy="sp-expanded-prop-view-scope">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ viewAndScope }}</strong
                            >View and Scope
                        </li>
                        <li class="md-masIcon-production" data-cy="sp-expanded-prop-production">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ production }}</strong
                            >Production
                        </li>
                        <li class="md-masIcon-maoriLand" data-cy="sp-expanded-prop-maori-land">
                            <strong class="qv-ellipsis qv-font-normal qv-text-base">{{ dvr.landUseData.isMaoriLand ? 'Y' : 'N' }}</strong
                            >Maori Land
                        </li>
                    </ul>
                </div>
                <div class="sp-sale-details" data-cy="sp-expanded-sale-details">
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Agreement" label-style="sp-sale-input-label" wrapper-style="qv-w-full">
                            <input-date v-model="sale.agreementDate" :errors="getErrorsForLabel('Agreement Date')"  data-cy="sp-expanded-sale-agreement-date"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Settlement" label-style="sp-sale-input-label" wrapper-style="qv-w-full">
                            <input-date v-model="sale.settlementDate" :errors="getErrorsForLabel('Settlement Date')"  data-cy="sp-expanded-sale-settlement-date"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex" title="To change Sale Type, click on the Sale hyperlink to open the Update Sale screen">
                        <input-inline-label label="Sale Type" label-style="sp-sale-input-label" wrapper-style="qv-w-full">
                            <form-select v-model="sale.classifications.saleTypeId" :errors="getErrorsForLabel('Sale Type')" :options="classifications.SaleType" :option-name="(option) => `${option.code} - ${option.description}`" />
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Tenure Type" label-style="sp-sale-input-label" wrapper-style="qv-w-full">
                            <form-select v-model="sale.classifications.saleTenureId" :options="classifications.SaleTenureType" :errors="getErrorsForLabel('Tenure Type')" :option-name="(option) => `${option.id} - ${option.description}`" />
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Price Value Relationship" label-style="sp-sale-input-label-lg" wrapper-style="qv-w-full">
                            <form-select v-model="sale.classifications.priceValueRelationshipId" :options="classifications.PriceValueRelationshipType" :errors="getErrorsForLabel('Price Value Relationship')" :option-name="(option) => `${option.id} - ${option.description}`" />
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Gross" label-style="sp-sale-input-label-sm">
                            <input-number v-model="sale.price.gross" :errors="getErrorsForLabel('Gross')" format="$0,0"  data-cy="sp-expanded-sale-gross"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Net" label-style="sp-sale-input-label-sm">
                            <input-number v-model="netPrice" :errors="getErrorsForLabel('Net')" format="$0,0" readonly  data-cy="sp-expanded-sale-net"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Chattels" label-style="sp-sale-input-label-sm">
                            <input-number v-model="sale.price.chattels" format="$0,0"  data-cy="sp-expanded-sale-chattels"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Other" label-style="sp-sale-input-label-sm">
                            <input-number v-model="sale.price.other" format="$0,0"  data-cy="sp-expanded-sale-price-other"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="GST" label-style="sp-sale-input-label-sm">
                            <input-number v-model="sale.price.gst" format="$0,0"  data-cy="sp-expanded-sale-price-gst"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Vendor/Purchaser" label-style="sp-sale-input-label-xlg" wrapper-style="qv-w-full">
                            <input-text v-model="sale.vendorPurchaser" maxlength="50" :errors="getErrorsForLabel('Vendor/Purchaser')"  data-cy="sp-expanded-sale-vendor-purchaser"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="QV/LINZ Remarks" label-style="sp-sale-input-label-xlg" wrapper-style="qv-w-full">
                            <input-text v-model="sale.remarks" maxlength="50" :errors="getErrorsForLabel('QV Remarks')"  data-cy="sp-expanded-sale-qv-remarks"/>
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex">
                        <input-inline-label label="Cross referenced QPIDS" label-style="sp-sale-input-label-xxlg" wrapper-style="qv-w-full">
                            <input-text v-model="crossReferencedQpids" :title="crossReferencedQpids" class="sp-cross-ref" maxlength="30" readonly />
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex"></div>
                    <div class="sp-sale-details-item qv-align-center qv-flex"></div>
                    <div class="sp-sale-details-item qv-align-center qv-flex-colum nqv-w-full">
                        <input-inline-label class="qv-h-full" label="Output Code" label-style="sp-sale-input-label-red" wrapper-style="qv-w-full">
                            <form-select v-model="sale.rfc.output" :options="classifications.ReasonOutput" :option-name="(option) => `${option.code} - ${option.description}`" :include-empty-option="true" :errors="getErrorsForLabel('Output')" data-cy="sp-expanded-sale-output-code" />
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex-column qv-w-full">
                        <input-inline-label class="qv-h-full" label="Source" label-style="sp-sale-input-label-red" wrapper-style="qv-w-full">
                            <form-select v-model="sale.rfc.source" :options="classifications.ReasonSource" :option-name="(option) => `${option.code} - ${option.description}`" :include-empty-option="true" :errors="getErrorsForLabel('Source')" data-cy="sp-expanded-sale-source" />
                        </input-inline-label>
                    </div>
                    <div class="sp-sale-details-item qv-align-center qv-flex-column qv-w-full">
                        <input-inline-label class="qv-h-full" label="Reason for change" label-style="sp-sale-input-label-red" wrapper-style="qv-w-full">
                            <form-text-area v-auto-grow maxlength="100" v-model="sale.rfc.reason" class="qv-h-full" :errors="getErrorsForLabel('Reason for Change')" data-cy="sp-expanded-sale-reason-for-change" />
                        </input-inline-label>
                    </div>
                    <div v-if="sale.rfs.date" class="sp-expanded-sale-rfs-container">
                        <p class="qv-color-blue qv-text-sm sp-expanded-sale-rfs-label">
                            RFS <span :class="{ 'sp-open-rfs' : saleRfsStatus === 1, 'closed-rfs' : saleRfsStatus === 2 }"><strong>{{ saleRfsStatusText }}</strong></span>
                        </p>
                        <div class="sp-expanded-sale-rfs">
                            <div class="sp-expanded-sale-rfs-row">
                                <div>RFS Date</div>
                                <div>{{ formatDate(sale.rfs.date) }}</div>
                                <div>Sale Id</div>
                                <div>{{ sale.saleId }}</div>
                            </div>
                            <div class="sp-expanded-sale-rfs-row">
                                <div>RFS Number</div>
                                <div>{{ sale.rfs.number }}</div>
                                <div>Sale QPID</div>
                                <div>{{ sale.rfs.qpid }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <ownership-update
                    v-if="shouldShowOwnershipSection && ownership.owners.length > 0"
                    data-cy="sp-ownership-update"
                    :validation-result="validationResult"
                    :get-errors-for-label="getErrorsForLabel"
                    :is-read-only="isOwnershipSectionReadOnly"
                    :ownership="ownership"
                    :is-any-existing-qivs-owner-non-occupier="isAnyExistingQivsOwnerNonOccupier"
                    :is-any-existing-qivs-owner-having-multiple-properties="isAnyExistingQivsOwnerHavingMultipleProperties"
                />
                <ul class="sp-tool-bar sp-listing-controls righty">
                    <li class="sp-md-pdf" title="View PDF" @click="viewSalePdf" data-cy="sp-expanded-pdf-link">
                        <i class="icons8-news-filled" :class="{ 'sp-md-pdf-na': !salePdfUrl }"></i>
                    </li>
                    <li class="sp-list-action sp-list-action-sales" @click="viewSale" data-cy="sp-expanded-sale-link"><label class="qv-text-sm">SALE</label><i class="sp-list-icons material-icons">call_made</i></li>
                    <li class="sp-list-action" @click="openQivs"><label class="qv-text-sm" data-cy="sp-expanded-qivs-link">QIVS</label><i class="sp-list-icons material-icons">call_made</i></li>
                    <li class="sp-list-action" @click="webSearch"><label class="qv-text-sm" data-cy="sp-expanded-web-link">WEB</label> <i class="sp-list-icons material-icons icon--flipped">search</i></li>
                    <li class="mdl-button mdl-js-button sp-mdl-button--icon" title="Close" @click="emit('toggleResultView')" data-cy="sp-expanded-close">
                        <i class="material-icons md-dark">&#xE5CD;</i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="sp-spinner-container">
            <loading-spinner v-if="saving" message="Saving sale..." />
        </div>
        <add-sale-inspection-consent-modal :property="propertyInfoFull" :is-open="shouldShowAddConsentModal" :address="streetAddress" :sale-id="saleId" :qpid="qpid" :active-consents="activeConsents" @close="shouldShowAddConsentModal = false" />
        <!-- TODO: Move this to own modal component -->
        <confirmation-modal dialog-title="Delete Sale" :warning="true" :is-open="shouldShowDeleteConfirmationModal" :confirm-disabled="shouldDeleteConfirmBeDisabled" confirm-class="sp-delete-sale-confirm" confirm-text="DELETE" @close="handleDeleteConfirmationClose" @confirm="deleteSale">
            <div class="sp-delete-sale-container">
                <div class="qv-text-base">
                    <p v-if="isRfcValid" style="margin-bottom: 1rem">Are you sure you want to delete this sale?</p>
                    <p v-if="isRfcValid && saleHasRfs" style="margin-bottom: 1rem">
                        This sale has an associated <strong>RFS Action Request</strong> with status:
                        <span :class="{ 'sp-open-rfs': saleRfsStatus === 1, 'closed-rfs': saleRfsStatus === 2 }"
                            ><strong>{{ saleRfsStatusText }}</strong></span
                        >
                    </p>
                    <p v-if="!isRfcOutputValid">Please select an Output Code.</p>
                    <p v-if="!isRfcSourceValid">Please select a Source.</p>
                    <p v-if="!isRfcReasonValid">Please specify a reason for deleting the sale.</p>
                </div>
                <rfs-details v-if="sale && saleHasRfs && isRfcValid" :sale-id="saleId" :qpid="qpid" :rfs="sale.rfs" />
            </div>
            <div v-if="saleHasRfs && saleRfsStatus === 1 && isRfcValid" class="sp-relink-sale">
                <label for="relinkSaleId"><strong>Re-link</strong> RFS and action record to sale id</label>
                <div class="sp-relink-sale-input-button">
                    <input-number id="relinkSaleId" v-model="sale.relinkSaleId" format="0" />
                    <button class="qv-dialog-button sp-relink-sale-button" :disabled="saving" @click="relinkSale">RELINK</button>
                </div>
            </div>
            <p v-if="relinkFailureMessage" style="margin-top: 1rem" class="sp-relink-failure">{{ relinkFailureMessage }}</p>
            <p v-if="showRelinkSuccessMessage" style="margin-top: 1rem" class="sp-relink-success">RFS and Action record relinked successfully</p>
            <loading-spinner v-if="saving" />
        </confirmation-modal>
        <!-- TODO: Move this to own modal component -->
        <confirmation-modal dialog-title="Update Sale" :warning="true" :is-open="shouldShowUpdateConfirmationModal" :confirm-disabled="!isRfcValid" confirm-class="sp-update-sale-button" confirm-text="UPDATE" @close="shouldShowUpdateConfirmationModal = false" @confirm="trySaveSale">
            <p v-if="isRfcValid && saleHasRfs && saleRfsStatus === 1" style="margin-bottom: 1rem">
                This sale has an associated <strong>RFS Action Request</strong> with status:
                <span class="sp-open-rfs"
                    ><strong>{{ saleRfsStatusText }}</strong></span
                >
                <br /><span>Updating the sale will close the RFS action record</span>
            </p>
            <p v-if="!isRfcOutputValid">Please select an Output Code.</p>
            <p v-if="!isRfcSourceValid">Please select a Source.</p>
            <p v-if="!isRfcReasonValid">Please specify a reason for updating the sale.</p>
        </confirmation-modal>
    </div>
</template>

<style lang="scss" src="./SalesSearchResult.scss" />
<style lang="scss">
.sp-flex-right {
    display: flex;
    justify-content: right;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
    row-gap: 0.5rem;
    margin-top: 0.5rem;
}
.sp-top-section {
    display: flex;
    justify-content: space-between;
}
</style>

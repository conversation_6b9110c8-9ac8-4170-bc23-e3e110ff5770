<script setup>
import { ref, computed } from 'vue';
import Multiselect from 'vue-multiselect';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import TerritorialAuthority from '../../filters/TerritorialAuthority.vue';
import Classification from '../../filters/Classification.vue';
import SaleStatus from '../../filters/SaleStatus.vue';
import { useSaleSearch } from '@/composables/useSaleSearch.js';
import { useRoute } from 'vue-router/composables';

const route = useRoute();
const labelStyle = { color: '#fff', fontSize: '1.1rem' };
const expandIconTitle = ref('Expand Results');
const showFeatures = false;
const {
    searchCriteria,
    searching,
    resetSearchCriteria,
    salesProcessingStatusOptions,
    salesSourceOptions,
    search,
    shouldshowExpandedView,
    saleClassificationOptions,
    saleStatusOptions,
    selectedSaleStatus,
    selectedClassifications,
    selectedSource,
    selectedSaleProcessingStatus,
    isCustomerCare,
    isAdminUser,
    SALES_TO_PROCESS_CRITERIA,
    SALES_INSPECTION_CRITERIA,
} = useSaleSearch();

function toggleSearchResultView() {
    shouldshowExpandedView.value = !shouldshowExpandedView.value;
    expandIconTitle.value = shouldshowExpandedView.value ? 'Collapse Results' : 'Expand Results';
}

const searchType = computed(() => {
    switch (route.name) {
        case 'sales-to-process':
            return SALES_TO_PROCESS_CRITERIA;
        case 'sales-inspection':
            return SALES_INSPECTION_CRITERIA;
        default:
            return null;
    }
});

</script>

<template>
    <div>
        <div class="sales-search-criteria">
            <div class="col-container qv-search-background">
                <div class="col-row">
                    <div class="col col-2">
                        <div class="qv-ta-search-container">
                            <territorial-authority :customLabelStyle="labelStyle" data-cy="ta-select" taId="advSearchTa"></territorial-authority>
                        </div>
                    </div>
                    <div class="col col-3">
                        <label class="col col-5 sp-date-label">
                            <span class="label qv-label-white">Sale Date</span>
                            <div class="input-date-wrapper">
                                <date-picker id="fromSaleDate" v-model="searchCriteria.fromSaleDate" data-cy="sp-from-sale-date" format="DD/MM/YYYY"
                                             type="date" value-type="DD/MM/YYYY"></date-picker>
                            </div>
                        </label>
                        <label class="col col-1">
                            <span class="label qv-label-white">&nbsp;</span>
                            <div class="qv-label-white" style="font-size: 1.1rem;">to</div>
                        </label>
                        <label class="col col-5 sp-date-label">
                            <span class="label qv-label-white">&nbsp;</span>
                            <div class="input-date-wrapper">
                                <date-picker v-model="searchCriteria.toSaleDate" data-cy="sp-to-sale-date" format="DD/MM/YYYY"
                                             type="date" value-type="DD/MM/YYYY"></date-picker>
                            </div>
                        </label>
                    </div>
                    <div class="col col-3">
                        <label class="col col-5 sp-date-label">
                            <span class="label qv-label-white">Sale Input Date</span>
                            <div class="input-date-wrapper">
                                <date-picker v-model="searchCriteria.fromSaleInputDate" data-cy="sp-from-input-date" format="DD/MM/YYYY"
                                             type="date" value-type="DD/MM/YYYY"></date-picker>
                            </div>
                        </label>
                        <label class="col col-1">
                            <span class="label qv-label-white">&nbsp;</span>
                            <div class="qv-label-white" style="font-size: 1.1rem;">to</div>
                        </label>
                        <label class="col col-5 sp-date-label">
                            <div class="input-date-wrapper">
                                <span class="label qv-label-white">&nbsp;</span>
                                <date-picker v-model="searchCriteria.toSaleInputDate" data-cy="sp-to-input-date" format="DD/MM/YYYY"
                                             type="date" value-type="DD/MM/YYYY"></date-picker>
                            </div>
                        </label>
                    </div>
                    <div class="col col-1">
                        <label>
                            <span class="label qv-label-white">Categories</span>
                            <input v-model="searchCriteria.categories" class="salesSearchCategory" data-cy="sp-categories" type="text" />
                        </label>
                    </div>
                    <div class="col col-3">
                        <label>
                            <span class="label qv-label-white">Source</span>
                            <multiselect
                                v-model="selectedSource"
                                :multiple="true"
                                :options="salesSourceOptions"
                                data-cy="sp-source-mulitselect"
                                deselect-label="⏎ remove"
                                label="description"
                                placeholder=""
                                select-label="⏎ select"
                                track-by="id"
                            />
                        </label>
                    </div>

                </div>

                <div class="col-row">
                    <div class="col col-2">
                        <div class="qv-sale-classification-search">
                            <classification
                                v-model="selectedClassifications"
                                :classification-options="saleClassificationOptions"
                                :custom-label-style="labelStyle"
                                data-cy="sp-classification">
                            </classification>
                        </div>
                    </div>
                    <div class="col col-3">
                        <label>
                            <span class="label qv-label-white">Processing Status</span>
                        </label>
                        <multiselect
                            v-model="selectedSaleProcessingStatus"
                            :disabled="isCustomerCare"
                            :multiple="true"
                            :options="salesProcessingStatusOptions"
                            data-cy="sp-processing-status-multiselect"
                            deselect-label="⏎ remove"
                            label="description"
                            placeholder=""
                            select-label="⏎ select"
                            track-by="id"
                        />
                    </div>
                    <div class="col col-3">
                        <sale-status
                            v-model="selectedSaleStatus"
                            :custom-label-style="labelStyle"
                            :sale-options="saleStatusOptions"
                            data-cy="sp-sale-status">
                        </sale-status>
                    </div>
                    <div class="col col-3 gross-sale-price-container">
                        <label class="qv-label-txt">
                            <span class="label qv-label-white">Gross Sale Price</span>
                            <input v-model="searchCriteria.fromGSP" class="sc-input--range qv-search-input-text" data-cy="sp-from-gsp" min="0" type="number" />
                            to
                            <input v-model="searchCriteria.toGSP" class="sc-input--range qv-search-input-text" data-cy="sp-to-gsp" min="0" type="number">
                        </label>
                    </div>
                    <div class="col col-1 exclude-hpi-rtv-container">
                        <div class="advSearch-group advSearch-row-dependent">
                            <label :style="labelStyle" for="excludeFromHpiRtvSale">Exclude from HPI-RTV</label>
                        </div>
                        <div class="saleStatus excludeHpiRtv">
                            <input id="excludeFromHpiRtvSale" v-model="searchCriteria.excludeFromHpiRtv" data-cy="sp-exclude-hpi-rtv" type="checkbox">
                        </div>
                    </div>
                </div>

                <div class="col-row">
                    <div class="righty search-control-buttons">
                        <div v-if="showFeatures" class="exportResults mdl-button mdl-js-button mdl-button--icon" title="Export Results">
                            <i class="material-icons md-dark">&#xE06F;</i>
                        </div>
                        <div v-if="showFeatures" :class="{'down': shouldshowExpandedView }" :title="expandIconTitle"
                             class="expandAll mdl-button mdl-js-button mdl-button--icon"
                             @click="toggleSearchResultView"
                        >
                            <i class="material-icons md-dark">&#xE8D7;</i>
                        </div>
                        <button :class="{ disabled: searching }"
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect advSearchClear" data-cy="sp-clear-button" title="Clear Search Criteria" @click="resetSearchCriteria(searchType)">
                            Clear
                        </button>
                        <button
                            :class="{ disabled: searching }"
                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored advsalesSearch"
                            data-cy="sp-search-button" @click="search(true)">
                            Search
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.search-control-buttons {
    line-height: 5.5;
    margin-bottom: -7.5em;
}

.sc-input {
    &--range {
        width: 45% !important;
    }
}

.status-actions {
    font-size: 1.1rem;
}

.advSearch-group input[type=text] {
    font-size: 1.2rem !important;
    padding: 0.5rem !important;
    border: solid 1px #d2d2d2 !important;
}

.exclude-hpi-rtv-container {
    padding: 0;
    width: 10%;
}

.gross-sale-price-container {
    width: 23%;
}

.input-date-wrapper {
    margin: 0;
    padding: 0 !important;
}

.router .multiselect--disabled {
    background: none;
}

</style>
<style lang="scss" scoped="true" src="../rollMaintenance.scss"></style>

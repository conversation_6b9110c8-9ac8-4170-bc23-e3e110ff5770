@import "../../../qv-layout";
@import "../../../qv-text";
@import "../../../qv-colors";

.sp-result-container {
	position: relative;
	background-color: #fff;
	padding: 1.6rem 1rem 0;
	margin: .8rem 0rem 1.15rem;
	height: inherit;

	&:hover {
		background-color: #f9f9f9;
		cursor: pointer;

		.sp-full-address {
			box-shadow: 0 0 0 .15rem rgba(74, 144, 226, .25);
			box-sizing: border-box;

			&:hover {
				color: #214d90;
				background: rgba(255, 255, 255, .25);
				box-shadow: 0 0 0 .15rem rgba(255, 111, 0, .5);

				&::after {
					color: #fc932f;
				}
			}

			&::after {
				@extend .qv-position-absolute;
				top: .9rem;
				right: .8rem;
				content: "";
				background-image: url(../../../../public/images/monarchLogo-mini.png);
				background-repeat: no-repeat;
				background-size: 100%;
				width: 2rem;
				height: 2rem;
			}
		}
	}
}

.sp-sup {
	font-size: .8rem;
	padding-left: .2rem;
}

.sp-btn-disabled {
	pointer-events: none;
	background: rgba(0, 0, 0, 0.25) !important;
}

.sp-address {
	margin: -.8rem 0 0 -.2rem;
	min-width: 50rem;
	@extend .qv-text-lg;
	@extend .qv-font-semibold;
}

.sp-pd-container {
	margin-top: 0.5rem;
}

.sp-prop-detail {
	@extend .qv-text-sm;
	@extend .qv-font-semibold;
	color: #204d90;
	line-height: 1.9;
	text-align: center;
	background-color: rgba(2, 136, 209, 0.06);
	padding: 0 0.5rem;
	border: 0.1rem solid rgba(2, 136, 209, 0.15);
	border-radius: 0.3rem;
	width: 14rem;
	pointer-events: none;
}

.sp-prop-details-dark {
	background: #002943;
	width: 10%;
}

.sp-tfa-tla {
	font-weight: 300;
	margin-right: 1.2rem;
	@extend .qv-text-base;
}

.sp-prop-val {
	@extend .qv-text-base;
	display: block;
	font-weight: 300;
	color: #fff;
	line-height: 1.5;

	&-sm {
		@extend .qv-text-sm;
		padding-left: .2rem;
	}
}

.sp-prop-heading {
	@extend .qv-position-absolute;
	@extend .qv-text-sm;
	top: 1.1rem;
	bottom: inherit;
	left: 1.1rem;
	color: #fff;
	font-weight: 300;
	padding-bottom: .3rem;
	width: calc(100% - 2rem);
}

.qv-ellipsis,
.sp-cross-ref input {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.sp-open-rfs,
.sp-relink-success {
	color: var(--qv-color-success);
}

.sp-closed-rfs,
.sp-relink-failure {
	color: var(--qv-color-error);
}

.sp-relink-sale {
	margin-top: 1rem;
	display: flex;
	justify-content: space-between;
}

.sp-relink-sale-input-button {
	display: flex;
	justify-content: flex-end;
	gap: 1rem;
}

.sp-relink-sale-button {
	color: #fff;
	background: var(--qv-color-success) !important;
}

.sp-delete-sale-confirm {
	background: var(--qv-color-error) !important;
}

.sp-delete-sale-container {
	display: flex;
	justify-content: space-between;
	gap: 1rem;
}

.sp-update-sale-button {
	background: var(--qv-color-mediumblue) !important;
}

.sp-property-detail {
	@extend .qv-text-base;
	text-align: left;
	position: relative;
	line-height: 1.35;
	padding: 2.7rem 0 2.5rem 1rem;
	border-right: .1rem solid rgba(255, 255, 255, .15);
	height: 8.4rem;
	box-sizing: border-box;
	float: left;
}

.sp-full-address {
	position: relative;
	color: #214d90;
	padding: .85rem 1rem .65rem;
	border-radius: .5rem;
	@extend .qv-font-semibold;
}

.sp-property-extras {
	display: block;
	padding: 15px 0;
	border-bottom: 1px dashed #cccccc;
}

.sp-tool-bar {
	@extend .qv-position-absolute;
	top: 1.2rem;
	right: 1.6rem;
	margin: 0;
}

.sp-property-details-wrapper {
	position: relative;
	color: #fff;
	background-color: #283c64;
	margin-top: 1rem;
	height: 8.4rem;
	width: 100%;
}

.sp-sale-details {
	display: grid;
	grid-template-columns: 1.5fr 1fr 2fr 2fr;
	grid-template-rows: repeat(5, auto);
	grid-auto-flow: column;
	column-gap: 30px;
	row-gap: 5px;
	padding: 15px 7px;
	width: 100%;
}

.sp-sale-details input {
	width: 100%;
	padding: 5px;
	border: 1px solid #ccc;
	border-radius: 3px;
}

.sp-sales-traffic-lights {
	@extend .qv-position-absolute;
	@extend .qv-justify-end;
	display: flex;
	gap: 10px;
	top: 8.5rem;
	left: calc(50% - 1rem);
	padding: .6rem 0 0.6rem .9rem;
	border-radius: 0.5rem;
	margin-left: 0.6rem;
	width: 50%;
}

.sp-pvr-two {
	color: #4e342e;
	background-color: rgba(255, 200, 40, 1);
	border: .1rem solid rgba(225, 175, 0, 1);
}

.sp-pvr-three {
	color: #fff;
	background-color: rgba(190, 55, 15, 1);
	border: .1rem solid rgba(165, 45, 10, 1);
}

.sp-action-button {
	border: none;
	box-shadow: 1px 1px darkgrey;
	color: #fff;
	border-radius: 2px;
	padding: 4px 8px;
	font-style: normal;
	width: 95px;
	height: auto;
    line-height: unset;
	margin-left: 0.5rem;
	@extend .qv-text-sm;
	@extend .qv-text-capitalize;
	@extend .qv-font-semibold;
}

.sp-add-consent {
	background: #002943;
	width: 112px;
}

.sp-confirm-sale {
	background: var(--qv-color-success);
}

.sp-delete-sale {
	background: var(--qv-color-error);
}

.sp-action-btn-wrapper {
	text-align: right;
	padding-right: 1rem !important;
}

.sp-md-warning {
	background-color: var(--qv-color-error);
	width: unset;
	text-align: start;
}

.sp-mdl-button--icon {
	font-size: 2.4rem;
	color: inherit;
	line-height: normal;
	padding: 0;
	border-radius: 50%;
	margin: .3rem 0 0 1rem;
	min-width: 3.2rem;
	width: 3.2rem;
	overflow: hidden;
	height: 3.2rem;
}

.sp-listing-controls {
	padding: 0;
	margin: 1rem auto 1rem;
	max-width: 144rem;
	overflow: auto;
	li {
		display: inline-block;
		font-size: 1.3rem;
		color: #455a64;
		line-height: 3.2rem;
		margin: .4rem 0 0 1rem;
		vertical-align: middle;
	}
}

.sp-list-icons {
	@extend .qv-text-sm;
	display: inline-block;
	vertical-align: inherit;
	width: 1.5rem;
}

li.sp-list-action {
	@extend .qv-text-sm;
	display: inline-block;
	position: relative;
	color: #fff;
	line-height: 1.8;
	background-color: #fc932f;
	padding: .3rem .3rem .25rem .6rem;
	border: none;
	border-radius: .3rem;
	margin: 0 0 0 .5rem;
	transition: opacity .2s ease;

	&-sales {
		background: var(--qv-color-mediumblue);
	}

	&:hover {
		opacity: .8;
	}
}

.sp-md-pdf {
	font-size: 2.4rem !important;
	color: var(--qv-color-mediumblue) !important;
	&:hover {
		opacity: .8;
	}
	&-na {
		color: rgba(0, 0, 0, .25) !important;
	}
}

.sp-sale-input-label {
	@extend .qv-font-semibold;
	min-width: 90px;
	line-height: 3;

	&-sm {
		@extend .qv-font-semibold;
		line-height: 3;
		min-width: 50px;
	}

	&-lg {
		@extend .qv-font-semibold;
		line-height: normal;
		padding-top: 10px;
	}

	&-xlg {
		@extend .qv-font-semibold;
		line-height: 2.5;
		min-width: 95px;
	}

	&-xxlg {
		@extend .qv-font-semibold;
		line-height: normal;
		min-width: 95px;
		padding-top: 10px;
	}

	&-red {
		@extend .qv-font-semibold;
		padding-top: 5px;
		line-height: 3;
		color: var(--qv-color-error);
		min-width: 100px;
	}
}

.sp-property-extras .md-landMas {
	font-size: .9rem;
	padding: 0.7rem 0 0.4rem 0;
	border: none;
	margin: 0 0 0 -0.3rem;
	width: calc(100% + 0.5rem);
	li {
		padding: 0.4rem 0.5rem 0.4rem 3.6rem;
		margin-bottom: 0.5rem;
		margin-left: 0.25rem;
		width: calc(16.5% - 0.25rem);
		background: rgba(237, 241, 245, .8);
		display: inline-block;
		position: relative;

		&:before {
			top: 0.8rem;
			left: 0.8rem;
			@extend .qv-text-lg;
		}
	}
}

.sp-expanded-sale-rfs-container {
    display: flex;
    justify-content: space-between;
    gap: 5px;
    margin-top: 0.5rem;
}

.sp-expanded-sale-rfs-label {
    min-width: 100px;
    font-weight: 600;
}

.sp-expanded-sale-rfs {
    width: 100%;
    border: 0.1rem solid rgba(2, 136, 209, 0.15);
    border-radius: 0.3rem;
    padding: 0 0.5rem;
    padding-top: 0.2rem;
    color: #204d90;
    background-color: rgba(2, 136, 209, 0.06);
    font-weight: 1.1rem;
}

.sp-expanded-sale-rfs-row {
    display: flex;
    flex-direction: row;
    gap: 5px;

    div {
        width: 25%;
        &:nth-child(2n) {
            font-weight: 600;
        }
  }
}

.sp-result-row {
    border-bottom: 1px solid var(--color-lightblue-0);
}

.sp-sale-to-process {
    background: var(--qv-color-red);
    &:hover {
        background: var(--color-red-0) !important;
    }
}

.sp-sale-autoProcessed {
   background: var(--qv-color-orange);
   &:hover {
       background: var(--color-orange-0) !important;
   }
}

.sp-prop-failed-validation {
	background-color: var(--qv-color-error);
}

.qv-unlinked-table {
	text-align: left;
	position: relative;
	background-color: white;
	border-collapse: collapse;

	td:first-child {
		padding-left: 2rem;
	}

	td:last-child {
		padding-right: 2rem;
	}

	th {
		position: sticky;
		top: 0;
		z-index: 1;
	}

	th:first-child > div {
		padding-left: 2rem;
	}

	th:last-child > div {
		padding-right: 2rem;
	}

	th > div {
		padding: 1rem 0;
		color: rgba(0, 0, 0, .54);
		text-decoration: underline;
		line-height: 3;
		background-color: var(--qv-color-light);
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	}

	td {
		padding: 1rem 0.5rem;
		margin: 1rem 0;
	}
}

.qv-unlinked-table-header {
	box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.4);
}

.sale-status-pending {
	background-color: var(--qv-color-yellow);
}
.sale-status-unconfirmed {
	color: white;
	background-color: var(--qv-color-darkred);
}
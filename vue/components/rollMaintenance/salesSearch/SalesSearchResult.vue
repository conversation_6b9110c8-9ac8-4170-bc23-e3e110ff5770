<script setup>
import { ref } from 'vue';
import SalesSearchResultExpanded from './SalesSearchResultExpanded.vue';
import SalesSearchResultLine from './SalesSearchResultLine.vue';
const showExpandedView = ref(false);

const props = defineProps({
    sale: {
        required: true,
        type: Object,
    },
    activeTab: {
        required: true,
        type: String,
    },
    shouldShowCheckbox: {
        required: true,
        default: false,
        type: Boolean
    }
});

function toggleResultView() {
    showExpandedView.value = !showExpandedView.value;
}

</script>

<template>
    <div class="sp-result-row">
        <sales-search-result-expanded
            v-if="showExpandedView"
            data-cy="sp-sales-search-result-expanded"
            :sale-id="sale.saleId"
            :qpid="sale.qpid"
            :active-tab="activeTab"
            @toggleResultView="toggleResultView()"
        />
        <sales-search-result-line v-else
            data-cy="sp-sales-search-result-line"
            :should-show-checkbox="shouldShowCheckbox"
            :sale="sale"
            @toggleResultView="toggleResultView()"
        />
    </div>
</template>
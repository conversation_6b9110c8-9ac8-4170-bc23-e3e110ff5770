<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { useSaleSearch } from '@/composables/useSaleSearch.js';
import { useRoute, useRouter } from 'vue-router/composables';
import SearchCriteriaTabBar from 'Common/form/SearchCriteriaTabBar.vue';
import SearchCriteriaTab from 'Common/form/SearchCriteriaTab.vue';
import { store } from '@/DataStore';

const router = useRouter();
const route = useRoute();
const sortField = ref('SALE_DATE');
const direction = ref('DESC');
const shouldShowCheckbox = ref(false);
const user = computed(() => store.state.userData);
const canAccessSalesToProcess = computed(() => (user.value?.isAdminUser || user.value?.isCustomerCare));

const {
    page,
    queryParams,
    searching,
    results,
    search,
    resetSearchCriteria,
    activeTab,
    SALES_TO_PROCESS_TAB,
    SALES_INSPECTION_TAB,
} = useSaleSearch();
</script>

<template>
    <div class="resultsWrapper sales-search-activity-list">
        <div class="resultsInner-wrapper mdl-shadow--3dp">
            <SearchCriteriaTabBar>
                <template #title>
                    Sales Search
                </template>
                <SearchCriteriaTab v-if="user.isAdminUser || user.isCustomerCare"
                                   :to="{name: 'sales-to-process'}">
                    Sales to Process
                </SearchCriteriaTab>
                <SearchCriteriaTab :to="{name: 'sales-inspection'}">
                    Sales Inspection
                </SearchCriteriaTab>
                <SearchCriteriaTab v-if="user.isAdminUser || user.isCustomerCare" :to="{name: 'sales-unlinked'}">
                    Unlinked Sales
                </SearchCriteriaTab>
            </SearchCriteriaTabBar>
            <router-view />
        </div>
    </div>
</template>


<style lang="scss" src="../search/searchRollMaintenance.scss"></style>
<style lang="scss" scoped>
.qv-sp-tab-title {
    padding: 1.6rem 2rem 0.6rem;
    margin: 0 0.3rem;
    margin-bottom: -0.1rem;
    box-shadow: none;
    height: 5.6rem;
    box-sizing: border-box;
    font-size: 1.6rem;
    color: #0e3a83;
    background-color: transparent;
    width: auto;
    font-weight: bold;
}
.qv-sp-wrapper {
    margin: 0 auto 0;
}
.qv-sp-toolbar {
    background: #edf1f5;
}
.qv-sp-title {
    background: #dae2e7;
    box-shadow: 0 0.2rem 0.1rem 0 #dae2e7;
}
.qv-sp-tab-label {
    color: #0e3a83
}
.qv-sp-tab-active {
    border-bottom: 0.3rem solid #0e3a83;
}
</style>

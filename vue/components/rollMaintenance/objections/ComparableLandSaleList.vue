<script setup>
import { computed, ref, watch, inject } from 'vue';
import SortHeader from 'Common/SortHeader.vue';
import { store } from '@/DataStore';
import ComparableSaleListItemForm from '@/components/rollMaintenance/objections/ComparableSaleListItemForm.vue';
import { openQivsInNewTab, openUrlInNewTab } from '@/utils/QivsUtils';
import { formatArea, formatDistance, formatAddressLine1, formatAddressLine2, formattedAddress } from '@/utils/FormatUtils';
import { isSaleOutsideRevisionRange, getSaleLandValue, getSaleContour, isSaleCurrentPropertyMaoriLand, isSaleToProcess, getSaleInfo } from './utils';
import useSales from '@/composables/useExpandedSales.js';
import { useSaleAnalysis } from '@/composables/useSaleAnalysis';


const { tryOpenAnalysisById } = useSaleAnalysis();
const qivsUrl = computed(() => store.state.userData.qivsUrl);
const currentRevisionDate = inject('currentRevisionDate');
const { viewSale } = useSales();

const props = defineProps({
    paginationInfo: {
        type: Object,
        default: () => ({
            totalResultsVisible: 0,
            offset: 0,
            totalResultCount: undefined,
            direction: 'DESC',
        }),
    },
    searching: {
        type: Boolean,
        default: false,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    selected: {
        type: Boolean,
        default: false,
    },
    sales: {
        type: Array,
        default: () => [],
    },
});
const emit = defineEmits(['sort', 'comparableSaleSelected', 'saveComparableSale', 'refreshComparableSale']);
const sortField = ref();
const photoUrlMap = computed(() => store.state.propertyPhotos.photoUrlMap);
loadPhotos();
watch(() => props.sales, loadPhotos);

function loadPhotos() {
    for (const sale of props.sales) {
        if (sale.property) {
            store.dispatch('propertyPhotos/getPropertyPhoto', sale.propertyId);
        }
    }
}

function openProperty(qpid) {
    if (!qpid) {
        return;
    }
    openUrlInNewTab(`/property/property?qupid=${qpid}`);
}

function getPhotoUrl(sale) {
    store.dispatch('propertyPhotos/getPropertyPhoto', sale.propertyId);
    if (!photoUrlMap.value) {
        return '';
    }
    const photo = photoUrlMap.value[sale.propertyId];
    return photo ? photo.smallImageUrl : store.state.propertyPhotos.noPhotoUrl;
}

function sort(data) {
    sortField.value = data.columnName;
    emit('sort', data);
}

function navButtonClick(buttonName, sale) {
    console.log('navButtonClick', buttonName, sale);
    switch (buttonName) {
        case 'qivs':
            openQivsLink(sale.qpid);
            break;
        case 'map':
            openMapLink(sale.qpid);
            break;
        case 'web':
            openUrlInNewTab(`https://google.co.nz/search?near=New+Zealand&q=${formattedAddress(sale)}`);
            break;
        case 'sale':
            viewSale(sale.saleId, sale.qpid, false);
            break;
        case 'analysis':
            tryOpenAnalysisById(sale.saleId);
            break;
        default:
            break;
    }
}

function masterDetailsUrl(qpid) {
    return store.getters['userData/qivsMasterDetailsUrl'](qpid);
}

function openQivsLink(qpid) {
    openQivsInNewTab(masterDetailsUrl(qpid));
}

function openMapLink(qpid) {
    const path = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/property/qv-map/0/0/${qpid}`;
    const qvMapWindow = window.open(path, 'QVMap', 'scrollbars=yes,resizable=yes,height=800,width=1366');
    qvMapWindow.focus();
}

function comparableSaleSelected(e, sale) {
    e.preventDefault();
    emit('comparableSaleSelected', { ...sale, checked: !props.selected, comparableType: 'LV' });
}

</script>

<template>
    <div class="qv-comparable-sales-list">
        <template v-if="selected">
            <p v-if="loading">loading...</p>
            <p v-else>{{ sales.length }} land sale{{ sales.length == 1 ? '' : 's' }} selected.</p>
        </template>
        <template v-else>
            <p v-if="searching">Loading results...</p>
            <p v-else-if="!searching && paginationInfo.totalResultCount == 0">
                No comparable sales found. Adjust search criteria and try again.
            </p>
            <p v-else-if="!searching">
                Showing {{ (paginationInfo.totalResultsVisible > 0 ? paginationInfo.offset + 1 : 0) | numeral }} to
                {{ (paginationInfo.offset + paginationInfo.totalResultsVisible) | numeral }} of
                {{ paginationInfo.totalResultCount | numeral }} comparable sales (excluding those selected).
            </p>
        </template>
        <table data-cy="lv-comparable-sales-list-table" class="table lv-comparable-sales-list-table" :class="{'qv-disabled': searching || loading}">
            <tr>
                <th>&nbsp;</th>
                <th data-cy="lv-comparables-table-address" class="colHeader text-left">
                    <sort-header column-name="ADDRESS" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'ADDRESS'" @onchange="sort">Address</sort-header>
                </th>
                <th data-cy="lv-comparables-table-valuation-reference" class="colHeader">
                    <sort-header column-name="VALUATION_REFERENCE" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'VALUATION_REFERENCE'" @onchange="sort">Val Ref</sort-header>
                </th>
                <th data-cy="lv-comparables-table-sale-id" class="colHeader">
                    <sort-header column-name="SALE_ID" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'SALE_ID'" @onchange="sort">Sale ID</sort-header>
                </th>
                <th data-cy="lv-comparables-table-sale-date" class="colHeader">
                    <sort-header column-name="SALE_DATE" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'SALE_DATE'" @onchange="sort">Sale Date</sort-header>
                </th>
                <th data-cy="lv-comparables-table-net-sale-price" class="colHeader" title="Net sale price">
                    <sort-header column-name="NSP" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'NSP'" @onchange="sort">NSP</sort-header>
                </th>
                <th data-cy="lv-comparables-table-current-land-value" class="colHeader" title="Current land value">
                    <sort-header column-name="LV" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'LV'" @onchange="sort">Current LV</sort-header>
                </th>
                <th data-cy="lv-comparables-table-land-area" class="colHeader">
                    <sort-header column-name="LAND_AREA" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'LAND_AREA'" @onchange="sort">Area (Ha)</sort-header>
                </th>
                <th data-cy="lv-comparables-table-zone-code" class="colHeader" title="Zone code">
                    <sort-header column-name="ZONE" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'ZONE'" @onchange="sort">Zone</sort-header>
                </th>
                <th data-cy="lv-comparables-table-contour-code" class="colHeader" title="Contour code">
                    <sort-header column-name="CONTOUR" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'CONTOUR'" @onchange="sort">Cntr</sort-header>
                </th>
                <th data-cy="lv-comparables-table-view-code-view-scope-code" class="colHeader" title="View code & view scope code">
                    <sort-header column-name="VIEV_SCP" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'VIEV_SCP'" @onchange="sort">View/Scp</sort-header>
                </th>
                <th data-cy="lv-comparables-table-lot-position-code" class="colHeader" title="Lot Position code">
                    <sort-header column-name="LOT_POS" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'LOT_POS'" @onchange="sort">Lot Pos</sort-header>
                </th>
                <th data-cy="lv-comparables-table-property-category" class="colHeader" title="Property Category">
                    <sort-header column-name="CATEGORY" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'CATEGORY'" @onchange="sort">Category</sort-header>
                </th>
                <th data-cy="lv-comparables-table-land-sale-net-rate" class="colHeader" title="Land Sale Net Rate">
                    <sort-header column-name="LAND_SNR" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'LAND_SNR'" @onchange="sort">Land SNR</sort-header>
                </th>
                <th data-cy="lv-comparables-table-distance" class="colHeader">
                    <sort-header column-name="DISTANCE" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'DISTANCE'" @onchange="sort">Distance</sort-header>
                </th>
                <th data-cy="lv-comparables-table-comparability-score" class="colHeader">
                    <sort-header column-name="COMPARABILITY_SCORE" :direction="paginationInfo.direction" :active="paginationInfo.sortField === 'COMPARABILITY_SCORE'" @onchange="sort">Match</sort-header>
                </th>
            </tr>
            <template v-for="sale in sales">
                <tr :title="getSaleInfo(sale, currentRevisionDate)" :class="{
                    'yellow-highlight': isSaleOutsideRevisionRange(currentRevisionDate, sale),
                    'sale-to-process': isSaleToProcess(sale),
                    'maori-land': isSaleCurrentPropertyMaoriLand(sale)
                }">
                    <td>
                        <input style="padding-left: 0.5rem;" type="checkbox" class="action-record-hide" :checked="selected" @click="comparableSaleSelected($event, sale)">
                    </td>
                    <td>
                        <div class="primaryThumb-Wrapper">
                            <img class="primaryPhoto_thumb" alt="" :src="getPhotoUrl(sale)">
                        </div>
                        <a class="action-record-clickable" @click.prevent="openProperty(sale.qpid)">
                            <div class="fullAddress" style="padding: 0 0 0 0.5rem; min-width: 9rem;">
                                <span>{{ formatAddressLine1(sale) }}</span>
                                <span>{{ formatAddressLine2(sale) }}</span>
                            </div>
                        </a>
                    </td>
                    <td class="text-right">{{ sale.rollNumber }} / {{ sale.assessmentNumber }} {{ sale.suffix }}</td>
                    <td class="text-right">{{ sale.saleId }}</td>
                    <td class="text-right">{{ sale.saleDate | date }}</td>
                    <td class="text-right">{{ sale.salePriceNet | currency }}</td>
                    <td class="text-right">{{ getSaleLandValue(sale) | currency }}</td>
                    <td class="text-right">{{ formatArea(sale.area) }}</td>
                    <td class="text-right">{{ sale.zone }}</td>
                    <td class="text-right">{{ getSaleContour(sale) }}</td>
                    <td class="text-right">{{ sale.viewCode || '' }}{{ sale.scopeCode || '' }}</td>
                    <td class="text-right">{{ sale.lotPosition }}</td>
                    <td class="text-right">{{ sale.categoryCode }}</td>
                    <td class="text-right">{{ sale.landSaleNetRate | currency }}</td>
                    <td class="text-right">{{ formatDistance(sale.distance) }}</td>
                    <td class="text-right" style="padding-right: 0.5rem;">{{ sale.matchPercent | percentage }}</td>
                </tr>
                <comparable-sale-list-item-form
                    v-if="selected"
                    :key="sale.saleId"
                    :comparable-sale="sale"
                    @nav-button-click="navButtonClick($event, sale)"
                    @save="data => emit('saveComparableSale', { ...sale, comparableType: 'LV', shouldSaveRatingValuation: data.shouldSaveRatingValuation })"
                    @refresh="emit('refreshComparableSale', {...sale, comparableType: 'LV'})"
                />
            </template>
        </table>
    </div>
</template>

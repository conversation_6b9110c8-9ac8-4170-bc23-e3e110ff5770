<template>
    <div class="qv-flex-column" style="gap: 2rem">
        <div v-if="propertyLoaded && propertyDetail.isStale" class="righty message message-warning">
            NOTE: The detailed information for this property has changed, please review the
            <router-link :to="{name: 'property', params: {qpid: qpid}}" target="_blank">current property</router-link>
            and update the draft property details.
        </div>
        <div v-if="activities.length && !isActionRecord" class="col-container mdl-shadow--3dp">
            <div class="qv-box-header">
                <h1 class="title">Building Consents</h1>
                <expander v-model="expandConsentsValue" />
            </div>
            <validation-header-message :validation-set="valuationValidationSet" :show-errors="true" message="Unable to update Building Consents" />
            <current-roll-maintenance-activities v-show="expandConsentsValue" :load-on-mount="false" :can-edit="!isJobReadOnly" />
        </div>
        <div v-if="objections.length" class="col-container mdl-shadow--3dp">
            <div class="qv-box-header">
                <h1 class="title">{{ getObjectionsTitle }}</h1>
                <ul class="qv-button-row">
                    <li class="md-qivs" @click="qivsObjections()">
                        <label>OBJECTIONS</label>
                        <i class="material-icons">call_made</i>
                    </li>
                    <li class="md-qivs" @click="qivsAttachments()">
                        <label>DOCUMENTATION</label>
                        <i class="material-icons">call_made</i>
                    </li>
                </ul>
                <expander v-model="expandObjectionValue" @change="$emit('toggleExpandObjection')" />
            </div>
            <objection-details :expand-objection="expandObjection" :objections="objections" :is-job-read-only="isJobReadOnly" :is-action-record="isActionRecord" @link-objection="setLinkObjection" :saving-link="savingLink"/>
        </div>
        <div v-if="isActionRecord && qpid" class="col-container mdl-shadow--3dp" :class="{ 'action-record-hide': !BCActivities || BCActivities.length == 0 }">
            <h1 class="title">
                Linked Building Consents
            </h1>
            <valuation-roll-maintenance-activities :qpid="qpid" />
        </div>
        <div :class="{'qv-read-only-wrapper': isJobReadOnly }">
            <div v-if="propertyLoaded" :class="{'qv-read-only': isJobReadOnly && !isActionRecord }" class="col-container mdl-shadow--3dp">
                <div class="qv-box-header">
                    <h1 class="title">Valuation Conclusions</h1>
                    <expander v-model="expandConclusions" />
                </div>
                <valuation-conclusions
                    v-if="expandConclusions"
                    :qpid="qpid"
                    :property-desc="propertyDetail.description"
                    :rating-valuation="ratingValuation"
                />
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import { initQivsLink } from '@/utils/CommonUtils.js';
import { updateObjectionLink } from '@/services/ObjectionController.js';

export default {
    name: 'DraftPropertyDetails',
    components: {
        expander: () => import(/* webpackChunkName: "Expander" */ '../../common/Expander.vue'),
        'current-roll-maintenance-activities': () => import(/* webpackChunkName: "RatingValuation" */ '../ratingValuation/activities/RollMaintenanceActivities.vue'),
        'objection-details': () => import(/* webpackChunkName: "RatingValuation" */ '../ratingValuation/activities/ObjectionDetails.vue'),
        'valuation-conclusions': () => import(/* webpackChunkName: "RatingValuation" */ '../ratingValuation/activities/ValuationConclusions.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "ValidationHeaderMessage" */ '../../common/form/ValidationHeaderMessage.vue'),
        'valuation-roll-maintenance-activities': () => import(/* webpackChunkName: "ValuationRollMaintenanceActivities" */ '../ratingValuation/activities/RollMaintenanceActivitiesReadOnly.vue'),
    },
    inject: [
        'ratingValuation',
        'valuationLoaded',
        'qpid',
        'valuationValidationSet',
        'objections',
        'propertyActivities',
        'taCode',
        'isJobReadOnly',
        'expandObjection',
    ],
    data() {
        return {
            expandConsentsValue: true,
            expandConclusions: true,
            savingLink: false,
            expandObjectionValue: true,
        };
    },
    mounted() {
        this.expandConsentsValue = this.expandConsents;
    },
    watch: {
        expandObjection(value) {
            this.expandObjectionValue = value;
        },
        expandConsents(value) {
            this.expandConsentsValue = value;
        },
    },
    props: {
        isActionRecord: {
            type: Boolean,
            default: false,
        },
        expandConsents: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        ...mapState('userData', [
            'qivsUrl',
        ]),
        ...mapState('propertyDraft', {
            propertyDetail: 'propertyDetail',
            draftLoading: 'loading',
            validationSet: 'validationSet',
            formIsStale: 'formIsStale',
        }),
        ...mapState('ratingValuation', [
            'valuationActivities',
        ]),
        BCActivities() {
            return this.valuationActivities?.filter(activity => activity.activityType.code == 'BC' && activity.status.code !== 'CANCELED');
        },
        activities() {
            return this.propertyActivities.filter(
                activity => activity.activityType.code == 'BC'
                    && (this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) || (activity.status.code !== 'DONE' && activity.status.code !== 'CANCELED')),
            );
        },
        propertyLoaded() {
            return (
                this.valuationLoaded
                && !this.draftLoading
                && this.propertyDetail
                && this.taCode
                && this.ratingValuation.ratingUnit.qpid === this.propertyDetail.qpid
            );
        },
        getObjectionsTitle(){
            return this.isActionRecord? "Linked Objections" : "Objections";
        }
    },
    methods: {
        qivsAttachments() {
            const linkedObjections = this.objections.filter(o => o);
            if (linkedObjections.length == 1) {
                initQivsLink(this.qivsUrl, 'objectionAttachments', linkedObjections[0].qpid, linkedObjections[0].objectionId);
                return;
            }
            initQivsLink(this.qivsUrl, 'objections', this.qpid);
        },
        qivsObjections() {
            initQivsLink(this.qivsUrl, 'objections', this.qpid);
        },

        async setLinkObjection({ toLink, rollMaintenanceActivityId, objectionId }) {
            this.savingLink = true;
            try {
                const { status, updated, message } = await updateObjectionLink(toLink, objectionId, this.ratingValuation.id);
                if (!(status == 'UPDATED' || status == 'SUCCESS')) {
                    console.error('Failed to update objection link');
                    return;
                }
                // unable to unlink if the objection is the only one.
                if (status == "SUCCESS" && updated === false) {
                    alert(`Unlink failed: ${message}`);
                    return;
                }
                if (toLink) {
                    await this.linkRollMaintenanceActivity(rollMaintenanceActivityId)
                }
                else {
                    await this.unlinkRollMaintenanceActivity(rollMaintenanceActivityId);
                }
            } catch (error){
                console.error('Failed to setObjectionLink', error);
            } finally {
                this.savingLink = false;
            }
        },

        async linkRollMaintenanceActivity(rollMaintenanceActivityId) {
            this.$store.dispatch('ratingValuation/linkRollMaintenanceActivity', rollMaintenanceActivityId);

        },
        async unlinkRollMaintenanceActivity(rollMaintenanceActivityId) {
            this.$store.dispatch('ratingValuation/unlinkRollMaintenanceActivity', rollMaintenanceActivityId);
        },
    },
};
</script>

<script setup>
import { useRoute, useRouter } from 'vue-router/composables';
import { onMounted, ref } from 'vue';
import Valuation from '../ratingValuation/Valuation.vue';

const route = useRoute();
const router = useRouter();
const { rollMaintenanceActivityId } = route.params;
const hasError = ref(false);
const actionRecordType = ref(null);

onMounted(initRedirect);

async function initRedirect() {
    const { url } = jsRoutes.controllers.RatingValuationController.getCompleteValuationForActivity(rollMaintenanceActivityId);
    try {
        const res = await fetch(`${url}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        const { id: ratingValuationId, rollMaintenanceActivityIds } = await res.json();
        if (rollMaintenanceActivityIds?.find(id => id?.substring(0, 2) === 'OB')) {
            await router.push({
                name: 'rating-valuation-objection-action-record',
                params: { ratingValuationId },
            });
        }

        else if (rollMaintenanceActivityIds?.find(id => id?.substring(0, 2) === 'BC')) {
            actionRecordType.value = 'BC';
        }
        else {
            actionRecordType.value = undefined;
        }
    }
    catch (error) {
        console.error(error);
        hasError.value = true;
    }
}

</script>

<template>
    <div>
        <h1 v-if="hasError || actionRecordType === undefined" style="font-size: xx-large; color: red;">No valuation found or you do not have permission to see it.</h1>
        <Valuation v-if="actionRecordType === 'BC'" />
    </div>
</template>

<script setup>
import { computed, ref, inject, watch } from 'vue';
import { useStore } from '@/composables/useStore';
import { onBeforeRouteLeave } from 'vue-router/composables';
import { debounce } from 'lodash';
import WorksheetForm from '@/components/rollMaintenance/ratingValuation/worksheet/WorksheetForm.vue';
import CommonObjectionBoxes from '@/components/rollMaintenance/objections/CommonObjectionBoxes.vue';
import AlertModal from 'Common/modal/AlertModal.vue';
import ValidationMessages from 'Common/ValidationMessages.vue';
import { WorksheetFormMaori } from '@/components/rollMaintenance/ratingValuation/worksheet';
import ValidationConfirmationModal from '@/components/rollMaintenance/ratingValuation/ValuationValidationModal.vue';
import useModal from '@/composables/useModal';
import { RatingValuation } from '@quotable-value/validation';
import { injectRatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';

const { STEPS } = RatingValuation
const context = injectRatingValuationContext();
const isJobReadOnly = inject('isJobReadOnly');
const modal = useModal();
const objectionJobValidationResult = inject('objectionJobValidationResult')
const objectionSteps = [
    'rating-valuation-objection-draft',
    'rating-valuation-objection-compare',
    'rating-valuation-objection-valuation',
    'rating-valuation-objection-complete',
];
const validationHeader = ref();
const successModalIsOpen = ref(false);
const successMessage = ref({
    heading: '',
    message: '',
});
const saveTimeout = ref(null);

const propertyStore = useStore('property');
const propertyDraftStore = useStore('propertyDraft');
const ratingValuationStore = useStore('ratingValuation');
const {
    ratingValuation,
    isLoading,
    isSaving,
    validationSet,
    exception
} = ratingValuationStore.state;
const { property } = propertyStore.state;
const { isMaoriLand } = propertyStore.getters;
const { propertyDetail } = propertyDraftStore.state;

const ratingValuationComponentsLength = computed(() => {
    return ratingValuation.value?.ratingValuationComponents?.length;
});
const errors = computed(() => {
    return objectionJobValidationResult.value?.valuationStepValidationResult?.errors;
});
const warnings = computed(() => {
    return objectionJobValidationResult.value?.valuationStepValidationResult?.warnings;
});
const isRatingValuationReadyToSave = computed(() => {
    let returnValue = true;
    for (const component of ratingValuation.value.ratingValuationComponents) {
        component.shouldHighlight = false;
        const isNewRow = !component?.buildingType && !component?.description && (component?.areaInSquareMetres ?? true) && (component?.valuePerSquareMetre ?? true) && component?.value == 0;
        if (isNewRow) {
            continue;
        }
        if (!(component?.buildingType || component?.description)) {
            component.shouldHighlight = true;
            returnValue = false;
        }
        const filtered = [component?.areaInSquareMetres, component?.valuePerSquareMetre, component?.value].filter(value => value ?? false);
        const hasAtLeast2OutOf3 = filtered.length >= 2;
        if (!hasAtLeast2OutOf3) {
            component.shouldHighlight = true;
            returnValue = false;
        }
    }
    return returnValue;
});

const debouncedSave = debounce(save, 2000);

function onWorksheetChanged() {
    context.save();
}

async function save(silent = false, override = false, routeLeave = false) {
    if ((isJobReadOnly.value || !isRatingValuationReadyToSave.value) && !override) {
        return;
    }

    const errors = routeLeave ? validationSet.value.errors.filter(error => error.field.step === STEPS.WORKSHEET) : validationSet.value.errors;
    const warnings = validationSet.value.warnings;
    let proceedToSave = true;

    if(errors.length > 0) {
        const subTitle = `Unable to save until the following ${errors.length} issues are resolved:`;
        const cancelErrorText = 'RETURN TO WORKSHEET';
        const payload = {
            subTitle,
            isError: true,
            cancelErrorText,
            onlyConfirm: false,
            validationList: errors
        }
        proceedToSave = await modal.show(ValidationConfirmationModal, payload);
        if(!proceedToSave) {
            return false;
        }
    }
    if(warnings.length > 0 && !routeLeave) {
        const title = 'Do you want to proceed?';
        const subTitle = 'The following validation checks are failing:';
        const confirmText = 'YES, SAVE WORKSHEET';
        const cancelText = 'NO, RETURN TO WORKSHEET';
        const payload = {
            title,
            subTitle,
            isWarning: true,
            cancelText,
            onlyConfirm: false,
            confirmText,
            validationList: warnings
        }
        proceedToSave = await modal.show(ValidationConfirmationModal, payload);
        if(!proceedToSave) {
            return false;
        }
    }

    await ratingValuationStore.dispatch('saveValuation', ratingValuation.value);
    await propertyDraftStore.dispatch('savePropertyDraft');

    ratingValuation.value.ratingValuationComponents = ratingValuation.value.ratingValuationComponents
        .map((component, index) => ({ ...component, id: index }));

    if (validationSet.success && !silent) {
        showSuccess();
    }

    return true;
}

function showSuccess(message = {}) {
    successMessage.value.heading = message.heading || 'Saved.';
    successMessage.value.message = message.message || 'Your changes have been saved.';
    successMessage.value.navigateTo = message.navigateTo || null;
    successModalIsOpen.value = true;
}

function closeSuccessModal() {
    successModalIsOpen.value = false;
    if (!successMessage.value.navigateTo) {
        return;
    }
    router.push(successMessage.value.navigateTo);
}

onBeforeRouteLeave(async (to, from, next) => {
    if (objectionSteps.includes(to.name) && !isJobReadOnly.value) {
        if (!await save(true, true, true)) {
            return;
        }
    }
    next();
});
</script>

<template>
    <div class="qv-worksheet" :class="{ 'qv-disabled' : isSaving }">
        <validation-messages :errors="errors" :warnings="warnings" />
        <CommonObjectionBoxes :expand-consents="false" @toggleExpandObjection="$emit('toggleExpandObjection')" />
        <div :class="{'qv-read-only-wrapper': isJobReadOnly }" v-if="ratingValuation && property">
            <div :class="{'qv-read-only': isJobReadOnly }" class="mdl-shadow--3dp form-section">
                <div class="col-container">
                    <div class="col-row">
                        <div class="col col-6">
                            <h1 class="title">Valuation Worksheet</h1>
                        </div>
                        <div class="col col-6 message-right-italic action-record-hide">
                            <p>Changes to property data should be made on the Draft Property Details screen.</p>
                            <p>Changes to the main net rate should be made on the Comparable Sales screen.</p>
                        </div>
                    </div>
                </div>
                <div :key="`${ratingValuation.id-ratingValuation.entityVersion}`" class="qv-p-3 qv-bg-light">
                    <WorksheetFormMaori v-if="isMaoriLand" @changed="onWorksheetChanged" :property="property" :property-detail="propertyDetail" :value="ratingValuation" :validation-set="validationSet"/>
                    <WorksheetForm v-else @changed="onWorksheetChanged" :property="property" :property-detail="propertyDetail" :value="ratingValuation" :validation-set="validationSet"/>
                </div>
            </div>
        </div>
        <alert-modal v-if="successModalIsOpen" success @close="closeSuccessModal">
            <h1>{{ successMessage.heading }}</h1>
            <p>{{ successMessage.message }}</p>
        </alert-modal>
    </div>
</template>

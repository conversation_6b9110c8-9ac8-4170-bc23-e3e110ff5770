<script setup>
import { ref, inject, onMounted, computed, watch } from 'vue';
import CommonObjectionBoxes from '@/components/rollMaintenance/objections/CommonObjectionBoxes.vue';
import ObjectionContact from '@/components/rollMaintenance/objections/ObjectionContact.vue';
import ObjectionValuerReview from '@/components/rollMaintenance/objections/ObjectionValuerReview.vue';
import ValidationMessages from '@/components/common/ValidationMessages.vue';
import { injectRatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';

import { ValidationContext } from '@/components/ui/validation';
import AlertModal from 'Common/modal/AlertModal.vue';
import { useStore } from '@/composables/useStore';
import { useJobCompletion } from '@/composables/objections/useJobCompletion';
import { RatingValuation } from '@quotable-value/validation';

const { STEPS } = RatingValuation;

const props = defineProps({
    furtherContactWarnings: {
        type: Array,
        default: () => [],
    },
});

const linkedObjection = inject('linkedObjection');
const ratingValuationId = inject('ratingValuationId');
const ratingValuation = inject('ratingValuation');
const validationSet = inject('valuationValidationSet');
const context = injectRatingValuationContext();

const objectionContactComponent = ref();
const objection = ref({});
const isLoaded = ref(false);
const riskType = ref(null);
const shouldShowValidationModal = ref(false);
const latestReview = computed(() => reviews.value?.find(r => r.reviewCompleted));
const currentReview = computed(() => reviews.value?.find(r => !r.reviewCompleted));

const emit = defineEmits([
    'refreshObjection',
    'toggleExpandObjection',
    'setContactValuer',
    'setContactRegisteredValuer',
]);

const {
    reviews,
    requestingReview,
    requestReview,
    saveDraft,
    validationAction,
    fetchReviews,
    sendToReview,
    completeReview,
    notesForReview,
    completeValuation,
} = useJobCompletion(ratingValuationId, linkedObjection, riskType, currentReview);

const ratingValuationStore = useStore('ratingValuation');

const saveRatingValuationException = computed(() => ratingValuation.value.exception);
const saveRatingValuationErrors = computed(() => ratingValuation.value.errors);
const validationResults = computed(() => validationSet?.value);
const errors = computed(() => validationSet?.value?.errors);
const warnings = computed(() => validationSet?.value?.warnings);

const completionValidations = computed(() => {
    return validationSet.value.getStep(STEPS.COMPLETION);
});


const validationMessage = computed(() => {
    if (errors?.value?.length) {
        return 'The following errors must be rectified before proceeding:';
    }
    return 'Are you sure you want to proceed with the following warnings?';
});

watch(linkedObjection, onLinkedObjectionChange);

onMounted(async () => {
    if (linkedObjection.value !== null) {
        await onLinkedObjectionChange(linkedObjection.value);
    }
});

async function handleSendToReview(notesForReviewer, risk) {
    try {
        const isSaved = await objectionContactComponent.value.updateObjectionContact();
        if (isSaved) {
            validationAction.value = 'review';
            notesForReview.value = notesForReviewer;
            riskType.value = risk?.description || null;
            await context.save();

            if (saveRatingValuationException.value || saveRatingValuationErrors.value) {
                return
            };
            await context.validate();
            await handleAction();
        }
    } catch (err) {
        console.error("error in handleSendToReview:", err);
    }
}

async function handleCompleteValuation() {
    requestingReview.value = false;
    validationAction.value = 'complete valuation';
    await context.save();

    if (saveRatingValuationException.value || saveRatingValuationErrors.value) {
        return
    }

    await context.validate();
    if (errors?.value?.length) {
        shouldShowValidationModal.value = true;
    } else {
        await handleAction();
    }
}

async function onLinkedObjectionChange(newVal) {
    if (newVal) {
        isLoaded.value = false;
        objection.value = newVal;
        await fetchReviews(ratingValuationId.value);
        isLoaded.value = true;
    }
}

function handleConfirm() {
    shouldShowValidationModal.value = false;
    handleAction();
}

async function handleAction() {
    if (validationAction.value == 'review') {
        await sendToReview(notesForReview.value);
        emit('refreshObjection')
    }
    else if (validationAction.value == 'complete valuation') {
        await completeValuation();
        emit('refreshObjection');
    }
}

async function setRatingValuationValuer(data) {
    const ratingValuationToSave = { ...ratingValuation.value };
    ratingValuationToSave.valuer = data;
    await ratingValuationStore.dispatch('setValuation', ratingValuationToSave);
}

async function setValuer(data) {
    if(data?.name) {
        emit('setContactValuer', data)
        await setRatingValuationValuer(data);
    }
}

function setRegisteredValuer(data) {
    if(data?.name) {
        emit('setContactRegisteredValuer', data);
    }
}

function validateValuation() {
    context.validate({
        atCompletion: true,
        isComplete: true,
    });
}

async function handleCompleteReview(outcome) {
    await completeReview(outcome);
    emit('refreshObjection');
}
</script>

<template>
    <ValidationContext :validation-set="validationSet">
        <div>
            <CommonObjectionBoxes
                :expand-consents="false"
                @toggleExpandObjection="emit('toggleExpandObjection')"
            />
            <div v-if="isLoaded">
                <ObjectionContact
                    ref="objectionContactComponent"
                    :objection="objection"
                    :ratingValuationId="ratingValuationId"
                    :loaded="isLoaded"
                    :latest-review="latestReview"
                    :furtherContactWarnings="furtherContactWarnings"
                    :objection-validation-set="validationResults"
                    @setValuer="setValuer"
                    @setRegisteredValuer="setRegisteredValuer"
                    @saveDraft="saveDraft"
                    @requestReview="requestReview"
                    @completeValuation="handleCompleteValuation"
                    @validate="validateValuation"
                    @validateSra="validateValuation"
                />
            </div>
            <ObjectionValuerReview
                :current-review="currentReview"
                :latest-review="latestReview"
                :ratingValuationId="ratingValuationId"
                :requesting-review="requestingReview"
                :validation-set="validationSet"
                @completeReview="handleCompleteReview"
                @sendToReview="handleSendToReview"
                @validate="validateValuation"
            />
            <AlertModal v-if="shouldShowValidationModal" warning @close="shouldShowValidationModal = false">
                <h3>Objection Job Validation Failed</h3>
                <p style="margin-bottom: 0.5rem;">{{ validationMessage }}</p>
                <validation-messages
                    :errors="errors"
                    :warnings="warnings"
                />
                <template #buttons>
                    <div class="alertButtons" style="margin-top: 1rem;">
                        <button class="mdl-button mdl-button--mini lefty" @click="shouldShowValidationModal = false">
                            No, Return to objection job
                        </button>
                        <button v-if="validationSet.validationPassed" class="mdl-button mdl-button--mini" @click="handleConfirm">
                            {{ validationAction === 'review' ? 'Yes, Send To Review': 'Confirm' }}
                        </button>
                    </div>
                </template>
            </AlertModal>
        </div>
    </ValidationContext>
</template>

<script setup>
import { ref, inject, onMounted, computed } from 'vue';
import { store } from '../../../../DataStore';
import ObjectionContact from '@/components/rollMaintenance/objections/ObjectionContact.vue';
import ComparableSales from '@/components/rollMaintenance/objections/step/ComparableSales.vue';
import CommonObjectionBoxes from '@/components/rollMaintenance/objections/CommonObjectionBoxes.vue';
import ObjectionValuerReviewOutcome from '@/components/rollMaintenance/objections/ObjectionValuerReviewOutcome.vue';
import ObjectionReinstatement from '@/components/rollMaintenance/objections/ObjectionReinstatement.vue';
import { reinstateObjectionJob } from '@/services/ObjectionController.js';
import { useRouter } from 'vue-router/composables';
import { generateObjectionReport } from '../utils.js';
import ValuationWorksheetReadOnly from '@/components/rollMaintenance/ratingValuation/ValuationWorksheetReadOnly.vue';
import PropertyDetailsReadOnly from '@/components/propertyDetails/PropertyDetailsReadOnly.vue';

const router = useRouter();
const linkedObjection = inject('linkedObjection');
const ratingValuationId = inject('ratingValuationId');
const ratingValuation = inject('ratingValuation');
const property = inject('property');
const propertyDetail = inject('propertyDetail');
const latestReview = ref(null);
const deleteConfirmationVisible = ref(false);
const reinstateConfirmationVisible = ref(false);
const isTAUser = computed(() => store.state.userData.isTAUser);
const props = defineProps(['furtherContactWarnings']);
const isWaiting = ref(false);
const externalObjectionAccess = computed(() => store.state.userData.externalObjectionAccess);
const valuationReference = computed(() => property?.value?.valuationReference);

const shouldShowReinstatementButton = computed(() => {
    return linkedObjection.value?.valJobStatus == 'TA Sign-off' && !isTAUser.value && !externalObjectionAccess.value;
});

const shouldShowViewReportBtn = computed(() => {
    return linkedObjection.value?.reportJobId;
});

const address = computed(() => {
    if (property.value?.address) {
        return (
            property.value.address.streetAddress
            + (property.value.address.suburb ? `, ${property.value.address.suburb}` : '')
        );
    }
    return null;
})
onMounted(async () => {
    if (ratingValuationId.value) {
        const endpoint = jsRoutes.controllers.ApiPicklistController.getObjectionJobReview(ratingValuationId.value);
        try {
            const res = await fetch(endpoint.url);
            const reviews = (await res.json()).reviews;
            latestReview.value = reviews.find(r => r.reviewCompleted);
        }
        catch (error) {
            console.error('Failed to retrieve objection job reviews', error);
        }
    }
});
async function handleReinstatement(reinstatement) {
    isWaiting.value = true;
    try {
        const reinstatementResult = await reinstateObjectionJob(reinstatement);
        if (reinstatementResult.status == 'ADDED' && reinstatementResult.reinstatement) {
            await router.push({
                name: 'rating-valuation-objection-draft',
                params: { ratingValuationId: ratingValuationId.value },
            });
            document.location.reload();
        }
        else {
            alert(`Error while reinstating objection job: ${reinstatementResult.message}`);
        }
    }
    catch (error) {
        console.error(error);
        alert('Error while reinstating objection job');
    }
    finally {
        isWaiting.value = false;
        hideConfirmationModal();
    }
}

async function generateReport() {
    const title = `Objection Report - ${address.value}.pdf`.replaceAll('/', '%2F');
    const preview = linkedObjection.value?.valJobStatus == 'Valued/Actioned';
    const url = await generateObjectionReport(linkedObjection.value.objectionId, title, preview);
    if(url) {
        window.open(url, '_blank');
        document.location.reload();
    }
}

function hideConfirmationModal() {
    deleteConfirmationVisible.value = false;
    reinstateConfirmationVisible.value = false;
}

</script>

<template>
    <div>
        <div>
            <div class="righty">
                <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                    @click="generateReport">
                    {{ shouldShowViewReportBtn ? 'View Report' : 'Create Report' }}
                </button>
                <button v-if="shouldShowReinstatementButton"
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored-red"
                    @click="reinstateConfirmationVisible = true">
                    Reinstate Job
                </button>
            </div>
            <objection-reinstatement :should-show-modal="reinstateConfirmationVisible" :objection="linkedObjection"
                :loading="isWaiting" @reinstatement="handleReinstatement"
                @hideModal="reinstateConfirmationVisible = false" />
        </div>
        <div v-if="ratingValuation && linkedObjection" class="col-container mdl-shadow--3dp non-clickable">
            <div class="title">
                <div class="col col-6">
                    <h1>
                        Values (as at {{ linkedObjection.revaluationDate | date }})
                    </h1>
                </div>
                <div class="righty">
                    <h1>
                        Action record created on : {{ (linkedObjection.TLAApprovalDate ||
                            linkedObjection.SeniorApprovalDate) | date }}
                    </h1>
                </div>
            </div>

            <div class="col-row">
                <div class="col col-1" />
                <div class="col col-2">
                    <label class="righty">
                        <span class="header">Capital Value</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="header">Land Value</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="header">Value of Improvements</span>
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-1">
                    <h3>Existing</h3>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>{{ ratingValuation.originalValue.capitalValue | currency }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>{{ ratingValuation.originalValue.landValue | currency }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>{{ ratingValuation.originalValue.valueOfImprovements | currency }}</span>
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-1">
                    <h3>Objector's&nbsp;Estimate</h3>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>{{ linkedObjection.estimatedCapitalValue | currency }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>{{ linkedObjection.estimatedLandValue | currency }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>
                            {{ linkedObjection.estimatedCapitalValue - linkedObjection.estimatedLandValue | currency }}
                        </span>
                    </label>
                </div>
            </div>

            <div v-if="ratingValuation.adoptedValue" class="col-row">
                <div class="col col-1">
                    <h3 class="adopted">Adopted&nbsp;Values</h3>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="adopted">{{ ratingValuation.adoptedValue.capitalValue | currency }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="adopted">{{ ratingValuation.adoptedValue.landValue | currency }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="adopted">{{ ratingValuation.adoptedValue.valueOfImprovements | currency }}</span>
                    </label>
                </div>
            </div>
            <div v-if="ratingValuation.adoptedValue && ratingValuation.originalValue" class="col-row">
                <div class="col col-1">
                    <h3>Difference</h3>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>{{ ratingValuation.adoptedValue.capitalValue - ratingValuation.originalValue.capitalValue |
                            currency }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>{{ ratingValuation.adoptedValue.landValue - ratingValuation.originalValue.landValue | currency
                        }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>
                            {{ ratingValuation.adoptedValue.valueOfImprovements -
                                ratingValuation.originalValue.valueOfImprovements | currency }}
                        </span>
                    </label>
                </div>
            </div>
        </div>

        <objection-contact v-if="linkedObjection" :furtherContactWarnings="furtherContactWarnings"
            :objection="linkedObjection" :ratingValuationId="ratingValuation.id" :loaded="true">
        </objection-contact>

        <div v-if="latestReview" class="col-container mdl-shadow--3dp non-clickable">
            <h1 class="title qv-mb-3">Registered Valuer Review - Outcome</h1>
            <objection-valuer-review-outcome :review="latestReview" />
        </div>
        <common-objection-boxes :is-action-record="true"></common-objection-boxes>

        <div class="col-container mdl-shadow--3dp">
            <h1 class="title">
                Valuation Worksheet
            </h1>

            <valuation-worksheet-read-only :rating-valuation="ratingValuation" :valuation-reference="valuationReference" />
        </div>
        <comparable-sales v-if="linkedObjection" :is-action-record="true"></comparable-sales>
        <div class="col-container mdl-shadow--3dp">
            <h1 class="title">
                Property Snapshot
            </h1>
            <property-details-read-only
                :property-detail="propertyDetail"
                :show-derived-dvr-fields="true"
                :show-buildings-and-spaces="true"
            />
        </div>

    </div>
</template>

<style scoped>
.non-clickable {
    pointer-events: none;
}

.header {
    /* font-size: 1.1rem; */
    line-height: 1.6;
    color: var(--qv-color-blue);
    height: 2.2rem;
    font-weight: 600;
}

::v-deep .action-record-clickable {
    pointer-events: all !important;
    cursor: hand !important;
}

::v-deep .col-container {
    pointer-events: none;
}

::v-deep .col-container textarea {
    user-select: none !important;
    pointer-events: none !important;
    border: 0 !important;
    box-shadow: none !important;
    background-color: white !important;
}

::v-deep .qv-comp-sale-inline-form {
    user-select: none;
    pointer-events: none;
    border: 0;
    box-shadow: none;
    background-color: white;
}

::v-deep .col-container input {
    user-select: none !important;
    pointer-events: none !important;
    border: 0 !important;
    box-shadow: none !important;
    background-color: white !important;
}

::v-deep .col-container .calculation-row {
    background-color: white !important;
    ;
}

::v-deep .header-row {
    background-color: white !important;
    color: var(--qv-color-blue) !important;
}

::v-deep .multiselect {
    user-select: none !important;
    pointer-events: none !important;
    border: 0 !important;
    box-shadow: none !important;
    background-color: white !important;
}

::v-deep .multiselect__tags {
    user-select: none !important;
    pointer-events: none !important;
    border: 0 !important;
    box-shadow: none !important;
    background-color: white !important;
}

::v-deep i {
    display: none !important;
    user-select: none !important;
    pointer-events: none !important;
    border: 0 !important;
    box-shadow: none !important;
    background-color: white !important;
}

::v-deep .action-record-hide {
    display: none;
}

::v-deep .multiselect__select {
    display: none;
}

::v-deep .col-container button {
    display: none;
}

::v-deep .qv-button-row {
    display: none;
}

::v-deep .qv-disabled {
    opacity: 1;
    pointer-events: none;
}

::v-deep .multiselect__tag {
    color: black;
    background: white;
}

::v-deep .qv-read-only-wrapper {
    cursor: pointer;
}
.adopted {
    font-weight: 550;
}
</style>

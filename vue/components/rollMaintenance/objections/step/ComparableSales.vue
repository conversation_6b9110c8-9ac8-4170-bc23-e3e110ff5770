<script setup>
import { ref, unref, inject, onMounted, computed, watch } from 'vue';
import { sortBy } from 'lodash';
import CommonObjectionBoxes from '@/components/rollMaintenance/objections/CommonObjectionBoxes.vue';
import ProposedValueForm from '@/components/rollMaintenance/objections/ComparableProposedValueForm.vue';
import ComparableLandSaleList from '@/components/rollMaintenance/objections/ComparableLandSaleList.vue';
import ComparableImprovedSaleList from '@/components/rollMaintenance/objections/ComparableImprovedSaleList.vue';
import ComparableSearchForm from '@/components/rollMaintenance/objections/ComparableSearchForm.vue';
import ValidationMessage from '@/components/common/form/ValidationMessage.vue';
import Paginate from '@/components/common/paginate/paginate.vue';
import ValidationMessages from '@/components/common/ValidationMessages.vue';
import { store } from '@/DataStore';
import { sortFieldMapping, addPropertyUUID, median } from '../utils';
import * as ApiPicklistController from '@/services/ApiPicklistController';
import { createValidationSet, ValidationSet } from '@quotable-value/validation';
import useModal from '@/composables/useModal';
import ValidationConfirmationModal from '@/components/rollMaintenance/ratingValuation/ValuationValidationModal.vue';
import { useStore } from '@/composables/useStore';
import { ValidationContext } from '@/components/ui/validation';
import { set } from 'lodash';
import { injectRatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';

const context = injectRatingValuationContext();
const modal = useModal();
const emit = defineEmits(['toggleExpandObjection']);
const ta = inject('taCode');
const isJobReadOnly = inject('isJobReadOnly');
const objectionJobValidationResult = inject('objectionJobValidationResult');
const ratingValuationId = inject('ratingValuationId');
const isActionRecord = inject('isActionRecord');
const valuationSet = inject('valuationValidationSet');
const ratingValuation = computed(() => store.state.ratingValuation.ratingValuation);
const errors = computed(() => valuationSet?.value?.errors);
const warnings = computed(() => valuationSet?.value?.warnings);
const property = inject('property');
const propertyDetail = inject('propertyDetail');
const draftPropertyParamsForComparableSales = inject('draftPropertyParamsForComparableSales');
const selectedImprovedSales = ref([]);
const selectedImprovedSalesSorted = computed(() => {
    const sorted = sortBy([...selectedImprovedSales.value], sortFieldMapping[uiSortInfoCV.value.sortField]);
    return uiSortInfoCV.value.sortDescending ? sorted.reverse() : sorted;
});
const selectedLandSales = ref([]);
const selectedLandSalesSorted = computed(() => {
    const sorted = sortBy([...selectedLandSales.value], sortFieldMapping[uiSortInfoLV.value.sortField]);
    return uiSortInfoLV.value.sortDescending ? sorted.reverse() : sorted;
});
const taCode = computed(() => ta?.value);
let lastSearchCriteria = { searchCriteria: { taCode: taCode, excludedSaleIds: [] } };
const searchType = ref('CV');
const validationSet = ref();
const searchValidationSet = ref();
const searching = ref(false);
const loading = ref(false);
const comparableSalesSearchResult = ref([]);
const props = defineProps({
    isActionRecord: {
        type: Boolean,
        default: false
    }
});
const comparableSearchValidation = ref(new ValidationSet());
const paginationInfo = ref({
    sortField: 'COMPARABILITY_SCORE',
    sortDescending: true,
    offset: 0,
    limit: 25,
    page: 1,
    totalPageCount: 0,
    direction: 'DESC',
    totalResultsVisible: 0,
    totalResultCount: undefined, // this is initial state, to differentiate from 0 where it is being queried and corresponding message should be shown.
});
const uiSortInfoCV = ref({
    sortField: 'COMPARABILITY_SCORE',
    sortDescending: true,
    direction: 'DESC',
});
const uiSortInfoLV = ref({
    sortField: 'COMPARABILITY_SCORE',
    sortDescending: true,
    direction: 'DESC',
});
const addImprovedSaleId = ref(null);
const addLandSaleId = ref(null);
const addSaleValidationSet = ref();
const medianNetRateSaving = ref(false);
resetValidation();

const workingNetRate = computed(() => {
    if (ratingValuation.value) {
        return ratingValuation.value.workingNetRate;
    }
    return null;
});
const isMaoriLand = computed(() => property.value?.landUseData?.isMaoriLand);

const getComparableSalesFirstLoaded = ref(false);
const ratingValuationStore = useStore('ratingValuation');
const { validationSet: valuationValidationSet} = ratingValuationStore.state;

onMounted(() => {
    if (propertyDetail.value?.qpid) {
        getComparableSales();
    }

    context.validate();
});

function updateNetRate(data) {
    const updatedRatingValuation = { ...ratingValuation.value };
    set(updatedRatingValuation, 'workingNetRate', data);
    ratingValuationStore.commit('setRatingValuation', updatedRatingValuation);
    context.validate();
}

watch(() => propertyDetail.value?.qpid, () => {
    if (selectedLandSales.value.length + selectedImprovedSales.value.length == 0) {
        getComparableSales();
    }
});
watch(() => taCode, () => {
    lastSearchCriteria = { searchCriteria: { taCode: taCode.value } };
});

function resetValidation() {
    const defaults = {
        success: true,
        errors: [],
    };

    validationSet.value = { ...defaults };
    searchValidationSet.value = { ...defaults };
    addSaleValidationSet.value = { ...defaults };
}

function calculateLandRow(landValue) {
    const { landArea } = property.value.landUseData;
    const area = landArea * 10000;
    const rate = (!isNaN(landValue / area) && area > 0) ? parseFloat((landValue / area).toFixed(4)) : null;
    return {
        areaInSquareMetres: area > 0 ? area : null,
        buildingType: null,
        componentType: 'LAND',
        description: 'Land',
        value: landValue,
        valuePerSquareMetre: rate,
    };
}

function round(value) {
    // ASSUMPTION Value is not negative
    let roundingAmount;
    switch (true) {
        case (value >= 0 && value <= 500):
            roundingAmount = 50;
            break;
        case (value >= 501 && value <= 1000):
            roundingAmount = 100;
            break;
        case (value >= 1001 && value <= 10000):
            roundingAmount = 500;
            break;
        case (value >= 10001 && value <= 150000):
            roundingAmount = 1000;
            break;
        case (value >= 150001 && value <= 500000):
            roundingAmount = 5000;
            break;
        case (value >= 500001 && value <= 10000000):
            roundingAmount = 10000;
            break;
        case (value >= 10000001):
            roundingAmount = 50000;
            break;
        default:
            return value;
    }
    return Math.round(value / roundingAmount) * roundingAmount;
}

function getWorksheetValues(rv) {
    const capitalValue = rv.ratingValuationComponents
        .map(component => component.value).reduce((total, current) => total + current, 0);

    const landValue = rv.ratingValuationComponents
        .filter(component => component.componentType === 'LAND')
        .map(component => component.value).reduce((total, current) => total + current, 0);
    const valueOfImprovements = capitalValue - landValue;
    return { capitalValue, landValue, valueOfImprovements };
}

function updateRoundedAdoptedValue(rv) {
    const { capitalValue, landValue, valueOfImprovements } = getWorksheetValues(rv);
    rv.adoptedValue = {
        capitalValue: round(capitalValue),
        landValue: round(landValue),
        valueOfImprovements,
    };
}

function updateRatingValuationComponents(rv, landRow) {
    const components = rv.ratingValuationComponents.filter(rvComponent => rvComponent.componentType !== 'LAND');
    components.push(landRow);
    rv.ratingValuationComponents = components;
}

function updateCalculatedValue(rv, landValue) {
    if (!rv || !rv.calculatedValue) {
        return;
    }
    const delta = landValue - rv.calculatedValue.landValue;
    rv.calculatedValue.landValue = landValue;
    if (rv.calculatedValue.capitalValue != null) {
        rv.calculatedValue.capitalValue += delta;
    }
}

async function handleRecalculate(data) {
    if (isJobReadOnly.value || props.isActionRecord || !property.value || !ratingValuation.value) return;
    const { worksheetNetRate, worksheetLandValue } = data;
    const landValue = parseInt(worksheetLandValue, 10);
    const ratingValuationToSave = JSON.parse(JSON.stringify(ratingValuation.value));
    ratingValuationToSave.workingNetRate = parseInt(worksheetNetRate, 10);
    if (worksheetLandValue) {
        updateRatingValuationComponents(ratingValuationToSave, calculateLandRow(landValue));
        updateCalculatedValue(ratingValuationToSave, landValue);
        updateRoundedAdoptedValue(ratingValuationToSave);
    }
    store.dispatch('ratingValuation/saveValuation', ratingValuationToSave);
}

async function searchComps(payload, queryParameters, getTotal) {
    if (!getTotal) {
        searching.value = true;
    }
    const queryParam = getTotal ? { total: true } : queryParameters;
    const queryParamStr = new URLSearchParams(queryParam).toString();
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.searchComparableSales();
        const res = await fetch(`${url}?${queryParamStr}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(payload),
        });
        const response = await res.json();
        const { status, message, total, comparableSales, validationSet } = response;
        if (status !== 'SUCCESS') {
            handleError(`Failed to search for comparable sales. ${message}`);
            return { total: 0, comparableSales: [] };
        }
        comparableSearchValidation.value = validationSet ? createValidationSet(validationSet?.validationResults) : new ValidationSet();
        comparableSales.forEach((sale) => {
            sale.propertyId = null;
        });
        return { total, comparableSales };
    }
    catch (error) {
        handleError('Error calling search comparable sale api', error);
        return { total: 0, comparableSales: [] };
    }
    finally {
        if (!getTotal) {
            searching.value = false;
        }
    }
}

function setExcludedSalesIds() {
    const excludedSaleIds = [];
    if (selectedImprovedSales.value) {
        selectedImprovedSales.value.forEach((sale) => {
            excludedSaleIds.push(sale.saleId);
        });
    }
    if (selectedLandSales.value) {
        selectedLandSales.value.forEach((sale) => {
            excludedSaleIds.push(sale.saleId);
        });
    }
    lastSearchCriteria.searchCriteria.excludedSaleIds = excludedSaleIds;
}

async function getComparableSales() {
    loading.value = true;
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getComparableSales(ratingValuationId.value);
        const params = new URLSearchParams(draftPropertyParamsForComparableSales.value);
        const res = await fetch(`${url}?${params}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        const { status, message, improvedSales, landSales } = await res.json();
        if (status !== 'SUCCESS') {
            handleError(`Error calling search comparable sale api. ${message}`);
            return;
        }
        selectedImprovedSales.value = improvedSales;
        selectedLandSales.value = landSales;

        const comparableSales = await addPropertyUUID([...improvedSales, ...landSales]);
        selectedImprovedSales.value = comparableSales.filter(s => s.comparableType == 'CV');
        selectedLandSales.value = comparableSales.filter(s => s.comparableType == 'LV');
    }
    catch (error) {
        handleError('Error calling get comparable sale api', error);
    }
    finally {
        getComparableSalesFirstLoaded.value = true;
        setExcludedSalesIds();
        loading.value = false;
    }
}

async function addComparableSales(comparableSalesToAdd, shouldFollowUp) {
    if (isJobReadOnly.value || props.isActionRecord) return;
    try {
        const { status, message, invalidSales, comparableSales } = await ApiPicklistController.addComparableSales(ratingValuationId.value, comparableSalesToAdd);
        if (Array.isArray(invalidSales) && invalidSales.length > 0) {
            const errors = invalidSales[0]?.validationErrors;
            const proceed = checkValidation(errors);
            if (!proceed) {
                return;
            }
        }
        if (status !== 'ADDED' || !comparableSales.length) {
            handleError(`Failed to add comparable sales. ${message}`);
            return;
        }
        await followUpActions(shouldFollowUp);
        await search(lastSearchCriteria);
    }
    catch (error) {
        handleError('Error calling add comparable sale api', error);
    }
}

async function checkValidation(errors) {
    const modal = useModal();
    let proceed = true;
    if(Array.isArray(errors) && errors.length > 0) {
        const title = 'The following errors must be rectified before proceeding:';
        const cancelErrorText = 'RETURN';
        const payload = {
            title,
            isError: true,
            cancelErrorText,
            onlyConfirm: false,
            validationList: errors
        }
        proceed = await modal.show(ValidationConfirmationModal, payload);
    }
    return proceed;
}

async function updateComparableSales(comparableSalesToUpdate) {
    if (isJobReadOnly.value || props.isActionRecord) return;
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.updateComparableSales(ratingValuationId.value);
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(comparableSalesToUpdate),
        });
        const { status, message, validationErrors, comparableSales } = await res.json();
        if (Array.isArray(validationErrors) && validationErrors.length > 0) {
            const errors = validationErrors[0]?.validationErrors;
            const proceed = checkValidation(errors);
            if (!proceed) {
                return;
            }
        }
        await followUpActions(comparableSalesToUpdate.shouldSaveRatingValuation);
        if (status !== 'UPDATED' || !comparableSales.length) {
            handleError(`Failed to update comparable sales. ${message}`);
        }
    }
    catch (error) {
        handleError('Error calling update comparable sale api', error);
    }
}

async function removeComparableSales(comparableSalesToRemove, shouldFollowUp) {
    if (isJobReadOnly.value || props.isActionRecord) return;
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.removeComparableSales(ratingValuationId.value);
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(comparableSalesToRemove),
        });
        const { status, message, comparableSales } = await res.json();
        if (status !== 'DELETED' || !comparableSales.length) {
            handleError(`Failed to remove comparable sales. ${message}`);
            return;
        }
        await followUpActions(shouldFollowUp);
        await search(lastSearchCriteria);
    }
    catch (error) {
        handleError('Error calling remove comparable sale api', error);
    }
}

function paginationParams() {
    return {
        limit: paginationInfo.value.limit,
        offset: paginationInfo.value.offset,
        sortBy: paginationInfo.value.sortField,
        sortDesc: paginationInfo.value.sortDescending,
    };
}

function resetPagination() {
    paginationInfo.value.page = 1;
    paginationInfo.value.totalPageCount = 0;
    paginationInfo.value.direction = 'ASC';
    paginationInfo.value.totalResultsVisible = 0;
    paginationInfo.value.totalResultCount = 0;
    paginationInfo.value.offset = 0;
    paginationInfo.value.limit = 25;
    paginationInfo.value.sortField = null;
}

async function search(criteria) {
    searching.value = true;
    resetValidation();
    resetPagination();
    const previousCriteria = lastSearchCriteria.searchCriteria;
    const searchCriteria = {
        searchCriteria: {
            ...previousCriteria,
            ...criteria,
            taCode: taCode.value,
        },
        targetProperty: draftPropertyParamsForComparableSales.value,
    };
    lastSearchCriteria = searchCriteria;
    searchType.value = searchCriteria.searchCriteria.searchType;
    const queryParameters = paginationParams();
    const { total, comparableSales } = await searchComps(searchCriteria, queryParameters);
    comparableSalesSearchResult.value = comparableSales;
    paginationInfo.value.totalResultsVisible = comparableSales.length;
    paginationInfo.value.totalResultCount = total;
    paginationInfo.value.totalPageCount = Math.ceil(total / paginationInfo.value.limit);
    searching.value = false;
    comparableSalesSearchResult.value = await addPropertyUUID(comparableSales);
}

async function sort(data) {
    searching.value = true;
    paginationInfo.value.sortField = data.columnName;
    paginationInfo.value.sortDescending = data.direction === 'DESC';
    paginationInfo.value.direction = data.direction;
    const { comparableSales } = await searchComps(lastSearchCriteria, paginationParams());
    comparableSalesSearchResult.value = comparableSales;
    paginationInfo.value.totalResultsVisible = comparableSales.length;
    searching.value = false;

    comparableSalesSearchResult.value = await addPropertyUUID(comparableSales);
}

async function uiSort(data, comparableType) {
    if (comparableType == 'CV') {
        uiSortInfoCV.value.sortField = data.columnName;
        uiSortInfoCV.value.sortDescending = data.direction === 'DESC';
        uiSortInfoCV.value.direction = data.direction;
    }
    else {
        uiSortInfoLV.value.sortField = data.columnName;
        uiSortInfoLV.value.sortDescending = data.direction === 'DESC';
        uiSortInfoLV.value.direction = data.direction;
    }
}

async function onChangePage(newPage) {
    searching.value = true;
    paginationInfo.value.page = newPage;
    paginationInfo.value.offset = (newPage - 1) * paginationInfo.value.limit;
    const { comparableSales } = await searchComps(lastSearchCriteria, paginationParams());
    comparableSalesSearchResult.value = comparableSales;
    paginationInfo.value.totalResultsVisible = comparableSales.length;
    searching.value = false;

    comparableSalesSearchResult.value = await addPropertyUUID(comparableSales);
}

function handleSelection(data) {
    const { checked, comparableType, saleId, objectionJobComparableSaleId } = data;
    if (checked) {
        addComparableSales({ ratingValuationId: ratingValuationId.value, saleId, comparableType, comparability: null }, comparableType === 'CV');
    }
    else {
        removeComparableSales({ objectionJobComparableSaleId }, comparableType === 'CV');
    }
}

function setAddSaleValidation(comparableType, status, message) {
    addSaleValidationSet.value = {
        success: false,
        errors: [
            {
                status,
                field: `addSale${comparableType}`,
                message,
            },
        ],
    };
}

function saleIdAlreadyAdded(comparableType, saleId) {
    const salesToCheck = [...comparableType === 'CV' ? selectedImprovedSales.value : selectedLandSales.value];
    if (salesToCheck.find(s => s.saleId === saleId)) return true;
    return false;
}

async function addComparableSaleById(comparableType) {
    if (isJobReadOnly.value || props.isActionRecord) return;
    resetValidation();
    const saleIdStr = comparableType === 'CV' ? addImprovedSaleId.value : addLandSaleId.value;
    if (!saleIdStr) {
        setAddSaleValidation(comparableType, 'INVALID', 'Please enter a sale id');
        return;
    }
    const saleId = parseInt(saleIdStr, 10);
    if (saleIdAlreadyAdded(comparableType, saleId)) {
        setAddSaleValidation(comparableType, 'INVALID', 'The sale is already in the list');
        return;
    }
    try {
        const saleToAdd = {
            saleId,
            comparableType,
            comparability: null,
        };

        const { status, invalidSales, comparableSales } = await ApiPicklistController.addComparableSales(ratingValuationId.value, saleToAdd, true);
        if (Array.isArray(invalidSales) && invalidSales.length > 0) {
            const errors = invalidSales[0]?.validationErrors;
            const proceed = checkValidation(errors);
            if (!proceed) {
                return;
            }
        }
        if (status !== 'ADDED' || !comparableSales.length) {
            setAddSaleValidation(comparableType, status, 'The sale id entered is not a valid sale');
            return;
        }
        await followUpActions(comparableType === 'CV');
        await search(lastSearchCriteria);
    }
    catch (error) {
        handleError('Error calling add comparable sale api', error);
    }
}

function saveComparableSale(data) {
    updateComparableSales({ ...data, ratingValuationId: ratingValuationId.value });
}

async function refreshComparableSale(data) {
    if (isJobReadOnly.value || props.isActionRecord) return;
    const comparableSaleToRefresh = { ...data, ratingValuationId: ratingValuationId.value };
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.refreshComparableSale(ratingValuationId.value);
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(comparableSaleToRefresh),
        });
        const { status, message, validationErrors, comparableSale } = await res.json();
        if (Array.isArray(validationErrors) && validationErrors.length > 0) {
            const proceed = checkValidation(validationErrors);
            if (!proceed) {
                return;
            }
        }
        if (status !== 'UPDATED' || !comparableSale) {
            handleError(`Failed to refresh comparable sales. ${message}`);
        }
        getComparableSales();
    }
    catch (error) {
        handleError('Error calling refresh comparable sale api', error);
    }
}

function handleError(msg, exception) {
    if (exception) {
        console.error(msg, exception);
        store.commit('ratingValuation/setException', `${msg} ${exception}`);
    }
    else {
        store.commit('ratingValuation/setException', msg);
    }
}

async function followUpActions(shouldSaveRatingValuation = true) {
    try {
        await getComparableSales();
        if (shouldSaveRatingValuation) {
            await updateMedianNetRate();
        }
    }
    catch (error) {
        console.error('Error calling followUpActions');
        handleError('Oops, something went wrong.', error);
    }
}

async function updateMedianNetRate() {
    medianNetRateSaving.value = true;
    try {
        const ratingValuationToSave = JSON.parse(JSON.stringify(ratingValuation.value));
        ratingValuationToSave.workingNetRate = parseInt(Math.round(median(selectedImprovedSales.value.map(sale => sale.adjustedNetRate))), 10);
        await store.dispatch('ratingValuation/saveValuation', ratingValuationToSave);
    }
    catch (error) {
        handleError('Error updating Median Net Rate', error);
    }
    finally {
        medianNetRateSaving.value = false;
    }
}
</script>

<template>
    <div>
        <common-objection-boxes v-if="!isActionRecord" :expand-consents="true" @toggleExpandObjection="emit('toggleExpandObjection')" />
        <div :class="{'qv-read-only-wrapper': isJobReadOnly }">
                <div :class="{'qv-read-only': !isActionRecord && isJobReadOnly }" class="col-container mdl-shadow--3dp">
                <h1 class="title" style="margin-bottom: 1rem;">Comparable Sales</h1>
                <ValidationContext :validation-set="valuationValidationSet">
                    <proposed-value-form
                        v-if="!isActionRecord"
                        :median-net-rate-saving="medianNetRateSaving"
                        :working-net-rate="workingNetRate"
                        :valuation-validation-set = "valuationValidationSet"
                        :is-maori-land="isMaoriLand"
                        @updateNetRate = "updateNetRate"
                        @submit="handleRecalculate" />
                </ValidationContext>
                <comparable-improved-sale-list
                    selected
                    style="margin-bottom: 1rem;"
                    :pagination-info="uiSortInfoCV"
                    :loading="loading"
                    :sales="selectedImprovedSalesSorted"
                    @refreshComparableSale="refreshComparableSale"
                    @comparableSaleSelected="handleSelection"
                    @saveComparableSale="saveComparableSale"
                    @sort="uiSort($event, 'CV')"
                />
                <div class="qv-flex-row" v-if="!isActionRecord" style="justify-content: center">
                    <div>
                        <div class="qv-input-label">Add Improved Sale</div>
                        <div class="qv-input-group">
                            <input v-model="addImprovedSaleId" class="qv-input" type="number" placeholder="Sale ID">
                            <button
                                @click="addComparableSaleById('CV')"
                                class="mdl-button mdl-button--raised mdl-button--colored"
                            >
                                Add
                            </button>
                        </div>
                        <validation-message :validation-set="addSaleValidationSet" field="addSaleCV" />
                    </div>
                </div>

                <hr>

                <comparable-land-sale-list
                    selected
                    style="margin-bottom: 1rem;"
                    :pagination-info="uiSortInfoLV"
                    :loading="loading"
                    :sales="selectedLandSalesSorted"
                    @comparableSaleSelected="handleSelection"
                    @saveComparableSale="saveComparableSale"
                    @refreshComparableSale="refreshComparableSale"
                    @sort="uiSort($event, 'LV')"
                />
                <div class="qv-flex-row" style="justify-content: center" v-if="!isActionRecord">
                    <div>
                        <div class="qv-input-label">Add Land Sale</div>
                        <div class="qv-input-group">
                            <input v-model="addLandSaleId" class="qv-input" type="number" placeholder="Sale ID">
                            <button
                                @click="addComparableSaleById('LV')"
                                class="mdl-button mdl-button--raised mdl-button--colored"
                            >
                                Add
                            </button>
                        </div>
                        <validation-message :validation-set="addSaleValidationSet" field="addSaleLV" />
                    </div>
                </div>
            </div>
        </div>

        <div :class="{'qv-read-only-wrapper': isJobReadOnly }"  v-if="!props.isActionRecord">
            <div :class="{'qv-read-only': !isActionRecord && isJobReadOnly }" class="col-container mdl-shadow--3dp">
                <comparable-search-form @submit="search" :validation-set="comparableSearchValidation" :taCode="taCode" :selected-comparable-sales-loaded="getComparableSalesFirstLoaded" />
                <comparable-improved-sale-list
                    v-if="searchType=='CV'"
                    :pagination-info="paginationInfo"
                    :searching="searching"
                    :sales="comparableSalesSearchResult"
                    @comparableSaleSelected="handleSelection"
                    @sort="sort"
                />
                <comparable-land-sale-list
                    v-else
                    :pagination-info="paginationInfo"
                    :searching="searching"
                    :sales="comparableSalesSearchResult"
                    @comparableSaleSelected="handleSelection"
                    @sort="sort"
                />
                <paginate
                    v-if="!searching && paginationInfo.totalResultCount > 0"
                    v-model="paginationInfo.page"
                    :page-count="paginationInfo.totalPageCount"
                    :page="paginationInfo.page"
                    @change="onChangePage"
                />
                <div v-if="searching">
                    <div class="results-loading">
                        <div
                            class="loadingSpinner loadingSpinnerSearchResults"
                            style="display: block"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

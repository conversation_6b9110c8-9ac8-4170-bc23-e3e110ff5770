<script setup>
import { ref, computed, watch, onMounted, nextTick, provide, inject } from 'vue';
import { onBeforeRouteLeave, useRouter } from 'vue-router/composables';
import { store } from '@/DataStore';
import { useStore } from '@/composables/useStore';
import useValuerInfo from '@/composables/useValuerInfo';

import CommonObjectionBoxes from '@/components/rollMaintenance/objections/CommonObjectionBoxes.vue';
import ValidationMessages from '@/components/common/ValidationMessages.vue';
import AlertModal from '@/components/common/modal/AlertModal.vue';
import DraftPropertyForm from '@/components/propertyDetails/residential/DraftPropertyForm.vue';
import { ValuationValidationList } from '@/components/rollMaintenance/ratingValuation/common';
import { autoSelectComparableSales as apiAutoSelectComparableSales } from '@/services/ApiPicklistController.js';
import { injectRatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';
import { openQivsInNewTab } from '@/utils/QivsUtils';
import ValidationConfirmationModal from '@/components/rollMaintenance/ratingValuation/ValuationValidationModal.vue';
import useModal from '@/composables/useModal';

const modal = useModal();
const ratingValuationId = inject('ratingValuationId');
const taCode = inject('taCode');
const isJobReadOnly = inject('isJobReadOnly');
const comparableSaleSearchCriteria = inject('comparableSaleSearchCriteria');
const draftPropertyParamsForComparableSales = inject('draftPropertyParamsForComparableSales');
const objectionJobValidationResult = inject('objectionJobValidationResult');
const objectionSteps = [
    'rating-valuation-objection-draft',
    'rating-valuation-objection-compare',
    'rating-valuation-objection-valuation',
    'rating-valuation-objection-complete',
];

const router = useRouter();
const ratingValuationStore = useStore('ratingValuation');
const classificationsStore = useStore('classifications');
const userDataStore = useStore('userData');
const propertyStore = useStore('property');
const currentPropertyDetailsStore = useStore('currentPropertyDetails');
const propertyDraftStore = useStore('propertyDraft');
const { users, loadUsers } = useValuerInfo();
const context = injectRatingValuationContext();

const { property } = propertyStore.state;
const { classifications } = classificationsStore.state;

provide('classifications', classifications);

const {
    propertyDetail: currentPropertyDetail,
    loading: loading,
} = currentPropertyDetailsStore.state;

const {
    propertyDetail,
    formIsStale,
    loading: draftLoading,
    saving: draftSaving,
    exception: draftException,
    validationSet,
} = propertyDraftStore.state;

const { userName } = userDataStore.state;

const {
    ratingValuation,
    propertyActivities,
    exception: valuationException,
    isLoading,
} = ratingValuationStore.state;

const setupCompletedByValuer = ref(false);
const navigateOnSetupComplete = ref(true);
const lastException = ref(null);
const successModalIsOpen = ref(false);
const showGenerateBuildingsModal = ref(false);
const generateBuildingsOptions = ref({});
const alertModalIsOpen = ref(false);
const alertMessage = ref({
    heading: '',
    message: '',
});
const successMessage = ref({
    heading: '',
    message: '',
    navigateTo: null,
});

const qpid = computed(() => ratingValuation.value.ratingUnit.qpid);
const errors = computed(() => objectionJobValidationResult?.propertyDraftValidationResult?.errors);
const warnings = computed(() => objectionJobValidationResult?.propertyDraftValidationResult?.warnings);
const propertyLoaded = computed(() => ratingValuation.value.ratingUnit.qpid === propertyDetail.value?.qpid && property.value);
const propertyId = computed(() => (!isLoading || !isLoading.value) ? ratingValuation.value.ratingUnit.propertyId : null);
watch(() => property.value, onPropertyValueChanged, { deep: true, immediate: true });

onMounted(async () => {
    await loadPropertyDetail();
    await loadCurrentPropertyDetails();
    await getAllUsers();
});

onBeforeRouteLeave(async (to, from, next) => {
    if (objectionSteps.includes(to.name) && !isJobReadOnly.value) {
        const saved = await saveChanges(true, true);
        if (!saved) {
            return;
        }
    }
    next();
});

function showAlertModal(heading, message) {
    alertMessage.value = {
        heading,
        message,
    };
    alertModalIsOpen.value = true;
}

function showSuccess(message = {}) {
    successMessage.value.heading = message.heading || 'Saved.';
    successMessage.value.message = message.message || 'Your changes have been saved.';
    successMessage.value.navigateTo = message.navigateTo || null;
    successModalIsOpen.value = true;
}

function closeSuccessModal() {
    successModalIsOpen.value = false;
    if (!successMessage.value.navigateTo) return;
    router.push(successMessage.value.navigateTo);
}

async function getAllUsers() {
    if (!users.value) {
        loadUsers();
    }
}

async function loadCurrentPropertyDetails() {
    try {
        await currentPropertyDetailsStore.dispatch('getPropertyDetailByPropertyId', propertyId.value);
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
        lastException.value = err;
    }
}

async function loadTAZoneClassification(code) {
    await classificationsStore.dispatch('fetchTAZoneClassification', code);
}

async function popupErrors(errors) {
    const subTitle = `Unable to save until the following ${errors.length} issues are resolved:`;
    const cancelErrorText = 'RETURN TO WORKSHEET';
    const payload = {
        subTitle,
        isError: true,
        cancelErrorText,
        onlyConfirm: false,
        validationList: errors
    }
    const proceedToSave = await modal.show(ValidationConfirmationModal, payload);
    return proceedToSave;
}

async function popupWarnings(warnings) {
    const title = 'Do you want to proceed?';
    const subTitle = 'The following validation checks are failing:';
    const confirmText = 'YES, SAVE WORKSHEET';
    const cancelText = 'NO, RETURN TO WORKSHEET';
    const payload = {
        title,
        subTitle,
        isWarning: true,
        cancelText,
        onlyConfirm: false,
        confirmText,
        validationList: warnings
    }
    const proceedToSave = await modal.show(ValidationConfirmationModal, payload);
    return proceedToSave;
}

async function saveChanges(silent = false, routeLeave = false) {
    if (isJobReadOnly.value) {
        return false;
    }
    let proceedToSave = true;

    if (validationSet.value.hasErrors) {
        proceedToSave = await popupErrors(validationSet.value.errors);
        if (!proceedToSave) {
            return false;
        }
    }

    if (validationSet.value.hasWarnings && !routeLeave) {
        proceedToSave = await popupWarnings(validationSet.value.warnings);
        if (!proceedToSave) {
            return false;
        }
    }

    try {
        await propertyDraftStore.dispatch('savePropertyDraft');
        await ratingValuationStore.dispatch('saveValuation');
        if (!silent) {
            showSuccess();
        }
        return true;
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
        lastException.value = err;
    }
    return false;
}

async function completeSetup(setupByValuer, navigateOnSuccess = true) {
    const route = 'rating-valuation-objection-compare';
    navigateOnSetupComplete.value = navigateOnSuccess;
    setupCompletedByValuer.value = setupByValuer;

    let proceedToSave = true;

    if (validationSet.value.hasErrors > 0) {
        proceedToSave = await popupErrors(validationSet.value.errors);
        if (!proceedToSave) {
            return false;
        }
    }
    if (validationSet.value.hasWarnings > 0) {
        proceedToSave = await popupWarnings(validationSet.value.warnings);
        if (!proceedToSave) {
            return false;
        }
    }

    try {
        await propertyDraftStore.dispatch('savePropertyDraft');
        await ratingValuationStore.dispatch('completeSetup');
        await loadPropertyDetail();
        await ratingValuationStore.dispatch('loadRelatedRollMaintenanceActivities');
        await autoSelectComparableSales();

        showSuccess({
            heading: 'Setup Complete',
            message: 'Your changes have been saved. This property is now ready for valuation.',
            navigateTo: navigateOnSetupComplete.value ? { name: route } : null,
        });
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
        lastException.value = err;
    }
}

async function loadPropertyDetail() {
    try {
        if (ratingValuation.value.propertyDetailId) {
            await propertyDraftStore.dispatch('getPropertyDetail', ratingValuation.value.propertyDetailId);
        } else {
            await propertyDraftStore.dispatch('getPropertyDetail', qpid.value);
        }
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
        lastException.value = err;
    }
}

async function completeSetupWithWarnings() {
    // hideWarningModal();
    try {
        await ratingValuationStore.dispatch('completeSetup');
        await loadPropertyDetail();
        // if (validationSet.value.hasErrors) {
        //     return;
        // }
        await ratingValuationStore.dispatch('loadRelatedRollMaintenanceActivities');
        await autoSelectComparableSales();

        showSuccess({
            heading: 'Setup Complete',
            message: 'Warnings were ignored and your changes have been saved. This property is now ready for valuation.',
        });
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred while communicating with the server: ${err}`);
        lastException.value = err;
    }
}

async function onPropertyValueChanged(value) {
    if (`${value?.qupid}` === `${ratingValuation.value.ratingUnit.qpid}`) {
        const code = value.territorialAuthority.code;
        await store.dispatch('fetchTAZoneClassification', code);
    }
}

async function generatePropertyDetailNewDwellingInformation() {
    try {
        await propertyDraftStore.dispatch('generatePropertyDetailNewDwellingInformation');
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
        lastException.value = err;
    }
}

async function generateBuildingsFromPropertyDetail(data) {
    try {
        await propertyDraftStore.dispatch('generateBuildingsFromPropertyDetail', data);
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
        lastException.value = err;
    }
}

async function autoSelectComparableSales() {
    const { CV, LV } = comparableSaleSearchCriteria.value;
    const body = {
        searchCriteriaCV: {
            searchCriteria: {
                ...CV,
                taCode: taCode.value,
                ratingValuationId:
                    ratingValuationId.value,
            },
            targetProperty:
                draftPropertyParamsForComparableSales.value,
        },
        searchCriteriaLV: {
            searchCriteria: {
                ...LV,
                taCode: taCode.value,
                ratingValuationId:
                    ratingValuationId.value,
            },
            targetProperty:
                draftPropertyParamsForComparableSales.value,
        },
    };

    const { status, message, medianNetRate } = await apiAutoSelectComparableSales(body);
    if (status !== 'SUCCESS' && status !== 'ADDED') {
        showAlertModal('Unexpected Error', `An unexpected error occurred calling autoSelectComparableSales: ${message}`);
    }

    if (medianNetRate >= 0) {
        const ratingValuationToSave = JSON.parse(JSON.stringify(ratingValuation.value));
        ratingValuationToSave.workingNetRate = medianNetRate;
        await ratingValuationStore.dispatch('saveValuation');
    }
}

function updatePropertyDetail($event) {
    propertyDraftStore.commit('setSinglePropertyDetail', $event);
    context.validate();
}

function openQivsImprovementSummary() {
    openQivsInNewTab(userDataStore.getters.qivsImprovementSummaryUrl.value(qpid.value));
}

</script>

<template>
    <div>
        <div v-if="lastException" class="bAlert bAlert-danger exception-message" @click="lastException = null">
            Unexpected Error: {{ lastException }}
        </div>
        <ValidationMessages :errors="errors" :warnings="warnings" />
        <CommonObjectionBoxes :expand-consents="true"
            @toggleExpandObjection=" $emit('toggleExpandObjection')" />
        <div :class="{'qv-read-only-wrapper': isJobReadOnly}">
            <div v-if="propertyLoaded" :class="{'qv-read-only':isJobReadOnly}"
                class="col-container mdl-shadow--3dp">
                <h1 class="title">
                    Draft Property
                </h1>
                <DraftPropertyForm
                    :property-detail="propertyDetail"
                    :property="property"
                    :current-property-detail="currentPropertyDetail"
                    :validation-set="validationSet"
                    :disabled="draftSaving"
                    :highlight="formIsStale"
                    @update:property-detail="updatePropertyDetail"
                    @update:dvr-snapshot="saveChanges"
                    @generate:property-detail="generatePropertyDetailNewDwellingInformation"
                    @generate:buildings="generateBuildingsFromPropertyDetail"
                    @open:qivs-improvement-summary="openQivsImprovementSummary"
                />
                <div class="col-row">
                    <div class="col col-12">
                        <div class="righty">
                            <button data-cy="save-as-draft"
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                :disabled="draftSaving" @click="saveChanges()">
                                Save as Draft
                            </button>
                            <button data-cy="setup-complete-and-value"
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                :disabled="draftSaving" @click="completeSetup(true)">
                                Setup Complete &
                                Value
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <AlertModal v-if="successModalIsOpen" success @close="closeSuccessModal">
            <h1>{{ successMessage.heading }}</h1>
            <p>{{ successMessage.message }}</p>
        </AlertModal>
    </div>
</template>

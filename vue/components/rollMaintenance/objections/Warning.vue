<script setup>
import { ref, unref, computed, onMounted, watch } from 'vue';

const props = defineProps(['header', 'message', 'closeLabel', 'proceedLabel', 'progress', 'isError']);

const emit = defineEmits(['close']);

const progressWidth = computed(() => props.progress ? `${Number.parseFloat(100 * props.progress).toFixed(2)}%` : null);

</script>

<template>
    <div class="warning alertWrapper modal" data-backdrop="static" data-keyboard="false">
        <div data-cy="alert-wrapper" class="alert mdl-shadow--24dp">
            <h3 id="errorHeader" :class="{ 'qv-modal-message': !isError }">{{ header }}</h3>
            <p id="errorMessage1" v-show="!Array.isArray(message)">{{ message }}</p>
            <p id="errorMessage2" v-show="Array.isArray(message)" v-for="msg in message">{{ msg }}</p>
            <p></p>
            <div>
                <div class="qv-modal-progress-bar-container" v-if="progress" v-bind:style="{ '--progress': progressWidth }">
                    <span class="qv-modal-progress-bar"></span>
                </div>
                <div class="qv-modal-progress-label">{{ progressWidth }}</div>
            </div>
            <ul class="alertButtons" v-if="closeLabel || proceedLabel">
                <li data-cy="alert-ok" @click="emit('onProceed')" class="mdl-button mdl-button--mini">{{ proceedLabel }} </li>
                <li data-cy="alert-cancel"  @click="emit('onClose')" class="mdl-button mdl-button--mini">{{ closeLabel }} </li>
            </ul>
        </div>
    </div>
</template>

<style lang="scss">
.qv-modal-progress-bar-container {
    width: 100%;
    margin-top: 20px;
    background-color: #ddd;
    padding: 0.5rem;
}

.qv-modal-progress-bar {
    height: 3rem;
    padding: 0.5rem;
    margin: 0;
    display: inline-block;
    width: var(--progress, 0%);
    max-width: calc(100%);
    min-width: 0.5rem;
    text-align: center;
    background-color: #04AA6D;
    vertical-align: middle;
    transition: width 777ms ease-out;
}

.qv-modal-progress-label {
    height: 3rem;
    display: block;
    text-align: center;
}

.qv-modal-message {
    font-size: 1.8rem;
    color: rgb(74, 144, 226) !important;
    margin-bottom: 0.8rem;
}
</style>
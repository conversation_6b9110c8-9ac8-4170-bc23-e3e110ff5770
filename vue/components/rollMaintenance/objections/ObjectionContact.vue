<script setup>
import { ref, onMounted, computed, watch, inject } from 'vue';
import moment from 'moment';
import { store } from '@/DataStore';
import ValidationMessage from '@/components/common/form/ValidationMessage.vue';
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import YesNoIndeterminateDropdown from '@/components/common/form/YesNoIndeterminateDropdown.vue';
import AlertModal from '@/components/common/modal/AlertModal.vue';
import DatePicker from 'vue2-datepicker';
import Multiselect from 'vue-multiselect';
import 'vue2-datepicker/index.css';
import useModal from '@/composables/useModal';
import ValidationConfirmationModal from '@/components/rollMaintenance/ratingValuation/ValuationValidationModal.vue';
import { checkValidation } from './utils.js';
import { nextTick } from 'vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const emit = defineEmits(['saveDraft', 'requestReview', 'completeValuation', 'setValuer', 'setRegisteredValuer', 'validate', 'validateObjectionContact', 'validateSra']);
const isJobReadOnly = inject('isJobReadOnly');
const isActionRecord = inject('isActionRecord');
const isCurrentUserAdmin = inject('isCurrentUserAdmin');
const isCurrentUserRegisteredValuerAssigned = inject('isCurrentUserRegisteredValuerAssigned');
const isCurrentUserValuerAssigned = inject('isCurrentUserValuerAssigned');
const isAssignedValuerRegistered = inject('isAssignedValuerRegistered');
const isValuerChanged = inject('isValuerChanged');
const isRegisteredValuerChanged = inject('isRegisteredValuerChanged');
const valuers = inject('valuers');
const registeredValuers = inject('registeredValuers');
const valuersLoaded = inject('valuersLoaded');
const props = defineProps({
    objection: {
        type: Object,
        default: () => ({}),
    },
    loaded: {
        type: Boolean,
        default: false
    },
    ratingValuationId: {
        type: String,
        default: null
    },
    latestReview: {
        type: Object,
        default: null
    },
    furtherContactWarnings : {
        type: Array,
        default: () => []
    },
    objectionValidationSet: {
        type: Object,
        default: () => ({})
    }
});

const objectionId = ref(null);
const ratingValuationId = ref(null);
const objectorContactLoaded = ref(false);
const validationSet = ref(null);
const objectorContact = ref({});
const isSaving = ref(false);
const jobStatus = ref(null);
const valuer = computed(() => objectorContact.value?.valuer);
const registeredValuer = computed(() => objectorContact.value?.registeredValuer);
const modal = useModal();

watch(() => valuer.value, (newVal) => {
    emit('setValuer', newVal);
});

watch(() => registeredValuer.value, (newVal) => {
    emit('setRegisteredValuer', newVal);
});

const alertModal = ref({
    isOpen: false,
    heading: 'This will delete the Objector Contact information for the selected row. Are you sure?',
    removingId: null,
    cancelText: 'No, Keep Contact Information',
    cancelAction: () => {
        alertModal.value.isOpen = false;
        alertModal.value.removingId = null;
    },
    confirmText: 'Yes, Delete Contact Information',
    confirmAction: () => {
        alertModal.value.isOpen = false;
        removeContact(alertModal.value.removingId);
    },
});

const objectionLoaded = computed(() => {
    return props.loaded && props.ratingValuationId && (props.objection !== null) && valuersLoaded.value
});

const isTaSignOff = computed(() => {
    return ['TA Sign-off','Valued/Actioned','Rejected','Withdrawn','Deleted'].includes(jobStatus.value?.trim());
})

onMounted(async () => {
    if (objectionLoaded.value) {
        init();
    }
});

watch(objectionLoaded, () => {
    if (objectionLoaded.value) {
        init();
    }
})

watch(objectorContact, (newVal) => {
    if (newVal && valuersLoaded.value) {
        resetObjectorContact();
    }
})

async function init() {
    jobStatus.value =  props.objection.valJobStatus;
    objectionId.value = props.objection.objectionId;
    ratingValuationId.value = props.ratingValuationId;
    objectorContact.value = await getObjectorContact(ratingValuationId.value);
}

const userCanUpdateJob = computed(() => {
    return (isCurrentUserAdmin.value || isCurrentUserRegisteredValuerAssigned.value || (isCurrentUserValuerAssigned.value && isAssignedValuerRegistered.value));
})

// condition for enabling [COMPLETE VALUATION] button:
// 1. The valuer is a registered valuer and the objection job is at the Ready to Value stage and the last review (if any) has not failed.
// 2. At no point a job can be completed when either of Valuer and Registered Valuer is Unassigned.
// 3. The valuer is not registered and the objection job is at the Further Contact Required stage and Contacted is Yes.
const isCompleteValuationEnabled = computed(() => {
    if (!objectionLoaded.value || !objectorContactLoaded.value || isSaving.value) {
        return false;
    }
    if(objectorContact.value?.valuer?.id === 'Unassigned' && objectorContact.value?.registeredValuer?.id === 'Unassigned'){
        return false;
    }
    if(objectorContact.value?.furtherContactTypes?.length > 0 && !objectorContact.value?.contacted){
        return false;
    }
    if (jobStatus.value?.trim() == "Ready to Value" && (props.latestReview == null || props.latestReview.reviewPassed)) {
        return userCanUpdateJob.value;
    }
    if (jobStatus.value?.trim() == "Further Contact Required" && objectorContact.value?.contacted) {
        return (isCurrentUserAdmin.value || isCurrentUserRegisteredValuerAssigned.value || isCurrentUserValuerAssigned.value);
    }
    return false;
})

// The Contacted drop-down list will be disabled for unregistered valuers until the Registered Valuer Review has been approved.
const isContactedEnabled = computed(() => {
    return (userCanUpdateJob.value || (isCurrentUserValuerAssigned.value && props.latestReview?.reviewPassed));
})

//If the valuer is registered, there will be a [REQUEST REVIEW] button to the right of the Registered Valuer drop-down list.
const reviewRequestEnabled = computed (() => {
    return valuer.value?.name !== 'Unassigned' && registeredValuer.value?.name !== 'Unassigned' && isAssignedValuerRegistered.value && !isValuerChanged.value  && !isRegisteredValuerChanged.value ;
})

function getClassification(categoryName, value) {
    const classifications = store.getters.getCategoryClassifications(categoryName);
    return classifications.find(item => item.id == value) ? classifications.find(item => item.id == value) : null;
}

function getValuer(valuerName) {
    return valuerName ? valuers.value.find(item => item.ntUsername?.trim().toLowerCase() == valuerName.trim().toLowerCase()) : null;
}

function getRegisteredValuer(registeredValuerName) {
    return registeredValuerName ? registeredValuers.value.find(item => item.ntUsername?.trim().toLowerCase() == registeredValuerName.trim().toLowerCase()) : null;
}

function getRandomID() {
    const d = new Date().getTime();
    const r = Math.floor(Math.random() * 1000);
    return `TEMP-${d}-${r}`;
}

function emptyContact() {
    return {
        id: getRandomID(),
        contactTypeId: null,
        contactDate: null,
        contactNotes: null
    }
}

function addNewObjectorContact() {
    objectorContact.value.objectionJobObjectorContact.push(emptyContact());
}

function removeContactMessage(id) {
    alertModal.value.removingId = id;
    alertModal.value.isOpen = true;
 }

function removeContact(id){
    objectorContact.value.objectionJobObjectorContact = objectorContact.value.objectionJobObjectorContact.filter(item => item.id != id);
}

async function getObjectorContact(ratingValuationId) {
    objectorContactLoaded.value = false;
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getObjectionContact(ratingValuationId);
        const res = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            }
        });
        const { status, objectorContact } = await res.json();
        if (status !== 'SUCCESS') throw Error(`status: ${status}`);
        objectorContact.inspectionDate = objectorContact.inspectionDate ? moment(objectorContact.inspectionDate).format("YYYY-MM-DD") : null;
        for (const contact of objectorContact.objectionJobObjectorContact) {
            contact.contactDate = contact.contactDate ? moment(contact.contactDate).format("YYYY-MM-DD") : null;
        }
        objectorContactLoaded.value = true;
        return objectorContact;
    }
    catch (error) {
        const message = 'Error calling search getObjectionContact api';
        console.error(message, error);
    }
}

function updateObjectorContacted(val) {
    if (val.length > 0 && objectorContact.value.contacted === null) {
        objectorContact.value.contacted = false;
    }
    if (val.length == 0 && objectorContact.value.contacted === false) {
        objectorContact.value.contacted = null;
    }
}

function update(data) {
    objectorContact.value[data.id] = data.value;
    if(data.id == 'furtherContactTypes'){
        updateObjectorContacted(data.value);
    }
}

function updateObjectorContact(id, propName, value) {
    const contactItem = objectorContact.value.objectionJobObjectorContact.find(item => item.id == id);
    contactItem[propName] = value;
}

function resetObjectorContact() {
    objectorContact.value.inspectionTypeId = getClassification('ObjectionJobInspectionType', objectorContact.value.inspectionTypeId);
    objectorContact.value.valuer = getValuer(objectorContact.value.valuerNTUserName || 'Unassigned');
    objectorContact.value.registeredValuer = getRegisteredValuer(objectorContact.value.registeredValuerNTUserName || 'Unassigned');
    objectorContact.value.furtherContactTypes = objectorContact.value.furtherContactTypes.map(item => {
        return getClassification('ObjectionJobFurtherContactReasonType', item.furtherContactReasonTypeId)
    });
    objectorContact.value.objectionJobObjectorContact = objectorContact.value.objectionJobObjectorContact.map(item => {
        return {
            id: item.id,
            contactNotes: item.contactNotes,
            contactDate: item.contactDate,
            contactTypeId: getClassification('ObjectionJobObjectorContactType', item.contactTypeId)
        }

    });
    if (objectorContact.value.objectionJobObjectorContact.length == 0) {
        objectorContact.value.objectionJobObjectorContact = [emptyContact()];
    }
    furtherContactWarningPrepopulation();
}

function furtherContactWarningPrepopulation() {
    const isFurtherContactTypesEmpty = !objectorContact.value?.furtherContactTypes?.length;
    const warnings = ['15% Decision Variance CV', '15% Decision Variance LV'];
    props.furtherContactWarnings.forEach((item) => {
        const warning = getClassification('ObjectionJobFurtherContactReasonType', item.id);
        if (warnings.includes(warning?.description) && isFurtherContactTypesEmpty) {
            objectorContact.value.furtherContactTypes.push(warning);
        }
    });
    updateObjectorContacted(objectorContact.value.furtherContactTypes);
}

function processObjectionJobObjectorContact(contactArr) {
    return contactArr.filter(item => item.contactDate || item.contactTypeId || item.contactNotes?.trim())
        .map(item => {
            return {
                id: item.id,
                contactTypeCode: item.contactTypeId?.description,
                contactDate: item.contactDate ? moment(item.contactDate).format('DD/MM/YYYY') : null,
                contactTypeId: item.contactTypeId?.id,
                contactNotes: item.contactNotes?.trim()
            }
        });
}

function processPayload(payload) {
    return {
        "objectionId": objectionId.value,
        "ratingValuationId": ratingValuationId.value,
        "valuer": payload.valuer?.ntUsername || 'Unassigned',
        "registeredValuer": payload.registeredValuer?.ntUsername || 'Unassigned',
        "qivsComment": payload.qivsComment?.trim(),
        "inspectionDate": payload.inspectionDate ? moment(payload.inspectionDate).format('DD/MM/YYYY') : null,
        "workDone": payload.workDone,
        "inspectionTypeId": payload.inspectionTypeId?.id ? payload.inspectionTypeId.id : null,
        "contacted": payload.contacted,
        "furtherContactNotes": payload.furtherContactNotes?.trim() || '',
        "furtherContactTypes": payload.furtherContactTypes.map(item => item.id),
        "objectionJobObjectorContact": processObjectionJobObjectorContact(payload.objectionJobObjectorContact)
    }
}

async function updateObjectionContact() {
    const processedPayload = processPayload(objectorContact.value);
    const { url } = jsRoutes.controllers.ApiPicklistController.updateObjectionContact(ratingValuationId.value);
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(processedPayload),
    });
    const result = await res.json();
    if (result.status !== 'SUCCESS') {
        throw Error(`error calling updateObjectionContact api, status: ${result.status}`);
    }
    if (result?.validationSet?.success == false) {
        validationSet.value = result.validationSet;
    } else {
        validationSet.value = null;
        updateObjection(result.objectorContact);
    }
    return result.status == 'SUCCESS' && validationSet.value == null;
}

async function saveDraft() {
    try {
        emit('validateSra');
        await nextTick();
        const validationErrors = props.objectionValidationSet?.errors;
        const validationWarnings = props.objectionValidationSet?.warnings;
        const proceed = await checkValidation(validationErrors, validationWarnings);
        if(!proceed) {
            return;
        }
        isSaving.value = true;
        emit('saveDraft');
        await updateObjectionContact();
    } catch (err) {
        console.error("error in saveDraft:", err);
    } finally {
        isSaving.value = false;
    }
}



async function requestReview() {
    try {
        const objectionContact = processPayload(objectorContact.value);
        emit('validate', objectionContact);
        await nextTick();
        const validationErrors = props.objectionValidationSet?.errors;
        const validationWarnings = props.objectionValidationSet?.warnings;
        const proceed = await checkValidation(validationErrors, validationWarnings);
        if(!proceed) {
            return;
        }
        isSaving.value = true;
        emit('saveDraft');
        const isSaved = await updateObjectionContact();
        if (isSaved) {
            emit('requestReview');
        }
    }
    catch (err) {
        console.error("error in requestReview:", err);
    } finally {
        isSaving.value = false;
    }
}

async function completeValuation() {
    try {
        const objectionContact = processPayload(objectorContact.value);
        emit('validate', objectionContact);
        await nextTick();
        const validationErrors = props.objectionValidationSet?.errors;
        const validationWarnings = props.objectionValidationSet?.warnings;
        const proceed = await checkValidation(validationErrors, validationWarnings);
        if(!proceed) {
            return;
        }
        isSaving.value = true;
        emit('saveDraft');
        const isSaved = await updateObjectionContact();
        if (isSaved) {
            emit('completeValuation');
        }
    }
    catch (err) {
        console.error("error in completeValuation:", err);
    } finally {
        isSaving.value = false;
    }
}
function sortById(a, b) {
    if (a.id < b.id) {
        return -1;
    }
    return 1;
}

function updateObjection(data){
    props.objection.valuer = data.valuer;
    props.objection.valuerNtUsername = data.valuerNTUserName;
    props.objection.registeredValuer = data.registeredValuer;
    props.objection.registeredValuerNtUsername = data.registeredValuerNTUserName;
}

defineExpose({
    updateObjectionContact
})
</script>

<template>
    <ValidationProvider path="objectionContact">
        <div :class="{'qv-read-only-wrapper': isJobReadOnly && !isValuerChanged && !isRegisteredValuerChanged}">
            <div :class="{'qv-read-only': !isActionRecord && isJobReadOnly && !isValuerChanged && !isRegisteredValuerChanged }" class="col-container mdl-shadow--3dp">
                <h1 class="title" style="margin-bottom: 1rem;">Objector Contact</h1>
                <div class="qv-comparable-sale-form" :class="{ 'qv-disabled': isSaving }">
                    <div class="qv-flex-row" style="justify-content: space-between">
                        <div class="col col-5">
                            <label>
                                <span class="label">Inspection Type</span>
                                <ValidationWrapper :path="FIELDS.INSPECTION_TYPE_ID">
                                    <classification-dropdown id="inspectionTypeId" category="ObjectionJobInspectionType" :value="objectorContact.inspectionTypeId" label="code" :sortFunction="sortById" @input="update" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-3">
                            <label>
                                <span class="label">Date of Inspection</span>
                                <ValidationWrapper :path="FIELDS.INSPECTION_DATE">
                                    <date-picker v-model="objectorContact.inspectionDate" type="date" format="D/M/YYYY"
                                        value-type="YYYY-MM-DD">
                                    </date-picker>
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-2">
                            <label>
                                <span class="label">New Work</span>
                                <ValidationWrapper :path="FIELDS.WORK_DONE">
                                    <yes-no-indeterminate-dropdown id="workDone" :value="objectorContact.workDone" @input="update" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-9">
                            <label>
                                <span class="label" >QIVS Comment
                                    <span class="qv-italic-text"> (Add a brief Summary for TLA Approval)</span>
                                </span>
                                <ValidationWrapper :path="FIELDS.QIVS_COMMENT">
                                    <input id="qivsComment" type="text" maxlength="200" v-model="objectorContact.qivsComment">
                                </ValidationWrapper>
                            </label>
                        </div>
                    </div>
                    <div v-for="(contact, index) in objectorContact.objectionJobObjectorContact" :key="contact.id"
                        class="qv-flex-row" style="justify-content: space-between">
                        <div class="col col-5">
                            <label>
                                <span class="label">Objector Contact</span>
                                <ValidationWrapper :path="FIELDS.CONTACT_TYPE_ID" :index="index">
                                    <classification-dropdown id="contactTypeId" category="ObjectionJobObjectorContactType"
                                        :value="contact.contactTypeId" label="code" :sortFunction="sortById"
                                        @input="(data) => updateObjectorContact(contact.id, 'contactTypeId', data.value)" />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-3">
                            <label>
                                <span class="label">Date of Contact</span>
                                <ValidationWrapper :path="FIELDS.CONTACT_DATE" :index="index">
                                    <date-picker v-model="contact.contactDate" type="date" format="D/M/YYYY"
                                        value-type="YYYY-MM-DD">
                                    </date-picker>
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-9">
                            <label>
                                <span class="label">Objector Contact Notes</span>
                                <ValidationWrapper :path="FIELDS.CONTACT_NOTES" :index="index">
                                    <input id="objectorContactNotes" type="text" maxlength="5000"
                                        v-model="contact.contactNotes">
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-2">
                            <div class="righty">
                                <label>
                                    <span class="label">&nbsp;</span>
                                    <i v-if="index > 0" class="material-icons" @click="removeContactMessage(contact.id)"></i>
                                    <i class="material-icons" @click="addNewObjectorContact"></i>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="qv-flex-row" style="justify-content: space-between">
                        <div v-if="!isActionRecord" class="col col-5">
                            <label>
                                <span class="label">Further Objector Contact Required</span>
                                <ValidationWrapper :path="FIELDS.FURTHER_CONTACT_TYPES">
                                    <classification-dropdown id="furtherContactTypes" type="text"
                                        category="ObjectionJobFurtherContactReasonType" :sortFunction="sortById"
                                        :value="objectorContact.furtherContactTypes" :taggable="true" :multiple="true"
                                        hide-codes @input="update" />
                                </ValidationWrapper>
                            </label>

                        </div>
                        <div v-if="!isActionRecord" class="col col-12">
                            <label>
                                <span class="label">Further Contact Notes</span>
                                <ValidationWrapper :path="FIELDS.FURTHER_CONTACT_NOTES">
                                    <input id="furtherContactNotes" type="text" maxlength="5000"
                                        v-model="objectorContact.furtherContactNotes">
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div class="col col-2">
                            <label :class="{'qv-read-only': !isActionRecord && !isContactedEnabled }">
                                <span class="label">Contacted</span>
                                <ValidationWrapper :path="FIELDS.CONTACTED">
                                    <yes-no-indeterminate-dropdown id="contacted" :value="objectorContact.contacted"
                                            @input="update"  />
                                </ValidationWrapper>
                            </label>
                        </div>
                    </div>
                    <div class="qv-flex-row" style="justify-content: space-between">
                        <div data-cy="objection-job-valuer" class="col col-5">
                            <label :class="{'qv-read-only': !isActionRecord && isTaSignOff}">
                                <span class="label">Valuer</span>
                                <multiselect v-if="valuersLoaded" :options="valuers" v-model="objectorContact.valuer" label="name"
                                    placeholder="Select Valuer" selectionLimit="1">
                                </multiselect>
                            </label>
                        </div>
                        <div data-cy="objection-job-registered-valuer" class="col col-5">
                            <label :class="{'qv-read-only': !isActionRecord && isTaSignOff}">
                                <span class="label">Registered Valuer</span>
                                <multiselect v-if="valuersLoaded" :options="registeredValuers" v-model="objectorContact.registeredValuer"
                                    label="name" placeholder="Select Registered Valuer" selectionLimit="1">
                                </multiselect>
                            </label>
                        </div>
                        <div class="col col-9">
                            <div class="righty">
                                <label>&nbsp;</label>
                                <div>
                                    <button
                                        data-cy="objection-request-review-button"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                        @click="requestReview()">
                                        Request Review
                                    </button>
                                    <button
                                        data-cy="objection-save-draft-button"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                        :disabled="isSaving"
                                        @click="saveDraft()">
                                        Save as Draft
                                    </button>
                                    <button
                                        data-cy="objection-complete-valuation-button"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                        @click="completeValuation()">
                                        Complete Valuation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <alert-modal v-if="alertModal.isOpen" caution>
                <h1>This will delete the Objector Contact information for the selected row. Are you sure? </h1>
                <template #buttons>
                    <div class="alertButtons">
                        <button
                            class="mdl-button mdl-button--mini lefty"
                            @click="alertModal.cancelAction"
                            data-cy="noWorksheetModalCancelButton"
                        >
                            {{ alertModal.cancelText }}
                        </button>
                        <button
                            id="continue"
                            class="mdl-button mdl-button--mini"
                            @click="alertModal.confirmAction"
                            data-cy="noWorksheetModalContinueButton"
                        >
                            {{ alertModal.confirmText }}
                        </button>
                    </div>
                </template>
            </alert-modal>
        </div>
    </ValidationProvider>
</template>

<style scoped>
.material-icons {
    color: var(--qv-color-mediumblue)
}

.qv-italic-text {
    font-style: italic;
}
</style>

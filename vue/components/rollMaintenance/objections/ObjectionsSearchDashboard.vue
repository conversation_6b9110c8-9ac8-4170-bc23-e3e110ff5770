<script setup>
import { useRouter } from 'vue-router/composables';
import { ref, unref, computed, onMounted, watch, provide, reactive } from 'vue';
import Multiselect from 'vue-multiselect';
import moment from 'moment';
import { store } from '../../../DataStore';
import { addPropertyUUID, fetchRegisteredValuers, fetchValuers } from './utils.js';
import * as fileSaver from "file-saver";
import ObjectionsSearchCriteria from './ObjectionsSearchCriteria.vue';
import ObjectionsSearchResult from './ObjectionsSearchResult.vue';
import ObjectionReinstatement from './ObjectionReinstatement.vue';
import paginate from '../../common/paginate/paginate.vue';
import SortHeader from '../../common/SortHeader.vue';
import AlertModal from /* webpackChunkName: "AlertModal" */'../../common/modal/AlertModal.vue';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import { actionObjectionJob, reinstateObjectionJob } from '@/services/ObjectionController.js';
import { RECEPTIONIST_TYPIST, AREA_VALUER, MANAGING_SENIOR_VALUER, SENIOR_VALUER } from '@/utils/Roles';
import { submitMonarchExport } from '../../reports/utils.js'
import { openMap } from '../../../utils/QivsUtils';

const props = defineProps({
    qpid: {
        default: null
    }
})

const router = useRouter();
const isCurrentUserAdmin = computed(() => store.state.userData.isAdminUser);
const isCurrentUserExternalUpdate = computed(() => store.state.userData.isExternalUser && store.state.userData.isReadOnlyUser === false);
provide('isCurrentUserAdmin', isCurrentUserAdmin);
provide('isCurrentUserExternalUpdate', isCurrentUserExternalUpdate);

const EXPORT_MAX_LIMIT = 50000;
const EXPORT_BATCH_SIZE = 2000;
const BULK_ASSIGN_MAX_LIMIT = 500;
const BULK_APPROVE_MAX_LIMIT = 100;
const objectionsSelected = ref(false);
const isShowMaps = ref(true);
const showModal = ref(false);
const propertiesSelected = ref(0);
const mapUrl = ref(null)
const searchByQPID = computed(() => Number.isInteger(parseInt(props?.qpid)));
const searchCriteria = computed(() => {
    if (searchByQPID.value) {
        return {
            objectionCriteria: {
                qpid: props.qpid,
            },
        };
    }
    return store.state.rollMaintenanceSearch.searchCriteria;
});
const qivsUrl = computed(() => store.state.userData.qivsUrl);

//only internal or TA Users are here in this page
const isInternalUser = computed(() => store.state.userData.isInternalUser);
const canAssignValuers = computed(() => store.getters['userData/userHasMonarchRole']([RECEPTIONIST_TYPIST, AREA_VALUER, MANAGING_SENIOR_VALUER, SENIOR_VALUER ] ));
const isTAUser = computed(() => !store.state.userData.isInternalUser && store.state.userData.isTAUser);
const externalObjectionAccess = computed(() => !store.state.userData.isInternalUser && store.state.userData.externalObjectionAccess);
const taCodes = computed(() => isTAUser.value ? [store.state.userData.userTACode].map(item => parseInt(item,10)) : store.state.taCodes.taCodes.map(item => parseInt(item,10)));
const loadObjectionSearch = computed(() => store.state.rollMaintenanceSearch.loading);
const loading = ref(false);
const exporting = ref(false);
const expectedExportTotal = ref(0);
const objectionSearchResult = ref([]);
const errorMessage = ref(null);
const page = ref(1);
const totalPageCount = ref(0);
const direction = ref('ASC');
const totalResultsVisible = ref(0);
const totalResultCount = ref(0);
const sortField = ref('VALUATION_REFERENCE');
const sortDescending = ref(false);
const selectedObjections = ref([]);
const offset = ref(0);
const limit = ref(100);
const selectAll = ref(false);
const showAssignValuersModal = ref(false);
const showTaApprovalModal = ref(false);
const showBulkApprovalMessage = ref(false);
const valuers = ref([]);
const registeredValuers = ref([]);
const valuerSelected = ref(null);
const registeredValuerSelected = ref(null);
const assignableObjections = computed(() => selectedObjections.value.filter(o => o.isActive));
const approvableObjections = computed(() => selectedObjections.value.filter(o => o.adminStatus === 'TLA Approval for Recommendations'));
const confirmAssign = ref(false);
const confirmApprove = ref(false);
const isWaiting = ref(false);
const assignValuersSuccess = ref(false);
const assignValuersCompleted = ref(false);
const assignValuersRowsAffected = ref(0);
const taApprovalDate = ref(null);
const approveObjectionsCompleted = ref(null);
const approveObjectionsSuccess = ref(null);
const approveObjectionsFailures = ref(0);
const showRejectObjectionModal = ref(false);
const objectionForRejection = ref(null);
const modal = reactive({
            mode: 'warning',
            isOpen: false,
            heading: 'heading',
            message: '',
            messages: [],
            cancelText: 'No',
            cancelAction: () => { },
            confirmText: 'Yes',
            confirmAction: () => { },
            code: '',
});
const validTaApprovalDate = computed(() => taApprovalDate.value && moment(taApprovalDate.value).isValid() && moment(taApprovalDate.value).isSameOrBefore(moment()));
const assignValuersMessages = computed(() => {
    if (isWaiting.value) return { title: 'Assigning Valuers...', message: '', warning: false };
    if (assignValuersCompleted.value) return {
        title: assignValuersSuccess.value ? 'Success' : 'Error',
        message: assignValuersSuccess.value ? `${assignValuersRowsAffected.value} of ${selectedObjections.value.length} objections are successfully assigned. ` : `Bulk assignment error.`,
        warning: (!assignValuersSuccess.value) || assignValuersRowsAffected.value === 0
    };
    return {
        title: 'Assign Valuers',
        message: assignableObjections.value.length > BULK_ASSIGN_MAX_LIMIT ?
            `The number of bulk assignment exceed the limit of ${BULK_ASSIGN_MAX_LIMIT} records.`
            : `${assignableObjections.value.length} of ${selectedObjections.value.length} selected objections are able to be assigned. `,
        warning: assignableObjections.value.length == 0 || assignableObjections.value.length > BULK_ASSIGN_MAX_LIMIT
    };
});
const taApprovalMessages = computed(() => {
    if (isWaiting.value) return { title: 'Approving Objections...', message: '', warning: false };
    if (approveObjectionsCompleted.value) return {
        title: approveObjectionsSuccess.value === false ? 'Error' : 'Success',
        message: approveObjectionsFailures.value ? `${approveObjectionsFailures.value} objections were not successfully approved` : 'Objections successfully approved.',
        warning: approveObjectionsFailures.value > 0,
    };
    return {
        title: 'Approve Objections',
        message: approvableObjections.value.length > BULK_APPROVE_MAX_LIMIT ?
            `The number of bulk approvals exceed the limit of ${BULK_APPROVE_MAX_LIMIT} records.`
            : `${approvableObjections.value.length} of ${selectedObjections.value.length} selected objections are able to be approved. `,
        warning: approvableObjections.value.length == 0 || approvableObjections.value.length > BULK_APPROVE_MAX_LIMIT
    };
});

const showModalMessage = computed(()=> {
    if (selectedObjections.value.length === 0 && objectionsSelected.value == true) {
        return {
            title: 'Create Objection Inspection Report',
            message: 'No objection selected. Please select objections for the Inspection report to be created.',
            warning: true
        };
    }
    if (selectedObjections.value.length > 500 && objectionsSelected.value == true) {
        return {
            title: 'Create Objection Inspection Report Limit exceeded',
            message: 'The report cannot be generated as more than 500 activities have been selected, please reduce your selection.',
            warning: true
        };
    }
    if (propertiesSelected.value > 100 && isShowMaps.value == false) {
        return {
            title: 'Limit exceeded',
            message: 'A maximum of 100 properties can be selected. Please refine your selection before selecting Map',
            warning: true
        };
    }
    if (propertiesSelected.value === 0 && isShowMaps.value == false) {
        return {
            title: 'Show Map',
            message: 'Please select a objection before selecting Map',
            warning: true
        };
    }
});

const address = computed(() => {
    if (property.value?.address) {
        return (
            property.value.address.streetAddress
            + (property.value.address.suburb ? `, ${property.value.address.suburb}` : '')
        );
    }
    return null;
});
const assignMessage = computed(() => {
    let message = '';
    if (valuerSelected.value) message += valuerSelected.value.name;
    if (registeredValuerSelected.value) {
        if (message) message += ' and ';
        message += registeredValuerSelected.value.name;
    }
    return message;
});

onMounted(async () => {
    valuers.value = await fetchValuers();
    registeredValuers.value = await fetchRegisteredValuers();
    if (searchByQPID.value || taCodes.value.length){
        search();
    }
});

watch(() => showTaApprovalModal.value, (newValue) => {
    if (newValue === false) {
        showBulkApprovalMessage.value = false;
    }
});

watch(() => props.qpid, async () => {
    if (taCodes.value.length) {
        search();
    }
});

function getIds(arr, id = 'id') {
    if (Array.isArray(arr)) return arr.map((item) => item[id]);
    return [];
}

function paginationParams() {
    return {
        offset: offset.value,
        limit: limit.value,
        sortBy: sortField.value,
        sortDesc: sortDescending.value,
    };
}

function resetValues() {
    page.value = 1;
    totalPageCount.value = 0;
    direction.value = 'ASC';
    totalResultsVisible.value = 0;
    totalResultCount.value = 0;
    selectedObjections.value = [];
    offset.value = 0;
    limit.value = 100;
    selectAll.value = false;
}

function resetAssignments() {
    valuerSelected.value = null;
    registeredValuerSelected.value = null;
    confirmAssign.value = false;
    isWaiting.value = false;
    assignValuersSuccess.value = false;
    assignValuersRowsAffected.value = 0;
    showAssignValuersModal.value = false;
    if (assignValuersCompleted.value){
        selectedObjections.value = [];
    }
    assignValuersCompleted.value = false;
    showBulkApprovalMessage.value = true;
    objectionsSelected.value = false;
    isShowMaps.value = true;
    showModal.value = false;
    propertiesSelected.value = 0;
}

async function searchObjection(criteria, queryParameters, getTotal = false, doExport = false) {
    if (!getTotal) loading.value = true;
    try {
        const queryParam = getTotal ? { total: true } : queryParameters;
        const queryParamStr = new URLSearchParams(queryParam).toString();
        const { url } = doExport ? jsRoutes.controllers.ApiPicklistController.exportObjection(queryParamStr) :
            jsRoutes.controllers.ApiPicklistController.searchObjection(queryParamStr);
        const res = await fetch(`${url}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(criteria),
        });
        errorMessage.value = null;
        const { status, total, objections } = await res.json();
        if (status !== 'SUCCESS') throw Error(`status: ${status}`);
        objections.forEach((o) => {
            o.propertyId = null;
        });
        return { total, objections };
    }
    catch (error) {
        const message = 'Error calling search objection api';
        console.error(message, error);
        errorMessage.value = message;
        return { total: 0, objections: [] };
    }
    finally {
        if (!getTotal) loading.value = false;
    }
}

function getPayload() {
    const criteria = unref(searchCriteria);
    const { objectionCriteria } = criteria;
    const {
        saleGroups,
        rollNumbers,
        objectionTypes,
        revisionYear,
        objectionCategoryGroups,
        objectionStatuses,
        objectionJobStatuses,
        objectorName,
        valuers,
        registeredValuers,
        ratingUnitCategories,
        qpid,
        rollNumber,
        assessmentNumber,
        assessmentSuffix,
    } = objectionCriteria;
    const searchPayload = {
        taCodes: searchByQPID.value ? [] : unref(taCodes),
        saleGroups,
        rollNumbers,
        objectionTypeId: objectionTypes ? objectionTypes.id : null,
        revisionYear,
        objectionJobStatusIds: getIds(objectionJobStatuses, 'code'),
        ratingUnitCategories,
        categoryGroupIds: getIds(objectionCategoryGroups),
        qpid,
        rollNumber: (saleGroups?.data || rollNumbers?.data) ? '' : rollNumber,
        assessmentNumber,
        assessmentSuffix,
        objectorName,
        valuers,
        registeredValuers,
        objectionStatusIds: getIds(objectionStatuses),
    };
    return searchPayload;
}

async function search() {
    resetValues();
    const searchPayload = getPayload();
    const queryParameters = paginationParams();
    const result = await Promise.all([
        searchObjection(searchPayload, queryParameters, true),
        searchObjection(searchPayload, queryParameters),
    ]);
    const [{ total }, { total: totalReturned, objections }] = result;
    objectionSearchResult.value = objections;
    totalResultsVisible.value = totalReturned;
    totalResultCount.value = total;
    totalPageCount.value = Math.ceil(total / limit.value);

    objectionSearchResult.value = await addPropertyUUID(objections);
}

async function onChangePage(newPage) {
    page.value = newPage;
    offset.value = (newPage - 1) * limit.value;
    selectAll.value = false;
    const searchPayload = getPayload();
    const { total: totalReturned, objections } = await searchObjection(searchPayload, paginationParams());
    objectionSearchResult.value = objections;
    totalResultsVisible.value = totalReturned;

    objectionSearchResult.value = await addPropertyUUID(objections);
}

async function sort(data) {
    sortField.value = data.columnName;
    sortDescending.value = data.direction === 'DESC';
    direction.value = data.direction;
    selectAll.value = false;
    const searchPayload = getPayload();
    const { total: totalReturned, objections } = await searchObjection(searchPayload, paginationParams());
    objectionSearchResult.value = objections;
    totalResultsVisible.value = totalReturned;

    objectionSearchResult.value = await addPropertyUUID(objections);
}

function clearAll() {
    selectedObjections.value = [];
}

async function generateSummaryReport() {
    if (selectedObjections.value.length === 0 || selectedObjections.value.length > 500) {
        objectionsSelected.value = true;
        showModal.value = true;
        return;
    }
    const activityIds = selectedObjections.value.map(o => o.activityId);
    const { url } = jsRoutes.controllers.RollMaintenanceController.generateInspectionReport();
    const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({activityIds, reportType: 'OB'}),
        });
    window.open(await res.json(), '_blank');
}

function toggleSelectAll() {
    objectionSearchResult.value.forEach((o) => {
        const i = selectedObjections.value.findIndex((selectedObjection) => selectedObjection.objectionId === o.objectionId);
        if (i >= 0 && selectAll.value === false) {
            selectedObjections.value.splice(i, 1);
        } else if (i < 0 && selectAll.value == true) {
            selectedObjections.value.push(o);
        }
    });
}

function isSelected(objectionId) {
    return selectedObjections.value.find(o => o.objectionId == objectionId) ? true : false;
}
function toggleSelectObjection(objection) {
    const i = selectedObjections.value.findIndex((o) => o.objectionId == objection.objectionId);
    i >= 0 ? selectedObjections.value.splice(i, 1) : selectedObjections.value.push(objection);
}

async function doAssignValuers(assignValuersPayload) {
    const { url } = jsRoutes.controllers.ApiPicklistController.bulkAssignObjection();
    try {
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(assignValuersPayload),
        });
        return res.json();
    } catch (error) {
        throw Error(`Error calling bulkAssignObjection: ${error}}`);
    }
}

async function assignValuers() {
    isWaiting.value = true;
    try {
        const payload = {
            valuer: valuerSelected.value ? valuerSelected.value.ntUsername : null,
            registeredValuer: registeredValuerSelected.value ? registeredValuerSelected.value.ntUsername : null,
            objectionIds: selectedObjections.value.filter(o => o.isActive).map(o => o.objectionId),
        };
        const { status, rowsAffected } = await doAssignValuers(payload);
        assignValuersSuccess.value = (status === 'SUCCESS');
        if (status !== 'SUCCESS') throw Error(`Error response status calling bulkAssignObjection: ${status}}`);
        assignValuersRowsAffected.value = rowsAffected;
    } catch (err) {
        assignValuersRowsAffected.value = 0;
        assignValuersSuccess.value = false;
        console.error(`ERR-OSD-003: Error calling assignValuers: ${err}`);
    }
    finally {
        assignValuersCompleted.value = true;
        valuerSelected.value = null;
        registeredValuerSelected.value = null;
        isWaiting.value = false;
    }
}


async function onExportCSVClicked() {
    exporting.value = true;

    const searchPayload = getPayload();
    const { total } = await searchObjection(searchPayload, '', true);

    const submitResult = await submitMonarchExport(
        'MONARCH_OBJECTION_EXPORT',
        searchPayload,
        total
    );

    if (submitResult) {
        if (submitResult.cancelText === 'View My Reports') {
            submitResult.cancelAction = () => { router.push({ name: 'report-dashboard-my-reports' }); }
        }

        setModal(submitResult);
    }

    exporting.value = false;
}

function setModal(newModal) {
    Object.keys(newModal).forEach(key => {
        modal[key] = newModal[key];
    });
}
function modalCancel() {
    modal.isOpen = false;
    modal.cancelAction();
}
function modalConfirm() {
    modal.isOpen = false;
    modal.confirmAction();
}


function resetApproval() {
    approveObjectionsSuccess.value = null;
    approveObjectionsFailures.value = 0;
    valuerSelected.value = null;
    registeredValuerSelected.value = null;
    confirmAssign.value = false;
    isWaiting.value = false;
    assignValuersSuccess.value = false;
    assignValuersRowsAffected.value = 0;
    showTaApprovalModal.value = false;
    if (approveObjectionsCompleted.value){
        selectedObjections.value = [];
    }
    approveObjectionsCompleted.value = false;
    confirmApprove.value = false;
    taApprovalDate.value = null;
}

async function approveObjections() {
    isWaiting.value = true;
    try {
        // only need 1 objection per objection job to action all objections in a job
        let filteredSelectedObjections = Array.from(
            new Set(
                selectedObjections.value.filter(o => o.adminStatus === 'TLA Approval for Recommendations' && o.ratingValuationId).map(o => o.ratingValuationId),
            ),
        ).map(ratingValuationId => selectedObjections.value.find(o => o.ratingValuationId === ratingValuationId));
        filteredSelectedObjections = [...filteredSelectedObjections, ...selectedObjections.value.filter(o => o.adminStatus === 'TLA Approval for Recommendations' && !o.ratingValuationId)];
        const responses = [];
        for (const objection of filteredSelectedObjections) {
            const queryParams = {
                fromMonarch: true,
                objectionId: objection.objectionId,
                qpid: objection.qpid,
                taApprovalDate: moment(taApprovalDate.value ?? new Date()).format('YYYY-MM-DD'),
            };
            responses.push(await actionObjectionJob(queryParams));
        }
        const anyFailure = responses.filter(r => r.status !== 200);

        if (anyFailure.length) {
            approveObjectionsSuccess.value = false;
            approveObjectionsFailures.value = anyFailure.length;
        }
    }
    catch (error) {
        approveObjectionsSuccess.value = false;
        console.error(error);
        alert('Oops, something went wrong!');
    }
    finally {
        approveObjectionsCompleted.value = true;
        showBulkApprovalMessage.value = false;
        isWaiting.value = false;
    }
}

async function singleObjectionApproval(objection) {
    selectedObjections.value = [objection];
    showTaApprovalModal.value = true;
    showBulkApprovalMessage.value = true;
}

async function singleObjectionRejection(objection) {
    objectionForRejection.value = objection;
    showRejectObjectionModal.value = true;
}

async function handleReinstatement(reinstatement) {
    isWaiting.value = true;
    try {
        const reinstatementResult = await reinstateObjectionJob(reinstatement);
        if (reinstatementResult.status !== 'ADDED' || !reinstatementResult.reinstatement) {
            alert(`Error while reinstating objection job: ${reinstatementResult.message}`);
        }
    }
    catch (error) {
        console.error(error);
        alert('Oops, something went wrong!');
    }
    finally {
        isWaiting.value = false;
        objectionForRejection.value = null;
        showRejectObjectionModal.value = false;
    }
}

function openQVMap() {
    const qpidListAsString = [... new Set(selectedObjections.value.map(x => x.qpid))].join(',');
    mapUrl.value = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/property/qv-map/0/0/${qpidListAsString}`;
    openMap(qpidListAsString);
}

async function showMap(){
    propertiesSelected.value = [... new Set(selectedObjections.value.map(x => x.qpid))].length;
    if (propertiesSelected.value > 100 || propertiesSelected.value === 0) {
        isShowMaps.value = false;
        showModal.value = true;
        return
    }
    openQVMap();
}

</script>
<template>
    <div class="resultsWrapper roll-maintenance-activity-list">
        <div class="resultsInner-wrapper mdl-shadow--3dp">
            <div class="resultsTitle qv-search-title">
                <h1>Objections</h1>
            </div>
            <objections-search-criteria
                v-if="!searchByQPID"
                @search="search"
                @exportCSVClicked="onExportCSVClicked"
                :exporting="exporting"
                :registeredValuers="registeredValuers"
                :valuers="valuers"
                :isTAUser="isTAUser"
                :isInternalUser="isInternalUser"
                :taCodes="taCodes"
                :externalObjectionAccess="externalObjectionAccess"
            />

            <div class="resultsFound">
                <p v-if="loading">Loading results...</p>
                <p v-else>
                    <span class="qv-obj-button-wrapper">
                        <div class="search-control-buttons-padded" style="display: flex; gap: 10px;">
                            <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored" :class="{ 'qv-disabled': selectedObjections.length == 0 }" v-if="canAssignValuers" @click="showAssignValuersModal = true">
                                Assign Valuers
                            </button>
                            <button data-cy="show-map" :map-url="mapUrl" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored-darkgrey" @click="showMap"> 
                                Map
                                <i class="material-icons">call_made</i>
                            </button>
                            <button data-cy="objection-report" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored-grey" v-if="isInternalUser" @click.prevent="generateSummaryReport">
                                Inspection Report
                            </button>
                            <button data-cy="ta-approval" v-if="isCurrentUserAdmin" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"  @click="showTaApprovalModal = true">
                                TA Approval
                            </button>
                        </div>
                    </span>
                    Showing {{ (totalResultsVisible > 0 ? offset + 1 : 0) | numeral }} to
                    {{ (offset + totalResultsVisible) | numeral }} of {{ totalResultCount | numeral }} results found.
                    {{ selectedObjections.length }} selected.
                    <br />
                    <a href="#" data-cy="clear-all" @click.prevent="clearAll"> Clear All Selected</a>
                </p>
                <p v-if="errorMessage" class="message message-error">
                    {{ errorMessage }}
                </p>
            </div>
            <div class="paginator--top" data-cy="paginate-top">
                <paginate v-if="!loading || totalResultsVisible > 0" v-model="page" :page-count="totalPageCount"
                    :page="page" @change="onChangePage" />
            </div>
            <div v-if="!loading">
                <table data-cy="objection-search-table" class="table">
                    <tr class="sortRow roll-maintenance-activity-list__headerRow">
                        <th class="colHeader activity-list--select">
                            <input type="checkbox" data-cy="obj-select-all" v-model="selectAll" @change="toggleSelectAll" />
                        </th>
                        <th data-cy="objection-result-address" :class="sortField === 'ADDRESS' && 'active'" class="colHeader activity-list--address">
                            <sort-header :direction="direction" :active="sortField === 'ADDRESS'" label="Address"
                                column-name="ADDRESS" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-valref" :class="sortField === 'VALUATION_REFERENCE' && 'active'"
                            class="colHeader activity-list--valRef">
                            <sort-header :direction="direction" :active="sortField === 'VALUATION_REFERENCE'"
                                label="Val Ref" column-name="VALUATION_REFERENCE" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-data-received" :class="sortField === 'DATE_RECEIVED' && 'active'" class="colHeader activity-list--center">
                            <sort-header :direction="direction" :active="sortField === 'DATE_RECEIVED'"
                                label="Date Received" column-name="DATE_RECEIVED" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-category" :class="sortField === 'CATEGORY' && 'active'" class="colHeader activity-list--category">
                            <sort-header :direction="direction" :active="sortField === 'CATEGORY'" label="Category"
                                column-name="CATEGORY" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-objector" :class="sortField === 'OBJECTOR' && 'active'" class="colHeader activity-list--center">
                            <sort-header :direction="direction" :active="sortField === 'OBJECTOR'" label="Objector"
                                column-name="OBJECTOR" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-valuer" :class="sortField === 'VALUER' && 'active'" class="colHeader activity-list--center">
                            <sort-header :direction="direction" :active="sortField === 'VALUER'" label="Valuer"
                                column-name="VALUER" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-registered-valuer" :class="sortField === 'REGISTERED_VALUER' && 'active'"
                            class="colHeader activity-list--center">
                            <sort-header :direction="direction" :active="sortField === 'REGISTERED_VALUER'"
                                label="Reg. Valuer" column-name="REGISTERED_VALUER" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-admin-status" :class="sortField === 'ADMIN_STATUS' && 'active'" class="colHeader activity-list--center">
                            <sort-header :direction="direction" :active="sortField === 'ADMIN_STATUS'"
                                label="Admin Status" column-name="ADMIN_STATUS" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-valuation-job-status" :class="sortField === 'VAL_JOB_STATUS' && 'active'" class="colHeader activity-list--center">
                            <sort-header :direction="direction" :active="sortField === 'VAL_JOB_STATUS'"
                                label="Val. Job Status" column-name="VAL_JOB_STATUS" @onchange="sort" />
                        </th>
                        <th data-cy="objection-result-docs" :class="sortField === 'ATTACHMENTS' && 'active'" class="colHeader activity-list--docs">
                            <sort-header :direction="direction" :active="sortField === 'ATTACHMENTS'"
                                label="Docs" column-name="ATTACHMENTS" />
                        </th>
                        <th class="colHeader activity-list--action">
                            <span />
                        </th>
                    </tr>
                    <objections-search-result v-for="objection in objectionSearchResult" :key="objection.objectionId"
                        :objection="objection" :qivs-url="qivsUrl" :selected="isSelected(objection.objectionId)"
                        :isTAUser="isTAUser" :isInternalUser="isInternalUser" :externalObjectionAccess="externalObjectionAccess" :class="{ 'disable-rows': loading }" @checked="toggleSelectObjection"
                        @singleObjectionApproval="singleObjectionApproval($event)"
                        @singleObjectionRejection="singleObjectionRejection($event)"
                        />
                </table>
                <paginate v-if="!loading || totalResultsVisible > 0" v-model="page" :page-count="totalPageCount" :page="page" @change="onChangePage" />
            </div>
            <div v-if="!loading && totalResultCount === 0" class="no-results">
                <p>No roll maintenance activities found.</p>
                <p>Adjust search criteria and try again.</p>
            </div>
            <div v-if="loading" class="results-loading">
                <div class="loadingSpinner loadingSpinnerSearchResults" />
            </div>
        </div>
        <objection-reinstatement
            :should-show-modal="showRejectObjectionModal"
            :objection="objectionForRejection"
            :loading="isWaiting"
            reason-wording="Reason for Rejection"
            @reinstatement="handleReinstatement"
            @hideModal="showRejectObjectionModal = false"
        />
        <alert-modal v-if="showAssignValuersModal" :success="!assignValuersMessages.warning">

            <h3>{{ assignValuersMessages.title }}</h3>
            <p :class="{ warning: assignValuersMessages.warning }">{{ assignValuersMessages.message }}</p>

            <div
                v-if="assignableObjections.length > 0 && assignableObjections.length <= BULK_ASSIGN_MAX_LIMIT && !isWaiting && !assignValuersCompleted">
                <label class="valuer-select">
                    <span class="label">Valuer</span>
                    <multiselect v-model="valuerSelected" :options="valuers" label="name" track-by="name"
                        :close-on-select="true" />
                </label>
                <label class="valuer-select">
                    <span class="label">Registered Valuer</span>
                    <multiselect v-model="registeredValuerSelected" :options="registeredValuers" label="name"
                        track-by="name" :close-on-select="true" />
                </label>
                <label class="confirm-assign">
                    <input type="checkbox" v-model="confirmAssign" id="confirmAssign">
                    <span>Are you sure you want to bulk assign {{ assignMessage }} to {{ assignableObjections.length }}
                        objections?</span>
                </label>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button id="close" class="mdl-button mdl-button--mini lefty" @click="resetAssignments()">
                        CLOSE
                    </button>
                    <button v-if="confirmAssign && assignMessage" id="assign-valuers"
                        class="mdl-button mdl-button--mini" @click="assignValuers">
                        ASSIGN
                    </button>
                </div>
            </template>
        </alert-modal>
        <!-- TA Approval Modal -->
        <alert-modal v-if="showTaApprovalModal" :success="!taApprovalMessages.warning">

            <h3>{{ taApprovalMessages.title }}</h3>
            <p v-if="showBulkApprovalMessage" style="font-weight: bold;margin-bottom: 0.5rem;">
                Are you sure? Objections can be approved in bulk using the TA Approval button above.
            </p>
            <p :class="{ warning: taApprovalMessages.warning }">{{ taApprovalMessages.message }}</p>

            <div
                v-if="approvableObjections.length > 0 && approvableObjections.length <= BULK_APPROVE_MAX_LIMIT && !isWaiting && !approveObjectionsCompleted">
                <label style="width:30%">
                    <span class="label">TA Approval Date</span>
                    <date-picker
                        class="qv-mx-datepicker"
                        v-model="taApprovalDate"
                        type="date"
                        format="D/M/YYYY"
                        value-type="YYYY-MM-DD" >
                    </date-picker>
                </label>
                <div class="qv-error" v-if="!validTaApprovalDate">Choose a valid date, not in the future</div>
                <label class="qv-confirm-approve">
                    <input type="checkbox" v-model="confirmApprove" id="confirmApprove">
                    <span>Are you sure you want to bulk approve {{ approvableObjections.length }} objections?</span>
                </label>
            </div>
            <div v-if="isWaiting">
                <div class="loadingSpinner loadingSpinnerSearchResults" />
            </div>
            <template v-slot:buttons>
                <div class="alertButtons">
                    <button id="close" class="mdl-button mdl-button--mini lefty" @click="resetApproval">
                        CLOSE
                    </button>
                    <button v-if="approvableObjections.length && !approveObjectionsCompleted && !isWaiting" id="approve-objections"
                        class="mdl-button mdl-button--mini" :class="{ 'disabled': !confirmApprove || !validTaApprovalDate }" @click="approveObjections">
                        APPROVE
                    </button>
                </div>
            </template>
        </alert-modal>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                        data-cy= 'modal-cancel-button'
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                        data-cy= 'modal-confirm-button'
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
            />
        </alert-modal>
        <alert-modal v-if="showModal">
            <h3>{{ showModalMessage.title }}</h3>
            <p :class="{ warning: showModalMessage.warning }">{{ showModalMessage.message }}</p>
            <template v-slot:buttons>
                <div class="alertButtons">
                    <button id="close" data-cy="close-assign-valuers" class="mdl-button mdl-button--mini righty" @click="resetAssignments">
                        CLOSE
                    </button>
                </div>
            </template>
        </alert-modal>
    </div>
</template>

<style lang="scss" scoped="true" src="../rollMaintenance.scss">

</style>
<style lang="scss" scoped="true" src="../search/searchRollMaintenance.scss">

</style>
<style lang="scss" scoped="true" src="./objectionsSearchDashBoard.scss">

</style>
<style lang="scss" scoped="true">
.warning {
    color: red;
}

.confirm-assign {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

#confirmAssign, #confirmApprove {
    margin-right: 1rem;
}

.valuer-select {
    margin: 0.5rem 0;
}

#assign-valuers, #approve-objections {
    color: white;
    background: #283c64;
}

.no-results {
    text-align: center;
    font-size: 1.5em;
    padding: 1em 0;
}

.bulk-assign {
    display: block;
    margin-top: 1rem;
    background: #283c64;
    border: none;
    box-shadow: 1px 1px darkgrey;
    color: white;
    border-radius: 2px;
    padding: 2px 8px;
    font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
    font-size: 13px;
    font-weight: 600;
    font-style: normal;
    text-transform: uppercase;
}

.qv-obj-button-wrapper {
    display: flex;
    justify-content: space-between;
}

.qv-confirm-approve {
    display: flex;
    align-items: center;
    margin: 3rem 0.5rem 0 0;
    font-size: 1.3rem;
}

.qv-mx-datepicker {
    width: 30% !important;
}

.mdl-button.mdl-button--colored-dark { 
    background: var(--qv-color-navyblue);
    border: none;
    box-shadow: 1px 1px darkgrey;
    color: white;
    border-radius: 2px;
    font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    text-transform: uppercase;
}

.search-control-buttons-padded{
    padding-top: 7px;
}
</style>

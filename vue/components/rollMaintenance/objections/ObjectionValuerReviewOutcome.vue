<script setup>
import MaterialIcon from 'Common/MaterialIcon.vue';
import Tag from 'Common/Tag.vue';
import { formatDate } from '@/utils/FormatUtils';
const { review } = defineProps(['review']);
</script>

<template>
    <div class="col-container mdl-shadow--3dp qv-mb-3">
        <div class="qv-flex-column">
            <div class="qv-flex-row qv-justify-space-between">
                <div class="qv-flex-column">
                    <div class="qv-input-label">
                        <div class="qv-label-icon qv-pb-2">
                            <label>Registered Valuer</label>
                        </div>
                    </div>
                    <div class="qv-flex-row">
                        <div class="qv-icon-row">
                            <i class="icons8-edit-user-male qv-icon"></i>
                            <span>
                                <h3 class="qv-color-dark qv-font-semibold">{{ review.reviewerFullName }}</h3>
                            </span>
                        </div>
                    </div>
                </div>
                <div>
                    <tag type="darkblue">{{ formatDate(review.reviewCompleted, 'DD/MM/YYYY', false) }}</tag>
                </div>
            </div>
            <div class="qv-flex-column">
                <div class="qv-input-label">
                    <div class="qv-label-icon qv-pb-2">
                        <label>Review Outcome</label>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <tag :icon="review.relevantSalesUsed ? 'done' : 'close'"
                         :type="review.relevantSalesUsed ? 'success' : 'error'"
                         size="lg"
                    >
                        {{ review.relevantSalesUsed ? 'PASS' : 'FAIL' }}
                    </tag>
                    <p>Relevant sales were used</p>
                </div>
                <div class="qv-flex-row">
                    <tag :icon="review.valuesAreAppropriate ? 'done' : 'close'"
                         :type="review.valuesAreAppropriate ? 'success' : 'error'"
                         size="lg"
                    >
                        {{ review.valuesAreAppropriate ? 'PASS' : 'FAIL' }}
                    </tag>
                    <p>The values that were arrived at are appropriate</p>
                </div>
                <div class="qv-flex-row">
                    <tag :icon="review.compliesWithOvg ? 'done' : 'close'"
                         :type="review.compliesWithOvg ? 'success' : 'error'"
                         size="lg"
                    >
                        {{ review.compliesWithOvg ? 'PASS' : 'FAIL' }}
                    </tag>
                    <p>Objection complies with OVG rules</p>
                </div>
                <div class="qv-flex-row">
                    <tag :icon="review.primaryPhotoUpdated ? 'done' : 'close'"
                         :type="review.primaryPhotoUpdated ? 'success' : 'error'"
                         size="lg"
                    >
                        {{ review.primaryPhotoUpdated ? 'PASS' : 'FAIL' }}
                    </tag>
                    <p>Primary photo has been updated</p>
                </div>
                <div class="qv-flex-row qv-pl-2 qv-mt-4" v-if="!review.reviewPassed">
                    <material-icon class="qv-color-error" icon="error" />
                    <div class="qv-flex-column qv-gap-1">
                        <div class="qv-input-label">
                            <div class="qv-label-icon">
                                <label>Reason for failure and suggestions for improvement</label>
                            </div>
                        </div>
                        <p class="qv-text-base">
                            {{ review.reviewFailureReason }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, inject, watch } from 'vue';
import { store } from '@/DataStore';
import { ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';
import { NumeralInput } from '@/components/ui/input';
const { FIELDS } = RatingValuation;

const isTyping = ref(false);

const handleInput = (event) => {
    isTyping.value = true;
};

const props = defineProps({
    workingNetRate: {
        type: Number,
        default: null,
    },
    medianNetRateSaving: {
        type: Boolean,
        default: false,
    },
    valuationValidationSet: {
        type: Object,
        default: null,
        required: false,
    },
    isMaoriLand: {
        type: Boolean,
        default: false,
    }
});
const ratingValuation = inject('ratingValuation');
const emit = defineEmits(['submit', 'updateNetRate']);
const worksheetNetRate = ref(parseInt(props.workingNetRate));
const worksheetLandValue = ref(ratingValuation.value.adoptedValue?.landValue || ratingValuation.value.originalValue.landValue);
const propertyDetail = computed(() => store.state.propertyDraft.propertyDetail);

watch(() => props.workingNetRate, () => {
    worksheetNetRate.value = parseInt(props.workingNetRate);
});

</script>

<template>
    <div class="qv-flex-row" style="justify-content: space-between">
        <div :title="isMaoriLand ? 'Unadjusted CV' : 'Proposed capital value'">
            <p class="label"><strong>{{ isMaoriLand ? 'Proposed UCV' : 'Proposed CV'}}</strong></p>
            <p>{{ ratingValuation.adoptedValue ? ratingValuation.adoptedValue.capitalValue : null | currency }}</p>
        </div>
        <div :title="isMaoriLand ? 'Unadjusted LV' : 'Proposed land value'">
            <p class="label"><strong>{{ isMaoriLand ? 'Proposed ULV' : 'Proposed LV'}}</strong></p>
            <p>{{ ratingValuation.adoptedValue ? ratingValuation.adoptedValue.landValue : null | currency }}</p>
        </div>
        <div :title="isMaoriLand ? 'Unadjusted VI' : 'Proposed value of improvements'">
            <p class="label"><strong>{{ isMaoriLand ? 'Proposed UVI' : 'Proposed VI'}}</strong></p>
            <p>{{ ratingValuation.adoptedValue ? ratingValuation.adoptedValue.valueOfImprovements : null | currency }}</p>
        </div>
        <template v-if="propertyDetail">
            <div title="Land area">
                <p class="label"><strong>Land (Ha)</strong></p>
                <p>{{ propertyDetail.site ? propertyDetail.site.landArea : null }}</p>
            </div>
            <div title="Total floor area">
                <p class="label"><strong>TFA</strong></p>
                <p>{{ propertyDetail.summary ? propertyDetail.summary.totalFloorArea : null }}</p>
            </div>
            <div title="Total living area">
                <p class="label"><strong>TLA</strong></p>
                <p>{{ propertyDetail.summary ? propertyDetail.summary.totalLivingArea : null }}</p>
            </div>
            <div title="Under main roof garages">
                <p class="label"><strong>UMR</strong></p>
                <p>{{ propertyDetail.dvrSnapshot ? propertyDetail.dvrSnapshot.numberOfUnderMainRoofGarages : null }}</p>
            </div>
            <div title="Freestanding garages">
                <p class="label"><strong>FS</strong></p>
                <p>{{ propertyDetail.dvrSnapshot ? propertyDetail.dvrSnapshot.numberOfFreestandingGarages : null }}</p>
            </div>
            <div title="Category code">
                <p class="label"><strong>Category</strong></p>
                <p>{{ propertyDetail.category ? propertyDetail.category.code : null }}</p>
            </div>
        </template>
        <div class="qv-flex-column">
            <label>
                <span class="label">Worksheet Net Rate</span>
                <ValidationWrapper :path="FIELDS.WORKING_NET_RATE" :hideWrapper="isTyping">
                    <NumeralInput
                        :disabled="medianNetRateSaving"
                        v-model="worksheetNetRate"
                        preset="WHOLE_POSITIVE"
                        min="0"
                        @focus="isTyping = true"
                        @input="isTyping = true; emit('updateNetRate', parseInt(worksheetNetRate))"
                        @blur="isTyping = false; emit('updateNetRate', parseInt(worksheetNetRate))"
                    />
                </ValidationWrapper>
            </label>
            <label>
                <span class="label">Worksheet Land Value</span>
                <input v-model="worksheetLandValue" type="number" min="0">
            </label>
        </div>
        <div>
            <p class="label">&nbsp;</p>
            <button class="mdl-button mdl-button--raised mdl-button--colored" @click="emit('submit', { worksheetNetRate, worksheetLandValue })">
                Recalculate
            </button>
        </div>
    </div>
</template>

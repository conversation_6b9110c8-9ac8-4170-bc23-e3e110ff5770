<script setup>
import { ref, computed } from 'vue';
import AlertModal from '../../common/modal/AlertModal.vue';

const reinstatementComment = ref('');
const taRejectedValue = 'TA Rejected';
const reasonRadioSelection = ref(taRejectedValue);
const emit = defineEmits(['reinstatement', 'hideModal']);

const props = defineProps({
    shouldShowModal: {
        type: Boolean,
        required: true,
    },
    objection: {
        default: () => ({}),
        required: true,
    },
    loading: {
        type: Boolean,
        default: () => false,
    },
    heading: {
        type: String,
        default: () => 'Reinstate Objection Job',
    },
    reasonWording: {
        type: String,
        default: () => 'Reason for Reinstatement',
    },
});

const validationError = computed(() => !reinstatementComment.value?.length);

function hideConfirmationModal() {
    reinstatementComment.value = '';
    emit('hideModal');
}

function handleReasonChange(e) {
    reasonRadioSelection.value = e.target.value;
}

function handleReinstatement() {
    const reinstatement = {
        ratingValuationId: props.objection.ratingValuationId,
        objectionId: props.objection.objectionId,
        rejectedByTa: reasonRadioSelection.value == taRejectedValue,
        reinstatementComment: reinstatementComment.value,
    };
    emit('reinstatement', reinstatement);
}
</script>

<template>
    <alert-modal v-if="shouldShowModal" info @close="hideConfirmationModal">
        <h3>{{ loading ? 'Saving...' : props.heading }}</h3>
        <div v-if="loading" class="results-loading">
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
        <div v-else>
            <label>
                <span class="label">{{ props.reasonWording }}</span>
                <textarea v-model="reinstatementComment" style="height: 10rem;" class="qv-input" maxlength="200" />
                <div class="qv-error" :class="{ 'hidden' : !validationError }" style="margin-bottom: 0.5rem; font-size: 1.1rem;">Please enter a reason</div>
            </label>
            <fieldset class="qv-reinstatement-fieldset">
                <input id="taRejected" class="qv-radio-button" type="radio" name="reason" :value="taRejectedValue"
                    checked @change="handleReasonChange">
                <label for="taRejected">TA Rejected</label>
                <input id="other" class="qv-radio-button" type="radio" name="reason" value="Other"
                    @change="handleReasonChange">
                <label for="other">Other</label>
            </fieldset>
        </div>
        <template #buttons>
            <div class="alertButtons">
                <button class="mdl-button mdl-button--mini lefty" @click="hideConfirmationModal">Cancel</button>
                <button id="reinstatement-save" class="mdl-button mdl-button--mini" :class="{ 'disabled' : validationError }" :disabled="validationError" @click="handleReinstatement">Save</button>
            </div>
        </template>
    </alert-modal>
</template>

<style lang="scss">
.hidden {
    visibility: hidden;
}
#reinstatement-save {
    color: white;
    background: #283c64;
}
</style>

$grid-columns:      12;

@mixin make-col($size, $columns: $grid-columns) {
    flex: 0 0 percentage(calc($size / $columns));
    width: percentage(calc($size / $columns));
}
.search-dahsboard {
    .search-title span{
        background-color: transparent;
        padding: 0.8rem 1.2rem 0.2rem 1.6rem;
        margin: 0;
        overflow: auto;
        font-size: 1.6rem;
        font-weight: bold;
        color: #37474f;
        font-weight: 700;
        margin-top: 1.6rem;
    }

    .qvToolbar {
        background: #edf1f5;
    }
       
    .linz-search-criteria {
        .qvToolbar-qivs {
            .md-qivs label {
                font-size: 1.5rem;
                font-weight: bold;
            }
        }
        .message {
            margin: 0px;
        }
        .col-container {
            .icons8-news-filled:before {
                color: #5290db;
                position: relative;
                top: 0.4rem;
                left: 0;
                font-size: 2.4rem;
            }
            label {
                font-size: 1.4rem;
            }
            .title-status-span {
                display: inline-block;
                padding-left: 5px;
            }
            .title-status label {
                input[type="checkbox"]{
                    vertical-align: middle;
                    margin: 0px 2px;
                    width: 1.5rem;
                }
            }
            .qvToolbar-qivs {
                .md-qivs {
                    padding: 0.3rem 0.6rem 0.25rem 0.6rem;
                    label {
                        padding-left: 1rem;
                    }
                }
            }
            .padding-zero {
                padding: 0px;
            }
            .margin-zero {
                margin: 0px;
            }
            .filter-action-buttons {
                display: block;
                padding: 1rem;
            }
            .col {
                &-legal-selection, &-part-section, &-ownerby-selection {
                    flex: 0 0 3%;
                    width: 3%;
                }
                &-legal-search-criteria, &-legal-ownerby-criteria {
                    flex: 0 0 97%;
                    width: 97%;
                }
                &-parcel-type-section {
                    flex: 0 0 22%;
                    width: 22%;
                }
                &-1 {
                    @include make-col(1);
                }
                &-2 {
                    @include make-col(2);
                }
                &-3 {
                    @include make-col(3);
                }
                &-4 {
                    @include make-col(4);
                }
                &-5 {
                    @include make-col(5);
                }
                &-6 {
                    @include make-col(6);
                }
                &-7 {
                    @include make-col(7);
                }
                &-8 {
                    @include make-col(8);
                }
                &-9 {
                    @include make-col(9);
                }
                &-10 {
                    @include make-col(10);
                }
            }
            input[disabled] {
                background-color: #eee;
            }
        }
    }
    .search-result {
        background-color: white;
        .certificate-title-line-parser {
            background: #ccc;
            height: 2px;
            border: none;
            margin: 0;
        }
        .certificate-title-detail {
            margin: 1em 0;
            .certificate-title {
                width: 100%;
                .no-title-msg {
                    color: red;
                    font-size: 1rem;
                }
                .title-reference {
                    display:block;
                    margin-bottom:1rem;
                    font-weight: 600;
                    font-size: 1.8rem;

                    .no-title {
                        color: red;
                    }
                }
                .title-status-part-cancelled {
                    color: #ba8714;
                    background-color: #fcc92a;
                }
                .title-status-cancelled {
                    background-color: #bc3711;
                    color: #d79376;
                }
                .righty {
                    float: right;
                }
                .toolbar {
                    margin-right: 20px;
                    display: block;
                    position: absolute;
                    top: 0;
                    right: 0;
                }
            }
            .certificate-legal-description {
                .col-row {
                    padding: 0;
                    color: white;
                    background-color: #283c64;
                    .certificate-detail-table {
                        text-align: left;
                        width: 100%;
                        th {
                            font-size: 1rem;
                            font-weight: bold;
                            padding: 0 5px;
                            border-right: 4px solid rgba(255,255,255,.15);
                        }
                        td {
                            font-size: 1.2rem;
                            padding: 0 5px; 
                            border-right: 4px solid rgba(255,255,255,.15);
                        }
                        .no-border {
                            border-right: none;
                        }
                    }                    
                    .col {
                        border-right: .1rem solid rgba(255,255,255,.15);
                        box-sizing:border-box;
                        float: left;
                        font-size: 1rem;
                        font-weight: bold;
                        span {
                            display: block;
                            font-size: 1.2rem;
                            a {
                                text-decoration: underline;;
                            }
                        }
                        .owner-details {
                            span {
                                display: inline-block;
                                &:first-child {
                                    width: 55%;
                                }
                                &:nth-child(2) {
                                    width: 18%;
                                }
                                &:nth-child(3) {
                                    width: 10%;
                                }
                                &:last-child {
                                    float: right;
                                    width: 14%;
                                }
                            }
                        }
                    }
                    .landAreaTitle {
                        font-size: 0.9rem;
                    }
                }
                .certificate-other-detail {
                    background: #fff;
                    font-size: 0.9rem;
                    color: rgb(55,61,64);
                    &:not(:first-child) {
                        margin-top: 5px;
                    }
                    .col-12, .no-padding {
                        padding: 0px;
                    }
                    .certificate-detail-table {
                        background: rgba(237, 241, 245, 0.8);
                        td, th {
                            border-right: 4px solid white;
                            padding: 0 5px;
                        }
                        .no-border {
                            border-right: none;
                        }
                        .owners-detail-table {
                            font-size: 1.2rem;
                            width: 100%;
                            td, th {
                                border: none;
                                padding: 0px;
                            }
                            .col-1 {
                                @include make-col(1);
                            }
                            .col-2 {
                                @include make-col(2);
                            }
                            .col-8 {
                                @include make-col(8);
                            }
                        }
                    }
                    .owners-detail-table {
                        font-size: 1.2rem;
                        width: 100%;
                        td, th {
                            border: none;
                            padding: 0px;
                        }
                    }
                    .col {
                        margin-bottom: 0;
                        background: rgba(237,241,245,.8);
                        border-right: 4px solid white;

                        &:last-child {
                            border: none;
                        }
                    }
                }
                .certificate-legal-description-parser {
                    background: white;
                    height: 1px;
                    border: none;
                    margin: 1em 0 0 0;
                }
            }
        }    
    }
    
}
.qv-clear-link {
    font-size: 1.1rem;
    line-height: 1.6;
    color: rgb(252,147,47);
    height: 2.2rem;
    display: block;
    a {
        color: rgb(252,147,47);
    }
}
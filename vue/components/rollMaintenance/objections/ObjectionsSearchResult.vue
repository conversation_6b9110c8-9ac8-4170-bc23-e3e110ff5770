<script setup>
import { RouterLink } from 'vue-router';
import { useRouter } from 'vue-router/composables';
import { computed, unref, watch, inject } from 'vue';
import { initQivsLink } from '../../../utils/CommonUtils';
import { store } from '../../../DataStore';
import { addRatingValuation } from '@/services/ApiPicklistController.js';
import { formatDate } from '../../../utils/FormatUtils';
import MaterialIcon from 'Common/MaterialIcon.vue';
import { actionObjectionJob } from '@/services/ObjectionController.js';

const attachmentsLinkTitle = 'Yes means there are additional attachments other than the Reason for objection';
const router = useRouter();
const props = defineProps({
    objection: {
        type: Object,
        required: true,
    },
    selected: {
        type: Boolean,
        default: () => false,
    },
    qivsUrl: {
        type: String,
        required: true,
    },
    isTAUser: {
        type: <PERSON>olean,
        default: () => false,
    },
    isInternalUser: {
        type: Boolean,
        default: () => false,
    },
    externalObjectionAccess: {
        type: Boolean,
        default: () => false,
    },
});
const emit = defineEmits(['checked', 'singleObjectionApproval', 'singleObjectionRejection']);

const isCurrentUserAdmin = inject('isCurrentUserAdmin');
const isCurrentUserExternalUpdate = inject('isCurrentUserExternalUpdate');

const actionText = computed(() => {
    const objectionJobStatuses = store.state.classifications.classifications.ObjectionJobStatusType;
    const activeJobStatus = objectionJobStatuses.filter(status => status.activeStatus === true);
    const valJobStatus = (props.objection.valJobStatus || '').trim().toLowerCase();
    if (['valued/actioned', 'ta sign-off'].includes(valJobStatus)) {
        return 'view valuation';
    }
    if (activeJobStatus.find(status => status.description.trim().toLowerCase() === valJobStatus)
        && (props.objection.canBeValued || props.objection.ratingValuationId)) {
        //return props.isInternalUser ? 'edit valuation' : ''; //TODO:
        return 'edit valuation';
    }
    return 'qivs';
});

const isHighRisk = computed(() => {
    const highRiskTypeId = store.state.classifications.classifications.ObjectionJobReviewRiskType.find(status => status.description == "High")?.id;
    return props.objection?.reviewRiskTypeId == highRiskTypeId;
});
const ratingValuationException = computed(() => store.state.ratingValuation.exception);
const ratingValuation = computed(() => store.state.ratingValuation.ratingValuation);
const isInternalUser = computed(() => store.state.userData.isInternalUser);
const isTAUser = computed(() => !store.state.userData.isInternalUser && store.state.userData.isTAUser);
const shouldShowTaSignoffButton = computed(() => (isCurrentUserAdmin.value || isCurrentUserExternalUpdate.value) && props.objection.adminStatus === 'TLA Approval for Recommendations');
const externalObjectionAccess = computed(() => !store.state.userData.isInternalUser && store.state.userData.externalObjectionAccess);

async function loadDraftPropertyDetails(objection) {
    await store.dispatch('ratingValuation/getInProgressValuationForActivity', {
        rollMaintenanceActivityId: objection.activityId,
    });
    if (!ratingValuation.value || ratingValuationException.value !== null) {
        alert('Error: Failed to load rating valuation');
        return;
    }
    const { rollMaintenanceActivityIds } = unref(ratingValuation);
    const activityMatch = rollMaintenanceActivityIds.find(activityId => activityId == props.objection.activityId);

    if (activityMatch && !props.objection.ratingValuationId) {
        const { status } = await addRatingValuation(props.objection.objectionId, ratingValuation.value.id);
        if (status !== 'SUCCESS') {
            alert('Error: Failed to add rating valuation id to qivs objection record');
        }
    }
    await store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
    await router.push({
        name: 'rating-valuation-objection-draft',
        params: { ratingValuationId: ratingValuation.value.id },
    });
}

async function viewActionRecord(objection) {
    if (objection?.valJobStatus?.trim()?.toLowerCase() === 'valued/actioned') {
        await store.dispatch('ratingValuation/getCompleteValuationForActivity', props.objection.activityId);
    }
    else {
        await store.dispatch('ratingValuation/getInProgressValuationForActivity', {
            rollMaintenanceActivityId: objection.activityId,
            shouldGenerate: false,
        });
    }
    if (ratingValuation.value) {
        await store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
    }
    if (!ratingValuation.value) {
        alert('Failed to load action record.');
        return;
    }
    await router.push({
        name: 'rating-valuation-objection-action-record',
        params: { ratingValuationId: ratingValuation.value.id },
    });
}

watch(() => props.objection.propertyId, (newValue) => {
    if (newValue) {
        store.dispatch('propertyPhotos/getPropertyPhoto', props.objection.propertyId);
    }
});

const photoUrl = computed(() => {
    const { photoUrlMap } = store.state.propertyPhotos;
    const photo = photoUrlMap[props.objection.propertyId];
    return photo ? photo.smallImageUrl : store.state.propertyPhotos.noPhotoUrl;
});

// TODO: move to util
function capitalise(words) {
    if (words.trim()) {
        return words.trim().split(' ').filter(word => word.trim()).map(word => word[0].toUpperCase() + word.substring(1).toLowerCase()).join(' ');
    }
    return '';
}

// TODO: move to util
function formattedAddress() {
    const { situationNumber, additionalNumber, street, suburb, town, ratingAuthority } = props.objection;
    const suburbFormatted = capitalise(suburb);
    const townFormatted = capitalise(town);
    return `${situationNumber && situationNumber.trim().length ? situationNumber + ' ' : ''}${additionalNumber && additionalNumber.trim().length ? additionalNumber + ' ' : ''}${capitalise(street)}, ${suburbFormatted ? suburbFormatted + ', ' : ''}${townFormatted ? townFormatted + ', ' : ''}${capitalise(ratingAuthority)}`;
}

function qivsAttachments() {
    const { qpid, objectionId } = props.objection;
    initQivsLink(props.qivsUrl, 'objectionAttachments', qpid, objectionId);
}

function qivsObjections() {
    initQivsLink(props.qivsUrl, 'objections', props.objection.qpid);
}

function isInactiveAssessment(objection) {
    return objection.assessmentStatus?.[0]?.toUpperCase() !== 'A';
}

async function approveObjection(objection) {
    emit('singleObjectionApproval', objection);
}

async function rejectObjection(objection) {
    emit('singleObjectionRejection', objection);
}

</script>

<template>
    <tr
        :key="objection.objectionId"
        class="resultsRow activity-list__row"
        :class="{
            overdueJob: objection.status === 'Overdue',
            completedJob: objection.status === 'Completed',
            highlight: selected,
            'yellow-highlight': objection.objectionType === 'Maintenance',
            'red-highlight': isInactiveAssessment(props.objection)
        }"
        @click="emit('checked', objection)"
    >
        <td class="colHeader activity-list--select">
            <input type="checkbox" :checked="selected" @click.stop="" @change="emit('checked', objection)">
        </td>
        <td class="colCell activity-list--address">
            <router-link
                class="row-link"
                :to="{name: 'property', params: {qpid: props.objection.qpid}}"
            >
                <span class="activity-list--thumb">
                    <img
                        class="primaryPhoto_thumb"
                        :src="photoUrl"
                    >
                </span>
                <div class="activity-list--fullAddress">
                    <span>{{ formattedAddress() }}</span>
                </div>
            </router-link>
        </td>
        <td class="colCell activity-list--valRef">
            <!-- todo: service can format valref ? -->
            {{ objection.rollNumber }} / {{ objection.assessmentNumber }} {{ objection.suffix }}
        </td>
        <td class="colCell activity-list--center">
            {{ formatDate(objection.dateReceived, 'DD/MM/YYYY', false) }}
        </td>
        <td class="colCell activity-list--category">
            {{ objection.categoryCode }}
        </td>
        <td class="colCell activity-list--center">
            <a @click.stop="qivsObjections">{{ objection.firstName }} {{ objection.lastName }}</a>
        </td>
        <td class="colCell activity-list--center">
            {{ objection.valuer }}
        </td>
        <td class="colCell activity-list--center">
            {{ objection.registeredValuer }}
        </td>
        <td class="colCell activity-list--center">
            {{ objection.adminStatus }}
        </td>
        <td class="colCell activity-list--center">
            {{ objection.valJobStatus }}
            <i v-if="isHighRisk" class="icon icon-needsInspection" title="High Risk Objection"></i>
        </td>
        <td class="colCell activity-list--docs">
            <a :title="attachmentsLinkTitle" v-if="objection.attachmentCount" @click.stop="qivsAttachments">Yes ↗</a>
            <a :title="attachmentsLinkTitle" v-else @click.stop="qivsObjections">No ↗</a>
        </td>
        <td class="colCell activity-list--action" data-cy="objection-list-action">
            <button
                v-if="actionText == 'edit valuation'"
                @click.stop="loadDraftPropertyDetails(objection)"
                class="action-button edit-valuation">
                Edit Valuation
            </button>
            <div v-else class="ta-sign-off">
                <button
                    v-if="actionText == 'qivs' &&
                        props.objection.categoryCode.startsWith('R') &&
                        props.objection.objectionTypeId === 1 &&
                        props.objection.assessmentStatus === 'I'"
                    @click.stop="qivsObjections()"
                    class="action-button qivs-link"
                    title="Objection is on an inactive assessment">
                    QIVS<i class="material-icons">call_made</i>
                </button>
                <button
                    v-else-if="actionText == 'qivs'"
                    @click.stop="qivsObjections()"
                    class="action-button qivs-link">
                    QIVS<i class="material-icons">call_made</i>
                </button>
                <p
                    v-if="actionText == 'qivs' &&
                        props.objection.categoryCode.startsWith('R') &&
                        props.objection.objectionTypeId === 1 &&
                        ![1,3,5].includes(props.objection.apportionmentId)"
                    class="qv-valuation-error"
                >
                    Not a rating unit.
                </p>
                <button
                    v-if="actionText == 'view valuation'"
                    class="action-button qivs-link"
                    @click.stop="viewActionRecord(objection)"
                >
                    View Valuation
                </button>
                <button
                    v-if="isCurrentUserAdmin && shouldShowTaSignoffButton"
                    class="action-button approve-objection"
                    @click.stop="approveObjection(objection)">
                    Approve
                </button>
                <button
                    v-if="isCurrentUserAdmin && shouldShowTaSignoffButton"
                    class="action-button reject-objection"
                    @click.stop="rejectObjection(objection)">
                    Reject
                </button>
            </div>
        </td>
    </tr>
</template>
<style lang="scss" src="../search/searchRollMaintenance.scss"></style>
<style lang="scss">
.qivs-link {
    background: #fc932f;
    i {
        display: inline-block;
        font-size: 1.4rem;
        vertical-align: text-top;
        width: 1.5rem;
        margin-right: -3px;
    }
}

.edit-valuation {
    background: #283c64;
}

.ta-sign-off {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    button {
        margin: 0.5rem 0;
    }
}

.approve-objection {
    background: var(--qv-color-success);
}

.reject-objection {
    background: var(--qv-color-error);
}

</style>

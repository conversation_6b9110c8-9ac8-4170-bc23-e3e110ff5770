<script setup>
import ValidationMessage from 'Common/form/ValidationMessage.vue';
import SalesGroupAndRolls from '../ratingValuation/common/SalesGroupsAndRolls.vue';
import { ref, inject, computed, watch, onMounted } from 'vue';
import { ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';
import { ValidationContext } from '@/components/ui/validation';

const { FIELDS } = RatingValuation;
const props = defineProps({
    validationSet: {
        type: Object,
        default: () => ({}),
    },
    taCode: {
        type: Number,
        required: false,
        default: null,
    },
    searching: Boolean,
    selectedComparableSalesLoaded: {
        type: Boolean,
        default: false,
    },
});
const hasNoCoordinates = inject('hasNoCoordinates');
const emit = defineEmits(['submit']);
const searchType = ref('CV');
const searchCriteriaCVLV = ref({
    CV: {},
    LV: {},
});

const defaultSearchCriteriaCVLV = inject('comparableSaleSearchCriteria');
const comparableSaleDefaultCriteriaLoaded = inject('comparableSaleDefaultCriteriaLoaded');

const selectedSaleGroupsAndRolls = ref({});
const searchCriteria = ref({});
const taCodes = computed(() => (props.taCode ? [props.taCode] : []));
const selectedRolls = ref([]);
const selectedSaleGroups = ref([]);
const defaultSearchCriteria = ref({});

defineExpose({
    validate() {
        return validateSearchCriteria();
    },
});

// to handle scenario where data is loaded at beginning, such as returning from other step.
onMounted(() => {
    if (comparableSaleDefaultCriteriaLoaded.value) {
        initDefaultCriteria();
        if (props.selectedComparableSalesLoaded) {
            searchComparables();
        }
    }
});

// initialize when first loaded
watch(comparableSaleDefaultCriteriaLoaded, (newVal) => {
    if (newVal) {
        initDefaultCriteria();
        if (props.selectedComparableSalesLoaded) {
            searchComparables();
        }
    }
});

watch(() => props.selectedComparableSalesLoaded, (newVal, oldVal) => {
    if (newVal && !oldVal && comparableSaleDefaultCriteriaLoaded.value) {
        searchComparables();
    }
});

watch(searchType, (newVal, oldVal) => {
    searchCriteriaCVLV.value[oldVal] = searchCriteria.value;
    searchCriteria.value = { ...searchCriteriaCVLV.value[newVal] };
    searchComparables();
});

function validateSearchCriteria() {
    if (searchCriteria.value?.fromLandArea && parseFloat(searchCriteria.value.fromLandArea)) {
        searchCriteria.value.fromLandArea = parseFloat(searchCriteria.value.fromLandArea).toFixed(4);
    }
    if (searchCriteria.value?.toLandArea && parseFloat(searchCriteria.value.toLandArea)) {
        searchCriteria.value.toLandArea = parseFloat(searchCriteria.value.toLandArea).toFixed(4);
    }
    if (searchCriteria.value?.ratingUnitCategories) {
        searchCriteria.value.ratingUnitCategories = searchCriteria.value.ratingUnitCategories.replace(/^,+|,+$/g, '');
    }
    if(searchCriteria.value?.landZoneGroups) {
        searchCriteria.value.landZoneGroups = searchCriteria.value.landZoneGroups.replace(/^,+|,+$/g, '');
    }
}

function searchComparables() {
    validateSearchCriteria();
    emit('submit', {
        ...searchCriteria.value,
        ...selectedSaleGroupsAndRolls.value,
        searchType: searchType.value,
    });
}

function initDefaultCriteria() {
    defaultSearchCriteria.value = { ...defaultSearchCriteriaCVLV.value[searchType.value] };
    searchCriteriaCVLV.value = { ...defaultSearchCriteriaCVLV.value };
    searchCriteria.value = { ...searchCriteriaCVLV.value[searchType.value] };
}

function resetSearchForm() {
    searchCriteriaCVLV.value[searchType.value] = { ...defaultSearchCriteriaCVLV.value[searchType.value] };
    searchCriteria.value = { ...searchCriteriaCVLV.value[searchType.value] };
}

function setSearchCriteriaItem({ id, value }) {
    selectedSaleGroupsAndRolls.value[id] = value;
}

function onSetRolls({saleGroups, rolls} = {}) {
    selectedSaleGroups.value = saleGroups;
    selectedRolls.value = rolls;
    setSearchCriteriaItem({ id: 'saleGroups', value: saleGroups && saleGroups.length > 0 ? saleGroups : null });
    setSearchCriteriaItem({ id: 'rollNumbers', value: rolls && rolls.length > 0 ? rolls : null });
}

</script>

<template>
    <ValidationContext :validation-set="validationSet">
        <div class="qv-comparable-sale-form">
            <p v-if="hasNoCoordinates" style="color: red; padding-bottom: 0.5rem;">
                This property does not have a qpid-parcel match so distance cannot be calculated.
            </p>
            <div class="qv-flex-row" style="justify-content: space-between">
                <div class="qv-toggle">
                    <input id="cv-comps" v-model="searchType" value="CV" type="radio">
                    <label for="cv-comps" data-cy="cv-comparables" class="mdl-button">CV Comparables</label>
                    <input id="lv-comps" v-model="searchType" value="LV" type="radio">
                    <label for="lv-comps" data-cy="lv-comparables" class="mdl-button">LV Comparables</label>
                </div>
                <div>
                    <sales-group-and-rolls :ta-codes="taCodes" :selected-rolls="selectedRolls" :selected-sale-groups="selectedSaleGroups"
                                        @setRolls="onSetRolls" />
                </div>
            </div>
            <template v-if="searchType == 'CV'">
                <div class="col-row">
                    <div data-cy="cv-categories" class="col col-2">
                        <label title="To search for different types of categories enter each separated by a comma, e.g. RC*, RH*">
                            <span class="label">Categories</span>
                            <input v-model="searchCriteria.ratingUnitCategories" type="text"
                                @change="validateSearchCriteria" @keyup.enter="searchComparables">
                        </label>
                    </div>
                    <div data-cy="cv-net-sale-price" class="col col-3">
                        <label>
                            <span class="label">Net Sale Price</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_NSP">
                                    <input data-cy="cv-from-net-sale-price" v-model="searchCriteria.fromNSP" type="number" min="1" :max="searchCriteria.toNSP || undefined"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_NSP">
                                    <input data-cy="cv-to-net-sale-price" v-model="searchCriteria.toNSP" type="number" :min="searchCriteria.fromNSP || 1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                
                            </span>
                        </label>
                    </div>
                    <div data-cy="cv-sale-date" class="col col-3">
                        <label>
                            <span class="label">Sale Date</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_SALE_DATE">
                                    <input data-cy="cv-from-sale-date" v-model="searchCriteria.fromSaleDate" type="text"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_SALE_DATE">
                                    <input data-cy="cv-to-sale-date" v-model="searchCriteria.toSaleDate" type="text"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                    <div data-cy="cv-land-value" class="col col-3">
                        <label>
                            <span class="label">Land Value</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_LAND_VALUE">
                                    <input data-cy="cv-from-land-value" v-model="searchCriteria.fromLandValue" type="number" :min="1" :max="searchCriteria.toLandValue || undefined"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_LAND_VALUE">
                                    <input data-cy="cv-to-land-value" v-model="searchCriteria.toLandValue" type="number" :min="searchCriteria.fromLandValue || 1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                
                            </span>
                        </label>
                    </div>
                    <div data-cy="cv-land-zone" class="col col-1">
                        <label>
                            <span class="label">Land Zone</span>
                            <input v-model="searchCriteria.landZoneGroups" type="text" @change="validateSearchCriteria" @keyup.enter="searchComparables">
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div data-cy="cv-distance" class="col col-2">
                        <label :title="hasNoCoordinates ? 'Distance field is disabled as subject property has no xy co-ordinates. Use Sales Groups and Rolls instead.' : ''">
                            <span class="label">Distance (m)</span>
                            <ValidationWrapper :path="FIELDS.DISTANCE">
                                <input v-model="searchCriteria.distance" type="number" step="1" min="1" :disabled="hasNoCoordinates"
                                    :class="{'qv-disabled':hasNoCoordinates}" @change="validateSearchCriteria" @keyup.enter="searchComparables">
                            </ValidationWrapper>
                        </label>
                    </div>
                    <div data-cy="cv-total-living-area" class="col col-3">
                        <label>
                            <span class="label">Total Living Area</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_TLA">
                                    <input data-cy="cv-from-total-living-area" v-model="searchCriteria.fromTLA" type="number" :min="1" :max="searchCriteria.toTLA || undefined" step="1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_TLA">
                                    <input data-cy="cv-to-total-living-area" v-model="searchCriteria.toTLA" type="number" :min="searchCriteria.fromTLA || 1" step="1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                    <div data-cy="cv-net-rate" class="col col-3">
                        <label>
                            <span class="label">Net Rate</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_NET_RATE">
                                    <input data-cy="cv-from-net-rate" v-model="searchCriteria.fromNetRate" type="number" @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_NET_RATE">
                                    <input data-cy="cv-to-net-rate" v-model="searchCriteria.toNetRate" type="number" @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                    <div data-cy="cv-effective-year-built" class="col col-3">
                        <label>
                            <span class="label">Effective Year Built</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_EYB">
                                    <input data-cy="cv-from-effective-year-built" v-model="searchCriteria.fromEYB" type="number"
                                        maxlength="4" min="1000" :max="searchCriteria.toEYB || undefined" step="1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_EYB">
                                    <input data-cy="cv-to-effective-year-built" v-model="searchCriteria.toEYB" type="number"
                                        maxlength="4" :min="searchCriteria.fromEYB || 1000" step="1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                </div>
            </template>
            <template v-else>
                <div class="col-row">
                    <div data-cy="lv-categories" class="col col-2">
                        <label title="To search for different types of categories enter each separated by a comma, e.g. RC*, RH*">
                            <span class="label">Categories</span>
                            <input v-model="searchCriteria.ratingUnitCategories" type="text"
                                @change="validateSearchCriteria" @keyup.enter="searchComparables">
                        </label>
                    </div>
                    <div data-cy="lv-net-sale-price-range" class="col col-3">
                        <label>
                            <span class="label">Net Sale Price</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_NSP">
                                    <input data-cy="lv-from-net-sale-price-range" v-model="searchCriteria.fromNSP" type="number" min="1" :max="searchCriteria.toNSP || undefined"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_NSP">
                                    <input data-cy="lv-to-net-sale-price-range" v-model="searchCriteria.toNSP" type="number" :min="searchCriteria.fromNSP || 1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                    <div data-cy="lv-sale-date-range" class="col col-3">
                        <label>
                            <span class="label">Sale Date</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_SALE_DATE">
                                    <input data-cy="lv-from-sale-date-range" v-model="searchCriteria.fromSaleDate" type="text"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_SALE_DATE">
                                    <input data-cy="lv-to-sale-date-range" v-model="searchCriteria.toSaleDate" type="text"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                    <div data-cy="lv-land-value-range" class="col col-3">
                        <label>
                            <span class="label">Land Value</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_LAND_VALUE">
                                    <input data-cy="lv-from-land-value-range" v-model="searchCriteria.fromLandValue" type="number" :min="1" :max="searchCriteria.toLandValue || undefined"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_LAND_VALUE">
                                    <input data-cy="lv-to-land-value-range" v-model="searchCriteria.toLandValue" type="number" :min="searchCriteria.fromLandValue || 1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                    <div data-cy="lv-land-zone" class="col col-1">
                        <label>
                            <span class="label">Land Zone</span>
                            <input v-model="searchCriteria.landZoneGroups" type="text" @change="validateSearchCriteria" @keyup.enter="searchComparables">
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div data-cy="lv-distance" class="col col-2">
                        <label :title="hasNoCoordinates ? 'Distance field is disabled as subject property has no xy co-ordinates. Use Sales Groups and Rolls instead.' : ''">
                            <span class="label">Distance (m)</span>
                            <ValidationWrapper :path="FIELDS.DISTANCE">
                                <input v-model="searchCriteria.distance" type="number" step="1" min="1" :disabled="hasNoCoordinates"
                                    :class="{'qv-disabled':hasNoCoordinates}" @change="validateSearchCriteria" @keyup.enter="searchComparables">
                            </ValidationWrapper>
                        </label>
                    </div>
                    <div data-cy="lv-land-area" class="col col-3">
                        <label>
                            <span class="label">Land Area (Ha)</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_LAND_AREA">
                                    <input data-cy="lv-from-land-area" v-model="searchCriteria.fromLandArea" type="number" step="0.0001" min="0.0001"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_LAND_AREA">
                                    <input data-cy="lv-to-land-area" v-model="searchCriteria.toLandArea" type="number" step="0.0001" :min="searchCriteria.fromLandArea || 0.0001"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                    <div data-cy="lv-land-sale-net-rate" class="col col-3">
                        <label
                            title="Calculated as Net Sale Price/(Land Area (Ha) x 10,000)">
                            <span class="label">Land Sale Net Rate</span>
                            <span class="qv-flex-row qv-input-pair">
                                <ValidationWrapper :path="FIELDS.FROM_LAND_SALE_NET_RATE">
                                    <input data-cy="lv-from-land-sale-net-rate" v-model="searchCriteria.fromLandSaleNetRate" type="number" step="1" min="1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                                to
                                <ValidationWrapper :path="FIELDS.TO_LAND_SALE_NET_RATE">
                                    <input data-cy="lv-to-land-sale-net-rate" v-model="searchCriteria.toLandSaleNetRate" type="number" step="1" :min="searchCriteria.fromLandSaleNetRate || 1"
                                        @change="validateSearchCriteria" @keyup.enter="searchComparables">
                                </ValidationWrapper>
                            </span>
                        </label>
                    </div>
                </div>
            </template>
            <div class="col-row">
                <div class="qv-flex-row" :class="{'qv-disabled':!comparableSaleDefaultCriteriaLoaded}" style="justify-content: end">
                    <button data-cy="comparables-reset" class="mdl-button mdl-button--raised" :disabled="searching" @click="resetSearchForm()">
                        Reset
                    </button>
                    <button data-cy="comparables-search" class="mdl-button mdl-button--raised mdl-button--colored" :disabled="searching" @click="searchComparables">
                        Search
                    </button>
                </div>
            </div>
        </div>
    </ValidationContext>
</template>

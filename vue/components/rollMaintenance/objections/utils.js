import json2csv from 'json2csv';
import moment from 'moment';
import useModal from '@/composables/useModal';
import ValidationConfirmationModal from '@/components/rollMaintenance/ratingValuation/ValuationValidationModal.vue';

export const sortFieldMapping = {
    ADDRESS: ['situationNumber', 'additionalNumber', 'street', 'suburb', 'town'],
    VALUATION_REFERENCE: ['rollNumber', 'assessmentNumber', 'suffix'],
    SALE_DATE: ['saleDate'],
    NSP: ['salePriceNet'],
    LV: ['LV'],
    LAND_AREA: ['area'],
    TFA: ['TFA'],
    TLA: ['TLA'],
    UMR: ['UMR'],
    FS: ['FS'],
    CATEGORY: ['categoryCode'],
    ZONE: ['zone'],
    CONTOUR: ['contour'],
    VIEV_SCP: ['viewCode', 'scopeCode'],
    LOT_POS: ['lotPosition'],
    LAND_SNR: ['landSaleNetRate'],
    DISTANCE: ['distance'],
    COMPARABILITY_SCORE: ['matchPercent'],
    BUILDING_NET_RATE: ['netRate'],
};


export function getFormattedDateExport() {
    let currDate = new Date(Date.now());
    return currDate.getFullYear() + '' + currDate.getMonth() + 1 + '' + currDate.getDate();
}

export async function addPropertyUUID(entityWithQpid, qpid = 'qpid') {
    // TODO: techdebt get the property UUID out of qivs db after KOAN postgres migration done.
    const qpidArr = Array.from(new Set(entityWithQpid.filter(entity => entity[qpid]).map(entity => entity[qpid])));
    if (!qpidArr?.length) {
        return entityWithQpid;
    }
    const { resultList } = await searchProperties(qpidArr);
    const properties = resultList.map(result => result.property);
    properties.forEach((property) => {
        entityWithQpid
            .filter(entity => entity[qpid] == property.qupid)
            .forEach((entity) => {
                entity.propertyId = property.id;
            });
    });
    return entityWithQpid;
}

export async function searchProperties(qpids, limit = 100) {
    try {
        const { url } = jsRoutes.controllers.PropertyController.searchProperties();
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({ qupids: qpids, max: limit }),
        });
        return res.json();
    }
    catch (error) {
        const message = `Error calling searchProperties, qpids length: ${qpids.length}, limit: ${limit}`;
        console.error(message, error);
        return { totalResults: 0, properties: [] };
    }
}

export async function getSalesByQpid(qpid) {
    try {
        const { url } = jsRoutes.controllers.SalesSearch.getSalesByQupid();
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({ locationCriteria: { qupid: qpid } }),
        });
        return res.json();
    }
    catch (error) {
        const message = `Error calling getSalesByQpid for qpid: ${qpid}`;
        console.error(message, error);
        return [];
    }
}

export async function openSaleAnalysisPopup(sale) {
    const sales = await getSalesByQpid(sale.qpid);
    const saleMatch = sales.find(s => s.qivsSaleId == sale.saleId);
    let message = 'Unable to find sale from sale service';
    if (saleMatch) {
        message = `Unable to open sale analysis for sale id: ${saleMatch.id}, qivs sale id: ${saleMatch.qivsSaleId}`;
        let url;
        let target;
        if (isResidentialSale(saleMatch)) {
            url = `${window.location.protocol}//${window.location.hostname}:${window.location.port}?saleId=${saleMatch.id}`;
            target = 'SaleAnalysis';
        }
        else if (isRuralSale(saleMatch)) {
            if (saleMatch.canBeAnalysed) {
                url = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/sale-analysis/${saleMatch.qivsSaleId}/rural`;
                target = 'RuralSaleAnalysis';
            } else {
                message = `Unable to open Rural Sale Analysis for qivs sale id: ${saleMatch.qivsSaleId}`;
            }
        }

        if (url) {
            const saleAnalysis = window.open(url, target, 'scrollbars=yes,resizable=yes,height=800,width=1366');
            saleAnalysis.focus();
            return;
        }
    }
    alert(message);
}

export function isResidentialSale(sale) {
    return sale.primaryPropertyCategory && sale.primaryPropertyCategory.code && 'RL'.includes(sale.primaryPropertyCategory.code.charAt(0));
}

export function isRuralSale(sale) {
    return sale.primaryPropertyCategory && sale.primaryPropertyCategory.code && 'ADFHPS'.includes(sale.primaryPropertyCategory.code.charAt(0));
}

export function median(arr){
    if (arr.length == 0) {
        return 0;
      }
      arr.sort((a, b) => a - b);
      const midpoint = Math.floor(arr.length / 2);
      const median = arr.length % 2 === 1 ?
        arr[midpoint] : //If odd length, just take midpoint
        (arr[midpoint - 1] + arr[midpoint]) / 2; // If even length, take median of midpoints
      return median;
}

export function defaultRange(value, delta, roundTo) {
    if (value === null || value === undefined) {
        return { fromValue: null, toValue: null };
    }
    const exp = Math.log10(roundTo);
    const decimal = exp >= 0 ? 0 : -exp;
    const fromValue = Math.round((value - (delta * value)) / roundTo) * roundTo;
    const toValue = Math.round((value + (delta * value)) / roundTo) * roundTo;
    return { fromValue: parseFloat(fromValue.toFixed(decimal)), toValue: parseFloat(toValue.toFixed(decimal)) };
}

export async function fetchRegisteredValuers() {
    const { url } = jsRoutes.controllers.ReferenceData.displayCountersigners();
    try {
        const res = await fetch(url);
        return res.json();
    } catch (error) {
        console.error('ERR-OSD-001: Error fetching registered valuers');
        return [];
    }
}

export async function fetchValuers() {
    const { url } = jsRoutes.controllers.ReferenceData.displayValuers();
    try {
        const res = await fetch(url);
        return res.json();
    } catch (error) {
        console.error('ERR-OSD-002: Error fetching valuers');
        return [];
    }
}
export async function fetchTerritorialAuthorities(){
    const { url } = jsRoutes.controllers.Application.fetchTerritorialAuthorities();
    try {
        const res = await fetch(url);
        return res.json();
    } catch (error) {
        console.error('ERR-OSD-003: Error fetching TerritorialAuthorities');
        return [];
    }
}

export async function generateObjectionReport(objectionId, title, preview = false) {
    const { url } = jsRoutes.controllers.ObjectionController.generateObjectionJobPDF(objectionId, title);
    if (!preview) {
        const target = window.open(url, '_blank');
        target.document.title = title;
        return;
    }
    const res = await fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
    });
    return await res.json();
}

export async function checkValidation(errors, warnings) {
    const modal = useModal();
    let proceed = true;
    if(errors.length > 0) {
        const title = 'Objection Job Validation Failed';
        const subTitle = `The following errors must be rectified before proceeding:`;
        const cancelErrorText = 'RETURN TO JOB COMPLETION';
        const payload = {
            title,
            subTitle,
            isError: true,
            cancelErrorText,
            onlyConfirm: false,
            validationList: errors
        }
        proceed = await modal.show(ValidationConfirmationModal, payload);
        return proceed;
    }
    if(warnings.length > 0) {
        const title = 'Do you want to proceed?';
        const subTitle = 'The following validation checks are failing:';
        const confirmText = 'YES, COMPLETE JOB';
        const cancelText = 'NO, RETURN TO JOB COMPLETION';
        const payload = {
            title,
            subTitle,
            isWarning: true,
            cancelText,
            onlyConfirm: false,
            confirmText,
            validationList: warnings
        }
        proceed = await modal.show(ValidationConfirmationModal, payload);
    }
    return proceed;
}

export function isSaleCurrentPropertyMaoriLand(sale) {
    return sale.currentPropertySummary.landUseData?.isMaoriLand;
}

export function getSaleLandValue(sale) {
    if (isSaleCurrentPropertyMaoriLand(sale)) {
        return sale.currentPropertySummary.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation?.landValue || 0;
    }
    return sale.LV || 0;
}

export function getSaleCapitalValue(sale) {
    if (isSaleCurrentPropertyMaoriLand(sale)) {
        return sale.currentPropertySummary.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation?.capitalValue || 0;
    }
    return sale.currentPropertySummary.currentValuation?.capitalValue || 0;
}

export function getSaleNetRate(sale) {
    let { netRate } = sale;
    if (isSaleCurrentPropertyMaoriLand(sale)) {
        netRate = sale.maoriLandNetRate;
    }
    return netRate > 0 ? netRate : 0;
}

export function isSaleOutsideRevisionRange(revisionDate, sale) {
    const { isAfter, isBefore } = checkSaleDate(revisionDate, sale);
    return isAfter || isBefore;
}

function checkSaleDate(revisionDate, sale) {
    if (!revisionDate) {
        return false;
    }
    const saleDate = moment(sale.saleDate);
    const revisionYear = revisionDate.startOf('year');
    return {
        isAfter: saleDate.isAfter(revisionDate),
        isBefore: saleDate.isBefore(revisionYear),
    };
}

export function isSaleToProcess(sale) {
    return sale.saleProcessingStatusId == 1;
}

export function getSaleInfo(sale, revisionDate) {
    const info = [];
    if (isSaleCurrentPropertyMaoriLand(sale)) {
        info.push('Sale current property is maori land.');
    }
    if (isSaleToProcess(sale)) {
        info.push('Sale status is "To Process".');
    }
    const { isAfter, isBefore } = checkSaleDate(revisionDate, sale);
    if (isAfter) {
        info.push('Sale date is after revision date.');
    }
    if (isBefore) {
        info.push('Sale date is before revision year.');
    }
    if (!info.length) {
        return undefined;
    }
    return info.join(' ');
}

export function getSaleContour(sale) {
    return sale.sale.primaryProperty.massAppraisalData?.classifications?.contour?.code;
}

export function getCapitalValueHeader(sale) {
    return `Current ${isSaleCurrentPropertyMaoriLand(sale) ? 'U' : ''}CV`;
}

<script setup>
import AlertModal from 'Common/modal/AlertModal.vue';
import ObjectionReinstatement from './ObjectionReinstatement.vue';
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router/composables';
import { store } from '@/DataStore';
import { reinstateObjectionJob } from '@/services/ObjectionController.js';
import { generateObjectionReport } from './utils.js';

const router = useRouter();
const props = defineProps({
    objection: {
        default: () => ({}),
        type: Object,
    },
    linkedObjections: {
        default: () => ([]),
        type: Array,
    },
    warnings: {
        default: () => ([]),
        type: Array,
    },
    ratingValuation: {
        default: () => ({}),
        type: Object,
    },
    propertyActivities: {
        default: () => ([]),
        type: Array,
    },
    setupComplete: {
        default: false,
        type: Boolean,
    },
    address: {
        type: String
    },
    shouldShowReinstatementButton: {
        default: false,
        type: Boolean,
    },
    shouldPromptDeleteObjectionJob: {
        default: false,
        type: Boolean,
    },
});
const emit = defineEmits(['closeDeleteConfirmationModal']);

const isWaiting = ref(false);
const deleteConfirmationVisible = ref(false);
const reinstateConfirmationVisible = ref(false);
const deleteConfirmationCustomMessage = ref(null);

const note = ref('');
const saving = ref(false);
const showNotesForValuerModal = ref(false);
const linkedBCActivities = computed(() => {
    return props.propertyActivities.filter(activity => activity.activityType.code == 'BC'
        && activity.status.code !== 'DONE'
        && activity.status.code !== 'CANCELED'
        && props.ratingValuation.rollMaintenanceActivityIds.includes(activity.id));
});
const hasNotes = computed(() => {
    // has notes if at least one linked activity has some notes defined
    if (linkedBCActivities.value.length > 0) {
        return linkedBCActivities.value.some(activity => activity.notes);
    }
    return false;
});
const ratingValuationException = computed(() => store.state.ratingValuation.exception);

const shouldShowViewReportBtn = computed(() => {
    return props.objection?.reportJobId;
});
watch(() => props.shouldPromptDeleteObjectionJob, (shouldPromptDeleteObjectionJob) => {
    if (shouldPromptDeleteObjectionJob) {
        deleteConfirmationCustomMessage.value = 'Category must be residential. Please delete the objection job and value the objection in QIVS.';
        deleteConfirmationVisible.value = true;
    }
});

async function handleReinstatement(reinstatement) {
    isWaiting.value = true;
    try {
        const reinstatementResult = await reinstateObjectionJob(reinstatement);
        if (reinstatementResult.status == 'ADDED' && reinstatementResult.reinstatement) {
            document.location.reload();
        }
        else {
            alert(`Error while reinstating objection job: ${reinstatementResult.message}`);
        }
    }
    catch (error) {
        console.error(error);
        alert('Error: failed to reinstate objection job');
    }
    finally {
        isWaiting.value = false;
        hideConfirmationModal();
    }
}

async function handleDelete() {
    hideConfirmationModal();
    let { activityId: rollMaintenanceActivityId } = props.objection;
    // TECHDEBT fix broken state jobs
    if (!rollMaintenanceActivityId) {
        rollMaintenanceActivityId = props.ratingValuation.rollMaintenanceActivityIds[0];
    }
    const objectionId = props.linkedObjections.map(o => o.objectionId);
    await store.dispatch('ratingValuation/deleteRatingValuation', { rollMaintenanceActivityId, objectionId });
    if (ratingValuationException.value) {
        console.error(ratingValuationException.value);
        alert('Error: failed to delete rating valuation');
        return;
    }
    router.push({ name: 'roll-maintenance', query: { tab: 'objections' } });
}

function hideConfirmationModal() {
    deleteConfirmationCustomMessage.value = null;
    deleteConfirmationVisible.value = false;
    reinstateConfirmationVisible.value = false;
    emit('closeDeleteConfirmationModal');
}

function openNotesForValuer(){
    note.value = '';
    showNotesForValuerModal.value = true;
}

async function handleNotesForValuer() {
    saving.value = true;
    // hide the modal and clear the note
    showNotesForValuerModal.value = false;
    await store.dispatch(`ratingValuation/requireMoreInformation`, { note: note.value, requestPlans: false });
    note.value = '';
    // reload
    store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
    saving.value = false;
}

async function generateReport() {
    const title = `Objection Report - ${props.address}.pdf`.replaceAll('/', '%2F');
    const preview = props.objection?.valJobStatus == 'Valued/Actioned';
    generateObjectionReport(props.objection?.objectionId, title, preview);
}

</script>

<template>
    <div class="qv-flex-column" style="margin-right: 1rem">
        <div>
            <h3 class="section-title" style="">Valuer</h3>
            <div class="qv-icon-row">
                <i class="icons8-edit-user-male qv-icon" />
                <span>{{ objection ? objection.valuer || '-' : '-' }}</span>
            </div>
        </div>
        <div>
            <h3 class="section-title">Registered Valuer</h3>
            <div class="qv-icon-row">
                <i class="icons8-edit-user-male qv-icon" />
                <span>{{ objection ? objection.registeredValuer || '-' : '-'}}</span>
            </div>
        </div>
        <div>
            <div>
                <button
                    :disabled="!setupComplete"
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored qv-sidebar-action-button"
                    @click="generateReport">
                    {{ shouldShowViewReportBtn ? 'View Report' : 'Create Report' }}
                </button>
            </div>
        </div>
        <div>
            <h3 class="section-title">Job Status</h3>
            <ul>
                <li class="md-qivs success md-qivs-wide">
                    <label>{{ objection ? objection.valJobStatus : 'unknown' }}</label>
                </li>
            </ul>
        </div>
        <div>
            <h3 class="section-title">Administration Status</h3>
            <ul>
                <li class="md-qivs info md-qivs-wide">
                    <label>{{ objection ? objection.adminStatus : 'unknown' }}</label>
                </li>
            </ul>
        </div>
        <div>
            <h3 class="section-title">Warnings</h3>
            <ul>
                <li v-for="warning in warnings" class="md-qivs danger md-qivs-wide">
                    <label>{{ warning.description }}</label>
                </li>
            </ul>
        </div>
        <div style="margin-top: 2rem">
            <button
                v-if="shouldShowReinstatementButton"
                class="mdl-button mdl-button--colored-red qv-sidebar-action-button"
                @click="reinstateConfirmationVisible = true"
            >
                Reinstate Job
            </button>
            <objection-reinstatement
                :should-show-modal="reinstateConfirmationVisible"
                :objection="props.objection"
                :loading="isWaiting"
                @reinstatement="handleReinstatement"
                @hideModal="reinstateConfirmationVisible = false"
            />
            <button
                data-cy="delete-objection-job"
                class="mdl-button mdl-button--colored-red qv-sidebar-action-button"
                @click="deleteConfirmationVisible = true"
            >
                Delete Objection Job
            </button>
        </div>
        <div>
            <button v-if="linkedBCActivities.length > 0"
                class="mdl-button mdl-button--raised qv-sidebar-action-button"
                :disabled="saving"
                @click="openNotesForValuer"
            >
                Notes for Valuer
            </button>
            <label>
                <span class="label notes" style="border-bottom: solid 1px #0e3a83; margin-bottom: 0.5rem;">
                    Notes
                </span>
                <span v-if="!hasNotes">—</span>
                <p v-for="a in linkedBCActivities" v-if="a.notes && a.notes.length > 0" style="white-space: pre-wrap;">{{ a.buildingConsent.consentNumber }} - {{ a.notes }}</p>
            </label>
            <alert-modal data-cy="alert-model-delete-objection-job" v-if="deleteConfirmationVisible" warning @close="hideConfirmationModal">
                <h3>Delete Objection Job</h3>
                <p v-if="deleteConfirmationCustomMessage" style="color: red; margin-bottom: 1rem;">{{ deleteConfirmationCustomMessage }}</p>
                <p>Warning: This will delete the current objection job including any updated draft property details. Are you sure?</p>
                <p>Note: This will not delete the Objection, just the Objection Job.</p>
                <template #buttons>
                    <div class="alertButtons">
                        <button class="mdl-button mdl-button--mini lefty" @click="hideConfirmationModal">
                            NO, RETURN TO OBJECTION JOB
                        </button>
                        <button class="mdl-button mdl-button--mini" @click="handleDelete">
                            YES, DELETE OBJECTION JOB
                        </button>
                    </div>
                </template>
            </alert-modal>
            <alert-modal v-if="showNotesForValuerModal" warning >
                <h3>
                    Notes for Valuer
                </h3>
                <p>Enter any note below:</p>
                <textarea
                    v-model="note"
                />
                <template #buttons>
                    <div class="alertButtons">
                        <button
                            id="errorCancel"
                            class="mdl-button mdl-button--mini lefty"
                            @click="showNotesForValuerModal = false"
                        >
                            Cancel
                        </button>
                        <button
                            id="continue"
                            class="mdl-button mdl-button--mini"
                            @click="handleNotesForValuer"
                        >
                            Confirm
                        </button>
                    </div>
                </template>
            </alert-modal>
        </div>
    </div>
</template>

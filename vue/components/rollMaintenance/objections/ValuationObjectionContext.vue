<script setup>
import { ref, computed, watch, provide, onMounted } from 'vue';
import { DateTime } from 'luxon';
import { debounce } from 'lodash';
import { useRouter, useRoute } from 'vue-router/composables';

import { defaultRange } from '@/components/rollMaintenance/objections/utils.js';
import ObjectionJobStepper from '@/components/rollMaintenance/ratingValuation/common/ObjectionJobStepper.vue';
import PropertySummary from '@/components/property/PropertySummary.vue';
import PropertySummaryProposedValues from '../../property/PropertySummaryProposedValues.vue';
import PropertyInfo from '@/components/property/PropertyInfo.vue';
import { ValuationValidationList } from '@/components/rollMaintenance/ratingValuation/common';
import ObjectionSidebar from './ObjectionSidebar.vue';
import { provideRatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';

import useModal from '@/composables/useModal';
import useValuerInfo from '@/composables/useValuerInfo';
import { useStore } from '@/composables/useStore';
import { store } from '@/DataStore';

const { users, valuers, registeredValuers, valuersLoaded, loadUsers, findRegisteredValuerByNtUsername } = useValuerInfo();

const modal = useModal();
const route = useRoute();
const router = useRouter();

const ratingValuationStore = useStore('ratingValuation');
const propertyDraftStore = useStore('propertyDraft');
const userDataStore = useStore('userData');
const propertyStore = useStore('property');

const expandObjection = ref(true);
const zoneLoaded = ref(false);

const contactValuer = ref();
const contactRegisteredValuer = ref();
const shouldPromptDeleteObjectionJob = ref(false);

const {
    ratingValuation,
    exception,
    propertyActivities,
    validationSet,
    loading
} = ratingValuationStore.state;

const {
    validationSet: propertyValidationSet,
    propertyDetail: draftProperty,
    loading: draftLoading,
} = propertyDraftStore.state;

const {
    userName,
    isInternalUser,
    isTAUser,
    isAdminUser,
    isReadOnly,
    isExternalUser,
    externalObjectionAccess,
} = userDataStore.state;

const {
    property,
    taSummary,
} = propertyStore.state;

const debouncedValidate = debounce(validate, 250);
const debouncedSave = debounce(save, 500);

provideRatingValuationContext({
    validate: debouncedValidate,
    save: debouncedSave
});

const ratingValuationId = computed(() => route.params.ratingValuationId);
const hasNoCoordinates = computed(() => !property.value?.coordinates?.latitude || !property?.value?.coordinates?.longitude);
const taCode = computed(() => parseInt(property.value?.territorialAuthority?.code) || null);
const currentRevisionDate = computed(() => taSummary?.value?.currentRevisionDate ? DateTime.fromISO(taSummary.value.currentRevisionDate, 'YYYY-MM-DD') : null);
const toSaleDate = computed(() => currentRevisionDate.value ? currentRevisionDate.value.toFormat('dd/MM/yyyy') : null);
const comparableSaleSearchCriteria = computed(() => {
    return {
        CV: { ...comparableSaleCriteriaCV.value },
        LV: { ...comparableSaleCriteriaLV.value },
    };
});

const comparableSaleDefaultCriteria = computed(() => property.value && draftProperty.value && taSummary.value);

const comparableSaleCriteriaCV = computed(() => {
    const categoryPrefix = draftProperty.value.category.code.trim().slice(0, 2);
    const zonePrefix = draftProperty.value.landUse.landZone.code.trim().slice(0, 1);
    const isRaRdRf = (['RA', 'RD', 'RF'].includes(categoryPrefix));
    const defaultLandValueRange = defaultRange(property.value.currentValuation.landValue, isRaRdRf ? 0.2 : 0.4, 1000);
    const defaultTLARange = defaultRange(draftProperty.value.summary.totalLivingArea, isRaRdRf ? 0.2 : 0.4, 1);
    const effectiveYearBuilt = parseInt(draftProperty.value.summary.effectiveYearBuilt, 10);
    const fromSaleDate = currentRevisionDate.value ? currentRevisionDate.value.minus({ months: 6 }).toFormat('dd/MM/yyyy') : null;
    const criteria = {
        fromNSP: null,
        toNSP: null,
        fromNetRate: null,
        toNetRate: null,
        fromEYB: null,
        toEYB: null,
        ratingUnitCategories: isRaRdRf ? `${categoryPrefix}*` : 'R*',
        fromLandValue: defaultLandValueRange.fromValue,
        toLandValue: defaultLandValueRange.toValue,
        landZoneGroups: isRaRdRf ? `${zonePrefix}*` : null,
        distance: hasNoCoordinates.value ? null : (isRaRdRf ? 1000 : 2000),
        fromTLA: defaultTLARange.fromValue,
        toTLA: defaultTLARange.toValue,
        toSaleDate: toSaleDate.value,
        fromSaleDate,
        fromLandArea: null,
        toLandArea: null,
        fromLandSaleNetRate: null,
        toLandSaleNetRate: null,
    };
    if (effectiveYearBuilt && isRaRdRf) {
        criteria.fromEYB = effectiveYearBuilt - 20;
        const currentYear = parseInt(DateTime.now().year, 10);
        criteria.toEYB = effectiveYearBuilt + 20 <= currentYear ? effectiveYearBuilt + 20 : currentYear;
    }
    return criteria;
});

const comparableSaleCriteriaLV = computed(() => {
    const zonePrefix = draftProperty.value.landUse.landZone.code.trim().slice(0, 1);
    const defaultZone = zonePrefix ? `${zonePrefix}*` : null;
    const defaultLandValueRange = defaultRange(property.value.currentValuation.landValue, 0.5, 1000);
    const defaultLandAreaRange = defaultRange(property.value.landUseData.landArea, 0.5, 0.0001);
    const fromSaleDate = currentRevisionDate.value ? currentRevisionDate.value.minus({ months: 9 }).toFormat('dd/MM/yyyy') : null;
    const criteria = {
        fromNSP: null,
        toNSP: null,
        fromLandSaleNetRate: null,
        toLandSaleNetRate: null,
        ratingUnitCategories: 'RV*',
        fromLandValue: defaultLandValueRange.fromValue,
        toLandValue: defaultLandValueRange.toValue,
        landZoneGroups: defaultZone,
        distance: hasNoCoordinates.value ? null : 2000,
        fromLandArea: defaultLandAreaRange.fromValue,
        toLandArea: defaultLandAreaRange.toValue,
        toSaleDate: toSaleDate.value,
        fromSaleDate,
    };
    return criteria;
});

const draftPropertyParamsForComparableSales = computed(() => {
    if (!draftProperty.value?.qpid) {
        return {};
    }
    return {
        targetDraftQpid: draftProperty.value?.qpid || '',
        targetDraftZone: draftProperty.value?.landUse?.landZone?.code || '',
        targetDraftEYB: draftProperty.value?.summary?.effectiveYearBuilt || '',
        targetDraftTFA: draftProperty.value?.summary?.totalFloorArea || '',
        targetDraftTLA: draftProperty.value?.summary?.totalLivingArea || '',
        targetDraftCategoryCode: property?.value?.category?.code || '',
        targetDraftContourCode: property?.value?.site?.contour?.code || '',
        targetDraftViewCode: property?.value?.site?.view?.code || '',
        targetDraftViewScopeCode: property?.value?.site?.viewScope?.code || '',
    };
});

const objections = computed(() => {
    const objections = [...propertyActivities?.value?.filter(activity => activity.activityType.code == 'OB')];
    if (linkedObjection.value) {
        const index = objections.findIndex(activity => activity.id === linkedObjection.value.activityId);
        objections.splice(index, 1);
        objections.splice(0, 0, { ...linkedObjection.value });
    }
    return objections;
});

const linkedObjection = computed(() => {
    const objections = [...propertyActivities?.value?.filter(activity => activity.activityType.code == 'OB')];
    if (objections?.length && ratingValuation?.value?.rollMaintenanceActivityIds) {
        return objections.find(o => o.ratingValuationId && ratingValuation?.value?.rollMaintenanceActivityIds.includes(o.activityId));
    }
    return null;
});

const linkedObjections = computed(() => {
    if (objections.value?.length && ratingValuation.value?.rollMaintenanceActivityIds) {
        const { rollMaintenanceActivityIds } = ratingValuation.value;
        return objections.value.filter(objection => rollMaintenanceActivityIds.includes(objection.activityId));
    }
    return null;
});

const isJobReadOnly = computed(() => {
    const jobStatus = linkedObjection.value?.valJobStatus?.trim();
    const assessmentStatus = linkedObjection.value?.assessmentStatus?.[0]?.toUpperCase();

    if (jobStatus === 'TA Sign-off' || assessmentStatus !== 'A') {
        return true;
    }

    return jobStatus === 'Registered Valuer Review' && !(isCurrentUserRegisteredValuerAssigned.value || isAdminUser.value);
});

const dataLoaded = ref(false);
const qpid = computed(() => ratingValuation.value?.ratingUnit.qpid);
const valuationLoaded = computed(() =>  !loading.value && ratingValuationId.value === ratingValuation.value?.id && dataLoaded.value)
const propertyId = computed(() => valuationLoaded.value && ratingValuation.value?.ratingUnit ? ratingValuation.value?.ratingUnit.propertyId : null);
const objectionOtherParties = computed(() => {
    if (linkedObjections.value?.length) {
        for (const objection of linkedObjections.value) {
            if (objection.otherParties) return true;
        }
    }
    return false;
});
const objectionRejectedByTa = computed(() => {
    if (linkedObjections.value?.length) {
        for (const objection of linkedObjections.value) {
            if (objection.rejectedByTa) return true;
        }
    }
    return false;
});
const objectionReviewFailed = computed(() => {
    if (linkedObjections.value?.length) {
        for (const objection of linkedObjections.value) {
            if (objection.reviewPassed === false) return true;
        }
    }
    return false;
});
const warnings = computed(() => {
    const warningsArr = [];
    if (showCVWarning.value) warningsArr.push({ id: 1, description: '15% CV Variance' });
    if (showLVWarning.value) warningsArr.push({ id: 2, description: '15% LV Variance' });
    if (objectionRejectedByTa.value) warningsArr.push({ id: 3, description: 'Failed TA Approval' });
    if (objectionOtherParties.value) warningsArr.push({ id: 4, description: 'Other Parties' });
    if (objectionReviewFailed.value) warningsArr.push({ id: 5, description: 'Review Failed' });
    return warningsArr;
});

const shouldShowReinstatementButton = computed(() => (linkedObjection.value && linkedObjection.value.adminStatus && linkedObjection.value.adminStatus.trim() == 'TLA Approval for Recommendations'));
const warningBaseObjects = computed(() => linkedObjections.value && ratingValuation?.value?.adoptedValue);
const showCVWarningObjections = computed(() => {
    for (const objection of linkedObjections.value) {
        if (objection.estimatedCapitalValue
            && shouldWarn(percentageChange(objection.estimatedCapitalValue, ratingValuation.value.adoptedValue.capitalValue))) {
            return true;
        }
    }
    return false;
});

const showCVWarning = computed(() => warningBaseObjects.value && ratingValuation.value.adoptedValue.capitalValue && showCVWarningObjections.value );
const showLVWarningObjections = computed(() => {
    for (const objection of linkedObjections.value) {
        if (objection.estimatedLandValue && shouldWarn(percentageChange(objection.estimatedLandValue, ratingValuation.value.adoptedValue.landValue))) {
            return true;
        }
    }
    return false;
});

const showLVWarning = computed(() => warningBaseObjects.value && ratingValuation.value.adoptedValue.landValue && showLVWarningObjections.value );
const hasReducedCV = computed(() => property.value?.currentValuation && ratingValuation.value?.adoptedValue && (ratingValuation.value?.adoptedValue.capitalValue < property.value?.currentValuation.capitalValue));
const hasLVChanged = computed(() => property.value?.currentValuation && ratingValuation.value?.adoptedValue && ratingValuation.value?.originalValue && (ratingValuation.value?.adoptedValue.landValue != ratingValuation.value?.originalValue.landValue));
const isSetupComplete = computed(() => {
    if (!propertyActivities?.value || !ratingValuation.value) {
        return false;
    }
    // if ALL linked activities are "setupComplete" then true (ie. nothing is false)
    // If one of the linked roll maintenance activities has setupComplete === false, then setupComplete should be false.
    return !propertyActivities?.value?.find(activity => ratingValuation.value.rollMaintenanceActivityIds.includes(activity.id) && activity.setupComplete === false);
});

const isActionRecord = computed(() => route.name === "rating-valuation-objection-action-record");
const address = computed(() => property?.value?.address ? property.value?.address.streetAddress + (property.value?.address.suburb ? `, ${property.value?.address.suburb}` : '') : null);
const currentUserNTUserName = computed(() => `QVNZ\\${userName.value?.trim()}`);
const isCurrentUserRegisteredValuerAssigned = computed(() => contactRegisteredValuer?.value?.ntUsername && currentUserNTUserName.value.toLowerCase() === contactRegisteredValuer?.value?.ntUsername.trim()?.toLowerCase());
const isCurrentUserValuerAssigned = computed(() => contactValuer?.value?.ntUsername && currentUserNTUserName?.value?.toLowerCase() === contactValuer?.value?.ntUsername.trim()?.toLowerCase());
const isAssignedValuerRegistered = computed(() => linkedObjection.value?.valuerNtUsername && !!findRegisteredValuerByNtUsername(linkedObjection.value.valuerNtUsername?.trim()));
const isValuerChanged = computed(() => {
    if (contactValuer?.value) {
        if (!linkedObjection.value?.valuerNtUsername && contactValuer?.value?.ntUsername?.trim()?.toLowerCase() == 'unassigned') { return false; }
        return contactValuer?.value?.ntUsername?.trim()?.toLowerCase() !== linkedObjection.value?.valuerNtUsername?.trim()?.toLowerCase();
    }
    return false;
});
const isRegisteredValuerChanged = computed(() => {
    if (contactRegisteredValuer.value) {
        if (!linkedObjection.value?.registeredValuerNtUsername && contactRegisteredValuer?.value?.ntUsername?.trim()?.toLowerCase() == 'unassigned') { return false; }
        return contactRegisteredValuer?.value?.ntUsername?.trim()?.toLowerCase() !== linkedObjection.value?.registeredValuerNtUsername?.trim()?.toLowerCase();
    }
    return false;
});
const isMaoriLand = computed(() => property.value?.landUseData?.isMaoriLand);

const draftValidationCategoryNonResi = computed(() => validationSet?.value?.errors?.find(error => error.message === 'must be residential')?.length);
const jobValidationCategoryNonResi = computed(() => propertyValidationSet?.value?.errors?.find(error => error.message === 'must be residential')?.length);
const unadjustedValuation = computed(() => property.value?.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation);

watch(ratingValuationId, loadPageData);

watch(property, (newVal) => {
    if (`${newVal.qupid}` === `${qpid}`) {
        loadTAZoneClassification(newVal.territorialAuthority.code);
    }
});

watch(draftValidationCategoryNonResi, handleNonResidentialWatch);
watch(jobValidationCategoryNonResi, handleNonResidentialWatch);

function handleNonResidentialWatch(isNonResidential) {
    if (isNonResidential) {
        shouldPromptDeleteObjectionJob.value = true;
    }
};

onMounted(async () => {
    if (!isInternalUser.value) {
        const actionRecordRouteName = "rating-valuation-objection-action-record";
        if (route.name !== actionRecordRouteName) {
            //shouldn't allow external users to go any other pages except action record
            router.push({
                name: 'roll-maintenance'
            });
            return;
        }
    }
    await loadPageData();
});

async function loadPageData() {
    await ratingValuationStore.dispatch('getValuation', ratingValuationId.value);

    const promises = []

    promises.push(loadPropertyDetail());
    promises.push(propertyStore.dispatch('getProperty', ratingValuation.value.ratingUnit.propertyId));
    promises.push(getAllUsers());
    promises.push(ratingValuationStore.dispatch('loadRelatedRollMaintenanceActivities', !isActionRecord.value));

    await Promise.allSettled(promises)
    await loadTAZoneClassification(property.value.territorialAuthority.code);
    dataLoaded.value = true;
}

async function loadPropertyDetail() {
    if (draftLoading.value) {
        return;
    }
    try {
        if (ratingValuation.value.propertyDetailId) {
           await propertyDraftStore.dispatch('getPropertyDetail', ratingValuation.value.propertyDetailId);
        }
        else {
           await propertyDraftStore.dispatch('getPropertyDetailByQpid', qpid.value);
        }
    }
    catch (err) {
        console.error('Failed to load property details', error);
        await modal.showError('Failed to load property details', 'An error occurred while loading the property details. Please contact support or try again later.');
    }
}

async function getAllUsers() {
    if (!users.value) {
        await loadUsers();
    }
}

function validate(options) {
    if (options?.atCompletion) {
        ratingValuationStore.dispatch('validateValuationAtCompletion', {
            propertyDetail: draftProperty.value,
            activities: propertyActivities.value,
            isComplete: options?.isComplete,
        });
    }
    else {
        console.info('validate Valuation', draftProperty.value, propertyActivities.value)
        ratingValuationStore.dispatch('validateValuation', {
            propertyDetail: draftProperty.value,
            activities: propertyActivities.value,
        });
    }
    propertyDraftStore.dispatch('validatePropertyDraft', {
        property: property.value,
    });
}

async function save(isCompletion = false) {
    await validate({ isComplete: isCompletion });

    await Promise.allSettled([
        ratingValuationStore.dispatch('saveValuation'),
        propertyDraftStore.dispatch('savePropertyDraft')
    ]);
}

function toggleExpandObjection() {
    expandObjection.value = !expandObjection.value;
}

async function loadTAZoneClassification(taCode) {
    await store.dispatch('fetchTAZoneClassification', taCode);
    zoneLoaded.value = true;
}

function percentageChange(val1, val2) {
    return ((val1 - val2) / val1) * 100;
}

function shouldWarn(val) {
    const threshold = 15;
    if (val < 1) return val < -threshold;
    if (val > 0) return val > threshold;
    return false;
}

function setContactRegisteredValuer(data) {
    contactRegisteredValuer.value =  { ...data };
}

function setContactValuer(data) {
    contactValuer.value = { ...data };
}

provide('valuers', valuers);
provide('registeredValuers', registeredValuers);
provide('valuersLoaded', valuersLoaded);
provide('ratingValuation', ratingValuation);
provide('ratingValuationId', ratingValuationId);
provide('valuationLoaded', valuationLoaded);
provide('qpid', qpid);
provide('objections', objections);
provide('linkedObjection', linkedObjection);
provide('propertyActivities', propertyActivities);
provide('valuationValidationSet', validationSet);
provide('propertyValidationSet', propertyValidationSet);
provide('property', property);
provide('propertyDetail', draftProperty);
provide('propertyId', propertyId);
provide('taPriceIndex', computed(() => linkedObjection.value?.taPriceIndex ?? null));
provide('taCode', taCode);
provide('isJobReadOnly', isJobReadOnly);
provide('hasNoCoordinates', hasNoCoordinates);
provide('comparableSaleSearchCriteria', comparableSaleSearchCriteria);
provide('comparableSaleCriteriaCV', comparableSaleCriteriaCV);
provide('comparableSaleCriteriaLV', comparableSaleCriteriaLV);
provide('comparableSaleDefaultCriteriaLoaded', comparableSaleDefaultCriteria);
provide('currentRevisionDate', currentRevisionDate);
provide('draftPropertyParamsForComparableSales', draftPropertyParamsForComparableSales);
provide('zoneLoaded', zoneLoaded);
provide('expandObjection', expandObjection);
provide('isCurrentUserAdmin', isAdminUser);
provide('isCurrentUserExternalUpdate', isExternalUser.value && !isReadOnly.value);
provide('isCurrentUserRegisteredValuerAssigned', isCurrentUserRegisteredValuerAssigned);
provide('isCurrentUserValuerAssigned', isCurrentUserValuerAssigned);
provide('isAssignedValuerRegistered', isAssignedValuerRegistered);
provide('isActionRecord', isActionRecord);
provide('isValuerChanged', isValuerChanged);
provide('isRegisteredValuerChanged', isRegisteredValuerChanged);
provide('objectionJobValidationResult', {}); //TODO: remove after Validation package integration
</script>

<template>
    <div class="contentWrapper resultsWrapper">
        <PropertySummary :property-id="propertyId" :can-navigate="false">
            <template v-if="ratingValuation && property">
                <PropertySummaryProposedValues
                    v-if="isMaoriLand"
                    :current-unadjusted-values="unadjustedValuation"
                    :current-values="property.currentValuation"
                    :proposed-values="ratingValuation.adoptedValue"
                    :unadjusted-values="ratingValuation.unadjustedValue"
                    :working-net-rate="ratingValuation.workingNetRate"
                    :show-unadjusted-values="isMaoriLand"
                    class="qv-bg-mediumblue"
                />
                <PropertySummaryProposedValues
                    v-else
                    :current-values="property.currentValuation"
                    :proposed-values="ratingValuation.adoptedValue"
                    :working-net-rate="ratingValuation.workingNetRate"
                />
            </template>
        </PropertySummary>
        <div v-if="!valuationLoaded">
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
        <div v-if="exception" class="bAlert bAlert-danger exception-message">
            An error occurred while loading the valuation job. Please contact support or try again later.
            <span v-if="exception">{{ exception }}</span>
        </div>
        <div v-if="valuationLoaded && !isActionRecord" class="col-container">
            <div class="qv-flex-row qv-w-full qv-bg-light qv-p-3 monarch-bg-white qv-worksheet">
                <div class="qv-flex-column qv-gap-4 qv-pt-3" style="width: 15%">
                    <ObjectionJobStepper
                        :is-setup-complete="isSetupComplete"
                        :validations="validationSet"
                        :property-validations="propertyValidationSet"
                    />
                    <objection-sidebar
                        :objection="linkedObjection"
                        :linked-objections="linkedObjections"
                        :warnings="warnings"
                        :rating-valuation="ratingValuation"
                        :property-activities="propertyActivities"
                        :should-show-reinstatement-button="shouldShowReinstatementButton"
                        :setup-complete="isSetupComplete"
                        :address="address"
                        :should-prompt-delete-objection-job="shouldPromptDeleteObjectionJob"
                        @closeDeleteConfirmationModal="shouldPromptDeleteObjectionJob = false" />
                    <property-info :qpid="qpid" />
                </div>
                <div class="qv-flex-grow">
                    <template v-if="validationSet">
                        <ValuationValidationList :validation-set="validationSet" :pd-validation-set="propertyValidationSet" />
                    </template>
                    <router-view
                        class="qv-flex-column"
                        style="gap: 2rem"
                        :furtherContactWarnings="warnings"
                        @toggleExpandObjection="toggleExpandObjection"
                        @refreshObjection="loadPageData"
                        @setContactValuer="setContactValuer"
                        @setContactRegisteredValuer="setContactRegisteredValuer"
                    />
                </div>
            </div>
        </div>
        <div v-if="valuationLoaded && isActionRecord" class="col-container">
            <div class="col-row">
                <div class="col col-12">
                    <router-view
                        style="gap: 2rem"
                        :furtherContactWarnings="warnings"
                        class="qv-flex-column"
                     />
                </div>
            </div>
        </div>
    </div>
</template>

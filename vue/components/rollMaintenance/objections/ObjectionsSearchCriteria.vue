<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import moment from 'moment';
import numeral from 'numeral';
import Multiselect from 'vue-multiselect';
import { store } from '../../../DataStore';
import TerritorialAuthority from '../../filters/TerritorialAuthority.vue';
import SalesGroupAndRolls from '../ratingValuation/common/SalesGroupsAndRolls.vue';
import ClassificationDropdown from '../../common/form/ClassificationDropdown.vue';
import NumberInput from '../../common/form/NumberInput.vue';
import { fetchTerritorialAuthorities } from './utils.js';

const props = defineProps(['exporting', 'warnings', 'showWarning', 'valuers', 'registeredValuers', 'isInternalUser', 'isTAUser', 'taCodes', 'externalObjectionAccess']);
const emit = defineEmits(['exportCSVClicked', 'search', 'closeWarning']);
const searchCriteria = computed(() => store.state.rollMaintenanceSearch.searchCriteria);
const classificationsLoaded = computed(() => store.state.classificationsLoaded);
const loading = computed(() => store.state.rollMaintenanceSearch.loading);
const defaultCriteriaLoaded = ref(false);
const showSaleGroupsAndRolls = ref(false);
const totalRollCounts = ref(0);
const taName = ref(''); //the single ta name for TA User
const taLabelStyle = {color: '#fff'};
const validRevisionYear = computed(() => {
    if (!searchCriteria.value.objectionCriteria.revisionYear) return true;
    const formattedDate = moment(searchCriteria.value.objectionCriteria.revisionYear, 'YYYY');
    return formattedDate && formattedDate.isValid() && formattedDate.year() <= moment().year();
});
const validSearch = computed(() => validRevisionYear.value && props.taCodes && props.taCodes.length > 0);
const valuersSelected = ref([]);
const registeredValuersSelected = ref([]);

const invalidRevisionYearMessage = 'Year must be numeric and not in future';
const invalidTaCodesMessage = 'Territorial Authority is required';

function setObjectionDefaultCriteria() {
    defaultCriteriaLoaded.value = false;
    const objectionType = store.state.classifications.classifications.ObjectionType;
    const objectionJobStatus = store.state.classifications.classifications.ObjectionJobStatusType;
    const objectionStatusType = store.state.classifications.classifications.ObjectionStatusType;

    const objectionTypeDefault = props.isTAUser || props.externalObjectionAccess ? objectionType.find(type => type.description.trim() === "Revision") : objectionType.find(type => type.defaultSelected === true);
    const objectionJobStatusDefault =  props.isTAUser || props.externalObjectionAccess ? [] : objectionJobStatus.filter(status => status.defaultSelected === true);
    const objectionStatusTypeDefault = props.isTAUser || props.externalObjectionAccess ? objectionStatusType.filter(statusType => statusType.description.trim() === "TLA Approval for Recommendations") : objectionStatusType.filter(statusType => statusType.defaultSelected === true);

    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', { id: 'objectionTypes', value: objectionTypeDefault });
    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', { id: 'objectionJobStatuses', value: objectionJobStatusDefault });
    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', { id: 'objectionStatuses', value: objectionStatusTypeDefault });
    defaultCriteriaLoaded.value = true;
}

const readyToSearch = computed(() => !loading.value && defaultCriteriaLoaded.value && props.taCodes?.length);

watch(readyToSearch, (value) => {
    if (value) {
        emit('search');
    }
});

watch(classificationsLoaded, (value) => {
    if (value === true) {
        setObjectionDefaultCriteria();
    }
});

watch(registeredValuersSelected, (value) => {
    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', { id: 'registeredValuers', value });
});
watch(valuersSelected, (value) => {
    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', { id: 'valuers', value });
});

const valuers = computed(() => [{ id: 'Unassigned', name: 'Unassigned', ntUsername: 'Unassigned' }].concat(props.valuers));
const registeredValuers = computed(() => [{ id: 'Unassigned', name: 'Unassigned', ntUsername: 'Unassigned' }].concat(props.registeredValuers));

onMounted(async () => {
    try {
        registeredValuersSelected.value = store.state.rollMaintenanceSearch.searchCriteria.objectionCriteria.registeredValuers;
        valuersSelected.value = store.state.rollMaintenanceSearch.searchCriteria.objectionCriteria.valuers;
        if (store.state.rollMaintenanceSearch.firstLoad) {
            store.commit('rollMaintenanceSearch/setFirstLoad', false);
            if (classificationsLoaded.value === true) {
                setObjectionDefaultCriteria();
            }
        }
        if (props.isTAUser && props.taCodes?.length == 1) {
            taName.value = await getTAName(props.taCodes[0]);
        }
    } catch (error) {
        console.error(error);
    } finally {
        store.commit('rollMaintenanceSearch/setLoading', false);
    }
});

function toDate(text) {
    const formattedDate = moment(text, 'YYYY');
    if (formattedDate.isValid() && formattedDate.year() <= moment().year()) {
        return formattedDate.format('YYYY');
    }
    return text;
}

function onSetRolls({saleGroups, rolls, total} = {}) {
    showSaleGroupsAndRolls.value = false;
    totalRollCounts.value = total;
    updateObjectionCriteriaItem({ id: 'saleGroups', value: saleGroups && saleGroups.length > 0 ? saleGroups : []})
    updateObjectionCriteriaItem({ id: 'rollNumbers', value: rolls && rolls.length > 0 ? rolls : []})
    if(total > 0){
        ['rollNumber', 'assessmentNumber', 'assessmentSuffix'].forEach( item =>
        updateObjectionCriteriaItem({ id: item, value: null}))
    }
}

function updateObjectionCriteriaItem(item) {
    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', item);
}

function updateRevisionYear(e) {
    const date = toDate(e.target.value);
    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', { id: 'revisionYear', value: date });
}

function toNumber(text) {
    const n = numeral(text).value();
    if (Number.isNaN(n)) return null;
    return n;
}

function clearSearchCriteria() {
    updateObjectionCriteriaItem({ id: 'ratingUnitCategories', value: null });
    updateObjectionCriteriaItem({ id: 'qpid', value: null });
    updateObjectionCriteriaItem({ id: 'rollNumber', value: null });
    updateObjectionCriteriaItem({ id: 'assessmentNumber', value: null });
    updateObjectionCriteriaItem({ id: 'assessmentSuffix', value: null });
    updateObjectionCriteriaItem({ id: 'objectionTypes', value: null });
    updateObjectionCriteriaItem({ id: 'revisionYear', value: null });
    updateObjectionCriteriaItem({ id: 'objectionCategoryGroups', value: null });
    updateObjectionCriteriaItem({ id: 'objectionStatuses', value: null });
    updateObjectionCriteriaItem({ id: 'objectionJobStatuses', value: null });
    updateObjectionCriteriaItem({ id: 'objectorName', value: null });
    valuersSelected.value = [];
    registeredValuersSelected.value = [];

    setObjectionDefaultCriteria();
}

function handleKeyupEnter(event) {
    // Manually blur and focus element to force IE 11 to fire an OnChange event to change the search input
    event.srcElement.blur();
    event.srcElement.focus();
    emit('search');
}

function setActiveObjectionJobStatuses() {
    const objectionJobStatuses = store.state.classifications.classifications.ObjectionJobStatusType;
    const activeStatus = objectionJobStatuses.filter(status => status.activeStatus === true);
    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', { id: 'objectionJobStatuses', value: activeStatus });
}

function setActiveObjectionStatuses() {
    const objectionStatuses = store.state.classifications.classifications.ObjectionStatusType;
    const activeStatus = objectionStatuses.filter(status => status.activeStatus === true);
    store.commit('rollMaintenanceSearch/setObjectionCriteriaItem', { id: 'objectionStatuses', value: activeStatus });
}

async function getTAName(taCode) {
    const tas = await fetchTerritorialAuthorities();
    for (const [taId, name] of Object.entries(tas)) {
        const id = parseInt(taId, 10);
        if (id == taCode) {
            return `${taCode} - ${name}`;
        }
    }
}
</script>

<template>
    <div class="search-dashboard">
        <div class="col-container qv-search-background" v-show="!showSaleGroupsAndRolls">
            <div class="col-row">
                <div class="col col-2">
                    <div class="qv-ta-search-container" v-if="!isTAUser">
                        <territorial-authority :validation-error="taCodes && taCodes.length === 0" :customLabelStyle="taLabelStyle"/>
                        <span v-if="taCodes && taCodes.length === 0" class="error-message">{{ invalidTaCodesMessage }}</span>
                    </div>
                    <label v-else class="qv-non-clickable">
                        <span class="label qv-label-white">Territorial Authorities</span>
                        <input type="text" :value="taName" class="grey-out">
                    </label>
                </div>
                <div data-cy="objection-type" class="col col-2">
                    <label :class="{'qv-non-clickable': isTAUser}">
                        <span class="label qv-label-white">Objection Type</span>
                        <classification-dropdown data-cy="objection-type-dropdown" v-if="!isTAUser && !externalObjectionAccess" id="objectionTypes" type="text" category="ObjectionType"
                            :value="searchCriteria.objectionCriteria.objectionTypes" hide-codes
                            @input="updateObjectionCriteriaItem" />
                        <input v-else type="text" :value="searchCriteria.objectionCriteria.objectionTypes?.description" class="grey-out">
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label qv-label-white">Revision Year</span>
                        <input type="text" :class="{ valError: !validRevisionYear }"
                            :value="searchCriteria.objectionCriteria.revisionYear" @change="updateRevisionYear"
                            @keyup.enter="handleKeyupEnter">
                        <span v-if="!validRevisionYear" class="error-message">{{ invalidRevisionYearMessage }}</span>
                    </label>
                </div>
                <div data-cy="valuation-job-statuses" class="col col-7">
                    <div class="righty status-actions qv-clear-link" v-if="!isTAUser && !externalObjectionAccess">
                        <a data-cy="valuation-job-statuses-all-active" @click="setActiveObjectionJobStatuses" >All Active</a> |
                        <a data-cy="valuation-job-statuses-clear" @click="searchCriteria.objectionCriteria.objectionJobStatuses = []" >Clear</a>
                    </div>
                    <label title="This is a list of the Monarch Objection Job Statuses" :class="{'qv-non-clickable': isTAUser || externalObjectionAccess}">
                        <span class="label qv-label-white">Valuation Job Statuses</span>
                        <classification-dropdown v-if="!isTAUser && !externalObjectionAccess" id="objectionJobStatuses" type="text"
                            :value="searchCriteria.objectionCriteria.objectionJobStatuses"
                            category="ObjectionJobStatusType" :taggable="true" :multiple="true" hide-codes
                            @input="updateObjectionCriteriaItem" />
                        <input v-else type="text" :value="searchCriteria.objectionCriteria.objectionJobStatuses?.[0]?.description" class="grey-out">
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2">
                    <div>&nbsp;</div>
                    <sales-group-and-rolls :taCodes="taCodes" :selected-rolls="searchCriteria.objectionCriteria.rollNumbers" :selected-sale-groups="searchCriteria.objectionCriteria.saleGroups" @setRolls="onSetRolls" />
                </div>
                <div data-cy="category" class="col col-1">
                    <label title="To search for different types of categories enter each separated by a comma, e.g. RC*, RH*">
                        <span class="label qv-label-white">Categories</span>
                        <input type="text" :value="searchCriteria.objectionCriteria.ratingUnitCategories" @change="updateObjectionCriteriaItem({
                                    id: 'ratingUnitCategories',
                                    value: $event.target.value
                        })" @keyup.enter="handleKeyupEnter">
                    </label>
                </div>
                <div data-cy="category-groups" class="col col-3">
                    <div class="righty status-actions">
                        <a data-cy="category-groups-clear" @click="searchCriteria.objectionCriteria.objectionCategoryGroups = []" class="qv-clear-link">Clear</a>
                    </div>
                    <label>
                        <span class="label qv-label-white">Category Groups</span>
                        <classification-dropdown id="objectionCategoryGroups" type="text"
                            :value="searchCriteria.objectionCriteria.objectionCategoryGroups"
                            category="CategoryGroupType" :taggable="true" :multiple="true"
                            @input="updateObjectionCriteriaItem" />
                    </label>
                </div>
                <div class="col col-1">
                    <label title="Search by QPID">
                        <span class="label qv-label-white">QPID</span>
                        <number-input data-cy="qpid-input" format="0" :value="searchCriteria.objectionCriteria.qpid" @change="updateObjectionCriteriaItem({
                            id: 'qpid',
                            value: toNumber($event.target.value)
                        })" @keyup.enter="handleKeyupEnter" />
                    </label>
                </div>
                <!-- VALREF -->
                <div class="col col-1">
                    <label title="Valuation reference roll number" :class="{ 'qv-non-clickable': totalRollCounts > 0 }">
                        <span class="label qv-label-white">Roll Number</span>
                        <number-input format="0" class="grey-out"
                            :value="searchCriteria.objectionCriteria.rollNumber" @change="updateObjectionCriteriaItem({
                                id: 'rollNumber',
                                value: toNumber($event.target.value)
                            })" @keyup.enter="handleKeyupEnter" />
                    </label>
                </div>
                <div class="col col-1">
                    <label title="Valuation reference assessment number" :class="{ 'qv-non-clickable': totalRollCounts > 0 }">
                        <span class="label qv-label-white">Assessment Number</span>
                        <number-input format="0" class="grey-out"
                            :value="searchCriteria.objectionCriteria.assessmentNumber" @change="updateObjectionCriteriaItem({
                                id: 'assessmentNumber',
                                value: toNumber($event.target.value)
                            })" @keyup.enter="handleKeyupEnter" />
                    </label>
                </div>
                <div class="col col-1">
                    <label title="Valuation reference assessment suffix" :class="{ 'qv-non-clickable': totalRollCounts > 0 }">
                        <span class="label qv-label-white">Suffix</span>
                        <input type="text" class="grey-out"
                            :value="searchCriteria.objectionCriteria.assessmentSuffix" @change="updateObjectionCriteriaItem({
                                id: 'assessmentSuffix',
                                value: $event.target.value
                            })" @keyup.enter="handleKeyupEnter">
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label qv-label-white">Objector Name</span>
                        <input id="objectorName" type="text" :value="searchCriteria.objectionCriteria.objectorName"
                            @change="updateObjectionCriteriaItem({
                                id: 'objectorName',
                                value: $event.target.value
                            })" @keyup.enter="handleKeyupEnter">
                    </label>
                </div>
            </div>
            <div data-cy="valuer" class="col-row">
                <div class="col col-2">
                    <div class="righty status-actions">
                        <a @click="valuersSelected = []" class="qv-clear-link">Clear</a>
                    </div>
                    <label>
                        <span class="label qv-label-white">Valuer</span>
                        <multiselect v-model="valuersSelected" :options="valuers" :value="valuersSelected" label="name"
                            :multiple="true" :close-on-select="false" track-by="name" select-label="⏎ select"
                            deselect-label="⏎ remove">
                            <template slot="selection" slot-scope="{ values, search, isOpen }">
                                <span class="multiselect__single" v-if="values.length > 2 && !isOpen">
                                    {{ values.length }} options selected
                                </span>
                            </template>
                        </multiselect>
                    </label>
                </div>
                <div data-cy="registered-valuer" class="col col-2">
                    <div class="righty status-actions">
                        <a data-cy="registered-valuer-clear" @click="registeredValuersSelected = []" class="qv-clear-link">Clear</a>
                    </div>
                    <label>
                        <span class="label qv-label-white">Registered Valuer</span>
                        <multiselect v-model="registeredValuersSelected" :options="registeredValuers"
                            :value="registeredValuersSelected" label="name" :multiple="true" :close-on-select="false"
                            track-by="name" select-label="⏎ select" deselect-label="⏎ remove">
                            <template slot="selection" slot-scope="{ values, search, isOpen }">
                                <span class="multiselect__single" v-if="values.length > 2 && !isOpen">
                                    {{ values.length }} options selected
                                </span>
                            </template>
                        </multiselect>
                    </label>
                </div>
                <div data-cy="administration-status" class="col col-8">
                    <div v-if="!isTAUser && !externalObjectionAccess" class="righty status-actions qv-clear-link">
                        <a @click="setActiveObjectionStatuses" >All Active</a> |
                        <a data-cy="administration-status-clear" @click="searchCriteria.objectionCriteria.objectionStatuses = []" >Clear</a>
                    </div>
                    <label title="This is the list of QIVS Objection Statuses" :class="{'qv-non-clickable': isTAUser || externalObjectionAccess}">
                        <span class="label qv-label-white">Administration Status</span>
                        <classification-dropdown v-if="!isTAUser && !externalObjectionAccess" id="objectionStatuses" type="text"
                            :value="searchCriteria.objectionCriteria.objectionStatuses" category="ObjectionStatusType"
                            :taggable="true" :multiple="true" hide-codes @input="updateObjectionCriteriaItem" />
                        <input v-else type="text" :value="searchCriteria.objectionCriteria.objectionStatuses?.[0]?.description" class="grey-out">
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="righty search-control-buttons">
                    <div data-cy="export" v-if="!isTAUser && !externalObjectionAccess" title="Export Results (limit 50,000)"
                        class="exportResults mdl-button mdl-js-button mdl-button--icon"
                        :class="{ disabled: loading || exporting || !validSearch }"
                        @click="emit('exportCSVClicked')">
                        <i class="material-icons md-dark">&#xE06F;</i>
                    </div>
                    <button data-cy="objection-clear" class="mdl-button mdl-js-button mdl-button--raised
                            mdl-js-ripple-effect advSearchClear"
                        :class="{ disabled: loading || props.exporting || !validSearch }" title="Clear Search Criteria"
                        @click="clearSearchCriteria">
                        Clear
                    </button>
                    <button data-cy="objection-search" class="mdl-button mdl-js-button mdl-button--raised
                        mdl-js-ripple-effect mdl-button--colored"
                        :class="{ disabled: loading || props.exporting || !validSearch }" @click="emit('search')">
                        Search
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped="true" src="../rollMaintenance.scss">

</style>
<style lang="scss" scoped="true" src="./objectionsSearchDashBoard.scss">

</style>

<!-- global styles with a prefix -->
<style lang="scss" scoped>
.valError {
    -webkit-box-shadow: inset 0 0 0 .1rem #ea2e2d;
    -moz-box-shadow: inset 0 0 0 .1rem #ea2e2d;
    box-shadow: inset 0 0 0 .1rem #ea2e2d;
}

.error-message {
    display: block;
    color: #ea2e2d;
    font-size: 1.1rem;
    line-height: 1.6;
}

.search-control-buttons {
    line-height: 5.5;
    margin-bottom: -7.5em;
}
</style>

<script setup>
import { inject, computed, ref, watch, onMounted } from 'vue';
import { getCapitalValueHeader, getSaleCapitalValue } from './utils';
import { formatPrice } from '@/utils/FormatUtils';

const emit = defineEmits(['nav-button-click', 'save', 'refresh']);
const props = defineProps({
    comparableSale: {
        type: Object,
        default: () => ({
            comment: null,
            adjustedNetRate: null,
            comparability: null,
        }),
    },
});
const compSale = ref({
    comment: null,
    adjustedNetRate: null,
    comparability: null,
});
const isManualEdit = ref(false);
watch(() => props.comparableSale, (value) => {
    compSale.value = value;
});
onMounted(() => {
    if (props.comparableSale) {
        compSale.value = props.comparableSale;
    }
});
const initialAdjustedNetRate = ref(null);
const taPriceIndex = inject('taPriceIndex');
const isActionRecord = inject('isActionRecord');
const hpiMovement = computed(() => taPriceIndex.value / props.comparableSale?.salePriceIndex);

async function refreshComment() {
    emit('refresh', compSale);
}

function handleChange(event) {
    compSale.value.adjustedNetRate = parseInt(event.target.value);
    if (!isManualEdit.value) {
        save();
    }
}

function save(shouldSaveRatingValuation = true) {
    emit('save', { compSale, shouldSaveRatingValuation });
}

function handleFocus() {
    initialAdjustedNetRate.value = compSale.value.adjustedNetRate;
    isManualEdit.value = true;
}

function handleBlur(event) {
    try {
        const value = parseInt(event.target.value) || 0;
        if (!isNaN(value) && initialAdjustedNetRate.value != value) {
            compSale.value.adjustedNetRate = value;
            save();
        }
    }
    catch (error) {
        console.error(error);
    }
    finally {
        initialAdjustedNetRate.value = null;
        isManualEdit.value = false;
    }
}

function getAdjustedNetSalePrice() {
    if (hpiMovement.value) {
        return Math.round((props.comparableSale?.nsp || 0) * hpiMovement.value / 1000) * 1000;
    }
    return props.comparableSale?.nsp;
}

</script>

<template>
    <tr>
        <td>&nbsp;</td>
        <td data-cy="comparable-sales" colspan="16">
            <div class="qv-comp-sale-inline-form" style="margin-top: 5px">
                <label style="flex: 1">
                    <span class="label lefty" title="This is initially populated from the Sale Analysis Comment. However, any changes saved will be updated against this Job only.">Sale Comment</span>
                    <span class="righty"><i class="material-icons qv-refresh-icon" title="Refresh from Sales Analysis" @click="refreshComment">&#xE5D5;</i></span>
                    <textarea
                        data-cy="sale-comment-textarea"
                        v-model="compSale.comment"
                        v-auto-grow
                        class="qv-input"
                        style="margin-top: 2px; min-height: 11rem;"
                        title="This is initially populated from the Sale Analysis Comment. However, any changes saved will be updated against this Job only."
                        @change="save(false)"
                    />
                </label>
                <div>
                    <div class="qv-flex-column">
                        <div style="display: flex; gap: 1rem;">
                            <label>
                                <span style="margin-bottom: 2px;" class="label">HPI Movement</span>
                                <input :value="hpiMovement ? hpiMovement.toFixed(2) : '-'" class="`qv-bg-dark-100 qv-read-only${isActionRecord?'-opaque':''}" disabled min="0" type="number">
                            </label>
                            <label>
                                <span style="margin-bottom: 2px;" class="label">Adjusted Net Rate</span>
                                <input :value="compSale.adjustedNetRate" min="0" type="number" @change="handleChange" @blur="handleBlur"
                                       @focus="handleFocus">
                            </label>
                        </div>
                        <div style="display: flex; gap: 1rem;">
                            <label>
                                <span class="label">Adjusted NSP</span>
                                <input :value="formatPrice(getAdjustedNetSalePrice())" disabled>
                            </label>
                            <label>
                                <span class="label">{{ getCapitalValueHeader(comparableSale) }}</span>
                                <input :value="formatPrice(getSaleCapitalValue(comparableSale))" disabled>
                            </label>
                        </div>
                    </div>
                </div>
                <div style="justify-content: flex-start;margin-top: 1.35rem;" class="qv-flex-column">
                    <div class="qv-nav-buttons-container action-record-hide qv-mt-5">
                        <button class="qv-nav-button qivs-button" @click="emit('nav-button-click', 'qivs')">QIVS<i class="material-icons">call_made</i></button>
                        <button class="qv-nav-button monarch-button" @click="emit('nav-button-click', 'map')">MAP<i class="material-icons">call_made</i></button>
                        <button class="qv-nav-button qivs-button" @click="emit('nav-button-click', 'web')">WEB<i class="material-icons icon--flipped">search</i></button>
                        <button class="qv-nav-button qivs-button" @click="emit('nav-button-click', 'sale')">SALE<i class="material-icons">call_made</i></button>
                        <button class="qv-nav-button monarch-button" style="grid-column: span 2;" @click="emit('nav-button-click', 'analysis')">ANALYSIS<i class="material-icons">call_made</i></button>
                    </div>
                    <div data-cy="comparability" class="qv-position-relative">
                        <span class="qvtd-comparable-statement-label">Comparability</span>
                        <div style="margin-top: 0.5rem;" class="comparableStatement qvtd-comparable-statement" title="The comparability is included in the Objection Report">
                            <span>
                                <select v-model="compSale.comparability" @change="save(false)">
                                    <option value="comparable">Comparable</option>
                                    <option value="inferior">Inferior</option>
                                    <option value="superior">Superior</option>
                                </select>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </td>
    </tr>
</template>

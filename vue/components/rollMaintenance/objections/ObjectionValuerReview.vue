<script setup>
import { inject, ref, computed, watch, onMounted } from 'vue';
import MaterialIcon from 'Common/MaterialIcon.vue';
import ObjectionValuerReviewOutcome from './ObjectionValuerReviewOutcome.vue';
import ClassificationDropdown from '@/components/common/form/ClassificationDropdown.vue';
import { store } from '@/DataStore';
import AlertModal from '@/components/common/modal/AlertModal.vue';
import useModal from '@/composables/useModal';
import { checkValidation } from './utils.js';

const emit = defineEmits(['sendToReview','validate']);
const props = defineProps([
    'currentReview',
    'ratingValuationId',
    'requestingReview',
    'latestReview',
    'validationSet'
]);
const notesForReviewer = ref('');
const risk = ref(null);
const reviewReason = ref('');
const reviewOutcome = ref({
    relevantSalesUsed: {
        value: null,
        description: 'Relevant sales were used',
    },
    valuesAreAppropriate: {
        value: null,
        description: 'The values that were arrived at are appropriate',
    },
    compliesWithOvg: {
        value: null,
        description: 'Objection complies with OVG rules',
    },
    primaryPhotoUpdated: {
        value: null,
        description: 'Primary photo has been updated',
    },
});

const objection = inject('linkedObjection');
const isJobReadOnly = inject('isJobReadOnly');
const isActionRecord = inject('isActionRecord');
const isAssignedValuerRegistered = inject('isAssignedValuerRegistered');
const isCurrentUserRegisteredValuerAssigned = inject('isCurrentUserRegisteredValuerAssigned');
const isValuerChanged = inject('isValuerChanged');
const isRegisteredValuerChanged = inject('isRegisteredValuerChanged');
const isReadyToValue = ref(false);
const isAtRegisteredValuerReview = ref(false);
const isApproved = computed(() => reviewOutcome.value.relevantSalesUsed.value
    && reviewOutcome.value.valuesAreAppropriate.value
    && reviewOutcome.value.compliesWithOvg.value
    && reviewOutcome.value.primaryPhotoUpdated.value);
const reviewComplete = computed(() => reviewOutcome.value.relevantSalesUsed.value !== null
    && reviewOutcome.value.valuesAreAppropriate.value !== null
    && reviewOutcome.value.compliesWithOvg.value !== null
    && reviewOutcome.value.primaryPhotoUpdated.value !== null)

const alertModal = ref({
    isOpen: false,
    heading: 'Some item(s) have not been entered. Are you sure you want to send to review?',
    missingItems: [],
    cancelText: 'No, return to Objection Job',
    cancelAction: () => {
        alertModal.value.isOpen = false;
    },
    confirmText: 'Yes, Send to Review',
    confirmAction: () => {
        alertModal.value.isOpen = false;
        emit('sendToReview', notesForReviewer.value, risk.value);
    }
})

const outcomeModal = ref({
    isOpen: false,
    heading: 'Missing required fields',
    missingItems: [],
    confirmText: 'Return to Objection Job',
    confirmAction() {
        outcomeModal.value.isOpen = false;
    }
})

const modal = useModal();

function validateOutcome(approved) {
    let valid = true;
    outcomeModal.value.missingItems = [];
    if (!approved && reviewReason.value.trim() === ''){
        valid = false;
        outcomeModal.value.missingItems.push("Please enter a reason");
    }
    for (const outcome of Object.values(reviewOutcome.value)) {
        if (outcome.value === null || outcome.value === undefined) {
            valid = false;
            outcomeModal.value.missingItems.push(outcome.description)
        }
    }

    if (!valid) {
        outcomeModal.value.isOpen = true;
    }

    return valid;
}

async function checkReviewContent() {
    emit('validate');
    const proceed = await checkValidation(props.validationSet.errors, props.validationSet.warnings);
    if(!proceed) {
        return;
    }
    alertModal.value.missingItems = [];
    if (notesForReviewer.value.trim() === "") {
        alertModal.value.missingItems.push("Notes for Registered Valuer have not been entered")
    }
    if (!risk.value) {
        alertModal.value.missingItems.push("A Risk option has not been selected");
    }
    if (!objection.value?.valuer || !objection.value?.registeredValuer) {
        alertModal.value.missingItems.push("Select a Valuer and Registered Valuer to continue");
    }
    if (alertModal.value.missingItems.length === 0) {
        alertModal.value.isOpen = false;
        emit('sendToReview', notesForReviewer.value, risk.value);
    } else {
        alertModal.value.isOpen = true;
    }
}

async function completeReview(approved) {
    if(approved) {
        emit('validate');
        const proceed = await checkValidation(props.validationSet.errors, props.validationSet.warnings);
        if(!proceed) {
            return;
        }
    }
    const valid = validateOutcome(approved);
    if (!valid) {
        return;
    }

    const outcome = reviewOutcome.value;
    const data = {
        relevantSalesUsed: outcome.relevantSalesUsed.value,
        valuesAreAppropriate: outcome.valuesAreAppropriate.value,
        compliesWithOvg: outcome.compliesWithOvg.value,
        primaryPhotoUpdated: outcome.primaryPhotoUpdated.value,
        reviewPassed: approved,
        reviewFailureReason: reviewReason.value,
    };

    emit('completeReview', data);
}
function getRiskType(riskTypeId){
    if(riskTypeId){
        const riskTypes = store.state.classifications.classifications.ObjectionJobReviewRiskType;
        return riskTypes.find(item => item.id == riskTypeId);
    }
}

watch(objection, (value) => {
    if (value) {
        isReadyToValue.value = value.valJobStatus === 'Ready to Value';
        isAtRegisteredValuerReview.value = value.valJobStatus === 'Registered Valuer Review';
    }
}, {deep: true})

onMounted(() => {
    if (objection?.value) {
        isReadyToValue.value = objection?.value?.valJobStatus === 'Ready to Value';
        isAtRegisteredValuerReview.value = objection?.value?.valJobStatus === 'Registered Valuer Review';
    }
});

const showValuationReview = computed(() => {
    if(isValuerChanged.value || isRegisteredValuerChanged.value) return false;
    if(props.latestReview?.reviewPassed && !isReadyToValue.value && !props.requestingReview) return false;
    if(!objection.value?.valuer || !objection.value?.registeredValuer) return false;
    if(isReadyToValue.value && isAssignedValuerRegistered.value) return props.requestingReview;
    if(isReadyToValue.value && !isCurrentUserRegisteredValuerAssigned.value) return true;
    if(isAtRegisteredValuerReview.value && isCurrentUserRegisteredValuerAssigned.value) return true;
    return false;
})
</script>

<template>
    <div class="col-container mdl-shadow--3dp">
        <template v-if="showValuationReview">
            <div :class="{'qv-read-only-wrapper': isJobReadOnly }">
                <div :class="{'qv-read-only': !isActionRecord && isJobReadOnly }">
                    <h1 class="title qv-mb-3">Objection Valuation Review</h1>
                    <div class="qv-comparable-sale-form">
                        <div class="qv-flex-row qv-justify-space-between">
                            <div data-cy="notes-for-registered-valuer" class="col col-12">
                                <div class="qv-label-textarea">
                                    <div class="qv-label-icon">
                                        <label>Notes for Registered Valuer</label>
                                    </div>
                                    <textarea v-if="isAtRegisteredValuerReview && currentReview" v-model="currentReview.notesForReviewer" disabled maxlength="2000" />
                                    <textarea v-else v-model="notesForReviewer" maxlength="2000" />
                                </div>
                            </div>
                        </div>
                        <div class="qv-flex-row qv-justify-space-between">
                            <div data-cy="risk-type" class="col col-2">
                                <div class="qv-label-textarea">
                                    <div class="qv-label-icon">
                                        <label>Risk</label>
                                    </div>
                                    <classification-dropdown v-if="isAtRegisteredValuerReview && currentReview" id="riskTypeId" disabled category="ObjectionJobReviewRiskType"
                                        :value="getRiskType(currentReview.riskTypeId)" label="code"
                                    />
                                    <classification-dropdown v-else id="riskTypeId" category="ObjectionJobReviewRiskType"
                                        :value="risk" label="code" @input="({ id, value }) => {risk = value}"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="qv-flex-row qv-justify-space-between">
                            <div data-cy="objection-valuation-send-to-review" class="col col-12">
                                <div class="righty">
                                    <div>
                                        <button
                                            :disabled="!isReadyToValue"
                                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                            @click="checkReviewContent">
                                            SEND TO REVIEW
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <alert-modal v-if="alertModal.isOpen" caution>
                        <h1> {{  alertModal.heading }}</h1>
                        <ul style="list-style: inside;">
                            <li v-for="item in alertModal.missingItems"> {{ item }} </li>
                        </ul>
                        <template #buttons>
                            <div class="alertButtons">
                                <button
                                    class="mdl-button mdl-button--mini lefty"
                                    @click="alertModal.cancelAction"
                                    data-cy="noWorksheetModalCancelButton"
                                >
                                    {{ alertModal.cancelText }}
                                </button>
                                <button
                                    id="continue"
                                    class="mdl-button mdl-button--mini"
                                    @click="alertModal.confirmAction"
                                    data-cy="noWorksheetModalContinueButton"
                                >
                                    {{ alertModal.confirmText }}
                                </button>
                            </div>
                        </template>
                    </alert-modal>
                </div>
            </div>
        </template>
        <template v-if="latestReview">
            <h1 class="title qv-mb-3">Registered Valuer Review - Outcome</h1>
            <objection-valuer-review-outcome :review="latestReview" />
        </template>
        <template v-if="!isValuerChanged && !isRegisteredValuerChanged && isCurrentUserRegisteredValuerAssigned && isAtRegisteredValuerReview">
            <h1 class="title qv-mb-3">Registered Valuer Review</h1>
            <div class="col-container mdl-shadow--3dp">
                <div class="qv-flex-column">
                    <div class="qv-input-label">
                        <div class="qv-label-icon qv-pb-2">
                            <label>Registered Valuer</label>
                        </div>
                    </div>
                    <div class="qv-flex-row">
                        <div class="qv-icon-row">
                            <i class="icons8-edit-user-male qv-icon"></i>
                            <span>
                                <h3 class="qv-color-dark qv-font-semibold">{{ objection.registeredValuer }}</h3>
                            </span>
                        </div>
                    </div>
                    <div class="qv-flex-row">
                        <div class="qv-input-label">
                            <div class="qv-label-icon">
                                <label>Review Outcome</label>
                            </div>
                        </div>
                    </div>
                    <div class="qv-flex-column qv-gap-4 qv-ml-3">
                        <div class="qv-flex-column">
                            <div v-for="(outcome) in reviewOutcome"
                                 :class="outcome.value === false ? ['qv-color-light', 'qv-bg-mediumblue'] : ['qv-bg-lightbuff', 'qv-color-dark']"
                                 class="qv-flex-row qv-py-2 qv-px-3 qv-justify-space-between"
                            >
                                <p class="lefty qv-font-semibold">{{ outcome.description }}</p>
                                <div class="righty qv-flex-row">
                                    <div class="qv-flex-row">
                                        <div><input v-model="outcome.value" :class="`qv-radio-button${outcome.value === false ? '-light' : ''}`" :value="true" type="radio"></div>
                                        <p>Yes</p>
                                    </div>
                                    <div class="qv-flex-row">
                                        <div><input v-model="outcome.value" :class="`qv-radio-button${outcome.value === false ? '-light' : ''}`" :value="false" type="radio" /></div>
                                        <p>No</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="qv-flex-row">
                            <div>
                                <material-icon v-if="!isApproved" class="qv-color-dark qv-opacity-40" icon="error" />
                            </div>
                            <div class="qv-flex-column qv-flex-grow">
                                <div v-if="!isApproved" class="qv-label-textarea">
                                    <div class="qv-label-icon">
                                        <label>Reason for failure and suggestions for improvement</label>
                                    </div>
                                    <textarea v-model="reviewReason" maxlength="2000" />
                                </div>
                                <div class="qv-flex-row qv-justify-end">
                                    <button
                                        :disabled="!isApproved || !reviewComplete"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect" @click="() => completeReview(true)">
                                        APPROVE
                                    </button>
                                    <button
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                        :disabled="isApproved || !reviewComplete"
                                        @click="() => completeReview(false)">
                                        REJECT
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <alert-modal v-if="outcomeModal && outcomeModal.isOpen" caution>
                        <h1> {{  outcomeModal.heading }}</h1>
                        <ul style="list-style: inside;">
                            <li v-for="item in outcomeModal.missingItems"> {{ item }} </li>
                        </ul>
                        <template #buttons>
                            <div class="alertButtons">
                                <button
                                    id="continue"
                                    class="mdl-button mdl-button--mini"
                                    @click="outcomeModal.confirmAction"
                                    data-cy="noWorksheetModalContinueButton"
                                >
                                    {{ outcomeModal.confirmText }}
                                </button>
                            </div>
                        </template>
                    </alert-modal>
                </div>
            </div>
        </template>
    </div>
</template>

.activity-list {
    &__row {

        &:hover {
            color: #000;
            cursor: default;

            .row-link {
                box-shadow:0 0 0 .15rem rgba(74,144,226,.25);
                box-sizing: border-box;
                border-radius:.5rem;
                vertical-align: middle;

                &:hover {
                    background:rgba(255,255,255,.25);
                    box-shadow:0 0 0 .15rem rgba(255,111,0,.5);
                }
            }

        }
    }

    .external-link {
        color: #000;
        font-weight: bold;
        font-size: 1.2em;
        width: 100%;
    }

    .row-link {
        padding: 1.7em 0.5em 1.9em;
    }

    .fill-cell {
        position: absolute;
        display: inline-block;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        text-align: center;
        vertical-align: middle;
        line-height: 6;

        &:hover {
            text-decoration: underline;
        }
    }

    &--select {
        width: 2%;
        text-align: center;
    }
    &--address {
        width: 18%;
        text-align: left;
        cursor: pointer;
        .row-link {
            padding: 1.7em 0.5em 1.9em 0.4em;
        }
    }
    &--thumb {
        display: inline-block;
        position: relative;
        width: 5.5rem;
        height: 5.5rem;
        vertical-align: middle;

        img {
            width: 100%;
            height: 100%;
        }
    }
    &--fullAddress {
        position: relative;
        display: inline-block;
        font-weight: 700;
        color: #214d90;
        padding-left: 1.2rem;
        border-radius: .5rem;
        margin-left: .4rem;
        vertical-align: middle;
        width: calc(100% - 7.5rem);
    }
    &--valRef {
        width: 5%;
    }
    &--consentNumber {
        position: relative;
        width: 8%;
        word-break: break-all;
        text-align: right;
        .row-link {
            padding: 1.7em 1.5em 1.9em;
        }
    }
    &--consentCost {
        width: 7%;
        text-align: right;
    }
    &--consentDescription {
        width: 18%;
        text-align: left;
        padding-left: 1.5em;
        word-break: break-word;
        padding-right: 1.0em;
    }
    &--valuer {
        width: 7%;
        text-align: left;
    }
    &--floorPlans {
        width: 7%;
        position: relative;
        text-align: center;

        .row-link {
            padding: 1.7em 1.5em 1.9em;
        }
    }
    &--status {
        width: 8%;
        padding-left: 1.5em;
        text-align: right;
    }
    &--consentDueDate {
        width: 8%;
        text-align: right;
    }
    &--actions {
        width: 12%;
        text-align: right;
        white-space: nowrap;
        .row-link {
            padding: 1.7em 0.5em 1.9em 0.4em;
        }
        .objections.row-link{
            padding: 1.7em 0.4em 1.9em 0.2em;
            color: var(--color-red-500);
            &:hover {
                background:rgba(255,255,255,.25);
                box-shadow:0 0 0 .15rem rgba(255,0,0,.5);
            }
        }
        .qivs.row-link {
            padding: 1.7em 1.5em 1.9em;
        }
    }
    &--objector {
        width: 10%;
        text-align: center;
    }
    &--center {
        width: 8%;
        text-align: center;
    }
    &--action {
        padding-right: 1rem;
        width: 12%;
        text-align: center;
    }
    &--category {
        width: 5%;
        text-align: center;
    }
    &--docs {
        width: 3%;
        text-align: center;
    }
    &--width-6 {
        width: 6%;
        text-align: center;
    }
}
.highlight {
    background-color: #ffffef !important;
}
.qvtd-table-fixed {
    table-layout: fixed;
}

.qv-qivs-link {
    background: #fc932f;
    i {
        display: inline-block;
        font-size: 1.4rem;
        vertical-align: text-top;
        width: 1.5rem;
        margin-right: -3px;
    }
}

.qv-edit-valuation {
    background: #283c64;
}

.qv-valuation-error {
    font-size: 1rem;
    color: var(--qv-color-red);
    margin-top: 0.5rem;
}
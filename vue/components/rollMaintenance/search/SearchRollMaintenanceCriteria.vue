<template>
    <div class="col-container qv-search-background">
        <div class="col-row">
            <div data-cy="territorial-authorities" class="col col-2">
                <div class="qv-ta-search-container">
                    <territorial-authority :validation-error="emptyTACodes" :customLabelStyle="taLabelStyle" />
                </div>
                <span data-cy="territorial-authorities-error-message" v-if="emptyTACodes" class="error-message">{{ invalidTaCodesMessage }}</span>
            </div>
            <div data-cy="activity-statuses" class="col col-6">
                <div class="righty status-actions">
                    <a data-cy="activity-statuses-all-active" @click="activeStatuses">All Active</a> |
                    <a data-cy="activity-statuses-clear" @click="clearActivityStatuses">Clear</a>
                </div>
                <label>
                    <span  class="label qv-label-white">Activity Statuses</span>
                    <classification-dropdown
                        id="activityStatuses"
                        data-cy="activity-statuses-dropdown"
                        type="text"
                        :value="searchCriteria.activityStatuses"
                        category="RollMaintenanceActivityStatus"
                        :taggable="true"
                        :multiple="true"
                        hide-codes
                        @input="updateCriteriaItem"
                    />
                </label>
            </div>
            <div data-cy="plan-status" class="col col-3">
                 <label>
                     <div class="righty status-actions">
                        <a data-cy="plan-status-clear" @click="clearPlanStatusTags">Clear</a>
                    </div>
                    <span  class="label qv-label-white">Plan Status</span>
                    <multiselect
                                data-cy="plan-status-multi-select-combo"
                                class="multiselect"
                                :value="searchCriteria.planStatusValues"
                                :options="Object.values(options)"
                                :close-on-select="true"
                                select-label="⏎ select"
                                deselect-label="⏎ remove"
                                placeholder=""
                                :multiple="true"
                                :taggable="true"
                                @select="updatePlanStatusState"
                                @remove="clearPlanStatuses"/>
                 </label>
            </div>
            <div data-cy="construction-complete" class="col col-1">
                <label title="Construction Complete">
                    <span class="label qv-label-white">Construction Comp</span>
                    <yes-no-indeterminate-dropdown
                        id="constructionComplete"
                        data-cy="construction-complete-dropdown"
                        :value="searchCriteria.buildingConsentCriteria.constructionComplete"
                        @input="updateConsentCriteriaItem"
                    />
                </label>
            </div>
        </div>
        <div  class="col-row">
            <div data-cy="categories" class="col col-2">
                <label title="To search for different types of categories enter each separated by a comma, e.g. RC*, RH*">
                    <span class="label qv-label-white">Categories</span>
                    <input
                        type="text"
                        :value="searchCriteria.ratingUnitCategories"
                        @change="updateCriteriaItem({
                            id: 'ratingUnitCategories',
                            value: $event.target.value
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                </label>
            </div>
            <div data-cy="bc-number" class="col col-2">
                <label>
                    <span class="label qv-label-white">BC Number</span>
                    <input
                        type="text"
                        :value="searchCriteria.buildingConsentCriteria.consentNumber"
                        @change="updateConsentCriteriaItem({
                            id: 'consentNumber',
                            value: $event.target.value
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                </label>
            </div>
            <div data-cy="bc-criteria-qpid" class="col col-1">
                <label title="Search by QPID">
                    <span class="label qv-label-white">QPID</span>
                    <number-input
                        format="0"
                        :value="searchCriteria.qpid"
                        @change="updateCriteriaItem({
                            id: 'qpid',
                            value: toNumber($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    />
                </label>
            </div>
            <!-- VALREF -->
            <div data-cy="roll-number" class="col col-1">
                <label title="Valuation reference roll number">
                    <span class="label qv-label-white">Roll Number</span>
                    <number-input
                        format="0"
                        :value="searchCriteria.rollNumber"
                        @change="updateCriteriaItem({
                            id: 'rollNumber',
                            value: toNumber($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    />
                </label>
            </div>
            <div data-cy="assessment" class="col col-1">
                <label title="Valuation reference assessment number">
                    <span class="label qv-label-white">Assessment Number</span>
                    <number-input
                        format="0"
                        :value="searchCriteria.assessmentNumber"
                        @change="updateCriteriaItem({
                            id: 'assessmentNumber',
                            value: toNumber($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    />
                </label>
            </div>
            <div data-cy="suffix" class="col col-1">
                <label title="Valuation reference assessment suffix">
                    <span class="label qv-label-white">Suffix</span>
                    <input
                        type="text"
                        :value="searchCriteria.assessmentSuffix"
                        @change="updateCriteriaItem({
                            id: 'assessmentSuffix',
                            value: $event.target.value
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                </label>
            </div>
            <div
                class="col col-2"
                :class="{valError: !isValidRange(
                    'date',
                    searchCriteria.initiatedDateFrom,
                    searchCriteria.initiatedDateTo
                )}"
            >
                <label data-cy="bc-issue-date" class="qv-label-txt">
                    <span  class="label qv-label-white">BC Issue Date</span>
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.initiatedDateFrom | date"
                        @change="updateCriteriaItem({
                            id: 'initiatedDateFrom',
                            value: toDate($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                    to
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.initiatedDateTo | date"
                        @change="updateCriteriaItem({
                            id: 'initiatedDateTo',
                            value: toDate($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                </label>
                <div
                    v-if="!isValidDate(searchCriteria.initiatedDateFrom)
                        || !isValidDate(searchCriteria.initiatedDateTo)
                    "
                    class="valMessage"
                >
                    <label>Date must be in DD/MM/YYYY format.</label>
                </div>
                <div
                    v-else
                    class="valMessage"
                >
                    <label data-cy="bc-issue-date-error">The To date must be equal to or greater than the From date.</label>
                </div>
            </div>
            <div
                class="col col-2"
                :class="{valError: !isValidRange(
                    'date',
                    searchCriteria.createdDateFrom,
                    searchCriteria.createdDateTo
                )}"
            >
                <label data-cy="bc-entered-date" class="qv-label-txt">
                    <span  class="label qv-label-white">BC Entered Date</span>
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.createdDateFrom | date"
                        @change="updateCriteriaItem({
                            id: 'createdDateFrom',
                            value: toDate($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                    to
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.createdDateTo | date"
                        @change="updateCriteriaItem({
                            id: 'createdDateTo',
                            value: toDate($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                </label>
                <div
                    v-if="!isValidDate(searchCriteria.createdDateFrom)
                        || !isValidDate(searchCriteria.createdDateTo)
                    "
                    class="valMessage error-cost"
                >
                    <label>Date must be in DD/MM/YYYY format.</label>
                </div>
                <div
                    v-else
                    class="valMessage error-cost"
                >
                    <label data-cy="bc-entered-date-error">The To date must be equal to or greater than the From date.</label>
                </div>
            </div>
        </div>
        <div data-cy="sale-group-codes" class="col-row">
            <div class="col col-2">
                <div>&nbsp;</div>
                <sales-group-and-rolls
                    :taCodes="taCodes"
                    :selected-sale-groups="searchCriteria.saleGroupCodes"
                    :selected-rolls="searchCriteria.rollNumbers"
                    @setRolls="onSetRolls"
                />
            </div>
            <div data-cy="bc-valuers" class="col col-2">
                <div data-cy="clear-valuers" class="righty status-actions">
                    <a @click="valuersSelected = []" class="qv-clear-link">Clear</a>
                </div>
                <label>
                    <span class="label qv-label-white">Valuers</span>
                    <multiselect
                        data-cy="multiselect-valuers-selected"
                        v-model="valuersSelected"
                        :options="valuersList"
                        :multiple="true"
                        :close-on-select="false"
                        :loading="valuersLoading"
                        label="name"
                        track-by="name"
                        select-label="⏎ select"
                        deselect-label="⏎ remove"
                    >
                        <template slot="selection" slot-scope="{ values, search, isOpen }">
                            <span class="multiselect__single" v-if="values.length > 2 && !isOpen">
                                {{ values.length }} options selected
                            </span>
                        </template>
                    </multiselect>
                </label>

            </div>
            <div
                class="col col-2"
                :class="{valError: !isValidRange(
                    'number',
                    searchCriteria.buildingConsentCriteria.costFrom,
                    searchCriteria.buildingConsentCriteria.costTo
                )}"
            >
                <label data-cy="bc-cost" class="qv-label-txt">
                    <span  class="label qv-label-white">BC Cost</span>
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.buildingConsentCriteria.costFrom | numeral"
                        @change="updateConsentCriteriaItem({
                            id: 'costFrom',
                            value: toNumber($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                    to
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.buildingConsentCriteria.costTo | numeral"
                        @change="updateConsentCriteriaItem({
                            id: 'costTo',
                            value: toNumber($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                </label>
                <div class="valMessage">
                    <label >The To number must be equal to or greater than the From number.</label>
                </div>
            </div>
            <div
                class="col col-2"
                :class="{valError: !isValidRange(
                    'date',
                    searchCriteria.dueDateFrom,
                    searchCriteria.dueDateTo
                )}"
            >
                <label  data-cy="bc-due-date" class="qv-label-txt">
                    <span  class="label qv-label-white">BC Due Date</span>
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.dueDateFrom | date"
                        @change="updateCriteriaItem({
                            id: 'dueDateFrom',
                            value: toDate($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                    to
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.dueDateTo | date"
                        @change="updateCriteriaItem({
                            id: 'dueDateTo',
                            value: toDate($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                </label>
                <div
                    v-if="!isValidDate(searchCriteria.dueDateFrom)
                        || !isValidDate(searchCriteria.dueDateTo)
                    "
                    class="valMessage error-cost"
                >
                    <label>Date must be in DD/MM/YYYY format.</label>
                </div>
                <div
                    v-else
                    class="valMessage error-cost"
                >
                    <label data-cy="bc-due-date-error-message">The To date must be equal to or greater than the From date.</label>
                </div>
            </div>
            <div
                class="col col-2"
                :class="{valError: !isValidRange(
                    'date',
                    searchCriteria.actionedDateFrom,
                    searchCriteria.actionedDateTo
                )}"
            >
                <label data-cy="actioned-date" class="qv-label-txt">
                    <span class="label qv-label-white">Actioned Date</span>
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.actionedDateFrom | date"
                        @change="updateCriteriaItem({
                            id: 'actionedDateFrom',
                            value: toDate($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                    to
                    <input
                        type="text"
                        class="sc-input--range sc-input-text"
                        :value="searchCriteria.actionedDateTo | date"
                        @change="updateCriteriaItem({
                            id: 'actionedDateTo',
                            value: toDate($event.target.value)
                        })"
                        @keyup.enter="handleKeyupEnter"
                    >
                </label>
                <div
                    v-if="!isValidDate(searchCriteria.actionedDateFrom)
                        || !isValidDate(searchCriteria.actionedDateTo)
                    "
                    class="valMessage error-cost"
                >
                    <label>Date must be in DD/MM/YYYY format.</label>
                </div>
                <div
                    v-else
                    class="valMessage error-cost"
                >
                    <label data-cy="actioned-date-error-message">The To date must be equal to or greater than the From date.</label>
                </div>
            </div>
            <div data-cy="notes-for-valuer" class="col col-1">
                <label>
                    <span  class="label qv-label-white">Notes for Valuer</span>
                    <yes-no-indeterminate-dropdown
                        data-cy="notes-for-valuer-dropDown"
                        id="needsMoreInformation"
                        :value="searchCriteria.needsMoreInformation"
                        @input="updateCriteriaItem"
                    />
                </label>
            </div>
            <div data-cy="needs-inspection" class="col col-1">
                <label>
                    <span class="label qv-label-white">Needs Inspection</span>
                    <yes-no-indeterminate-dropdown
                        data-cy="needs-inspection-dropdown"
                        id="needsInspection"
                        :value="searchCriteria.needsInspection"
                        @input="updateCriteriaItem"
                    />
                </label>
            </div>
        </div>
        <div class="col-row">
            <div class="righty search-control-buttons">
                <div
                    data-cy="export-button"
                    title="Export Results (limit 100,000)"
                    class="exportResults mdl-button mdl-js-button mdl-button--icon"
                    :class="{ 'disabled': exportResultsDisabled }"
                    @click="exportResults()"
                >
                    <i class="material-icons md-dark">&#xE06F;</i>
                </div>
                <button
                    data-cy="clear-btn"
                    class="mdl-button mdl-js-button mdl-button--raised
                        mdl-js-ripple-effect advSearchClear
                    "
                    title="Clear Search Criteria"
                    @click="clearSearch"
                >
                    Clear
                </button>
                <button
                    data-cy="search-btn"
                    class="mdl-button mdl-js-button mdl-button--raised
                    mdl-js-ripple-effect mdl-button--colored"
                    :class="{disabled: !isValid || loading || emptyTACodes}"
                    @click="search"
                >
                    Search
                </button>
            </div>
        </div>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
            />
        </alert-modal>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import moment from 'moment';
import numeral from 'numeral';
import commonUtils from '../../../utils/CommonUtils';
import { submitMonarchExport } from '../../reports/utils.js'
import Multiselect from 'vue-multiselect';

import useValuerInfo from '@/composables/useValuerInfo';
const { valuers, valuersLoaded } = useValuerInfo();

export default {
    mixins: [commonUtils],
    components: {
        'territorial-authority': () => import('../../filters/TerritorialAuthority.vue'),
        'classification-dropdown': () => import('../../common/form/ClassificationDropdown.vue'),
        'yes-no-indeterminate-dropdown': () => import(/* webpackChunkName: "YesNoIndeterminateDropdown" */'../../common/form/YesNoIndeterminateDropdown.vue'),
        'number-input': () => import(/* webpackChunkName: "NumberInput" */ '../../common/form/NumberInput.vue'),
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../../common/modal/AlertModal.vue'),
        'sales-group-and-rolls': () => import('../ratingValuation/common/SalesGroupsAndRolls.vue'),
        Multiselect,
    },
    props: {
        taCodes: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            invalidTaCodesMessage: 'Territorial Authority is required',
            taLabelStyle: {
                color: '#fff',
            },
            valuersSelected: [],
            options:
            {
                'plansAvailable': 'Plans Drawn',
                'plansNeeded':'Plans Required',
                'plansUnavailable':'Plans Unavailable',
                'plansNotRequired': 'Plans Not Required',
                'plansRequestedWithTa': 'Plans Requested with TA',
                'plansUnknown' : 'Plans Unknown'
            },
            exportResultsDisabled: false,
            modal: {
                mode: 'warning',
                isOpen: false,
                heading: 'heading',
                message: '',
                messages: [],
                cancelText: 'No',
                cancelAction: () => { },
                confirmText: 'Yes',
                confirmAction: () => { },
                code: '',
            },
        }
    },
    computed: {
        ...mapState('rollMaintenanceSearch', [
            'searchCriteria',
            'lastResults',
            'loading',
            'exportSearchResults',
        ]),
        activityStatusOptions() {
            return this.$store.getters.getCategoryClassifications('RollMaintenanceActivityStatus');
        },
        isValid() {
            return this.isValidRange('number', this.searchCriteria.buildingConsentCriteria.costFrom, this.searchCriteria.buildingConsentCriteria.costTo)
                && this.isValidRange('date', this.searchCriteria.initiatedDateFrom, this.searchCriteria.initiatedDateTo)
                && this.isValidRange('date', this.searchCriteria.createdDateFrom, this.searchCriteria.createdDateTo)
                && this.isValidRange('date', this.searchCriteria.dueDateFrom, this.searchCriteria.dueDateTo)
                && this.isValidRange('date', this.searchCriteria.actionedDateFrom, this.searchCriteria.actionedDateTo);
        },
        valuersLoading() {
            return !valuersLoaded.value;
        },
        valuersList() {
            return valuers?.value ?? [];
        },
        emptyTACodes() {
            return !this.taCodes || this.taCodes.length === 0;
        },
    },
    methods: {
        updatePlanStatusState(event) {
            if(this.searchCriteria.planStatusValues === undefined){
                this.searchCriteria.planStatusValues = [];
            }
            if(!this.searchCriteria.planStatusValues.includes(event)){
                switch(event) {
                    case 'Plans Required':
                        this.updateConsentCriteriaItem({ id: 'plansNeeded', value: true });
                        this.searchCriteria.planStatusValues.push(event);
                        break;
                    case 'Plans Drawn':
                        this.updateConsentCriteriaItem({ id: 'plansAvailable', value: true });
                        this.searchCriteria.planStatusValues.push(event);
                        break;
                    case 'Plans Not Required':
                        this.updateConsentCriteriaItem({ id: 'plansNotRequired', value: true });
                        this.searchCriteria.planStatusValues.push(event);
                        break;
                    case 'Plans Unavailable':
                        this.updateConsentCriteriaItem({ id: 'plansUnavailable', value: true });
                        this.searchCriteria.planStatusValues.push(event);
                        break;
                    case 'Plans Requested with TA':
                        this.updateConsentCriteriaItem({ id: 'plansRequestedWithTa', value: true });
                        this.searchCriteria.planStatusValues.push(event);
                        break;
                    case 'Plans Unknown':
                        this.updateConsentCriteriaItem({ id: 'plansUnknown', value: true });;
                        this.searchCriteria.planStatusValues.push(event);
                        break;
            }
        }},
        activeStatuses() {
            this.searchCriteria.activityStatuses = [
                {code: 'NEW'},
                {code: 'PENDING'},
                {code: 'BLOCKED'},
                {code: 'INSPECT'},
                {code: 'READY'},
            ];
        },
        clearActivityStatuses() {
            this.searchCriteria.activityStatuses = [];
        },
        clearPlanStatuses(status) {
            var planStatusOptionKey = this.getKeyByValue(this.options, status);
            this.updateConsentCriteriaItem({ id: planStatusOptionKey, value: null });
            this.searchCriteria.planStatusValues = this.searchCriteria.planStatusValues.filter(e => e != status);
        },
        getKeyByValue(object, value){
            return Object.keys(object).find(key => object[key] === value);
        },
        clearPlanStatusTags(){
            for(const value in this.searchCriteria.planStatusValues){
                var planStatusOptionKey = this.getKeyByValue(this.options, this.searchCriteria.planStatusValues[value]);
                this.updateConsentCriteriaItem({ id: planStatusOptionKey, value: null });
            }
            this.searchCriteria.planStatusValues = [];
        },
        search() {
            if (!this.isValid || this.loading) {
                return;
            }
            // reset offset
            this.updateCriteriaItem({ id: 'offset', value: 0 });
            this.$emit('search', false);
        },
        // TODO Maybe set a default search criteria instead of clearing
        clearSearch() {
            this.updateCriteriaItem({ id: 'ratingUnitCategories', value: null });
            this.updateCriteriaItem({ id: 'qpid', value: null });
            this.updateCriteriaItem({ id: 'initiatedDateFrom', value: null });
            this.updateCriteriaItem({ id: 'initiatedDateTo', value: null });
            this.updateCriteriaItem({ id: 'createdDateFrom', value: null });
            this.updateCriteriaItem({ id: 'createdDateTo', value: null });
            this.updateCriteriaItem({ id: 'dueDateFrom', value: null });
            this.updateCriteriaItem({ id: 'dueDateTo', value: null });
            this.updateCriteriaItem({ id: 'actionedDateFrom', value: null });
            this.updateCriteriaItem({ id: 'actionedDateTo', value: null });
            this.updateCriteriaItem({ id: 'rollNumber', value: null });
            this.updateCriteriaItem({ id: 'assessmentNumber', value: null });
            this.updateCriteriaItem({ id: 'assessmentSuffix', value: null });
            this.updateCriteriaItem({ id: 'needsMoreInformation', value: null });
            this.updateCriteriaItem({ id: 'needsInspection', value: null });
            this.updateConsentCriteriaItem({ id: 'consentNumber', value: null });
            this.updateConsentCriteriaItem({ id: 'costFrom', value: null });
            this.updateConsentCriteriaItem({ id: 'costTo', value: null });
            this.updateConsentCriteriaItem({ id: 'natureOfWorks', value: null });
            this.clearPlanStatusTags();
            this.updateConsentCriteriaItem({ id: 'constructionComplete', value: null });
            this.valuersSelected = [];
        },
        updateNatureOfWorks(item) {
            this.$store.commit('rollMaintenanceSearch/setBuildingConsentCriteriaItem', item);
        },
        updateCriteriaItem(data) {
            if (data.value === '') {
                data.value = null;
            }
            this.$store.commit('rollMaintenanceSearch/setSearchCriteriaItem', data);
        },
        updateConsentCriteriaItem(data) {
            this.$store.commit('rollMaintenanceSearch/setBuildingConsentCriteriaItem', data);
        },
        onSetRolls({rolls, total, saleGroupDetails} = {}) {
            const bySaleGroupDetails = (saleGroupDetails && saleGroupDetails.length > 0) ? saleGroupDetails.map(sgd => sgd.rollNumbers.map(sgn => sgn.rollNumber)).flat() : [];
            const byRollNumbers = (rolls && rolls.length > 0) ? rolls : [];
            const allRollNumbers = [...bySaleGroupDetails, ...byRollNumbers];

            // saleGroupCodes needs to be from: sgt.code2 value, rather than sgt.sale_group_id. however sgt.code2 can duplicate across TAs. sending through expanded set of roll numbers
            this.updateCriteriaItem({ id: 'saleGroupCodes', value: [] });
            this.updateCriteriaItem({ id: 'rollNumbers', value: allRollNumbers });
            if(total > 0) {
                ['rollNumber', 'assessmentNumber', 'assessmentSuffix'].forEach((id) => {
                    this.updateCriteriaItem({ id: id, value: null });
                });
            }
        },
        isValidRange(type, from, to) {
            // TODO Replace with validation mixin
            if (type === 'number') {
                if (from != null && to != null) {
                    return (!Number.isNaN(from) && !Number.isNaN(to) && from <= to);
                }
            } else if (type === 'date') {
                if (from && to) {
                    return (
                        moment(from).isValid()
                        && moment(to).isValid()
                        && moment(to).isSameOrAfter(from)
                    );
                }
                if (from) {
                    return moment(from).isValid();
                }
                if (to) {
                    return moment(to).isValid();
                }
            }
            return true;
        },
        isValidDate(date) {
            return moment(date).isValid();
        },
        toNumber(text) {
            const n = numeral(text).value();
            if (Number.isNaN(n)) return null;
            return n;
        },
        toDate(text) {
            const formattedDate = moment(text, 'DD/MM/YYYY');
            if (!formattedDate.isValid()) {
                return null;
            }
            return formattedDate.format('YYYY-MM-DD');
        },
        handleKeyupEnter(event) {
            // Manually blur and focus element to force IE 11 to fire an OnChange event to change the search input
            event.srcElement.blur();
            event.srcElement.focus();
            this.search();
        },
        async exportResults() {
            this.exportResultsDisabled = true;

            const submitResult = await submitMonarchExport(
                'MONARCH_BUILDING_CONSENT_EXPORT',
                this.lastResults.searchCriteria,
                this.lastResults.results.totalResultCount
            );

            if (submitResult) {
                if (submitResult.cancelText === 'View My Reports') {
                    submitResult.cancelAction = () => { this.$router.push({ name: 'report-dashboard-my-reports' }); }
                }
                this.setModal(submitResult);
            }

            this.exportResultsDisabled = false;
        },
        setModal(modal) {
            this.modal = modal;
        },
        modalCancel() {
            this.modal.isOpen = false;
            this.modal.cancelAction();
        },
        modalConfirm() {
            this.modal.isOpen = false;
            this.modal.confirmAction();
        },
    },
    watch: {
        valuersSelected: function(newValue,oldValue) {
            const mappedNames = newValue.map(v => v.ntUsername);
            this.updateCriteriaItem({ id: 'ntUserNames', value: mappedNames });
        },
    },
};
</script>

<!-- TODO This shouldnt be component scoped but be global instead -->
<style lang="scss" scoped="true" src="../rollMaintenance.scss"></style>

<style lang="scss" scoped>

.search-control-buttons {
    line-height: 5.5;
    margin-bottom: -7.5em;
}

.sc-input {
    &--range {
        width: 45% !important;
    }
}

.status-actions {
    font-size: 1.1rem;
    color: rgb(252,147,47);
    a {
        color: rgb(252,147,47);
    }
}

.sc-input-text {
    color : #35495e;
}

</style>

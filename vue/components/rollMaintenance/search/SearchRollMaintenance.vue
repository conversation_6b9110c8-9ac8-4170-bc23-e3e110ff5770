<template>
    <div class="resultsWrapper roll-maintenance-activity-list">
        <div class="resultsInner-wrapper mdl-shadow--3dp">
            <div class="resultsTitle qv-search-title">
                <h1>Building Consents</h1>
            </div>
            <search-roll-maintenance-criteria
                v-if="showCriteria"
                :taCodes="taCodes"
                @search="search"
            />
            <div class="resultsFound">
                <p v-if="loading">
                    Loading results...
                </p>
                <p v-else>
                    <span class="qv-obj-button-wrapper">
                        <div class="search-control-buttons-padded" style="display: flex; gap: 10px;">
                            <button data-cy="bulk-assign" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored" :class="{ 'qv-disabled': selectedAssignableActivityIds.length == 0 }" @click="showAssignValuersModal = true" v-if="canAssignValuers">
                                Assign Valuers
                            </button>
                            <button data-cy="show-map" :map-url="mapUrl" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored-darkgrey" @click="showMap">
                                Map
                                <i class="material-icons">call_made</i>
                            </button>
                            <button data-cy="consent-report" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored-grey" @click.prevent="generateInspectionReport">
                                Inspection Report
                            </button>
                        </div>
                    </span>
                    Showing {{ firstResultIndex | numeral }}
                    to {{ lastResultIndex | numeral }}
                    of {{ totalResultCount | numeral }} results found.
                    {{ countSelected }} selected.
                    <br>
                    <a
                        data-cy="clear-all"
                        href="#"
                        @click.prevent="clearAll"
                    >
                        Clear All Selected
                    </a>
                </p>
                <p
                    v-if="error"
                    class="message message-error"
                >
                    {{ error }}
                </p>
                <p
                    v-if="reportException"
                    class="message message-error"
                >
                    {{ reportException }}
                </p>
            </div>
            <div class="paginator--top" data-cy="paginate-top">
                <paginate
                    v-if="!loading || totalResultsVisible > 0"
                    v-model="page"
                    :page-count="totalPageCount"
                />
            </div>
            <div v-if="!loading">
                <table  data-cy="bc-search-table" class="table qvtd-table-fixed">
                    <tr class="sortRow roll-maintenance-activity-list__headerRow">
                        <th  data-cy="checkbox-header" class="colHeader activity-list--select">
                            <input type="checkbox" data-cy="select-all" :checked="selectAll" @change="toggleSelected">
                        </th>
                        <th :class="sortField === 'ADDRESS' && 'active'" class="colHeader activity-list--address">
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'ADDRESS'"
                                label="Address"
                                column-name="ADDRESS"
                                @onchange="sort"
                            />
                        </th>
                        <th :class="sortField === 'VALUATION_REFERENCE' && 'active'" class="colHeader activity-list--valRef">
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'VALUATION_REFERENCE'"
                                label="Val Ref"
                                data-cy="val-ref-header"
                                column-name="VALUATION_REFERENCE"
                                @onchange="sort"
                            />
                        </th>
                        <th
                            :class="sortField === 'REFERENCE' && 'active'"
                            class="colHeader activity-list--consentNumber"
                        >
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'REFERENCE'"
                                label="BC Number"
                                data-cy="bc-number-header"
                                column-name="REFERENCE"
                                @onchange="sort"
                            />
                        </th>
                        <th
                            :class="sortField === 'VALUE' && 'active'"
                            class="colHeader activity-list--consentCost"
                        >
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'VALUE'"
                                label="BC Cost"
                                data-cy="bc-cost-header"
                                column-name="VALUE"
                                @onchange="sort"
                            />
                        </th>
                        <th
                            :class="sortField === 'DESCRIPTION' && 'active'"
                            class="colHeader activity-list--consentDescription"
                        >
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'DESCRIPTION'"
                                label="Description"
                                data-cy="description-header"
                                column-name="DESCRIPTION"
                                @onchange="sort"
                            />
                        </th>
                        <th
                            :class="sortField === 'VALUER' && 'active'"
                            class="colHeader activity-list--valuer"
                        >
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'VALUER'"
                                label="Valuer"
                                column-name="VALUER"
                                @onchange="sort"
                            />
                        </th>
                        <th
                            :class="sortField === 'FLOOR_PLANS' && 'active'"
                            class="colHeader activity-list--floorPlans"
                        >
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'PLANS_DRAWN'"
                                label="Plans Drawn"
                                column-name="PLANS_DRAWN"
                                @onchange="sort"
                            />
                        </th>
                        <th
                            :class="sortField === 'STATUS' && 'active'"
                            class="colHeader activity-list--status"
                        >
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'STATUS'"
                                label="Status"
                                column-name="STATUS"
                                directionTitleTextPrefix="job completion status"
                            />
                        </th>
                        <th class="colHeader activity-list--status-icon" />
                        <th
                            :class="sortField === 'DUE_DATE' && 'active'"
                            class="colHeader activity-list--consentDueDate"
                        >
                            <sort-header
                                :direction="direction"
                                :active="sortField === 'DUE_DATE'"
                                label="Consent Due Date"
                                data-cy="consent-header"
                                column-name="DUE_DATE"
                                @onchange="sort"
                            />
                        </th>
                        <th class="colHeader activity-list--actions" />
                    </tr>
                    <activity-row
                        v-for="activity in results"
                        :key="activity.id"
                        :activity="activity"
                        :selected="isSelected(activity.id)"
                        :class="{ 'disable-rows': loading }"
                        @checked="select"
                        @update-qpid="handleUpdateQpid"
                    />
                </table>
                <paginate
                    v-model="page"
                    :page-count="totalPageCount"
                />
            </div>
            <div
                v-if="!loading && totalResultCount === 0"
                class="no-results"
            >
                <p>No roll maintenance activities found.</p>
                <p>Adjust search criteria and try again.</p>
            </div>
            <div
                v-if="loading"
                class="results-loading"
            >
                <div class="loadingSpinner loadingSpinnerSearchResults" />
            </div>
        </div>
        <alert-modal data-cy="show-assign-valuers-modal" v-if="showAssignValuersModal" :success="!assignValuersMessages.warning">
            <h3>{{ assignValuersMessages.title }}</h3>
            <p :class="{ warning: assignValuersMessages.warning }">{{ assignValuersMessages.message }}</p>

            <div
                v-if="selectedAssignableActivityIds.length > 0 && selectedAssignableActivityIds.length <= BULK_ASSIGN_MAX_LIMIT && !isWaiting && !assignValuersCompleted"
                >
                <label class="valuer-select">
                    <span class="label">Valuer</span>
                    <multiselect
                        data-cy="multiselect-valuers"
                        v-model="valuerSelected"
                        :options="valuers" :close-on-select="true"
                        label="name" track-by="name"
                        />
                </label>
                <label class="confirm-assign">
                    <input type="checkbox" v-model="confirmAssign" data-cy="checkbox-confirm-assign" id="confirmAssign">
                    <span>Are you sure you want to bulk assign
                        {{ assignMessage }} to {{ selectedAssignableActivityIds.length }}
                        consents?</span>
                </label>
            </div>

            <template v-slot:buttons>
                <div class="alertButtons">
                    <button id="close" data-cy="close-assign-valuers" class="mdl-button mdl-button--mini lefty" @click="resetAssignments">
                        CLOSE
                    </button>
                    <button v-if="confirmAssign && assignMessage" data-cy="confirm-assign-valuers" id="assign-valuers"
                        class="mdl-button mdl-button--mini" @click="assignValuers">
                        ASSIGN
                    </button>
                </div>
            </template>
        </alert-modal>
        <alert-modal data-cy="view-modal-message" v-if="showModal">
            <h3>{{ showModalMessage.title }}</h3>
            <p :class="{ warning: showModalMessage.warning }">{{ showModalMessage.message }}</p>
            <template v-slot:buttons>
                <div class="alertButtons">
                    <button id="close" data-cy="close-modal" class="mdl-button mdl-button--mini righty" @click="resetAssignments">
                        CLOSE
                    </button>
                </div>
            </template>
        </alert-modal>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import axios from '../../../utils/AxiosHeaders';
import { openMap } from '../../../utils/QivsUtils';
import { store } from '../../../DataStore';
import Multiselect from 'vue-multiselect';
import { fetchValuers } from './../objections/utils.js';
import { RECEPTIONIST_TYPIST, AREA_VALUER, MANAGING_SENIOR_VALUER, SENIOR_VALUER } from '@/utils/Roles';

export default {
    components: {
        'activity-row': () => import('./SearchRollMaintenanceResult.vue'),
        'sort-header': () => import('../../common/SortHeader.vue'),
        'search-roll-maintenance-criteria': () => import('./SearchRollMaintenanceCriteria.vue'),
        'alert-modal': () => import('../../common/modal/AlertModal.vue'),
        Multiselect,
        paginate: () => import(/* webpackChunkName: "Paginate" */ '../../common/paginate/paginate.vue')
    },
    data() {
        return {
            showCriteria: true,
            isFirstTime: true,
            selectAll: false,
            selectedRollMaintenanceActivityIds: [],
            selectedAssignableActivityIds: [],
            reportException: null,
            showAssignValuersModal: false,
            valuers: [],
            valuerSelected: null,
            consentSelected: false,
            isShowMaps: false,
            showModal: false,
            confirmAssign: false,
            isWaiting: false,
            assignValuersSuccess: false,
            assignValuersRowsAffected: 0,
            assignValuersCompleted: false,
            BULK_ASSIGN_MAX_LIMIT: 500,
            qpidList: [],
            selectedConsents: 0,
            mapUrl: null,
        };
    },
    computed: {
        ...mapState('rollMaintenanceSearch', [
            'searchCriteria',
            'lastResults',
            'loading',
            'error',
        ]),
        ...mapGetters('rollMaintenanceSearch', [
            'totalPageCount',
            'currentPage',
        ]),
        ...mapState({
            taCodes: state => state.taCodes.taCodes,
            taCodesLoaded: state => state.taCodes.taCodesLoaded,
        }),
        isTAUser() {
            return !store.state.userData.isInternalUser && store.state.userData.isTAUser;
        },
        canAssignValuers() {
            return store.getters['userData/userHasMonarchRole']([RECEPTIONIST_TYPIST, AREA_VALUER, MANAGING_SENIOR_VALUER, SENIOR_VALUER]);
        },
        taCodes() {
            return this.isTAUser
                ? [store.state.userData.userTACode].map(item => parseInt(item, 10))
                : store.state.taCodes.taCodes.map(item => parseInt(item, 10));
        },
        results() {
            if (!this.lastResults.results) return false;
            return this.lastResults.results.rollMaintenanceActivities;
        },
        totalResultCount() {
            if (!this.lastResults || !this.lastResults.results) return 0;
            return this.lastResults.results.totalResultCount;
        },
        totalResultsVisible() {
            if (!this.results) return 0;
            return this.results.length;
        },
        firstResultIndex() {
            return this.totalResultCount > 0 ? this.searchCriteria.offset + 1 : 0;
        },
        lastResultIndex() {
            return this.searchCriteria.offset + this.totalResultsVisible;
        },
        direction() {
            return (this.searchCriteria.sortDescending ? 'DESC' : 'ASC');
        },
        sortField() {
            return this.searchCriteria.sortField;
        },
        page: {
            get() {
                return this.currentPage;
            },
            set(value) {
                this.changePage(value);
            },
        },
        isSelected() {
            return activityId => this.selectedRollMaintenanceActivityIds.includes(activityId);
        },
        countSelected() {
            return this.selectedRollMaintenanceActivityIds.length;
        },
        isSearchingByQpid() {
            return this.$route.query && this.$route.query.qpid != null;
        },
        assignMessage() {
            let message = '';
            if (this.valuerSelected) {
                message += this.valuerSelected.name;
            }
            return message;
        },
        showModalMessage(){
            if (this.selectedRollMaintenanceActivityIds.length === 0 && this.consentSelected ) {
                return {
                    title: 'Create Consent Inspection Report',
                    message: 'No consent selected. Please select consents for the Inspection report to be created.',
                    warning: true
                };
            }
            if (this.selectedRollMaintenanceActivityIds.length > 500 && this.consentSelected) {
                return {
                    title: 'Create Consent Inspection Report Limit exceeded',
                    message: 'The report cannot be generated as more than 500 activities have been selected, please reduce your selection.',
                    warning: true
                };
            }
            if (this.selectedConsents > 100 && this.isShowMaps)
            {
                return {
                    title: 'Limit exceeded',
                    message: 'A maximum of 100 properties can be selected. Please refine your selection before selecting Map',
                    warning: true
                };
            }
            if (this.selectedConsents === 0 && this.isShowMaps){
                return {
                    title: 'Show Map',
                    message: 'Please select a consent before selecting Map',
                    warning: true
                };
            }
        },
        assignValuersMessages() {
            if (this.isWaiting) {
                return {
                    title: 'Assigning Valuers...',
                    message: '',
                    warning: false
                };
            }
            if (this.assignValuersCompleted) {
                return {
                    title: this.assignValuersSuccess ? 'Success' : 'Error',
                    message: this.assignValuersSuccess ? `${this.assignValuersRowsAffected} of ${this.selectedAssignableActivityIds.length} consents are successfully assigned.` : 'Bulk assignment error.',
                    warning: (!this.assignValuersSuccess) || this.assignValuersRowsAffected === 0,
                };
            }
            return {
                title: 'Assign Valuers',
                message: this.selectedRollMaintenanceActivityIds.length > this.BULK_ASSIGN_MAX_LIMIT ?
                    `The number of bulk assignment exceed the limit of ${this.BULK_ASSIGN_MAX_LIMIT} records` :
                    `${this.selectedAssignableActivityIds.length} of ${this.selectedRollMaintenanceActivityIds.length} consents are able to be assigned.`,
                warning: this.selectedAssignableActivityIds.length == 0 || this.selectedAssignableActivityIds.length > this.BULK_ASSIGN_MAX_LIMIT,
            };
        },
    },
    watch: {
        '$route'() {
            const qpid = this.$route.query && this.$route.query.qpid;
            this.handleRouteChange(qpid);
        }
    },
    mounted() {
        // Choose search
        const qpid = this.$route.query && this.$route.query.qpid;
        this.handleRouteChange(qpid);
        if (qpid)
            this.searchByQpid(qpid);
        else
            this.search(true);
        this.$nextTick(async () => {
            this.valuers = await fetchValuers();
        });
    },
    beforeRouteUpdate (to, from, next) {
        // Choose between qpid or normal search on a path change
        const qpid = to.query && to.query.qpid;
        this.handleRouteChange(qpid);
        next();
    },
    methods: {
        handleRouteChange(qpid) {
            if (qpid) {
                this.searchByQpid(qpid);
                return;
            }
            this.search(true);
        },
        handleUpdateQpid(activitySelected, notSelected){
            const selectedQpid = activitySelected.ratingUnit.qpid;
            const activityId = activitySelected.id;
            const qpidIndex = this.qpidList.findIndex(x => x.activityId === activityId && x.qpid === selectedQpid);
            const checkList = this.qpidList.some(x => x.activityId === activityId && x.qpid === selectedQpid);

            if (!notSelected && !checkList){
                this.qpidList.push({qpid: selectedQpid, activityId: activityId});
            }
            if (notSelected && checkList) {
                this.qpidList.splice(qpidIndex, 1);
            }
        },
        searchByQpid(qpid) {
            // search by qpid straight away
            this.selectAll = false;
            this.showCriteria = false;
            this.selectedRollMaintenanceActivityIds = [];
            this.selectedAssignableActivityIds = [];
            this.$store.dispatch('rollMaintenanceSearch/searchByQpid', qpid);
        },
        search(useLastCriteria) {
            this.selectAll = false;
            this.showCriteria = true;
            this.selectedRollMaintenanceActivityIds = [];
            this.selectedAssignableActivityIds = [];
            // If ta Codes Loaded then search by last search now
            if (this.taCodesLoaded) {
                this.$store.dispatch('rollMaintenanceSearch/searchActivities', useLastCriteria);
            } else {
                // Otherwise wait for selected TA codes to load
                this.$store.subscribe((mutation) => {
                    if (mutation.type === 'setSelectedTaCodes') {
                        // Only wait once
                        if (this.isFirstTime) {
                            this.$store.dispatch('rollMaintenanceSearch/searchActivities', useLastCriteria);
                        }
                        this.isFirstTime = false;
                    }
                });
            }
        },
        changePage(data) {
            this.selectAll = false;
            if (this.isSearchingByQpid) {
                // TODO This is QUICK FIX. Searching by qpid should probably have a separate state.
                // Because we don't want the search by qpid to store its criteria to the normal search criteria
                const searchCriteria = JSON.parse(JSON.stringify(this.lastResults.searchCriteria));
                searchCriteria.offset = (data - 1) * searchCriteria.pageSize;
                this.$store.dispatch('rollMaintenanceSearch/searchBySuppliedCriteria', searchCriteria);
            } else {
                this.$store.dispatch('rollMaintenanceSearch/changePage', data);
            }
        },
        sort(data) {
            if (this.isSearchingByQpid) {
                // TODO This is QUICK FIX. Searching by qpid should probably have a separate state.
                // Because we don't want the search by qpid to store its criteria to the normal search criteria
                const searchCriteria = JSON.parse(JSON.stringify(this.lastResults.searchCriteria));
                searchCriteria.sortField = data.columnName;
                searchCriteria.sortDescending = data.direction === 'DESC';
                this.$store.dispatch('rollMaintenanceSearch/searchBySuppliedCriteria', searchCriteria);
            } else {
                this.$store.dispatch('rollMaintenanceSearch/changeSort', data);
            }
        },
        toggleSelected() {
            this.selectAll = !this.selectAll;
            this.results.forEach(x=> {
                this.select({
                    id: x.id,
                    value: this.selectAll,
                    canAssign: !(['DONE', 'CANCELED'].includes(x.status.code))
                });
                this.handleUpdateQpid(x, !this.selectAll);
            });
        },
        clearAll() {
            this.selectAll = false;
            this.selectedRollMaintenanceActivityIds = [];
            this.selectedAssignableActivityIds = [];
            this.qpidList = [];
            this.selectedConsents = 0;
        },
        select(item) {
            if (item.value) {
                if (!this.selectedRollMaintenanceActivityIds.includes(item.id)) {
                    this.selectedRollMaintenanceActivityIds.push(item.id);
                }
                if (item.canAssign && !this.selectedAssignableActivityIds.includes(item.id)) {
                    this.selectedAssignableActivityIds.push(item.id);
                }
            } else {
                this.selectedRollMaintenanceActivityIds = this.selectedRollMaintenanceActivityIds.filter(x => x != item.id);
                this.selectedAssignableActivityIds = this.selectedAssignableActivityIds.filter(x => x != item.id);
            }
        },
        resetAssignments() {
            this.showAssignValuersModal = false;
            this.valuerSelected = null;
            this.consentSelected = false;
            this.isShowMaps = false;
            this.showModal = false;
            this.confirmAssign = false;
            this.isWaiting = false;
            this.assignValuersSuccess = false;
            this.assignValuersRowsAffected = 0;
            this.assignValuersCompleted = false;
        },
        async doAssignValuers(assignValuersPayload) {
            const { url } = jsRoutes.controllers.ConsentController.bulkAssignConsent();
            try {
                const res = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: JSON.stringify(assignValuersPayload),
                });
                return res.json();
            } catch (error) {
                throw Error(`ERR-SRM-5c1cc0: error calling bulkAssignConsent: ${error}`);
            }
        },
        async assignValuers() {
            this.isWaiting = true;
            try {
                const valuer = this.valuerSelected ? this.valuerSelected.ntUsername : null;
                const consentIds = this.selectedAssignableActivityIds.map(x => x.slice(3));
                const payload = {
                    valuer,
                    consentIds,
                };
                const response = await this.doAssignValuers(payload);
                const { status, rowsAffected } = response;
                this.assignValuersSuccess = (status === 'SUCCESS');
                if (status !== 'SUCCESS') {
                    throw Error(`ERR-SRM-919840: Error response status calling bulkAssignConsent: ${status}}`);
                }
                this.assignValuersRowsAffected = rowsAffected;
            } catch (err) {
                this.assignValuersRowsAffected = 0;
                this.assignValuersSuccess = false;
                console.error(`ERR-SRM-5c1cc0: Error calling assignValuers: ${err}`);
            } finally {
                this.assignValuersCompleted = true;
                this.valuerSelected = null;
                this.isWaiting = false;
            }
        },
        async showMap() {
            this.selectedConsents = [... new Set(this.qpidList.map(x => x.qpid))].length;
            if (this.selectedConsents > 100 || this.selectedConsents === 0) {
                this.isShowMaps = true;
                this.showModal = true;
                return;
            }
            this.openQVMap()
        },
        openQVMap() {
            const qpidListAsString = [... new Set(this.qpidList.map(x => x.qpid))].join(',');
            this.mapUrl = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/property/qv-map/0/0/${qpidListAsString}`;
            openMap(qpidListAsString);
        },
        async generateInspectionReport() {
        try {
            const activityIds = this.selectedRollMaintenanceActivityIds;
            if (activityIds.length === 0 || activityIds.length > 500) {
                this.consentSelected = true;
                this.showModal = true;
                return;
            }
            const response = await axios({
                method: 'post',
                url: jsRoutes.controllers.RollMaintenanceController.generateInspectionReport().url,
                data: { activityIds, reportType: 'BC' },
            });

            const url = response.data;
            // TODO do this properly ... using simple downloader for now...
            const link = document.createElement('a');
            link.style.display = 'none';
            link.target = '_blank';
            link.href = url;
            link.download = 'ConsentListReport.pdf';
            document.body.appendChild(link);
            link.click();
            } catch (ex) {
                this.reportException = ex;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.disable-rows {
    opacity: 0.5;
    pointer-events: none;
}
.paginator--top {
    border-bottom: 1em solid white;
}
.no-results {
    text-align: center;
    font-size: 1.5em;
    padding: 1em 0;
}
</style>

<style lang="scss" scoped="true">
.warning {
    color: red;
}

.confirm-assign {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

#confirmAssign, #confirmApprove {
    margin-right: 1rem;
}

.valuer-select {
    margin: 0.5rem 0;
}

#assign-valuers, #approve-objections {
    color: white;
    background: #283c64;
}

.no-results {
    text-align: center;
    font-size: 1.5em;
    padding: 1em 0;
}

.bulk-assign {
    display: block;
    margin-top: 1rem;
    background: #283c64;
    border: none;
    box-shadow: 1px 1px darkgrey;
    color: white;
    border-radius: 2px;
    padding: 2px 8px;
    font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
    font-size: 13px;
    font-weight: 600;
    font-style: normal;
    text-transform: uppercase;
}

.qv-obj-button-wrapper {
    display: flex;
    justify-content: space-between;
}

.search-control-buttons-padded{
    padding-top: 7px;
}
</style>

<!-- TODO This shouldnt be component scoped but be global instead -->
<style lang="scss" scoped="true" src="../rollMaintenance.scss"></style>
<style lang="scss" scoped="true" src="./searchRollMaintenance.scss"></style>

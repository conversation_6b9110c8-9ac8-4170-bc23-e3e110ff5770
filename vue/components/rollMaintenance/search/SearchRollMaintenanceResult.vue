<template>
    <tr
        :key="activity.id"
        class="resultsRow activity-list__row"
        :class="{
            overdueJob: activity.status === 'Overdue',
            completedJob: activity.status === 'Completed',
            highlight: selected,
            'red-highlight': isInactiveAssessment(activity)
        }"
        @click="toggleSelected"
    >
        <!--
            ^^ THE ELEMENT ".resultsRow.valjobRow" CAN INCLUDE
            ".overdueJob" , ".completedJob" BASED ON THE ITS DUE DATE OR STATUS
        -->

        <td class="colHeader activity-list--select">
            <input type="checkbox" :checked="selected" @click.stop="" @change="toggleSelected">
        </td>
        <td class="colCell activity-list--address">
            <router-link
                class="row-link"
                :to="{name: 'property', params: {qpid: qpid}}"
            >
                <span class="activity-list--thumb">
                    <img
                        class="primaryPhoto_thumb"
                        :src="photoUrl"
                    >
                </span>
                <div class="activity-list--fullAddress">
                    <span>{{ activity.property.address.streetAddress }}</span>
                    <span>{{ getAddressLine2() }}</span>
                </div>
            </router-link>
        </td>

        <!-- Property Address, Val/Ref, BC Number, BC Cost, Description, Consent Due Date -->
        <td class="colCell activity-list--valRef">
            {{ activity.ratingUnit.valuationReference }}
        </td>
        <td class="colCell activity-list--consentNumber">
            <a
                class="row-link"
                data-cy="activity-list-consent-number"
                :href="getRouteUrl({
                    name: 'roll-maintenance-activity',
                    params: { id: activity.id },
                })"
                @click.prevent.stop="openRouteInNewTab({
                    name: 'roll-maintenance-activity',
                    params: { id: activity.id }
                })"
            >
                {{ activity.buildingConsent.consentNumber | emptyTo('—') }}
            </a>
        </td>
        <td class="colCell activity-list--consentCost">
            {{ activity.buildingConsent.cost | currency }}
        </td>
        <td class="colCell activity-list--consentDescription">
            {{ activity.description }}
        </td>
        <td class="colCell activity-list--valuer">
            {{ activity.valuer ? activity.valuer.name : '' }}
        </td>
        <td class="colCell activity-list--floorPlans">
            <router-link
                class="row-link"
                :to="{name: 'floor-plan-measure', params: {qpid}}"
                title="View or Edit plans"
                target="_blank"
            >
                {{ activity.buildingConsent.plansObtained | yesno('No') }}
            </router-link>
        </td>
        <td class="colCell activity-list--status">
            {{ activity.status.description }}
        </td>
        <td class="colCell activity-list--status-icons">
            <i v-if="activity.needsMoreInformation" class="icon icon-needsMoreInformation" title="Notes for Valuer"></i>
            <i v-if="activity.needsInspection" class="icon icon-needsInspection" title="Needs Inspection"></i>
        </td>
        <td class="colCell activity-list--consentDueDate">
            {{ activity.estimatedInspectionReadyDate | date }}
        </td>
        <td class="colCell activity-list--action">
            <button
                v-if="ratingValuationComplete && !canBeValued"
                @click.prevent.stop="loadFinishedValuation(activity)"
                class="action-button qv-qivs-link">
                View Valuation
            </button>
            <button
                v-if="ratingValuationComplete && canBeValued && !activeObjection"
                @click.prevent.stop="loadFinishedValuation(activity)"
                class="action-button qv-qivs-link"
            >
                View Valuation
            </button>
            <p v-if="ratingValuationComplete && canBeValued && !activeObjection" class="qv-valuation-error"
                title="Create a new BC to be able to revalue this consent."
            >
                Consent previously completed.
            </p>
            <button
                v-else-if="canBeValued && activeObjection"
                @click.prevent.stop="loadDraftPropertyDetailsObjection()"
                class="action-button qv-edit-valuation">
                Objection on Property
            </button>
            <button
                v-else-if="canBeValued && !activeObjection && !relinkConsent"
                @click.prevent.stop="loadDraftPropertyDetails(activity)"
                class="action-button qv-edit-valuation">
                Edit Valuation
            </button>
            <button
                v-else-if="canBeValued && !activeObjection && relinkConsent"
                @click.prevent.stop="openQivs(masterDetailsUrl)"
                class="action-button qv-edit-valuation">
                Relink
            </button>
            <p v-if="canBeValued && !activeObjection && relinkConsent" class="qv-valuation-error"
                :title=relinkButtonTitle
            >
                Relink consent.
            </p>
            <button
                v-else-if="!canBeValued && shouldBeValued"
                @click.prevent.stop="openQivs(masterDetailsUrl)"
                class="action-button qv-qivs-link"
                title="This consent must be valued in QIVS as it is non-residential,
                is Maori land, has rating apportionments or has SRAs."
                >
                QIVS<i class="material-icons">call_made</i>
            </button>
        </td>
    </tr>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { openQivsInNewTab, openUrlInNewTab } from '../../../utils/QivsUtils';

export default {
    data() {
        return {
            ratingValuationLinkedActivities: [],
            ratingValuationId: null,
        };
    },
    props: {
        activity: {
            type: Object,
            required: true,
        },
        selected: {
            type: Boolean,
            required: true
        },
    },
    computed: {
        ...mapState('ratingValuation', [
            'ratingValuation',
            'propertyActivities'
        ]),
        ...mapState('propertyPhotos', [
            'photoUrlMap',
        ]),
        ...mapState('sra',[
            'sras',
            'sraClasses'
        ]),
        qpid() {
            return this.activity.ratingUnit.qpid;
        },
        photoUrl() {
            const photo = this.photoUrlMap[this.activity.property.id];
            return photo ? photo.smallImageUrl : '';
        },
        floorPlanUrl() {
            return this.$store.getters['userData/qivsFloorPlanUrl'](this.qpid);
        },
        masterDetailsUrl() {
            return this.$store.getters['userData/qivsMasterDetailsUrl'](this.qpid);
        },
        canBeValued() {
            return !['DONE', 'CANCELED'].includes(this.activity.status.code)
                && this.activity.property.category.code.startsWith('R')
                && !(this.activity.property.hasSameRollAndAssessment || this.activity.property.hasRatingRW);
        },
        relinkButtonTitle() {
            return this.activity.property.assessmentStatus === 'I'
                ? 'BC needs to be relinked to an active rating unit.'
                : 'BC needs to be relinked to a rating unit.';
        },
        relinkConsent() {
            return ![0,2,5].includes(parseInt(this.activity.property.apportionmentCode)) || ['D', 'I'].includes(this.activity.property.assessmentStatus);
        },
        activeObjection() {
            return this.activity.hasActiveObjection;
        },
        shouldBeValued() {
            return !['DONE', 'CANCELED'].includes(this.activity.status.code);
        },
        ratingValuationComplete() {
            return this.activity.ratingValuation && this.activity.ratingValuation.status === 'COMPLETE';
        },
        linkedActivitesCompleteOrCancelled(){
            return this.propertyActivities.find(linkedActivity => this.ratingValuation.rollMaintenanceActivityIds.includes(linkedActivity.id) && ['DONE', 'CANCELED'].includes(linkedActivity.status.code));
        }
    },
    mounted() {
        this.$store.dispatch('propertyPhotos/getPropertyPhoto', this.activity.property.id);
    },
    methods: {
        ...mapActions('sra',['loadSraProperty']),
        sendQpid(){
            this.$emit('update-qpid', this.activity, this.selected);
        },
        toggleSelected() {
            this.sendQpid();
            this.$emit('checked', {id: this.activity.id, value: !this.selected, canAssign: !(['DONE', 'CANCELED'].includes(this.activity.status.code))});
        },
        async deleteInactiveRatingValuation(ratingValId, rollID){
            let valuationObj = {
                inactiveValuation: ratingValId,
                rollMaintenanceActivityId: rollID
            }
            try {
                await this.$store.dispatch('ratingValuation/deleteInactiveRatingValuation', valuationObj);
            }
            catch(err){
                console.log("Deleting Inactive valuation job failed: ", err);
            }
        },
        async loadValuationJob(activity) {
            this.$router.push({ name: 'roll-maintenance-activity', params: { id: activity.id } });
        },
        async loadFinishedValuation(activity) {
            this.$router.push({ name: 'roll-maintenance-activity-latest-valuation', params: { rollMaintenanceActivityId: activity.id } });
        },
        async loadDraftPropertyDetails(activity) {
            await this.$store.dispatch('ratingValuation/getInProgressValuationForActivity', {
                rollMaintenanceActivityId: activity.id,
            });
            await this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');

            let hasSraSplitChanged = false;
            if (this.ratingValuation?.sra?.sras?.length > 0) {
                hasSraSplitChanged = await this.checkIfSraSplitHasChanged();
            }
            if (activity.ratingValuation && activity.ratingValuation.qpid !== activity.buildingConsent.qpid) {
                alert("The Building Consent is not on the original property it was setup on so the Job will need to be setup again.")
                await this.deleteInactiveRatingValuation(this.ratingValuation.id, activity.id);
            }
            else if (this.linkedActivitesCompleteOrCancelled) {
                alert("There is a Building Consent linked to this Job that has already been actioned so the Job will need to be set up again.");
                await this.deleteInactiveRatingValuation(this.ratingValuation.id, activity.id);
            }
            else if (hasSraSplitChanged) {
                this.ratingValuation.sra.sras = this.sras;
                await this.$store.dispatch('ratingValuation/saveValuation');
            }
            this.$router.push({
                name: 'rating-valuation-property-details',
                params: { id: this.ratingValuation.id },
            });
        },
        async checkIfSraSplitHasChanged() {
            await this.loadSraProperty(this.qpid);

            const jobCounts = new Map();
            this.ratingValuation?.sra?.sras?.forEach(s => {
                const count = jobCounts.get(s.ratingAuthority.id) || 0;
                jobCounts.set(s.ratingAuthority.id, count + 1);
            });

            const propertyCounts = new Map();
            this.sras?.forEach(s => {
                const count = propertyCounts.get(s.ratingAuthority.id) || 0;
                propertyCounts.set(s.ratingAuthority.id, count + 1);
            });

            let hasChanged = false;
            propertyCounts.forEach((count, id) => {
                const jobCount = jobCounts.get(id) || 0;
                if (count !== jobCount && (count > 1 || jobCount > 1)) {
                    hasChanged = true;
                }
            });
            return hasChanged;
        },
        async loadDraftPropertyDetailsObjection() {
            await this.$router.push({
                name: 'rating-valuation-objection-draft',
                params: { ratingValuationId: this.activity.activeObjectionUUID },
            });
        },
        getAddressLine2() {
            const { address } = this.activity.property;
            const components = [
                address.suburb,
                address.town,
                this.activity.property.territorialAuthority.name,
            ];
            return components.filter(value => value).join(', ');
        },
        openQivs(url) {
            openQivsInNewTab(url);
        },
        openRouteInNewTab(location) {
            openUrlInNewTab(this.getRouteUrl(location));
            return false;
        },
        getRouteUrl(location) {
            return this.$router.resolve(location).href;
        },
        isInactiveAssessment(activity){
            return activity.ratingUnit.assessmentStatus === 'I';
        }
    },
};
</script>
<style lang="scss" scoped="true" src="./searchRollMaintenance.scss"></style>

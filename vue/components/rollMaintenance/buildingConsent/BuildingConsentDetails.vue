<template>
    <div class="contentWrapper resultsWrapper">
        <!-- eslint-disable max-len -->
        <property-summary
            :property-id="propertyId"
            :can-navigate="false"
        />
        <!-- HACK ... quick n dirty layout -->
        <div class="resultsInner-wrapper mdl-shadow--3dp">
            <div class="col-container">
                <div class="col-row">
                    <div class="col col-12">
                        <h1 class="title" data-cy="building-consent-title">
                            Building Consent
                        </h1>
                        <validation-header-message
                            :validation-set="saveResult"
                            :show-errors="true"
                        />
                        <div v-if="!hasLoaded">
                            <span class="loadingSpinner loadingSpinnerSearchResults" />
                        </div>
                        <div
                            v-if="exception"
                            class="bAlert bAlert-danger"
                            data-cy="exception-message"
                        >
                            {{ exception }}
                        </div>
                    </div>
                </div>
                <div
                    v-if="hasLoaded"
                    class="col-row"
                >
                    <div class="col col-10">
                        <div class="col-container">
                            <div class="col-row">
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Consent Number</span>
                                        <input
                                            v-model="rollMaintenanceActivity.buildingConsent.consentNumber"
                                            type="text"
                                            class="bc-input--consent-number"
                                            data-cy="consent-number-input"
                                            :class="errorClasses('buildingConsent.consentNumber')"
                                            :readonly="readonly"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.consentNumber"
                                        />
                                    </label>
                                </div>
                                <div class="col col-3">
                                    <label>
                                        <span class="label">Territorial Authority</span>
                                        <input
                                            type="text"
                                            class="bc-input--territorial-authority"
                                            data-cy="territorial-authority-input"
                                            readonly
                                            :value="rollMaintenanceActivity.territorialAuthority.code + ' - ' + rollMaintenanceActivity.territorialAuthority.name"
                                        >
                                    </label>
                                </div>
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Cost</span>
                                        <input
                                            v-model="rollMaintenanceActivity.buildingConsent.cost"
                                            type="number"
                                            class="bc-input--cost"
                                            data-cy="cost-input"
                                            :class="errorClasses('buildingConsent.cost')"
                                            :readonly="readonly"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.cost"
                                        />
                                    </label>
                                </div>
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Floor Area (m<sup>2</sup>)</span>
                                        <input
                                            v-model="rollMaintenanceActivity.buildingConsent.totalFloorArea"
                                            type="number"
                                            class="bc-input--floor-area"
                                            data-cy="floor-area-input"
                                            :class="errorClasses('buildingConsent.totalFloorArea')"
                                            :readonly="readonly"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.totalFloorArea"
                                        />
                                    </label>
                                </div>
                                <div class="col col-3">
                                    <label>
                                        <span class="label">Estimated Date Due</span>
                                        <input
                                            type="text"
                                            class="bc-input--estimated-inspection-ready-date"
                                            data-cy="estimated-inspection-ready-date-input"
                                            :value="rollMaintenanceActivity.estimatedInspectionReadyDate | date"
                                            :class="errorClasses('estimatedInspectionReadyDate')"
                                            :readonly="readonly"
                                            @change="setEstimatedInspectionReadyDate"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="estimatedInspectionReadyDate"
                                            data-cy="estimated-inspection-ready-date-validation-message"
                                        />
                                    </label>
                                </div>
                            </div>
                            <div class="col-row">
                                <div class="col col-7">
                                    <label class="bc-input--street-number">
                                        <span class="label">Location</span>
                                        <input
                                            v-model="rollMaintenanceActivity.buildingConsent.streetNumber"
                                            type="text"
                                            data-cy="street-number-input"
                                            :class="errorClasses('buildingConsent.streetNumber')"
                                            :readonly="readonly"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.streetNumber"
                                        />
                                    </label>
                                    <label class="bc-input--street-name">
                                        <input
                                            v-model="rollMaintenanceActivity.buildingConsent.streetName"
                                            type="text"
                                            data-cy="street-name-input"
                                            :class="errorClasses('buildingConsent.streetName')"
                                            :readonly="readonly"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.streetName"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Issue Date</span>
                                        <input
                                            type="text"
                                            class="bc-input--consent-date"
                                            data-cy="consent-date-input"
                                            :value="rollMaintenanceActivity.buildingConsent.consentDate | date"
                                            :class="errorClasses('buildingConsent.consentDate')"
                                            :readonly="readonly"
                                            @change="setConsentDate"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.consentDate"
                                        />
                                    </label>
                                </div>
                                <div class="col col-1">
                                    <label>
                                        <span class="label">Date Entered</span>
                                        <input
                                            type="text"
                                            class="bc-input--entered-date"
                                            data-cy="entered-date-input"
                                            readonly
                                            :value="rollMaintenanceActivity.createdDateTime | date"
                                        >
                                    </label>
                                </div>
                                <div class="col col-3">
                                    <label>
                                        <span class="label">Valuer</span>
                                        <input
                                            type="text"
                                            data-cy="valuer-input"
                                            readonly
                                            :value="rollMaintenanceActivity.valuer && rollMaintenanceActivity.valuer.name"
                                        >
                                    </label>
                                </div>
                            </div>
                            <div class="col-row">
                                <div class="col col-7">
                                    <label>
                                        <span class="label">Owner or Consent Applicant</span>
                                        <input
                                            v-model="rollMaintenanceActivity.buildingConsent.applicant"
                                            type="text"
                                            class="bc-input--applicant"
                                            data-cy="applicant-input"
                                            :class="errorClasses('buildingConsent.applicant')"
                                            :readonly="readonly"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.applicant"
                                        />
                                    </label>
                                </div>
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Construction Completion Date</span>
                                        <input
                                            type="text"
                                            class="bc-input--construction-completion-date"
                                            data-cy="construction-completion-date"
                                            :value="rollMaintenanceActivity.buildingConsent.constructionCompletionDate | date"
                                            :class="errorClasses('buildingConsent.constructionCompletionDate')"
                                            :readonly="readonly"
                                            @change="setConstructionCompletionDate"
                                        >
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.constructionCompletionDate"
                                        />
                                    </label>
                                </div>
                                <div class="col col-3">
                                    <label>
                                        <span class="label">Date Consent Actioned</span>
                                        <input
                                            type="text"
                                            class="bc-input--consent-actioned"
                                            data-cy="consent-actioned-input"
                                            readonly
                                            :value="rollMaintenanceActivity.rollUpdatedDate | date"
                                        >
                                    </label>
                                </div>
                            </div>
                            <div class="col-row">
                                <div class="col col-7">
                                    <label>
                                        <span class="label">Consent Description</span>
                                        <textarea
                                            v-model="rollMaintenanceActivity.buildingConsent.description"
                                            class="bc-input--description"
                                            data-cy="consent-description-input"
                                            :class="errorClasses('buildingConsent.description')"
                                            :readonly="readonly"
                                        />
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.description"
                                        />
                                    </label>
                                </div>
                                <div class="col col-2">
                                    <label>
                                        <span class="label">Inspection Status</span>
                                        <classification-dropdown
                                            id="inspectionState"
                                            category="InspectionState"
                                            data-cy="inspection-state-dropdown"
                                            :value="rollMaintenanceActivity.inspectionState"
                                            hide-codes
                                            :disabled="readonly"
                                            @input="updateInspectionState"
                                        />
                                    </label>
                                </div>
                                <div class="col col-3">
                                    <label>
                                        <span class="label">Nature Of Works</span>
                                        <classification-dropdown
                                            id="natureOfWorks"
                                            category="NatureOfWorks"
                                            data-cy="nature-of-works-dropdown"
                                            :value="rollMaintenanceActivity.buildingConsent.natureOfWorks"
                                            hide-codes
                                            :class="errorClasses('buildingConsent.natureOfWorks')"
                                            :disabled="readonly"
                                            @input="updateNatureOfWorks"
                                        />
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="buildingConsent.natureOfWorks"
                                        />
                                    </label>
                                </div>
                            </div>
                            <div class="col-row">
                                <div class="col col-12">
                                    <label>
                                        <span class="label">Notes</span>
                                        <textarea
                                            v-model="rollMaintenanceActivity.notes"
                                            class="bc-input--notes"
                                            data-cy="notes-input"
                                            :class="errorClasses('notes')"
                                            :readonly="readonly"
                                        />
                                        <validation-message
                                            :validation-set="saveResult"
                                            field="notes"
                                        />
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col col-2 status-panel" data-cy="status-panel">
                        <h2 data-cy="status-panel-header">{{ rollMaintenanceActivity.status.description }}</h2>
                        <ul class="status-list" data-cy="status-list-ul">
                            <li
                                v-if="rollMaintenanceActivity.needsInspection"
                                class="md-qivs danger"
                                data-cy="status-list-item-needs-inspection"
                            >
                                <label data-cy="status-list-label-needs-inspection">Needs Inspection</label>
                            </li>
                            <li
                                v-if="rollMaintenanceActivity.needsMoreInformation"
                                class="md-qivs danger"
                                data-cy="status-list-item-needs-more-information"
                            >
                                <label data-cy="status-list-label-needs-more-information">Notes for Valuer</label>
                            </li>
                            <li
                                v-if="!this.planStatusValue"
                                class="md-qivs danger"
                                data-cy="status-list-item-plans-unknown"
                            >
                                <label data-cy="status-list-label-plans-unknown">Plans Unknown</label>
                            </li>
                            <li
                                v-if="this.planStatusValue === this.options.plansNeeded"
                                class="md-qivs danger"
                                data-cy="status-list-item-plans-needed"
                            >
                                <label data-cy="status-list-label-plans-needed">{{this.planStatusValue}}</label>
                            </li>
                            <li
                                v-if="this.planStatusValue === this.options.plansAvailable"
                                class="md-qivs success"
                                data-cy="status-list-item-plans-available"
                            >
                                <label data-cy="status-list-label-plans-available">{{this.planStatusValue}}</label>
                            </li>
                            <li
                                v-if="this.planStatusValue
                                && this.planStatusValue !== this.options.plansNeeded
                                && this.planStatusValue !== this.options.plansAvailable"
                                class="md-qivs info"
                                data-cy="status-list-item-plans-requested-with-ta"
                            >
                                <label data-cy="status-list-label-plans-requested-with-ta">{{this.planStatusValue}}</label>
                            </li>
                            <li
                                v-if="rollMaintenanceActivity.setupComplete"
                                class="md-qivs success"
                                data-cy="status-list-item-setup-complete"
                            >
                                <label data-cy="status-list-label-setup-complete">Setup Complete</label>
                            </li>
                            <li
                                v-if="!rollMaintenanceActivity.buildingConsent.constructionComplete"
                                class="md-qivs info"
                                data-cy="status-list-item-construction-in-progress"
                            >
                                <label data-cy="status-list-label-construction-in-progress">Construction In Progress</label>
                            </li>
                            <li
                                v-if="rollMaintenanceActivity.buildingConsent.constructionComplete"
                                class="md-qivs success"
                                data-cy="status-list-item-construction-complete"
                            >
                                <label data-cy="status-list-label-construction-complete">Construction Complete</label>
                            </li>
                        </ul>
                        <h3>
                            <span class="label">Documentation</span>
                        </h3>
                        <label>
                            <span class="label">Plan Status</span>
                            <multiselect
                                v-model="planStatusValue"
                                :options="Object.values(options)"
                                :close-on-select="true"
                                select-label="⏎ select"
                                deselect-label="⏎ remove"
                                placeholder=""
                                data-cy="plan-status-dropdown"
                                @input="updatePlanStatusState"/>
                        </label>
                        <label class="status-panel--yes-no">
                            <input
                                v-model="rollMaintenanceActivity.needsMoreInformation"
                                type="checkbox"
                                :disabled="readonly"
                                data-cy="needs-more-information-checkbox"
                            >
                            <span>Notes for Valuer</span>
                        </label>
                        <h3>
                            <span class="label">Inspection</span>
                        </h3>
                        <label class="status-panel--yes-no">
                            <input
                                v-model="rollMaintenanceActivity.needsInspection"
                                type="checkbox"
                                :disabled="readonly"
                                data-cy="needs-inspection-checkbox"
                            >
                            <span>Needs Inspection</span>
                        </label>
                        <h3>
                            <span class="label">Construction</span>
                        </h3>
                        <label class="status-panel--yes-no">
                            <input
                                v-model="rollMaintenanceActivity.buildingConsent.constructionComplete"
                                type="checkbox"
                                :disabled="readonly"
                                data-cy="construction-complete-checkbox"
                                @click.stop="toggleConstructionComplete"
                            >
                            <span>Construction Complete</span>
                        </label>
                        <label class="status-panel--yes-no">
                            <input
                                v-model="rollMaintenanceActivity.buildingConsent.complianceCertificateIssued"
                                type="checkbox"
                                :disabled="readonly"
                                data-cy="compliance-certificate-issued-checkbox"
                                @click.stop="toggleComplianceCertificateIssued"
                            >
                            <span>Code of Compliance Issued</span>
                        </label>
                        <h3>
                            <span class="label">Valuation</span>
                        </h3>
                        <label>
                            <span
                                v-if="rollMaintenanceActivity.noAddedValue"
                                class="material-icons icons-check status-panel--yes-no"
                            >done</span>
                            <span
                                v-else
                                class="material-icons icons-cancel status-panel--yes-no"
                            >clear</span>
                            <span>No Added Value</span>
                        </label>
                        <h3>
                            <span class="label">Property Activity</span>
                        </h3>
                        <label>
                            <span
                                v-if="rollMaintenanceActivity.property.hasActiveObjection"
                                class="material-icons icons-check status-panel--yes-no"
                            >done</span>
                            <span
                                v-else
                                class="material-icons icons-cancel status-panel--yes-no"
                            >clear</span>
                            <span>Objection In Progress</span>
                        </label>
                        <label>
                            <span
                                v-if="rollMaintenanceActivity.property.hasActiveSubdivision"
                                class="material-icons icons-check status-panel--yes-no"
                            >done</span>
                            <span
                                v-else
                                class="material-icons icons-cancel status-panel--yes-no"
                            >clear</span>
                            <span>Subdivision In Progress</span>
                        </label>
                        <label>
                            <span
                                v-if="rollMaintenanceActivity.property.hasActiveConsent"
                                class="material-icons icons-check status-panel--yes-no"
                            >done</span>
                            <span
                                v-else
                                class="material-icons icons-cancel status-panel--yes-no"
                            >clear</span>
                            <span>Consent In Progress</span>
                        </label>
                    </div>
                </div>
                <div
                    v-if="hasLoaded"
                    class="col-row"
                >
                    <div class="col col-12">
                        <div class="lefty">
                            <button
                                v-if="rollMaintenanceActivity.status && rollMaintenanceActivity.status.code =='NEW'"
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                :disabled="saving"
                                data-cy="setup-complete-button"
                                @click="setupComplete"
                            >
                                Setup Complete
                            </button>
                        </div>
                        <div class="righty">
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                data-cy="cancel-button"
                                @click="cancel"
                            >
                                Cancel
                            </button>
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                :disabled="saving || readonly"
                                data-cy="save-button"
                                @click="saveState(false)"
                            >
                                Save
                            </button>
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                :disabled="saving || readonly"
                                data-cy="save-and-close-button"
                                @click="saveState(true)"
                            >
                                Save &amp; Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <alert-modal
            v-show="validationModalIsOpen"
            warning
            @close="closeValidationModal"
        >
            <h1>Date fields are not valid</h1>
            <p>There are date fields that are invalid.</p>
            <p>Check all date fields are filled in correctly with <code>DD/MM/YYYY</code>.</p>
        </alert-modal>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import moment from 'moment';
import commonUtils from '../../../utils/CommonUtils';
import Multiselect from 'vue-multiselect';

export default {
    components: {
        'property-summary': () => import('../../property/PropertySummary.vue'),
        'classification-dropdown': () => import('../../common/form/ClassificationDropdown.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../common/form/ValidationMessage.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "ValidationHeaderMessage" */ '../../common/form/ValidationHeaderMessage.vue'),
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../../common/modal/AlertModal.vue'),
        Multiselect,
    },
    mixins: [commonUtils],
    data() {
        return {
            validationModalIsOpen: false,
            planStatusValue: '',
            options: {
                'plansAvailable': 'Plans Drawn',
                'plansNeeded':'Plans Required',
                'plansUnavailable':'Plans Unavailable',
                'plansNotRequired': 'Plans Not Required',
                'plansRequestedWithTa': 'Plans Requested with TA'
            },
        };
    },
    computed: {
        ...mapState('rollMaintenanceActivity', [
            'loading',
            'rollMaintenanceActivity', // TODO: Stop using v-model and writing directly to a Vuex store.
            'saveResult',
            'saving',
            'exception',
        ]),
        ...mapGetters('rollMaintenanceActivity', [
            'areRollMaintenanceActivityDatesValid',
        ]),
        hasLoaded() {
            /* Is loaded if vuex is not loading and the right activity has been loaded */
            return (
                !this.loading
                && this.rollMaintenanceActivity
                && this.$route.params.id === this.rollMaintenanceActivity.id
            );
        },
        errorMessage() {
            let message;
            if (this.error) {
                if (!this.rollMaintenanceActivity) {
                    message = `Unable to fetch roll maintenance activity ${this.$route.params.id}. ${this.error.message}`;
                }
            }
            return message;
        },
        propertyId() {
            if (this.hasLoaded) return this.rollMaintenanceActivity.property.id;

            return null;
        },
        errors() { return (this.saveResult && this.saveResult.errors) || []; },
        readonly() {
            const readOnlyCode = ['DONE', 'CANCELED'];
            return this.rollMaintenanceActivity && readOnlyCode.includes(this.rollMaintenanceActivity.status.code.toUpperCase());
        },
    },
    watch: {
        rollMaintenanceActivity(){
            if(this.planStatusValue === ''){
                this.planStatusValue=this.getPlanStatus();
            }
        }
    },
    mounted() {
        const rollMaintenanceActivityId = this.$route.params.id;
        this.$store.dispatch('rollMaintenanceActivity/getActivity', rollMaintenanceActivityId);
    },
    methods: {

        setEstimatedInspectionReadyDate(event) {
            const theDate = moment(event.target.value, 'DD/MM/YYYY', true);
            this.$store.dispatch('rollMaintenanceActivity/addValidationError', { field: 'estimatedInspectionReadyDate' });
            if (!theDate.isValid()) {
                this.$store.dispatch('rollMaintenanceActivity/addValidationError', {
                    field: 'estimatedInspectionReadyDate',
                    message: 'must be dd/mm/yyyy',
                });
            }
            if (!theDate.isValid() || event.target.value.trim() === '') {
                this.rollMaintenanceActivity.estimatedInspectionReadyDate = event.target.value;
                return;
            }
            this.rollMaintenanceActivity.estimatedInspectionReadyDate = theDate.format('YYYY-MM-DD');
        },
        setConsentDate(event) {
            const theDate = moment(event.target.value, 'DD/MM/YYYY', true);
            this.$store.dispatch('rollMaintenanceActivity/addValidationError', { field: 'buildingConsent.consentDate' });
            if (!theDate.isValid()) {
                this.$store.dispatch('rollMaintenanceActivity/addValidationError', {
                    field: 'buildingConsent.consentDate',
                    message: 'must be dd/mm/yyyy',
                });
            }
            if (!theDate.isValid() || event.target.value.trim() === '') {
                this.rollMaintenanceActivity.buildingConsent.consentDate = event.target.value;
                return;
            }
            this.rollMaintenanceActivity.buildingConsent.consentDate = theDate.format('YYYY-MM-DD');
        },
        setConstructionCompletionDate(event) {
            const theDate = moment(event.target.value, 'DD/MM/YYYY', true);
            this.$store.dispatch('rollMaintenanceActivity/addValidationError', { field: 'buildingConsent.constructionCompletionDate' });
            if (!theDate.isValid()) {
                this.$store.dispatch('rollMaintenanceActivity/addValidationError', {
                    field: 'buildingConsent.constructionCompletionDate',
                    message: 'must be dd/mm/yyyy',
                });
            }
            if (!theDate.isValid() || event.target.value.trim() === '') {
                this.rollMaintenanceActivity.buildingConsent.constructionCompletionDate = event.target.value;
                return;
            }
            this.rollMaintenanceActivity.buildingConsent.constructionCompletionDate = theDate.format('YYYY-MM-DD');
        },

        updatePlanStatusState(event){
            this.resetPlanStatus();
            switch(event){
                case this.options.plansUnavailable:
                    this.rollMaintenanceActivity.buildingConsent.plansUnavailable = true;
                    break;
                case this.options.plansAvailable:
                    this.rollMaintenanceActivity.buildingConsent.plansObtained = true;
                    break;
                case this.options.plansNeeded:
                    this.rollMaintenanceActivity.buildingConsent.plansRequired = true;
                    break;
                case this.options.plansNotRequired:
                    this.rollMaintenanceActivity.buildingConsent.plansNotRequired = true;
                    break;
                case this.options.plansRequestedWithTa:
                    this.rollMaintenanceActivity.buildingConsent.plansRequestedWithTa = true;
                    break;
            }
        },
        updateInspectionState(item) {
            this.rollMaintenanceActivity.inspectionState = item.value;
        },
        updateNatureOfWorks(item) {
            this.rollMaintenanceActivity.buildingConsent.natureOfWorks = item.value;
        },
        getPlanStatus(){
            const bc = this.rollMaintenanceActivity.buildingConsent;
            if(bc.plansNotRequired){
                return this.options.plansNotRequired;
            }
            else if(bc.plansObtained){
                return this.options.plansAvailable;
            }
            else if(bc.plansRequestedWithTa){
                return this.options.plansRequestedWithTa;
            }
            else if(bc.plansRequired){
                return this.options.plansNeeded;
            }
            else if(bc.plansUnavailable){
                return this.options.plansUnavailable;
            }
            return '';
        },
        resetPlanStatus(){
            this.rollMaintenanceActivity.buildingConsent.plansUnavailable = false;
            this.rollMaintenanceActivity.buildingConsent.plansObtained = false;
            this.rollMaintenanceActivity.buildingConsent.plansRequired = false;
            this.rollMaintenanceActivity.buildingConsent.plansNotRequired = false;
            this.rollMaintenanceActivity.buildingConsent.plansRequestedWithTa = false;
        },
        toggleConstructionComplete(event) {
            if (event.target.checked) {
                if(!this.rollMaintenanceActivity.buildingConsent.constructionCompletionDate)
                    this.rollMaintenanceActivity.buildingConsent.constructionCompletionDate = moment().format('YYYY-MM-DD');
            } else {
                this.rollMaintenanceActivity.buildingConsent.constructionCompletionDate = "";
            }
        },
        toggleComplianceCertificateIssued(event) {
            if (event.target.checked) {
                this.rollMaintenanceActivity.buildingConsent.constructionComplete = true;
                if(!this.rollMaintenanceActivity.buildingConsent.constructionCompletionDate)
                    this.rollMaintenanceActivity.buildingConsent.constructionCompletionDate = moment().format('YYYY-MM-DD');
            }
        },
        setupComplete() {
            if (!this.validateRollMaintenanceActivity()) return;
            // eslint-disable-next-line no-alert, no-restricted-globals
            if (!confirm('Residential properties should have draft property details completed. Are you sure?')) {
                return;
            }
            this.rollMaintenanceActivity.setupComplete = true;
            this.save();
        },
        saveState(close) {
            if (!this.validateRollMaintenanceActivity()) return;
            // eslint-disable-next-line no-alert, no-restricted-globals
            this.save(close);
        },
        cancel() {
            this.$router.backOrClose();
        },
        validateRollMaintenanceActivity() {
            if (!this.areRollMaintenanceActivityDatesValid) {
                this.openValidationModal();
                return false;
            }
            return true;
        },
        async save(close) {
            try {
                await this.$store.dispatch('rollMaintenanceActivity/saveActivity');
                if (this.saveResult.success) {
                    alert('Saved!'); // eslint-disable-line no-alert
                    if(close) this.$router.backOrClose();
                }
            } catch (err) {
                alert(`Something went wrong. ${err}`); // eslint-disable-line no-alert
            }
        },
        openValidationModal() {
            this.validationModalIsOpen = true;
        },
        closeValidationModal() {
            this.validationModalIsOpen = false;
        },

    },
};
</script>

<!-- TODO This shouldnt be component scoped but be global instead -->
<style lang="scss" scoped src="../rollMaintenance.scss"></style>

<style lang="scss" scoped>
/* Component specific layout */
.bc-input {
    &--street-number {
        width: 19% !important;
        display: inline-block;
    }
    &--street-name {
        width: 80% !important;
        display: inline-block;
    }
    &--description {
        height: 8rem !important;
    }
    &--notes {
        height: 95px;
    }
}

/* TODO Mega hack for side bar buttons and label */
.status-panel {
    h2 {
        text-align: center;
        width: 100%;
        border-radius: .6rem;
        margin-bottom: 4px;
        background-color: #214d90;
        color: #fff;
        font-size: 1.5rem;
        font-weight: bold;
        text-transform: uppercase;
        padding: 6px;
    }

    .label {
        margin-top: 1rem;
    }

    label {
        display:block;
    }

    .status-list{
        min-height: 130px;
    }

    .status-panel--yes-no {
        vertical-align: middle;

        input[type=checkbox] {
            vertical-align: middle;
            height: 2.5em !important;
        }
    }

}
</style>

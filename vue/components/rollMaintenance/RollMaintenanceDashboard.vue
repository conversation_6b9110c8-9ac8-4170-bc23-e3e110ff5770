<template>
    <div class="contentWrapper">
        <toolbar title="Roll Maintenance" />
        <template v-if="isInternalUser || isTAUser || externalObjectionAccess">
            <router-view/>
        </template>
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    components: {
        Toolbar: () => import('@/components/common/Toolbar.vue'),
        SearchRollMaintenance: () => import('./search/SearchRollMaintenance.vue'),
        LinzSearchDashboard: () => import('./linzSearch/LinzSearchDashboard.vue'),
        SalesSearchDashboard: () => import('./salesSearch/SalesSearchDashboard.vue'),
        ObjectionsSearchDashboard: () => import('./objections/ObjectionsSearchDashboard.vue'),
    },
    data() {
        return {
            currentSelectedTab: this.isInternalUser ? 'buildingConsents' : 'objections',
        }
    },
    computed: {
        ...mapState('userData', [
            'isInternalUser',
            'isTAUser',
            'externalObjectionAccess',
        ]),
        ...mapState('linzSearch', [
            'linzFilterByTitles'
        ]),
        tab() {
            return this.$route.query.tab || (this.isInternalUser ? 'consents-search' : 'objections-search');
        },
        qpid(){
            return this.$route.query?.qpid != null ? parseInt(this.$route.query.qpid) : undefined;
        }
    },
    methods: {
        isLinzFilterSet() {
            if(this.linzFilterByTitles !== null) {
                this.currentSelectedTab = 'linzSearch';
            }
        },
        routeBackwardsCompatibility() {
            switch (this.$route.query.tab) {
                case 'buildingConsents':
                    this.$router.push({ name: "consents-search" });
                    break;
                case 'objections':
                    this.$router.push({ name: "objections-search" });
                    break;
                case 'linzSearch':
                    this.$router.push({ name: "linz-search" });
                    break;
                case 'salesSearch':
                    this.$router.push({ name: 'sales-dashboard' });
                    break;
                default:
                    this.$router.push({ name: this.tab });
            }
        }
    },
    mounted: function() {
        this.currentSelectedTab = this.tab;
        this.isLinzFilterSet();

        if (this.$route.name === 'roll-maintenance') {
            this.routeBackwardsCompatibility();
        }
    }
};
</script>
<style src="./rollMaintenance.scss"/>

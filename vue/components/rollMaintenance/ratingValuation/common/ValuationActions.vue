<template>
    <div>
        <ul class="status-list" data-cy="statusList">
            <li
                v-if="!plansObtained
                    && !plansRequired && !plansNotRequired
                    && !plansRequestedWithTa && !plansUnavailable && !plansMixedStatus
                "
                class="md-qivs info"
            >
                <label>Plans Unknown</label>
            </li>
            <li v-if="plansRequired" class="md-qivs danger">
                <label>Plans Required</label>
            </li>
            <li v-if="plansObtained" class="md-qivs success">
                <label>Plans Drawn</label>
            </li>
            <li v-if="plansRequestedWithTa" class="md-qivs danger">
                <label>Plans Requested With TA</label>
            </li>
            <li v-if="plansUnavailable" class="md-qivs danger">
                <label>Plans Unavailable</label>
            </li>
            <li v-if="plansNotRequired" class="md-qivs success">
                <label>Plans Not Required</label>
            </li>
            <li v-if="plansMixedStatus" class="md-qivs danger">
                <label>Plans - Mixed Status</label>
            </li>
            <li v-if="!setupComplete" class="md-qivs danger">
                <label>Setup Incomplete</label>
            </li>
            <li v-if="setupComplete" class="md-qivs success">
                <label>Setup Complete</label>
            </li>
            <li v-if="!constructionComplete" class="md-qivs info">
                <label>Construction In Progress</label>
            </li>
            <li v-if="constructionComplete" class="md-qivs success">
                <label>Construction Complete</label>
            </li>
            <li v-if="!complianceCertificateIssued" class="md-qivs info">
                <label>No Compliance Certificate</label>
            </li>
            <li v-if="complianceCertificateIssued" class="md-qivs success">
                <label>Code of Compliance Issued</label>
            </li>
            <li v-if="needsMoreInformation" class="md-qivs danger">
                <label>Notes for Valuer</label>
            </li>
            <!-- TODO This part is commented out as apparently this will later come back
            <li
                v-if="needsInspection"
                class="md-qivs danger"
            >
                <label>Needs Inspection</label>
            </li>
            -->
        </ul>
        <ul class="action-buttons">
            <li>
                <button
                    class="mdl-button mdl-js-button--raised mdl-button--raised mdl-js-ripple-effect mdl-button--colored-red"
                    :disabled="saving"
                    @click="handleDeleteConsentClick()"
                >
                    Delete Consent Job
                </button>
            </li>
            <li v-if="setupComplete && valuationJobStep === 0">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                    :disabled="saving"
                    @click="handleUpdateDraftClick()"
                >
                    Update Draft
                </button>
            </li>
            <li v-if="canAutoValue">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                    :disabled="saving"
                    @click="handleAutoValueClick()"
                >
                    Auto-Value
                </button>
            </li>
            <li v-if="needsMoreInformation && false">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                    :disabled="saving"
                    @click="doAction('informationProvided')"
                >
                    Info Provided
                </button>
            </li>
            <li>
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                    :disabled="saving"
                    @click="startAction('requireMoreInformation', 'Notes for Valuer')"
                >
                    Notes for Valuer
                </button>
            </li>
            <!-- TODO This part is commented out as apparently this will later come
            <li v-if="needsInspection">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                    :disabled="saving"
                    @click="doAction('inspected')"
                >
                    Inspected
                </button>
            </li>
            <li v-if="!needsInspection">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                    :disabled="saving"
                    @click="startAction('requireInspection', 'Needs Inspection')"
                >
                    Needs Inspection
                </button>
            </li>
            -->
            <li>
                <label>
                    <span class="label notes">
                        Notes
                    </span>
                    <span v-if="!hasNotes">—</span>
                    <p v-for="a in linkedActivities" v-if="a.notes && a.notes.length > 0" style="white-space: pre-wrap;">{{ a.buildingConsent.consentNumber }} - {{ a.notes }}</p>
                </label>
            </li>
        </ul>
        <alert-modal
            v-if="showActionModal"
            warning
        >
            <h3>
                {{ actionTitle }}
            </h3>
            <p>Enter any note below:</p>
            <textarea
                v-model="note"
            />
            <!-- Only allow a valuer to mark as plans required if any of the consents do not already have plans drawn flagged -->
            <!--<label v-if="action == 'requireMoreInformation'" class="plans-required">
                <span>Plans Required?</span>
                <input
                    v-model="requestPlans"
                    type="checkbox"
                    :disabled="plansObtained"
                >
                <span v-if="plansObtained">All plans marked as drawn. If this is incorrect please indicate this on the individual consents.</span>
            </label> -->
            <template #buttons>
                <div class="alertButtons">
                    <button
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="showActionModal = false"
                    >
                        Cancel
                    </button>
                    <button
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="actionCallback"
                    >
                        Confirm
                    </button>
                </div>
            </template>
        </alert-modal>
        <!-- Confirmation Modal -->
        <alert-modal
            v-if="confirmation.visible"
            warning
        >
            <h3>{{ confirmation.title }}</h3>
            <p>{{ confirmation.message }}</p>
            <p><br>{{ confirmation.note }}</p>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="confirmation.cancel"
                    >
                        {{ confirmation.del ? '[NO, RETURN TO CONSENT JOB]' : 'Cancel' }}
                    </button>
                    <button
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="confirmation.confirm"
                    >
                        {{ confirmation.del ? '[YES, DELETE CONSENT JOB]' : 'Confirm' }}
                    </button>
                </div>
            </template>
        </alert-modal>
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    components: {
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../../../common/modal/AlertModal.vue'),
    },
    props: {
        loadOnMount: {
            type: Boolean,
            required: true,
        },
        canAutoValue: {
            type: Boolean,
            default: true,
        },
        valuationJobStep: {
            type: Number,
            default: null,
        },
    },
    data() {
        return {
            note: '',
            action: null,
            actionTitle: null,
            requestPlans: null,
            showActionModal: false,
            confirmation: {
                visible: false,
                title: null,
                message: null,
                note: null,
                confirm: null,
                cancel: null,
                del: false,
            },
        };
    },
    computed: {
        ...mapState('ratingValuation', [
            'loading',
            'ratingValuation',
            'saving',
            'propertyActivities',
        ]),
        ...mapState('rollMaintenanceActivity', { activitySaving: 'saving', activitySaveResult: 'saveResult' }),
        needsInspection() {
            if (!this.propertyActivities) {
                return false;
            }
            // if any linked activities are "needsInspection" then true
            return !!this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.needsInspection == true);
        },
        needsMoreInformation() {
            if (!this.propertyActivities) {
                return false;
            }
            // if any linked activities are "needsInspection" then true
            return !!this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.needsMoreInformation == true);
        },
        constructionComplete() {
            if (!this.propertyActivities) {
                return false;
            }
            // if ALL linked activities are "constructionComplete" then true
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.constructionComplete == false);
        },
        plansRequired() {
            if (!this.propertyActivities) {
                return false;
            }
            // if any linked activities needs plans then true
            return !!this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansRequired == true);
        },
        plansObtained() {
            if (!this.propertyActivities) {
                return false;
            }
            // if ALL linked activities are "plansObtained" then true (ie. nothing is false)
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansObtained == false);
        },
        plansUnavailable() {
            if (!this.propertyActivities) {
                return false;
            }
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansUnavailable == false);
        },
        plansRequestedWithTa() {
            if (!this.propertyActivities) {
                return false;
            }
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansRequestedWithTa == false);
        },
        plansNotRequired() {
            if (!this.propertyActivities) {
                return false;
            }
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansNotRequired == false);
        },
        plansMixedStatus() {
            const plansRequiredYN = !!this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansRequired == true);
            const plansObtained = !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansObtained == false);
            const plansNotRequired = !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansNotRequired == false);
            const plansUnavailable = !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansUnavailable == false);
            const plansRequestedWithTa = !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansRequestedWithTa == false);
            const propertyActivitiesMap = this.propertyActivities.map((activity) => {
                const bc = activity.buildingConsent;
                bc.plansUnknown = !(bc.plansObtained || bc.plansRequiredYN || bc.plansNotRequired || bc.plansUnavailable || bc.plansRequestedWithTa);
                return activity;
            });
            const plansUnknown = !propertyActivitiesMap.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.plansUnknown == false);
            if (!this.propertyActivities || plansRequiredYN) {
                return false;
            }
            return !plansObtained && !plansRequestedWithTa && !plansNotRequired && !plansUnavailable && !plansUnknown;
        },
        setupComplete() {
            if (!this.propertyActivities) {
                return false;
            }
            // if ALL linked activities are "setupComplete" then true (ie. nothing is false)
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.setupComplete == false);
        },
        complianceCertificateIssued() {
            if (!this.propertyActivities) {
                return false;
            }
            // if ALL linked activities have Code then true (ie. nothing is false)
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.buildingConsent.complianceCertificateIssued == false);
        },
        linkedActivities() {
            if (!this.propertyActivities) {
                return null;
            }

            return this.propertyActivities.filter(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id));
        },
        hasNotes() {
            // has notes if at least one linked activity has some notes defined
            if (this.linkedActivities) {
                return this.linkedActivities.some(activity => activity.notes && activity.notes.length > 0);
            }

            return false;
        },
    },
    mounted() {
        if (this.loadOnMount) {
            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        }
    },
    methods: {
        autoValue() {
            this.$store.dispatch('ratingValuation/autoValue');
        },
        async doAction(action) {
            await this.$store.dispatch(`ratingValuation/${action}`);
            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
        async deleteRatingValuation(rollID, objectionId) {
            return this.$store.dispatch('ratingValuation/deleteRatingValuation', { rollMaintenanceActivityId: rollID, objectionId });
        },
        startAction(action, title) {
            this.action = action;
            this.actionTitle = title;
            this.requestPlans = false;
            this.note = '';
            this.showActionModal = true;
        },
        async actionCallback() {
            // hide the modal
            this.showActionModal = false;

            // make request with note + plans required if set
            await this.$store.dispatch(`ratingValuation/${this.action}`, { note: this.note, requestPlans: this.requestPlans });

            // clear the note
            this.note = '';

            // clear plans
            this.requestPlans = false;

            // reload
            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
        // Purpose of update draft is to reflect the changes of property details to the valuation job without navigating around..
        updateDraft() {
            this.$emit('updateValuationJob');
        },
        handleAutoValueClick() {
            const title = 'Auto Value';
            const message = 'This will refresh comparables, calculations and write-up to reflect the current state of the draft property details. Are you sure?';
            const visible = true;
            const confirm = () => {
                this.autoValue();
                this.hideConfirmation();
            };
            const cancel = () => this.hideConfirmation();
            this.confirmation = { visible, title, message, confirm, cancel };
        },
        handleUpdateDraftClick() {
            const title = 'Update Draft';
            const message = 'This will refresh comparables, calculations and write-up to reflect the current state of the draft property details. This will also update the state of the Building Consents linked to this valuation job. Are you sure?';
            const visible = true;
            const confirm = () => {
                this.updateDraft();
                this.hideConfirmation();
            };
            const cancel = () => this.hideConfirmation();
            this.confirmation = { visible, title, message, confirm, cancel };
        },
        async handleDeleteConsentClick() {
            const del = true;
            const title = 'Delete Consent Job';
            const message = 'Warning: This will delete the current consent job including any updated draft property details, are you sure?';
            const note = 'Note: This will not delete the Building Consent, just the Consent Job.';
            const visible = true;

            const confirm = async () => {
                this.hideConfirmation();
                const activityIds = [...this.ratingValuation.rollMaintenanceActivityIds];
                const objections = activityIds.filter(id => id.startsWith('OB'));
                const objectionId = objections.length > 0 ? objections.map(id => parseInt(id.split('-')[1])) : null;
                const res = await this.deleteRatingValuation(activityIds[0], objectionId);
                for (const id of activityIds.splice(1)) {
                    await this.deleteRatingValuation(id, null);
                }
                if (res?.data === true) {
                    this.$router.push('/roll-maintenance');
                }
                else {
                    alert('Failed to delete consent job');
                }
            };

            const cancel = () => this.hideConfirmation();
            this.confirmation = { visible, title, message, note, confirm, cancel, del };
        },
        hideConfirmation() {
            this.confirmation.visible = false;
        },
    },
};
</script>
<style lang="scss" scoped>
.notes {
    border-bottom: solid 1px #0e3a83;
    margin-bottom: 0.5rem;
}

textarea {
    border: solid 1px #d2d2d2;
    height: 10rem;
}

.plans-required {
    margin-bottom: 1rem;
    text-align: right;
}

.plans-required > span {
    vertical-align: top;
    display: inline-block;
    padding-top: 1rem;
}
</style>

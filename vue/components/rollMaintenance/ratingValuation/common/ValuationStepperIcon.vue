<script setup>
import { BadgeGroupOverlay, Badge } from '@/components/ui/badge';
import { StepperIcon } from '@/components/ui/stepper';
import MaterialIcon from '@/components/common/MaterialIcon.vue';
import { computed, toRefs, useAttrs } from 'vue';

const props = defineProps({
    isActive: {
        type: Boolean,
        default: false,
    },
    errors: {
        type: Number,
        default: 0,
    },
    warnings: {
        type: Number,
        default: 0,
    },
});

const {
    isActive,
} = toRefs(props);
const attrs = useAttrs();
const isDisabled = computed(() => attrs.disabled);
const classes = computed(() => {
    const defaults = ['qv-circle']

    if (isActive.value) {
        return ['qv-color-light', 'qv-bg-mediumblue', ...defaults];
    }

    if (isDisabled.value) {
        return ['qv-bg-lightgray', 'qv-border-darkblue', 'qv-border-3', 'qv-color-darkblue', ...defaults];
    }

    return ['qv-color-light', 'qv-bg-darkblue', ...defaults]
});

const icon = computed(() => {
    if (isDisabled.value) {
        return 'lock';
    }

    return isActive.value ? 'star' : 'star_border';
});
</script>

<template>
    <BadgeGroupOverlay>
        <template v-slot:badges>
            <Badge variant="error" v-if="errors > 0" :title="`${errors} errors`">{{ errors }}</Badge>
            <Badge variant="warning" v-if="warnings > 0" :title="`${warnings} warnings`">{{ warnings }}</Badge>
        </template>
        <StepperIcon :class="classes">
            <MaterialIcon :icon="icon" class="qv-text-base" />
        </StepperIcon>
    </BadgeGroupOverlay>
</template>

<style lang="scss" scoped>

</style>

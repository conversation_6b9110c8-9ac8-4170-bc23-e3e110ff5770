<script setup>
import { StepperSeparator, StepperTitle, RouteStepper } from '@/components/ui/stepper';
import ValuationStepperItem from '@/components/rollMaintenance/ratingValuation/common/ValuationStepperItem.vue';
import { ratingValuationRoutes } from '@/components/rollMaintenance/routes';
import { computed } from 'vue';
import { ValidationSet, RatingValuation } from '@quotable-value/validation';

const { STEPS } = RatingValuation

const props = defineProps({
    validationSet: {
        type: ValidationSet,
        required: false,
    },
    propertyValidationSet: {
        type: ValidationSet,
        required: false,
    },
    isSetupComplete: {
        type: Boolean,
        required: false,
        default: false,
    },
});

const {
    propertyDetails,
    comparables,
    worksheet,
    writeUp,
} = ratingValuationRoutes;

const routes = [
    propertyDetails,
    comparables,
    worksheet,
    writeUp,
];

const propertyValidations = computed(() => {
    return props.propertyValidationSet || undefined;
});

const comparableValidations = computed(() => {
    return props.validationSet?.getStep(STEPS.COMPARABLES);
});

const worksheetValidations = computed(() => {
    return props.validationSet?.getStep(STEPS.WORKSHEET);
});

const completionValidations = computed(() => {
    return props.validationSet?.getStep(STEPS.COMPLETION);
});
</script>

<template>
    <RouteStepper :routes="routes" data-cy="consent-stepper">
        <ValuationStepperItem :name="propertyDetails.name" :validations="propertyValidations">
            <StepperTitle>
                Draft Property Details
            </StepperTitle>
        </ValuationStepperItem>
        <StepperSeparator class="qv-bg-darkblue" />
        <ValuationStepperItem :name="comparables.name" :validations="comparableValidations" :disabled="!isSetupComplete">
            <StepperTitle>
                Comparable Properties
            </StepperTitle>
        </ValuationStepperItem>
        <StepperSeparator class="qv-bg-darkblue" />
        <ValuationStepperItem :name="worksheet.name" :validations="worksheetValidations" :disabled="!isSetupComplete">
            <StepperTitle>
                Valuation Worksheet
            </StepperTitle>
        </ValuationStepperItem>
        <StepperSeparator class="qv-bg-darkblue" />
        <ValuationStepperItem :name="writeUp.name" :validations="completionValidations" :disabled="!isSetupComplete">
            <StepperTitle>
                Job Completion
            </StepperTitle>
        </ValuationStepperItem>
    </RouteStepper>
</template>

<script setup>
import { RouteStepperItem } from '@/components/ui/stepper';
import ValuationStepperIcon from '@/components/rollMaintenance/ratingValuation/common/ValuationStepperIcon.vue';
import { computed, useAttrs } from 'vue';
import { createValidationSet, ValidationSet } from '@quotable-value/validation';

const props = defineProps({
    name: {
        type: Number|String,
        required: true,
    },
    validations: {
        type: ValidationSet,
        required: false,
        default: () => createValidationSet()
    }
})

const attrs = useAttrs();
const isDisabled = computed(() => attrs.disabled);
</script>

<template>
    <RouteStepperItem v-bind="props" v-slot="{ isActive }" class="qv-color-darkblue qv-color-mediumblue:hover">
        <ValuationStepperIcon
            :is-active="isActive"
            :disabled="isDisabled"
            :errors="validations.errorCount"
            :warnings="validations.warningCount"
        />
        <slot v-bind="{ isActive }"/>
    </RouteStepperItem>
</template>

<style scoped lang="scss">

</style>

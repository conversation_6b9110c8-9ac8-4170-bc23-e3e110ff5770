<script setup>
import { ref, onMounted, watch } from 'vue';
import AlertModal from '../../../common/modal/AlertModal.vue';

const props = defineProps({
    taCodes: {
        type: Array,
        default: () => [],
    },
    selectedRolls: {
        type: Array,
        default: () => [],
    },
    selectedSaleGroups: {
        type: Array,
        default: () => [],
    },
});
const emit = defineEmits(['setRolls']);
const tas = ref([]);
const isActive = ref(false);

// the root of the tree data
const territorialAuthorities = ref([]);
const loading = ref(true);
const isExpanded = ref(false);
const totalRollCounts = ref(0);

onMounted(async () => {
    loadData();
});

watch(() => props.taCodes, async () => {
    loadData();
});

async function fetchJson(url, options = {}) {
    try {
        const response = await fetch(url, options);
        return await response.json();
    }
    catch (error) {
        throw error;
    }
}

async function loadData() {
    isActive.value = props.taCodes && props.taCodes.length > 0;
    if (!isActive.value) {
        return;
    }
    tas.value = [];
    territorialAuthorities.value = [];
    try {
        loading.value = true;
        await loadTerritorialAuthorities();
        await loadSalesGroupsAndRolls();
    }
    catch (error) {
        console.error(`Error loading salesgroupsandrolls data: ${error}}`);
    }
    finally {
        loading.value = false;
    }
}


async function loadTerritorialAuthorities() {
    const data = await fetchJson(jsRoutes.controllers.Application.fetchTerritorialAuthorities().url);
    for (const [taId, name] of Object.entries(data)) {
        const id = parseInt(taId, 10);
        if (props.taCodes.includes(id)) {
            const taCode = id < 10 ? `0${id}` : `${id}`;
            tas.value.push(taCode);
            territorialAuthorities.value.push({
                taCode,
                name,
                checked: false,
                salesGroups: [],
            });
        }
    }
}

async function loadSalesGroupsAndRolls() {
    const taCodes = tas.value.join(',');
    const dataJson = await fetchJson(
        jsRoutes.controllers.ApiPicklistController.getSalesGroupsAndRolls(taCodes).url,
    );
    const data = dataJson.result;
    let totalCheckedRollNumbers = 0;
    data.forEach((ta) => {
        const item = territorialAuthorities.value.find(el => el.taCode == ta.taCode);
        if (item) {
            item.salesGroups = ta.salesGroups;
            item.salesGroups.forEach((saleGroup) => {
                saleGroup.checked = props.selectedSaleGroups.includes(saleGroup.saleGroupId);
                saleGroup.rollNumbers = saleGroup.rollNumbers.map(rollNumber => ({
                    rollNumber,
                    checked: props.selectedRolls.includes(rollNumber),
                }));
                if (saleGroup.checked) {
                    syncSaleGroup(true, saleGroup);
                }
                totalCheckedRollNumbers += saleGroup.rollNumbers.filter( roll => roll.checked).length;
            });
            item.checked = item.salesGroups.every(saleGroup => saleGroup.checked);
        } else {
            console.error('error finding ta:', ta.taCode);
        }
    });
    totalRollCounts.value = totalCheckedRollNumbers;
}

function resetCheckboxes(){
    let totalCheckedRollNumbers = 0;
    territorialAuthorities.value.forEach((item) => {
        item.salesGroups.forEach((saleGroup) => {
            saleGroup.checked = props.selectedSaleGroups.includes(saleGroup.saleGroupId);
            saleGroup.rollNumbers.forEach((rollNumber) => {
                rollNumber.checked = props.selectedRolls.includes(rollNumber);
            });
            if (saleGroup.checked) {
                syncSaleGroup(true, saleGroup);
            }
            totalCheckedRollNumbers += saleGroup.rollNumbers.filter( roll => roll.checked).length;
        });
        item.checked = item.salesGroups.every(saleGroup => saleGroup.checked);
    });
    totalRollCounts.value = totalCheckedRollNumbers;
}

function setRolls() {
    const resultSaleGroups = [];
    const resultRolls = [];
    let total = 0;
    territorialAuthorities.value.forEach((ta) => {
        ta.salesGroups.forEach((saleGroup) => {
            if (saleGroup.checked) {
                resultSaleGroups.push(saleGroup);
                total += saleGroup.rollNumbers.length;
            }
            else {
                saleGroup.rollNumbers.forEach((roll) => {
                    if (roll.checked) {
                        resultRolls.push(roll.rollNumber);
                        total += 1;
                    }
                });
            }
        });
    });
    setIsExpanded(false);
    totalRollCounts.value = total;
    const saleGroupIds = resultSaleGroups.map(saleGroup => saleGroup.saleGroupId);

    emit('setRolls', {
        saleGroups: saleGroupIds,
        rolls: resultRolls,
        total,
        saleGroupDetails: resultSaleGroups,
    });
}

function clearRolls() {
    territorialAuthorities.value.forEach((ta) => {
        ta.checked = false;
        ta.salesGroups.forEach((saleGroup) => {
            saleGroup.checked = false;
            saleGroup.rollNumbers.forEach((roll) => {
                roll.checked = false;
            });
        });
    });
    totalRollCounts.value = 0;

    emit('setRolls', {
        saleGroups: [],
        rolls: [],
        total: 0,
        saleGroupDetails: [],

    });
}

function syncTA(checked, ta) {
    ta.salesGroups.forEach((saleGroup) => {
        syncSaleGroup(checked, saleGroup);
    });
}

function syncSaleGroup(checked, saleGroup, ta = null, doSyncUp = false) {
    saleGroup.checked = checked;
    saleGroup.rollNumbers.forEach((roll) => {
        roll.checked = checked;
    });
    if (doSyncUp) {
        syncUp(null, ta);
    }
}

function syncUp(saleGroup, ta) {
    if (saleGroup) {
        saleGroup.checked = saleGroup.rollNumbers.every(roll => roll.checked);
    }
    if (ta) {
        ta.checked = ta.salesGroups.every(sg => sg.checked);
    }
}

function setIsExpanded(isExpandedValue) {
    isExpanded.value = isExpandedValue;
}

//cancel current selections and reset checkboxes based on props
function onClose(){
    setIsExpanded(false);
    resetCheckboxes();
}
</script>

<template>
    <div>
        <alert-modal v-if="isExpanded && isActive" info>
            <div class="header">
                <div class="center mdl-button--colored">
                    <h3>SALES GROUPS AND ROLLS</h3>
                </div>
                <span data-cy="sales-group-form-close" class="righty" @click="onClose"><i class="material-icons md-dark salesGroupForm-close"></i></span>
            </div>
            <div v-if="loading" class="inner-scroll-y salesGroup-wrapper salesGroupForm">
                loading...
            </div>
            <div data-cy="sales-group-form-wrapper" v-if="!loading" class="inner-scroll-y salesGroup-wrapper salesGroupForm">
                <div v-for="ta in territorialAuthorities" class="salesGroup-ta">
                    <div class="advSearch-group noMargin divider">
                        <div class="salesGroup-taname">
                            <span>
                                <input :id="'ta' + ta.taCode" v-model="ta.checked" class="taCheckbox" type="checkbox"
                                       @change="syncTA($event.target.checked, ta)">
                                <label :for="'ta' + ta.taCode"><span>{{ ta.taCode }}</span>{{ ta.name }}</label>
                            </span>
                        </div>
                    </div>
                    <div v-for="saleGroup in ta.salesGroups" class="advSearch-group noMargin">
                        <div class="salesGroup-name">
                            <span>
                                <input :id="'sg' + saleGroup.taCode + saleGroup.salesGroupNumber" v-model="saleGroup.checked" class="sgCheckbox"
                                       type="checkbox"
                                       @change="syncSaleGroup($event.target.checked, saleGroup, ta, true)">
                                <label :for="'sg' + saleGroup.taCode + saleGroup.salesGroupNumber">{{ saleGroup.salesGroupNumber }} -
                                    {{ saleGroup.description }}</label>
                            </span>
                        </div>
                        <fieldset class="rollValues">
                            <span v-for="roll in saleGroup.rollNumbers" class="tag">
                                <input :id="'roll' + saleGroup.taCode + saleGroup.salesGroupNumber + roll.rollNumber" v-model="roll.checked" type="checkbox"
                                       @change="syncUp(saleGroup, ta)">
                                <label :for="'roll' + saleGroup.taCode + saleGroup.salesGroupNumber + roll.rollNumber">{{ roll.rollNumber }}</label>
                            </span>
                        </fieldset>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="buttons">
                    <button data-cy="sales-group-set-rolls-button" class="mdl-button mdl-button--raised mdl-button--colored" @click="setRolls"> SET ROLLS</button>
                    <button data-cy="sales-group-clear-button" class="mdl-button mdl-button--raised mdl-button--colored" @click="clearRolls"> CLEAR</button>
                </div>
            </template>
        </alert-modal>
        <button
            data-cy="sales-groups-and-rolls-button"
            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored qv-rolls-badge-parent btn-width-100"
            :class="{'qv-disabled': !isActive}"
            @click="setIsExpanded(true)">
            <span v-if="totalRollCounts > 0" class="qv-rolls-badge"> {{ totalRollCounts }}</span>
            Sales Groups &amp; ROLLS
        </button>
    </div>
</template>

<style scoped>
.inner-scroll-y {
    overflow-y: auto;
    background: white;
    margin: 5px auto;
}

.header {
    display: flex;
    margin-top: 5px;
}

.buttons {
    margin: 1rem;
    justify-content: center;
    display: flex;
    column-gap: 5rem;
}

.center {
    margin: auto;
}

.btn-width-100 {
    width: 100%;
}
</style>

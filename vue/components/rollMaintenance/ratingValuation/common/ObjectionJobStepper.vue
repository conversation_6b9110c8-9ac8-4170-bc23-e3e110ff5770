<script setup>
import { StepperSeparator, StepperTitle, RouteStepper } from '@/components/ui/stepper';
import ValuationStepperItem from '@/components/rollMaintenance/ratingValuation/common/ValuationStepperItem.vue';
import { objectionRoutes } from '@/components/rollMaintenance/routes';
import { computed } from 'vue';
import { ValidationSet, RatingValuation } from '@quotable-value/validation';

const { STEPS } = RatingValuation

const props = defineProps({
    validations: {
        type: ValidationSet,
        required: false,
    },
    propertyValidations: {
        type: ValidationSet,
        required: false,
    },
    isSetupComplete: {
        type: Boolean,
        required: false,
        default: false,
    },
});

const {
    propertyRoute,
    comparableRoute,
    worksheetRoute,
    writeupRoute
} = objectionRoutes;

const routes = [
    propertyRoute,
    comparableRoute,
    worksheetRoute,
    writeupRoute
];

const propertyValidations = computed(() => {
    return props.propertyValidations || undefined;
});

const comparableValidations = computed(() => {
    return props.validations?.getStep(STEPS.COMPARABLES);
});

const worksheetValidations = computed(() => {
    return props.validations?.getStep(STEPS.WORKSHEET);
});

const completionValidations = computed(() => {
    return props.validations?.getStep(STEPS.COMPLETION);
});
</script>

<template>
    <RouteStepper :routes="routes" data-cy="consent-stepper">
        <ValuationStepperItem :name="propertyRoute.name" :validations="propertyValidations">
            <StepperTitle>
                Draft Property Details
            </StepperTitle>
        </ValuationStepperItem>
        <StepperSeparator class="qv-bg-darkblue" />
        <ValuationStepperItem :name="comparableRoute.name" :validations="comparableValidations" :disabled="!isSetupComplete">
            <StepperTitle>
                Comparable Sales
            </StepperTitle>
        </ValuationStepperItem>
        <StepperSeparator class="qv-bg-darkblue" />
        <ValuationStepperItem :name="worksheetRoute.name" :validations="worksheetValidations" :disabled="!isSetupComplete">
            <StepperTitle>
                Valuation Worksheet
            </StepperTitle>
        </ValuationStepperItem>
        <StepperSeparator class="qv-bg-darkblue" />
        <ValuationStepperItem :name="writeupRoute.name" :validations="completionValidations" :disabled="!isSetupComplete">
            <StepperTitle>
                Job Completion
            </StepperTitle>
        </ValuationStepperItem>
    </RouteStepper>
</template>

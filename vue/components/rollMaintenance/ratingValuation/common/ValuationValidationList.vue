<script setup>
import { <PERSON><PERSON><PERSON><PERSON>le, ValidationAlert, ValidationAlertList, ValidationAlertListItem } from '@/components/ui/alert';
import { ValidationSet } from '@quotable-value/validation';
import { computed } from 'vue';

const props = defineProps({
    validationSet: {
        type: ValidationSet,
        required: true,
    },
    pdValidationSet: {
        type: ValidationSet,
        required: true,
    },
});

const showWarnings = computed(() => props.pdValidationSet.hasWarnings || props.validationSet.hasWarnings);
const showErrors = computed(() => props.pdValidationSet.hasErrors || props.validationSet.hasErrors);
</script>

<template>
    <div>
        <ValidationAlert v-if="showErrors" class="qv-text-base" variant="error">
            <AlertTitle>
                Unable to save until the following {{ validationSet.errorCount + pdValidationSet.errorCount }} issues are resolved:
            </AlertTitle>
            <ValidationAlertList>
                <ValidationAlertListItem v-for="validation in validationSet.errors" :key="`${validation.field.path}-${validation.index}-${validation.message}`" :validation="validation" />
                <ValidationAlertListItem v-for="validation in pdValidationSet.errors" :key="`${validation.field.path}-${validation.index || 0}-${validation.message}`" :validation="validation" />
            </ValidationAlertList>
        </ValidationAlert>
        <ValidationAlert v-if="showWarnings" class="qv-text-base" variant="warning">
            <AlertTitle>
                There are {{ validationSet.warningCount + pdValidationSet.warningCount }} warnings that may need attention:
            </AlertTitle>
            <ValidationAlertList>
                <ValidationAlertListItem v-for="validation in pdValidationSet.warnings" :key="`${validation.field.path}-${validation.index}-${validation.message}`" :validation="validation" />
                <ValidationAlertListItem v-for="validation in validationSet.warnings" :key="`${validation.field.path}-${validation.index}-${validation.message}`" :validation="validation" />
            </ValidationAlertList>
        </ValidationAlert>
    </div>
</template>

<template>
    <div>
        <div class="comparables-section col-container mdl-shadow--3dp">
            <expander class="righty" v-model="expandConsents" />
            <h1 class="title">
                Building Consents
            </h1>
            <current-roll-maintenance-activities
                :load-on-mount="true"
                :can-edit="false"
                v-show="expandConsents"
            />
        </div>
        <div class="comparables-section" ref="selectedComparablesContainer">
            <ValidationContext :validation-set="ratingValuationValidationSet">
            <div class="resultsInner-wrapper mdl-shadow--3dp">
                <div class="col-container">
                    <h1
                        class="title"
                    >
                        Comparable Properties
                    </h1>

                    <div class="col-row">
                        <div class="col property-draft-data">
                            <p class="label">
                                <strong v-if="isMaoriLand" title="Unadjusted CV">Proposed UCV</strong>
                                <strong v-else>Proposed CV</strong>
                            </p>
                            <p>
                                {{ ratingValuation.adoptedValue ? ratingValuation.adoptedValue.capitalValue : null | currency }}
                            </p>
                        </div>
                        <div class="col property-draft-data">
                            <p class="label">
                                <strong v-if="isMaoriLand" title="Unadjusted LV">Proposed ULV</strong>
                                <strong v-else>Proposed LV</strong>
                            </p>
                            <p>
                                {{ ratingValuation.adoptedValue ? ratingValuation.adoptedValue.landValue : null | currency }}
                            </p>
                        </div>
                        <div class="col property-draft-data">
                            <p class="label">
                                <strong v-if="isMaoriLand" title="Unadjusted VI">Proposed UVI</strong>
                                <strong v-else>Proposed VI</strong>
                            </p>
                            <p>
                                {{ ratingValuation.adoptedValue ? ratingValuation.adoptedValue.valueOfImprovements : null | currency }}
                            </p>
                        </div>
                        <div class="col property-draft-data" v-if="propertyDetail">
                            <p class="label">
                                <strong>Land (Ha)</strong>
                            </p>
                            <p>
                                {{propertyDetail.site ? propertyDetail.site.landArea : null }}
                            </p>
                        </div>
                        <div class="col property-draft-data" v-if="propertyDetail">
                            <p class="label">
                                <strong>TFA</strong>
                            </p>
                            <p>
                              {{propertyDetail.summary ? propertyDetail.summary.totalFloorArea : null }}
                            </p>
                        </div>
                        <div class="col property-draft-data" v-if="propertyDetail">
                            <p class="label">
                                <strong>TLA</strong>
                            </p>
                            <p>
                              {{propertyDetail.summary ? propertyDetail.summary.totalLivingArea : null }}
                            </p>
                        </div>
                        <div class="col property-draft-data" v-if="propertyDetail">
                            <p class="label">
                                <strong>UMR</strong>
                            </p>
                            <p>
                                {{propertyDetail.dvrSnapshot ? propertyDetail.dvrSnapshot.numberOfUnderMainRoofGarages : null }}
                            </p>
                        </div>
                        <div class="col property-draft-data" v-if="propertyDetail">
                            <p class="label">
                                <strong>FS</strong>
                            </p>
                            <p>
                              {{propertyDetail.dvrSnapshot ? propertyDetail.dvrSnapshot.numberOfFreestandingGarages : null }}
                            </p>
                        </div>
                        <div class="col property-draft-data" v-if="propertyDetail">
                            <p class="label">
                                <strong>Category</strong>
                            </p>
                            <p>
                              {{propertyDetail.category ? propertyDetail.category.code : null }}
                            </p>
                        </div>
                        <div class="col property-draft-data"/>
                        <div class="col col-2 worksheet-net-rate-container">
                            <label>
                                <span class="label">Worksheet Net Rate</span>
                                <ValidationWrapper :path="fields.WORKING_NET_RATE" :hideWrapper="isTyping">
                                    <number-input
                                        class=""
                                        :value="ratingValuation.workingNetRate"
                                        @focus="isTyping = true"
                                        @blur="($event) => { isTyping = false; updateRatingValuation('workingNetRate', toNumber($event.srcElement.value)); }"
                                        @input="($event) => { isTyping = true; updateRatingValuation('workingNetRate', toNumber($event.srcElement.value)); }"
                                    />
                                </ValidationWrapper>
                            </label>
                        </div>
                        <div data-cy="valuation-comparables-recalculate" class="col col-2 recalculate-button">
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                :disabled="saving || loading"
                                title="Recalculating will cause the worksheet to be recalculated based on the supplied net rate."
                                @click="saveRatingValuation"
                            >
                                Recalculate
                            </button>
                        </div>
                    </div>
                    <div
                        v-if="!loading"
                        class="col-row"
                    >
                        <div class="col col-12">
                            <p>{{ ratingValuation.comparableProperties.length }} properties selected.</p>
                            <table
                                ref="selectedCompHeaderWrapper"
                                class="table"
                            >
                                <tr
                                    ref="selectedCompHeaderRow"
                                >
                                    <th class="comparable-property-list--selected" />
                                    <th
                                        class="colHeader comparable-property-list--address"
                                        :class="{active: sortField === 'ADDRESS'}"
                                    >
                                        <sort-header
                                            label="Address"
                                            column-name="ADDRESS"
                                            :direction="direction"
                                            :active="sortField === 'ADDRESS'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--valRef"
                                        :class="{active: sortField === 'VALUATION_REFERENCE'}"
                                    >
                                        <sort-header
                                            label="Val Ref"
                                            column-name="VALUATION_REFERENCE"
                                            :direction="direction"
                                            :active="sortField === 'VALUATION_REFERENCE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--capitalValue"
                                        :class="{active: sortField === 'CAPITAL_VALUE'}"
                                    >
                                        <sort-header
                                            label="CV"
                                            column-name="CAPITAL_VALUE"
                                            :direction="direction"
                                            :active="sortField === 'CAPITAL_VALUE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--landValue"
                                        :class="{active: sortField === 'LAND_VALUE'}"
                                    >
                                        <sort-header
                                            label="LV"
                                            column-name="LAND_VALUE"
                                            :direction="direction"
                                            :active="sortField === 'LAND_VALUE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--landArea"
                                        :class="{active: sortField === 'LAND_AREA'}"
                                    >
                                        <sort-header
                                            label="Land Area (Ha)"
                                            column-name="LAND_AREA"
                                            :direction="direction"
                                            :active="sortField === 'LAND_AREA'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--totalFloorArea"
                                        :class="{active: sortField === 'TOTAL_FLOOR_AREA'}"
                                    >
                                        <sort-header
                                            label="TFA"
                                            column-name="TOTAL_FLOOR_AREA"
                                            :direction="direction"
                                            :active="sortField === 'TOTAL_FLOOR_AREA'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--totalLivingArea"
                                        :class="{active: sortField === 'TOTAL_LIVING_AREA'}"
                                    >
                                        <sort-header
                                            label="TLA"
                                            column-name="TOTAL_LIVING_AREA"
                                            :direction="direction"
                                            :active="sortField === 'TOTAL_LIVING_AREA'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--umrGarages"
                                        :class="{active: sortField === 'UNDER_MAIN_ROOF_GARAGES'}"
                                    >
                                        <sort-header
                                            label="UMR"
                                            column-name="UNDER_MAIN_ROOF_GARAGES"
                                            :direction="direction"
                                            :active="sortField === 'UNDER_MAIN_ROOF_GARAGES'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--fsGarages"
                                        :class="{active: sortField === 'FREESTANDING_GARAGES'}"
                                    >
                                        <sort-header
                                            label="FS"
                                            column-name="FREESTANDING_GARAGES"
                                            :direction="direction"
                                            :active="sortField === 'FREESTANDING_GARAGES'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--category"
                                        :class="{active: sortField === 'CATEGORY'}"
                                    >
                                        <sort-header
                                            label="Category"
                                            column-name="CATEGORY"
                                            :direction="direction"
                                            :active="sortField === 'CATEGORY'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--netRate"
                                        :class="{active: sortField === 'BUILDING_NET_RATE'}"
                                    >
                                        <sort-header
                                            label="Net Rate"
                                            column-name="BUILDING_NET_RATE"
                                            :direction="direction"
                                            :active="sortField === 'BUILDING_NET_RATE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--distance"
                                        :class="{active: sortField === 'DISTANCE'}"
                                    >
                                        <sort-header
                                            label="Distance"
                                            column-name="DISTANCE"
                                            :direction="direction"
                                            :active="sortField === 'DISTANCE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--comparabilityScore"
                                        :class="{active: sortField === 'COMPARABILITY_SCORE'}"
                                    >
                                        <sort-header
                                            label="Match"
                                            column-name="COMPARABILITY_SCORE"
                                            :direction="direction"
                                            :active="sortField === 'COMPARABILITY_SCORE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                </tr>
                                <comparable-property-row
                                    v-if="selectedComparables.totalResults > 0"
                                    v-for="(result, index) in selectedComparables.resultList"
                                    :key="result.property.id"
                                    :property-search-result="result"
                                    :selected="true"
                                    :index="index"
                                    @state-changed="selectComparableProperty"
                                />
                            </table>
                            <div class="qv-flex-row" style="justify-content: center">
                                <div>
                                    <div class="qv-input-label">Add by QPID</div>
                                    <div class="qv-input-group">
                                        <input v-model="addQpidStr" class="qv-input" type="number" placeholder="QPID">
                                        <button
                                            @click="addComparablePropertyByQpid"
                                            class="mdl-button mdl-button--raised mdl-button--colored"
                                        >
                                            Add
                                        </button>
                                    </div>
                                    <validation-message
                                        :validation-set="addPropertyValidationSet"
                                        field="addQpid"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div
                        v-if="!(selectedComparables.totalResults > 0)"
                        class="no-results"
                    >
                        <p>No comparable properties selected.</p>
                    </div>
                </div>
                <div v-if="loading">
                    <div class="results-loading">
                        <div
                            class="loadingSpinner loadingSpinnerSearchResults"
                            style="display: block"
                        />
                    </div>
                </div>
            </div>
            </ValidationContext>
        </div>
        <!-- Comps search -->
        <div class="comparables-section">
            <div class="resultsInner-wrapper mdl-shadow--3dp">
                <div class="col-container">
                    <div class="qv-flex-row" style="justify-content: space-between">
                        <div>
                            <h1
                                ref="compSearchHeader"
                                class="title"
                            >
                                Add Comparables
                            </h1>
                        </div>
                        <div>
                            <sales-group-and-rolls
                                :ta-codes="taCodes"
                                :selected-sale-groups="get(comparableSearchCriteria, 'saleGroups')"
                                :selected-rolls="get(comparableSearchCriteria, 'rollNumbers')"
                                @setRolls="onSetRolls"
                            />
                        </div>
                    </div>
                    <div class="col-row">
                        <div class="col col-2">
                            <label title="To search for different types of categories enter each separated by a comma, e.g. RC*, RH*">
                                <span class="label">Categories</span>
                                <input
                                    type="text"
                                    :value="includedCategories"
                                    @input="($event) => includedCategories = $event.srcElement.value"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                >
                            </label>
                        </div>
                        <div class="col col-3">
                            <label>
                                <span class="label">Capital Value</span>
                                <number-input
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'capitalValueFrom')"
                                    @input="($event) => updateSearchCriteria('capitalValueFrom', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                to
                                <number-input
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'capitalValueTo')"
                                    @input="($event) => updateSearchCriteria('capitalValueTo', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    field="capitalValueRange"
                                />
                            </label>
                        </div>
                        <div class="col col-3">
                            <label>
                                <span class="label">Land Value</span>
                                <number-input
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'landValueFrom')"
                                    @input="($event) => updateSearchCriteria('landValueFrom', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                to
                                <number-input
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'landValueTo')"
                                    @input="($event) => updateSearchCriteria('landValueTo', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    field="landValueRange"
                                />
                            </label>
                        </div>
                        <div class="col col-3">
                            <label>
                                <span class="label">Total Living Area</span>
                                <number-input
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'totalLivingAreaFrom')"
                                    @input="($event) => updateSearchCriteria('totalLivingAreaFrom', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                to
                                <number-input
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'totalLivingAreaTo')"
                                    @input="($event) => updateSearchCriteria('totalLivingAreaTo', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    field="totalLivingAreaRange"
                                />
                            </label>
                        </div>
                    </div>
                    <div class="col-row">
                        <div class="col col-2">
                            <label>
                                <span class="label">Distance (m)</span>
                                <number-input
                                    :value="get(comparableSearchCriteria, 'distanceCriteria.distanceInMetres')"
                                    @input="($event) => updateSearchCriteria('distanceCriteria.distanceInMetres', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    field="distanceCriteria"
                                />
                            </label>
                        </div>
                        <div class="col col-3">
                            <label>
                                <span class="label">Net Rate</span>
                                <number-input
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'buildingNetRateFrom')"
                                    @input="($event) => updateSearchCriteria('buildingNetRateFrom', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                to
                                <number-input
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'buildingNetRateTo')"
                                    @input="($event) => updateSearchCriteria('buildingNetRateTo', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                />
                                <validation-message
                                    :validation-set="validationSet"
                                    field="buildingNetRateRange"
                                />
                            </label>
                        </div>
                        <div class="col col-3">
                            <label>
                                <span class="label">Effective Year Built</span>
                                <input
                                    type="number"
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'effectiveYearBuiltFrom')"
                                    @input="($event) => updateSearchCriteria('effectiveYearBuiltFrom', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                >
                                to
                                <input
                                    type="number"
                                    class="range-input"
                                    :value="get(comparableSearchCriteria, 'effectiveYearBuiltTo')"
                                    @input="($event) => updateSearchCriteria('effectiveYearBuiltTo', toNumber($event.srcElement.value))"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                >
                                <validation-message
                                    :validation-set="validationSet"
                                    field="effectiveYearBuiltRange"
                                />
                            </label>
                        </div>
                        <div class="col col-1">
                            <label>
                                <span class="label">Land Zone</span>
                                <input
                                    type="text"
                                    :value="get(comparableSearchCriteria, 'landZone')"
                                    @input="($event) => updateSearchCriteria('landZone', ($event.srcElement.value).trim() || null)"
                                    @blur="validateSearchCriteria"
                                    @keyup.enter="searchComparables"
                                >
                            </label>
                        </div>
                        <div class="col col-3 comp-search-control-buttons">
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                :disabled="searching"
                                @click="getDefaultComparableSearchCriteria"
                            >
                                Reset
                            </button>
                            <button
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                :disabled="searching || !validationSet.success"
                                @click="searchComparables"
                            >
                                Search
                            </button>
                        </div>
                    </div>
                    <div class="col-row">
                        <div class="col col-6">
                            <transition name="fade">
                                <div v-show="!searching">
                                    <p v-show="potentialComparables.totalResults > 0">
                                        Showing
                                        {{ comparableSearchCriteria.offset + 1 | numeral }}
                                        to {{ totalResultsVisible + comparableSearchCriteria.offset | numeral }}
                                        of {{ potentialComparables.totalResults | numeral }} comparable properties (excluding those selected).
                                    </p>
                                    <p v-show="potentialComparables.totalResults === 0">
                                        0 comparable properties found (excluding those selected).
                                    </p>
                                </div>
                            </transition>
                        </div>
                    </div>
                    <div
                        v-if="!loading"
                        class="col-row"
                    >
                        <div class="col col-12">
                            <table class="table">
                                <tr
                                    ref="sortHeaderRow"
                                >
                                    <th class="colHeader comparable-property-list--selected" />
                                    <th
                                        class="colHeader comparable-property-list--address"
                                        :class="{active: sortField === 'ADDRESS'}"
                                    >
                                        <sort-header
                                            label="Address"
                                            column-name="ADDRESS"
                                            :direction="direction"
                                            :active="sortField === 'ADDRESS'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--valRef"
                                        :class="{active: sortField === 'VALUATION_REFERENCE'}"
                                    >
                                        <sort-header
                                            label="Val Ref"
                                            column-name="VALUATION_REFERENCE"
                                            :direction="direction"
                                            :active="sortField === 'VALUATION_REFERENCE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--capitalValue"
                                        :class="{active: sortField === 'CAPITAL_VALUE'}"
                                    >
                                        <sort-header
                                            label="CV"
                                            column-name="CAPITAL_VALUE"
                                            :direction="direction"
                                            :active="sortField === 'CAPITAL_VALUE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--landValue"
                                        :class="{active: sortField === 'LAND_VALUE'}"
                                    >
                                        <sort-header
                                            label="LV"
                                            column-name="LAND_VALUE"
                                            :direction="direction"
                                            :active="sortField === 'LAND_VALUE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--landArea"
                                        :class="{active: sortField === 'LAND_AREA'}"
                                    >
                                        <sort-header
                                            label="Land Area (Ha)"
                                            column-name="LAND_AREA"
                                            :direction="direction"
                                            :active="sortField === 'LAND_AREA'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--totalFloorArea"
                                        :class="{active: sortField === 'TOTAL_FLOOR_AREA'}"
                                    >
                                        <sort-header
                                            label="TFA"
                                            column-name="TOTAL_FLOOR_AREA"
                                            :direction="direction"
                                            :active="sortField === 'TOTAL_FLOOR_AREA'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--totalLivingArea"
                                        :class="{active: sortField === 'TOTAL_LIVING_AREA'}"
                                    >
                                        <sort-header
                                            label="TLA"
                                            column-name="TOTAL_LIVING_AREA"
                                            :direction="direction"
                                            :active="sortField === 'TOTAL_LIVING_AREA'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--umrGarages"
                                        :class="{active: sortField === 'UNDER_MAIN_ROOF_GARAGES'}"
                                    >
                                        <sort-header
                                            label="UMR"
                                            column-name="UNDER_MAIN_ROOF_GARAGES"
                                            :direction="direction"
                                            :active="sortField === 'UNDER_MAIN_ROOF_GARAGES'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--fsGarages"
                                        :class="{active: sortField === 'FREESTANDING_GARAGES'}"
                                    >
                                        <sort-header
                                            label="FS"
                                            column-name="FREESTANDING_GARAGES"
                                            :direction="direction"
                                            :active="sortField === 'FREESTANDING_GARAGES'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--category"
                                        :class="{active: sortField === 'CATEGORY'}"
                                    >
                                        <sort-header
                                            label="Category"
                                            column-name="CATEGORY"
                                            :direction="direction"
                                            :active="sortField === 'CATEGORY'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--netRate"
                                        :class="{active: sortField === 'BUILDING_NET_RATE'}"
                                    >
                                        <sort-header
                                            label="Net Rate"
                                            column-name="BUILDING_NET_RATE"
                                            :direction="direction"
                                            :active="sortField === 'BUILDING_NET_RATE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--distance"
                                        :class="{active: sortField === 'DISTANCE'}"
                                    >
                                        <sort-header
                                            label="Distance"
                                            column-name="DISTANCE"
                                            :direction="direction"
                                            :active="sortField === 'DISTANCE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                    <th
                                        class="colHeader comparable-property-list--comparabilityScore"
                                        :class="{active: sortField === 'COMPARABILITY_SCORE'}"
                                    >
                                        <sort-header
                                            label="Match"
                                            column-name="COMPARABILITY_SCORE"
                                            :direction="direction"
                                            :active="sortField === 'COMPARABILITY_SCORE'"
                                            @onchange="sort"
                                        />
                                    </th>
                                </tr>
                                <comparable-property-row
                                    v-if="potentialComparables.totalResults > 0"
                                    :class="{ 'rows-disabled': loadingComparables }"
                                    v-for="result in potentialComparables.resultList"
                                    :key="result.property.id"
                                    :property-search-result="result"
                                    :selected="false"
                                    @state-changed="selectComparableProperty"
                                />
                            </table>
                        </div>
                    </div>
                    <div
                        v-if="!searching && !(potentialComparables.totalResults > 0)"
                        class="no-results"
                    >
                        <p>No comparable properties found.</p>
                        <p>Adjust search criteria and try again.</p>
                    </div>
                </div>
                <paginate
                    v-if="!loading && potentialComparables.totalResults > 0 && potentialComparables.resultList && potentialComparables.resultList.length > 0"
                    v-model="page"
                    :page-count="totalPageCount"
                />
                <div
                    v-if="loading || searching"
                >
                    <div class="results-loading">
                        <div
                            class="loadingSpinner loadingSpinnerSearchResults"
                            style="display: block"
                        />
                    </div>
                </div>
            </div>
        </div>

        <alert-modal
            v-if="showSuccessModal"
            success
            @close="closeSuccessModal"
        >
            <h1>{{ successMessage.heading }}</h1>
            <p>{{ successMessage.message }}</p>
        </alert-modal>
    </div>
</template>

<script>
import { ref } from 'vue';
import { searchProperties } from '@/services/PropertyController';
import set from 'lodash/set';
import get from 'lodash/get';
import { mapState, mapGetters } from 'vuex';
import numeral from 'numeral';
import { RatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';
import { ValidationContext, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

export default {
    components: {
        ValidationWrapper,
        ValidationContext,
        'sort-header': () => import(/* webpackChunkName: "SortHeader" */'../../common/SortHeader.vue'),
        'comparable-property-row': () => import(/* webpackChunkName: "ComparablePropertyRow" */'./comparables/ComparableProperty.vue'),
        'property-summary': () => import(/* webpackChunkName: "PropertySummary" */ '../../property/PropertySummary.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "ValidationHeaderMessage" */ '../../common/form/ValidationHeaderMessage.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../common/form/ValidationMessage.vue'),
        'current-roll-maintenance-activities': () => import(/* webpackChunkName: "ValuationRollMaintenanceActivities" */ './activities/RollMaintenanceActivities.vue'),
        'number-input': () => import(/* webpackChunkName: "NumberInput" */ '../../common/form/NumberInput.vue'),
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../../common/modal/AlertModal.vue'),
        'paginate': () => import(/* webpackChunkName: "Paginate" */ '../../common/paginate/paginate.vue'),
        'valuation-actions': () => import(/* webpackChunkName: "ValuationActions" */ './common/ValuationActions.vue'),
        'expander': () => import(/* webpackChunkName: "Expander" */ '../../common/Expander.vue'),
        'sales-group-and-rolls': () => import(/* webpackChunkName: "SalesGroupsAndRolls" */ './common/SalesGroupsAndRolls.vue'),
    },
    inject: {
        context: {
            from: RatingValuationContext,
        }
    },
    data() {
        return {
            validationSet: {
                success: true,
                errors: [],
            },
            searching: false,
            successMessage: {},
            showSuccessModal: false,
            expandConsents: false,
            addQpidStr: null,
            addPropertyValidationSet: {
                success: true,
                errors: []
            },
            isTyping: false,
        };
    },
    computed: {
        fields() {
            return RatingValuation.FIELDS
        },
        ...mapState('ratingValuation', {
            saving: 'saving',
            loading: 'loading',
            loadingComparables: 'loadingComparables',
            ratingValuation: 'ratingValuation',
            selectedComparables: 'selectedComparables',
            potentialComparables: 'potentialComparables',
            comparableSearchCriteria: 'comparableSearchCriteria',
            exception: 'exception',
            ratingValuationValidationSet: 'validationSet',
            propertyActivities: 'propertyActivities',
        }),
        ...mapGetters('ratingValuation', [
            'totalPageCount',
            'currentPage',
        ]),
        ...mapState('property', [
            'property',
        ]),
        ...mapState('propertyDraft', [
            'propertyDetail'
        ]),
        ...mapState({
            taCodes: state => state.property?.property?.territorialAuthority?.code ? [state.property.property.territorialAuthority.code] : [],
        }),
        totalResultsVisible() {
            if (!this.potentialComparables || !this.potentialComparables.resultList) return 0;
            return this.potentialComparables.resultList.length;
        },
        sortField() {
            return this.comparableSearchCriteria.sort.length > 1 ? 'ADDRESS' : this.comparableSearchCriteria.sort[0];
        },
        direction() {
            return this.comparableSearchCriteria.order;
        },
        hasLoaded() {
            return (
                /* Not loading the valuation ... */
                !this.loading
                /* ... and has a valuation that is the valuation we want ... */
                && this.ratingValuation && this.$route.params.id === this.ratingValuation.id
            );
        },
        isMaoriLand() {
            return this.propertyDetail?.landUse.isMaoriLand
        },
        propertyId() {
            if (this.hasLoaded) return this.ratingValuation.ratingUnit.propertyId;

            return null;
        },
        includedCategories: {
            get() {
                if (!Array.isArray(this.comparableSearchCriteria.includedCategories)) return '';
                const includedCategories = [...this.comparableSearchCriteria.includedCategories];
                return includedCategories.join(', ');
            },
            set(newValue) {
                if (!newValue.length) {
                    this.updateSearchCriteria('includedCategories', []);
                    return;
                }
                if (newValue.endsWith(',')) {
                    return;
                }
                const includedCategories = newValue.split(',').map(cat => cat.trim()).filter(cat => cat.length > 0);
                this.updateSearchCriteria('includedCategories', includedCategories);
            },
        },
        page: {
            get() {
                return this.currentPage;
            },
            set(value) {
                this.changePage(value);
            },
        },
        // Used for remembering comparable search criteria for a valuation job.
        storedValuationComparableSearchCriteria: {
            get() {
                const storedComparableSearchCriteria = JSON.parse(localStorage.getItem('valuationComparableSearchCriteria')) || {};
                return storedComparableSearchCriteria[this.ratingValuation.id];
            },
            set(value) {
                const storedComparableSearchCriteria = JSON.parse(localStorage.getItem('valuationComparableSearchCriteria')) || {};
                storedComparableSearchCriteria[this.ratingValuation.id] = value;
                localStorage.setItem('valuationComparableSearchCriteria', JSON.stringify(storedComparableSearchCriteria));
            },
        },
        isSetupComplete() {
            if (!this.propertyActivities) {
                return false;
            }
            // if ALL linked activities are "setupComplete" then true (ie. nothing is false)
            // If one of the linked roll maintenance activities has setupComplete === false, then setupComplete should be false.
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.setupComplete === false);
        },
        disabledSteps() {
            if (this.isSetupComplete === false) {
                return [1, 2, 3];
            }
            return [];
        },
    },
    async mounted() {
        // If there is a stored valuation comparable search criteria then use that
        if (this.storedValuationComparableSearchCriteria != null) {
            this.$store.commit('ratingValuation/setComparableSearchCriteria', this.storedValuationComparableSearchCriteria);
        } else {
            // Otherwise get the default comparable search criteria
            await this.$store.dispatch('ratingValuation/getDefaultComparableSearchCriteria');
        }
        window.addEventListener('scroll', this.stickSortHeader);
    },
    destroyed() {
        window.removeEventListener('scroll', this.stickSortHeader);
    },
    methods: {
        sort(data) {
            this.$store.dispatch('ratingValuation/changeSort', data);
        },
        changePage(data) {
            this.$store.dispatch('ratingValuation/changePage', data);
        },
        onSetRolls({saleGroups, rolls, saleGroupDetails} = {}) {
            this.updateSearchCriteria('saleGroups', saleGroups);
            this.updateSearchCriteria('rollNumbers', rolls);
            this.updateSearchCriteria('saleGroupDetails', saleGroupDetails);
        },
        selectComparableProperty(event) {
            this.$store.dispatch('ratingValuation/selectComparableProperty', event);
        },
        async searchComparables() {
            if (!this.validateSearchCriteria() || this.loading) return;
            this.searching = true;
            this.page = 1;
            await this.$store.dispatch('ratingValuation/loadComparables');
            this.searching = false;
            this.$nextTick(() => {
                const element = !this.exception
                    ? this.$refs.compSearchHeader
                    : this.$refs.exceptionMessage;
                window.scrollTo({ top: element.offsetTop, left: 0, behavior: 'smooth' });
            });
            this.storedValuationComparableSearchCriteria = this.comparableSearchCriteria;
        },
        updateRatingValuation(path, value) {
            const ratingValuation = { ...this.ratingValuation };
            set(ratingValuation, path, value);
            this.$store.commit('ratingValuation/setRatingValuation', ratingValuation);
            this.onChanged();
        },
        onChanged() {
            this.context.validate();
        },
        async saveRatingValuation() {
            try {
                await this.context.save();

                if (this.ratingValuationValidationSet.hasErrors) {
                    return;
                }

                this.showSuccess({
                    heading: 'Worksheet Updated',
                    message: 'Net Rate has been applied to the worksheet.',
                });
            } catch (exception) {
                console.error(exception.message);
            }
        },
        updateSearchCriteria(path, value) {
            const comparableSearchCriteria = { ...this.comparableSearchCriteria };
            set(comparableSearchCriteria, path, value);
            this.$store.commit(
                'ratingValuation/setComparableSearchCriteria',
                comparableSearchCriteria,
            );
        },
        get(src, path, valueIfNull) {
            return get(src, path, valueIfNull);
        },
        isValidRange(from, to) {
            if ((from && to) && (from > to)) {
                return false;
            }
            return true;
        },
        toNumber(text) {
            const n = numeral(text).value();
            if (Number.isNaN(n)) return null;
            return n;
        },
        async getDefaultComparableSearchCriteria() {
            await this.$store.dispatch('ratingValuation/getDefaultComparableSearchCriteria');
            this.validationSet = { success: true, errors: [] };
        },
        validateSearchCriteria() {
            if (!this.comparableSearchCriteria) return false;

            const errors = [];
            const criteria = this.comparableSearchCriteria;
            if (
                criteria.distanceCriteria
                && criteria.distanceCriteria.distanceInMetres !== null
                && criteria.distanceCriteria.distanceInMetres < 1
            ) {
                errors.push({
                    field: 'distanceCriteria',
                    message: 'Distance (m) must be greater than 0',
                });
            }
            if (
                criteria.distanceCriteria
                && !criteria.distanceCriteria.coordinates
                && criteria.distanceCriteria.distanceInMetres !== null
            ) {
                errors.push({
                    field: 'distanceCriteria',
                    message: 'Subject property co-ordinates are not known',
                });
            }
            if (
                criteria.capitalValueFrom != null
                && criteria.capitalValueTo != null
                && criteria.capitalValueFrom > criteria.capitalValueTo
            ) {
                errors.push({
                    field: 'capitalValueRange',
                    message: 'Capital Value To must be greater than or equal to Capital Value From',
                });
            }
            if (
                criteria.landValueFrom != null
                && criteria.landValueTo != null
                && criteria.landValueFrom > criteria.landValueTo
            ) {
                errors.push({
                    field: 'landValueRange',
                    message: 'Land Value To must be greater than or equal to Land Value From',
                });
            }
            if (
                criteria.totalLivingAreaFrom != null
                && criteria.totalLivingAreaTo != null
                && criteria.totalLivingAreaFrom > criteria.totalLivingAreaTo
            ) {
                errors.push({
                    field: 'totalLivingAreaRange',
                    message: 'Total Living Area To must be greater than or equal to Total Living Area From',
                });
            }
            if (
                criteria.buildingNetRateFrom != null
                && criteria.buildingNetRateTo != null
                && criteria.buildingNetRateFrom > criteria.buildingNetRateTo
            ) {
                errors.push({
                    field: 'buildingNetRateRange',
                    message: 'Net Rate To must be greater than or equal to Net Rate From',
                });
            }
            if (
                criteria.effectiveYearBuiltFrom != null

                && criteria.effectiveYearBuiltTo != null
                && criteria.effectiveYearBuiltFrom > criteria.effectiveYearBuiltTo
            ) {
                errors.push({
                    field: 'effectiveYearBuiltRange',
                    message: 'Effective Year Built To must be greater than or equal to Effective Year Built From',
                });
            }

            const validationSet = { errors, success: !errors.length };
            this.validationSet = validationSet;
            return validationSet.success;
        },
        stickSortHeader() {
            const { sortHeaderRow, selectedCompHeaderRow } = this.$refs;
            const { sortHeaderWrapper, selectedCompHeaderWrapper } = this.$refs;
            const { selectedComparablesContainer } = this.$refs;

            if (!sortHeaderRow || !sortHeaderWrapper || !selectedCompHeaderRow || !selectedCompHeaderWrapper || !selectedComparablesContainer) {
                return;
            }
            // Stick
            if (selectedCompHeaderRow.getBoundingClientRect().top < 1) {
                selectedCompHeaderRow.style.position = 'fixed';
                selectedCompHeaderRow.style.top = '0';
                selectedCompHeaderRow.style['z-index'] = 1;
                selectedCompHeaderRow.style['max-width'] = `${sortHeaderWrapper.getBoundingClientRect().width}px`;
            }
            // Unstick
            if (selectedCompHeaderWrapper.getBoundingClientRect().top >= 1 || selectedComparablesContainer.getBoundingClientRect().bottom < 1) {
                selectedCompHeaderRow.style.position = 'static';
                selectedCompHeaderRow.style.top = '';
            }

            // Stick
            if (sortHeaderRow.getBoundingClientRect().top < 1) {
                sortHeaderRow.style.position = 'fixed';
                sortHeaderRow.style.top = '0';
                sortHeaderRow.style['z-index'] = 1;
                sortHeaderRow.style['max-width'] = `${sortHeaderWrapper.getBoundingClientRect().width}px`;
            }

            // Unstick
            if (sortHeaderWrapper.getBoundingClientRect().top >= 1) {
                sortHeaderRow.style.position = 'static';
                sortHeaderRow.style.top = '';
            }
        },
        showSuccess(message = {}) {
            this.successMessage.heading = message.heading || 'Saved.';
            this.successMessage.message = message.message || 'Your changes have been saved.';
            this.successMessage.navigateTo = message.navigateTo || null;
            this.showSuccessModal = true;
        },
        closeSuccessModal() {
            this.showSuccessModal = false;
            if (!this.successMessage.navigateTo) return;
            this.$router.push(this.successMessage.navigateTo);
        },
        async addComparablePropertyByQpid() {

            this.resetValidation(); // Reset any previous validations

            if(!this.addQpidStr) {
                this.setAddPropertyValidation('INVALID','Please enter a property QPID');
                return;
            }

            const addQpid = parseInt(this.addQpidStr, 10); // Assuming addQpid is bound to an input model

            if (this.qpidAlreadyAdded(addQpid)) {
                this.setAddPropertyValidation('INVALID', 'Property already selected.');
                return;
            }

            try {

                const payload = {
                    qupid: addQpid
                }

                const propertyData = await searchProperties(payload);

                if (propertyData.totalResults == 0) {
                    this.setAddPropertyValidation('INVALID', 'Please enter a valid QPID. This QPID does NOT exist');
                    return;
                }

                // Check if the property matches the required criteria
                if (!['0', '2', '5'].includes(propertyData.resultList[0].property.apportionment.code)) {
                    this.setAddPropertyValidation('INVALID', 'Property is not a Rating Unit');
                    return;
                }

                if (!['A', 'S'].includes(propertyData.resultList[0].property.status.code)) {
                    this.setAddPropertyValidation('INVALID', 'Property is not an active property');
                    return;
                }

                // Add the QPID to the selected properties
                await this.$store.dispatch('ratingValuation/addPropertyByQpid', propertyData.resultList[0]);
                this.onChanged();

            } catch (error) {
                this.setAddPropertyValidation('ERROR', 'An unexpected error occurred while adding the property');
                console.error('addComparablePropertyByQpid Error:', error);
            }
        },
        setAddPropertyValidation(status, message) {
            this.addPropertyValidationSet = {
                success: false,
                errors: [
                    {
                        status,
                        field: `addQpid`,
                        message,
                    },
               ],
            };
        },
        resetValidation() {
            const defaults = {
                success: true,
                errors: [],
            };
            this.addPropertyValidationSet = { ...defaults };
        },
        qpidAlreadyAdded(qpid) {
                return this.selectedComparables.resultList.some(p => p.property.qupid === qpid);
        },
    }
};
</script>

<style scoped>
    .col-container {
        margin-top: 0;
    }
    .col.property-draft-data{
        margin-right: 0.5em;
    }
    .no-results {
        text-align: center;
        font-size: 1.5em;
        padding: 1em 0;
    }
    .results-loading {
        text-align: center;
        font-size: 3em;
        padding-top: 4em;
    }
    .range-input {
        width: 45% !important;
    }
    .fade-enter-active, .fade-leave-active {
        transition: opacity 0.5s;
    }
    .fade-enter, .fade-leave-to {
        opacity: 0;
    }
    .rows-disabled {
        opacity: .5;
        pointer-events: none;
    }
    .recalculate-button {
        margin-bottom:-4.5em;
        line-height: 4.5;
        text-align: right;
    }
    .comp-search-control-buttons {
        margin-bottom: -5.5em;
        line-height: 5.5;
        text-align: right;
    }
    .worksheet-net-rate-container {
        margin-top: 0;
    }
    .comparables-section {
        margin-bottom: 2rem;
    }
    .proposedValues ul{
        display: inline-grid;
    }
    .proposedValues.error {
        background-color: #fc3d39;
        padding-left: 0.3rem;
    }
</style>
<style lang="scss" src="./comparables/comparableProperties.scss" scoped></style>

<template>
    <table class="table">
        <tr>
            <th class="comparable-property-list--address">
                <sort-header label="Address" />
            </th>
            <th class="comparable-property-list--valRef">
                <sort-header label="Val Ref" />
            </th>
            <th class="comparable-property-list--capitalValue">
                <sort-header label="CV" />
            </th>
            <th class="comparable-property-list--landValue">
                <sort-header label="LV" />
            </th>
            <th class="comparable-property-list--landArea">
                <sort-header label="Land Area (Ha)" />
            </th>
            <th class="comparable-property-list--view">
                <sort-header label="View" />
            </th>
            <th class="comparable-property-list--viewScope">
                <sort-header label="Scope" />
            </th>
            <th class="comparable-property-list--totalFloorArea">
                <sort-header label="TFA" />
            </th>
            <th class="comparable-property-list--totalLivingArea">
                <sort-header label="TLA" />
            </th>
            <th class="comparable-property-list--umrGarages">
                <sort-header label="UMR" />
            </th>
            <th class="comparable-property-list--fsGarages">
                <sort-header label="FS" />
            </th>
            <th class="comparable-property-list--category">
                <sort-header label="Category" />
            </th>
            <th class="comparable-property-list--netRate">
                <sort-header label="Net Rate" />
            </th>
        </tr>
        <view-comparable-property-row
            v-for="comparableProperty in comparables"
            :key="comparableProperty.propertyId"
            :comparable-property="comparableProperty"
        />
    </table>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import numeral from 'numeral';

export default {
    components: {
        'sort-header': () => import(/* webpackChunkName: "SortHeader" */'../../../common/SortHeader.vue'),
        'view-comparable-property-row': () => import(/* webpackChunkName: "ComparablePropertyRow" */'./ComparablePropertyReadOnly.vue'),
    },
    props: {
        comparables: {
            type: Array,
            required: true,
        },
    },
};
</script>

<style lang="scss" src="../../rollMaintenance.scss" scoped />
<style lang="scss" src="./comparableProperties.scss" scoped />

<template>
    <tr
        class="comparable-property-list__row"
        v-bind:class="{'comparable-property-list--maoriLand': comparableProperty.isMaoriLand, 'comparable-property-list--nonMaoriLand': !comparableProperty.isMaoriLand}"
    >
        <td class="comparable-property-list--address">
            <span class="primaryThumb-Wrapper">
                <img
                    class="primaryPhoto_thumb"
                    :src="photoUrl"
                >
            </span>
            <router-link :to="{name: 'property', params: {qpid: qpid || 0}}" target="_blank">
                <div class="fullAddress">
                    <span>{{ comparableProperty.address.streetAddress }}</span>
                    <span>{{ addressLine2 }}</span>
                </div>
            </router-link>
        </td>
        <td class="comparable-property-list--valRef">
            {{ comparableProperty.valuationReference }}
        </td>
        <td class="comparable-property-list--capitalValue">
            {{ comparableProperty.unadjustedCapitalValue | currency }}
        </td>
        <td class="comparable-property-list--landValue">
            {{ comparableProperty.unadjustedLandValue | currency }}
        </td>
        <td class="comparable-property-list--landArea">
            {{ comparableProperty.landArea }}
        </td>
        <td class="comparable-property-list--view">
            {{ comparableProperty.view | description }}
        </td>
        <td class="comparable-property-list--viewScope">
            {{ comparableProperty.viewScope | description }}
        </td>
        <td class="comparable-property-list--totalFloorArea">
            {{ comparableProperty.totalFloorArea }}
        </td>
        <td class="comparable-property-list--totalLivingArea">
            {{ comparableProperty.totalLivingArea }}
        </td>
        <td class="comparable-property-list--umrGarages">
            {{ comparableProperty.underMainRoofGarages }}
        </td>
        <td class="comparable-property-list--fsGarages">
            {{ comparableProperty.freestandingGarages }}
        </td>
        <td class="comparable-property-list--category">
            {{ comparableProperty.category | code }}
        </td>
        <td class="comparable-property-list--netRate">
            {{ comparableProperty.unadjustedBuildingNetRate | currency }}
        </td>
    </tr>
</template>

<script>
import { mapState } from 'vuex';
import { openUrlInNewTab } from '../../../../utils/QivsUtils';

export default {
    props: {
        comparableProperty: {
            type: Object,
            required: true,
        },
    },
    computed: {
        ...mapState('propertyPhotos', [
            'photoUrlMap',
        ]),
        photoUrl() {
            const photo = this.photoUrlMap[this.comparableProperty.propertyId];
            return photo ? photo.smallImageUrl : '';
        },
        addressLine2() {
            const { address } = this.comparableProperty;
            const components = [
                address.suburb,
                address.town,
                this.comparableProperty.territorialAuthority.name,
            ];
            return components.filter(value => value).join(', ');
        },
        qpid() {
            return this.comparableProperty && this.comparableProperty.qpid;
        },
    },
    mounted() {
        this.$store.dispatch('propertyPhotos/getPropertyPhoto', this.comparableProperty.propertyId);
    },
    methods: {
        openProperty(qpid) {
            if (qpid == null) {
                return;
            }
            openUrlInNewTab(`/property/property?qupid=${qpid}`);
        },
    },
};
</script>

<style lang="scss" src="../../rollMaintenance.scss" scoped />
<style lang="scss" src="./comparableProperties.scss" scoped />

<template>
    <ValidationWrapper
        v-bind:class="{selected: selected, 'comparable-property-list--maoriLand': isMaoriLand, 'comparable-property-list--nonMaoriLand': !isMaoriLand}"
        :path="fields.COMPARABLE_PROPERTY"
        :index="index"
        tag="tr">
        <td class="comparable-property-list--selected">
            <input type="checkbox" :checked="selected" :disabled="saving" @change="onStateChange"/>
        </td>
        <td class="comparable-property-list--address">
            <span class="primaryThumb-Wrapper">
                <img class="primaryPhoto_thumb" :src="photoUrl">
            </span>
            <a @click.prevent="openProperty(propertySearchResult.property.qupid)">
                <div class="fullAddress">
                    <span>{{propertySearchResult.property.address.streetAddress}}</span>
                    <span>{{addressLine2}}</span>
                </div>
            </a>
        </td>
        <td class="comparable-property-list--valRef">{{propertySearchResult.property.valuationReference}}</td>
        <td class="comparable-property-list--capitalValue">{{ unadjustedWhenMaoriLandCV | currency }}</td>
        <td class="comparable-property-list--landValue">{{ unadjustedWhenMaoriLandLV | currency }}</td>
        <td class="comparable-property-list--landArea">{{propertySearchResult.property.landUseData.landArea | numeral('0,0.0000')}}</td>
        <td class="comparable-property-list--totalFloorArea">{{propertySearchResult.property.landUseData.totalFloorArea}}</td>
        <td class="comparable-property-list--totalLivingArea">{{propertySearchResult.property.massAppraisalData.totalLivingArea}}</td>
        <td class="comparable-property-list--umrGarages">{{propertySearchResult.property.massAppraisalData.underMainRoofGarages}}</td>
        <td class="comparable-property-list--fsGarages">{{propertySearchResult.property.massAppraisalData.freestandingGarages}}</td>
        <td class="comparable-property-list--category">{{propertySearchResult.property.category.code}}</td>
        <td class="comparable-property-list--netRate">{{propertySearchResult.property.unadjustedBuildingNetRate | currency}}</td>
        <td class="comparable-property-list--distance">{{propertySearchResult.distanceInMetres | distance}}</td>
        <td class="comparable-property-list--comparabilityScore">{{propertySearchResult.comparabilityScore | percentage }}</td>
    </ValidationWrapper>
</template>

<script>
import { mapState } from 'vuex';
import  { openUrlInNewTab } from '../../../../utils/QivsUtils';
import { ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

export default {
    components: { ValidationWrapper },
    props: {
        propertySearchResult: {
            type: Object,
        },
        selected: {
            type: Boolean,
        },
        index: {
            type: Number,
        }
    },
    computed: {
        fields: () => RatingValuation.FIELDS,
        ...mapState('ratingValuation', [
            'saving',
        ]),
        ...mapState('propertyPhotos', [
            'photoUrlMap',
        ]),
        photoUrl: function() {
            const photo = this.photoUrlMap[this.propertySearchResult.property.id];
            return photo ? photo.smallImageUrl : '';
        },
        addressLine2: function() {
            const address = this.propertySearchResult.property.address;
            const components = [address.suburb, address.town, this.propertySearchResult.property.territorialAuthority.name];
            return components.filter(value => value).join(', ');
        },
        isMaoriLand: function() {
            return this.propertySearchResult.property.landUseData?.isMaoriLand === true;
        },
        unadjustedWhenMaoriLandCV: function() {
            if (this.propertySearchResult.property.landUseData?.isMaoriLand === true && this.propertySearchResult.property.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation?.capitalValue) {
                return this.propertySearchResult.property.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation?.capitalValue;
            }
            return this.propertySearchResult.property.currentValuation.capitalValue;
        },
        unadjustedWhenMaoriLandLV: function() {
            if (this.propertySearchResult.property.landUseData?.isMaoriLand === true && this.propertySearchResult.property.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation?.landValue) {
                return this.propertySearchResult.property.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation?.landValue;
            }
            return this.propertySearchResult.property.currentValuation.landValue;
        }
    },
    methods: {
        onStateChange() {
            this.$emit('state-changed', {"propertyId": this.propertySearchResult.property.id, "selected": !this.selected});
        },

        openProperty(qpid) {
            if(qpid == null) {
                return;
            }
            openUrlInNewTab(`/property/property?qupid=${qpid}`);
        }
    },
    mounted() {
        this.$store.dispatch('propertyPhotos/getPropertyPhoto', this.propertySearchResult.property.id);
    }
};
</script>
<style lang="scss" src="./comparableProperties.scss" scoped />

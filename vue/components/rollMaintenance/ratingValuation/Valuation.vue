<script>
import { mapState } from 'vuex';
import commonUtils from '../../../utils/CommonUtils';
import ValuationWorksheetReadOnly from '@/components/rollMaintenance/ratingValuation/ValuationWorksheetReadOnly.vue';

export default {
    components: {
        ValuationWorksheetReadOnly,
        'property-summary': () => import(/* webpackChunkName: "PropertySummary" */ '../../property/PropertySummary.vue'),
        'classification-lookup': () => import(/* webpackChunkName: "ClassificationLookup" */ '../../common/ClassificationLookup.vue'),
        'property-details-read-only': () => import(/* webpackChunkName: "PropertyDetailsReadOnly" */ '../../propertyDetails/PropertyDetailsReadOnly.vue'),
        'valuation-roll-maintenance-activities': () => import(/* webpackChunkName: "ValuationRollMaintenanceActivities" */ './activities/RollMaintenanceActivitiesReadOnly.vue'),
        'view-comparable-properties': () => import(/* webpackChunkName: "ViewComparableProperties" */'./comparables/ComparablePropertiesReadOnly.vue'),
    },
    mixins: [
        commonUtils,
    ],
    computed: {
        ...mapState('userData', [
            'isInternalUser',
        ]),
        ...mapState('propertyDraft', {
            propertyDetail: 'propertyDetail',
            draftLoading: 'loading',
            draftSaving: 'saving',
            draftException: 'exception',
            validationSet: 'validationSet',
            formIsStale: 'formIsStale',
        }),
        ...mapState('ratingValuation', {
            ratingValuation: 'ratingValuation',
            valuationLoading: 'loading',
            valuationException: 'exception',
            valuationActivities: 'valuationActivities',
        }),
        ...mapState('property', {
            property: 'property',
        }),
        qpid() {
            return this.ratingValuation.ratingUnit.qpid;
        },
        loading() {
            return this.draftLoading && this.valuationLoading;
        },
        latestActivityDate() {
            return this.valuationActivities?.filter(activity => activity.activityType.code == 'BC' && activity.status.code !== 'CANCELED')
                .sort(activity => activity.actionedDate)?.pop()?.actionedDate;
        },
        valuationReference() {
            return this.property?.valuationReference;
        },
        valuerName() {
            return this.ratingValuation.valuer?.name;
        }
    },
    watch: {
        $route: (newVal, oldVal) => {
            if (newVal === oldVal) return;
            this.loadConsentRatingValuation();
        },
    },
    created() {
        this.loadConsentRatingValuation();
    },
    methods: {
        async loadConsentRatingValuation() {
            try {
                // HACK Choose how to load the valuation based on whats given
                if (this.$route.params.rollMaintenanceActivityId != null) {
                    await this.$store.dispatch('ratingValuation/getCompleteValuationForActivity', this.$route.params.rollMaintenanceActivityId);
                } else {
                    await this.$store.dispatch('ratingValuation/getValuation', this.$route.params.id);
                }
                await this.$store.dispatch('propertyDraft/getPropertyDetail', this.ratingValuation.propertyDetailId);
                await this.$store.dispatch('property/getProperty', this.propertyDetail.propertyId);
            } catch (err) {
                this.handleException(err);
            }
        },
        /* TODO Global exception handling (shouldnt need to do on all components etc) */
        handleException(err) {
            alert(err);
        },
    },
};
</script>

<template>
    <div class="contentWrapper resultsWrapper">
        <property-summary
            v-if="propertyDetail && propertyDetail.propertyId"
            :property-id="propertyDetail.propertyId"
            :can-navigate="false"
        />
        <div v-if="loading">
            Loading...
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
        <div
            v-if="draftException || valuationException"
            class="bAlert bAlert-danger"
        >
            Unexpected Error: {{ valuationException }} {{ draftException }}
        </div>

        <div
            v-if="!loading
                && propertyDetail
                && ratingValuation
            "
        >
            <div class="col-container mdl-shadow--3dp">
                <div class="title">
                    <div class="col col-6">
                        <h1>
                            Values (as at {{ ratingValuation.effectiveDate | date }})
                        </h1>
                    </div>
                    <div class="righty">
                        <h1>
                            Action record created on : {{ latestActivityDate | date }}
                        </h1>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-1" />
                    <div class="col col-2">
                        <label class="righty">
                            <span class="label">Capital Value</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span class="label">Land Value</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span class="label">Value of Improvements</span>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-1">
                        <h3>Existing</h3>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>{{ ratingValuation.originalValue.capitalValue | currency }}</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>{{ ratingValuation.originalValue.landValue | currency }}</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.originalValue.valueOfImprovements | currency }}
                            </span>
                        </label>
                    </div>
                </div>
                <div
                    v-if="ratingValuation.adoptedValue"
                    class="col-row"
                >
                    <div class="col col-1">
                        <h3 class="adopted">Adopted&nbsp;Values</h3>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span class="adopted">{{ ratingValuation.adoptedValue.capitalValue | currency }}</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span class="adopted">{{ ratingValuation.adoptedValue.landValue | currency }}</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span class="adopted">{{ ratingValuation.adoptedValue.valueOfImprovements | currency }}</span>
                        </label>
                    </div>
                </div>
                <div
                    v-if="ratingValuation.adoptedValue && ratingValuation.originalValue"
                    class="col-row"
                >
                    <div class="col col-1">
                        <h3>Difference</h3>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>{{ ratingValuation.adoptedValue.capitalValue - ratingValuation.originalValue.capitalValue | currency }}</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>{{ ratingValuation.adoptedValue.landValue - ratingValuation.originalValue.landValue | currency }}</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>{{ ratingValuation.adoptedValue.valueOfImprovements - ratingValuation.originalValue.valueOfImprovements | currency }}</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-container mdl-shadow--3dp">
                <h1 class="title">
                    Linked Building Consents
                </h1>
                <!-- TODO Change to use valuation not QPID -->
                <valuation-roll-maintenance-activities :qpid="qpid" />
            </div>

            <div class="col-container mdl-shadow--3dp">
                <h1 class="title">
                    Job Completion
                </h1>
                <div class="col-row">
                    <div class="col col-12">
                        <label>
                            <span class="label">Updated Property Description</span>
                            <span>{{ propertyDetail.description | emptyToDash }}</span>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-12">
                        <label>
                            <span class="label">Changes to Property</span>
                            <span>{{ ratingValuation.summaryOfChanges | emptyToDash }}</span>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-6">
                        <label>
                            <span class="label">Output Code</span>
                            <classification-lookup
                                v-if="propertyDetail && propertyDetail.audit"
                                category="OutputCode_DVR"
                                :value="propertyDetail.audit.outputCode"
                            />
                        </label>
                    </div>
                    <div class="col col-6">
                        <label>
                            <span class="label">Source</span>
                            <classification-lookup
                                v-if="propertyDetail && propertyDetail.audit"
                                category="Source_DVR"
                                :value="propertyDetail.audit.source"
                            />
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-6">
                        <label>
                            <span class="label">Reason for Change</span>
                            <span>{{ propertyDetail.audit.reasonForChange | emptyToDash }}</span>
                        </label>
                    </div>
                    <div class="col col-6">
                        <label>
                            <span class="label">Job Valuer</span>
                            <span>{{ valuerName | emptyToDash }}</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="col-container mdl-shadow--3dp">
                <h1 class="title">
                    Valuation Worksheet
                </h1>

                <valuation-worksheet-read-only :rating-valuation="ratingValuation" :valuation-reference="valuationReference" />
            </div>

            <div
                v-if="ratingValuation.comparablePropertySnapshots"
                class="col-container mdl-shadow--3dp"
            >
                <h1 class="title">
                    Comparable Properties
                </h1>
                <view-comparable-properties
                    :comparables="ratingValuation.comparablePropertySnapshots"
                />
            </div>

            <div class="col-container mdl-shadow--3dp">
                <h1 class="title">
                    Property Snapshot
                </h1>
                <property-details-read-only
                    :property-detail="propertyDetail"
                    :show-derived-dvr-fields="true"
                    :show-buildings-and-spaces="true"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
label {
    cursor: text;
}

.label {
    display: block;
}
.col-container.mdl-shadow--3dp {
    margin-top: 0.8rem;
    margin-bottom: 0.8rem;
}
.adopted {
    font-weight: 550;
}

</style>

<script>
export default {
    props: {
        ratingValuation: {},
        valuationReference: '',
    },
    computed: {
        landComponents() {
            return this.ratingValuation.ratingValuationComponents
                .filter(c => c.componentType === 'LAND')
                .sort((a, b) => a.value > b.value);
        },
        otherBuildingComponents() {
            return this.ratingValuation.ratingValuationComponents
                .filter(c => c.componentType === 'OTHER_BUILDING')
                .sort((a, b) => a.value > b.value);
        },
        otherImprovementComponents() {
            return this.ratingValuation.ratingValuationComponents
                .filter(c => c.componentType === 'OTHER_IMPROVEMENT')
                .sort((a, b) => a.value > b.value);
        },
        principalBuildingComponents() {
            return this.ratingValuation.ratingValuationComponents
                .filter(c => c.componentType === 'PRIMARY_BUILDING')
                .sort((a, b) => a.value > b.value);
        },
        withApportionments() {
            return this.ratingValuation.apportionmentValues?.length > 0;
        },
        adjustmentsColumnToggle() {
            return this.ratingValuation.apportionmentValues?.length > 0 ? 'col col-1' : 'col col-2';
        }
    },
    methods: {
        calculateApportionmentLumpSum(apportionment) {
            return Math.round(
                this.ratingValuation.lumpSum * apportionment.adoptedUnadjustedValue.capitalValue / this.ratingValuation.unadjustedValue.capitalValue
            );
        }
    },
};
</script>

<template>
    <div>
        <div class="property-draft-section col-container">
            <h2 class="section-title">
                Improvements
            </h2>
            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-2">
                    <label>
                        <span class="label">Description</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span class="label">Area (m<sup>2</sup>)</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span class="label">Rate (per m<sup>2</sup>)</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span class="label">Value</span>
                    </label>
                </div>
            </div>
            <div
                v-for="(comp, index) in principalBuildingComponents"
                :key="`principalBuildingComponents${index}`"
                class="col-row"
            >
                <div class="col col-2">
                    <h3 v-if="index == 0">
                        Principal Buildings
                    </h3>
                </div>
                <div class="col col-2">
                    <label>
                        <span>{{ comp.buildingType }} - {{ comp.description }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span v-if="comp.areaInSquareMetres">
                            {{ comp.areaInSquareMetres | numeral }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span v-if="comp.valuePerSquareMetre">
                            {{ comp.valuePerSquareMetre | currency({ fractionCount: 2 }) }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span>{{ comp.value | currency }}</span>
                    </label>
                </div>
            </div>
            <div
                v-for="(comp, index) in otherBuildingComponents"
                :key="`otherBuildingComponents${index}`"
                class="col-row"
            >
                <div class="col col-2">
                    <h3 v-if="index == 0">
                        Other Buildings
                    </h3>
                </div>
                <div class="col col-2">
                    <label>
                        <span>{{ comp.buildingType }} - {{ comp.description }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span v-if="comp.areaInSquareMetres">
                            {{ comp.areaInSquareMetres | numeral }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span v-if="comp.valuePerSquareMetre">
                            {{ comp.valuePerSquareMetre | currency({ fractionCount: 2 }) }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span>{{ comp.value | currency }}</span>
                    </label>
                </div>
            </div>
            <div
                v-for="(comp, index) in otherImprovementComponents"
                :key="`otherImprovementComponents${index}`"
                class="col-row"
            >
                <div class="col col-2">
                    <h3 v-if="index == 0">
                        Other Improvements
                    </h3>
                </div>
                <div class="col col-2">
                    <label>
                        <span>{{ comp.description }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span v-if="comp.areaInSquareMetres">
                            {{ comp.areaInSquareMetres | numeral }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span v-if="comp.valuePerSquareMetre">
                            {{ comp.valuePerSquareMetre | currency({ fractionCount: 2 }) }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span>{{ comp.value | currency }}</span>
                    </label>
                </div>
            </div>

            <h2 class="section-title">
                Land
            </h2>
            <div class="col-row">
                <div class="col col-2" />
                <div class="col col-2">
                    <label>
                        <span class="label">Description</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span class="label">Area (m<sup>2</sup>)</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span class="label">Rate (per m<sup>2</sup>)</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span class="label">Value</span>
                    </label>
                </div>
            </div>
            <div
                v-for="(comp, index) in landComponents"
                :key="`landComponents${index}`"
                class="col-row"
            >
                <div class="col col-2" />
                <div class="col col-2">
                    <label>
                        <span>{{ comp.description }}</span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span v-if="comp.areaInSquareMetres">
                            {{ comp.areaInSquareMetres | numeral }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span v-if="comp.valuePerSquareMetre">
                            {{ comp.valuePerSquareMetre | currency({ fractionCount: 2 }) }}
                        </span>
                    </label>
                </div>
                <div class="col col-1">
                    <label class="righty">
                        <span>{{ comp.value | currency }}</span>
                    </label>
                </div>
            </div>
        </div>
        <div class="property-draft-section col-container">
            <h2 class="section-title">
                <template v-if="ratingValuation.isMaoriLand">
                    Worksheet Totals (Unadjusted)
                </template>
                <template v-else>
                    Worksheet Totals
                </template>
            </h2>
            <div class="col-row">
                <div class="col col-1" />
                <div class="col col-2">
                    <label class="righty">
                        <span class="label">Capital Value</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="label">Land Value</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="label">Value of Improvements</span>
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-1">
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>
                            {{ ratingValuation.calculatedValue.capitalValue | currency }}
                        </span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>{{ ratingValuation.calculatedValue.landValue | currency }}</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span>
                            {{ ratingValuation.calculatedValue.valueOfImprovements | currency }}
                        </span>
                    </label>
                </div>
            </div>
        </div>
        <template v-if="ratingValuation.isMaoriLand">
            <div class="property-draft-section col-container">
                <h2 class="section-title">
                    Maori Land
                </h2>
                <div class="col-row">
                    <div class="col col-2" />
                    <div class="col col-1">
                        <label class="righty">
                            <span class="label">Adjustment</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span class="label">Revision Adjustment</span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span class="label">No. of Owners</span>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-2">
                        <h3>
                            Multiple Owners
                        </h3>
                    </div>
                    <div class="col col-1">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.maoriLandData.currentMaoriLandAdjustment.multipleOwnerAdjustmentPercentage | percentage }}
                            </span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.maoriLandData.revisedMaoriLandAdjustment.multipleOwnerAdjustmentPercentage | percentage }}
                            </span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.maoriLandData.numberOfOwners | numeral}}
                            </span>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-2">
                        <h3>
                            Site Significance
                        </h3>
                    </div>
                    <div class="col col-1">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.maoriLandData.currentMaoriLandAdjustment.siteSignificanceAdjustmentPercentage | percentage }}
                            </span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.maoriLandData.revisedMaoriLandAdjustment.siteSignificanceAdjustmentPercentage | percentage }}
                            </span>
                        </label>
                    </div>
                </div>
            </div>
        </template>
        <div class="property-draft-section col-container">
            <h2 class="section-title">
                Adopted Values
            </h2>
            <div class="col-row">
                <div class="col col-1">
                    <template v-if="withApportionments">
                        <label>
                            <span class="label">Valuation&nbsp;Reference</span>
                        </label>
                    </template>
                </div>
                <template v-if="ratingValuation.isMaoriLand">
                    <template v-if="withApportionments">
                        <div class="col col-3">
                            <label class="righty">
                                <span class="label">Lump Sum</span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label class="righty">
                                <span class="label">Adjustment</span>
                            </label>
                        </div>
                    </template>
                    <template v-else>
                        <div class="col col-2">
                            <label class="righty">
                                <span class="label">Lump Sum</span>
                            </label>
                        </div>
                        <div class="col col-2">
                            <label class="righty">
                                <span class="label">Adjustment</span>
                            </label>
                        </div>
                    </template>
                </template>
                <div class="col col-2">
                    <label class="righty">
                        <span class="label">Capital Value</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="label">Land Value</span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="label">Value of Improvements</span>
                    </label>
                </div>
            </div>
            <template v-for="(apportionment, index) in ratingValuation.apportionmentValues">
                <template v-if="ratingValuation.isMaoriLand">
                    <div :key="`unadjustedValueApportionment${index}`" class="col-row">
                        <div class="col col-1">
                            <h3>
                                {{ valuationReference }}&nbsp;{{ apportionment.suffix }}
                            </h3>
                        </div>
                        <div class="col col-2">
                            <h3>
                                Unadjusted&nbsp;Values
                            </h3>
                        </div>
                        <div class="col col-1"/>
                        <div class="col col-1"/>
                        <div class="col col-2">
                            <label class="righty">
                                <span>
                                    {{ apportionment.adoptedUnadjustedValue.capitalValue | currency }}
                                </span>
                            </label>
                        </div>
                        <div class="col col-2">
                            <label class="righty">
                                <span>
                                    {{ apportionment.adoptedUnadjustedValue.landValue | currency }}
                                </span>
                            </label>
                        </div>
                        <div class="col col-2">
                            <label class="righty">
                                <span>
                                    {{ apportionment.adoptedUnadjustedValue.valueOfImprovements | currency }}
                                </span>
                            </label>
                        </div>
                    </div>
                </template>
                <div :key="`adoptedValueApportionment${index}`" class="col-row">
                    <div class="col col-1">
                        <h3>
                            {{ valuationReference }} {{ apportionment.suffix }}
                        </h3>
                    </div>
                    <template v-if="ratingValuation.isMaoriLand">
                        <div class="col col-2">
                            <h3>
                                Rating Values
                            </h3>
                        </div>
                        <div class="col col-1">
                            <label class="righty">
                                <span>
                                    {{ calculateApportionmentLumpSum(apportionment) | currency }}
                                </span>
                            </label>
                        </div>
                        <div class="col col-1">
                            <label class="righty">
                                {{ ratingValuation.currentTotalAdjustment | percentage }}
                            </label>
                        </div>
                    </template>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ apportionment.adoptedValue.capitalValue | currency }}
                            </span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ apportionment.adoptedValue.landValue | currency }}
                            </span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ apportionment.adoptedValue.valueOfImprovements | currency }}
                            </span>
                        </label>
                    </div>
                </div>
            </template>
            <template v-if="ratingValuation.isMaoriLand">
                <div class="col-row">
                    <div class="col col-1">
                        <template v-if="withApportionments">
                            <h3>
                                {{ valuationReference }}
                            </h3>
                        </template>
                        <template v-else>
                            <h3>
                                Unadjusted&nbsp;Values
                            </h3>
                        </template>
                    </div>
                    <template v-if="withApportionments">
                        <div class="col col-2">
                            <h3>
                                Unadjusted&nbsp;Values
                            </h3>
                        </div>
                    </template>
                    <div v-bind:class="adjustmentsColumnToggle"/>
                    <div v-bind:class="adjustmentsColumnToggle"/>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.unadjustedValue.capitalValue | currency }}
                            </span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.unadjustedValue.landValue | currency }}
                            </span>
                        </label>
                    </div>
                    <div class="col col-2">
                        <label class="righty">
                            <span>
                                {{ ratingValuation.unadjustedValue.valueOfImprovements | currency }}
                            </span>
                        </label>
                    </div>
                </div>
            </template>
            <div class="col-row">
                <div class="col col-1">
                    <template v-if="withApportionments">
                        <h3 class="adopted">
                            {{ valuationReference }}
                        </h3>
                    </template>
                    <template v-else>
                        <h3 class="adopted">
                            Rating Values
                        </h3>
                    </template>
                </div>
                <template v-if="ratingValuation.isMaoriLand">
                    <template v-if="withApportionments">
                        <div class="col col-2">
                            <h3 class="adopted">
                                Rating Values
                            </h3>
                        </div>
                    </template>
                    <div v-bind:class="adjustmentsColumnToggle">
                        <label class="righty">
                            <span class="adopted">
                                {{ ratingValuation.lumpSum | currency }}
                            </span>
                        </label>
                    </div>
                    <div v-bind:class="adjustmentsColumnToggle">
                        <label class="righty">
                            <span class="adopted">
                                {{ ratingValuation.currentTotalAdjustment | percentage }}
                            </span>
                        </label>
                    </div>
                </template>
                <div class="col col-2">
                    <label class="righty">
                        <span class="adopted">
                            {{ ratingValuation.adoptedValue.capitalValue | currency }}
                        </span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="adopted">
                            {{ ratingValuation.adoptedValue.landValue | currency }}
                        </span>
                    </label>
                </div>
                <div class="col col-2">
                    <label class="righty">
                        <span class="adopted">
                            {{ ratingValuation.adoptedValue.valueOfImprovements | currency }}
                        </span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
label {
    cursor: text;
}
.label {
    display: block;
}
.adopted {
    font-weight: 550;
}

</style>

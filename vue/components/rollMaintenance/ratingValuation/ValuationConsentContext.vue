<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import PropertySummary from '../../property/PropertySummary.vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore } from '@/composables/useStore';
import { store } from '@/DataStore';
import useModal from '@/composables/useModal';
import PropertySummaryProposedValues from '../../property/PropertySummaryProposedValues.vue';
import ValuationActions from '@/components/rollMaintenance/ratingValuation/common/ValuationActions.vue';
import ConsentJobStepper from '@/components/rollMaintenance/ratingValuation/common/ConsentJobStepper.vue';
import { ValuationValidationList } from '@/components/rollMaintenance/ratingValuation/common';
import { debounce } from 'lodash';
import { provideRatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';
import ValidationConfirmationModal from '@/components/rollMaintenance/ratingValuation/ValuationValidationModal.vue';
import { openQivsInNewTab } from '@/utils/QivsUtils';

const modal = useModal();
const route = useRoute();
const router = useRouter();

const loading = ref(true);
const errored = ref(false);
const exceptionMessage = ref();

const ratingValuationId = computed(() => route.params.id);

const userStore = useStore('userData');
const {
    qivsMasterDetailsUrl
} = userStore.getters;

const ratingValuationStore = useStore('ratingValuation');
const {
    ratingValuation,
    exception,
    propertyActivities,
    validationSet,
} = ratingValuationStore.state;

const propertyId = computed(() => ratingValuation.value?.ratingUnit?.propertyId);
const propertyDraftStore = useStore('propertyDraft');
const {
    validationSet: propertyValidationSet,
    propertyDetail,
} = propertyDraftStore.state;

const propertyStore = useStore('property');
const {
    property,
} = propertyStore.state;

const isMaoriLand = computed(() => property.value?.landUseData?.isMaoriLand);
const unadjustedValuation = computed(() => property.value?.maoriLandData?.currentMaoriLandAdjustment?.unadjustedValuation);

const isSetupComplete = computed(() => {
    if (!propertyActivities.value) {
        return false;
    }

    return !propertyActivities.value.find(activity => ratingValuation.value.rollMaintenanceActivityIds.includes(activity.id) && activity.setupComplete === false);
});

async function loadRatingValuationJob(id) {
    loading.value = true;

    try {
        if (!ratingValuation.value || ratingValuation.value.id !== ratingValuationId.value) {
            await ratingValuationStore.dispatch('getValuation', id);
        }

        const promises = []
        promises.push(propertyStore.dispatch('getProperty', propertyId.value));
        promises.push(propertyDraftStore.dispatch('getPropertyDetail', ratingValuation.value.propertyDetailId || ratingValuation.value.ratingUnit.qpid));
        promises.push(ratingValuationStore.dispatch('loadComparables'));
        promises.push(ratingValuationStore.dispatch('loadRelatedRollMaintenanceActivities'))

        await Promise.allSettled(promises);
        await store.dispatch('fetchTAZoneClassification', property.value.territorialAuthority.code);
        validate();
    }
    catch (error) {
        console.error('Failed to load valuation job', error);
        await modal.showError('Failed to load valuation job', 'An error occurred while loading the valuation job. Please contact support or try again later.');
        errored.value = true;
    }

    loading.value = false;
}

const debouncedValidate = debounce(validate, 250);
function validate(options) {
    if (options?.atCompletion) {
        ratingValuationStore.dispatch('validateValuationAtCompletion', {
            propertyDetail: propertyDetail.value,
            activities: propertyActivities.value,
            isComplete: options?.isComplete,
        });
    }
    else {
        ratingValuationStore.dispatch('validateValuation', {
            propertyDetail: propertyDetail.value,
            activities: propertyActivities.value,
        });
    }

    propertyDraftStore.dispatch('validatePropertyDraft', {
        property: property.value,
    });
}

const debouncedSave = debounce(save, 500);

async function wrappedSave(...args) {
    return new Promise((resolve) => {
        debouncedSave.flush();
        resolve(save(...args));
    });
}

async function save(silent = false, isComplete = false, isPropertyDraftStep = false, atCompletion = false) {
    await validate({ atCompletion, isComplete });
    let errors = [];
    let warnings = [];

    if (atCompletion) {
        errors = [...validationSet.value.errors, ...propertyValidationSet.value.errors];
        warnings = [...validationSet.value.warnings, ...propertyValidationSet.value.warnings];
    }
    else if (isPropertyDraftStep) {
        errors = propertyValidationSet.value.errors;
        warnings = propertyValidationSet.value.warnings;
    }
    else {
        errors =  validationSet.value.errors;
        warnings = validationSet.value.warnings;
    }

    return await saveRatingValuation(errors, warnings, silent, false, atCompletion);
}

async function complete() {
    const linkedActivities = propertyActivities.value.filter(activity => ratingValuation.value.rollMaintenanceActivityIds.includes(activity.id));
    const saleInspectionActivity = linkedActivities.find(activity => activity.saleInspectionConsent?.saleId);

    let message = 'Your changes have been saved. The property has now been updated.';
    let navigateTo = {
        name: 'roll-maintenance',
    };

    if (saleInspectionActivity) {
        message = `${message}\nOne of the linked activities is an SI consent, the sale property data must be refreshed.`;
        navigateTo = {
            name: 'property-sale',
            params: {
                qpid: ratingValuation.value.ratingUnit.qpid,
                id: saleInspectionActivity.saleInspectionConsent.saleId,
            },
        };
    }

    try {
        await ratingValuationStore.dispatch('saveValuation');
        await propertyDraftStore.dispatch('savePropertyDraft');
        await ratingValuationStore.dispatch('completeValuation');

        const propertyFailed = propertyValidationSet.value.hasErrors || propertyValidationSet.value.hasWarnings;
        const valuationFailed = validationSet.value.hasErrors || validationSet.value.hasWarnings;
        if (!valuationFailed && !propertyFailed) {
            await modal.showSuccess('Valuation Complete', message);
            router.push(navigateTo);
            openQivsInNewTab(qivsMasterDetailsUrl.value(ratingValuation.value.ratingUnit.qpid));
        } else {
            if (validationSet.value.hasErrors || propertyValidationSet.value.hasErrors) {
                await modal.show(
                    ValidationConfirmationModal,
                    {
                        title: 'Failed to Complete Valuation',
                        subTitle: 'Unable to complete until the following issues are resolved:',
                        isError: true,
                        validationList: validationSet.value.merge(propertyValidationSet.value).errors,
                        cancelErrorText: 'RETURN TO JOB COMPLETION',
                    }
                );
                return;
            }

            if (propertyValidationSet.value.hasWarnings || validationSet.value.hasWarnings) {
                const continueCompletion = await modal.show(
                    ValidationConfirmationModal,
                    {
                        title: "Do you want to proceed?",
                        subTitle: "The following validation checks are failing:",
                        validationList: validationSet.value.merge(propertyValidationSet.value).warnings,
                        isWarning: true,
                        cancelText: 'NO, RETURN TO VALUATION',
                        confirmText: 'YES, COMPLETE VALUATION',
                    }
                )

                if (!continueCompletion) {
                    return;
                }

                await ratingValuationStore.dispatch('completeValuation', true);
                await modal.showSuccess("Valuation Complete", message);
                router.push(navigateTo);
                openQivsInNewTab(qivsMasterDetailsUrl.value(ratingValuation.value.ratingUnit.qpid));
            }
        }
    } catch (err) {
        console.error('Failed to complete valuation', err);
        modal.showError("Error", "Failed to complete the valuation.");
    }
}

const debouncedSaveBetweenSteps = debounce(saveBetweenSteps, 500);

async function wrappedSaveBetweenSteps(...args) {
    return new Promise((resolve) => {
        debouncedSaveBetweenSteps.flush();
        resolve(saveBetweenSteps(...args));
    });
}

async function saveBetweenSteps(step, silent, isPropertyDraftStep = false) {
    const errors = isPropertyDraftStep ? propertyValidationSet.value.errors : validationSet.value.errors.filter(error => error.field.step === step);
    const warnings = isPropertyDraftStep ? propertyValidationSet.value.warnings : validationSet.value.warnings.filter(warning => warning.field.step === step);
    return await saveRatingValuation(errors, warnings, silent, true);
}

async function saveRatingValuation(errors, warnings, silent, isOnSaveBetweenSteps = false, isWriteupStep = false) {
    let proceedToSave = true;

    if (errors.length > 0) {
        const title = `Unable to save until the following ${errors.length} issues are resolved:`;
        const cancelErrorText = isWriteupStep ? 'RETURN TO JOB COMPLETION' : 'RETURN TO WORKSHEET';
        const payload = {
            title,
            isError: true,
            cancelErrorText,
            onlyConfirm: false,
            validationList: errors
        }
        proceedToSave = await modal.show(ValidationConfirmationModal, payload);
        if (!proceedToSave) {
            return false;
        }
    }
    if (!isOnSaveBetweenSteps && warnings.length > 0) {
        const title = 'Do you want to proceed?';
        const subTitle = 'The following validation checks are failing:';
        const confirmText = isWriteupStep ? 'YES SAVE JOB' : 'YES, SAVE WORKSHEET';
        const cancelText = isWriteupStep ? 'NO, RETURN TO JOB COMPLETION' : 'NO, RETURN TO WORKSHEET';
        const payload = {
            title,
            subTitle,
            isWarning: true,
            cancelText,
            onlyConfirm: false,
            confirmText,
            validationList: warnings,
        }
        proceedToSave = await modal.show(ValidationConfirmationModal, payload);
        if (!proceedToSave) {
            return false;
        }
    }
    const results = await Promise.allSettled([
        ratingValuationStore.dispatch('saveValuation'),
        propertyDraftStore.dispatch('savePropertyDraft')
    ]);

    const failed = results.some(result => result.status === 'rejected');

    if (failed) {
        await modal.showError('Error', 'Failed to save Rating Valuation Job');
        return false;
    }

    if (!silent) {
        await modal.showSuccess('Saved Job', 'Your changes have been saved');
    }
    return true;
}

onMounted(async () => {
    const id = route.params.id;
    await loadRatingValuationJob(id);

    if (ratingValuation.value?.status === 'COMPLETE') {
        router.push({
            name: 'roll-maintenance-activity-latest-valuation',
            params: {
                rollMaintenanceActivityId: ratingValuation.value.rollMaintenanceActivityIds[0],
            }
        })
    }
});

watch(exceptionMessage, () => {
    if (exceptionMessage.value) {
        window.scrollTo({
            top: exceptionMessage.value?.$el?.offsetTop || 0,
            left: 0,
            behavior: 'smooth',
        });
    }
});

provideRatingValuationContext({
    save: wrappedSave,
    saveBetweenSteps: wrappedSaveBetweenSteps,
    validate: debouncedValidate,
    complete,
})
</script>

<template>
    <div class="contentWrapper resultsWrapper">
        <PropertySummary
            :can-navigate="false"
            :property-id="propertyId"
        >
            <template v-if="ratingValuation && property">
                <PropertySummaryProposedValues
                    v-if="isMaoriLand"
                    :current-unadjusted-values="unadjustedValuation"
                    :current-values="property.currentValuation"
                    :proposed-values="ratingValuation.adoptedValue"
                    :unadjusted-values="ratingValuation.unadjustedValue"
                    :working-net-rate="ratingValuation.workingNetRate"
                    :show-unadjusted-values="isMaoriLand"
                    class="qv-bg-mediumblue"
                />
                <PropertySummaryProposedValues
                    v-else
                    :current-values="property.currentValuation"
                    :proposed-values="ratingValuation.adoptedValue"
                    :working-net-rate="ratingValuation.workingNetRate"
                />
            </template>
        </PropertySummary>
        <div v-if="loading">
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
        <div v-else-if="!errored && !exception">
            <div class="qv-flex-row qv-w-full qv-bg-light qv-p-3 monarch-bg-white qv-worksheet">
                <div class="qv-flex-column qv-gap-4 qv-pt-3" style="width: 15%">
                    <ConsentJobStepper :is-setup-complete="isSetupComplete"
                                       :property-validation-set="propertyValidationSet"
                                       :validation-set="validationSet" />
                    <ValuationActions :load-on-mount="true" />
                </div>
                <div class="qv-flex-grow">
                    <template v-if="validationSet">
                        <ValuationValidationList :validation-set="validationSet" :pd-validation-set="propertyValidationSet" />
                    </template>
                    <RouterView/>
                </div>
            </div>
        </div>
        <div v-else>
            <div class="bAlert bAlert-danger exception-message">
                An error occurred while loading the valuation job. Please contact support or try again later.
                <span v-if="exception">{{ exceptionMessage }}</span>
            </div>
        </div>
    </div>

</template>

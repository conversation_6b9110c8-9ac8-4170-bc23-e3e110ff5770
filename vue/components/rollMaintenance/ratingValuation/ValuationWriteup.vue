<template>
    <div>
        <div
            v-if="propertyDetail && propertyDetail.isStale"
            class="righty message message-warning"
        >
            NOTE: The detailed information for this property has changed, please review the
            <router-link :to="{name: 'property', params: {qpid: qpid}}" target="_blank">current property</router-link>
            and update the draft property details.
        </div>
        <div class="col-container mdl-shadow--3dp form-section">
            <expander class="righty" v-model="expandConsents" />
            <h1 class="title">
                Building Consents
            </h1>
            <current-roll-maintenance-activities
                :load-on-mount="true"
                :can-edit="false"
                v-show="expandConsents"
            />
        </div>
        <ValidationContext :validation-set="validationSet" class="col-container mdl-shadow--3dp form-section">
            <h1>Job Completion</h1>
            <div class="property-draft-section col-container">
                <div class="col-row">
                    <div class="col col-12">
                        <label>
                            <span class="label">Updated Property Description</span>
                            <ValidationWrapper :path="pdFields.DESCRIPTION">
                                <textarea
                                    id="description"
                                    v-model="descriptionGetSet"
                                    :class="errorClasses('description')"
                                    @input="onChanged"
                                />
                            </ValidationWrapper>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-12" >
                        <label>
                            <span class="label">Changes to Property</span>
                            <ValidationWrapper :path="fields.SUMMARY_OF_CHANGES">
                                <textarea
                                    id="summaryOfChanges"
                                    v-model="summaryOfChangesGetSet"
                                    :class="errorClasses('summaryOfChanges')"
                                    @input="onChanged"
                                />
                            </ValidationWrapper>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-3">
                        <label>
                            <span class="label">Output Code</span>
                            <ValidationWrapper :path="pdFields.AUDIT_OUTPUT_CODE">
                                <classification-dropdown
                                    id="outputCode"
                                    category="OutputCode_DVR"
                                    label="code"
                                    :single-label-function="
                                        (opt) => `${opt.code} — ${opt.description}`
                                    "
                                    :class="errorClasses('audit.outputCode')"
                                    :value="propertyDetail.audit.outputCode"
                                    @input="updatePropertyDetail"
                                />
                            </ValidationWrapper>
                        </label>
                    </div>
                    <div class="col col-3">
                        <label>
                            <span class="label">Source</span>
                            <ValidationWrapper :path="pdFields.AUDIT_SOURCE">
                                <classification-dropdown
                                    id="source"
                                    category="Source_DVR"
                                    label="code"
                                    :single-label-function="(opt) => `${opt.description}`"
                                    :label-function="(opt) => `${opt.description}`"
                                    :class="errorClasses('audit.source')"
                                    :value="propertyDetail.audit.source"
                                    @input="updatePropertyDetail"
                                />
                            </ValidationWrapper>
                        </label>
                    </div>
                    <div class="col col-6" >
                        <label>
                            <span class="label">Reason for Change</span>
                            <ValidationWrapper :path="pdFields.AUDIT_REASON_FOR_CHANGE">
                                <input
                                    id="reasonForChange"
                                    class="qv-form-input"
                                    v-model="reasonForChangeGetSet"
                                    maxlength="100"
                                    :class="errorClasses('reasonForChange')"
                                    @input="onChanged"
                                >
                            </ValidationWrapper>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-3">
                        <label>
                            <span class="label">Job Valuer</span>
                            <ValidationWrapper data-cy="bc-job-valuers" :path="fields.VALUER">
                                <valuer-dropdown
                                    id="valuer"
                                    label="ntUsername"
                                    hide-codes
                                    :class="errorClasses('ratingValuation.valuer')"
                                    :value="ratingValuation.valuer"
                                    placeholder=""
                                    @input="updateRatingValuation"
                                />
                            </ValidationWrapper>
                        </label>
                    </div>
                    <div class="col col-3">
                        <label>
                            <span class="label">Inspection Status</span>
                            <ValidationWrapper data-cy="bc-job-valuers" :path="fields.PROPERTY_INSPECTED_TYPE">
                                <property-inspected-type-dropdown
                                    :value = "propertyInspectedType"
                                    :options="propertyInspectedTypeList"
                                    @input="updatePropertyInspectedType"
                                />
                            </ValidationWrapper>
                        </label>
                    </div>
                </div>
                <div class="col-row">
                    <div class="col col-12">
                        <div class="righty">
                            <button
                                class="mdl-button mdl-js-button
                                    mdl-button--raised mdl-js-ripple-effect
                                "
                                :disabled="saving"
                                @click="saveChanges()"
                            >
                                Save as Draft
                            </button>
                            <button
                                data-cy="bc-job-complete-valuation"
                                class="mdl-button mdl-js-button
                                    mdl-button--raised mdl-js-ripple-effect
                                    mdl-button--colored
                                "
                                :disabled="saving || !canValueRatingUnit"
                                :title="!canValueRatingUnit
                                    && `You do not have permission to value a rating unit.`
                                "
                                @click="complete"
                            >
                                Complete Valuation
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </ValidationContext>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import commonUtils from '@/utils/CommonUtils';
import { openQivsInNewTab } from '@/utils/QivsUtils';
import { RatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';
import { RatingValuation, PropertyDetail, createValidationSet } from '@quotable-value/validation';
import { ValidationContext, ValidationWrapper } from '@/components/ui/validation';
import { getPropertyInspectedTypes } from '@/composables/propertyInspectedType.js';
import { useStore } from '@/composables/useStore';
import { set } from 'lodash';

export default {
    components: {
        ValidationContext,
        ValidationWrapper,
        'classification-dropdown': () => import(/* webpackChunkName: "ClassificationDropdown" */ '../../common/form/ClassificationDropdown.vue'),
        'valuer-dropdown': () => import(/* webpackChunkName: "ValuerDropdown" */ '../../common/form/ValuerDropdown.vue'),
        'property-summary': () => import(/* webpackChunkName: "PropertySummary" */ '../../property/PropertySummary.vue'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../common/form/ValidationMessage.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "ValidationHeaderMessage" */ '../../common/form/ValidationHeaderMessage.vue'),
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../../common/modal/AlertModal.vue'),
        'valuation-actions': () => import(/* webpackChunkName: "ValuationActions" */ './common/ValuationActions.vue'),
        'current-roll-maintenance-activities': () => import(/* webpackChunkName: "ValuationRollMaintenanceActivities" */ './activities/RollMaintenanceActivities.vue'),
        'expander': () => import(/* webpackChunkName: "Expander" */ '../../common/Expander.vue'),
        'property-inspected-type-dropdown': () => import(/* webpackChunkName: "PropertyInspectedTypeDropDown" */ '../../common/form/PropertyInspectedTypeDropDown.vue'),
    },
    mixins: [commonUtils],
    inject: {
        context: {
            from: RatingValuationContext,
        }
    },
    data() {
        return {
            expandConsents: true,
            propertyInspectedTypeList: [],
            propertyInspectedType: null
        };
    },
    computed: {
        fields() {
            return RatingValuation.FIELDS;
        },
        pdFields() {
            return PropertyDetail.FIELDS;
        },
        ...mapState('valuersList', {
            valuers: 'valuers',
            valuersListLoading: 'loading',
        }),
        ...mapState('userData', {
            userFullName: 'userFullName',
            isInternalUser: 'isInternalUser',
            userDataLoading: 'loading',
        }),
        ...mapState('propertyDraft', {
            propertyDetail: 'propertyDetail',
            loading: 'loading',
            saving: 'saving',
            exception: 'exception',
            propertyDetailValidationSet: 'validationSet',
        }),
        ...mapGetters([
            'classificationsLoaded',
            'getCategoryClassifications',
        ]),
        ...mapState('property', {
            property: 'property',
            propertyLoading: 'loading',
            propertyErrors: 'errors',
        }),
        ...mapState('ratingValuation', {
            ratingValuation: 'ratingValuation',
            ratingValuationException: 'exception',
            ratingValuationValidationSet: 'validationSet',
            ratingValuationLoading: 'loading',
            propertyActivities: 'propertyActivities',
        }),
        ...mapState({
            canValueRatingUnit: state => state.application.monarchUser.canValueRatingUnit || false,
        }),
        descriptionGetSet: {
            get() {
                return this.propertyDetail.description;
            },
            set(value) {
                this.updatePropertyDetail({ id: 'description', value });
            },
        },
        summaryOfChangesGetSet: {
            get() {
                return this.ratingValuation.summaryOfChanges;
            },
            set(value) {
                this.updateRatingValuation({ id: 'summaryOfChanges', value });
            },
        },
        reasonForChangeGetSet: {
            get() {
                return this.propertyDetail.audit.reasonForChange;
            },
            set(value) {
                this.updatePropertyDetail({ id: 'reasonForChange', value });
            },
        },
        qpid() {
            return this.ratingValuation.ratingUnit.qpid;
        },
        errors() {
            const empty = {
                success: true,
                errors: [],
                warnings: [],
            };
            const propertyDetail = this.propertyDetailValidationSet || empty;
            const ratingValuation = this.ratingValuationValidationSet || empty;
            // these validation sets don't always have errors or warnings arrays in them.
            const hasErrors = (
                (propertyDetail.errors || propertyDetail.warnings)
                || (ratingValuation.errors || ratingValuation.warnings)
            );
            if (!hasErrors) {
                return empty;
            }
            return {
                success: propertyDetail.success && ratingValuation.success,
                errors: [
                    ...(propertyDetail.errors || []),
                    ...(ratingValuation.errors || []),
                ],
                warnings: [
                    ...(propertyDetail.warnings || []),
                    ...(ratingValuation.warnings || []),
                ],
            };
        },
        hasLoaded() {
            return (
                /* Not loading the valuation ... */
                !this.loading
                /* ... and has a valuation that is the valuation we want ... */
                && this.ratingValuation && this.$route.params.id === this.ratingValuation.id
            );
        },
        propertyId() {
            if (this.hasLoaded) return this.ratingValuation.ratingUnit.propertyId;

            return null;
        },
        validationSet() {
            const valSet = createValidationSet();
            if (this.propertyDetailValidationSet) {
                valSet.merge(this.propertyDetailValidationSet);
            }
            if (this.ratingValuationValidationSet) {
                valSet.merge(this.ratingValuationValidationSet);
            }

            return valSet;
        },
        hasReducedCV() {
            return this.property
                && this.property.currentValuation
                && this.ratingValuation
                && this.ratingValuation.adoptedValue
                && (this.ratingValuation.adoptedValue.capitalValue < this.property.currentValuation.capitalValue);
        },
        hasLVChanged() {
            return this.property
                && this.property.currentValuation
                && this.ratingValuation
                && this.ratingValuation.adoptedValue
                && this.ratingValuation.originalValue
                && (this.ratingValuation.adoptedValue.landValue !== this.ratingValuation.originalValue.landValue);
        },
        isSetupComplete() {
            if (!this.propertyActivities) {
                return false;
            }
            // if ALL linked activities are "setupComplete" then true (i.e. nothing is false)
            // If one of the linked roll maintenance activities has setupComplete === false, then setupComplete should be false.
            return !this.propertyActivities.find(activity => this.ratingValuation.rollMaintenanceActivityIds.includes(activity.id) && activity.setupComplete === false);
        },
        disabledSteps() {
            if (this.isSetupComplete === false) {
                return [1, 2, 3];
            }
            return [];
        },
        masterDetailsUrl() {
            return this.$store.getters['userData/qivsMasterDetailsUrl'](this.qpid);
        },
    },
    async created() {
        await this.$store.dispatch('valuersList/getValuers');
        this.setDefaultValuer();
        this.setDefaultOutputCode();
        await this.loadPropertyInspectedTypes();
    },
    methods: {
        updatePropertyInspectedType(value) {
            const ratingValuationStore = useStore('ratingValuation');
            const updatedRatingValuation = { ...this.ratingValuation };
            set(updatedRatingValuation, 'propertyInspectedType', value?.code);
            ratingValuationStore.commit('setRatingValuation', updatedRatingValuation);
            this.setPropertyInspectedType();
            this.onChanged();
        },
        async loadPropertyInspectedTypes() {
            this.propertyInspectedTypeList = await getPropertyInspectedTypes();
            this.setPropertyInspectedType();
        },
        setPropertyInspectedType() {
            this.propertyInspectedType = this.propertyInspectedTypeList.find(
                (type) => type.code === this.ratingValuation.propertyInspectedType
            ) || null;
        },
        setDefaultValuer(){
            for (let i = 0; i < this.valuers.length; i++) {
                if (this.valuers[i].name === this.userFullName && this.isInternalUser) {
                    this.ratingValuation.valuer = this.valuers[i];
                }
            }
        },
        updatePropertyDetail(data) {
            this.$store.commit('propertyDraft/setSinglePropertyDetail', data);
            this.onChanged();
        },
        updateRatingValuation(data) {
            const ratingValuation = { ...this.ratingValuation };
            ratingValuation[data.id] = data.value;
            this.$store.dispatch('ratingValuation/setValuation', ratingValuation);
            this.onChanged();
        },
        async saveChanges(silentSave = false) {
            let savePropertyDetailSuccess = false;
            let saveRatingValuationSuccess = false;
            let success = false;
            const isCompletion = false;
            const isPropertyDraftStep = false;
            const isWriteupStep = true;
            try {
                const saved = await this.context.save(silentSave, isCompletion, isPropertyDraftStep, isWriteupStep);
                if (!saved) {
                    return;
                }
                if (!this.propertyDetailValidationSet.hasErrors) {
                    savePropertyDetailSuccess = true;
                }
                if (!this.ratingValuationValidationSet.hasErrors) {
                    saveRatingValuationSuccess = true;
                }
                if (savePropertyDetailSuccess && saveRatingValuationSuccess) {
                    success = true;
                } else {
                    this.$nextTick(() => {
                        window.scrollTo({ top: this.$refs.validationHeader.$el.offsetTop, left: 0, behavior: 'smooth' });
                    });
                }
            } catch (err) {
                alert('Saving the form failed... ', err);
            }
            return success;
        },
        updateText(id, event) {
            const { value } = event.srcElement;
            this.update({ id, value });
        },
        async complete() {
            return this.context.complete();
        },
        scrollToErrors() {
            this.$nextTick(() => {
                const errorIcons = Array.from(document.querySelectorAll('span.material-icons'))
                    .filter(el => el.textContent.trim() === 'error');
                if (errorIcons.length > 0) {
                    errorIcons[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        },
        setDefaultOutputCode() {
            if (this.propertyDetail) {
                const noOwnersNotice = this.getCategoryClassifications('OutputCode_DVR').find(clas => clas.code == 7);
                const generateOwnersNotice = this.getCategoryClassifications("OutputCode_DVR").find( clas => clas.code == 6);
                const rv=this.ratingValuation;
                if (rv.adoptedValue && rv.originalValue) {
                    if (rv.adoptedValue.capitalValue === rv.originalValue.capitalValue && rv.adoptedValue.landValue === rv.originalValue.landValue) {
                        this.updatePropertyDetail({ id: "outputCode", value: noOwnersNotice });
                    }
                    else {
                        this.updatePropertyDetail({ id: "outputCode", value: generateOwnersNotice });
                    }
                }
            }

        },
        onChanged() {
            this.context.validate({ atCompletion: true, isComplete: true });
        }
    },
    async beforeRouteLeave(to, from, next) {
        // Silently save when navigating around valuation job pages
        const valuationJobPages = ['rating-valuation-comparable-properties', 'rating-valuation-worksheet', 'rating-valuation-writeup', 'rating-valuation-property-details'];
        const { STEPS } = RatingValuation
        if (valuationJobPages.includes(to.name)) {
            const saved = await this.context.saveBetweenSteps(STEPS.COMPLETION, true);
            if (!saved) {
                return;
            }
        }
        next();
    },
};
</script>
<style lang="scss" scoped>
    textarea {
        height: 100px;
    }
    .validation-header-message--warnings {
        font-size: 0.9em;
        overflow: auto;
    }

    .form-section {
        margin-bottom: 2rem;
    }
    .proposedValues ul{
        display: inline-grid;
    }
    .proposedValues.error {
        background-color: #fc3d39;
        padding-left: 0.3rem;
    }
</style>

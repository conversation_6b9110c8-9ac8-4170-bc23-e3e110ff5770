<script setup>
import {
    BaseTable,
    BaseTableHead,
    BaseTableBody,
    WorksheetTableHeader,
    WorksheetTableRow,
    useTable,
    BaseTableCell,
} from '@/components/ui/table';
import {
    VALUATION_COMPONENTS,
    calculateValue,
    calculateValuePerSquareMetre,
} from '@/composables/ratingValuation';
import { useVModel } from '@/composables/useVModel';
import { BaseInput, NumeralInput } from '@/components/ui/input';
import AddRemoveButton from '@/components/ui/button/AddRemoveButton.vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Array,
        required: true,
    },
    validations: {
        type: Object,
        required: false,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
const table = useTable(modelValue, {
    defaultValue: {
        componentType: VALUATION_COMPONENTS.PRIMARY_BUILDING,
        description: '',
        areaInSquareMetres: null,
        valuePerSquareMetre: null,
        value: null,
    },
});
</script>

<template>
    <ValidationProvider :path="FIELDS.PRIMARY_BUILDINGS" v-slot="{ hasErrorsForIndex }">
        <BaseTable data-cy="worksheet-table-primary-buildings">
            <WorksheetTableHeader>
                <BaseTableHead style="width: 17%;">
                    Building Type
                </BaseTableHead>
                <BaseTableHead style="width: 25%">
                    Space Description
                </BaseTableHead>
                <BaseTableHead>
                    Area
                </BaseTableHead>
                <BaseTableHead>
                    Rate /m<sup>2</sup>
                </BaseTableHead>
                <BaseTableHead>
                    Calculated Value
                </BaseTableHead>
                <BaseTableHead v-if="!readonly" aria-label="Action Column" />
            </WorksheetTableHeader>
            <BaseTableBody>
                <WorksheetTableRow v-for="(row, index) in modelValue" :key="row.identifier" :errored="hasErrorsForIndex(index)">
                    <BaseTableCell>
                        <BaseInput v-model="row.buildingType" :readonly="readonly" data-cy="building-type" />
                    </BaseTableCell>
                    <BaseTableCell>
                        <BaseInput v-model="row.description" :readonly="readonly" data-cy="description" />
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :index="index" :path="FIELDS.PRIMARY_BUILDING_AREA">
                            <NumeralInput v-model="row.areaInSquareMetres" :readonly="readonly" data-cy="area" preset="AREA" @input="() => calculateValuePerSquareMetre(row)" />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :index="index" :path="FIELDS.PRIMARY_BUILDING_RATE">
                            <NumeralInput v-model="row.valuePerSquareMetre" :readonly="readonly" data-cy="value-per-square-metre" fixed="4" preset="MONEY_2DP" @input="() => calculateValue(row)" />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :index="index" :path="FIELDS.PRIMARY_BUILDING_VALUE">
                            <NumeralInput v-model="row.value" :readonly="readonly" data-cy="value" preset="MONEY" @input="() => calculateValuePerSquareMetre(row)" />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell v-if="!readonly" class="qv-vertical-align-middle">
                        <AddRemoveButton :can-remove="index > 0" data-cy="add-remove-button" @add="() => table.addRow(index)" @remove="() => table.removeRow(index)" />
                    </BaseTableCell>
                </WorksheetTableRow>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

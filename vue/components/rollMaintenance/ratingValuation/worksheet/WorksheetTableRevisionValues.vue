<script setup>
import {
    BaseTable,
    BaseTableBody,
    WorksheetTableRow,
    BaseTableCell,
} from '@/components/ui/table';
import WorksheetTableValuesHeader from './WorksheetTableValuesHeader.vue';
import { useVModel } from '@/composables/useVModel';
import { NumeralInput } from '@/components/ui/input';

const props = defineProps({
    value: {
        type: Object,
        required: true,
    }
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
</script>

<template>
    <BaseTable>
        <WorksheetTableValuesHeader/>
        <BaseTableBody>
            <WorksheetTableRow>
                <BaseTableCell>
                    <NumeralInput preset="MONEY_POSITIVE" v-model="modelValue.capitalValue" readonly data-cy="revision-capital-value"/>
                </BaseTableCell>
                <BaseTableCell>
                    <NumeralInput preset="MONEY_POSITIVE" v-model="modelValue.landValue" readonly data-cy="revision-land-value"/>
                </BaseTableCell>
                <BaseTableCell>
                    <NumeralInput preset="MONEY" v-model="modelValue.valueOfImprovements" readonly data-cy="revision-value-of-improvements"/>
                </BaseTableCell>
            </WorksheetTableRow>
        </BaseTableBody>
    </BaseTable>
</template>

<style lang="scss" scoped></style>

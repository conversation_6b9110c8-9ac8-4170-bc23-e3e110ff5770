<script setup>
import { BaseTable, BaseTableBody } from '@/components/ui/table';
import WorksheetTableApportionmentValuesHeader from './WorksheetTableApportionmentValuesHeader.vue';
import { useVModel } from '@/composables/useVModel';
import WorksheetTableAdoptedApportionmentValuesItem from '@/components/rollMaintenance/ratingValuation/worksheet/WorksheetTableAdoptedApportionmentValuesItem.vue';
import { ValidationProvider } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Array,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    },
    parentValue: {
        type: Object,
        required: true,
    }
});
const emit = defineEmits(['input', 'changed', 'update:parentValue']);
const modelValue = useVModel(props, emit, 'value', { deep: true });

function parentValuesChanged(value) {
    emit('update:parentValue', value);
    emit('changed');
}
</script>

<template>
    <ValidationProvider :path="FIELDS.RATING_APPORTIONMENTS_ADOPTED_REVISION_VALUES" v-slot="{ hasErrors }">
        <BaseTable class="qv-table-striped">
            <WorksheetTableApportionmentValuesHeader />
            <BaseTableBody>
                <template v-for="(apportionmentValue, index) in modelValue">
                    <WorksheetTableAdoptedApportionmentValuesItem
                        :key="index"
                        :index="index"
                        :last="index === modelValue.length - 1"
                        :valuationReference='valuationReference + " " + apportionmentValue.suffix'
                        v-model="apportionmentValue.adoptedRevisionValue"
                        :validationPathCV="FIELDS.RATING_APPORTIONMENTS_ADOPTED_REVISION_CV"
                        :validationPathLV="FIELDS.RATING_APPORTIONMENTS_ADOPTED_REVISION_LV"
                        :validationPathVI="FIELDS.RATING_APPORTIONMENTS_ADOPTED_REVISION_VI"
                        :dataCyPrefix="'apportionment-adopted-revision'"
                    />
                </template>
                <WorksheetTableAdoptedApportionmentValuesItem
                    :valuationReference='valuationReference'
                    :value="parentValue"
                    @input="parentValuesChanged"
                    :hasErrors="hasErrors"
                    :validationPathCV="FIELDS.ADOPTED_REVISION_CV"
                    :validationPathLV="FIELDS.ADOPTED_REVISION_LV"
                    :validationPathVI="FIELDS.ADOPTED_REVISION_VI"
                    :dataCyPrefix="'parent-adopted-revision'"
                />
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

<script setup>
import {
    WorksheetTableLand,
    WorksheetTableOtherBuildings,
    WorksheetTableOtherImprovements,
    WorksheetTablePrimaryBuildings,
    WorksheetTableValues,
    WorksheetTableAdoptedValues,
    WorksheetTableRevisionValues,
    WorksheetTableAdoptedRevisionValues,
    WorksheetTableAdoptedApportionmentValues,
    WorksheetTableRevisionApportionmentValues,
    WorksheetTableAdoptedRevisionApportionmentValues,
    WorksheetTableSraValues,
} from './';
import { useValuationWorksheet, recalculateRevisionValues, recalculateApportionmentRevisionValues } from '@/composables/ratingValuation';
import { toRefs } from 'vue';
import { ValidationContext } from '@/components/ui/validation';
import { ValidationSet } from '@quotable-value/validation';

const emit = defineEmits(['update:value', 'changed']);
const props = defineProps({
    value: {
        type: Object,
        required: true,
    },
    property: {
        required: true,
    },
    propertyDetail: {
        required: true,
    },
    validationSet: {
        type: ValidationSet,
        required: false,
    },
});

const {
    value,
    property,
    propertyDetail,
    validationSet
} = toRefs(props);

const {
    land,
    primaryBuildings,
    otherBuildings,
    otherImprovements,
    worksheetValues,
    hasRevisionValue,
    adoptedRevisionValue,
    originalRevisionValue,
    maoriLandValues,
    addComponent,
    apportionmentValues,
    hasSraValues,
    hasSraRevision,
} = useValuationWorksheet(value, property, propertyDetail);

function onChanged() {
    emit('changed');
}

function recalculateNewRevisionValues() {
    this.adoptedRevisionValue = recalculateRevisionValues(this.value, this.property);

    this.apportionmentValues?.forEach((apportionmentValue) => {
        const currentApportionmentValues = this.propertyDetail.dvrSnapshot.ratingApportionments.find(
            oldValue =>
                oldValue.qpid === apportionmentValue.qpid
                && oldValue.suffix === apportionmentValue.suffix,
        );

        apportionmentValue = recalculateApportionmentRevisionValues(apportionmentValue, currentApportionmentValues);
    });
}
</script>

<template>
    <ValidationContext :validation-set="validationSet">
        <div class="qv-w-full" :key="value.id">
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Improvements</h2>
                <h4 class="qv-text-base qv-text-darkblue">Principal Buildings</h4>
                <WorksheetTablePrimaryBuildings v-model="primaryBuildings" class="qv-pb-2" @changed="onChanged"/>
                <h4 class="qv-text-base qv-text-darkblue">Other Buildings</h4>
                <WorksheetTableOtherBuildings v-model="otherBuildings" class="qv-pb-2" @changed="onChanged"/>
                <h4 class="qv-text-base qv-text-darkblue">Other Improvements</h4>
                <WorksheetTableOtherImprovements v-model="otherImprovements" class="qv-pb-2" @changed="onChanged"/>
            </div>
            <hr>
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Land</h2>
                <WorksheetTableLand v-model="land" class="qv-pb-2" @changed="onChanged"/>
            </div>
            <hr>
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Worksheet Values</h2>
                <WorksheetTableValues v-model="worksheetValues" class="qv-pb-2" @changed="onChanged" />
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Adopted Values</h2>
                <template v-if="apportionmentValues">
                    <WorksheetTableAdoptedApportionmentValues
                        v-model="apportionmentValues"
                        :valuationReference="property.valuationReference"
                        :parentValue="value.adoptedValue"
                        class="qv-pb-2"
                        @changed="onChanged"
                        @update:parentValue="value.adoptedValue = $event"
                    />
                </template>
                <template v-else>
                    <WorksheetTableAdoptedValues v-model="value.adoptedValue" class="qv-pb-2" @changed="onChanged" />
                </template>
                <template v-if="hasRevisionValue">
                    <hr>
                    <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Existing Revision Values</h2>
                    <template v-if="apportionmentValues">
                        <WorksheetTableRevisionApportionmentValues
                            v-model="apportionmentValues"
                            :valuationReference="property.valuationReference"
                            :parentValue="originalRevisionValue"
                            class="qv-pb-2"
                            @changed="onChanged"
                            @update:parentValue="originalRevisionValue = $event"
                        />
                    </template>
                    <template v-else>
                        <WorksheetTableRevisionValues v-model="originalRevisionValue" class="qv-pb-2" @changed="onChanged" />
                    </template>
                    <div class="qv-flex-row" style="justify-content: space-between">
                        <div>
                            <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">New Revision Values</h2>
                        </div>
                        <div>
                            <button
                                data-cy="recalculate-revision-values"
                                class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored qv-rolls-badge-parent btn-width-100"
                                @click="recalculateNewRevisionValues()"
                            >
                                Calculate Revision Values
                            </button>
                        </div>
                    </div>
                    <template v-if="apportionmentValues">
                        <WorksheetTableAdoptedRevisionApportionmentValues
                            v-model="apportionmentValues"
                            :valuationReference="property.valuationReference"
                            :parentValue="adoptedRevisionValue"
                            class="qv-pb-2"
                            @changed="onChanged"
                            @update:parentValue="adoptedRevisionValue = $event"
                        />
                    </template>
                    <template v-else>
                        <WorksheetTableAdoptedRevisionValues v-model="adoptedRevisionValue" class="qv-pb-2" @changed="onChanged" />
                    </template>
                </template>
                <WorksheetTableSraValues
                    v-if="hasSraValues"
                    v-model="value.sra.sras"
                    :hasSraRevision="hasSraRevision"
                    :qpid="value.ratingUnit.qpid"
                    class="qv-pb-2"
                    @changed="onChanged" />
            </div>
        </div>
    </ValidationContext>
</template>

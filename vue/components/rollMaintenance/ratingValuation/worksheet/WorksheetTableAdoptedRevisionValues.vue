<script setup>
import {
    BaseTable,
    BaseTableBody,
    WorksheetTableRow,
    BaseTableCell,
} from '@/components/ui/table';
import WorksheetTableValuesHeader from './WorksheetTableValuesHeader.vue';
import { calculateValueOfImprovement } from '@/composables/ratingValuation';
import { useVModel } from '@/composables/useVModel';
import { NumeralInput } from '@/components/ui/input';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Object,
        required: true,
    }
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
</script>

<template>
    <ValidationProvider :path="FIELDS.ADOPTED_REVISION_VALUES">
        <BaseTable>
            <WorksheetTableValuesHeader />
            <BaseTableBody>
                <WorksheetTableRow>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_REVISION_CV">
                            <NumeralInput preset="MONEY_POSITIVE" v-model="modelValue.capitalValue"
                                @input="calculateValueOfImprovement(modelValue)" data-cy="adopted-revision-capital-value"/>
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_REVISION_LV">
                            <NumeralInput preset="MONEY_POSITIVE" v-model="modelValue.landValue"
                                @input="calculateValueOfImprovement(modelValue)" data-cy="adopted-revision-land-value"/>
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_REVISION_VI">
                            <NumeralInput preset="MONEY" v-model="modelValue.valueOfImprovements"
                                :readonly="true" data-cy="adopted-revision-value-of-improvements"/>
                        </ValidationWrapper>
                    </BaseTableCell>
                </WorksheetTableRow>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

<script setup>
import { BaseTable, BaseTableBody, WorksheetTableRow, BaseTableCell } from '@/components/ui/table';
import WorksheetTableValuesHeader from './WorksheetTableValuesHeader.vue';
import { calculateValueOfImprovement } from '@/composables/ratingValuation';
import { useVModel } from '@/composables/useVModel';
import { NumeralInput } from '@/components/ui/input';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Object,
        required: true,
    },
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
</script>

<template>
    <ValidationProvider :path="FIELDS.ADOPTED_VALUES" v-slot="{ hasErrors }">
        <BaseTable>
            <WorksheetTableValuesHeader />
            <BaseTableBody>
                <WorksheetTableRow :errored="hasErrors">
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_CV">
                            <NumeralInput
                                v-model="modelValue.capitalValue"
                                data-cy="adopted-capital-value"
                                preset="MONEY_POSITIVE"
                                @input="calculateValueOfImprovement(modelValue)"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_LV">
                            <NumeralInput
                                v-model="modelValue.landValue"
                                data-cy="adopted-land-value"
                                preset="MONEY_POSITIVE"
                                @input="calculateValueOfImprovement(modelValue)"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_VI">
                            <NumeralInput
                                v-model="modelValue.valueOfImprovements"
                                :readonly="true"
                                data-cy="adopted-value-of-improvements"
                                preset="MONEY"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                </WorksheetTableRow>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

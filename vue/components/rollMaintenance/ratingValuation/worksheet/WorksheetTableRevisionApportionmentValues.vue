<script setup>
import { BaseTable, BaseTableBody } from '@/components/ui/table';
import WorksheetTableApportionmentValuesHeader from './WorksheetTableApportionmentValuesHeader.vue';
import { useVModel } from '@/composables/useVModel';
import WorksheetTableOriginalApportionmentValuesItem from '@/components/rollMaintenance/ratingValuation/worksheet/WorksheetTableOriginalApportionmentValuesItem.vue';
import { ValidationProvider } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Array,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    },
    parentValue: {
        type: Object,
        required: true,
    }
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });

</script>

<template>
    <ValidationProvider :path="FIELDS.RATING_APPORTIONMENTS_ORIGINAL_REVISION_VALUES">
        <BaseTable class="qv-table-striped">
            <WorksheetTableApportionmentValuesHeader />
            <BaseTableBody>
                <template v-for="(apportionmentValue, index) in modelValue">
                    <WorksheetTableOriginalApportionmentValuesItem
                        :last='index === modelValue.length - 1'
                        :valuationReference='valuationReference + " " + apportionmentValue.suffix'
                        v-model="apportionmentValue.originalRevisionValue"
                        :dataCyPrefix="'apportionment-original-revision'"
                    />
                </template>
                <WorksheetTableOriginalApportionmentValuesItem
                    :valuationReference='valuationReference'
                    :value="parentValue"
                    :dataCyPrefix="'parent-original-revision'"
                />
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

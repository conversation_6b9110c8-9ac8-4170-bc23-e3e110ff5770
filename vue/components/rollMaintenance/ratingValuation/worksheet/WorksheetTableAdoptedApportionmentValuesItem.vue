<script setup>
import { BaseTableCell } from '@/components/ui/table';
import { WorksheetTableRow } from '@/components/ui/table';
import { RatingValuation } from '@quotable-value/validation';
import { NumeralInput } from '@/components/ui/input';
import { useVModel } from '@/composables/useVModel';
import { calculateValueOfImprovement } from '@/composables/ratingValuation';
import { ValidationWrapper } from '@/components/ui/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Object,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    },
    last: {
        type: Boolean,
        default: false,
    },
    hasErrors: {
        type: Boolean,
        default: false,
    },
    validationPathCV: {
        type: String,
        required: true,
    },
    validationPathLV: {
        type: String,
        required: true,
    },
    validationPathVI: {
        type: String,
        required: true,
    },
    dataCyPrefix: {
        type: String,
        required: true,
    },
    index: {
        type: Number,
        required: false,
    },
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
</script>
<template>
    <WorksheetTableRow :last="last" :errored="hasErrors">
        <BaseTableCell class="qv-vertical-align-middle">
            <p class="qv-font-bold qv-text-md qv-color-darkblue">
                {{ valuationReference }}
            </p>
        </BaseTableCell>
        <BaseTableCell>
            <ValidationWrapper :index="index" :path="validationPathCV">
                <NumeralInput
                    v-model="modelValue.capitalValue"
                    :data-cy='dataCyPrefix + "-capital-value"'
                    preset="MONEY_POSITIVE"
                    @input="calculateValueOfImprovement(modelValue)"
                />
            </ValidationWrapper>
        </BaseTableCell>
        <BaseTableCell>
            <ValidationWrapper :index="index" :path="validationPathLV">
                <NumeralInput
                    v-model="modelValue.landValue"
                    :data-cy='dataCyPrefix + "-land-value"'
                    preset="MONEY_POSITIVE"
                    @input="calculateValueOfImprovement(modelValue)"
                />
            </ValidationWrapper>
        </BaseTableCell>
        <BaseTableCell>
            <ValidationWrapper :index="index" :path="validationPathVI">
                <NumeralInput
                    v-model="modelValue.valueOfImprovements"
                    :readonly="true"
                    :data-cy='dataCyPrefix + "-value-of-improvements"'
                    preset="MONEY"
                />
            </ValidationWrapper>
        </BaseTableCell>
    </WorksheetTableRow>
</template>

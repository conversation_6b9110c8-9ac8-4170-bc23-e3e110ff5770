<script setup>
import {
    BaseTable,
    BaseTableBody,
    WorksheetTableRow,
    BaseTableCell,
} from '@/components/ui/table';
import WorksheetTableValuesHeader from './WorksheetTableValuesHeader.vue';
import { useVModel } from '@/composables/useVModel';
import { NumeralInput } from '@/components/ui/input';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Object,
        required: true,
    }
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
</script>

<template>
    <ValidationProvider path="maoriLand">
        <BaseTable>
            <WorksheetTableValuesHeader/>
            <BaseTableBody>
                <WorksheetTableRow>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.UNADJUSTED_CAPITAL_VALUE">
                            <NumeralInput preset="MONEY_POSITIVE" v-model="modelValue.capitalValue" readonly data-cy="worksheet-capital-value" />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.UNADJUSTED_LAND_VALUE">
                            <NumeralInput preset="MONEY_POSITIVE" v-model="modelValue.landValue" readonly data-cy="worksheet-land-value" />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <NumeralInput preset="MONEY" v-model="modelValue.valueOfImprovements" readonly data-cy="worksheet-value-of-improvements" />
                    </BaseTableCell>
                </WorksheetTableRow>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

<style lang="scss" scoped></style>

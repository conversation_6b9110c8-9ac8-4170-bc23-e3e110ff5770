<script setup>
import { BaseTableCell } from '@/components/ui/table';
import { WorksheetTableRow } from '@/components/ui/table';
import { NumeralInput } from '@/components/ui/input';
import { useVModel } from '@/composables/useVModel';

const props = defineProps({
    value: {
        type: Object,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    },
    last: {
        type: Boolean,
        default: false,
    },
    dataCyPrefix: {
        type: String,
        required: true,
    }
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
</script>
<template>
    <WorksheetTableRow :last="last">
        <BaseTableCell class="qv-vertical-align-middle">
            <p class="qv-font-bold qv-text-md qv-color-darkblue">
                {{ valuationReference }}
            </p>
        </BaseTableCell>
        <BaseTableCell>
            <NumeralInput
                v-model="modelValue.capitalValue"
                :readonly="true"
                :data-cy='dataCyPrefix + "-capital-value"'
                preset="MONEY_POSITIVE"
            />
        </BaseTableCell>
        <BaseTableCell>
            <NumeralInput
                v-model="modelValue.landValue"
                :readonly="true"
                :data-cy='dataCyPrefix + "-land-value"'
                preset="MONEY_POSITIVE"
            />
        </BaseTableCell>
        <BaseTableCell>
            <NumeralInput
                v-model="modelValue.valueOfImprovements"
                :readonly="true"
                :data-cy='dataCyPrefix + "-value-of-improvements"'
                preset="MONEY"
            />
        </BaseTableCell>
    </WorksheetTableRow>
</template>

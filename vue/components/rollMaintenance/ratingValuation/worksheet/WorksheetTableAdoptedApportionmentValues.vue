<script setup>
import { BaseTable, BaseTableBody } from '@/components/ui/table';
import WorksheetTableApportionmentValuesHeader from './WorksheetTableApportionmentValuesHeader.vue';
import { useVModel } from '@/composables/useVModel';
import WorksheetTableAdoptedApportionmentValuesItem from '@/components/rollMaintenance/ratingValuation/worksheet/WorksheetTableAdoptedApportionmentValuesItem.vue';
import { ValidationProvider } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Array,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    },
    parentValue: {
        type: Object,
        required: true,
    }
});
const emit = defineEmits(['input', 'changed', 'update:parentValue']);
const modelValue = useVModel(props, emit, 'value', { deep: true });

function parentValuesChanged(value) {
    emit('update:parentValue', value);
    emit('changed');
}
</script>

<template>
    <ValidationProvider :path="FIELDS.ADOPTED_VALUES" v-slot="{ hasErrors }">
        <BaseTable class="qv-table-striped">
            <WorksheetTableApportionmentValuesHeader />
            <BaseTableBody>
                <template v-for="(apportionmentValue, index) in modelValue">
                    <WorksheetTableAdoptedApportionmentValuesItem
                        :key="index"
                        :index="index"
                        :last="index === modelValue.length - 1"
                        :valuationReference='valuationReference + " " + apportionmentValue.suffix'
                        v-model="apportionmentValue.adoptedValue"
                        :validationPathCV="FIELDS.RATING_APPORTIONMENTS_ADOPTED_CV"
                        :validationPathLV="FIELDS.RATING_APPORTIONMENTS_ADOPTED_LV"
                        :validationPathVI="FIELDS.RATING_APPORTIONMENTS_ADOPTED_VI"
                        :dataCyPrefix="'apportionment-adopted-value'"
                    />
                </template>
                <WorksheetTableAdoptedApportionmentValuesItem
                    :valuationReference='valuationReference'
                    :value="parentValue"
                    @input="parentValuesChanged"
                    :hasErrors="hasErrors"
                    :validationPathCV="FIELDS.ADOPTED_CV"
                    :validationPathLV="FIELDS.ADOPTED_LV"
                    :validationPathVI="FIELDS.ADOPTED_VI"
                    :dataCyPrefix="'parent-adopted-value'"
                />
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

<script setup>
import { BaseTable, BaseTableBody } from '@/components/ui/table';
import WorksheetTableMaoriApportionmentValuesHeader from './WorksheetTableMaoriApportionmentValuesHeader.vue';
import { useVModel } from '@/composables/useVModel';
import WorksheetTableMaoriOriginalApportionmentValuesItem from '@/components/rollMaintenance/ratingValuation/worksheet/maori/WorksheetTableMaoriOriginalApportionmentValuesItem.vue';
import { ValidationProvider } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';
import { formatPrice, formatPercentage } from '@/utils/FormatUtils';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Array,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    },
    unadjusted: {
        type: Object,
        required: true,
    },
    revision: {
        type: Object,
        required: true,
    },
    lumpSum: {
        type: Number,
        required: false,
    },
    adjustment: {
        type: Number,
        required: false,
    },
    isCurrentValue: {
        type: Boolean,
        default: false,
        required: false,
    }
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
</script>

<template>
    <BaseTable class="qv-table-striped">
        <WorksheetTableMaoriApportionmentValuesHeader />
        <BaseTableBody>
            <template v-for="(apportionmentValue, index) in modelValue">
                <WorksheetTableMaoriOriginalApportionmentValuesItem
                    :key="index + '-unadjusted'"
                    :isUnadjusted=true
                    :valuationReference='valuationReference + " " + apportionmentValue.suffix'
                    v-model="apportionmentValue.originalUnadjustedRevisionValue"
                    :dataCyPrefix="'apportionment-original-revision'"
                />
                <WorksheetTableMaoriOriginalApportionmentValuesItem
                    :key="index + '-adjusted'"
                    :last='index === modelValue.length - 1'
                    :valuationReference='valuationReference + " " + apportionmentValue.suffix'
                    v-model="apportionmentValue.originalRevisionValue"
                    :dataCyPrefix="'apportionment-original-revision'"
                />
            </template>
            <WorksheetTableMaoriOriginalApportionmentValuesItem
                :valuationReference='valuationReference'
                :isUnadjusted=true
                :value="unadjusted"
                :dataCyPrefix="'parent-original-revision'"
            />
            <WorksheetTableMaoriOriginalApportionmentValuesItem
                :valuationReference='valuationReference'
                :value="revision"
                :lumpSum = "lumpSum"
                :adjustment = "adjustment"
                :dataCyPrefix="'parent-original-revision'"
            />
        </BaseTableBody>
    </BaseTable>
</template>
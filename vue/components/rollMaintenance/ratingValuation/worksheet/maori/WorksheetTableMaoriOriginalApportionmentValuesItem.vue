<script setup>
import { BaseTable, BaseTableCell } from '@/components/ui/table';
import { WorksheetTableRow } from '@/components/ui/table';
import { NumeralInput } from '@/components/ui/input';
import { useVModel } from '@/composables/useVModel';
import { formatPrice, formatPercentage } from '@/utils/FormatUtils';

const props = defineProps({
    value: {
        type: Object,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    },
    last: {
        type: Boolean,
        default: false,
    },
    dataCyPrefix: {
        type: String,
        required: true,
    },
    isUnadjusted: {
        type: Boolean,
        default: false,
        required: false,
    },
    lumpSum: {
        type: Number,
        required: false,
    },
    adjustment: {
        type: Number,
        required: false,
    },
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
</script>
<template>
    <WorksheetTableRow :last="last">
        <BaseTableCell class="qv-vertical-align-middle">
            <p class="qv-font-bold">
                {{ valuationReference }}
            </p>
        </BaseTableCell>
        <BaseTableCell v-if="isUnadjusted" class="qv-vertical-align-middle">
            Unadjusted Revision Values
        </BaseTableCell>
        <BaseTableCell v-else class="qv-vertical-align-middle">
            Revision Values
        </BaseTableCell>
        <BaseTableCell v-if="lumpSum" class="qv-vertical-align-middle">
            {{ formatPrice(lumpSum) }}
        </BaseTableCell>
        <BaseTableCell v-else class="qv-vertical-align-middle">
        </BaseTableCell>
        <BaseTableCell v-if="adjustment" class="qv-vertical-align-middle">
            {{ formatPercentage(adjustment) }}
        </BaseTableCell>
        <BaseTableCell v-else class="qv-vertical-align-middle">
        </BaseTableCell>
        <BaseTableCell>
            <NumeralInput
                v-model="modelValue.capitalValue"
                :readonly="true"
                :data-cy='dataCyPrefix + "-capital-value"'
                preset="MONEY_POSITIVE"
            />
        </BaseTableCell>
        <BaseTableCell>
            <NumeralInput
                v-model="modelValue.landValue"
                :readonly="true"
                :data-cy='dataCyPrefix + "-land-value"'
                preset="MONEY_POSITIVE"
            />
        </BaseTableCell>
        <BaseTableCell>
            <NumeralInput
                v-model="modelValue.valueOfImprovements"
                :readonly="true"
                :data-cy='dataCyPrefix + "-value-of-improvements"'
                preset="MONEY"
            />
        </BaseTableCell>
    </WorksheetTableRow>
</template>

<script setup>
import { BaseTable, BaseTableBody, BaseTableCell, WorksheetTableRow } from '@/components/ui/table';
import { computed } from 'vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';
import WorksheetTableMaoriApportionmentValuesHeader from '@/components/rollMaintenance/ratingValuation/worksheet/maori/WorksheetTableMaoriApportionmentValuesHeader.vue';
import { formatPercentage, formatPrice } from '@/utils/FormatUtils';
import { NumeralInput } from '@/components/ui/input';

const { FIELDS } = RatingValuation;
const props = defineProps({
    apportionmentValues: {
        type: Array,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    },
    parentValue: {
        type: Object,
        required: true,
    },
});

const parentValues = computed(() => props.parentValue);
const emits = defineEmits(['update:parentValue', 'changed']);

function onParentValueChanged() {
    emits('update:parentValue', parentValues.value);
    emits('changed');
}

function updateApportionmentRatingCV(index, value) {
    props.apportionmentValues[index].adoptedValue.capitalValue = value;
    emits('changed');
}

function updateApportionmentRatingLV(index, value) {
    props.apportionmentValues[index].adoptedValue.landValue = value;
    emits('changed');
}

function updateApportionmentUnadjustedCV(index, value) {
    props.apportionmentValues[index].adoptedUnadjustedValue.capitalValue = value;
    props.apportionmentValues[index].adoptedUnadjustedValue.valueOfImprovements =
        props.apportionmentValues[index].adoptedUnadjustedValue.capitalValue
        - props.apportionmentValues[index].adoptedUnadjustedValue.landValue;
    props.apportionmentValues[index].adoptedValue.capitalValue = null;
    props.apportionmentValues[index].adoptedValue.landValue = null;
    emits('changed');
}

function updateApportionmentUnadjustedLV(index, value) {
    props.apportionmentValues[index].adoptedUnadjustedValue.landValue = value;
    props.apportionmentValues[index].adoptedUnadjustedValue.valueOfImprovements =
        props.apportionmentValues[index].adoptedUnadjustedValue.capitalValue
        - props.apportionmentValues[index].adoptedUnadjustedValue.landValue;
    props.apportionmentValues[index].adoptedValue.capitalValue = null;
    props.apportionmentValues[index].adoptedValue.landValue = null;
    emits('changed');
}

const apportionmentLumpSums = computed(() => {

    let totalApportionmentsLandValue = 0;
    props.apportionmentValues.forEach(a =>
        totalApportionmentsLandValue += a.adoptedUnadjustedValue.landValue
    )

    return props.apportionmentValues.map(a =>
        Math.round(props.parentValue.lumpSum * a.adoptedUnadjustedValue.landValue / totalApportionmentsLandValue)
    )
});

const apportionmentCalculatedRatingValues = computed(() =>
    props.apportionmentValues.map(
        (a, idx) => {
            const interimCV = a.adoptedUnadjustedValue.capitalValue - apportionmentLumpSums.value[idx];
            const interimLV = a.adoptedUnadjustedValue.landValue - apportionmentLumpSums.value[idx];
            const interimVI = interimCV - interimLV;

            const totalAdjustmentPctn = props.parentValue.adjustment;

            const unroundedAdjustedLV = interimLV - (interimLV * totalAdjustmentPctn / 100);
            const unroundedAdjustedVI = interimVI - (interimVI * totalAdjustmentPctn / 100);

            let adjustedLV = roundByMaoriLandRounding(unroundedAdjustedLV);
            let adjustedVI = roundByMaoriLandRounding(unroundedAdjustedVI);
            let adjustedCV = adjustedLV + adjustedVI;

            // apply minimum rule
            if (a.adoptedUnadjustedValue.capitalValue === 0) {
                adjustedCV = 0;
            }
            if (a.adoptedUnadjustedValue.landValue === 0) {
                adjustedLV = 0;
            }
            if (a.adoptedUnadjustedValue.capitalValue > 0 && adjustedCV < 100) {
                adjustedCV = 100;
            }
            if (a.adoptedUnadjustedValue.landValue > 0 && adjustedLV < 100) {
                adjustedLV = 100;
            }

            return {
                capitalValue : adjustedCV,
                landValue : adjustedLV,
                valueOfImprovements : adjustedCV - adjustedLV
            }
        }
    )
);

const apportionmentUsedRatingValues = computed(() =>
    props.apportionmentValues.map(
        (a, index) => {
            let usedCV = a.adoptedValue?.capitalValue;
            let usedLV = a.adoptedValue?.landValue;
            let usedVI = a.adoptedValue?.valueOfImprovements;

            if (usedCV === null) {
                usedCV = apportionmentCalculatedRatingValues.value[index].capitalValue;
            }
            if (usedLV === null) {
                usedLV = apportionmentCalculatedRatingValues.value[index].landValue;
            }

            if (usedCV !== null && usedLV !== null) {
                usedVI = usedCV - usedLV;
            }

            a.adoptedValue.capitalValue = usedCV;
            a.adoptedValue.landValue = usedLV;
            a.adoptedValue.valueOfImprovements = usedVI;

            return a.adoptedValue;
        }
    )
);

function roundByMaoriLandRounding(value) {
    // re-implements over-engineered Maori-specific rounding by TA, where all TAs use the same rules
    // this way we don't need another api call to back end, and these apis for the sake of getting 8 numbers that
    // will most likely never change, nor have TA-specific deviation from the norm.

    let roundingAmount;
    switch (true) {
        case (value >= 0 && value <= 500):
            roundingAmount = 50;
            break;
        case (value > 500 && value <= 1000):
            roundingAmount = 100;
            break;
        case (value > 1000 && value <= 10000):
            roundingAmount = 500;
            break;
        default:
            roundingAmount = 1000;
    }
    return Math.round(value / roundingAmount) * roundingAmount;
}

</script>

<template>
    <ValidationProvider path="apportionment">
        <BaseTable data-cy="worksheet-table-maori-adopted-values" class="qv-table-striped">
            <WorksheetTableMaoriApportionmentValuesHeader />
            <BaseTableBody>
                <template v-for="(apportionmentValue, index) in apportionmentValues">
                    <WorksheetTableRow data-cy="unadjusted-values">
                        <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                            {{ valuationReference }} {{ apportionmentValue.suffix }}
                        </BaseTableCell>
                        <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                            Unadjusted Values
                        </BaseTableCell>
                        <BaseTableCell>
                        </BaseTableCell>
                        <BaseTableCell>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_UNADJUSTED_CV">
                                <NumeralInput
                                    :value="apportionmentValue.adoptedUnadjustedValue.capitalValue"
                                    data-cy="unadjusted-capital-value"
                                    path="unadjustedValue.capitalValue"
                                    preset="MONEY_POSITIVE"
                                    @input="updateApportionmentUnadjustedCV(index,$event)"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_UNADJUSTED_LV">
                                <NumeralInput
                                    :value="apportionmentValue.adoptedUnadjustedValue.landValue"
                                    data-cy="unadjusted-land-value"
                                    path="unadjustedValue.landValue"
                                    preset="MONEY_POSITIVE"
                                    @input="updateApportionmentUnadjustedLV(index,$event)"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <NumeralInput
                                :value="apportionmentValue.adoptedUnadjustedValue.valueOfImprovements"
                                :readonly="true"
                                data-cy="unadjusted-value-of-improvements"
                                path="unadjustedValue.valueOfImprovements"
                                preset="MONEY"
                            />
                        </BaseTableCell>
                    </WorksheetTableRow>
                    <WorksheetTableRow data-cy="rating-values" :last="index === apportionmentValues.length - 1">
                        <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                            {{ valuationReference }} {{ apportionmentValue.suffix}}
                        </BaseTableCell>
                        <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                            Rating Values
                        </BaseTableCell>
                        <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="lump-sum">
                            <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_LUMP_SUM">
                                {{ formatPrice(apportionmentLumpSums[index]) }}
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="adjustment">
                            {{ formatPercentage(parentValue.adjustment) }}
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_ADOPTED_CV">
                                <NumeralInput
                                    :value="apportionmentUsedRatingValues[index].capitalValue"
                                    data-cy="rating-capital-value"
                                    preset="MONEY_POSITIVE"
                                    @input="updateApportionmentRatingCV(index,$event)"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_ADOPTED_LV">
                                <NumeralInput
                                    :value="apportionmentUsedRatingValues[index].landValue"
                                    data-cy="rating-land-value"
                                    preset="MONEY_POSITIVE"
                                    @input="updateApportionmentRatingLV(index,$event)"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.RATING_APPORTIONMENTS_ADOPTED_VI">
                                <NumeralInput
                                    :value="apportionmentUsedRatingValues[index].valueOfImprovements"
                                    :readonly="true"
                                    data-cy="rating-value-of-improvements"
                                    preset="MONEY"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                    </WorksheetTableRow>
                </template>
                <WorksheetTableRow data-cy="unadjusted-values">
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                        {{ valuationReference }}
                    </BaseTableCell>
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                        Unadjusted Values
                    </BaseTableCell>
                    <BaseTableCell>
                    </BaseTableCell>
                    <BaseTableCell>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.UNADJUSTED_CV">
                            <NumeralInput
                                v-model="parentValues.unadjusted.capitalValue"
                                data-cy="unadjusted-capital-value"
                                preset="MONEY_POSITIVE"
                                @input="onParentValueChanged"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.UNADJUSTED_LV">
                            <NumeralInput
                                v-model="parentValues.unadjusted.landValue"
                                data-cy="unadjusted-land-value"
                                preset="MONEY_POSITIVE"
                                @input="onParentValueChanged"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <NumeralInput
                            v-model="parentValues.unadjusted.valueOfImprovements"
                            :readonly="true"
                            data-cy="unadjusted-value-of-improvements"
                            path="unadjustedValue.valueOfImprovements"
                            preset="MONEY"
                        />
                    </BaseTableCell>
                </WorksheetTableRow>
                <WorksheetTableRow data-cy="rating-values">
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                        {{ valuationReference }}
                    </BaseTableCell>
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                        Rating Values
                    </BaseTableCell>
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="lump-sum">
                        <ValidationWrapper :path="FIELDS.LUMP_SUM">
                            {{ formatPrice(parentValues.lumpSum) }}
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="adjustment">
                        {{ formatPercentage(parentValues.adjustment) }}
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_CV">
                            <NumeralInput
                                v-model="parentValues.rating.capitalValue"
                                data-cy="rating-capital-value"
                                preset="MONEY_POSITIVE"
                                @input="onParentValueChanged"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_LV">
                            <NumeralInput
                                v-model="parentValues.rating.landValue"
                                data-cy="rating-land-value"
                                preset="MONEY_POSITIVE"
                                @input="onParentValueChanged"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADOPTED_VI">
                            <NumeralInput
                                v-model="parentValues.rating.valueOfImprovements"
                                :readonly="true"
                                data-cy="rating-value-of-improvements"
                                preset="MONEY"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                </WorksheetTableRow>
                <slot />
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

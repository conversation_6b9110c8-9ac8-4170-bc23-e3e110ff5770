<script setup>
import { BaseTable, BaseTableBody, BaseTableCell, BaseTableHead, WorksheetTableHeader, WorksheetTableRow } from '@/components/ui/table';
import { computed } from 'vue';
import { formatPrice, formatPercentage } from '@/utils/FormatUtils';
import { NumeralInput, NUMERAL_PRESET } from '@/components/ui/input';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { MONEY_POSITIVE } = NUMERAL_PRESET;
const { FIELDS } = RatingValuation;
const props = defineProps({
    unadjusted: {
        type: Object,
        required: true,
    },
    rating: {
        type: Object,
        required: true,
    },
    lumpSum: {
        type: Number,
        required: false,
    },
    adjustment: {
        type: Number,
        required: false,
    },
});

const unadjustedValues = computed(() => props.unadjusted);
const ratingValues = computed(() => props.rating);
const emits = defineEmits(['update:unadjusted', 'update:rating', 'changed']);

function onRatingChanged() {
    emits('update:rating', ratingValues.value);
    emits('changed');
}

function onUnadjustedChanged() {
    emits('update:unadjusted', unadjustedValues.value);
    emits('changed');
}
</script>

<template>
    <ValidationProvider path="maoriLand">
        <BaseTable data-cy="worksheet-table-maori-adopted-values">
        <WorksheetTableHeader>
            <BaseTableHead aria-label="titles" />
            <BaseTableHead>
                Lump Sum
            </BaseTableHead>
            <BaseTableHead>
                Adjustment
            </BaseTableHead>
            <BaseTableHead style="width: 20%">
                Capital value
            </BaseTableHead>
            <BaseTableHead style="width: 20%">
                Land value
            </BaseTableHead>
            <BaseTableHead style="width: 20%">
                Value of Improvements
            </BaseTableHead>
        </WorksheetTableHeader>
        <BaseTableBody>
            <WorksheetTableRow data-cy="unadjusted-values">
                <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                    Unadjusted Values
                </BaseTableCell>
                <BaseTableCell>
                </BaseTableCell>
                <BaseTableCell>
                </BaseTableCell>
                <BaseTableCell>
                    <ValidationWrapper :path="FIELDS.UNADJUSTED_CV">
                        <NumeralInput
                            v-model="unadjustedValues.capitalValue"
                            data-cy="unadjusted-capital-value"
                            path="unadjustedValue.capitalValue"
                            :preset="MONEY_POSITIVE"
                            @input="onUnadjustedChanged"
                        />
                    </ValidationWrapper>
                </BaseTableCell>
                <BaseTableCell>
                    <ValidationWrapper :path="FIELDS.UNADJUSTED_LV">
                        <NumeralInput
                            v-model="unadjustedValues.landValue"
                            data-cy="unadjusted-land-value"
                            path="unadjustedValue.landValue"
                            :preset="MONEY_POSITIVE"
                            @input="onUnadjustedChanged"
                        />
                    </ValidationWrapper>
                </BaseTableCell>
                <BaseTableCell>
                    <NumeralInput
                        v-model="unadjustedValues.valueOfImprovements"
                        :readonly="true"
                        data-cy="unadjusted-value-of-improvements"
                        path="unadjustedValue.valueOfImprovements"
                        :preset="MONEY_POSITIVE"
                    />
                </BaseTableCell>
            </WorksheetTableRow>
            <WorksheetTableRow data-cy="rating-values">
                <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                    Rating Values
                </BaseTableCell>
                <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="lump-sum">
                    <ValidationWrapper :path="FIELDS.LUMP_SUM">
                        {{ formatPrice(lumpSum) }}
                    </ValidationWrapper>
                </BaseTableCell>
                <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="adjustment">{{ formatPercentage(adjustment) }}</BaseTableCell>
                <BaseTableCell>
                    <ValidationWrapper :path="FIELDS.ADJUSTED_CAPITAL_VALUE">
                        <NumeralInput
                            v-model="rating.capitalValue"
                            data-cy="rating-capital-value"
                            path="adoptedValue.capitalValue"
                            :preset="MONEY_POSITIVE"
                            @input="onRatingChanged"
                        />
                    </ValidationWrapper>

                </BaseTableCell>
                <BaseTableCell>
                    <NumeralInput
                        v-model="rating.landValue"
                        data-cy="rating-land-value"
                        path="adoptedValue.landValue"
                        :preset="MONEY_POSITIVE"
                        @input="onRatingChanged"
                    />
                </BaseTableCell>
                <BaseTableCell>
                    <NumeralInput
                        v-model="rating.valueOfImprovements"
                        :readonly="true"
                        data-cy="rating-value-of-improvements"
                        path="adoptedValue.valueOfImprovements"
                        :preset="MONEY_POSITIVE"
                    />
                </BaseTableCell>
            </WorksheetTableRow>
            <slot />
        </BaseTableBody>
    </BaseTable>
    </ValidationProvider>
</template>

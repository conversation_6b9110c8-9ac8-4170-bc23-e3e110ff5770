<script setup>
import InputNumber from 'Common/form/InputNumber.vue';
import { BaseTable, BaseTableBody, BaseTableCell, BaseTableHead, WorksheetTableHeader, WorksheetTableRow } from '@/components/ui/table';
import { computed } from 'vue';
import { formatPrice, formatPercentage } from '@/utils/FormatUtils';

const props = defineProps({
    unadjusted: {
        type: Object,
        required: true,
    },
    revision: {
        type: Object,
        required: true,
    },
    lumpSum: {
        type: Number,
        required: false,
    },
    adjustment: {
        type: Number,
        required: false,
    },
});

const unadjustedValues = computed(() => props.unadjusted);
const revisionValues = computed(() => props.revision);
</script>

<template>
    <BaseTable data-cy="worksheet-table-maori-revision-values">
        <WorksheetTableHeader>
            <BaseTableHead aria-label="titles"/>
            <BaseTableHead>
                Lump Sum
            </BaseTableHead>
            <BaseTableHead>
                Adjustment
            </BaseTableHead>
            <BaseTableHead style="width: 20%">
                Capital value
            </BaseTableHead>
            <BaseTableHead style="width: 20%">
                Land value
            </BaseTableHead>
            <BaseTableHead style="width: 20%">
                Value of Improvements
            </BaseTableHead>
        </WorksheetTableHeader>
        <BaseTableBody>
            <WorksheetTableRow data-cy="unadjusted-values">
                <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                    Unadjusted Revision Values
                </BaseTableCell>
                <BaseTableCell>
                </BaseTableCell>
                <BaseTableCell>
                </BaseTableCell>
                <BaseTableCell>
                    <InputNumber format="$0,0" v-model="unadjustedValues.capitalValue" :display-empty="true" :readonly="true" data-cy="unadjusted-capital-value"/>
                </BaseTableCell>
                <BaseTableCell>
                    <InputNumber format="$0,0" v-model="unadjustedValues.landValue" :display-empty="true" :readonly="true" data-cy="unadjusted-land-value"/>
                </BaseTableCell>
                <BaseTableCell>
                    <InputNumber format="$0,0" v-model="unadjustedValues.valueOfImprovements" :display-empty="true" :readonly="true" data-cy="unadjusted-value-of-improvements" />
                </BaseTableCell>
            </WorksheetTableRow>
            <WorksheetTableRow data-cy="revision-values">
                <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                    Revision Values
                </BaseTableCell>
                <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="lump-sum">{{ formatPrice(lumpSum) }}</BaseTableCell>
                <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="adjustment">{{ formatPercentage(adjustment) }}</BaseTableCell>
                <BaseTableCell>
                    <InputNumber format="$0,0" v-model="revisionValues.capitalValue" :display-empty="true" :readonly="true" data-cy="revision-capital-value" />
                </BaseTableCell>
                <BaseTableCell>
                    <InputNumber format="$0,0" v-model="revisionValues.landValue" :display-empty="true" :readonly="true" data-cy="revision-land-value" />
                </BaseTableCell>
                <BaseTableCell>
                    <InputNumber format="$0,0" v-model="revisionValues.valueOfImprovements" :display-empty="true" :readonly="true" data-cy="revision-value-of-improvements" />
                </BaseTableCell>
            </WorksheetTableRow>
            <slot/>
        </BaseTableBody>
    </BaseTable>
</template>

<script setup>
import InputNumber from 'Common/form/InputNumber.vue';
import { BaseTable, BaseTableBody, BaseTableCell, BaseTableHead, WorksheetTableHeader, WorksheetTableRow } from '@/components/ui/table';
import { computed } from 'vue';
import { formatPrice, formatPercentage } from '@/utils/FormatUtils';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    unadjusted: {
        type: Object,
        required: true,
    },
    revision: {
        type: Object,
        required: true,
    },
    lumpSum: {
        type: Number,
        required: false,
    },
    adjustment: {
        type: Number,
        required: false,
    },
});

const unadjustedValues = computed(() => props.unadjusted);
const revisionValues = computed(() => props.revision);
const emits = defineEmits(['update:unadjusted', 'update:revision', 'changed']);

function onRevisionChanged() {
    emits('update:revision', revisionValues.value);
    emits('changed');
}

function onUnadjustedChanged() {
    emits('update:unadjusted', unadjustedValues.value);
    emits('changed');
}
</script>

<template>
    <ValidationProvider path="maoriLand">
        <BaseTable data-cy="worksheet-table-maori-adopted-revision-values">
            <WorksheetTableHeader>
                <BaseTableHead aria-label="titles"/>
                <BaseTableHead>
                    Lump Sum
                </BaseTableHead>
                <BaseTableHead>
                    Adjustment
                </BaseTableHead>
                <BaseTableHead style="width: 20%">
                    Capital value
                </BaseTableHead>
                <BaseTableHead style="width: 20%">
                    Land value
                </BaseTableHead>
                <BaseTableHead style="width: 20%">
                    Value of Improvements
                </BaseTableHead>
            </WorksheetTableHeader>
            <BaseTableBody>
                <WorksheetTableRow data-cy="adopted-unadjusted-values">
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                        Unadjusted Revision Values
                    </BaseTableCell>
                    <BaseTableCell>
                    </BaseTableCell>
                    <BaseTableCell>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.UNADJUSTED_REVISION_CAPITAL_VALUE">
                            <InputNumber round format="$0,0" v-model="unadjustedValues.capitalValue" :display-empty="true" data-cy="unadjusted-capital-value" @input="onUnadjustedChanged"/>
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.UNADJUSTED_REVISION_LAND_VALUE">
                            <InputNumber round format="$0,0" v-model="unadjustedValues.landValue" :display-empty="true" data-cy="unadjusted-land-value" @input="onUnadjustedChanged"/>
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <InputNumber format="$0,0" v-model="unadjustedValues.valueOfImprovements" :display-empty="true" :readonly="true" data-cy="unadjusted-value-of-improvements" />
                    </BaseTableCell>
                </WorksheetTableRow>
                <WorksheetTableRow data-cy="adopted-revision-values">
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                        Revision Values
                    </BaseTableCell>
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="lump-sum">{{ formatPrice(lumpSum) }}</BaseTableCell>
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle" data-cy="adjustment">{{ formatPercentage(adjustment) }}</BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.ADJUSTED_REVISION_CAPITAL_VALUE">
                            <InputNumber format="$0,0" v-model="revisionValues.capitalValue" :display-empty="true" :readonly="true" data-cy="revision-capital-value" @input="onRevisionChanged"/>
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <InputNumber format="$0,0" v-model="revisionValues.landValue" :display-empty="true" :readonly="true" data-cy="revision-land-value" @input="onRevisionChanged"/>
                    </BaseTableCell>
                    <BaseTableCell>
                        <InputNumber format="$0,0" v-model="revisionValues.valueOfImprovements" :display-empty="true" :readonly="true" data-cy="revision-value-of-improvements" />
                    </BaseTableCell>
                </WorksheetTableRow>
                <slot/>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

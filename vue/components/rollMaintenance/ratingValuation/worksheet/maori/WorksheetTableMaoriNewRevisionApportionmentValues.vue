<script setup>
import { BaseTable, BaseTableCell, BaseTableBody, WorksheetTableRow } from '@/components/ui/table';
import { NumeralInput } from '@/components/ui/input';
import WorksheetTableMaoriApportionmentValuesHeader from './WorksheetTableMaoriApportionmentValuesHeader.vue';
import { useVModel } from '@/composables/useVModel';
import WorksheetTableMaoriOriginalApportionmentValuesItem from '@/components/rollMaintenance/ratingValuation/worksheet/maori/WorksheetTableMaoriOriginalApportionmentValuesItem.vue';
import { RatingValuation } from '@quotable-value/validation';
import { formatPrice, formatPercentage } from '@/utils/FormatUtils';
import { calculateValueOfImprovement } from '@/composables/ratingValuation';
import { computed, watchEffect, onMounted, ref } from 'vue';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';

const { FIELDS } = RatingValuation;

const isLoading = ref(true);

const props = defineProps({
    parentValue: {
        type: Object,
        required: true,
    },
    apportionmentValues: {
        type: Array,
        required: true,
    },
    valuationReference: {
        type: String,
        required: true,
    }
});

const parentValues = computed(() => {
    return props.parentValue
});

watchEffect(() => {
    if (props.apportionmentValues?.length > 0) {
        isLoading.value = false;
    }
});

const emits = defineEmits(['update:parentValue', 'input', 'changed']);

function onParentValueChanged() {
    emits('update:parentValue', parentValues.value);
    emits('changed');
}

function updateApportionmentUnadjustedCV(index, value) {
    props.apportionmentValues[index].unadjustedRevisionValue.capitalValue = value;
    props.apportionmentValues[index].adoptedRevisionValue.capitalValue = null;
    props.apportionmentValues[index].adoptedRevisionValue.landValue = null;
    emits('changed');
}

function updateApportionmentUnadjustedLV(index, value) {
    props.apportionmentValues[index].unadjustedRevisionValue.landValue = value;
    props.apportionmentValues[index].adoptedRevisionValue.capitalValue = null;
    props.apportionmentValues[index].adoptedRevisionValue.landValue = null;
    emits('changed');
}

function updateApportionmentAdjustedRatingCV(index, value) {
    props.apportionmentValues[index].adoptedRevisionValue.capitalValue = value;
    emits('changed');
}

function updateApportionmentAdjustedRatingLV(index, value) {
    props.apportionmentValues[index].adoptedRevisionValue.landValue = value;
    emits('changed');
}

const apportionmentLumpSums = computed(() =>
    props.apportionmentValues.map(a =>
        Math.round(props.parentValue.lumpSum * a.unadjustedRevisionValue.landValue / parentValues.value.unadjusted.landValue)
    )
);
const parentCalculatedRatingValues = computed(() => {
        const interimCV = parentValues.value.unadjusted.capitalValue - parentValues.value.lumpSum;
        const interimLV = parentValues.value.unadjusted.landValue - parentValues.value.lumpSum;
        const interimVI = interimCV - interimLV;

        const totalAdjustmentPctn = parentValues.value.adjustment;

        const unroundedAdjustedLV = interimLV - (interimLV * totalAdjustmentPctn / 100);
        const unroundedAdjustedVI = interimVI - (interimVI * totalAdjustmentPctn / 100);

        let adjustedLV = roundByMaoriLandRounding(unroundedAdjustedLV);
        let adjustedVI = roundByMaoriLandRounding(unroundedAdjustedVI);
        let adjustedCV = adjustedLV + adjustedVI;

        if (parentValues.value.unadjusted.capitalValue === 0) {
            adjustedCV = 0;
        }
        if (parentValues.value.unadjusted.landValue === 0) {
            adjustedLV = 0;
        }
        if (parentValues.value.unadjusted.capitalValue > 0 && adjustedCV < 100) {
            adjustedCV = 100;
        }
        if (parentValues.value.unadjusted.landValue && adjustedLV < 100) {
            adjustedLV = 100;
        }

        return {
            capitalValue : adjustedCV,
            landValue : adjustedLV,
            valueOfImprovements : adjustedCV - adjustedLV
        }
    }
);

const parentAdjustedRevisonRatingValues = computed(() => {
        let usedCV = parentValues.value.revision?.capitalValue;
        let usedLV = parentValues.value.revision?.landValue;
        let usedVI = parentValues.value.revision?.valueOfImprovements;

        if (!isLoading.value || usedCV !== null || usedCV !== undefined) {
            usedCV = parentCalculatedRatingValues.value.capitalValue;
        }

        if (!isLoading.value || usedLV !== null || usedLV !== undefined) {
            usedLV = parentCalculatedRatingValues.value.landValue;
        }

        if (usedCV !== null && usedLV !== null && usedCV !== undefined && usedLV !== undefined) {
            usedVI = usedCV - usedLV;
        }

        parentValues.value.revision.capitalValue = usedCV;
        parentValues.value.revision.landValue = usedLV;
        parentValues.value.revision.valueOfImprovements = usedVI;

        return parentValues.value.revision;
    }
);

const apportionmentCalculatedRatingValues = computed(() =>
    props.apportionmentValues.map(
        (a, idx) => {
            const interimCV = a.unadjustedRevisionValue.capitalValue - apportionmentLumpSums.value[idx];
            const interimLV = a.unadjustedRevisionValue.landValue - apportionmentLumpSums.value[idx];
            const interimVI = interimCV - interimLV;

            const totalAdjustmentPctn = props.parentValue.adjustment;

            const unroundedAdjustedLV = interimLV - (interimLV * totalAdjustmentPctn / 100);
            const unroundedAdjustedVI = interimVI - (interimVI * totalAdjustmentPctn / 100);

            let adjustedLV = roundByMaoriLandRounding(unroundedAdjustedLV);
            let adjustedVI = roundByMaoriLandRounding(unroundedAdjustedVI);
            let adjustedCV = adjustedLV + adjustedVI;

            if (a.unadjustedRevisionValue.capitalValue === 0) {
                adjustedCV = 0;
            }
            if (a.unadjustedRevisionValue.landValue === 0) {
                adjustedLV = 0;
            }
            if (a.unadjustedRevisionValue.capitalValue > 0 && adjustedCV < 100) {
                adjustedCV = 100;
            }
            if (a.unadjustedRevisionValue.landValue > 0 && adjustedLV < 100) {
                adjustedLV = 100;
            }

            return {
                capitalValue : adjustedCV,
                landValue : adjustedLV,
                valueOfImprovements : adjustedCV - adjustedLV
            }
        }
    )
);

const apportionmentAdjustedRevisionRatingValues = computed(() =>
    props.apportionmentValues.map(
        (a, index) => {
            let usedCV = a.adoptedRevisionValue?.capitalValue;
            let usedLV = a.adoptedRevisionValue?.landValue;
            let usedVI = a.adoptedRevisionValue?.valueOfImprovements;

            if (!isLoading.value || usedCV !== null || usedCV !== undefined) {
                usedCV = apportionmentCalculatedRatingValues.value[index].capitalValue;
            }
            if (!isLoading.value || usedLV !== null || usedLV !== undefined) {
                usedLV = apportionmentCalculatedRatingValues.value[index].landValue;
            }

            if (usedCV !== null && usedLV !== null && usedCV !== undefined && usedLV !== undefined) {
                usedVI = usedCV - usedLV;
            }

            a.adoptedRevisionValue.capitalValue = usedCV;
            a.adoptedRevisionValue.landValue = usedLV;
            a.adoptedRevisionValue.valueOfImprovements = usedVI;

            return a.adoptedRevisionValue;
        }
    )
);

function roundByMaoriLandRounding(value) {
    let roundingAmount;
    switch (true) {
        case (value >= 0 && value <= 500):
            roundingAmount = 50;
            break;
        case (value > 500 && value <= 1000):
            roundingAmount = 100;
            break;
        case (value > 1000 && value <= 10000):
            roundingAmount = 500;
            break;
        default:
            roundingAmount = 1000;
    }
    return Math.round(value / roundingAmount) * roundingAmount;
}

onMounted(() => {

});
</script>

<template>
    <ValidationProvider path="apportionment">
        <BaseTable class="qv-table-striped" data-cy="worksheet-table-maori-adopted-revision-apportionment-values">
            <WorksheetTableMaoriApportionmentValuesHeader />
            <BaseTableBody>
                <template v-for="(apportionmentValue, index) in apportionmentValues">
                    <!-- Apportionment unadjusted values -->
                    <template>
                        <WorksheetTableRow :key="index + '-unadjusted'" data-cy="unadjusted-revision-values">
                            <BaseTableCell class="qv-vertical-align-middle">
                                <p class="qv-font-bold">
                                    {{ valuationReference + " " + apportionmentValue.suffix }}
                                </p>
                            </BaseTableCell>
                            <BaseTableCell class="qv-vertical-align-middle">
                                Unadjusted Revision Values
                            </BaseTableCell>
                            <BaseTableCell class="qv-vertical-align-middle">
                            </BaseTableCell>
                            <BaseTableCell class="qv-vertical-align-middle">
                            </BaseTableCell>
                            <BaseTableCell>
                                <NumeralInput
                                    :value="apportionmentValue.unadjustedRevisionValue.capitalValue"
                                    preset="MONEY_POSITIVE"
                                    :min="0"
                                    data-cy="unadjusted-revision-cv"
                                    @input="updateApportionmentUnadjustedCV(index,$event)"
                                />
                            </BaseTableCell>
                            <BaseTableCell>
                                <NumeralInput
                                    :value="apportionmentValue.unadjustedRevisionValue.landValue"
                                    preset="MONEY_POSITIVE"
                                    :min="0"
                                    data-cy="unadjusted-revision-lv"
                                    @input="updateApportionmentUnadjustedLV(index,$event)"
                                />
                            </BaseTableCell>
                            <BaseTableCell>
                                <NumeralInput
                                    :value="apportionmentValue.unadjustedRevisionValue.valueOfImprovements"
                                    :readonly="true"
                                    preset="MONEY"
                                    data-cy="unadjusted-revision-vi"
                                />
                            </BaseTableCell>
                        </WorksheetTableRow>
                    </template>
                    <!-- Apportionment adjusted values -->
                    <template>
                        <WorksheetTableRow :key="index + '-adjusted'" :last="index === apportionmentValues.length - 1" data-cy="revision-values">
                            <BaseTableCell class="qv-vertical-align-middle">
                                <p class="qv-font-bold">
                                    {{ valuationReference + " " + apportionmentValue.suffix }}
                                </p>
                            </BaseTableCell>
                            <BaseTableCell class="qv-vertical-align-middle">
                                Revision Values
                            </BaseTableCell>
                            <BaseTableCell class="qv-vertical-align-middle" data-cy="lump-sum">
                                {{ formatPrice(apportionmentLumpSums[index]) }}
                            </BaseTableCell>
                            <BaseTableCell class="qv-vertical-align-middle" data-cy="adjustment">
                                {{ formatPercentage(parentValue.adjustment) }}
                            </BaseTableCell>
                            <BaseTableCell>
                                <NumeralInput
                                    :value="apportionmentAdjustedRevisionRatingValues[index].capitalValue"
                                    preset="MONEY_POSITIVE"
                                    :min="0"
                                    data-cy="revision-cv"
                                    @input="updateApportionmentAdjustedRatingCV(index,$event)"
                                />
                            </BaseTableCell>
                            <BaseTableCell>
                                <NumeralInput
                                    :value="apportionmentAdjustedRevisionRatingValues[index].landValue"
                                    preset="MONEY_POSITIVE"
                                    :min="0"
                                    data-cy="revision-lv"
                                    @input="updateApportionmentAdjustedRatingLV(index,$event)"
                                />
                            </BaseTableCell>
                            <BaseTableCell>
                                <NumeralInput
                                    :value="apportionmentAdjustedRevisionRatingValues[index].valueOfImprovements"
                                    :readonly="true"
                                    data-cy="revision-vi"
                                    preset="MONEY"
                                />
                            </BaseTableCell>
                        </WorksheetTableRow>
                    </template>
                </template>
                <!-- Parent unadjusted values -->
                <template>
                    <WorksheetTableRow>
                        <BaseTableCell class="qv-vertical-align-middle">
                            <p class="qv-font-bold">
                                {{ valuationReference }}
                            </p>
                        </BaseTableCell>
                        <BaseTableCell class="qv-vertical-align-middle">
                            Unadjusted Revision Values
                        </BaseTableCell>
                        <BaseTableCell class="qv-vertical-align-middle">
                        </BaseTableCell>
                        <BaseTableCell class="qv-vertical-align-middle">
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.UNADJUSTED_REVISION_CAPITAL_VALUE">
                                <NumeralInput
                                    v-model="parentValues.unadjusted.capitalValue"
                                    preset="MONEY_POSITIVE"
                                    :min="0"
                                    @input="onParentValueChanged"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.UNADJUSTED_REVISION_LAND_VALUE">
                                <NumeralInput
                                    v-model="parentValues.unadjusted.landValue"
                                    preset="MONEY_POSITIVE"
                                    :min="0"
                                    @input="onParentValueChanged"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <NumeralInput
                                v-model="parentValue.unadjusted.valueOfImprovements"
                                :readonly="true"
                                preset="MONEY"
                            />
                        </BaseTableCell>
                    </WorksheetTableRow>
                </template>
                <!-- Parent adjusted values -->
                <template>
                    <WorksheetTableRow>
                        <BaseTableCell class="qv-vertical-align-middle">
                            <p class="qv-font-bold">
                                {{ valuationReference }}
                            </p>
                        </BaseTableCell>
                        <BaseTableCell class="qv-vertical-align-middle">
                            Revision Values
                        </BaseTableCell>
                        <BaseTableCell class="qv-vertical-align-middle">
                            {{ formatPrice(parentValue.lumpSum) }}
                        </BaseTableCell>
                        <BaseTableCell class="qv-vertical-align-middle">
                            {{ formatPercentage(parentValue.adjustment) }}
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.ADOPTED_REVISION_CV">
                                <NumeralInput
                                    v-model="parentAdjustedRevisonRatingValues.capitalValue"
                                    preset="MONEY_POSITIVE"
                                    :readonly="true"
                                    :min="0"
                                    @input="onParentValueChanged"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.ADOPTED_REVISION_LV">
                                <NumeralInput
                                    v-model="parentAdjustedRevisonRatingValues.landValue"
                                    preset="MONEY_POSITIVE"
                                    :readonly="true"
                                    :min="0"
                                    @input="onParentValueChanged"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <NumeralInput
                                v-model="parentAdjustedRevisonRatingValues.valueOfImprovements"
                                :readonly="true"
                                preset="MONEY"
                            />
                        </BaseTableCell>
                    </WorksheetTableRow>
                </template>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

<script setup>
import {
    WorksheetTableLand,
    WorksheetTableOtherBuildings,
    WorksheetTableOtherImprovements,
    WorksheetTablePrimaryBuildings,
    WorksheetTableValues,
    WorksheetTableMaoriRevisionValues,
    WorksheetTableMaoriAdoptedRevisionValues,
    WorksheetTableMaoriLand,
    WorksheetTableMaoriAdoptedValues,
    WorksheetTableMaoriAdoptedApportionmentValues,
    WorksheetTableMaoriCurrentRevisionApportionmentValues,
    WorksheetTableMaoriNewRevisionApportionmentValues, WorksheetTableSraValues,
} from '../';
import { useValuationWorksheetMaori } from '@/composables/ratingValuation';
import { computed, toRefs } from 'vue';
import { ValidationContext } from '@/components/ui/validation';

const emit = defineEmits(['update:value', 'changed']);
const props = defineProps({
    value: {
        type: Object,
        required: true,
    },
    property: {
        required: true,
    },
    propertyDetail: {
        required: true,
    },
    validationSet: {
        type: Object,
        required: false,
    },
});

const {
    value,
    property,
    propertyDetail,
    validationSet
} = toRefs(props);

const {
    land,
    primaryBuildings,
    otherBuildings,
    otherImprovements,
    worksheetValues,
    hasRevisionValue,
    maoriLandValues,
    unadjustedValue,
    unadjustedRevisionValue,
    ratingValue,
    revisionRatingValue,
    totalCurrentAdjustment,
    totalRevisionAdjustment,
    currentAdjustmentValue,
    revisionAdjustmentValue,
    originalMaoriRevisionValue,
    originalUnadjustedRevisionValue,
    originalLumpSumRevision,
    totalOriginalRevisionAdjustment,
    componentValidations,
    apportionmentValues,
    hasSraValues,
    hasSraRevision,
} = useValuationWorksheetMaori(value, property, propertyDetail);

const parentValue = computed(() => ({
    unadjusted: unadjustedValue.value,
    rating: ratingValue.value,
    lumpSum: props.value.lumpSum,
    adjustment: totalCurrentAdjustment.value,
}));

function updateParentValue(event) {
    value.unadjustedValue = event.unadjusted.value;
    value.adoptedValue = event.rating.value;
    emit('changed');
}

const newRevisionParentValue = computed(() => ({
    unadjusted: unadjustedRevisionValue.value,
    revision: revisionRatingValue.value,
    lumpSum: value.value.lumpSumRevision,
    adjustment: totalRevisionAdjustment.value,
}));

function updateNewRevisionParentValue(event) {
    unadjustedRevisionValue.value = event.unadjusted;
    revisionRatingValue.value = event.revision;
    emit('changed');
}

function onChanged() {
    emit('changed');
}
</script>

<template>
    <ValidationContext :validation-set="validationSet">
        <div class="qv-w-full" :key="value.id" data-cy="worksheet-form-maori">
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Improvements</h2>
                <h4 class="qv-text-base qv-text-darkblue">Principal Buildings</h4>
                <WorksheetTablePrimaryBuildings v-model="primaryBuildings" class="qv-pb-2" @changed="onChanged"/>
                <h4 class="qv-text-base qv-text-darkblue">Other Buildings</h4>
                <WorksheetTableOtherBuildings v-model="otherBuildings" class="qv-pb-2" @changed="onChanged"/>
                <h4 class="qv-text-base qv-text-darkblue">Other Improvements</h4>
                <WorksheetTableOtherImprovements v-model="otherImprovements" class="qv-pb-2" @changed="onChanged"/>
            </div>
            <hr>
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Land</h2>
                <WorksheetTableLand v-model="land" class="qv-pb-2" @changed="onChanged"
                />
            </div>
            <hr>
            <div class="qv-flex-column qv-gap-1">
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Worksheet Values (Unadjusted)</h2>
                <WorksheetTableValues v-model="worksheetValues" class="qv-pb-2" @changed="onChanged" />
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Maori Land</h2>
                <WorksheetTableMaoriLand v-model="maoriLandValues" :revision-adjustment.sync="revisionAdjustmentValue" :current-adjustment.sync="currentAdjustmentValue" class="qv-pb-2" @changed="onChanged" />
                <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Adopted Values</h2>
                <template v-if="apportionmentValues">
                    <WorksheetTableMaoriAdoptedApportionmentValues
                        :apportionmentValues="apportionmentValues"
                        :valuationReference="property.valuationReference"
                        :parentValue="parentValue"
                        class="qv-pb-2"
                        @update:parentValue="updateParentValue($event)"
                        @changed="onChanged"/>
                </template>
                <template v-else>
                    <WorksheetTableMaoriAdoptedValues
                        :unadjusted.sync="unadjustedValue"
                        :rating.sync="ratingValue"
                        :lump-sum="value.lumpSum"
                        :adjustment="totalCurrentAdjustment"
                        class="qv-pb-2"
                        @changed="onChanged"/>
                </template>
                <template v-if="hasRevisionValue">
                    <hr>
                    <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Existing Revision Values</h2>
                    <template v-if="apportionmentValues">
                        <WorksheetTableMaoriCurrentRevisionApportionmentValues
                            v-model="apportionmentValues"
                            :valuationReference="property.valuationReference"
                            :unadjusted="originalUnadjustedRevisionValue"
                            :revision="originalMaoriRevisionValue"
                            :lump-sum="value.lumpSumRevision"
                            :adjustment="totalOriginalRevisionAdjustment"
                            class="qv-pb-2"
                            @changed="onChanged"
                            @update:parentValue="originalMaoriRevisionValue = $event"
                        />
                    </template>
                    <template v-else>
                        <WorksheetTableMaoriRevisionValues
                            :unadjusted="originalUnadjustedRevisionValue"
                            :revision="originalMaoriRevisionValue"
                            :lump-sum="originalLumpSumRevision"
                            :adjustment="totalOriginalRevisionAdjustment"
                            class="qv-pb-2"
                            @changed="onChanged"
                        />
                    </template>
                    <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">New Revision Values</h2>
                    <template v-if="apportionmentValues">
                        <WorksheetTableMaoriNewRevisionApportionmentValues
                            :apportionmentValues="apportionmentValues"
                            :parentValue="newRevisionParentValue"
                            :valuationReference="property.valuationReference"
                            class="qv-pb-2"
                            @changed="onChanged"
                            @update:parentValue="updateNewRevisionParentValue($event)"
                        />
                    </template>
                    <template v-else>
                        <WorksheetTableMaoriAdoptedRevisionValues
                            :unadjusted.sync="unadjustedRevisionValue"
                            :revision.sync="revisionRatingValue"
                            :lump-sum="value.lumpSumRevision"
                            :adjustment="totalRevisionAdjustment"
                            class="qv-pb-2"
                            @changed="onChanged"/>
                    </template>
                </template>
                <WorksheetTableSraValues
                    v-if="hasSraValues"
                    v-model="value.sra.sras"
                    :hasSraRevision="hasSraRevision"
                    :qpid="value.ratingUnit.qpid"
                    class="qv-pb-2"
                    @changed="onChanged" />
            </div>
        </div>
    </ValidationContext>
</template>

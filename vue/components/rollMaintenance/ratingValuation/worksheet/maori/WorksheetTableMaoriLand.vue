<script setup>
import {
    BaseTable,
    BaseTableHead,
    BaseTableBody,
    WorksheetTableHeader,
    WorksheetTableRow,
    BaseTableCell,
} from '@/components/ui/table';
import { computed, onMounted, toRefs } from 'vue';
import { useVModel } from '@/composables/useVModel';
import { NumeralInput } from '@/components/ui/input';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Object,
        required: true,
    },
    currentAdjustment: {
        type: Object,
        required: true,
    },
    revisionAdjustment: {
        type: Object,
        required: true,
    },
});

const emit = defineEmits(['input', 'changed', 'update:current-adjustment', 'update:revision-adjustment']);
const modelValue = useVModel(props, emit, 'value', { deep: true });

const { numberOfOwners } = toRefs(props.value);

const currentAdjustment = computed(() => props.currentAdjustment);
const revisionAdjustment = computed(() => props.revisionAdjustment);

function adjustmentChanged(type) {
    emit(`update:${type}-adjustment`, type === 'current' ? currentAdjustment.value : revisionAdjustment.value);
    emit('changed');
}


</script>

<template>
    <ValidationProvider path="maoriLand">
        <BaseTable>
            <WorksheetTableHeader>
                <BaseTableHead style="width: 15%" />
                <BaseTableHead style="width: 25%">
                    Adjustment
                </BaseTableHead>
                <BaseTableHead style="width: 25%">
                    Revision Adjustment
                </BaseTableHead>
                <BaseTableHead style="width: 34%" />
            </WorksheetTableHeader>
            <BaseTableBody>
                <WorksheetTableRow>
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                        Multiple Owners:
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.MULTIPLE_OWNER_ADJUSTMENT">
                            <NumeralInput
                                v-model="currentAdjustment.multipleOwnerAdjustmentPercentage"
                                :default="0"
                                :min="0"
                                fixed="1"
                                preset="PERCENT_ONE_DECIMAL"
                                data-cy="multiple-owners-adjusted"
                                @input="adjustmentChanged('current')"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.REVISION_MULTIPLE_OWNER_ADJUSTMENT">
                            <NumeralInput
                                v-model="revisionAdjustment.multipleOwnerAdjustmentPercentage"
                                :default="0"
                                :min="0"
                                fixed="1"
                                preset="PERCENT_ONE_DECIMAL"
                                data-cy="multiple-owners-revision-adjusted"
                                @input="adjustmentChanged('revision')"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <div class="qv-flex-row qv-justify-end qv-gap-3 qv-align-center">
                            <div class="qv-font-semibold qv-vertical-align-middle">No. of Owners:</div>
                            <ValidationWrapper :path="FIELDS.NUMBER_OF_OWNERS">
                                <NumeralInput
                                    v-model="modelValue.numberOfOwners"
                                    :min="0"
                                    :default="0"
                                    data-cy="number-of-owners"
                                />
                            </ValidationWrapper>
                        </div>
                    </BaseTableCell>
                </WorksheetTableRow>
                <WorksheetTableRow>
                    <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                        Site Significance:
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.SITE_SIGNIFICANCE_ADJUSTMENT">
                            <NumeralInput
                                v-model="currentAdjustment.siteSignificanceAdjustmentPercentage"
                                :default="0"
                                :min="0"
                                fixed="1"
                                data-cy="site-significance-adjusted"
                                preset="PERCENT_ONE_DECIMAL"
                                @input="adjustmentChanged('current')"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.REVISION_SITE_SIGNIFICANCE_ADJUSTMENT">
                            <NumeralInput
                                v-model="revisionAdjustment.siteSignificanceAdjustmentPercentage"
                                :default="0"
                                :min="0"
                                fixed="1"
                                data-cy="site-significance-revision-adjusted"
                                preset="PERCENT_ONE_DECIMAL"
                                @input="adjustmentChanged('revision')"
                            />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell />
                </WorksheetTableRow>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

<script setup>
import {
    BaseTable,
    BaseTableHead,
    BaseTableBody,
    WorksheetTableHeader,
    WorksheetTableRow,
    BaseTableCell,
    useTable,
} from '@/components/ui/table';
import AddRemoveButton from '@/components/ui/button/AddRemoveButton.vue';
import {
    VALUATION_COMPONENTS,
    calculateValue,
    calculateValuePerSquareMetre,
} from '@/composables/ratingValuation';
import { useVModel } from '@/composables/useVModel';
import { BaseInput, NumeralInput } from '@/components/ui/input';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { RatingValuation } from '@quotable-value/validation';

const { FIELDS } = RatingValuation;

const props = defineProps({
    value: {
        type: Array,
        required: true,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['input', 'changed', 'add']);
const modelValue = useVModel(props, emit, 'value', { deep: true });
const table = useTable(modelValue, {
    defaultValue: {
        buildingType: '',
        componentType: VALUATION_COMPONENTS.OTHER_BUILDING,
        description: '',
        areaInSquareMetres: null,
        valuePerSquareMetre: null,
        value: null,
    }
});
</script>

<template>
    <ValidationProvider :path="FIELDS.OTHER_BUILDINGS" v-slot="{ hasErrorsForIndex }">
        <BaseTable data-cy="worksheet-table-other-buildings">
            <WorksheetTableHeader>
                <BaseTableHead style="width: 17%;">
                    Building Type
                </BaseTableHead>
                <BaseTableHead style="width: 25%">
                    Space Description
                </BaseTableHead>
                <BaseTableHead>
                    Area
                </BaseTableHead>
                <BaseTableHead>
                    Rate /m<sup>2</sup>
                </BaseTableHead>
                <BaseTableHead>
                    Calculated Value
                </BaseTableHead>
                <BaseTableHead v-if="!readonly" aria-label="Action Column" />
            </WorksheetTableHeader>
            <BaseTableBody>
                <WorksheetTableRow v-for="(row, index) in modelValue" :key="index" :errored="hasErrorsForIndex(index)">
                    <BaseTableCell>
                            <BaseInput v-model="row.buildingType" :readonly="readonly" data-cy="building-type" />
                    </BaseTableCell>
                    <BaseTableCell>
                            <BaseInput v-model="row.description" :readonly="readonly" data-cy="description" />
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.OTHER_BUILDING_AREA" :index="index">
                            <NumeralInput v-model="row.areaInSquareMetres" :readonly="readonly" data-cy="area" format="0,0" preset="AREA" @input="() => calculateValuePerSquareMetre(row)" />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.OTHER_BUILDING_RATE" :index="index">
                            <NumeralInput v-model="row.valuePerSquareMetre" :readonly="readonly" data-cy="value-per-square-metre" preset="MONEY_2DP" fixed="4" @input="() => calculateValue(row)" />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell>
                        <ValidationWrapper :path="FIELDS.OTHER_BUILDING_VALUE" :index="index">
                            <NumeralInput v-model="row.value" :readonly="readonly" data-cy="value" preset="MONEY" @input="() => calculateValuePerSquareMetre(row)" />
                        </ValidationWrapper>
                    </BaseTableCell>
                    <BaseTableCell v-if="!readonly" class="qv-vertical-align-middle">
                        <AddRemoveButton :can-remove="index > 0" @add="() => table.addRow(index)" @remove="() => table.removeRow(index)" />
                    </BaseTableCell>
                </WorksheetTableRow>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

<script setup>
import { BaseTable, BaseTableBody, WorksheetTableRow, BaseTableCell, WorksheetTableHeader, BaseTableHead } from '@/components/ui/table';
import { calculateValueOfImprovement } from '@/composables/ratingValuation';
import { useVModel } from '@/composables/useVModel';
import { NumeralInput } from '@/components/ui/input';
import { ValidationProvider, ValidationWrapper } from '@/components/ui/validation';
import { Sra } from '@quotable-value/validation';
import { useStore } from '@/composables/useStore';
import { onBeforeMount } from 'vue';

const { FIELDS } = Sra;

const props = defineProps({
    value: {
        type: Array,
        required: true,
    },
    hasSraRevision: {
        type: Boolean,
        default: false,
    },
    qpid: {
        type: Number,
        required: true,
    },
});
const emit = defineEmits(['input', 'changed']);
const modelValue = useVModel(props, emit, 'value', { deep: true });

const sraStore = useStore('sra');

function getAuthorityDescription(classId) {
    return sraStore.state?.sraClasses?.value?.find(c => c.id === classId)?.description;
}

onBeforeMount(async () => {
    await sraStore.dispatch('loadSraProperty', props.qpid);
});

</script>

<template>
    <ValidationProvider path="sraValues" v-slot="{ hasErrors }">
        <h2 class="qv-text-md qv-font-semibold qv-text-darkblue">Special Rating Authority</h2>
        <BaseTable class="qv-table-striped">
            <WorksheetTableHeader>
                <BaseTableHead>
                    Special Rating Authority
                </BaseTableHead>
                <BaseTableHead style="width: 10%">
                    Area
                </BaseTableHead>
                <BaseTableHead style="width: 20%">
                    Capital value
                </BaseTableHead>
                <BaseTableHead style="width: 20%">
                    Land value
                </BaseTableHead>
                <BaseTableHead style="width: 20%">
                    Value of Imporvements
                </BaseTableHead>
            </WorksheetTableHeader>
            <BaseTableBody>
                <template v-for="(sraValue, index) in modelValue">
                    <WorksheetTableRow :errored="hasErrors">
                        <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                            {{ getAuthorityDescription(sraValue.classId) }}
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.SRA_AREA" :index="sraValue.id">
                                <NumeralInput
                                    v-model="sraValue.area"
                                    :data-cy='"sra-area-" + index'
                                    preset="AREA_HECTARES"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.SRA_CV" :index="sraValue.id">
                                <NumeralInput
                                    v-model="sraValue.capitalValue"
                                    :data-cy='"sra-capital-value-" + index'
                                    preset="MONEY"
                                    @input="calculateValueOfImprovement(sraValue)"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.SRA_LV" :index="sraValue.id">
                                <NumeralInput
                                    v-model="sraValue.landValue"
                                    :data-cy='"sra-land-value-" + index'
                                    preset="MONEY"
                                    @input="calculateValueOfImprovement(sraValue)"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.SRA_VI">
                                <NumeralInput
                                    :value="sraValue.valueOfImprovements"
                                    :readonly="true"
                                    :data-cy='"sra-value-of-improvements-" + index'
                                    preset="MONEY"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                    </WorksheetTableRow>
                    <WorksheetTableRow v-if="hasSraRevision" :errored="hasErrors">
                        <BaseTableCell class="qv-font-semibold qv-vertical-align-middle">
                            Revision Values
                        </BaseTableCell>
                        <BaseTableCell/>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.SRA_REVISION_CV" :index="sraValue.id">
                                <NumeralInput
                                    v-model="sraValue.revision.capitalValue"
                                    :data-cy='"sra-revision-capital-value-" + index'
                                    preset="MONEY"
                                    @input="calculateValueOfImprovement(sraValue.revision)"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.SRA_REVISION_LV" :index="sraValue.id">
                                <NumeralInput
                                    v-model="sraValue.revision.landValue"
                                    :data-cy='"sra-revision-land-value-" + index'
                                    preset="MONEY"
                                    @input="calculateValueOfImprovement(sraValue.revision)"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                        <BaseTableCell>
                            <ValidationWrapper :path="FIELDS.SRA_REVISION_VI" :index="sraValue.id">
                                <NumeralInput
                                    :value="sraValue.revision.valueOfImprovements"
                                    :readonly="true"
                                    :data-cy='"sra-revision-value-of-improvements-" + index'
                                    preset="MONEY"
                                />
                            </ValidationWrapper>
                        </BaseTableCell>
                    </WorksheetTableRow>
                </template>
            </BaseTableBody>
        </BaseTable>
    </ValidationProvider>
</template>

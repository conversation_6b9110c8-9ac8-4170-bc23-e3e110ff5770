import { inject, provide, unref } from 'vue';

export const RatingValuationContext = Symbol('RatingValuationContext');

/**
 * @typedef Context
 * @property {(silent?: boolean) => Promise<void>} save
 * @property {() => void} validate
 *
 * Provides rating valuation job context
 * @param context {Context}
 */
export function provideRatingValuationContext(context) {
    return provide(RatingValuationContext, context);
}

/**
 * Injects rating valuation job context
 * @returns {Context}
 */
export function injectRatingValuationContext() {
    return unref(inject(RatingValuationContext));
}

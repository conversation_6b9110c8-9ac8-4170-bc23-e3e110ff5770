<script setup>
import { ref, computed, watch, onMounted, provide } from 'vue';
import { onBeforeRouteLeave, useRouter } from 'vue-router/composables';
import { store } from '@/DataStore';
import { useStore } from '@/composables/useStore';
import useValuerInfo from '@/composables/useValuerInfo';

import RollMaintenanceActivities from './activities/RollMaintenanceActivities.vue';
import Expander from '@/components/common/Expander.vue';
import AlertModal from '@/components/common/modal/AlertModal.vue';
import DraftPropertyForm from '../../propertyDetails/residential/DraftPropertyForm.vue';
import { ValuationValidationList } from '@/components/rollMaintenance/ratingValuation/common';

import { openQivsInNewTab } from '@/utils/QivsUtils';
import { formatDate } from '../../propertyDetails/residential';
import { injectRatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';

const router = useRouter();
const ratingValuationStore = useStore('ratingValuation');
const classificationsStore = useStore('classifications');
const userDataStore = useStore('userData');
const propertyStore = useStore('property');
const currentPropertyDetailsStore = useStore('currentPropertyDetails');
const propertyDraftStore = useStore('propertyDraft');
const { users, loadUsers } = useValuerInfo();
const context = injectRatingValuationContext();

const { property } = propertyStore.state;
const { classifications } = classificationsStore.state;

provide('classifications', classifications);

const {
    propertyDetail : currentPropertyDetail,
    loading: loading
} = currentPropertyDetailsStore.state;

const {
    propertyDetail,
    formIsStale,
    loading: draftLoading,
    saving: draftSaving,
    exception: draftException,
    validationSet,
} = propertyDraftStore.state;

const { userName } = userDataStore.state;

const {
    ratingValuation,
    propertyActivities,
    validationSet: rvValidationSet,
    exception: valuationException,
} = ratingValuationStore.state;

const showGenerateBuildingsModal = ref(false);
const expandConsents = ref(true);
const validationHeader = ref();
const consentJobUpdateInfo = ref(null);
const showWarnings = ref(false);
const successModalIsOpen = ref(false);
const alertMessage = ref({
    heading: '',
    message: '',
});
const successMessage = ref({
    heading: '',
    message: '',
    navigateTo: null,
});
const alertModalIsOpen = ref(false);
const navigateOnSetupComplete = ref(false);
const navigateOnSuccess = ref(false);
const setupCompletedByValuer = ref(false);

const qpid = computed(() =>ratingValuation.value.ratingUnit.qpid);
const hasConsentJobUpdateInfo = computed(() => consentJobUpdateInfo.value);
const propertyLoaded = computed(() => ratingValuation.value.ratingUnit.qpid === propertyDetail.value.qpid);
const createdBy = computed(() => {
    if (hasConsentJobUpdateInfo.value && users.value) {
        const match = users.value.filter((user) => user.ntUsername === consentJobUpdateInfo.value.createdBy);

        if (match && match.length > 0) {
            return match[0].name;
        }
        if (match && match.length === 0) {
            alert(`ERR-JSU-003: No match found for "Job Created By". This can happen when a user doesn't exist in Monarch but exists in QIVS. \n
                    Please contact qvsupport and ask them to make a monarch user for this username: "${consentJobUpdateInfo.value.createdBy}"`);
        }
    }
});

const createdAt = computed(() => {
    if (hasConsentJobUpdateInfo.value) {
        return formatDate(consentJobUpdateInfo.value.createdAt);
    }
});
const isSetupComplete = computed(() => {
    if (!propertyActivities.value) {
        return false;
    }
    return !propertyActivities.value.find((activity) => ratingValuation.value.rollMaintenanceActivityIds.includes(activity.id) && activity.setupComplete === false);
});
const disabledSteps = computed(() => {
    if (isSetupComplete.value === false) {
        return [1, 2, 3];
    }
    return [];
});

const propertyId = computed(() => ratingValuation.value.ratingUnit.propertyId || null);

watch(() => property.value, onPropertyValueChanged, { deep: true, immediate: true });

onMounted(() => {
    loadPropertyDetail();
    loadCurrentPropertyDetails();
    getAllUsers();
    getConsentJobUpdateInformation();
});

async function onSaveBetweenSteps() {
    const step = 'propertyDetails';
    const isPropertyDraftStep = true;
    const saved = await context.saveBetweenSteps(step, true, isPropertyDraftStep);
    return saved;
}

onBeforeRouteLeave(async (to, from, next) => {
    const valuationJobPages = ['rating-valuation-comparable-properties', 'rating-valuation-worksheet', 'rating-valuation-writeup', 'rating-valuation-property-details'];
    if (valuationJobPages.includes(to.name)) {
        const saved = await onSaveBetweenSteps();
        if (!saved) {
            return;
        }
    }
    next();
});

function showAlertModal(heading, message) {
    alertMessage.value = {
        heading,
        message,
    };
    alertModalIsOpen.value = true;
}

function closeAlertModal() {
    alertModalIsOpen.value = false;
}

function hideWarningModal() {
    showWarnings.value = false;
}

function showWarningModal() {
    showWarnings.value = true;
}

function showSuccess(message = {}) {
    successMessage.value.heading = message.heading || 'Saved.';
    successMessage.value.message = message.message || 'Your changes have been saved.';
    successMessage.value.navigateTo = message.navigateTo || null;
    successModalIsOpen.value = true;
}

function closeSuccessModal() {
    successModalIsOpen.value = false;
    if (!successMessage.value.navigateTo) return;
    router.push(successMessage.value.navigateTo);
}

async function getAllUsers() {
    if (!users.value) {
        loadUsers();
    }
}

async function loadCurrentPropertyDetails() {
    try {
        await currentPropertyDetailsStore.dispatch('getPropertyDetailByPropertyId', propertyId.value);
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
    }
}

async function loadTAZoneClassification(code) {
    await classificationsStore.dispatch('fetchTAZoneClassification', code);
}

const silent = false;
const isCompletion = false;
const isPropertyDraftStep = true;

async function onSave(silent = false) {
    const saved = await context.save(silent, isCompletion, isPropertyDraftStep);
    if (!saved) {
        return;
    }
}

async function completeSetup(setupByValuer, navigateOnSuccess = true) {
    navigateOnSetupComplete.value = navigateOnSuccess;
    setupCompletedByValuer.value = setupByValuer;
    const route = setupCompletedByValuer.value ? 'rating-valuation-comparable-properties' : 'roll-maintenance';
    try {
        const saved = await context.save(silent, isCompletion, isPropertyDraftStep);
        if (!saved) {
            return;
        }
        await ratingValuationStore.dispatch('completeSetup');
        await loadPropertyDetail();
        if (validationSet.hasErrors) {
            return;
        }
        await saveConsentJobUpdateInformation();
        await ratingValuationStore.dispatch('loadRelatedRollMaintenanceActivities');
        showSuccess({
            heading: 'Setup Complete',
            message: 'Your changes have been saved. This property is now ready for valuation.',
            navigateTo: navigateOnSetupComplete.value ? { name: route } : null,
        });
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
    }
}

async function loadPropertyDetail() {
    if (draftLoading.value) return;
    try {
        if (ratingValuation.value.propertyDetailId) {
            await propertyDraftStore.dispatch('getPropertyDetail', ratingValuation.value.propertyDetailId);
        } else {
            await propertyDraftStore.dispatch('getPropertyDetail', qpid.value);
        }
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
    }
}

async function completeSetupWithWarnings() {
    hideWarningModal();
    try {
        await ratingValuationStore.dispatch('completeSetup');
        await loadPropertyDetail();
        if (!validationSet.success) {
            return;
        }

        await ratingValuationStore.dispatch('loadRelatedRollMaintenanceActivities');
        navigateOnSuccess.value = true;

        const route = setupCompletedByValuer.value ? 'rating-valuation-comparable-properties' : 'roll-maintenance';

        await saveConsentJobUpdateInformation();
        showSuccess({
            heading: 'Setup Complete',
            message: 'Warnings were ignored, and your changes have been saved. This property is now ready for valuation.',
            navigateTo: navigateOnSetupComplete.value ? { name: route } : null,
        });
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred while communicating with the server: ${err}`);
    }
}

async function onPropertyValueChanged(value) {
    await context.validate();
    if (`${value.qupid}` === `${ratingValuation.value.ratingUnit.qpid}`) {
        const code = value.territorialAuthority.code;
        await store.dispatch('fetchTAZoneClassification', code);
    }
}

async function generatePropertyDetailNewDwellingInformation() {
    try {
        await propertyDraftStore.dispatch('generatePropertyDetailNewDwellingInformation');
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
    }
}

async function generateBuildingsFromPropertyDetail(data) {
    try {
        await propertyDraftStore.dispatch('generateBuildingsFromPropertyDetail', data);
    } catch (err) {
        showAlertModal('Unexpected Error', `An unexpected error occurred attempting to communicate with the server: ${err}`);
    }
}

async function getConsentJobUpdateInformation() {
    const ratingValuationId = ratingValuation.value.id;
    const { url } = jsRoutes.controllers.RatingValuationController.getConsentJobUpdateInformation(ratingValuationId);
    try {
        const res = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        consentJobUpdateInfo.value = await res.json();
    } catch (error) {
        console.error(`ERR-JSU-001: Error fetching consent job update information:`, error);
        throw error;
    }
}

async function saveConsentJobUpdateInformation() {
    const name = `QVNZ\\${userName.value}`;
    const consentJobId = ratingValuation.value.id;
    const { url } = jsRoutes.controllers.RatingValuationController.saveConsentJobUpdateInformation(consentJobId, name, qpid.value);
    try {
        const res = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            cache: 'no-store'
        });
        consentJobUpdateInfo.value = await res.json();
    } catch (error) {
        console.error(`ERR-JSU-002: Error saving consent job update information:`, error);
        throw error;
    }
}

function updatePropertyDetail($event) {
    propertyDraftStore.commit('setSinglePropertyDetail', $event);
    context.validate();
}

function openQivsImprovementSummary() {
    openQivsInNewTab(userDataStore.getters.qivsImprovementSummaryUrl.value(qpid.value));
}
</script>

<template>
    <div class="qv-draft-property">
        <div v-if="propertyLoaded && propertyDetail.isStale" class="righty message message-warning">
            NOTE: The detailed information for this property has changed, please review the
            <router-link :to="{ name: 'property', params: { qpid: qpid } }" target="_blank">current property</router-link>
            and update the draft property details.
        </div>
        <div class="mdl-shadow--3dp form-section">
            <div class="col-container">
                <expander v-model="expandConsents" class="righty" />
                <h1 class="title">
                    Building Consents
                </h1>
                <roll-maintenance-activities
                    v-show="expandConsents"
                    :can-edit="true"
                    :load-on-mount="false"
                />
            </div>
        </div>
        <div class="col-container mdl-shadow--3dp form-section">
            <div class="qv-flex-row qv-justify-space-between">
                <h1 class="title">Draft Property</h1>
                <div class="qv-text-md qv-font-semibold qv-text-darkblue" v-if="consentJobUpdateInfo">
                    <ul>
                        <li>
                            Job Created By: <span>{{ createdBy }}</span>
                        </li>
                        <li>
                            Created On: <span>{{ createdAt }}</span>
                        </li>
                    </ul>
                </div>
            </div>
            <DraftPropertyForm
                :property-detail="propertyDetail"
                :property="property"
                :current-property-detail="currentPropertyDetail"
                :validation-set="validationSet"
                :disabled="draftSaving"
                :highlight="formIsStale"
                @update:property-detail="updatePropertyDetail"
                @update:dvr-snapshot="onSave"
                @generate:property-detail="generatePropertyDetailNewDwellingInformation"
                @generate:buildings="generateBuildingsFromPropertyDetail"
                @open:qivs-improvement-summary="openQivsImprovementSummary"
            />
            <div class="col-row">
                <div data-cy="valuation-property-details-setup-button" class="col col-12">
                    <div class="righty">
                        <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored" :disabled="draftSaving" @click="completeSetup(true)">Setup Complete & Value</button>
                        <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect" :disabled="draftSaving" @click="onSave">Save as Draft</button>
                        <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored" :disabled="draftSaving" @click="completeSetup">Setup Complete</button>
                    </div>
                </div>
            </div>
        </div>

        <AlertModal v-if="showWarnings" warning>
            <h3 id="errorHeader">Do you want to proceed?</h3>
            <p>The following validation checks are failing:</p>
            <div class="validation-header-message--warnings">
                    <ValuationValidationList :validation-set="rvValidationSet" :pd-validation-set="validationSet" />
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button id="errorCancel" class="mdl-button mdl-button--mini lefty" @click="hideWarningModal">No, Return to draft</button>
                    <button id="continue" class="mdl-button mdl-button--mini" @click="completeSetupWithWarnings">Yes, Complete Setup</button>
                </div>
            </template>
        </AlertModal>
        <AlertModal v-if="alertModalIsOpen" warning @close="closeAlertModal">
            <h1>{{ alertMessage.heading }}</h1>
            <p>{{ alertMessage.message }}</p>
        </AlertModal>
        <AlertModal v-if="successModalIsOpen" success @close="closeSuccessModal">
            <h1>{{ successMessage.heading }}</h1>
            <p>{{ successMessage.message }}</p>
        </AlertModal>
    </div>
</template>

<script setup>
import useModal from '@/composables/useModal';
import { AlertTitle, ValidationAlert, ValidationAlertList, ValidationAlertListItem } from '@/components/ui/alert';

const props = defineProps({
  isError: {
    default: false,
  },
  isWarning: {
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  subTitle: {
    type: String,
    default: '',
  },
  validationList: {
    type: Array,
    default: '',
  },
  confirmText: {
    type: String,
    default: '',
  },
  cancelText: {
    type: String,
    default: '',
  },
  cancelErrorText: {
    type: String,
    default: '',
  },
  onlyConfirm: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close']);
</script>

<template>
  <div class="qv-flex-column qv-flex-grow qv-justify-space-between">
    <div class="qv-dialog-content">
      <h1 :class="{ 'qv-color-error': isError, 'qv-color-warning': isWarning }" class="qv-text-lg" style="margin-bottom: 1rem;" data-cy="dialog-title">{{ title }}</h1>
      <ValidationAlert v-if="isError" class="qv-text-base" variant="error" data-cy="errors-pop-up">
          <AlertTitle>{{ subTitle }}</AlertTitle>
        <ValidationAlertList style="margin-top: 0.5rem;">
          <ValidationAlertListItem v-for="validation in validationList" :key="`${validation.field.path}-${validation.message}`" :validation="validation" />
        </ValidationAlertList>
      </ValidationAlert>
      <ValidationAlert v-if="isWarning" class="qv-text-base" variant="warning" data-cy="warnings-pop-up">
        <AlertTitle>{{ subTitle }}</AlertTitle>
        <ValidationAlertList style="margin-top: 0.5rem;">
          <ValidationAlertListItem v-for="validation in validationList" :key="`${validation.field.path}-${validation.message}`" :validation="validation" />
        </ValidationAlertList>
      </ValidationAlert>
    </div>
    <div class="qv-dialog-buttons" :class="!isError ? 'qv-justify-space-between' : 'qv-justify-end'">
      <button v-if="!onlyConfirm" class="qv-dialog-button qv-color-dark" @click="() => emit('close',false)" data-cy="button-dialog-cancel">{{ isError ? cancelErrorText : cancelText }}</button>
      <button v-if="!isError" class="qv-dialog-button" @click="() => emit('close',true)" data-cy="button-dialog-confirm">{{ confirmText }}</button>
    </div>
  </div>
</template>

<style lang="scss">

.qv-dialog[open] {
  border: none;
  z-index: 421;
  position: fixed;
  width: 60rem;
  min-height: 30rem;
  max-height: 75rem;
  border-radius: 3px;
  box-shadow: 10px 10px 500px 100px rgba(0, 0, 0, 0.2), 0 11px 15px -7px rgba(0, 0, 0, 0.12), 0 24px 38px 3px rgba(0, 0, 0, 0.2);

  display: flex;
  flex-direction: column;
  justify-content: space-between;

  button:hover {
    opacity: 0.8;
  }

  .qv-dialog-buttons {
    display: flex;
    &.qv-justify-space-between {
      justify-content: space-between;
    }
    &.qv-justify-end {
      justify-content: flex-end;
    }
  }

  .qv-dialog-button {
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    text-transform: uppercase;
    line-height: 36px;
    border: none;
    border-radius: 2px;
    vertical-align: middle;
    padding: 0 16px;
    margin: 0;
    min-width: 8.5rem;
    height: 36px;
  }

  .qv-dialog-button:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }

  .validation-message {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    padding: 0.8rem;
  }

  .qv-color-warning {
    color: #db7e00;
  }
}
</style>

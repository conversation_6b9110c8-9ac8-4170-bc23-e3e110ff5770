<script setup>
import { ref, computed, inject } from 'vue';
import AlertModal from 'Common/modal/AlertModal.vue';
import Badge from 'Common/Badge.vue';
import { store } from '@/DataStore';
import { formatPrice, formatDate } from '@/utils/FormatUtils';

const props = defineProps({
    qpid: {
        type: Number,
        required: true,
        default: () => null,
    },
    propertyDesc: {
        type: String,
        required: false,
        default: '',
    },
    ratingValuation: {
        type: Object,
        default: () => { },
    },
});

const saving = ref(false);
const taCode = inject('taCode');
const reloadingPropertyDescription = ref(false);
const showRefreshModal = ref(false);
const refreshModalMessage = ref('');
const refreshModalHandler = ref('');
const propertyDescriptionGetSet = computed({
    get() {
        return props.propertyDesc;
    },
    set(value) {
        store.commit('propertyDraft/setSinglePropertyDetail', { id: 'description', value });
    },
});

const showDescriptionWarning = ref(false);

const valuationConclusionsGetSet = computed({
    get() {
        return props.ratingValuation.summaryOfChanges;
    },
    set(value) {
        updateRatingValuation({ id: 'summaryOfChanges', value });
    },
});

async function regeneratePropertyDescription() {
    reloadingPropertyDescription.value = true;
    showRefreshModal.value = false;
    const { url } = jsRoutes.controllers.PropertyDetailController.generatePropertyDetailDescription(props.ratingValuation.propertyDetailId)
    try {
        const res = await fetch(url, { method: 'GET' });
        propertyDescriptionGetSet.value = await res.json();
    }
    catch (error) {
        throw Error(`Error calling regeneratePropertyDescription: ${error}}`);
    }
    finally {
        reloadingPropertyDescription.value = false;
    }
}

async function getImprovedComparableSales(ratingValuationId) {
    try {
        const { url } = jsRoutes.controllers.ApiPicklistController.getComparableSales(ratingValuationId);
        const res = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        const { status, message, improvedSales } = await res.json();
        if (status !== 'SUCCESS') {
            console.error(`Error calling get comparable sale api. ${message}`);
            return null;
        }
        return improvedSales;
    }
    catch (error) {
        console.error('Error calling get comparable sale api', error);
        return null;
    }
}

async function getNetRateRangesFromComparableSales() {
    const improvedComparableSales = await getImprovedComparableSales(props.ratingValuation.id);
    if (!improvedComparableSales?.length) {
        return null;
    }
    const minNetRate = improvedComparableSales.reduce((a, b) => a.adjustedNetRate < b.adjustedNetRate ? a : b).adjustedNetRate;
    const maxNetRate = improvedComparableSales.reduce((a, b) => a.adjustedNetRate > b.adjustedNetRate ? a : b).adjustedNetRate;
    const diff = maxNetRate - minNetRate;
    const oneThird = diff / 3.0;
    return {
        min: minNetRate,
        max: maxNetRate,
        lower: minNetRate + oneThird,
        higher: minNetRate + oneThird * 2.0,
    };
}

async function getComparableSaleSalad() {
    const propertyNetRate = props.ratingValuation?.workingNetRate;
    if (!propertyNetRate || propertyNetRate === 0.0) {
        return '';
    }
    const ranges = await getNetRateRangesFromComparableSales();
    if (!ranges) {
        return '';
    }
    // this is considering the case where there is a single comparable, then middle is returned should it equal to that specific value.
    const comparedNetRateRange = propertyNetRate < ranges.lower ? 'lower' : (propertyNetRate <= ranges.higher ? 'middle' : 'higher');
    return `Net rates for the comparable sales fall within the range of $${ranges.min} to $${ranges.max}. Having considered the sales in relation to the subject property attributes we consider the net rate for the subject property to be in the ${comparedNetRateRange} portion of this range. In assessing the rating value of the subject property we have adopted a net rate of $${propertyNetRate} over the main living area of the dwelling.  We have then applied values to the remaining improvements relative to the analysed sales.`;
}

async function getPreviousRevisionDate() {
    const { url } = jsRoutes.controllers.TADashboardController.displayGraphs(taCode.value);
    try {
        const res = await fetch(url, { method: 'GET' });
        const json = await res.json();
        return json.taMarketSummary.previousRevisionDate;
    }
    catch (error) {
        throw Error(`Error calling getPreviousRevisionDate: ${error}}`);
    }
}

async function regenerateValuationConclusion() {
    showRefreshModal.value = false;
    const comparableSalesSalad = await getComparableSaleSalad();
    const prevRevisionDate = await getPreviousRevisionDate();
    const recentSaleSalad = await getLastSaleSalad(props.qpid, prevRevisionDate);
    valuationConclusionsGetSet.value = comparableSalesSalad ? (comparableSalesSalad + '\n' + recentSaleSalad) : recentSaleSalad;
}

function updateRatingValuation(data) {
    const ratingValuationCopy = { ...props.ratingValuation };
    ratingValuationCopy[data.id] = data.value;
    store.dispatch('ratingValuation/setValuation', ratingValuationCopy);
}

async function saveChanges() {
    saving.value = true;
    try {
        await Promise.all([
            store.dispatch('ratingValuation/saveValuation'),
            store.dispatch('propertyDraft/savePropertyDraft'),
        ]);
    }
    catch (error) {
        console.error('Error trying to save rating valuation and property draft');
    }
    finally {
        saving.value = false;
    }
}

async function getLastSaleSalad(qpid, dateSince) {
    const { url } = jsRoutes.controllers.SalesSearch.displaySalesSearchResult();
    try {
        const payLoad = {
            locationCriteria: { qupid: qpid },
            classificationCriteria: {
                saleTypeCodes: ['S'],
                saleTenureCodes: ['1'],
                saleStatusCodes: ['1'],
                priceValueRelationshipCodes: ['1'],
            },
            saleDateFrom: dateSince,
            sort: ['SALE_DATE'],
            order: 'DESC',
        };
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(payLoad),
        });
        const sales = await res.json();
        let lastSale = sales.length > 0 ? sales.find(sale => sale.isDeleted === false) : null;
        const sameDateSales = sales.filter(s => s.isDeleted === false && s.saleDate === lastSale.saleDate);
        if (sameDateSales.length >= 1) {
            lastSale = sameDateSales.reduce((a, b) => a.qivsSaleId > b.qivsSaleId ? a : b);
        }
        if (lastSale) {
            return `The subject property sold on ${formatDate(lastSale.saleDate, 'DD/MM/YYYY', false)} for ${formatPrice(lastSale.net)}, net plus ${lastSale.chattels ? formatPrice(lastSale.chattels) : 0} chattels.`;
        }
        return '';
    }
    catch (error) {
        throw Error(`Error calling getLastSaleSalad: ${error}}`);
    }
}

function toggleRefreshModal(actionedFrom) {
    showRefreshModal.value = true;
    refreshModalHandler.value = actionedFrom;
    refreshModalMessage.value = 'This will refresh the saved ' + actionedFrom + ' write-up';
}
</script>

<template>
    <div style="padding-top: 1rem;">
        <div class="qv-label-textarea"
             @focusin="() => showDescriptionWarning = true"
             @focusout="() => showDescriptionWarning = false"
        >
            <div class="qv-label-icon">
                <div data-cy="updated-property-description" class="qv-flex-row">
                    <p class="qv-label-text">Updated Property Description</p>
                    <div>
                        <badge class="qv-mb-1 warning" icon="priority_high" :show-on="showDescriptionWarning" :show-on-hover="true">
                                <p class="qv-font-semibold qv-text-nowrap">This description is included in the Objection Report</p>
                        </badge>
                    </div>
                </div>
                <span :class="{ 'qv-disabled': reloadingPropertyDescription }"><i class="material-icons refresh-icon"
                        @click="toggleRefreshModal('Property Description')">&#xE5D5;</i></span>
            </div>
            <textarea v-auto-grow v-model="propertyDescriptionGetSet" maxlength="5000" @change="saveChanges"
                      :class="{ 'qv-disabled': reloadingPropertyDescription }" />
        </div>
        <div class="qv-label-textarea">
            <div class="qv-label-icon">
                <label>Valuation Conclusions</label>
                <span><i class="material-icons refresh-icon" @click="toggleRefreshModal('Valuations Conclusions')">&#xE5D5;</i></span>
            </div>
            <textarea v-auto-grow v-model="valuationConclusionsGetSet" maxlength="5000" @change="saveChanges" />
        </div>
        <alert-modal v-if="showRefreshModal" caution>
            <h3>{{ refreshModalMessage }}</h3>
            <p>Are you sure?</p>
            <template #buttons>
                <div class="alertButtons">
                    <button class="mdl-button mdl-button--mini lefty" @click="showRefreshModal = false">
                        NO
                    </button>
                    <button class="mdl-button mdl-button--mini"
                            @click="refreshModalHandler === 'Property Description' ? regeneratePropertyDescription() : regenerateValuationConclusion()"
                    >YES
                    </button>
                </div>
            </template>
        </alert-modal>
    </div>
</template>

<style lang="scss">
.qv-label-icon {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    label {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #0e3a83;
        width: 100%;
        height: 2.2rem;
    }
}

.qv-label-textarea {
    textarea {
        resize: none;
        min-height: 100px;
        overflow: hidden;
        border: 1px solid #d2d2d2;
        border-radius: 5px;
        font-size: 1.2rem;
        padding: 0.5rem;
    }
}

.refresh-icon {
    background-color: var(--qv-color-mediumblue);
    color: white;
    border-radius: 5px;
    font-size: 2rem;
    padding: 0.25rem 0.5rem;
}
</style>

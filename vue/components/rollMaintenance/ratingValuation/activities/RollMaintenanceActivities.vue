<template>
    <div>
        <validation-header-message
            :validation-set="activitySaveResult"
            :show-errors="true"
            action="update the consent"
        />
        <table data-cy="current-roll-maintenance-activity-table" class="table">
            <tr>
                <th class="building-consent-list--selected">
                    <span title="Consents that are related to the valuation">Linked</span>
                </th>
                <th class="building-consent-list--consent-number">
                    <sort-header label="BC Number" />
                </th>
                <th class="building-consent-list--description">
                    <sort-header label="Description" />
                </th>
                <th class="building-consent-list--plans-drawn">
                    <sort-header label="Plans" />
                </th>
                <th class="building-consent-list--cost">
                    <sort-header label="Cost" />
                </th>
                <th class="building-consent-list--status">
                    <sort-header label="Status" />
                </th>
                <th class="building-consent-list--status-icons" />
                <th class="building-consent-list--issue-date">
                    <sort-header label="Issue Date" />
                </th>
                <th class="building-consent-list--floor-area">
                    <sort-header label="TFA" />
                </th>
                <th class="building-consent-list--plan-status">
                    <sort-header label="Plan Status" />
                </th>
                <!-- <th class="building-consent-list--inspection-required">
                    <sort-header label="Needs Inspection?" />
                </th> -->
                <th class="building-consent-list--construction-complete">
                    <sort-header title="Construction Completed?" class="conscomp" label="Cons
                    Comp?"/>
                </th>
                <th class="building-consent-list--compliance-certificate-issued">
                    <sort-header label="Has CCC?" />
                </th>
            </tr>
            <tr
                v-for="activity in potentialActivities"
                :key="activity.id"
                :class="isLinked(activity.id) ? 'linked' : ''"
            >
                <td
                    class="building-consent-list--selected"
                >
                    <input
                        type="checkbox"
                        :disabled="saving || !canEdit"
                        :checked="isLinked(activity.id)"
                        @click.prevent="isLinked(activity.id)
                            ? unlinkRollMaintenanceActivity(activity.id)
                            : linkRollMaintenanceActivity(activity.id)
                        "
                    >
                </td>
                <td
                    class="building-consent-list--consent-number"
                    :href="getRouteUrl({
                        name: 'roll-maintenance-activity',
                        params: { id: activity.id },
                    })"
                    @click.prevent.stop="openRouteInNewTab({
                        name: 'roll-maintenance-activity',
                        params: { id: activity.id }
                    })"
                >
                    <a>{{ activity.buildingConsent.consentNumber | emptyToDash }}</a>
                </td>
                <td class="building-consent-list--description">
                    {{ activity.buildingConsent.description }}
                </td>
                <td class="building-consent-list--plans-drawn">
                    <a
                        href="#"
                        @click.prevent.stop="openQivsFloorPlans(activity.ratingUnit.qpid)"
                    >{{ activity.buildingConsent.plansObtained | yesno('No') }}</a>
                </td>
                <td class="building-consent-list--cost">
                    {{ activity.buildingConsent.cost | currency }}
                </td>
                <td class="building-consent-list--status">
                    {{ activity.status.description }}
                </td>
                <td class="building-consent-list--status-icons">
                    <i v-if="activity.needsMoreInformation" class="icon icon-needsMoreInformation" title="Notes for Valuer"></i>
                    <i v-if="activity.needsInspection" class="icon icon-needsInspection" title="Needs Inspection"></i>
                </td>
                <td class="building-consent-list--issue-date">
                    {{ activity.initiatedDate | date }}
                </td>
                <td class="building-consent-list--floor-area">
                    {{ activity.buildingConsent.totalFloorArea }}
                </td>
                <td class="building-consent-list--plan-status">
                    <multiselect
                                v-if="planStatusHasLoaded"
                                class="planStatusDropdown"
                                v-model="planStatusObj[activity.id]"
                                :options="Object.values(options)"
                                :close-on-select="true"
                                select-label="⏎ select"
                                deselect-label="⏎ remove"
                                @input="updatePlanStatusState($event, activity)"/>
                </td>
                <!-- <td class="building-consent-list--inspection-required">
                    <input
                        v-if="isLinked(activity.id)"
                        type="checkbox"
                        :checked="activity.needsInspection"
                        :disabled="activitySaving || !canEdit"
                        @click.prevent.stop="toggleInspectionState(activity)"
                    >
                </td> -->
                <td class="building-consent-list--construction-complete">
                    <input
                        v-if="isLinked(activity.id)"
                        type="checkbox"
                        :checked="activity.buildingConsent.constructionComplete"
                        :disabled="activitySaving || activity.buildingConsent.complianceCertificateIssued || !canEdit"
                        @click.prevent.stop="toggleConstructionComplete(activity)"
                    >
                </td>
                <td class="building-consent-list--compliance-certificate-issued">
                    <input
                        v-if="isLinked(activity.id)"
                        type="checkbox"
                        :checked="activity.buildingConsent.complianceCertificateIssued"
                        :disabled="activitySaving || !canEdit"
                        @click.prevent.stop="toggleComplianceCertificateIssued(activity)"
                    >
                </td>
            </tr>
        </table>
     </div>
</template>

<script>
import { mapState } from 'vuex';
import { openQivsInNewTab, openUrlInNewTab } from '../../../../utils/QivsUtils';
import Multiselect from 'vue-multiselect';

export default {
    components: {
        'sort-header': () => import('../../../common/SortHeader.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "ValidationHeaderMessage" */ '../../../common/form/ValidationHeaderMessage.vue'),
        Multiselect,
    },
    data() {
        return {
            planStatusObj: {},
            options: {
                'plansAvailable': 'Plans Drawn',
                'plansNeeded':'Plans Required',
                'plansUnavailable':'Plans Unavailable',
                'plansNotRequired': 'Plans Not Required',
                'plansRequestedWithTa': 'Plans Requested with TA'
            },
            planStatusHasLoaded: false,
        }
    },
    props: {
        loadOnMount: {
            type: Boolean,
            required: true,
        },
        canEdit: {
            type: Boolean,
            required: true,
        },
    },
    computed: {
        ...mapState('ratingValuation', [
            'loading',
            'ratingValuation',
            'saving',
            'propertyActivities',
        ]),
        ...mapState('rollMaintenanceActivity', { activitySaving: 'saving', activitySaveResult: 'saveResult' }),
        potentialActivities() {
            return this.propertyActivities.filter(activity => activity.activityType.code == 'BC' && (activity.activeConsentIds.includes(activity.id) || (activity.status.code !== 'DONE' && activity.status.code !== 'CANCELED')));
        },
        qpid() {
            return this.ratingValuation.ratingUnit.qpid;
        },
    },
    watch: {
        qpid() {
            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
    },
    async created() {
        this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        await this.setPlanStatuses();
    },
    mounted() {
            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
    },
    methods: {
        updatePlanStatusState(event, activity){
            var self = this;
            var currPropertyIndex = this.getCurrentPropertyIndex(activity.buildingConsent);
            self.resetPlanStatus(currPropertyIndex);
            var id = activity.id;

            switch(event){
                case this.options.plansUnavailable:
                    self.potentialActivities[currPropertyIndex].buildingConsent.plansUnavailable = true;
                    self.planStatusObj[activity.id] = self.options.plansUnavailable;
                    break;
                case this.options.plansAvailable:
                    self.potentialActivities[currPropertyIndex].buildingConsent.plansObtained = true;
                    self.planStatusObj[activity.id] = self.options.plansAvailable;
                    break;
                case this.options.plansNeeded:
                    self.potentialActivities[currPropertyIndex].buildingConsent.plansRequired = true;
                    self.planStatusObj[activity.id] = self.options.plansNeeded;
                    break;
                case this.options.plansNotRequired:
                    self.potentialActivities[currPropertyIndex].buildingConsent.plansNotRequired = true;
                    self.planStatusObj[activity.id] =self.options.plansNotRequired;
                    break;
                case this.options.plansRequestedWithTa:
                    self.potentialActivities[currPropertyIndex].buildingConsent.plansRequestedWithTa = true;
                    self.planStatusObj[activity.id] = self.options.plansRequestedWithTa;
                    break;
            }
            self.togglePlanStatus(activity, self.getKeyByValue(self.options, event));
        },

        getCurrentPropertyIndex(currentConsent){
            var currPropertyIndex;
            $.each(this.potentialActivities, function(index, element){
                if(element.buildingConsent.consentNumber == currentConsent.consentNumber){
                    currPropertyIndex = index;
                }
            });
            return currPropertyIndex;
        },

        getKeyByValue(object, value) {
            return Object.keys(object).find(key => object[key] === value);
        },

         resetPlanStatus(currPropertyIndex){
            this.potentialActivities[currPropertyIndex].buildingConsent.plansUnavailable = false;
            this.potentialActivities[currPropertyIndex].buildingConsent.plansObtained = false;
            this.potentialActivities[currPropertyIndex].buildingConsent.plansRequired = false;
            this.potentialActivities[currPropertyIndex].buildingConsent.plansNotRequired = false;
            this.potentialActivities[currPropertyIndex].buildingConsent.plansRequestedWithTa = false;
        },

         getPlanStatus(){
            var self = this;
            $.each(self.potentialActivities, function(index, element){
                if(element.buildingConsent.plansObtained){
                     self.planStatusObj[element.id] = self.options.plansAvailable;
                }
                else if(element.buildingConsent.plansNotRequired){
                     self.planStatusObj[element.id] =self.options.plansNotRequired;
                }
                else if(element.buildingConsent.plansRequestedWithTa){
                     self.planStatusObj[element.id] = self.options.plansRequestedWithTa;
                }
                else if(element.buildingConsent.plansRequired){
                     self.planStatusObj[element.id] = self.options.plansNeeded;
                }
                else if(element.buildingConsent.plansUnavailable){
                     self.planStatusObj[element.id] = self.options.plansUnavailable;
                }
            });
            self.planStatusHasLoaded = true;
        },
        updateInspectionState(item) {
            this.rollMaintenanceActivity.inspectionState = item.value;
        },
        updateNatureOfWorks(item) {
            this.rollMaintenanceActivity.buildingConsent.natureOfWorks = item.value;
        },
        isLinked(rollMaintenanceActivityId) {
            return this.ratingValuation.rollMaintenanceActivityIds
                .includes(rollMaintenanceActivityId);
        },
        openQivsFloorPlans(qpid) {
            const floorPlanUrl = this.$store.getters['userData/qivsFloorPlanUrl'](qpid);
            openQivsInNewTab(floorPlanUrl);
        },
        openRouteInNewTab(location) {
            openUrlInNewTab(this.getRouteUrl(location));
            return false;
        },
        getRouteUrl(location) {
            return this.$router.resolve(location).href;
        },
        async setPlanStatuses () {
            const self = this;
            await this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities').then(() => {
                setTimeout(() => {
                    self.getPlanStatus();
                }, 1000);
            });
        },
        async linkRollMaintenanceActivity(rollMaintenanceActivityId) {
            this.$store.dispatch('ratingValuation/linkRollMaintenanceActivity', rollMaintenanceActivityId);
        },
        async unlinkRollMaintenanceActivity(rollMaintenanceActivityId) {
            this.$store.dispatch('ratingValuation/unlinkRollMaintenanceActivity', rollMaintenanceActivityId);
        },
        async toggleInspectionState(activity) {
            if(activity.needsInspection)
                await this.$store.dispatch('rollMaintenanceActivity/inspected', activity.id);
            else
                await this.$store.dispatch('rollMaintenanceActivity/requireInspection', {id: activity.id});

            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
        async togglePlansRequired(activity) {
            await this.$store.dispatch('rollMaintenanceActivity/togglePlansRequired', activity.id);
            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
        async togglePlansDrawn(activity) {
            await this.$store.dispatch('rollMaintenanceActivity/togglePlansDrawn', activity.id);
            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
        async togglePlanStatus(activity, event){
            await this.$store.dispatch('rollMaintenanceActivity/togglePlanStatus',{ rollMaintenanceActivityId: activity.id, event: event});
            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
        async toggleConstructionComplete(activity) {
            if(activity.buildingConsent.constructionComplete)
                await this.$store.dispatch('rollMaintenanceActivity/constructionInProgress', activity.id);
            else
                await this.$store.dispatch('rollMaintenanceActivity/constructionComplete', activity.id);

            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
        async toggleComplianceCertificateIssued(activity) {
            if(activity.buildingConsent.complianceCertificateIssued)
                await this.$store.dispatch('rollMaintenanceActivity/noComplianceCertificate', activity.id);
            else
                await this.$store.dispatch('rollMaintenanceActivity/complianceCertificateIssued', activity.id);

            this.$store.dispatch('ratingValuation/loadRelatedRollMaintenanceActivities');
        },
    },
};
</script>
<style lang="scss" scoped>

tr.linked {
    background-color: #ffffef;
}

table.table tr.linked:hover {
    background-color: #ffffcf;
}

td.building-consent-list--consent-number > a {
    margin-left: -1.2rem;
    padding: 1.2rem;

    &:hover {
        box-shadow:0 0 0 .15rem rgba(74,144,226,.25);
        box-sizing: border-box;
        border-radius:.5rem;
        background:rgba(255,255,255,.25);
        text-decoration: underline;
    }
}

td.building-consent-list--plans-drawn a {
    &:hover {
        text-decoration: underline;
    }
}

.planStatusDropdown{
    display: inline-block;
    width: 140px;
}
.building-consent-list {
    &--selected {
        padding: 1rem;
        text-align: left;
    }
    &--consent-number {
        padding: 1rem;
        text-align: left;
        white-space: nowrap;
    }
    &--description {
        padding: 1rem;
        text-align: left;
        word-break: break-word;
    }
    &--cost {
        padding: 1rem;
        text-align: right;
    }
    &--status {
        padding: 1rem;
    }
    &--issue-date {
        padding: 1rem;
    }
    &--floor-area {
        padding: 1rem;
        text-align: right;
    }
    &--plan-status {
        padding: 1rem;
        text-align: center;
    }
    &--plans-drawn {
        padding: 1rem;
        text-align: left;
    }
    &--plans-required {
        padding: 1rem;
        text-align: center;
    }
    &--inspection-required {
        padding: 1rem;
        text-align: center;
    }
    &--construction-complete {
        padding: 1rem;
        text-align: center;
    }
    &--compliance-certificate-issued {
        padding: 1rem;
        text-align: center;
    }
}

</style>

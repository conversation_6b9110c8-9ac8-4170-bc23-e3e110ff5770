<script setup>
import { inject, unref, computed } from 'vue';
import SortHeader from '../../../common/SortHeader.vue';
import { formatPrice, formatDate } from '@/utils/FormatUtils';

const props = defineProps({
    objections: {
        type: Array,
        default: () => ([]),
    },
    savingLink: {
        type: Boolean,
        default: false,
    },
    isJobReadOnly: {
        type: Boolean,
        default: false,
    },
    expandObjection: {
        type: Boolean,
        default: true,
    },
    isActionRecord: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['link-objection']);
const ratingValuation = inject('ratingValuation');
const linkedObjection = inject('linkedObjection');
const isLinkedObjectionReadyToValue = computed(() => linkedObjection.value?.valJobStatus === 'Ready to Value');

function objectorName(objection) {
    return [objection.firstName, objection.lastName].filter(e => e).join(' ');
}

function dateReceived(objection) {
    return formatDate(objection.dateReceived, 'DD/MM/YYYY', false);
}

function kitchenYear(objection) {
    return objection.kitchenYear || '-';
}

function rentalIncome(objection) {
    return formatPrice(objection.rentalIncome) || '-';
}

function estimatedCapitalValue(objection) {
    return formatPrice(objection.estimatedCapitalValue) || '-';
}

function estimatedLandValue(objection) {
    return formatPrice(objection.estimatedLandValue) || '-';
}

function objectorValueOfImprovements(objection) {
    return objection?.estimatedCapitalValue && objection?.estimatedLandValue ? formatPrice(objection.estimatedCapitalValue - objection.estimatedLandValue) : '-';
}

function isLinked(activityId) {
    const valuation = unref(ratingValuation);
    if (!valuation || !valuation.rollMaintenanceActivityIds) {
        return false;
    }
    return valuation.rollMaintenanceActivityIds.includes(activityId);
}
function toggleLinked(activityId, objectionId) {
    emit('link-objection', { toLink: !isLinked(activityId), rollMaintenanceActivityId: activityId, objectionId: objectionId });
}

function isMaintenanceObjection(objection) {
    return cleanQivsString(objection.objectionType) == 'maintenance';
}

function isObjectionReadyToValue(objection) {
    return objection.valJobStatus === 'Ready to Value';
}

function cleanQivsString(str) {
    return str.trim().toLowerCase();
}

function singleLinkedObjection(activityId) {
    const objectionIds = props.objections.map(objection => objection.id);
    const linkedObjections = objectionIds.filter(id => ratingValuation.value.rollMaintenanceActivityIds.includes(id));
    return linkedObjections?.length == 1 && linkedObjections?.[0] == activityId
}

</script>

<template>
    <div class="qv-objection-details">
        <table class="table">
            <tr>
                <th v-if="!isActionRecord" style="text-align: center">
                    <span>Linked</span>
                </th>
                <th>
                    <sort-header label="Objector Name" />
                </th>
                <th>
                    <sort-header label="Type" />
                </th>
                <th>
                    <sort-header label="Date Received" />
                </th>
                <th>
                    <sort-header label="Kitchen Age" />
                </th>
                <th>
                    <sort-header label="Bathrooms" />
                </th>
                <th>
                    <sort-header label="Bedrooms" />
                </th>
                <th>
                    <sort-header label="Rental $" />
                </th>
                <th>
                    <sort-header label="Objector CV" />
                </th>
                <th>
                    <sort-header label="Objector LV" />
                </th>
                <th>
                    <sort-header label="Objector VI" />
                </th>
            </tr>
            <template v-for="(objection, index) in props.objections">
                <tr v-if="(!isActionRecord && (expandObjection || index === 0)) || (isActionRecord && isLinked(objection.activityId))"
                    :class="{ 'red-highlight': isMaintenanceObjection(objection) }">
                    <td v-if="!isActionRecord" style="text-align: center">
                        <input type="checkbox" title="Linking of objections must be at 'ready to value' status"
                               :checked="isLinked(objection.activityId)"
                               :disabled="isMaintenanceObjection(objection)
                                   || savingLink
                                   || singleLinkedObjection(objection.activityId)
                                   || isJobReadOnly
                                   || !isLinkedObjectionReadyToValue
                                   || !isObjectionReadyToValue(objection)"

                            @click.prevent="toggleLinked(objection.activityId, objection.objectionId)">
                    </td>
                    <td>{{ objectorName(objection) }}</td>
                    <td>{{ objection.objectionType || '-' }}</td>
                    <td>{{ dateReceived(objection) }}</td>
                    <td>{{ kitchenYear(objection) }}</td>
                    <td>{{ objection.numberOfBathrooms || '-' }}</td>
                    <td>{{ objection.numberOfBedrooms || '-' }}</td>
                    <td>{{ rentalIncome(objection) }}</td>
                    <td>{{ estimatedCapitalValue(objection) }}</td>
                    <td>{{ estimatedLandValue(objection) }}</td>
                    <td>{{ objectorValueOfImprovements(objection) }}</td>
                </tr>
                <tr v-if="(!isActionRecord && expandObjection) || (isActionRecord && isLinked(objection.activityId))">
                    <td colspan="11" style="margin-bottom: 1rem;">
                        <div data-cy="recent-work" class="qv-label-block">
                            <div>Recent Work</div>
                            <div>{{ objection.recentChanges || '-' }}</div>
                        </div>
                        <div class="qv-label-block">
                            <div>Reason</div>
                            <div>{{ objection.reasonForObjecting ? objection.reasonForObjecting : objection.qivsReason || '-' }}</div>
                        </div>
                    </td>
                </tr>
            </template>
        </table>
    </div>
</template>

<style lang="scss">
.qv-objection-details {
    :is(th, td) {
        padding: 1rem;
        text-align: left;
    }

    .router & table.table tr {
        height: initial;
    }
}

.qv-label-block {
    >div:first-child {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #0e3a83;
        height: 2.2rem;
    }
}
</style>

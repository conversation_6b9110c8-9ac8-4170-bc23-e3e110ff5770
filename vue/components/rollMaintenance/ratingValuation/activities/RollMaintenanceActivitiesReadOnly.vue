<template>
    <table class="table">
        <tr>
            <th class="building-consent-list--consent-number">
                <sort-header label="BC Number" />
            </th>
            <th class="building-consent-list--description">
                <sort-header label="Description" />
            </th>
            <th class="building-consent-list--inspection">
                <sort-header label="Inspection Status" />
            </th>
            <th class="building-consent-list--cost">
                <sort-header label="Cost" />
            </th>
            <th class="building-consent-list--issue-date">
                <sort-header label="Issue Date" />
            </th>
            <th class="building-consent-list--floor-area">
                <sort-header label="TFA" />
            </th>
            <th class="building-consent-list--actioned-date">
                <sort-header label="Actioned Date" />
            </th>
        </tr>
        <tr
            v-for="activity in BCActivities"
            :key="activity.id"
        >
            <td
                class="building-consent-list--consent-number"
                @click="navigateToRollMaintenanceActivity(activity.id)"
            >
                {{ activity.buildingConsent.consentNumber }}
            </td>
            <td class="building-consent-list--description">
                {{ activity.buildingConsent.description | emptyToDash }}
            </td>
            <td class="building-consent-list--inspection">
                {{ activity.inspectionState.description | emptyToDash }}
            </td>
            <td class="building-consent-list--cost">
                {{ activity.buildingConsent.cost | currency }}
            </td>
            <td class="building-consent-list--issue-date">
                {{ activity.initiatedDate | date }}
            </td>
            <td class="building-consent-list--floor-area">
                {{ activity.buildingConsent.totalFloorArea | emptyToDash }}
            </td>
            <td class="building-consent-list--actioned-date">
                {{ activity.actionedDate | date }}
            </td>
        </tr>
    </table>
</template>

<script>
import { mapState } from 'vuex';
import { openUrlInNewTab } from '../../../../utils/QivsUtils';

export default {
    components: {
        'sort-header': () => import('../../../common/SortHeader.vue'),
    },
    computed: {
        ...mapState('ratingValuation', [
            'loading',
            'ratingValuation',
            'saving',
            'valuationActivities',
        ]),
        id() {
            return this.ratingValuation.id;
        },
        BCActivities() {
            return this.valuationActivities.filter(activity => activity.activityType.code == 'BC' && activity.status.code !== 'CANCELED');
        }
    },
    watch: {
        id() {
            this.$store.dispatch('ratingValuation/loadRollMaintenanceActivities');
        },
    },
    mounted() {
        this.$store.dispatch('ratingValuation/loadRollMaintenanceActivities');
    },
    methods: {
        navigateToRollMaintenanceActivity(rollMaintenanceActivityId) {
            this.openRouteInNewTab({
                name: 'roll-maintenance-activity',
                params: {
                    id: rollMaintenanceActivityId,
                },
            });
        },
        openRouteInNewTab(location) {
            openUrlInNewTab(this.getRouteUrl(location));
            return false;
        },
        getRouteUrl(location) {
            return this.$router.resolve(location).href;
        },
    },
};
</script>

<style lang="scss" scoped>

.building-consent-list--consent-number {

    &:hover {
        box-shadow:0 0 0 .15rem rgba(74,144,226,.25);
        box-sizing: border-box;
        border-radius:.5rem;
        vertical-align: middle;
        background:rgba(255,255,255,.25);
        /* Xbox-shadow:0 0 0 .15rem rgba(255,111,0,.5); */
    }
}

.building-consent-list {
    &--consent-number {
        padding: 1rem;
        width: 10%;
        text-align: left;
    }
    &--description {
        padding: 1rem;
        width: 55%;
        text-align: left;
        word-break: break-word;
    }
    &--inspection {
        padding: 1rem;
        width: 10%;
        text-align: right;
    }
    &--cost {
        padding: 1rem;
        width: 10%;
        text-align: right;
    }
    &--issue-date {
        padding: 1rem;
        width: 10%;
        text-align: right;
    }
    &--floor-area {
        padding: 1rem;
        width: 5%;
        text-align: right;
    }
    &--actioned-date {
        padding: 1rem;
        width: 10%;
        text-align: right;
    }
}

</style>

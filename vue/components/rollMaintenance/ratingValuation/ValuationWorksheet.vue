<script setup>
import { computed, ref, unref } from 'vue';
import { useStore } from '@/composables/useStore';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router/composables';
import { WorksheetForm, WorksheetFormMaori } from '@/components/rollMaintenance/ratingValuation/worksheet';

import RollMaintenanceActivities from './activities/RollMaintenanceActivities.vue';
import Expander from '@/components/common/Expander.vue';
import { injectRatingValuationContext } from '@/components/rollMaintenance/ratingValuation/context';
import { RatingValuation } from '@quotable-value/validation';

const { STEPS } = RatingValuation
const expandConsents = ref(false);
const validationHeader = ref();
const context = injectRatingValuationContext();

const userDataStore = useStore('userData');
const propertyStore = useStore('property');
const propertyDraftStore = useStore('propertyDraft');
const ratingValuationStore = useStore('ratingValuation');
const route = useRoute();
const router = useRouter();

const { isInternalUser } = userDataStore.state;
const { property } = propertyStore.state;
const { isMaoriLand } = propertyStore.getters;
const {
    ratingValuation,
    isLoading,
    isSaving,
    validationSet,
    exception,
    propertyActivities,
} = ratingValuationStore.state;

const { propertyDetail } = propertyDraftStore.state;

const hasLoaded = computed(() => {
    return (
        !isLoading
        && ratingValuation.value && route.params.id === ratingValuation.value.id
    );
});

const propertyId = computed(() => {
    if (hasLoaded.value) {
        return ratingValuation.value.ratingUnit.propertyId;
    }

    return null;
});

function cancel() {
    router.push({ name: 'roll-maintenance' });
}

async function onWorksheetChanged() {
    await context.validate();
}

async function onSaveBetweenSteps() {
    const saved = await context.saveBetweenSteps(STEPS.WORKSHEET, true)
    ratingValuation.value.ratingValuationComponents = ratingValuation.value.ratingValuationComponents
        .map((component, index) => ({ ...component, id: index }));
    return saved;
}

onBeforeRouteLeave(async (to, from, next) => {
    const valuationJobPages = ['rating-valuation-comparable-properties', 'rating-valuation-worksheet', 'rating-valuation-writeup', 'rating-valuation-property-details'];
    if (valuationJobPages.includes(to.name)) {
        const saved = await onSaveBetweenSteps();
        if (!saved) {
            return;
        }
    }
    next();
});
</script>

<template>
    <div>
        <div class="qv-worksheet">
            <div class="mdl-shadow--3dp form-section">
                <div class="col-container">
                    <expander v-model="expandConsents" class="righty" />
                    <h1 class="title">
                        Building Consents
                    </h1>
                    <roll-maintenance-activities
                        v-show="expandConsents"
                        :can-edit="false"
                        :load-on-mount="true"
                    />
                </div>
            </div>
            <div class="mdl-shadow--3dp form-section">
                <div class="col-container">
                    <div class="col-row">
                        <div class="col col-6">
                            <h1 class="title">
                                Valuation Worksheet
                            </h1>
                        </div>
                        <div class="col col-6 message-right-italic">
                            <p>Changes to property data should be made on the Draft Property Details screen.</p>
                            <p>Changes to the main net rate should be made on the Comparable Properties screen.</p>
                        </div>
                    </div>
                </div>
                <div  class="qv-px-3">
                    <WorksheetFormMaori v-if="isMaoriLand" :property="property" :property-detail="propertyDetail" :validation-set="validationSet" :value="ratingValuation" @changed="onWorksheetChanged" />
                    <WorksheetForm v-else :property="property" :property-detail="propertyDetail" :validation-set="validationSet" :value="ratingValuation" @changed="onWorksheetChanged" />
                </div>
                <div class="QVHV-buttons col-container">
                    <div :class="[isSaving || isLoading ? 'disabled' : '']" class="QVHV-buttons-right" data-cy="valuation-save-as-draft">
                        <button class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored" data-cy="valuation-save-as-draft-btn" @click="context.save()">
                            Save As Draft
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

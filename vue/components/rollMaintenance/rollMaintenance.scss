@use "../../qv-colors" as qv-colors;
@use "../../qv-text" as qv-text;
@use "../../qv-layout" as qv-layout;


/*  This is a temporary collection of styling that is used to consolidate BC details, BC search and Draft Property.
    It generally follows the pattern in "Draft Property".
    It demonstrates a few issues that would be good to do generically.
 */

:root {
  --qv-input-height: 3.9rem;
}

/* New stuff inside old stuff */
.legacy-scope {
  /* At the moment a Container might be inside a Monarch wrapping style or hard against property banner above so might be helpful to knock out any vertical margin aby default? */
  .col-container {
    margin-top: 0;
  }

  label > span {
    margin: 0;
  }

  label > span.label {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--qv-color-blue);
    height: 2.2rem;
  }

  h2 {
    margin-left: 15px;
    margin-bottom: 0;
  }

  h3 {
    margin-left: 15px;
  }

  h4 {
    margin-left: 15px;
    margin-top: 1.3rem;
    font-size: 1.3rem;
    font-weight: 600;
    line-height: 1.3;
  }

  .property-draft-section {
    border-bottom: .1rem dashed #899ac5;
  }
}

/* Scope everything to just new stuff ... */
.router {
  /* At the moment a Container might be inside a Monarch wrapping style or hard against property banner above so might be helpful to knock out any vertical margin aby default? */
  .col-container {
    margin-top: 0;
  }

  /* If a container is inside a column it is helpful to be able to collapse padding */
  .col-container.col-collapse {
    margin: 0;
    margin-top: -1rem;
    margin-left: -15px;
    margin-right: -15px;
    padding: 0;
  }

  label > input, label > select, label .mx-datepicker .mx-input-wrapper input {
    height: var(--qv-input-height);
  }

  .col {
    padding-left: 5px;
    padding-right: 5px;
  }

  .col-row {
    padding-top: 0;
  }

}

.router {

  /* Override Monarch wrapper to compact it a bit */
  &.contentWrapper {
    margin-top: 0;
    padding-top: 1rem;
  }

  .colHeader.center-header {
    text-align: center;
  }

  /* Override Monarch margins which are way too much. */
  .QVHV-buttons {
    margin-top: 0;
    margin-bottom: 0;
  }

  /* Override Monarch spinner which pulls it -4 rem up on to content above. */
  .loadingSpinner {
    margin-top: 1rem;
    display: block;
  }

  /* Set all text areas used as "fields" to match input fonts */
  label textarea {
    font-family: 'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif !important;
  }

  /* Base styling copied from draft property */
  label {

    .label {
      display: block;
    }

    .field {
      display: block;
      font-size: 14px;
      line-height: 2.8em;
    }

    .readonly-text {
      line-height: 2.8em;
    }

    input, textarea, select, .mx-datepicker input {
      font-size: 1.2rem;
      padding: 0.5rem;
      border-radius: 5px;
      border: 1px solid #e8e8e8;
      background-color: #fff;
      margin: 0;
      width: 100%;

      &[readonly] {
        background-color: #eee;
      }
    }

    .multiselect .multiselect__tags input.multiselect__input,
    .multiselect.multiselect--disabled .multiselect__tags input.multiselect__input {
      border: none;
    }

    .mx-datepicker {
      width: 100%;
    }

  }

  /* This copies existing monarch styling onto new pattern*/
  label {
    .label {
      font-size: 1.1rem;
      line-height: 1.6;
      color: var(--qv-color-blue);
      height: 2.2rem;
    }

    input[type=text],
    input[type=number],
    textarea,
    select {
      font-size: 1.2rem;
      padding: .5rem;
      border: solid 1px #d2d2d2;
    }

    input[type=checkbox] {
      width: 2.5rem;
      background-color: #fff;
    }

    input[type=radio] {
      width: 1.3rem;
      margin: -3px 5px 0 15px;
      vertical-align: middle;
    }

    textarea {
      overflow: auto;
    }
  }

  /* Red box for validation errors */
  label {
    input.error {
      border: 1px solid #f00 !important;
    }
  }

  h2.section-title {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 0;
    margin-top: 0.5rem;
  }

  // ¯\_(ツ)_/¯
  h3.section-title {
    color: var(--qv-color-darkblue);
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  /* Temporary colors for errors taken from bootstrap. */
  .bAlert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
  }

  .bAlert-warning {
    color: #9c6500;
    background-color: #ffeb9c;
    border-color: #ffe471;
  }

  .bAlert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
  }

  .exception-message {
    font-size: 1.5em;
  }

  /* Special styling for the property banner proposed values slot */
  .md-summaryHeader ul.proposedValues {
    padding: 0 0 1.2rem .8rem;
    width: 55rem;

    li {
      display: inline-block;
      font-size: 1.8rem;
      margin: 0 .7rem;
      width: calc(25% - 1.6rem);
      vertical-align: top;
      color: #fff;

      label {
        display: block;
        font-size: 1.1rem;
        line-height: 2.4;
        border-bottom: .1rem solid rgba(255, 255, 255, .2);
        margin-bottom: .35rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      span {
        font-size: 1.2rem;
      }
    }
  }

  /* Special address hover copied from legacy Monarch - narrowed to be a clickable address */
  tr:hover a > .fullAddress {
    box-shadow: 0 0 0 .15rem rgba(74, 144, 226, .25);
    box-sizing: border-box;
  }

  tr:hover a > .fullAddress:hover {
    color: #214d90;
    background: rgba(255, 255, 255, .25);
    box-shadow: 0 0 0 .15rem rgba(255, 111, 0, .5);
  }

  tr:hover a > .fullAddress::after {
    position: absolute;
    top: .9rem;
    right: .8rem;
    content: "";
    background-image: url(../../../public/images/monarchLogo-mini.png);
    background-repeat: no-repeat;
    background-size: 100%;
    width: 2rem;
    height: 2rem;
  }

  tr:hover a > .fullAddress:hover::after {
    color: #fc932f;
  }

  /* Quick n dirty general message */
  .message {
    border: 1px solid transparent;
    border-radius: .25rem;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    margin-top: 0.5rem;
    font-size: 1.3rem;
  }

  .message-error {
    color: #721c24;
    border-color: #f5c6cb;
    background-color: #f8d7da;
  }

  .message-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
  }

  .taRollMaintenance-title span {
    padding: 1.6rem 2rem .6rem;
    min-width:180px;
    margin-right: 0 !important;
    margin-bottom: -.1rem;
    box-shadow: none;
    height: 5.6rem;
    box-sizing: border-box;
    font-size: 1.6rem;
    color: #fff;
    background-color: transparent;
    width: auto;
    font-weight: bold;
  }

  .proposedValues ul {
    display: inline-grid;
  }

  .proposedValues.error {
    background-color: #fc3d39;
    padding-left: 0.3rem;
  }

  .draft-property {
    margin-bottom: 2rem;
  }

  .property-draft-section {
    border-bottom: .1rem dashed #899ac5;
    padding-top: 0;
    padding-bottom: 0.8rem;
    display: table;
    width: 100%;
  }

  .validation-header-message--warnings {
    font-size: 0.9em;
    overflow: auto;
  }

  .derived-title {
    font-weight: 600;
    padding-left: 5px;
    padding-top: 5px;
  }

  .derived-field {
    cursor: auto;
  }

  .form-stale {
    background-color: #eee;
  }

  .form-stale-warning {
    text-align: center;
    font-size: 0.8em;

    &--save-button {
      height: 28px;
      line-height: 28px;
      font-size: 0.9em;
    }
  }

  .generate-modal-input--small {
    width: 30%;
    display: inline-block;
    margin-right: 1em;
  }

  .button--small {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
  }
}

.qv-icon-row {
  display: flex;
  align-content: center;
  gap: 1rem;

  i.qv-icon {
    color: var(--qv-color-mediumblue);
    font-size: 2rem;
    display: flex;
  }
}

.md-qivs-wide, li.md-qivs-wide {
  width: 100%;
  margin: 0 0 2px;
}

.md-qivs {
  text-transform: uppercase;
  margin: 0 0 2px;
  text-align: center;

  label {
    font-size: 1rem;
  }
}

.md-qivs.info {
  color: var(--qv-color-blue);
  background-color: #fff;
  border: solid 1px var(--qv-color-blue);
}

.md-qivs.success {
  color: var(--qv-color-success);
  background-color: #fff;
  border: solid 1px var(--qv-color-success);
}

.md-qivs.danger, .md-qivs.error {
  color: #fff;
  background-color: var(--qv-color-error);
  border: solid 1px var(--qv-color-error);
}

.header-row {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, .9);
  background-color: #283c64;
  padding: 0 1rem;
}

.header-row > div:first-of-type {
  padding-left: 1rem;
}

.message-right-italic {
  text-align: right;
  font-style: italic;
  font-size: 1.3rem;
}

.qv-worksheet {
  :is(h2, h4) {
    margin-bottom: 5px;
  }

  .form-section {
    margin-bottom: 2rem;
  }
}

.qv-box-header {
  display: flex;
  flex-direction: row;
  gap: 1rem;

  .title {
    flex-grow: 1;
  }

  .qv-button-row {
    align-self: center;
  }
}

.qv-button-row {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
}

.action-buttons {
  margin-top: 1rem;
  margin-right: 1rem;

  li {
    margin-bottom: 1rem;
    margin-top: 0;
  }

  button {
    width: 100%;
  }
}

.status-list {
  margin-right: 1rem;
  margin-bottom: 2rem;

  li {
    margin-bottom: 1rem;
    margin-top: 0;
  }

  .md-qivs {
    display: block;
    margin: 0 0 2px;

    label {
      cursor: default;
    }
  }
}

.qv-comparable-sale-form {
  label input {
    height: var(--qv-input-height);
  }
}

.qv-input-pair {
  align-items: center;
  gap: 0.5rem;
}

.qv-input-label {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--qv-color-blue);
  height: 2.2rem;
}

.qv-input {
  font-size: 1.2rem;
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
  background-color: #fff;
  margin: 0;
  width: 100%;
  height: var(--qv-input-height);

  &[readonly] {
    background-color: #eee;
  }
}

.qv-input-group {
  display: flex;

  & > * {
    height: var(--qv-input-height);
  }

  & > :first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  & > :not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.qv-toggle {
  display: flex;

  input[type="radio"] {
    display: none;

    &:checked + label {
      background-color: var(--qv-color-mediumblue);
      cursor: default;
    }
  }

  label {
    color: #fff;
    cursor: pointer;
    min-width: 6rem;
    transition: background-color 200ms;
    background-color: var(--qv-color-darkblue);
    text-align: center;

    &:first-of-type {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &:not(:first-of-type) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.qv-comp-sale-inline-form {
  display: grid;
  grid-template-columns: 8fr 3fr 1fr;
  column-gap: 1rem;
}

.qv-nav-buttons-container {
  display: grid;
  gap: 0.5rem;
  grid-template-columns: repeat(3, 1fr);
}

.qv-nav-button {
  flex: 0 0 25%;
  font-size: 1rem;
  color: #fff;
  line-height: 1.8;
  border: none;
  border-radius: 0.3rem;
  transition: opacity .2s ease;

  i {
    display: inline-block;
    font-size: 1.4rem;
    vertical-align: text-top;
    width: 1.5rem;
  }
}

.qivs-button {
  background-color: #fc932f;
}

.monarch-button {
  background-color: rgb(74, 144, 226);
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.qv-comparable-sales-list {
  table.table tr:first-child {
    height: initial;
  }

  table.table td:first-child {
    width: 2rem;
  }

  table.table :is(th, td):not(:first-child) {
    padding-left: 0.5rem;
  }
}

tr.yellow-highlight {
  background-color: rgba(255,255,245,1);
}

tr.red-highlight {
  background: #ffd0d0;
}

.qvtd-sort-header.active .icon {
  display: inline-block;
  position: relative;
  top: 0.45rem;
}

.qv-disabled {
  opacity: .75;
  pointer-events: none;
}

.qv-non-clickable {
  pointer-events: none;
  .opaque {
    opacity: .75;
  }
  .grey-out {
    background: var(--qv-color-light);
  }
}
.qv-rolls-badge {
  position: absolute;
  right: 0;
  padding: 0.3rem 0.8rem;
  color: #fff;
  background: #7eb4ec;
  border-radius: 1.2rem;
  translate: 50% -50%;
  font-weight: bold;
  line-height: initial;
}

.qv-rolls-badge-parent {
  position: relative;
  overflow: initial;
}

.qv-sidebar-action-button {
  width: 100%;
  margin: 0 0 2px;
}

.qv-radio-button {
  display: inline-block;
  -webkit-appearance: none;
  border-radius: 50%;
  box-shadow: 0 0 0 0.3rem rgba(0, 0, 0, .54);
  width: 1.4rem;
  height: 1.4rem;
  transform: translateY(-0.3rem);

  &:checked {
    color: #fff;
    background: #346cae;
    box-shadow: 0 0 0 0.3rem rgba(74, 144, 226, 1), inset 0 0 0 0.23rem #fff;
    width: 1.4rem;
    transform: translateY(-0.3rem);
  }
}

.qv-radio-button-light {
  @extend .qv-radio-button;
  box-shadow: 0 0 0 0.3rem var(--qv-color-light);

  &:checked {
    background: var(--qv-color-light);
    box-shadow: 0 0 0 0.3rem var(--qv-color-light), inset 0 0 0 0.23rem var(--qv-color-mediumblue);;
  }
}

.qv-reinstatement-fieldset {
  label {
    padding-left: 0.5rem;
    margin-right: 1rem;
    font-size: 14px !important;
  }
}

.qv-divider {
  border-width: 0.25rem;
  border-radius: 0px;
  border-style: solid;
  margin: 2rem 0 2rem 0;
}


.qv-refresh-icon {
  padding: 0.01rem;
  border-radius: 2px;
  font-size: 1.75rem;
  background-color: var(--qv-color-mediumblue);
  color: white;
}

.qv-tag {
  padding: 0.3rem 0.6rem;
  border: none;
  border-radius: 0.3rem;
  min-width: 3rem;
  text-align: center;

  &-base {
    @extend .qv-tag;
    min-width: 3rem;
  }

  &-lg {
    @extend .qv-tag;
    min-width: 6rem;
  }
}

.qv-read-only {
  opacity: 0.75;
  pointer-events: none;
  &-opaque {
    opacity: 1;
  }
}

.qv-read-only-wrapper {
  cursor: not-allowed;
}

.qv-objection-job-validation-errors {
  font-size: 1.3rem;
  padding: 1rem;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 3px;
  border: 1px solid #ffb5bc;
}

.qv-objection-job-validation-warnings {
  font-size: 1.3rem;
  padding: 1rem;
  color: #723c1c;
  background-color: #ffd4c4;
  border-radius: 3px;
  border: 1px solid #ffb498;
}

.qv-number-circle {
  display: inline-block;
  color: white;
  width: 27.5px;
  line-height: 25px;
  border-radius: 50%;
  text-align: center;
  font-size: 12px;
}

.qv-number-circle-error {
  color: #fff;
  background-color: var(--qv-color-error);
  border: 1px solid var(--qv-color-error);
}

.qv-number-circle-warning {
  color: #723c1c;
  background-color: #ffd4c4;
  border: 1px solid #ffb498;
}

.qv-table {
  border-spacing: 0;
  border-collapse: separate;
  border-radius: 6px;
  border: 1px solid white;

  overflow: hidden;
  font-weight: normal;

  &-striped {
    tbody>tr:nth-child(even) {
      background-color: rgba(158,158,158, 0.05);
    }
  }

  th, td {
    padding: 1rem;
    text-align: left;
  }

  thead>tr:not(:last-child)>th,
  thead>tr:not(:last-child)>td,
  tbody>tr:not(:last-child)>th,
  tbody>tr:not(:last-child)>td
  {
    border-bottom: 1px solid var(--qv-color-lightbuff);
  }
}

.qv-error {
  color: var(--qv-color-error) !important;
  border: none !important;
}

.qv-label-text {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #0e3a83;
    width: 100%;
    height: 2.2rem;
}

.qv-label-white {
  color: #fff !important;
}


.qv-label-txt {
  font-size: 1.1rem;
  color: #fff;
}

.qv-search-title {
  background:#162b3f;
  h1 {
      color: #fff;
      border-bottom: 1px solid #37474f;
      padding-bottom: 0.6rem;
  }
}

.qv-search-background {
  background:#162b3f;
}

.qv-ta-search-container,
.qv-sale-classification-search {
  padding-right: 0.6rem;
}

.qv-ta-search-container > .advSearch-group,
.qv-sale-classification-search > div.advSearch-group {
  width: 100%;
}

.qv-ta-search-container > .advSearch-group .open > .dropdown-menu {
  width: 250px;
}

.qv-ta-search-container > .advSearch-group .btn,
.qv-ta-search-container > .advSearch-group .btn-group,
.qv-sale-classification-search > .advSearch-group .btn,
.qv-sale-classification-search > .advSearch-group .btn-group {
  border-radius: .4rem;
  width: 100%;
}

.qv-ta-search-container > .advSearch-group > span.fieldTwo,
.qv-sale-classification-search > .advSearch-group > span.fieldTwo {
  width: 100% !important;
}

.qv-sale-classification-search > .advSearch-group > span.fieldTwo > span.multiselect-native-select {
 border-radius: 0.4rem;
}

.qv-search-input-text {
  color : #35495e;
}

.sp-ownership-container {
  padding-top: 0.5rem;
  padding-bottom: 1.5rem;
  border-top: 1px dashed #cccccc;
}

.qivs-update-messages {
  margin: 0 0.5rem;
  padding-left: 7px;
  color: var(--qv-color-error);
}

.sp-ownership-grid {
  display: grid;
  grid-template-columns: 0.9fr 2.5fr 0.9fr;
}

.contact-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr;
  gap: 5px;
}

.contact-row-segment-1 {
  display: grid;
  gap: 5px;
  grid-template-columns: 1.5fr 0.7fr 1.5fr;
}
.contact-row-segment-2 {
  display: grid;
  gap: 5px;
  grid-template-columns: 1.43fr 1.74fr;
}
.contact-row-segment-3 {
  display: grid;
  gap: 5px;
  grid-template-columns: 1.35fr 1fr 2.46fr;
}
.contact-row-segment-4 {
  display: grid;
  gap: 5px;
  grid-template-columns: 1fr 5fr 1.2fr;
}

.town-postcode-container {
  display: grid;
  grid-template-columns: 1.95fr 1fr;
  gap: 5px;
}

.sp-ownership-update {
  padding: 0px 7px;
  width: 100%;
  div {
      margin-bottom: 5px;
  }
}

.occupier-container {
  padding: 0px 7px;
  width: 100%;
}

.occupier-row {
  display: grid;
  grid-template-columns: 0.9fr 0.4fr 0.9fr 1fr 1fr 1fr 1fr 0.4fr;
  column-gap: 0.5rem;
  margin-bottom: 15px;
}
.occupier-row-org {
  display: grid;
  grid-template-columns: 0.9fr 0.4fr 0.9fr 4.11fr 0.4fr;
  column-gap: 0.5rem;
  margin-bottom: 15px;
}

.occupier-label-container {
  display: grid;
  grid-template-columns: 2.3fr 1fr 1fr 1fr 1fr 0.4fr;
  column-gap: 0.5rem;
  margin-bottom: 5px;
}

.occupier-secret-checkbox-container {
  display: flex;
  justify-content: center;
  padding-top: 0.95rem;
}

.sp-sale-input-label-small {
  min-width: 60px;
  line-height: 3;
  font-weight: 600;
}

.sp-sale-input-label-xs {
  line-height: 3;
  font-weight: 600;
}

.ownership-label {
  font-weight: 600;
  font-size: 1.1rem;
  line-height: 3;
  color: var(--qv-color-blue);
}

.sp-date-label {
  margin: 0;
  padding: 0 !important;
}

.action-button {
  border: none;
  box-shadow: 1px 1px darkgrey;
  color: white;
  border-radius: 2px;
  padding: 4px 8px;
  font-family: "Open Sans", "Helvetica", "Arial", sans-serif;
  font-size: 11px;
  font-weight: 600;
  font-style: normal;
  text-transform: uppercase;
  width: 110px;
}

.sp-revision-value {
  padding-top: 2px;
  padding-left: 6px;
  font-size: 1.2rem;
}

.multiselect__placeholder {
  margin-bottom: 9px !important;
}

.sp-saving-sale {
  opacity: 50%;
}
.sp-spinner-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
}

tr.sale-to-process {
  > td,
  .fullAddress {
    color: var(--qv-color-red);
  }

  &:hover a .fullAddress:hover {
    color: var(--color-red-0);
  }
}

tr.maori-land {
  outline: 1px solid var(--qv-color-red);
}

.error-message {
  display: block;
  color: #ea2e2d;
  font-size: 1.1rem;
  line-height: 1.6;
}

.qv-dialog[open] {
  border: none;
  z-index: 421;
  position: fixed;
  width: 60rem;
  min-height: 30rem;
  max-height: 75rem;
  border-radius: 3px;
  box-shadow: 10px 10px 500px 100px rgba(0, 0, 0, 0.2), 0 11px 15px -7px rgba(0, 0, 0, 0.12), 0 24px 38px 3px rgba(0, 0, 0, 0.2);

  display: flex;
  flex-direction: column;
  justify-content: space-between;

  button:hover {
    opacity: 0.8;
  }

  .qv-dialog-button {
    font-size: 14px;
    font-weight: 600;
    font-style: normal;
    text-transform: uppercase;
    line-height: 36px;
    border: none;
    border-radius: 2px;
    vertical-align: middle;
    padding: 0 16px;
    margin: 0;
    min-width: 8.5rem;
    height: 36px;
  }

  .qv-dialog-button:disabled {
    cursor: not-allowed;
    opacity: 0.7;
  }
}

.qv-draft-property {
  :is(h2, h4) {
    margin-bottom: 5px;
  }

  .form-section {
    margin-bottom: 2rem;
  }
}

.lv-comparable-sales-list-table td {
  font-size: 1.1rem !important;
}

.cv-comparable-sales-list-table td {
  font-size: 1.1rem !important;
}
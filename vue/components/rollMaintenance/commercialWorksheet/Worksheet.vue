<!-- eslint-disable vue/multi-word-component-names -->
<script setup>
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router/composables';

import PropertyInfoPanel from '@/components/property/PropertyInfo.vue';
import PropertyDetail from '@/components/rollMaintenance/commercialWorksheet/PropertyDetail.vue';
import Land from '@/components/rollMaintenance/commercialWorksheet/Land.vue';
import CurrentAssessment from '@/components/rollMaintenance/commercialWorksheet/CurrentAssessmentValues.vue';
import RevisionValues from '@/components/rollMaintenance/commercialWorksheet/RevisionValues.vue';
import Improvements from '@/components/rollMaintenance/commercialWorksheet/Improvements.vue';
import ActualRentals from '@/components/rollMaintenance/commercialWorksheet/ActualRentals.vue';
import WorksheetTotalValues from '@/components/rollMaintenance/commercialWorksheet/WorksheetTotals.vue';
import WorksheetAdoptedValues from '@/components/rollMaintenance/commercialWorksheet/WorksheetAdopted.vue';
import ExpandableSection from '@/components/common/ExpandableSectionWrapper.vue';
import ReasonForChange from '@/components/rollMaintenance/commercialWorksheet/ReasonForChange.vue';
import MButton from 'Common/MButton.vue';
import ConfirmationModal from '@/components/rollMaintenance/commercialWorksheet/CommercialWorksheetConfirmationModal.vue';
import { store } from '@/DataStore';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';
import useModal from '@/composables/useModal';

const modal = useModal();

const {
    loaded,
    initialPageLoad,
    deleteCommercialWorksheet,
    isRevision,
    createRevision,
    updateAssessment,
    updateCommercialWorksheet,
    isReadOnly,
    printCommercialWorksheet,
    property,
} = useCommercialWorksheet();

const router = useRouter();
const route = useRoute();
const props = defineProps({
    worksheet: {
        type: Object,
        required: true,
    },
    propertyDetails: {
        type: Object,
        required: true,
    },
    qpid: {
        type: Number,
        required: true,
    },
});

const user = computed(() => store.state.userData);
const isInternalUser = computed(() => user?.value?.isInternalUser);
const isExternalUser = computed(() => user?.value?.isExternalUser);
const isReadOnlyUser = computed(() => user?.value?.isReadOnlyUser);
const canAccessRevisionWorksheet = computed(() => isInternalUser.value || (isExternalUser.value && isReadOnlyUser.value === false));
const shouldShowRevisionWorksheetButton = computed(() => (props.worksheet.hasRevision || isRevision.value || createRevision.value) && canAccessRevisionWorksheet.value);

const improvementsExpandableInitialState = ref('expanded');
const saving = ref(false);
const rfcSaving = ref(false);
const isCurrent = computed(() => !isRevision.value && !createRevision.value);
const isCreateWorksheet = computed(() => (props.worksheet.isUpdate ?? 0) === 0);
const pageTitle = computed(() => (isCreateWorksheet.value ? 'Create Commercial Worksheet' : `Update Commercial Worksheet ${isRevision.value || createRevision.value ? ' - Revision Values' : ''}`));
const isPendingAssessment = computed(() => property.value.property.status?.code?.trim().toUpperCase() === 'P');

const firstOrLastWorksheetMessage = computed(() => {
    if (!props.worksheet) {
        return '';
    }
    if (!props.worksheet.prevQpid) {
        return 'This is the first commercial worksheet on this roll.';
    }
    if (!props.worksheet.nextQpid) {
        return 'This is the last commercial worksheet on this roll.';
    }
    return '';
});

function goToCommercialWorksheet(back = true) {
    const qpid = back ? props.worksheet.prevQpid : props.worksheet.nextQpid;
    if (!qpid) {
        return;
    }
    const routerName = isCurrent.value ? 'commercial-worksheet': 'commercial-revision-worksheet';
    router.push({ name: routerName, params: { qpid } });
}

function goToCurrentCommercialWorksheet(){
    if(route.name === 'commercial-worksheet'){
        isRevision.value = false;
        createRevision.value = false;
        initialPageLoad(props.qpid, false, false);
        return;
    }

    router.push({ name: 'commercial-worksheet', params: { qpid: props.qpid } });
}

async function goToCommercialRevisionWorksheet() {
    if (props.worksheet.hasRevision) {
        router.push({ name: 'commercial-revision-worksheet', params: { qpid: props.qpid } });
    }
    else {
        const title = 'Revision Worksheet does not exist for this property';
        const message = 'Click OK to go to the  current Commercial Worksheet.';
        const payload = {
            title,
            message,
            confirmText: 'OK',
            showCancelBtn: false
        }
        const hasConfirmed = await modal.show(ConfirmationModal, payload);
        if(!hasConfirmed) {
            return;
        }
        goToCurrentCommercialWorksheet();
    }
}

async function createCommercialRevisionWorksheet() {
    await initialPageLoad(props.qpid, false, true);
    handleCreateRevision();
}

function arrowTitle(back = true) {
    if (back) {
        return `Previous Worksheet: ${props.worksheet.prevQpid ?? ''}`;
    }
    return `Next Worksheet: ${props.worksheet.nextQpid ?? ''}`;
}

async function handleDiscardChanges() {
    const payload = {
        title: 'Are you sure?',
        message: 'You will lose all your current changes.',
        confirmText: 'Yes, Discard My Changes',
        cancelText: 'No, Return To Worksheet',
    };
    const hasConfirmedDiscard = await modal.show(ConfirmationModal, payload);
    if (!hasConfirmedDiscard) {
        return;
    }
    router.push({ name: 'property', params: { qpid: props.qpid }});
}

async function handleDeleteWorksheet() {
    const payload = {
        title: 'Do you want to proceed?',
        message: `The following worksheets for this assessment will be deleted. Are you sure?
                    - Current commercial worksheet
                    - Revision commercial worksheet`,
        confirmText: 'Yes, Delete Worksheet',
        cancelText: 'No, Return To Worksheet',
    };
    const hasConfirmedDelete = await modal.show(ConfirmationModal, payload);
    if (!hasConfirmedDelete) {
        return;
    }
    await deleteCommercialWorksheet();
}

async function handleCreateRevision() {
    const title = 'Create Revision Worksheet';
    const message = 'All editable fields have been copied from the current worksheet. Please update all values as required.';
    const payload = {
        title,
        message,
        showCancelBtn: false
    }
    await modal.show(ConfirmationModal, payload);
}

</script>

<template>
    <div>
        <div v-if="!loaded">
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
        <div v-else class="masterDetails-Wrapper bootstrap-ify mdl-shadow--3dp">
            <div v-if="saving || rfcSaving" class="page-mask">
                <div class="loadingSpinnerWrapper">
                    <div class="loadingSpinnerBox">
                        <div class="loadingSpinner loadingSpinnerSearchResults" />
                    </div>
                </div>
            </div>
            <div class="container-fluid" style="padding-top: 1rem">
                <div class="row">
                    <div class="col-lg-2">
                        <property-info-panel :qpid="qpid" />
                    </div>
                    <div class="col-lg-10">
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-12">
                                    <h1 class="title" style="padding-top: 10px; margin-bottom: 20px"
                                        data-cy="cw-page-title">
                                        {{ pageTitle }}
                                        <div v-if="!worksheet.isInSubdivision" class="righty" style="margin-top: -5px">
                                            <span>
                                                <i data-direction="back"
                                                    class="listingButton material-icons assessmentNav"
                                                    :title="arrowTitle(true)" :class="{ disabled: !worksheet.prevQpid }"
                                                    @click="goToCommercialWorksheet(true)"> arrow_back </i>
                                                <i data-direction="forward"
                                                    class="listingButton material-icons assessmentNav"
                                                    :title="arrowTitle(false)"
                                                    :class="{ disabled: !worksheet.nextQpid }"
                                                    @click="goToCommercialWorksheet(false)"> arrow_forward </i>
                                            </span>
                                        </div>
                                    </h1>
                                    <p v-if="firstOrLastWorksheetMessage"
                                        style="color: red;margin-bottom: 1rem;text-align: right;">
                                        {{ firstOrLastWorksheetMessage }}
                                    </p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <ul id="tabsElementCommercialWorksheet" class="QVHV-tabs">
                                        <li @click="goToCurrentCommercialWorksheet">
                                            <span data-cy="commercialWorksheetTab1"
                                                :class="{ 'is-active': isCurrent }">Current Worksheet</span>
                                        </li>
                                        <li v-if="shouldShowRevisionWorksheetButton"
                                            @click="goToCommercialRevisionWorksheet">
                                            <span data-cy="commercialWorksheetTab2"
                                                :class="{ 'is-active': isRevision || createRevision }">Revision
                                                Worksheet</span>
                                        </li>
                                        <li v-else-if="isInternalUser && !isCreateWorksheet && !isReadOnly"
                                            @click="createCommercialRevisionWorksheet" class="button-link">
                                            <span data-cy="cw-create-revision-button">Create Revision Worksheet</span>
                                        </li>
                                        <li class="righty button-link">
                                            <div style="border:0; padding: 0.8rem 1.2rem;">
                                                <m-button
                                                    v-if="isInternalUser"
                                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                                    data-cy="cw-print-button-top" title="Unsaved data won’t be printed" @click="printCommercialWorksheet">
                                                    PRINT </m-button>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="container-fluid space-rows QVHV-box" style="border: none;">
                                        <div class="row">
                                            <div class="col-lg-12" style="padding: 0">
                                                <h3 class="section-title" data-cy="cw-property-detail-header">Property
                                                    Detail</h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <property-detail />
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12" style="padding: 0">
                                                <h3 class="section-title" data-cy="cw-land-header">Land</h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <land />
                                        </div>
                                        <div class="row">
                                            <expandable-section title="Improvements"
                                                title-data-cy="cw-improvements-header"
                                                :initial-state="improvementsExpandableInitialState">
                                                <improvements />
                                            </expandable-section>
                                        </div>
                                        <div class="row" style="margin-top: 1.5rem;">
                                            <div class="col-lg-12" style="padding: 0">
                                                <h3 class="section-title" data-cy="cw-total-worksheet-values-header">
                                                    Total Worksheet Values</h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <worksheet-total-values />
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12" style="padding: 0">
                                                <h3 class="section-title" data-cy="cw-actual-rentals-header">Actual
                                                    Rentals</h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <actual-rentals />
                                        </div>
                                        <div class="row" style="margin-top: 1.5rem;">
                                            <div class="col-lg-12" style="padding: 0">
                                                <h3 class="section-title" data-cy="cw-adopted-worksheet-values-header">
                                                    Adopted Worksheet Values</h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <worksheet-adopted-values />
                                        </div>
                                        <div class="row grey" v-if="!isCurrent">
                                            <div class="col-lg-12" style="padding: 0">
                                                <h3 class="section-title" data-cy="cw-current-assessment-header">
                                                    Revision Values</h3>
                                            </div>
                                        </div>
                                        <div class="row" v-if="!isCurrent">
                                            <revision-values :worksheet="worksheet" />
                                        </div>
                                        <div class="row" :class="{'grey': !isCurrent}">
                                            <div class="col-lg-12" style="padding: 0">
                                                <h3 class="section-title" data-cy="cw-current-assessment-header">Current
                                                    Assessment Values</h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <current-assessment :worksheet="worksheet" />
                                        </div>
                                        <div class="row" v-if="isInternalUser && isCurrent">
                                            <div class="col-lg-12" style="padding: 0">
                                                <h3 class="section-title" data-cy="cw-rfc-header">Reason for change</h3>
                                            </div>
                                        </div>
                                        <div class="row" v-if="isInternalUser && isCurrent">
                                            <reason-for-change />
                                        </div>
                                        <div class="row" style="padding-top: 10px">
                                            <div class="col-lg-4" style="padding: 0">
                                                <m-button
                                                    v-if="isInternalUser && !createRevision && !isRevision && !isCreateWorksheet && !isReadOnly"
                                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored-red"
                                                    data-cy="cw-delete-button" @click="handleDeleteWorksheet"> DELETE
                                                    WORKSHEET </m-button>
                                                <m-button
                                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                                    data-cy="cw-cancel-button" @click="handleDiscardChanges"> CANCEL
                                                    CHANGES </m-button>
                                            </div>
                                            <div class="col-lg-2" style="text-align:right;">
                                                <m-button
                                                    v-if="isInternalUser"
                                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                                    data-cy="cw-print-button-bottom" @click="printCommercialWorksheet"
                                                    title="Unsaved data won’t be printed">
                                                    PRINT </m-button>
                                            </div>
                                            <div class="col-lg-6" style="text-align:right;padding: 0;">
                                                <m-button v-if="isInternalUser && !isReadOnly"
                                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                                    data-cy="cw-update-assessment-button" @click="updateAssessment">
                                                    UPDATE {{isCurrent ? 'ASSESSMENT' : ''}} </m-button>
                                                <m-button
                                                    v-if="isInternalUser && !isRevision && !createRevision && !worksheet.isInSubdivision && !isReadOnly && !isPendingAssessment"
                                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                                    data-cy="cw-update-worksheet-button"
                                                    @click="updateCommercialWorksheet"> UPDATE WORKSHEET ONLY
                                                </m-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

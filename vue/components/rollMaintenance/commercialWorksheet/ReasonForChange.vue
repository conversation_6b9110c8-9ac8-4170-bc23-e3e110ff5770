<script setup>
import ReasonForChange from 'Common/form/ReasonForChange.vue';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';

const { rfc, rfcClassifications, isReadOnly } = useCommercialWorksheet();

</script>

<template>
    <div class="container-fluid">
        <div class="col-lg-3 cw-rfc-ua-label" data-cy="cw-rfc-update-assessment-label">Update Assessment</div>
        <div class="col-lg-9" data-cy="cw-rfc-input">
            <reason-for-change
                :rfc=rfc
                :showHeader="false"
                :outputOptions="rfcClassifications.reasonOutputs"
                :sourceOptions="rfcClassifications.reasonSources"
                :isFilteredOptions="true"
                :readonly="isReadOnly"
            />
        </div>
    </div>
</template>

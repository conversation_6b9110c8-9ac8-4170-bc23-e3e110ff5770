<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

import PropertySummary from '@/components/property/PropertySummary.vue';
import Worksheet from '@/components/rollMaintenance/commercialWorksheet/Worksheet.vue';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';
import useModal from '@/composables/useModal';

const modal = useModal();
const {
    worksheet,
    initialPageLoad,
    loaded,
} = useCommercialWorksheet();

onMounted(async () => {
    console.log(`Commercial Worksheet Mounted with qpid: ${qpid.value}`);
    await initialPageLoad(parseInt(qpid.value), isRevision.value, createRevision.value);
    if (![1, 3, 5].includes(parseInt(worksheet.value.apportionmentId)) || !(/^(C|I|O|U)/.test(worksheet.value.categoryCode))) {
        await modal.showError('Invalid Property', ` Unable to create commercial worksheet for this property. The property has an invalid category or apportionment code. `);
        router.push({ name: 'property', params: { qpid: route.params.qpid } });
    }
});

const router = useRouter();
const route = useRoute();

const qpid = computed(() => parseInt(route.params.qpid));
const isRevision = computed(() => route.name === 'commercial-revision-worksheet');
const createRevision = computed(() => route.query.createRevision == 'Y');

watch(() => [qpid.value, isRevision.value, createRevision.value], async () => {
    loaded.value = false;
    await initialPageLoad(parseInt(qpid.value), isRevision.value, createRevision.value);
    loaded.value = true;
});

</script>

<template>
    <div class="resultsWrapper router qv-cw-form">
        <property-summary
            :property-id="`${qpid}`"
            :can-navigate="false"
        />
        <worksheet
            :worksheet="worksheet"
            :property-details="{}"
            :qpid="qpid"
            :loaded="loaded"
        />
    </div>
</template>

<style src="./commercialWorksheet.scss"></style>

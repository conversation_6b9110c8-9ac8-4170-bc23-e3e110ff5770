<script setup>
import { computed } from 'vue';
import FormTextArea from '@/components/common/form/FormTextArea.vue';
import InputNumber from '@/components/common/form/InputNumber2.vue';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';

const {
    worksheet,
    totalLandValue,
    totalCvIncome,
    totalViSummation,
    cvIncomeImprovementValue,
    viSummationCapitalValue,
    cvByTotalArea,
    lvByLandArea,
    viByTotalArea,
    isRevision,
    createRevision,
    isReadOnly,
} = useCommercialWorksheet();
const revisionPrefix = computed(() => (createRevision.value || isRevision.value ? 'R' : ''));
const revisionKeyword = computed(() => (createRevision.value || isRevision.value ? 'Revision' : ''));
const readonly = computed(() => isReadOnly.value || isRevision.value || createRevision.value);

</script>

<template>
    <div class="container-fluid">
        <div
            class="row table-head"
            data-cy="cw-totals-table-head"
            style="padding-top: 3px; margin-bottom: 1rem;"
        >
            <div class="col-lg-6">
                &nbsp;
            </div>
            <div class="col-lg-2">
                {{revisionKeyword}} Capital Value
            </div>
            <div class="col-lg-2">
                {{revisionKeyword}} Land Value
            </div>
            <div class="col-lg-2">
                {{revisionKeyword}} Value of Improvements
            </div>
        </div>
        <div
            class="row"
            style="margin-bottom: 1rem;"
        >
            <div class="col-lg-6">
                <div class="cw-checkbox-label-container">
                    <input
                        type="radio"
                        id="CV2"
                        :value="2"
                        v-model="worksheet.commercialValuationMethodId"
                        tabindex="-1"
                        data-cy="cw-totals-cv-income-radio"
                        :disabled="readonly"
                    >
                    <label for="CV2">CV Income</label>
                </div>
            </div>
            <div class="col-lg-2">
                <label>
                    <input-number
                        :value="totalCvIncome"
                        format="$0,0"
                        readonly
                        data-cy="cw-totals-total-income-cv"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <input-number
                        :value="totalLandValue"
                        format="$0,0"
                        readonly
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <input-number
                        :value="cvIncomeImprovementValue"
                        format="$0,0"
                        readonly
                        data-cy="cw-totals-cv-income-vi"
                    />
                </label>
            </div>
        </div>
        <div
            class="row"
        >
            <div class="col-lg-6">
                <div class="cw-checkbox-label-container">
                    <input type="radio"
                        id="VI2"
                        :value="4"
                        v-model="worksheet.commercialValuationMethodId"
                        tabindex="-1"
                        data-cy="cw-totals-vi-income-radio"
                        :disabled="readonly"
                    >
                    <label for="VI2">VI Summation</label>
                </div>
            </div>
            <div class="col-lg-2">
                <label>
                    <input-number
                        :value="viSummationCapitalValue"
                        format="$0,0"
                        readonly
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <input-number
                        :value="totalLandValue"
                        format="$0,0"
                        readonly
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <input-number
                        :value="totalViSummation"
                        format="$0,0"
                        readonly
                    />
                </label>
            </div>
        </div>
        <div class="row">
            <div class="col col-lg-8">
                <label for="comment" class="cw-label-heading">Comment</label>
                <form-text-area
                    id="comment"
                    v-model="worksheet.comment"
                    label="Comment"
                    custom-class="cw-totals-comment"
                    :maxlength="1000"
                    data-cy="cw-totals-comment"
                    :shouldAutoGrow="false"
                    :preventEnter="false"
                    :readonly="isReadOnly"
                />
            </div>
            <div class="col-lg-4">
                <div class="row" style="margin-top: 2.9rem;">
                    <div class="col-lg-6 cw-total-text">
                        {{revisionPrefix}}CV/Total Area
                    </div>
                    <div class="col-lg-6">
                        <label>
                            <input-number
                                :value="cvByTotalArea"
                                format="$0,0"
                                readonly
                            />
                        </label>
                    </div>
                </div>
                <div class="row" style="margin-top: 1rem;">
                    <div class="col-lg-6 cw-total-text">
                        {{revisionPrefix}}LV/Land Area
                    </div>
                    <div class="col-lg-6">
                        <label>
                            <input-number
                                :value="lvByLandArea"
                                format="$0,0"
                                readonly
                            />
                        </label>
                    </div>
                </div>
                <div class="row" style="margin-top: 1rem;">
                    <div class="col-lg-6 cw-total-text">
                        {{revisionPrefix}}VI/Total Area
                    </div>
                    <div class="col-lg-6">
                        <label>
                            <input-number
                                :value="viByTotalArea"
                                format="$0,0"
                                readonly
                            />
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

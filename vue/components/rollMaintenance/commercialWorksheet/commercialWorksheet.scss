.QVHV-box {
    border: none !important;
}

.multiselect__tags {
    max-height: var(--qv-input-height);
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.qv-multiselect.multiselect--disabled {
    background-color: #ebf9ff;
    border: none;
    border-radius: 5px;

    .multiselect__tags {
        background-color: #ebf9ff;
        border: 1px solid #afdcef;

        .multiselect__single {
            background-color: #ebf9ff;
        }
    }

    .multiselect__select {
        background-color: #ebf9ff;
        border-radius: 5px;
    }
}

.cw-checkbox-label-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cw-space-between {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.cw-justify-end {
    display: flex;
    justify-content: end;
    gap: 1rem;
}

.cw-total-text {
    text-align: right;
    font-weight: 700;
    vertical-align: middle;
    line-height: 38px;
    white-space: nowrap;
}

.cw-totals-comment {
    height: 130px !important;
}

.cw-label-heading {
    color: #0e3a83;
    font-size: 1.1rem;
}

.cw-table-headers-sticky {
    position: sticky;
    top: 0;
    z-index: 1;
    background: white;
}

.cw-parent-row {
    border-top: 1px solid #5e5e5e;
    margin-top: 15px;
    border-bottom: none !important;
}

.cw-max-value-apportionment {
    background: #e2ebff;
    border-radius: 3px;
}

.cw-pad-right {
    padding-right: 10px;
}


.cw-rfc-ua-label {
    font-weight:bold;
    line-height:75px;
}

.cw-color-caution {
  color: var(--qv-color-orange);
}
.cw-modal-message {
  white-space: pre-line;
  font-size: 1.3rem;
  color: #3f3f3f;
}

.bootstrap-ify label input.qv-form-input {
    height: var(--qv-input-height);
}

.cw-textarea {
    textarea {
        line-height: 1.5rem;
        min-height: var(--qv-input-height);
    }
}

.ch-input-right {
    input {
        text-align: right;
    }
}
<!-- eslint-disable vue/multi-word-component-names -->
<script setup>
import { ref, computed, nextTick, watch } from 'vue';
import FormSelect from '@/components/common/form/FormSelect.vue';
import InputNumber from '@/components/common/form/InputNumber2.vue';
import FormTextArea from '@/components/common/form/FormTextArea.vue';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';

import _ from 'lodash';
const UNDEFINED_OPTION = 'Undefined';

const {
    classifications,
    worksheet,
    totalLandArea,
    totalLandValue,
    newLandRow,
    isRevision,
    createRevision,
    isReadOnly
} = useCommercialWorksheet();

const newLandRowDescription = ref(null);
const newLandRowHasAtLeastOneValue = [newLandRow.value.area, newLandRow.value.ratePerMetre, newLandRow.value.description, newLandRow.value.streetLocationTypeId].some(item => !_.isNil(item));
const isCurrent = computed(() => !isRevision.value && !createRevision.value);
const landRows = ref([]);
const hideStreetLocationUndefinedOption = ref(false);
const streetLocationOptions = computed(() => classifications.value.StreetLocationType?.filter(item => item.ratingAuthorityId === worksheet.value.ratingAuthorityId && (!isCurrent.value || !hideStreetLocationUndefinedOption.value || item.description != UNDEFINED_OPTION)) ?? []);

watch(worksheet, (newVal) => {
    landRows.value = newVal.landRows.map(row => {
        setValidLandAreaFlag(row);
        return row;
    });
}, { immediate: true });

async function handleEnter(event) {
    if (isReadOnly){
        return;
    }
    if (event.key === 'Enter' && isCurrent.value) {
        addNewLandRow();
    }
}

async function handleEnterAndTab(event) {
    if (isReadOnly){
        return;
    }
    if (event.key === 'Enter' && isCurrent.value) {
        addNewLandRow();
        return;
    }
    if (event.key === 'Tab') {
        if (event.shiftKey) {
            return;
        }
        if (isCurrent.value){
            addNewLandRow(true, event);
        }
    }
}

async function addNewLandRow(checkForValue = false, event = null) {
    if (checkForValue && !newLandRowHasAtLeastOneValue.value) {
        return;
    }
    if (event) {
        event.preventDefault();
    }
    worksheet.value.landRows.push({ ...newLandRow.value, commercialWorksheetRowId: _.uniqueId('new_land_row_') });
    landRows.value = worksheet.value.landRows.map(row => {
        setValidLandAreaFlag(row);
        return row;
    });
    newLandRow.value = { isValidLandArea: true };
    await nextTick();
    newLandRowDescription.value.focus();
}

function removeLandRow(rowId) {
    const index = worksheet.value.landRows.findIndex(landRow => landRow.commercialWorksheetRowId === rowId);
    if (index !== -1) {
        worksheet.value.landRows.splice(index, 1);
        landRows.value.splice(index, 1);
    }
}

function getValueForRow(landRow) {
    if (_.isNil(landRow.area) || _.isNil(landRow.ratePerMetre)) {
        return 0;
    }
    return landRow.area * landRow.ratePerMetre;
}

function onStreetLocationDropdownOpen() {
    if (isCurrent.value) {
        hideStreetLocationUndefinedOption.value = true;
    }
}

function onStreetLocationDropdownClose() {
    if (isCurrent.value) {
        hideStreetLocationUndefinedOption.value = false;
    }
}

function handleStreetLocationTypeErrors(landRow) {
    const errors = [];
    if (landRow && isCurrent.value) {
        const selectedOption = streetLocationOptions.value.find(item => item.id == landRow.streetLocationTypeId);
        if (selectedOption && selectedOption.description === UNDEFINED_OPTION) {
            errors.push('Street Location must not be Undefined');
        }
        if(landRow.invalid){
            errors.push('');
        }
    }
    return errors;
}

function setValidLandAreaFlag(landRow) {
    landRow['isValidLandArea'] = true;
    if (isCurrent.value && (_.isNil(worksheet.value.landArea) || worksheet.value.landArea === 0) && !_.isNil(landRow.area) && landRow.area < 10) {
        landRow['isValidLandArea'] = false;
    }
}

</script>

<template>
    <div class="container-fluid">
        <div
            class="row table-head"
            data-cy="cw-land-table-head"
            style="padding-top: 3px;"
        >
            <div class="col-lg-5">
                Description
            </div>
            <div class="col-lg-2">
                Street Location
            </div>
            <div class="col-lg-1">
                Area m<sup>2</sup>
            </div>
            <div class="col-lg-1">
                Rate/m<sup>2</sup>
            </div>
            <div class="col-lg-2">
                Value
            </div>
            <div class="col-lg-1">
                &nbsp;
            </div>
        </div>
        <div
            v-for="landRow of landRows"
            :key="landRow.commercialWorksheetRowId"
            class="row table-row"
            :class="{ invalid: landRow.invalid }"
            data-cy="cw-land-row"
        >
            <div class="col-lg-5">
                <label>
                    <form-text-area
                        :readonly="!isCurrent || isReadOnly"
                        maxlength="100"
                        v-model="landRow.description"
                        :shouldAutoGrow="true" rows="1"
                        :preventEnter="true"
                        class="cw-textarea"
                        data-cy="cw-land-row-description"
                        :errors="landRow.invalid ? [''] : []"
                    />
                </label>
            </div>
            <div class="col-lg-2"
                 data-cy="cw-land-row-street-location-type-div"
                 @focusin="onStreetLocationDropdownOpen"
                 @focusout="onStreetLocationDropdownClose"
            >
                <label>
                    <form-select
                        v-model="landRow.streetLocationTypeId"
                        :options="streetLocationOptions"
                        :errors="handleStreetLocationTypeErrors(landRow)"
                        data-cy="cw-land-row-street-location-type"
                        :readonly="!isCurrent || isReadOnly"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="landRow.area"
                        format="0,0.00"
                        data-cy="cw-land-row-area"
                        :readonly="!isCurrent || isReadOnly"
                        :warnings="!landRow.isValidLandArea"
                        :title="!landRow.isValidLandArea ? 'Please enter effective land area' : ''"
                        @input="setValidLandAreaFlag(landRow)"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="landRow.ratePerMetre"
                        format="$0,0.00"
                        data-cy="cw-land-row-rate-per-metre"
                        :max="99999999999.99"
                        :readonly="isReadOnly"
                    />
                </label>
            </div>
            <div class="col-lg-3">
                <div class="cw-space-between">
                    <label>
                        <input-number
                            :value="getValueForRow(landRow)"
                            readonly
                        />
                    </label>
                    <i
                        v-if="isCurrent && !isReadOnly"
                        class="saRow-remove material-icons"
                        data-cy="cw-remove-land-row"
                        @click="removeLandRow(landRow.commercialWorksheetRowId)"
                    ></i>
                </div>
            </div>
        </div>
        <div v-if="isCurrent && !isReadOnly"
            class="row table-row"
            data-cy="cw-new-land-row"
            :class="{ invalid: newLandRow.invalid }"
        >
            <div class="col-lg-5">
                <label>
                    <form-text-area
                        ref="newLandRowDescription"
                        maxlength="100"
                        v-model="newLandRow.description"
                        data-cy="cw-new-land-row-description"
                        :shouldAutoGrow="true" :rows="1"
                        class="cw-textarea"
                        :preventEnter="true"
                        :handle-keyup="handleEnter"
                        :errors="newLandRow.invalid ? [''] : []"
                    />
                </label>
            </div>
            <div class="col-lg-2"
                 data-cy="cw-new-land-row-street-location-type-div"
                 @focusin="onStreetLocationDropdownOpen"
                 @focusout="onStreetLocationDropdownClose"
            >
                <label>
                    <form-select
                        v-model="newLandRow.streetLocationTypeId"
                        data-cy="cw-new-land-row-street-location-type"
                        :options="streetLocationOptions"
                        :errors="handleStreetLocationTypeErrors(newLandRow)"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newLandRow.area"
                        data-cy="cw-new-land-row-area"
                        format="0,0.00"
                        :handle-keyup="handleEnter"
                        :warnings="!newLandRow.isValidLandArea"
                        :title="!newLandRow.isValidLandArea ? 'Please enter effective land area' : ''"
                        @input="setValidLandAreaFlag(newLandRow)"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newLandRow.ratePerMetre"
                        data-cy="cw-new-land-row-rate"
                        :handle-keyup="handleEnterAndTab"
                        format="$0,0.00"
                        :max="99999999999.99"
                    />
                </label>
            </div>
            <div class="col-lg-3">
                <div class="cw-space-between">
                    <label>
                        <input-number
                            :value="getValueForRow(newLandRow)"
                            readonly
                        />
                    </label>
                    <i v-if="isCurrent"
                        class="saRow-add material-icons"
                        data-cy="cw-add-new-land-row-button"
                        @click="addNewLandRow()"
                    >
                        
                    </i>
                </div>
            </div>
        </div>
        <div class="row" style="margin-top: 1rem;">
            <div class="col-lg-7 cw-total-text">
                Total
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        data-cy="cw-total-land-area"
                        :value="totalLandArea"
                        readonly
                        format="0.00"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <span />
            </div>
            <div class="col-lg-2">
                <label>
                    <input-number
                        style="width: 149px;"
                        :value="totalLandValue"
                        readonly
                        data-cy="cw-total-land-value"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <span />
            </div>
        </div>
    </div>
</template>

<style lang="scss">

.cw-total-text {
    text-align: right;
    font-weight: 700;
    vertical-align: middle;
    line-height: 38px;
}

</style>

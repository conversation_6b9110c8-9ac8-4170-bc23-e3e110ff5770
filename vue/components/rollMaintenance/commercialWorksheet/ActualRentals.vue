<script setup>
import { ref, computed, nextTick } from 'vue';
import FormSelect from '@/components/common/form/FormSelect.vue';
import InputDate from '@/components/common/form/InputDate.vue';
import InputText from '@/components/common/form/InputText.vue';
import InputNumber from '@/components/common/form/InputNumber2.vue';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';
import _ from 'lodash';
import { DateTime } from 'luxon';

const {
    classifications,
    worksheet,
    newActualRentalRow,
    isRevision,
    createRevision,
    isReadOnly,
    rentalDateErrors,
    newRentalDateErrors,
} = useCommercialWorksheet();

// refs
const newActualRentalRowRentalDate = ref(null);

// computed
const isCurrent = computed(() => !isRevision.value && !createRevision.value);
const newActualRentalRowHasAtLeastOneValue = computed(() => !_.isNil(newActualRentalRow.value.rentalDate) || !_.isNil(newActualRentalRow.value.grossRental) || !_.isNil(newActualRentalRow.value.floorArea) || !_.isNil(newActualRentalRow.value.rentalPerSqmt) || !_.isNil(newActualRentalRow.value.tenant));
const actualRentalTypes = computed(() => classifications.value.ActualRentalType ?? []);
const defaultActualRentalType = computed(() => actualRentalTypes.value.find(item => item.code == 'M') || null);
const rentalYearsWithinConfigKey = computed(() => isCurrent.value ? 'annual_value_current_worksheet_actual_rental_years_within' : 'annual_value_revision_worksheet_actual_rental_years_within');
const rentalYearsWithinConfig = computed(() => worksheet.value.configs.find(item => item.keyName == rentalYearsWithinConfigKey.value));

// init
getDefaultRentCode(newActualRentalRow.value);

// function
function getRentalDateErrors(row) {
    if (row.actualRentalId) {
        const errorObject = rentalDateErrors.value.find(item => item.actualRentalId === row.actualRentalId);
        if (errorObject && errorObject.errors && errorObject.errors.length > 0) {
            return errorObject.errors;
        }
    }
    else {
        return newRentalDateErrors.value;
    }
}

function handleRentalDateInput(refObj) {
    const errors = validateRentalDate(refObj.rentalDate);
    handleRentalDateInputErrors(refObj, errors);
}

function handleRentalDateInputErrors(refObj, errors) {
    if (refObj.actualRentalId) {
        const index = rentalDateErrors.value.findIndex(item => item.actualRentalId === refObj.actualRentalId);
        if (index > -1) {
            rentalDateErrors.value[index] = { actualRentalId: refObj.actualRentalId, errors };
        }
        else {
            rentalDateErrors.value.push({ actualRentalId: refObj.actualRentalId, errors });
        }
    }
    else {
        newRentalDateErrors.value = errors;
    }
}

function handleAnnualRentalInput(refObj) {
    calculateRentalPerSqmt(refObj);
}

function handleRentalPerSqmtInput(refObj) {
    calculateAnnualRental(refObj);
}

function getDefaultRentCode(refObj) {
    refObj.actualRentalTypeId = defaultActualRentalType.value.id;
    return refObj.actualRentalTypeId;
}

function handleFloorAreaInput(refObj, value) {
    if (refObj.rentalPerSqmt == null) {
        calculateRentalPerSqmt(refObj);
    }
    else if (refObj.grossRental == null) {
        calculateAnnualRental(refObj);
    }
    else {
        calculateRentalPerSqmt(refObj);
    }
}

function calculateAnnualRental(refObj) {
    if (refObj.floorArea != null && refObj.rentalPerSqmt != null) {
        const valueToBeUpdated = Math.round(refObj.floorArea * refObj.rentalPerSqmt);
        refObj.grossRental = valueToBeUpdated;
    }
    return refObj.grossRental;
}

function calculateRentalPerSqmt(refObj) {
    if (refObj.grossRental != null && refObj.floorArea != null) {
        let valueToBeUpdated = Math.round(refObj.grossRental / refObj.floorArea);
        if (refObj.floorArea == 0) {
            valueToBeUpdated = 0;
        }

        refObj.rentalPerSqmt = valueToBeUpdated;
    }
    return refObj.rentalPerSqmt;
}

function validateRentalDate(date) {
    const errors = [];
    if (date == null || date == '') {
        return [];
    }

    const parsedDate = DateTime.fromFormat(date, 'yyyy-MM-dd');
    if (!parsedDate.isValid) {
        errors.push('Date must be in the format dd/mm/yyyy');
        return errors;
    }
    // date is valid
    // check if it is in the future
    const currentDate = DateTime.now();
    if (parsedDate > currentDate) {
        errors.push('Date cannot be in the future');
        return errors;
    }

    if (rentalYearsWithinConfig.value) {
        const pastYearsWithinDate = currentDate.minus({ years: parseInt(rentalYearsWithinConfig.value.keyValue) });
        if (parsedDate <= pastYearsWithinDate) {
            errors.push(`Date must be within ${rentalYearsWithinConfig.value.keyValue} years`);
            return errors;
        }
    }

    return errors;
}

async function handleEnter(event) {
    if (isReadOnly){
        return;
    }
    if (event.key === 'Enter') {
        addNewActualRentalRow();
        return;
    }
}

async function handleEnterAndTab(event) {
    if (isReadOnly){
        return;
    }
    if (event.key === 'Enter') {
        addNewActualRentalRow();
        return;
    }
    if (event.key === 'Tab') {
        if (event.shiftKey) {
            return;
        }
        addNewActualRentalRow(true, event);
    }
}

async function addNewActualRentalRow(checkForValue = false, event = null) {
    if (checkForValue && !newActualRentalRowHasAtLeastOneValue.value) {
        return;
    }
    if (event) {
        event.preventDefault();
    }

    const newRow = { ...newActualRentalRow.value, actualRentalId: _.uniqueId('new_actual_rental_row_') };
    worksheet.value.actualRentalRows.push(newRow);
    rentalDateErrors.value.push({ actualRentalId: newRow.actualRentalId, errors: newRentalDateErrors.value.slice() });

    // setup new row
    newActualRentalRow.value = {
        actualRentalTypeId: (defaultActualRentalType.value != null ? defaultActualRentalType.value.id : null),
        rentalDate: null,
        grossRental: null,
        floorArea: null,
        rentalPerSqmt: null,
        tenant: null
    };

    newRentalDateErrors.value = [];
    await nextTick();
    newActualRentalRowRentalDate.value.$el.focus();
}

function removeActualRentalRow(index) {
    worksheet.value.actualRentalRows.splice(index, 1);
}

</script>

<template>
    <div class="container-fluid">
        <div class="row table-head" data-cy="cw-actual-rental-table-head" style="padding-top: 3px;">
            <div class="col-lg-6">
                <span class="col-lg-3">
                    Date Rent Set
                </span>
                <span class="col-lg-3">
                    Annual Rental
                </span>
                <span class="col-lg-3">
                    Floor Area
                </span>
                <span class="col-lg-3">
                    Rental/m<sup>2</sup>
                </span>
            </div>
            <div class="col-lg-6">
                <span class="col-lg-3">
                    Rent Code
                </span>
                <span class="col-lg-8">
                    Comment (e.g. Tenant)
                </span>
                <span class="col-lg-1">
                    &nbsp;
                </span>
            </div>
        </div>
        <div v-for="(actualRentalRow, index) of worksheet.actualRentalRows"
            :key="actualRentalRow.actualRentalId" class="row table-row" :class="{ invalid: actualRentalRow.invalid }"
            data-cy="cw-actual-rental-row">
            <div class="col-lg-6">
                <div class="col-lg-3">
                    <label>
                        <input-date v-model="actualRentalRow.rentalDate" :errors="getRentalDateErrors(actualRentalRow)"
                            @input="handleRentalDateInput(actualRentalRow)" required
                            :readonly="isReadOnly"
                            data-cy="cw-actual-rental-date-rent" />
                    </label>
                </div>
                <div class="col-lg-3">
                    <label>
                        <input-number v-model="actualRentalRow.grossRental" format="$0,0"
                            @input="handleAnnualRentalInput(actualRentalRow)" min="0"
                            :readonly="isReadOnly"
                            data-cy="cw-actual-rental-annual-rent" />
                    </label>
                </div>
                <div class="col-lg-3">
                    <label>
                        <input-number v-model="actualRentalRow.floorArea" format="0,0.00"
                            @input="handleFloorAreaInput(actualRentalRow, $event)"
                            :readonly="isReadOnly"
                            data-cy="cw-actual-rental-area" />
                    </label>
                </div>
                <div class="col-lg-3">
                    <label>
                        <input-number v-model="actualRentalRow.rentalPerSqmt" format="$0,0"
                            @input="handleRentalPerSqmtInput(actualRentalRow)"
                            :readonly="isReadOnly"
                            data-cy="cw-actual-rental-per-sqmt" />
                    </label>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="col-lg-3">
                    <label>
                        <form-select v-model="actualRentalRow.actualRentalTypeId" :options="actualRentalTypes" :allow-empty="false" :readonly="isReadOnly" data-cy="cw-actual-rental-rent-code" />
                    </label>
                </div>
                <div class="col-lg-8">
                    <label>
                        <input-text v-model="actualRentalRow.tenant" maxLength="100" :readonly="isReadOnly" data-cy="cw-actual-rental-tenant" />
                    </label>
                </div>
                <div class="col-lg-1">
                    <i v-if="isCurrent && !isReadOnly" class="saRow-remove material-icons" data-cy="cw-remove-actual-rental-button"
                        @click="removeActualRentalRow(index)"></i>
                </div>
            </div>
        </div>
        <div v-if="!isReadOnly"
            class="row table-row"
            :class="{ invalid: newActualRentalRow.invalid }"
            data-cy="cw-new-actual-rental-row"
        >
            <div class="col-lg-6">
                <div class="col-lg-3">
                    <label>
                        <input-date v-model="newActualRentalRow.rentalDate" ref="newActualRentalRowRentalDate"
                            :errors="getRentalDateErrors(newActualRentalRow)"
                            @input="handleRentalDateInput(newActualRentalRow)"
                            data-cy="cw-new-actual-rental-date-rent"
                            required
                        />
                    </label>
                </div>
                <div class="col-lg-3">
                    <label>
                        <input-number v-model="newActualRentalRow.grossRental" format="$0,0"
                            @input="handleAnnualRentalInput(newActualRentalRow, $event)"
                            :handle-keyup="handleEnter"
                            data-cy="cw-new-actual-rental-annual-rent" />
                    </label>
                </div>
                <div class="col-lg-3">
                    <label>
                        <input-number v-model="newActualRentalRow.floorArea" format="0,0.00"
                            @input="handleFloorAreaInput(newActualRentalRow, $event)"
                            :handle-keyup="handleEnter"
                            data-cy="cw-new-actual-rental-area" />
                    </label>
                </div>
                <div class="col-lg-3">
                    <label>
                        <input-number v-model="newActualRentalRow.rentalPerSqmt" format="$0,0"
                            @input="handleRentalPerSqmtInput(newActualRentalRow, $event)"
                            :handle-keyup="handleEnter"
                            data-cy="cw-new-actual-rental-per-sqmt" />
                    </label>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="col-lg-3">
                    <label>
                        <form-select v-model="newActualRentalRow.actualRentalTypeId" :options="actualRentalTypes" data-cy="cw-new-actual-rental-rent-code" :allow-empty="false" :handle-keyup="handleEnter" :readonly="isReadOnly" />
                    </label>
                </div>
                <div class="col-lg-8">
                    <label>
                        <input-text v-model="newActualRentalRow.tenant" maxLength="100" data-cy="cw-new-actual-rental-tenant" :handle-keyup="handleEnter" :readonly="isReadOnly" />
                    </label>
                </div>
                <div class="col-lg-1">
                    <i v-if="isCurrent && !isReadOnly" class="saRow-add material-icons" data-cy="cw-add-new-actual-rental-button"
                        @click="addNewActualRentalRow()"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss">
.total-value {
    width: 100px;
}
</style>

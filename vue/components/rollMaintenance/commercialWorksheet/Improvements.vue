<script setup>
import { ref, computed, nextTick, onMounted } from 'vue';
import FormSelect from '@/components/common/form/FormSelect.vue';
import FormTextArea from '@/components/common/form/FormTextArea.vue';
import InputNumber from '@/components/common/form/InputNumber2.vue';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';
import _ from 'lodash';
import { appendPercentage } from '@/utils/FormatUtils';
import ConfirmationModal from '@/components/rollMaintenance/commercialWorksheet/CommercialWorksheetConfirmationModal.vue';
import useModal from '@/composables/useModal';

const modal = useModal();
const {
    classifications,
    worksheet,
    totalLandValue,
    newImprovementRow,
    suffixOptions,
    isApportionmentCode5,
    calculateCvIncome,
    calculateViSummation,
    calculateTotalRent,
    totalCvIncome,
    allImprovementRows,
    totalViSummation,
    calculateCvIncomeFormula,
    cvIncomeFormula,
    calculateViSummationFormula,
    viSummationFormula,
    isRevision,
    createRevision,
    isReadOnly,
    getErrorsForLabel,
} = useCommercialWorksheet();

onMounted(() => {
    newImprovementRow.value.suffix = suffixOptions.value?.[0]?.id ?? null;
});
const currentYear = new Date().getFullYear();
// refs
const newImprovementRowDescription = ref(null);

// computed
const newImprovementRowHasAtLeastOneValue = computed(() => [newImprovementRow.value.description, newImprovementRow.value.improvementId, newImprovementRow.value.area, newImprovementRow.value.carparks, newImprovementRow.value.rental, newImprovementRow.value.percentVacant, newImprovementRow.value.percentExcessLand, newImprovementRow.value.multiple, newImprovementRow.value.life, newImprovementRow.value.yearBuilt, newImprovementRow.value.percentObsolete, newImprovementRow.value.lumpSum].some(item => !_.isNil(item)));
const improvementOptions = computed(() => classifications.value.WorksheetImprovementType?.filter(item => item.commercial && item.ratingAuthorityId === worksheet.value.ratingAuthorityId) ?? []);

const totalOutgoings = computed(() => !isNaN(worksheet.value.outgoings) && Math.round(worksheet.value.outgoings) > 0 ? Math.round(nla.value * Math.round(worksheet.value.outgoings)) : null);
const totalNetRent = computed(() => allImprovementRows.value.reduce((total, improvementRow) => total + calculateTotalRent(improvementRow), 0));
const totalGrossRent = computed(() => !isNaN(worksheet.value.outgoings) && Math.round(worksheet.value.outgoings) > 0 ? totalNetRent.value + totalOutgoings.value : null);
const nla = computed(() => allImprovementRows.value.reduce((total, improvementRow) => total + _.defaultTo(improvementRow.area, 0), 0));
const nlaByTotalArea = computed(() => nla.value && worksheet.value.buildingFloorArea ? Math.round(nla.value * 100 / worksheet.value.buildingFloorArea) : null);
const isCurrent = computed(() => !isRevision.value && !createRevision.value);
const revisionPrefix = computed(() => createRevision.value || isRevision.value ? 'R' : '');

// function
function checkInvalidOrZero(value) {
    if (isNaN(value) || value === 0) {
        return null;
    }
    return value;
}

async function handleEnter(event) {
    if (isReadOnly){
        return;
    }
    if (event.key === 'Enter' && isCurrent.value) {
        addNewImprovementRow();
    }
}

async function handleEnterAndTab(event) {
    if (isReadOnly){
        return;
    }
    if (event.key === 'Enter' && isCurrent.value) {
        addNewImprovementRow();
        return;
    }
    if (event.key === 'Tab') {
        if (event.shiftKey) {
            return;
        }
        if (isCurrent.value){
            addNewImprovementRow(true, event);
        }
    }
}

function getErrors(label) {
    const errors = getErrorsForLabel(label, 'warnings');
    return errors && errors.length > 0 ? [''] : [];
}

async function addNewImprovementRow(checkForValue = false, event = null) {
    if (checkForValue && !newImprovementRowHasAtLeastOneValue.value) {
        return;
    }
    if (event) {
        event.preventDefault();
    }
    worksheet.value.improvementRows.push({ ...newImprovementRow.value, commercialWorksheetRowId: _.uniqueId('new_improvement_row_') });
    newImprovementRow.value = { suffix: (suffixOptions?.value?.[0]?.id) ?? null };
    await nextTick();
    newImprovementRowDescription.value.focus();
}

function removeImprovementRow(rowId) {
    const index = worksheet.value.improvementRows.findIndex(improvementRow => improvementRow.commercialWorksheetRowId === rowId);
    if (index !== -1) {
        worksheet.value.improvementRows.splice(index, 1);
    }
}

function validateAreaOrParks(row) {
    row.invalid = false;
    if(row.area && row.carparks){
        modal.show(ConfirmationModal, {
            title: 'Commercial Worksheet Validation',
            message: 'Area and car parks cannot be used in the same calculation.',
            confirmText: 'OK',
            showCancelBtn: false,
        });
        row.invalid = true;
    }
}

</script>

<template>
    <div class="container-fluid">
        <div class="cw-table-headers-sticky">
            <div
                class="row table-head"
                data-cy="cw-improvements-table-head"
                style="padding-top: 3px; background: white;"
            >
                <div v-if="isApportionmentCode5" class="col-lg-1">
                    Suffix
                </div>
                <div :class="{ 'col-lg-2': isApportionmentCode5, 'col-lg-3': !isApportionmentCode5 }">
                    Description
                </div>
                <div class="col-lg-1">
                    Imp Type
                </div>
                <div class="col-lg-1">
                    Area m<sup>2</sup>
                </div>
                <template v-if="worksheet.isUsingValuationMethodCv">
                    <div class="col-lg-1" style="position: relative;">
                        <div title="Weekly rent x 52">
                            Parks
                            <i class="icon icon-needsMoreInformation" style="position: absolute;" />
                        </div>
                    </div>
                    <div class="col-lg-1">
                        Rent
                    </div>
                    <div class="col-lg-1">
                        Total Rent
                    </div>
                    <div class="col-lg-1">
                        Vac%
                    </div>
                    <div class="col-lg-1">
                        XSLand%
                    </div>
                    <div class="col-lg-2" :title="cvIncomeFormula()">
                        {{revisionPrefix}}CV Income
                    </div>
                </template>
                <template v-else>
                    <div class="col-lg-1">
                        Multiple
                    </div>
                    <div class="col-lg-1">
                        Life
                    </div>
                    <div class="col-lg-1">
                        Year Built
                    </div>
                    <div class="col-lg-1">
                        Obsol%
                    </div>
                    <div class="col-lg-1">
                        Lump Sum
                    </div>
                    <div class="col-lg-2" :title="viSummationFormula()">
                        {{revisionPrefix}}VI Summation
                    </div>
                </template>
            </div>
            <div
                v-if="worksheet.isUsingValuationMethodCv && worksheet.isUsingValuationMethodVi"
                class="row table-head"
                style="padding-top: 3px; background: white;"
            >
                <div class="col-lg-5">
                    <div>&nbsp;</div>
                </div>
                <div class="col-lg-1">
                    Multiple
                </div>
                <div class="col-lg-1">
                    Life
                </div>
                <div class="col-lg-1">
                    Year Built
                </div>
                <div class="col-lg-1">
                    Obsol%
                </div>
                <div class="col-lg-1">
                    Lump Sum
                </div>
                <div class="col-lg-2" :title="viSummationFormula()">
                    {{revisionPrefix}}VI Summation
                </div>
            </div>
        </div>
        <div
            v-for="improvementRow of worksheet.improvementRows"
            :key="improvementRow.commercialWorksheetRowId"
            class="row table-row"
            :class="{ invalid: improvementRow.invalid }"
            data-cy="cw-improvement-row"
        >
            <div v-if="isApportionmentCode5" class="col-lg-1">
                <label>
                    <form-select
                        v-model="improvementRow.suffix"
                        :options="suffixOptions"
                        :allow-empty="false"
                        deselect-label="Can't remove suffix"
                        :readonly="isReadOnly"
                    />
                </label>
            </div>
            <div :class="{ 'col-lg-2': isApportionmentCode5, 'col-lg-3': !isApportionmentCode5 }">
                <label>
                    <form-text-area
                        :readonly="!isCurrent || isReadOnly"
                        maxlength="100"
                        v-model="improvementRow.description"
                        :shouldAutoGrow="true" :rows="1"
                        class="cw-textarea"
                        :preventEnter="true"
                        data-cy="cw-imp-row-description"
                        :errors="improvementRow.invalid ? [''] : []"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <form-select
                        :readonly="!isCurrent || isReadOnly"
                        v-model="improvementRow.worksheetImprovementId"
                        :options="improvementOptions"
                        data-cy="cw-imp-row-imp-type"
                        :errors="improvementRow.invalid ? [''] : []"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        :readonly="!isCurrent || isReadOnly"
                        v-model="improvementRow.area"
                        format="0,0.00"
                        @input="validateAreaOrParks(improvementRow)"
                        data-cy="cw-imp-row-area"
                    />
                </label>
            </div>
            <template v-if="worksheet.isUsingValuationMethodCv">
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :readonly="!isCurrent || isReadOnly"
                            v-model="improvementRow.carparks"
                            format="0,0"
                            :round="true"
                            @input="validateAreaOrParks(improvementRow)"
                            data-cy="cw-imp-row-parks"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            v-model="improvementRow.rental"
                            format="$0,0[.]00"
                            data-cy="cw-imp-row-rent"
                            :readonly="isReadOnly"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :value="calculateTotalRent(improvementRow)"
                            readonly
                            format="$0,0[.]00"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            v-model="improvementRow.percentVacant"
                            format="0[.]00"
                            :post-format-format="appendPercentage"
                            :max="99.99"
                            data-cy="cw-imp-row-percent-vacant"
                            :readonly="isReadOnly"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            v-model="improvementRow.percentExcessLand"
                            :custom-format="appendPercentage"
                            :min="0"
                            :max="100"
                            :round="true"
                            data-cy="cw-imp-row-percent-excess-land"
                            :readonly="isReadOnly"
                        />
                    </label>
                </div>
                <div class="col-lg-2">
                    <div class="cw-space-between" :class="{ 'cw-pad-right' : worksheet.isUsingValuationMethodCv && worksheet.isUsingValuationMethodVi }">
                        <label :title="calculateCvIncomeFormula(improvementRow)">
                            <input-number
                                :value="calculateCvIncome(improvementRow)"
                                readonly
                            />
                        </label>
                        <i
                            v-if="isCurrent && !isReadOnly"
                            class="saRow-remove material-icons"
                            data-cy="cw-remove-improvement-row"
                            @click="removeImprovementRow(improvementRow.commercialWorksheetRowId)"
                        ></i>
                    </div>
                </div>
            </template>
            <div v-if="worksheet.isUsingValuationMethodCv && worksheet.isUsingValuationMethodVi" class="col-lg-1">
                <div>&nbsp;</div>
            </div>
            <template v-if="worksheet.isUsingValuationMethodVi">
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :readonly="!isCurrent || isReadOnly"
                            v-model="improvementRow.multiple"
                            format="0[.]00"
                            data-cy="cw-imp-row-multiple"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :readonly="!isCurrent || isReadOnly"
                            v-model="improvementRow.life"
                            format="0"
                            :round="true"
                            data-cy="cw-imp-row-life"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :readonly="!isCurrent || isReadOnly"
                            v-model="improvementRow.yearBuilt"
                            placeholder="YYYY"
                            :max="currentYear"
                            format="0"
                            :min="1800"
                            :round="true"
                            data-cy="cw-imp-row-year-built"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :readonly="!isCurrent || isReadOnly"
                            v-model="improvementRow.percentObsolete"
                            format="0[.]00"
                            :post-format-format="appendPercentage"
                            data-cy="cw-imp-row-percent-obsolete"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :readonly="!isCurrent || isReadOnly"
                            v-model="improvementRow.lumpSum"
                            format="0"
                            :round="true"
                            data-cy="cw-imp-row-lump-sum"
                        />
                    </label>
                </div>
                <div class="col-lg-2">
                    <div v-if="!worksheet.isUsingValuationMethodCv" class="cw-space-between">
                        <label :title="calculateViSummationFormula(improvementRow)">
                            <input-number
                                :value="calculateViSummation(improvementRow)"
                                readonly
                            />
                        </label>
                        <i
                            v-if="isCurrent && !isReadOnly"
                            class="saRow-remove material-icons"
                            data-cy="cw-remove-improvement-row"
                            @click="removeImprovementRow(improvementRow.commercialWorksheetRowId)"
                        ></i>
                    </div>
                    <label v-else :title="calculateViSummationFormula(improvementRow)">
                        <input-number
                            style="width: 136px;"
                            :value="calculateViSummation(improvementRow)"
                            readonly
                        />
                    </label>
                </div>
            </template>
        </div>
        <div
            v-if="isCurrent && !isReadOnly"
            class="row table-row"
            data-cy="cw-new-improvement-row"
            :class="{ invalid: newImprovementRow.invalid }"
        >
            <div v-if="isApportionmentCode5" class="col-lg-1">
                <label>
                    <form-select
                        v-model="newImprovementRow.suffix"
                        :options="suffixOptions"
                        :allow-empty="false"
                        deselect-label="Can't remove suffix"
                    />
                </label>
            </div>
            <div :class="{ 'col-lg-2': isApportionmentCode5, 'col-lg-3': !isApportionmentCode5 }">
                <label>
                    <form-text-area
                        ref="newImprovementRowDescription"
                        maxlength="100"
                        v-model="newImprovementRow.description"
                        data-cy="cw-new-improvement-row-description"
                        :handle-keyup="handleEnter"
                        :shouldAutoGrow="true" :rows="1"
                        class="cw-textarea"
                        :preventEnter="true"
                        :errors="newImprovementRow.invalid ? [''] : []"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <form-select
                        v-model="newImprovementRow.worksheetImprovementId"
                        :handle-keyup="handleEnter"
                        :options="improvementOptions"
                        data-cy="cw-new-improvement-row-imp-type"
                        :errors="newImprovementRow.invalid ? [''] : []"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.area"
                        :handle-keyup="handleEnter"
                        format="0,0.00"
                        data-cy="cw-new-improvement-row-area"
                        @input="validateAreaOrParks(newImprovementRow)"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.carparks"
                        :handle-keyup="handleEnter"
                        format="0,0"
                        :round="true"
                        data-cy="cw-new-improvement-row-carparks"
                        @input="validateAreaOrParks(newImprovementRow)"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.rental"
                        :handle-keyup="handleEnter"
                        format="$0,0.00"
                        data-cy="cw-new-improvement-row-rental"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        :value="calculateTotalRent(newImprovementRow)"
                        readonly
                        format="$0,0.00"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.percentVacant"
                        :handle-keyup="handleEnter"
                        format="0[.]00"
                        :post-format-format="appendPercentage"
                        :max="99.99"
                        data-cy="cw-new-improvement-row-vacant"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.percentExcessLand"
                        :handle-keyup="handleEnter"
                        :custom-format="appendPercentage"
                        :min="0"
                        :max="100"
                        :round="true"
                        data-cy="cw-new-improvement-row-excess-land"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <div class="cw-space-between" style="padding-right: 10px;">
                    <label :title="calculateCvIncomeFormula(newImprovementRow)">
                        <input-number
                            :value="calculateCvIncome(newImprovementRow)"
                            readonly
                            data-cy="cw-new-improvement-row-cv-income"
                        />
                    </label>
                    <i v-if="isCurrent"
                        class="saRow-add material-icons"
                        data-cy="cw-add-new-improvement-row-button"
                        @click="addNewImprovementRow()"
                    ></i>
                </div>
            </div>
            <div class="col-lg-1">
                <div>&nbsp;</div>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.multiple"
                        :handle-keyup="handleEnter"
                        format="0[.]00"
                        data-cy="cw-new-improvement-row-multiple"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.life"
                        :handle-keyup="handleEnter"
                        format="0"
                        :round="true"
                        data-cy="cw-new-improvement-row-life"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.yearBuilt"
                        :handle-keyup="handleEnter"
                        placeholder="YYYY"
                        :max="currentYear"
                        format="0"
                        :min="1800"
                        :round="true"
                        data-cy="cw-new-improvement-row-year-built"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.percentObsolete"
                        :handle-keyup="handleEnter"
                        format="0[.]00"
                        :post-format-format="appendPercentage"
                        data-cy="cw-new-improvement-row-obsolete"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        v-model="newImprovementRow.lumpSum"
                        :handle-keyup="handleEnterAndTab"
                        format="0"
                        :round="true"
                        data-cy="cw-new-improvement-row-lump-sum"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label :title="calculateViSummationFormula(newImprovementRow)">
                    <input-number
                        style="width: 136px;"
                        :value="calculateViSummation(newImprovementRow)"
                        readonly
                        data-cy="cw-new-improvement-row-vi-summation"
                    />
                </label>
            </div>
        </div>
        <div class="row" style="margin-top: 1rem;">
            <div class="col-lg-10" style="padding-left: 5px;">
                <div class="row" style="margin: 0 -5px;">
                    <div class="col-lg-3 cw-total-text cw-justify-end">
                        Total Gross Rent
                        <label class="total-value">
                            <input-number
                                :value="totalGrossRent"
                                readonly
                                format="$0,0"
                                data-cy="cw-improvement-total-gross-rent"
                            />
                        </label>
                    </div>
                    <div class="col-lg-3 cw-total-text cw-justify-end">
                        Total Outgoings
                        <label class="total-value">
                            <input-number
                                :value="totalOutgoings"
                                readonly
                                format="$0,0"
                                data-cy="cw-improvement-total-outgoings"
                            />
                        </label>
                    </div>
                    <div class="col-lg-3 cw-total-text cw-justify-end">
                        Total Net Rent
                        <label class="total-value">
                            <input-number
                                :value="totalNetRent"
                                readonly
                                format="$0,0"
                                data-cy="cw-improvement-total-net-rent"
                            />
                        </label>
                    </div>
                    <div class="col-lg-3 cw-total-text" style="padding-right: 5px;">Total {{revisionPrefix}}CV Income</div>
                </div>
            </div>
            <div class="col-lg-2">
                <label class="total-value">
                    <input-number
                        style="width: 136px;"
                        :value="totalCvIncome"
                        readonly
                        format="$0,0"
                    />
                </label>
            </div>
        </div>
        <div class="row" style="margin-top: 1rem;">
            <div class="col-lg-10" style="padding-left: 5px;">
                <div class="row" style="margin: 0 -5px;">
                    <div title="Total Area from Land Use Data" class="col-lg-3 cw-total-text cw-justify-end">
                        NLA/Total Area
                        <label class="total-value">
                            <input-number
                                :value="checkInvalidOrZero(nlaByTotalArea)"
                                readonly
                                format="0"
                                :post-format-format="appendPercentage"
                                data-cy="cw-nla-by-total-area"
                            />
                        </label>
                    </div>
                    <div title="Net Lettable Area" class="col-lg-2 cw-total-text cw-justify-end">
                        NLA
                        <label class="total-value">
                            <input-number
                                :value="checkInvalidOrZero(nla)"
                                readonly
                                format="0,0.00"
                                data-cy="cw-nla"
                            />
                        </label>
                    </div>
                    <div class="col-lg-3 cw-total-text cw-justify-end">
                        Outgoings $/m²
                        <label class="total-value">
                            <input-number
                                v-model="worksheet.outgoings"
                                format="$0,0"
                                data-cy="cw-outgoings-per-mtsq"
                                :errors="getErrors('Outgoings')"
                                max="9999"
                            />
                        </label>
                    </div>
                    <div class="col-lg-2 cw-total-text cw-justify-end">
                        Cap. Rate
                        <label class="total-value">
                            <input-number
                                v-model="worksheet.capRate"
                                format="0[.]00"
                                :post-format-format="appendPercentage"
                                data-cy="cw-improvement-cap-rate"
                                :readonly="isReadOnly"
                                style="font-weight: bold;"
                            />
                        </label>
                    </div>
                    <div class="col-lg-2 cw-total-text" style="padding-right: 5px;">Total {{revisionPrefix}}VI Summation</div>
                </div>
            </div>
            <div class="col-lg-2">
                <label class="total-value">
                    <input-number
                        style="width: 136px;"
                        :value="totalViSummation"
                        readonly
                        format="$0,0"
                    />
                </label>
            </div>
        </div>
    </div>
</template>

<style lang="scss">

.total-value {
    width: 100px;
}

</style>

<script setup>
import { formatPrice } from '@/utils/FormatUtils';

const props = defineProps({
    worksheet: {
        type: Object,
        required: true,
    },
});

</script>

<template>
    <div class="container-fluid">
        <div
            class="row table-head"
            data-cy="cw-revision-values-table-head"
            style="padding-top: 3px;"
        >
            <div class="col-lg-6">
                Valuation Reference
            </div>
            <div class="col-lg-2">
                Revision Capital Value
            </div>
            <div class="col-lg-2">
                Revision Land Value
            </div>
            <div class="col-lg-2">
                Revision Value of improvements
            </div>
        </div>
        <div
            v-for="apportionment of worksheet.adoptedValues"
            :key="apportionment.apptQupid"
            class="row table-row"
            data-cy="cw-revision-assessment-row"
        >
            <div class="col-lg-6">
                <b> {{ `${worksheet.parentValref} ${apportionment.suffix || ''}` }} </b>
            </div>
            <div class="col-lg-2">
                {{ formatPrice(apportionment.revisionCapitalValue, '$0,0') }}
            </div>
            <div class="col-lg-2">
                {{ formatPrice(apportionment.revisionLandValue, '$0,0') }}
            </div>
            <div class="col-lg-2">
                {{ formatPrice(apportionment.revisionImprovementValue, '$0,0') }}
            </div>
        </div>
        <div
            class="row table-row"
            :class="{ 'cw-parent-row': worksheet.apportionmentId == 5 }"
        >
            <div class="col-lg-6">
                <b> {{ worksheet.parentValref }} </b>
            </div>
            <div class="col-lg-2">
                {{ formatPrice(worksheet.parentRcv, '$0,0') }}
            </div>
            <div class="col-lg-2">
                {{ formatPrice(worksheet.parentRlv, '$0,0') }}
            </div>
            <div class="col-lg-2">
                {{ formatPrice(worksheet.parentRvi, '$0,0') }}
            </div>
        </div>
    </div>
</template>

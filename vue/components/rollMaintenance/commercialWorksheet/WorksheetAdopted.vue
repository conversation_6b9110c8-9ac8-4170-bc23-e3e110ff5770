<script setup>
import _ from 'lodash';
import { computed } from 'vue';
import InputNumber from '@/components/common/form/InputNumber2.vue';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';
import { appendPercentage, formatPrice } from '@/utils/FormatUtils';
import PercentChange from './PercentChange.vue';

const {
    worksheet,
    isApportionmentCode5,
    adoptedCapitalValue,
    adoptedLandValue,
    adoptedImprovementValue,
    calculateApportionmentCvPercent,
    calculateApportionmentLvPercent,
    highestCvApportionmentSuffix,
    highestLvApportionmentSuffix,
    cvOverrideTotal,
    lvOverrideTotal,
    totalApportionmentCvPercent,
    totalApportionmentLvPercent,
    getApportionmentCv,
    getApportionmentLv,
    valuationMethod,
    isRevision,
    createRevision,
    isReadOnly,
    allCvOverridesProvided,
    allLvOverridesProvided,
} = useCommercialWorksheet();

const valrefColumnClass = computed(() => (isApportionmentCode5.value ? 'col-lg-2' : 'col-lg-6'));

const cvMismatch = computed(() => (adoptedCapitalValue.value - getTotalApportionmentCv.value));
const lvMismatch = computed(() => (adoptedLandValue.value - getTotalApportionmentLv.value));
const cvOverrideTotalErrors = computed(() => (allCvOverridesProvided.value && cvMismatch.value ? [formatPrice(cvMismatch.value)] : []));
const lvOverrideTotalErrors = computed(() => (allLvOverridesProvided.value && lvMismatch.value ? [formatPrice(lvMismatch.value)] : []));
const totalApportionmentCvErrors = computed(() => (!allCvOverridesProvided.value && cvMismatch.value ? [formatPrice(cvMismatch.value)] : []));
const totalApportionmentLvErrors = computed(() => (!allLvOverridesProvided.value && lvMismatch.value ? [formatPrice(lvMismatch.value)] : []));

const getTotalApportionmentCv = computed(() => {
    if (allCvOverridesProvided.value) {
        return cvOverrideTotal.value;
    }
    return worksheet.value.adoptedValues.reduce((acc, apportionment) => acc + (getApportionmentCv(apportionment) || 0), 0);
});
const getTotalApportionmentLv = computed(() => {
    if (allLvOverridesProvided.value) {
        return lvOverrideTotal.value;
    }
    return worksheet.value.adoptedValues.reduce((acc, apportionment) => acc + (getApportionmentLv(apportionment) || 0), 0);
});
const revisionPrefix = computed(() => createRevision.value || isRevision.value ? 'R' : '');
const revisionKeyword = computed(() => createRevision.value || isRevision.value ? 'Revision' : '');
const showPercentChange = computed(() => createRevision.value || isRevision.value);

function handleErrors(type, apportionment) {
    switch (type) {
        case 'CV':
            return (getApportionmentCv(apportionment) <= 0 ? [''] : []);
        case 'LV':
            return (getApportionmentLv(apportionment) <= 0 ? [''] : []);
        case 'VI':
            return (getApportionmentCv(apportionment) - getApportionmentLv(apportionment) < 0 ? [''] : []);
        case 'CV Override':
            return (valuationMethod.value === 'VI Summation' &&  (!apportionment.cvOverride || apportionment.cvOverride <=0 ) ? [''] : []);
        case 'LV Override':
            return (valuationMethod.value === 'VI Summation' &&  (!apportionment.lvOverride || apportionment.lvOverride <=0 ) ? [''] : []);
        default:
            return [];
    }
}

function getPercentageChange(type, apportionment) {
    const currentAssessmentValue = worksheet.value.currentAssessmentValues.find(a => a.qpid === apportionment?.apptQupid);
    switch (type) {
        case 'CV':
            return getPercentValue(currentAssessmentValue?.cv, getApportionmentCv(apportionment));
        case 'LV':
            return getPercentValue(currentAssessmentValue?.lv, getApportionmentLv(apportionment));
        case 'VI':
            const rvi = getApportionmentCv(apportionment) - getApportionmentLv(apportionment);
            const vi = (currentAssessmentValue.vi <= worksheet.value.revisionMinVi) ?  worksheet.value.revisionMinVi : currentAssessmentValue.vi;
            return getPercentValue(vi, rvi);
        case 'TOTAL_CV':
            return getPercentValue(worksheet.value.parentCv, getTotalApportionmentCv.value);
        case 'TOTAL_LV':
            return getPercentValue(worksheet.value.parentLv, getTotalApportionmentLv.value);
        case 'TOTAL_VI':
            return getPercentValue((worksheet.value.parentCv - worksheet.value.parentLv),(getTotalApportionmentCv.value - getTotalApportionmentLv.value));
        case 'ADOPTED_CV':
            return getPercentValue(worksheet.value.parentCv,adoptedCapitalValue.value);
        case 'ADOPTED_LV':
            return getPercentValue(worksheet.value.parentLv, adoptedLandValue.value,);
        case 'ADOPTED_VI':
            return getPercentValue((worksheet.value.parentCv - worksheet.value.parentLv), adoptedImprovementValue.value);
        default:
            return '';
    }
}

function getPercentValue(currentVal, revisionVal) {
    const val = revisionVal/currentVal;
    return !isNaN(val) ? val -1 : 0;
}

</script>

<template>
    <div class="container-fluid">
        <div data-cy="cw-adopted-table-head" class="row table-head" style="padding-top: 3px">
            <div :class="valrefColumnClass">Valuation Reference</div>
            <template v-if="isApportionmentCode5">
                <div class="col-lg-1">{{revisionPrefix}}CV%</div>
                <div class="col-lg-1">{{revisionPrefix}}LV%</div>
                <div class="col-lg-1">{{revisionPrefix}}CV Override</div>
                <div class="col-lg-1">{{revisionPrefix}}LV Override</div>
            </template>
            <div class="col-lg-2">{{revisionKeyword}} Capital Value</div>
            <div class="col-lg-2">{{revisionKeyword}} Land Value</div>
            <div class="col-lg-2">{{revisionKeyword}} Value of Improvements</div>
        </div>
        <div v-for="apportionment of worksheet.adoptedValues" :key="apportionment.apptQupid" class="row table-row"
            data-cy="cw-adopted-row">
            <div :class="valrefColumnClass">
                <b class="cw-total-text">{{ worksheet.parentValref }} {{ apportionment.suffix }}</b>
            </div>
            <template v-if="isApportionmentCode5">
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :value="calculateApportionmentCvPercent(apportionment)"
                            format="0.00"
                            :post-format-format="appendPercentage"
                            readonly
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            :value="calculateApportionmentLvPercent(apportionment)"
                            format="0.00"
                            :post-format-format="appendPercentage"
                            readonly
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            v-model="apportionment.cvOverride"
                            format="$0,0"
                            data-cy="cw-adopted-cvOverride"
                            :readonly="isReadOnly"
                            :errors="handleErrors('CV Override', apportionment)"
                        />
                    </label>
                </div>
                <div class="col-lg-1">
                    <label>
                        <input-number
                            v-model="apportionment.lvOverride"
                            format="$0,0"
                            data-cy="cw-adopted-lvOverride"
                            :errors="handleErrors('LV Override', apportionment)"
                            :readonly="isReadOnly"
                        />
                    </label>
                </div>
            </template>
            <div class="col-lg-2"
                :class="{ 'cw-max-value-apportionment' : highestCvApportionmentSuffix === apportionment.suffix }">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('CV', apportionment)"
                    />
                    <input-number
                        :value="getApportionmentCv(apportionment)"
                        format="$0,0"
                        readonly
                        class="ch-input-right"
                        :errors="handleErrors('CV', apportionment)"
                        data-cy="cw-apportionment-cv"
                    />
                </label>
            </div>
            <div class="col-lg-2"
                :class="{ 'cw-max-value-apportionment' : highestLvApportionmentSuffix === apportionment.suffix }">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('LV', apportionment)"
                    />
                    <input-number
                        :value="getApportionmentLv(apportionment)"
                        format="$0,0"
                        readonly
                        class="ch-input-right"
                        :errors="handleErrors('LV', apportionment)"
                        data-cy="cw-apportionment-lv"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('VI', apportionment)"
                    />
                    <input-number
                        :value="getApportionmentCv(apportionment) - getApportionmentLv(apportionment)"
                        class="ch-input-right"
                        format="$0,0"
                        readonly
                        :errors="handleErrors('VI', apportionment)"
                    />
                </label>
            </div>
        </div>
        <div v-if="isApportionmentCode5" class="row table-row">
            <div :class="valrefColumnClass">
                <b class="cw-total-text">Total</b>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        :value="totalApportionmentCvPercent"
                        format="0.00"
                        :post-format-format="appendPercentage"
                        readonly
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        :value="totalApportionmentLvPercent"
                        format="0.00"
                        :post-format-format="appendPercentage"
                        readonly
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        :value="cvOverrideTotal"
                        data-cy="cw-total-cv-override"
                        format="$0,0"
                        readonly
                        :errors="cvOverrideTotalErrors"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input-number
                        :value="lvOverrideTotal"
                        data-cy="cw-total-lv-override"
                        format="$0,0"
                        readonly
                        :errors="lvOverrideTotalErrors"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('TOTAL_CV')"
                    />
                    <input-number
                        :value="getTotalApportionmentCv"
                        data-cy="cw-total-apportionment-cv"
                        format="$0,0"
                        class="ch-input-right"
                        readonly
                        :errors="totalApportionmentCvErrors"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('TOTAL_LV')"
                    />
                    <input-number
                        :value="getTotalApportionmentLv"
                        data-cy="cw-total-apportionment-lv"
                        format="$0,0"
                        class="ch-input-right"
                        readonly
                        :errors="totalApportionmentLvErrors"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('TOTAL_VI')"
                    />
                    <input-number
                        class="ch-input-right"
                        :value="getTotalApportionmentCv - getTotalApportionmentLv"
                        format="$0,0"
                        readonly
                    />
                </label>
            </div>
        </div>
        <div class="row table-row" :class="{ 'cw-parent-row': isApportionmentCode5 }"
            :style="{ 'border-bottom: none;' : !isApportionmentCode5 }">
            <div class="col-lg-6">
                <b class="cw-total-text">{{ worksheet.parentValref }}</b>
            </div>
            <div class="col-lg-2">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('ADOPTED_CV')"
                    />
                    <input-number
                        :value="adoptedCapitalValue"
                        format="$0,0"
                        readonly
                        data-cy="cw-adopted-cv"
                        class="ch-input-right"
                        :errors="isApportionmentCode5 && cvMismatch ? [''] : []"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('ADOPTED_LV')"
                    />
                    <input-number
                        :value="adoptedLandValue"
                        format="$0,0"
                        readonly
                        data-cy="cw-adopted-lv"
                        class="ch-input-right"
                        :errors="isApportionmentCode5 && lvMismatch ? [''] : []"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <percent-change
                        :showPercentChange="showPercentChange"
                        :value="getPercentageChange('ADOPTED_VI')"
                    />
                    <input-number
                        class="ch-input-right"
                        :value="adoptedImprovementValue"
                        format="$0,0"
                        readonly
                        data-cy="cw-adopted-vi"
                    />
                </label>
            </div>
        </div>
    </div>
</template>

<script setup>
import ValidationMessages from '@/components/common/ValidationMessages.vue';

const props = defineProps({
  formattedErrors: {
    type: Array,
    default: () => [],
  },
  formattedWarnings: {
    type: Array,
    default: () => [],
  },
  hasErrors: {
    type: Boolean,
    default: false,
  },
  hasWarnings: {
    type: Boolean,
    default: false,
  },
  customBtnLabel: {
    type: String,
    default: 'Yes, Update Worksheet',
  },
  customWarningBtnLabel: {
    type: String,
    default: 'Accept New Values',
  },
});

const emit = defineEmits(['close']);
</script>

<template>
    <div class="qv-flex-column qv-flex-grow qv-justify-space-between">
        <div class="qv-dialog-content qv-mb-3">
            <h1 class="qv-text-lg qv-mb-2 cw-validation-title" data-cy="cw-validation-title">Commercial Worksheet Validation</h1>
            <validation-messages data-cy="cw-validation-modal-validation-messages"
                :errors="formattedErrors"
                :warnings="formattedWarnings"
            />
        </div>
        <div class="qv-flex-row qv-justify-space-between">
            <button class="qv-dialog-button qv-color-dark qv-bg-lightbuff" @click="() => emit('close', false)" data-cy="button-cw-validation-return">No, Return to worksheet</button>
            <button v-if="!hasErrors && !hasWarnings"
                class="qv-dialog-button qv-color-light qv-bg-mediumblue"
                @click="() => emit('close', true)"
                data-cy="button-cw-validation-confirm"
            >
                {{ customBtnLabel }}
            </button>
            <button v-if="hasWarnings && !hasErrors"
                class="qv-dialog-button qv-color-light qv-bg-mediumblue"
                @click="() => emit('close', true)"
                data-cy="button-cw-validation-confirm"
            > {{ customWarningBtnLabel }} </button>
        </div>
    </div>
</template>

<style lang="scss">
.cw-validation-title {
    color: var(--qv-color-error);
}
</style>

<script setup>
import { formatPrice } from '@/utils/FormatUtils';

const props = defineProps({
    worksheet: {
        type: Object,
        required: true,
    },
});

</script>

<template>
    <div class="container-fluid">
        <div
            class="row table-head"
            data-cy="cw-current-assessment-table-head"
            style="padding-top: 3px;"
        >
            <div class="col-lg-6">
                Valuation Reference
            </div>
            <div class="col-lg-2">
                Capital Value
            </div>
            <div class="col-lg-2">
                Land Value
            </div>
            <div class="col-lg-2">
                Value of Improvements
            </div>
        </div>
        <div
            v-for="currentAssessment of worksheet.currentAssessmentValues"
            :key="currentAssessment.qpid"
            class="row table-row"
            data-cy="cw-current-assessment-row"
        >
            <div class="col-lg-6">
                <b> {{ currentAssessment.valref }} </b>
            </div>
            <div class="col-lg-2">
                {{ formatPrice(currentAssessment.cv,'$0,0') }}
            </div>
            <div class="col-lg-2">
                {{ formatPrice(currentAssessment.lv,'$0,0') }}
            </div>
            <div class="col-lg-2">
                {{ formatPrice(currentAssessment.vi,'$0,0') }}
            </div>
        </div>
        <div
            v-if="worksheet.apportionmentId == 5"
            class="row cw-parent-row table-row"
        >
            <div class="col-lg-6">
                <b> {{ worksheet.parentValref }} </b>
            </div>
            <div class="col-lg-2">
                {{ formatPrice(worksheet.parentCv,'$0,0') }}
            </div>
            <div class="col-lg-2">
                {{ formatPrice(worksheet.parentLv,'$0,0') }}
            </div>
            <div class="col-lg-2">
                {{formatPrice(worksheet.parentCv - worksheet.parentLv, '$0,0')}}
            </div>
        </div>
    </div>
</template>

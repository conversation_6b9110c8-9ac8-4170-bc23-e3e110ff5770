<script setup>
import { ref, onMounted, computed } from 'vue';
import _ from 'lodash';

import FormSelect from '@/components/common/form/FormSelect.vue';
import InputText from '@/components/common/form/InputText.vue';
import InputDate from '@/components/common/form/InputDate.vue';
import InputNumber from '@/components/common/form/InputNumber2.vue';
import useCommercialWorksheet from '@/composables/useCommercialWorksheet';
import { appendPercentage } from '@/utils/FormatUtils';

const {
    classifications,
    picklistValues,
    propertyDetails,
    zoningOptions,
    worksheet,
    property,
    earthquakeRatingRangeRef,
    earthquakeRatingAssessorRef,
    liquefactionRef,
    proposedZoneRef,
    qvCategoryRef,
    commercialGroupingRef,
    isRevision,
    createRevision,
    getErrorsForLabel,
    isReadOnly,
} = useCommercialWorksheet();

const loaded = ref(false);

const earthquakeRatingRangeOptions = computed(() => classifications.value.EarthquakeRating?.map(item => ({ ...item, id: item.code })) ?? []);
const earthquakeRatingAssessorOptions = computed(() => classifications.value.EarthquakeRatingAssessor?.map(item => ({ ...item, id: item.code })) ?? []);
const liquefactionOptions = computed(() => classifications.value.LiquefactionRating?.map(item => ({ ...item, id: item.code })) ?? []);
const groupingOptions = computed(() => classifications.value.PropertyGroupingTypeCommercial?.map(item => ({ ...item, id: item.code })) ?? []);
const qualityOptions = computed(() => classifications.value.QualityType);
const locationOptions = computed(() => classifications.value.LocationType?.filter(item => item.ratingAuthorityId === worksheet.value.ratingAuthorityId)) ?? [];
const actualEarthquakeRatingErrors = computed(() => {
    const errors = [];
    const value = propertyDetails.value.actualEarthquakeRating;
    const rangeCode = propertyDetails.value.earthquakeRatingRange;
    const rangeObject = earthquakeRatingRangeOptions.value.find(item => item.code === rangeCode);
    const [min, max] = rangeObject?.description?.replace('%', '').split('-') ?? [0, 0];
    if (!_.isNil(value) && (value < parseFloat(min) || value > parseFloat(max))) {
        errors.push('Actual Earthquake Rating must be within Earthquake Rating Range');
    }
    return errors;
});
const isCurrent = computed(() => !isRevision.value && !createRevision.value);

function handleActualEarthquakeRatingInput(value) {
    const match = earthquakeRatingRangeOptions.value.find((item) => {
        const [min, max] = item.description.replace('%', '').split('-');
        return value >= parseFloat(min) && value <= (parseFloat(max) || Infinity);
    });
    propertyDetails.value.earthquakeRatingRange = match?.code ?? null;
}

function getErrors(label) {
    const errors = getErrorsForLabel(label);
    return errors && errors.length > 0 ? [''] : [];
}

onMounted(() => {
    loaded.value = true;
});

</script>


<template>
    <div>
        <div class="row" >
            <div class="col col-lg-3">
                <label>
                    <span class="label" data-cy="cw-pd-qv-category-label">QV Category</span>
                    <form-select
                        v-model="propertyDetails.qvCategoryCode"
                        :readonly="!isCurrent || isReadOnly"
                        :options="picklistValues.qvCategoryTypes.map((item) => ({ ...item, id: item.code }))"
                        :close-on-select="true"
                        :errors="[]"
                        ref="qvCategoryRef"
                        data-cy="cw-pd-qv-category"
                    />
                </label>
            </div>
            <div class="col col-lg-2">
                <label>
                    <span class="label">Grouping</span>
                    <form-select
                        v-model="propertyDetails.commercialGroupingCode"
                        :readonly="!isCurrent || isReadOnly"
                        :options="groupingOptions"
                        :errors="[]"
                        ref="commercialGroupingRef"
                        data-cy="cw-pd-grouping"
                    />
                </label>
            </div>
            <div class="col col-lg-2">
                <label>
                    <span class="label">Proposed Zone</span>
                    <form-select
                        v-model="propertyDetails.proposedZoneCode"
                        :readonly="!isCurrent || isReadOnly"
                        :options="zoningOptions.map(item => ({ ...item, id: item.code, description: item.shortDescription }))"
                        :errors="[]"
                        ref="proposedZoneRef"
                        data-cy="cw-pd-proposed-zone"
                    />
                </label>
            </div>
            <div class="col col-lg-1">
                <label>
                    <span class="label">Plan #</span>
                    <input-text
                        v-model="worksheet.planNumber"
                        data-cy="cw-pd-plan-number"
                        :readonly="!isCurrent || isReadOnly"
                        maxlength="5"
                    />
                </label>
            </div>
            <div class="col col-lg-1">
                <label>
                    <span class="label">Zone</span>
                    <input-text
                        :value="property.property.landUseData.landZone"
                        readonly
                        data-cy="cw-pd-zone"
                    />
                </label>
            </div>
            <div class="col col-lg-1">
                <label>
                    <span class="label">Quality</span>
                    <form-select
                        v-model="worksheet.qualityId"
                        :readonly="!isCurrent || isReadOnly"
                        :options="qualityOptions"
                        :errors="[]"
                        data-cy="cw-pd-quality"
                    />
                </label>
            </div>
            <div class="col col-lg-2">
                <label>
                    <span class="label">Location</span>
                    <form-select
                        v-model="worksheet.locationId"
                        :readonly="!isCurrent || isReadOnly"
                        :options="locationOptions"
                        :errors="[]"
                        data-cy="cw-pd-location"
                    />
                </label>
            </div>
        </div>
        <div class="row">
            <div class="col col-lg-2">
                <label>
                    <span class="label">Actual Earthquake Rating</span>
                    <input-number
                        v-model="propertyDetails.actualEarthquakeRating"
                        :round="true"
                        :max="999"
                        :readonly="!isCurrent || isReadOnly"
                        :errors="actualEarthquakeRatingErrors"
                        :custom-format="appendPercentage"
                        @input="handleActualEarthquakeRatingInput"
                        data-cy="cw-pd-actual-eq-rating"
                    />
                </label>
            </div>
            <div class="col col-lg-3">
                <label>
                    <span class="label">Earthquake Rating Range</span>
                    <form-select
                        v-model="propertyDetails.earthquakeRatingRange"
                        :readonly="!isCurrent || isReadOnly"
                        :options="earthquakeRatingRangeOptions"
                        :errors="[]"
                        :tabindex="-1"
                        ref="earthquakeRatingRangeRef"
                        data-cy="cw-pd-eq-rating-range"
                    />
                </label>
            </div>
            <div class="col col-lg-3">
                <label>
                    <span class="label">Earthquake Rating Assessor</span>
                    <form-select
                        v-model="propertyDetails.earthquakeRatingAssessor"
                        :readonly="!propertyDetails.earthquakeRatingRange || !isCurrent || isReadOnly"
                        :options="earthquakeRatingAssessorOptions"
                        :errors="[]"
                        ref="earthquakeRatingAssessorRef"
                        data-cy="cw-pd-eq-rating-assessor"
                    />
                </label>
            </div>
            <div class="col col-lg-1">
                <label title="The year the remedial earthquake work must be done by">
                    <span class="label" style="white-space: nowrap;" >Remedy Deadline</span>
                    <input-number
                        v-model="propertyDetails.remedyYear"
                        format="0"
                        placeholder="YYYY"
                        maxlength="4"
                        :readonly="!isCurrent || isReadOnly"
                        :errors="getErrors('Remedy Deadline')"
                        data-cy="cw-pd-remedy-year"
                    />
                </label>
            </div>
            <div class="col col-lg-3">
                <label>
                    <span class="label">Liquefaction (TC Rating)</span>
                    <form-select
                        v-model="propertyDetails.liquefaction"
                        :readonly="!isCurrent || isReadOnly"
                        :options="liquefactionOptions"
                        :errors="[]"
                        ref="liquefactionRef"
                        data-cy="cw-pd-liquefaction"
                    />
                </label>
            </div>
        </div>
        <div class="row">
            <div class="col col-lg-5">
                <label>
                    <span class="label">Property Name</span>
                    <input-text
                        v-model="worksheet.propertyName"
                        readonly
                        :errors="[]"
                        data-cy="cw-pd-property-name"
                    />
                </label>
            </div>
            <div class="col col-lg-2">
                <label title="Current Revision Date">
                    <span class="label">Valuation Date</span>
                    <input-date
                        v-model="worksheet.currentRevisionDate"
                        readonly
                        :errors="[]"
                        data-cy="cw-pd-valuation-date"
                    />
                </label>
            </div>
            <div class="col col-lg-1">
                <label>
                    <span class="label">Modal</span>
                    <input-number
                        v-model="worksheet.modal"
                        readonly
                        :errors="getErrors('Modal')"
                        data-cy="cw-ta-modal"
                    />
                </label>
            </div>
            <div class="col col-lg-1">
                <label>
                    <span class="label" style="font-weight:bold;">Cap. Rate</span>
                    <input-number
                        v-model="worksheet.capRate"
                        format="0[.]00"
                        :errors="getErrors('Cap Rate')"
                        :post-format-format="appendPercentage"
                        data-cy="cw-pd-cap-rate"
                        :readonly="isReadOnly"
                        style = "font-weight: bold;"
                    />
                </label>
            </div>
            <div class="col col-lg-3">
                <label>
                    <span title="Display or hide valuation method rows in improvements section" class="label">View Valuation Methods</span>
                    <div class="cw-checkbox-label-container">
                        <input type="checkbox" id="CV" data-cy="cw-pd-cv" v-model="worksheet.isUsingValuationMethodCv" tabindex="-1" :disabled="!worksheet.isUsingValuationMethodVi || isReadOnly" style="width: 1.5rem;">
                        <label for="CV">CV Income</label>
                        <input type="checkbox" id="VI" data-cy="cw-pd-vi" v-model="worksheet.isUsingValuationMethodVi" tabindex="-1" :disabled="!worksheet.isUsingValuationMethodCv || isReadOnly" style="width: 1.5rem; margin-left: 1rem;">
                        <label for="VI">VI Summation</label>
                    </div>
                </label>
            </div>
        </div>
    </div>
</template>

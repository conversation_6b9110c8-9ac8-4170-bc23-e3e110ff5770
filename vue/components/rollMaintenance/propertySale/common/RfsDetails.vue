<script setup>
import { formatDate } from '@/utils/FormatUtils';

const props = defineProps({
    saleId: {
        required: true,
    },
    qpid: {
        required: true,
    },
    rfs: {
        type: Object,
        required: true,
    },
});
</script>

<template>
    <div class="rfs-container">
        <div class="rfs-labels">
            <label>RFS Date</label>
            <label>RFS Number</label>
            <label>Sale Id</label>
            <label>Sale Qpid</label>
        </div>
        <div class="rfs-values">
            <span>{{ formatDate(props.rfs.date) }}</span>
            <span>{{ props.rfs.number }}</span>
            <span>{{ props.saleId }}</span>
            <span>{{ props.qpid }}</span>
        </div>
    </div>
</template>


<style lang="scss">
.rfs-container {
    font-size: 0.9rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.rfs-labels {
    label {
        display: block;
        text-wrap: nowrap;
    }
}

.rfs-values {
    span {
        display: block;
        text-wrap: nowrap;
    }
}
</style>

<script setup>
import ValidationMessages from '@/components/common/ValidationMessages.vue';

const props = defineProps({
  formattedErrors: {
    type: Array,
    default: () => [],
  },
  formattedWarnings: {
    type: Array,
    default: () => [],
  },
  hasErrors: {
    type: Boolean,
    default: false,
  },
  customBtnLabel: {
    type: String,
    default: 'Confirm',
  },
  showUpdateSaleMsg: {
    type: Boolean,
    default: false,
  },
  href: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['close']);
</script>

<template>
    <div class="qv-flex-column qv-flex-grow qv-justify-space-between">
        <div class="qv-dialog-content qv-mb-3">
            <h1 class="qv-text-lg qv-mb-2 sale-validation-title">Sale Validation</h1>
            <p v-if="showUpdateSaleMsg" class="qv-text-sm qv-mb-2 sale-validation-title">*Clicking [Update Sale] will ignore changes made on this screen. </p>
            <validation-messages data-cy="sale-validation-modal-validation-messages"
                :errors="formattedErrors"
                :warnings="formattedWarnings"
            />
        </div>
        <div class="qv-flex-row qv-justify-space-between">
            <button class="qv-dialog-button qv-color-dark qv-bg-lightbuff" @click="() => emit('close', false)">Close</button>
            <button
                class="qv-dialog-button qv-color-light qv-bg-mediumblue" :disabled="hasErrors"
                @click="() => emit('close', true)"
                data-cy="button-sale-validation-confirm"
                :data-url="href"
            >
                {{ customBtnLabel }}
            </button>
        </div>
    </div>
</template>

<style lang="scss">
.sale-validation-title {
    color: var(--qv-color-error);
}
</style>

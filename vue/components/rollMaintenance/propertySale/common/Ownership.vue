<template>
    <div class="property-draft-section col-container">
        <div class="col-row">
            <div class="col col-12">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small righty"
                    title=""
                    v-on:click="addOwner()"
                    tabindex="-1"
                >
                    Add Owner/Occupier
                </button>
            </div>
        </div>
        <div class="col-row" v-for="(owner, index) in myOwners" :key="owner.id">
            <div class="col col-12">
                <div class="col-container" style="border-bottom: 0.1rem dashed #e2e2e2;padding-bottom:10px;">
                    <div class="col-row reduce-margins">
                        <div class="col col-7">
                            <div class="col-container">
                                <div class="col-row">
                                    <div class="col col-2">
                                        <label>
                                            <span class="label" v-if="index === 0">Occupier</span>
                                            <select v-model.number="owner.type1">
                                                <option v-for="option in opts.ownerOccupierType1" :key="option.code" :value="option.code">
                                                    {{ option.label }}
                                                </option>
                                            </select>
                                        </label>
                                    </div>
                                    <div class="col col-2">
                                        <label>
                                            <span class="label" v-if="index === 0">Order</span>
                                            <select 
                                                :value="index + 1"
                                            >
                                                <option v-for="index in 26" :key="index">
                                                    {{ index }}
                                                </option>
                                            </select>
                                        </label>
                                    </div>
                                    <div class="col col-4">
                                        <label>
                                            <span class="label" v-if="index === 0">Type</span>
                                            <select v-model.number="owner.type2">
                                                <option v-for="option in opts.ownerOccupierType2" :key="option.code" :value="option.code">
                                                    {{ option.code }} - {{ option.label }}
                                                </option>
                                            </select>
                                        </label>
                                    </div>
                                    <div class="col col-4">
                                        <label>
                                            <span class="label" v-if="index === 0">Surname/Organisation</span>
                                            <input
                                                type="text"
                                                v-model="owner.name"
                                            />
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-5">
                            <div class="col-container">
                                <div class="col-row">
                                    <div class="col col-3">
                                        <label>
                                            <span class="label" v-if="index === 0">First Name</span>
                                            <input
                                                type="text"
                                                v-model="owner.firstName"
                                            />
                                        </label>
                                    </div>
                                    <div class="col col-3">
                                        <label>
                                            <span class="label" v-if="index === 0">Second Name</span>
                                            <input
                                                type="text"
                                                v-model="owner.secondName"
                                            />
                                        </label>
                                    </div>
                                    <div class="col col-3">
                                        <label>
                                            <span class="label" v-if="index === 0">Third Name</span>
                                            <input
                                                type="text"
                                                v-model="owner.thirdName"
                                            />
                                        </label>
                                    </div>
                                    <div class="col col-1">
                                        <label>
                                            <span class="label" v-if="index === 0">&nbsp;</span>
                                            <input
                                                type="checkbox"
                                                title="Secret"
                                                tabindex="-1"
                                            >
                                        </label>
                                    </div>
                                    <div class="col col-2">
                                        <label>
                                            <span class="label" v-if="index === 0">&nbsp;</span>
                                        </label>
                                        <input type="button" 
                                            v-if="index !== 0"
                                            value="Remove" 
                                            class="mdl-button mdl-button-small mdl-js-button mdl-button--raised mdl-js-ripple-effect" 
                                            v-on:click="removeOwner(index)"
                                            tabindex="-1"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-row reduce-margins" v-if="index !== 0">
                        <div class="col col-4">
                            <input
                                type="checkbox"
                                tabindex="-1"
                                style="vertical-align: middle;"
                                v-model="owner.contactDetails.sameAsPrimary"
                            >
                            <span style="font-size: 1.1rem;line-height: 1.6;color: #0e3a83;height: 2.0rem;">Contact Details: Same as Primary</span>
                        </div>
                        <div class="col col-4">
                            <input
                                type="checkbox"
                                tabindex="-1"
                                style="vertical-align: middle;"
                                v-model="owner.contactAddress.sameAsPrimary"
                            >
                            <span style="font-size: 1.1rem;line-height: 1.6;color: #0e3a83;height: 2.0rem;">Contact Address: Same as Primary</span>
                        </div>
                    </div>
                    <div class="col-row" v-if="index === 0 || !owner.contactDetails.sameAsPrimary || !owner.contactAddress.sameAsPrimary">
                        <div class="col col-4">
                            <div class="col-container" v-if="index === 0 || !owner.contactDetails.sameAsPrimary">
                                <div class="col-row">
                                    <div class="col col-12">
                                        Contact Details
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-3">
                                        <label>
                                            <span class="label" style="white-space: nowrap;">Daytime Phone</span>
                                            <select>
                                                <option>03</option>
                                                <option>04</option>
                                                <option>06</option>
                                                <option>09</option>
                                            </select>
                                        </label>
                                    </div>
                                    <div class="col col-5">
                                        <label>
                                            <span class="label">&nbsp;</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-3">
                                        <label>
                                            <span class="label" style="white-space: nowrap;">Mobile Phone</span>
                                            <select>
                                                <option>020</option>
                                                <option>021</option>
                                                <option>022</option>
                                                <option>024</option>
                                                <option>025</option>
                                                <option>027</option>
                                                <option>029</option>
                                            </select>
                                        </label>
                                    </div>
                                    <div class="col col-5">
                                        <label>
                                            <span class="label">&nbsp;</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-12">
                                        <label>
                                            <span class="label">Email</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-12">
                                        <input type="button" 
                                            value="Clear" 
                                            class="mdl-button mdl-button-small mdl-js-button mdl-button--raised mdl-js-ripple-effect" 
                                            tabindex="-1"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-4">
                            <div class="col-container" v-if="index === 0 || !owner.contactAddress.sameAsPrimary">
                                <div class="col-row">
                                    <div class="col col-12">
                                        <h3>Contact Address</h3>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-12">
                                        <label>
                                            <span class="label">C/O</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-6">
                                        <label>
                                            <span class="label">Street / Box Number</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                    <div class="col col-6">
                                        <label>
                                            <span class="label">Unit/Addl Number</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-12">
                                        <label>
                                            <span class="label">Street Name</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-12">
                                        <label>
                                            <span class="label">Town</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-4">
                                        <label>
                                            <span class="label">Postcode</span>
                                            <input
                                                type="text"
                                            />
                                        </label>
                                    </div>
                                </div>
                                <div class="col-row reduce-margins">
                                    <div class="col col-12">
                                        <label>
                                            <span class="label">Country</span>
                                            <input
                                                type="text"
                                                value="New Zealand"
                                            />
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col col-4" v-if="index === 0 || !owner.contactAddress.sameAsPrimary">
                            <div style="display:table;background:#CCC;margin:0 auto;width:90%;height:200px;padding:10px;margin-top:50px;">
                                <div style="display:table-cell;vertical-align:middle;margin:0 auto;border:1px dashed #000;width:100%;height:100%;padding:20px;text-align:center;font-weight:bold;">
                                    NZ Post Address Check <br/> Placeholder
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-row" v-if="myOwners.length > 2">
            <div class="col col-12">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small righty"
                    title=""
                    v-on:click="addOwner()"
                    tabindex="-1"
                >
                    Add Owner/Occupier
                </button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        id: {
            type: String,
            default: "test",
        },
        sale: {
            type: Object
        },
        property: {
            type: Object
        },
    },
    data() {
        return {
            mySale: this.sale,
            myProperty: this.property,
            opts: {
                ownerOccupierType1: [
                    { code: 1, label: 'Occupier' },
                    { code: 2, label: 'Owner 1' },
                    { code: 3, label: 'Owner 2' },
                    { code: 4, label: 'Owner 3' },
                ],
                ownerOccupierType2: [
                    { code: 1, label: 'Private: Individual' },
                    { code: 2, label: 'Private: Company/Organisation' },
                    { code: 3, label: 'Core Crow: e.g. Ministry/Dept' },
                    { code: 4, label: 'Local Authority e.g. TA or RC' },
                    { code: 5, label: 'Crown: SOE, DHB, Education etc.' },
                    { code: 6, label: 'Maori Land: Private Individual' },
                    { code: 7, label: 'Maori Land: Many Owners or Org' },
                ]
            },
            myOwners: [
                {
                    id: 1,
                    type1: 1,
                    order: 1,
                    type2: 2,
                    name: "Test 1",
                    firstName: "",
                    secondName: "",
                    thirdName: "",
                    contactDetails: {
                        sameAsPrimary: false,
                    },  
                    contactAddress: {
                        sameAsPrimary: false,
                    },  
                },
                {
                    id: 2,
                    type1: 3,
                    order: 1,
                    type2: 5,
                    name: "Test 2",
                    firstName: "",
                    secondName: "",
                    thirdName: "",  
                    contactDetails: {
                        sameAsPrimary: true,
                    },  
                    contactAddress: {
                        sameAsPrimary: true,
                    },                    
                },
                {
                    id: 3,
                    type1: 2,
                    order: 1,
                    type2: 4,
                    name: "Test 3",
                    firstName: "",
                    secondName: "",
                    thirdName: "",   
                    contactDetails: {
                        sameAsPrimary: true,
                    },  
                    contactAddress: {
                        sameAsPrimary: true,
                    },                   
                }
            ]
        }
    },
    methods: {
        addOwner() {
            if (this.myOwners.length < 26){
                let newId = (Math.max.apply(Math, this.myOwners.map(function(o) { return o.id; })) || 0) + 1 
                this.myOwners.push({
                        id: newId,
                        type1: 2,
                        order: 1,
                        type2: 4,
                        name: "Test " + newId,
                        firstName: "",
                        secondName: "",
                        thirdName: "",
                        contactDetails: {
                            sameAsPrimary: true,
                        },  
                        contactAddress: {
                            sameAsPrimary: true,
                        },  
                    });
            }
        },
        removeOwner(index){
            this.myOwners.splice(index, 1);
        },
    },
    watch:{
        mySale:{
            handler: function() {
                this.sale = this.mySale;
                //console.log(this.mySale);
            },
            deep:true,
        },
        myProperty:{
            handler: function() {
                this.property = this.myProperty;
                //console.log(this.mySale);
            },
            deep:true,
        }
    }
}
</script>
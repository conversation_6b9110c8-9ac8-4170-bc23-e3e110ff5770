<template>
    <div class="property-draft-section col-container">
        <div class="col-row">
            <div class="col col-12">
                <h3>Sale Comment</h3>
                <label>
                    <textarea
                        type="text"
                        v-model="saleDetail.salesAnalysisComment"
                        style="height:139px;"
                        :readonly="editingRestricted"
                    >
                    </textarea>
                </label>
            </div>
            <div class="col col-3">
                <h3>Total Property Rent</h3>
                <div class="col-container">
                    <div class="col-row">
                        <div class="col col-12">
                            <label>
                                <span class="label">Actual ($ Per Week)</span>
                                <number-input 
                                    :value="totalPropertyRent" 
                                    format="$0,0"
                                    @blur="($event) => totalPropertyRent = toNumber($event.srcElement.value)"
                                />
                            </label>
                        </div>
                    </div>
                    <div class="col-row">
                        <div class="col col-12">
                            <label>
                                <span class="label">Known (MM/YYYY)</span>
                                <date-picker
                                    type="month"
                                    v-model="knownDate"
                                    format="MM/YYYY"
                                >
                                </date-picker>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    props:{
        saleDetail: {
            type: Object
        },
        editingRestricted: {
            type: Boolean
        },
    },
}
</script>

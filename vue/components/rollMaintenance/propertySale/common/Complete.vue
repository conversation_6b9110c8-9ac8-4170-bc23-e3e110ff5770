<template>
    <div class="property-draft-section col-container">
        <div class="col-row">
            <div class="col col-12">
                <h3>Reason for Change</h3>
            </div>
        </div>
        <div class="col-row">
            <div class="col col-3">
                <label>
                    <span class="label">Output Code</span>
                    <select>
                        <option></option>
                        <option v-for="option in classifications.ReasonOutput" :key="option.code" :value="option.code">
                            {{ option.code }} - {{ option.description }}
                        </option>
                    </select>
                </label>
            </div>
            <div class="col col-3">
                <label>
                    <span class="label">Source</span>
                    <select>
                        <option></option>
                        <option v-for="option in classifications.ReasonSource" :key="option.code" :value="option.code">
                            {{ option.description }}
                        </option>
                    </select>
                </label>
            </div>
            <div class="col col-6">
                <label>
                    <span class="label">Reason for Change</span>
                    <input
                        type="text"
                    />
                </label>
            </div>
        </div>
        <div class="col-row" v-if="saleDetail.rfs && saleDetail.rfs.number">
            <div class="col col-2">
                <label>
                    <span class="label">RFS Number</span>
                    <input
                        type="text"
                        v-model="saleDetail.rfs.number"
                        :readonly="editingRestricted || !rfsEditable"
                    />
                </label>
            </div>
            <div class="col col-2">
                <label>
                    <span class="label">RFS Date</span>
                    <date-picker
                        v-model="saleDetail.rfs.date"
                        type="date"
                        format="D/M/YYYY"
                        value-type="YYYY-MM-DD"
                        :editable="!editingRestricted && rfsEditable"
                        :disabled="editingRestricted || !rfsEditable"
                    >
                    </date-picker>
                </label>
            </div>
            <div class="col col-2" style="text-align:center;">
                <label>
                    <span class="label" style="white-space: nowrap;">Close Action Record</span>
                    <input
                        type="checkbox"
                        :disabled="editingRestricted || !rfsEditable"
                    >
                </label>
            </div>
        </div>
    </div>
</template>

<script>
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';

export default {
    components: {
        DatePicker,
    },
    props: {
        saleDetail: {
            type: Object
        },
        classifications: {
            type: Object
        },
        editingRestricted: {
            type: Boolean
        },
    },
    computed:{
        rfsEditable(){
            return this.saleDetail.rfs.status != 2;
        }
    }
}
</script>
<template>
    <div>
        <div
            v-if="propertyLoaded && draftProperty.isStale"
            class="righty message message-warning"
        >
            NOTE: The detailed information for this property has changed, please review the
            <router-link :to="{name: 'property', params: {qpid: qpid}}" target="_blank">current property</router-link>
            and update the draft property details.
        </div>
        <div
            v-if="propertyLoaded && classificationsLoaded"
            class="col-container"
        >
            <div v-if="consentJobUpdateInfo" class="updateInformation">
                <ul>
                    <li>Job Created By: <span>{{createdBy}}</span></li>
                    <li>Created On: <span>{{createdAt}}</span></li>
                </ul>
            </div>
            <validation-header-message
                ref="validationHeader"
                :validation-set="validationSet"
                :show-errors="true"
                action="save the draft property"
            />
            <alert-modal
                v-if="showWarnings"
                warning
            >
                <h3 id="errorHeader">
                    Do you want to proceed?
                </h3>
                <p>The following validation checks are failing:</p>
                <div class="validation-header-message--warnings">
                    <validation-header-message
                        ref="warnings"
                        :validation-set="{errors:validationSet.warnings}"
                        :show-errors="true"
                        message=""
                    />
                </div>
                <template #buttons>
                    <div class="alertButtons">
                        <button
                            id="errorCancel"
                            class="mdl-button mdl-button--mini lefty"
                            @click="hideWarningModal()"
                        >
                            No, Return to draft
                        </button>
                        <button
                            id="continue"
                            class="mdl-button mdl-button--mini"
                            @click="completeSetupWithWarnings()"
                        >
                            Yes, Complete Setup
                        </button>
                    </div>
                </template>
            </alert-modal>
        </div>
        <h2 class="section-title">
            General Property Information
            <div
                v-if="isResidentialVacant"
                class="righty"
            >
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small"
                    title="Populates some fields with default values for new dwellings."
                    :disabled="draftSaving || editingRestricted"
                    @click="generatePropertyDetailNewDwellingInformation()"
                >
                    Populate DVR Data
                </button>
            </div>
        </h2>

        <div class="property-draft-section" v-if="draftProperty">
            <div class="col-row">
                <div class="col col-4">
                    <label>
                        <span class="label">Category</span>
                        <classification-dropdown
                            id="category"
                            category="Category_DVR"
                            :value="draftProperty.category"
                            label="code"
                            :single-label-function="(opt) => `${opt.code} — ${opt.description}`"
                            :class="errorClasses('category')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="category"
                        />
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span
                            class="label"
                            title="To add multiple of an improvement type double click on a selection and enter the number."
                        >
                            Nature of Improvements
                        </span>
                        <nature-of-improvements
                            id="natureOfImprovements"
                            :value="draftProperty.natureOfImprovements"
                            :class="errorClasses('natureOfImprovements')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="natureOfImprovements"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">Property Name</span>
                        <input
                            id="propertyName"
                            type="text"
                            :value="draftProperty.propertyName"
                            :class="errorClasses('propertyName')"
                            @input="updateText('propertyName', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="propertyName"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-4">
                    <label>
                        <span class="label">Land Use</span>
                        <classification-dropdown
                            id="landUse"
                            category="LandUse_DVR"
                            :value="draftProperty.landUse.landUse"
                            :single-label-function="(opt) => `${opt.code} — ${opt.description}`"
                            :class="errorClasses('landUse.landUse')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="landUse.landUse"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">TA Land Zone</span>
                        <classification-dropdown
                            v-if="taCode"
                            id="landZone"
                            :category="taLandZoneClassification"
                            :value="draftProperty.landUse.landZone"
                            :class="errorClasses('landUse.landZone')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <span
                            v-else
                            class="loading"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="landUse.landZone"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Effective Land Area, ha</span>
                        <input
                            id="effectiveLandArea"
                            type="number"
                            min="0"
                            step="0.0001"
                            :value="draftProperty.site.effectiveLandArea | hectares"
                            :class="errorClasses('site.effectiveLandArea')"
                            @change="updateNumber('effectiveLandArea', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="site.effectiveLandArea"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Land Area, ha</span>
                        <input
                            id="landArea"
                            type="number"
                            min="0"
                            step="0.0001"
                            readonly
                            :value="draftProperty.site.landArea | hectares"
                        >
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Māori Land</span>
                        <input
                            type="text"
                            readonly
                            :value="draftProperty.landUse.isMaoriLand ? 'Yes' : 'No'"
                            :class="errorClasses('isMaoriLand')"
                        >
                        <!--
                        <span
                            v-if="propertyDetail.landUse.isMaoriLand"
                            class="readonly-text"
                        >
                            ✔ Is Māori Land
                        </span>
                        <span
                            v-else
                            class="readonly-text"
                        >
                            ❌ Not Māori Land
                        </span>
                        -->
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Plan ID</span>
                        <input
                            type="text"
                            readonly
                            :value="draftProperty.planNumber"
                            :class="errorClasses('planNumber')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="propertyName"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Production</span>
                        <input
                            id="production"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.landUse.production"
                            :class="errorClasses('landUse.production')"
                            @input="updateNumber('production', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="landUse.production"
                        />
                    </label>
                </div>
            </div>
        </div>
        <h2 class="section-title">
            Location Details
        </h2>
        <div class="property-draft-section">
            <div class="col-row">
                <div class="col col-1">
                    <label>
                        <span class="label">Lot position</span>
                        <classification-dropdown
                            id="lotPosition"
                            category="LotPosition_DVR"
                            :value="draftProperty.site.lotPosition"
                            :class="errorClasses('site.lotPosition')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.lotPosition"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Contour</span>
                        <classification-dropdown
                            id="contour"
                            category="Contour_DVR"
                            :value="draftProperty.site.contour"
                            :class="errorClasses('site.contour')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.contour"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">View</span>
                        <classification-dropdown
                            id="view"
                            category="View_DVR"
                            :value="draftProperty.site.view"
                            :class="errorClasses('site.view')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.view"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">View Scope</span>
                        <classification-dropdown
                            id="viewScope"
                            category="ViewScope_DVR"
                            :value="draftProperty.site.viewScope"
                            :class="errorClasses('site.viewScope')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.viewScope"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">Class of Surrounding Improvements (CSI)</span>
                        <classification-dropdown
                            id="classOfSurroundingImprovements"
                            category="ClassOfSurroundingImprovements_DVR"
                            :value="draftProperty.site.classOfSurroundingImprovements"
                            :class="errorClasses('site.classOfSurroundingImprovements')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.classOfSurroundingImprovements"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Outlier</span>
                        <span class="field">
                            <yes-no-indeterminate-dropdown
                                id="isOutlier"
                                :value="draftProperty.isOutlier"
                                :class="errorClasses('isOutlier')"
                                @input="update"
                                :disabled="editingRestricted"
                            />
                        </span>
                        <validation-message
                            :validation-set="validationSet"
                            field="isOutlier"
                        />
                    </label>
                </div>
            </div>
        </div>
        <h2 class="section-title">
            Property Summary
        </h2>
        <div class="property-draft-section">
            <div class="col-row">
                <div class="col col-3">
                    <label>
                        <span class="label">House Type</span>
                        <classification-dropdown
                            id="houseType"
                            category="HouseType_DVR"
                            :value="draftProperty.summary.houseType"
                            :class="errorClasses('summary.houseType')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.houseType"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Units of Use</span>
                        <input
                            id="units"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.units"
                            :class="errorClasses('summary.units')"
                            @input="updateNumber('units', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.units"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span
                            class="label"
                            title="This is the building age from the land use data"
                        >Age</span>
                        <classification-dropdown
                            id="age"
                            category="Age_DVR"
                            :value="draftProperty.summary.age"
                            :label-function="(opt) => opt.description"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.age"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Effective Year Built</span>
                        <input
                            id="effectiveYearBuilt"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.effectiveYearBuilt"
                            :class="errorClasses('summary.effectiveYearBuilt')"
                            @input="updateNumber('effectiveYearBuilt', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.effectiveYearBuilt"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Has Poor Foundations"
                        >
                            Poor Fdn.
                        </span>
                        <yes-no-indeterminate-dropdown
                            id="hasPoorFoundations"
                            :value="draftProperty.summary.hasPoorFoundations"
                            :class="errorClasses('summary.hasPoorFoundations')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.hasPoorFoundations"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Bedrooms"
                        >
                            Total Bedrms
                        </span>
                        <input
                            id="totalBedrooms"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.totalBedrooms"
                            :class="errorClasses('summary.totalBedrooms')"
                            @input="updateNumber('totalBedrooms', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalBedrooms"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Bathrooms"
                        >
                            Total Bathrms
                        </span>
                        <input
                            id="totalBathrooms"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.totalBathrooms"
                            :class="errorClasses('summary.totalBathrooms')"
                            @input="updateNumber('totalBathrooms', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalBathrooms"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Toilets"
                        >
                            Total Toilets
                        </span>
                        <input
                            id="totalToilets"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.totalToilets"
                            :class="errorClasses('summary.totalToilets')"
                            @input="updateNumber('totalToilets', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalToilets"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Building Site Cover, m<sup>2</sup></span>
                        <input
                            id="buildingSiteCover"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.buildingSiteCover"
                            :class="errorClasses('summary.buildingSiteCover')"
                            @input="updateNumber('buildingSiteCover', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.buildingSiteCover"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Total Floor Area, m<sup>2</sup></span>
                        <input
                            id="totalFloorArea"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.totalFloorArea"
                            :class="errorClasses('summary.totalFloorArea')"
                            @input="updateNumber('totalFloorArea', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalFloorArea"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Main Living Area, m<sup>2</sup></span>
                        <input
                            id="mainLivingArea"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.mainLivingArea"
                            :class="errorClasses('summary.mainLivingArea')"
                            @input="updateNumber('mainLivingArea', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.mainLivingArea"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Total Living Area, m<sup>2</sup></span>
                        <input
                            id="totalLivingArea"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.summary.totalLivingArea"
                            :class="errorClasses('summary.totalLivingArea')"
                            @input="updateNumber('totalLivingArea', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalLivingArea"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Has a Laundry or Workshop outside of living area or garage"
                        >
                            Ldy/Wkshp
                        </span>
                        <yes-no-indeterminate-dropdown
                            id="hasLaundryOrWorkshop"
                            :value="draftProperty.summary.hasLaundryOrWorkshop"
                            :class="errorClasses('summary.hasLaundryOrWorkshop')"
                            @input="update"
                            :disabled="editingRestricted"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.hasLaundryOrWorkshop"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Car Access</span>
                        <span class="field">
                            <yes-no-indeterminate-dropdown
                                id="hasCarAccess"
                                :value="draftProperty.site.hasCarAccess"
                                :class="errorClasses('site.hasCarAccess')"
                                @input="update"
                                :disabled="editingRestricted"
                            />
                        </span>
                        <validation-message
                            :validation-set="validationSet"
                            field="site.hasCarAccess"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Driveway</span>
                        <span class="field">
                            <yes-no-indeterminate-dropdown
                                id="hasDriveway"
                                :value="draftProperty.site.hasDriveway"
                                :class="errorClasses('site.hasDriveway')"
                                @input="update"
                                :disabled="editingRestricted"
                            />
                        </span>
                        <validation-message
                            :validation-set="validationSet"
                            field="site.hasDriveway"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Carparks</span>
                        <input
                            id="carparks"
                            type="number"
                            min="0"
                            step="1"
                            :value="draftProperty.site.carparks"
                            :class="errorClasses('site.carparks')"
                            @input="updateNumber('carparks', $event)"
                            :readonly="editingRestricted"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="site.carparks"
                        />
                    </label>
                </div>
            </div>
        </div>
        <!-- DERIVED DVR FIELDS -->
        <derived-dvr-fields-section
            v-if="draftProperty"
            :property-detail="draftProperty"
            :validation-set="validationSet"
            :show-save="true"
            :can-save="true"
            :highlight="formIsStale"
            @click-save="saveChanges()"
        />
        <div
            v-if="isResidentialVacant"
            class="col-row"
        >
            <div class="col col-12">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small"
                    title="This will generate buildings based on the draft property data provided"
                    :disabled="draftSaving || editingRestricted"
                    @click="startGenerateBuildingsFromPropertyDetail"
                >
                    Generate Buildings
                </button>
            </div>
        </div>
        <div
            v-if="!isResidentialVacant && showRegenerateButton"
            class="col-row"
        >
            <div class="col col-12">
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored button--small"
                    title="This will generate buildings based on the draft property data provided"
                    :disabled="draftSaving || editingRestricted"
                    @click="startGenerateBuildingsFromPropertyDetail"
                >
                    Regenerate Buildings
                </button>
            </div>
        </div>
        <!-- BUILDINGS AND SPACES -->
        <buildings-and-spaces-section
            :buildings="buildings"
            :validation-set="validationSet"
            :qpid="qpid"
            :has-qivs-improvements="hasQivsImprovements"
            :has-useful-qivs-improvements="hasUsefulQivsImprovements"
            :disabled="editingRestricted"
            @update="update"
        />
        <!-- SITE IMPROVEMENTS -->
        <site-improvements-section
            :site-development="siteDevelopment"
            :other-improvements="otherImprovements"
            :validation-set="validationSet"
            :disabled="editingRestricted"
            @update="update"
        />

    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
// set from 'lodash/set';
import commonUtils from '../../../../utils/CommonUtils';
import {store} from '../../../../DataStore';

export default {
    components: {
        'nature-of-improvements': () => import(/* webpackChunkName: "SaleProcess" */ '../../../propertyDetails/NatureOfImprovements.vue'),
        'classification-dropdown': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/form/ClassificationDropdown.vue'),
        'yes-no-indeterminate-dropdown': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/form/YesNoIndeterminateDropdown.vue'),
        'current-roll-maintenance-activities': () => import(/* webpackChunkName: "SaleProcess" */ '../../ratingValuation/activities/RollMaintenanceActivities.vue'),
        'alert-modal': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/modal/AlertModal.vue'),
        'validation-message': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/form/ValidationMessage.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/form/ValidationHeaderMessage.vue'),
        'derived-dvr-fields-section': () => import(/* webpackChunkName: "SaleProcess" */ '../../../propertyDetails/DerivedDvrFields.vue'),
        'buildings-and-spaces-section': () => import(/* webpackChunkName: "SaleProcess" */ '../../../propertyDetails/BuildingsAndSpaces.vue'),
        'site-improvements-section': () => import(/* webpackChunkName: "SaleProcess" */ '../../../propertyDetails/SiteImprovements.vue'),
        'expander': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/Expander.vue'),
    },
    mixins: [commonUtils],
    props: {
        id: {
            type: String,
            default: "test",
        },
        saleId: {
            type: String
        },
        saleDetail: {
            type: Object
        },
        property: {
            type: Object
        },
        editingRestricted: {
            type: Object
        },
    },
    data() {
        return {
            taCode: '',
            alertModalIsOpen: false,
            alertMessage: {
                heading: '',
                message: '',
            },
            showGenerateBuildingsModal: false,
            generateBuildingsOptions: {},
            successModalIsOpen: false,
            successMessage: {
                heading: '',
                message: '',
                navigateTo: null,
            },
            navigateOnSetupComplete: false,
            setupCompletedByValuer: false,
            showWarnings: false,
            expandConsents: true,
            consentJobUpdateInfo: null
        };

    },
    computed: {
        ...mapState('propertyDraft', {
            draftProperty: 'propertyDetail',
            loading: 'loading',
            draftSaving: 'saving',
            draftException: 'exception',
            validationSet: 'validationSet',
            formIsStale: 'formIsStale',
        }),
        /* TODO Somewhat of a hack - map the property store to support the TA Zone dropdown. */
        ...mapGetters([
            'classificationsLoaded',
        ]),
        qpid() {
            return this.saleDetail.qupid;
        },

        hasConsentJobUpdateInfo(){
            if(this.consentJobUpdateInfo){
                return true;
            }
            else {
                return false;
            }
        },

        createdBy: function() {
            if(this.hasConsentJobUpdateInfo &&  this.$store.state.users){
                let users = this.$store.state.users;
                const match = users.filter(user => user.ntUsername === this.consentJobUpdateInfo.createdBy);
                if(match) {
                    return match[0].name;
                }
            }  
        },

        createdAt: function() {
                if(this.hasConsentJobUpdateInfo){
                    return this.formatDate(this.consentJobUpdateInfo.createdAt);
                }
            },
        isResidentialVacant() {
            // Check if LIVE property category is vacant.
            return this.property && this.property.category.code && ['RB', 'RM', 'RV'].includes(String(this.property.category.code).substring(0, 2).toUpperCase());
        },
        // valuationLoaded() {
        //     return (
        //         /* Not loading the valuation ... */
        //         !this.valuationLoading
        //         /* ... and has a valuation that is the valuation we want ... */
        //         && this.ratingValuation 
        //         && this.draftProperty.propertyId === this.ratingValuation.id
        //     );
        // },
        propertyLoaded() {
            return (
                /* If the valuation has loaded ... */
                this.property
                /* ... and has the property detail for the property we want. */
                && !this.draftLoading
                && this.draftProperty
                && this.saleDetail.qpid === this.draftProperty.qpid
            );
        },
        propertyId() {
            if (this.propertyLoaded) return this.draftProperty.propertyId;

            return null;
        },
        taLandZoneClassification() {
            return `TA_${this.taCode}_LandZone_DVR`;
        },
        errors() { return (this.validationSet && this.validationSet.errors) || []; },
        buildings() {
            if (!this.draftProperty.buildings || this.draftProperty.buildings.length === 0) {
                return [{}];
            }
            return this.draftProperty.buildings;
        },
        otherImprovements() {
            if (
                !this.draftProperty.otherImprovements
                || this.draftProperty.otherImprovements.length === 0
            ) {
                return [{}];
            }
            return this.draftProperty.otherImprovements;
        },
        siteDevelopment() {
            if (
                this.draftProperty
                && this.draftProperty.site
                && this.draftProperty.site.siteDevelopment
            ) {
                return this.draftProperty.site.siteDevelopment;
            }
            return { quality: null, description: null };
        },
        hasQivsImprovements() {
            return this.draftProperty.qivsImprovementsStatus !== 'NO_IMPROVEMENTS';
        },
        hasUsefulQivsImprovements() {
            return this.draftProperty.qivsImprovementsStatus === 'USEFUL_IMPROVEMENTS';
        },
        disabledSteps() {
            if (this.isSetupComplete === false) {
                return [1, 2, 3];
            }
            return [];
        },
        showRegenerateButton() {
            if(this.currentProperty && this.currentProperty.buildings.length === 0){
                return true;
            }
            else {
                return false;
            }
        },
    },
    methods: {
        formatDate(date){
            const splitDate = date.split("-");
            return splitDate[2] + '/' + splitDate[1] + '/' + splitDate[0];   
        },
        async loadTAZoneClassification(taCode) {
            await this.$store.dispatch('fetchTAZoneClassification', taCode);
            this.taCode = taCode;
        },
        updateNumber(id, event) {
            let value = null;
            if(event.srcElement.value && event.srcElement.value != '')
                value = parseFloat(event.srcElement.value);
            this.update({ id, value });
        },
        updateText(id, event) {
            const { value } = event.srcElement;
            this.update({ id, value });
        },
        update(data) {
            this.$store.commit('propertyDraft/setSinglePropertyDetail', data);
        },
        /* TODO Global exception handling (shouldnt need to do on all components etc) */
        handleException(err) {
            this.showAlertModal(
                'Unexpected Error',
                `An unexpected error occurred attempting to communicate with the server: ${err}`,
            );
        },
        scrollToTop() {
            this.$nextTick(() => {
                window.scrollTo({ top: this.$refs.validationHeader.$el.offsetTop, left: 0, behavior: 'smooth' });
            });
        },
        async saveChanges(silent = false) {
            let success = false;
            try {
                await this.$store.dispatch('propertyDraft/savePropertyDraft');
                if (this.validationSet.success) {
                    success = true;
                    this.navigateOnSuccess = false;
                    if (!silent) {
                        this.showSuccess();
                    }
                } else {
                    this.scrollToTop();
                }
            } catch (err) {
                this.handleException(err);
            }
            return success;
        },
        showAlertModal(heading, message) {
            this.alertMessage = {
                heading,
                message,
            };
            this.alertModalIsOpen = true;
        },
        closeAlertModal() {
            this.alertModalIsOpen = false;
        },
        showSuccess(message = {}) {
            this.successMessage.heading = message.heading || 'Saved.';
            this.successMessage.message = message.message || 'Your changes have been saved.';
            this.successMessage.navigateTo = message.navigateTo || null;
            this.successModalIsOpen = true;
        },
        closeSuccessModal() {
            this.successModalIsOpen = false;
            if (!this.successMessage.navigateTo) return;
            this.$router.push(this.successMessage.navigateTo);
        },
        hideWarningModal() {
            this.showWarnings = false;
        },
        showWarningModal() {
            this.showWarnings = true;
        },
        async generatePropertyDetailNewDwellingInformation() {
            try {
                await this.$store.dispatch('propertyDraft/generatePropertyDetailNewDwellingInformation');
            } catch (err) {
                this.handleException(err);
            }
        },
        startGenerateBuildingsFromPropertyDetail() {
            this.showGenerateBuildingsModal = true;
        },
        async generateBuildingsFromPropertyDetail(umrGarages, fsGarages) {
            try {
                await this.$store.dispatch('propertyDraft/generateBuildingsFromPropertyDetail', { umrGarages, fsGarages });
            } catch (err) {
                this.handleException(err);
            }
        },
        // Handler for the update draft button clicked. This will save property draft and do a setup complete without navigating around.
        handleUpdateDraft() {
            const navigateOnSuccess = false;
            this.completeSetup(false, navigateOnSuccess);
        },
        toNumber(text) {
            const n = Number.parseInt(text, 10);
            return Number.isNaN(n) ? null : n;
        },
    },
}
</script>
<template>
    <div class="property-draft-section col-container">
        <div class="col-row">
            <div class="col col-12">
                <h3>Reason for Change</h3>
            </div>
        </div>
        <div class="col-row">
            <div class="col col-3">
                <label>
                    <span class="label">Output Code</span>
                    <select v-model="mySale.outputCode">
                        <option v-for="option in opts.outputCode" :key="option.code" :value="option.code">
                            {{ option.label }}
                        </option>
                    </select>
                </label>
            </div>
            <div class="col col-3">
                <label>
                    <span class="label">Source</span>
                    <select v-model="mySale.source">
                        <option v-for="option in opts.sourceCode" :key="option.code" :value="option.code">
                            {{ option.label }}
                        </option>
                    </select>
                </label>
            </div>
            <div class="col col-6">
                <label>
                    <span class="label">Reason for Change</span>
                    <input
                        type="text"
                    />
                </label>
            </div>
        </div>
        <div class="col-row">
            <div class="col col-2">
                <label>
                    <span class="label">RFS Date</span>
                    <input
                        type="text"
                        readonly
                    />
                </label>
            </div>
            <div class="col col-2">
                <label>
                    <span class="label">RFS Number</span>
                    <input
                        type="text"
                        readonly
                    />
                </label>
            </div>
            <div class="col col-1" style="text-align:center;">
                <label>
                    <span class="label" style="white-space: nowrap;">Close Action Record</span>
                    <input
                        type="checkbox"
                    >
                </label>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        id: {
            type: String,
            default: "test",
        },
        sale: {
            type: Object
        },
        property: {
            type: Object
        },
    },
    data() {
        return {
            mySale: this.sale,
            myProperty: this.property,
            opts: {
                
            }
        }
    },
    watch:{
        mySale:{
            handler: function() {
                this.sale = this.mySale;
            },
            deep:true,
        },
        myProperty:{
            handler: function() {
                this.property = this.myProperty;
            },
            deep:true,
        }
    }
}
</script>
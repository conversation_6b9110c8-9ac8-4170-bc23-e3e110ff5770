<script setup>
import PropertyTypeahead from 'Common/form/PropertyTypeahead.vue';
import { useAddressSearch } from '@/composables/useAddressSearch';
import Vue, { computed, ref } from 'vue';
import _ from 'underscore';
import MaterialIcon from 'Common/MaterialIcon.vue';
import useSales from '@/composables/useSales';
import MButton from '@/components/common/MButton.vue';
import FormItemErrors from 'Common/form/FormItemErrors.vue';

const {
    crossReferencedProperties,
    addCrossReferencedProperty,
    removeCrossReferencedProperty,
    recalculateValues,
    getErrorsForLabel,
} = useSales();

const { results, query, searching, errored } = useAddressSearch({
  filter(property) {
    return ['0', '2', '5'].includes(property.apportionmentCode);
  }
});
const props = defineProps({
    readonly: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['select', 'remove']);
const selectedQpids = computed(() => crossReferencedProperties?.value?.filter(p => !p.deleted)?.map(p => parseInt(p.qpid)));

function selectProperty(property) {
    if (!property?.qpid) {
        return;
    }

    const exists = _.findIndex(crossReferencedProperties.value, p => parseInt(p.qpid) === parseInt(property.qpid));
    if (exists !== -1 && crossReferencedProperties.value[exists].deleted) {
        const existingProperty = crossReferencedProperties.value[exists];
        existingProperty.deleted = false;
        Vue.set(crossReferencedProperties.value, exists, existingProperty);
        recalculateValues();
        return;
    }
    if (exists !== -1) {
        return removeCrossReferencedProperty(crossReferencedProperties.value[exists]);
    }
    addCrossReferencedProperty(property);
}

</script>
<template>
    <div class="qv-w-full qv-flex-column" data-cy="sale-cross-referenced-properties">
        <div v-if="!props.readonly" class="qv-flex-row">
            <property-typeahead v-model="query" :highlighted="selectedQpids" :results="results" :errors="getErrorsForLabel('Property Search')" @select="selectProperty" >
                <transition name="fade" duration="150">
                    <material-icon v-if="searching && !errored" icon="autorenew" class="qv-color-mediumblue qv-spinner"/>
                    <material-icon v-else-if="errored" title="Search Failed" icon="warning" class="qv-color-error"/>
                </transition>
            </property-typeahead>
        </div>
        <form-item-errors :errors="getErrorsForLabel('Property Search')" />
        <table class="qv-table qv-table-striped qv-multi-sale-table qv-color-dark">
            <thead>
            <tr>
                <th>QPID</th>
                <th>Val Ref</th>
                <th>Address</th>
                <th>Area</th>
                <th>CV</th>
                <th>LV</th>
                <th>VI</th>
                <th style="width: 10rem"></th>
            </tr>
            </thead>
            <tbody>
            <template v-for="(property, index) in crossReferencedProperties">
                <tr class="qv-table-row qv-text-base" :key="property.qpid" v-if="!property.deleted">
                    <td>{{ property.qpid }}</td>
                    <td>{{ property.valRef }}</td>
                    <td>{{ property.address }}</td>
                    <td>{{ property.area }}</td>
                    <td>{{ property.capitalValue }}</td>
                    <td>{{ property.landValue }}</td>
                    <td>{{ property.improvementsValue }}</td>
                    <td><button v-if="!props.readonly" @click="() => removeCrossReferencedProperty(property)">Remove</button></td>

                </tr>
            </template>
            </tbody>
        </table>
      <div class="qv-flex-row qv-align-center">
        <m-button v-if="!props.readonly" class="mdl-button--colored" @click="recalculateValues(true)">Calculate Values</m-button>
      </div>
    </div>
</template>

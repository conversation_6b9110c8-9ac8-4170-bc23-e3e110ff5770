<template>
    <div v-if="!propertyLoaded">
        <div class="loadingSpinner loadingSpinnerSearchResults" />
    </div>
    <div v-else data-cy="dvr-property-details">
        <!-- <div
            v-if="propertyLoaded && dvr.isStale"
            class="righty message message-warning"
        >
            NOTE: The detailed information for this property has changed, please review the
            <router-link :to="{name: 'property', params: {qpid: qpid}}" target="_blank">current property</router-link>
            and update the draft property details.
        </div> -->

        <div class="qv-flex-row qv-justify-space-between qv-align-center qv-pt-5">
            <div class="qv-flex-row qv-w-2/3">
                <input-label label="RTV" label-style="qv-sale-input-label-sm">
                    <input-number
                        :value="rtv"
                        :readonly="true"
                        :display-empty="true"
                        format="$0,0"
                    />
                    <p v-if="revisionValues.atLeastOneRevisionValue" class="qv-color-error sp-revision-value">Proposed Revision</p>
                </input-label>
                <input-label label="Capital Value" label-style="qv-sale-input-label-sm">
                    <input-number
                        v-model="dvr.currentValuation.capitalValue"
                        :readonly="isPropertyValueReadOnly"
                        :errors="getErrorsForLabel('Capital Value')"
                        format="$0,0"
                    />
                    <p v-if="revisionValues.atLeastOneRevisionValue" class="qv-color-error sp-revision-value">{{ revisionValues.totalRevisionCapitalValueFormatted }}</p>
                </input-label>
                <input-label label="Land Value" label-style="qv-sale-input-label-sm">
                    <input-number
                        v-model="dvr.currentValuation.landValue"
                        :readonly="isPropertyValueReadOnly"
                        format="$0,0"
                    />
                    <p v-if="revisionValues.atLeastOneRevisionValue" class="qv-color-error sp-revision-value">{{ revisionValues.totalRevisionLandValueFormatted }}</p>
                </input-label>
                <input-label label="Improvements" label-style="qv-sale-input-label-sm">
                    <input-number
                        :value="improvementsValue"
                        :errors="getErrorsForLabel('Improvements')"
                        format="$0,0"
                        readonly
                    />
                    <p v-if="revisionValues.atLeastOneRevisionValue" class="qv-color-error sp-revision-value">{{ revisionValues.totalRevisionImprovementsValueFormatted }}</p>
                </input-label>
            </div>
            <m-button v-if="!readonly" class="mdl-button--colored" @click="refreshPropertyData()">Refresh Property Data</m-button>
        </div>
        <p v-if="rtvMissing" class="qv-color-error sp-revision-value">{{ rtvWarningMessage }}</p>
        <p v-if="revisionValues.shouldDisplayWarning" class="qv-color-error sp-revision-value">{{ revisionValues.warningMessage }}</p>
        <div class="col-container">
            <validation-header-message
                ref="validationHeader"
                :validation-set="validationSet"
                :show-errors="true"
                action="save the draft property"
            />
            <alert-modal
                v-if="showWarnings"
                warning
            >
                <h3 id="errorHeader">
                    Do you want to proceed?
                </h3>
                <p>The following validation checks are failing:</p>
                <div class="validation-header-message--warnings">
                    <validation-header-message
                        ref="warnings"
                        :validation-set="{errors:validationSet.warnings}"
                        :show-errors="true"
                        message=""
                    />
                </div>
                <template #buttons>
                    <div class="alertButtons">
                        <button
                            id="errorCancel"
                            class="mdl-button mdl-button--mini lefty"
                            @click="hideWarningModal()"
                        >
                            No, Return to draft
                        </button>
                        <button
                            id="continue"
                            class="mdl-button mdl-button--mini"
                            @click="completeSetupWithWarnings()"
                        >
                            Yes, Complete Setup
                        </button>
                    </div>
                </template>
            </alert-modal>
        </div>

        <div class="col-container" style="padding:0;">
            <div class="col-row">
                <div class="col col-10">
                    <h3>
                        General Property Information
                    </h3>
                </div>
            </div>
        </div>

        <div class="property-draft-section">
            <div class="col-row">
                <div class="col col-5">
                    <label>
                        <span class="label">Category</span>
                        <classification-dropdown
                            id="category"
                            category="Category_DVR"
                            :value="dvr.category"
                            label="code"
                            :single-label-function="(opt) => `${opt.code} — ${opt.description}`"
                            :class="errorClasses('category')"
                            :disabled="readonly"
                            :errors="getErrorsForLabel('Category')"
                            @input="update"
                        />
                        <form-item-errors :errors="getErrorsForLabel('Category')" />
                    </label>
                </div>
                <div class="col col-5">
                    <label>
                        <span
                            class="label"
                            title="To add multiple of an improvement type double click on a selection and enter the number."
                        >
                            Nature of Improvements
                        </span>
                        <nature-of-improvements
                            id="natureOfImprovements"
                            :value="dvr.natureOfImprovements"
                            :class="errorClasses('natureOfImprovements')"
                            :disabled="readonly"
                            :errors="getErrorsForLabel('Nature of Improvements')"
                            @input="update"
                        />
                        <form-item-errors :errors="getErrorsForLabel('Nature of Improvements')" />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Property Name</span>
                        <input :readonly="readonly"
                            type="text"
                            v-model="dvr.summary.propertyName"
                            :class="errorClasses('summary.propertyName')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.propertyName"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-4">
                    <label>
                        <span class="label">Land Use</span>
                        <form-select
                            v-model.number="dvr.landUseData.landUse"
                            :readonly="readonly"
                            :options="classifications.LandUseType"
                            :option-name="parseOptionName"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="landUse.landUse"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">TA Land Zone</span>
                        <form-select
                            v-model="dvr.landUseData.landZone"
                            :readonly="readonly"
                            :options="taLandUse"
                            :option-name="(opt) => opt.code"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="landUse.landZone"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Effective Land Area, ha</span>
                        <input-number :readonly="readonly"
                            id="effectiveLandArea"
                            type="number"
                            min="0"
                            step="0.0001"
                            :format="'0,0.0000'"
                            :display-empty="true"
                            :errors="getErrorsForLabel('Effective Land Area')"
                            v-model.number="dvr.site.effectiveLandArea"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Land Area, ha</span>
                        <input-number :readonly="readonly"
                            id="landArea"
                            type="number"
                            min="0"
                            step="0.0001"
                            :format="'0,0.0000'"
                            :display-empty="true"
                            :errors="getErrorsForLabel('Land Area')"
                            v-model.number="dvr.site.landArea"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Māori Land</span>
                        <input-text
                            readonly
                            :value="dvr.landUseData.isMaoriLand ? 'Yes' : 'No'"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Plan ID</span>
                        <input :readonly="readonly"
                            type="text"
                            v-model="dvr.planNumber"
                            :class="errorClasses('planNumber')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="planNumber"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Production</span>
                        <input-number
                            v-model.number="dvr.landUseData.production"
                            :errors="getErrorsForLabel('Production')"
                            format="0,0"
                            :readonly="readonly"
                            :display-empty="true"
                        />
                    </label>
                </div>
            </div>
        </div>
        <h3  v-if="dvrFullViewMode">
            Location Details
        </h3>
        <div class="property-draft-section" v-if="dvrFullViewMode">
            <div class="col-row">
                <div class="col col-1">
                    <label>
                        <span class="label">Lot position</span>
                        <form-select
                            v-model.number="dvr.site.lotPosition"
                            :readonly="readonly"
                            :options="classifications.LotPositionType"
                            :option-name="(opt) => opt.description"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.lotPosition"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Contour</span>
                        <form-select
                            v-model.number="dvr.site.contour"
                            :readonly="readonly"
                            :options="classifications.ContourType"
                            :option-name="parseOptionName"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.contour"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">View</span>
                        <form-select
                            v-model.number="dvr.site.view"
                            :readonly="readonly"
                            :options="classifications.ViewType"
                            :option-name="parseOptionName"
                            :errors="getErrorsForLabel('View')"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">View Scope</span>
                        <form-select
                            v-model.number="dvr.site.viewScope"
                            :readonly="readonly"
                            :options="classifications.ViewScopeType"
                            :option-name="option => `${option.code} - ${option.description}`"
                            :errors="getErrorsForLabel('View Scope')"
                        />
                    </label>
                </div>
                <div class="col col-3">
                    <label>
                        <span class="label">Class of Surrounding Improvements (CSI)</span>
                        <form-select
                            v-model.number="dvr.site.classOfSurroundingImprovements"
                            :readonly="readonly"
                            :options="classifications.ClassSurroundingImprovementType"
                            :option-name="parseOptionName"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.classOfSurroundingImprovements"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Outlier</span>
                        <form-select v-model="dvr.isOutlier"
                                     :readonly="readonly"
                                     :options="yesNoOptions"
                                     :has-boolean-selection="true"/>
                        <validation-message
                            :validation-set="validationSet"
                            field="isOutlier"
                        />
                    </label>
                </div>
            </div>
        </div>
        <h3>
            Property Summary
        </h3>
        <div class="property-draft-section col-container" v-if="!dvrFullViewMode">
            <div class="col-row">
                <div class="col col-2">
                    <div class="col-container">
                        <div class="col-row">
                            <div class="col col-6">
                                <label>
                                    <span class="label">Units of Use</span>
                                    <input-number
                                        :readonly="readonly"
                                        :errors="getErrorsForLabel('Units of Use')"
                                        id="units"
                                        min="0"
                                        step="1"
                                        v-model.number="dvr.summary.units"
                                        format="0,0"
                                        :display-empty="true"
                                    />
                                </label>
                            </div>
                            <div class="col col-6">
                                <label>
                                    <span
                                        class="label"
                                        title="This is the building age from the land use data"
                                    >
                                        Age
                                    </span>
                                    <form-select
                                        v-model.number="dvr.summary.age"
                                        :readonly="readonly"
                                        :options="classifications.BuildingAgeType"
                                        :errors="getErrorsForLabel('Age')"
                                    />
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col col-6">
                    <div class="col-container">
                        <div class="col-row">
                            <div class="col col-3">
                                <label>
                                    <span class="label" style="white-space: nowrap;">Building Site Cover, m<sup>2</sup></span>
                                    <input :readonly="readonly"
                                        id="buildingSiteCover"
                                        type="number"
                                        min="0"
                                        step="1"
                                        v-model.number="dvr.summary.buildingSiteCover"
                                    >
                                    <form-item-errors :errors="getErrorsForLabel('Building Site Cover')" />
                                </label>
                            </div>
                            <div class="col col-3">
                                <label>
                                    <span class="label">Total Floor Area, m<sup>2</sup></span>
                                    <input :readonly="readonly"
                                        id="totalFloorArea"
                                        type="number"
                                        min="0"
                                        step="1"
                                        v-model.number="dvr.summary.totalFloorArea"
                                    >
                                    <form-item-errors :errors="getErrorsForLabel('Total Floor Area')" />
                                </label>
                            </div>
                            <div class="col col-4">
                                <label>
                                    <span class="label">Wall Construction</span>
                                    <form-select
                                        v-model.number="dvr.summary.wallConstruction"
                                        :readonly="readonly"
                                        :options="classifications.BuildingConstructionType"
                                    />
                                    <validation-message
                                        :validation-set="validationSet"
                                        field="summary.wallConstruction"
                                    />
                                </label>
                            </div>
                            <div class="col col-2">
                                <label>
                                    <span class="label">Wall Condition</span>
                                    <form-select
                                        v-model.number="dvr.summary.wallCondition"
                                        :readonly="readonly"
                                        :options="classifications.BuildingConditionType"
                                    />
                                    <validation-message
                                        :validation-set="validationSet"
                                        field="summary.wallCondition"
                                    />
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col col-4">
                    <div class="col-container">
                        <div class="col-row">
                            <div class="col col-6">
                                <label>
                                    <span class="label">Roof Construction</span>
                                    <form-select
                                        v-model.number="dvr.summary.roofConstruction"
                                        :readonly="readonly"
                                        :options="classifications.BuildingConstructionType"
                                    />
                                    <validation-message
                                        :validation-set="validationSet"
                                        field="summary.roofConstruction"
                                    />
                                </label>
                            </div>
                            <div class="col col-4">
                                <label>
                                    <span class="label">Roof Condition</span>
                                    <form-select
                                        v-model.number="dvr.summary.roofCondition"
                                        :readonly="readonly"
                                        :options="classifications.BuildingConditionType"
                                    />
                                    <validation-message
                                        :validation-set="validationSet"
                                        field="summary.roofCondition"
                                    />
                                </label>
                            </div>
                            <div class="col col-2">
                                <label>
                                    <span class="label">Carparks</span>
                                    <input :readonly="readonly"
                                        id="carparks"
                                        type="number"
                                        min="0"
                                        step="1"
                                        v-model.number="dvr.site.carparks"
                                        :class="errorClasses('site.carparks')"
                                    >
                                    <validation-message
                                        :validation-set="validationSet"
                                        field="site.carparks"
                                    />
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="property-draft-section" v-if="dvrFullViewMode">
            <div class="col-row">
                <div class="col col-3">
                    <label>
                        <span class="label">House Type</span>
                        <form-select
                            v-model.number="dvr.summary.houseType"
                            :readonly="readonly"
                            :options="classifications.HouseType"
                            :option-name="parseOptionName"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.houseType"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Units of Use</span>
                        <input-number
                            :readonly="readonly"
                            :errors="getErrorsForLabel('Units of Use')"
                            id="units"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.units"
                            format="0,0"
                            :display-empty="true"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span
                            class="label"
                            title="This is the building age from the land use data"
                        >Age</span>
                        <form-select
                            v-model.number="dvr.summary.age"
                            :readonly="readonly"
                            :options="classifications.BuildingAgeType"
                        />
                        <form-item-errors :errors="getErrorsForLabel('Age')" />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Effective Year Built</span>
                        <input-number
                            id="effectiveYearBuilt"
                            type="number"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.effectiveYearBuilt"
                            :errors="getErrorsForLabel('Effective Year Built')"
                            :readonly="readonly"
                            format="0"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Has Poor Foundations"
                        >
                            Poor Fdn.
                        </span>
                        <form-select
                            v-model="dvr.summary.hasPoorFoundations"
                            :readonly="readonly"
                            :options="yesNoOptions"
                            :has-boolean-selection="true"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.hasPoorFoundations"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Bedrooms"
                        >
                            Total Bedrms
                        </span>
                        <input :readonly="readonly"
                            id="totalBedrooms"
                            type="number"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.totalBedrooms"
                            :class="errorClasses('summary.totalBedrooms')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalBedrooms"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Bathrooms"
                        >
                            Total Bathrms
                        </span>
                        <input :readonly="readonly"
                            id="totalBathrooms"
                            type="number"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.totalBathrooms"
                            :class="errorClasses('summary.totalBathrooms')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalBathrooms"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Total Toilets"
                        >
                            Total Toilets
                        </span>
                        <input :readonly="readonly"
                            id="totalToilets"
                            type="number"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.totalToilets"
                            :class="errorClasses('summary.totalToilets')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalToilets"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Building Site Cover, m<sup>2</sup></span>
                        <input-number
                            :readonly="readonly"
                            id="buildingSiteCover"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.buildingSiteCover"
                            :errors="getErrorsForLabel('Building Site Cover')"
                            format="0,0"
                            :display-empty="true"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Total Floor Area, m<sup>2</sup></span>
                        <input-number
                            :readonly="readonly"
                            id="totalFloorArea"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.totalFloorArea"
                            :errors="getErrorsForLabel('Total Floor Area')"
                            format="0,0"
                            :display-empty="true"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Main Living Area, m<sup>2</sup></span>
                        <input-number
                            :readonly="readonly"
                            id="mainLivingArea"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.mainLivingArea"
                            :errors="getErrorsForLabel('Main Living Area')"
                            format="0,0"
                            :display-empty="true"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Total Living Area, m<sup>2</sup></span>
                        <input :readonly="readonly"
                            id="totalLivingArea"
                            type="number"
                            min="0"
                            step="1"
                            v-model.number="dvr.summary.totalLivingArea"
                            :class="errorClasses('summary.totalLivingArea')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.totalLivingArea"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span
                            class="label"
                            title="Has a Laundry or Workshop outside of living area or garage"
                        >
                            Ldy/Wkshp
                        </span>
                        <form-select
                            v-model="dvr.summary.hasLaundry"
                            :readonly="readonly"
                            :options="yesNoOptions"
                            :has-boolean-selection="true"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.hasLaundryOrWorkshop"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Car Access</span>
                        <form-select
                            v-model="dvr.site.hasCarAccess"
                            :readonly="readonly"
                            :options="yesNoOptions"
                            :errors="getErrorsForLabel('Car Access')"
                            :has-boolean-selection="true"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Driveway</span>
                        <form-select
                            v-model="dvr.site.hasDriveway"
                            :readonly="readonly"
                            :options="yesNoOptions"
                            :has-boolean-selection="true"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="site.hasDriveway"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Carparks</span>
                        <input :readonly="readonly"
                            id="carparks"
                            type="number"
                            min="0"
                            step="1"
                            v-model.number="dvr.site.carparks"
                            :class="errorClasses('site.carparks')"
                        >
                        <validation-message
                            :validation-set="validationSet"
                            field="site.carparks"
                        />
                    </label>
                </div>
            </div>
            <div class="col-row">
                <div class="col col-2">
                    <label>
                        <span class="label">Wall Construction</span>
                        <form-select
                            v-model.number="dvr.summary.wallConstruction"
                            :readonly="readonly"
                            :options="classifications.BuildingConstructionType"
                            :errors="getErrorsForLabel('Wall Construction')"
                            data-cy="input-dvr-wall-construction"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Wall Condition</span>
                        <form-select
                            v-model.number="dvr.summary.wallCondition"
                            :readonly="readonly"
                            :options="classifications.BuildingConditionType"
                            :errors="getErrorsForLabel('Wall Condition')"
                            data-cy="input-dvr-wall-condition"
                        />
                    </label>
                </div>
                <div class="col col-2">
                    <label>
                        <span class="label">Roof Construction</span>
                        <form-select
                            v-model.number="dvr.summary.roofConstruction"
                            :readonly="readonly"
                            :options="classifications.BuildingConstructionType"
                            :errors="getErrorsForLabel('Roof Construction')"
                            data-cy="input-dvr-roof-construction"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Roof Condition</span>
                        <form-select
                            v-model.number="dvr.summary.roofCondition"
                            :readonly="readonly"
                            :options="classifications.BuildingConditionType"
                            :errors="getErrorsForLabel('Roof Condition')"
                            data-cy="input-dvr-roof-condition"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Modernisation</span>
                        <form-select
                            v-model="dvr.summary.modernisation"
                            :readonly="readonly"
                            :options="yesNoOptions"
                            :has-boolean-selection="true"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Landscaping</span>
                        <form-select
                            v-model.number="dvr.site.landscapingQuality"
                            :readonly="readonly"
                            :options="classifications.LandscapingQualityType"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Deck</span>
                        <form-select
                            v-model="dvr.summary.hasDeck"
                            :readonly="readonly"
                            :options="yesNoOptions"
                            :has-boolean-selection="true"
                        />
                        <validation-message
                            :validation-set="validationSet"
                            field="summary.hasDeck"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">Large Ols</span>
                        <form-select
                            v-model="dvr.summary.hasLargeOtherImprovements"
                            :readonly="readonly"
                            :options="yesNoOptions"
                            :has-boolean-selection="true"
                        />
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">UMR Garaging</span>
                        <input :readonly="readonly"
                            type="number"
                            v-model.number="dvr.summary.underMainRoofGarages"
                        >
                    </label>
                </div>
                <div class="col col-1">
                    <label>
                        <span class="label">FS Garaging</span>
                        <input :readonly="readonly"
                            type="number"
                            v-model.number="dvr.summary.freestandingGarages"
                        >
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
// set from 'lodash/set';
import commonUtils from '../../../../utils/CommonUtils';
import InputNumber from 'Common/form/InputNumber.vue';
import InputLabel from 'Common/form/InputLabel.vue';
import MButton from 'Common/MButton.vue';
import FormItemErrors from 'Common/form/FormItemErrors.vue';
import InputText from 'Common/form/InputText.vue';
import FormSelect from 'Common/form/FormSelect.vue';

export default {
    components: {
        MButton, InputLabel, InputNumber, FormItemErrors, InputText, FormSelect,
        'nature-of-improvements': () => import(/* webpackChunkName: "SaleProcess" */ '../../../propertyDetails/NatureOfImprovements.vue'),
        'classification-dropdown': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/form/ClassificationDropdown.vue'),
        'alert-modal': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/modal/AlertModal.vue'),
        'validation-message': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/form/ValidationMessage.vue'),
        'validation-header-message': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/form/ValidationHeaderMessage.vue'),
    },
    mixins: [commonUtils],
    props: {
        property: {
            type: Object
        },
        rtv: {
            type: Number,
            default: null,
        },
        dvr: {
            type: Object
        },
        classifications: {
            type: Object
        },
        taLandUse: {
            type: Array
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        validationResult: {
            type: Object,
            default: () => ({
                status: null,
                hasErrors: null,
                hasWarnings: null,
                validations: null,
            }),
        },
        revisionValues: {
            type: Object,
            default: () => ({}),
        },
        rtvMissing: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            taCode: '',
            dvrFullViewMode: true,
            alertModalIsOpen: false,
            alertMessage: {
                heading: '',
                message: '',
            },
            showGenerateBuildingsModal: false,
            generateBuildingsOptions: {},
            successModalIsOpen: false,
            successMessage: {
                heading: '',
                message: '',
                navigateTo: null,
            },
            navigateOnSetupComplete: false,
            showWarnings: false,
            validationSet: null,
            yesNoOptions: [
                { id: true, description: 'Yes' },
                { id: false, description: 'No' },
            ],
            rtvWarningMessage: 'Not all linked properties have RTV values',
        }
    },
    computed: {
        /* TODO Somewhat of a hack - map the property store to support the TA Zone dropdown. */
        ...mapGetters([
            'classificationsLoaded',
        ]),
        qpid() {
            return this.sale.qupid;
        },
        propertyLoaded() {
            return this.dvr !== null;
        },
        propertyId() {
            if (this.propertyLoaded) return this.dvr.propertyId;

            return null;
        },
        improvementsValue() {
            return this.dvr.currentValuation.capitalValue - this.dvr.currentValuation.landValue;
        },
        errors() { return (this.validationSet && this.validationSet.errors) || []; },
        isPropertyValueReadOnly() { return this.readonly || (this.dvr.saleCurrentRevisionDate && this.dvr.saleCurrentRevisionDate < this.dvr.raCurrentRevisionDate) },
    },
    methods: {
        getErrorsForLabel(label) {
            return this.validationResult?.validations?.[label]?.errors ?? [];
        },
        toggleViewMode(){
            this.dvrFullViewMode = !this.dvrFullViewMode;
        },
        formatDate(date){
            const splitDate = date.split("-");
            return splitDate[2] + '/' + splitDate[1] + '/' + splitDate[0];
        },
        async loadTAZoneClassification() {
            await this.$store.dispatch('fetchTAZoneClassification', this.property.territorialAuthority.code);
        },
        updateNumber(id, event) {
            let value = null;
            if(event.srcElement.value && event.srcElement.value != '')
                value = parseFloat(event.srcElement.value);
            this.update({ id, value });
        },
        updateText(id, event) {
            const { value } = event.srcElement;
            this.update({ id, value });
        },
        update(data) {
            this.dvr[data.id] = data.value; // TODO have this bound to the sale object (and saved somewhere against the sale).
        },
        /* TODO Global exception handling (shouldnt need to do on all components etc) */
        handleException(err) {
            this.showAlertModal(
                'Unexpected Error',
                `An unexpected error occurred attempting to communicate with the server: ${err}`,
            );
        },
        scrollToTop() {
            this.$nextTick(() => {
                window.scrollTo({ top: this.$refs.validationHeader.$el.offsetTop, left: 0, behavior: 'smooth' });
            });
        },
        toNumber(text) {
            const n = Number.parseInt(text, 10);
            return Number.isNaN(n) ? null : n;
        },
        refreshPropertyData() {
            this.$emit('refreshPropertyData');
        },
        parseOptionName(option) {
            return `${option.code} - ${option.description}`;
        },
    },
    created() {

    }

}
</script>

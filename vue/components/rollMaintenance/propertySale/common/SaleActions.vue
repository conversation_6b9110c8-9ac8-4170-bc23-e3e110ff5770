<template>
    <div>
        <ul class="action-buttons">
            <li>
                <button
                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                >
                    Notes for Valuer
                </button>
            </li>
            <li>
                <label>
                    <span class="label notes">
                        Notes
                    </span>
                    <p style="white-space: pre-wrap;">Sample note placeholder</p>
                </label>
            </li>
        </ul>
    </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
    components: {
        'alert-modal': () => import(/* webpackChunkName: "SaleProcess" */ '../../../common/modal/AlertModal.vue'),
    },
    data() {
        return {
            note: '',
            action: null,
            actionTitle: null,
            requestPlans: null,
            showActionModal: false,
            confirmation: {
                visible: false,
                title: null,
                message: null,
                note: null,
                confirm: null,
                cancel: null,
                del: false,
            },
        };
    },
    props: {
    },
    computed: {
    },
    methods: {
        
    },
};
</script>

<style lang="scss" scoped src="../../rollMaintenance.scss"></style>

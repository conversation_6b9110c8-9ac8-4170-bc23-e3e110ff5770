<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import useSales from '@/composables/useSales';
import InputInlineLabel from 'Common/form/InputInlineLabel.vue';
import InputLabel from 'Common/form/InputLabel.vue';
import InputNumber from 'Common/form/InputNumber.vue';
import InputDate from 'Common/form/InputDate.vue';
import MaterialIcon from 'Common/MaterialIcon.vue';
import FormSelect from 'Common/form/FormSelect.vue';
import FormItemErrors from 'Common/form/FormItemErrors.vue';
import InputText from 'Common/form/InputText.vue';
import FormTextArea from '../../../common/form/FormTextArea.vue';

const {
    isNewSale,
    getErrorsForLabel,
} = useSales();


const props = defineProps({
    sale: {
        required: true,
    },
    classifications: {
        required: true,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    focus: {
        type: Boolean,
        default: true,
    },
    isNewSale: {
        type: Boolean,
    },
    salePdfLoading: {
        type: Boolean,
        default: false,
    },
});

const pdfNumberSourceOptions = [
    // List items ordered alphabetically (and by frequency of use)
    { id: 1, description: 'LINZ' },
    { id: 3, description: 'REINZ' },
    { id: 2, description: 'SalesDirect' },
];

const emit = defineEmits(['updateSaletype', 'salePdfNumberChanged']);
const firstElement = ref(null);
const netPrice = computed(() => calculateNetSalePrice());

function calculateNetSalePrice() {
    return props.sale.price.gross - (props.sale.price.chattels + props.sale.price.other + props.sale.price.gst);
}

const isConfirmedSale = computed(() => props.sale.saleStatusId === 1);

watch(() => netPrice.value, () => {
    props.sale.price.net = netPrice.value ?? 0;
});

watch(() => props.sale.saleStatusId, () => {
    if(isConfirmedSale.value) {
        props.sale.assuranceLevelId = null;
    }
});

onMounted(() => {
    props.sale.price.net = calculateNetSalePrice();
    firstElement?.value?.focus();
});
</script>

<template>
    <div class="qv-flex-row qv-w-full qv-py-3" data-cy="sale-details">
        <div class="qv-sale-details-left">
            <div class="qv-sale-status">
                <input-inline-label class="qv-font-bold" label="Status" label-style="qv-sale-input-label">
                    <form-select v-model="sale.saleStatusId" :select-ref="firstElement" :options="props.classifications.SaleStatus" :readonly="props.readonly" :errors="getErrorsForLabel('Status')" data-cy="input-sale-status" />
                </input-inline-label>
                <input-inline-label label="Level of Assurance" label-style="qv-sale-input-label">
                    <form-select v-model="sale.assuranceLevelId" :readonly="props.readonly || isConfirmedSale" :options="props.classifications.SaleAssuranceLevel" :errors="getErrorsForLabel('Level of Assurance')" />
                </input-inline-label>
                <input-inline-label label="Agreement Date" label-style="qv-sale-input-label">
                    <input-date v-model="sale.agreementDate" :readonly="props.readonly" :errors="getErrorsForLabel('Agreement Date')" data-cy="input-sale-agreement-date" />
                </input-inline-label>
                <input-inline-label label="Settlement Date" label-style="qv-sale-input-label">
                    <input-date v-model="sale.settlementDate" :readonly="props.readonly" :errors="getErrorsForLabel('Settlement Date')" data-cy="input-sale-settlement-date"/>
                </input-inline-label>
            </div>
            <div class="qv-sale-price">
                <input-inline-label label="Gross" label-style="qv-sale-input-label-sm">
                    <input-number
                        :readonly="props.readonly"
                        :errors="getErrorsForLabel('Gross')"
                        v-model="sale.price.gross"
                        format="$0,0"
                        data-cy="input-sale-gross"
                    />
                </input-inline-label>
                <input-inline-label label="Net" label-style="qv-sale-input-label-sm">
                    <input-number
                        v-model="netPrice"
                        :errors="getErrorsForLabel('Net')"
                        format="$0,0"
                        readonly
                    />
                </input-inline-label>
                <input-inline-label label="Chattels" label-style="qv-sale-input-label-sm">
                    <input-number
                        :readonly="props.readonly"
                        v-model="sale.price.chattels"
                        format="$0,0"
                    />
                </input-inline-label>
                <input-inline-label label="Other" label-style="qv-sale-input-label-sm">
                    <input-number
                        :readonly="props.readonly"
                        v-model="sale.price.other"
                        format="$0,0"
                    />
                </input-inline-label>
                <input-inline-label label="GST" label-style="qv-sale-input-label-sm">
                    <input-number
                        :readonly="props.readonly"
                        v-model="sale.price.gst"
                        format="$0,0"
                    />
                </input-inline-label>
            </div>
        </div>
        <div class="qv-sale-details-right">
            <div class="qv-flex-row qv-flex-grow qv-flex-shrink qv-gap-1">
                <div class="qv-sale-classification">
                    <input-inline-label label="Sale Type" label-style="qv-sale-input-label-sm">
                        <form-select
                            v-model="sale.classifications.saleTypeId" :readonly="props.readonly"
                            :options="classifications.SaleType" :errors="getErrorsForLabel('Sale Type')"
                            :option-name="option => `${option.code} - ${option.description}`"
                        />
                    </input-inline-label>
                    <input-inline-label label="Tenure Type" label-style="qv-sale-input-label-sm">
                        <form-select
                            v-model="sale.classifications.saleTenureId" :readonly="props.readonly"
                            :options="classifications.SaleTenureType" :errors="getErrorsForLabel('Tenure Type')"
                            :option-name="option => `${option.id} - ${option.description}`"
                        />
                    </input-inline-label>
                    <input-inline-label label="Price Value Relationship" label-style="qv-sale-input-label-sm">
                        <form-select
                            v-model="sale.classifications.priceValueRelationshipId" :readonly="props.readonly"
                            :options="classifications.PriceValueRelationshipType" :errors="getErrorsForLabel('Price Value Relationship')"
                            :option-name="option => `${option.id} - ${option.description}`"
                        />
                    </input-inline-label>
                    <input-inline-label label="Sale PDF#" label-style="qv-sale-input-label-sm">
                        <div class="qv-form-input-spinner-container">
                            <input class="qv-form-input" v-model="sale.salesDirectId" :readonly="props.readonly || props.salePdfLoading" @change="emit('salePdfNumberChanged')"
                                   :class="{ 'qv-form-input-error': getErrorsForLabel('Sale PDF#').length }"
                                   data-cy='input-sale-pdf'
                            >
                            <transition name="fade" duration="50">
                                <material-icon v-if="props.salePdfLoading" icon="autorenew" class="qv-color-mediumblue qv-spinner qv-form-input-spinner"/>
                            </transition>
                            <form-item-errors :errors="getErrorsForLabel('Sale PDF#')" />
                        </div>
                    </input-inline-label>
                </div>
                <div class="qv-flex-column qv-flex-grow">
                    <input-inline-label label="Vendor/Purchaser" label-style="qv-sale-input-label">
                        <input-text
                            v-model="sale.vendorPurchaser" :readonly="props.readonly"
                            maxlength="50" :errors="getErrorsForLabel('Vendor/Purchaser')"
                            data-cy="input-sale-vendor"
                        />
                    </input-inline-label>
                    <input-inline-label class="qv-h-full" label="QV Remarks" label-style="qv-sale-input-label">
                        <form-text-area
                            v-model="sale.remarks" :readonly="props.readonly" :errors="getErrorsForLabel('QV Remarks')"
                            class="qv-h-full" maxlength="50"
                        />
                    </input-inline-label>
                    <div class="qv-flex-row" style="justify-content: space-between;">
                        <input-label label="Source" style="max-width: 96px; width: unset;">
                            <form-select
                                v-model="sale.pdfNumberSource" :readonly="props.readonly"
                                :options="pdfNumberSourceOptions" :errors="getErrorsForLabel('Sale PDF# Source')"
                                :include-empty-option="true"
                                @change="emit('salePdfNumberChanged')"
                                style="min-width: 10rem;"
                                data-cy="input-sale-pdf-source"
                            />
                        </input-label>
                        <input-inline-label label="Exclude from RTV/HPI" label-style="qv-sale-input-label-sm" style="align-items: flex-end;margin-right: -10px;">
                            <input class="qv-form-input" type="checkbox" v-model="sale.excludeFromRTV" :readonly="props.readonly" />
                        </input-inline-label>
                    </div>
                </div>
            </div>
            <div class="qv-flex-row qv-flex-grow">
                <input-inline-label class="qv-flex-grow" label="Legal" label-style="qv-sale-input-label-sm">
                    <input-text
                        v-model="sale.propertyInfo.legalDescription" :readonly="props.readonly" maxlength="50"
                        :errors="getErrorsForLabel('Legal')"
                        data-cy="input-sale-legal"
                    />
                </input-inline-label>
            </div>
        </div>
    </div>
</template>

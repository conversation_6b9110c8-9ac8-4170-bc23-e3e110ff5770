<script setup>
import { ref, computed } from 'vue';
import FormTextArea from '@/components/common/form/FormTextArea.vue';
import ConfirmationModal from '@/components/common/modal/dialog/ConfirmationModal.vue';
import LoadingSpinner from '@/components/common/LoadingSpinner.vue';
import useExpandedSales from '@/composables/useExpandedSales';

const { saving, addSaleInspectionConsent, addSaleInspectionConsentResult } = useExpandedSales();

const props = defineProps({
    formattedErrors: {
        type: Array,
        default: () => [],
    },
    activeConsents: {
        type: Array,
        default: () => [],
    },
    isOpen: {
        type: Boolean,
        default: false,
    },
    qpid: {
        required: true,
    },
    saleId: {
        required: true,
    },
    address: {
        required: true,
        type: String,
    },
    property: {
        required: true,
        type: Object,
    },
});

const canBeValuedInMonarch = computed(() => props.property?.canBeValued && props.property?.categoryCode?.startsWith('R'));

const emit = defineEmits(['close']);
const defaultDescription = 'Sales Inspection Request';
const siConsentDescription = ref(defaultDescription);
const siConsentNotes = ref('');
const siConsentDescriptionErrors = computed(() => {
    const errors = [];
    if (!siConsentDescription.value) {
        errors.push('Description is required');
    }
    return errors;
});

function handleClose() {
    addSaleInspectionConsentResult.value = {};
    siConsentDescription.value = defaultDescription;
    siConsentNotes.value = '';
    emit('close');
}

const requestCompleted = computed(() => addSaleInspectionConsentResult?.value?.status);
const cancelText = computed(() => (requestCompleted.value ? 'CLOSE' : 'CANCEL'));
const confirmText = computed(() => (requestCompleted.value ? 'CLOSE' : 'ADD'));

async function addConsent() {
    if (requestCompleted.value) {
        handleClose();
        return;
    }
    await addSaleInspectionConsent({
        saleId: props.saleId,
        description: siConsentDescription.value,
        notes: siConsentNotes.value,
    });
}

const confirmDisabled = computed(() => {
    if (requestCompleted.value) {
        return false;
    }
    return !siConsentDescription.value.length > 0 || (addSaleInspectionConsentResult?.value?.status ?? '').length > 0 || saving.value === true;
});

</script>

<template>
    <confirmation-modal
        dialog-title="Add Sales Inspection Consent" :is-open="props.isOpen" :should-confirm-close="false"
        :confirm-disabled="confirmDisabled" :show-confirm-button="!requestCompleted" :confirm-text="confirmText" :cancel-text="cancelText" @close="handleClose"
        @confirm="addConsent"
    >
        <div v-if="(addSaleInspectionConsentResult && addSaleInspectionConsentResult.status) || saving" class="qv-text-sm" :class="{ 'qv-error' : addSaleInspectionConsentResult.status === 'FAILED' }">
            {{ addSaleInspectionConsentResult.message || '' }}
        </div>
        <div v-else>
            <div class="si-consent-info">
                <div>
                    <div class="si-consent-label">QPID</div>
                    <div class="si-consent-value">{{ props.qpid }}</div>
                </div>
                <div>
                    <div class="si-consent-label">Address</div>
                    <div class="si-consent-value">{{ props.address }}</div>
                </div>
                <div>
                    <label for="siConsentDescription" class="si-consent-label">Description</label>
                    <form-text-area id="siConsentDescription" class="si-consent-value" v-model="siConsentDescription" maxlength="100" :should-trim="true" :errors="siConsentDescriptionErrors" />
                </div>
                <div v-if="canBeValuedInMonarch">
                    <label for="siConsentNotes" class="si-consent-label">Notes</label>
                    <form-text-area id="siConsentNotes" class="si-consent-value" v-model="siConsentNotes" :should-trim="true" />
                </div>
            </div>
            <div v-if="props.activeConsents && props.activeConsents.length" class="active-consents">
                <div style="font-size: 1.1rem; text-decoration: underline;">Active Consents on Property</div>
                <div class="active-consents-grid">
                    <div v-for="consent in props.activeConsents" class="active-consents-grid-row">
                        <div>{{ consent.buildingConsentNumber }}</div>
                        <div>{{ consent.buildingConsentDescription }}</div>
                        <div>{{ consent.buildingConsentStatusDescription }}</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <p v-if="errorMessage" style="margin-top: 1rem" class="relink-failure">Error message here</p>
        <p v-if="showRelinkSuccessMessage" style="margin-top: 1rem" class="relink-success">Sales Inspection Consent Added</p> -->
        <loading-spinner v-if="saving" />
    </confirmation-modal>
</template>

<style lang="scss">
.si-consent-info > div {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
}

.sale-validation-title {
    color: var(--qv-color-error);
}

.active-consents-grid-row {
    color: var(--qv-color-error);
    display: grid;
    grid-template-columns: 1fr 10fr 1fr;
    gap: 1rem;
    padding: 0.5rem 0;
}

.active-consents-grid-row > div:first-child {
  min-width: 80px;
  overflow-wrap: break-word;
}

.si-consent-info {
    font-size: 1.1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.si-consent-label {
    min-width: 80px;
}

.si-consent-value {
    flex: 1
}

</style>

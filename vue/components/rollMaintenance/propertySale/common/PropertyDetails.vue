<template>
    <div v-if="property && linzTitles">
        <table class="table">
            <thead>
                <tr>
                    <th>

                    </th>
                    <th>
                        <span>Category</span>
                    </th>
                    <th>
                        <span>Title</span>
                    </th>
                    <th>
                        <span>Legal</span>
                    </th>
                    <th>
                        <span>Area</span>
                    </th>
                    <th>
                        <span>Subdivision</span>
                    </th>
                    <th>
                        <span>Consent</span>
                    </th>
                    <th>
                        <span>Floor Plans</span>
                    </th>
                    <th>
                        <span>Objection</span>
                    </th>
                    <th>
                        <span>Owner/Occupier</span>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <th>
                        QIVS
                    </th>
                    <td>
                        {{ property.category.code }}
                    </td>
                    <td>
                        {{ property.certificateOfTitle }}
                    </td>
                    <td>
                        {{ property.legalDescription }}
                    </td>
                    <td>
                        {{ property.landUseData.landArea }}
                    </td>
                    <td>
                        ???
                    </td>
                    <td>
                        ???
                    </td>
                    <td>
                        ???
                    </td>
                    <td>
                        ???
                    </td>
                    <td v-html="qivsOwnerOccupier">
                    </td>
                </tr>
                <tr v-if="linzTitles.titles">
                    <th>
                        LINZ
                    </th>
                    <td>
                    </td>
                    <td>
                        {{ linzTitles.titles[0].titleReference }}
                    </td>
                    <td>
                        {{ linzTitles.titles[0].legalDescriptions[0].description }}
                    </td>
                    <td>
                        {{ linzTitles.titles[0].landArea }}
                    </td>
                    <td>
                    </td>
                    <td>
                    </td>
                    <td>
                    </td>
                    <td>
                    </td>
                    <td>

                    </td>
                </tr>
            </tbody>
        </table>

    </div>
</template>

<script>
export default {
    props: {
        property: {
            type: Object
        },
    },
    data() {
        return {
            linzTitles: null
        }
    },
    computed: {
        qivsOwnerOccupier() {
            var ownerOccupierDisplay = "";

            this.property.owners.forEach(o => {
                ownerOccupierDisplay = ownerOccupierDisplay + 'Owner - ' + (o.firstName ? o.firstName + ' ' : '') + (o.secondName ? o.secondName + ' ' : '') + (o.thirdName ? o.thirdName + ' ' : '' )
                            + (o.lastName ? o.lastName + ' ' : '') + '<br />';
            });
            this.property.occupiers.forEach(o => {
                ownerOccupierDisplay = ownerOccupierDisplay + 'Occupier - ' + (o.firstName ? o.firstName + ' ' : '') + (o.secondName ? o.secondName + ' ' : '') + (o.thirdName ? o.thirdName + ' ' : '' )
                            + (o.lastName ? o.lastName + ' ' : '') + '<br />';
            });

            return ownerOccupierDisplay;
        }
    },
    methods: {
        async getLinzTitles(qpid) {
            var tmp_linzTitles = {};
            var get = jsRoutes.controllers.PropertyMasterData.getLinzTitles(qpid);
            await $.ajax({
                type: "GET",
                url: get.url,
                cache: false,
                success: function (response) {
                    tmp_linzTitles = response;
                },
                error: function (response) {
                    console.error('error getting qv property details: ' + response);
                    console.log(response);
                }
            });
            this.linzTitles = tmp_linzTitles;
        },
    },
    async created() {
        await this.getLinzTitles(this.property.qupid);
    }
}
</script>

<style lang="scss" scoped>
    .router table.table, .legacy-scope table.table{
        margin-top:20px;
    }

    .router table.table tr, .legacy-scope table.table tr{
        height:4rem;
    }

    .router table.table tr th, .legacy-scope table.table tr th {
        font-size: 1.2rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 1);
        background-color:#2c3c61;
        border-bottom: none;
    }

    .router table.table thead tr th, .legacy-scope table.table thead tr th,
    .router table.table tbody tr td, .legacy-scope table.table tbody tr td {
        text-align:left;
        padding:5px 10px;
    }

    .router table.table tbody tr th, .legacy-scope table.table tbody tr th {
        color: rgba(255, 255, 255, 1);
        background-color:#5290db;
        padding:0 10px;
    }
</style>

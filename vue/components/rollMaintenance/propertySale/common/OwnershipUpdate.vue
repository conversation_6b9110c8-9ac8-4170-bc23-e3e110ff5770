<script setup>
import { computed } from 'vue';
import InputText from '../../../common/form/InputText.vue';
import InputInlineLabel from '../../../common/form/InputInlineLabel.vue';
import FormSelect from '../../../common/form/FormSelect.vue';

const props = defineProps({
    ownership: {
        type: Object,
        required: true,
    },
    isReadOnly: {
        type: Boolean,
        required: false,
        default: false,
    },
    isAnyExistingQivsOwnerNonOccupier: {
        type: Boolean,
        required: false,
        default: false,
    },
    isAnyExistingQivsOwnerHavingMultipleProperties: {
        type: Boolean,
        required: false,
        default: false,
    },
    getErrorsForLabel: {
        type: Function,
        required: true,
    },
    validationResult: {
        type: Object,
        required: false,
        default: () => ({}),
    },
});

const addressError = computed(() => props.validationResult?.validations?.['Address']?.errors?.[0]);

const phonePrefixOptions = [
    { id: null, description: '' },
    { id: '03', description: '03' },
    { id: '04', description: '04' },
    { id: '06', description: '06' },
    { id: '07', description: '07' },
    { id: '08', description: '08' },
    { id: '09', description: '09' },
];
const mobilePrefixOptions = [
    { id: null, description: '' },
    { id: '021', description: '021' },
    { id: '022', description: '022' },
    { id: '027', description: '027' },
    { id: '029', description: '029' },
    { id: '020', description: '020' },
];

function isIndividual(owner) {
    if (owner.linzOwner) {
        return !owner.isOrganisation;
    }
    return [1, 6].includes(parseInt(owner.entityId));
}

</script>

<template>
    <div class="sp-ownership-container">
        <div class="qivs-update-messages">
            <div v-if="isAnyExistingQivsOwnerNonOccupier">This property has more than one level of ownership.</div>
            <div v-if="isAnyExistingQivsOwnerHavingMultipleProperties">This owner is associated with more than one property.</div>
            <div v-if="isAnyExistingQivsOwnerNonOccupier || isAnyExistingQivsOwnerHavingMultipleProperties">Please update ownership in QIVS.</div>
        </div>
        <div class="sp-ownership-grid">
            <div class="occupier-container">
                <label class="ownership-label" style="margin-left: 95px;">TORA</label>
            </div>
            <div class="occupier-container occupier-label-container">
                <label class="ownership-label">Ownership Details</label>
                <label class="ownership-label">Surname</label>
                <label class="ownership-label">First Name</label>
                <label class="ownership-label">Second Name</label>
                <label class="ownership-label">Third Name</label>
                <label class="ownership-label" style="text-align: center;">Secret</label>
            </div>
            <div class="occupier-container">
                <label class="ownership-label" style="margin-left: 65px;">Owner/Occupier Address Details</label>
            </div>
            <div class="sp-ownership-update">
                <div class="sp-sale-details-item qv-align-center qv-flex">
                    <input-inline-label label="Tenure" label-style="sp-sale-input-label" wrapper-style="qv-w-full">
                        <input-text :value="ownership.toras.tenure" :readonly="true" :errors="getErrorsForLabel('Tenure')" />
                    </input-inline-label>
                </div>
                <div class="sp-sale-details-item qv-align-center qv-flex">
                    <input-inline-label label="Ownership" label-style="sp-sale-input-label" wrapper-style="qv-w-full">
                        <input-text :value="ownership.toras.entity" :readonly="true" :errors="getErrorsForLabel('Ownership')" />
                    </input-inline-label>
                </div>
                <div class="sp-sale-details-item qv-align-center qv-flex">
                    <input-inline-label label="Rateability" label-style="sp-sale-input-label" wrapper-style="qv-w-full">
                        <input-text :value="ownership.toras.rateability" :readonly="true" :errors="getErrorsForLabel('Rateability')" />
                    </input-inline-label>
                </div>
                <div class="sp-sale-details-item qv-align-center qv-flex">
                    <input-inline-label label="Apport" label-style="sp-sale-input-label" wrapper-style="qv-w-full">
                        <input-text :value="ownership.toras.apportionment" :readonly="true" :errors="getErrorsForLabel('Apport')" />
                    </input-inline-label>
                </div>
            </div>
            <div class="occupier-container">
                <div v-for="(owner, index) in ownership.owners" :class="{'occupier-row-org' : !(isIndividual(owner)), 'occupier-row' : isIndividual(owner)}">
                    <input-text :value="owner.type" :readonly="true" />
                    <input-text :value="owner.assessment.order" :readonly="true" :text-center="true"/>
                    <input-text :value="owner.entity" :readonly="true" :errors="getErrorsForLabel(`Owner Entity ${index}`)" />
                    <input-text v-if="!(isIndividual(owner))" data-cy="sp-surname-org" v-model="owner.surname" :readonly="isReadOnly" :errors="getErrorsForLabel(`Organisation ${index}`)" />
                    <input-text v-if="isIndividual(owner)" data-cy="sp-surname-org" v-model="owner.surname" :readonly="isReadOnly" :errors="getErrorsForLabel(`Surname ${index}`)" />
                    <input-text v-if="isIndividual(owner)" v-model="owner.firstName" :readonly="isReadOnly" :errors="getErrorsForLabel(`First Name ${index}`)" />
                    <input-text v-if="isIndividual(owner)" v-model="owner.secondName" :readonly="isReadOnly" :errors="getErrorsForLabel(`Second Name ${index}`)" />
                    <input-text v-if="isIndividual(owner)" v-model="owner.thirdName" :readonly="isReadOnly" :errors="getErrorsForLabel(`Third Name ${index}`)" />
                    <div class="occupier-secret-checkbox-container">
                        <input v-model="owner.nameSecretYN" tabindex="0" type="checkbox" class="qv-form-base" style="width: 19px; height: 19px;" :disabled="isReadOnly">
                    </div>
                </div>
                <div v-if="!ownership.owners || ownership.owners.length === 0">
                    <span style="margin-bottom: 1rem; color: red;">No occupiers associated with sale</span>
                </div>
                <div class="contact-row">
                    <div class="contact-row-segment-1">
                        <label class="ownership-label" style="text-align: right;">Phone</label>
                        <form-select
                            v-model="ownership.owners[0].daytimePhoneSTD"
                            :options="phonePrefixOptions"
                            :option-name="option => option.description"
                            :readonly="isReadOnly"
                            :select-class="'qv-p-0'"
                            :errors="getErrorsForLabel('Phone Prefix')"
                            placeholder=""
                        />
                        <input-text v-model="ownership.owners[0].daytimePhone" :readonly="isReadOnly" :errors="getErrorsForLabel('Phone')"/>
                    </div>
                    <div class="contact-row-segment-2">
                        <div class="contact-row-segment-3">
                            <label class="ownership-label" style="text-align: right;">Mobile</label>
                            <form-select
                                v-model="ownership.owners[0].mobilePhoneSTD"
                                :options="mobilePrefixOptions"
                                :option-name="option => option.description"
                                :readonly="isReadOnly"
                                :select-class="'qv-p-0'"
                                :errors="getErrorsForLabel('Mobile Prefix')"
                                placeholder=""
                            />
                            <input-text v-model="ownership.owners[0].mobilePhone" :readonly="isReadOnly" :errors="getErrorsForLabel('Mobile')"/>
                        </div>
                        <div class="contact-row-segment-4">
                            <label class="ownership-label" style="text-align: right;">Email</label>
                            <input-text v-model="ownership.owners[0].email" :readonly="isReadOnly" :errors="getErrorsForLabel('Email')"/>
                            <span />
                        </div>
                    </div>
                </div>
            </div>
            <div class="sp-ownership-update">
                <div v-if="addressError" class="qv-form-input-error-message" style="margin-bottom: 10px;">
                    {{ addressError }}
                </div>
                <div class="sp-sale-details-item qv-align-center qv-flex">
                    <input-inline-label label="C/O" label-style="sp-sale-input-label-small" wrapper-style="qv-w-full">
                        <input-text v-model="ownership.owners[0].address.CO" :readonly="isReadOnly" :errors="getErrorsForLabel('C/O')"/>
                    </input-inline-label>
                </div>
                <div class="sp-sale-details-item qv-align-center qv-flex">
                    <input-inline-label label="Street/Box" label-style="sp-sale-input-label-small" wrapper-style="qv-w-full">
                        <input-text v-model="ownership.owners[0].address.streetBox" :readonly="isReadOnly" :errors="getErrorsForLabel('Street/Box')"/>
                    </input-inline-label>
                </div>
                <div class="sp-sale-details-item qv-align-center qv-flex">
                    <input-inline-label label="Suburb" label-style="sp-sale-input-label-small" wrapper-style="qv-w-full">
                        <input-text v-model="ownership.owners[0].address.suburb" :readonly="isReadOnly" :errors="getErrorsForLabel('Suburb')"/>
                    </input-inline-label>
                </div>
                <div class="town-postcode-container">
                    <input-inline-label label="Town" label-style="sp-sale-input-label-small" wrapper-style="qv-w-full">
                        <input-text v-model="ownership.owners[0].address.town" :readonly="isReadOnly" :errors="getErrorsForLabel('Town')"/>
                    </input-inline-label>
                    <input-inline-label label="Postcode" label-style="sp-sale-input-label-xs" wrapper-style="qv-w-full">
                        <input-text v-model="ownership.owners[0].address.postcode" :readonly="isReadOnly" :errors="getErrorsForLabel('Postcode')"/>
                    </input-inline-label>
                </div>
            </div>
        </div>
    </div>
</template>
<style lang="scss" src='../../../rollMaintenance/rollMaintenance.scss' />

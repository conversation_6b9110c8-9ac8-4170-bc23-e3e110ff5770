<script setup>
import { useRoute, useRouter } from 'vue-router/composables';
import { onMounted, ref } from 'vue';

const route = useRoute();
const router = useRouter();
const { qpid } = route.params;
const hasError = ref(false);

onMounted(initRedirect);

async function initRedirect() {
    try {
        const { url } = jsRoutes.controllers.SalesProcessingController.createDraftSale(qpid);
        const res = await fetch(`${url}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({}),
        });
        const sale = await res.json();
        if (!sale?.saleId) throw new Error('Unable to create draft sale');
        await router.push({ name: 'draft-property-sale', params: { id: sale.saleId, step: 1 } });
    }
    catch (error) {
        console.error(error);
        hasError.value = true;
    }
}

</script>

<template>
    <div>
        <h1 v-if="hasError" style="font-size: xx-large; color: red;">Oops, something went wrong!</h1>
    </div>
</template>

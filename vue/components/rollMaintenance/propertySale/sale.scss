@import "../../../qv-layout";
@import "../../../qv-text";
@import "../../../qv-form";
@import "../../../qv-colors";

.qv-sale-input-label {
  min-width: 100px;

  &-sm {
    min-width: 70px;
    max-width: 70px;
  }
}

.qv-sale-details-left {
  @extend .qv-flex-row;
  width: 45%;

  .qv-sale-status {
    @extend .qv-flex-column;
    width: 55%;
  }

  .qv-sale-price {
    @extend .qv-flex-column;
    width: 45%;
  }
}

.qv-sale-details-right {
  @extend .qv-flex-column;
  width: 55%;

  .qv-sale-classification {
    @extend .qv-flex-column;
    width: 40%;
  }
}

.qv-multi-sale-table {
  tr > th {
    border-bottom: solid 1px var(--qv-color-dark)
  }

  $mdl-bg-color: rgba(158,158,158, 0.20);
  button {
    @extend .qv-text-sm;
    color: var(--qv-color-dark);
    font-weight: bold;
    background-color: $mdl-bg-color;
    border: none;
    border-radius: $input-border-radius;
    padding: 5px 10px;
    display: inline-flex;
    align-items: center;
    text-transform: capitalize;

    &:hover {
      background-color: darken($mdl-bg-color, 20%);
      cursor: pointer;
    }

    &:focus {
      outline: 2px solid var(--qv-color-mediumblue);
    }
  }
}
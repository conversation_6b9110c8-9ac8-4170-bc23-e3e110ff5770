<script setup>
import axios from '@/utils/AxiosHeaders';
import { computed, onMounted, ref, watch } from 'vue';
import PropertySummary from '@/components/property/PropertySummary.vue';
import { store } from '@/DataStore';
import { useRoute, useRouter } from 'vue-router/composables';
import useSales from '@/composables/useSales';
import useValuerInfo from '@/composables/useValuerInfo';
import PropertyInfo from '@/components/property/PropertyInfo.vue';
import Sale from '@/components/rollMaintenance/propertySale/common/Sale.vue';
import ContainerCollapsible from 'Common/ContainerCollapsible.vue';
import DvrDetails from '@/components/rollMaintenance/propertySale/common/DvrPropertyDetails.vue';
import MButton from 'Common/MButton.vue';
import LoadingSpinner from 'Common/LoadingSpinner.vue';
import PropertyTitles from '@/components/property/PropertyTitles.vue';
import ReasonForChange from 'Common/form/ReasonForChange.vue';
import CrossReferencedProperties from '@/components/rollMaintenance/propertySale/common/CrossReferencedProperties.vue';
import ConfirmationModal from '../../common/modal/dialog/ConfirmationModal.vue';
import InputNumber from '../../common/form/InputNumber.vue';
import RfsDetails from '@/components/rollMaintenance/propertySale/common/RfsDetails.vue';
import JobSidebar from '@/components/common/JobSidebar.vue';
import Vue from 'vue';
import { DateTime } from 'luxon';
import useModal from '@/composables/useModal';

const modal = useModal();
const router = useRouter();
const route = useRoute();


const {
    sale,
    dvr,
    saving,
    getSalesDirectSaleExistsLoading,
    validationResult,
    formattedValidationResult,
    saleValidationModalOpen,
    exception,
    titles,
    taLandUse,
    isMultiSale,
    crossReferencedProperties,
    qpid,
    saleId,
    isNewSale,
    primaryProperty,
    propertyInfoFull,
    loadSale,
    loadPrimaryProperty,
    saveSale,
    deleteSale,
    validateSale,
    relinkSaleRfs,
    refreshSale,
    getDvr,
    getSalePortalSalePdfUrl,
    refreshPropertyData,
    getErrorsForLabel,
    isSaleToBeProcessed,
    linzWarnings,
    recalculateValues,
    revisionValues,
    openSalePdf,
    rtv,
    rtvMissing,
} = useSales();


const {
    users,
    loadUsers,
} = useValuerInfo();

const relinkFailureMessage = ref(null);
const showRelinkSuccessMessage = ref(false);
const shouldShowDeleteConfirmationModal = ref(false);
const shouldShowUpdateConfirmationModal = ref(false);
const classifications = ref(null);
const loaded = ref(false);
const loadingMessage = ref('');
const propertyId = computed(() => {
    return primaryProperty.value?.property.id;
});
const salesDirectState = ref({
    buttonText: 'VIEW',
    searching: false,
    searched: false,
    urlValid: false,
    searchTimer: null,
});
const pageTitle = computed(() => {
    if (isNewSale?.value) {
        return 'Add Sale';
    }
    if (!isReadOnly?.value) {
        return 'Update Sale';
    }
    return 'View Sale';
});
const selectedQpids = computed(() => crossReferencedProperties.value.map(p => p.qpid));
const user = computed(() => store.state.userData);
const isInternalUser = computed(() => user?.value?.isInternalUser);
const isReadOnly = computed(() => !isInternalUser.value || sale.value.deleted);
const shouldShowDeleteButton = computed(() => !isNewSale.value && !isReadOnly.value);
const isRfcOutputValid = computed(() => parseInt(sale.value?.rfc?.output) >= 0);
const isRfcSourceValid = computed(() => sale.value?.rfc?.source);
const isRfcReasonValid = computed(() => sale.value?.rfc?.reason);
const isRfcValid = computed(() => isRfcOutputValid.value && isRfcSourceValid.value && isRfcReasonValid.value);
const shouldDeleteConfirmBeDisabled = computed(() => !isRfcValid.value || saleRfsStatus.value === 1);
const saleHasRfs = computed(() => sale.value?.rfs?.number);
const saleRfsStatus = computed(() => sale.value?.rfs?.status);
const saleRfsStatusText = computed(() => {
    if (sale.value?.rfs?.status === 1) {
        return 'OPEN';
    }
    if (sale.value?.rfs?.status === 2) {
        return 'CLOSED';
    }
    if (sale.value?.rfs?.status === 0) {
        return 'N/A';
    }
    return 'unknown';
});
const hasNonDeleteCrossReferencedProperties = computed(() => crossReferencedProperties?.value?.filter(p => p.deleted !== true).length > 0);
const expandTitles = ref(true);
const expandSale = ref(true);
const expandCrossReferencedProperties = ref(false);
const expandDvrDetails = ref(true);
const expandRfc = ref(true);
const saleHasCrossReferenceProperties = computed(()=> sale.value?.crossReferencedProperties?.length > 0);
const multiSaleTypeId = 1;
const salePdfUrl = ref(null);
const isSaleToProcess = computed(() => sale.value?.monarchSaleProcessingStatusId == 1);

watch(() => titles.value, () => {
    recalculateValues();
});

async function handleSalePdfNumberChanged() {
    salePdfUrl.value = null;
    const containsLetters = /[a-zA-Z]/.test(sale.value?.salesDirectId);
    if (containsLetters) {
        return;
    }
    const salePdfNumber = parseFloat(sale.value?.salesDirectId);
    if (!(Number.isInteger(salePdfNumber)) || salePdfNumber < 1 || !sale.value?.pdfNumberSource) {
        return;
    }
    const { pdfUrl } = await getSalePortalSalePdfUrl(sale.value?.salesDirectId, sale.value?.pdfNumberSource ?? 1);
    salePdfUrl.value = pdfUrl;
}

async function relinkSale() {
    const result = await relinkSaleRfs();
    relinkFailureMessage.value = null;
    if (result?.status === 'INVALID') {
        relinkFailureMessage.value = result.message;
    }
    else if (result?.status === 'UPDATED') {
        await refreshSale(saleId.value);
        showRelinkSuccessMessage.value = true;
    }
    else {
        relinkFailureMessage.value = 'Oops, something went wrong!';
    }
}

function hideWarningModal() {
    this.showWarnings = false;
}

function showWarningModal() {
    this.showWarnings = true;
}

async function trySaveSale() {
    if (isReadOnly.value) {
        return;
    }
    await saveSale();
}

async function updateAndProcessSale() {
    if (isReadOnly.value) {
        return;
    }
    isSaleToBeProcessed.value = true;
    await saveSale();
}

async function loadProperty(qpid) {
    propertyDetail.value = await getProperty(qpid);
}

function handleDeleteConfirmationClose() {
    shouldShowDeleteConfirmationModal.value = false;
    relinkFailureMessage.value = null;
    showRelinkSuccessMessage.value = false;
}

function cancel() {
    window.close();
}

const lastUpdatedBy = computed(()=> {
    return {
        name: isNewSale?.value ? null : sale?.value?.updatedBy || sale?.value?.createdBy || 'System',
        date: (sale?.value?.updatedBy || sale?.value?.createdBy) ? (sale?.value?.updatedBy ? sale?.value?.updatedAt : sale?.value?.createdAt) : null,
    }
});

const labels = computed(() => {
    const labelsArr = [
        {
            title: 'Source',
            value: isNewSale?.value ? 'MANUAL' : sale.value?.saleSource ?? 'UNKNOWN',
            hide: isReadOnly.value,
            class: 'success',
        },
        {
            title: 'Processing Status',
            value: sale.value?.monarchSaleProcessingStatus ?? 'UNKNOWN',
            hide: isReadOnly.value,
            class: 'info',
        },
    ];
    if (!isNewSale?.value) {
        labelsArr.unshift({
            title: 'Sale Id',
            value: saleId.value,
            class: 'success',
        });
    }
    if (sale?.value?.previousSale) {
        labelsArr.push({
            title: 'Previous Sale',
            value: sale.value.previousSale.description === 'Whole' ? 'Single' : sale.value.previousSale.description,
            hide: isReadOnly.value,
            class: 'success',
            onClick: () => {
                const url = router.resolve({
                    name: 'property-sale',
                    params: {
                        id: sale.value.previousSale.saleId,
                        qpid: qpid.value,
                    },
                });
                window.open(url.href, '_blank');
            },
        });
    }

    if (sale?.value?.deleted) {
      labelsArr.push({
        title: 'Warnings',
        value: 'Deleted Sale',
        class: 'error',
      })
    }

    return labelsArr;
});
const warnings = computed(() => linzWarnings.value);
const apportionmentCode = computed(() => primaryProperty.value?.property?.apportionment?.code);

function handleLabelClick(labelTitle) {
    const label = labels.value.find(l => l.title === labelTitle);
    if (label?.onClick) {
        label.onClick();
    }
}

function viewSalePdf() {
    if (!salePdfUrl.value) {
        return;
    }
    openSalePdf(sale.value?.salesDirectId, sale.value?.pdfNumberSource);
}

watch(isMultiSale, (value) => {
    if (value) {
        expandCrossReferencedProperties.value = true;
    }
});

onMounted(async () => {
    loadingMessage.value = 'Loading sale...';
    const promises = [
        loadSale(parseInt(route.params.qpid), route.params.id, Boolean(route.query?.validate)),
        loadUsers(),
    ];
    await Promise.all(promises);
    if (isNewSale.value && !isInternalUser.value) {
        router.push({ name: 'property', params: { qpid: route.params.qpid } });
    }
    if (isNewSale.value && ![0, 2, 5].includes(parseInt(apportionmentCode.value))) {
        await modal.showError('Invalid Property', `Property apportionment code is ${apportionmentCode.value || 'missing'}. You can only add sales to properties with apportionment codes 0, 2 and 5.`);
        router.push({ name: 'property', params: { qpid: route.params.qpid } });
    }
    if (sale.value?.salesDirectId) {
        const { pdfUrl } = await getSalePortalSalePdfUrl(sale.value?.salesDirectId, sale.value?.pdfNumberSource ?? 1);
        salePdfUrl.value = pdfUrl;
    }
    classifications.value = store.state.saleClassifications.classifications;
    loaded.value = true;

    if (sale.value.classifications.saleTypeId === 1) {
        expandCrossReferencedProperties.value = true;
    }
});

function handleKeyPress(event) {
    if (event.ctrlKey && event.shiftKey && (event.key === 's' || event.key === 'S')) {
        // Replace this with the function you want to run
        trySaveSale();
    }
}

// Adding event listener for 'keydown' event
document.addEventListener('keydown', handleKeyPress);

</script>

<template>
    <div class="contentWrapper resultsWrapper draftProperty">
        <property-summary
            :can-navigate="false"
            :property-id="propertyId"
        />
        <transition name="fade" mode="out-in">
            <div v-if="!loaded" key="1" class="col-container" style="padding-top: 15rem; padding-bottom: 15rem;">
                <loading-spinner :message="loadingMessage" />
            </div>
            <div v-else key="2" class="col-container" data-cy="sales-processing">
                <div class="qv-flex-row qv-pt-2 qv-pr-2">
                    <div class="qv-px-2 qv-flex-column qv-gap-2" style="min-width: 20%">
                        <job-sidebar
                            :last-updated-by="lastUpdatedBy"
                            :labels="labels"
                            :warnings="warnings"
                            :should-hide-warnings="isReadOnly"
                            @labelClicked="handleLabelClick"
                        >
                            <template #top-buttons>
                                <m-button class="mdl-button--colored" :disabled="!salePdfUrl" @click="viewSalePdf">VIEW SALE PDF</m-button>
                            </template>
                            <template #bottom-buttons>
                                <button
                                    v-if="shouldShowDeleteButton"
                                    class="mdl-button mdl-button--colored-red qv-sidebar-action-button"
                                    @click="shouldShowDeleteConfirmationModal = true"
                                    data-cy="button-sale-delete"
                                >
                                    DELETE SALE
                                </button>
                            </template>
                        </job-sidebar>
                      <!-- TODO: Move this to own modal component -->
                        <ConfirmationModal
                            dialog-title="Delete Sale"
                            :warning="true"
                            :is-open="shouldShowDeleteConfirmationModal"
                            :confirm-disabled="shouldDeleteConfirmBeDisabled"
                            confirm-class="delete-sale-confirm"
                            confirm-text="DELETE"
                            @close="handleDeleteConfirmationClose"
                            @confirm="deleteSale"
                        >
                            <div class="delete-sale-container">
                                <div class="delete-sale-messages">
                                    <p v-if="isRfcValid" style="margin-bottom: 1rem;">Are you sure you want to delete this sale?</p>
                                    <p v-if="isRfcValid && saleHasRfs" style="margin-bottom: 1rem;">This sale has an associated <strong>RFS Action Request</strong> with status: <span :class="{ 'open-rfs' : saleRfsStatus === 1, 'closed-rfs' : saleRfsStatus === 2 }"><strong>{{ saleRfsStatusText }}</strong></span></p>
                                    <p v-if="!isRfcOutputValid">Please select an Output Code.</p>
                                    <p v-if="!isRfcSourceValid">Please select a Source.</p>
                                    <p v-if="!isRfcReasonValid">Please specify a reason for deleting the sale.</p>
                                </div>
                                <rfs-details
                                    v-if="sale && saleHasRfs && isRfcValid"
                                    :sale-id="saleId"
                                    :qpid="qpid"
                                    :rfs="sale.rfs"
                                />
                            </div>
                            <div v-if="saleHasRfs && saleRfsStatus === 1 && isRfcValid" class="relink-sale">
                                <label for="relinkSaleId"><strong>Re-link</strong> RFS and action record to sale id</label>
                                <div class="relink-sale-input-button">
                                    <InputNumber
                                        id="relinkSaleId"
                                        v-model="sale.relinkSaleId"
                                        format="0"
                                    />
                                    <button class="qv-dialog-button relink-sale-button" :disabled="saving" @click="relinkSale">RELINK</button>
                                </div>
                            </div>
                            <p v-if="relinkFailureMessage" style="margin-top: 1rem" class="relink-failure">{{ relinkFailureMessage }}</p>
                            <p v-if="showRelinkSuccessMessage" style="margin-top: 1rem" class="relink-success">RFS and Action record relinked successfully</p>
                            <LoadingSpinner v-if="saving" />
                        </ConfirmationModal>
                        <property-info :qpid="qpid" :readonly="isReadOnly" />
                    </div>
                    <div class="qv-flex-grow qv-flex-column qv-gap-3">
                        <h1 class="qv-text-lg" data-cy="sale-page-title">{{ pageTitle }}</h1>
                        <container-collapsible v-model="expandTitles" ref="pdContainer" title="Title & Ownership Check" v-if="isNewSale || isSaleToProcess">
                            <property-titles v-model="titles" class="qv-w-full qv-my-3"/>
                        </container-collapsible>
                        <container-collapsible v-model="expandSale" ref="saleContainer" title="Sale Details">
                            <sale :is-new-sale="isNewSale"
                                  :classifications="classifications"
                                  :dvr="dvr"
                                  :sale="sale"
                                  :sales-direct-state="salesDirectState"
                                  tabindex="20"
                                  :readonly="isReadOnly"
                                  :sale-pdf-loading="getSalesDirectSaleExistsLoading"
                                  @salePdfNumberChanged="handleSalePdfNumberChanged"
                            />
                        </container-collapsible>
                        <container-collapsible v-model="expandCrossReferencedProperties" v-if="isMultiSale || hasNonDeleteCrossReferencedProperties" title="Multi Sale - Cross Referenced Properties">
                            <cross-referenced-properties :readonly="isReadOnly"/>
                        </container-collapsible>
                        <container-collapsible v-model="expandDvrDetails" ref="dvrContainer" title="DVR Property Details">
                            <dvr-details :classifications="classifications"
                                         :rtv="rtv" :dvr="dvr"
                                         :ta-land-use="taLandUse"
                                         :readonly="isReadOnly"
                                         :validation-result="validationResult"
                                         :revision-values="revisionValues"
                                         :rtv-missing="rtvMissing"
                                         @refreshPropertyData="refreshPropertyData"
                            />
                        </container-collapsible>
                        <container-collapsible v-model="expandRfc" title="Complete Sale">
                            <reason-for-change
                                v-if="sale"
                                :rfc="sale.rfc"
                                :readonly="isReadOnly"
                                :output-errors="getErrorsForLabel('Output')"
                                :source-errors="getErrorsForLabel('Source')"
                                :reason-errors="getErrorsForLabel('Reason for Change')"
                            >
                                <template #header>
                                    <div v-if="saleHasRfs" class="rfs-header">
                                        <h3>This sale has an associated <strong>RFS Action Request</strong> with status: <span :class="{ 'open-rfs' : saleRfsStatus === 1, 'closed-rfs' : saleRfsStatus === 2 }"><strong>{{ saleRfsStatusText }}</strong></span></h3>
                                        <rfs-details
                                            :sale-id="saleId"
                                            :qpid="qpid"
                                            :rfs="sale.rfs"
                                        />
                                    </div>
                                    <h3 v-else style="font-size: 0.9rem;">Sale has <span class="closed-rfs"><strong>NO</strong></span> RFS Action Record</h3>
                                </template>
                            </reason-for-change>
                          <!-- TODO: Move this to own modal component -->
                          <ConfirmationModal
                                dialog-title="Update Sale"
                                :warning="true"
                                :is-open="shouldShowUpdateConfirmationModal"
                                :confirm-disabled="!isRfcValid"
                                confirm-class="update-sale-button"
                                confirm-text="UPDATE"
                                @close="shouldShowUpdateConfirmationModal = false"
                                @confirm="saveSale"
                            >
                                <p v-if="isRfcValid && saleHasRfs && saleRfsStatus === 1" style="margin-bottom: 1rem;">
                                    This sale has an associated <strong>RFS Action Request</strong> with status: <span class="open-rfs"><strong>{{ saleRfsStatusText }}</strong></span>
                                    <br><span>Updating the sale will close the RFS action record</span>
                                </p>
                                <p v-if="!isRfcOutputValid">Please select an Output Code.</p>
                                <p v-if="!isRfcSourceValid">Please select a Source.</p>
                                <p v-if="!isRfcReasonValid">Please specify a reason for updating the sale.</p>
                            </ConfirmationModal>
                        </container-collapsible>
                        <div v-if="!isReadOnly" class="qv-flex-row qv-justify-end">
                            <m-button @click="cancel" data-cy="cancel-sale-button">Cancel</m-button>
                            <m-button class="mdl-button--colored" :disabled="saving" v-if="isSaleToProcess" @click="updateAndProcessSale" data-cy="update-process-sale-button">Update & Process</m-button>
                            <m-button class="mdl-button--colored" :disabled="saving" @click="trySaveSale" data-cy="save-sale-button">{{ isNewSale ? 'Add Sale' : 'Update Sale'}}</m-button>
                        </div>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<style lang="scss" src="./sale.scss"></style>
<style lang="scss">
.proposedValues ul {
    display: inline-grid;
}

.proposedValues.error {
    background-color: #fc3d39;
    padding-left: 0.3rem;
}

.draft-property {
    margin-bottom: 2rem;
}

.router {
    .col {
        padding-left: 0.3em;
        padding-right: 0.3em;
    }

    label {
        input:active,
        input:focus,
        select:active,
        select:focus {
            border: 2px solid #5290db;
        }

        select {
            -moz-appearance: initial;
            appearance: auto;
        }
    }

    .property-draft-section {
        padding-top: 0;
        padding-bottom: 0.8rem;
        /* TODO need to standardise col container but allow for collapsing margins. */
        display: table;
        width: 100%;

        .multiselect {
            input {
                border: none;
            }
        }
    }

    .property-draft-section.col-container, .property-draft-section .col-container, .property-draft-section .col-row {
        padding: 0;
        margin: 0;
    }

    .property-draft-section.col-container .col-container .col-row:first-child .col {
        margin-top: 0;
    }

    .property-draft-section.col-container .col-container .col-row .col:first-child {
        padding-left: 0;
    }

    .property-draft-section.col-container .col-container .col-row .col:last-child {
        padding-right: 0;
    }

    label {
        span.property-value {
            line-height: 39px;
            font-weight: bold;
        }
    }
}

.validation-header-message--warnings {
    font-size: 0.9em;
    overflow: auto;
}

.derived-title {
    font-weight: 600;
    padding-left: 5px;
    padding-top: 5px;
}

.derived-field {
    cursor: auto;
}

.form-stale {
    background-color: #eee;
}

.form-stale-warning {
    text-align: center;
    font-size: 0.8em;

    &--save-button {
        height: 28px;
        line-height: 28px;
        font-size: 0.9em;
    }
}

.generate-modal-input--small {
    width: 30%;
    display: inline-block;
    margin-right: 1em;
}

.button--small {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

/* TODO Standardise - this is a "small button" for inline operations */
button {
    height: 28px;
    line-height: 28px;
    font-size: 0.9em;
}

/* TODO Quick n dirty */
h2.section-title {
    background-color: #283c64;
    color: #fff;
    padding: 0.5rem;
    padding-left: 1rem;
}

.property-draft-section-row {
    display: table;
    width: 100%;
    padding-bottom: 5px;
    margin-bottom: 3px;
}

.button-row {
    display: table;
    width: 100%;
}

.mdl-button-small {
    font-size: 11px;
    padding: 0;
    line-height: 20px;
    min-width: 5rem;
    height: auto;
}

.mdl-button-small.loadingSpinner {
    font-size: 0;
    background-position-y: 1px;
    background-size: 35%;
}

.col > .mdl-button-small {
    margin-top: 10px;
}

.col-row.reduce-margins > .col {
    margin: 0;
}

.router table.table, .legacy-scope table.table {
    margin-top: 20px;
}

.router table.table tr, .legacy-scope table.table tr {
    height: 4rem;
}

.router table.table tr th, .legacy-scope table.table tr th {
    font-size: 1.2rem;
    font-weight: 600;
    border-bottom: 1px solid #2c3c61;
}

.router table.table thead tr th, .legacy-scope table.table thead tr th,
.router table.table tbody tr td, .legacy-scope table.table tbody tr td {
    text-align: left;
    padding: 0 10px;
}

.router table.table tbody tr th, .legacy-scope table.table tbody tr th {
    color: rgba(255, 255, 255, 1);
    background-color: #5290db;
    padding: 0 10px;
}

.router table.table tbody tr.alt td, .legacy-scope table.table tbody tr.alt td {
    background-color: #f6f6f6;
}

.open-rfs, .relink-success {
    color: var(--qv-color-success);
}
.closed-rfs, .relink-failure {
    color: var(--qv-color-error);
}

.relink-sale {
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
}
.relink-sale-input-button {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}
.relink-sale-button {
    color: #fff;
    background: var(--qv-color-success) !important;
}

.delete-sale-confirm {
    background: var(--qv-color-error) !important;
}

.delete-sale-container {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.rfs-header {
    font-size: 0.9rem;
    display: flex;
    flex-direction: column;
}

.update-sale-button {
    background: var(--qv-color-mediumblue) !important;
}

.refresh-property-data-button {
    background: var(--qv-color-mediumblue) !important;
    float: right;
}

</style>

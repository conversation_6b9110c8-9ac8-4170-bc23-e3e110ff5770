<template>
    <div class="resultsWrapper router">
        <property-summary
            :is-in-subdivision="worksheet.isInSubdivision"
            :property-id="qpid"
            :can-navigate="false"
        />
        <div v-if="!loaded">
            <div class="loadingSpinner loadingSpinnerSearchResults" />
        </div>
        <div v-else class="masterDetails-Wrapper bootstrap-ify mdl-shadow--3dp">
            <div v-if="saving || rfcSaving" class="page-mask">
                <div class="loadingSpinnerWrapper">
                    <div class="loadingSpinnerBox">
                        <div class="loadingSpinner loadingSpinnerSearchResults" />
                    </div>
                </div>
            </div>
            <div class="container-fluid" style="padding-top:1rem;">
                <div class="row">
                    <div class="col-lg-2">
                        <property-info-panel :qpid="qpid" />
                    </div>
                    <div class="col-lg-10">
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-12">
                                    <h1
                                        class="title"
                                        style="padding-top:10px;margin-bottom: 20px;"
                                        data-cy="ruralWorksheetHeader"
                                        ref="ruralWorksheetHeader"
                                    >
                                        {{ pageTitle }}
                                        <div v-if="!worksheet.isInSubdivision" class="righty" style="margin-top:-5px;">
                                            <span>
                                                <i
                                                    data-direction="back"
                                                    class="listingButton material-icons assessmentNav"
                                                    :title="worksheetNavigationTooltip('prev')"
                                                    :class="{ disabled : !worksheet.state.prevQpid}"
                                                    @click="goToWorksheet(worksheet.state.prevQpid, isRevision ? 'Revision' : 'Current');"
                                                >
                                                    arrow_back
                                                </i>
                                                <i
                                                    data-direction="forward"
                                                    class="listingButton material-icons assessmentNav"
                                                    :title="worksheetNavigationTooltip('next')"
                                                    :class="{ disabled : !worksheet.state.nextQpid}"
                                                    @click="goToWorksheet(worksheet.state.nextQpid, isRevision ? 'Revision' : 'Current');"
                                                >
                                                    arrow_forward
                                                </i>
                                            </span>
                                        </div>
                                    </h1>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <ul id="tabsElementRuralWorksheet" class="QVHV-tabs">
                                        <li
                                            @click="goToWorksheet(qpid, 'Current')"
                                        >
                                            <span data-cy="ruralWorksheetTab1" :class="{ 'is-active' : isCurrent }">Current Worksheet</span>
                                        </li>
                                        <li
                                            v-if="shouldShowRevisionWorksheetButton"
                                            @click="goToWorksheet(qpid, 'Revision')"
                                        >
                                            <span data-cy="ruralWorksheetTab2" :class="{ 'is-active' : isRevision }">Revision Worksheet</span>
                                        </li>
                                        <li
                                            v-else-if="isInternalUser && !isCreateWorksheet"
                                            @click="createRevisionWorksheet()"
                                            class="button-link"
                                        >
                                            <span data-cy="ruralWorksheetCreateRevisionButton">Create Revision Worksheet</span>
                                        </li>
                                        <li
                                            v-if="isInternalUser && worksheet.state.hasRtvValues"
                                            @click="goToWorksheet(qpid, 'RTV')"
                                        >
                                            <span data-cy="ruralWorksheetTab3" :class="{ 'is-active' : isRtv }">RTV Worksheet</span>
                                        </li>
                                        <li
                                            v-if="displayPrintWorksheetButton"
                                            class="righty button-link"
                                        >
                                            <div style="border:0; padding: 0.8rem 1.2rem;">
                                                <input
                                                    type="button"
                                                    class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                                    value="Print"
                                                    @click="printWorksheet(ruralWorksheetType)"
                                                    data-cy="ruralWorksheetPrintButton"
                                                />
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="container-fluid space-rows QVHV-box">
                                        <template v-if="isRtv">
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <h3
                                                        class="section-title"
                                                        data-cy="ruralWorksheetRtvIndexParametersHeader"
                                                    >
                                                        RTV Index Parameters
                                                    </h3>
                                                </div>
                                            </div>
                                            <rtv-index-parameters
                                                :qpid="qpid"
                                            />
                                        </template>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <h3
                                                    class="section-title"
                                                    data-cy="ruralWorksheetPropertyDetailHeader"
                                                >
                                                    Property Detail
                                                </h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <property-detail
                                                v-if="propertyDetail"
                                                :property-detail="propertyDetail"
                                                :is-revision="isRevision"
                                                :property-grouping-types="(picklistValues && picklistValues.propertyGroupingTypes) || []"
                                                :qv-category-types="(picklistValues && picklistValues.qvCategoryTypes) || []"
                                                :quality-rating-types="(picklistValues && picklistValues.qualityRatingTypes) || []"
                                                :worksheet="worksheet"
                                                :validation-set="validationSet"
                                                :is-editable="propertyDetailIsEditable"
                                            />
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <h3 class="section-title" data-cy="ruralWorksheetImprovementsHeader">Improvements</h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12 space-rows">
                                                <improvements
                                                    :improvements="worksheet.improvementsData"
                                                    :original-improvements="tempWorksheetValues.originalImprovements"
                                                    :classifications="classifications"
                                                    :worksheet-type="ruralWorksheetType"
                                                    @update-rounded-totals="resetRoundedTotals()"
                                                />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="container-fluid">
                                                    <div class="row">
                                                        <div class="col-lg-8">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-1" style="text-align:right;">
                                                            <h3 style="font-weight:bold;line-height:39px;">Total VI</h3>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    :value="totalVI"
                                                                    format="$0,0"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetImprovementTotalVI"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <h3 class="section-title" data-cy="ruralWorksheetLandHeader">Land
                                                    <div class="righty" style="text-transform:none;">
                                                        <ul>
                                                            <li class="li-link-notClickable">Matches Land Matrix:
                                                                <span
                                                                    class="yes-no"
                                                                    :data-text="!landUseDataMatchesLandMatrix"
                                                                    data-cy="ruralWorksheetMatchesLandMatrixIndicator"
                                                                >
                                                                    {{ landUseDataMatchesLandMatrix ? "YES" : "NO" }}
                                                                </span>
                                                            </li>
                                                            <li
                                                                v-if="propertyDetail && propertyDetail.canBeEdited"
                                                                class="li-link"
                                                                @click="goToRuralPD()"
                                                            >
                                                                PD has Irrigation:
                                                                <span
                                                                    class="yes-no"
                                                                    :data-text="propertyDetailHasIrrigation"
                                                                    data-cy="ruralWorksheetHasIrrigationIndicator"
                                                                >
                                                                    {{ propertyDetailHasIrrigation ? "YES" : "NO" }}
                                                                </span>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12 space-rows">
                                                <land-use-types
                                                    :land-use-types="worksheet.landUseTypeData"
                                                    :original-land-use-types="tempWorksheetValues.originalLandUseTypes"
                                                    :classifications="classifications"
                                                    :worksheet-type="ruralWorksheetType"
                                                    @compare-to-land-matrix="compareLandMatrixToLandUseData()"
                                                    @update-rounded-totals="resetRoundedTotals()"
                                                />
                                            </div>
                                        </div>
                                        <div class="row" v-if="landSiteList.length === 0">
                                            <!-- I am here for spacing when there is nothing in the land site list -->
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12 space-rows">
                                                <div class="container-fluid">
                                                    <div class="row">
                                                        <div class="col-lg-7">
                                                            <div class="row">
                                                                <div class="col-lg-3">
                                                                    <h4 class="section-title">Site Value</h4>
                                                                </div>
                                                                <div class="col-lg-9 space-rows">
                                                                    <land-sites
                                                                        :sites="worksheet.landSiteData"
                                                                        :original-sites="tempWorksheetValues.originalLandSites"
                                                                        :classifications="classifications"
                                                                        :worksheet-type="ruralWorksheetType"
                                                                        @update-rounded-totals="resetRoundedTotals()"
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-4 space-rows" style="margin-top:-15px;">
                                                            <div v-for="i in landSiteList.length" :key="i" style="display:block;height:49px;width:100%;">
                                                                <!-- I am here to push the total fields down the page as more sites are added (bottom sticky positioning is hard) -->
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-lg-6" style="text-align:right;">
                                                                    <h3 style="font-weight:bold;line-height:39px;">Total Area</h3>
                                                                </div>
                                                                <div class="col-lg-3">
                                                                    <label>
                                                                        <input
                                                                            type="text"
                                                                            :value="totalArea"
                                                                            style="text-align:right;"
                                                                            data-cy="ruralWorksheetLandTotalArea"
                                                                            disabled
                                                                        />
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-lg-6" style="text-align:right;">
                                                                    <h3 style="font-weight:bold;line-height:39px;">Total LV</h3>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label>
                                                                        <number-input
                                                                            :value="totalLV"
                                                                            format="$0,0"
                                                                            style="text-align:right;"
                                                                            data-cy="ruralWorksheetLandTotalLV"
                                                                            disabled
                                                                        />
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-1">&nbsp;</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12 space-rows">
                                                <expandable-section
                                                    :title="worksheet.apportionmentCode == 0 ? 'LAND MATRIX' : 'LAND MATRIX FOR PARENT'"
                                                    :initial-state="landMatrixInitialState"
                                                    data-cy="ruralWorksheetLandMatrixSection"
                                                >
                                                    <template #buttons>
                                                        <li
                                                            v-if="worksheet.apportionmentCode == 0 && isCurrent"
                                                            class="li-link"
                                                            @click="recalculateLandMatrix(false)"
                                                            data-cy="ruralWorksheetLandMatrixRecalculateLandMatrixButton"
                                                        >
                                                            Recalculate Land Matrix
                                                        </li>
                                                        <li
                                                            v-if="worksheet.apportionmentCode == 0 && isCurrent"
                                                            class="li-link"
                                                            @click="copyLandMatrix(false)"
                                                            data-cy="ruralWorksheetLandMatrixCopyLandMatrixButton"
                                                        >
                                                            Copy Land Matrix
                                                        </li>
                                                    </template>

                                                    <div class="row">
                                                        <div class="col-lg-12 space-rows">
                                                            <land-matrix
                                                                :land-matrix="worksheet.landMatrix"
                                                                :worksheet-type="ruralWorksheetType"
                                                            />
                                                        </div>
                                                    </div>
                                                </expandable-section>
                                            </div>
                                        </div>
                                        <div v-if="worksheet.isMaoriLand" class="row">
                                            <div class="col-lg-12">
                                                <h3 class="section-title" data-cy="ruralWorksheetMaoriLandHeader">Maori Land</h3>
                                            </div>
                                        </div>
                                        <div v-if="worksheet.isMaoriLand" class="row">
                                            <div class="col-lg-12">
                                                <div class="container-fluid">
                                                    <div class="row table-head">
                                                        <div class="col-lg-5">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Multiple Owners
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Site Significance
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Number of Owners
                                                        </div>
                                                        <div class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                    </div>
                                                    <div class="row table-row">
                                                        <div class="col-lg-3" style="text-align:right;line-height:39px;">
                                                            Maori Land Adjustments
                                                        </div>
                                                        <div class="col-lg-2">
                                                            &nbsp;
                                                        </div>
                                                        <div v-if ="!isRevision" class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    :value="worksheet.multipleOwnerAdjustment / 100"
                                                                    format="0.00%"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetMaoriLandMultipleOwnerAdjustment"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div v-else class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    :value="worksheet.multipleOwnerAdjustmentACR / 100"
                                                                    format="0.00%"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetMaoriLandMultipleOwnerAdjustment"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div v-if ="!isRevision" class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    :value="worksheet.siteSignificanceAdjustment / 100"
                                                                    format="0.00%"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetMaoriLandSiteSignificanceAdjustment"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div v-else class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    :value="worksheet.siteSignificanceAdjustmentACR / 100"
                                                                    format="0.00%"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetMaoriLandSiteSignificanceAdjustment"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <input
                                                                    type="text"
                                                                    :value="worksheet.maoriOwnerCount"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetMaoriLandMaoriOwnerCount"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <h3 class="section-title" data-cy="ruralWorksheetWorksheetTotalsHeader">Worksheet Totals</h3>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="container-fluid">
                                                    <div class="row table-head">
                                                        <div class="col-lg-3">
                                                            &nbsp;
                                                        </div>
                                                        <div v-if="worksheet.isMaoriLand && isRevision" class="col-lg-1">
                                                            Lump Sum
                                                        </div>
                                                        <div v-else class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                        <div v-if="worksheet.isMaoriLand" class="col-lg-1">
                                                            Adjustment
                                                        </div>
                                                        <div v-else class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Capital Value
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Land Value
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Value of Improvements
                                                        </div>
                                                        <div class="col-lg-1">
                                                            Trees
                                                        </div>
                                                    </div>
                                                    <div class="row table-row">
                                                        <div class="col-lg-3" style='text-align:right;line-height:39px;'>
                                                            Worksheet Values
                                                        </div>
                                                        <div class="col-lg-2">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    :value="totalCV"
                                                                    format="$0,0"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesTotalCV"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    :value="totalLV"
                                                                    format="$0,0"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesTotalLV"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    :value="totalVI"
                                                                    format="$0,0"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesTotalVI"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label>
                                                                <number-input
                                                                    :value="worksheet.treesValue"
                                                                    format="$0,0"
                                                                    style="text-align:right;"
                                                                    step="1000"
                                                                    min="0"
                                                                    @blur="($event) => worksheet.treesValue = toNumber($event.srcElement.value)"
                                                                    data-cy="ruralWorksheetWorksheetValuesTV"
                                                                    :disabled="isRtv"
                                                                />
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row table-row">
                                                        <div class="col-lg-3" style='text-align:right;line-height:39px;'>
                                                            Worksheet Values Rounded
                                                        </div>
                                                        <div class="col-lg-2">
                                                            &nbsp;
                                                        </div>
                                                        <template v-if="isCurrent && !worksheet.isMaoriLand">
                                                            <div class="col-lg-2">
                                                                <label>
                                                                    <number-input
                                                                        format="$0,0"
                                                                        :value="tempWorksheetValues.roundedCV"
                                                                        style="text-align:right;"
                                                                        step="1000"
                                                                        min="0"
                                                                        @blur="($event) =>  updateRoundedValues('CV', toNumber($event.srcElement.value))"
                                                                        data-cy="ruralWorksheetWorksheetValuesRoundedCV"
                                                                    />
                                                                </label>
                                                            </div>
                                                            <div class="col-lg-2">
                                                                <label>
                                                                    <number-input
                                                                        format="$0,0"
                                                                        :value="tempWorksheetValues.roundedLV"
                                                                        style="text-align:right;"
                                                                        step="1000"
                                                                        min="0"
                                                                        @blur="($event) =>  updateRoundedValues('LV', toNumber($event.srcElement.value))"
                                                                        data-cy="ruralWorksheetWorksheetValuesRoundedLV"
                                                                    />
                                                                </label>
                                                            </div>
                                                            <div class="col-lg-2">
                                                                <label>
                                                                    <number-input
                                                                        format="$0,0"
                                                                        :value="tempWorksheetValues.roundedVI"
                                                                        style="text-align:right;"
                                                                        data-cy="ruralWorksheetWorksheetValuesRoundedVI"
                                                                        disabled
                                                                    />
                                                                </label>
                                                            </div>
                                                        </template>
                                                        <template v-else>
                                                            <div class="col-lg-2">
                                                                <label>
                                                                    <number-input
                                                                        format="$0,0"
                                                                        :value="roundedTotalCV"
                                                                        style="text-align:right;"
                                                                        data-cy="ruralWorksheetWorksheetValuesRoundedCV"
                                                                        disabled
                                                                    />
                                                                </label>
                                                            </div>
                                                            <div class="col-lg-2">
                                                                <label>
                                                                    <number-input
                                                                        format="$0,0"
                                                                        :value="roundedTotalLV"
                                                                        style="text-align:right;"
                                                                        data-cy="ruralWorksheetWorksheetValuesRoundedLV"
                                                                        disabled
                                                                    />
                                                                </label>
                                                            </div>
                                                            <div class="col-lg-2">
                                                                <label>
                                                                    <number-input
                                                                        format="$0,0"
                                                                        :value="roundedTotalVI"
                                                                        style="text-align:right;"
                                                                        data-cy="ruralWorksheetWorksheetValuesRoundedVI"
                                                                        disabled
                                                                    />
                                                                </label>
                                                            </div>
                                                        </template>
                                                        <div class="col-lg-1">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="roundedTrees"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesRoundedTV"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div v-if="worksheet.isMaoriLand" class="row table-row">
                                                        <div class="col-lg-3" style='text-align:right;line-height:39px;'>
                                                            Worksheet Values Adjusted
                                                        </div>
                                                        <div v-if="isRevision" class="col-lg-1">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="worksheet.maoriLandLumpSum"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetMaoriLandLumpSum"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div v-else class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label>
                                                                <number-input
                                                                    format="0.00%"
                                                                    :value="totalAdjustment"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesAdjustment"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="adjustedCV"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesAdjustedCV"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="adjustedLV"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesAdjustedLV"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="adjustedVI"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesAdjustedVI"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                    </div>
                                                    <div v-if="worksheet.isMaoriLand" class="row table-row">
                                                        <div class="col-lg-3" style='text-align:right;font-weight:bold;line-height:39px;'>
                                                            Adjusted Values Rounded
                                                        </div>
                                                        <div class="col-lg-2">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="tempWorksheetValues.adjustedCV"
                                                                    style="text-align:right;"
                                                                    step="1000"
                                                                    min="0"
                                                                    @blur="($event) =>  updateAdjustedValues('CV', toNumber($event.srcElement.value))"
                                                                    data-cy="ruralWorksheetWorksheetValuesAdjustedRoundedCV"
                                                                    :disabled="isRtv"
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="tempWorksheetValues.adjustedLV"
                                                                    style="text-align:right;"
                                                                    step="1000"
                                                                    min="0"
                                                                    @blur="($event) => updateAdjustedValues('LV', toNumber($event.srcElement.value))"
                                                                    data-cy="ruralWorksheetWorksheetValuesAdjustedRoundedLV"
                                                                    :disabled="isRtv"
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="tempWorksheetValues.adjustedVI"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetValuesAdjustedRoundedVI"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                    </div>
                                                    <div class="row table-row">
                                                        <div class="col-lg-3" style='text-align:right;font-weight:bold;line-height:39px;'>
                                                            Worksheet Ratios
                                                        </div>
                                                        <div class="col-lg-2" style='text-align:right;line-height:39px;'>
                                                            $/Ha:
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="ratioCvHa"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetRatiosCvHa"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="ratioLvHa"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetRatiosLvHa"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2" style='text-align:right;line-height:39px;'>
                                                            Total Land Area (Ha):
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label>
                                                                <input
                                                                    type="text"
                                                                    :value="totalArea"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetTotalsLandArea"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row table-row" style="display:none;">
                                                        <!-- I am here to cause row coloring to be the same for the rows above and below me -->
                                                    </div>
                                                    <div class="row table-row">
                                                        <div class="col-lg-3">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-2" style='text-align:right;line-height:39px;'>
                                                            $/Production:
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="ratioCvProduction"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetTotalsCvProduction"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label>
                                                                <number-input
                                                                    format="$0,0"
                                                                    :value="ratioLvProduction"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetTotalsLvProduction"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-2" style='text-align:right;line-height:39px;'>
                                                            Production:
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label>
                                                                <input
                                                                    type="text"
                                                                    :value="worksheet.production"
                                                                    style="text-align:right;"
                                                                    data-cy="ruralWorksheetWorksheetTotalsProduction"
                                                                    disabled
                                                                />
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row table-row">
                                                        <div class="col-lg-3" style='text-align:right;line-height:39px;'>
                                                            Worksheet Comment:
                                                        </div>
                                                        <div class="col-lg-9">
                                                            <label>
                                                                <textarea
                                                                    style="height:100px;"
                                                                    v-model="worksheet.comment"
                                                                    maxlength="1000"
                                                                    data-cy="ruralWorksheetComment"
                                                                    :disabled="!isCurrent"
                                                                >
                                                                </textarea>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="tempWorksheetValues.maoriLandValuesChangeWarning" class="row">
                                            <div class="col-lg-12 ">
                                                <div class="message-warning">
                                                    Caution: Maori land details have changed since this worksheet was last saved.
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Revision worksheet values -->
                                        <template v-if="!isCurrent">
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <h3 class="section-title" data-cy="ruralWorksheetRevisionValuesHeader">Adopted Worksheet Values</h3>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <div class="container-fluid">
                                                        <div class="row table-head">
                                                            <div class="col-lg-5">
                                                                Description
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ ruralWorksheetType }} Capital Value
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ ruralWorksheetType }} Land Value
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ ruralWorksheetType }} Value of Improvements
                                                            </div>
                                                            <div v-if="!isRtv" class="col-lg-1" style="white-space:nowrap;">
                                                                {{ ruralWorksheetType }} Trees
                                                            </div>
                                                            <div v-else class="col-lg-1">
                                                                &nbsp;
                                                            </div>
                                                        </div>
                                                        <template v-if="worksheet.isMaoriLand">
                                                            <div class="row table-row">
                                                                <div class="col-lg-2" style="font-weight:bold;line-height:39px;">
                                                                    {{ valRef }}
                                                                </div>
                                                                <div class="col-lg-3" style="line-height:39px;text-align:right;">
                                                                    Unadjusted
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="roundedTotalCV"
                                                                            style="text-align:right;"
                                                                            tabindex="-1"
                                                                            disabled
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueUnadjustedCV"
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="roundedTotalLV"
                                                                            style="text-align:right;"
                                                                            tabindex="-1"
                                                                            disabled
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueUnadjustedLV"
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="roundedTotalVI"
                                                                            style="text-align:right;"
                                                                            tabindex="-1"
                                                                            disabled
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueUnadjustedVI"
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-1">
                                                                    <label>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="roundedTrees"
                                                                            style="text-align:right;"
                                                                            tabindex="-1"
                                                                            disabled
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueUnadjustedTV"
                                                                        />
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row table-row" style="display:none;">
                                                                <!-- I am here to cause row coloring to be the same for the rows above and below me -->
                                                            </div>
                                                            <div class="row table-row">
                                                                <div class="col-lg-2">
                                                                    &nbsp;
                                                                </div>
                                                                <div class="col-lg-3" style="font-weight:bold;line-height:39px;text-align:right;">
                                                                    Adjusted
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <span
                                                                            class="change-label"
                                                                            :class="{ 'positive': percentageChangeCV > 0, 'negative' : percentageChangeCV < 0 }"
                                                                        >
                                                                            {{ formatPercentage(percentageChangeCV) }}
                                                                        </span>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="tempWorksheetValues.adjustedCV"
                                                                            style="text-align:right;"
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueAdjustedCV"
                                                                            disabled
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <span
                                                                            class="change-label"
                                                                            :class="{ 'positive': percentageChangeLV > 0, 'negative' : percentageChangeLV < 0 }"
                                                                        >
                                                                            {{ formatPercentage(percentageChangeLV) }}
                                                                        </span>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="tempWorksheetValues.adjustedLV"
                                                                            style="text-align:right;"
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueAdjustedLV"
                                                                            disabled
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <span
                                                                            class="change-label"
                                                                            :class="{ 'positive': percentageChangeVI > 0, 'negative' : percentageChangeVI < 0 }"
                                                                        >
                                                                            {{ formatPercentage(percentageChangeVI) }}
                                                                        </span>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="tempWorksheetValues.adjustedVI"
                                                                            style="text-align:right;"
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueAdjustedVI"
                                                                            disabled
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-1">
                                                                    &nbsp;
                                                                </div>
                                                            </div>
                                                        </template>
                                                        <template v-else>
                                                            <div class="row table-row">
                                                                <div class="col-lg-2">
                                                                    &nbsp;
                                                                </div>
                                                                <div class="col-lg-3" style="font-weight:bold;line-height:39px;text-align:right;">
                                                                    Adjusted
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <span
                                                                            class="change-label"
                                                                            :class="{ 'positive': percentageChangeCV > 0, 'negative' : percentageChangeCV < 0 }"
                                                                        >
                                                                            {{ formatPercentage(percentageChangeCV) }}
                                                                        </span>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="tempWorksheetValues.roundedCV"
                                                                            style="text-align:right;"
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueAdjustedCV"
                                                                            disabled
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <span
                                                                            class="change-label"
                                                                            :class="{ 'positive': percentageChangeLV > 0, 'negative' : percentageChangeLV < 0 }"
                                                                        >
                                                                            {{ formatPercentage(percentageChangeLV) }}
                                                                        </span>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="tempWorksheetValues.roundedLV"
                                                                            style="text-align:right;"
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueAdjustedLV"
                                                                            disabled
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    <label>
                                                                        <span
                                                                            class="change-label"
                                                                            :class="{ 'positive': percentageChangeVI > 0, 'negative' : percentageChangeVI < 0 }"
                                                                        >
                                                                            {{ formatPercentage(percentageChangeVI) }}
                                                                        </span>
                                                                        <number-input
                                                                            format="$0,0"
                                                                            :value="tempWorksheetValues.roundedVI"
                                                                            style="text-align:right;"
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueAdjustedVI"
                                                                            disabled
                                                                        />
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-1">
                                                                    <label>
                                                                        <number-input
                                                                            v-if="!isRtv"
                                                                            format="$0,0"
                                                                            :value="roundedTrees"
                                                                            style="text-align:right;"
                                                                            tabindex="-1"
                                                                            disabled
                                                                            data-cy="ruralWorksheetRevisionTotalAssessedValueUnadjustedTV"
                                                                        />
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                            <template v-if="worksheet.state.hasRevision">
                                                <div class="row grey">
                                                    <div class="col-lg-12">
                                                        <h3 class="section-title" data-cy="ruralWorksheetRevisionTotalAssessedValueHeader">Revision Values</h3>
                                                    </div>
                                                </div>
                                                <div v-if="worksheet.isMaoriLand" class="row grey">
                                                    <div class="col-lg-12">
                                                        <div class="container-fluid">
                                                            <div class="row table-head">
                                                                <div class="col-lg-5">
                                                                    Description
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    Revision Capital Value
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    Revision Land Value
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    Revision Value of Improvements
                                                                </div>
                                                                <div class="col-lg-1" style="white-space:nowrap;">
                                                                    Revision Trees
                                                                </div>
                                                            </div>
                                                            <template v-for="child of worksheet.children" >
                                                                <div class="row table-row summary" :key="'revisionUnadjustedChild'+child.qpid">
                                                                    <div class="col-lg-2" style="text-align:left;">
                                                                        {{ child.valRef }}
                                                                    </div>
                                                                    <div class="col-lg-3">
                                                                        Unadjusted
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionUnadjustedCV) }}
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionUnadjustedLV) }}
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionUnadjustedVI) }}
                                                                    </div>
                                                                    <div class="col-lg-1">
                                                                        {{ formatCurrency(child.revisionTrees) }}
                                                                    </div>
                                                                </div>
                                                                <div class="row table-row" style="display:none;" :key="'revisionChildSpacer'+child.qpid">
                                                                    <!-- I am here to cause row coloring to be the same for the rows above and below me -->
                                                                </div>
                                                                <div class="row table-row summary" :class="{'bold':child.valRef.replace(' / ','/') === property.valuationReference }" :key="'revisionAdjustedChild'+child.qpid">
                                                                    <div class="col-lg-2">
                                                                        &nbsp;
                                                                    </div>
                                                                    <div class="col-lg-3">
                                                                        Adjusted
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionCV) }}
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionLV) }}
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionVI) }}
                                                                    </div>
                                                                    <div class="col-lg-1">
                                                                        &nbsp;
                                                                    </div>
                                                                </div>
                                                            </template>
                                                            <div v-if="worksheet.children.length > 0" class="row" style="border-bottom:1px solid #5e5e5e">
                                                                &nbsp;
                                                            </div>
                                                            <div class="row table-row summary">
                                                                <div class="col-lg-2" style="font-weight:bold;text-align:left;">
                                                                    {{ worksheet.parent.valRef }}
                                                                </div>
                                                                <div class="col-lg-3">
                                                                    Unadjusted
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionUnadjustedCV) }}
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionUnadjustedLV) }}
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionUnadjustedVI) }}
                                                                </div>
                                                                <div class="col-lg-1">
                                                                    {{ formatCurrency(worksheet.parent.revisionTrees) }}
                                                                </div>
                                                            </div>
                                                            <div class="row table-row" style="display:none;">
                                                                <!-- I am here to cause row coloring to be the same for the rows above and below me -->
                                                            </div>
                                                            <div class="row table-row summary bold">
                                                                <div class="col-lg-2">
                                                                    &nbsp;
                                                                </div>
                                                                <div class="col-lg-3">
                                                                    Adjusted
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionCV) }}
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionLV) }}
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionCV - worksheet.parent.revisionLV) }}
                                                                </div>
                                                                <div class="col-lg-1">
                                                                    &nbsp;
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-else class="row grey">
                                                    <div class="col-lg-12">
                                                        <div class="container-fluid">
                                                            <div class="row table-head">
                                                                <div class="col-lg-5">
                                                                    Description
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    Revision Capital Value
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    Revision Land Value
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    Revision Value of Improvements
                                                                </div>
                                                                <div class="col-lg-1" style="white-space:nowrap;">
                                                                    Revision Trees
                                                                </div>
                                                            </div>
                                                            <template v-for="child of worksheet.children" >
                                                                <div class="row table-row summary" :class="{'bold':child.valRef.replace(' / ','/') === property.valuationReference }" :key="'child'+child.qpid">
                                                                    <div class="col-lg-2">
                                                                    {{ child.valRef }}
                                                                    </div>
                                                                    <div class="col-lg-3">
                                                                        &nbsp;
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionCV) }}
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionLV) }}
                                                                    </div>
                                                                    <div class="col-lg-2">
                                                                        {{ formatCurrency(child.revisionVI) }}
                                                                    </div>
                                                                    <div class="col-lg-1">
                                                                        {{ formatCurrency(child.revisionTrees) }}
                                                                    </div>
                                                                </div>
                                                            </template>
                                                            <div v-if="worksheet.children.length > 0" class="row" style="border-bottom:1px solid #5e5e5e">
                                                                &nbsp;
                                                            </div>
                                                            <div class="row table-row summary">
                                                                <div class="col-lg-5" style="font-weight:bold;">
                                                                    {{ worksheet.parent.valRef }}
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionCV) }}
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionLV) }}
                                                                </div>
                                                                <div class="col-lg-2">
                                                                    {{ formatCurrency(worksheet.parent.revisionVI) }}
                                                                </div>
                                                                <div class="col-lg-1">
                                                                    {{ formatCurrency(worksheet.parent.revisionTrees) }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </template>
                                        <!-- Existing assessment values -->
                                        <div class="row" :class="{'grey': !isCurrent}">
                                            <div class="col-lg-12">
                                                <h3 class="section-title" data-cy="ruralWorksheetCurrentValuesHeader">Current Assessment Values</h3>
                                            </div>
                                        </div>
                                        <div v-if="worksheet.isMaoriLand" class="row" :class="{'grey': !isCurrent}">
                                            <div class="col-lg-12">
                                                <div class="container-fluid">
                                                    <div class="row table-head">
                                                        <div class="col-lg-5">
                                                            Description
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Capital Value
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Land Value
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Value of Improvements
                                                        </div>
                                                        <div class="col-lg-1">
                                                            Trees
                                                        </div>
                                                    </div>
                                                    <template v-for="child of worksheet.children" >
                                                        <div class="row table-row summary" :key="'unadjustedChild'+child.qpid">
                                                            <div class="col-lg-2" style="text-align:left;">
                                                                {{ child.valRef }}
                                                            </div>
                                                            <div class="col-lg-3">
                                                                Unadjusted
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.unadjustedCV) }}
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.unadjustedLV) }}
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.unadjustedVI) }}
                                                            </div>
                                                            <div class="col-lg-1">
                                                                {{ formatCurrency(child.trees) }}
                                                            </div>
                                                        </div>
                                                        <div class="row table-row" style="display:none;" :key="'childSpacer'+child.qpid">
                                                            <!-- I am here to cause row coloring to be the same for the rows above and below me -->
                                                        </div>
                                                        <div class="row table-row summary bold" :key="'adjustedChild'+child.qpid">
                                                            <div class="col-lg-2">
                                                                &nbsp;
                                                            </div>
                                                            <div class="col-lg-3">
                                                                Adjusted
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.CV) }}
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.LV) }}
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.VI) }}
                                                            </div>
                                                            <div class="col-lg-1">
                                                                &nbsp;
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <div v-if="worksheet.children.length > 0" class="row" style="border-bottom:1px solid #5e5e5e">
                                                        &nbsp;
                                                    </div>
                                                    <div class="row table-row summary">
                                                        <div class="col-lg-2" style="text-align:left;">
                                                            {{ worksheet.parent.valRef }}
                                                        </div>
                                                        <div class="col-lg-3">
                                                            Unadjusted
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.unadjustedCV) }}
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.unadjustedLV) }}
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.unadjustedVI) }}
                                                        </div>
                                                        <div class="col-lg-1">
                                                            {{ formatCurrency(worksheet.parent.trees) }}
                                                        </div>
                                                    </div>
                                                    <div class="row table-row" style="display:none;">
                                                        <!-- I am here to cause row coloring to be the same for the rows above and below me -->
                                                    </div>
                                                    <div class="row table-row summary bold">
                                                        <div class="col-lg-2">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-3">
                                                            Adjusted
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.CV) }}
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.LV) }}
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.CV - worksheet.parent.LV) }}
                                                        </div>
                                                        <div class="col-lg-1">
                                                            &nbsp;
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="row grey">
                                            <div class="col-lg-12">
                                                <div class="container-fluid">
                                                    <div class="row table-head">
                                                        <div class="col-lg-5">
                                                            Description
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Capital Value
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Land Value
                                                        </div>
                                                        <div class="col-lg-2">
                                                            Value of Improvements
                                                        </div>
                                                        <div class="col-lg-1">
                                                            Trees
                                                        </div>
                                                    </div>
                                                    <template v-for="child of worksheet.children" >
                                                        <div class="row table-row summary" :class="{'bold':child.valRef.replace(' / ','/') === property.valuationReference }" :key="'adjustedChild'+child.qpid">
                                                            <div class="col-lg-2">
                                                                {{ child.valRef }}
                                                            </div>
                                                            <div class="col-lg-3">
                                                                &nbsp;
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.CV) }}
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.LV) }}
                                                            </div>
                                                            <div class="col-lg-2">
                                                                {{ formatCurrency(child.VI) }}
                                                            </div>
                                                            <div class="col-lg-1">
                                                                {{ formatCurrency(child.trees) }}
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <div v-if="worksheet.children.length > 0" class="row" style="border-bottom:1px solid #5e5e5e">
                                                        &nbsp;
                                                    </div>
                                                    <div class="row table-row summary">
                                                        <div class="col-lg-2" style="font-weight:bold;text-align:left;">
                                                            {{ worksheet.parent.valRef }}
                                                        </div>
                                                        <div class="col-lg-3">
                                                            &nbsp;
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.CV) }}
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.LV) }}
                                                        </div>
                                                        <div class="col-lg-2">
                                                            {{ formatCurrency(worksheet.parent.VI) }}
                                                        </div>
                                                        <div class="col-lg-1">
                                                            {{ formatCurrency(worksheet.parent.trees) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Reason For Change -->
                                        <template v-if="isInternalUser && isCurrent">
                                            <div class="row">
                                                <div class="col-lg-12">
                                                    <h3 class="section-title" data-cy="ruralWorksheetReasonForChangeHeader">Reason for Change</h3>
                                                </div>
                                            </div>
                                            <div v-if="reasonForChange" class="row" :class="{ invalid: reasonForChange.invalid }">
                                                <div class="col-lg-2">
                                                    <label>
                                                        <span class="label">&nbsp;</span>
                                                    </label>
                                                    <span style="font-weight:bold;line-height:39px;">Update Assessment</span>
                                                </div>
                                                <div class="col-lg-1">
                                                    &nbsp;
                                                </div>
                                                <div class="col-lg-3">
                                                    <label>
                                                        <span class="label">Output code</span>
                                                        <select
                                                            v-model.number="reasonForChange.outputId"
                                                            @change="reasonForChangeValid(false)"
                                                            data-cy="ruralWorksheetReasonForChangeOutput">
                                                            <option :value="null"></option>
                                                            <option v-for="option in classifications.reasonOutputs" :key="option.id" :value="option.id">
                                                                {{ option.description }}
                                                            </option>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="col-lg-2">
                                                    <label>
                                                        <span class="label">Source</span>
                                                        <select
                                                            v-model.number="reasonForChange.sourceId"
                                                            @change="reasonForChangeValid(false)"
                                                            data-cy="ruralWorksheetReasonForChangeSource">
                                                            <option :value="null"></option>
                                                            <option v-for="option in classifications.reasonSources" :key="option.id" :value="option.id">
                                                                {{ option.description }}
                                                            </option>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="col-lg-4">
                                                    <label>
                                                        <span class="label">Reason for Change</span>
                                                        <input
                                                            type="text"
                                                            v-model="reasonForChange.reason"
                                                            @blur="reasonForChangeValid(false)"
                                                            data-cy="ruralWorksheetReasonForChangeReason"
                                                        />
                                                    </label>
                                                </div>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            <div v-if="!isRtv" class="row" style="padding:10px;">
                                <div class="col-lg-6">
                                    <input
                                        v-if="isInternalUser && !isRevision && !isCreateWorksheet"
                                        type="button"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored-red"
                                        value="Delete Worksheet"
                                        @click="deleteWorksheet(false)"
                                        data-cy="ruralWorksheetDeleteButton"
                                    />
                                    <input
                                        type="button"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect"
                                        value="Cancel Changes"
                                        @click="cancelChanges(false)"
                                        data-cy="ruralWorksheetCancelButton"
                                    />
                                </div>
                                <div class="col-lg-6" style="text-align:right;">
                                    <input
                                        v-if="isInternalUser && !isRevision"
                                        type="button"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                        value="Update Assessment"
                                        @click="updateAssessment(false, false, false)"
                                        data-cy="ruralWorksheetUpdateAssessmentButton"
                                    />
                                    <input
                                        v-if="isInternalUser && !isRevision && !worksheet.isInSubdivision"
                                        type="button"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                        value="Update Worksheet Only"
                                        @click="updateWorksheet(false)"
                                        data-cy="ruralWorksheetUpdateWorksheetButton"
                                    />
                                    <input
                                        v-if="isInternalUser && isRevision"
                                        type="button"
                                        class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect mdl-button--colored"
                                        value="Update"
                                        @click="updateRevisionWorksheet(false)"
                                        data-cy="ruralWorksheetUpdateRevisionButton"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='caution'"
            :warning="modal.mode==='warning'"
        >
            <h1 data-cy="ruralWorksheetModalHeading">{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
                data-cy="ruralWorksheetModalMessage"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div
                        class="message message-error"
                        :class="{ 'message-error': modal.mode==='warning', 'message-warning': modal.mode==='caution' }"
                        data-cy="ruralWorksheetModalListBox"
                    >
                        <ul>
                            <li v-for="msg in modal.messages" :key="msg" data-cy="ruralWorksheetModalListItem"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <input
                id="modalResponseCode"
                type="hidden"
                data-cy="ruralWorksheetModalResponseCode"
                :value="modal.code"
            />
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                        data-cy="ruralWorksheetModalCancelButton"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                        data-cy="ruralWorksheetModalContinueButton"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
        </alert-modal>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import numeral from 'numeral';
import axios from '../../../utils/AxiosHeaders';
import commonUtils from "../../../utils/CommonUtils";
import {openBase64DataInNewTab} from '@/utils/QivsUtils';

export default {
    components: {
        'property-summary': () => import('../../property/PropertySummary.vue'),
        'property-info-panel': () => import('../../property/PropertyInfo.vue'),
        'expandable-section': () => import(/* webpackChunkName: "RuralWorksheet" */'../../common/ExpandableSectionWrapper.vue'),
        'property-detail': () => import(/* webpackChunkName: "RuralWorksheet" */'./sections/PropertyDetail.vue'),
        'improvements': () => import(/* webpackChunkName: "RuralWorksheet" */'./sections/Improvements.vue'),
        'land-use-types': () => import(/* webpackChunkName: "RuralWorksheet" */'./sections/LandUseTypes.vue'),
        'land-matrix': () => import(/* webpackChunkName: "RuralWorksheet" */'./sections/LandMatrix.vue'),
        'land-sites': () => import(/* webpackChunkName: "RuralWorksheet" */'./sections/LandSites.vue'),
        'rtv-index-parameters': () => import(/* webpackChunkName: "RuralWorksheet" */'./sections/RtvIndexParameters.vue'),
        'number-input': () => import(/* webpackChunkName: "RuralWorksheet" */'../../common/form/NumberInput.vue'),
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../../common/modal/AlertModal.vue'),
    },
    data() {
        return {
            revisionWorksheetNeedsUpdating: false,
            landUseDataMatchesLandMatrix: false,
            canPrintWorksheet: false,
            landMatrixInitialState: "expanded",
            modal: {
                mode: 'warning',
                isOpen: false,
                heading: 'heading',
                message: '',
                messages: [],
                cancelText: 'No',
                cancelAction: () => { },
                confirmText: 'Yes',
                confirmAction: () => { },
                code: '',
            },
            tempWorksheetValues: {
                roundedCV: 0,
                roundedLV: 0,
                roundedVI: 0,
                adjustedCV: 0,
                adjustedLV: 0,
                adjustedVI: 0,
                originalProduction: 0,
                originalTreesValue: 0,
                originalImprovements: [],
                originalLandUseTypes: [],
                originalLandSites: [],
                maoriLandValuesChangeWarning: false,
            }
        }
    },
    mixins: [commonUtils],
    computed: {
        ...mapState('ruralWorksheet', {
           worksheet: 'worksheet',
           roundingTables: 'roundingTables',
           classifications: 'lists',
           loading: 'loading',
           saving: 'saving',
           errors: 'errors',
           warnings: 'warnings',
           pdfFileStream: 'pdfFileStream'
        }),
        ...mapState('reasonForChange', {
           reasonForChange: 'reasonForChange',
           rfcSaving: 'saving',
           rfcLoading: 'loading',
           rfcErrors: 'errors',
        }),
        ...mapState('userData', {
            userLoaded: 'loaded',
            userData: 'userData',
            userName: 'userName',
            userId: 'userId',
            qivsUrl: 'qivsUrl',
            isInternalUser: 'isInternalUser',
            isExternalUser: 'isExternalUser',
            isReadOnlyUser: 'isReadOnlyUser',
        }),
        ...mapState('property', ['property', 'properties']),
        ...mapState('propertyDraft', {
            propertyDetail: 'propertyDetail',
            draftLoading: 'loading',
            draftSaving: 'saving',
            draftException: 'exception',
            validationSet: 'validationSet',
            formIsStale: 'formIsStale',
            picklistValues: 'picklistValues',
        }),
        loaded() {
            return !this.loading
                    && !this.rfcLoading
                    && this.userLoaded
                    && this.worksheet
                    && this.worksheet.state
                    && !this.draftLoading;
        },
        qpid() {
            return this.$route.params.id ? '' + this.$route.params.id : '';
        },
        shouldShowRevisionWorksheetButton() {
            const isRevision = this.worksheet.state.hasRevision || this.isRevision;
            const canAccess = this.isInternalUser || (this.isExternalUser && this.isReadOnlyUser === false);
            return isRevision && canAccess;
        },
        isCreateWorksheet() {
            return this.worksheet.state ? this.worksheet.state.isNew : false;
        },
        isCurrent() {
            if (!this.worksheet || !this.worksheet.state) {
                return false;
            }
            return !this.worksheet.state.isRevisionWorksheet && !this.worksheet.state.isRtvWorksheet
        },
        isRevision() {
            if (!this.worksheet || !this.worksheet.state) {
                return false;
            }
            return this.worksheet.state.isRevisionWorksheet;
        },
        displayPrintWorksheetButton() {
            return this.canPrintWorksheet;
        },
        isRtv() {
            if (!this.worksheet || !this.worksheet.state) {
                return false;
            }
            return this.worksheet.state.isRtvWorksheet;
        },
        ruralWorksheetType() {
            if (this.isRtv) {
                return 'RTV';
            }
            if (this.isRevision) {
                return 'Revision';
            }
            return 'Current';
        },
        pageTitle() {
            if (this.isCreateWorksheet) {
                return 'Create Rural Worksheet';
            }
            if (this.isRtv) {
                return 'Rural RTV Worksheet';
            }
            if (this.isRevision) {
                return 'Update Rural Worksheet - Revision Values';
            }
            return 'Update Rural Worksheet';
        },
        activeTabClass() {
            if (this.isRtv) {
                return 'QVHVTab-3';
            }
            if (this.isRevision) {
                return 'QVHVTab-2';
            }
            return 'QVHVTab-1';
        },
        propertyDetailHasIrrigation() {
            return this.propertyDetail
                    && this.propertyDetail.ruralDetail
                    && this.propertyDetail.ruralDetail.irrigationTypeConsents.length > 0;
        },
        improvementList() {
            return this.worksheet.improvementsData.filter(obj => obj.state !== 'deleted');
        },
        landUseTypeList() {
            return this.worksheet.landUseTypeData.filter(obj => obj.state !== 'deleted');
        },
        landSiteList() {
            return this.worksheet.landSiteData.filter(obj => obj.state !== 'deleted');
        },
        totalArea() {
            let landUseTotal = 0;
            let landSiteTotal = 0;
            if (this.worksheet) {
                if (this.landUseTypeList.length > 0){
                    landUseTotal = this.landUseTypeList.map(item => item.itemSize).reduce((prev, next) => prev + next);
                }
                if (this.landSiteList.length > 0) {
                    landSiteTotal = this.landSiteList.map(item => item.siteArea).reduce((prev, next) => prev + next);
                }
            }
            let result = Math.round((landUseTotal + landSiteTotal) * 10000) / 10000;
            return result;
        },
        totalLV() {
            let landUseTotal = 0;
            let landSiteTotal = 0;
            if (this.worksheet) {
                if (this.landUseTypeList.length > 0) {
                    landUseTotal = this.landUseTypeList.map(item => item.landValue).reduce((prev, next) => prev + next);
                }
                if (this.landSiteList.length > 0) {
                    landSiteTotal = this.landSiteList.map(item => item.improvementValue).reduce((prev, next) => prev + next);
                }
            }
            return landUseTotal + landSiteTotal;
        },
        totalVI() {
            if (this.worksheet && this.improvementList.length > 0) {
                return this.improvementList.map(imp => imp.improvementValue).reduce((prev, next) => prev + next);
            }
            return 0;
        },
        totalCV() {
            return this.totalLV + this.totalVI;
        },
        roundedTotalCV() {
            if (this.roundValue(this.totalCV) - this.roundValue(this.totalLV) < this.totalVI) {
                return this.roundValue(this.totalLV) + this.roundValue(this.totalVI);
            }
            return this.roundValue(this.totalCV);
        },
        roundedTotalLV() {
            return this.roundValue(this.totalLV);
        },
        roundedTotalVI() {
            if (this.roundValue(this.totalCV) - this.roundValue(this.totalLV) < this.totalVI) {
                return this.roundValue(this.totalVI);
            }
            return this.roundValue(this.totalCV) - this.roundValue(this.totalLV);
        },
        roundedTrees() {
            return this.roundValue(this.worksheet.treesValue);
        },
        totalAdjustment() {
            if (this.worksheet.isMaoriLand) {
                return (this.worksheet.multipleOwnerAdjustment + this.worksheet.siteSignificanceAdjustment) / 100
            }
            return 0;
        },
        adjustedCV() {
            if (this.worksheet.isMaoriLand) {
                return this.adjustedLV + this.adjustedVI;
            }
            return this.roundedTotalCV;
        },
        adjustedLV() {
            if (this.worksheet.isMaoriLand) {
                return this.roundedTotalLV * (1.00 - this.totalAdjustment);
            }
            return this.roundedTotalLV;
        },
        adjustedVI() {
            if (this.worksheet.isMaoriLand) {
                return this.roundedTotalVI * (1.00 - this.totalAdjustment);
            }
            return this.roundedTotalVI;
        },
        adjustedRoundedCV() {
            if (this.worksheet.isMaoriLand) {
                return this.adjustedRoundedLV + this.adjustedRoundedVI;
            }
            return this.roundedTotalCV;
        },
        adjustedRoundedLV() {
            if (this.worksheet.isMaoriLand) {
                return this.roundMaoriLandValue(this.adjustedLV);
            }
            return this.roundedTotalLV;
        },
        adjustedRoundedVI() {
            if (this.worksheet.isMaoriLand) {
                return this.roundMaoriLandValue(this.adjustedVI);
            }
            return this.roundedTotalVI;
        },
        ratioCvHa() {
            if (this.worksheet.masterDetailsArea) {
                return Math.round(this.totalCV / this.worksheet.masterDetailsArea);
            }
            return 0;
        },
        ratioLvHa() {
            if (this.worksheet.masterDetailsArea) {
                return Math.round(this.totalLV / this.worksheet.masterDetailsArea);
            }
            return 0;
        },
        ratioCvProduction() {
            if (this.worksheet.production) {
                return Math.round(this.totalCV / this.worksheet.production);
            }
            return null;
        },
        ratioLvProduction() {
            if (this.worksheet.production) {
                return Math.round(this.totalLV / this.worksheet.production);
            }
            return null;
        },
        certificateTabEnabled() {
            return selectedLinzSearchTab === 'certificateTab'
        },
        calculatedValues() {
            let calcVI = this.roundedTotalVI;
            let calcLV = this.roundedTotalLV;
            let calcCV = this.roundedTotalCV;
            let mlCV = 0;
            let mlLV = 0;
            let mlVI = 0;
            const roundedTrees = this.roundedTrees;
            const area = this.totalArea;

            if(this.isCurrent && !this.worksheet.isMaoriLand) {
                calcCV = this.tempWorksheetValues.roundedCV;
                calcLV = this.tempWorksheetValues.roundedLV;
                calcVI = this.tempWorksheetValues.roundedVI;
            }
            if(this.worksheet.isMaoriLand) {
                mlCV = this.tempWorksheetValues.adjustedCV;
                mlLV = this.tempWorksheetValues.adjustedLV;
                mlVI = this.tempWorksheetValues.adjustedVI;
            }

            return {
                cv: calcCV,
                lv: calcLV,
                vi: calcVI,
                maoriLandCV: mlCV,
                maoriLandLV: mlLV,
                maoriLandVI: mlVI,
                roundedTrees: roundedTrees,
                area: area,
            }
        },
        valRef() {
            if (this.worksheet.children.length > 0){
                const child = this.worksheet.children.filter(item => item.qpid == this.worksheet.qpid ? item : null);
                if (child && child.length > 0) {
                    return child[0].valRef;
                }
            }
            return this.worksheet.parent.valRef;
        },
        comparisonValues() {
            let comparisonValues = this.worksheet.parent;
            if (this.worksheet.children.length > 0){
                const child = this.worksheet.children.filter(item => item.qpid == this.worksheet.qpid ? item : null);
                if (child && child.length > 0) {
                    comparisonValues = child[0];
                }
            }
            return comparisonValues;
        },
        percentageChangeCV() {
            if (this.worksheet.state.isCreateRevision) {
                return 0;
            }
            if (this.worksheet.isMaoriLand) {
                return this.tempWorksheetValues.adjustedCV / this.comparisonValues.CV - 1;
            }
            return this.roundedTotalCV / this.comparisonValues.CV - 1;
        },
        percentageChangeLV() {
            if (this.worksheet.state.isCreateRevision) {
                return 0;
            }
            if (this.worksheet.isMaoriLand) {
                return this.tempWorksheetValues.adjustedLV / this.comparisonValues.LV - 1;
            }
            return this.roundedTotalLV / this.comparisonValues.LV - 1;
        },
        percentageChangeVI() {
            if (this.worksheet.state.isCreateRevision) {
                return 0;
            }
            if (this.worksheet.isMaoriLand) {
                return (this.tempWorksheetValues.adjustedCV - this.tempWorksheetValues.adjustedLV) / this.comparisonValues.VI - 1;
            }
            return this.roundedTotalVI / this.comparisonValues.VI - 1;
        },
        updateRevisionMessage() {
            return this.worksheet.state.hasRevision ? '\n\nRevision worksheet must be updated.' : '';
        },
        propertyDetailIsEditable() {
            if (!this.propertyDetail) {
                return false;
            }
            if (this.propertyDetail.canBeEdited === null || !this.propertyDetail.canBeEdited) {
                return false;
            }
            if (this.isRtv) {
                return false;
            }
            const restrictedApportionmentCodes = ["1","3","7"];
            return !restrictedApportionmentCodes.includes(this.worksheet.apportionmentCode);
        }
    },
    methods: {
        resetTempWorksheetValues() {
            this.tempWorksheetValues = {
                roundedCV: 0,
                roundedLV: 0,
                adjustedCV: 0,
                adjustedLV: 0,
                originalProduction: 0,
                originalTreesValue: 0,
                originalImprovements: [],
                originalLandUseTypes: [],
                originalLandSites: [],
                maoriLandValuesChangeWarning: false
            }
        },
        async getPickListValues() {
            try {
                await this.$store.dispatch('propertyDraft/getPickListValues');
            } catch (error) {
                this.handleException(error);
            }
        },
        async loadWorksheet(createRevision) {
            try {
                this.resetTempWorksheetValues();
                this.revisionWorksheetNeedsUpdating = false;
                this.canPrintWorksheet = false;
                if(createRevision) {
                    await this.$store.dispatch('ruralWorksheet/createRevisionWorksheet', this.qpid);
                    this.landMatrixInitialState = "collapsed";
                }
                else if(this.$route.name === 'rural-revision-worksheet') {
                    await this.$store.dispatch('ruralWorksheet/getRevisionWorksheet', this.qpid);
                    this.landMatrixInitialState = "collapsed";
                    this.canPrintWorksheet = true;
                }
                else if(this.$route.name === 'rural-rtv-worksheet') {
                    await this.$store.dispatch('ruralWorksheet/getRtvWorksheet', this.qpid);
                    this.landMatrixInitialState = "collapsed";
                }
                else {
                    await this.$store.dispatch('ruralWorksheet/getCurrentWorksheet', this.qpid);
                    this.landMatrixInitialState = "expanded";
                    this.canPrintWorksheet = true;
                }
                // set temporary working variables
                this.tempWorksheetValues.roundedCV = this.worksheet.parent.adjustedCV;
                this.tempWorksheetValues.roundedLV = this.worksheet.parent.adjustedLV;
                this.tempWorksheetValues.roundedVI = this.worksheet.parent.adjustedCV - this.worksheet.parent.adjustedLV;
                this.tempWorksheetValues.adjustedCV = this.worksheet.parent.adjustedCV;
                this.tempWorksheetValues.adjustedLV = this.worksheet.parent.adjustedLV;
                this.tempWorksheetValues.adjustedVI = this.worksheet.parent.adjustedCV - this.worksheet.parent.adjustedLV;
                this.tempWorksheetValues.originalProduction = this.worksheet.production;
                this.tempWorksheetValues.originalTreesValue = this.worksheet.treesValue;
                this.tempWorksheetValues.originalImprovements = JSON.parse(JSON.stringify(this.worksheet.improvementsData));
                this.tempWorksheetValues.originalLandUseTypes = JSON.parse(JSON.stringify(this.worksheet.landUseTypeData));
                this.tempWorksheetValues.originalLandSites = JSON.parse(JSON.stringify(this.worksheet.landSiteData));

                if (!this.worksheet.state.hasRtvValues && this.$route.name === 'rural-rtv-worksheet') {
                    this.worksheet.state = null;
                    this.setModal({
                        mode: 'warning',
                        isOpen: true,
                        heading: 'RTV values do not exist for this property.',
                        message: 'Click OK to go to the current Rural Worksheet',
                        messages: [],
                        cancelText: null,
                        cancelAction: () => { },
                        confirmText: 'OK',
                        confirmAction: () => {
                            this.$router.push({ name: 'rural-worksheet',  params: { id: this.qpid }});
                            setTimeout(async () => await this.loadWorksheet(), 1000); /* wait a second for route name to update */
                        },
                        code: '',
                    });
                    return false;
                }

                if (this.isRtv) {
                    this.resetRoundedTotals();
                }

                if (this.isCreateWorksheet && !this.isInternalUser) {
                    this.worksheet.state = null;
                    this.setModal({
                        mode: 'warning',
                        isOpen: true,
                        heading: 'Invalid action.',
                        message: 'You do not have valid permissions to create a rural worksheet.',
                        messages: [],
                        cancelText: null,
                        cancelAction: () => { },
                        confirmText: 'OK',
                        confirmAction: async () => { await this.goToPropertySummary(); },
                        code: '',
                    });
                    return false;
                }

                if (this.errors && this.errors.length > 0) {
                    if (this.errors.filter(e => e.error === 'NoRevisionWorksheet').length > 0) {
                        this.worksheet.state = null;
                        this.setModal({
                            mode: 'warning',
                            isOpen: true,
                            heading: 'Revision worksheet does not exist for this property.',
                            message: 'Click OK to go to the current Rural Worksheet',
                            messages: [],
                            cancelText: null,
                            cancelAction: () => { },
                            confirmText: 'OK',
                            confirmAction: () => {
                                this.$router.push({ name: 'rural-worksheet',  params: { id: this.qpid }});
                                setTimeout(async () => await this.loadWorksheet(), 1000); /* wait a second for route name to update */
                            },
                            code: '',
                        });
                        return false;
                    }
                    else {
                        if(this.isCreateWorksheet){
                            this.worksheet.state = null;
                            this.setModal({
                                mode: 'warning',
                                isOpen: true,
                                heading: 'Oops',
                                message: '',
                                messages: this.errors.map(item => item.message),
                                cancelText: null,
                                cancelAction: () => { },
                                confirmText: 'OK',
                                confirmAction: async () => { await this.goToPropertySummary(); },
                                code: 'CannotCreateWorksheet',
                            });
                            return false;
                        }
                        if(createRevision){
                            this.setModal({
                                mode: 'warning',
                                isOpen: true,
                                heading: 'Oops',
                                message: '',
                                messages: this.errors.map(item => item.message),
                                cancelText: null,
                                cancelAction: () => { },
                                confirmText: 'OK',
                                confirmAction: async () => { await this.loadWorksheet(); },
                                code: 'NoRevisionWorksheet',
                            });
                            return false;
                        }
                    }
                }

                await this.loadReasonForChange();
                await this.loadPropertyDetail();

                if (this.warnings && this.warnings.length > 0) {
                    if (this.warnings.filter(e => e.warning === 'MaoriLandValuesChanged').length > 0) {
                        this.setModal({
                            mode: 'caution',
                            isOpen: true,
                            heading: 'Maori Land Information has changed',
                            message: 'Maori land details have changed since this worksheet was last saved.  Click the OK button to update this worksheet with the current Maori land details.',
                            messages: [],
                            cancelText: 'Cancel',
                            cancelAction: () => { this.updateMaoriLandValues(false); },
                            confirmText: 'OK',
                            confirmAction: () => { this.updateMaoriLandValues(true); },
                            code: 'Warning',
                        });
                        return false;
                    }
                    else {
                        this.setModal({
                            mode: 'caution',
                            isOpen: true,
                            heading: 'Oops',
                            message: '',
                            messages: this.warnings.map(item => item.message),
                            cancelText: 'No, Return to Worksheet',
                            cancelAction: () => { },
                            confirmText: 'Yes, Update Worksheet',
                            confirmAction: () => { },
                            code: 'Warning',
                        });
                        return false;
                    }
                }
            }
            catch (err) {
                console.error(err);
            }
        },
        async loadReasonForChange() {
            try {
                if (this.userLoaded) {
                    await this.$store.dispatch('reasonForChange/getExistingReasonForChange', { qpid: this.qpid, userName: this.userName });
                }
            }
            catch (err) {
                console.error(err);
            }
        },
        async loadUserData() {
            try {
                await this.$store.dispatch('userData/fetchUserData');
            } catch (err) {
                console.error(err);
            }
        },
        async createRevisionWorksheet() {
            await this.loadWorksheet(true);
        },
        async goToWorksheet(qpid, type) {
            if (qpid) {
                let routeName = 'rural-worksheet'
                if (type === 'Revision') {
                    routeName = 'rural-revision-worksheet';
                }
                if (type === 'RTV') {
                    routeName = 'rural-rtv-worksheet';
                }

                this.worksheet.state = null;
                this.$router.push({ name: routeName,  params: { id: qpid }});
                setTimeout(async () => await this.loadWorksheet(), 1000);
            }
        },
        async loadPropertyDetail() {
            if (this.draftLoading) return;
            if (!this.property) return;
            if (!this.worksheet) return;
            if (!this.worksheet.parent) return;

            let propertyId = this.property.id;
            if (this.worksheet.parent.qpid && this.property.qupid != this.worksheet.parent.qpid) {
                try {
                    const parent = this.properties.find(o => !o.suffix);
                    propertyId = parent ? parent.id : null;

                    if (!propertyId) {
                        const response = await axios({
                            method: 'get',
                            url: jsRoutes.controllers.PropertyMasterData.getProperty(this.worksheet.parent.qpid).url,
                        });

                        const parentPropertyData = response.data;
                        propertyId = parentPropertyData.property && parentPropertyData.property.id ? parentPropertyData.property.id : null;
                    }
                } catch (err) {
                    this.handleException(err);
                }
            }

            if (propertyId) {
                try {
                    if (this.isInternalUser) {
                        await this.$store.dispatch('propertyDraft/editCurrentPropertyDetail',
                            propertyId);
                    } else {
                        await this.$store.dispatch('propertyDraft/getReadOnlyCurrentPropertyDetail',
                            propertyId)
                    }
                } catch (err) {
                    this.handleException(err);
                }
            }
        },
        goToPropertySummary() {
            this.$router.push({ name: 'property',  params: { qpid: this.qpid }});
        },
        goToRuralPD() {
            const targetRoute = this.$router.resolve({name: 'rural-property-detail-edit', params: { id: this.propertyDetail.propertyId }});
            window.open(targetRoute.href, '_blank');
        },
        goToSRAUpdatePage() {
            this.$router.push({ name: 'property-sra-values',  params: { qpid: this.qpid }});
        },
        formatPercentage(value){
            return numeral(Math.abs(value)).format('0.0%');
        },
        formatCurrency(value){
            return numeral(value).format('$0,0');
        },
        worksheetNavigationTooltip(direction) {
            if (!this.worksheet) {
                return '';
            }
            if(!this.worksheet.state.nextQpid && !this.worksheet.state.prevQpid) {
                return 'This is the only worksheet on this roll.'
            }
            if(direction === 'next') {
                return this.worksheet.state.nextQpid ? 'Next Worksheet: ' + this.worksheet.state.nextQpid : 'This is the last worksheet on this roll.';
            }
            if(direction === 'prev') {
                return this.worksheet.state.prevQpid ? 'Previous Worksheet: ' + this.worksheet.state.prevQpid : 'This is the first worksheet on this roll.';
            }
            return '';
        },
        toNumber(text) {
            const n = numeral(text).value();
            return Number.isNaN(n) ? null : n;
        },
        resetRoundedTotals() {
            this.updateRoundedValues('CV', 0);
            this.updateRoundedValues('LV', 0);
            this.updateAdjustedValues('CV', 0);
            this.updateAdjustedValues('LV', 0);
        },
        updateRoundedValues(valType, newValue) {
            if (valType === 'CV') {
                this.tempWorksheetValues.roundedCV = newValue > 0 ? newValue : this.roundedTotalCV;
            }
            if (valType === 'LV') {
                this.tempWorksheetValues.roundedLV = newValue > 0 ? newValue : this.roundedTotalLV;
            }
            this.tempWorksheetValues.roundedVI = this.tempWorksheetValues.roundedCV - this.tempWorksheetValues.roundedLV;
        },
        updateAdjustedValues(valType, newValue) {
            if (valType === 'CV') {
                this.tempWorksheetValues.adjustedCV = newValue > 0 ? newValue : this.adjustedRoundedCV;
            }
            if (valType === 'LV') {
                this.tempWorksheetValues.adjustedLV = newValue > 0 ? newValue : this.adjustedRoundedLV;
            }
            this.tempWorksheetValues.adjustedVI = this.tempWorksheetValues.adjustedCV - this.tempWorksheetValues.adjustedLV;
        },
        roundValue(val) {
            val = val || 0;
            if (!this.roundingTables || !this.roundingTables.roundingTable)
                return val;

            const roundSetting = this.roundingTables.roundingTable.filter(item => item.value_from <= val && item.value_to >= val ? true : false);

            if (roundSetting.length === 1 && roundSetting[0].rounding_amount) {
                return Math.round(val / roundSetting[0].rounding_amount) * roundSetting[0].rounding_amount;
            }
            return 0;
        },
        roundMaoriLandValue(val) {
            val = val || 0;
            if (!this.roundingTables || !this.roundingTables.maoriLandRoundingTable) {
                return val;
            }

            const roundSetting = this.roundingTables.maoriLandRoundingTable.filter(item => item.value_from <= val && item.value_to >= val ? true : false);

            if (roundSetting.length === 1 && roundSetting[0].rounding_amount) {
                return Math.floor(val / roundSetting[0].rounding_amount) * roundSetting[0].rounding_amount;
            }

            return 0;
        },
        async recalculateLandMatrix(confirmed) {
            if (!confirmed) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Do you want to proceed?',
                    message: 'You will lose any changes you have made in the Land Matrix section.',
                    messages: [],
                    cancelText: 'No, Return to worksheet',
                    cancelAction: () => { },
                    confirmText: 'OK',
                    confirmAction: () => { this.recalculateLandMatrix(true) },
                    code: 'RecalculateLandMatrixWarning',
                });
                return false;
            }

            let worksheetLandValue = this.worksheet.landUseTypeData.filter(obj => obj.state !== 'deleted').reduce((accumulatedTotal, item) => accumulatedTotal + item.landValue, 0);
            worksheetLandValue += this.worksheet.landSiteData.filter(obj => obj.state !== 'deleted').reduce((accumulatedTotal, item) => accumulatedTotal + item.improvementValue, 0);
            await this.$store.dispatch('ruralWorksheet/recalculateLandMatrix', { qpid: this.worksheet.parent.qpid || this.qpid, worksheetLandValue: worksheetLandValue, apportionmentCode: this.worksheet.apportionmentCode });

            this.compareLandMatrixToLandUseData();
        },
        copyLandMatrix(confirmed) {
            if (!confirmed) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Do you want to proceed?',
                    message: 'Land rows will be deleted and be replaced by Land Matrix rows. Please check total value of Land Matrix section equals existing worksheet land value. If there is a site value you will need to manually remove the area and value from one of the land rows.',
                    messages: [],
                    cancelText: 'No, Return to worksheet',
                    cancelAction: () => { },
                    confirmText: 'OK',
                    confirmAction: () => { this.copyLandMatrix(true) },
                    code: 'CopyLandMatrixWarning',
                });
                return false;
            }

            this.worksheet.landUseTypeData.map((landUseType) => {
                landUseType.state = 'deleted';
                return landUseType
            });

            this.worksheet.landMatrix.map((landMatrixItem) => {
                const minRowId = Math.min.apply(Math, this.worksheet.landUseTypeData.map(o => o.rowId));
                this.worksheet.landUseTypeData.push({
                    rowId: minRowId > 0 ? -1 : minRowId - 1,
                    itemDescription: landMatrixItem.coverName,
                    ruralUseId: landMatrixItem.qvCoverId,
                    ruralContourId: landMatrixItem.qvContourId,
                    irrigationTypeId: null,
                    itemSize: landMatrixItem.itemSize,
                    rate: landMatrixItem.estimatedRate,
                    landValue: Math.round(landMatrixItem.estimatedValue),
                    state: 'new'
                });
            });

            this.resetRoundedTotals();
            this.compareLandMatrixToLandUseData();
        },
        compareLandMatrixToLandUseData() {
            let landUseList = this.worksheet.landUseTypeData.filter(item =>
                item.state !== 'deleted'
                && (
                    item.itemSize > 0
                    || item.ruralContourId !== ''
                    || item.ruralUseId !== ''
                )
            );
            if (!this.worksheet.landMatrix || this.worksheet.landMatrix.length === 0){
                this.landUseDataMatchesLandMatrix = false;
                return;
            }
            if (landUseList.length !== this.worksheet.landMatrix.length) {
                this.landUseDataMatchesLandMatrix = false;
                return;
            }

            let allMatch = true;
            this.worksheet.landMatrix.map((landMatrixItem) => {
                const landUseMatch = landUseList.find(item =>
                    item.ruralUseId === landMatrixItem.qvCoverId
                    && item.ruralContourId === landMatrixItem.qvContourId
                    && item.itemSize === landMatrixItem.itemSize
                );

                if (landUseMatch) {
                    landUseList = landUseList.filter(item => item.rowId !== landUseMatch.rowId);
                }
                else {
                    allMatch = false;
                }
            });

            this.landUseDataMatchesLandMatrix = allMatch;
        },
        finaliseWorksheetValues() {
            //update the object with new calculated values
            this.worksheet['calculatedValues'] = this.calculatedValues;
        },
        changedValuesCheck() {
            let changedValues = [];
            // if production has been modified, only Update Assessment may be executed.
            if (this.worksheet.production !== this.tempWorksheetValues.originalProduction) {
                changedValues.push('Production');
            }
            if (this.worksheet.treesValue !== this.tempWorksheetValues.originalTreesValue) {
                changedValues.push('Trees');
            }
            if (changedValues.length > 0) {
                const changedStr = changedValues.join(' & ');
                const pluralized = changedValues.length > 1 ? 'values' : 'value'

                let msg = `It looks like you have modified the ${changedStr} ${pluralized} - the ${changedStr} ${pluralized} can only be modified from this worksheet when the \'Update Assessment\' button is clicked.\n\n`;
                msg += `If you need to update the ${changedStr} without changing the assessment, please use the update assessment screen.`;

                this.setModal({
                    mode: 'warning',
                    isOpen: true,
                    heading: 'Oops',
                    message: msg,
                    messages: [],
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'Return to worksheet',
                    confirmAction: async () => { await this.loadWorksheet(); },
                    code: 'AssessmentValuesCannotBeUpdatedError',
                });
                return false;
            }
            return true;
        },
        balanceChangedCheck() {
            if (this.isRevision) {
                return true;
            }
            const invalidOutputCodes = [1,6,7];
            if (!invalidOutputCodes.includes(this.reasonForChange.outputId)) {
                return true;
            }
            if (this.isMaoriLand){
                return (this.worksheet.calculatedValues.maoriLandLV === this.worksheet.landValue
                        && this.worksheet.calculatedValues.lv === this.worksheet.unadjustedLandValue);
            }
            return this.worksheet.calculatedValues.lv === this.worksheet.landValue;
        },
        areaValidate() {
            // if worksheet area does not match property area, warn the user before proceeding.
            this.modal.messages = [];
            if (this.totalArea !== this.worksheet.worksheetArea || this.totalArea !== this.worksheet.masterDetailsArea) {
                if (this.totalArea !== this.worksheet.worksheetArea) {
                    this.modal.messages.push('Total Land Area does not match Worksheet Area');
                }
                if (this.totalArea !== this.worksheet.masterDetailsArea) {
                    this.modal.messages.push('Total Land Area does not match assessment Land Area');
                }
                return false;
            }
            return true;
        },
        worksheetRowsHaveChanged() {
            if (this.isRevision) {
                return false;
            }
            if (!this.worksheet.state.hasRevision) {
                return false;
            }
            if (this.worksheet.improvementsData.filter(obj => obj.state === 'deleted' && obj.rowId > 0).length > 0) {
                // existing improvement deleted
                return true;
            }
            if (this.worksheet.improvementsData.filter(obj => obj.state !== 'deleted' && obj.rowId < 0).length > 0) {
                // new improvement added
                return true;
            }
            if (this.worksheet.landUseTypeData.filter(obj => obj.state === 'deleted' && obj.rowId > 0).length > 0) {
                // existing land use deleted
                return true;
            }
            if (this.worksheet.landUseTypeData.filter(obj => obj.state !== 'deleted' && obj.rowId < 0).length > 0) {
                // new land use added
                return true;
            }
            if (this.worksheet.landSiteData.filter(obj => obj.state === 'deleted' && obj.rowId > 0).length > 0) {
                // existing land site deleted
                return true;
            }
            if (this.worksheet.landSiteData.filter(obj => obj.state !== 'deleted' && obj.rowId < 0).length > 0) {
                // new land site added
                return true;
            }
            return false;
        },
        reasonForChangeValid(doValidate) {
            if (!this.reasonForChange) {
                return false;
            }
            if(this.reasonForChange.invalid !== null || doValidate) {
                this.reasonForChange.invalid = (this.reasonForChange.sourceId === -1
                                                || this.reasonForChange.outputId === -1
                                                || this.reasonForChange.reason === ''
                                                || this.reasonForChange.reason === null);
            }
        },
        updateMaoriLandValues(accept) {
            let ws = this.worksheet;
            // logic translated from QIVS
            ws.isMaoriLand = ws.isMaoriLandAC;
            if(ws.state.isNew) {
                ws.multipleOwnerAdjustment = ws.multipleOwnerAdjustmentAC;
                ws.siteSignificanceAdjustment = ws.siteSignificanceAdjustmentAC;
            }
            else if(!ws.state.isRevisionWorksheet && !ws.state.isCreateRevision) {
                if (accept) {
                    if (ws.multipleOwnerAdjustment !== 0) {
                        ws.multipleOwnerAdjustment = ws.multipleOwnerAdjustmentAC;
                        ws.siteSignificanceAdjustment = ws.siteSignificanceAdjustmentAC;
                    }
                    else if (ws.multipleOwnerAdjustmentAC !== 0) {
                        ws.multipleOwnerAdjustment = ws.multipleOwnerAdjustmentAC;
                        ws.siteSignificanceAdjustment = ws.siteSignificanceAdjustmentAC;
                    }
                }
            }
            else if(ws.state.isRevisionWorksheet && !ws.state.isCreateRevision) {
                if(accept) {
                    if (ws.multipleOwnerAdjustment !== 0 || ws.multipleOwnerAdjustmentACR !== 0) {
                        ws.multipleOwnerAdjustment = ws.multipleOwnerAdjustmentACR;
                        ws.siteSignificanceAdjustment = ws.siteSignificanceAdjustmentACR;
                    }
                    else {
                        ws.multipleOwnerAdjustment = ws.multipleOwnerAdjustmentAC;
                        ws.siteSignificanceAdjustment = ws.siteSignificanceAdjustmentAC;
                    }
                }
            }
            this.tempWorksheetValues.maoriLandValuesChangeWarning = (!accept);
            this.updateAdjustedValues('CV', 0);
            this.updateAdjustedValues('LV', 0);
        },
        async saveReasonForChange() {
            try {
                await this.$store.dispatch('reasonForChange/addReasonForChange', this.reasonForChange);
            } catch (err) {
                console.error(err);
            }
        },
        cancelChanges(confirmed) {
            if (!confirmed) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Are you sure?',
                    message: 'You will lose all your current changes.',
                    messages: [],
                    cancelText: 'No, Return to worksheet',
                    cancelAction: () => { },
                    confirmText: 'Yes, Discard my Changes',
                    confirmAction: () => { this.cancelChanges(true); },
                    code: 'CancelChangesWarning',
                });
                return false;
            }

            this.goToPropertySummary();
        },
        async updateWorksheet(areaValidated) {
            if (!this.changedValuesCheck()) {
                return false;
            }
            if (!areaValidated && !this.areaValidate()) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Do you want to proceed?',
                    message: '',
                    messages: this.modal.messages,
                    cancelText: 'No, Return to worksheet',
                    cancelAction: () => { },
                    confirmText: 'Yes, Update worksheet',
                    confirmAction: () => { this.updateWorksheet(true, false); },
                    code: 'AreaMismatchWarning',
                });
                return false;
            }
            this.revisionWorksheetNeedsUpdating = this.worksheetRowsHaveChanged();
            this.finaliseWorksheetValues();

            try{
                await this.$store.dispatch('ruralWorksheet/updateWorksheet', { worksheet: this.worksheet, userName: this.userName, applyWorksheet: true });
            } catch (err) {
                return this.handleException(err);
            }

            if (this.errors && this.errors.length > 0) {
                this.setModal({
                    mode: 'warning',
                    isOpen: true,
                    heading: 'Oops',
                    message: 'The following validation errors have occurred:',
                    messages: this.errors.map(item => item.message),
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'OK',
                    confirmAction: () => { },
                    code: 'ValidationError',
                });
                return false;
            }
            if (this.revisionWorksheetNeedsUpdating) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Success - Revision worksheet must be updated',
                    message: `The worksheet was successfully updated. ${this.updateRevisionMessage}`,
                    messages: [],
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'OK',
                    confirmAction: () => { this.goToWorksheet(this.qpid, 'Revision'); },
                    code: 'RevisionWorksheetUpdateRequired',
                });
                return false;
            }

            //successfully updated the worksheet
            this.setModal({
                mode: 'success',
                isOpen: true,
                heading: 'Success',
                message: 'The worksheet was successfully updated.',
                messages: [],
                cancelText: null,
                cancelAction: () => { },
                confirmText: 'OK',
                confirmAction: () => { },
                code: 'SuccessMessage',
            });

            this.tempWorksheetValues.roundedCV = this.worksheet.parent.adjustedCV;
            this.tempWorksheetValues.roundedLV = this.worksheet.parent.adjustedLV;
            this.tempWorksheetValues.adjustedCV = this.worksheet.parent.adjustedCV;
            this.tempWorksheetValues.adjustedLV = this.worksheet.parent.adjustedLV;
            this.tempWorksheetValues.originalProduction = this.worksheet.production;
            this.tempWorksheetValues.originalTreesValue = this.worksheet.treesValue;
            this.tempWorksheetValues.originalImprovements = JSON.parse(JSON.stringify(this.worksheet.improvementsData));
            this.tempWorksheetValues.originalLandUseTypes = JSON.parse(JSON.stringify(this.worksheet.landUseTypeData));
            this.tempWorksheetValues.originalLandSites = JSON.parse(JSON.stringify(this.worksheet.landSiteData));
        },
        async deleteWorksheet(deleteConfirmed) {
            if (!deleteConfirmed) {
                let msg = 'The current rural worksheet for this assessment will be deleted. Are you sure?\n\n';
                let confirmText = 'Yes, Delete Worksheet';
                if (this.worksheet.state.hasRevision || this.worksheet.state.hasRtvValues){
                    msg = 'The following worksheets for this assessment will be deleted. Are you sure?\n\n'
                    msg += ' - Current rural worksheet\n';
                    if (this.worksheet.state.hasRevision) {
                        msg += ' - Revision rural worksheet\n';
                    }
                    if (this.worksheet.state.hasRtvValues) {
                        msg += ' - RTV rural worksheet\n';
                    }
                    confirmText += 's';
                }

                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Do you want to proceed?',
                    message: msg,
                    messages: [],
                    cancelText: 'No, Return to Worksheet',
                    cancelAction: () => { },
                    confirmText: confirmText,
                    confirmAction: () => { this.deleteWorksheet(true); },
                    code: 'DeleteWorksheetWarning',
                });
                return false;
            }

            try {
                await this.$store.dispatch('ruralWorksheet/deleteWorksheet', { qpid: this.qpid });
            } catch (err) {
                return this.handleException(err);
            }

            this.goToPropertySummary();
        },
        async printWorksheet(ruralWorksheetType) {
            try {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Rural Worksheet Report',
                    message: 'Generating the PDF...',
                    messages: []
                });
                await this.$store.dispatch('ruralWorksheet/generateRuralWorksheetPdfReport', {qpid: this.qpid, type: ruralWorksheetType});
                if (this.pdfFileStream) {
                    openBase64DataInNewTab(this.pdfFileStream, 'application/pdf');
                }

                this.setModal({
                    mode: 'success',
                    isOpen: true,
                    heading: 'Success',
                    message: 'The PDF was successfully generated.',
                    messages: [],
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'OK',
                    confirmAction: () => { },
                    code: 'SuccessMessage',
                });
            } catch (err) {
                return this.handleException(err);
            }
        },
        async updateAssessment(areaValidated, valuesConfirmed, balanceChangeConfirmed) {
            this.reasonForChangeValid(true);
            if (this.reasonForChange.invalid) {
                // doing a front-end pre-check of reason for change before the worksheet update occurs
                this.setModal({
                    mode: 'warning',
                    isOpen: true,
                    heading: 'Oops',
                    message: '',
                    messages: [ 'Please complete the Reason for Change.' ],
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'Return to worksheet',
                    confirmAction: () => { },
                    code: 'ReasonForChangeInvalid',
                });
                return false;
            }
            if (!areaValidated && !this.areaValidate()) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Do you want to proceed?',
                    message: '',
                    messages: this.modal.messages,
                    cancelText: 'No, Return to worksheet',
                    cancelAction: () => { },
                    confirmText: 'Yes, Update assessment',
                    confirmAction: () => { this.updateAssessment(true, false, false); },
                    code: 'AreaMismatchWarning',
                });
                return false;
            }
            if (!valuesConfirmed) {
                let msg = 'New Assessment values will be\n\n'
                if (this.worksheet.isMaoriLand) {
                    msg += ' - CV: ' + this.$options.filters.numeral(this.calculatedValues.maoriLandCV, '$0,0') + '\n';
                    msg += ' - LV: ' + this.$options.filters.numeral(this.calculatedValues.maoriLandLV, '$0,0') + '\n';
                    msg += ' - VI: ' + this.$options.filters.numeral(this.calculatedValues.maoriLandVI, '$0,0') + '\n';
                } else {
                    msg += ' - CV: ' + this.$options.filters.numeral(this.calculatedValues.cv, '$0,0') + '\n';
                    msg += ' - LV: ' + this.$options.filters.numeral(this.calculatedValues.lv, '$0,0') + '\n';
                    msg += ' - VI: ' + this.$options.filters.numeral(this.calculatedValues.vi, '$0,0') + '\n';
                }
                if (this.worksheet.isMaoriLand && this.worksheet.apportionmentCode !== '5' && this.worksheet.apportionmentCode !== '6') {
                    msg += '\nHave you deducted the Maori land lump sum and applied the $100 minimum value rule?';
                }
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Do you want to proceed?',
                    message: msg,
                    messages: [],
                    cancelText: 'No, Return to Worksheet',
                    cancelAction: () => { },
                    confirmText: 'Accept new values',
                    confirmAction: () => { this.updateAssessment(true, true, false); },
                    code: 'NewAssessmentValuesConfirmation',
                });
                return false;
            }

            this.revisionWorksheetNeedsUpdating = this.worksheetRowsHaveChanged();

            this.finaliseWorksheetValues();
            if (!balanceChangeConfirmed && !this.balanceChangedCheck()) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Do you want to proceed?',
                    message: 'Worksheet land value is different from land value on assessment.',
                    messages: [],
                    cancelText: 'No, Return to Worksheet',
                    cancelAction: () => { },
                    confirmText: 'Yes, Update assessment',
                    confirmAction: () => { this.updateAssessment(true, true, true); },
                    code: 'BalanceChangedWarning',
                });
                return false;
            }

            if (this.propertyDetailIsEditable) {
                try {
                    await this.$store.dispatch('propertyDraft/saveCurrentPropertyDetail', true);
                    if (!this.validationSet.success) {
                        this.setModal({
                            mode: 'warning',
                            isOpen: true,
                            heading: 'Oops',
                            message: 'There was a problem saving changes to the Property Detail.',
                            messages: this.validationSet.errors && this.validationSet.errors.length
                                    ? this.validationSet.errors.map(item => item.message)
                                    : [],
                            cancelText: null,
                            cancelAction: () => { },
                            confirmText: 'Return to worksheet',
                            confirmAction: () => { },
                            code: 'PropertyDetailSaveError',
                        });
                        return false;
                    }
                } catch (err) {
                    return this.handleException(err);
                }
            }

            try{
                await this.$store.dispatch('ruralWorksheet/updateWorksheet', { worksheet: this.worksheet, userName: this.userName, applyWorksheet: false });
            } catch (err) {
                return this.handleException(err);
            }

            if (this.errors && this.errors.length > 0) {
                this.setModal({
                    mode: 'warning',
                    isOpen: true,
                    heading: 'Oops',
                    message: 'The following validation errors have occurred:',
                    messages: this.errors.map(item => item.message),
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'Return to worksheet',
                    confirmAction: () => { },
                    code: 'ValidationError',
                });
                return false;
            }

            await this.saveReasonForChange();
            if (this.rfcErrors && this.rfcErrors.length > 0) {
                this.setModal({
                    mode: 'warning',
                    isOpen: true,
                    heading: 'Oops',
                    message: 'The following validation errors have occurred:',
                    messages: this.rfcErrors.map(item => item.message),
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'Return to worksheet',
                    confirmAction: () => { },
                    code: 'ReasonForChangeError',
                });
                return false;
            }
            this.worksheet['reasonForChangeId'] = this.reasonForChange.reasonId;

            this.finaliseWorksheetValues();
            try{
                await this.$store.dispatch('ruralWorksheet/updateAssessment', { worksheet: this.worksheet, userName: this.userName });
            } catch (err) {
                return this.handleException(err);
            }

            if (this.errors && this.errors.length > 0) {
                this.setModal({
                    mode: 'warning',
                    isOpen: true,
                    heading: 'Oops',
                    message: 'The following validation errors have occurred:',
                    messages: this.errors.map(item => item.message),
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'Return to worksheet',
                    confirmAction: () => { },
                    code: 'ValidationError',
                });
                return false;
            }

            if (this.warnings && this.warnings.length > 0) {
                if (this.warnings.filter(e => e.warning === 'SRAValuesChanging').length > 0) {

                    this.setModal({
                        mode: 'caution',
                        isOpen: true,
                        heading: 'Success - SRA values need updating',
                        message: `The assessment was successfully updated, but the SRA values need to be adjusted.${this.updateRevisionMessage}`,
                        messages: [],
                        cancelText: null,
                        cancelAction: () => { },
                        confirmText: 'Go to SRA values',
                        confirmAction: () => {
                            this.goToSRAUpdatePage();
                        },
                        code: 'SRAValuesWarning',
                    });
                    return false;
                }
                else {
                    this.setModal({
                        mode: 'caution',
                        isOpen: true,
                        heading: 'Oops',
                        message: 'The following validation errors have occurred:',
                        messages: this.warnings.map(item => item.message),
                        cancelText: null,
                        cancelAction: () => { },
                        confirmText: 'Return to worksheet',
                        confirmAction: () => { },
                        code: 'Warning',
                    });
                    return false;
                }
            }

            if (this.revisionWorksheetNeedsUpdating) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Success - Revision worksheet must be updated',
                    message: `The assessment was successfully updated.${this.updateRevisionMessage}`,
                    messages: [],
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'OK',
                    confirmAction: () => { this.goToWorksheet(this.qpid, 'Revision'); },
                    code: 'RevisionWorksheetUpdateRequired',
                });
                return false;
            }

            //successfully updated the worksheet
            this.setModal({
                mode: 'success',
                isOpen: true,
                heading: 'Success',
                message: 'The assessment was successfully updated.',
                messages: [],
                cancelText: null,
                cancelAction: () => { },
                confirmText: 'OK',
                confirmAction: () => { this.goToPropertySummary(); },
                code: 'SuccessMessage',
            });
            return false;
        },
        async updateRevisionWorksheet(valuesConfirmed){
            if (!valuesConfirmed) {
                let msg = 'New Assessment values will be\n\n'
                msg += ' - CV: ' + this.$options.filters.numeral(this.calculatedValues.cv, '$0,0') + '\n';
                msg += ' - LV: ' + this.$options.filters.numeral(this.calculatedValues.lv, '$0,0') + '\n';
                msg += ' - VI: ' + this.$options.filters.numeral(this.calculatedValues.vi, '$0,0') + '\n';
                if (this.worksheet.isMaoriLand && this.worksheet.apportionmentCode !== '5' && this.worksheet.apportionmentCode !== '6') {
                    msg += '\nHave you deducted the Maori land lump sum and applied the $100 minimum value rule?\n\n';
                }

                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Do you want to proceed?',
                    message: msg,
                    messages: [],
                    cancelText: 'No, Return to Worksheet',
                    cancelAction: () => { },
                    confirmText: 'Accept new values',
                    confirmAction: () => { this.updateRevisionWorksheet(true); },
                    code: 'NewAssessmentValuesConfirmation',
                });
                return false;
            }

            this.finaliseWorksheetValues();
            try{
                await this.$store.dispatch('ruralWorksheet/updateWorksheet', { worksheet: this.worksheet, userName: this.userName, applyWorksheet: false });
            } catch (err) {
                return this.handleException(err);
            }

            if (this.errors && this.errors.length > 0) {
                this.setModal({
                    mode: 'warning',
                    isOpen: true,
                    heading: 'Oops',
                    message: 'The following validation errors have occurred:',
                    messages: this.errors.map(item => item.message),
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'Return to worksheet',
                    confirmAction: () => { },
                    code: 'ValidationError',
                });
                return false;
            }
            this.worksheet['reasonForChangeId'] = 0;
            try{
                await this.$store.dispatch('ruralWorksheet/updateAssessment', { worksheet: this.worksheet, userName: this.userName });
            } catch (err) {
                return this.handleException(err);
            }

            if (this.errors && this.errors.length > 0) {
                this.setModal({
                    mode: 'warning',
                    isOpen: true,
                    heading: 'Oops',
                    message: 'The following validation errors have occurred:',
                    messages: this.errors.map(item => item.message),
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'Return to worksheet',
                    confirmAction: () => { },
                    code: 'ValidationError',
                });
                return false;
            }

            if (this.warnings && this.warnings.length > 0) {
                this.setModal({
                    mode: 'caution',
                    isOpen: true,
                    heading: 'Oops',
                    message: 'The following validation errors have occurred:',
                    messages: this.warnings.map(item => item.message),
                    cancelText: null,
                    cancelAction: () => { },
                    confirmText: 'Return to worksheet',
                    confirmAction: () => { },
                    code: 'Warning',
                });
                return false;
            }

            //successfully updated the worksheet
            this.setModal({
                mode: 'success',
                isOpen: true,
                heading: 'Success',
                message: 'The worksheet was successfully updated.',
                messages: [],
                cancelText: null,
                cancelAction: () => { },
                confirmText: 'OK',
                confirmAction: () => { this.goToPropertySummary(); },
                code: 'SuccessMessage',
            });
            return false;
        },
        handleException(err) {
            this.setModal({
                mode: 'warning',
                isOpen: true,
                heading: 'Unexpected Error',
                message: `An unexpected error occurred attempting to communicate with the server: ${err}`,
                messages: [],
                cancelText: null,
                cancelAction: () => { },
                confirmText: 'Return to Worksheet',
                confirmAction: () => { },
                code: '',
            });
            return false;
        },
        setModal(modal) {
            this.modal = modal;
        },
        modalCancel() {
            this.modal.isOpen = false;
            this.modal.cancelAction();
        },
        modalConfirm() {
            this.modal.isOpen = false;
            this.modal.confirmAction();
        },
    },
    watch: {
        property(newVal) {
            if (newVal) {
                this.loadPropertyDetail();

            }
        }
    },
    async created() {
        await this.loadUserData();
        await this.loadWorksheet();
        await this.getPickListValues();
        this.compareLandMatrixToLandUseData();
    }
}
</script>

<style lang="scss" src="../rollMaintenance.scss"></style>
<style lang="scss" src="./ruralWorksheet.scss"></style>

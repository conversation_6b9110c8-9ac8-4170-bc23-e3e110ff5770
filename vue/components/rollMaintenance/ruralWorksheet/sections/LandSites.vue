<template>
    <div class="container-fluid">
        <div
            class="row table-head"
            style="padding-top: 3px;"
        >
            <div class="col-lg-5">
                Views
            </div>
            <div class="col-lg-3">
                Area
            </div>
            <div class="col-lg-3">
                Lump Sum
            </div>
            <div class="col-lg-1">
                &nbsp;
            </div>
        </div>
        <div
            v-for="site of sitesList"
            :key="site.rowId"
            class="row table-row"
            :class="{ invalid: site.invalid }"
        >
            <div class="col-lg-5">
                <label>
                    <select
                        v-model.number="site.ruralViewId"
                        data-cy="ruralWorksheetSiteEditView"
                        :disabled="!isCurrent"
                    >
                        <option
                            v-for="option in classifications.landViewTypes"
                            :key="option.id"
                            :value="option.id"
                        >
                            {{ option.description }}
                        </option>
                    </select>
                </label>
            </div>
            <div class="col-lg-3">
                <label>
                    <input
                        v-model.number="site.siteArea"
                        type="number"
                        style="text-align:right;"
                        step="0.001"
                        data-cy="ruralWorksheetSiteEditArea"
                        :disabled="!isCurrent"
                        @blur="updateSite(site, null)"
                    >
                </label>
            </div>
            <div class="col-lg-3">
                <label>
                    <number-input
                        :value="site.improvementValue"
                        format="$0,0"
                        style="text-align:right;"
                        step="1000"
                        data-cy="ruralWorksheetSiteEditValue"
                        :disabled="isRtv"
                        @blur="($event) => updateSite(site, toNumber($event.srcElement.value))"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <i
                    v-if="isCurrent"
                    class="saRow-remove material-icons"
                    data-cy="ruralWorksheetSiteRemoveButton"
                    @click="removeSite(site.rowId)"
                ></i>
            </div>
        </div>
        <div
            v-if="isCurrent"
            class="row table-row"
        >
            <div class="col-lg-5">
                <label>
                    <select
                        ref="newSiteView"
                        v-model="newSiteView"
                        data-cy="ruralWorksheetSiteAddView"
                        @keyup.enter="addSite(true)"
                    >
                        <option />
                        <option
                            v-for="option in classifications.landViewTypes"
                            :key="option.id"
                            :value="option.id"
                        >
                            {{ option.description }}
                        </option>
                    </select>
                </label>
            </div>
            <div class="col-lg-3">
                <label>
                    <input
                        v-model.number="newSiteArea"
                        type="number"
                        value="0"
                        style="text-align:right;"
                        step="0.001"
                        data-cy="ruralWorksheetSiteAddArea"
                        onclick="this.select()"
                        @keyup.enter="addSite(true)"
                    >
                </label>
            </div>
            <div class="col-lg-3">
                <label>
                    <input
                        v-model.number="newSiteValue"
                        type="number"
                        value=""
                        style="text-align:right;"
                        step="1000"
                        min="0"
                        data-cy="ruralWorksheetSiteAddValue"
                        @blur="addSite()"
                        @keyup.enter="addSite(true)"
                    >
                </label>
            </div>
            <div class="col-lg-1">
                <i
                    class="saRow-add material-icons"
                    data-cy="ruralWorksheetSiteAddButton"
                    @click="addSite(true)"
                >
                    
                </i>
            </div>
        </div>
    </div>
</template>


<script>
import numeral from 'numeral';

export default {
    components: {
        'number-input': () => import(/* webpackChunkName: "RuralWorksheet" */ '../../../common/form/NumberInput.vue'),
    },
    props: {
        worksheetType: {
            type: String,
            required: true,
            default: 'Current',
        },
        sites: {
            type: Array,
            default: [],
        },
        classifications: {
            type: Object,
            default: {},
        },
        originalSites: {
            type: Array,
            default: [],
        },
    },
    data() {
        return {
            newSiteView: '',
            newSiteArea: 0,
            newSiteValue: null,
        };
    },
    computed: {
        isCurrent() {
            return this.worksheetType === 'Current';
        },
        isRevision() {
            return this.worksheetType === 'Revision';
        },
        isRtv() {
            return this.worksheetType === 'RTV';
        },
        sitesList() {
            return this.sites.filter(obj => obj.state !== 'deleted');
        },
    },
    methods: {
        newSiteValid() {
            return this.newSiteView !== ''
                || (this.newSiteArea && parseFloat(this.newSiteArea) !== 0 && this.newSiteArea !== '')
                || (this.newSiteValue && parseFloat(this.newSiteValue) !== 0 && this.newSiteValue !== '');
        },
        addSite(force) {
            if (this.newSiteValid()
                || force) {
                // determine new row id
                const minRowId = Math.min.apply(Math, this.sites.map(o => o.rowId));
                // add new improvement to array
                this.sites.push({
                    rowId: minRowId > 0 ? -1 : minRowId - 1,
                    ruralViewId: this.newSiteView,
                    siteArea: Math.round(parseFloat(this.newSiteArea || 0) * 10000) / 10000,
                    improvementValue: Math.round(parseFloat(this.newSiteValue || 0)),
                    state: 'new',
                });
                this.resetInputs();
                this.$refs.newSiteView.focus();
                this.$emit('update-rounded-totals');
            }
        },
        updateSite(site, newSum) {
            if (!site.siteArea || site.siteArea === '') {
                site.siteArea = 0;
            }
            site.siteArea = Math.round(parseFloat(site.siteArea) * 10000) / 10000;
            if (newSum !== null) {
                site.improvementValue = Math.round(newSum);
            }
            if (site.invalid) {
                site.invalid = !(site.ruralViewId > 0 && site.siteArea > 0 && site.improvementValue > 0);
            }
            this.$emit('update-rounded-totals');
        },
        resetInputs() {
            this.newSiteView = '';
            this.newSiteArea = 0;
            this.newSiteValue = null;
        },
        removeSite(rowId) {
            const foundItem = this.sites.find(item => item.rowId === rowId);
            if (foundItem) {
                foundItem.state = 'deleted';
                this.$emit('update-rounded-totals');
            }
        },
        toNumber(text) {
            const n = numeral(text).value();
            return Number.isNaN(n) ? null : n;
        },
    },
};

</script>

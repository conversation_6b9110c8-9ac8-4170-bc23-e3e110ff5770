<template>
    <div class="container-fluid">
        <div class="row table-head">
            <div class="col-lg-2">
                Description
            </div>
            <div
                class="col-lg-2 sortable"
                @click="changeSortOrder('ruralContour')"
            >
                Contour
                <span :class="sortableHeaderClass('ruralContour')" />
            </div>
            <div
                class="col-lg-2 sortable"
                @click="changeSortOrder('ruralUse')"
            >
                Type/Use
                <span :class="sortableHeaderClass('ruralUse')" />
            </div>
            <div class="col-lg-2">
                Irrigation
            </div>
            <div class="col-lg-1">
                Area
            </div>
            <div class="col-lg-1">
                Rate
            </div>
            <div class="col-lg-1">
                Value
            </div>
            <div class="col-lg-1">
                &nbsp;
            </div>
        </div>
        <div
            v-for="landUse of landUseTypeList"
            :key="landUse.rowId"
            class="row table-row"
            :class="{ invalid: landUse.invalid }"
        >
            <div class="col-lg-2">
                <label>
                    <input
                        v-model="landUse.itemDescription"
                        type="text"
                        maxlength="50"
                        data-cy="ruralWorksheetLandUseTypeEditDescription"
                        :disabled="!isCurrent"
                        @blur="updateLandUseType(landUse, null)"
                    >
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <select
                        v-model.number="landUse.ruralContourId"
                        data-cy="ruralWorksheetLandUseTypeEditContour"
                        :disabled="!isCurrent"
                        @blur="updateLandUseType(landUse, null)"
                    >
                        <template v-for="option in classifications.landContourTypes">
                            <option
                                v-if="option.description !== 'Undefined' || option.id === landUse.ruralContourId"
                                :key="option.id"
                                :value="option.id"
                            >
                                {{ option.description }}
                            </option>
                        </template>
                    </select>
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <select
                        v-model.number="landUse.ruralUseId"
                        data-cy="ruralWorksheetLandUseTypeEditType"
                        :disabled="!isCurrent"
                        @blur="updateLandUseType(landUse, null)"
                    >
                        <template v-for="option in classifications.landUseTypes">
                            <option
                                v-if="option.description !== 'Undefined' || option.id === landUse.ruralUseId"
                                :key="option.id"
                                :value="option.id"
                            >
                                {{ option.description }}
                            </option>
                        </template>
                    </select>
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <select
                        v-model.number="landUse.irrigationTypeId"
                        data-cy="ruralWorksheetLandUseTypeEditIrrigation"
                        :disabled="!isCurrent"
                        @blur="updateLandUseType(landUse, null)"
                    >
                        <option :value="null" />
                        <option
                            v-for="option in classifications.landIrrigationTypes"
                            :key="option.id"
                            :value="option.id"
                        >
                            {{ option.description }}
                        </option>
                    </select>
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input
                        v-model.number="landUse.itemSize"
                        type="number"
                        style="text-align:right;"
                        step="1"
                        min="0"
                        data-cy="ruralWorksheetLandUseTypeEditSize"
                        :disabled="!isCurrent"
                        @blur="updateLandUseType(landUse, null)"
                    >
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <number-input
                        :value="landUse.rate"
                        format="$0,0.0"
                        style="text-align:right;"
                        step="10"
                        min="0"
                        data-cy="ruralWorksheetLandUseTypeEditRate"
                        :disabled="isRtv"
                        @blur="($event) => updateLandUseType(landUse, toNumber($event.srcElement.value))"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <number-input
                        :value="landUse.landValue"
                        format="$0,0"
                        style="text-align:right;"
                        step="1000"
                        min="0"
                        data-cy="ruralWorksheetLandUseTypeEditValue"
                        disabled
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <i
                    v-if="isCurrent"
                    class="saRow-remove material-icons"
                    data-cy="ruralWorksheetLandUseTypeRemoveButton"
                    @click="removeLandUseType(landUse.rowId)"
                ></i>
            </div>
        </div>
        <div
            v-if="isCurrent"
            class="row table-row"
        >
            <div class="col-lg-2">
                <label>
                    <input
                        ref="newUseDescription"
                        v-model="newUseDescription"
                        type="text"
                        data-cy="ruralWorksheetLandUseTypeAddDescription"
                        @keyup.enter="addLandUseType(true)"
                    >
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <select
                        v-model="newUseContour"
                        data-cy="ruralWorksheetLandUseTypeAddContour"
                        @keyup.enter="addLandUseType(true)"
                    >
                        <option />
                        <template v-for="option in classifications.landContourTypes">
                            <option
                                v-if="option.description !== 'Undefined'"
                                :key="option.id"
                                :value="option.id"
                            >
                                {{ option.description }}
                            </option>
                        </template>
                    </select>
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <select
                        v-model="newUseType"
                        data-cy="ruralWorksheetLandUseTypeAddType"
                        @keyup.enter="addLandUseType(true)"
                    >
                        <option />
                        <template v-for="option in classifications.landUseTypes">
                            <option
                                v-if="option.description !== 'Undefined'"
                                :key="option.id"
                                :value="option.id"
                            >
                                {{ option.description }}
                            </option>
                        </template>
                    </select>
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <select
                        v-model.number="newUseIrrigation"
                        data-cy="ruralWorksheetLandUseTypeAddIrrigation"
                        @keyup.enter="addLandUseType(true)"
                    >
                        <option :value="null" />
                        <option
                            v-for="option in classifications.landIrrigationTypes"
                            :key="option.id"
                            :value="option.id"
                        >
                            {{ option.description }}
                        </option>
                    </select>
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input
                        v-model.number="newUseSize"
                        type="number"
                        step="0.001"
                        style="text-align:right;"
                        data-cy="ruralWorksheetLandUseTypeAddSize"
                        onclick="this.select()"
                        @keyup.enter="addLandUseType(true)"
                    >
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input
                        v-model.number="newUseRate"
                        type="number"
                        step="10"
                        style="text-align:right;"
                        data-cy="ruralWorksheetLandUseTypeAddRate"
                        onclick="this.select()"
                        @blur="addLandUseType()"
                        @keyup.enter="addLandUseType(true)"
                    >
                </label>
            </div>
            <div class="col-lg-1" />
            <div class="col-lg-1">
                <i
                    class="saRow-add material-icons"
                    data-cy="ruralWorksheetLandUseTypeAddButton"
                    @click="addLandUseType(true)"
                >
                    
                </i>
            </div>
        </div>
    </div>
</template>

<script>
import numeral from 'numeral';

export default {
    components: {
        'number-input': () => import(/* webpackChunkName: "RuralWorksheet" */ '../../../common/form/NumberInput.vue'),
    },
    props: {
        worksheetType: {
            type: String,
            required: true,
            default: 'Current',
        },
        landUseTypes: {
            type: Array,
            default: [],
            required: true,
        },
        classifications: {
            type: Object,
            default: {},
            required: true,
        },
        originalLandUseTypes: {
            type: Array,
            default: [],
            required: true,
        },
    },
    data() {
        return {
            newUseContour: '',
            newUseType: '',
            newUseIrrigation: null,
            newUseDescription: '',
            newUseSize: 0,
            newUseRate: 0,
            sortBy: {
                type: String,
                default: '',
                required: false,
            },
            sortOrder: {
                type: String,
                default: 'DESC',
                required: false,
            },
        };
    },
    computed: {
        isCurrent() {
            return this.worksheetType === 'Current';
        },
        isRevision() {
            return this.worksheetType === 'Revision';
        },
        isRtv() {
            return this.worksheetType === 'RTV';
        },
        landUseTypeList() {
            return this.landUseTypes.filter(obj => obj.state !== 'deleted');
        },
    },
    methods: {
        newLandUseTypeValid() {
            return this.newUseDescription !== ''
                    || this.newUseType !== ''
                    || this.newUseContour !== ''
                    || (this.newUseSize && parseFloat(this.newUseSize) !== 0 && this.newUseSize !== '')
                    || (this.newUseRate && parseFloat(this.newUseRate) !== 0 && this.newUseRate !== '');
        },
        addLandUseType(force) {
            if (this.newLandUseTypeValid()
                || force) {
                // determine new row id
                const minRowId = Math.min.apply(Math, this.landUseTypes.map(o => o.rowId));
                const newItemSize = Math.round(parseFloat(this.newUseSize || 0) * 10000) / 10000;
                const newRate = Math.round(parseFloat(this.newUseRate || 0) * 10) / 10;
                // add new improvement to array
                this.landUseTypes.push({
                    rowId: minRowId > 0 ? -1 : minRowId - 1,
                    itemDescription: this.newUseDescription.substring(0, 50),
                    ruralUseId: this.newUseType,
                    ruralContourId: this.newUseContour,
                    irrigationTypeId: this.newUseIrrigation,
                    itemSize: newItemSize,
                    rate: newRate,
                    landValue: Math.round(newItemSize * newRate),
                    state: 'new',
                });
                this.resetInputs();
                this.$refs.newUseDescription.focus();

                this.$emit('update-rounded-totals');
                this.$emit('compare-to-land-matrix');
            }
        },
        resetInputs() {
            this.newUseDescription = '';
            this.newUseType = '';
            this.newUseContour = '';
            this.newUseIrrigation = null;
            this.newUseSize = 0;
            this.newUseRate = 0;
        },
        updateLandUseType(landUseType, newRate) {
            if (!landUseType.itemSize || landUseType.itemSize === '') {
                landUseType.itemSize = 0;
            }
            landUseType.itemSize = Math.round(parseFloat(landUseType.itemSize) * 10000) / 10000;
            if (newRate !== null) {
                landUseType.rate = Math.round(parseFloat(newRate) * 10) / 10;
            }
            landUseType.landValue = Math.round(landUseType.itemSize * landUseType.rate);
            if (landUseType.rowId > 0) {
                const originalLandUseType = this.originalLandUseTypes.filter(item => item.rowId === landUseType.rowId);
                if (originalLandUseType) {
                    // compare to original and set state
                    if (originalLandUseType[0].itemDescription === landUseType.itemDescription
                        && originalLandUseType[0].ruralUseId === landUseType.ruralUseId
                        && originalLandUseType[0].ruralContourId === landUseType.ruralContourId
                        && originalLandUseType[0].irrigationTypeId === landUseType.irrigationTypeId
                        && originalLandUseType[0].itemSize === landUseType.itemSize
                        && originalLandUseType[0].rate === landUseType.rate
                    ) {
                        landUseType.state = 'existing';
                    } else {
                        landUseType.state = 'modified';
                    }
                }
            }
            if (landUseType.invalid) {
                const itemIsBlank = ((!landUseType.description || landUseType.description === null)
                                    && landUseType.ruralUseId <= 0
                                    && landUseType.ruralContourId <= 0
                                    && landUseType.irrigationTypeId <= 0
                                    && landUseType.itemSize <= 0
                                    && landUseType.rate <= 0);

                landUseType.invalid = !(itemIsBlank || (landUseType.ruralUseId > 0 && landUseType.ruralContourId > 0 && landUseType.itemSize > 0));
            }
            this.$emit('update-rounded-totals');
            this.$emit('compare-to-land-matrix');
        },
        removeLandUseType(rowId) {
            const foundItem = this.landUseTypes.find(item => item.rowId === rowId);
            if (foundItem) {
                foundItem.state = 'deleted';
            }
            this.$emit('update-rounded-totals');
            this.$emit('compare-to-land-matrix');
        },
        toNumber(text) {
            const n = numeral(text).value();
            return Number.isNaN(n) ? null : n;
        },
        changeSortOrder(column) {
            if (this.sortBy !== column) {
                this.sortBy = column;
                this.sortOrder = 'ASC';
            } else {
                this.sortOrder = this.sortOrder === 'ASC' ? 'DESC' : 'ASC';
            }
            this.sortLandUseTypes();
        },
        sortLandUseTypes() {
            this.landUseTypes.map((lu) => {
                const ruralContour = this.classifications.landContourTypes.find(c => c.id === lu.ruralContourId);
                const ruralUse = this.classifications.landUseTypes.find(c => c.id === lu.ruralUseId);
                lu.ruralContour = ruralContour ? ruralContour.description.toLowerCase() : '0';
                lu.ruralUse = ruralUse ? ruralUse.description.toLowerCase() : '0';

                return lu;
            });
            if (this.sortOrder === 'ASC') {
                this.landUseTypes.sort((a, b) => ((a[this.sortBy] > b[this.sortBy]) ? 1 : -1));
            } else {
                this.landUseTypes.sort((a, b) => ((a[this.sortBy] < b[this.sortBy]) ? 1 : -1));
            }
        },
        sortableHeaderClass(column) {
            return this.sortBy !== column ? '' : { asc: this.sortOrder === 'ASC', desc: this.sortOrder === 'DESC' };
        },
    },
};

</script>

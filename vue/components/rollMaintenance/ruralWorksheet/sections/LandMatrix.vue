<template>
    <div class="container-fluid">
        <div class="row table-head">
            <div
                class="col-lg-2"
                data-cy="ruralWorksheetLandMatrixContourColumn"
            >
                Contour
            </div>
            <div
                class="col-lg-3"
                data-cy="ruralWorksheetLandMatrixLandCoverColumn"
            >
                Land Cover
            </div>
            <div
                class="col-lg-2"
                data-cy="ruralWorksheetLandMatrixQvLandCoverColumn"
            >
                QV Land Cover
            </div>
            <div
                class="col-lg-1"
                data-cy="ruralWorksheetLandMatrixAreaColumn"
            >
                Area
            </div>
            <div
                class="col-lg-1"
                data-cy="ruralWorksheetLandMatrixEstimatedRateColumn"
            >
                Est. Rate
            </div>
            <div
                class="col-lg-2"
                data-cy="ruralWorksheetLandMatrixEstimatedValueColumn"
            >
                Est. Value
            </div>
            <div class="col-lg-1">
                &nbsp;
            </div>
        </div>
        <div
            v-for="(matrixItem, index) of landMatrix"
            :key="index"
            class="row table-row"
        >
            <div class="col-lg-2">
                <label>
                    <input
                        type="text"
                        :value="matrixItem.contourName"
                        data-cy="ruralWorksheetLandMatrixContourName"
                        disabled
                    >
                </label>
            </div>
            <div class="col-lg-3">
                <label>
                    <input
                        type="text"
                        :value="matrixItem.coverName"
                        data-cy="ruralWorksheetLandMatrixCoverName"
                        disabled
                    >
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <input
                        type="text"
                        :value="matrixItem.qvCoverName"
                        data-cy="ruralWorksheetLandMatrixQvCoverName"
                        disabled
                    >
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <number-input
                        :value="matrixItem.itemSize"
                        format="0.00"
                        style="text-align:right;"
                        data-cy="ruralWorksheetLandMatrixArea"
                        disabled
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <number-input
                        :value="matrixItem.estimatedRate"
                        format="$0,0"
                        style="text-align:right;"
                        step="10"
                        min="0"
                        data-cy="ruralWorksheetLandMatrixEstimatedRate"
                        :disabled="!isCurrent"
                        @blur="($event) => updateLandMatrixItem(
                            matrixItem.rowId,
                            toNumber($event.srcElement.value),
                            'rate'
                        )"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <number-input
                        :value="matrixItem.estimatedValue"
                        format="$0,0"
                        style="text-align:right;"
                        step="10"
                        min="0"
                        data-cy="ruralWorksheetLandMatrixEstimatedValue"
                        :disabled="!isCurrent"
                        @blur="($event) => updateLandMatrixItem(
                            matrixItem.rowId,
                            toNumber($event.srcElement.value),
                            'value'
                        )"
                    />
                </label>
            </div>
            <div class="col-lg-1" />
        </div>
        <div
            class="row"
            style="margin-top:10px;"
        >
            <div class="col-lg-7">
                &nbsp;
            </div>
            <div
                class="col-lg-2"
                style="text-align:right;"
            >
                <h3 style="font-weight:bold;line-height:39px;">
                    Total Area
                </h3>
            </div>
            <div class="col-lg-1">
                <label>
                    <input
                        :value="estimatedTotalLandArea"
                        style="text-align:right;"
                        data-cy="ruralWorksheetLandMatrixTotalArea"
                        disabled
                    >
                </label>
            </div>
            <div class="col-lg-2">
                &nbsp;
            </div>
        </div>
        <div
            class="row"
            style="margin-top:10px;"
        >
            <div class="col-lg-7">
                <span style="font-style:italic">Note: changes to this section are not saved</span>
            </div>
            <div
                class="col-lg-2"
                style="text-align:right;"
            >
                <h3 style="font-weight:bold;line-height:39px;">
                    Estimated Total LV
                </h3>
            </div>
            <div class="col-lg-2">
                <label>
                    <number-input
                        :value="estimatedTotalLandValue"
                        format="$0,0"
                        style="text-align:right;"
                        data-cy="ruralWorksheetLandMatrixTotalLV"
                        disabled
                    />
                </label>
            </div>
            <div class="col-lg-1">
                &nbsp;
            </div>
        </div>
    </div>
</template>

<script>
import numeral from 'numeral';

export default {
    components: {
        'number-input': () => import(/* webpackChunkName: "RuralWorksheet" */ '../../../common/form/NumberInput.vue'),
    },
    props: {
        worksheetType: {
            type: String,
            required: true,
            default: 'Current',
        },
        landMatrix: {
            type: Array,
            default: () => [],
            required: true,
        },
        sortBy: {
            type: String,
            default: 'ruralContour',
            required: false,
        },
        sortOrder: {
            type: String,
            default: 'ASC',
            required: false,
        },
    },
    computed: {
        isCurrent() {
            return this.worksheetType === 'Current';
        },
        isRevision() {
            return this.worksheetType === 'Revision';
        },
        isRtv() {
            return this.worksheetType === 'RTV';
        },
        estimatedTotalLandValue() {
            return Math.round(this.landMatrix.reduce((accumulatedTotal, item) => accumulatedTotal + item.estimatedValue, 0), 0);
        },
        estimatedTotalLandArea() {
            return Math.round(this.landMatrix.reduce((accumulatedTotal, item) => accumulatedTotal + item.itemSize, 0) * 1000) / 1000;
        },
    },
    methods: {
        updateLandMatrixItem(landMatrixRowId, updatedValue, updatedMatrixProperty) {
            let newValue = updatedValue;
            if (!updatedValue || updatedValue === '') {
                newValue = 0;
            }
            const matrixItem = this.landMatrix.find(item => item.rowId === landMatrixRowId);
            if (matrixItem) {
                if (updatedMatrixProperty === 'rate') {
                    const newEstimatedRate = Math.round(parseFloat(newValue));
                    matrixItem.estimatedRate = newEstimatedRate;
                    matrixItem.estimatedValue = Math.round(matrixItem.itemSize * newEstimatedRate);
                }
                if (updatedMatrixProperty === 'value') {
                    const newEstimatedValue = Math.round(parseFloat(newValue));
                    matrixItem.estimatedValue = newEstimatedValue;
                    matrixItem.estimatedRate = Math.round(newEstimatedValue / matrixItem.itemSize);
                }
            }
        },
        toNumber(text) {
            const n = numeral(text).value();
            return Number.isNaN(n) ? null : n;
        },
    },
};

</script>

<template>
    <div class="container-fluid">
        <div class="row table-head">
            <div class="col-lg-4">
                Description
            </div>
            <div class="col-lg-3">
                Improvement Type
            </div>
            <div class="col-lg-1">
                Size/No
            </div>
            <div class="col-lg-1">
                Rate
            </div>
            <div class="col-lg-2">
                Value
            </div>
            <div class="col-lg-1">
                &nbsp;
            </div>
        </div>
        <div
            v-for="improvement of improvementList"
            :key="improvement.rowId"
            class="row table-row"
            :class="{ invalid: improvement.invalid }"
        >
            <div class="col-lg-4">
                <label>
                    <input
                        v-model="improvement.itemDescription"
                        type="text"
                        maxlength="50"
                        data-cy="ruralWorksheetImprovementEditDescription"
                        :disabled="!isCurrent"
                        @blur="updateImprovement(improvement, null)"
                    >
                </label>
            </div>
            <div class="col-lg-3">
                <label>
                    <select
                        v-model.number="improvement.ruralImprovementId"
                        data-cy="ruralWorksheetImprovementEditType"
                        :disabled="!isCurrent"
                        @blur="updateImprovement(improvement, null)"
                    >
                        <template v-for="option in classifications.improvementTypes">
                            <option
                                v-if="option.description !== 'Undefined' || option.id === improvement.ruralImprovementId"
                                :key="option.id"
                                :value="option.id"
                            >
                                {{ option.description }}
                            </option>
                        </template>
                    </select>
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input
                        v-model.number="improvement.itemSize"
                        type="number"
                        step="1"
                        min="0"
                        style="text-align:right;"
                        data-cy="ruralWorksheetImprovementEditSize"
                        :disabled="!isCurrent"
                        @blur="updateImprovement(improvement, null)"
                    >
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <number-input
                        :value="improvement.rate"
                        format="$0,0.0"
                        style="text-align:right;"
                        step="10"
                        min="0"
                        data-cy="ruralWorksheetImprovementEditRate"
                        :disabled="isRtv"
                        @blur="($event) => updateImprovement(improvement,toNumber($event.srcElement.value))"
                    />
                </label>
            </div>
            <div class="col-lg-2">
                <label>
                    <number-input
                        :value="improvement.improvementValue"
                        format="$0,0"
                        style="text-align:right;"
                        data-cy="ruralWorksheetImprovementRemoveButton"
                        disabled
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <i
                    v-if="isCurrent"
                    class="saRow-remove material-icons"
                    @click="removeImprovement(improvement.rowId);"
                ></i>
            </div>
        </div>
        <div
            v-if="isCurrent"
            class="row table-row"
        >
            <div class="col-lg-4">
                <label>
                    <input
                        ref="newImprovementDescription"
                        v-model="newImprovementDescription"
                        type="text"
                        data-cy="ruralWorksheetImprovementAddDescription"
                        @keyup.enter="addImprovement(true)"
                    >
                </label>
            </div>
            <div class="col-lg-3">
                <label>
                    <select
                        v-model="newImprovementType"
                        data-cy="ruralWorksheetImprovementAddType"
                        @keyup.enter="addImprovement(true)"
                    >
                        <option />
                        <template v-for="option in classifications.improvementTypes">
                            <option
                                v-if="option.description !== 'Undefined'"
                                :key="option.id"
                                :value="option.id"
                            >
                                {{ option.description }}
                            </option>
                        </template>
                    </select>
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input
                        v-model.number="newImprovementSize"
                        type="number"
                        value="0"
                        style="text-align:right;"
                        step="1"
                        min="0"
                        data-cy="ruralWorksheetImprovementAddSize"
                        onclick="this.select()"
                        @keyup.enter="addImprovement(true)"
                    >
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <input
                        v-model.number="newImprovementRate"
                        type="number"
                        value="0"
                        style="text-align:right;"
                        step="10"
                        min="0"
                        data-cy="ruralWorksheetImprovementAddRate"
                        onclick="this.select()"
                        @blur="addImprovement()"
                        @keyup.enter="addImprovement(true)"
                    >
                </label>
            </div>
            <div class="col-lg-2" />
            <div class="col-lg-1">
                <i
                    class="saRow-add material-icons"
                    data-cy="ruralWorksheetImprovementAddButton"
                    @click="addImprovement(true)"
                >
                    
                </i>
            </div>
        </div>
    </div>
</template>

<script>
import numeral from 'numeral';

export default {
    components: {
        'number-input': () => import(/* webpackChunkName: "RuralWorksheet" */ '../../../common/form/NumberInput.vue'),
    },
    props: {
        worksheetType: {
            type: String,
            required: true,
            default: 'Current',
        },
        improvements: {
            type: Array,
            default: [],
        },
        classifications: {
            type: Object,
            default: {},
        },
        originalImprovements: {
            type: Array,
            default: [],
        },
    },
    data() {
        return {
            newImprovementDescription: '',
            newImprovementType: '',
            newImprovementSize: 0,
            newImprovementRate: 0,
        };
    },
    computed: {
        isCurrent() {
            return this.worksheetType === 'Current';
        },
        isRevision() {
            return this.worksheetType === 'Revision';
        },
        isRtv() {
            return this.worksheetType === 'RTV';
        },
        improvementList() {
            return this.improvements.filter(obj => obj.state !== 'deleted');
        },
    },
    methods: {
        newImprovementValid() {
            return this.newImprovementDescription !== ''
                || this.newImprovementType !== ''
                || (this.newImprovementSize && parseFloat(this.newImprovementSize) !== 0 && this.newImprovementSize !== '')
                || (this.newImprovementRate && parseFloat(this.newImprovementRate) !== 0 && this.newImprovementRate !== '');
        },
        addImprovement(force) {
            if (this.newImprovementValid()
                || force) {
                // determine new row id
                const minRowId = Math.min.apply(Math, this.improvements.map(o => o.rowId));
                const newItemSize = Math.round(parseFloat(this.newImprovementSize || 0) * 10000) / 10000;
                const newRate = Math.round(parseFloat(this.newImprovementRate || 0) * 10) / 10;

                // add new improvement to array
                this.improvements.push({
                    rowId: minRowId > 0 ? -1 : minRowId - 1,
                    itemDescription: this.newImprovementDescription.substring(0, 50),
                    ruralImprovementId: this.newImprovementType,
                    itemSize: newItemSize,
                    rate: newRate,
                    improvementValue: Math.round(newItemSize * newRate),
                    state: 'new',
                });
                this.resetInputs();
                this.$refs.newImprovementDescription.focus();
                this.$emit('update-rounded-totals');
            }
        },
        updateImprovement(improvement, newRate) {
            if (!improvement.itemSize || improvement.itemSize === '') {
                improvement.itemSize = 0;
            }
            improvement.itemSize = Math.round(parseFloat(improvement.itemSize) * 10000) / 10000;
            if (newRate !== null) {
                improvement.rate = Math.round(parseFloat(newRate) * 10) / 10;
            }
            improvement.improvementValue = Math.round(improvement.itemSize * improvement.rate);
            if (improvement.rowId > 0) {
                const originalImprovement = this.originalImprovements.filter(imp => (imp.rowId === improvement.rowId));
                if (originalImprovement) {
                    // compare to original and set state
                    if (originalImprovement[0].itemDescription === improvement.itemDescription
                        && originalImprovement[0].ruralImprovementId === improvement.ruralImprovementId
                        && originalImprovement[0].itemSize === improvement.itemSize
                        && originalImprovement[0].rate === improvement.rate
                    ) {
                        improvement.state = 'existing';
                    } else {
                        improvement.state = 'modified';
                    }
                }
            }
            if (improvement.invalid) {
                const itemIsBlank = ((!improvement.itemDescription || improvement.itemDescription === '')
                                    && improvement.ruralImprovementId <= 0
                                    && improvement.itemSize <= 0
                                    && improvement.rate <= 0);

                improvement.invalid = !(itemIsBlank || (improvement.ruralImprovementId > 0 && improvement.itemSize > 0));
            }
            this.$emit('update-rounded-totals');
        },
        resetInputs() {
            this.newImprovementDescription = '';
            this.newImprovementType = '';
            this.newImprovementSize = 0;
            this.newImprovementRate = 0;
        },
        removeImprovement(rowId) {
            const foundItem = this.improvements.find(item => item.rowId === rowId);
            if (foundItem) {
                foundItem.state = 'deleted';
                this.$emit('update-rounded-totals');
            }
        },
        toNumber(text) {
            const n = numeral(text).value();
            return Number.isNaN(n) ? null : n;
        },
    },
};

</script>

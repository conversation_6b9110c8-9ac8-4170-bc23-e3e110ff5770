<template>
    <div class="row">
        <div class="col-lg-12">
            <div class="container-fluid">
                <div class="row table-head">
                    <div class="col-lg-2">
                        Index Type
                    </div>
                    <div class="col-lg-1">
                        Status
                    </div>
                    <div class="col-lg-1">
                        Run Date
                    </div>
                    <div class="col-lg-4">
                        Filters
                    </div>
                    <div class="col-lg-1 center">
                        LV Percent
                    </div>
                    <div class="col-lg-1 center">
                        LV Lump Sum
                    </div>
                    <div class="col-lg-1 center">
                        VI Percent
                    </div>
                    <div class="col-lg-1 center">
                        VI Lump Sum
                    </div>
                </div>
                <div
                    v-for="(indice, indiceIndex) in rtvIndices"
                    :key="`indice_${indiceIndex}`"
                    class="row table-row"
                    :class="indiceClass(indice)"
                >
                    <div class="col-lg-2">
                        {{ indice.source_type }}
                    </div>
                    <div class="col-lg-1">
                        {{ indice.status }}
                    </div>
                    <div class="col-lg-1">
                        {{ formatDate(indice.index_date) }}
                    </div>
                    <div class="col-lg-4">
                        <template v-if="indice.filters">
                            <span
                                v-for="(filter, filterIndex) in indice.filters"
                                :key="`indice_${indiceIndex}_${filterIndex}`"
                            >
                                {{ filter.description }}: {{ filter.value }}<br>
                            </span>
                        </template>
                    </div>
                    <div class="col-lg-1 center">
                        {{ formatPercentIndex(indice.lv_percent_index) }}
                    </div>
                    <div class="col-lg-1 center">
                        {{ formatLumpSumIndex(indice.lv_lump_sum_index) }}
                    </div>
                    <div class="col-lg-1 center">
                        {{ formatPercentIndex(indice.vi_percent_index) }}
                    </div>
                    <div class="col-lg-1 center">
                        {{ formatLumpSumIndex(indice.vi_lump_sum_index) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import numeral from 'numeral';
import moment from 'moment';

export default ({
    props: {
        qpid: {
            type: String,
            required: true,
        },
    },
    computed: {
        ...mapState('ruralRtv', {
            rtvIndices: 'rtvIndices',
        }),
    },
    mounted() {
        this.loadRtvIndices();
    },
    methods: {
        loadRtvIndices() {
            this.$store.dispatch('ruralRtv/getRuralPropertyRtvIndices', this.qpid);
        },
        indiceClass(indice) {
            return { bold: indice.status === 'Current' };
        },
        formatDate(date) {
            if (!date) {
                return '-';
            }
            return moment(date).tz('Pacific/Auckland').format('DD/MM/YYYY');
        },
        formatPercentIndex(value) {
            return numeral(value).format('0.00');
        },
        formatLumpSumIndex(value) {
            return numeral(value).format('$0,0');
        },
    },
});

</script>

<style scoped="true">

    .table-row div {
        font-size: 1.1rem;
    }

    .table-row.bold div, .table-row.bold div span {
        font-weight:bold;
    }

    div.center {
        text-align:center;
    }

</style>

<template>
    <div class="container-fluid space-rows">
        <div class="row">
            <div class="col-lg-5">
                <label>
                    <span class="label">QV Category</span>
                </label>
                <vue-multiselect
                        v-model="propertyDetail.qvCategory"
                        :options="qvCategoryTypes || []"
                        :searchable="true"
                        track-by="code"
                        label="description"
                        placeholder=""
                        select-label="⏎ select"
                        deselect-label="⏎ remove"
                        :value="propertyDetail.qvCategory"
                        @input="update"
                        data-cy="ruralWorksheetQVCategory"
                        :readonly="isRevision || !isEditable"
                        :disabled="isRevision || !isEditable"
                    />
            </div>
            <div class="col-lg-2">
                <label>
                    <span class="label">Grouping</span>
                </label>
                <vue-multiselect
                    v-model="propertyDetail.grouping"
                    :options="propertyGroupingTypes || []"
                    :searchable="true"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :value="propertyDetail.grouping"
                    @input="update"
                    data-cy="ruralWorksheetGrouping"
                    :readonly="isRevision || !isEditable"
                    :disabled="isRevision || !isEditable"
                />
            </div>
            <div class="col-lg-2">
                <label>
                    <span class="label">Quality Rating</span>
                </label>
                <vue-multiselect
                    v-model="propertyDetail.ruralDetail.qualityRating"
                    :options="qualityRatingTypes || []"
                    :searchable="true"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :value="propertyDetail.ruralDetail.qualityRating"
                    @input="update"
                    data-cy="ruralWorksheetQualityRating"
                    :readonly="isRevision || !isEditable"
                    :disabled="isRevision || !isEditable"
                />
            </div>
            <div class="col-lg-1">
                &nbsp;
            </div>
            <div class="col-lg-1">
                <label>
                    <span class="label">Area (ha)</span>
                    <input
                        type="number" 
                        v-model.number="worksheet.worksheetArea"
                        step=".0001"
                        min="0"
                        style="text-align:right;"
                        data-cy="ruralWorksheetWorksheetArea"
                        :disabled="!isCurrent"
                    />
                </label>
            </div>
            <div class="col-lg-1">
                <label>
                    <span class="label">Production</span>
                    <input
                        type="number" 
                        v-model.number="worksheet.production"
                        step="1"
                        min="0"
                        style="text-align:right;"
                        data-cy="ruralWorksheetProduction"
                        :disabled="!isCurrent"
                    />
                </label>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-3">
                <label>
                    <span class="label">TA Land Zone</span>
                </label>
                <vue-multiselect
                    :options="landZoneOptions || []"
                    track-by="code"
                    label="label"
                    select-label="⏎ select"
                    deselect-label=""
                    :value="propertyDetail.landUse.landZone"
                    @input="update"
                    data-cy="ruralWorksheetLandZone"
                    :readonly="true"
                    :disabled="true"
                />
            </div>
            <div class="col-lg-2">
                &nbsp;
            </div>
            <div class="col-lg-2">
                <label>
                    <span class="label">Nutrient Score</span>
                </label>
                <vue-multiselect
                    :options="qualityRatingTypes || []"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label=""
                    :value="nutrientManagementConsents[0].nutrientScore"
                    @select="value => updateNutrientDetails(0, 'nutrientScore', value)"
                    @remove="updateNutrientDetails(0, 'nutrientScore', null)"
                    data-cy="ruralWorksheetNutrientScore"
                    :readonly="isRevision || !isEditable"
                    :disabled="isRevision || !isEditable"
                />
            </div>
            <div class="col-lg-2">
                <label>
                    <span class="label">Water Quality Rating</span>
                </label>
                <vue-multiselect
                    v-model="propertyDetail.ruralDetail.waterQualityRating"
                    :options="qualityRatingTypes || []"
                    :searchable="true"
                    track-by="code"
                    label="description"
                    placeholder=""
                    select-label="⏎ select"
                    deselect-label="⏎ remove"
                    :value="propertyDetail.ruralDetail.waterQualityRating"
                    @input="update"
                    data-cy="ruralWorksheetWaterQualityRating"
                    :readonly="isRevision || !isEditable"
                    :disabled="isRevision || !isEditable"
                />
            </div>
            <div class="col-lg-3">
                &nbsp;
            </div>
        </div>

    </div>
</template>

<script>
import { mapState } from 'vuex';
import commonUtils from '../../../../utils/CommonUtils';

export default {
    components: {
        'vue-multiselect': () => import(/* webpackChunkName: "vue-multiselect" */ 'vue-multiselect'),
        'validation-message': () => import(/* webpackChunkName: "ValidationMessage" */ '../../../common/form/ValidationMessage.vue'),
    },
    mixins: [commonUtils],
    data() {
        return {
            landZoneOptions: []
        }
    },
    props: {
        propertyDetail: {
            type: Object,
            required: true,
        },
        propertyGroupingTypes: {
            type: Array,
            default: () => [],
            required: true,
        },
        qvCategoryTypes: {
            type: Array,
            default: () => [],
            required: true,
        },
        qualityRatingTypes: {
            type: Array,
            default: () => [],
            required: true,
        },
        validationSet: {
            type: Object,
            default: null,
        },
        isEditable: {
            type: Boolean,
            default: true,
            required: false,
        }
    },
    computed: {
        ...mapState('property', ['property']),
        ...mapState('ruralWorksheet', {
           worksheet: 'worksheet',
        }),
        isCurrent() {
            if (!this.worksheet || !this.worksheet.state) {
                return false;
            }
            return !this.worksheet.state.isRevisionWorksheet && !this.worksheet.state.isRtvWorksheet
        },
        isRevision() {
            if (!this.worksheet || !this.worksheet.state) {
                return false;
            }
            return this.worksheet.state.isRevisionWorksheet;
        },
        isRtv() {
            if (!this.worksheet || !this.worksheet.state) {
                return false;
            }
            return this.worksheet.state.isRtvWorksheet;
        },
        errors() {
            return (this.validationSet && this.validationSet.errors) || [];
        },
        nutrientManagementConsents() {
            if (
                this.propertyDetail &&
                this.propertyDetail.ruralDetail &&
                this.propertyDetail.ruralDetail.nutrientManagementConsents &&
                this.propertyDetail.ruralDetail.nutrientManagementConsents.length > 0
            ) {
                return this.propertyDetail.ruralDetail.nutrientManagementConsents;
            }
            return [{ consentId: -1, linkedProperties: [] }];
        },
        taLandZoneClassification() {
            if (
                this.propertyDetail &&
                this.propertyDetail.landUse &&
                this.propertyDetail.landUse.landZone
            ) {
                return this.propertyDetail.landUse.landZone.category;
            }
            return null;
        },
    },
    methods: {
        async loadTAZoneClassification(taCode) {
            if (!this.taLandZoneClassification) this.landZoneOptions = [];

            await this.$store.dispatch('fetchTAZoneClassification', taCode);

            const classifications = this.$store.getters.getCategoryClassifications(this.taLandZoneClassification);
            if (!classifications) {
                throw new Error(`Couldn't find classification for ${this.taLandZoneClassification}`);
            }
            classifications.map(item => { item.label = [item.description, item.shortDescription].filter(Boolean).join(' - ');})
            this.propertyDetail.landUse.landZone = classifications.find(opt => opt.code === this.propertyDetail.landUse.landZone.code);
            this.landZoneOptions = classifications;
        },

        update(data) {
            this.$store.commit('propertyDraft/setSinglePropertyDetail', data);
        },
        
        updateNutrientDetails(index, key, value) {
            const nutrientConsentDetails = [...this.nutrientManagementConsents];
            const updatedData = { ...nutrientConsentDetails[index] };
            updatedData[key] = value;
            
            // Replace the old object with the updated one.
            nutrientConsentDetails.splice(index, 1, updatedData);
            this.update({
                id: 'nutrientManagementConsents',
                value: nutrientConsentDetails,
            });
        },
    },
    created() {
        if (this.property && this.property.territorialAuthority) {
            this.loadTAZoneClassification(this.property.territorialAuthority.code)
        }
    },
}

</script>
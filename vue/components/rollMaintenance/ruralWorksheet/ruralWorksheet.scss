
.router .bootstrap-ify {

    font-size:1.4rem;

    .page-mask {
        position:fixed;
        top:0;
        left:0;
        height:100%;
        width:100%;
        background:rgba(220,220,220,0.5);
        z-index:10;

        .loadingSpinnerWrapper {
            position: relative;
            top:50%;
            left:50%;
        }

        .loadingSpinnerBox {
            position: relative;
            height:100px;
            width:100px;
            transform: translate(-50%, -50%);
            background:#FFFFFF;
            border:1px solid rgba(237,241,245,.75);
            border-radius:3px;

            .loadingSpinner {
                background-position: 50% 50%;
                background-size: 4rem;
                margin: 0.4rem 0 0 0;
            }
        }
    }

    span.yes-no {
        display:inline-block;
        padding:0 10px;
        background:#a9a9a9;
        border-radius:3px;
        text-transform:uppercase;

        &[data-text='true'] {
            background:#ff0000;
        }
    }

    .section-title {
            
        li.li-link {
            padding: 2px 5px;
            border-radius:3px;
            margin-top:-2px;
            float: right;
            margin-left: 20px;
            margin-right: 20px;

            &:hover {
                cursor:pointer;
                background: rgba(255,255,255,0.2);
                text-decoration:underline;
            }
        }

         li.li-link-notClickable {
            padding: 2px 5px;
            border-radius:3px;
            margin-top:-2px;
            float: right;
            margin-left: 20px;
            margin-right: 20px;
        }
    }

    label {
        input:active, 
        input:focus, 
        select:active , 
        select:focus {
            border:2px solid #5290db;
        }

        select {
            -webkit-appearance: auto;
            -moz-appearance: initial;
            appearance: auto;
        }
    }

    label{
        span.property-value{
            line-height:39px;
            font-weight:bold;
        }

        span.change-label {
            display: block;
            color: #FFFFFF;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 12px;
            padding: 5px;
            font-size: 1.2rem;
            border-radius: 3px;
            background:#626970;

            &.positive{
                background:#4caf50;
                padding-left:22px;
            }

            &.negative{
                background:#fc3d39;
                padding-left:22px;
            }
        }

        .positive:before, .negative:before{
            position: absolute;
            content:'';
            top: calc(50% - 0.3rem);
            left: 0.7rem;
            border-left: 0.4rem solid transparent;
            border-right: 0.4rem solid transparent;
        }

        .positive:before {
            border-bottom:.4rem solid #fff;
        }

        .negative:before{
            border-top:.4rem solid #fff;
        }
    }

    .container-fluid {
        h3.section-title {
            background: #2c3c61;
            font-size:1.3rem;
            color:#FFF;
            font-weight:bold;
            text-transform:uppercase;
            padding: 5px 15px;
        }

        h4.section-title {
            border-bottom: 1px solid #2c3c61;
            color:#2c3c61;
            font-weight:bold;
            text-transform:uppercase;
            padding: 5px 5px 3px 5px;
        }

        .row.grey {

            h3.section-title {
                background: #5e5e5e;
            }

            h4.section-title {
                border-bottom: 1px solid #5e5e5e;
                color:#5e5e5e;
            }
        }

        div.message-warning {
            padding:10px;
            border: 1px solid #d8a951;
        }
    }

    table.table {
        tr {
            height:3rem;

            th {
                font-size:1.2rem;
                font-weight: 600;    
                color: #2c3c61;
                border-bottom:1px solid #2c3c61;
            }

            td{
                line-height:39px;
            }
        }
    }

    .QVHV-tabs {

        .button-link {

            width:auto;

            span{
                display:block;
                border: 1px solid #2c3c61;
                color:#2c3c61;
                padding: 5px 10px;
                margin: 0 5px;
                border-radius:3px;
                font-weight:bold;

                &.is-active {
                    border-bottom:3px solid #2c3c61;
                }
            }
            
            span:hover, span:active, span:focus {
                background-color: #2c3c61;
                color:#FFF;
                opacity:1;
            }

            span:active{
                background-color: #212e4d;
            }
        }

        li span.is-active {
            border-bottom:3px solid #2c3c61;
        }
    }

    .QVHV-box {

        border:1px solid rgba(237,241,245,.75);
        padding-bottom:30px;

        label {
            
            input:disabled, textarea:disabled, select:disabled {
                background-color: #ebf9ff;
                border: 1px solid #afdcef;
            }
            
            input:read-only, textarea:read-only {
                background-color: #ebf9ff;
                border: 1px solid #afdcef;
            }
        }

        .grey {
            label {
                input:disabled, textarea:disabled, select:disabled {
                    background-color: #fafafa;
                    border: 1px solid #dfdfdf;
                }

                input:read-only, textarea:read-only {
                    background-color: #fafafa;
                    border: 1px solid #dfdfdf;
                }
            }
        }
    }
        
    .container, .container-fluid {
        .row {

            &.table-head {

                // margin-left:0;
                // margin-right:0;

                div {
                    &[class*='col-']{
                        padding:5px;
                        color:#0e3a83;
                        font-size:1.1rem;
                        border-bottom:1px solid #2c3c61;
                    }

                    
                }

                div.sortable {
                    position:relative;

                    &:hover {
                        text-decoration:underline;
                        cursor:pointer;
                        background:#e8f8ff;
                    }

                    span {
                        display: block;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        right: 5px;
                        padding: 5px;
                        font-size: 1.5rem;
                        border-radius: 3px;
            
                        &.asc{
                            padding-left:22px;
                        }
            
                        &.desc{
                            padding-left:22px;
                        }

                    }

                    .asc:before, .desc:before{
                        position: absolute;
                        content:'';
                        top: calc(50% - 0.5rem);
                        left: 0.7rem;
                        border-left: 0.6rem solid transparent;
                        border-right: 0.6rem solid transparent;
                    }
            
                    .asc:before {
                        border-top:.8rem solid #0e3a83;
                    }
            
                    .desc:before{
                        border-bottom:.8rem solid #0e3a83;
                    }
                }
            }

            
            &.grey {

                .table-row {
                    &:nth-child(even){
                        background:#fafafa;
                    }   
                    &:nth-child(odd){
                        background:#f1f1f1;
                    } 
                    
                    &:last-child{
                        border-bottom:1px solid #dfdfdf;
                    }
                }
            }

            &.table-row {

                &:nth-child(even){
                    background:#f5fcff;
                }
                &:nth-child(odd){
                    background:#e8f8ff;
                }

                &:last-child{
                    border-bottom:1px solid #b6e9ff;
                }

                &.summary {
                    div {
                        &[class*='col-']{
                        }
                    }

                    &.bold  {
                        div {
                            &[class*='col-']{
                                font-weight:bold;
                            }
                        }
                    }
                }

                div {

                    &[class*='col-']{
                        padding:5px;
                    }
                    
                }

                .material-icons {
                    line-height:39px;
                }
            }

            > div {
                &[class*='col-']{

                    &:first-child {
                        padding-left:15px;
                    }
                    
                    &:last-child {
                        padding-right:15px;
                    }

                    &.space-rows {

                        > .row {
                            margin-top:5px;
                            margin-bottom:10px;
                        }
                    }
                }
            }
            
        }    
        
        &.space-rows {
            > .row {
                margin-top:10px;
                margin-bottom:10px;
            }
        }
    }

    /* TODO Quick hack to make all inputs similar height */
    label > input, label > select, label .mx-datepicker .mx-input-wrapper input {
        height: 39px !important;
    }

    .row {
        .invalid {

            input, select {
                border: 1px solid red;
            }
        }
    }

    /* Override Monarch wrapper to compact it a bit */
    &.contentWrapper {
        margin-top: 0;
        padding-top: 1rem;
    }

    .colHeader.center-header {
        text-align: center;
    }

    /* Override Monarch margins which are way too much. */
    .QVHV-tabs {
        padding:0;
    }

    .QVHV-buttons {
        margin-top: 0;
        margin-bottom: 0;
    }

    /* Override Monarch spinner which pulls it -4 rem up on to content above. */
    .loadingSpinner {
        margin-top: 1rem;
        display: block;
    }

    /* Set all text areas used as 'fields' to match input fonts */
    label textarea {
        font-family: 'Open Sans', 'Helvetica Neue', helvetica, helve, sans-serif !important;
    }

    /* Base styling copied from draft property */
    label {
        line-height: 1.5em;

        .label {
            display: block;
        }

        .field {
            display: block;
            font-size: 14px;
            line-height: 2.8em;
        }

        .readonly-text {
            line-height: 2.8em;
        }

        input, textarea, select, .mx-datepicker input {
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 5px;
            border: 1px solid #e8e8e8;
            background-color: #fff;
            /* Curios as to why block? Caused some funky layout?
            display: block; */
            margin: 0;
            width: 100%;

            &[readonly] {
                background-color: #eee;
            }
        }

        .mx-datepicker{
            width:100%;
        }

    }

    /* This copies existing monarch styling onto new pattern*/
    label {
        font-size: 1.2rem;

        .label {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #0e3a83;
            height: 2.2rem;
        }
        input[type=text],
        input[type=number],
        textarea, 
        select
         {
            font-size: 1.2rem;
            padding: .5rem;
            border: solid 1px #d2d2d2;
        }
        input, 
        select {
            height: 2.7rem;
        }
        input[type=checkbox] {
            width: 2.5rem;
            background-color: #fff;
        }
        input[type=radio] {
            width: 1.3rem;
            margin: -3px 5px 0 15px;
            vertical-align: middle;
        }
        textarea {
            overflow: auto;
        }
    }

    /* Red box for validation errors */
    label {
        input.error {
            border: 1px solid #f00 !important;
        }
    }

    /* TODO Refactor this hack which copies style from old Monarch. */
    h2.section-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0;
        margin-top: 0.5rem;
    }

    h3 {
        font-size:1.6rem;
    }

    /* Temporary colors for errors taken from bootstrap. */
    .bAlert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    .bAlert-warning {
        color: #9c6500;
        background-color: #ffeb9c;
        border-color: #ffe471;
    }

    .bAlert {
        position: relative;
        padding: .75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: .25rem;
    }

    .exception-message {
        font-size: 1.5em;
    }

    /* Special styling for the property banner proposed values slot */
    .md-summaryHeader ul.proposedValues{
        padding: 0 0 1.2rem .8rem;
        width: 55rem;

        li {
            display: inline-block;
            font-size: 1.8rem;
            margin: 0 .7rem;
            width: calc(25% - 1.6rem);
            vertical-align: top;
            color: #fff;

            label {
                display: block;
                font-size: 1.1rem;
                line-height: 2.4;
                border-bottom: .1rem solid rgba(255,255,255,.2);
                margin-bottom: .35rem;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            span {
                font-size: 1.2rem;
            }
        }
    }

    /* Special address hover copied from legacy Monarch - narrowed to be a clickable address */
    tr:hover a > .fullAddress {
        box-shadow:0 0 0 .15rem rgba(74,144,226,.25);
        box-sizing:border-box;
    }

    tr:hover a > .fullAddress:hover {
        color:#214d90;
        background:rgba(255,255,255,.25);
        box-shadow:0 0 0 .15rem rgba(255,111,0,.5);
    }

    tr:hover a > .fullAddress::after {
        position:absolute;
        top:.9rem;
        right:.8rem;
        content:'';
        background-image:url(../../../../public/images/monarchLogo-mini.png);
        background-repeat:no-repeat;
        background-size:100%;
        width:2rem;
        height:2rem;
    }

    tr:hover a > .fullAddress:hover::after { color:#fc932f; }

    /* Quick n dirty general message */
    .message {
        border: 1px solid transparent;
        border-radius: .25rem;
        padding: .75rem 1.25rem;
        margin-bottom: 1rem;
        margin-top: 0.5rem;
        font-size: 1.3rem;
    }
    .message-error {
        color: #721c24;
        border-color: #f5c6cb;
        background-color: #f8d7da;
    }
    .message-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
    }

}

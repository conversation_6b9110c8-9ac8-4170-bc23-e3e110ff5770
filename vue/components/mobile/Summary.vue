<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="summary-wrapper">
            <h2 data-cy="property-address" class="h2">{{ property.address1 }}<span>{{ property.address2 }}</span></h2>
            <ul data-cy="property-val-ref" class="propertyValref inlineLabel">
                <li><label>Val Ref:</label>{{ property.valuationReference }}</li>
            </ul>
            <ul data-cy="property-category" class="propertyCategory inlineLabel">
                <li><strong>{{ property.category.code }}:</strong> {{ property.category.description }}</li>
                <li><label>Effective:</label> {{ taSummary ? taSummary.currentRevisionDate : '' }}</li>
            </ul>
            <div data-cy="property-summary" class="propertySummary inlineLabel">
                <ul class="rate">
                    <li><label>Units:</label>{{ property.units }}</li>
                    <li><label>Total Floor Area:</label>{{ property.TFA }}<span>m<sup>2</sup></span></li>
                    <li><label>Total Living Area:</label>{{ property.TLA }}<span>m<sup>2</sup></span></li>
                    <li><label>Land Area:</label>{{ formatDecimal(property.landArea, 4) }}<span>Ha</span></li>
                </ul>
                <ul>
                    <li v-if="property.occupiers && property.occupiers.length > 0"><label>Occupier:</label>{{ property.occupiers[0].isNameSecret ? 'Not Available' : property.occupiers[0].fullName }}</li>
                    <li v-if="property.occupiers && property.occupiers.length > 1"><label>Occupier:</label>{{ property.occupiers[1].isNameSecret ? 'Not Available' : property.occupiers[1].fullName }}</li>
                    <li v-if="property.owners && property.owners.length > 0"><label>Owner:</label>{{ property.owners[0].isNameSecret ? 'Not Available' : property.owners[0].fullName }}</li>
                    <li><label>Improvements:</label>{{ property.natureOfImprovements }}</li>
                </ul>
            </div>
            <ul data-cy="property-sale-date" class="propertySale-date confirmedSale inlineLabel">
                <li>
                    <label>Last Sale:</label>{{ lsDate }}
                    <strong class="saleClass" v-if="sales && sales.length > 0">{{ lsSaleType ? lsSaleType.code : '' }}{{ lsSaleTenure ? lsSaleTenure.code : '' }}{{ lsPriceValueRelationship ? lsPriceValueRelationship.code : '' }}</strong>
                </li>
                <li v-if="lsStatus"><span>{{ lsStatus }}</span></li>
            </ul>
            <ul data-cy="property-sale-details" class="propertySale-details hrLabel rate">
                <li><label>Net Sale Price</label>{{ lsNetPrice ? lsNetPrice : '--' }}</li>
                <li><label>Chattels</label>{{ lsChattels ? lsChattels : '--' }}</li>
                <li><label>Analysed Land</label>{{ lsAnalysedLV ? lsAnalysedLV : '--' }}</li>
                <li><label>NSP/CV</label>{{ lsNSPCV ? lsNSPCV : '--' }}</li>
                <li><label>Sale BNR</label>{{ lsBuildingNetRate ? lsBuildingNetRate : '--' }}<span v-if="lsBuildingNetRate">m<sup>2</sup></span></li>
                <li><label>Gross Rate</label>{{ lsBuildingGrossRate ? lsBuildingGrossRate : '--' }}<span v-if="lsBuildingGrossRate">m<sup>2</sup></span></li>
            </ul>
            <ul data-cy="property-estimates" class="propertyEstimates roundBox">
                <li><label>RealTime Value</label>{{ realTimeValue }}
                    <span v-bind:class="[realTimeToCapitalValueUpDown ? (realTimeToCapitalValueUpDown < 0 ? 'valueDown' : 'valueUp') : '']">{{ realTimeToCapitalValue }}</span></li>
                <li><label>Market Estimate: <strong>{{ marketEstimateDate ? marketEstimateDate : ''}}</strong></label>{{ marketEstimateValue }}
                    <span v-bind:class="[marketEstimateToCapitalValueUpDown ? (marketEstimateToCapitalValueUpDown < 0 ? 'valueDown' : 'valueUp') : '']">{{ marketEstimateToCapitalValue }}</span></li>
            </ul>
            <ul data-cy="property-values" class="propertyValues hrLabel rate">
                <li><label>Capital Value</label>{{ property.capitalValue }} <div>{{property.cvNetRate}}<span>m<sup>2</sup></span></div></li>
                <li><label>Land Value</label>{{ property.landValue }} <div>{{property.lvNetRate}}<span>m<sup>2</sup></span></div></li>
                <li><label>Improvements</label>{{ property.valueOfImprovements }} <div>{{ property.viNetRate }}<span>m<sup>2</sup></span></div></li>
                <li><label>Building Net Rate</label>{{property.buildingNetRate}} <span>m<sup>2</sup></span></li>
            </ul>

            <div class="propertyPhotos"></div>

            <ul data-cy="property-data-icon-label" class="propertyData iconLabel roundBox">
                <li class="icons8-category-filled">		<label>Category</label>{{ property.category.code }}</li>
                <li class="icons8-calendar-filled">		<label>Effective Year Built</label>{{ property.effectiveYearBuilt }}</li>
                <li class="icons8-bed-filled">			<label>Bedrooms</label>{{ property.bedrooms }}</li>
                <li class="icons8-toilet-bowl-filled">	<label>Toilets</label>{{ property.toilets }}</li>
                <li class="icons8-room-filled">			<label>Units</label>{{ property.units }}</li>
                <li class="icons8-garage-closed-filled"><label>Free Standing Garaging</label>{{ property.freeStandingGarages }}</li>
                <li class="icons8-garage-filled">		<label>Under Main Roof Garaging</label>{{ property.underMainRoofGarages }}</li>
                <li class="icons8-swimming-pool">		<label>Other Large Improvements</label>{{ property.otherLargeImprovements }}</li>
                <li class="icons8-maintenance-filled">	<label>Modernisation</label>{{ property.modernisation }}</li>
                <li class="icons8-crosshair-filled">	<label>Zone</label>{{ property.zone }}</li>
                <li class="icons8-lot-position-filled">	<label>Lot Position</label>{{ property.lotPosition }}</li>
                <li class="icons8-maori-new">			<label>Maori Land</label>{{ property.maoriLand }}</li>
                <li class="icons8-land-use-new">		<label>Land Use</label>{{ property.landUse }}</li>
                <li class="icons8-brick-wall-filled">	<label>Wall Construction and Condition</label>{{ property.wallConstructionAndCondition }}</li>
                <li class="icons8-structural-filled ">	<label>Roof Construction and Condition</label>{{ property.roofConstructionAndCondition }}</li>
                <li class="icons8-field-filled">		<label>Contour</label>{{ property.contour }}</li>
                <li class="icons8-panorama">			<label>View and Scope</label>{{ property.viewScope }}</li>
                <li class="icons8-cow-filled">			<label>Production</label>{{ property.production }}</li>
            </ul>
        </div>
</template>
<script>
    import {EventBus} from '../../EventBus.js';
    import {store} from '../../DataStore';
    import { mapGetters } from 'vuex';
    import slick from '../../slick.js';
    import numeral from 'numeral';
    import PropertySummary from './Summary.vue';
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import propertyUtils from '../../utils/PropertyUtils';

    export default {
        props: ['property', 'sale', 'saleAnalysis', 'fromPhotoUpload'],
        mixins: [formatUtils, commonUtils, propertyUtils],
        data: function () {
            return {
                showTemplate: false,
                taSummary: null,
                sales: null,
                lsDate: null,
                lsStatus: null,
                lsNetPrice: null,
                lsChattels: null,
                lsAnalysedLV: null,
                lsNSPCV: null,
                lsBuildingGrossRate: null,
                lsBuildingNetRate: null,
                lsSaleType: null,
                lsSaleTenure: null,
                lsPriceValueRelationship: null,
                realTimeValue: null,
                realTimeToCapitalValue: null,
                realTimeToCapitalValueUpDown: null,
                marketEstimateDate: null,
                marketEstimateValue: null,
                marketEstimateToCapitalValue: null,
                marketEstimateToCapitalValueUpDown: null
            }
        },
        computed: {
            ...mapGetters(['getCategoryClassifications']),

            improvementDateRangeDescriptions() {
                const improvementDateRangeClassifications = this.getCategoryClassifications('ImprovementDateRange');
                // Convert the array of ImprovementDateRange Classifications into a key-value pair of classification code to classification description.
                return improvementDateRangeClassifications.reduce((accumulator, currentValue) => ({ ...accumulator, [currentValue.code]: currentValue.description }), {});
            },

            tagsDescriptions() {
                const tagsClassifications = this.getCategoryClassifications('Tags');
                // Convert the array of Tags Classifications into a key-value pair of classification code to classification description.
                return tagsClassifications.reduce((accumulator, currentValue) => ({ ...accumulator, [currentValue.code]: currentValue.description }), {});
            },
        },
        mounted: function () {
            var self = this;
            self.populateTASummary(self.property.territorialAuthorityId);
            if(self.sale) {
                self.lsDate = self.sale ? self.formatDate(self.sale.saleDate) : 'No sale on record';
                self.lsStatus = self.sale ? self.sale.status.description : null;
                self.lsNetPrice = self.sale && self.sale.price ? numeral(self.sale.price.net ? self.sale.price.net : 0).format('$0,0') : '';
                self.lsChattels = self.sale && self.sale.price ? numeral(self.sale.price.chattels ? self.sale.price.chattels : 0).format('$0,0') : '';
                self.lsAnalysedLV = self.saleAnalysis ? numeral(self.saleAnalysis.totalAnalysedLandValue ?
                        self.saleAnalysis.totalAnalysedLandValue : 0).format('$0,0') : '';
                self.lsSaleType = self.sale ? self.sale.saleType : null;
                self.lsSaleTenure = self.sale ? self.sale.saleTenure : null;
                self.lsPriceValueRelationship = self.sale ? self.sale.priceValueRelationship : null;

                var nSPCV = 0;
                var netPrice = self.sale && self.sale.price ? self.sale.price.net : 0;
                var capitalValue = numeral(self.property.capitalValue).value();
                if (capitalValue > 0) {
                    nSPCV = (Math.round((netPrice / capitalValue) * 100)) / 100;
                }
                self.lsNSPCV = self.sale ? nSPCV : '';

                var grossRate = 0;
                if (netPrice > 0 && self.property.TFA > 0) {
                    grossRate = Math.round(netPrice / self.property.TFA);
                }
                self.lsBuildingGrossRate = self.sale ? numeral(grossRate).format('$0,0') : '';
                self.lsBuildingNetRate = self.saleAnalysis ? numeral(self.saleAnalysis && self.saleAnalysis.analysedMainBuilding ?
                        self.saleAnalysis.analysedMainBuilding.pricePerSquareMeter : 0).format('$0,0') : '';

            } else {
                self.populateSales(self.property.qupid);
            }
            self.populateStatsSummary(self.property.qupid);
            if(self.fromPhotoUpload) {
                setTimeout(function() {
                    self.getPhotos(self.property.id);
                }, 4000);
            } else {
                self.getPhotos(self.property.id);
            }
        },
        methods: {
            populateTASummary: function (taCode) {
                var self = this;
                var m = jsRoutes.controllers.PropertyMasterData.getTASummary(taCode);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        self.taSummary = {};
                        self.taSummary.nextRatingValuationDate = response.nextRevisionDate ? 'As at ' + self.formatDate(response.nextRevisionDate, 'DD MMMM YYYY') : '';
                        self.taSummary.currentRevisionDate = response.currentRevisionDate ? self.formatDate(response.currentRevisionDate) : '';
                        self.taSummary.rawCurrentRevisionDate = response.currentRevisionDate ? response.currentRevisionDate : '';
                    },
                    error: function (response) {
                        console.log('error fetch property search results: ' + response);
                        self.errorHandler(response);
                    }
                });
            },
            populateSales: function (qupid) {
                const self = this;
                var m = jsRoutes.controllers.SalesSearch.getSalesByQupid();
                var postData = {locationCriteria: {qupid: qupid}, sort: ['SALE_DATE'], order: 'DESC', max: 1};
                $.ajax({
                    type: "POST",
                    url: m.url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(postData),
                    dataType: "json",
                    success: function (response) {
                        self.sales = response;
                        var latestSale = self.sales && self.sales.length > 0 ? self.sales[0] : null;
                        self.lsDate = latestSale ? self.formatDate(latestSale.saleDate) : 'No sale on record';
                        self.lsStatus = latestSale ? latestSale.status.description : null;
                        self.lsNetPrice = latestSale && latestSale.price ? numeral(latestSale.price.net ? latestSale.price.net : 0).format('$0,0') : null;
                        self.lsChattels = latestSale && latestSale.price ? numeral(latestSale.price.chattels ? latestSale.price.chattels : 0).format('$0,0') : null;
                        self.lsAnalysedLV = latestSale && latestSale.saleAnalysis ? numeral(latestSale.saleAnalysis.totalAnalysedLandValue ?
                                latestSale.saleAnalysis.totalAnalysedLandValue : 0).format('$0,0') : null;
                        self.lsSaleType = latestSale ? latestSale.saleType : null;
                        self.lsSaleTenure = latestSale ? latestSale.saleTenure : null;
                        self.lsPriceValueRelationship = latestSale ? latestSale.priceValueRelationship : null;

                        var nSPCV = 0;
                        var netPrice = latestSale && latestSale.price ? latestSale.price.net : 0;
                        var capitalValue = numeral(self.property.capitalValue).value();
                        if (capitalValue > 0) {
                            nSPCV = (Math.round((netPrice / capitalValue) * 100)) / 100;
                        }
                        self.lsNSPCV = latestSale ? nSPCV : null;

                        var grossRate = 0;
                        if (netPrice > 0 && self.property.TFA > 0) {
                            grossRate = Math.round(netPrice / self.property.TFA);
                        }
                        self.lsBuildingGrossRate = latestSale ? numeral(grossRate).format('$0,0') : null;
                        self.lsBuildingNetRate = latestSale && latestSale.saleAnalysis ? numeral(latestSale.saleAnalysis.analysedMainBuilding ?
                                latestSale.saleAnalysis.analysedMainBuilding.pricePerSquareMeter : 0).format('$0,0') : null;

                    },
                    error: function (response) {
                        console.log('error fetching sales for property: ' + response);
                        self.sales = [];
                        self.errorHandler(response);
                    }
                });
            },
            populateStatsSummary: function (qupid) {
                var self = this;
                var m = jsRoutes.controllers.PropertyMasterData.getStatsSummary(qupid);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        var data = response;
                        var rawMarketEstimateValue = data.marketEstimateValue ? data.marketEstimateValue : 0;
                        var capitalValue = numeral(self.property.capitalValue).value();
                        self.marketEstimateDate = data.marketEstimateDate ? self.formatDate(data.marketEstimateDate) : '';
                        self.marketEstimateValue = data.marketEstimateValue ? numeral(data.marketEstimateValue).format('$0,0') : 'N/A';
                        self.marketEstimateToCapitalValue = capitalValue && data.marketEstimateValue ? Math.round(((rawMarketEstimateValue * 100) / capitalValue) - 100) + '%' : '-';
                        self.marketEstimateToCapitalValueUpDown = capitalValue && data.marketEstimateValue ? Math.round(((rawMarketEstimateValue * 100) / capitalValue) - 100) : null;
                        var rawRealTimeValue = data.realTimeValue ? data.realTimeValue : 0;
                        self.realTimeValue = data.realTimeValue ? numeral(Math.round(data.realTimeValue / 1000) * 1000).format('$0,0') : 'N/A';
                        self.realTimeToCapitalValue = capitalValue && data.realTimeValue ? Math.round(((rawRealTimeValue * 100) / capitalValue) - 100) + '%' : '-';
                        self.realTimeToCapitalValueUpDown = capitalValue && data.realTimeValue ? Math.round(((rawRealTimeValue * 100) / capitalValue) - 100) : null;
                    },
                    error: function (response) {
                        console.log('error fetch property search results: ' + response);
                        self.errorHandler(response);
                    }
                });
            },
            getPhotos: function(propertyId) {
                var self = this;
                $(".propertyPhotos").not('.slick-initialized').slick({
                    dots: true,
                    infinite: false,
                    speed: 300,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    swipe: false
                });
                var media = jsRoutes.controllers.MediaController.getMediaByOwner(propertyId);
                $.ajax({
                    type: "GET",
                    url: media.url,
                    cache: false,
                    success: function (response) {
                        $.each(response, function (index, photo) {
                            var photoSlick = self.generateSlickElement((self.fromPhotoUpload == true && index == 0) ? photo.mediaItem.originalImageUrl : photo.mediaItem.largeImageUrl, photo);
                            $('.propertyPhotos').slick('slickAdd', photoSlick);
                        });
                    },
                    error: function (response) {
                        console.log("Unable to get photos");
                        self.errorHandler(response);
                    }
                });
            },
            generateSlickElement: function(summaryPhoto, photo) {
                var slickElem =
                    `<div class=\"slick-slide\">
                        <img src=\"${summaryPhoto}\"/>
                        <ul class=\"galleryCaption\">" +
                            <li><strong>${photo.description ? photo.description : ''}</strong></li>
                            <li>${this.generatePhotoTags(photo.mediaItem.improvementDateRange, photo.mediaItem.tags)}</li>
                            <li>${this.formatDate(photo.mediaItem.captureDate)}</li>
                        </ul>
                    </div>`;
                return slickElem;
            },
            /**
             * This method takes Improvement Date Range code and Tags code and returns the comma-separated list of descriptions
             */
            generatePhotoTags(improvementDateRange, tags) {
                // Retrieve the list of ImprovementDateRange description
                const improvementDateRangeDescription = this.improvementDateRangeDescriptions[improvementDateRange];
                // Sort the tags array, then for each tag, retrieve the Tag classification
                const tagsToDisplay = [...tags].sort().map(tag => this.tagsDescriptions[tag]);
                // Array of photo tags and the date range.
                const displayedTags = [improvementDateRangeDescription, ...tagsToDisplay].filter(description => !!description);
                return displayedTags.join(', ');
            },
        }
    }

</script>

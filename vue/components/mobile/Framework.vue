<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="monarchMobile-wrapper" v-if="showTemplate">
        <!-- MOBILE HEADER -->
        <header>
            <ul>
                <li v-if="selectedTab == 'comps' && job" class="comparablesList-back" style="display:none">
                    <i class="material-icons">arrow_back_ios</i><label>Back</label>
                </li>
                <li class="home-back" @click="showHome()"><i class="material-icons">home</i></li>
            </ul>
        </header>

        <!-- MAIN VIEW -->
        <main class="summary-view" v-if="selectedTab == 'summary'">
            <property-summary :property="property" fromPhotoUpload="false"></property-summary>
        </main>
        <edit-details v-if="selectedTab == 'details' && job" :job="job" :readOnly="isReadOnly"></edit-details>
        <div action="no/presigned/url/assigned" v-show="showDropzone" id="photo-dropzone-mobile-valuation"></div>
        <comparables v-if="selectedTab == 'comps' && job" :job="job" :property="property" :readOnly="isReadOnly"></comparables>
        <job-info v-if="selectedTab == 'job' && job" :job="job"></job-info>

        <!-- MOBILE FOOTER -->
        <footer v-if="showTemplate">
            <a @click="selectedTab = 'summary'" data-cy="valuation-job-summary"><span class="summary-link"><i class="material-icons">web</i><label>Summary</label></span></a>
            <a @click="selectedTab = 'details'" data-cy="valuation-job-edit-details"><span class="editDetails-link"><i class="material-icons">edit</i><label>Edit Details</label></span></a>
            <a data-cy="valuation-job-add-photos" id="takePhoto" v-bind:style="{ 'pointer-events': isReadOnly ? 'none' : 'auto', 'opacity': isReadOnly ? '0.5' : '1' }"><span class="addPhotos-link"><i class="material-icons">photo_camera</i><label>Add Photos</label></span></a>
            <a @click="selectedTab = 'comps'" data-cy="valuation-job-comparables"><span class="comparablesList-link"><i class="material-icons">burst_mode</i><label>Comparables</label></span></a>
            <a @click="selectedTab = 'job'" data-cy="valuation-job-information"><span class="jobInfo-link"><i class="material-icons">content_paste</i><label>Job Info</label></span></a>
        </footer>
    </div>
</template>
<script>
    import {mapState} from 'vuex';
    import {EventBus} from '../../EventBus.js';
    import {store} from '../../DataStore';
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import propertyUtils from '../../utils/PropertyUtils';
    import PropertySummary from './Summary.vue';
    import EditDetails from './EditDetails.vue';
    import Comparables from './Comparables.vue';
    import JobInfo from './JobInfo.vue';
    import * as loadImage from 'blueimp-load-image';
    import * as Dropzone from '../../dropzone';

    export default {
        props: ['propertyId', 'jobId'],
        mixins: [formatUtils, commonUtils, propertyUtils],
        components: {
            PropertySummary,
            EditDetails,
            Comparables,
            JobInfo
        },
        data: function () {
            return {
                showTemplate: true,
                refreshPropertyPlusTemplate : false,
                selectedTab: "null",
                property: null,
                job: null,
                taSummary: null,
                saveCounter: 0,
                readOnly: false,
                mediaDropped: [],
                mediaProcessed: [],
                showDropzone: false,
                dropzone: null
            }
        },
        computed: {
            ...mapState('userData', [
                'qivsUrl'
            ]),
            isReadOnly: function () {
                return this.isHomeValuationReadOnly(this.job ? this.job.status : null, this.job ? this.job.valuer : null, this.job ? this.job.countersigner : null);
            }
        },
        mounted: function () {
            var self = this;
            self.getPropertyDetails(self.propertyId, function() {
                self.selectedTab = "summary";
                self.refreshPropertyPlusDisplay();
            });
            self.getValuationJob(self.jobId);

            EventBus.$on('mobile-home-valuation-save', function (event) {
                self.saveHomeValuation();
            });
            self.registerMobilePhotoUploader();

            $('.comparablesList-back').off("click").click(function () {
                self.showHome();
            });
        },
        methods: {
            refreshPropertyPlusDisplay: function () {
                var self = this;
                self.showPropertyPlusTemplate = !self.showPropertyPlusTemplate;
                if (!self.showPropertyPlusTemplate) {
                    self.refreshPropertyPlusTemplate = true;
                }
            },
            getPropertyDetails: function (id, callback) {
                var self = this;
                var m = jsRoutes.controllers.PropertyMasterData.getProperty(id);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        self.property = self.generateMasterDetailsData(response, self.qivsUrl);
                        if (callback) callback();
                    },
                    error: function (response) {
                        console.log('error fetch property search results: ' + response);
                        self.errorHandler(response);
                    }
                });
            },
            showHome: function () {
                window.location = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?home=true';
            },
            getValuationJob: function (jobId) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.HomeValuation.getHomeValuation(jobId).url,
                    cache: false,
                    success: function (response) {
                        self.job = response;
                    },
                    error: function (response) {
                        console.log("Error in getting home valuation");
                        self.errorHandler(response);
                    }
                });
            },
            checkVersion: function (callback1) {
                var self = this;
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.HomeValuation.getHomeValuation(self.job.id).url,
                    cache: false,
                    success: function (response) {
                        if (response.entityVersion == self.job.entityVersion) {
                            if (callback1) callback1();
                        } else {
                            self.job = response;
                        }
                    },
                    error: function (response) {
                        console.log("Error in getting home valuation");
                        self.errorHandler(response);
                    }
                });
            },
            saveHomeValuation: function () {
                var self = this;
                self.checkVersion(function () {
                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.HomeValuation.saveHomeValuation().url,
                        cache: false,
                        processData: false,
                        contentType: 'application/json',
                        data: JSON.stringify(self.job),
                        success: function (response) {
                            self.job.entityVersion = response.entityVersion;
                            self.saveCounter = 0;
                        },
                        error: function (response) {
                            if (self.saveCounter < 5) {
                                console.log('Retrying save: ' + self.saveCounter);
                                self.saveCounter++;
                                setTimeout(function () {
                                    self.saveHomeValuation();
                                }, 500);
                            } else {
                                console.log('There was an error saving the valuation job.');
                                self.saveCounter = 0;
                                self.errorHandler(response);
                            }
                        }
                    });
                });
            },
            getMediaIndexField: function (propertyName, value) {
                var self = this;
                for (var i = 0; i < self.mediaDropped.length; i++)
                    if (self.mediaDropped[i][propertyName] === value && self.mediaDropped[i].uploaded === false) {
                        return i;
                    }
                return -1;
            },
            onDropzoneDone: function (success, file) {
                var self = this;
                var i = self.getMediaIndexField("mediaId", file.mediaId);
                if (self.mediaDropped[i] && file.status == 'success') {
                    var mediaEntry = self.mediaDropped[i].mediaEntry;
                    mediaEntry.mediaItem.status = success ? "UPLOAD_COMPLETED" : "UPLOAD_FAILED";
                    var url = jsRoutes.controllers.MediaController.saveMedia().url;
                    $.ajax({
                        type: "POST",
                        cache: false,
                        url: url,
                        processData: false,
                        contentType: 'application/json',
                        data: JSON.stringify(mediaEntry)
                    }).done(function (response) {
                        if (self.mediaProcessed.indexOf(file.mediaId) < 0 && file.mediaId) self.mediaProcessed.push(file.mediaId);
                        self.showDropzone = false
                    });
                } else {
                    if (self.mediaProcessed.indexOf(file.mediaId) < 0 && file.mediaId) self.mediaProcessed.push(file.mediaId);
                    self.showDropzone = false
                }
            },
            initializeDropzone: function () {
                var self = this;
                Dropzone.autoDiscover = false;
                self.dropzone = new Dropzone("#photo-dropzone-mobile-valuation", {
                    autoDiscover: false,
                    acceptedFiles: '.png,.jpg,.gif,.bmp,.jpeg,.pdf,.xls,.xlsx,.doc,.docx,.tif,.tiff',
                    autoProcessQueue: false,
                    method: 'PUT',
                    contentType: false,
                    maxFiles: 1,
                    uploadMultiple: false
                });

                self.dropzone.on("success", function (file) {
                    self.onDropzoneDone(true, file);
                });

                self.dropzone.on("error", function (file) {
                    self.onDropzoneDone(false, file);
                });
            },
            registerMobilePhotoUploader: function () {
                const self = this;
                var camera = (function () {
                    'use strict';
                    var options;
                    var $input;

                    function photoSend(blob) {
                        if (options.callback_blob) options.callback_blob(blob);
                        if (!options.xhr && !options.callback_blob && !options.callback_canvas)
                            return alert('Options need either callback_blob or xhr');
                        // Send with Formdata
                        var name = options.xhr_name() + '.' + options.photo_ext;
                        var formData = new FormData();
                        blob.name = name;// if a blob has a name it is a File
                        formData.append('camera', blob, name);
                        var xhr = new XMLHttpRequest();
                        xhr.open('post', options.xhr, true);
                        xhr.upload.onprogress = function (e) {
                            if (e.lengthComputable) {
                                var percentage = Math.round((e.loaded / e.total) * 100);
                                options.callback_xhr && options.callback_xhr(percentage);
                            }
                        };

                        xhr.onerror = function (e) { // TODO
                            options.callback_xhr && options.callback_xhr('An error occurred while submitting the form. Maybe your file is too big' + e);
                        };

                        xhr.onload = function () {
                            options.callback_xhr && options.callback_xhr(this);
                        };

                        xhr.send(formData);
                    }

                    function photoResize(evt) {
                        var files = evt.target.files;
                        var opts = {
                            maxWidth: options.photo_max_size,
                            maxHeight: options.photo_max_size,
                            canvas: true
                        };
                        $.each(files, function (i, file) {
                            loadImage.parseMetaData(file, function (data) {
                                if (data.exif) opts.orientation = data.exif.get('Orientation');
                                if (file) {
                                    const captureDateString = self.convertToNZDate(
                                        file.lastModified ||
                                        new Date(Date.now()).getTime()
                                    );
                                    var data = {
                                        "ownerId": self.jobId,
                                        "category": "HomeValuationPhoto",
                                        "isPrimary": false,
                                        "description": null,
                                        "tags": [],
                                        "mediaItem": {
                                            "fileName": file.name,
                                            "contentType": (file.type == 'image/tif') ? 'image/tiff' : file.type,
                                            "uploadedDate": self.convertToNZDate(new Date(Date.now()).getTime()),
                                            "captureDate": captureDateString,
                                            "tags": [],
                                            "improvementDateRange": ""
                                        }
                                    };

                                    $.ajax({
                                        type: "POST",
                                        cache: false,
                                        url: jsRoutes.controllers.MediaController.generateMediaEntryID().url,
                                        processData: false,
                                        contentType: 'application/json',
                                        data: JSON.stringify(data)
                                    }).done(function (response) {
                                        self.dropzone.options.url = response.mediaItem.imageUrl;
                                        self.dropzone.options.headers = {'Content-Type': (file.type == 'image/tif') ? 'image/tiff' : file.type};
                                        self.mediaDropped.push({
                                            mediaId: response.mediaItem.id,
                                            fileName: response.mediaItem.fileName,
                                            mediaEntry: response,
                                            uploaded: false
                                        });
                                        file.mediaId = response.mediaItem.id;
                                        self.dropzone.processFile(file);
                                    });
                                }
                            });
                        });
                    }

                    function makeInputTag() {
                        $input = $('<input type="file" accept="image/*" style="visibility:hidden" multiple>')
                                .appendTo('body')
                                .change(photoResize);
                        return $input;
                    }

                    function getInputTag() {
                        return $input || makeInputTag();
                    }

                    function getTimestamp() {
                        return Date.now();
                    }

                    return function (options_) {
                        options = $.extend({
                            photo_max_size: Infinity,     // or 800
                            photo_jpeg_quality: 0.7,         // 0-1 only for jpeg
                            xhr: null,        // '/post/path/fotoupload',
                            xhr_name: getTimestamp,// function returns string
                            callback_xhr: null,        // function(s){$div.html((s===true)?'Upload
                                                       // finished':(isNaN(s))?'Error'+s:s
                                                       // + '%');} // true ... ok, number
                                                       // ... percentage, string ... error
                            callback_blob: null,        // function(blob){do_something},
                            callback_canvas: null         // function(canvas){$('#image').attr('src',
                                                          // canvas.toDataUrl());}
                        }, options_, {
                            photo_type: 'image/jpeg', // can not be changed
                            photo_ext: 'jpg'        // can not be changed
                        });
                        getInputTag().trigger('click');
                    };
                }());

                var showPhoto = function (canvas) {
                    $('#image').attr('src', canvas.toDataUrl());
                };

                $('#takePhoto').off("click").click(function () {
                    self.showDropzone = true;
                    if(!self.dropzone)self.initializeDropzone();
                    camera({
                        photo_jpeg_quality: 0.7,       // jpeg-quality from 0 to 1.0
                        xhr: '/foto',   // url of where the photo shall be
                        callback_xhr: null,  // show some info on upload progress
                        callback_canvas: showPhoto  // display the photo in an img-element
                    });
                });
            }
        },
        updated: function() {
            if (this.refreshPropertyPlusTemplate) {
                this.showPropertyPlusTemplate = true;
                this.refreshPropertyPlusTemplate = false;
            }

            $(window).data('ajaxready', true).scroll(function (e) {
                var scroll = $(document).scrollTop();
                var headerHeight = $('header, nav').outerHeight();
                //console.log(headerHeight);

                $(window).scroll(function () {
                    // scrolled is new position just obtained
                    var scrolled = $(document).scrollTop();

                    // optionally emulate non-fixed positioning behaviour

                    if (scrolled > headerHeight) {
                        $('header, nav').addClass('off-canvas');
                    } else {
                        $('header, nav').removeClass('off-canvas');
                    }
                    if (scrolled > scroll) {
                        // scrolling down
                        $('header, nav').removeClass('fixed');
                    } else {
                        //scrolling up
                        $('header, nav').addClass('fixed');
                    }
                    scroll = $(document).scrollTop();
                });
            });
        },
        destroyed: function () {
            EventBus.$off('mobile-home-valuation-save', this.listener);
        },
        beforeDestroy: function () {
            EventBus.$off('mobile-home-valuation-save', this.listener);
        }
    }
</script>

<style>
    @import "/assets/stylesheets/mobile/reset.css";
    @import "/assets/stylesheets/mobile/normalize.css";
    @import "/assets/stylesheets/slick/slick.css";
    @import "/assets/stylesheets/slick/slick-theme.css";
    @import "/assets/stylesheets/mobile/dropzone.css";
    @import "/assets/stylesheets/mobile/qv-icons.css";
    @import "/assets/stylesheets/mobile/mobile-grid.css";
    @import "/assets/stylesheets/mobile/mobile-summary-grid.css";
    @import "/assets/stylesheets/mobile/mobile-editdetails-grid.css";
    @import "/assets/stylesheets/mobile/mobile-addphotos-grid.css";
    @import "/assets/stylesheets/mobile/mobile-comparables-grid.css";
    @import "/assets/stylesheets/mobile/mobile-jobinfo-grid.css";
</style>

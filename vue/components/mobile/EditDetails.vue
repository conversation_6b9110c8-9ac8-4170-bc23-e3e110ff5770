<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <main class="editDetails-view">

        <div class="editDetails-wrapper">

            <nav>
                <ul class="tabs">
                    <li class="editDetails-tab--1" data-tab="editDetails-tab--1" data-container="overview" @click="saveJob()">
                        <span class="is-active"><i class="material-icons"></i>Overview</span>
                    </li>
                    <li class="editDetails-tab--2" data-tab="editDetails-tab--2" data-container="interior" @click="saveJob()">
                        <span> <i class="material-icons"></i>Interior</span>
                    </li>
                    <li class="editDetails-tab--3" data-tab="editDetails-tab--3" data-container="bedrooms" @click="saveJob()">
                        <span><i class="material-icons"></i>Bedrooms</span>
                    </li>
                    <li class="editDetails-tab--4" data-tab="editDetails-tab--4" data-container="kitchen" @click="saveJob()">
                        <span><i class="material-icons"></i>Kitchen</span>
                    </li>
                    <li class="editDetails-tab--5" data-tab="editDetails-tab--5" data-container="bathrooms" @click="saveJob()">
                        <span><i class="material-icons"></i>Bathrooms</span>
                    </li>
                    <li class="editDetails-tab--6" data-tab="editDetails-tab--6" data-container="garaging" @click="saveJob()">
                        <span><i class="material-icons"></i>Garaging </span>
                    </li>
                    <li class="editDetails-tab--7" data-tab="editDetails-tab--7" data-container="improvements" @click="saveJob()">
                        <span><i class="material-icons"></i>Improvements</span>
                    </li>
                </ul>
            </nav>

            <overview v-if="job" :job="job" :readOnly="readOnly"></overview>

            <interior v-if="job" :job="job" :readOnly="readOnly"></interior>

            <bedrooms v-if="job" :job="job" :readOnly="readOnly"></bedrooms>

            <kitchen v-if="job" :job="job" :readOnly="readOnly"></kitchen>

            <bathrooms v-if="job" :job="job" :readOnly="readOnly"></bathrooms>

            <garaging v-if="job" :job="job" :readOnly="readOnly"></garaging>

            <improvements v-if="job" :job="job" :readOnly="readOnly"></improvements>

            <div class="form-save" v-bind:style="{ 'pointer-events': readOnly ? 'none' : 'auto', 'opacity': readOnly ? '0.5' : '1' }">
                <button class="button primary" @click="saveJob()"><i class="material-icons">save</i>Save Changes</button>
            </div>

        </div>

    </main>
</template>
<script>
    import {EventBus} from '../../EventBus.js';
    import {store} from '../../DataStore';
    import Overview from './editDetails/Overview.vue';
    import Interior from './editDetails/Interior.vue';
    import Bedrooms from './editDetails/Bedrooms.vue';
    import Kitchen from './editDetails/Kitchen.vue';
    import Bathrooms from './editDetails/Bathrooms.vue';
    import Garaging from './editDetails/Garaging.vue';
    import Improvements from './editDetails/Improvements.vue';
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import propertyUtils from '../../utils/PropertyUtils';

    export default {
        props: ['job', 'readOnly'],
        mixins: [formatUtils, commonUtils, propertyUtils],
        components: {
            Overview,
            Interior,
            Bedrooms,
            Kitchen,
            Bathrooms,
            Garaging,
            Improvements
        },
        mounted: function () {
            var self = this;
            self.initTabs();
        },
        created: function () {
            var self = this;
        },
        updated: function () {
            var self = this;
        },
        methods: {
            saveJob: function() {
                EventBus.$emit('mobile-home-valuation-save', "save");
            }
        }
    }

</script>
<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="comparablesSummary-wrapper">
        <nav>
            <ul class="tabs">
                <li class="comparablesSummary-tab--1" data-tab="comparablesSummary-tab--1" data-container="comparablesSummary"><span class="is-active"><i class="material-icons"></i>Summary</span></li>
                <li class="comparablesSummary-tab--2" data-tab="comparablesSummary-tab--2" data-container="comparablesSummaryPhoto"><span><i class="material-icons"></i>Photo</span></li>
                <li class="comparablesSummary-tab--3" data-tab="comparablesSummary-tab--3" data-container="comparablesSummaryComment"><span><i class="material-icons"></i>Comment</span></li>
            </ul>
        </nav>

        <!-- COMPARABLES LIST VIEW/TAB STARTS HERE -->
        <div class="tabBlock-wrapper comparablesSummary default-section">
            <comp-summary :property="comp.property" :sale="comp.sale" :saleAnalysis="comp.saleAnalysis" :fromPhotoUpload="fromPhotoUpload" v-if="showSummary"></comp-summary>
        </div>

        <div class="tabBlock-wrapper comparablesSummaryPhoto" v-bind:style="{ 'pointer-events': readOnly ? 'none' : 'auto', 'opacity': readOnly ? '0.5' : '1' }">
            <div action="no/presigned/url/assigned" v-show="false" id="photo-dropzone-comp"></div>
            <div class="dropzone" id="my-awesome-dropzone" v-show="showPhotoUpload">
                <ul class="editComparables-item">
                    <li class="Add_a_Primary_Photo_for_this_Property">
                        <i class="material-icons">camera</i>
                        <div>
                            <label>Add a Primary Photo for this Property</label>
                            <input type="file" name="file" id="compTakePhoto">
                        </div>
                    </li>
                </ul>
            </div>
            <ul id="previewImages" class="cpm-uploadPhoto" v-show="showPreview">
                <li class="buttonRow">
                    <button @click="cancelPhoto()"><i class="material-icons cpm-blue">cancel</i></button>
                    <button @click="savePhoto()"><i class="material-icons cpm-blue">check_circle</i></button>
                </li>
            </ul>
        </div>

        <div class="tabBlock-wrapper comparablesSummaryComment" v-bind:style="{ 'pointer-events': readOnly ? 'none' : 'auto', 'opacity': readOnly ? '0.5' : '1' }">
            <ul class="editComparables-item">
                <li class="Comment">
                    <i class="icons8-pencil"></i>
                    <div>
                        <label>Sales Analysis Comment</label>
                        <textarea class="textarea textarea_xlarge" textarea="" type="text" title="Comment" v-model="comp.saleAnalysis.saleComment"></textarea>
                        <span class="valMessage"></span>
                    </div>
                </li>
            </ul>
            <div class="form-save">
                <button class="button primary" @click="saveComment()"><i class="material-icons">save</i>Save Changes</button>
            </div>
        </div>

    </div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';
    import CompSummary from '../Summary.vue';
    import * as loadImage from 'blueimp-load-image';
    const Dropzone = require('../../../dropzone');

    export default {
        props: ['comp', 'readOnly'],
        mixins: [commonUtils],
        components: {
            CompSummary
        },
        data: function () {
            return {
                currentFile: undefined,
                showSummary: true,
                showPhotoUpload: true,
                showPreview: false,
                mediaDropped: [],
                mediaProcessed: [],
                dropzone: null,
                fromPhotoUpload: false
            }
        },
        mounted: function () {
            var self = this;
            $('.comparablesList-back').show();
            self.initTabs();
            self.registerMobilePhotoUploader();
        },
        methods: {
            saveComment: function() {
                var self = this;
                var m = jsRoutes.controllers.SalesAnalysis.updateSalesAnalysis();
                $.ajax({
                    type: "POST",
                    url: m.url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(self.comp.saleAnalysis),
                    dataType: "json",
                    success: function (response) {
                        self.comp.saleAnalysis = response;
                    },
                    error: function (response) {
                        console.log('Error in updating sales analysis');
                        self.errorHandler(response);
                    }
                });
            },
            registerMobilePhotoUploader: function(){
                var self = this;
                var camera = (function() {
                    'use strict';
                    var options;
                    var $input;

                    function photoSend(blob){
                        if (options.callback_blob) options.callback_blob(blob);
                        if (!options.xhr && !options.callback_blob && !options.callback_canvas)
                            return alert('Options need either callback_blob or xhr');
                        // Send with Formdata
                        var name = options.xhr_name() + '.' + options.photo_ext;
                        var formData = new FormData();
                        blob.name = name;// if a blob has a name it is a File
                        formData.append('camera', blob, name);
                        var xhr = new XMLHttpRequest();
                        xhr.open('post', options.xhr, true);
                        xhr.upload.onprogress = function(e) {
                            if (e.lengthComputable) {
                                var percentage = Math.round((e.loaded / e.total) * 100);
                                options.callback_xhr && options.callback_xhr(percentage);
                            }
                        };

                        xhr.onerror = function(e) { // TODO
                            options.callback_xhr && options.callback_xhr('An error occurred while submitting the form. Maybe your file is too big' +e);
                        };

                        xhr.onload = function() {
                            options.callback_xhr && options.callback_xhr(this);
                        };

                        xhr.send(formData);
                    }

                    function canvasSend(canvas){
                        self.showPreview = true;
                        var div = $('<li class="imageHolder"><i class="material-icons cpm-light imageDelete">delete</i></li>');
                        div.append(canvas);
                        $('#previewImages').append(div);
                        self.showPhotoUpload = false;
                        $('.imageHolder').off("click").click(function(){
                            self.cancelPhoto();
                        });
                    }

                    function photoResize(evt) {
                        var file = evt.target.files[0];
                        var opts = {
                            maxWidth: options.photo_max_size,
                            maxHeight: options.photo_max_size,
                            canvas: true
                        };
                        loadImage.parseMetaData(file, function (data) {
                            self.currentFile = file
                            if (data.exif) opts.orientation = data.exif.get('Orientation');
                            loadImage(file, canvasSend, opts);
                        });
                    }

                    function makeInputTag(){
                        $input = $('<input type="file" accept="image/*" capture="camera" style="visibility:hidden">')
                                .appendTo('body')
                                .change(photoResize);
                        return $input;
                    }

                    function getInputTag() {
                        return $input || makeInputTag();
                    }

                    function getTimestamp(){return Date.now();}

                    return function(options_){
                        options = $.extend({
                            photo_max_size:     Infinity,     // or 800
                            photo_jpeg_quality:  0.7,         // 0-1 only for jpeg
                            xhr:                 null,        // '/post/path/fotoupload',
                            xhr_name:            getTimestamp,// function returns string
                            callback_xhr:        null,        // function(s){$div.html((s===true)?'Upload
                                                              // finished':(isNaN(s))?'Error'+s:s
                                                              // + '%');} // true ... ok, number
                                                              // ... percentage, string ... error
                            callback_blob:       null,        // function(blob){do_something},
                            callback_canvas:     null         // function(canvas){$('#image').attr('src',
                                                              // canvas.toDataUrl());}
                        },options_,{
                            photo_type:         'image/jpeg', // can not be changed
                            photo_ext:          'jpg'        // can not be changed
                        });
                        getInputTag().trigger('click');
                    };
                }());

                var showPhoto = function(canvas){$('#image').attr('src', canvas.toDataUrl());};

                $('#compTakePhoto').off("click").click(function(event){
                    event.preventDefault();
                    if(!self.dropzone) self.initializeDropzone();
                    camera({
                        photo_jpeg_quality: 0.7,       // jpeg-quality from 0 to 1.0
                        xhr:                '/foto',   // url of where the photo shall be
                        callback_xhr:       null,  // show some info on upload progress
                        callback_canvas:   showPhoto  // display the photo in an img-element
                    });
                });

            },
            getMediaIndexField: function (propertyName, value) {
                var self = this;
                for (var i = 0; i < self.mediaDropped.length; i++)
                    if (self.mediaDropped[i][propertyName] === value && self.mediaDropped[i].uploaded === false) {
                        return i;
                    }
                return -1;
            },
            onDropzoneDone: function (success, file) {
                var self = this;
                var i = self.getMediaIndexField("mediaId", file.mediaId);
                if (self.mediaDropped[i] && file.status == 'success') {
                    self.showSummary = false;
                    var mediaEntry = self.mediaDropped[i].mediaEntry;
                    mediaEntry.mediaItem.status = success ? "UPLOAD_COMPLETED" : "UPLOAD_FAILED";
                    var url = jsRoutes.controllers.MediaController.saveMedia().url;
                    $.ajax({
                        type: "POST",
                        cache: false,
                        url: url,
                        processData: false,
                        contentType: 'application/json',
                        data: JSON.stringify(mediaEntry)
                    }).done(function (response) {
                        if (self.mediaProcessed.indexOf(file.mediaId) < 0 && file.mediaId) self.mediaProcessed.push(file.mediaId);
                        self.comp.property.primaryPhoto = response.mediaItem.smallImageUrl;
                        self.showPhotoUpload = false;
                        self.fromPhotoUpload = true;
                        self.showSummary = true;
                        $('.comparablesSummary-tab--1').click();
                        self.cancelPhoto();
                    });
                } else {
                    if (self.mediaProcessed.indexOf(file.mediaId) < 0 && file.mediaId) self.mediaProcessed.push(file.mediaId);
                    self.showPhotoUpload = false;
                }
            },
            initializeDropzone: function () {
                var self = this;
                Dropzone.autoDiscover = false;
                self.dropzone = new Dropzone("#photo-dropzone-comp", {
                    autoDiscover: false,
                    acceptedFiles: '.png,.jpg,.gif,.bmp,.jpeg,.pdf,.xls,.xlsx,.doc,.docx,.tif,.tiff',
                    autoProcessQueue: false,
                    method: 'PUT',
                    contentType: false,
                    maxFiles: 1,
                    uploadMultiple: false
                });

                self.dropzone.on("success", function (file) {
                    self.onDropzoneDone(true, file);
                });

                self.dropzone.on("error", function (file) {
                    self.onDropzoneDone(false, file);
                });
            },
            savePhoto: function() {
                var self = this;
                var data = {
                    "ownerId": self.comp.property.id,
                    "category": "Property",
                    "isPrimary":true,
                    "description": null,
                    "tags": [],
                    "mediaItem":{
                        "fileName": self.currentFile.name,
                        "contentType": (self.currentFile.type == 'image/tif') ? 'image/tiff' : self.currentFile.type,
                        "uploadedDate": new Date(Date.now()).toUTCString(),
                        "captureDate": self.currentFile.lastModifiedDate ? self.currentFile.lastModifiedDate : new Date(Date.now()).toUTCString(),
                        "tags": [130],
                        "improvementDateRange": ""
                    }
                };
                $.ajax({
                    type: "POST",
                    cache: false,
                    url:  jsRoutes.controllers.MediaController.generateMediaEntryID().url,
                    processData: false,
                    contentType: 'application/json',
                    data: JSON.stringify(data)
                }).done(function (response) {
                    self.dropzone.options.url = response.mediaItem.imageUrl;
                    self.dropzone.options.headers = {'Content-Type': (self.currentFile.type == 'image/tif') ? 'image/tiff' : self.currentFile.type};
                    self.mediaDropped.push({
                        mediaId: response.mediaItem.id,
                        fileName: response.mediaItem.fileName,
                        mediaEntry: response,
                        uploaded: false
                    });
                    self.currentFile.mediaId = response.mediaItem.id;
                    self.dropzone.processFile(self.currentFile);
                });
            },
            cancelPhoto: function() {
                var self = this;
                self.currentFile = undefined;
                self.showPhotoUpload = true;
                self.showPreview = false;
                self.mediaDropped = [];
                self.mediaProcessed = [];
                $('.imageHolder').remove();
            }
        }
    }

</script>

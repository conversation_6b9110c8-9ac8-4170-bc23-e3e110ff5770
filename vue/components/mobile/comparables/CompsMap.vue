<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div id="map-canvas" @click="stopTimer()"></div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['comps', 'subjectProperty', 'readOnly'],
        mixins: [commonUtils],
        data: function () {
            return {
                directionsDisplay: null,
                directionsService: new google.maps.DirectionsService(),
                map: null,
                markers: [],
                waypts: [],
                routes: [],
                currentLocation: null,
                valuerMarker: null
            }
        },
        mounted: function () {
            var self = this;
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    self.currentLocation = { latitude: position.coords.latitude, longitude: position.coords.longitude};
                    self.defineRoute();
                    self.startTimer();
                }, function(error) {
                    self.handleLocationError(error);
                },
                {
                    enableHighAccuracy: true
                });
            }
        },
        methods: {
            initialize: function(){
                var self = this;
                self.directionsDisplay = new google.maps.DirectionsRenderer({
                    suppressMarkers: true,
                    preserveViewport: true
                });
                self.map = new google.maps.Map(document.getElementById("map-canvas"), {
                    zoom: 15,
                    mapTypeId: google.maps.MapTypeId.ROADMAP,
                    center: self.waypts[0].location
                });
                self.directionsDisplay.setMap(self.map);
                self.calcRoute();
                self.mapValuer();
            },
            createMarker: function (latlng, posRef, label) {
                var self = this;
                var marker = new google.maps.Marker({
                    position: latlng,
                    map: self.map,
                    draggable: false,
                    posRef: posRef,
                    label: label
                });
                self.markers.push(marker);
            },
            calcRoute: function () {
                var self = this;
                var request = {
                    origin: self.waypts[0].location,
                    destination: self.waypts[0].location,
                    waypoints: self.waypts.slice(1),
                    optimizeWaypoints: true,
                    travelMode: google.maps.TravelMode.DRIVING
                };

                self.directionsService.route(request, function(response, status) {
                    if (status == google.maps.DirectionsStatus.OK) {
                        self.directionsDisplay.setDirections(response);
                        var route = response.routes[0];

                        // Add Subject Property Marker
                        self.createMarker(route.legs[0].start_location, 0, self.routes[0].label);
                        // Add Comparable Markers (taking into account waypoint re-ordering by Google Maps)
                        for (var i = 1; i < route.legs.length; i++) {
                            var address = self.routes[route.waypoint_order[i - 1] + 1].label
                            self.createMarker(route.legs[i].start_location, i, address);
                        }
                    }
                });
            },
            handleLocationError: function(error) {
                console.log(error);
            },
            defineRoute: function() {
                var self = this;
                // Add Subject Property
                self.routes = [{
                    coords: {latitude: self.subjectProperty.coords.latitude, longitude: self.subjectProperty.coords.longitude},
                    label: 'SP:' + self.subjectProperty.details.address1.substring(0, self.subjectProperty.details.address1.length - 1)
                }];
                // Add Comparables
                $.each(self.comps, function(i, comp) {
                    var address = comp.property.address1.trim();
                    self.routes.push({
                        coords: {latitude: comp.propertySummary.latitude, longitude: comp.propertySummary.longitude},
                        label: address.substring(0, address.length - 1)
                    });
                });
                // Create Waypoints from Route
                $.each(self.routes, function(i, route) {
                    self.waypts.push({
                        location: new google.maps.LatLng(route.coords.latitude, route.coords.longitude),
                        stopover: true
                    });
                });
                self.initialize();
            },
            mapValuer: function() {
                var self = this;
                self.valuerMarker = new google.maps.Marker({
                    position: new google.maps.LatLng(self.currentLocation.latitude, self.currentLocation.longitude),
                    map: self.map,
                    draggable: false,
                    icon: {
                        path: "M-20,0a20,20 0 1,0 40,0a20,20 0 1,0 -40,0",
                        fillColor: '#ADDFFF',
                        fillOpacity: 1,
                        anchor: new google.maps.Point(0,0),
                        strokeWeight: 0,
                        scale: 1
                    },
                    label: "You"
                })
            },
            startTimer: function () {
                var self = this;
                self.timer = setInterval(function() {
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(function(position) {
                            self.valuerMarker.setPosition(new google.maps.LatLng(position.coords.latitude, position.coords.longitude));
                        }, function(error) {
                            self.handleLocationError(error);
                        },
                        {
                            enableHighAccuracy: true
                        });
                    }
                }, 1000);
            },
            stopTimer: function() {
                var self = this;
                clearInterval(self.timer);
            }
        },
        destroyed: function() {
            var self = this;
            self.stopTimer();
        },
        beforeDestroy: function() {
            var self = this;
            self.stopTimer();
        }
    }

</script>
<style>
    #map-canvas {
    height: 400px;
    }
</style>
<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="tabBlock-wrapper garaging" v-bind:class="{ disabled: readOnly }">
        <ul class="editDetails-item" v-for="garageType,key in garagingDetails" v-if="showGaragingTemplate">
            <li data-cy="garage-type" class="Garage_Type">
                <i class="icons8-garage-filled"></i>
                <div class="select">
                    <label>Garage Type</label>
                    <select v-model="garageType.buildingType">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('GarageType')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garage-description" class="Garage_Description">
                <i class="icons8-todo-list-filled"></i>
                <div class="select select_multiple">
                    <label>Garage Description</label>
                    <select multiple="" v-model="garageType.descriptions">
                        <option v-for="sourceInfo in getClassifications('GarageTypeDescription')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garage-age" class="Garage_Age">
                <i class="icons8-calendar-filled"></i>
                <div class="select">
                    <label>Age</label>
                    <select v-model="garageType.age">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('GarageAge')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garage-modernisation" class="Garage_Modernisation">
                <i class="icons8-maintenance-filled"></i>
                <div class="select">
                    <label>Modernisation</label>
                    <select v-model="garageType.modernisation">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('Modernisation')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garage-floor-area" class="Garage_Floor_Area">
                <i class="icons8-floor-plan-filled"></i>
                <div>
                    <label>Floor Area</label>
                    <input class="advSearch-text" type="number" min="0" pattern="[0-9]*" title="Garage Floor Area" v-model="garageType.floorArea">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garage-exterior-cladding" class="Garage_Exterior_Cladding">
                <i class="icons8-brick-wall-filled"></i>
                <div class="select select_multiple">
                    <label>Exterior Cladding</label>
                    <select multiple="" v-model="garageType.externalWallConstruction">
                        <option v-for="sourceInfo in getClassifications('ExteriorCladding')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garage-roof-construction" class="Garage_Roof_Construction">
                <i class="icons8-structural-filled"></i>
                <div class="select select_multiple">
                    <label>Roof Construction</label>
                    <select multiple="" v-model="garageType.roofConstruction">
                        <option v-for="sourceInfo in getClassifications('RoofConstruction')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garage-foundation" class="Garage_Foundation">
                <i class="icons8-foundation-new"></i>
                <div class="select select_multiple">
                    <label>Foundation</label>
                    <select multiple="" v-model="garageType.foundation">
                        <option v-for="sourceInfo in getClassifications('Foundation')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garage-notes" class="Garage_Notes">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Garage Notes</label>
                    <textarea class="textarea" type="text" title="Notes for Garage" v-model="garageType.notes"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li class="Add_Remove">
                <label>Add or Remove Row</label>
                <i v-if="key>0" data-cy="remove-field" class="material-icons" @click="removeField(key, 1)"></i>
                <i data-cy="add-field" class="material-icons" @click="addField(key, 1)"></i>
            </li>
        </ul>
        <ul class="editDetails-item"  v-for="otherBuilding,key in otherBuildingDetails" v-if="showGaragingTemplate">
            <li data-cy="other-buildings" class="Other_Buildings">
                <i class="icons8-log-cabin-filled"></i>
                <div class="select">
                    <label>Other Buildings</label>
                    <select v-model="otherBuilding.buildingType">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('OtherBuildings')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="other-buildings-description" class="Other_Buildings_Description">
                <i class="icons8-todo-list-filled"></i>
                <div class="select select_multiple">
                    <label>Other Buildings Description</label>
                    <select multiple="" v-model="otherBuilding.descriptions">
                        <option v-for="sourceInfo in getClassifications('OtherBuildingsDescription')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="other-buildings-age" class="Other_Buildings_Age">
                <i class="icons8-calendar-filled"></i>
                <div class="select">
                    <label>Age</label>
                    <select v-model="otherBuilding.age">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('OtherBuildingsAge')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="other-buildings-modernisation" class="Other_Buildings_Modernisation">
                <i class="icons8-maintenance-filled"></i>
                <div class="select">
                    <label>Modernisation</label>
                    <select v-model="otherBuilding.modernisation">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('Modernisation')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="other-buildings-floor-area" class="Other_Buildings_Floor_Area">
                <i class="icons8-floor-plan-filled"></i>
                <div>
                    <label>Floor Area</label>
                    <input class="advSearch-text" type="number" min="0" pattern="[0-9]*" title="Garage Floor Area" v-model="otherBuilding.floorArea">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="other-buildings-exterior-cladding" class="Other_Buildings_Exterior_Cladding">
                <i class="icons8-brick-wall-filled"></i>
                <div class="select select_multiple">
                    <label>Exterior Cladding</label>
                    <select multiple="" v-model="otherBuilding.externalWallConstruction">
                        <option v-for="sourceInfo in getClassifications('ExteriorCladding')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="other-buildings-roof-construction" class="Other_Buildings_Roof_Construction">
                <i class="icons8-structural-filled"></i>
                <div class="select select_multiple">
                    <label>Roof Construction</label>
                    <select multiple="" v-model="otherBuilding.roofConstruction">
                        <option v-for="sourceInfo in getClassifications('RoofConstruction')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="other-buildings-foundation" class="Other_Buildings_Foundation">
                <i class="icons8-foundation-new"></i>
                <div class="select select_multiple">
                    <label>Foundation</label>
                    <select multiple="" v-model="otherBuilding.foundation">
                        <option v-for="sourceInfo in getClassifications('Foundation')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="other-buildings-notes" class="Other_Buildings_Notes">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Other Buildings Notes</label>
                    <textarea class="textarea" type="text" title="Notes for Other Buildings"v-model="otherBuilding.notes"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li class="Add_Remove">
                <label>Add or Remove Row</label>
                <i v-if="key>0" data-cy="remove-other-buildings" class="material-icons" @click="removeField(key, 2)"></i>
                <i class="add-other-buildings" @click="addField(key, 2)"></i>
            </li>
        </ul>
    </div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['job', 'readOnly'],
        mixins: [commonUtils],
        data: function () {
            return {
                garagingDetails: [],
                otherBuildingDetails: [],
                showGaragingTemplate: true,
                refreshGaragingTemplate : false
            }
        },
        mounted: function () {
            var self = this;
            if(!self.job.propertyDetails.garagingDetails) {
                self.job.propertyDetails.garagingDetails = [];
                self.garagingDetails = self.job.propertyDetails.garagingDetails;
            } else {
                self.garagingDetails = self.job.propertyDetails.garagingDetails;
            }
            if(!self.garagingDetails || self.garagingDetails.length == 0) {
                self.addField(0, 1);
            }
            if(!self.job.propertyDetails.otherBuildingDetails) {
                self.job.propertyDetails.otherBuildingDetails = self.otherBuildingDetails;
            } else {
                self.otherBuildingDetails = self.job.propertyDetails.otherBuildingDetails;
            }
            if (!self.otherBuildingDetails || self.otherBuildingDetails.length == 0) {
                self.addField(0, 2);
            }
        },
        updated: function () {
            var self = this;
            if (self.refreshGaragingTemplate) {
                self.showGaragingTemplate = true;
                self.refreshGaragingTemplate = false;
            }
        },
        methods: {
            addField: function (key, index) {
                const self = this;
                var field = (index == 1) ? self.garagingDetails : self.otherBuildingDetails;
                if(index == 1)
                if (!field) {
                    field = [];
                }
                field.splice(key + 1, 0, {});
                self.refreshView();
            },
            removeField: function (key, index) {
                const self = this;
                var field = (index == 1) ? self.garagingDetails : self.otherBuildingDetails;
                field.splice(key, 1);
                self.refreshView();
            },
            refreshView: function() {
                const self = this
                self.showGaragingTemplate = !self.showGaragingTemplate;
                if (!self.showGaragingTemplate) {
                    self.refreshGaragingTemplate = true;
                }
            }
        }
    }
</script>

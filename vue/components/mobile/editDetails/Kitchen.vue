<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="tabBlock-wrapper kitchen" v-bind:class="{ disabled: readOnly }">
        <ul class="editDetails-item">
            <li data-cy="kitchen-layout" class="Kitchen_Layout">
                <i class="icons8-fridge-filled"></i>
                <div class="select select_multiple">
                    <label>Kitchen Layout</label>
                    <select multiple="" v-model="kitchenDetails.layouts">
                        <option v-for="sourceInfo in getClassifications('KitchenLayout')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="age" class="Kitchen_Age">
                <i class="icons8-calendar-filled"></i>
                <div class="select">
                    <label>Age</label>
                    <select v-model="kitchenDetails.age">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('KitchenAge_HV')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="quality" class="Kitchen_Quality">
                <i class="icons8-rating-filled"></i>
                <div class="select">
                    <label>Quality</label>
                    <select v-model="kitchenDetails.quality">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('KitchenQuality')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="appliances" class="Appliances">
                <i class="icons8-dishwasher-filled"></i>
                <div class="select select_multiple">
                    <label>Appliances</label>
                    <select multiple="" v-model="kitchenDetails.appliances">
                        <option v-for="sourceInfo in getClassifications('Appliances')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="bench-and-sink" class="Bench_and_Sink">
                <i class="icons8-bench-sink-filled"></i>
                <div class="select select_multiple">
                    <label>Bench and Sink</label>
                    <select multiple="" v-model="kitchenDetails.benchAndSink">
                        <option v-for="sourceInfo in getClassifications('BenchandSink')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="floor" class="Kitchen_Floor">
                <i class="icons8-kitchen-floor-new"></i>
                <div class="select">
                    <label>Floor</label>
                    <select v-model="kitchenDetails.floor">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('KitchenFloor')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="kitchen-notes" class="Kitchen_Notes">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Kitchen Notes</label>
                    <textarea class="textarea" type="text" title="Notes for Kitchen" v-model="kitchenDetails.notes"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['job', 'readOnly'],
        mixins: [commonUtils],
        data: function () {
            return {
                kitchenDetails: {}
            }
        },
        mounted: function () {
            var self = this;
            self.kitchenDetails = self.job.propertyDetails.kitchenDetails;
        }
    }
</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="tabBlock-wrapper overview default-section" v-bind:class="{ disabled: readOnly }">
        <ul class="editDetails-item">
            <li data-cy="bedrooms" class="Bedrooms">
                <i class="icons8-bed-filled"></i>
                <div>
                    <label>Bedrooms</label>
                    <input type="number" pattern="[0-9]*" min="0"
                           title="Number of Bedrooms"
                           v-model="houseDetails.numberOfBedrooms">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="bathrooms" class="Bathrooms">
                <i class="icons8-total-bathrooms-filled"></i>
                <div>
                    <label>Bathrooms</label>
                    <input type="number" pattern="[0-9]*" min="0"
                           title="Number of Bathrooms"
                           v-model="houseDetails.numberOfBathrooms">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="toilets" class="Toilets">
                <i class="icons8-toilet-bowl-filled"></i>
                <div>
                    <label>Toilets</label>
                    <input type="number" pattern="[0-9]*" min="0"
                           title="Number of Toilets"
                           v-model="houseDetails.numberOfToilets">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="living-areas" class="Living_Areas_Overview">
                <i class="icons8-living-areas-new"></i>
                <div>
                    <label>Living Areas</label>
                    <input type="number" pattern="[0-9]*" min="0"
                           title="Number of Living Areas"
                           v-model="houseDetails.numberOfLivingAreas">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="garaging" class="Garaging">
                <i class="icons8-garage-filled"></i>
                <div>
                    <label>Garaging</label>
                    <input type="number" pattern="[0-9]*" min="0"
                           title="Number of Garage Spaces"
                           v-model="houseDetails.garaging">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="offstreet-parking" class="Offstreet_Parking">
                <i class="icons8-traffic-jam-filled"></i>
                <div>
                    <label>Offstreet Parking</label>
                    <input type="number" pattern="[0-9]*" min="0"
                           title="Number of Offstreet Car Parks"
                           v-model="houseDetails.offstreetParking">
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="house-type" class="House_Type">
                <i class="icons8-house-filled"></i>
                <div class="select">
                    <label>House Type</label>
                    <select v-model="houseDetails.houseType">
                        <option :value="null" label="Select..." title="">Select..</option>
                        <option v-for="sourceInfo in getClassifications('HouseType_HV')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="year-built" class="Year_Built">
                <i class="icons8-calendar-filled"></i>
                <div>
                    <label>Year Built</label>
                    <input type="number" min="1900" max="2099"
                           step="1" value="2016" title="Year Built"
                           v-model="houseDetails.estimatedYearOfConstruction">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="total-floor-area" class="Total_Floor_Area">
                <i class="icons8-floor-plan-filled"></i>
                <div>
                    <label>Total Floor Area</label>
                    <input type="number" pattern="[0-9]*" title="Total Floor Area" min="0"
                           v-model="houseDetails.totalFloorArea" >
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="total-floor-area-description" class="Total_Floor_Area_Description">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Total Floor Area Description</label>
                    <textarea class="textarea" type="text" title="Total Floor Area Description"
                    v-model="houseDetails.totalFloorDescription"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="exterior-cladding" class="Exterior_Cladding">
                <i class="icons8-rating-filled"></i>
                <div class="select select_multiple">
                    <label>Exterior Cladding</label>
                    <select multiple="" v-model="houseDetails.exteriorCladding">
                        <option v-for="sourceInfo in getClassifications('ExteriorCladding')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="foundation" class="Foundation">
                <i class="icons8-foundation-new"></i>
                <div class="select select_multiple">
                    <label>Foundation</label>
                    <select multiple="" v-model="houseDetails.foundation">
                        <option v-for="sourceInfo in getClassifications('Foundation')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="joinery" class="Joinery">
                <i class="icons8-structural-filled"></i>
                <div class="select select_multiple">
                    <label>Joinery</label>
                    <select multiple="" v-model="houseDetails.joinery">
                        <option v-for="sourceInfo in getClassifications('Joinery')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="roof-style" class="Roof_Style">
                <i class="icons8-structural-filled"></i>
                <div class="select select_multiple">
                    <label>Roof Style</label>
                    <select multiple="" v-model="houseDetails.roofStyle">
                        <option v-for="sourceInfo in getClassifications('RoofStyle')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="roof-construction" class="Roof_Construction">
                <i class="icons8-structural-filled"></i>
                <div class="select select_multiple">
                    <label>Roof Construction</label>
                    <select multiple="" v-model="houseDetails.roofConstruction">
                        <option v-for="sourceInfo in getClassifications('RoofConstruction')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="spouting" class="Spouting">
                <i class="icons8-structural-filled"></i>
                <div class="select select_multiple">
                    <label>Spouting</label>
                    <select multiple="" v-model="houseDetails.spouting">
                        <option v-for="sourceInfo in getClassifications('Spouting')" :value="sourceInfo">{{ sourceInfo.description }}</option></select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="external-condition" class="External_Condition">
                <i class="icons8-rating-filled"></i>
                <div class="select select_multiple">
                    <label>External Condition</label>
                    <select multiple="" v-model="houseDetails.externalCondition">
                        <option v-for="sourceInfo in getClassifications('ExternalCondition')" :value="sourceInfo">{{ sourceInfo.description }}</option></select>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="quality-of-external-presentation" class="Quality_of_External_Presentation">
                <i class="icons8-rating-filled"></i>
                <div class="select">
                    <label>Quality of External Presentation</label>
                    <select v-model="houseDetails.qualityOfExternalPresentation">
                        <option :value="null" label="Select..." title=""></option>
                        <option v-for="sourceInfo in getClassifications('QualityofExternalPresentation')" :value="sourceInfo">{{ sourceInfo.description }}</option></select>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="internal-condition" class="Internal_Condition">
                <i class="icons8-rating-filled"></i>
                <div class="select select_multiple">
                    <label>Internal Condition</label>
                    <select multiple="" v-model="houseDetails.internalCondition">
                        <option v-for="sourceInfo in getClassifications('InternalCondition_HV')" :value="sourceInfo">{{ sourceInfo.description }}</option></select>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="quality-of-internal-presentation" class="Quality_of_Internal_Presentation">
                <i class="icons8-rating-filled"></i>
                <div class="select">
                    <label>Quality of Internal Presentation</label>
                    <select v-model="houseDetails.qualityOfInternalPresentation">
                        <option :value="null" label="Select..." title=""></option>
                        <option v-for="sourceInfo in getClassifications('QualityofInternalPresentation')" :value="sourceInfo">{{ sourceInfo.description }}</option></select>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="standard-of-accomodation" class="Standard_of_Accomodation">
                <i class="icons8-rating-filled"></i>
                <div class="select">
                    <label>Standard of Accomodation</label>
                    <select v-model="houseDetails.standardOfAccommodation">
                        <option :value="null" label="Select..." title=""></option>
                        <option v-for="sourceInfo in getClassifications('StandardOfAccommodation')" :value="sourceInfo">{{ sourceInfo.description }}</option></select>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="layout-descriptions" class="Legal_Descriptions">
                <i class="icons8-rating-filled"></i>
                <div class="select select_multiple">
                    <label>Layout Description</label>
                    <select multiple="" v-model="houseDetails.layoutDescriptions">
                        <option v-for="sourceInfo in getClassifications('LayoutDescription')" :value="sourceInfo">{{ sourceInfo.description }}</option></select>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="maintenance-required" class="Maintenance_Required">
                <i class="icons8-maintenance-filled"></i>
                <div>
                    <label>Maintenance Required</label>
                    <input type="text" title="Maintenance Required" v-model="houseDetails.maintenanceRequired">
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="immediate-maintenance-required" class="Immediate_Maintenance_Required">
                <i class="icons8-brake-warning-filled"></i>
                <div>
                    <label>Immediate Maintenance Required</label>
                    <input type="text" title="Immediate Maintenance Required" v-model="houseDetails.immediateMaintenanceRequired">
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="recent-alterations" class="Recent_Alterations">
                <i class="icons8-maintenance-filled"></i>
                <div>
                    <label>Recent Alterations</label>
                    <input type="text" title="Recent Alterations" v-model="houseDetails.recentAlterations">
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="code-compliance" class="Code_Compliance">
                <i class="icons8-paste-special-filled"></i>
                <div>
                    <label>Code Compliance</label>
                    <input type="text" title="Code Compliance" v-model="houseDetails.codeCompliance">
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="overview-notes" class="Overview_Notes">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Overview Notes</label>
                    <textarea class="textarea" type="text" title="Overview Notes" v-model="houseDetails.notes"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['job', 'readOnly'],
        mixins: [commonUtils],
        data: function () {
            return {
                houseDetails: {}
            }
        },
        mounted: function () {
            var self = this;
            self.houseDetails = self.job.propertyDetails.houseDetails;
        }
    }

</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="tabBlock-wrapper bathrooms" v-bind:class="{ disabled: readOnly }">
        <ul class="editDetails-item">
            <li data-cy="main-bathroom" class="Main_Bathroom">
                <i class="icons8-shower-and-tub-filled"></i>
                <div class="select">
                    <label>Main Bathroom</label>
                    <select v-model="bathroomDetails.mainBathroom.key">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('MainBathroom')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="main-bathroom-description" class="Main_Bathroom_Description">
                <i class="icons8-todo-list-filled"></i>
                <div class="select select_multiple">
                    <label>Main Bathroom Description</label>
                    <select multiple="" v-model="bathroomDetails.mainBathroom.values">
                        <option v-for="sourceInfo in getClassifications('MainBathroomDescription')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="main-bathroom-age" class="Main_Bathroom_Age">
                <i class="icons8-calendar-filled"></i>
                <div class="select">
                    <label>Age</label>
                    <select v-model="bathroomDetails.mainBathroomAge">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('MainBathroomAge')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="main-bathroom-quality" class="Main_Bathroom_Quality">
                <i class="icons8-rating-filled"></i>
                <div class="select">
                    <label>Quality</label>
                    <select v-model="bathroomDetails.mainBathroomQuality">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('MainBathroomQuality')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="ensuit" class="Ensuite">
                <i class="icons8-shower-and-tub-filled"></i>
                <div class="select">
                    <label>Ensuite</label>
                    <select v-model="bathroomDetails.ensuiteBathroom.key">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('Ensuite')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="ensuit-description" class="Ensuite_Description">
                <i class="icons8-todo-list-filled"></i>
                <div class="select select_multiple">
                    <label>Ensuite Description</label>
                    <select multiple="" v-model="bathroomDetails.ensuiteBathroom.values">
                        <option v-for="sourceInfo in getClassifications('EnsuiteDescription')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="ensuit-age" class="Ensuite_Age">
                <i class="icons8-calendar-filled"></i>
                <div class="select">
                    <label>Age</label>
                    <select v-model="bathroomDetails.ensuiteAge">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('EnsuiteAge')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="ensuit-quality" class="Ensuite_Quality">
                <i class="icons8-rating-filled"></i>
                <div class="select">
                    <label>Quality</label>
                    <select v-model="bathroomDetails.ensuiteQuality">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('EnsuiteQuality')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item" v-for="otherBathroom,key in bathroomDetails.otherBathrooms" v-if="showBathroomsTemplate">
            <li data-cy="bathroom-or-toilet"  class="Bathroom_or_Toilet">
                <i class="icons8-toilet-bowl-filled"></i>
                <div class="select">
                    <label>Bathroom or Toilet</label>
                    <select v-model="otherBathroom.key">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('BathroomorToilet')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="bathroom-or-toilet-description" class="Bathroom_or_Toilet_Description">
                <i class="icons8-todo-list-filled"></i>
                <div class="select select_multiple">
                    <label>Bathroom or Toile Description</label>
                    <select multiple="" v-model="otherBathroom.values">
                        <option v-for="sourceInfo in getClassifications('MainBathroomDescription')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li class="Add_Remove">
                <label>Add or Remove Row</label>
                <i v-if="key>0" data-cy="remove-bathroom" class="material-icons" @click="removeField(key, 'otherBathrooms')"></i>
                <i data-cy="add-bathroom" class="material-icons" @click="addField(key, 'otherBathrooms')"></i>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="bathroom-notes" class="Bathroom_Notes">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Bathroom Notes</label>
                    <textarea class="textarea" type="text" title="Notes for Bedroom" v-model="bathroomDetails.notes"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['job', 'readOnly'],
        mixins: [commonUtils],
        data: function () {
            return {
                bathroomDetails: {
                    mainBathroom:{},
                    ensuiteBathroom: {}
                },
                showBathroomsTemplate: true,
                refreshBathroomsTemplate : false,
            }
        },
        mounted: function () {
            var self = this;
            self.bathroomDetails = self.job.propertyDetails.bathroomDetails;
            if (!self.bathroomDetails.mainBathroom) {
                self.bathrooms.mainBathroom = {};
            }
            if (!self.bathroomDetails.ensuiteBathroom) {
                self.bathrooms.ensuiteBathroom = {};
            }
            if (!self.bathroomDetails.otherBathrooms || self.bathroomDetails.otherBathrooms.length == 0) {
                self.addField(0, 'otherBathrooms');
            }
        },
        updated: function () {
            var self = this;
            if (self.refreshBathroomsTemplate) {
                self.showBathroomsTemplate = true;
                self.refreshBathroomsTemplate = false;
            }
        },
        methods: {
            addField: function(key, field) {
                const self = this
                if (!self.bathroomDetails[field]) {
                    self.bathroomDetails[field] = [];
                }
                self.bathroomDetails[field].splice(key+1, 0, {});
                self.refreshView();
            },
            removeField: function(key, field){
                const self = this;
                self.bathroomDetails[field].splice(key, 1);
                self.refreshView();
            },
            refreshView: function() {
                const self = this
                self.showBathroomsTemplate = !self.showBathroomsTemplate;
                if (!self.showBathroomsTemplate) {
                    self.refreshBathroomsTemplate = true;
                }
            }
        }
    }

</script>

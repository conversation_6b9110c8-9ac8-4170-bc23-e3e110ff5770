<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="tabBlock-wrapper interior" v-bind:class="{ disabled: readOnly }">
        <ul class="editDetails-item" v-for="livingArea,key in interiorDetails.livingAreas" v-if="showInteriorTemplate">
            <li data-cy="living-area" class="Living_Areas_Interior">
                <i class="icons8-living-areas-new"></i>
                <div class="select">
                    <label>Living Area {{ key+1 }}</label>
                    <select v-model="livingArea.key">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('LivingArea')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="living-area-description" class="Living_Areas_Description">
                <i class="icons8-todo-list-filled"></i>
                <div class="select select_multiple">
                    <label>Living Area Description</label>
                    <select multiple="" v-model="livingArea.values">
                        <option v-for="sourceInfo in getClassifications('LivingAreaDescription')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li class="Add_Remove">
                <label>Add or Remove Row</label>
                <i v-if="key>0" class="material-icons" @click="removeLivingArea(key)"></i>
                <i class="material-icons" @click="addLivingArea(key)"></i>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="laundry" class="Laundry">
                <i class="icons8-washing-machine-filled"></i>
                <div class="select select_multiple">
                    <label>Laundry</label>
                    <select multiple="" v-model="interiorDetails.laundry">
                        <option v-for="sourceInfo in getClassifications('Laundry')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="internal-linings" class="Internal_Linings">
                <i class="icons8-brick-wall-filled"></i>
                <div class="select select_multiple">
                    <label>Internal Linings</label>
                    <select multiple="" v-model="interiorDetails.internalWallLinings">
                        <option v-for="sourceInfo in getClassifications('InternalLinings')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="floors" class="Floors">
                <i class="icons8-carpet-filled"></i>
                <div class="select select_multiple">
                    <label>Floors</label>
                    <select multiple="" v-model="interiorDetails.floors">
                        <option v-for="sourceInfo in getClassifications('Floors')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="chattels" class="Chattels">
                <i class="icons8-curtains-filled"></i>
                <div class="select select_multiple">
                    <label>Chattels</label>
                    <select multiple="" v-model="interiorDetails.chattels">
                        <option v-for="sourceInfo in getClassifications('Chattels')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="heating-type" class="Heating_Type">
                <i class="icons8-fire-station-filled"></i>
                <div class="select select_multiple">
                    <label>Heating Type</label>
                    <select multiple="" v-model="interiorDetails.heatingType">
                        <option v-for="sourceInfo in getClassifications('HeatingType')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="insulation" class="Insulation">
                <i class="icons8-heating-room-filled"></i>
                <div class="select select_multiple">
                    <label>Insulation</label>
                    <select multiple="" v-model="interiorDetails.insulation">
                        <option v-for="sourceInfo in getClassifications('Insulation')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="plumbing-age" class="Plumbing_Age">
                <i class="icons8-piping-filled"></i>
                <div class="select">
                    <label>Plumbing Age</label>
                    <select v-model="interiorDetails.plumbingAge">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('PlumbingAge')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="wiring-age" class="Wiring_Age">
                <i class="icons8-plug-4-filled"></i>
                <div class="select">
                    <label>Wiring Age</label>
                    <select v-model="interiorDetails.wiringAge">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('WiringAge')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="double-glazing" class="Double_Glazing">
                <i class="icons8-closed-window-filled"></i>
                <div class="select">
                    <label>Double Glazing</label>
                    <select v-model="interiorDetails.doubleGlazing">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('DoubleGlazing')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="alternative-energy" class="Alternative_Energy">
                <i class="icons8-solar-panel-filled"></i>
                <div class="select select_multiple">
                    <label>Alternative Energy</label>
                    <select multiple="" v-model="interiorDetails.alternativeEnergy">
                        <option v-for="sourceInfo in getClassifications('AlternativeEnergy')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="ventilation" class="Ventilation">
                <i class="icons8-air-conditioner-filled"></i>
                <div class="select">
                    <label>Ventilation</label>
                    <select v-model="interiorDetails.ventilation">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('Ventilation')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="redecoration-age" class="Redecoration_Age">
                <i class="icons8-roller-brush-filled"></i>
                <div class="select">
                    <label>Redecoration Age</label>
                    <select v-model="interiorDetails.redecorationAge">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('RedecorationAge')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="other-features" class="Other_Features">
                <i class="icons8-temperature-inside-filled"></i>
                <div class="select select_multiple">
                    <label>Other Features</label>
                    <select multiple="" v-model="interiorDetails.otherFeatures">
                        <option v-for="sourceInfo in getClassifications('OtherFeatures')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="interior-notes" class="Interior_Notes">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Interior Notes</label>
                    <textarea class="textarea" type="text" title="Notes for Interior"  v-model="interiorDetails.notes"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['job', 'readOnly'],
        mixins: [commonUtils],
        data: function () {
            return {
                interiorDetails: {},
                showInteriorTemplate: true,
                refreshInteriorTemplate : false,
            }
        },
        mounted: function () {
            var self = this;
            self.interiorDetails = self.job.propertyDetails.interiorDetails;
            if (!self.interiorDetails.livingAreas || self.interiorDetails.livingAreas.length == 0) {
                self.addLivingArea(0);
                self.refreshView();
            }
        },
        updated: function () {
            var self = this;
            if (self.refreshInteriorTemplate) {
                self.showInteriorTemplate = true;
                self.refreshInteriorTemplate = false;
            }
        },
        methods: {
            addLivingArea: function(key) {
                const self = this
                if (!self.interiorDetails.livingAreas) {
                    self.interiorDetails.livingAreas = [];
                }
                self.interiorDetails.livingAreas.splice(key+1, 0, {});
                self.refreshView();
            },
            removeLivingArea: function(key){
                const self = this;
                self.interiorDetails.livingAreas.splice(key, 1);
                self.refreshView();
            },
            refreshView: function() {
                const self = this
                self.showInteriorTemplate = !self.showInteriorTemplate
                if (!self.showInteriorTemplate) {
                    self.refreshInteriorTemplate = true;
                }
            }
        }
    }

</script>

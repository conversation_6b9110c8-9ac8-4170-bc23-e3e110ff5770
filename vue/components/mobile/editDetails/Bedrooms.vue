<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="tabBlock-wrapper bedrooms" v-bind:class="{ disabled: readOnly }">
        <ul class="editDetails-item" v-for="bedroom,key in bedroomDetails.bedrooms" v-if="showBedroomsTemplate">
            <li :data-cy="`bedroom-${key+1}`" class="Bedroom_Type">
                <i class="icons8-bed-filled"></i>
                <div class="select">
                    <label>Bedroom {{ key+1}}</label>
                    <select v-model="bedroom.key">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('Bedroom')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="bedroom-description" class="Bedroom_Description">
                <i class="icons8-todo-list-filled"></i>
                <div class="select select_multiple">
                    <label>Bedroom Description</label>
                    <select multiple="" v-model="bedroom.values">
                        <option v-for="sourceInfo in getClassifications('BedroomDescriptions')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li class="Add_Remove">
                <label>Add or Remove Row</label>
                <i v-if="key>0" data-cy="remove-bedrooms" class="material-icons" @click="removeField(key, 'bedrooms')"></i>
                <i data-cy="add-bedrooms" class="material-icons" @click="addField(key, 'bedrooms')"></i>
            </li>
        </ul>
        <ul class="editDetails-item" v-for="study, key in bedroomDetails.studies" v-if="showBedroomsTemplate">
            <li data-cy="home-office-or-study" class="Bedroom_Type">
                <i class="icons8-pc-on-desk-filled"></i>
                <div class="select">
                    <label>Home Office or Study</label>
                    <select v-model="study.key">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('HomeofficeorStudy')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="home-office-or-study-description" class="Bedroom_Description">
                <i class="icons8-todo-list-filled"></i>
                <div class="select select_multiple">
                    <label>Home Office or Study Description</label>
                    <select multiple="" v-model="study.values">
                        <option v-for="sourceInfo in getClassifications('HomeOfficeDescriptions')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li class="Add_Remove">
                <label>Add or Remove Row</label>
                <i v-if="key>0" data-cy="remove-home-office" class="material-icons" @click="removeField(key, 'studies')"></i>
                <i data-cy="add-home-office" class="material-icons" @click="addField(key, 'studies')"></i>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="bedroom_notes" class="Bedroom_Notes">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Bedroom Notes</label>
                    <textarea class="textarea" type="text" title="Notes for Bedroom" v-model="bedroomDetails.notes"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['job', 'readOnly'],
        mixins: [commonUtils],
        data: function () {
            return {
                bedroomDetails: {},
                showBedroomsTemplate: true,
                refresBedroomsTemplate : false
            }
        },
        mounted: function () {
            var self = this;
            self.bedroomDetails = self.job.propertyDetails.bedroomDetails;
            if (!self.bedroomDetails.bedrooms || self.bedroomDetails.bedrooms.length == 0) {
                self.addField(0, 'bedrooms');
                self.addField(1, 'bedrooms');
            }
            if (!self.bedroomDetails.studies || self.bedroomDetails.studies.length == 0) {
                self.addField(0, 'studies');
            }
        },
        updated: function () {
            var self = this;
            if (self.refresBedroomsTemplate) {
                self.showBedroomsTemplate = true;
                self.refresBedroomsTemplate = false;
            }
        },
        methods: {
            addField: function(key, field) {
                const self = this
                if (!self.bedroomDetails[field]) {
                    self.bedroomDetails[field] = [];
                }
                self.bedroomDetails[field].splice(key+1, 0, {});
                self.refreshView();
            },
            removeField: function(key, field){
                const self = this;
                self.bedroomDetails[field].splice(key, 1);
                self.refreshView();
            },
            refreshView: function() {
                const self = this
                self.showBedroomsTemplate = !self.showBedroomsTemplate;
                if (!self.showBedroomsTemplate) {
                    self.refresBedroomsTemplate = true;
                }
            }
        }
    }
</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <div class="tabBlock-wrapper improvements" v-bind:class="{ disabled: readOnly }">
        <ul class="editDetails-item" v-for="majorSiteImprovement,key in improvementDetails.majorSiteImprovements" v-if="showImproventTemplate">
            <li data-cy="major-site-improvements" class="Major_Site_Improvements">
                <i class="icons8-swimming-pool"></i>
                <div class="select">
                    <label>Major Site Improvements</label>
                    <select v-model="majorSiteImprovement.improvement">
                        <option :value="null" label="Select... " title=""></option>
                        <option v-for="sourceInfo in getClassifications('MajorSiteImprovements')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="major-site-improvements-description" class="Major_Site_Improvements_Description">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Major Site Improvements Description</label>
                    <textarea class="textarea" textarea="" type="text" title="Major Site Improvements Description" v-model="majorSiteImprovement.description"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li class="Add_Remove">
                <label>Add or Remove Row</label>
                <i v-if="key>0" data-cy="remove-major-site-improvement" class="material-icons" @click="removeField(key, 'majorSiteImprovements')"></i>
                <i data-cy="add-major-site-improvement" class="material-icons" @click="addField(key, 'majorSiteImprovements')"></i>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="minor-site-improvements" class="Minor_Site_Improvements">
                <i class="icons8-front-gate-closed-filled"></i>
                <div class="select select_multiple">
                    <label>Minor Site Improvements</label>
                    <select multiple="" v-model="improvementDetails.minorSiteImprovements">
                        <option v-for="sourceInfo in getClassifications('MinorSiteImprovements')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="driveway" class="Driveway">
                <i class="icons8-driveway-new"></i>
                <div class="select select_multiple">
                    <label>Driveway</label>
                    <select multiple="" v-model="improvementDetails.driveway">
                        <option v-for="sourceInfo in getClassifications('Driveway')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="landscaping" class="Landscaping">
                <i class="icons8-forest-filled"></i>
                <div class="select select_multiple">
                    <label>Landscaping</label>
                    <select multiple="" v-model="improvementDetails.landscaping">
                        <option v-for="sourceInfo in getClassifications('Landscaping')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
            <li data-cy="fencing" class="Fencing">
                <i class="icons8-defensive-wood-wall-filled"></i>
                <div class="select select_multiple">
                    <label>Fencing</label>
                    <select multiple="" v-model="improvementDetails.fencing">
                        <option v-for="sourceInfo in getClassifications('Fencing')" :value="sourceInfo">{{ sourceInfo.description }}</option>
                    </select>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
        <ul class="editDetails-item">
            <li data-cy="improvements-notes" class="Improvements_Notes">
                <i class="icons8-pencil"></i>
                <div>
                    <label>Improvements Notes</label>
                    <textarea class="textarea" textarea="" type="text" title="Notes for Improvements" v-model="improvementDetails.notes"></textarea>
                    <span class="valMessage"></span>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
    import commonUtils from '../../../utils/CommonUtils';

    export default {
        props: ['job', 'readOnly'],
        mixins: [commonUtils],
        data: function () {
            return {
                improvementDetails: {},
                showImproventTemplate: true,
                refresImprovementTemplate : false
            }
        },
        mounted: function () {
            var self = this;
            if(!self.job.propertyDetails.improvementDetails) {
                self.job.propertyDetails.improvementDetails = {};
                self.improvementDetails = self.job.propertyDetails.improvementDetails;
            } else {
                self.improvementDetails = self.job.propertyDetails.improvementDetails;
            }
            if(!self.improvementDetails.majorSiteImprovements || self.improvementDetails.majorSiteImprovements.length == 0) {
                self.addField(0, 'majorSiteImprovements');
            }
        },
        updated: function () {
            var self = this;
            if (self.refresImprovementTemplate) {
                self.showImproventTemplate = true;
                self.refresImprovementTemplate = false;
            }
        },
        methods: {
            addField: function(key, field) {
                const self = this
                if (!self.improvementDetails[field]) {
                    self.improvementDetails[field] = [];
                }
                self.improvementDetails[field].splice(key+1, 0, {});
                self.refreshView();
            },
            removeField: function(key, field){
                const self = this;
                self.improvementDetails[field].splice(key, 1);
                self.refreshView();
            },
            refreshView: function() {
                const self = this
                self.showImproventTemplate = !self.showImproventTemplate;
                if (!self.showImproventTemplate) {
                    self.refresImprovementTemplate = true;
                }
            }
        }
    }
</script>

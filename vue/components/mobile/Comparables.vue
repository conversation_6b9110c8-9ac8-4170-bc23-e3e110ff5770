<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <main class="comparablesList-view">

        <div class="comparablesList-wrapper">
            <nav>
                <ul class="tabs">
                    <li class="comparablesList-tab--1" data-tab="comparablesList-tab--1" data-container="compsList"><span class="is-active"><i class="material-icons"></i>Comparables</span></li>
                    <li class="comparablesList-tab--2" data-tab="comparablesList-tab--2" data-container="compsMap"><span><i class="material-icons"></i>Map</span></li>
                </ul>
            </nav>

            <!-- COMPARABLES LIST VIEW/TAB STARTS HERE -->
            <div class="tabBlock-wrapper comparablesList default-section compsList" v-for="comp,key in comparableSales" v-if="showList" @click="showCompDetails(key)">
                <ul class="comparablesList propertylist-item hrLabel rate">
                    <li><img :src="comp.property.primaryPhoto"></li>
                    <li><h3 data-cy="property-address" class="h3">{{ comp.property.address1 }}<span>{{ comp.property.address2 }}</span></h3></li>
                    <li class="inlineLabel"><label>Val Ref:</label>{{ comp.property.valuationReference }}</li>
                    <li><label>Gross Sale</label>{{ formatPrice(comp.grossSalePrice, '$0,0') }}</li>
                    <li><label>Sale Date</label>{{ formatDate(comp.saleDate, 'DD/MM/YYYY') }}</li>
                    <li><label>Category</label>{{ comp.property.category.code }}</li>
                    <li><label>Total Floor Area</label>{{ comp.property.TFA }}<span>m<sup>2</sup></span></li>
                    <li><label>Total Living Area</label>{{ comp.property.TLA }}<span>m<sup>2</sup></span></li>
                    <li><label>Land Area</label>{{ formatDecimal(comp.property.landArea, 4) }}<span>Ha</span></li>
                    <li class="icons8-crosshair-filled"><label>Distance</label>{{ formatDecimal(comp.distance, 1) }}<span>Km</span></li>
                </ul>
            </div>

            <div class="tabBlock-wrapper comparablesList compsMap">
                <comps-map :comps="comparableSales" :subjectProperty="subjectProperty" :readOnly="readOnly" v-if="showMap"></comps-map>
            </div>

        </div>

        <comp-details v-if="showComps" :comp="selectedComp" :readOnly="readOnly"></comp-details>
    </main>
</template>
<script>
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import propertyUtils from '../../utils/PropertyUtils';
    import CompDetails from './comparables/CompDetails.vue';
    import CompsMap from './comparables/CompsMap.vue';

    export default {
        props: ['job', 'readOnly', 'property'],
        mixins: [formatUtils, commonUtils, propertyUtils],
        components: {
            CompDetails,
            CompsMap
        },
        data: function () {
            return {
                comparableSales: [],
                showList: false,
                selectedComp: null,
                showComps: false,
                showMap: false,
                subjectProperty: {
                    details: null,
                    coords: null
                }
            }
        },
        mounted: function () {
            var self = this;
            self.initTabs();
            self.comparableSales = JSON.parse(JSON.stringify(self.job.comparableSales));
            self.populateStatsSummary(self.loadCompSales);

            $('.comparablesList-back').off("click").click(function () {
                self.selectedComp = null;
                self.showComps = false;
                $('.comparablesList-back').hide();
                self.showList = true;
            });
        },
        methods: {
            loadCompSales: function() {
                var self = this;
                var saleIds = [];
                if(self.comparableSales) {
                    for (var i = 0; i < self.comparableSales.length; i++) {
                        saleIds.push(self.comparableSales[i].qivsSaleId);
                    }
                }
                if (saleIds && saleIds.length > 0) {
                    var searchCriteria = {};
                    searchCriteria.qivsSaleIds = saleIds;
                    searchCriteria.max = saleIds.length;
                    var compSaleCriteria = {};
                    compSaleCriteria.saleSearchCriteria = searchCriteria;
                    compSaleCriteria.homeValuationId = self.job.id;
                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ComparableSales.getComparableSaleBySaleId().url,
                        data: JSON.stringify(compSaleCriteria),
                        contentType: "application/json; charset=utf-8",
                        cache: false,
                        dataType: "json",
                        success: function (response) {
                            if (response.length > 0) {
                                for (var i = 0; i < response.length; i++) {
                                    var property = response[i].sale.primaryProperty;
                                    property.id = response[i].propertyID;
                                    property.currentValuation = { landValue: response[i].sale.landValue, capitalValue: response[i].sale.capitalValue };
                                    self.comparableSales.filter(function(comp) {return comp.qivsSaleId === response[i].sale.qivsSaleId}).forEach(function(comparableSale) {
                                        comparableSale.property =  self.generateMasterDetailsData({property: property, photos: [response[i].photoURLs]});
                                        comparableSale.sale = response[i].sale;
                                        comparableSale.saleAnalysis = response[i].saleAnalysis;
                                        comparableSale.propertySummary = response[i].propertySummary;
                                    })
                                }
                                self.showMap = true;
                            }
                            self.showList = true;
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            console.log('Error while refreshing comparable sales => ' + response);
                        }
                    });
                }
            },
            showCompDetails: function(key) {
                var self = this;
                self.selectedComp = self.comparableSales[key];
                self.showList = false;
                self.showMap = false;
                self.showComps = true;
            },
            populateStatsSummary: function (callback) {
                var self = this;
                var m = jsRoutes.controllers.PropertyMasterData.getStatsSummary(self.property.qupid);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        var data = response;
                        self.subjectProperty.details = self.property;
                        self.subjectProperty.coords = {latitude: data.latitude, longitude: data.longitude};
                        if(callback) callback();
                    },
                    error: function (response) {
                        console.log('error fetch property search results: ' + response);
                        self.errorHandler(response);
                    }
                });
            }

        }
    }

</script>

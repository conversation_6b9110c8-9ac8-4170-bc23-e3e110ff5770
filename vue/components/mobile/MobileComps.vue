<template>
    <div class="compProperty-mobile" v-if="showComps">
        <ul class="cpm-headerWrapper">
            <li><h1>QV Monarch Comparaple Properties</h1></li>
            <li class="cpm-homeLink" @click="loadDashboard"><a><i class="material-icons">home</i></a></li>
            <li><h2>{{propertyAddress}}</h2></li>
        </ul>

        <div>

            <h3 class="compsCount">Comparable Properties<span>Showing {{comparableSales.length}} Selected Properties</span></h3>

            <ul class="cpm-list" v-show="showListView">
                <li v-for="sale,key in comparableSales" @click="showDetailedSale(sale)" class="cpm-card">
                    <h3 class="cpm-address">{{sale.address1}}<span>{{sale.address2}}</span></h3>
                    <img :src="sale.primaryPhoto">
                    <ul class="cpm-values">
                        <li><label>Net Sale Price</label>{{sale.netPrice}}</li>
                        <li><label>Chattels</label>{{sale.chattels}}</li>
                        <li><label>Analysed Land</label>{{sale.analysedLandValue}}</li>
                    </ul>
                </li>
            </ul>

        </div>

        <div action="no/presigned/url/assigned" v-show="false" v-if="showDropzone" id="photo-dropzone-HomeValuationReport"></div>

        <div class="cpm-expanded" v-if="showDetailedView">
            <ul class="cpm-toolbar">
                <li title="Add Details" @click="showListOfSales()"><a href="#"><i class="material-icons cpm-blue">arrow_back</i></a></li>
                <li title="Take Photo" id="takePhoto"><a href="#"><i class="material-icons cpm-blue">photo_camera</i></a></li>
                <li title="Add Details" @click="editComments()"><a href="#"><i class="material-icons cpm-blue">mode_edit</i></a></li>
            </ul>
            <h3 class="cpm-address">{{selectedSale.address1}}<span>{{selectedSale.address2}}</span></h3>
            <ul class="cpm-totals">
                <li><label>TFA:</label>{{selectedSale.TFA}}<span>m<sup>2</sup></span></li>
                <li><label>TLA:</label>{{selectedSale.TLA}}<span>m<sup>2</sup></span></li>
                <li><label>Land:</label>{{selectedSale.landArea}}<span>Ha</span></li>
            </ul>

            <ul class="cpm-values">
                <li><label>Net Sale Price</label>{{selectedSale.netPrice}}</li>
                <li><label>Chattels</label>{{selectedSale.chattels}}</li>
                <li><label>Analysed Land</label>{{selectedSale.analysedLandValue}}</li>
                <li><label>NSP/CV</label>{{selectedSale.nspCV}}</li>
                <li><label>Building Net Rate</label>{{selectedSale.buildingNetRate}}<span>m<sup>2</sup></span></li>
                <li><label>Gross Rate</label>{{selectedSale.buildingGrossRate}}<span>m<sup>2</sup></span></li>
            </ul>

            <div class="propPics" v-bind:class="'comp-carousel'+selectedSale.id">
                <img v-for="it in selectedSale.propertyPhotos" class="photoGallery_thumb" v-bind:class="'comp-carousel-photo'+selectedSale.id" v-bind:data-id="it.id" v-bind:data-property="it.propertyId" v-bind:src="it.link">
            </div>

            <ul class="cpm-values">
                <li><label>Capital Value</label>{{selectedSale.capitalValue}}<strong>{{selectedSale.cvNetRate}}<span>m<sup>2</sup></span></strong></li>
                <li><label>Land Value</label>{{selectedSale.landValue}}<strong>{{selectedSale.lvNetRate}}<span>m<sup>2</sup></span></strong></li>
                <li><label>Value of Imps</label>{{selectedSale.valueOfImprovements}}<strong>{{selectedSale.viNetRate}}<span>m<sup>2</sup></span></strong></li>
            </ul>

            <ul class="cpm-landMas">
                <li class="icons8-category-filled">		<label>Category</label>{{selectedSale.category}}</li>
                <li class="icons8-calendar-filled">		<label>Effective Year Built</label>{{selectedSale.effectiveYearBuilt}}</li>
                <li class="icons8-bed-filled">			<label>Bedrooms</label>{{selectedSale.bedrooms}}</li>
                <li class="icons8-toilet-bowl-filled">	<label>Toilets</label>{{selectedSale.toilets}}</li>
                <li class="icons8-room-filled">			<label>Units</label>{{selectedSale.units}}</li>
                <li class="icons8-garage-closed-filled"><label>Free Standing Garaging</label>{{selectedSale.freeStandingGarages}}</li>
                <li class="icons8-garage-filled">		<label>Under Main Roof Garaging</label>{{selectedSale.underMainRoofGarages}}</li>
                <li class="icons8-swimming-pool">		<label>Other Large Improvements</label>{{selectedSale.otherLargeImprovements}}</li>
                <li class="icons8-maintenance-filled">	<label>Modernisation</label>{{selectedSale.modernisation}}</li>
                <li class="icons8-crosshair-filled">	<label>Zone</label>{{selectedSale.zone}}</li>
                <li class="icons8-lot-position-filled">	<label>Lot Position</label>{{selectedSale.lotPosition}}</li>
                <li class="icons8-maori-new">			<label>Maori Land</label>{{selectedSale.maoriLand}}</li>
                <li class="icons8-land-use-new">		<label>Land Use</label>{{selectedSale.landUse}}</li>
                <li class="icons8-brick-wall-filled">	<label>Wall Construction and Condition</label>{{selectedSale.wallConstructionAndCondition}}</li>
                <li class="icons8-structural-filled ">	<label>Roof Construction and Condition</label>{{selectedSale.roofConstructionAndCondition}}</li>
                <li class="icons8-field-filled">		<label>Contour</label>{{selectedSale.contour}}</li>
                <li class="icons8-panorama">			<label>View and Scope</label>{{selectedSale.viewScope}}</li>
                <li class="icons8-cow-filled">			<label>Production</label>{{selectedSale.production}}</li>
            </ul>

            <div class="cpm-additionalFeatures">
                <h3>Additional Features for this Property</h3>
                <p>{{selectedSale.otherFeatures}}</p>
            </div>
        </div>

        <div id="salesAnalysis-comment" class="cpm-extraText" v-show="showComments">
            <h3 id="restoreSalesAnalysis">Sales Analysis Comment <!--<i title="Restore" class="material-icons cpm-blue"></i>--></h3>
            <textarea id="salesAnalysis-comment" v-model="selectedSale.saleAnalysis.saleComment" textarea type="text" placeholder="Add a comment..."></textarea>
            <ul>
                <li class="buttonRow">
                    <button id="clearAll-comment"><i class="material-icons cpm-blue">cancel</i></button>
                    <button id="saveAll-comment"><i class="material-icons cpm-blue">check_circle</i></button>
                </li>
            </ul>
        </div>
        <ul id="previewImages" class="cpm-uploadPhoto" style="display:none">
            <li class="buttonRow">
                <button id="clearAll-image"><i class="material-icons cpm-blue">cancel</i></button>
                <button id="saveAll-image"><i class="material-icons cpm-blue">check_circle</i></button>
            </li>
        </ul>
    </div>
</template>
<script>
    import { EventBus } from '../../EventBus.js';
    import { store } from '../../DataStore';
    import numeral from 'numeral';
    import * as json2csv from "json2csv";
    import * as fileSaver from "file-saver";
    import * as ajaxq from '../../ajaxq.js';
    import * as loadImage from 'blueimp-load-image';
    import commonUtils from '../../utils/CommonUtils';
    import * as Dropzone from '../../dropzone';
    import * as download from '../../download';
    import moment from 'moment';

    export default {
        mixins: [commonUtils],
        components: {
        },
        data: function() {
            return {
                comparableSales: [],
                selectedSale: {
                    saleAnalysis:{
                        saleComment:''
                    }
                },
                propertyAddress: '',
                showComps: false,
                showDetailedView: false,
                showListView: true,
                showComments: false,
                currentFile: undefined,
                mediaDropped: [],
                mediaProcessed: [],
                showDropzone: false
            }
        },
        methods: {
            loadDashboard: function() {
                window.location = window.location.protocol+'//'+window.location.hostname+':'+window.location.port+'?home=true';
            },
            getMediaIndexField: function(propertyName, value) {
                var self = this;
                for (var i = 0; i < self.mediaDropped.length; i++)
                    if (self.mediaDropped[i][propertyName] === value && self.mediaDropped[i].uploaded === false) {
                        return i;
                    }
                return -1;
            },
            onDropzoneDone: function(success, file) {
                var self = this;
                var i = self.getMediaIndexField("mediaId", file.mediaId);
                if(self.mediaDropped[i] && file.status == 'success') {
                    var mediaEntry = self.mediaDropped[i].mediaEntry;
                    mediaEntry.mediaItem.status = success ? "UPLOAD_COMPLETED" : "UPLOAD_FAILED";
                    var url = jsRoutes.controllers.MediaController.saveMedia().url;
                    $.ajax({
                        type: "POST",
                        cache: false,
                        url:  url,
                        processData: false,
                        contentType: 'application/json',
                        data: JSON.stringify(mediaEntry)
                    }).done(function (response) {
                        if(self.mediaProcessed.indexOf(file.mediaId) < 0 && file.mediaId) self.mediaProcessed.push(file.mediaId);
                        setTimeout(function() {
                            self.selectedSale.primaryPhoto = response && response.mediaItem ? response.mediaItem.smallImageUrl : self.selectedSale.primaryPhoto
                            self.getPhotos(self.selectedSale.propertyId, self.selectedSale.id)
                        }, 3500);
                        self.showDropzone = false
                    });
                } else {
                    if(self.mediaProcessed.indexOf(file.mediaId) < 0 && file.mediaId) self.mediaProcessed.push(file.mediaId);
                    self.showDropzone = false
                }
            },
            editComments: function(){
                const self = this
                self.showComments = true
            },
            sendToServer: function(){
                var data = {};
                data.property = '';
                data.images = [];
                data.notes = $('#salesAnalysis-comment').val();
                $.each($('canvas'), function(i, v) {
                    data.images.push(v.toDataURL("image/jpeg"));
                });
                return data;
            },
            registerMobilePhotoUploader: function(){
                const self = this
                var camera = (function() {
                    'use strict';
                    var options;
                    var $input;

                    function photoSend(blob){
                        if (options.callback_blob) options.callback_blob(blob);
                        if (!options.xhr && !options.callback_blob && !options.callback_canvas)
                            return alert('Options need either callback_blob or xhr');
                        // Send with Formdata
                        var name = options.xhr_name() + '.' + options.photo_ext;
                        var formData = new FormData();
                        blob.name = name;// if a blob has a name it is a File
                        formData.append('camera', blob, name);
                        var xhr = new XMLHttpRequest();
                        xhr.open('post', options.xhr, true);
                        xhr.upload.onprogress = function(e) {
                            if (e.lengthComputable) {
                                var percentage = Math.round((e.loaded / e.total) * 100);
                                options.callback_xhr && options.callback_xhr(percentage);
                            }
                        };

                        xhr.onerror = function(e) { // TODO
                            options.callback_xhr && options.callback_xhr('An error occurred while submitting the form. Maybe your file is too big' +e);
                        };

                        xhr.onload = function() {
                            // options.callback_xhr &&
                            // options.callback_xhr((this.status===200)?true:this.statusText);
                            options.callback_xhr && options.callback_xhr(this);
                        };

                        xhr.send(formData);
                    }

                    function canvasSend(canvas){
                        // document.body.appendChild(canvas);
                        $('#previewImages').show();
                        var div = $('<li class="imageHolder"><i class="material-icons cpm-light imageDelete">delete</i></li>');
                        div.append(canvas)
                        $('#previewImages').append(div);
                    }

                    function photoResize(evt) {
                        var file = evt.target.files[0];
                        var opts = {
                            maxWidth: options.photo_max_size,
                            maxHeight: options.photo_max_size,
                            canvas: true
                        };
                        loadImage.parseMetaData(file, function (data) {
                            self.currentFile = file
                            if (data.exif) opts.orientation = data.exif.get('Orientation');
                            loadImage(file, canvasSend, opts);
                        });
                    }

                    function makeInputTag(){
                        $input = $('<input type="file" accept="image/*" capture="camera" style="visibility:hidden">')
                            .appendTo('body')
                            .change(photoResize);
                        return $input;
                    }
                    function getInputTag() {
                        return $input || makeInputTag();
                    }
                    function getTimestamp(){return Date.now();}

                    return function(options_){
                        options = $.extend({
                            photo_max_size:     Infinity,     // or 800
                            photo_jpeg_quality:  0.7,         // 0-1 only for jpeg
                            xhr:                 null,        // '/post/path/fotoupload',
                            xhr_name:            getTimestamp,// function returns string
                            callback_xhr:        null,        // function(s){$div.html((s===true)?'Upload
                                                              // finished':(isNaN(s))?'Error'+s:s
                                                              // + '%');} // true ... ok, number
                                                              // ... percentage, string ... error
                            callback_blob:       null,        // function(blob){do_something},
                            callback_canvas:     null         // function(canvas){$('#image').attr('src',
                                                              // canvas.toDataUrl());}
                        },options_,{
                            photo_type:         'image/jpeg', // can not be changed
                            photo_ext:          'jpg'        // can not be changed
                        });
                        getInputTag().trigger('click');
                    };
                }());

                var showPhoto = function(canvas){$('#image').attr('src', canvas.toDataUrl());};

                $('#takePhoto').off("click").click(function(){
                    self.showDropzone = true
                    camera({
                        photo_jpeg_quality: 0.7,       // jpeg-quality from 0 to 1.0
                        xhr:                '/foto',   // url of where the photo shall be
                        // uploaded
                        callback_xhr:       null,  // show some info on upload progress
                        callback_canvas:   showPhoto  // display the photo in an img-element
                    });
                });
            },
            getFormattedDate: function(date) {
                if (date && date != '') {
                    return moment(String(date)).format('DD/MM/YYYY')
                }
                return ''
            },
            getRenderableValue: function(val) {
                return (val && val != '') ? val : '-'
            },
            showListOfSales: function(){
                const self = this
                self.selectedSale = {}
                self.selectedSale.saleAnalysis = {}
                self.selectedSale.saleAnalysis.saleComment = ''
                self.showDetailedView = false
                self.showListView = true
                self.showComments = false
            },
            registerCarousel: function(key) {
                $(".comp-carousel"+key).not('.slick-initialized').slick({
                    dots: true,
                    infinite: true,
                    speed: 300,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    swipe: true
                });
            },
            getPhotos: function(property, key) {
                var self = this;
                var media = jsRoutes.controllers.MediaController.getMediaByOwner(property);
                $.ajax({
                    type: "GET",
                    url: media.url,
                    cache: false,
                    success: function (response) {
                        self.registerCarousel(key);
                        $('.comp-carousel'+key).slick('removeSlide', null, null, true);
                        $.each(response, function(index, photo) {
                            $('.comp-carousel'+key).slick('slickAdd',
                                "<img class=\"photoGallery_thumb comp-carousel-photo"+key+"\" data-id=\"" + photo.id + "\" data-property=\"" + photo.ownerId + "\" src=\""+ photo.mediaItem.mediumImageUrl +"\"/>");
                        });
                    },
                    error: function (response) {
                        console.log("Unable to get photos");
                        self.errorHandler(response);
                    }
                });
            },
            showDetailedSale: function(sale){
                const self = this
                self.selectedSale = sale
                self.showListView = false
                self.showDetailedView = true
            },
            getPropertyPhotos: function(photoURLs, propertyId, qupid) {
                var propertyPhotos = [];
                var primaryPhoto = 'assets/images/property/addPhotos.png';
                $.each(photoURLs, function (i, obj) {
                    if(obj.isPrimary) {
                        primaryPhoto = obj.mediaItem.smallImageUrl;
                    }
                    propertyPhotos.push({
                        'id': obj.id,
                        'propertyId': propertyId,
                        'qupid': qupid,
                        'link': obj.mediaItem.mediumImageUrl
                    });
                });
                var result = {primary: primaryPhoto, list: propertyPhotos};
                return result;
            },
            getURLParam: function(sParam){
                var sPageURL = window.location.search.substring(1);
                var sURLVariables = sPageURL.split('&');
                for (var i = 0; i < sURLVariables.length; i++){
                    var sParameterName = sURLVariables[i].split('=');
                    if (sParameterName[0] == sParam){
                        return sParameterName[1];
                    }
                }
            },
            generateCompSale: function(comp, updatePhotos) {
                const self = this
                var sale = comp.sale
                var property = comp.primaryProperty
                var saleAnalysis = comp.saleAnalysis
                var photoURLs = comp.photoURLs
                var compToRender = {}
                compToRender.id = sale.id
                compToRender.saleAnalysis = comp.saleAnalysis ? comp.saleAnalysis : {}
                compToRender.originalSaleAnalysis = JSON.parse(JSON.stringify(compToRender.saleAnalysis))
                compToRender.summary = comp.summary
                compToRender.qivsSaleId = sale.qivsSaleId
                compToRender.parties = sale.parties
                compToRender.qupid = sale.primaryProperty ? sale.primaryProperty.qupid : ''
                //compToRender.propertyId = comp.property.properties[0].id
                compToRender.propertyId = comp.propertyID
                compToRender.lastSaleDate = self.getFormattedDate(sale.saleDate)
                compToRender.rawSaleDate = moment(sale.saleDate).valueOf()
                compToRender.effectiveDate = self.getFormattedDate(sale.revisionDate)
                compToRender.other = sale.other ? numeral(sale.other).format('$0,0') : '$0'
                compToRender.gst = sale.gst ? numeral(sale.gst).format('$0,0') : '$0'
                var classifications = sale.classifications
                compToRender.classification = classifications ? (classifications.saleType ? classifications.saleType.code : '') : ''
                compToRender.classification += classifications ? (classifications.saleTenure ? classifications.saleTenure.code : '') : ''
                compToRender.classification += classifications ? (classifications.priceValueRelationship ? classifications.priceValueRelationship.code : '') : ''
                compToRender.priceValueRelationship = classifications ? (classifications.priceValueRelationship ? classifications .priceValueRelationship.code : '') : ''
                var currProperty = sale.primaryProperty
                var streetNumberSuffix = currProperty.address ? (currProperty.address.streetNumberSuffix ? ' '+currProperty.address.streetNumberSuffix : '') : ''
                compToRender.address1 = currProperty.address ? ((currProperty.address.streetNumber ? currProperty.address.streetNumber : '')+streetNumberSuffix+ ' ' + (currProperty.address.streetName ? currProperty.address.streetName : '')  + (currProperty.address.streetType ? ' '+currProperty.address.streetType.description + ',' : '')) : ''
                if (compToRender.address1.indexOf('undefined') !== -1) {
                    compToRender.address1 = ''
                }
                compToRender.address2 = currProperty.address ? (currProperty.address.suburb ? (currProperty.address.suburb+', ') : '') : ''
                compToRender.address2 += currProperty.address ? (currProperty.address.town ? currProperty.address.town + ', ' : '') : ''
                compToRender.address2 += currProperty.territorialAuthority ? currProperty.territorialAuthority.name : ''
                if (compToRender.address2.indexOf('undefined') !== -1) {
                    compToRender.address2 = ''
                }

                compToRender.fullAddress = compToRender.address1 + compToRender.address2

                compToRender.valRef = currProperty.rollNumber ? currProperty.rollNumber : ''
                compToRender.valRef += currProperty.assessmentNumber ? '/'+currProperty.assessmentNumber : ''
                compToRender.valRef += currProperty.suffix ? ' ' + currProperty.suffix : ''
                if (compToRender.valRef.indexOf('undefined') !== -1) {
                    compToRender.valRef = ''
                }

                compToRender.legalDescription = currProperty.legalDescription

                compToRender.category = self.getRenderableValue(currProperty.category ? currProperty.category.code : '')
                compToRender.TLA = currProperty.massAppraisalData ? (currProperty.massAppraisalData.totalLivingArea ? currProperty.massAppraisalData.totalLivingArea : '0') : '0'
                compToRender.TFA = currProperty.landUseData ? (currProperty.landUseData.totalFloorArea ? currProperty.landUseData.totalFloorArea : '0') : '0'
                //compToRender.dist = 10
                //compToRender.saleAnalysis = self.getSaleAnalysisFromPropertyData(sale.hasSaleAnalysis, compToRender.category.charAt(0))

                compToRender.landArea = currProperty.landUseData ? currProperty.landUseData.landArea : ''
                if (typeof compToRender.landArea == 'number') {
                    compToRender.landArea = compToRender.landArea.toFixed(4)
                }

                compToRender.landValue = numeral(sale.landValue).format('$0,0')
                compToRender.capitalValue = numeral(sale.capitalValue).format('$0,0')

                var valueOfImprovements = 0
                var landValue = sale.landValue
                var capitalValue = sale.capitalValue
                var totalFloorArea = currProperty.landUseData ? currProperty.landUseData.totalFloorArea : 0
                var landArea = currProperty.landUseData ? currProperty.landUseData.landArea : 0
                var netPrice = sale.price ? sale.price.net : 0

                var saleNetRate = 0
                if (netPrice > 0 && totalFloorArea > 0) {
                    saleNetRate = Math.round(netPrice/totalFloorArea)
                }
                compToRender.saleNetRate = numeral(saleNetRate).format('$0,0')

                if (landValue && landValue > 0 && capitalValue && capitalValue > 0) {
                    valueOfImprovements = Math.round(capitalValue - landValue)
                }
                compToRender.valueOfImprovements = numeral(valueOfImprovements).format('$0,0')

                var cvNetRate = 0
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0) {
                    cvNetRate = Math.round(capitalValue/totalFloorArea)
                }
                compToRender.cvNetRate = numeral(cvNetRate).format('$0,0')

                var lvNetRate = 0
                if (landArea > 0 && landValue && landValue > 0) {
                    lvNetRate = Math.round(landValue/(landArea*10000))
                }
                compToRender.lvNetRate = numeral(lvNetRate).format('$0,0')

                var viNetRate = 0
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0 && landValue && landValue > 0) {
                    viNetRate = Math.round((capitalValue - landValue)/totalFloorArea)
                }
                compToRender.viNetRate = numeral(viNetRate).format('$0,0')

                compToRender.netPrice = numeral(sale.price ? sale.price.net : 0).format('$0,0')
                compToRender.grossPrice = numeral(sale.price ? sale.price.gross : 0).format('$0,0')
                compToRender.rawGrossPrice = sale.price ? sale.price.gross : 0
                compToRender.chattels = numeral(sale.price ? sale.price.chattels : 0).format('$0,0')

                compToRender.analysedLandValue = numeral(saleAnalysis.totalAnalysedLandValue).format('$0,0')

                var nspCV = 0
                if (capitalValue && capitalValue > 0) {
                    nspCV = (Math.round((netPrice/capitalValue)*100))/100
                }
                compToRender.nspCV = nspCV

                compToRender.buildingNetRate = numeral(saleAnalysis.analysedMainBuilding ? saleAnalysis.analysedMainBuilding.pricePerSquareMeter : 0).format('$0,0')

                var grossRate = 0
                if (netPrice > 0 && totalFloorArea > 0) {
                    grossRate = Math.round(netPrice/totalFloorArea)
                }
                compToRender.buildingGrossRate = numeral(grossRate).format('$0,0')

                compToRender.status = sale.status ? sale.status.description : ''

                var data = currProperty
                compToRender.buildingAge = self.getRenderableValue(data.landUseData ? (data.landUseData.buildingAge ? data.landUseData.buildingAge.description : '') : '')
                compToRender.siteCoverage = self.getRenderableValue(data.landUseData ? data.landUseData.buildingSiteCover : '')
                compToRender.carParks = self.getRenderableValue(data.landUseData ? data.landUseData.carparks : '')
                compToRender.csi = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.classOfSurroundingImprovements ? data.massAppraisalData.classifications.classOfSurroundingImprovements.description : '') : '') : '')
                compToRender.houseType = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.houseType ? data.massAppraisalData.classifications.houseType.description : '') : '') : '')
                compToRender.houseTypeObj = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.houseType ? data.massAppraisalData.classifications.houseType : null) : null) : null)

                compToRender.landscaping = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.landscapingQuality ? data.massAppraisalData.classifications.landscapingQuality.description : '') : '') : '')
                compToRender.deck = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasDeck ? 'Yes' : 'No') : '')
                compToRender.foundation = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasPoorFoundations ? 'Yes' : 'No') : '')
                compToRender.laundryWorkshop = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasLaundry ? 'Yes' : 'No') : '')
                compToRender.carAccess = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasCarAccess ? 'Yes' : 'No') : '')
                compToRender.driveway = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasDriveway ? 'Yes' : 'No') : '')
                compToRender.outlier = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.isOutlier ? 'Yes' : 'No') : '')
                compToRender.effectiveYearBuilt = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.effectiveYearBuilt : '')
                compToRender.landUse = self.getRenderableValue(data.landUseData ? (data.landUseData.landUse ? data.landUseData.landUse.description : '') : '')
                compToRender.units = self.getRenderableValue(data.landUseData ? data.landUseData.units : '')
                compToRender.bedrooms = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.bedrooms : '')
                compToRender.toilets = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.toilets : '')
                compToRender.wallConstructionAndCondition = data.landUseData ? (data.landUseData.wallConstruction ? data.landUseData.wallConstruction.description : '') : ''
                compToRender.wallConstruction = data.landUseData ? (data.landUseData.wallConstruction ? data.landUseData.wallConstruction : null) : null;
                compToRender.wallConstructionAndCondition += data.landUseData ? (data.landUseData.wallCondition ? ' '+data.landUseData.wallCondition.description : '') : ''
                compToRender.wallConstructionAndCondition = self.getRenderableValue(compToRender.wallConstructionAndCondition)
                compToRender.roofConstruction = data.landUseData ? (data.landUseData.roofConstruction ? data.landUseData.roofConstruction : null) : null;
                compToRender.roofConstructionAndCondition = data.landUseData ? (data.landUseData.roofConstruction ? data.landUseData.roofConstruction.description : '') : ''
                compToRender.roofConstructionAndCondition += data.landUseData ? (data.landUseData.roofCondition ? ' '+data.landUseData.roofCondition.description : '') : ''
                compToRender.roofConstructionAndCondition = self.getRenderableValue(compToRender.roofConstructionAndCondition)
                compToRender.underMainRoofGarages = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.underMainRoofGarages : '')
                compToRender.freeStandingGarages = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.freestandingGarages : '')
                compToRender.otherLargeImprovements = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasLargeOtherImprovements ? 'Yes' : 'No') : 'No')
                compToRender.modernisation = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.isModernised ? 'Yes' : 'No') : 'No')
                compToRender.zone = self.getRenderableValue(data.landUseData ? data.landUseData.landZone : '')
                compToRender.lotPosition = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.lotPosition ? data.massAppraisalData.classifications.lotPosition.description : '') : '') : '')
                compToRender.contour = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.contour ? data.massAppraisalData.classifications.contour.description : '') : '') : '')
                var viewDescription = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.view ? (data.massAppraisalData.classifications.view.description+' ') : '') : '') : '')
                var viewCode = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.view ? (data.massAppraisalData.classifications.view.code+' ') : '') : '') : '')
                if(viewCode.trim() == 'N'){
                    compToRender.viewScope = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.viewScope ? data.massAppraisalData.classifications.viewScope.description : '') : '') : '')
                }else{
                    compToRender.viewScope = ((viewDescription && viewDescription != '-') ? viewDescription.substring(viewDescription.trim().lastIndexOf(" ")+1) : '') + self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.viewScope ? data.massAppraisalData.classifications.viewScope.description : '') : '') : '')
                }

                compToRender.production = self.getRenderableValue(data.landUseData ? data.landUseData.production : '')
                compToRender.maoriLand = data.landUseData ? (data.landUseData.isMaoriLand ? 'Yes' : 'No') : 'No'

                if (updatePhotos) {
                    var propertyPhotos = self.getPropertyPhotos(photoURLs, compToRender.propertyId, compToRender.qupid)
                    compToRender.propertyPhotos = propertyPhotos.list
                    compToRender.primaryPhoto = propertyPhotos.primary
                }
                else {
                    self.refreshPhotos = true
                }

                compToRender.mapUrl = 'http://qvartgis01:8080/QVMS/?qpid='
                return compToRender
            },
            loadCompSales: function(valuationJobId, comparableSales, updatePhotos) {
                var self = this
                var saleIds = []
                if(comparableSales) {
                    for (var i = 0; i < comparableSales.length; i++) {
                        saleIds.push(comparableSales[i].qivsSaleId)
                    }
                }
                if (saleIds && saleIds.length > 0) {
                    var searchCriteria = {}
                    searchCriteria.qivsSaleIds = saleIds
                    searchCriteria.max = saleIds.length

                    var compSaleCriteria = {}
                    compSaleCriteria.saleSearchCriteria = searchCriteria
                    compSaleCriteria.homeValuationId = valuationJobId

                    $.ajax({
                        type: "POST",
                        url: jsRoutes.controllers.ComparableSales.getComparableSaleBySaleId().url,
                        data: JSON.stringify(compSaleCriteria),
                        contentType: "application/json; charset=utf-8",
                        cache: false,
                        dataType: "json",
                        success: function (response) {
                            if (response.length > 0) {
                                self.comparableSales = []
                                for (var i = 0; i < response.length; i++) {
                                    var compSale = self.generateCompSale(response[i], updatePhotos)
                                    if (comparableSales) {
                                        $.each(comparableSales, function (i, obj) {
                                            if (obj.id == compSale.id && obj.selected) {
                                                if (obj.otherFeatures) {
                                                    compSale.otherFeatures = obj.otherFeatures;
                                                }
                                                self.comparableSales.push(compSale)
                                            }
                                        });
                                    }
                                }
                                self.showComps = true
                            }
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            console.log('Error while refreshing comparable sales => ' + response)
                        }
                    });
                } else {
                    self.showComps = true
                }
            }
        },
        created: function() {
            const self = this
            var valJobId = self.getURLParam('valJobId');
            if (valJobId) {
                $.ajax({
                    type: "GET",
                    url: jsRoutes.controllers.HomeValuation.getHomeValuation(valJobId).url,
                    cache: false,
                    success: function (response) {
                        var comparableSales = response.comparableSales
                        var currProperty = response.propertySummary
                        var streetNumberSuffix = currProperty.address ? (currProperty.address.streetNumberSuffix ? ' '+currProperty.address.streetNumberSuffix : '') : ''
                        var address1 = currProperty.address ? ((currProperty.address.streetNumber ? currProperty.address.streetNumber : '')+streetNumberSuffix+ ' ' + (currProperty.address.streetName ? currProperty.address.streetName : '')  + (currProperty.address.streetType ? ' '+currProperty.address.streetType.description + ',' : '')) : ''
                        if (address1.indexOf('undefined') !== -1) {
                            address1 = ''
                        }
                        var address2 = currProperty.address ? (currProperty.address.suburb ? (currProperty.address.suburb+', ') : '') : ''
                        address2 += currProperty.address ? (currProperty.address.town ? currProperty.address.town + ', ' : '') : ''
                        address2 += currProperty.territorialAuthority ? currProperty.territorialAuthority.name : ''
                        if (address2.indexOf('undefined') !== -1) {
                            address2 = ''
                        }

                        self.propertyAddress = address1 + address2

                        self.loadCompSales(valJobId, comparableSales, true)
                    },
                    error: function (response) {
                        console.log("Error in getting home valuation");
                        self.errorHandler(response);
                    }
                });
            }
        },
        updated: function() {
            const self = this
            if (self.showDetailedView && !self.showComments) {
                self.registerMobilePhotoUploader()
                //setTimeout(function(){
                    self.getPhotos(self.selectedSale.propertyId, self.selectedSale.id)
                //},1000)
            }
            $(document).off("click").on( 'click', '.cpm-card', function(evt) {
                $('.cpm-expanded').show()
                $('#slick-slide-control00').trigger('click')
                $( 'body' ).addClass( "noScroller" );
                propertyId = $(evt.target).closest('.cpm-card').data().qupid
            });

            $(document).off("click").on( 'click', '.imageDelete', function(evt) {
                $(evt.target).parent().remove();
                if ($('.imageHolder').length == 0) {
                    $('#previewImages').hide();
                }
            });

            $('#doBack').off("click").click(function(evt) {
                $('.cpm-expanded').hide();
                $('.imageHolder').remove();
                $('#previewImages').hide();
                $('.cpm-extraText').hide();
                $('.cpm-extraText textarea').val('');
                $( 'body' ).removeClass( "noScroller" );
            });

            $('#clearAll-comment').off("click").click(function() {
                self.selectedSale.saleAnalysis = JSON.parse(JSON.stringify(self.selectedSale.originalSaleAnalysis))
                self.showComments = false
            });

            $('#restoreSalesAnalysis').off("click").click(function() {
                self.selectedSale.saleAnalysis = JSON.parse(JSON.stringify(self.selectedSale.originalSaleAnalysis))
                //self.showComments = false
            });

            $('#saveAll-comment').off("click").click(function() {
                self.showComments = false
                var data = self.selectedSale.saleAnalysis

                var analysisData = JSON.stringify(data);
                var m = jsRoutes.controllers.SalesAnalysis.updateSalesAnalysis();
                $.ajax({
                    type: "POST",
                    url: m.url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: analysisData,
                    dataType: "json",
                    success: function (response) {
                        self.selectedSale.originalSaleAnalysis = JSON.parse(JSON.stringify(self.selectedSale.saleAnalysis))
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            });


            $('#clearAll-image').off("click").click(function() {
                $('.imageHolder').remove();
                $('#previewImages').hide();
            });

            $('#saveAll-image').off("click").click(function() {
                //self.sendToServer();
                var acceptedFiles = '.png,.jpg,.gif,.bmp,.jpeg,.pdf,.xls,.xlsx,.doc,.docx,.tif,.tiff';
                Dropzone.autoDiscover = false;
                var dropzone = new Dropzone("#photo-dropzone-HomeValuationReport",{
                    autoDiscover: false,
                    acceptedFiles: acceptedFiles,
                    autoProcessQueue: false,
                    method: 'PUT',
                    contentType: false,
                    maxFiles: 1,
                    uploadMultiple: false});

                var data = {
                    "ownerId": self.selectedSale.propertyId,
                    "category": "Property",
                    "isPrimary":true,
                    "description":"",
                    "tags": [],
                    "mediaItem":{
                        "fileName": self.currentFile.name,
                        "contentType": (self.currentFile.type == 'image/tif') ? 'image/tiff' : self.currentFile.type,
                        "uploadedDate": new Date(Date.now()).toUTCString(),
                        "captureDate": self.currentFile.lastModifiedDate ? self.currentFile.lastModifiedDate : new Date(Date.now()).toUTCString(),
                        "tags": [],
                        "improvementDateRange": ""
                    }
                };
                $.ajax({
                    type: "POST",
                    cache: false,
                    url:  jsRoutes.controllers.MediaController.generateMediaEntryID().url,
                    processData: false,
                    contentType: 'application/json',
                    data: JSON.stringify(data)
                }).done(function (response) {
                    dropzone.options.url = response.mediaItem.imageUrl;
                    dropzone.options.headers = {'Content-Type': (self.currentFile.type == 'image/tif') ? 'image/tiff' : self.currentFile.type};
                    self.mediaDropped.push({
                        mediaId: response.mediaItem.id,
                        fileName: response.mediaItem.fileName,
                        mediaEntry: response,
                        uploaded: false
                    });
                    self.currentFile.mediaId = response.mediaItem.id;
                    dropzone.processFile(self.currentFile);
                });

                dropzone.on("success", function (file) {
                    self.onDropzoneDone(true, file);
                });

                dropzone.on("error", function (file) {
                    self.onDropzoneDone(false, file);
                });

                $('.imageHolder').remove();
                $('#previewImages').hide();
            });

/*            $('#saveAll-comment').off("click").click(function() {
                self.sendToServer();
                $('#salesAnalysis-comment').hide();
            });*/
        }
    }
</script>

<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
    <main class="jobInfo-view">
        <div class="jobInfo-wrapper">

            <ul class="jobInfo-item">
                <li data-cy="site-inspection-notes" class="Site_Inspection_Notes">
                    <i class="icons8-pencil"></i>
                    <div>
                        <label>Site Inspection Notes</label>
                        <span title="Site Inspection Notes">{{ job.jobInstruction.siteInspectionNotes }}</span>
                    </div>
                </li>
            </ul>

            <ul class="jobInfo-item">
                <li data-cy="inspection-type" class="Inspection_Type">
                    <i class="icons8-happy"></i>
                    <div>
                        <label>Inspection Type</label>
                        <span title="Inspection Type">{{ job.jobInstruction.inspectionType.description }}</span>
                    </div>
                </li>
                <li data-cy="inspection-date" class="Inspection_Date">
                    <i class="icons8-calendar-filled"></i>
                    <div>
                        <label>Inspection Date</label>
                        <span title="Inspection Date">{{ formatDate(job.inspectionDate, 'DD/MM/YYYY') }}</span>
                    </div>
                </li>
                <li data-cy="inspection-time" class="Inspection_Time">
                    <i class="icons8-clock"></i>
                    <div>
                        <label>Inspection Time</label>
                        <span title="Inspection Time">{{ formatDate(job.inspectionDate, 'HH:mm') }}</span>
                    </div>
                </li>
            </ul>

            <ul class="jobInfo-item">
                <li data-cy="valuation-report-type" class="Valuation_Report_Type">
                    <i class="icons8-news-filled"></i>
                    <div>
                        <label>Valuation Report Type</label>
                        <span title="Valuation Report Type">{{ job.reportType.description }}</span>
                    </div>
                </li>
                <li data-cy="purpose-of-valuation" class="Purpose_of_Valuation">
                    <i class="icons8-ask-question"></i>
                    <div>
                        <label>Purpose of Valuation</label>
                        <span title="Purpose of Valuation">{{ job.purpose.description }}</span>
                    </div>
                </li>
            </ul>

            <ul class="jobInfo-item">
                <li data-cy="valuation-date" class="Valuation_Date">
                    <i class="icons8-calendar-filled"></i>
                    <div>
                        <label>Valuation Date</label>
                        <span title="Valuation Date">{{ formatDate(job.valuationDueDate, 'DD/MM/YYYY') }}</span>
                    </div>
                </li>
                <li data-cy="valuation-time" class="Valuation_Time">
                    <i class="icons8-clock"></i>
                    <div>
                        <label>Valuation Time</label>
                        <span title="Valuation Time">{{ formatDate(job.valuationDueDate, 'HH:mm') }}</span>
                    </div>
                </li>
            </ul>

            <ul class="jobInfo-item">
                <li data-cy="valuer" class="Valuer">
                    <i class="icons8-edit-user-male"></i>
                    <div>
                        <label>Valuer</label>
                        <span title="Valuer">{{ job.valuer.name }}</span>
                    </div>
                </li>
                <li data-cy="countersigned-by" class="Countersigned_By">
                    <i class="icons8-edit-user-male"></i>
                    <div>
                        <label>Countersigned By</label>
                        <span title="Countersigned By">{{ job.countersigner.name }}</span>
                    </div>
                </li>
            </ul>

            <ul class="jobInfo-item">
                <li data-cy="client-name" class="Client_Name">
                    <i class="icons8-contacts"></i>
                    <div>
                        <label>Client Name</label>
                        <span title="Client Name">{{ job.jobInstruction.clientName }}</span>
                    </div>
                </li>
                <li data-cy="borrower" class="Borrower">
                    <i class="icons8-contacts"></i>
                    <div>
                        <label>Borrower</label>
                        <span title="Borrower">{{ job.jobInstruction.borrower }}</span>
                    </div>
                </li>
                <li data-cy="lender-name" class="Lender_Name">
                    <i class="icons8-museum-filled"></i>
                    <div>
                        <label>Lender Name</label>
                        <span title="Lender Name">{{ job.jobInstruction.lenderName }}</span>
                    </div>
                </li>
                <li data-cy="instructed-by" class="Instructed_By">
                    <i class="icons8-contacts"></i>
                    <div>
                        <label>Instructed By</label>
                        <span title="Instructed By">{{ job.jobInstruction.instructedBy }}</span>
                    </div>
                </li>
                <li data-cy="extended-to" class="Extended_To">
                    <i class="icons8-contacts"></i>
                    <div>
                        <label>Extended To</label>
                        <span title="Extended To">{{ job.jobInstruction.extendedTo }}</span>
                    </div>
                </li>
            </ul>

            <ul class="jobInfo-item">
                <li data-cy="other-instructions" class="Other_Instructions">
                    <i class="icons8-pencil"></i>
                    <div>
                        <label>Other Instructions</label>
                        <span title="Other Instructions">{{ job.jobInstruction.otherInstructions }}</span>
                    </div>
                </li>
            </ul>
        </div>

    </main></template>
<script>
    import {store} from '../../DataStore';
    import formatUtils from '../../utils/FormatUtils';

    export default {
        props: ['job'],
        mixins: [formatUtils]
    }
</script>

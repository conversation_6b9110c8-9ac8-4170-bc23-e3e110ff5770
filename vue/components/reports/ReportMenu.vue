<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router/composables';
import ReportCategoryTreeView from './ReportCategoryTreeView.vue';
import { objectToSortedArray } from '../../utils/common';

const router = useRouter();
const routeName = computed(() => router.currentRoute.name);
defineProps(['reports']);

function viewMyReports() {
    if (routeName !== 'report-dashboard-my-reports') {
        router.push({ name: 'report-dashboard-my-reports' });
    }
}

</script>

<template>
    <div class="qv-flex-column menu-area mdl-shadow--3dp">
        <div>
            <button
                class="my-reports-button"
                @click="viewMyReports"
                data-cy="my-reports-button"
            >
                VIEW MY REPORTS
            </button>
        </div>
        <div
            class="menu"
            data-cy="reports-menu"
        >
            <template v-for="reportCategory in objectToSortedArray(reports, 'name')">
                <report-category-tree-view :key="reportCategory.id" :reportCategory="reportCategory" />
            </template>
        </div>
    </div>
</template>

<script setup>

import Vue, { onMounted, ref } from 'vue';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";

// Define props and get their values
const props = defineProps({
    jsonSchema: {
        type: Object,
        required: true,
    },
});
const jsonSchema = ref(props.jsonSchema);

// Define refs
const flattenedJsonSchema = ref({});
const parameters = ref({});

// Define emits
const emit = defineEmits(['submit']);
defineExpose({
    submit,
    clear,
});

onMounted(() => {
    // Create flattened JSON schema
    flattenedJsonSchema.value = flattenJsonSchema(jsonSchema.value);

    // Set default values in parameters
    for (const path in flattenedJsonSchema.value) {
        const schema = flattenedJsonSchema.value[path];
        if (Object.hasOwn(schema, 'default')) {
            // If we don't use Vue.set, the default value won't be reactive
            Vue.set(parameters.value, path, schema.default);
        }
    }
});

/**
 * Flatten a JSON schema into a simple object with the following structure:
 * {
 *     "path/to/property": {
 *         "type": "Property Type",
 *         "format": "Property Format",
 *         "default": "Property Default Value/Hint",
 *         "title": "Property Title",
 *         "description": "Property Description"
 *     },
 *     ...
 * }
 * NOTE: Objects in arrays are not flattened
 */
function flattenJsonSchema(schema, path = '') {
    const flattenedSchema = {};

    if (schema.type === 'object') {
        Object.keys(schema.properties).forEach(property => {
            const newPath = path === '' ? property : `${path}/${property}`;
            Object.assign(flattenedSchema, flattenJsonSchema(schema.properties[property], newPath));
        });
    }
    else {
        flattenedSchema[path] = JSON.parse(JSON.stringify(schema));
    }

    return flattenedSchema;
}

/**
 * Expand a flattened object into an object that matches the JSON schema
 * NOTE: Only supports integer, string and array JSON schema types
 */
function expandObject(flattenedObject, flattenedJsonSchema) {
    const expandedObject = {};

    for (const key in flattenedObject) {
        const keyParts = key.split('/');
        let currentObject = expandedObject;
        for (let i = 0; i < keyParts.length; i++) {
            const keyPart = keyParts[i];
            if (i === keyParts.length - 1) {
                const schema = flattenedJsonSchema[key];
                if (schema.include === false) {
                    // skip this parameter to exclude from report request
                    continue;
                }
                if (Array.isArray(schema.type)) {
                    schema.type = schema.type[0];
                }
                if (schema.type === 'integer') {
                    currentObject[keyPart] = parseInt(flattenedObject[key]);
                }
                else if (schema.type === 'string') {
                    currentObject[keyPart] = flattenedObject[key];
                }
                else if (schema.type === 'array') {
                    if (Object.hasOwn(schema['items'], 'enum')) { // Already an array
                        currentObject[keyPart] = flattenedObject[key];
                    }
                    else { // Comma-separated string
                        currentObject[keyPart] = flattenedObject[key].split(',').map(value => {
                            if (schema['items'].type === 'integer') {
                                return parseInt(value);
                            }
                            else {
                                return value;
                            }
                        });
                    }
                }
                else {
                    console.error(`Unsupported JSON schema type: ${schema.type}`);
                }
            }
            else {
                if (!currentObject[keyPart]) {
                    currentObject[keyPart] = {};
                }
                currentObject = currentObject[keyPart];
            }
        }
    }

    return expandedObject;
}

function isDropdown(path) {
    if (!Object.hasOwn(flattenedJsonSchema.value, path) || typeof flattenedJsonSchema.value[path] !== 'object') {
        return false;
    }

    const schema = flattenedJsonSchema.value[path];

    // Single-select with more than 5 options
    if (Object.hasOwn(schema, 'enum') && Array.isArray(schema['enum']) && schema['enum'].length > 5) {
        return true;
    }

    // Multi-select
    return schema.type === 'array' && Object.hasOwn(schema, 'items') &&
        Object.hasOwn(schema['items'], 'enum') && Array.isArray(schema['items']['enum']);
}

function isDropdownMultiple(path) {
    return isDropdown(path) && flattenedJsonSchema.value[path].type === 'array';
}

function getDropdownOptions(path) {
    return isDropdownMultiple(path) ? flattenedJsonSchema.value[path]['items']['enum'] : flattenedJsonSchema.value[path]['enum'];
}

function getDropdownPlaceholder(path) {
    return isDropdownMultiple(path) ? 'Select one or more' : 'Select one';
}

function isRadio(path) {
    if (!Object.hasOwn(flattenedJsonSchema.value, path) || typeof flattenedJsonSchema.value[path] !== 'object') {
        return false;
    }

    // Single-select with 5 or fewer options
    const schema = flattenedJsonSchema.value[path];
    return Object.hasOwn(schema, 'enum') && Array.isArray(schema['enum']) && schema['enum'].length < 6;
}

function isDatePicker(parameter) {
    return flattenedJsonSchema.value[parameter].format === 'date';
}

function getLabel(option, labelStr, index) {
    if (labelStr) {
        const labels = labelStr.split(',');
        if (labels.length > index) {
            return labels[index];
        }
    }
    return option;
}

function submit() {
    // Expand flattened parameters into an object that matches the JSON schema
    const expandedParameters = expandObject(parameters.value, flattenedJsonSchema.value);

    // Emit the expanded parameters
    emit('submit', {
        status: 'ok',
        data: expandedParameters,
    });
}

function showControl(parameter) {
    let display = flattenedJsonSchema.value[parameter].display;
    if (display === undefined || display === null) {
        return true;
    }
    if (display.constructor === Boolean) {
        return display;
    }

    if (display.constructor === String) {
        let showControl = true;
        const displayConditions = JSON.parse(display);
        for (let i = 0; i < displayConditions.length; i++) {
            if (parameters.value[displayConditions[i].fieldName] !== displayConditions[i].fieldValue) {
                showControl = false;
            }
        }
        // if this field is hidden due to display conditions, set 'include' property to false
        // fields that are part of a 'oneOf' JSON schema in report definition will fail if both are present in request (regardless of value of the field)
        flattenedJsonSchema.value[parameter]['include'] = showControl;
        return showControl;
    }
    return false;
}

function controlType(parameter) {
    if (isDropdown(parameter)) {
        return 'dropdown';
    }
    if (isRadio(parameter)) {
        return 'radio';
    }
    if (isDatePicker(parameter)) {
        return 'datepicker';
    }
    return 'text';
}

function inputType(parameter) {
    if (parameter.display === false) {
        return 'hidden';
    }
    if (parameter.type === 'integer') {
        return 'number';
    }
    return 'text';
}

function convertToKebabCase(str) {
    if (str === undefined) {
        return 'undefined';
    }
    return str
        .replace(/([a-z])([A-Z])/g, "$1-$2")
        .replace(/[\s_]+/g, "-")
        .toLowerCase();
}

function clear() {
    for (const path in flattenedJsonSchema.value) {
        if (Object.hasOwn(flattenedJsonSchema.value[path], 'default')) {
            parameters.value[path] = flattenedJsonSchema.value[path].default;
        }
        else {
            parameters.value[path] = '';
        }
    }
}

</script>

<template>
    <div class="qv-flex-column report-criteria">
        <template v-for="path in Object.keys(flattenedJsonSchema)">
            <label
                v-if="showControl(path)"
                :key="path+'_h3'"
                :for="path"
                :title="flattenedJsonSchema[path].description"
            >
                {{ flattenedJsonSchema[path].title }}
            </label>
            <div
                v-show="showControl(path)"
                class="qv-flex-row"
                :key="path+'_input'"
            >
                <!-- Dropdown for multi-select or single-select with more than 5 options -->
                <multiselect
                    v-if="controlType(path) === 'dropdown'"
                    v-model="parameters[path]"
                    :clear-on-select="false"
                    :close-on-select="!isDropdownMultiple(path)"
                    :multiple="isDropdownMultiple(path)"
                    :options="getDropdownOptions(path)"
                    :placeholder="getDropdownPlaceholder(path)"
                    :preserve-search="false"
                    :data-cy= "`${convertToKebabCase(flattenedJsonSchema[path].title)}-selector`"
                />

                <!-- Radio buttons for single-select with 5 or fewer options -->
                <div v-if="controlType(path) === 'radio'">
                    <div v-for="(option, index) in flattenedJsonSchema[path].enum" :key="option">
                        <input
                            :id="option"
                            v-model="parameters[path]"
                            :name="path"
                            :value="option"
                            type="radio"
                        />
                        <label :for="option">{{ getLabel(option, flattenedJsonSchema[path].label, index) }}</label>
                    </div>
                </div>

                <date-picker
                    v-if="controlType(path) === 'datepicker'"
                    v-model="parameters[path]"
                    :name="path"
                    class="report-datepicker"
                    format="D/M/YYYY"
                    type="date"
                    value-type="YYYY-MM-DD"
                    :data-cy= "`${convertToKebabCase(flattenedJsonSchema[path].title)}-datepicker`"
                />

                <input
                    v-if="controlType(path) === 'text'"
                    :id="path"
                    v-model="parameters[path]"
                    :name="path"
                    :placeholder="flattenedJsonSchema[path].description"
                    :type="inputType(flattenedJsonSchema[path])"
                    :data-cy= "`${convertToKebabCase(flattenedJsonSchema[path].title)}-input`"
                />
            </div>
        </template>
    </div>
</template>

export const reportRoutes = [
    {
        path: 'my-reports',
        name: 'report-dashboard-my-reports',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/MyReports.vue'),
    },
    {
        path: 'SSRS_WORK_UNIT',
        name: 'report-SSRS_WORK_UNIT',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/WorkUnitCriteria.vue')
    },
    {
        path: 'CONSENTS_LISTING',
        name: 'report-CONSENTS_LISTING',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/BuildingConsentsListingCriteria.vue')
    },
    {
        path: 'OBJECTIONS_LISTING',
        name: 'report-OBJECTIONS_LISTING',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/ObjectionsListingCriteria.vue')
    },
    {
        path: 'SUBDIVISIONS_LISTING',
        name: 'report-SUBDIVISIONS_LISTING',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/SubdivisionsListingCriteria.vue')
    },
    {
        path: 'EXCEPTION_LIST',
        name: 'report-EXCEPTION_LIST',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/ExceptionListingCriteria.vue')
    },
    {
        path: 'REVISIONS_REPORT_2_INCLUDES_EXCEPTION_LIST',
        name: 'report-REVISIONS_REPORT_2_INCLUDES_EXCEPTION_LIST',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/ExceptionListingCriteria.vue')
    },
    {
        path: 'OVG_STATISTICS',
        name: 'report-OVG_STATISTICS',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/OvgStatisticsCriteria.vue')
    },
    {
        path: 'COMMERCIAL_WORKSHEET_REPORT',
        name: 'report-COMMERCIAL_WORKSHEET_REPORT',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/CommercialWorksheetCriteria.vue')
    },
    {
        path: 'CUTDOWN_OVG_ROLL_EXTRACTS',
        name: 'report-CUTDOWN_OVG_ROLL_EXTRACTS',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/CutdownOvgRollExtractsCriteria.vue')
    },
    {
        path: 'CUTDOWN_OVG_SALES_EXTRACTS',
        name: 'report-CUTDOWN_OVG_SALES_EXTRACTS',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/CutdownOvgSalesExtractsCriteria.vue')
    },
    {
        path: 'COST_CENTRE_REVENUE_SUMMARY',
        name: 'report-COST_CENTRE_REVENUE_SUMMARY',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/CostCentreRevenueCriteria.vue')
    },
    {
        path: 'TA_RATING_SUMMARY',
        name: 'report-TA_RATING_SUMMARY',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/TaRatingCriteria.vue')
    },
    {
        path: 'QV_ANALYTICS_REVALUATION_UPDATE_PROPERTY_VALUES_REVISION_RURAL_WORKSHEET',
        name: 'report-QV_ANALYTICS_REVALUATION_UPDATE_PROPERTY_VALUES_REVISION_RURAL_WORKSHEET',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/RevisionRuralWorksheet.vue')
    },
    {
        path: 'RECONCILIATION_STATEMENT',
        name: 'report-RECONCILIATION_STATEMENT',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/ReconciliationStatementCriteria.vue')
    },
    {
        path: 'RURAL_WORKSHEET_REPORT',
        name: 'report-RURAL_WORKSHEET',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/RuralWorksheetCriteria.vue')
    },
    {
        path: 'ASSESSMENTS_NOT_INDEXED',
        name: 'report-ASSESSMENTS_NOT_INDEXED',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/AssessmentsNotIndexedCriteria.vue')
    },
    {
        path: 'SUMMARY_VALUES_BY_CATEGORY',
        name: 'report-SUMMARY_VALUES_BY_CATEGORY',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/SummaryValuesByCategoryCriteria.vue')
    },
    {
        path: 'ROLL_LISTING',
        name: 'report-ROLL_LISTING',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/RollListingCriteria.vue')
    },
    {
        path: 'SUMMARY_OF_VALUES',
        name: 'report-SUMMARY_OF_VALUES',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/SummaryOfValuesCriteria.vue')
    },
    {
        path: 'SALES_LISTING',
        name: 'report-SALES_LISTING',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/SalesListingCriteria.vue')
    },
    {
        path: 'SUMMARY_CONSENTS_TA_OR_VALUER',
        name: 'report-SUMMARY_CONSENTS_TA_OR_VALUER',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/SummaryOfConsentsForTLACriteria.vue')
    },
    {
        path: 'SALES_ANALYSIS',
        name: 'report-SALES_ANALYSIS',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/SalesAnalysisCriteria.vue')
    },
    {
        path: 'SALES_EXTRACT',
        name: 'report-SALES_EXTRACT',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/SalesExtractCriteria.vue')

    },
    {
        path: 'OVG_COST_RECOVERY_STATISTICS',
        name: 'report-OVG_COST_RECOVERY_STATISTICS',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/criteria/OVGCostRecoveryStatisticsCriteria.vue')

    },
    {
        path: ':reportName',
        component: () => import(/* webpackChunkName: "ReportDashboard" */ '@/components/reports/ReportForm.vue'),
    }
]

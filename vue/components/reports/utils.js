import { computed, ref } from 'vue';
import { store } from '../../DataStore';
import moment from 'moment';
import { DateTime } from 'luxon';

const isInternalUser = computed(() => store.state.userData.isInternalUser);
const isReadOnlyUser = computed(() => store.state.userData.isReadOnlyUser);
export const ntUsername = computed(() => `QVNZ\\${store.state.userData.userName?.trim()}`);

export const NONE = { id: -1, code: '-1', description: '- None -' }


const reportDefinitions = [
    {
        reportType: 'MONARCH_BUILDING_CONSENT_EXPORT',
        exportLimit: 100000,
        includeUserPermissions: false,
    },
    {
        reportType: 'MONARCH_OBJECTION_EXPORT',
        exportLimit: 50000,
        includeUserPermissions: false,
    },
    {
        reportType: 'MONARCH_HOME_VALUATION_EXPORT',
        exportLimit: 50000,
        includeUserPermissions: false,
    },
    {
        reportType: 'MONARCH_PROPERTY_EXPORT',
        exportLimit: 200000,
        includeUserPermissions: true,
    },
    {
        reportType: 'MONARCH_SALE_EXPORT',
        exportLimit: 50000,
        includeUserPermissions: true,
    },
];
export const RURAL_WORKSHEET_UPLOAD = 'QV_ANALYTICS_REVALUATION_UPDATE_PROPERTY_VALUES_REVISION_RURAL_WORKSHEET_UPLOAD';

export const reports = ref(null);
export const reportsIncludingHidden = ref(null);
export const currentReport = ref(null);
export const notifications = ref([]);

export async function getReports(includeHidden) {
    try {
        const { url } = jsRoutes.controllers.ReportGeneratorController.getReports(includeHidden);
        const res = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        const result = await res.json();
        return result.message;
    }
    catch (error) {
        const message = 'Error calling report generator API to get reports';
        console.error(message, error);
        return [];
    }
}

export function findReportByName(reports, name) {
    let report = null;

    for (const topLevelId in reports) {
        // Top level report, no category
        if (name === reports[topLevelId]?.name) {
            report = reports[topLevelId];
            report['id'] = topLevelId;
            return report;
        }

        if (reports[topLevelId].reports) {
            report = findReportByName(reports[topLevelId].reports, name);
            if (report) {
                return report;
            }
        }

        if (reports[topLevelId].categories) {
            report = findReportByName(reports[topLevelId].categories, name);
            if (report) {
                return report;
            }
        }
    }
}

export async function getReportJobsPagination(pageSize, pageNumber, orderBy, orderDirection) {
    try {
        const { url } = jsRoutes.controllers.ReportGeneratorController.getReportJobsPagination(pageSize, pageNumber, orderBy, orderDirection);
        const res = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });
        const result = await res.json();
        return {
            reportJobs: result.message.reportJobs,
            total: result.message.total,
        };
    }
    catch (error) {
        const message = 'Error calling report generator API to get report jobs';
        console.error(message, error);
        errorMessage.value = message;
    }
}

export async function createReportJob(reportRequest, isExternal = false) {
    try {
        const { url } = jsRoutes.controllers.ReportGeneratorController.createReportJob(isExternal);
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(reportRequest),
        });
        return await res.json();
    }
    catch (error) {
        const message = 'Error calling report generator API to create a report job';
        console.error(message, error);
    }
}

export async function updateReportJobStatus(reportJobId, status) {
    try {
        const { url } = jsRoutes.controllers.ReportGeneratorController.updateReportJobStatus(reportJobId, status);
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify({}),
        });
        return await res.json();
    }
    catch (error) {
        const message = 'Error calling report generator API to update a report job status';
        console.error(message, error);
    }
}

export function getReportJobFileUrl(reportJob) {
    const { url } = jsRoutes.controllers.ReportGeneratorController.getReportJobFile(reportJob.download_url, reportJob.report_file_name);
    return url;
}

export async function createExternalReportJob(reportRequest) {
    try {
        const { url } = jsRoutes.controllers.ReportGeneratorController.createExternalReportJob();
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(reportRequest),
        });
        return await res.json();
    }
    catch (error) {
        const message = 'Error calling report generator API to create a report job';
        console.error(message, error);
    }
}

export async function createInternalReportJob(parameters, optionalParameters = {}) {
    const reportId = optionalParameters.reportId || currentReport.value.id;
    const reportName = optionalParameters.reportName || currentReport.value.name;
    const reportRequest = {
        reportId,
        reportName,
        parameters,
    };

    try {
        const { url } = jsRoutes.controllers.ReportGeneratorController.createReportJob();
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(reportRequest),
        });
        return await res.json();
    } catch (error) {
        const message = 'Error calling report generator API to create a report job';
        console.error(message, error);
    }
}

export function clearNotifications(clearStickyNotifications = false) {
    if (clearStickyNotifications) {
        notifications.value = [];
        return;
    }
    notifications.value = notifications.value.filter(x => x.sticky);
}

export function validateReportField(value, validations, required) {
    const errors = [];

    if (required && (value === null || value === undefined || value === '' || value.length === 0)) {
        errors.push(`${validations.title} is required`);
        return errors;
    }

    for (const prop in validations) {
        if (prop === 'format' && validations[prop] === 'date') {
            if (!moment(value).isValid()) {
                errors.push(`Invalid date`);
            }
        }
        if (prop === 'enum') {
            if (!validations.enum.includes(value)) {
                errors.push(`Value must be one of ${validations.enum.join(', ')}`);
            }
        }
        if (prop === 'type' && validations[prop] === 'array') {
            if (!value || value.constructor !== Array) {
                errors.push(`Value must be an array`);
            }
        }
        if (prop === 'type' && validations[prop] === 'string') {
            if (value.constructor !== String) {
                errors.push(`Value must be a string`);
            }
        }
        if (prop === 'type' && validations[prop] === 'boolean') {
            if (value.constructor !== Boolean) {
                errors.push(`Value must be a boolean`);
            }
        }
    }

    return errors;
}

export async function submitMonarchExport(exportType, searchCriteria, totalResults) {
    if (!searchCriteria) {
        console.error('Search criteria not provided.');
        return;
    }
    const reportSettings = reportDefinitions.find(x => x.reportType === exportType);
    if (!reportSettings) {
        console.info('Report settings not found.');
    }

    if (reportSettings && totalResults > reportSettings.exportLimit) {
        return {
            mode: 'error',
            isOpen: true,
            heading: 'Export limit exceeded',
            message: `A maximum of ${reportSettings.exportLimit} results may be exported.  Please refine your search criteria.`,
            messages: [],
            cancelText: null,
            cancelAction: () => {
            },
            confirmText: 'OK',
            confirmAction: () => {
            },
            code: 'EXPORT_LIMIT_ERROR',
        };
    }

    const includeHiddenReports = true;
    const reports = await getReports(includeHiddenReports);
    const report = findReportByName(reports, exportType);
    if (!report) {
        return {
            mode: 'error',
            isOpen: true,
            heading: 'Export failed',
            message: `Could not find report definition for your export.  Please contact support or try again later.`,
            messages: [],
            cancelText: null,
            cancelAction: () => {
            },
            confirmText: 'OK',
            confirmAction: () => {
            },
            code: 'EXPORT_NOTFOUND_ERROR',
        };
    }

    const reportRequest = {
        reportId: report.id,
        parameters: {
            body: searchCriteria,
            total: totalResults,
        },
    };

    if (reportSettings && reportSettings.includeUserPermissions) {
        reportRequest.parameters.isInternalUser = isInternalUser.value;
        reportRequest.parameters.isReadOnlyUser = isReadOnlyUser.value;
    }

    try {
        const result = await createReportJob(reportRequest);

        if (result?.status !== 'CREATED') {
            return {
                mode: 'error',
                isOpen: true,
                heading: 'Scheduling failed',
                message: 'An error occurred while attempting to schedule your export.  Please contact support or try again later.',
                messages: [],
                cancelText: 'View My Reports',
                cancelAction: () => {
                },
                confirmText: 'OK',
                confirmAction: () => {
                },
                code: 'EXPORT_FAILED_MESSAGE',
            };
        }

        return {
            mode: 'message',
            isOpen: true,
            heading: 'Export Scheduled',
            message: 'Your export has been acknowledged and can be viewed in View My Reports.',
            messages: [],
            cancelText: 'View My Reports',
            cancelAction: () => {
            },
            confirmText: 'OK',
            confirmAction: () => {
            },
            code: 'EXPORT_SCHEDULED_MESSAGE',
        };
    }
    catch (error) {
        const message = 'Error calling report generator API to get reports';
        console.error(message, error);
        return {
            mode: 'error',
            isOpen: true,
            heading: 'An error occurred',
            message: message,
            messages: [],
            cancelText: null,
            cancelAction: () => {
            },
            confirmText: 'OK',
            confirmAction: () => {
            },
            code: 'EXPORT_FAILED_ERROR',
        };
    }
}

export async function scheduleSurveyReportForQpid(qpid, surveyTypeCode) {
    const reportName = 'REVALUATION_SURVEY_RESULTS';
    const reports = await getReports(true);
    const reportId = findReportByName(reports, reportName).id;
    const surveyType = getSurveyTypeFromCode(surveyTypeCode);
    if (!surveyType) {
        console.error('Could not determine survey type from category', surveyTypeCode);
        return;
    }
    const parameters = {
        qpid,
        surveyType,
        dateFrom: '1900-01-01',
    };
    const optionalParameters = {
        reportId,
        reportName,
    };
    return createInternalReportJob(parameters, optionalParameters);
}

export async function createWorksheetUploadJob(s3Url) {
    const reportId = findReportByName(reportsIncludingHidden.value, RURAL_WORKSHEET_UPLOAD).id;
    const parameters = {
        s3Url,
    };
    const optionalParameters = {
        reportId,
        reportName: RURAL_WORKSHEET_UPLOAD,
    };
    return createInternalReportJob(parameters, optionalParameters);
}

function getSurveyTypeFromCode(surveyTypeCode) {
    switch (surveyTypeCode) {
        case 'F':
            return 'Farm';
        case 'FO':
            return 'Forestry';
        case 'H':
            return 'Horticulture';
        case 'CA':
            return 'Commercial Accommodation';
        case 'BP':
            return 'Business Property';
        default:
            return null;
    }
}

export async function createAndUploadExternalReportJob(reportJobPayload, externalReportFile) {
    try {
        const formData = new FormData();
        for (const key in reportJobPayload) {
            formData.append(key, reportJobPayload[key]);
        }
        formData.append('file', externalReportFile);

        const { url } = jsRoutes.controllers.ReportGeneratorController.createAndUploadExternalReportJobAuth();
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: formData,
        });
        if (response.status !== 200) {
            console.error(`Server responded with ${response.status} when submitting external report job.`);
            return false;
        }
        return response.json();
    }
    catch (error) {
        console.error(error);
        return false;
    }
}

export function formatDateToMMDDYYYY(dateString) {
    return DateTime.fromISO(dateString).toFormat('MM/dd/yyyy');
}

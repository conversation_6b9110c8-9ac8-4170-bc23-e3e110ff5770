<script setup>
import { computed, defineEmits, defineProps, ref } from 'vue';
import { useTaList } from '../../composables/taList';
import Multiselect from "vue-multiselect";
import { NONE } from './utils';

const props = defineProps({
    value: {
        type: Object,
        default: () => NONE
    },
    allowEmpty: {
        type: Boolean,
        default: true
    }
});

const taStore = useTaList();
const taList = computed(() => props.allowEmpty? [NONE].concat(taStore.value) : taStore.value);


const emit = defineEmits(['input']);

const selectedValue = computed({
    get: () => {
        return props.value
    },
    set: (value) => {
        emit('input', value);
    }
});

function defaultTAToNone() {
    selectedValue.value = taList.value[0];
}
</script>

<template>
    <div>
        <multiselect v-model="selectedValue" :options="taList || []" :multiple="false"
            :close-on-select="true" track-by="id" label="description" :preselect-first="true"
            placeholder="Select Territorial Authorities" select-label="⏎ select" deselect-label="⏎ remove"
            data-cy="ta-selector" @remove="defaultTAToNone"/>
    </div>
</template>
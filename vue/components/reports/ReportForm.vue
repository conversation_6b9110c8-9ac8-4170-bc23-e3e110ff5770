<script setup>

import { onMounted, ref, watch } from 'vue';
import { createInternalReportJob, currentReport, notifications } from './utils';
import { useRouter } from 'vue-router/composables';
import JsonSchemaForm from './JsonSchemaForm.vue';
import AlertModal from '../common/modal/AlertModal.vue';
import { useTaList, taCodeToId } from '../../composables/taList';
import { useWorkUnitValuerList } from '../../composables/workUnitValuerList';
import moment from 'moment';
import { DateTime } from 'luxon';
import useValuerInfo from '@/composables/useValuerInfo';

const emit = defineEmits(['overwriteNotification', 'overwriteModal']);
defineExpose({
    setModal,
    setSaving,
});

const { valuers: monarchValuers } = useValuerInfo();
const router = useRouter();
const taList = useTaList();
const valuerList = useWorkUnitValuerList();

const props = defineProps({
    isOverwriteScenario: {
        type: Boolean,
        default: false,
    },
    alterSchemaProperties: {
        type: Function,
        default: () => {},
    },
});

const isOverwriteScenario = ref(props.isOverwriteScenario);

const readyToRender = ref(false);
const jsonSchema = ref({});
const updatedJsonSchema = ref({});
const jsonSchemaFormReload = ref(0);
const reportCriteria = ref(null);

const saving = ref(false);
const modal = ref({
    mode: 'warning',
    isOpen: false,
    heading: 'heading',
    message: '',
    messages: [],
    cancelText: 'No',
    cancelAction: () => {
    },
    confirmText: 'Yes',
    confirmAction: () => {
    },
    code: '',
});

watch(currentReport, (newValue) => {
    if (taList.value.length > 0) {
        renderReport(newValue);
    }
});

// eslint-disable-next-line no-unused-vars
watch(taList, (_newValue) => {
    if (currentReport.value !== null && currentReport.value !== undefined) {
        renderReport(currentReport.value);
    }
});

onMounted(() => {
    if (currentReport.value !== null && currentReport.value !== undefined && taList.value.length > 0) {
        renderReport(currentReport.value);
    }
});

function setModal(inputModal) {
    modal.value = inputModal;
}

function modalCancel() {
    modal.value.isOpen = false;
    modal.value.cancelAction();
}

function modalConfirm() {
    modal.value.isOpen = false;
    modal.value.confirmAction();
}

function setNotifications(message) {
    notifications.value = [{
        type: 'info',
        message: message.replace(/\\n/g, '\n'),
        sticky: false,
    }];
}

function deepCopyAndUpdate(object, updateMap) {
    if (typeof object !== 'object' || object === null) {
        return object;
    }

    const newObject = Array.isArray(object) ? [] : {};

    for (const key in object) {
        if (Object.hasOwn(updateMap, key)) {
            newObject[key] = updateMap[key](object[key]);
        }
        else {
            newObject[key] = deepCopyAndUpdate(object[key], updateMap);
        }
    }

    return newObject;
}

function validateRollMaintenanceComplianceQA(submittedParameters) {
    const fromDateActioned = submittedParameters.fromDateActioned;
    const ratingAuthorityQA = submittedParameters.ratingAuthority;
    const valuerQA = submittedParameters.valuerMonarch;
    const now = DateTime.now().startOf('day');
    if (!fromDateActioned) {
        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Scheduling Failed',
            message: 'Please enter a From Date',
            messages: [],
            cancelText: null,
            cancelAction: () => {
                saving.value = false;
                router.push({ name: 'report-dashboard-my-reports' });
            },
            confirmText: 'OK',
            confirmAction: () => {
                saving.value = false;
            },
            code: 'INVALID_REPORT_PARAMETERS',
        });
        return true;
    }
    if (DateTime.fromISO(fromDateActioned) > now) {
        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Scheduling Failed',
            message: 'From date cannot be in the future',
            messages: [],
            cancelText: null,
            cancelAction: () => {
                saving.value = false;
                router.push({ name: 'report-dashboard-my-reports' });
            },
            confirmText: 'OK',
            confirmAction: () => {
                saving.value = false;
            },
            code: 'INVALID_REPORT_PARAMETERS',
        });
        return true;
    }
    if (!ratingAuthorityQA && !valuerQA) {
        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Scheduling Failed',
            message: 'Either Rating Authority or Valuer must be selected',
            messages: [],
            cancelText: null,
            cancelAction: () => {
                saving.value = false;
                router.push({ name: 'report-dashboard-my-reports' });
            },
            confirmText: 'OK',
            confirmAction: () => {
                saving.value = false;
            },
            code: 'INVALID_REPORT_PARAMETERS',
        });
        return true;
    }
    return false;
}

function renderReport(report) {
    // Show report description in notification area
    setNotifications(report.description);
    emit('overwriteNotification');

    // Render report form
    jsonSchema.value = JSON.parse(report.parameters);

    if (isOverwriteScenario.value) {
        jsonSchema.value = props.alterSchemaProperties(jsonSchema.value);
    }

    const updateMap = {
        // Add rating authority list
        ratingAuthority: value => ({
            ...value,
            type: 'string',
            enum: taList.value.map(ta => ta.description),
        }),
        ratingAuthorityId: value => ({
            ...value,
            type: 'string',
            enum: taList.value.map(ta => ta.description),
        }),
        ta: value => ({
            ...value,
            items: {
                type: 'string',
                enum: taList.value.map(ta => ta.description),
            },
        }),
        taCode: value => ({
            ...value,
            items: {
                type: 'string',
                enum: taList.value.map(ta => ta.description),
            },
        }),

        valuer: value => ({
            ...value,
            type: 'string',
            enum: valuerList.value.map(valuer => `${valuer.id} - ${valuer.description.split(' - ')[1]}`),
        }),
        Id: value => ({
            ...value,
            type: 'string',
            enum: taList.value.map(ta => ta.description),

        }),
        valuerMonarch: value => ({
            ...value,
            type: 'string',
            enum: monarchValuers.value.map(va => va.name),
        }),
        id: value => ({
            ...value,
            type: 'string',
            enum: taList.value.map(ta => ta.description),

        }),
        // Calculating default values based on the hints
        default: (value) => {
            // Check if value starts with 'hint:'
            if (typeof value !== 'string' || !value.startsWith('hint:')) {
                return value;
            }

            // Get hint
            const hint = value.substring(5);
            switch (hint) {
                case 'today':
                    return moment().format('YYYY-MM-DD');
                case 'firstDayOfLastMonth':
                    return moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
                case 'firstDayOfCurrentMonth':
                    return moment().startOf('month').format('YYYY-MM-DD');
                case 'firstDayOfCurrentYear':
                    return moment().startOf('year').format('YYYY-MM-DD');
                default:
                    return value;
            }
        },
    };

    updatedJsonSchema.value = deepCopyAndUpdate(jsonSchema.value, updateMap);
    if (!readyToRender.value) {
        readyToRender.value = true;
    }
    else {
        jsonSchemaFormReload.value += 1;
    }
}

async function handleSubmit(message) {
    saving.value = true;
    const { status, data } = message;

    if (status === 'error') {
        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Invalid Report Parameters',
            message: data,
            messages: [],
            cancelText: 'View My Reports',
            cancelAction: () => {
                saving.value = false;
                router.push({ name: 'report-dashboard-my-reports' });
            },
            confirmText: 'OK',
            confirmAction: () => {
                saving.value = false;
            },
            code: 'INVALID_REPORT_PARAMETERS',
        });
        return;
    }

    const reportParameters = deepCopyAndUpdate(data, {
        ratingAuthority: value => parseInt(value?.split(' ')[0]),
        ta: value => (value.map(ta => taCodeToId(parseInt(ta.split(' ')[0]), taList.value)).join(',')),
        taCode: value => value.map(ta => parseInt(ta.split(' ')[0])),
        valuer: value => parseInt(value.split(' ')[0]),
        ratingAuthorityId: (value) => {
            const selectedTa = taList.value.find(ta => ta?.description === value);
            return selectedTa ? selectedTa.id : -1;
        },
        Id: (value) => {
            const selectedTa = taList.value.find(ta => ta?.description === value);
            return selectedTa ? selectedTa.id : -1;
        },
        id: (value) => {
            const selectedTa = taList.value.find(ta => ta?.description === value);
            return selectedTa ? selectedTa.id : -1;
        },
        valuerMonarch: (value) => {
            const selectedValuer = monarchValuers.value.find(va => va?.name === value);
            return selectedValuer ? selectedValuer.ntUsername : null;
        },
    });

    if (reportParameters.extractName === 'RollMaintenanceComplianceQA') {
        if (validateRollMaintenanceComplianceQA(reportParameters)) {
            return;
        }
    }
    const result = await createInternalReportJob(reportParameters);
    if (result.status !== 'CREATED') {
        if (isOverwriteScenario.value) {
            emit('overwriteModal', result);
            return;
        }

        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Scheduling failed',
            message: result.message,
            messages: [],
            cancelText: null,
            cancelAction: () => {
            },
            confirmText: 'OK',
            confirmAction: () => {
                saving.value = false;
            },
            code: 'REPORT_SCHEDULING_FAILED',
        });
        return;
    }

    setModal({
        mode: 'message',
        isOpen: true,
        heading: 'Report Scheduled',
        message: 'Your report has been scheduled and can be viewed in View My Reports.',
        messages: [],
        cancelText: 'View My Reports',
        cancelAction: () => {
            saving.value = false;
            router.push({ name: 'report-dashboard-my-reports' });
        },
        confirmText: 'OK',
        confirmAction: () => {
            saving.value = false;
        },
        code: 'REPORT_SCHEDULED_MESSAGE',
    });
}

async function submitForm() {
    reportCriteria.value.submit();
}

function clearForm() {
    reportCriteria.value.clear();
}

function setSaving(value) {
    saving.value = value;
}

</script>

<template>
    <div
        v-if="readyToRender"
        class="qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving || modal.isOpen"
            class="page-mask"
            data-cy="report-page-mask"
        />
        <alert-modal
            v-if="modal.isOpen"
            :caution="modal.mode==='warning'"
            :success="modal.mode==='success'"
            :warning="modal.mode==='error'"
            data-cy="report-modal"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >
                {{ modal.message.trim() }}
            </p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }" class="message message-error">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        data-cy="report-modal-cancel"
                        @click="modalCancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        data-cy="report-modal-confirm"
                        @click="modalConfirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                :value="modal.code"
                type="hidden"
            >
        </alert-modal>

        <JsonSchemaForm
            ref="reportCriteria"
            :key="jsonSchemaFormReload"
            :json-schema="updatedJsonSchema"
            @submit="handleSubmit"
        />
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                class="mdl-button mdl-button--raised"
                :disabled="saving"
                data-cy="clear-button"
                @click="clearForm"
            >
                CLEAR
            </button>
            <button
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving"
                data-cy="schedule-report-button"
                @click="submitForm"
            >
                SCHEDULE REPORT
            </button>
        </div>
    </div>
</template>

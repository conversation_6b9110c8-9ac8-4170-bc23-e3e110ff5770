<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import { useWorkUnitValuerList } from '../../../composables/workUnitValuerList';
import {
    currentReport,
    notifications,
    clearNotifications
} from '../utils';
import Multiselect from "vue-multiselect";
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, isPositiveInteger, allowOnlyNumberKeys, validateTerritorialAuthority, validateValuer, validateYear } from './shared'
import { NONE } from '../utils';

const saving = ref(false);
const router = useRouter();

// -- List Sales By:
const territorialAuthority = ref(NONE);
const valuer = ref('');
const year = ref('');

const validationErrors = ref({
    taError: null,
    valuerError: null,
    yearError: null
});

const taStore = useTaList();
const valuerStore = useWorkUnitValuerList();
const taList = computed(() => [{ id: -1, code: '-1', description: '- None -' }].concat(taStore.value));
const valuerList = computed(() => [{ id: -1, code: '-1', description: '- None -' }].concat(valuerStore.value));
const reportId = computed(() => currentReport.value?.id);

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: `Generate a Summary of Consents for a TA or Valuer, for a specific Year.`,
        sticky: false,
    });
});

function getFilterValues() {
    const filters = {
        format: 'EXCEL',
        territorialAuthority: territorialAuthority.value,
        valuer: valuer.value,
        year: year.value
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
        
    territorialAuthority.value = taList.value[0];
    valuer.value = valuerList.value[0];
    year.value = '';

    saving.value = false;
}

function clearFilterValidations() {
    validationErrors.value = {
        taError: null,
        valuerError: null,
        yearError: null
    };
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilterValues();   

    clearFilterValidations();

    if (! validateFilters(filters)) {
        saving.value = false;
        return;
    }
   
    const isTA = filters.territorialAuthority.id !== -1;
    const type = isTA? 'A' : 'V';
    const id = isTA? filters.territorialAuthority.id : filters.valuer.id;

    const parameters = {
        format: filters.format,
        Type: type,
        Id: id,
        Year: filters.year
    };
    
    let reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function validateFilters(filters) {
    let valid = true;

    const validTerritorialAuthority = validateTerritorialAuthority(filters.territorialAuthority);
    const validValuer = validateValuer(filters.valuer);
    const validYear = validateYear(filters.year);

    if(!validTerritorialAuthority.isValid && !validValuer.isValid) {
        valid = false;
        validationErrors.value.taError = validTerritorialAuthority.message;
        validationErrors.value.valuerError = validValuer.message;
    }

    if(!validYear.isValid) {
        valid = false;
        validationErrors.value.yearError = validYear.message;
    }

    return valid;    
}

function filterYearFormat(event) {
    let input = event.target.value.replace(/\D/g, '');

    if (input.length > 4) {
        input = input.slice(0, 4);
    }

    year.value = input;
}

function handleKeyPress(event) {
    allowOnlyNumberKeys(event);
}

function defaultValuerToNone() {
    valuer.value = valuerList.value[0];
}

</script>
<template>
    <div class="report-criteria qv-flex-column" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask">
        </div>

        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <div class="qv-flex-column" style="justify-content: space-evenly;">
                    <div class="qv-flex-row">
                        <span style="flex: 1;">
                            Territorial Authorities
                        </span>
                        <div style="flex: 3;">
                            <tooltip display-mode="error" :text="validationErrors.taError" :border="true">
                                <ta-drop-down-select v-model="territorialAuthority"/>
                            </tooltip>
                        </div>
                    </div>
                    <div class="qv-flex-row">
                        <span style="flex: 1;">
                            Valuer
                        </span>
                        <div style="flex: 3;">
                            <tooltip display-mode="error" :text="validationErrors.valuerError" :border="true">
                                <multiselect 
                                v-model="valuer" 
                                :options="valuerList || []" 
                                :multiple="false"
                                :close-on-select="true" 
                                track-by="id" 
                                label="description" 
                                :preselect-first="true"
                                placeholder="Select Territorial Authorities" 
                                select-label="⏎ select"
                                deselect-label="⏎ remove"
                                @remove="defaultValuerToNone"
                                data-cy="valuer-selector"
                                />
                            </tooltip>
                        </div>
                    </div>
                    <div class="qv-flex-row">
                        <span style="flex: 1">
                            Enter Year to Report
                        </span>
                        <div style="flex: 3">
                            <tooltip display-mode="error" :text="validationErrors.yearError" :border="true">
                                <input 
                                type="text" 
                                class="text-input" 
                                v-model="year"
                                pattern="\\d{4}" 
                                placeholder="Enter year - YYYY"                                
                                @input="filterYearFormat" 
                                @keypress="handleKeyPress" 
                                data-cy="year-of-report-input"
                                />
                            </tooltip>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button @click="clearFilters" class="mdl-button mdl-button--raised" data-cy="clear-report-button">
                Clear
            </button>
            <button @click="createReportRequest" class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving" data-cy="schedule-report-button">
                Schedule Report
            </button>
        </div>
    </div>

</template>

<style lang="scss" src='../../reports/reports.scss' />
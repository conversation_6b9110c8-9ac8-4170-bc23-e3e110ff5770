<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import { useObjectionStatus } from '../../../composables/objectionStatusTypes';
import {
    currentReport,
    notifications,
    clearNotifications,
    createReportJob,
    formatDateToMMDDYYYY
} from '../utils';
import DatePicker from "vue2-datepicker";
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";
import Tooltip from '@/components/common/Tooltip.vue';
import { DateTime } from 'luxon';
import ConfirmationModal from '@/components/common/modal/ConfirmationModal.vue';
import useModal from '@/composables/useModal';

const modal = useModal(); 
const saving = ref(false);
const router = useRouter();

const taValues = ref([]);
const revisionDate = ref('');
const objectionType = ref({ value: 0, label: 'All' });
const statusValues = ref([]);

const reportId = computed(() => currentReport.value?.id);
const parameters = computed(() => {
  const params = currentReport.value?.parameters ? JSON.parse(currentReport.value.parameters) : {};
  return params.properties || {};
});

const validationErrors = ref({
    objectionType: null,
    revisionDate: null,
    taValues: null,
    statusValues: null
});

const taStore = useTaList();
const taList = computed(() => [{ id: 0, code: '0', description: 'All Territorial Authorities' }].concat(taStore.value));

const objectionStatusStore = useObjectionStatus();
const objectionStatusList = computed(() => [{ id: 0, code: '0', description: 'All' }].concat(objectionStatusStore.value));

const objectionTypes = [
  { value: 0, label: 'All' },
  { value: 1, label: 'Revision' },
  { value: 2, label: 'Maintenance' }
];

onMounted(async () => {
    clearNotifications();
    objectionType.value = { value: 0, label: 'All' };
    revisionDate.value = '';
    updateSelectedTAs();
    updateSelectedStatus();
    notifications.value.push({
        type: 'info',
        message: `Generate a Objections Listing Report for TAs, for a specific period.\n\nImportant Note: This report will schedule a large file size/requests, any other export requests made by users will be affected and held up this large request.\n\nWe recommend this report be requested Afterhours or towards end of day, to prevent causing issues for other users`,
        sticky: false,
    });
});

async function submitReportRequest() {
    const reportRequest = createReportRequest();

    if (!reportRequest) {
        return;
    }

    saving.value = true;
    const result = await createReportJob(reportRequest);

    if (result.status !== 'CREATED'){
        await modal.showError(
            'Scheduling failed',
            'An error occurred while attempting to schedule your report. Please contact support or try again later.'
        );
        saving.value = false;
        return;
    }

    try {
        const confirmed = await modal.show(
            ConfirmationModal,
            {
                title: 'Report Scheduled',
                message: 'Your report has been acknowledged and can be viewed in View My Reports.',
                confirmText: 'OK',
                cancelText: 'View My Reports',
                showCancelButton: true,
                showConfirmButton: true
            }
        );
        if (confirmed) {
            saving.value = false;
        } else {
            saving.value = false;
            await router.push({ name: 'report-dashboard-my-reports' });
        }
    } catch (error) {
        console.error('Modal error:', error);
        saving.value = false;
    }
}

function getFilters() {
    const taVals = getTAValues();
    const statusVals = getStatusValues();
    const objectionStatusVal = getObjectionsValue();
    const filters = {
        objectionType: objectionStatusVal,
        revisionDate: revisionDate.value ?? '',
        taValues: taVals,
        statusValues: statusVals
    };
    return filters;
}

function validateFilters(filters) {
    let valid = true;
    for (const prop in parameters.value) {
        validationErrors.value[prop] = null;
        const errs = validateReportField(filters[prop], parameters.value[prop]);
        if (errs.length > 0) {
            validationErrors.value[prop] = errs.join('\n')
            valid = false;
        }
    }
    return valid;
}

function clearFilters() {
    saving.value = true;
    objectionType.value = { value: 0, label: 'All' };
    revisionDate.value = '';
    taValues.value = [],
    statusValues.value = [],
    updateSelectedStatus();
    updateSelectedTAs();
    saving.value = false;
}

function getTAValues() {
    let values = []
    values = taValues.value.map(a => a.code);
    if (taValues.value.length === 1 && taValues.value[0]?.id === 0) {
        values = taList.value.map(a => a.code);
    }
    return values;
}

function getStatusValues() {
    let values = []
    values = statusValues.value.map(a => a.code);
    if (statusValues.value.length === 1 && statusValues.value[0]?.id === 0) {
        values = objectionStatusList.value.map(a => a.code);
    }
    return values;
}

function getObjectionsValue() {
    return objectionType.value.value;
}

function createReportRequest() {
    const filters = getFilters();
    if (filters.statusValues && Array.isArray(filters.statusValues)) {
        filters.statusValues = filters.statusValues.join(',');
    }
    if (filters.taValues && Array.isArray(filters.taValues)) {
        filters.taValues = filters.taValues.join(',');
    }
    if (filters.revisionDate) {
        filters.revisionDate = formatDateToMMDDYYYY(filters.revisionDate);
    }
    if (!validateFilters(filters)) {
        return;
    }
    return {
        reportId: reportId.value,
        parameters: filters,
    }
}

function updateSelectedTAs() {
    if (taValues.value.length > 0 && taValues.value[taValues.value.length -1 ].id === 0) {
        taValues.value = [{ id: 0, code: '0', description: 'All Territorial Authorities' }];
    }
    if (taValues.value.length > 1) {
        taValues.value = taValues.value.filter(x => x.id !== 0);
    }
    if (taValues.value.length === 0) {
        taValues.value.push({ id: 0, code: '0', description: 'All Territorial Authorities' });
    }
}

function updateSelectedStatus() {
    if (statusValues.value.length > 0 && statusValues.value[statusValues.value.length -1 ].id === 0) {
        statusValues.value = [{ id: 0, code: '0', description: 'All' }];
    }
    if (statusValues.value.length > 1) {
        statusValues.value = statusValues.value.filter(x => x.id !== 0);
    }
    if (statusValues.value.length === 0) {
        statusValues.value.push({ id: 0, code: '0', description: 'All' });
    }
}

function validateReportField(value, validations) {
    const errors = [];
    for (const prop in validations) {
        if (prop === 'format' && validations[prop] === 'date' && value !== '') {
            const date = DateTime.fromISO(value);
            if (!date.isValid) {
                errors.push(`Invalid date`);
            }
        }
        if (prop === 'enum') {
            if (!validations.enum.includes(value)) {
                errors.push(`Value must be one of ${validations.enum.join(', ')}`);
            }
        }
        if (prop === 'type' && validations[prop] === 'array') {
            if (!value || value.constructor !== Array) {
                errors.push(`Value must be an array`);
            }
        }
        if (prop === 'type' && validations[prop] === 'string') {
            if (value.constructor !== String) {
                errors.push(`Value must be a string`);
            }
        }
        if (prop === 'type' && validations[prop] === 'boolean') {
            if (value.constructor !== Boolean) {
                errors.push(`Value must be a boolean`);
            }
        }
    }
    return errors;
}

function defaultObjectionType() {
    objectionType.value = { value: 0, label: 'All' };
}

</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving || modal.isOpen"
            class="page-mask"
            data-cy="report-page-mask"
        >
        </div>
        <h3>List Objections by</h3>
        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column">
                <h3>Objection Type:</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :border="true"
                    >
                        <multiselect
                            v-model="objectionType"
                            :options="objectionTypes"
                            :multiple="false"
                            :close-on-select="true"
                            track-by="value"
                            label="label"
                            :preselect-first="true"
                            placeholder="Select Objection Type"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            @remove="defaultObjectionType"
                        />
                    </tooltip>
                </div>
                <h3>Revision Date</h3>
                <div class="qv-flex-row">
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.revisionDate"
                            :border="true"
                        >
                            <date-picker
                                v-model="revisionDate"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                data-cy="revision-date"
                            />
                        </tooltip>
                    </span>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Territorial Authorities</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :border="true"
                    >
                        <multiselect
                            v-model="taValues"
                            :options="taList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Territorial Authorities"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            @input="updateSelectedTAs"
                        />
                    </tooltip>
                </div>
                <h3>Status</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :border="true"
                    >
                        <multiselect
                            v-model="statusValues"
                            :options="objectionStatusList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Status"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            @input="updateSelectedStatus"
                        />
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                @click="clearFilters"
                class="mdl-button mdl-button--raised"
            >
                Clear
            </button>
            <button
                @click="submitReportRequest"
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving"
                data-cy="schedule-report-button"
            >
                Schedule Report
            </button>
        </div>
    </div>
</template>
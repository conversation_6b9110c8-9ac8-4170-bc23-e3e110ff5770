<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
    NONE } from '../utils';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, isPositiveInteger } from './shared';

const saving = ref(false);
const router = useRouter();

const format = ref('PDF');

const rollType = ref('S');
const rollStart = ref(null);
const rollEnd = ref(null);
const rollSingle = ref(null);
const multipleRolls = reactive({
    multipleRolls1: null,
    multipleRolls2: null,
    multipleRolls3: null,
    multipleRolls4: null,
    multipleRolls5: null,
});
const territorialAuthority = ref(NONE);
const closingDate = ref('');
const regionId = ref('');
const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null,
    territorialAuthority: null,
    closingDate: null,
    regionId: null,
});

const taStore = useTaList();
const taList = computed(() => [{ id: -1, code: '-1', description: '- None -' }].concat(taStore.value));

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate a Reconciliation Statement.',
        sticky: false,
    });
});


function getFilterValues() {
    const filters = {
        territorialAuthority: territorialAuthority.value,
        rollSingle: rollSingle.value,
        rollType: rollType.value,
        rollStart: rollStart.value,
        rollEnd: rollEnd.value,
        multipleRolls1: multipleRolls.multipleRolls1,
        multipleRolls2: multipleRolls.multipleRolls2,
        multipleRolls3: multipleRolls.multipleRolls3,
        multipleRolls4: multipleRolls.multipleRolls4,
        multipleRolls5: multipleRolls.multipleRolls5,
        closingDate: closingDate.value,
        regionId: regionId.value,
        format: format.value,
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    clearFilterValidations();
    rollType.value = 'S';
    rollStart.value = null;
    rollEnd.value = null;
    rollSingle.value = null;
    multipleRolls.multipleRolls1 = null;
    multipleRolls.multipleRolls2 = null;
    multipleRolls.multipleRolls3 = null;
    multipleRolls.multipleRolls4 = null;
    multipleRolls.multipleRolls5 = null;
    territorialAuthority.value = taList.value[0];
    closingDate.value = '';
    regionId.value = '';
    saving.value = false;
    format.value = 'EXCEL';
}

function clearFilterValidations() {
    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null,
        closingDate: null,
        regionId: null,
        territorialAuthority: null,
    };
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilterValues();

    clearFilterValidations();

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    // Set values to -1 if they are not used.
    if (filters.rollType === 'S') {
        filters.rollStart = filters.rollSingle ? parseInt(filters.rollSingle) : -1;
        filters.rollEnd = -1;
    }
    else if (filters.rollType === 'M') {
        filters.rollStart = -1;
        filters.rollEnd = -1;
    }
    else {
        filters.rollStart = parseInt(filters.rollStart);
        filters.rollEnd = parseInt(filters.rollEnd);
    }

    // if regionId is set, set territorialAuthority to -1
    if (filters.regionId) {
        filters.territorialAuthority = -1;
        filters.regionId = parseInt(filters.regionId);
    }
    else { // if territorialAuthority is set, set regionId to -1
        filters.territorialAuthority = filters.territorialAuthority.id;
        filters.regionId = -1;
    }

    const parameters = {
        format: filters.format,
        RollType: filters.rollType,
        RollStart: filters.rollStart,
        RollEnd: filters.rollEnd,
        RollMultiple: [
            filters.multipleRolls1,
            filters.multipleRolls2,
            filters.multipleRolls3,
            filters.multipleRolls4,
            filters.multipleRolls5,
        ]
            .filter(roll => roll !== null)
            .join(','),
        RAId: filters.territorialAuthority,
        RegionId: filters.regionId,
        ClosingDate: filters.closingDate,
    };

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function toggleRollType(type) {
    rollType.value = type;
    rollSingle.value = null;
    rollEnd.value = null;
    rollStart.value = null;
    multipleRolls.multipleRolls1 = null;
    multipleRolls.multipleRolls2 = null;
    multipleRolls.multipleRolls3 = null;
    multipleRolls.multipleRolls4 = null;
    multipleRolls.multipleRolls5 = null;
}


function validateFilters(filters) {
    let valid = true;

    if (!filters.closingDate) {
        validationErrors.value.closingDate = 'Please specify a closing date.';
        valid = false;
    }

    if (filters.regionId && !isPositiveInteger(filters.regionId)) {
        validationErrors.value.regionId = 'Please specify a valid region ID.';
        valid = false;
    }

    return valid;
}

</script>

<template>
    <div class="report-criteria qv-flex-column" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask"/>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Closing Date</h3>
                <div class="qv-flex-row">
                    <tooltip display-mode="error" :text="validationErrors.closingDate" :border="true">
                        <date-picker
                            v-model="closingDate"
                            class="report-datepicker"
                            type="date" format="D/M/YYYY"
                            data-cy="closing-date"
                            value-type="YYYY-MM-DD" />
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('S')">
                    <input v-model="rollType" type="radio" name="roll-type" value="S" >
                    <span>
                        Single Roll
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('R')">
                    <input v-model="rollType" type="radio" name="roll-type" value="R" >
                    <span>
                        Range of Rolls
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('M')">
                    <input v-model="rollType" type="radio" name="roll-type" value="M" >
                    <span>
                        Multiple Rolls
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rollSingle" :border="true">
                        <input v-model.number="rollSingle" type="number" :disabled="rollType !== 'S'"
                               data-cy="single-roll-input" >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rangeOfRoll" :border="true">
                        <input v-model.number="rollStart" type="number" :disabled="rollType !== 'R'" >
                        <input v-model.number="rollEnd" type="number" :disabled="rollType !== 'R'" >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.multipleRolls" :border="true">
                        <input v-model.number="multipleRolls.multipleRolls1" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls2" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls3" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls4" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls5" type="number" :disabled="rollType !== 'M'" >
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.5;">
                <h3>Territorial Authority</h3>
                <div>
                    <tooltip display-mode="error" :text="validationErrors.territorialAuthority" :border="true" style="width: 100%;">
                        <ta-drop-down-select v-model="territorialAuthority"/>
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 0.5">
                <h3>Region ID</h3>
                <div class="qv-flex-row">
                    <tooltip display-mode="error" :text="validationErrors.regionId" :border="true" style="width: 100%;">
                        <input v-model="regionId" type="text" class="text-input" data-cy="regionId-input" >
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Report Format</h3>
                <div class="qv-flex-row report-option" @click="format = 'EXCEL'">
                    <input v-model="format" type="radio" name="format" value="EXCEL" >
                    <span>
                        Excel
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="format = 'PDF'">
                    <input v-model="format" type="radio" name="format" value="PDF" >
                    <span>
                        PDF
                    </span>
                </div>
            </div>
            <div class="qv-flex-row" style="justify-content:end;">
                <button class="mdl-button mdl-button--raised" @click="clearFilters">
                    Clear
                </button>
                <button class="mdl-button mdl-button--raised mdl-button--colored" :disabled="saving"
                        data-cy="schedule-report-button" @click="createReportRequest">
                    Schedule Report
                </button>
            </div>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router/composables';
import { DateTime } from 'luxon';
import { store } from '../../../DataStore';
import {
    currentReport,
    notifications,
    clearNotifications,
    createReportJob,
    validateReportField,
} from '../utils';
import DatePicker from "vue2-datepicker";
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";
import Tooltip from '@/components/common/Tooltip.vue';
import AlertModal from '@/components/common/modal/AlertModal.vue';
import { getDatePeriod } from './shared';

const loading = ref(false);
const saving = ref(false);
const router = useRouter();
const datePeriodDescription = ref('CurrentMonth');

const selectedCostCentres = ref([]);
const filters = ref({
    dateFrom: null,
    dateTo: null,
    format: 'EXCEL',
    type: 'Cost Centre',
    values : [],
});
const reportId = computed(() => currentReport.value?.id);
const parameters = computed(() => JSON.parse(currentReport.value?.parameters || '{ properties: null }').properties);
const validationErrors = ref({
    dateFrom: null,
    dateTo: null,
    format: null,
    type: null,
    values: null,
});

const costCentreListStore = computed(() => store.state.classifications?.classifications?.CostCentre.sort((a,b) => (a.description > b.description) ? 1 : ((b.description > a.description) ? -1 : 0)) || [])
const costCentreList = computed(() => [{ id: '0', code: '0', description: 'All Cost Centres' }].concat(costCentreListStore.value));

const modal = ref({
    mode: 'warning',
    isOpen: false,
    heading: 'heading',
    message: '',
    messages: [],
    cancelText: 'No',
    cancelAction: () => { },
    confirmText: 'Yes',
    confirmAction: () => { },
    code: '',
})

watch(datePeriodDescription, () => {
    setDatePeriod();
});

onMounted(async () => {
    clearNotifications();
    setDatePeriod();
    updateSelectedCostCentre();
    notifications.value.push({
        type: 'info',
        message: `Generate Revenue Summary for a Cost Centre for a specific period. Note that the data entered in Valor, is not included. After scheduling, the report can be accessed by clicking on “View My Reports” in the left menu.`,
        sticky: false,
    });
});

async function submitReportRequest() {
    const reportRequest = createReportRequest();
    if (!validateFilters()) {
        return;
    }
    saving.value = true;
    const result = await createReportJob(reportRequest);

    if (result.status !== 'CREATED'){
        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Scheduling failed',
            message: 'An error occurred while attempting to schedule your report.  Please contact support or try again later.',
            messages: [],
            cancelText: null,
            cancelAction: () => { },
            confirmText: 'OK',
            confirmAction: () => { saving.value = false; },
            code: 'REPORT_FAILED_MESSAGE',
        });
        return;
    }

    setModal({
        mode: 'message',
        isOpen: true,
        heading: 'Report Scheduled',
        message: 'Your report has been acknowledged and can be viewed in View My Reports.',
        messages: [],
        cancelText: 'View My Reports',
        cancelAction: () => { saving.value = false; router.push({ name: 'report-dashboard-my-reports' }); },
        confirmText: 'OK',
        confirmAction: () => { saving.value = false; },
        code: 'REPORT_SCHEDULED_MESSAGE',
    });
}

function validateFilters() {
    let valid = true;
    for (const prop in parameters.value) {
        validationErrors.value[prop] = null;
        const errs = validateReportField(filters.value[prop], parameters.value[prop], true);
        if (errs.length > 0) {
            validationErrors.value[prop] = errs.join('\n')
            valid = false;
        }
    }
    return valid;
}

function clearFilters() {
    saving.value = true;
    datePeriodDescription.value = 'CurrentMonth';
    filters.value = {
        dateFrom: null,
        dateTo: null,
        type: 'Cost Centre',
        values : [],
        format: 'EXCEL',
    }
    setDatePeriod();
    selectedCostCentres.value = [];
    updateSelectedCostCentre();
    saving.value = false;
}

function getFilterValues() {
    let values = []
    if (filters.value.type === 'Cost Centre') {
        values = selectedCostCentres.value.map(a => a.id);
        if (selectedCostCentres.value.length === 1 && selectedCostCentres.value[0]?.id === '0') {
            values = costCentreList.value.map(a => a.id);
        }
    }
    return values;
}

function createReportRequest() {
    filters.value.values = getFilterValues();

    return {
        reportId: reportId.value,
        parameters: filters.value,
    }
}

function updateSelectedCostCentre() {
    if (selectedCostCentres.value.length > 0 && selectedCostCentres.value[selectedCostCentres.value.length -1 ].id === '0') {
        selectedCostCentres.value = [{ id: '0', code: '0', description: 'All Cost Centres' }];
    }
    if (selectedCostCentres.value.length > 1) {
        selectedCostCentres.value = selectedCostCentres.value.filter(x => x.id !== '0');
    }
    if (selectedCostCentres.value.length === 0) {
        selectedCostCentres.value.push({ id: '0', code: '0', description: 'All Cost Centres' });
    }
}

function updateDates() {
    if (DateTime.fromISO(filters.value.dateFrom).isValid() && DateTime.fromISO(filters.value.dateTo).isValid() ) {
        if (DateTime.fromISO(filters.value.dateFrom).isAfter(DateTime.fromISO(filters.value.dateTo))) {
            const tempDate = filters.value.dateTo;
            filters.value.dateTo = filters.value.dateFrom;
            filters.value.dateFrom = tempDate;
        }
    }
}

function setDatePeriod() {
    if (datePeriodDescription.value !== 'Custom') {
        const period = getDatePeriod(datePeriodDescription.value);
        filters.value.dateFrom = period.from;
        filters.value.dateTo = period.to;
    }
}

function valuesValidationMessage(type) {
    if (type !== filters.value.type) {
        return null;
    }
    return validationErrors.value.values;
}

function setModal(inputModal) {
    modal.value = inputModal;
}

function modalCancel() {
    modal.value.isOpen = false;
    modal.value.cancelAction();
}

function modalConfirm() {
    modal.value.isOpen = false;
    modal.value.confirmAction();
}

</script>
<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving || modal.isOpen"
            class="page-mask"
            data-cy="report-page-mask"
        >
        </div>
        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column">
                <h3>Period</h3>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentMonth'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentMonth"
                    />
                    <span>
                        Current Month
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='PreviousMonth'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="PreviousMonth"
                    />
                    <span>
                        Previous Month
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentQuarter'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentQuarter"
                    />
                    <span>
                        Current Quarter
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='PreviousQuarter'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="PreviousQuarter"
                    />
                    <span>
                        Previous Quarter
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentFinancialYear'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentFinancialYear"
                    />
                    <span>
                        Current Financial Year
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='PreviousFinancialYear'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="PreviousFinancialYear"
                    />
                    <span>
                        Previous Financial Year
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='Custom'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="Custom"
                    />
                    <span>
                        Custom
                    </span>
                </div>
                <div class="qv-flex-row">
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">From</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateFrom"
                            :border="true"
                        >
                            <date-picker
                                v-model="filters.dateFrom"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                :disabled="datePeriodDescription !== 'Custom'"
                                @input="updateDates"
                            />
                        </tooltip>
                    </span>
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">To</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateTo"
                            :border="true"
                        >
                            <date-picker
                                v-model="filters.dateTo"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                :disabled="datePeriodDescription !== 'Custom'"
                                @input="updateDates"
                            />
                        </tooltip>
                    </span>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Report Type</h3>
                <div class="qv-flex-row report-option" @click="filters.type='Cost Centre'">
                    <input
                        v-model="filters.type"
                        type="radio"
                        name="report-type"
                        value="Cost Centre"
                    />
                    <i class="material-symbols-outlined" style="margin-top: auto;font-size:2.8rem;padding-bottom: 5px;">flag</i>
                    <span class="icon-label">
                        Cost Centre
                    </span>
                </div>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="valuesValidationMessage('Cost Centre')"
                        :border="true"
                    >
                        <multiselect
                            v-model="selectedCostCentres"
                            :options="costCentreList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Cost"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            :disabled="filters.type !== 'Cost Centre'"
                            @input="updateSelectedCostCentre"
                        />
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Report Format</h3>
                <div class="qv-flex-row report-option" @click="filters.format='EXCEL'">
                    <input
                        v-model="filters.format"
                        type="radio"
                        name="report-format"
                        value="EXCEL"
                    />
                    <span>
                        Excel
                    </span>
                </div>
            </div>
        </div>        
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                @click="clearFilters"
                class="mdl-button mdl-button--raised"
            >
                Clear
            </button>
            <button
                @click="submitReportRequest"
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving"
                data-cy="schedule-report-button"
            >
                Schedule Report
            </button>
        </div>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
            data-cy="report-modal"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                        data-cy="report-modal-cancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                        data-cy="report-modal-confirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
            />
        </alert-modal>
    </div>
</template>
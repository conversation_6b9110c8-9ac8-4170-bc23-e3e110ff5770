<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router/composables';
import {
    currentReport,
    notifications,
    clearNotifications,
    NONE } from '../utils';
import 'vue2-datepicker/index.css';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Multiselect from 'vue-multiselect';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, validateRolls, validateTerritorialAuthority, convertToInt } from './shared';
import { useSalesGroups } from '@/composables/salesGroups';


const saving = ref(false);
const router = useRouter();
const salesGroups = useSalesGroups();

const sourceMapping = {
    SALES_GROUPS: 'S',
    ROLLS: 'R',
    TERRITORIAL_AUTHORITY: 'T',
};

const sourceList = [
    { id: 'S', description: 'Sales Groups' },
    { id: 'R', description: 'Rolls' },
    { id: 'T', description: 'Territorial Authority' },
];

const reportTypeMapping = {
    STANDARD: 'S',
    ANNUAL: 'A',
};

const format = ref('EXCEL');
const source = ref({ id: 'T', description: 'Territorial Authority' });
const rollType = ref('S');
const rollSingle = ref(null);
const rollStart = ref(null);
const rollEnd = ref(null);
const multipleRolls = reactive({
    multipleRolls1: null,
    multipleRolls2: null,
    multipleRolls3: null,
    multipleRolls4: null,
    multipleRolls5: null,
});
const territorialAuthority = ref(NONE);
const categoryGroup = ref('*');
const categoryLength = ref(null);
const reportType = ref(reportTypeMapping.STANDARD);
const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null,
    territorialAuthority: null,
    categoryGroup: null,
});

function getMappedSalesGroup(salesGroup) {
    const matchingTASalesGroups = salesGroups.value.filter(
        sg => parseInt(sg.taCode) === parseInt(territorialAuthority.value.code),
    );
    const group = matchingTASalesGroups.find(
        sg => parseInt(sg.salesGroupNumber) === salesGroup,
    );
    return group ? group.saleGroupId : null;
}

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate a Summary of Values by Category',
        sticky: false,
    });
});

function getFilterValues() {
    const filters = {
        source: source.value,
        rollType: rollType.value,
        rollSingle: rollSingle.value,
        rollStart: rollStart.value,
        rollEnd: rollEnd.value,
        multipleRolls1: multipleRolls.multipleRolls1,
        multipleRolls2: multipleRolls.multipleRolls2,
        multipleRolls3: multipleRolls.multipleRolls3,
        multipleRolls4: multipleRolls.multipleRolls4,
        multipleRolls5: multipleRolls.multipleRolls5,
        territorialAuthority: territorialAuthority.value,
        categoryGroup: categoryGroup.value,
        categoryLength: categoryLength.value,
        format: format.value,
        reportType: reportType.value,
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    clearFilterValidations();
    source.value = { id: sourceMapping.TERRITORIAL_AUTHORITY, description: 'Territorial Authority' };
    clearRolls();
    territorialAuthority.value = NONE;
    categoryGroup.value = '*';
    categoryLength.value = null;
    reportType.value = reportTypeMapping.STANDARD;
    saving.value = false;
}

function clearRolls() {
    rollType.value = 'S';
    rollStart.value = null;
    rollEnd.value = null;
    rollSingle.value = null;
    resetMultipleRolls();
}

function clearFilterValidations() {
    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null,
        categoryGroup: null,
        territorialAuthority: null,
    };
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilterValues();

    clearFilterValidations();

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    // Set values to null if they are not used.
    if (filters.rollType === 'S') {
        filters.rollStart = convertToInt(filters.rollSingle, null);
    }
    else if (filters.rollType === 'R') {
        filters.rollStart = convertToInt(filters.rollStart, null);
        filters.rollEnd = convertToInt(filters.rollEnd, null);
    }

    const parameters = {
        format: filters.format,
        reportType: filters.reportType === 'S' ? 0 : 1,
        Source: filters.source.id,
        Type: filters.rollType,
        Start: filters.source.id === 'S' && filters.rollStart ? getMappedSalesGroup(filters.rollStart) : filters.rollStart,
        End: filters.source.id === 'S' && filters.rollEnd ? getMappedSalesGroup(filters.rollEnd) : filters.rollEnd,
        Multiple: [
            filters.source.id === 'S' && filters.multipleRolls1 ? getMappedSalesGroup(filters.multipleRolls1) : filters.multipleRolls1,
            filters.source.id === 'S' && filters.multipleRolls2 ? getMappedSalesGroup(filters.multipleRolls2) : filters.multipleRolls2,
            filters.source.id === 'S' && filters.multipleRolls3 ? getMappedSalesGroup(filters.multipleRolls3) : filters.multipleRolls3,
            filters.source.id === 'S' && filters.multipleRolls4 ? getMappedSalesGroup(filters.multipleRolls4) : filters.multipleRolls4,
            filters.source.id === 'S' && filters.multipleRolls5 ? getMappedSalesGroup(filters.multipleRolls5) : filters.multipleRolls5,
        ]
            .filter(roll => roll)
            .join(','),
        RatingAuthorityId: filters.territorialAuthority.id,
        CategoryType: filters.categoryGroup ? filters.categoryGroup.replace(/\*/g, '%') : null,
        CategoryLength: filters.categoryLength,
    };

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function validateFilters(filters) {
    let valid = true;

    const validRolls = validateRolls(filters);
    const validTerritorialAuthority = validateTerritorialAuthority(filters.territorialAuthority);

    if (source.value.id !== sourceMapping.TERRITORIAL_AUTHORITY && !validRolls.isValid) {
        valid = false;
        validationErrors.value.rollSingle = rollType.value === 'S' ? validRolls.message : '';
        validationErrors.value.rangeOfRoll = rollType.value === 'R' ? validRolls.message : '';
        validRolls.multipleRolls = rollType.value === 'M' ? validRolls.message : '';
    }
    if (source.value.id === sourceMapping.TERRITORIAL_AUTHORITY && !validTerritorialAuthority.isValid) {
        valid = false;
        validationErrors.value.territorialAuthority = validTerritorialAuthority.message;
    }

    if (source.value.id === sourceMapping.SALES_GROUPS && !validTerritorialAuthority.isValid) {
        valid = false;
        validationErrors.value.territorialAuthority = 'Territorial Authority is required when selecting Sales Groups';
    }
    if (source.value.id === sourceMapping.SALES_GROUPS && !validateSalesGroups(filters)) {
        valid = false;
    }


    return valid;
}

function validateSalesGroups(filters) {
    let valid = true;
    if (filters.rollType === 'S' && getMappedSalesGroup(filters.rollSingle) === null) {
        valid = false;
        validationErrors.value.rollSingle = 'Sales Group not found in TA Code';
    }
    if (filters.rollType === 'R' && (getMappedSalesGroup(filters.rollStart) === null || getMappedSalesGroup(filters.rollEnd) === null)) {
        valid = false;
        validationErrors.value.rangeOfRoll = 'Sales Group not found in TA Code';
    }
    if (filters.rollType === 'M') {
        const rolls = Object.values(multipleRolls).filter(roll => roll);
        rolls.forEach((roll) => {
            if (roll !== null && getMappedSalesGroup(roll) === null) {
                valid = false;
                validationErrors.value.multipleRolls = 'One or More Sales Groups not found in TA Code';
            }
        });
    }
    return valid;
}

function toggleReportType(type) {
    reportType.value = type;
}

function toggleRollType(type) {
    clearFilterValidations();
    rollType.value = type;
    rollSingle.value = null;
    rollEnd.value = null;
    rollStart.value = null;
    resetMultipleRolls();
}

watch(
    () => source.value.id,
    (newId) => {
        if (newId === 'T') {
            clearRolls();
        }
    },
);

function resetMultipleRolls() { Object.keys(multipleRolls).forEach((key) => { multipleRolls[key] = null; }); }

</script>

<template>
    <div class="report-criteria qv-flex-column" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask"/>
        <h3>General</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.5;">
                <h3>Source</h3>
                <div>
                    <multiselect v-model="source" :options="sourceList" :multiple="false" :close-on-select="true"
                                 track-by="id" label="description" :preselect-first="true"
                                 placeholder="Select Source" select-label="⏎ select" :allow-empty="false" />
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('S')">
                    <input v-model="rollType" type="radio" name="roll-type" value="S" :disabled="source.id === 'T'">
                    <span>
                        Single Roll or Sales Group
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('R')">
                    <input v-model="rollType" type="radio" name="roll-type" value="R" :disabled="source.id === 'T'">
                    <span>
                        Range of Rolls or Sales Groups
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('M')">
                    <input v-model="rollType" type="radio" name="roll-type" value="M" :disabled="source.id === 'T'">
                    <span>
                        Multiple Rolls or Sales Groups
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rollSingle" :border="true">
                        <input v-model.number="rollSingle" type="number" :disabled="rollType !== 'S' || source.id === 'T'"
                               data-cy="single-roll-input" maxlength="7">
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rangeOfRoll" :border="true">
                        <input v-model.number="rollStart" type="number" :disabled="rollType !== 'R' || source.id === 'T'" maxlength="7" >
                        <input v-model.number="rollEnd" type="number" :disabled="rollType !== 'R' || source.id === 'T'" maxlength="7">
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.multipleRolls" :border="true">
                        <input v-model.number="multipleRolls.multipleRolls1" type="number" :disabled="rollType !== 'M' || source.id === 'T'" maxlength="7">
                        <input v-model.number="multipleRolls.multipleRolls2" type="number" :disabled="rollType !== 'M' || source.id === 'T'" maxlength="7">
                        <input v-model.number="multipleRolls.multipleRolls3" type="number" :disabled="rollType !== 'M' || source.id === 'T'" maxlength="7">
                        <input v-model.number="multipleRolls.multipleRolls4" type="number" :disabled="rollType !== 'M' || source.id === 'T'" maxlength="7">
                        <input v-model.number="multipleRolls.multipleRolls5" type="number" :disabled="rollType !== 'M' || source.id === 'T'" maxlength="7">
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.5;">
                <h3>Territorial Authorities</h3>
                <div>
                    <tooltip display-mode="error" :text="validationErrors.territorialAuthority" :border="true" style="width: 100%;">
                        <ta-drop-down-select v-model="territorialAuthority"/>
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.5">
                <h3>Category Group</h3>
                <div class="qv-flex-row">
                    <tooltip display-mode="error" :text="validationErrors.categoryGroup" :border="true" style="width: 100%;">
                        <input v-model="categoryGroup" type="text" class="text-input" data-cy="category-group-input" maxlength="7">
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 0.5">
                <h3>Category Length</h3>
                <div class="qv-flex-row">
                    <tooltip display-mode="error" :text="validationErrors.categoryLength" :border="true" style="width: 100%;">
                        <input v-model.number="categoryLength" type="number" class="text-input" data-cy="category-length-input" maxlength="2">
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <h3>Report Type</h3>
            <div class="qv-flex-column" style="flex-grow: 0.25;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleReportType(reportTypeMapping.STANDARD)">
                    <input
                        v-model="reportType"
                        type="radio"
                        value="S"
                    >
                    <span>
                        Standard
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 0.25;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleReportType(reportTypeMapping.ANNUAL)">
                    <input
                        v-model="reportType"
                        type="radio"
                        value="A"
                    >
                    <span>
                        Annual Value
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button class="mdl-button mdl-button--raised" @click="clearFilters">
                Clear
            </button>
            <button class="mdl-button mdl-button--raised mdl-button--colored" data-cy="schedule-report-button"
                    :disabled="saving" @click="createReportRequest">
                Schedule Report
            </button>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

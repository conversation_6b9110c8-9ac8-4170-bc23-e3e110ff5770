<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router/composables';
import { DateTime } from 'luxon';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
} from '../utils';
import { submitReportRequest, getDatePeriod } from './shared';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import Multiselect from 'vue-multiselect';
import Tooltip from '@/components/common/Tooltip.vue';

const saving = ref(false);
const datePeriodDescription = ref('CurrentMonth');
const router = useRouter();

const selectedTAs = ref([]);
const filters = ref({
    dateFrom: null,
    dateTo: null,
    format: 'EXCEL',
    type: 'TA',
    values: [],
});
const reportId = computed(() => currentReport.value?.id);
const validationErrors = ref({
    dateFrom: null,
    dateTo: null,
    format: null,
    type: null,
    values: null,
});

const taStore = useTaList();
const taList = computed(() => [{ id: 0, code: '0', description: 'All Territorial Authorities' }].concat(taStore.value));

watch(datePeriodDescription, () => {
    setDatePeriod();
});

onMounted(async () => {
    clearNotifications();
    setDatePeriod();
    updateSelectedTAs();
    notifications.value.push({
        type: 'info',
        message: 'Generate a TA Rating Summary for a work unit actuals for TAs, for a specific period. Note that the data entered in Valor, is not included. After scheduling, the report can be accessed by clicking on “View My Reports” in the left menu.',
        sticky: false,
    });
});

function clearFilters() {
    datePeriodDescription.value = 'CurrentMonth';
    filters.value = {
        dateFrom: null,
        dateTo: null,
        type: 'TA',
        values: [],
        format: 'EXCEL',
    };
    setDatePeriod();
    selectedTAs.value = [];
    updateSelectedTAs();
}

function getFilterValues() {
    if (selectedTAs.value.length === 1 && selectedTAs.value[0]?.id === 0) {
        return taList.value.map(a => a.id);
    }
    return selectedTAs.value.map(a => a.id);
}

async function createReportRequest() {
    saving.value = true;

    filters.value.values = getFilterValues();

    const reportRequest = {
        reportId: reportId.value,
        parameters: {
            dateFrom: filters.value.dateFrom,
            dateTo: filters.value.dateTo,
            format: filters.value.format,
            values: filters.value.values,
            type: filters.value.type,
        },
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function updateSelectedTAs() {
    if (selectedTAs.value.length > 0 && selectedTAs.value[selectedTAs.value.length - 1].id === 0) {
        selectedTAs.value = [{ id: 0, code: '0', description: 'All Territorial Authorities' }];
    }
    if (selectedTAs.value.length > 1) {
        selectedTAs.value = selectedTAs.value.filter(x => x.id !== 0);
    }
    if (selectedTAs.value.length === 0) {
        selectedTAs.value.push({ id: 0, code: '0', description: 'All Territorial Authorities' });
    }
}

function updateDates() {
    if (DateTime.fromISO(filters.value.dateFrom).isValid()
    && DateTime.fromISO(filters.value.dateTo).isValid()
    && (DateTime.fromISO(filters.value.dateFrom).isAfter(DateTime.fromISO(filters.value.dateTo)))) {
        const tempDate = filters.value.dateTo;
        filters.value.dateTo = filters.value.dateFrom;
        filters.value.dateFrom = tempDate;
    }
}

function setDatePeriod() {
    if (datePeriodDescription.value !== 'Custom') {
        const period = getDatePeriod(datePeriodDescription.value);
        filters.value.dateFrom = period.from;
        filters.value.dateTo = period.to;
    }
}

function valuesValidationMessage(type) {
    if (type !== filters.value.type) {
        return null;
    }
    return validationErrors.value.values;
}

</script>
<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving"
            class="page-mask"
            data-cy="report-page-mask"
        />
        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column">
                <h3>Period</h3>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentMonth'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentMonth"
                    >
                    <span>
                        Current Month
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='PreviousMonth'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="PreviousMonth"
                    >
                    <span>
                        Previous Month
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentQuarter'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentQuarter"
                    >
                    <span>
                        Current Quarter
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='PreviousQuarter'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="PreviousQuarter"
                    >
                    <span>
                        Previous Quarter
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentFinancialYear'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentFinancialYear"
                    >
                    <span>
                        Current Financial Year
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='PreviousFinancialYear'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="PreviousFinancialYear"
                    >
                    <span>
                        Previous Financial Year
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='Custom'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="Custom"
                    >
                    <span>
                        Custom
                    </span>
                </div>
                <div class="qv-flex-row">
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">From</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateFrom"
                            :border="true"
                        >
                            <date-picker
                                v-model="filters.dateFrom"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                :disabled="datePeriodDescription !== 'Custom'"
                                @input="updateDates"
                            />
                        </tooltip>
                    </span>
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">To</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateTo"
                            :border="true"
                        >
                            <date-picker
                                v-model="filters.dateTo"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                :disabled="datePeriodDescription !== 'Custom'"
                                @input="updateDates"
                            />
                        </tooltip>
                    </span>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Report Type</h3>
                <div class="qv-flex-row report-option" @click="filters.type='TA'">
                    <input
                        v-model="filters.type"
                        type="radio"
                        name="report-type"
                        value="TA"
                    >
                    <i class="material-symbols-outlined" style="margin-top: auto;font-size:2.8rem;padding-bottom: 5px;">flag</i>
                    <span class="icon-label">
                        Territorial Authorities
                    </span>
                </div>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="valuesValidationMessage('TA')"
                        :border="true"
                    >
                        <multiselect
                            v-model="selectedTAs"
                            :options="taList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Territorial Authorities"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            data-cy="territorial-authority-selector"
                            @input="updateSelectedTAs"
                        />
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Report Format</h3>
                <div class="qv-flex-row report-option" @click="filters.format='EXCEL'">
                    <input
                        v-model="filters.format"
                        type="radio"
                        name="report-format"
                        value="EXCEL"
                    >
                    <span>
                        Excel
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                class="mdl-button mdl-button--raised"
                @click="clearFilters"
            >
                Clear
            </button>
            <button
                :disabled="saving"
                class="mdl-button mdl-button--raised mdl-button--colored"
                data-cy="schedule-report-button"
                @click="createReportRequest"
            >
                Schedule Report
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router/composables';
import { DateTime } from 'luxon';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
    createReportJob,
    validateReportField,
    formatDateToMMDDYYYY
} from '../utils';
import DatePicker from "vue2-datepicker";
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";
import Tooltip from '@/components/common/Tooltip.vue';
import AlertModal from '@/components/common/modal/AlertModal.vue';

const saving = ref(false);
const router = useRouter();

const startDate = ref('');
const endDate = ref('');
const taValues = ref([]);
const status = ref({ value: 'All', label: 'All' });

const reportId = computed(() => currentReport.value?.id);
const parameters = computed(() => {
  const params = currentReport.value?.parameters ? JSON.parse(currentReport.value.parameters) : {};
  return params.properties || {};
});

const validationErrors = ref({
    startDate: null,
    endDate: null,
    taValues: null,
    status: null
});

const taStore = useTaList();
const taList = computed(() => [{ id: 0, code: '0', description: 'All Territorial Authorities' }].concat(taStore.value));

const statusOptions = [
  { value: 'All', label: 'All' },
  { value: 'A', label: 'Actioned' },
  { value: 'U', label: 'Unactioned' }
];

const modal = ref({
    mode: 'warning',
    isOpen: false,
    heading: 'heading',
    message: '',
    messages: [],
    cancelText: 'No',
    cancelAction: () => { },
    confirmText: 'Yes',
    confirmAction: () => { },
    code: '',
})

onMounted(async () => {
    clearNotifications();
    status.value = { value: 'All', label: 'All' }
    updateDates();
    updateSelectedTAs();
    notifications.value.push({
        type: 'info',
        message: `Generate a Subdivisions Listing Report for TAs, for a specific period.\n\nImportant Note: This report will schedule a large file size/requests, any other export requests made by users will be affected and held up this large request.\n\nWe recommend this report be requested Afterhours or towards end of day, to prevent causing issues for other users`,
        sticky: false,
    });
});

async function submitReportRequest() {
    const reportRequest = createReportRequest();

    if (!reportRequest) {
        return;
    }

    saving.value = true;
    const result = await createReportJob(reportRequest);

    if (result.status !== 'CREATED'){
        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Scheduling failed',
            message: 'An error occurred while attempting to schedule your report.  Please contact support or try again later.',
            messages: [],
            cancelText: null,
            cancelAction: () => { },
            confirmText: 'OK',
            confirmAction: () => { saving.value = false; },
            code: 'REPORT_FAILED_MESSAGE',
        });
        return;
    }

    setModal({
        mode: 'message',
        isOpen: true,
        heading: 'Report Scheduled',
        message: 'Your report has been acknowledged and can be viewed in View My Reports.',
        messages: [],
        cancelText: 'View My Reports',
        cancelAction: () => { saving.value = false; router.push({ name: 'report-dashboard-my-reports' }); },
        confirmText: 'OK',
        confirmAction: () => { saving.value = false; },
        code: 'REPORT_SCHEDULED_MESSAGE',
    });
}

function getFilters() {
    const taVals = getTAValues();
    const statusVal = getStatusValue();
    const filters = {
        startDate: startDate.value,
        endDate: endDate.value,
        taValues: taVals,
        status: statusVal
    };
    return filters;
}

function validateFilters(filters) {
    let valid = true;
    for (const prop in parameters.value) {
        validationErrors.value[prop] = null;
        const errs = validateReportField(filters[prop], parameters.value[prop], true);
        if (errs.length > 0) {
            validationErrors.value[prop] = errs.join('\n')
            valid = false;
        }
    }
    return valid;
}

function clearFilters() {
    saving.value = true;
    startDate.value = '';
    endDate.value = '';
    taValues.value = [],
    updateDates();
    updateSelectedTAs();
    defaultStatus();
    saving.value = false;
}

function getStatusValue() {
    return status.value.value;
}

function getTAValues() {
    let values = []
    values = taValues.value.map(a => a.code);
    if (taValues.value.length === 1 && taValues.value[0]?.id === 0) {
        values = taList.value.map(a => a.code);
    }
    return values;
}

function createReportRequest() {
    const filters = getFilters();
    if (filters.taValues && Array.isArray(filters.taValues)) {
        filters.taValues = filters.taValues.join(',');
    }
    if (filters.startDate) {
        filters.startDate = formatDateToMMDDYYYY(filters.startDate);
    }
    if (filters.endDate) {
        filters.endDate = formatDateToMMDDYYYY(filters.endDate);
    }
    if (!validateFilters(filters)) {
        return;
    }
    return {
        reportId: reportId.value,
        parameters: filters,
    }
}

function updateSelectedTAs() {
    if (taValues.value.length > 0 && taValues.value[taValues.value.length -1 ].id === 0) {
        taValues.value = [{ id: 0, code: '0', description: 'All Territorial Authorities' }];
    }
    if (taValues.value.length > 1) {
        taValues.value = taValues.value.filter(x => x.id !== 0);
    }
    if (taValues.value.length === 0) {
        taValues.value.push({ id: 0, code: '0', description: 'All Territorial Authorities' });
    }
}

function updateDates() {
    const start = DateTime.fromISO(startDate.value);
    const end = DateTime.fromISO(endDate.value);
    if (start.isValid && end.isValid) {
        if (start > end) {
            const tempDate = endDate.value;
            endDate.value = startDate.value;
            startDate.value = tempDate;
        }
    }
}

function setModal(inputModal) {
    modal.value = inputModal;
}

function modalCancel() {
    modal.value.isOpen = false;
    modal.value.cancelAction();
}

function modalConfirm() {
    modal.value.isOpen = false;
    modal.value.confirmAction();
}

function defaultStatus() {
    status.value = { value: 'All', label: 'All' };
}

</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving || modal.isOpen"
            class="page-mask"
            data-cy="report-page-mask"
        >
        </div>
        <h3>List Subdivisions By</h3>
        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column">
                <h3>Status:</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :border="true"
                    >
                        <multiselect
                            v-model="status"
                            :options="statusOptions"
                            :multiple="false"
                            :close-on-select="true"
                            track-by="value"
                            label="label"
                            :preselect-first="true"
                            placeholder="Select status"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            data-cy="status-multiselect"
                            @remove="defaultStatus"
                        />
                    </tooltip>
                </div>
                <div class="qv-flex-row">
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">Start Date</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.startDate"
                            :border="true"
                        >
                            <date-picker
                                v-model="startDate"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                @input="updateDates"
                                data-cy="start-date"
                            />
                        </tooltip>
                    </span>
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">End Date</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.endDate"
                            :border="true"
                        >
                            <date-picker
                                v-model="endDate"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                @input="updateDates"
                                data-cy="end-date"
                            />
                        </tooltip>
                    </span>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Territorial Authorities</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :border="true"
                    >
                        <multiselect
                            v-model="taValues"
                            :options="taList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Territorial Authorities"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            data-cy="ta-multiselect"
                            @input="updateSelectedTAs"
                        />
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                @click="clearFilters"
                class="mdl-button mdl-button--raised"
                data-cy='clear-report-button'
            >
                Clear
            </button>
            <button
                @click="submitReportRequest"
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving"
                data-cy="schedule-report-button"
            >
                Schedule Report
            </button>
        </div>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
            data-cy="report-modal"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                        data-cy="report-modal-cancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                        data-cy="report-modal-confirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
            />
        </alert-modal>
    </div>
</template>
<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router/composables';
import {
    currentReport,
    notifications,
    clearNotifications
} from '../utils';
import DatePicker from "vue2-datepicker";
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, convertToInt } from './shared'
import moment from 'moment';
import { NONE } from '../utils';

const saving = ref(false);
const router = useRouter();

const rollTypeMapping = {
    SINGLE: 'S',
    RANGE: 'R',
    MULTIPLE: 'M'
}
const sortOrderMapping = {
    VALUATION_REF_NO: 'D',
    STREET_ADDRESS: 'S',
    SALE_DATE: 'T'
}
const reportFormatMapping = {
    PDF: 'P',
    EXCEL: 'E'
}

const FILTER_DEFAULTS = {
    format: reportFormatMapping.PDF,
    RollType: rollTypeMapping.SINGLE,
    RollStart: null,
    RollEnd: null,
    MultipleRolls: null,
    RatingAuthorityId: null,
    SaleDateStart: null,
    SaleDateEnd: moment().format('YYYY-MM-DD'),
    Category: '*',
    CategoryGroup: '*',
    InputDateStart: null,
    InputDateEnd: null,    
    PriceStart: null,
    PriceEnd: null,    
    CVStart: null,
    CVEnd: null,
    SaleStatus: { id: -1, description: 'All Sales' },
    sortby: sortOrderMapping.VALUATION_REF_NO
};

const reportId = computed(() => currentReport.value?.id);

const filters = reactive(Object.assign({}, FILTER_DEFAULTS));
const rollSingleInput = ref(null);
const multipleRolls = reactive({
    multipleRolls1: undefined,
    multipleRolls2: undefined,
    multipleRolls3: undefined,
    multipleRolls4: undefined,
    multipleRolls5: undefined
});
const territorialAuthority = ref(NONE);

const VALIDATION_ERRORS_DEFAULT = {
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null,
    territorialAuthority: null,
    saleDateFrom: null,
    saleDateTo: null,
    category: null,
    categoryGroup: null,
    inputDateFrom: null,
    inputDateTo: null,
    netPriceStart: null,
    netPriceEnd: null,
    capitalValueFrom: null,
    capitalValueTo: null
};

const validationErrors = ref(Object.assign({}, VALIDATION_ERRORS_DEFAULT));

const saleStatusList = [
    { id: -1, description: 'All Sales' },
    { id: 1, description: 'Exclude Unconfirmed and Pending Sales' },
    { id: 2, description: 'Unconfirmed and Pending Sales Only' }
];

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: `Generate a Sales Extract by TA or Roll`,
        sticky: false,
    }, {
        type: 'info',
        message: 'Pending and Unconfirmed Sales - An unconfirmed sale is a recent sale that has not yet been formally reported. A pending sale is a recent sale that QV has been notified of by solicitors but not yet settled.',
        sticky: false,
    });
});

function clearFilters() {
    saving.value = true;
    clearFilterValidations();
    Object.assign(filters, FILTER_DEFAULTS);
    resetMultipleRolls();
    saving.value = false;
    rollSingleInput.value = null;
    territorialAuthority.value = NONE;
}

function clearFilterValidations() {
    Object.assign(validationErrors.value, VALIDATION_ERRORS_DEFAULT);
}

function validateFilters() {
    let valid = true;

    if (territorialAuthority.value.id === -1) {
        valid = false;
        validationErrors.value.territorialAuthority = 'Territorial Authority needs to be entered';
    }

    if (filters.RollType === rollTypeMapping.SINGLE && !rollSingleInput.value) {
        valid = false;
        validationErrors.value.rollSingle = 'Single Roll number needs to be entered';
    } else if (filters.RollType === rollTypeMapping.RANGE && (!filters.RollStart || !filters.RollEnd)) {
        valid = false;
        validationErrors.value.rangeOfRoll = 'Range of Roll numbers need to be entered';
    } else if (filters.RollType === rollTypeMapping.MULTIPLE && !multipleRolls.multipleRolls1 && !multipleRolls.multipleRolls2 && !multipleRolls.multipleRolls3 && !multipleRolls.multipleRolls4 && !multipleRolls.multipleRolls5) {
        valid = false;
        validationErrors.value.multipleRolls = 'At least one Multiple Roll number needs to be entered';
    }

    if (!filters.SaleDateStart) {
        valid = false;
        validationErrors.value.saleDateFrom = 'Sale Date From needs to be selected';
    }
    if (!filters.SaleDateEnd) {
        valid = false;
        validationErrors.value.saleDateTo = 'Sale Date To needs to be selected';
    }

    if (filters.InputDateStart && !filters.InputDateEnd) {
        valid = false;
        validationErrors.value.inputDateFrom = 'Input Date Range From can only be used when Input Date Range To';
    }
    if (!filters.InputDateStart && filters.InputDateEnd) {
        valid = false;
        validationErrors.value.inputDateTo = 'Input Date Range To can only used when Input Date Range From';
    }

    if (filters.PriceStart && !filters.PriceEnd) {
        valid = false;
        validationErrors.value.netPriceStart = 'Net Price Range From can only be used when Net Price Range To';
    }
    if (!filters.PriceStart && filters.PriceEnd) {
        valid = false;
        validationErrors.value.netPriceEnd = 'Net Price Range To can only be used when Net Price Range From';
    }

    if (!filters.CVStart && filters.CVEnd) {
        valid = false;
        validationErrors.value.capitalValueFrom = 'Capital Value Range From can only be used when Capital Value Range To';
    }
    if (filters.CVStart && !filters.CVEnd) {
        valid = false;
        validationErrors.value.capitalValueTo = 'Capital Value Range To can only be used when Capital Value Range From';
    }

    return valid;
}

async function createReportRequest() {
    saving.value = true;

    clearFilterValidations();

    if (!validateFilters()) {
        saving.value = false;
        return;
    }

    const parameters = {...filters};

    parameters.format = parameters.format === reportFormatMapping.PDF ? 'PDF' : 'EXCEL';
    if (parameters.RollType === 'S') {
        parameters.RollStart = convertToInt(rollSingleInput.value, null);
    } else {
        parameters.RollStart = convertToInt(parameters.RollStart, null);
        parameters.RollEnd = convertToInt(parameters.RollEnd, null);
    }
    parameters.MultipleRolls = [
        multipleRolls.multipleRolls1,
        multipleRolls.multipleRolls2,
        multipleRolls.multipleRolls3,
        multipleRolls.multipleRolls4,
        multipleRolls.multipleRolls5
    ].filter(roll => (roll && roll !== ''))
    .join(',');    
    if (parameters.MultipleRolls === '') {
        parameters.MultipleRolls = '-1';
    }
    parameters.RatingAuthorityId = territorialAuthority.value.id;
    parameters.Category = parameters.Category ?? '*';
    parameters.CategoryGroup = parameters.CategoryGroup ?? '*';
    parameters.PriceStart = convertToInt(parameters.PriceStart, null);
    parameters.PriceEnd = convertToInt(parameters.PriceEnd, null);
    parameters.CVStart = convertToInt(parameters.CVStart, null);
    parameters.CVEnd = convertToInt(parameters.CVEnd, null);
    parameters.SaleStatus = parameters.SaleStatus.id;

    let reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function toggleReportFormat(reportFormat) {
    filters.format = reportFormat;
}

function toggleSortOrder(order) {
    filters.sortby = order;
}

function toggleRollType(type) {
    filters.RollType = type;
    rollSingleInput.value = null;
    filters.RollEnd = null;
    filters.RollStart = null;
    resetMultipleRolls();
}

function resetMultipleRolls() { Object.keys(multipleRolls).forEach(key => { multipleRolls[key] = ''; }); }

function defaultSaleStatus() {
    filters.SaleStatus = saleStatusList[0];
}

</script>

<template>
    <div class="report-criteria qv-flex-column" style="overflow:visible" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask">
        </div>
        <h3>General:</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" @click="toggleRollType(rollTypeMapping.SINGLE)"
                    style="white-space: nowrap;" data-cy="single-roll-radio">
                    <input v-model="filters.RollType" type="radio" name="roll-type" value='S' />
                    <span>
                        Single Roll
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType(rollTypeMapping.RANGE)"
                    style="white-space: nowrap;" data-cy="range-of-roll-radio">
                    <input v-model="filters.RollType" type="radio" name="roll-type" value='R' />
                    <span>
                        Range of Rolls
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType(rollTypeMapping.MULTIPLE)"
                    style="white-space: nowrap;" data-cy="multiple-rolls-radio">
                    <input v-model="filters.RollType" type="radio" name="roll-type" value='M' />
                    <span>
                        Multiple Rolls
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rollSingle" :border="true">
                        <input v-model="rollSingleInput" type="number"
                            :disabled="filters.RollType !== 'S'" data-cy="single-roll-input"
                            />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rangeOfRoll" :border="true">
                        <input v-model="filters.RollStart" type="number"
                            :disabled="filters.RollType !== 'R'"
                            data-cy="range-of-roll-start-input" />
                        <input v-model="filters.RollEnd" type="number"
                            :disabled="filters.RollType !== 'R'"
                            data-cy="range-of-roll-end-input"/>
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.multipleRolls" :border="true">
                        <input style="margin-right: 0.25rem" v-for="(value, key, index) in multipleRolls"
                            :disabled="filters.RollType !== 'M'" v-model="multipleRolls[key]" type="number"
                            :data-cy="`multiple-roll-${index + 1}-input`" />
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Territorial Authority:</h4>
                    <div style="flex: 3">
                        <tooltip display-mode="error" :text="validationErrors.territorialAuthority" :border="true">
                            <ta-drop-down-select v-model="territorialAuthority"/>
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Sale Date Range:</h4>
                    <div class="qv-flex-row" style="flex: 3">
                        <div style="flex: 20;">
                            <tooltip display-mode="error" :text="validationErrors.saleDateFrom" :border="true"
                                style="width: 100%;">
                                <DatePicker v-model="filters.SaleDateStart" format="D/M/YYYY" value-type="YYYY-MM-DD"
                                    style="width: 100%;" data-cy="sale-date-from-datepicker" />
                            </tooltip>
                        </div>
                        <span style="flex: 1; align-self: center;">to</span>
                        <div style="flex: 20;">
                            <tooltip display-mode="error" :text="validationErrors.saleDateTo" :border="true"
                                style="width: 100%;">
                                <DatePicker v-model="filters.SaleDateEnd" format="D/M/YYYY" value-type="YYYY-MM-DD"
                                    style="width: 100%;" data-cy="sale-date-to-datepicker" />
                            </tooltip>
                        </div>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Category:</h4>
                    <div class="qv-flex-row" style="flex: 3">
                        <tooltip display-mode="error" :text="validationErrors.category" :border="true"
                            style="width: 100%;">
                            <input 
                                v-model="filters.Category" 
                                type="text" 
                                class="text-input" 
                                data-cy="category-input" 
                                maxlength="7" />
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Category Group:</h4>
                    <div class="qv-flex-row" style="flex: 3">
                        <tooltip display-mode="error" :text="validationErrors.categoryGroup" :border="true"
                            style="width: 100%;">
                            <input 
                                v-model="filters.CategoryGroup" 
                                type="text" 
                                class="text-input" 
                                data-cy="category-group-selector" 
                                maxlength="7" />
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Sale Status:</h4>
                    <div style="flex: 3">
                        <multiselect v-model="filters.SaleStatus" :options="saleStatusList || []" :multiple="false"
                            :close-on-select="true" track-by="id" label="description" :preselect-first="true"
                            placeholder="Select Sale Status" select-label="⏎ select" :allow-empty="true"
                            deselect-label="⏎ remove" @remove="defaultSaleStatus"
                            data-cy="sale-status-selector" />
                    </div>
                </div>
                <hr />
            </div>
        </div>
        <h3>Optional:</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Input Date Range:</h4>
                    <div class="qv-flex-row" style="flex: 3">
                        <div style="flex: 20;">
                            <tooltip display-mode="error" :text="validationErrors.inputDateFrom" :border="true"
                                style="width: 100%;">
                                <DatePicker v-model="filters.InputDateStart" format="D/M/YYYY" value-type="YYYY-MM-DD"
                                    style="width: 100%;" data-cy="input-date-range-from-datepicker" />
                            </tooltip>
                        </div>
                        <span style="flex: 1; align-self: center;">to</span>
                        <div style="flex: 20;">
                            <tooltip display-mode="error" :text="validationErrors.inputDateTo" :border="true"
                                style="width: 100%;">
                                <DatePicker v-model="filters.InputDateEnd" format="D/M/YYYY" value-type="YYYY-MM-DD"
                                    style="width: 100%;" data-cy="input-date-range-to-datepicker" />
                            </tooltip>
                        </div>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 14; align-self: center;">Net Price Range:</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.netPriceStart" :border="true"
                            style="width: 100%;">
                            <input v-model="filters.PriceStart" type="number" class="text-input" 
                                data-cy="price-start-range-from-input"
                                />
                        </tooltip>
                    </div>
                    <h4 style="flex: 1; align-self: center;">to</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.netPriceEnd" :border="true"
                            style="width: 100%;">
                            <input v-model="filters.PriceEnd" type="number" class="text-input" 
                                data-cy="price-start-range-to-input"
                                />
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 14; align-self: center;">Capital Value Range:</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.capitalValueFrom" :border="true"
                            style="width: 100%;">
                            <input v-model="filters.CVStart" type="number" class="text-input" 
                                data-cy="capital-value-range-from-input" 
                                />
                        </tooltip>
                    </div>
                    <h4 style="flex: 1; align-self: center;">to</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.capitalValueTo" :border="true"
                            style="width: 100%;">
                            <input v-model="filters.CVEnd" type="number" class="text-input" 
                                data-cy="capital-value-range-to-input"
                                />
                        </tooltip>
                    </div>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <h3 style="margin-top: 1rem; flex: 1">Sort Order</h3>
            <div style="flex: 3" class="qv-flex-column">
                <div class="qv-flex-row report-option" @click="toggleSortOrder(sortOrderMapping.VALUATION_REF_NO)"
                    style="white-space: nowrap;">
                    <input v-model="filters.sortby" type="radio" value="D" data-cy="sort-order-valuation-ref-no-radio" />
                    <span>
                        Valuation Ref No.
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleSortOrder(sortOrderMapping.STREET_ADDRESS)"
                    style="white-space: nowrap;">
                    <input v-model="filters.sortby" type="radio" value="S" data-cy="sort-order-street-address-radio" />
                    <span>
                        Street Address
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleSortOrder(sortOrderMapping.SALE_DATE)"
                    style="white-space: nowrap;">
                    <input v-model="filters.sortby" type="radio" value="T" data-cy="sort-order-sale-date-radio" />
                    <span>
                        Sale Date
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <h3 style="margin-top: 1rem; flex: 1">Report Format</h3>
            <div style="flex: 3" class="qv-flex-column">
                <div class="qv-flex-row report-option" @click="toggleReportFormat(reportFormatMapping.PDF)"
                    style="white-space: nowrap;">
                    <input v-model="filters.format" type="radio" value="P" data-cy="report-format-toggle-pdf-radio" />
                    <span>
                        PDF
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleReportFormat(reportFormatMapping.EXCEL)"
                    style="white-space: nowrap;">
                    <input v-model="filters.format" type="radio" value="E" data-cy="report-format-toggle-excel-radio" />
                    <span>
                        EXCEL
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button @click="clearFilters" class="mdl-button mdl-button--raised" data-cy="clear-report-button">
                Clear
            </button>
            <button @click="createReportRequest" class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving" data-cy="schedule-report-button">
                Schedule Report
            </button>
        </div>
    </div>

</template>

<style lang="scss" src='../../reports/reports.scss' />
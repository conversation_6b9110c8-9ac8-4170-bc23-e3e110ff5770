import useModal from '@/composables/useModal';
import { createReportJob } from '../utils';
import ReportConfirmationModal from '../modal/ReportConfirmationModal.vue';
import { DateTime } from 'luxon';

export const rollTypeMapping = {
    SINGLE: 'S',
    RANGE: 'R',
    MULTIPLE: 'M',
};

export async function submitReportRequest(reportRequest, router) {
    const modal = useModal();

    if (!reportRequest) {
        return;
    }
    const result = await createReportJob(reportRequest);

    if (!result || result.status !== 'CREATED') {
        await modal.show(
            ReportConfirmationModal,
            {
                status: 'failed',
            },
        );
        return;
    }

    try {
        const confirmed = await modal.show(
            ReportConfirmationModal,
            {
                status: 'success',
            },
        );
        if (!confirmed) {
            await router.push({ name: 'report-dashboard-my-reports' });
        }
    }
    catch (error) {
        console.error('Modal error:', error);
    }
}


export function isPositiveInteger(value) {
    return Number.isInteger(value) && value >= 0;
}

export function validateRolls(filters) {
    let valid = true;

    let rollTypeErrorMessage = null;

    if (filters.rollType === rollTypeMapping.SINGLE) {
        if (filters.rollSingle !== null && !isPositiveInteger(filters.rollSingle)) {
            valid = false;
            rollTypeErrorMessage = 'Please specify a valid roll number.';
        }
    }

    if (filters.rollType === rollTypeMapping.RANGE) {
        rollTypeErrorMessage = 'Please specify a valid range of rolls.';
        valid = validateRangeOfRolls(filters);
    }

    if (filters.rollType === rollTypeMapping.MULTIPLE) {
        rollTypeErrorMessage = 'Please specify a valid list of rolls.';
        valid = validateMultipleRolls(filters);
    }

    return {
        message: rollTypeErrorMessage,
        isValid: valid,
    };
}

function validateRangeOfRolls(filters) {
    let valid = true;
    const isRollStartValid = isPositiveInteger(filters.rollStart);
    const isRollEndValid = isPositiveInteger(filters.rollEnd);
    if ((filters.rollStart && !filters.rollEnd) || (!filters.rollStart && filters.rollEnd)) { valid = false; }
    if (!isRollStartValid && !isRollEndValid) {
        valid = false;
    }
    if ((filters.rollStart && !isRollStartValid) || (filters.rollEnd && !isRollEndValid)) {
        valid = false;
    }

    return valid;
}

function validateMultipleRolls(filters) {
    let valid = true;
    const multipleRolls = [
        { roll: filters.multipleRolls1 },
        { roll: filters.multipleRolls2 },
        { roll: filters.multipleRolls3 },
        { roll: filters.multipleRolls4 },
        { roll: filters.multipleRolls5 },
    ];
    const isAtLeastOneRoll = multipleRolls.some(rollItem => rollItem.roll !== null);
    if (!isAtLeastOneRoll) {
        valid = false;
    }

    const isAtLeastOneRollValid = multipleRolls.every(rollItem => (rollItem.roll === null || isPositiveInteger(rollItem.roll)));
    if (!isAtLeastOneRollValid) {
        valid = false;
    }
    else {
        const invalidRoll = multipleRolls.find(rollItem => rollItem.roll && !isPositiveInteger(rollItem.roll));
        if (invalidRoll) {
            valid = false;
        }
    }

    return valid;
}

function validateAttribute(attribute, attributeName) {
    let valid = true;
    const attributeErrorMessage = `Please select a ${attributeName}.`;

    if (!attribute || !isPositiveInteger(attribute.id)) {
        valid = false;
    }

    return {
        message: attributeErrorMessage,
        isValid: valid,
    };
}

export function validateTerritorialAuthority(territorialAuthority) {
    return validateAttribute(territorialAuthority, 'Territorial Authority');
}

export function validateValuer(valuer) {
    return validateAttribute(valuer, 'Valuer');
}

export function validateYear(year) {
    let valid = true;
    const yearErrorMessage = 'Please select a year.';

    if (!isPositiveInteger(parseInt(year))) {
        valid = false;
    }

    return {
        message: yearErrorMessage,
        isValid: valid,
    };
}

export function allowOnlyNumberKeys(event) {
    if (isNaN(event.key) && event.key !== 'Backspace') {
        event.preventDefault();
    }
}

export function limitInputLength(input, size) {
    let limitedInput = input;
    if (input.length > size) {
        limitedInput = input.slice(0, size);
    }
    return limitedInput;
}

export function filterOutNonNumericCharacters(input) {
    return input.replace(/\D/g, '');
}

export function convertToInt(value, defaultValue = 0) {
    return value ? parseInt(value) : defaultValue;
}

export function getDatePeriod(periodDescription) {
    let from = null;
    let to = null;

    if (periodDescription === 'CurrentMonth') {
        from = DateTime.local().startOf('month').toFormat('yyyy-MM-dd');
        to = DateTime.local().toFormat('yyyy-MM-dd');
    }
    else if (periodDescription === 'PreviousMonth') {
        from = DateTime.local().minus({ months: 1 }).startOf('month').toFormat('yyyy-MM-dd');
        to = DateTime.local().minus({ months: 1 }).endOf('month').toFormat('yyyy-MM-dd');
    }
    else if (periodDescription === 'CurrentQuarter') {
        const currentMonth = DateTime.local().month;
        const startMonth = currentMonth - ((currentMonth - 1) % 3); // Calculate start month of the quarter
        const startOfQuarter = DateTime.local().set({ month: startMonth }).startOf('month');
        from = startOfQuarter.toFormat('yyyy-MM-dd');
        to = startOfQuarter.plus({ months: 2 }).endOf('month').toFormat('yyyy-MM-dd');
    }
    else if (periodDescription === 'PreviousQuarter') {
        const currentMonth = DateTime.local().month;
        const startMonth = currentMonth - ((currentMonth - 1) % 3) - 3; // Calculate start month of the previous quarter
        const startOfPreviousQuarter = DateTime.local().set({ month: startMonth }).startOf('month');
        from = startOfPreviousQuarter.toFormat('yyyy-MM-dd');
        to = startOfPreviousQuarter.plus({ months: 2 }).endOf('month').toFormat('yyyy-MM-dd');
    }
    else if (periodDescription === 'CurrentFinancialYear') {
        const currentDate = DateTime.local();
        const year = currentDate.month < 7 ? currentDate.year - 1 : currentDate.year; // Financial year starts in July
        from = DateTime.local(year, 7, 1).toFormat('yyyy-MM-dd');
        to = DateTime.local(year + 1, 6, 30).toFormat('yyyy-MM-dd');
    }
    else if (periodDescription === 'PreviousFinancialYear') {
        const currentDate = DateTime.local();
        const year = currentDate.month < 7 ? currentDate.year - 2 : currentDate.year - 1; // Previous financial year
        from = DateTime.local(year, 7, 1).toFormat('yyyy-MM-dd');
        to = DateTime.local(year + 1, 6, 30).toFormat('yyyy-MM-dd');
    }

    return { from, to };
}

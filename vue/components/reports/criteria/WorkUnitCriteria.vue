<script setup>
import { ref, computed, onMounted, watch, inject } from 'vue';
import { useRouter } from 'vue-router/composables';
import moment from 'moment';
import { store } from '../../../DataStore';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
    createReportJob,
    validateReportField,
} from '../utils';
import DatePicker from "vue2-datepicker";
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";
import Tooltip from '@/components/common/Tooltip.vue';
import AlertModal from '@/components/common/modal/AlertModal.vue';
import { getDatePeriod } from './shared';

const saving = ref(false);
const router = useRouter();
const datePeriodDescription = ref('CurrentMonth');

const userData = inject('userData');

const selectedValuers = ref([{ id: 0, code: '0', description: 'All Valuers' }]);
const selectedTAs = ref([]);
const filters = ref({
    dateFrom: null,
    dateTo: null,
    format: 'EXCEL',
    type: 'Valuer',
    values : [],
});
const reportId = computed(() => currentReport.value?.id);
const parameters = computed(() => JSON.parse(currentReport.value?.parameters || '{ properties: null }').properties);

const validationErrors = ref({
    dateFrom: null,
    dateTo: null,
    format: null,
    type: null,
    values: null,
});

const classificationStore = computed(() => store.state.classifications);

const WorkUnitValuers = computed(() => classificationStore.value?.classifications?.WorkUnitValuer || []);
const WorkUnitValuer = computed(() => WorkUnitValuers.value?.find(user => user.description.includes(userData?.value.userFullName)) || null);

const isAValidWorkUnitValuer = computed(() => WorkUnitValuer.value && userData?.value.isValuer);
const isSubmitDisabled = computed(() => saving.value || userData?.value.isCustomerCare); 

const valuerStore = computed(() => WorkUnitValuers.value.sort((a,b) => (a.description > b.description) ? 1 : ((b.description > a.description) ? -1 : 0)) || [])
const taStore = useTaList();
const taList = computed(() => [{ id: 0, code: '0', description: 'All Territorial Authorities' }].concat(taStore.value));
const valuerList = ref([{ id: 0, code: '0', description: 'All Valuers' }].concat(valuerStore.value));

const hasLoaded = computed(() => userData?.value && WorkUnitValuers.value?.length);

const modal = ref({
    mode: 'warning',
    isOpen: false,
    heading: 'heading',
    message: '',
    messages: [],
    cancelText: 'No',
    cancelAction: () => { },
    confirmText: 'Yes',
    confirmAction: () => { },
    code: '',
})

watch(hasLoaded, () => {
    resetSelectedValuer();
    resetValuerOptions();
})

function resetSelectedValuer() {
    selectedValuers.value = [getDefaultValuer()];
}

function resetValuerOptions() {
    if (isAValidWorkUnitValuer.value) {
        valuerList.value = [getDefaultValuer()];
    } else {
        valuerList.value = [getDefaultValuer(), ...valuerStore.value];
    }
}

function getDefaultValuer() {
    if (isAValidWorkUnitValuer.value) {
        return {
            id: WorkUnitValuer.value.id,
            code: WorkUnitValuer.value.code,
            description: WorkUnitValuer.value.description
        };
    }
    return { id: 0, code: '0', description: 'All Valuers' };
}

watch(datePeriodDescription, () => {
    setDatePeriod();
});

onMounted(async () => {
    clearNotifications();
    setDatePeriod();
    updateSelectedTAs();
    updateSelectedValuers();
    updateValuerOptions();
    notifications.value.push({
        type: 'info',
        message: `Generate a Work Unit Report for Valuers and TAs, for a specific period.\nNote that the data entered in Valor is not included.\nAfter scheduling, the report can be accessed by clicking on “View My Reports” in the left menu.`,
        sticky: false,
    });
});

async function submitReportRequest() {
    const reportRequest = createReportRequest();
    if (!validateFilters()) {
        return;
    }
    saving.value = true;
    const result = await createReportJob(reportRequest);

    if (result.status !== 'CREATED'){
        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Scheduling failed',
            message: 'An error occurred while attempting to schedule your report.  Please contact support or try again later.',
            messages: [],
            cancelText: null,
            cancelAction: () => { },
            confirmText: 'OK',
            confirmAction: () => { saving.value = false; },
            code: 'REPORT_FAILED_MESSAGE',
        });
        return;
    }

    setModal({
        mode: 'message',
        isOpen: true,
        heading: 'Report Scheduled',
        message: 'Your report has been acknowledged and can be viewed in View My Reports.',
        messages: [],
        cancelText: 'View My Reports',
        cancelAction: () => { saving.value = false; router.push({ name: 'report-dashboard-my-reports' }); },
        confirmText: 'OK',
        confirmAction: () => { saving.value = false; },
        code: 'REPORT_SCHEDULED_MESSAGE',
    });
}

function validateFilters() {
    let valid = true;
    for (const prop in parameters.value) {
        validationErrors.value[prop] = null;
        const errs = validateReportField(filters.value[prop], parameters.value[prop], true);
        if (errs.length > 0) {
            validationErrors.value[prop] = errs.join('\n')
            valid = false;
        }
    }
    return valid;
}

function clearFilters() {
    saving.value = true;
    datePeriodDescription.value = 'CurrentMonth';
    filters.value = {
        dateFrom: null,
        dateTo: null,
        type: 'Valuer',
        values : [],
        format: 'EXCEL',
    }
    setDatePeriod();
    selectedValuers.value = [];
    updateSelectedValuers();
    selectedTAs.value = [];
    updateSelectedTAs();
    saving.value = false;
}

function getFilterValues() {
    let values = []
    if (filters.value.type === 'TA') {
        values = selectedTAs.value.map(a => a.id);
        if (selectedTAs.value.length === 1 && selectedTAs.value[0]?.id === 0) {
            values = taList.value.map(a => a.id);
        }
    }
    if (filters.value.type === 'Valuer') {
        values = selectedValuers.value.map(a => a.id);
        if (selectedValuers.value.length === 1 && selectedValuers.value[0]?.id === 0) {
            values = valuerStore.value.map(a => a.id);
        }
    }
    return values;
}

function createReportRequest() {
    filters.value.values = getFilterValues();

    return {
        reportId: reportId.value,
        parameters: filters.value,
    }
}

function updateSelectedValuers() {
    if (selectedValuers.value.length > 1) {
        selectedValuers.value = selectedValuers.value.filter(x => x.id !== 0);
        return;
    }

    if (selectedValuers.value.length === 0) {
        selectedValuers.value = [getDefaultValuer()];
    }
}

function updateSelectedTAs() {
    if (selectedTAs.value.length > 0 && selectedTAs.value[selectedTAs.value.length -1].id === 0) {
        selectedTAs.value = [{ id: 0, code: '0', description: 'All Territorial Authorities' }];
    }
    if (selectedTAs.value.length > 1) {
        selectedTAs.value = selectedTAs.value.filter(x => x.id !== 0);
    }
    if (selectedTAs.value.length === 0) {
        selectedTAs.value.push({ id: 0, code: '0', description: 'All Territorial Authorities' });
    }
}

function updateValuerOptions() {
    resetValuerOptions();

    if (valuerList.value.length > 1) {
        valuerList.value = valuerList.value.filter(x => x.id !== 0);
    }
}

function updateDates() {
    if (moment(filters.value.dateFrom).isValid() && moment(filters.value.dateTo).isValid() ) {
        if (moment(filters.value.dateFrom).isAfter(moment(filters.value.dateTo))) {
            const tempDate = filters.value.dateTo;
            filters.value.dateTo = filters.value.dateFrom;
            filters.value.dateFrom = tempDate;
        }
    }
}

function setDatePeriod() {
    if (datePeriodDescription.value !== 'Custom') {
        const period = getDatePeriod(datePeriodDescription.value);
        filters.value.dateFrom = period.from;
        filters.value.dateTo = period.to;
    }
}

function valuesValidationMessage(type) {
    if (type !== filters.value.type) {
        return null;
    }
    return validationErrors.value.values;
}

function setModal(inputModal) {
    modal.value = inputModal;
}

function modalCancel() {
    modal.value.isOpen = false;
    modal.value.cancelAction();
}

function modalConfirm() {
    modal.value.isOpen = false;
    modal.value.confirmAction();
}

</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-work-unit-criteria"
    >
        <div
            v-if="saving || modal.isOpen"
            class="page-mask"
            data-cy="report-page-mask"
        >
        </div>
        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column">
                <h3>Period</h3>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentMonth'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentMonth"
                    />
                    <span>
                        Current Month
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='PreviousMonth'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="PreviousMonth"
                    />
                    <span>
                        Previous Month
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentQuarter'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentQuarter"
                    />
                    <span>
                        Current Quarter
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='CurrentFinancialYear'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="CurrentFinancialYear"
                    />
                    <span>
                        Current Financial Year
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="datePeriodDescription='Custom'">
                    <input
                        v-model="datePeriodDescription"
                        type="radio"
                        name="date-period"
                        value="Custom"
                    />
                    <span>
                        Custom
                    </span>
                </div>
                <div class="qv-flex-row">
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">From</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateFrom"
                            :border="true"
                        >
                            <date-picker
                                v-model="filters.dateFrom"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                :disabled="datePeriodDescription !== 'Custom'"
                                @input="updateDates"
                            />
                        </tooltip>
                    </span>
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">To</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateTo"
                            :border="true"
                        >
                            <date-picker
                                v-model="filters.dateTo"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                :disabled="datePeriodDescription !== 'Custom'"
                                @input="updateDates"
                            />
                        </tooltip>
                    </span>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Report Type</h3>
                <div class="qv-flex-row report-option" @click="filters.type='Valuer'" data-cy="valuer-button">
                    <input
                        v-model="filters.type"
                        type="radio"
                        name="report-type"
                        value="Valuer"
                        data-cy="valuer-radio"
                    />
                    <i class="material-symbols-outlined" style="margin-top: auto;font-size:2.8rem;padding-bottom: 5px;">group</i>
                    <span class="icon-label">
                        Valuers
                    </span>
                </div>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="valuesValidationMessage('Valuer')"
                        :border="true"
                    >
                        <multiselect
                            v-model="selectedValuers"
                            :options="valuerList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Valuers"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            :disabled="filters.type !== 'Valuer'"
                            @input="updateSelectedValuers"
                            data-cy="valuer-multiselect"
                        />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" @click="filters.type='TA'" :class="{ 'disabled': isAValidWorkUnitValuer }" data-cy="ta-button">
                    <input
                        v-model="filters.type"
                        type="radio"
                        name="report-type"
                        value="TA"
                        :disabled="isAValidWorkUnitValuer" 
                        data-cy="ta-radio" 
                    />
                    <i class="material-symbols-outlined" style="margin-top: auto;font-size:2.8rem;padding-bottom: 5px;">flag</i>
                    <span class="icon-label">
                        Territorial Authorities
                    </span>
                </div>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="valuesValidationMessage('TA')"
                        :border="true"
                    >
                        <multiselect
                            v-model="selectedTAs"
                            :options="taList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Territorial Authorities"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            :disabled="filters.type !== 'TA'"
                            @input="updateSelectedTAs"
                            data-cy="ta-multiselect"
                        />
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Report Format</h3>
                <div class="qv-flex-row report-option" @click="filters.format='EXCEL'">
                    <input
                        v-model="filters.format"
                        type="radio"
                        name="report-format"
                        value="EXCEL"
                    />
                    <span>
                        Excel
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="filters.format='PDF'">
                    <input
                        v-model="filters.format"
                        type="radio"
                        name="report-format"
                        value="PDF"
                    />
                    <span>
                        PDF
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button @click="clearFilters" class="mdl-button mdl-button--raised">
                Clear
            </button>
            <button
                @click="submitReportRequest"
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="isSubmitDisabled"
                data-cy="schedule-report-button"
            >
                Schedule Report
            </button>
        </div>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
            data-cy="report-modal"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                        data-cy="report-modal-cancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                        data-cy="report-modal-confirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
            />
        </alert-modal>
    </div>
</template>
<style scoped>
.enabled {
    pointer-events: auto;
    opacity: 1;
}

.disabled {
    pointer-events: none;
    opacity: 0.6;
}
</style>
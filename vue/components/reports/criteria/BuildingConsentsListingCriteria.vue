<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router/composables';
import moment from 'moment';
import { useTaList } from '../../../composables/taList';
import { useConsentStatus } from '../../../composables/consentStatusTypes';
import {
    currentReport,
    notifications,
    clearNotifications,
    createReportJob,
    validateReportField,
    formatDateToMMDDYYYY
} from '../utils';
import DatePicker from "vue2-datepicker";
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";
import Tooltip from '@/components/common/Tooltip.vue';
import AlertModal from '@/components/common/modal/AlertModal.vue';

const saving = ref(false);
const router = useRouter();

const dateUsed = ref(1);
const dateFrom = ref('');
const dateTo = ref('');
const taValues = ref([]);
const statusValues = ref([]);
const excludeConsentsByQV = ref(0);

const reportId = computed(() => currentReport.value?.id);
const parameters = computed(() => {
  const params = currentReport.value?.parameters ? JSON.parse(currentReport.value.parameters) : {};
  return params.properties || {};
});

const validationErrors = ref({
    dateUsed: null,
    dateFrom: null,
    dateTo: null,
    taValues: null,
    statusValues: null,
    excludeConsentsByQV: null
});

const taStore = useTaList();
const taList = computed(() => [{ id: 0, code: '0', description: 'All Territorial Authorities' }].concat(taStore.value));

const consentStatusStore = useConsentStatus();
const consentStatusList = computed(() => [{ id: 0, code: '0', description: 'All' }].concat(consentStatusStore.value));

const modal = ref({
    mode: 'warning',
    isOpen: false,
    heading: 'heading',
    message: '',
    messages: [],
    cancelText: 'No',
    cancelAction: () => { },
    confirmText: 'Yes',
    confirmAction: () => { },
    code: '',
})

onMounted(async () => {
    clearNotifications();
    updateDates();
    updateSelectedTAs();
    updateSelectedStatus();
    notifications.value.push({
        type: 'info',
        message: `Generate a Building Consents Listing Report for TAs, for a specific period.\n\nImportant Note: This report will schedule a large file size/requests, any other export requests made by users will be affected and held up this large request.\n\nWe recommend this report be requested Afterhours or towards end of day, to prevent causing issues for other users`,
        sticky: false,
    });
});

async function submitReportRequest() {
    const reportRequest = createReportRequest();

    if (!reportRequest) {
        return;
    }

    saving.value = true;
    const result = await createReportJob(reportRequest);

    if (result.status !== 'CREATED'){
        setModal({
            mode: 'error',
            isOpen: true,
            heading: 'Scheduling failed',
            message: 'An error occurred while attempting to schedule your report.  Please contact support or try again later.',
            messages: [],
            cancelText: null,
            cancelAction: () => { },
            confirmText: 'OK',
            confirmAction: () => { saving.value = false; },
            code: 'REPORT_FAILED_MESSAGE',
        });
        return;
    }

    setModal({
        mode: 'message',
        isOpen: true,
        heading: 'Report Scheduled',
        message: 'Your report has been acknowledged and can be viewed in View My Reports.',
        messages: [],
        cancelText: 'View My Reports',
        cancelAction: () => { saving.value = false; router.push({ name: 'report-dashboard-my-reports' }); },
        confirmText: 'OK',
        confirmAction: () => { saving.value = false; },
        code: 'REPORT_SCHEDULED_MESSAGE',
    });
}

function getFilters() {
    const taVals = getTAValues();
    const statusVals = getStatusValues();
    const filters = {
        dateUsed: dateUsed.value,
        dateFrom: dateFrom.value,
        dateTo: dateTo.value,
        taValues: taVals,
        statusValues: statusVals,
        excludeConsentsByQV: excludeConsentsByQV.value
    };
    return filters;
}

function validateFilters(filters) {
    let valid = true;
    for (const prop in parameters.value) {
        validationErrors.value[prop] = null;
        const errs = validateReportField(filters[prop], parameters.value[prop], true);
        if (errs.length > 0) {
            validationErrors.value[prop] = errs.join('\n')
            valid = false;
        }
    }
    return valid;
}

function clearFilters() {
    saving.value = true;
    dateUsed.value = 1;
    dateFrom.value = '';
    dateTo.value = '';
    excludeConsentsByQV.value = 0;
    taValues.value = [],
    statusValues.value = [],
    updateDates();
    updateSelectedStatus();
    updateSelectedTAs();
    saving.value = false;
}

function getTAValues() {
    let values = []
    values = taValues.value.map(a => a.code);
    if (taValues.value.length === 1 && taValues.value[0]?.id === 0) {
        values = taList.value.map(a => a.code);
    }
    return values;
}

function getStatusValues() {
    let values = []
    values = statusValues.value.map(a => a.id);
    if (statusValues.value.length === 1 && statusValues.value[0]?.id === 0) {
        values = consentStatusList.value.map(a => a.id);
    }
    return values;
}

function createReportRequest() {
    const filters = getFilters();
    if (filters.statusValues && Array.isArray(filters.statusValues)) {
        filters.statusValues = filters.statusValues.join(',');
    }
    if (filters.taValues && Array.isArray(filters.taValues)) {
        filters.taValues = filters.taValues.join(',');
    }
    if (filters.dateFrom) {
        filters.dateFrom = formatDateToMMDDYYYY(filters.dateFrom);
    }
    if (filters.dateTo) {
        filters.dateTo = formatDateToMMDDYYYY(filters.dateTo);
    }
    if (!validateFilters(filters)) {
        return;
    }
    return {
        reportId: reportId.value,
        parameters: filters,
    }
}

function updateSelectedTAs() {
    if (taValues.value.length > 0 && taValues.value[taValues.value.length -1 ].id === 0) {
        taValues.value = [{ id: 0, code: '0', description: 'All Territorial Authorities' }];
    }
    if (taValues.value.length > 1) {
        taValues.value = taValues.value.filter(x => x.id !== 0);
    }
    if (taValues.value.length === 0) {
        taValues.value.push({ id: 0, code: '0', description: 'All Territorial Authorities' });
    }
}

function updateSelectedStatus() {
    if (statusValues.value.length > 0 && statusValues.value[statusValues.value.length -1 ].id === 0) {
        statusValues.value = [{ id: 0, code: '0', description: 'All' }];
    }
    if (statusValues.value.length > 1) {
        statusValues.value = statusValues.value.filter(x => x.id !== 0);
    }
    if (statusValues.value.length === 0) {
        statusValues.value.push({ id: 0, code: '0', description: 'All' });
    }
}

function updateDates() {
    if (moment(dateFrom).isValid() && moment(dateTo).isValid() ) {
        if (moment(dateFrom).isAfter(moment(dateTo))) {
            const tempDate = dateTo;
            dateTo = dateFrom;
            dateFrom = tempDate;
        }
    }
}

function toggleExcludeConsentsByQV() {
    excludeConsentsByQV.value = excludeConsentsByQV.value === 0 ? 1 : 0;
}

function setModal(inputModal) {
    modal.value = inputModal;
}

function modalCancel() {
    modal.value.isOpen = false;
    modal.value.cancelAction();
}

function modalConfirm() {
    modal.value.isOpen = false;
    modal.value.confirmAction();
}

</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving || modal.isOpen"
            class="page-mask"
            data-cy="report-page-mask"
        >
        </div>
        <h3>List Consents By</h3>
        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column">
                <h3>Date used is:</h3>
                <div class="qv-flex-row report-option" @click="dateUsed=0">
                    <input
                        v-model="dateUsed"
                        type="radio"
                        name="date-used"
                        value="0"
                    />
                    <span>
                        Date Issued
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="dateUsed=1">
                    <input
                        v-model="dateUsed"
                        type="radio"
                        name="date-used"
                        value="1"
                    />
                    <span>
                        Date Actioned or Deleted
                    </span>
                </div>
                <div class="qv-flex-row">
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">Start Date</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateFrom"
                            :border="true"
                        >
                            <date-picker
                                v-model="dateFrom"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                @input="updateDates"
                            />
                        </tooltip>
                    </span>
                    <span class="qv-flex-column" style="gap:0;flex:1;">
                        <label style="font-size:1.3rem;">End Date</label>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateTo"
                            :border="true"
                        >
                            <date-picker
                                v-model="dateTo"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                @input="updateDates"
                            />
                        </tooltip>
                    </span>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Territorial Authorities</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :border="true"
                    >
                        <multiselect
                            v-model="taValues"
                            :options="taList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Territorial Authorities"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            @input="updateSelectedTAs"
                        />
                    </tooltip>
                </div>
                <h3>Status</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :border="true"
                    >
                        <multiselect
                            v-model="statusValues"
                            :options="consentStatusList || []"
                            :multiple="true"
                            :close-on-select="false"
                            track-by="code"
                            label="description"
                            :preselect-first="true"
                            placeholder="Select Status"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                            @input="updateSelectedStatus"
                        />
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Exclude Consents added by QV</h3>
                <div class="qv-flex-row report-option" @click="toggleExcludeConsentsByQV">
                    <input
                        v-model="excludeConsentsByQV"
                        type="checkbox"
                        name="exclude-consents-by-QV"
                        :true-value="1"
                        :false-value="0"
                    />
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                @click="clearFilters"
                class="mdl-button mdl-button--raised"
            >
                Clear
            </button>
            <button
                @click="submitReportRequest"
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving"
                data-cy="schedule-report-button"
            >
                Schedule Report
            </button>
        </div>
        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='warning'"
            :warning="modal.mode==='error'"
            data-cy="report-modal"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode==='error', 'message-warning': modal.mode==='warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index"> - {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                        data-cy="report-modal-cancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                        data-cy="report-modal-confirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input
                id="modalResponseCode"
                type="hidden"
                :value="modal.code"
            />
        </alert-modal>
    </div>
</template>

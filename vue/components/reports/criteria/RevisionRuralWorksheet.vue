<script setup>
import { computed } from 'vue';
import ReportForm from '../ReportForm.vue';
import WorksheetUploader from './RevisionRuralWorksheetUploader.vue';
import { RURAL_WORKSHEET_UPLOAD, findReportByName, reportsIncludingHidden } from '../utils';

const uploadAllowed = computed(() => userHasUploadAccess());

function userHasUploadAccess() {
    const revisionWorksheetUploadReport = findReportByName(reportsIncludingHidden.value, RURAL_WORKSHEET_UPLOAD);
    return revisionWorksheetUploadReport != null;
}

</script>

<template>
    <div class="report-criteria qv-flex-row" style="gap: 3rem;">
        <div class="qv-flex-column" style="flex:6;">
            <h3>Download Revision Rural Worksheet</h3>
            <report-form style="flex:1;" />
        </div>
        <div
            v-if="uploadAllowed"
            class="qv-flex-column"
            style="flex:4;gap: 4.2rem"
        >
            <h3 data-cy="rw-upload-title">Upload Revision Rural Worksheet</h3>
            <worksheet-uploader style="flex: 1;" />
        </div>
    </div>
</template>

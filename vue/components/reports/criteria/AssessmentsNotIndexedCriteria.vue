<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router/composables';
import {
    currentReport,
    notifications,
    clearNotifications,
    NONE } from '../utils';
import 'vue2-datepicker/index.css';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, validateRolls, validateTerritorialAuthority } from './shared';

const saving = ref(false);
const router = useRouter();

const format = ref('PDF');

const rollType = ref('S');
const rollStart = ref(null);
const rollEnd = ref(null);
const rollSingle = ref(null);
const multipleRolls = reactive({
    multipleRolls1: null,
    multipleRolls2: null,
    multipleRolls3: null,
    multipleRolls4: null,
    multipleRolls5: null,
});
const territorialAuthority = ref(NONE);
const category = ref('');
const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null,
    territorialAuthority: null,
    category: null,
});

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: `Generate a Report for Assessments not Indexed, for a TA, category or Roll 
                \ndescription for now - business to confirm if different`,
        sticky: false,
    });
});

function getFilterValues() {
    const filters = {
        territorialAuthority: territorialAuthority.value,
        rollSingle: rollSingle.value,
        rollType: rollType.value,
        rollStart: rollStart.value,
        rollEnd: rollEnd.value,
        multipleRolls1: multipleRolls.multipleRolls1,
        multipleRolls2: multipleRolls.multipleRolls2,
        multipleRolls3: multipleRolls.multipleRolls3,
        multipleRolls4: multipleRolls.multipleRolls4,
        multipleRolls5: multipleRolls.multipleRolls5,
        category: category.value ? category.value : '*',
        format: format.value,
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    clearFilterValidations();
    rollType.value = 'S';
    rollStart.value = null;
    rollEnd.value = null;
    rollSingle.value = null;
    resetMultipleRolls();
    territorialAuthority.value = NONE;
    category.value = '';
    saving.value = false;
    format.value = 'PDF';
}

function resetMultipleRolls() { Object.keys(multipleRolls).forEach((key) => { multipleRolls[key] = ''; }); }

function clearFilterValidations() {
    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null,
        category: null,
        territorialAuthority: null,
    };
}

async function createReportRequest() {
    const filters = getFilterValues();

    clearFilterValidations();

    if (!validateFilters(filters)) {
        return;
    }

    // Set values to null if they are not used.
    if (filters.rollType === 'S') {
        filters.rollStart = filters.rollSingle ? parseInt(filters.rollSingle) : null;
        filters.rollEnd = null;
    }
    else if (filters.rollType === 'M') {
        filters.rollStart = null;
        filters.rollEnd = null;
    }
    else {
        filters.rollStart = parseInt(filters.rollStart);
        filters.rollEnd = parseInt(filters.rollEnd);
    }

    const parameters = {
        format: filters.format,
        RollType: filters.rollType,
        RollStart: filters.rollStart,
        RollEnd: filters.rollEnd,
        MultipleRolls: [
            filters.multipleRolls1,
            filters.multipleRolls2,
            filters.multipleRolls3,
            filters.multipleRolls4,
            filters.multipleRolls5,
        ]
            .filter(roll => roll !== null)
            .join(','),
        RatingAuthorityId: filters.territorialAuthority.id,
        CategoryType: filters.category,
    };

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function toggleRollType(type) {
    rollType.value = type;
    rollSingle.value = null;
    rollEnd.value = null;
    rollStart.value = null;
    multipleRolls.multipleRolls1 = null;
    multipleRolls.multipleRolls2 = null;
    multipleRolls.multipleRolls3 = null;
    multipleRolls.multipleRolls4 = null;
    multipleRolls.multipleRolls5 = null;
}

function validateFilters(filters) {
    let valid = true;

    const validRolls = validateRolls(filters);
    const validTerritorialAuthority = validateTerritorialAuthority(filters.territorialAuthority);

    if (!validRolls.isValid) {
        valid = false;
        validationErrors.value.rollSingle = rollType.value === 'S' ? validRolls.message : null;
        validationErrors.value.rangeOfRoll = rollType.value === 'R' ? validRolls.message : null;
        validationErrors.value.multipleRolls = rollType.value === 'M' ? validRolls.message : null;
    }
    if (!validTerritorialAuthority.isValid) {
        valid = false;
        validationErrors.value.territorialAuthority = validTerritorialAuthority.message;
    }

    return valid;
}

</script>

<template>
    <div class="report-criteria qv-flex-column" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask"/>
        <h3>General</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('S')">
                    <input v-model="rollType" type="radio" name="roll-type" value="S" >
                    <span>
                        Single Roll
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('R')">
                    <input v-model="rollType" type="radio" name="roll-type" value="R" >
                    <span>
                        Range of Rolls
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('M')">
                    <input v-model="rollType" type="radio" name="roll-type" value="M" >
                    <span>
                        Multiple Rolls
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rollSingle" :border="true">
                        <input v-model.number="rollSingle" type="number" :disabled="rollType !== 'S'"
                               data-cy="single-roll-input" >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rangeOfRoll" :border="true">
                        <input v-model.number="rollStart" type="number" :disabled="rollType !== 'R'" data-cy="roll-start-input">
                        <input v-model.number="rollEnd" type="number" :disabled="rollType !== 'R'" data-cy="roll-end-input">
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.multipleRolls" :border="true">
                        <input v-model.number="multipleRolls.multipleRolls1" type="number" :disabled="rollType !== 'M'" data-cy="roll-multiple-one-input">
                        <input v-model.number="multipleRolls.multipleRolls2" type="number" :disabled="rollType !== 'M'" data-cy="roll-multiple-two-input">
                        <input v-model.number="multipleRolls.multipleRolls3" type="number" :disabled="rollType !== 'M'" data-cy="roll-multiple-three-input">
                        <input v-model.number="multipleRolls.multipleRolls4" type="number" :disabled="rollType !== 'M'" data-cy="roll-multiple-four-input">
                        <input v-model.number="multipleRolls.multipleRolls5" type="number" :disabled="rollType !== 'M'" data-cy="roll-multiple-five-input">
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.5;">
                <h3>Territorial Authority</h3>
                <div>
                    <tooltip display-mode="error" :text="validationErrors.territorialAuthority" :border="true" style="width: 100%;">
                        <ta-drop-down-select v-model="territorialAuthority"/>
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 0.5">
                <h3>Category (Optional)</h3>
                <div class="qv-flex-row">
                    <tooltip display-mode="error" :text="validationErrors.category" :border="true" style="width: 100%;">
                        <input v-model="category" type="text" class="text-input" data-cy="category-input" >
                    </tooltip>
                </div>
            </div>
        </div>

        <div class="qv-flex-row" style="justify-content:end;">
            <button class="mdl-button mdl-button--raised" @click="clearFilters">
                Clear
            </button>
            <button class="mdl-button mdl-button--raised mdl-button--colored" data-cy="schedule-report-button"
                    :disabled="saving" @click="createReportRequest">
                Schedule Report
            </button>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

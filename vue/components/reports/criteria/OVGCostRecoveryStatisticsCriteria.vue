<script setup>
import { ref } from 'vue';
import ReportForm from '@/components/reports/ReportForm.vue';
import { useRoute } from 'vue-router/composables';

import {
    notifications,
    clearNotifications,
} from '../utils';

const reformFormRef = ref(null);
const route = useRoute();

function setNotification() {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate an OVG Cost Recovery Statistics report for a TA',
        sticky: false,
    });
}

function setModal(result) {
    const taSubString = "data must have required property 'ta'";
    const startDateSubString = "data must have required property 'startdate'";
    const endDateSubString = "data must have required property 'enddate'";

    if (result.message.includes(taSubString)) {
        const validTaDateMessage = 'You must enter one or more TAs to generate this report';
        result.message = result.message.replace(taSubString, validTaDateMessage);
    }
    if (result.message.includes(startDateSubString)) {
        const validStartDateMessage = 'You must enter a start date to generate this report';
        result.message = result.message.replace(startDateSubString, validStartDateMessage);
    }
    if (result.message.includes(endDateSubString)) {
        const validEndDateMessage = 'You must enter an end date to generate this report';
        result.message = result.message.replace(endDateSubString, validEndDateMessage);
    }

    const modalObj = {
        mode: 'error',
        isOpen: true,
        heading: 'Scheduling failed',
        message: result.message,
        messages: [],
        cancelText: null,
        cancelAction: () => {
        },
        confirmText: 'OK',
        confirmAction: () => {
            reformFormRef.value.setSaving(false);
        },
        code: 'REPORT_SCHEDULING_FAILED',
    };

    reformFormRef.value.setModal(modalObj);
}


function isOverwriteScenario() {
    return route.name === 'report-OVG_COST_RECOVERY_STATISTICS';
}

function alterTaSchemaProperties(jsonSchema) {
    return {
        ...jsonSchema,
        properties: {
            ...jsonSchema.properties,
            ta: {
                ...jsonSchema.properties.ta,
                type: 'array',
                items: { type: 'integer' },
                uniqueItems: true,
                minLength: 1,
            },
        },
    };
}

</script>

<template>
    <report-form
        ref="reformFormRef"
        :is-overwrite-scenario="isOverwriteScenario()"
        :alter-schema-properties="alterTaSchemaProperties"
        @overwriteModal="setModal"
        @overwriteNotification="setNotification"
    />
</template>

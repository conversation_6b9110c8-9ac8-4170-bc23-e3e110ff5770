<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
    NONE } from '../utils';
import 'vue2-datepicker/index.css';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { validateRolls, submitReportRequest, validateTerritorialAuthority, isPositiveInteger } from './shared';

const saving = ref(false);
const router = useRouter();

const PropertyTypeMapping = {
    CURRENTWORKSHEET: 'Current',
    REVISIONWORKSHEET: 'Revision',
};

const rollType = ref('S');
const rollSingle = ref(null);
const rollStart = ref(null);
const rollEnd = ref(null);
const multipleRolls = reactive({
    multipleRolls1: null,
    multipleRolls2: null,
    multipleRolls3: null,
    multipleRolls4: null,
    multipleRolls5: null,
});
const territorialAuthority = ref(NONE);
const category = ref('');
const categoryGroup = ref('');
const capitalValueStart = ref('');
const capitalValueEnd = ref('');
const productionUnitStart = ref('');
const productionUnitEnd = ref('');
const propertyType = ref(PropertyTypeMapping.CURRENTWORKSHEET);
const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null,
    territorialAuthority: null,
    category: null,
    categoryGroup: null,
    capitalValue: null,
    productionUnit: null,
});

const taStore = useTaList();
const taList = computed(() => [{ id: -1, code: '-1', description: '- None -' }].concat(taStore.value));

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate Rural Worksheets.',
        sticky: false,
    });
});


watch(territorialAuthority, () => {
    if (territorialAuthority.value.id !== -1) {
        console.log('TA selected');
        rollType.value = '';
        rollStart.value = '';
        rollEnd.value = '';
        rollSingle.value = '';
        resetMultipleRolls();
    }
    else {
        rollType.value = 'S';
    }
});

function getFilterValues() {
    const filters = {
        territorialAuthority: territorialAuthority.value,
        rollType: rollType.value,
        rollSingle: rollSingle.value,
        rollStart: rollStart.value,
        rollEnd: rollEnd.value,
        multipleRolls1: multipleRolls.multipleRolls1,
        multipleRolls2: multipleRolls.multipleRolls2,
        multipleRolls3: multipleRolls.multipleRolls3,
        multipleRolls4: multipleRolls.multipleRolls4,
        multipleRolls5: multipleRolls.multipleRolls5,
        category: category.value,
        categoryGroup: categoryGroup.value,
        capitalValueStart: capitalValueStart.value,
        capitalValueEnd: capitalValueEnd.value,
        productionUnitStart: productionUnitStart.value,
        productionUnitEnd: productionUnitEnd.value,
        propertyType: propertyType.value,
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    clearFilterValidations();
    rollType.value = 'S';
    rollStart.value = null;
    rollEnd.value = null;
    rollSingle.value = null;
    multipleRolls.multipleRolls1 = null;
    multipleRolls.multipleRolls2 = null;
    multipleRolls.multipleRolls3 = null;
    multipleRolls.multipleRolls4 = null;
    multipleRolls.multipleRolls5 = null;
    territorialAuthority.value = taList.value[0];
    category.value = '';
    categoryGroup.value = '';
    capitalValueStart.value = '';
    capitalValueEnd.value = '';
    productionUnitStart.value = '';
    productionUnitEnd.value = '';
    saving.value = false;
    propertyType.value = PropertyTypeMapping.CURRENTWORKSHEET;
}

function clearFilterValidations() {
    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null,
        territorialAuthority: null,
        category: null,
        categoryGroup: null,
        capitalValueStart: null,
        capitalValueEnd: null,
    };
}

function validateCategory(filters) {
    let valid = true;
    if (!filters.category && !filters.categoryGroup) {
        validationErrors.value.category = 'Must input either category or category group.';
        validationErrors.value.categoryGroup = 'Must input either category or category group.';
        valid = false;
    }
    else if (filters.category && filters.categoryGroup) {
        validationErrors.value.category = 'Can only input one of: category or category group';
        validationErrors.value.categoryGroup = 'Can only input one of: category or category group';
        valid = false;
    }
    return valid;
}


function validateFilters(filters) {
    let valid = true;

    const validRolls = validateRolls(filters);
    const validTerritorialAuthority = validateTerritorialAuthority(filters.territorialAuthority);
    const validCategory = validateCategory(filters);
    const validCapitalRange = validateCapitalRange(filters);

    if (!validRolls.isValid && filters.territorialAuthority.id === -1) {
        valid = false;
        validationErrors.value.rollSingle = rollType.value === 'S' ? validRolls.message : null;
        validationErrors.value.rangeOfRoll = rollType.value === 'R' ? validRolls.message : null;
        validationErrors.value.multipleRolls = rollType.value === 'M' ? validRolls.message : null;
    }
    if (!validTerritorialAuthority.isValid && !validRolls.isValid) {
        valid = false;
        validationErrors.value.territorialAuthority = validTerritorialAuthority.message;
    }

    if (valid) {
        valid = validCategory && validCapitalRange;
    }

    return valid;
}

function validateCapitalRange(filters) {
    let valid = true;
    if ((filters.capitalValueStart && !filters.capitalValueEnd) || (!filters.capitalValueStart && filters.capitalValueEnd)) {
        if (filters.capitalValueStart !== 0) {
            validationErrors.value.capitalValueStart = 'Must input both of From and To';
            validationErrors.value.capitalValueEnd = 'Must input both of From and To';
            valid = false;
        }
    }
    else if (filters.capitalValueStart && filters.capitalValueEnd) {
        if (!isPositiveInteger(filters.capitalValueStart)) {
            validationErrors.value.capitalValueStart = 'Must input a positive integer';
            valid = false;
        }
        if (!isPositiveInteger(filters.capitalValueEnd)) {
            validationErrors.value.capitalValueEnd = 'Must input a positive integer';
            valid = false;
        }
    }
    return valid;
}

async function createReportRequest() {
    const filters = getFilterValues();

    clearFilterValidations();

    if (!validateFilters(filters)) {
        return;
    }

    const parameters = {
        format: 'PDF',
        pi_roll_type: filters.rollType,
        pi_roll_number: filters.rollSingle ? filters.rollSingle : null,
        pi_roll_number_start: filters.rollStart ? filters.rollStart : null,
        pi_roll_number_end: filters.rollEnd ? filters.rollEnd : null,
        pi_roll_numbers: [
            filters.multipleRolls1,
            filters.multipleRolls2,
            filters.multipleRolls3,
            filters.multipleRolls4,
            filters.multipleRolls5,
        ]
            .filter(roll => roll !== null)
            .join(','),
        pi_ta_id: filters.territorialAuthority ? filters.territorialAuthority.id : -1,
        pi_ta_name: (() => {
            if (filters.territorialAuthority) {
                if (filters.territorialAuthority.description === '- None -') {
                    return filters.territorialAuthority.description;
                }
                return filters.territorialAuthority.description
                    .split(' - ')
                    .slice(1)
                    .join(' - ')
                    .trim();
            }
            return '';
        })(),
        pi_category: filters.category ? filters.category.replace(/\*/g, '%') : null,
        pi_category_group: filters.categoryGroup ? filters.categoryGroup.replace(/\*/g, '%') : null,
        pi_capital_value_start: filters.capitalValueStart ? filters.capitalValueStart : null,
        pi_capital_value_end: filters.capitalValueEnd ? filters.capitalValueEnd : null,
        pi_production_units_start: filters.productionUnitStart ? filters.productionUnitStart : null,
        pi_production_units_end: filters.productionUnitEnd ? filters.productionUnitEnd : null,
        pi_show_revision_values: filters.propertyType ? filters.propertyType === PropertyTypeMapping.REVISIONWORKSHEET : false,
    };

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function toggleRollType(type) {
    rollType.value = type;
    rollSingle.value = null;
    rollEnd.value = null;
    rollStart.value = null;
    resetMultipleRolls();
}

function togglePropertyType(type) {
    propertyType.value = type;
}

function resetMultipleRolls() {
    Object.keys(multipleRolls).forEach((key) => { multipleRolls[key] = null; });
}


</script>

<template>
    <div class="report-criteria qv-flex-column" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask"/>
        <h3>General</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="territorialAuthority.id === -1 ? toggleRollType('S') : null">
                    <input v-model="rollType" type="radio" name="roll-type" value="S" :disabled="territorialAuthority.id !== -1" >
                    <span>
                        Single Roll
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="territorialAuthority.id === -1 ? toggleRollType('R') : null">
                    <input v-model="rollType" type="radio" name="roll-type" value="R" :disabled="territorialAuthority.id !== -1" >
                    <span>
                        Range of Rolls
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="territorialAuthority.id === -1 ? toggleRollType('M') : null">
                    <input v-model="rollType" type="radio" name="roll-type" value="M" :disabled="territorialAuthority.id !== -1" >
                    <span>
                        Multiple Rolls
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rollSingle" :border="true">
                        <input v-model.number="rollSingle" type="number" :disabled="rollType !== 'S'"
                               data-cy="single-roll-input" >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rangeOfRoll" :border="true">
                        <input v-model.number="rollStart" type="number" :disabled="rollType !== 'R'" >
                        <input v-model.number="rollEnd" type="text" :disabled="rollType !== 'R'" >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.multipleRolls" :border="true">
                        <input v-model.number="multipleRolls.multipleRolls1" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls2" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls3" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls4" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls5" type="number" :disabled="rollType !== 'M'" >
                    </tooltip>
                </div>
            </div>
        </div>
        <h3>Or</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.5;">
                <h3>Territorial Authority</h3>
                <div>
                    <tooltip display-mode="error" :text="validationErrors.territorialAuthority" :border="true" style="width: 100%;">
                        <ta-drop-down-select v-model="territorialAuthority"/>
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Category</h3>
                <div class="qv-flex-row">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.category"
                        :border="true"
                        style="width: 100%;"
                    >
                        <input
                            v-model="category"
                            type="text"
                            class="text-input"
                            data-cy="category-input"
                        >
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Category Group</h3>
                <div class="qv-flex-row">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.categoryGroup"
                        :border="true"
                        style="width: 100%;"
                    >
                        <input
                            v-model="categoryGroup"
                            type="text"
                            class="text-input"
                        >
                    </tooltip>
                </div>
            </div>
        </div>
        <h3>Optional</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Capital Value Range</h3>
                <h3>From</h3>
                <tooltip
                    display-mode="error"
                    :text="validationErrors.capitalValueStart"
                    :border="true"
                    style="width: 100%;"
                >
                    <input
                        v-model.number="capitalValueStart"
                        type="text"
                        class="text-input"
                    >
                </tooltip>
            </div>
            <div class="qv-flex-column">
                <h3 style="visibility: hidden;">Capital Value Range</h3>
                <h3>To</h3>
                <tooltip
                    display-mode="error"
                    :text="validationErrors.capitalValueEnd"
                    :border="true"
                    style="width: 100%;"
                >
                    <input
                        v-model.number="capitalValueEnd"
                        type="text"
                        class="text-input"
                    >
                </tooltip>
            </div>
            <div class="qv-flex-column">
                <h3>Production Units</h3>
                <h3>From</h3>
                <tooltip
                    display-mode="error"
                    :text="validationErrors.productionUnitStart"
                    :border="true"
                    style="width: 100%;"
                >
                    <input
                        v-model.number="productionUnitStart"
                        type="text"
                        class="text-input"
                    >
                </tooltip>
            </div>
            <div class="qv-flex-column">
                <h3 style="visibility: hidden;">Production Unit Range</h3>
                <h3>To</h3>
                <tooltip
                    display-mode="error"
                    :text="validationErrors.productionUnitEnd"
                    :border="true"
                    style="width: 100%;"
                >
                    <input
                        v-model.number="productionUnitEnd"
                        type="text"
                        class="text-input"
                    >
                </tooltip>
            </div>
        </div>
        <h3>Report Type</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.25;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="togglePropertyType(PropertyTypeMapping.CURRENTWORKSHEET)">
                    <input
                        v-model="propertyType"
                        type="radio"
                        value="Current"
                    >
                    <span>
                        Current Worksheets
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 0.25;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="togglePropertyType(PropertyTypeMapping.REVISIONWORKSHEET)">
                    <input
                        v-model="propertyType"
                        type="radio"
                        value="Revision"
                    >
                    <span>
                        Revision Worksheets
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button class="mdl-button mdl-button--raised" @click="clearFilters">
                Clear
            </button>
            <button class="mdl-button mdl-button--raised mdl-button--colored" data-cy="schedule-report-button"
                    :disabled="saving" @click="createReportRequest">
                Schedule Report
            </button>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

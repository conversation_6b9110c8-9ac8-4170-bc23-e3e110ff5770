<script setup>
import { ref, computed, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications
} from '../utils';
import DatePicker from "vue2-datepicker";
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, allowOnlyNumberKeys, limitInputLength, filterOutNonNumericCharacters, convertToInt } from './shared'
import moment from 'moment';
import { useCategoryType } from '../../../composables/categoryTypes';
import { useCategoryGroupType } from '../../../composables/categoryGroupTypes';
import { NONE } from '../utils';

const saving = ref(false);
const router = useRouter();

const reportTypeMapping = {
    STANDARD: 0,
    RESIDENTIAL: 1,
    RURAL: 2,
    COMMERCIAL: 3
}
const reportGeneralTypeMapping = {
    FULL: 'F',
    STANDARD: 'S'
}
const rollTypeMapping = {
    SINGLE: 'S',
    RANGE: 'R',
    MULTIPLE: 'M'
}

const NULL_INT_VALUE = -1;
const STRINGIFIED_NULL_INT_VALUE = NULL_INT_VALUE.toString();

const format = ref('PDF');
const reportId = computed(() => currentReport.value?.id);

const reportGeneralType = ref(reportGeneralTypeMapping.FULL);

const rollType = ref('S');
const rollSingle = ref('');
const rollStart = ref('');
const rollEnd = ref('');
const multipleRolls = reactive({
    multipleRolls1: '',
    multipleRolls2: '',
    multipleRolls3: '',
    multipleRolls4: '',
    multipleRolls5: '',
});
const territorialAuthority = ref(NONE);
const category = ref('');
const categoryGroup = ref({ code: '' });
const dateFrom = ref('');
const dateTo = ref(moment().format('YYYY-MM-DD'));
const saleStatus = ref('');

const capitalValueFrom = ref('');
const capitalValueTo = ref('');
const netPriceFrom = ref('');
const netPriceTo = ref('');
const prodUnitFrom = ref('');
const prodUnitTo = ref('');

const analysedSalesOnly = ref(true);
const marketFreeholdSalesOnly = ref(false);

const validationErrors = ref({
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null,
    territorialAuthority: null,
    category: null,
    categoryGroup: null,
    dateFrom: null,
    dateTo: null,
    netPriceFrom: null,
    netPriceTo: null,
    capitalValueFrom: null,
    capitalValueTo: null,
    prodUnitFrom: null,
    prodUnitTo: null
});

const taStore = useTaList();
const categoryStore = useCategoryType();
const categoryGroupStore = useCategoryGroupType();

const taList = computed(() => [{ id: -1, code: '-1', description: '- None -' }].concat(taStore.value));
const categoryGroupList = computed(() => [{ code: '-1', description: '- None -', id: -1, valuationMethodId: -1 }].concat(categoryGroupStore.value.map(item => ({ ...item, description: item.code + ' - ' + item.description }))));
const saleStatusList = [
    { id: NULL_INT_VALUE, description: 'All Sales' },
    { id: 1, description: 'Exclude Unconfirmed and Pending Sales' },
    { id: 2, description: 'Unconfirmed and Pending Sales Only' }
];

watch(territorialAuthority, () => {
    if (territorialAuthority.value.id !== -1) {
        rollType.value = 'S';
        rollStart.value = '';
        rollEnd.value = '';
        rollSingle.value = '';
        resetMultipleRolls();
    }
});

watch(categoryGroup, () => {
    if (categoryGroup.value.id !== -1) {
        category.value = '';
    }
});

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate a report of Analysed Sales for a TA or Roll',
        sticky: false,
    });

    defaultCategoryGroupToNone();
});

function getFilterValues() {
    const filters = {
        format: format.value,
        reportType: reportTypeMapping.STANDARD,
        reportGeneralType: reportGeneralType.value,
        rollSingle: convertToInt(rollSingle.value, NULL_INT_VALUE),
        rollStart: convertToInt(rollStart.value, NULL_INT_VALUE),
        rollEnd: convertToInt(rollEnd.value, NULL_INT_VALUE),
        multipleRolls1: multipleRolls.multipleRolls1,
        multipleRolls2: multipleRolls.multipleRolls2,
        multipleRolls3: multipleRolls.multipleRolls3,
        multipleRolls4: multipleRolls.multipleRolls4,
        multipleRolls5: multipleRolls.multipleRolls5,
        rollType: rollType.value,
        territorialAuthority: territorialAuthority.value,
        dateFrom: dateFrom.value ? dateFrom.value : '',
        dateTo: dateTo.value ? dateTo.value : '',
        category: category.value !== '' ? category.value : STRINGIFIED_NULL_INT_VALUE,
        categoryGroup: {
            code: categoryGroup.value.code !== '' ? categoryGroup.value.code : STRINGIFIED_NULL_INT_VALUE,
        },
        saleStatus: saleStatus.value,
        capitalValueFrom: convertToInt(capitalValueFrom.value, NULL_INT_VALUE),
        capitalValueTo: convertToInt(capitalValueTo.value, NULL_INT_VALUE),
        netPriceFrom: convertToInt(netPriceFrom.value, NULL_INT_VALUE),
        netPriceTo: convertToInt(netPriceTo.value, NULL_INT_VALUE),
        prodUnitFrom: convertToInt(prodUnitFrom.value, NULL_INT_VALUE),
        prodUnitTo: convertToInt(prodUnitTo.value, NULL_INT_VALUE),
        analysedSalesOnly: analysedSalesOnly.value ? 1 : 0,
        marketSaleOnly: marketFreeholdSalesOnly.value ? 1 : 0,
    };

    return filters;
}

function clearFilters() {
    saving.value = true;
    clearFilterValidations();
    rollType.value = 'S';
    rollStart.value = '';
    rollEnd.value = '';
    rollSingle.value = '';
    resetMultipleRolls();
    territorialAuthority.value = taList.value[0];
    dateFrom.value = '';
    dateTo.value = moment().format('YYYY-MM-DD');
    category.value = '';
    categoryGroup.value = categoryGroupList.value[0];
    saleStatus.value = saleStatusList[0];
    capitalValueFrom.value = '';
    capitalValueTo.value = '';
    netPriceFrom.value = '';
    netPriceTo.value = '';
    prodUnitFrom.value = '';
    prodUnitTo.value = '';
    analysedSalesOnly.value = true;
    marketFreeholdSalesOnly.value = false;
    reportGeneralType.value = reportGeneralTypeMapping.FULL;
    saving.value = false;

    defaultCategoryGroupToNone();
}

function clearFilterValidations() {
    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null,
        territorialAuthority: null,
        category: null,
        categoryGroup: null,
        dateFrom: null,
        dateTo: null,
        netPriceFrom: null,
        netPriceTo: null,
        capitalValueFrom: null,
        capitalValueTo: null,
        prodUnitFrom: null,
        prodUnitTo: null
    };
}

function findReportTypeByCategory(category) {
    if (category === STRINGIFIED_NULL_INT_VALUE) {
        return reportTypeMapping.STANDARD;
    }
    const catCode = category.toUpperCase();
    let foundCategoryGroup = undefined;

    if (catCode.endsWith('*')) {
        const slicedCatCode = catCode.slice(0, -1);
        foundCategoryGroup = categoryStore.value.find(cat => cat.code.startsWith(slicedCatCode));
    }
    else {
        const paddedCatCode = catCode.padEnd(7, ' ');
        foundCategoryGroup = categoryStore.value.find(cat => cat.code === paddedCatCode);
    }

    if (foundCategoryGroup !== undefined) {
        return categoryGroupStore.value.find(cat => cat.id === foundCategoryGroup.categoryGroupId).valuationMethodId;
    }

    return reportTypeMapping.STANDARD;
}

function findReportTypeByCategoryGroup(inputCategoryGroup) {
    const uppercaseCategoryGroupCode = inputCategoryGroup.code.toUpperCase();

    const foundCategoryGroupCode = categoryGroupStore.value.find(
        cat => (uppercaseCategoryGroupCode.endsWith('*')
            ? cat.code.startsWith(uppercaseCategoryGroupCode.slice(0, -1))
            : cat.code === uppercaseCategoryGroupCode),
    );

    return foundCategoryGroupCode?.valuationMethodId || reportTypeMapping.STANDARD;
}

function validateFilters(filters) {
    let valid = true;

    if (filters.reportGeneralType === reportGeneralTypeMapping.FULL && filters.territorialAuthority.id !== -1) {
        valid = false;
        validationErrors.value.territorialAuthority = 'Roll number is required to run this report type. Please entered Single, Range of or Multiple Roll/s';
    }

    if (filters.category !== STRINGIFIED_NULL_INT_VALUE && filters.categoryGroup.code !== STRINGIFIED_NULL_INT_VALUE) {
        valid = false;
        validationErrors.value.category = 'Category and Category Group cannot both be selected';
        validationErrors.value.categoryGroup = 'Category and Category Group cannot both be selected';
    } else if (filters.category === STRINGIFIED_NULL_INT_VALUE && filters.categoryGroup.code === STRINGIFIED_NULL_INT_VALUE) {
        valid = false;
        validationErrors.value.category = 'Category or Category Group must be selected';
        validationErrors.value.categoryGroup = 'Category or Category Group must be selected';
    }

    if (filters.territorialAuthority.id === -1) {
        if (filters.rollType === rollTypeMapping.SINGLE && filters.rollSingle === -1) {
            valid = false;
            validationErrors.value.rollSingle = 'Single Roll must be selected';
        }
        if (filters.rollType === rollTypeMapping.RANGE) {
            if (filters.rollStart === -1 || filters.rollEnd === -1) {
                valid = false;
                validationErrors.value.rangeOfRoll = 'Range of Rolls must be selected';
            } else if (filters.rollStart > filters.rollEnd) {
                valid = false;
                validationErrors.value.rangeOfRoll = 'Start Roll must be less than End Roll';
            }
        }
        if (filters.rollType === rollTypeMapping.MULTIPLE && filters.multipleRolls1 === 0 && filters.multipleRolls2 === 0 && filters.multipleRolls3 === 0 && filters.multipleRolls4 === 0 && filters.multipleRolls5 === 0) {
            valid = false;
            validationErrors.value.multipleRolls = 'Multiple Rolls must be selected';
        }
    }

    if (filters.dateFrom === '' && filters.dateTo !== '') {
        valid = false;
        validationErrors.value.dateFrom = 'The field Sale Date From can only be used when Sale Date To is also used';
    }
    if (filters.dateTo === '' && filters.dateFrom !== '') {
        valid = false;
        validationErrors.value.dateTo = 'The field Sale Date To can only be used when Sale Date From is also used';
    }
    if (filters.dateFrom === '' && filters.dateTo === '') {
        valid = false;
        validationErrors.value.dateFrom = 'Sale Date From is required';
        validationErrors.value.dateTo = 'Sale Date To is required';
    }

    if (filters.netPriceFrom === NULL_INT_VALUE && filters.netPriceTo !== NULL_INT_VALUE) {
        valid = false;
        validationErrors.value.netPriceFrom = 'Net Price To can only be used when Net Price From is also used';
    }
    else if (filters.netPriceFrom !== NULL_INT_VALUE && filters.netPriceTo === NULL_INT_VALUE) {
        valid = false;
        validationErrors.value.netPriceTo = 'Net Price From can only be used when Net Price To is also used';
    }
    else if (filters.netPriceFrom > filters.netPriceTo) {
        valid = false;
        validationErrors.value.netPriceFrom = 'Net Price From must be less than Net Price To';
    }

    if (filters.capitalValueFrom === NULL_INT_VALUE && filters.capitalValueTo !== NULL_INT_VALUE) {
        valid = false;
        validationErrors.value.capitalValueFrom = 'Capital Value To can only be used when Capital Value From is also used';
    }
    else if (filters.capitalValueFrom !== NULL_INT_VALUE && filters.capitalValueTo === NULL_INT_VALUE) {
        valid = false;
        validationErrors.value.capitalValueTo = 'Capital Value From can only be used when Capital Value To is also used';
    }
    else if (filters.capitalValueFrom > filters.capitalValueTo) {
        valid = false;
        validationErrors.value.capitalValueFrom = 'Capital Value From must be less than Capital Value To';
    }

    if (filters.prodUnitFrom === NULL_INT_VALUE && filters.prodUnitTo !== NULL_INT_VALUE) {
        valid = false;
        validationErrors.value.prodUnitFrom = 'Production Unit To can only be used when Production Unit From is also used';
    }
    else if (filters.prodUnitFrom !== NULL_INT_VALUE && filters.prodUnitTo === NULL_INT_VALUE) {
        valid = false;
        validationErrors.value.prodUnitTo = 'Production Unit From can only be used when Production Unit To is also used';
    }
    else if (filters.prodUnitFrom > filters.prodUnitTo) {
        valid = false;
        validationErrors.value.prodUnitFrom = 'Production Unit From must be less than Production Unit To';
    }

    return valid;
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilterValues();

    clearFilterValidations();

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    if (filters.reportGeneralType === reportGeneralTypeMapping.FULL) {
        filters.reportType = filters.categoryGroup.code !== STRINGIFIED_NULL_INT_VALUE
            ? findReportTypeByCategoryGroup(filters.categoryGroup)
            : findReportTypeByCategory(filters.category);
    }

    let multipleRolls = [
        filters.multipleRolls1,
        filters.multipleRolls2,
        filters.multipleRolls3,
        filters.multipleRolls4,
        filters.multipleRolls5
    ]
        .filter(roll => roll !== '')
        .join(',');

    if (multipleRolls === '') {
        multipleRolls = '-1';
    }

    const parameters = {
        format: filters.format,
        reportType: filters.reportType,
        SingleRoll: filters.rollSingle !== NULL_INT_VALUE ? filters.rollSingle : null,
        RollStart: filters.rollStart !== NULL_INT_VALUE ? filters.rollStart : null,
        RollEnd: filters.rollEnd !== NULL_INT_VALUE ? filters.rollEnd : null,
        MultipleRolls: multipleRolls !== STRINGIFIED_NULL_INT_VALUE ? multipleRolls : null,
        RollType: filters.territorialAuthority.id !== NULL_INT_VALUE ? null : filters.rollType,
        TA: filters.territorialAuthority.id !== NULL_INT_VALUE ? filters.territorialAuthority.id : null,
        SaleDateStart: filters.dateFrom,
        SalesDateEnd: filters.dateTo,
        Category: filters.category !== STRINGIFIED_NULL_INT_VALUE ? filters.category : null,
        CategoryGroup: filters.categoryGroup.code !== STRINGIFIED_NULL_INT_VALUE ? filters.categoryGroup.code : null,
        NetPriceFrom: filters.netPriceFrom !== NULL_INT_VALUE ? filters.netPriceFrom : null,
        NetPriceTo: filters.netPriceTo !== NULL_INT_VALUE ? filters.netPriceTo : null,
        CapitalValueFrom: filters.capitalValueFrom !== NULL_INT_VALUE ? filters.capitalValueFrom : null,
        CapitalValueTo: filters.capitalValueTo !== NULL_INT_VALUE ? filters.capitalValueTo : null,
        ProdUnitFrom: filters.prodUnitFrom !== NULL_INT_VALUE ? filters.prodUnitFrom : null,
        ProdUnitTo: filters.prodUnitTo !== NULL_INT_VALUE ? filters.prodUnitTo : null,
        AnalysedOnly: filters.analysedSalesOnly,
        MarketSaleOnly: filters.marketSaleOnly,
        SaleStatus: filters.saleStatus.id !== NULL_INT_VALUE ? filters.saleStatus.id : null,
    };

    let reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function toggleReportType(type) {
    reportGeneralType.value = type;
}

function toggleAnalysedSalesOnly() {
    analysedSalesOnly.value = !analysedSalesOnly.value;
}

function toggleMarketFreeholdSalesOnly() {
    marketFreeholdSalesOnly.value = !marketFreeholdSalesOnly.value;
}

function toggleRollType(type) {
    if (territorialAuthority.value.id !== -1) {
        return;
    }
    rollType.value = type;
    rollSingle.value = '';
    rollEnd.value = '';
    rollStart.value = '';
    resetMultipleRolls();
}

function resetMultipleRolls() { Object.keys(multipleRolls).forEach(key => { multipleRolls[key] = ''; }); }

function handleKeyPress(event) {
    allowOnlyNumberKeys(event);
}

function filterNetPriceFrom(event) {
    netPriceFrom.value = filterOutNonNumericCharacters(event.target.value);
}
function filterNetPriceTo(event) {
    netPriceTo.value = filterOutNonNumericCharacters(event.target.value);
}
function filterCapitalValueFrom(event) {
    capitalValueFrom.value = filterOutNonNumericCharacters(event.target.value);
}
function filterCapitalValueTo(event) {
    capitalValueTo.value = filterOutNonNumericCharacters(event.target.value);
}
function filterProdUnitFrom(event) {
    prodUnitFrom.value = filterOutNonNumericCharacters(event.target.value);
}
function filterProdUnitTo(event) {
    prodUnitTo.value = filterOutNonNumericCharacters(event.target.value);
}

function defaultCategoryGroupToNone() {
    categoryGroup.value = { code: '' };
}

function defaultSaleStatusToNone() {
    saleStatus.value = saleStatusList[0];
}

</script>

<template>
    <div class="report-criteria qv-flex-column" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask">
        </div>
        <h3>General</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" @click="toggleRollType(rollTypeMapping.SINGLE)"
                    style="white-space: nowrap;" data-cy="single-roll-radio">
                    <input v-model="rollType" type="radio" name="roll-type" value='S'
                        :disabled="territorialAuthority.id !== -1" />
                    <span>
                        Single Roll
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType(rollTypeMapping.RANGE)"
                    style="white-space: nowrap;" data-cy="range-of-roll-radio">
                    <input v-model="rollType" type="radio" name="roll-type" value='R'
                        :disabled="territorialAuthority.id !== -1" />
                    <span>
                        Range of Rolls
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType(rollTypeMapping.MULTIPLE)"
                    style="white-space: nowrap;" data-cy="multiple-rolls-radio">
                    <input v-model="rollType" type="radio" name="roll-type" value='M'
                        :disabled="territorialAuthority.id !== -1" />
                    <span>
                        Multiple Rolls
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rollSingle" :border="true">
                        <input v-model="rollSingle" type="text"
                            :disabled="rollType !== 'S' || territorialAuthority.id !== -1"
                            data-cy="single-roll-input"
                            @keypress="handleKeyPress" />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rangeOfRoll" :border="true">
                        <input v-model="rollStart" type="text"
                            :disabled="rollType !== 'R' || territorialAuthority.id !== -1"
                            data-cy="range-of-roll-start-input"
                            @keypress="handleKeyPress" />
                        <input v-model="rollEnd" type="text"
                            :disabled="rollType !== 'R' || territorialAuthority.id !== -1"
                            data-cy="range-of-roll-end-input"
                            @keypress="handleKeyPress" />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.multipleRolls" :border="true">
                        <input style="margin-right: 0.25rem" v-for="(value, key, index) in multipleRolls" :disabled="rollType !== 'M' || territorialAuthority.id !== -1" v-model="multipleRolls[key]" :data-cy="`multiple-roll-${index + 1}-input`" @keypress="handleKeyPress"/>
                    </tooltip>
                </div>
            </div>
        </div>
        <h3>Or:</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Territorial Authorities</h4>
                    <div style="flex: 3">
                        <tooltip display-mode="error" :text="validationErrors.territorialAuthority" :border="true">
                            <ta-drop-down-select v-model="territorialAuthority"/>
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Sale Date Range</h4>
                    <div class="qv-flex-row" style="flex: 3">
                        <div style="flex: 20;">
                            <tooltip display-mode="error" :text="validationErrors.dateFrom" :border="true"
                                style="width: 100%;">
                                <DatePicker v-model="dateFrom" format="D/M/YYYY" value-type="YYYY-MM-DD"
                                    style="width: 100%;"
                                    data-cy="sale-date-from-datepicker"
                                    />
                            </tooltip>
                        </div>
                        <span style="flex: 1; align-self: center;">to</span>
                        <div style="flex: 20;">
                            <tooltip display-mode="error" :text="validationErrors.dateTo" :border="true"
                                style="width: 100%;">
                                <DatePicker v-model="dateTo" format="D/M/YYYY" value-type="YYYY-MM-DD"
                                    style="width: 100%;"
                                    data-cy="sale-date-to-datepicker"
                                    />
                            </tooltip>
                        </div>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Category</h4>
                    <div class="qv-flex-row" style="flex: 3">
                        <tooltip display-mode="error" :text="validationErrors.category" :border="true"
                            style="width: 100%;">
                            <input v-model="category" type="text" class="text-input" data-cy="category-input">
                        </tooltip>
                    </div>
                </div>

                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Category Group</h4>
                    <div class="qv-flex-row" style="flex: 3">
                        <tooltip display-mode="error" :text="validationErrors.categoryGroup" :border="true"
                                 style="width: 100%;">
                            <input v-model="categoryGroup.code" type="text" class="text-input" data-cy="category-group-input">
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1; align-self: center;">Sale Status</h4>
                    <div style="flex: 3">
                        <multiselect v-model="saleStatus" :options="saleStatusList || []" :multiple="false"
                            :close-on-select="true" track-by="id" label="description" :preselect-first="true"
                            placeholder="Select Sale Status" select-label="⏎ select" deselect-label="⏎ remove"
                            @remove="defaultSaleStatusToNone"
                            data-cy="sale-status-selector"
                            />
                    </div>
                </div>
                <h4 style="font-style: italic; margin-top: 1rem;"><span style="font-weight: bold">Pending and
                        Unconfirmed Sales</span> - An
                    unconfirmed sale is a recent sale that has not yet been formally reported. A pending sale is a
                    recent sale
                    that QV has been notified of by solicitors but not yet settled.
                    Both unconfirmed and pending sales are indicated by red italics.
                </h4>
            </div>
        </div>
        <h3>Optional:</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <div class="qv-flex-row">
                    <h4 style="flex: 14; align-self: center;">Net Price Range</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.netPriceFrom" :border="true"
                            style="width: 100%;">
                            <input v-model="netPriceFrom" type="text" class="text-input" data-cy="net-price-range-from-input"
                                @keypress="handleKeyPress" @input="filterNetPriceFrom" />
                        </tooltip>
                    </div>
                    <h4 style="flex: 1; align-self: center;">to</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.netPriceTo" :border="true"
                            style="width: 100%;">
                            <input v-model="netPriceTo" type="text" class="text-input" data-cy="net-price-range-to-input"
                                @keypress="handleKeyPress" @input="filterNetPriceTo" />
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 14; align-self: center;">Capital Value Range</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.capitalValueFrom" :border="true"
                            style="width: 100%;">
                            <input v-model="capitalValueFrom" type="text" class="text-input" data-cy="capital-value-range-from-input"
                                @keypress="handleKeyPress" @input="filterCapitalValueFrom" />
                        </tooltip>
                    </div>
                    <h4 style="flex: 1; align-self: center;">to</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.capitalValueTo" :border="true"
                            style="width: 100%;">
                            <input v-model="capitalValueTo" type="text" class="text-input" data-cy="capital-value-range-to-input"
                                @keypress="handleKeyPress" @input="filterCapitalValueTo" />
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 14; align-self: center;">Production Units:</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.prodUnitFrom" :border="true"
                            style="width: 100%;">
                            <input v-model="prodUnitFrom" type="text" class="text-input" data-cy="production-unit-from-input"
                                @keypress="handleKeyPress" @input="filterProdUnitFrom" />
                        </tooltip>
                    </div>
                    <h4 style="flex: 1; align-self: center;">to</h4>
                    <div class="qv-flex-row" style="flex: 20;">
                        <tooltip display-mode="error" :text="validationErrors.prodUnitTo" :border="true"
                            style="width: 100%;">
                            <input v-model="prodUnitTo" type="text" class="text-input" data-cy="production-unit-to-input"
                                @keypress="handleKeyPress" @input="filterProdUnitTo" />
                        </tooltip>
                    </div>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.25;">
                <div class="qv-flex-row report-option" @click="toggleAnalysedSalesOnly" style="white-space: nowrap;">
                    <input v-model="analysedSalesOnly" type="radio" value="true" data-cy="analysed-sale-only-radio" />
                    <span>
                        Analysed Sales Only
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleMarketFreeholdSalesOnly"
                    style="white-space: nowrap;">
                    <input v-model="marketFreeholdSalesOnly" type="radio" value="true" data-cy="market-freehold-sales-only-radio" />
                    <span>
                        Market Freehold Sales Only
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <h3 style="margin-top: 1rem;">Report Type</h3>
            <div class="qv-flex-row" style="flex-grow: 0.25; margin-top: 4rem;">
                <div class="qv-flex-row report-option" @click="toggleReportType(reportGeneralTypeMapping.FULL)"
                    style="white-space: nowrap;">
                    <input v-model="reportGeneralType" type="radio" value="F" data-cy="full-report-radio"/>
                    <span>
                        Full Report
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleReportType(reportGeneralTypeMapping.STANDARD)"
                    style="white-space: nowrap;">
                    <input v-model="reportGeneralType" type="radio" value="S" data-cy="standard-report-radio"/>
                    <span>
                        Standard Report
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button @click="clearFilters" class="mdl-button mdl-button--raised"
                data-cy="clear-report-button"
            >
                Clear
            </button>
            <button @click="createReportRequest" class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving" data-cy="schedule-report-button">
                Schedule Report
            </button>
        </div>
    </div>

</template>

<style lang="scss" src='../../reports/reports.scss' />

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
    formatDateToMMDDYYYY } from '../utils';
import { DateTime } from 'luxon';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, isPositiveInteger } from './shared';

const saving = ref(false);
const router = useRouter();

const CriteriaMapping = {
    ALL: 0,
    S12: 1,
};

const ratingAuthority = ref(null);
const rollFrom = ref(null);
const rollTo = ref(null);
const category = ref('');
const criteria = ref(CriteriaMapping.ALL);
const dateFrom = ref('');
const dateTo = ref('');

const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    rollFrom: null,
    rollTo: null,
    dateFrom: null,
    dateTo: null,
});

const taStore = useTaList();
const taList = computed(() => taStore.value);

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate a Cutdown OVG Sales Extracts, for a specific period.\n\nImportant Note: This report will schedule a large file size/requests, any other export requests made by users will be affected and held up this large request.\n\nWe recommend this report be requested Afterhours or towards end of day, to prevent causing issues for other users',
        sticky: false,
    });
});

function getFilters() {
    const filters = {
        ratingAuthorityId: ratingAuthority.value.id,
        rollFrom: rollFrom.value,
        rollTo: rollTo.value,
        category: category.value,
        criteria: criteria.value,
        dateFrom: dateFrom.value,
        dateTo: dateTo.value,
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    clearValidationErrors();
    ratingAuthority.value = taList.value[0];
    rollFrom.value = '';
    rollTo.value = '';
    category.value = '';
    criteria.value = CriteriaMapping.ALL;
    dateFrom.value = '';
    dateTo.value = '';
    saving.value = false;
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilters();
    if (filters.dateFrom) {
        filters.dateFrom = formatDateToMMDDYYYY(filters.dateFrom);
    }
    if (filters.dateTo) {
        filters.dateTo = formatDateToMMDDYYYY(filters.dateTo);
    }

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    const parameters = {
        taId: filters.ratingAuthorityId,
        rollFrom: filters.rollFrom ? filters.rollFrom : null,
        rollTo: filters.rollTo ? filters.rollTo : null,
        category: filters.category ? filters.category : '',
        s12SalesOnly: filters.criteria,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo,
    };

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function clearValidationErrors() {
    validationErrors.value = {
        rollFrom: null,
        rollTo: null,
        dateFrom: null,
        dateTo: null,
    };
}

function validateFilters(filters) {
    let valid = true;

    clearValidationErrors();

    if (filters.rollFrom && !isPositiveInteger(filters.rollFrom)) {
        validationErrors.value.rollFrom = 'Must input a positive integer';
        valid = false;
    }

    if (filters.rollTo && !isPositiveInteger(filters.rollTo)) {
        validationErrors.value.rollTo = 'Must input a positive integer';
        valid = false;
    }

    if (!filters.dateFrom) {
        validationErrors.value.dateFrom = 'Must input a start date';
        valid = false;
    }

    if (!filters.dateTo) {
        validationErrors.value.dateTo = 'Must input an end date';
        valid = false;
    }

    return valid;
}

function updateDates() {
    const start = DateTime.fromISO(dateFrom.value);
    const end = DateTime.fromISO(dateTo.value);
    if (start.isValid && end.isValid) {
        if (start > end) {
            const tempDate = dateTo.value;
            dateTo.value = dateFrom.value;
            dateFrom.value = tempDate;
        }
    }
}

</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving"
            class="page-mask"
            data-cy="report-page-mask"
        />
        <h3>Get Results By</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Territorial Authorities</h3>
                <div>
                    <ta-drop-down-select v-model="ratingAuthority" :allow-empty="false"/>
                </div>
                <h3>Roll From</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rollFrom"
                        :border="true"
                    >
                        <input
                            v-model.number="rollFrom"
                            type="text"
                            class="text-input"
                        >
                    </tooltip>
                </div>
                <div class="qv-flex-row" style="justify-content: space-between;">
                    <div class="qv-flex-column">
                        <h3>Sale Date From</h3>
                        <div class="qv-flex-row">
                            <tooltip
                                display-mode="error"
                                :text="validationErrors.dateFrom"
                                :border="true"
                            >
                                <date-picker
                                    v-model="dateFrom"
                                    class="report-datepicker"
                                    type="date"
                                    value-type="YYYY-MM-DD"
                                    format="D/M/YYYY"
                                    data-cy="date-from"
                                    @input="updateDates"
                                />
                            </tooltip>
                        </div>
                    </div>
                    <div class="qv-flex-column">
                        <h3>Sale Date To</h3>
                        <div class="qv-flex-row">
                            <tooltip
                                display-mode="error"
                                :text="validationErrors.dateTo"
                                :border="true"
                            >
                                <date-picker
                                    v-model="dateTo"
                                    class="report-datepicker"
                                    type="date"
                                    format="D/M/YYYY"
                                    data-cy="date-to"
                                    value-type="YYYY-MM-DD"
                                    @input="updateDates"
                                />
                            </tooltip>
                        </div>
                    </div>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Category</h3>
                <div>
                    <input
                        v-model="category"
                        type="text"
                        class="text-input"
                    >
                </div>
                <h3>Roll To</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rollTo"
                        :border="true"
                    >
                        <input
                            v-model.number="rollTo"
                            type="text"
                            class="text-input"
                        >
                    </tooltip>
                </div>
                <h3>Criteria</h3>
                <div class="qv-flex-row">
                    <div class="qv-flex-column">
                        <div class="qv-flex-row">
                            <input
                                v-model.number="criteria"
                                type="radio"
                                value="0"
                            >
                            <span>
                                All Sales Classifications
                            </span>
                        </div>
                    </div>
                    <div class="qv-flex-column">
                        <div class="qv-flex-row">
                            <input
                                v-model.number="criteria"
                                type="radio"
                                value="1"
                            >
                            <span>
                                S12 Sales Only
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end; margin-top: 5rem">
            <button
                class="mdl-button mdl-button--raised"
                @click="clearFilters"
            >
                Clear
            </button>
            <button
                :disabled="saving"
                class="mdl-button mdl-button--raised mdl-button--colored"
                data-cy="schedule-report-button"
                @click="createReportRequest"
            >
                Schedule Report
            </button>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router/composables';
import moment from 'moment';
import {
    currentReport,
    notifications,
    clearNotifications,
    NONE } from '../utils';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import Multiselect from 'vue-multiselect';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, isPositiveInteger, allowOnlyNumberKeys, limitInputLength, filterOutNonNumericCharacters, validateTerritorialAuthority } from './shared';
import { useSalesGroups } from '@/composables/salesGroups';

const saving = ref(false);
const router = useRouter();
const salesGroups = useSalesGroups();

const ReportType = {
    SALES_GROUPS: 0,
    ROLLS: 1,
    TERRITORIAL_AUTHORITY: 2,
};

const ReportFormat = {
    PDF: 'PDF',
    EXCEL: 'EXCEL',
};

const SortOrder = {
    AgreementDateDESC: 'D',
    StreetAddressASC: 'A',
};

const reportTypeList = [
    { id: 0, description: 'Sales Groups' },
    { id: 1, description: 'Rolls' },
    { id: 2, description: 'Territorial Authority' },
];

const saleTypeList = [
    { id: -1, description: 'All' },
    { id: 1, description: 'M Multi' },
    { id: 2, description: 'N Not classified' },
    { id: 3, description: 'O Other' },
    { id: 4, description: 'P part' },
    { id: 5, description: 'S Whole' },
];

const saleTenureList = [
    { id: -1, description: 'All' },
    { id: 1, description: '1 Freehold' },
    { id: 2, description: '2 Leasehold' },
    { id: 3, description: '3 Part interest' },
    { id: 4, description: '4 Other' },
    { id: 5, description: '5 Not classified' },
];

const priceValueRelationshipList = [
    { id: -1, description: 'All' },
    { id: 1, description: '1 Market-level' },
    { id: 2, description: '2 Market-interim' },
    { id: 3, description: '3 Non-market-level' },
    { id: 4, description: '4 Not classified' },
];

const saleStatusList = [
    { id: -1, description: 'All Sales' },
    { id: 1, description: 'Exclude Unconfirmed & Pending Sales' },
    { id: 2, description: 'Unconfirmed & Pending Sales Only' },
];

const territorialAuthority = ref(NONE);
const rollNumber = ref(null);
const salesGrp = ref(null);
const category = ref('');
const saleStart = ref('');
const saleEnd = ref('');
const selectedReportFormat = ref(ReportFormat.PDF);
const selectedSortOrder = ref(SortOrder.AgreementDateDESC);
const saleType = ref(saleTypeList[0]);
const saleTenure = ref(saleTenureList[0]);
const priceValueRelationship = ref(priceValueRelationshipList[0]);
const saleStatus = ref(saleStatusList[0]);

const validationErrors = ref({
    taError: null,
    salesGroupError: null,
    categoryError: null,
    saleStartError: null,
    saleEnd: null,
});

const reportId = computed(() => currentReport.value?.id);
const salesGroupsLoaded = computed(() => salesGroups.value && salesGroups.value.length > 0);
const matchingSalesGroupObj = computed(() => {
    const matchingTASalesGroups = salesGroups.value.filter(
        sg => parseInt(sg.taCode) === parseInt(territorialAuthority.value.code),
    );
    return matchingTASalesGroups.find(
        sg => sg.salesGroupNumber === salesGrp.value,
    );
});

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate a Sales Listing of either Sales Groups, Rolls or Territorial Authorities.',
        sticky: false,
    });
});

function getReportType() {
    if (rollNumber.value) {
        return reportTypeList[ReportType.ROLLS];
    }
    if (salesGrp.value && isPositiveInteger(territorialAuthority.value.id)) {
        return reportTypeList[ReportType.SALES_GROUPS];
    }
    return reportTypeList[ReportType.TERRITORIAL_AUTHORITY];
}

function getFilterValues() {
    const filters = {
        format: selectedReportFormat.value,
        reportType: getReportType(),
        territorialAuthority: territorialAuthority.value,
        rollNumber: rollNumber.value,
        salesGrp: salesGrp.value,
        matchingSalesGroupObj: matchingSalesGroupObj.value,
        category: category.value,
        saleStart: saleStart.value,
        saleEnd: saleEnd.value,
        sortby: selectedSortOrder.value,
        salesType: saleType.value,
        salesTenure: saleTenure.value,
        salesPriceValRel: priceValueRelationship.value,
        saleStatus: saleStatus.value,
    };
    return filters;
}

function updateDates() {
    if (moment(saleStart.value).isValid() && moment(saleEnd.value).isValid()) {
        if (moment(saleStart.value).isAfter(moment(saleEnd.value))) {
            const tempDate = saleEnd.value;
            saleEnd.value = saleStart.value;
            saleStart.value = tempDate;
        }
    }
}

function clearFilters() {
    saving.value = true;
    territorialAuthority.value = NONE;
    rollNumber.value = '';
    salesGrp.value = '';
    category.value = '';
    saleStart.value = '';
    saleEnd.value = '';
    toggleSortOrder(SortOrder.AgreementDateDESC);
    toggleReportFormat(ReportFormat.PDF);
    saleType.value = saleTypeList[0];
    saleTenure.value = saleTenureList[0];
    priceValueRelationship.value = priceValueRelationshipList[0];
    saleStatus.value = saleStatusList[0];

    saving.value = false;
}

function clearFilterValidations() {
    validationErrors.value = {
        taError: null,
        salesGroupError: null,
        categoryError: null,
        saleStartError: null,
        saleEnd: null,
    };
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilterValues();

    clearFilterValidations();

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    const parameters = {
        format: filters.format,
        reportType: filters.reportType.id,
        Category: filters.category,
        SaleStatus: filters.saleStatus.id,
        SaleStart: filters.saleStart,
        SaleEnd: filters.saleEnd,
        SalesType: filters.salesType.id,
        SalesTenure: filters.salesTenure.id,
        SalesPriceValRel: filters.salesPriceValRel.id,
        sortby: filters.sortby,
    };

    if (filters.reportType.id === ReportType.ROLLS) {
        parameters.RollNumber = parseInt(filters.rollNumber);
    }
    else if (filters.reportType.id === ReportType.SALES_GROUPS) {
        parameters.SalesGrp = filters.matchingSalesGroupObj.saleGroupId;
        parameters.RatingAuthorityId = filters.territorialAuthority.id;
    }
    else {
        parameters.RatingAuthorityId = filters.territorialAuthority.id;
    }

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function validateFilters(filters) {
    let valid = true;

    const validTerritorialAuthority = validateTerritorialAuthority(filters.territorialAuthority);

    if (filters.reportType.id === ReportType.TERRITORIAL_AUTHORITY && !validTerritorialAuthority.isValid) {
        valid = false;
        validationErrors.value.taError = validTerritorialAuthority.message;
    }

    if (filters.category === '') {
        valid = false;
        validationErrors.value.categoryError = 'Category must be entered';
    }
    if (filters.saleStart === '') {
        valid = false;
        validationErrors.value.saleStartError = 'Sale Start Date must be entered';
    }
    if (filters.saleEnd === '') {
        valid = false;
        validationErrors.value.saleEnd = 'Sale End Date must be entered';
    }

    if (filters.reportType.id === ReportType.SALES_GROUPS && !filters.matchingSalesGroupObj) {
        valid = false;
        validationErrors.value.salesGroupError = salesGroupsLoaded.value
            ? `Sales Group ${filters.salesGrp} not found in TA Code ${filters.territorialAuthority.code}`
            : 'Sales Groups not loaded';
    }

    return valid;
}

function toggleReportFormat(type) {
    selectedReportFormat.value = type;
}

function toggleSortOrder(type) {
    selectedSortOrder.value = type;
}

function filterRollNumberFormat(event) {
    rollNumber.value = filterOutNonNumericCharacters(event.target.value);
}

function filterSalesGroupFormat(event) {
    const input = filterOutNonNumericCharacters(event.target.value);
    salesGrp.value = limitInputLength(input, 4);
}

function filterCategoryFormat(event) {
    category.value = limitInputLength(event.target.value, 8);
}

function handleKeyPress(event) {
    allowOnlyNumberKeys(event);
}

function defaultSaleType() {
    saleType.value = saleTypeList[0];
}
function defaultSaleTenure() {
    saleTenure.value = saleTenureList[0];
}
function defaultPriceValueRelationship() {
    priceValueRelationship.value = priceValueRelationshipList[0];
}
function defaultSaleStatus() {
    saleStatus.value = saleStatusList[0];
}

</script>
<template>
    <div class="report-criteria qv-flex-column" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask"/>
        <h3>List Sales By:</h3>

        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <div class="qv-flex-column" style="justify-content: space-evenly;">
                    <div class="qv-flex-row">
                        <span style="flex: 1;">
                            Territorial Authorities
                        </span>
                        <div style="flex: 3;">
                            <tooltip display-mode="error" :text="validationErrors.taError" :border="true">
                                <ta-drop-down-select v-model="territorialAuthority"/>
                            </tooltip>
                        </div>
                    </div>
                    <div class="qv-flex-row">
                        <span style="flex: 1">
                            Roll Number
                        </span>
                        <div style="flex: 3">
                            <input
                                v-model.number="rollNumber"
                                type="text"
                                class="text-input"
                                placeholder="Roll number (Single roll number only. Number values only)"
                                data-cy="roll-number-input"
                                @input="filterRollNumberFormat"
                                @keypress="handleKeyPress"
                            >
                        </div>
                    </div>
                    <div class="qv-flex-row">
                        <span style="flex: 1">
                            Sales Group
                        </span>
                        <div style="flex: 3">
                            <tooltip display-mode="error" :text="validationErrors.salesGroupError" :border="true">
                                <input
                                    v-model.number="salesGrp"
                                    type="text"
                                    class="text-input"
                                    placeholder="Sale Groups (Single sales group only. 4 character long limit, number values only)"
                                    data-cy="sales-group-input"
                                    @input="filterSalesGroupFormat"
                                    @keypress="handleKeyPress"
                                >
                            </tooltip>
                        </div>
                    </div>
                    <div class="qv-flex-row">
                        <span style="flex: 1">
                            Category
                        </span>
                        <div style="flex: 3">
                            <tooltip display-mode="error" :text="validationErrors.categoryError" :border="true">
                                <input
                                    v-model="category"
                                    type="text"
                                    class="text-input"
                                    placeholder="Category (8 character long limit, wildcard * accepted)"
                                    data-cy="category-input"
                                    @input="filterCategoryFormat"
                                >
                            </tooltip>
                        </div>
                    </div>
                </div>
                <div class="qv-flex-row" style="padding:1rem 0rem">
                    <h4 style="flex: 21">Sale Start Date</h4>
                    <div style="flex:20; display: flex;">
                        <tooltip display-mode="error" :text="validationErrors.saleStartError" :border="true">
                            <date-picker
                                v-model="saleStart"
                                type="date"
                                class="report-datepicker"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                data-cy="sale-start-date-datepicker"
                                @input="updateDates"
                            />
                        </tooltip>
                    </div>
                    <h4 style="flex: 21">Sale End Date</h4>
                    <div style="flex:20; display: flex;">
                        <tooltip display-mode="error" :text="validationErrors.saleEnd" :border="true">
                            <date-picker
                                v-model="saleEnd"
                                type="date"
                                class="report-datepicker"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                data-cy="sale-end-date-datepicker"
                                @input="updateDates"
                            />
                        </tooltip>
                    </div>
                </div>
                <div class="qv-flex-row" style="justify-content: flex-start;">
                    <h4 style="justify-self: center; flex: 1;">Sort Order</h4>
                    <div class="qv-flex-column" style="flex: 3;">
                        <div class="qv-flex-row report-option" style="white-space: nowrap;"
                             @click="toggleSortOrder(SortOrder.AgreementDateDESC)">
                            <input v-model="selectedSortOrder" type="radio" value="D" >
                            <span>
                                Agreement Date (descending)
                            </span>
                        </div>
                        <div class="qv-flex-row report-option" style="white-space: nowrap;"
                             @click="toggleSortOrder(SortOrder.StreetAddressASC)">
                            <input v-model="selectedSortOrder" type="radio" value="A" >
                            <span>
                                Street Address (ascending)
                            </span>
                        </div>
                    </div>
                </div>
                <div class="qv-flex-row" style="justify-content: flex-start">
                    <h4 style="flex: 1">Report Format</h4>
                    <div class="qv-flex-column" style="flex: 3;">
                        <div class="qv-flex-row report-option" style="white-space: nowrap;"
                             @click="toggleReportFormat(ReportFormat.PDF)">
                            <input v-model="selectedReportFormat" type="radio" value="PDF" >
                            <span>
                                PDF
                            </span>
                        </div>
                        <div class="qv-flex-row report-option" style="white-space: nowrap;"
                             @click="toggleReportFormat(ReportFormat.EXCEL)">
                            <input v-model="selectedReportFormat" type="radio" value="EXCEL" >
                            <span>
                                EXCEL
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <hr >

        <h3>With Classification of:</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <div class="qv-flex-row">
                    <h4 style="flex: 1">Sale Type</h4>
                    <div style="flex: 3">
                        <multiselect v-model="saleType" :options="saleTypeList" :multiple="false"
                                     :close-on-select="true" track-by="id" label="description" :preselect-first="true"
                                     placeholder="Select Source" select-label="⏎ select" deselect-label="⏎ remove"
                                     @remove="defaultSaleType" />
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1">Sale Tenure</h4>
                    <div style="flex: 3">
                        <multiselect v-model="saleTenure" :options="saleTenureList" :multiple="false"
                                     :close-on-select="true" track-by="id" label="description" :preselect-first="true"
                                     placeholder="Select Source" select-label="⏎ select" deselect-label="⏎ remove"
                                     @remove="defaultSaleTenure" />
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1">Price Value Relationship</h4>
                    <div style="flex: 3">
                        <multiselect v-model="priceValueRelationship" :options="priceValueRelationshipList"
                                     :multiple="false" :close-on-select="true" track-by="id" label="description"
                                     :preselect-first="true" placeholder="Select Source" select-label="⏎ select"
                                     deselect-label="⏎ remove" @remove="defaultPriceValueRelationship" />
                    </div>
                </div>
                <div class="qv-flex-row">
                    <h4 style="flex: 1">Sale Status</h4>
                    <div style="flex: 3">
                        <multiselect v-model="saleStatus" :options="saleStatusList" :multiple="false"
                                     :close-on-select="true" track-by="id" label="description" :preselect-first="true"
                                     placeholder="Select Source" select-label="⏎ select" deselect-label="⏎ remove"
                                     @remove="defaultSaleStatus"
                        />
                    </div>
                </div>
            </div>
        </div>
        <h4 style="font-style: italic; color: grey">
            Pending and Unconfirmed Sales - An unconfirmed sale is a recent sale
            that has not yet been formally
            reported. A pending sale is a recent sale that QV has been notified of by solicitors but not yet settled.
            Both unconfirmed and pending sales are indicated by red italics.
        </h4>
        <div class="qv-flex-row" style="justify-content:end;">
            <button class="mdl-button mdl-button--raised" data-cy="clear-report-button" @click="clearFilters">
                Clear
            </button>
            <button class="mdl-button mdl-button--raised mdl-button--colored" :disabled="saving"
                    data-cy="schedule-report-button" @click="createReportRequest">
                Schedule Report
            </button>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

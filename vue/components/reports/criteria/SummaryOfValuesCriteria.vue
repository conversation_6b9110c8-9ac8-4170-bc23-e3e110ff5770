<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
    NONE } from '../utils';
import 'vue2-datepicker/index.css';
import Multiselect from 'vue-multiselect';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, validateRolls, validateTerritorialAuthority } from './shared';
import DatePicker from 'vue2-datepicker';
import store from '../../../store/classifications';

const saving = ref(false);
const router = useRouter();

const format = ref('PDF');
const rollType = ref('S');
const rollStart = ref(null);
const rollEnd = ref(null);
const rollSingle = ref(null);
const multipleRolls = reactive({
    multipleRolls1: null,
    multipleRolls2: null,
    multipleRolls3: null,
    multipleRolls4: null,
    multipleRolls5: null,
});
const territorialAuthority = ref(NONE);
const region = ref('');
const closingDate = ref(null);
const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null,
    territorialAuthority: null,
    regions: null,
});

const taStore = useTaList();
const taList = computed(() => [{ id: -1, code: '-1', description: '- None -' }].concat(taStore.value));
const regionList = computed(() => [{ code: '-1', description: '- None -' }].concat(store.state.classifications.RegionType));

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate a Summary of Values for a TA, Region or Roll',
        sticky: false,
    });
});

function getFilterValues() {
    const filters = {
        rollType: rollType.value,
        rollSingle: rollSingle.value,
        rollStart: rollStart.value,
        rollEnd: rollEnd.value,
        multipleRolls1: multipleRolls.multipleRolls1,
        multipleRolls2: multipleRolls.multipleRolls2,
        multipleRolls3: multipleRolls.multipleRolls3,
        multipleRolls4: multipleRolls.multipleRolls4,
        multipleRolls5: multipleRolls.multipleRolls5,
        territorialAuthority: territorialAuthority.value,
        region: region.value,
        format: format.value,
        closingDate: closingDate.value,
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    clearFilterValidations();
    rollType.value = 'S';
    rollStart.value = null;
    rollEnd.value = null;
    rollSingle.value = null;
    resetMultipleRolls();
    territorialAuthority.value = taList.value[0];
    region.value = regionList.value[0];
    closingDate.value = null;
    saving.value = false;
}

function clearFilterValidations() {
    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null,
        region: null,
        territorialAuthority: null,
    };
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilterValues();

    clearFilterValidations();

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    // Set values to -1 if they are not used.
    if (filters.rollType === 'S') {
        filters.rollStart = filters.rollSingle ? filters.rollSingle : -1;
        filters.rollEnd = -1;
    }
    else if (filters.rollType === 'M') {
        filters.rollStart = -1;
        filters.rollEnd = -1;
    }
    else {
        filters.rollStart = filters.rollStart;
        filters.rollEnd = filters.rollEnd;
    }


    const parameters = {
        format: filters.format,
        RollType: filters.rollType,
        RollStart: filters.rollStart,
        RollEnd: filters.rollEnd,
        RollMultiple: [
            filters.multipleRolls1,
            filters.multipleRolls2,
            filters.multipleRolls3,
            filters.multipleRolls4,
            filters.multipleRolls5,
        ]
            .filter(roll => roll !== null)
            .join(','),
        RAId: filters.territorialAuthority.id,
        RegionId: parseInt(filters.region.code),
        ClosingDate: filters.closingDate,
    };

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function validateFilters(filters) {
    let valid = true;

    const validRolls = validateRolls(filters);
    const validTerritorialAuthority = validateTerritorialAuthority(filters.territorialAuthority);
    const validRegion = validateRegion(filters.region);

    if (!closingDate.value) {
        valid = false;
        validationErrors.value.closingDate = 'Closing Date is required';
    }

    if (!validTerritorialAuthority.isValid && !validRegion) {
        valid = false;
        validationErrors.value.territorialAuthority = 'Territorial Authority or Region is required';
        validationErrors.value.regions = 'Territorial Authority or Region is required';
    }
    else if (validTerritorialAuthority.isValid && validRegion) {
        valid = false;
        validationErrors.value.territorialAuthority = 'Can Only Select One of Territorial Authority or Region';
        validationErrors.value.regions = 'Can Only Select One of Territorial Authority or Region';
    }

    if (!validRolls.isValid) {
        valid = false;
        validationErrors.value.rollSingle = rollType.value === 'S' ? validRolls.message : '';
        validationErrors.value.rangeOfRoll = rollType.value === 'R' ? validRolls.message : '';
        validationErrors.value.multipleRolls = rollType.value === 'M' ? validRolls.message : '';
    }
    return valid;
}

function validateRegion(selectedRegion) {
    if (selectedRegion.code !== '-1') {
        return true;
    }
    return false;
}

function toggleRollType(type) {
    rollType.value = type;
    rollEnd.value = null;
    rollStart.value = null;
    rollSingle.value = null;
    resetMultipleRolls();
}

function resetMultipleRolls() { Object.keys(multipleRolls).forEach((key) => { multipleRolls[key] = null; }); }

function defaultRegionToNone() {
    region.value = regionList.value[0];
}

</script>

<template>
    <div class="report-criteria qv-flex-column" data-cy="report-criteria">
        <div v-if="saving" class="page-mask" data-cy="report-page-mask"/>
        <h3>General</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('S')">
                    <input v-model="rollType" type="radio" name="roll-type" value="S">
                    <span>
                        Single Roll
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('R')">
                    <input v-model="rollType" type="radio" name="roll-type" value="R">
                    <span>
                        Range of Rolls
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('M')">
                    <input v-model="rollType" type="radio" name="roll-type" value="M">
                    <span>
                        Multiple Rolls
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rollSingle" :border="true">
                        <input v-model.number="rollSingle" type="number" :disabled="rollType !== 'S'"
                               data-cy="single-roll-input" >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.rangeOfRoll" :border="true">
                        <input v-model.number="rollStart" type="number" :disabled="rollType !== 'R'" >
                        <input v-model.number="rollEnd" type="number" :disabled="rollType !== 'R'" >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip display-mode="error" :text="validationErrors.multipleRolls" :border="true">
                        <input v-model.number="multipleRolls.multipleRolls1" type="number" :disabled="rollType !== 'M'">
                        <input v-model.number="multipleRolls.multipleRolls2" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls3" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls4" type="number" :disabled="rollType !== 'M'" >
                        <input v-model.number="multipleRolls.multipleRolls5" type="number" :disabled="rollType !== 'M'" >
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <h3>Closing Date</h3>
            <tooltip display-mode="error" :text="validationErrors.closingDate" :border="true">
                <date-picker
                    v-model="closingDate"
                    class="report-datepicker"
                    type="date" format="D/M/YYYY"
                    data-cy="closing-date"
                    value-type="YYYY-MM-DD" />
            </tooltip>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.5;">
                <h3>Territorial Authorities</h3>
                <div>
                    <tooltip display-mode="error" :text="validationErrors.territorialAuthority" :border="true" style="width: 100%;">
                        <ta-drop-down-select v-model="territorialAuthority"/>
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 0.5;">
                <h3>Regions</h3>
                <div>
                    <tooltip display-mode="error" :text="validationErrors.regions" :border="true" style="width: 100%;">
                        <multiselect v-model="region"
                                     :options="regionList || []"
                                     :multiple="false"
                                     :close-on-select="true"
                                     track-by="code"
                                     label="description"
                                     :preselect-first="true"
                                     placeholder="Select Region"
                                     select-label="⏎ select"
                                     deselect-label="⏎ remove"
                                     @remove="defaultRegionToNone"/>
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Report Format</h3>
                <div class="qv-flex-row report-option" @click="format = 'EXCEL'">
                    <input v-model="format" type="radio" name="format" value="EXCEL" >
                    <span>
                        Excel
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="format = 'PDF'">
                    <input v-model="format" type="radio" name="format" value="PDF" >
                    <span>
                        PDF
                    </span>
                </div>
            </div>
            <div class="qv-flex-row" style="justify-content:end;">
                <button class="mdl-button mdl-button--raised" @click="clearFilters">
                    Clear
                </button>
                <button class="mdl-button mdl-button--raised mdl-button--colored" :disabled="saving"
                        data-cy="schedule-report-button" @click="createReportRequest">
                    Schedule Report
                </button>
            </div>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
} from '../utils';
import 'vue2-datepicker/index.css';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, isPositiveInteger } from './shared';

const saving = ref(false);
const router = useRouter();

const IncludePostalMapping = {
    YES: 1,
    NO: 0,
};

const CriteriaMapping = {
    ALL: 0,
    MAORI: 1,
    SUSPECT: 2,
    RURAL: 3,
    COMMERCIAL: 4,
};

const ratingAuthority = ref(null);
const rollFrom = ref(null);
const rollTo = ref(null);
const category = ref('');
const includePostal = ref(IncludePostalMapping.YES);
const criteria = ref(CriteriaMapping.ALL);

const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    rollFrom: null,
    rollTo: null,
});

const taStore = useTaList();
const taList = computed(() => taStore.value);

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate a Cutdown OVG Roll Extracts, for a specific period.\n\nImportant Note: This report will schedule a large file size/requests, any other export requests made by users will be affected and held up this large request.\n\nWe recommend this report be requested Afterhours or towards end of day, to prevent causing issues for other users',
        sticky: false,
    });
});

function getFilters() {
    const filters = {
        ratingAuthorityId: ratingAuthority.value.id,
        rollFrom: rollFrom.value,
        rollTo: rollTo.value,
        category: category.value,
        includePostal: includePostal.value,
        criteria: criteria.value,
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    validationErrors.value = {
        rollFrom: null,
        rollTo: null,
    };
    ratingAuthority.value = taList.value[0];
    rollFrom.value = null;
    rollTo.value = null;
    category.value = '';
    includePostal.value = IncludePostalMapping.YES;
    criteria.value = CriteriaMapping.ALL;
    saving.value = false;
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilters();

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    const parameters = {
        taId: filters.ratingAuthorityId,
        rollFrom: filters.rollFrom ? filters.rollFrom : null,
        rollTo: filters.rollTo ? filters.rollTo : null,
        category: filters.category ? filters.category : '',
        includePostal: filters.includePostal,
        criteria: filters.criteria,
    };

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function validateFilters(filters) {
    let valid = true;

    validationErrors.value = {
        rollFrom: null,
        rollTo: null,
    };

    if (filters.rollFrom && !isPositiveInteger(filters.rollFrom)) {
        validationErrors.value.rollFrom = 'Must input a positive integer';
        valid = false;
    }

    if (filters.rollTo && !isPositiveInteger(filters.rollTo)) {
        validationErrors.value.rollTo = 'Must input a positive integer';
        valid = false;
    }

    return valid;
}
</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving"
            class="page-mask"
            data-cy="report-page-mask"
        />
        <h3>Get Results By</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Territorial Authorities</h3>
                <div>
                    <ta-drop-down-select v-model="ratingAuthority" :allow-empty="false"/>
                </div>
                <h3>Roll From</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rollFrom"
                        :border="true"
                    >
                        <input
                            v-model.number="rollFrom"
                            type="text"
                            class="text-input"
                        >
                    </tooltip>
                </div>
                <h3>Criteria</h3>
                <div class="qv-flex-row">
                    <input
                        v-model.number="criteria"
                        type="radio"
                        value="0"
                    >
                    <span>
                        All Assessments
                    </span>
                </div>
                <div class="qv-flex-row">
                    <input
                        v-model.number="criteria"
                        type="radio"
                        value="1"
                    >
                    <span>
                        Maori Land
                    </span>
                </div>
                <div class="qv-flex-row">
                    <input
                        v-model.number="criteria"
                        type="radio"
                        value="2"
                    >
                    <span>
                        Suspect Valuations
                    </span>
                </div>
                <div class="qv-flex-row">
                    <input
                        v-model.number="criteria"
                        type="radio"
                        value="3"
                    >
                    <span>
                        Rural (D, P, H, S)
                    </span>
                </div>
                <div class="qv-flex-row">
                    <input
                        v-model.number="criteria"
                        type="radio"
                        value="4"
                    >
                    <span>
                        Commercial/Industrial (C & I)
                    </span>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Category</h3>
                <div>
                    <input
                        v-model="category"
                        type="text"
                        class="text-input"
                    >
                </div>
                <h3>Roll To</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rollTo"
                        :border="true"
                    >
                        <input
                            v-model.number="rollTo"
                            type="text"
                            class="text-input"
                        >
                    </tooltip>
                </div>
                <h3>Include Postal</h3>
                <div class="qv-flex-row">
                    <div class="qv-flex-column">
                        <div class="qv-flex-row">
                            <input
                                v-model.number="includePostal"
                                type="radio"
                                value="1"
                            >
                            <span>
                                Yes
                            </span>
                        </div>
                    </div>
                    <div class="qv-flex-column">
                        <div class="qv-flex-row">
                            <input
                                v-model.number="includePostal"
                                type="radio"
                                value="0"
                            >
                            <span>
                                No
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                class="mdl-button mdl-button--raised"
                @click="clearFilters"
            >
                Clear
            </button>
            <button
                :disabled="saving"
                class="mdl-button mdl-button--raised mdl-button--colored"
                data-cy="schedule-report-button"
                @click="createReportRequest"
            >
                Schedule Report
            </button>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

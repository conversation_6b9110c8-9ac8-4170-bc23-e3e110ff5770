<script setup>
import { ref } from 'vue';
import AlertModal from '@/components/common/modal/AlertModal.vue';
import { useRouter } from 'vue-router/composables';
import {
    createAndUploadExternalReportJob,
    createWorksheetUploadJob,
} from '../utils.js';

const router = useRouter();

const saving = ref(false);
const worksheetFile = ref(null);
const validationError = ref(null);
const modal = ref({
    mode: 'warning',
    isOpen: false,
    heading: 'heading',
    message: '',
    messages: [],
    cancelText: 'No',
    cancelAction: () => { },
    confirmText: 'Yes',
    confirmAction: () => { },
    code: '',
});

function filesChanged(files) {
    const [file] = Array.from(files);
    if (file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        validationError.value = 'You can only upload an Excel file';
        return;
    }
    worksheetFile.value = file;
}

function filesValidated(result) {
    validationError.value = '';
    worksheetFile.value = null;
    if (result === 'EXTENSION_ERROR') {
        validationError.value = 'You can only upload an Excel file';
    }
    if (result === 'FILE_SIZE_ERROR') {
        validationError.value = 'File is too large';
    }
    if (result === 'MULTIFILES_ERROR') {
        validationError.value = 'You can only upload one file';
    }
}

function formValid() {
    validationError.value = null;

    if (!worksheetFile.value) {
        validationError.value = 'Please select a worksheet to upload.';
        return false;
    }

    return true;
}

async function uploadWorksheet() {
    if (!formValid()) {
        return;
    }

    saving.value = true;

    const uploadReportJobRequest = {
        user: 'rw-upload-user', // hiding the report by not providing a specific user / no audience
        reportDisplayName: 'Revision Rural Worksheet Upload - External Report Job',
        reportFileName: worksheetFile.value.name,
        reportFileFormat: 'xlsx',
    };

    // request external report
    const completedExternalJobResponse = await createAndUploadExternalReportJob(uploadReportJobRequest, worksheetFile.value);

    const { s3Uri } = completedExternalJobResponse;
    if (!s3Uri) {
        setModalFailure();
        return;
    }

    const createInternalReportJobResponse = await createWorksheetUploadJob(s3Uri);
    if (createInternalReportJobResponse.status !== 'CREATED') {
        setModalFailure();
        return;
    }

    setModalSuccess();
}

function setModalFailure() {
    setModal({
        mode: 'error',
        isOpen: true,
        heading: 'Scheduling failed',
        message: 'An error occurred while attempting to schedule your report.  Please contact support or try again later.',
        messages: [],
        cancelText: null,
        cancelAction: () => { },
        confirmText: 'OK',
        confirmAction: () => { saving.value = false; },
        code: 'REPORT_FAILED_MESSAGE',
    });
}

function setModalSuccess() {
    setModal({
        mode: 'message',
        isOpen: true,
        heading: 'Report Scheduled',
        message: 'Your report has been acknowledged and can be viewed in View My Reports.',
        messages: [],
        cancelText: 'View My Reports',
        cancelAction: () => { saving.value = false; router.push({ name: 'report-dashboard-my-reports' }); },
        confirmText: 'OK',
        confirmAction: () => { saving.value = false; },
        code: 'REPORT_SCHEDULED_MESSAGE',
    });
}

function setModal(inputModal) {
    modal.value = inputModal;
}

function modalCancel() {
    modal.value.isOpen = false;
    modal.value.cancelAction();
}

function modalConfirm() {
    modal.value.isOpen = false;
    modal.value.confirmAction();
}

</script>

<template>
    <div class="qv-flex-column">
        <div v-if="saving || modal.isOpen" class="page-mask" data-cy="upload-worksheet-page-mask" />
        <div class="qv-flex-row" style="flex: 1; justify-content: center">
            <file-selector :multiple="false" accept-extensions=".xlsx" data-cy="rw-file-selector" @validated="filesValidated" @changed="filesChanged">
                <div class="uploaderBody">
                    <div class="dropzone">
                        <div class="dz-message">
                            <h3>{{ worksheetFile ? worksheetFile.name : 'Drop the worksheet here to upload' }}</h3>
                            <div class="fileUploader-button">
                                <span class="mdl-button mdl-js-button mdl-button--raised"> or find a worksheet on your computer </span>
                            </div>
                            <span class="error">{{ validationError }}</span>
                        </div>
                    </div>
                </div>
            </file-selector>
        </div>
        <div class="qv-flex-row" style="justify-content: end">
            <button class="mdl-button mdl-button--raised mdl-button--colored" data-cy="upload-worksheet-button" :disabled="saving" @click="uploadWorksheet">UPLOAD</button>
        </div>
        <alert-modal v-if="modal.isOpen" :success="modal.mode === 'success'" :caution="modal.mode === 'warning'" :warning="modal.mode === 'error'" data-cy="upload-worksheet-modal">
            <h1>{{ modal.heading }}</h1>
            <p v-if="modal.message !== ''" style="white-space: pre-wrap">{{ modal.message.trim() }}</p>
            <div v-if="modal.messages.length > 0">
                <div class="validation-header-message--warnings">
                    <div class="message message-error" :class="{ 'message-error': modal.mode === 'error', 'message-warning': modal.mode === 'warning' }">
                        <ul>
                            <li v-for="(msg, index) in modal.messages" :key="index">- {{ msg }}</li>
                        </ul>
                    </div>
                </div>
            </div>
            <template #buttons>
                <div class="alertButtons">
                    <button v-if="modal.cancelText" id="errorCancel" class="mdl-button mdl-button--mini lefty" @click="modalCancel" data-cy="report-modal-cancel">
                        {{ modal.cancelText }}
                    </button>
                    <button v-if="modal.confirmText" id="continue" class="mdl-button mdl-button--mini" @click="modalConfirm" data-cy="report-modal-confirm">
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
            <input id="modalResponseCode" type="hidden" :value="modal.code" />
        </alert-modal>
    </div>
</template>

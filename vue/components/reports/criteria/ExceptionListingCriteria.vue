<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
    createReportJob
} from '../utils';
import 'vue2-datepicker/index.css';
import Multiselect from "vue-multiselect";
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, validateRolls } from './shared';
 
const saving = ref(false);
const router = useRouter();

const RollTypeMapping = {
    SINGLE: 'S',
    RANGE: 'R',
    MULTIPLE: 'M'
}

const rollType = ref('S');
const rollSingle = ref(null);
const rollStart = ref(null);
const rollEnd = ref(null);
const multipleRolls1 = ref(null);
const multipleRolls2 = ref(null);
const multipleRolls3 = ref(null);
const multipleRolls4 = ref(null);
const multipleRolls5 = ref(null);
const ratingAuthority = ref(null);
const categoryLength = ref(2);
const level = ref({ value: 'R', label: 'Rolls' });
const categoryType = ref('*');

const reportId = computed(() => currentReport.value?.id);

const levelOptions = [
  { value: 'R', label: 'Rolls' },
  { value: 'S', label: 'Sale Group' },
  { value: 'A', label: 'Rating Authority' }
];

const validationErrors = ref({
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null
});

const taStore = useTaList();
const taList = computed(() => taStore.value);

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: `Generate a Exception List Report for TAs, for a specific period.`,
        sticky: false,
    });
    watch(taList, (newList) => {
        if (newList && newList.length > 0 && !ratingAuthority.value) {
            ratingAuthority.value = newList[0];
        }
    }, { immediate: true });
});

function getFilters() {
    const filters = {
        ratingAuthorityId: ratingAuthority.value.id,
        rollType: rollType.value,
        rollSingle: rollSingle.value,
        rollStart: rollStart.value,
        rollEnd: rollEnd.value,
        multipleRolls1: multipleRolls1.value,
        multipleRolls2: multipleRolls2.value,
        multipleRolls3: multipleRolls3.value,
        multipleRolls4: multipleRolls4.value,
        multipleRolls5: multipleRolls5.value,
        categoryLength: categoryLength.value,
        level: level.value,
        categoryType: categoryType.value
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null
    };
    rollType.value = 'S';
    rollSingle.value = null;
    rollStart.value = null;
    rollEnd.value = null;
    multipleRolls1.value = null;
    multipleRolls2.value = null;
    multipleRolls3.value = null;
    multipleRolls4.value = null;
    multipleRolls5.value = null;
    ratingAuthority.value = taList.value[0];
    categoryLength.value = 2;
    level.value = { value: 'R', label: 'Rolls' };
    categoryType.value = '*';
    saving.value = false;
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilters();

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    const parameters = {
        RollType: filters.rollType,
        format: 'PDF',
        RollStart: filters.rollType === 'S' ? filters.rollSingle : filters.rollStart,
        RollEnd: filters.rollEnd ? filters.rollEnd : null,
        MultipleRolls: filters.rollType === 'M' ? [
            filters.multipleRolls1,
            filters.multipleRolls2,
            filters.multipleRolls3,
            filters.multipleRolls4,
            filters.multipleRolls5
        ]
            .filter(roll => roll !== null)
            .join(',') : null,
        RatingAuthorityId: filters.ratingAuthorityId ? filters.ratingAuthorityId: null,
        CategoryLength: filters.categoryLength ? filters.categoryLength : null,
        Level: filters.level ? filters.level.value : '',
        CategoryType: filters.categoryType ? filters.categoryType : '',
        FromReport: router.currentRoute.name === 'report-EXCEPTION_LIST' ? 'N' : 'Y'
    };

    let reportRequest = {
        reportId: reportId.value,
        parameters
    }
    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function toggleRollType(type) {
    rollType.value = type;
    rollEnd.value = null;
    rollStart.value = null;
    rollSingle.value = null;
    multipleRolls1.value = null;
    multipleRolls2.value = null;
    multipleRolls3.value = null;
    multipleRolls4.value = null;
    multipleRolls5.value = null;
}

function validateFilters(filters) {
    let valid = true;

    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null
    };

    if(filters.level?.value === 'A') {
        return valid;
    }

    const validRolls = validateRolls(filters);

    validationErrors.value.rollSingle = rollType.value === 'S' && !validRolls.isValid ? validRolls.message : null;
    validationErrors.value.rangeOfRoll = rollType.value === 'R' && !validRolls.isValid ? validRolls.message : null;
    validationErrors.value.multipleRolls = rollType.value === 'M' && !validRolls.isValid ? validRolls.message : null;

    return validRolls.isValid;
}

</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving"
            class="page-mask"
            data-cy="report-page-mask"
        >
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Territorial Authorities</h3>
                <div>
                    <ta-drop-down-select v-model="ratingAuthority" :allow-empty="false"/>
                </div>
                <h3>Category Length</h3>
                <div class="qv-flex-row">
                    <input
                        v-model="categoryLength"
                        type="number"
                    />
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Level Options</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :border="true"
                    >
                        <multiselect
                            v-model="level"
                            :options="levelOptions"
                            :multiple="false"
                            :close-on-select="false"
                            track-by="value"
                            label="label"
                            :preselect-first="true"
                            placeholder="Select Level"
                            select-label="⏎ select"
                            deselect-label="⏎ remove"
                        />
                    </tooltip>
                </div>
                <h3>Category Type</h3>
                <div class="qv-flex-row">
                    <input
                        v-model="categoryType"
                        type="text"
                    />
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" @click="toggleRollType('S')" style="white-space: nowrap;">
                    <input
                        v-model="rollType"
                        type="radio"
                        name="roll-type"
                        value="S"
                    />
                    <span>
                        Single Roll
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType('R')" style="white-space: nowrap;">
                    <input
                        v-model="rollType"
                        type="radio"
                        name="roll-type"
                        value="R"
                    />
                    <span>
                        Range of Rolls
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType('M')" style="white-space: nowrap;">
                    <input
                        v-model="rollType"
                        type="radio"
                        name="roll-type"
                        value="M"
                    />
                    <span>
                        Multiple Rolls
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rollSingle"
                        :border="true"
                    >
                        <input
                            v-model.number="rollSingle"
                            type="number"
                            :disabled="rollType !== 'S'"
                            data-cy="single-roll-input"
                        />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rangeOfRoll"
                        :border="true"
                    >
                        <input
                            v-model.number="rollStart"
                            type="number"
                            :disabled="rollType !== 'R'"
                        />
                        <input
                            v-model.number="rollEnd"
                            type="number"
                            :disabled="rollType !== 'R'"
                        />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.multipleRolls"
                        :border="true"
                    >
                        <input
                            v-model.number="multipleRolls1"
                            type="number"
                            :disabled="rollType !== 'M'"
                        />
                        <input
                            v-model.number="multipleRolls2"
                            type="number"
                            :disabled="rollType !== 'M'"
                        />
                        <input
                            v-model.number="multipleRolls3"
                            type="number"
                            :disabled="rollType !== 'M'"
                        />
                        <input
                            v-model.number="multipleRolls4"
                            type="number"
                            :disabled="rollType !== 'M'"
                        />
                        <input
                            v-model.number="multipleRolls5"
                            type="number"
                            :disabled="rollType !== 'M'"
                        />
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                @click="clearFilters"
                class="mdl-button mdl-button--raised"
            >
                Clear
            </button>
            <button
                @click="createReportRequest"
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving"
                data-cy="schedule-report-button"
            >
                Schedule Report
            </button>
        </div>
    </div>
</template>
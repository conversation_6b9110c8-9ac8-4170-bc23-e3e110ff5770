<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import { useCategoryType } from '../../../composables/categoryTypes';
import { useCategoryGroupType } from '../../../composables/categoryGroupTypes';
import { DateTime } from 'luxon';
import {
    currentReport,
    notifications,
    clearNotifications,
    createReportJob
} from '../utils';
import DatePicker from "vue2-datepicker";
import 'vue2-datepicker/index.css';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import ConfirmationModal from '@/components/common/modal/ConfirmationModal.vue';
import useModal from '@/composables/useModal';
import { submitReportRequest } from './shared';

const modal = useModal(); 
const saving = ref(false);
const router = useRouter();

const ParamTypeMapping = {
    SINGLE: 'S',
    RANGE: 'R',
    MULTIPLE: 'M'
}

const RollOrSgTypeMapping = {
    ROLL: 'R',
    SALESGROUP: 'S'
}

const CategoryTypeMapping = {
    CATEGORY: 'C',
    CATEGORYGROUP: 'CG'
}

const ratingAuthority = ref(null);
const dateFrom = ref('');
const dateTo = ref('');
const category = ref('');
const rollOrSg = ref('S');
const paramType = ref('S');
const singleValue = ref('');
const rangeStart = ref('');
const rangeEnd = ref('');
const params = reactive({
    param1: '',
    param2: '',
    param3: '',
    param4: '',
    param5: '',
})
const categories = reactive({
    category1: '',
    category2: '',
    category3: '',
    category4: '',
})
const categoryType = ref('C')

const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    dateFrom: null,
    dateTo: null,
    singleRoll: null,
    rangeOfRoll: null,
    multipleRolls: null,
    category: null,
    categoryList: null
});

const taStore = useTaList();
const taList = computed(() => taStore.value);

const categoryTypeStore = useCategoryType();
const categoryTypeList = computed(() => categoryTypeStore.value.map(item => item.code.trim()));

const categoryGroupTypeStore = useCategoryGroupType();
const categoryGroupTypeList = computed(() => categoryGroupTypeStore.value.map(item => item.code.trim()));

onMounted(() => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: `Generate a OVG Statistics Report for TAs, for a specific period.`,
        sticky: false,
    });
});

function getFilters() {
    const filters = {
        ratingAuthorityId: ratingAuthority.value.id,
        dateFrom: dateFrom.value,
        dateTo: dateTo.value,
        category: category.value,
        rollOrSg: rollOrSg.value,
        paramType: paramType.value,
        singleValue: singleValue.value,
        rangeStart: rangeStart.value,
        rangeEnd: rangeEnd.value,
        param1: params.param1,
        param2: params.param2,
        param3: params.param3,
        param4: params.param4,
        param5: params.param5,
        category1: categories.category1,
        category2: categories.category2,
        category3: categories.category3,
        category4: categories.category4,
        categoryType: categoryType.value,
    };
    if (filters.dateFrom) {
        filters.dateFrom = formatDate(filters.dateFrom);
    }
    if (filters.dateTo) {
        filters.dateTo = formatDate(filters.dateTo);
    }
    return filters;
}

function formatDate(dateString) {
    return DateTime.fromISO(dateString).toFormat('dd/MM/yyyy');
}

function clearFilters() {
    saving.value = true;
    clearFilterValidations();
    ratingAuthority.value = taList.value[0];
    dateFrom.value = '';
    dateTo.value = '';
    category.value = '';
    rollOrSg.value = 'S';
    paramType.value = 'S';
    singleValue.value = '';
    rangeStart.value = '';
    rangeEnd.value = '';
    params.param1 = '';
    params.param2 = '';
    params.param3 = '';
    params.param4 = '';
    params.param5 = '';
    categories.category1 = '';
    categories.category2 = '';
    categories.category3 = '';
    categories.category4 = '';
    categoryType.value = 'C';
    saving.value = false;
}

function clearFilterValidations() {
    validationErrors.value = {
        dateFrom: null,
        dateTo: null,
        singleRoll: null,
        rangeOfRoll: null,
        multipleRolls: null,
        category: null,
        categoryList: null
    };
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilters();

    const saleGroupTypes = await getSaleGroupTypes(ratingAuthority.value.code);

    const isValid = await validateFilters(filters, saleGroupTypes);

    if (!isValid) {
        saving.value = false;
        return;
    }

    if (filters.rollOrSg === RollOrSgTypeMapping.SALESGROUP) {
        const mapSalesGroupToId = (value) => {
            const foundGroup = saleGroupTypes.find(group => group.salesGroupNumber === value);
            return foundGroup ? String(foundGroup.saleGroupId) : String(value);
        };
        filters.singleValue = filters.singleValue ? mapSalesGroupToId(filters.singleValue) : '';
        filters.rangeStart = filters.rangeStart ? mapSalesGroupToId(filters.rangeStart) : '';
        filters.rangeEnd = filters.rangeEnd ? mapSalesGroupToId(filters.rangeEnd) : '';
        filters.param1 = filters.param1 ? mapSalesGroupToId(filters.param1) : '';
        filters.param2 = filters.param2 ? mapSalesGroupToId(filters.param2) : '';
        filters.param3 = filters.param3 ? mapSalesGroupToId(filters.param3) : '';
        filters.param4 = filters.param4 ? mapSalesGroupToId(filters.param4) : '';
        filters.param5 = filters.param5 ? mapSalesGroupToId(filters.param5) : '';
    }

    if (filters.paramType === ParamTypeMapping.SINGLE) {
        filters.param1 = filters.singleValue;
        filters.param2 = filters.singleValue;
    }
    else if (filters.paramType === ParamTypeMapping.RANGE) {
        filters.param1 = filters.rangeStart ? filters.rangeStart : filters.rangeEnd;
        filters.param2 = filters.rangeEnd ? filters.rangeEnd : filters.rangeStart;
    }

    const parameters = {
        format: 'PDF',
        ta_id: filters.ratingAuthorityId ? filters.ratingAuthorityId.toString() : '',
        date_start: filters.dateFrom ? filters.dateFrom : '',
        date_end: filters.dateTo ? filters.dateTo : '',
        category: filters.category ? filters.category.toUpperCase() : '',
        roll_or_sg: filters.rollOrSg ? filters.rollOrSg : '',
        param_type: filters.paramType ? filters.paramType : '',
        p_one: filters.param1 ? filters.param1 : '',
        p_two: filters.param2 ? filters.param2 : '',
        p_three: filters.param3 ? filters.param3 : '',
        p_four: filters.param4 ? filters.param4 : '',
        p_five: filters.param5 ? filters.param5 : '',
        pi_category_code_list: [
            filters.category1.toUpperCase(),
            filters.category2.toUpperCase(),
            filters.category3.toUpperCase(),
            filters.category4.toUpperCase(),
        ]
            .filter(category => category !== '')
            .join(',')
    };

    let reportRequest = {
        reportId: reportId.value,
        parameters
    }

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function toggleRollType(type) {
    paramType.value = type;
    clearValues();
}

function toggleRollOrSgType(type) {
    rollOrSg.value = type;
    clearValues();
}

function clearValues() {
    rangeStart.value = '';
    rangeEnd.value = '';
    singleValue.value = '';
    params.param1 = '';
    params.param2 = '';
    params.param3 = '';
    params.param4 = '';
    params.param5 = '';
}

function toggleCategory(type) {
    categoryType.value = type;
    category.value = '';
    categories.category1 = '';
    categories.category2 = '';
    categories.category3 = '';
    categories.category4 = '';
}

function updateDates() {
    const start = DateTime.fromISO(dateFrom.value);
    const end = DateTime.fromISO(dateTo.value);
    if (start.isValid && end.isValid) {
        if (start > end) {
            const tempDate = dateTo.value;
            dateTo.value = dateFrom.value;
            dateFrom.value = tempDate;
        }
    }
}

async function fetchJson(url, options = {}) {
    try {
        const response = await fetch(url, options);
        return await response.json();
    }
    catch (error) {
        throw error;
    }
}

async function getSaleGroupTypes(ta) {
    try {
        const formattedTa = ta.padStart(2, '0');
        const dataJson = await fetchJson(
            jsRoutes.controllers.ApiPicklistController.getSalesGroupsAndRolls(formattedTa).url,
        );
        return dataJson.result[0]?.salesGroups;
    }
    catch (error) {
        const message = 'Error calling controller to get Sale Group Types';
        console.error(message, error);
    }
}

function validateRequiredValues(filters) {
    let valid = true;

    if (filters.paramType === ParamTypeMapping.SINGLE) {
        if (!filters.singleValue) {
            validationErrors.value.singleRoll = 'Please specify a value.';
            valid = false;
        }
    }
    else if (filters.paramType === ParamTypeMapping.RANGE) {
        if (!filters.rangeStart && !filters.rangeEnd) {
            validationErrors.value.rangeOfRoll = 'Please specify a value start or end.';
            valid = false;
        }
    }
    else if (filters.paramType === ParamTypeMapping.MULTIPLE) {
        if (!filters.param1 && !filters.param2 && !filters.param3 && !filters.param4 && !filters.param5) {
            validationErrors.value.multipleRolls = 'Please specify a value.';
            valid = false;
        }
    }

    if (!filters.dateFrom) {
        validationErrors.value.dateFrom = 'Please specify a start date';
        valid = false;
    }
    
    if (!filters.dateTo) {
        validationErrors.value.dateTo = 'Please specify a end date';
        valid = false;
    }

    if (filters.categoryType === CategoryTypeMapping.CATEGORYGROUP) {
        if (!filters.category) {
            validationErrors.value.category = 'Please specify a value.';
            valid = false;
        }
    }
    else if (filters.categoryType === CategoryTypeMapping.CATEGORY) {
        if (!filters.category1 && !filters.category2 && !filters.category3 && !filters.category4) {
            validationErrors.value.categoryList = 'Please specify a value.';
            valid = false;
        }
    }

    return valid;
}

async function validateSalesGroupValues(filters, saleGroupTypes) {
    let valid = true;

    const salesGroupNumber = saleGroupTypes.map(sg => sg.salesGroupNumber);

    const message = 'Please specify a valid sales group';

    const isValidGroup = (value) => value && !salesGroupNumber.includes(value);

    if (filters.paramType === ParamTypeMapping.SINGLE) {
        if (isValidGroup(filters.singleValue)) {
            validationErrors.value.singleRoll = message;
            valid = false;
        }
    }

    if (filters.paramType === ParamTypeMapping.RANGE) {
        if (isValidGroup(filters.rangeStart) || isValidGroup(filters.rangeEnd)) {
            validationErrors.value.rangeOfRoll = message;
            valid = false;
        }
    }

    if (filters.paramType === ParamTypeMapping.MULTIPLE) {
        const params = [filters.param1, filters.param2, filters.param3, filters.param4, filters.param5];
        if (params.some(isValidGroup)) {
            validationErrors.value.multipleRolls = message;
            valid = false;
        }
    }

    return valid;
}

function validateCategoryType(filters) {
    let valid = true;

    const isValidCategory = (category, list) => {
        const normalizedCategory = category.replace('*', '%');
        const regex = new RegExp('^' + normalizedCategory.replace('%', '.*') + '$');
        return list.some(type => regex.test(type));
    };

    if (categoryType.value === CategoryTypeMapping.CATEGORY) {
        const categories = [filters.category1, filters.category2, filters.category3, filters.category4];
        for (const category of categories) {
            if (category && !isValidCategory(category.toUpperCase(), categoryTypeList.value)) {
                valid = false;
                validationErrors.value.categoryList = 'Please specify valid categories';
                break;
            }
        }
    }
    else if (categoryType.value === CategoryTypeMapping.CATEGORYGROUP) {
        if (filters.category && !isValidCategory(filters.category.toUpperCase(), categoryGroupTypeList.value)) {
            valid = false;
            validationErrors.value.category = 'Category group must be a valid category group';
        }
    }

    return valid;
}

async function validateFilters(filters, saleGroupTypes) {
    let valid = true;

    validationErrors.value = {
        dateFrom: null,
        dateTo: null,
        singleRoll: null,
        rangeOfRoll: null,
        multipleRolls: null,
        category: null,
        categoryList: null
    };

    if (!validateRequiredValues(filters)) {
        valid = false;
    }

    if (rollOrSg.value === RollOrSgTypeMapping.SALESGROUP) {
        const isSalesGroupValid = validateSalesGroupValues(filters, saleGroupTypes);
        if (!isSalesGroupValid) {
            valid = false;
        }
    }

    if (!validateCategoryType(filters)) {
        valid = false;
    }

    return valid;
}

</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
        style="overflow:visible"
    >
        <div
            v-if="saving || modal.isOpen"
            class="page-mask"
            data-cy="report-page-mask"
        >
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Territorial Authorities</h3>
                <div>
                    <ta-drop-down-select v-model="ratingAuthority" :allow-empty="false"/>
                </div>
            </div>
            <div class="qv-flex-column">
                <div class="qv-flex-row">
                    <div class="qv-flex-column">
                        <h3>Start Date</h3>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateFrom"
                            :border="true"
                        >
                            <date-picker
                                v-model="dateFrom"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                @input="updateDates"
                                data-cy="start-date"
                            />
                        </tooltip>
                    </div>
                    <div class="qv-flex-column">
                        <h3>End Date</h3>
                        <tooltip
                            display-mode="error"
                            :text="validationErrors.dateTo"
                            :border="true"
                        >
                            <date-picker
                                v-model="dateTo"
                                class="report-datepicker"
                                type="date"
                                format="D/M/YYYY"
                                value-type="YYYY-MM-DD"
                                @input="updateDates"
                                data-cy="end-date"
                            />
                        </tooltip>
                    </div>
                </div>
            </div>
        </div>

        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" @click="toggleRollOrSgType('S')" style="white-space: nowrap;">
                    <input
                        v-model="rollOrSg"
                        type="radio"
                        name="Sales-group-type"
                        value="S"
                    />
                    <span>
                        Sales Group
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType('S')" style="white-space: nowrap;">
                    <input
                        v-model="paramType"
                        type="radio"
                        name="roll-type"
                        value="S"
                    />
                    <span>
                        Single Value
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType('R')" style="white-space: nowrap;">
                    <input
                        v-model="paramType"
                        type="radio"
                        name="roll-type"
                        value="R"
                    />
                    <span>
                        Range of Values
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleRollType('M')" style="white-space: nowrap;">
                    <input
                        v-model="paramType"
                        type="radio"
                        name="roll-type"
                        value="M"
                    />
                    <span>
                        Multiple Values
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" @click="toggleRollOrSgType('R')" style="white-space: nowrap;">
                    <input
                        v-model="rollOrSg"
                        type="radio"
                        name="Roll-number-type"
                        value="R"
                    />
                    <span>
                        Roll Number
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.singleRoll"
                        :border="true"
                    >
                        <input
                            v-model="singleValue"
                            data-cy="single-value"
                            type="text"
                            :disabled="paramType !== 'S'"
                        />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rangeOfRoll"
                        :border="true"
                    >
                        <input
                            v-model="rangeStart"
                            type="text"
                            :disabled="paramType !== 'R'"
                        />
                        <input
                            v-model="rangeEnd"
                            type="text"
                            :disabled="paramType !== 'R'"
                        />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.multipleRolls"
                        :border="true"
                    >
                        <input
                            v-model="params.param1"
                            type="text"
                            :disabled="paramType !== 'M'"
                        />
                        <input
                            v-model="params.param2"
                            type="text"
                            :disabled="paramType !== 'M'"
                        />
                        <input
                            v-model="params.param3"
                            type="text"
                            :disabled="paramType !== 'M'"
                        />
                        <input
                            v-model="params.param4"
                            type="text"
                            :disabled="paramType !== 'M'"
                        />
                        <input
                            v-model="params.param5"
                            type="text"
                            :disabled="paramType !== 'M'"
                        />
                    </tooltip>
                </div>
            </div>
        </div>

        <div class="qv-flex-row" style="flex:1;">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" @click="toggleCategory('C')" style="white-space: nowrap;">
                    <input
                        v-model="categoryType"
                        type="radio"
                        name="category-type"
                        value="C"
                    />
                    <span>
                        Category
                    </span>
                </div>
                <div class="qv-flex-row report-option" @click="toggleCategory('CG')" style="white-space: nowrap;">
                    <input
                        v-model="categoryType"
                        type="radio"
                        name="category-type"
                        value="CG"
                    />
                    <span>
                        Category Group
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.categoryList"
                        :border="true"
                    >
                        <input
                            v-model="categories.category1"
                            type="text"
                            :disabled="categoryType !== 'C'"
                            data-cy="category-input"
                        />
                        <input
                            v-model="categories.category2"
                            type="text"
                            :disabled="categoryType !== 'C'"
                        />
                        <input
                            v-model="categories.category3"
                            type="text"
                            :disabled="categoryType !== 'C'"
                        />
                        <input
                            v-model="categories.category4"
                            type="text"
                            :disabled="categoryType !== 'C'"
                        />
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.category"
                        :border="true"
                    >
                        <input
                            v-model="category"
                            type="text"
                            :disabled="categoryType !== 'CG'"
                        />
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                @click="clearFilters"
                class="mdl-button mdl-button--raised"
            >
                Clear
            </button>
            <button
                @click="createReportRequest"
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving"
                data-cy="schedule-report-button"
            >
                Schedule Report
            </button>
        </div>
    </div>
</template>
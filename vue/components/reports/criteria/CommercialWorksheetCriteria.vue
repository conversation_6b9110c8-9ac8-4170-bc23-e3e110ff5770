<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router/composables';
import { useTaList } from '../../../composables/taList';
import {
    currentReport,
    notifications,
    clearNotifications,
    NONE } from '../utils';
import 'vue2-datepicker/index.css';
import TaDropDownSelect from '../TADropDownSelect.vue';
import Tooltip from '@/components/common/Tooltip.vue';
import { submitReportRequest, isPositiveInteger, validateRolls } from './shared';

const saving = ref(false);
const router = useRouter();

const RollTypeMapping = {
    SINGLE: 'S',
    RANGE: 'R',
    MULTIPLE: 'M',
};

const PropertyTypeMapping = {
    CURRENTWORKSHEET: 'Current',
    REVISIONWORKSHEET: 'Revision',
};

const rollType = ref('S');
const rollSingle = ref(null);
const rollStart = ref(null);
const rollEnd = ref(null);
const multipleRolls = reactive({
    multipleRolls1: null,
    multipleRolls2: null,
    multipleRolls3: null,
    multipleRolls4: null,
    multipleRolls5: null,
});
const ratingAuthority = ref(NONE);
const category = ref('');
const categoryGroup = ref('');
const capitalRangeFrom = ref(null);
const capitalRangeTo = ref(null);
const propertyType = ref(PropertyTypeMapping.CURRENTWORKSHEET);

const reportId = computed(() => currentReport.value?.id);

const validationErrors = ref({
    rollSingle: null,
    rangeOfRoll: null,
    multipleRolls: null,
    category: null,
    categoryGroup: null,
    capitalRangeFrom: null,
    capitalRangeTo: null,
    ratingAuthorityId: null,
});

const taStore = useTaList();
const taList = computed(() => [{ id: -1, code: '-1', description: '- None -' }].concat(taStore.value));

onMounted(async () => {
    clearNotifications();
    notifications.value.push({
        type: 'info',
        message: 'Generate a Commercial Worksheet Report, for a specific period.',
        sticky: false,
    });
});


function getFilters() {
    const filters = {
        ratingAuthorityId: ratingAuthority.value.id,
        ratingAuthorityName: ratingAuthority.value.description,
        rollType: rollType.value,
        rollSingle: rollSingle.value,
        rollStart: rollStart.value,
        rollEnd: rollEnd.value,
        multipleRolls1: multipleRolls.multipleRolls1,
        multipleRolls2: multipleRolls.multipleRolls2,
        multipleRolls3: multipleRolls.multipleRolls3,
        multipleRolls4: multipleRolls.multipleRolls4,
        multipleRolls5: multipleRolls.multipleRolls5,
        category: category.value,
        categoryGroup: categoryGroup.value,
        capitalRangeFrom: capitalRangeFrom.value,
        capitalRangeTo: capitalRangeTo.value,
        propertyType: propertyType.value,
    };
    return filters;
}

function clearFilters() {
    saving.value = true;
    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null,
        category: null,
        categoryGroup: null,
        capitalRangeFrom: null,
        capitalRangeTo: null,
        ratingAuthorityId: null,
    };
    rollType.value = 'S';
    rollSingle.value = null;
    rollStart.value = null;
    rollEnd.value = null;
    multipleRolls.multipleRolls1 = null;
    multipleRolls.multipleRolls2 = null;
    multipleRolls.multipleRolls3 = null;
    multipleRolls.multipleRolls4 = null;
    multipleRolls.multipleRolls5 = null;
    ratingAuthority.value = taList.value[0];
    category.value = '';
    categoryGroup.value = '';
    capitalRangeFrom.value = null;
    capitalRangeTo.value = null;
    propertyType.value = PropertyTypeMapping.CURRENTWORKSHEET;
    saving.value = false;
}

async function createReportRequest() {
    saving.value = true;
    const filters = getFilters();

    if (!validateFilters(filters)) {
        saving.value = false;
        return;
    }

    const parameters = {
        format: 'PDF',
        pi_roll_type: filters.ratingAuthorityId === -1 ? filters.rollType : null,
        pi_roll_number: filters.rollSingle ? filters.rollSingle : null,
        pi_roll_number_start: filters.rollStart ? filters.rollStart : null,
        pi_roll_number_end: filters.rollEnd ? filters.rollEnd : null,
        pi_roll_numbers: filters.rollType === 'M' ? [
            filters.multipleRolls1,
            filters.multipleRolls2,
            filters.multipleRolls3,
            filters.multipleRolls4,
            filters.multipleRolls5,
        ]
            .filter(roll => roll !== null)
            .join(',') : null,
        pi_ta_id: filters.ratingAuthorityId ? filters.ratingAuthorityId : -1,
        pi_ta_name: filters.ratingAuthorityName 
            ? (filters.ratingAuthorityName === '- None -' 
                ? filters.ratingAuthorityName 
                : filters.ratingAuthorityName.split(' - ').slice(1).join(' - ').trim())
            : null,
        pi_category: filters.category ? filters.category.replace(/\*/g, '%') : null,
        pi_category_group: filters.categoryGroup ? filters.categoryGroup.replace(/\*/g, '%') : null,
        pi_capital_value_start: filters.capitalRangeFrom ?? null,
        pi_capital_value_end: filters.capitalRangeTo ?? null,
        pi_show_revision_values: filters.propertyType === PropertyTypeMapping.CURRENTWORKSHEET ? 'False' : 'True',
    };

    const reportRequest = {
        reportId: reportId.value,
        parameters,
    };

    await submitReportRequest(reportRequest, router);

    saving.value = false;
}

function toggleRollType(type) {
    rollType.value = type;
    rollEnd.value = null;
    rollStart.value = null;
    rollSingle.value = null;
    multipleRolls.multipleRolls1 = null;
    multipleRolls.multipleRolls2 = null;
    multipleRolls.multipleRolls3 = null;
    multipleRolls.multipleRolls4 = null;
    multipleRolls.multipleRolls5 = null;
}

function togglePropertyType(type) {
    propertyType.value = type;
}

function validateCategory(filters) {
    let valid = true;
    if (!filters.category && !filters.categoryGroup) {
        validationErrors.value.category = 'Must input either category or category group.';
        validationErrors.value.categoryGroup = 'Must input either category or category group.';
        valid = false;
    }
    else if (filters.category && filters.categoryGroup) {
        validationErrors.value.category = 'Can only input one of them: category or category group';
        validationErrors.value.categoryGroup = 'Can only input one of them: category or category group';
        valid = false;
    }
    return valid;
}

function validateCapitalRange(filters) {
    let valid = true;

    const capitalRangeFromExists = filters.capitalRangeFrom === 0 || filters.capitalRangeFrom;

    if ((capitalRangeFromExists && !filters.capitalRangeTo) || (!capitalRangeFromExists && filters.capitalRangeTo)) {
        validationErrors.value.capitalRangeFrom = 'Must input both of From and To';
        validationErrors.value.capitalRangeTo = 'Must input both of From and To';
        valid = false;
    }
    else if (capitalRangeFromExists && filters.capitalRangeTo) {
        if (!isPositiveInteger(filters.capitalRangeFrom)) {
            validationErrors.value.capitalRangeFrom = 'Must input a positive integer';
            valid = false;
        }
        if (!isPositiveInteger(filters.capitalRangeTo)) {
            validationErrors.value.capitalRangeTo = 'Must input a positive integer';
            valid = false;
        }
    }
    return valid;
}

function validateTa(filters) {
    let valid = true;
    const isRollAvailable = [
        filters.rollSingle,
        filters.rollStart,
        filters.rollEnd,
        filters.multipleRolls1,
        filters.multipleRolls2,
        filters.multipleRolls3,
        filters.multipleRolls4,
        filters.multipleRolls5,
    ].some(Boolean);

    if (isRollAvailable && filters.ratingAuthorityId !== -1) {
        validationErrors.value.ratingAuthorityId = 'TA can not be used when Roll is used';
        valid = false;
    }
    else if (!isRollAvailable && filters.ratingAuthorityId === -1) {
        validationErrors.value.ratingAuthorityId = 'Must use one of either TA or Roll';
        valid = false;
    }
    return valid;
}


function validateFilters(filters) {
    let valid = true;

    validationErrors.value = {
        rollSingle: null,
        rangeOfRoll: null,
        multipleRolls: null,
        category: null,
        categoryGroup: null,
        capitalRangeFrom: null,
        capitalRangeTo: null,
        ratingAuthorityId: null,
    };

    const isCategoryValid = validateCategory(filters);

    if (!isCategoryValid) {
        valid = false;
    }

    const iscapitalRangeValid = validateCapitalRange(filters);

    if (!iscapitalRangeValid) {
        valid = false;
    }

    const isTaValid = validateTa(filters);

    if (!isTaValid) {
        valid = false;
    }

    const isRollValid = validateRolls(filters);

    const isRollAvailable = [
        filters.rollSingle,
        filters.rollStart,
        filters.rollEnd,
        filters.multipleRolls1,
        filters.multipleRolls2,
        filters.multipleRolls3,
        filters.multipleRolls4,
        filters.multipleRolls5,
    ].some(Boolean);

    if (isRollAvailable && !isRollValid.isValid) {
        validationErrors.value.rollSingle = rollType.value === RollTypeMapping.SINGLE ? isRollValid.message : null;
        validationErrors.value.rangeOfRoll = rollType.value === RollTypeMapping.RANGE ? isRollValid.message : null;
        validationErrors.value.multipleRolls = rollType.value === RollTypeMapping.MULTIPLE ? isRollValid.message : null;
        valid = false;
    }

    return valid;
}

</script>

<template>
    <div
        class="report-criteria qv-flex-column"
        data-cy="report-criteria"
    >
        <div
            v-if="saving"
            class="page-mask"
            data-cy="report-page-mask"
        />
        <h3>General</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('S')">
                    <input
                        v-model="rollType"
                        type="radio"
                        name="roll-type"
                        value="S"
                    >
                    <span>
                        Single Roll
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('R')">
                    <input
                        v-model="rollType"
                        type="radio"
                        name="roll-type"
                        value="R"
                    >
                    <span>
                        Range of Rolls
                    </span>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="toggleRollType('M')">
                    <input
                        v-model="rollType"
                        type="radio"
                        name="roll-type"
                        value="M"
                    >
                    <span>
                        Multiple Rolls
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 1;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rollSingle"
                        :border="true"
                    >
                        <input
                            v-model.number="rollSingle"
                            type="text"
                            :disabled="rollType !== 'S'"
                            data-cy="single-roll-input"
                        >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.rangeOfRoll"
                        :border="true"
                    >
                        <input
                            v-model.number="rollStart"
                            type="text"
                            :disabled="rollType !== 'R'"
                        >
                        <input
                            v-model.number="rollEnd"
                            type="text"
                            :disabled="rollType !== 'R'"
                        >
                    </tooltip>
                </div>
                <div class="qv-flex-row report-option" style="white-space: nowrap;">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.multipleRolls"
                        :border="true"
                    >
                        <input
                            v-model.number="multipleRolls.multipleRolls1"
                            type="text"
                            :disabled="rollType !== 'M'"
                        >
                        <input
                            v-model.number="multipleRolls.multipleRolls2"
                            type="text"
                            :disabled="rollType !== 'M'"
                        >
                        <input
                            v-model.number="multipleRolls.multipleRolls3"
                            type="text"
                            :disabled="rollType !== 'M'"
                        >
                        <input
                            v-model.number="multipleRolls.multipleRolls4"
                            type="text"
                            :disabled="rollType !== 'M'"
                        >
                        <input
                            v-model.number="multipleRolls.multipleRolls5"
                            type="text"
                            :disabled="rollType !== 'M'"
                        >
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.5;">
                <h3>Territorial Authorities</h3>
                <div>
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.ratingAuthorityId"
                        :border="true"
                        style="width: 100%;"
                    >
                        <ta-drop-down-select v-model="ratingAuthority"/>
                    </tooltip>
                </div>
            </div>
        </div>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Category</h3>
                <div class="qv-flex-row">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.category"
                        :border="true"
                        style="width: 100%;"
                    >
                        <input
                            v-model="category"
                            type="text"
                            class="text-input"
                            data-cy="category-input"
                        >
                    </tooltip>
                </div>
            </div>
            <div class="qv-flex-column">
                <h3>Category Group</h3>
                <div class="qv-flex-row">
                    <tooltip
                        display-mode="error"
                        :text="validationErrors.categoryGroup"
                        :border="true"
                        style="width: 100%;"
                    >
                        <input
                            v-model="categoryGroup"
                            type="text"
                            class="text-input"
                        >
                    </tooltip>
                </div>
            </div>
        </div>
        <h3 style="margin-top: 0.5rem;">Optional</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column">
                <h3>Capital Value Range</h3>
                <h3>From</h3>
                <tooltip
                    display-mode="error"
                    :text="validationErrors.capitalRangeFrom"
                    :border="true"
                    style="width: 100%;"
                >
                    <input
                        v-model.number="capitalRangeFrom"
                        type="text"
                        class="text-input"
                    >
                </tooltip>
            </div>
            <div class="qv-flex-column">
                <h3 style="visibility: hidden;">Capital Value Range</h3>
                <h3>To</h3>
                <tooltip
                    display-mode="error"
                    :text="validationErrors.capitalRangeTo"
                    :border="true"
                    style="width: 100%;"
                >
                    <input
                        v-model.number="capitalRangeTo"
                        type="text"
                        class="text-input"
                    >
                </tooltip>
            </div>
        </div>
        <h3 style="margin-top: 0.5rem;">Report Type</h3>
        <div class="qv-flex-row">
            <div class="qv-flex-column" style="flex-grow: 0.25;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="togglePropertyType(PropertyTypeMapping.CURRENTWORKSHEET)">
                    <input
                        v-model="propertyType"
                        type="radio"
                        value="Current"
                    >
                    <span>
                        Current Worksheets
                    </span>
                </div>
            </div>
            <div class="qv-flex-column" style="flex-grow: 0.25;">
                <div class="qv-flex-row report-option" style="white-space: nowrap;" @click="togglePropertyType(PropertyTypeMapping.REVISIONWORKSHEET)">
                    <input
                        v-model="propertyType"
                        type="radio"
                        value="Revision"
                    >
                    <span>
                        Revision Worksheets
                    </span>
                </div>
            </div>
        </div>
        <div class="qv-flex-row" style="justify-content:end;">
            <button
                class="mdl-button mdl-button--raised"
                @click="clearFilters"
            >
                Clear
            </button>
            <button
                class="mdl-button mdl-button--raised mdl-button--colored"
                :disabled="saving"
                data-cy="schedule-report-button"
                @click="createReportRequest"
            >
                Schedule Report
            </button>
        </div>
    </div>
</template>

<style lang="scss" src='../../reports/reports.scss' />

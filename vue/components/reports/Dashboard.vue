<script setup>
import { currentReport, findReportByName, getReports, notifications, reports, reportsIncludingHidden } from './utils';
import Toolbar from '@/components/common/Toolbar.vue';
import ReportMenu from '@/components/reports/ReportMenu.vue';

import { computed, onMounted } from 'vue';
import { useRoute } from 'vue-router/composables';
import { store } from '../../DataStore';

const route = useRoute();
const showMenu = computed(() =>
    !(store.state.userData.isExternalUser)
);

onMounted(async () => {
    const [reportsRes, reportsIncludingHiddenRes] = await Promise.all([
        getReports(),
        getReports(true),
    ]);
    reports.value = reportsRes;
    reportsIncludingHidden.value = reportsIncludingHiddenRes;

    // Route directly called from browser address bar instead of clicking on a report
    if (route.name !== 'report-dashboard-my-reports' && !currentReport.value) {
        currentReport.value = findReportByName(reports.value, route.path.split('/').pop());
    }
});

</script>

<template>
    <div class="contentWrapper">
        <toolbar title="Reports" />
        <div class="resultsWrapper">
            <div class="qv-flex-row resultsInner-wrapper report-dashboard mdl-shadow--3dp">
                <report-menu
                    v-if="showMenu"
                    :reports="reports"
                    data-cy="reports-menu-panel"
                />
                <div class="qv-flex-column content-wrapper">
                    <div
                        v-if="notifications.length > 0"
                        class="qv-flex-column notification-area mdl-shadow--3dp"
                        data-cy="reports-notifications-panel"
                    >
                        <div
                            v-for="(notification, index) in notifications"
                            :key="index"
                            style="white-space:pre-wrap;"
                        >
                            <span style="font-weight: normal">{{ notification.message }}</span>
                        </div>
                    </div>
                    <div
                        class="qv-flex-column content-area mdl-shadow--3dp"
                        data-cy="reports-main-panel"
                    >
                        <router-view />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

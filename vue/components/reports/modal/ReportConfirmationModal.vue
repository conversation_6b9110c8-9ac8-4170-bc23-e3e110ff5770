<script setup>

const props = defineProps({
  status: {
      type: String,
      validate: (value) => ['failed', 'success'].includes(value),
  },
});

const emit = defineEmits(['close']);
</script>

<template>
  <div class="qv-flex-column qv-flex-grow qv-justify-space-between" style="padding: 0.8rem">
    <div>
      <div v-if="status === 'success'">
        <h1 class="qv-text-lg qv-color-mediumblue" data-cy="dialog-title">Report Scheduled
        </h1>
        <p class="cw-modal-message qv-text-sm">Your report has been acknowledged and can be viewed in View My Reports.
        </p>
      </div>
      <div v-else-if="status === 'failed'">
        <h1 class="qv-text-lg qv-color-error" data-cy="dialog-title">Scheduling failed</h1>
        <p class="cw-modal-message qv-text-sm">An error occurred while attempting to schedule your report. Please
          contact support or try again later.</p>
      </div>
    </div>
    <div v-if="status === 'success'" class="qv-flex-row qv-justify-space-between">
      <button class="qv-dialog-button qv-bg-transparent" @click="() => emit('close', false)"
        data-cy="button-dialog-cancel">VIEW MY REPORTS</button>
      <button class="qv-dialog-button qv-bg-transparent" @click="() => emit('close', true)"
        data-cy="button-dialog-confirm">OK</button>
    </div>
    <div v-else-if="status === 'failed'" class="qv-flex-row qv-justify-end">
      <button class="qv-dialog-button qv-bg-transparent" @click="() => emit('close', true)"
        data-cy="button-dialog-confirm">OK</button>
    </div>
  </div>
</template>
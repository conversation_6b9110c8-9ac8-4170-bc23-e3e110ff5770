<script setup>
import { ref, computed, onMounted } from 'vue';
import { notifications, clearNotifications, getReportJobsPagination, RURAL_WORKSHEET_UPLOAD, updateReportJobStatus, getReportJobFileUrl } from './utils';
import { formatFileSize } from '@/utils/common.js';
import moment from 'moment';
import AlertModal from '@/components/common/modal/AlertModal.vue';
import LoadingSpinner from '@/components/common/LoadingSpinner.vue';

const loading = ref(false);
const errorMessage = ref(null);
const modal = ref({});

/*
  TODO
  - pageSize, orderBy and orderDirection should be user preferences
  - delete report jobs
*/

const pageSize = 10;
let orderBy = 'createdAt';
let orderDirection = 'DESC';

const deleting = ref(false);
const pageNumber = ref(1);
const total = ref(0);
const reportJobs = ref([]);
const selectedReportJobs = ref([]);

const totalPages = computed(() => {
    return Math.ceil(total.value / pageSize);
});

const reportIds = computed(() => {
    return reportJobs.value.map(i => i.id);
});
const allJobsSelected = computed(() => {
    const numJobsSelectable = reportJobs.value.filter(job => canSelectForDelete(job)).length;
    const numJobsSelected = selectedReportJobs.value.filter(id => reportIds.value.includes(id)).length;
    return numJobsSelectable > 0 && numJobsSelectable === numJobsSelected;
});

onMounted(async () => {
    await clearNotifications();
    await loadReportJobs(pageSize, 1, orderBy, orderDirection);
    notifications.value.push({
        type: 'info',
        message: `View your scheduled Reports and Extracts here. Press “Download” to view the report.\nDelete unwanted and failed reports by selecting it and clicking "Delete".`,
        sticky: false,
    });
});

function modalCancel() {
    modal.value.isOpen = false;
    modal.value.cancelAction();
}

function modalConfirm() {
    modal.value.isOpen = false;
    modal.value.confirmAction();
}

function handleDelete() {
    modal.value = {
        mode: 'error',
        isOpen: true,
        heading: `Delete Report${selectedReportJobs.value.length > 1 ? 's' : ''}`,
        message: `Are you sure you want to delete ${selectedReportJobs.value.length} report${selectedReportJobs.value.length > 1 ? 's' : ''}?`,
        cancelText: 'Cancel',
        cancelAction: () => {
            modal.value.isOpen = false;
        },
        confirmText: 'OK',
        confirmAction: () => {
            deleteSelectedReports();
            modal.value.isOpen = true;
        },
    };
}

async function deleteSelectedReports() {
    try {
        deleting.value = true;
        const promises = selectedReportJobs.value.map(reportId => updateReportJobStatus(reportId, `${REPORT_JOB_STATUS_DELETED}`));
        const statusUpdateResponses = await Promise.all(promises);
        const failureCount = statusUpdateResponses.filter(response => response.status !== 'UPDATED').length;
        if (failureCount > 0) {
            modal.value = {
                mode: 'error',
                isOpen: true,
                heading: `Error Deleting Report${selectedReportJobs.value.length > 1 ? 's' : ''}`,
                message: `Failed to delete ${failureCount} report${failureCount > 1 ? 's' : ''}.`,
                confirmText: 'OK',
                confirmAction: () => {
                    modal.value.isOpen = false;
                },
            };
        }
        else {
            modal.value = {
                mode: 'success',
                isOpen: true,
                heading: `Successfully Deleted Report${selectedReportJobs.value.length > 1 ? 's' : ''}`,
                message: `Successfully deleted ${selectedReportJobs.value.length} report${selectedReportJobs.value.length > 1 ? 's' : ''}.`,
                confirmText: 'OK',
                confirmAction: () => {
                    modal.value.isOpen = false;
                },
            };
        }
        selectedReportJobs.value = [];
        pageNumber.value = 1;
        await loadReportJobs(pageSize, pageNumber.value, orderBy, orderDirection);
    }
    catch (error) {
        console.error(error);
    }
    finally {
        deleting.value = false;
    }
}

async function loadReportJobs(pgSize, pgNo, orderBy, orderDirection) {
    loading.value = true;
    const jobs = await getReportJobsPagination(pgSize, pgNo, orderBy, orderDirection);
    reportJobs.value = jobs.reportJobs.map((job) => {
        job.selected = isJobSelected(job.id);
        return job;
    });
    total.value = jobs.total;
    loading.value = false;
}

const reportJobResultMessage = computed(() => {
    if (!total.value || loading.value) {
        return '';
    }
    const start = (pageNumber.value - 1) * pageSize + 1;
    const end = Math.min(pageNumber.value * pageSize, total.value);
    let message = `Showing ${start} to ${end} of ${total.value} reports.`;
    if (selectedReportJobs.value.length) {
        message = `${message} ${selectedReportJobs.value.length} selected.`;
    }
    return message;
});
const pages = computed(() => {
    const pages = [];
    const start = Math.max(1, pageNumber.value - 2);
    const end = Math.min(totalPages.value, pageNumber.value + 2);
    for (let i = start; i <= end; i++) {
        pages.push(i);
    }
    if (start > 2) {
        pages.unshift('...');
        pages.unshift(1);
    }
    else if (start === 2) {
        pages.unshift(1);
    }
    if (end < totalPages.value - 1) {
        pages.push('...');
        pages.push(totalPages.value);
    }
    else if (end === totalPages.value - 1) {
        pages.push(totalPages.value);
    }
    return pages;
});

const REPORT_JOB_STATUS_QUEUED = 1;
const REPORT_JOB_STATUS_STARTED = 2;
const REPORT_JOB_STATUS_SUCCEEDED = 3;
const REPORT_JOB_STATUS_FAILED = 4;
const REPORT_JOB_STATUS_DELETED = 5;

function canSelectForDelete(reportJob) {
    return [REPORT_JOB_STATUS_FAILED, REPORT_JOB_STATUS_SUCCEEDED].includes(reportJob.status) && !reportJob.shared;
}

function statusString(job) {
    const { status } = job;
    switch (status) {
        case REPORT_JOB_STATUS_QUEUED:
            return 'Queued';
        case REPORT_JOB_STATUS_STARTED:
            return 'Pending';
        case REPORT_JOB_STATUS_SUCCEEDED:
            if (job.report_name === RURAL_WORKSHEET_UPLOAD) {
                return 'Completed';
            }
            return 'Ready';
        case REPORT_JOB_STATUS_FAILED:
            return 'Failed';
        default:
            return 'Unknown';
    }
}

function goToPage(page) {
    if (page < 1 || page > totalPages.value || page === '...') {
        return;
    }
    pageNumber.value = page;
    loadReportJobs(pageSize, pageNumber.value, orderBy, orderDirection);
}

function returnRunTime(job) {
    if (!job.started_at || !job.finished_at) {
        return '-';
    }
    const start = moment(job.started_at);
    const end = moment(job.finished_at);
    const duration = moment.duration(end.diff(start));
    return moment.utc(duration.asMilliseconds()).format('H:mm:ss');
}

function shouldShowDownloadButton(job) {
    return job.status === REPORT_JOB_STATUS_SUCCEEDED && job.report_name !== RURAL_WORKSHEET_UPLOAD;
}

function isJobSelected(reportId) {
    return selectedReportJobs.value.includes(reportId);
}

function toggleReportSelection(reportId) {
    const report = reportJobs.value.find(job => job.id === reportId);
    if (!canSelectForDelete(report)) {
        return;
    }
    if (!report.selected && isJobSelected(reportId)) {
        selectedReportJobs.value = selectedReportJobs.value.filter(id => id !== reportId);
    }
    else if (report.selected && !isJobSelected(reportId)) {
        selectedReportJobs.value.push(reportId);
    }
}

function toggleSelectAllJobs() {
    const addReports = JSON.parse(JSON.stringify(!allJobsSelected.value));
    selectedReportJobs.value = selectedReportJobs.value.filter(id => !reportIds.value.includes(id));
    if (addReports) {
        selectedReportJobs.value = selectedReportJobs.value.concat(reportIds.value.filter(id => canSelectForDelete(reportJobs.value.find(job => job.id === id))));
    }
    reportJobs.value = reportJobs.value.map((job) => {
        if (addReports && canSelectForDelete(job)) {
            job.selected = true;
            return job;
        }
        job.selected = false;
        return job;
    });
}

function sortJobs(field) {
    if (orderBy === field) {
        orderDirection = orderDirection === 'ASC' ? 'DESC' : 'ASC';
    }
    else {
        orderBy = field;
        orderDirection = 'ASC';
    }
    loadReportJobs(pageSize, pageNumber.value, orderBy, orderDirection);
}

function sortClass(field) {
    if (orderBy === field) {
        return orderDirection === 'ASC' ? 'asc' : 'desc';
    }
}

</script>

<template>
    <div>
        <div class="my-reports qv-flex-column">
            <div class="qv-flex-row">
                <h2 data-cy="my-reports-heading">My Reports</h2>
                <button
                    data-cy="my-reports-refresh-button"
                    @click="goToPage(pageNumber)"
                    class="material-symbols-outlined mdl-button"
                >
                    refresh
                </button>
            </div>
            <div class="qv-flex-row" style="position:relative;flex:1;">
                <div v-if="loading" class="spinner" />
                <div class="qv-flex-column" style="flex:1;">
                    <table
                        class="report-job-table"
                        data-cy="my-reports-table"
                    >
                        <thead>
                            <tr>
                                <th>
                                    <input
                                        type="checkbox"
                                        class="select-report-checkbox"
                                        :checked="allJobsSelected"
                                        @click="toggleSelectAllJobs()"
                                    />
                                </th>
                                <th
                                    style="text-align:left;"
                                    class="sortable"
                                    :class="sortClass('reportName')"
                                    @click="sortJobs('reportName')"
                                >
                                    Name
                                </th>
                                <th>Type</th>
                                <th
                                    class="sortable"
                                    :class="sortClass('reportFileSize')"
                                    @click="sortJobs('reportFileSize')"
                                >
                                    Size
                                </th>
                                <th>Run Time</th>
                                <th
                                    class="sortable"
                                    :class="sortClass('createdAt')"
                                    @click="sortJobs('createdAt')"
                                >
                                    Scheduled Date
                                </th>
                                <th
                                    class="sortable"
                                    :class="sortClass('status')"
                                    @click="sortJobs('status')"
                                >
                                    Status
                                </th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr
                                v-for="(reportJob, index) in reportJobs"
                                data-cy="my-reports-table-row"
                                :key="index"
                                style="text-align:center;"
                            >
                                <td>
                                    <input
                                        :id="reportJob.id"
                                        type="checkbox"
                                        class="select-report-checkbox"
                                        v-model="reportJob.selected"
                                        :disabled="!canSelectForDelete(reportJob)"
                                        @change="toggleReportSelection(reportJob.id)"
                                        data-cy="report-checkbox"
                                    />
                                </td>
                                <td data-cy="report-name" class="report-name">
                                    <label :for="reportJob.id" @click="toggleReportSelection(reportJob.id)">
                                        {{ reportJob.report_file_name || reportJob.report_display_name }}
                                    </label>
                                </td>
                                <td>{{ (reportJob.report_file_format || '').toUpperCase() }}</td>
                                <td>{{ formatFileSize(parseInt(reportJob.report_file_size)) }}</td>
                                <td>{{ returnRunTime(reportJob) }}</td>
                                <td data-cy="report-date" :title="moment(reportJob.created_at).local().format('DD/MM/YYYY HH:mm:ss')">{{ moment(reportJob.created_at).local().format('D MMMM YYYY h:mm a') }}</td>
                                <td data-cy="report-status" :class="{ 'red-text': reportJob.status === REPORT_JOB_STATUS_FAILED }">
                                    {{ statusString(reportJob) }}
                                </td>
                                <td><a v-if="shouldShowDownloadButton(reportJob)" class="download-button" :href="getReportJobFileUrl(reportJob)" target="_blank">DOWNLOAD</a></td>
                            </tr>
                        </tbody>
                    </table>
                    <div>
                        <div style="margin-bottom: 0.5rem;">{{ reportJobResultMessage }}</div>
                        <button :disabled="!selectedReportJobs.length" class="delete-button" @click="handleDelete" data-cy="delete-report-button">DELETE</button>
                    </div>
                    <!-- Pagination -->
                    <div v-if="totalPages > 1" class="pagination">
                        <span
                            v-if="pageNumber > 1"
                            class="page-number"
                            @click="goToPage(pageNumber-1)"
                        >
                            &lt;
                        </span>
                        <template v-for="(page, index) in pages">
                            <span
                                class="page-number"
                                :class="{'active': page === pageNumber}"
                                @click="goToPage(page)"
                                :key="index"
                            >
                                {{ page }}
                            </span>
                        </template>
                        <span
                            v-if="pageNumber < totalPages"
                            class="page-number"
                            @click="goToPage(pageNumber+1)"
                        >
                            &gt;
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <alert-modal
            v-if="modal.isOpen"
            :caution="modal.mode==='warning'"
            :success="modal.mode==='success'"
            :warning="modal.mode==='error'"
            data-cy="my-reports-modal"
        >
            <h1>{{ modal.heading }}</h1>
            <p
                v-if="modal.message !== ''"
                style="white-space:pre-wrap;"
            >{{ modal.message.trim() }}</p>
            <loading-spinner v-if="deleting" />
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modalCancel"
                        data-cy="report-modal-cancel"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modalConfirm"
                        data-cy="report-modal-confirm"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
        </alert-modal>
    </div>
</template>

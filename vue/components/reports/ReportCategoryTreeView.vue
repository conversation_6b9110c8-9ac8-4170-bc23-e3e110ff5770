<script setup>
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { objectToSortedArray } from '../../utils/common';
import ReportCategoryTreeView from './ReportCategoryTreeView.vue';
import { currentReport } from './utils';

const router = useRouter();
const route = useRoute();

const props = defineProps({
    reportCategory: {
        type: Object,
        required: true,
    },
    treeLevel: {
        type: Number,
        default: 0,
    },
});

const expandByDefault = (
    props.treeLevel === 0
    && props.reportCategory.categories
);
const expanded = ref(expandByDefault);

onMounted(() => {
    if (sectionHasSelectedReport()) {
        expand();
    }
});

// Define emits
const emit = defineEmits(['expand']);

function sectionHasSelectedReport() {
    if (!props.reportCategory.reports) {
        return false;
    }
    return Object.keys(props.reportCategory.reports).some(report => 
        route.path === `/reports/${props.reportCategory.reports[report].name}`
    );
}

function expand() {
    expanded.value = true;
    if (props.treeLevel > 0) {
        emit('expand');
    }
}

function viewCriteria(report) {
    const path = `/reports/${report.name}`;
    if (route.path !== path) {
        currentReport.value = report;
        router.push({ path: path });
    }
}

</script>

<template>
    <div class="report-category qv-flex-column">
        <div
            class="report-category-name"
            :class="{ expanded }"
            :style="{ 'padding-left': `${(treeLevel + 1) * 10}px` }"
            @click="expanded = !expanded"
            data-cy="report-category"
        >
            {{ reportCategory.display_name }}
        </div>
        <div class="qv-flex-column report-menu-section" :class="{ expanded }" style="gap:0;">
            <template v-if="reportCategory.reports">
                <div
                    v-for="report in objectToSortedArray(reportCategory.reports, 'display_name')"
                    :key="report.id"
                    :class="{ 'active': $route.path === `/reports/${report.name}` }"
                    :style="{ 'padding-left': `${(treeLevel + 3) * 10}px` }"
                    class="report-name"
                    @click="viewCriteria(report)"
                    data-cy="report-item"
                >
                    {{ report.display_name }}
                </div>
            </template>
            <template v-if="reportCategory.categories">
                <report-category-tree-view
                    v-for="category in objectToSortedArray(reportCategory.categories, 'display_name')"
                    :key="category.id"
                    :treeLevel="treeLevel + 1"
                    :reportCategory="category"
                    @expand="expand"
                />
            </template>
        </div>
    </div>
</template>

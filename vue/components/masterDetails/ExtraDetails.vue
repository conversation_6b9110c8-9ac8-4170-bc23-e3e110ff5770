<template xmlns:v-on="http://www.w3.org/1999/xhtml">
    <!-- eslint-disable max-len -->
    <div class="md-full md-bodyWrapper">
        <div class="md-table mdl-shadow--2dp">
            <h2 data-cy="ed-card-title">Extra Property Details</h2>
            <div
                class="expandAll"
                :class="[tabState === 'open' ? 'down' : '']"
                @click="tabState = (tabState === 'open') ? 'closed' : 'open'"
            >
                <span
                    title="Expand Form"
                    class="mdl-button mdl-js-button mdl-button--icon"
                ><i class="material-icons md-dark"></i></span>
            </div>
            <div v-if="hasUpdateInformation" class="updateInformation">
                <ul>
                    <li>Last Updated By: <span>{{lastUpdatedBy}}</span></li>
                    <li>On: <span>{{lastUpdatedDate}}</span></li>
                </ul>
            </div>
            <ul :class="[tabState === 'open' ? 'hide' : 'QVHV-tabs']">
                <li
                    class="QVHVTab-1"
                    data-tab="QVHVTab-1"
                    data-container="monarchdetails"
                    @click="currentTab = 'MonarchDetails'"
                    data-cy="cy-ed-monarch-details"
                >
                    <span
                        :class="{
                            'is-active': currentTab === 'MonarchDetails' || tabState === 'open',
                        }"
                    >
                        Monarch Details
                    </span>
                </li>
                <li
                    v-if="isInternalUser"
                    class="QVHVTab-2"
                    data-tab="QVHVTab-2"
                    data-container="investmentdetails"
                    @click="currentTab = 'InvestmentDetails'"
                >
                    <span
                        :class="{
                            'is-active': currentTab === 'InvestmentDetails' || tabState === 'open',
                        }"
                    >
                        Investment Details
                    </span>
                </li>
                <li
                    v-if="isInternalUser"
                    class="QVHVTab-3"
                    data-tab="QVHVTab-3"
                    data-container="risksandhazards"
                    data-cy="cy-ed-risks-and-hazards"
                    @click="currentTab = 'RisksAndHazards'"
                >
                    <span
                        :class="{
                            'is-active': currentTab === 'RisksAndHazards' || tabState === 'open',
                        }"
                    >
                        Risks and Hazards
                    </span>
                </li>
                <li
                    v-if="!hasPropertyDetail && isInternalUser"
                    class="QVHVTab-4"
                    data-tab="QVHVTab-4"
                    data-container="propertyplus"
                    @click="currentTab = 'PropertyPlus'"
                >
                    <span
                        :class="{
                            'is-active': currentTab === 'PropertyPlus' || tabState === 'open',
                        }"
                    >
                        Property Plus
                    </span>
                </li>
                <hr
                    align="left"
                    :class="classTabBar"
                >
            </ul>
            <div
                v-show="currentTab === 'MonarchDetails' || tabState === 'open'"
                class="QVHV-Container monarchdetails active"
                :class="{'canOpener': tabState === 'open'}"
            >
                <ul class="QVHV-tabs">
                    <li><span class="is-active">Monarch Details</span></li>
                    <hr align="left">
                </ul>

                <div
                    v-if="showPropertyPlusTemplate"
                    class="QVHV-formSection legacy-scope"
                >
                    <div class="righty">
                        <button
                            class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect edit-property-detail"
                            v-if="canEditPropertyDetail"
                            @click="editPropertyDetail"
                            data-cy="edit-property-detail-button"
                        >
                            Edit
                        </button>
                    </div>
                    <property-details-read-only
                        v-if="!loading && propertyDetail"
                        :property-detail="propertyDetail"
                        :qv-property-details="qvPropertyDetails"
                        :zone-info="zoneInfo"
                        :show-derived-dvr-fields="true"
                        :show-buildings-and-spaces="true"
                    />
                </div>
            </div>

            <!---------------------------------------->
            <!-- EXTRA DETAILS MRI-1717 STARTS ------->
            <!---------------------------------------->
            <div
                v-if="isInternalUser"
                v-show="!hasPropertyDetail && (currentTab === 'PropertyPlus' || tabState == 'open')"
                class="QVHV-Container propertyplus active"
                :class="{'canOpener': tabState === 'open'}"
            >
                <ul class="QVHV-tabs">
                    <li><span class="is-active">Property Plus</span></li>
                    <hr align="left">
                </ul>
                <div
                    v-if="showPropertyPlusTemplate"
                    class="QVHV-formSection"
                >
                    <div class="advSearch-row">
                        <text-input
                            id="numberOfSingleBedroomsPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.numberOfSingleBedrooms"
                            attr-name="numberOfSingleBedrooms"
                            field-type="number"
                            icon-class="icons8-bed-filled"
                            label="Single Bedrooms"
                            component-name="property-plus"
                            maxlength="2"
                        />
                        <text-input
                            id="numberOfDoubleBedroomsPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.numberOfDoubleBedrooms"
                            attr-name="numberOfDoubleBedrooms"
                            field-type="number"
                            icon-class="icons8-bed-filled"
                            label="Double Bedrooms"
                            component-name="property-plus"
                            maxlength="2"
                        />
                        <text-input
                            id="numberOfHomeOfficesOrStudiesPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.numberOfHomeOfficesOrStudies"
                            attr-name="numberOfHomeOfficesOrStudies"
                            field-type="number"
                            icon-class="icons8-pc-on-desk-filled"
                            label="Home Office or Study"
                            component-name="property-plus"
                            maxlength="2"
                        />
                        <text-input
                            id="numberOfBathroomsPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.numberOfBathrooms"
                            attr-name="numberOfBathrooms"
                            field-type="number"
                            icon-class="icons8-total-bathrooms-filled"
                            label="Bathrooms"
                            component-name="property-plus"
                            maxlength="2"
                        />
                        <text-input
                            id="effectiveFloorAreaPropPlus"
                            :curr-val="formatDecimal(qvPropertyDetails.effectiveLandArea,4)"
                            attr-name="effectiveLandArea"
                            field-type="number"
                            decimal="4"
                            icon-class="twentyfivePct righty icons8-site-coverage-new"
                            label="Effective Land Area (Ha)"
                            component-name="qv-property-details"
                        />
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                            id="mainBathroomAgePropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.mainBathroomAge"
                            icon-class="twentyfivePct icons8-shower-and-tub-filled"
                            component-name="property-plus"
                            attr-name="mainBathroomAge"
                            filter-id="propPlus-mainBathroomAge"
                            label="Main Bathroom Age"
                            select-class="monarch-multiselect-mainBathroomAge"
                            data-to-fetch="Age"
                        />
                        <valuation-multi-select-filter
                            id="mainBathroomQualityPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.mainBathroomQuality"
                            icon-class="twentyfivePct icons8-rating-filled"
                            component-name="property-plus"
                            attr-name="mainBathroomQuality"
                            filter-id="propPlus-mainBathroomQuality"
                            label="Main Bathroom Quality"
                            select-class="monarch-multiselect-mainBathroomQuality"
                            data-to-fetch="Quality"
                        />
                        <valuation-multi-select-filter
                            id="ensuiteAgePropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.ensuiteAge"
                            icon-class="twentyfivePct icons8-shower-and-tub-filled"
                            component-name="property-plus"
                            attr-name="ensuiteAge"
                            filter-id="propPlus-ensuiteAge"
                            label="Ensuite Age"
                            select-class="monarch-multiselect-ensuiteAge"
                            data-to-fetch="Age"
                        />
                        <valuation-multi-select-filter
                            id="ensuiteQualityPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.ensuiteQuality"
                            icon-class="twentyfivePct icons8-rating-filled"
                            component-name="property-plus"
                            attr-name="ensuiteQuality"
                            filter-id="propPlus-ensuiteQuality"
                            label="Ensuite Quality"
                            select-class="monarch-multiselect-ensuiteQuality"
                            data-to-fetch="Quality"
                        />
                    </div>

                    <!-- NEW ROW -->
                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                            id="propertyPluskitchenAgePropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.kitchenAge"
                            icon-class="twentyfivePct icons8-fridge-filled"
                            component-name="property-plus"
                            attr-name="kitchenAge"
                            filter-id="propPlus-kitchenAge"
                            label="Kitchen Age"
                            select-class="monarch-multiselect-kitchenAge"
                            data-to-fetch="Age"
                        />
                        <valuation-multi-select-filter
                            id="kitchenQualityPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.kitchenQuality"
                            icon-class="twentyfivePct icons8-rating-filled"
                            component-name="property-plus"
                            attr-name="kitchenQuality"
                            filter-id="propPlus-kitchenQuality"
                            label="Kitchen Quality"
                            select-class="monarch-multiselect-kitchenQuality"
                            data-to-fetch="Quality"
                        />
                        <valuation-multi-select-filter
                            id="redecorationAgePropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.redecorationAge"
                            icon-class="twentyfivePct icons8-roller-brush-filled"
                            component-name="property-plus"
                            attr-name="redecorationAge"
                            filter-id="propPlus-redecorationAge"
                            label="Redecoration Age"
                            select-class="monarch-multiselect-redecorationAge"
                            data-to-fetch="Age"
                        />
                        <valuation-multi-select-filter
                            id="internalConditionPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.internalCondition"
                            icon-class="twentyfivePct icons8-rating-filled"
                            component-name="property-plus"
                            attr-name="internalCondition"
                            filter-id="propPlus-internalCondition"
                            label="Interior Condition"
                            select-class="monarch-multiselect-internalCondition"
                            data-to-fetch="Quality"
                        />
                    </div>

                    <!-- NEW ROW -->
                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                            id="heatingTypePropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.heatingType"
                            icon-class="twentyfivePct icons8-fire-station-filled"
                            component-name="property-plus"
                            attr-name="heatingType"
                            filter-id="propPlus-heatingType"
                            label="Heating Type"
                            select-class="monarch-multiselect-heatingType"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="HeatingType"
                        />
                        <valuation-multi-select-filter
                            id="insulationPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.insulation"
                            icon-class="twentyfivePct icons8-heating-room-filled"
                            component-name="property-plus"
                            attr-name="insulation"
                            filter-id="propPlus-insulation"
                            label="Insulation"
                            select-class="monarch-multiselect-insulation"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="Insulation"
                        />
                        <valuation-multi-select-filter
                            id="plumbingAgePropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.plumbingAge"
                            icon-class="twentyfivePct icons8-piping-filled"
                            component-name="property-plus"
                            attr-name="plumbingAge"
                            filter-id="propPlus-plumbingAge"
                            label="Plumbing Age"
                            select-class="monarch-multiselect-plumbingAge"
                            data-to-fetch="Age"
                        />
                        <valuation-multi-select-filter
                            id="wiringAgePropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.wiringAge"
                            icon-class="twentyfivePct icons8-plug-4-filled"
                            component-name="property-plus"
                            attr-name="wiringAge"
                            filter-id="propPlus-wiringAge"
                            label="Wiring Age"
                            select-class="monarch-multiselect-wiringAge"
                            data-to-fetch="Age"
                        />
                    </div>

                    <!-- NEW ROW -->
                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                            id="doubleGlazingPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.doubleGlazing"
                            icon-class="twentyfivePct icons8-closed-window-filled"
                            component-name="property-plus"
                            attr-name="doubleGlazing"
                            filter-id="propPlus-doubleGlazing"
                            label="Double Glazing"
                            select-class="monarch-multiselect-doubleGlazing"
                            data-to-fetch="DoubleGlazing"
                        />
                        <valuation-multi-select-filter
                            id="alternativeEnergyPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.alternativeEnergy"
                            icon-class="twentyfivePct icons8-solar-panel-filled"
                            component-name="property-plus"
                            attr-name="alternativeEnergy"
                            filter-id="propPlus-alternativeEnergy"
                            label="Alternative Energy"
                            select-class="monarch-multiselect-alternativeEnergy"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="AlternativeEnergy"
                        />
                        <valuation-multi-select-filter
                            id="studHeightPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.studHeight"
                            icon-class="twentyfivePct icons8-living-areas-new"
                            component-name="property-plus"
                            attr-name="studHeight"
                            filter-id="propPlus-studHeight"
                            label="Stud Height"
                            select-class="monarch-multiselect-studHeight"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="StudHeight"
                        />
                        <valuation-multi-select-filter
                            id="additionalFeaturesPropPlus"
                            :curr-val="qvPropertyDetails.aggregateUnitDetails.additionalFeatures"
                            icon-class="twentyfivePct icons8-temperature-inside-filled"
                            component-name="property-plus"
                            attr-name="additionalFeatures"
                            filter-id="propPlus-additionalFeatures"
                            label="Other Features"
                            select-class="monarch-multiselect-additionalFeatures"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="AdditionalFeatures"
                        />
                    </div>
                </div>
            </div>

            <!---------------------------------------->
            <!-- INVESTMENT DETAILS MRI-3241 STARTS -->
            <!---------------------------------------->
            <div
                v-if="isInternalUser"
                v-show="currentTab === 'InvestmentDetails' || tabState === 'open'"
                class="QVHV-Container investmentdetails active"
                :class="[tabState === 'open' ? 'canOpener' : '']"
            >
                <ul class="QVHV-tabs">
                    <li><span class="is-active">Investment Details</span></li> <hr align="left">
                </ul>
                <div
                    v-if="showPropertyPlusTemplate"
                    class="QVHV-formSection"
                >
                    <!-- RENT ROW -->
                    <div class="advSearch-row">
                        <div class="advSearch-group twentyPct icons8-sell-property-filled">
                            <h4 class="investmentUnit">
                                Total Property Rent
                            </h4>
                        </div>
                        <div class="advSearch-group tenPct" />
                        <text-input
                            id="rentPropPlus"
                            :curr-val="formatPrice(rent.amount,'$0,0')"
                            attr-name="rentPropPlus"
                            field-type="price"
                            icon-class="fifteenPct icons8-sell-property-filled calculated"
                            label="Estimated ($ Per Week)"
                            component-name="investment-details"
                            format="$0,0"
                            read-only="true"
                        />
                        <text-input
                            id="rentBasisPropPlus"
                            :curr-val="rent.basis"
                            attr-name="rentBasisPropPlus"
                            field-type="text"
                            icon-class="twentyfivePct icons8-legal-description calculated"
                            label="Rental Estimate Basis"
                            component-name="investment-details"
                            read-only="true"
                        />
                        <text-input
                            id="weeklyRentalAmountPropPlus"
                            :curr-val="formatPrice(qvPropertyDetails.aggregateUnitDetails.rentalAmount.weeklyRentalAmount,'$0,0')"
                            attr-name="weeklyRentalAmount"
                            parent-attr-name="rentalAmount"
                            field-type="price"
                            icon-class="fifteenPct icons8-sell-property-filled"
                            label="Actual ($ Per Week)"
                            format="$0,0"
                            component-name="property-plus"
                            maxlength="8"
                        />
                        <text-input
                            id="knownDateAmountPropPlus"
                            :curr-val="formatDate(qvPropertyDetails.aggregateUnitDetails.rentalAmount.knownDate,'MM/YYYY')"
                            attr-name="knownDate"
                            parent-attr-name="rentalAmount"
                            field-type="date"
                            icon-class="fifteenPct icons8-calendar-filled"
                            label="Known (MM/YYYY)"
                            format="MM/YYYY"
                            maxlength="7"
                            component-name="property-plus"
                        />
                    </div>

                    <!-- NEW ROW -->
                    <div
                        v-show="!hasPropertyDetail"
                        v-for="(unit, key) in qvPropertyDetails.unitDetails"
                        :key="key"
                        class="advSearch-row"
                    >
                        <div class="advSearch-Subrow">
                            <div
                                v-if="property.units > 4"
                                class="advSearch-group twentyPct icons8-room-filled"
                            >
                                <h4>Combined Property Details</h4>
                            </div>
                            <valuation-multi-select-filter
                                v-if="property.units <= 4"
                                :id="'unitTypeUnitDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="unit.unitType"
                                icon-class="twentyPct icons8-room-filled"
                                component-name="unit-details"
                                attr-name="unitType"
                                :filter-id="'unit-details-unitType'+(key+1)"
                                :label="'Unit '+unit.unitNumber"
                                :select-class="'monarch-multiselect-unit-details-unitType'+(key+1)"
                                data-to-fetch="UnitType"
                            />
                            <div class="advSearch-group tenPct" />
                            <text-input
                                :id="'effectiveFloorAreaUnitUnitDetails'+(key+1)"
                                :curr-val="unit.effectiveFloorArea"
                                :obj-key="key+1"
                                attr-name="effectiveFloorArea"
                                field-type="number"
                                maxlength="7"
                                icon-class="tenPct icons8-floor-plan-filled"
                                label="Floor Area"
                                component-name="unit-details"
                            />
                            <text-input
                                :id="'numberOfSingleBedroomsUnitDetails'+(key+1)"
                                :curr-val="unit.numberOfSingleBedrooms"
                                :obj-key="key+1"
                                attr-name="numberOfSingleBedrooms"
                                field-type="number"
                                icon-class="tenPct icons8-bed-filled"
                                label="Single Bed"
                                component-name="unit-details"
                                maxlength="2"
                            />
                            <text-input
                                :id="'numberOfDoubleBedroomsUnitDetails'+(key+1)"
                                :curr-val="unit.numberOfDoubleBedrooms"
                                :obj-key="key+1"
                                attr-name="numberOfDoubleBedrooms"
                                field-type="number"
                                icon-class="tenPct icons8-bed-filled"
                                label="Double Bed"
                                component-name="unit-details"
                                maxlength="2"
                            />
                            <text-input
                                :id="'numberOfBathroomsUnitDetails'+(key+1)"
                                :curr-val="unit.numberOfBathrooms"
                                :obj-key="key+1"
                                attr-name="numberOfBathrooms"
                                field-type="number"
                                icon-class="tenPct icons8-total-bathrooms-filled"
                                label="Bathrooms"
                                component-name="unit-details"
                                maxlength="2"
                            />
                            <text-input
                                :id="'weeklyRentalAmountUnitDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="formatPrice(unit.rentalAmount.weeklyRentalAmount,'$0,0')"
                                attr-name="weeklyRentalAmount"
                                parent-attr-name="rentalAmount"
                                field-type="price"
                                icon-class="fifteenPct icons8-sell-property-filled"
                                label="Rental Income ($/week)"
                                component-name="unit-details"
                                format="$0,0"
                                maxlength="8"
                            />
                            <text-input
                                :id="'knownDateUnitDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="formatDate(unit.rentalAmount.knownDate,'MM/YYYY')"
                                attr-name="knownDate"
                                parent-attr-name="rentalAmount"
                                field-type="date"
                                icon-class="fifteenPct icons8-calendar-filled"
                                label="Known (MM/YYYY)"
                                format="MM/YYYY"
                                maxlength="7"
                                component-name="unit-details"
                            />
                        </div>
                        <label
                            for="Investment Unit Extras 1"
                            class="investmentExtras-trigger"
                            @click="showUnitExtraDetails(key)"
                        >Add Extra Details<i class="material-icons">&#xE5C5;</i></label>
                        <!-- OPTIONAL/HIDDEN FIELDS ROW -->
                        <div
                            :id="'extraDetailsUnit'+key"
                            class="advSearch-Subrow"
                        >
                            <div class="advSearch-group twentyPct" />
                            <valuation-multi-select-filter
                                :id="'kitchenAgeUnitDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="unit.kitchenAge"
                                icon-class="twentyPct icons8-fridge-filled"
                                component-name="unit-details"
                                attr-name="kitchenAge"
                                :filter-id="'unit-details-kitchenAge'+(key+1)"
                                label="Kitchen Age"
                                :select-class="'monarch-multiselect-unit-details-kitchenAge'+(key+1)"
                                data-to-fetch="Age"
                            />
                            <valuation-multi-select-filter
                                :id="'heatingTypeUnitDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="unit.heatingType"
                                icon-class="twentyPct icons8-fire-station-filled"
                                component-name="unit-details"
                                attr-name="heatingType"
                                :filter-id="'unit-details-heatingType'+(key+1)"
                                label="Heating Type"
                                :on-dropdown-hide="saveOnDropDownHide"
                                multiple="true"
                                :select-class="'monarch-multiselect-unit-details-heatingType'+(key+1)"
                                data-to-fetch="HeatingType"
                            />
                            <valuation-multi-select-filter
                                :id="'insulationUnitDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="unit.insulation"
                                icon-class="twentyPct icons8-heating-room-filled"
                                component-name="unit-details"
                                attr-name="insulation"
                                :filter-id="'unit-details-insulation'+(key+1)"
                                label="Insulation"
                                :select-class="'monarch-multiselect-unit-details-insulation'+(key+1)"
                                :on-dropdown-hide="saveOnDropDownHide"
                                multiple="true"
                                data-to-fetch="Insulation"
                            />
                            <valuation-multi-select-filter
                                :id="'doubleGlazingDetails'+(key+1)"
                                :obj-key="key+1"
                                :curr-val="unit.doubleGlazing"
                                icon-class="twentyPct icons8-closed-window-filled"
                                component-name="unit-details"
                                attr-name="doubleGlazing"
                                :filter-id="'unit-details-doubleGlazing'+(key+1)"
                                label="Glazing"
                                :select-class="'monarch-multiselect-unit-details-doubleGlazing'+(key+1)"
                                data-to-fetch="DoubleGlazing"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- RISKS AND HAZARDS VIEW STARTS -->

            <div
                v-if="isInternalUser"
                v-show="currentTab=='RisksAndHazards' || tabState == 'open'"
                class="QVHV-Container risksandhazards active"
                :class="{'canOpener': tabState === 'open'}"
            >
                <ul class="QVHV-tabs">
                    <li><span class="is-active">Risks and Hazards</span></li>
                    <hr align="left">
                </ul>

                <div
                    v-if="showPropertyPlusTemplate"
                    class="QVHV-formSection"
                    data-cy="ed-property-plus-section"
                >
                    <div class="advSearch-row"  v-if="zoneInfoLoaded">
                        <text-input
                            id="planNumberPropPlus"
                            :curr-val="property.planNumber"
                            attr-name="planNumberPropPlus"
                            field-type="text"
                            icon-class="fifteenPct icons8-legal-description calculated"
                            label="Plan #"
                            component-name="property-plus"
                            read-only="true"
                        />
                        <div class="advSearch-group tenPct" />
                        <text-input
                            id="numberOfSites"
                            :curr-val="zoneInfo.numberOfSites"
                            attr-name="numberOfSites"
                            field-type="number"
                            icon-class="twelvefivePct icons8-site-coverage-new"
                            label="Number of Sites"
                            component-name="qv-zone-info"
                            maxlength="5"
                        />
                        <text-input
                            id="maxSites"
                            :curr-val="zoneInfo.maxSites"
                            attr-name="maxSites"
                            field-type="number"
                            icon-class="twelvefivePct icons8-lot-position-filled"
                            label="Maximum Sites"
                            component-name="qv-zone-info"
                            maxlength="5"
                        />
                        <zone-single-select
                            v-if="proposedZoneOptionsLoaded"
                            :value="zoneInfo.proposedZone"
                            :options="proposedZoneOptions"
                            fieldName="proposedZone"
                            title="Proposed Zone"
                            filter-id="proposedZone">
                        </zone-single-select>
                        <zone-single-select
                            v-if="zoneOverlayOptionsLoaded"
                            id="zoneOverlay"
                            :value="zoneInfo.zoneOverlay"
                            :options="zoneOverlayOptions"
                            fieldName="zoneOverlay"
                            title="Zone Overlay"
                            filter-id="zoneOverlay">
                        </zone-single-select>
                    </div>
                    <div class="advSearch-row">
                        <text-input
                            id="ratingPropPlus"
                            :curr-val="formatPrice(earthquake.rating,'0%')"
                            attr-name="rating"
                            field-type="price"
                            format="0%"
                            maxlength="3"
                            icon-class="fifteenPct icons8-earthquake-symbol"
                            label="Actual Earthquake Rating"
                            component-name="hazards-earthquake"
                        />
                        <div class="advSearch-group tenPct" />
                        <valuation-multi-select-filter
                            id="earthquakeRatingPropPlus"
                            :curr-val="getHazardsClassifications('EarthquakeRating',false)"
                            icon-class="twentyfivePct icons8-earthquake-symbol"
                            component-name="property-plus-hazards"
                            attr-name="EarthquakeRating"
                            filter-id="propPlus-EarthquakeRating"
                            label="Earthquake Rating Range"
                            select-class="monarch-multiselect-EarthquakeRating"
                            data-to-fetch="EarthquakeRating"
                        />
                        <valuation-multi-select-filter
                            id="sourcePropPlus"
                            :curr-val="earthquake.source"
                            :icon-class="earthquake.sourceClass + ' twelvefivePct'"
                            component-name="hazards-earthquake"
                            attr-name="source"
                            filter-id="propPlus-EarthquakeRatingAssessor"
                            label="Earthquake Rating Assessor"
                            select-class="monarch-multiselect-EarthquakeRatingAssessor"
                            data-to-fetch="EarthquakeRatingAssessor"
                            style="padding-right: 0px; width: 14%"
                        />
                        <text-input-2
                            id="remedyYearPropPlus"
                            :curr-val="qvPropertyDetails.remedyYear"
                            attr-name="remedyYear"
                            field-type="number"
                            icon-class="twelvefivePct"
                            format="0"
                            minlength="4"
                            maxlength="4"
                            label="Remedy Deadline"
                            data-to-fetch="remedyYear"
                            component-name="qv-property-details"
                            :title="remedyYearTooltipText"
                            place-holder="YYYY"
                            data-cy="ed-remedy-year"
                            style="padding: 0 1rem; width: 10%; margin-right: 1.4rem"
                        />
                        <valuation-multi-select-filter
                            id="liquefactionRatingPropPlus"
                            :curr-val="getHazardsClassifications('LiquefactionRating',false)"
                            icon-class="twentyfivePct icons8-liquifaction"
                            component-name="property-plus-hazards"
                            attr-name="LiquefactionRating"
                            filter-id="propPlus-LiquefactionRating"
                            label="Liquefaction (TC Rating)"
                            select-class="monarch-multiselect-LiquefactionRating"
                            data-to-fetch="LiquefactionRating"
                        />
                    </div>
                    <div class="advSearch-row">
                        <text-input
                            id="averageDailySunshineHoursPropPlus"
                            :curr-val="formatDecimal(qvPropertyDetails.averageDailySunshineHours,2)"
                            attr-name="averageDailySunshineHours"
                            field-type="number"
                            decimal="2"
                            icon-class="fifteenPct icons8-sun-rays"
                            label="Sunshine Hours (Avg)"
                            component-name="qv-property-details"
                        />
                        <div class="advSearch-group tenPct" />
                        <valuation-multi-select-filter
                            id="weatherTightnessIssuePropPlus"
                            :curr-val="getHazardsClassifications('WeatherTightnessIssue',false)"
                            icon-class="twentyfivePct icons8-leaky-home"
                            component-name="property-plus-hazards"
                            attr-name="WeatherTightnessIssue"
                            filter-id="propPlus-WeatherTightnessIssue"
                            label="Weather-tightness Issues"
                            select-class="monarch-multiselect-WeatherTightnessIssue"
                            data-to-fetch="WeatherTightnessIssue"
                        />
                        <valuation-multi-select-filter
                            id="otherConstructionIssuePropPlus"
                            :curr-val="getHazardsClassifications('OtherConstructionIssue',true)"
                            icon-class="twentyfivePct icons8-living-areas-new"
                            component-name="property-plus-hazards"
                            attr-name="OtherConstructionIssue"
                            filter-id="propPlus-OtherConstructionIssue"
                            label="Other Construction Details"
                            select-class="monarch-multiselect-OtherConstructionIssue"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="OtherConstructionIssue"
                        />
                        <valuation-multi-select-filter
                            id="contaminationIssuePropPlus"
                            :curr-val="getHazardsClassifications('ContaminationIssue',true)"
                            icon-class="twentyfivePct icons8-oil-barrel"
                            component-name="property-plus-hazards"
                            attr-name="ContaminationIssue"
                            filter-id="propPlus-ContaminationIssue"
                            label="Contamination"
                            select-class="monarch-multiselect-ContaminationIssue"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="ContaminationIssue"
                        />
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                            id="planningRestrictionPropPlus"
                            :curr-val="getHazardsClassifications('PlanningRestriction',true)"
                            icon-class="twentyfivePct icons8-brake-warning-filled"
                            component-name="property-plus-hazards"
                            attr-name="PlanningRestriction"
                            filter-id="propPlus-PlanningRestriction"
                            label="Plan Restrictions"
                            select-class="monarch-multiselect-PlanningRestriction"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="PlanningRestriction"
                        />
                        <valuation-multi-select-filter
                            id="floodZonePropPlus"
                            :curr-val="getHazardsClassifications('FloodZone',false)"
                            icon-class="twentyfivePct  icons8-floods-filled"
                            component-name="property-plus-hazards"
                            attr-name="FloodZone"
                            filter-id="propPlus-FloodZone"
                            label="Flood Zone"
                            select-class="monarch-multiselect-FloodZone"
                            data-to-fetch="FloodZone"
                        />
                        <valuation-multi-select-filter
                            id="tsunamiRiskPropPlus"
                            :curr-val="getHazardsClassifications('TsunamiRisk',false)"
                            icon-class="twentyfivePct icons8-tsunami"
                            component-name="property-plus-hazards"
                            attr-name="TsunamiRisk"
                            filter-id="propPlus-TsunamiRisk"
                            label="Tsunami Risk"
                            select-class="monarch-multiselect-TsunamiRisk"
                            data-to-fetch="TsunamiRisk"
                        />
                        <valuation-multi-select-filter
                            id="landslipRiskPropPlus"
                            :curr-val="getHazardsClassifications('LandslipRisk',false)"
                            icon-class="twentyfivePct icons8-slip"
                            component-name="property-plus-hazards"
                            attr-name="LandslipRisk"
                            filter-id="propPlus-LandslipRisk"
                            label="Landslip Risk"
                            select-class="monarch-multiselect-LandslipRisk"
                            data-to-fetch="LandslipRisk"
                        />
                    </div>
                    <div class="advSearch-row">
                        <valuation-multi-select-filter
                            id="localityIssuesPropPlus"
                            :curr-val="getHazardsClassifications('LocalityIssue',true)"
                            icon-class="thirtysevenfivePct icons8-transmission-tower"
                            component-name="property-plus-hazards"
                            attr-name="LocalityIssue"
                            filter-id="propPlus-LocalityIssue"
                            label="Locality Attributes"
                            select-class="monarch-multiselect-LocalityIssue"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="LocalityIssue"
                        />
                        <valuation-multi-select-filter
                            id="heritageFeaturesPropPlus"
                            :curr-val="getHeritageFeaturesClassifications()"
                            icon-class="thirtysevenfivePct icons8-maori-new"
                            component-name="property-plus-heritage-features"
                            attr-name="SitesOfSpecialSignificance"
                            filter-id="propPlus-SitesOfSpecialSignificance"
                            label="Sites of Special Significance"
                            select-class="monarch-multiselect-SitesOfSpecialSignificance"
                            :on-dropdown-hide="saveOnDropDownHide"
                            multiple="true"
                            data-to-fetch="SitesOfSpecialSignificance"
                        />
                    </div>
                    <div class="advSearch-row">
                        <text-area-input
                            maxlength="5000"
                            :curr-val="qvPropertyDetails.hazardNotes"
                            attr-name="hazardNotes"
                            field-type="text"
                            icon-class="hundyPct hundyPct icons8-pencil"
                            label="Notes"
                            component-name="qv-property-details"
                        />
                    </div>
                </div>
            </div>
        </div>

        <div class="QVHV-buttons"
            v-if="isInternalUser"
            v-show="currentTab != 'MonarchDetails'"
        >
            <div class="QVHV-buttons-right property-plus-save">
                <button
                    data-cy="ed-save-button"
                    class="primary"
                    @click="saveQvProperty(true)"
                >
                    Save
                </button>
            </div>
        </div>
    </div>
</template>

<script>
/* eslint-disable max-len */
import { mapState } from 'vuex';
import usePropertyInfo from '../../composables/usePropertyInfo';

import { EventBus } from '../../EventBus';
import TextInput from '../filters/TextInput.vue';
import TextInput2 from '../filters/TextInput2.vue';
import TextAreaInput from '../filters/TextAreaInput.vue';
import ValuationMultiSelectFilter from '../filters/ValuationMultiSelectFilter.vue';
import {store} from '../../DataStore';
import formatUtils from '../../utils/FormatUtils';
import commonUtils from '../../utils/CommonUtils';
import ZoneSingleSelect from '../filters/ZoneSingleSelect.vue';
import _ from 'lodash';
const { refreshPropertyInfo } = usePropertyInfo();

export default {
    components: {
        TextInput,
        TextInput2,
        TextAreaInput,
        ValuationMultiSelectFilter,
        'property-details-read-only': () => import(/* webpackChunkName: "PropertyDetailsReadOnly" */ '../propertyDetails/PropertyDetailsReadOnly.vue'),
        ZoneSingleSelect
    },
    mixins: [formatUtils, commonUtils],
    props: ['property', 'rent'],
    data() {
        return {
            hasQvDetails: false,
            showPropertyPlusTemplate: true,
            refreshPropertyPlusTemplate: false,
            tabState: 'closed',
            currentTab: 'MonarchDetails',
            qvPropertyDetails: {
                id: this.property.id,
                qupid: this.property.qupid,
                effectiveDateOfCollection: null,
                effectiveLandArea: null,
                aggregateUnitDetails: {
                    unitNumber: null,
                    unitType: null,
                    numberOfSingleBedrooms: null,
                    numberOfDoubleBedrooms: null,
                    numberOfHomeOfficesOrStudies: null,
                    numberOfBathrooms: null,
                    mainBathroomAge: null,
                    mainBathroomQuality: null,
                    ensuiteAge: null,
                    ensuiteQuality: null,
                    kitchenAge: null,
                    kitchenQuality: null,
                    redecorationAge: null,
                    internalCondition: null,
                    heatingType: [],
                    insulation: [],
                    plumbingAge: null,
                    wiringAge: null,
                    doubleGlazing: null,
                    alternativeEnergy: [],
                    effectiveFloorArea: null,
                    studHeight: null,
                    additionalFeatures: null,
                    rentalAmount: {
                        source: null,
                        knownDate: null,
                        weeklyRentalAmount: null,
                    },
                },
                unitDetails: [],
                hazards: [],
                hazardNotes: null,
                heritageFeatures: [],
                averageDailySunshineHours: null,

            },
            zoneInfo: {
                qpid: null,
                numberOfSites: null,
                maxSites: null,
                zoneOverlay: {
                    category: '',
                    code: '',
                    description: '',
                    shortDescription: '',
                    sortOrder: '',
                    isActive: null,
                    parentClassification: null,
                    externalCode: null
                },
                proposedZone: {
                    category: '',
                    code: '',
                    description: '',
                    shortDescription: '',
                    sortOrder: '',
                    isActive: null,
                    parentClassification: null,
                    externalCode: null
                },
            },
            proposedZones: {},
            zoneOverlays: {},
            proposedZoneOptions: [],
            proposedZoneOptionsLoaded: false,
            zoneOverlayOptionsLoaded: false,
            zoneInfoLoaded: false,
            zoneOverlayOptions: [],
            lastUpdatedBy: '',
            lastUpdatedDate: '',
            hasUpdateInformation: false,
            remedyYearTooltipText: 'The year the remedial earthquake work must be done by',
            editableApportionmentCode: [0,2,5],
            editableExcludeCategoryPrefix: ['L', 'M', 'Z']
        };
    },
    computed: {
        ...mapState('currentPropertyDetails', [
            'propertyDetail',
            'loading',
        ]),
        ...mapState('userData', ['isInternalUser', 'userData']),
        hasPropertyDetail() {
            return this.propertyDetail && this.property.qupid == this.propertyDetail.qpid && this.propertyDetail.entityVersion > 0;
        },
        earthquake() {
            const self = this;
            const earthquake = {
                rating: null, source: null, classification: null, sourceClass: 'twentyfivePct icons8-contacts disabled',
            };
            const hazard = self.findHazards('EarthquakeRating');
            if (hazard && hazard.length > 0) {
                earthquake.rating = (typeof hazard[0].rating === 'number') ? hazard[0].rating / 100 : '';
                earthquake.source = hazard[0].source;
                earthquake.classification = hazard[0].classification;
                earthquake.sourceClass = 'twentyfivePct icons8-contacts';
            }
            return earthquake;
        },
        classTabBar() {
            const tabNumber = [
                'MonarchDetails',
                'InvestmentDetails',
                'RisksAndHazards',
                'PropertyPlus',
            ].indexOf(this.currentTab) + 1;
            return `QVHVTab-${tabNumber}`;
        },
        canEditPropertyDetail() {
            return this.isInternalUser && this.propertyDetail?.canBeEdited;
        }
    },
    created: function() {
        const self = this;
        self.setupZoningOptions();
    },
    mounted() {
        const self = this;
        this.loadCurrentPropertyDetails();
        if(this.isInternalUser) {
            this.getLastDetailsUpdateInformation();
            this.hasQvProperty(self.getQvProperty);
            self.getQvZoneInfo();
            EventBus.$on('proposedZone-multiselect-update', function (data) {
                if(!data.val){
                    self.zoneInfo[data.attrName] = null;
                }
                else {
                    self.zoneInfo[data.attrName] = self.proposedZones.filter(option => option.code === data.val.value)[0];
                }
                self.saveQvProperty(false);
            });

            EventBus.$on('zoneOverlay-multiselect-update', function (data) {
                if(!data.val){
                    self.zoneInfo[data.attrName] = null;
                }
                else {
                    self.zoneInfo[data.attrName] = self.zoneOverlays.filter(option => option.code === data.val.value)[0];
                }
                self.saveQvProperty(false);
            });


            EventBus.$on('notify-simple-property-plus', (data) => {
                if (data.parentAttrName) {
                    self.qvPropertyDetails.aggregateUnitDetails[data.parentAttrName][data.attrName] = (data.fieldType === 'date' || data.fieldType === 'price') ? data.raw : data.val;
                } else {
                    self.qvPropertyDetails.aggregateUnitDetails[data.attrName] = (data.fieldType === 'date' || data.fieldType === 'price') ? data.raw : data.val;
                }
                self.saveQvProperty(false);
            });
            EventBus.$on('notify-multi-property-plus', (data) => {
                if ($.type(data.val) === 'object') {
                    const classificationList = self.$store.getters.getCategoryClassifications(data.val.category);
                    const exists = classificationList.filter(e => e.code === data.val.code);
                    if (exists.length > 0) {
                        self.qvPropertyDetails.aggregateUnitDetails[data.attrName] = exists[0];
                        self.saveQvProperty(false);
                    }
                } else if ($.type(data.val) === 'array') {
                    if (data.val.length === 0) {
                        self.qvPropertyDetails.aggregateUnitDetails[data.attrName] = data.val;
                    } else {
                        const classificationList = self.$store.getters.getCategoryClassifications(data.val[0].category);
                        const classifications = [];
                        $.each(data.val, (i, obj) => {
                            const exists = classificationList.filter(e => e.code === obj.code);
                            if (exists.length > 0) {
                                classifications.push(exists[0]);
                            }
                        });
                        self.qvPropertyDetails.aggregateUnitDetails[data.attrName] = classifications;
                    }
                } else if (data.val === undefined) {
                    self.qvPropertyDetails.aggregateUnitDetails[data.attrName] = null;
                    self.saveQvProperty(false);
                }
            });
            EventBus.$on('notify-simple-qv-property-details', (data) => {
                self.qvPropertyDetails[data.attrName] = data.val;
                self.saveQvProperty(false);
            });
            EventBus.$on('notify-simple-qv-zone-info', (data) => {
                self.zoneInfo[data.attrName] = data.val;
                self.saveQvProperty(false);
            });
            EventBus.$on('notify-simple-nested-unit-details', (data) => {
                if (data.parentAttrName) {
                    self.qvPropertyDetails.unitDetails[data.key - 1][data.parentAttrName][data.attrName] = (data.fieldType === 'date' || data.fieldType === 'price') ? data.raw : data.val;
                } else {
                    self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = (data.fieldType === 'date' || data.fieldType === 'price') ? data.raw : data.val;
                }
                self.saveQvProperty(false);
            });
            EventBus.$on('notify-multi-nested-unit-details', (data) => {
                if ($.type(data.val) === 'object') {
                    const classificationList = self.$store.getters.getCategoryClassifications(data.val.category);
                    const exists = classificationList.filter(e => e.code === data.val.code);
                    if (exists.length > 0) {
                        self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = exists[0];
                        self.saveQvProperty(false);
                    }
                } else if ($.type(data.val) === 'array') {
                    if (data.val.length === 0) {
                        self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = data.val;
                    } else {
                        const classificationList = self.$store.getters.getCategoryClassifications(data.val[0].category);
                        const classifications = [];
                        $.each(data.val, (i, obj) => {
                            const exists = classificationList.filter(e => e.code === obj.code);
                            if (exists.length > 0) {
                                classifications.push(exists[0]);
                            }
                        });
                        self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = classifications;
                    }
                } else if (data.val === undefined) {
                    self.qvPropertyDetails.unitDetails[data.key - 1][data.attrName] = null;
                    self.saveQvProperty(false);
                }
            });
            EventBus.$on('notify-multi-property-plus-hazards', (data) => {
                if ($.type(data.val) === 'object') {
                    const classificationList = self.$store.getters.getCategoryClassifications(data.val.category);
                    const exists = classificationList.filter(e => e.code === data.val.code);
                    if (exists.length > 0) {
                        const hazard = self.findHazards(data.attrName);
                        if (self.qvPropertyDetails.hazards) {
                            self.qvPropertyDetails.hazards = $.grep(self.qvPropertyDetails.hazards, e => e.classification.category !== data.attrName);
                        } else {
                            self.qvPropertyDetails.hazards = [];
                        }
                        if (data.attrName === 'EarthquakeRating' && hazard && hazard.length > 0) {
                            self.qvPropertyDetails.hazards.push({ classification: exists[0], source: hazard[0].source, rating: hazard[0].rating });
                        } else {
                            self.qvPropertyDetails.hazards.push({ classification: exists[0] });
                        }
                        self.saveQvProperty(false);
                    }
                } else if ($.type(data.val) === 'array') {
                    if (data.val.length === 0) {
                        if (self.qvPropertyDetails.hazards) {
                            self.qvPropertyDetails.hazards = $.grep(self.qvPropertyDetails.hazards, e => e.classification.category !== data.attrName);
                        }
                    } else {
                        const classificationList = self.$store.getters.getCategoryClassifications(data.val[0].category);
                        const classifications = $.grep(self.qvPropertyDetails.hazards, e => e.classification.category !== data.attrName);
                        $.each(data.val, (i, obj) => {
                            const exists = classificationList.filter(e => e.code === obj.code);
                            if (exists.length > 0) {
                                classifications.push({ classification: exists[0] });
                            }
                        });
                        self.qvPropertyDetails.hazards = classifications;
                    }
                } else if (data.val === undefined) {
                    const hazard = self.findHazards(data.attrName);
                    if (self.qvPropertyDetails.hazards) {
                        self.qvPropertyDetails.hazards = $.grep(self.qvPropertyDetails.hazards, e => e.classification.category !== data.attrName);
                    } else {
                        self.qvPropertyDetails.hazards = [];
                    }
                    if (data.attrName === 'EarthquakeRating' && hazard && hazard.length > 0) {
                        if (hazard[0].rating) {
                            const ratingCodeClass = self.getEarthquakeRating(hazard[0].rating);
                            self.qvPropertyDetails.hazards.push({ classification: ratingCodeClass, source: hazard[0].source, rating: hazard[0].rating });
                        }
                        self.refreshPropertyPlusDisplay();
                    }
                    self.saveQvProperty(false);
                }
            });
            EventBus.$on('notify-multi-property-plus-heritage-features', (data) => {
                if (data.val.length === 0) {
                    self.qvPropertyDetails.heritageFeatures = [];
                } else {
                    const classificationList = self.$store.getters.getCategoryClassifications(data.val[0].category);
                    const classifications = [];
                    $.each(data.val, (i, obj) => {
                        const exists = classificationList.filter(e => e.code === obj.code);
                        if (exists.length > 0) {
                            classifications.push({ classification: exists[0] });
                        }
                    });
                    self.qvPropertyDetails.heritageFeatures = classifications;
                }
            });
            EventBus.$on('notify-simple-hazards-earthquake', (data) => {
                const rating = data.raw;
                const ratingCodeClass = self.getEarthquakeRating(rating);
                const earthquakeRating = self.findHazards('EarthquakeRating');
                if (ratingCodeClass) {
                    if (self.qvPropertyDetails.hazards) {
                        self.qvPropertyDetails.hazards = $.grep(self.qvPropertyDetails.hazards, e => e.classification.category !== 'EarthquakeRating');
                    } else {
                        self.qvPropertyDetails.hazards = [];
                    }
                    self.qvPropertyDetails.hazards.push({
                        classification: ratingCodeClass,
                        rating,
                        source: (earthquakeRating && earthquakeRating.length > 0)
                            ? earthquakeRating[0].source
                            : null,
                    });
                    self.saveQvProperty(false);
                    self.refreshPropertyPlusDisplay();
                } else if (earthquakeRating && earthquakeRating.length > 0) {
                    if (self.qvPropertyDetails.hazards) {
                        self.qvPropertyDetails.hazards = $.grep(self.qvPropertyDetails.hazards, e => e.classification.category !== 'EarthquakeRating');
                    } else {
                        self.qvPropertyDetails.hazards = [];
                    }
                    self.qvPropertyDetails.hazards.push({
                        classification: earthquakeRating[0].classification,
                        rating,
                        source: earthquakeRating[0].source,
                    });
                    self.saveQvProperty(false);
                    self.refreshPropertyPlusDisplay();
                }
            });
            EventBus.$on('notify-multi-hazards-earthquake', (data) => {
                const earthquakeRatingList = self.findHazards('EarthquakeRating');
                if (earthquakeRatingList && earthquakeRatingList.length > 0) {
                    const earthquakeRating = earthquakeRatingList[0];
                    if (self.qvPropertyDetails.hazards) {
                        self.qvPropertyDetails.hazards = $.grep(self.qvPropertyDetails.hazards, e => e.classification.category !== 'EarthquakeRating');
                    } else {
                        self.qvPropertyDetails.hazards = [];
                    }
                    if (data.val === undefined) {
                        earthquakeRating[data.attrName] = null;
                    } else {
                        const classificationList = self.$store.getters.getCategoryClassifications(data.val.category);
                        const exists = classificationList.filter(e => e.code === data.val.code);
                        if (exists.length > 0 && earthquakeRating) {
                            earthquakeRating[data.attrName] = exists[0];
                        }
                    }
                    self.qvPropertyDetails.hazards.push(earthquakeRating);
                    self.saveQvProperty(false);
                }
            });
        }
    },
    updated() {
        if (this.refreshPropertyPlusTemplate) {
            this.showPropertyPlusTemplate = true;
            this.refreshPropertyPlusTemplate = false;
        }
    },
    destroyed() {
        EventBus.$off('proposedZone-multiselect-update', this.listener);
        EventBus.$off('zoneOverlay-multiselect-update', this.listener);
        EventBus.$off('notify-simple-property-plus', this.listener);
        EventBus.$off('notify-multi-property-plus', this.listener);
        EventBus.$off('notify-simple-qv-property-details', this.listener);
        EventBus.$off('notify-simple-qv-zone-info', this.listener);
        EventBus.$off('notify-simple-nested-unit-details', this.listener);
        EventBus.$off('notify-multi-nested-unit-details', this.listener);
        EventBus.$off('notify-multi-property-plus-hazards', this.listener);
        EventBus.$off('notify-multi-property-plus-heritage-features', this.listener);
        EventBus.$off('notify-simple-hazards-earthquake', this.listener);
        EventBus.$off('notify-multi-hazards-earthquake', this.listener);
    },
    beforeDestroy() {
        EventBus.$off('proposedZone-multiselect-update', this.listener);
        EventBus.$off('zoneOverlay-multiselect-update', this.listener);
        EventBus.$off('notify-simple-property-plus', this.listener);
        EventBus.$off('notify-multi-property-plus', this.listener);
        EventBus.$off('notify-simple-qv-property-details', this.listener);
        EventBus.$off('notify-simple-qv-zone-info', this.listener);
        EventBus.$off('notify-simple-nested-unit-details', this.listener);
        EventBus.$off('notify-multi-nested-unit-details', this.listener);
        EventBus.$off('notify-multi-property-plus-hazards', this.listener);
        EventBus.$off('notify-multi-property-plus-heritage-features', this.listener);
        EventBus.$off('notify-simple-hazards-earthquake', this.listener);
        EventBus.$off('notify-multi-hazards-earthquake', this.listener);
    },
    methods: {
        setupZoningOptions: function () {
            const self = this;
            self.getZoningOptions('TAPZ', 'proposedZoning', 'TAPZoning');
            self.getZoningOptions('ZO', 'zoneOverlay', 'ZoneOverlay');
        },
        getZoningOptions: function (groupCode) {
            const self = this;
            var criteria = {
                "parentCode": self.property.territorialAuthorityId,
                "groupCode": groupCode,
                "sort": "SHORT_DESCRIPTION",
                "order": "ASC"
            };
            this.$nextTick(function () {
                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.ReferenceData.searchClassifications().url,
                    cache: false,
                    contentType: "application/json",
                    data: JSON.stringify(criteria),
                    success: function (response) {
                        if (groupCode === 'TAPZ') {
                            self.proposedZones = response;
                            self.proposedZoneOptions = response.map(option => ({label: option.shortDescription, value: option.code.trim(), title: option.category}));
                            self.proposedZoneOptionsLoaded = true;
                        }
                        if(groupCode === 'ZO'){
                            self.zoneOverlays = response;
                            self.zoneOverlayOptions = response.map(option => ({label: option.shortDescription, value: option.code.trim(), title: option.category}));
                            self.zoneOverlayOptionsLoaded = true;
                        }
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    }
                });
            });
        },
        async loadCurrentPropertyDetails() {
            try {
                await this.$store.dispatch('currentPropertyDetails/getPropertyDetailByPropertyId', this.property.id);
            } catch (err) {
                this.handleException(err);
            }
        },
        hasQvProperty(callback) {
            const self = this;
            const get = jsRoutes.controllers.PropertyMasterData.hasQvProperty(self.property.id);
            $.ajax({
                type: 'GET',
                dataType: 'json',
                url: get.url,
                cache: false,
                success(response) {
                    if (response) {
                        self.hasQvDetails = true;
                        if (callback) callback();
                    } else {
                        self.addUnits();
                    }
                },
                error(response) {
                    console.log(`error checking property has qv property details: ${response}`);
                    self.errorHandler(response);
                },
            });
        },
        getQvProperty() {
            const self = this;
            const getPropertyDetails = jsRoutes.controllers.PropertyMasterData.getQvProperty(self.property.id);
            $.ajax({
                type: 'GET',
                dataType: 'json',
                url: getPropertyDetails.url,
                cache: false,
                success(response) {
                    self.qvPropertyDetails = response;
                    if (!self.qvPropertyDetails.aggregateUnitDetails.rentalAmount) {
                        self.qvPropertyDetails.aggregateUnitDetails.rentalAmount = {
                            source: null,
                            knownDate: null,
                            weeklyRentalAmount: null,
                        };
                    }
                    self.addUnits();
                    self.refreshPropertyPlusDisplay();
                },
                error(response) {
                    console.log(`error getting qv property details: ${response}`);
                    self.errorHandler(response);
                },
            });
        },

        getQvZoneInfo() {
            const self = this;
            const getZoneInfo = jsRoutes.controllers.PropertyMasterData.getQvPropertyZoneInfo(self.property.qupid);
            $.ajax({
                type: 'GET',
                dataType: 'json',
                url: getZoneInfo.url,
                cache: false,
                success(response) {
                    self.zoneInfo = response;
                    self.zoneInfoLoaded = true;
                    self.refreshPropertyPlusDisplay();
                },
                error(response) {
                    console.log(`ERR-ED-1 Error getting qv property Zone Information: ${response}`);
                    self.errorHandler(response);
                },
            });
        },
        async saveQvProperty(buttonEffects) {
            const self = this;
            if (buttonEffects) self.disableButtons();

            $.ajax({
                type: 'POST',
                url: jsRoutes.controllers.PropertyMasterData.saveQvProperty().url,
                cache: false,
                processData: false,
                contentType: 'application/json',
                data: JSON.stringify(self.qvPropertyDetails),
                success: async function(response) {
                    if (response.success) {
                        self.qvPropertyDetails = response.value;
                        try {
                            await refreshPropertyInfo(self.property.qupid);
                            console.log('Property info updated successfully', self.property.qupid);
                        } catch (error) {
                            console.log('Error updating property info', error);
                        }
                    } else {
                        console.log(`Error in saving qvPropertyDetails${response.errors}`);
                    }
                    if (buttonEffects) self.enableButtons();
                },
                error(response) {
                    console.log('Error in saving qvPropertyDetails');
                    self.errorHandler(response);
                    if (buttonEffects) self.enableButtons();
                },
            });

            $.ajax({
                type: 'POST',
                url: jsRoutes.controllers.PropertyMasterData.saveQvPropertyZoneInfo().url,
                cache: false,
                processData: false,
                contentType: 'application/json',
                data: JSON.stringify(self.zoneInfo),
                success(response) {
                    self.zoneInfo = response;
                },
                error(response) {
                    console.error('ERR-ED-3 could not save qvPropertyDetailsZoneInformation ');
                    self.errorHandler(response);
                },
            });
        },
        refreshPropertyPlusDisplay() {
            const self = this;
            self.showPropertyPlusTemplate = !self.showPropertyPlusTemplate;
            if (!self.showPropertyPlusTemplate) {
                self.refreshPropertyPlusTemplate = true;
            }
        },
        enableButtons() {
            setTimeout(() => {
                $('.property-plus-save').removeClass('disabled');
            }, 2000);
        },
        disableButtons() {
            $('.property-plus-save').addClass('disabled');
        },
        saveOnDropDownHide() {
            this.saveQvProperty(false);
        },
        addUnits() {
            const self = this;
            const units = self.property.units <= 4 ? self.property.units : 1;
            const existingUnits = self.qvPropertyDetails.unitDetails.length;
            if (units > existingUnits) {
                const start = (existingUnits > 0 && existingUnits < units) ? +existingUnits + 1 : 1;
                for (let i = start; i <= units; i += 1) {
                    self.qvPropertyDetails.unitDetails.push({
                        unitNumber: i,
                        unitType: null,
                        numberOfSingleBedrooms: null,
                        numberOfDoubleBedrooms: null,
                        numberOfBathrooms: null,
                        kitchenAge: null,
                        heatingType: [],
                        insulation: [],
                        doubleGlazing: null,
                        effectiveFloorArea: null,
                        rentalAmount: {
                            source: null,
                            knownDate: null,
                            weeklyRentalAmount: null,
                        },
                    });
                }
            } else if (units < existingUnits) {
                self.qvPropertyDetails.unitDetails = self.qvPropertyDetails.unitDetails.slice(0, units);
                self.saveQvProperty();
            }
        },
        showUnitExtraDetails(key) {
            $(`#extraDetailsUnit${key}`).toggle();
        },
        findHazards(category) {
            const self = this;
            let hazards = null;
            if (self.qvPropertyDetails.hazards) {
                hazards = $.grep(self.qvPropertyDetails.hazards, e => e.classification.category === category);
            }
            return hazards;
        },
        getHazardsClassifications(category, multiple) {
            const self = this;
            let classifications = null;
            const hazards = self.findHazards(category);
            if (hazards) {
                classifications = hazards.map(a => a.classification);
                if (!multiple) classifications = classifications[0];
            }
            return classifications;
        },
        getHeritageFeaturesClassifications() {
            const self = this;
            let classifications = null;
            const { heritageFeatures } = self.qvPropertyDetails;
            if (heritageFeatures) {
                classifications = heritageFeatures.map(a => a.classification);
            }
            return classifications;
        },
        editPropertyDetail() {
            if (!this.propertyDetail
                || !this.propertyDetail.category
                || !this.propertyDetail.category.code) {
                return;
            }
            const propertyBaseCategory = this.propertyDetail.category.code.charAt(0);
            const valuableRuralCategories = 'ADFHPS';
            const valuableCommercialCategories = 'CIOU'
            if(valuableRuralCategories.includes(propertyBaseCategory)) {
                this.$router.push({ name: 'rural-property-detail-edit', params: { id: this.property.id } });
            }
            else if (propertyBaseCategory === 'R' || valuableCommercialCategories.includes(propertyBaseCategory)) {
                this.$router.push({ name: 'property-detail-edit', params: { id: this.property.id, qvPropertyDetails: _.cloneDeep(this.qvPropertyDetails) } });
            }
        },
        async getLastDetailsUpdateInformation() {
            const self = this;
            var get = jsRoutes.controllers.PropertyDetailController.getPropertyDetailUpdateInformation(self.property.qupid);
            $.ajax({
                type: 'GET',
                url: get.url,
                cache: false,
                dataType: 'json',
                success: function (response) {
                    self.setUpLastUpdatedFields(response);
                },
                error: function (response) {
                    console.log('error checking property has qv property details: ' + response);
                    self.errorHandler(response);
                }
            });
        },
        setUpLastUpdatedFields(lastUpdate) {
            let users = this.$store.state.users;
            const match = users.filter(user => user.ntUsername === lastUpdate.user);
            if (match) {
                this.lastUpdatedBy = match[0]?.name || this.$store.state.userData.userName || lastUpdate.user;
                this.lastUpdatedDate = this.formatUpdatedDate(lastUpdate.lastUpdatedDateTime);
                this.hasUpdateInformation = true;
            }
            else {
                console.log("No match for user in QIVS database and user in MONARCH picklist");
            }
        },
        formatUpdatedDate(date){
            const splitDate = date.split("-");
            return splitDate[2] + '/' + splitDate[1] + '/' + splitDate[0];
        }
    },
};
</script>
<style lang="scss" src='../rollMaintenance/rollMaintenance.scss' scoped></style>
<style lang="scss" scoped>
label {
    cursor: text;
}

.label {
    display: block;
}

.edit-property-detail {
    margin-right: 2rem;
}

</style>

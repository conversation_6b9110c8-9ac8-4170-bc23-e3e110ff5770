<template xmlns:v-bind="http://www.w3.org/1999/xhtml" xmlns:v-on="http://www.w3.org/1999/xhtml">
    <div class="masterDetails" v-if="showTemplate">
        <div class="listingControls">
            <span v-if="canNavigate">
                <i title="Previous Assessment" class="listingButton material-icons assessmentNav"
                @click="loadMasterDetails(property.previousPropertyId, 'back')" data-direction="back">arrow_back</i>
                <i title="Next Assessment" class="listingButton material-icons assessmentNav"
                @click="loadMasterDetails(property.nextPropertyId, 'forward')" data-direction="forward">arrow_forward</i>
            </span>
            <ul>
                <li v-if="property.status && property.status.code==='I'" id="inactive-assessment-text">THIS IS AN INACTIVE ASSESSMENT</li>
                <li class="md-qpid">QPID: <strong v-html="property.qupid"></strong></li>
                <li v-if="property.tapid" class="md-qpid">TAPID: <strong v-html="property.tapid"></strong></li>
                <li class="md-valRef">
                    <div v-if="property.valRefList && property.valRefList.length > 1">
                        <label>VAL REF:</label>
                        <select @change="loadPlates()" v-model="selectedValRef">
                            <option v-for="valRef in property.valRefList"
                                :selected="selectedValRef == valRef.propertyId ? 'true' : 'false'"
                                :value="valRef.propertyId"
                                :key="valRef.propertyId"
                            >
                                {{valRef.valuationReference}}
                            </option>
                        </select>
                    </div>
                    <div v-else>
                        VAL REF:<strong>{{property.valuationReference}}</strong>
                    </div>
                </li>
                <li v-if="property.valRefList && property.valRefList.length > 1" class= "md-extensions">
                    <span v-html="property.valRefList.length -1" ></span>
                </li>
                <li class="md-copyValref" title="Copy Val Ref">
                    <i id="ValRef-to-Clipboard" class="material-icons md-dark"
                    v-clipboard:copy="property.valuationReference"
                    v-clipboard:success="succesfullyCopiedValRef">content_copy</i>
                </li>
            </ul>
        </div>

        <div class="masterDetails-Wrapper mdl-shadow--3dp clearfix" v-bind:class="{maoriLand: property.maoriLand == 'Yes'}">
            <div class="md-full md-summaryHeader">
                <div class="md-propertyOverview">
                    <ul class="md-categoryDescription">
                        <li>{{ getRenderableValue(property.category ? property.category.code : '') }}: <em>{{ getRenderableValue(property.category ? property.category.description : '') }}</em></li>
                        <li><em>Effective: <strong>{{property.currentRevisionDate}}</strong></em></li>
                    </ul>
                    <div class="md-summaryAddress">
                        <h1>{{property.address1}}<span v-html="property.address2"></span></h1>
                    </div>
                    <ul class="md-summaryOwners">
                        <li><label>Nature of Imps:</label>{{ property.natureOfImprovements }}</li>
                        <li v-if="property.occupiers && property.occupiers.length > 0"><label>Occupier:</label>{{ property.occupiers[0].isNameSecret ? 'Not Available' : property.occupiers[0].fullName }}</li>
                        <li v-if="property.occupiers && property.occupiers.length > 1"><label>Occupier:</label>{{ property.occupiers[1].isNameSecret ? 'Not Available' : property.occupiers[1].fullName }}</li>
                        <li v-if="property.owners && property.owners.length > 0"><label>Owner:</label>{{ property.owners[0].isNameSecret ? 'Not Available' : property.owners[0].fullName }}</li>
                    </ul>
                    <ul class="md-summaryTotals">
                        <li><label>Units: </label>{{property.units}}</li>
                        <li><label>Total Floor:</label>{{property.TFA}}<span>m<sup>2</sup></span></li>
                        <li><label>Total Living:</label>{{property.TLA}}<span>m<sup>2</sup></span></li>
                        <li><label>Land Area:</label>{{property.landArea}}<span>ha</span></li>
                    </ul>
                    <ul class="md-summaryValues">
                        <li><label>Capital Value</label>{{property.capitalValue}}<span><strong>{{property.cvNetRate}}</strong>m<sup>2</sup></span>
                        </li>
                        <li><label>Land Value</label>{{property.landValue}}<span v-if="!property.showHectares"><strong>{{property.lvNetRate}}</strong>m<sup>2</sup></span><span v-else><strong>{{property.lvNetRate}}</strong>ha</span>
                        </li>
                        <li><label>Improvements</label>{{property.valueOfImprovements}}<span><strong>{{property.viNetRate}}</strong>m<sup>2</sup></span>
                        </li>
                        <li><label v-if="isInternalUser">Building Net Rate</label>{{ isInternalUser ? property.buildingNetRate : ''}}<span v-if="isInternalUser">m<sup>2</sup></span></li>
                    </ul>
                </div>

                <div class="md-photoGallery master-details-carousel"></div>

                <div class="md-rtvSales-overview">

                    <div class="md-estimates-wrapper" v-if="isInternalUser">
                        <ul class="md-marketEstimate mev">
                            <li><em>RealTime Value: <strong>{{property.marketEstimateDate}}</strong></em></li>
                            <li><h3>{{property.marketEstimateValue}}</h3></li>
                            <li class="me-comparison me-compare-CV">
                                <label>Compared to Current CV</label>
                                <span v-bind:class="[property.marketEstimateToCapitalValueUpDown != null ? (property.marketEstimateToCapitalValueUpDown < 0 ? 'valueDown' : 'valueUp') : '']">{{property.marketEstimateToCapitalValue}}</span>
                            </li>
                        </ul>
                        <ul class="md-marketEstimate mev">
                            <li><em>RealTime Land Value: </em></li>
                            <li><h3>{{property.realTimeLandValue}}</h3></li>
                            <li class="me-comparison me-compare-LV">
                                <label>Compared to Current LV</label>
                                <span v-bind:class="[property.realTimeLandValueToLandValueUpDown != null ? (property.realTimeLandValueToLandValueUpDown < 0 ? 'valueDown' : 'valueUp') : '']">{{property.realTimeLandValueToLandValue}}</span>
                            </li>
                        </ul>
                    </div>

                    <!-- LATEST SALE information to be displayed in This Area of the New Header Starts Here -->
                    <ul class="md-summaryValues md-summaryLastSale" v-if="property.maoriLand != 'Yes'">
                        <li>
                            Last Sale:
                            <strong>{{ lsDate }}</strong>
                            <strong class="saleClass" v-if="sales && sales.length > 0">{{ lsSaleType ? lsSaleType.code : '' }}{{ lsSaleTenure ? lsSaleTenure.code : '' }}{{ lsPriceValueRelationship ? lsPriceValueRelationship.code : '' }}</strong>
                            <ul class="saleClass-description mdl-shadow--2dp"  v-if="sales && sales.length > 0">
                                <li v-if="lsSaleType"><em>Sale Type</em><span>{{ lsSaleType.code }}</span><span>{{ lsSaleType.description }}</span></li>
                                <li v-if="lsSaleTenure"><em>Sale Tenure</em><span>{{ lsSaleTenure.code }}</span><span>{{ lsSaleTenure.description }}</span></li>
                                <li v-if="lsPriceValueRelationship"><em>Price Value Relationship</em><span>{{ lsPriceValueRelationship.code }}</span><span>{{ lsPriceValueRelationship.description }}</span></li>
                            </ul>
                            <span class="saleStatus" v-if="lsStatus">{{ lsStatus }}</span>
                        </li>
                        <li><label>Net Sale Price</label>{{ lsNetPrice ? lsNetPrice : '--' }}&nbsp;</li>
                        <li><label>Chattels</label>{{ lsChattels ? lsChattels : '--' }}&nbsp;</li>
                        <li v-if="!isRuralProperty"><label>Analysed Land</label>{{ isInternalUser && lsAnalysedLV ? lsAnalysedLV : '--'}}&nbsp;</li>
                        <li v-else><label>Analysed Land</label>{{ isInternalUser && ruralAnalysedLV ? ruralAnalysedLV : '--'}}&nbsp;</li>
                        <li><label>NSP/CV</label>{{ lsNSPCV ? lsNSPCV : '--' }}&nbsp;</li>
                        <li v-if="!isRuralProperty"><label>Gross Rate</label>{{ lsBuildingGrossRate ? lsBuildingGrossRate : '--' }}<span v-if="lsBuildingGrossRate">m<sup>2</sup></span>&nbsp;</li>
                        <li v-else><label>Analysed Land/Ha</label>{{ isInternalUser && ruralAnalysedLandPerHa ? ruralAnalysedLandPerHa : '--' }}&nbsp;</li>
                        <li v-if="!isRuralProperty"><label>Sale BNR</label>{{ isInternalUser && lsBuildingNetRate ? lsBuildingNetRate : '--' }}<span v-if="lsBuildingNetRate && isInternalUser">m<sup>2</sup></span>&nbsp;</li>
                        <li v-else><label>Analysed Land/Prodn</label>{{ isInternalUser && ruralAnalysedLandPerProdn ? ruralAnalysedLandPerProdn : '--' }}&nbsp;</li>
                    </ul>
                    <!-- For Maori Land Properties -->
                    <ul class="md-summaryValues" v-if="property.maoriLand == 'Yes'">
                        <li>
                            <label>Unadjusted CV</label>{{ property.cml.capitalValue }}
                            <span><strong>{{ property.cml.cvNetRate }}</strong>m<sup>2</sup></span>
                        </li>
                        <li>
                            <label>Unadjusted LV</label>{{ property.cml.landValue }}
                            <span><strong>{{ property.cml.lvNetRate }}</strong>m<sup>2</sup></span>
                        </li>
                        <li>
                            <label>Unadjusted VI</label>{{ property.cml.vi }}
                            <span><strong>{{ property.cml.viNetRate }}</strong>m<sup>2</sup></span>
                        </li>
                        <li class="maoriLand-adjustment significancePct"><span>{{ property.cml.significance }}%</span><label>Significance</label></li>
                        <li class="maoriLand-adjustment ownersPct"><span>{{ property.cml.owners }}%</span><label>Owners</label></li>
                        <li class="maoriLand-adjustment adjustmentPct"><span>{{ property.cml.adjustment }}%</span><label>Adjustment</label></li>
                        <li class="maoriLand-adjustment currentLumpSum" v-if="property.maoriLandCurrentLumpSum != null && property.lumpSumApportionmentYN == 'Yes'"><span>${{ property.maoriLandCurrentLumpSum }}</span><label>Lump Sum</label></li>
                        <li class="maoriLand-adjustment totalOwners"><span>{{ property.mlnoOfOwners }}</span><label>Number of Owners</label></li>
                    </ul>
                    <div class="md-legacyMonarchDataCheckFlags" v-show="false">
                         <span>Data Issues: <button id="dataIssuesButton" title="Some property data on this assessment fails validation checks." disabled>{{dataIssuesYN}}</button></span>
                         <span>Issues Checked: <button id="issuesCheckedButton" title="If the failed validation is acceptable then change this to “Yes”, if the failed validation is not acceptable then fix the data.">{{issuesCheckedYN}}</button></span>
                    </div>
                </div>

                <div class="morePhotos" v-show="moreThanTwentyPhotos" :data-id="twentyFirstPhotoId" :data-property="property.id"><i class="material-icons">add_to_photos</i><span>View More Photos</span></div>

                <div class="md-full qvToolbar">
                    <ul class="qvToolbar-links lefty">
                        <li id="propertySummary" data-cy="propertySummaryTab" class="active summary property-details-tabs">
                            <a v-on:click="tabActionHandler(0, property.qupid)">Summary</a>
                        </li>
                        <li class="extra-details property-details-tabs">
                            <a v-on:click="tabActionHandler(1, property.id)">Extra Details</a>
                        </li>
                        <li id="valuationJob" class="valuationjobs-class property-details-tabs" v-if="isInternalUser">
                            <label v-on:click="valuationJobsMenu = !valuationJobsMenu;valuationJobIdToDisplay = null;" class="menu valuation">Valuation Jobs<i class="material-icons valuation">arrow_drop_down</i></label>
                            <ul class="valJobs-menu mdl-shadow--2dp" v-if="valuationJobsMenu">
                                <li class="divider" v-on:click="tabActionHandler(2)">New Valuation Job</li>
                                <li v-for="job in valuationJobs" v-bind:ref="job.id+'-valJob'"
                                    @click="tabActionHandler(2, job.id)"
                                    :key="job.id"
                                >
                                    {{ formatDate(job.valuationCreatedDate, 'DD MMMM YYYY') }}
                                </li>
                            </ul>
                        </li>
                        <li id="rollMaintenance" class="property-details-tabs" v-if="isInternalUser">
                            <a v-on:click="tabActionHandler(3, property.qupid)" data-cy="master-details-property-toolbar-roll-maintenance-tab-link">Consents</a>
                        </li>
                        <li class="property-details-tabs" v-if="isInternalUser || isTAUser || externalObjectionAccess">
                            <a @click="$router.push({ name: 'objections-search', query: { qpid: property.qupid } })">Objections</a>
                        </li>
                        <li class="qv-map property-details-tabs" v-if="isQVMapUser">
                            <a v-on:click="tabActionHandler(6, property.qupid)">Map</a>
                        </li>
                    </ul>
                    <ul class="qvToolbar-icons righty">
                        <li class="photo-uploader"
                            :class="{disabled: isReadOnly == true}"
                            title="Upload Photos"
                            @click="openPhotoUploader()"
                            data-upgraded=",MaterialButton"
                        >
                            <i class="material-icons md-light">&#xE251;</i>
                        </li>
                    </ul>
                    <ul class="qvToolbar-qivs righty">
                        <li class="md-qivs" @click="initQivsLink(property.qivsURL, 'QIVS', property.qupid)">
                            <label>QIVS</label> <i class="material-icons">call_made</i>
                        </li>
                        <li class="md-qivs" @click="openMonarchFloorPlanTab()"><label>FLOOR PLANS</label> <i class="material-icons">call_made</i></li>
                        <li class="md-qvms" v-if="isQVMapUser"
                            @click="openQVMap()">
                            <label>MAP</label> <i class="material-icons">call_made</i>
                        </li>
                        <li class="md-qvms"
                            @click="openGoogleSearchTab()">
                            <label>WEB</label> <i class="material-icons icon--flipped">search</i>
                        </li>
                    </ul>
                </div>
            </div>
            <div id="md-propertyInfoSection" class="md-property-info-table mdl-shadow--2dp" v-if="propertyInfoLoaded && showPropertyInfoSection" style="padding-left:1rem ">
                <property-info :qpid="property.qupid" />
            </div>
            <div id="dragSection" v-if="showDragSection" class="md-right propertySummary property-details-tab-contents">
                <div id="dragBlock1" class="dragBlock dragBlockPropertySummary" draggable="true">
                    <div id="dragBlockPropertySummary" class="md-summary"
                         v-show="property.propertyDesc && property.propertyDesc != ''">
                        <h3 data-cy="property-summary-header">Property Summary</h3>
                        <p v-html="property.propertyDesc"></p>
                    </div>
                </div>
                <div id="dragBlock2" class="dragBlock dragBlockRevisionBox" draggable="true">
                    <div id="dragBlockRevisionBox" class="md-revisionBox" v-bind:class="{hide : !(((isInternalUser == false && isReadOnly == false) || isInternalUser) && property.hasRevision)}">
                        <h3 v-if="propertyInfo.hasRuralRevisionWorksheet && !propertyInfo.hasCommercialRevisionWorksheet" @click="goToRuralRevisionWorksheet()">
                            <a>Revision Values Worksheet</a><em>{{property.nextRatingValuationDate}}</em>
                        </h3>
                        <h3 v-else-if="propertyInfo.hasCommercialRevisionWorksheet && !propertyInfo.hasRuralRevisionWorksheet">
                            <router-link data-cy="commercialRevisionWorksheetLink" :to="{ name: 'commercial-revision-worksheet', params: { qpid: property.qupid }}">
                                Revision Values Worksheet<em>{{property.nextRatingValuationDate}}</em>
                            </router-link>
                        </h3>
                        <h3 v-else-if="(propertyInfo.hasRuralWorksheet || propertyInfo.hasCommercialWorksheet) && (propertyInfo.hasNoRevisionWorksheet)">Revision Values<em>{{property.nextRatingValuationDate}}</em></h3>
                        <h3 v-else-if="propertyInfo.displayUpdateRevisionValuesNoWorksheet && propertyInfo.hasNoCurrentWorksheet"
                            @click="initQivsLink(property.qivsURL, 'updateRevisionValues', property.qupid, propertySummary.rollNumber, propertySummary.assessmentNumber, property.tora, property.natureOfImprovements, property.revisionCapVal, property.revisionLandVal)">
                                <a>Revision Values</a><em>{{property.nextRatingValuationDate}}</em>
                        </h3>
                        <h3 v-else>Revision Values</h3>
                        <div class="revisionLumpSum" v-if="property.maoriLandRevisionLumpSum != null && property.lumpSumApportionmentYN == 'Yes'">
                            <label>Lump Sum Deduction</label>
                            <span>${{property.maoriLandRevisionLumpSum}}</span>
                        </div>
                        <ul class="revisionValues maoriLand" v-if="property.maoriLand == 'Yes'">
                            <li><label>Unadjusted Capital Value</label>{{property.rml.capitalValue}}<span
                                    v-bind:class="[property.rml.capitalValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.rml.capitalValueDiff}}%</span>
                                <div><strong>{{ property.rml.cvNetRate }}</strong>m<sup>2</sup></div>
                            </li>
                            <li><label>Unadjusted Land Value</label>{{property.rml.landValue}}<span
                                    v-bind:class="[property.rml.landValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.rml.landValueDiff}}%</span>
                                <div><strong>{{ property.rml.lvNetRate }}</strong>m<sup>2</sup></div>
                            </li>
                            <li><label>Unadjusted Value of Improvements</label>{{property.rml.vi}}<span
                                    v-bind:class="[property.rml.valueOfImprovementsDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.rml.valueOfImprovementsDiff}}%</span>
                                <div><strong>{{ property.rml.viNetRate }}</strong>m<sup>2</sup></div>
                            </li>
                            <li class="maoriLand-adjustment significancePct"><span>{{ property.rml.significance }}%</span><label>Significance</label></li>
                            <li class="maoriLand-adjustment ownersPct"><span>{{ property.rml.owners }}%</span><label>Owners</label></li>
                            <li class="maoriLand-adjustment adjustmentPct"><span>{{ property.rml.adjustment }}%</span><label>Adjustment</label></li>
                        </ul>
                        <ul class="revisionValues">
                            <li><label>Capital Value</label>{{property.revisionCapVal}}<span
                                    v-bind:class="[property.capitalValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.capitalValueDiff}}%</span>
                                <div><strong>{{property.revalCapValNetRate}}</strong>m<sup>2</sup></div>
                            </li>
                            <li><label>Land Value</label>{{property.revisionLandVal}}<span
                                    v-bind:class="[property.landValueDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.landValueDiff}}%</span>
                                <div><span v-if="!property.showHectares"><strong>{{property.revalLandValNetRate}}</strong>m<sup>2</sup></span> <span v-else><strong>{{property.revalLandValNetRate}}</strong>ha</span></div>
                            </li>
                            <li><label>Value of Improvements</label>{{property.revalValueOfImprovements}}<span
                                    v-bind:class="[property.valueOfImprovementsDiff >= 0 ? 'valueUp' : 'valueDown']">{{property.valueOfImprovementsDiff}}%</span>
                                <div><strong>{{property.revalValueOfImprovementsNetRate}}</strong>m<sup>2</sup></div>
                            </li>
                            <li v-if="isInternalUser"><label>Building Net Rate</label>{{property.revisedBuildingNetRate}}<span>m<sup>2</sup></span></li>
                        </ul>
                    </div>
                </div>

                <div id="dragBlock3" class="dragBlock dragBlockOtherInfos propertySummary" draggable="true">
                    <div id="dragBlockOtherInfos">
                        <ul class="md-landMas-tabs">
                            <li class="MasTab-1" data-tab="MasTab-1" data-container="valuerData"><span
                                    class="is-active">Valuer View</span></li>
                            <li class="MasTab-2" data-tab="MasTab-2" data-container="landuseData" data-filter="ludItem">
                                <span>Land Use Data</span></li>
                            <li class="MasTab-3" data-tab="MasTab-3" data-container="masData" data-filter="masItem">
                                <span>Mass Appraisal Data</span></li>
                            <li class="MasTab-4" data-tab="MasTab-4" data-container="showallData"><span>Show All</span>
                            </li>
                            <hr class="MasTab-1">
                        </ul>
                        <div class="md-landMas-Container landuseData">
                            <ul class="md-landMas">
                                <li class="ludItem md-masIcon-zone"><strong v-html="property.zone"></strong>Zone</li>
                                <li class="ludItem md-masIcon-landUse"><strong v-html="property.landUse"></strong>Land
                                    Use
                                </li>
                                <li class="ludItem md-masIcon-units"><strong v-html="property.units"></strong>Units</li>
                                <li class="ludItem md-masIcon-walls"><strong
                                        v-html="property.wallConstructionAndCondition"></strong>Wall Construction and
                                    Condition
                                </li>
                                <li class="ludItem md-masIcon-roof"><strong
                                        v-html="property.roofConstructionAndCondition"></strong>Roof Construction and
                                    Condition
                                </li>
                                <li class="ludItem md-masIcon-age"><strong v-html="property.buildingAge"></strong>Age
                                </li>
                                <li class="ludItem md-masIcon-landArea"><strong
                                        v-html="property.landArea"><span>Ha</span></strong>Land Area
                                </li>
                                <li class="ludItem md-masIcon-siteCoverage">
                                    <strong>{{property.siteCoverage}}<span>m<sup>2</sup></span></strong>Site Coverage
                                </li>
                                <li class="ludItem md-masIcon-tfa">
                                    <strong>{{property.TFA}} <span>m<sup>2</sup></span></strong>Total Floor Area
                                </li>
                                <li class="ludItem md-masIcon-carParks"><strong v-html="property.carParks"></strong>Car
                                    Parks
                                </li>
                                <li class="ludItem md-masIcon-maoriLand"><strong v-html="property.maoriLand"></strong>Maori
                                    Land
                                </li>
                                <li class="ludItem md-masIcon-production"><strong v-html="property.production"></strong>Production
                                </li>
                            </ul>
                        </div>

                        <div class="md-landMas-Container masData">
                            <ul class="md-landMas">
                                <li class="masItem md-masIcon-csi"><strong v-html="property.csi"></strong>Class of
                                    Surrounding Improvements
                                </li>
                                <li class="masItem md-masIcon-viewScope"><strong v-html="property.viewScope"></strong>View
                                    and Scope
                                </li>
                                <li class="masItem md-masIcon-contour"><strong v-html="property.contour"></strong>Contour
                                </li>
                                <li class="masItem md-masIcon-lotPosition"><strong
                                        v-html="property.lotPosition"></strong>Lot Position
                                </li>
                                <li class="masItem md-masIcon-houseType"><strong v-html="property.houseType"></strong>House
                                    Type
                                </li>
                                <li class="masItem md-masIcon-eyb"><strong
                                        v-html="property.effectiveYearBuilt"></strong>Effective Year Built
                                </li>
                                <li class="masItem md-masIcon-landscaping"><strong
                                        v-html="property.landscaping"></strong>Landscaping
                                </li>
                                <li class="masItem md-masIcon-modernisation"><strong
                                        v-html="property.modernisation"></strong>Modernisation
                                </li>
                                <li class="masItem md-masIcon-mla">
                                    <strong>{{property.MLA}} <span>m<sup>2</sup></span></strong>Main Living Area
                                </li>
                                <li class="masItem md-masIcon-tla">
                                    <strong>{{property.TLA}} <span>m<sup>2</sup></span></strong>Total Living Area
                                </li>
                                <li class="masItem md-masIcon-bedrooms"><strong v-html="property.bedrooms"></strong>Bedrooms
                                </li>
                                <li class="masItem md-masIcon-toilets"><strong v-html="property.toilets"></strong>Toilets
                                </li>
                                <li class="masItem md-masIcon-oli"><strong
                                        v-html="property.otherLargeImprovements"></strong>Other Large Improvements
                                </li>
                                <li class="masItem md-masIcon-deck"><strong v-html="property.deck"></strong>Deck</li>
                                <li class="masItem md-masIcon-foundation"><strong v-html="property.foundation"></strong>Foundation
                                </li>
                                <li class="masItem md-masIcon-laundryWorkshop"><strong
                                        v-html="property.laundryWorkshop"></strong>Laundry or Workshop
                                </li>
                                <li class="masItem md-masIcon-carAccess"><strong v-html="property.carAccess"></strong>Car
                                    Access
                                </li>
                                <li class="masItem md-masIcon-driveway"><strong v-html="property.driveway"></strong>Driveway
                                </li>
                                <li class="masItem md-masIcon-umrg"><strong
                                        v-html="property.underMainRoofGarages"></strong>Under Main Roof Garaging
                                </li>
                                <li class="masItem md-masIcon-fsg"><strong
                                        v-html="property.freeStandingGarages"></strong>Free Standing Garaging
                                </li>
                                <li class="masItem md-masIcon-outlier"><strong v-html="property.outlier"></strong>Outlier
                                </li>
                            </ul>
                        </div>

                        <div class="md-landMas-Container valuerData active">
                            <ul class="md-landMas">
                                <li class="ludItem md-masIcon-zone"><strong v-html="property.zone"></strong>Zone</li>
                                <li class="ludItem md-masIcon-landUse"><strong v-html="property.landUse"></strong>Land
                                    Use
                                </li>
                                <li class="ludItem md-masIcon-units"><strong v-html="property.units"></strong>Units</li>
                                <li class="ludItem md-masIcon-walls"><strong
                                        v-html="property.wallConstructionAndCondition"></strong>Wall Construction and
                                    Condition
                                </li>
                                <li class="ludItem md-masIcon-roof"><strong
                                        v-html="property.roofConstructionAndCondition"></strong>Roof Construction and
                                    Condition
                                </li>
                                <li class="masItem md-masIcon-modernisation"><strong
                                        v-html="property.modernisation"></strong>Modernisation
                                </li>
                                <li class="masItem md-masIcon-umrg"><strong
                                        v-html="property.underMainRoofGarages"></strong>Under Main Roof Garaging
                                </li>
                                <li class="masItem md-masIcon-fsg"><strong
                                        v-html="property.freeStandingGarages"></strong>Free Standing Garaging
                                </li>
                                <li class="masItem md-masIcon-oli"><strong
                                        v-html="property.otherLargeImprovements"></strong>Other Large Improvements
                                </li>
                                <li class="masItem md-masIcon-bedrooms"><strong v-html="property.bedrooms"></strong>Bedrooms
                                </li>
                                <li class="masItem md-masIcon-bathrooms"><strong v-html="property.bathrooms"></strong>Bathrooms</li>
                                <li class="masItem md-masIcon-toilets"><strong v-html="property.toilets"></strong>Toilets
                                </li>
                                <li class="masItem md-masIcon-lotPosition"><strong
                                        v-html="property.lotPosition"></strong>Lot Position
                                </li>
                                <li class="masItem md-masIcon-contour"><strong v-html="property.contour"></strong>Contour
                                </li>
                                <li class="masItem md-masIcon-viewScope"><strong v-html="property.viewScope"></strong>View
                                    and Scope
                                </li>
                            </ul>
                        </div>
                        <div class="md-landMas-Container showallData">
                            <ul class="md-landMas landuseList">
                                <li class="ludItem md-masIcon-landUse"><strong v-html="property.landUse"></strong>Land
                                    Use
                                </li>
                                <li class="ludItem md-masIcon-units"><strong v-html="property.units"></strong>Units</li>
                                <li class="masItem md-masIcon-houseType"><strong v-html="property.houseType"></strong>House
                                    Type
                                </li>
                                <li class="masItem md-masIcon-modernisation"><strong
                                        v-html="property.modernisation"></strong>Modernisation
                                </li>
                                <li class="ludItem md-masIcon-walls"><strong
                                        v-html="property.wallConstructionAndCondition"></strong>Wall Construction and
                                    Condition
                                </li>
                                <li class="ludItem md-masIcon-roof"><strong
                                        v-html="property.roofConstructionAndCondition"></strong>Roof Construction and
                                    Condition
                                </li>
                                <li class="ludItem md-masIcon-age"><strong v-html="property.buildingAge"></strong>Age
                                </li>
                                <li class="masItem md-masIcon-eyb"><strong
                                        v-html="property.effectiveYearBuilt"></strong>Effective Year Built
                                </li>
                                <li class="ludItem md-masIcon-tfa">
                                    <strong>{{property.TFA}} <span>m<sup>2</sup></span></strong>Total Floor Area
                                </li>
                                <li class="masItem md-masIcon-tla">
                                    <strong>{{property.TLA}} <span>m<sup>2</sup></span></strong>Total Living Area
                                </li>
                                <li class="ludItem md-masIcon-siteCoverage"><strong>{{property.siteCoverage}} <span
                                        v-if="property.siteCoverage != '-'">m<sup>2</sup></span></strong>Site Coverage
                                </li>
                                <li class="masItem md-masIcon-mla">
                                    <strong>{{property.MLA}} <span>m<sup>2</sup></span></strong>Main Living Area
                                </li>
                                <li class="masItem md-masIcon-bedrooms"><strong v-html="property.bedrooms"></strong>Bedrooms
                                </li>
                                <li class="masItem md-masIcon-toilets"><strong v-html="property.toilets"></strong>Toilets
                                </li>
                                <li class="masItem md-masIcon-umrg"><strong
                                        v-html="property.underMainRoofGarages"></strong>Under Main Roof Garaging
                                </li>
                                <li class="masItem md-masIcon-fsg"><strong
                                        v-html="property.freeStandingGarages"></strong>Free Standing Garaging
                                </li>
                                <li class="ludItem md-masIcon-carParks"><strong v-html="property.carParks"></strong>Car
                                    Parks
                                </li>
                                <li class="masItem md-masIcon-carAccess"><strong v-html="property.carAccess"></strong>Car
                                    Access
                                </li>
                                <li class="masItem md-masIcon-driveway"><strong v-html="property.driveway"></strong>Driveway
                                </li>
                                <li class="masItem md-masIcon-oli"><strong
                                        v-html="property.otherLargeImprovements"></strong>Other Large Improvements
                                </li>
                                <li class="masItem md-masIcon-foundation"><strong v-html="property.foundation"></strong>Foundation
                                </li>
                                <li class="masItem md-masIcon-laundryWorkshop"><strong
                                        v-html="property.laundryWorkshop"></strong>Laundry or Workshop
                                </li>
                                <li class="masItem md-masIcon-deck"><strong v-html="property.deck"></strong>Deck</li>
                                <li class="masItem md-masIcon-outlier"><strong v-html="property.outlier"></strong>Outlier
                                </li>
                                <li class="ludItem md-masIcon-zone"><strong v-html="property.zone"></strong>Zone</li>
                                <li class="masItem md-masIcon-csi"><strong v-html="property.csi"></strong>Class of
                                    Surrounding Improvements
                                </li>
                                <li class="masItem md-masIcon-landscaping"><strong
                                        v-html="property.landscaping"></strong>Landscaping
                                </li>
                                <li class="ludItem md-masIcon-production"><strong v-html="property.production"></strong>Production
                                </li>
                                <li class="masItem md-masIcon-lotPosition"><strong
                                        v-html="property.lotPosition"></strong>Lot Position
                                </li>
                                <li class="masItem md-masIcon-contour"><strong v-html="property.contour"></strong>Contour
                                </li>
                                <li class="masItem md-masIcon-viewScope"><strong v-html="property.viewScope"></strong>View
                                    and Scope
                                </li>
                                <li class="ludItem md-masIcon-maoriLand"><strong v-html="property.maoriLand"></strong>Maori
                                    Land
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div id="dragBlock4" class="dragBlock dragBlockOwnerOccupier" draggable="true">
                    <div id="dragBlockOwnerOccupier" class="md-table mdl-shadow--2dp">
                        <h3>Owner/Occupier Details</h3>
                        <p>TORA Code <span>{{property.tora}}</span>
                            <dl class="toraDescription mdl-shadow--2dp">
                                <dt>Tenure:</dt>
                                <dd>{{property.tenure}}</dd>
                                <dt>Ownership:</dt>
                                <dd>{{property.ownership}}</dd>
                                <dt>Rateability:</dt>
                                <dd>{{property.rateability}}</dd>
                                <dt>Apportionment:</dt>
                                <dd>{{property.apportionment}}</dd>
                            </dl>
                        </p>
                        <div v-if="property.ownersAndOccupiers && property.ownersAndOccupiers.length > 5">
                            <span title="Show Names" class="hideNames mdl-button mdl-js-button mdl-button--icon"><i
                                    class="material-icons md-dark">&#xE3a5;</i></span>
                            <span title="Expand"
                                  class="expandAllOwnersOccupiers mdl-button mdl-js-button mdl-button--icon"><i
                                    class="material-icons md-dark">&#xE89D;</i></span>
                        </div>
                        <div v-else>
                            <span title="Show Names"
                                  class="hideNames right mdl-button mdl-js-button mdl-button--icon"><i
                                    class="material-icons md-dark">&#xE3a5;</i></span>
                        </div>
                        <ul class="md-tableRow md-headerRow">
                            <li class="mediumCell">Type</li>
                            <li class="wideCell">Name</li>
                            <li class="jumboCell">Address</li>
                        </ul>
                        <ul v-for="(ownerOccupier, key) in orderedOwnersAndOccupiers" class="md-tableRow"
                            :class="[key > 4 ? 'excess hide' : '', ownerOccupier.isNameSecret ? 'supressed supressedMark' : '']"
                            :key="key"
                        >
                            <li v-html="ownerOccupier.type"></li>
                            <li v-html="ownerOccupier.fullName"></li>
                            <li v-if="ownerOccupier.order == 1" v-html="ownerOccupier.address"></li>
                            <li v-else></li>
                        </ul>
                    </div>
                </div>

                <div id="dragBlock5" class="dragBlock dragBlockCoT" draggable="true">
                    <div id="dragBlockCoT" class="md-table mdl-shadow--2dp">
                        <h3>Title Information</h3>
                        <div class="qvTitles">
                            <p>QV Titles</p>
                            <ul class="md-titleSummary">
                                <li>Titles: <span v-bind:class="[qvTitles.length == linzTitlesOnly.length ? '' : 'mismatch']">{{ qvTitles.length }}</span></li>
                                <li>Total Area: <span v-bind:class="[property.landArea == linzTitles.totalArea ? '' : 'mismatch']">{{property.landArea}} Ha</span></li>
                            </ul>
                            <dl class="md-titleCard">
                                <dt>Certificates of Title:</dt>
                                <dd>
                                    <span v-for="title,key in qvTitles"
                                        :class="[isMatched(title) ? '' : 'mismatch']"
                                        :key="key">
                                            {{ title }}
                                    </span>
                                </dd>
                                <dt>Legal Descriptions:</dt>
                                <dd>
                                    <span v-html="property.legalDescription"></span>
                                </dd>
                                <dt>Land Area:</dt>
                                <dd>
                                    <span v-bind:class="[property.landArea == linzTitles.totalArea ? '' : 'mismatch']">{{property.landArea}} Ha</span>
                                </dd>
                            </dl>
                        </div>
                        <div class="linzTitles">
                            <p>
                                <!-- TODO: Disabled until LINZ Search Release -->
                                <template v-if="false">
                                    <a href="javascript:void(0);" @click="loadCertificateTitleDetail();">LINZ Title</a>
                                </template>
                                <template v-else>
                                    LINZ Title
                                </template>
                            </p>
                            <ul class="md-titleSummary">
                                <li>Titles: <span>{{ linzTitlesOnly.length }}</span></li>
                                <li>Total Area: <span>{{ formatDecimal(linzTitles.totalArea, 4) }} Ha</span></li>
                            </ul>
                            <div class="linzTitles-wrapper">
                                <dl class="md-titleCard" v-for="title,key in linzTitles.titles" :key="key">
                                    <dt>Certificate of Title:</dt>
                                    <dd><span>{{ composeTitleReference(title) }}</span></dd>
                                    <dt>Legal Description:</dt>
                                    <dd><span>{{ composeLegalDescriptions(title.legalDescriptions) }}</span></dd>
                                    <dt>Land Area:</dt>
                                    <dd><span>{{ formatDecimal(title.landArea, 4) }} Ha</span></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="dragBlock6" class="dragBlock dragBlockSales" draggable="true">
                    <div id="dragBlockSales" class="md-table mdl-shadow--2dp" v-show="sales" data-cy="sale-details">
                        <div class="sales-section-heading">
                            <span >Sale Details</span>
                            <button @click="saleHistoryClickHandler" class="qivs-link qv-bg-orange">HISTORY<i class="material-icons">call_made</i></button>
                            <button @click="addSale" class="qivs-link qv-bg-orange qv-bg-lightbuff:disabled" :disabled="!canAddSale" data-cy="button-add-sale" :data-url="addSaleUrl">ADD SALE</button>
                        </div>
                        <p>{{ sales && sales.length >= 1 ? 'Latest Sale Information' : 'No sales exist for this property' }}</p>
                        <div v-if="sales" class="qv-position-absolute" style="top: 4.8rem; right: 2rem">
                            <button v-if="hasDeletedSales"
                                    @click="toggleDeletedSales"
                                    class="qivs-link qv-bg-mediumblue"
                                    :class="{'qv-bg-error': showDeletedSales}">
                                {{ showDeletedSales ? 'Hide' : 'Show' }} Deleted Sales
                            </button>
                        </div>
                        <span v-if="sales && sales.length > 1" title="Show All Sales"
                              class="expandAll expandAllPropertySales mdl-button mdl-js-button mdl-button--icon"
                              @click="() => expandSaleDetails = !expandSaleDetails"
                              :class="{'down': expandSaleDetails}">
                                <i class="material-icons md-dark">&#xE8D7;</i>
                        </span>
                        <ul v-if="sales && sales.length >= 1" class="md-tableRow md-headerRow salesTable">
                            <li>Agreement</li>
                            <li>Settlement</li>
                            <li>Gross Sale</li>
                            <li>Net Sale</li>
                            <li>Chattels</li>
                            <li>Class</li>
                            <li>Status</li>
                            <li>Sale Id</li>
                            <li>Analysis</li>
                        </ul>
                        <ul v-for="(propSales, key) in salesToShow"
                            class="md-tableRow salesTable"
                            :class="{'excessSales hide': (key > 0 && !expandSaleDetails) || (propSales.isDeleted && !showDeletedSales), 'deleted-sale': propSales.isDeleted}"
                            :key="key"
                            data-cy="qivs-sale"
                        >
                            <li class="qivs-sales" v-if="propSales.saleDate">
                                <router-link :to="{ name: 'property-sale', params: {qpid: property.qupid, id: propSales.qivsSaleId}}" target="_blank">{{ formatDate(propSales.saleDate, 'DD/MM/YYYY') }}</router-link></li>
                            <li v-else>&nbsp;</li>
                            <li v-if="propSales.settlementDate">{{ formatDate(propSales.settlementDate, 'DD/MM/YYYY') }}</li>
                            <li v-else>&nbsp;</li>
                            <li><span v-html="formatPrice(propSales.price.gross, '$0,0')"></span></li>
                            <li><span v-html="formatPrice(propSales.price.net, '$0,0')"></span></li>
                            <li v-html="formatPrice(propSales.price.chattels, '$0,0')"></li>
                            <li>
                                {{ propSales.saleType ? propSales.saleType.code : '' }}{{ propSales.saleTenure ? propSales.saleTenure.code : '' }}{{ propSales.priceValueRelationship ? propSales.priceValueRelationship.code : '' }}
                            </li>
                            <li>{{propSales.status.description}}</li>
                            <li>{{propSales.qivsSaleId}}</li>
                            <li v-if="isInternalUser && propSales.primaryPropertyCategory && propSales.primaryPropertyCategory.code && (propSales.primaryPropertyCategory.code.startsWith('R') || propSales.primaryPropertyCategory.code.startsWith('L'))"
                                class="sales-analysis-link" v-bind:data-sale="propSales.qivsSaleId" @click="tryOpenAnalysisById(propSales.qivsSaleId, true)" data-cy="sales-analysis-link">
                                <a v-if="propSales.hasSaleAnalysis">Yes</a>
                                <a v-else>New</a></li>
                            <li v-else-if="isInternalUser && isRuralSale(propSales)"
                                class="rural-sales-analysis-link"
                                :data-sale="propSales.qivsSaleId"
                                @click="tryOpenAnalysisById(propSales.qivsSaleId, true)"
                            >
                                <a v-if="propSales.hasSaleAnalysis">Yes</a>
                                <a v-else>New</a>
                            </li>
                            <li v-else>N/A</li>
                            <li class="vendorCell" v-html="propSales.parties"></li>
                            <li class="remarksCell" v-html="propSales.remarks"></li>
                        </ul>
                    </div>
                </div>
            </div>

            <framework class="homeValuation property-details-tab-contents" :property="property.id" :qupid="property.qupid" @updateList="updateValuationList" v-if="isInternalUser"></framework>

            <extra-details
                v-if="showExtraDetails"
                class="extraDetails property-details-tab-contents"
                :property="property"
                :rent="{amount: property.predictedWeeklyMarketRent, basis: property.marketRentBasis}"
            />
            <update-sra-values-section
                v-if="showUpdateSraValues"
                :qupid="property.qupid"
                :rollNumber="propertySummary.rollNumber"
                :userName="userName"
                 @closeSraScreen="closeSraScreen"
            />
        <!-- feature/DEV2-855-objections-property-page
            <div v-if="showMap">
                <QVMap class="property-details-tab-contents qvMap"
                    :lat="coordinates.lat"
                    :lng="coordinates.lng"
                    :qupid="property.qupid"
                    :geoserver-url="geoserverUrl"
                />
            </div>
        -->
        </div>
        <warning :header="warningHeader" :message="warningMessage" close="Ok"></warning>

        <alert-modal
            v-if="modal.isOpen"
            :success="modal.mode==='success'"
            :caution="modal.mode==='caution'"
            :warning="modal.mode==='warning'"
        >
            <h1 data-cy="noWorksheetModalHeading">{{ modal.heading }}</h1>
            <p v-if="modal.message !== ''" style="white-space:pre-wrap;" data-cy="noWorksheetModalMessage">{{ modal.message }}</p>
            <template #buttons>
                <div class="alertButtons">
                    <button
                        v-if="modal.cancelText"
                        id="errorCancel"
                        class="mdl-button mdl-button--mini lefty"
                        @click="modal.cancelAction"
                        data-cy="noWorksheetModalCancelButton"
                    >
                        {{ modal.cancelText }}
                    </button>
                    <button
                        v-if="modal.confirmText"
                        id="continue"
                        class="mdl-button mdl-button--mini"
                        @click="modal.confirmAction"
                        data-cy="noWorksheetModalContinueButton"
                    >
                        {{ modal.confirmText }}
                    </button>
                </div>
            </template>
        </alert-modal>
    </div>
</template>

<script>
    import { mapState, mapGetters } from 'vuex';
    import {EventBus} from '../../EventBus.js';
    import {store} from '../../DataStore.js';
    import Warning from '../common/Warning.vue';
    import AlertModal from '../common/modal/AlertModal.vue';
    import ToggleButton from 'vue-js-toggle-button'
    import Framework from '../valuation/Framework.vue';
    import ExtraDetails from '../masterDetails/ExtraDetails.vue';
    import PropertyInfo from '../property/PropertyInfo.vue';
    import UpdateSraValuesSection from '../propertyDetails/sraValues/UpdateSraValuesSection.vue';
    import Slick from '../../slick.js';
    import numeral from 'numeral';
    import Vue from 'vue';
    import orderBy from "lodash/orderBy";
    import formatUtils from '../../utils/FormatUtils';
    import commonUtils from '../../utils/CommonUtils';
    import moment from 'moment';
    import { openUrlInNewTab, openMap } from '../../utils/QivsUtils';
    import axios from '../../utils/AxiosHeaders';
    import VueClipboard from 'vue-clipboard2';
    import { scheduleSurveyReportForQpid } from '../reports/utils';
    import { useSaleAnalysis } from '@/composables/useSaleAnalysis';
    Vue.use(VueClipboard);
    Vue.use(ToggleButton);

    export default {
        components: {
            Warning,
            AlertModal,
            Framework,
            ExtraDetails,
            UpdateSraValuesSection,
            PropertyInfo
        },
        mixins: [formatUtils, commonUtils],
        data: function () {
            return {
                property: {},
                photos: [],
                showTemplate: false,
                nextQupid: '',
                prevQupid: '',
	            coordinates: {
                    lat: null,
                    lng: null
                },
                selectedValRef: '',
                sales: [],
                draftSales: [],
                warningHeader: '',
                warningMessage: '',
                close: 'Ok',
                modal: {
                    mode: 'warning',
                    isOpen: false,
                    heading: 'No Rural Worksheet',
                    message: '',
                    cancelText: 'OK',
                    cancelAction: () => this.modal.isOpen = false,
                    confirmText: 'Create Rural Worksheet',
                    confirmAction: () => {
                        this.modal.isOpen = false;
                        this.goToRuralWorksheet();
                    },
                },
                valuableRuralCategories: 'ADFHPS',
                valuationJobs: [],
                valuationJobsMenu: false,
                salesMenu: false,
                propertySummary: {},
                showDragSection: true,
                dragBlocksCustomised: true,
                canPlatesBeLoaded: false,
                lsDate: null,
                lsStatus: null,
                lsNetPrice: null,
                lsChattels: null,
                lsAnalysedLV: null,
                lsNSPCV: null,
                lsBuildingGrossRate: null,
                lsBuildingNetRate: null,
                lsSaleType: null,
                lsSaleTenure: null,
                lsPriceValueRelationship: null,
                moreThanTwentyPhotos: 0,
                twentyFirstPhotoId: '',
                showExtraDetails: false,
                showUpdateSraValues: false,
                valuationJobIdToDisplay: null,
                linzTitles: [],
                linzTitlesOnly: [],
                initialTab: 0,
                ruralSales: null,
                dataIssuesYN: 'NO',
                issuesCheckedYN: 'NO',
                savedPhotoId: '',
                propertyInfo: {},
                propertyInfoLoaded: false,
                showPropertyInfoSection: false,
                canNavigate: true,
                showDeletedSales: false,
                hasDeletedSales: false,
                expandSaleDetails: false,
                addSaleUrl: '',
            }
        },
        computed: {
            ...mapState('userData', [
                'userName',
                'userId',
                'isInternalUser',
                'isRtvUser',
                'isReportingRevalUser',
                'isReadOnlyUser',
                'qivsUrl',
                'geoserverUrl',
                'googleMapApiKey',
                'linzMapApiKey',
                'isQVMapUser',
                'isTAUser',
                'externalObjectionAccess',
            ]),
            ...mapState('linzSearch', [
                'linzFilterByTitles'
            ]),
            ...mapGetters(['getCategoryClassifications']),
            ...mapState('ruralRtv' , {
                ruralRtvCategories: 'baseCategories',
                ruralRtvValues: 'rtvValues'
            }),
            latestRevalSurveyYear() {
                const dateString = this.propertyInfo.revalSurveys?.[0]?.createdDate;
                if (dateString) {
                    return new Date(dateString).getFullYear();
                }
                return '';
            },
            salesToShow() {
                if (this.showDeletedSales) {
                    return this.sales;
                }
                return this.sales.filter(sale => !sale.isDeleted);
            },
            improvementDateRangeDescriptions() {
                const improvementDateRangeClassifications = this.getCategoryClassifications('ImprovementDateRange');
                // Convert the array of ImprovementDateRange Classifications into a key-value pair of classification code to classification description.
                return improvementDateRangeClassifications.reduce((accumulator, currentValue) => ({ ...accumulator, [currentValue.code]: currentValue.description }), {});
            },

            tagsDescriptions() {
                const tagsClassifications = this.getCategoryClassifications('Tags');
                // Convert the array of Tags Classifications into a key-value pair of classification code to classification description.
                return tagsClassifications.reduce((accumulator, currentValue) => ({ ...accumulator, [currentValue.code]: currentValue.description }), {});
            },

            orderedOwnersAndOccupiers: function () {
                return orderBy(this.property.ownersAndOccupiers, 'order')
            },
            orderedOwners: function () {
                return orderBy(this.property.owners, 'order')
            },
            orderedOccupiers: function () {
                return orderBy(this.property.occupiers, 'order')
            },
            qvTitles: function () {
                var qvTitles = [];
                if(this.property && this.property.certificateOfTitle) {
                    qvTitles = this.property.certificateOfTitle.split(" ");
                }
                return qvTitles;
            },
            isReadOnly: function() {
                return this.isReadOnlyUser;
            },
            isRuralProperty() {
                if (!this.property
                    || !this.property.category
                    || !this.property.category.code) {
                    return false;
                }
                const propertyBaseCategory = this.property.category.code.charAt(0);
                if (this.valuableRuralCategories.includes(propertyBaseCategory)) {
                    return true;
                }
                return false;
            },
            ruralAnalysedLV() {
                return this.ruralSales
                    ? numeral(this.ruralSales.analysedLV ? this.ruralSales.analysedLV : 0).format('$0,0')
                    : null;
            },
            ruralAnalysedLandPerHa() {
                if (this.ruralSales) {
                    const landArea = this.ruralSales.primaryProperty && this.ruralSales.primaryProperty.landUse
                        ? this.ruralSales.primaryProperty.landUse.landArea
                        : null;
                    return this.property && landArea && this.ruralSales.analysedLV
                        ? numeral(this.ruralSales.analysedLV / landArea).format('$0,0')
                        : null;
                }
                return null;
            },
            ruralAnalysedLandPerProdn() {
                if (this.ruralSales) {
                    const production = this.ruralSales.primaryProperty && this.ruralSales.primaryProperty.landUse
                        ? numeral(this.ruralSales.primaryProperty.landUse.production)
                        : null;
                    return this.property && production && this.ruralSales.analysedLV
                        ? numeral(this.ruralSales.analysedLV / production).format('$0,0')
                        : null;
                }
                return null;
            },
            canAddSale: function() {
                const validApportionmentCodes = [0, 2, 5].includes(parseInt(this.property.apportionmentCode));

                return validApportionmentCodes && this.isInternalUser;
            }
        },
        methods: {
            async scheduleSurveyReport() {
                if (!this.isRtvUser && !this.isReportingRevalUser) {
                    return;
                }
                const latestSurveyType = this.propertyInfo.revalSurveys?.[0]?.surveyTypeCode;
                const res = await scheduleSurveyReportForQpid(this.property.qupid, latestSurveyType);
                if (res) {
                    this.modal = {
                        mode: 'message',
                        isOpen: true,
                        heading: 'Export Scheduled',
                        message: 'Your export has been acknowledged and can be viewed in View My Reports.',
                        messages: [],
                        cancelText: 'View My Reports',
                        cancelAction: () => {
                            this.modal.isOpen = false;
                            const routeData = this.$router.resolve({ name: 'report-dashboard-my-reports' });
                            window.open(routeData.href, '_blank');
                        },
                        confirmText: 'OK',
                        confirmAction: () => this.resetModal(),
                        code: 'EXPORT_SCHEDULED_MESSAGE',
                    };
                    this.modal.isOpen = true;
                }
            },
            resetModal() {
                this.modal = {
                    mode: 'warning',
                    isOpen: false,
                    heading: 'No Rural Worksheet',
                    message: '',
                    cancelText: 'OK',
                    cancelAction: () => this.modal.isOpen = false,
                    confirmText: 'Create Rural Worksheet',
                    confirmAction: () => {
                        this.modal.isOpen = false;
                        this.goToRuralWorksheet();
                    },
                };
            },
            async fetchPropertyInfo(qpid) {
                const m = jsRoutes.controllers.PropertyMasterData.getPropertyInfo(qpid);
                try {
                    const response = await fetch(m.url, {
                        method: m.method,
                        cache: 'no-cache',
                    });
                    if (response.status >= 300) {
                        console.error(`Error fetching property info for qpid ${qpid}: `, response);
                        throw new Exception(`Error fetching property info for qpid ${qpid}`, response);
                    }
                    const responseBody = await response.json();
                    return responseBody;
                }
                catch(err) {
                    console.error(`Error fetching property info for qpid ${qpid}: `, err);
                    throw new Exception(`Error fetching property info for qpid ${qpid}`, err);
                }
            },
            isRuralSale(sale) {
                return sale.primaryPropertyCategory && sale.primaryPropertyCategory.code
                    && this.valuableRuralCategories.includes(sale.primaryPropertyCategory.code.charAt(0));
            },
            updatePropertyInfoToggle: function(event, field) {

                const updatePayload = {
                    qpid: this.property.qupid,
                    fieldName: field,
                    value: event.value
                };

                $.ajax({
                    type: "POST",
                    url: jsRoutes.controllers.PropertyMasterData.updatePropertyInfoField().url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(updatePayload),
                    dataType: "json",
                    success: function (response) {
                        console.log("Successfuly Updated Field: ", updatePayload.fieldName);
                    },
                    error: function (response) {
                        self.errorHandler(response);
                    },
                });

            },

            loadPlates: function () {
                const self = this
                if (self.canPlatesBeLoaded) {
                    self.loadMasterDetails(self.selectedValRef, 'forward')
                }
            },

            generatePropertySummary: function (data) {
                const self = this
                var propertySummary = {}
                propertySummary.id = data.id
                propertySummary.rollNumber = data.rollNumber
                propertySummary.assessmentNumber = data.assessmentNumber
                propertySummary.suffix = data.suffix
                var address = {}
                if (data.address) {
                    var propertyAddress = data.address
                    address.streetNumber = propertyAddress.streetNumber
                    address.streetNumberSuffix = propertyAddress.streetNumberSuffix
                    address.streetName = propertyAddress.streetName
                    address.streetType = propertyAddress.streetType
                    address.suburb = propertyAddress.suburb
                    address.town = propertyAddress.town
                }

                propertySummary.territorialAuthority = data.territorialAuthority
                propertySummary.address = address
                self.propertySummary = propertySummary
            },
            tabActionHandler: function(index, id) {
                const self = this;
                // 6 is Map - Retain whatever is previously clicked/visible.
                if (index !== 6) {
                    $(".property-details-tabs").removeClass("active");
                    self.valuationJobIdToDisplay = null;
                    self.valuationJobsMenu = false;
                    self.showExtraDetails = false;
                    self.salesMenu = false;
                    self.showUpdateSraValues = false;
                    self.showPropertyInfoSection = false;
                    self.showDragSection = false;
                }
                // Summary = 0, Extra Details = 1, Home Valuation = 2
                if(index == 0) {
                    self.getPropertyInfo(self.property.qupid);
                    if(self.showDragSection == false) {
                        self.showDragSection = true;
                        self.dragBlocksCustomised = true;
                        setTimeout(function() {
                            $(".summary").addClass("active");
                            $('.property-details-tab-contents').hide();
                            $('.propertySummary').show();
                        }, 500);
                    } else {
                        //Working only  self.showDragSection = true;
                        $('.property-details-tab-contents').hide();
                        $(".summary").addClass("active");
                        $('.propertySummary').show();
                    }
                } else if(index == 1) {
                    self.showExtraDetails = true;
                    $(".extra-details").addClass("active");
                    setTimeout(function() {
                        $('.property-details-tab-contents').hide();
                        $('.extraDetails').show();
                    }, 500);
                } else if(index == 2) {
                    this.initialTab = 2;
                    var event = {};
                    event.type = id ? 'existing' : 'new';
                    event.data = id ? id : self.property;
                    event.propertySummary = self.propertySummary;
                    event.sales = self.sales;
                    event.property = self.property;
                    event.loadDefaultData = true;
                    self.valuationJobIdToDisplay = id;
                    if(id) EventBus.$emit('load-default-property-data', event);
                    EventBus.$emit('home-valuation-job-display', event);
                    $('.property-details-tab-contents').hide();
                    $(".valJobs-menu mdl-shadow--2dp").hide();
                    $(".valuationjobs-class").addClass("active");
                    $('.homeValuation').show();
                } else if(index == 3) {
                    this.$router.push({ name: 'consents-search',  query: { qpid: id }});
                } else if (index == 6){
                    this.openQVMap();
                } else if (index == 7){
                    self.getPropertyInfo(self.property.qupid);
                    self.showUpdateSraValues = true;
                    $(".update-sra-values-section").addClass("active");
                    setTimeout(function() {
                        $('.property-details-tab-contents').hide();
                        $('.updateSraValuesSection').show();
                    }, 500);
                }
            },
            initHideExcessNamesLink: function () {
                $(".hideNames").off("click").click(function () {
                    $(this).toggleClass("down");
                    var down = $(this).hasClass('down');
                    if (down == true) {
                        $('.supressedMark').removeClass('supressed');
                    } else {
                        $('.supressedMark').addClass('supressed');
                    }
                });
            },
            addSale() {
                const url = this.$router.resolve({
                  name: "property-sale-create",
                  params: {
                    qpid: this.property.qupid
                  }
                }).href;

                window.open(url, '_blank');
            },
            initAddSaleLink() {
                const url = this.$router.resolve({
                  name: "property-sale-create",
                  params: {
                    qpid: this.property.qupid
                  }
                }).href;

                this.addSaleUrl = url;
            },
            toggleDeletedSales() {
              this.showDeletedSales = !this.showDeletedSales;

              if (!this.expandSaleDetails) {
                this.expandSaleDetails = true;
              }
            },
            goToRuralWorksheet() {
                this.$router.push({ name: 'rural-worksheet',  params: { id: this.property.qupid }});
            },
            goToRuralRevisionWorksheet() {
                this.$router.push({ name: 'rural-revision-worksheet',  params: { id: this.property.qupid }});
            },
            salesAnalysisClickHandler: function (saleId) {
                var path = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?saleId=' + saleId;
                var saleAnalysis = window.open(path, 'SaleAnalysis', 'scrollbars=yes,resizable=yes,height=800,width=1366');
                saleAnalysis.focus();
            },
            saleHistoryClickHandler() {
                var self = this;
                var url = self.qivsUrl + "/default.asp?Property/Sales/Sales.asp?Qpid=" + self.property.qupid;
                window.open(url, "QIVZ");
            },
            saleDetailsClickHandler: function (saleId, qupid) {
                var self = this;
                var url = self.qivsUrl + "/default.asp?Property/Sales/Sales.asp?sAction=ViewSale&SaleId=" + saleId + "&QPID=" + qupid;
                window.open(url, "QIVZ");
            },
            externalMapLinkHandler: function (url) {
                window.open(url, "Map");
            },
            closeSraScreen: function() {
                this.tabActionHandler(0, this.property.qupid);
            },
            getPropertyInfo: function (qpid){
                let self = this;
                var m = jsRoutes.controllers.PropertyMasterData.getPropertyInfo(qpid);
                self.propertyInfoLoaded = false;
                self.showPropertyInfoSection = false;
                $.ajax({
                        type: "GET",
                        url: m.url,
                        cache: false,
                        success: function (response) {
                            self.propertyInfo.hasAttachments = response.hasAttachments;
                            self.propertyInfo.revalSurveys = response.revalSurveys;
                            self.propertyInfo.hasConsents = response.hasConsents;
                            self.propertyInfo.hasFloorPlans = response.hasFloorPlans;
                            self.propertyInfo.hasObjections = response.hasObjections;
                            self.propertyInfo.hasSRAs = response.hasSRAs;
                            self.propertyInfo.hasSitePlans = response.hasSitePlans;
                            self.propertyInfo.hasSubdivisions = response.hasSubdivisions;
                            self.propertyInfo.hasSurveyPlans = response.hasSurveyPlans;
                            self.propertyInfo.hasValuationData = response.hasValuationData;
                            self.propertyInfo.isAutoMAS = response.isAutoMAS;
                            self.propertyInfo.isAutoPopulateRuralWorksheet = response.isAutoPopulateRuralWorksheet;
                            self.propertyInfo.isAutoPopulateCommercialWorksheet = response.isAutoPopulateCommercialWorksheet;
                            self.propertyInfo.isSuspectValuation = response.isSuspectValuation;
                            self.propertyInfo.hasPropertyFile = response.hasPropertyFile;
                            self.propertyInfo.hasCommercialWorksheet = response.hasCommercialWorksheet;
                            self.propertyInfo.hasRuralWorksheet = response.hasRuralWorksheet;
                            self.propertyInfo.hasCommercialRevisionWorksheet = response.hasCommercialRevisionWorksheet;
                            self.propertyInfo.hasRuralRevisionWorksheet = response.hasRuralRevisionWorksheet;
                            self.propertyInfo.displayRuralWorksheetSection = response.displayRuralWorksheetSection;
                            self.propertyInfo.displayCommercialWorksheetSection = response.displayCommercialWorksheetSection;
                            self.propertyInfo.displayUpdateRevisionValuesNoWorksheet = response.displayUpdateRevisionValuesNoWorksheet;
                            self.propertyInfo.hasNoCurrentWorksheet = !self.propertyInfo.hasCommercialWorksheet && !self.propertyInfo.hasRuralWorksheet;
                            self.propertyInfo.hasNoRevisionWorksheet = !self.propertyInfo.hasCommercialRevisionWorksheet && !self.propertyInfo.hasRuralRevisionWorksheet;
                            self.propertyInfo.isMaoriLand = self.property.maoriLand == 'Yes';
                            self.propertyInfoLoaded = true;
                            self.showPropertyInfoSection = true;
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            console.log('Error fetching property info: ' + response);
                        }
                });

            },
            loadMasterDetails: function (id, direction) {
                if (direction) {
                    const self = this;
                    self.showPropertyInfoSection = false;
                    self.valuationJobIdToDisplay = null;
                    if (id) {
                        self.initialTab = 0;
                        self.showDragSection = false;
                        self.dragBlocksCustomised = false;
                        self.canPlatesBeLoaded = false;
                        var m = jsRoutes.controllers.PropertyMasterData.getProperty(id);
                        $.ajax({
                            type: "GET",
                            url: m.url,
                            cache: false,
                            success: function (response) {
                                self.generatePropertySummary(response.property);
                                self.property = self.generateMasterDetailsData(response, self.qivsUrl)
                                self.$nextTick(function () {
                                    self.getPhotos(id);
                                    self.getHomeValuationJobs(id);
                                });
                            },
                            error: function (response) {
                                self.errorHandler(response);
                                console.log('error fetch property search results: ' + response);
                            }
                        });
                    }
                    else {
                        self.warningHeader = 'Navigate Assessment';
                        if (direction == 'back') {
                            self.warningMessage = 'You are at the first assessment of this roll.';
                        }
                        else {
                            self.warningMessage = 'You are at the last assessment of this roll.';
                        }
                        $('.warning').show();
                    }
                }
            },
            populatePropertyDescription: function (propertyId, rawPropertyData) {
                var self = this;
                var m = jsRoutes.controllers.PropertyMasterData.getPropertyDescription(propertyId);
                $.ajax({
                    type: "POST",
                    url: m.url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(rawPropertyData),
                    dataType: "json",
                    success: function (response) {
                        self.property.propertyDesc = response && response.summaryText ? response.summaryText : ''
                        self.populateStatsSummary(rawPropertyData.qupid, rawPropertyData);
                    },
                    error: function (response) {
                        console.log('error fetching property description: ' + response);
                        self.errorHandler(response);
                        self.populateStatsSummary(rawPropertyData.qupid, rawPropertyData);
                    }
                });
            },
            populateSales: function (qupid) {
                const self = this;
                var m = jsRoutes.controllers.SalesSearch.getSalesByQupid();
                var postData = {};
                postData.locationCriteria = {};
                postData.locationCriteria.qupid = qupid;
                postData.sort = ['SALE_DATE'];
                postData.order = 'DESC';
                postData.showDeleted = self.isInternalUser;
                if(!self.isInternalUser) {
                    postData.classificationCriteria = { saleStatusCodes : ['1'] };
                }
                $.ajax({
                    type: "POST",
                    url: m.url,
                    cache: false,
                    contentType: "application/json; charset=utf-8",
                    data: JSON.stringify(postData),
                    dataType: "json",
                    success: function (response) {
                        self.sales = response;
                        let latestSale = null;
                        for (const sale of self.sales) {
                            if (!sale.isDeleted) {
                                latestSale = sale;
                                break;
                            }
                        }
                        self.lsDate = latestSale ? self.formatDate(latestSale.saleDate) : 'No sale on record';
                        self.lsStatus = latestSale ? latestSale.status.description : null;
                        self.lsNetPrice = latestSale && latestSale.price ? numeral(latestSale.price.net ? latestSale.price.net : 0).format('$0,0') : null;
                        self.lsChattels = latestSale && latestSale.price ? numeral(latestSale.price.chattels ? latestSale.price.chattels : 0).format('$0,0') : null;
                        self.lsAnalysedLV = latestSale && latestSale.saleAnalysis ? numeral(latestSale.saleAnalysis.totalAnalysedLandValue ?
                                latestSale.saleAnalysis.totalAnalysedLandValue : 0).format('$0,0') : null;
                        self.lsSaleType = latestSale ? latestSale.saleType : null;
                        self.lsSaleTenure = latestSale ? latestSale.saleTenure : null;
                        self.lsPriceValueRelationship = latestSale ? latestSale.priceValueRelationship : null;

                        var nSPCV = 0;
                        var netPrice = latestSale && latestSale.price ? latestSale.price.net : 0;
                        if (self.capitalValue > 0) {
                            nSPCV = (Math.round((netPrice/self.capitalValue)*100))/100;
                        }
                        self.lsNSPCV = latestSale ? nSPCV : null;

                        if(self.sales.some((s) => s.isDeleted)) {
                            self.hasDeletedSales = true;
                        }


                        if(latestSale && latestSale.saleAnalysis && latestSale.saleAnalysis.analysedMainUnitGrossRate)
                            self.lsBuildingGrossRate = numeral(latestSale.saleAnalysis.analysedMainUnitGrossRate).format('$0,0');

                        //TODO: this is an intermittent step to deal with existing Sale Analysis that have not yet filled the gross rate.
                        // It should be removed eventually - Chris L 02 Oct 2020
                        else if(latestSale && latestSale.saleAnalysis && netPrice > 0 && self.totalFloorArea > 0)
                            self.lsBuildingGrossRate = numeral(Math.round(netPrice/self.totalFloorArea)).format('$0,0');

                        else
                            self.lsBuildingGrossRate = null;


                        self.lsBuildingNetRate = latestSale && latestSale.saleAnalysis ? numeral(latestSale && latestSale.saleAnalysis.analysedMainBuilding ?
                                latestSale.saleAnalysis.analysedMainBuilding.pricePerSquareMeter : 0).format('$0,0') : null;

                        if(self.valuationJobIdToDisplay) {
                            self.valuationJobIdToDisplay = null;
                        } else {
                            self.showDragSection = true;
                            self.dragBlocksCustomised = true;
                            self.tabActionHandler(self.initialTab);
                        }

                        if (self.isRuralProperty) {
                            self.ruralSales = null;
                            self.getRuralSaleAnalysis(latestSale.qivsSaleId);
                        }
                    },
                    error: function (response) {
                        console.log('error fetching sales for property: ' + response);
                        self.errorHandler(response);
                        self.sales = [];
                        if(self.valuationJobIdToDisplay) {
                            self.valuationJobIdToDisplay = null;
                        } else {
                            self.showDragSection = true;
                            self.dragBlocksCustomised = true;
                            self.tabActionHandler(self.initialTab);
                        }
                    }
                });
            },
            populateStatsSummary: function (propertyId, data) {
                var self = this;
                self.totalFloorArea = data.landUseData ? data.landUseData.totalFloorArea : 0;
                self.landArea = data.landUseData ? data.landUseData.landArea : 0;
                self.capitalValue = data.currentValuation ? data.currentValuation.capitalValue : 0;
                self.landValue = data.currentValuation ? data.currentValuation.landValue : 0;
                self.territorialAuthorityId = data.territorialAuthority ? data.territorialAuthority.code : '';
                var m = jsRoutes.controllers.PropertyMasterData.getStatsSummary(propertyId);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        if (!response) {
                            alert('Error fetching stats summary');
                            return;
                        }
                        var data = response;
                        self.property.tapid = data.tapid;
                        self.property.revisionCapVal = data.revisionCapitalValue ? numeral(data.revisionCapitalValue).format('$0,0') : '$0';
                        self.property.revisionLandVal = data.revisionLandValue ? numeral(data.revisionLandValue).format('$0,0') : '$0';
                        self.property.hasRevision = data.revisionCapitalValue || data.revisionLandValue;
                        self.property.predictedWeeklyMarketRent = data.predictedWeeklyMarketRent;
                        self.property.marketRentBasis = data.marketRentBasis;
                        var revalCapVal = data.revisionCapitalValue ? data.revisionCapitalValue : 0;
                        var revalLandVal = data.revisionLandValue ? data.revisionLandValue : 0;
                        var revalValueOfImprovements = 0;
                        if (revalCapVal > 0 && revalLandVal > 0) {
                            revalValueOfImprovements = Math.round(revalCapVal - revalLandVal);
                        }
                        self.property.revalValueOfImprovements = numeral(revalValueOfImprovements).format('$0,0');
                        var revalCapValNetRate = 0;
                        if (revalCapVal > 0 && self.totalFloorArea > 0) {
                            revalCapValNetRate = Math.round(revalCapVal / self.totalFloorArea);
                        }
                        self.property.revalCapValNetRate = numeral(revalCapValNetRate).format('$0,0');
                        var revalLandValNetRate = 0;
                        if (revalLandVal > 0 && self.landArea > 0) {
                            revalLandValNetRate = Math.round(revalLandVal / (self.landArea * 10000));
                            if (self.landArea >= 1){
                                revalLandValNetRate = Math.round(revalLandVal / self.landArea);
                            }
                        }
                        self.property.revalLandValNetRate = numeral(revalLandValNetRate).format('$0,0');
                        var revalValueOfImprovementsNetRate = 0;
                        if (self.totalFloorArea > 0) {
                            revalValueOfImprovementsNetRate = Math.round((revalCapVal - revalLandVal) / self.totalFloorArea);
                        }
                        self.property.revalValueOfImprovementsNetRate = numeral(revalValueOfImprovementsNetRate).format('$0,0');
                        var capitalValueDiff = 0;
                        if (self.capitalValue && self.capitalValue > 0 && revalCapVal > 0) {
                            capitalValueDiff = ((revalCapVal * 100) / self.capitalValue) - 100;
                        }
                        self.property.capitalValueDiff = (Math.round(capitalValueDiff * 10)) / 10;
                        var landValueDiff = 0
                        if (self.landValue && self.landValue > 0 && revalLandVal > 0) {
                            landValueDiff = ((revalLandVal * 100) / self.landValue) - 100;
                        }
                        self.property.landValueDiff = (Math.round(landValueDiff * 10)) / 10;
                        var valueOfImprovements = 0
                        if (self.capitalValue) {
                            valueOfImprovements = Math.round(self.capitalValue - self.landValue);
                        }

                        var valueOfImprovementsDiff = 0;
                        if (valueOfImprovements > 0) {
                            valueOfImprovementsDiff = (((revalCapVal - revalLandVal) * 100) / valueOfImprovements) - 100;
                        }
                        self.property.valueOfImprovementsDiff = (Math.round(valueOfImprovementsDiff * 10)) / 10;
                        self.property.marketEstimateDate = data.marketEstimateDate ? self.formatDate(data.marketEstimateDate) : '';
                        self.property.marketEstimateValue = data.marketEstimateValue ? numeral(data.marketEstimateValue).format('$0,0') : 'N/A';
                        self.property.realTimeDate = data.realTimeValueDate ? self.formatDate(data.realTimeValueDate) : '-';
                        self.property.realTimeValue = data.realTimeValue ? numeral(Math.round(data.realTimeValue / 1000) * 1000).format('$0,0') : 'N/A';
                        var rawMarketEstimateValue = data.marketEstimateValue ? data.marketEstimateValue : 0;
                        self.property.marketEstimateToCapitalValue = self.capitalValue && data.marketEstimateValue ? Math.round(((rawMarketEstimateValue * 100) / self.capitalValue) - 100) + '%' : '-';
                        self.property.marketEstimateToCapitalValueUpDown = self.capitalValue && data.marketEstimateValue ? Math.round(((rawMarketEstimateValue * 100) / self.capitalValue) - 100) : null;

                        //TODO: Remove rtv
                        var rawRealTimeValue = data.realTimeValue ? data.realTimeValue : 0;
                        self.property.realTimeToCapitalValue = self.capitalValue &&  data.realTimeValue ? Math.round(((rawRealTimeValue * 100) / self.capitalValue) - 100) + '%' : '-';
                        self.property.realTimeToCapitalValueUpDown = self.capitalValue &&  data.realTimeValue ? Math.round(((rawRealTimeValue * 100) / self.capitalValue) - 100) : null;

                        self.populateTASummary(self.territorialAuthorityId);
                        self.property.realTimeLandValue = data.realTimeLandValue ? numeral(data.realTimeLandValue).format('$0,0') : 'N/A';
                        self.property.estimatedLandValue = numeral(data.estimatedLandValue).format('$0,0');
                        self.property.realTimeLandValueToLandValue = self.landValue && data.realTimeLandValue ? Math.round(((data.realTimeLandValue * 100) / self.landValue) - 100) + '%' : '-';
                        self.property.realTimeLandValueToLandValueUpDown = self.landValue && data.realTimeLandValue ? Math.round(((data.realTimeLandValue * 100) / self.landValue) - 100) : null;

                        self.loadRuralRtvValues();
                    },
                    error: function (response) {
                        console.log('error fetch property search results: ' + response);
                        self.errorHandler(response);
                        self.populateTASummary(self.territorialAuthorityId);
                    }
                });
            },
            populateTASummary: function (propertyId) {
                var self = this;
                var m = jsRoutes.controllers.PropertyMasterData.getTASummary(propertyId);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        self.property.nextRatingValuationDate = response.nextRevisionDate ? 'As at ' + self.formatDate(response.nextRevisionDate, 'DD MMMM YYYY') : '';
                        self.property.currentRevisionDate = response.currentRevisionDate ? self.formatDate(response.currentRevisionDate) : '';
                        self.property.rawCurrentRevisionDate = response.currentRevisionDate ? response.currentRevisionDate : '';
                        self.populateSales(self.property.qupid);
                    },
                    error: function (response) {
                        console.log('error fetch property search results: ' + response);
                        self.errorHandler(response);
                        self.populateSales(self.property.qupid);
                    }
                });
            },
            populateHPIData: function (taCode) {
                var m = jsRoutes.controllers.TADashboardController.displayGraphs(taCode);
                var self = this;
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        var HPI = response ? (response.taMarketSummary ? response.taMarketSummary.taSinceRevalMarketMovement : 1) : 1;
                        self.property.hpi = HPI ? (HPI / 100) + 1 : '';
                    },
                    error: function (response) {
                        console.log('error fetch property HPI data: ' + response);
                        self.errorHandler(response);
                        self.property.hpi = '';
                    }
                });
            },
            generateMasterDetailsData: function (response, qivsURL) {
                var self = this;
                var data = response.property;
                data.massAppraisalData.numberOfBathrooms = response.numberOfBathrooms;
                var property = {};
                property.classifications = data.massAppraisalData ? data.massAppraisalData.classifications : [];
                property.previousPropertyId = response.previousProperty;
                property.nextPropertyId = response.nextProperty;
                var valRefList = [];

                $.each(response.propertiesList, function (i, obj) {
                    var valRef = obj.rollNumber + '/' + obj.assessmentNumber + ' ' + obj.suffix;
                    var propertyId = obj.id;
                    var valRefObj = {};
                    valRefObj.valuationReference = valRef;
                    valRefObj.propertyId = propertyId;
                    valRefList.push(valRefObj);
                });

                property.valRefList = valRefList;
                property.valuationReference = (data.rollNumber ? data.rollNumber + '/' : '') + (data.assessmentNumber ? data.assessmentNumber : '') + ' ' + (data.suffix ? data.suffix : '');
                self.selectedValRef = data.id;
                property.id = data.id;
                property.qupid = data.qupid;
                property.territorialAuthorityId = data.territorialAuthority ? data.territorialAuthority.code : '';
                property.certificateOfTitle = data.certificateOfTitle;
                property.legalDescription = data.legalDescription;
                property.extensions = data.massAppraisalData ? data.massAppraisalData.extensions : 0;
                property.maoriLand = data.landUseData ? (data.landUseData.isMaoriLand ? 'Yes' : 'No') : 'No';
                property.planNumber = data.planNumber;

                var streetNumberSuffix = data.address ? (data.address.streetNumberSuffix ? ' ' + data.address.streetNumberSuffix : '') : '';
                property.address1 = data.address ? ((data.address.streetNumber ? data.address.streetNumber : '') + streetNumberSuffix + ' ' + (data.address.streetName ? data.address.streetName : '')
                + (data.address.streetType ? ' ' + data.address.streetType.description + ',' : '')) : '';
                if (property.address1.indexOf('undefined') !== -1) {
                    property.address1 = '';
                }
                property.address2 = data.address ? (data.address.suburb ? (data.address.suburb + ', ') : '') : '';
                property.address2 += data.address ? (data.address.town ? data.address.town + ', ' : '') : '';
                property.address2 += data.territorialAuthority ? data.territorialAuthority.name : '';
                if (property.address2.indexOf('undefined') !== -1) {
                    property.address2 = '';
                }

                property.category = data.category;
                property.TLA = data.massAppraisalData ? (data.massAppraisalData.totalLivingArea ? data.massAppraisalData.totalLivingArea : '0') : '0';
                property.TFA = data.landUseData ? (data.landUseData.totalFloorArea ? data.landUseData.totalFloorArea : '0') : '0';
                property.MLA = data.massAppraisalData ? (data.massAppraisalData.mainLivingArea ? data.massAppraisalData.mainLivingArea : '0') : '0';
                property.landArea = data.landUseData ? (data.landUseData.landArea ? data.landUseData.landArea : 0) : 0;
                if (typeof property.landArea == 'number') {
                    property.landArea = property.landArea.toFixed(4);
                }

                property.capitalValue = data.currentValuation ? numeral(data.currentValuation.capitalValue).format('$0,0') : '$0';
                property.landValue = data.currentValuation ? numeral(data.currentValuation.landValue).format('$0,0') : '$0';
                property.buildingNetRate = data.buildingNetRate ? numeral(data.buildingNetRate).format('$0,0') : '$0';
                property.revisedBuildingNetRate = data.revisedBuildingNetRate ? numeral(data.revisedBuildingNetRate).format('$0,0') : '$0';

                property.rawCapitalValue = data.currentValuation ? data.currentValuation.capitalValue : 0;
                property.rawLandValue = data.currentValuation ? data.currentValuation.landValue : 0;

                var valueOfImprovements = 0;
                var landValue = data.currentValuation ? data.currentValuation.landValue : 0;
                var capitalValue = data.currentValuation ? data.currentValuation.capitalValue : 0;
                var totalFloorArea = data.landUseData ? data.landUseData.totalFloorArea : 0;
                var landArea = data.landUseData ? data.landUseData.landArea : 0;

                if (capitalValue) {
                    valueOfImprovements = Math.round(capitalValue - landValue);
                }
                property.valueOfImprovements = numeral(valueOfImprovements).format('$0,0');

                var cvNetRate = 0;
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0) {
                    cvNetRate = Math.round(capitalValue / totalFloorArea);
                }
                property.cvNetRate = numeral(cvNetRate).format('$0,0');
                var lvNetRate = 0;
                property.showHectares = false;
                if (landArea < 1 && landArea > 0 && landValue && landValue > 0) {
                    lvNetRate = Math.round(landValue / (landArea * 10000));
                }
                if (landArea >= 1 && landValue && landValue > 0) {
                    lvNetRate = Math.round(landValue / landArea);
                    property.showHectares = true;
                }
                property.lvNetRate = numeral(lvNetRate).format('$0,0');

                var viNetRate = 0;
                if (totalFloorArea > 0 && capitalValue && capitalValue > 0 && landValue && landValue > 0) {
                    viNetRate = Math.round((capitalValue - landValue) / totalFloorArea);
                }
                property.viNetRate = numeral(viNetRate).format('$0,0');

                property.propertyPhotos = self.getPropertyPhotos(response.photos, property.qupid).list;
                property.natureOfImprovements = data.natureOfImprovements;
                property.buildingAge = self.getRenderableValue(data.landUseData ? (data.landUseData.buildingAge ? data.landUseData.buildingAge.description : '') : '');
                property.siteCoverage = self.getRenderableValue(data.landUseData ? data.landUseData.buildingSiteCover : '');
                property.carParks = self.getRenderableValue(data.landUseData ? data.landUseData.carparks : '');
                property.csi = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.classOfSurroundingImprovements ? data.massAppraisalData.classifications.classOfSurroundingImprovements.description : '') : '') : '');
                property.houseType = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.houseType ? data.massAppraisalData.classifications.houseType.description : '') : '') : '');
                property.houseTypeObj = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.houseType ? data.massAppraisalData.classifications.houseType : null) : null) : null);
                property.landscaping = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.landscapingQuality ? data.massAppraisalData.classifications.landscapingQuality.description : '') : '') : '');
                property.deck = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasDeck ? 'Yes' : 'No') : '');
                property.foundation = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasPoorFoundations ? 'Yes' : 'No') : '');
                property.laundryWorkshop = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasLaundry ? 'Yes' : 'No') : '');
                property.carAccess = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasCarAccess ? 'Yes' : 'No') : '');
                property.driveway = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasDriveway ? 'Yes' : 'No') : '');
                property.outlier = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.isOutlier ? 'Yes' : 'No') : '');
                property.effectiveYearBuilt = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.effectiveYearBuilt : '');
                property.landUse = self.getRenderableValue(data.landUseData ? (data.landUseData.landUse ? data.landUseData.landUse.description : '') : '');
                property.units = self.getRenderableValue(data.landUseData ? data.landUseData.units : '');
                property.bedrooms = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.bedrooms : '');
                property.bathrooms = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.numberOfBathrooms : '');
                property.toilets = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.toilets : '');
                property.wallConstructionAndCondition = data.landUseData ? (data.landUseData.wallConstruction ? data.landUseData.wallConstruction.description : '') : '';
                property.wallConstruction = data.landUseData ? (data.landUseData.wallConstruction ? data.landUseData.wallConstruction : null) : null;
                property.wallConstructionAndCondition += data.landUseData ? (data.landUseData.wallCondition ? ' ' + data.landUseData.wallCondition.description : '') : '';
                property.wallConstructionAndCondition = self.getRenderableValue(property.wallConstructionAndCondition);
                property.roofConstruction = data.landUseData ? (data.landUseData.roofConstruction ? data.landUseData.roofConstruction : null) : null;
                property.roofConstructionAndCondition = data.landUseData ? (data.landUseData.roofConstruction ? data.landUseData.roofConstruction.description : '') : '';
                property.roofConstructionAndCondition += data.landUseData ? (data.landUseData.roofCondition ? ' ' + data.landUseData.roofCondition.description : '') : '';
                property.roofConstructionAndCondition = self.getRenderableValue(property.roofConstructionAndCondition);
                property.underMainRoofGarages = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.underMainRoofGarages : '');
                property.freeStandingGarages = self.getRenderableValue(data.massAppraisalData ? data.massAppraisalData.freestandingGarages : '');
                property.otherLargeImprovements = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.hasLargeOtherImprovements ? 'Yes' : 'No') : 'No');
                property.modernisation = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.isModernised ? 'Yes' : 'No') : 'No');
                property.zone = self.getRenderableValue(data.landUseData ? data.landUseData.landZone : '');
                property.lotPosition = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.lotPosition ? data.massAppraisalData.classifications.lotPosition.description : '') : '') : '');
                property.contour = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.contour ? data.massAppraisalData.classifications.contour.description : '') : '') : '');
                var viewDescription = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.view ? (data.massAppraisalData.classifications.view.description + ' ') : '') : '') : '');
                var viewCode = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.view ? (data.massAppraisalData.classifications.view.code + ' ') : '') : '') : '');
                if (viewCode.trim() == 'N') {
                    property.viewScope = self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.viewScope ? data.massAppraisalData.classifications.viewScope.description : '') : '') : '')
                } else {
                    property.viewScope = ((viewDescription && viewDescription != '-') ? viewDescription.substring(viewDescription.trim().lastIndexOf(" ") + 1) : '') + self.getRenderableValue(data.massAppraisalData ? (data.massAppraisalData.classifications ? (data.massAppraisalData.classifications.viewScope ? data.massAppraisalData.classifications.viewScope.description : '') : '') : '');
                }
                property.production = self.getRenderableValue(data.landUseData ? data.landUseData.production : '');
                if (qivsURL && qivsURL != '') {
                    property.qivsURL = qivsURL;
                }

                var tora = ''
                if (data.tenure && data.tenure.code) {
                    tora += data.tenure.code;
                    property.tenure = data.tenure.description;
                }
                if (data.ownership && data.ownership.code) {
                    tora += data.ownership.code;
                    property.ownership = data.ownership.description;
                }
                if (data.rateability && data.rateability.code) {
                    tora += data.rateability.code;
                    property.rateability = data.rateability.description;
                }
                if (data.apportionment && data.apportionment.code) {
                    tora += data.apportionment.code;
                    property.apportionment = data.apportionment.description;
                    property.apportionmentCode = data.apportionment.code;
                }
                property.tora = tora;

                var owners = data.owners;
                var occupiers = data.occupiers;
                var propertyOwnersAndOccupiers = [];
                var propertyOwners = [];
                var propertyOccupiers = [];
                $.each(owners, function (i, obj) {
                    var propertyOwner = {};
                    propertyOwner.fullName = (obj.firstName ? obj.firstName + ' ' : '') + (obj.secondName ? obj.secondName + ' ' : '') + (obj.thirdName ? obj.thirdName + ' ' : '' )
                            + (obj.lastName ? obj.lastName + ' ' : '');
                    propertyOwner.type = 'Owner';
                    propertyOwner.order = obj.order;
                    propertyOwner.isNameSecret = obj.isNameSecret;
                    if (obj.mailingAddress) {
                        var mailingAddress = obj.mailingAddress;
                        propertyOwner.address = (mailingAddress.careOf ? mailingAddress.careOf + ', ' : '') +
                                (mailingAddress.organisation ? mailingAddress.organisation + ', ' : '') +
                                (mailingAddress.unit ? mailingAddress.unit + ', ' : '') +
                                (mailingAddress.building ? mailingAddress.building + ', ' : '') +
                                (mailingAddress.streetAddress ? mailingAddress.streetAddress + ', ' : '') +
                                (mailingAddress.suburb ? mailingAddress.suburb + ', ' : '') +
                                (mailingAddress.town ? mailingAddress.town : '') +
                                (mailingAddress.postcode ? ' ' + mailingAddress.postcode : '') +
                                (mailingAddress.country ? ', ' + mailingAddress.country : '');
                    }
                    propertyOwners.push(propertyOwner);
                });

                $.each(occupiers, function (i, obj) {
                    var propertyOccupier = {};
                    propertyOccupier.fullName = (obj.firstName ? obj.firstName + ' ' : '') + (obj.secondName ? obj.secondName + ' ' : '') + (obj.thirdName ? obj.thirdName + ' ' : '' )
                            + (obj.lastName ? obj.lastName + ' ' : '');
                    propertyOccupier.type = 'Occupier';
                    propertyOccupier.order = obj.order;
                    propertyOccupier.isNameSecret = obj.isNameSecret;
                    if (obj.mailingAddress) {
                        var mailingAddress = obj.mailingAddress
                        propertyOccupier.address = (mailingAddress.careOf ? mailingAddress.careOf + ', ' : '') +
                                (mailingAddress.organisation ? mailingAddress.organisation + ', ' : '') +
                                (mailingAddress.unit ? mailingAddress.unit + ', ' : '') +
                                (mailingAddress.building ? mailingAddress.building + ', ' : '') +
                                (mailingAddress.streetAddress ? mailingAddress.streetAddress + ', ' : '') +
                                (mailingAddress.suburb ? mailingAddress.suburb + ', ' : '') +
                                (mailingAddress.town ? mailingAddress.town : '') +
                                (mailingAddress.postcode ? ' ' + mailingAddress.postcode : '') +
                                (mailingAddress.country ? ', ' + mailingAddress.country : '');
                    }
                    propertyOccupiers.push(propertyOccupier);
                });
                property.owners = propertyOwners;
                property.occupiers = propertyOccupiers;
                property.ownersAndOccupiers = propertyOwners.concat(propertyOccupiers);

                property.propertySales = data.sales;
                property.massAppraisal = data.massAppraisalData;
                property.rawLandUse = data.landUseData;

                if(property.maoriLand == 'Yes') {
                    property.lumpSumApportionmentYN = ['0', '2', '5'].includes(data.apportionment.code) ?'Yes' : 'No';
                    property.cml = self.calculateMaorilandValues(data.maoriLandData.currentMaoriLandAdjustment, landArea, totalFloorArea);
                    property.rml = self.calculateMaorilandValues(data.maoriLandData.revisedMaoriLandAdjustment, landArea, totalFloorArea, property.cml);
                    property.mlnoOfOwners = data.maoriLandData.numberOfOwners;
                    this.getMaoriLandLumpSums(property.qupid);
                }
                self.getLinzTitles(property.qupid);
                self.populateHPIData(property.territorialAuthorityId);
                self.populatePropertyDescription(property.id, data);
                this.getCurrentRollLandValueIndex(property.id);

                if (data.coordinates) {
                    self.coordinates.lat = data.coordinates.latitude;
                    self.coordinates.lng = data.coordinates.longitude;
                }

                property.propertiesList = response.propertiesList;
                return property;
            },
            async getCurrentRollLandValueIndex(propertyId) {
                try{
                    const response = await axios({
                        method: 'get',
                        url: jsRoutes.controllers.PropertyMasterData.getCurrentRollLandValueIndex(propertyId).url,
                    });
                    this.property.landValueIndex= response.data;
                }
                catch (exception) {
                    throw exception;
                }
            },
            calculateMaorilandValues: function (adjustment, landArea, totalFloorArea, currentAdjustment) {
                var self = this;
                var maoriLand = {}
                var mlCvNetRate = 0;
                var mlLvNetRate = 0;
                var mlViNetRate = 0;
                var unadjustedValuation = adjustment.unadjustedValuation;
                var mlCapitalValue = unadjustedValuation.capitalValue ? unadjustedValuation.capitalValue : 0;
                var mlLandValue = unadjustedValuation.landValue ? unadjustedValuation.landValue : 0;
                var mlVi = mlCapitalValue - mlLandValue;

                if (totalFloorArea > 0 && mlCapitalValue > 0) {
                    mlCvNetRate = Math.round(mlCapitalValue / totalFloorArea);
                }
                maoriLand.rawCapitalValue = mlCapitalValue;
                maoriLand.capitalValue = numeral(mlCapitalValue).format('$0,0');
                maoriLand.cvNetRate = numeral(mlCvNetRate).format('$0,0');

                if (landArea > 0 && mlLandValue > 0) {
                    mlLvNetRate = Math.round(mlLandValue / (landArea * 10000));
                }
                maoriLand.rawLandValue = mlLandValue;
                maoriLand.landValue = numeral(mlLandValue).format('$0,0');
                maoriLand.lvNetRate = numeral(mlLvNetRate).format('$0,0');

                if (totalFloorArea > 0 && mlVi > 0) {
                    mlViNetRate = Math.round(mlVi / totalFloorArea);
                }
                maoriLand.rawVi = mlVi;
                maoriLand.vi = numeral(Math.round(mlCapitalValue - mlLandValue)).format('$0,0');
                maoriLand.viNetRate = numeral(mlViNetRate).format('$0,0');

                var mlSignificance = adjustment.siteSignificanceAdjustmentPercentage ? adjustment.siteSignificanceAdjustmentPercentage : 0;
                var mlOwners = adjustment.multipleOwnerAdjustmentPercentage ? adjustment.multipleOwnerAdjustmentPercentage : 0;
                maoriLand.adjustment = numeral(self.round((+mlSignificance + +mlOwners), 1)).format('0,0.0');
                maoriLand.significance = numeral(self.round(mlSignificance, 1)).format('0,0.0');
                maoriLand.owners = numeral(self.round(mlOwners, 1)).format('0,0.0');

                if(currentAdjustment) {
                    var capitalValueDiff = 0;
                    if (currentAdjustment.rawCapitalValue > 0 && mlCapitalValue > 0) {
                        capitalValueDiff = ((mlCapitalValue * 100) / currentAdjustment.rawCapitalValue) - 100;
                    }
                    maoriLand.capitalValueDiff = (Math.round(capitalValueDiff * 10)) / 10;
                    var landValueDiff = 0
                    if (currentAdjustment.rawLandValue > 0 && mlLandValue > 0) {
                        landValueDiff = ((mlLandValue * 100) / currentAdjustment.rawLandValue) - 100;
                    }
                    maoriLand.landValueDiff = (Math.round(landValueDiff * 10)) / 10;
                    var valueOfImprovementsDiff = 0;
                    if (currentAdjustment.rawVi > 0 && mlVi > 0) {
                        valueOfImprovementsDiff = ((mlVi * 100) / currentAdjustment.rawVi) - 100;
                    }
                    maoriLand.valueOfImprovementsDiff = (Math.round(valueOfImprovementsDiff * 10)) / 10;
                }
                return maoriLand;
            },
            handleDrop: function (e) {
                var self = this
                if (e.stopPropagation) {
                    e.stopPropagation();
                }
                var target = e.target || e.srcElement;
                while (true) {
                    if (target == null || target.nodeName == "body" || target.getAttribute("draggable") == "true" || target.getAttribute("draggable") == true) {
                        break;
                    } else {
                        target = target.parentElement;
                    }
                }
                if (this.dragSrcEl_ != target) {
                    localStorage.setItem(self.userId + target.id, this.dragSrcEl_.children[0].id);
                    localStorage.setItem(self.userId + this.dragSrcEl_.id, target.children[0].id);
                    this.dragSrcEl_.innerHTML = target.innerHTML;
                    target.innerHTML = e.dataTransfer.getData('text');
                    $('.dragBlock').css('opacity', '1');
                    $('.dragBlock').removeClass('over');
                    $('.dragBlock').removeClass('moving');
                    self.initExpandAllOwnersOccupiers();
                    self.initHideExcessNamesLink();
                }
                return false;
            },
            initMasterDetailsCustomisation: function (userId) {
                var id_ = 'dragSection';
                this.boxes_ = document.querySelectorAll('#' + id_ + ' .dragBlock');
                var self = this;
                [].forEach.call(this.boxes_, function (box) {
                    box.setAttribute('draggable', 'true');
                    box.addEventListener('dragstart', self.handleDragStart, false);
                    box.addEventListener('dragenter', self.handleDragEnter, false);
                    box.addEventListener('dragover', self.handleDragOver, false);
                    box.addEventListener('dragleave', self.handleDragLeave, false);
                    box.addEventListener('drop', self.handleDrop, false);
                    box.addEventListener('dragend', self.handleDragEnd, false);
                });

                var dragSectionHtml = $($('#dragSection').html());
                var defaultOrder = ['dragBlockPropertySummary', 'dragBlockRevisionBox', 'dragBlockOtherInfos',
                    'dragBlockOwnerOccupier', 'dragBlockCoT', 'dragBlockSales'];
                var newOrder = {};
                var newOrderArray = [];
                var blockKeys = [];
                for (var i = 1; i < 7; i++) {
                    var dragContent = localStorage[userId + 'dragBlock' + i];
                    if($.inArray(dragContent, newOrderArray) > -1) blockKeys.push(i);
                    newOrder[i] = dragContent;
                    if(dragContent) newOrderArray.push(dragContent);
                }
                if(newOrderArray.length > 0) {
                    var diff = $(defaultOrder).not(newOrderArray).get();
                    if(blockKeys.length > 0 && diff.length == blockKeys.length) {
                        $.each(blockKeys, function (index, value) {
                            newOrder[value] = diff[0];
                            localStorage.setItem(self.userId + 'dragBlock' + value, newOrder[value]);
                            diff.splice(0, 1);
                        });
                    }
                    $.each(newOrder, function (index, value) {
                        $('#dragBlock' + index).html(dragSectionHtml.filter('.' + value).html());
                    });
                }

                var selectedTab = localStorage[userId + 'mdSelectedTab'];
                if (selectedTab) {
                    $(".md-landMas-tabs li span").removeClass("is-active");
                    $(".md-landMas-tabs li." + selectedTab + " span").addClass("is-active");

                    $(".md-landMas-tabs hr").removeClass();
                    $(".md-landMas-tabs hr").addClass(selectedTab);

                    var container = $(".md-landMas-tabs li." + selectedTab).data("container");
                    if (container) {
                        $(".md-landMas-Container").removeClass("active");
                        $("." + container).addClass("active").show();
                    }
                }
            },
            openPhotoUploader() {
                const self = this;
                const path = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?propertyId=' + this.property.id;
                const searchWindow = window.open(path, 'PropertyPhotos', 'scrollbars=no,resizable=yes,height=800,width=1024');
                searchWindow.focus();
                const timer = setInterval(function () {
                        if (searchWindow.closed == true) {
                            self.getPhotos(self.property.id);
                            clearInterval(timer);
                        }
                    }, 1000);
            },
            registerPhotoClickHandler: function () {
                var self = this;
                $('.master-details-carousel-photo, .morePhotos').off("click").click(function (evt) {
                    var propertyId = $(this).attr('data-property')
                    self.savedPhotoId = propertyId;
                    var photoId = $(this).data('id');
                    var path = window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '?propertyId=' + propertyId + '&photoId=' + photoId;
                    var searchWindow = window.open(path, 'PropertyPhotos', 'scrollbars=no,resizable=yes,height=800,width=1024');
                    searchWindow.focus();
                    var timer = setInterval(function () {
                        if (searchWindow.closed == true) {
                            if (self.savedPhotoId != "") {
                                self.getPhotos(self.savedPhotoId);
                                self.savedPhotoId = "";
                            }
                            clearInterval(timer);
                        }
                    }, 1000);
                });
            },
            getPhotos: function (property) {
                var self = this;
                var media = jsRoutes.controllers.MediaController.getMediaByOwner(property);
                $.ajax({
                    type: "GET",
                    url: media.url,
                    cache: false,
                    success: function (response) {
                        self.registerCarousel();
                        $('.master-details-carousel').slick('removeSlide', null, null, true);
                        self.moreThanTwentyPhotos = response.length > 20;
                        self.twentyFirstPhotoId = self.moreThanTwentyPhotos ? response[20].id : '';
                        $.each(response.slice(0,20), function (index, photo) {
                            // Retrieve the description for the improvement date range
                            const improvementDateRange = self.improvementDateRangeDescriptions[photo.mediaItem.improvementDateRange];
                            // Sort the tags, then for each tag retrieve the tag description.
                            const tags = photo.mediaItem.tags.sort().map(tag => self.tagsDescriptions[tag]);
                            // Array of photo tags and the date range.
                            const displayedTagsArray = [improvementDateRange, ...tags].filter(description => !!description);
                            const displayedTagsText = displayedTagsArray.join(', ');
                            $('.master-details-carousel').slick('slickAdd',
                                `<div>
                                    <img class=\"md-primary master-details-carousel-photo\" data-id=\"${photo.id}\" data-property=\"${photo.ownerId}\" src=\"${photo.mediaItem.largeImageUrl}\"/>
                                    <ul class=\"galleryCaption\">
                                    <li><strong> ${(photo.description ? photo.description : '')} </strong></li>
                                    <li> ${displayedTagsText} </li>
                                    <li> ${self.formatDate(photo.mediaItem.captureDate)} </li>
                                    </ul>
                                </div>`
                            );
                        });
                        self.registerPhotoClickHandler();
                    },
                    error: function (response) {
                        console.log("Unable to get photos");
                        self.errorHandler(response);
                    }
                });
            },
            registerCarousel: function () {
                $(".master-details-carousel").not('.slick-initialized').slick({
                    dots: true,
                    infinite: false,
                    speed: 300,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    swipe: false
                });
            },
            getPropertyPhotos: function (photos, qupid) {
                var propertyPhotos = [];
                var primaryPhoto = 'assets/images/property/addPhotos.png';
                $.each(photos[0], function (i, obj) {
                    if (obj.isPrimary) {
                        primaryPhoto = obj.mediaItem.smallImageUrl;
                    }
                    propertyPhotos.push({
                        'id': obj.id,
                        'propertyId': obj.ownerId,
                        'qupid': qupid,
                        'link': obj.mediaItem.mediumImageUrl
                    });
                });
                var result = {primary: primaryPhoto, list: propertyPhotos};
                return result;
            },
            getHomeValuationJobs: function (propertyId) {
                var self = this;
                var homeValuationByProperty = jsRoutes.controllers.HomeValuation.getHomeValuationByProperty(propertyId);
                $.ajax({
                    type: "GET",
                    url: homeValuationByProperty.url,
                    cache: false,
                    success: function (response) {
                        self.valuationJobs = response.homeValuations;
                    },
                    error: function (response) {
                        console.log('Error while fetching Valuation Jobs: ' + response);
                        self.errorHandler(response);
                    }
                });
            },
            updateValuationList: function (homeValuation) {
                var self = this;
                var isOnTheList = self.valuationJobs.filter(function (e) {
                            return e.id == homeValuation.id;
                        }).length > 0;
                if (!isOnTheList) {
                    self.valuationJobs.unshift(homeValuation);
                }
            },
            isMatched: function (qvTitle) {
                var match = false;
                if($.inArray(qvTitle, this.linzTitlesOnly) !== -1) {
                    match = true;
                }
                return match;
            },
            getLinzTitles: function(qupid) {
                var self = this;
                var get = jsRoutes.controllers.PropertyMasterData.getLinzTitles(qupid);
                $.ajax({
                    type: "GET",
                    url: get.url,
                    cache: false,
                    success: function (response) {
                        self.linzTitles = response;
                        self.linzTitlesOnly = self.linzTitles.titles.map(function(a){
                            return a.titleReference;
                        });
                    },
                    error: function (response) {
                        console.log('error getting qv property details: ' + response);
                        self.errorHandler(response);
                    }
                });
            },
            async getMaoriLandLumpSums(qupid) {
                try{
                    const response = await axios({
                        method: 'get',
                        url: jsRoutes.controllers.PropertyMasterData.getMaoriLandLumpSum(qupid).url,
                    }).then(response => {
                        var revisionLumpSum = response.data[0] > 0 ? response.data[0].toString() : null;
                        var currentLumpSum = response.data[1] > 0 ? response.data[1].toString() : null;
                        this.property.maoriLandCurrentLumpSum = currentLumpSum != null ? this.formatLumpSum(currentLumpSum) : null;
                        this.property.maoriLandRevisionLumpSum = revisionLumpSum != null ? this.formatLumpSum(revisionLumpSum) : null;
                    });
                }
                catch (exception) {
                    console.log("Problem getting the lump sum for qpid: ", qpid, "Exception: ", exception)
                    throw exception;
                }
            },
            formatLumpSum(lumpSum){
                if(lumpSum.length > 3){
                    lumpSum = lumpSum.substr(0,1) + ',' + lumpSum.substr(1, lumpSum.length);
                }
                return lumpSum;
            },
            composeLegalDescriptions: function(titleLegalDescriptions) {
                var descriptions = [];
                $.each(titleLegalDescriptions, function (i, ld) {
                    var desc = ld.description;
                    if(ld.estateShare != '1/1') {
                        desc = ld.description + " " + ld.estateShare + " Share";
                    }
                    descriptions.push(desc);
                });
                return descriptions.join(", ");
            },
            composeTitleReference: function(title) {
                var titleReference = null;
                if(title && title.titleReference) {
                    titleReference = title.titleReference;
                    if(title.status) {
                        titleReference += ' ' + ((title.status == 'Historic') ? '(H)' : (title.status == 'Part Cancelled') ? '(P)' : '');
                    }
                }
                return titleReference;
            },
            loadCertificateTitleDetail: function() {
                if(this.linzTitlesOnly.length > 0) {
                    this.$store.commit('linzSearch/setLinzFilterByTitles', this.linzTitlesOnly.join(','));
                    this.$router.push('linz-search');
                }
            },
            async loadRuralRtvValues() {
                await this.$store.dispatch('ruralRtv/getLookupLists');

                const isValidForRuralRtv = (this.ruralRtvCategories.map(i => i.code).includes(this.property.category.code[0])
                    && ['SEPARATE VALUATION','APPORTIONMENT FOLLOWS','NOT APPLICABLE'].includes(this.property.apportionment));

                if (isValidForRuralRtv){
                    await this.$store.dispatch('ruralRtv/getRuralPropertyRtvValues', this.property.qupid);

                    if (this.ruralRtvValues.qpid === this.property.qupid && this.ruralRtvValues.rtv_cv) {
                        const rawMarketEstimateValue = this.ruralRtvValues.rtv_cv
                            ? this.ruralRtvValues.rtv_cv
                            : 0;

                        this.property.marketEstimateValue = this.ruralRtvValues.rtv_cv
                            ? numeral(this.ruralRtvValues.rtv_cv).format('$0,0')
                            : 'N/A';

                        this.property.realTimeLandValue = this.ruralRtvValues.rtv_lv
                            ? numeral(this.ruralRtvValues.rtv_lv).format('$0,0')
                            : 'N/A';

                        this.property.marketEstimateDate = this.ruralRtvValues.rtv_date
                            ? moment(this.ruralRtvValues.rtv_date).tz('Pacific/Auckland').format('DD/MM/YYYY')
                            : '-';

                        this.property.marketEstimateToCapitalValue = this.capitalValue && rawMarketEstimateValue
                            ? Math.round(((rawMarketEstimateValue * 100) / this.capitalValue) - 100) + '%'
                            : '-';

                        this.property.marketEstimateToCapitalValueUpDown = this.capitalValue && rawMarketEstimateValue
                            ? Math.round(((rawMarketEstimateValue * 100) / this.capitalValue) - 100)
                            : null;

                        this.property.realTimeLandValueToLandValue = this.landValue && this.ruralRtvValues.rtv_lv
                            ? Math.round(((this.ruralRtvValues.rtv_lv * 100) / this.landValue) - 100) + '%'
                            : '-';

                        this.property.realTimeLandValueToLandValueUpDown = this.landValue && this.ruralRtvValues.rtv_lv
                            ? Math.round(((this.ruralRtvValues.rtv_lv * 100) / this.landValue) - 100)
                            : null;
                    }
                }
            },
            initExpandAllOwnersOccupiers: function() {
                $(".expandAllOwnersOccupiers").off("click").click(function () {
                    $(this).toggleClass("down");
                    var down = $(this).hasClass('down');
                    if (down == true) {
                        $('.excess').removeClass('hide');
                    } else {
                        $('.excess').addClass('hide');
                    }
                });
            },
            initStoreMasTab: function() {
                $(".md-landMas-tabs li").off("click").click(function () {
                    localStorage.setItem(self.userId + 'mdSelectedTab', $(this).data('tab'));
                });
            },
            openMonarchFloorPlanTab(){
                openUrlInNewTab(`${window.location.protocol}//${window.location.hostname}:${window.location.port}/roll-maintenance/floorPlan/${this.property.qupid}`);
            },
            openGoogleSearchTab() {
                openUrlInNewTab(`https://google.co.nz/search?near=New+Zealand&q=${this.property.address1}+${this.property.address2}`);
            },
            succesfullyCopiedValRef(valRef){
                $('#ValRef-to-Clipboard').addClass('md-light');
                setTimeout(function(){$('#ValRef-to-Clipboard').removeClass('md-light');}, 1000);
            },
            getRuralSaleAnalysis: function (saleId) {
                const self = this;
                const m = jsRoutes.controllers.RuralSaleAnalysisController.getRuralSaleAnalysis(saleId);
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    success: function (response) {
                        self.ruralSales = response;
                    },
                    error: function (response) {
                        self.errorHandler(response);
                        self.ruralSales = null;
                        console.log('Error while fetching Rural Sale Analysis: ' + response);
                    }
                });
            },
            openQVMap() {
                openMap(this.property.qupid,this.coordinates.lat,this.coordinates.lng)
            },
            goToSraValues() {
              this.$router.push({name: 'property-sra-values', params: {qpid: this.property.qupid}})
            },
        },
        setup() {
            const { tryOpenAnalysisById } = useSaleAnalysis();

            return {
                tryOpenAnalysisById
            }
        },
        mounted: function () {
            const self = this;

            EventBus.$on('display-content', function (event) {
                var searchType = event.searchType
                self.showPropertyInfoSection = false;
                self.showDeletedSales = false;
                self.hasDeletedSales = false;
                if (searchType && searchType == 'master-details') {
                    if(!self.showTemplate) self.showTemplate = true;
                    if (event.valuationJobId) {
                        self.valuationJobIdToDisplay = event.valuationJobId;
                    } else {
                        self.valuationJobIdToDisplay = null;
                        self.valuationJobsMenu = false
                    }
                    self.showDragSection = false;
                    self.dragBlocksCustomised = false;
                    self.canPlatesBeLoaded = false;
                    var m = jsRoutes.controllers.PropertyMasterData.getProperty(event.property ? event.property.id : event.propertyId);
                    $.ajax({
                        type: "GET",
                        url: m.url,
                        cache: false,
                        success: function (response) {
                            self.generatePropertySummary(response.property);
                            self.property = self.generateMasterDetailsData(response, self.qivsUrl);
                            self.property.status = response.property.status;
                            self.$nextTick(function () {
                                self.initialTab = 0;

                                if(event.showExtraDetails){
                                    self.initialTab = 1;
                                }
                                if(event.showSraValues) {
                                    self.initialTab = 7;
                                }
                                if (self.valuationJobIdToDisplay || event.valuationJobId == 0) {
                                    self.initialTab = 2;
                                    self.tabActionHandler(2, self.valuationJobIdToDisplay);
                                }
                                self.getPhotos(self.property.id);
                                self.getHomeValuationJobs(self.property.id);
                            });

                            self.canNavigate = self.property.status.code !== "P";
                        },
                        error: function (response) {
                            self.errorHandler(response);
                            console.log('error fetch property search results: ' + response);
                            self.warningHeader = 'Server error while fetching Property Data'
                            self.warningMessage = 'Please contact system administrator.'
                            $('.warning').show();
                        }
                    });
                }
                else {
                    self.showTemplate = false
                }
            });
            $(document).click(function (event) {
                if (event.target != null && event.target.getAttribute("class") != null && event.target.getAttribute("class").indexOf("valuation") == -1 && self.valuationJobsMenu) {
                    self.valuationJobsMenu = false;
                }
            });
            EventBus.$on('home-valuation-completed-photos', function (event) {
                self.getPhotos(self.property.id);
            });
        },
        beforeDestroy: function () {
            EventBus.$off('display-content');
            EventBus.$off('home-valuation-completed-photos');
        },
        destroyed: function () {
            EventBus.$off('display-content');
            EventBus.$off('home-valuation-completed-photos');
        },
        updated: function () {
            const self = this;
            if (self.showDragSection && self.dragBlocksCustomised) {
                self.$nextTick(function () {
                    self.initMasterDetailsCustomisation(self.userId);
                    self.initHideExcessNamesLink();
                    self.initExpandAllOwnersOccupiers();
                    self.initStoreMasTab();
                    self.initAddSaleLink();
                    self.canPlatesBeLoaded = true;
                    self.dragBlocksCustomised = false;
                });
            }
            if (self.showTemplate == true) {
                self.initHideExcessNamesLink();
                self.initAddSaleLink();
                self.registerCarousel();
                self.registerPhotoClickHandler();
                self.initExpandAllOwnersOccupiers();
                self.initStoreMasTab();
                $('body').off("click").on('click', '.md-landMas-tabs li', function () {
                    // store the CSS name of the container in a variable
                    var _ContainerToShow = '.' + $(this).attr('data-container');
                    // hide all existing data containers except current target
                    $('.md-landMas-Container')
                            .not(_ContainerToShow + ':visible')
                            .slideUp(); // slide up every container except the one we want to see
                    if (!$(_ContainerToShow).is(':visible')) { // if the container is NOT visible, slide it down
                        $(_ContainerToShow).slideDown();
                    }

                    $(this).closest('ul') // find the parent UL
                            .find('li > span').removeClass();// clear out the active class on all LI > SPAN elements

                    $(this).find('span')
                            .addClass('is-active'); // add the active class to the clicked LI's SPAN

                    $(this).closest('ul') // find the parent UL
                            .find('hr') // now find the HR element in the UL (our moving underline)
                            .removeClass() // remove any existing classes on the HR
                            .addClass('MasTab-' + (parseInt($(this).index()) + 1)); // and add a new class with a numeric indentifier based on the LI we clicked on (remember indexes start at 0 hence to + 1 to keep things clean)

                    if ($(this).attr('data-filter')) {
                        // store the CSS name of the filter value in a variable
                        var _FilterToShow = '.' + $(this).attr('data-filter');

                        $(_ContainerToShow)
                                .find('.md-landMas li')
                                .slideDown(500)
                                .not(_FilterToShow)
                                .slideUp();
                    } else {
                        $(_ContainerToShow)
                                .find('.md-landMas li')
                                .slideDown(500);
                    }
                });
                $('.daterangepicker').hide();
            }
        }
    }
</script>
<style lang="scss" scoped>
.icon--flipped {
    transform: scale(-1, 1);
}

.status--pending {
    font-style:italic;
    color:red;
}

.sales-section-heading span {
    font-size: 1.5rem;
    font-weight: 600;
    cursor: pointer;
}

.qivs-link {
    i {
        display: inline-block;
        font-size: 1.4rem;
        vertical-align: text-top;
        width: 1.5rem;
        margin-right: -3px;
    }
    border: none;
    box-shadow: 1px 1px darkgrey;
    color: white;
    border-radius: 2px;
    font-size: 11px;
    font-weight: 600;
    font-style: normal;
    text-transform: uppercase;
    vertical-align: text-top;
}

.deleted-sale {
    background-color: rgba(205,50,0,.75);
    color: white;
}

.deleted-sale a {
    color: white;
}
</style>
<style lang="scss" scoped="true" src="./masterDetails.scss"></style>

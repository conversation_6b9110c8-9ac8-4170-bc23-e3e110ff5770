<template>
    <transition-group name="fade" tag="div">
        <div
            v-if="!loaded && !exception"
            key="1"
            class="loading"
        >
            <div class="spinner" />
            <div class="message">
                {{ loadingMessage }}
            </div>
        </div>
        <div
            v-else-if="exception"
            key="2"
            class="error"
        >
            <h1 class="error-message">
                {{ exception }}
            </h1>
        </div>
        <div key="3" class="monarch-qv-map" >
            <MapContainer
                :v-show="loaded"
                :search-properties="searchedProperties"
                :select-properties="selectedProperties"
                :layer-data-for-hover="layerDataForHover"
                :monarch-qpid="monarchQpid"
                :geo-server-url="geoserverUrl"
                :two-letter-zones="twoLetterZones || []"
                :two-letter-categories="twoLetterCategories || []"
                :sale-classifications="saleClassifications || []"
                :properties-sale-info="propertiesSaleInfo"
                :google-map-api-key="googleMapApiKey"
                :linz-map-api-key="linzMapApiKey"
                :isExternalUser="isExternalUser && !externalAccessToMapping"
                :externalAccessToMapping="externalAccessToMapping"
                :rolls-list="rollsList || []"
                :ta-list="taList || []"
                :primaryZoneCategories="primaryZoneCategories || []"
                :userData="userData"
                @getPropertyDetailsForLatLng="getPropertyDetailsForLatLng"
                @getLayerDataForLatLng="getLayerDataForLatLng"
                @getPropertyDetailsForQpids="getPropertyDetailsForQpids"
                @getPropertyDetailsForBndryPoints="getPropertyDetailsForBndryPoints"
                @getPropertySaleInfoForLatLng="fetchPropertySaleInfoForLatLng"
                @generateSitePlan="generateSitePlan"
                @exportAllProperties="exportAllProperties"
                @mapInitializationComplete="mapInitializationComplete"
                @getRollsForTa="getRollsForTa"
                @showAlertMessage="showAlertModal"
                @loadMasterDetails="loadPropertyDetails"
            />
            <alert-modal
                v-if="alertModalIsOpen"
                warning
                @close="closeAlertModal"
            >
                <h3>{{ message.heading }}</h3>
                <p>{{ message.message }}</p>
            </alert-modal>
            <alert-modal
                v-if="warningsModalIsOpen"
                warning
            >
                <h3>Do you want to proceed?</h3>
                <p>This will capture the current map view to a Site Plan for the selected property.</p>
                <template #buttons>
                    <div class="alertButtons">
                        <button
                            id="errorCancel"
                            class="mdl-button mdl-button--mini lefty"
                            @click="closeWarningModal()"
                        >
                            Cancel
                        </button>
                        <button
                            id="continue"
                            class="mdl-button mdl-button--mini"
                            @click="attachSitePlan()"
                        >
                            OK
                        </button>
                    </div>
                </template>
            </alert-modal>
        </div>
    </transition-group>
</template>

<script>
import { mapState } from 'vuex';
import { getPropertySearchInfo } from '../../utils/exportPropertyInfo';

export default {
    components: {
        MapContainer: () => import('qv-maps'),
        'alert-modal': () => import(/* webpackChunkName: "AlertModal" */ '../common/modal/AlertModal.vue'),
    },
    data() {
        return {
            loaded: false,
            loadingMessage: 'Loading, Please wait...',
            lat: null,
            lng: null,
            monarchQpid: null,
            qpids: null,
            alertModalIsOpen: false,
            message: null,
            warningsModalIsOpen: false,
            siteplanData: null,
        };
    },
    computed: {
        ...mapState('userData', [
            'userData',
            'geoserverUrl',
            'googleMapApiKey',
            'linzMapApiKey',
            'isInternalUser',
            'isExternalUser',
            'isQVMapUser',
            'taGroupName',
        ]),
        ...mapState('qvMaps', {
            searchedProperties: 'searchedProperties',
            selectedProperties: 'selectedProperties',
            layerDataForHover: 'layerDataForHover',
            twoLetterZones: 'twoLetterZones',
            twoLetterCategories: 'twoLetterCategories',
            saleClassifications: 'saleClassifications',
            propertiesSaleInfo: 'propertiesSaleInfo',
            alertMessage: 'alertMessage',
            exception: 'exception',
            rollsList: 'rollsList',
            taList: 'taList',
            primaryZoneCategories: 'primaryZoneCategories',
        }),
        externalAccessToMapping() {
            return this.taGroupName === 'OVG' || this.taGroupName === 'Auckland Council';
        },
    },
    watch: {
        alertMessage(val) {
            if (val) {
                this.showAlertModal(val);
            }
        },
    },
    async mounted() {
        if (this.$route.params) {
            if (this.$route.query.parcelIds) {
                await this.getPropertyDetailsForQpids(this.$route.params.qpid, true);
                const qpids = this.searchedProperties?.map(property => property.QPID);
                this.$router.push({ name: 'qv-map', params: { qpid: qpids.join(','), lat: 0, lng: 0 } });
                window.location.reload();
            }

            this.lat = this.$route.params.lat;
            this.lng = this.$route.params.lng;
            const isIntegerArrayRegex = /^(\d+(,\d+)*)?$/;
            if (isIntegerArrayRegex.test(this.$route.params.qpid)) {
                this.qpids = this.$route.params.qpid.split(',').map(Number);
                this.monarchQpid = this.qpids[this.qpids.length - 1];
            }
        }
        this.$store.dispatch('qvMaps/getPickListValues');
    },
    methods: {
        mapInitializationComplete() {
            // Search and highlight the property only after it is loaded.
            if (this.qpids) {
                this.getPropertyDetailsForQpids(this.qpids);
                return;
            }
            if (this.lat && this.lng) {
                this.getPropertyDetailsForLatLng([this.lat, this.lng]);
            }
        },

        async getPropertyDetailsForQpids(qpids, isParcelIds) {
            await this.$store.dispatch('qvMaps/getPropertyDetailsForQpids', { qpids, isParcelIds });
            this.loaded = true;
        },

        async getPropertyDetailsForLatLng(coords) {
            if (coords && coords.length === 2) {
                await this.$store.dispatch('qvMaps/getPropertyDetailsForLatLng', coords);
            }
            this.loaded = true;
        },

        async getLayerDataForLatLng(data) {
            const { column, coords } = data;
            if (column && coords && coords.length === 2) {
                await this.$store.dispatch('qvMaps/getLayerDataForLatLng', data);
            }
            this.loaded = true;
        },

        async getPropertyDetailsForBndryPoints(geomWkt) {
            if (geomWkt) {
                await this.$store.dispatch('qvMaps/getPropertyDetailsForBndryPoints', geomWkt);
            }
        },

        async exportAllProperties(qpids) {
            if (!qpids || qpids === undefined || qpids.length === 0) {
                return;
            }
            getPropertySearchInfo({ qupids: qpids, propertyCount: qpids.length, max: qpids.length + 1 }, this.isInternalUser);
        },

        generateSitePlan(data) {
            this.siteplanData = null;
            if (data) {
                this.siteplanData = data;
                this.showWarningModal();
            }
        },

        async attachSitePlan() {
            this.closeWarningModal();
            if (this.siteplanData) {
                this.siteplanData.description = 'Captured from new QV Map in Monarch';
                this.siteplanData.scale = 0;
                await this.$store.dispatch('qvMaps/uploadSiteplanImage', this.siteplanData);
            }
        },

        async fetchPropertySaleInfoForLatLng(coords) {
            await this.$store.dispatch('qvMaps/getPropertySaleInfoFromLatLng', coords);
        },

        async getRollsForTa(ta) {
            await this.$store.dispatch('qvMaps/getRollsListForTA', ta.ratingAuthorityId);
        },
        loadPropertyDetails(qpid) {
            if (!qpid) {
                return;
            }
            window.open(`${window.location.protocol}//${window.location.hostname}:${window.location.port}/property/search?qupid=${qpid}`).focus();
        },
        showAlertModal(message) {
            this.message = {
                heading: message.heading,
                message: message.message,
            };
            this.alertModalIsOpen = true;
        },

        closeAlertModal() {
            this.alertModalIsOpen = false;
        },

        showWarningModal() {
            this.warningsModalIsOpen = true;
        },

        closeWarningModal() {
            this.warningsModalIsOpen = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.monarch-qv-map {
    box-sizing: border-box;
    height: 100vh;
    width: 100%;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity .5s
}
.fade-enter,
.fade-leave-to {
    opacity: 0
}
.loading {
    position: fixed;
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 50%;
    .message {
        position: absolute;
        padding: 1rem 4rem;
        width: 100%;
        height: 100%;
        color: var(--color-blue-700);
        z-index: 100000;
        text-align: center;
        font-weight: bold;
        font-size: 1.2rem;
    }
}
.error {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    .error-message {
        font-size: 1.4rem;
        color: var(--color-red-600);
        padding: 1rem 4rem;
    }
}
</style>

<template>
    <div class="home desktop" v-bind:class="[showSalesAnalysis ? 'salesAnalysis' : '']">
        <!-- Main Application -->
        <div v-if="showMainPage">
            <div class="searchbarWrapper mdl-shadow--2dp" v-if="!isMobileView">
                <form class="searchBar">
                    <div class="typeahead__container">
                        <territorial-authority showLabel="false" taId="quickSearchTa"></territorial-authority>
                        <search></search>
                        <span class="advSearch-icon" @click="iconClick" title="Advanced Search"><i class="material-icons md-dark">&#xE429;</i></span>
                        <span class="simpleSearch-icon"><i class="material-icons md-dark">&#xE8B6;</i></span>
                        <type-ahead></type-ahead>
                    </div>
                </form>
            </div>
            <div class="contentWrapper">
                <valuation-home-toolbar></valuation-home-toolbar>
                <valuation-job :isMobileView="isMobileView"></valuation-job>
                <div v-if="!isMobileView">
                    <ta-dashboard></ta-dashboard>
                    <sales-search-result></sales-search-result>
                    <property-search-result></property-search-result>
                    <property-master-details></property-master-details>
                    <welcome></welcome>
                    <administration></administration>
                    <user-maintenance></user-maintenance>
                </div>
            </div>
        </div>
        <!-- VueJS Bug: Consecutive v-if conditions cause reactivity to break so changed to v-else-if -->
        <!-- Sales Analysis popup (from Monarch) -->
        <div class="contentWrapper" v-else-if="showSalesAnalysis && !isMobileView">
            <div class="analysis">
                <sale-analysis></sale-analysis>
            </div>
        </div>
        <!-- Subdivision Relink popup (from QIVS) -->
        <div class="contentWrapper" v-else-if="showSubdivision && !isMobileView">
            <div class="subRelink">
                <subdivision-relink></subdivision-relink>
            </div>
        </div>
    </div>
</template>

<script>
    import { mapState } from 'vuex';
    import Search from './search/Search.vue'
    import SalesSearchResult from './search/SalesSearchResult.vue'
    import PropertySearchResult from './search/PropertySearchResult.vue'
    import PropertyMasterDetails from './masterDetails/MasterDetails.vue'
    import SaleAnalysis from './analysis/ResidentialSaleAnalysis.vue'
    import TerritorialAuthority from './filters/TerritorialAuthority.vue'
    import TaDashboard from './dashboard/TaDashboard.vue'
    import TypeAhead from './filters/TypeAhead.vue'
    import Welcome from './Welcome.vue'
    import SubdivisionRelink from './subdivision/SubdivisionRelink.vue'
    import ValuationJob from './valuation/ValuationJob.vue'
    import ValuationHomeToolbar from './valuation/ValuationHomeToolbar.vue'
    import { EventBus } from '../EventBus.js'
    import { store } from '../DataStore.js'
    import Administration from './admin/Administration.vue'
    import UserMaintenance from './admin/UserMaintenance.vue'

    export default {
        props: ['isMobileView', 'displayContentEvent'],
        components: {
            Search,
            SalesSearchResult,
            PropertySearchResult,
            PropertyMasterDetails,
            SaleAnalysis,
            EventBus,
            TerritorialAuthority,
            TypeAhead,
            TaDashboard,
            Welcome,
            SubdivisionRelink,
            ValuationJob,
            ValuationHomeToolbar,
            Administration,
            UserMaintenance
        },
        data: function() {
            return {
                showSubdivision: false,
                showSalesAnalysis: false,
                showMainPage: true,
/* NOT USED               showAdmin: false, */
                waitForContinuedLoading: false,
            }
        },
        methods: {
            iconClick: function() {
                $('.typeahead__result').hide();
                $('.advSearch-wrapper').toggle();
            },
            recalculateAdvSearchWindowHeight: function() {
                var viewportHeight = window.innerHeight;
                var pageWrapperFixedHeight = 196;
                var advSearchFormFixedHeight = 704;
                if ((viewportHeight-pageWrapperFixedHeight) < advSearchFormFixedHeight) {
                    $('.advSearch-wrapper').height(viewportHeight - pageWrapperFixedHeight);
                } else {
                    $('.advSearch-wrapper').height(advSearchFormFixedHeight);
                }
            },
            getURLParam: function(sParam, sUrl){
                var sPageURL = window.location.search.substring(1);
                if(sUrl) {
                    sPageURL = sUrl;
                }
                var sURLVariables = sPageURL.split('&');
                for (var i = 0; i < sURLVariables.length; i++){
                    var sParameterName = sURLVariables[i].split('=');
                    if (sParameterName[0] == sParam){
                        return sParameterName[1];
                    }
                }
            },
            openQIVSLink: function(){
                window.open(this.qivsUrl, "QIVZ");
            },
            finishLoading: function () {
                const self = this;
                console.log('Home.finishLoading');
                this.recalculateAdvSearchWindowHeight();
                var qupid = this.getURLParam('qupid');
                var qupidFromRef = this.getURLParam('qupid', document.referrer.substring(document.referrer.indexOf("?") + 1))

                $('.qivs-app-link').off('click').click(function() {
                    self.openQIVSLink();
                });

                $('.propertyDomain-search').off('click').click(function() {
                    window.location = window.location.protocol+'//'+window.location.hostname+'/property?source=play'
                });
                let isInternalUser = this.isInternalUser;
                let userTACode = this.userTACode;
                let externalObjectionAccess = this.externalObjectionAccess;
                let allowedTACodes = this.allowedTACodes;

                /* If we have been told to emit a display event then do that */
                if(this.displayContentEvent) {
                    console.log('showing requested content');
                    EventBus.$emit('display-content', this.displayContentEvent);
                    return;
                }

                if (qupidFromRef || qupid) {
                    var event={}
                    event.searchType = 'master-details';
                    event.propertyId = qupid ? qupid : qupidFromRef;
                    EventBus.$emit('display-content', event);
                } else if (isInternalUser) {
                    var currentHomeSelection = localStorage.getItem(this.userId+'current-home-selection')
                    if (currentHomeSelection) {
                        var eventObj = {}
                        currentHomeSelection = JSON.parse(currentHomeSelection)
                        if (currentHomeSelection.valuers) {
                            eventObj.searchType = 'valuation-jobs';
                            eventObj.onHomePage = true
                            eventObj.viewValuationJobs = true
                            EventBus.$emit('display-content', eventObj);
                        }
                        if (currentHomeSelection.TA) {
                            eventObj.taCode = currentHomeSelection.TA;
                            eventObj.searchType = 'ta-dashboard';
                            eventObj.onHomePage = true
                            EventBus.$emit('display-content', eventObj);
                        }
                    } else {
                        var event = {}
                        event.searchType = 'valuation-jobs'
                        event.onHomePage = true
                        event.viewValuationJobs = true
                        EventBus.$emit('display-content', event);
                    }
                } else if (userTACode && userTACode != '') {
                    var eventObj = {};
                    eventObj.taCode = userTACode;
                    eventObj.showSplashPage = true
                    eventObj.searchType = 'ta-dashboard';
                    EventBus.$emit('display-content', eventObj);
                } else if (!isInternalUser) {
                    if (externalObjectionAccess){
                        localStorage.setItem(this.userId+'current-home-selection', JSON.stringify({'TA': allowedTACodes[0]}));
                        var eventObj = {};
                        eventObj.taCode = allowedTACodes[0]; // For externalObjectionAccess only, where userTACode is blank and allowedTACodes is populated
                        eventObj.onHomePage = true;
                        eventObj.searchType = 'ta-dashboard';
                        EventBus.$emit('display-content', eventObj);
                    }
                    else {
                        var event = {}
                        event.searchType = 'splash-page';
                        EventBus.$emit('display-content', event)
                }
                }
            },
        },
        computed: {
            ...mapState('userData', {
                userLoaded: 'loaded',
            }),
            ...mapState('userData', [
                'qivsUrl',
                'isInternalUser',
                'userTACode',
                'userId',
                'externalObjectionAccess',
                'allowedTACodes'
            ]),
            /* HACK: Following existing pattern to cope with asynch calls. Nasty. */
            ...mapState([
                'classificationsLoaded',
            ]),
        },
        watch: {
            userLoaded: function(newValue, oldValue) {
                if (this.waitForContinuedLoading && newValue && this.classificationsLoaded) {
                    this.waitForContinuedLoading = false;
                    this.finishLoading();
                }
            },
            classificationsLoaded: function(newValue, oldValue) {
                if (this.waitForContinuedLoading && newValue && this.userLoaded) {
                    this.waitForContinuedLoading = false;
                    this.finishLoading();
                }
            }
        },
        mounted: function() {
            var self = this;
            var saleId = self.getURLParam('saleId');
            var saleIdFromRef = self.getURLParam('saleId', document.referrer.substring(document.referrer.indexOf("?") + 1));
            var subRelink = self.getURLParam('In') != null && self.getURLParam('Out') != null;
            var subRelinkFromRef = self.getURLParam('In', document.referrer.substring(document.referrer.indexOf("?") + 1)) != null &&
                self.getURLParam('Out', document.referrer.substring(document.referrer.indexOf("?") + 1)) != null;
            var subToHomePage = self.getURLParam('home') != null;
            var qupid = self.getURLParam('qupid');
            var qupidFromRef = self.getURLParam('qupid', document.referrer.substring(document.referrer.indexOf("?") + 1))

            if (saleId) {
                self.showSubdivision = false;
                self.showSalesAnalysis = true;
                self.showMainPage = false;
                $('.main').hide();
                $('.headerWrapper').hide();
                $('.searchbarWrapper').hide();
                $('.resultsWrapper').hide();
                $('.daterangepicker').hide();
                /* $('.analysis').show(); SHOULDNT BE NEEDED */
                var event = {}
                event.searchType = 'sales-analysis'
                EventBus.$emit('display-content', event)
                var m = jsRoutes.controllers.Application.fetchUserData();
                $.ajax({
                    type: "GET",
                    url: m.url,
                    cache: false,
                    async: false,
                    success: function (response) {
                        var jsonData = response
                        store.commit('definition', jsonData);
                    },
                    error: function (response) {
                        console.error(response);
                    }
                });
            } else if (saleIdFromRef) {
                self.showSubdivision = false;
                self.showSalesAnalysis = true;
                self.showMainPage = false;
                $('.main').hide();
                $('.headerWrapper').hide();
                $('.searchbarWrapper').hide();
                $('.resultsWrapper').hide();
                $('.daterangepicker').hide();
                var event = {}
                event.searchType = 'sales-analysis'
                EventBus.$emit('display-content', event)
                window.location = window.location.protocol+'//'+window.location.hostname+'?saleId=' + saleIdFromRef;
            } else if ((subRelink || subRelinkFromRef) && !subToHomePage) {
                self.showSubdivision = true;
                self.showSalesAnalysis = false;
                self.showMainPage = false;
                $('.searchbarWrapper').hide();
                $('.resultsWrapper').hide();
                $('.daterangepicker').hide();
                $('.analysis').hide();
                $('.photoviewer').hide();
                $('.photoUpload').hide();
                $('.subRelink').show();
            } else {
                self.showSubdivision = false;
                self.showSalesAnalysis = false;
                self.showMainPage = true;
                $(document).mouseup(function (e) {
                    var container = $('.advSearch-wrapper');
                    var calendar = $('.reportrange');
                    var daterangepicker = $('.daterangepicker');
                    if ((!container.is(e.target) && container.has(e.target).length === 0) &&
                            (!calendar.is(e.target) && calendar.has(e.target).length === 0) &&
                            (!daterangepicker.is(e.target) && daterangepicker.has(e.target).length === 0)) {
                        container.hide();
                        $('body').removeClass('stopScrolling');
                    }
                });
                $(window).resize(function(){
                    self.recalculateAdvSearchWindowHeight();
                });
                this.waitForContinuedLoading = true;

                $('.daterangepicker').hide();
            }
        },
    }
</script>

<style scoped>
    @import "/assets/stylesheets/desktop.css";
</style>

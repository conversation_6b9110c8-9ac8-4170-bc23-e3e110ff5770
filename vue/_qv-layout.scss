@use "helpers" as *;

// Margin
.qv-m {
  @include spacingMixin(margin);

  &-auto {
    margin: auto;
  }
}

// Padding
.qv-p {
  @include spacingMixin(padding);

  &-auto {
    margin: auto;
  }
}

// Flex Layout
.qv-flex-column {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  &.qv-gap {
    @include sizeLoop(1rem, gap);
  }
}

.qv-flex-row {
  display: flex;
  flex-direction: row;
  gap: 1rem;

  &.qv-gap {
    @include sizeLoop(1rem, gap);
  }
}

.qv-flex-grow {
  flex-grow: 1;
}

.qv-flex-flow {
  flex-flow: wrap;
}

.qv-justify-space-between {
  justify-content: space-between;
}

.qv-justify-space-evenly {
  justify-content: space-evenly;
}

.qv-justify-center {
  justify-content: center;
}

.qv-justify-end {
  justify-content: flex-end;
}

.qv-align-center {
  align-items: center;
}

.qv-align-self-end {
  align-self: flex-end;
}

.qv-align-self-center {
  align-self: center;
}

.qv-align-self-start {
  align-self: flex-start;
}

.qv-vertical-align-middle {
  vertical-align: middle;
}

.qv-h {
  @include sizeLoop(1rem, height);

  &-full {
    height: 100%;
  }

  &-section {
    min-height: 20vh;
  }
}

.qv-w {
  @include columnMixin(2);
  @include columnMixin(3);
  @include columnMixin(4);
  @include columnMixin(6);
  @include columnMixin(8);
  @include columnMixin(100);

  &-fit-content {
    width: fit-content;
  }

  &-full {
    width: 100%;
  }

  &-screen {
    width: 100vw;
  }

  &-half {
    width: 50%
  }
}

.qv-position-absolute {
  position: absolute;
}

.qv-position-sticky {
  position: sticky;
}

.qv-bottom {
  bottom: 0;
}

.qv-top {
  top: 0;
}

.qv-rounded {
  &-sm {
    border-radius: 0.2rem;
  }

  &-md {
      border-radius: 0.4rem;
  }

  &-lg {
      border-radius: 0.6rem;
  }
}

.qv-border {
  border-style: solid;
  border-width: 0.1px;

  @include sizeLoop(2px, border-width) {
    border-style: solid;
  }

  &-bottom {
    border-bottom-width: 0.1px;
    border-bottom-style: solid;
    @include sizeLoop(2px, border-bottom-width) {
      border-bottom-style: solid;
    }
  }

  &-top {
    border-top-width: 0.1px;
    border-top-style: solid;
    @include sizeLoop(2px, border-top-width) {
      border-top-style: solid;
    }
  }
}

.qv-outline {
  @include sizeLoop(2px, outline-width) {
    outline-style: solid;
  }
}

.qv-divider-light {
  border-bottom: 1px solid rgba(255, 255, 255, .15);
}

.qv-divider-dark {
  border-bottom: 1px solid rgba(0, 0, 0, .15);
}

.qv-list-disc {
  list-style: disc;
}

.qv-circle {
  border-radius: 50%;
}
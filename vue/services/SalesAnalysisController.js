const controller = jsRoutes.controllers.SalesAnalysis;

export async function getSale(saleId, opts = {}) {
    const response = await fetch(controller.getSale(saleId).url, { ...opts });
    return await response.json();
}

export async function displaySalesAnalysis(saleId) {
    const response = await fetch(controller.displaySalesAnalysis(saleId).url);
    return await response.json();
}

export async function checkAnalysis(saleId) {
    return await fetch(controller.checkAnalysis(saleId).url);
}

export async function migrateResidentialToRuralAnalysis(saleId) {
    return await fetch(controller.migrateResidentialToRuralAnalysis(saleId).url);
}

export async function deleteSalesAnalysis(saleId) {
    return fetch(controller.deleteSalesAnalysis(saleId).url)
}

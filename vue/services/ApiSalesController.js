const controller = jsRoutes.controllers.SalesProcessingController;

export async function getSale(saleId, opts = {}) {
    const response = await fetch(controller.getSale(saleId).url, { ...opts });
    return await response.json();
}

export async function save(payload) {
    const response = await fetch(controller.saveSale().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });

    return await response.json();
}

export async function getDvr(id, current = false, saleIdToRefresh = 0) {
    const response = await fetch(controller.getDVR(id, current, saleIdToRefresh).url);
    return await response.json();
}

export async function getTALandUseZone(qpid) {
    const response = await fetch(controller.getTALandUseZone(qpid).url);
    return await response.json();

}

export async function getTitles(saleId, qpids) {
    const response = await fetch(controller.getTitles(saleId, qpids).url);
    return await response.json();
}

export async function relinkSaleRfs(body) {
    const response = await fetch(controller.relinkSaleRfs().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(body),
    });

    return await response.json();
}

export async function deleteSale(body) {
    const response = await fetch(controller.deleteSale().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(body),
    });

    return response.json();
}

export async function validateSale(body) {
    const response = await fetch(controller.validateSale().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(body),
    });

    return response.json();
}

export async function addSaleInspectionConsent(body) {
    const response = await fetch(controller.addSaleInspectionConsent().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(body),
    });

    return response.json();
}

export async function getSalePortalSalePdfUrl(salePortalSaleId, sourceId) {
    const response = await fetch(controller.getSalePortalSalePdfUrl(salePortalSaleId, sourceId).url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
    });
    return response.json();
}
export async function openSalesNoticePreview(noticeId, sourceId = 1) {
    const url = controller.viewSalesNotice(noticeId, sourceId).url;
    window.open(url, '_blank')
}

export async function getChattelParameter(qpid) {
    const response = await fetch(controller.getChattelParameter(qpid).url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
    });
    return await response.json();
}

export async function getLinzSaleWarnings(saleId) {
    const response = await fetch(controller.getLinzSaleWarnings(saleId).url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
    });
    return await response.json();
}

export async function searchUnlinkedNotices(payload) {
    const response = await fetch(controller.searchUnlinkedNotices().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });

    return await response.json();
}

export async function linkNotice(payload) {
    return await fetch(controller.linkNotice().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
}

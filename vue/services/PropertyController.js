const controller = jsRoutes.controllers.PropertyController;

export async function searchProperties(payload) {
    const res = await fetch(controller.searchProperties().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    return await res.json();
}
export async function reinstateObjectionJob(reinstatement) {
    const { url } = jsRoutes.controllers.ObjectionController.reinstateObjectionJob();
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(reinstatement),
    });
    return res.json();
}

export async function updateObjectionLink(toLink, objectionId, ratingValuationId) {
    const { url } = jsRoutes.controllers.ObjectionController.updateObjectionLink(objectionId);
    const data = {
        isLinked: toLink,
        ratingValuationId,
    };
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(data),
    });
    return res.json();
}

export async function actionObjectionJob(queryParams) {
    const url = new URL(jsRoutes.controllers.ObjectionController.actionObjection().url, window.location.origin);
    const params = new URLSearchParams(queryParams);
    url.search = params.toString();
    console.log('url', url);
    return fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
    });
}

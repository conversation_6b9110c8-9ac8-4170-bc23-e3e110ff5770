const controller = jsRoutes.controllers.SalesSearchController;

export async function search(payload) {
    const response = await fetch(controller.getSearchSale().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    return await response.json();
}

export async function getSalesProcessingStatus() {
    const response = await fetch(controller.getSalesProcessingStatus().url);
    return await response.json();
}

export async function getSalesProcessingSource() {
    const response = await fetch(controller.getSalesProcessingSource().url);
    return await response.json();
}

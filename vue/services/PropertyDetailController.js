const controller = jsRoutes.controllers.PropertyDetailController;

export async function getCurrentPropertyDetail(propertyId) {
    const response = await fetch(controller.getCurrentPropertyDetail(propertyId).url);
    return await response.json();
}

export async function saveCurrentPropertyDetail(payload) {
    const response = await fetch(controller.saveCurrentPropertyDetail().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    if (!response.ok) throw new Error('Failed to save Current Property Detail');
    return await response.json();
}

export async function validateOnSave(propertyDetail, property, dvrSnapshot, validationContext = null) {
    const response = await fetch(controller.validateOnSave().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({ propertyDetail, property, dvrSnapshot, validationContext }),
    });

    return await response.json();
}

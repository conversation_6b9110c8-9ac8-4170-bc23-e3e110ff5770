const controller = jsRoutes.controllers.PropertyMasterData;

export async function getPropertyDetails(qpid) {
    const response = await fetch(controller.getPropertyInfo(qpid).url);
    return await response.json();
}

export async function getProperty(qpid) {
    const response = await fetch(controller.getProperty(qpid).url);
    return await response.json();
}

export async function getPropertyInfoFull(qpid) {
    const response = await fetch(controller.getPropertyInfoFull(qpid).url);
    return await response.json();
}

export async function updateOwners(payload) {
    const response = await fetch(controller.updateOwners().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    if (!response.ok) throw new Error(response.message);
    return await response.json();
}

export async function getPicklistValues() {
    const response = await fetch(controller.getPickListValues().url);
    return await response.json();
}

export async function getQvProperty(propertyId) {
    const response = await fetch(controller.getQvProperty(propertyId).url);
    return await response.json();
}
export async function getQvPropertyZoneInfo(qpid) {
    const response = await fetch(controller.getQvPropertyZoneInfo(qpid).url);
    return await response.json();
}

export async function saveQvProperty(payload) {
    const response = await fetch(controller.saveQvProperty().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    if (!response.ok) throw new Error('Failed to save QV Property');
    return await response.json();
}

export async function saveQvPropertyZoneInfo(payload) {
    const response = await fetch(controller.saveQvPropertyZoneInfo().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    if (!response.ok) throw new Error('Failed to save QV Property Zone Info');
    return await response.json();
}

export async function saveHazardNotes(payload) {
    const response = await fetch(controller.saveHazardNotes().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    if (!response.ok) {
        throw new Error(`Failed to save hazard notes: ${response.statusText}`);
    }
    try {
        return await response.json();
    } catch (error) {
        throw new Error(`Error parsing JSON response: ${error.message}`);
    }
}

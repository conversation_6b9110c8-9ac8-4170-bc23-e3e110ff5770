export async function addComparableSales(ratingValuationId, comparableSalesToAdd, shouldIncludeAllSales = false) {
    const { url } = jsRoutes.controllers.ApiPicklistController.addComparableSales(ratingValuationId, shouldIncludeAllSales);
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(comparableSalesToAdd),
    });
    return res.json();
}

export async function addRatingValuation(objectionId, ratingValuationId) {
    const { url } = jsRoutes.controllers.ApiPicklistController.addRatingValuation(objectionId);
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({ ratingValuationId }),
    });
    return res.json();
}

export async function validateObjectionJob(ratingValuationId, isValidatingAtReviewStage = false) {
    const { url } = jsRoutes.controllers.ApiPicklistController.validateObjectionJob(ratingValuationId, isValidatingAtReviewStage);
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({}),
    });
    return res.json();
}

export async function completeObjectionJobValuation(ratingValuationId) {
    const { url } = jsRoutes.controllers.ApiPicklistController.completeObjectionJobValuation(ratingValuationId);
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({}),
    });
    return res.json();
}

export async function autoSelectComparableSales(body) {
    const { url } = jsRoutes.controllers.ApiPicklistController.autoSelectComparableSales();
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(body),
    });
    return res.json();
}

export async function getPicklistValue(pickListValue) {
    const { url } = jsRoutes.controllers.ApiPicklistController.getPicklistValue(pickListValue);
    const res = await fetch(`${url}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    });
    return res.json();
}

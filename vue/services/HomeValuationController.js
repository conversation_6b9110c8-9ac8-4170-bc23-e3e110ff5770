const controller = jsRoutes.controllers.HomeValuation;

export async function displayMarketValuationJobSearchResult(payload) {
    var m = controller.displayMarketValuationJobSearchResult();
    try {
        const res = await fetch(m.url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
            body: JSON.stringify(payload),
        });
        const data = await res.json();
        return data;
    }
    catch (error) {
        throw Error(`Error calling displayMarketValuationJobSearchResult: ${error}}`);
    }
}

const controller = jsRoutes.controllers.RuralWorksheetController;

export async function getCommercialWorksheet(qpid) {
    const { url } = controller.getCommercialWorksheet(qpid);
    const response = await fetch(url);
    return await response.json();
}

export async function getCommercialRevisionWorksheet(qpid) {
    const { url } = controller.getCommercialRevisionWorksheet(qpid);
    const response = await fetch(url);
    return await response.json();
}

export async function createCommercialRevisionWorksheet(qpid) {
    const { url } = controller.createCommercialRevisionWorksheet(qpid);
    const response = await fetch(url);
    return await response.json();
}

export async function getRfcClassifications(qpid) {
    const { url } = controller.getRfcClassifications(qpid);
    const response = await fetch(url);
    return await response.json();
}

export async function deleteCommercialWorksheet(qpid) {
    const { url } = controller.deleteCommercialWorksheet(qpid);
    const response = await fetch(url);
    return await response.json();
}

export async function updateCommercialWorksheet(worksheet) {
    const response = await fetch(controller.updateCommercialWorksheet().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(worksheet),
    });
    if (response.ok) {
        return response.json();
    }
}

export async function validateCommercialWorksheet(worksheet) {
    const response = await fetch(controller.validateCommercialWorksheet().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(worksheet),
    });
    if (response.ok) {
        return response.json();
    }
}

export async function generateCommercialWorksheetPdf(qpid, type) {
    const { url } = controller.generateCommercialWorksheetPdf(qpid, type);
    const response = await fetch(url);
    return await response.json();
}



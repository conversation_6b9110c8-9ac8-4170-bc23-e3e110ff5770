const controller = jsRoutes.controllers.RatingValuationController;

export async function validate(ratingValuation, propertyDetail, activities, isComplete) {
    const result = await fetch(controller.validate().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({ ratingValuation, propertyDetail, activities, isComplete }),
    });

    return await result.json();
}

export async function validateOnSave(ratingValuation, propertyDetail, activities) {
    const result = await fetch(controller.validateOnSave().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({ ratingValuation, propertyDetail, activities })
    })

    return await result.json();
}

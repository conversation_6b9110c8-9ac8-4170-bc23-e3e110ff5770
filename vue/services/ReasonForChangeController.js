const controller = jsRoutes.controllers.ReasonForChangeController;

export async function getExistingReasonForChange(qpid, userName) {
    const { url } = controller.getExistingReasonForChange(qpid, userName);
    const response = await fetch(url);
    return await response.json();
}

export async function addReasonForChange(payload) {
    const res = await fetch(controller.addReasonForChange().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    return await res.json();
}
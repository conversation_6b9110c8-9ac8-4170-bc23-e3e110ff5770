const controller = jsRoutes.controllers.ReferenceData;

export async function getZoningOptions(payload) {
    const response = await fetch(controller.searchClassifications().url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(payload),
    });
    return await response.json();
}
// Fade
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

// Expand
.expand {
  height: auto;
}

.expand-leave-active {
  transition: max-height 225ms ease-in, opacity 225ms ease-in;
}

.expand-enter-active {
  transition: max-height 225ms ease-out, opacity 225ms ease-out;
}

.expand-enter, .expand-leave-to {
  opacity: 0;
  max-height: 0;

}

.expand-enter-to, .expand-leave {
  opacity: 1;
  max-height: 10rem;
}

// Infinite Spin
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.qv-spinner {
  animation: spin 2s linear infinite;
}

// Flip
.qv-flip {
  transition: transform 0.2s linear;
  -webkit-transition: transform 0.2s linear;
}

.qv-flipped {
  transform: rotate(-180deg);
}


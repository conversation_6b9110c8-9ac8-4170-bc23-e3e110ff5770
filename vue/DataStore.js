import 'babel-polyfill'

import Vue from 'vue';
import Vuex from 'vuex';

import taCodesEventbusToVuex from './plugins/taCodes-eventbus-to-vuex.js';

import appVersion from './store/appVersion';
import userData from './store/userData';
import classifications from './store/classifications';
import rollMaintenanceSearch from './store/rollMaintenanceSearch';
import rollMaintenanceActivity from './store/rollMaintenanceActivity';
import ratingValuation from './store/ratingValuation';
import linzSearch from './store/linzSearch';
import salesSearch from './store/salesSearch';
import propertyPhotos from './store/propertyPhotos';
import saleAnalysis from './store/saleAnalysis';
import sra from './store/sra';
//TODO is it worthwhile changing conventon to 'propertyStore' etc?
import property from './store/property';
import zoneInfo from './store/zoneInfo';
import ruralRtv from './store/ruralRtv';
import ruralWorksheet from './store/ruralWorksheet';
import reasonForChange from './store/reasonForChange';
import propertyDraft from './store/propertyDraft';
import qvProperty from './store/qvProperty';
import currentPropertyDetails from './store/currentPropertyDetails';
import valuersList from './store/valuersList';
import taCodes from './store/taCodes';
import floorplan from './store/floorplan';
import qvMaps from './store/qvMaps';
import saleClassifications from '@/store/saleClassifications';

Vue.use(Vuex);

export const store = new Vuex.Store({
    // strict: process.env.NODE_ENV !== 'production',
    state: {
        application: {},
        classificationsLoaded: false,
        users: undefined,
    },
    mutations: {
        definition: function(state, value) {
            state.application = value;
        },
        allUsers: function(state, value) {
            state.users = value;
        },
        /* HACK Should be done properly. This is a quick fix to flag all classifications have been loaded */
        classificationsLoaded: function(state, value) {
            state.classificationsLoaded = value;
        }

/* REMOVED: Moved all classification store access to classifications.js which is acting as a Vuex module.
        classifications: function(state, value){
            state.classifications[value.key] = value.data;
        },
*/
    },
    plugins: [
        taCodesEventbusToVuex,
    ],
    modules: {
        appVersion,
        userData,
        taCodes,
        classifications,
        rollMaintenanceSearch,
        rollMaintenanceActivity,
        ratingValuation,
        linzSearch,
        salesSearch,
        propertyPhotos,
        propertyDraft,
        qvProperty,
        currentPropertyDetails,
        property,
        zoneInfo,
        valuersList,
        saleAnalysis,
        ruralRtv,
        ruralWorksheet,
        reasonForChange,
        saleClassifications,
        floorplan,
        qvMaps,
        sra,
    },
});


store.dispatch('appVersion/getMonarchWebServerVersion');
store.dispatch('userData/fetchUserData');
store.dispatch('fetchClassifications');
store.dispatch('saleClassifications/fetchClassifications');

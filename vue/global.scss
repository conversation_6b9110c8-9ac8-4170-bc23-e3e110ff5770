/* -----------------------------------------
https://stackoverflow.com/questions/24307455/import-css-scss-file-into-a-class

Name your inner file with an underscore, and ending in scss. .Yes, even if it's plain css, i.e. foo.css → _foo.scss

Have an outer File like so:

#main .content {  // if that's, where you want them to rule only
    @import 'foo';
}
Reasons:
- import only works with css
- underscore-files are glady skipped by sass (also as in gulp.src(<some wildcards).sass())
- if you have no influence in your repo about the css filename whatsoever.
  or it's a major pain on upgrades, consider using a symbolic link under an .scss extension...

Cant do this though ...
@import '../node_modules/vue-multiselect/dist/vue-multiselect.min.css';
-----------------------------------------
 */
@use "components/ui";

@import "qv-colors";
@import "qv-transition";
@import "qv-form";
@import "helpers";

@import "components/property/property";
@import "components/propertyDetails/residential/draftProperty";

/* Scope all new CSS to our "router" only ... could name it differently but it is just contianing CSS from the rest of Monarch */
.router {
    @import "vue-multiselect";
    @import '~vue2-datepicker/scss/index.scss';
}

/* Styles dropdown - is not scoped on purpose due to dropdown not inheriting scoping classes.
   Didnt try to do line heights etc as that would be more involved.
*/
.router {
    .multiselect,
    .multiselect__tags,
    .multiselect__input,
    .multiselect__single
    {
        font-size: 1.2rem;
    }

    .multiselect.optional .multiselect__tags,
    .multiselect.optional .multiselect__tags span,
    .multiselect.optional .multiselect__tags input {
        background: #e3f5fc;
    }

    .multiselect__content-wrapper {
        /* Ensure dropdown width never gets too small */
        min-width: 200px;
    }
    .multiselect__single {
        /* Reduce left padding on main input */
        padding-left: 0px;
    }
    .multiselect__select {
        /* Reduce space taken by selection arrow */
        width: 26px;
    }
    .multiselect__tags {
        border-color: #d2d2d2;
        /* Reduce space taken by selection arrow */
        padding-right: 20px;
    }
    .advSearch-group .dropdown-menu {
        top: 4.1rem;
    }
    .btn .caret {
        top: 1.6rem;
    }

    .multiselect__select--multiple {
        background-image: url('multiselect-multiple.png');
        background-position-x: 1px;
    }

    .multiselect__option--selected {
        color: #43926E;
    }
    .multiselect__option--selected > span{
        font-weight: bold;
    }
    .multiselect--disabled {
        opacity: inherit;

        .multiselect__tags {
            background: #eee;

            .multiselect__single {
                background: #eee;
            }
        }
    }
    .multiselect__tags--red {
        @extend .multiselect__tag;
        background: #ff0000;
    }

    @media print {
        .multiselect__select {
            display: none !important;
        }
        .multiselect__tags {
            border: none !important;
            box-shadow: none !important;
            border-radius: 0px;
            background: transparent;
        }
    }
}

.router {
    .mx-datepicker.past-date {
        .mx-input {
            color: #ff0000 !important;
        }
    }
}

.router, .legacy-scope {
    .col {
        float: left;
        padding-right: 5px;
        padding-left: 5px;
        margin: 0.8rem 0;
        display: table-cell;

        &-container{
            margin-top: 1.6rem;
            padding: 1.6rem;
            background-color: #ffffff;
            box-sizing: border-box;
            font-size: 1.4rem;
            display: table;
            width: 100%;
        }

        &-row {
            padding: 0.5rem;
            margin-right: -15px;
            margin-left: -15px;
            display: table-row;
        }

        &-right {
            float: right;
        }

        &-1 {
            width: 8.333%;
        }
        &-2 {
            width: 16.667%;
        }
        &-3 {
            width: 25%;
        }
        &-4 {
            width: 33.333%;
        }
        &-5 {
            width: 41.667%;
        }
        &-6 {
            width: 50%;
        }
        &-7 {
            width: 58.333%;
        }
        &-8 {
            width: 66.667%;
        }
        &-9 {
            width: 75%;
        }
        &-10 {
            width: 83.333%;
        }
        &-11 {
            width: 91.667%;
        }
        &-12 {
            width: 100%;
        }
    }

    /* Copy Monarch styling into at least something workable.
       No time to do this properly.
    */
    table.table {
        display: table;
        width: 100%;
    }

    table.table tr {
        display: table-row;
        padding: 0 2.4rem 0 .8rem;
        margin: 0;
        border-bottom: .1rem solid #f2f2f2;
        height: 7.2rem;
        width: 100%;
        -moz-transition: all .1s linear;
        -webkit-transition: all .1s linear;
        transition: all .1s linear;
    }

    table.table tr:hover {
        background-color:#f9f9f9;
    }

    table.table tr:last-child {
        border-bottom: none;
    }

    table.table tr th {
        display: table-cell;
        font-size: 1rem;
        font-weight: 600;
        color: rgba(0,0,0,.5);
        vertical-align: middle;
        border-bottom: 0.4rem solid #ddd;
    }

    table.table tr th > a {
        color: rgba(0,0,0,.5);
    }

    table.table tr td {
        display: table-cell;
        font-size: 1.2rem;
        vertical-align: middle;
    }


    /* Basic icon support */
    .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-repeat: no-repeat ;
        background-size: auto;
    }

    .icon-needsMoreInformation {
        background-image: url('../public/images/skin/information.png');
    }

    .icon-needsInspection {
        background-image: url('../public/images/skin/exclamation.png');
    }
}

.qv-pointer-events-none {
    pointer-events: none;
}

.qv-cursor-pointer {
    cursor: pointer;
}

.qv-outline-none {
    outline: none;
}
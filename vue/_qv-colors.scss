// Colors from QV Brand Signature Principles Document
@use "helpers" as *;

$_colors: (
        "darkblue": #0E256A,
        "blue": #1E4196,
        "mediumblue": #5290db,
        "lightblue": #BAD1EB,
        "orange": #FF990F,
        "darkorange": #EA6A0F,
        "red": #d2362b,
);

$_color--interval: 4%;
@function shades($color) {
  @return (
          "0": lighten($color, $_color--interval * 5),
          "100": lighten($color, $_color--interval * 4),
          "200": lighten($color, $_color--interval * 3),
          "300": lighten($color, $_color--interval * 2),
          "400": lighten($color, $_color--interval),
          "500": $color,
          "600": darken($color, $_color--interval),
          "700": darken($color, $_color--interval * 2),
          "800": darken($color, $_color--interval * 3),
          "900": darken($color, $_color--interval * 4)
  )
}

$color-stoneblue: #162b3f;
$color-darkblue: #0E256A;
$color-blue: #1E4196;
$color-mediumblue: #5290db;
$color-lightblue: #BAD1EB;
$color-yellow: rgba(255, 200, 40, 1);
$color-orange: #FF990F;
$color-darkorange: #EA6A0F;
$color-red: #d2362b;
$color-darkred: rgba(190, 55, 15, 1);
$color-light: #fafafa;
$color-lightgray: #ddd;
$color-lightbuff: #cacaca;
$color-dark: #05190F;

$color-error: #fc3d39;
$color-warning: #d09c1a;
$color-success: #4caf50;
$color-info: $color-mediumblue;

$color-white: #fff;
$color-dullblue: #283c64;

:root {
  --qv-color-stoneblue: #{$color-stoneblue};
  --qv-color-navyblue: #283c64;
  --qv-color-darkblue: #{$color-darkblue};
  --qv-color-blue: #{$color-blue};
  --qv-color-mediumblue: #{$color-mediumblue};
  --qv-color-lightblue: #{$color-lightblue};
  --qv-color-yellow: #{$color-yellow};
  --qv-color-orange: #{$color-orange};
  --qv-color-darkorange: #{$color-darkorange};
  --qv-color-red: #{$color-red};
  --qv-color-darkred: #{$color-darkred};
  --qv-color-light: #{$color-light};
  --qv-color-lightgray: #{$color-lightgray};
  --qv-color-lightbuff: #{$color-lightbuff};
  --qv-color-dark: #{$color-dark};

  --qv-color-error: #{$color-error};
  --qv-color-warning: #{$color-warning};
  --qv-color-success: #{$color-success};
  --qv-color-info: #{$color-mediumblue};
  --qv-color-transparent: rgba(0,0,0,0);

  --monarch-color-dullblue: #{$color-dullblue};
  --monarch-color-white: #{$color-white};


  /** FIXME: Deprecated - migrate any usage to --qv-color variables. **/
  @each $key, $value in $_colors {
    $shades: shades($value);
    @each $shade, $color in $shades {
      --color-#{$key}-#{$shade}: #{$color};
    }
  }
}

.qv {
  @include color('stoneblue', $color-stoneblue);
  @include color('darkblue', $color-darkblue);
  @include color('blue', $color-blue);
  @include color('mediumblue', $color-mediumblue);
  @include color('lightblue', $color-lightblue);
  @include color('yellow', $color-yellow);
  @include color('orange', $color-orange);
  @include color('darkorange', $color-darkorange);
  @include color('red', $color-red);
  @include color('darkred', $color-darkred);
  @include color('light', $color-light);
  @include color('lightgray', $color-lightgray);
  @include color('lightbuff', $color-lightbuff);
  @include color('dark', $color-dark);

  @include color('error', $color-error);
  @include color('warning', $color-warning);
  @include color('success', $color-success);
  @include color('info', $color-mediumblue);
}

.monarch {
  @include color('dullblue', $color-dullblue);
  @include color('white', $color-white);

}

/** FIXME: Deprecated - migrate any usage to qv-color/qv-bg classes. **/
@each $key, $value in $_colors {
  $shades: shades($value);
  @each $shade, $color in $shades {
    .bg-#{$key}-#{$shade}, .qv-bg-#{$key}-#{$shade} {
      background-color: $color;
    }
    .text-#{$key}-#{$shade}, .qv-text-#{$key}-#{$shade} {
      color: $color;
    }

    .qv-border-#{$key}-#{$shade} {
      border-color: $color;
    }
  }
}

@mixin colorMixin($key) {
  &-darkblue {
    @include selectorMixin {
      #{$key}: var(--qv-color-darkblue);
    }
  }

  &-blue {
    @include selectorMixin {
      #{$key}: var(--qv-color-blue);
    }
  }

  &-mediumblue {
    @include selectorMixin {
      #{$key}: var(--qv-color-mediumblue);
    }
  }

  &-lightblue {
    @include selectorMixin {
      #{$key}: var(--qv-color-lightblue);
    }
  }

  &-orange {
    @include selectorMixin {
      #{$key}: var(--qv-color-orange);
    }
  }

  &-darkorange {
    @include selectorMixin {
      #{$key}: var(--qv-color-darkorange);
    }
  }

  &-red {
    @include selectorMixin {
      #{$key}: var(--qv-color-red);
    }
  }

  &-error {
    @include selectorMixin {
      #{$key}: var(--qv-color-error);
    }
  }

  &-warning {
    @include selectorMixin {
      #{$key}: var(--qv-color-warning);
    }
  }

  &-success {
    @include selectorMixin {
      #{$key}: var(--qv-color-success);
    }
  }

  &-light {
    @include selectorMixin {
      #{$key}: var(--qv-color-light);
    }
  }

  &-lightbuff {
    @include selectorMixin {
      #{$key}: var(--qv-color-lightbuff)
    }
  }

  &-dark {
    @include selectorMixin {
      #{$key}: var(--qv-color-dark);
    }
  }

  &-stoneblue {
    @include selectorMixin {
      #{$key}: var(--qv-color-stoneblue);
    }
  }

  &-lightgray {
    @include selectorMixin {
      #{$key}: var(--qv-color-lightgray)
    }
  }

  &-transparent {
    @include selectorMixin {
      #{$key}: var(--qv-color-transparent)
    }
  }
}

/** Color Utility Classes **/
.qv-color {
  @include colorMixin('color');
}
.qv-bg {
  @include colorMixin('background-color');
}
.qv-border-color {
  @include colorMixin('border-color')
}

.qv-overlay {
  @include selectorMixin {
    &:after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
      pointer-events: none;
    }
  }
}

.qv-opacity {
  $opacity-step: 5;

  @for $i from 0 through 95 {
    @if $i % $opacity-step == 0 {
      &-#{$i} {
        opacity: calc($i / 100)
      }
    }
  }
}

